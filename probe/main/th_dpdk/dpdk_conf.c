#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <numa.h>
#include <unistd.h>

#include "dpdk_conf.h"
#include "cJSON.h"


static unsigned int round_to_pow2(unsigned int x)
{
    if(0 == x)
    {
        return 1;
    }
    x--;
    x |= x >> 1;
    x |= x >> 2;
    x |= x >> 4;
    x |= x >> 8;
    x |= x >> 16;
    x++;
    return x;
}

static unsigned int parse_thd_lcore_list(cJSON *conf_json, int thid, int *lcore_list)
{
    cJSON *tmps = NULL, *tmp = NULL, *value1 = NULL, *value2 = NULL;
    unsigned int lcore_num = 0;
    int thid_iter = 0;

    tmps = cJSON_GetObjectItemCaseSensitive(conf_json, "task");
    if(cJSON_IsArray(tmps) && cJSON_GetArraySize(tmps))
    {
        thid_iter = 0;
        cJSON_ArrayForEach(tmp, tmps)
        {
            if(thid_iter == thid)
            {
                value1 = cJSON_GetObjectItemCaseSensitive(tmp, "lcore");
                if(cJSON_IsArray(value1) && cJSON_GetArraySize(value1))
                {
                    cJSON_ArrayForEach(value2, value1)
                    {
                        if(cJSON_IsNumber(value2) && lcore_num < MAX_LCORE_NUM)
                        {
                            lcore_list[lcore_num] = value2->valueint;
                            lcore_num ++;
                        }
                    }
                }
                break;
            }
            thid_iter ++;
        }
    }
    return lcore_num;
}

static unsigned int parse_thd_port_list(cJSON *conf_json, int thid, int *port_list)
{
    char task_buf[11];
    cJSON *tmps = NULL, *tmp = NULL;
    int if_num = 0;
    unsigned int port_num = 0;

    tmps = cJSON_GetObjectItemCaseSensitive(conf_json, "if");
    if(cJSON_IsArray(tmps) && cJSON_GetArraySize(tmps))
    {
        cJSON_ArrayForEach(tmp, tmps)
        {
            if_num ++;
        }
    }

    sprintf(task_buf, "%d", thid);
    tmps = cJSON_GetObjectItemCaseSensitive(conf_json, task_buf);
    if(cJSON_IsArray(tmps) && cJSON_GetArraySize(tmps))
    {
        cJSON_ArrayForEach(tmp, tmps)
        {
            if(cJSON_IsNumber(tmp))
            {
                if(tmp->valueint < if_num)
                {
                    port_list[port_num] = tmp->valueint;
                    port_num ++;
                }
            }
        }
    }
    return port_num;
}

static int parse_thd_suspend(cJSON *conf_json, int thid)
{
    cJSON *tmps = NULL, *tmp = NULL;
    int i = 0;

    tmps = cJSON_GetObjectItemCaseSensitive(conf_json, "suspend");
    if(cJSON_IsArray(tmps) && cJSON_GetArraySize(tmps))
    {
        cJSON_ArrayForEach(tmp, tmps)
        {
            if(cJSON_IsString(tmp) && NULL != tmp->valuestring)
            {
                i = atoi(tmp->valuestring);
                if(i == thid)
                {
                    return 1;
                }
            }
        }
    }
    return 0;
}

static int parse_thd_forward(cJSON *conf_json, int thid)
{
    cJSON *tmps = NULL, *tmp = NULL;
    int i = 0;

    tmps = cJSON_GetObjectItemCaseSensitive(conf_json, "forward");
    if(cJSON_IsArray(tmps) && cJSON_GetArraySize(tmps))
    {
        cJSON_ArrayForEach(tmp, tmps)
        {
            if(cJSON_IsString(tmp) && NULL != tmp->valuestring)
            {
                i = atoi(tmp->valuestring);
                if(i == thid)
                {
                    return 1;
                }
            }
        }
    }
    return 0;
}

int parse_thd_if_conf(int thid)
{
    char conf_path[512];
    FILE *ifconf_file = NULL;
    long ifconf_size = 0;
    char *ifconf_buf = NULL;
    cJSON *conf_json = NULL, *tmps = NULL, *tmp = NULL, *pci = NULL, *name = NULL;
    cJSON *value1 = NULL, *value2 = NULL;
    int i = 0, thid_iter = 0, offset = 0;
    int lcore_need = 0, b_conf = 0, b_first_working = 0, b_suspend = 0;
    unsigned int tmp_hugepage;
    int lcore_list[MAX_LCORE_NUM];
    int lcore_num = 0;
    int borrow_lcore_list[MAX_LCORE_NUM];
    int borrow_lcore_num = 0;
    int port_list[MAX_IF_NUM];
    unsigned int port_num = 0, tx_lcore_num = 0;


    memset(&g_if_conf, 0, sizeof(thd_if_conf));
    memset(&g_task_conf, 0, sizeof(thd_task_conf));


    sprintf(conf_path, "%s/ifconf.json", getenv("THE_CONFPUB_PATH"));
    ifconf_file = fopen(conf_path, "r");
    if(NULL == ifconf_file)
    {
        printf("%s open fail !\n", conf_path);
        return -1;
    }
    fseek(ifconf_file, 0L, SEEK_END);
    ifconf_size = ftell(ifconf_file);
    fseek(ifconf_file, 0L, SEEK_SET);

    ifconf_buf = (char *)malloc(ifconf_size+1);
    ifconf_buf[ifconf_size] = '\0';
    fread(ifconf_buf, 1, ifconf_size, ifconf_file);
    fclose(ifconf_file);
    ifconf_file = NULL;

    conf_json = cJSON_Parse(ifconf_buf);
    if(NULL == conf_json)
    {
        printf("%s json parse error !\n", conf_path);
        free(ifconf_buf);
        ifconf_buf = NULL;
        return -1;
    }

    tmp = cJSON_GetObjectItemCaseSensitive(conf_json, "max_task");
    if(cJSON_IsNumber(tmp))
    {
        if(tmp->valueint <= MAX_TASK_NUM)
        {
            g_if_conf.max_task = tmp->valueint;
        }
        else
        {
            g_if_conf.max_task = MAX_TASK_NUM;
        }
    }

    if(thid >= g_if_conf.max_task)
    {
        cJSON_Delete(conf_json);
        conf_json = NULL;
        free(ifconf_buf);
        ifconf_buf = NULL;
        return -1;
    }

    tmps = cJSON_GetObjectItemCaseSensitive(conf_json, "if");
    if(cJSON_IsArray(tmps) && cJSON_GetArraySize(tmps))
    {
        cJSON_ArrayForEach(tmp, tmps)
        {
            pci = cJSON_GetObjectItemCaseSensitive(tmp, "pci");
            name = cJSON_GetObjectItemCaseSensitive(tmp, "name");
            if(cJSON_IsString(pci) && NULL != pci->valuestring)
            {
                strncpy(g_if_conf.if_list[g_if_conf.if_num].pci, pci->valuestring, 128);
                g_if_conf.if_list[g_if_conf.if_num].pci[127] = '\0';
            }
            if(cJSON_IsString(name) && NULL != name->valuestring)
            {
                strncpy(g_if_conf.if_list[g_if_conf.if_num].name, name->valuestring, 128);
                g_if_conf.if_list[g_if_conf.if_num].name[127] = '\0';
            }
            g_if_conf.if_num ++;
        }
    }

    g_task_conf.port_num = parse_thd_port_list(conf_json, thid, g_task_conf.port_list);
    if(0 == g_task_conf.port_num)
    {
        printf("no port for this task!\n");
        cJSON_Delete(conf_json);
        conf_json = NULL;
        free(ifconf_buf);
        ifconf_buf = NULL;
        return -1;
    }

    g_task_conf.b_suspend = parse_thd_suspend(conf_json, thid);
    if(0 == g_task_conf.b_suspend)                                              //挂起任务不转发
    {
        g_task_conf.b_forward = parse_thd_forward(conf_json, thid);
        if(g_task_conf.b_forward)
        {
            tx_lcore_num = TX_LCORE_NUM;
        }
    }

    //计算当前任务可用资源
    tmps = cJSON_GetObjectItemCaseSensitive(conf_json, "task");
    if(cJSON_IsArray(tmps) && cJSON_GetArraySize(tmps))
    {
        thid_iter = 0;
        cJSON_ArrayForEach(tmp, tmps)
        {
            if(thid_iter == thid)   //当前探针
            {
                b_conf = 1;
                value1 = cJSON_GetObjectItemCaseSensitive(tmp, "numa");         //运行的NUMA节点
                if(cJSON_IsNumber(value1))
                {
                    g_task_conf.expect_numa = value1->valueint;
                }
                value1 = cJSON_GetObjectItemCaseSensitive(tmp, "hugepage");
                if(cJSON_IsArray(value1) && cJSON_GetArraySize(value1))
                {
                    offset = 0;
                    cJSON_ArrayForEach(value2, value1)
                    {
                        if(cJSON_IsNumber(value2))
                        {
                            if(g_task_conf.mbuf_hugepage < (unsigned int)value2->valueint)
                            {
                                g_task_conf.mbuf_hugepage = (unsigned int)value2->valueint;
                            }
                            if(0 == offset)
                            {
                                offset += sprintf(&g_task_conf.socket_mem_arg[offset], "%d", value2->valueint * 1024);
                            }
                            else
                            {
                                offset += sprintf(&g_task_conf.socket_mem_arg[offset], ",%d", value2->valueint * 1024);
                            }
                        }
                    }
                }
                g_task_conf.lcore_num = parse_thd_lcore_list(conf_json, thid, g_task_conf.lcore_list);
                break;
            }
            thid_iter ++;
        }
    }
    //计算当前任务可借用资源
    if(0 == g_task_conf.b_suspend && b_conf)
    {
        tmps = cJSON_GetObjectItemCaseSensitive(conf_json, "task");
        if(cJSON_IsArray(tmps) && cJSON_GetArraySize(tmps))
        {
            thid_iter = 0;
            b_first_working = 1;
            borrow_lcore_num = 0;
            cJSON_ArrayForEach(tmp, tmps)
            {
                if(thid_iter < thid && b_first_working)
                {
                    value1 = cJSON_GetObjectItemCaseSensitive(tmp, "numa");
                    if(cJSON_IsNumber(value1))
                    {
                        if(value1->valueint == g_task_conf.expect_numa)
                        {
                            port_num = parse_thd_port_list(conf_json, thid_iter, port_list);
                            b_suspend = parse_thd_suspend(conf_json, thid_iter);
                            if(0 == port_num || b_suspend)
                            {
                                if(0 == port_num)
                                {
                                    lcore_need = 0;
                                }
                                else
                                {
                                    lcore_need = LOG_LCORE_NUM + RX_LCORE_NUM;
                                }
                                lcore_num = parse_thd_lcore_list(conf_json, thid_iter, lcore_list);
                                for(i = lcore_need; i < lcore_num && borrow_lcore_num < MAX_LCORE_NUM; i ++)
                                {
                                    borrow_lcore_list[borrow_lcore_num] = lcore_list[i];
                                    borrow_lcore_num ++;
                                }
                            }
                            else
                            {
                                b_first_working = 0;
                                borrow_lcore_num = 0;
                            }
                        }
                    }
                }
                else if(thid_iter > thid && thid_iter < g_if_conf.max_task)
                {
                    value1 = cJSON_GetObjectItemCaseSensitive(tmp, "numa");
                    if(cJSON_IsNumber(value1))
                    {
                        if(value1->valueint == g_task_conf.expect_numa)
                        {
                            port_num = parse_thd_port_list(conf_json, thid_iter, port_list);
                            b_suspend = parse_thd_suspend(conf_json, thid_iter);
                            if(0 == port_num || b_suspend)
                            {
                                if(0 == port_num)
                                {
                                    lcore_need = 0;
                                }
                                else
                                {
                                    lcore_need = LOG_LCORE_NUM + RX_LCORE_NUM;
                                }
                                lcore_num = parse_thd_lcore_list(conf_json, thid_iter, lcore_list);
                                for(i = lcore_need; i < lcore_num && borrow_lcore_num < MAX_LCORE_NUM; i ++)
                                {
                                    borrow_lcore_list[borrow_lcore_num] = lcore_list[i];
                                    borrow_lcore_num ++;
                                }
                            }
                            else
                            {
                                break;
                            }
                        }
                    }
                }
                thid_iter ++;
            }
        }
        if(borrow_lcore_num)
        {
            for(i = 0; i < borrow_lcore_num && g_task_conf.lcore_num < MAX_LCORE_NUM; i ++)
            {
                g_task_conf.lcore_list[g_task_conf.lcore_num] = borrow_lcore_list[i];
                g_task_conf.lcore_num ++;
            }
        }
    }

    if(g_task_conf.lcore_num > (LOG_LCORE_NUM + tx_lcore_num + RX_LCORE_NUM + MAX_WORKER_NUM))
    {
        g_task_conf.lcore_num = (LOG_LCORE_NUM + tx_lcore_num + RX_LCORE_NUM + MAX_WORKER_NUM);
    }
    if(g_task_conf.b_suspend && g_task_conf.lcore_num > (LOG_LCORE_NUM + tx_lcore_num + RX_LCORE_NUM))
    {
        g_task_conf.lcore_num = (LOG_LCORE_NUM + tx_lcore_num + RX_LCORE_NUM);
    }
    if(g_task_conf.lcore_num >= (LOG_LCORE_NUM + tx_lcore_num + RX_LCORE_NUM))
    {
        g_task_conf.master_lcore = g_task_conf.lcore_list[LOG_LCORE_NUM+tx_lcore_num];
    }
    else
    {
        g_task_conf.master_lcore = -1;
    }
    if(g_task_conf.lcore_num > (LOG_LCORE_NUM + tx_lcore_num + RX_LCORE_NUM))
    {
        g_task_conf.thread_num = g_task_conf.lcore_num - (LOG_LCORE_NUM + tx_lcore_num + RX_LCORE_NUM);
    }

    if((g_task_conf.mbuf_hugepage < 2)
        || (0 > g_task_conf.master_lcore)
        || ((0 == g_task_conf.b_suspend) && (0 == g_task_conf.thread_num)))
    {
        cJSON_Delete(conf_json);
        conf_json = NULL;
        free(ifconf_buf);
        ifconf_buf = NULL;
        return -1;
    }

    offset = 0;
    for(i = LOG_LCORE_NUM + tx_lcore_num; i < g_task_conf.lcore_num; i ++)
    {
        if(0 == offset)
        {
            offset += sprintf(&g_task_conf.lcore_arg[offset], "%d", g_task_conf.lcore_list[i]);
        }
        else
        {
            offset += sprintf(&g_task_conf.lcore_arg[offset], ",%d", g_task_conf.lcore_list[i]);
        }
    }

    if(g_task_conf.mbuf_hugepage < 5)
    {
        tmp_hugepage = g_task_conf.mbuf_hugepage - 1;
        if(tmp_hugepage == round_to_pow2(tmp_hugepage))
        {
            g_task_conf.mbuf_hugepage = tmp_hugepage;
        }
        else
        {
            g_task_conf.mbuf_hugepage = round_to_pow2(tmp_hugepage) / 2;
        }
    }
    else
    {
        g_task_conf.mbuf_hugepage = g_task_conf.mbuf_hugepage / 5 * 4;
    }

    g_task_conf.mbuf_num = DEF_MBUF_PER_GB * g_task_conf.mbuf_hugepage - 1;
    g_task_conf.ring_size = (g_task_conf.mbuf_num + 1) / (round_to_pow2(g_task_conf.thread_num+1));

    g_task_conf.th_id = thid;

    cJSON_Delete(conf_json);
    conf_json = NULL;
    free(ifconf_buf);
    ifconf_buf = NULL;

    return 0;
}

#ifndef __TH_DPDK_PUB_H__
#define __TH_DPDK_PUB_H__

#include <inttypes.h>

#define TX_LCORE_NUM 1
#define RX_LCORE_NUM 1
#define LOG_LCORE_NUM 1
#define MAX_TASK_NUM 4
#define MAX_IF_NUM 16
#define MAX_WORKER_NUM 64
#define MAX_LCORE_NUM 128
#define MBUF_CACHE_SIZE 250
#define DEF_MBUF_PER_GB 524288
#define MAX_PKT_BURST 128


#define NODEN_CPUMAP_PATH "/sys/devices/system/node/node%d/cpumap"
#define LIBPCAP_TX_VDEV "gstx%d"

typedef struct
{
    unsigned long long bytes;                   //网卡收字节数
    unsigned long long pkts;                    //网卡收包
    unsigned long long dev_pkts_lost;           //网卡丢包总    网卡读取
    unsigned long long ring_pkts_lost;          //软件丢包总    ring_list汇总
    unsigned long long handle_bytes;            //已处理字节数  ring_list汇总
    unsigned long long handle_pkts;             //已处理包数    ring_list汇总 all = dev_pkts_lost + pkts; pkts = ring_pkts_lost + handle_pkts + cached_pkts;
} s_if_statistics;

typedef struct
{
    s_if_statistics if_list[MAX_IF_NUM];
    struct
    {
        unsigned long long bytes_en;
        unsigned long long pkts_en;
        unsigned long long bytes_de;
        unsigned long long pkts_de;
        unsigned long long bytes_lost;
        unsigned long long pkts_lost;
        unsigned long long ring_lost_dev[MAX_IF_NUM];
        unsigned long long handle_bytes[MAX_IF_NUM];
        unsigned long long handle_pkts[MAX_IF_NUM];
    } ring_list[MAX_WORKER_NUM];
    struct
    {
        unsigned long long bytes_en;
        unsigned long long pkts_en;
        unsigned long long bytes_de;
        unsigned long long pkts_de;
        unsigned long long bytes_lost;
        unsigned long long pkts_lost;
    } tx_ring_list[MAX_WORKER_NUM];
    struct
    {
        unsigned long long pkts_tx;
        unsigned long long bytes_drop;
        unsigned long long pkts_drop;
        unsigned long long bytes_ring_lost;
        unsigned long long pkts_ring_lost;
    } tx_if;
} s_thd_statistics;

typedef struct
{
    unsigned int        th_id;                              //探针编号
    struct rte_mempool  *mbuf_pool;                         //mbuf pool
    struct rte_ring     *work_rings[MAX_WORKER_NUM];        //work ring
    struct rte_ring     *tx_rings[MAX_WORKER_NUM];          //tx ring
    s_thd_statistics    statistics;                         //数据统计
    int port_dpdk2json[MAX_IF_NUM];                         //网口映射 init -1
    int port_json2dpdk[MAX_IF_NUM];                         //网口映射 init -1
    char port_json2mac[MAX_IF_NUM][18];                     //mac地址列表
    int dpdk_port_tx;                                       //发包口 init -1
    void *tx_buffer;                                        //发包缓冲区
} S_TH_DPDK_PUB;
extern S_TH_DPDK_PUB *g_p_th_pub;

typedef struct thd_ext_pktinfo
{
    uint8_t dpdk_port_id;
    uint8_t json_port_id;
} thd_ext_pktinfo;



#endif
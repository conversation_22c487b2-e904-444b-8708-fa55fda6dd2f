#define _GNU_SOURCE             /* See feature_test_macros(7) */
#include <sched.h>
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <pthread.h>
#include <sys/wait.h>
#include <unistd.h>
#include <sys/types.h>
#include <pwd.h>
#include <rte_ring.h>
#include <rte_ether.h>
#include <rte_ethdev.h>
#include <rte_bus_pci.h>
#include <rte_mbuf.h>



#include "dpdk_conf.h"
#include "dpdk_log.h"
#include "dpdk_dll.h"


static pthread_t log_t;
unsigned int g_ts_lastlog = 0;

static int get_process_info(char *buff, int buff_size, long long task_id, long long batch_id, const char *device_id_str, unsigned int timestamp)
{
    FILE *fp;
    int father_pid, thread_num = 1;
    unsigned long meminfo;
    double cpu_times = 0, begintime = 0;
    char username[32];
    char comm[256];
    unsigned long utime, stime;
    unsigned long long starttime;
    unsigned long rss;
    long page_size_kb = sysconf(_SC_PAGESIZE) / 1024;
    
    // Get process info from /proc/self/stat
    fp = fopen("/proc/self/stat", "r");
    if(fp) {
        // Format: pid comm state ppid pgrp session tty_nr tpgid flags minflt cminflt majflt cmajflt utime stime cutime cstime priority nice num_threads itrealvalue starttime vsize rss
        fscanf(fp, "%*d %s %*c %d %*d %*d %*d %*d %*u %*lu %*lu %*lu %*lu %lu %lu %*lu %*lu %*ld %*ld %d %*lu %lu %*lu %lu",
               comm, &father_pid, &utime, &stime, &thread_num, &starttime, &rss);
        fclose(fp);
        
        // Calculate CPU times (user + system time in seconds)
        cpu_times = (utime + stime) / (double)sysconf(_SC_CLK_TCK);
        
        // Calculate memory usage (RSS in bytes)
        meminfo = rss * page_size_kb * 1024;
    }

    // Get username
    struct passwd *pw = getpwuid(geteuid());
    if(pw) {
        strncpy(username, pw->pw_name, sizeof(username));
    } else {
        strcpy(username, "unknown");
    }

    // Get system boot time from /proc/stat
   fp = fopen("/proc/stat", "r");
   unsigned long long btime = 0;
   if(fp) {
       char line[256];
       while(fgets(line, sizeof(line), fp)) {
           if(strncmp(line, "btime ", 6) == 0) {
               sscanf(line + 6, "%llu", &btime);
               break;
           }
       }
       fclose(fp);
   }

   // Calculate start time (seconds since boot)
    begintime = btime + starttime / (double)sysconf(_SC_CLK_TCK);

    // Format process info message
    return snprintf(buff, buff_size, "{\"type\":303,\"task_id\":\"%lld\",\"batch_id\":\"%lld\",\"device_id\":\"%s\",\"time\":%u,\
\"ps_info\":{\"探针主程序\":{\"name\":\"探针主程序\",\"father_pid\":%d,\"username\":\"%s\",\"begintime\":%.2f,\
\"cpu_times\":%.10f,\"meminfo\":%lu,\"thread_num\":%d}}}",
        task_id, batch_id, device_id_str, timestamp,
        father_pid, username, begintime, cpu_times, meminfo, thread_num);
}

static void* log_main( void *arg)
{
    int portid, json_port, worker_num, threadid, msglen, i;
    char buff[1024];
    int port_count = g_task_conf.port_num;
    int port_json2dpdk[MAX_IF_NUM];
    int disabled_json_if[MAX_IF_NUM];
    struct rte_eth_stats stats;
    unsigned long long total_lost, total_b, total_p, not_lost_pkt = 0, lost_pkt = 0;
    unsigned int ts_now = 0, ts3now = 0, ts3last = 0;
    s_if_statistics if_list_last[MAX_IF_NUM];
    s_if_statistics if_list_now[MAX_IF_NUM];
    s_if_statistics tmp;
    char ps_buff[1024];
    int ps_msglen;

    long long task_id = atoi(getenv("THE_TASKID"));
    long long batch_id = atoi(getenv("THE_BATCHID"));
    char device_id_str[11];
    snprintf(device_id_str, 11, "%s", getenv("THE_DEVICEID"));

    memcpy(port_json2dpdk, g_p_th_pub->port_json2dpdk, sizeof(int) * MAX_IF_NUM);
    memset(disabled_json_if, 0, sizeof(int) * MAX_IF_NUM);

    if(g_task_conf.b_suspend)
    {
        worker_num = 0;
    }
    else
    {
        worker_num = g_task_conf.thread_num;
    }
    for(json_port = 0; json_port < port_count; json_port++)
    {
        if(port_json2dpdk[json_port] < 0)
        {
            disabled_json_if[json_port] = 1;
        }
    }

    while(1)
    {
        ts_now = time(NULL);
        ts3now = ts_now / 3;
        g_ts_lastlog = ts_now / 30;
        for(i = 0; i < port_count; i ++)
        {
            json_port = g_task_conf.port_list[i];
            
            if(disabled_json_if[json_port])
            {
                continue;
            }
            portid = port_json2dpdk[json_port];

            total_lost = 0;
            total_b = 0;
            total_p = 0;

            rte_eth_stats_get(portid, &stats);
            g_p_th_pub->statistics.if_list[json_port].dev_pkts_lost = stats.imissed + stats.ierrors;

            for(threadid = 0; threadid < worker_num; threadid ++)
            {
                total_lost += g_p_th_pub->statistics.ring_list[threadid].ring_lost_dev[json_port];
                total_b += g_p_th_pub->statistics.ring_list[threadid].handle_bytes[json_port];
                total_p += g_p_th_pub->statistics.ring_list[threadid].handle_pkts[json_port];
            }
            g_p_th_pub->statistics.if_list[json_port].ring_pkts_lost = total_lost;
            g_p_th_pub->statistics.if_list[json_port].handle_bytes = total_b;
            g_p_th_pub->statistics.if_list[json_port].handle_pkts = total_p;
        }
        if(ts3last != ts3now)   //every 3s
        {
            for(i = 0; i < port_count; i ++)
            {
                json_port = g_task_conf.port_list[i];
                if_list_now[json_port] = g_p_th_pub->statistics.if_list[json_port];
            }

            if(0 == ts3last)        //first time take snapshot
            {
                for(i = 0; i < port_count; i ++)
                {
                    json_port = g_task_conf.port_list[i];
                    if_list_last[json_port] = if_list_now[json_port];
                }
            }
            else
            {
                for(i = 0; i < port_count; i ++)
                {
                    json_port = g_task_conf.port_list[i];

                    tmp.bytes = if_list_now[json_port].bytes - if_list_last[json_port].bytes;
                    tmp.pkts = if_list_now[json_port].pkts - if_list_last[json_port].pkts;
                    tmp.dev_pkts_lost = if_list_now[json_port].dev_pkts_lost - if_list_last[json_port].dev_pkts_lost;
                    tmp.ring_pkts_lost = if_list_now[json_port].ring_pkts_lost - if_list_last[json_port].ring_pkts_lost;
                    if(tmp.ring_pkts_lost > tmp.pkts)
                    {
                        tmp.ring_pkts_lost = tmp.pkts;
                    }
                    tmp.handle_bytes = if_list_now[json_port].handle_bytes - if_list_last[json_port].handle_bytes;
                    tmp.handle_pkts = if_list_now[json_port].handle_pkts - if_list_last[json_port].handle_pkts;
                    total_p = tmp.dev_pkts_lost+tmp.pkts;
                    msglen = sprintf(buff, "{\"type\":220,\"task_id\":\"%lld\",\"batch_id\":\"%lld\",\"device_id\":\"%s\",\"time\":%u,\
\"portid\":%d,\"pci\":\"%s\",\"mac\":\"%s\",\"name\":\"%s\",\
\"bps\":\"%llu\",\"pps\":\"%llu\",\"dev_drop\":\"%.3f\%\",\"ring_drop\":\"%.3f\%\",\
\"not_lost_pkts\":\"%llu\",\"lost_pkts\":\"%llu\",\
\"handle_bps\":\"%llu\",\"handle_pps\":\"%llu\"}",
                            task_id, batch_id, device_id_str, ts3now*3,
                            json_port, g_if_conf.if_list[json_port].pci, g_p_th_pub->port_json2mac[json_port], g_if_conf.if_list[json_port].name,
                            tmp.bytes * 8 / 3, tmp.pkts / 3, total_p?(((double)tmp.dev_pkts_lost*100)/total_p):0, total_p?(((double)tmp.ring_pkts_lost*100)/total_p):0,
                            tmp.handle_pkts, tmp.dev_pkts_lost+tmp.ring_pkts_lost,
                            tmp.handle_bytes * 8 / 3, tmp.handle_pkts / 3
                        );
                    nrs_handle.th_engine_send_log(nrs_handle.p_engine, "slog", NULL, 0, buff, msglen);
                    not_lost_pkt += tmp.handle_pkts;
                    lost_pkt += (tmp.dev_pkts_lost+tmp.ring_pkts_lost);
                    
                    if_list_last[json_port] = if_list_now[json_port];
                }
                if(0 == ts3now % 10)
                {
                    msglen = sprintf(buff, "{\"type\":222,\"task_id\":\"%lld\",\"batch_id\":\"%lld\",\"device_id\":\"%s\",\"time\":%u,\
\"not_lost_pkts\":\"%llu\",\"lost_pkts\":\"%llu\"}",
                            task_id, batch_id, device_id_str, ts3now*3,
                            not_lost_pkt, lost_pkt
                        );
                    nrs_handle.th_engine_send_log(nrs_handle.p_engine, "slog", NULL, 0, buff, msglen);
                    not_lost_pkt = 0;
                    lost_pkt = 0;
                }
            }
            // Get and send process info
            ps_msglen = get_process_info(ps_buff, sizeof(ps_buff), task_id, batch_id, device_id_str, ts3now*3);
            nrs_handle.th_engine_send_log(nrs_handle.p_engine, "slog", NULL, 0, ps_buff, ps_msglen);
            
            ts3last = ts3now;
        }
        usleep(10*1000);
    }

    return NULL;
}

int start_log_main(int cpuid)
{
    int ret;
    cpu_set_t mask;
    pthread_attr_t pthr_attr;

    if(cpuid >= 0)
    {
        ret = pthread_attr_init(&pthr_attr);
        if(ret)
        {
            fprintf(stderr, "pthread_attr_init error\n");
            return -1;
        }
        CPU_ZERO(&mask);
        CPU_SET(cpuid, &mask);
        ret = pthread_attr_setaffinity_np(&pthr_attr, sizeof(cpu_set_t), &mask);
        if (ret)
        {
            fprintf(stderr, "pthread_attr_setaffinity_np error\n");
            return -1;
        }
        ret = pthread_create(&log_t, &pthr_attr, log_main, NULL);
    }
    else
    {
        ret = pthread_create(&log_t, NULL, log_main, NULL);
    }
    return ret;
}



#define _GNU_SOURCE             /* See feature_test_macros(7) */
#include <sched.h>
#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <unistd.h>
#include <rte_ether.h>
#include <rte_ethdev.h>
#include <rte_bus_pci.h>
#include <rte_mbuf.h>
#include <sys/wait.h>
#include <stdio.h>

#include "the_version.h"
#include "cJSON.h"
#include "dpdk_pub.h"
#include "dpdk_conf.h"
#include "dpdk_dll.h"
#include "dpdk_log.h"
#include "dpdk_dev.h"


#define MAX_BULK 128

thd_if_conf g_if_conf;
thd_task_conf g_task_conf;

S_TH_DPDK_PUB *g_p_th_pub = NULL;

static void* tx_main(__attribute__((unused)) void *arg)
{
    struct rte_mbuf *mbuf[MAX_BULK];
    unsigned int last_timeout_ts;
    unsigned i, count;
    uint8_t times = 0;
    uint16_t len;
    int idx, send;
    struct rte_eth_dev_tx_buffer *tx_buffer = (struct rte_eth_dev_tx_buffer *)g_p_th_pub->tx_buffer;

    while(0 == g_ts_lastlog)
    {
        usleep(1);
    }

    last_timeout_ts = g_ts_lastlog;
    while(1)
    {
        for(idx = 0; idx < g_task_conf.thread_num; idx ++)
        {
            count = rte_ring_dequeue_burst(g_p_th_pub->tx_rings[idx], mbuf, MAX_BULK, NULL);
            for(i = 0; i < count; i ++)
            {
                len =  mbuf[i]-> data_len;
                g_p_th_pub->statistics.tx_ring_list[idx].bytes_de += len;
                g_p_th_pub->statistics.tx_ring_list[idx].pkts_de ++;
                send = rte_eth_tx_buffer(g_p_th_pub->dpdk_port_tx, 0, tx_buffer, mbuf[i]);
                if (send)
                {
                    g_p_th_pub->statistics.tx_if.pkts_tx += send;
                }
            }
        }
        times ++;
        if(0 == (times & 0x1f))
        {
            if(last_timeout_ts != g_ts_lastlog)
            {
                send = rte_eth_tx_buffer_flush(g_p_th_pub->dpdk_port_tx, 0, tx_buffer);
                if (send)
                {
                    g_p_th_pub->statistics.tx_if.pkts_tx += send;
                }
                last_timeout_ts = g_ts_lastlog;
            }
        }
    }
    return NULL;
}

static int slave_main(__attribute__((unused)) void *param)
{
    int thread_id = get_thread_id();
    char *data;
    uint16_t len;
    uint8_t times = 0;
    thd_ext_pktinfo *p_ext_pinfo = NULL;
    struct rte_ring *work_ring = NULL, *tx_ring = NULL;
    struct rte_mbuf *mbuf[MAX_BULK];
    unsigned int last_timeout_ts;
    unsigned count;
    int i;

    nrs_handle.th_engine_thread_init(nrs_handle.p_engine, thread_id);
    work_ring = g_p_th_pub->work_rings[thread_id];
    tx_ring = g_p_th_pub->tx_rings[thread_id];

    if(thread_id >= g_task_conf.thread_num)
    {
        return 0;
    }

    while(0 == g_ts_lastlog)
    {
        usleep(1);
    }

    last_timeout_ts = g_ts_lastlog;

    while(1)
    {
        count = rte_ring_dequeue_burst(work_ring, mbuf, MAX_BULK, NULL);
        for(i = 0; i < count; i ++)
        {
            p_ext_pinfo = (thd_ext_pktinfo *)mbuf[i]->buf_addr;
            data = (char *)(mbuf[i] -> buf_addr )+ mbuf[i] -> data_off;
            len =  mbuf[i]-> data_len;
            g_p_th_pub->statistics.ring_list[thread_id].bytes_de += len;
            g_p_th_pub->statistics.ring_list[thread_id].pkts_de ++;
            g_p_th_pub->statistics.ring_list[thread_id].handle_bytes[p_ext_pinfo->json_port_id] += len;
            g_p_th_pub->statistics.ring_list[thread_id].handle_pkts[p_ext_pinfo->json_port_id] ++;
            nrs_handle.th_engine_pkt_handle(nrs_handle.p_engine, 0, data, len, NULL, thread_id, p_ext_pinfo->json_port_id);
            if(g_task_conf.b_forward)
            {
                if(0 != rte_ring_enqueue(tx_ring, mbuf[i]))
                {
                    g_p_th_pub->statistics.tx_ring_list[thread_id].bytes_lost += len;
                    g_p_th_pub->statistics.tx_ring_list[thread_id].pkts_lost ++;
                    rte_pktmbuf_free(mbuf[i]);
                }
                else
                {
                    g_p_th_pub->statistics.tx_ring_list[thread_id].bytes_en += len;
                    g_p_th_pub->statistics.tx_ring_list[thread_id].pkts_en ++;
                }
            }
            else
            {
                rte_pktmbuf_free(mbuf[i]);
            }
        }
        times ++;
        if(0 == (times & 0x1f))
        {
            if(last_timeout_ts != g_ts_lastlog)
            {
                nrs_handle.th_engine_timeout_handle(nrs_handle.p_engine, thread_id);
                last_timeout_ts = g_ts_lastlog;
            }
        }
    }
    return 0;
}

static int master_main()
{
    int portid, count, json_port, ringid, worker_num, i;
    int port_count = rte_eth_dev_count();
    struct rte_mbuf *mbuf[MAX_BULK];
    int port_dpdk2json[MAX_IF_NUM];
    int disabled_dpdk_if[MAX_IF_NUM];

    memcpy(port_dpdk2json, g_p_th_pub->port_dpdk2json, sizeof(int) * MAX_IF_NUM);
    memset(disabled_dpdk_if, 0, sizeof(int) * MAX_IF_NUM);

    if(g_task_conf.b_suspend)
    {
        worker_num = 0;
    }
    else
    {
        worker_num = g_task_conf.thread_num;
    }

    for(portid = 0; portid < port_count; portid++)
    {
        if(port_dpdk2json[portid] < 0)
        {
            disabled_dpdk_if[portid] = 1;
        }
    }

    if(worker_num)
    {
        while(1)
        {
            for (portid = 0; portid < port_count; portid++)
            {
                if( disabled_dpdk_if[portid])
                {
                    continue;
                }
                count = rte_eth_rx_burst(portid, 0, mbuf, MAX_BULK);
                json_port = port_dpdk2json[portid];
                for (i = 0; i < count; i++)
                {
                    thd_ext_pktinfo *p_ext_pinfo = (thd_ext_pktinfo *)mbuf[i]->buf_addr;
                    p_ext_pinfo->dpdk_port_id = portid;
                    p_ext_pinfo->json_port_id = json_port;

                    g_p_th_pub->statistics.if_list[json_port].bytes += mbuf[i]-> data_len;
                    g_p_th_pub->statistics.if_list[json_port].pkts ++;

                    ringid = mbuf[i]->hash.rss % worker_num;
                    if(0 != rte_ring_enqueue(g_p_th_pub->work_rings[ringid], mbuf[i]))
                    {
                        g_p_th_pub->statistics.ring_list[ringid].bytes_lost += mbuf[i]-> data_len;
                        g_p_th_pub->statistics.ring_list[ringid].pkts_lost ++;
                        g_p_th_pub->statistics.ring_list[ringid].ring_lost_dev[json_port] ++;
                        rte_pktmbuf_free(mbuf[i]);
                    }
                    else
                    {
                        g_p_th_pub->statistics.ring_list[ringid].bytes_en += mbuf[i]-> data_len;
                        g_p_th_pub->statistics.ring_list[ringid].pkts_en ++;
                    }
                }
            }
        }
    }
    else
    {
        while(1)
        {
            for (portid = 0; portid < port_count; portid++)
            {
                if( disabled_dpdk_if[portid])
                {
                    continue;
                }
                count = rte_eth_rx_burst(portid, 0, mbuf, MAX_BULK);
                json_port = port_dpdk2json[portid];
                for (i = 0; i < count; i++)
                {
                    g_p_th_pub->statistics.if_list[json_port].bytes += mbuf[i]-> data_len;
                    g_p_th_pub->statistics.if_list[json_port].pkts ++;
                    rte_pktmbuf_free(mbuf[i]); // XXX Dont' forget!!!
                }
            }
        }
    }
    return 0;
}

static int fill_eal_args(int *p_eal_argc, char **eal_argv)
{
    int i = 0, eal_argc = 0, json_port = 0;

    *p_eal_argc = 0;

    sprintf(eal_argv[eal_argc], "thd");
    eal_argc ++;
    sprintf(eal_argv[eal_argc], "--proc-type");
    eal_argc ++;
    sprintf(eal_argv[eal_argc], "auto");
    eal_argc ++;
    sprintf(eal_argv[eal_argc], "-n");
    eal_argc ++;
    sprintf(eal_argv[eal_argc], "4");
    eal_argc ++;
    sprintf(eal_argv[eal_argc], "-l");
    eal_argc ++;
    free(eal_argv[eal_argc]);
    eal_argv[eal_argc] = strdup(g_task_conf.lcore_arg);
    eal_argc ++;
    sprintf(eal_argv[eal_argc], "--master-lcore");
    eal_argc ++;
    sprintf(eal_argv[eal_argc], "%d", g_task_conf.master_lcore);
    eal_argc ++;
    for(i = 0; i < g_task_conf.port_num; i ++)
    {
        sprintf(eal_argv[eal_argc], "-w");
        eal_argc ++;
        json_port = g_task_conf.port_list[i];
        sprintf(eal_argv[eal_argc], "%s", g_if_conf.if_list[json_port].pci);
        eal_argc ++;
    }
    if(g_task_conf.b_forward)
    {
        sprintf(eal_argv[eal_argc], "--vdev");
        eal_argc ++;
        sprintf(eal_argv[eal_argc], "net_pcap0,iface=" LIBPCAP_TX_VDEV, g_task_conf.th_id);
        eal_argc ++;
    }
    sprintf(eal_argv[eal_argc], "--file-prefix");
    eal_argc ++;
    sprintf(eal_argv[eal_argc], "thd_%d_", g_task_conf.th_id);
    eal_argc ++;
    sprintf(eal_argv[eal_argc], "--socket-mem");
    eal_argc ++;
    free(eal_argv[eal_argc]);
    eal_argv[eal_argc] = strdup(g_task_conf.socket_mem_arg);
    eal_argc ++;
    sprintf(eal_argv[eal_argc], "--");
    eal_argc ++;

    *p_eal_argc = eal_argc;
    return 0;
}

static int start_tx_main(int cpuid)
{
    int ret;
    cpu_set_t mask;
    pthread_attr_t pthr_attr;
    pthread_t tx_t;

    if(cpuid >= 0)
    {
        ret = pthread_attr_init(&pthr_attr);
        if(ret)
        {
            fprintf(stderr, "pthread_attr_init error\n");
            return -1;
        }
        CPU_ZERO(&mask);
        CPU_SET(cpuid, &mask);
        ret = pthread_attr_setaffinity_np(&pthr_attr, sizeof(cpu_set_t), &mask);
        if (ret)
        {
            fprintf(stderr, "pthread_attr_setaffinity_np error\n");
            return -1;
        }
        ret = pthread_create(&tx_t, &pthr_attr, tx_main, NULL);
    }
    else
    {
        ret = pthread_create(&tx_t, NULL, tx_main, NULL);
    }
    return ret;
}



int main(int argc, char *argv[])
{
    int b_version = 0, eal_argc = 0, th_id = -1, i = 0;
    char conf_path[256];
    char *eal_argv[64];

    for(i = 1; i < argc; i ++)
    {
        if(0 == strcmp(argv[i], "-v"))
        {
            b_version = 1;
        }
    }
    
    if(b_version)
    {
        printf("%s\n", THE_VER);
        return 0;
    }

    th_id = atoi(getenv("THE_ID"));

    if(0 != parse_thd_if_conf(th_id))
    {
        printf("ifconf.json parse error!\n");
        return -1;
    }

    if(th_id >= g_if_conf.max_task || th_id < 0)
    {
        printf("no work !\n");
        return 0;
    }

    for(i = 0; i < 64 ; i++)
    {
        eal_argv[i] = (char *)malloc(MAX_EALARG_LEN);
        eal_argv[i][0] = '\0';
    }

    fill_eal_args(&eal_argc, eal_argv);
    rte_eal_init(eal_argc, eal_argv);

    g_p_th_pub = (S_TH_DPDK_PUB *)malloc(sizeof(S_TH_DPDK_PUB));
    memset(g_p_th_pub, 0, sizeof(S_TH_DPDK_PUB));
    for(i = 0; i < MAX_IF_NUM; i ++)
    {
        g_p_th_pub->port_dpdk2json[i] = -1;
        g_p_th_pub->port_json2dpdk[i] = -1;
    }
    g_p_th_pub->th_id = th_id;

    if(0 != start_dev())
    {
        printf("failed to start device!\n");
        return -1;
    }

    sprintf(conf_path, "%s/bin/%s", getenv("THE_ROOT"), SOPATH);
    open_handle(conf_path, &nrs_handle);

    if(g_task_conf.b_suspend || 0 == g_task_conf.thread_num)
    {
        if(0 != nrs_handle.th_engine_start_log(nrs_handle.p_engine, g_task_conf.lcore_list[0]))
        {
            printf("th_engine_start_log fail!\n");
            return -3;
        }

        start_log_main(g_task_conf.lcore_list[0]);

        return master_main();
    }
    else
    {
        if(0 != nrs_handle.th_engine_init(nrs_handle.p_engine, g_task_conf.thread_num, g_if_conf.if_num))
        {
            printf("th_engine_init fail!\n");
            return -2;
        }
        if(0 != nrs_handle.th_engine_start_log(nrs_handle.p_engine, g_task_conf.lcore_list[0]))
        {
            printf("th_engine_start_log fail!\n");
            return -3;
        }

        start_log_main(g_task_conf.lcore_list[0]);

        if(g_task_conf.b_forward)
        {
            start_tx_main(g_task_conf.lcore_list[LOG_LCORE_NUM]);
        }

        rte_eal_mp_remote_launch(slave_main, NULL, SKIP_MASTER);

        return master_main();
    }
}

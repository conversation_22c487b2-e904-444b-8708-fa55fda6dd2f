// Last Update:2019-06-11 14:51:18
/**
 * @file dpdk_dll.c
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-10-28
 */
#include "dpdk_dll.h"

struct nrs_dpdk  nrs_handle ;
static int num = 0;
int get_thread_id()
{
    int old = num ;
    do {
        old = num; // 更新old
    }
    while(!__sync_bool_compare_and_swap(&num, old, old + 1));  // 如果old等于sum, 就把old+1写入sum
    return  old;
}

static void *handle = NULL;
void  open_handle(const char * so_path ,struct nrs_dpdk  *p_handle)
{
    char *error;

    handle = dlopen(so_path, RTLD_LAZY);
    if (!handle) {
        fprintf(stderr, " open  so_path error ::::::: %s\n", dlerror());
        exit(EXIT_FAILURE);
    }
    printf("打开动态库成功\n");
    //清除之前存在的错误
    dlerror();


    *(void **)(&(p_handle->th_engine_new))   = dlsym(handle, "th_engine_new");
    if ((error = dlerror()) != NULL)  {
        fprintf(stderr, "%s\n", error);
        exit(EXIT_FAILURE);
    }
    *(void **)(&(p_handle->th_engine_init))   = dlsym(handle, "th_engine_init");
    if ((error = dlerror()) != NULL)  {
        fprintf(stderr, "%s\n", error);
        exit(EXIT_FAILURE);
    }
    *(void **)(&(p_handle->th_engine_close))   = dlsym(handle, "th_engine_close");
    if ((error = dlerror()) != NULL)  {
        fprintf(stderr, "%s\n", error);
        exit(EXIT_FAILURE);
    }
    *(void **)(&(p_handle->th_engine_start_log))   = dlsym(handle, "th_engine_start_log");
    if ((error = dlerror()) != NULL)  {
        fprintf(stderr, "%s\n", error);
        exit(EXIT_FAILURE);
    }
    *(void **)(&(p_handle->th_engine_send_log))   = dlsym(handle, "th_engine_send_log");
    if ((error = dlerror()) != NULL)  {
        fprintf(stderr, "%s\n", error);
        exit(EXIT_FAILURE);
    }
    *(void **)(&(p_handle->th_engine_thread_init))   = dlsym(handle, "th_engine_thread_init");
    if ((error = dlerror()) != NULL)  {
        fprintf(stderr, "%s\n", error);
        exit(EXIT_FAILURE);
    }
    *(void **)(&(p_handle->th_engine_thread_close))   = dlsym(handle, "th_engine_thread_close");
    if ((error = dlerror()) != NULL)  {
        fprintf(stderr, "%s\n", error);
        exit(EXIT_FAILURE);
    }
    *(void **)(&(p_handle->th_engine_pkt_handle))   = dlsym(handle, "th_engine_pkt_handle");
    if ((error = dlerror()) != NULL)  {
        fprintf(stderr, "%s\n", error);
        exit(EXIT_FAILURE);
    }
    *(void **)(&(p_handle->th_engine_timeout_handle))   = dlsym(handle, "th_engine_timeout_handle");
    if ((error = dlerror()) != NULL)  {
        fprintf(stderr, "%s\n", error);
        exit(EXIT_FAILURE);
    }
    *(void **)(&(p_handle->th_engine_set_devinfo))   = dlsym(handle, "th_engine_set_devinfo");
    if ((error = dlerror()) != NULL)  {
        fprintf(stderr, "%s\n", error);
        exit(EXIT_FAILURE);
    }
    *(void **)(&(p_handle->th_engine_pkt_hash))   = dlsym(handle, "th_engine_pkt_hash");
    if ((error = dlerror()) != NULL)  {
        fprintf(stderr, "%s\n", error);
        exit(EXIT_FAILURE);
    }

    p_handle->p_engine = p_handle->th_engine_new();

}
void close_handle(void )
{
    //关闭动态链接库
    dlclose(handle);
    exit(EXIT_SUCCESS);
}


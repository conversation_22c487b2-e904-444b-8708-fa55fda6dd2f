#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <numa.h>
#include <rte_ring.h>
#include <rte_ether.h>
#include <rte_ethdev.h>
#include <rte_bus_pci.h>
#include <rte_mbuf.h>
#include <sys/wait.h>
#include <rte_malloc.h>

#include "dpdk_dev.h"
#include "dpdk_conf.h"

// ------------------------config---------------------------

#define RX_PTHRESH 	8 /* Default values of RX prefetch threshold reg. */
#define RX_HTHRESH 	8 /* Default values of RX host threshold reg. */
#define RX_WTHRESH 	4 /* Default values of RX write-back threshold reg. */

#define TX_PTHRESH 	36 /* Default values of TX prefetch threshold reg. */
#define TX_HTHRESH 	0  /* Default values of TX host threshold reg. */
#define TX_WTHRESH 	16  /* Default values of TX write-back threshold reg. */

static uint8_t rss_sym_key[40] = { 
    0x6D, 0x5A, 0x6D, 0x5A, 0x6D, 0x5A, 0x6D, 0x5A, 
    0x6D, 0x5A, 0x6D, 0x5A, 0x6D, 0x5A, 0x6D, 0x5A, 
    0x6D, 0x5A, 0x6D, 0x5A, 0x6D, 0x5A, 0x6D, 0x5A, 
    0x6D, 0x5A, 0x6D, 0x5A, 0x6D, 0x5A, 0x6D, 0x5A, 
    0x6D, 0x5A, 0x6D, 0x5A, 0x6D, 0x5A, 0x6D, 0x5A, 
}; 
/* port config */
static const struct rte_eth_conf pushad_port_conf =
{
    //	.link_speed	= ETH_LINK_SPEED_AUTONEG,
    //	.link_duplex	= ETH_LINK_AUTONEG_DUPLEX,
    .rxmode =
    {
        .mq_mode	= ETH_MQ_RX_RSS,
        .max_rx_pkt_len = ETHER_MAX_LEN,
        .split_hdr_size = 0,
        .header_split   = 0, /**< Header Split disabled */
        .hw_ip_checksum = 1, /**< IP checksum offload enabled */
        .hw_vlan_filter = 0, /**< VLAN filtering disabled */
        .jumbo_frame    = 0, /**< Jumbo Frame Support disabled */
        .hw_strip_crc   = 0, /**< CRC stripped by hardware */
    },
    .rx_adv_conf =
    {
        .rss_conf =
        {
            .rss_key = rss_sym_key,
            //.rss_hf = (ETH_RSS_IPV4
            //    | ETH_RSS_IPV6
             //   ),
            .rss_hf = ETH_RSS_IP | ETH_RSS_TCP | ETH_RSS_UDP,
        },
    },
    .txmode =
    {
        .mq_mode = ETH_MQ_TX_NONE,
    },
};

/* rx queue config */
static const struct rte_eth_rxconf pushad_rx_conf =
{
    .rx_thresh =
    {
        .pthresh = RX_PTHRESH,
        .hthresh = RX_HTHRESH,
        .wthresh = RX_WTHRESH,
    },
    .rx_free_thresh = 32,
};

/* tx queue config */

static const struct rte_eth_txconf pushad_tx_conf =
{
    .tx_thresh =
    {
        .pthresh = TX_PTHRESH,
        .hthresh = TX_HTHRESH,
        .wthresh = TX_WTHRESH,
    },
    .tx_free_thresh = 0, 
    .tx_rs_thresh 	= 1,
};
#define RTE_TEST_RX_DESC_DEFAULT 2048
#define RTE_TEST_TX_DESC_DEFAULT 2048

static uint16_t nb_rxd = RTE_TEST_RX_DESC_DEFAULT;
static uint16_t nb_txd = RTE_TEST_TX_DESC_DEFAULT;
// ------------------------config-end-----------------------

static void buffer_tx_error_cb(struct rte_mbuf **pkts, uint16_t unsent, void *userdata)
{
    unsigned i;
    for(i = 0; i < unsent; i ++)
    {
        g_p_th_pub->statistics.tx_if.bytes_drop += pkts[i]->data_len;
        rte_pktmbuf_free(pkts[i]);
    }
    g_p_th_pub->statistics.tx_if.pkts_drop += unsent;
}


int start_dev()
{
    int i = 0, portid, ret, queueid;
    char name[128] = {0}, mac_buf[18] = {0};
    struct rte_eth_dev_info dev_info;
    struct ether_addr mac_addr;
    struct rte_eth_txconf txq_conf;
    struct rte_eth_dev_tx_buffer *tx_buffer;

    g_p_th_pub->mbuf_pool = rte_pktmbuf_pool_create("dpdk_mbuf_pool", g_task_conf.mbuf_num, MBUF_CACHE_SIZE, 0, RTE_MBUF_DEFAULT_BUF_SIZE, rte_socket_id());
    if(NULL == g_p_th_pub->mbuf_pool)
    {
        printf("rte_pktmbuf_pool_create fail, check HugePages !\n");
        return -1;
    }
    if(0 == g_task_conf.b_suspend && g_task_conf.thread_num)
    {
        for(i = 0; i < g_task_conf.thread_num; i ++)
        {
            sprintf(name,"WORK_RING_%d", i);
            g_p_th_pub->work_rings[i] = rte_ring_create(name, g_task_conf.ring_size, rte_socket_id(), RING_F_SP_ENQ | RING_F_SC_DEQ);
            if(NULL == g_p_th_pub->work_rings[i])
            {
                printf("rte_ring_create fail !\n");
                return -2;
            }
        }
        if(g_task_conf.b_forward)
        {
            for(i = 0; i < g_task_conf.thread_num; i ++)
            {
                sprintf(name,"TX_RING_%d", i);
                g_p_th_pub->tx_rings[i] = rte_ring_create(name, g_task_conf.ring_size/8, rte_socket_id(), RING_F_SP_ENQ | RING_F_SC_DEQ);
                if(NULL == g_p_th_pub->tx_rings[i])
                {
                    printf("rte_ring_create fail !\n");
                    return -2;
                }
            }
        }
    }

    for (portid = 0; portid < rte_eth_dev_count(); portid++)
    {
        rte_eth_dev_info_get(portid, &dev_info);
        rte_eth_macaddr_get (portid, &mac_addr);
        sprintf(mac_buf, "%02x:%02x:%02x:%02x:%02x:%02x", mac_addr.addr_bytes[0],
                mac_addr.addr_bytes[1],
                mac_addr.addr_bytes[2],
                mac_addr.addr_bytes[3],
                mac_addr.addr_bytes[4],
                mac_addr.addr_bytes[5]
            );
        if(0 == strcmp("net_pcap", dev_info.driver_name))
        {
            g_p_th_pub->dpdk_port_tx = portid;
        }
        else if(dev_info.pci_dev)
        {
            for(i = 0; i < g_if_conf.if_num; i ++)
            {
                if(0 == strcmp(g_if_conf.if_list[i].pci, dev_info.pci_dev->name))
                {
                    strcpy(&g_p_th_pub->port_json2mac[i][0], mac_buf);
                    g_p_th_pub->port_dpdk2json[portid] = i;
                    g_p_th_pub->port_json2dpdk[i] = portid;
                    break;
                }
            }
        }
    }
    for (portid = 0; portid < rte_eth_dev_count(); portid++)
    {
        if(g_p_th_pub->port_dpdk2json[portid] < 0)
        {
            continue;
        }
        /* port setup */
        ret = rte_eth_dev_configure(portid, 1, 0, &pushad_port_conf);
        if (ret < 0)
        {
            fprintf(stderr, "rte_eth_dev_configure error\n");
            return -3;
        }
        rte_eth_dev_adjust_nb_rx_tx_desc(portid, &nb_rxd, &nb_txd);

        for (queueid = 0; queueid < 1; queueid++)
        {
            ret = rte_eth_rx_queue_setup(portid, queueid, nb_rxd, rte_socket_id(), &pushad_rx_conf, g_p_th_pub->mbuf_pool);
            if (ret < 0)
            {
                fprintf(stderr, "rte_eth_rx_queue_setup error\n");
                return -4;
            }
        }

        /* port start */
        if ((ret = rte_eth_dev_start(portid)) < 0)
        {
            fprintf(stderr, "rte_eth_dev_start error\n");
            return -5;
        }

        rte_eth_promiscuous_enable(portid);
    }
    if(g_task_conf.b_forward && g_p_th_pub->dpdk_port_tx >= 0)
    {
        portid = g_p_th_pub->dpdk_port_tx;

        rte_eth_dev_info_get(portid, &dev_info);
        rte_eth_dev_configure(portid, 0, 1, &pushad_port_conf);
        txq_conf = dev_info.default_txconf;
        txq_conf.txq_flags = ETH_TXQ_FLAGS_IGNORE;
        txq_conf.offloads = ETH_MQ_TX_NONE;
        rte_eth_dev_adjust_nb_rx_tx_desc(portid, &nb_rxd, &nb_txd);

        ret = rte_eth_tx_queue_setup(portid, 0, nb_txd, rte_socket_id(), &txq_conf);
        if (ret < 0)
        {
            fprintf(stderr, "rte_eth_tx_queue_setup error\n");
            return -4;
        }
        tx_buffer = (struct rte_eth_dev_tx_buffer *)rte_zmalloc_socket("tx_buffer", RTE_ETH_TX_BUFFER_SIZE(MAX_PKT_BURST), 0, rte_socket_id());
        if(NULL == tx_buffer)
        {
            fprintf(stderr, "Cannot allocate buffer for tx on port %u\n", portid);
            return -6;
        }
        rte_eth_tx_buffer_init(tx_buffer, MAX_PKT_BURST);
        ret = rte_eth_tx_buffer_set_err_callback(tx_buffer, buffer_tx_error_cb, NULL);
        if(ret < 0)
        {
            fprintf(stderr, "Cannot set error callback for tx buffer on port %u\n", portid);
            return -6;
        }
        g_p_th_pub->tx_buffer = (void *)tx_buffer;

        /* port start */
        if ((ret = rte_eth_dev_start(portid)) < 0)
        {
            fprintf(stderr, "rte_eth_dev_start error\n");
            return -5;
        }
    }
    if(0 == rte_eth_dev_count())
    {
        fprintf(stderr, "no port available!,check ifconf.json\n");
        return -6;
    }
    return 0;
}





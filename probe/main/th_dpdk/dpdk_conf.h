#ifndef __TH_DPDK_CONF_H__
#define __TH_DPDK_CONF_H__

#include "dpdk_pub.h"


#define MAX_EALARG_LEN 256
#define MAX_LCOREARG_LEN 1024


typedef struct
{
    char pci[128];
    char name[128];
} s_if_info;

typedef struct _thd_if_conf
{
    int max_task;                           //最大并行任务数
    unsigned int if_num;                    //全部网卡数量
    s_if_info if_list[MAX_IF_NUM];          //全部网卡列表
} thd_if_conf;

typedef struct _thd_task_conf
{
    int th_id;                              //探针编号
    int expect_numa;                        //运行的NUMA节点
    unsigned int mbuf_hugepage;             //MBUF使用的大页内存数GB
    unsigned int b_suspend;                 //任务挂起
    unsigned int b_forward;                 //任务转发
    unsigned int thread_num;                //工作线程数 LCORE_NUM-LOG_LCORE_NUM-TX_LCORE_NUM-RX_LCORE_NUM
    unsigned int mbuf_num;                  //MBUF数量
    unsigned int ring_size;                 //RING_SIZE
    int port_list[MAX_IF_NUM];              //json网口数组
    unsigned int port_num;                  //json网口数
    unsigned int lcore_num;                 //核心数
    int master_lcore;                       //MASTER LCORE
    int lcore_list[MAX_LCORE_NUM];          //核心列表
    char socket_mem_arg[MAX_EALARG_LEN];    //命令行参数
    char lcore_arg[MAX_LCOREARG_LEN];       //命令行参数
} thd_task_conf;

int parse_thd_if_conf(int thid);

extern thd_if_conf g_if_conf;
extern thd_task_conf g_task_conf;

#endif
// Last Update:2019-06-11 14:44:33
/**
 * @file dpdk_dll.h
 * @brief : 打开ia
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-01-03
 */

#ifndef DPDK_DLL_H
#define DPDK_DLL_H

#include <stdio.h>
#include <stdlib.h>
#include <dlfcn.h>
#include <stdint.h>
//#include "handle.h"
//动态链接库路径
//
// 定义接口函数 
#define SOPATH  "libth_engine.so"
#define CASADD __sync_fetch_and_add
#define CAS __sync_bool_compare_and_swap


struct nrs_dpdk
{
    void *p_engine;
    void *(*th_engine_new)();
    int (*th_engine_init)(void *p_engine, int thread_num, uint8_t port_num);
    int (*th_engine_close)(void *p_engine);
    int (*th_engine_start_log)(void *p_engine, int lcore);
    int (*th_engine_send_log)(void *p_engine, const char *topic, void *pKey, unsigned int keyLen, void *pValue, unsigned int valueLen);
    int (*th_engine_thread_init)(void *p_engine, int thread_id);
    int (*th_engine_thread_close)(void *p_engine, int thread_id);
    int (*th_engine_pkt_handle)(void *p_engine, int first_proto, unsigned char *buf, uint32_t len, uint32_t *ts, int thread_id, uint8_t port_id);
    int (*th_engine_timeout_handle)(void *p_engine, int thread_id);
    int (*th_engine_set_devinfo)(void *p_engine, uint8_t port_id, char *pci, char *mac, char *name);
    uint64_t (*th_engine_pkt_hash)(void *p_engine, int first_proto, unsigned char *buf, uint32_t len);
};
extern struct nrs_dpdk nrs_handle;
int get_thread_id();
void open_handle(const char * so_path ,struct nrs_dpdk *s_handle);
void close_handle(void );
#endif  /*DPDK_DLL_H*/

#ifndef __BASIC_PARSE_READ_PCAP_H__
#define __BASIC_PARSE_READ_PCAP_H__

#include <stdint.h>
#include <inttypes.h>

class basic_parse_read_pcap
{
public:
    basic_parse_read_pcap();
    ~basic_parse_read_pcap();
    int set_path(char* p_path);
    char* get_pkt_buff(uint32_t & len,uint32_t & time_ts);
    char* get_pkt_buff(uint32_t & len, uint32_t & caplen, uint32_t & time_ts);
    static int get_first_proto(int linktype);
private:
    void* p_CDeSnifferFileEx;

};

#endif

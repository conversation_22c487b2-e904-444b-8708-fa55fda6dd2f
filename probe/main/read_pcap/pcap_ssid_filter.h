#ifndef __PCAP_SSID_FILTER_H__
#define __PCAP_SSID_FILTER_H__

#include <string>
#include <unordered_map>
#include <set>
#include <cstdint>
#include "DataStructure/HashMap.h"

using namespace std;

class CIdxOf64
{
public:
	uint32_t operator()( const uint64_t key)
	{
		return (uint32_t)key;
	}
};

class pcap_ssid_filter {
    public:
        pcap_ssid_filter();
        ~pcap_ssid_filter();
        void init_mem();
        int add_packet(string thread_id, uint32_t pkt_ts, uint64_t ssid, int b_first_pkt);
        void read_sessionid_dir();

        //返回0 跳过该pcap, 返回1 处理该文件
        int add_file(string thread_id, uint32_t pcap_ts);
    private:
        void read_session_id();
        void clear();
        string ssid_filename;
        string thread_id;
        uint32_t ts_last_clean;
        CTemplateHashMap<uint64_t,uint32_t,CIdxOf64> *p_session_id_map;
        std::unordered_map<uint32_t,std::set<uint64_t>> timeout_id_map;
        CTemplateHashMap<uint64_t,int,CIdxOf64>*p_open_session_map;
        std::unordered_map<uint32_t,int> hour4ts_map[64];
        char print_buf[11];
};


#endif  /*PCAP_DATA_OFFLINE_H*/

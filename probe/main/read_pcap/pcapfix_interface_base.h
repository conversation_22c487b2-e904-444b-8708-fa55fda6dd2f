#ifndef __PCAPFIX_INTERFACE_BASE_H__
#define __PCAPFIX_INTERFACE_BASE_H__

#include <stdio.h>
#ifdef __WIN32__
  #include <Winsock.h>   		/* needed for htons,htonl on windows systems */

  /* fseeko, ftello are unkown on mingw, use o64 instead */
  #define fseeko fseeko64
  #define ftello ftello64

  /* compatibility for fixed size integer types on windows */
  typedef uint8_t u_int8_t;
  typedef uint16_t u_int16_t;
  typedef uint32_t u_int32_t;

#else
  #include <libgen.h>    		/* needed for basename */
  #include <arpa/inet.h>		/* htons, htonl */
#endif

struct packet_hdr_s {
  u_int32_t ts_sec;         /* timestamp seconds */
  u_int32_t ts_usec;        /* timestamp microseconds */
  u_int32_t incl_len;       /* number of octets of packet saved in file */
  u_int32_t orig_len;       /* actual length of packet */
};

typedef void (*pcapfix_handler_cb)(void *user, const struct packet_hdr_s *pkthdr, void *buf);

typedef struct {
    FILE *pfile;
    uint64_t filesize;
    uint32_t header_magic;
    int swapped;
    int nanoseconds;
    int first_check;
    unsigned int hide_len;
}pcapfix_handle;

static unsigned short _conshort(pcapfix_handle *phandle, unsigned short var) {
  if (phandle->swapped == 0) return(var);
  return(htons(var));
}
static unsigned int _conint(pcapfix_handle *phandle, unsigned int var) {
  if (phandle->swapped == 0) return(var);
  return(htonl(var));
}


#endif


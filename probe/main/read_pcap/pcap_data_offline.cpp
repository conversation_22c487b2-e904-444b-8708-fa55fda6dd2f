// Last Update:2019-12-26 14:42:28
/**
 * @file pcap_data_offline.cpp
 * @brief :
 * <AUTHOR>
 * @version 0.1.00
 * @date 2015-03-30
 */
#include "pcap_data_offline.h"
#include "TH_engine_define.h"
#include <commit_tools.h>
#include <pthread.h>
#include <libxml/parser.h>
#include <sys/stat.h>
#include <fstream>
#include <sstream>
#include <iostream>
#include <dlfcn.h>
#include <chrono>
#include <iomanip>
#include "read_conf_commit.h"
#include "geeksec_pcap_ext.h"
using namespace std;

uint64_t pcap_data_offline::send_begin_time = 0; 
uint64_t pcap_data_offline::sum_byte_num  = 0 ;
bool pcap_data_offline::b_remove_updata_time = false ;
uint32_t pcap_data_offline::ts_offline = 0;
uint32_t pcap_data_offline::ts_last_timeout = 0;

static int num = 0;
int get_thread_id()
{
    int old = num ;
    do
    {
        old = num;
    }
    while(!__sync_bool_compare_and_swap(&num, old, old + 1));  // 如果old等于sum, 就把old+1写入sum
    return  old;
}

int pcap_data_offline::conf_timeout_ts = 0;
uint32_t pcap_data_offline::max_buffer = 65536;
pcap_data_offline::pcap_data_offline(int cmd_cpu_recv)
{
    p_stat = NULL;
    thread_init_done = 0;

    pkt_enqueue = 0;
    pkt_dequeue = 0;
    conf_timeout_ts = 30;
    ts_offline = time(NULL);
    ts_last_timeout = ts_offline;
    b_move_files = 1;
    parse_readpcap_conf();
    if(cmd_cpu_recv > 0)
    {
        work_thread_num = cmd_cpu_recv;
    }
    if (work_thread_num < 1 || work_thread_num > 64)
    {
        fprintf(stderr, "work_thread_num %d error\n", work_thread_num);
        exit(1);
    }
    buf_cache = new pkt_buffer[max_buffer];
    p_distribute_ring = NULL;
    p_unused_ring = NULL;
    p_deal_rings = new NQueue*[work_thread_num];
    p_return_rings = new NQueue*[work_thread_num];
    for(int i = 0; i < work_thread_num; i ++)
    {
        p_deal_rings[i] = NULL;
        p_return_rings[i] = NULL;
    }
    p_engine = NULL;
    task_id = atoll(getenv("THE_TASKID"));
    batch_id = atoll(getenv("THE_BATCHID"));
}

pcap_data_offline::~pcap_data_offline()
{
    if(buf_cache)
    {
        delete []buf_cache;
    }

    if(p_distribute_ring)
    {
        N_queue_destry(p_distribute_ring);
    }
    if(p_unused_ring)
    {
        N_queue_destry(p_unused_ring);
    }
    if(p_deal_rings)
    {
        for(int i = 0; i < work_thread_num; i ++)
        {
            if(p_deal_rings[i])
            {
                N_queue_destry(p_deal_rings[i]);
            }
        }
        delete []p_deal_rings;
    }
    if(p_return_rings)
    {
        for(int i = 0; i < work_thread_num; i ++)
        {
            if(p_return_rings[i])
            {
                N_queue_destry(p_return_rings[i]);
            }
        }
        delete []p_return_rings;
    }
    if(p_stat)
    {
        delete p_stat;
    }
}

static bool clean_subdir_if_empty(string path)
{
    bool b_empty = true;
    bool ret;
    DIR *dir=opendir(path.c_str());
    if (NULL==dir)
    {
        return true;
    }
    struct dirent *ptr;
    struct stat statbuf;
    string child_path;

    while((ptr=readdir(dir))!=NULL)
    {
        //跳过'.'和'..'和隐藏文件
        if(strcmp(ptr->d_name,".") == 0 || strcmp(ptr->d_name,"..") == 0)
            continue;
        child_path = path + "/" + string(ptr->d_name);
        lstat(child_path.c_str(), &statbuf);
        if (S_IFDIR & statbuf.st_mode)
        {
            ret = clean_subdir_if_empty(child_path);
            if (false == ret)
            {
                b_empty = false;
            }
            else
            {
                remove(child_path.c_str());
            }
        }
    }
    
    if (b_empty)
    {
        seekdir(dir, 0);
        while((ptr=readdir(dir))!=NULL)
        {
            //跳过'.'和'..'和隐藏文件
            if(strcmp(ptr->d_name,".") == 0 || strcmp(ptr->d_name,"..") == 0)
                continue;
            child_path = path + "/" + string(ptr->d_name);
            lstat(child_path.c_str(), &statbuf);
            if (S_IFDIR & statbuf.st_mode == 0)
            {
                b_empty = false;
                break;
            }
        }
    }
    closedir(dir);
    return b_empty;
}

void pcap_data_offline::check_dir(std::string pcap_dir, int &file_num)
{
    DIR *dir=opendir(pcap_dir.c_str());
    if (NULL == dir)
    {
        return;
    }

    struct dirent *ptr;
    struct stat statbuf;
    string child_path;
    string from_dir = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/capfiles/";
    string to_dir  = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/savecaps/";

    while((ptr=readdir(dir))!=NULL)
    {
        //跳过'.'和'..'和隐藏文件
        if(ptr->d_name[0] == '.')
            continue;
        child_path = pcap_dir + "/" + string(ptr->d_name);
        lstat(child_path.c_str(), &statbuf);
        if (S_IFDIR & statbuf.st_mode)
        {
            check_dir(child_path, file_num);
            if (file_num >= 10000)
            {
                break;
            }
        }
        else if (S_IFREG & statbuf.st_mode)
        {
            if(cmp_wiat_end(ptr->d_name,".pcap") || cmp_wiat_end(ptr->d_name,".cap") || cmp_wiat_end(ptr->d_name,".pcapng"))
            {
                filelist.push_back(pcap_dir + "/" + string(ptr->d_name) );
                file_num++;
                if (file_num >= 10000)
                {
                    break;
                }
            }
            else
            {
                if (b_move_files)
                {
                    string ori_file_name = child_path;
                    string target = ori_file_name.replace(0, from_dir.length(), to_dir);
                    string path = string(target, 0, target.rfind("/")+1);
                    create_path(path);
                    rename(child_path.c_str(), target.c_str());
                }
                else
                {
                    remove(child_path.c_str());
                }
            }
        }
    }
    closedir(dir);  
}

long long read_num_file()
{
    long long readed = 0;
    ifstream number_ifs(string(getenv("THE_CONF_PATH")) + "/task_pcap_read.conf");
    if (number_ifs.is_open())
    {
        number_ifs >> readed;
    }
    return readed;
}

int update_num_file(long long readed)
{
    string tmp_file = string(getenv("THE_CONF_PATH")) + "/.task_pcap_read.conf.tmp";
    string num_file = string(getenv("THE_CONF_PATH")) + "/task_pcap_read.conf";
    ofstream out(tmp_file);
    out << readed << endl;
    out.close();
    rename(tmp_file.c_str(), num_file.c_str());
    return 0;
}

void pcap_data_offline::open_handle()
{
    char *error;

    void *handle = dlopen(SOPATH, RTLD_LAZY);
    if (!handle) {
        fprintf(stderr, " open  so_path error ::::::: %s\n", dlerror());
        exit(EXIT_FAILURE);
    }
    printf("打开动态库成功\n");
    //清除之前存在的错误
    dlerror();


    *(void **)(&(this->th_engine_new))   = dlsym(handle, "th_engine_new");
    if ((error = dlerror()) != NULL)  {
        fprintf(stderr, "%s\n", error);
        exit(EXIT_FAILURE);
    }
    *(void **)(&(this->th_engine_init))   = dlsym(handle, "th_engine_init");
    if ((error = dlerror()) != NULL)  {
        fprintf(stderr, "%s\n", error);
        exit(EXIT_FAILURE);
    }
    *(void **)(&(this->th_engine_close))   = dlsym(handle, "th_engine_close");
    if ((error = dlerror()) != NULL)  {
        fprintf(stderr, "%s\n", error);
        exit(EXIT_FAILURE);
    }
    *(void **)(&(this->th_engine_start_log))   = dlsym(handle, "th_engine_start_log");
    if ((error = dlerror()) != NULL)  {
        fprintf(stderr, "%s\n", error);
        exit(EXIT_FAILURE);
    }
    *(void **)(&(this->th_engine_send_log))   = dlsym(handle, "th_engine_send_log");
    if ((error = dlerror()) != NULL)  {
        fprintf(stderr, "%s\n", error);
        exit(EXIT_FAILURE);
    }
    *(void **)(&(this->th_engine_thread_init))   = dlsym(handle, "th_engine_thread_init");
    if ((error = dlerror()) != NULL)  {
        fprintf(stderr, "%s\n", error);
        exit(EXIT_FAILURE);
    }
    *(void **)(&(this->th_engine_thread_close))   = dlsym(handle, "th_engine_thread_close");
    if ((error = dlerror()) != NULL)  {
        fprintf(stderr, "%s\n", error);
        exit(EXIT_FAILURE);
    }
    *(void **)(&(this->th_engine_pkt_handle))   = dlsym(handle, "th_engine_pkt_handle");
    if ((error = dlerror()) != NULL)  {
        fprintf(stderr, "%s\n", error);
        exit(EXIT_FAILURE);
    }
    *(void **)(&(this->th_engine_timeout_handle))   = dlsym(handle, "th_engine_timeout_handle");
    if ((error = dlerror()) != NULL)  {
        fprintf(stderr, "%s\n", error);
        exit(EXIT_FAILURE);
    }
    *(void **)(&(this->th_engine_set_devinfo))   = dlsym(handle, "th_engine_set_devinfo");
    if ((error = dlerror()) != NULL)  {
        fprintf(stderr, "%s\n", error);
        exit(EXIT_FAILURE);
    }
    *(void **)(&(this->th_engine_pkt_hash))   = dlsym(handle, "th_engine_pkt_hash");
    if ((error = dlerror()) != NULL)  {
        fprintf(stderr, "%s\n", error);
        exit(EXIT_FAILURE);
    }

    void * obj = dlsym (handle, "gBVar_pkt_sum");
    if ((error = dlerror()) != NULL)  {
        fprintf(stderr, "%s\n", error);
        exit(EXIT_FAILURE);
    }
    p_gBVar_pkt_sum = (TH_PKT_SUM *) obj;

    obj = dlsym (handle, "th_engine_g_var");
    if ((error = dlerror()) != NULL)  {
        fprintf(stderr, "%s\n", error);
        exit(EXIT_FAILURE);
    }
    p_th_engine_g_var = (TH_ENGINE_GVAR *) obj;

    memset(p_gBVar_pkt_sum, 0, sizeof(TH_PKT_SUM) * MAX_THREAD_NUM);
    memset(p_th_engine_g_var, 0, sizeof(TH_ENGINE_GVAR));

    this->p_engine = this->th_engine_new();
    if(0 != this->th_engine_init(this->p_engine, work_thread_num, 1))
    {
        exit(-1);
    }
    if(0 != this->th_engine_start_log(this->p_engine, -1))
    {
        exit(-2);
    }

    string statistics_path = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/statistics/";
    p_stat = new offline_statistics(work_thread_num, p_gBVar_pkt_sum, p_th_engine_g_var, statistics_path);
}

void pcap_data_offline::bpf_file_pcap_pasre(string filename, basic_parse_read_pcap *p_bprp_handle, string thread_id = "", int b_ssid_filter = 0)
{
    int first_proto = p_bprp_handle -> set_path((char*)filename.c_str());
    void *p_ret = NULL;
    pkt_buffer *p_pkt = NULL;

    for(;;)
    {
        uint32_t ts_t[2] = {0};
        uint32_t len = 0;
        uint32_t caplen = 0;
        uint64_t session_id = 0;
        int b_has_session_id = 0;
        int b_first_pkt = 0;
        unsigned char * buff = (unsigned char *)p_bprp_handle -> get_pkt_buff(len, caplen, ts_t[0]);
        if(buff == NULL)
        {
            return ;
        }

        p_stat->total_pkts ++;

        if (len > MAX_PKT_LEN || len < 0)
        {
            len  =  ntohl(len);
            caplen = ntohl(caplen);
            if (len > MAX_PKT_LEN || len < 0)
            {
                // 异常格式文件  记录
                p_stat->drop_error_pkts ++;
                continue;
            }
        }

        if(b_ssid_filter && caplen >= 20 + len && PKT_ID == buff[len])
        {
            int ext_len = caplen - len;
            unsigned char *p_ext = buff + len;
            while (ext_len >= 2)
            {
                gs_pkt_tlv *ptlv = (gs_pkt_tlv *)p_ext;
                if(SS_ID == ptlv->type)
                {
                    b_has_session_id = 1;
                    memcpy(&session_id, (void *)ptlv->val, ptlv->len);
                }
                else if(FIRST_PKT == ptlv->type)
                {
                    if(ptlv->val[0])
                    {
                        b_first_pkt = 1;
                    }
                }
                p_ext += (2 + ptlv->len);
                ext_len -= (2 + ptlv->len);
            }
        }
        if(b_ssid_filter)
        {
            if(0 == b_has_session_id || ssid_filter.add_packet(thread_id, ts_t[0], session_id, b_first_pkt) <= 0)
            {
                p_stat->drop_ssid_filter ++;
                continue;
            }
        }
        
        for(;;)
        {
            for(int i = 0; i < 128; i ++)
            {
                p_ret = N_queue_dequeue(p_unused_ring);
                if(p_ret)
                {
                    break;
                }
            }
            if(p_ret)
            {
                break;
            }
            usleep(1);
        }
        p_pkt = (pkt_buffer *)p_ret;

        if( b_remove_updata_time || 0 == ts_t[0] )
        {
            ts_t[0] = 0;
            ts_t[1] = 0;
            p_pkt->init((char *)buff, len, ts_t, first_proto);
        }
        else 
        {
            if(ts_t[0] < p_stat->ts_min)
            {
                p_stat->ts_min = ts_t[0];
            }
            if(ts_t[0] > p_stat->ts_max)
            {
                p_stat->ts_max = ts_t[0];
            }
            p_pkt->init((char *)buff, len, ts_t, first_proto);
        }
        N_queue_enqueue_block(p_distribute_ring, p_ret);
        pkt_enqueue ++;
    }
}
void pcap_data_offline::fix_pcap_parse(string filename )
{
    void *fix_handle = pcapfix_open(filename.c_str());
    if(NULL == fix_handle)
    {
        return ;
    }
    int linktype = pcapfix_datalink(fix_handle);
    if(linktype >= 0)
    {
        pcap_call_back_args args(this);
        args.first_proto = basic_parse_read_pcap::get_first_proto(linktype);
        pcapfix_loop(fix_handle,pcap_data_offline::fix_call_back, (void *)&args );
    }
    pcapfix_close(fix_handle);
}
static char sz_error[PCAP_ERRBUF_SIZE];
void pcap_data_offline::file_pcap_parse(string filename, string thread_id = "", int b_ssid_filter = 0)
{
    pcap_call_back_args args(this);
    args.thread_id = thread_id;
    args.b_ssid_filter = b_ssid_filter;
    FILE *pf = fopen(filename.c_str(), "rb");
    if(NULL == pf)
    {
        return;
    }
    uint32_t magic = 0;
    if(fread(&magic, 4, 1, pf))
    {
        fseek(pf, 0, SEEK_SET);
        if(magic == 0xa1b23c4d || magic == 0x4d3cb2a1)
        {
            args.nanosec_pcap = 1;
        }
    }
    else
    {
        fclose(pf);
        return;
    }
    pcap_t* pPcap = pcap_fopen_offline(pf, sz_error);
    if (NULL == pPcap)
    {
        fclose(pf);
        return;
    }
    int linktype = pcap_datalink(pPcap);
    
    args.first_proto = basic_parse_read_pcap::get_first_proto(linktype);
    int32_t PKT_CNT_ALL = -1;
    int nRetCode = pcap_loop(pPcap, PKT_CNT_ALL, pcap_data_offline::call_back, (u_char *)&args);
    if (nRetCode < 0)
    {
        printf("PCAP LOOP ERROR %s ", pcap_geterr(pPcap));
        // 写日志 ，打开文件失败
    }
    pcap_close(pPcap);
}

static string pcap_mv_2_mid(string pcap_mid, string filename)
{
    string target = pcap_mid + string(filename, filename.rfind("/")+1);
    rename(filename.c_str(), target.c_str());
    return target;
}


static bool g_conf_bpf_parse = false;
static bool g_conf_fix_parse = false;
void pcap_data_offline::parse_readpcap_conf()
{
    string conf_path = string(getenv("THE_CONF_PATH")) + "/read_pcap.xml";
    xmlDocPtr doc = NULL;
    xmlNodePtr curNode = NULL;
    xmlNodePtr curNode_son = NULL;
    doc = xmlReadFile(conf_path.c_str(), "UTF-8", XML_PARSE_RECOVER);
    if (NULL == doc)
    {
        return;
    }
    curNode = xmlDocGetRootElement(doc);
    if (NULL == curNode)
    {
        xmlFreeDoc(doc);
        return;
    }
    if (0 != xmlStrcmp(curNode->name, (xmlChar*) "config"))
    {
        xmlFreeDoc(doc);
        return;
    }
    curNode = curNode->xmlChildrenNode;
    int i_plugin_id = 0;
    char* p_tmp = NULL;
    for(; curNode != NULL; curNode = curNode->next)
    {
        if( xmlStrcmp(curNode->name, (xmlChar*) "b_bpf_parse") == 0)
        {
            p_tmp = (char*)xmlNodeGetContent(curNode);
            if (NULL != p_tmp)
            {
                string str_key(p_tmp);
                if (str_key == "true")
                {
                    g_conf_bpf_parse = true;
                }
            }
            xmlFree(p_tmp);
            p_tmp = NULL;
        }
        else if( xmlStrcmp(curNode->name, (xmlChar*) "b_fix_parse") == 0)
        {
            p_tmp = (char*)xmlNodeGetContent(curNode);
            if (NULL != p_tmp)
            {
                string str_key(p_tmp);
                if (str_key == "true")
                {
                    g_conf_fix_parse = true;
                }
            }
            xmlFree(p_tmp);
            p_tmp = NULL;
        }
        else if(xmlStrcmp(curNode->name, (xmlChar*) "timeout_ts") == 0)
        {
            p_tmp = (char*)xmlNodeGetContent(curNode);
            int tmp_i = 0;
            if (NULL != p_tmp)
            {
                tmp_i = atoi(p_tmp);
                if (tmp_i > 0)
                {
                    conf_timeout_ts = tmp_i;
                }
            }
            xmlFree(p_tmp);
            p_tmp = NULL;
        }
        else if(xmlStrcmp(curNode->name, (xmlChar*) "max_buffer") == 0)
        {
            p_tmp = (char*)xmlNodeGetContent(curNode);
            int tmp_i = 0;
            if (NULL != p_tmp)
            {
                tmp_i = atoi(p_tmp);
                if (tmp_i > 0)
                {
                    max_buffer = (uint32_t)tmp_i;
                }
            }
            xmlFree(p_tmp);
            p_tmp = NULL;
        }
        else if(xmlStrcmp(curNode->name, (xmlChar*) "thread_num") == 0)
        {
            p_tmp = (char*)xmlNodeGetContent(curNode);
            int tmp_i = 0;
            if (NULL != p_tmp)
            {
                tmp_i = atoi(p_tmp);
                if (tmp_i > 0)
                {
                    work_thread_num = tmp_i;
                }
            }
            xmlFree(p_tmp);
            p_tmp = NULL;
        }
    }
    xmlFreeDoc(doc);

    conf_path = string(getenv("THE_CONF_PATH")) + "/th_engine_conf.xml";
    doc = xmlReadFile(conf_path.c_str(), "UTF-8", XML_PARSE_RECOVER);
    if (NULL == doc)
    {
        return;
    }
    curNode = xmlDocGetRootElement(doc);
    if (NULL == curNode)
    {
        xmlFreeDoc(doc);
        return;
    }
    if (0 != xmlStrcmp(curNode->name, (xmlChar*) "config"))
    {
        xmlFreeDoc(doc);
        abort();
        return;
    }
    curNode = curNode->xmlChildrenNode;
    i_plugin_id = 0;
    p_tmp = NULL;
    for(; curNode != NULL; curNode = curNode->next)
    {
        if( xmlStrcmp(curNode->name, (xmlChar*) "b_move_files") == 0)
        {
            p_tmp = (char*)xmlNodeGetContent(curNode);
            if (NULL != p_tmp)
            {
                string str_key(p_tmp);
                if (str_key == "true")
                {
                    b_move_files = 1;
                }
                else
                {
                    b_move_files = 0;
                }
            }
            xmlFree(p_tmp);
            p_tmp = NULL;
        }
    }
    xmlFreeDoc(doc);
    return;
}


// 读取目录下的所有 pcap 和 cap 文件
void pcap_data_offline::pcap_parse() 
{
    string midfile;
    basic_parse_read_pcap bprp_handle;
    // session_handle_ThreadInit(&m,0);
    std::string from_dir = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/capfiles/";
    std::string mid_dir  = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/midfiles/";
    std::string to_dir   = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/savecaps/";

    for(;;)
    {
        filelist.clear();
        int file_num = 0;
        check_dir(from_dir, file_num);

        // 开始读取数据包   
        list<string>::iterator itr = filelist.begin() ;

        for(;itr!= filelist.end();itr++)
        {
            string ori_filename = *itr;
            midfile = pcap_mv_2_mid(mid_dir, ori_filename);
            if(g_conf_fix_parse)
            {
                fix_pcap_parse(midfile);
            }
            else if(g_conf_bpf_parse)
            {
                bpf_file_pcap_pasre(midfile, &bprp_handle);
            }
            else
            {
                file_pcap_parse(midfile);
            }
            if (b_move_files)
            {
                string target = ori_filename.replace(0, from_dir.length(), to_dir);
                string path = string(target, 0, target.rfind("/")+1);
                create_path(path);
                rename(midfile.c_str(), target.c_str());
            }
            else
            {
                remove(midfile.c_str());
            }
            p_stat->total_files ++;
        }
        if (file_num)
        {
            clean_subdir_if_empty(from_dir);
        }
        sleep(1);
    }
}

static long long get_file_index_total()
{
    long long total = 0;
    string line;
    ifstream index_ifs(string(getenv("THE_CONF_PATH")) + "/file_index.txt");
    
    if (index_ifs.is_open())
    {
        line.resize(4096);
        while(index_ifs.getline(&line[0], 4096))
        {
            total ++;
        }
    }
    return total;
}

void pcap_data_offline::read_index_file()
{
    long long task_num = 0, readed = 0, total = 0;
    string line, filename;
    string log_buffer;
    basic_parse_read_pcap bprp_handle;
    int linelen, msglen;
    struct stat stat_buffer;

    readed = read_num_file();
    total = get_file_index_total();
    p_stat->total_files = total;
    ifstream index_ifs(string(getenv("THE_CONF_PATH")) + "/file_index.txt");
    if (index_ifs.is_open())
    {
        log_buffer.reserve(1024);
        line.resize(4096);
        while (getline(index_ifs, filename)) {
            //end.pcap标识结束
            if(filename == "end.pcap"){
                break;
            }
            if (stat(filename.c_str(), &stat_buffer) == 0) {
                p_stat->total_bytes +=  stat_buffer.st_size;
            } else {
                cerr << "Error getting size for file: " << filename << ". "
                  << "Error code: " << errno << ". "
                  << "Description: " << strerror(errno) << endl;
            } 
        }

        if (p_stat->total_bytes == 0){
            cout << "no pkts need to be handle" << endl;
            return;
        }

        p_stat->begin_time = time(NULL);
        p_stat->batch_status = 2;
        p_stat->update_mysql_db_tb_field("begin_time", to_string(p_stat->begin_time));
        p_stat->update_mysql_db_tb_field("batch_status", to_string(p_stat->batch_status));

        index_ifs.clear();
        index_ifs.seekg(ios::beg);
        while (index_ifs.getline(&line[0], 4096))
        {
            task_num++;
            if (task_num > readed)
            {
                update_num_file(++readed);
                linelen = strlen(&line[0]);
                if (linelen)
                {
                    if ('\r' == line[linelen-1])
                    {
                        linelen --;
                    }
                }
                if (linelen > 1)
                {
                    filename = string(line, 0, linelen);
                    if (g_conf_fix_parse)
                    {
                        fix_pcap_parse(filename);
                    }
                    else if (g_conf_bpf_parse)
                    {
                        bpf_file_pcap_pasre(filename, &bprp_handle);
                    }
                    else
                    {
                        file_pcap_parse(filename);
                    }
                    msglen = snprintf(&log_buffer[0], 1024, "{\"type\":%d,\"task_id\":\"%lld\",\"batch_id\":\"%lld\",\"total\":\"%lld\",\"finished\":\"%lld\",\"time\":\"%u\"}",
                        223, task_id, batch_id, total, readed, time(NULL));
                    this->th_engine_send_log(this->p_engine, "slog", NULL, 0, &log_buffer[0], msglen);
                }
            }
        }

        for (int i = 0; i < 1800; i ++)
        {
            if(pkt_dequeue < pkt_enqueue)
            {
                sleep(1);
            }
            else
            {     
                p_stat->total_pkts = p_stat->total_pkts_handled;
                p_stat->batch_progress = "1";
                p_stat->batch_status = 3;
                p_stat->end_time = time(NULL);
                p_stat->update_offline_statistics();
                send_log();

                sleep(300);
                break;
            }
        }
        return;
    }
    return;
}



void pcap_data_offline::ssid_filter_handle()
{
    long long task_num = 0, readed = 0, total = 0;
    string line, filename;
    string log_buffer;
    basic_parse_read_pcap bprp_handle;
    int linelen, msglen;
    uint32_t pcap_ts = 0;
    uint32_t pcap_id = 0;
    string thread_id = "";
    std::vector<std::string> filepath;

    ssid_filter.init_mem();
    ssid_filter.read_sessionid_dir();

    readed = read_num_file();
    total = get_file_index_total();
    ifstream index_ifs(string(getenv("THE_CONF_PATH")) + "/file_index.txt");
    if (index_ifs.is_open())
    {
        line.resize(4096);
        while (index_ifs.getline(&line[0], 4096))
        {
            task_num++;
            if (task_num > readed)
            {
                update_num_file(++readed);
                linelen = strlen(&line[0]);
                if (linelen)
                {
                    if ('\r' == line[linelen-1])
                    {
                        linelen --;
                    }
                }
                if (linelen > 1)
                {
                    filename = string(line, 0, linelen);
                    filepath.clear();
                    thread_id = "";
                    pcap_ts = 0;

                    splitEx2(filename, "/", filepath);
                    if(filepath.size() < 3)
                    {
                        continue;
                    }
                    for(int i = filepath.size()-3; i >= 0; i --)
                    {
                        if(1 == filepath[i].length())
                        {
                            if(isdigit(filepath[i].c_str()[0]))
                            {
                                thread_id = filepath[i];
                                break;
                            }
                        }
                        if(2 == filepath[i].length())
                        {
                            if(isdigit(filepath[i].c_str()[0]) && isdigit(filepath[i].c_str()[1]))
                            {
                                thread_id = filepath[i];
                                break;
                            }
                        }
                    }
                    if(thread_id == "")
                    {
                        continue;
                    }
                    if(2 != sscanf(filepath[filepath.size()-1].c_str(), "%u_%u%*s", &pcap_ts,&pcap_id))
                    {
                        continue;
                    }

                    if(0 == ssid_filter.add_file(thread_id, pcap_ts))
                    {
                        p_stat->ssid_drop_file ++;
                    }
                    else
                    {
                        bpf_file_pcap_pasre(filename, &bprp_handle, thread_id, 1);
                        p_stat->total_files ++;
                    }
                    msglen = snprintf(&log_buffer[0], 1024, "{\"type\":%d,\"task_id\":\"%lld\",\"batch_id\":\"%lld\",\"total\":\"%lld\",\"finished\":\"%lld\",\"time\":\"%u\"}",
                        223, task_id, batch_id, total, readed, time(NULL));
                    this->th_engine_send_log(this->p_engine, "slog", NULL, 0, &log_buffer[0], msglen);
                }
            }
        }
        for (int i = 0; i < 1800; i ++)
        {
            if(pkt_dequeue < pkt_enqueue)
            {
                sleep(1);
            }
            else
            {
                sleep(300);
                break;
            }
        }
        return;
    }
    return;
}

void pcap_data_offline::fix_call_back(void * user , const struct packet_hdr_s * phdr ,void * pkt)
{
    struct pcap_pkthdr hdr;
    hdr.ts.tv_sec = phdr->ts_sec;
    hdr.ts.tv_usec = phdr->ts_usec;
    hdr.caplen = phdr->incl_len;
    hdr.len = phdr->orig_len;
    call_back((u_char *)user, &hdr, (const u_char *)pkt);
}

void pcap_data_offline::call_back(u_char * user , const struct pcap_pkthdr * hdr ,const u_char * pkt)
{
    pcap_call_back_args *args = (pcap_call_back_args *)user;
    int first_proto = args->first_proto;
    int nanosec = args->nanosec_pcap;
    void *p_ret = NULL;
    pkt_buffer *p_pkt = NULL;
    uint64_t session_id = 0;
    int b_has_session_id = 0;
    int b_first_pkt = 0;
    int len = hdr -> len;
    int caplen = hdr -> caplen;
    
    if (len > MAX_PKT_LEN || len < 0)
    {
        len  =  ntohl(len);
        caplen = ntohl(caplen);
        if (len > MAX_PKT_LEN || len < 0)
        {
            // 异常格式文件  记录
            args->p_pcap_data->p_stat->drop_error_pkts ++;
            return;
        }
    }
    if(args->b_ssid_filter && caplen >= 20 + len && PKT_ID == pkt[len])
    {
        int ext_len = caplen - len;
        const u_char *p_ext = pkt + len;
        while (ext_len >= 2)
        {
            gs_pkt_tlv *ptlv = (gs_pkt_tlv *)p_ext;
            if(SS_ID == ptlv->type)
            {
                b_has_session_id = 1;
                memcpy(&session_id, (void *)ptlv->val, ptlv->len);
            }
            else if(FIRST_PKT == ptlv->type)
            {
                if(ptlv->val[0])
                {
                    b_first_pkt = 1;
                }
            }
            p_ext += (2 + ptlv->len);
            ext_len -= (2 + ptlv->len);
        }
    }
    //回调函数
    uint32_t ts[2];
    ts[0]=hdr -> ts.tv_sec;
    if(nanosec)
    {
        ts[1]=hdr -> ts.tv_usec;
    }
    else
    {
        ts[1]=hdr -> ts.tv_usec * 1000;
    }

    if(args->b_ssid_filter)
    {
        if(0 == b_has_session_id || args->p_pcap_data->ssid_filter.add_packet(args->thread_id, ts[0], session_id, b_first_pkt) <= 0)
        {
            args->p_pcap_data->p_stat->drop_ssid_filter ++;
            return;
        }
    }

    for(;;)
    {
        for(int i = 0; i < 128; i ++)
        {
            p_ret = N_queue_dequeue(args->p_pcap_data->p_unused_ring);
            if(p_ret)
            {
                break;
            }
        }
        if(p_ret)
        {
            break;
        }
        usleep(1);
    }
    p_pkt = (pkt_buffer *)p_ret;

    if( b_remove_updata_time || 0 == ts[0] )
    {
        ts[0] = 0;
        ts[1] = 0;
        p_pkt->init((char *)pkt, (uint32_t)len, ts, first_proto);
    }
    else
    {
        if(ts[0] < args->p_pcap_data->p_stat->ts_min)
        {
            args->p_pcap_data->p_stat->ts_min = ts[0];
        }
        if(ts[0] > args->p_pcap_data->p_stat->ts_max)
        {
            args->p_pcap_data->p_stat->ts_max = ts[0];
        }
        p_pkt->init((char *)pkt, (uint32_t)len, ts, first_proto);
    }
    N_queue_enqueue_block(args->p_pcap_data->p_distribute_ring, p_ret);
    args->p_pcap_data->pkt_enqueue ++;
}


void pcap_data_offline::start_ts_thread()
{
    if (pthread_create(&thread_ts, NULL, pcap_data_offline::loop_ts, (void *)this))
    {
        fprintf(stderr, "loop_ts pthread_create error\n");
        exit(-1);
    }
}

static void *workthread_main(void *arg)
{
    int de_success = 0;
    pcap_data_offline *p_pcap_read = (pcap_data_offline *)arg;
    int n_thread = get_thread_id();
    p_pcap_read->th_engine_thread_init(p_pcap_read->p_engine, n_thread);
    if(0 == n_thread)
    {
        pcap_data_offline::ts_last_timeout = pcap_data_offline::ts_offline;
    }
    __sync_fetch_and_add(&p_pcap_read->thread_init_done, 1);
    while(p_pcap_read->thread_init_done != p_pcap_read->work_thread_num)
    {
        usleep(1);
    }
    uint32_t thread_last_timeout = pcap_data_offline::ts_last_timeout;
    for(;;)
    {
        de_success = 0;
        if (thread_last_timeout != pcap_data_offline::ts_last_timeout)
        {
            thread_last_timeout = pcap_data_offline::ts_last_timeout;
            p_pcap_read->th_engine_timeout_handle(p_pcap_read->p_engine, n_thread);
        }
        for(int i = 0; i < 128; i ++)
        {
            void *p_ret = N_queue_dequeue(p_pcap_read->p_deal_rings[n_thread]);
            if(p_ret)
            {
                de_success = 1;
                pkt_buffer *p_pkt = (pkt_buffer *)p_ret;
                p_pcap_read->th_engine_pkt_handle(p_pcap_read->p_engine, p_pkt->first_proto, (unsigned char *)p_pkt->pkt_buf, p_pkt->pkt_len, p_pkt->ts, n_thread, 0);
                N_queue_enqueue_block(p_pcap_read->p_return_rings[n_thread], p_ret);
            }
        }
        if(0 == de_success)
        {
            usleep(1);
        }
    }
}

void pcap_data_offline::start_work_threads()
{
    pthread_t thread_work;
    int ret;

    p_distribute_ring = N_queue_new(pcap_data_offline::max_buffer);
    p_unused_ring = N_queue_new(pcap_data_offline::max_buffer);

    for (int i = 0; i < work_thread_num; i ++)
    {
        p_deal_rings[i] = N_queue_new(pcap_data_offline::max_buffer);
        p_return_rings[i] = N_queue_new(pcap_data_offline::max_buffer);
        ret = pthread_create(&thread_work, NULL, workthread_main, this);
        if (ret)
        {
            exit(1);
        }
    }
}

static void *distributethread_main(void *arg)
{
    void * p_ret = NULL;
    pkt_buffer *p_pkt = NULL;
    int de_success = 0;
    uint32_t thread_id = 0;
    uint32_t mod = 0;
    pcap_data_offline *p_pcap_read = (pcap_data_offline *)arg;

    if(1 == p_pcap_read->work_thread_num)
    {
        for(;;)
        {
            de_success = 0;
            for(int i = 0; i < 128; i ++)
            {
                p_ret = N_queue_dequeue(p_pcap_read->p_distribute_ring);
                if(p_ret)
                {
                    de_success = 1;
                    p_pkt = (pkt_buffer *)p_ret;
                    p_pkt->hash = 0;
                    N_queue_enqueue_block(p_pcap_read->p_deal_rings[0], p_ret);
                }
            }
            if(de_success == 0)
            {
                usleep(1);
            }
        }
    }
    else
    {
        mod = p_pcap_read->work_thread_num - 1;
        for(;;)
        {
            de_success = 0;
            for(int i = 0; i < 128; i ++)
            {
                p_ret = N_queue_dequeue(p_pcap_read->p_distribute_ring);
                if(p_ret)
                {
                    de_success = 1;
                    p_pkt = (pkt_buffer *)p_ret;
                    p_pkt->hash = p_pcap_read->th_engine_pkt_hash(p_pcap_read->p_engine, p_pkt->first_proto, (unsigned char *)p_pkt->pkt_buf, p_pkt->pkt_len);
                    if(0 == p_pkt->hash)
                    {
                        thread_id = mod;
                    }
                    else
                    {
                        thread_id = (p_pkt->hash % mod);
                    }
                    N_queue_enqueue_block(p_pcap_read->p_deal_rings[thread_id], p_ret);
                }
            }
            if(0 == de_success)
            {
                usleep(1);
            }
        }
    }
}

void pcap_data_offline::start_distribute_thread()
{
    pthread_t thread_tid;
    int ret;
    if(0 != pthread_create(&thread_tid, NULL, distributethread_main, this))
    {
        exit(1);
    }
}

static void *recoverthread_main(void *arg)
{
    void * p_ret = NULL;
    int de_success = 0;
    static int last_total_pkts_handled = 0;

    pcap_data_offline *p_pcap_read = (pcap_data_offline *)arg;
    offline_statistics *p_stat = p_pcap_read->p_stat;


    for(int i = 0; i < pcap_data_offline::max_buffer; i ++)
    {
        N_queue_enqueue_block(p_pcap_read->p_unused_ring, &p_pcap_read->buf_cache[i]);
    }
    for(;;)
    {
        de_success = 0;
        for(int i = 0; i < 128; i ++)
        {
            for(int j = 0 ; j < p_pcap_read->work_thread_num; j ++)
            {
                p_ret = N_queue_dequeue(p_pcap_read->p_return_rings[j]);
                if(p_ret)
                {
                    de_success = 1;
                    N_queue_enqueue_block(p_pcap_read->p_unused_ring, p_ret);
                    p_pcap_read ->pkt_dequeue ++;
                    p_stat->total_pkts_handled++;
                    p_stat->total_bytes_handled += ((pkt_buffer *)p_ret) -> pkt_len;
                }
            }
        }
        if(0 == de_success)
        {
            usleep(1);
        } else {
            stringstream ss;
            if(p_stat->total_pkts_handled - last_total_pkts_handled > 10000){
                last_total_pkts_handled = p_stat->total_pkts_handled;
                ss << setprecision(2) << fixed <<(double)p_stat->total_bytes_handled / p_stat->total_bytes;
                p_stat->batch_progress = ss.str();
                p_stat->update_mysql_db_tb_field("batch_progress", p_stat->batch_progress);
            }
        }
    }
}

void pcap_data_offline::start_recover_thread()
{
    pthread_t thread_tid;
    int ret;
    if(0 != pthread_create(&thread_tid, NULL, recoverthread_main, this))
    {
        exit(1);
    }
}

static void *logthread_main(void *arg)
{
    pcap_data_offline *p_pcap_read = (pcap_data_offline *)arg;
    uint32_t thread_last_timeout = pcap_data_offline::ts_last_timeout;
    for(;;)
    {
        if(thread_last_timeout != pcap_data_offline::ts_last_timeout)
        {
            thread_last_timeout = pcap_data_offline::ts_last_timeout;
            p_pcap_read->p_stat->update_summary_file();
            p_pcap_read->send_log();
            printf("\n========================================ring dump========================================\n");
            printf("ring %s    ----------used:%11d -----en:%21llu -----de:%21llu\n", "  buf_pool", p_pcap_read->p_unused_ring->cycle,p_pcap_read->p_unused_ring->en_success, p_pcap_read->p_unused_ring->de_success);
            printf("ring %s    ----------used:%11d -----en:%21llu -----de:%21llu\n", "distribute", p_pcap_read->p_distribute_ring->cycle,p_pcap_read->p_distribute_ring->en_success, p_pcap_read->p_distribute_ring->de_success);
            for(int i = 0; i < p_pcap_read->work_thread_num; i ++)
            {
                printf("ring %s%3d ----------used:%11d -----en:%21llu -----de:%21llu\n", "   to_deal", i, p_pcap_read->p_deal_rings[i]->cycle,p_pcap_read->p_deal_rings[i]->en_success, p_pcap_read->p_deal_rings[i]->de_success);
                printf("ring %s%3d ----------used:%11d -----en:%21llu -----de:%21llu\n", "to_recover", i, p_pcap_read->p_return_rings[i]->cycle,p_pcap_read->p_return_rings[i]->en_success, p_pcap_read->p_return_rings[i]->de_success);
            }
        }
        sleep(1);
    }
}

void pcap_data_offline::start_log_thread()
{
    pthread_t thread_tid;
    int ret;
    if(0 != pthread_create(&thread_tid, NULL, logthread_main, this))
    {
        exit(1);
    }
}

void* pcap_data_offline::loop_ts( void *arg)
{
    while(1)
    {
        usleep(100000);
        ts_offline = time(NULL);
        if(ts_offline >= (ts_last_timeout + conf_timeout_ts))
        {
            ts_last_timeout = ts_offline;
        }
    }
    return NULL;
}

void pcap_data_offline::send_log(){
    
    stringstream ss;
    ss << "{"
        << "\"type\":" << 224
        << ",\"task_id\":" << task_id
        << ",\"batch_id\":" << batch_id
        << ",\"begin_time\":" << p_stat->begin_time
        << ",\"end_time\":" << p_stat->end_time
        << ",\"data_begin_time\":" << p_stat->ts_min
        << ",\"data_end_time\":" << p_stat->ts_max
        << ",\"total_files\":" << p_stat->total_files
        << ",\"total_pkts\":" << p_stat->total_pkts
        << ",\"total_bytes\":" << p_stat->total_bytes
        << ",\"total_session_num\":" << p_stat->total_session_num
        << ",\"total_alarm_num\":" << p_stat->total_alarm_num
        << ",\"total_important_target_num\":" << p_stat->total_important_target_num
        << ",\"total_filter_bytes\":" << p_stat->total_filter_bytes
        << ",\"total_wl_hit_pkts\":" << p_stat->total_wl_hit_pkts
        << ",\"total_wl_hit_bytes\":" << p_stat->total_wl_hit_bytes
        << ",\"total_rule_hit_pkts\":" << p_stat->total_rule_hit_pkts
        << ",\"total_rule_hit_bytes\":" << p_stat->total_rule_hit_bytes
        << ",\"total_pkts_handled\":" << p_stat->total_pkts_handled
        << ",\"total_bytes_handled\":" << p_stat->total_bytes_handled
        << ",\"batch_progress\":" << p_stat->batch_progress
        << ",\"batch_status\":" << p_stat->batch_status
        << ",\"time\":" << static_cast<unsigned long long>(time(NULL))
        << "}";

    string log_buffer = ss.str();
    th_engine_send_log(p_engine, "slog", NULL, 0, (void *)log_buffer.c_str(), log_buffer.size());
}
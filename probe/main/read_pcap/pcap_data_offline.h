// Last Update:2019-10-17 10:55:00
/**
 * @file pcap_data_offline.h
 * @brief :
 * <AUTHOR>
 * @version 0.1.00
 * @date 2015-03-30
 */

#ifndef PCAP_DATA_OFFLINE_H
#define PCAP_DATA_OFFLINE_H

#include <pcap/pcap.h>
#include <dirent.h>
#include <dlfcn.h>
#include <sys/types.h>

#include <errno.h>
#include <string>
#include <list>
#include "pcapfix_interface.h"
#include "pcap_data_offline_statistics.h"
#include "BasicParse_Variable.h"
#include "TH_engine_define.h"
#include "basic_parse_readpcap.h"
#include "pcap_ssid_filter.h"
#include "no_lock_queue.h"

#define SOPATH  "libth_engine.so"


using namespace std;

#define MAX_PKT_LEN 100000
class pkt_buffer
{
public:
    pkt_buffer(){memset(pkt_buf, 0, MAX_PKT_LEN);}
    char pkt_buf[MAX_PKT_LEN];
    uint32_t pkt_len;
    uint32_t ts[2];
    int first_proto;
    uint64_t hash;
    void init(char *pkt, uint32_t pkt_len, uint32_t *ts, int first_proto)
    {
        memcpy(pkt_buf, pkt, pkt_len);
        this->pkt_len = pkt_len;
        this->ts[0] = ts[0];
        this->ts[1] = ts[1];
        this->first_proto = first_proto;
        this->hash = 0;
    };
};

class pcap_data_offline {
    // -- 
    public:
        pcap_data_offline(int work_thread_num = 0);
        ~pcap_data_offline();
        void open_handle();
        void start_ts_thread();
        void start_work_threads();
        void start_recover_thread();
        void start_distribute_thread();
        void start_log_thread();
        void wait_for_init() {while (thread_init_done < work_thread_num) {usleep(1);} };
        void pcap_parse();
        static void call_back(u_char *user, const struct pcap_pkthdr *hdr, const u_char *pkt);
        static void fix_call_back(void * user , const struct packet_hdr_s * phdr ,void * pkt);
        void parse_readpcap_conf();
        void read_index_file();
        void ssid_filter_handle();
        static void check_timeout();
        static void* loop_ts( void *arg);
        void send_log();
        static int conf_timeout_ts;
        static uint32_t ts_offline;
        static uint32_t ts_last_timeout;
        static uint32_t max_buffer;
        int work_thread_num;
        int thread_init_done;
        NQueue* p_distribute_ring;
        NQueue* p_unused_ring;
        NQueue** p_deal_rings;
        NQueue** p_return_rings;
        pkt_buffer *buf_cache;
        offline_statistics *p_stat;
        uint64_t pkt_enqueue;
        uint64_t pkt_dequeue;

        void *(*th_engine_new)();
        int (*th_engine_init)(void *p_engine, int thread_num, uint8_t port_num);
        int (*th_engine_close)(void *p_engine);
        int (*th_engine_start_log)(void *p_engine, int lcore);
        int (*th_engine_send_log)(void *p_engine, const char *topic, void *pKey, unsigned int keyLen, void *pValue, unsigned int valueLen);
        int (*th_engine_thread_init)(void *p_engine, int thread_id);
        int (*th_engine_thread_close)(void *p_engine, int thread_id);
        int (*th_engine_pkt_handle)(void *p_engine, int first_proto, unsigned char *buf, uint32_t len, uint32_t *ts, int thread_id, uint8_t port_id);
        int (*th_engine_timeout_handle)(void *p_engine, int thread_id);
        int (*th_engine_set_devinfo)(void *p_engine, uint8_t port_id, char *pci, char *mac, char *name);
        uint64_t (*th_engine_pkt_hash)(void *p_engine, int first_proto, unsigned char *buf, uint32_t len);

        TH_PKT_SUM *p_gBVar_pkt_sum;
        TH_ENGINE_GVAR *p_th_engine_g_var;
        void *p_engine;
        pcap_ssid_filter ssid_filter;
    private:
        void check_dir(std::string pcap_dir, int &file_num);
        static void  send_speed_control( );
        list<string> filelist;
        void file_pcap_parse(string filename, string thread_id, int b_ssid_filter);
        void fix_pcap_parse(string filename );
        void  bpf_file_pcap_pasre(string filename, basic_parse_read_pcap *p_bprp_handle, string thread_id, int b_ssid_filter);
        
        static  uint64_t  sum_byte_num ;
        static uint64_t send_begin_time;

        static  bool    b_remove_updata_time  ;
        int b_move_files;
        pthread_t thread_ts;
        long long task_id;
        long long batch_id;
};


class pcap_call_back_args
{
public:
    pcap_call_back_args(pcap_data_offline *p)
    {
        p_pcap_data = p;
        first_proto = 0;
        nanosec_pcap = 0;
        thread_id = "";
        b_ssid_filter = 0;
    }
    pcap_data_offline *p_pcap_data;
    int first_proto;
    int nanosec_pcap;
    string thread_id;
    int b_ssid_filter;
};



#endif  /*PCAP_DATA_OFFLINE_H*/

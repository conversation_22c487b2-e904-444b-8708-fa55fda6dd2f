#include "pcap_data_offline_statistics.h"
#include <string>
#include "json/json.h"
#include <unistd.h>
using namespace std;



offline_statistics::offline_statistics(uint32_t work_thread_num, TH_PKT_SUM *p_gBVar_pkt_sum, TH_ENGINE_GVAR *p_th_engine_g_var, string output)
{
    this->work_thread_num = work_thread_num;
    this->p_gBVar_pkt_sum = p_gBVar_pkt_sum;
    this->p_th_engine_g_var = p_th_engine_g_var;
    this->output = output;

    ts_min = 0xffffffff;
    ts_max = 0;
    total_files = 0;
    ssid_drop_file = 0;
    total_pkts = 0;
    drop_ssid_filter = 0;
    drop_error_pkts = 0;
    total_pkts_handled = 0;
    drop_decode_pkts = 0;
    drop_filter_pkts = 0;
    for(int i = 0 ; i < MAX_DEFENSE_TYPE; i ++)
    {
        drop_defense_pkts[i] = 0;
    }
    drop_nocreate_pkts = 0;
    total_session_pkts_ipv4 = 0;
    total_session_pkts_ipv6 = 0;
    total_session_pkts_multi_ip = 0;
    total_session_ipv4 = 0;
    total_session_ipv6 = 0;
    total_session_multi_ip = 0;
    total_bytes = 0;
    
    batch_progress = "0";
    batch_status = 1;
    begin_time = 0;
    end_time = 0;
    total_alarm_num = 0;
    total_session_num = 0;
    total_important_target_num = 0;
    total_filter_bytes = 0;
    total_rule_hit_bytes = 0;
    total_rule_hit_pkts = 0;
    total_wl_hit_bytes = 0;
    total_wl_hit_pkts = 0;

    task_id = atoll(getenv("THE_TASKID"));
    batch_id = atoll(getenv("THE_BATCHID"));

    mysql = mysql_init(NULL);
    if (!mysql) {
        cerr << "MySQL initialization failed." << mysql_error(mysql) << endl;
    }
    mysql = mysql_real_connect(mysql, mysql_config.host, mysql_config.user, mysql_config.password, NULL, mysql_config.port, NULL, 0);
    if (!mysql) {
        cerr << "MySQL connection failed: " << mysql_error(mysql) << endl;
    }
    if (mysql_select_db(mysql, mysql_config.dbName)) {
        cerr << "MySQL select db failed: " << mysql_error(mysql) << endl;
        mysql_close(mysql);
    }

}

offline_statistics::~offline_statistics()
{
    if(mysql){
        mysql_close(mysql);
        mysql = NULL;
    }
}

void offline_statistics::read_summary_file()
{
    string filepath = output + "/offline_statistics.json";
    FILE *pfile = fopen(filepath.c_str(), "r");
    if (pfile)
    {
        int len;
        fseek(pfile,0,SEEK_END);
        len = ftell(pfile);
        fseek(pfile,0,SEEK_SET);
        if (len > 0)
        {
            Json::Reader reader;
            Json::Value value;
            char *buf = new char[len];
            int size = fread(buf, 1, len, pfile);
            if (reader.parse(buf, buf+size, value, false))
            {
                if (false == value["ts_min"].isNull())
                {
                    ts_min = value["ts_min"].asUInt();
                }
                if (false == value["ts_max"].isNull())
                {
                    ts_max = value["ts_max"].asUInt();
                }
                double dval = value["total_files"].asDouble();
                total_files = (uint64_t) dval;
                dval = value["ssid_drop_file"].asDouble();
                ssid_drop_file = (uint64_t) dval;
                dval = value["total_pkts"].asDouble();
                total_pkts = (uint64_t) dval;
                dval = value["drop_ssid_filter"].asDouble();
                drop_ssid_filter = (uint64_t) dval;
                dval = value["drop_error_pkts"].asDouble();
                drop_error_pkts = (uint64_t) dval;
                dval = value["total_pkts_handled"].asDouble();
                total_pkts_handled = (uint64_t) dval;
                dval = value["drop_decode_pkts"].asDouble();
                drop_decode_pkts = (uint64_t) dval;
                dval = value["drop_filter_pkts"].asDouble();
                drop_filter_pkts = (uint64_t) dval;
                const Json::Value aval = value["drop_defense_pkts"];
                int type;
                for (int i = 0; i < aval.size(); i++)
                {
                    type = aval[i]["type"].asInt();
                    dval = aval[i]["pkts"].asDouble();
                    drop_defense_pkts[type] = (uint64_t) dval;
                }
                dval = value["drop_nocreate_pkts"].asDouble();
                drop_nocreate_pkts = (uint64_t) dval;
                dval = value["ipv4_pkts"].asDouble();
                total_session_pkts_ipv4 = (uint64_t) dval;
                dval = value["ipv6_pkts"].asDouble();
                total_session_pkts_ipv6 = (uint64_t) dval;
                dval = value["multi_ip_pkts"].asDouble();
                total_session_pkts_multi_ip = (uint64_t) dval;
                dval = value["ipv4_session"].asDouble();
                total_session_ipv4 = (uint64_t) dval;
                dval = value["ipv6_session"].asDouble();
                total_session_ipv6 = (uint64_t) dval;
                dval = value["multi_ip_session"].asDouble();
                total_session_multi_ip = (uint64_t) dval;
            }
            delete []buf;
        }
        fclose(pfile);
    }
}

void offline_statistics::update_summary_file()
{
    string filepath = output + "/offline_statistics.json";
    string filepath_back = output + "/.offline_statistics.json.tmp";

    Json::Value pkt_sum;
    if(ts_max && (ts_min <= ts_max))
    {
        pkt_sum["ts_min"] = ts_min;
        pkt_sum["ts_max"] = ts_max;
    }
    pkt_sum["total_files"] = (double)total_files;
    pkt_sum["ssid_drop_file"] = (double)ssid_drop_file;
    pkt_sum["total_pkts"] = (double)total_pkts;
    pkt_sum["drop_ssid_filter"] = (double)drop_ssid_filter;
    pkt_sum["drop_error_pkts"] = (double)drop_error_pkts;
    pkt_sum["total_pkts_handled"] = (double)total_pkts_handled;
    uint64_t tmp = drop_decode_pkts;
    for(int i = 0; i < work_thread_num; i ++)
    {
        tmp += p_gBVar_pkt_sum[i].drop_decode_pkts;
    }
    pkt_sum["drop_decode_pkts"] = (double)tmp;
    tmp = drop_filter_pkts;
    for(int i = 0; i < work_thread_num; i ++)
    {
        tmp += p_gBVar_pkt_sum[i].drop_filter_pkts;
    }
    pkt_sum["drop_filter_pkts"] = (double)tmp;

    Json::Value pkt_defense;
    for(int j=0; j < MAX_DEFENSE_TYPE; j++)
    {
        tmp = drop_defense_pkts[j];
        for(int i = 0; i < work_thread_num; i ++)
        {
            tmp += p_gBVar_pkt_sum[i].drop_defense_pkts[j];
        }
        if(tmp)
        {
            Json::Value item;
            item["type"] = j;
            item["pkts"] = (double)tmp;
            pkt_defense.append(item);
        }
    }
    pkt_sum["drop_defense_pkts"] = pkt_defense;
    tmp = drop_nocreate_pkts;
    for(int i = 0; i < work_thread_num; i ++)
    {
        tmp += p_th_engine_g_var->drop_nocreate_pkt[i];
    }
    pkt_sum["drop_nocreate_pkts"] = (double)tmp;
    tmp = total_session_pkts_ipv4;
    for(int i = 0; i < work_thread_num; i ++)
    {
        tmp += p_th_engine_g_var->ipv4_pkt[i];
    }
    pkt_sum["ipv4_pkts"] =  (double)tmp;
    tmp = total_session_pkts_ipv6;
    for(int i = 0; i < work_thread_num; i ++)
    {
        tmp += p_th_engine_g_var->ipv6_pkt[i];
    }
    pkt_sum["ipv6_pkts"] =  (double)tmp;
    tmp = total_session_pkts_multi_ip;
    for(int i = 0; i < work_thread_num; i ++)
    {
        tmp += p_th_engine_g_var->firstip_pkt[i];
    }
    pkt_sum["multi_ip_pkts"] =  (double)tmp;

    tmp = total_session_ipv4;
    for(int i = 0; i < work_thread_num; i ++)
    {
        tmp += p_th_engine_g_var->ipv4_session[i];
    }
    pkt_sum["ipv4_session"] =  (double)tmp;
    tmp = total_session_ipv6;
    for(int i = 0; i < work_thread_num; i ++)
    {
        tmp += p_th_engine_g_var->ipv6_session[i];
    }
    pkt_sum["ipv6_session"] =  (double)tmp;
    tmp = total_session_multi_ip;
    for(int i = 0; i < work_thread_num; i ++)
    {
        tmp += p_th_engine_g_var->firstip_session[i];
    }
    pkt_sum["multi_ip_session"] =  (double)tmp;

    pkt_sum["total_bytes"] =  (double)total_bytes;
    pkt_sum["batch_status"] =  (double)batch_status;
    pkt_sum["begin_time"] =  (double)begin_time;
    pkt_sum["end_time"] =  (double)end_time;
    pkt_sum["total_session_num"] =  (double)total_session_num;
    pkt_sum["total_alarm_num"] =  (double)total_alarm_num;
    pkt_sum["total_important_target_num"] =  (double)total_important_target_num;
    pkt_sum["total_filter_bytes"] =  (double)total_filter_bytes;
    pkt_sum["total_wl_hit_bytes"] =  (double)total_wl_hit_bytes;
    pkt_sum["total_rule_hit_bytes"] =  (double)total_rule_hit_bytes;
    

    FILE *pfile = fopen(filepath_back.c_str(), "w");
    if (pfile)
    {
        string jsonstr = pkt_sum.toStyledString();
        fwrite(jsonstr.c_str(), 1, jsonstr.size(), pfile);
        fclose(pfile);
        rename(filepath_back.c_str(), filepath.c_str());
    }
}

bool offline_statistics::check_mysql_connection(){
    unsigned int max_retries = 3;
    unsigned int retry_interval = 2;
    unsigned int retries = 0;
    while (retries < max_retries) {
        if (mysql_ping(mysql) == 0) {
            // 连接仍然有效
            return true;
        } else {
            printf("Attempting to reconnect...\n");
            mysql = mysql_real_connect(mysql, mysql_config.host, mysql_config.user, mysql_config.password, mysql_config.dbName, mysql_config.port, NULL, 0);
            if (mysql != NULL) {
                printf("Reconnected to the database.\n");
                return true;
            }
        }
        retries++;
        sleep(retry_interval); // 延迟重试
    }
    return false;
}

void offline_statistics::update_mysql_db_tb(string sql){
    if(check_mysql_connection()){
        if (mysql_query(mysql, sql.c_str())) {
            cerr << "MySQL Query error: " << mysql_error(mysql) << endl;
        }
    }else{
        cerr << "connect to mysql failed !!! " << endl;
    }
}

void offline_statistics::update_mysql_db_tb_field(string field, string value){
    if(check_mysql_connection()){
        string mysql_query_sql = "UPDATE tb_task_batch SET " +
                        field + "=" + value +
                        " WHERE batch_id = " + to_string(batch_id) + " AND task_id = " + to_string(task_id);
        if (mysql_query(mysql, mysql_query_sql.c_str())) {
            cerr << "MySQL Query error: " << mysql_error(mysql) << endl;
        }
    }else{
        cerr << "connect to mysql failed !!! " << endl;
    }
}

void offline_statistics::update_offline_statistics(){//汇总数据，更新数据库；当前批次处理完成后调用一次该函数
    for(int i = 0; i < work_thread_num; i++ )
    {
        total_session_num += p_th_engine_g_var->session_num[i];
        total_important_target_num += p_th_engine_g_var->important_target_num[i];
        total_rule_hit_bytes += p_gBVar_pkt_sum[i].rule_hit_bytes;
        total_rule_hit_pkts += p_gBVar_pkt_sum[i].rule_hit_pkts;
        total_filter_bytes += p_gBVar_pkt_sum[i].drop_filter_bytes;
        total_wl_hit_bytes += p_th_engine_g_var->white_list_hit_bytes[i];
        total_wl_hit_pkts += p_th_engine_g_var->white_list_hit_pkts[i];
        for(int j = 0; j < 3; j++){
            total_alarm_num += p_th_engine_g_var->alarm_num[i][j];
            total_alarm_num += p_gBVar_pkt_sum[i].alarm_num[j];
        }

    }

    string sql = "UPDATE tb_task_batch SET "
            " batch_session = " + to_string(total_session_num) + "," +
            " batch_alarm = " + to_string(total_alarm_num) + "," +
            " importrarnt_target = " + to_string(total_important_target_num) + "," +
            " filter_data_total = " + to_string(total_filter_bytes) + "," +
            " rule_hits_data_total = " + to_string(total_rule_hit_bytes) + "," +
            " whitelist_filter_total = " + to_string(total_wl_hit_bytes) + "," +
            " data_begin_time = " + to_string(ts_min) + "," +
            " data_end_time = " + to_string(ts_max) + "," +
            " end_time = " + to_string(end_time) + "," +
            " batch_bytes = " + to_string(total_bytes) + "," +
            " batch_progress = " + batch_progress + "," +
            " batch_status = " + to_string(batch_status) + 
            " WHERE batch_id = " + to_string(batch_id) + " AND task_id = " + to_string(task_id);
    update_mysql_db_tb(sql);
}
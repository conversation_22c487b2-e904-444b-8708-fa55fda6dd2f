// Last Update:2019-10-17 15:54:30
/**
 * @file read_pcap.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-08-30
 */
#define SNAP_LEN 1518       // 以太网帧最大长度
#define SIZE_ETHERNET 14   // 以太网包头长度 mac 6*2, type: 2
#define ETHER_ADDR_LEN  6  // mac地址长度
 
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/types.h> 
#include <sys/wait.h>
#include  "pcap_data_offline.h"
#include "the_version.h"

#include<fstream>
#include <sstream>
#include <iostream>
using namespace std;



/*void loop_callback(unsigned char *args, const struct pcap_pkthdr *header, const u_char *packet) {

    Handle(&m,(char *) packet ,1024,0); 
 
}*/

/*
    return 0 扫描模式
    return 1 任务模式
    return 2 任务+会话ID过滤
*/
static int get_task_type()
{
    string task_sign;
    ifstream fin;
    fin.open(string(getenv("THE_CONF_PATH"))+"/task_sign.conf", ios::in);
    if (fin.is_open())
    {
        fin >> task_sign;
    }
    if ("true" == task_sign || "1" == task_sign)
    {
        return 1;
    }
    else if ("2" == task_sign)
    {
        return 2;
    }
    return 0;
}

static int check_task_finished()
{
    long long task_num = 0, readed = 0;
    string line;
    ifstream index_ifs(string(getenv("THE_CONF_PATH")) + "/file_index.txt");
    ifstream number_ifs(string(getenv("THE_CONF_PATH")) + "/task_pcap_read.conf");
    
    if (number_ifs.is_open())
    {
        number_ifs >> readed;
    }
    if (index_ifs.is_open())
    {
        line.resize(4096);
        while(index_ifs.getline(&line[0], 4096))
        {
            if(++task_num > readed)
            {
                return 0;
            }
        }
        cout << readed << " pcap files finished! task done!" << endl;
        return 1;
    }
    else
    {
        return -1;
    }
}

int read_pcap_main(int cmd_cpu_recv = 0)
{
    int task_type = 0;

    pcap_data_offline pcap_read(cmd_cpu_recv);
    pcap_read.open_handle();//TODO:这个函数执行很慢，需要优化
    pcap_read.start_ts_thread();
    pcap_read.start_work_threads();
    pcap_read.start_recover_thread();
    pcap_read.start_distribute_thread();
    pcap_read.start_log_thread();
    pcap_read.wait_for_init();

    task_type = get_task_type();
    if(1 == task_type)
    {
        pcap_read.read_index_file();
    }
    else if(2 == task_type)
    {
        pcap_read.ssid_filter_handle();
    }
    else
    {
        pcap_read.pcap_parse();
    }
    return 1;
}

int main(int argc, char *argv[])
{
    uint8_t b_debug = 0, b_version = 0;
    int arg_i = 0, cmd_cpu_recv = 0, task_type = 0, status = 0;
    pid_t cpid;
    string tmp;

    for (int i = 1; i < argc; i++)
    {
        tmp = string(argv[i]);
        if("-d" == tmp || "-debug" == tmp)
        {
            b_debug = 1;
        }
        else if("-v" == tmp)
        {
            b_version = 1;
        }
        else if(tmp.size() && '-' == tmp[0])
        {
            if(atoi(&tmp[1]) > 0)
            {
                cmd_cpu_recv = atoi(&tmp[1]);
            }
        }
    }
    if (b_version)
    {
        cout << THE_VER << endl;
        return 0;
    }

    task_type = get_task_type();
    if (b_debug)
    {
        read_pcap_main(cmd_cpu_recv);
        return 0;
    }
    else
    {
        while(0 == task_type || 0 == check_task_finished())
        {
            cpid=fork();
            if(0 == cpid)
            {
                read_pcap_main(cmd_cpu_recv);
                exit(0);
            }
            else if(cpid > 0)
            {
                waitpid(cpid, &status, 0);
            }
            sleep(1);
        }
    }
    return 0;
}




#include "pcap_ssid_filter.h"
#include <malloc.h>
#include <sys/stat.h>
#include <dlfcn.h>
#include <dirent.h>


pcap_ssid_filter::pcap_ssid_filter()
{
    ssid_filename = "";
    ts_last_clean = 0;
    thread_id = "";
    p_session_id_map = NULL;
    p_open_session_map = NULL;
}

pcap_ssid_filter::~pcap_ssid_filter()
{
    if(p_session_id_map)
    {
        delete p_session_id_map;
    }
    if(p_open_session_map)
    {
        delete p_session_id_map;
    }
}

void pcap_ssid_filter::init_mem()
{
    p_session_id_map = new CTemplateHashMap<uint64_t,uint32_t,CIdxOf64>(25000 * 3600);
    if(0 == p_session_id_map->IsOK())
    {
        printf("session_id_filter can not alloc mem\n");
        exit(1);
    }
    p_open_session_map = new CTemplateHashMap<uint64_t,int,CIdxOf64>(25000 * 90);
    if(0 == p_open_session_map->IsOK())
    {
        delete p_session_id_map;
        p_session_id_map = NULL;
        printf("session_id_filter can not alloc mem\n");
        exit(1);
    }
}

void pcap_ssid_filter::read_sessionid_dir()
{
    string dirname = string(getenv("THE_CONF_PATH")) + "/ssidfile/";
    string threadname = "";

    DIR *dir=opendir(dirname.c_str());
    if (NULL == dir)
    {
        return;
    }

    struct dirent *ptr;
    struct stat statbuf;
    string child_path;
    int thread_id = -1;
    uint32_t ts = 0;

    while((ptr=readdir(dir))!=NULL)
    {
        //跳过'.'和'..'和隐藏文件
        if(ptr->d_name[0] == '.')
            continue;
        child_path = string(ptr->d_name);
        if(1 == child_path.length() && isdigit(child_path.c_str()[0]))
        {
            thread_id = atoi(child_path.c_str());
        }
        else if(2 == child_path.length() && isdigit(child_path.c_str()[0]) && isdigit(child_path.c_str()[1]))
        {
            thread_id = atoi(child_path.c_str());
        }
        else
        {
            continue;
        }
        if(thread_id >= 64 || thread_id < 0)
        {
            continue;
        }
        threadname = dirname + "/" + child_path;
        lstat(threadname.c_str(), &statbuf);
        if (S_IFDIR & statbuf.st_mode)
        {
            DIR *subdir=opendir(threadname.c_str());
            if (NULL == subdir)
            {
                continue;
            }
            while((ptr=readdir(subdir))!=NULL)
            {
                if(ptr->d_name[0] == '.')
                    continue;
                child_path = string(ptr->d_name);
                if(1 == sscanf(child_path.c_str(), "%u", &ts))
                {
                    ts /= 4;
                    hour4ts_map[thread_id][ts] = 1;
                }
            }
            closedir(subdir);
        }
    }
    closedir(dir);
}

void pcap_ssid_filter::clear()
{
    ssid_filename = "";
    ts_last_clean = 0;
    p_session_id_map->Reset();
    timeout_id_map.clear();
    p_open_session_map->Reset();
    // malloc_trim(0);
}
void pcap_ssid_filter::read_session_id()
{
    char buf[64];
    string filename = string(getenv("THE_CONF_PATH")) + "/ssidfile/" + ssid_filename;
    FILE *fp = fopen (filename.c_str(),"r");
    if (fp == NULL)
    {
        return;
    }
    uint64_t ssid = 0;
    uint32_t end_ts = 0;
    int ret = 0;
    while(fgets(buf, 64, fp))
    {
        if(2 == sscanf(buf, "%llu,%u", &ssid, &end_ts))
        {
            p_session_id_map->AddAlways(ssid, end_ts);
        }
    }
    fclose(fp);
}

int pcap_ssid_filter::add_file(string thread_id, uint32_t pcap_ts)
{
    int thread_id_i = atoi(thread_id.c_str());
    uint32_t dir_ts = pcap_ts / 3600 / 4;
    
    if(this->thread_id != thread_id)
    {
        clear();
        ts_last_clean = 0;
        this->thread_id = thread_id;
    }

    if((hour4ts_map[thread_id_i].find(dir_ts) == hour4ts_map[thread_id_i].end()) && (0 == p_open_session_map->GetSize()))
    {
        return 0;
    }
    return 1;
}

int pcap_ssid_filter::add_packet(string thread_id, uint32_t pkt_ts, uint64_t ssid, int b_first_pkt)
{
    //切换线程，清理所有map
    if(this->thread_id != thread_id)
    {
        clear();
        ts_last_clean = 0;
        this->thread_id = thread_id;
    }
    //线程首包
    if(0 == ts_last_clean)
    {
        ts_last_clean = pkt_ts;
    }
    //清理超时会话
    if(ts_last_clean < pkt_ts)
    {
        for(uint32_t i = ts_last_clean; i < pkt_ts; i ++)
        {
            auto search = timeout_id_map.find(i);
            if(search != timeout_id_map.end())
            {
                std::set<uint64_t> tmp_set = search->second;
                for(auto j = tmp_set.begin(); j != tmp_set.end(); j++)
                {
                    p_open_session_map->Delete(*j);
                }
                timeout_id_map.erase(search);
            }
        }
        ts_last_clean = pkt_ts;
    }
    //理论上不存在，时间戳回滚
    else if(ts_last_clean > pkt_ts)
    {
        return -1;
    }
    //判断是否需要读取会话ID列表
    sprintf(print_buf, "%u", (pkt_ts/3600));
    string key = thread_id + "/" + string(print_buf);
    if(key != ssid_filename)
    {
        ssid_filename = key;
        p_session_id_map->Reset();
        read_session_id();
    }
    //会话首包查总会话表
    if(b_first_pkt)
    {
        if(0 == p_session_id_map->GetSize())
        {
            return 0;
        }
        uint32_t ts_end;
        if(p_session_id_map->GetValue(ssid, ts_end) > 0)
        {
            //添加到并发连接表
            p_open_session_map->AddAlways(ssid, 1);
            auto search2 = timeout_id_map.find(ts_end);
            if(search2 != timeout_id_map.end())
            {
                search2->second.insert(ssid);
            }
            else
            {
                std::set<uint64_t> tmp_set;
                tmp_set.insert(ssid);
                timeout_id_map[ts_end] = tmp_set;
            }
            return 1;
        }
        else
        {
            return 0;
        }
    }
    else
    {
        if(0 == p_open_session_map->GetSize())
        {
            return 0;
        }
        //非首包查并发连接
        int tmp;
        if(p_open_session_map->GetValue(ssid,tmp) > 0)
        {
            return 1;
        }
        else
        {
            return 0;
        }
    }
    return 0;
}
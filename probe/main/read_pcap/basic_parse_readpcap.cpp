#include <stdio.h>
#include <stdlib.h>
#include "basic_parse_readpcap.h"
#include "DeSnifferFileEx.h"




basic_parse_read_pcap::basic_parse_read_pcap()
{
    p_CDeSnifferFileEx = new CDeSnifferFileEx;
    DWORD re = ((CDeSnifferFileEx*)p_CDeSnifferFileEx)->Init(1<<28);
    if(re!=0)
    {
        printf("DeFile Error");
        exit(0);
    }
}

basic_parse_read_pcap::~basic_parse_read_pcap()
{
    if (p_CDeSnifferFileEx != NULL)
    {
        ((CDeSnifferFileEx*)p_CDeSnifferFileEx)->Quit();
        delete p_CDeSnifferFileEx;
        p_CDeSnifferFileEx = NULL;
    }
}

int basic_parse_read_pcap::set_path(char* p_path)
{
    ((CDeSnifferFileEx*)p_CDeSnifferFileEx)->SetFile(p_path);
    return (int)((CDeSnifferFileEx*)p_CDeSnifferFileEx)->GetFirstPro();
}
int basic_parse_read_pcap::get_first_proto(int linktype)
{
	return (int)CDeSnifferFileEx::GetFirstPro(linktype);
}

char* basic_parse_read_pcap::get_pkt_buff(uint32_t & len,uint32_t & time_ts)
{
    STR_TIMESTAMP_JIB CulTimeStamp;
    DWORD CulPro;
    unsigned char* buff = ((CDeSnifferFileEx*)p_CDeSnifferFileEx)->GetPacket(len, CulPro, &CulTimeStamp);
    time_ts = CulTimeStamp.Time ;
    return (char*)buff;
}

char* basic_parse_read_pcap::get_pkt_buff(uint32_t & len, uint32_t & caplen, uint32_t & time_ts)
{
    STR_TIMESTAMP_JIB CulTimeStamp;
    DWORD CulPro;
    unsigned char* buff = ((CDeSnifferFileEx*)p_CDeSnifferFileEx)->GetPacket(len, CulPro, &CulTimeStamp, &caplen);
    time_ts = CulTimeStamp.Time ;
    return (char*)buff;
}

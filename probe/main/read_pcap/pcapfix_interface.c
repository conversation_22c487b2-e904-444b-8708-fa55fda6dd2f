#include "pcapfix_interface.h"
#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>
#include <string.h>
#include "pcap.h"
#include "pcapng.h"
#include "pcap_kuznet.h"

#define PCAP_EXT_MAGIC 0xa1b2cd34               /* the magic of the extended pcap global header (non swapped) */
#define PCAP_EXT_MAGIC_SWAPPED 0x34cdb2a1	/* the magic of the extended pcap global header (swapped) */
#define PCAP_MAGIC 0xa1b2c3d4			/* the magic of the pcap global header (non swapped) */
#define PCAP_MAGIC_SWAPPED 0xd4c3b2a1		/* the magic of the pcap global header (non swapped) */
#define PCAPNG_MAGIC 0x0a0d0d0a			/* the magic of the pcap global header (non swapped) */
#define PCAP_NSEC_MAGIC 0xa1b23c4d		/* the magic of the pcap global header (nanoseconds - non swapped) */
#define PCAP_NSEC_MAGIC_SWAPPED 0x4d3cb2a1  /* the magic of the pcap global header (nanoseconds - swapped) */


void *pcapfix_open(const char *path)
{
    FILE *pfile = fopen(path, "rb");
    if(pfile)
    {
        pcapfix_handle *phandle = (pcapfix_handle *)malloc(sizeof(pcapfix_handle));
        if(phandle)
        {
            memset(phandle, 0, sizeof(pcapfix_handle));
            phandle->pfile = pfile;
            phandle->first_check = 1;
            fseeko(phandle->pfile, 0, SEEK_END);
            phandle->filesize = ftello(phandle->pfile);
            fseeko(phandle->pfile, 0, SEEK_SET);
            if(phandle->filesize > 24)
            {
                fread(&phandle->header_magic, 4, 1, phandle->pfile);
                fseeko(phandle->pfile, 0, SEEK_SET);
                switch(phandle->header_magic)
                {
                    case PCAP_EXT_MAGIC:
                    case PCAP_EXT_MAGIC_SWAPPED:
                    case PCAPNG_MAGIC:
                    case PCAP_MAGIC:
                    case PCAP_MAGIC_SWAPPED:
                    case PCAP_NSEC_MAGIC:
                    case PCAP_NSEC_MAGIC_SWAPPED:
                        break;
                    default:
                        fclose(phandle->pfile);
                        free(phandle);
                        return NULL;
                }
                return phandle;
            }
            else
            {
                fclose(phandle->pfile);
                free(phandle);
                return NULL;
            }
        }
        else
        {
            fclose(pfile);
            return NULL;
        }
    }
    else
    {
        return NULL;
    }
}


int pcapfix_loop(void *arg, pcapfix_handler_cb callback, void *user)
{
    pcapfix_handle *phandle = (pcapfix_handle *)arg;
    if(phandle)
    {
        switch(phandle->header_magic)
        {
            case PCAP_EXT_MAGIC:
            case PCAP_EXT_MAGIC_SWAPPED:
                return fix_pcap_kuznetzov_callback(phandle, callback, user);
            case PCAPNG_MAGIC:
                return fix_pcapng_callback(phandle, callback, user);
            case PCAP_MAGIC:
            case PCAP_MAGIC_SWAPPED:
            case PCAP_NSEC_MAGIC:
            case PCAP_NSEC_MAGIC_SWAPPED:
                return fix_pcap_callback(phandle, callback, user);
        }
        return -1;
    }
    else
    {
        return -1;
    }
}

int pcapfix_datalink(void *arg)
{
    pcapfix_handle *phandle = (pcapfix_handle *)arg;
    if(phandle)
    {
        switch(phandle->header_magic)
        {
            case PCAP_EXT_MAGIC:
            case PCAP_EXT_MAGIC_SWAPPED:
                return fix_pcap_kuznetzov_datalink(phandle);
            case PCAPNG_MAGIC:
                return fix_pcapng_datalink(phandle);
            case PCAP_MAGIC:
            case PCAP_MAGIC_SWAPPED:
            case PCAP_NSEC_MAGIC:
            case PCAP_NSEC_MAGIC_SWAPPED:
                return fix_pcap_datalink(phandle);
        }
        return -1;
    }
    else
    {
        return -1;
    }
}

int pcapfix_close(void *arg)
{
    pcapfix_handle *phandle = (pcapfix_handle *)arg;
    if(phandle)
    {
        if(phandle->pfile)
        {
            fclose(phandle->pfile);
            phandle->pfile = NULL;
        }
        free(phandle);
    }
    return 0;
}

int pcapfix_nanoseconds(void *arg)
{
    pcapfix_handle *phandle = (pcapfix_handle *)arg;
    if(phandle)
    {
        switch(phandle->header_magic)
        {
            case PCAP_NSEC_MAGIC:
            case PCAP_NSEC_MAGIC_SWAPPED:
                return 1;
        }
    }

    return 0;
}
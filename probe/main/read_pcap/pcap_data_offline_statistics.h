#ifndef __OFFLINE_STATISTICS_H__
#define __OFFLINE_STATISTICS_H__
#include "BasicParse_Variable.h"
#include "TH_engine_define.h"
#include <mysql/mysql.h>

// 数据库连接配置
typedef struct DBConfig {
    const char* host;
    const char* user;
    const char* password;
    const char* dbName;
    unsigned int port;
}th_mysql_config;

class offline_statistics
{
public:
    offline_statistics(uint32_t work_thread_num, TH_PKT_SUM *p_gBVar_pkt_sum, TH_ENGINE_GVAR *p_th_engine_g_var, string output);
    ~offline_statistics();
    void read_summary_file();
    void update_summary_file();
    bool check_mysql_connection();
    void update_mysql_db_tb(string sql);
    void update_mysql_db_tb_field(string field, string value);
    void update_offline_statistics();
    
    uint32_t work_thread_num;

    uint32_t ts_min;
    uint32_t ts_max;
    uint64_t total_files;
    uint64_t ssid_drop_file;
    uint64_t total_pkts;
    uint64_t drop_ssid_filter;
    uint64_t drop_error_pkts;
    uint64_t total_pkts_handled;
    uint64_t total_bytes_handled;
    uint64_t drop_decode_pkts;
    uint64_t drop_filter_pkts;
    uint64_t drop_defense_pkts[MAX_DEFENSE_TYPE];
    uint64_t drop_nocreate_pkts;
    uint64_t total_session_pkts_ipv4;
    uint64_t total_session_pkts_ipv6;
    uint64_t total_session_pkts_multi_ip;
    uint64_t total_session_ipv4;
    uint64_t total_session_ipv6;
    uint64_t total_session_multi_ip;

    time_t begin_time; //导入开始时间
    time_t end_time; //导入结束时间
    uint64_t total_bytes;//批次总字节数
    string batch_progress;//处理进度
    uint64_t batch_status; //批次状态（1-等待导入；2-正在导入；3-导入完成；）
    uint64_t total_session_num;//所有线程处理的会话数汇总
    uint64_t total_alarm_num;//所有线程的所有类型告警数汇总
    uint64_t total_important_target_num;//所有线程的重点目标命中数汇总
    uint64_t total_filter_bytes;//所有线程的过滤数据量汇总
    uint64_t total_wl_hit_bytes;//所有线程的白名单命中数据量汇总
    uint64_t total_wl_hit_pkts;//所有线程的白名单命中包数汇总
    uint64_t total_rule_hit_bytes;//所有线程的规则命中数据量汇总
    uint64_t total_rule_hit_pkts;

private:
    TH_PKT_SUM *p_gBVar_pkt_sum;
    TH_ENGINE_GVAR *p_th_engine_g_var;
    string output;
    long long task_id;
    long long batch_id;
    string mysql_query_sql;
    MYSQL *mysql = NULL;
    th_mysql_config mysql_config = {
        .host = "127.0.0.1",
        .user = "root", 
        .password = "simpleuse23306p",
        .dbName = "th_analysis",
        .port = 23306
    };

};

#endif
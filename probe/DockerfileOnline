ARG builder_version
FROM hb.gs.lan/dev_52/th_common_lib:${builder_version} AS builder

ARG release_version
ARG product_name

ENV THE_ROOT=/opt/TH_ENGINE \
    THE_SDK=/opt/TH_ENGINE/sdk/ \
    RTE_SDK=/usr/local/src/dpdk-stable-18.02.2/ \
    RTE_TARGET=x86_64-native-linuxapp-gcc

COPY ./ /opt/TH_ENGINE/

RUN cd ${THE_ROOT}/scripts && bash create_local_install_bin.sh -p ${product_name} -v ${release_version}

ENV DEST=http://sfp.gs.lan/u/d/6ea101e9ca0546bb9667/

RUN cd ${THE_ROOT}/upload && rpm -ivh --force --nodeps python3/*.rpm && cd whl && pip3 install --no-index -r requirements.txt --find-links=./ && python3 ${THE_ROOT}/upload/upload.py ${THE_ROOT}/scripts/common_lib_install_local_*.tar.gz && python3 ${THE_ROOT}/upload/upload.py ${THE_ROOT}/scripts/th_release_file_local_*.tar.gz


FROM hb.gs.lan/dev_52/th_common_lib:${builder_version}

ENV THE_ROOT=/opt/TH_ENGINE \
    THE_SDK=/opt/TH_ENGINE/sdk/ \
    RTE_SDK=/usr/local/src/dpdk-stable-18.02.2/ \
    RTE_TARGET=x86_64-native-linuxapp-gcc

COPY --from=builder ${THE_ROOT}/scripts/th_release_file_local_*.tar.gz /install/

ENV THE_ROOT=/opt/GeekSec/th/ \
    THE_SDK=/opt/GeekSec/th/sdk/ \
    PATH=/opt/GeekSec/th/bin/:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/root/bin \
    LD_LIBRARY_PATH=/opt/GeekSec/th/bin/:/opt/GeekSec/th/sdk/lib/ \
    THE_CONFPUB_PATH=/opt/GeekSec/th/bin/conf_pub/ \
    THE_DB_PATH=/opt/GeekSec/th/bin/db/ \
    THE_CONF_PATH=/opt/GeekSec/th/bin/conf/0/ \
    THE_ID=0

RUN cd /install/ && \
    tar zxvf th_release_file_local_*.tar.gz && \
    ./th_release_file_local_*/th_install_local_*.bin && \
    cd / && cd / && rm -rf /install/

CMD [ "/opt/GeekSec/th/bin/thd" ]



# TEST_TASKID=0
# TEST_BATCHID=100001
# echo '{"task_id":'${TEST_TASKID}',"batch_id":'${TEST_BATCHID}'}' > /conf/${TEST_TASKID}/${TEST_BATCHID}/task_info.json
# echo '{"max_task":1,"node_mem":2,"max_thread":2,"numa":0,"if":[{"pci":"0000:04:00.1","name":"2"}],"0":[0],"suspend":[]}' > /conf/${TEST_TASKID}/${TEST_BATCHID}/ifconf.json

# docker run -d -it --privileged --name product_probe_${TEST_TASKID}_${TEST_BATCHID} \
#     -v /sys/bus/pci/drivers:/sys/bus/pci/drivers \
#     -v /sys/kernel/mm/hugepages:/sys/kernel/mm/hugepages \
#     -v /sys/devices/system/node:/sys/devices/system/node \
#     -v /dev:/dev \
#     -v /usr/sbin/dmidecode:/usr/sbin/dmidecode \
#     -v /etc/.serialnumber:/etc/.serialnumber \
#     -v /conf/${TEST_TASKID}/${TEST_BATCHID}/ifconf.json:/opt/GeekSec/th/bin/conf_pub/ifconf.json \
#     -v /conf/${TEST_TASKID}/${TEST_BATCHID}:/opt/GeekSec/th/bin/conf/0 \
#     -v /data/${TEST_TASKID}/${TEST_BATCHID}:/data/${TEST_TASKID}/${TEST_BATCHID} \
#     xxxxxxxx:xxxxxxxx

// Last Update:2019-07-08 13:30:45
/**
 * @file nssh_str.h
 * @brief 
 * <AUTHOR>
 * @version 0.0.00
 * @date 2019-04-22
 */
 

#include <commit_tools.h>
#include <sys/stat.h>
#include <sys/time.h>
#include "ssh_plugin.h"

extern "C" {
    int get_plugin_id()
    {
        return 10061;
    }
    session_pasre_base * attach()
    {
        return new ssh_plugin();
    }
}

ssh_plugin::ssh_plugin()
{
    
}

ssh_plugin::~ssh_plugin()
{
    ;
}

void ssh_plugin::J_HexToAscII(unsigned char *IN_pHexBuffer,int IN_HexBufferLen,unsigned char *OUT_pASCIIBuffer,int IN_HexBufSize,int &OUT_ASCIIBufferLen )
{
    int i,temp;
    OUT_ASCIIBufferLen=0;
    for(i=0;i<IN_HexBufferLen;i++)
    {
        OUT_pASCIIBuffer[OUT_ASCIIBufferLen]=0;
 
        temp=(IN_pHexBuffer[i]>>4)&0xf;
        if( temp<10 )
        {
            if(OUT_ASCIIBufferLen>=IN_HexBufSize) return;
            OUT_pASCIIBuffer[OUT_ASCIIBufferLen++] =0x30+temp;
        }
        else
        {
            if(OUT_ASCIIBufferLen>=IN_HexBufSize) return;
            OUT_pASCIIBuffer[OUT_ASCIIBufferLen++] =0x37+temp;
        }
        temp=IN_pHexBuffer[i]&0xf;
        if( temp<10 )
        {
            if(OUT_ASCIIBufferLen>=IN_HexBufSize) return;
            OUT_pASCIIBuffer[OUT_ASCIIBufferLen++] =0x30+temp;
        }
        else
        {
            if(OUT_ASCIIBufferLen>=IN_HexBufSize) return;
            OUT_pASCIIBuffer[OUT_ASCIIBufferLen++] =0x37+temp;
        }
 
    }
 
    OUT_pASCIIBuffer[OUT_ASCIIBufferLen]=0;
}

void ssh_plugin::init_ssh_session(ssh_session* p_ssh_session)
{
    memset(p_ssh_session, 0, sizeof(ssh_session));
    p_ssh_session->p_ssh_message = new ssh_message();
}

void ssh_plugin::reload()
{
    ;
}

bool ssh_plugin::potocol_init(session_pub* p_session, c_packet* p_packet)
{
    if(p_session==NULL || p_packet==NULL)
    {
        return false;
    }
    proto_parse_session *p_pp_session = (proto_parse_session*)(p_session->p_sp_session);
    ssh_session * p_ssh_session = (ssh_session *)p_pp_session->expansion_data;
    init_ssh_session(p_ssh_session);
    p_ssh_session->thread_id = p_packet->thread_id;
    return true;
}

bool ssh_plugin::pkt_is_whole(c_packet * p_packet)
{
    if(p_packet->m_str_packet_moudle.Stack.ProtocolNum && PROTOCOL_SSH == p_packet->m_str_packet_moudle.Stack.pProtocol[p_packet->m_str_packet_moudle.Stack.ProtocolNum-1].Protocol)
    {
        if (p_packet->app_data_len >= 4 && strncmp((const char*)p_packet->p_app_data,"SSH-",4) == 0)
        {
            return true;
        }
        else
        {
            uint8_t* p_cur = p_packet->p_app_data;
            uint32_t offset = 0;
            for(;offset + 4 <= p_packet->app_data_len;)
            {
                uint32_t length = ntohl(*(uint32_t *)(p_cur+offset));
                offset += 4;
                if(offset + length < p_packet->app_data_len)
                {
                    offset += length;
                    continue;
                }
                else if(offset + length == p_packet->app_data_len)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            return false;
        }
    }
    return false;
}

bool ssh_plugin::data_is_whole(ssh_session* p_ssh_session)
{
    if (p_ssh_session->p_data == NULL || p_ssh_session->data_len == 0)
    {
        return false;
    }
    unsigned char* p_cur = p_ssh_session->p_data;
    uint32_t offset = 0;
    if (p_ssh_session->data_len >= 4 && strncmp((const char*)p_ssh_session->p_data,"SSH-",4) == 0)
    {
        return true;
    }
    for(;offset + 4 <= p_ssh_session->data_len;)
    {
        uint32_t length = ntohl(*(uint32_t *)(p_cur+offset));
        offset += 4;
        if(offset + length < p_ssh_session->data_len)
        {
            offset += length;
            continue;
        }
        else if(offset + length == p_ssh_session->data_len)
        {
            return true;
        }
        else
        {
            return false;
        }
    }
    return false;
}

bool ssh_plugin::potocol_sign_judge(session_pub * p_session,c_packet * p_packet) //组包
{
    uint32_t seq_tmp;

    if(p_session==NULL || p_packet==NULL)
    {
        return false;
    }
    proto_parse_session* p_pp_session = (proto_parse_session * )( p_session -> p_sp_session);
    ssh_session * p_ssh_session = (ssh_session *)p_pp_session->expansion_data;
    
    if (p_packet->app_data_len == 0 || p_ssh_session->b_parse_over[p_packet->Directory] == true)
    {
        return false;
    }

    if(pkt_is_whole(p_packet))              //pkt
    {
        p_ssh_session->p_data = p_packet->p_app_data;
        p_ssh_session->data_len = p_packet->app_data_len;
        return true;
    }
    else
    {
        if(0 == p_ssh_session->b_cacheing[p_packet->Directory])
        {
            if(0 == p_session->p_session_ext)
            {
                return false;
            }
            p_session->p_tcp_recom_pop_cb(p_session, p_packet->Directory);
        }
        if(NULL == p_session->p_session_ext->tcp_repcom[p_packet->Directory])
        {
            return false;
        }
        else
        {
            p_session->p_session_ext->tcp_repcom[p_packet->Directory]->add_fragment(p_packet->seq, p_packet->app_data_len, (char*)p_packet->p_app_data);
            p_session->p_session_ext->tcp_repcom[p_packet->Directory]->front_fragment(seq_tmp, p_ssh_session->data_len, (char *&)p_ssh_session->p_data);
            p_ssh_session->b_cacheing[p_packet->Directory] = 1;
        }
    }

    if(data_is_whole(p_ssh_session))   //如果此次组包之后，恰好得到完整的N块，则进入协议解析
    {
        return true;
    }
    else if(p_session->p_session_ext->tcp_repcom[p_packet->Directory]->get_fragment_num() > 1)   //maybe pkt lost
    {
        p_ssh_session->ofo_pkt_num[p_packet->Directory] ++;
        if(p_ssh_session->ofo_pkt_num[p_packet->Directory] >= 10)            //dont wait that pkt
        {
            p_ssh_session->ofo_pkt_num[p_packet->Directory] = 0;
            return true;
        }
        return false;
    }
    else if(p_ssh_session->data_len >= 0xffff)
    {
        return true;
    }
    else    //no pkt lost
    {
        return false;
    }
}

void ssh_plugin::key_exchange_init(unsigned char* p_data, uint32_t data_len, ssh_message* p_ssh_message, unsigned char directory)
{
    int buff_len = 0;
    uint32_t tmp_len = 0;
    string tmp_string;
    uint32_t offset = 0;
    if(offset + 2 > data_len)
    {
        return;
    }
    char padding_length = *(p_data + offset);
    offset += 1;
    char message_code = *(p_data + offset);
    offset += 1;
    //获取 cookie
    if(offset + 16 > data_len)
    {
        return;
    }
    J_HexToAscII(p_data + offset, 16, uc_arr, 65535, buff_len);
    string cookie((char*)uc_arr, buff_len);
    p_ssh_message->cookie[directory] = cookie;
    offset += 16;
//获取 kex_algorithms
    if(offset + 4 > data_len)
    {
        return;
    }
    tmp_len = ntohl(*(uint32_t *)(p_data + offset));
    offset += 4;
    if(offset + tmp_len > data_len)
    {
        return;
    }
    tmp_string = string((char *)p_data + offset, tmp_len);
    splitEx(tmp_string, ",", p_ssh_message->kex_algorithms[directory]);
    offset += tmp_len;

//获取 server_host_key_algorithms
    if(offset + 4 > data_len)
    {
        return;
    }
    tmp_len = ntohl(*(uint32_t *)(p_data + offset));
    offset += 4;
    if(offset + tmp_len > data_len)
    {
        return;
    }
    tmp_string = string((char *)p_data + offset, tmp_len);
    splitEx(tmp_string, ",", p_ssh_message->server_host_key_algorithms[directory]);
    offset += tmp_len;

//获取 encryption_algorithms_client_to_server
    if(offset + 4 > data_len)
    {
        return;
    }
    tmp_len = ntohl(*(uint32_t *)(p_data + offset));
    offset += 4;
    if(offset + tmp_len > data_len)
    {
        return;
    }
    tmp_string = string((char *)p_data + offset, tmp_len);
    splitEx(tmp_string, ",", p_ssh_message->encryption_algorithms_client_to_server[directory]);
    offset += tmp_len;

//获取 encryption_algorithms_server_to_client
    if(offset + 4 > data_len)
    {
        return;
    }
    tmp_len = ntohl(*(uint32_t *)(p_data + offset));
    offset += 4;
    if(offset + tmp_len > data_len)
    {
        return;
    }
    tmp_string = string((char *)p_data + offset, tmp_len);
    splitEx(tmp_string, ",", p_ssh_message->encryption_algorithms_server_to_client[directory]);
    offset += tmp_len;

//获取 mac_algorithms_client_to_server
    if(offset + 4 > data_len)
    {
        return;
    }
    tmp_len = ntohl(*(uint32_t *)(p_data + offset));
    offset += 4;
    if(offset + tmp_len > data_len)
    {
        return;
    }
    tmp_string = string((char *)p_data + offset, tmp_len);
    splitEx(tmp_string, ",", p_ssh_message->mac_algorithms_client_to_server[directory]);
    offset += tmp_len;

//获取 mac_algorithms_server_to_client
    if(offset + 4 > data_len)
    {
        return;
    }
    tmp_len = ntohl(*(uint32_t *)(p_data + offset));
    offset += 4;
    if(offset + tmp_len > data_len)
    {
        return;
    }
    tmp_string = string((char *)p_data + offset, tmp_len);
    splitEx(tmp_string, ",", p_ssh_message->mac_algorithms_server_to_client[directory]);
    offset += tmp_len;

//获取 compression_algorithms_client_to_server
    if(offset + 4 > data_len)
    {
        return;
    }
    tmp_len = ntohl(*(uint32_t *)(p_data + offset));
    offset += 4;
    if(offset + tmp_len > data_len)
    {
        return;
    }
    tmp_string = string((char *)p_data + offset, tmp_len);
    splitEx(tmp_string, ",", p_ssh_message->compression_algorithms_client_to_server[directory]);
    offset += tmp_len;

//获取 compression_algorithms_server_to_client
    if(offset + 4 > data_len)
    {
        return;
    }
    tmp_len = ntohl(*(uint32_t *)(p_data + offset));
    offset += 4;
    if(offset + tmp_len > data_len)
    {
        return;
    }
    tmp_string = string((char *)p_data + offset, tmp_len);
    splitEx(tmp_string, ",", p_ssh_message->compression_algorithms_server_to_client[directory]);
    offset += tmp_len;

    return;
}

void ssh_plugin::kex_algorithm_init(unsigned char* p_data, uint32_t data_len, ssh_message* p_ssh_message, unsigned char directory)
{
    uint32_t offset = 0;
    uint32_t tmp_len = 0;
    int buff_len = 0;
    if(offset + 2 > data_len)
    {
        return;
    }
    unsigned char padding_length = *(p_data + offset);
    offset += 1;
    unsigned char message_code = *(p_data + offset);
    offset += 1;
    if(32 == message_code)
    {
        p_ssh_message->kex_type = 3;
    }
    if(offset + 4 > data_len)
    {
        return;
    }
    tmp_len = ntohl(*(uint32_t *)(p_data + offset));
    offset += 4;
    if(offset + tmp_len > data_len)
    {
        return;
    }
    J_HexToAscII(p_data + offset, tmp_len, uc_arr, 65535, buff_len);
    p_ssh_message->kex_init_key = string((char*)uc_arr, buff_len);
    if(0 == p_ssh_message->server)
    {
        if(0 == directory)
        {
            p_ssh_message->server = 1;
        }
        else
        {
            p_ssh_message->server = 2;
        }
    }
}

int ssh_plugin::kex_algorithm_reply_block_num(unsigned char* p_data, uint32_t data_len)
{
    uint32_t offset = 0;
    int block = 0;
    uint32_t tmp_len = 0;
    if(offset + 2 > data_len)
    {
        return 0;
    }
    unsigned char padding_length = *(p_data + offset);
    offset += 1;
    unsigned char message_code = *(p_data + offset);
    offset += 1;

    while(1)
    {
        if(offset + 4 + padding_length > data_len)
        {
            break;
        }
        tmp_len = ntohl(*(uint32_t *)(p_data + offset));
        offset += 4;
        if(offset + tmp_len + padding_length > data_len)
        {
            break;
        }
        offset += tmp_len;
        block ++;
    }
    return block;
}

void ssh_plugin::kex_algorithm_reply_host_key(unsigned char* p_data, uint32_t data_len, ssh_message* p_ssh_message)
{
    uint32_t offset = 0;
    uint32_t tmp_len = 0;
    int buff_len;

    if(offset + 4 > data_len)
    {
        return;
    }
    tmp_len = ntohl(*(uint32_t *)(p_data + offset));
    offset += 4;
    if(offset + tmp_len > data_len)
    {
        return;
    }
    p_ssh_message->host_key_type = string((char *)p_data+offset, tmp_len);
    offset += tmp_len;
    judge_host_key_type(p_ssh_message);
    switch (p_ssh_message->host_key_itype)
    {
    case 1:
    {
        if(offset + 4 > data_len)
        {
            return;
        }
        tmp_len = ntohl(*(uint32_t *)(p_data + offset));
        offset += 4;
        if(offset + tmp_len > data_len)
        {
            return;
        }
        J_HexToAscII(p_data + offset, tmp_len, uc_arr, 65535, buff_len);
        p_ssh_message->host_key_rsa_e = string((char*)uc_arr, buff_len);
        offset += tmp_len;
        if(offset + 4 > data_len)
        {
            return;
        }
        tmp_len = ntohl(*(uint32_t *)(p_data + offset));
        offset += 4;
        if(offset + tmp_len > data_len)
        {
            return;
        }
        J_HexToAscII(p_data + offset, tmp_len, uc_arr, 65535, buff_len);
        p_ssh_message->host_key_rsa_n = string((char*)uc_arr, buff_len);
        offset += tmp_len;
        break;
    }
    case 2:
    {
        if(offset + 4 > data_len)
        {
            return;
        }
        tmp_len = ntohl(*(uint32_t *)(p_data + offset));
        offset += 4;
        if(offset + tmp_len > data_len)
        {
            return;
        }
        J_HexToAscII(p_data + offset, tmp_len, uc_arr, 65535, buff_len);
        p_ssh_message->host_key_dsa_p = string((char*)uc_arr, buff_len);
        offset += tmp_len;
        if(offset + 4 > data_len)
        {
            return;
        }
        tmp_len = ntohl(*(uint32_t *)(p_data + offset));
        offset += 4;
        if(offset + tmp_len > data_len)
        {
            return;
        }
        J_HexToAscII(p_data + offset, tmp_len, uc_arr, 65535, buff_len);
        p_ssh_message->host_key_dsa_q = string((char*)uc_arr, buff_len);
        offset += tmp_len;
        if(offset + 4 > data_len)
        {
            return;
        }
        tmp_len = ntohl(*(uint32_t *)(p_data + offset));
        offset += 4;
        if(offset + tmp_len > data_len)
        {
            return;
        }
        J_HexToAscII(p_data + offset, tmp_len, uc_arr, 65535, buff_len);
        p_ssh_message->host_key_dsa_g = string((char*)uc_arr, buff_len);
        offset += tmp_len;
        if(offset + 4 > data_len)
        {
            return;
        }
        tmp_len = ntohl(*(uint32_t *)(p_data + offset));
        offset += 4;
        if(offset + tmp_len > data_len)
        {
            return;
        }
        J_HexToAscII(p_data + offset, tmp_len, uc_arr, 65535, buff_len);
        p_ssh_message->host_key_dsa_y = string((char*)uc_arr, buff_len);
        offset += tmp_len;
        break;
    }
    case 3:
    {
        if(offset + 4 > data_len)
        {
            return;
        }
        tmp_len = ntohl(*(uint32_t *)(p_data + offset));
        offset += 4;
        if(offset + tmp_len > data_len)
        {
            return;
        }
        p_ssh_message->host_key_ecdsa_id = string((char *)p_data + offset, tmp_len);
        offset += tmp_len;
        if(offset + 4 > data_len)
        {
            return;
        }
        tmp_len = ntohl(*(uint32_t *)(p_data + offset));
        offset += 4;
        if(offset + tmp_len > data_len)
        {
            return;
        }
        J_HexToAscII(p_data + offset, tmp_len, uc_arr, 65535, buff_len);
        p_ssh_message->host_key_ecdsa_q = string((char*)uc_arr, buff_len);
        offset += tmp_len;
        break;
    }
    case 4:
    {
        if(offset + 4 > data_len)
        {
            return;
        }
        tmp_len = ntohl(*(uint32_t *)(p_data + offset));
        offset += 4;
        if(offset + tmp_len > data_len)
        {
            return;
        }
        J_HexToAscII(p_data + offset, tmp_len, uc_arr, 65535, buff_len);
        p_ssh_message->host_key_eddsa_key = string((char*)uc_arr, buff_len);
        offset += tmp_len;
        break;
    }
    default:
        break;
    }
    return;
}

void ssh_plugin::kex_algorithm_reply(unsigned char* p_data, uint32_t data_len, ssh_message* p_ssh_message, unsigned char directory)
{
    uint32_t offset = 0;
    uint32_t tmp_len = 0;
    int buff_len;
    int block_num;

    block_num = kex_algorithm_reply_block_num(p_data, data_len);

    if(offset + 2 > data_len)
    {
        return;
    }
    unsigned char padding_length = *(p_data + offset);
    offset += 1;
    unsigned char message_code = *(p_data + offset);
    offset += 1;

    if(33 == message_code)
    {
        p_ssh_message->kex_type = 3;
    }
    if(0 == p_ssh_message->kex_type && 31 == message_code && 2 == block_num)    //guess
    {
        p_ssh_message->kex_type = 3;
    }

    if(31 == message_code && 3 == p_ssh_message->kex_type && 2 <= block_num)        //KEX_DH_GEX_GROUP
    {
        tmp_len = ntohl(*(uint32_t *)(p_data + offset));
        offset += 4;
        J_HexToAscII(p_data + offset, tmp_len, uc_arr, 65535, buff_len);
        p_ssh_message->dh_gex_p = string((char *)uc_arr, buff_len);
        offset += tmp_len;
        tmp_len = ntohl(*(uint32_t *)(p_data + offset));
        offset += 4;
        J_HexToAscII(p_data + offset, tmp_len, uc_arr, 65535, buff_len);
        p_ssh_message->dh_gex_g = string((char *)uc_arr, buff_len);
        offset += tmp_len;
    }
    else if(3 == block_num)     //KEXDH_REPLY || KEX_ECDH_REPLY || KEX_DH_GEX_REPLY
    {
        tmp_len = ntohl(*(uint32_t *)(p_data + offset));
        offset += 4;
        kex_algorithm_reply_host_key(p_data + offset, tmp_len, p_ssh_message);
        offset += tmp_len;
        tmp_len = ntohl(*(uint32_t *)(p_data + offset));
        offset += 4;
        J_HexToAscII(p_data + offset, tmp_len, uc_arr, 65535, buff_len);
        p_ssh_message->kex_reply_key = string((char *)uc_arr, buff_len);
        offset += tmp_len;
        tmp_len = ntohl(*(uint32_t *)(p_data + offset));
        offset += 4;
        J_HexToAscII(p_data + offset, tmp_len, uc_arr, 65535, buff_len);
        p_ssh_message->kex_h_sig = string((char *)uc_arr, buff_len);
        offset += tmp_len;
        if(p_ssh_message->host_key_itype > 0 && p_ssh_message->host_key_itype < 5 && 0 == p_ssh_message->server)
        {
            if(0 == directory)
            {
                p_ssh_message->server = 2;
            }
            else
            {
                p_ssh_message->server = 1;
            }
        }
    }
    return;
}

void ssh_plugin::kex_dh_gex_request(unsigned char* p_data, uint32_t data_len, ssh_message* p_ssh_message, unsigned char directory)
{
    uint32_t offset = 0;
    uint32_t tmp_len = 0;
    int buff_len = 0;
    if(offset + 2 > data_len)
    {
        return;
    }
    unsigned char padding_length = *(p_data + offset);
    offset += 1;
    unsigned char message_code = *(p_data + offset);
    offset += 1;
    if(34 == message_code)
    {
        p_ssh_message->kex_type = 3;
    }
    if(offset + 4 > data_len)
    {
        return;
    }
    p_ssh_message->dh_gex_min = std::to_string(ntohl(*(uint32_t *)(p_data + offset)));
    offset += 4;
    if(offset + 4 > data_len)
    {
        return;
    }
    p_ssh_message->dh_gex_nbits = std::to_string(ntohl(*(uint32_t *)(p_data + offset)));
    offset += 4;
    if(offset + 4 > data_len)
    {
        return;
    }
    p_ssh_message->dh_gex_max = std::to_string(ntohl(*(uint32_t *)(p_data + offset)));
    offset += 4;
    return;
}

bool ssh_plugin::protocol_parse(unsigned char* p_data, uint32_t data_len, ssh_message* p_ssh_message, unsigned char directory)
{
    string str_data((char *)p_data, data_len);
    string::size_type nPos = str_data.find("\r\n");
    if (nPos == string::npos)
    {
        string::size_type nPos1 = str_data.find("\n");
        if (nPos1 == string::npos)
        {
            return false;
        }
        else
        {
            str_data.erase(nPos1, str_data.size());
        }
    }
    else
    {
        str_data.erase(nPos, str_data.size());
    }
    p_ssh_message->protocol[directory] = str_data;
}

void ssh_plugin::judge_kex_type(ssh_message* p_ssh_message)
{
    int find = 0;
    for(int i = 0; i < p_ssh_message->kex_algorithms[0].size(); i ++)
    {
        for(int j = 0; j < p_ssh_message->kex_algorithms[1].size(); j ++)
        {
            if(p_ssh_message->kex_algorithms[0][i] == p_ssh_message->kex_algorithms[1][j])
            {
                find = 1;
                if((p_ssh_message->kex_algorithms[0][i] == "diffie-hellman-group-exchange-sha1")
                        || (p_ssh_message->kex_algorithms[0][i] == "diffie-hellman-group-exchange-sha256"))
                {
                    p_ssh_message->kex_type = 3;
                }
                else if(0 == (p_ssh_message->kex_algorithms[0][i].find("ecdh-sha2-"))
                        || (p_ssh_message->kex_algorithms[0][i] == "<EMAIL>")
                        || (p_ssh_message->kex_algorithms[0][i] == "curve25519-sha256")
                        || (p_ssh_message->kex_algorithms[0][i] == "curve448-sha512"))
                {
                    p_ssh_message->kex_type = 2;
                }
                else
                {
                    p_ssh_message->kex_type = 1;
                }
                break;
            }
            if(find)
            {
                break;
            }
        }
    }
}

void ssh_plugin::judge_host_key_type(ssh_message* p_ssh_message)
{
    if(p_ssh_message->host_key_type == "ssh-rsa")
    {
        p_ssh_message->host_key_itype = 1;
    }
    else if(p_ssh_message->host_key_type == "ssh-dss")
    {
        p_ssh_message->host_key_itype = 2;
    }
    else if(0 == (p_ssh_message->host_key_type.find("ecdsa-sha2-")))
    {
        p_ssh_message->host_key_itype = 3;
    }
    else if(0 == (p_ssh_message->host_key_type.find("ssh-ed")))
    {
        p_ssh_message->host_key_itype = 4;
    }
}

bool ssh_plugin::level_1_data_parse(unsigned char* p_data, uint32_t data_len, ssh_session* p_ssh_session, unsigned char directory)
{
    if (p_data == NULL || data_len == 0)
    {
        return false;
    }
    
    ssh_message* p_ssh_message = p_ssh_session->p_ssh_message;
    if (data_len > 4 && strncmp((const char *)p_data,"SSH-",4) == 0)//版本协商包
    {
        protocol_parse(p_data, data_len, p_ssh_message, directory);
        return false;
    }
    else 
    {
        uint32_t offset = 0;
        for (;offset + 6 <= data_len;)
        {
            uint32_t packet_length = ntohl(*(uint32_t *)(p_data+offset));
            uint32_t block_length = packet_length;
            offset += 4;
            unsigned char padding_length = *(p_data+offset);
            unsigned char message_code = *(p_data+offset+1);

            //获取本块数据
            if(packet_length > 0x7fffffff)
            {
                break;
            }
            if(offset + packet_length > data_len)
            {
                if(packet_length > 0xffff || message_code >= 128)
                {
                    //enc msg
                    block_length = 0;
                }
                else
                {
                    block_length = data_len - offset;
                }
            }
            if(0 == block_length)
            {
                p_ssh_session->b_parse_over[directory] = true;
                break;
            }
            switch (message_code)
            {
                case 20: //KEXINIT
                {
                    key_exchange_init(p_data+offset, block_length, p_ssh_message, directory);
                    if(p_ssh_message->kex_algorithms[0].size() && p_ssh_message->kex_algorithms[1].size())
                    {
                        judge_kex_type(p_ssh_message);
                    }
                    break;
                }
                case 21: //new keys 检测到此值发送数据，之后为加密传输，不再解析
                {
                    p_ssh_session->b_parse_over[directory] = true;
                    return true;
                    break;
                }
                case 30:    //KEXDH_INIT || KEX_ECDH_INIT || KEX_DH_GEX_REQUEST_OLD
                case 32:    //KEX_DH_GEX_INIT
                {
                    kex_algorithm_init(p_data+offset, block_length, p_ssh_message, directory);
                    break;
                }
                case 31:    //KEXDH_REPLY || KEX_ECDH_REPLY || KEX_DH_GEX_GROUP
                case 33:    //KEX_DH_GEX_REPLY
                {
                    kex_algorithm_reply(p_data+offset, block_length, p_ssh_message, directory);
                    break;
                }
                case 34:    //KEX_DH_GEX_REQUEST
                {
                    kex_dh_gex_request(p_data+offset, block_length, p_ssh_message, directory);
                    break;
                }
            }
            offset += packet_length;
        }
        return false;
    }
    return false;
}

bool ssh_plugin::potocol_parse_handle(session_pub * p_session,c_packet *p_packet) //解析
{
    uint32_t seq_tmp;
    if (p_session == NULL)
    {
        return false;
    }
    proto_parse_session* p_pp_session = (proto_parse_session * )( p_session->p_sp_session);
    ssh_session * p_ssh_session = (ssh_session *)p_pp_session->expansion_data;
    if(p_packet)
    {
        //如果解析数据之后发现可以发送了，就返回true
        level_1_data_parse(p_ssh_session->p_data, p_ssh_session->data_len, p_ssh_session, p_packet->Directory);//解析完数据之后要将组包buff清理出来
        if(p_ssh_session->b_parse_over[p_packet->Directory])
        {
            if(p_session->p_session_ext && p_session->p_session_ext->tcp_repcom[p_packet->Directory])
            {
                p_session->p_tcp_recom_push_cb(p_session, p_packet->Directory);
                p_ssh_session->b_cacheing[p_packet->Directory] = 0;
            }
        }
        if(p_ssh_session->b_parse_over[0] && p_ssh_session->b_parse_over[1])
        {
            return true;
        }
        return false;
    }
    else
    {
        if(p_session->p_session_ext && p_session->p_session_ext->tcp_repcom[0])
        {
            p_session->p_session_ext->tcp_repcom[0]->front_fragment(seq_tmp, p_ssh_session->data_len, (char *&)p_ssh_session->p_data);
            level_1_data_parse(p_ssh_session->p_data, p_ssh_session->data_len, p_ssh_session, 0);
            p_session->p_tcp_recom_push_cb(p_session, 0);
            p_ssh_session->b_cacheing[0] = 0;
        }
        if(p_session->p_session_ext && p_session->p_session_ext->tcp_repcom[1])
        {
            p_session->p_session_ext->tcp_repcom[1]->front_fragment(seq_tmp, p_ssh_session->data_len, (char *&)p_ssh_session->p_data);
            level_1_data_parse(p_ssh_session->p_data, p_ssh_session->data_len, p_ssh_session, 1);
            p_session->p_tcp_recom_push_cb(p_session, 1);
            p_ssh_session->b_cacheing[1] = 0;
        }
        return false;
    }
}

void ssh_plugin::ssh_msg_send(session_pub* p_session)  //发送
{
    proto_parse_session* p_pp_session = (proto_parse_session * )( p_session -> p_sp_session);
    ssh_session * p_ssh_session = (ssh_session *)p_pp_session->expansion_data;
    ssh_message* p = p_ssh_session->p_ssh_message;
    unsigned char client_id = 0, server_id = 1;
    if (p_ssh_session->b_send_over)
    {
        return;
    }
    if (0 == p->kex_algorithms[0].size() && 0 == p->kex_algorithms[1].size()) {
        p_ssh_session->b_send_over = 1;
        if (p_ssh_session->p_ssh_message != NULL)
        {
            delete p_ssh_session->p_ssh_message;
            p_ssh_session->p_ssh_message = NULL;
        }
        return;
    }
    if(should_log)
    {
        JKNmsg * p_msg = p_session->p_value->get();
        if (p_msg == NULL)
        {
            p_ssh_session->b_send_over = 1;
            if (p_ssh_session->p_ssh_message != NULL)
            {
                delete p_ssh_session->p_ssh_message;
                p_ssh_session->p_ssh_message = NULL;
            }
            return;
        }
        p_msg->set_type(28);
        ssh_msg* p_ssh = p_msg->mutable_ssh();
        Comm_msg* p_comm = p_ssh->mutable_comm_msg();
        ssh_kex_msg *p_client = p_ssh->mutable_client();
        ssh_kex_msg *p_server = p_ssh->mutable_server();
        //公共信息部分
        commsg_fill(p_session , p_comm, "10061", p_th_tools);

        ////ssh msg
        if(0 == p->server)
        {
            p->server = p_session->session_basic.Server;
        }
        if(0 == p->server)
        {
            p->server = 1;
        }
        if(1 == p->server)
        {
            client_id = 0;
            server_id = 1;
        }
        else
        {
            client_id = 1;
            server_id = 0;
        }
        p_client->set_protocol(p->protocol[client_id]);
        p_server->set_protocol(p->protocol[server_id]);
        p_client->set_cookie(p->cookie[client_id]);
        p_server->set_cookie(p->cookie[server_id]);
        for(int i = 0; i < p->kex_algorithms[client_id].size(); i ++)
        {
            p_client->add_kex_algorithms(p->kex_algorithms[client_id][i]);
        }
        for(int i = 0; i < p->kex_algorithms[server_id].size(); i ++)
        {
            p_server->add_kex_algorithms(p->kex_algorithms[server_id][i]);
        }
        for(int i = 0; i < p->server_host_key_algorithms[client_id].size(); i ++)
        {
            p_client->add_server_host_key_algorithms(p->server_host_key_algorithms[client_id][i]);
        }
        for(int i = 0; i < p->server_host_key_algorithms[server_id].size(); i ++)
        {
            p_server->add_server_host_key_algorithms(p->server_host_key_algorithms[server_id][i]);
        }
        for(int i = 0; i < p->encryption_algorithms_client_to_server[client_id].size(); i ++)
        {
            p_client->add_encryption_algorithms_client_to_server(p->encryption_algorithms_client_to_server[client_id][i]);
        }
        for(int i = 0; i < p->encryption_algorithms_client_to_server[server_id].size(); i ++)
        {
            p_server->add_encryption_algorithms_client_to_server(p->encryption_algorithms_client_to_server[server_id][i]);
        }
        for(int i = 0; i < p->encryption_algorithms_server_to_client[client_id].size(); i ++)
        {
            p_client->add_encryption_algorithms_server_to_client(p->encryption_algorithms_server_to_client[client_id][i]);
        }
        for(int i = 0; i < p->encryption_algorithms_server_to_client[server_id].size(); i ++)
        {
            p_server->add_encryption_algorithms_server_to_client(p->encryption_algorithms_server_to_client[server_id][i]);
        }
        for(int i = 0; i < p->mac_algorithms_client_to_server[client_id].size(); i ++)
        {
            p_client->add_mac_algorithms_client_to_server(p->mac_algorithms_client_to_server[client_id][i]);
        }
        for(int i = 0; i < p->mac_algorithms_client_to_server[server_id].size(); i ++)
        {
            p_server->add_mac_algorithms_client_to_server(p->mac_algorithms_client_to_server[server_id][i]);
        }
        for(int i = 0; i < p->mac_algorithms_server_to_client[client_id].size(); i ++)
        {
            p_client->add_mac_algorithms_server_to_client(p->mac_algorithms_server_to_client[client_id][i]);
        }
        for(int i = 0; i < p->mac_algorithms_server_to_client[server_id].size(); i ++)
        {
            p_server->add_mac_algorithms_server_to_client(p->mac_algorithms_server_to_client[server_id][i]);
        }
        for(int i = 0; i < p->compression_algorithms_client_to_server[client_id].size(); i ++)
        {
            p_client->add_compression_algorithms_client_to_server(p->compression_algorithms_client_to_server[client_id][i]);
        }
        for(int i = 0; i < p->compression_algorithms_client_to_server[server_id].size(); i ++)
        {
            p_server->add_compression_algorithms_client_to_server(p->compression_algorithms_client_to_server[server_id][i]);
        }
        for(int i = 0; i < p->compression_algorithms_server_to_client[client_id].size(); i ++)
        {
            p_client->add_compression_algorithms_server_to_client(p->compression_algorithms_server_to_client[client_id][i]);
        }
        for(int i = 0; i < p->compression_algorithms_server_to_client[server_id].size(); i ++)
        {
            p_server->add_compression_algorithms_server_to_client(p->compression_algorithms_server_to_client[server_id][i]);
        }
        switch(p->kex_type)
        {
            case 1:
            {
                p_ssh->set_dh_e(p->kex_init_key);
                p_ssh->set_dh_f(p->kex_reply_key);
                break;
            }
            case 2:
            {
                p_ssh->set_ecdh_q_c(p->kex_init_key);
                p_ssh->set_ecdh_q_s(p->kex_reply_key);
                break;
            }
            case 3:
            {
                p_ssh->set_dh_e(p->kex_init_key);
                p_ssh->set_dh_f(p->kex_reply_key);
                p_ssh->set_dh_gex_min(p->dh_gex_min);
                p_ssh->set_dh_gex_nbits(p->dh_gex_nbits);
                p_ssh->set_dh_gex_max(p->dh_gex_max);
                p_ssh->set_dh_gex_p(p->dh_gex_p);
                p_ssh->set_dh_gex_g(p->dh_gex_g);
                break;
            }
            default:
            {
                break;
            }
        }
        p_ssh->set_host_key_type(p->host_key_type);
        switch(p->host_key_itype)
        {
            case 1:
            {
                p_ssh->set_host_key_rsa_e(p->host_key_rsa_e);
                p_ssh->set_host_key_rsa_n(p->host_key_rsa_n);
                break;
            }
            case 2:
            {
                p_ssh->set_host_key_dsa_p(p->host_key_dsa_p);
                p_ssh->set_host_key_dsa_q(p->host_key_dsa_q);
                p_ssh->set_host_key_dsa_g(p->host_key_dsa_g);
                p_ssh->set_host_key_dsa_y(p->host_key_dsa_y);
                break;
            }
            case 3:
            {
                p_ssh->set_host_key_ecdsa_id(p->host_key_ecdsa_id);
                p_ssh->set_host_key_ecdsa_q(p->host_key_ecdsa_q);
                break;
            }
            case 4:
            {
                p_ssh->set_host_key_eddsa_key(p->host_key_eddsa_key);
                break;
            }
            default:
            {
                break;
            }
        }
        p_ssh->set_kex_h_sig(p->kex_h_sig);
    }
    p_ssh_session->b_send_over = 1;
    if (p_ssh_session->p_ssh_message != NULL)
    {
        delete p_ssh_session->p_ssh_message;
        p_ssh_session->p_ssh_message = NULL;
    }
    return;
}

void ssh_plugin::potocol_data_handle(session_pub* p_session,c_packet * p_packet)  //发送
{
    if(p_session==NULL)
    {
        return;
    }
    
    proto_parse_session* p_pp_session = (proto_parse_session * )( p_session -> p_sp_session);
    ssh_session * p_ssh_session = (ssh_session *)p_pp_session->expansion_data;
    if(p_ssh_session->b_parse_over[0] && p_ssh_session->b_parse_over[1])
    {
        ssh_msg_send(p_session);
    }
}

bool ssh_plugin::time_out(session_pub * p_session, uint32_t check_time)
{
    if (p_session)
    {
        return true;
    }
    return true;
}

void ssh_plugin::resources_recovery(session_pub * p_session)
{
    proto_parse_session *p_pp_session = (proto_parse_session*)(p_session->p_sp_session);
    ssh_session * p_ssh_session = (ssh_session *)p_pp_session->expansion_data;

    if(p_ssh_session->b_cacheing[0] || p_ssh_session->b_cacheing[1])
    {
        p_pp_session -> p_handle -> potocol_parse_handle(p_session ,NULL);
        p_pp_session -> p_handle -> potocol_data_handle( p_session  , NULL );
    }
    ssh_msg_send(p_session);//超时释放session时，调一次发送
    if (p_ssh_session->p_ssh_message != NULL)
    {
        delete p_ssh_session->p_ssh_message;
        p_ssh_session->p_ssh_message = NULL;
    }
    return;
}

// Last Update:2019-03-05 10:06:00
/**
 * @file ssh_str.h
 * @brief 
 * <AUTHOR>
 * @version 0.0.00
 * @date 2019-04-22
 */

#ifndef NSSH_PLUGIN_H
#define NSSH_PLUGIN_H
#include <stdio.h>
#include <iomanip>
#include <sstream>
#include <xml_parse.h>
#include <c_ip.h>
#include <map>
#include <TH_engine_interface.h>
#include <proto_parse_session.h>
#include "ssh_str.h"

using namespace std;

extern "C" {
    int get_plugin_id();
    session_pasre_base * attach();
};

#define PROTOCOL_SSH                    580

class ssh_plugin : public session_pasre_base{
    public:
        ssh_plugin();
        ~ssh_plugin();
        virtual void reload();
        virtual bool potocol_init(session_pub* p_sess, c_packet* p_pack);
        virtual bool potocol_sign_judge(session_pub* p_sess, c_packet* p_pack);
        virtual bool potocol_parse_handle(session_pub* p_sess,c_packet * p_packet);
        virtual void potocol_data_handle(session_pub* p_sess,c_packet * p_packet);
        virtual bool time_out(session_pub* p_sess,uint32_t check_time);
        virtual void resources_recovery(session_pub* p_sess);
    private:
        void J_HexToAscII(unsigned char *IN_pHexBuffer,int IN_HexBufferLen,unsigned char *OUT_pASCIIBuffer,int IN_HexBufSize,int &OUT_ASCIIBufferLen );
        void init_ssh_session(ssh_session* p_ssh_session);
        bool data_is_whole(ssh_session* p_ssh_session);
        bool pkt_is_whole(c_packet * p_packet);
        bool level_1_data_parse(unsigned char* p_data, uint32_t data_len, ssh_session* p_ssh_session, unsigned char directory);
        void key_exchange_init(unsigned char* p_data, uint32_t data_len, ssh_message* p_ssh_message, unsigned char directory);
        void kex_algorithm_init(unsigned char* p_data, uint32_t data_len, ssh_message* p_ssh_message, unsigned char directory);
        void kex_algorithm_reply_host_key(unsigned char* p_data, uint32_t data_len, ssh_message* p_ssh_message);
        int kex_algorithm_reply_block_num(unsigned char* p_data, uint32_t data_len);
        void kex_algorithm_reply(unsigned char* p_data, uint32_t data_len, ssh_message* p_ssh_message, unsigned char directory);
        void kex_dh_gex_request(unsigned char* p_data, uint32_t data_len, ssh_message* p_ssh_message, unsigned char directory);
        void judge_kex_type(ssh_message* p_ssh_message);
        void judge_host_key_type(ssh_message* p_ssh_message);
        bool protocol_parse(unsigned char* p_data, uint32_t data_len, ssh_message* p_ssh_message, unsigned char directory);
        void ssh_msg_send(session_pub* p_session);
    private:
        unsigned char uc_arr[65535];
};
#endif  /*NSSH_PLUGIN_H*/

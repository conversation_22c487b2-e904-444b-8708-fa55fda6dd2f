// Last Update:2019-03-05 10:06:00
/**
 * @file ssh_str.h
 * @brief 
 * <AUTHOR>
 * @version 0.0.00
 * @date 2019-04-22
 */

#ifndef NSSH_STR_H
#define NSSH_STR_H

#include <session_pub.h>
#include <packet.h>
#include <stdint.h>
#include <string> 
#include <list> 


using namespace std;

class ssh_message
{
public:
    ssh_message()
    {
        kex_type = 0;
        host_key_itype = 0;
        server = 0;
    }
    string protocol[2];
    string cookie[2];
    vector<string> kex_algorithms[2];
    vector<string> server_host_key_algorithms[2];
    vector<string> encryption_algorithms_client_to_server[2];
    vector<string> encryption_algorithms_server_to_client[2];
    vector<string> mac_algorithms_client_to_server[2];
    vector<string> mac_algorithms_server_to_client[2];
    vector<string> compression_algorithms_client_to_server[2];
    vector<string> compression_algorithms_server_to_client[2];

    string kex_init_key;
    string kex_reply_key;
    string kex_h_sig;

    string dh_gex_min;
    string dh_gex_nbits;
    string dh_gex_max;
    string dh_gex_p;
    string dh_gex_g;

    string host_key_type;
    string host_key_rsa_e;
    string host_key_rsa_n;
    string host_key_ecdsa_id;
    string host_key_ecdsa_q;
    string host_key_dsa_p;
    string host_key_dsa_q;
    string host_key_dsa_g;
    string host_key_dsa_y;
    string host_key_eddsa_key;

    unsigned char kex_type;   //0:unknown 1:dh 2:ecdh 3:dh_gex
    unsigned char host_key_itype;   //0:unknown 1:rsa 2:dsa 3:ecdsa 4:eddsa
    unsigned char server;   //0:unknown 1:c2s 2:s2c
};

class ssh_session
{
public:
    ssh_message* p_ssh_message;
    unsigned char* p_data;
    uint32_t data_len;
    uint32_t thread_id;
    uint16_t ofo_pkt_num[2];
    uint8_t b_parse_over[2];
    uint8_t b_cacheing[2];
    uint8_t b_send_over;
};
#endif  /*NSSH_STR_H*/

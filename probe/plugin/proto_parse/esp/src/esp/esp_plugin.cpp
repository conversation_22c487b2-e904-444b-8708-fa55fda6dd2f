// Last Update:2018-11-26 10:06:00
/**
 * @file esp_str.h
 * @brief 
 * <AUTHOR>
 * @version 0.0.00
 * @date 2018-11-26
 */
 

#include <commit_tools.h>
#include <sys/stat.h>
#include <sys/time.h>
#include "esp_plugin.h"

extern "C" {
    int get_plugin_id()
    {
        return 50;
    }
    session_pasre_base * attach( )
    {
        return new  esp_plugin();
    }
}

esp_plugin::esp_plugin()
{
    reload();
}
esp_plugin::~esp_plugin()
{
    ;
}
void esp_plugin::reload()
{
    xml_parse  xml;
    string conf_path = string(getenv("THE_CONF_PATH")) + "/esp_plugin.xml";
    xml.set_file_path(conf_path.c_str());
    char * p_value = (char *)xml.get_value("/config/send_data_type");
    string tmp = "";
    if(p_value != NULL)
    {
        tmp = p_value;
    }
    p_value = (char *)xml.get_value("/config/time_out");
    if(p_value != NULL)
    {
        //esp_time_out = atoi(p_value);
    }
}

void esp_plugin::init_esp_session(esp_session * p_esp_session)
{
    p_esp_session -> esp_spi = 0;
    p_esp_session -> esp_seq = 0;
    p_esp_session -> esp_data_str = "";
    p_esp_session -> esp_data_len = 0;
}

bool  esp_plugin::potocol_init(session_pub * p_session, c_packet * p_packet)
{
    if(p_session==NULL || p_packet==NULL)
    {
        return false;
    }
    proto_parse_session *p_pp_session = (proto_parse_session * )( p_session -> p_sp_session) ;
    esp_session * p_esp_session = (esp_session *)p_pp_session->expansion_data;
    init_esp_session(p_esp_session);
    return true ;
}

bool esp_plugin:: potocol_sign_judge(session_pub * p_session,c_packet * p_packet)
{
    if(p_session==NULL || p_packet==NULL)
    {
        return false;
    }
    //proto_parse_session *p_pp_session = (proto_parse_session * )( p_session -> p_sp_session) ;
    //esp_session * p_esp_session = (esp_session *)p_pp_session->expansion_data;
    if(p_packet->app_data_len == 1)//排除 NAT-keepalive packet 这种数据包
    {
        return false;
    }
    return true;
}

string esp_plugin::char2str(const char* s, uint32_t len)
{
    if(NULL == s || len > 1472)//1500(以太网MTU) - 20(IP首部) - 4(SPI) - 4(SEQ)
    {
        return "";
    }
    string raw(s, len);// n byte
    char buf[len * 2];
    for(unsigned int i=0; i<len; i++)
    {
        sprintf(buf+i*2, "%02x", (unsigned char)raw[i]);
    }
    string str(&buf[0], sizeof(buf));
    return str;
}

void esp_plugin::_protocol_parse(esp_session * p_esp_session, uint8_t* raw, uint32_t raw_len, uint32_t offset)
{

    uint32_t esp_len =  raw_len - offset;//[包长-(以太网协议头长+IP协议头长)]-AH协议头长度
    p_esp_session->esp_spi = ntohl(*(uint32_t*)(raw + offset));// 4 byte
    if(p_esp_session->esp_spi != 0)
    {
        p_esp_session->esp_seq = ntohl(*(uint32_t*)(raw + offset + 4));// 4 byte
        const char* s = (char*)(raw + offset + 4 + 4);
        p_esp_session->esp_data_len = esp_len - 4 - 4;
        p_esp_session->esp_data_str = char2str(s, p_esp_session->esp_data_len);
    }
    else
    {
        p_esp_session->esp_seq = 0;
        p_esp_session->esp_data_len = 0;
        p_esp_session->esp_data_str = "";
    }
}

bool esp_plugin::potocol_parse_handle(session_pub * p_session,c_packet *p_packet)
{
    if(p_session == NULL)
    {
        return false;
    }
    if(p_packet == NULL) 
    {
        return false;
    }

    proto_parse_session *p_pp_session = (proto_parse_session * )( p_session -> p_sp_session) ;
    esp_session * p_esp_session = (esp_session *)p_pp_session->expansion_data;
    if(p_esp_session==NULL)
    {
        return false;
    }
    _protocol_parse(p_esp_session, p_packet->p_app_data, p_packet->app_data_len, 0);
    return true;
}

void esp_plugin::potocol_data_handle(session_pub* p_session,c_packet * p_packet)
{
    if(p_session == NULL)
    {
        return;
    }
    proto_parse_session *p_pp_session = (proto_parse_session * )( p_session -> p_sp_session) ;
    esp_session * p_esp_session = (esp_session *)p_pp_session->expansion_data;

    if(p_esp_session == NULL)
    {
        return;
    }
    if(should_log)
    {
        JKNmsg *  msg = p_session -> p_value -> get() ;
        msg ->set_type(61); 
        esp_msg * p_esp_msg = msg->mutable_esp();
        Comm_msg* p_comm = p_esp_msg->mutable_comm_msg();
        // 公共 消息 
        commsg_fill(p_session , p_comm , "1500001", p_th_tools);
        p_esp_msg->set_esp_spi(p_esp_session->esp_spi);
        p_esp_msg->set_esp_seq(p_esp_session->esp_seq);
        p_esp_msg->set_esp_data_len(p_esp_session->esp_data_len);
        p_esp_msg->set_esp_data(p_esp_session->esp_data_str);
    }
}


bool  esp_plugin::time_out(session_pub * p_session, uint32_t check_time)
{
    if(p_session)
    {
        return true;
    }
    return true;
}

void esp_plugin::resources_recovery(session_pub * p_session)
{
    return;
}


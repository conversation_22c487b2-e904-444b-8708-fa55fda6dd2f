// Last Update:2018-11-26 10:06:00
/**
 * @file esp_str.h
 * @brief 
 * <AUTHOR>
 * @version 0.0.00
 * @date 2018-11-26
 */

#ifndef ESP_PLUGIN_H
#define ESP_PLUGIN_H
#include <stdio.h>
#include <iomanip>
#include <sstream>
#include <xml_parse.h>
#include <c_ip.h>
#include <map>
#include <TH_engine_interface.h>
#include <proto_parse_session.h>
#include "esp_str.h"

using namespace std;

extern "C" {
    int get_plugin_id();
    session_pasre_base * attach();
};

#include "dnumstr.h"

class esp_plugin :public session_pasre_base{
    public:
        esp_plugin();
        ~esp_plugin();
        virtual void reload();
        virtual bool potocol_init(session_pub* p_sess, c_packet* p_pack);
        virtual bool potocol_sign_judge(session_pub* p_sess, c_packet* p_pack);
        virtual bool potocol_parse_handle(session_pub* p_sess,c_packet * p_packet);
        virtual void potocol_data_handle(session_pub* p_sess,c_packet * p_packet);
        virtual bool time_out(session_pub* p_sess,uint32_t check_time);
        virtual void resources_recovery(session_pub* p_sess);
    private:
        string char2str(const char* s, uint32_t len);
        void _protocol_parse(esp_session * p_esp_session, uint8_t* raw, uint32_t raw_len, uint32_t offset);
        void init_esp_session(esp_session * p_esp_session);
};
#endif  /*dns_PLUGIN_H*/

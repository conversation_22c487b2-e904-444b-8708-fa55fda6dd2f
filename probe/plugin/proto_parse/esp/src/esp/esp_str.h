// Last Update:2018-11-26 10:06:00
/**
 * @file esp_str.h
 * @brief 
 * <AUTHOR>
 * @version 0.0.00
 * @date 2018-11-26
 */

#ifndef ESP_STR_H
#define ESP_STR_H

#include <session_pub.h>
#include <stdint.h>
#include <string> 
#include <list> 


using namespace std;

class esp_session
{
    public:
        uint32_t esp_spi;
        uint32_t esp_seq;
        string esp_data_str;
        uint32_t esp_data_len;
};
#endif  /*ESP_STR_H*/

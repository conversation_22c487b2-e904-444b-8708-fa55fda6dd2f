#ifndef __TLS_FINGER_BROWSER_H__
#define __TLS_FINGER_BROWSER_H__

#include <sstream>
#include <fstream>
#include "json/reader.h"
#include "json/value.h"
#include "json/writer.h"
#include "DataStructure/TemplateMatch.h"
using namespace std;

class tls_finger_map
{
public:
    static tls_finger_map *getInstance()
    {
        static tls_finger_map s_instance;
        return &s_instance;
    }
    uint32_t judge(uint64_t finger)
    {
        return finger_map.JudgeValue((uint32_t)finger, finger);
    }

private:
    tls_finger_map() {
        DWORD IN_HashSize = GetPrimeNum(10000 >> 2, 10000);
        IN_HashSize = J_max(IN_HashSize, 1);

        finger_array.Init( 10000 + 1);
        finger_map.Init( IN_HashSize,10000,&finger_array);

        string str;
        ifstream fin;
        string db_path = string(getenv("THE_DB_PATH")) + "/tls_finger_browser.json";
        fin.open(db_path, ios::in);
        stringstream buf;
        buf << fin.rdbuf(); 
        str = buf.str();
        cout << str << endl;
        fin.close();

        Json::Reader reader;
        Json::Value rule_json;
        uint32_t finger_num;
        uint64_t finger;
        DWORD IsAdd=0;

        if (false != reader.parse(str, rule_json))
        {
            if(rule_json.isNull() == false)
            {
                finger_num = rule_json.size();
                for(uint32_t i = 0; i < finger_num; i ++)
                {
                    if(rule_json[i]["finger"].isNull() == false)
                    {
                        string finger_str = rule_json[i]["finger"].asString();
                        sscanf(finger_str.c_str(), "%"PRIu64, &finger);
                        finger_map.JudgeAndAdd((uint32_t)finger, finger, IsAdd);
                    }
                }
            }
        }
    }
    CTemplateMatch<uint64_t>    finger_map;
    CArrayBasic<uint64_t>       finger_array;
};

#endif
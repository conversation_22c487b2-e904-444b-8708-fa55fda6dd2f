// Last Update:2019-06-01 10:33:44
/**
 * @file ssl_str.h
 * @brief 
 * <AUTHOR>
 * @version 0.0.00
 * @date 2019-03-05
 */

#ifndef NSS_STR_H
#define NSS_STR_H

#include <session_pub.h>
#include <packet.h>
#include <stdint.h>
#include <string> 
#include <list>
#include "json/value.h"


using namespace std;

class ssl_message
{
public:
    ssl_message()
    {
        ssl_version = 0;
        ssl_c_version = 0;
        ssl_hello_c_version = 0;
        ssl_hello_c_time = 0;
        ssl_hello_c_random = "";
        ssl_hello_c_sessionid = "";
        ssl_hello_c_sessionidlen = 0;
        ssl_hello_c_ciphersuit = "";
        ssl_hello_c_ciphersuitnum = 0;
        ssl_hello_c_compressionmethod = "";
        ssl_hello_c_compressionmethodlen = 0;
        ssl_hello_c_extentionnum = 0;
        ssl_hello_c_extention = "";
        ssl_hello_c_alpn = "";
        j_ssl_hello_c_alpn.clear();
        ssl_hello_c_servername = "";
        ssl_hello_c_servernametype = 0;
        ssl_hello_c_sessionticket = "";
        
        ssl_cert_num[0] = 0;
        ssl_cert_num[1] = 0;
        ssl_cert_hash[0] = "";
        ssl_cert_hash[1] = "";
        j_ssl_cert_hash[0].clear();
        j_ssl_cert_hash[1].clear();
        ssl_cert_raw[0].clear();
        ssl_cert_raw[1].clear();
        
        ssl_hello_s_version = 0;
        ssl_hello_s_time = 0;
        ssl_hello_s_random = "";
        ssl_hello_s_sessionid = "";
        ssl_hello_s_sessionidlen = 0;
        ssl_hello_s_ciphersuit = "";
        ssl_hello_s_compressionmethod = "";
        ssl_hello_s_extentionnum = 0;
        ssl_hello_s_extention = "";
        ssl_hello_s_alpn = "";
        ssl_hello_s_sessionticket = "";
        
        ssl_s_newsessionticket_lifetime = 0;
        ssl_s_newsessionticket_ticket = "";
        ssl_s_newsessionticket_ticketlen = 0;
        
        ssl_c_keyexchangelen = 0;
        ssl_c_keyexchange = "";
        ssl_s_keyexchangelen = 0;
        ssl_s_keyexchange = "";

        ssl_f_extensions[0] = "";
        ssl_f_extensions[1] = "";
        ssl_f_extensions_5[0] = "";
        ssl_f_extensions_5[1] = "";
        ssl_f_extensions_10[0] = "";
        ssl_f_extensions_10[1] = "";
        ssl_f_extensions_11[0] = "";
        ssl_f_extensions_11[1] = "";
        ssl_f_extensions_13[0] = "";
        ssl_f_extensions_13[1] = "";
        ssl_f_csuite[0] = "";
        ssl_f_csuite[1] = "";
    }
public:
    uint32_t ssl_version;
    uint32_t ssl_c_version;
    
    uint32_t ssl_hello_c_version;
    uint32_t ssl_hello_c_time;
    string   ssl_hello_c_random;
    string   ssl_hello_c_sessionid;
    uint32_t ssl_hello_c_sessionidlen;
    string   ssl_hello_c_ciphersuit;
    uint32_t ssl_hello_c_ciphersuitnum;
    string   ssl_hello_c_compressionmethod;
    uint32_t ssl_hello_c_compressionmethodlen;
    uint32_t ssl_hello_c_extentionnum;
    string   ssl_hello_c_extention;
    string   ssl_hello_c_alpn;
    Json::Value j_ssl_hello_c_alpn;
    string   ssl_hello_c_servername;
    uint32_t ssl_hello_c_servernametype;
    string   ssl_hello_c_sessionticket;
    
    uint32_t ssl_cert_num[2];
    string   ssl_cert_hash[2];
    Json::Value j_ssl_cert_hash[2];
    vector<string> ssl_cert_raw[2];  // Raw certificate data for each direction
    
    uint32_t ssl_hello_s_version;
    uint32_t ssl_hello_s_time;
    string   ssl_hello_s_random;
    string   ssl_hello_s_sessionid;
    uint32_t ssl_hello_s_sessionidlen;
    string   ssl_hello_s_ciphersuit;
    string   ssl_hello_s_compressionmethod;
    uint32_t ssl_hello_s_extentionnum;
    string   ssl_hello_s_extention;
    string   ssl_hello_s_alpn;
    string   ssl_hello_s_sessionticket;
    
    uint32_t ssl_s_newsessionticket_lifetime;
    string   ssl_s_newsessionticket_ticket;
    uint32_t ssl_s_newsessionticket_ticketlen;
    
    uint32_t ssl_c_keyexchangelen;
    string   ssl_c_keyexchange;
    uint32_t ssl_s_keyexchangelen;
    string   ssl_s_keyexchange;

    string  ssl_f_extensions[2];
    string  ssl_f_extensions_5[2];
    string  ssl_f_extensions_10[2];
    string  ssl_f_extensions_11[2];
    string  ssl_f_extensions_13[2];
    string  ssl_f_csuite[2];
};

class ssl_session
{
public:
    char* p_data;
    uint32_t data_len;
    ssl_message* p_ssl_message;
    bool b_parse_over[2];
    bool b_cacheing[2];
    bool b_send_over;
    uint16_t do_ssl_pb ; 
    uint16_t ofo_pkt_num[2];   //out of order pkt number
    uint32_t thread_id;
    uint32_t has_unknown_pkt;
};

#endif  /*NSS_STR_H*/

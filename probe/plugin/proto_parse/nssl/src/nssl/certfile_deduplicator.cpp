#include "DataStructure/Func_Math.h"
#include "certfile_deduplicator.h"


CCertfileDeduplicator::CCertfileDeduplicator(DWORD IN_MaxNodeNum)
{
    isOk = 0;
    MaxNodeNum = IN_MaxNodeNum;
    HashSize = GetPrimeNum(MaxNodeNum >> 2, MaxNodeNum);
    HashSize = J_max(HashSize, 1);
    if( 0 != m_Array.Init( MaxNodeNum+1 ))
    {
        return;
    }
    if( 0 != m_Judge.Init( HashSize,MaxNodeNum,&m_Array ))
    {
        return;
    }
    if( 0 != m_Sequence.Init( MaxNodeNum+1 ))
    {
        return;
    }
    isOk = 1;
}


CCertfileDeduplicator::~CCertfileDeduplicator()
{
    
}

unsigned long long CCertfileDeduplicator::JudgeValue(unsigned char *pkey, unsigned int clock)
{
    DWORD Index, Ret, IsAdd;
    DWORD *pIndex;
    
    STR_SHA1_NODE TmpNode;

    if(0 == clock)
    {
        clock = 1;
    }

    memcpy(&TmpNode.key, pkey, sizeof(sha1key));
    TmpNode.value = 1;

    pIndex = (DWORD *)pkey;

    Ret = m_Judge.JudgeAndAdd(*pIndex, TmpNode, IsAdd);
    if(ERRORSIGN < Ret)
    {
        Ret = m_Sequence.PopNode();
        if(Ret != 0)
        {
            m_Judge.DeleteValue(Ret);
            Ret = m_Judge.JudgeAndAdd(*pIndex, TmpNode, IsAdd);
        }
        else
        {
            //something wrong!
            return 1;
        }
    }

    if(!IsAdd)
    {
        m_Array.m_pData[Ret].value ++;
    }
    m_Sequence.RenewNode(Ret, clock);
    return m_Array.m_pData[Ret].value;
}

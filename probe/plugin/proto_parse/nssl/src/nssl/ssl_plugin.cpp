// Last Update:2019-09-02 14:40:07
/**
 * @file esp_str.h
 * @brief 
 * <AUTHOR>
 * @version 0.0.00
 * @date 2019-03-05
 */
 

#include <commit_tools.h>
#include <sys/stat.h>
#include <sys/time.h>
#include "ssl_plugin.h"
#include "ssl_sha1.h"
#include <fstream>
#include <hash.h>
#include <string.h>
#include <arpa/inet.h>
#include "crc64.h"
#include "aes_hash.h"
#include "tls_cipher_suites.h"
#include "tls_finger_browser.h"
#include <algorithm>
#include "rapidjson/stringbuffer.h"
#include "rapidjson/writer.h"
#include "GeneralInclude/Define_ProtocolID.h"
#include "th_engine_tools.h"

extern "C" {
    int get_plugin_id()
    {
        return 10638;
    }
    session_pasre_base * attach()
    {
        return new ssl_plugin();
    }
}

unsigned int hash_func(unsigned int bucktes, void *key)
{
    uint8_t * p = (uint8_t  *)key ; 
   return (unsigned int ) ((*(uint64_t*)p)^(*(uint64_t*)(p+8))^(*(uint64_t*)(p+16))^(*(uint64_t*)(p+24)) ^(*(uint64_t*)(p+32))) % bucktes ;
}
ssl_plugin::ssl_plugin()
{
    cerfile_dir = NULL;
    backList_hash  = (hash_t *) hash_alloc(65535,hash_func);
    trustList_hash  = (hash_t *) hash_alloc(65535,hash_func);
    reload();
    backList_parse();
    trustList_parse();
    aes_hash_ctx = AES_InitKey();
    if(NULL == aes_hash_ctx)
    {
        exit(-1);
    }
    tls_cipher_suites::getInstance();
    pDeduplicator = new CCertfileDeduplicator(2500);
}

ssl_plugin::~ssl_plugin()
{
    if(cerfile_dir)
    {
        free(cerfile_dir);
        cerfile_dir = NULL;
    }
    AES_CleanUp(aes_hash_ctx);
}

void ssl_plugin::reload()
{
    string conf_path = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/cerfiles/";
    cerfile_dir = strdup(conf_path.c_str());
}
void ssl_plugin::trustList_parse()
{
    string str;
    ifstream fin;
    string db_path = string(getenv("THE_DB_PATH")) + "/cert_trust.json";
    fin.open(db_path, ios::in);
    stringstream buf;
    buf << fin.rdbuf(); 
    str = buf.str();
    fin.close();
    
    Json::Reader reader;
    Json::Value rule_json;
    uint32_t hash_num;
    int type = 0;
    
    if (false != reader.parse(str, rule_json))
    {
        if(rule_json["hash"].isNull() == false)
        {
            type = 1;
            hash_num = rule_json["hash"].size();
            for(uint32_t i = 0; i < hash_num; i ++)
            {
                string hash = rule_json["hash"][i].asString();
                if(hash.length() == 40)
                {
                    transform(hash.begin(), hash.end(), hash.begin(), ::tolower);
                    hash_add_entry(trustList_hash , (void *)hash.c_str() , hash.length(),(void *)&type,sizeof(int)); 
                }
            }
        }
        if(rule_json["hashlink"].isNull() == false)
        {
            type = 2;
            hash_num = rule_json["hashlink"].size();
            for(uint32_t i = 0; i < hash_num; i ++)
            {
                string hash = rule_json["hashlink"][i].asString();
                if(hash.length() >= 80)
                {
                    transform(hash.begin(), hash.end(), hash.begin(), ::tolower);
                    hash_add_entry(trustList_hash , (void *)hash.c_str() , hash.length(),(void *)&type,sizeof(int)); 
                }
            }
        }
    }
    // 
}
void ssl_plugin::backList_parse()
{
    //打开配置配置文件 ， 解析 
    std::ifstream ifs;
    ifs.open("conf/cert_config.json");
    Json::Value val;
    Json::Reader reader;

    if (!reader.parse(ifs, val)) {
        return ;
    }
    white.do_ssl_pb = val["whiteListLog"].asUInt();
    white.do_session_pb = val["whiteListFlow"].asUInt();
    back.do_ssl_pb = val["blackListLog"].asUInt();
    back.do_session_pb = val["blackListFlow"].asUInt();

    // 读取文件  证书黑白名单配置 
    char buff[2048];
    FILE * fp = fopen("conf/certbwlist.json","r");
    if(fp != NULL) 
    {
        while (fgets(buff,2048,fp) !=NULL)
        {
            // 
             Json::Value val;
             Json::Reader reader;
             if (!reader.parse(buff, val))
             {
                 continue ;
             }
             unsigned int type = val["Type"].asUInt();
             std::string  str = val[""].asString();
             char buf[64];
             memcpy(buf,str.c_str(),40);
             hash_add_entry(backList_hash , (void *)buf , 40,(void *)&type,sizeof(int)); 
        }
        fclose(fp);
    }
    else 
    {
        printf("打开文件失败\n");
    }
    // 
}
bool ssl_plugin::potocol_init(session_pub* p_session, c_packet* p_packet)
{
    if(p_session==NULL || p_packet==NULL)
    {
        return false;
    }
    proto_parse_session *p_pp_session = (proto_parse_session*)(p_session->p_sp_session);
    ssl_session * p_ssl_session = (ssl_session *)p_pp_session->expansion_data;
    init_ssl_session(p_ssl_session);
    p_ssl_session->thread_id = p_packet->thread_id;
    p_ssl_session -> do_ssl_pb = 1; 
    return true;
}

void ssl_plugin::init_ssl_session(ssl_session* p_ssl_session)
{
    p_ssl_session->p_data = NULL;
    p_ssl_session->data_len = 0;
    p_ssl_session->p_ssl_message = new ssl_message();
    p_ssl_session->b_parse_over[0] = false;
    p_ssl_session->b_parse_over[1] = false;
    p_ssl_session->b_cacheing[0] = false;
    p_ssl_session->b_cacheing[1] = false;
    p_ssl_session->ofo_pkt_num[0] = 0;
    p_ssl_session->ofo_pkt_num[1] = 0;
    p_ssl_session->b_send_over = false;
    p_ssl_session->has_unknown_pkt = 0;
}
string ssl_plugin::bin2hex(const char *p_data, uint32_t len) 
{
    string s = "";
    char formate_char[3];
    for (uint32_t i = 0; i < len; ++i)
    {
        sprintf(formate_char, "%02x", (unsigned char)p_data[i]);
        s.append(formate_char, 2);
    }
    return s;
}

string ssl_plugin::bin2string(const char* p_data, uint32_t len) {
    // Check for null pointer
    if (p_data == nullptr) {
        return std::string();
    }
    
    // Check for valid length
    if (len == 0) {
        return std::string();
    }
    
    // Create string safely
    return std::string(p_data, len);
}

bool ssl_plugin::pkt_is_whole(c_packet * p_packet)
{
    if(p_packet->m_str_packet_moudle.Stack.ProtocolNum && PROTOCOL_SSL == p_packet->m_str_packet_moudle.Stack.pProtocol[p_packet->m_str_packet_moudle.Stack.ProtocolNum-1].Protocol)
    {
        uint8_t* p_cur = p_packet->p_app_data;
        uint32_t offset = 0;
        if(offset + 5 <= p_packet->app_data_len)
        {
            uint8_t content_type = *(uint8_t *)(p_cur+offset);
            offset += 1;
            if(*(uint8_t *)(p_cur+offset) != 0x03 || *(uint8_t *)(p_cur+offset+1) > 0x03)
            {
                return false;
            }
            offset += 2;
            uint16_t length = ntohs(*(uint16_t *)(p_cur+offset));
            offset += 2;
            if(23 == content_type)  //application data
            {
                return true;
            }
            if(offset + length == p_packet->app_data_len)
            {
                return true;
            }
        }
    }
    return false;
}

bool ssl_plugin::data_is_whole(ssl_session * p_ssl_session)
{
    if (p_ssl_session->p_data == NULL || p_ssl_session->data_len == 0)
    {
        return false;
    }
    char* p_cur = p_ssl_session->p_data;
    uint32_t offset = 0;
    for(;offset + 5 <=  p_ssl_session->data_len;)
    {
        uint8_t content_type = *(uint8_t *)(p_cur+offset);
        offset += 1;
        if(*(uint8_t *)(p_cur+offset) != 0x03 || *(uint8_t *)(p_cur+offset+1) > 0x03)
        {
            return false;
        }
        offset += 2;
        uint16_t length = ntohs(*(uint16_t *)(p_cur+offset));
        offset += 2;
        if(23 == content_type)  //application data
        {
            return true;
        }
        if(offset + length == p_ssl_session->data_len)
        {
            return true;
        }
        else if (offset + length < p_ssl_session->data_len)
        {
            offset += length;
            continue;
        }
        else
        {
            return false;
        }
    }
    return false;
}

bool ssl_plugin:: potocol_sign_judge(session_pub * p_session,c_packet * p_packet) //组包
{
    uint32_t seq_tmp;
    if(p_session==NULL || p_packet==NULL)
    {
        return false;
    }
    if(PROTOCOL_SSL_2 == p_packet->app_pro)
    {
        ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_TLS_LOW_VERSION);
    }
    proto_parse_session* p_pp_session = (proto_parse_session * )( p_session -> p_sp_session);
    ssl_session * p_ssl_session = (ssl_session *)p_pp_session->expansion_data;

    if(PROTOCOL_SSL != p_packet->m_str_packet_moudle.Stack.pProtocol[p_packet->m_str_packet_moudle.Stack.ProtocolNum-1].Protocol)
    {
        p_ssl_session->has_unknown_pkt = 1;
    }
    if(p_ssl_session -> do_ssl_pb == 0) 
    {
        return true ;
    }
    //组包
    if (p_packet->app_data_len == 0)
    {
        return false;
    }
    if(PROTOCOL_SSL == p_packet->m_str_packet_moudle.Stack.pProtocol[p_packet->m_str_packet_moudle.Stack.ProtocolNum-1].Protocol && 0x17 == p_packet->p_app_data[0] && 5 <= p_packet->app_data_len && p_session->tls_app_len_num < 255)
    {
        uint8_t app_len = 0;
        app_len = (p_packet->p_app_data[4] & 7);
        p_session->tls_app_len[app_len] ++;
        p_session->tls_app_len_num ++;
    }
    if(p_session->session_basic.Server == PACKETFROMUNKOWN || p_ssl_session->b_parse_over[p_packet->Directory] == true)
    {
        return false;
    }

    if(p_ssl_session->b_cacheing[p_packet->Directory])
    {
        p_session->p_session_ext->tcp_repcom[p_packet->Directory]->add_fragment(p_packet->seq, p_packet->app_data_len, (char*)p_packet->p_app_data);
        p_session->p_session_ext->tcp_repcom[p_packet->Directory]->front_fragment(seq_tmp, p_ssl_session->data_len, p_ssl_session->p_data);
    }
    else
    {
        if (p_packet->flags.b_has_flag_syn
                && PROTOCOL_SSL == p_packet->m_str_packet_moudle.Stack.pProtocol[p_packet->m_str_packet_moudle.Stack.ProtocolNum-1].Protocol
                && p_packet->app_data_len >= 43
                && ((0 == p_packet->Directory && PACKETFROMCLIENT == p_session->session_basic.Server) || (1 == p_packet->Directory && PACKETFROMSERVER == p_session->session_basic.Server)))
        {
            if(0x16 == p_packet->p_app_data[0] && p_packet->p_app_data[9] == 0x03 && p_packet->p_app_data[10] <= 0x03 && 0x01 == p_packet->p_app_data[5])
            {
                ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_SYN_SSL_PAYLOAD);
            }
        }
        if(pkt_is_whole(p_packet))              //pkt
        {
            p_ssl_session->p_data = (char *)p_packet->p_app_data;
            p_ssl_session->data_len = p_packet->app_data_len;
            return true;
        }
        else if(p_packet->m_str_packet_moudle.Stack.ProtocolNum && PROTOCOL_SSL == p_packet->m_str_packet_moudle.Stack.pProtocol[p_packet->m_str_packet_moudle.Stack.ProtocolNum-1].Protocol)
        {
            if(NULL == p_session->p_session_ext)
            {
                return false;
            }
            p_session->p_tcp_recom_pop_cb(p_session, p_packet->Directory);
            if(p_session->p_session_ext->tcp_repcom[p_packet->Directory])
            {
                p_session->p_session_ext->tcp_repcom[p_packet->Directory]->add_fragment(p_packet->seq, p_packet->app_data_len, (char*)p_packet->p_app_data);
                p_session->p_session_ext->tcp_repcom[p_packet->Directory]->front_fragment(seq_tmp, p_ssl_session->data_len, p_ssl_session->p_data);
                p_ssl_session->b_cacheing[p_packet->Directory] = true;
            }
        }
        else
        {
            if(NULL == p_session->p_session_ext)
            {
                return false;
            }
            p_session->p_tcp_recom_pop_cb(p_session, p_packet->Directory);
            if(p_session->p_session_ext->tcp_repcom[p_packet->Directory])
            {
                p_session->p_session_ext->tcp_repcom[p_packet->Directory]->add_fragment(p_packet->seq, p_packet->app_data_len, (char*)p_packet->p_app_data);
                p_session->p_session_ext->tcp_repcom[p_packet->Directory]->front_fragment(seq_tmp, p_ssl_session->data_len, p_ssl_session->p_data);
                p_ssl_session->b_cacheing[p_packet->Directory] = true;
            }
            return false;
        }
    }

    if (data_is_whole(p_ssl_session))   //如果此次组包之后，恰好得到完整的N块，则进入协议解析
    {
        return true;
    }
    else if(p_session->p_session_ext->tcp_repcom[p_packet->Directory] && p_session->p_session_ext->tcp_repcom[p_packet->Directory]->get_fragment_num() > 1)   //maybe pkt lost
    {
        p_ssl_session->ofo_pkt_num[p_packet->Directory] ++;
        if(p_ssl_session->ofo_pkt_num[p_packet->Directory] >= 10)            //dont wait that pkt
        {
            p_ssl_session->ofo_pkt_num[p_packet->Directory] = 0;
            return true;
        }
        return false;
    }
    else    //no pkt lost or tcp_recom_pop fail
    {
        return false;
    }
}

void ssl_plugin::level_5_alpn_parse(char* p_data, uint16_t data_len, ssl_session * p_ssl_session, int c_or_s)
{
    char* p_cur = p_data;
    uint16_t offset = 0;
    ssl_message* p = p_ssl_session->p_ssl_message;
    Json::Value json_info;
    Json::FastWriter fast_writer;

    if(offset + 2 > data_len)
    {
        return;
    }
    uint16_t alpn_extention_length = ntohs(*(uint16_t *)(p_cur+offset));
    offset += 2;
    for (;offset + 1 <= data_len;)
    {
        uint8_t alpn_string_length = *(uint8_t *)(p_cur+offset);
        offset += 1;
        if(offset + alpn_string_length > data_len)
        {
            break;
        }
        string protocol(p_cur+offset, alpn_string_length);
        if("h2"==protocol || (protocol.length() >= 4 && "http" == protocol.substr(4)))
        {
            ((th_engine_tools *)p_th_tools)->th_set_label(p_s_session->labels, LABEL_SSL_WEB);
        }
        json_info.append(protocol);
        offset += alpn_string_length;
        if (offset >= data_len)
        {
            break;
        }
    }
    if (c_or_s == 0)
    {
        p->ssl_hello_c_alpn = fast_writer.write(json_info);
        p->ssl_hello_c_alpn.pop_back();
        p->j_ssl_hello_c_alpn = json_info;
    }
    else if (c_or_s == 1)
    {
        p->ssl_hello_s_alpn = fast_writer.write(json_info);
        p->ssl_hello_s_alpn.pop_back();
    }
}

void ssl_plugin::level_5_server_name_parse(char* p_data, uint16_t data_len, ssl_session * p_ssl_session, int c_or_s)
{
    if (c_or_s != 0)
    {
        return;
    }
    char* p_cur = p_data;
    uint16_t offset = 0;
    ssl_message* p = p_ssl_session->p_ssl_message;
    if(offset + 5 > data_len)
    {
        return;
    }

    //uint16_t server_name_list_length = ntohs(*(uint16_t *)(p_cur+offset));
    offset += 2;
    p->ssl_hello_c_servernametype = *(uint8_t *)(p_cur+offset);
    offset += 1;
    uint16_t server_name_length = ntohs(*(uint16_t *)(p_cur+offset));
    offset += 2;
    if(offset + server_name_length > data_len)
    {
        return;
    }
    string ssl_hello_c_servername((p_cur+offset), server_name_length);
    p->ssl_hello_c_servername = ssl_hello_c_servername;
}

void ssl_plugin::level_5_status_request_parse(char* p_data, uint16_t data_len, ssl_session * p_ssl_session, int c_or_s)
{
    ssl_message* p = p_ssl_session->p_ssl_message;
    if(data_len && 1 == p_data[0])
    {
        ((th_engine_tools *)p_th_tools)->th_set_label(p_s_session->labels, LABEL_SSL_CHELLO_OCSP);
    }
    p->ssl_f_extensions_5[c_or_s] = bin2string((const char*)(p_data), data_len);
}
void ssl_plugin::level_5_supported_groups_parse(char* p_data, uint16_t data_len, ssl_session * p_ssl_session, int c_or_s)
{
    char* p_cur = p_data;
    uint16_t offset = 0;
    ssl_message* p = p_ssl_session->p_ssl_message;
    
    if (offset + 2 > data_len)
    {
        return;
    }
    offset += 2;
    p->ssl_f_extensions_10[c_or_s] = "";
    for(; offset + 2 <= data_len; offset += 2)
    {
        string ssuit = bin2string((const char*)(p_cur+offset), 2);
        uint16_t suit = *(uint16_t *)(p_cur+offset);
        uint8_t *psuit = (uint8_t *)&suit;
        if(0x0a0a!=(suit&0x0f0f) || ((psuit[0]>>4) != (psuit[1] >> 4)))
        {
            p->ssl_f_extensions_10[c_or_s] += ssuit;
        }
        else
        {
            p->ssl_f_extensions_10[c_or_s] += "gres";
        }
    }
}
void ssl_plugin::level_5_ec_point_formats_parse(char* p_data, uint16_t data_len, ssl_session * p_ssl_session, int c_or_s)
{
    char* p_cur = p_data;
    uint16_t offset = 0;
    ssl_message* p = p_ssl_session->p_ssl_message;
    
    if (offset + 1 > data_len)
    {
        return;
    }
    offset += 1;
    p->ssl_f_extensions_11[c_or_s] = "";
    if(data_len > 1)
    {
        p->ssl_f_extensions_11[c_or_s] = bin2string((const char*)(p_cur+offset), data_len-1);
    }
}
void ssl_plugin::level_5_signature_algorithms_parse(char* p_data, uint16_t data_len, ssl_session * p_ssl_session, int c_or_s)
{
    char* p_cur = p_data;
    uint16_t offset = 0;
    ssl_message* p = p_ssl_session->p_ssl_message;
    
    if (offset + 2 > data_len)
    {
        return;
    }
    offset += 2;
    p->ssl_f_extensions_13[c_or_s] = "";
    for(; offset + 2 <= data_len; offset += 2)
    {
        string ssuit = bin2string((const char*)(p_cur+offset), 2);
        uint16_t suit = *(uint16_t *)(p_cur+offset);
        uint8_t *psuit = (uint8_t *)&suit;
        if(0x0a0a!=(suit&0x0f0f) || ((psuit[0]>>4) != (psuit[1] >> 4)))
        {
            p->ssl_f_extensions_13[c_or_s] += ssuit;
        }
        else
        {
            p->ssl_f_extensions_13[c_or_s] += "gres";
        }
    }
}

void ssl_plugin::level_5_session_ticket_parse(char* p_data, uint16_t data_len, ssl_session * p_ssl_session, int c_or_s)
{
    char* p_cur = p_data;
    uint16_t offset = 0;
    ssl_message* p = p_ssl_session->p_ssl_message;

    if (c_or_s == 0)
    {
        p->ssl_hello_c_sessionticket = bin2string((const char*)(p_cur+offset), data_len);
    }
    else if (c_or_s == 1)
    {
        p->ssl_hello_s_sessionticket = bin2string((const char*)(p_cur+offset), data_len);
    }
}

void ssl_plugin::level_4_extention_parse(char* p_data, uint16_t data_len, ssl_session * p_ssl_session, int c_or_s)//c_or_s:  0是client 1是server
{
    char* p_cur = p_data;
    uint16_t offset = 0;
    ssl_message* p = p_ssl_session->p_ssl_message;

    uint32_t extentionnum = 0;
    Json::Value json_extention;
    Json::FastWriter fast_writer;
    p->ssl_f_extensions[c_or_s] = "";

    for(;offset + 4 <= data_len;)
    {
        extentionnum++;
        uint16_t type = ntohs(*(uint16_t *)(p_cur+offset));
        offset += 2;
        uint16_t length = ntohs(*(uint16_t *)(p_cur+offset));
        offset += 2;
        //异常校验
        if (offset + length > data_len)
        {
            return;
        }
        string data = bin2string((const char*)(p_cur+offset), length);
        Json::Value json_tmp;
        json_tmp["t"] = type;
        json_tmp["l"] = length;
        json_tmp["v"] = data;
        json_extention.append(json_tmp);
        //第二层结构解析
        switch (type)
        {
            case 0: //server_name
                {
                    level_5_server_name_parse(p_cur+offset, length, p_ssl_session, c_or_s);
                    break;
                }
            case 5: //status request
                {
                    level_5_status_request_parse(p_cur+offset, length, p_ssl_session, c_or_s);
                    break;
                }
            case 10: //Type: supported_groups (10)
                {
                    level_5_supported_groups_parse(p_cur+offset, length, p_ssl_session, c_or_s);
                    break;
                }
            case 11: //Type: ec_point_formats (11)
                {
                    level_5_ec_point_formats_parse(p_cur+offset, length, p_ssl_session, c_or_s);
                    break;
                }
            case 13: //Type: signature_algorithms (13)
                {
                    level_5_signature_algorithms_parse(p_cur+offset, length, p_ssl_session, c_or_s);
                    break;
                }
            case 16: //application_layer_protocol_negotiation
                {
                    level_5_alpn_parse(p_cur+offset, length, p_ssl_session, c_or_s);
                    break;
                }
            case 35: //session_ticket
                {
                    level_5_session_ticket_parse(p_cur+offset, length, p_ssl_session, c_or_s);
                    break;
                }
        }
        uint8_t *ptype = (uint8_t *)&type;
        if(0x0a0a!=(type&0x0f0f) || ((ptype[0]>>4) != (ptype[1] >> 4)))
        {
            p->ssl_f_extensions[c_or_s] += bin2string((const char*)&type, 2);
        }
        else
        {
            p->ssl_f_extensions[c_or_s] += "gres";
        }
        
        //解析完成，转到下一个块
        offset += length;
        if(offset >= data_len)
        {
            if (c_or_s == 0)
            {
                p->ssl_hello_c_extentionnum = extentionnum;
                p->ssl_hello_c_extention = fast_writer.write(json_extention);
                p->ssl_hello_c_extention.pop_back();
            }
            else if (c_or_s == 1)
            {
                p->ssl_hello_s_extentionnum = extentionnum;
                p->ssl_hello_s_extention = fast_writer.write(json_extention);
                p->ssl_hello_s_extention.pop_back();
            }
            return;
        }
    }
}

void ssl_plugin::level_3_client_hello_parse(char* p_data, uint16_t data_len, ssl_session * p_ssl_session)
{
    char* p_cur = p_data;
    uint16_t offset = 0;
    ssl_message* p = p_ssl_session->p_ssl_message;
    if(offset + 6 > data_len)
    {
        return;
    }
    //uint8_t handshake_type = *(uint8_t *)(p_cur+offset);
    offset += 1;
    //获取三字节整型数
    uint32_t length = *(uint8_t *)(p_cur+offset);
    length = length << 16;
    offset += 1;
    length += ntohs(*(uint16_t *)(p_cur+offset));
    offset += 2;
    p->ssl_hello_c_version = ntohs(*(uint16_t *)(p_cur+offset));
    offset += 2;
    p->ssl_hello_c_time = ntohl(*(uint32_t *)(p_cur+offset));
    //异常校验
    if (offset + 32 > data_len)
    {
        return;
    }
    p->ssl_hello_c_random = bin2string((const char*)(p_cur+offset), 32);
    offset += 32;
    if(offset + 1 > data_len)
    {
        return;
    }
    p->ssl_hello_c_sessionidlen = *(uint8_t *)(p_cur+offset);
    offset += 1;
    //异常校验
    if (offset + p->ssl_hello_c_sessionidlen > data_len)
    {
        return;
    }
    p->ssl_hello_c_sessionid = bin2string((const char*)(p_cur+offset), p->ssl_hello_c_sessionidlen);
    offset += p->ssl_hello_c_sessionidlen;
    if (offset + 2 > data_len)
    {
        return;
    }
    uint32_t ssl_hello_c_ciphersuitlen = ntohs(*(uint16_t *)(p_cur+offset));
    p->ssl_hello_c_ciphersuitnum = ssl_hello_c_ciphersuitlen / 2;
    offset += 2;
    //异常校验
    if (offset + ssl_hello_c_ciphersuitlen > data_len)
    {
        return;
    }
    p->ssl_f_csuite[0] = "";
    p->ssl_hello_c_ciphersuit = "";
    for(uint32_t i=0;i<p->ssl_hello_c_ciphersuitnum;i ++)
    {
        if(offset + 2 > data_len)
        {
            return;
        }
        string ssuit=bin2string((const char*)(p_cur+offset), 2);
        uint16_t suit = *(uint16_t *)(p_cur+offset);
        uint8_t *psuit = (uint8_t *)&suit;
        if(0x0a0a!=(suit&0x0f0f) || ((psuit[0]>>4) != (psuit[1] >> 4)))
        {
            p->ssl_f_csuite[0] += ssuit;
            if(0 == tls_cipher_suites::getInstance()->get_label(suit))
            {
                ((th_engine_tools *)p_th_tools)->th_set_label(p_s_session->labels, LABEL_SSL_UNKNOW_SUIT);
            }
        }
        else
        {
            p->ssl_f_csuite[0] += "gres";
        }
        p->ssl_hello_c_ciphersuit += ssuit;
        offset += 2;
    }
    if(offset + 1 > data_len)
    {
        return;
    }
    p->ssl_hello_c_compressionmethodlen = *(uint8_t *)(p_cur+offset);
    offset += 1;
    //异常校验
    if (offset + p->ssl_hello_c_compressionmethodlen > data_len)
    {
        return;
    }
    p->ssl_hello_c_compressionmethod = bin2string((const char*)(p_cur+offset), p->ssl_hello_c_compressionmethodlen);
    offset += p->ssl_hello_c_compressionmethodlen;
    //server_hello有时没有extention信息，故此处也做此判断
    if (offset + 2 > data_len)
    {
        ((th_engine_tools *)p_th_tools)->th_set_label(p_s_session->labels, LABEL_SSL_NO_EXT_C);
        return;
    }
    uint32_t ssl_hello_c_extention_len = ntohs(*(uint16_t *)(p_cur+offset));
    offset += 2;
    //异常校验
    if (offset + ssl_hello_c_extention_len > data_len)
    {
        return;
    }
    level_4_extention_parse(p_cur+offset, ssl_hello_c_extention_len, p_ssl_session, 0);//0：client

    if(0==p->ssl_hello_c_extentionnum)
    {
        ((th_engine_tools *)p_th_tools)->th_set_label(p_s_session->labels, LABEL_SSL_NO_EXT_C);
    }
    if(""==p->ssl_hello_c_servername)
    {
        ((th_engine_tools *)p_th_tools)->th_set_label(p_s_session->labels, LABEL_SSL_NO_SNI);
    }
    else
    {
        uint32_t ip;
        if(inet_pton(AF_INET, p->ssl_hello_c_servername.c_str(), (void *)&ip))
        {
            ((th_engine_tools *)p_th_tools)->th_set_label(p_s_session->labels, LABEL_SSL_IP_SNI);
            if(PACKETFROMCLIENT == p_s_session->session_basic.Server && p_s_session->session_basic.pIP[1].str() != p->ssl_hello_c_servername)
            {
                ((th_engine_tools *)p_th_tools)->th_set_label(p_s_session->labels, LABEL_SNI_DIFF_IP);
            }
            else if(PACKETFROMSERVER == p_s_session->session_basic.Server && p_s_session->session_basic.pIP[0].str() != p->ssl_hello_c_servername)
            {
                ((th_engine_tools *)p_th_tools)->th_set_label(p_s_session->labels, LABEL_SNI_DIFF_IP);
            }
        }
        else if(p_s_session->domain_judge_cb(p_s_session->thread_id, p->ssl_hello_c_servername.c_str()))
        {
            ((th_engine_tools *)p_th_tools)->th_set_label(p_s_session->labels, LABEL_SSL_DOMAIN_T1000);
        }
    }
    
    offset += ssl_hello_c_extention_len;
    if (offset >= data_len)
    {
        return;
    }
}

void ssl_plugin::level_3_server_hello_parse(char* p_data, uint16_t data_len, ssl_session * p_ssl_session)
{
    char* p_cur = p_data;
    uint16_t offset = 0;
    ssl_message* p = p_ssl_session->p_ssl_message;
    if(offset + 6 > data_len)
    {
        return;
    }

    //uint8_t handshake_type = *(uint8_t *)(p_cur+offset);
    offset += 1;
    //获取三字节整型数
    uint32_t length = *(uint8_t *)(p_cur+offset);
    length = length << 16;
    offset += 1;
    length += ntohs(*(uint16_t *)(p_cur+offset));
    offset += 2;
    p->ssl_hello_s_version = ntohs(*(uint16_t *)(p_cur+offset));
    offset += 2;
    p->ssl_hello_s_time = ntohl(*(uint32_t *)(p_cur+offset));
    //异常校验
    if (offset + 32 > data_len)
    {
        return;
    }
    p->ssl_hello_s_random = bin2string((const char*)(p_cur+offset), 32);
    offset += 32;
    if (offset + 1 > data_len)
    {
        return;
    }
    p->ssl_hello_s_sessionidlen = *(uint8_t *)(p_cur+offset);
    offset += 1;
    //异常校验
    if (offset + p->ssl_hello_s_sessionidlen > data_len)
    {
        return;
    }
    p->ssl_hello_s_sessionid = bin2string((const char*)(p_cur+offset), p->ssl_hello_s_sessionidlen);
    offset += p->ssl_hello_s_sessionidlen;
    if (offset + 2 > data_len)
    {
        return;
    }
    p->ssl_f_csuite[1] = "";
    p->ssl_hello_s_ciphersuit = bin2string((const char*)(p_cur+offset), 2);
    uint16_t suit = *(uint16_t *)(p_cur+offset);
    uint8_t *psuit = (uint8_t *)&suit;
    if(0x0a0a!=(suit&0x0f0f) || ((psuit[0]>>4) != (psuit[1] >> 4)))
    {
        p->ssl_f_csuite[1] = p->ssl_hello_s_ciphersuit;
    }
    else
    {
        p->ssl_f_csuite[1] = "gres";
    }
    uint8_t suit_type = tls_cipher_suites::getInstance()->get_label(suit);
    if(0 == suit_type)
    {
        ((th_engine_tools *)p_th_tools)->th_set_label(p_s_session->labels, LABEL_SSL_UNKNOW_SUIT);
    }
    else if(1 == suit_type)
    {
        ((th_engine_tools *)p_th_tools)->th_set_label(p_s_session->labels, LABEL_SSL_DES);
    }
    else if(2 == suit_type)
    {
        ((th_engine_tools *)p_th_tools)->th_set_label(p_s_session->labels, LABEL_SSL_RC4);
    }
    offset += 2;
    if (offset + 1 > data_len)
    {
        return;
    }
    p->ssl_hello_s_compressionmethod = bin2string((const char*)(p_cur+offset), 1);
    offset += 1;
    //有时候没有extention
    if (offset + 2 > data_len)
    {
        ((th_engine_tools *)p_th_tools)->th_set_label(p_s_session->labels, LABEL_SSL_NO_EXT_S);
        return;
    }
    uint32_t ssl_hello_s_extention_len = ntohs(*(uint16_t *)(p_cur+offset));
    offset += 2;
    //异常校验
    if (offset + ssl_hello_s_extention_len > data_len)
    {
        return;
    }
    level_4_extention_parse(p_cur+offset, ssl_hello_s_extention_len, p_ssl_session, 1);//1:server
    if(0==p->ssl_hello_s_extentionnum)
    {
        ((th_engine_tools *)p_th_tools)->th_set_label(p_s_session->labels, LABEL_SSL_NO_EXT_S);
    }
    offset += ssl_hello_s_extention_len;
    if (offset >= data_len)
    {
        return;
    }
}

void ssl_plugin::level_3_new_session_ticket_parse(char* p_data, uint16_t data_len, ssl_session * p_ssl_session)
{
    char* p_cur = p_data;
    uint16_t offset = 0;
    ssl_message* p = p_ssl_session->p_ssl_message;
    if(offset + 10 > data_len)
    {
        return;
    }

    //uint8_t handshake_type = *(uint8_t *)(p_cur+offset);
    offset += 1;
    //获取三字节整型数
    uint32_t length = *(uint8_t *)(p_cur+offset);
    length = length << 16;
    offset += 1;
    length += ntohs(*(uint16_t *)(p_cur+offset));
    offset += 2;
    p->ssl_s_newsessionticket_lifetime = ntohl(*(uint32_t *)(p_cur+offset));
    offset += 4;
    p->ssl_s_newsessionticket_ticketlen = ntohs(*(uint16_t *)(p_cur+offset));
    offset += 2;
    //异常校验
    if (offset + p->ssl_s_newsessionticket_ticketlen > data_len)
    {
        return;
    }
    p->ssl_s_newsessionticket_ticket = bin2string((const char*)(p_cur+offset), p->ssl_s_newsessionticket_ticketlen);
    offset += p->ssl_s_newsessionticket_ticketlen;
    if (offset >= data_len)
    {
        return;
    }
}

void ssl_plugin::level_3_client_key_exchange_parse(char* p_data, uint16_t data_len, ssl_session * p_ssl_session)
{
    char* p_cur = p_data;
    uint16_t offset = 0;
    ssl_message* p = p_ssl_session->p_ssl_message;
    if(offset + 5 > data_len)
    {
        return;
    }

    //uint8_t handshake_type = *(uint8_t *)(p_cur+offset);
    offset += 1;
    //获取三字节整型数
    uint32_t length = *(uint8_t *)(p_cur+offset);
    length = length << 16;
    offset += 1;
    length += ntohs(*(uint16_t *)(p_cur+offset));
    offset += 2;
    p->ssl_c_keyexchangelen = *(uint8_t *)(p_cur+offset);
    offset += 1;
    //异常校验
    if (offset + p->ssl_c_keyexchangelen > data_len)
    {
        return;
    }
    p->ssl_c_keyexchange = bin2string((const char*)(p_cur+offset), p->ssl_c_keyexchangelen);
    offset += p->ssl_c_keyexchangelen;
    if (offset >= data_len)
    {
        return;
    }
}

void ssl_plugin::level_3_server_key_exchange_parse(char* p_data, uint16_t data_len, ssl_session * p_ssl_session)
{
    char* p_cur = p_data;
    uint16_t offset = 0;
    ssl_message* p = p_ssl_session->p_ssl_message;
    if(offset + 4 > data_len)
    {
        return;
    }

    //uint8_t handshake_type = *(uint8_t *)(p_cur+offset);
    offset += 1;
    //获取三字节整型数
    uint32_t length = *(uint8_t *)(p_cur+offset);
    length = length << 16;
    offset += 1;
    length += ntohs(*(uint16_t *)(p_cur+offset));
    offset += 2;
    //异常校验
    if (offset + length > data_len)
    {
        return;
    }
    p->ssl_s_keyexchangelen = length;
    p->ssl_s_keyexchange = bin2string((const char*)(p_cur+offset), length);
    offset += length;
    if (offset >= data_len)
    {
        return;
    }
}

string ssl_plugin::write_certificate_file(char* p_data, std::string str_key, uint32_t data_len, ssl_session * p_ssl_session)
{
    string filename(cerfile_dir);
    filename += "/";
    DNUMTOSTR(p_ssl_session->thread_id, filename);
    filename += "/";
    filename += str_key.substr(0, 2);
    filename += "/";
    create_path(filename);
    filename += str_key;
    filename += ".cer";
    if (access(filename.c_str(), F_OK) == -1)
    {
        FILE* fp = fopen(filename.c_str(),"w+");
        if(fp == NULL)
        {
            printf("文件打开错误,filename:%s \n", filename.c_str());
            abort();
        }
        fwrite(p_data, data_len, 1, fp);
        fclose(fp);
    }
    return str_key;
}

void ssl_plugin::level_3_certificate_parse(char* p_data, uint16_t data_len, ssl_session * p_ssl_session, uint8_t Directory)
{
    char* p_cur = p_data;
    uint16_t offset = 0;
    ssl_message* p = p_ssl_session->p_ssl_message;
    unsigned long long times, mod;

    if(offset + 4 > data_len)
    {
        return;
    }

    //uint8_t handshake_type = *(uint8_t *)(p_cur+offset);
    offset += 1;
    //获取三字节整型数
    uint32_t length = *(uint8_t *)(p_cur+offset);
    length = length << 16;
    offset += 1;
    length += ntohs(*(uint16_t *)(p_cur+offset));
    offset += 2;
    //校验， data_len比length大4
    if (data_len != length +4 || offset + 3 > data_len)
    {
        return;
    }
    //获取certificates_length
    uint32_t certificates_length = *(uint8_t *)(p_cur+offset);
    certificates_length = certificates_length << 16;
    offset += 1;
    certificates_length += ntohs(*(uint16_t *)(p_cur+offset));
    //校验，length应该比certificates_length大3
    if (length != certificates_length+3)
    {
        return;
    }
    if (certificates_length == 0)
    {
        return;
    }
    offset += 2;
    //证书队列
    uint32_t certificates_num = 0;
    // Json::Value jcer_hash_list;
    p->j_ssl_cert_hash[Directory].clear();
    p->ssl_cert_raw[Directory].clear();  // Clear any existing raw certificate data
    string hash_list = "";
    Json::FastWriter fast_writer;
    for(;offset + 3 <= data_len;)
    {
        //获取certificate_length
        uint32_t certificate_length = *(uint8_t *)(p_cur+offset);
        certificate_length = certificate_length << 16;
        offset += 1;
        certificate_length += ntohs(*(uint16_t *)(p_cur+offset));
        offset += 2;
        //异常校验，获取到当前证书长度之后做一下校验
        if(0 == certificate_length)
        {
            continue;
        }
        if (offset + certificate_length > data_len)
        {
            return;
        }
        unsigned char sha1_key[20] = {0};
        ssl_Func_SHA(1, (unsigned char*)p_cur+offset , certificate_length, sha1_key);
        string str_key = bin2string((const char*)sha1_key, 20);
        hash_list += str_key;
        int black = 0, white = 0, important = 0;
        p_s_session->target_scan_handle_cb(str_key, p_s_session->thread_id, black, white, important);
        // if(black)
        // {
        //     ((th_engine_tools *)p_th_tools)->th_set_label(p_s_session->labels, LABEL_BLACK_SESSION);
        // }
        if(white)
        {
            ((th_engine_tools *)p_th_tools)->th_set_label(p_s_session->labels, LABEL_WHITE_RELATE);
        }
        if(important)
        {
            ((th_engine_tools *)p_th_tools)->th_set_label(p_s_session->labels, LABEL_TARGET_RELATE);
        }
        if(NULL != (int *)hash_lookup_entry(trustList_hash, (void *)str_key.c_str(), 40))
        {
            ((th_engine_tools *)p_th_tools)->th_set_label(p_s_session->labels, LABEL_SSL_CERT_T1000);
        }
        int *p_type = (int *)hash_lookup_entry(backList_hash, (void *)str_key.c_str(), 40);
        if(p_type != NULL)
        {
            if(*p_type == 1)  // 白名单
            {
                p_ssl_session -> do_ssl_pb = this->white.do_ssl_pb ;
                p_s_session -> do_session_pb = this->white.do_session_pb ;
            }
            else
            {
                p_ssl_session -> do_ssl_pb = back.do_ssl_pb ;
                p_s_session -> do_session_pb = back.do_session_pb ;
                p_s_session -> b_black_ssl = 1;
            }
        }
        //将证书内容存到文件中
        times = pDeduplicator->JudgeValue(sha1_key, ((th_engine_tools *)p_th_tools)->get_clock_ts());
        if(times < 10000)
        {
            mod = 500;
        }
        else
        {
            mod = 5000;
        }
        if(1 == times % mod)
        {
            ((th_engine_tools *)p_th_tools)->send_log("certfile", (void *)sha1_key, 20, p_cur+offset, certificate_length);
        }
        
        // Store raw certificate data
        string cert_data(p_cur+offset, certificate_length);
        p->ssl_cert_raw[Directory].push_back(cert_data);
        
        string hash = str_key;
        p->j_ssl_cert_hash[Directory].append(str_key);
        //证书检测  
        certificates_num++;
        //解下一个证书
        offset += certificate_length;
        if (offset >= data_len)
        {
            break;
        }
    }
    if(hash_list.length() >= 80)
    {
        if(NULL != (int *)hash_lookup_entry(trustList_hash, (void *)hash_list.c_str(), hash_list.length()))
        {
            ((th_engine_tools *)p_th_tools)->th_set_label(p_s_session->labels, LABEL_SSL_CHAIN_LEGAL);
        }
    }
    p->ssl_cert_num[Directory] = certificates_num;
    p->ssl_cert_hash[Directory] = fast_writer.write(p->j_ssl_cert_hash[Directory]);
    p->ssl_cert_hash[Directory].pop_back();
    return;
}

void ssl_plugin::level_2_handshake_protocol_parse(char* p_data, uint16_t data_len, ssl_session * p_ssl_session, uint16_t version, uint8_t Directory)
{
    //注意，此时拿到handshake的buff,可能是多块handshake排列下来，未必只是一个
    //二层头解析
    char* p_cur = p_data;
    uint16_t offset = 0;
    //ssl_message* p = p_ssl_session->p_ssl_message;
    for (;offset + 4 <= data_len;)
    {
        uint8_t handshake_type = *(uint8_t *)(p_cur+offset);
        offset += 1;
        if (handshake_type != 1)
        {
            p_ssl_session->p_ssl_message->ssl_version = version;
        }
        else
        {
            p_ssl_session->p_ssl_message->ssl_c_version = version;
        }
        //获取三字节整型数，此值为当前handshake块的长度-4 
        uint32_t length = *(uint8_t *)(p_cur+offset);
        length = length << 16;
        offset += 1;
        length += ntohs(*(uint16_t *)(p_cur+offset));
        offset += 2;
        //异常校验
        if (offset + length > data_len || length <= 2)
        {
            return;
        }
        //第三层解析
        switch (handshake_type)
        {
            case 1://client_hello
                {
                    level_3_client_hello_parse(p_cur+offset-4, length+4, p_ssl_session);
                    break;
                }
            case 2://server_hello
                {
                    level_3_server_hello_parse(p_cur+offset-4, length+4, p_ssl_session);
                    break;
                }
            case 4://new_session_ticket
                {
                    level_3_new_session_ticket_parse(p_cur+offset-4, length+4, p_ssl_session);
                    break;
                }
            case 11://certificate
                {
                    level_3_certificate_parse(p_cur+offset-4, length+4, p_ssl_session, Directory);
                    break;
                }
            case 12://server_key_exchange
                {
                    level_3_server_key_exchange_parse(p_cur+offset-4, length+4, p_ssl_session);
                    break;
                }
            case 16://client_key_exchange
                {
                    level_3_client_key_exchange_parse(p_cur+offset-4, length+4, p_ssl_session);
                    break;
                }
        }
        //至此解完了一块handshake，看下是否还有
        offset += length;
        if (offset >= data_len)
        {
            break;
        }
    }
    return;
}

bool ssl_plugin::level_1_data_parse(char* p_data, uint32_t data_len, ssl_session * p_ssl_session, uint8_t Directory)
{
    if (p_data == NULL || data_len == 0)
    {
        return false;
    }
    char* p_cur = p_data;
    uint32_t offset = 0;
    for(;offset + 5 <= data_len;)
    {
        //Record Layer解析
        uint8_t content_type = *(uint8_t *)(p_cur+offset); 
        offset += 1;
        uint16_t version = ntohs(*(uint16_t *)(p_cur+offset)); 
        offset += 2;
        uint16_t length = ntohs(*(uint16_t *)(p_cur+offset));
        offset += 2;
        //第二层结构解析
        if(offset+length>data_len)
        {
            length = data_len - offset;     //防止越界访问
        }
        switch (content_type)
        {
            case 22://Handshake Protocol解析
                {
                    level_2_handshake_protocol_parse(p_cur+offset, length, p_ssl_session, version, Directory);
                    break;
                }
            case 23://application data
                {
                    p_ssl_session->b_parse_over[Directory] = true;
                    break;
                }
        }
        if(true == p_ssl_session->b_parse_over[Directory])
        {
            return true;
        }
        //解析完成，转到下一个块
        offset += length;
        if(offset < data_len)
        {
            continue;
        }
        else if (offset == data_len)
        {
            return false;//是否满足发送条件
        }
        else
        {
            return false;//是否满足发送条件
        }
    }
    return false;
}

bool ssl_plugin::potocol_parse_handle(session_pub * p_session,c_packet *p_packet) //解析
{
    uint32_t seq_tmp;
    if(p_session==NULL)
    {
        return false;
    }

    proto_parse_session* p_pp_session = (proto_parse_session * )( p_session -> p_sp_session);
    ssl_session * p_ssl_session = (ssl_session *)p_pp_session->expansion_data;
    if(p_ssl_session -> do_ssl_pb == 0) 
    {
        return true ;
    }
    p_s_session = p_session ;

    if(p_packet)
    {
        //如果解析数据之后发现可以发送了，就返回true
        bool ret = level_1_data_parse(p_ssl_session->p_data, p_ssl_session->data_len, p_ssl_session, p_packet->Directory);
        //解析完数据之后要将组包buff清理出来
        if(p_session->p_session_ext && p_session->p_session_ext->tcp_repcom[p_packet->Directory])
        {
            p_session->p_tcp_recom_push_cb(p_session, p_packet->Directory);
            p_ssl_session->b_cacheing[p_packet->Directory] = false;
        }
        //如果可以发送了，直接返回true
        if (ret)
        {
            return true;
        }
        //如果是FIN包也发送，fix me
        if (false)
        {
            return true;
        }
    }
    else    //release
    {
        if(p_session->p_session_ext && p_session->p_session_ext->tcp_repcom[0])
        {
            p_session->p_session_ext->tcp_repcom[0]->front_fragment(seq_tmp, p_ssl_session->data_len, p_ssl_session->p_data);
            level_1_data_parse(p_ssl_session->p_data, p_ssl_session->data_len, p_ssl_session, 0);
            p_session->p_tcp_recom_push_cb(p_session, 0);
            p_ssl_session->b_cacheing[0] = false;
        }
        if(p_session->p_session_ext && p_session->p_session_ext->tcp_repcom[1])
        {
            p_session->p_session_ext->tcp_repcom[1]->front_fragment(seq_tmp, p_ssl_session->data_len, p_ssl_session->p_data);
            level_1_data_parse(p_ssl_session->p_data, p_ssl_session->data_len, p_ssl_session, 1);
            p_session->p_tcp_recom_push_cb(p_session, 1);
            p_ssl_session->b_cacheing[1] = false;
        }
        return false;
    }

    return false;
}

void ssl_plugin::ssl_msg_send(session_pub* p_session)  //发送
{
    proto_parse_session* p_pp_session = (proto_parse_session * )( p_session -> p_sp_session);
    ssl_session * p_ssl_session = (ssl_session *)p_pp_session->expansion_data;
    if(p_ssl_session -> do_ssl_pb == 0) 
    {
        return  ;
    }
    ssl_message* p = p_ssl_session->p_ssl_message;

    if (p_ssl_session->b_send_over)
    {
        return;
    }
    if(768 == p->ssl_version || 769 == p->ssl_version)
    {
        ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_TLS_LOW_VERSION);
    }
    if(p_session->session_basic.Server == PACKETFROMCLIENT)
    {
        if(p->ssl_cert_num[0] > 0)
        {
            ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_SSL_CLIENT_CERT);
        }
    }
    else if(p_session->session_basic.Server == PACKETFROMSERVER)
    {
        if(p->ssl_cert_num[1] > 0)
        {
            ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_SSL_CLIENT_CERT);
        }
    }
    if(p->ssl_s_newsessionticket_lifetime)
    {
        if(p->ssl_s_newsessionticket_lifetime <= 3600)
        {
            ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_SSL_TICKET_LE1);
        }
        else if(p->ssl_s_newsessionticket_lifetime <= 86400)
        {
            ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_SSL_TICKET_LE24);
        }
        else if(p->ssl_s_newsessionticket_lifetime <= 604800)
        {
            ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_SSL_TICKET_LE168);
        }
        else
        {
            ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_SSL_TICKET_LONG);
        }
    }
    if(p->ssl_hello_c_time && p->ssl_hello_s_time)
    {
        uint32_t diff = (p->ssl_hello_c_time > p->ssl_hello_s_time) ? (p->ssl_hello_c_time - p->ssl_hello_s_time) : (p->ssl_hello_s_time - p->ssl_hello_c_time);
        if(diff < 86400)
        {
            ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_SSL_TS_DIFF_LT24);
        }
        else
        {
            ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_SSL_TS_DIFF_GE24);
        }
    }
    if("" != p->ssl_hello_c_sessionticket)
    {
        ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_SSL_SESS_TICKET);
    }
    else if("" != p->ssl_hello_c_sessionid)
    {
        ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_SSL_SESS_ID);
    }
    if(0 == p->ssl_cert_num[0] && 0 == p->ssl_cert_num[1])
    {
        ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_SSL_NOCERT);
    }
    else if(1 == p->ssl_cert_num[0] || 1 == p->ssl_cert_num[1])
    {
        ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_SSL_ONECERT);
    }
    else if(1 < p->ssl_cert_num[0] || 1 < p->ssl_cert_num[1])
    {
        ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_SSL_CERTCHAIN);
    }
//  ssl finger
    unsigned char aes_buf[16] = {0};
    Json::FastWriter writer;
    Json::Value json_c, json_s;
    string str_c = "", str_s = "";
    uint64_t finger_c = 0, finger_s = 0 ;
    char version_buf[11] = {0};
    if("" != p->ssl_hello_c_ciphersuit && 0 != p->ssl_hello_c_version)
    {
        uint32_t ts_offset = 0;
        if(p->ssl_hello_c_time > p_session->session_basic.StartTime[0])
        {
            ts_offset = p->ssl_hello_c_time - p_session->session_basic.StartTime[0];
        }
        else
        {
            ts_offset = p_session->session_basic.StartTime[0] - p->ssl_hello_c_time;
        }
        sprintf(version_buf,"%u",p->ssl_hello_c_version);
        str_c = string(version_buf) +
                "|" + p->ssl_hello_c_compressionmethod +
                "|" + p->ssl_f_csuite[0] +
                "|" + p->ssl_f_extensions[0] +
                "|" + ((ts_offset > 600)?"1":"0") +
                "|" + p->ssl_f_extensions_5[0] +
                "|" + p->ssl_f_extensions_10[0] +
                "|" + p->ssl_f_extensions_11[0] +
                "|" + p->ssl_f_extensions_13[0] +
                "|" + p->ssl_hello_c_alpn;
        // cout << "ssl_finger_c:" << str_c << endl;
        if(0 == AES_Hash(aes_hash_ctx, (unsigned char *)str_c.c_str(), str_c.length(), aes_buf))
        {
            finger_c = *(uint64_t *)aes_buf;
            finger_c &= (uint64_t)0x7fffffffffffffff;
        }
    }
    if(p_session->p_session_ext)
    {
        p_session->p_session_ext->app_c_finger = finger_c;
    }
    if(tls_finger_map::getInstance()->judge(finger_c))
    {
        ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_SSL_FP_BROWSER);
    }
    if("" != p->ssl_hello_s_ciphersuit && 0 != p->ssl_hello_s_version)
    {
        uint32_t ts_offset = 0;
        if(p->ssl_hello_s_time > p_session->session_basic.StartTime[0])
        {
            ts_offset = p->ssl_hello_s_time - p_session->session_basic.StartTime[0];
        }
        else
        {
            ts_offset = p_session->session_basic.StartTime[0] - p->ssl_hello_s_time;
        }
        sprintf(version_buf,"%u",p->ssl_hello_s_version);
        str_s = string(version_buf) +
                "|" + p->ssl_hello_s_compressionmethod +
                "|" + p->ssl_f_csuite[1] +
                "|" + p->ssl_f_extensions[1] +
                "|" + ((ts_offset > 600)?"1":"0") +
                "|" + p->ssl_f_extensions_5[1] +
                "|" + p->ssl_f_extensions_10[1] +
                "|" + p->ssl_f_extensions_11[1] +
                "|" + p->ssl_f_extensions_13[1] +
                "|" + p->ssl_hello_s_alpn;
        // cout << "ssl_finger_s:" << str_s << endl;
        if(0 == AES_Hash(aes_hash_ctx, (unsigned char *)str_s.c_str(), str_s.length(), aes_buf))
        {
            finger_s = *(uint64_t *)aes_buf;
            finger_s &= (uint64_t)0x7fffffffffffffff;
        }
    }
    if(p_session->p_session_ext)
    {
        p_session->p_session_ext->app_s_finger = finger_s;
    }
//  ssl finger --end
    if(should_log)
    {
        JKNmsg * p_msg = p_session->p_value->get();
        if (p_msg == NULL)
        {
            p_ssl_session->b_send_over = true;
            return;
        }
        CJsonMsg *p_json_msg = p_session->p_value -> jget();
        p_msg->set_type(29);
        ssl_msg* p_ssl = p_msg->mutable_ssl();
        Comm_msg* p_comm = p_ssl->mutable_comm_msg();
        //公共信息部分
        commsg_fill(p_session , p_comm, "10638", p_th_tools);
        //ssl msg
        p_ssl->set_ssl_version(p->ssl_version);
        p_ssl->set_ssl_c_version(p->ssl_c_version);
        p_ssl->set_ssl_hello_c_version(p->ssl_hello_c_version);
        p_ssl->set_ssl_hello_c_time(p->ssl_hello_c_time);
        p_ssl->set_ssl_hello_c_random(p->ssl_hello_c_random);
        p_ssl->set_ssl_hello_c_sessionid(p->ssl_hello_c_sessionid);
        p_ssl->set_ssl_hello_c_sessionidlen(p->ssl_hello_c_sessionidlen);
        p_ssl->set_ssl_hello_c_ciphersuit(p->ssl_hello_c_ciphersuit);
        p_ssl->set_ssl_hello_c_ciphersuitnum(p->ssl_hello_c_ciphersuitnum);
        p_ssl->set_ssl_hello_c_compressionmethod(p->ssl_hello_c_compressionmethod);
        p_ssl->set_ssl_hello_c_compressionmethodlen(p->ssl_hello_c_compressionmethodlen);
        p_ssl->set_ssl_hello_c_extentionnum(p->ssl_hello_c_extentionnum);
        p_ssl->set_ssl_hello_c_extention(p->ssl_hello_c_extention);
        p_ssl->set_ssl_hello_c_alpn(p->ssl_hello_c_alpn);
        p_ssl->set_ssl_hello_c_servername(p->ssl_hello_c_servername);
        p_ssl->set_ssl_hello_c_servernametype(p->ssl_hello_c_servernametype);
        p_ssl->set_ssl_hello_c_sessionticket(p->ssl_hello_c_sessionticket);
        if(p_session->session_basic.Server == PACKETFROMCLIENT)
        {
            p_ssl->set_ssl_cert_c_num(p->ssl_cert_num[0]);
            p_ssl->set_ssl_cert_c_hash(p->ssl_cert_hash[0]);
            p_ssl->set_ssl_cert_s_num(p->ssl_cert_num[1]);
            p_ssl->set_ssl_cert_s_hash(p->ssl_cert_hash[1]);
            // Add raw certificate data for client certificates
            for(int i = 0; i < p->ssl_cert_raw[0].size(); i++) {
                p_ssl->add_ssl_cert_c_raw(p->ssl_cert_raw[0][i]);
            }
            // Add raw certificate data for server certificates
            for(int i = 0; i < p->ssl_cert_raw[1].size(); i++) {
                p_ssl->add_ssl_cert_s_raw(p->ssl_cert_raw[1][i]);
            }
        }
        else if(p_session->session_basic.Server == PACKETFROMSERVER)
        {
            p_ssl->set_ssl_cert_c_num(p->ssl_cert_num[1]);
            p_ssl->set_ssl_cert_c_hash(p->ssl_cert_hash[1]);
            p_ssl->set_ssl_cert_s_num(p->ssl_cert_num[0]);
            p_ssl->set_ssl_cert_s_hash(p->ssl_cert_hash[0]);
            // Add raw certificate data for client certificates
            for(int i = 0; i < p->ssl_cert_raw[1].size(); i++) {
                p_ssl->add_ssl_cert_c_raw(p->ssl_cert_raw[1][i]);
            }
            // Add raw certificate data for server certificates
            for(int i = 0; i < p->ssl_cert_raw[0].size(); i++) {
                p_ssl->add_ssl_cert_s_raw(p->ssl_cert_raw[0][i]);
            }
        }
        p_ssl->set_ssl_hello_s_version(p->ssl_hello_s_version);
        p_ssl->set_ssl_hello_s_time(p->ssl_hello_s_time);
        p_ssl->set_ssl_hello_s_random(p->ssl_hello_s_random);
        p_ssl->set_ssl_hello_s_sessionid(p->ssl_hello_s_sessionid);
        p_ssl->set_ssl_hello_s_sessionidlen(p->ssl_hello_s_sessionidlen);
        p_ssl->set_ssl_hello_s_cipersuite(p->ssl_hello_s_ciphersuit);
        p_ssl->set_ssl_hello_s_compressionmethod(p->ssl_hello_s_compressionmethod);
        p_ssl->set_ssl_hello_s_extentionnum(p->ssl_hello_s_extentionnum);
        p_ssl->set_ssl_hello_s_extention(p->ssl_hello_s_extention);
        p_ssl->set_ssl_hello_s_alpn(p->ssl_hello_s_alpn);
        p_ssl->set_ssl_hello_s_sessionticket(p->ssl_hello_s_sessionticket);
        p_ssl->set_ssl_s_newsessionticket_lifetime(p->ssl_s_newsessionticket_lifetime);
        p_ssl->set_ssl_s_newsessionticket_ticket(p->ssl_s_newsessionticket_ticket);
        p_ssl->set_ssl_s_newsessionticket_ticketlen(p->ssl_s_newsessionticket_ticketlen);
        p_ssl->set_ssl_c_keyexchangelen(p->ssl_c_keyexchangelen);
        p_ssl->set_ssl_c_keyexchange(p->ssl_c_keyexchange);
        p_ssl->set_ssl_s_keyexchangelen(p->ssl_s_keyexchangelen);
        p_ssl->set_ssl_s_keyexchange(p->ssl_s_keyexchange);
        p_ssl->set_ssl_c_finger(finger_c);
        p_ssl->set_ssl_s_finger(finger_s);

        session_pub_ssl ssl_msg;
        ssl_msg.ch_ciphersuit = p->ssl_hello_c_ciphersuit;
        ssl_msg.ch_ciphersuit_num = p->ssl_hello_c_ciphersuitnum;
        ssl_msg.ch_server_name = p->ssl_hello_c_servername;
        ssl_msg.ch_alpn = p->ssl_hello_c_alpn;
        if(p_session->session_basic.Server == PACKETFROMCLIENT)
        {
            ssl_msg.c_cert = p->ssl_cert_hash[0];
            ssl_msg.c_cert_num = p->ssl_cert_num[0];
            ssl_msg.s_cert = p->ssl_cert_hash[1];
            ssl_msg.s_cert_num = p->ssl_cert_num[1];
        }
        else if(p_session->session_basic.Server == PACKETFROMSERVER)
        {
            ssl_msg.c_cert = p->ssl_cert_hash[1];
            ssl_msg.c_cert_num = p->ssl_cert_num[1];
            ssl_msg.s_cert = p->ssl_cert_hash[0];
            ssl_msg.s_cert_num = p->ssl_cert_num[0];
        }
        session_pub_push_ssl(p_session, &ssl_msg);

        if(p_json_msg)
        {
            rapidjson::StringBuffer strBuf;
            rapidjson::Writer<rapidjson::StringBuffer> writer(strBuf);
            writer.StartObject();
            writer.Key("type");
            writer.Uint(156);
            writer.Key("SessionId");
            writer.String(p_comm->session_id().c_str(), p_comm->session_id().length());
            writer.Key("sIp");
            writer.String(p_comm->src_ip().c_str(), p_comm->src_ip().length());
            writer.Key("dIp");
            writer.String(p_comm->dst_ip().c_str(),p_comm->dst_ip().length());
            writer.Key("sPort");
            writer.Uint(p_comm->src_port());
            writer.Key("dPort");
            writer.Uint(p_comm->dst_port());
            writer.Key("StartTime");
            writer.Uint(p_comm->begin_time());
            writer.Key("CH_Ciphersuit");
            writer.String(p_ssl->ssl_hello_c_ciphersuit().c_str(),p_ssl->ssl_hello_c_ciphersuit().length());
            writer.Key("CH_ServerName");
            writer.String(p_ssl->ssl_hello_c_servername().c_str(),p_ssl->ssl_hello_c_servername().length());
            writer.Key("CH_ALPN");
                writer.StartArray();
                for(int idx = 0; idx < p->j_ssl_hello_c_alpn.size(); idx ++)
                {
                    writer.String(p->j_ssl_hello_c_alpn[idx].asString().c_str());
                }
                writer.EndArray();
            writer.Key("cSSLFinger");
            writer.String(std::to_string(p_ssl->ssl_c_finger()).c_str());
            writer.Key("sSSLFinger");
            writer.String(std::to_string(p_ssl->ssl_s_finger()).c_str());
            writer.Key("sCertHash");
                writer.StartArray();
                if(p_session->session_basic.Server == PACKETFROMCLIENT)
                {
                    for(int idx = 0; idx < p->j_ssl_cert_hash[1].size(); idx ++)
                    {
                        writer.String(p->j_ssl_cert_hash[1][idx].asString().c_str());
                    }
                }
                else if(p_session->session_basic.Server == PACKETFROMSERVER)
                {
                    for(int idx = 0; idx < p->j_ssl_cert_hash[0].size(); idx ++)
                    {
                        writer.String(p->j_ssl_cert_hash[0][idx].asString().c_str());
                    }
                }
                writer.EndArray();
            writer.Key("sNewSessionTicket_LifeTime");
            writer.Uint(p_ssl->ssl_s_newsessionticket_lifetime());
            writer.EndObject();
            p_json_msg->copy_buf((uint8_t *)strBuf.GetString(), strBuf.GetSize());
        }
    }
    p_ssl_session->b_send_over = true;
    return;
}

void ssl_plugin::potocol_data_handle(session_pub* p_session,c_packet * p_packet)  //发送
{
    if(p_session==NULL)
    {
        return;
    }
    
    proto_parse_session* p_pp_session = (proto_parse_session * )( p_session -> p_sp_session);
    ssl_session * p_ssl_session = (ssl_session *)p_pp_session->expansion_data;
    if(p_ssl_session->b_parse_over[0] && p_ssl_session->b_parse_over[1])
    {
        ssl_msg_send(p_session);
    }
}

bool ssl_plugin::time_out(session_pub * p_session, uint32_t check_time)
{
    if (p_session)
    {
        return true;
    }
    return true;
}

void ssl_plugin::resources_recovery(session_pub * p_session)
{
    proto_parse_session *p_pp_session = (proto_parse_session*)(p_session->p_sp_session);
    ssl_session * p_ssl_session = (ssl_session *)p_pp_session->expansion_data;

    if(p_ssl_session->b_cacheing[0] || p_ssl_session->b_cacheing[1])
    {
        p_pp_session -> p_handle -> potocol_parse_handle(p_session ,NULL);
        p_pp_session -> p_handle -> potocol_data_handle( p_session  , NULL );
    }
    if(0 == p_ssl_session->has_unknown_pkt)
    {
        ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_SSL_NO_TCPSEG);
    }
    if (p_ssl_session->p_ssl_message != NULL)
    {
        ssl_msg_send(p_session);
        delete p_ssl_session->p_ssl_message;
        p_ssl_session->p_ssl_message = NULL;
    }
    return;
}

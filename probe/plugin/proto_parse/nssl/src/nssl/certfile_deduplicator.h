#ifndef __CERTFILE_DEDUPLICATOR_H__
#define __CERTFILE_DEDUPLICATOR_H__

#include "DataStructure/TemplateMatch.h"
#include "DataStructure/SequenceList.h"


typedef unsigned char sha1key[20];

typedef struct
{
    sha1key key;
    unsigned long long value;
} STR_SHA1_NODE;

class STR_SHA1_KEY_COMPARE
{
public:
    int operator()( const STR_SHA1_NODE &IN_A,const STR_SHA1_NODE &IN_B )
    {
        return memcmp(IN_A.key, IN_B.key, sizeof(sha1key));
    }
};

class CCertfileDeduplicator
{
public:
    CCertfileDeduplicator(DWORD IN_MaxNodeNum);
    ~CCertfileDeduplicator();
    unsigned long long JudgeValue(unsigned char *pkey, unsigned int clock);
    DWORD isConstructOk()
    {
        return isOk;
    }
private:
    CTemplateMatch<STR_SHA1_NODE, STR_SHA1_KEY_COMPARE> m_Judge;
    CArrayBasic<STR_SHA1_NODE> m_Array;
    CSequenceList m_Sequence;
    DWORD MaxNodeNum;
    DWORD HashSize;
    DWORD isOk;
};


#endif
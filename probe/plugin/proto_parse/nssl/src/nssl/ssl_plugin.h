// Last Update:2019-06-01 11:28:51
/**
 * @file ssl_str.h
 * @brief 
 * <AUTHOR>
 * @version 0.0.00
 * @date 2019-03-05
 */

#ifndef NSS_PLUGIN_H
#define NSS_PLUGIN_H
#include <stdio.h>
#include <iomanip>
#include <sstream>
#include <xml_parse.h>
#include <c_ip.h>
#include <map>
#include <TH_engine_interface.h>
#include <proto_parse_session.h>
#include "ssl_str.h"
#include <c_hash.h>
#include "certfile_deduplicator.h"
using namespace std;

extern "C" {
    int get_plugin_id();
    session_pasre_base * attach();
};

#include "dnumstr.h"
#define PROTOCOL_SSL                    678	 //SSL

class ssl_plugin : public session_pasre_base{
    public:
        ssl_plugin();
        ~ssl_plugin();
        virtual void reload();
        virtual bool potocol_init(session_pub* p_sess, c_packet* p_pack);
        virtual bool potocol_sign_judge(session_pub* p_sess, c_packet* p_pack);
        virtual bool potocol_parse_handle(session_pub* p_sess,c_packet * p_packet);
        virtual void potocol_data_handle(session_pub* p_sess,c_packet * p_packet);
        virtual bool time_out(session_pub* p_sess,uint32_t check_time);
        virtual void resources_recovery(session_pub* p_sess);
    private:
        string bin2hex(const char *p_data, uint32_t len);
        string bin2string(const char *p_data, uint32_t len);
        void init_ssl_session(ssl_session * p_ssl_session);
        bool data_is_whole(ssl_session * p_ssl_session);
        bool pkt_is_whole(c_packet * p_packet);
        bool level_1_data_parse(char* p_data, uint32_t data_len, ssl_session* p_ssl_session, uint8_t Directory);
        void level_2_handshake_protocol_parse(char* p_data, uint16_t data_len, ssl_session* p_ssl_session, uint16_t version, uint8_t Directory);
        void level_3_client_hello_parse(char* p_data, uint16_t data_len, ssl_session* p_ssl_session);
        void level_3_server_hello_parse(char* p_data, uint16_t data_len, ssl_session* p_ssl_session);
        void level_3_new_session_ticket_parse(char* p_data, uint16_t data_len, ssl_session* p_ssl_session);
        void level_3_client_key_exchange_parse(char* p_data, uint16_t data_len, ssl_session* p_ssl_session);
        void level_3_server_key_exchange_parse(char* p_data, uint16_t data_len, ssl_session* p_ssl_session);
        void level_3_certificate_parse(char* p_data, uint16_t data_len, ssl_session* p_ssl_session, uint8_t Directory);
        string write_certificate_file(char* p_data,std::string str_key, uint32_t data_len, ssl_session* p_ssl_session);
        void level_4_extention_parse(char* p_data, uint16_t data_len, ssl_session * p_ssl_session, int c_or_s);
        void level_5_server_name_parse(char* p_data, uint16_t data_len, ssl_session * p_ssl_session, int c_or_s);
        void level_5_status_request_parse(char* p_data, uint16_t data_len, ssl_session * p_ssl_session, int c_or_s);
        void level_5_supported_groups_parse(char* p_data, uint16_t data_len, ssl_session * p_ssl_session, int c_or_s);
        void level_5_ec_point_formats_parse(char* p_data, uint16_t data_len, ssl_session * p_ssl_session, int c_or_s);
        void level_5_signature_algorithms_parse(char* p_data, uint16_t data_len, ssl_session * p_ssl_session, int c_or_s);
        void level_5_alpn_parse(char* p_data, uint16_t data_len, ssl_session * p_ssl_session, int c_or_s);
        void level_5_session_ticket_parse(char* p_data, uint16_t data_len, ssl_session * p_ssl_session, int c_or_s);
        void ssl_msg_send(session_pub* p_session);
        hash_t * backList_hash ;
        hash_t * trustList_hash ;
        void backList_parse();
        void trustList_parse();
        class back_cert_do{
            public:
            back_cert_do()
            {
                do_ssl_pb = 1;
                do_session_pb = 1;
            }
            uint16_t do_ssl_pb ; // ssl 日志 是否留存
            uint16_t do_session_pb ; // 流量日志是否留存
        } ;
        back_cert_do back ; // 黑名单处理方式 
        back_cert_do white ; // 白名单处理方式
        session_pub * p_s_session ;
        char *cerfile_dir;
        void *aes_hash_ctx;
        CCertfileDeduplicator *pDeduplicator;
};
#endif  /*NSS_PLUGIN_H*/

#ifndef __TLS_CIPHER_SUITE_H__
#define __TLS_CIPHER_SUITE_H__

#include <sstream>
#include <fstream>
#include "json/reader.h"
#include "json/value.h"
#include "json/writer.h"
using namespace std;

class tls_cipher_suites
{
public:
    static tls_cipher_suites *getInstance()
    {
        static tls_cipher_suites s_instance;
        return &s_instance;
    }
    uint8_t get_label(uint16_t label)
    {
        uint8_t get = 1;
        uint16_t byte_idx = label / 8;
        uint16_t bit_idx = label % 8;
        if(data[byte_idx] & (get << bit_idx))
        {
            if(data_des[byte_idx] & (get << bit_idx))
            {
                return 1;
            }
            else if(data_rc4[byte_idx] & (get << bit_idx))
            {
                return 2;
            }
            return 255;
        }
        return 0;
    }

private:
    int set_label(uint16_t label)
    {
        uint8_t set = 1;
        uint16_t byte_idx = label / 8;
        uint16_t bit_idx = label % 8;
        data[byte_idx] |= (set << bit_idx);
        return 0;
    }
    int set_label(uint16_t label, uint8_t *p_data)
    {
        uint8_t set = 1;
        uint16_t byte_idx = label / 8;
        uint16_t bit_idx = label % 8;
        p_data[byte_idx] |= (set << bit_idx);
        return 0;
    }

    tls_cipher_suites() {
        memset(data, 0, 8192);
        memset(data_des, 0, 8192);
        memset(data_rc4, 0, 8192);
        string str;
        ifstream fin;
        string db_path = string(getenv("THE_DB_PATH")) + "/tls_cipher_suites.json";
        fin.open(db_path, ios::in);
        stringstream buf;
        buf << fin.rdbuf(); 
        str = buf.str();
        cout << str << endl;
        fin.close();

        Json::Reader reader;
        Json::Value rule_json;
        uint32_t suite_num;
        uint32_t suite;

        if (false != reader.parse(str, rule_json))
        {
            if(rule_json.isNull() == false)
            {
                suite_num = rule_json.size();
                for(uint32_t i = 0; i < suite_num; i ++)
                {
                    string suite_str = rule_json[i]["suite"].asString();
                    sscanf(suite_str.c_str(), "0x%x", &suite);
                    set_label(htons((uint16_t)suite));
                    string suite_name = rule_json[i]["name"].asString();
                    if(string::npos != suite_name.find("_WITH_DES"))
                    {
                        set_label(htons((uint16_t)suite), data_des);
                    }
                    else if(string::npos != suite_name.find("_WITH_RC4"))
                    {
                        set_label(htons((uint16_t)suite), data_rc4);
                    }
                }
            }
        }
    }
    uint8_t data[8192];
    uint8_t data_des[8192];
    uint8_t data_rc4[8192];
};

#endif
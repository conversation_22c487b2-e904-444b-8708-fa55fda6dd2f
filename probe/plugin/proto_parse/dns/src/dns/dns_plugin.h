// Last Update:2019-06-24 18:35:08
/**
 * @file dns_plugin.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2015-04-14
 */

#ifndef DNS_PLUGIN_H
#define DNS_PLUGIN_H
#include <stdio.h>
#include <iomanip>
#include <sstream>
#include <xml_parse.h>
#include <c_ip.h>
#include <json/json.h>
#include "dns_str.h"
#include <map>
#include <TH_engine_interface.h>
#include <proto_parse_session.h>
using namespace std;

extern "C" {
    int get_plugin_id();
    session_pasre_base * attach();
};

#include "dnumstr.h"

class dns_plugin :public session_pasre_base{
    public:
        dns_plugin();
        ~dns_plugin();
        virtual void reload();
        virtual bool potocol_init(session_pub* p_sess, c_packet* p_pack);
        virtual bool potocol_sign_judge(session_pub* p_sess, c_packet* p_pack);
        virtual bool potocol_parse_handle(session_pub* p_sess,c_packet * p_packet);
        virtual void potocol_data_handle(session_pub* p_sess,c_packet * p_packet);
        virtual bool time_out(session_pub* p_sess,uint32_t check_time);
        virtual void resources_recovery(session_pub* p_sess);
    private:
        void init_dns_session(dns_session * p_dns_session);
        void  dns_session_reset(session_pub * p_session);

        int dns_url_parse_string(dns_session *p_dns_session, uint8_t *p_start, string &domain, int depth);
        
        int dns_quest_parse(session_pub * p_session,dns_session *p_dns_session);
        int dns_answers_parse(session_pub * p_session,dns_session *p_dns_session);
        int dns_authority_parse(session_pub * p_session,dns_session *p_dns_session);
        int dns_addtional_parse(session_pub * p_session,dns_session *p_dns_session);
        
        string get_que_json_string(c_quest* p_quest, int quest_num);
        string get_ans_json_string(c_answers* p_c_answers, int answer_num);
        string bin2hex(const char *p_data, uint32_t len);
        string bin2string(const char *p_data, uint32_t len);

        //void softether_data_handle(session_pub* p_session);
        uint8_t known_resp_type[65536];
};
#endif  /*dns_PLUGIN_H*/

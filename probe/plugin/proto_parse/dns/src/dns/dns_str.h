// Last Update:2019-06-24 13:55:18
/**
 * @file dns_str.h
 * @brief DNS Session 定义
 * <AUTHOR>
 * @version 0.1.00
 * @date 2015-04-14
 */

#ifndef DNS_STR_H
#define DNS_STR_H

#include <session_pub.h>

#include <stdint.h>

#include <string>
#include <list>


#define  DNS_ANSWERS_TYPE_CNAME 0x0005
#define  DNS_ANSWERS_TYPE_A     0x0001
#define  DNS_ANSWERS_TYPE_AAAA  0x001c
#define  DNS_ANSWERS_TYPE_PTR   0x000c
#define  DNS_ANSWERS_TYPE_NS    0x0002
#define  DNS_ANSWERS_TYPE_OPT   0x0029

using namespace std;

typedef struct {
    uint16_t transanction_id;
    uint16_t flags;
    uint16_t questions;
    uint16_t answer_rrs;
    uint16_t authority_rrs;
    uint16_t addtional_rrs;
} dns_head;

class c_quest {
    public:
        c_quest(){
            qry_name ="";
            qry_type = 0;
            qry_class = 0;
        };
        string qry_name;
        uint16_t qry_type;
        uint16_t qry_class;
};

class c_answers {
    public:
        c_answers(){
            resp_name = "";
            resp_type = 0;
            resp_class = 0 ;
            resp_ttl = 0;
            resp_len = 0;
            resp_data = "";
        };
        string resp_name;
        uint16_t resp_type;
        uint16_t resp_class;
        uint32_t resp_ttl;
        uint16_t resp_len;
        string   resp_data;
};

class dns_session
{
    public:
        uint8_t  *p_start;
        uint8_t  *p_end;
        uint8_t  *p_cur;

        c_quest *p_quest;
        c_answers *p_answers;
        c_answers *p_authoritys;
        c_answers *p_addtionals;

        uint32_t ttl_min;
        uint16_t transanction_id; //请求个数 
        uint16_t flags; //请求个数 
        uint16_t quest_num; //请求个数 
        uint16_t answer_num; //应答个数 
        uint16_t authority_num; // 验证信息
        uint16_t addtional_num; // 附加信息
};

#endif  /*DNS_STR_H*/

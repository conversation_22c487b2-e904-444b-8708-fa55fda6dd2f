// Last Update:2019-09-03 11:10:49
/**
 * @file dns_plugin.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2015-04-14
 */
#include "dns_plugin.h"
#include <commit_tools.h>
//#include "config_text.h"
#include <sys/stat.h>
#include <sys/time.h>
#define DNSDEFINEPORT 53
#include "rapidjson/stringbuffer.h"
#include "rapidjson/writer.h"
extern "C" {
    int get_plugin_id()
    {
        return 10071;
    }
    session_pasre_base * attach( )
    {
        return new  dns_plugin();
    }
}

/// 输出yyyy-mm-dd-hh-mm-ss 格式的日期信息

dns_plugin::dns_plugin()
{
    //wireshark 3.6.6
    memset(known_resp_type, 0, 65536);
    for(int i = 1; i <= 52; i ++) {known_resp_type[i] = 1;};
    for(int i = 55; i <= 65; i ++) {known_resp_type[i] = 1;};
    for(int i = 99; i <= 109; i ++) {known_resp_type[i] = 1;};
    for(int i = 249; i <= 257; i ++) {known_resp_type[i] = 1;};
    for(int i = 32768; i <= 32769; i ++) {known_resp_type[i] = 1;};
    for(int i = 65281; i <= 65282; i ++) {known_resp_type[i] = 1;};
    for(int i = 65422; i <= 65422; i ++) {known_resp_type[i] = 1;};

    reload();
}
dns_plugin::~dns_plugin()
{
}
void dns_plugin::reload()
{
}

void dns_plugin::init_dns_session(dns_session * p_dns_session)
{
    p_dns_session->p_start = NULL;
    p_dns_session->p_end = NULL;
    p_dns_session->p_cur = NULL;

    p_dns_session->p_quest = NULL;
    p_dns_session->p_answers = NULL;
    p_dns_session->p_authoritys = NULL;
    p_dns_session->p_addtionals = NULL;
    
    p_dns_session->ttl_min = 0xffffffff;
    p_dns_session->transanction_id = 0;
    p_dns_session->flags = 0;
    p_dns_session->quest_num = 0;
    p_dns_session->answer_num = 0;
    p_dns_session->authority_num = 0;
    p_dns_session->addtional_num = 0;
}

bool  dns_plugin::potocol_init(session_pub * p_session, c_packet * p_packet)
{
    if(p_session==NULL || p_packet==NULL)
    {
        return false;
    }
    proto_parse_session *p_pp_session = (proto_parse_session * )( p_session -> p_sp_session) ;
    dns_session * p_dns_session = (dns_session *)p_pp_session->expansion_data;
    init_dns_session(p_dns_session);
    return true ;
}

bool dns_plugin:: potocol_sign_judge(session_pub * p_session,c_packet * p_packet)
{
    if(p_session==NULL || p_packet==NULL)
    {
        return false;
    }
    proto_parse_session *p_pp_session = (proto_parse_session * )( p_session -> p_sp_session) ;
    dns_session * p_dns_session = (dns_session *)p_pp_session->expansion_data;
    dns_head *p_dns_head = NULL;
    uint16_t flags = 0;

    char * p_data =(char *)p_packet ->app_buf; 
    if(p_data != NULL)
    {
        if((0 == p_packet->Directory && PACKETFROMCLIENT == p_session->session_basic.Server) || (1 == p_packet->Directory && PACKETFROMSERVER == p_session->session_basic.Server))
        {
            p_session->app_req_num ++;
        }
        else if((0 == p_packet->Directory && PACKETFROMSERVER == p_session->session_basic.Server) || (1 == p_packet->Directory && PACKETFROMCLIENT == p_session->session_basic.Server))
        {
            p_session->app_resp_num ++;
        }
    }
    if(p_packet->src_port == DNSDEFINEPORT && p_data!=NULL && p_packet -> app_len >= 12)
    {
        p_dns_head = (dns_head *)p_data;
        flags = (uint16_t)ntohs(p_dns_head->flags);
        if(flags & (1 << 9))            //.... ..1. .... .... = Truncated: Message is truncated
        {
            return false;
        }
        else if(flags & (1 << 15))      //1... .... .... .... = Response: Message is a response
        {
            return true;
        }
    }
    return false;
}

bool dns_plugin::potocol_parse_handle(session_pub * p_session,c_packet *p_packet)
{
    if(p_session == NULL)
    {
        return false;
    }
    if(p_packet == NULL) 
    {
        return false;
    }

    proto_parse_session *p_pp_session = (proto_parse_session * )( p_session -> p_sp_session) ;
    dns_session * p_dns_session = (dns_session *)p_pp_session->expansion_data;
    dns_session_reset(p_session);
    int ret = 0;

    p_dns_session->p_start = p_packet ->app_buf;
    p_dns_session->p_end = p_dns_session->p_start + p_packet -> app_len;
    p_dns_session->p_cur = p_dns_session->p_start;
    
    dns_head *p_dns_head = NULL;
    // 连接 去除
    if(p_dns_session->p_cur + sizeof(dns_head) > p_dns_session->p_end)
    {
        dns_session_reset(p_session);
        return false;
    }

    p_dns_head = (dns_head * )p_dns_session->p_cur;
    
    p_dns_session->transanction_id  = (uint16_t)ntohs(p_dns_head->transanction_id);
    p_dns_session->flags  = (uint16_t)ntohs(p_dns_head->flags);
    p_dns_session->quest_num  = (uint16_t)ntohs(p_dns_head->questions);
    p_dns_session->answer_num = (uint16_t)ntohs(p_dns_head->answer_rrs);
    p_dns_session->authority_num = (uint16_t)ntohs(p_dns_head->authority_rrs);
    p_dns_session->addtional_num = (uint16_t)ntohs(p_dns_head->addtional_rrs);
    
    if(3 == (p_dns_session->flags & 0xf))         //.... .... .... 0011 = Reply code: No such name (3)
    {
        ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_DNS_FAIL);
    }
    else if(1 == p_dns_session->answer_num)
    {
        ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_DNS_ONE_RECORD);
    }
    if(0 == p_dns_session->quest_num)
    {
        dns_session_reset(p_session);
        return false;
    }

    p_dns_session->p_cur += sizeof(dns_head);

    ret = dns_quest_parse(p_session, p_dns_session);
    if(0 != ret)
    {
        dns_session_reset(p_session);
        return false;
    }
    if(p_session->domain_judge_cb(p_session->thread_id,p_dns_session->p_quest[0].qry_name.c_str()))
    {
        ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_DNS_DOMAIN_T1000);
    }
    if(p_dns_session->answer_num)
    {
        ret = dns_answers_parse(p_session, p_dns_session);
        if(0 != ret)
        {
            dns_session_reset(p_session);
            return false;
        }
        if(p_dns_session->ttl_min < 60)
        {
            ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_DNS_TTL_LT60);
        }
        else if(p_dns_session->ttl_min < 600)
        {
            ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_DNS_TTL_LT600);
        }
        else if(p_dns_session->ttl_min < 3600)
        {
            ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_DNS_TTL_LT3600);
        }
    }
    if(p_dns_session->authority_num)
    {
        ret = dns_authority_parse(p_session, p_dns_session);
        if(0 != ret)
        {
            dns_session_reset(p_session);
            return false;
        }
    }
    if(p_dns_session->addtional_num)
    {
        ret = dns_addtional_parse(p_session, p_dns_session);
        if(0 != ret)
        {
            dns_session_reset(p_session);
            return false;
        }
    }
    if(p_dns_session->p_cur < p_dns_session->p_end)
    {
        ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_DNS_TAIL_DATA);
    }
    return true;
}

string dns_plugin::get_que_json_string(c_quest* p_quest, int quest_num)
{
    Json::Value FinArr;
    FinArr.resize(0);
    Json::FastWriter fast_writer;
    for(int i = 0; i < quest_num; i++)
    {
        Json::Value jsonl;
        jsonl["name"] = p_quest[i].qry_name;
        jsonl["type"] = p_quest[i].qry_type;
        jsonl["class"] = p_quest[i].qry_class;
        FinArr.append(jsonl);
    }
    string ser_json = fast_writer.write(FinArr);
    ser_json.pop_back();
    return ser_json;
}

string dns_plugin::get_ans_json_string(c_answers* p_answers, int answer_num)
{
    
    Json::Value FinArr; 
    Json::FastWriter fast_writer;
    for(int i = 0; i < answer_num; i++)
    {
        Json::Value jsonl;
        jsonl["name"] = p_answers[i].resp_name ;
        jsonl["type"] = p_answers[i].resp_type;
        jsonl["class"] = p_answers[i].resp_class;
        jsonl["ttl"] = p_answers[i].resp_ttl;
        jsonl["data_len"] = p_answers[i].resp_len;
        jsonl["value"] = p_answers[i].resp_data;
        FinArr.append(jsonl);
    }
    string ser_json = fast_writer.write(FinArr);
    ser_json.pop_back();
    return ser_json;
}

int dns_plugin::dns_quest_parse(session_pub *p_session, dns_session *p_dns_session)
{
    int ret = 0;
    if(NULL == p_session || NULL == p_dns_session)
    {
        return -1;
    }

    p_dns_session->p_quest = new c_quest[p_dns_session->quest_num];
    for(int i = 0;i < p_dns_session->quest_num  ;i++ )
    {
        ret = dns_url_parse_string(p_dns_session, p_dns_session->p_cur, p_dns_session->p_quest[i].qry_name, 0);
        if(0 != ret)
        {
            return ret;
        }
        //  des_type
        if(p_dns_session->p_cur + 2 > p_dns_session->p_end)
        {
            return -2;
        }
        p_dns_session->p_quest[i].qry_type = ntohs(*(uint16_t*)p_dns_session->p_cur);
        p_dns_session->p_cur += 2;

        //    dns_quest_class
        if(p_dns_session->p_cur + 2 > p_dns_session->p_end)
        {
            return -2;
        }
        p_dns_session->p_quest[i].qry_class = ntohs(*(uint16_t*)p_dns_session->p_cur);
        p_dns_session->p_cur += 2;
    }
    return 0;
}

// p_start 域名的首字符地址或指针的地址
// domain 解析结果
// depth 递归层级
int dns_plugin::dns_url_parse_string(dns_session *p_dns_session, uint8_t *p_start, string &domain, int depth)
{
    int part_id = 0, ret = 0;
    uint8_t domain_len = 0, b_parse_end = 0, *p_tmp = p_start, *p_ptr_domain = NULL;
    uint16_t ptr_off = 0;
    string ptr_domain = "", part_domain = "";

    if(NULL == p_dns_session || NULL == p_start)
    {
        return -1;
    }
    if(p_start < (p_dns_session->p_start + 12) || p_start >= p_dns_session->p_end)
    {
        return -2;
    }
    if(depth > 10) //递归跳出
    {
        return -3;
    }

    while(p_tmp + 1 <= p_dns_session->p_end)
    {
        domain_len = *p_tmp;
        if(0 == domain_len)
        {
            p_tmp ++;
            b_parse_end = 1;
            break;
        }
        else if(domain_len >= 0xC0)
        {
            if(p_tmp + 2 > p_dns_session->p_end)
            {
                return -4;
            }
            ptr_off = ntohs(*(uint16_t*)p_tmp) & 0x3fff;
            p_ptr_domain = p_dns_session->p_start + ptr_off;
            if(ptr_off < 12 || (p_dns_session->p_start + ptr_off) >= p_dns_session->p_end)
            {
                return -5;
            }
            ret = dns_url_parse_string(p_dns_session, p_ptr_domain, ptr_domain, depth + 1);
            if(0 != ret)
            {
                return ret;
            }
            if(part_id > 0)
            {
                domain += ".";
            }
            domain += ptr_domain;
            part_id++;

            p_tmp += 2;
            b_parse_end = 1;
            break;
        }
        else
        {
            p_tmp ++;
            if(p_tmp + domain_len > p_dns_session->p_end)
            {
                return -6;
            }

            part_domain = string((char *)p_tmp,0,domain_len);
            if(part_id > 0)
            {
                domain += ".";
            }
            domain += part_domain;
            part_id++;

            p_tmp += domain_len;
        }
    }

    if(b_parse_end)
    {
        if(0 == depth)
        {
            p_dns_session->p_cur = p_tmp;
        }
        return 0;
    }

    return -7;
}

string dns_plugin::bin2hex(const char *p_data, uint32_t len) 
{
    string s = "";
    char formate_char[3];
    for (int i = 0; i < len; ++i)
    {
        sprintf(formate_char, "%02x", (unsigned char)p_data[i]);
        s.append(formate_char, 2);
    }
    return s;
}

string dns_plugin::bin2string(const char* p_data, uint32_t len) {
    // Check for null pointer
    if (p_data == nullptr) {
        return std::string();
    }
    
    // Check for valid length
    if (len == 0) {
        return std::string();
    }
    
    // Create string safely
    return std::string(p_data, len);
}

int dns_plugin::dns_answers_parse(session_pub *p_session, dns_session *p_dns_session)
{
    int ret = 0;
    uint32_t num_of_a = 0;
    uint8_t *p_expect = NULL;

    if(NULL == p_session || NULL == p_dns_session)
    {
        return -1;
    }
    p_dns_session->p_answers = new c_answers[p_dns_session->answer_num];
    
    for(int i = 0;i < p_dns_session->answer_num ; i++ )
    {
        ret = dns_url_parse_string(p_dns_session, p_dns_session->p_cur, p_dns_session->p_answers[i].resp_name, 0);
        if(0 != ret)
        {
            return ret;
        }
        if(p_dns_session->p_cur + 2 > p_dns_session->p_end)
        {
            return -2;
        }
        p_dns_session->p_answers[i].resp_type = ntohs(*(uint16_t*)p_dns_session->p_cur);
        p_dns_session->p_cur += 2;

        if(DNS_ANSWERS_TYPE_CNAME == p_dns_session->p_answers[i].resp_type)
        {
            ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_DNS_WITH_CNAME);
        }

        if(p_dns_session->p_cur + 2 > p_dns_session->p_end)
        {
            return -2;
        }
        p_dns_session->p_answers[i].resp_class = ntohs(*(uint16_t*)p_dns_session->p_cur);
        p_dns_session->p_cur += 2;

        if(p_dns_session->p_cur + 4 > p_dns_session->p_end)
        {
            return -2;
        }
        p_dns_session->p_answers[i].resp_ttl = ntohl(*(uint32_t*)p_dns_session->p_cur);
        p_dns_session->p_cur += 4;

        if(p_dns_session->p_answers[i].resp_ttl < p_dns_session->ttl_min)
        {
            p_dns_session->ttl_min = p_dns_session->p_answers[i].resp_ttl;
        }

        if(p_dns_session->p_cur + 2 > p_dns_session->p_end)
        {
            return -2;
        }
        p_dns_session->p_answers[i].resp_len = ntohs(*(uint16_t*)p_dns_session->p_cur);
        p_dns_session->p_cur += 2;

        p_expect = p_dns_session->p_cur + p_dns_session->p_answers[i].resp_len;
        if(p_expect > p_dns_session->p_end)
        {
            return -2;
        }
        
        if(DNS_ANSWERS_TYPE_CNAME == p_dns_session->p_answers[i].resp_type || 
            DNS_ANSWERS_TYPE_NS == p_dns_session->p_answers[i].resp_type || 
            DNS_ANSWERS_TYPE_PTR == p_dns_session->p_answers[i].resp_type)    //此时Value为域名
        {
            ret = dns_url_parse_string(p_dns_session, p_dns_session->p_cur, p_dns_session->p_answers[i].resp_data, 0);
            if(0 != ret)
            {
                return ret;
            }
        }
        else if (DNS_ANSWERS_TYPE_A == p_dns_session->p_answers[i].resp_type)//此时Value为IP
        {
            if(1 == num_of_a)
            {
                ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_DNS_MULTI_A);
            }
            if(4 == p_dns_session->p_answers[i].resp_len)
            {
                uint32_t i_ip = (*(uint32_t *)p_dns_session->p_cur);
                c_ip class_ip (i_ip);
                p_dns_session->p_answers[i].resp_data = class_ip.ip_str();
            }
            else
            {
                p_dns_session->p_answers[i].resp_data = bin2string((const char*)p_dns_session->p_cur, p_dns_session->p_answers[i].resp_len);
            }
            num_of_a ++;
            p_dns_session->p_cur += p_dns_session->p_answers[i].resp_len;
        }
        else if (DNS_ANSWERS_TYPE_AAAA == p_dns_session->p_answers[i].resp_type)//此时Value为IPv6
        {
            if(1 == num_of_a)
            {
                ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_DNS_MULTI_A);
            }
            if(16 == p_dns_session->p_answers[i].resp_len)
            {
                c_ip class_ipv6((struct in6_addr *)p_dns_session->p_cur);
                p_dns_session->p_answers[i].resp_data = class_ipv6.ip_str();
            }
            else
            {
                p_dns_session->p_answers[i].resp_data = bin2string((const char*)p_dns_session->p_cur, p_dns_session->p_answers[i].resp_len);
            }
            num_of_a ++;
            p_dns_session->p_cur += p_dns_session->p_answers[i].resp_len;
        }
        else //其他为二进制流
        {
            if(0 == known_resp_type[p_dns_session->p_answers[i].resp_type])
            {
                ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_DNS_UNKNOW_TYPE);
            }
            p_dns_session->p_answers[i].resp_data = bin2string((const char*)p_dns_session->p_cur, p_dns_session->p_answers[i].resp_len);
            p_dns_session->p_cur += p_dns_session->p_answers[i].resp_len;
        }
        if(p_expect != p_dns_session->p_cur)
        {
            return -4;
        }
    }
    return 0;
}


int dns_plugin::dns_authority_parse(session_pub *p_session, dns_session *p_dns_session)
{
    int ret = 0;
    uint8_t *p_expect = NULL;

    if(NULL == p_session || NULL == p_dns_session)
    {
        return -1;
    }
    p_dns_session->p_authoritys = new c_answers[p_dns_session->authority_num];

    for(int i = 0;i < p_dns_session->authority_num ; i++ )
    {
        ret = dns_url_parse_string(p_dns_session, p_dns_session->p_cur, p_dns_session->p_authoritys[i].resp_name, 0);
        if(0 != ret)
        {
            return ret;
        }
        if(p_dns_session->p_cur + 2 > p_dns_session->p_end)
        {
            return -2;
        }
        p_dns_session->p_authoritys[i].resp_type = ntohs(*(uint16_t*)p_dns_session->p_cur);
        p_dns_session->p_cur += 2;

        if(p_dns_session->p_cur + 2 > p_dns_session->p_end)
        {
            return -2;
        }
        p_dns_session->p_authoritys[i].resp_class = ntohs(*(uint16_t*)p_dns_session->p_cur);
        p_dns_session->p_cur += 2;

        if(p_dns_session->p_cur + 4 > p_dns_session->p_end)
        {
            return -2;
        }
        p_dns_session->p_authoritys[i].resp_ttl = ntohl(*(uint32_t*)p_dns_session->p_cur);
        p_dns_session->p_cur += 4;

        if(p_dns_session->p_cur + 2 > p_dns_session->p_end)
        {
            return -2;
        }
        p_dns_session->p_authoritys[i].resp_len = ntohs(*(uint16_t*)p_dns_session->p_cur);
        p_dns_session->p_cur += 2;

        p_expect = p_dns_session->p_cur + p_dns_session->p_authoritys[i].resp_len;
        if(p_expect > p_dns_session->p_end)
        {
            return -2;
        }
        
        if(DNS_ANSWERS_TYPE_CNAME == p_dns_session->p_authoritys[i].resp_type || 
            DNS_ANSWERS_TYPE_NS == p_dns_session->p_authoritys[i].resp_type || 
            DNS_ANSWERS_TYPE_PTR == p_dns_session->p_authoritys[i].resp_type)    //此时Value为域名
        {
            ret = dns_url_parse_string(p_dns_session, p_dns_session->p_cur, p_dns_session->p_authoritys[i].resp_data, 0);
            if(0 != ret)
            {
                return ret;
            }
        }
        else if (DNS_ANSWERS_TYPE_A == p_dns_session->p_authoritys[i].resp_type)//此时Value为IP
        {
            if(4 == p_dns_session->p_authoritys[i].resp_len)
            {
                uint32_t i_ip = (*(uint32_t *)p_dns_session->p_cur);
                c_ip class_ip (i_ip);
                p_dns_session->p_authoritys[i].resp_data = class_ip.ip_str();
            }
            else
            {
                p_dns_session->p_authoritys[i].resp_data = bin2string((const char*)p_dns_session->p_cur, p_dns_session->p_authoritys[i].resp_len);
            }
            p_dns_session->p_cur += p_dns_session->p_authoritys[i].resp_len;
        }
        else if (DNS_ANSWERS_TYPE_AAAA == p_dns_session->p_authoritys[i].resp_type)//此时Value为IPv6
        {
            if(16 == p_dns_session->p_authoritys[i].resp_len)
            {
                c_ip class_ipv6((struct in6_addr *)p_dns_session->p_cur);
                p_dns_session->p_authoritys[i].resp_data = class_ipv6.ip_str();
            }
            else
            {
                p_dns_session->p_authoritys[i].resp_data = bin2string((const char*)p_dns_session->p_cur, p_dns_session->p_authoritys[i].resp_len);
            }
            p_dns_session->p_cur += p_dns_session->p_authoritys[i].resp_len;
        }
        else //其他为二进制流
        {
            if(0 == known_resp_type[p_dns_session->p_authoritys[i].resp_type])
            {
                ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_DNS_UNKNOW_TYPE);
            }
            p_dns_session->p_authoritys[i].resp_data = bin2string((const char*)p_dns_session->p_cur, p_dns_session->p_authoritys[i].resp_len);
            p_dns_session->p_cur += p_dns_session->p_authoritys[i].resp_len;
        }
        if(p_expect != p_dns_session->p_cur)
        {
            return -4;
        }
    }
    return 0;
}

int dns_plugin::dns_addtional_parse(session_pub *p_session, dns_session *p_dns_session)
{
    int ret = 0;
    uint8_t *p_expect = NULL;

    if(NULL == p_session || NULL == p_dns_session)
    {
        return -1;
    }
    p_dns_session->p_addtionals = new c_answers[p_dns_session->addtional_num];

    for(int i = 0;i < p_dns_session->addtional_num ; i++ )
    {
        ret = dns_url_parse_string(p_dns_session, p_dns_session->p_cur, p_dns_session->p_addtionals[i].resp_name, 0);
        if(0 != ret)
        {
            return ret;
        }
        if(p_dns_session->p_cur + 2 > p_dns_session->p_end)
        {
            return -2;
        }
        p_dns_session->p_addtionals[i].resp_type = ntohs(*(uint16_t*)p_dns_session->p_cur);
        p_dns_session->p_cur += 2;

        if(p_dns_session->p_cur + 2 > p_dns_session->p_end)
        {
            return -2;
        }
        p_dns_session->p_addtionals[i].resp_class = ntohs(*(uint16_t*)p_dns_session->p_cur);
        p_dns_session->p_cur += 2;

        if(p_dns_session->p_cur + 4 > p_dns_session->p_end)
        {
            return -2;
        }
        p_dns_session->p_addtionals[i].resp_ttl = ntohl(*(uint32_t*)p_dns_session->p_cur);
        p_dns_session->p_cur += 4;

        if(p_dns_session->p_cur + 2 > p_dns_session->p_end)
        {
            return -2;
        }
        p_dns_session->p_addtionals[i].resp_len = ntohs(*(uint16_t*)p_dns_session->p_cur);
        p_dns_session->p_cur += 2;

        p_expect = p_dns_session->p_cur + p_dns_session->p_addtionals[i].resp_len;
        if(p_expect > p_dns_session->p_end)
        {
            return -2;
        }
        
        if(DNS_ANSWERS_TYPE_CNAME == p_dns_session->p_addtionals[i].resp_type || 
            DNS_ANSWERS_TYPE_NS == p_dns_session->p_addtionals[i].resp_type || 
            DNS_ANSWERS_TYPE_PTR == p_dns_session->p_addtionals[i].resp_type)    //此时Value为域名
        {
            ret = dns_url_parse_string(p_dns_session, p_dns_session->p_cur, p_dns_session->p_addtionals[i].resp_data, 0);
            if(0 != ret)
            {
                return ret;
            }
        }
        else if (DNS_ANSWERS_TYPE_A == p_dns_session->p_addtionals[i].resp_type)//此时Value为IP
        {
            if(4 == p_dns_session->p_addtionals[i].resp_len)
            {
                uint32_t i_ip = (*(uint32_t *)p_dns_session->p_cur);
                c_ip class_ip (i_ip);
                p_dns_session->p_addtionals[i].resp_data = class_ip.ip_str();
            }
            else
            {
                p_dns_session->p_addtionals[i].resp_data = bin2string((const char*)p_dns_session->p_cur, p_dns_session->p_addtionals[i].resp_len);
            }
            p_dns_session->p_cur += p_dns_session->p_addtionals[i].resp_len;
        }
        else if (DNS_ANSWERS_TYPE_AAAA == p_dns_session->p_addtionals[i].resp_type)//此时Value为IPv6
        {
            if(16 == p_dns_session->p_addtionals[i].resp_len)
            {
                c_ip class_ipv6((struct in6_addr *)p_dns_session->p_cur);
                p_dns_session->p_addtionals[i].resp_data = class_ipv6.ip_str();
            }
            else
            {
                p_dns_session->p_addtionals[i].resp_data = bin2string((const char*)p_dns_session->p_cur, p_dns_session->p_addtionals[i].resp_len);
            }
            p_dns_session->p_cur += p_dns_session->p_addtionals[i].resp_len;
        }
        else //其他为二进制流
        {
            if(0 == known_resp_type[p_dns_session->p_addtionals[i].resp_type])
            {
                ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_DNS_UNKNOW_TYPE);
            }
            p_dns_session->p_addtionals[i].resp_data = bin2string((const char*)p_dns_session->p_cur, p_dns_session->p_addtionals[i].resp_len);
            p_dns_session->p_cur += p_dns_session->p_addtionals[i].resp_len;
        }
        if(p_expect != p_dns_session->p_cur)
        {
            return -4;
        }
    }
    return 0;
}

void dns_plugin::potocol_data_handle(session_pub* p_session,c_packet * p_packet)
{
    if(NULL == p_session)
    {
        return;
    }
    string domain_ip = "";
    int ip_count = 0;
    session_pub_dns pub_dns_msg;
    session_pub_dns_answer answer_msg;

    proto_parse_session *p_pp_session = (proto_parse_session * )( p_session -> p_sp_session) ;
    dns_session * p_dns_session = (dns_session *)p_pp_session->expansion_data;

    if(p_dns_session == NULL)
    {
        return;
    }

    if(should_log)
    {
        JKNmsg *  msg = p_session -> p_value -> get() ;
        if (NULL == msg)
        {
            dns_session_reset(p_session);
            return;
        }
        CJsonMsg *p_json_msg = p_session->p_value -> jget();
        msg ->set_type(4);
        dns_msg * p_dns =  msg->mutable_dns();
        Comm_msg* p_comm =  p_dns -> mutable_comm_msg();
        // 公共 消息 
        commsg_fill(p_session , p_comm , "1500001", p_th_tools);
        //dns_msg
        p_dns -> set_dns_id(p_dns_session->transanction_id); 
        p_dns -> set_dns_flags(p_dns_session->flags); 
        p_dns -> set_dns_que(p_dns_session->quest_num); 
        p_dns -> set_dns_ans(p_dns_session->answer_num); 
        p_dns -> set_dns_auth(p_dns_session->authority_num); 
        p_dns -> set_dns_add(p_dns_session->addtional_num); 
        string str_query = get_que_json_string(p_dns_session->p_quest, p_dns_session->quest_num);
        p_dns -> set_dns_query(str_query);
        string str_answer = p_dns_session->p_answers == NULL ? "" : get_ans_json_string(p_dns_session->p_answers, p_dns_session->answer_num);
        p_dns -> set_dns_answer(str_answer);
        
        domain_ip="";
        ip_count = 0;
        if(p_dns_session->p_answers)
        {
            for(int i = 0; i < p_dns_session->answer_num; i ++)
            {
                answer_msg.name = p_dns_session->p_answers[i].resp_name;
                answer_msg.value = p_dns_session->p_answers[i].resp_data;
                pub_dns_msg.answer.push_back(answer_msg);

                if(DNS_ANSWERS_TYPE_A == p_dns_session->p_answers[i].resp_type || DNS_ANSWERS_TYPE_AAAA == p_dns_session->p_answers[i].resp_type)
                {
                    if(ip_count)
                    {
                        domain_ip +="|";
                    }
                    domain_ip += p_dns_session->p_answers[i].resp_data;
                    ip_count ++;
                }
            }
        }
        p_dns ->set_dns_domain_ip(domain_ip);
        p_dns -> set_dns_domain(p_dns_session->p_quest[0].qry_name);
        pub_dns_msg.domain_ip = domain_ip;
        pub_dns_msg.domain = p_dns_session->p_quest[0].qry_name;
        session_pub_push_dns(p_session, &pub_dns_msg);

        if(p_json_msg)
        {
            rapidjson::StringBuffer strBuf;
            rapidjson::Writer<rapidjson::StringBuffer> writer(strBuf);
            writer.StartObject();
            writer.Key("type");
            writer.Uint(148);
            writer.Key("SessionId");
            writer.String(p_comm->session_id().c_str(), p_comm->session_id().length());
            writer.Key("sIp");
            writer.String(p_comm->src_ip().c_str(), p_comm->src_ip().length());
            writer.Key("dIp");
            writer.String(p_comm->dst_ip().c_str(),p_comm->dst_ip().length());
            writer.Key("sPort");
            writer.Uint(p_comm->src_port());
            writer.Key("dPort");
            writer.Uint(p_comm->dst_port());
            writer.Key("IPPro");
            writer.Uint(p_comm->ippro());
            writer.Key("StartTime");
            writer.Uint(p_comm->begin_time());
            writer.Key("Domain");
            writer.String(p_dns->dns_domain().c_str(),p_dns->dns_domain().length());
            writer.Key("DomainIp");
            writer.String(p_dns->dns_domain_ip().c_str(),p_dns->dns_domain_ip().length());
            writer.EndObject();
            p_json_msg->copy_buf((uint8_t *)strBuf.GetString(), strBuf.GetSize());
        }
    }
    dns_session_reset(p_session);
    return;
}


bool  dns_plugin::time_out(session_pub * p_session, uint32_t check_time)
{
    if(p_session)
        return true;

}
void  dns_plugin ::dns_session_reset(session_pub * p_session)
{
    if(p_session == NULL)
        return;

    proto_parse_session *p_pp_session = (proto_parse_session * )( p_session -> p_sp_session) ;
    dns_session *p_dns_session = (dns_session *)p_pp_session->expansion_data;

    if(p_dns_session->p_quest != NULL)
        delete [] p_dns_session->p_quest;
    p_dns_session->p_quest = NULL;

    if(p_dns_session ->p_answers != NULL)
        delete [] p_dns_session ->p_answers;
    p_dns_session ->p_answers = NULL;

    if(p_dns_session ->p_authoritys != NULL)
        delete [] p_dns_session ->p_authoritys;
    p_dns_session ->p_authoritys = NULL;

    if(p_dns_session ->p_addtionals != NULL)
        delete [] p_dns_session ->p_addtionals;
    p_dns_session ->p_addtionals = NULL;

    init_dns_session(p_dns_session);
}
void dns_plugin::resources_recovery(session_pub * p_session)
{
    dns_session_reset(p_session);
}


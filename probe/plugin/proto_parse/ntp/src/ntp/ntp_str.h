// Last Update:2019-03-05 10:06:00
/**
 * @file ssl_str.h
 * @brief
 * <AUTHOR>
 * @version 0.0.00
 * @date 2019-03-05
 */

#ifndef NTP_STR_H
#define NTP_STR_H

#include <list>
#include <packet.h>
#include <session_pub.h>
#include <stdint.h>
#include <string>

#define NTP_LI_MASK	0xC0
#define NTP_VN_MASK	0x38
#define NTP_MODE_MASK 7

#define NTP_MODE_RSV 0
#define NTP_MODE_SYMACT 1
#define NTP_MODE_SYMPAS 2
#define NTP_MODE_CLIENT 3
#define NTP_MODE_SERVER 4
#define NTP_MODE_BCAST 5
#define NTP_MODE_CTRL 6
#define NTP_MODE_PRIV 7

#define NTP_UNIX_EPOCH_OFFSET 2208988800
#define NS_1_NTP_TSFRAC 0.23283064365387 // NTP时间戳小数部分的单位为232皮秒，即0.232纳秒
using namespace std;

enum pkt_direction
{
    PKTFROMUNKNOWN = 0,
    PKTFROMCLIENT,
    PKTFROMSERVER,
};

enum msg_parsed_type {
    NTP_CLIENT_MSG,
    NTP_SERVER_MSG,
};

class ntp_ext_msg
{
  public:
    ntp_ext_msg(){
      field_type = 0;
      ext_value = "";
    }
    uint16_t field_type;
    string ext_value;
};

class ntp_client_data
{
  public:
    ntp_client_data()
    {
        version = 0;
        clock_stratum = 0;
        polling_interval_sec = 0;
        clock_precision = 0;
        root_delay = 0;
        root_dispersion = 0;
        reference_id = "";
        refer_ts_sec = 0;
        refer_ts_nsec = 0;
        origin_ts_sec = 0;
        origin_ts_nsec = 0;
        recv_ts_sec = 0;
        recv_ts_nsec = 0;
        xmit_ts_sec = 0;
        xmit_ts_nsec = 0;
    }
    uint8_t version;
    uint8_t clock_stratum;
    uint16_t polling_interval_sec;
    double clock_precision;
    double root_delay;
    double root_dispersion;
    string reference_id;
    uint32_t refer_ts_sec;
    uint32_t refer_ts_nsec;
    uint32_t origin_ts_sec;
    uint32_t origin_ts_nsec;
    uint32_t recv_ts_sec;
    uint32_t recv_ts_nsec;
    uint32_t xmit_ts_sec;
    uint32_t xmit_ts_nsec;
};
class ntp_server_data
{
  public:
    ntp_server_data()
    {
        version = 0;
        clock_stratum = 0;
        polling_interval_sec = 0;
        clock_precision = 0;
        root_delay = 0;
        root_dispersion = 0;
        reference_id = "";
        refer_ts_sec = 0;
        refer_ts_nsec = 0;
        origin_ts_sec = 0;
        origin_ts_nsec = 0;
        recv_ts_sec = 0;
        recv_ts_nsec = 0;
        xmit_ts_sec = 0;
        xmit_ts_nsec = 0;
    }
    uint8_t version;
    uint8_t clock_stratum;
    uint16_t polling_interval_sec;
    double clock_precision;
    double root_delay;
    double root_dispersion;
    string reference_id;
    uint32_t refer_ts_sec;
    uint32_t refer_ts_nsec;
    uint32_t origin_ts_sec;
    uint32_t origin_ts_nsec;
    uint32_t recv_ts_sec;
    uint32_t recv_ts_nsec;
    uint32_t xmit_ts_sec;
    uint32_t xmit_ts_nsec;
};
class ntp_session
{
  public:
    ntp_session()
    {
        thread_id = 0;
        p_data = NULL;
        data_len = 0;
        pktdirec = PKTFROMUNKNOWN;
        p_ntp_client_data = NULL;
        p_ntp_server_data = NULL;
        memset(msg_parse_done, 0, sizeof(msg_parse_done));
        msg_send_done = false;
    }
    uint32_t thread_id;
    uint8_t *p_data;
    uint32_t data_len;
    enum pkt_direction pktdirec;
    ntp_client_data *p_ntp_client_data;
    ntp_server_data *p_ntp_server_data;
    ntp_ext_msg *p_ntp_ext_msg;
    bool msg_parse_done[2];
    bool msg_send_done;
};
#endif /*NTP_STR_H*/

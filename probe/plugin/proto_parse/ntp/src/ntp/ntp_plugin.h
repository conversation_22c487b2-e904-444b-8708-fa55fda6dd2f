// Last Update:2019-03-05 10:06:00
/**
 * @file ssl_str.h
 * @brief 
 * <AUTHOR>
 * @version 0.0.00
 * @date 2019-03-05
 */

#ifndef NSS_PLUGIN_H
#define NSS_PLUGIN_H
#include <stdio.h>
#include <iomanip>
#include <sstream>
#include <xml_parse.h>
#include <c_ip.h>
#include <map>
#include <TH_engine_interface.h>
#include <proto_parse_session.h>
#include "ntp_str.h"

using namespace std;

extern "C" {
    int get_plugin_id();
    session_pasre_base * attach();
};

#include "dnumstr.h"

class ntp_plugin : public session_pasre_base{
    public:
        ntp_plugin();
        ~ntp_plugin();
        virtual void reload();
        virtual bool potocol_init(session_pub* p_sess, c_packet* p_pack);
        virtual bool potocol_sign_judge(session_pub* p_sess, c_packet* p_pack);
        virtual bool potocol_parse_handle(session_pub* p_sess,c_packet * p_packet);
        virtual void potocol_data_handle(session_pub* p_sess,c_packet * p_packet);
        virtual bool time_out(session_pub* p_sess,uint32_t check_time);
        virtual void resources_recovery(session_pub* p_sess);
    private:
        string bin2hex(const char *p_data, uint32_t len);
        string bin2string(const char *p_data, uint32_t len);
        string bin2ascii(const char *p_data, uint32_t len);
        string ts2utcstr(const time_t ts, const time_t ts_frac);
        uint32_t ntp_to_nstime(uint32_t ntp_time);
        int get_ipv4_string(uint32_t ipv4_data, string &ipv4_string);
        int get_ipv6_string(uint32_t ipv6_data[4], string &ipv6_string);
        void ntp_msg_send(session_pub* p_session);
        void init_ntp_session(ntp_session* p_ntp_session);
        void reset_ntp_session(ntp_session* p_ntp_session);
        bool parse_ntp_data(ntp_session* p_ntp_session);
};
#endif  /*ntp_PLUGIN_H*/

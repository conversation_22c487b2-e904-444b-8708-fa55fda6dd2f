// Last Update:2019-03-07 14:17:24
/**
 * @file esp_str.h
 * @brief
 * <AUTHOR>
 * @version 0.0.00
 * @date 2019-03-05
 */

#include "ntp_plugin.h"
#include <commit_tools.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <cmath>

extern "C" {
int get_plugin_id() {
    return 10413;
}
session_pasre_base *attach() {
    return new ntp_plugin();
}
}

ntp_plugin::ntp_plugin() {
    reload();
}

ntp_plugin::~ntp_plugin() {
    ;
}

void ntp_plugin::reload() {
    ;
}

string ntp_plugin::bin2hex(const char *p_data, uint32_t len) {
    if (p_data == NULL || len < 1) {
        return "";
    }
    string s = "";
    char formate_char[3];
    for (uint32_t i = 0; i < len; ++i) {
        sprintf(formate_char, "%02x", (unsigned char)p_data[i]);
        s.append(formate_char, 2);
    }
    return s;
}

string ntp_plugin::bin2string(const char* p_data, uint32_t len) {
    // Check for null pointer
    if (p_data == nullptr) {
        return std::string();
    }
    
    // Check for valid length
    if (len == 0) {
        return std::string();
    }
    
    // Create string safely
    return std::string(p_data, len);
}

string ntp_plugin::bin2ascii(const char *p_data, uint32_t len) {
    if (p_data == NULL || len < 1) {
        return "";
    }
    string s = "";
    char formate_char[3];
    for (uint32_t i = 0; i < len; ++i) {
        if (p_data[i] >= 32 && p_data[i] <= 126) { // printable characters
            sprintf(formate_char, "%c", (unsigned char)p_data[i]);
        } else {
            sprintf(formate_char, "%c", ' ');
        }
        s.append(formate_char, 1);
    }
    return s;
}

// 函数用于将时间戳转换为指定格式的字符串（UTC时间）
string ntp_plugin::ts2utcstr(const time_t ts, const time_t ts_frac) {
    char buffer[80];
    string result_str = "";
    string ts_frac_str = "";

    ts_frac_str = to_string((double)ts_frac * 0x1P-32F);
    ts_frac_str.erase(0, 1);

    if (strftime(buffer, sizeof(buffer), "%m/%d/%Y %H:%M:%S", gmtime(&ts)) > 0) {
        result_str = string(buffer) + ts_frac_str;
    }
    return result_str;
}

uint32_t ntp_plugin::ntp_to_nstime(uint32_t ntp_time){
    if (ntp_time > NTP_UNIX_EPOCH_OFFSET){
		return ntp_time - NTP_UNIX_EPOCH_OFFSET;
    }
	else{
		return ntp_time;
    }
}

int ntp_plugin::get_ipv4_string(uint32_t ipv4_data, string &ipv4_string) {

    char ip4str[INET_ADDRSTRLEN];

    struct sockaddr_in in_addr;
    in_addr.sin_family = AF_INET;
    in_addr.sin_addr.s_addr = ipv4_data;

    char str[INET_ADDRSTRLEN];
    if (inet_ntop(AF_INET, &in_addr.sin_addr, ip4str, INET_ADDRSTRLEN) != NULL) {
        ipv4_string.assign(ip4str);
        return 0;
    } else {
        return -1;
    }
}

int ntp_plugin::get_ipv6_string(uint32_t ipv6_data[4], string &ipv6_string) {

    char ip6str[INET6_ADDRSTRLEN];
    struct sockaddr_in6 sockaddr6;
    sockaddr6.sin6_family = AF_INET6;
    sockaddr6.sin6_addr.__in6_u.__u6_addr32[0] = ipv6_data[0];
    sockaddr6.sin6_addr.__in6_u.__u6_addr32[1] = ipv6_data[1];
    sockaddr6.sin6_addr.__in6_u.__u6_addr32[2] = ipv6_data[2];
    sockaddr6.sin6_addr.__in6_u.__u6_addr32[3] = ipv6_data[3];
    if (inet_ntop(AF_INET6, &sockaddr6.sin6_addr, ip6str, INET6_ADDRSTRLEN) != NULL) {
        ipv6_string.assign(ip6str);
        return 0;
    } else {
        return -1;
    }
}

void ntp_plugin::init_ntp_session(ntp_session *p_ntp_session) {
    memset(p_ntp_session, 0, sizeof(ntp_session));
    p_ntp_session->p_ntp_client_data = new ntp_client_data();
    p_ntp_session->p_ntp_server_data = new ntp_server_data();
}

void ntp_plugin::reset_ntp_session(ntp_session *p_ntp_session) {

    if (p_ntp_session->p_ntp_client_data) {
        delete p_ntp_session->p_ntp_client_data;
        p_ntp_session->p_ntp_client_data = NULL;
    }
    if (p_ntp_session->p_ntp_server_data) {
        delete p_ntp_session->p_ntp_server_data;
        p_ntp_session->p_ntp_server_data = NULL;
    }
}

bool ntp_plugin::parse_ntp_data(ntp_session *p_ntp_session) {

    uint8_t *pcur = p_ntp_session->p_data;
    uint8_t flags;
    uint8_t clock_stratum;
    int8_t polling_interval;
    int8_t clock_precision;
    uint32_t root_delay;
    uint32_t root_dispersion;
    uint32_t reference_id;
    string refer_id_str = "";
    string kiss_code_str = "";
    uint32_t refer_ts;
    uint32_t refer_ts_frac;
    uint32_t origin_ts;
    uint32_t origin_ts_frac;
    uint32_t recv_ts;
    uint32_t recv_ts_frac;
    uint32_t xmit_ts;
    uint32_t xmit_ts_frac;

    flags = *pcur;
    pcur++;

    if (!((flags & NTP_MODE_MASK) == NTP_MODE_CLIENT || (flags & NTP_MODE_MASK) == NTP_MODE_SERVER)) { //只解析ntp会话中的client请求报文和server回复报文
        return false;
    }

    clock_stratum = *pcur;
    pcur++;

    polling_interval = *pcur;
    pcur++;

    clock_precision = *pcur;
    pcur++;

    root_delay = ntohl(*(uint32_t *)pcur);
    pcur += 4;

    root_dispersion = ntohl(*(uint32_t *)pcur);
    pcur += 4;

    reference_id = ntohl(*(uint32_t *)pcur);
    if (clock_stratum == 0){
        kiss_code_str = bin2ascii((const char *)pcur, 4);
    } else if (clock_stratum == 1){
        refer_id_str = bin2ascii((const char *)pcur, 4);
    } else {
        if (get_ipv4_string(*(uint32_t *)pcur, refer_id_str) == -1){
            refer_id_str = "";
        }
    }
    pcur += 4;

    refer_ts = ntohl(*(uint32_t *)pcur);
    pcur += 4;

    refer_ts_frac = ntohl(*(uint32_t *)pcur);
    pcur += 4;

    origin_ts = ntohl(*(uint32_t *)pcur);
    pcur += 4;

    origin_ts_frac = ntohl(*(uint32_t *)pcur);
    pcur += 4;

    recv_ts = ntohl(*(uint32_t *)pcur);
    pcur += 4;

    recv_ts_frac = ntohl(*(uint32_t *)pcur);
    pcur += 4;

    xmit_ts = ntohl(*(uint32_t *)pcur);
    pcur += 4;

    xmit_ts_frac = ntohl(*(uint32_t *)pcur);
    pcur += 4;

    if((flags & NTP_MODE_MASK) == NTP_MODE_CLIENT){
        if (!p_ntp_session->msg_parse_done[NTP_CLIENT_MSG]){// 只解析会话中首个client请求报文        
            p_ntp_session->p_ntp_client_data->version = (flags & NTP_VN_MASK) >> 3;
            p_ntp_session->p_ntp_client_data->clock_stratum = clock_stratum;
            p_ntp_session->p_ntp_client_data->polling_interval_sec = pow(2.0, polling_interval);
            p_ntp_session->p_ntp_client_data->clock_precision = pow(2.0, clock_precision);
            p_ntp_session->p_ntp_client_data->root_delay = (root_delay >> 16) + (root_delay & 0xffff) / 65536.0;
            p_ntp_session->p_ntp_client_data->root_dispersion = (root_dispersion >> 16) + (root_dispersion & 0xffff) / 65536.0;
            p_ntp_session->p_ntp_client_data->reference_id = refer_id_str;
            p_ntp_session->p_ntp_client_data->refer_ts_sec = ntp_to_nstime(refer_ts);
            p_ntp_session->p_ntp_client_data->origin_ts_sec = ntp_to_nstime(origin_ts);
            p_ntp_session->p_ntp_client_data->recv_ts_sec = ntp_to_nstime(recv_ts);
            p_ntp_session->p_ntp_client_data->xmit_ts_sec = ntp_to_nstime(xmit_ts);
            p_ntp_session->p_ntp_client_data->refer_ts_nsec = (uint32_t)(refer_ts_frac * NS_1_NTP_TSFRAC);
            p_ntp_session->p_ntp_client_data->origin_ts_nsec = (uint32_t)(origin_ts_frac * NS_1_NTP_TSFRAC);
            p_ntp_session->p_ntp_client_data->recv_ts_nsec = (uint32_t)(recv_ts_frac * NS_1_NTP_TSFRAC);
            p_ntp_session->p_ntp_client_data->xmit_ts_nsec = (uint32_t)(xmit_ts_frac * NS_1_NTP_TSFRAC);

            p_ntp_session->msg_parse_done[NTP_CLIENT_MSG] = true;
        }
    }

    if((flags & NTP_MODE_MASK) == NTP_MODE_SERVER){
       if (!p_ntp_session->msg_parse_done[NTP_SERVER_MSG]){// 只解析会话中首个server回复报文   
            p_ntp_session->p_ntp_server_data->version = (flags & NTP_VN_MASK) >> 3;
            p_ntp_session->p_ntp_server_data->clock_stratum = clock_stratum;
            p_ntp_session->p_ntp_server_data->polling_interval_sec = pow(2.0, polling_interval);
            p_ntp_session->p_ntp_server_data->clock_precision = pow(2.0, clock_precision);
            p_ntp_session->p_ntp_server_data->root_delay = (root_delay >> 16) + (root_delay & 0xffff) / 65536.0;
            p_ntp_session->p_ntp_server_data->root_dispersion = (root_dispersion >> 16) + (root_dispersion & 0xffff) / 65536.0;
            p_ntp_session->p_ntp_server_data->reference_id = refer_id_str;
            p_ntp_session->p_ntp_server_data->refer_ts_sec = ntp_to_nstime(refer_ts);
            p_ntp_session->p_ntp_server_data->origin_ts_sec = ntp_to_nstime(origin_ts);
            p_ntp_session->p_ntp_server_data->recv_ts_sec = ntp_to_nstime(recv_ts);
            p_ntp_session->p_ntp_server_data->xmit_ts_sec = ntp_to_nstime(xmit_ts);
            p_ntp_session->p_ntp_server_data->refer_ts_nsec = (uint32_t)(refer_ts_frac * NS_1_NTP_TSFRAC);
            p_ntp_session->p_ntp_server_data->origin_ts_nsec = (uint32_t)(origin_ts_frac * NS_1_NTP_TSFRAC);
            p_ntp_session->p_ntp_server_data->recv_ts_nsec = (uint32_t)(recv_ts_frac * NS_1_NTP_TSFRAC);
            p_ntp_session->p_ntp_server_data->xmit_ts_nsec = (uint32_t)(xmit_ts_frac * NS_1_NTP_TSFRAC);

            p_ntp_session->msg_parse_done[NTP_SERVER_MSG] = true;
       }
    }

    if (p_ntp_session->msg_parse_done[NTP_CLIENT_MSG] && p_ntp_session->msg_parse_done[NTP_SERVER_MSG]){
        return true;
    } else {
        return false;
    }
}

bool ntp_plugin::potocol_init(session_pub *p_session, c_packet *p_packet) {
    if (p_session == NULL || p_packet == NULL) {
        return false;
    }
    proto_parse_session *p_pp_session = (proto_parse_session *)(p_session->p_sp_session);
    ntp_session *p_ntp_session = (ntp_session *)p_pp_session->expansion_data;
    init_ntp_session(p_ntp_session);
    p_ntp_session->thread_id = p_session->thread_id;
    return true;
}

bool ntp_plugin::potocol_sign_judge(session_pub *p_session, c_packet *p_packet) // 组包
{
    if (p_session == NULL || p_packet == NULL) {
        return false;
    }
    proto_parse_session *p_pp_session = (proto_parse_session *)(p_session->p_sp_session);
    ntp_session *p_ntp_session = (ntp_session *)p_pp_session->expansion_data;
    // 组包
    if (p_packet->app_data_len == 0) {
        return false;
    } else {
        p_ntp_session->p_data = p_packet->p_app_data;
        p_ntp_session->data_len = p_packet->app_data_len;
        if ((0 == p_packet->Directory && PACKETFROMCLIENT == p_session->session_basic.Server) ||
            (1 == p_packet->Directory && PACKETFROMSERVER == p_session->session_basic.Server)) {
            p_ntp_session->pktdirec = PKTFROMCLIENT;
        } else if ((0 == p_packet->Directory && PACKETFROMSERVER == p_session->session_basic.Server) ||
                   (1 == p_packet->Directory && PACKETFROMCLIENT == p_session->session_basic.Server)) {
            p_ntp_session->pktdirec = PKTFROMSERVER;
        }
        return true;
    }
}

bool ntp_plugin::potocol_parse_handle(session_pub *p_session, c_packet *p_packet) // 解析
{
    uint16_t opcode;
    uint16_t version;
    bool ret = false;
    if (p_session == NULL || p_packet == NULL) {
        return false;
    }
    proto_parse_session *p_pp_session = (proto_parse_session *)(p_session->p_sp_session);
    ntp_session *p_ntp_session = (ntp_session *)p_pp_session->expansion_data;
    // 如果解析数据之后发现可以发送了，就返回true


    ret = parse_ntp_data(p_ntp_session);


    return ret;
    
}

void ntp_plugin::ntp_msg_send(session_pub *p_session) { // 发送
    if (p_session == NULL) {
        return;
    }
    proto_parse_session *p_pp_session = (proto_parse_session *)(p_session->p_sp_session);
    ntp_session *p_ntp_session = (ntp_session *)p_pp_session->expansion_data;

    if (p_ntp_session->msg_send_done) { // only send once
        return;
    }

    if (should_log) {
        JKNmsg *p_msg = p_session->p_value->get();
        if (p_msg == NULL) {
            return;
        }
        p_msg->set_type(106); // TODO:应当遵循什么原则指定type的值？
        ntp_msg *p_ntp = p_msg->mutable_ntp();
        Comm_msg *p_comm = p_ntp->mutable_comm_msg();
        ntp_client_msg *p_ntp_client = p_ntp->mutable_client_msg();
        ntp_server_msg *p_ntp_server = p_ntp->mutable_server_msg();
        // 公共信息部分
        commsg_fill(p_session, p_comm, "10413", p_th_tools);

        ////ntp msg
        p_ntp->set_version(p_ntp_session->p_ntp_client_data->version);

        p_ntp_client->set_stratum(p_ntp_session->p_ntp_client_data->clock_stratum);
        p_ntp_client->set_poll_interval_sec(p_ntp_session->p_ntp_client_data->polling_interval_sec);
        p_ntp_client->set_clock_precision(p_ntp_session->p_ntp_client_data->clock_precision);
        p_ntp_client->set_root_delay(p_ntp_session->p_ntp_client_data->root_delay);
        p_ntp_client->set_root_dispersion(p_ntp_session->p_ntp_client_data->root_dispersion);
        p_ntp_client->set_reference_identifier(p_ntp_session->p_ntp_client_data->reference_id);
        p_ntp_client->set_refer_ts_sec(p_ntp_session->p_ntp_client_data->refer_ts_sec);
        p_ntp_client->set_refer_ts_nsec(p_ntp_session->p_ntp_client_data->refer_ts_nsec);
        p_ntp_client->set_origin_ts_sec(p_ntp_session->p_ntp_client_data->origin_ts_sec);
        p_ntp_client->set_origin_ts_nsec(p_ntp_session->p_ntp_client_data->origin_ts_nsec);
        p_ntp_client->set_recv_ts_sec(p_ntp_session->p_ntp_client_data->recv_ts_sec);
        p_ntp_client->set_recv_ts_nsec(p_ntp_session->p_ntp_client_data->recv_ts_nsec);
        p_ntp_client->set_xmit_ts_sec(p_ntp_session->p_ntp_client_data->xmit_ts_sec);
        p_ntp_client->set_xmit_ts_nsec(p_ntp_session->p_ntp_client_data->xmit_ts_nsec);

        p_ntp_server->set_stratum(p_ntp_session->p_ntp_server_data->clock_stratum);
        p_ntp_server->set_poll_interval_sec(p_ntp_session->p_ntp_server_data->polling_interval_sec);
        p_ntp_server->set_clock_precision(p_ntp_session->p_ntp_server_data->clock_precision);
        p_ntp_server->set_root_delay(p_ntp_session->p_ntp_server_data->root_delay);
        p_ntp_server->set_root_dispersion(p_ntp_session->p_ntp_server_data->root_dispersion);
        p_ntp_server->set_reference_identifier(p_ntp_session->p_ntp_server_data->reference_id);
        p_ntp_server->set_refer_ts_sec(p_ntp_session->p_ntp_server_data->refer_ts_sec);
        p_ntp_server->set_refer_ts_nsec(p_ntp_session->p_ntp_server_data->refer_ts_nsec);
        p_ntp_server->set_origin_ts_sec(p_ntp_session->p_ntp_server_data->origin_ts_sec);
        p_ntp_server->set_origin_ts_nsec(p_ntp_session->p_ntp_server_data->origin_ts_nsec);
        p_ntp_server->set_recv_ts_sec(p_ntp_session->p_ntp_server_data->recv_ts_sec);
        p_ntp_server->set_recv_ts_nsec(p_ntp_session->p_ntp_server_data->recv_ts_nsec);
        p_ntp_server->set_xmit_ts_sec(p_ntp_session->p_ntp_server_data->xmit_ts_sec);
        p_ntp_server->set_xmit_ts_nsec(p_ntp_session->p_ntp_server_data->xmit_ts_nsec);
    }

    p_ntp_session->msg_send_done = true;

    return;
}

void ntp_plugin::potocol_data_handle(session_pub *p_session, c_packet *p_packet) // 发送
{
    ntp_msg_send(p_session);
}

bool ntp_plugin::time_out(session_pub *p_session, uint32_t check_time) {
    if (p_session) {
        return true;
    }
    return true;
}

void ntp_plugin::resources_recovery(session_pub *p_session) {
    proto_parse_session *p_pp_session = (proto_parse_session *)(p_session->p_sp_session);
    ntp_session *p_ntp_session = (ntp_session *)p_pp_session->expansion_data;

    reset_ntp_session(p_ntp_session);
    return;
}

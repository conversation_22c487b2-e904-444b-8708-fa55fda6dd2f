#include "rdp_plugin.h"
#include "aes_hash.h"
#include "crc64.h"
#include "rapidjson/stringbuffer.h"
#include "rapidjson/writer.h"
#include "ssl_sha1.h"
#include <commit_tools.h>
#include <fstream>
#include <hash.h>
#include <string.h>

extern "C" {
int get_plugin_id() {
    return 10639;
}
session_pasre_base *attach() {
    return new rdp_plugin();
}
}

static map<uint32_t, string> rdp_sec_proto_str = {
    {0x00000000, "Standard RDP Security"},
    {0x00000001, "TLS 1.0, 1.1 or 1.2"},
    {0x00000002, "CredSSP"},
    {0x00000004, "RDSTLS protocol"},
    {0x00000008, "CredSSP with Early User Authorization Result PDU"},
    {0x00000010, "RDS AAD Auth security"}
};

static map<uint8_t, string> rdp_c_flags_str = {
    {0x01, "Restricted Admin Mode"},
    {0x02, "Redirected Authentication"},
    {0x08, "Correlation Info Present"},
};

static map<uint8_t, string> rdp_s_flags_str = {
    {0x01, "Extended Client Data Blocks"},
    {0x02, "Graphics Pipeline Extension Protocol"},
    {0x08, "Restricted Admin Mode"},
    {0x10, "Redirected Authentication Mode"}
};

unsigned int hash_func(unsigned int bucktes, void *key) {
    uint8_t *p = (uint8_t *)key;
    return (unsigned int)((*(uint64_t *)p) ^ (*(uint64_t *)(p + 8)) ^ (*(uint64_t *)(p + 16)) ^
                          (*(uint64_t *)(p + 24)) ^ (*(uint64_t *)(p + 32))) %
           bucktes;
}

rdp_plugin::rdp_plugin() {
    cerfile_dir = NULL;
    backList_hash = (hash_t *)hash_alloc(65535, hash_func);
    reload();
    backList_parse();
    aes_hash_ctx = AES_InitKey();
    if (NULL == aes_hash_ctx) {
        exit(-1);
    }
    pDeduplicator = new CCertfileDeduplicator(2500);
}
rdp_plugin::~rdp_plugin() {
    key_cookie.Quit();
    key_Duca.Quit();
    key_McDn.Quit();
    key_0d0a.Quit();
    if (cerfile_dir) {
        free(cerfile_dir);
        cerfile_dir = NULL;
    }
    AES_CleanUp(aes_hash_ctx);
}

void rdp_plugin::backList_parse() {
    // 打开配置配置文件 ， 解析
    std::ifstream ifs;
    ifs.open("conf/cert_config.json");
    Json::Value val;
    Json::Reader reader;

    if (!reader.parse(ifs, val)) {
        return;
    }
    white.do_ssl_pb = val["whiteListLog"].asUInt();
    white.do_session_pb = val["whiteListFlow"].asUInt();
    back.do_ssl_pb = val["blackListLog"].asUInt();
    back.do_session_pb = val["blackListFlow"].asUInt();

    // 读取文件  证书黑白名单配置
    char buff[2048];
    FILE *fp = fopen("conf/certbwlist.json", "r");
    if (fp != NULL) {
        while (fgets(buff, 2048, fp) != NULL) {
            //
            Json::Value val;
            Json::Reader reader;
            if (!reader.parse(buff, val)) {
                continue;
            }
            unsigned int type = val["Type"].asUInt();
            std::string str = val[""].asString();
            char buf[64];
            memcpy(buf, str.c_str(), 40);
            hash_add_entry(backList_hash, (void *)buf, 40, (void *)&type, sizeof(int));
        }
        fclose(fp);
    } else {
        printf("打开文件失败\n");
    }
    //
}
void rdp_plugin::clear_ssl_session(session_pub *p_session) {
    if (p_session == NULL)
        return;
    proto_parse_session *p_tmp_mid = (proto_parse_session *)p_session->p_sp_session;
    void **pp_session = (void **)p_tmp_mid->expansion_data;
    rdp_session *p_rdp = (rdp_session *)pp_session[0];
    ssl_session *p_ssl_session = (ssl_session *)pp_session[1];
    if (p_ssl_session) {
        if (p_ssl_session->p_ssl_message != NULL) {
            delete p_ssl_session->p_ssl_message;
            p_ssl_session->p_ssl_message = NULL;
        }
        delete p_ssl_session;
        pp_session[1] = NULL;
    }
    return;
}

void rdp_plugin::clear_rdp_session(session_pub *p_session) {
    if (p_session == NULL)
        return;
    proto_parse_session *p_tmp_mid = (proto_parse_session *)p_session->p_sp_session;
    void **pp_session = (void **)p_tmp_mid->expansion_data;
    rdp_session *p_rdp = (rdp_session *)pp_session[0];
    ssl_session *p_ssl_session = (ssl_session *)pp_session[1];
    if (p_rdp) {
        if (p_rdp->p_cs_core) {
            delete[] p_rdp->p_cs_core;
            p_rdp->p_cs_core = NULL;
        }
        if (p_rdp->p_cs_security) {
            delete[] p_rdp->p_cs_security;
            p_rdp->p_cs_security = NULL;
        }
        if (p_rdp->p_cs_net) {
            delete[] p_rdp->p_cs_net;
            p_rdp->p_cs_net = NULL;
        }
        if (p_rdp->p_cs_cluster) {
            delete[] p_rdp->p_cs_cluster;
            p_rdp->p_cs_cluster = NULL;
        }
        if (p_rdp->p_sc_core) {
            delete[] p_rdp->p_sc_core;
            p_rdp->p_sc_core = NULL;
        }
        if (p_rdp->p_sc_security) {
            delete[] p_rdp->p_sc_security;
            p_rdp->p_sc_security = NULL;
        }
        if (p_rdp->p_sc_net) {
            delete[] p_rdp->p_sc_net;
            p_rdp->p_sc_net = NULL;
        }
        if (p_rdp->p_rdp_cookie) {
            delete[] p_rdp->p_rdp_cookie;
            p_rdp->p_rdp_cookie = NULL;
        }
        delete p_rdp;
        pp_session[0] = NULL;
    }
    return;
}

void rdp_plugin::init_ssl_session(ssl_session *p_ssl_session) {
    p_ssl_session->p_data = NULL;
    p_ssl_session->data_len = 0;
    p_ssl_session->p_ssl_message = new ssl_message();
    p_ssl_session->b_parse_over[0] = false;
    p_ssl_session->b_parse_over[1] = false;
    p_ssl_session->b_cacheing[0] = false;
    p_ssl_session->b_cacheing[1] = false;
    p_ssl_session->ofo_pkt_num[0] = 0;
    p_ssl_session->ofo_pkt_num[1] = 0;
    p_ssl_session->b_send_over = false;
    p_ssl_session->b_use_ssl[0] = 0;
    p_ssl_session->b_use_ssl[1] = 0;
}

// 解析插件自身的配置文件
bool rdp_plugin::potocol_init(session_pub *p_session, c_packet *p_packet) {
    if (p_session == NULL || p_packet == NULL)
        return false;
    proto_parse_session *p_tmp_mid = (proto_parse_session *)p_session->p_sp_session;
    void **pp_session = (void **)p_tmp_mid->expansion_data;
    pp_session[0] = new rdp_session;
    memset(pp_session[0], 0, sizeof(rdp_session));
    pp_session[1] = new ssl_session();
    ssl_session *p_ssl_session = (ssl_session *)pp_session[1];
    init_ssl_session(p_ssl_session);
    p_ssl_session->thread_id = p_packet->thread_id;
    p_ssl_session->do_ssl_pb = 1;
    return true;
}

string rdp_plugin::bin2hex(const char *p_data, uint32_t len) {
    string s = "";
    char formate_char[3];
    for (uint32_t i = 0; i < len; ++i) {
        sprintf(formate_char, "%02x", (unsigned char)p_data[i]);
        s.append(formate_char, 2);
    }
    return s;
}

string rdp_plugin::bin2string(const char* p_data, uint32_t len) {
    // Check for null pointer
    if (p_data == nullptr) {
        return std::string();
    }
    
    // Check for valid length
    if (len == 0) {
        return std::string();
    }
    
    // Create string safely
    return std::string(p_data, len);
}

bool rdp_plugin::pkt_is_whole(c_packet *p_packet) {
    if (p_packet->m_str_packet_moudle.Stack.ProtocolNum &&
        PROTOCOL_SSL ==
            p_packet->m_str_packet_moudle.Stack.pProtocol[p_packet->m_str_packet_moudle.Stack.ProtocolNum - 1]
                .Protocol) {
        uint8_t *p_cur = p_packet->p_app_data;
        uint32_t offset = 0;
        if (offset + 5 <= p_packet->app_data_len) {
            uint8_t content_type = *(uint8_t *)(p_cur + offset);
            offset += 1;
            if (*(uint8_t *)(p_cur + offset) != 0x03 || *(uint8_t *)(p_cur + offset + 1) > 0x03) {
                return false;
            }
            offset += 2;
            uint16_t length = ntohs(*(uint16_t *)(p_cur + offset));
            offset += 2;
            if (23 == content_type) // application data
            {
                return true;
            }
            if (offset + length == p_packet->app_data_len) {
                return true;
            }
        }
    }
    return false;
}

bool rdp_plugin::data_is_whole(ssl_session *p_ssl_session) {
    if (p_ssl_session->p_data == NULL || p_ssl_session->data_len == 0) {
        return false;
    }
    char *p_cur = p_ssl_session->p_data;
    uint32_t offset = 0;
    for (; offset + 5 <= p_ssl_session->data_len;) {
        uint8_t content_type = *(uint8_t *)(p_cur + offset);
        offset += 1;
        if (*(uint8_t *)(p_cur + offset) != 0x03 || *(uint8_t *)(p_cur + offset + 1) > 0x03) {
            return false;
        }
        offset += 2;
        uint16_t length = ntohs(*(uint16_t *)(p_cur + offset));
        offset += 2;
        if (23 == content_type) // application data
        {
            return true;
        }
        if (offset + length == p_ssl_session->data_len) {
            return true;
        } else if (offset + length < p_ssl_session->data_len) {
            offset += length;
            continue;
        } else {
            return false;
        }
    }
    return false;
}

bool rdp_plugin::potocol_sign_judge(session_pub *p_session, c_packet *p_packet) {
    uint32_t seq_tmp;
    if (p_session == NULL || p_packet == NULL) {
        return false;
    }
    proto_parse_session *p_tmp_mid = (proto_parse_session *)p_session->p_sp_session;
    void **pp_session = (void **)p_tmp_mid->expansion_data;
    rdp_session *p_rdp = (rdp_session *)pp_session[0];
    ssl_session *p_ssl_session = (ssl_session *)pp_session[1];

    if (NULL == p_ssl_session) {
        return false;
    }
    if (p_ssl_session->b_use_ssl[p_packet->Directory] == 0 && PROTOCOL_SSL == p_packet->app_pro) {
        p_ssl_session->b_use_ssl[p_packet->Directory] = 1;
    }
    if (p_ssl_session->b_use_ssl[p_packet->Directory]) {
        if (p_ssl_session->do_ssl_pb == 0) {
            return true;
        }
        // 组包
        if (p_packet->app_data_len == 0 || p_ssl_session->b_parse_over[p_packet->Directory] == true ||
            p_session->session_basic.Server == PACKETFROMUNKOWN) {
            return false;
        }

        if (p_ssl_session->b_cacheing[p_packet->Directory]) {
            p_session->p_session_ext->tcp_repcom[p_packet->Directory]->add_fragment(
                p_packet->seq, p_packet->app_data_len, (char *)p_packet->p_app_data);
            p_session->p_session_ext->tcp_repcom[p_packet->Directory]->front_fragment(seq_tmp, p_ssl_session->data_len,
                                                                                      p_ssl_session->p_data);
        } else {
            if (pkt_is_whole(p_packet)) // pkt
            {
                p_ssl_session->p_data = (char *)p_packet->p_app_data;
                p_ssl_session->data_len = p_packet->app_data_len;
                return true;
            } else if (p_packet->m_str_packet_moudle.Stack.ProtocolNum &&
                       PROTOCOL_SSL == p_packet->m_str_packet_moudle.Stack
                                           .pProtocol[p_packet->m_str_packet_moudle.Stack.ProtocolNum - 1]
                                           .Protocol) {
                if (NULL == p_session->p_session_ext) {
                    return false;
                }
                p_session->p_tcp_recom_pop_cb(p_session, p_packet->Directory);
                if (p_session->p_session_ext->tcp_repcom[p_packet->Directory]) {
                    p_session->p_session_ext->tcp_repcom[p_packet->Directory]->add_fragment(
                        p_packet->seq, p_packet->app_data_len, (char *)p_packet->p_app_data);
                    p_session->p_session_ext->tcp_repcom[p_packet->Directory]->front_fragment(
                        seq_tmp, p_ssl_session->data_len, p_ssl_session->p_data);
                    p_ssl_session->b_cacheing[p_packet->Directory] = true;
                }
            } else {
                if (NULL == p_session->p_session_ext) {
                    return false;
                }
                p_session->p_tcp_recom_pop_cb(p_session, p_packet->Directory);
                if (p_session->p_session_ext->tcp_repcom[p_packet->Directory]) {
                    p_session->p_session_ext->tcp_repcom[p_packet->Directory]->add_fragment(
                        p_packet->seq, p_packet->app_data_len, (char *)p_packet->p_app_data);
                    p_session->p_session_ext->tcp_repcom[p_packet->Directory]->front_fragment(
                        seq_tmp, p_ssl_session->data_len, p_ssl_session->p_data);
                    p_ssl_session->b_cacheing[p_packet->Directory] = true;
                }
                return false;
            }
        }

        if (data_is_whole(p_ssl_session)) // 如果此次组包之后，恰好得到完整的N块，则进入协议解析
        {
            return true;
        } else if (p_session->p_session_ext->tcp_repcom[p_packet->Directory] &&
                   p_session->p_session_ext->tcp_repcom[p_packet->Directory]->get_fragment_num() > 1) // maybe pkt lost
        {
            p_ssl_session->ofo_pkt_num[p_packet->Directory]++;
            if (p_ssl_session->ofo_pkt_num[p_packet->Directory] >= 10) // dont wait that pkt
            {
                p_ssl_session->ofo_pkt_num[p_packet->Directory] = 0;
                return true;
            }
            return false;
        } else // no pkt lost or tcp_recom_pop fail
        {
            return false;
        }
    } else if (PROTOCOL_RDPV8 == p_packet->app_pro && NULL != p_rdp) {
        return true;
    }
}
static bool decode_per_len(uint8_t *buf, uint32_t len, uint32_t &offset, uint16_t *per_len) {
    if (offset + 1 <= len) {
        if ((buf[offset] & 0x80) == 0) {
            *per_len = buf[offset];
            offset += 1;
            return true;
        } else if ((buf[offset] & 0xc0) == 0x80 && offset + 2 <= len) {
            *per_len = ((buf[offset] & 0x3f) << 8) + buf[offset + 1];
            offset += 2;
            return true;
        }
    }
    return false;
}

// wireshark packet-rdp.c
#define CS_CORE 0xC001
#define CS_SECURITY 0xC002
#define CS_NET 0xC003
#define CS_CLUSTER 0xC004
#define CS_MONITOR 0xC005
#define CS_MCS_MSGCHANNEL 0xC006
#define CS_MONITOR_EX 0xC008
#define CS_MULTITRANSPORT 0xC00A

#define SC_CORE 0x0C01
#define SC_SECURITY 0x0C02
#define SC_NET 0x0C03
#define SC_MCS_MSGCHANNEL 0x0C04
#define SC_MULTITRANSPORT 0x0C08
// wireshark packet-rdp.c

bool rdp_plugin::decode_rdp_clientdata(session_pub *p_session, uint8_t *buf, uint32_t len, uint32_t &offset) {
    bool retval = false;
    proto_parse_session *p_tmp_mid = (proto_parse_session *)p_session->p_sp_session;
    rdp_session **pp_rdp = (rdp_session **)p_tmp_mid->expansion_data;
    rdp_session *p_rdp = *pp_rdp;
    if (NULL == p_rdp) {
        return false;
    }
    while (offset + 4 <= len) {
        uint16_t type = *(uint16_t *)(buf + offset);
        offset += 2;
        uint16_t length = *(uint16_t *)(buf + offset);
        offset += 2;
        if (length < 4 || (length - 4 + offset > len)) {
            break;
        }
        switch (type) {
        case CS_CORE:
            if (p_rdp->p_cs_core) {
                delete[] p_rdp->p_cs_core;
                p_rdp->p_cs_core = NULL;
            }
            p_rdp->cs_core_len = length - 4;
            p_rdp->p_cs_core = new uint8_t[p_rdp->cs_core_len];
            memcpy(p_rdp->p_cs_core, buf + offset, p_rdp->cs_core_len);
            p_rdp->flag |= 1;
            retval = true;
            break;
        case CS_SECURITY:
            if (p_rdp->p_cs_security) {
                delete[] p_rdp->p_cs_security;
                p_rdp->p_cs_security = NULL;
            }
            p_rdp->cs_security_len = length - 4;
            p_rdp->p_cs_security = new uint8_t[p_rdp->cs_security_len];
            memcpy(p_rdp->p_cs_security, buf + offset, p_rdp->cs_security_len);
            p_rdp->flag |= 1;
            retval = true;
            break;
        case CS_NET:
            if (p_rdp->p_cs_net) {
                delete[] p_rdp->p_cs_net;
                p_rdp->p_cs_net = NULL;
            }
            p_rdp->cs_net_len = length - 4;
            p_rdp->p_cs_net = new uint8_t[p_rdp->cs_net_len];
            memcpy(p_rdp->p_cs_net, buf + offset, p_rdp->cs_net_len);
            p_rdp->flag |= 1;
            retval = true;
            break;
        case CS_CLUSTER:
            if (p_rdp->p_cs_cluster) {
                delete[] p_rdp->p_cs_cluster;
                p_rdp->p_cs_cluster = NULL;
            }
            p_rdp->cs_cluster_len = length - 4;
            p_rdp->p_cs_cluster = new uint8_t[p_rdp->cs_cluster_len];
            memcpy(p_rdp->p_cs_cluster, buf + offset, p_rdp->cs_cluster_len);
            p_rdp->flag |= 1;
            retval = true;
            break;
        default:
            break;
        }
        offset += (length - 4);
    }
    return retval;
}

bool rdp_plugin::decode_rdp_serverdata(session_pub *p_session, uint8_t *buf, uint32_t len, uint32_t &offset) {
    bool retval = false;
    proto_parse_session *p_tmp_mid = (proto_parse_session *)p_session->p_sp_session;
    rdp_session **pp_rdp = (rdp_session **)p_tmp_mid->expansion_data;
    rdp_session *p_rdp = *pp_rdp;
    if (NULL == p_rdp) {
        return false;
    }
    while (offset + 4 <= len) {
        uint16_t type = *(uint16_t *)(buf + offset);
        offset += 2;
        uint16_t length = *(uint16_t *)(buf + offset);
        offset += 2;
        if (length < 4 || (length - 4 + offset > len)) {
            break;
        }
        switch (type) {
        case SC_CORE:
            if (p_rdp->p_sc_core) {
                delete[] p_rdp->p_sc_core;
                p_rdp->p_sc_core = NULL;
            }
            p_rdp->sc_core_len = length - 4;
            p_rdp->p_sc_core = new uint8_t[p_rdp->sc_core_len];
            memcpy(p_rdp->p_sc_core, buf + offset, p_rdp->sc_core_len);
            p_rdp->flag |= (1 << 1);
            retval = true;
            break;
        case SC_SECURITY:
            if (p_rdp->p_sc_security) {
                delete[] p_rdp->p_sc_security;
                p_rdp->p_sc_security = NULL;
            }
            p_rdp->sc_security_len = length - 4;
            p_rdp->p_sc_security = new uint8_t[p_rdp->sc_security_len];
            memcpy(p_rdp->p_sc_security, buf + offset, p_rdp->sc_security_len);
            p_rdp->flag |= (1 << 1);
            retval = true;
            break;
        case SC_NET:
            if (p_rdp->p_sc_net) {
                delete[] p_rdp->p_sc_net;
                p_rdp->p_sc_net = NULL;
            }
            p_rdp->sc_net_len = length - 4;
            p_rdp->p_sc_net = new uint8_t[p_rdp->sc_net_len];
            memcpy(p_rdp->p_sc_net, buf + offset, p_rdp->sc_net_len);
            p_rdp->flag |= (1 << 1);
            retval = true;
            break;
        default:
            break;
        }
        offset += (length - 4);
    }
    return retval;
}

bool rdp_plugin::decode_t124_guess(session_pub *p_session, uint8_t *buf, uint32_t len, uint32_t &offset) {
    uint8_t *find = NULL;
    if (offset + 1 <= len) {
        uint8_t type = buf[offset];
        offset += 1;
        uint16_t objlen;
        if (0 == type && decode_per_len(buf, len, offset, &objlen)) {
            offset += objlen;
            if (decode_per_len(buf, len, offset, &objlen)) {
                if (offset + 1 <= len) {
                    type = (buf[offset] & 0x7f) >> 4;
                    offset += 1;
                    if (0 == type) {
                        find = key_Duca.Find(buf + offset, buf + len);
                        if (find) {
                            offset = find - buf + 4;
                            if (decode_per_len(buf, len, offset, &objlen)) {
                                return decode_rdp_clientdata(p_session, buf, len, offset);
                            }
                        }
                    } else if (1 == type) {
                        find = key_McDn.Find(buf + offset, buf + len);
                        if (find) {
                            offset = find - buf + 4;
                            if (decode_per_len(buf, len, offset, &objlen)) {
                                return decode_rdp_serverdata(p_session, buf, len, offset);
                            }
                        }
                    }
                }
            }
        }
    }
    return false;
}

static bool decode_ber_identifier(uint8_t *buf, uint32_t len, uint32_t &offset, uint8_t &ber_class, uint32_t &ber_tag) {
    if (offset + 1 <= len) {
        uint8_t id = buf[offset];
        offset += 1;
        uint8_t bclass = id >> 6 & 0x03;
        uint32_t tag = id & 0x1f;
        if (0x1f == tag) {
            tag = 0;
            while (offset < len) {
                uint8_t tmp = buf[offset];
                offset += 1;
                tag <<= 7;
                tag |= tmp & 0x7f;
                if (!(tmp & 0x80)) {
                    break;
                }
            }
        }
        ber_class = bclass;
        ber_tag = tag;
        return true;
    }
    return false;
}

static bool decode_ber_length(uint8_t *buf, uint32_t len, uint32_t &offset, uint32_t &length) {
    if (offset + 1 <= len) {
        uint8_t oct = buf[offset];
        offset += 1;
        if (!(oct & 0x80)) {
            length = oct;
        } else {
            uint8_t length_size = oct & 0x7f;
            if ((length_size > 0) && (offset + length_size <= len)) {
                while (length_size--) {
                    oct = buf[offset];
                    length = (length << 8) + oct;
                    offset += 1;
                }
            }
        }
        return true;
    }
    return false;
}

bool rdp_plugin::decode_connect_inital(session_pub *p_session, uint8_t *buf, uint32_t len, uint32_t &offset) {
    bool decode_ret = true;
    uint8_t ber_class = 0;
    uint32_t ber_tag = 0;
    uint32_t ber_length = 0;
    int i = 0;
    for (i = 0; i < 6 && decode_ret; i++) {
        decode_ret = decode_ber_identifier(buf, len, offset, ber_class, ber_tag);
        if (decode_ret) {
            decode_ret = decode_ber_length(buf, len, offset, ber_length);
            if (decode_ret) {
                offset += ber_length;
            } else {
                break;
            }
        } else {
            break;
        }
    }
    if (decode_ret && i == 6) {
        decode_ret = decode_ber_identifier(buf, len, offset, ber_class, ber_tag);
        if (decode_ret) {
            decode_ret = decode_ber_length(buf, len, offset, ber_length);
            if (decode_ret) {
                return decode_t124_guess(p_session, buf, len, offset);
            }
        }
    } else {
        return false;
    }
    return false;
}

bool rdp_plugin::decode_connect_response(session_pub *p_session, uint8_t *buf, uint32_t len, uint32_t &offset) {
    bool decode_ret = true;
    uint8_t ber_class = 0;
    uint32_t ber_tag = 0;
    uint32_t ber_length = 0;
    int i = 0;
    for (i = 0; i < 3 && decode_ret; i++) {
        decode_ret = decode_ber_identifier(buf, len, offset, ber_class, ber_tag);
        if (decode_ret) {
            decode_ret = decode_ber_length(buf, len, offset, ber_length);
            if (decode_ret) {
                offset += ber_length;
            } else {
                break;
            }
        } else {
            break;
        }
    }
    if (decode_ret && i == 3) {
        decode_ret = decode_ber_identifier(buf, len, offset, ber_class, ber_tag);
        if (decode_ret) {
            decode_ret = decode_ber_length(buf, len, offset, ber_length);
            if (decode_ret) {
                return decode_t124_guess(p_session, buf, len, offset);
            }
        }
    } else {
        return false;
    }
    return false;
}

// wireshark packet-ber.h
#define BER_CLASS_UNI 0
#define BER_CLASS_APP 1
#define BER_CLASS_CON 2
#define BER_CLASS_PRI 3
// wireshark packet-ber.h

#define CONNECT_INITIAL 101
#define CONNECT_RESPONSE 102
#define CONNECT_ADDITIONAL 103
#define CONNECT_RESULT 104

bool rdp_plugin::decode_t125(session_pub *p_session, uint8_t *buf, uint32_t len, uint32_t &offset) {
    uint8_t ber_class = 0;
    uint32_t ber_tag = 0;
    bool decode_ret = decode_ber_identifier(buf, len, offset, ber_class, ber_tag);
    if (decode_ret) {
        if ((BER_CLASS_APP == ber_class) && (ber_tag >= CONNECT_INITIAL) && (ber_tag <= CONNECT_RESPONSE)) {
            uint32_t ber_length = 0;
            decode_ret = decode_ber_length(buf, len, offset, ber_length);
            if (decode_ret && CONNECT_INITIAL == ber_tag) {
                return decode_connect_inital(p_session, buf, len, offset);
            }
            if (decode_ret && CONNECT_RESPONSE == ber_tag) {
                return decode_connect_response(p_session, buf, len, offset);
            }
        }
    }
    return false;
}
bool rdp_plugin::decode_rdp_nego_request(session_pub *p_session, uint8_t *buf, uint32_t len, uint32_t &offset) {
    proto_parse_session *p_tmp_mid = (proto_parse_session *)p_session->p_sp_session;
    rdp_session **pp_rdp = (rdp_session **)p_tmp_mid->expansion_data;
    rdp_session *p_rdp = *pp_rdp;
    if (NULL == p_rdp) {
        return false;
    }
    if (offset + 8 <= len) {
        p_rdp->rdp_c_flag = buf[offset + 1];
        p_rdp->request_protocols = *(uint32_t *)(buf + offset + 4);
        p_rdp->flag |= 1;
        p_rdp->flag |= (1 << 2);
        offset += 8;
        return true;
    }
    return false;
}
bool rdp_plugin::decode_rdp_nego_response(session_pub *p_session, uint8_t *buf, uint32_t len, uint32_t &offset) {
    proto_parse_session *p_tmp_mid = (proto_parse_session *)p_session->p_sp_session;
    rdp_session **pp_rdp = (rdp_session **)p_tmp_mid->expansion_data;
    rdp_session *p_rdp = *pp_rdp;
    if (NULL == p_rdp) {
        return false;
    }
    if (offset + 8 <= len) {
        p_rdp->rdp_s_flag = buf[offset + 1];
        p_rdp->selected_protocols = *(uint32_t *)(buf + offset + 4);
        p_rdp->flag |= (1 << 1);
        p_rdp->flag |= (1 << 3);
        offset += 8;
        return true;
    }
    return false;
}

bool rdp_plugin::level_1_data_parse(char *p_data, uint32_t data_len, ssl_session *p_ssl_session, uint8_t Directory) {
    if (p_data == NULL || data_len == 0) {
        return false;
    }
    char *p_cur = p_data;
    uint32_t offset = 0;
    for (; offset + 5 <= data_len;) {
        // Record Layer解析
        uint8_t content_type = *(uint8_t *)(p_cur + offset);
        offset += 1;
        uint16_t version = ntohs(*(uint16_t *)(p_cur + offset));
        offset += 2;
        uint16_t length = ntohs(*(uint16_t *)(p_cur + offset));
        offset += 2;
        // 第二层结构解析
        if (offset + length > data_len) {
            length = data_len - offset; // 防止越界访问
        }
        switch (content_type) {
        case 22: // Handshake Protocol解析
        {
            level_2_handshake_protocol_parse(p_cur + offset, length, p_ssl_session, version, Directory);
            break;
        }
        case 23: // application data
        {
            p_ssl_session->b_parse_over[Directory] = true;
            break;
        }
        }
        if (true == p_ssl_session->b_parse_over[Directory]) {
            return true;
        }
        // 解析完成，转到下一个块
        offset += length;
        if (offset < data_len) {
            continue;
        } else if (offset == data_len) {
            return false; // 是否满足发送条件
        } else {
            return false; // 是否满足发送条件
        }
    }
    return false;
}
void rdp_plugin::level_2_handshake_protocol_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session,
                                                  uint16_t version, uint8_t Directory) {
    // 注意，此时拿到handshake的buff,可能是多块handshake排列下来，未必只是一个
    // 二层头解析
    char *p_cur = p_data;
    uint16_t offset = 0;
    // ssl_message* p = p_ssl_session->p_ssl_message;
    for (; offset + 4 <= data_len;) {
        uint8_t handshake_type = *(uint8_t *)(p_cur + offset);
        offset += 1;
        if (handshake_type != 1) {
            p_ssl_session->p_ssl_message->ssl_version = version;
        } else {
            p_ssl_session->p_ssl_message->ssl_c_version = version;
        }
        // 获取三字节整型数，此值为当前handshake块的长度-4
        uint32_t length = *(uint8_t *)(p_cur + offset);
        length = length << 16;
        offset += 1;
        length += ntohs(*(uint16_t *)(p_cur + offset));
        offset += 2;
        // 异常校验
        if (offset + length > data_len || length <= 2) {
            return;
        }
        // 第三层解析
        switch (handshake_type) {
        case 1: // client_hello
        {
            level_3_client_hello_parse(p_cur + offset - 4, length + 4, p_ssl_session);
            break;
        }
        case 2: // server_hello
        {
            level_3_server_hello_parse(p_cur + offset - 4, length + 4, p_ssl_session);
            break;
        }
        case 4: // new_session_ticket
        {
            level_3_new_session_ticket_parse(p_cur + offset - 4, length + 4, p_ssl_session);
            break;
        }
        case 11: // certificate
        {
            level_3_certificate_parse(p_cur + offset - 4, length + 4, p_ssl_session, Directory);
            break;
        }
        case 12: // server_key_exchange
        {
            level_3_server_key_exchange_parse(p_cur + offset - 4, length + 4, p_ssl_session);
            break;
        }
        case 16: // client_key_exchange
        {
            level_3_client_key_exchange_parse(p_cur + offset - 4, length + 4, p_ssl_session);
            break;
        }
        }
        // 至此解完了一块handshake，看下是否还有
        offset += length;
        if (offset >= data_len) {
            break;
        }
    }
    return;
}
void rdp_plugin::level_3_client_hello_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session) {
    char *p_cur = p_data;
    uint16_t offset = 0;
    ssl_message *p = p_ssl_session->p_ssl_message;
    if (offset + 6 > data_len) {
        return;
    }
    // uint8_t handshake_type = *(uint8_t *)(p_cur+offset);
    offset += 1;
    // 获取三字节整型数
    uint32_t length = *(uint8_t *)(p_cur + offset);
    length = length << 16;
    offset += 1;
    length += ntohs(*(uint16_t *)(p_cur + offset));
    offset += 2;
    p->ssl_hello_c_version = ntohs(*(uint16_t *)(p_cur + offset));
    offset += 2;
    p->ssl_hello_c_time = ntohl(*(uint32_t *)(p_cur + offset));
    // 异常校验
    if (offset + 32 > data_len) {
        return;
    }
    p->ssl_hello_c_random = bin2string((const char *)(p_cur + offset), 32);
    offset += 32;
    if (offset + 1 > data_len) {
        return;
    }
    p->ssl_hello_c_sessionidlen = *(uint8_t *)(p_cur + offset);
    offset += 1;
    // 异常校验
    if (offset + p->ssl_hello_c_sessionidlen > data_len) {
        return;
    }
    p->ssl_hello_c_sessionid = bin2string((const char *)(p_cur + offset), p->ssl_hello_c_sessionidlen);
    offset += p->ssl_hello_c_sessionidlen;
    if (offset + 2 > data_len) {
        return;
    }
    uint32_t ssl_hello_c_ciphersuitlen = ntohs(*(uint16_t *)(p_cur + offset));
    p->ssl_hello_c_ciphersuitnum = ssl_hello_c_ciphersuitlen / 2;
    offset += 2;
    // 异常校验
    if (offset + ssl_hello_c_ciphersuitlen > data_len) {
        return;
    }
    p->ssl_f_csuite[0] = "";
    p->ssl_hello_c_ciphersuit = "";
    for (uint32_t i = 0; i < p->ssl_hello_c_ciphersuitnum; i++) {
        if (offset + 2 > data_len) {
            return;
        }
        string ssuit = bin2string((const char *)(p_cur + offset), 2);
        uint16_t suit = *(uint16_t *)(p_cur + offset);
        uint8_t *psuit = (uint8_t *)&suit;
        if (0x0a0a != (suit & 0x0f0f) || ((psuit[0] >> 4) != (psuit[1] >> 4))) {
            p->ssl_f_csuite[0] += ssuit;
        } else {
            p->ssl_f_csuite[0] += "gres";
        }
        p->ssl_hello_c_ciphersuit += ssuit;
        offset += 2;
    }
    if (offset + 1 > data_len) {
        return;
    }
    p->ssl_hello_c_compressionmethodlen = *(uint8_t *)(p_cur + offset);
    offset += 1;
    // 异常校验
    if (offset + p->ssl_hello_c_compressionmethodlen > data_len) {
        return;
    }
    p->ssl_hello_c_compressionmethod = bin2string((const char *)(p_cur + offset), p->ssl_hello_c_compressionmethodlen);
    offset += p->ssl_hello_c_compressionmethodlen;
    // server_hello有时没有extention信息，故此处也做此判断
    if (offset + 2 > data_len) {
        return;
    }
    uint32_t ssl_hello_c_extention_len = ntohs(*(uint16_t *)(p_cur + offset));
    offset += 2;
    // 异常校验
    if (offset + ssl_hello_c_extention_len > data_len) {
        return;
    }
    level_4_extention_parse(p_cur + offset, ssl_hello_c_extention_len, p_ssl_session, 0); // 0：client
    offset += ssl_hello_c_extention_len;
    if (offset >= data_len) {
        return;
    }
}
void rdp_plugin::level_3_server_hello_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session) {
    char *p_cur = p_data;
    uint16_t offset = 0;
    ssl_message *p = p_ssl_session->p_ssl_message;
    if (offset + 6 > data_len) {
        return;
    }

    // uint8_t handshake_type = *(uint8_t *)(p_cur+offset);
    offset += 1;
    // 获取三字节整型数
    uint32_t length = *(uint8_t *)(p_cur + offset);
    length = length << 16;
    offset += 1;
    length += ntohs(*(uint16_t *)(p_cur + offset));
    offset += 2;
    p->ssl_hello_s_version = ntohs(*(uint16_t *)(p_cur + offset));
    offset += 2;
    p->ssl_hello_s_time = ntohl(*(uint32_t *)(p_cur + offset));
    // 异常校验
    if (offset + 32 > data_len) {
        return;
    }
    p->ssl_hello_s_random = bin2string((const char *)(p_cur + offset), 32);
    offset += 32;
    if (offset + 1 > data_len) {
        return;
    }
    p->ssl_hello_s_sessionidlen = *(uint8_t *)(p_cur + offset);
    offset += 1;
    // 异常校验
    if (offset + p->ssl_hello_s_sessionidlen > data_len) {
        return;
    }
    p->ssl_hello_s_sessionid = bin2string((const char *)(p_cur + offset), p->ssl_hello_s_sessionidlen);
    offset += p->ssl_hello_s_sessionidlen;
    if (offset + 2 > data_len) {
        return;
    }
    p->ssl_f_csuite[1] = "";
    p->ssl_hello_s_ciphersuit = bin2string((const char *)(p_cur + offset), 2);
    uint16_t suit = *(uint16_t *)(p_cur + offset);
    uint8_t *psuit = (uint8_t *)&suit;
    if (0x0a0a != (suit & 0x0f0f) || ((psuit[0] >> 4) != (psuit[1] >> 4))) {
        p->ssl_f_csuite[1] = p->ssl_hello_s_ciphersuit;
    } else {
        p->ssl_f_csuite[1] = "gres";
    }
    offset += 2;
    if (offset + 1 > data_len) {
        return;
    }
    p->ssl_hello_s_compressionmethod = bin2string((const char *)(p_cur + offset), 1);
    offset += 1;
    // 有时候没有extention
    if (offset + 2 > data_len) {
        return;
    }
    uint32_t ssl_hello_s_extention_len = ntohs(*(uint16_t *)(p_cur + offset));
    offset += 2;
    // 异常校验
    if (offset + ssl_hello_s_extention_len > data_len) {
        return;
    }
    level_4_extention_parse(p_cur + offset, ssl_hello_s_extention_len, p_ssl_session, 1); // 1:server
    offset += ssl_hello_s_extention_len;
    if (offset >= data_len) {
        return;
    }
}
void rdp_plugin::level_3_new_session_ticket_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session) {
    char *p_cur = p_data;
    uint16_t offset = 0;
    ssl_message *p = p_ssl_session->p_ssl_message;
    if (offset + 10 > data_len) {
        return;
    }

    // uint8_t handshake_type = *(uint8_t *)(p_cur+offset);
    offset += 1;
    // 获取三字节整型数
    uint32_t length = *(uint8_t *)(p_cur + offset);
    length = length << 16;
    offset += 1;
    length += ntohs(*(uint16_t *)(p_cur + offset));
    offset += 2;
    p->ssl_s_newsessionticket_lifetime = ntohl(*(uint32_t *)(p_cur + offset));
    offset += 4;
    p->ssl_s_newsessionticket_ticketlen = ntohs(*(uint16_t *)(p_cur + offset));
    offset += 2;
    // 异常校验
    if (offset + p->ssl_s_newsessionticket_ticketlen > data_len) {
        return;
    }
    p->ssl_s_newsessionticket_ticket = bin2string((const char *)(p_cur + offset), p->ssl_s_newsessionticket_ticketlen);
    offset += p->ssl_s_newsessionticket_ticketlen;
    if (offset >= data_len) {
        return;
    }
}
void rdp_plugin::level_3_client_key_exchange_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session) {
    char *p_cur = p_data;
    uint16_t offset = 0;
    ssl_message *p = p_ssl_session->p_ssl_message;
    if (offset + 5 > data_len) {
        return;
    }

    // uint8_t handshake_type = *(uint8_t *)(p_cur+offset);
    offset += 1;
    // 获取三字节整型数
    uint32_t length = *(uint8_t *)(p_cur + offset);
    length = length << 16;
    offset += 1;
    length += ntohs(*(uint16_t *)(p_cur + offset));
    offset += 2;
    p->ssl_c_keyexchangelen = *(uint8_t *)(p_cur + offset);
    offset += 1;
    // 异常校验
    if (offset + p->ssl_c_keyexchangelen > data_len) {
        return;
    }
    p->ssl_c_keyexchange = bin2string((const char *)(p_cur + offset), p->ssl_c_keyexchangelen);
    offset += p->ssl_c_keyexchangelen;
    if (offset >= data_len) {
        return;
    }
}
void rdp_plugin::level_3_server_key_exchange_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session) {
    char *p_cur = p_data;
    uint16_t offset = 0;
    ssl_message *p = p_ssl_session->p_ssl_message;
    if (offset + 4 > data_len) {
        return;
    }

    // uint8_t handshake_type = *(uint8_t *)(p_cur+offset);
    offset += 1;
    // 获取三字节整型数
    uint32_t length = *(uint8_t *)(p_cur + offset);
    length = length << 16;
    offset += 1;
    length += ntohs(*(uint16_t *)(p_cur + offset));
    offset += 2;
    // 异常校验
    if (offset + length > data_len) {
        return;
    }
    p->ssl_s_keyexchangelen = length;
    p->ssl_s_keyexchange = bin2string((const char *)(p_cur + offset), length);
    offset += length;
    if (offset >= data_len) {
        return;
    }
}
void rdp_plugin::level_3_certificate_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session,
                                           uint8_t Directory) {
    char *p_cur = p_data;
    uint16_t offset = 0;
    unsigned long long times;
    ssl_message *p = p_ssl_session->p_ssl_message;
    if (offset + 4 > data_len) {
        return;
    }

    // uint8_t handshake_type = *(uint8_t *)(p_cur+offset);
    offset += 1;
    // 获取三字节整型数
    uint32_t length = *(uint8_t *)(p_cur + offset);
    length = length << 16;
    offset += 1;
    length += ntohs(*(uint16_t *)(p_cur + offset));
    offset += 2;
    // 校验， data_len比length大4
    if (data_len != length + 4 || offset + 3 > data_len) {
        return;
    }
    // 获取certificates_length
    uint32_t certificates_length = *(uint8_t *)(p_cur + offset);
    certificates_length = certificates_length << 16;
    offset += 1;
    certificates_length += ntohs(*(uint16_t *)(p_cur + offset));
    // 校验，length应该比certificates_length大3
    if (length != certificates_length + 3) {
        return;
    }
    if (certificates_length == 0) {
        return;
    }
    offset += 2;
    // 证书队列
    uint32_t certificates_num = 0;
    // Json::Value jcer_hash_list;
    p->j_ssl_cert_hash[Directory].clear();
    Json::FastWriter fast_writer;
    for (; offset + 3 <= data_len;) {
        // 获取certificate_length
        uint32_t certificate_length = *(uint8_t *)(p_cur + offset);
        certificate_length = certificate_length << 16;
        offset += 1;
        certificate_length += ntohs(*(uint16_t *)(p_cur + offset));
        offset += 2;
        // 异常校验，获取到当前证书长度之后做一下校验
        if (0 == certificate_length) {
            continue;
        }
        if (offset + certificate_length > data_len) {
            return;
        }
        unsigned char sha1_key[20] = {0};
        ssl_Func_SHA(1, (unsigned char *)p_cur + offset, certificate_length, sha1_key);
        string str_key = bin2string((const char *)sha1_key, 20);

        int *p_type = (int *)hash_lookup_entry(backList_hash, (void *)str_key.c_str(), 40);
        if (p_type != NULL) {
            if (*p_type == 1) // 白名单
            {
                p_ssl_session->do_ssl_pb = white.do_ssl_pb;
                p_s_session->do_session_pb = white.do_session_pb;
            } else {
                p_ssl_session->do_ssl_pb = back.do_ssl_pb;
                p_s_session->do_session_pb = back.do_session_pb;
                p_s_session->b_black_ssl = 1;
            }
        }
        // 将证书内容存到文件中
        times = pDeduplicator->JudgeValue(sha1_key, ((th_engine_tools *)p_th_tools)->get_clock_ts());
        if (1 == times % 250) {
            ((th_engine_tools *)p_th_tools)
                ->send_log("certfile", (void *)sha1_key, 20, p_cur + offset, certificate_length);
        }
        string hash = str_key;
        p->j_ssl_cert_hash[Directory].append(str_key);
        // 证书检测
        certificates_num++;
        // 解下一个证书
        offset += certificate_length;
        if (offset >= data_len) {
            break;
        }
    }
    p->ssl_cert_num[Directory] = certificates_num;
    p->ssl_cert_hash[Directory] = fast_writer.write(p->j_ssl_cert_hash[Directory]);
    p->ssl_cert_hash[Directory].pop_back();
    return;
}
string rdp_plugin::write_certificate_file(char *p_data, std::string str_key, uint32_t data_len,
                                          ssl_session *p_ssl_session) {
    string filename(cerfile_dir);
    filename += "/";
    DNUMTOSTR(p_ssl_session->thread_id, filename);
    filename += "/";
    filename += str_key.substr(0, 2);
    filename += "/";
    create_path(filename);
    filename += str_key;
    filename += ".cer";
    if (access(filename.c_str(), F_OK) == -1) {
        FILE *fp = fopen(filename.c_str(), "w+");
        if (fp == NULL) {
            printf("文件打开错误,filename:%s \n", filename.c_str());
            abort();
        }
        fwrite(p_data, data_len, 1, fp);
        fclose(fp);
    }
    return str_key;
}
void rdp_plugin::level_5_status_request_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session, int c_or_s) {
    ssl_message *p = p_ssl_session->p_ssl_message;
    p->ssl_f_extensions_5[c_or_s] = bin2string((const char *)(p_data), data_len);
}
void rdp_plugin::level_5_supported_groups_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session,
                                                int c_or_s) {
    char *p_cur = p_data;
    uint16_t offset = 0;
    ssl_message *p = p_ssl_session->p_ssl_message;

    if (offset + 2 > data_len) {
        return;
    }
    offset += 2;
    p->ssl_f_extensions_10[c_or_s] = "";
    for (; offset + 2 <= data_len; offset += 2) {
        string ssuit = bin2string((const char *)(p_cur + offset), 2);
        uint16_t suit = *(uint16_t *)(p_cur + offset);
        uint8_t *psuit = (uint8_t *)&suit;
        if (0x0a0a != (suit & 0x0f0f) || ((psuit[0] >> 4) != (psuit[1] >> 4))) {
            p->ssl_f_extensions_10[c_or_s] += ssuit;
        } else {
            p->ssl_f_extensions_10[c_or_s] += "gres";
        }
    }
}
void rdp_plugin::level_5_ec_point_formats_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session,
                                                int c_or_s) {
    char *p_cur = p_data;
    uint16_t offset = 0;
    ssl_message *p = p_ssl_session->p_ssl_message;

    if (offset + 1 > data_len) {
        return;
    }
    offset += 1;
    p->ssl_f_extensions_11[c_or_s] = "";
    if (data_len > 1) {
        p->ssl_f_extensions_11[c_or_s] = bin2string((const char *)(p_cur + offset), data_len - 1);
    }
}
void rdp_plugin::level_5_signature_algorithms_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session,
                                                    int c_or_s) {
    char *p_cur = p_data;
    uint16_t offset = 0;
    ssl_message *p = p_ssl_session->p_ssl_message;

    if (offset + 2 > data_len) {
        return;
    }
    offset += 2;
    p->ssl_f_extensions_13[c_or_s] = "";
    for (; offset + 2 <= data_len; offset += 2) {
        string ssuit = bin2string((const char *)(p_cur + offset), 2);
        uint16_t suit = *(uint16_t *)(p_cur + offset);
        uint8_t *psuit = (uint8_t *)&suit;
        if (0x0a0a != (suit & 0x0f0f) || ((psuit[0] >> 4) != (psuit[1] >> 4))) {
            p->ssl_f_extensions_13[c_or_s] += ssuit;
        } else {
            p->ssl_f_extensions_13[c_or_s] += "gres";
        }
    }
}

void rdp_plugin::level_4_extention_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session,
                                         int c_or_s) // c_or_s:  0是client 1是server
{
    char *p_cur = p_data;
    uint16_t offset = 0;
    ssl_message *p = p_ssl_session->p_ssl_message;

    uint32_t extentionnum = 0;
    Json::Value json_extention;
    Json::FastWriter fast_writer;
    p->ssl_f_extensions[c_or_s] = "";

    for (; offset + 4 <= data_len;) {
        extentionnum++;
        uint16_t type = ntohs(*(uint16_t *)(p_cur + offset));
        offset += 2;
        uint16_t length = ntohs(*(uint16_t *)(p_cur + offset));
        offset += 2;
        // 异常校验
        if (offset + length > data_len) {
            return;
        }
        string data = bin2string((const char *)(p_cur + offset), length);
        Json::Value json_tmp;
        json_tmp["t"] = type;
        json_tmp["l"] = length;
        json_tmp["v"] = data;
        json_extention.append(json_tmp);
        // 第二层结构解析
        switch (type) {
        case 0: // server_name
        {
            level_5_server_name_parse(p_cur + offset, length, p_ssl_session, c_or_s);
            break;
        }
        case 5: // status request
        {
            level_5_status_request_parse(p_cur + offset, length, p_ssl_session, c_or_s);
            break;
        }
        case 10: // Type: supported_groups (10)
        {
            level_5_supported_groups_parse(p_cur + offset, length, p_ssl_session, c_or_s);
            break;
        }
        case 11: // Type: ec_point_formats (11)
        {
            level_5_ec_point_formats_parse(p_cur + offset, length, p_ssl_session, c_or_s);
            break;
        }
        case 13: // Type: signature_algorithms (13)
        {
            level_5_signature_algorithms_parse(p_cur + offset, length, p_ssl_session, c_or_s);
            break;
        }
        case 16: // application_layer_protocol_negotiation
        {
            level_5_alpn_parse(p_cur + offset, length, p_ssl_session, c_or_s);
            break;
        }
        case 35: // session_ticket
        {
            level_5_session_ticket_parse(p_cur + offset, length, p_ssl_session, c_or_s);
            break;
        }
        }
        uint8_t *ptype = (uint8_t *)&type;
        if (0x0a0a != (type & 0x0f0f) || ((ptype[0] >> 4) != (ptype[1] >> 4))) {
            p->ssl_f_extensions[c_or_s] += bin2string((const char *)&type, 2);
        } else {
            p->ssl_f_extensions[c_or_s] += "gres";
        }

        // 解析完成，转到下一个块
        offset += length;
        if (offset >= data_len) {
            if (c_or_s == 0) {
                p->ssl_hello_c_extentionnum = extentionnum;
                p->ssl_hello_c_extention = fast_writer.write(json_extention);
                p->ssl_hello_c_extention.pop_back();
            } else if (c_or_s == 1) {
                p->ssl_hello_s_extentionnum = extentionnum;
                p->ssl_hello_s_extention = fast_writer.write(json_extention);
                p->ssl_hello_s_extention.pop_back();
            }
            return;
        }
    }
}
void rdp_plugin::level_5_alpn_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session, int c_or_s) {
    char *p_cur = p_data;
    uint16_t offset = 0;
    ssl_message *p = p_ssl_session->p_ssl_message;
    Json::Value json_info;
    Json::FastWriter fast_writer;

    if (offset + 2 > data_len) {
        return;
    }
    uint16_t alpn_extention_length = ntohs(*(uint16_t *)(p_cur + offset));
    offset += 2;
    for (; offset + 1 <= data_len;) {
        uint8_t alpn_string_length = *(uint8_t *)(p_cur + offset);
        offset += 1;
        if (offset + alpn_string_length > data_len) {
            break;
        }
        string protocol(p_cur + offset, alpn_string_length);
        json_info.append(protocol);
        offset += alpn_string_length;
        if (offset >= data_len) {
            break;
        }
    }
    if (c_or_s == 0) {
        p->ssl_hello_c_alpn = fast_writer.write(json_info);
        p->ssl_hello_c_alpn.pop_back();
        p->j_ssl_hello_c_alpn = json_info;
    } else if (c_or_s == 1) {
        p->ssl_hello_s_alpn = fast_writer.write(json_info);
        p->ssl_hello_s_alpn.pop_back();
    }
}
void rdp_plugin::level_5_server_name_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session, int c_or_s) {
    if (c_or_s != 0) {
        return;
    }
    char *p_cur = p_data;
    uint16_t offset = 0;
    ssl_message *p = p_ssl_session->p_ssl_message;
    if (offset + 5 > data_len) {
        return;
    }

    // uint16_t server_name_list_length = ntohs(*(uint16_t *)(p_cur+offset));
    offset += 2;
    p->ssl_hello_c_servernametype = *(uint8_t *)(p_cur + offset);
    offset += 1;
    uint16_t server_name_length = ntohs(*(uint16_t *)(p_cur + offset));
    offset += 2;
    if (offset + server_name_length > data_len) {
        return;
    }
    string ssl_hello_c_servername((p_cur + offset), server_name_length);
    p->ssl_hello_c_servername = ssl_hello_c_servername;
}
void rdp_plugin::level_5_session_ticket_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session, int c_or_s) {
    char *p_cur = p_data;
    uint16_t offset = 0;
    ssl_message *p = p_ssl_session->p_ssl_message;

    if (c_or_s == 0) {
        p->ssl_hello_c_sessionticket = bin2string((const char *)(p_cur + offset), data_len);
    } else if (c_or_s == 1) {
        p->ssl_hello_s_sessionticket = bin2string((const char *)(p_cur + offset), data_len);
    }
}

// 解析部分
bool rdp_plugin::potocol_parse_handle(session_pub *p_session, c_packet *p_packet) {
    uint32_t seq_tmp;
    if (p_session == NULL || p_packet == NULL)
        return false;
    proto_parse_session *p_tmp_mid = (proto_parse_session *)p_session->p_sp_session;
    void **pp_session = (void **)p_tmp_mid->expansion_data;
    rdp_session *p_rdp = (rdp_session *)pp_session[0];
    ssl_session *p_ssl_session = (ssl_session *)pp_session[1];
    if (NULL == p_ssl_session) {
        return false;
    }
    if (p_ssl_session->b_use_ssl[p_packet->Directory]) {
        if (p_ssl_session->do_ssl_pb == 0) {
            return true;
        }
        p_s_session = p_session;

        if (p_packet) {
            // 如果解析数据之后发现可以发送了，就返回true
            bool ret =
                level_1_data_parse(p_ssl_session->p_data, p_ssl_session->data_len, p_ssl_session, p_packet->Directory);
            // 解析完数据之后要将组包buff清理出来
            if (p_session->p_session_ext && p_session->p_session_ext->tcp_repcom[p_packet->Directory]) {
                p_session->p_tcp_recom_push_cb(p_session, p_packet->Directory);
                p_ssl_session->b_cacheing[p_packet->Directory] = false;
            }
            // 如果可以发送了，直接返回true
            if (ret) {
                return true;
            }
            // 如果是FIN包也发送，fix me
            if (false) {
                return true;
            }
        } else // release
        {
            if (p_session->p_session_ext && p_session->p_session_ext->tcp_repcom[0]) {
                p_session->p_session_ext->tcp_repcom[0]->front_fragment(seq_tmp, p_ssl_session->data_len,
                                                                        p_ssl_session->p_data);
                level_1_data_parse(p_ssl_session->p_data, p_ssl_session->data_len, p_ssl_session, 0);
                p_session->p_tcp_recom_push_cb(p_session, 0);
                p_ssl_session->b_cacheing[0] = false;
            }
            if (p_session->p_session_ext && p_session->p_session_ext->tcp_repcom[1]) {
                p_session->p_session_ext->tcp_repcom[1]->front_fragment(seq_tmp, p_ssl_session->data_len,
                                                                        p_ssl_session->p_data);
                level_1_data_parse(p_ssl_session->p_data, p_ssl_session->data_len, p_ssl_session, 1);
                p_session->p_tcp_recom_push_cb(p_session, 1);
                p_ssl_session->b_cacheing[1] = false;
            }
            return false;
        }

        return false;
    } else if (PROTOCOL_RDPV8 == p_packet->app_pro && NULL != p_rdp) {
        if (4 <= p_packet->app_len) {
            uint32_t offset = 0;
            uint8_t type = p_packet->app_buf[0];
            uint16_t length = p_packet->app_buf[2] + (p_packet->app_buf[3] << 8);
            uint8_t *cookie;
            if (0x01 == type && 0x08 == length) {
                return decode_rdp_nego_request(p_session, p_packet->app_buf, p_packet->app_len, offset);
            } else if (0x02 == type && 0x08 == length) {
                return decode_rdp_nego_response(p_session, p_packet->app_buf, p_packet->app_len, offset);
            } else if (cookie = key_cookie.Find(p_packet->app_buf, p_packet->app_buf + p_packet->app_len)) {
                uint8_t *end = key_0d0a.Find(cookie, p_packet->app_buf + p_packet->app_len);
                if (end) {
                    if (p_rdp->p_rdp_cookie) {
                        delete[] p_rdp->p_rdp_cookie;
                        p_rdp->p_rdp_cookie = NULL;
                    }
                    p_rdp->rdp_cookie_len = end - cookie - 8;
                    p_rdp->p_rdp_cookie = new uint8_t[p_rdp->rdp_cookie_len];
                    if (p_rdp->p_rdp_cookie) {
                        memcpy(p_rdp->p_rdp_cookie, cookie + 8, p_rdp->rdp_cookie_len);
                    }
                    p_rdp->flag |= 1;
                    offset = end + 2 - p_packet->app_buf;
                    if (offset + 4 <= p_packet->app_len) {
                        type = p_packet->app_buf[offset];
                        length = p_packet->app_buf[offset + 2] + (p_packet->app_buf[offset + 3] << 8);
                        if (0x01 == type || 0x08 == length) {
                            decode_rdp_nego_request(p_session, p_packet->app_buf, p_packet->app_len, offset);
                        }
                    }
                    return true;
                }
            } else {
                return decode_t125(p_session, p_packet->app_buf, p_packet->app_len, offset);
            }
        }
    }

    return false;
}

static string convert_utf16_to_utf8(string in) {
    int in_len = in.length() / 2;
    char *buf = new char[in_len * 4];
    int out_len = 0;
    int x, y, h, j, z, flag;
    for (int i = 0; i < in_len; i++) {
        flag = 0;
        y = in[i * 2];
        x = in[i * 2 + 1];
        h = (x << 8) | y;
        if ((h <= 0xD7FF) || (h >= 0xE000 && h <= 0xFFFF)) {
            flag = 1;
            z = h;
        }
        if (h >= 0xD800 && h <= 0xDBFF) {
            i++;
            if (i < in_len) {
                y = in[i * 2];
                x = in[i * 2 + 1];
                j = (x << 8) | y;
                if (j >= 0xDC00 && j <= 0xDFFF) {
                    flag = 1;
                    h -= 0xD800;
                    j -= 0xDC00;
                    z = (h << 10) | j;
                    z += 0x10000;
                }
            } else {
                flag = 0;
            }
        }
        if (!flag) {
            break;
        }
        if (z < 0x80) {
            buf[out_len++] = z;
        } else if (z < 0x800) {
            buf[out_len++] = (192 | z >> 6);
            buf[out_len++] = (128 | z & 63);

        } else if (z <= 0xFFFF) {
            if (z < 0xD800 || z > 0xDFFF) {
                buf[out_len++] = (224 | z >> 12);
                buf[out_len++] = (128 | z >> 6 & 63);
                buf[out_len++] = (128 | z & 63);
            }
        } else if (z <= 0x10FFFF) {
            buf[out_len++] = (240 | z >> 18);
            buf[out_len++] = (128 | z >> 12 & 63);
            buf[out_len++] = (128 | z >> 6 & 63);
            buf[out_len++] = (128 | z & 63);
        }
    }
    string out_str(buf, out_len);
    delete[] buf;
    return out_str;
}

static void rdp_cb_cs_version_major(uint8_t *buf, void *to_fill) {
    rdp_client_msg *p_rdp_client_msg = (rdp_client_msg *)to_fill;
    p_rdp_client_msg->set_version_major(*(uint16_t *)buf);
    return;
}
static void rdp_cb_cs_version_minor(uint8_t *buf, void *to_fill) {
    rdp_client_msg *p_rdp_client_msg = (rdp_client_msg *)to_fill;
    p_rdp_client_msg->set_version_minor(*(uint16_t *)buf);
    return;
}
static void rdp_cb_cs_desktop_width(uint8_t *buf, void *to_fill) {
    rdp_client_msg *p_rdp_client_msg = (rdp_client_msg *)to_fill;
    p_rdp_client_msg->set_desktop_width(*(uint16_t *)buf);
    return;
}
static void rdp_cb_cs_desktop_height(uint8_t *buf, void *to_fill) {
    rdp_client_msg *p_rdp_client_msg = (rdp_client_msg *)to_fill;
    p_rdp_client_msg->set_desktop_height(*(uint16_t *)buf);
    return;
}
static void rdp_cb_cs_color_depth(uint8_t *buf, void *to_fill) {
    rdp_client_msg *p_rdp_client_msg = (rdp_client_msg *)to_fill;
    p_rdp_client_msg->set_color_depth(*(uint16_t *)buf);
    return;
}
static void rdp_cb_cs_sas_sequence(uint8_t *buf, void *to_fill) {
    rdp_client_msg *p_rdp_client_msg = (rdp_client_msg *)to_fill;
    p_rdp_client_msg->set_sas_sequence(*(uint16_t *)buf);
    return;
}
static void rdp_cb_cs_keyboard_layout(uint8_t *buf, void *to_fill) {
    rdp_client_msg *p_rdp_client_msg = (rdp_client_msg *)to_fill;
    p_rdp_client_msg->set_keyboard_layout(*(uint16_t *)buf);
    return;
}
static void rdp_cb_cs_client_build(uint8_t *buf, void *to_fill) {
    rdp_client_msg *p_rdp_client_msg = (rdp_client_msg *)to_fill;
    p_rdp_client_msg->set_client_build(*(uint16_t *)buf);
    return;
}
static void rdp_cb_cs_client_name(uint8_t *buf, void *to_fill) {
    rdp_client_msg *p_rdp_client_msg = (rdp_client_msg *)to_fill;
    string tmp((char *)buf, 32);
    p_rdp_client_msg->set_client_name(convert_utf16_to_utf8(tmp));
    return;
}
static void rdp_cb_cs_keyboard_type(uint8_t *buf, void *to_fill) {
    rdp_client_msg *p_rdp_client_msg = (rdp_client_msg *)to_fill;
    p_rdp_client_msg->set_keyboard_type(*(uint32_t *)buf);
    return;
}
static void rdp_cb_cs_keyboard_subtype(uint8_t *buf, void *to_fill) {
    rdp_client_msg *p_rdp_client_msg = (rdp_client_msg *)to_fill;
    p_rdp_client_msg->set_keyboard_subtype(*(uint32_t *)buf);
    return;
}
static void rdp_cb_cs_keyboard_funckey(uint8_t *buf, void *to_fill) {
    rdp_client_msg *p_rdp_client_msg = (rdp_client_msg *)to_fill;
    p_rdp_client_msg->set_keyboard_funckey(*(uint32_t *)buf);
    return;
}
static void rdp_cb_cs_ime_filename(uint8_t *buf, void *to_fill) {
    rdp_client_msg *p_rdp_client_msg = (rdp_client_msg *)to_fill;
    string tmp((char *)buf, 64);
    p_rdp_client_msg->set_ime_filename(convert_utf16_to_utf8(tmp));
    return;
}
static void rdp_cb_cs_pb2_colordepth(uint8_t *buf, void *to_fill) {
    rdp_client_msg *p_rdp_client_msg = (rdp_client_msg *)to_fill;
    p_rdp_client_msg->set_pb2_colordepth(*(uint16_t *)buf);
    return;
}
static void rdp_cb_cs_client_productid(uint8_t *buf, void *to_fill) {
    rdp_client_msg *p_rdp_client_msg = (rdp_client_msg *)to_fill;
    p_rdp_client_msg->set_client_productid(*(uint16_t *)buf);
    return;
}
static void rdp_cb_cs_serial_num(uint8_t *buf, void *to_fill) {
    rdp_client_msg *p_rdp_client_msg = (rdp_client_msg *)to_fill;
    p_rdp_client_msg->set_serial_num(*(uint32_t *)buf);
    return;
}
static void rdp_cb_cs_high_colordepth(uint8_t *buf, void *to_fill) {
    rdp_client_msg *p_rdp_client_msg = (rdp_client_msg *)to_fill;
    p_rdp_client_msg->set_high_colordepth(*(uint16_t *)buf);
    return;
}
static void rdp_cb_cs_support_colordepth(uint8_t *buf, void *to_fill) {
    rdp_client_msg *p_rdp_client_msg = (rdp_client_msg *)to_fill;
    p_rdp_client_msg->set_support_colordepth(*(uint16_t *)buf);
    return;
}
static void rdp_cb_cs_early_cflags(uint8_t *buf, void *to_fill) {
    rdp_client_msg *p_rdp_client_msg = (rdp_client_msg *)to_fill;
    p_rdp_client_msg->set_early_cflags(*(uint16_t *)buf);
    return;
}
static void rdp_cb_cs_client_dproductid(uint8_t *buf, void *to_fill) {
    rdp_client_msg *p_rdp_client_msg = (rdp_client_msg *)to_fill;
    string tmp((char *)buf, 64);
    p_rdp_client_msg->set_client_dproductid(convert_utf16_to_utf8(tmp));
    return;
}
static void rdp_cb_cs_connection_type(uint8_t *buf, void *to_fill) {
    rdp_client_msg *p_rdp_client_msg = (rdp_client_msg *)to_fill;
    p_rdp_client_msg->set_connection_type(buf[0]);
    return;
}
static void rdp_cb_cs_pad1octet(uint8_t *buf, void *to_fill) {
    rdp_client_msg *p_rdp_client_msg = (rdp_client_msg *)to_fill;
    p_rdp_client_msg->set_pad1octet(buf[0]);
    return;
}
static void rdp_cb_cs_sselected_protocol(uint8_t *buf, void *to_fill) {
    rdp_client_msg *p_rdp_client_msg = (rdp_client_msg *)to_fill;
    p_rdp_client_msg->set_sselected_protocol(*(uint32_t *)buf);
    return;
}
static void rdp_cb_cs_encryption_methods(uint8_t *buf, void *to_fill) {
    rdp_client_msg *p_rdp_client_msg = (rdp_client_msg *)to_fill;
    p_rdp_client_msg->set_encryption_methods(*(uint32_t *)buf);
    return;
}
static void rdp_cb_cs_ext_encrytionmethod(uint8_t *buf, void *to_fill) {
    rdp_client_msg *p_rdp_client_msg = (rdp_client_msg *)to_fill;
    p_rdp_client_msg->set_ext_encrytionmethod(*(uint32_t *)buf);
    return;
}
static void rdp_cb_cs_cluster_flags(uint8_t *buf, void *to_fill) {
    rdp_client_msg *p_rdp_client_msg = (rdp_client_msg *)to_fill;
    p_rdp_client_msg->set_cluster_flags(*(uint32_t *)buf);
    return;
}
static void rdp_cb_cs_redirect_sessionid(uint8_t *buf, void *to_fill) {
    rdp_client_msg *p_rdp_client_msg = (rdp_client_msg *)to_fill;
    p_rdp_client_msg->set_redirect_sessionid(*(uint32_t *)buf);
    return;
}
static void rdp_cb_sc_version_major(uint8_t *buf, void *to_fill) {
    rdp_server_msg *p_rdp_server_msg = (rdp_server_msg *)to_fill;
    p_rdp_server_msg->set_version_major(*(uint16_t *)buf);
    return;
}
static void rdp_cb_sc_version_minor(uint8_t *buf, void *to_fill) {
    rdp_server_msg *p_rdp_server_msg = (rdp_server_msg *)to_fill;
    p_rdp_server_msg->set_version_minor(*(uint16_t *)buf);
    return;
}
static void rdp_cb_sc_encryption_method(uint8_t *buf, void *to_fill) {
    rdp_server_msg *p_rdp_server_msg = (rdp_server_msg *)to_fill;
    p_rdp_server_msg->set_encryption_method(*(uint32_t *)buf);
    return;
}
static void rdp_cb_sc_encryption_level(uint8_t *buf, void *to_fill) {
    rdp_server_msg *p_rdp_server_msg = (rdp_server_msg *)to_fill;
    p_rdp_server_msg->set_encryption_level(*(uint32_t *)buf);
    return;
}

typedef void (*rdp_callback_fn)(uint8_t *buf, void *to_fill);
typedef struct {
    uint32_t length;
    rdp_callback_fn func;
} rdp_callback_t;

static const rdp_callback_t rdp_cs_core_cb_sequence[] = {
    {2, rdp_cb_cs_version_major},   {2, rdp_cb_cs_version_minor},      {2, rdp_cb_cs_desktop_width},
    {2, rdp_cb_cs_desktop_height},  {2, rdp_cb_cs_color_depth},        {2, rdp_cb_cs_sas_sequence},
    {4, rdp_cb_cs_keyboard_layout}, {4, rdp_cb_cs_client_build},       {32, rdp_cb_cs_client_name},
    {4, rdp_cb_cs_keyboard_type},   {4, rdp_cb_cs_keyboard_subtype},   {4, rdp_cb_cs_keyboard_funckey},
    {64, rdp_cb_cs_ime_filename},   {2, rdp_cb_cs_pb2_colordepth},     {2, rdp_cb_cs_client_productid},
    {4, rdp_cb_cs_serial_num},      {2, rdp_cb_cs_high_colordepth},    {2, rdp_cb_cs_support_colordepth},
    {2, rdp_cb_cs_early_cflags},    {64, rdp_cb_cs_client_dproductid}, {1, rdp_cb_cs_connection_type},
    {1, rdp_cb_cs_pad1octet},       {4, rdp_cb_cs_sselected_protocol}};
static const rdp_callback_t rdp_cs_security_cb_sequence[] = {{4, rdp_cb_cs_encryption_methods},
                                                             {4, rdp_cb_cs_ext_encrytionmethod}};
static const rdp_callback_t rdp_cs_cluster_cb_sequence[] = {{4, rdp_cb_cs_cluster_flags},
                                                            {4, rdp_cb_cs_redirect_sessionid}};
static const rdp_callback_t rdp_sc_core_cb_sequence[] = {{2, rdp_cb_sc_version_major}, {2, rdp_cb_sc_version_minor}};
static const rdp_callback_t rdp_sc_security_cb_sequence[] = {{4, rdp_cb_sc_encryption_method},
                                                             {4, rdp_cb_sc_encryption_level}};

static void BinToHex(uint8_t *IN_pHexBuffer, uint32_t IN_HexBufferLen, unsigned char *OUT_pASCIIBuffer,
                     uint32_t IN_HexBufSize, uint32_t &OUT_ASCIIBufferLen) {
    DWORD i, temp;

    //
    OUT_ASCIIBufferLen = 0;

    for (i = 0; i < IN_HexBufferLen; i++) {
        OUT_pASCIIBuffer[OUT_ASCIIBufferLen] = 0;

        temp = (IN_pHexBuffer[i] >> 4) & 0xf;
        if (temp < 10) {
            if (OUT_ASCIIBufferLen >= IN_HexBufSize)
                return;
            OUT_pASCIIBuffer[OUT_ASCIIBufferLen++] = 0x30 + temp;
        } else {
            if (OUT_ASCIIBufferLen >= IN_HexBufSize)
                return;
            OUT_pASCIIBuffer[OUT_ASCIIBufferLen++] = 0x37 + temp;
        }
        temp = IN_pHexBuffer[i] & 0xf;
        if (temp < 10) {
            if (OUT_ASCIIBufferLen >= IN_HexBufSize)
                return;
            OUT_pASCIIBuffer[OUT_ASCIIBufferLen++] = 0x30 + temp;
        } else {
            if (OUT_ASCIIBufferLen >= IN_HexBufSize)
                return;
            OUT_pASCIIBuffer[OUT_ASCIIBufferLen++] = 0x37 + temp;
        }
    }

    OUT_pASCIIBuffer[OUT_ASCIIBufferLen] = 0;
}

void rdp_plugin::fill_rdp_msg(session_pub *p_session, JKNmsg *p_msg, rdp_session *p_rdp) {
    uint32_t offset = 0;
    if (p_msg && p_rdp) {
        p_msg->set_type(639);
        rdp_msg *p_rdp_msg = p_msg->mutable_rdp();
        Comm_msg *p_comm = p_rdp_msg->mutable_comm_msg();
        commsg_fill(p_session, p_comm, "10639", p_th_tools);
        rdp_client_msg *p_rdp_client_msg = p_rdp_msg->mutable_rdp_client();
        rdp_server_msg *p_rdp_server_msg = p_rdp_msg->mutable_rdp_server();
        rdp_negotiate_msg *p_rdp_negotiate_msg = p_rdp_msg->mutable_rdp_negotiate();
        if (p_rdp->p_cs_core) {
            offset = 0;
            for (int i = 0; (i < 23) && (offset + rdp_cs_core_cb_sequence[i].length <= p_rdp->cs_core_len); i++) {
                rdp_cs_core_cb_sequence[i].func(p_rdp->p_cs_core + offset, p_rdp_client_msg);
                offset += rdp_cs_core_cb_sequence[i].length;
            }
        }
        if (p_rdp->p_cs_security) {
            offset = 0;
            for (int i = 0; (i < 2) && (offset + rdp_cs_security_cb_sequence[i].length <= p_rdp->cs_security_len);
                 i++) {
                rdp_cs_security_cb_sequence[i].func(p_rdp->p_cs_security + offset, p_rdp_client_msg);
                offset += rdp_cs_security_cb_sequence[i].length;
            }
        }
        if (p_rdp->p_cs_net) {
            offset = 0;
            if (p_rdp->cs_net_len >= 4) {
                uint32_t channel_num = *(uint32_t *)p_rdp->p_cs_net;
                p_rdp_client_msg->set_channel_count(channel_num);
                offset += 4;
                for (int i = 0; (i < channel_num) && (offset + 12 <= p_rdp->cs_net_len); i++) {
                    rdp_channeldef *p_channeldef = p_rdp_client_msg->add_channeldef();
                    string chan_name((char *)(p_rdp->p_cs_net + offset), 8);
                    p_channeldef->set_name(chan_name);
                    offset += 8;
                    p_channeldef->set_option(*(uint32_t *)(p_rdp->p_cs_net + offset));
                    offset += 4;
                }
            }
        }
        if (p_rdp->p_cs_cluster) {
            offset = 0;
            for (int i = 0; (i < 2) && (offset + rdp_cs_cluster_cb_sequence[i].length <= p_rdp->cs_cluster_len); i++) {
                rdp_cs_cluster_cb_sequence[i].func(p_rdp->p_cs_cluster + offset, p_rdp_client_msg);
                offset += rdp_cs_cluster_cb_sequence[i].length;
            }
        }
        if (p_rdp->p_sc_core) {
            offset = 0;
            for (int i = 0; (i < 2) && (offset + rdp_sc_core_cb_sequence[i].length <= p_rdp->sc_core_len); i++) {
                rdp_sc_core_cb_sequence[i].func(p_rdp->p_sc_core + offset, p_rdp_server_msg);
                offset += rdp_sc_core_cb_sequence[i].length;
            }
        }
        if (p_rdp->p_sc_security) {
            offset = 0;
            for (int i = 0; (i < 2) && (offset + rdp_sc_security_cb_sequence[i].length <= p_rdp->sc_security_len);
                 i++) {
                rdp_sc_security_cb_sequence[i].func(p_rdp->p_sc_security + offset, p_rdp_server_msg);
                offset += rdp_sc_security_cb_sequence[i].length;
            }
            uint32_t ser_ramdom_len = 0, ser_cer_len = 0;
            if (p_rdp->sc_security_len >= 16) {
                ser_ramdom_len = *(uint32_t *)(p_rdp->p_sc_security + offset);
                p_rdp_server_msg->set_server_randomlen(ser_ramdom_len);
                offset += 4;
                ser_cer_len = *(uint32_t *)(p_rdp->p_sc_security + offset);
                p_rdp_server_msg->set_server_certlen(ser_cer_len);
                offset += 4;
            }
            if (ser_ramdom_len && offset + ser_ramdom_len <= p_rdp->sc_security_len) {
                uint8_t StrHax[2048];
                uint32_t strlen = 2048;
                uint32_t RealLen = 0;
                BinToHex((uint8_t *)p_rdp->p_sc_security + offset, ser_ramdom_len, StrHax, strlen, RealLen);
                p_rdp_server_msg->set_server_random((char *)StrHax, RealLen);
                offset += ser_ramdom_len;
                if (ser_cer_len && offset + ser_cer_len <= p_rdp->sc_security_len) {
                    RealLen = 0;
                    BinToHex((uint8_t *)p_rdp->p_sc_security + offset, ser_cer_len, StrHax, strlen, RealLen);
                    p_rdp_server_msg->set_server_cert((char *)StrHax, RealLen);
                    offset += ser_cer_len;
                }
            }
        }
        if (p_rdp->p_sc_net) {
            offset = 0;
            if (p_rdp->sc_net_len >= 2) {
                uint16_t mcs_channelid = *(uint16_t *)p_rdp->p_sc_net;
                p_rdp_server_msg->set_mcs_channelid(mcs_channelid);
                offset += 2;
                if (offset + 2 <= p_rdp->sc_net_len) {
                    uint16_t channel_num = *(uint16_t *)(p_rdp->p_sc_net + offset);
                    p_rdp_server_msg->set_channel_count(channel_num);
                    offset += 2;
                    for (int i = 0; (i < channel_num) && (offset + 2 <= p_rdp->sc_net_len); i++) {
                        p_rdp_server_msg->add_channel_id(*(uint16_t *)(p_rdp->p_sc_net + offset));
                        offset += 2;
                    }
                }
            }
        }
        if (p_rdp->p_rdp_cookie) {
            p_rdp_client_msg->set_cookie((char *)p_rdp->p_rdp_cookie, p_rdp->rdp_cookie_len);
        }
        if (p_rdp->flag & (1 << 2)) {
            p_rdp_client_msg->set_request_protocols(p_rdp->request_protocols);
            p_rdp_client_msg->set_rdp_c_flag(p_rdp->rdp_c_flag);
            for (auto it = rdp_sec_proto_str.begin(); it != rdp_sec_proto_str.end(); ++it) {
                if (p_rdp->request_protocols & it->first) {
                    p_rdp_negotiate_msg->add_c_requested_protocols(it->second);
                }
            }
            for (auto it = rdp_c_flags_str.begin(); it != rdp_c_flags_str.end(); ++it) {
                if (p_rdp->rdp_c_flag & it->first) {
                    p_rdp_negotiate_msg->add_c_flag_protocols(it->second);
                }
            }
        }
        if (p_rdp->flag & (1 << 3)) {
            p_rdp_server_msg->set_selected_protocols(p_rdp->selected_protocols);
            p_rdp_server_msg->set_rdp_s_flag(p_rdp->rdp_s_flag);
            for (auto it = rdp_sec_proto_str.begin(); it != rdp_sec_proto_str.end(); ++it) {
                if (p_rdp->selected_protocols & it->first) {
                    p_rdp_negotiate_msg->add_s_selected_protocols(it->second);
                }
            }
            for (auto it = rdp_s_flags_str.begin(); it != rdp_s_flags_str.end(); ++it) {
                if (p_rdp->rdp_s_flag & it->first) {
                    p_rdp_negotiate_msg->add_s_flag_protocols(it->second);
                }
            }
        }
    }
    return;
}

void rdp_plugin::ssl_msg_send(session_pub *p_session) // 发送
{
    proto_parse_session *p_tmp_mid = (proto_parse_session *)p_session->p_sp_session;
    void **pp_session = (void **)p_tmp_mid->expansion_data;
    rdp_session *p_rdp = (rdp_session *)pp_session[0];
    ssl_session *p_ssl_session = (ssl_session *)pp_session[1];

    if (p_ssl_session->do_ssl_pb == 0) {
        return;
    }
    ssl_message *p = p_ssl_session->p_ssl_message;

    if (p_ssl_session->b_send_over) {
        return;
    }

    //  ssl finger
    unsigned char aes_buf[16] = {0};
    Json::FastWriter writer;
    Json::Value json_c, json_s;
    string str_c = "", str_s = "";
    uint64_t finger_c = 0, finger_s = 0;
    char version_buf[11] = {0};
    if ("" != p->ssl_hello_c_ciphersuit && 0 != p->ssl_hello_c_version) {
        uint32_t ts_offset = 0;
        if (p->ssl_hello_s_time > p_session->session_basic.StartTime[0]) {
            ts_offset = p->ssl_hello_s_time - p_session->session_basic.StartTime[0];
        } else {
            ts_offset = p_session->session_basic.StartTime[0] - p->ssl_hello_s_time;
        }
        sprintf(version_buf, "%u", p->ssl_hello_s_version);
        str_s = string(version_buf) + "|" + p->ssl_hello_s_compressionmethod + "|" + p->ssl_f_csuite[1] + "|" +
                p->ssl_f_extensions[1] + "|" + ((ts_offset > 600) ? "1" : "0") + "|" + p->ssl_f_extensions_5[1] + "|" +
                p->ssl_f_extensions_10[1] + "|" + p->ssl_f_extensions_11[1] + "|" + p->ssl_f_extensions_13[1] + "|" +
                p->ssl_hello_s_alpn;
        // cout << "ssl_finger_c:" << str_c << endl;
        if (0 == AES_Hash(aes_hash_ctx, (unsigned char *)str_c.c_str(), str_c.length(), aes_buf)) {
            finger_c = *(uint64_t *)aes_buf;
            finger_c &= (uint64_t)0x7fffffffffffffff;
        }
    }
    if (p_session->p_session_ext) {
        p_session->p_session_ext->app_c_finger = finger_c;
    }
    if ("" != p->ssl_hello_s_ciphersuit && 0 != p->ssl_hello_s_version) {
        uint32_t ts_offset = 0;
        if (p->ssl_hello_s_time > p_session->session_basic.StartTime[0]) {
            ts_offset = p->ssl_hello_s_time - p_session->session_basic.StartTime[0];
        } else {
            ts_offset = p_session->session_basic.StartTime[0] - p->ssl_hello_s_time;
        }
        sprintf(version_buf, "%u", p->ssl_hello_s_version);
        str_s = string(version_buf) + "|" + p->ssl_hello_s_compressionmethod + "|" + p->ssl_f_csuite[1] + "|" +
                p->ssl_f_extensions[1] + "|" + ((ts_offset > 600) ? "1" : "0") + "|" + p->ssl_f_extensions_5[1] + "|" +
                p->ssl_f_extensions_10[1] + "|" + p->ssl_f_extensions_11[1] + "|" + p->ssl_f_extensions_13[1] + "|" +
                p->ssl_hello_s_alpn;
        // cout << "ssl_finger_s:" << str_s << endl;
        if (0 == AES_Hash(aes_hash_ctx, (unsigned char *)str_s.c_str(), str_s.length(), aes_buf)) {
            finger_s = *(uint64_t *)aes_buf;
            finger_s &= (uint64_t)0x7fffffffffffffff;
        }
    }
    if (p_session->p_session_ext) {
        p_session->p_session_ext->app_s_finger = finger_s;
    }
    //  ssl finger --end

    if (should_log) {
        JKNmsg *p_msg = p_session->p_value->get();
        if (p_msg == NULL) {
            p_ssl_session->b_send_over = true;
            return;
        }
        CJsonMsg *p_json_msg = p_session->p_value->jget();
        p_msg->set_type(29);
        ssl_msg *p_ssl = p_msg->mutable_ssl();
        Comm_msg *p_comm = p_ssl->mutable_comm_msg();
        // 公共信息部分
        commsg_fill(p_session, p_comm, "10639", p_th_tools);
        // ssl msg
        p_ssl->set_ssl_version(p->ssl_version);
        p_ssl->set_ssl_c_version(p->ssl_c_version);
        p_ssl->set_ssl_hello_c_version(p->ssl_hello_c_version);
        p_ssl->set_ssl_hello_c_time(p->ssl_hello_c_time);
        p_ssl->set_ssl_hello_c_random(p->ssl_hello_c_random);
        p_ssl->set_ssl_hello_c_sessionid(p->ssl_hello_c_sessionid);
        p_ssl->set_ssl_hello_c_sessionidlen(p->ssl_hello_c_sessionidlen);
        p_ssl->set_ssl_hello_c_ciphersuit(p->ssl_hello_c_ciphersuit);
        p_ssl->set_ssl_hello_c_ciphersuitnum(p->ssl_hello_c_ciphersuitnum);
        p_ssl->set_ssl_hello_c_compressionmethod(p->ssl_hello_c_compressionmethod);
        p_ssl->set_ssl_hello_c_compressionmethodlen(p->ssl_hello_c_compressionmethodlen);
        p_ssl->set_ssl_hello_c_extentionnum(p->ssl_hello_c_extentionnum);
        p_ssl->set_ssl_hello_c_extention(p->ssl_hello_c_extention);
        p_ssl->set_ssl_hello_c_alpn(p->ssl_hello_c_alpn);
        p_ssl->set_ssl_hello_c_servername(p->ssl_hello_c_servername);
        p_ssl->set_ssl_hello_c_servernametype(p->ssl_hello_c_servernametype);
        p_ssl->set_ssl_hello_c_sessionticket(p->ssl_hello_c_sessionticket);
        if (p_session->session_basic.Server == PACKETFROMCLIENT) {
            p_ssl->set_ssl_cert_c_num(p->ssl_cert_num[0]);
            p_ssl->set_ssl_cert_c_hash(p->ssl_cert_hash[0]);
            p_ssl->set_ssl_cert_s_num(p->ssl_cert_num[1]);
            p_ssl->set_ssl_cert_s_hash(p->ssl_cert_hash[1]);
        } else if (p_session->session_basic.Server == PACKETFROMSERVER) {
            p_ssl->set_ssl_cert_c_num(p->ssl_cert_num[1]);
            p_ssl->set_ssl_cert_c_hash(p->ssl_cert_hash[1]);
            p_ssl->set_ssl_cert_s_num(p->ssl_cert_num[0]);
            p_ssl->set_ssl_cert_s_hash(p->ssl_cert_hash[0]);
        }
        p_ssl->set_ssl_hello_s_version(p->ssl_hello_s_version);
        p_ssl->set_ssl_hello_s_time(p->ssl_hello_s_time);
        p_ssl->set_ssl_hello_s_random(p->ssl_hello_s_random);
        p_ssl->set_ssl_hello_s_sessionid(p->ssl_hello_s_sessionid);
        p_ssl->set_ssl_hello_s_sessionidlen(p->ssl_hello_s_sessionidlen);
        p_ssl->set_ssl_hello_s_cipersuite(p->ssl_hello_s_ciphersuit);
        p_ssl->set_ssl_hello_s_compressionmethod(p->ssl_hello_s_compressionmethod);
        p_ssl->set_ssl_hello_s_extentionnum(p->ssl_hello_s_extentionnum);
        p_ssl->set_ssl_hello_s_extention(p->ssl_hello_s_extention);
        p_ssl->set_ssl_hello_s_alpn(p->ssl_hello_s_alpn);
        p_ssl->set_ssl_hello_s_sessionticket(p->ssl_hello_s_sessionticket);

        p_ssl->set_ssl_s_newsessionticket_lifetime(p->ssl_s_newsessionticket_lifetime);
        p_ssl->set_ssl_s_newsessionticket_ticket(p->ssl_s_newsessionticket_ticket);
        p_ssl->set_ssl_s_newsessionticket_ticketlen(p->ssl_s_newsessionticket_ticketlen);
        p_ssl->set_ssl_c_keyexchangelen(p->ssl_c_keyexchangelen);
        p_ssl->set_ssl_c_keyexchange(p->ssl_c_keyexchange);
        p_ssl->set_ssl_s_keyexchangelen(p->ssl_s_keyexchangelen);
        p_ssl->set_ssl_s_keyexchange(p->ssl_s_keyexchange);

        p_ssl->set_ssl_c_finger(finger_c);
        p_ssl->set_ssl_s_finger(finger_s);
        session_pub_ssl ssl_msg;
        ssl_msg.ch_ciphersuit = p->ssl_hello_c_ciphersuit;
        ssl_msg.ch_ciphersuit_num = p->ssl_hello_c_ciphersuitnum;
        ssl_msg.ch_server_name = p->ssl_hello_c_servername;
        ssl_msg.ch_alpn = p->ssl_hello_c_alpn;
        if (p_session->session_basic.Server == PACKETFROMCLIENT) {
            ssl_msg.c_cert = p->ssl_cert_hash[0];
            ssl_msg.c_cert_num = p->ssl_cert_num[0];
            ssl_msg.s_cert = p->ssl_cert_hash[1];
            ssl_msg.s_cert_num = p->ssl_cert_num[1];
        } else if (p_session->session_basic.Server == PACKETFROMSERVER) {
            ssl_msg.c_cert = p->ssl_cert_hash[1];
            ssl_msg.c_cert_num = p->ssl_cert_num[1];
            ssl_msg.s_cert = p->ssl_cert_hash[0];
            ssl_msg.s_cert_num = p->ssl_cert_num[0];
        }
        session_pub_push_ssl(p_session, &ssl_msg);

        if (p_json_msg) {
            rapidjson::StringBuffer strBuf;
            rapidjson::Writer<rapidjson::StringBuffer> writer(strBuf);
            writer.StartObject();
            writer.Key("type");
            writer.Uint(156);
            writer.Key("SessionId");
            writer.String(p_comm->session_id().c_str(), p_comm->session_id().length());
            writer.Key("sIp");
            writer.String(p_comm->src_ip().c_str(), p_comm->src_ip().length());
            writer.Key("dIp");
            writer.String(p_comm->dst_ip().c_str(), p_comm->dst_ip().length());
            writer.Key("sPort");
            writer.Uint(p_comm->src_port());
            writer.Key("dPort");
            writer.Uint(p_comm->dst_port());
            writer.Key("StartTime");
            writer.Uint(p_comm->begin_time());
            writer.Key("CH_Ciphersuit");
            writer.String(p_ssl->ssl_hello_c_ciphersuit().c_str(), p_ssl->ssl_hello_c_ciphersuit().length());
            writer.Key("CH_ServerName");
            writer.String(p_ssl->ssl_hello_c_servername().c_str(), p_ssl->ssl_hello_c_servername().length());
            writer.Key("CH_ALPN");
            writer.StartArray();
            for (int idx = 0; idx < p->j_ssl_hello_c_alpn.size(); idx++) {
                writer.String(p->j_ssl_hello_c_alpn[idx].asString().c_str());
            }
            writer.EndArray();
            writer.Key("cSSLFinger");
            writer.String(std::to_string(p_ssl->ssl_c_finger()).c_str());
            writer.Key("sSSLFinger");
            writer.String(std::to_string(p_ssl->ssl_s_finger()).c_str());
            writer.Key("sCertHash");
            writer.StartArray();
            if (p_session->session_basic.Server == PACKETFROMCLIENT) {
                for (int idx = 0; idx < p->j_ssl_cert_hash[1].size(); idx++) {
                    writer.String(p->j_ssl_cert_hash[1][idx].asString().c_str());
                }
            } else if (p_session->session_basic.Server == PACKETFROMSERVER) {
                for (int idx = 0; idx < p->j_ssl_cert_hash[0].size(); idx++) {
                    writer.String(p->j_ssl_cert_hash[0][idx].asString().c_str());
                }
            }
            writer.EndArray();
            writer.Key("sNewSessionTicket_LifeTime");
            writer.Uint(p_ssl->ssl_s_newsessionticket_lifetime());
            writer.EndObject();
            p_json_msg->copy_buf((uint8_t *)strBuf.GetString(), strBuf.GetSize());
        }
    }
    p_ssl_session->b_send_over = true;
    return;
}

// 发送数据
void rdp_plugin::potocol_data_handle(session_pub *p_session, c_packet *p_packet) {
    if (p_session == NULL || p_packet == NULL)
        return;
    proto_parse_session *p_tmp_mid = (proto_parse_session *)p_session->p_sp_session;
    void **pp_session = (void **)p_tmp_mid->expansion_data;
    rdp_session *p_rdp = (rdp_session *)pp_session[0];
    ssl_session *p_ssl_session = (ssl_session *)pp_session[1];
    if (NULL == p_ssl_session) {
        return;
    }
    if (p_ssl_session->b_parse_over[0] && p_ssl_session->b_parse_over[1]) {
        ssl_msg_send(p_session);
    }
    if (p_rdp) {
        if ((p_rdp->flag & 1) && (p_rdp->flag & (1 << 1))) {
            if (should_log) {
                JKNmsg *p_msg = p_session->p_value->get();
                if (p_msg != NULL) {
                    fill_rdp_msg(p_session, p_msg, p_rdp);
                }
            }
            clear_rdp_session(p_session);
        }
    }

    return;
}

// 超时
bool rdp_plugin::time_out(session_pub *p_session, uint32_t check_time) {
    return true;
}
// 资源回收
void rdp_plugin::resources_recovery(session_pub *p_session) {
    if (p_session == NULL)
        return;
    proto_parse_session *p_tmp_mid = (proto_parse_session *)p_session->p_sp_session;
    void **pp_session = (void **)p_tmp_mid->expansion_data;
    rdp_session *p_rdp = (rdp_session *)pp_session[0];
    ssl_session *p_ssl_session = (ssl_session *)pp_session[1];

    if (p_ssl_session) {
        if (p_ssl_session->p_ssl_message != NULL && (p_ssl_session->b_use_ssl[0] || p_ssl_session->b_use_ssl[1])) {
            ssl_msg_send(p_session);
            delete p_ssl_session->p_ssl_message;
            p_ssl_session->p_ssl_message = NULL;
        }
    }
    if (p_rdp) {
        if (p_rdp->flag != 0) {
            if (should_log) {
                JKNmsg *p_msg = p_session->p_value->get();
                if (p_msg != NULL) {
                    fill_rdp_msg(p_session, p_msg, p_rdp);
                }
            }
        }
    }

    clear_rdp_session(p_session);
    clear_ssl_session(p_session);
    return;
}

void rdp_plugin::reload() {
    key_cookie.Init((unsigned char *)"cookie: ", 7, CASEINSENSITIVE);
    key_Duca.Init((unsigned char *)"duca", 4, CASEINSENSITIVE);
    key_McDn.Init((unsigned char *)"mcdn", 4, CASEINSENSITIVE);
    key_0d0a.Init((unsigned char *)"\r\n", 2, CASEINSENSITIVE);

    string conf_path = "/data/" + string(getenv("THE_TASKID")) + "/" + string(getenv("THE_BATCHID")) + "/cerfiles/";
    cerfile_dir = strdup(conf_path.c_str());
    return;
}

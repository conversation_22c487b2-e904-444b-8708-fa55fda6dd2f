#ifndef __RDP_PLUGIN_H__
#define __RDP_PLUGIN_H__

#include "DataStructure/QuickSearch.h"
#include "certfile_deduplicator.h"
#include "ssl_str.h"
#include <TH_engine_interface.h>
#include <c_hash.h>
#include <json/json.h>
#include <proto_parse_session.h>
#include <stdio.h>
#include <xml_parse.h>

#define PROTOCOL_RDPV8 1400 // 新的RDP
#define PROTOCOL_SSL 678    // SSL

using namespace std;
extern "C" {
int get_plugin_id();
session_pasre_base *attach();
};

typedef struct {
    uint16_t flag; // client 0x1, server 0x2, nego_requ 0x4, nego_resp 0x8
    uint16_t cs_core_len;
    uint16_t cs_security_len;
    uint16_t cs_net_len;
    uint16_t cs_cluster_len;
    uint16_t sc_core_len;
    uint16_t sc_security_len;
    uint16_t sc_net_len;
    uint8_t *p_cs_core;
    uint8_t *p_cs_security;
    uint8_t *p_cs_net;
    uint8_t *p_cs_cluster;
    uint8_t *p_sc_core;
    uint8_t *p_sc_security;
    uint8_t *p_sc_net;
    uint32_t request_protocols;
    uint32_t selected_protocols;
    uint8_t rdp_c_flag;
    uint8_t rdp_s_flag;
    uint16_t rdp_cookie_len;
    uint8_t *p_rdp_cookie;
} rdp_session;

class rdp_plugin : public session_pasre_base {
  public:
    rdp_plugin();
    ~rdp_plugin();
    virtual bool potocol_init(session_pub *p_sess, c_packet *p_pack);
    virtual bool potocol_sign_judge(session_pub *p_session, c_packet *p_pack);
    virtual bool potocol_parse_handle(session_pub *p_session, c_packet *p_packet);
    virtual void potocol_data_handle(session_pub *p_session, c_packet *p_packet);
    virtual bool time_out(session_pub *p_session, uint32_t check_time);
    virtual void resources_recovery(session_pub *p_session);
    virtual void reload();

  private:
    void fill_rdp_msg(session_pub *p_session, JKNmsg *p_msg, rdp_session *p_rdp);
    void clear_rdp_session(session_pub *p_session);
    bool decode_rdp_clientdata(session_pub *p_session, uint8_t *buf, uint32_t len, uint32_t &offset);
    bool decode_rdp_serverdata(session_pub *p_session, uint8_t *buf, uint32_t len, uint32_t &offset);
    bool decode_t124_guess(session_pub *p_sess, uint8_t *buf, uint32_t len, uint32_t &offset);
    bool decode_connect_inital(session_pub *p_sess, uint8_t *buf, uint32_t len, uint32_t &offset);
    bool decode_connect_response(session_pub *p_sess, uint8_t *buf, uint32_t len, uint32_t &offset);
    bool decode_t125(session_pub *p_sess, uint8_t *buf, uint32_t len, uint32_t &offset);
    bool decode_rdp_nego_request(session_pub *p_sess, uint8_t *buf, uint32_t len, uint32_t &offset);
    bool decode_rdp_nego_response(session_pub *p_sess, uint8_t *buf, uint32_t len, uint32_t &offset);

    CQuickSearch key_cookie;
    CQuickSearch key_Duca;
    CQuickSearch key_McDn;
    CQuickSearch key_0d0a;

    void clear_ssl_session(session_pub *p_session);
    // ssl
    string bin2hex(const char *p_data, uint32_t len);
    string bin2string(const char *p_data, uint32_t len);
    void init_ssl_session(ssl_session *p_ssl_session);
    bool data_is_whole(ssl_session *p_ssl_session);
    bool pkt_is_whole(c_packet *p_packet);
    bool level_1_data_parse(char *p_data, uint32_t data_len, ssl_session *p_ssl_session, uint8_t Directory);
    void level_2_handshake_protocol_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session, uint16_t version,
                                          uint8_t Directory);
    void level_3_client_hello_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session);
    void level_3_server_hello_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session);
    void level_3_new_session_ticket_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session);
    void level_3_client_key_exchange_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session);
    void level_3_server_key_exchange_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session);
    void level_3_certificate_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session, uint8_t Directory);
    string write_certificate_file(char *p_data, std::string str_key, uint32_t data_len, ssl_session *p_ssl_session);
    void level_4_extention_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session, int c_or_s);
    void level_5_server_name_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session, int c_or_s);
    void level_5_status_request_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session, int c_or_s);
    void level_5_supported_groups_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session, int c_or_s);
    void level_5_ec_point_formats_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session, int c_or_s);
    void level_5_signature_algorithms_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session, int c_or_s);
    void level_5_alpn_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session, int c_or_s);
    void level_5_session_ticket_parse(char *p_data, uint16_t data_len, ssl_session *p_ssl_session, int c_or_s);
    void ssl_msg_send(session_pub *p_session);
    void rdp_msg_send(session_pub *p_session);
    hash_t *backList_hash;
    void backList_parse();
    class back_cert_do {
      public:
        back_cert_do() {
            do_ssl_pb = 1;
            do_session_pb = 1;
        }
        uint16_t do_ssl_pb;     // ssl ?? ????
        uint16_t do_session_pb; // ????????
    };
    back_cert_do back;  // ???????
    back_cert_do white; // ???????
    session_pub *p_s_session;
    char *cerfile_dir;
    void *aes_hash_ctx;
    CCertfileDeduplicator *pDeduplicator;
    // ssl --end
};
#endif

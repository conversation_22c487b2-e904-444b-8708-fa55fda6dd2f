######################################
#
#
######################################
#target you can change test to what you want
#共享库文件名，lib*.so
TARGET  := librlogin.so
#compile and lib parameter
#编译参数
SRC     := ../src/rlogin
CC      := g++
LIBS    :=-L$(THE_SDK)/lib -liconv  -lpthread -ldl -lpcap -lxml2  -lrt -lprotobuf -ljson -lthe_protobuf
LDFLAGS :=
DEFINES :=  -std=c++11 -Wall -fPIC -g
INCLUDE := -I. -I$(THE_SDK)/include -I$(THE_SDK)/../lib_src/common_tool/src/basic_parse/
CFLAGS  :=  $(DEFINES) $(INCLUDE)
CXXFLAGS:= $(CFLAGS) -DHAVE_CONFIG_H
SHARE   :=  -shared  -o
#SHARE   :=   -o

ALIB    := $(THE_SDK)/lib/libcommontools.a
  
#i think you should do anything here
#下面的基本上不需要做任何改动了
  
#source file
#源文件，自动找所有.c和.cpp文件，并将目标定义为同名.o文件
SOURCE  := $(wildcard $(SRC)/*.c) $(wildcard $(SRC)/*.cpp)
OBJS    := $(patsubst %.c,%.o,$(patsubst %.cpp,%.o,$(SOURCE)))
  
.PHONY : everything objs clean veryclean rebuild
  
everything : $(TARGET)
  
all : $(TARGET)
  
objs : $(OBJS)
  
rebuild: veryclean everything
                
clean :
	rm -fr $(SRC)/*.o
	rm -fr $(TARGET)
  
$(TARGET) : $(OBJS)
	$(CC)  $(CXXFLAGS) $(OBJS) $(ALIB) $(SHARE) $@ $(LDFLAGS) $(LIBS)
install:
	cp -rf $(TARGET) $(THE_ROOT)/build/bin/plugin/proto_parse/

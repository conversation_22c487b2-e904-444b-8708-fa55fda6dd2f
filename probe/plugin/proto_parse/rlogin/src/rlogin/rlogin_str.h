/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-09-04 10:40:41
 * @LastEditors: x<PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-09-04 13:54:12
 * @Description:
 */

#ifndef RLOGIN_STR_H
#define RLOGIN_STR_H

#include <list>
#include <packet.h>
#include <session_pub.h>
#include <stdint.h>
#include <string>

using namespace std;

enum msg_parsed_type {
    USER_INFO_MSG,
    PASSWD_MSG,
    WIN_INFO_MSG,
};

enum pkt_direction {
    PKTFROMCLIENT = 1,
    PKTFROMESERVER,
};

class user_info_msg {
  public:
    user_info_msg() {
        client_user_name = "";
        server_user_name = "";
        term_type = "";
        term_speed = "";
        passwd = "";
    };
    string client_user_name; // client login name
    string server_user_name; // server login name
    string term_type;
    string term_speed;
    string passwd;
};

class win_info_msg {
  public:
    win_info_msg() {
        magic_cookie = 0;
        rows = 0;
        columns = 0;
        x_pixels = 0;
        y_pixels = 0;
    };
    uint16_t magic_cookie;
    uint16_t winsize_marker;
    uint16_t rows;
    uint16_t columns;
    uint16_t x_pixels;
    uint16_t y_pixels;
};

class rlogin_session {
  public:
    uint8_t *p_data;
    uint32_t data_len;
    uint32_t thread_id;
    user_info_msg *user_info;
    win_info_msg *win_info;
    enum pkt_direction pktdirec;
    bool msg_parse_done[4];
    bool msg_send_done;
};
#endif /*RLOGIN_STR_H*/

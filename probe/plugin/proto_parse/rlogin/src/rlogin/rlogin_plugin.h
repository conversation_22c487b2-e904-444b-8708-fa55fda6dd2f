/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-09-04 11:25:16
 * @LastEditors: x<PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-09-04 13:54:02
 * @Description:
 */

#ifndef RLOGIN_PLUGIN_H
#define RLOGIN_PLUGIN_H
#include "rlogin_str.h"
#include <TH_engine_interface.h>
#include <c_ip.h>
#include <iomanip>
#include <map>
#include <proto_parse_session.h>
#include <sstream>
#include <stdio.h>
#include <xml_parse.h>

using namespace std;

extern "C" {
int get_plugin_id();
session_pasre_base *attach();
};

#include "dnumstr.h"

class rlogin_plugin : public session_pasre_base {
  public:
    rlogin_plugin();
    ~rlogin_plugin();
    virtual void reload();
    virtual bool potocol_init(session_pub *p_sess, c_packet *p_pack);
    virtual bool potocol_sign_judge(session_pub *p_sess, c_packet *p_pack);
    virtual bool potocol_parse_handle(session_pub *p_sess, c_packet *p_packet);
    virtual void potocol_data_handle(session_pub *p_sess, c_packet *p_packet);
    virtual bool time_out(session_pub *p_sess, uint32_t check_time);
    virtual void resources_recovery(session_pub *p_sess);

  private:
    void rlogin_msg_send(session_pub *p_session);
    string bin2hex(const char *p_data, uint32_t len);
    string bin2string(const char *p_data, uint32_t len);
    string bin2ascii(const char *p_data, uint32_t len);
    void init_rlogin_session(rlogin_session *p_rlogin_session);
    void reset_rlogin_session(rlogin_session *p_rlogin_session);
    bool userinfo_data_parse(rlogin_session *p_rlogin_session);
    bool passwd_data_parse(rlogin_session *p_rlogin_session);
    bool wininfo_data_parse(rlogin_session *p_rlogin_session);
};
#endif /*RLOGIN_PLUGIN_H*/

/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-09-04 10:58:47
 * @LastEditors: x<PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-09-04 13:53:56
 * @Description:
 */

#include "rlogin_plugin.h"
#include <commit_tools.h>
#include <sys/stat.h>
#include <sys/time.h>

extern "C" {
int get_plugin_id() { return 10065; }
session_pasre_base *attach() { return new rlogin_plugin(); }
}

rlogin_plugin::rlogin_plugin() { reload(); }

rlogin_plugin::~rlogin_plugin() { ; }

void rlogin_plugin::reload() { ; }

string rlogin_plugin::bin2hex(const char *p_data, uint32_t len) {
    string s = "";

    if (p_data == NULL || len <= 0) {
        return "";
    }

    char formate_char[3];
    for (uint32_t i = 0; i < len; ++i) {
        sprintf(formate_char, "%02x", (unsigned char)p_data[i]);
        s.append(formate_char, 2);
    }
    return s;
}

string rlogin_plugin::bin2string(const char* p_data, uint32_t len) {
    // Check for null pointer
    if (p_data == nullptr) {
        return std::string();
    }
    
    // Check for valid length
    if (len == 0) {
        return std::string();
    }
    
    // Create string safely
    return std::string(p_data, len);
}

string rlogin_plugin::bin2ascii(const char *p_data, uint32_t len) {
    string s = "";

    if (p_data == NULL || len <= 0) {
        return "";
    }

    char format_char[3];
    for (uint32_t i = 0; i < len; ++i) {
        sprintf(format_char, "%c", (unsigned char)p_data[i]);
        s.append(format_char, 1);
    }
    return s;
}

void rlogin_plugin::init_rlogin_session(rlogin_session *p_rlogin_session) {

    if (p_rlogin_session == NULL) {
        return;
    }

    memset(p_rlogin_session, 0, sizeof(rlogin_session));
}

void rlogin_plugin::reset_rlogin_session(rlogin_session *p_rlogin_session) {

    if (p_rlogin_session == NULL) {
        return;
    }

    if (p_rlogin_session->user_info) {
        delete p_rlogin_session->user_info;
        p_rlogin_session->user_info = NULL;
    }
    if (p_rlogin_session->win_info) {
        delete p_rlogin_session->win_info;
        p_rlogin_session->win_info = NULL;
    }
}

bool rlogin_plugin::userinfo_data_parse(rlogin_session *p_rlogin_session) {
    string term_str;
    int offset = 0;
    int offset_arr[3] = {0};

    if (p_rlogin_session == NULL) {
        return false;
    }

    for (int i = 0; i < p_rlogin_session->data_len; i++) {
        if (p_rlogin_session->p_data[i] == 0x00) {
            offset_arr[offset] = i;
            offset++;
        }
    }

    p_rlogin_session->user_info->client_user_name = bin2ascii((const char *)p_rlogin_session->p_data, offset_arr[0]);
    p_rlogin_session->user_info->server_user_name = bin2ascii((const char *)p_rlogin_session->p_data + offset_arr[0] + 1, offset_arr[1] - offset_arr[0] - 1);
    term_str = bin2ascii((const char *)p_rlogin_session->p_data + offset_arr[1] + 1, offset_arr[2] - offset_arr[1] - 1);

    p_rlogin_session->user_info->term_type = term_str.substr(0, term_str.find("/"));
    p_rlogin_session->user_info->term_speed = term_str.substr(term_str.find("/") + 1);

    if (offset_arr[2] <= p_rlogin_session->data_len) // TODO:更完善的判断方式？
    {
        return true;
    } else {
        return false;
    }
}

bool rlogin_plugin::passwd_data_parse(rlogin_session *p_rlogin_session) {
    string passwd_prompt = "";

    if (p_rlogin_session == NULL) {
        return false;
    }

    passwd_prompt = bin2ascii((const char *)p_rlogin_session->p_data, p_rlogin_session->data_len);
    if (!strncmp(passwd_prompt.c_str(), "Password:", 9)) {
        return false;
    }

    if (p_rlogin_session->data_len == 1 && *(p_rlogin_session->p_data) != 0x0d) {
        p_rlogin_session->user_info->passwd.append(1, *(p_rlogin_session->p_data));
        return false;
    }

    if (p_rlogin_session->data_len == 2 && *((uint16_t *)p_rlogin_session->p_data) == 0x0d0a) { //\n\r indicate end
        return true;
    }
}

bool rlogin_plugin::wininfo_data_parse(rlogin_session *p_rlogin_session) {

    if (p_rlogin_session == NULL) {
        return false;
    }

    if (p_rlogin_session->pktdirec == PKTFROMESERVER && p_rlogin_session->data_len == 1) { // window size request
        return false;
    }

    if (p_rlogin_session->pktdirec == PKTFROMCLIENT && p_rlogin_session->data_len == 12) { // window info
        p_rlogin_session->win_info->magic_cookie = ntohs(*(uint16_t *)p_rlogin_session->p_data);
        p_rlogin_session->win_info->winsize_marker = ntohs(*((uint16_t *)p_rlogin_session->p_data + 1));
        p_rlogin_session->win_info->rows = ntohs(*((uint16_t *)p_rlogin_session->p_data + 2));
        p_rlogin_session->win_info->columns = ntohs(*((uint16_t *)p_rlogin_session->p_data + 3));
        p_rlogin_session->win_info->x_pixels = ntohs(*((uint16_t *)p_rlogin_session->p_data + 4));
        p_rlogin_session->win_info->y_pixels = ntohs(*((uint16_t *)p_rlogin_session->p_data + 5));
        return true;
    }
}

bool rlogin_plugin::potocol_init(session_pub *p_session, c_packet *p_packet) {
    if (p_session == NULL || p_packet == NULL) {
        return false;
    }
    proto_parse_session *p_pp_session = (proto_parse_session *)(p_session->p_sp_session);
    rlogin_session *p_rlogin_session = (rlogin_session *)p_pp_session->expansion_data;
    init_rlogin_session(p_rlogin_session);
    p_rlogin_session->thread_id = p_session->thread_id;
    p_rlogin_session->user_info = new user_info_msg;
    p_rlogin_session->win_info = new win_info_msg;
    return true;
}

bool rlogin_plugin::potocol_sign_judge(session_pub *p_session,
                                       c_packet *p_packet) // 组包
{
    if (p_session == NULL || p_packet == NULL) {
        return false;
    }
    proto_parse_session *p_pp_session = (proto_parse_session *)(p_session->p_sp_session);
    rlogin_session *p_rlogin_session = (rlogin_session *)p_pp_session->expansion_data;
    // 组包
    if (p_packet->app_data_len == 0) {
        return false;
    } else {
        p_rlogin_session->p_data = p_packet->p_app_data;
        p_rlogin_session->data_len = p_packet->app_data_len;
        if ((0 == p_packet->Directory && PACKETFROMCLIENT == p_session->session_basic.Server) || (1 == p_packet->Directory && PACKETFROMSERVER == p_session->session_basic.Server)) {
            p_rlogin_session->pktdirec = PKTFROMCLIENT;
        } else if ((0 == p_packet->Directory && PACKETFROMSERVER == p_session->session_basic.Server) || (1 == p_packet->Directory && PACKETFROMCLIENT == p_session->session_basic.Server)) {
            p_rlogin_session->pktdirec = PKTFROMESERVER;
        }
        return true;
    }

    return false;
}

bool rlogin_plugin::potocol_parse_handle(session_pub *p_session,
                                         c_packet *p_packet) // 解析
{
    if (p_session == NULL || p_packet == NULL) {
        return false;
    }
    proto_parse_session *p_pp_session = (proto_parse_session *)(p_session->p_sp_session);
    rlogin_session *p_rlogin_session = (rlogin_session *)p_pp_session->expansion_data;

    if (p_rlogin_session->data_len == 1 && *(p_rlogin_session->p_data) == 0x00) { // client start up flag or server info received flag
        return false;
    }

    if (!p_rlogin_session->msg_parse_done[USER_INFO_MSG]) {
        if (userinfo_data_parse(p_rlogin_session)) {
            p_rlogin_session->msg_parse_done[USER_INFO_MSG] = true;
        }
        return false;
    }

    if (p_rlogin_session->msg_parse_done[USER_INFO_MSG] && !p_rlogin_session->msg_parse_done[PASSWD_MSG]) {
        if (passwd_data_parse(p_rlogin_session)) {
            p_rlogin_session->msg_parse_done[PASSWD_MSG] = true;
        }
        return false;
    }

    if (p_rlogin_session->msg_parse_done[USER_INFO_MSG] && p_rlogin_session->msg_parse_done[PASSWD_MSG] && !p_rlogin_session->msg_parse_done[WIN_INFO_MSG]) {
        if (wininfo_data_parse(p_rlogin_session)) {
            p_rlogin_session->msg_parse_done[WIN_INFO_MSG] = true;
        }
    }

    enum msg_parsed_type t;
    for (t = USER_INFO_MSG; t <= WIN_INFO_MSG; t = (msg_parsed_type)(t + 1)) {
        if (!p_rlogin_session->msg_parse_done[t]) {
            return false;
        }
    }

    return true;
}

void rlogin_plugin::rlogin_msg_send(session_pub *p_session) // 发送
{
    if (NULL == p_session) {
        return;
    }

    proto_parse_session *p_pp_session = (proto_parse_session *)(p_session->p_sp_session);
    rlogin_session *p_rlogin_session = (rlogin_session *)p_pp_session->expansion_data;

    if (p_rlogin_session == NULL) {
        return;
    }

    if (p_rlogin_session->msg_send_done) { // only send once
        return;
    }

    if (should_log) {
        JKNmsg *p_msg = p_session->p_value->get();
        if (p_msg == NULL) {
            return;
        }
        p_msg->set_type(100); // TODO:应当遵循什么原则指定type的值？
        rlogin_msg *p_rlogin = p_msg->mutable_rlogin();
        Comm_msg *p_comm = p_rlogin->mutable_comm_msg();
        rlogin_wininfo_msg *p_wininfo = p_rlogin->mutable_wininfo();
        rlogin_userinfo_msg *p_userinfo = p_rlogin->mutable_userinfo();
        // 公共信息部分
        commsg_fill(p_session, p_comm, "10065", p_th_tools);

        ////rlogin msg
        p_wininfo->set_magic_cookie(p_rlogin_session->win_info->magic_cookie);
        p_wininfo->set_winsize_marker(p_rlogin_session->win_info->winsize_marker);
        p_wininfo->set_rows(p_rlogin_session->win_info->rows);
        p_wininfo->set_columns(p_rlogin_session->win_info->columns);
        p_wininfo->set_x_pixels(p_rlogin_session->win_info->x_pixels);
        p_wininfo->set_y_pixels(p_rlogin_session->win_info->y_pixels);
        p_wininfo->set_term_type(p_rlogin_session->user_info->term_type);
        p_wininfo->set_term_speed(p_rlogin_session->user_info->term_speed);

        p_userinfo->set_client_username(p_rlogin_session->user_info->client_user_name);
        p_userinfo->set_server_username(p_rlogin_session->user_info->server_user_name);
        p_userinfo->set_passwd(p_rlogin_session->user_info->passwd);
    }
    p_rlogin_session->msg_send_done = true;
    return;
}

void rlogin_plugin::potocol_data_handle(session_pub *p_session,
                                        c_packet *p_packet) // 发送
{
    if (p_session == NULL) {
        return;
    }
    rlogin_msg_send(p_session);
}

bool rlogin_plugin::time_out(session_pub *p_session, uint32_t check_time) {
    if (p_session) {
        return true;
    }
    return true;
}

void rlogin_plugin::resources_recovery(session_pub *p_session) {
    proto_parse_session *p_pp_session = (proto_parse_session *)(p_session->p_sp_session);
    rlogin_session *p_rlogin_session = (rlogin_session *)p_pp_session->expansion_data;

    reset_rlogin_session(p_rlogin_session);
    return;
}

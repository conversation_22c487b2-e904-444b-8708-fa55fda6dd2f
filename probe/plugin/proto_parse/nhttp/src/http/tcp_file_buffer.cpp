#include "tcp_file_buffer.h"
using namespace std;

int TcpFileBuffer::create_file(std::string name, char *ptr, std::uint32_t seq_raw, std::uint32_t size, bool chunked_mode)
{
    uint64_t start = 0x100000000ULL + seq_raw;
    return FileBuffer::create_file(name, ptr, Block(start, start+size), chunked_mode);
}

bool TcpFileBuffer::guess_seq_offset(std::uint32_t seq_raw, std::uint64_t &offset)
{
    uint32_t next_seq = (uint32_t) (next_offset & 0xffffffffULL);
    uint32_t distance1 = seq_raw-next_seq;
    uint32_t distance2 = next_seq-seq_raw;
    if (BUFFER_MAX_SIZE >= distance1)
    {
        offset = next_offset + distance1;
        return true;
    }
    else if (BUFFER_MAX_SIZE >= distance2)
    {
        if (next_offset >= distance2)
        {
            offset = next_offset - distance2;
            return true;
        }
    }
    return false;
}

int TcpFileBuffer::add_fragment(char *ptr, std::uint32_t seq_raw, std::uint32_t size)
{
    uint64_t start;
    if (0 == size)
    {
        return 0;
    }
    if (is_open())
    {
        if (guess_seq_offset(seq_raw, start))
        {
            return add_block(ptr, Block(start, start+size));
        }
        else
        {
            close_file();
            return -3;
        }
    }
    return -1;
}
#include <sys/stat.h>
#include <sys/types.h>
#include <cstring>
#include "file_buffer.h"

using namespace std;

static const char g_chunk_char[256] =
{
    0,0,0,0,0,0,0,0,0,0,3,0,0,2,0,0,
    0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
    0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
    1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,
    0,1,1,1,1,1,1,0,0,0,0,0,0,0,0,0,
    0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
    0,1,1,1,1,1,1,0,0,0,0,0,0,0,0,0,
    0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
    0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
    0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
    0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
    0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
    0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
    0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
    0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
    0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
};

void FileBuffer::ChunkedOFS::open( const std::string &filename, ios_base::openmode mode, bool chunked_mode)
{
    this->chunked_mode = chunked_mode;
    close();
    ofs.open(filename, mode);
    return;
}

bool FileBuffer::ChunkedOFS::is_open()
{
    return ofs.is_open();
}

void FileBuffer::ChunkedOFS::close()
{
    if (ofs.is_open())
    {
        ofs.close();
    }
    chunk_size_buff.clear();
    chunk_size_remaining = 0;
    state = BEGIN;
    return;
}

void FileBuffer::ChunkedOFS::write(char *p, uint64_t size)
{
    if (chunked_mode)
    {
        uint64_t offset = 0, size_to_write = 0;
        while (offset < size)
        {
            if (chunk_size_remaining)
            {
                size_to_write = min(chunk_size_remaining, (size-offset));
                ofs.write(&p[offset], size_to_write);
                chunk_size_remaining -= size_to_write;
                offset += size_to_write;
            }
            else
            {
                switch (g_chunk_char[(unsigned int)p[offset]])
                {
                    case 1:
                    {
                        if (BEGIN == state)
                        {
                            state = HEX;
                            chunk_size_buff.clear();
                            chunk_size_buff += p[offset];
                        }
                        else if (HEX == state)
                        {
                            chunk_size_buff += p[offset];
                        }
                        break;
                    }
                    case 2:
                    {
                        if (HEX == state)
                        {
                            state = CR;
                        }
                        break;
                    }
                    case 3:
                    {
                        if (CR == state)
                        {
                            char *end;
                            chunk_size_remaining = (uint64_t)strtoull(chunk_size_buff.c_str(), &end, 16);
                            state = BEGIN;
                        }
                        break;
                    }
                }
                offset++;
            }
        }
    }
    else
    {
        ofs.write(p, size);
    }
    return;
}

int FileBuffer::mkdirs(std::string name)
{
    string::size_type pos = 0;
    string pdir;
    while ((pos = name.find_first_of('/', pos)) != string::npos)
    {
        pdir = name.substr(0, pos+1);
        mkdir(pdir.c_str(), 0777);
        pos ++;
    }
    return 0;
}

FileBuffer::FileBuffer(std::uint32_t buffer_size)
{
    if (buffer_size < BUFFER_MIN_SIZE)
    {
        buffer_size = BUFFER_MIN_SIZE;
    }
    if (buffer_size > BUFFER_MAX_SIZE)
    {
        buffer_size = BUFFER_MAX_SIZE;
    }
    buffer.resize(buffer_size);
}

bool FileBuffer::block_is_expected(Block block, uint64_t &size_to_write) //判断当前数据块，是否可以写入，可以写入的数据长度
{
    if (block.start >= block.end)   //错包
    {
        size_to_write = 0;
        return true;
    }
    if (block.start > next_offset)  //未来的数据
    {
        size_to_write = 0;
        return false;
    }
    else    //预期的数据，或者重复数据
    {
        if (block.end > next_offset)    //预期的数据
        {
            size_to_write = block.end-next_offset;
        }
        else    //重复数据
        {
            size_to_write = 0;
        }
        return true;
    }
}

bool FileBuffer::block_cache_success(char *ptr, Block block)
{
    uint64_t offset_min, offset_max;
    if (block.start >= block.end)   //不需要缓存
    {
        return true;
    }
    if (cache_list.size())  //已存在缓存数据
    {
        offset_min = min(block.start, cache_list.front().start);
        offset_max = max(block.end, cache_list.back().end);
        if (offset_max - offset_min <= buffer.size())   //缓存够
        {
            if (cache_list.front().start > offset_min)  //新来的块最靠前，向后移动已缓存的数据
            {
                memmove(&buffer[cache_list.front().start-offset_min], &buffer[0], cache_list.back().end-cache_list.front().start);
            }
            memcpy(&buffer[block.start-offset_min], ptr, (block.end - block.start));

            auto riter = cache_list.rbegin();
            for (; riter != cache_list.rend(); riter ++)        //逆序判定与当前缓存块的结合性
            {
                if ((*riter).end < block.start) //大于且，不相交，后插入
                {
                    cache_list.insert(riter.base(), block);
                    return true;
                }
                else if ((*riter).start > block.end)    //小于且，不相交，检测下个节点
                {
                    continue;
                }
                else    //相交
                {
                    //更新分段
                    (*riter).start = min((*riter).start, block.start);
                    (*riter).end = max((*riter).end, block.end);
                    
                    //检测更新后的分段，与下个节点是否相交
                    auto riter_next = riter;
                    riter_next ++;
                    while (cache_list.rend() != riter_next)
                    {
                        if (min((*riter).end, (*riter_next).end) >= max((*riter).start, (*riter_next).start))   //相交
                        {
                            //更新分段
                            (*riter).start = min((*riter).start, (*riter_next).start);
                            (*riter).end = max((*riter).end, (*riter_next).end);
                            //删除下个节点
                            riter_next = decltype(riter_next)(cache_list.erase((++riter_next).base()));
                        }
                        else    //不相交，插入结束
                        {
                            break;
                        }
                    }
                    return true;
                }
            }
            //小于所有节点
            cache_list.push_front(block);
            return true;
        }
    }
    else
    {
        if (block.end - block.start <= buffer.size())   //缓存够
        {
            memcpy(&buffer[0], ptr, (block.end - block.start));
            cache_list.push_back(block);
            return true;
        }
    }
    return false;
}

int FileBuffer::add_block(char *ptr, Block block)
{
    uint64_t size_to_write, buffer_start;
    if (block.end <= block.start)   //没有数据需要写入
    {
        return 0;
    }
    if (ofs.is_open())  //添加数据
    {
        if (block_is_expected(block, size_to_write))    //预期的数据，或者重复数据
        {
            if (size_to_write)
            {
                ofs.write(ptr+(next_offset-block.start), size_to_write);
                next_offset = block.end;
                while (cache_list.size())   //检查缓存的数据是否可以写入
                {
                    if (block_is_expected(cache_list.front(), size_to_write))   //预期的数据，或者重复数据
                    {
                        buffer_start = cache_list.front().start;
                        if (size_to_write)
                        {
                            ofs.write(&buffer[(next_offset-buffer_start)], size_to_write);
                            next_offset = cache_list.front().end;
                        }
                        cache_list.pop_front();
                        if (cache_list.size())
                        {
                            memmove(&buffer[0], &buffer[cache_list.front().start-buffer_start], cache_list.back().end - cache_list.front().start);
                        }
                    }
                    else
                    {
                        break;
                    }
                }
            }
            return 0;
        }
        else    //非预期数据，先缓存下来
        {
            if (block_cache_success(ptr, block))  //成功缓存
            {
                return 0;
            }
            else    //缓存失败
            {
                ofs.close();
                cache_list.clear();
                return -2;
            }
        }
    }
    else    //文件还原失败
    {
        return -1;
    }
}

int FileBuffer::close_file()
{
    if (ofs.is_open())
    {
        ofs.close();
    }
    cache_list.clear();
    return 0;
}

int FileBuffer::create_file(std::string name, char *ptr, Block block, bool chunked_mode)
{
    close_file();
    // mkdirs(name);    //取消建立文件夹操作，所有文件夹预先建立完成
    ofs.open(name, ios::binary|ios::out, chunked_mode);
    if (ofs.is_open())
    {
        next_offset = block.start;
        return add_block(ptr, block);
    }
    return -1;
}




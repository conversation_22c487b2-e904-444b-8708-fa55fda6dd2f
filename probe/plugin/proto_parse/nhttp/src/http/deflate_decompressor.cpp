#include <cstdlib>
#include <cstring>
#include <stdexcept>

#include "deflate_decompressor.h"

using namespace std;


const uInt DeflateDecompressor::def_buffer_size = 512;

voidpf DeflateDecompressor::zalloc(voidpf opaque, uInt items, uInt size)
{
    return calloc(items, size);
}

void DeflateDecompressor::zfree(voidpf opaque, voidpf address)
{
    free(address);
    return;
}

DeflateDecompressor::DeflateDecompressor(int windowBits, uInt buffer_size)
{
    memset(&stream, 0, sizeof(z_stream));
    stream.zalloc = zalloc;
    stream.zfree = zfree;
    stream.opaque = (voidpf)0;
    
    if (6 >= buffer_size)
    {
        this->buffer_size = def_buffer_size;
    }
    else
    {
        this->buffer_size = buffer_size;
    }
    buffer.resize(this->buffer_size);
    if (Z_OK != inflateInit2(&stream, windowBits))
    {
        throw runtime_error("inflateInit2 fail");
    }
}

int DeflateDecompressor::decompress_until_the_end(Bytef *in, uInt in_size, vector<Bytef> &out)
{
    int ret;
    
    out.clear();
    ret = inflateReset(&stream);
    if (Z_OK != ret)
    {
        return ret;
    }
    if (in_size)
    {
        out.reserve(in_size / 4);
        
        stream.next_in = in;
        stream.avail_in = in_size;
        for (;;)
        {
            stream.next_out = &buffer[0];
            stream.avail_out = buffer_size;
            ret = inflate(&stream, Z_NO_FLUSH);
            if ((Z_OK != ret) && (Z_STREAM_END != ret))
            {
                return ret;
            }
            else
            {
                out.insert(out.end(), buffer.begin(), buffer.begin()+(buffer_size-stream.avail_out));
                if (Z_STREAM_END == ret)
                {
                    break;
                }
            }
        }
        
        return inflateReset(&stream);
    }
    return Z_OK;
}

int DeflateDecompressor::decompress_begin()
{
    return inflateReset(&stream);
}

int DeflateDecompressor::decompress_add(Bytef *in, uInt in_size, vector<Bytef> &out)
{
    int ret;
    
    out.clear();
    if (in_size)
    {
        out.reserve(in_size / 4);
        
        stream.next_in = in;
        stream.avail_in = in_size;
        for (;stream.avail_in;)
        {
            stream.next_out = &buffer[0];
            stream.avail_out = buffer_size;
            ret = inflate(&stream, Z_NO_FLUSH);
            if ((Z_OK != ret) && (Z_STREAM_END != ret))
            {
                return ret;
            }
            else
            {
                out.insert(out.end(), buffer.begin(), buffer.begin()+(buffer_size-stream.avail_out));
                if (Z_STREAM_END == ret)
                {
                    break;
                }
            }
        }
        return Z_OK;
    }
    return Z_OK;
}
int DeflateDecompressor::decompress_end(Bytef *in, uInt in_size, vector<Bytef> &out)
{
    int ret;
    
    out.clear();
    
    out.reserve(in_size / 4);
    
    stream.next_in = in;
    stream.avail_in = in_size;
    for (;;)
    {
        stream.next_out = &buffer[0];
        stream.avail_out = buffer_size;
        ret = inflate(&stream, Z_NO_FLUSH);
        if ((Z_OK != ret) && (Z_STREAM_END != ret))
        {
            return ret;
        }
        else
        {
            out.insert(out.end(), buffer.begin(), buffer.begin()+(buffer_size-stream.avail_out));
            if (Z_STREAM_END == ret)
            {
                break;
            }
        }
    }
    return inflateReset(&stream);
}
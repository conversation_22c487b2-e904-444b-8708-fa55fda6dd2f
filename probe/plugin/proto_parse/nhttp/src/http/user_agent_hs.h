// Last Update:2020-12-03 14:32:21
/**
 * @file user_agent_hs.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2020-12-02
 */

#ifndef USER_AGENT_HS_H
#define USER_AGENT_HS_H
#include <string>
#include <hs.h>
#include <string.h>
#include <iostream>
#include <fstream>
#include <stdio.h>
#include <vector>
#include <json/json.h>
#define OS_UNKNOWN  0
#define OS_WINDOWS 1
#define OS_MACOS   2
#define OS_LINUX   3
#define  OS_IOS    4
#define  OS_ANDROND  5
#define BROWER 7
using namespace std;
class user_agent_hs {
    public:
        user_agent_hs()
        {

        }
        user_agent_hs(std::string jsonfile) 
        {
            init(jsonfile );
        }
        void init(std::string jsonfile) 
        {
            b_init_suc = true;
            Json::Reader reader;
            Json::Value root;

            hs_error_t err;
            ifstream in(jsonfile.c_str(), std::ios::binary);
            if( !in.is_open() )
            {
                b_init_suc = false;
                return;

            }
            if(reader.parse(in,root))
            {

                std::vector<const char *> expressions ;
                std::vector<unsigned> flags ;
                std::vector<unsigned> ids;
                int sz = root.size();
                int mode = HS_MODE_BLOCK ;
                for (int i = 0 ; i < sz ; i++ )
                {
                    std::string s_type = root[i]["type"].asString();
                    std::string   expressions_str  = root[i]["expressions"].asString();
                    char * p = new  char[expressions_str.length()];
                    memcpy(p,expressions_str.c_str() , expressions_str.length()) ;
                    expressions.push_back( p) ;
                    printf("%s\n",p);
                    flags.push_back(HS_FLAG_CASELESS|HS_FLAG_SINGLEMATCH);
                    if (s_type == "OS")
                    {
                        std::string os =  root[i]["os"].asString();
                        if (os == "windows") 
                        {
                            ids.push_back(OS_WINDOWS);
                        } 
                        else if (os == "macos") 
                        {
                            ids.push_back(OS_MACOS) ;
                        }
                        else if (os == "linux") 
                        {
                            ids.push_back(OS_LINUX);
                        }
                        else if (os == "ios") 
                        {
                            ids.push_back(OS_IOS);
                        }
                        else if (os == "android")
                        {
                            ids.push_back(OS_ANDROND);
                        }

                    }
                    else if (s_type == "Brower")
                    {
                        ids.push_back(BROWER);
                    }
                }
                hs_compile_error_t *compileErr;

 //               Clock clock;
 //               clock.start();

                err = hs_compile_multi(expressions.data(), flags.data(), ids.data(),
                        expressions.size(), mode, NULL, &db, &compileErr);

  //              clock.stop();

                if (err != HS_SUCCESS) {
                    printf("********************\n");
                    if (compileErr->expression < 0) {
                        // The error does not refer to a particular expression.
                        std::cerr << "ERROR: " << compileErr->message << std::endl;
                    } else {
                        std::cerr << "ERROR: Pattern '" << expressions[compileErr->expression]
                            << "' failed compilation with error: " << compileErr->message
                            << std::endl;
                    }
                    // As the compileErr pointer points to dynamically allocated memory, if
                    // we get an error, we must be sure to release it. This is not
                    // necessary when no error is detected.
                    hs_free_compile_error(compileErr);
                    b_init_suc = false ;
                    for (int i = 0 ; i < expressions.size() ; i++) 
                    {
                        delete expressions[i];
                    }
                    expressions.clear();
                    return ;
                }
                for (int i = 0 ; i < expressions.size() ; i++) 
                {
                    delete expressions[i];
                }
                expressions.clear();
            }
            // 分酝空间 
            scratch_prototype = NULL;
            err = hs_alloc_scratch(db, &scratch_prototype);
            if (err != HS_SUCCESS) {
                printf("hs_alloc_scratch failed!   %d \n",err);
                b_init_suc = false ;
                return ;
            }
        }
        void scan_handle(std::string pkt, int &os_type , bool &Brower_type , bool & phone)
        {
             ret_num m(os_type,Brower_type , phone);

             hs_error_t err = hs_scan(db, pkt.c_str(), pkt.length(), 0,
                                     scratch_prototype, onMatch, &m);
        }
        static int onMatch (unsigned int id  , unsigned long long from  , unsigned long long to  , unsigned int flags , void * context ) 
        {
            // printf("******** onMatch ************\n");
            // printf("id= = %d ,flags = %d \n",id ,flags);
            ret_num * p_ret_num = (ret_num *) context ;
            if (id  <  BROWER   ) 
            {
                *(p_ret_num -> i_os_type)  =  id ;
                if (*(p_ret_num -> i_os_type) >3 ) 
                {
                    *(p_ret_num -> b_phone) = true;
                }
            } 
            else 
            {
                *(p_ret_num -> b_Brower_type) = true;
            }
            return 0 ; 
        }
    private:
        hs_database_t *db;
        bool b_init_suc ;
        hs_scratch_t *scratch_prototype;
        class   ret_num {
            public :
                ret_num(int &os_type , bool &Brower_type , bool & phone)
                {
                    os_type = OS_UNKNOWN ; 
                    Brower_type = false; 
                    phone = false ;
                    i_os_type = &os_type ;
                    b_Brower_type = &Brower_type ; 
                    b_phone = &phone ;


                }
            int * i_os_type ; 
            bool * b_Brower_type ; 
            bool * b_phone;
        };
};

#endif  /*USER_AGENT_HS_H*/

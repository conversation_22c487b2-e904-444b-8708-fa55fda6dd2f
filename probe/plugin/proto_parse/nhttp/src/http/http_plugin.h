// Last Update:2018-10-16 21:04:17
/**
 * @file http_plugin.h
 * @brief head file of http plugin.
 * <AUTHOR> @version 0.1.00
 * @date 2018-10-10
 */

#ifndef HTTP_PLUGIN_H
#define HTTP_PLUGIN_H

#include <TH_engine_interface.h>

#include "http_str.h"
#include "Interface_HTTP.h"
#include "user_agent_hs.h"
#include "file_fmt_interface.h"
#include "deflate_decompressor.h"

extern "C" {
    int get_plugin_id();
    session_pasre_base* attach();
};

////HTTP
#define PROTOCOL_HTTP                   263		//HTTP全部 
#define PROTOCOL_HTTP_OPTIONS           792		//返回服务器正对特点资源所支持的HTTP
#define PROTOCOL_HTTP_HEAD              793		//向服务器索要与GET
#define PROTOCOL_HTTP_GET				794		//向特定资//
#define PROTOCOL_HTTP_POST				795		//
#define PROTOCOL_HTTP_PUT				796		//
#define PROTOCOL_HTTP_DELETE			797		//请求服务器删除Request-url
#define PROTOCOL_HTTP_TRACE				798		//
#define PROTOCOL_HTTP_CONNECT			799		//
#define PROTOCOL_HTTP_PATCH				800		//




class http_plugin: public session_pasre_base
{
public:
    http_plugin();
    ~http_plugin();

    bool potocol_init(session_pub* p_sess, c_packet* p_pack);
    bool potocol_sign_judge(session_pub* p_session, c_packet* p_pack);
    bool potocol_parse_handle(session_pub* p_session, c_packet* p_packet);
    void potocol_data_handle(session_pub* p_session, c_packet * p_packet);
    bool time_out(session_pub* p_session, uint32_t check_time);
    void resources_recovery(session_pub* p_session);
    void reload();

private:
    bool potocol_parse_handle(session_pub* p_session, c_packet* p_packet, uint8_t packet_directory, char *payload, uint32_t length);
    int judge_need_pkt_cache(session_pub* p_session, uint8_t Directory, char *payload, uint32_t length);
    void init_http_session(http_session * p_http_session);
    void clear_http_session(http_session * p_http_session);
    bool request_head_parse(char*, uint32_t, http_session*);
    bool response_head_parse(char*, uint32_t, http_session*);
    string decompress_or_dechunk(int compress_type, int chunked, char *payload_start, uint32_t length);

    DeflateDecompressor deflate_decompressor;
    DeflateDecompressor gzip_decompressor;
    
    HttpContentJudge content_judge;
    user_agent_hs ua_engine;
    string file_dir;
    void *aes_hash_ctx;
    void *p_fmt;
    std::uint8_t b_file_recover;
};

#endif  /*HTTP_PLUGIN_H*/

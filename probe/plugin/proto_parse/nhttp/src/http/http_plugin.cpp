// Last Update:2019-04-15 18:44:26
/**
 * @file http_plugin.cpp
 * @brief http plugin parse.
 * <AUTHOR> @version 0.1.00
 * @date 2018-10-10
 */

#define _GNU_SOURCE         /* See feature_test_macros(7) */
#include <string.h>
#include "http_plugin.h"
#include <commit_tools.h>
#include <CConvCharSet.h>
#include <arpa/inet.h>
#include <algorithm>
#include <functional>
#include "Interface_HTTP.h"
#include "crc64.h"
#include "aes_hash.h"
#include "rapidjson/stringbuffer.h"
#include "rapidjson/writer.h"
#include "proto_parse_session.h"

using namespace std;

static const unsigned char g_chunk_char[256] =
{
    0,0,0,0,0,0,0,0,0,0,3,0,0,2,0,0,
    0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
    0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
    1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,
    0,1,1,1,1,1,1,0,0,0,0,0,0,0,0,0,
    0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
    0,1,1,1,1,1,1,0,0,0,0,0,0,0,0,0,
    0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
    0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
    0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
    0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
    0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
    0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
    0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
    0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
    0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
};

extern "C" {
    int get_plugin_id()
    {
        return 10637;
    }
    session_pasre_base* attach( )
    {
        return new  http_plugin();
    }
}

static const char *http_header_end_sign = "\r\n\r\n";
static const int http_header_max_size = 8192;       //最大缓存8k

static const string crlf = "\r\n";
static const string ua_key = "User-Agent";
static const string host_key = "Host";
static const string ce_key = "Content-Encoding";
static const string te_key = "Transfer-Encoding";
static const string xff_key = "X-Forwarded-For";
static const string cookie_key = "Cookie";
static const string referer_key = "Referer";
static const string server_key = "Server";


static const char* const lut = "0123456789ABCDEF";
static string HexToAscII(string &input)
{
    string output;
    output.reserve(2 * input.size());
    for (auto i = 0; i < input.size(); i ++)
    {
        const unsigned char c = input[i];
        output.push_back(lut[c >> 4]);
        output.push_back(lut[c & 15]);
    }
    return output;
}
static string HexToAscII(char input)
{
    string output;
    output.reserve(2);
    const unsigned char c = (const unsigned char)input;
    output.push_back(lut[c >> 4]);
    output.push_back(lut[c & 15]);
    return output;
}


http_plugin::http_plugin():deflate_decompressor(-MAX_WBITS),gzip_decompressor(16+MAX_WBITS)
{
    p_fmt = NULL;
    reload();
    content_judge.init();
    aes_hash_ctx = AES_InitKey();
    if(NULL == aes_hash_ctx)
    {
        exit(-1);
    }
}

//解析插件自身的配置文件
void http_plugin::reload()
{
    xmlDocPtr doc;
    xmlNodePtr curNode;

    b_file_recover = 0;

    string conf_path = string(getenv("THE_CONF_PATH")) + "/http_plugin.xml";
    doc = xmlReadFile(conf_path.c_str(), "UTF-8", XML_PARSE_RECOVER);
    curNode = xmlDocGetRootElement(doc);
    if (0 != xmlStrcmp(curNode->name, (xmlChar*) "config"))
    {
        abort();
        return;
    }
    curNode = curNode->xmlChildrenNode;
    for(; curNode != NULL; curNode = curNode->next)
    {
        if( xmlStrcmp(curNode->name, (xmlChar*)"file_recover") == 0 )
        {
            if (string((char *)xmlNodeGetContent(curNode)) == "true")
            {
                b_file_recover = 1;
            }
        }
    }
    conf_path = string(getenv("THE_DB_PATH")) + "/ua.json";
    ua_engine.init(conf_path);
    if(p_fmt)
    {
        file_fmt_engine_release(p_fmt);
        p_fmt = NULL;
    }
    p_fmt = file_fmt_engine_load(NULL);
    file_dir = "/files/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/";
    return;
}

http_plugin::~http_plugin()
{
    AES_CleanUp(aes_hash_ctx);
    content_judge.quit();
}

bool http_plugin::potocol_init(session_pub* p_session, c_packet* p_packet)
{
    if( p_session == NULL || p_packet == NULL)
        return false;
    proto_parse_session* p_tmp_mid = (proto_parse_session*)p_session->p_sp_session;
    http_session* p_http_session = (http_session*)p_tmp_mid->expansion_data;
    init_http_session(p_http_session);
    return true;
}
void http_plugin::init_http_session(http_session * p_http_session)
{
    p_http_session->p_http_message = new http_message();
}

void http_plugin::clear_http_session(http_session * p_http_session)
{
    p_http_session->p_http_message->clear();
}

//判断是否需要组包
int http_plugin::judge_need_pkt_cache(session_pub* p_session, uint8_t Directory, char *payload, uint32_t length)
{
    proto_parse_session* p_tmp_mid = (proto_parse_session*)p_session->p_sp_session;
    http_session* p_http_session = (http_session*)p_tmp_mid->expansion_data;
    char *payload_end = payload + length;

    bool b_resp = ((PACKETFROMCLIENT==p_http_session->p_http_message->server_direction && 1 == Directory)
            ||(PACKETFROMSERVER==p_http_session->p_http_message->server_direction && 0 == Directory));
    char *patten = (char *)http_header_end_sign;
    char *patten_end = patten + 4;
    char *ret = 0;

    if (0 == payload || 0 == length)   //地址非法
    {
        return 0;
    }
    else if (4 > length)     //负载太短，异常现象
    {
        return 0;
    }
    else if (http_header_max_size <= length)  //包或者缓存超过最大允许容量，不进行缓存
    {
        return 0;
    }

    ret = search(payload, payload_end, patten, patten_end);
    if (ret == payload_end) //HTTP头未结束，缓存
    {
        return 1;
    }
    else        //HTTP头结束
    {
        if (ret + 4 < payload_end)   //找到HTTP负载，结束缓存
        {
            return 0;
        }
        else    //尚未发现负载，检测头是否标记有负载, http.content_length!=0 或http.transfer_encoding==chunked
        {
            if (p_http_session->p_http_message->last_req_head && b_resp)
            //HEAD请求的响应，不应有负载，不应继续缓存
            {
                return 0;
            }
            if (content_judge.judge(payload, (unsigned int)(payload_end-payload))) //应有负载
            {
                if(b_resp)  //应有负载的HTTP响应头，HTTP负载位于独立的数据包
                {
                    ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_HTTPRESP_PKTALONE);
                }
                return 1;
            }
            else    //不应有HTTP负载
            {
                return 0;
            }
        }
    }
}

bool http_plugin::potocol_sign_judge(session_pub* p_session, c_packet* p_packet)
{
    //取消预处理，所有数据包进入后续流程，方便做文件还原
    return true;
}

bool http_plugin::request_head_parse(char *httpbuf , uint32_t len, http_session* p_http_session)
{
    char *payload_start = httpbuf;
    char *payload_end = payload_start + len;
    char *pat = "\r\n";
    char *pat_end = pat + 2;
    char *ret = search(payload_start, payload_end, pat, pat_end);
    if (ret != payload_end)             //首行完整
    {
        string line(payload_start, ret-payload_start);
        auto pos = line.find(" ");
        if (pos != string::npos)
        {
            p_http_session->p_http_message->act = line.substr(0, pos);
            auto pos1 = line.find(" ", pos+1);
            if (pos1 != string::npos)
            {
                p_http_session->p_http_message->url = line.substr(pos+1, pos1-pos-1);
                return true;
            }
        }
    }
    return false;
}

bool http_plugin::response_head_parse(char * httpbuf , uint32_t len, http_session* p_http_session)
{
    char *payload_start = httpbuf;
    char *payload_end = payload_start + len;
    char *pat = "\r\n";
    char *pat_end = pat + 2;
    char *ret = search(payload_start, payload_end, pat, pat_end);
    if (ret != payload_end)             //首行完整
    {
        p_http_session->p_http_message->response = string(payload_start, ret-payload_start);
        return true;
    }
    else
    {
        return false;
    }
}

//包解析入口
bool http_plugin::potocol_parse_handle(session_pub* p_session, c_packet* p_packet)
{
    bool ret;
    char *payload, *payload_end;
    if( p_session == NULL || p_packet == NULL)
    {
        return false;
    }
    proto_parse_session* p_tmp_mid = (proto_parse_session*)p_session->p_sp_session;
    http_session* p_http_session = (http_session*)p_tmp_mid->expansion_data;

    switch(p_packet->app_pro)
    {
        case PROTOCOL_HTTP_OPTIONS:
        case PROTOCOL_HTTP_HEAD:
        case PROTOCOL_HTTP_GET:
        case PROTOCOL_HTTP_POST:
        case PROTOCOL_HTTP_PUT:
        case PROTOCOL_HTTP_DELETE:
        case PROTOCOL_HTTP_TRACE:
        case PROTOCOL_HTTP_CONNECT:
        case PROTOCOL_HTTP_PATCH:
        case PROTOCOL_HTTP:
        {
            if (0 == p_http_session->p_http_message->server_direction)  //使用自定义的HTTP服务端方向，防止socks 反向代理造成方向判反
            {
                if (PROTOCOL_HTTP == p_packet->app_pro)
                {
                    p_http_session->p_http_message->server_direction = (0 == p_packet->Directory ? PACKETFROMSERVER : PACKETFROMCLIENT);
                }
                else
                {
                    p_http_session->p_http_message->server_direction = (0 == p_packet->Directory ? PACKETFROMCLIENT : PACKETFROMSERVER);
                }
            }
            //TODO 重传包支持
            //当前方向缓存有上次的HTTP头，调用解析并清理缓存
            if (p_http_session->p_http_message->cache[p_packet->Directory].size())
            {
                if (potocol_parse_handle(p_session, NULL, p_packet->Directory, 
                        &p_http_session->p_http_message->cache[p_packet->Directory].buffer[0],
                        p_http_session->p_http_message->cache[p_packet->Directory].size()
                    ))
                {
                    potocol_data_handle(p_session, NULL);
                }
                p_http_session->p_http_message->cache[p_packet->Directory].clear();
            }

            if (PROTOCOL_HTTP_HEAD == p_packet->app_pro)    //HEAD 请求
            {
                p_http_session->p_http_message->last_req_head = 1;
            }
            else if(PROTOCOL_HTTP != p_packet->app_pro)     //非HEAD 请求
            {
                p_http_session->p_http_message->last_req_head = 0;
            }

            if (judge_need_pkt_cache(p_session, p_packet->Directory, (char *)p_packet->app_buf, p_packet->app_len))
            {
                p_http_session->p_http_message->cache[p_packet->Directory].add_pkt(p_packet->seq, p_packet->app_len, (char *)p_packet->app_buf);
                return false;
            }
            else
            {
                return potocol_parse_handle(p_session, p_packet, p_packet->Directory, (char *)p_packet->app_buf, p_packet->app_len);
            }
        }
        default:
        {
            if (p_packet->app_len
                    && p_http_session->p_http_message->cache[p_packet->Directory].size()
                    && p_packet->seq == p_http_session->p_http_message->cache[p_packet->Directory].seq_next)    //预期的一下包
            {
                p_http_session->p_http_message->cache[p_packet->Directory].add_pkt(p_packet->seq, p_packet->app_len, (char *)p_packet->app_buf);
                if (judge_need_pkt_cache(p_session, p_packet->Directory, 
                            &p_http_session->p_http_message->cache[p_packet->Directory].buffer[0], 
                            p_http_session->p_http_message->cache[p_packet->Directory].size()
                    ))           //再次检测是否可以解析了
                {
                    return false;
                }
                else
                {
                    ret = potocol_parse_handle(p_session, p_packet, p_packet->Directory, 
                            &p_http_session->p_http_message->cache[p_packet->Directory].buffer[0],
                            p_http_session->p_http_message->cache[p_packet->Directory].size()
                        );
                    p_http_session->p_http_message->cache[p_packet->Directory].clear();
                    return ret;
                }
            }
            else if (p_http_session->p_http_message->file[p_packet->Directory].is_open() && p_packet->app_len)
            {
                p_http_session->p_http_message->file[p_packet->Directory].add_fragment((char *)p_packet->app_buf, p_packet->seq, p_packet->app_len);
                return false;
            }
            else
            {
                return false;
            }
        }
    }
}

string http_plugin::decompress_or_dechunk(int compress_type, int chunked, char *payload_start, uint32_t length)
{
    uint32_t offset = 0, chunk_start_offset = 0, tmp_len;
    long long chunk_len = 0;
    string merged_payload, tmp_payload;
    vector<Bytef> decompressed_data;
    char *str_end;
    char state = 0;
    if (0 == length)
    {
        return "";
    }
    if (chunked)    //分段传输
    {
        while (offset < length)
        {
            if (0 == state && 1 == g_chunk_char[payload_start[offset]])
            {
                offset ++;
            }
            else if (0 == state && 2 == g_chunk_char[payload_start[offset]])
            {
                state = 1;
                offset ++;
            }
            else if (1 == state && 3 == g_chunk_char[payload_start[offset]])
            {
                state = 2;
                offset ++;
            }
            else if (2 == state)
            {
                chunk_len = strtoll(payload_start + chunk_start_offset, &str_end, 16);
                if (0 == chunk_len)
                {
                    break;
                }
                else
                {
                    if (chunk_len < length && (offset + chunk_len <= length))
                    {
                        merged_payload += string(payload_start + offset, chunk_len);
                        offset += chunk_len;
                        chunk_start_offset = offset;
                        state = 0;
                    }
                    else
                    {
                        tmp_len = length-offset;
                        merged_payload += string(payload_start + offset, tmp_len);
                        break;
                    }
                }
            }
            else
            {
                break;
            }
        }
        if (merged_payload.size())      //合并成功
        {
            if (1 == compress_type)
            {
                deflate_decompressor.decompress_until_the_end((Bytef *)merged_payload.c_str(), (uInt)merged_payload.size(), decompressed_data);
            }
            else if(2 == compress_type)
            {
                gzip_decompressor.decompress_until_the_end((Bytef *)merged_payload.c_str(), (uInt)merged_payload.size(), decompressed_data);
            }
            if (decompressed_data.size())       //解压成功
            {
                if (decompressed_data.size() > 64)
                {
                    tmp_payload = string((char *)&decompressed_data[0], 64);
                    return HexToAscII(tmp_payload);
                }
                else
                {
                    tmp_payload = string((char *)&decompressed_data[0], decompressed_data.size());
                    return HexToAscII(tmp_payload);
                }
            }
            else        //解压失败或无压缩
            {
                if (merged_payload.size() > 64)
                {
                    tmp_payload = merged_payload.substr(0,64);
                    return HexToAscII(tmp_payload);
                }
                else
                {
                    return HexToAscII(merged_payload);
                }
            }
        }
        //合并失败，返回输入负载
    }
    else    //非分段传输
    {
        if (1 == compress_type)
        {
            deflate_decompressor.decompress_until_the_end((Bytef *)payload_start, (uInt)length, decompressed_data);
        }
        else if (2 == compress_type)
        {
            gzip_decompressor.decompress_until_the_end((Bytef *)payload_start, (uInt)length, decompressed_data);
        }
        if (decompressed_data.size())       //解压成功
        {
            if (decompressed_data.size() > 64)
            {
                tmp_payload = string((char *)&decompressed_data[0], 64);
                return HexToAscII(tmp_payload);
            }
            else
            {
                tmp_payload = string((char *)&decompressed_data[0], decompressed_data.size());
                return HexToAscII(tmp_payload);
            }
        }
        //解压失败或者无压缩，返回输入负载
    }
    if (length > 64)
    {
        tmp_payload = string(payload_start, 64);
        return HexToAscII(tmp_payload);
    }
    else
    {
        tmp_payload = string(payload_start, length);
        return HexToAscII(tmp_payload);
    }
}

static int http_header_crlf_scan(char *start, uint32_t size, vector<uint32_t> &position_list)
{
    position_list.clear();
    if (start && size)
    {
        char *pos = start, *end = start + size;
        char *last_pos = NULL;
        while (pos < end)
        {
            pos = (char *)memmem(pos, end-pos, crlf.c_str(), crlf.size());
            if (pos)
            {
                position_list.push_back(pos-start);
                if (pos == (last_pos+crlf.size()))
                {
                    break;
                }
                last_pos = pos;
                pos += crlf.size();
            }
            else
            {
                break;
            }
        }
    }
    return 0;
}

static bool is_string_case_equal(const std::string& str1, const std::string& str2)
{
    if (str1.size() != str2.size())
    {
        return false;
    }
    return std::equal(str1.begin(), str1.end(), str2.begin(),
            [](char c1, char c2) { return std::tolower(c1) == std::tolower(c2); }
        );
}

//实际解析部分，解析数据包或者缓存
bool http_plugin::potocol_parse_handle(session_pub* p_session, c_packet* p_packet, uint8_t packet_directory, char *data_start, uint32_t data_len)
{
    proto_parse_session* p_tmp_mid = (proto_parse_session*)p_session->p_sp_session;
    http_session* p_http_session = (http_session*)p_tmp_mid->expansion_data;
    
    uint32_t ip = 0;

    int black = 0, white = 0, important = 0;
    int os_type = 0;
    bool Brower_type = false; 
    bool phone = false;

    int compress_type = 0, chunked = 0;
    vector<uint32_t> positions;
    std::unordered_set<string> title_set;
    string host, referer;

    string key, value;

    if( p_session == NULL )
        return false;

    if (0 == data_start || 0 == data_len)
    {
        return false;
    }

    //HTTP请求
    if ((0 == packet_directory && PACKETFROMCLIENT == p_http_session->p_http_message->server_direction)
            || (1 == packet_directory && PACKETFROMSERVER == p_http_session->p_http_message->server_direction))
    {
        if (p_http_session->p_http_message->flag)   //有缓存的元数据，先发走
        {
            potocol_data_handle(p_session, p_packet);
        }
        p_session->app_req_num ++;

        if(!request_head_parse(data_start, data_len, p_http_session))
        {
            return false;
        }
        http_header_crlf_scan(data_start, data_len, positions);

        for (unsigned int i = 1; i < positions.size(); i ++)
        {
            uint32_t offset_start = positions[i-1]+crlf.size();
            uint32_t offset_end = positions[i];

            if (offset_start == offset_end) //http payload
            {
                auto offset = offset_end+crlf.size();
                if (offset < data_len)
                {
                    uint32_t payload_len = data_len-offset;
                    if (compress_type || chunked)
                    {
                        p_http_session->p_http_message->client_payload = 
                                decompress_or_dechunk(compress_type, chunked, data_start+offset, payload_len
                            );
                    }
                    else
                    {
                        string payload(data_start+offset, min((int)payload_len, 64));
                        p_http_session->p_http_message->client_payload = HexToAscII(payload);
                    }
                    if (p_packet && b_file_recover)
                    {
                        char *p_ssid = (char *)&p_session->session_basic.ConnectKeyID;
                        string file_name = file_dir + to_string(p_session->StartClock/14400) + "/" + HexToAscII(p_ssid[0]) + "/" +
                                to_string(p_session->session_basic.ConnectKeyID)+"_"+to_string(packet_directory)+"_"+to_string(++ p_http_session->p_http_message->file_id[packet_directory]);
                        uint32_t seq_raw = p_packet->seq + p_packet->app_len;
                        seq_raw -= payload_len;
                        p_http_session->p_http_message->file[packet_directory].create_file(file_name, data_start+offset, seq_raw, payload_len, (0!=chunked));
                        p_http_session->p_http_message->client_file = file_name;
                    }
                }
                else
                {
                    p_http_session->p_http_message->client_payload = "NoPayload";
                }
            }
            else    //http header
            {
                for (auto offset = offset_start; offset < offset_end; offset ++)
                {
                    if (':' == data_start[offset])
                    {
                        key = string(data_start+offset_start, offset-offset_start);
                        offset ++;
                        while (offset < offset_end)
                        {
                            if (' ' == data_start[offset])
                            {
                                offset ++;
                                continue;
                            }
                            else
                            {
                                break;
                            }
                        }
                        value = string(data_start+offset, offset_end-offset);
                        p_http_session->p_http_message->client.push_back(pair<string,string>(key, value));
                        title_set.insert(key);

                        if (is_string_case_equal(key, ce_key))
                        {
                            if (value == "deflate")
                            {
                                compress_type = 1;
                            }
                            else if (value == "gzip")
                            {
                                compress_type = 2;
                            }
                        }
                        else if (is_string_case_equal(key, te_key))
                        {
                            if (value == "chunked")
                            {
                                chunked = 1;
                            }
                        }
                        else if (is_string_case_equal(key, ua_key))
                        {
                            p_http_session->p_http_message->ua = value;
                        }
                        else if (is_string_case_equal(key, host_key))
                        {
                            host = value;
                            p_http_session->p_http_message->host = value;
                        }
                        else if (is_string_case_equal(key, xff_key))
                        {
                            ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_HTTP_XFF);
                        }
                        else if (is_string_case_equal(key, cookie_key))
                        {
                            ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_HTTP_COOKIE);
                        }
                        else if (is_string_case_equal(key, referer_key))
                        {
                            referer = value;
                        }
                        break;
                    }
                }
            }
        }
        if(title_set.size() < p_http_session->p_http_message->client.size())
        {
            ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_HTTP_REQ_SAMEHEAD);
        }
        if(host.size())
        {
            if(inet_pton(AF_INET, host.c_str(), (void *)&ip))
            {
                ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_HTTP_IP_HOST);
                if(p_packet && p_packet->dst_ip.str() != host)
                {
                    ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_HOST_DIFF_IP);
                }
            }
            else if(p_session->domain_judge_cb(p_session->thread_id, host.c_str()))
            {
                ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_HTTP_DOMAIN_T1000);
            }
            if(referer.size())
            {
                if(host.length() > 2 && referer.length() > 2)
                {
                    string part_referer = referer.substr(0, host.length()+8);
                    if(std::string::npos == part_referer.find(host))
                    {
                        ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_HTTP_OUT_REFERER);
                    }
                    else
                    {
                        ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_HTTP_HOST_REFERER);
                    }
                }
            }
            p_session->target_scan_handle_cb(host, p_session->thread_id, black, white, important);
            // if(black)
            // {
            //     ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_BLACK_SESSION);
            // }
            if(white)
            {
                ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_WHITE_RELATE);
            }
            if(important)
            {
                ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_TARGET_RELATE);
            }
        }
        else
        {
            ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_HTTP_NO_HOST);
        }
        if (p_http_session->p_http_message->ua.size())
        {
            ua_engine.scan_handle(p_http_session->p_http_message->ua, os_type ,Brower_type , phone) ;
            // if(Brower_type)
            // {
            //     ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_HTTP_UA_BROWSER);
            // }
            if(phone)
            {
                ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_HTTP_UA_MOBILE);
            }
            switch(os_type)
            {
                case OS_LINUX:
                {
                    ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_HTTP_UA_LINUX);
                    break;
                }
                case OS_WINDOWS:
                {
                    ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_HTTP_UA_WIN);
                    break;
                }
                case OS_MACOS:
                {
                    ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_HTTP_UA_OS);
                    break;
                }
                case OS_IOS:
                {
                    ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_HTTP_UA_IOS);
                    break;
                }
                case OS_ANDROND:
                {
                    ((th_engine_tools *)p_th_tools)->th_set_label(p_session->labels, LABEL_HTTP_UA_ANDROID);
                    break;
                }
            }
        }
        p_http_session->p_http_message->flag |= 1;
        return false;
    }
    //HTTP响应
    else if ((0 == packet_directory && PACKETFROMSERVER == p_http_session->p_http_message->server_direction)
            || (1 == packet_directory && PACKETFROMCLIENT == p_http_session->p_http_message->server_direction))
    {
        if (p_http_session->p_http_message->flag & 2)   //有缓存的RESP元数据，先发走
        {
            potocol_data_handle(p_session, p_packet);
        }
        if(!response_head_parse(data_start, data_len, p_http_session))
        {
            return false;
        }
        http_header_crlf_scan(data_start, data_len, positions);
        for (unsigned int i = 1; i < positions.size(); i ++)
        {
            uint32_t offset_start = positions[i-1]+crlf.size();
            uint32_t offset_end = positions[i];

            if (offset_start == offset_end) //http payload
            {
                auto offset = offset_end+crlf.size();
                if (offset < data_len)
                {
                    uint32_t payload_len = data_len-offset;
                    if (compress_type || chunked)
                    {
                        p_http_session->p_http_message->server_payload = 
                                decompress_or_dechunk(compress_type, chunked, data_start+offset, payload_len
                            );
                    }
                    else
                    {
                        string payload(data_start+offset, min((int)payload_len, 64));
                        p_http_session->p_http_message->server_payload = HexToAscII(payload);
                    }
                    if (p_packet && b_file_recover)
                    {
                        char *p_ssid = (char *)&p_session->session_basic.ConnectKeyID;
                        string file_name = file_dir + to_string(p_session->StartClock/14400) + "/" + HexToAscII(p_ssid[0]) + "/" +
                                to_string(p_session->session_basic.ConnectKeyID)+"_"+to_string(packet_directory)+"_"+to_string(++ p_http_session->p_http_message->file_id[packet_directory]);
                        uint32_t seq_raw = p_packet->seq + p_packet->app_len;
                        seq_raw -= payload_len;
                        p_http_session->p_http_message->file[packet_directory].create_file(file_name, data_start+offset, seq_raw, payload_len, (0!=chunked));
                        p_http_session->p_http_message->server_file = file_name;
                    }
                }
                else
                {
                    p_http_session->p_http_message->server_payload = "NoPayload";
                }
            }
            else    //http header
            {
                for (auto offset = offset_start; offset < offset_end; offset ++)
                {
                    if (':' == data_start[offset])
                    {
                        key = string(data_start+offset_start, offset-offset_start);
                        offset ++;
                        while (offset < offset_end)
                        {
                            if (' ' == data_start[offset])
                            {
                                offset ++;
                                continue;
                            }
                            else
                            {
                                break;
                            }
                        }
                        value = string(data_start+offset, offset_end-offset);
                        p_http_session->p_http_message->server.push_back(pair<string,string>(key, value));
                        // title_set.insert(key);

                        if (is_string_case_equal(key, ce_key))
                        {
                            if (value == "deflate")
                            {
                                compress_type = 1;
                            }
                            else if (value == "gzip")
                            {
                                compress_type = 2;
                            }
                        }
                        else if (is_string_case_equal(key, te_key))
                        {
                            if (value == "chunked")
                            {
                                chunked = 1;
                            }
                        }
                        else if (is_string_case_equal(key, server_key))
                        {
                            p_http_session->p_http_message->server_name = value;
                        }
                        break;
                    }
                }
            }
        }
        p_http_session->p_http_message->flag |= 2;
        return true;
    }
    return false;
}

//发送数据
void http_plugin::potocol_data_handle(session_pub* p_session, c_packet* p_packet)
{
    if( p_session == NULL)
        return;
    // Json::FastWriter fast_writer;
    proto_parse_session* p_tmp_mid = (proto_parse_session*)p_session->p_sp_session;
    http_session* p_http_session = (http_session*)p_tmp_mid->expansion_data;
    string ua = "";
    unsigned char aes_buf[16] = {0};
    uint64_t finger_c = 0, finger_s = 0;
    session_pub_http http_msg;
    
    if(should_log)
    {
        JKNmsg * p_msg = p_session->p_value->get();
        if( p_msg == NULL)
        {
            clear_http_session(p_http_session);
            return;
        }
        CJsonMsg * p_json_msg = p_session->p_value -> jget();
        http_header_msg* p_http = p_msg->mutable_http_header();
        p_msg->set_type(80);
        Comm_msg* p_comm = p_http->mutable_comm_msg();
        //公共信息部分
        commsg_fill(p_session, p_comm, "12345", p_th_tools);
        if (p_http_session->p_http_message->flag & 1)
        {
            for (unsigned int i = 0; i < p_http_session->p_http_message->client.size(); i ++)
            {
                key_val_msg *p_kv = p_http->add_http_client_kv();
                p_http->add_http_client_title(p_http_session->p_http_message->client[i].first);
                p_kv->set_key(p_http_session->p_http_message->client[i].first);
                p_kv->set_val(p_http_session->p_http_message->client[i].second);
            }
            if (p_http_session->p_http_message->client_payload.size())
            {
                key_val_msg *p_kv = p_http->add_http_client_kv();
                p_kv->set_key("Payload");
                p_kv->set_val(p_http_session->p_http_message->client_payload);
            }
            if (p_http_session->p_http_message->client_file.size())
            {
                key_val_msg *p_kv = p_http->add_http_client_kv();
                p_kv->set_key("File");
                p_kv->set_val(p_http_session->p_http_message->client_file);
            }
            p_http->set_act(p_http_session->p_http_message->act);
            http_msg.act = p_http_session->p_http_message->act;
            p_http->set_url(p_http_session->p_http_message->url);
            http_msg.url = p_http_session->p_http_message->url;
            p_http->set_host(p_http_session->p_http_message->host);
            http_msg.host = p_http_session->p_http_message->host;
            ua = p_http_session->p_http_message->ua;
            http_msg.user_agent = ua;
            if (ua.size())
            {
                if(0 == AES_Hash(aes_hash_ctx, (unsigned char *)ua.c_str(),
                        ua.size(), aes_buf)
                    )
                {
                    finger_c = *(uint64_t *)aes_buf;
                    finger_c &= (uint64_t)0x7fffffffffffffff;
                    if(p_session->p_session_ext)
                    {
                        p_session->p_session_ext->app_c_finger = finger_c;
                    }
                    p_http->set_http_c_finger(finger_c);
                }
            }
        }
        if (p_http_session->p_http_message->flag & 2)
        {
            for (unsigned int i = 0; i < p_http_session->p_http_message->server.size(); i ++)
            {
                key_val_msg *p_kv = p_http->add_http_server_kv();
                p_http->add_http_server_title(p_http_session->p_http_message->server[i].first);
                p_kv->set_key(p_http_session->p_http_message->server[i].first);
                p_kv->set_val(p_http_session->p_http_message->server[i].second);
            }
            if (p_http_session->p_http_message->server_payload.size())
            {
                key_val_msg *p_kv = p_http->add_http_server_kv();
                p_kv->set_key("Payload");
                p_kv->set_val(p_http_session->p_http_message->server_payload);
            }
            if (p_http_session->p_http_message->server_file.size())
            {
                key_val_msg *p_kv = p_http->add_http_server_kv();
                p_kv->set_key("File");
                p_kv->set_val(p_http_session->p_http_message->server_file);
            }
            p_http->set_response(p_http_session->p_http_message->response);
            http_msg.response = p_http_session->p_http_message->response;
            if(p_http_session->p_http_message->server_name.size())
            {
                if(0 == AES_Hash(aes_hash_ctx, (unsigned char *)p_http_session->p_http_message->server_name.c_str(),
                        p_http_session->p_http_message->server_name.length(), aes_buf)
                    )
                {
                    finger_s = *(uint64_t *)aes_buf;
                    finger_s &= (uint64_t)0x7fffffffffffffff;
                    if(p_session->p_session_ext)
                    {
                        p_session->p_session_ext->app_s_finger = finger_s;
                    }
                    p_http->set_http_s_finger(finger_s);
                }
            }
        }
        session_pub_push_http(p_session, &http_msg);
        if(p_json_msg)
        {
            rapidjson::StringBuffer strBuf;
            rapidjson::Writer<rapidjson::StringBuffer> writer(strBuf);
            writer.StartObject();
            writer.Key("type");
            writer.Uint(3008);
            writer.Key("SessionId");
            writer.String(p_comm->session_id().c_str(), p_comm->session_id().length());
            writer.Key("sIp");
            writer.String(p_comm->src_ip().c_str(), p_comm->src_ip().length());
            writer.Key("dIp");
            writer.String(p_comm->dst_ip().c_str(),p_comm->dst_ip().length());
            writer.Key("sPort");
            writer.Uint(p_comm->src_port());
            writer.Key("dPort");
            writer.Uint(p_comm->dst_port());
            writer.Key("StartTime");
            writer.Uint(p_comm->begin_time());
            writer.Key("Host");
            writer.String(p_http->host().c_str(),p_http->host().length());
            writer.Key("Url");
            writer.String(p_http->url().c_str(),p_http->url().length());
            writer.Key("Act");
            writer.String(p_http->act().c_str(),p_http->act().length());
            writer.Key("Response");
            writer.String(p_http->response().c_str(),p_http->response().length());
            writer.Key("Client");
                writer.StartObject();
                writer.Key("User-Agent");
                writer.String(ua.c_str(),ua.length());
                writer.EndObject();
            writer.EndObject();
            p_json_msg->copy_buf((uint8_t *)strBuf.GetString(), strBuf.GetSize());
        }
    }
    clear_http_session(p_http_session);
    return;
}

//超时
bool http_plugin::time_out(session_pub* p_session, uint32_t check_time)
{
   return true; 
}
//资源回收
void http_plugin::resources_recovery(session_pub* p_session)
{
    if( p_session == NULL)
        return;
    proto_parse_session* p_tmp_mid = (proto_parse_session*)p_session->p_sp_session;
    http_session* p_http_session = (http_session*)p_tmp_mid->expansion_data;

    if (p_http_session->p_http_message->cache[0].size())
    {
        potocol_parse_handle(p_session, NULL, 0, &p_http_session->p_http_message->cache[0].buffer[0],
                p_http_session->p_http_message->cache[0].size()
            );
        p_http_session->p_http_message->cache[0].clear();
    }
    if (p_http_session->p_http_message->cache[1].size())
    {
        potocol_parse_handle(p_session, NULL, 1, &p_http_session->p_http_message->cache[0].buffer[0],
                p_http_session->p_http_message->cache[1].size()
            );
        p_http_session->p_http_message->cache[1].clear();
    }
    if(p_http_session->p_http_message->flag)
    {
        potocol_data_handle(p_session, NULL);
    }
    clear_http_session(p_http_session);
    delete p_http_session->p_http_message;
    p_http_session->p_http_message = 0;
    return;
}


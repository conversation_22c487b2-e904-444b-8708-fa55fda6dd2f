#ifndef __DEFLATE_DECOMPRESSOR_H__
#define __DEFLATE_DECOMPRESSOR_H__

#include <vector>
#include <zlib.h>


class DeflateDecompressor
{
public:
    static const uInt def_buffer_size;
    DeflateDecompressor(int windowBits = MAX_WBITS, uInt buffer_size = def_buffer_size);
    
    int decompress_begin();
    int decompress_add(Bytef *in, uInt in_size, std::vector<Bytef> &out);
    int decompress_end(Bytef *in, uInt in_size, std::vector<Bytef> &out);
    
    int decompress_until_the_end(Bytef *in, uInt in_size, std::vector<Bytef> &out);
    
private:
    static voidpf zalloc(voidpf opaque, uInt items, uInt size);
    static void zfree(voidpf opaque, voidpf address);
    z_stream stream;
    std::vector<Bytef> buffer;
    uInt buffer_size;
};

#endif

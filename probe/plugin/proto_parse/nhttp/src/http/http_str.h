// Last Update:2018-10-16 02:34:10
/**
 * @file http_str.h
 * @brief http session part file.
 * <AUTHOR> @version 0.1.00
 * @date 2018-10-10
 */

#ifndef HTTP_STR_H
#define HTTP_STR_H

#include <string>
#include <unordered_map>
#include <cstdint>
#include <vector>
#include "tcp_file_buffer.h"



class http_message
{
public:
    class HttpPktCache
    {
    public:
        int add_pkt(std::uint32_t seq, std::uint32_t len, char *p_data)
        {
            auto size = buffer.size();
            if (len && (0 == size || seq == seq_next))
            {
                seq_next = seq + len;
                buffer.resize(size + len);
                memcpy(&buffer[size], p_data, len);
                return 0;
            }
            else
            {
                return -1;
            }
        }
        std::uint32_t size()
        {
            return buffer.size();
        }
        int clear()
        {
            buffer.clear();
            return 0;
        }
        std::uint32_t seq_next;
        std::vector<char> buffer;
    };

    http_message()
    {
        last_req_head = 0;
        server_direction = 0;
        file_id[0] = 0;
        file_id[1] = 0;
        clear();
    }
    int clear()
    {
        client.clear();
        server.clear();
        act = "";
        url = "";
        host = "";
        response = "";
        ua = "";
        server_name = "";
        client_file = "";
        server_file = "";
        client_payload = "";
        server_payload = "";
        flag = 0;
        return 0;
    }
    std::vector<std::pair<string, string>> client;
    std::vector<std::pair<string, string>> server;
    HttpPktCache cache[2];      //仅在sign_judge 加入cache，仅在potocol_data_handle 清理cache
    TcpFileBuffer file[2];      //文件还原
    std::string act;
    std::string url;
    std::string host;
    std::string response;
    std::string ua;
    std::string server_name;
    std::string client_file;
    std::string server_file;
    std::string client_payload;
    std::string server_payload;
    std::uint16_t file_id[2];       //文件编号
    std::uint8_t flag;              //位图，flag为元数据缓存标记，1请求元数据，2响应元数据
    std::uint8_t last_req_head;         //上次请求为HEAD
    std::uint8_t server_direction;      //PACKETFROMUNKOWN/PACKETFROMCLIENT/PACKETFROMSERVER
};

class http_session
{
    public:
        http_message* p_http_message;
};

#endif  /*HTTP_STR_H*/

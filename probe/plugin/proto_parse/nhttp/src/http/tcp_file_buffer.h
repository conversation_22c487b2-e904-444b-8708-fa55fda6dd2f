#ifndef __TCP_FILE_BUFFER_H__
#define __TCP_FILE_BUFFER_H__

#include "file_buffer.h"

class TcpFileBuffer: public FileBuffer
{
public:
    TcpFileBuffer(std::uint32_t buffer_size = BUFFER_MIN_SIZE):FileBuffer(buffer_size) {};
    int create_file(std::string name, char *ptr, std::uint32_t seq_raw, std::uint32_t size, bool chunked_mode = false);
    int add_fragment(char *ptr, std::uint32_t seq_raw, std::uint32_t size);
private:
    bool guess_seq_offset(std::uint32_t seq_raw, std::uint64_t &offset);
};

#endif

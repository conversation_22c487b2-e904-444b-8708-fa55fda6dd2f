/*
 * HyperScan.cpp
 *
 *  Created on: Apr 1, 2017
 *      Author: will
 */
 
#include "HyperScan.h"
#include <hs.h>
#include <stdio.h>
#include <time.h>
#include <cstring>
#include <chrono>
#include <fstream>
#include <iomanip>
#include <iostream>
#include <unordered_map>
#include <unistd.h>
 
#include <netinet/in.h>
#include <netinet/in_systm.h>
#include <netinet/ip.h>
#include <netinet/tcp.h>
#include <netinet/udp.h>
#include <netinet/ip_icmp.h>
#include <net/ethernet.h>
#include <arpa/inet.h>
 
 
CHyperScan::CHyperScan()
{
	m_Database=0;
	m_Scratch=0;
}
CHyperScan::~CHyperScan()
{
	Quit();
}
 
DWORD CHyperScan::Init( void* IN_pParam )
{
	m_Database=0;
	m_Scratch=0;
 
	m_RegEx.clear();
	m_RegExBuf.clear();
	m_RuleID.clear();
	m_RuleFlag.clear();
 
 
	return 0;
}
void CHyperScan::Quit()
{
	if(m_Scratch!=0)
	{
		hs_free_scratch(m_Scratch);
		m_Scratch=0;
	}
 
	if(m_Database!=0)
	{
		hs_free_database(m_Database);
		m_Database=0;
	}
 
 
}
 
 
//
// HS_FLAG_DOTALL
// HS_FLAG_CASELESS
DWORD CHyperScan::AddRegular( char *IN_pRegularString,DWORD IN_RuleID,DWORD IN_Flag )
{
	vector<string>::iterator ite;
	m_RegEx.push_back(IN_pRegularString);
	m_RuleID.push_back(IN_RuleID);
	m_RuleFlag.push_back(IN_Flag);
 
	ite=m_RegEx.end()-1;
	m_RegExBuf.push_back(ite->c_str());
 
	return 0;
}
 
//锟斤拷锟斤拷-锟斤拷锟斤拷锟节伙拷取DFA
//IN_MOde: HS_MODE_BLOCK  HS_MODE_STREAM
void CHyperScan::Gather( DWORD IN_Mode  )
{
	m_Mode=IN_Mode;
	hs_compile_error_t *compileErr;
	hs_error_t err;
 
	err = hs_compile_multi(m_RegExBuf.data(), m_RuleFlag.data(), m_RuleID.data(),
			m_RegExBuf.size(), IN_Mode, 0, &m_Database, &compileErr);
	if (err != HS_SUCCESS) {
		if (compileErr->expression < 0) {
			// The error does not refer to a particular expression.
			cerr << "ERROR: " << compileErr->message << endl;
		} else {
			cerr << "ERROR: Pattern '" << m_RegExBuf[compileErr->expression]
				 << "' failed compilation with error: " << compileErr->message
				 << endl;
		}
		// As the compileErr pointer points to dynamically allocated memory, if
		// we get an error, we must be sure to release it. This is not
		// necessary when no error is detected.
		hs_free_compile_error(compileErr);
		exit(-1);
	}
 
    err = hs_alloc_scratch(m_Database, &m_Scratch);
    if (err != HS_SUCCESS) {
        cerr << "ERROR: could not allocate scratch space. Exiting." << endl;
        exit(-1);
    }
 
    size_t ScratchSize=0;
    err=hs_scratch_size(m_Scratch,&ScratchSize);
    if(err==HS_SUCCESS)
    {
    	printf("ScratchSize %ld\n",ScratchSize);;
    }
 
}
 
/*
 
-- 
*/
DWORD CHyperScan::Match(unsigned char *IN_pStart,DWORD IN_DataLen,FUNC_ONMATCH_HYPERSCAN IN_Func,void*IO_Param )
{
	hs_scan(m_Database,(char*)IN_pStart,IN_DataLen,0,m_Scratch,IN_Func,IO_Param);
 
	return 0;
}

/*
bool CHyperScan::ToFile(CFile_OS *pFile)
{
	if(pFile!=0)
	{
		DWORD Magic=m_Magic;
		DWORD RuleNum=m_RuleID.size();
		DWORD Flag;
		DWORD ID;
		DWORD RegExLen;
		char *pRegEx;
 
		DWORD Writen;
 
		pFile->Write(&Magic,sizeof(DWORD),&Writen);
		pFile->Write(&m_Mode,sizeof(DWORD),&Writen);
		pFile->Write(&RuleNum,sizeof(DWORD),&Writen);
 
 
		for(int i=0;i<RuleNum;i++)
		{
			ID=m_RuleID[i];
			Flag=m_RuleFlag[i];
			RegExLen=m_RegEx[i].size();
			pRegEx=(char*)m_RegEx[i].c_str();
 
			pFile->Write(&ID,sizeof(DWORD),&Writen);
			pFile->Write(&Flag,sizeof(DWORD),&Writen);
			pFile->Write(&RegExLen,sizeof(DWORD),&Writen);
			pFile->Write(pRegEx,RegExLen,&Writen);
		}
 
		return true;
 
	}
	return false;
}
*/
/*
bool CHyperScan::FromFile(CFile_OS *pFile)
{
	if(pFile!=0)
	{
		DWORD Magic;
		DWORD RuleNum;
 
		DWORD Flag;
		DWORD ID;
		DWORD RegExLen;
		DWORD RegExSize=1<<20;
		char *pRegEx=new char [RegExSize];
 
		DWORD Readed;
 
 
		pFile->Read(&Magic,sizeof(DWORD),&Readed);
		if(Magic!=m_Magic)
		{
			printf("CHyperScan -- Read Rule Fail\n");
			exit(0);
		}
		pFile->Read(&m_Mode,sizeof(DWORD),&Readed);
		pFile->Read(&RuleNum,sizeof(DWORD),&Readed);
 
		for(int i=0;i<RuleNum;i++)
		{
			pFile->Read(&ID,sizeof(DWORD),&Readed);
			pFile->Read(&Flag,sizeof(DWORD),&Readed);
			pFile->Read(&RegExLen,sizeof(DWORD),&Readed);
			pFile->Read(pRegEx,RegExLen,&Readed);
			pRegEx[RegExLen]=0;
 
			AddRegular(pRegEx,ID,Flag);
		}
 
		Gather(m_Mode);
 
		delete [] pRegEx;
		return true;
 
	}
	return false;
}
*/

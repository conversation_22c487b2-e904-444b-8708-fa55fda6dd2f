#ifndef __FILE_BUFFER_H__
#define __FILE_BUFFER_H__


#include <cstdint>
#include <fstream>
#include <vector>
#include <list>


//支持乱序文件块，重组还原，可以预设缓冲区大小

class FileBuffer
{
public:
    class Block
    {
    public:
        Block(){};
        Block(std::uint64_t start, std::uint64_t end):start(start),end(end){};
        std::uint64_t start;
        std::uint64_t end;
    };
    class ChunkedOFS
    {
    public:
        void open( const std::string &filename, std::ios_base::openmode mode = std::ios_base::out, bool chunked_mode = false);
        bool is_open();
        void write(char*, uint64_t);
        void close();
    private:
        enum State {BEGIN=1, HEX, CR};
        std::ofstream ofs;
        std::string chunk_size_buff;
        std::uint64_t chunk_size_remaining;
        State state;
        bool chunked_mode;
    };
    static const std::uint32_t BUFFER_MIN_SIZE = 0x1000;
    static const std::uint32_t BUFFER_MAX_SIZE = 0x1000000;
    FileBuffer(std::uint32_t buffer_size = BUFFER_MIN_SIZE);
    virtual ~FileBuffer() {};
    int create_file(std::string name, char *ptr, Block block, bool chunked_mode = false);
    int add_block(char *ptr, Block block);
    int close_file();
    bool is_open() {return ofs.is_open();};
    static int mkdirs(std::string name);
protected:
    std::uint64_t next_offset;
private:
    bool block_is_expected(Block block, std::uint64_t &size_to_write);
    bool block_cache_success(char *ptr, Block block);
    std::vector<char> buffer;
    std::list<Block> cache_list;
    ChunkedOFS ofs;
};

#endif

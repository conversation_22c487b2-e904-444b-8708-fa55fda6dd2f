#ifndef ICMP_PLUGIN_H
#define ICMP_PLUGIN_H

#include <stdio.h>
#include <iomanip>
#include <sstream>
#include <xml_parse.h>
#include <c_ip.h>
#include <map>
#include <TH_engine_interface.h>
#include <proto_parse_session.h>
#include "icmp_str.h"

using namespace std;

extern "C" {
    int get_plugin_id();
    session_pasre_base* attach();
};

class icmp_plugin : public session_pasre_base {
public:
    icmp_plugin();
    ~icmp_plugin();
    virtual void reload();
    virtual bool potocol_init(session_pub* p_sess, c_packet* p_pack);
    virtual bool potocol_sign_judge(session_pub* p_sess, c_packet* p_pack);
    virtual bool potocol_parse_handle(session_pub* p_sess, c_packet* p_packet);
    virtual void potocol_data_handle(session_pub* p_sess, c_packet* p_packet);
    virtual bool time_out(session_pub* p_sess, uint32_t check_time);
    virtual void resources_recovery(session_pub* p_sess);

private:
    string bin2string(const char* p_data, int32_t len);
    string bin2ascii(const char* p_data, int32_t len);
    int get_ipv4_string(uint32_t ipv4_data, string& ipv4_string);
    int get_ipv6_string(uint32_t ipv6_data[4], string& ipv6_string);
    void print_icmp_fields(icmp_session* p_icmp_session);
    void icmp_msg_send(session_pub* p_session);
    void init_icmp_session(icmp_session* p_icmp_session);
    void reset_icmp_session(icmp_session* p_icmp_session);
    bool parse_icmp_data_msg(icmp_session* p_icmp_session);
};

#endif /*ICMP_PLUGIN_H*/
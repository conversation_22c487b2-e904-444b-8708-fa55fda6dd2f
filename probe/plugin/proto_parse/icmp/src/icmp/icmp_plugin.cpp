#include "icmp_plugin.h"
#include <netinet/ip_icmp.h>
#include <netinet/icmp6.h>
#include <arpa/inet.h>
#include <cstring>
#include <ctime>
#include <commit_tools.h>

extern "C" {
int get_plugin_id() {
    return 10040;  // ICMP插件ID
}

session_pasre_base* attach() {
    return new icmp_plugin();
}
}

icmp_plugin::icmp_plugin() {
    reload();
}

icmp_plugin::~icmp_plugin() {
}

void icmp_plugin::reload() {
}

string icmp_plugin::bin2string(const char* p_data, int32_t len) {
    if (p_data == NULL || len < 1) {
        return "";
    }
    string s = "";
    char formate_char[3];
    for (int32_t i = 0; i < len; ++i) {
        sprintf(formate_char, "%02x", (unsigned char)p_data[i]);
        s.append(formate_char, 2);
    }
    return s;
}

string icmp_plugin::bin2ascii(const char* p_data, int32_t len) {
    if (p_data == NULL || len < 1) {
        return "";
    }
    string s = "";
    char formate_char[3];
    for (int32_t i = 0; i < len; ++i) {
        if (p_data[i] >= 32 && p_data[i] <= 126) {
            sprintf(formate_char, "%c", (unsigned char)p_data[i]);
        } else {
            sprintf(formate_char, "%c", ' ');
        }
        s.append(formate_char, 1);
    }
    return s;
}

int icmp_plugin::get_ipv4_string(uint32_t ipv4_data, string& ipv4_string) {
    char ip4str[INET_ADDRSTRLEN];
    struct sockaddr_in in_addr;
    in_addr.sin_family = AF_INET;
    in_addr.sin_addr.s_addr = ipv4_data;

    if (inet_ntop(AF_INET, &in_addr.sin_addr, ip4str, INET_ADDRSTRLEN) != NULL) {
        ipv4_string.assign(ip4str);
        return 0;
    }
    return -1;
}

int icmp_plugin::get_ipv6_string(uint32_t ipv6_data[4], string& ipv6_string) {
    char ip6str[INET6_ADDRSTRLEN];
    struct sockaddr_in6 sockaddr6;
    sockaddr6.sin6_family = AF_INET6;
    memcpy(&sockaddr6.sin6_addr, ipv6_data, sizeof(struct in6_addr));

    if (inet_ntop(AF_INET6, &sockaddr6.sin6_addr, ip6str, INET6_ADDRSTRLEN) != NULL) {
        ipv6_string.assign(ip6str);
        return 0;
    }
    return -1;
}

void icmp_plugin::init_icmp_session(icmp_session* p_icmp_session) {
    memset(p_icmp_session, 0, sizeof(icmp_session));
    p_icmp_session->p_icmp_request = new icmp_data_msg();
    p_icmp_session->p_icmp_reply = new icmp_data_msg();
}

void icmp_plugin::reset_icmp_session(icmp_session* p_icmp_session) {
    if (p_icmp_session->p_icmp_request) {
        delete p_icmp_session->p_icmp_request;
        p_icmp_session->p_icmp_request = NULL;
    }
    if (p_icmp_session->p_icmp_reply) {
        delete p_icmp_session->p_icmp_reply;
        p_icmp_session->p_icmp_reply = NULL;
    }
}

bool icmp_plugin::parse_icmp_data_msg(icmp_session* p_icmp_session) {
    if (!p_icmp_session || !p_icmp_session->p_data || p_icmp_session->data_len < sizeof(struct icmphdr)) {
        return false;
    }

    struct iphdr* ip_header = (struct iphdr*)(p_icmp_session->p_data + sizeof(struct iphdr));
    struct icmphdr* icmp = (struct icmphdr*)p_icmp_session->p_data;
    icmp_data_msg* p_icmp_data_msg = NULL;

    // 判断是请求还是响应
    if (icmp->type == ICMP_ECHO) {
        if (!p_icmp_session->msg_parse_done[ICMP_REQUEST_MSG]) {
            p_icmp_data_msg = p_icmp_session->p_icmp_request;
        }
    } else if (icmp->type == ICMP_ECHOREPLY) {
        if (!p_icmp_session->msg_parse_done[ICMP_REPLY_MSG]) {
            p_icmp_data_msg = p_icmp_session->p_icmp_reply;
        }
    }

    if (!p_icmp_data_msg) {
        return false;
    }

    // 基本字段解析
    p_icmp_data_msg->msg_type = icmp->type;
    p_icmp_data_msg->info_code = icmp->code;
    p_icmp_data_msg->check_sum = ntohs(icmp->checksum);
    p_icmp_data_msg->ver = ip_header->version;
    p_icmp_data_msg->ttl = ip_header->ttl;

    // 根据ICMP类型解析特定字段
    switch (icmp->type) {
        case ICMP_ECHO:
        case ICMP_ECHOREPLY:
            {
                struct icmp_echo* echo = (struct icmp_echo*)icmp;
                p_icmp_data_msg->echo_seq_num = ntohs(echo->sequence);
                
                // 时间戳处理
                struct timeval tv;
                gettimeofday(&tv, NULL);
                p_icmp_data_msg->orig_time_stamp = tv.tv_sec * 1000 + tv.tv_usec / 1000;
                
                // 数据内容
                uint8_t* data_ptr = (uint8_t*)icmp + sizeof(struct icmphdr);
                int32_t data_len = p_icmp_session->data_len - sizeof(struct icmphdr);
                if (data_len > 0) {
                    p_icmp_data_msg->data_con = bin2string((char*)data_ptr, data_len);
                }
            }
            break;

        case ICMP_DEST_UNREACH:
            {
                // 解析原始IP包头
                struct iphdr* orig_ip = (struct iphdr*)((uint8_t*)icmp + sizeof(struct icmphdr));
                char addr[INET_ADDRSTRLEN];
                
                // 源地址
                inet_ntop(AF_INET, &orig_ip->saddr, addr, INET_ADDRSTRLEN);
                p_icmp_data_msg->unr_src_addr = addr;
                p_icmp_data_msg->exc_src_addr = addr;
                
                // 目的地址
                inet_ntop(AF_INET, &orig_ip->daddr, addr, INET_ADDRSTRLEN);
                p_icmp_data_msg->unr_dst_addr = addr;
                p_icmp_data_msg->exc_dst_addr = addr;
                
                // 协议号和TTL
                p_icmp_data_msg->unr_prot = orig_ip->protocol;
                p_icmp_data_msg->exc_prot = orig_ip->protocol;
                p_icmp_data_msg->unc_ttl = orig_ip->ttl;
                p_icmp_data_msg->exc_ttl = orig_ip->ttl;

                // 如果是TCP/UDP端口不可达，解析端口信息
                if (icmp->code == ICMP_PORT_UNREACH) {
                    struct tcphdr* tcp = (struct tcphdr*)((uint8_t*)orig_ip + (orig_ip->ihl * 4));
                    p_icmp_data_msg->unreachable_source_port = ntohs(tcp->source);
                    p_icmp_data_msg->unreachable_destination_port = ntohs(tcp->dest);
                }
            }
            break;

        case ICMP_TIME_EXCEEDED:
            {
                struct iphdr* orig_ip = (struct iphdr*)((uint8_t*)icmp + sizeof(struct icmphdr));
                char addr[INET_ADDRSTRLEN];
                
                inet_ntop(AF_INET, &orig_ip->saddr, addr, INET_ADDRSTRLEN);
                p_icmp_data_msg->exc_src_addr = addr;
                
                inet_ntop(AF_INET, &orig_ip->daddr, addr, INET_ADDRSTRLEN);
                p_icmp_data_msg->exc_dst_addr = addr;
                
                p_icmp_data_msg->exc_ttl = orig_ip->ttl;
            }
            break;

        case ICMP_REDIRECT:
            {
                struct icmp_redirect* redirect = (struct icmp_redirect*)icmp;
                char addr[INET_ADDRSTRLEN];
                inet_ntop(AF_INET, &redirect->gateway, addr, INET_ADDRSTRLEN);
                p_icmp_data_msg->gw_addr = addr;
            }
            break;

        case ICMP_ADDRESS:
        case ICMP_ADDRESSREPLY:
            {
                struct icmp_address* addr = (struct icmp_address*)icmp;
                p_icmp_data_msg->mask = ntohl(addr->mask);
            }
            break;
    }

    // 计算响应时间
    struct timeval tv;
    gettimeofday(&tv, NULL);
    p_icmp_data_msg->response_time = tv.tv_sec * 1000000ULL + tv.tv_usec;

    // 设置解析完成标志
    if (icmp->type == ICMP_ECHO) {
        p_icmp_session->msg_parse_done[ICMP_REQUEST_MSG] = true;
    } else if (icmp->type == ICMP_ECHOREPLY) {
        p_icmp_session->msg_parse_done[ICMP_REPLY_MSG] = true;
    }

    return true;
}

bool icmp_plugin::potocol_init(session_pub* p_session, c_packet* p_packet) {
    if (!p_session || !p_packet) {
        return false;
    }
    proto_parse_session* p_pp_session = (proto_parse_session*)(p_session->p_sp_session);
    icmp_session* p_icmp_session = (icmp_session*)p_pp_session->expansion_data;
    init_icmp_session(p_icmp_session);
    p_icmp_session->thread_id = p_session->thread_id;
    return true;
}

bool icmp_plugin::potocol_sign_judge(session_pub* p_session, c_packet* p_packet) {
    if (!p_session || !p_packet) {
        return false;
    }
    proto_parse_session* p_pp_session = (proto_parse_session*)(p_session->p_sp_session);
    icmp_session* p_icmp_session = (icmp_session*)p_pp_session->expansion_data;

    if (p_packet->app_data_len < sizeof(struct icmphdr)) {
        return false;
    }

    p_icmp_session->p_data = p_packet->p_app_data;
    p_icmp_session->data_len = p_packet->app_data_len;

    // 设置数据包方向
    if ((0 == p_packet->Directory && PACKETFROMCLIENT == p_session->session_basic.Server) ||
        (1 == p_packet->Directory && PACKETFROMSERVER == p_session->session_basic.Server)) {
        p_icmp_session->pktdirec = PKTFROMCLIENT;
    } else if ((0 == p_packet->Directory && PACKETFROMSERVER == p_session->session_basic.Server) ||
               (1 == p_packet->Directory && PACKETFROMCLIENT == p_session->session_basic.Server)) {
        p_icmp_session->pktdirec = PKTFROMSERVER;
    }

    return true;
}

bool icmp_plugin::potocol_parse_handle(session_pub* p_session, c_packet* p_packet) {
    if (!p_session || !p_packet) {
        return false;
    }
    proto_parse_session* p_pp_session = (proto_parse_session*)(p_session->p_sp_session);
    icmp_session* p_icmp_session = (icmp_session*)p_pp_session->expansion_data;
    
    return parse_icmp_data_msg(p_icmp_session);
}

void icmp_plugin::print_icmp_fields(icmp_session* p_icmp_session) {
    if (!p_icmp_session) {
        printf("Invalid ICMP session pointer\n");
        return;
    }

    printf("\n====== ICMP Message Fields ======\n");
    
    // 打印请求消息字段
    if (p_icmp_session->msg_parse_done[ICMP_REQUEST_MSG] && p_icmp_session->p_icmp_request) {
        icmp_data_msg* req = p_icmp_session->p_icmp_request;
        printf("\n--- ICMP Request ---\n");
        printf("Message Type: %u (", req->msg_type);
        switch(req->msg_type) {
            case ICMP_ECHO: printf("Echo Request"); break;
            case ICMP_ECHOREPLY: printf("Echo Reply"); break;
            case ICMP_DEST_UNREACH: printf("Destination Unreachable"); break;
            case ICMP_SOURCE_QUENCH: printf("Source Quench"); break;
            case ICMP_REDIRECT: printf("Redirect"); break;
            case ICMP_TIME_EXCEEDED: printf("Time Exceeded"); break;
            case ICMP_PARAMETERPROB: printf("Parameter Problem"); break;
            case ICMP_TIMESTAMP: printf("Timestamp Request"); break;
            case ICMP_TIMESTAMPREPLY: printf("Timestamp Reply"); break;
            case ICMP_INFO_REQUEST: printf("Information Request"); break;
            case ICMP_INFO_REPLY: printf("Information Reply"); break;
            case ICMP_ADDRESS: printf("Address Mask Request"); break;
            case ICMP_ADDRESSREPLY: printf("Address Mask Reply"); break;
            default: printf("Unknown"); break;
        }
        printf(")\n");
        
        printf("Info Code: %u\n", req->info_code);
        printf("check_sum: 0x%04x\n", req->check_sum);
        printf("Echo Sequence Number: %u\n", req->echo_seq_num);
        printf("Data Content: %s\n", req->data_con.empty() ? "Empty" : req->data_con.c_str());
        printf("Version: %u\n", req->ver);
        printf("TTL: %u\n", req->ttl);
        
        // 时间戳信息
        printf("Original Timestamp: %lu ms\n", req->orig_time_stamp);
        printf("Receive Timestamp: %lu ms\n", req->recv_time_stamp);
        printf("Transmit Timestamp: %lu ms\n", req->trans_time_stamp);

        // 不可达信息
        if (!req->unr_src_addr.empty() || !req->unr_dst_addr.empty()) {
            printf("\nUnreachable Information:\n");
            printf("Source Address: %s\n", req->unr_src_addr.c_str());
            printf("Destination Address: %s\n", req->unr_dst_addr.c_str());
            printf("Protocol: %u\n", req->unr_prot);
            printf("TTL: %u\n", req->unc_ttl);
            if (req->unreachable_source_port > 0 || req->unreachable_destination_port > 0) {
                printf("Source Port: %u\n", req->unreachable_source_port);
                printf("Destination Port: %u\n", req->unreachable_destination_port);
            }
        }

        // 异常信息
        if (!req->exc_src_addr.empty() || !req->exc_dst_addr.empty()) {
            printf("\nException Information:\n");
            printf("Source Address: %s\n", req->exc_src_addr.c_str());
            printf("Destination Address: %s\n", req->exc_dst_addr.c_str());
            printf("Protocol: %u\n", req->exc_prot);
            printf("TTL: %u\n", req->exc_ttl);
            printf("Source Port: %u\n", req->exc_src_port);
            printf("Destination Port: %u\n", req->exc_dst_port);
        }

        // 网关和路由信息
        if (!req->gw_addr.empty()) {
            printf("\nGateway Information:\n");
            printf("Gateway Address: %s\n", req->gw_addr.c_str());
        }

        // NDP相关信息
        if (req->ndp_life_time > 0 || !req->ndp_link_addr.empty()) {
            printf("\nNDP Information:\n");
            printf("Lifetime: %u seconds\n", req->ndp_life_time);
            printf("Link Address: %s\n", req->ndp_link_addr.c_str());
            printf("Prefix Length: %u\n", req->ndp_pre_len);
            printf("Prefix: %s\n", req->ndp_pre_fix.c_str());
            printf("Valid Lifetime: %u\n", req->ndp_val_life_time);
            printf("Current MTU: %u\n", req->ndp_cur_mtu);
            printf("Target Address: %s\n", req->ndp_tar_addr.c_str());
        }

        // MTU和其他信息
        if (req->next_hop_mtu > 0) {
            printf("\nMTU Information:\n");
            printf("Next Hop MTU: %u\n", req->next_hop_mtu);
        }
    }

    // 打印响应消息字段
    if (p_icmp_session->msg_parse_done[ICMP_REPLY_MSG] && p_icmp_session->p_icmp_reply) {
        icmp_data_msg* rep = p_icmp_session->p_icmp_reply;
        printf("\n--- ICMP Reply ---\n");
        printf("Message Type: %u (", rep->msg_type);
        switch(rep->msg_type) {
            case ICMP_ECHOREPLY: printf("Echo Reply"); break;
            case ICMP_TIMESTAMPREPLY: printf("Timestamp Reply"); break;
            case ICMP_INFO_REPLY: printf("Information Reply"); break;
            case ICMP_ADDRESSREPLY: printf("Address Mask Reply"); break;
            default: printf("Unknown"); break;
        }
        printf(")\n");
        
        printf("Info Code: %u\n", rep->info_code);
        printf("check_sum: 0x%04x\n", rep->check_sum);
        printf("Echo Sequence Number: %u\n", rep->echo_seq_num);
        printf("Data Content: %s\n", rep->data_con.empty() ? "Empty" : rep->data_con.c_str());
        printf("Version: %u\n", rep->ver);
        printf("TTL: %u\n", rep->ttl);
        printf("Response TTL: %u\n", rep->rep_ttl);

        // 时间信息
        if (rep->response_time > 0) {
            printf("Response Time: %lu microseconds\n", rep->response_time);
        }
    }

    printf("\n====== End of ICMP Message ======\n\n");
}

void icmp_plugin::icmp_msg_send(session_pub* p_session) {
    if (!p_session) {
        return;
    }
    proto_parse_session* p_pp_session = (proto_parse_session*)(p_session->p_sp_session);
    icmp_session* p_icmp_session = (icmp_session*)p_pp_session->expansion_data;

    //print_icmp_fields(p_icmp_session);

    if (p_icmp_session->msg_send_done) {
        return;
    }

    JKNmsg* p_msg = p_session->p_value->get();
    if (!p_msg) {
        return;
    }

    p_msg->set_type(108);
    icmp_msg* p_icmp = p_msg->mutable_icmp();
    Comm_msg* p_comm = p_icmp->mutable_comm_msg();

    // 填充公共消息
    commsg_fill(p_session, p_comm, "10040", p_th_tools);

    // 从请求和响应中获取数据
    icmp_data_msg* req = p_icmp_session->p_icmp_request;
    icmp_data_msg* rep = p_icmp_session->p_icmp_reply;

    if (req) {
        p_icmp->set_msg_type(req->msg_type);
        p_icmp->set_info_code(req->info_code);
        p_icmp->set_check_sum(req->check_sum);
        p_icmp->set_echo_seq_num(req->echo_seq_num);
        if (!req->data_con.empty()) {
            p_icmp->set_data_con(req->data_con);
        }
        p_icmp->set_ver(req->ver);
        p_icmp->set_ttl(req->ttl);

        p_icmp->set_orig_time_stamp(req->orig_time_stamp);
        p_icmp->set_recv_time_stamp(req->recv_time_stamp);
        p_icmp->set_trans_time_stamp(req->trans_time_stamp);

        if (!req->unr_src_addr.empty()) {
            p_icmp->set_unr_src_addr(req->unr_src_addr);
        }
        if (!req->unr_dst_addr.empty()) {
            p_icmp->set_unr_dst_addr(req->unr_dst_addr);
        }
        if (req->unr_prot > 0) {
            p_icmp->set_unr_prot(req->unr_prot);
        }
        if (req->unc_ttl > 0) {
            p_icmp->set_unc_ttl(req->unc_ttl);
        }

        if (req->mask > 0) {
            p_icmp->set_mask(req->mask);
        }
        if (req->sub_net_id > 0) {
            p_icmp->set_sub_net_id(req->sub_net_id);
        }

        if (req->rtr_time_out > 0) {
            p_icmp->set_rtr_time_out(req->rtr_time_out);
        }

        if (!req->exc_src_addr.empty()) {
            p_icmp->set_exc_src_addr(req->exc_src_addr);
        }
        if (!req->exc_dst_addr.empty()) {
            p_icmp->set_exc_dst_addr(req->exc_dst_addr);
        }
        if (req->exc_prot > 0) {
            p_icmp->set_exc_prot(req->exc_prot);
        }
        if (req->exc_src_port > 0) {
            p_icmp->set_exc_src_port(req->exc_src_port);
        }
        if (req->exc_dst_port > 0) {
            p_icmp->set_exc_dst_port(req->exc_dst_port);
        }

        if (!req->gw_addr.empty()) {
            p_icmp->set_gw_addr(req->gw_addr);
        }

        if (req->qur_type > 0) {
            p_icmp->set_qur_type(req->qur_type);
        }
        if (!req->qur_ipv6_addr.empty()) {
            p_icmp->set_qur_ipv6_addr(req->qur_ipv6_addr);
        }
        if (!req->qur_ipv4_addr.empty()) {
            p_icmp->set_qur_ipv4_addr(req->qur_ipv4_addr);
        }
        if (!req->qur_dns.empty()) {
            p_icmp->set_qur_dns(req->qur_dns);
        }

        if (req->ndp_life_time > 0) {
            p_icmp->set_ndp_life_time(req->ndp_life_time);
        }
        if (!req->ndp_link_addr.empty()) {
            p_icmp->set_ndp_link_addr(req->ndp_link_addr);
        }
        if (req->ndp_pre_len > 0) {
            p_icmp->set_ndp_pre_len(req->ndp_pre_len);
        }
        if (!req->ndp_pre_fix.empty()) {
            p_icmp->set_ndp_pre_fix(req->ndp_pre_fix);
        }
        if (req->ndp_val_life_time > 0) {
            p_icmp->set_ndp_val_life_time(req->ndp_val_life_time);
        }
        if (req->ndp_cur_mtu > 0) {
            p_icmp->set_ndp_cur_mtu(req->ndp_cur_mtu);
        }
        if (!req->ndp_tar_addr.empty()) {
            p_icmp->set_ndp_tar_addr(req->ndp_tar_addr);
        }

        if (req->next_hop_mtu > 0) {
            p_icmp->set_next_hop_mtu(req->next_hop_mtu);
        }
        if (req->exc_pointer > 0) {
            p_icmp->set_exc_pointer(req->exc_pointer);
        }
        if (!req->mul_cast_addr.empty()) {
            p_icmp->set_mul_cast_addr(req->mul_cast_addr);
        }

        if (req->unreachable_source_port > 0) {
            p_icmp->set_unreachable_source_port(req->unreachable_source_port);
        }
        if (req->unreachable_destination_port > 0) {
            p_icmp->set_unreachable_destination_port(req->unreachable_destination_port);
        }

        if (req->rtraddr > 0) {
            p_icmp->set_rtraddr(req->rtraddr);
        }
    }

    if (rep) {
        p_icmp->set_check_sum_reply(rep->check_sum);
        p_icmp->set_rep_ttl(rep->rep_ttl);
        p_icmp->set_res_time(rep->res_time);
        p_icmp->set_exc_ttl(rep->exc_ttl);
        p_icmp->set_response_time(rep->response_time);
    }

    p_icmp_session->msg_send_done = true;
}

void icmp_plugin::potocol_data_handle(session_pub* p_session, c_packet* p_packet) {
    icmp_msg_send(p_session);
}

bool icmp_plugin::time_out(session_pub* p_session, uint32_t check_time) {
    return true;
}

void icmp_plugin::resources_recovery(session_pub* p_session) {
    if (!p_session) {
        return;
    }
    proto_parse_session* p_pp_session = (proto_parse_session*)(p_session->p_sp_session);
    icmp_session* p_icmp_session = (icmp_session*)p_pp_session->expansion_data;
    reset_icmp_session(p_icmp_session);
}
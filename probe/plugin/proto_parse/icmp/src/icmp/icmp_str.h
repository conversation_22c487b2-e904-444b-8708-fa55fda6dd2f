#ifndef ICMP_STR_H
#define ICMP_STR_H

#include <list>
#include <packet.h>
#include <session_pub.h>
#include <stdint.h>
#include <string>
#include <netinet/ip_icmp.h>
#include <netinet/icmp6.h>

using namespace std;

enum pkt_direction {
    PKTFROMUNKNOWN = 0,
    PKTFROMCLIENT,
    PKTFROMSERVER,
};

enum msg_parsed_type {
    ICMP_REQUEST_MSG,
    ICMP_REPLY_MSG,
};

// ICMP报文结构体定义
struct icmp_echo {
    uint8_t  type;
    uint8_t  code;
    uint16_t checksum;
    uint16_t id;
    uint16_t sequence;
};

struct icmp_redirect {
    uint8_t  type;
    uint8_t  code;
    uint16_t checksum;
    struct in_addr gateway;
};

struct icmp_address {
    uint8_t  type;
    uint8_t  code;
    uint16_t checksum;
    uint16_t id;
    uint16_t sequence;
    uint32_t mask;
};

// ICMP数据包基本信息
class icmp_data_msg {
public:
    icmp_data_msg() {
        msg_type = 0;                    // 消息类型
        info_code = 0;                   // 信息代码
        check_sum = 0;                   // icmp层数据校验和
        echo_seq_num = 0;                // 反射数据包序列号
        data_con = "";                   // 数据内容
        unr_src_addr = "";               // 不可达源IP地址
        unr_dst_addr = "";               // 不可达目的IP地址
        unr_prot = 0;                    // 不可达协议号
        unc_ttl = 0;                     // 不可达跳数
        ver = 0;                         // ICMP协议版本号
        orig_time_stamp = 0;             // 发送时间戳（单位：毫秒）
        recv_time_stamp = 0;             // 接收时间戳（单位：毫秒）
        trans_time_stamp = 0;            // 传送时间戳（单位：毫秒）
        mask = 0;                        // 网络掩码
        sub_net_id = 0;                  // 子网络号
        rtr_time_out = 0;                // 通告地址的有效时间（单位：秒）
        exc_src_addr = "";               // 导致发生异常消息的报文的源ip地址，异常消息：目的不可达、超时时间
        exc_dst_addr = "";               // 导致发生异常消息的报文的目的ip地址，异常消息：目的不可达、超时时间
        exc_prot = 0;                    // 导致发生异常消息的报文的协议号，异常消息：目的不可达、超时时间
        exc_src_port = 0;                // 导致发生异常消息的报文的源ip端口，异常消息：目的不可达、超时时间
        exc_dst_port = 0;                // 导致发生异常消息的报文的目的ip端口，异常消息：目的不可达、超时时间
        gw_addr = "";                    // 重路由网关gateway
        ttl = 0;                         // 生存时间
        rep_ttl = 0;                     // 响应生存时间
        qur_type = 0;                    // 查询类型
        qur_ipv6_addr = "";              // 查询的IPV6地址
        qur_ipv4_addr = "";              // 查询的IPV4地址
        qur_dns = "";                    // 查询的DNS
        ndp_life_time = 0;               // 作为默认路由器时的生存期,从Router lifetime 提取，如：1800 （单位：秒）
        ndp_link_addr = "";              // NDP(邻居发现协议)重路由网关的链路地址
        ndp_pre_len = 0;                 // NDP(邻居发现协议)前缀长度,从Prefix length 获取，如：64
        ndp_pre_fix = "";                // NDP(邻居发现协议)前缀，从 Prefix 获取，如：3ffe:507:0:1::
        ndp_val_life_time = 0;           // NDP(邻居发现协议)前缀有效生存期,从Valid Lifetime 获取，如:3600000
        ndp_cur_mtu = 0;                 // NDP(邻居发现协议)当前链路的MTU大小，从MTU获取，如：1500
        ndp_tar_addr = "";               // 邻居目的地址
        next_hop_mtu = 0;                // 下一跳链路的MTU大小，从MTU获取，如：65000
        exc_pointer = 0;                 // 参数错误的位置（单位：字节）,标识出报文中出现错误地方的8位片偏移量
        mul_cast_addr = "";              // 组播组地址，从 Multicast Address 获取，如：ff02::1:ff0e:4c67
        check_sum_reply = 0;             // icmp层响应数据校验和
        rtraddr = 0;                     // 路由器地址，多个值时以逗号分隔
        res_time = 0;                    // 响应时间
        exc_ttl = 0;                     // 不可达跳数
        response_time = 0;               // ICMP协议的响应时间间隔，微秒级
        unreachable_source_port = 0;      // ICMP的不可达源端口
        unreachable_destination_port = 0;  // ICMP的不可达目的端口
    }

    uint32_t msg_type;                   // 消息类型
    uint32_t info_code;                  // 信息代码
    uint32_t check_sum;                  // icmp层数据校验和
    uint32_t echo_seq_num;               // 反射数据包序列号
    string data_con;                     // 数据内容
    string unr_src_addr;                 // 不可达源IP地址
    string unr_dst_addr;                 // 不可达目的IP地址
    uint32_t unr_prot;                   // 不可达协议号
    uint32_t unc_ttl;                    // 不可达跳数
    uint32_t ver;                        // ICMP协议版本号
    uint64_t orig_time_stamp;            // 发送时间戳（单位：毫秒）
    uint64_t recv_time_stamp;            // 接收时间戳（单位：毫秒）
    uint64_t trans_time_stamp;           // 传送时间戳（单位：毫秒）
    uint32_t mask;                       // 网络掩码
    uint32_t sub_net_id;                 // 子网络号
    uint32_t rtr_time_out;               // 通告地址的有效时间（单位：秒）
    string exc_src_addr;                 // 导致发生异常消息的报文的源ip地址，异常消息：目的不可达、超时时间
    string exc_dst_addr;                 // 导致发生异常消息的报文的目的ip地址，异常消息：目的不可达、超时时间
    uint32_t exc_prot;                   // 导致发生异常消息的报文的协议号，异常消息：目的不可达、超时时间
    uint32_t exc_src_port;               // 导致发生异常消息的报文的源ip端口，异常消息：目的不可达、超时时间
    uint32_t exc_dst_port;               // 导致发生异常消息的报文的目的ip端口，异常消息：目的不可达、超时时间
    string gw_addr;                      // 重路由网关gateway
    uint32_t ttl;                        // 生存时间
    uint32_t rep_ttl;                    // 响应生存时间
    uint32_t qur_type;                   // 查询类型
    string qur_ipv6_addr;                // 查询的IPV6地址
    string qur_ipv4_addr;                // 查询的IPV4地址
    string qur_dns;                      // 查询的DNS
    uint32_t ndp_life_time;              // 作为默认路由器时的生存期,从Router lifetime 提取，如：1800 （单位：秒）
    string ndp_link_addr;                // NDP(邻居发现协议)重路由网关的链路地址
    uint32_t ndp_pre_len;                // NDP(邻居发现协议)前缀长度,从Prefix length 获取，如：64
    string ndp_pre_fix;                  // NDP(邻居发现协议)前缀，从 Prefix 获取，如：3ffe:507:0:1::
    uint32_t ndp_val_life_time;          // NDP(邻居发现协议)前缀有效生存期,从Valid Lifetime 获取，如:3600000
    uint32_t ndp_cur_mtu;                // NDP(邻居发现协议)当前链路的MTU大小，从MTU获取，如：1500
    string ndp_tar_addr;                 // 邻居目的地址
    uint32_t next_hop_mtu;               // 下一跳链路的MTU大小，从MTU获取，如：65000
    uint32_t exc_pointer;                // 参数错误的位置（单位：字节）,标识出报文中出现错误地方的8位片偏移量
    string mul_cast_addr;                // 组播组地址，从 Multicast Address 获取，如：ff02::1:ff0e:4c67
    uint32_t check_sum_reply;            // icmp层响应数据校验和
    uint32_t rtraddr;                    // 路由器地址，多个值时以逗号分隔
    uint64_t res_time;                   // 响应时间
    uint32_t exc_ttl;                    // 不可达跳数
    uint64_t response_time;              // ICMP协议的响应时间间隔，微秒级
    uint32_t unreachable_source_port;     // ICMP的不可达源端口
    uint32_t unreachable_destination_port; // ICMP的不可达目的端口
};

class icmp_session {
public:
    icmp_session() {
        thread_id = 0;
        p_data = NULL;
        data_len = 0;
        pktdirec = PKTFROMUNKNOWN;
        p_icmp_request = NULL;
        p_icmp_reply = NULL;
        memset(msg_parse_done, 0, sizeof(msg_parse_done));
        msg_send_done = false;
    }

    uint32_t thread_id;
    uint8_t* p_data;
    uint32_t data_len;
    enum pkt_direction pktdirec;
    icmp_data_msg* p_icmp_request;
    icmp_data_msg* p_icmp_reply;
    bool msg_parse_done[2];
    bool msg_send_done;
};

#endif /*ICMP_STR_H*/
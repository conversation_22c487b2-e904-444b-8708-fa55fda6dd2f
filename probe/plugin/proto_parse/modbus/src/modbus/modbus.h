#ifndef MODBUS_LIBRARY_H
#define MODBUS_LIBRARY_H

#include <stdio.h>
#include <iomanip>
#include <sstream>
#include <xml_parse.h>
#include <c_ip.h>
#include <map>
#include <TH_engine_interface.h>
#include <proto_parse_session.h>
#include "modbus_str.h"

using namespace std;

extern "C" {
int get_plugin_id();
session_pasre_base * attach();
};
class modbus_plugin : public session_pasre_base{
public:
    //standard interface
    modbus_plugin();
    ~modbus_plugin();
    virtual void reload();
    virtual bool potocol_init(session_pub* p_sess, c_packet* p_pack);
    virtual bool potocol_sign_judge(session_pub* p_sess, c_packet* p_pack);
    virtual bool potocol_parse_handle(session_pub* p_sess,c_packet * p_packet);
    virtual void potocol_data_handle(session_pub* p_sess,c_packet * p_packet);
    virtual bool time_out(session_pub* p_sess,uint32_t check_time);
    virtual void resources_recovery(session_pub* p_sess);
private:
    //self-defined function
    void init_modbus_session(modbus_session * p_modbus_session);
    bool modbus_data_parse(char* p_data, uint32_t data_len, modbus_session* p_modbus_session);
    bool modbustcp_data_parse(char* p_data, uint32_t data_len, modbus_session* p_modbus_session);
    void modbus_msg_send(session_pub* p_session);
};

#endif

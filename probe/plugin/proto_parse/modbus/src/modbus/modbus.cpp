#include <commit_tools.h>
#include <sys/stat.h>
#include <sys/time.h>
#include "modbus.h"


#define PROTOCOL_IP 13
#define PROTOCOL_IPV6 17
#define PROTOCOL_TCP 14
#define PROTOCOL_UDP 15

void get_packet_buf_and_len(c_packet *p_packet, modbus_session *p_modbus_session)
{
    STR_PROTOCOLINFOR *pStack = p_packet -> m_str_packet_moudle.Stack.pProtocol;
    for(int i=0; i<MAXSTACKNUM; i++)
    {
        if(pStack[i].Protocol == 0)
        {break;}
    
        switch(pStack[i].Protocol)
        {
            case PROTOCOL_IP:
            case PROTOCOL_IPV6:
            {
                p_modbus_session->data_len = pStack[i].Len;
                p_modbus_session->p_data = (char *)p_packet -> buf + pStack[i].Offset;
                break;
            }
            case PROTOCOL_UDP:
            case PROTOCOL_TCP:
            {
                p_modbus_session->data_len = p_packet -> packet_len - pStack[i].Offset - pStack[i].Len;
                p_modbus_session->p_data = (char *)p_packet -> buf + pStack[i].Offset + pStack[i].Len;
                break;
            }
        }
    }
}

extern "C" {
int get_plugin_id()
{
    return 10650;
}
session_pasre_base * attach()
{
    return new modbus_plugin();
}
}

modbus_plugin::modbus_plugin()
{
    reload();
}

modbus_plugin::~modbus_plugin()
{
    ;
}

void modbus_plugin::reload()
{
    ;
}

bool modbus_plugin::potocol_init(session_pub* p_session, c_packet* p_packet)
{
    if(p_session==NULL || p_packet==NULL)
    {
        return false;
    }
    proto_parse_session *p_pp_session = (proto_parse_session*)(p_session -> p_sp_session);
    modbus_session * p_modbus_session = (modbus_session *)p_pp_session->expansion_data;
    init_modbus_session(p_modbus_session);
    p_modbus_session->thread_id = p_packet->thread_id;
    return true;
}

bool modbus_plugin::potocol_sign_judge(session_pub * p_session,c_packet * p_packet) //组包
{
    return true;
}

void modbus_plugin::init_modbus_session(modbus_session* p_modbus_session)
{
    p_modbus_session->p_data = NULL;
    p_modbus_session->data_len = 0;
    p_modbus_session->p_modbus_message = new modbus_message();
    p_modbus_session->b_parse_over = false;
    p_modbus_session->p_cur_packet = NULL;
    p_modbus_session->b_send_over = false;
}

bool modbus_plugin::modbus_data_parse(char* p_data, uint32_t data_len, modbus_session * p_modbus_session)
{
    char* p_cur = p_data;
    uint32_t offset = 0;
    modbus_message* p = p_modbus_session->p_modbus_message;
    
    uint8_t slaveid = *(uint8_t *)(p_cur+offset);
    offset += 1;
    uint8_t funccode = *(uint8_t *)(p_cur+offset);
    offset += 1;
    p->slave_id = slaveid;
    p->func_code = funccode;
    return true;
}

bool modbus_plugin::modbustcp_data_parse(char* p_data, uint32_t data_len, modbus_session * p_modbus_session)
{
    if (p_data == NULL || data_len == 0)
    {
        return false;
    }
    char* p_cur = p_data;
    uint32_t offset = 0;
    modbus_message* p = p_modbus_session->p_modbus_message;
    //Mobuds_tcp Header
    uint16_t transid = ntohs(*(uint16_t *)(p_cur+offset));
    offset += 2;
    uint16_t protocolid = ntohs(*(uint16_t *)(p_cur+offset));
    offset += 2;
    uint16_t length = ntohs(*(uint16_t *)(p_cur+offset));
    offset += 2;
    p->trans_id = transid;
    p->protocol_id = protocolid;
    bool ret = false;
    if (data_len >= length + offset)
    {ret = modbus_data_parse(p_cur+offset, length, p_modbus_session);}
    if (ret)
    {return true;}

    return false;
}

bool modbus_plugin::potocol_parse_handle(session_pub * p_session,c_packet *p_packet) //解析
{
    if (p_session == NULL || p_packet == NULL) {
        return false;
    }
    proto_parse_session* p_pp_session = (proto_parse_session * )( p_session -> p_sp_session);
    modbus_session * p_modbus_session = (modbus_session *)p_pp_session->expansion_data;
    //p_modbus_session->p_data = (char *)p_packet->app_buf;
    //p_s7_session->data_len = p_packet->app_len;
    //p_modbus_session->data_len = ntohs(*(uint16_t *)(p_modbus_session->p_data + 4))+6;
    get_packet_buf_and_len(p_packet, p_modbus_session);
    //如果解析数据之后发现可以发送了，就返回true
    bool ret = modbustcp_data_parse(p_modbus_session->p_data, p_modbus_session->data_len, p_modbus_session);
    if(p_packet -> Directory)
    {
       // b_packet_c2s = 0 ;
        p_modbus_session -> u_c2s =  0;
    }
    else
    {
        p_modbus_session -> u_c2s =  1;
    }
    
    //如果可以发送了，直接返回true
    if (ret)
    {
        return true;
    }
    //如果是FIN包也发送，fix me
    if (false)
    {
        return true;
    }
    return false;
}

void modbus_plugin::modbus_msg_send(session_pub* p_session)  //发送
{
    proto_parse_session* p_pp_session = (proto_parse_session * )( p_session -> p_sp_session);
    modbus_session * p_modbus_session = (modbus_session *)p_pp_session->expansion_data;
    modbus_message* p = p_modbus_session->p_modbus_message;

    //if (p_modbus_session->b_send_over)
    //{
    //    return;
    //}
    if(should_log)
    {
        JKNmsg * p_msg = p_session->p_value->get();
        if (p_msg == NULL)
        {
            p_modbus_session->b_send_over = true;
            return;
        }
        p_msg->set_type(42);
        modbus_msg* p_modbus = p_msg->mutable_modbus();
        Comm_msg* p_comm = p_modbus->mutable_comm_msg();
        //公共信息部分
        commsg_fill(p_session , p_comm, "10650", p_th_tools);
    
        p_modbus->set_trans_id(p->trans_id);
        p_modbus->set_protocol_id(p->protocol_id);
        p_modbus->set_slave_id(p->slave_id);
        p_modbus->set_func_code(p->func_code);
        p_modbus->set_packet_c2s(p_modbus_session->u_c2s);
    
        p_modbus_session->b_send_over = true;
    }
    return;
}

void modbus_plugin::potocol_data_handle(session_pub* p_session,c_packet * p_packet)  //发送
{
    modbus_msg_send(p_session);
}

bool modbus_plugin::time_out(session_pub * p_session, uint32_t check_time)
{
    if (p_session)
    {
        return true;
    }
    return true;
}

void modbus_plugin::resources_recovery(session_pub * p_session)
{
    proto_parse_session *p_pp_session = (proto_parse_session*)(p_session -> p_sp_session);
    modbus_session * p_modbus_session = (modbus_session *)p_pp_session->expansion_data;
    if (p_modbus_session->p_modbus_message != NULL)
    {
        delete p_modbus_session->p_modbus_message;
        p_modbus_session->p_modbus_message = NULL;
    }
    return;
}





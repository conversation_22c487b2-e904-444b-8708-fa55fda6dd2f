//
// Created by root on 4/21/19.
//

#ifndef MODBUS_MODBUS_STR_H
#define MODBUS_MODBUS_STR_H

class modbus_message {
public:
    modbus_message()
    {
        trans_id = 0;
        protocol_id = 0;
        modbus_length = 0;
        slave_id = 0;
        func_code = 0;
    }
public:
    uint16_t  trans_id;
    uint16_t  protocol_id;
    uint16_t  modbus_length;
    uint8_t   slave_id;
    uint8_t   func_code;
};

class modbus_session
{
public:
    char* p_data;
    uint32_t data_len;
    modbus_message* p_modbus_message;
    bool b_parse_over;
    bool b_send_over;
    c_packet* p_cur_packet;
    uint32_t thread_id;
    uint32_t u_c2s ;
};

#endif //S7_S7_STR_H

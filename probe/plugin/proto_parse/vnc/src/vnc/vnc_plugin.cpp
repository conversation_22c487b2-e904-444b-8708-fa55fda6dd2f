/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-09-04 10:59:51
 * @LastEditors: x<PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-09-04 13:54:31
 * @Description:
 */

#include "vnc_plugin.h"
#include <commit_tools.h>
#include <sys/stat.h>
#include <sys/time.h>

extern "C" {
int get_plugin_id() { return 10184; }
session_pasre_base *attach() { return new vnc_plugin(); }
}

map<uint8_t, string> vnc_security_types_str = {{VNC_SECURITY_TYPE_INVALID, "Invalid"},
                                               {VNC_SECURITY_TYPE_NONE, "None"},
                                               {VNC_SECURITY_TYPE_VNC, "VNC"},
                                               {VNC_SECURITY_TYPE_RA2, "RA2"},
                                               {VNC_SECURITY_TYPE_RA2ne, "RA2ne"},
                                               {VNC_SECURITY_TYPE_TIGHT, "Tight"},
                                               {VNC_SECURITY_TYPE_ULTRA, "Ultra"},
                                               {VNC_SECURITY_TYPE_TLS, "TLS"},
                                               {VNC_SECURITY_TYPE_VENCRYPT, "VeNCrypt"},
                                               {VNC_SECURITY_TYPE_GTK_VNC_SASL, "GTK-VNC SASL"},
                                               {VNC_SECURITY_TYPE_ARD, "Apple Remote Desktop"}};

map<uint32_t, string> vnc_vencrypt_auth_types_str = {{VNC_SECURITY_TYPE_NONE, "None"},
                                                     {VNC_SECURITY_TYPE_VNC, "VNC"},
                                                     {VNC_VENCRYPT_AUTH_PLAIN, "Plain"},
                                                     {VNC_VENCRYPT_AUTH_TLSNONE, "TLS None"},
                                                     {VNC_VENCRYPT_AUTH_TLSVNC, "TLS VNC"},
                                                     {VNC_VENCRYPT_AUTH_TLSPLAIN, "TLS Plain"},
                                                     {VNC_VENCRYPT_AUTH_X509_NONE, "X.509 None"},
                                                     {VNC_VENCRYPT_AUTH_X509_VNC, "X.509 VNC"},
                                                     {VNC_VENCRYPT_AUTH_X509_PLAIN, "X.509 Plain"},
                                                     {VNC_VENCRYPT_AUTH_TLSSASL, "TLS SASL"},
                                                     {VNC_VENCRYPT_AUTH_X509_SASL, "X.509 SASL"}};

map<uint32_t, string> encoding_types_str = {{VNC_ENCODING_TYPE_DESKTOP_SIZE, "DesktopSize (pseudo)"},
                                            {VNC_ENCODING_TYPE_LAST_RECT, "LastRect (pseudo)"},
                                            {VNC_ENCODING_TYPE_POINTER_POS, "Pointer pos (pseudo)"},
                                            {VNC_ENCODING_TYPE_RICH_CURSOR, "Rich Cursor (pseudo)"},
                                            {VNC_ENCODING_TYPE_X_CURSOR, "X Cursor (pseudo)"},
                                            {VNC_ENCODING_TYPE_RAW, "Raw"},
                                            {VNC_ENCODING_TYPE_COPY_RECT, "CopyRect"},
                                            {VNC_ENCODING_TYPE_RRE, "RRE"},
                                            {VNC_ENCODING_TYPE_CORRE, "CoRRE"},
                                            {VNC_ENCODING_TYPE_HEXTILE, "Hextile"},
                                            {VNC_ENCODING_TYPE_ZLIB, "Zlib"},
                                            {VNC_ENCODING_TYPE_TIGHT, "Tight"},
                                            {VNC_ENCODING_TYPE_ZLIBHEX, "ZlibHex"},
                                            {VNC_ENCODING_TYPE_ULTRA, "Ultra"},
                                            {VNC_ENCODING_TYPE_RLE, "ZRLE"},
                                            {VNC_ENCODING_TYPE_HITACHI_ZYWRLE, "Hitachi ZYWRLE"},
                                            {VNC_ENCODING_TYPE_JPEG_0, "JPEG quality level 0"},
                                            {VNC_ENCODING_TYPE_JPEG_1, "JPEG quality level 1"},
                                            {VNC_ENCODING_TYPE_JPEG_2, "JPEG quality level 2"},
                                            {VNC_ENCODING_TYPE_JPEG_3, "JPEG quality level 3"},
                                            {VNC_ENCODING_TYPE_JPEG_4, "JPEG quality level 4"},
                                            {VNC_ENCODING_TYPE_JPEG_5, "JPEG quality level 5"},
                                            {VNC_ENCODING_TYPE_JPEG_6, "JPEG quality level 6"},
                                            {VNC_ENCODING_TYPE_JPEG_7, "JPEG quality level 7"},
                                            {VNC_ENCODING_TYPE_JPEG_8, "JPEG quality level 8"},
                                            {VNC_ENCODING_TYPE_JPEG_9, "JPEG quality level 9"},
                                            {VNC_ENCODING_TYPE_COMPRESSION_0, "Compression level 0"},
                                            {VNC_ENCODING_TYPE_COMPRESSION_1, "Compression level 1"},
                                            {VNC_ENCODING_TYPE_COMPRESSION_2, "Compression level 2"},
                                            {VNC_ENCODING_TYPE_COMPRESSION_3, "Compression level 3"},
                                            {VNC_ENCODING_TYPE_COMPRESSION_4, "Compression level 4"},
                                            {VNC_ENCODING_TYPE_COMPRESSION_5, "Compression level 5"},
                                            {VNC_ENCODING_TYPE_COMPRESSION_6, "Compression level 6"},
                                            {VNC_ENCODING_TYPE_COMPRESSION_7, "Compression level 7"},
                                            {VNC_ENCODING_TYPE_COMPRESSION_8, "Compression level 8"},
                                            {VNC_ENCODING_TYPE_COMPRESSION_9, "Compression level 9"},
                                            /* FIXME understand for real what the below mean. Taken from Ultra VNC source code */
                                            /*	{VNC_ENCODING_TYPE_CACHE,     */
                                            {VNC_ENCODING_TYPE_CACHE_ENABLE, "Enable Caching"},
                                            /*	{VNC_ENCODING_TYPE_XOR_ZLIB,
                                                {VNC_ENCODING_TYPE_XOR_MONO_ZLIB,
                                                {VNC_ENCODING_TYPE_XOR_MULTI_ZLIB,
                                                {VNC_ENCODING_TYPE_SOLID_COLOR,
                                                {VNC_ENCODING_TYPE_XOR_ENABLE,
                                                {VNC_ENCODING_TYPE_CACHE_ZIP,
                                                {VNC_ENCODING_TYPE_SOL_MONO_ZIP,
                                                {VNC_ENCODING_TYPE_ULTRA_ZIP,
                                            */
                                            {VNC_ENCODING_TYPE_SERVER_STATE, "Server State"},
                                            {VNC_ENCODING_TYPE_ENABLE_KEEP_ALIVE, "Enable Keep Alive"},
                                            {VNC_ENCODING_TYPE_FTP_PROTO_VER, "FTP protocol version"},
                                            {VNC_ENCODING_TYPE_EXTENDED_DESK_SIZE, "Extended Desktop Size"},
                                            {VNC_ENCODING_TYPE_DESKTOP_NAME, "Desktop Name"},
                                            {VNC_ENCODING_TYPE_KEYBOARD_LED_STATE, "Keyboard LED State"},
                                            {VNC_ENCODING_TYPE_SUPPORTED_MESSAGES, "Supported Messages"},
                                            {VNC_ENCODING_TYPE_SUPPORTED_ENCODINGS, "Supported Encodings"},
                                            {VNC_ENCODING_TYPE_SERVER_IDENTITY, "Server Identity"},
                                            {VNC_ENCODING_TYPE_MIRRORLINK, "MirrorLink"},
                                            {VNC_ENCODING_TYPE_CONTEXT_INFORMATION, "Context Information"},
                                            {VNC_ENCODING_TYPE_SLRLE, "SLRLE"},
                                            {VNC_ENCODING_TYPE_TRANSFORM, "Transform"},
                                            {VNC_ENCODING_TYPE_HSML, "HSML"},
                                            {VNC_ENCODING_TYPE_H264, "H264"}};

set<uint32_t> recognizable_encoding_types = {VNC_ENCODING_TYPE_DESKTOP_SIZE,
                                             VNC_ENCODING_TYPE_LAST_RECT,
                                             VNC_ENCODING_TYPE_POINTER_POS,
                                             VNC_ENCODING_TYPE_RICH_CURSOR,
                                             VNC_ENCODING_TYPE_X_CURSOR,
                                             VNC_ENCODING_TYPE_RAW,
                                             VNC_ENCODING_TYPE_COPY_RECT,
                                             VNC_ENCODING_TYPE_RRE,
                                             VNC_ENCODING_TYPE_CORRE,
                                             VNC_ENCODING_TYPE_HEXTILE,
                                             VNC_ENCODING_TYPE_ZLIB,
                                             VNC_ENCODING_TYPE_TIGHT,
                                             VNC_ENCODING_TYPE_ZLIBHEX,
                                             VNC_ENCODING_TYPE_ULTRA,
                                             VNC_ENCODING_TYPE_TRLE,
                                             VNC_ENCODING_TYPE_RLE,
                                             VNC_ENCODING_TYPE_HITACHI_ZYWRLE,
                                             VNC_ENCODING_TYPE_JPEG_0,
                                             VNC_ENCODING_TYPE_JPEG_1,
                                             VNC_ENCODING_TYPE_JPEG_2,
                                             VNC_ENCODING_TYPE_JPEG_3,
                                             VNC_ENCODING_TYPE_JPEG_4,
                                             VNC_ENCODING_TYPE_JPEG_5,
                                             VNC_ENCODING_TYPE_JPEG_6,
                                             VNC_ENCODING_TYPE_JPEG_7,
                                             VNC_ENCODING_TYPE_JPEG_8,
                                             VNC_ENCODING_TYPE_JPEG_9,
                                             VNC_ENCODING_TYPE_COMPRESSION_0,
                                             VNC_ENCODING_TYPE_COMPRESSION_1,
                                             VNC_ENCODING_TYPE_COMPRESSION_2,
                                             VNC_ENCODING_TYPE_COMPRESSION_3,
                                             VNC_ENCODING_TYPE_COMPRESSION_4,
                                             VNC_ENCODING_TYPE_COMPRESSION_5,
                                             VNC_ENCODING_TYPE_COMPRESSION_6,
                                             VNC_ENCODING_TYPE_COMPRESSION_7,
                                             VNC_ENCODING_TYPE_COMPRESSION_8,
                                             VNC_ENCODING_TYPE_COMPRESSION_9,
                                             VNC_ENCODING_TYPE_WMVi,
                                             VNC_ENCODING_TYPE_CACHE,
                                             VNC_ENCODING_TYPE_CACHE_ENABLE,
                                             VNC_ENCODING_TYPE_XOR_ZLIB,
                                             VNC_ENCODING_TYPE_XOR_MONO_ZLIB,
                                             VNC_ENCODING_TYPE_XOR_MULTI_ZLIB,
                                             VNC_ENCODING_TYPE_SOLID_COLOR,
                                             VNC_ENCODING_TYPE_XOR_ENABLE,
                                             VNC_ENCODING_TYPE_CACHE_ZIP,
                                             VNC_ENCODING_TYPE_SOL_MONO_ZIP,
                                             VNC_ENCODING_TYPE_ULTRA_ZIP,
                                             VNC_ENCODING_TYPE_SERVER_STATE,
                                             VNC_ENCODING_TYPE_ENABLE_KEEP_ALIVE,
                                             VNC_ENCODING_TYPE_FTP_PROTO_VER,
                                             VNC_ENCODING_TYPE_POINTER_CHANGE,
                                             VNC_ENCODING_TYPE_EXT_KEY_EVENT,
                                             VNC_ENCODING_TYPE_AUDIO,
                                             VNC_ENCODING_TYPE_DESKTOP_NAME,
                                             VNC_ENCODING_TYPE_EXTENDED_DESK_SIZE,
                                             VNC_ENCODING_TYPE_KEYBOARD_LED_STATE,
                                             VNC_ENCODING_TYPE_SUPPORTED_MESSAGES,
                                             VNC_ENCODING_TYPE_SUPPORTED_ENCODINGS,
                                             VNC_ENCODING_TYPE_SERVER_IDENTITY,
                                             VNC_ENCODING_TYPE_MIRRORLINK,
                                             VNC_ENCODING_TYPE_CONTEXT_INFORMATION,
                                             VNC_ENCODING_TYPE_SLRLE,
                                             VNC_ENCODING_TYPE_TRANSFORM,
                                             VNC_ENCODING_TYPE_HSML,
                                             VNC_ENCODING_TYPE_H264};

vnc_plugin::vnc_plugin() { reload(); }

vnc_plugin::~vnc_plugin() { ; }

void vnc_plugin::reload() { ; }

string vnc_plugin::bin2hex(const char *p_data, uint32_t len) {
    if (p_data == NULL || len <= 0) {
        return "";
    }
    string s = "";
    char formate_char[3];
    for (uint32_t i = 0; i < len; ++i) {
        sprintf(formate_char, "%02x", (unsigned char)p_data[i]);
        s.append(formate_char, 2);
    }
    return s;
}

string vnc_plugin::bin2string(const char* p_data, uint32_t len) {
    // Check for null pointer
    if (p_data == nullptr) {
        return std::string();
    }
    
    // Check for valid length
    if (len == 0) {
        return std::string();
    }
    
    // Create string safely
    return std::string(p_data, len);
}

string vnc_plugin::bin2ascii(const char *p_data, uint32_t len) {
    if (p_data == NULL || len <= 0) {
        return "";
    }
    string s = "";
    char formate_char[3];
    for (uint32_t i = 0; i < len; ++i) {
        if (p_data[i] >= 32 && p_data[i] <= 126) { // printable characters
            sprintf(formate_char, "%c", (unsigned char)p_data[i]);
        } else {
            sprintf(formate_char, "%c", ' ');
        }
        s.append(formate_char, 1);
    }
    return s;
}

void vnc_plugin::init_vnc_session(vnc_session *p_vnc_session) {
    if (p_vnc_session == NULL) {
        return;
    }
    p_vnc_session->p_conversation_info = new vnc_conversation_info;
    p_vnc_session->p_startup_msg = new vnc_startup_msg;
    p_vnc_session->p_fb_para_msg = new vnc_server_fb_para_msg;
    p_vnc_session->p_pixel_format = new vnc_pixel_format;
    p_vnc_session->p_set_encoding_msg = new vnc_set_encoding_msg;
}

void vnc_plugin::reset_vnc_session(vnc_session *p_vnc_session) {
    if (p_vnc_session == NULL) {
        return;
    }
    if (p_vnc_session->p_conversation_info != NULL) {
        delete p_vnc_session->p_conversation_info;
    }
    if (p_vnc_session->p_fb_para_msg != NULL) {
        delete p_vnc_session->p_fb_para_msg;
    }
    if (p_vnc_session->p_startup_msg != NULL) {
        delete p_vnc_session->p_startup_msg;
    }
    if (p_vnc_session->p_pixel_format != NULL) {
        delete p_vnc_session->p_pixel_format;
    }
    if (p_vnc_session->p_set_encoding_msg != NULL) {
        delete p_vnc_session->p_set_encoding_msg;
    }
}

void vnc_plugin::parse_server_framebuffer_para_msg(vnc_session *p_vnc_session) {

    uint8_t *pcur = p_vnc_session->p_data;
    uint16_t fb_width;
    uint16_t fb_height;
    uint8_t bits_per_pixel;
    uint8_t depth;
    uint8_t big_endian_flag;
    uint8_t true_color_flag;
    uint16_t red_maximum;
    uint16_t green_maximum;
    uint16_t blue_maximum;
    uint8_t red_shift;
    uint8_t green_shift;
    uint8_t blue_shift;
    uint32_t desktop_name_len;
    string destktop_name;

    if (p_vnc_session == NULL) {
        return;
    }

    fb_width = ntohs(*(uint16_t *)pcur);

    pcur += 2;
    fb_height = ntohs(*(uint16_t *)pcur);

    pcur += 2;
    bits_per_pixel = *pcur;

    pcur++;
    depth = *pcur;

    pcur++;
    big_endian_flag = *pcur;

    pcur++;
    true_color_flag = *pcur;

    pcur++;
    red_maximum = ntohs(*(uint16_t *)pcur);

    pcur += 2;
    green_maximum = ntohs(*(uint16_t *)pcur);

    pcur += 2;
    blue_maximum = ntohs(*(uint16_t *)pcur);

    pcur += 2;
    red_shift = *pcur;

    pcur++;
    green_shift = *pcur;

    pcur++;
    blue_shift = *pcur;

    pcur++;
    pcur += 3; /* Skip over 3 bytes of padding */

    if (p_vnc_session->data_len - (pcur - p_vnc_session->p_data) > 4) {
        /* Sometimes the desktop name & length is skipped */
        desktop_name_len = ntohl(*(uint32_t *)pcur);
        pcur += 4;
        destktop_name = bin2ascii((const char *)pcur, desktop_name_len);
    }

    p_vnc_session->p_fb_para_msg->fb_width = fb_width;
    p_vnc_session->p_fb_para_msg->fb_height = fb_height;
    p_vnc_session->p_fb_para_msg->bits_per_pixel = bits_per_pixel;
    p_vnc_session->p_fb_para_msg->depth = depth;
    p_vnc_session->p_fb_para_msg->big_endian_flag = big_endian_flag;
    p_vnc_session->p_fb_para_msg->true_color_flag = true_color_flag;
    p_vnc_session->p_fb_para_msg->red_maximum = red_maximum;
    p_vnc_session->p_fb_para_msg->green_maximum = green_maximum;
    p_vnc_session->p_fb_para_msg->blue_maximum = blue_maximum;
    p_vnc_session->p_fb_para_msg->red_shift = red_shift;
    p_vnc_session->p_fb_para_msg->green_shift = green_shift;
    p_vnc_session->p_fb_para_msg->blue_shift = blue_shift;
    p_vnc_session->p_fb_para_msg->desktop_name = destktop_name;
}

void vnc_plugin::parse_set_encoding_msg(vnc_session *p_vnc_session) {

    uint8_t *pcur = p_vnc_session->p_data;
    uint16_t encoding_cnt;
    uint32_t encoding_type;

    if (p_vnc_session == NULL) {
        return;
    }

    pcur += 2;
    encoding_cnt = ntohs(*(uint16_t *)pcur);

    pcur += 2;
    for (int i = 0; i < encoding_cnt; i++) {
        encoding_type = ntohl(*(uint32_t *)pcur);
        if (recognizable_encoding_types.find(encoding_type) != recognizable_encoding_types.end()) {
            p_vnc_session->p_set_encoding_msg->encoding_types.push_back(encoding_types_str[encoding_type]);
        } else {
            p_vnc_session->p_set_encoding_msg->encoding_types.push_back("Unknown");
        }
        pcur += 4;
    }
}

void vnc_plugin::parse_set_pixel_format_msg(vnc_session *p_vnc_session) {

    uint8_t *pcur = p_vnc_session->p_data;
    uint8_t bits_per_pixel;
    uint8_t depth;
    uint8_t big_endian_flag;
    uint8_t true_color_flag;
    uint16_t red_maximum;
    uint16_t green_maximum;
    uint16_t blue_maximum;
    uint8_t red_shift;
    uint8_t green_shift;
    uint8_t blue_shift;

    if (p_vnc_session == NULL) {
        return;
    }

    pcur += 4;
    bits_per_pixel = *pcur;

    pcur++;
    depth = *pcur;

    pcur++;
    big_endian_flag = *pcur;

    pcur++;
    true_color_flag = *pcur;

    pcur++;
    red_maximum = ntohs(*(uint16_t *)pcur);

    pcur += 2;
    green_maximum = ntohs(*(uint16_t *)pcur);

    pcur += 2;
    blue_maximum = ntohs(*(uint16_t *)pcur);

    pcur += 2;
    red_shift = *pcur;

    pcur++;
    green_shift = *pcur;

    pcur++;
    blue_shift = *pcur;

    p_vnc_session->p_pixel_format->bits_per_pixel = bits_per_pixel;
    p_vnc_session->p_pixel_format->depth = depth;
    p_vnc_session->p_pixel_format->big_endian_flag = big_endian_flag;
    p_vnc_session->p_pixel_format->true_color_flag = true_color_flag;
    p_vnc_session->p_pixel_format->red_maximum = red_maximum;
    p_vnc_session->p_pixel_format->green_maximum = green_maximum;
    p_vnc_session->p_pixel_format->blue_maximum = blue_maximum;
    p_vnc_session->p_pixel_format->red_shift = red_shift;
    p_vnc_session->p_pixel_format->green_shift = green_shift;
    p_vnc_session->p_pixel_format->blue_shift = blue_shift;
}

bool vnc_plugin::parse_startup_msg(vnc_session *p_vnc_session) {
    uint8_t *pcur = p_vnc_session->p_data;
    string data_str;
    uint8_t security_type_cnt;
    uint32_t num_tunnel_types;
    uint32_t num_auth_types;
    uint32_t auth_code;
    uint32_t auth_result;
    vnc_conversation_info *p_conversation_info = p_vnc_session->p_conversation_info;

    if (p_vnc_session == NULL) {
        return false;
    }

    switch (p_conversation_info->vnc_next_state) {
    case VNC_SESSION_STATE_SERVER_VERSION: {
        data_str = bin2ascii((const char *)pcur + 4, p_vnc_session->data_len - 5); // end with '\n'
        p_vnc_session->p_startup_msg->server_protocol_ver = data_str;
        try {
            p_conversation_info->server_proto_ver = stod(data_str);
        } catch (const std::invalid_argument &e) {
            p_conversation_info->server_proto_ver = 0;
        }
        p_conversation_info->vnc_next_state = VNC_SESSION_STATE_CLIENT_VERSION;
        break;
    }
    case VNC_SESSION_STATE_CLIENT_VERSION: {
        data_str = bin2ascii((const char *)pcur + 4, p_vnc_session->data_len - 5); // end with '\n'
        p_vnc_session->p_startup_msg->client_protocol_ver = data_str;
        try {
            p_conversation_info->client_proto_ver = stod(data_str);
        } catch (const std::invalid_argument &e) {
            p_conversation_info->client_proto_ver = 0;
        }
        p_conversation_info->vnc_next_state = VNC_SESSION_STATE_SECURITY;
        break;
    }
    case VNC_SESSION_STATE_SECURITY: {
        if (p_conversation_info->server_proto_ver >= 3.007) {
            security_type_cnt = *pcur;
            pcur++;
            for (int i = 0; i < security_type_cnt; i++) {
                p_vnc_session->p_startup_msg->server_sectype_supported.push_back(vnc_security_types_str[*pcur]);
                pcur++;
            }
            p_conversation_info->vnc_next_state = VNC_SESSION_STATE_SECURITY_TYPES;
        } else { /* Version < 3.007: The server decides the authentication type for us to use */
            p_conversation_info->security_type_selected = *pcur;
            p_vnc_session->p_startup_msg->client_sectype_seleted = vnc_security_types_str[p_conversation_info->security_type_selected];
            switch (p_conversation_info->security_type_selected) {
            case VNC_SECURITY_TYPE_INVALID:
                p_conversation_info->vnc_next_state = VNC_SESSION_STATE_SECURITY_TYPES;
                break;
            case VNC_SECURITY_TYPE_NONE:
                p_conversation_info->vnc_next_state = VNC_SESSION_STATE_CLIENT_INIT;
                break;
            case VNC_SECURITY_TYPE_VNC:
                p_conversation_info->vnc_next_state = VNC_SESSION_STATE_VNC_AUTHENTICATION_CHALLENGE;
                break;
            case VNC_SECURITY_TYPE_ARD:
                p_conversation_info->vnc_next_state = VNC_SESSION_STATE_ARD_AUTHENTICATION_CHALLENGE;
                break;

            default:
                break;
            }
        }
        break;
    }
    case VNC_SESSION_STATE_SECURITY_TYPES: {
        p_conversation_info->security_type_selected = *pcur;
        p_vnc_session->p_startup_msg->client_sectype_seleted = vnc_security_types_str[p_conversation_info->security_type_selected];
        switch (p_conversation_info->security_type_selected) {
        case VNC_SECURITY_TYPE_NONE:
            if (p_conversation_info->client_proto_ver >= 3.008)
                p_conversation_info->vnc_next_state = VNC_SESSION_STATE_SECURITY_RESULT;
            else
                p_conversation_info->vnc_next_state = VNC_SESSION_STATE_CLIENT_INIT;

            break;

        case VNC_SECURITY_TYPE_VNC:
            p_conversation_info->vnc_next_state = VNC_SESSION_STATE_VNC_AUTHENTICATION_CHALLENGE;
            break;

        case VNC_SECURITY_TYPE_TIGHT:
            p_conversation_info->tight_enabled = true;
            p_conversation_info->vnc_next_state = VNC_SESSION_STATE_TIGHT_TUNNELING_CAPABILITIES;
            break;

        case VNC_SECURITY_TYPE_ARD:
            p_conversation_info->vnc_next_state = VNC_SESSION_STATE_ARD_AUTHENTICATION_CHALLENGE;
            break;

        case VNC_SECURITY_TYPE_VENCRYPT:
            p_conversation_info->vnc_next_state = VNC_SESSION_STATE_VENCRYPT_SERVER_VERSION;
            break;
        default:
            /* Security type not supported by this dissector */
            break;
        }
        break;
    }
    case VNC_SESSION_STATE_TIGHT_TUNNELING_CAPABILITIES: {
        num_tunnel_types = ntohl(*(uint32_t *)pcur);
        if (num_tunnel_types == 0)
            p_conversation_info->vnc_next_state = VNC_SESSION_STATE_TIGHT_AUTH_CAPABILITIES;
        else
            p_conversation_info->vnc_next_state = VNC_SESSION_STATE_TIGHT_TUNNEL_TYPE_REPLY;
        break;
    }
    case VNC_SESSION_STATE_TIGHT_TUNNEL_TYPE_REPLY: {
        break;
    }
    case VNC_SESSION_STATE_TIGHT_AUTH_CAPABILITIES: {
        num_auth_types = ntohl(*(uint32_t *)pcur);
        if (num_auth_types == 0)
            p_conversation_info->vnc_next_state = VNC_SESSION_STATE_CLIENT_INIT;
        else
            p_conversation_info->vnc_next_state = VNC_SESSION_STATE_TIGHT_AUTH_TYPE_REPLY;
        break;
    }
    case VNC_SESSION_STATE_TIGHT_AUTH_TYPE_REPLY: {
        auth_code = ntohl(*(uint32_t *)pcur);

        switch (auth_code) {
        case VNC_SECURITY_TYPE_NONE:
            p_conversation_info->security_type_selected = VNC_SECURITY_TYPE_NONE;
            p_conversation_info->vnc_next_state = VNC_SESSION_STATE_CLIENT_INIT;
            break;
        case VNC_SECURITY_TYPE_VNC:
            p_conversation_info->security_type_selected = VNC_SECURITY_TYPE_VNC;
            p_conversation_info->vnc_next_state = VNC_SESSION_STATE_VNC_AUTHENTICATION_CHALLENGE;
            break;
        case VNC_SECURITY_TYPE_GTK_VNC_SASL:
            p_conversation_info->security_type_selected = VNC_SECURITY_TYPE_GTK_VNC_SASL;
            p_conversation_info->vnc_next_state = VNC_SESSION_STATE_TIGHT_UNKNOWN_PACKET3;
            break;
        case VNC_TIGHT_AUTH_TGHT_ULGNAUTH:
            p_conversation_info->security_type_selected = VNC_TIGHT_AUTH_TGHT_ULGNAUTH;
            p_conversation_info->vnc_next_state = VNC_SESSION_STATE_TIGHT_UNKNOWN_PACKET3;
            break;
        case VNC_TIGHT_AUTH_TGHT_XTRNAUTH:
            p_conversation_info->security_type_selected = VNC_TIGHT_AUTH_TGHT_XTRNAUTH;
            p_conversation_info->vnc_next_state = VNC_SESSION_STATE_TIGHT_UNKNOWN_PACKET3;
            break;
        default:
            p_conversation_info->vnc_next_state = VNC_SESSION_STATE_TIGHT_UNKNOWN_PACKET3;
            break;
        }
        break;
    }
    case VNC_SESSION_STATE_TIGHT_UNKNOWN_PACKET3: {
        p_conversation_info->vnc_next_state = VNC_SESSION_STATE_VNC_AUTHENTICATION_CHALLENGE;
        break;
    }
    case VNC_SESSION_STATE_VNC_AUTHENTICATION_CHALLENGE: {
        data_str = bin2string((const char *)pcur, p_vnc_session->data_len);
        p_vnc_session->p_startup_msg->server_authen_challenge = data_str;
        p_conversation_info->vnc_next_state = VNC_SESSION_STATE_VNC_AUTHENTICATION_RESPONSE;
        break;
    }
    case VNC_SESSION_STATE_VNC_AUTHENTICATION_RESPONSE: {
        data_str = bin2string((const char *)pcur, p_vnc_session->data_len);
        p_vnc_session->p_startup_msg->client_authen_response = data_str;
        p_conversation_info->vnc_next_state = VNC_SESSION_STATE_SECURITY_RESULT;
        break;
    }
    case VNC_SESSION_STATE_ARD_AUTHENTICATION_CHALLENGE: {
        p_conversation_info->vnc_next_state = VNC_SESSION_STATE_ARD_AUTHENTICATION_RESPONSE;
        break;
    }
    case VNC_SESSION_STATE_SECURITY_RESULT: {
        auth_result = ntohl(*(uint32_t *)pcur);
        switch (auth_result) {
        case 0: /* OK */
            p_vnc_session->p_startup_msg->authen_result = "OK";
            p_conversation_info->vnc_next_state = VNC_SESSION_STATE_CLIENT_INIT;
            break;

        case 1: /* Failed */
            p_vnc_session->p_startup_msg->authen_result = "Failed";
            return false; /* All versions: Do not continue
                    processing VNC packets as connection
                    will be	closed after this packet. */
        }
        break;
    }
    case VNC_SESSION_STATE_VENCRYPT_SERVER_VERSION: {
        p_conversation_info->vnc_next_state = VNC_SESSION_STATE_VENCRYPT_CLIENT_VERSION;
        break;
    }
    case VNC_SESSION_STATE_VENCRYPT_CLIENT_VERSION: {
        p_conversation_info->vnc_next_state = VNC_SESSION_STATE_VENCRYPT_AUTH_CAPABILITIES;
        break;
    }
    case VNC_SESSION_STATE_VENCRYPT_AUTH_CAPABILITIES: {
        p_conversation_info->vnc_next_state = VNC_SESSION_STATE_VENCRYPT_AUTH_TYPE_REPLY;
        break;
    }
    case VNC_SESSION_STATE_VENCRYPT_AUTH_TYPE_REPLY: {
        uint32_t authtype = ntohl(*(uint32_t *)pcur);
        if (authtype == VNC_SECURITY_TYPE_NONE) {
            p_conversation_info->vnc_next_state = VNC_SESSION_STATE_CLIENT_INIT;
            p_conversation_info->security_type_selected = VNC_SECURITY_TYPE_NONE;
        } else if (authtype == VNC_SECURITY_TYPE_VNC) {
            p_conversation_info->vnc_next_state = VNC_SESSION_STATE_VNC_AUTHENTICATION_CHALLENGE;
            p_conversation_info->security_type_selected = VNC_SECURITY_TYPE_VNC;
        } else {
            p_conversation_info->vnc_next_state = VNC_SESSION_STATE_VENCRYPT_AUTH_ACK;
        }
        break;
    }
    case VNC_SESSION_STATE_VENCRYPT_AUTH_ACK: {
        p_conversation_info->vnc_next_state = VNC_SESSION_STATE_NORMAL_TRAFFIC;
        break;
    }
    case VNC_SESSION_STATE_CLIENT_INIT: {
        uint8_t share_desktop_flag = *pcur;
        p_vnc_session->p_startup_msg->share_desktop_flag = share_desktop_flag;
        p_conversation_info->vnc_next_state = VNC_SESSION_STATE_SERVER_INIT;
        break;
    }
    case VNC_SESSION_STATE_SERVER_INIT: {
        parse_server_framebuffer_para_msg(p_vnc_session);
        if (p_conversation_info->tight_enabled) {
            p_conversation_info->vnc_next_state = VNC_SESSION_STATE_TIGHT_INTERACTION_CAPS;
        } else {
            p_conversation_info->vnc_next_state = VNC_SESSION_STATE_NORMAL_TRAFFIC;
        }
        break;
    }
    case VNC_SESSION_STATE_TIGHT_INTERACTION_CAPS: {
        p_conversation_info->vnc_next_state = VNC_SESSION_STATE_NORMAL_TRAFFIC;
        break;
    }
    case VNC_SESSION_STATE_NORMAL_TRAFFIC: {
        return true;
    }
    default:
        break;
    }
    return false;
}

void vnc_plugin::parse_c2s_msg(vnc_session *p_vnc_session) {

    uint8_t *pcur = p_vnc_session->p_data;
    uint8_t message_type;

    if (p_vnc_session == NULL) {
        return;
    }

    message_type = *pcur;

    pcur++;
    switch (message_type) {
    case VNC_CLIENT_MESSAGE_TYPE_SET_ENCODINGS:
        parse_set_encoding_msg(p_vnc_session);
        p_vnc_session->msg_parse_done[ENCODING_TYPE] = true;
        break;
    case VNC_CLIENT_MESSAGE_TYPE_SET_PIXEL_FORMAT:
        parse_set_pixel_format_msg(p_vnc_session);
        p_vnc_session->msg_parse_done[PIXEL_FORMAT] = true;
        break;

    default: // So far, I don't care about other types
        break;
    }
}

void vnc_plugin::parse_s2c_msg(vnc_session *p_vnc_session) {
    return; // do nothing, maybe add something in future
}

bool vnc_plugin::potocol_init(session_pub *p_session, c_packet *p_packet) {
    if (p_session == NULL || p_packet == NULL) {
        return false;
    }
    proto_parse_session *p_pp_session = (proto_parse_session *)(p_session->p_sp_session);
    vnc_session *p_vnc_session = (vnc_session *)p_pp_session->expansion_data;
    init_vnc_session(p_vnc_session);
    p_vnc_session->thread_id = p_session->thread_id;
    p_vnc_session->p_conversation_info->vnc_next_state = VNC_SESSION_STATE_SERVER_VERSION;
    return true;
}

bool vnc_plugin::potocol_sign_judge(session_pub *p_session, c_packet *p_packet) // 组包
{
    if (p_session == NULL || p_packet == NULL) {
        return false;
    }
    proto_parse_session *p_pp_session = (proto_parse_session *)(p_session->p_sp_session);
    vnc_session *p_vnc_session = (vnc_session *)p_pp_session->expansion_data;

    if (p_vnc_session->msg_send_done) { // send done, do nothing
        return false;
    }
    // 组包
    if (p_packet->app_data_len == 0) {
        return false;
    } else {
        p_vnc_session->p_data = p_packet->p_app_data;
        p_vnc_session->data_len = p_packet->app_data_len;
        if ((0 == p_packet->Directory && PACKETFROMCLIENT == p_session->session_basic.Server) || (1 == p_packet->Directory && PACKETFROMSERVER == p_session->session_basic.Server)) {
            p_vnc_session->pktdirec = PKTFROMCLIENT;
        } else if ((0 == p_packet->Directory && PACKETFROMSERVER == p_session->session_basic.Server) || (1 == p_packet->Directory && PACKETFROMCLIENT == p_session->session_basic.Server)) {
            p_vnc_session->pktdirec = PKTFROMSERVER;
        }
        return true;
    }

    return false;
}

bool vnc_plugin::potocol_parse_handle(session_pub *p_session, c_packet *p_packet) // 解析
{
    bool ret;

    if (p_session == NULL || p_packet == NULL) {
        return false;
    }
    proto_parse_session *p_pp_session = (proto_parse_session *)(p_session->p_sp_session);
    vnc_session *p_vnc_session = (vnc_session *)p_pp_session->expansion_data;

    ret = parse_startup_msg(p_vnc_session);

    if (!ret) { /* We're in a "startup" state; Cannot yet do "normal" processing */
        return false;
    }

    if (p_vnc_session->pktdirec = PKTFROMCLIENT) {
        parse_c2s_msg(p_vnc_session);
    } else {
        parse_s2c_msg(p_vnc_session);
    }

    if (p_vnc_session->msg_parse_done[ENCODING_TYPE] && p_vnc_session->msg_parse_done[PIXEL_FORMAT]) {
        return true;
    } else {
        return false;
    }
}

void vnc_plugin::vnc_msg_send(session_pub *p_session) {
    if (p_session == NULL) {
        return;
    }
    proto_parse_session *p_pp_session = (proto_parse_session *)(p_session->p_sp_session);
    vnc_session *p_vnc_session = (vnc_session *)p_pp_session->expansion_data;

    if (p_vnc_session->msg_send_done) { // only send once
        return;
    }

    if (should_log) {
        JKNmsg *p_msg = p_session->p_value->get();
        if (p_msg == NULL) {
            return;
        }
        p_msg->set_type(104); // TODO:应当遵循什么原则指定type的值？
        vnc_msg *p_vnc = p_msg->mutable_vnc();
        Comm_msg *p_comm = p_vnc->mutable_comm_msg();

        vnc_sever_fb_msg *p_server_fb = p_vnc->mutable_server_fb();
        vnc_client_set_encoding_msg *p_encoding = p_vnc->mutable_encoding();
        vnc_client_set_pixel_format_msg *p_pixel_format = p_vnc->mutable_pixel_format();
        // 公共信息部分
        commsg_fill(p_session, p_comm, "10184", p_th_tools);

        ////vnc msg
        p_vnc->set_server_protocol_ver(p_vnc_session->p_startup_msg->server_protocol_ver);
        p_vnc->set_client_protocol_ver(p_vnc_session->p_startup_msg->client_protocol_ver);
        for (int i = 0; i < p_vnc_session->p_startup_msg->server_sectype_supported.size(); i++) {
            p_vnc->add_server_secure_type_supported(p_vnc_session->p_startup_msg->server_sectype_supported[i]);
        }
        p_vnc->set_client_secure_type_selected(p_vnc_session->p_startup_msg->client_sectype_seleted);
        p_vnc->set_server_authentication_challenge(p_vnc_session->p_startup_msg->server_authen_challenge);
        p_vnc->set_client_authentication_response(p_vnc_session->p_startup_msg->client_authen_response);
        p_vnc->set_authentication_result(p_vnc_session->p_startup_msg->authen_result);
        p_vnc->set_share_dsktp_flag(p_vnc_session->p_startup_msg->share_desktop_flag);

        p_server_fb->set_fb_width(p_vnc_session->p_fb_para_msg->fb_width);
        p_server_fb->set_fb_height(p_vnc_session->p_fb_para_msg->fb_height);
        p_server_fb->set_bits_per_pixel(p_vnc_session->p_fb_para_msg->bits_per_pixel);
        p_server_fb->set_big_endian_flag(p_vnc_session->p_fb_para_msg->big_endian_flag);
        p_server_fb->set_true_color_flag(p_vnc_session->p_fb_para_msg->true_color_flag);
        p_server_fb->set_red_maximum(p_vnc_session->p_fb_para_msg->red_maximum);
        p_server_fb->set_green_maximum(p_vnc_session->p_fb_para_msg->green_maximum);
        p_server_fb->set_blue_maximum(p_vnc_session->p_fb_para_msg->blue_maximum);
        p_server_fb->set_red_shift(p_vnc_session->p_fb_para_msg->red_shift);
        p_server_fb->set_green_shift(p_vnc_session->p_fb_para_msg->green_shift);
        p_server_fb->set_blue_shift(p_vnc_session->p_fb_para_msg->blue_shift);
        p_server_fb->set_desktop_name(p_vnc_session->p_fb_para_msg->desktop_name);

        for (int i = 0; i < p_vnc_session->p_set_encoding_msg->encoding_types.size(); i++) {
            p_encoding->add_encoding_types(p_vnc_session->p_set_encoding_msg->encoding_types[i]);
        }

        p_pixel_format->set_bits_per_pixel(p_vnc_session->p_pixel_format->bits_per_pixel);
        p_pixel_format->set_big_endian_flag(p_vnc_session->p_pixel_format->big_endian_flag);
        p_pixel_format->set_true_color_flag(p_vnc_session->p_pixel_format->true_color_flag);
        p_pixel_format->set_red_maximum(p_vnc_session->p_pixel_format->red_maximum);
        p_pixel_format->set_green_maximum(p_vnc_session->p_pixel_format->green_maximum);
        p_pixel_format->set_blue_maximum(p_vnc_session->p_pixel_format->blue_maximum);
        p_pixel_format->set_red_shift(p_vnc_session->p_pixel_format->red_shift);
        p_pixel_format->set_green_shift(p_vnc_session->p_pixel_format->green_shift);
        p_pixel_format->set_blue_shift(p_vnc_session->p_pixel_format->blue_shift);
    }

    p_vnc_session->msg_send_done = true;

    return;
}

void vnc_plugin::potocol_data_handle(session_pub *p_session, c_packet *p_packet) // 发送
{
    vnc_msg_send(p_session);
}

bool vnc_plugin::time_out(session_pub *p_session, uint32_t check_time) {
    if (p_session) {
        return true;
    }
    return true;
}

void vnc_plugin::resources_recovery(session_pub *p_session) {
    proto_parse_session *p_pp_session = (proto_parse_session *)(p_session->p_sp_session);
    vnc_session *p_vnc_session = (vnc_session *)p_pp_session->expansion_data;

    reset_vnc_session(p_vnc_session);
    return;
}

/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-09-04 10:41:00
 * @LastEditors: x<PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-09-04 13:55:15
 * @Description:
 */

#ifndef VNC_STR_H
#define VNC_STR_H

#include <list>
#include <packet.h>
#include <session_pub.h>
#include <stdint.h>
#include <string>

#define VNC_ENCODING_TYPE_DESKTOP_SIZE 0xFFFFFF21
#define VNC_ENCODING_TYPE_LAST_RECT 0xFFFFFF20
#define VNC_ENCODING_TYPE_POINTER_POS 0xFFFFFF18
#define VNC_ENCODING_TYPE_RICH_CURSOR 0xFFFFFF11
#define VNC_ENCODING_TYPE_X_CURSOR 0xFFFFFF10
#define VNC_ENCODING_TYPE_RAW 0
#define VNC_ENCODING_TYPE_COPY_RECT 1
#define VNC_ENCODING_TYPE_RRE 2
#define VNC_ENCODING_TYPE_CORRE 4
#define VNC_ENCODING_TYPE_HEXTILE 5
#define VNC_ENCODING_TYPE_ZLIB 6
#define VNC_ENCODING_TYPE_TIGHT 7
#define VNC_ENCODING_TYPE_ZLIBHEX 8
#define VNC_ENCODING_TYPE_ULTRA 9
#define VNC_ENCODING_TYPE_TRLE 15
#define VNC_ENCODING_TYPE_RLE 16
#define VNC_ENCODING_TYPE_HITACHI_ZYWRLE 17
#define VNC_ENCODING_TYPE_JPEG_0 0xFFFFFFE0 // -32
#define VNC_ENCODING_TYPE_JPEG_1 0xFFFFFFE1 // -31
#define VNC_ENCODING_TYPE_JPEG_2 0xFFFFFFE2 // -30
#define VNC_ENCODING_TYPE_JPEG_3 0xFFFFFFE3 // -29
#define VNC_ENCODING_TYPE_JPEG_4 0xFFFFFFE4 // -28
#define VNC_ENCODING_TYPE_JPEG_5 0xFFFFFFE5 // -27
#define VNC_ENCODING_TYPE_JPEG_6 0xFFFFFFE6 // -26
#define VNC_ENCODING_TYPE_JPEG_7 0xFFFFFFE7 // -25
#define VNC_ENCODING_TYPE_JPEG_8 0xFFFFFFE8 // -24
#define VNC_ENCODING_TYPE_JPEG_9 0xFFFFFFE9 // -23
#define VNC_ENCODING_TYPE_COMPRESSION_0 0xFFFFFF00
#define VNC_ENCODING_TYPE_COMPRESSION_1 0xFFFFFF01
#define VNC_ENCODING_TYPE_COMPRESSION_2 0xFFFFFF02
#define VNC_ENCODING_TYPE_COMPRESSION_3 0xFFFFFF03
#define VNC_ENCODING_TYPE_COMPRESSION_4 0xFFFFFF04
#define VNC_ENCODING_TYPE_COMPRESSION_5 0xFFFFFF05
#define VNC_ENCODING_TYPE_COMPRESSION_6 0xFFFFFF06
#define VNC_ENCODING_TYPE_COMPRESSION_7 0xFFFFFF07
#define VNC_ENCODING_TYPE_COMPRESSION_8 0xFFFFFF08
#define VNC_ENCODING_TYPE_COMPRESSION_9 0xFFFFFF09
#define VNC_ENCODING_TYPE_WMVi 0x574D5669
#define VNC_ENCODING_TYPE_CACHE 0xFFFF0000
#define VNC_ENCODING_TYPE_CACHE_ENABLE 0xFFFF0001
#define VNC_ENCODING_TYPE_XOR_ZLIB 0xFFFF0002
#define VNC_ENCODING_TYPE_XOR_MONO_ZLIB 0xFFFF0003
#define VNC_ENCODING_TYPE_XOR_MULTI_ZLIB 0xFFFF0004
#define VNC_ENCODING_TYPE_SOLID_COLOR 0xFFFF0005
#define VNC_ENCODING_TYPE_XOR_ENABLE 0xFFFF0006
#define VNC_ENCODING_TYPE_CACHE_ZIP 0xFFFF0007
#define VNC_ENCODING_TYPE_SOL_MONO_ZIP 0xFFFF0008
#define VNC_ENCODING_TYPE_ULTRA_ZIP 0xFFFF0009
#define VNC_ENCODING_TYPE_SERVER_STATE 0xFFFF8000
#define VNC_ENCODING_TYPE_ENABLE_KEEP_ALIVE 0xFFFF8001
#define VNC_ENCODING_TYPE_FTP_PROTO_VER 0xFFFF8002
#define VNC_ENCODING_TYPE_POINTER_CHANGE 0xFFFFFEFF     //-257
#define VNC_ENCODING_TYPE_EXT_KEY_EVENT 0xFFFFFEFE      //-258
#define VNC_ENCODING_TYPE_AUDIO 259                     // TODO:from wireshark, is this right?
#define VNC_ENCODING_TYPE_DESKTOP_NAME 0xFFFFFECD       //-307
#define VNC_ENCODING_TYPE_EXTENDED_DESK_SIZE 0xFFFFFECC //-308
#define VNC_ENCODING_TYPE_KEYBOARD_LED_STATE 0XFFFE0000
#define VNC_ENCODING_TYPE_SUPPORTED_MESSAGES 0XFFFE0001
#define VNC_ENCODING_TYPE_SUPPORTED_ENCODINGS 0XFFFE0002
#define VNC_ENCODING_TYPE_SERVER_IDENTITY 0XFFFE0003
#define VNC_ENCODING_TYPE_MIRRORLINK 0xFFFFFDF5
#define VNC_ENCODING_TYPE_CONTEXT_INFORMATION 0xFFFFFDF4
#define VNC_ENCODING_TYPE_SLRLE 0xFFFFFDF3
#define VNC_ENCODING_TYPE_TRANSFORM 0xFFFFFDF2
#define VNC_ENCODING_TYPE_HSML 0xFFFFFDF1
#define VNC_ENCODING_TYPE_H264 0X48323634

#define PADDING_LEN 3

using namespace std;

enum pkt_direction {
    PKTFROMUNKNOWN = 0,
    PKTFROMCLIENT,
    PKTFROMSERVER,
};

enum parse_done_msg {
    ENCODING_TYPE,
    PIXEL_FORMAT,
};
typedef enum {
    VNC_SECURITY_TYPE_INVALID = 0,
    VNC_SECURITY_TYPE_NONE = 1,
    VNC_SECURITY_TYPE_VNC = 2,
    VNC_SECURITY_TYPE_RA2 = 5,
    VNC_SECURITY_TYPE_RA2ne = 6,
    VNC_SECURITY_TYPE_TIGHT = 16,
    VNC_SECURITY_TYPE_ULTRA = 17,
    VNC_SECURITY_TYPE_TLS = 18,
    VNC_SECURITY_TYPE_VENCRYPT = 19,
    VNC_SECURITY_TYPE_GTK_VNC_SASL = 20,
    VNC_SECURITY_TYPE_MD5_HASH_AUTH = 21,
    VNC_SECURITY_TYPE_XVP = 22,
    VNC_SECURITY_TYPE_ARD = 30,
    VNC_TIGHT_AUTH_TGHT_ULGNAUTH = 119,
    VNC_TIGHT_AUTH_TGHT_XTRNAUTH = 130,
    VNC_VENCRYPT_AUTH_PLAIN = 256,
    VNC_VENCRYPT_AUTH_TLSNONE = 257,
    VNC_VENCRYPT_AUTH_TLSVNC = 258,
    VNC_VENCRYPT_AUTH_TLSPLAIN = 259,
    VNC_VENCRYPT_AUTH_X509_NONE = 260,
    VNC_VENCRYPT_AUTH_X509_VNC = 261,
    VNC_VENCRYPT_AUTH_X509_PLAIN = 262,
    VNC_VENCRYPT_AUTH_TLSSASL = 263,
    VNC_VENCRYPT_AUTH_X509_SASL = 264
} vnc_security_types_e;

typedef enum {
    VNC_SESSION_STATE_SERVER_VERSION,
    VNC_SESSION_STATE_CLIENT_VERSION,

    VNC_SESSION_STATE_SECURITY,
    VNC_SESSION_STATE_SECURITY_TYPES,

    VNC_SESSION_STATE_TIGHT_TUNNELING_CAPABILITIES,
    VNC_SESSION_STATE_TIGHT_TUNNEL_TYPE_REPLY,
    VNC_SESSION_STATE_TIGHT_AUTH_CAPABILITIES,
    VNC_SESSION_STATE_TIGHT_AUTH_TYPE_REPLY,
    VNC_SESSION_STATE_TIGHT_UNKNOWN_PACKET3,

    VNC_SESSION_STATE_VNC_AUTHENTICATION_CHALLENGE,
    VNC_SESSION_STATE_VNC_AUTHENTICATION_RESPONSE,

    VNC_SESSION_STATE_ARD_AUTHENTICATION_CHALLENGE,
    VNC_SESSION_STATE_ARD_AUTHENTICATION_RESPONSE,

    VNC_SESSION_STATE_SECURITY_RESULT,

    VNC_SESSION_STATE_VENCRYPT_SERVER_VERSION,
    VNC_SESSION_STATE_VENCRYPT_CLIENT_VERSION,
    VNC_SESSION_STATE_VENCRYPT_AUTH_CAPABILITIES,
    VNC_SESSION_STATE_VENCRYPT_AUTH_TYPE_REPLY,
    VNC_SESSION_STATE_VENCRYPT_AUTH_ACK,

    VNC_SESSION_STATE_CLIENT_INIT,
    VNC_SESSION_STATE_SERVER_INIT,

    VNC_SESSION_STATE_TIGHT_INTERACTION_CAPS,

    VNC_SESSION_STATE_NORMAL_TRAFFIC
} vnc_session_state_e;

typedef enum {
    /* Required */
    VNC_CLIENT_MESSAGE_TYPE_SET_PIXEL_FORMAT = 0,
    VNC_CLIENT_MESSAGE_TYPE_SET_ENCODINGS = 2,
    VNC_CLIENT_MESSAGE_TYPE_FRAMEBUF_UPDATE_REQ = 3,
    VNC_CLIENT_MESSAGE_TYPE_KEY_EVENT = 4,
    VNC_CLIENT_MESSAGE_TYPE_POINTER_EVENT = 5,
    VNC_CLIENT_MESSAGE_TYPE_CLIENT_CUT_TEXT = 6,
    /* Optional */
    VNC_CLIENT_MESSAGE_TYPE_MIRRORLINK = 128,
    VNC_CLIENT_MESSAGE_TYPE_ENABLE_CONTINUOUS_UPDATES = 150, /* TightVNC */
    VNC_CLIENT_MESSAGE_TYPE_FENCE = 248,                     /* TigerVNC */
    VNC_CLIENT_MESSAGE_TYPE_XVP = 250,
    VNC_CLIENT_MESSAGE_TYPE_SETR_DESKTOP_SIZE = 251,
    VNC_CLIENT_MESSAGE_TYPE_TIGHT = 252,
    VNC_CLIENT_MESSAGE_TYPE_GII = 253,
    VNC_CLIENT_MESSAGE_TYPE_QEMU = 255
} vnc_client_message_types_e;

typedef enum {
    VNC_SERVER_MESSAGE_TYPE_FRAMEBUFFER_UPDATE = 0,
    VNC_SERVER_MESSAGE_TYPE_SET_COLORMAP_ENTRIES = 1,
    VNC_SERVER_MESSAGE_TYPE_RING_BELL = 2,
    VNC_SERVER_MESSAGE_TYPE_CUT_TEXT = 3,
    VNC_SERVER_MESSAGE_TYPE_MIRRORLINK = 128,
    VNC_SERVER_MESSAGE_TYPE_END_CONTINUOUS_UPDATES = 150, /* TightVNC */
    VNC_SERVER_MESSAGE_TYPE_FENCE = 248,                  /* TigerVNC */
    VNC_SERVER_MESSAGE_TYPE_XVP = 250,
    VNC_SERVER_MESSAGE_TYPE_TIGHT = 252,
    VNC_SERVER_MESSAGE_TYPE_GII = 253,
    VNC_SERVER_MESSAGE_TYPE_QEMU = 255
} vnc_server_message_types_e;

class vnc_startup_msg {
  public:
    vnc_startup_msg() {
        server_protocol_ver = "";
        client_protocol_ver = "";
        client_sectype_seleted = "";
        server_authen_challenge = "";
        client_authen_response = "";
        authen_result = "";
        share_desktop_flag = 0;
    }
    string server_protocol_ver;
    string client_protocol_ver;
    vector<string> server_sectype_supported;
    string client_sectype_seleted;
    string server_authen_challenge;
    string client_authen_response;
    string authen_result;
    uint8_t share_desktop_flag;
};

class vnc_server_fb_para_msg {
  public:
    vnc_server_fb_para_msg() {
        fb_width = 0;
        fb_height = 0;
        bits_per_pixel = 0;
        depth = 0;
        big_endian_flag = 0;
        true_color_flag = 0;
        red_maximum = 0;
        green_maximum = 0;
        blue_maximum = 0;
        red_shift = 0;
        green_shift = 0;
        blue_shift = 0;
        desktop_name = "";
    }
    uint16_t fb_width;
    uint16_t fb_height;
    uint8_t bits_per_pixel;
    uint8_t depth;
    uint8_t big_endian_flag;
    uint8_t true_color_flag;
    uint16_t red_maximum;
    uint16_t green_maximum;
    uint16_t blue_maximum;
    uint8_t red_shift;
    uint8_t green_shift;
    uint8_t blue_shift;
    string desktop_name;
};

class vnc_pixel_format {
  public:
    vnc_pixel_format() {
        bits_per_pixel = 0;
        depth = 0;
        big_endian_flag = 0;
        true_color_flag = 0;
        red_maximum = 0;
        green_maximum = 0;
        blue_maximum = 0;
        red_shift = 0;
        green_shift = 0;
        blue_shift = 0;
    }
    uint8_t bits_per_pixel;
    uint8_t depth;
    uint8_t big_endian_flag;
    uint8_t true_color_flag;
    uint16_t red_maximum;
    uint16_t green_maximum;
    uint16_t blue_maximum;
    uint8_t red_shift;
    uint8_t green_shift;
    uint8_t blue_shift;
};

class vnc_set_encoding_msg {
  public:
    vector<string> encoding_types;
};

class vnc_conversation_info {
  public:
    vnc_conversation_info() {
        tight_enabled = false;
        server_proto_ver = 0;
        client_proto_ver = 0;
        security_type_selected = 0;
        vnc_next_state = VNC_SESSION_STATE_SERVER_VERSION;
    }
    bool tight_enabled;
    double server_proto_ver;
    double client_proto_ver;
    uint8_t security_type_selected;
    vnc_session_state_e vnc_next_state;
};
class vnc_session {
  public:
    vnc_session() {
        thread_id = 0;
        p_data = NULL;
        data_len = 0;
        p_startup_msg = NULL;
        p_fb_para_msg = NULL;
        p_pixel_format = NULL;
        p_set_encoding_msg = NULL;
        p_set_encoding_msg = NULL;
        p_conversation_info = NULL;
        msg_send_done = false;
        memset(msg_parse_done, 0, sizeof(msg_parse_done));
    }
    uint32_t thread_id;
    uint8_t *p_data;
    uint32_t data_len;
    enum pkt_direction pktdirec;
    vnc_startup_msg *p_startup_msg;
    vnc_server_fb_para_msg *p_fb_para_msg;
    vnc_pixel_format *p_pixel_format;
    vnc_set_encoding_msg *p_set_encoding_msg;
    vnc_conversation_info *p_conversation_info;

    bool msg_parse_done[8];
    bool msg_send_done;
};
#endif /*VNC_STR_H*/

/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-09-04 11:27:03
 * @LastEditors: x<PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-09-04 13:54:55
 * @Description:
 */

#ifndef VNC_PLUGIN_H
#define VNC_PLUGIN_H
#include "vnc_str.h"
#include <TH_engine_interface.h>
#include <c_ip.h>
#include <iomanip>
#include <map>
#include <proto_parse_session.h>
#include <sstream>
#include <stdio.h>
#include <xml_parse.h>

using namespace std;

extern "C" {
int get_plugin_id();
session_pasre_base *attach();
};

#include "dnumstr.h"

class vnc_plugin : public session_pasre_base {
  public:
    vnc_plugin();
    ~vnc_plugin();
    virtual void reload();
    virtual bool potocol_init(session_pub *p_sess, c_packet *p_pack);
    virtual bool potocol_sign_judge(session_pub *p_sess, c_packet *p_pack);
    virtual bool potocol_parse_handle(session_pub *p_sess, c_packet *p_packet);
    virtual void potocol_data_handle(session_pub *p_sess, c_packet *p_packet);
    virtual bool time_out(session_pub *p_sess, uint32_t check_time);
    virtual void resources_recovery(session_pub *p_sess);

  private:
    string bin2hex(const char *p_data, uint32_t len);
    string bin2string(const char *p_data, uint32_t len);
    string bin2ascii(const char *p_data, uint32_t len);
    void vnc_msg_send(session_pub *p_session);
    void init_vnc_session(vnc_session *p_vnc_session);
    void reset_vnc_session(vnc_session *p_vnc_session);
    bool parse_startup_msg(vnc_session *p_vnc_session);
    void parse_c2s_msg(vnc_session *p_vnc_session);
    void parse_s2c_msg(vnc_session *p_vnc_session);
    void parse_set_encoding_msg(vnc_session *p_vnc_session);
    void parse_set_pixel_format_msg(vnc_session *p_vnc_session);
    void parse_server_framebuffer_para_msg(vnc_session *p_vnc_session);
    // void c2s_msg_parse(vnc_session* p_vnc_session, unsigned char directory);
};
#endif /*VNC_PLUGIN_H*/

/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-09-04 10:41:21
 * @LastEditors: x<PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-09-04 11:45:43
 * @Description:
 */

#ifndef XDMCP_STR_H
#define XDMCP_STR_H

#include <list>
#include <packet.h>
#include <session_pub.h>
#include <stdint.h>
#include <string>

/* 操作码定义 */
#define XDMCP_BROADCAST_QUERY 1
#define XDMCP_QUERY 2
#define XDMCP_INDIRECT_QUERY 3
#define XDMCP_FORWARD_QUERY 4
#define XDMCP_WILLING 5
#define XDMCP_UNWILLING 6
#define XDMCP_REQUEST 7
#define XDMCP_ACCEPT 8
#define XDMCP_DECLINE 9
#define XDMCP_MANAGE 10
#define XDMCP_REFUSE 11
#define XDMCP_FAILED 12
#define XDMCP_KEEPALIVE 13
#define XDMCP_ALIVE 14

#define XDMCP_FIXED_LEN 6

#define CONN_TYPE_IPV4 0x0000
#define CONN_TYPE_IPV6 0x0006

using namespace std;

enum pkt_direction {
    PKTFROMCLIENT = 1,
    PKTFROMSERVER,
};

enum xdmcp_conn_status {
    XDMCP_CONN_WILLING = 0,
    XDMCP_CONN_UNWILLING,
    XDMCP_CONN_REQUEST,
    XDMCP_CONN_ACCEPT,
    XDMCP_CONN_DECLINE,
    XDMCP_CONN_REFUSED,
    XDMCP_CONN_FAILED,
    XDMCP_CONN_MANAGED,
    XDMCP_CONN_ESTABLISHED
};

/*
A Query packet is sent from the display to a specific host to ask if that host is willing to provide management services to this display.
Display -> Manager
*/
class xdmcp_query_msg {
  public:
    vector<string> authen_names; // Specifies a list of authentication names that the display supports
};

class xdmcp_forward_qeury_msg {
  public:
    xdmcp_forward_qeury_msg() {
        client_addr = "";
        client_port = "";
    }
    string client_addr;
    string client_port;
    vector<string> authen_names; // Specifies a list of authentication names that the display supports
};

/*
A Willing packet is sent by managers that may service connections from this display.
Manager -> Display
*/
class xdmcp_willing_msg {
  public:
    xdmcp_willing_msg() {
        authen_name = "";
        hostname = "";
        status = "";
    }
    string authen_name; // Specifies the authentication method selected by manager
    string hostname;
    string status; // a human readable string describing the status of the host
};

/*
An Unwilling packet is sent by managers in response to direct Query requests (as opposed to BroadcastQuery or IndirectQuery requests) if the manager will not accept requests for management.
Manager -> Display
*/
class xdmcp_unwilling_msg {
  public:
    xdmcp_unwilling_msg() {
        hostname = "";
        status = "";
    }
    string hostname;
    string status; // indicate to the user a reason for the refusal of service
};

/*
A Request packet is sent by a display to a specific host to request a session ID in preparation for a establishing a connection.
Display -> Manager
*/
class xdmcp_request_msg {
  public:
    xdmcp_request_msg() {
        display_number = 0;
        authen_name = "";
        authen_data = "";
        manufacture_disp_id = "";
    }
    uint16_t display_number; // Specifies the index of this particular server for the host on which the display is resident.
    map<string, string> addr_types;
    string authen_name;          // Specifies the authentication protocol that the display expects the manager to validate itself with
    string authen_data;          // is expected to contain data that the manager will interpret, modify and use to authenticate itself
    vector<string> author_names; // Specifies which types of authorization the display supports
    string manufacture_disp_id;
};

/*
An Accept packet is sent by a manager in response to a Request packet if the manager is willing to establish a connection for the display.
Manager -> Display
*/
class xdmcp_accept_msg {
  public:
    xdmcp_accept_msg() {
        session_id = 0;
        authen_name = "";
        authen_data = "";
        author_name = "";
        author_data = "";
    }
    uint32_t session_id; // Identifies the session that can be started by the manager.
    string authen_name;
    string authen_data; // the data sent back to the display to authenticate the manager.
    string author_name;
    string author_data; // the data sent to the display to indicate the type of authorization the manager will be using in the first call to XOpenDisplay after the Manage packet is received.
};

/*
A Decline packet is sent by a manager in response to a Request packet if the manager is unwilling to establish a connection for the display.
Manager -> Display
*/
class xdmcp_decline_msg {
  public:
    xdmcp_decline_msg() {
        status = "";
        authen_name = "";
        authen_data = "";
    }
    string status; // a human readable string indicating the reason for refusal of service.
    string authen_name;
    string authen_data; // the data sent back to the display to authenticate the manager.
};

/*
A Manage packet is sent by a display to ask the manager to begin a session on the display.
Display -> Manager
*/
class xdmcp_manage_msg {
  public:
    xdmcp_manage_msg() {
        session_id = 0;
        display_number = 0;
        display_class = "";
    }
    uint32_t session_id;     // Should contain the nonzero session ID returned in the Accept packet.
    uint16_t display_number; // Must match the value sent in the previous Request packet.
    string display_class;    // Specifies the class of the display.
};

/*
A Refuse packet is sent by a manager when the Session ID received in the Manage packet does not match the current Session ID.
Manager -> Display
*/
class xdmcp_refuse_msg {
  public:
    xdmcp_refuse_msg() { session_id = 0; }
    uint32_t session_id; // Should be set to the Session ID received in the Manage packet.
};

/*
A Failed packet is sent by a manager when it has problems establishing the initial X connection in response to the Manage packet.
Manager -> Display
*/
class xdmcp_failed_msg {
  public:
    xdmcp_failed_msg() {
        session_id = 0;
        status = "";
    }
    uint32_t session_id; // Should be set to the Session ID received in the Manage packet.
    string status;       // a human readable string indicating the reason for failure
};

/*
A KeepAlive packet can be sent at any time during the session by a display to discover if the manager is running.
Display -> Manager
*/
class xdmcp_keepalive_msg {
  public:
    xdmcp_keepalive_msg() {
        display_number = 0;
        session_id = 0;
    }
    uint16_t display_number; // Set to the display index for the display host.
    uint32_t session_id;     // Should be set to the Session ID received in the Manage packet during the negotiation for the current session.
};

/*
An Alive packet is sent in response to a KeepAlive request.
Manager -> Display
*/
class xdmcp_alive_msg {
  public:
    xdmcp_alive_msg() {
        session_running = "";
        session_id = 0;
    }
    string session_running; // Indicates that the session identified by Session ID is currently active.
    uint32_t session_id;    // Specifies the ID of the currently running session; if any. When no session is active this field should be zero.
};

class xdmcp_session {
  public:
    uint32_t thread_id;
    uint8_t *p_data;
    uint32_t data_len;
    enum pkt_direction pktdirec;
    xdmcp_query_msg *query_msg;
    xdmcp_forward_qeury_msg *forward_qeury_msg;
    xdmcp_willing_msg *willing_msg;
    xdmcp_unwilling_msg *unwilling_msg;
    xdmcp_request_msg *request_msg;
    xdmcp_accept_msg *accept_msg;
    xdmcp_decline_msg *decline_msg;
    xdmcp_manage_msg *manage_msg;
    xdmcp_refuse_msg *refuse_msg;
    xdmcp_failed_msg *failed_msg;
    xdmcp_keepalive_msg *keepalive_msg;
    xdmcp_alive_msg *alive_msg;
    bool enable_authenticate;
    bool msg_parse_done[8];
    bool msg_send_done;
};
#endif /*XDMCP_STR_H*/

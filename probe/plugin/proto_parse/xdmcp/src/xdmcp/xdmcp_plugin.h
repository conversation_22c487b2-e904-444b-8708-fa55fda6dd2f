/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-09-04 11:28:00
 * @LastEditors: x<PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-09-04 11:45:33
 * @Description:
 */

#ifndef XDMCP_PLUGIN_H
#define XDMCP_PLUGIN_H
#include "xdmcp_str.h"
#include <TH_engine_interface.h>
#include <c_ip.h>
#include <iomanip>
#include <map>
#include <proto_parse_session.h>
#include <sstream>
#include <stdio.h>
#include <xml_parse.h>

using namespace std;

extern "C" {
int get_plugin_id();
session_pasre_base *attach();
};

#include "dnumstr.h"

class xdmcp_plugin : public session_pasre_base {
  public:
    xdmcp_plugin();
    ~xdmcp_plugin();
    virtual void reload();
    virtual bool potocol_init(session_pub *p_sess, c_packet *p_pack);
    virtual bool potocol_sign_judge(session_pub *p_sess, c_packet *p_pack);
    virtual bool potocol_parse_handle(session_pub *p_sess, c_packet *p_packet);
    virtual void potocol_data_handle(session_pub *p_sess, c_packet *p_packet);
    virtual bool time_out(session_pub *p_sess, uint32_t check_time);
    virtual void resources_recovery(session_pub *p_sess);

  private:
    string bin2hex(const char *p_data, uint32_t len);
    string bin2string(const char *p_data, uint32_t len);
    string bin2ascii(const char *p_data, uint32_t len);
    int get_ipv4_string(uint32_t ipv4_data, string &ipv4_string);
    int get_ipv6_string(uint32_t ipv6_data[4], string &ipv6_string);
    void xdmcp_msg_send(session_pub *p_session);
    void init_xdmcp_session(xdmcp_session *p_xdmcp_session);
    void reset_xdmcp_session(xdmcp_session *p_xdmcp_session);
    void parse_conn_info(xdmcp_session *p_xdmcp_session, uint8_t *p_start, uint16_t &conn_info_len);
    void parse_authen_info(xdmcp_session *p_xdmcp_session, uint8_t *p_start, uint16_t &authen_info_len);
    void parse_author_info(xdmcp_session *p_xdmcp_session, uint8_t *p_start, uint16_t &author_info_len);
    void parse_query_data(xdmcp_session *p_xdmcp_session);
    void parse_forward_query_data(xdmcp_session *p_xdmcp_session);
    void parse_willing_data(xdmcp_session *p_xdmcp_session);
    void parse_unwilling_data(xdmcp_session *p_xdmcp_session);
    void parse_request_data(xdmcp_session *p_xdmcp_session);
    void parse_accept_data(xdmcp_session *p_xdmcp_session);
    void parse_decline_data(xdmcp_session *p_xdmcp_session);
    void parse_manage_data(xdmcp_session *p_xdmcp_session);
    void parse_refuse_data(xdmcp_session *p_xdmcp_session);
    void parse_failed_data(xdmcp_session *p_xdmcp_session);
    void parse_keepalive_data(xdmcp_session *p_xdmcp_session);
    void parse_alive_data(xdmcp_session *p_xdmcp_session);
};
#endif /*XDMCP_PLUGIN_H*/

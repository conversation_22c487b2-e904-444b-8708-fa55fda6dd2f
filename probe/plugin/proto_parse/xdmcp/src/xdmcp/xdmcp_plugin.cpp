/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-09-04 11:05:34
 * @LastEditors: x<PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-09-04 13:51:59
 * @Description:
 */

#include "xdmcp_plugin.h"
#include <commit_tools.h>
#include <sys/stat.h>
#include <sys/time.h>

extern "C" {
int get_plugin_id() { return 10189; }
session_pasre_base *attach() { return new xdmcp_plugin(); }
}

xdmcp_plugin::xdmcp_plugin() { reload(); }

xdmcp_plugin::~xdmcp_plugin() { ; }

void xdmcp_plugin::reload() { ; }

string xdmcp_plugin::bin2hex(const char *p_data, uint32_t len) {
    string s = "";

    if (p_data == NULL || len <= 0) {
        return "";
    }

    char formate_char[3];
    for (uint32_t i = 0; i < len; ++i) {
        sprintf(formate_char, "%02x", (unsigned char)p_data[i]);
        s.append(formate_char, 2);
    }
    return s;
}

string xdmcp_plugin::bin2string(const char* p_data, uint32_t len) {
    // Check for null pointer
    if (p_data == nullptr) {
        return std::string();
    }
    
    // Check for valid length
    if (len == 0) {
        return std::string();
    }
    
    // Create string safely
    return std::string(p_data, len);
}

string xdmcp_plugin::bin2ascii(const char *p_data, uint32_t len) {
    string s = "";
    char formate_char[3];

    if (p_data == NULL || len <= 0) {
        return "";
    }

    for (uint32_t i = 0; i < len; ++i) {
        if (p_data[i] >= 32 && p_data[i] <= 126) { // printable characters
            sprintf(formate_char, "%c", (unsigned char)p_data[i]);
        } else {
            sprintf(formate_char, "%c", ' ');
        }
        s.append(formate_char, 1);
    }
    return s;
}

int xdmcp_plugin::get_ipv4_string(uint32_t ipv4_data, string &ipv4_string) {

    char ip4str[INET_ADDRSTRLEN];

    struct sockaddr_in in_addr;
    in_addr.sin_family = AF_INET;
    in_addr.sin_addr.s_addr = ipv4_data;

    char str[INET_ADDRSTRLEN];
    if (inet_ntop(AF_INET, &in_addr.sin_addr, ip4str, INET_ADDRSTRLEN) != NULL) {
        ipv4_string.assign(ip4str);
        return 0;
    } else {
        return -1;
    }
}

int xdmcp_plugin::get_ipv6_string(uint32_t ipv6_data[4], string &ipv6_string) {

    char ip6str[INET6_ADDRSTRLEN];
    struct sockaddr_in6 sockaddr6;
    sockaddr6.sin6_family = AF_INET6;
    sockaddr6.sin6_addr.__in6_u.__u6_addr32[0] = ipv6_data[0];
    sockaddr6.sin6_addr.__in6_u.__u6_addr32[1] = ipv6_data[1];
    sockaddr6.sin6_addr.__in6_u.__u6_addr32[2] = ipv6_data[2];
    sockaddr6.sin6_addr.__in6_u.__u6_addr32[3] = ipv6_data[3];
    if (inet_ntop(AF_INET6, &sockaddr6.sin6_addr, ip6str, INET6_ADDRSTRLEN) != NULL) {
        ipv6_string.assign(ip6str);
        return 0;
    } else {
        return -1;
    }
}

void xdmcp_plugin::parse_conn_info(xdmcp_session *p_xdmcp_session, uint8_t *p_start, uint16_t &conn_info_len) {

    uint8_t *pcur = p_start;
    uint8_t conn_type_cnt;
    uint8_t conn_addr_cnt;
    vector<uint16_t> conn_types;
    uint16_t conn_type;
    string conn_addr;
    uint32_t ipv4_addr;
    uint32_t ipv6_addr[4];
    map<uint16_t, string> conn_type_str;
    uint16_t addr_len;

    if (p_xdmcp_session == NULL || p_start == NULL) {
        return;
    }

    conn_type_str.insert(make_pair(0x0000, "Internet"));
    conn_type_str.insert(make_pair(0x0006, "InternetV6"));

    conn_type_cnt = *pcur;

    pcur += 1;
    for (int i = 0; i < conn_type_cnt; i++) {
        uint16_t conn_type = ntohs(*(uint16_t *)(pcur + 2 * i));
        conn_types.push_back(conn_type);
    }

    pcur += 2 * conn_type_cnt;
    conn_addr_cnt = *pcur;

    if (conn_type_cnt != conn_addr_cnt) {
        return;
    }

    pcur += 1;
    for (int i = 0; i < conn_addr_cnt; i++) {
        conn_type = conn_types[i];
        addr_len = ntohs(*(uint16_t *)pcur);
        pcur += 2;
        if (conn_type == CONN_TYPE_IPV4) {
            ipv4_addr = *(uint32_t *)pcur;
            pcur += 4;
            if (get_ipv4_string(ipv4_addr, conn_addr) == -1) {
                conn_addr = "unkonwn addr";
            }
        } else if (conn_type == CONN_TYPE_IPV6) {
            for (int j = 0; j < 4; j++) {
                ipv6_addr[j] = *(uint32_t *)pcur;
                pcur += 4;
            }
            if (get_ipv6_string(ipv6_addr, conn_addr) == -1) {
                conn_addr = "unkonwn addr";
            }
        } else {
            conn_addr = "unkonwn addr";
        }
        p_xdmcp_session->request_msg->addr_types.insert(make_pair(conn_addr, conn_type_str[conn_type]));
    }
    conn_info_len = pcur - p_start;
}

void xdmcp_plugin::parse_authen_info(xdmcp_session *p_xdmcp_session, uint8_t *p_start, uint16_t &authen_info_len) {

    uint8_t *pcur = p_start;
    uint16_t authen_name_len;
    uint16_t authen_data_len;
    string authen_name;
    string authen_data;
    uint16_t opcode;

    if (p_xdmcp_session == NULL || p_start == NULL) {
        return;
    }

    opcode = ntohs(*(uint16_t *)(p_xdmcp_session->p_data + 2));

    authen_name_len = ntohs(*(uint16_t *)pcur);

    pcur += 2;
    if (authen_name_len != 0) {
        authen_name = bin2ascii((const char *)pcur, authen_name_len);
    }

    pcur += authen_name_len;
    authen_data_len = ntohs(*(uint16_t *)pcur);

    pcur += 2;
    if (authen_data_len != 0) {
        authen_data = bin2string((const char *)pcur, authen_data_len);
    }

    authen_info_len = pcur - p_start;

    if (authen_name_len != 0 && authen_data_len != 0) {
        switch (opcode) {
        case XDMCP_REQUEST:
            p_xdmcp_session->enable_authenticate = true;
            p_xdmcp_session->request_msg->authen_name = authen_name;
            p_xdmcp_session->request_msg->authen_data = authen_data;
            break;
        case XDMCP_ACCEPT:
            p_xdmcp_session->accept_msg->authen_name = authen_name;
            p_xdmcp_session->accept_msg->authen_data = authen_data;
            break;
        case XDMCP_DECLINE:
            p_xdmcp_session->decline_msg->authen_name = authen_name;
            p_xdmcp_session->decline_msg->authen_data = authen_data;
            break;

        default:
            break;
        }
    }
}

void xdmcp_plugin::parse_author_info(xdmcp_session *p_xdmcp_session, uint8_t *p_start, uint16_t &author_info_len) {

    uint8_t *pcur = p_start;
    uint8_t author_name_cnt;
    uint16_t author_name_len;
    uint16_t author_data_len;
    string author_name;
    string author_data;
    uint16_t opcode;

    if (p_xdmcp_session == NULL || p_start == NULL) {
        return;
    }

    opcode = ntohs(*(uint16_t *)(p_xdmcp_session->p_data + 2));

    switch (opcode) {
    case XDMCP_REQUEST:
        author_name_cnt = *pcur;
        pcur++;
        for (int i = 0; i < author_name_cnt; i++) {
            author_name_len = ntohs(*(uint16_t *)pcur);
            pcur += 2;
            if (author_name_len != 0) {
                author_name = bin2ascii((const char *)pcur, author_name_len);
            }
            pcur += author_name_len;
            p_xdmcp_session->request_msg->author_names.push_back(author_name);
        }
        break;
    case XDMCP_ACCEPT:
        author_name_len = ntohs(*(uint16_t *)pcur);
        pcur += 2;
        if (author_name_len != 0) {
            author_name = bin2ascii((const char *)pcur, author_name_len);
        }
        pcur += author_name_len;
        author_data_len = ntohs(*(uint16_t *)pcur);
        pcur += 2;
        if (author_data_len != 0) {
            author_data = bin2string((const char *)pcur, author_data_len);
        }
        p_xdmcp_session->accept_msg->author_name = author_name;
        p_xdmcp_session->accept_msg->author_data = author_data;
        break;

    default:
        break;
    }

    author_info_len = pcur - p_start;
}

void xdmcp_plugin::init_xdmcp_session(xdmcp_session *p_xdmcp_session) {

    if (p_xdmcp_session == NULL) {
        return;
    }
    memset(p_xdmcp_session, 0, sizeof(xdmcp_session));
}

void xdmcp_plugin::reset_xdmcp_session(xdmcp_session *p_xdmcp_session) {

    if (p_xdmcp_session == NULL) {
        return;
    }

    if (p_xdmcp_session->query_msg) {

        delete p_xdmcp_session->query_msg;
        p_xdmcp_session->query_msg = NULL;
    }
    if (p_xdmcp_session->forward_qeury_msg) {

        delete p_xdmcp_session->forward_qeury_msg;
        p_xdmcp_session->forward_qeury_msg = NULL;
    }
    if (p_xdmcp_session->willing_msg) {

        delete p_xdmcp_session->willing_msg;
        p_xdmcp_session->willing_msg = NULL;
    }
    if (p_xdmcp_session->unwilling_msg) {

        delete p_xdmcp_session->unwilling_msg;
        p_xdmcp_session->unwilling_msg = NULL;
    }
    if (p_xdmcp_session->request_msg) {

        delete p_xdmcp_session->request_msg;
        p_xdmcp_session->request_msg = NULL;
    }
    if (p_xdmcp_session->accept_msg) {

        delete p_xdmcp_session->accept_msg;
        p_xdmcp_session->accept_msg = NULL;
    }
    if (p_xdmcp_session->decline_msg) {

        delete p_xdmcp_session->decline_msg;
        p_xdmcp_session->decline_msg = NULL;
    }
    if (p_xdmcp_session->manage_msg) {

        delete p_xdmcp_session->manage_msg;
        p_xdmcp_session->manage_msg = NULL;
    }
    if (p_xdmcp_session->refuse_msg) {

        delete p_xdmcp_session->refuse_msg;
        p_xdmcp_session->refuse_msg = NULL;
    }
    if (p_xdmcp_session->failed_msg) {

        delete p_xdmcp_session->failed_msg;
        p_xdmcp_session->failed_msg = NULL;
    }
    if (p_xdmcp_session->keepalive_msg) {

        delete p_xdmcp_session->keepalive_msg;
        p_xdmcp_session->keepalive_msg = NULL;
    }
    if (p_xdmcp_session->alive_msg) {

        delete p_xdmcp_session->alive_msg;
        p_xdmcp_session->alive_msg = NULL;
    }
}

void xdmcp_plugin::parse_query_data(xdmcp_session *p_xdmcp_session) {

    uint8_t *pcur = p_xdmcp_session->p_data;
    uint16_t msg_len;
    uint8_t authen_name_sent_num;
    uint16_t name_len;
    string authen_name;

    if (p_xdmcp_session == NULL) {
        return;
    }

    if (p_xdmcp_session->pktdirec != PKTFROMCLIENT) {
        // return;
    }

    pcur += 4;
    msg_len = ntohs(*(uint16_t *)pcur);

    if (msg_len + XDMCP_FIXED_LEN != p_xdmcp_session->data_len) { //
        return;
    }

    if (msg_len == 1) { // nothing
        return;
    }

    pcur += 2;
    authen_name_sent_num = *pcur;

    pcur++;
    for (int i = 0; i < authen_name_sent_num; i++) {
        name_len = ntohs(*(uint16_t *)pcur);
        pcur += 2;
        authen_name = bin2ascii((const char *)pcur, name_len);
        p_xdmcp_session->query_msg->authen_names.push_back(authen_name);
    }
}

void xdmcp_plugin::parse_forward_query_data(xdmcp_session *p_xdmcp_session) {

    uint8_t *pcur = p_xdmcp_session->p_data;
    uint16_t msg_len;
    uint16_t client_addr_len;
    uint16_t client_port_len;
    uint16_t name_len;
    uint8_t authen_name_send_nb;
    string client_addr;
    string client_port;
    string authen_name;

    if (p_xdmcp_session == NULL) {
        return;
    }

    pcur += 4;
    msg_len = ntohs(*(uint16_t *)pcur);

    if (msg_len + XDMCP_FIXED_LEN != p_xdmcp_session->data_len) { //
        return;
    }

    if (msg_len == 1) { // nothing
        return;
    }

    pcur += 2;
    client_addr_len = ntohs(*(uint16_t *)pcur);
    if (client_addr_len != 0) {
        client_addr = bin2ascii((const char *)(pcur + 2), client_addr_len);
        p_xdmcp_session->forward_qeury_msg->client_addr = client_addr;
    }

    pcur += client_addr_len + 2;
    client_port_len = ntohs(*(uint16_t *)pcur);
    if (client_port_len != 0) {
        client_port = bin2ascii((const char *)(pcur + 2), client_port_len);
        p_xdmcp_session->forward_qeury_msg->client_port = client_port;
    }

    pcur += client_port_len + 2;
    authen_name_send_nb = *pcur;

    pcur++;
    for (int i = 0; i < authen_name_send_nb; i++) {
        name_len = ntohs(*(uint16_t *)pcur);
        pcur += 2;
        if (name_len != 0) {
            authen_name = bin2ascii((const char *)pcur, name_len);
            p_xdmcp_session->forward_qeury_msg->authen_names.push_back(authen_name);
        }
        pcur += name_len;
    }
    return;
}

void xdmcp_plugin::parse_willing_data(xdmcp_session *p_xdmcp_session) {

    uint8_t *pcur = p_xdmcp_session->p_data;
    uint16_t msg_len;
    uint8_t authen_name_len;
    uint16_t hostname_len;
    uint16_t status_len;
    string authen_name = "";
    string hostname = "";
    string status = "";

    if (p_xdmcp_session == NULL) {
        return;
    }

    if (p_xdmcp_session->pktdirec != PKTFROMSERVER) {
        // return; 没找到xdmcp报文方向判断不准的原因，暂时不进行方向判断，不影响解析功能
    }

    pcur += 4;
    msg_len = ntohs(*(uint16_t *)pcur);
    if (msg_len + XDMCP_FIXED_LEN != p_xdmcp_session->data_len) { //
        return;
    }

    pcur += 2;
    authen_name_len = ntohs(*(uint16_t *)pcur);
    if (authen_name_len != 0) {
        authen_name = bin2ascii((const char *)(pcur + 2), authen_name_len);
        p_xdmcp_session->willing_msg->authen_name = authen_name;
    }

    pcur += authen_name_len + 2;
    hostname_len = ntohs(*(uint16_t *)pcur);
    if (hostname_len != 0) {
        hostname = bin2ascii((const char *)(pcur + 2), hostname_len);
        p_xdmcp_session->willing_msg->hostname = hostname;
    }

    pcur += hostname_len + 2;
    status_len = ntohs(*(uint16_t *)pcur);
    if (status_len != 0) {
        status = bin2ascii((const char *)(pcur + 2), status_len);
        p_xdmcp_session->willing_msg->status = status;
    }
    p_xdmcp_session->msg_parse_done[XDMCP_CONN_WILLING] = true;
}

void xdmcp_plugin::parse_unwilling_data(xdmcp_session *p_xdmcp_session) {

    uint8_t *pcur = p_xdmcp_session->p_data;
    uint16_t msg_len;
    uint16_t hostname_len;
    uint16_t status_len;
    string hostname = "";
    string status = "";

    if (p_xdmcp_session == NULL) {
        return;
    }

    if (p_xdmcp_session->pktdirec != PKTFROMSERVER) {
        // return;
    }

    pcur += 4;
    msg_len = ntohs(*(uint16_t *)pcur);
    if (msg_len + XDMCP_FIXED_LEN != p_xdmcp_session->data_len) { //
        return;
    }

    pcur += 2;
    hostname_len = ntohs(*(uint16_t *)pcur);
    if (hostname_len != 0) {
        hostname = bin2ascii((const char *)(pcur + 2), hostname_len);
        p_xdmcp_session->unwilling_msg->hostname = hostname;
    }

    pcur += hostname_len + 2;
    status_len = ntohs(*(uint16_t *)pcur);
    if (status_len != 0) {
        status = bin2ascii((const char *)(pcur + 2), status_len);
        p_xdmcp_session->unwilling_msg->status = status;
    }

    p_xdmcp_session->msg_parse_done[XDMCP_CONN_UNWILLING] = true;
}

void xdmcp_plugin::parse_request_data(xdmcp_session *p_xdmcp_session) {

    uint8_t *pcur = p_xdmcp_session->p_data;

    uint16_t msg_len;
    uint16_t display_number;
    uint16_t conn_info_len;
    uint16_t authen_info_len;
    uint16_t author_info_len;
    uint16_t disp_id_len;
    string disp_id;

    if (p_xdmcp_session == NULL) {
        return;
    }

    if (p_xdmcp_session->pktdirec != PKTFROMCLIENT) {
        // return;
    }

    pcur += 4;
    msg_len = ntohs(*(uint16_t *)pcur);

    if (msg_len + XDMCP_FIXED_LEN != p_xdmcp_session->data_len) { //
        return;
    }

    pcur += 2;
    display_number = ntohs(*(uint16_t *)(pcur));
    p_xdmcp_session->request_msg->display_number = display_number;

    pcur += 2;
    parse_conn_info(p_xdmcp_session, pcur, conn_info_len);

    pcur += conn_info_len;
    parse_authen_info(p_xdmcp_session, pcur, authen_info_len);

    pcur += authen_info_len;
    parse_author_info(p_xdmcp_session, pcur, author_info_len);

    pcur += author_info_len;
    disp_id_len = ntohs(*(uint16_t *)pcur);

    pcur += 2;
    if (disp_id_len != 0) {
        disp_id = bin2ascii((const char *)pcur, disp_id_len);
        p_xdmcp_session->request_msg->manufacture_disp_id = disp_id;
    }

    p_xdmcp_session->msg_parse_done[XDMCP_CONN_REQUEST] = true;
}

void xdmcp_plugin::parse_accept_data(xdmcp_session *p_xdmcp_session) {

    uint8_t *pcur = p_xdmcp_session->p_data;
    uint16_t msg_len;
    uint32_t session_id;
    uint16_t authen_info_len;
    uint16_t author_info_len;

    string author_name;
    string author_data;

    if (p_xdmcp_session == NULL) {
        return;
    }

    if (p_xdmcp_session->pktdirec != PKTFROMSERVER) {
        // return;
    }

    pcur += 4;
    msg_len = ntohs(*(uint16_t *)pcur);

    if (msg_len + XDMCP_FIXED_LEN != p_xdmcp_session->data_len) { //
        return;
    }

    pcur += 2;

    session_id = ntohl(*(uint32_t *)pcur);
    p_xdmcp_session->accept_msg->session_id = session_id;

    pcur += 4;

    if (p_xdmcp_session->enable_authenticate) {
        parse_authen_info(p_xdmcp_session, pcur, authen_info_len);
    } else {
        authen_info_len = 4;
    }

    pcur += authen_info_len;

    parse_author_info(p_xdmcp_session, pcur, authen_info_len);

    p_xdmcp_session->msg_parse_done[XDMCP_CONN_ACCEPT] = true;

    return;
}

void xdmcp_plugin::parse_decline_data(xdmcp_session *p_xdmcp_session) {

    uint8_t *pcur = p_xdmcp_session->p_data;
    uint16_t msg_len;
    uint16_t status_len;
    uint16_t authen_info_len;
    uint16_t author_name_len;
    uint16_t author_data_len;
    string status;
    string author_name;
    string author_data;

    if (p_xdmcp_session == NULL) {
        return;
    }

    if (p_xdmcp_session->pktdirec != PKTFROMSERVER) {
        // return;
    }

    pcur += 4;
    msg_len = ntohs(*(uint16_t *)pcur);

    if (msg_len + XDMCP_FIXED_LEN != p_xdmcp_session->data_len) { //
        return;
    }

    pcur += 2;

    status_len = ntohs(*(uint16_t *)pcur);

    pcur += status_len + 2;

    if (p_xdmcp_session->enable_authenticate) {
        parse_authen_info(p_xdmcp_session, pcur, authen_info_len);
    } else {
        authen_info_len = 4;
    }

    p_xdmcp_session->msg_parse_done[XDMCP_CONN_DECLINE] = true;
}

void xdmcp_plugin::parse_manage_data(xdmcp_session *p_xdmcp_session) {

    uint8_t *pcur = p_xdmcp_session->p_data;
    uint16_t msg_len;
    uint32_t session_id;
    uint16_t display_number;
    uint16_t disp_cls_len;
    string display_class;

    if (p_xdmcp_session == NULL) {
        return;
    }

    if (p_xdmcp_session->pktdirec != PKTFROMCLIENT) {
        // return;
    }

    pcur += 4;
    msg_len = ntohs(*(uint16_t *)pcur);

    if (msg_len + XDMCP_FIXED_LEN != p_xdmcp_session->data_len) { //
        return;
    }

    pcur += 2;
    session_id = ntohl(*(uint32_t *)pcur);
    if (session_id != p_xdmcp_session->accept_msg->session_id) {
        return;
    }

    pcur += 4;
    display_number = ntohs(*(uint16_t *)pcur);
    if (display_number != p_xdmcp_session->request_msg->display_number) {
        return;
    }

    pcur += 2;
    disp_cls_len = ntohs(*(uint16_t *)pcur);

    pcur += 2;
    display_class = bin2string((const char *)pcur, disp_cls_len);

    p_xdmcp_session->manage_msg->session_id = session_id;
    p_xdmcp_session->manage_msg->display_number = display_number;
    p_xdmcp_session->manage_msg->display_class = display_class;

    p_xdmcp_session->msg_parse_done[XDMCP_CONN_MANAGED] = true;

    return;
}

void xdmcp_plugin::parse_refuse_data(xdmcp_session *p_xdmcp_session) {

    uint8_t *pcur = p_xdmcp_session->p_data;
    uint16_t msg_len;
    uint32_t session_id;

    if (p_xdmcp_session == NULL) {
        return;
    }

    if (p_xdmcp_session->pktdirec != PKTFROMSERVER) {
        // return;
    }

    pcur += 4;
    msg_len = ntohs(*(uint16_t *)pcur);

    if (msg_len + XDMCP_FIXED_LEN != p_xdmcp_session->data_len) { //
        return;
    }

    pcur += 2;

    session_id = ntohl(*(uint32_t *)pcur);

    if (session_id != p_xdmcp_session->manage_msg->session_id) {
        return;
    }

    p_xdmcp_session->refuse_msg->session_id = session_id;

    p_xdmcp_session->msg_parse_done[XDMCP_CONN_REFUSED] = true;
}

void xdmcp_plugin::parse_failed_data(xdmcp_session *p_xdmcp_session) {

    uint8_t *pcur = p_xdmcp_session->p_data;
    uint16_t msg_len;
    uint16_t status_len;
    uint32_t session_id;
    string status;

    if (p_xdmcp_session == NULL) {
        return;
    }

    if (p_xdmcp_session->pktdirec != PKTFROMSERVER) {
        // return;
    }

    pcur += 4;
    msg_len = ntohs(*(uint16_t *)pcur);

    if (msg_len + XDMCP_FIXED_LEN != p_xdmcp_session->data_len) { //
        return;
    }

    pcur += 2;
    session_id = ntohl(*(uint32_t *)pcur);

    if (session_id != p_xdmcp_session->manage_msg->session_id) {
        return;
    }

    pcur += 4;

    status_len = ntohs(*(uint16_t *)pcur);
    if (status_len != 0) {
        status = bin2ascii((const char *)(pcur + 2), status_len);
    }

    p_xdmcp_session->failed_msg->session_id = session_id;
    p_xdmcp_session->failed_msg->status = status;

    p_xdmcp_session->msg_parse_done[XDMCP_CONN_FAILED] = true;
}

void xdmcp_plugin::parse_keepalive_data(xdmcp_session *p_xdmcp_session) {

    uint8_t *pcur = p_xdmcp_session->p_data;
    uint16_t msg_len;
    uint16_t display_number;
    uint32_t session_id;

    if (p_xdmcp_session == NULL) {
        return;
    }

    if (p_xdmcp_session->pktdirec != PKTFROMCLIENT) {
        // return;
    }

    pcur += 4;
    msg_len = ntohs(*(uint16_t *)pcur);

    if (msg_len + XDMCP_FIXED_LEN != p_xdmcp_session->data_len) { //
        return;
    }

    pcur += 2;
    display_number = ntohs(*(uint16_t *)pcur);

    pcur += 2;
    session_id = ntohl(*(uint32_t *)pcur);

    if (session_id != p_xdmcp_session->accept_msg->session_id) {
        return;
    }

    p_xdmcp_session->keepalive_msg->display_number = display_number;
    p_xdmcp_session->keepalive_msg->session_id = session_id;

    return;
}

void xdmcp_plugin::parse_alive_data(xdmcp_session *p_xdmcp_session) {

    uint8_t *pcur = p_xdmcp_session->p_data;
    uint16_t msg_len;
    uint8_t is_session_running;
    uint32_t session_id;
    string session_running;

    if (p_xdmcp_session == NULL) {
        return;
    }

    if (p_xdmcp_session->pktdirec != PKTFROMSERVER) {
        // return;
    }

    pcur += 4;
    msg_len = ntohs(*(uint16_t *)pcur);

    if (msg_len + XDMCP_FIXED_LEN != p_xdmcp_session->data_len) { //
        return;
    }

    pcur += 2;
    session_running = *pcur;

    if (is_session_running) {
        session_running = "running";
        pcur += 1;
        session_id = ntohl(*(uint32_t *)pcur);
    } else {
        session_running = "not running";
    }

    if (session_id != p_xdmcp_session->accept_msg->session_id) {
        return;
    }

    p_xdmcp_session->alive_msg->session_id = session_id;
    p_xdmcp_session->alive_msg->session_running = session_running;

    return;
}

bool xdmcp_plugin::potocol_init(session_pub *p_session, c_packet *p_packet) {
    if (p_session == NULL || p_packet == NULL) {
        return false;
    }
    proto_parse_session *p_pp_session = (proto_parse_session *)(p_session->p_sp_session);
    xdmcp_session *p_xdmcp_session = (xdmcp_session *)p_pp_session->expansion_data;
    init_xdmcp_session(p_xdmcp_session);
    p_xdmcp_session->thread_id = p_session->thread_id;
    p_xdmcp_session->query_msg = new xdmcp_query_msg();
    p_xdmcp_session->forward_qeury_msg = new xdmcp_forward_qeury_msg();
    p_xdmcp_session->willing_msg = new xdmcp_willing_msg();
    p_xdmcp_session->unwilling_msg = new xdmcp_unwilling_msg();
    p_xdmcp_session->request_msg = new xdmcp_request_msg();
    p_xdmcp_session->accept_msg = new xdmcp_accept_msg();
    p_xdmcp_session->decline_msg = new xdmcp_decline_msg();
    p_xdmcp_session->manage_msg = new xdmcp_manage_msg();
    p_xdmcp_session->refuse_msg = new xdmcp_refuse_msg();
    p_xdmcp_session->failed_msg = new xdmcp_failed_msg();
    p_xdmcp_session->keepalive_msg = new xdmcp_keepalive_msg();
    p_xdmcp_session->alive_msg = new xdmcp_alive_msg();
    return true;
}

bool xdmcp_plugin::potocol_sign_judge(session_pub *p_session, c_packet *p_packet) // 组包
{
    if (p_session == NULL || p_packet == NULL) {
        return false;
    }
    proto_parse_session *p_pp_session = (proto_parse_session *)(p_session->p_sp_session);
    xdmcp_session *p_xdmcp_session = (xdmcp_session *)p_pp_session->expansion_data;
    // 组包
    if (p_packet->app_data_len == 0) {
        return false;
    } else {
        p_xdmcp_session->p_data = p_packet->p_app_data;
        p_xdmcp_session->data_len = p_packet->app_data_len;
        if ((0 == p_packet->Directory && PACKETFROMCLIENT == p_session->session_basic.Server) || (1 == p_packet->Directory && PACKETFROMSERVER == p_session->session_basic.Server)) {
            p_xdmcp_session->pktdirec = PKTFROMCLIENT;
        } else if ((0 == p_packet->Directory && PACKETFROMSERVER == p_session->session_basic.Server) || (1 == p_packet->Directory && PACKETFROMCLIENT == p_session->session_basic.Server)) {
            p_xdmcp_session->pktdirec = PKTFROMSERVER;
        }
        return true;
    }
}

bool xdmcp_plugin::potocol_parse_handle(session_pub *p_session, c_packet *p_packet) // 解析
{
    uint16_t opcode;
    uint16_t version;
    if (p_session == NULL || p_packet == NULL) {
        return false;
    }
    proto_parse_session *p_pp_session = (proto_parse_session *)(p_session->p_sp_session);
    xdmcp_session *p_xdmcp_session = (xdmcp_session *)p_pp_session->expansion_data;
    // 如果解析数据之后发现可以发送了，就返回true

    version = ntohs(*(uint16_t *)p_xdmcp_session->p_data);

    if (version != 1) {
        return false;
    }

    opcode = ntohs(*(uint16_t *)(p_xdmcp_session->p_data + 2));

    switch (opcode) {
    case XDMCP_BROADCAST_QUERY:
    case XDMCP_QUERY:
    case XDMCP_INDIRECT_QUERY:
        parse_query_data(p_xdmcp_session);
        break;
    case XDMCP_FORWARD_QUERY:
        parse_forward_query_data(p_xdmcp_session);
        break;
    case XDMCP_WILLING:
        parse_willing_data(p_xdmcp_session);
        break;
    case XDMCP_UNWILLING:
        parse_unwilling_data(p_xdmcp_session);
        return 1;
    case XDMCP_REQUEST:
        parse_request_data(p_xdmcp_session);
        break;
    case XDMCP_ACCEPT:
        parse_accept_data(p_xdmcp_session);
        break;
    case XDMCP_DECLINE:
        parse_decline_data(p_xdmcp_session);
        return 1;
    case XDMCP_MANAGE:
        parse_manage_data(p_xdmcp_session);
        return 1;
    case XDMCP_REFUSE:
        parse_refuse_data(p_xdmcp_session);
        return 1;
    case XDMCP_FAILED:
        parse_failed_data(p_xdmcp_session);
        return 1;
    case XDMCP_KEEPALIVE:
        parse_keepalive_data(p_xdmcp_session);
        break;
    case XDMCP_ALIVE:
        parse_alive_data(p_xdmcp_session);
        break;

    default:
        break;
    }
}

void xdmcp_plugin::xdmcp_msg_send(session_pub *p_session) { // 发送
    if (p_session == NULL) {
        return;
    }
    proto_parse_session *p_pp_session = (proto_parse_session *)(p_session->p_sp_session);
    xdmcp_session *p_xdmcp_session = (xdmcp_session *)p_pp_session->expansion_data;

    if (p_xdmcp_session->msg_send_done) { // only send once
        return;
    }

    if (should_log) {
        JKNmsg *p_msg = p_session->p_value->get();
        if (p_msg == NULL) {
            return;
        }
        p_msg->set_type(105); // TODO:应当遵循什么原则指定type的值？
        xdmcp_msg *p_xdmcp = p_msg->mutable_xdmcp();
        Comm_msg *p_comm = p_xdmcp->mutable_comm_msg();
        xdmcp_connection_msg *p_connections;
        xdmcp_auth_msg *p_client_auth = p_xdmcp->mutable_client_auth();
        xdmcp_auth_msg *p_server_auth = p_xdmcp->mutable_server_auth();
        // 公共信息部分
        commsg_fill(p_session, p_comm, "10189", p_th_tools);

        ////xdmcp msg
        p_xdmcp->set_version(1);
        if (p_xdmcp_session->msg_parse_done[XDMCP_CONN_WILLING]) {
            p_xdmcp->set_hostname(p_xdmcp_session->willing_msg->hostname);
            p_xdmcp->set_status(p_xdmcp_session->willing_msg->status);
        }
        if (p_xdmcp_session->msg_parse_done[XDMCP_CONN_UNWILLING]) {
            p_xdmcp->set_hostname(p_xdmcp_session->unwilling_msg->hostname);
            p_xdmcp->set_status(p_xdmcp_session->unwilling_msg->status);
        }
        if (p_xdmcp_session->msg_parse_done[XDMCP_CONN_REQUEST]) {
            p_xdmcp->set_display_number(p_xdmcp_session->request_msg->display_number);
            map<string, string> tmp = p_xdmcp_session->request_msg->addr_types;
            for (auto it = tmp.begin(); it != tmp.end(); ++it) {
                p_connections = p_xdmcp->add_connections();
                p_connections->set_addr(it->first);
                p_connections->set_type(it->second);
            }
            p_client_auth->add_authentication_names(p_xdmcp_session->request_msg->authen_name);
            p_client_auth->set_authentication_data(p_xdmcp_session->request_msg->authen_data);
            for (size_t i = 0; i < p_xdmcp_session->request_msg->author_names.size(); i++) {
                p_client_auth->add_authorization_names(p_xdmcp_session->request_msg->author_names[i]);
            }
            p_xdmcp->set_manufacture_disp_id(p_xdmcp_session->request_msg->manufacture_disp_id);
        }
        if (p_xdmcp_session->msg_parse_done[XDMCP_CONN_ACCEPT]) {
            p_xdmcp->set_session_id(p_xdmcp_session->accept_msg->session_id);
            p_server_auth->add_authentication_names(p_xdmcp_session->accept_msg->authen_name);
            p_server_auth->set_authentication_data(p_xdmcp_session->accept_msg->authen_data);
            p_server_auth->add_authorization_names(p_xdmcp_session->accept_msg->author_name);
            p_server_auth->set_authorization_data(p_xdmcp_session->accept_msg->author_data);
        }
        if (p_xdmcp_session->msg_parse_done[XDMCP_CONN_MANAGED]) {
            p_xdmcp->set_display_class(p_xdmcp_session->manage_msg->display_class);
        }
    }

    p_xdmcp_session->msg_send_done = true;

    return;
}

void xdmcp_plugin::potocol_data_handle(session_pub *p_session, c_packet *p_packet) // 发送
{
    xdmcp_msg_send(p_session);
}

bool xdmcp_plugin::time_out(session_pub *p_session, uint32_t check_time) {
    if (p_session) {
        return true;
    }
    return true;
}

void xdmcp_plugin::resources_recovery(session_pub *p_session) {
    proto_parse_session *p_pp_session = (proto_parse_session *)(p_session->p_sp_session);
    xdmcp_session *p_xdmcp_session = (xdmcp_session *)p_pp_session->expansion_data;

    reset_xdmcp_session(p_xdmcp_session);
    return;
}

/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-09-04 10:40:12
 * @LastEditors: x<PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-09-04 13:53:37
 * @Description:
 */

#ifndef TELNET_STR_H
#define TELNET_STR_H

#include <list>
#include <packet.h>
#include <session_pub.h>
#include <stdint.h>
#include <string>

#define CMD_EOF 236   // 文件结束符
#define CMD_SUSP 237  // 挂起当前进程（作业控制）
#define CMD_ABORT 238 // 异常中止进程
#define CMD_EOR 239   // 记录结束符
#define CMD_SE 240    // 子选项结束
#define CMD_NOP 241   // 无操作
#define CMD_DM 242    // 数据标记
#define CMD_BRK 243   // 中断
#define CMD_IP 244    // 中断进程
#define CMD_AO 245    // 异常中止输出
#define CMD_AYT 246   // 对方是否还在运行？
#define CMD_EC 247    // 转义字符
#define CMD_EL 248    // 删除行
#define CMD_GA 249    // 继续进行
#define CMD_SB 250    // 子选项开始
#define CMD_WILL 251  // 选项协商
#define CMD_WONT 252  // 选项协商
#define CMD_DO 253    // 选项协商
#define CMD_DONT 254  // 选项协商
#define CMD_IAC 255   // 数据字节255 (该字节后面一个字节作为命令字节解释)

#define CMD_ECHO 1
#define CMD_SUPRESS_GO_AHEAD 3
#define CMD_STATUS 5
#define CMD_CLOCK_ID 6
#define CMD_TERMTYPE 0x18
#define CMD_WINSZ 0x1f
#define CMD_TERMSPEED 0x20
#define CMD_REMOTE_FLOW_CTRL 33
#define CMD_LINEMODE 34
#define CMD_XDISP_LOCATOIN 35
#define CMD_NEW_ENV_VAR 0x27

#define CMD_OPT_LEN 2

using namespace std;

enum telnet_data_type {
    OPT_NEGOTIATION = 0,
    USERNAME,
    PASSWORD,
};

enum telnet_op_mode { // telnet 服务端和客户端之间的操作方式
    CHAR = 0,         // 一次一字符
    KLUDGE,           // 准行方式
    LINEMODE,         // 实行方式
};

enum pkt_direction {
    PKTFROMCLIENT = 1,
    PKTFROMESERVER,
};

enum msg_parsed_type { TERM_SPEED_MSG, TERM_TYPE_MSG, TERM_WINSZ_MSG, USERNAME_MSG, PASSWD_MSG };

class term_info {
  public:
    term_info() {
        term_type = "";
        term_send_speed = "";
        term_recv_speed = "";
        win_width = 0;
        win_height = 0;
    }
    string term_type;
    string term_send_speed;
    string term_recv_speed;
    uint16_t win_width;
    uint16_t win_height;
};

class user_info {
  public:
    user_info() {
        username = "";
        passwd = "";
    }
    string username;
    string passwd;
};

class telnet_session {
  public:
    uint32_t thread_id;
    uint8_t *p_data;
    uint32_t data_len;
    term_info *terminfo;
    user_info *userinfo;
    enum pkt_direction pktdirec;
    bool msg_parse_done[6];
    bool msg_send_done;
};
#endif /*TELNET_STR_H*/

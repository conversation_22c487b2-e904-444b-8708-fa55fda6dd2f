/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-09-04 11:22:18
 * @LastEditors: x<PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-09-04 13:57:33
 * @Description:
 */

#include "telnet_plugin.h"

#include <commit_tools.h>
#include <sys/stat.h>
#include <sys/time.h>

static set<uint8_t> telnet_nvt_cmd;
static set<uint8_t> telnet_neg_req;
static map<uint8_t, uint8_t> client_cmd_neg;
static map<uint8_t, uint8_t> server_cmd_neg;
static map<uint8_t, bool> neg_result;
static enum telnet_op_mode op_mode = CHAR;

extern "C" {
int get_plugin_id() { return 10066; }
session_pasre_base *attach() { return new telnet_plugin(); }
}

telnet_plugin::telnet_plugin() { reload(); }

telnet_plugin::~telnet_plugin() { ; }

void telnet_plugin::get_negotiaton_result() {
    for (auto it = client_cmd_neg.begin(); it != client_cmd_neg.end(); ++it) {
        if ((it->second == CMD_WILL && server_cmd_neg[it->first] == CMD_DO) || (it->second == CMD_DO && server_cmd_neg[it->first] == CMD_WILL)) {
            neg_result[it->first] = true;
        } else {
            neg_result[it->first] = false;
        }
    }
}

void telnet_plugin::parse_new_env_var(telnet_session *p_telnet_session, uint8_t *opt_data, uint8_t data_len) {
    string var_str = "";
    int var_len;
    int tmp_index;

    if (p_telnet_session == NULL || opt_data == NULL || data_len <= 0) {
        return;
    }

    tmp_index = bin_find((const uint8_t *)opt_data, data_len, 0x01);
    if (tmp_index != -1 && tmp_index >= 2) {
        var_len = tmp_index - 2;
    }

    if (var_len == 4) {
        var_str = bin2ascii((const char *)opt_data + 2, var_len);
        if (var_str == "USER") {
            string username = bin2ascii((const char *)opt_data + tmp_index + 1, data_len - tmp_index - 1);
            if (!username.empty()) {
                p_telnet_session->userinfo->username = bin2ascii((const char *)opt_data + tmp_index + 1, data_len - tmp_index - 1);
                p_telnet_session->msg_parse_done[USERNAME_MSG] = true;
            }
        }
    }
}

void telnet_plugin::parse_term_speed(telnet_session *p_telnet_session, uint8_t *opt_data, uint8_t data_len) {
    string send_speed = "";
    string recv_speed = "";
    int tmp_index;

    if (p_telnet_session == NULL || opt_data == NULL || data_len <= 0) {
        return;
    }

    tmp_index = bin_find((const uint8_t *)opt_data, data_len, 0x2c); // send_speed and recv_speed is separeted by ','

    if (tmp_index < 0) {
        return;
    }

    send_speed = bin2ascii((const char *)opt_data + 1, tmp_index - 1);
    recv_speed = bin2ascii((const char *)opt_data + tmp_index + 1, data_len - tmp_index - 1);

    if (send_speed.empty() || recv_speed.empty()) {
        return;
    }

    p_telnet_session->terminfo->term_send_speed = send_speed;
    p_telnet_session->terminfo->term_recv_speed = recv_speed;

    p_telnet_session->msg_parse_done[TERM_SPEED_MSG] = true;
}

void telnet_plugin::parse_term_type(telnet_session *p_telnet_session, uint8_t *opt_data, uint8_t data_len) {
    string term_type = "";

    if (p_telnet_session == NULL || opt_data == NULL || data_len <= 0) {
        return;
    }

    term_type = bin2ascii((const char *)opt_data + 1, data_len - 1);

    if (term_type.empty()) {
        return;
    }
    p_telnet_session->terminfo->term_type = term_type;

    p_telnet_session->msg_parse_done[TERM_TYPE_MSG] = true;
}

void telnet_plugin::parse_win_size(telnet_session *p_telnet_session, uint8_t *opt_data, uint8_t data_len) {
    uint16_t width;
    uint16_t height;

    if (p_telnet_session == NULL || opt_data == NULL || data_len <= 0) {
        return;
    }

    width = ntohs(*(uint16_t *)opt_data);
    height = ntohs(*((uint16_t *)opt_data + 1));

    p_telnet_session->terminfo->win_width = width;
    p_telnet_session->terminfo->win_height = height;

    p_telnet_session->msg_parse_done[TERM_WINSZ_MSG] = true;
}

void telnet_plugin::parse_subopt_data(telnet_session *p_telnet_session, uint8_t subcmd, uint8_t *opt_data, uint8_t data_len) {
    if (p_telnet_session == NULL || opt_data == NULL || data_len <= 0) {
        return;
    }

    if (*opt_data == 0x01 && data_len == 1) { // request for actual data
        return;
    }

    switch (subcmd) {
    case CMD_TERMTYPE:
        parse_term_type(p_telnet_session, opt_data, data_len);
        break;
    case CMD_TERMSPEED:
        parse_term_speed(p_telnet_session, opt_data, data_len);
        break;
    case CMD_NEW_ENV_VAR:
        parse_new_env_var(p_telnet_session, opt_data, data_len);
        break;
    case CMD_WINSZ:
        parse_win_size(p_telnet_session, opt_data, data_len);
        break;

    default:
        break;
    }
}

int telnet_plugin::bin_find(const uint8_t *pdata, uint8_t len, uint8_t byte) { // 返回二进制数据中特定字节首次出现的位置

    if (pdata == NULL || len <= 0) {
        return -1;
    }

    for (int i = 0; i < len; i++) {
        if (pdata[i] == byte) {
            return i;
        }
    }
    return -1;
}

void telnet_plugin::reload() { ; }

string telnet_plugin::bin2hex(const char *p_data, uint32_t len) {
    string s = "";

    if (p_data == NULL || len <= 0) {
        return "";
    }

    char formate_char[3];
    for (uint32_t i = 0; i < len; ++i) {
        sprintf(formate_char, "%02x", (unsigned char)p_data[i]);
        s.append(formate_char, 2);
    }
    return s;
}

string telnet_plugin::bin2string(const char* p_data, uint32_t len) {
    // Check for null pointer
    if (p_data == nullptr) {
        return std::string();
    }
    
    // Check for valid length
    if (len == 0) {
        return std::string();
    }
    
    // Create string safely
    return std::string(p_data, len);
}

string telnet_plugin::bin2ascii(const char *p_data, uint32_t len) {
    string s = "";

    if (p_data == NULL || len <= 0) {
        return "";
    }

    char formate_char[3];
    for (uint32_t i = 0; i < len; ++i) {
        if (p_data[i] >= 32 && p_data[i] <= 126) { // printable characters
            sprintf(formate_char, "%c", (unsigned char)p_data[i]);
        } else {
            sprintf(formate_char, "%c", ' ');
        }
        s.append(formate_char, 1);
    }
    return s;
}

void telnet_plugin::init_telnet_session(telnet_session *p_telnet_session) {

    if (p_telnet_session == NULL) {
        return;
    }

    telnet_nvt_cmd.insert(CMD_ECHO);
    telnet_nvt_cmd.insert(CMD_SUPRESS_GO_AHEAD);
    telnet_nvt_cmd.insert(CMD_STATUS);
    telnet_nvt_cmd.insert(CMD_CLOCK_ID);
    telnet_nvt_cmd.insert(CMD_TERMTYPE);
    telnet_nvt_cmd.insert(CMD_WINSZ);
    telnet_nvt_cmd.insert(CMD_TERMSPEED);
    telnet_nvt_cmd.insert(CMD_REMOTE_FLOW_CTRL);
    telnet_nvt_cmd.insert(CMD_LINEMODE);
    telnet_nvt_cmd.insert(CMD_XDISP_LOCATOIN);
    telnet_nvt_cmd.insert(CMD_NEW_ENV_VAR);

    telnet_neg_req.insert(CMD_WILL);
    telnet_neg_req.insert(CMD_WONT);
    telnet_neg_req.insert(CMD_DO);
    telnet_neg_req.insert(CMD_DONT);

    memset(p_telnet_session, 0, sizeof(telnet_session));
}

void telnet_plugin::reset_telnet_session(telnet_session *p_telnet_session) {

    if (p_telnet_session == NULL) {
        return;
    }

    if (p_telnet_session->terminfo != NULL) {
        delete p_telnet_session->terminfo;
        p_telnet_session->terminfo = NULL;
    }
    if (p_telnet_session->userinfo != NULL) {
        delete p_telnet_session->userinfo;
        p_telnet_session->userinfo = NULL;
    }
}

void telnet_plugin::negotiation_data_parse(telnet_session *p_telnet_session) {
    uint8_t *p_cur = p_telnet_session->p_data;
    uint8_t *cmd;
    uint8_t *subcmd;
    int subopt_data_len = 0;
    int offset = 0;

    if (p_telnet_session == NULL) {
        return;
    }

    while (*p_cur == CMD_IAC) {
        cmd = p_cur + 1;
        subcmd = p_cur + 2;
        if (telnet_nvt_cmd.find(*subcmd) != telnet_nvt_cmd.end() && telnet_neg_req.find(*cmd) != telnet_neg_req.end()) {
            if (p_telnet_session->pktdirec == PKTFROMCLIENT) {
                client_cmd_neg[*subcmd] = *cmd;
            } else if (p_telnet_session->pktdirec == PKTFROMESERVER) {
                server_cmd_neg[*subcmd] = *cmd;
            }
        }

        if (*cmd == CMD_SB) {
            subopt_data_len = 0;
            while (*(subcmd + subopt_data_len) != CMD_IAC) {
                subopt_data_len++;
            }
            subopt_data_len--;
            parse_subopt_data(p_telnet_session, *subcmd, subcmd + 1, subopt_data_len);
            offset = offset + subopt_data_len + 3;
        } else if (*cmd == CMD_SE) {
            offset += 2;
        } else {
            offset = offset + 3;
        }
        if (offset > p_telnet_session->data_len) {
            break;
        }
        p_cur = p_telnet_session->p_data + offset;
    }
}

bool telnet_plugin::username_data_parse(telnet_session *p_telnet_session) { // 解析用户名

    if (p_telnet_session == NULL) {
        return false;
    }

    uint8_t *p_cur = p_telnet_session->p_data;
    if (p_telnet_session->pktdirec == PKTFROMESERVER) {
        if (ntohs(*(uint16_t *)p_cur) == 0x0d0a) { // server reply \r\n
            return true;
        }
    } else if (p_telnet_session->pktdirec == PKTFROMCLIENT) {
        if (ntohs(*(uint16_t *)p_cur) != 0x0d00) {
            p_telnet_session->userinfo->username.append(1, *p_cur);
        }
        return false;
    } else {
        return false;
    }
}

bool telnet_plugin::passwd_data_parse(telnet_session *p_telnet_session) { // 解析密码

    if (p_telnet_session == NULL) {
        return false;
    }

    uint8_t *p_cur = p_telnet_session->p_data;
    if (p_telnet_session->pktdirec == PKTFROMESERVER) {
        if (ntohs(*(uint16_t *)p_cur) == 0x0d0a) { // server reply \r\n
            return true;
        }
    } else if (p_telnet_session->pktdirec == PKTFROMCLIENT) {
        if (ntohs(*(uint16_t *)p_cur) != 0x0d00) {
            p_telnet_session->userinfo->passwd.append(1, *p_cur);
        }
        return false;
    } else {
        return false;
    }
}

bool telnet_plugin::potocol_init(session_pub *p_session, c_packet *p_packet) {
    if (p_session == NULL || p_packet == NULL) {
        return false;
    }

    proto_parse_session *p_pp_session = (proto_parse_session *)(p_session->p_sp_session);
    telnet_session *p_telnet_session = (telnet_session *)p_pp_session->expansion_data;
    init_telnet_session(p_telnet_session);
    p_telnet_session->thread_id = p_session->thread_id;
    p_telnet_session->terminfo = new term_info();
    p_telnet_session->userinfo = new user_info();
    return true;
}

bool telnet_plugin::potocol_sign_judge(session_pub *p_session, c_packet *p_packet) // 组包
{
    if (p_session == NULL || p_packet == NULL) {
        return false;
    }
    proto_parse_session *p_pp_session = (proto_parse_session *)(p_session->p_sp_session);
    telnet_session *p_telnet_session = (telnet_session *)p_pp_session->expansion_data;
    if (p_packet->app_data_len == 0) // 没有telnet data
    {
        return false;
    } else {
        p_telnet_session->p_data = p_packet->p_app_data;
        p_telnet_session->data_len = p_packet->app_data_len;
        if ((0 == p_packet->Directory && PACKETFROMCLIENT == p_session->session_basic.Server) || (1 == p_packet->Directory && PACKETFROMSERVER == p_session->session_basic.Server)) {
            p_telnet_session->pktdirec = PKTFROMCLIENT;
        } else if ((0 == p_packet->Directory && PACKETFROMSERVER == p_session->session_basic.Server) || (1 == p_packet->Directory && PACKETFROMCLIENT == p_session->session_basic.Server)) {
            p_telnet_session->pktdirec = PKTFROMESERVER;
        }
        return true;
    }
}

bool telnet_plugin::potocol_parse_handle(session_pub *p_session, c_packet *p_packet) // 解析
{
    static enum telnet_data_type next_telnet_data_type = OPT_NEGOTIATION;
    static bool change_flag = true;
    string app_data_str = "";

    if (p_session == NULL || p_packet == NULL) {
        return false;
    }

    proto_parse_session *p_pp_session = (proto_parse_session *)(p_session->p_sp_session);
    telnet_session *p_telnet_session = (telnet_session *)p_pp_session->expansion_data;
    // 如果解析数据之后发现可以发送了，就返回true

    if (change_flag) {
        app_data_str = bin2ascii((const char *)p_telnet_session->p_data, p_telnet_session->data_len);
        if (app_data_str.find("login:") != string::npos) {
            next_telnet_data_type = USERNAME;
            if (op_mode == CHAR) { // 单字符模式下，通信双方每个包传递一个字符数据，login:数据包之后的连续几个数据包表示username
                change_flag = false;
            }
            return false;
        } else if (app_data_str.find("Password:") != string::npos) {
            next_telnet_data_type = PASSWORD;
            if (CHAR == op_mode) { // 单字符模式下，通信双方每个包传递一个字符数据，Password:数据包之后的连续几个数据包表示password
                change_flag = false;
            }
            return false;
            ;
        } else {
            next_telnet_data_type = OPT_NEGOTIATION;
        }
    }

    switch (next_telnet_data_type) {
    case USERNAME:
        if (username_data_parse(p_telnet_session)) {
            p_telnet_session->msg_parse_done[USERNAME_MSG] = true;
            change_flag = true; // 每个报文传递username的一个字符，获取到完整的username后，才能修改next_telnet_data_type
        }
        break;
    case PASSWORD:
        if (passwd_data_parse(p_telnet_session)) {
            p_telnet_session->msg_parse_done[PASSWD_MSG] = true;
            change_flag = true; // 每个报文传递password的一个字符，获取到完整的password后，才能修改next_telnet_data_type
        }
        break;
    case OPT_NEGOTIATION:
        negotiation_data_parse(p_telnet_session);
        break;

    default:
        break;
    }

    enum msg_parsed_type t;
    for (t = TERM_SPEED_MSG; t <= PASSWD_MSG; t = (msg_parsed_type)(t + 1)) {
        if (!p_telnet_session->msg_parse_done[t]) {
            return false;
        }
    }

    return true;
}

void telnet_plugin::telnet_msg_send(session_pub *p_session) // 发送
{
    if (p_session == NULL) {
        return;
    }
    proto_parse_session *p_pp_session = (proto_parse_session *)(p_session->p_sp_session);
    telnet_session *p_telnet_session = (telnet_session *)p_pp_session->expansion_data;

    if (p_telnet_session == NULL) {
        return;
    }

    if (p_telnet_session->msg_send_done) { // only send once
        return;
    }

    if (should_log) {
        JKNmsg *p_msg = p_session->p_value->get();
        if (p_msg == NULL) {
            return;
        }
        p_msg->set_type(101); // TODO:应当遵循什么原则指定type的值？
        telnet_msg *p_telnet = p_msg->mutable_telnet();
        Comm_msg *p_comm = p_telnet->mutable_comm_msg();
        telnet_terminfo_msg *p_terminfo = p_telnet->mutable_terminfo();
        telnet_userinfo_msg *p_userinfo = p_telnet->mutable_userinfo();
        // 公共信息部分
        commsg_fill(p_session, p_comm, "10066", p_th_tools);

        ////telnet msg
        p_terminfo->set_term_height(p_telnet_session->terminfo->win_height);
        p_terminfo->set_term_width(p_telnet_session->terminfo->win_width);
        p_terminfo->set_term_speed(p_telnet_session->terminfo->term_send_speed);
        p_terminfo->set_term_type(p_telnet_session->terminfo->term_type);

        p_userinfo->set_username(p_telnet_session->userinfo->username);
        p_userinfo->set_passwd(p_telnet_session->userinfo->passwd);
    }
    p_telnet_session->msg_send_done = true;

    return;
}

void telnet_plugin::potocol_data_handle(session_pub *p_session, c_packet *p_packet) // 发送
{
    if (p_session == NULL) {
        return;
    }

    telnet_msg_send(p_session);
}

bool telnet_plugin::time_out(session_pub *p_session, uint32_t check_time) {
    if (p_session) {
        return true;
    }
    return true;
}

void telnet_plugin::resources_recovery(session_pub *p_session) {
    proto_parse_session *p_pp_session = (proto_parse_session *)(p_session->p_sp_session);
    telnet_session *p_telnet_session = (telnet_session *)p_pp_session->expansion_data;
    // 如果有资源要释放
    reset_telnet_session(p_telnet_session);
    return;
}

/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-09-04 11:26:21
 * @LastEditors: x<PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-09-04 13:53:17
 * @Description:
 */

#ifndef TELNET_PLUGIN_H
#define TELNET_PLUGIN_H
#include "telnet_str.h"
#include <TH_engine_interface.h>
#include <c_ip.h>
#include <iomanip>
#include <map>
#include <proto_parse_session.h>
#include <sstream>
#include <stdio.h>
#include <xml_parse.h>

using namespace std;

extern "C" {
int get_plugin_id();
session_pasre_base *attach();
};

#include "dnumstr.h"

class telnet_plugin : public session_pasre_base {
  public:
    telnet_plugin();
    ~telnet_plugin();
    virtual void reload();
    virtual bool potocol_init(session_pub *p_sess, c_packet *p_pack);
    virtual bool potocol_sign_judge(session_pub *p_sess, c_packet *p_pack);
    virtual bool potocol_parse_handle(session_pub *p_sess, c_packet *p_packet);
    virtual void potocol_data_handle(session_pub *p_sess, c_packet *p_packet);
    virtual bool time_out(session_pub *p_sess, uint32_t check_time);
    virtual void resources_recovery(session_pub *p_sess);

  private:
    void telnet_msg_send(session_pub *p_session);
    string bin2hex(const char *p_data, uint32_t len);
    string bin2string(const char *p_data, uint32_t len);
    string bin2ascii(const char *p_data, uint32_t len);
    void init_telnet_session(telnet_session *p_telnet_session);
    void reset_telnet_session(telnet_session *p_telnet_session);
    void negotiation_data_parse(telnet_session *p_telnet_session);
    bool username_data_parse(telnet_session *p_telnet_session);
    bool passwd_data_parse(telnet_session *p_telnet_session);
    void parse_new_env_var(telnet_session *p_telnet_session, uint8_t *opt_data, uint8_t data_len);
    void parse_term_speed(telnet_session *p_telnet_session, uint8_t *opt_data, uint8_t data_len);
    void parse_term_type(telnet_session *p_telnet_session, uint8_t *opt_data, uint8_t data_len);
    void parse_win_size(telnet_session *p_telnet_session, uint8_t *opt_data, uint8_t data_len);
    void parse_subopt_data(telnet_session *p_telnet_session, uint8_t subcmd, uint8_t *opt_data, uint8_t data_len);
    int bin_find(const uint8_t *pdata, uint8_t len, uint8_t byte);
    void get_negotiaton_result();
};
#endif /*TELNET_PLUGIN_H*/

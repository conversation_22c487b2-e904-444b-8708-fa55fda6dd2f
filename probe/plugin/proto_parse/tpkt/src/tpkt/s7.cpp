#include "s7.h"
#include <math.h>
#include <commit_tools.h>
#include <sys/stat.h>
#include <sys/time.h>
#include "s7.h"

#define PROTOCOL_IP 13
#define PROTOCOL_IPV6 17
#define PROTOCOL_TCP 14
#define PROTOCOL_UDP 15

void get_packet_buf_and_len(c_packet *p_packet, s7_session *p_s7_session)
{
    STR_PROTOCOLINFOR *pStack = p_packet -> m_str_packet_moudle.Stack.pProtocol;
    for(int i=0; i<MAXSTACKNUM; i++)
    {
        if(pStack[i].Protocol == 0)
        {break;}
    
        switch(pStack[i].Protocol)
        {
            case PROTOCOL_IP:
            case PROTOCOL_IPV6:
            {
                p_s7_session->data_len = pStack[i].Len;
                p_s7_session->p_data = (char *)p_packet -> buf + pStack[i].Offset;
                break;
            }
            case PROTOCOL_UDP:
            case PROTOCOL_TCP:
            {
                p_s7_session->data_len = p_packet -> packet_len - pStack[i].Offset - pStack[i].Len;
                p_s7_session->p_data = (char *)p_packet -> buf + pStack[i].Offset + pStack[i].Len;
                break;
            }
        }
    }
}

extern "C" {
int get_plugin_id()
{
    //return 10640;
    return 613;
}
session_pasre_base * attach()
{
    return new s7_plugin();
}
}

s7_plugin::s7_plugin()
{
    reload();
}

s7_plugin::~s7_plugin()
{
    ;
}

void s7_plugin::reload()
{
    ;
}

bool s7_plugin::potocol_init(session_pub* p_session, c_packet* p_packet)
{
    if(p_session==NULL || p_packet==NULL)
    {
        return false;
    }
    proto_parse_session *p_pp_session = (proto_parse_session*)(p_session->p_sp_session);
    s7_session * p_s7_session = (s7_session *)p_pp_session->expansion_data;
    init_s7_session(p_s7_session);
    p_s7_session->thread_id = p_packet->thread_id;
    return true;
}

bool s7_plugin::potocol_sign_judge(session_pub * p_session,c_packet * p_packet) //组包
{
    return true;
}

void s7_plugin::init_s7_session(s7_session* p_s7_session)
{
    p_s7_session->p_data = NULL;
    p_s7_session->data_len = 0;
    p_s7_session->p_s7_message = new s7_message();
    p_s7_session->b_parse_over = false;
    p_s7_session->p_cur_packet = NULL;
    p_s7_session->b_send_over = false;
}

bool s7_plugin::s7comm_data_parse(char* p_data, uint32_t data_len, s7_session * p_s7_session)
{
    char* p_cur = p_data;
    uint32_t offset = 0;
    s7_message* p = p_s7_session->p_s7_message;
    uint8_t magic = *(uint8_t *)(p_cur+offset);
    if (magic != 50)
    {return false;}
    offset += 1;
    uint8_t S7_type = *(uint8_t *)(p_cur+offset);
    offset += 5;
    uint16_t S7para_len = ntohs(*(uint16_t *)(p_cur+offset));
    offset += 2;
    uint16_t S7data_len = ntohs(*(uint16_t *)(p_cur+offset));
    offset += 2;
    p->s7_type = S7_type;
    p->s7_function = 0;
    p->system_type = 0;
    p->system_group_function = 0;
    p->system_sub_function = 0;
    if(S7_type==1){
        uint8_t S7_func = *(uint8_t *)(p_cur+offset);
        p->s7_function = S7_func;
    }
    else if(S7_type==3){
        offset += 2;
        uint8_t S7_func = *(uint8_t *)(p_cur+offset);
        p->s7_function = S7_func;
    }
    else if(S7_type==7){
        offset += 5;
        uint8_t system_type = *(uint8_t *)(p_cur+offset)>>4;
        uint8_t system_group_func = *(uint8_t *)(p_cur+offset)<<4;
        offset += 1;
        uint8_t system_sub_func = *(uint8_t *)(p_cur+offset);
        p->system_type = system_type;
        p->system_group_function = system_group_func;
        p->system_sub_function = system_sub_func;

    }

    return true;
}

bool s7_plugin::cotp_data_parse(char* p_data, uint32_t data_len, s7_session * p_s7_session)
{
    char* p_cur = p_data;
    uint32_t offset = 0;
    s7_message* p = p_s7_session->p_s7_message;
    uint8_t COTP_len = *(uint8_t *)(p_cur+offset);
    offset += 1;
    uint8_t COTP_type = *(uint8_t *)(p_cur+offset);
    p->cotp_type = COTP_type;
    offset += 1;
    //0xd0 connect response 0xe0 connect request
    p->dst_ref = 0;
    p->src_ref = 0;
    p->pdu_size = 0;
    p->src_connect_type = 0;
    p->src_rack = 0;
    p->src_slot = 0;
    p->dst_connect_type = 0;
    p->dst_rack = 0;
    p->dst_slot = 0;
    if ((COTP_type == 208)||(COTP_type == 224))
    {
        uint16_t dst_ref = ntohs(*(uint16_t *)(p_cur+offset));
        offset += 2;
        uint16_t src_ref = ntohs(*(uint16_t *)(p_cur+offset));
        offset += 2;
        p->dst_ref = dst_ref;
        p->src_ref = src_ref;
        offset += 1;
        while(offset < COTP_len+1)
        {
            uint8_t para_code = *(uint8_t *)(p_cur+offset);
            offset += 1;
            uint8_t para_len = *(uint8_t *)(p_cur+offset);
            offset += 1;
            if((para_code==192)&&(para_len==1))
            {
                uint8_t tmp = *(uint8_t *)(p_cur+offset);
                uint32_t pdu_size = pow(2,tmp);
                offset += 1;
                p->pdu_size = pdu_size;
            }
            else if ((para_code==193)&&(para_len==2))
            {
                uint8_t c1_connect_type = *(uint8_t *)(p_cur+offset);
                offset += 1;
                uint8_t location = *(uint8_t *)(p_cur+offset);
                offset += 1;
                uint8_t src_rack = (location >> 4) & 0x0f;
                uint8_t src_slot = location & 0x0f;
                p->src_connect_type = c1_connect_type;
                p->src_rack = src_rack;
                p->src_slot = src_slot;
            }
            else if ((para_code==194)&&(para_len==2))
            {
                uint8_t c2_connect_type = *(uint8_t *)(p_cur+offset);
                offset += 1;
                uint8_t location = *(uint8_t *)(p_cur+offset);
                offset += 1;
                uint8_t dst_rack = (location >> 4) & 0x0f;
                uint8_t dst_slot = location & 0x0f;
                p->dst_connect_type = c2_connect_type;
                p->dst_rack = dst_rack;
                p->dst_slot = dst_slot;
            }
        }
        return true;
    }
    //0xd0 data
    else if (COTP_type == 240)
    {
        offset += 1;
        if ((data_len-COTP_len-1) <= 8)
        {return false;}
        bool ret = s7comm_data_parse(p_cur+offset, data_len-COTP_len-1, p_s7_session);
        if (ret)
        {return true;}
    }
    return false;
}


bool s7_plugin::tpkt_data_parse(char* p_data, uint32_t data_len, s7_session * p_s7_session)
{
    if (p_data == NULL || data_len == 0)
    {
        return false;
    }
    char* p_cur = p_data;
    uint32_t offset = 0;
    s7_message* p = p_s7_session->p_s7_message;
    //TPKT Header
    uint8_t version = *(uint8_t *)(p_cur+offset);
    offset += 2;
    uint16_t length = ntohs(*(uint16_t *)(p_cur+offset));
    offset += 2;
    p->tpkt_version = version;
    p->tpkt_length = length;
    
    bool ret = cotp_data_parse(p_cur+offset, length-4, p_s7_session);
    if (ret)
    {return true;}

    return false;
}

bool s7_plugin::potocol_parse_handle(session_pub * p_session,c_packet *p_packet) //解析
{
    if (p_session == NULL || p_packet == NULL) {
        return false;
    }
    proto_parse_session* p_pp_session = (proto_parse_session * )( p_session -> p_sp_session);
    s7_session * p_s7_session = (s7_session *)p_pp_session->expansion_data;
    get_packet_buf_and_len(p_packet, p_s7_session);
    //p_s7_session->p_data = (char *)p_packet->app_buf-4;
    //p_s7_session->data_len = p_packet->app_len;
    //p_s7_session->data_len = ntohs(*(uint16_t *)(p_s7_session->p_data + 2));
    if(p_packet -> Directory)
    {
        p_s7_session -> u_c2s =  0;
    }
    else
    {
        p_s7_session -> u_c2s =  1;
    }
    //如果解析数据之后发现可以发送了，就返回true
    bool ret = tpkt_data_parse(p_s7_session->p_data, p_s7_session->data_len, p_s7_session);
    //如果可以发送了，直接返回true
    if (ret)
    {
        return true;
    }
    //如果是FIN包也发送，fix me
    if (false)
    {
        return true;
    }
    return false;
}

void s7_plugin::s7_msg_send(session_pub* p_session)  //发送
{
    proto_parse_session* p_pp_session = (proto_parse_session * )( p_session -> p_sp_session);
    s7_session * p_s7_session = (s7_session *)p_pp_session->expansion_data;
    s7_message* p = p_s7_session->p_s7_message;

    //if (p_s7_session->b_send_over)
    //{
    //    return;
    //}

    if(should_log)
    {
        JKNmsg * p_msg = p_session->p_value->get();
        if (p_msg == NULL)
        {
            p_s7_session->b_send_over = true;
            return;
        }
        p_msg->set_type(41);
        s7_msg* p_s7 = p_msg->mutable_s7();
        Comm_msg* p_comm = p_s7->mutable_comm_msg();
        //公共信息部分
        commsg_fill(p_session , p_comm, "10640", p_th_tools);

        p_s7->set_tpkt_version(p->tpkt_version);
        p_s7->set_cotp_type(p->cotp_type);
        p_s7->set_s7_type(p->s7_type);
        p_s7->set_s7_function(p->s7_function);
        p_s7->set_system_type(p->system_type);
        p_s7->set_system_group_function(p->system_group_function);
        p_s7->set_system_sub_function(p->system_sub_function);

        p_s7->set_dst_ref(p->dst_ref);
        p_s7->set_src_ref(p->src_ref);
        p_s7->set_pdu_size(p->pdu_size);
        p_s7->set_src_connect_type(p->src_connect_type);
        p_s7->set_src_rack(p->src_rack);
        p_s7->set_src_slot(p->src_slot);
        p_s7->set_dst_connect_type(p->dst_connect_type);
        p_s7->set_dst_rack(p->dst_rack);
        p_s7->set_dst_slot(p->dst_slot);
        p_s7->set_packet_c2s(p_s7_session->u_c2s);
        p_s7_session->b_send_over = true;
    }
    return;
}

void s7_plugin::potocol_data_handle(session_pub* p_session,c_packet * p_packet)  //发送
{
    s7_msg_send(p_session);
}

bool s7_plugin::time_out(session_pub * p_session, uint32_t check_time)
{
    if (p_session)
    {
        return true;
    }
    return true;
}

void s7_plugin::resources_recovery(session_pub * p_session)
{
    proto_parse_session *p_pp_session = (proto_parse_session*)(p_session->p_sp_session);
    s7_session * p_s7_session = (s7_session *)p_pp_session->expansion_data;
    if (p_s7_session->p_s7_message != NULL)
    {
        delete p_s7_session->p_s7_message;
        p_s7_session->p_s7_message = NULL;
    }
    return;
}





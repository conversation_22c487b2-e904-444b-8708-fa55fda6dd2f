//
// Created by root on 4/21/19.
//

#ifndef S7_S7_STR_H
#define S7_S7_STR_H

class s7_message {
public:
    s7_message()
    {
        tpkt_version = 0;
        tpkt_length = 0;
        cotp_type = 0;
        s7_type = 0;
        s7_function = 0;
        system_type = 0;
        system_group_function = 0;
        system_sub_function = 0;
        dst_ref = 0;
        src_ref = 0;
        pdu_size = 0;
        src_connect_type = 0;
        src_rack = 0;
        src_slot = 0;
        dst_connect_type = 0; 
        dst_rack = 0;
        dst_slot = 0;
    }
public:
    uint8_t  tpkt_version;
    uint16_t tpkt_length;
    uint8_t  cotp_type;
    uint8_t  s7_type;
    uint8_t  s7_function;
    uint8_t  system_type;
    uint8_t  system_group_function;
    uint8_t  system_sub_function;
    uint16_t dst_ref;
    uint16_t src_ref;
    uint32_t pdu_size;
    uint8_t  src_connect_type;
    uint8_t  src_rack;
    uint8_t  src_slot;
    uint8_t  dst_connect_type;
    uint8_t  dst_rack;
    uint8_t  dst_slot;
};

class s7_session
{
public:
    char* p_data;
    uint32_t data_len;
    s7_message* p_s7_message;
    bool b_parse_over;
    bool b_send_over;
    c_packet* p_cur_packet;
    uint32_t thread_id;
    uint32_t u_c2s ;
};

#endif //S7_S7_STR_H

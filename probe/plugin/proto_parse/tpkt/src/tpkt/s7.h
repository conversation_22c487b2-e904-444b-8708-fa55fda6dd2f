#ifndef S7_LIBRARY_H
#define S7_LIBRARY_H

#include <stdio.h>
#include <iomanip>
#include <sstream>
#include <xml_parse.h>
#include <c_ip.h>
#include <map>
#include <TH_engine_interface.h>
#include <proto_parse_session.h>
#include "s7_str.h"

using namespace std;

extern "C" {
int get_plugin_id();
session_pasre_base * attach();
};
class s7_plugin : public session_pasre_base{
public:
    //standard interface
    s7_plugin();
    ~s7_plugin();
    virtual void reload();
    virtual bool potocol_init(session_pub* p_sess, c_packet* p_pack);
    virtual bool potocol_sign_judge(session_pub* p_sess, c_packet* p_pack);
    virtual bool potocol_parse_handle(session_pub* p_sess,c_packet * p_packet);
    virtual void potocol_data_handle(session_pub* p_sess,c_packet * p_packet);
    virtual bool time_out(session_pub* p_sess,uint32_t check_time);
    virtual void resources_recovery(session_pub* p_sess);
private:
    //self-defined function
    void init_s7_session(s7_session * p_s7_session);
    bool tpkt_data_parse(char* p_data, uint32_t data_len, s7_session* p_s7_session);
    bool cotp_data_parse(char* p_data, uint32_t data_len, s7_session* p_s7_session);
    bool s7comm_data_parse(char* p_data, uint32_t data_len, s7_session* p_s7_session);
    void s7_msg_send(session_pub* p_session);
};

#endif
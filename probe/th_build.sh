#!/bin/bash

# 定义颜色变量
RED='\033[31m'
GREEN='\033[32m'
YELLOW='\033[33m'
BLUE='\033[34m'
MAGENTA='\033[35m'
CYAN='\033[36m'
WHITE='\033[37m'
RESET='\033[0m' # 用于重置颜色到默认值

# 定义打印彩色文本的函数
color_echo() {
    local text="$1" # 要打印的文本   
    if [ -z $2 ]; then
        local color="${CYAN}"
    else  
        local color="$2" # 颜色变量
    fi 
    local curdatetime=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${MAGENTA}${curdatetime}   ${color}${text}${RESET}" # 打印彩色文本并重置颜色
}

do_log(){
    "$@" >> ${THE_ROOT}/build/build.log 2>&1
}

check_dir(){
    if [ $PWD == ${THE_ROOT} ]; then
        color_echo "check dir failed" ${RED}
        exit 1
    fi
}

function t_cmd()
 {
     if [ $1 -eq 0 ];
     then 
         do_log echo "        完成" 
     else 
         do_log echo "        出错" 
         exit 1
     fi
 }





#使用 ./build.sh -j 带上数字参数，可加快编译速度，
#根据编译环境条件,使用-j4 或-j8
MAKE_THREAD_NUM=8

#使用 ./build.sh 默认使用git版本号，可使用 -v 手动指定版本号
THE_VER=""

PRODUCT="product_forensics"

#使用 ./build.sh -d 编译debug版本程序，debug版本程序硬件需求低，可运行于个人电脑或虚拟机
DEBUG_MODE="false"
BUILD_MODE="true"

#检查环境变量是否指向当前分支的代码库
if [ "$(realpath $0)" != "$(realpath ${THE_ROOT}/th_build.sh)" ]
then
    echo "Please check your THE_ROOT environmental variable!"
    exit 1
fi

if [ "$(realpath ${THE_ROOT}/sdk)" != "$(realpath ${THE_SDK})" ]
then
    echo "Please check your THE_SDK environmental variable!"
    exit 1
fi

BUILD_DIR=${THE_ROOT}/build


if [ "x$THE_VER" == "x" ]
then
    #THE_VER="$(git branch | grep '*' | awk '{print $NF}' | tr '/' '-' | tr -d ')' | tr -d '）' | tr -d ' ')_$(git rev-list HEAD |wc -l)_$(git log -n1 --pretty=format:'%H')"
    THE_VER="$(git rev-parse --abbrev-ref HEAD)_$(git rev-list HEAD |wc -l)_$(git rev-parse --short HEAD)"
fi

if [ "x$THE_VER" == "x" ]
then
    echo "ERROR:no version"
    exit 1
fi

sed -i "s/\".*\"/\"$THE_VER\"/g" "${THE_ROOT}/sdk/include/the_version.h"
(echo app_id,app_name;(for f in $(find $THE_ROOT/db/_ProRule -type f -name '*.Rule'); do echo "$(xmllint --xpath '/Application/APPID/text()' $f),$(xmllint --xpath '/Application/Name/text()' $f )"; done) |sort -n |sed 's/APP_//') | python2 $THE_ROOT/bin/tools/csv2json.py > $THE_ROOT/db/app_id_list.json
t_cmd $?

build_lib_src(){
    #1 编译lib_src
    color_echo "build_lib_src"
    rm -rf ${BUILD_DIR}/lib_src
    cp -af lib_src ${BUILD_DIR}/
    do_log pushd ${BUILD_DIR}/lib_src
    check_dir

    LIB_ARRAY=("/common_tool/build/" "/dllproto/" "/basic_parse/build/")

    for idx in $(seq 0 $((${#LIB_ARRAY[*]}-1)))
    do
        SUBLIB=${BUILD_DIR}/lib_src/${LIB_ARRAY[${idx}]}
        cd ${SUBLIB}
        t_cmd $?
        color_echo "  building $PWD"
        if [ -f ${SUBLIB}/build.sh ]
        then
            do_log bash build.sh
            t_cmd $?
        else
            do_log make clean
            do_log make -j $MAKE_THREAD_NUM
            t_cmd $?
            do_log make install
            t_cmd $?
        fi
    done

    do_log popd
    
}

build_libth_engine(){
    #2 编译libth_engine.so
    color_echo "build_libth_engine"

    rm -rf ${BUILD_DIR}/src
    cp -af src ${BUILD_DIR}/
    do_log pushd ${BUILD_DIR}/src/TH_ENGINE
    check_dir


    color_echo "  building $PWD"
    do_log make clean
    do_log make -j $MAKE_THREAD_NUM
    t_cmd $?
    do_log make install
    t_cmd $?
    do_log popd 

    BATCHID_START=100001
    do_log pushd ${THE_ROOT}/build/bin
    check_dir
    #最多2个探针任务并行
    echo '{"task_id":0,"batch_id":'$BATCHID_START'}' > conf/default/task_info.json
    for i in $(seq 0 1)
    do
        \cp -arf conf/default conf/$i
        t_cmd $?
        echo '{"task_id":'$i',"batch_id":'$(($BATCHID_START+$i))'}' > conf/$i/task_info.json
        t_cmd $?
    done
    do_log popd
    
}

build_main(){
    #3 编译主程序
    color_echo "build_main"
    rm -rf ${BUILD_DIR}/main
    cp -af main ${BUILD_DIR}/
    do_log pushd ${BUILD_DIR}/main
    check_dir

    MAIN_ARRAY=("read_pcap" "th_dpdk")
    for idx in $(seq 0 $((${#MAIN_ARRAY[*]}-1)))
    do
        SUBLIB=${BUILD_DIR}/main/${MAIN_ARRAY[${idx}]}
        cd ${SUBLIB}
        t_cmd $?
        color_echo "  building $PWD"
        if [ -f ${SUBLIB}/build.sh ]
        then
            if [ "true" == $DEBUG_MODE ]
            then
                do_log bash build.sh -d
                t_cmd $?
            else
                do_log bash build.sh
                t_cmd $?
            fi
        else
            do_log make clean
            do_log make -j $MAKE_THREAD_NUM
            t_cmd $?
            do_log make install
            t_cmd $?
        fi
    done
    do_log popd
    
}

build_plugin(){
    #4 编译插件
    #4.1 libengine_common.a
    color_echo "build_plugin"

    rm -rf ${BUILD_DIR}/src/TH_ENGINE/common
    cp -af src/TH_ENGINE/common ${BUILD_DIR}/src/TH_ENGINE/
    do_log pushd ${BUILD_DIR}/src/TH_ENGINE/common/bulid
    check_dir
    color_echo "  building $PWD"
    do_log make clean
    do_log make -j $MAKE_THREAD_NUM
    t_cmd $?
    do_log make install
    t_cmd $?
    do_log popd

    rm -rf ${BUILD_DIR}/plugin
    cp -af plugin ${BUILD_DIR}/
    do_log pushd ${BUILD_DIR}/plugin
    check_dir
    #4.2 proto_parse
    for dir in $(find ${BUILD_DIR}/plugin/proto_parse/ -mindepth 1 -maxdepth 1 -type d)
    do
        cd $dir/build/
        t_cmd $?
        color_echo "  building $PWD"
        do_log make clean
        do_log make -j $MAKE_THREAD_NUM
        t_cmd $?
        do_log make install
        t_cmd $?
    done
    do_log popd
    
}

build_tools(){
    #5 编译工具
    color_echo "build_tools"
    rm -rf ${BUILD_DIR}/tools
    cp -af tools ${BUILD_DIR}/
    do_log pushd ${BUILD_DIR}/tools/
    check_dir

    for dir in $(find ${BUILD_DIR}/tools/ -mindepth 1 -maxdepth 1 -type d)
    do
        if [ -f "$dir/build.sh" ]
        then
            cd $dir
            color_echo "  building $PWD"
            do_log bash build.sh
            t_cmd $?
        else
            cd $dir/build/
            t_cmd $?
            color_echo "  building $PWD"
            do_log make clean
            do_log make -j $MAKE_THREAD_NUM
            t_cmd $?
            do_log make install
            t_cmd $?
        fi
    done

    do_log popd
    
}

build_other(){
    #6 other
    color_echo "build_other"
    rm -rf ${BUILD_DIR}/other
    cp -af other ${BUILD_DIR}/
    do_log pushd ${BUILD_DIR}/other/
    check_dir

    for dir in $(find ${BUILD_DIR}/other/ -mindepth 1 -maxdepth 1 -type d)
    do
        if [ -f "$dir/build.sh" ]
        then
            cd $dir
            color_echo "  building $PWD"
            do_log bash build.sh
            t_cmd $?
        else
            cd $dir/build/
            t_cmd $?
            color_echo "  building $PWD"
            do_log make clean
            do_log make -j $MAKE_THREAD_NUM
            t_cmd $?
            do_log make install
            t_cmd $?
        fi
    done

    do_log popd
    
}

product_modify(){
    #7 product_modify
    color_echo "product_modify"
    cd ${THE_ROOT}/build/bin
    do_log bash product_modify.sh $PRODUCT
}

while getopts "p:j:dv:" arg #选项后面的冒号表示该选项需要参数
do
    case $arg in
        j)
            if [ "x$OPTARG" != "x" ]
            then
                $MAKE_THREAD_NUM=$OPTARG
            fi
            ;;
        d)
            DEBUG_MODE="true"
            color_echo "build debug mode"
            ;;
        v)
            if [ "x$OPTARG" != "x" ]
            then
                THE_VER=$OPTARG
            fi
            ;;
        p)
            if [ "x$OPTARG" != "x" ]
            then
                PRODUCT=$OPTARG
            fi
            ;;
        ?) #当有不认识的选项的时候arg为?
            echo "unkonw argument"
            ;;
    esac
done

color_echo "THE_ROOT: ${THE_ROOT}"
color_echo "THE_SDK:  ${THE_SDK}"
color_echo "BUILD_DIR:${BUILD_DIR}"
color_echo "pruduct:$PRODUCT, version:$THE_VER, debug:$DEBUG_MODE\n"

if [ "true" == $BUILD_MODE ];then
    if [ ! -e ${BUILD_DIR} ]; then
        mkdir -p ${BUILD_DIR}
    fi
    rm -rf ${BUILD_DIR}/bin
    cp -af bin ${BUILD_DIR}/
    build_lib_src
    build_libth_engine
    build_main
    build_plugin
    build_tools
    build_other
    product_modify 
fi

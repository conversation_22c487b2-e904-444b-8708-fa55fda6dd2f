[{"app": "MAIL_POP", "appid": "10101", "ipproto": "tcp", "port": "109"}, {"app": "MAIL_POP", "appid": "10101", "ipproto": "tcp", "port": "110"}, {"app": "NNTP", "appid": "10012", "ipproto": "tcp", "port": "119"}, {"app": "NTP", "appid": "10413", "ipproto": "tcp", "port": "123"}, {"app": "MAIL_IMAP", "appid": "10105", "ipproto": "tcp", "port": "143"}, {"app": "SNMP", "appid": "10034", "ipproto": "tcp", "port": "161"}, {"app": "SNMP", "appid": "10034", "ipproto": "tcp", "port": "162"}, {"app": "BGP", "appid": "10032", "ipproto": "tcp", "port": "179"}, {"app": "FTP", "appid": "10001", "ipproto": "tcp", "port": "20"}, {"app": "FTP", "appid": "10001", "ipproto": "tcp", "port": "21"}, {"app": "MAIL_IMAP", "appid": "10105", "ipproto": "tcp", "port": "220"}, {"app": "SSH", "appid": "10061", "ipproto": "tcp", "port": "22"}, {"app": "Rlogin", "appid": "10065", "ipproto": "tcp", "port": "513"}, {"app": "Telnet", "appid": "10066", "ipproto": "tcp", "port": "23"}, {"app": "MAIL_SMTP", "appid": "10067", "ipproto": "tcp", "port": "25"}, {"app": "HTTP", "appid": "10637", "ipproto": "tcp", "port": "280"}, {"app": "SSL", "appid": "10638", "ipproto": "tcp", "port": "443"}, {"app": "SSL", "appid": "10638", "ipproto": "tcp", "port": "465"}, {"app": "IPSec_ISAKMP", "appid": "10422", "ipproto": "tcp", "port": "500"}, {"app": "DNS", "appid": "10071", "ipproto": "tcp", "port": "53"}, {"app": "KERBEROS", "appid": "10093", "ipproto": "tcp", "port": "543"}, {"app": "KERBEROS", "appid": "10093", "ipproto": "tcp", "port": "544"}, {"app": "DHCPv6", "appid": "10037", "ipproto": "tcp", "port": "547"}, {"app": "AFP", "appid": "10391", "ipproto": "tcp", "port": "548"}, {"app": "RTSP", "appid": "10136", "ipproto": "tcp", "port": "554"}, {"app": "SSL", "appid": "10638", "ipproto": "tcp", "port": "563"}, {"app": "SSL", "appid": "10638", "ipproto": "tcp", "port": "585"}, {"app": "MAIL_SMTP", "appid": "10067", "ipproto": "tcp", "port": "587"}, {"app": "HTTP", "appid": "10637", "ipproto": "tcp", "port": "591"}, {"app": "HTTP", "appid": "10637", "ipproto": "tcp", "port": "593"}, {"app": "MMS", "appid": "10409", "ipproto": "tcp", "port": "651"}, {"app": "MMS", "appid": "10409", "ipproto": "tcp", "port": "654"}, {"app": "SSL", "appid": "10638", "ipproto": "tcp", "port": "695"}, {"app": "KERBEROS", "appid": "10093", "ipproto": "tcp", "port": "749"}, {"app": "KERBEROS", "appid": "10093", "ipproto": "tcp", "port": "751"}, {"app": "KERBEROS", "appid": "10093", "ipproto": "tcp", "port": "754"}, {"app": "KERBEROS", "appid": "10093", "ipproto": "tcp", "port": "760"}, {"app": "HTTP", "appid": "10637", "ipproto": "tcp", "port": "80"}, {"app": "SSH", "appid": "10061", "ipproto": "tcp", "port": "830"}, {"app": "SSL", "appid": "10638", "ipproto": "tcp", "port": "832"}, {"app": "SSL", "appid": "10638", "ipproto": "tcp", "port": "853"}, {"app": "RSYNC", "appid": "10397", "ipproto": "tcp", "port": "873"}, {"app": "KERBEROS", "appid": "10093", "ipproto": "tcp", "port": "88"}, {"app": "SSL", "appid": "10638", "ipproto": "tcp", "port": "898"}, {"app": "SSL", "appid": "10638", "ipproto": "tcp", "port": "989"}, {"app": "SSL", "appid": "10638", "ipproto": "tcp", "port": "990"}, {"app": "SSL", "appid": "10638", "ipproto": "tcp", "port": "992"}, {"app": "SSL", "appid": "10638", "ipproto": "tcp", "port": "993"}, {"app": "SSL", "appid": "10638", "ipproto": "tcp", "port": "994"}, {"app": "SSL", "appid": "10638", "ipproto": "tcp", "port": "995"}, {"app": "NTP", "appid": "10413", "ipproto": "udp", "port": "123"}, {"app": "NBNS", "appid": "10046", "ipproto": "udp", "port": "137"}, {"app": "NBDGM", "appid": "10049", "ipproto": "udp", "port": "138"}, {"app": "SNMP", "appid": "10034", "ipproto": "udp", "port": "161"}, {"app": "SNMP", "appid": "10034", "ipproto": "udp", "port": "162"}, {"app": "VNC", "appid": "10184", "ipproto": "udp", "port": "5900"}, {"app": "XDMCP", "appid": "10189", "ipproto": "udp", "port": "177"}, {"app": "PTP", "appid": "10610", "ipproto": "udp", "port": "319"}, {"app": "PTP", "appid": "10610", "ipproto": "udp", "port": "320"}, {"app": "LDAP", "appid": "10113", "ipproto": "udp", "port": "389"}, {"app": "Tacacs+", "appid": "10070", "ipproto": "udp", "port": "49"}, {"app": "IPSec_ISAKMP", "appid": "10422", "ipproto": "udp", "port": "500"}, {"app": "SYSLOG", "appid": "10127", "ipproto": "udp", "port": "514"}, {"app": "SYSLOG", "appid": "10127", "ipproto": "udp", "port": "514"}, {"app": "RIP", "appid": "10026", "ipproto": "udp", "port": "520"}, {"app": "RIP", "appid": "10026", "ipproto": "udp", "port": "520"}, {"app": "DNS", "appid": "10071", "ipproto": "udp", "port": "53"}, {"app": "KERBEROS", "appid": "10093", "ipproto": "udp", "port": "543"}, {"app": "KERBEROS", "appid": "10093", "ipproto": "udp", "port": "544"}, {"app": "DHCPv6", "appid": "10037", "ipproto": "udp", "port": "546"}, {"app": "DHCPv6", "appid": "10037", "ipproto": "udp", "port": "547"}, {"app": "LDAP", "appid": "10113", "ipproto": "udp", "port": "636"}, {"app": "DHCP", "appid": "10035", "ipproto": "udp", "port": "647"}, {"app": "Bootstrap_Protocol", "appid": "10481", "ipproto": "udp", "port": "67"}, {"app": "DHCP", "appid": "10035", "ipproto": "udp", "port": "67"}, {"app": "DHCP", "appid": "10035", "ipproto": "udp", "port": "68"}, {"app": "KERBEROS", "appid": "10093", "ipproto": "udp", "port": "749"}, {"app": "KERBEROS", "appid": "10093", "ipproto": "udp", "port": "751"}, {"app": "KERBEROS", "appid": "10093", "ipproto": "udp", "port": "754"}, {"app": "KERBEROS", "appid": "10093", "ipproto": "udp", "port": "760"}, {"app": "DHCP", "appid": "10035", "ipproto": "udp", "port": "847"}, {"app": "KERBEROS", "appid": "10093", "ipproto": "udp", "port": "88"}]
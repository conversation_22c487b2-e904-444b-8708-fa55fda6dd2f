[{"type": "OS", "os": "windows", "expressions": "windows"}, {"type": "OS", "os": "macos", "expressions": "mac\\s*os"}, {"type": "OS", "os": "linux", "expressions": "linux"}, {"type": "OS", "os": "linux", "expressions": "centos"}, {"type": "OS", "os": "ios", "expressions": "[^a-zA-Z0-9]ios[^a-zA-Z0-9]"}, {"type": "OS", "os": "android", "expressions": "android"}, {"type": "Brower", "expressions": "mozilla"}, {"type": "Brower", "expressions": "chrome"}]
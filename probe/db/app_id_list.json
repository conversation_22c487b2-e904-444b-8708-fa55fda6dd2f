[{"app_id": "10000", "app_name": "UNKNOWN"}, {"app_id": "10001", "app_name": "FTP"}, {"app_id": "10012", "app_name": "NNTP"}, {"app_id": "10022", "app_name": "WakeOnLan"}, {"app_id": "10026", "app_name": "RIP"}, {"app_id": "10027", "app_name": "IGRP"}, {"app_id": "10028", "app_name": "EIGRP"}, {"app_id": "10029", "app_name": "OSPF"}, {"app_id": "10030", "app_name": "IGP"}, {"app_id": "10031", "app_name": "EGP"}, {"app_id": "10032", "app_name": "BGP"}, {"app_id": "10033", "app_name": "VRRP"}, {"app_id": "10034", "app_name": "SNMP"}, {"app_id": "10035", "app_name": "DHCP"}, {"app_id": "10037", "app_name": "DHCPv6"}, {"app_id": "10040", "app_name": "ICMP_v4"}, {"app_id": "10042", "app_name": "ICMP_v6"}, {"app_id": "10044", "app_name": "ARP"}, {"app_id": "10046", "app_name": "NBNS"}, {"app_id": "10049", "app_name": "NBDGM"}, {"app_id": "10056", "app_name": "NBSS"}, {"app_id": "10061", "app_name": "SSH"}, {"app_id": "10065", "app_name": "Rlogin"}, {"app_id": "10066", "app_name": "Telnet"}, {"app_id": "10067", "app_name": "MAIL_SMTP"}, {"app_id": "10069", "app_name": "WHOISDAS"}, {"app_id": "10070", "app_name": "Tacacs+"}, {"app_id": "10071", "app_name": "DNS"}, {"app_id": "10074", "app_name": "Il_DNS"}, {"app_id": "10093", "app_name": "KERBEROS"}, {"app_id": "10101", "app_name": "MAIL_POP"}, {"app_id": "10103", "app_name": "DCERPC"}, {"app_id": "10105", "app_name": "MAIL_IMAP"}, {"app_id": "10113", "app_name": "LDAP"}, {"app_id": "10120", "app_name": "CISCOVPN"}, {"app_id": "10123", "app_name": "SMB"}, {"app_id": "10127", "app_name": "SYSLOG"}, {"app_id": "10136", "app_name": "RTSP"}, {"app_id": "10138", "app_name": "VMWARE"}, {"app_id": "10145", "app_name": "SOCKS5"}, {"app_id": "10146", "app_name": "OPENVPN"}, {"app_id": "10148", "app_name": "TDS"}, {"app_id": "10149", "app_name": "MSSQL"}, {"app_id": "10152", "app_name": "NDPI_CITRIX"}, {"app_id": "10154", "app_name": "H323"}, {"app_id": "10158", "app_name": "MSN"}, {"app_id": "10159", "app_name": "RTMP"}, {"app_id": "10161", "app_name": "SKINNY"}, {"app_id": "10162", "app_name": "NFS"}, {"app_id": "10165", "app_name": "MYSQL"}, {"app_id": "10171", "app_name": "IAX"}, {"app_id": "10172", "app_name": "RADMIN"}, {"app_id": "10180", "app_name": "POSTGRES"}, {"app_id": "10184", "app_name": "VNC"}, {"app_id": "10188", "app_name": "TEAMVIEWER"}, {"app_id": "10189", "app_name": "XDMCP"}, {"app_id": "10201", "app_name": "ANCP"}, {"app_id": "10205", "app_name": "TOR"}, {"app_id": "10208", "app_name": "TEAMSPEAK"}, {"app_id": "10209", "app_name": "ENIP"}, {"app_id": "10213", "app_name": "SOPCAST"}, {"app_id": "10215", "app_name": "ORACLE_TNS"}, {"app_id": "10216", "app_name": "GUILDWARS"}, {"app_id": "10217", "app_name": "DOFUS"}, {"app_id": "10221", "app_name": "NDPI_EDONKEY"}, {"app_id": "10222", "app_name": "FIESTA"}, {"app_id": "10226", "app_name": "FILETOPIA"}, {"app_id": "10231", "app_name": "STEAM"}, {"app_id": "10232", "app_name": "SOULSEEK"}, {"app_id": "10233", "app_name": "FCIP"}, {"app_id": "10234", "app_name": "TVANTS"}, {"app_id": "10236", "app_name": "SOCKS4"}, {"app_id": "10237", "app_name": "PANDO"}, {"app_id": "10240", "app_name": "CORBA"}, {"app_id": "10243", "app_name": "MANOLITO"}, {"app_id": "10244", "app_name": "PVFS"}, {"app_id": "10245", "app_name": "IMESH"}, {"app_id": "10381", "app_name": "NETMAN"}, {"app_id": "10383", "app_name": "ZATTOO"}, {"app_id": "10386", "app_name": "ZMQ"}, {"app_id": "10387", "app_name": "MAPLESTORY"}, {"app_id": "10388", "app_name": "STUN"}, {"app_id": "10391", "app_name": "AFP"}, {"app_id": "10392", "app_name": "APPLEJUICE"}, {"app_id": "10393", "app_name": "NDPI_BITBORRENT"}, {"app_id": "10394", "app_name": "SPOTIFY"}, {"app_id": "10395", "app_name": "TVUPLAYER"}, {"app_id": "10396", "app_name": "OSCAR"}, {"app_id": "10397", "app_name": "RSYNC"}, {"app_id": "10399", "app_name": "PPSTREAM"}, {"app_id": "10401", "app_name": "POPO"}, {"app_id": "10402", "app_name": "SHOUTCAST"}, {"app_id": "10403", "app_name": "STEALTHNET"}, {"app_id": "10404", "app_name": "LOTUS_NOTES"}, {"app_id": "10405", "app_name": "SOCRATES"}, {"app_id": "10406", "app_name": "HTTP_ACTIVESYNC"}, {"app_id": "10409", "app_name": "MMS"}, {"app_id": "10413", "app_name": "NTP"}, {"app_id": "10419", "app_name": "NETMAN_NEGOTIATION"}, {"app_id": "10422", "app_name": "IPSec_ISAKMP"}, {"app_id": "10429", "app_name": "L2TP"}, {"app_id": "10432", "app_name": "RADIUS"}, {"app_id": "10435", "app_name": "SSDP"}, {"app_id": "10436", "app_name": "HSRP"}, {"app_id": "10438", "app_name": "NETFLOW"}, {"app_id": "10439", "app_name": "GTP"}, {"app_id": "10440", "app_name": "GTP_MANAGER"}, {"app_id": "10443", "app_name": "IPMessage"}, {"app_id": "10444", "app_name": "MEGACO"}, {"app_id": "10445", "app_name": "XBOX"}, {"app_id": "10449", "app_name": "<PERSON><PERSON>"}, {"app_id": "10454", "app_name": "MDNS"}, {"app_id": "10456", "app_name": "LLMNR"}, {"app_id": "10457", "app_name": "PCANYWHERE"}, {"app_id": "10458", "app_name": "SFLOW"}, {"app_id": "10459", "app_name": "Tencent_OICQ"}, {"app_id": "10460", "app_name": "Tencent_MayBe_OICQ"}, {"app_id": "10464", "app_name": "Tencent_QQMail"}, {"app_id": "10465", "app_name": "COLLECTD"}, {"app_id": "10466", "app_name": "QUAKE"}, {"app_id": "10473", "app_name": "NOE"}, {"app_id": "10474", "app_name": "NDPI_ARMAGETRON"}, {"app_id": "10480", "app_name": "TFTP"}, {"app_id": "10481", "app_name": "Bootstrap_Protocol"}, {"app_id": "10482", "app_name": "KONTIKI"}, {"app_id": "10486", "app_name": "VIBER"}, {"app_id": "10487", "app_name": "PPLIVE"}, {"app_id": "10492", "app_name": "HALFLIFE2_AND_MODS"}, {"app_id": "10497", "app_name": "XT800"}, {"app_id": "10503", "app_name": "SUNLOGIN"}, {"app_id": "10504", "app_name": "NDPI_BATTLEFIELD"}, {"app_id": "10508", "app_name": "SKYPE"}, {"app_id": "10509", "app_name": "HOPOPTS"}, {"app_id": "10510", "app_name": "IGMP"}, {"app_id": "10511", "app_name": "GGP"}, {"app_id": "10512", "app_name": "STREAM"}, {"app_id": "10513", "app_name": "CBT"}, {"app_id": "10514", "app_name": "BBN_RCC"}, {"app_id": "10515", "app_name": "NVPII"}, {"app_id": "10516", "app_name": "PUP"}, {"app_id": "10517", "app_name": "ARGUS"}, {"app_id": "10518", "app_name": "EMCON"}, {"app_id": "10519", "app_name": "XNET"}, {"app_id": "10520", "app_name": "CHAOS"}, {"app_id": "10521", "app_name": "MUX"}, {"app_id": "10522", "app_name": "DCNMEAS"}, {"app_id": "10523", "app_name": "HMP"}, {"app_id": "10524", "app_name": "PRM"}, {"app_id": "10525", "app_name": "IDP"}, {"app_id": "10526", "app_name": "TRUNK1"}, {"app_id": "10527", "app_name": "TRUNK2"}, {"app_id": "10528", "app_name": "LEAF1"}, {"app_id": "10529", "app_name": "LEAF2"}, {"app_id": "10530", "app_name": "IRT"}, {"app_id": "10531", "app_name": "BULK"}, {"app_id": "10532", "app_name": "MFE_NSP"}, {"app_id": "10533", "app_name": "MERIT"}, {"app_id": "10534", "app_name": "DCCP"}, {"app_id": "10535", "app_name": "3PC"}, {"app_id": "10536", "app_name": "IDPR"}, {"app_id": "10537", "app_name": "XTP"}, {"app_id": "10538", "app_name": "DDP"}, {"app_id": "10539", "app_name": "CMTP"}, {"app_id": "10540", "app_name": "TPPP"}, {"app_id": "10541", "app_name": "IL"}, {"app_id": "10542", "app_name": "SDRP"}, {"app_id": "10543", "app_name": "ROUTING"}, {"app_id": "10544", "app_name": "FRAGMENT"}, {"app_id": "10545", "app_name": "IDRP"}, {"app_id": "10546", "app_name": "RSVP"}, {"app_id": "10548", "app_name": "DSR"}, {"app_id": "10549", "app_name": "BNA"}, {"app_id": "10550", "app_name": "INSLP"}, {"app_id": "10551", "app_name": "SWIPE"}, {"app_id": "10552", "app_name": "NHRP"}, {"app_id": "10553", "app_name": "MOBILE"}, {"app_id": "10554", "app_name": "TLSP"}, {"app_id": "10555", "app_name": "SKIP"}, {"app_id": "10556", "app_name": "NONE"}, {"app_id": "10557", "app_name": "DSTOPTS"}, {"app_id": "10558", "app_name": "SHIM6_OL"}, {"app_id": "10559", "app_name": "MIP6"}, {"app_id": "10560", "app_name": "SATEXPAK"}, {"app_id": "10561", "app_name": "KRYPTOLA"}, {"app_id": "10562", "app_name": "RVD"}, {"app_id": "10563", "app_name": "IPPC"}, {"app_id": "10564", "app_name": "SATMON"}, {"app_id": "10565", "app_name": "VISA"}, {"app_id": "10566", "app_name": "IPCV"}, {"app_id": "10567", "app_name": "CPNX"}, {"app_id": "10568", "app_name": "CPHB"}, {"app_id": "10569", "app_name": "WSN"}, {"app_id": "10570", "app_name": "PVP"}, {"app_id": "10571", "app_name": "BRSATMON"}, {"app_id": "10572", "app_name": "SUNND"}, {"app_id": "10573", "app_name": "WBMON"}, {"app_id": "10574", "app_name": "WBEXPAK"}, {"app_id": "10575", "app_name": "OSI"}, {"app_id": "10576", "app_name": "VMTP"}, {"app_id": "10577", "app_name": "SVMTP"}, {"app_id": "10578", "app_name": "VINES"}, {"app_id": "10579", "app_name": "TTP"}, {"app_id": "10580", "app_name": "NSFNETIG"}, {"app_id": "10581", "app_name": "DGP"}, {"app_id": "10582", "app_name": "TCF"}, {"app_id": "10583", "app_name": "SPRITE"}, {"app_id": "10584", "app_name": "LARP"}, {"app_id": "10585", "app_name": "MTP"}, {"app_id": "10586", "app_name": "AX25"}, {"app_id": "10587", "app_name": "IPINIP"}, {"app_id": "10588", "app_name": "MICP"}, {"app_id": "10589", "app_name": "SCCCP"}, {"app_id": "10590", "app_name": "ETHERIP"}, {"app_id": "10591", "app_name": "ENCAP"}, {"app_id": "10592", "app_name": "GMTP"}, {"app_id": "10593", "app_name": "IFMP"}, {"app_id": "10594", "app_name": "PNNI"}, {"app_id": "10595", "app_name": "PIM"}, {"app_id": "10596", "app_name": "ARIS"}, {"app_id": "10597", "app_name": "SCPS"}, {"app_id": "10598", "app_name": "QNX"}, {"app_id": "10599", "app_name": "AN"}, {"app_id": "10600", "app_name": "SNP"}, {"app_id": "10601", "app_name": "COMPAQ"}, {"app_id": "10602", "app_name": "PGM"}, {"app_id": "10603", "app_name": "DDX"}, {"app_id": "10604", "app_name": "IATP"}, {"app_id": "10605", "app_name": "STP"}, {"app_id": "10606", "app_name": "SRP"}, {"app_id": "10607", "app_name": "UTI"}, {"app_id": "10608", "app_name": "SMP"}, {"app_id": "10609", "app_name": "SM"}, {"app_id": "10610", "app_name": "PTP"}, {"app_id": "10611", "app_name": "ISIS"}, {"app_id": "10612", "app_name": "FIRE"}, {"app_id": "10613", "app_name": "CRTP"}, {"app_id": "10614", "app_name": "CRUDP"}, {"app_id": "10615", "app_name": "SSCOPMCE"}, {"app_id": "10616", "app_name": "IPLT"}, {"app_id": "10617", "app_name": "SPS"}, {"app_id": "10618", "app_name": "PIPE"}, {"app_id": "10619", "app_name": "SCTP"}, {"app_id": "10620", "app_name": "FC"}, {"app_id": "10621", "app_name": "MPLS"}, {"app_id": "10622", "app_name": "MANET"}, {"app_id": "10623", "app_name": "HIP"}, {"app_id": "10624", "app_name": "SHIM6"}, {"app_id": "10625", "app_name": "WESP"}, {"app_id": "10626", "app_name": "ROHC"}, {"app_id": "10627", "app_name": "AX4000"}, {"app_id": "10628", "app_name": "NCS"}, {"app_id": "10629", "app_name": "PPTP"}, {"app_id": "10630", "app_name": "IPSEC"}, {"app_id": "10631", "app_name": "VOIP"}, {"app_id": "10632", "app_name": "HTTPS"}, {"app_id": "10633", "app_name": "NNTPS"}, {"app_id": "10634", "app_name": "SMTPS"}, {"app_id": "10635", "app_name": "IMAPS"}, {"app_id": "10636", "app_name": "POP3S"}, {"app_id": "10637", "app_name": "HTTP"}, {"app_id": "10638", "app_name": "SSL"}, {"app_id": "10639", "app_name": "RDP"}, {"app_id": "10640", "app_name": "Simatic_S7"}, {"app_id": "10641", "app_name": "COTP"}, {"app_id": "10650", "app_name": "Modbus"}, {"app_id": "10651", "app_name": "GMSSL"}, {"app_id": "10701", "app_name": "No_Payload"}, {"app_id": "10702", "app_name": "TCP_QueryOnly"}, {"app_id": "10703", "app_name": "TCP_PortClose"}, {"app_id": "10704", "app_name": "TCP_NoPayload"}, {"app_id": "10705", "app_name": "TCP_Unknow"}, {"app_id": "10706", "app_name": "TCP_Other"}, {"app_id": "10707", "app_name": "UDP_NoPayload"}, {"app_id": "10708", "app_name": "UDP_Unknown"}]

#------------------------------------------------------------------------------
# $File: pdp,v 1.11 2017/03/17 21:35:28 christos Exp $
# pdp:  file(1) magic for PDP-11 executable/object and APL workspace
#
0	lelong		0101555		PDP-11 single precision APL workspace
0	lelong		0101554		PDP-11 double precision APL workspace
#
# PDP-11 a.out
#
0	leshort		0407		PDP-11 executable
>8	leshort		>0		not stripped
>15	byte		>0		- version %d

# updated by <PERSON><PERSON> at Mar 2013
# GRR: line below too general as it catches also Windows precompiled setup information *.PNF
0	leshort		0401
# skip *.PNF with WinDirPathOffset 58h
>68	ulelong		!0x00000058	PDP-11 UNIX/RT ldp
# skip *.PNF with high byte of InfVersionDatumCount zero
#>>15	byte		!0		PDP-11 UNIX/RT ldp
0	leshort		0405		PDP-11 old overlay

0	leshort		0410		PDP-11 pure executable
>8	leshort		>0		not stripped
>15	byte		>0		- version %d

0	leshort		0411		PDP-11 separate I&D executable
>8	leshort		>0		not stripped
>15	byte		>0		- version %d

0	leshort		0437		PDP-11 kernel overlay

# These last three are derived from 2.11BSD file(1)
0	leshort		0413		PDP-11 demand-paged pure executable
>8	leshort		>0		not stripped

0	leshort		0430		PDP-11 overlaid pure executable
>8	leshort		>0		not stripped

0	leshort		0431		PDP-11 overlaid separate executable
>8	leshort		>0		not stripped

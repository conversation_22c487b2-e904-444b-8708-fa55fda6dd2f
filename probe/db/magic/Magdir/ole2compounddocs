
#------------------------------------------------------------------------------
# $File: ole2compounddocs,v 1.8 2020/03/28 23:10:30 christos Exp $
# Microsoft OLE 2 Compound Documents : file(1) magic for Microsoft Structured
# storage (https://en.wikipedia.org/wiki/Compound_File_Binary_Format)
# Additional tests for OLE 2 Compound Documents should be under this recipe.
# reference:	https://www.openoffice.org/sc/compdocfileformat.pdf

0   string  \320\317\021\340\241\261\032\341
# https://digital-preservation.github.io/droid/
# skip droid skeleton like fmt-39-signature-id-128.doc by valid version
>0x1A	ushort		!0xABAB		OLE 2 Compound Document
#>0x1C	uleshort		x			\b, endnian 0x%4.4x
# big endian not tested
>>0x1C	ubeshort		=0xfffe			\b, big-endian
>>>546	string	jbjb			: Microsoft Word Document
!:mime	application/msword
!:apple	MSWDWDBN
!:ext	doc
# Byte Order 0xFFFE means little-endian found in real world applications
#>>0x1C	uleshort		=0xfffe			\b, little-endian
>>0x1C	uleshort		=0xfffe
# From:		Joerg Jenderek
# Major Version 3 or 4
>>>0x1A	uleshort		x			\b, v%u
# Minor Version 32h=50 3Bh=59 3Eh=62
>>>0x18	uleshort		x			\b.%u
# SecID of first sector of the directory stream is often 1 but high like 3144h
>>>48	ulelong			x			\b, SecID 0x%x
# Sector Shift Exponent in short-stream container stream: 6~64 bytes
>>>32	uleshort		!6			\b, exponent of short stream %u
# total number of sectors used for the FAT
>>>44	ulelong			>1			\b, %u FAT sectors
# SecID of first sector of the short-sector allocation table (Mini FAT)
# or -2 (End Of ChainSecID) if not extant
>>>60	ulelong			!0xffFFffFE		\b, Mini FAT start sector 0x%x
# total number of sectors used for the short-sector allocation table
>>>64	ulelong			!1			\b, %u Mini FAT sector
# plural s
>>>>64	ulelong			>1			\bs
# SecID of first sector of the master sector allocation table (DIFAT)
# or -2 (End Of Chain SecID) if no additional sectors used
>>>68	ulelong			!0xffFFffFE		\b, DIFAT start sector 0x%x
# total number of sectors used for the master sector allocation table (DIFAT)
>>>72	ulelong			>0			\b, %u DIFAT sectors
# First part of the master sector allocation table (DIFAT) containing 109 SecIDs
#>>>76 	ubequad			x			\b, DIFAT=0x%16.16llx
#>>>84 	ubequad			x			\b%16.16llx...
# pointer to root entry only works with standard configuration for SecID ~< 800h
# Red-Carpet-presentation-1.0-1.sdd sg10.sdv 2000_GA_Annual_Review_Data.xls
# "ORLEN Factbook 2017.xls" XnView_metadata.doc
# "Barham, Lisa - Die Shopping-Prinzessinnen.doc" then not recognized
>>>48	ulelong			>0x800			too big for FILE_BYTES_MAX = 1 MiB
# Sector Shift Exponent 9~512 for major version 3 or C~4096 for major version 4
>>>0x1E	uleshort		0xc			\b, blocksize 4096
# jump to one block (4096 bytes per block) before root storage block
>>>>(48.l*4096)	ubyte	x
>>>>>&4095 	use		ole2-directory
#>>>0x1E	uleshort		9			\b, blocksize 512
>>>0x1E	uleshort		9
# jump to one block (512 bytes per block) before root storage block
# in 5.37 only true for offset ~< FILE_BYTES_MAX=7 MiB defined in ../../src/file.h 
>>>>(48.l*512)	ubyte		x
>>>>>&511 	use		ole2-directory
# check directory entry structure and display types by GUID
0	name			ole2-directory
# directory entry name like "Root Entry"
#>0 	lestring16	x 			\b, 1st %.10s
# type of the entry; 5~Root storage
#>66 	ubyte		x			\b, type %x
# node colour of the entry: 00H ~ Red 01H ~ Black
#>67 	ubyte		x			\b, color %x
# the DirIDs of the child nodes. Should both be -1 in the root storage entry
#>68 	bequad		!0xffffffffffffffff	\b, DirIDs %llx
# second directory entry name like VisioDocument Control000 
#>128	lestring16	x \b, 2nd %.20s
# third directory entry like WordDocument
#>256	lestring16	x \b, 3rd %.20s
# forth
#>384	lestring16	x \b, 4th %.10s
# 5th
#>512	lestring16	x \b, 5th %.10s
# 6th
#>640	lestring16	x \b, 6th %.10s
# 7th
#>768	lestring16	x \b, 7th %.10s
#	https://wikileaks.org/ciav7p1/cms/page_13762814.html
#	https://m.blog.naver.com/superman4u/40047693679
#	https://misc.daniel-marschall.de/projects/guid_analysis/guid.txt
#	http://www.windowstricks.in/online-windows-guid-converter
#>80 	ubequad		!0			\b, clsid 0x%16.16llx
#>>88 	ubequad		x			\b%16.16llx
# test for "Root Entry" inside directory by type 5 value
>66 	ubyte		5
# look for CLSID GUID 0
>>88 	ubequad		0x0
>>>80 	ubequad		0x0
# - Microstation V8 DGN files (www.bentley.com)
# URL:	https://en.wikipedia.org/wiki/MicroStation
#   Last update on 10/23/2006 by Lester Hightower
#   07/24/2019 by Joerg Jenderek
# Second directory entry name like Dgn~H Dgn~S 
>>>>128 	lestring16	Dgn~			: Microstation V8 CAD
#!:mime	application/x-ole-storage
!:mime	application/x-bentley-dgn
# http://www.q-cad.com/files/samples_cad_files/1344468165.dgn
!:ext	dgn
#
# URL:	http://fileformats.archiveteam.org/wiki/WordPerfect
# Second directory entry name PerfectOffice_
>>>>128 	lestring16	PerfectOffice_		: WordPerfect 7-X3 presentations Master, Document or Graphic
!:mime	application/vnd.wordperfect
# https://www.macdisk.com/macsigen.php "WPC2" for Wordperfect 2 *.wpd
!:apple	????WPC7
!:ext	mst/wpd/wpg
#
# URL:	http://fileformats.archiveteam.org/wiki/Microsoft_Works_Word_Processor
# Second directory entry name MatOST_
>>>>128 	lestring16	MatOST			: Microsoft Works 3.0 document
!:mime	application/vnd.ms-works
!:apple	????AWWP
!:ext	wps
#
# URL:	http://fileformats.archiveteam.org/wiki/Microsoft_Works_Spreadsheet
# 3rd directory entry name WksSSWorkBook
>>>>256 	lestring16	WksSSWorkBook		: Microsoft Works 6-9 spreadsheet
!:mime	application/vnd.ms-works
!:apple	????AWSS
!:ext	xlr
#
# URL:	http://fileformats.archiveteam.org/wiki/XLS
# what is the difference to {00020820-0000-0000-c000-000000000046} ?
# Second directory entry name Workbook
>>>>128 	lestring16	Workbook
>>>>>256 	lestring16	!WksSSWorkBook		: Microsoft Excel 97-2003 worksheet 0 clsid
!:mime	application/vnd.ms-excel
# https://www.macdisk.com/macsigen.php	XLS5 for Excel 5
!:apple	????XLS9
!:ext	xls
#
# URL:	http://fileformats.archiveteam.org/wiki/PPT
# Second directory entry name Object1 Object12 Object35
>>>>128 	lestring16	Object			: Microsoft PowerPoint 4 presentation
!:mime	application/vnd.ms-powerpoint
# https://www.macdisk.com/macsigen.php
!:apple	????PPT3
!:ext	ppt
#
# URL:	https://www.msoutlook.info/question/164
# Second directory entry name __CollDataStm
>>>>128 	lestring16	__CollDataStm		: Microsoft Outlook Send Receive Settings
#!:mime	application/vnd.ms-outlook
!:mime	application/x-ms-srs
# %APPDATA%\Microsoft\Outlook\Outlook.srs
!:ext	srs
#
# URL:	https://www.file-extensions.org/cag-file-extension
# Second directory entry name Category
>>>>128 	lestring16	Category		: Microsoft Clip Art Gallery
#!:mime	application/x-ole-storage
!:mime	application/x-ms-cag
!:apple	MScgCGdb
!:ext	cag/
#
# URL:	https://www.filesuffix.com/de/extension/rra
# 3rd directory entry name StrIndex_StringTable
>>>>256 	lestring16	StrIndex_StringTable	: Windows temporarily installer
#!:mime	application/x-ole-storage
!:mime	application/x-ms-rra
!:ext	rra
#
# URL:	https://www.forensicswiki.org/wiki/Jump_Lists
# 3rd directory entry name DestList	
>>>>256 	lestring16	DestList		: Windows jump list
#!:mime	application/x-ole-storage
!:mime	application/x-ms-jumplist
# %APPDATA%\Microsoft\Windows\Recent\AutomaticDestinations\*.automaticDestinations-ms
!:ext	automaticDestinations-ms
#
# URL:	https://en.wikipedia.org/wiki/Windows_thumbnail_cache
# Second directory entry name 256_
>>>>128 	lestring16	256_			: Windows thumbnail database 256
#!:mime	application/x-ole-storage
!:mime	application/x-ms-thumbnail
# Thumbs.db
!:ext	db
>>>>128 	lestring16	96_			: Windows thumbnail database 96
!:mime	application/x-ms-thumbnail
!:ext	db
# 3rd directory entry name Catalog_
>>>>256 	lestring16	Catalog			: Windows thumbnail database
!:mime	application/x-ms-thumbnail
!:ext	db
#
# URL:	https://support.microsoft.com/en-us/help/300887/how-to-use-system-information-msinfo32-command-line-tool-switches
# Note:	older Microsoft Systeminfo (MSInfo Configuration File of msinfo32); newer use xml based
# Second directory entry name Control000
>>>>128 	lestring16	Control000		: Microsoft old Systeminfo
#!:mime	application/x-ole-storage
!:mime	application/x-ms-info
!:ext	nfo
#
# URL:	http://fileformats.archiveteam.org/wiki/Corel_Print_House
# Second directory entry name Thumbnail
>>>>128 	lestring16	Thumbnail		: Corel PrintHouse image
#!:mime	application/x-ole-storage
!:mime	application/x-corel-cph
!:ext	cph
# 3rd directory entry name Thumbnail
>>>>256 	lestring16	Thumbnail		: Corel PrintHouse image
!:mime	application/x-corel-cph
!:ext	cph
#
# URL:	https://en.wikipedia.org/wiki/Hangul_(word_processor)
# Note:	"HWP Document File" signature found in FileHeader
# Second directory entry name FileHeader hint for Thinkfree Office document
>>>>128 	lestring16	FileHeader		: Hangul (Korean) 5.0 Word Processor File
#!:mime	application/haansofthwp
!:mime	application/x-hwp
# https://example-files.online-convert.com/document/hwp/example.hwp
!:ext	hwp
#
# URL:	https://ask.libreoffice.org/en/question/26303/creating-new-themes-for-the-gallery-not-functioning/
# Second directory entry name like dd2000 dd2001 dd2036 dd2060 dd2083
>>>>128 	lestring16	dd2			: StarOffice Gallery view
#!:mime	application/x-ole-storage
!:mime	application/x-star-sdv
!:ext	sdv
# URL:	https://en.wikipedia.org/wiki/SoftMaker_Office
# second directory entry name Current User
>>>>128 	lestring16	Current\ User		: SoftMaker
# third directory entry name SMNativeObjData
>>>>>256	lestring16	SMNativeObjData		
# 5th directory entry nane PowerPoint
>>>>>>512	lestring16	PowerPoint		PowerPoint presentation or template
!:mime	application/vnd.ms-powerpoint
!:ext	ppt/pps/pot
# 4th directory entry name PowerPoint
>>>>>384	lestring16	PowerPoint		Presentations or template
# http://extension.nirsoft.net/prv
!:mime	application/vnd.softmaker.presentations
!:ext	prd/prv
# third directory entry name like Current User
>>>>256 	lestring16	Current\ User		: SoftMaker
# 5th directory entry name PowerPoint
>>>>>512	lestring16	PowerPoint		Presentations or template
# http://extension.nirsoft.net/prd
!:mime	application/vnd.softmaker.presentations
!:ext	prd/prv
# 2nd directory entry name Pictures
>>>>>>128 	lestring16	Pictures		with pictures
#	remaining null clsid
>>>>128 	default		x			: UNKNOWN
!:mime	application/x-ole-storage
#	look for known clsid GUID
# - Visio documents
# URL:	http://fileformats.archiveteam.org/wiki/Visio
#   Last update on 10/23/2006 by Lester Hightower, 07/20/2019 by Joerg Jenderek
>>88 	ubequad		0xc000000000000046	: Microsoft
>>>80 	ubequad		0x131a020000000000	Visio 2000-2002 Document, stencil or template
!:mime	application/vnd.visio
# VSD~Drawing VSS~Stencil VST~Template 
!:ext	vsd/vss/vst
>>>80 	ubequad		0x141a020000000000	Visio 2003-2010 Document, stencil or template
!:mime	application/vnd.visio
!:ext	vsd/vss/vst
#
# URL:	http://fileformats.archiveteam.org/wiki/Windows_Installer
>>>80 	ubequad		0x84100c0000000000	Windows Installer Package
!:mime	application/x-msi
#!:mime	application/x-ms-win-installer
!:ext	msi
>>>80 	ubequad		0x86100c0000000000	Windows Installer Patch
# ??
!:mime	application/x-wine-extension-msp
#!:mime	application/x-ms-msp
!:ext	msp
#
# URL:	http://fileformats.archiveteam.org/wiki/DOC
>>>80 	ubequad		0x0009020000000000	Word 6-95 document or template
!:mime	application/msword
# for template MSWDW8TN
!:apple	MSWDWDBN
!:ext	doc/dot
>>>80 	ubequad		0x0609020000000000	Word 97-2003 document or template
!:mime	application/msword
!:apple	MSWDWDBN
# dot for template; no extension on Macintosh
!:ext	doc/dot/
#
# URL:	http://fileformats.archiveteam.org/wiki/Microsoft_Works_Word_Processor
>>>80 	ubequad		0x0213020000000000	Works 3-4 document or template
!:mime	application/vnd.ms-works
!:apple	????AWWP
# ps for template	https://filext.com/file-extension/PS	bps for backup
!:ext	wps/ps/bps
#
# URL:	http://fileformats.archiveteam.org/wiki/Microsoft_Works_Database
>>>80 	ubequad		0x0313020000000000	Works 3-4 database or template
!:mime	application/vnd.ms-works-db
# https://www.macdisk.com/macsigen.php
!:apple	????AWDB
# db for template www.file-extensions.org/db-file-extension-microsoft-works-data bdb for backup
!:ext	wdb/db/bdb
#
# URL:	https://en.wikipedia.org/wiki/Microsoft_Excel
>>>80 	ubequad		0x1008020000000000	Excel 5-95 worksheet, addin or template
!:mime	application/vnd.ms-excel
# https://www.macdisk.com/macsigen.php
!:apple	????XLS5
# worksheet/addin/template/no extension on Macintosh
!:ext	xls/xla/xlt/
#
>>>80 	ubequad		0x2008020000000000	Excel 97-2003
!:mime	application/vnd.ms-excel
# https://www.macdisk.com/macsigen.php	XLS5 for Excel 5
!:apple	????XLS9
# 3nd directory entry name
>>>>256 	lestring16	_VBA_PROJECT_CUR	addin
!:ext	xla/
# 4th directory entry name
>>>>384 	lestring16	_VBA_PROJECT_CUR	addin
!:ext	xla
#!:ext	xla/
>>>>256 	default		x			worksheet or template
!:ext	xls/xlt
#!:ext	xls/xlt/
#
# URL:	http://fileformats.archiveteam.org/wiki/OLE2
>>>80 	ubequad		0x0b0d020000000000	Outlook 97-2003 item
#>>>80 	ubequad		0x0b0d020000000000	Outlook 97-2003 Message
#!:mime	application/vnd.ms-outlook
!:mime	application/x-ms-msg
!:ext	msg
# URL:	https://wiki.fileformat.com/email/oft/
>>>80 	ubequad		0x46f0060000000000	Outlook 97-2003 item template
#!:mime	application/vnd.ms-outlook
!:mime	application/x-ms-oft
!:ext	oft
#
# URL:	http://fileformats.archiveteam.org/wiki/PPT
>>>80 	ubequad		0x5148040000000000	PowerPoint 4.0 presentation
!:mime	application/vnd.ms-powerpoint
# https://www.macdisk.com/macsigen.php
!:apple	????PPT3
!:ext	ppt
#??
# URL:	http://www.checkfilename.com/view-details/Microsoft-Works/RespageIndex/0/sTab/2/
>>88 	ubequad		0xa29a00aa004a1a72	: Microsoft
# URL:	http://fileformats.archiveteam.org/wiki/Microsoft_Works_Word_Processor
>>>80 	ubequad		0xc2dbcd28e20ace11	Works 4 document
!:mime	application/vnd.ms-works
!:apple	????AWWP
!:ext	wps
#
# URL:	http://fileformats.archiveteam.org/wiki/Microsoft_Works_Database
>>>80 	ubequad		0xc3dbcd28e20ace11	Works 4 database
!:mime	application/vnd.ms-works-db
!:apple	????AWDB
!:ext	wdb/bdb
#??
>>88 	ubequad		0xa40700c04fb932ba	: Microsoft
# URL:	http://fileformats.archiveteam.org/wiki/Microsoft_Works_Word_Processor
>>>80 	ubequad		0xb25aa40e0a9ed111	Works 5-6 document
!:mime	application/vnd.ms-works
!:apple	????AWWP
!:ext	wps
#??
# URL:	http://fileformats.archiveteam.org/wiki/Microsoft_Publisher
>>88 	ubequad		0x00c0000000000046	: Microsoft
>>>80 	ubequad		0x0112020000000000	Publisher
!:mime	application/vnd.ms-publisher
!:ext	pub
#
# URL:	http://fileformats.archiveteam.org/wiki/PPT
#??
>>88 	ubequad		0xa90300aa00510ea3	: Microsoft
>>>80 	ubequad		0x70ae7bea3bfbcd11	PowerPoint 95 presentation
!:mime	application/vnd.ms-powerpoint
# https://www.macdisk.com/macsigen.php
!:apple	????PPT3
!:ext	ppt/pot
#??
>>88 	ubequad		0x86ea00aa00b929e8	: Microsoft
>>>80 	ubequad		0x108d81649b4fcf11	PowerPoint 97-2003 presentation or template
!:mime	application/vnd.ms-powerpoint
!:apple	????PPT3
# /autostart/template
!:ext	ppt/pps/pot
#
# URL:	https://en.wikipedia.org/wiki/Microsoft_Project
#??
>>88 	ubequad		0xbe1100c04fb6faf1	: Microsoft
>>>80 	ubequad		0x3a8fb774c8c8d111	Project
!:mime	application/vnd.ms-project
!:ext	mpp
#
# URL:	http://fileformats.archiveteam.org/wiki/SHW_(Corel)
#???
>>88 	ubequad		0x99ae04021c007002	: WordPerfect
>>>80 	ubequad		0x62fe2e4099191b10	7-X3 presentation
!:mime	application/x-corelpresentations
#!:mime	application/x-shw-viewer
#!:mime	image/x-presentations
!:ext	shw
#
# URL:	http://www.checkfilename.com/view-details/WordPerfect-Office-X3/RespageIndex/0/sTab/2/
>>>80 	ubequad		0x60fe2e4099191b10	9 Graphic
#!:mime	application/x-wpg
#!:mime	image/x-wordperfect-graphics
!:mime	image/x-wpg
# https://www.macdisk.com/macsigen.php "WPC2" for Wordperfect 2 *.wpd
!:apple	????WPC9
!:ext	wpg
#
# URL:	http://fileformats.archiveteam.org/wiki/StarOffice_binary_formats
>>88 	ubequad		0x996104021c007002	: StarOffice
>>>80 	ubequad		0x407e5cdc5cb31b10	StarWriter 3.0 document or template
# https://www.openoffice.org/framework/documentation/mimetypes/mimetypes.html
!:mime	application/x-starwriter
!:ext	sdw/vor
#
>>>80 	ubequad		0xa03f543fa6b61b10	StarCalc 3.0 spreadsheet or template
!:mime	application/x-starcalc
!:ext	sdc/vor
#
>>>80 	ubequad		0xe0aa10af6db31b10	StarDraw 3.0 drawing or template
!:mime	application/x-starimpress
#!:mime	application/x-stardraw
# sda ??
!:ext	sdd/sda/vor
#??
>>88 	ubequad		0x89cb008029e4b0b1	: StarOffice
>>>80 	ubequad		0x41d461633542d011	StarCalc 4.0 spreadsheet or template
!:mime	application/x-starcalc
!:ext	sdc/vor
#
>>>80 	ubequad		0x61b8a5c6d685d111	StarCalc 5.0 spreadsheet or template
!:mime	application/vnd.stardivision.cal
!:ext	sdc/vor
#
>>>80 	ubequad		0xc03c2d011642d011	StarImpress 4.0 presentation or template
!:mime	application/x-starimpress
!:ext	sdd/vor
#??
>>88 	ubequad		0xb12a04021c007002	: StarOffice
>>>80 	ubequad		0x600459d4fd351c10	StarMath 3.0
!:mime	application/x-starmath
!:ext	smf
#??
>>88 	ubequad		0x8e2c00001b4cc711	: StarOffice
>>>80 	ubequad		0xe0999cfb6d2c1c10	StarChart 3.0
!:mime	application/x-starchart
!:ext	sds
#??
>>88 	ubequad		0xa45e00a0249d57b1	: StarOffice
>>>80 	ubequad		0xb0e9048b0e42d011	StarWriter 4.0 document or template
!:mime	application/x-starwriter
!:ext	sdw/vor
#??
>>88 	ubequad		0x89ca008029e4b0b1	: StarOffice
>>>80 	ubequad		0xe1b7b3022542d011	StarMath 4.0
!:mime	application/x-starmath
!:ext	smf
#
>>>80 	ubequad		0xe0b7b3022542d011	StarChart 4.0
!:mime	application/x-starchart
!:ext	sds
#??
>>88 	ubequad		0xa53f00a0249d57b1	: StarOffice
>>>80 	ubequad		0x70c90a340de3d011	Master 4.0 document
!:mime	application/x-starwriter-global
!:ext	sgl
#??
>>88 	ubequad		0x89d0008029e4b0b1	: StarOffice
>>>80 	ubequad		0x40e6b5ffde85d111	StarMath 5.0
!:mime	application/vnd.stardivision.math
!:ext	smf
#
>>>80 	ubequad		0xa005892ebd85d111	StarDraw 5.0 drawing or template
!:mime	application/vnd.stardivision.draw
!:ext	sda/vor
#
>>>80 	ubequad		0x21725c56bc85d111	StarImpress 5.0 presentation or template
!:mime	application/vnd.stardivision.impress
# sda is used for what?
!:ext	sdd/vor/sda
#
>>>80 	ubequad		0x214388bfdd85d111	StarChart 5.0
!:mime	application/vnd.stardivision.chart
!:ext	sds
# ??
>>88 	ubequad		0xaab4006097da561a	: StarOffice
>>>80 	ubequad		0xd1f90cc2ae85d111	StarWriter 5.0 document or template
!:mime	application/vnd.stardivision.writer
!:ext	sdw/vor
#
>>>80 	ubequad		0xd3f90cc2ae85d111	Master 5.0 document
!:mime	application/vnd.stardivision.writer-global
!:ext	sgl
#??
# URL:	http://fileformats.archiveteam.org/wiki/FlashPix
>>88 	ubequad		0x855300aa00a1f95b	: Kodak
>>>80 	ubequad		0x0067615654c1ce11	FlashPIX Image
!:mime	image/vnd.fpx
!:apple	????FPix
!:ext	fpx
# URL:	https://en.wikipedia.org/wiki/SoftMaker_Office
>>88 	ubequad		0x95f600a0cc3cca14	: PlanMaker
>>>80 	ubequad		0x9174088a6452d411	document or template
!:mime	application/vnd.softmaker.planmaker
# pmv for template	https://www.file-extensions.org/pmv-file-extension
!:ext	pmd/pmv
# remaining non null clsid
>>88 	default		x			: UNKNOWN
!:mime	application/x-ole-storage
>>>80 	ubequad		!0			\b, clsid 0x%16.16llx
>>>88 	ubequad		x			\b%16.16llx



#------------------------------------------------------------------------------
# $File: diff,v 1.16 2017/03/17 22:20:22 christos Exp $
# diff:  file(1) magic for diff(1) output
#
0	search/1	diff\040	diff output text
!:mime	text/x-diff
0	search/1	***\040 	diff output text
!:mime	text/x-diff
0	search/1	Only\040in\040 	diff output text
!:mime	text/x-diff
0	search/1	Common\040subdirectories:\040 	diff output text
!:mime	text/x-diff

0	search/1	Index:		RCS/CVS diff output text
!:mime	text/x-diff

# bsdiff:  file(1) magic for bsdiff(1) output
0	string/b		BSDIFF40	bsdiff(1) patch file


# unified diff
0	search/4096	---\040
>&0	search/1024 \n
>>&0	search/1 +++\040
>>>&0	search/1024 \n
>>>>&0	search/1 @@	unified diff output text
!:mime	text/x-diff
!:strength + 90

# librsync -- the library for network deltas
#
# Copyright (C) 2001 by Martin Pool.  You may do whatever you want with
# this file.
#
0	belong		0x72730236	rdiff network-delta data

0	belong		0x72730136	rdiff network-delta signature data
>4	belong		x		(block length=%d,
>8	belong		x		signature strength=%d)

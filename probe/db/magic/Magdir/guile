
#------------------------------------------------------------------------------
# $File: guile,v 1.2 2019/04/19 00:42:27 christos Exp $
# <AUTHOR> <EMAIL>
# https://www.gnu.org/s/guile/
# https://git.savannah.gnu.org/gitweb/?p=guile.git;f=libguile/_scm.h;hb=HEAD#l250

0	string	GOOF----	Guile Object
>8	string	LE		\b, little endian
>8	string	BE		\b, big endian
>11	string	4		\b, 32bit
>11	string	8		\b, 64bit
>13	regex	.\..		\b, bytecode v%s

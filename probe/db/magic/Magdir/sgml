
#------------------------------------------------------------------------------
# $File: sgml,v 1.41 2020/06/07 18:16:43 christos Exp $
# Type:	SVG Vectorial Graphics
# From:	<PERSON> <<EMAIL>>
0	string		\<?xml\ version=
>14	regex		['"\ \t]*[0-9.]+['"\ \t]*
>>19	search/4096	\<svg			SVG Scalable Vector Graphics image
!:mime	image/svg+xml
>>19	search/4096	\<gnc-v2		GnuCash file
!:mime	application/x-gnucash
0	string		\<svg			SVG Scalable Vector Graphics image
!:mime	image/svg+xml

# Sitemap file
0	string/t		\<?xml\ version=
>14	regex		['"\ \t]*[0-9.]+['"\ \t]*
>>19	search/4096	\<urlset		XML Sitemap document text
!:mime	application/xml-sitemap

# OpenStreetMap XML (.osm)
# https://wiki.openstreetmap.org/wiki/OSM_XML
# <AUTHOR> <EMAIL>
0	string		\<?xml\ version=
>14	regex		['"\ \t]*[0-9.]+['"\ \t]*
>>19	search/4096	\<osm			OpenStreetMap XML data

# xhtml
0	string/t		\<?xml\ version="
>19	search/4096/cWbt	\<!doctype\ html	XHTML document text
>>15	string		>\0	(version %.3s)
!:mime	text/html
0	string/t		\<?xml\ version='
>19	search/4096/cWbt	\<!doctype\ html	XHTML document text
>>15	string		>\0	(version %.3s)
!:mime	text/html
0	string/t		\<?xml\ version="
>19	search/4096/cWbt	\<html	broken XHTML document text
>>15	string		>\0	(version %.3s)
!:mime	text/html

#------------------------------------------------------------------------------
# sgml:  file(1) magic for Standard Generalized Markup Language
# HyperText Markup Language (HTML) is an SGML document type,
# from Daniel Quinlan (<EMAIL>)
# adapted to string extenstions by Anthon van der Neut <<EMAIL>)
0	search/4096/cWt	\<!doctype\ html	HTML document text
!:mime	text/html
!:strength + 5

# SVG document
# https://www.w3.org/TR/SVG/single-page.html
0	search/4096/cWbt	\<!doctype\ svg	SVG XML document
!:mime  image/svg+xml
!:strength + 5

0	search/4096/cwt	\<head\>		HTML document text
!:mime	text/html
!:strength + 5
0	search/4096/cWt	\<head\ 		HTML document text
!:mime	text/html
!:strength + 5
0	search/4096/cwt	\<title\>		HTML document text
!:mime	text/html
!:strength + 5
0	search/4096/cWt	\<title\ 		HTML document text
!:mime	text/html
!:strength + 5
0	search/4096/cwt	\<html\>		HTML document text
!:mime	text/html
!:strength + 5
0	search/4096/cWt	\<html\ 		HTML document text
!:mime	text/html
!:strength + 5
0	search/4096/cwt	\<script\> 		HTML document text
!:mime	text/html
!:strength + 5
0	search/4096/cWt	\<script\ 		HTML document text
!:mime	text/html
!:strength + 5
0	search/4096/cwt	\<style\> 		HTML document text
!:mime	text/html
!:strength + 5
0	search/4096/cWt	\<style\  		HTML document text
!:mime	text/html
!:strength + 5
0	search/4096/cwt	\<table\>		HTML document text
!:mime	text/html
!:strength + 5
0	search/4096/cWt	\<table\ 		HTML document text
!:mime	text/html
!:strength + 5

0	search/4096/cwt	\<a\ href=		HTML document text
!:mime	text/html
!:strength + 5

# Extensible markup language (XML), a subset of SGML
# from Marc Prud'hommeaux (<EMAIL>)
0	search/1/cwt	\<?xml			XML document text
!:mime	text/xml
!:strength + 5
0	string/t		\<?xml\ version\ "	XML
!:mime	text/xml
!:strength + 5
0	string/t		\<?xml\ version="	XML
!:mime	text/xml
!:strength + 5
>15	string/t	>\0			%.3s document text
>>23	search/1	\<xsl:stylesheet	(XSL stylesheet)
>>24	search/1	\<xsl:stylesheet	(XSL stylesheet)
0	string/t	\<?xml\ version='	XML
!:mime	text/xml
!:strength + 5
>15	string/t	>\0			%.3s document text
>>23	search/1	\<xsl:stylesheet	(XSL stylesheet)
>>24	search/1	\<xsl:stylesheet	(XSL stylesheet)
0	search/1/wt	\<?XML			broken XML document text
!:mime	text/xml
!:strength - 10


# SGML, mostly from rph@sq
0	search/4096/cwt	\<!doctype		exported SGML document text
0	search/4096/cwt	\<!subdoc		exported SGML subdocument text
0	search/4096/cwt	\<!--			exported SGML document text
!:strength - 10

# Web browser cookie files
# (Mozilla, Galeon, Netscape 4, Konqueror..)
# <AUTHOR> <EMAIL>
0	search/1	#\ HTTP\ Cookie\ File	Web browser cookie text
0	search/1	#\ Netscape\ HTTP\ Cookie\ File	Netscape cookie text
0	search/1	#\ KDE\ Cookie\ File	Konqueror cookie text

# XML-based format representing braille pages in a digital format.
#
# Specification:
# http://files.pef-format.org/specifications/pef-2008-1/pef-specification.html
#
# <AUTHOR> <EMAIL>
0   string      \<?xml\ version=
>14 regex       ['"\ \t]*[0-9.]+['"\ \t]*
>>19    search/4096 \<pef           Portable Embosser Format
!:mime  application/x-pef+xml

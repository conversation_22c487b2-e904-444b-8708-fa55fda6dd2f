
#------------------------------------------------------------------------------
# $File: palm,v 1.14 2019/04/19 00:42:27 christos Exp $
# palm:	 file(1) magic for PalmOS {.prc,.pdb}: applications, docfiles, and hacks
#
# <PERSON> <<EMAIL>>

# These are weak, byte 59 is not guaranteed to be 0 and there are
# 8 character identifiers at byte 60, one I found for appl is BIGb.
# What are the possibilities and where is this documented?

# The common header format for PalmOS .pdb/.prc files is
# {
#         char            name[ 32 ];
#         Word            attributes;
#         Word            version;
#         DWord           creationDate;
#         DWord           modificationDate;
#         DWord           lastBackupDate;
#         DWord           modificationNumber;
#         DWord           appInfoID;
#         DWord           sortInfoID;
#         char            type[4];
#         char            creator[4];
#         DWord           uniqueIDSeed;
#         RecordListType  recordList;
# };
#
# Datestamps are unsigned seconds since the MacOS epoch (Jan 1, 1904),
# or Unix/POSIX time + 2082844800.

0		name		aportisdoc
# date is supposed to be big-endian seconds since 1 Jan 1904, but many
# files contain the timestamp in little-endian or a completely
# nonsensical value...
#>36		bedate-2082844800	>0	\b, created %s
# compression: 1=uncomp, 2=orig, 0x4448=HuffDic
>(78.L)		beshort		=1		\b, uncompressed
# compressed
>(78.L)		beshort		>1
>>(78.L+4)	belong		x		\b, %d bytes uncompressed

# appl
#60		string		appl		PalmOS application
#>0		string		>\0		"%s"

# HACK
#60		string		HACK		HackMaster hack
#>0		string		>\0		"%s"

# iSiloX e-book
60		string		SDocSilX	iSiloX E-book
>0		string		>\0		"%s"

# Mobipocket (www.mobipocket.com), donated by Carl Witty
# expanded by Ralf Brown
60		string	 	BOOKMOBI	Mobipocket E-book
# MobiPocket stores a full title, pointed at by the belong at offset
# 0x54 in its header at (78.L), with length given by the belong at
# offset 0x58.
# there's no guarantee that the title string is null-terminated, but
# we currently can't specify a variable-length string where the length
# field is not at the start of the string; in practice, the data
# following the string always seems to start with a zero byte
>(78.L)		belong		x
>>&(&0x50.L-4)	string		>\0		"%s"
>0		use		aportisdoc
>>(78.L+0x68)	belong		>0		\b, version %d
>>(78.L+0x1C)	belong		!0		\b, codepage %d
>>(78.L+0x0C)	beshort	 	>0		\b, encrypted (type %d)

# AportisDoc/PalmDOC
60		string		TEXtREAd	AportisDoc/PalmDOC E-book
>0		string		>\0		"%s"
>0		use		aportisdoc

# Variety of PalmOS document types
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL> for his DocType
60	string			BVokBDIC	BDicty PalmOS document
>0	string			>\0		"%s"
60	string			DB99DBOS	DB PalmOS document
>0	string			>\0		"%s"
60	string			vIMGView	FireViewer/ImageViewer PalmOS document
>0	string			>\0		"%s"
60	string			PmDBPmDB	HanDBase PalmOS document
>0	string			>\0		"%s"
60	string			InfoINDB	InfoView PalmOS document
>0	string			>\0		"%s"
60	string			ToGoToGo	iSilo PalmOS document
>0	string			>\0		"%s"
60	string			JfDbJBas	JFile PalmOS document
>0	string			>\0		"%s"
60	string			JfDbJFil	JFile Pro PalmOS document
>0	string			>\0		"%s"
60	string			DATALSdb	List PalmOS document
>0	string			>\0		"%s"
60	string			Mdb1Mdb1	MobileDB PalmOS document
>0	string			>\0		"%s"
60	string			PNRdPPrs	PeanutPress PalmOS document
>0	string			>\0		"%s"
60	string			DataPlkr	Plucker PalmOS document
>0	string			>\0		"%s"
60	string			DataSprd	QuickSheet PalmOS document
>0	string			>\0		"%s"
60	string			SM01SMem	SuperMemo PalmOS document
>0	string			>\0		"%s"
60	string			TEXtTlDc	TealDoc PalmOS document
>0	string			>\0		"%s"
60	string			InfoTlIf	TealInfo PalmOS document
>0	string			>\0		"%s"
60	string			DataTlMl	TealMeal PalmOS document
>0	string			>\0		"%s"
60	string			DataTlPt	TealPaint PalmOS document
>0	string			>\0		"%s"
60	string			dataTDBP	ThinkDB PalmOS document
>0	string			>\0		"%s"
60	string			TdatTide	Tides PalmOS document
>0	string			>\0		"%s"
60	string			ToRaTRPW	TomeRaider PalmOS document
>0	string			>\0		"%s"

# A GutenPalm zTXT etext for use on Palm Pilots (http://gutenpalm.sf.net)
# For version 1.xx zTXTs, outputs version and numbers of bookmarks and
#   annotations.
# For other versions, just outputs version.
#
60		string		zTXT		A GutenPalm zTXT e-book
>0		string		>\0		"%s"
>(0x4E.L)	byte		0
>>(0x4E.L+1)	byte		x		(v0.%02d)
>(0x4E.L)	byte		1
>>(0x4E.L+1)	byte		x		(v1.%02d)
>>>(0x4E.L+10)	beshort		>0
>>>>(0x4E.L+10) beshort		<2		- 1 bookmark
>>>>(0x4E.L+10) beshort		>1		- %d bookmarks
>>>(0x4E.L+14)	beshort		>0
>>>>(0x4E.L+14) beshort		<2		- 1 annotation
>>>>(0x4E.L+14) beshort		>1		- %d annotations
>(0x4E.L)	byte		>1		(v%d.
>>(0x4E.L+1)	byte		x		%02d)

# Palm OS .prc file types
60		string		libr
# flags, only bit 0 or bit 6
# https://en.wikipedia.org/wiki/PRC_%28Palm_OS%29
# https://web.mit.edu/tytso/www/pilot/prc-format.html
>0x20		beshort&0xffbe	0
>>0		string		>\0		Palm OS dynamic library data "%s"
60		string		ptch		Palm OS operating system patch data
>0		string		>\0		"%s"

# Mobipocket (www.mobipocket.com), donated by Carl Witty
60	string			BOOKMOBI	Mobipocket E-book
>0	string			>\0		"%s"


# $File: espressif,v 1.2 2019/11/15 21:03:14 christos Exp $
# configuration dump of Tasmota firmware for ESP8266 based devices by Espressif
# URL: https://github.com/arendst/Sonoff-Tasmota/
# Reference: https://codeload.github.com/arendst/Sonoff-Tasmota/zip/release-6.2/
# Sonoff-Tasmota-release-6.2.zip/Sonoff-Tasmota-release-6.2/sonoff/settings.h 
# From: <PERSON><PERSON>
#
# cfg_holder=4617=0x1209
0		uleshort	4617
# remaining settings normally 0x5A+offset XORed; free_1D5[20] empty since 5.12.0e
>0x1D5		ubequad		0x2f30313233343536	configuration of Tasmota firmware (ESP8266)
!:mime	application/x-tasmota-dmp
!:ext	dmp
# version like ******* ~ 0x06020100 XORed to 0x63666262
>>11		ubyte^0x65	x			\b, version %u
>>10		ubyte^0x64	x			\b.%u
>>9		ubyte^0x63	x			\b.%u
>>8		ubyte^0x62	x			\b.%u
#>8		ubelong		x			(0x%x)
# hostname[33] XORed
>>0x165		ubyte^0x1BF	x			\b, hostname %c
>>0x166		ubyte^0x1C0	>037			\b%c
>>0x167		ubyte^0x1C1	>037			\b%c
>>0x168		ubyte^0x1C2	>037			\b%c
>>0x169		ubyte^0x1C3	>037			\b%c
>>0x16A		ubyte^0x1C4	>037			\b%c
>>0x16B		ubyte^0x1C5	>037			\b%c
>>0x16C		ubyte^0x1C6	>037			\b%c
>>0x16D		ubyte^0x1C7	>037			\b%c
>>0x16E		ubyte^0x1C8	>037			\b%c
>>0x16F		ubyte^0x1C9	>037			\b%c
>>0x170		ubyte^0x1CA	>037			\b%c
>>0x171		ubyte^0x1CB	>037			\b%c
>>0x172		ubyte^0x1CC 	>037			\b%c
>>0x173		ubyte^0x1CD	>037			\b%c
>>0x174		ubyte^0x1CE	>037			\b%c
>>0x175		ubyte^0x1CF	>037			\b%c
>>0x176		ubyte^0x1D0	>037			\b%c
>>0x177		ubyte^0x1D1	>037			\b%c
>>0x178		ubyte^0x1D2	>037			\b%c
>>0x179		ubyte^0x1D3	>037			\b%c
>>0x17A		ubyte^0x1D4	>037			\b%c
>>0x17B		ubyte^0x1D5	>037			\b%c
>>0x17C		ubyte^0x1D6	>037			\b%c
>>0x17D		ubyte^0x1D7	>037			\b%c
>>0x17E		ubyte^0x1D8	>037			\b%c
>>0x17F		ubyte^0x1D9	>037			\b%c
>>0x180		ubyte^0x1DA	>037			\b%c
>>0x181		ubyte^0x1DB	>037			\b%c
>>0x182		ubyte^0x1DC	>037			\b%c
>>0x183		ubyte^0x1DD	>037			\b%c
>>0x184		ubyte^0x1DE	>037			\b%c
>>0x185		ubyte^0x1DF	>037			\b%c
#>>0x165		string		x			(%.33s)



#------------------------------------------------------------------------------
# $File: c-lang,v 1.28 2019/11/15 21:03:14 christos Exp $
# c-lang:  file(1) magic for C and related languages programs
#
# The strength is to beat standard HTML

# BCPL
0	search/8192	"libhdr"	BCPL source text
!:mime	text/x-bcpl
0	search/8192	"LIBHDR"	BCPL source text
!:mime	text/x-bcpl

# C
# Check for class if include is found, otherwise class is beaten by include because of lowered strength
0	search/8192	#include
>0	regex	\^#include			C
>>0	regex	\^class[[:space:]]+
>>>&0	regex 	\\{[\.\*]\\}(;)?$			\b++
>>&0	clear	x				source text
!:strength + 13
!:mime	text/x-c
0	search/8192	pragma
>0	regex	\^#[[:space:]]*pragma	C source text
!:mime	text/x-c
0	search/8192	endif
>0	regex	\^#[[:space:]]*(if\|ifn)def
>>&0	regex	\^#[[:space:]]*endif$	C source text
!:mime	text/x-c
0	search/8192	define
>0	regex	\^#[[:space:]]*(if\|ifn)def
>>&0	regex	\^#[[:space:]]*define	C source text
!:mime	text/x-c
0	search/8192	char
>0	regex	\^[[:space:]]*char(\ \\*|\\*)(.+)(=.*)?;[[:space:]]*$			C source text
!:mime	text/x-c
0	search/8192	double
>0	regex	\^[[:space:]]*double(\ \\*|\\*)(.+)(=.*)?;[[:space:]]*$			C source text
!:mime	text/x-c
0	search/8192	extern
>0	regex	\^[[:space:]]*extern[[:space:]]+		C source text
!:mime	text/x-c
0	search/8192	float
>0	regex	\^[[:space:]]*float(\ \\*|\\*)(.+)(=.*)?;[[:space:]]*$			C source text
!:mime	text/x-c
0	search/8192	struct
>0	regex	\^struct[[:space:]]+		C source text
!:mime	text/x-c
0	search/8192	union
>0	regex	\^union[[:space:]]+		C source text
!:mime	text/x-c
0	search/8192	main(
>&0 regex	\\)[[:space:]]*\\{		C source text
!:mime	text/x-c

# C++
# The strength of these rules is increased so they beat the C rules above
0	search/8192	namespace
>0	regex	\^namespace[[:space:]]+[_[:alpha:]]{1,30}[[:space:]]*\\{	C++ source text
!:strength + 30
!:mime	text/x-c++
# using namespace [namespace] or using std::[lib]
0	search/8192	using
>0	regex	\^using[[:space:]]+(namespace\ )?std(::)?[[:alpha:]]*[[:space:]]*;		C++ source text
!:strength + 30
!:mime	text/x-c++
0	search/8192	template
>0	regex	\^[[:space:]]*template[[:space:]]*<.*>[[:space:]]*$	C++ source text
!:strength + 30
!:mime	text/x-c++
0	search/8192	virtual
>0	regex	\^[[:space:]]*virtual[[:space:]]+.*[};][[:space:]]*$		C++ source text
!:strength + 30
!:mime	text/x-c++
# But class alone is reduced to avoid beating php (Jens Schleusener)
0	search/8192	class
>0	regex	\^[[:space:]]*class[[:space:]]+[[:digit:][:alpha:]:_]+[[:space:]]*\\{(.*[\n]*)*\\}(;)?$		C++ source text
!:strength + 13
!:mime	text/x-c++
0	search/8192	public
>0	regex	\^[[:space:]]*public:		C++ source text
!:strength + 30
!:mime	text/x-c++
0	search/8192	private
>0	regex	\^[[:space:]]*private:		C++ source text
!:strength + 30
!:mime	text/x-c++
0	search/8192	protected
>0	regex	\^[[:space:]]*protected:		C++ source text
!:strength + 30
!:mime	text/x-c++

# Objective-C
0	search/8192	#import
>0	regex	\^#import			Objective-C source text
!:strength + 25
!:mime	text/x-objective-c

# <AUTHOR> <EMAIL>
0	string		cscope		cscope reference data
>7	string		x		version %.2s
# We skip the path here, because it is often long (so file will
# truncate it) and mostly redundant.
# The inverted index functionality was added some time between
# versions 11 and 15, so look for -q if version is above 14:
>7	string		>14
>>10	search/100	\ -q\ 		with inverted index
>10	search/100	\ -c\ 		text (non-compressed)

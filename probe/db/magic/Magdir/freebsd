
#------------------------------------------------------------------------------
# $File: freebsd,v 1.7 2009/09/19 16:28:09 christos Exp $
# freebsd:  file(1) magic for FreeBSD objects
#
# All new-style FreeBSD magic numbers are in host byte order (i.e.,
# little-endian on x86).
#
# XXX - this comes from the file "freebsd" in a recent FreeBSD version of
# "file"; it, and the NetBSD stuff in "netbsd", appear to use different
# schemes for distinguishing between executable images, shared libraries,
# and object files.
#
# FreeBSD says:
#
#    Regardless of whether it's pure, demand-paged, or none of the
#    above:
#
#	if the entry point is < 4096, then it's a shared library if
#	the "has run-time loader information" bit is set, and is
#	position-independent if the "is position-independent" bit
#	is set;
#
#	if the entry point is >= 4096 (or >4095, same thing), then it's
#	an executable, and is dynamically-linked if the "has run-time
#	loader information" bit is set.
#
# On x86, NetBSD says:
#
#    If it's neither pure nor demand-paged:
#
#	if it has the "has run-time loader information" bit set, it's
#	a dynamically-linked executable;
#
#	if it doesn't have that bit set, then:
#
#	    if it has the "is position-independent" bit set, it's
#	    position-independent;
#
#	    if the entry point is non-zero, it's an executable, otherwise
#	    it's an object file.
#
#    If it's pure:
#
#	if it has the "has run-time loader information" bit set, it's
#	a dynamically-linked executable, otherwise it's just an
#	executable.
#
#    If it's demand-paged:
#
#	if it has the "has run-time loader information" bit set,
#	then:
#
#	    if the entry point is < 4096, it's a shared library;
#
#	    if the entry point is = 4096 or > 4096 (i.e., >= 4096),
#	    it's a dynamically-linked executable);
#
#	if it doesn't have the "has run-time loader information" bit
#	set, then it's just an executable.
#
# (On non-x86, NetBSD does much the same thing, except that it uses
# 8192 on 68K - except for "68k4k", which is presumably "68K with 4K
# pages - SPARC, and MIPS, presumably because Sun-3's and Sun-4's
# had 8K pages; dunno about MIPS.)
#
# I suspect the two will differ only in perverse and uninteresting cases
# ("shared" libraries that aren't demand-paged and whose pages probably
# won't actually be shared, executables with entry points <4096).
#
# I leave it to those more familiar with FreeBSD and NetBSD to figure out
# what the right answer is (although using ">4095", FreeBSD-style, is
# probably better than separately checking for "=4096" and ">4096",
# NetBSD-style).  (The old "netbsd" file analyzed FreeBSD demand paged
# executables using the NetBSD technique.)
#
0	lelong&0377777777	041400407	FreeBSD/i386
>20	lelong			<4096
>>3	byte&0xC0		&0x80		shared library
>>3	byte&0xC0		0x40		PIC object
>>3	byte&0xC0		0x00		object
>20	lelong			>4095
>>3	byte&0x80		0x80		dynamically linked executable
>>3	byte&0x80		0x00		executable
>16	lelong			>0		not stripped

0	lelong&0377777777	041400410	FreeBSD/i386 pure
>20	lelong			<4096
>>3	byte&0xC0		&0x80		shared library
>>3	byte&0xC0		0x40		PIC object
>>3	byte&0xC0		0x00		object
>20	lelong			>4095
>>3	byte&0x80		0x80		dynamically linked executable
>>3	byte&0x80		0x00		executable
>16	lelong			>0		not stripped

0	lelong&0377777777	041400413	FreeBSD/i386 demand paged
>20	lelong			<4096
>>3	byte&0xC0		&0x80		shared library
>>3	byte&0xC0		0x40		PIC object
>>3	byte&0xC0		0x00		object
>20	lelong			>4095
>>3	byte&0x80		0x80		dynamically linked executable
>>3	byte&0x80		0x00		executable
>16	lelong			>0		not stripped

0	lelong&0377777777	041400314	FreeBSD/i386 compact demand paged
>20	lelong			<4096
>>3	byte&0xC0		&0x80		shared library
>>3	byte&0xC0		0x40		PIC object
>>3	byte&0xC0		0x00		object
>20	lelong			>4095
>>3	byte&0x80		0x80		dynamically linked executable
>>3	byte&0x80		0x00		executable
>16	lelong			>0		not stripped

# XXX gross hack to identify core files
# cores start with a struct tss; we take advantage of the following:
# byte 7:     highest byte of the kernel stack pointer, always 0xfe
#      8/9:   kernel (ring 0) ss value, always 0x0010
#      10 - 27: ring 1 and 2 ss/esp, unused, thus always 0
#      28:    low order byte of the current PTD entry, always 0 since the
#             PTD is page-aligned
#
7	string	\357\020\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0	FreeBSD/i386 a.out core file
>1039	string	>\0	from '%s'

# /var/run/ld.so.hints
# What are you laughing about?
0	lelong			011421044151	ld.so hints file (Little Endian
>4	lelong			>0		\b, version %d)
>4	belong			<1		\b)
0	belong			011421044151	ld.so hints file (Big Endian
>4	belong			>0		\b, version %d)
>4	belong			<1		\b)

#
# Files generated by FreeBSD scrshot(1)/vidcontrol(1) utilities
#
0	string	SCRSHOT_	scrshot(1) screenshot,
>8	byte	x		version %d,
>9	byte	2		%d bytes in header,
>>10	byte	x		%d chars wide by
>>11	byte	x		%d chars high


#------------------------------------------------------------
# $File: java,v 1.21 2019/02/18 17:58:50 christos Exp $
# Java ByteCode and Mach-O binaries (e.g., Mac OS X) use the
# same magic number, 0xcafebabe, so they are both handled
# in the entry called "cafebabe".
#------------------------------------------------------------
# Java serialization
# From Martin Pool (<EMAIL>)
0	beshort		0xaced		Java serialization data
>2	beshort		>0x0004		\b, version %d

0	belong		0xfeedfeed	Java KeyStore
!:mime	application/x-java-keystore
0	belong		0xcececece	Java JCE KeyStore
!:mime	application/x-java-jce-keystore

# Java source
0	regex	\^import.*;$	Java source
!:mime	text/x-java

# Java HPROF dumps
# https://java.net/downloads/heap-snapshot/hprof-binary-format.html
0	string		JAVA\x20PROFILE\x201.0.
>0x12	byte		0
>>0x11	ubyte-0x31	<2      Java HPROF dump,
>>>0x17	beqdate/1000	x       created %s

# Java jmod module
# See https://hg.openjdk.java.net/jdk9/jdk9/jdk/file/tip/src/java.base/share/classes/jdk/internal/jmod/JmodFile.java
# Grr. 2 byte magic "JM", really? In 2019?
0	belong		0x4a4d0100	Java jmod module version 1.0
!:mime	application/x-java-jmod

# Java jlinked image
# See https://hg.openjdk.java.net/jdk9/jdk9/jdk/file/tip/src/java.base/share/native/libjimage/imageFile.hpp
0	belong	0xcafedada	Java module image (big endian)
>4	beshort	>0x00	\b, version %d
>6	beshort	x	\b.%d
!:mime	application/x-java-image

0	lelong	0xcafedada	Java module image (little endian)
>6	leshort	>0x00	\b, version %d
>4	leshort	x	\b.%d
!:mime	application/x-java-image

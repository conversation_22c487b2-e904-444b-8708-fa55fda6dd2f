
#------------------------------------------------------------------------------
# $File: sereal,v 1.3 2015/02/05 19:14:45 christos Exp $
# sereal: file(1) magic the Sereal binary serialization format
#
# From: <PERSON><PERSON> <<EMAIL>>
#
# See the specification of the format at
# https://github.com/Sereal/Sereal/blob/master/sereal_spec.pod#document-header-format
#
# I'd have liked to do the byte&0xF0 matching against 0, 1, 2 ... by
# doing (byte&0xF0)>>4 here, but unfortunately that's not
# supported. So when we print out a message about an unknown format
# we'll print out e.g. 0x30 instead of the more human-readable
# 0x30>>4.
#
# See https://github.com/Sereal/Sereal/commit/35372ae01d in the
# Sereal.git repository for test Sereal data.
0	name		sereal
>4	byte&0x0F	x		(version %d,
>4	byte&0xF0	0x00		uncompressed)
>4	byte&0xF0	0x10		compressed with non-incremental Snappy)
>4	byte&0xF0	0x20		compressed with incremental Snappy)
>4	byte&0xF0	>0x20		unknown subformat, flag: %d>>4)

0	string/b	\=srl		Sereal data packet
!:mime application/sereal
>&0	use		sereal
0	string/b	\=\xF3rl	Sereal data packet
!:mime application/sereal
>&0	use		sereal
0	string/b	\=\xC3\xB3rl	Sereal data packet, UTF-8 encoded
!:mime application/sereal
>&0	use		sereal


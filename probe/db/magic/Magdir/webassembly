#------------------------------------------------------------------------------
# $File: webassembly,v 1.3 2019/04/19 00:42:27 christos Exp $
# webassembly:  file(1) magic for WebAssembly modules
#
# WebAssembly is a virtual architecture developed by a W3C Community
# Group at https://webassembly.org/. The file extension is .wasm, and
# the MIME type is application/wasm.
#
# https://webassembly.org/docs/binary-encoding/ is the main
# document describing the binary format.
# From: Pip <PERSON>t <<EMAIL>> and <PERSON>

0	string	\0asm	WebAssembly (wasm) binary module
>4	lelong	=1	version %#x (MVP)
>4	lelong	>1	version %#x


#------------------------------------------------------------------------------
# $File: vms,v 1.10 2017/03/17 21:35:28 christos Exp $
# vms:  file(1) magic for VMS executables (experimental)
#
# VMS .exe formats, both VAX and AXP (<PERSON>, <EMAIL>)

# GRR 950122:  I'm just guessing on these, based on inspection of the headers
# of three executables each for Alpha and VAX architectures.  The VAX files
# all had headers similar to this:
#
#   00000  b0 00 30 00 44 00 60 00  00 00 00 00 30 32 30 35  ..0.D.`.....0205
#   00010  01 01 00 00 ff ff ff ff  ff ff ff ff 00 00 00 00  ................
#
0	string	\xb0\0\x30\0	VMS VAX executable
>44032	string	PK\003\004	\b, Info-ZIP SFX archive v5.12 w/decryption
#
# The AXP files all looked like this, except that the byte at offset 0x22
# was 06 in some of them and 07 in others:
#
#   00000  03 00 00 00 00 00 00 00  ec 02 00 00 10 01 00 00  ................
#   00010  68 00 00 00 98 00 00 00  b8 00 00 00 00 00 00 00  h...............
#   00020  00 00 07 00 00 00 00 00  00 00 00 00 00 00 00 00  ................
#   00030  00 00 00 00 01 00 00 00  00 00 00 00 00 00 00 00  ................
#   00040  00 00 00 00 ff ff ff ff  ff ff ff ff 02 00 00 00  ................
#
# GRR this test is still too general as it catches example adressen.dbt
0	belong	0x03000000
>8	ubelong	0xec020000	VMS Alpha executable
>>75264	string	PK\003\004	\b, Info-ZIP SFX archive v5.12 w/decryption

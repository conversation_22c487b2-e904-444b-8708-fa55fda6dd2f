
#------------------------------------------------------------------------------
# $File: coverage,v 1.2 2019/04/19 00:42:27 christos Exp $
# xoverage:  file(1) magic for test coverage data

# File formats used to store test coverage data
# 2016-05-21, <PERSON> <<EMAIL>>


# - GCC gcno - written by <PERSON><PERSON> at compile time when compiling with
# 	gcc -ftest-coverage
# - GCC gcda - written by a program that was compiled with
#	gcc -fprofile-arcs
# - LLVM raw profiles - generated by a program compiled with
#	clang -fprofile-instr-generate -fcoverage-mapping ...
# - LLVM indexed profiles - generated by
#	llvm-profdata
# - GCOV reports, i.e. the annotated source code
# - LCOV trace files, i.e. aggregated GCC profiles
#
# GCC coverage tracefiles
# .gcno file are created during compile time,
# while data collected during runtime is stored in .gcda files
# cf. gcov-io.h
# https://gcc.gnu.org/onlinedocs/gcc-5.3.0/gcc/Gcov-Data-Files.html
# Examples:
# Fedora 23/x86-64/gcc-5.3.1: 6f 6e 63 67 52 33 30 35
# Debian 8 PPC64/gcc-4.9.2  : 67 63 6e 6f 34 30 39 2a
0	lelong	0x67636e6f	GCC gcno coverage (-ftest-coverage),
>&3	byte	x	version %c.
>&1	byte	x	\b%c

# big endian
0	belong	0x67636e6f	GCC gcno coverage (-ftest-coverage),
>&0	byte	x	version %c.
>&2	byte	x	\b%c (big-endian)

# Examples:
# Fedora 23/x86-64/gcc-5.3.1: 61 64 63 67 52 33 30 35
# Debian 8 PPC64/gcc-4.9.2  : 67 63 64 61 34 30 39 2a
0	lelong	0x67636461	GCC gcda coverage (-fprofile-arcs),
>&3	byte	x	version %c.
>&1	byte	x	\b%c

# big endian
0	belong	0x67636461	GCC gcda coverage (-fprofile-arcs),
>&0	byte	x	version %c.
>&2	byte	x	\b%c (big-endian)


# LCOV tracefiles
# cf. http://ltp.sourceforge.net/coverage/lcov/geninfo.1.php
0	string	TN:
>&0	search/64	\nSF:/	LCOV coverage tracefile


# Coverage reports generated by gcov
# i.e. source code annoted with coverage information
0	string	\x20\x20\x20\x20\x20\x20\x20\x20-:\x20\x20\x20\ 0:Source:
>&0	search/128	\x20\x20\x20\x20\x20\x20\x20\x20-:\x20\x20\x20\ 0:Graph:
>>&0	search/128	\x20\x20\x20\x20\x20\x20\x20\x20-:\x20\x20\x20\ 0:Data:	GCOV coverage report


# LLVM coverage files

# raw data after running a program compiled with:
# `clang -fprofile-instr-generate -fcoverage-mapping ...`
# default name: default.profraw
# magic is: \xFF lprofr \x81
# cf. https://llvm.org/docs/doxygen/html/InstrProfData_8inc_source.html
0	lequad	0xff6c70726f667281	LLVM raw profile data,
>&0	byte	x	version %d

# big endian
0	bequad	0xff6c70726f667281	LLVM raw profile data,
>&7	byte	x	version %d (big-endian)


# LLVM indexed instruction profile (as generated by llvm-profdata)
# magic is: reverse(\xFF lprofi \x81)
# cf. https://llvm.org/docs/CoverageMappingFormat.html
# https://llvm.org/docs/doxygen/html/namespacellvm_1_1IndexedInstrProf.html
# https://llvm.org/docs/CommandGuide/llvm-cov.html
# https://llvm.org/docs/CommandGuide/llvm-profdata.html
0	lequad	0x8169666f72706cff	LLVM indexed profile data,
>&0	byte	x	version %d

# big endian
0	bequad	0x8169666f72706cff	LLVM indexed profile data,
>&7	byte	x	version %d (big-endian)


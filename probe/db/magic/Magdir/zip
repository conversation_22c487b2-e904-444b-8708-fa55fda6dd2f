#------------------------------------------------------------------------------
# $File: zip,v 1.4 2020/03/03 13:46:52 christos Exp $
# zip:  file(1) magic for zip files; this is not use
# Note the version of magic in archive is currently stronger, this is
# just an example until negative offsets are supported better

# Zip Central Directory record
0	name		zipcd
>0	string		PK\001\002	Zip archive data
>>4	leshort		x		\b, made by
>>4	use		zipversion
>>4	use		ziphost
>>6	leshort		x		\b, extract using at least
>>6	use		zipversion
>>12	ledate		x		\b, last modified %s
>>24	lelong		>0		\b, uncompressed size %d
>>10	leshort		x		\b, method=
>>10	use		zipcompression

# URL:		https://en.wikipedia.org/wiki/Zip_(file_format)
# reference:	https://pkware.cachefly.net/webdocs/APPNOTE/APPNOTE-6.3.6.TXT
# Zip known compressions
0	name		zipcompression
>0	leshort		0		\bstore
>0	leshort		1		\bShrinking
>0	leshort		6		\bImploding
>0	leshort		7		\bTokenizing
>0	leshort		8		\bdeflate
>0	leshort		9		\bdeflate64
>0	leshort		10		\bLibrary imploding
#>0	leshort		11 		\bReserved by PKWARE
>0	leshort		12		\bbzip2
#>0	leshort		13 		\bReserved by PKWARE
>0	leshort		14		\blzma
#>0	leshort		15 		\bReserved by PKWARE
>0	leshort		16		\bCMPSC Compression
#>0	leshort		17 		\bReserved by PKWARE
>0	leshort		18		\bIBM TERSE
>0	leshort		19		\bIBM LZ77
# https://support.winzip.com/hc/en-us/articles/115012122828-Compression-method-used-for-this-file-is-94
>0	leshort		94		\bMP3
>0	leshort		95		\bxz
>0	leshort		96		\bJpeg
>0	leshort		97		\bWavPack
>0	leshort		98		\bPPMd
>0	leshort		99		\bAES Encrypted
>0	default		x
>>0	leshort		x		\b[%#x]

# Zip known versions
0	name		zipversion
# The lower byte indicates the ZIP version of this file. The value/10 indicates
# the major version number, and the value mod 10 is the minor version number.
>0	ubyte/10	x		v%u
>0	ubyte%10	x		\b.%u
# >0	leshort		0x09		v0.9
# >0	leshort		0x0a		v1.0
# >0	leshort		0x0b		v1.1
# >0	leshort		0x14		v2.0
# >0	leshort		0x15		v2.1
# >0	leshort		0x19		v2.5
# >0	leshort		0x1b		v2.7
# >0	leshort		0x2d		v4.5
# >0	leshort		0x2e		v4.6
# >0	leshort		0x32		v5.0
# >0	leshort		0x33		v5.1
# >0	leshort		0x34		v5.2
# >0	leshort		0x3d		v6.1
# >0	leshort		0x3e		v6.2
# >0	leshort		0x3f		v6.3
# >0	default		x
# >>0	leshort		x		v?[%#x]

#	display compatible host system name of ZIP archive
0	name		ziphost
# The upper byte indicates the compatibility of the file attribute information.
# If the file is compatible with MS-DOS (v 2.04g) then this value will be zero.
#>1	ubyte		0		DOS
>1	ubyte		1 		Amiga
>1	ubyte		2		OpenVMS
>1	ubyte		3		UNIX
>1	ubyte		4		VM/CMS
>1	ubyte		6		OS/2
>1	ubyte		7		Macintosh
>1	ubyte		11		MVS
>1	ubyte		13		Acorn Risc
>1	ubyte		16		BeOS
>1	ubyte		17		Tandem
# 9 untested
>1	ubyte		5		Atari ST
>1	ubyte		8		Z-System
>1	ubyte		9		CP/M
>1	ubyte		10		Windows NTFS
>1	ubyte		12		VSE
>1	ubyte		14		VFAT
>1	ubyte		15		alternate MVS
>1	ubyte		18		OS/400
>1	ubyte		19		OS X
# unused
#>1	ubyte		>19		unused 0x%x

# Zip End Of Central Directory record
-22	string		PK\005\006
#>4	leshort		>1		\b, %d disks
#>6	leshort		>1		\b, central directory disk %d
#>8	leshort		>1		\b, %d central directories on this disk
#>10	leshort		>1		\b, %d central directories
#>12	lelong		x		\b, %d central directory bytes
>(16.l)	use		zipcd
>>20	pstring/l	>0		\b, %s

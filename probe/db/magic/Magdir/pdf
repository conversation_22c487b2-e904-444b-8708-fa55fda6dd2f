
#------------------------------------------------------------------------------
# $File: pdf,v 1.12 2020/01/30 01:48:44 christos Exp $
# pdf:  file(1) magic for Portable Document Format
#

0	name	pdf
>8	search/512	/Filter/FlateDecode/	(password protected)

0	string		%PDF-		PDF document
!:mime	application/pdf
!:strength +60
>5	byte		x		\b, version %c
>7	byte		x		\b.%c
>0	use		pdf

0	string		\012%PDF-	PDF document
!:mime	application/pdf
!:strength +60
>6	byte		x		\b, version %c
>8	byte		x		\b.%c
>0	use		pdf

0	string		\xef\xbb\xbf%PDF-	PDF document (UTF-8)
!:mime	application/pdf
!:strength +60
>6	byte		x		\b, version %c
>8	byte		x		\b.%c
>0	use		pdf

# From: <PERSON> <nick@schmal<PERSON>er.us>
# Forms Data Format
0       string          %FDF-           FDF document
!:mime application/vnd.fdf
!:strength +60
>5      byte            x               \b, version %c
>7      byte            x               \b.%c

0	search/256	%PDF-		PDF document
!:mime	application/pdf
!:strength +60
>&0	byte		x		\b, version %c
>&2	byte		x		\b.%c
>0	use		pdf

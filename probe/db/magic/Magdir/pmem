
#------------------------------------------------------------------------------
# $File: pmem,v 1.3 2019/06/13 11:45:44 christos Exp $
# pmem: file(1) magic for Persistent Memory Development Kit pool files
#
0	string		PMEM
>4	string		POOLSET		Persistent Memory Poolset file
>>11	search		REPLICA		with replica
>4	regex		LOG|BLK|OBJ	Persistent Memory Pool file, type: %s,
>>8	lelong		>0		version: 0x%x,
>>12	lelong		x		compat: 0x%x,
>>16	lelong		x		incompat: 0x%x,
>>20	lelong		x		ro_compat: 0x%x,


>>120	leqldate	x		crtime: %s,
>>128	lequad		x		alignment_desc: 0x%016llx,

>>136	clear		x
>>136	byte		2		machine_class: 64-bit,
>>136	default		x		machine_class: unknown
>>>136	byte		x		(0x%d),

>>137	clear		x
>>137	byte		1		data: little-endian,
>>137	byte		2		data: big-endian,
>>137	default		x		data: unknown
>>>137	byte		x		(0x%d),

>>138	byte		!0		reserved[0]: %d,
>>139	byte		!0		reserved[1]: %d,
>>140	byte		!0		reserved[2]: %d,
>>141	byte		!0		reserved[3]: %d,

>>142	clear		x
>>142	leshort		62		machine: x86_64
>>142	leshort		183		machine: aarch64
>>142	default		x		machine: unknown
>>>142	leshort		x		(0x%d)

>4	string		BLK
>>4096	lelong		x		\b, blk.bsize: %d

>4	string		OBJ
>>4096	string		>0		\b, obj.layout: '%s'
>>4096	string		<0		\b, obj.layout: NULL

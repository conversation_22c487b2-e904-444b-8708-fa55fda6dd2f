# Type:	OpenSSH key files
# From:	<PERSON> <<EMAIL>>

0	string	SSH\ PRIVATE\ KEY	OpenSSH RSA1 private key,
>28	string	>\0			version %s
0	string	-----BEGIN\ OPENSSH\ PRIVATE\ KEY-----	OpenSSH private key

0	string	ssh-dss\ 		OpenSSH DSA public key
0	string	ssh-rsa\ 		OpenSSH RSA public key
0	string	ecdsa-sha2-nistp256	OpenSSH ECDSA public key
0	string	ecdsa-sha2-nistp384	OpenSSH ECDSA public key
0	string	ecdsa-sha2-nistp521	OpenSSH ECDSA public key
0	string	ssh-ed25519		OpenSSH ED25519 public key

0	string	SSHKRL\n\0
>8	ubelong	1		OpenSSH key/certificate revocation list, format %u
>>12	ubequad	x		\b, version %llx
>>>20	beqdate	x		\b, generated %s

# From:		Joe<PERSON>
# URL:		https://en.wikipedia.org/wiki/PuTTY
# Reference:	https://the.earth.li/~sgtatham/putty/latest/putty-0.73.tar.gz
#		/sshpubk.c
0	string		PuTTY-User-Key-File-	PuTTY Private Key File
#!:mime	text/plain
# https://github.com/github/putty/blob/master/windows/installer.wxs
!:mime	application/x-putty-private-key
!:ext	ppk
# version 1 or 2
>20	string		x			\b, version %.1s
# name of the algorithm like: ssh-dss ssh-rsa ecdsa-sha2-nistp256 ssh-ed25519
>23	string		x			\b, algorithm %s
# next line says "Encryption: " plus an encryption type like aes256-cbc or none
>32	search/13	Encryption:\040		\b, Encryption
>>&0	string		x			%s
# next line says "Comment: " plus the comment string
>>>&0	search/3	Comment:\040
>>>>&0	string		x			"%s"


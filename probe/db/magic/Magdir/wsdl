
#------------------------------------------------------------------------------
# $File: wsdl,v 1.5 2019/04/19 00:42:27 christos Exp $
# wsdl: PHP WSDL Cache, https://www.php.net/manual/en/book.soap.php
# Cache format extracted from source:
# https://svn.php.net/viewvc/php/php-src/trunk/ext/soap/php_sdl.c?revision=HEAD&view=markup
# Requires file >= 5.05
# By <PERSON><PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON> <<EMAIL>>, 2010-2011
0		string		wsdl		PHP WSDL cache,
>4		byte		x		version 0x%02x
>6		ledate		x		\b, created %s

# uri
>10		lelong		<0x7fffffff
>>10		pstring/l	x		\b, uri: "%s"

# source
>>>&0		lelong		<0x7fffffff
>>>>&-4		pstring/l	x		\b, source: "%s"

# target_ns
>>>>>&0		lelong		<0x7fffffff
>>>>>>&-4	pstring/l	x		\b, target_ns: "%s"

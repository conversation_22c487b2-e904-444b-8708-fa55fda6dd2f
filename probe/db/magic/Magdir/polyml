
#------------------------------------------------------------------------------
# $File: polyml,v 1.2 2019/04/19 00:42:27 christos Exp $
# polyml:  file(1) magic for PolyML
#
# PolyML
# MPEG, FLI, DL <NAME_EMAIL> (VaX#n8)
# FLC, SGI, Apple originally from <PERSON> (<EMAIL>)

# [0]: https://www.polyml.org/
# [1]: https://github.com/polyml/polyml/blob/master/\
#	libpolyml/savestate.cpp#L146-L147
# [2]: https://github.com/polyml/polyml/blob/master/\
#	libpolyml/savestate.cpp#L1262-L1263

# Type: Poly/ML saved data
# From: <PERSON> <<EMAIL>>

0	string	POLYSAVE	Poly/ML saved state
>8	long	x		version %u

0	string  POLYMODU	Poly/ML saved module
>8	long	x		version %u


#------------------------------------------------------------------------------
# $File: uuencode,v 1.8 2019/12/14 20:40:26 christos Exp $
# uuencode:  file(1) magic for ASCII-encoded files
#

# GRR:  the first line of xxencoded files is identical to that in uuencoded
# files, but the first character in most subsequent lines is 'h' instead of
# 'M'.  (xxencoding uses lowercase letters in place of most of uuencode's
# punctuation and survives BITNET gateways better.)  If regular expressions
# were supported, this entry could possibly be split into two with
# "begin\040\.\*\012M" or "begin\040\.\*\012h" (where \. and \* are REs).
0	search/1	begin\ 		uuencoded or xxencoded text

# btoa(1) is an alternative to uuencode that requires less space.
0	search/1	xbtoa\ Begin	btoa'd text

# ship(1) is another, much cooler alternative to uuencode.
# <PERSON>, <EMAIL>
0	search/1	$\012ship	ship'd binary text

# bencode(8) is used to encode compressed news batches (Bnews/Cnews only?)
# <PERSON>lofs, <EMAIL>
0	search/1	Decode\ the\ following\ with\ bdeco	bencoded News text

# GRR: handle BASE64

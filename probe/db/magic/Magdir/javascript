
#------------------------------------------------------------------------------
# $File: javascript,v 1.2 2019/08/05 10:34:26 christos Exp $
# javascript:  magic for javascript and node.js scripts.
#
0	search/1/w	#!/bin/node		Node.js script text executable
!:mime application/javascript
0	search/1/w	#!/usr/bin/node		Node.js script text executable
!:mime application/javascript
0	search/1/w	#!/bin/nodejs		Node.js script text executable
!:mime application/javascript
0	search/1/w	#!/usr/bin/nodejs	Node.js script text executable
!:mime application/javascript
0	search/1	#!/usr/bin/env\ node	Node.js script text executable
!:mime application/javascript
0	search/1	#!/usr/bin/env\ nodejs	Node.js script text executable
!:mime application/javascript
# Hermes by Facebook https://hermesengine.dev/
# https://github.com/facebook/hermes/blob/master/include/hermes/\
# BCGen/HBC/BytecodeFileFormat.h#L24
0	lequad		0x1F1903C103BC1FC6	Hermes JavaScript bytecode
>8	lelong		x			\b, version %d

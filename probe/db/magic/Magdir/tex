
#------------------------------------------------------------------------------
# $File: tex,v 1.21 2019/04/19 00:42:27 christos Exp $
# tex:  file(1) magic for TeX files
#
# XXX - needs byte-endian stuff (big-endian and little-endian DVI?)
#
# <AUTHOR> <EMAIL>

# Although we may know the offset of certain text fields in TeX DVI
# and font files, we can't use them reliably because they are not
# zero terminated. [but we do anyway, christos]
0	string		\367\002	TeX DVI file
!:mime	application/x-dvi
>16	string		>\0		(%s)
0	string		\367\203	TeX generic font data
0	string		\367\131	TeX packed font data
>3	string		>\0		(%s)
0	string		\367\312	TeX virtual font data
0	search/1	This\ is\ TeX,	TeX transcript text
0	search/1	This\ is\ METAFONT,	METAFONT transcript text

# There is no way to detect TeX Font Metric (*.tfm) files without
# breaking them apart and reading the data.  The following patterns
# match most *.tfm files generated by METAFONT or afm2tfm.
2	string		\000\021	TeX font metric data
!:mime	application/x-tex-tfm
>33	string		>\0		(%s)
2	string		\000\022	TeX font metric data
!:mime	application/x-tex-tfm
>33	string		>\0		(%s)

# Texinfo and GNU Info, from Daniel Quinlan (<EMAIL>)
0	search/1	\\input\ texinfo	Texinfo source text
!:mime	text/x-texinfo
0	search/1	This\ is\ Info\ file	GNU Info text
!:mime	text/x-info

# TeX documents, from Daniel Quinlan (<EMAIL>)
0	search/4096	\\input		TeX document text
!:mime	text/x-tex
!:strength + 15
0	search/4096	\\begin		LaTeX document text
!:mime	text/x-tex
!:strength + 15
0	search/4096	\\section	LaTeX document text
!:mime	text/x-tex
!:strength + 18
0	search/4096	\\setlength	LaTeX document text
!:mime	text/x-tex
!:strength + 15
0	search/4096	\\documentstyle	LaTeX document text
!:mime	text/x-tex
!:strength + 18
0	search/4096	\\chapter	LaTeX document text
!:mime	text/x-tex
!:strength + 18
0	search/4096	\\documentclass	LaTeX 2e document text
!:mime	text/x-tex
!:strength + 15
0	search/4096	\\relax		LaTeX auxiliary file
!:mime	text/x-tex
!:strength + 15
0	search/4096	\\contentsline	LaTeX table of contents
!:mime	text/x-tex
!:strength + 15
0	search/4096	%\ -*-latex-*-	LaTeX document text
!:mime	text/x-tex

# <AUTHOR> <EMAIL>
0   	search/1	\\ifx		TeX document text

# Index and glossary files
0	search/4096	\\indexentry	LaTeX raw index file
0	search/4096	\\begin{theindex}	LaTeX sorted index
0	search/4096	\\glossaryentry	LaTeX raw glossary
0	search/4096	\\begin{theglossary}	LaTeX sorted glossary
0	search/4096	This\ is\ makeindex	Makeindex log file

# End of TeX

#------------------------------------------------------------------------------
# file(1) magic for BibTex text files
# <AUTHOR> <EMAIL>

0	search/1/c	@article{	BibTeX text file
0	search/1/c	@book{		BibTeX text file
0	search/1/c	@inbook{	BibTeX text file
0	search/1/c	@incollection{	BibTeX text file
0	search/1/c	@inproceedings{	BibTeX text file
0	search/1/c	@manual{	BibTeX text file
0	search/1/c	@misc{		BibTeX text file
0	search/1/c	@preamble{	BibTeX text file
0	search/1/c	@phdthesis{	BibTeX text file
0	search/1/c	@techreport{	BibTeX text file
0	search/1/c	@unpublished{	BibTeX text file

73	search/1	%%%\ \ 		BibTeX-file{ BibTex text file (with full header)

73	search/1	%%%\ \ @BibTeX-style-file{   BibTeX style text file (with full header)

0	search/1	%\ BibTeX\ standard\ bibliography\ 	BibTeX standard bibliography style text file

0	search/1	%\ BibTeX\ `	BibTeX custom bibliography style text file

0	search/1	@c\ @mapfile{	TeX font aliases text file

0	string		#LyX		LyX document text

# ConTeXt documents
#	https://wiki.contextgarden.net/
0	search/4096	\\setupcolors[		ConTeXt document text
!:strength + 15
0	search/4096	\\definecolor[		ConTeXt document text
!:strength + 15
0	search/4096	\\setupinteraction[	ConTeXt document text
!:strength + 15
0	search/4096	\\useURL[		ConTeXt document text
!:strength + 15
0	search/4096	\\setuppapersize[	ConTeXt document text
!:strength + 15
0	search/4096	\\setuplayout[		ConTeXt document text
!:strength + 15
0	search/4096	\\setupfooter[		ConTeXt document text
!:strength + 15
0	search/4096	\\setupfootertexts[	ConTeXt document text
!:strength + 15
0	search/4096	\\setuppagenumbering[	ConTeXt document text
!:strength + 15
0	search/4096	\\setupbodyfont[	ConTeXt document text
!:strength + 15
0	search/4096	\\setuphead[		ConTeXt document text
!:strength + 15
0	search/4096	\\setupitemize[		ConTeXt document text
!:strength + 15
0	search/4096	\\setupwhitespace[	ConTeXt document text
!:strength + 15
0	search/4096	\\setupindenting[	ConTeXt document text
!:strength + 15

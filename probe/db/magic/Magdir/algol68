
#------------------------------------------------------------------------------
# $File: algol68,v 1.3 2018/10/19 01:04:21 christos Exp $
# algol68:  file(1) magic for Algol 68 source
#
0	search/8192	(input,			Algol 68 source text
!:mime	text/x-Algol68
0	regex/1024	\^PROC			Algol 68 source text
!:mime	text/x-Algol68
0	regex/1024	\bMODE[\t\ ]		Algol 68 source text
!:mime	text/x-Algol68
0	regex/1024	\bREF[\t\ ]		Algol 68 source text
!:mime	text/x-Algol68
0	regex/1024	\bFLEX[\t\ ]\*\\[	Algol 68 source text
!:mime	text/x-Algol68
#0	regex          	[\t\ ]OD		Algol 68 source text
#!:mime	text/x-Algol68
#0	regex          	[\t\ ]FI		Algol 68 source text
#!:mime	text/x-Algol68

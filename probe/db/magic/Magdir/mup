
# ------------------------------------------------------------------------
# $File: mup,v 1.5 2017/03/17 21:35:28 christos Exp $
# mup: file(1) magic for <PERSON><PERSON> (Music Publisher) input file.
#
# From: <PERSON> <abel (@) oaka.org>
#
# NOTE: This header is mainly proposed in the Arkkra mailing list,
# and is not a mandatory header because of old mup input file
# compatibility. Noteedit also use mup format, but is not forcing
# user to use any header as well.
#
0		search/1	//!Mup		Mup music publication program input text
>6		string		-Arkkra		(Arkkra)
>>13		string		-
>>>16		string		.
>>>>14		string		x		\b, need V%.4s
>>>15		string		.
>>>>14		string		x		\b, need V%.3s
>6		string		-
>>9		string		.
>>>7		string		x		\b, need V%.4s
>>8		string		.
>>>7		string		x		\b, need V%.3s

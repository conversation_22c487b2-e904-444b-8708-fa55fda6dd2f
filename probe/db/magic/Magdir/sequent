
#------------------------------------------------------------------------------
# $File: sequent,v 1.14 2019/04/19 00:42:27 christos Exp $
# sequent:  file(1) magic for Sequent machines
#
# Sequent information updated by <PERSON> <atsun!dwiggins>.
# For Sequent's multiprocessor systems (incomplete).
0	lelong	0x00ea        	BALANCE NS32000 .o
>16	lelong	>0		not stripped
>124	lelong	>0		version %d
0	lelong	0x10ea        	BALANCE NS32000 executable (0 @ 0)
>16	lelong  >0            	not stripped
>124	lelong	>0		version %d
0	lelong	0x20ea        	BALANCE NS32000 executable (invalid @ 0)
>16	lelong  >0            	not stripped
>124	lelong	>0		version %d
0	lelong	0x30ea        	BALANCE NS32000 standalone executable
>16	lelong  >0          	not stripped
>124	lelong	>0		version %d
#
# Symmetry information added by <PERSON> <<EMAIL>>.
# Symmetry magic nums will not be reached if DOS COM comes before them;
# byte 0xeb is matched before these get a chance.
0	leshort	0x12eb		SYMMETRY i386 .o
>16	lelong	>0		not stripped
>124	lelong	>0		version %d
0	leshort	0x22eb		SYMMETRY i386 executable (0 @ 0)
>16	lelong	>0		not stripped
>124	lelong	>0		version %d
0	leshort	0x32eb		SYMMETRY i386 executable (invalid @ 0)
>16	lelong	>0		not stripped
>124	lelong	>0		version %d
# https://en.wikipedia.org/wiki/Sequent_Computer_Systems
# below test line conflicts with MS-DOS 2.11 floppies and Acronis loader
#0	leshort	0x42eb		SYMMETRY i386 standalone executable
0	leshort	0x42eb
# skip unlike negative version
>124	lelong	>-1
# assuming version 28867614 is very low probable
>>124	lelong	!28867614	SYMMETRY i386 standalone executable
>>>16	lelong	>0		not stripped
>>>124	lelong	>0		version %d


#------------------------------------------------------------------------------
# $File: images,v 1.181 2020/05/30 23:49:03 christos Exp $
# images:  file(1) magic for image formats (see also "iff", and "c-lang" for
# XPM bitmaps)
#
# <NAME_EMAIL> (<PERSON><PERSON>),
# <NAME_EMAIL> as well as others. <PERSON> also suggested
# merging several one- and two-line files into here.
#
# little magic: PCX (first byte is 0x0a)

# Targa - matches `povray', `ppmtotga' and `xv' outputs
# by <PERSON> <<EMAIL>>
# URL: http://justsolve.archiveteam.org/wiki/TGA
# Reference: http://www.dca.fee.unicamp.br/~martino/disciplinas/ea978/tgaffs.pdf
# Update: <PERSON><PERSON>
# at 2, byte ImgType must be 1, 2, 3, 9, 10 or 11
#	,32 or 33 (both not observed)
# at 1, byte CoMapType must be 1 if ImgType is 1 or 9, 0 otherwise
#	or theoretically 2-128 reserved for use by Truevision or 128-255 may be used for developer applications
# at 3, leshort Index is 0 for povray, ppmtotga and xv outputs
# `xv' recognizes only a subset of the following (RGB with pixelsize = 24)
# `tgatoppm' recognizes a superset (Index may be anything)
#
# test of Color Map Type 0~no 1~color map
# and Image Type 1 2 3 9 10 11 32 33
# and Color Map Entry Size 0 15 16 24 32
0	ubequad&0x00FeC400000000C0	0
# Conflict with MPEG sequences.
!:strength -40
# Prevent conflicts with CRI ADX.
>(2.S-2) belong	!0x28632943
# skip more garbage like *.iso by looking for positive image type
>>2	ubyte			>0
# skip some compiled terminfo like xterm+tmux by looking for image type less equal 33
>>>2	ubyte			<34
# skip arches.3200 , Finder.Root , Slp.1 by looking for low pixel depth 1 8 15 16 24 32
>>>>16	ubyte			1
>>>>>0		use		tga-image
>>>>16	ubyte			8
>>>>>0		use		tga-image
>>>>16	ubyte			15
>>>>>0		use		tga-image
>>>>16	ubyte			16
>>>>>0		use		tga-image
>>>>16	ubyte			24
>>>>>0		use		tga-image
>>>>16	ubyte			32
>>>>>0		use		tga-image
#	display tga bitmap image information
0	name				tga-image
>2	ubyte		<34		Targa image data
!:mime	image/x-tga
!:apple	????TPIC
# normal extension .tga but some Truevision products used others:
# tpic (Apple),icb (Image Capture Board),vda (Video Display Adapter),vst (NuVista),win (UNSURE about that)
!:ext	tga/tpic/icb/vda/vst
# image type 1 2 3 9 10 11 32 33
>2	ubyte&0xF7	1		- Map
>2	ubyte&0xF7	2		- RGB
# alpha channel
>>17	ubyte&0x0F	>0		\bA
>2	ubyte&0xF7	3		- Mono
# type not found, but by http://www.fileformat.info/format/tga/corion.htm
# Compressed color-mapped data, using Huffman, Delta, and runlength encoding
>2	ubyte		32		- Color
# Compressed color-mapped data, using Huffman, Delta, and RLE. 4-pass quadtree- type process
>2	ubyte		33		- Color
# Color Map Type 0~no 1~color map
>1	ubyte		1		(
# first color map entry, 0 normal
>>3	uleshort	>0		\b%d-
# color map length 0 2 1dh 3bh d9h 100h
>>5	uleshort	x		\b%d)
# 8~run length encoding bit
>2	ubyte&0x08	8		- RLE
# gimp can create big pictures!
>12	uleshort	>0		%d x
>12	uleshort	=0		65536 x
# image height. 0 interpreted as 65536
>14	uleshort	>0		%d
>14	uleshort	=0		65536
# Image Pixel depth 1 8 15 16 24 32
>16	ubyte		x		x %d
# X origin of image. 0 normal
>8	uleshort	>0		+%d
# Y origin of image. 0 normal; positive for top
>10	uleshort	>0		+%d
# Image descriptor: bits 3-0 give the alpha channel depth, bits 5-4 give direction
>17	ubyte&0x0F	>0		- %d-bit alpha
# bits 5-4 give direction. normal bottom left
>17	ubyte		&0x20		- top
#>17	ubyte		^0x20		- bottom
>17	ubyte		&0x10		- right
#>17	ubyte		^0x10		- left
# some info say other bits 6-7 should be zero
# but data storage interleave by http://www.fileformat.info/format/tga/corion.htm
# 00 - no interleave;01 - even/odd interleave; 10 - four way interleave; 11 - reserved
#>17	ubyte&0xC0	0x00		- no interleave
>17	ubyte&0xC0	0x40		- interleave
>17	ubyte&0xC0	0x80		- four way interleave
>17	ubyte&0xC0	0xC0		- reserved
# positive length implies identification field
>0	ubyte		>0
>>18	string		x		"%s"
# last 18 bytes of newer tga file footer signature
>18	search/4261301/s	TRUEVISION-XFILE.\0
# extension area offset if not 0
>>&-8		ulelong			>0
# length of the extension area. normal 495 for version 2.0
>>>(&-4.l)	uleshort		0x01EF
# AuthorName[41]
>>>>&0		string			>\0		- author "%-.40s"
# Comment[324]=4 * 80 null terminated
>>>>&41		string			>\0		- comment "%-.80s"
# date
>>>>&365	ubequad&0xffffFFFFffff0000	!0
# Day
>>>>>&-6		uleshort		x		%d
# Month
>>>>>&-8		uleshort		x		\b-%d
# Year
>>>>>&-4		uleshort		x		\b-%d
# time
>>>>&371	ubequad&0xffffFFFFffff0000	!0
# hour
>>>>>&-8		uleshort		x		%d
# minutes
>>>>>&-6		uleshort		x		\b:%.2d
# second
>>>>>&-4		uleshort		x		\b:%.2d
# JobName[41]
>>>>&377		string			>\0		- job "%-.40s"
# JobHour Jobminute Jobsecond
>>>>&418	ubequad&0xffffFFFFffff0000	!0
>>>>>&-8		uleshort		x		%d
>>>>>&-6		uleshort		x		\b:%.2d
>>>>>&-4		uleshort		x		\b:%.2d
# SoftwareId[41]
>>>>&424		string			>\0		- %-.40s
# SoftwareVersionNumber
>>>>&424	ubyte				>0
>>>>>&40		uleshort/100		x		%d
>>>>>&40		uleshort%100		x		\b.%d
# VersionLetter
>>>>>&42		ubyte			>0x20		\b%c
# KeyColor
>>>>&468		ulelong			>0		- keycolor 0x%8.8x
# Denominator of Pixel ratio. 0~no pixel aspect
>>>>&474	uleshort			>0
# Numerator
>>>>>&-4		uleshort		>0		- aspect %d
>>>>>&-2		uleshort		x		\b/%d
# Denominator of Gamma ratio. 0~no Gamma value
>>>>&478	uleshort			>0
# Numerator
>>>>>&-4		uleshort		>0		- gamma %d
>>>>>&-2		uleshort		x		\b/%d
# ColorOffset
#>>>>&480	ulelong			x		- col offset 0x%8.8x
# StampOffset
#>>>>&484	ulelong			x		- stamp offset 0x%8.8x
# ScanOffset
#>>>>&488	ulelong			x		- scan offset 0x%8.8x
# AttributesType
#>>>>&492	ubyte			x		- Attributes 0x%x
## EndOfTGA

# PBMPLUS images
# The next byte following the magic is always whitespace.
# strength is changed to try these patterns before "x86 boot sector"
0	name		netpbm
>3	regex/s		=[0-9]{1,50}\ [0-9]{1,50}	Netpbm image data
>>&0	regex		=[0-9]{1,50} 			\b, size = %s x
>>>&0	regex		=[0-9]{1,50}			\b %s

0	search/1	P1
>0	regex/4		P1[\040\t\f\r\n]
>>0	use		netpbm
>>0	string		x	\b, bitmap
!:strength + 65
!:mime	image/x-portable-bitmap

0	search/1	P2
>0	regex/4		P2[\040\t\f\r\n]
>>0	use		netpbm
>>0	string		x	\b, greymap
!:strength + 65
!:mime	image/x-portable-greymap

0	search/1	P3
>0	regex/4		P3[\040\t\f\r\n]
>>0	use		netpbm
>>0	string		x	\b, pixmap
!:strength + 65
!:mime	image/x-portable-pixmap

0	string		P4
>0	regex/4		P4[\040\t\f\r\n]
>>0	use		netpbm
>>0	string		x	\b, rawbits, bitmap
!:strength + 65
!:mime	image/x-portable-bitmap

0	string		P5
>0	regex/4		P5[\040\t\f\r\n]
>>0	use		netpbm
>>0	string		x	\b, rawbits, greymap
!:strength + 65
!:mime	image/x-portable-greymap

0	string		P6
>0	regex/4		P6[\040\t\f\r\n]
>>0	use		netpbm
>>0	string		x	\b, rawbits, pixmap
!:strength + 65
!:mime	image/x-portable-pixmap

0	string		P7		Netpbm PAM image file
!:mime	image/x-portable-pixmap

# From: <EMAIL> (Bryan Henderson)
0	string		\117\072	Solitaire Image Recorder format
>4	string		\013		MGI Type 11
>4	string		\021		MGI Type 17
0	string		.MDA		MicroDesign data
>21	byte		48		version 2
>21	byte		51		version 3
0	string		.MDP		MicroDesign page data
>21	byte		48		version 2
>21	byte		51		version 3

# NIFF (Navy Interchange File Format, a modification of TIFF) images
# [GRR:  this *must* go before TIFF]
0	string		IIN1		NIFF image data
!:mime	image/x-niff

# Canon RAW version 1 (CRW) files are a type of Canon Image File Format
# (CIFF) file. These are apparently all little-endian.
# <AUTHOR> <EMAIL>
# URL: https://www.sno.phy.queensu.ca/~phil/exiftool/canon_raw.html
0	string		II\x1a\0\0\0HEAPCCDR	Canon CIFF raw image data
!:mime	image/x-canon-crw
>16	leshort		x	\b, version %d.
>14	leshort		x	\b%d

# Canon RAW version 2 (CR2) files are a kind of TIFF with an extra magic
# number. Put this above the TIFF test to make sure we detect them.
# These are apparently all little-endian.
# <AUTHOR> <EMAIL>
# URL: https://libopenraw.freedesktop.org/wiki/Canon_CR2
0	string		II\x2a\0\x10\0\0\0CR	Canon CR2 raw image data
!:mime	image/x-canon-cr2
!:strength +80
>10	byte		x	\b, version %d.
>11	byte		x	\b%d

# Tag Image File Format, from Daniel Quinlan (<EMAIL>)
# The second word of TIFF files is the TIFF version number, 42, which has
# never changed.  The TIFF specification recommends testing for it.
0	string		MM\x00\x2a	TIFF image data, big-endian
!:strength +70
!:mime	image/tiff
>(4.L)	use		\^tiff_ifd
0	string		II\x2a\x00	TIFF image data, little-endian
!:mime	image/tiff
!:strength +70
>(4.l)	use		tiff_ifd

0	name		tiff_ifd
>0	leshort		x		\b, direntries=%d
>2	use		tiff_entry

0	name		tiff_entry
# NewSubFileType
>0	leshort		0xfe
>>12	use		tiff_entry
>0	leshort		0x100
>>4	lelong		1
>>>12	use		tiff_entry
>>>8	leshort		x		\b, width=%d
>0	leshort		0x101
>>4	lelong		1
>>>8	leshort		x		\b, height=%d
>>>12	use		tiff_entry
>0	leshort		0x102
>>8	leshort		x		\b, bps=%d
>>12	use		tiff_entry
>0	leshort		0x103
>>4	lelong		1		\b, compression=
>>>8	leshort		1		\bnone
>>>8	leshort		2		\bhuffman
>>>8	leshort		3		\bbi-level group 3
>>>8	leshort		4		\bbi-level group 4
>>>8	leshort		5		\bLZW
>>>8	leshort		6		\bJPEG (old)
>>>8	leshort		7		\bJPEG
>>>8	leshort		8		\bdeflate
>>>8	leshort		9		\bJBIG, ITU-T T.85
>>>8	leshort		0xa		\bJBIG, ITU-T T.43
>>>8	leshort		0x7ffe		\bNeXT RLE 2-bit
>>>8	leshort		0x8005		\bPackBits (Macintosh RLE)
>>>8	leshort		0x8029		\bThunderscan RLE
>>>8	leshort		0x807f		\bRasterPadding (CT or MP)
>>>8	leshort		0x8080		\bRLE (Line Work)
>>>8	leshort		0x8081		\bRLE (High-Res Cont-Tone)
>>>8	leshort		0x8082		\bRLE (Binary Line Work)
>>>8	leshort		0x80b2		\bDeflate (PKZIP)
>>>8	leshort		0x80b3		\bKodak DCS
>>>8	leshort		0x8765		\bJBIG
>>>8	leshort		0x8798		\bJPEG2000
>>>8	leshort		0x8799		\bNikon NEF Compressed
>>>8	default		x
>>>>8	leshort		x		\b(unknown 0x%x)
>>>12	use		tiff_entry
>0	leshort		0x106		\b, PhotometricIntepretation=
>>8	clear		x
>>8	leshort		0		\bWhiteIsZero
>>8	leshort		1		\bBlackIsZero
>>8	leshort		2		\bRGB
>>8	leshort		3		\bRGB Palette
>>8	leshort		4		\bTransparency Mask
>>8	leshort		5		\bCMYK
>>8	leshort		6		\bYCbCr
>>8	leshort		8		\bCIELab
>>8	default		x
>>>8	leshort		x		\b(unknown=0x%x)
>>12	use		tiff_entry
# FillOrder
>0	leshort		0x10a
>>4	lelong		1
>>>12	use		tiff_entry
# DocumentName
>0	leshort		0x10d
>>(8.l)	string		x		\b, name=%s
>>>12	use		tiff_entry
# ImageDescription
>0	leshort		0x10e
>>(8.l)	string		x		\b, description=%s
>>>12	use		tiff_entry
# Make
>0	leshort		0x10f
>>(8.l)	string		x		\b, manufacturer=%s
>>>12	use		tiff_entry
# Model
>0	leshort		0x110
>>(8.l)	string		x		\b, model=%s
>>>12	use		tiff_entry
# StripOffsets
>0	leshort		0x111
>>12	use		tiff_entry
# Orientation
>0	leshort		0x112		\b, orientation=
>>8	leshort		1		\bupper-left
>>8	leshort		3		\blower-right
>>8	leshort		6		\bupper-right
>>8	leshort		8		\blower-left
>>8	leshort		9		\bundefined
>>8	default		x
>>>8	leshort		x		\b[*%d*]
>>12	use		tiff_entry
# XResolution
>0	leshort		0x11a
>>8	lelong		x		\b, xresolution=%d
>>12	use		tiff_entry
# YResolution
>0	leshort		0x11b
>>8	lelong		x		\b, yresolution=%d
>>12	use		tiff_entry
# ResolutionUnit
>0	leshort		0x128
>>8	leshort		x		\b, resolutionunit=%d
>>12	use		tiff_entry
# Software
>0	leshort		0x131
>>(8.l)	string		x		\b, software=%s
>>12	use		tiff_entry
# Datetime
>0	leshort		0x132
>>(8.l)	string		x		\b, datetime=%s
>>12	use		tiff_entry
# HostComputer
>0	leshort		0x13c
>>(8.l)	string		x		\b, hostcomputer=%s
>>12	use		tiff_entry
# WhitePoint
>0	leshort		0x13e
>>12	use		tiff_entry
# PrimaryChromaticities
>0	leshort		0x13f
>>12	use		tiff_entry
# YCbCrCoefficients
>0	leshort		0x211
>>12	use		tiff_entry
# YCbCrPositioning
>0	leshort		0x213
>>12	use		tiff_entry
# ReferenceBlackWhite
>0	leshort		0x214
>>12	use		tiff_entry
# Copyright
>0	leshort		0x8298
>>(8.l)	string		x		\b, copyright=%s
>>12	use		tiff_entry
# ExifOffset
>0	leshort		0x8769
>>12	use		tiff_entry
# GPS IFD
>0	leshort		0x8825		\b, GPS-Data
>>12	use		tiff_entry

#>0	leshort		x		\b, unknown=0x%x
#>>12	use		tiff_entry

0	string		MM\x00\x2b	Big TIFF image data, big-endian
!:mime	image/tiff
0	string		II\x2b\x00	Big TIFF image data, little-endian
!:mime	image/tiff

# PNG [Portable Network Graphics, or "PNG's Not GIF"] images
# (Greg Roelofs, <EMAIL>)
# (Albert Cahalan, <EMAIL>)
#
# 137 P N G \r \n ^Z \n [4-byte length] I H D R [HEAD data] [HEAD crc] ...
#

# IHDR parser
0	name		png-ihdr
>0	belong		x		\b, %d x
>4	belong		x		%d,
>8	byte		x		%d-bit
>9	byte		0		grayscale,
>9	byte		2		\b/color RGB,
>9	byte		3		colormap,
>9	byte		4		gray+alpha,
>9	byte		6		\b/color RGBA,
#>10	byte		0		deflate/32K,
>12	byte		0		non-interlaced
>12	byte		1		interlaced

# Standard PNG image.
0	string		\x89PNG\x0d\x0a\x1a\x0a\x00\x00\x00\x0DIHDR	PNG image data
!:mime	image/png
!:ext   png
!:strength +10
>16	use		png-ihdr

# Apple CgBI PNG image.
0	string		\x89PNG\x0d\x0a\x1a\x0a\x00\x00\x00\x04CgBI
>24	string  	\x00\x00\x00\x0DIHDR	PNG image data (CgBI)
!:mime	image/png
!:ext   png
!:strength +10
>>32	use		png-ihdr

# possible GIF replacements; none yet released!
# (Greg Roelofs, <EMAIL>)
#
# GRR 950115:  this was mine ("Zip GIF"):
0	string		GIF94z		ZIF image (GIF+deflate alpha)
!:mime	image/x-unknown
#
# GRR 950115:  this is Jeremy Wohl's Free Graphics Format (better):
#
0	string		FGF95a		FGF image (GIF+deflate beta)
!:mime	image/x-unknown
#
# GRR 950115:  this is Thomas Boutell's Portable Bitmap Format proposal
# (best; not yet implemented):
#
0	string		PBF		PBF image (deflate compression)
!:mime	image/x-unknown

# GIF
# Strength set up to beat 0x55AA DOS/MBR signature word lookups (+65)
0	string		GIF8		GIF image data
!:strength +80
!:mime	image/gif
!:apple	8BIMGIFf
>4	string		7a		\b, version 8%s,
>4	string		9a		\b, version 8%s,
>6	leshort		>0		%d x
>8	leshort		>0		%d
#>10	byte		&0x80		color mapped,
#>10	byte&0x07	=0x00		2 colors
#>10	byte&0x07	=0x01		4 colors
#>10	byte&0x07	=0x02		8 colors
#>10	byte&0x07	=0x03		16 colors
#>10	byte&0x07	=0x04		32 colors
#>10	byte&0x07	=0x05		64 colors
#>10	byte&0x07	=0x06		128 colors
#>10	byte&0x07	=0x07		256 colors

# ITC (CMU WM) raster files.  It is essentially a byte-reversed Sun raster,
# 1 plane, no encoding.
0	string		\361\0\100\273	CMU window manager raster image data
>4	lelong		>0		%d x
>8	lelong		>0		%d,
>12	lelong		>0		%d-bit

# Magick Image File Format
# URL:		https://imagemagick.org/script/miff.php
# Reference:	http://fileformats.archiveteam.org/wiki/MIFF
# Update:	Joerg Jenderek
# http://www.nationalarchives.gov.uk/pronom/fmt/930
0	search/256/bc	id=imagemagick
# skip bad ASCII text by following new line~0x0A or space~0x20 character
#>&0	ubyte		x		\b, next character 0x%x
# called by TriD ImageMagick Machine independent File Format bitmap
>&0	ubyte&0xD5	0		MIFF image data
# https://reposcope.com/mimetype/image/miff
#!:mime	image/miff
!:mime	image/x-miff
!:ext	miff/mif
# examples with standard file(1) magic
#>>0	string		=id=ImageMagick	with standard magic
# examples with unusual file(1) magic like 
>>0	string		!id=ImageMagick	starting with
# start with comment (brace) like http://samples.fileformat.info/.../AQUARIUM.MIF
>>>0	ubyte		=0x7b		comment
# skip second character which is often a newline and show comment
>>>>2	string		x		"%s"
# does not start with comment, probably letters with other case like Id=ImageMagick
# ImageMagick-7.0.9-2/Magick++/demo/smile_anim.miff
>>>0	ubyte		!0x7b
>>>>0	string		>\0		'%-.14s'
# URL:		https://imagemagick.org/
# Reference:	https://imagemagick.org/script/magick-vector-graphics.php
# From:		Joerg Jenderek
# Note:		all white-spaces between commands are ignored
0	string		push
# skip some white spaces
>5	search/3	graphic-context	ImageMagick Vector Graphic
# TODO: look for dangerous commands like CVE-2016-3715
#!:mime	text/plain
!:mime	image/x-mvg
!:ext	mvg

# Artisan
0	long		1123028772	Artisan image data
>4	long		1		\b, rectangular 24-bit
>4	long		2		\b, rectangular 8-bit with colormap
>4	long		3		\b, rectangular 32-bit (24-bit with matte)

# FIG (Facility for Interactive Generation of figures), an object-based format
0	search/1	#FIG		FIG image text
>5	string		x		\b, version %.3s

# PHIGS
0	string		ARF_BEGARF		PHIGS clear text archive
0	string		@(#)SunPHIGS		SunPHIGS
# version number follows, in the form m.n
>40	string		SunBin			binary
>32	string		archive			archive

# GKS (Graphics Kernel System)
0	string		GKSM		GKS Metafile
>24	string		SunGKS		\b, SunGKS

# CGM image files
0	string		BEGMF		clear text Computer Graphics Metafile

# MGR bitmaps  (Michael Haardt, <EMAIL>)
0	string	yz	MGR bitmap, modern format, 8-bit aligned
0	string	zz	MGR bitmap, old format, 1-bit deep, 16-bit aligned
0	string	xz	MGR bitmap, old format, 1-bit deep, 32-bit aligned
0	string	yx	MGR bitmap, modern format, squeezed

# Fuzzy Bitmap (FBM) images
0	string		%bitmap\0	FBM image data
>30	long		0x31		\b, mono
>30	long		0x33		\b, color

# facsimile data
1	string		PC\ Research,\ Inc	group 3 fax data
>29	byte		0		\b, normal resolution (204x98 DPI)
>29	byte		1		\b, fine resolution (204x196 DPI)
# <AUTHOR> <EMAIL>
0	string		Sfff		structured fax file

# <AUTHOR> <EMAIL>
# most files with the extension .EPA and some with .BMP
0	string		\x11\x06	Award BIOS Logo, 136 x 84
!:mime	image/x-award-bioslogo
0	string		\x11\x09	Award BIOS Logo, 136 x 126
!:mime	image/x-award-bioslogo
#0	string		\x07\x1f	BIOS Logo corrupted?
# http://www.blackfiveservices.co.uk/awbmtools.shtml
# http://biosgfx.narod.ru/v3/
# http://biosgfx.narod.ru/abr-2/
0	string		AWBM
>4	leshort		<1981		Award BIOS bitmap
!:mime	image/x-award-bmp
# image width is a multiple of 4
>>4	leshort&0x0003	0
>>>4		leshort	x		\b, %d
>>>6		leshort	x		x %d
>>4	leshort&0x0003	>0		\b,
>>>4	leshort&0x0003	=1
>>>>4		leshort	x		%d+3
>>>4	leshort&0x0003	=2
>>>>4		leshort	x		%d+2
>>>4	leshort&0x0003	=3
>>>>4		leshort	x		%d+1
>>>6		leshort	x		x %d
# at offset 8 starts imagedata followed by "RGB " marker

# PC bitmaps (OS/2, Windows BMP files)  (Greg Roelofs, <EMAIL>)
# https://en.wikipedia.org/wiki/BMP_file_format#DIB_header_.\
# 28bitmap_information_header.29
# Note:	variant starting direct with DIB header see
#	http://fileformats.archiveteam.org/wiki/BMP
#	verified by ImageMagick version 6.8.9-8 command `identify *.dib`
0	leshort		40
# skip bad samples like GAME by looking for valid number of color planes
>12	uleshort	1		Device independent bitmap graphic
!:mime	image/bmp
!:apple	????BMPp
!:ext	dib
>>4	lelong		x		\b, %d x
>>8	lelong		x		%d x
>>14	leshort		x		%d
# number of color planes (must be 1) 
#>>12	uleshort	>1		\b, %u color planes
# compression method: 0~no 1~RLE 8-bit/pixel 3~Huffman 1D
#>>16	ulelong		3		\b, Huffman 1D compression
>>16	ulelong		>0		\b, %u compression
# image size is the size of raw bitmap; a dummy 0 can be given for BI_RGB bitmaps
>>20	ulelong		x		\b, image size %u
# horizontal and vertical resolution of the image (pixel per metre, signed integer) 
>>24	lelong		>0		\b, resolution %d x
>>>28	lelong		x		%d px/m
# number of colors in palette, or 0 to default to 2**n
#>>32	ulelong		>0		\b, %u colors
# number of important colors used, or 0 when every color is important
>>36	ulelong		>0		\b, %u important colors
0	string		BM
>14	leshort		12		PC bitmap, OS/2 1.x format
!:mime	image/x-ms-bmp
>>18	leshort		x		\b, %d x
>>20	leshort		x		%d
>14	leshort		64		PC bitmap, OS/2 2.x format
!:mime	image/bmp
!:apple	????BMPp
!:ext	bmp
# image width and height fields are unsigned integers for OS/2
>>18	ulelong		x		\b, %u x
>>22	ulelong		x		%u
# number of bits per pixel (color depth); found 1 4 8
>>28	uleshort	>1		x %u
# x, y coordinates of the hotspot
>>6	uleshort	>0		\b, hotspot %ux
>>>8	uleshort	x		\b%u
>>26	uleshort	>1		\b, %u color planes
# cbSize; size of file or headers
>>2	ulelong		x		\b, cbSize %u
#>>2	ulelong		x		\b, cbSize 0x%x
# offBits; offset to bitmap data like 56h 5Eh 8Eh 43Eh
>>10	ulelong			x	\b, bits offset %u
#>>10	ulelong			x	\b, bits offset 0x%x
#>>(10.l) ubequad		!0	\b, bits 0x%16.16llx
# BITMAPV2INFOHEADER	adds RGB bit masks
>14	leshort		52		PC bitmap, Adobe Photoshop
!:mime	image/bmp
!:apple	????BMPp
!:ext	bmp
>>18	lelong		x		\b, %d x
>>22	lelong		x		%d x
>>28	leshort		x		%d
# BITMAPV3INFOHEADER	adds alpha channel bit mask
>14	leshort		56		PC bitmap, Adobe Photoshop with alpha channel mask
!:mime	image/bmp
!:apple	????BMPp
!:ext	bmp
>>18	lelong		x		\b, %d x
>>22	lelong		x		%d x
>>28	leshort		x		%d
>14	leshort		40
# jump 4 bytes before end of file/header to skip fmt-116-signature-id-118.dib
>>(2.l-4)	ulong	x		PC bitmap, Windows 3.x format
!:mime	image/bmp
!:apple	????BMPp
>>>18	lelong		x		\b, %d x
>>>22	lelong		x		%d
# 320 x 400		https://en.wikipedia.org/wiki/LOGO.SYS
>>>18	ulequad		=0x0000019000000140	x
!:ext	bmp/sys
>>>18	ulequad		!0x0000019000000140
# compression method 2~RLE 4-bit/pixel implies also extension rle
>>>>30	ulelong		2		x
!:ext	bmp/rle
>>>>30	default		x		x
!:ext	bmp
# number of bits per pixel (color depth); found 1 2 4 8 16 24 32
>>>28	leshort		x		%d
# x, y coordinates of the hotspot; there is no hotspot in bitmaps, so values 0
#>>>6	uleshort	>0		\b, hotspot %ux
#>>>>8	uleshort	x		\b%u
# number of color planes (must be 1), except badplanes.bmp for testing
#>>>26	uleshort	>1		\b, %u color planes
# compression method: 0~no 1~RLE 8-bit/pixel 2~RLE 4-bit/pixel 3~Huffman 1D 6~RGBA bit field masks
#>>>30	ulelong		3		\b, Huffman 1D compression
>>>30	ulelong		>0		\b, %u compression
# image size is the size of raw bitmap; a dummy 0 can be given for BI_RGB bitmaps
>>>34	ulelong		>0		\b, image size %u
# horizontal and vertical resolution of the image (pixel per metre, signed integer) 
>>>38	lelong		>0		\b, resolution %d x
>>>>42	lelong		x		%d px/m
# number of colors in palette 16 256, or 0 to default to 2**n
#>>>46	ulelong		>0		\b, %u colors
# number of important colors used, or 0 when every color is important
>>>50	ulelong		>0		\b, %u important colors
# cbSize; often size of file
>>>2	ulelong		x		\b, cbSize %u
#>>>2	ulelong		x		\b, cbSize 0x%x
# offBits; offset to bitmap data like 36h 76h BEh 236h 406h 436h 4E6h
>>>10	ulelong			x	\b, bits offset %u
#>>>10	ulelong			x	\b, bits offset 0x%x
#>>>(10.l) ubequad		!0	\b, bits 0x%16.16llxd
>14	leshort		124		PC bitmap, Windows 98/2000 and newer format
!:mime	image/x-ms-bmp
>>18	lelong		x		\b, %d x
>>22	lelong		x		%d x
>>28	leshort		x		%d
>14	leshort		108		PC bitmap, Windows 95/NT4 and newer format
!:mime	image/x-ms-bmp
>>18	lelong		x		\b, %d x
>>22	lelong		x		%d x
>>28	leshort		x		%d
>14	leshort		128		PC bitmap, Windows NT/2000 format
!:mime	image/x-ms-bmp
>>18	lelong		x		\b, %d x
>>22	lelong		x		%d x
>>28	leshort		x		%d
# Update:	Joerg Jenderek
# URL:		http://fileformats.archiveteam.org/wiki/OS/2_Icon
# Reference:	http://www.fileformat.info
#		/format/os2bmp/spec/902d5c253f2a43ada39c2b81034f27fd/view.htm
# Note: verified by command like `deark -l -d3 OS2MEMU.ICO`
0	string			IC
# skip Lotus smart icon *.smi by looking for valid hotspot coordinates
>6	ulelong&0xFF00FF00	=0	OS/2 icon
# jump 4 bytes before end of header/file and test for accessibility
#>>(2.l-4) ubelong		x	End of header is OK!
!:mime	image/x-os2-ico
!:ext	ico
# cbSize; size of header or file in bytes like 1ah 120h 420h
>>2	ulelong			x	\b, cbSize %u
# xHotspot, yHotspot; coordinates of the hotspot for icons like 16 32
>>6	uleshort		x	\b, hotspot %ux
>>8	uleshort		x	\b%u
# offBits; offset in bytes to the beginning of the bit-map pel data like 20h
>>10	ulelong			x	\b, bits offset %u
#>>(10.l) ubequad		x	\b, bits 0x%16.16llx
#0	string		PI		PC pointer image data
#0	string		CI		PC color icon data
0	string		CI
# test also for valid dib header sizes 12 or 64
>14	ulelong		<65		OS/2
# test also for valid hotspot coordinates
#>>6	ulelong&0xFE00FE00	=0	OS/2
!:mime	image/x-os2-ico
!:ext	ico
>>14	ulelong		12		1.x color icon
# image width and height fields are unsigned integers for OS/2
>>>18	uleshort	x		%u x
# stored height = 2 * real height
>>>20	uleshort/2	x		%u
# number of bits per pixel (color depth). Typical 32 24 16 8 4 but only 1 found
>>>24	uleshort	>1		x %u
# color planes; must be 1
#>>>22	uleshort	>1		\b, %u color planes
>>14	ulelong		64		2.x color icon
# image width and height
>>>18	ulelong		x		%u x
# stored height = 2 * real height
>>>22	ulelong/2	x		%u
# number of bits per pixel (color depth). only 1 found
>>>28	uleshort	>1		x %u
#>>>26	uleshort	>1		\b, %u color planes
# compression method: 0~no 3~Huffman 1D
>>>30	ulelong		3		\b, Huffman 1D compression
#>>>30	ulelong		>0		\b, %u compression
# xHotspot, yHotspot; coordinates of the hotspot like 0 1 16 20 32 33 63 64
>>6	uleshort		x	\b, hotspot %ux
>>8	uleshort		x	\b%u
# cbSize; size of header or maybe file in bytes like 1Ah 4Eh 84Eh
>>2	ulelong			x	\b, cbSize %u
#>>2	ulelong			x	\b, cbSize %x
# offBits; offset to bitmap data (pixel array) like E4h 3Ah 66h 6Ah 33Ah 4A4h
>>10	ulelong			x	\b, bits offset %u
#>>10	ulelong			x	\b, bits offset 0x%x
#>>(10.l) ubequad		!0	\b, bits 0x%16.16llx
# dib header size: 12~Ch~OS/2 1.x 64~40h~OS/2 2.x
#>>14	ulelong		x		\b, dib header size %u
#0	string		CP		PC color pointer image data
# URL:		http://fileformats.archiveteam.org/wiki/OS/2_Pointer
# Reference:	http://www.fileformat.info/format/os2bmp/egff.htm
0	string		CP
# skip CPU-Z Report by checking for valid dib header sizes 12 or 64
>14	ulelong		<65		OS/2
# http://extension.nirsoft.net/PTR
!:mime	image/x-ibm-pointer
!:ext	ptr
>>14	ulelong		12		1.x color pointer
# image width and height fields are unsigned integers for OS/2
>>>18	uleshort	x		%u x
# stored height = 2 * real height
>>>20	uleshort/2	x		%u
# number of bits per pixel (color depth). Typical 32 24 16 8 4 but only 1 found
>>>24	uleshort	>1		x %u
# color planes; must be 1
#>>>22	uleshort	>1		\b, %u color planes
>>14	ulelong		64		2.x color pointer
# image width and height
>>>18	ulelong		x		%u x
# stored height = 2 * real height
>>>22	ulelong/2	x		%u
# number of bits per pixel (color depth). only 1 found
>>>28	uleshort	>1		x %u
#>>>26	uleshort	>1		\b, %u color planes
# compression method: 0~no 3~Huffman 1D
>>>30	ulelong		3		\b, Huffman 1D compression
#>>>30	ulelong		>0		\b, %u compression
# xHotspot, yHotspot; coordinates of the hotspot like 0 3 4 8 15 16 23 27 31
>>6	uleshort		x	\b, hotspot %ux
>>8	uleshort		x	\b%u
# cbSize; size of header or maybe file in bytes like 1Ah 4Eh
>>2	ulelong			x	\b, cbSize %u
#>>2	ulelong			x	\b, cbSize %x
# offBits; offset to bitmap data (pixel array) like 6Ah A4h E4h 4A4h
>>10	ulelong			x	\b, bits offset %u
#>>10	ulelong			x	\b, bits offset 0x%x
#>>(10.l) ubequad		!0	\b, bits 0x%16.16llx
# dib header size: 12~Ch~OS/2 1.x 64~40h~OS/2 2.x
#>>14	ulelong		x		\b, dib header size %u
# Conflicts with other entries [BABYL]
# URL:	http://fileformats.archiveteam.org/wiki/BMP#OS.2F2_Bitmap_Array
# Note:	container for OS/2 icon "IC", color icon "CI", color pointer "CP" or bitmap "BM"
#0	string		BA		PC bitmap array data
0	string		BA
# skip old Emacs RMAIL BABYL ./mail.news by checking for low header size
>2	ulelong		<0x004c5942	OS/2 graphic array
!:mime	image/x-os2-graphics
#!:apple	????BMPf
# cbSize; size of header like 28h 5Ch
>>2	ulelong			x	\b, cbSize %u
#>>2	ulelong			x	\b, cbSize 0x%x
# offNext; offset to data like 0 48h F2h 4Eh 64h C6h D2h D6h DAh E6h EAh 348h
>>6	ulelong			>0	\b, data offset %u
#>>6	ulelong			>0	\b, data offset 0x%x
#>>(6.l) ubequad		!0	\b, data 0x%16.16llx
# dimensions of the intended device like 640 x 480 for VGA or 1024 x 768
>>10	uleshort	>0		\b, display %u
>>>12	uleshort	>0		x %u
# usType of first array element
#>>14	string		x		\b, usType %2.2s
# 1 space char after "1st"
# no *.bga examples found https://www.openwith.org/file-extensions/bga/1342
>>14	string		BM	\b; 1st 
!:ext	bmp/bga
>>14	string		CI	\b; 1st 
!:ext	ico
>>14	string		CP	\b; 1st 
!:ext	ico
>>14	string		IC	\b; 1st 
!:ext	ico
# no white-black pointer found
#>>14	string		PT	\b; 1st 
#!:ext	
>>14	indirect	x	

# XPM icons (Greg Roelofs, <EMAIL>)
0	search/1	/*\ XPM\ */	X pixmap image text
!:mime	image/x-xpmi

# Utah Raster Toolkit RLE images (<EMAIL>)
0	leshort		0xcc52		RLE image data,
>6	leshort		x		%d x
>8	leshort		x		%d
>2	leshort		>0		\b, lower left corner: %d
>4	leshort		>0		\b, lower right corner: %d
>10	byte&0x1	=0x1		\b, clear first
>10	byte&0x2	=0x2		\b, no background
>10	byte&0x4	=0x4		\b, alpha channel
>10	byte&0x8	=0x8		\b, comment
>11	byte		>0		\b, %d color channels
>12	byte		>0		\b, %d bits per pixel
>13	byte		>0		\b, %d color map channels

# image file format (Robert Potter, <EMAIL>)
0	string		Imagefile\ version-	iff image data
# this adds the whole header (inc. version number), informative but longish
>10	string		>\0		%s

# Sun raster images, from Daniel Quinlan (<EMAIL>)
0	belong		0x59a66a95	Sun raster image data
>4	belong		>0		\b, %d x
>8	belong		>0		%d,
>12	belong		>0		%d-bit,
#>16	belong		>0		%d bytes long,
>20	belong		0		old format,
#>20	belong		1		standard,
>20	belong		2		compressed,
>20	belong		3		RGB,
>20	belong		4		TIFF,
>20	belong		5		IFF,
>20	belong		0xffff		reserved for testing,
>24	belong		0		no colormap
>24	belong		1		RGB colormap
>24	belong		2		raw colormap
#>28	belong		>0		colormap is %d bytes long

# SGI image file format, from Daniel Quinlan (<EMAIL>)
#
# See
#	http://reality.sgi.com/grafica/sgiimage.html
#
0	beshort		474		SGI image data
#>2	byte		0		\b, verbatim
>2	byte		1		\b, RLE
#>3	byte		1		\b, normal precision
>3	byte		2		\b, high precision
>4	beshort		x		\b, %d-D
>6	beshort		x		\b, %d x
>8	beshort		x		%d
>10	beshort		x		\b, %d channel
>10	beshort		!1		\bs
>80	string		>0		\b, "%s"

0	string		IT01		FIT image data
>4	belong		x		\b, %d x
>8	belong		x		%d x
>12	belong		x		%d
#
0	string		IT02		FIT image data
>4	belong		x		\b, %d x
>8	belong		x		%d x
>12	belong		x		%d
#
2048	string		PCD_IPI		Kodak Photo CD image pack file
>0xe02	byte&0x03	0x00		, landscape mode
>0xe02	byte&0x03	0x01		, portrait mode
>0xe02	byte&0x03	0x02		, landscape mode
>0xe02	byte&0x03	0x03		, portrait mode
0	string		PCD_OPA		Kodak Photo CD overview pack file

# <AUTHOR> <EMAIL>
# FITS is the Flexible Image Transport System, the de facto standard for
# data and image transfer, storage, etc., for the astronomical community.
# (FITS floating point formats are big-endian.)
0	string	SIMPLE\ \ =	FITS image data
!:mime	image/fits
!:ext	fits/fts
>109	string	8		\b, 8-bit, character or unsigned binary integer
>108	string	16		\b, 16-bit, two's complement binary integer
>107	string	\ 32		\b, 32-bit, two's complement binary integer
>107	string	-32		\b, 32-bit, floating point, single precision
>107	string	-64		\b, 64-bit, floating point, double precision

# other images
0	string	This\ is\ a\ BitMap\ file	Lisp Machine bit-array-file

# From SunOS 5.5.1 "/etc/magic" - appeared right before Sun raster image
# stuff.
#
0	beshort		0x1010		PEX Binary Archive

# DICOM medical imaging data
# URL:		https://en.wikipedia.org/wiki/DICOM#Data_format
# Note:		"dcm" is the official file name extension
# 		XnView mention also "dc3" and "acr" as file name extension
128	string	DICM			DICOM medical imaging data
!:mime	application/dicom
!:ext dcm/dicom/dic

# XWD - X Window Dump file.
#   As described in /usr/X11R6/include/X11/XWDFile.h
#   used by the xwd program.
#   Bradford Castalia, idaeim, 1/01
#   updated by Adam Buchbinder, 2/09
# The following assumes version 7 of the format; the first long is the length
# of the header, which is at least 25 4-byte longs, and the one at offset 8
# is a constant which is always either 1 or 2. Offset 12 is the pixmap depth,
# which is a maximum of 32.
0	belong	>100
>8	belong	<3
>>12	belong	<33
>>>4	belong	7			XWD X Window Dump image data
!:mime	image/x-xwindowdump
>>>>100	string	>\0			\b, "%s"
>>>>16	belong	x			\b, %dx
>>>>20	belong	x			\b%dx
>>>>12	belong	x			\b%d

# PDS - Planetary Data System
#   These files use Parameter Value Language in the header section.
#   Unfortunately, there is no certain magic, but the following
#   strings have been found to be most likely.
0	string	NJPL1I00		PDS (JPL) image data
2	string	NJPL1I			PDS (JPL) image data
0	string	CCSD3ZF			PDS (CCSD) image data
2	string	CCSD3Z			PDS (CCSD) image data
0	string	PDS_			PDS image data
0	string	LBLSIZE=		PDS (VICAR) image data

# pM8x: ATARI STAD compressed bitmap format
#
# <AUTHOR> <EMAIL> Feb 2, 2001
# p M 8 5/6 xx yy zz data...
# Atari ST STAD bitmap is always 640x400, bytewise runlength compressed.
# bytes either run horizontally (pM85) or vertically (pM86). yy is the
# most frequent byte, xx and zz are runlength escape codes, where xx is
# used for runs of yy.
#
0	string	pM85		Atari ST STAD bitmap image data (hor)
>5	byte	0x00		(white background)
>5	byte	0xFF		(black background)
0	string	pM86		Atari ST STAD bitmap image data (vert)
>5	byte	0x00		(white background)
>5	byte	0xFF		(black background)

# <AUTHOR> <EMAIL>
# https://www.atarimax.com/jindroush.atari.org/afmtatr.html
0	leshort	0x0296		Atari ATR image

# XXX:
# This is bad magic 0x5249 == 'RI' conflicts with RIFF and other
# magic.
# <AUTHOR> <EMAIL>
#0	beshort	0x5249		RICE image
#>2	beshort	x		v%d
#>4	beshort	x		(%d x
#>6	beshort	x		%d)
#>8	beshort	0		8 bit
#>8	beshort	1		10 bit
#>8	beshort	2		12 bit
#>8	beshort	3		13 bit
#>10	beshort	0		4:2:2
#>10	beshort	1		4:2:2:4
#>10	beshort	2		4:4:4
#>10	beshort	3		4:4:4:4
#>12	beshort	1		RGB
#>12	beshort	2		CCIR601
#>12	beshort	3		RP175
#>12	beshort	4		YUV

# PCX image files
# <AUTHOR> <EMAIL>
# updated by Joerg Jenderek at Feb 2013 by https://de.wikipedia.org/wiki/PCX
# https://web.archive.org/web/20100206055706/http://www.qzx.com/pc-gpe/pcx.txt
# GRR: original test was still too general as it catches xbase examples T5.DBT,T6.DBT with 0xa000000
# test for bytes 0x0a,version byte (0,2,3,4,5),compression byte flag(0,1), bit depth (>0) of PCX or T5.DBT,T6.DBT
0	ubelong&0xffF8fe00	0x0a000000
# for PCX bit depth > 0
>3	ubyte		>0
# test for valid versions
>>1	ubyte		<6
>>>1	ubyte		!1	PCX
!:mime	image/x-pcx
#!:mime	image/pcx
>>>>1	ubyte		0	ver. 2.5 image data
>>>>1	ubyte		2	ver. 2.8 image data, with palette
>>>>1	ubyte		3	ver. 2.8 image data, without palette
>>>>1	ubyte		4	for Windows image data
>>>>1	ubyte		5	ver. 3.0 image data
>>>>4	uleshort	x	bounding box [%d,
>>>>6	uleshort	x	%d] -
>>>>8	uleshort	x	[%d,
>>>>10	uleshort	x	%d],
>>>>65	ubyte		>1	%d planes each of
>>>>3	ubyte		x	%d-bit
>>>>68	byte		1	colour,
>>>>68	byte		2	grayscale,
# this should not happen
>>>>68	default		x	image,
>>>>12	leshort		>0	%d x
>>>>>14	uleshort	x	%d dpi,
>>>>2	byte		0	uncompressed
>>>>2	byte		1	RLE compressed

# Adobe Photoshop
# <AUTHOR> <EMAIL>
0	string		8BPS Adobe Photoshop Image
!:mime	image/vnd.adobe.photoshop
>4   beshort 2 (PSB)
>18  belong  x \b, %d x
>14  belong  x %d,
>24  beshort 0 bitmap
>24  beshort 1 grayscale
>>12 beshort 2 with alpha
>24  beshort 2 indexed
>24  beshort 3 RGB
>>12 beshort 4 \bA
>24  beshort 4 CMYK
>>12 beshort 5 \bA
>24  beshort 7 multichannel
>24  beshort 8 duotone
>24  beshort 9 lab
>12  beshort > 1
>>12  beshort x \b, %dx
>12  beshort 1 \b,
>22  beshort x %d-bit channel
>12  beshort > 1 \bs

# XV thumbnail indicator (ThMO)
0	string		P7\ 332		XV thumbnail image data

# NITF is defined by United States MIL-STD-2500A
0	string	NITF	National Imagery Transmission Format
>25	string	>\0	dated %.14s

# GEM Image: Version 1, Headerlen 8 (Wolfram Kleff)
# <AUTHOR> <EMAIL>
# Update: Joerg Jenderek
# See http://fileformats.archiveteam.org/wiki/GEM_Raster
# For variations, also see:
#    https://www.seasip.info/Gem/ff_img.html (Ventura)
#    http://www.atari-wiki.com/?title=IMG_file (XIMG, STTT)
#    http://www.fileformat.info/format/gemraster/spec/index.htm (XIMG, STTT)
#    http://sylvana.net/1stguide/1STGUIDE.ENG (TIMG)
0       beshort     0x0001
# header_size
>2      beshort     0x0008
>>0     use gem_info
>2      beshort     0x0009
>>0     use gem_info
# no example for NOSIG
>2      beshort     24
>>0     use gem_info
# no example for HYPERPAINT
>2      beshort     25
>>0     use gem_info
16      string      XIMG\0
>0      use gem_info
# no example
16      string      STTT\0\x10
>0      use gem_info
# no example or description
16      string      TIMG\0
>0      use gem_info

0   name        gem_info
# version is 2 for some XIMG and 1 for all others
>0	beshort		<0x0003		GEM
# https://www.snowstone.org.uk/riscos/mimeman/mimemap.txt
!:mime	image/x-gem
# header_size 24 25 27 59 779 words for colored bitmaps
>>2	beshort		>9
>>>16	string		STTT\0\x10	STTT
>>>16	string		TIMG\0		TIMG
# HYPERPAINT or NOSIG variant
>>>16	string		\0\x80
>>>>2	beshort		=24		NOSIG
>>>>2	beshort		!24		HYPERPAINT
# NOSIG or XIMG variant
>>>16	default		x
>>>>16	string		!XIMG\0		NOSIG
>>16	string		=XIMG\0		XIMG Image data
!:ext	img/ximg
# to avoid Warning: Current entry does not yet have a description for adding a EXTENSION type
>>16	string		!XIMG\0		Image data
!:ext	img
# header_size is 9 for Ventura files and 8 for other GEM Paint files
>>2	beshort		9		(Ventura)
#>>2	beshort		8		(Paint)
>>12	beshort		x		%d x
>>14	beshort		x		%d,
# 1 4 8
>>4	beshort		x		%d planes,
# in tenths of a millimetre
>>8	beshort		x		%d x
>>10	beshort		x		%d pixelsize
# pattern_size 1-8. 2 for GEM Paint
>>6	beshort		!2		\b, pattern size %d

# GEM Metafile (Wolfram Kleff)
0	lelong		0x0018FFFF	GEM Metafile data
>4	leshort		x		version %d

#
# SMJPEG. A custom Motion JPEG format used by Loki Entertainment
# <AUTHOR> <EMAIL>.
#
0	string	\0\nSMJPEG	SMJPEG
>8	belong	x		%d.x data
# According to the specification you could find any number of _TXT
# headers here, but I can't think of any way of handling that. None of
# the SMJPEG files I tried it on used this feature. Even if such a
# file is encountered the output should still be reasonable.
>16	string	_SND		\b,
>>24	beshort	>0		%d Hz
>>26	byte	8		8-bit
>>26	byte	16		16-bit
>>28	string	NONE		uncompressed
# >>28	string	APCM		ADPCM compressed
>>27	byte	1		mono
>>28	byte	2		stereo
# Help! Isn't there any way to avoid writing this part twice?
>>32	string	_VID		\b,
# >>>48	string	JFIF		JPEG
>>>40	belong	>0		%d frames
>>>44	beshort	>0		(%d x
>>>46	beshort	>0		%d)
>16	string	_VID		\b,
# >>32	string	JFIF		JPEG
>>24	belong	>0		%d frames
>>28	beshort	>0		(%d x
>>30	beshort	>0		%d)

0	string	Paint\ Shop\ Pro\ Image\ File	Paint Shop Pro Image File

# "thumbnail file" (icon)
# descended from "xv", but in use by other applications as well (Wolfram Kleff)
0       string          P7\ 332         XV "thumbnail file" (icon) data

# taken from fkiss: (<<EMAIL>> ?)
0       string          KiSS            KISS/GS
>4      byte            16              color
>>5     byte            x               %d bit
>>8     leshort         x               %d colors
>>10    leshort         x               %d groups
>4      byte            32              cell
>>5     byte            x               %d bit
>>8     leshort         x               %d x
>>10    leshort         x               %d
>>12    leshort         x               +%d
>>14    leshort         x               +%d

# Webshots (www.webshots.com), by John Harrison
0       string          C\253\221g\230\0\0\0 Webshots Desktop .wbz file

# Hercules DASD image files
# <AUTHOR> <EMAIL>
0       string  CKD_P370        Hercules CKD DASD image file
>8      long    x               \b, %d heads per cylinder
>12     long    x               \b, track size %d bytes
>16     byte    x               \b, device type 33%2.2X

0       string  CKD_C370        Hercules compressed CKD DASD image file
>8      long    x               \b, %d heads per cylinder
>12     long    x               \b, track size %d bytes
>16     byte    x               \b, device type 33%2.2X

0       string  CKD_S370        Hercules CKD DASD shadow file
>8      long    x               \b, %d heads per cylinder
>12     long    x               \b, track size %d bytes
>16     byte    x               \b, device type 33%2.2X

# Squeak images and programs - <EMAIL>
0	string		\146\031\0\0	Squeak image data
0	search/1	'From\040Squeak	Squeak program text

# partimage: file(1) magic for PartImage files (experimental, incomplete)
# <AUTHOR> <EMAIL>
0		string	PaRtImAgE-VoLuMe	PartImage
>0x0020		string	0.6.1		file version %s
>>0x0060	lelong	>-1		volume %d
#>>0x0064 8 byte identifier
#>>0x007c reserved
>>0x0200	string	>\0		type %s
>>0x1400	string	>\0		device %s,
>>0x1600	string	>\0		original filename %s,
# Some fields omitted
>>0x2744	lelong	0		not compressed
>>0x2744	lelong	1		gzip compressed
>>0x2744	lelong	2		bzip2 compressed
>>0x2744	lelong	>2		compressed with unknown algorithm
>0x0020		string	>0.6.1		file version %s
>0x0020		string	<0.6.1		file version %s

# DCX is multi-page PCX, using a simple header of up to 1024
# offsets for the respective PCX components.
# <AUTHOR> <EMAIL>
0	lelong	987654321	DCX multi-page PCX image data

# <AUTHOR> <EMAIL>
# Kodak Cineon format for scanned negatives
# http://www.kodak.com/US/en/motion/support/dlad/
0	lelong  0xd75f2a80	Cineon image data
>200	belong  >0		\b, %d x
>204	belong  >0		%d


# Bio-Rad .PIC is an image format used by microscope control systems
# and related image processing software used by biologists.
# <AUTHOR> <EMAIL>
# BOOL values are two-byte integers; use them to rule out false positives.
# https://web.archive.org/web/20050317223257/www.cs.ubc.ca/spider/ladic/text/biorad.txt
# Samples: https://www.loci.wisc.edu/software/sample-data
14	leshort <2
>62	leshort <2
>>54	leshort 12345		Bio-Rad .PIC Image File
>>>0	leshort >0		%d x
>>>2	leshort >0		%d,
>>>4	leshort =1		1 image in file
>>>4	leshort >1		%d images in file

# <AUTHOR> <EMAIL>
# The description of *.mrw format can be found at
# http://www.dalibor.cz/minolta/raw_file_format.htm
0	string	\000MRM			Minolta Dimage camera raw image data

# Summary: DjVu image / document
# Extension: .djvu
# Reference: http://djvu.org/docs/DjVu3Spec.djvu
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
0	string	AT&TFORM
>12	string	DJVM		DjVu multiple page document
!:mime	image/vnd.djvu
>12	string	DJVU		DjVu image or single page document
!:mime	image/vnd.djvu
>12	string	DJVI		DjVu shared document
!:mime	image/vnd.djvu
>12	string	THUM		DjVu page thumbnails
!:mime	image/vnd.djvu

# Originally by Marc Espie
# Modified by Robert Minsk <robertminsk at yahoo.com>
# https://www.openexr.com/openexrfilelayout.pdf
0	lelong		20000630	OpenEXR image data,
!:mime image/x-exr
>4	lelong&0x000000ff x		version %d,
>4	lelong		^0x00000200	storage: scanline
>4	lelong		&0x00000200	storage: tiled
>8	search/0x1000	compression\0	\b, compression:
>>&16	byte		0		none
>>&16	byte		1		rle
>>&16	byte		2		zips
>>&16	byte		3		zip
>>&16	byte		4		piz
>>&16	byte		5		pxr24
>>&16	byte		6		b44
>>&16	byte		7		b44a
>>&16	byte		8		dwaa
>>&16	byte		9		dwab
>>&16	byte		>9		unknown
>8	 search/0x1000	dataWindow\0	\b, dataWindow:
>>&10	lelong		x		(%d
>>&14	lelong		x		%d)-
>>&18	lelong		x		\b(%d
>>&22	lelong		x		%d)
>8	search/0x1000	displayWindow\0	\b, displayWindow:
>>&10	lelong		x		(%d
>>&14	lelong		x		%d)-
>>&18	lelong		x		\b(%d
>>&22	lelong		x		%d)
>8	search/0x1000	lineOrder\0	 \b, lineOrder:
>>&14	byte		0		increasing y
>>&14	byte		1		decreasing y
>>&14	byte		2		random y
>>&14	byte		>2		unknown

# SMPTE Digital Picture Exchange Format, SMPTE DPX
#
# ANSI/SMPTE 268M-1994, SMPTE Standard for File Format for Digital
# Moving-Picture Exchange (DPX), v1.0, 18 February 1994
# Robert Minsk <robertminsk at yahoo.com>
# Modified by Harry Mallon <hjmallon at gmail.com>
0	string		SDPX	DPX image data, big-endian,
!:mime image/x-dpx
>0	use		dpx_info
0	string		XPDS	DPX image data, little-endian,
!:mime image/x-dpx
>0	use		\^dpx_info

0	name		dpx_info
>768	beshort		<4
>>772	belong		x	%dx
>>776	belong		x	\b%d,
>768	beshort		>3
>>776	belong		x	%dx
>>772	belong		x	\b%d,
>768	beshort		0	left to right/top to bottom
>768	beshort		1	right to left/top to bottom
>768	beshort		2	left to right/bottom to top
>768	beshort		3	right to left/bottom to top
>768	beshort		4	top to bottom/left to right
>768	beshort		5	top to bottom/right to left
>768	beshort		6	bottom to top/left to right
>768	beshort		7	bottom to top/right to left

# <AUTHOR> <EMAIL>
# https://www.unidata.ucar.edu/packages/netcdf/
0	string	CDF\001			NetCDF Data Format data
# 64-bit offset netcdf Classic https://www.unidata.ucar.edu/software/netcdf/docs/file_format_specifications
0	string	CDF\002			NetCDF Data Format data (64-bit offset)

#-----------------------------------------------------------------------
# Hierarchical Data Format, used to facilitate scientific data exchange
# specifications at http://hdf.ncsa.uiuc.edu/
0	belong	0x0e031301	Hierarchical Data Format (version 4) data
!:mime	application/x-hdf
0	string	\211HDF\r\n\032\n	Hierarchical Data Format (version 5) data
!:mime	application/x-hdf
512	string	\211HDF\r\n\032\n	Hierarchical Data Format (version 5) with 512 bytes user block
!:mime	application/x-hdf
1024	string	\211HDF\r\n\032\n	Hierarchical Data Format (version 5) with 1k user block
!:mime	application/x-hdf
2048	string	\211HDF\r\n\032\n	Hierarchical Data Format (version 5) with 2k user block
!:mime	application/x-hdf
4096	string	\211HDF\r\n\032\n	Hierarchical Data Format (version 5) with 4k user block
!:mime	application/x-hdf


# <AUTHOR> <EMAIL>
# Xara (for a while: Corel Xara) is a graphic package, see
# http://www.xara.com/ for Windows and as GPL application for Linux
0	string	XARA\243\243	Xara graphics file

# https://www.cartesianinc.com/Tech/
0	string	CPC\262		Cartesian Perceptual Compression image
!:mime	image/x-cpi

# <AUTHOR> <EMAIL>
# puredigital used it for the CVS disposable camcorder
#8       lelong  4       ZBM bitmap image data
#>4      leshort x       %u x
#>6      leshort x       %u

# <AUTHOR> <EMAIL>
# uncompressed 5:6:5 HighColor image for OLPC XO firmware icons
0       string C565     OLPC firmware icon image data
>4      leshort x       %u x
>6      leshort x       %u

# Applied Images - Image files from Cytovision
# <AUTHOR> <EMAIL>
0	string	\xce\xda\xde\xfa	Cytovision Metaphases file
0	string	\xed\xad\xef\xac	Cytovision Karyotype file
0	string	\x0b\x00\x03\x00	Cytovision FISH Probe file
0	string	\xed\xfe\xda\xbe	Cytovision FLEX file
0	string	\xed\xab\xed\xfe	Cytovision FLEX file
0	string	\xad\xfd\xea\xad	Cytovision RATS file

# Wavelet Scalar Quantization format used in gray-scale fingerprint images
# <AUTHOR> <EMAIL>
0	string	\xff\xa0\xff\xa8\x00	Wavelet Scalar Quantization image data

# Type:		PCO B16 image files
# URL:		http://www.pco.de/fileadmin/user_upload/db/download/MA_CWDCOPIE_0412b.pdf
# <AUTHOR> <EMAIL>
# Extension:	.b16
# Description:	Pixel image format produced by PCO Camware, typically used
#		together with PCO cameras.
# Note:		Different versions exist for e.g. 8 bit and 16 bit images.
#		Documentation is incomplete.
0	string/b	PCO-	PCO B16 image data
>12	lelong		x	\b, %dx
>16	lelong		x	\b%d
>20	lelong		0	\b, short header
>20	lelong		-1	\b, extended header
>>24	lelong		0	\b, grayscale
>>>36	lelong		0	linear LUT
>>>36	lelong		1	logarithmic LUT
>>>28	lelong		x	[%d
>>>32	lelong		x	\b,%d]
>>24	lelong		1	\b, color
>>>64	lelong		0	linear LUT
>>>64	lelong		1	logarithmic LUT
>>>40	lelong		x	r[%d
>>>44	lelong		x	\b,%d]
>>>48	lelong		x	g[%d
>>>52	lelong		x	\b,%d]
>>>56	lelong		x	b[%d
>>>60	lelong		x	\b,%d]

# Polar Monitor Bitmap (.pmb) used as logo for Polar Electro watches
# From: Markus Heidelberg <markus.heidelberg at web.de>
0	string/t	[BitmapInfo2]	Polar Monitor Bitmap text
!:mime	image/x-polar-monitor-bitmap

# <AUTHOR> <EMAIL>
# updated by: Joerg Jenderek
# URL: http://techmods.net/nuvi/
0	string	GARMIN\ BITMAP\ 01	Garmin Bitmap file
# extension is also used for
# Sony SRF raw image (image/x-sony-srf)
# SRF map
# Terragen Surface Map (https://www.planetside.co.uk/terragen)
# FileLocator Pro search criteria file (https://www.mythicsoft.com/filelocatorpro)
!:ext srf
#!:mime	image/x-garmin-srf
# version 1.00,2.00,2.10,2.40,2.50
>0x2f	string		>0		\b, version %4.4s
# width (2880,2881,3240)
>0x55	uleshort	>0		\b, %dx
# height (80,90)
>>0x53	uleshort	x		\b%d

# Type:	Ulead Photo Explorer5 (.pe5)
# URL:	http://www.jisyo.com/cgibin/view.cgi?EXT=pe5 (Japanese)
# <AUTHOR> <EMAIL>
0	string	IIO2H			Ulead Photo Explorer5

# Type:	X11 cursor
# URL:	http://webcvs.freedesktop.org/mime/shared-mime-info/freedesktop.org.xml.in?view=markup
# <AUTHOR> <EMAIL>
0	string	Xcur			X11 cursor

# Type:	Olympus ORF raw images.
# URL:	https://libopenraw.freedesktop.org/wiki/Olympus_ORF
# <AUTHOR> <EMAIL>
0	string		MMOR		Olympus ORF raw image data, big-endian
!:mime	image/x-olympus-orf
0	string		IIRO		Olympus ORF raw image data, little-endian
!:mime	image/x-olympus-orf
0	string		IIRS		Olympus ORF raw image data, little-endian
!:mime	image/x-olympus-orf

# Type: files used in modern AVCHD camcoders to store clip information
# Extension: .cpi
# <AUTHOR> <EMAIL>
0	string	HDMV0100	AVCHD Clip Information

# <AUTHOR> <EMAIL>
# URL: http://local.wasp.uwa.edu.au/~pbourke/dataformats/pic/
# Radiance HDR; usually has .pic or .hdr extension.
0	string	#?RADIANCE\n	Radiance HDR image data
#!mime	image/vnd.radiance

# <AUTHOR> <EMAIL>
# URL: https://www.mpi-inf.mpg.de/resources/pfstools/pfs_format_spec.pdf
# Used by the pfstools packages. The regex matches for the image size could
# probably use some work. The MIME type is made up; if there's one in
# actual common use, it should replace the one below.
0	string	PFS1\x0a	PFS HDR image data
#!mime	image/x-pfs
>1	regex	[0-9]*\ 		\b, %s
>>1	regex	\ [0-9]{4}		\bx%s

# Type: Foveon X3F
# URL:  https://www.photofo.com/downloads/x3f-raw-format.pdf
# <AUTHOR> <EMAIL>
# Note that the MIME type isn't defined anywhere that I can find; if
# there's a canonical type for this format, it should replace this one.
0	string	FOVb	Foveon X3F raw image data
!:mime	image/x-x3f
>6	leshort	x	\b, version %d.
>4	leshort	x	\b%d
>28	lelong	x	\b, %dx
>32	lelong	x	\b%d

# Paint.NET file
# <AUTHOR> <EMAIL>
0	string	PDN3	Paint.NET image data
!:mime	image/x-paintnet

# Not really an image.
# <AUTHOR> <EMAIL>
0	string	\x46\x4d\x52\x00	ISO/IEC 19794-2 Format Minutiae Record (FMR)

# doc: https://www.shikino.co.jp/eng/products/images/FLOWER.jpg.zip
# example: https://www.shikino.co.jp/eng/products/images/FLOWER.wdp.zip
90	bequad		0x574D50484F544F00	JPEG-XR Image
>98	byte&0x08	=0x08			\b, hard tiling
>99	byte&0x80	=0x80			\b, tiling present
>99	byte&0x40	=0x40			\b, codestream present
>99	byte&0x38	x			\b, spatial xform=
>99	byte&0x38	0x00			\bTL
>99	byte&0x38	0x08			\bBL
>99	byte&0x38	0x10			\bTR
>99	byte&0x38	0x18			\bBR
>99	byte&0x38	0x20			\bBT
>99	byte&0x38	0x28			\bRB
>99	byte&0x38	0x30			\bLT
>99	byte&0x38	0x38			\bLB
>100	byte&0x80	=0x80			\b, short header
>>102	beshort+1	x			\b, %d
>>104	beshort+1	x			\bx%d
>100	byte&0x80	=0x00			\b, long header
>>102	belong+1	x			\b, %x
>>106	belong+1	x			\bx%x
>101	beshort&0xf	x			\b, bitdepth=
>>101	beshort&0xf	0x0			\b1-WHITE=1
>>101	beshort&0xf	0x1			\b8
>>101	beshort&0xf	0x2			\b16
>>101	beshort&0xf	0x3			\b16-SIGNED
>>101	beshort&0xf	0x4			\b16-FLOAT
>>101	beshort&0xf	0x5			\b(reserved 5)
>>101	beshort&0xf	0x6			\b32-SIGNED
>>101	beshort&0xf	0x7			\b32-FLOAT
>>101	beshort&0xf	0x8			\b5
>>101	beshort&0xf	0x9			\b10
>>101	beshort&0xf	0xa			\b5-6-5
>>101	beshort&0xf	0xb			\b(reserved %d)
>>101	beshort&0xf	0xc			\b(reserved %d)
>>101	beshort&0xf	0xd			\b(reserved %d)
>>101	beshort&0xf	0xe			\b(reserved %d)
>>101	beshort&0xf	0xf			\b1-BLACK=1
>101	beshort&0xf0	x			\b, colorfmt=
>>101	beshort&0xf0	0x00			\bYONLY
>>101	beshort&0xf0	0x10			\bYUV240
>>101	beshort&0xf0	0x20			\bYWV422
>>101	beshort&0xf0	0x30			\bYWV444
>>101	beshort&0xf0	0x40			\bCMYK
>>101	beshort&0xf0	0x50			\bCMYKDIRECT
>>101	beshort&0xf0	0x60			\bNCOMPONENT
>>101	beshort&0xf0	0x70			\bRGB
>>101	beshort&0xf0	0x80			\bRGBE
>>101	beshort&0xf0	>0x80			\b(reserved 0x%x)

# <AUTHOR> <EMAIL>
#
# BPG (Better Portable Graphics) format
# https://bellard.org/bpg/
# http://fileformats.archiveteam.org/wiki/BPG
#
0	string	\x42\x50\x47\xFB	BPG (Better Portable Graphics)
!:mime  image/bpg

# From: Joerg Jenderek
# URL: https://en.wikipedia.org/wiki/Apple_Icon_Image_format
0	string		icns		Mac OS X icon
!:mime	image/x-icns
!:apple	????icns
!:ext icns
>4	ubelong		>0
# file size
>>4	ubelong		x		\b, %d bytes
# icon type
>>8	string		x		\b, "%4.4s" type

# TIM images
0		lelong		0x00000010	TIM image,
>4		lelong  	0x8		4-Bit,
>4		lelong  	0x9		8-Bit,
>4		lelong  	0x2		15-Bit,
>4		lelong  	0x3		24-Bit,
>4		lelong 		&8
>>(8.l+12)	leshort		x		Pixel at (%d,
>>(8.l+14)	leshort		x		\b%d)
>>(8.l+16)	leshort		x		Size=%dx
>>(8.l+18)	leshort		x		\b%d,
>>4		lelong 		0x8		16 CLUT Entries at
>>4		lelong 		0x9		256 CLUT Entries at
>>12		leshort		x		(%d,
>>14		leshort		x		\b%d)
>4		lelong		^8
>>12		leshort		x		Pixel at (%d,
>>14		leshort		x		\b%d)
>>16		leshort		x		Size=%dx
>>18		leshort		x		\b%d

# MDEC streams
0		lelong		0x80010160	MDEC video stream,
>16		leshort		x		%dx
>18		leshort		x		\b%d
#>8		lelong		x		%d frames
#>4		leshort		x		secCount=%d;
#>6		leshort		x		nSectors=%d;
#>12		lelong		x		frameSize=%d;

# BS encoded bitstreams
2		leshort		0x3800		BS image,
>6		leshort		x		Version %d,
>4		leshort		x		Quantization %d,
>0		leshort		x		(Decompresses to %d words)

# Type: farbfeld image.
# Url: http://tools.suckless.org/farbfeld/
# <AUTHOR> <EMAIL>
#
0		string		farbfeld	farbfeld image data,
>8		ubelong		x		%dx
>12		ubelong		x		\b%d

# Type: Microsoft DirectDraw Surface (common data)
# URL:	https://msdn.microsoft.com/library/default.asp?url=/library/en-us/directx9_c/directx/graphics/reference/DDSFileReference/ddsfileformat.asp
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
0	name	ms-directdraw-surface
>0x10	ulelong	x			%u x
>0x0C	ulelong	x			%u
# Color depth.
>0x58	ulelong	>0			\b, %u-bit color
# Determine the pixel format.
>0x50	ulelong&0x4	4
# FIXME: Handle DX10 and XBOX formats.
>>0x54	string	x			\b, compressed using %.4s
>0x50	ulelong&0x2	0x2		\b, alpha only
>0x50	ulelong&0x200	0x200		\b, YUV
>0x50	ulelong&0x20000	0x20000		\b, luminance
# RGB pixel format
>0x50	ulelong&0x40	0x40

# Determine the RGB format using the color masks.
# ulequad order: 0xGGGGGGGGRRRRRRRR, 0xAAAAAAAABBBBBBBB

>>0x58		ulelong	16

# NOTE: 15-bit color formats usually have 16-bit listed as the color depth.
>>>0x5C		ulequad	0x000003E000007C00
>>>>0x64	ulequad 0x000000000000001F	\b, RGB555
>>>0x5C		ulequad	0x000003E000001F00
>>>>0x64	ulequad 0x000000000000007C	\b, BGR555

>>>0x5C		ulequad	0x000007E00000F800
>>>>0x64	ulequad 0x000000000000001F	\b, RGB565
>>>0x5C		ulequad	0x000007E000001F00
>>>>0x64	ulequad 0x00000000000000F8	\b, BGR565

>>>0x5C		ulequad	0x000000F000000F00
>>>>0x64	ulequad 0x0000F0000000000F	\b, ARGB4444
>>>0x5C		ulequad	0x000000F00000000F
>>>>0x64	ulequad 0x0000F00000000F00	\b, ABGR4444

>>>0x5C		ulequad	0x00000F000000F000
>>>>0x64	ulequad 0x0000000F000000F0	\b, RGBA4444
>>>0x5C		ulequad	0x00000F00000000F0
>>>>0x64	ulequad 0x0000000F0000F000	\b, BGRA4444

>>>0x5C		ulequad	0x000000F000000F00
>>>>0x64	ulequad 0x000000000000000F	\b, xRGB4444
>>>0x5C		ulequad	0x000000F00000000F
>>>>0x64	ulequad 0x0000000000000F00	\b, xBGR4444

>>>0x5C		ulequad	0x00000F000000F000
>>>>0x64	ulequad 0x00000000000000F0	\b, RGBx4444
>>>0x5C		ulequad	0x00000F00000000F0
>>>>0x64	ulequad 0x000000000000F000	\b, BGRx4444

>>>0x5C		ulequad	0x000003E000007C00
>>>>0x64	ulequad 0x000080000000001F	\b, ARGB1555
>>>0x5C		ulequad	0x000003E000001F00
>>>>0x64	ulequad 0x000080000000007C	\b, ABGR1555
>>>0x5C		ulequad	0x000007C00000F800
>>>>0x64	ulequad 0x000000010000003E	\b, RGBA5551
>>>0x5C		ulequad	0x000007C00000003E
>>>>0x64	ulequad 0x000000010000F800	\b, BGRA5551

>>88		ulelong 24
>>>0x5C		ulequad	0x0000FF0000FF0000
>>>>0x64	ulequad 0x00000000000000FF	\b, RGB888
>>>0x5C		ulequad	0x0000FF00000000FF
>>>>0x64	ulequad 0x0000000000FF0000	\b, BGR888

>>88		ulelong 32
>>>0x5C		ulequad	0x0000FF0000FF0000
>>>>0x64	ulequad 0xFF000000000000FF	\b, ARGB8888
>>>0x5C		ulequad	0x0000FF00000000FF
>>>>0x64	ulequad 0xFF00000000FF0000	\b, ABGR8888

>>>0x5C		ulequad	0x00FF0000FF000000
>>>>0x64	ulequad 0x000000FF0000FF00	\b, RGBA8888
>>>0x5C		ulequad	0x00FF00000000FF00
>>>>0x64	ulequad 0x000000FFFF000000	\b, BGBA8888

>>>0x5C		ulequad	0x0000FF0000FF0000
>>>>0x64	ulequad 0x00000000000000FF	\b, xRGB8888
>>>0x5C		ulequad	0x0000FF00000000FF
>>>>0x64	ulequad 0x0000000000FF0000	\b, xBGR8888

>>>0x5C		ulequad	0x00FF0000FF000000
>>>>0x64	ulequad 0x000000000000FF00	\b, RGBx8888
>>>0x5C		ulequad	0x00FF00000000FF00
>>>>0x64	ulequad 0x00000000FF000000	\b, BGBx8888

# Less common 32-bit color formats.
>>>0x5C		ulequad	0xFFFF00000000FFFF
>>>>0x64	ulequad 0x0000000000000000	\b, G16R16
>>>0x5C		ulequad	0x0000FFFFFFFF0000
>>>>0x64	ulequad 0x0000000000000000	\b, R16G16

>>>0x5C		ulequad	0x000FFC003FF00000
>>>>0x64	ulequad 0xC0000000000003FF	\b, A2R10G10B10
>>>0x5C		ulequad	0x000FFC00000003FF
>>>>0x64	ulequad 0xC00000003FF00000	\b, A2B10G10R10

# Type: Microsoft DirectDraw Surface
# URL:	https://msdn.microsoft.com/library/default.asp?url=/library/en-us/directx9_c/directx/graphics/reference/DDSFileReference/ddsfileformat.asp
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
0	string/b	DDS\040\174\000\000\000 Microsoft DirectDraw Surface (DDS):
>0	use	ms-directdraw-surface

# Type: Sega PVR image.
# <AUTHOR> <EMAIL>
# References:
# - https://fabiensanglard.net/Mykaruga/tools/segaPVRFormat.txt
# - https://github.com/yazgoo/pvrx2png
# - https://github.com/nickworonekin/puyotools

# Sega PVR header.
0	name	sega-pvr-image-header
>0x0C	leshort	x	%u x
>0x0E	leshort	x	%u
# Image format.
>0x08	byte	0	\b, ARGB1555
>0x08	byte	1	\b, RGB565
>0x08	byte	2	\b, ARGB4444
>0x08	byte	3	\b, YUV442
>0x08	byte	4	\b, Bump
>0x08	byte	5	\b, 4bpp
>0x08	byte	6	\b, 8bpp
# Image data type.
>0x09	byte	0x01	\b, square twiddled
>0x09	byte	0x02	\b, square twiddled & mipmap
>0x09	byte	0x03	\b, VQ
>0x09	byte	0x04	\b, VQ & mipmap
>0x09	byte	0x05	\b, 8-bit CLUT twiddled
>0x09	byte	0x06	\b, 4-bit CLUT twiddled
>0x09	byte	0x07	\b, 8-bit direct twiddled
>0x09	byte	0x08	\b, 4-bit direct twiddled
>0x09	byte	0x09	\b, rectangle
>0x09	byte	0x0B	\b, rectangular stride
>0x09	byte	0x0D	\b, rectangular twiddled
>0x09	byte	0x10	\b, small VQ
>0x09	byte	0x11	\b, small VQ & mipmap
>0x09	byte	0x12	\b, square twiddled & mipmap

# Sega PVR image.
0	string	PVRT
>0x10	string	DDS\040\174\000\000\000 Sega PVR (Xbox) image:
>>0x20	use	ms-directdraw-surface
>0x10	belong	!0x44445320		Sega PVR image:
>>0	use	sega-pvr-image-header

# Sega PVR image with GBIX.
0	string	GBIX
>0x10	string	PVRT
>>0x10	string	DDS\040\174\000\000\000 Sega PVR (Xbox) image:
>>>0x20	use	ms-directdraw-surface
>>0x10	belong	!0x44445320		Sega PVR image:
>>>0x10	use	sega-pvr-image-header
>>0x08	lelong	x	\b, global index = %u

# Sega GVR header.
0	name	sega-gvr-image-header
>0x0C	beshort	x	%u x
>0x0E	beshort	x	%u
# Image data format.
>0x0B	byte	0	\b, I4
>0x0B	byte	1	\b, I8
>0x0B	byte	2	\b, IA4
>0x0B	byte	3	\b, IA8
>0x0B	byte	4	\b, RGB565
>0x0B	byte	5	\b, RGB5A3
>0x0B	byte	6	\b, ARGB8888
>0x0B	byte	8	\b, CI4
>0x0B	byte	9	\b, CI8
>0x0B	byte	14	\b, DXT1

# Sega GVR image.
0	string	GVRT	Sega GVR image:
>0x10	use	sega-gvr-image-header

# Sega GVR image with GBIX.
0	string	GBIX
>0x10	string	GVRT	Sega GVR image:
>>0x10	use	sega-gvr-image-header
>>0x08	belong	x	\b, global index = %u

# Sega GVR image with GCIX. (Wii)
0	string	GCIX
>0x10	string	GVRT	Sega GVR image:
>>0x10	use	sega-gvr-image-header
>>0x08	belong	x	\b, global index = %u

# Light Field Picture
# Documentation: http://optics.miloush.net/lytro/TheFileFormat.aspx
# Typical file extensions: .lfp .lfr .lfx

0	belong	0x894C4650
>4	belong	0x0D0A1A0A
>12	belong	0x00000000	Lytro Light Field Picture
>8	belong	x		\b, version %d

# Type: Vision Research Phantom CINE Format
# URL: https://www.phantomhighspeed.com/
# URL2: http://phantomhighspeed.force.com/vriknowledge/servlet/fileField?id=0BEU0000000Cfyk
# From: Harry Mallon <hjmallon at gmail.com>
#
# This has a short "CI" code but the 44 is the size of the struct which is
# stable
0	string	CI
>2	leshort 44		Vision Research CINE Video,
>>4	leshort	0		Grayscale,
>>4	leshort 1		JPEG Compressed,
>>4	leshort 2		RAW,
>>6	leshort x		version %d,
>>20	lelong	x		%d frames,
>>48	lelong	x		%dx
>>52	lelong	x		\b%d

# Type: ARRI Raw Image
# Info: SMPTE RDD30:2014
# From: Harry Mallon <hjmallon at gmail.com>
0	string ARRI		ARRI ARI image data,
>4	lelong 0x78563412	little-endian,
>4 	lelong 0x12345678	big-endian,
>12	lelong x		version %d,
>20	lelong x 		%dx
>24	lelong x		\b%d

# Type: Khronos KTX texture.
# <AUTHOR> <EMAIL>
# Reference: https://www.khronos.org/opengles/sdk/tools/KTX/file_format_spec/

# glEnum decoding.
# NOTE: Only the most common formats are listed here.
0	name	khronos-ktx-glEnum
>0	lelong	0x1907	\b, RGB
>0	lelong	0x1908	\b, RGBA
>0	lelong	0x1909	\b, LUMINANCE
>0	lelong	0x190A	\b, LUMINANCE_ALPHA
>0	lelong	0x80E1	\b, BGR
>0	lelong	0x80E2	\b, BGRA
>0	lelong	0x83A0	\b, RGB_S3TC
>0	lelong	0x83A1	\b, RGB4_S3TC
>0	lelong	0x83A2	\b, RGBA_S3TC
>0	lelong	0x83A3	\b, RGBA4_S3TC
>0	lelong	0x83A4	\b, RGBA_DXT5_S3TC
>0	lelong	0x83A5	\b, RGBA4_DXT5_S3TC
>0	lelong	0x83F0	\b, COMPRESSED_RGB_S3TC_DXT1_EXT
>0	lelong	0x83F1	\b, COMPRESSED_RGBA_S3TC_DXT1_EXT
>0	lelong	0x83F2	\b, COMPRESSED_RGBA_S3TC_DXT3_EXT
>0	lelong	0x83F3	\b, COMPRESSED_RGBA_S3TC_DXT5_EXT
>0	lelong	0x8D64	\b, ETC1_RGB8_OES
>0	lelong	0x9270	\b, COMPRESSED_R11_EAC
>0	lelong	0x9271	\b, COMPRESSED_SIGNED_R11_EAC
>0	lelong	0x9272	\b, COMPRESSED_RG11_EAC
>0	lelong	0x9273	\b, COMPRESSED_SIGNED_RG11_EAC
>0	lelong	0x9274	\b, COMPRESSED_RGB8_ETC2
>0	lelong	0x9275	\b, COMPRESSED_SRGB8_ETC2
>0	lelong	0x9276	\b, COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2
>0	lelong	0x9277	\b, COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2
>0	lelong	0x9278	\b, COMPRESSED_RGBA2_ETC2_EAC
>0	lelong	0x9279	\b, COMPRESSED_SRGB8_ALPHA8_ETC2_EAC
>0	lelong	0x93B0	\b, COMPRESSED_RGBA_ASTC_4x4_KHR
>0	lelong	0x93B1	\b, COMPRESSED_RGBA_ASTC_5x4_KHR
>0	lelong	0x93B2	\b, COMPRESSED_RGBA_ASTC_5x5_KHR
>0	lelong	0x93B3	\b, COMPRESSED_RGBA_ASTC_6x5_KHR
>0	lelong	0x93B4	\b, COMPRESSED_RGBA_ASTC_6x6_KHR
>0	lelong	0x93B5	\b, COMPRESSED_RGBA_ASTC_8x5_KHR
>0	lelong	0x93B6	\b, COMPRESSED_RGBA_ASTC_8x6_KHR
>0	lelong	0x93B7	\b, COMPRESSED_RGBA_ASTC_8x8_KHR
>0	lelong	0x93B8	\b, COMPRESSED_RGBA_ASTC_10x5_KHR
>0	lelong	0x93B9	\b, COMPRESSED_RGBA_ASTC_10x6_KHR
>0	lelong	0x93BA	\b, COMPRESSED_RGBA_ASTC_10x8_KHR
>0	lelong	0x93BB	\b, COMPRESSED_RGBA_ASTC_10x10_KHR
>0	lelong	0x93BC	\b, COMPRESSED_RGBA_ASTC_12x10_KHR
>0	lelong	0x93BD	\b, COMPRESSED_RGBA_ASTC_12x12_KHR
>0	lelong	0x93D0	\b, COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR
>0	lelong	0x93D1	\b, COMPRESSED_SRGB8_ALPHA8_ASTC_5x4_KHR
>0	lelong	0x93D2	\b, COMPRESSED_SRGB8_ALPHA8_ASTC_5x5_KHR
>0	lelong	0x93D3	\b, COMPRESSED_SRGB8_ALPHA8_ASTC_6x5_KHR
>0	lelong	0x93D4	\b, COMPRESSED_SRGB8_ALPHA8_ASTC_6x6_KHR
>0	lelong	0x93D5	\b, COMPRESSED_SRGB8_ALPHA8_ASTC_8x5_KHR
>0	lelong	0x93D6	\b, COMPRESSED_SRGB8_ALPHA8_ASTC_8x6_KHR
>0	lelong	0x93D7	\b, COMPRESSED_SRGB8_ALPHA8_ASTC_8x8_KHR
>0	lelong	0x93D8	\b, COMPRESSED_SRGB8_ALPHA8_ASTC_10x5_KHR
>0	lelong	0x93D9	\b, COMPRESSED_SRGB8_ALPHA8_ASTC_10x6_KHR
>0	lelong	0x93DA	\b, COMPRESSED_SRGB8_ALPHA8_ASTC_10x8_KHR
>0	lelong	0x93DB	\b, COMPRESSED_SRGB8_ALPHA8_ASTC_10x10_KHR
>0	lelong	0x93DC	\b, COMPRESSED_SRGB8_ALPHA8_ASTC_12x10_KHR
>0	lelong	0x93DD	\b, COMPRESSED_SRGB8_ALPHA8_ASTC_12x12_KHR

# Endian-specific KTX header.
# TODO: glType (all textures I've seen so far are GL_UNSIGNED_BYTE)
0	name	khronos-ktx-endian-header
>20	lelong	x	\b, %u
>24	lelong	>1	x %u
>28	lelong	>1	x %u
>8	lelong	>0
>>8	use	khronos-ktx-glEnum
>8	lelong	0
>>12	use	khronos-ktx-glEnum

# Main KTX header.
# Determine endianness, then check the rest of the header.
0	string	\xABKTX\ 11\xBB\r\n\x1A\n	Khronos KTX texture
>12	lelong	0x04030201			(little-endian)
>>16	use	khronos-ktx-endian-header
>12	belong	0x04030201			(big-endian)
>>16	use	\^khronos-ktx-endian-header

# Type: Khronos KTX2 texture.
# <AUTHOR> <EMAIL>
# Based on draft19.
# Reference: http://github.khronos.org/KTX-Specification/

# Supercompression enum.
0	name	khronos-ktx2-supercompression
>0	lelong	1	Basis Universal
>0	lelong	2	Zstandard

# Vulkan format identifier.
# NOTE: Formats prohibited from KTX2 are commented out.
0	name	khronos-ktx2-vkFormat
>0	lelong	0	UNDEFINED
>0	lelong	1	R4G4_UNORM_PACK8
>0	lelong	2	R4G4B4A4_UNORM_PACK16
>0	lelong	3	B4G4R4A4_UNORM_PACK16
>0	lelong	4	R5G6B5_UNORM_PACK16
>0	lelong	5	B5G6R5_UNORM_PACK16
>0	lelong	6	R5G5B5A1_UNORM_PACK16
>0	lelong	7	B5G5R5A1_UNORM_PACK16
>0	lelong	8	A1R5G5B5_UNORM_PACK16
>0	lelong	9	R8_UNORM
>0	lelong	10	R8_SNORM
#>0	lelong	11	R8_USCALED
#>0	lelong	12	R8_SSCALED
>0	lelong	13	R8_UINT
>0	lelong	14	R8_SINT
>0	lelong	15	R8_SRGB
>0	lelong	16	R8G8_UNORM
>0	lelong	17	R8G8_SNORM
#>0	lelong	18	R8G8_USCALED
#>0	lelong	19	R8G8_SSCALED
>0	lelong	20	R8G8_UINT
>0	lelong	21	R8G8_SINT
>0	lelong	22	R8G8_SRGB
>0	lelong	23	R8G8B8_UNORM
>0	lelong	24	R8G8B8_SNORM
#>0	lelong	25	R8G8B8_USCALED
#>0	lelong	26	R8G8B8_SSCALED
>0	lelong	27	R8G8B8_UINT
>0	lelong	28	R8G8B8_SINT
>0	lelong	29	R8G8B8_SRGB
>0	lelong	30	B8G8R8_UNORM
>0	lelong	31	B8G8R8_SNORM
#>0	lelong	32	B8G8R8_USCALED
#>0	lelong	33	B8G8R8_SSCALED
>0	lelong	34	B8G8R8_UINT
>0	lelong	35	B8G8R8_SINT
>0	lelong	36	B8G8R8_SRGB
>0	lelong	37	R8G8B8A8_UNORM
>0	lelong	38	R8G8B8A8_SNORM
#>0	lelong	39	R8G8B8A8_USCALED
#>0	lelong	40	R8G8B8A8_SSCALED
>0	lelong	41	R8G8B8A8_UINT
>0	lelong	42	R8G8B8A8_SINT
>0	lelong	43	R8G8B8A8_SRGB
>0	lelong	44	B8G8R8A8_UNORM
>0	lelong	45	B8G8R8A8_SNORM
#>0	lelong	46	B8G8R8A8_USCALED
#>0	lelong	47	B8G8R8A8_SSCALED
>0	lelong	48	B8G8R8A8_UINT
>0	lelong	49	B8G8R8A8_SINT
>0	lelong	50	B8G8R8A8_SRGB
#>0	lelong	51	A8B8G8R8_UNORM_PACK32
#>0	lelong	52	A8B8G8R8_SNORM_PACK32
#>0	lelong	53	A8B8G8R8_USCALED_PACK32
#>0	lelong	54	A8B8G8R8_SSCALED_PACK32
#>0	lelong	55	A8B8G8R8_UINT_PACK32
#>0	lelong	56	A8B8G8R8_SINT_PACK32
#>0	lelong	57	A8B8G8R8_SRGB_PACK32
>0	lelong	58	A2R10G10B10_UNORM_PACK32
>0	lelong	59	A2R10G10B10_SNORM_PACK32
#>0	lelong	60	A2R10G10B10_USCALED_PACK32
#>0	lelong	61	A2R10G10B10_SSCALED_PACK32
>0	lelong	62	A2R10G10B10_UINT_PACK32
>0	lelong	63	A2R10G10B10_SINT_PACK32
>0	lelong	64	A2B10G10R10_UNORM_PACK32
>0	lelong	65	A2B10G10R10_SNORM_PACK32
#>0	lelong	66	A2B10G10R10_USCALED_PACK32
#>0	lelong	67	A2B10G10R10_SSCALED_PACK32
>0	lelong	68	A2B10G10R10_UINT_PACK32
>0	lelong	69	A2B10G10R10_SINT_PACK32
>0	lelong	70	R16_UNORM
>0	lelong	71	R16_SNORM
#>0	lelong	72	R16_USCALED
#>0	lelong	73	R16_SSCALED
>0	lelong	74	R16_UINT
>0	lelong	75	R16_SINT
>0	lelong	76	R16_SFLOAT
>0	lelong	77	R16G16_UNORM
>0	lelong	78	R16G16_SNORM
#>0	lelong	79	R16G16_USCALED
#>0	lelong	80	R16G16_SSCALED
>0	lelong	81	R16G16_UINT
>0	lelong	82	R16G16_SINT
>0	lelong	83	R16G16_SFLOAT
>0	lelong	84	R16G16B16_UNORM
>0	lelong	85	R16G16B16_SNORM
#>0	lelong	86	R16G16B16_USCALED
#>0	lelong	87	R16G16B16_SSCALED
>0	lelong	88	R16G16B16_UINT
>0	lelong	89	R16G16B16_SINT
>0	lelong	90	R16G16B16_SFLOAT
>0	lelong	91	R16G16B16A16_UNORM
>0	lelong	92	R16G16B16A16_SNORM
#>0	lelong	93	R16G16B16A16_USCALED
#>0	lelong	94	R16G16B16A16_SSCALED
>0	lelong	95	R16G16B16A16_UINT
>0	lelong	96	R16G16B16A16_SINT
>0	lelong	97	R16G16B16A16_SFLOAT
>0	lelong	98	R32_UINT
>0	lelong	99	R32_SINT
>0	lelong	100	R32_SFLOAT
>0	lelong	101	R32G32_UINT
>0	lelong	102	R32G32_SINT
>0	lelong	103	R32G32_SFLOAT
>0	lelong	104	R32G32B32_UINT
>0	lelong	105	R32G32B32_SINT
>0	lelong	106	R32G32B32_SFLOAT
>0	lelong	107	R32G32B32A32_UINT
>0	lelong	108	R32G32B32A32_SINT
>0	lelong	109	R32G32B32A32_SFLOAT
>0	lelong	110	R64_UINT
>0	lelong	111	R64_SINT
>0	lelong	112	R64_SFLOAT
>0	lelong	113	R64G64_UINT
>0	lelong	114	R64G64_SINT
>0	lelong	115	R64G64_SFLOAT
>0	lelong	116	R64G64B64_UINT
>0	lelong	117	R64G64B64_SINT
>0	lelong	118	R64G64B64_SFLOAT
>0	lelong	119	R64G64B64A64_UINT
>0	lelong	120	R64G64B64A64_SINT
>0	lelong	121	R64G64B64A64_SFLOAT
>0	lelong	122	B10G11R11_UFLOAT_PACK32
>0	lelong	123	E5B9G9R9_UFLOAT_PACK32
>0	lelong	124	D16_UNORM
>0	lelong	125	X8_D24_UNORM_PACK32
>0	lelong	126	D32_SFLOAT
>0	lelong	127	S8_UINT
>0	lelong	128	D16_UNORM_S8_UINT
>0	lelong	129	D24_UNORM_S8_UINT
>0	lelong	130	D32_SFLOAT_S8_UINT

>0	lelong	131	BC1_RGB_UNORM_BLOCK
>0	lelong	132	BC1_RGB_SRGB_BLOCK
>0	lelong	133	BC1_RGBA_UNORM_BLOCK
>0	lelong	134	BC1_RGBA_SRGB_BLOCK
>0	lelong	135	BC2_UNORM_BLOCK
>0	lelong	136	BC2_SRGB_BLOCK
>0	lelong	137	BC3_UNORM_BLOCK
>0	lelong	138	BC3_SRGB_BLOCK
>0	lelong	139	BC4_UNORM_BLOCK
>0	lelong	140	BC4_SNORM_BLOCK
>0	lelong	141	BC5_UNORM_BLOCK
>0	lelong	142	BC5_SNORM_BLOCK
>0	lelong	143	BC6H_UFLOAT_BLOCK
>0	lelong	144	BC6H_SFLOAT_BLOCK
>0	lelong	145	BC7_UNORM_BLOCK
>0	lelong	146	BC7_SRGB_BLOCK

>0	lelong	147	ETC2_R8G8B8_UNORM_BLOCK
>0	lelong	148	ETC2_R8G8B8_SRGB_BLOCK
>0	lelong	149	ETC2_R8G8B8A1_UNORM_BLOCK
>0	lelong	150	ETC2_R8G8B8A1_SRGB_BLOCK
>0	lelong	151	ETC2_R8G8B8A8_UNORM_BLOCK
>0	lelong	152	ETC2_R8G8B8A8_SRGB_BLOCK

>0	lelong	153	EAC_R11_UNORM_BLOCK
>0	lelong	154	EAC_R11_SNORM_BLOCK
>0	lelong	155	EAC_R11G11_UNORM_BLOCK
>0	lelong	156	EAC_R11G11_SNORM_BLOCK

>0	lelong	157	ASTC_4x4_UNORM_BLOCK
>0	lelong	158	ASTC_4x4_SRGB_BLOCK
>0	lelong	159	ASTC_5x4_UNORM_BLOCK
>0	lelong	160	ASTC_5x4_SRGB_BLOCK
>0	lelong	161	ASTC_5x5_UNORM_BLOCK
>0	lelong	162	ASTC_5x5_SRGB_BLOCK
>0	lelong	163	ASTC_6x5_UNORM_BLOCK
>0	lelong	164	ASTC_6x5_SRGB_BLOCK
>0	lelong	165	ASTC_6x6_UNORM_BLOCK
>0	lelong	166	ASTC_6x6_SRGB_BLOCK
>0	lelong	167	ASTC_8x5_UNORM_BLOCK
>0	lelong	168	ASTC_8x5_SRGB_BLOCK
>0	lelong	169	ASTC_8x6_UNORM_BLOCK
>0	lelong	170	ASTC_8x6_SRGB_BLOCK
>0	lelong	171	ASTC_8x8_UNORM_BLOCK
>0	lelong	172	ASTC_8x8_SRGB_BLOCK
>0	lelong	173	ASTC_10x5_UNORM_BLOCK
>0	lelong	174	ASTC_10x5_SRGB_BLOCK
>0	lelong	175	ASTC_10x6_UNORM_BLOCK
>0	lelong	176	ASTC_10x6_SRGB_BLOCK
>0	lelong	177	ASTC_10x8_UNORM_BLOCK
>0	lelong	178	ASTC_10x8_SRGB_BLOCK
>0	lelong	179	ASTC_10x10_UNORM_BLOCK
>0	lelong	180	ASTC_10x10_SRGB_BLOCK
>0	lelong	181	ASTC_12x10_UNORM_BLOCK
>0	lelong	182	ASTC_12x10_SRGB_BLOCK
>0	lelong	183	ASTC_12x12_UNORM_BLOCK
>0	lelong	184	ASTC_12x12_SRGB_BLOCK

>0	lelong	1000156000	G8B8G8R8_422_UNORM
>0	lelong	1000156001	B8G8R8G8_422_UNORM
>0	lelong	1000156002	G8_B8_R8_3PLANE_420_UNORM
>0	lelong	1000156003	G8_B8R8_2PLANE_420_UNORM
>0	lelong	1000156004	G8_B8_R8_3PLANE_422_UNORM
>0	lelong	1000156005	G8_B8R8_2PLANE_422_UNORM
>0	lelong	1000156006	G8_B8_R8_3PLANE_444_UNORM
>0	lelong	1000156007	R10X6_UNORM_PACK16
>0	lelong	1000156008	R10X6G10X6_UNORM_2PACK16
>0	lelong	1000156009	R10X6G10X6B10X6A10X6_UNORM_4PACK16
>0	lelong	1000156010	G10X6B10X6G10X6R10X6_422_UNORM_4PACK16
>0	lelong	1000156011	B10X6G10X6R10X6G10X6_422_UNORM_4PACK16
>0	lelong	1000156012	G10X6_B10X6_R10X6_3PLANE_420_UNORM_3PACK16
>0	lelong	1000156013	G10X6_B10X6R10X6_2PLANE_420_UNORM_3PACK16
>0	lelong	1000156014	G10X6_B10X6_R10X6_3PLANE_422_UNORM_3PACK16
>0	lelong	1000156015	G10X6_B10X6R10X6_2PLANE_422_UNORM_3PACK16
>0	lelong	1000156016	G10X6_B10X6_R10X6_3PLANE_444_UNORM_3PACK16
>0	lelong	1000156017	R12X4_UNORM_PACK16
>0	lelong	1000156018	R12X4G12X4_UNORM_2PACK16
>0	lelong	1000156019	R12X4G12X4B12X4A12X4_UNORM_4PACK16
>0	lelong	1000156020	G12X4B12X4G12X4R12X4_422_UNORM_4PACK16
>0	lelong	1000156021	B12X4G12X4R12X4G12X4_422_UNORM_4PACK16
>0	lelong	1000156022	G12X4_B12X4_R12X4_3PLANE_420_UNORM_3PACK16
>0	lelong	1000156023	G12X4_B12X4R12X4_2PLANE_420_UNORM_3PACK16
>0	lelong	1000156024	G12X4_B12X4_R12X4_3PLANE_422_UNORM_3PACK16
>0	lelong	1000156025	G12X4_B12X4R12X4_2PLANE_422_UNORM_3PACK16
>0	lelong	1000156026	G12X4_B12X4_R12X4_3PLANE_444_UNORM_3PACK16
>0	lelong	1000156027	G16B16G16R16_422_UNORM
>0	lelong	1000156028	B16G16R16G16_422_UNORM
>0	lelong	1000156029	G16_B16_R16_3PLANE_420_UNORM
>0	lelong	1000156030	G16_B16R16_2PLANE_420_UNORM
>0	lelong	1000156031	G16_B16_R16_3PLANE_422_UNORM
>0	lelong	1000156032	G16_B16R16_2PLANE_422_UNORM
>0	lelong	1000156033	G16_B16_R16_3PLANE_444_UNORM

>0	lelong	1000054000	PVRTC1_2BPP_UNORM_BLOCK_IMG
>0	lelong	1000054001	PVRTC1_4BPP_UNORM_BLOCK_IMG
>0	lelong	1000054002	PVRTC2_2BPP_UNORM_BLOCK_IMG
>0	lelong	1000054003	PVRTC2_4BPP_UNORM_BLOCK_IMG
>0	lelong	1000054004	PVRTC1_2BPP_SRGB_BLOCK_IMG
>0	lelong	1000054005	PVRTC1_4BPP_SRGB_BLOCK_IMG
>0	lelong	1000054006	PVRTC2_2BPP_SRGB_BLOCK_IMG
>0	lelong	1000054007	PVRTC2_4BPP_SRGB_BLOCK_IMG

>0	lelong	1000066000	ASTC_4x4_SFLOAT_BLOCK_EXT
>0	lelong	1000066001	ASTC_5x4_SFLOAT_BLOCK_EXT
>0	lelong	1000066002	ASTC_5x5_SFLOAT_BLOCK_EXT
>0	lelong	1000066003	ASTC_6x5_SFLOAT_BLOCK_EXT
>0	lelong	1000066004	ASTC_6x6_SFLOAT_BLOCK_EXT
>0	lelong	1000066005	ASTC_8x5_SFLOAT_BLOCK_EXT
>0	lelong	1000066006	ASTC_8x6_SFLOAT_BLOCK_EXT
>0	lelong	1000066007	ASTC_8x8_SFLOAT_BLOCK_EXT
>0	lelong	1000066008	ASTC_10x5_SFLOAT_BLOCK_EXT
>0	lelong	1000066009	ASTC_10x6_SFLOAT_BLOCK_EXT
>0	lelong	1000066010	ASTC_10x8_SFLOAT_BLOCK_EXT
>0	lelong	1000066011	ASTC_10x10_SFLOAT_BLOCK_EXT
>0	lelong	1000066012	ASTC_12x10_SFLOAT_BLOCK_EXT
>0	lelong	1000066013	ASTC_12x12_SFLOAT_BLOCK_EXT

# Main KTX2 header.
0	string	\xABKTX\ 20\xBB\r\n\x1A\n	Khronos KTX2 texture
>20	lelong	x	\b, %u
>24	lelong	>1	x %u
>28	lelong	>1	x %u
>32	lelong	>1	\b, %u layers
>36	lelong	>1	\b, %u faces
>40	lelong	>1	\b, %u mipmaps
>44	lelong	>0	\b,
>>44	use	khronos-ktx2-supercompression
>12	lelong	>0	\b,
>>12	use	khronos-ktx2-vkFormat

# Type: Valve VTF texture.
# <AUTHOR> <EMAIL>
# References:
# - https://developer.valvesoftware.com/wiki/Valve_Texture_Format

# VTF image formats.
0	name	vtf-image-format
>0	lelong	0	RGBA8888
>0	lelong	1	ABGR8888
>0	lelong	2	RGB888
>0	lelong	3	BGR888
>0	lelong	4	RGB565
>0	lelong	5	I8
>0	lelong	6	IA88
>0	lelong	7	P8
>0	lelong	8	A8
>0	lelong	9	RGB888 (bluescreen)
>0	lelong	10	BGR888 (bluescreen)
>0	lelong	11	ARGB8888
>0	lelong	12	BGRA8888
>0	lelong	13	DXT1
>0	lelong	14	DXT3
>0	lelong	15	DXT5
>0	lelong	16	BGRx8888
>0	lelong	17	BGR565
>0	lelong	18	BGRx5551
>0	lelong	19	BGRA4444
>0	lelong	20	DXT1+A1
>0	lelong	21	BGRA5551
>0	lelong	22	UV88
>0	lelong	23	UVWQ8888
>0	lelong	24	RGBA16161616F
>0	lelong	25	RGBA16161616
>0	lelong	26	UVLX8888

# Main VTF header.
0	string	VTF\0				Valve Texture Format
>4	lelong	x				v%u
>8	lelong	x				\b.%u
>0x10	leshort	x				\b, %u
>0x12	leshort	>1				x %u
>4	lequad	0x0000000700000002
>>0x3F	leshort	>1				x %u
>0x18	leshort	>1				\b, %u frames
>0x38	byte	x				\b, mipmaps: %u
>0x34	lelong	>-1				\b,
>>0x34	use	vtf-image-format

# Type: Valve VTF3 (PS3) texture.
# <AUTHOR> <EMAIL>
0	string		VTF3	Valve Texture Format (PS3)
>0x14	beshort		x	\b, %u
>0x16	beshort		x	\b x %u
>0x10	belong&0x2000	0	\b, DXT1
>0x10	belong&0x2000	0x2000	\b, DXT5

# Type: ASTC texture.
# <AUTHOR> <EMAIL>
# References:
# - https://stackoverflow.com/questions/22600678/determine-internal-format-of-given-astc-compressed-image-through-its-header
# - https://stackoverflow.com/a/22682244
0	lelong	0x5ca1ab13			ASTC
>4	byte	x				%u
>5	byte	x				\bx%u
>6	byte	>1				\bx%u
# X, Y, and Z dimensions are stored as 24-bit LE.
# Pretend it's 32-bit and mask off the high byte.
>7	lelong&0x00FFFFFF	x		texture, %u
>10	lelong&0x00FFFFFF	x		x %u
>13	lelong&0x00FFFFFF	>1		x %u

# Zebra Metafile graphic
# http://www.fileformat.info/format/zbr/egff.htm
0	beshort	0x9a02	Zebra Metafile graphic
>2	leshort 1	(version 1.x)
>2	leshort	2	(version 1.1x or 1.2x)
>2	leshort	3	(version 1.49)
>2	leshort	4	(version 1.50)
>4	string	x	(comment = %s)

# Microsoft Paint graphic
# http://www.fileformat.info/format/mspaint/egff.htm
0	string	DanM 	icrosoft Paint image data (version 1.x)
>4	leshort	x	(%d
>>6	leshort	x	x %d)
0	string	LinS 	Microsoft Paint image data (version 2.0)
>4	leshort	x	(%d
>>6	leshort	x	x %d)

# reMarkable tablet internal file format (https://www.remarkable.com/)
# https://github.com/ax3l/lines-are-beautiful
# https://plasma.ninja/blog/devices/remarkable/binary/format/2017/12/26/\
#	reMarkable-lines-file-format.html#what-to-do-next
# from Axel Huebl
0		string	reMarkable
>11		string	lines
>>17		string	with
>>>22		string	selections
>>>>33		string	and
>>>>>37		string	layers
>>>>>>43	lelong	x	reMarkable tablet notebook lines, 1404 x 1872, %x page(s)

# newer per-page files for the reMarkable
0		string	reMarkable
>11		string	.lines
>>18		string	file,
>>>24		string	version=
>>>>32		byte	x	reMarkable tablet page (v%c), 1404 x 1872,
>>>>>43		lelong	x	%d layer(s)

# Type: PVR3 texture.
# <AUTHOR> <EMAIL>
# References:
# - http://cdn.imgtec.com/sdk-documentation/PVR+File+Format.Specification.pdf

# PVR3 pixel formats.
0	name		pvr3-pixel-format
>0	ulelong	0	PVRTC 2bpp RGB
>0	ulelong	1	PVRTC 2bpp RGBA
>0	ulelong	2	PVRTC 4bpp RGB
>0	ulelong	3	PVRTC 4bpp RGBA
>0	ulelong	4	PVRTC-II 2bpp
>0	ulelong	5	PVRTC-II 4bpp
>0	ulelong	6	ETC1
>0	ulelong	7	DXT1
>0	ulelong	8	DXT2
>0	ulelong	9	DXT3
>0	ulelong	10	DXT4
>0	ulelong	11	DXT5
>0	ulelong	12	BC4
>0	ulelong	13	BC5
>0	ulelong	14	BC6
>0	ulelong	15	BC7
>0	ulelong	16	UYVY
>0	ulelong	17	YUY2
>0	ulelong	18	BW1bpp
>0	ulelong	19	R9G9B9E5 Shared Exponent
>0	ulelong	20	RGBG8888
>0	ulelong	21	GRGB8888
>0	ulelong	22	ETC2 RGB
>0	ulelong	23	ETC2 RGBA
>0	ulelong	24	ETC2 RGB A1
>0	ulelong	25	EAC R11
>0	ulelong	26	EAC RG11
>0	ulelong	27	ASTC_4x4
>0	ulelong	28	ASTC_5x4
>0	ulelong	29	ASTC_5x5
>0	ulelong	30	ASTC_6x5
>0	ulelong	31	ASTC_6x6
>0	ulelong	32	ASTC_8x5
>0	ulelong	33	ASTC_8x6
>0	ulelong	34	ASTC_8x8
>0	ulelong	35	ASTC_10x5
>0	ulelong	36	ASTC_10x6
>0	ulelong	37	ASTC_10x8
>0	ulelong	38	ASTC_10x10
>0	ulelong	39	ASTC_12x10
>0	ulelong	40	ASTC_12x12
>0	ulelong	41	ASTC_3x3x3
>0	ulelong	42	ASTC_4x3x3
>0	ulelong	43	ASTC_4x4x3
>0	ulelong	44	ASTC_4x4x4
>0	ulelong	45	ASTC_5x4x4
>0	ulelong	46	ASTC_5x5x4
>0	ulelong	47	ASTC_5x5x5
>0	ulelong	48	ASTC_6x5x5
>0	ulelong	49	ASTC_6x6x5
>0	ulelong	50	ASTC_6x6x6

0	string		PVR\x03			PowerVR 3.0 texture:
>0x18	ulelong		x	%u x
>0x1C	ulelong		x	%u
>0x20	ulelong		>1	x %u
>0x08	byte		x	\b,
>0x0C	ulelong		0
>>0x08	use	pvr3-pixel-format
>0x0C	ulelong		!0
>>0x08	byte	!0	%c
>>>0x0C	byte	!0	\b%u
>>0x09	byte	!0	\b%c
>>>0x0D	byte	!0	\b%u
>>0x0A	byte	!0	\b%c
>>>0x0E	byte	!0	\b%u
>>0x0B	byte	!0	\b%c
>>>0x0F	byte	!0	\b%u
>0x10	ulelong		1	\b, sRGB
>0x04	ulelong&0x02	0x02	\b, premultiplied alpha

0	string		\x03RVP			PowerVR 3.0 texture: BE,
>0x18	ubelong		x	%u x
>0x1C	ubelong		x	%u
>0x20	ubelong		>1	x %u
>0x08	byte		x	\b,
>0x0C	ubelong		0
>>0x08	use	pvr3-pixel-format
>0x0C	ubelong		!0
>>0x0B	byte	!0	%c
>>>0x0F	byte	!0	\b%u
>>0x0A	byte	!0	\b%c
>>>0x0E	byte	!0	\b%u
>>0x09	byte	!0	\b%c
>>>0x0D	byte	!0	\b%u
>>0x08	byte	!0	\b%c
>>>0x0C	byte	!0	\b%u
>0x10	ubelong		1	\b, sRGB
>0x04	ubelong&0x02	0x02	\b, premultiplied alpha

# Type: Microsoft Xbox XPR0 texture.
# <AUTHOR> <EMAIL>
# References:
# - https://github.com/Cxbx-Reloaded/Cxbx-Reloaded/blob/develop/src/core/hle/D3D8/XbD3D8Types.h

# XPR pixel formats.
0	name	xbox-xpr-pixel-format
>0	byte	0x00	L8
>0	byte	0x01	AL8
>0	byte	0x02	ARGB1555
>0	byte	0x03	RGB555
>0	byte	0x04	ARGB4444
>0	byte	0x05	RGB565
>0	byte	0x06	ARGB8888
>0	byte	0x07	xRGB8888
>0	byte	0x0B	P8
>0	byte	0x0C	DXT1
>0	byte	0x0E	DXT2
>0	byte	0x0F	DXT4
>0	byte	0x10	Linear ARGB1555
>0	byte	0x11	Linear RGB565
>0	byte	0x12	Linear ARGB8888
>0	byte	0x13	Linear L8
>0	byte	0x16	Linear R8B8
>0	byte	0x17	Linear G8B8
>0	byte	0x19	A8
>0	byte	0x1A	A8L8
>0	byte	0x1B	Linear AL8
>0	byte	0x1C	Linear RGB555
>0	byte	0x1D	Linear ARGB4444
>0	byte	0x1E	Linear xRGB8888
>0	byte	0x1F	Linear A8
>0	byte	0x20	Linear A8L8
>0	byte	0x24	YUY2
>0	byte	0x25	UYVY
>0	byte	0x27	L6V5U5
>0	byte	0x28	V8U8
>0	byte	0x29	R8B8
>0	byte	0x2A	D24S8
>0	byte	0x2B	F24S8
>0	byte	0x2C	D16
>0	byte	0x2D	F16
>0	byte	0x2E	Linear D24S8
>0	byte	0x2F	Linear F24S8
>0	byte	0x30	Linear D16
>0	byte	0x31	Linear F16
>0	byte	0x32	L16
>0	byte	0x33	V16U16
>0	byte	0x35	Linear L16
>0	byte	0x36	Linear V16U16
>0	byte	0x37	Linear L6V5U5
>0	byte	0x38	RGBA5551
>0	byte	0x39	RGBA4444
>0	byte	0x3A	QWVU8888
>0	byte	0x3B	BGRA8888
>0	byte	0x3C	RGBA8888
>0	byte	0x3D	Linear RGBA5551
>0	byte	0x3E	Linear RGBA4444
>0	byte	0x3F	Linear ABGR8888
>0	byte	0x40	Linear BGRA8888
>0	byte	0x41	Linear RGBA8888
>0	byte	0x64	Vertex Data

0	string		XPR0	Microsoft Xbox XPR0 texture
>0x19	byte	x	\b, format:
>>0x19	use	xbox-xpr-pixel-format

# ILDA Image Data Transfer Format
# https://www.ilda.com/resources/StandardsDocs/ILDA_IDTF14_rev011.pdf
#
# Updated by Chuck Hein (<EMAIL>)
#
0	string	ILDA	ILDA Image Data Transfer Format
>7	byte	0x00	3D Coordinates with Indexed Color
>7	byte	0x01	2D Coordinates with Indexed Color
>7	byte	0x02	Color Palette
>7	byte	0x04	3D Coordinates with True Color
>7	byte	0x05	2D Coordinates with True Color
>8	string	>0	\b, palette %s
>16	string	>0	\b, company %s
>24	beshort	>0	\b, number of records %d
>>26	beshort	x	\b, palette number %d
>>28	beshort	>0	\b, number of frames %d
>>30	byte	>0	\b, projector number %d

# Dropbox "lepton" compressed jpeg format
# https://github.com/dropbox/lepton
0	belong&0xfffff0ff	0xcf84005a	Lepton image file
>2	byte			x		(version %d)

# Apple QuickTake camera raw images
# https://en.wikipedia.org/wiki/Apple_QuickTake
# dcraw can decode them
0	name quicktake
>4	belong	8
>>544	beshort	x	\b, %dx
>>546	beshort	x	\b%d
>4	belong	4
>>546	beshort	x	\b, %dx
>>544	beshort	x	\b%d

0	string	qktk	Apple QuickTake 100 Raw Image
>0	use quicktake

0	string	qktn
>4	byte	0	Apple QuickTake 150 Raw Image
>4	byte	>0	Apple QuickTake 200 Raw Image
>0	use quicktake

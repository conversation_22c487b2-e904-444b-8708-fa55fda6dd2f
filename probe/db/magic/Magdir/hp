
#------------------------------------------------------------------------------
# $File: hp,v 1.25 2019/01/13 00:32:38 christos Exp $
# hp:  file(1) magic for Hewlett Packard machines (see also "printer")
#
# XXX - somebody should figure out whether any byte order needs to be
# applied to the "TML" stuff; I'm assuming the Apollo stuff is
# big-endian as it was mostly 68K-based.
#
# I think the 500 series was the old stack-based machines, running a
# UNIX environment atop the "SUN kernel"; dunno whether it was
# big-endian or little-endian.
#
# <PERSON> (<EMAIL>): hp200 machines are 68010 based;
# hp300 are 68020+68881 based; hp400 are also 68k.  The following basic
# HP magic is useful for reference, but using "long" magic is a better
# practice in order to avoid collisions.
#
# <PERSON> (<EMAIL>): some additions to this list came from
# HP-UX 10.0's "/usr/include/sys/unistd.h" (68030, 68040, PA-RISC 1.1,
# 1.2, and 2.0).  The 1.2 and 2.0 stuff isn't in the HP-UX 10.0
# "/etc/magic", though, except for the "archive file relocatable library"
# stuff, and the 68030 and 68040 stuff isn't there at all - are they not
# used in executables, or have they just not yet updated "/etc/magic"
# completely?
#
# 0	beshort		200		hp200 (68010) BSD binary
# 0	beshort		300		hp300 (68020+68881) BSD binary
# 0	beshort		0x20c		hp200/300 HP-UX binary
# 0	beshort		0x20d		hp400 (68030) HP-UX binary
# 0	beshort		0x20e		hp400 (68040?) HP-UX binary
# 0	beshort		0x20b		PA-RISC1.0 HP-UX binary
# 0	beshort		0x210		PA-RISC1.1 HP-UX binary
# 0	beshort		0x211		PA-RISC1.2 HP-UX binary
# 0	beshort		0x214		PA-RISC2.0 HP-UX binary

#
# The "misc" stuff needs a byte order; the archives look suspiciously
# like the old 177545 archives (0xff65 = 0177545).
#
#### Old Apollo stuff
0	beshort		0627		Apollo m68k COFF executable
>18	beshort		^040000		not stripped
>22	beshort		>0		- version %d
0	beshort		0624		apollo a88k COFF executable
>18	beshort		^040000		not stripped
>22	beshort		>0		- version %d
0       long            01203604016     TML 0123 byte-order format
0       long            01702407010     TML 1032 byte-order format
0       long            01003405017     TML 2301 byte-order format
0       long            01602007412     TML 3210 byte-order format
#### PA-RISC 1.1
0	belong 		0x02100106	PA-RISC1.1 relocatable object
0	belong 		0x02100107	PA-RISC1.1 executable
>168	belong		&0x00000004	dynamically linked
>(144)	belong		0x054ef630	dynamically linked
>96	belong		>0		- not stripped

0	belong 		0x02100108	PA-RISC1.1 shared executable
>168	belong&0x4	0x4		dynamically linked
>(144)	belong		0x054ef630	dynamically linked
>96	belong		>0		- not stripped

0	belong 		0x0210010b	PA-RISC1.1 demand-load executable
>168	belong&0x4	0x4		dynamically linked
>(144)	belong		0x054ef630	dynamically linked
>96	belong		>0		- not stripped

0	belong 		0x0210010e	PA-RISC1.1 shared library
>96	belong		>0		- not stripped

0	belong 		0x0210010d	PA-RISC1.1 dynamic load library
>96	belong		>0		- not stripped

#### PA-RISC 2.0
0	belong		0x02140106	PA-RISC2.0 relocatable object

0       belong		0x02140107	PA-RISC2.0 executable
>168	belong		&0x00000004	dynamically linked
>(144)	belong		0x054ef630	dynamically linked
>96	belong		>0		- not stripped

0       belong		0x02140108	PA-RISC2.0 shared executable
>168	belong		&0x00000004	dynamically linked
>(144)	belong		0x054ef630	dynamically linked
>96	belong		>0		- not stripped

0       belong		0x0214010b	PA-RISC2.0 demand-load executable
>168	belong		&0x00000004	dynamically linked
>(144)	belong		0x054ef630	dynamically linked
>96	belong		>0		- not stripped

0       belong		0x0214010e	PA-RISC2.0 shared library
>96	belong		>0		- not stripped

0       belong		0x0214010d	PA-RISC2.0 dynamic load library
>96	belong		>0		- not stripped

#### 800
0	belong 		0x020b0106	PA-RISC1.0 relocatable object

0	belong 		0x020b0107	PA-RISC1.0 executable
>168	belong&0x4	0x4		dynamically linked
>(144)	belong		0x054ef630	dynamically linked
>96	belong		>0		- not stripped

0	belong 		0x020b0108	PA-RISC1.0 shared executable
>168	belong&0x4	0x4		dynamically linked
>(144)	belong		0x054ef630	dynamically linked
>96	belong		>0		- not stripped

0	belong 		0x020b010b	PA-RISC1.0 demand-load executable
>168	belong&0x4	0x4		dynamically linked
>(144)	belong		0x054ef630	dynamically linked
>96	belong		>0		- not stripped

0	belong 		0x020b010e	PA-RISC1.0 shared library
>96	belong		>0		- not stripped

0	belong 		0x020b010d	PA-RISC1.0 dynamic load library
>96	belong		>0		- not stripped

#### 500
0	long		0x02080106	HP s500 relocatable executable
>16	long		>0		- version %d

0	long		0x02080107	HP s500 executable
>16	long		>0		- version %d

0	long		0x02080108	HP s500 pure executable
>16	long		>0		- version %d

#### 200
0	belong 		0x020c0108	HP s200 pure executable
>4	beshort		>0		- version %d
>8	belong		&0x80000000	save fp regs
>8	belong		&0x40000000	dynamically linked
>8	belong		&0x20000000	debuggable
>36	belong		>0		not stripped

0	belong		0x020c0107	HP s200 executable
>4	beshort		>0		- version %d
>8	belong		&0x80000000	save fp regs
>8	belong		&0x40000000	dynamically linked
>8	belong		&0x20000000	debuggable
>36	belong		>0		not stripped

0	belong		0x020c010b	HP s200 demand-load executable
>4	beshort		>0		- version %d
>8	belong		&0x80000000	save fp regs
>8	belong		&0x40000000	dynamically linked
>8	belong		&0x20000000	debuggable
>36	belong		>0		not stripped

0	belong		0x020c0106	HP s200 relocatable executable
>4	beshort		>0		- version %d
>6	beshort		>0		- highwater %d
>8	belong		&0x80000000	save fp regs
>8	belong		&0x20000000	debuggable
>8	belong		&0x10000000	PIC

0	belong 		0x020a0108	HP s200 (2.x release) pure executable
>4	beshort		>0		- version %d
>36	belong		>0		not stripped

0	belong		0x020a0107	HP s200 (2.x release) executable
>4	beshort		>0		- version %d
>36	belong		>0		not stripped

0	belong		0x020c010e	HP s200 shared library
>4	beshort		>0		- version %d
>6	beshort		>0		- highwater %d
>36	belong		>0		not stripped

0	belong		0x020c010d	HP s200 dynamic load library
>4	beshort		>0		- version %d
>6	beshort		>0		- highwater %d
>36	belong		>0		not stripped

#### MISC
0	long		0x0000ff65	HP old archive
0	long		0x020aff65	HP s200 old archive
0	long		0x020cff65	HP s200 old archive
0	long		0x0208ff65	HP s500 old archive

0	long		0x015821a6	HP core file

0	long		0x4da7eee8	HP-WINDOWS font
>8	byte		>0		- version %d
0	string		Bitmapfile	HP Bitmapfile

0	string		IMGfile	CIS 	compimg HP Bitmapfile
# XXX - see "lif"
#0	short		0x8000		lif file
0	long		0x020c010c	compiled Lisp

0	string		msgcat01	HP NLS message catalog,
>8	long		>0		%d messages

# Summary: HP-48/49 calculator
# Created by: <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL> (HP49 support)
0	string		HPHP		HP
>4	string		48		48 binary
>4	string		49		49 binary
>7	byte		>64		- Rev %c
>8	leshort		0x2911		(ADR)
>8	leshort		0x2933		(REAL)
>8	leshort		0x2955		(LREAL)
>8	leshort		0x2977		(COMPLX)
>8	leshort		0x299d		(LCOMPLX)
>8	leshort		0x29bf		(CHAR)
>8	leshort		0x29e8		(ARRAY)
>8	leshort		0x2a0a		(LNKARRAY)
>8	leshort		0x2a2c		(STRING)
>8	leshort		0x2a4e		(HXS)
>8	leshort		0x2a74		(LIST)
>8	leshort		0x2a96		(DIR)
>8	leshort		0x2ab8		(ALG)
>8	leshort		0x2ada		(UNIT)
>8	leshort		0x2afc		(TAGGED)
>8	leshort		0x2b1e		(GROB)
>8	leshort		0x2b40		(LIB)
>8	leshort		0x2b62		(BACKUP)
>8	leshort		0x2b88		(LIBDATA)
>8	leshort		0x2d9d		(PROG)
>8	leshort		0x2dcc		(CODE)
>8	leshort		0x2e48		(GNAME)
>8	leshort		0x2e6d		(LNAME)
>8	leshort		0x2e92		(XLIB)

0	string		%%HP:		HP text
>6	string		T(0)		- T(0)
>6	string		T(1)		- T(1)
>6	string		T(2)		- T(2)
>6	string		T(3)		- T(3)
>10	string		A(D)		A(D)
>10	string		A(R)		A(R)
>10	string		A(G)		A(G)
>14	string		F(.)		F(.);
>14	string		F(,)		F(,);


# Summary: HP-38/39 calculator
# <AUTHOR> <EMAIL>
0	string		HP3
>3	string		8		HP 38
>3	string		9		HP 39
>4	string		Bin		binary
>4	string		Asc		ASCII
>7	string		A		(Directory List)
>7	string		B		(Zaplet)
>7	string		C		(Note)
>7	string		D		(Program)
>7	string		E		(Variable)
>7	string		F		(List)
>7	string		G		(Matrix)
>7	string		H		(Library)
>7	string		I		(Target List)
>7	string		J		(ASCII Vector specification)
>7	string		K		(wildcard)

# Summary: HP-38/39 calculator
# <AUTHOR> <EMAIL>
0	string		HP3
>3	string		8		HP 38
>3	string		9		HP 39
>4	string		Bin		binary
>4	string		Asc		ASCII
>7	string		A		(Directory List)
>7	string		B		(Zaplet)
>7	string		C		(Note)
>7	string		D		(Program)
>7	string		E		(Variable)
>7	string		F		(List)
>7	string		G		(Matrix)
>7	string		H		(Library)
>7	string		I		(Target List)
>7	string		J		(ASCII Vector specification)
>7	string		K		(wildcard)

# hpBSD magic numbers
0	beshort		200		hp200 (68010) BSD
>2	beshort		0407		impure binary
>2	beshort		0410		read-only binary
>2	beshort		0413		demand paged binary
0	beshort		300		hp300 (68020+68881) BSD
>2	beshort		0407		impure binary
>2	beshort		0410		read-only binary
>2	beshort		0413		demand paged binary
#
# <AUTHOR> <EMAIL>
# HP-UX 10.20 core file format from /usr/include/sys/core.h
# Unfortunately, HP-UX uses corehead blocks without specifying the order
# There are four we care about:
#     CORE_KERNEL, which starts with the string "HP-UX"
#     CORE_EXEC, which contains the name of the command
#     CORE_PROC, which contains the signal number that caused the core dump
#     CORE_FORMAT, which contains the version of the core file format (== 1)
# The only observed order in real core files is KERNEL, EXEC, FORMAT, PROC
# but we include all 6 variations of the order of the first 3, and
# assume that PROC will always be last
# Order 1: KERNEL, EXEC, FORMAT, PROC
0x10		string	HP-UX
>0		belong	2
>>0xC		belong	0x3C
>>>0x4C		belong	0x100
>>>>0x58	belong	0x44
>>>>>0xA0	belong	1
>>>>>>0xAC	belong	4
>>>>>>>0xB0	belong	1
>>>>>>>>0xB4	belong	4		core file
>>>>>>>>>0x90	string	>\0		from '%s'
>>>>>>>>>0xC4	belong	3		- received SIGQUIT
>>>>>>>>>0xC4	belong	4		- received SIGILL
>>>>>>>>>0xC4	belong	5		- received SIGTRAP
>>>>>>>>>0xC4	belong	6		- received SIGABRT
>>>>>>>>>0xC4	belong	7		- received SIGEMT
>>>>>>>>>0xC4	belong	8		- received SIGFPE
>>>>>>>>>0xC4	belong	10		- received SIGBUS
>>>>>>>>>0xC4	belong	11		- received SIGSEGV
>>>>>>>>>0xC4	belong	12		- received SIGSYS
>>>>>>>>>0xC4	belong	33		- received SIGXCPU
>>>>>>>>>0xC4	belong	34		- received SIGXFSZ
# Order 2: KERNEL, FORMAT, EXEC, PROC
>>>0x4C		belong	1
>>>>0x58	belong	4
>>>>>0x5C	belong	1
>>>>>>0x60	belong	0x100
>>>>>>>0x6C	belong	0x44
>>>>>>>>0xB4	belong	4		core file
>>>>>>>>>0xA4	string	>\0		from '%s'
>>>>>>>>>0xC4	belong	3		- received SIGQUIT
>>>>>>>>>0xC4	belong	4		- received SIGILL
>>>>>>>>>0xC4	belong	5		- received SIGTRAP
>>>>>>>>>0xC4	belong	6		- received SIGABRT
>>>>>>>>>0xC4	belong	7		- received SIGEMT
>>>>>>>>>0xC4	belong	8		- received SIGFPE
>>>>>>>>>0xC4	belong	10		- received SIGBUS
>>>>>>>>>0xC4	belong	11		- received SIGSEGV
>>>>>>>>>0xC4	belong	12		- received SIGSYS
>>>>>>>>>0xC4	belong	33		- received SIGXCPU
>>>>>>>>>0xC4	belong	34		- received SIGXFSZ
# Order 3: FORMAT, KERNEL, EXEC, PROC
0x24		string	HP-UX
>0		belong	1
>>0xC		belong	4
>>>0x10		belong	1
>>>>0x14	belong	2
>>>>>0x20	belong	0x3C
>>>>>>0x60	belong	0x100
>>>>>>>0x6C	belong	0x44
>>>>>>>>0xB4	belong	4		core file
>>>>>>>>>0xA4	string	>\0		from '%s'
>>>>>>>>>0xC4	belong	3		- received SIGQUIT
>>>>>>>>>0xC4	belong	4		- received SIGILL
>>>>>>>>>0xC4	belong	5		- received SIGTRAP
>>>>>>>>>0xC4	belong	6		- received SIGABRT
>>>>>>>>>0xC4	belong	7		- received SIGEMT
>>>>>>>>>0xC4	belong	8		- received SIGFPE
>>>>>>>>>0xC4	belong	10		- received SIGBUS
>>>>>>>>>0xC4	belong	11		- received SIGSEGV
>>>>>>>>>0xC4	belong	12		- received SIGSYS
>>>>>>>>>0xC4	belong	33		- received SIGXCPU
>>>>>>>>>0xC4	belong	34		- received SIGXFSZ
# Order 4: EXEC, KERNEL, FORMAT, PROC
0x64		string	HP-UX
>0		belong	0x100
>>0xC		belong	0x44
>>>0x54		belong	2
>>>>0x60	belong	0x3C
>>>>>0xA0	belong	1
>>>>>>0xAC	belong	4
>>>>>>>0xB0	belong	1
>>>>>>>>0xB4	belong	4		core file
>>>>>>>>>0x44	string	>\0		from '%s'
>>>>>>>>>0xC4	belong	3		- received SIGQUIT
>>>>>>>>>0xC4	belong	4		- received SIGILL
>>>>>>>>>0xC4	belong	5		- received SIGTRAP
>>>>>>>>>0xC4	belong	6		- received SIGABRT
>>>>>>>>>0xC4	belong	7		- received SIGEMT
>>>>>>>>>0xC4	belong	8		- received SIGFPE
>>>>>>>>>0xC4	belong	10		- received SIGBUS
>>>>>>>>>0xC4	belong	11		- received SIGSEGV
>>>>>>>>>0xC4	belong	12		- received SIGSYS
>>>>>>>>>0xC4	belong	33		- received SIGXCPU
>>>>>>>>>0xC4	belong	34		- received SIGXFSZ
# Order 5: FORMAT, EXEC, KERNEL, PROC
0x78		string	HP-UX
>0		belong	1
>>0xC		belong	4
>>>0x10		belong	1
>>>>0x14	belong	0x100
>>>>>0x20	belong	0x44
>>>>>>0x68	belong	2
>>>>>>>0x74	belong	0x3C
>>>>>>>>0xB4	belong	4		core file
>>>>>>>>>0x58	string	>\0		from '%s'
>>>>>>>>>0xC4	belong	3		- received SIGQUIT
>>>>>>>>>0xC4	belong	4		- received SIGILL
>>>>>>>>>0xC4	belong	5		- received SIGTRAP
>>>>>>>>>0xC4	belong	6		- received SIGABRT
>>>>>>>>>0xC4	belong	7		- received SIGEMT
>>>>>>>>>0xC4	belong	8		- received SIGFPE
>>>>>>>>>0xC4	belong	10		- received SIGBUS
>>>>>>>>>0xC4	belong	11		- received SIGSEGV
>>>>>>>>>0xC4	belong	12		- received SIGSYS
>>>>>>>>>0xC4	belong	33		- received SIGXCPU
>>>>>>>>>0xC4	belong	34		- received SIGXFSZ
# Order 6: EXEC, FORMAT, KERNEL, PROC
>0		belong	0x100
>>0xC		belong	0x44
>>>0x54		belong	1
>>>>0x60	belong	4
>>>>>0x64	belong	1
>>>>>>0x68	belong	2
>>>>>>>0x74	belong	0x2C
>>>>>>>>0xB4	belong	4		core file
>>>>>>>>>0x44	string	>\0		from '%s'
>>>>>>>>>0xC4	belong	3		- received SIGQUIT
>>>>>>>>>0xC4	belong	4		- received SIGILL
>>>>>>>>>0xC4	belong	5		- received SIGTRAP
>>>>>>>>>0xC4	belong	6		- received SIGABRT
>>>>>>>>>0xC4	belong	7		- received SIGEMT
>>>>>>>>>0xC4	belong	8		- received SIGFPE
>>>>>>>>>0xC4	belong	10		- received SIGBUS
>>>>>>>>>0xC4	belong	11		- received SIGSEGV
>>>>>>>>>0xC4	belong	12		- received SIGSYS
>>>>>>>>>0xC4	belong	33		- received SIGXCPU
>>>>>>>>>0xC4	belong	34		- received SIGXFSZ



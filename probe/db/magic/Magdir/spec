
#------------------------------------------------------------------------------
# $File: spec,v 1.4 2009/09/19 16:28:12 christos Exp $
# spec:  file(1) magic for SPEC raw results (*.raw, *.rsf)
#
# Cloyce <PERSON>. <PERSON>pradling <<EMAIL>>

0	string	spec			SPEC
>4	string	.cpu			CPU
>>8	string	<:			\b%.4s
>>12	string	.			raw result text

17	string	version=SPECjbb		SPECjbb
>32	string	<:			\b%.4s
>>37	string	<:			v%.4s raw result text

0	string	BEGIN\040SPECWEB	SPECweb
>13	string	<:			\b%.2s
>>15	string	_SSL			\b_SSL
>>>20	string	<:			v%.4s raw result text
>>16	string	<:			v%.4s raw result text

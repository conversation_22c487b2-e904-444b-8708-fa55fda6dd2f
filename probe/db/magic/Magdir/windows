
#------------------------------------------------------------------------------
# $File: windows,v 1.31 2020/03/15 16:44:37 christos Exp $
# windows:  file(1) magic for Microsoft Windows
#
# This file is mainly reserved for files where programs
# using them are run almost always on MS Windows 3.x or
# above, or files only used exclusively in Windows OS,
# where there is no better category to allocate for.
# For example, even though WinZIP almost run on Windows
# only, it is better to treat them as "archive" instead.
# For format usable in DOS, such as generic executable
# format, please specify under "msdos" file.
#


# Summary: Outlook Express DBX file
# Extension: .dbx
# Created by: <PERSON>
0	string	\xCF\xAD\x12\xFE	MS Outlook Express DBX file
>4	byte	=0xC5			\b, message database
>4	byte	=0xC6			\b, folder database
>4	byte	=0xC7			\b, account information
>4	byte	=0x30			\b, offline database


# Summary: Windows crash dump
# Extension: .dmp
# Created by: <PERSON> (https://computer.forensikblog.de/)
# Reference (1): https://computer.forensikblog.de/en/2008/02/64bit_magic.html
# Modified by (1): Abel Cheung (Avoid match with first 4 bytes only)
0	string		PAGE
>4	string		DUMP		MS Windows 32bit crash dump
>>0x05c	byte            0		\b, no PAE
>>0x05c	byte            1		\b, PAE
>>0xf88	lelong		1		\b, full dump
>>0xf88	lelong		2		\b, kernel dump
>>0xf88	lelong		3		\b, small dump
>>0x068	lelong		x		\b, %d pages
>4	string		DU64		MS Windows 64bit crash dump
>>0xf98	lelong		1		\b, full dump
>>0xf98	lelong		2		\b, kernel dump
>>0xf98	lelong		3		\b, small dump
>>0x090	lequad		x		\b, %lld pages


# Summary: Vista Event Log
# Extension: .evtx
# Created by: Andreas Schuster (https://computer.forensikblog.de/)
# Reference (1): https://computer.forensikblog.de/en/2007/05/some_magic.html
0	string		ElfFile\0	MS Windows Vista Event Log
>0x2a	leshort		x		\b, %d chunks
>>0x10	lelong		x		\b (no. %d in use)
>0x18	lelong		>1		\b, next record no. %d
>0x18	lelong		=1		\b, empty
>0x78	lelong		&1		\b, DIRTY
>0x78	lelong		&2		\b, FULL

# Summary: Windows System Deployment Image
# Created by: Joerg Jenderek
# URL: http://en.wikipedia.org/wiki/System_Deployment_Image
# Reference: http://skolk.livejournal.com/1320.html
0	string			$SDI
>4	string			0001		System Deployment Image
!:mime	application/x-ms-sdi
#!:mime	application/octet-stream
# \Boot\boot.sdi
!:ext	sdi
# MDBtype: 0~Unspecified 1~RAM 2~ROM
>>8	ulequad			!0		\b, MDBtype 0x%llx
# BootCodeOffset
>>16	ulequad			!0		\b, BootCodeOffset 0x%llx
# BootCodeSize
>>24	ulequad			!0		\b, BootCodeSize 0x%llx
# VendorID
>>32	ulequad			!0		\b, VendorID 0x%llx
# DeviceID
>>40	ulequad			!0		\b, DeviceID 0x%llx
# DeviceModel
>>48	ulequad			!0		\b, DeviceModel 0x%llx
>>>56	ulequad			!0		\b%llx
# DeviceRole
>>64	ulequad			!0		\b, DeviceRole 0x%llx
# Reserved1; reserved fields and gaps between BLOBs are padded with \0
#>>72	ulequad			!0		\b, Reserved1 0x%llx
# RuntimeGUID
>>80	ulequad			!0		\b, RuntimeGUID 0x%llx
>>>88	ulequad			!0		\b%llx
# RuntimeOEMrev
>>96	ulequad			!0		\b, RuntimeOEMrev 0x%llx
# Reserved2
#>>104	ulequad			!0		\b, Reserved2 0x%llx
# BLOB alignment value in pages, as specified in sdimgr /pack: 1~4K 2~8k
>>112	ulequad			!0		\b, PageAlignment %llu
# Reserved3[48]
#>>120	ulequad			!0		\b, Reserved3 0x%llx
# SDI checksum 39h
>>0x1f8	ulequad			x		\b, checksum 0x%llx
# BLOBtype[8] \0-padded: PART, WIM , BOOT, LOAD, DISK
>>0x400	string			>\0		\b, type %-3.8s
# 0~non-filesystem 7~NTFS 6~BIGFAT
>>>0x420	ulequad		!0		(0x%llx)
# ATTRibutes
>>>0x408	ulequad		!0		0x%llx attributes
# Offset
>>>0x410	ulequad		x		at 0x%llx
# print 1 space after size and then handles NTFS boot sector by ./filesystems
>>>0x418	ulequad		>0		%llu bytes 
>>>>(0x410.l)	indirect	x
# 2nd BLOB: WIM
>>0x440		string		>\0		\b, type %-3.8s
>>>0x428	ulequad		!0		(0x%llx)
# ATTRibutes
>>>0x448	ulequad		!0		0x%llx attributes
# Offset
>>>0x450	ulequad		x		at 0x%llx
>>>0x458	ulequad		>0		%llu bytes 
>>>>(0x450.l)	indirect	x
# 3rd BLOB
>>0x480		string		>\0		\b, type %-3.8s

# Summary:	Windows boot status log BOOTSTAT.DAT
# From:		Joerg Jenderek
# Reference:	https://www.geoffchappell.com/notes/windows/boot/bsd.htm
# Note:		mainly refers to older Windows Vista, sometimes
#		BOOTSTAT.DAT only contains nulls or invalid data
# checking for valid version below 5
0		ulelong		<5
# skip many ISO images by checking for valid 64 KiB file size
>8		ulelong		=0x00010000
>>0		use		bootstat-dat
# display information of BOOTSTAT.DAT
0	name		bootstat-dat
>0		ulelong		x		Windows boot log
#!:mime	application/octet-stream
!:mime	application/x-ms-dat
# BOOTSTAT.DAT in BOOT subdirectory
!:ext	dat
# apparently a version number: 2 for older like Vista, 3, 4 Windows 10
>0		ulelong		>2		\b, version %u
# apparently the size of the header: often 10h in older Windows, 14h, 18h
>4		ulelong		!0x10		\b, header size 0x%x
#>4		ulelong		!0x10		\b, header size %u
# apparently the size of the file: always 0x00010000~64KiB
# the file is acceptable to BOOTMGR only if it is exactly 64 KiB
>8		ulelong		!0x00010000	\b, file size 0x%x
# size of valid data, in bytes: C8h 50h 172h 5D5Ch
>0xc		ulelong		x		\b, 0x%x valid bytes
# skip header and jump to first bootstat entry and display information
>(0x4.l-1)	ubyte		x
>>&0		use		bootstat-entry
# jump to first entry again because pointer are bad after "use"
>(0x4.l-1)	ubyte		x
# by 1st entry size jump to 2nd entry and display information
>>&(&0x18.l-1)	ubyte		x
>>>&0		use		bootstat-entry
# jump to possible 3rd boot entry and display information
# >(0x4.l-1)	ubyte		x
# >>&(&0x18.l-1)	ubyte		x
# >>>&(&0x18.l-1)	ubyte		x
# >>>>&0		use		bootstat-entry
#	display BOOTSTAT.DAT entry
0	name		bootstat-entry
#>0x00		ubequad		x		\b, ENTRY %16.16llx
# size of entry, in bytes: 40h(init) 78h(launced) 9Ch
#>0x18		ulelong		x		\b; entry size %u
>0x18		ulelong		x		\b; entry size 0x%x
# time stamp, in seconds 
>0x00		ulelong		x		\b, 0x%x seconds
# always zero, significance unknown
>0x04		ulelong		!0		\b, not null %u
# GUID of event source; but empty if event source is BOOTMGR 
>0x08		ubequad		!0		\b, GUID 0x%16.16llx
>>0x10		ubequad		x		\b%16.16llx
# severity code: 1~informational 3~errors
>0x1C		ulelong		!1		\b, severity 0x%x
# apparently a version number: 2 
>0x20		ulelong		!2		\b, version %u
# event identifier 1~log file initialised 11h~boot application launched 
#>0x24		ulelong		x		\b, event 0x%x
>0x24		ulelong		!1
>>0x24		ulelong		!0x11		\b, event 0x%x
# entry data; size depends on event identifier  
#>0x28		ubequad		x		\b, data 0x%16.16llx
>0x24		ulelong		=0x1		\b, Init
# always 0, significance unknown 
>>0x34		uleshort	!0		\b, not null %u
# always 7, significance unknown 
>>0x36		uleshort	!7		\b, not seven %u
# year
>>0x28		uleshort	x		%u
# month
>>0x2A		uleshort	x		\b-%u
# day
>>0x2C		uleshort	x		\b-%u
# hour
>>0x2E		uleshort	x		%u
# minute
>>0x30		uleshort	x		\b:%u
# second
>>0x32		uleshort	x		\b:%u
# boot application launched
>0x24		ulelong		=0x11		\b, launched
# type of start: 0 normally, 1 or 2 maybe in a recovery sequence
>>0x38		uleshort	!0		\b, type %u
# pathname of boot application, as null-terminated Unicode string; typically
# \Windows\system32\winload.exe \Windows\system32\winload.efi
>>0x3C		lestring16	x		%s

# Summary:	Windows Error Report text files
# URL:		https://en.wikipedia.org/wiki/Windows_Error_Reporting
# Reference:	https://www.nirsoft.net/utils/app_crash_view.html
# Created by:	Joerg Jenderek
# Note:		in directories	%ProgramData%\Microsoft\Windows\WER\{ReportArchive,ReportQueue}
#				%LOCALAPPDATA%\Microsoft\Windows\WER\{ReportArchive,ReportQueue}
0	lestring16	Version=	
>22	lestring16	EventType	Windows Error Report
!:mime	text/plain
# Report.wer
!:ext	wer

# Summary: Windows 3.1 group files
# Extension: .grp
# Created by: unknown
0	string		\120\115\103\103	MS Windows 3.1 group files


# Summary: Old format help files
# URL: https://en.wikipedia.org/wiki/WinHelp
# Reference: https://www.oocities.org/mwinterhoff/helpfile.htm
# Update: Joerg Jenderek
# <AUTHOR> <EMAIL>
#
# check and then display version and date inside MS Windows HeLP file fragment
0	name				help-ver-date
# look for Magic of SYSTEMHEADER
>0	leshort		0x036C
# version Major		1 for right file fragment
>>4	leshort		1		Windows
# print non empty string above to avoid error message
# Warning: Current entry does not yet have a description for adding a MIME type
!:mime	application/winhelp
!:ext	hlp
# version Minor of help file format is hint for windows version
>>>2	leshort		0x0F		3.x
>>>2	leshort		0x15		3.0
>>>2	leshort		0x21		3.1
>>>2	leshort		0x27		x.y
>>>2	leshort		0x33		95
>>>2	default		x		y.z
>>>>2	leshort		x		0x%x
# to complete message string like "MS Windows 3.x help file"
>>>2	leshort		x		help
# GenDate often older than file creation date
>>>6	ldate		x		\b, %s
#
# Magic for HeLP files
0	lelong		0x00035f3f
# ./windows (version 5.25) labeled the entry as "MS Windows 3.x help file"
# file header magic 0x293B at DirectoryStart+9
>(4.l+9)	uleshort	0x293B		MS
# look for @VERSION	bmf.. like IBMAVW.ANN
>>0xD4		string	=\x62\x6D\x66\x01\x00	Windows help annotation
!:mime	application/x-winhelp
!:ext	ann
>>0xD4		string	!\x62\x6D\x66\x01\x00
# "GID Help index" by TrID
>>>(4.l+0x65)	string	=|Pete			Windows help Global Index
!:mime	application/x-winhelp
!:ext	gid
# HeLP Bookmark or
# "Windows HELP File" by TrID
>>>(4.l+0x65)		string		!|Pete
# maybe there exist a cleaner way to detect HeLP fragments
# brute search for Magic 0x036C with matching Major maximal 7 iterations
# discapp.hlp
>>>>16			search/0x49AF/s	\x6c\x03
>>>>>&0			use 		help-ver-date
>>>>>&4			leshort		!1
# putty.hlp
>>>>>>&0		search/0x69AF/s	\x6c\x03
>>>>>>>&0		use 		help-ver-date
>>>>>>>&4		leshort		!1
>>>>>>>>&0		search/0x49AF/s	\x6c\x03
>>>>>>>>>&0		use 		help-ver-date
>>>>>>>>>&4		leshort		!1
>>>>>>>>>>&0		search/0x49AF/s	\x6c\x03
>>>>>>>>>>>&0		use 		help-ver-date
>>>>>>>>>>>&4		leshort		!1
>>>>>>>>>>>>&0		search/0x49AF/s	\x6c\x03
>>>>>>>>>>>>>&0		use 		help-ver-date
>>>>>>>>>>>>>&4		leshort		!1
>>>>>>>>>>>>>>&0	search/0x49AF/s	\x6c\x03
>>>>>>>>>>>>>>>&0	use 		help-ver-date
>>>>>>>>>>>>>>>&4	leshort		!1
>>>>>>>>>>>>>>>>&0	search/0x49AF/s	\x6c\x03
# GCC.HLP is detected after 7 iterations
>>>>>>>>>>>>>>>>>&0	use 		help-ver-date
# this only happens if bigger hlp file is detected after used search iterations
>>>>>>>>>>>>>>>>>&4	leshort		!1		Windows y.z help
!:mime	application/winhelp
!:ext	hlp
# repeat search again or following default line does not work
>>>>16			search/0x49AF/s	\x6c\x03
# remaining files should be HeLP Bookmark WinHlp32.BMK (XP 32-bit) or WinHlp32 (Windows 8.1 64-bit)
>>>>16	default				x	Windows help Bookmark
!:mime	application/x-winhelp
!:ext	bmk
## FirstFreeBlock normally FFFFFFFFh 10h for *ANN
##>>8	lelong			x		\b, FirstFreeBlock 0x%8.8x
# EntireFileSize
>>12	lelong			x		\b, %d bytes
## ReservedSpace normally 042Fh AFh for *.ANN
#>>(4.l)	lelong		x		\b, ReservedSpace 0x%8.8x
## UsedSpace normally 0426h A6h for *.ANN
#>>(4.l+4)	lelong		x		\b, UsedSpace 0x%8.8x
## FileFlags normally 04...
#>>(4.l+5)	lelong		x		\b, FileFlags 0x%8.8x
## file header magic 0x293B
#>>(4.l+9)	uleshort	x		\b, file header magic 0x%4.4x
## file header Flags		0x0402
#>>(4.l+11)	uleshort	x		\b, file header Flags 0x%4.4x
## file header PageSize	0400h 80h for *.ANN
#>>(4.l+13)	uleshort	x		\b, PageSize 0x%4.4x
## Structure[16]		z4
#>>(4.l+15)	string		>\0		\b, Structure_"%-.16s"
## MustBeZero			0
#>>(4.l+31)	uleshort	x		\b, MustBeZero 0x%4.4x
## PageSplits
#>>(4.l+33)	uleshort	x		\b, PageSplits 0x%4.4x
## RootPage
#>>(4.l+35)	uleshort	x		\b, RootPage 0x%4.4x
## MustBeNegOne			0xffff
#>>(4.l+37)	uleshort	x		\b, MustBeNegOne 0x%4.4x
## TotalPages			1
#>>(4.l+39)	uleshort	x		\b, TotalPages 0x%4.4x
## NLevels			0x0001
#>>(4.l+41)	uleshort	x		\b, NLevels 0x%4.4x
## TotalBtreeEntries
#>>(4.l+43)	ulelong		x		\b, TotalBtreeEntries 0x%8.8x
## pages of the B+ tree
#>>(4.l+47)	ubequad		x		\b, PageStart 0x%16.16llx

# start with colon or semicolon for comment line like Back2Life.cnt
0		regex		\^(:|;)
# look for first keyword Base
>0		search/45	:Base
>>&0				use 		cnt-name
# only solution to search again from beginning , because relative offsets changes when use is called
>0		search/45	:Base
>0		default		x
# look for other keyword Title like in putty.cnt
>>0		search/45	:Title
>>>&0				use 		cnt-name
#
# display mime type and name of Windows help Content source
0	name				cnt-name
# skip space at beginning
>0     string		\040
# name without extension and greater character or name with hlp extension
>>1	regex/c		\^([^\xd>]*|.*\.hlp)	MS Windows help file Content, based "%s"
!:mime	text/plain
!:apple	????TEXT
!:ext	cnt
#
# Windows creates a full text search from hlp file, if the user clicks the "Find" tab and enables keyword indexing
0	string		tfMR			MS Windows help Full Text Search index
!:mime application/x-winhelp-fts
!:ext	fts
>16	string		>\0			for "%s"

# Summary: Hyper terminal
# Extension: .ht
# Created by: unknown
0	string		HyperTerminal\040
>15	string		1.0\ --\ HyperTerminal\ data\ file	MS Windows HyperTerminal profile

# https://ithreats.files.wordpress.com/2009/05/\040
# lnk_the_windows_shortcut_file_format.pdf
# Summary: Windows shortcut
# Extension: .lnk
# Created by: unknown
# 'L' + GUUID
0	string		\114\0\0\0\001\024\002\0\0\0\0\0\300\0\0\0\0\0\0\106	MS Windows shortcut
>20	lelong&1	1	\b, Item id list present
>20	lelong&2	2	\b, Points to a file or directory
>20	lelong&4	4	\b, Has Description string
>20	lelong&8	8	\b, Has Relative path
>20	lelong&16	16	\b, Has Working directory
>20	lelong&32	32	\b, Has command line arguments
>20	lelong&64	64	\b, Icon
>>56	lelong		x	\b number=%d
>24	lelong&1	1	\b, Read-Only
>24	lelong&2	2	\b, Hidden
>24	lelong&4	4	\b, System
>24	lelong&8	8	\b, Volume Label
>24	lelong&16	16	\b, Directory
>24	lelong&32	32	\b, Archive
>24	lelong&64	64	\b, Encrypted
>24	lelong&128	128	\b, Normal
>24	lelong&256	256	\b, Temporary
>24	lelong&512	512	\b, Sparse
>24	lelong&1024	1024	\b, Reparse point
>24	lelong&2048	2048	\b, Compressed
>24	lelong&4096	4096	\b, Offline
>28	leqwdate	x	\b, ctime=%s
>36	leqwdate	x	\b, mtime=%s
>44	leqwdate	x	\b, atime=%s
>52	lelong		x	\b, length=%u, window=
>60	lelong&1	1	\bhide
>60	lelong&2	2	\bnormal
>60	lelong&4	4	\bshowminimized
>60	lelong&8	8	\bshowmaximized
>60	lelong&16	16	\bshownoactivate
>60	lelong&32	32	\bminimize
>60	lelong&64	64	\bshowminnoactive
>60	lelong&128	128	\bshowna
>60	lelong&256	256	\brestore
>60	lelong&512	512	\bshowdefault
#>20	lelong&1	0
#>>20	lelong&2	2
#>>>(72.l-64)	pstring/h	x	\b [%s]
#>20	lelong&1	1
#>>20	lelong&2	2
#>>>(72.s)	leshort	x
#>>>&75	pstring/h	x	\b [%s]

# Summary: Outlook Personal Folders
# Created by: unknown
0	lelong		0x4E444221	Microsoft Outlook email folder
>10	leshort		0x0e		(<=2002)
>10	leshort		0x17		(>=2003)


# Summary: Windows help cache
# Created by: unknown
0	string		\164\146\115\122\012\000\000\000\001\000\000\000	MS Windows help cache


# Summary: IE cache file
# Created by: Christophe Monniez
0	string	Client\ UrlCache\ MMF 	Internet Explorer cache file
>20	string	>\0			version %s


# Summary: Registry files
# Created by: unknown
# Modified by (1): Joerg Jenderek
0	string		regf		MS Windows registry file, NT/2000 or above
0	string		CREG		MS Windows 95/98/ME registry file
0	string		SHCC3		MS Windows 3.1 registry file


# Summary: Windows Registry text
# URL: https://en.wikipedia.org/wiki/Windows_Registry#.REG_files
# Reference: http://fileformats.archiveteam.org/wiki/Windows_Registry
# <AUTHOR> <EMAIL>
# Update: Joerg Jenderek
#		Windows 3-9X variant
0	string		REGEDIT
# skip ASCII text like "REGEDITor.txt" but match
# L1WMAP.REG with only 1 CRNL or org.gnome.gnumeric.reg with 2 NL
>7	search/3	\n			Windows Registry text
!:mime	text/x-ms-regedit
!:ext	reg
#		Windows 9X variant
>>0	string		REGEDIT4		(Win95 or above)
#		Windows 2K ANSI variant
0	string		Windows\ Registry\ Editor\ 
>&0	string		Version\ 5.00\r\n\r\n	Windows Registry text (Win2K or above)
!:mime	text/x-ms-regedit
!:ext	reg
#		Windows 2K UTF-16 variant
2	lestring16	Windows\ Registry\ Editor\ 
>0x32	lestring16	Version\ 5.00\r\n\r\n	Windows Registry little-endian text (Win2K or above)
# relative offset not working
#>&0	lestring16	Version\ 5.00\r\n\r\n	Windows Registry little-endian text (Win2K or above)
!:mime	text/x-ms-regedit
!:ext	reg
#		WINE variant
# URL: https://en.wikipedia.org/wiki/Wine_(software)
# Reference: https://www.winehq.org/pipermail/wine-cvs/2005-October/018763.html
# Note:	WINE use text based registry (system.reg,user.reg,userdef.reg)
#	instead binary hiv structure like Windows
0	string	WINE\ REGISTRY\ Version\ 	WINE registry text
# version 2
>&0	string	x				\b, version %s
!:mime	text/x-wine-extension-reg
!:ext	reg

# Windows *.INF *.INI files updated by Joerg Jenderek at Apr 2013, Feb 2018
# empty ,comment , section
# PR/383: remove unicode BOM because it is not portable across regex impls
#0	regex/s		\\`(\\r\\n|;|[[])
# empty line CRLF
0	ubeshort	0x0D0A
>0	use		ini-file
# comment line
0	string		;
>0	use		ini-file
# section line
0	string		[
>0	use		ini-file
# check and then display Windows INItialization configuration
0	name		ini-file
# look for left bracket in section line
>0	search/8192	[
# https://en.wikipedia.org/wiki/Autorun.inf
# https://msdn.microsoft.com/en-us/library/windows/desktop/cc144200.aspx
# space after right bracket
# or AutoRun.Amd64 for 64 bit systems
# or only NL separator
>>&0	regex/c		\^(autorun)
# but sometimes total commander directory tree file "treeinfo.wc" with lines like
# [AUTORUN]
# [boot]
>>>&0	string		=]\r\n[					Total commander directory treeinfo.wc
!:mime text/plain
!:ext	wc
# <AUTHOR> <EMAIL>
# Autorun File
>>>&0	string		!]\r\n[					Microsoft Windows Autorun file
!:mime application/x-setupscript
!:ext	inf
# https://msdn.microsoft.com/en-us/library/windows/hardware/ff549520(v=vs.85).aspx
# version strings ASCII coded case-independent for Windows setup information script file
>>&0	regex/c		\^(version|strings)]				Windows setup INFormation
!:mime	application/x-setupscript
#!:mime application/x-wine-extension-inf
!:ext	inf
# NETCRC.INF OEMCPL.INF
>>&0	regex/c		\^(WinsockCRCList|OEMCPL)]			Windows setup INFormation
!:mime	application/x-setupscript
!:ext	inf
# http://www.winfaq.de/faq_html/Content/tip2500/onlinefaq.php?h=tip2653.htm
# https://msdn.microsoft.com/en-us/library/windows/desktop/cc144102.aspx
# .ShellClassInfo DeleteOnCopy LocalizedFileNames ASCII coded case-independent
>>&0	regex/c	\^(\.ShellClassInfo|DeleteOnCopy|LocalizedFileNames)]	Windows desktop.ini
!:mime application/x-wine-extension-ini
#!:mime text/plain
# https://support.microsoft.com/kb/84709/
>>&0	regex/c		\^(don't\ load)]				Windows CONTROL.INI
!:mime application/x-wine-extension-ini
!:ext	ini
>>&0	regex/c		\^(ndishlp\\$|protman\\$|NETBEUI\\$)]		Windows PROTOCOL.INI
!:mime application/x-wine-extension-ini
!:ext	ini
# https://technet.microsoft.com/en-us/library/cc722567.aspx
# http://www.winfaq.de/faq_html/Content/tip0000/onlinefaq.php?h=tip0137.htm
>>&0	regex/c		\^(windows|Compatibility|embedding)]		Windows WIN.INI
!:mime application/x-wine-extension-ini
!:ext	ini
# https://en.wikipedia.org/wiki/SYSTEM.INI
>>&0	regex/c		\^(boot|386enh|drivers)]			Windows SYSTEM.INI
!:mime application/x-wine-extension-ini
!:ext	ini
# http://www.mdgx.com/newtip6.htm
>>&0	regex/c		\^(SafeList)]					Windows IOS.INI
!:mime application/x-wine-extension-ini
!:ext	ini
# https://en.wikipedia.org/wiki/NTLDR	Windows Boot Loader information
>>&0	regex/c		\^(boot\x20loader)]				Windows boot.ini
!:mime application/x-wine-extension-ini
!:ext	ini
# https://en.wikipedia.org/wiki/CONFIG.SYS
>>&0	regex/c		\^(menu)]					MS-DOS CONFIG.SYS
# @CONFIG.UI configuration file of previous DOS version saved by Caldera OPENDOS INSTALL.EXE
# CONFIG.PSS saved version of file CONFIG.SYS created by %WINDIR%\SYSTEM\MSCONFIG.EXE
# CONFIG.TSH renamed file CONFIG.SYS.BAT by %WINDIR%\SYSTEM\MSCONFIG.EXE
# dos and w40 used in dual booting scene
!:ext	sys/dos/w40
# https://support.microsoft.com/kb/118579/
>>&0	regex/c		\^(Paths)]\r\n					MS-DOS MSDOS.SYS
!:ext	sys/dos
# http://chmspec.nongnu.org/latest/INI.html#HHP
>>&0	regex/c		\^(options)]\r\n				Microsoft HTML Help Project
!:mime text/plain
!:ext	hhp
# unknown keyword after opening bracket
>>&0	default				x
#>>>&0	string/c			x	UNKNOWN [%s
# look for left bracket of second section
>>>&0	search/8192			[
# version Strings FileIdentification
>>>>&0	string/c			version				Windows setup INFormation
!:mime application/x-setupscript
!:ext	inf
# https://en.wikipedia.org/wiki/Initialization_file	Windows Initialization File or other
>>>>&0	default				x
>>>>>&0	ubyte				x
# characters, digits, underscore and white space followed by right bracket
# terminated by CR implies section line to skip BOOTLOG.TXT DETLOG.TXT
>>>>>>&-1	regex			\^([A-Za-z0-9_\(\)\ ]+)\]\r	Generic INItialization configuration [%-.40s
# NETDEF.INF multiarc.ini 
#!:mime	application/x-setupscript
!:mime	application/x-wine-extension-ini
#!:mime	text/plain
!:ext	ini/inf
# UTF-16 BOM followed by CR~0D00 , comment~semicolon~3B00 , section~bracket~5B00
0	ubelong&0xFFff89FF	=0xFFFE0900
# look for left bracket in section line
>2	search/8192		[
# keyword without 1st letter which is maybe up-/down-case
>>&3	lestring16		ersion]			Windows setup INFormation
!:mime	application/x-setupscript
!:ext	inf
>>&3	lestring16		trings]			Windows setup INFormation
!:mime	application/x-setupscript
!:ext	inf
>>&3	lestring16		ourceDisksNames]	Windows setup INFormation
!:mime	application/x-setupscript
!:ext	inf
# netnwcli.inf start with ;---[ NetNWCli.INX ]
>>&3	default			x
# look for NL followed by left bracket
>>>&0	search/8192		\x0A\x00\x5b
>>>>&3	lestring16		ersion]			Windows setup INFormation
!:mime	application/x-setupscript
!:ext	inf

# Windows Precompiled INF files *.PNF added by Joerg Jenderek at Mar 2013 of _PNF_HEADER inf.h
# http://read.pudn.com/downloads3/sourcecode/windows/248345/win2k/private/windows/setup/setupapi/inf.h__.htm
# URL:		http://fileformats.archiveteam.org/wiki/INF_(Windows)
# Reference:	http://en.verysource.com/code/10350344_1/inf.h.html
# Note:		stored in %Windir%\Inf %Windir%\System32\DriverStore\FileRepository
# check for valid major and minor versions: 101h - 303h 
0		leshort&0xFcFc	=0x0000
# GRR: line above (strength 50) is too general as it catches also "PDP-11 UNIX/RT ldp" ./pdp
>0		leshort&0x0303	!0x0000
# test for valid InfStyles: 1 2 
>>2		uleshort	>0
>>>2		uleshort	<3
# look for colon in WinDirPath after PNF header
#>>>>0x59	search/18	:
>>>>0	use	PreCompiledInf
0	name	PreCompiledInf
>0		uleshort	x	Windows Precompiled iNF
!:mime	application/x-pnf
!:ext	pnf
# major version 1 for older Windows like XP and 3 since about Windows Vista
# 101h~98-XP; 301h~Windows Vista-7 ; 302h~Windows 10 14393; 303h~Windows 10 18362
>1		ubyte		x		\b, version %u
>0		ubyte		x		\b.%u
>0		uleshort	=0x0101		(Windows
>>4	ulelong&0x00000001	!0x00000001	98)
>>4	ulelong&0x00000001	=0x00000001	XP)
>0		uleshort	=0x0301		(Windows Vista-8.1)
>0		uleshort	=0x0302		(Windows 10 older)
>0		uleshort	=0x0303		(Windows 10)
# 1 ,2 (windows 98 SE)
>2		uleshort	!2		\b, InfStyle %u
#	PNF_FLAG_IS_UNICODE		0x00000001
#	PNF_FLAG_HAS_STRINGS		0x00000002
#	PNF_FLAG_SRCPATH_IS_URL		0x00000004
#	PNF_FLAG_HAS_VOLATILE_DIRIDS	0x00000008
#	PNF_FLAG_INF_VERIFIED		0x00000010
#	PNF_FLAG_INF_DIGITALLY_SIGNED	0x00000020
#	UNKNOWN8			0x00000080
#	UNKNOWN				0x00000100
#	UNKNOWN1			0x01000000
#	UNKNOWN2			0x02000000
>4	ulelong&0x03000180	>0		\b, flags
>>4	ulelong			x		0x%x
>4	ulelong&0x00000001	0x00000001	\b, unicoded
>4	ulelong&0x00000002	0x00000002	\b, has strings
>4	ulelong&0x00000004	0x00000004	\b, src URL
>4	ulelong&0x00000008	0x00000008	\b, volatile dir ids
>4	ulelong&0x00000010	0x00000010	\b, verified
>4	ulelong&0x00000020	0x00000020	\b, digitally signed
# >4	ulelong&0x00000080	0x00000080	\b, UNKNOWN8
# >4	ulelong&0x00000100	0x00000100	\b, UNKNOWN
# >4	ulelong&0x01000000	0x01000000	\b, UNKNOWN1
# >4	ulelong&0x02000000	0x02000000	\b, UNKNOWN2
#>8		ulelong		x		\b, InfSubstValueListOffset 0x%x
# many 0, 1 lmouusb.PNF, 2 linkfx10.PNF , f webfdr16.PNF
# , 6 bth.PNF, 9 usbport.PNF, d netnwifi.PNF, 10h nettcpip.PNF
#>12		uleshort	x		\b, InfSubstValueCount 0x%x
# only < 9 found: 8 hcw85b64.PNF
#>14		uleshort	x		\b, InfVersionDatumCount 0x%x
# only found values lower 0x0000ffff ??
#>16		ulelong		x		\b, InfVersionDataSize 0x%x
# only found positive values lower 0x00ffFFff for InfVersionDataOffset
>20		ulelong		x		\b, at 0x%x
>4	ulelong&0x00000001	=0x00000001
# case independent: CatalogFile Class DriverVer layoutfile LayoutFile SetupClass signature Signature
>>(20.l)	lestring16	x		"%s"
>4	ulelong&0x00000001	!0x00000001
>>(20.l)	string		x		"%s"
# FILETIME is number of 100-nanosecond intervals since 1 January 1601
#>24		ulequad		x		\b, InfVersionLastWriteTime %16.16llx
#>24		foodate-0xbar	x		\b, InfVersionLastWriteTime %s
# for Windows 98, XP
>0		uleshort	<0x0102
# only found values lower 0x00ffFFff
# often 70 but also 78h for corelist.PNF
# >>32		ulelong		x		\b, StringTableBlockOffset 0x%x
# >>36		ulelong		x		\b, StringTableBlockSize 0x%x
# >>40		ulelong		x		\b, InfSectionCount 0x%x
# >>44		ulelong		x		\b, InfSectionBlockOffset 0x%x
# >>48		ulelong		x		\b, InfSectionBlockSize 0x%x
# >>52		ulelong		x		\b, InfLineBlockOffset 0x%x
# >>56		ulelong		x		\b, InfLineBlockSize 0x%x
# >>60		ulelong		x		\b, InfValueBlockOffset 0x%x
# >>64		ulelong		x		\b, InfValueBlockSize 0x%x
# WinDirPathOffset
# like 58h, which means direct after PNF header
#>>68		ulelong		x		\b, at 0x%x
>>68		ulelong		x
>>>4	ulelong&0x00000001	=0x00000001
#>>>>(68.l)	ubequad		=0x43003a005c005700
# normally unicoded C:\Windows
#>>>>>(68.l)	lestring16	x		\b, WinDirPath "%s"
>>>>(68.l)	ubequad		!0x43003a005c005700
>>>>>(68.l)	lestring16	x		\b, WinDirPath "%s"
>>>4	ulelong&0x00000001	!0x00000001
# normally ASCII C:\WINDOWS
#>>>>(68.l)	string		=C:\\WINDOWS	\b, WinDirPath "%s"
>>>>(68.l)	string		!C:\\WINDOWS
>>>>>(68.l)	string		x		\b, WinDirPath "%s"
# found OsLoaderPathOffset values often 0 , once 70h corelist.PNF, once 68h ASCII machine.PNF
>>>72		ulelong		>0		\b,
>>>>4	ulelong&0x00000001	=0x00000001
>>>>>(72.l)	lestring16	x		OsLoaderPath "%s"
>>>>4	ulelong&0x00000001	!0x00000001
# seldom C:\ instead empty
>>>>>(72.l)	string		x		OsLoaderPath "%s"
# 1fdh
#>>>76		uleshort	x		\b, StringTableHashBucketCount 0x%x
# only 407h found
>>>78		uleshort	!0x409		\b, LanguageID %x
#>>>78		uleshort	=0x409		\b, LanguageID %x
# InfSourcePathOffset often 0
>>>80		ulelong		>0		\b, at 0x%x
>>>>4	ulelong&0x00000001	=0x00000001
>>>>>(80.l)	lestring16	x		SourcePath "%s"
>>>>4	ulelong&0x00000001	!0x00000001
>>>>>(80.l)	string		>\0		SourcePath "%s"
# OriginalInfNameOffset often 0
>>>84		ulelong		>0		\b, at 0x%x
>>>>4	ulelong&0x00000001	=0x00000001
>>>>>(84.l)	lestring16	x		InfName "%s"
>>>>4	ulelong&0x00000001	!0x00000001
>>>>>(84.l)	string		>\0		InfName "%s"

# for newer Windows like Vista, 7 , 8.1 , 10
>0		uleshort	>0x0101
>>80	ulelong			x		\b, at 0x%x WinDirPath
>>>4	ulelong&0x00000001	0x00000001
# normally unicoded C:\Windows
#>>>>(80.l)	ubequad		=0x43003a005c005700
#>>>>>(80.l)	lestring16	x		"%s"
>>>>(80.l)	ubequad		!0x43003a005c005700
>>>>>(80.l)	lestring16	x		"%s"
# language id: 0 407h~german 409h~English_US
>>90		uleshort	!0x409		\b, LanguageID %x
#>>90		uleshort	=0x409		\b, LanguageID %x
>>92	ulelong			>0		\b, at 0x%x
>>>4	ulelong&0x00000001	0x00000001
# language string like: de-DE en-US
>>>>(92.l)	lestring16	x		language %s

# Summary: backup file created with utility like NTBACKUP.EXE shipped with Windows NT/2K/XP/2003
# Extension: .bkf
# Created by: Joerg Jenderek
# URL: https://en.wikipedia.org/wiki/NTBackup
# Reference: http://laytongraphics.com/mtf/MTF_100a.PDF
# Descriptor BloCK name of Microsoft Tape Format
0	string			TAPE
# Format Logical Address is zero
>20	ulequad			0
# Reserved for MBC is zero
>>28	uleshort		0
# Control Block ID is zero
>>>36	ulelong			0
# BIT4-BIT15, BIT18-BIT31 of block attributes are unused
>>>>4	ulelong&0xFFfcFFe0	0		Windows NTbackup archive
#!:mime application/x-ntbackup
!:ext bkf
# OS ID
>>>>>10	ubyte			1		\b NetWare
>>>>>10	ubyte			13		\b NetWare SMS
>>>>>10	ubyte			14		\b NT
>>>>>10	ubyte			24		\b 3
>>>>>10	ubyte			25		\b OS/2
>>>>>10	ubyte			26		\b 95
>>>>>10	ubyte			27		\b Macintosh
>>>>>10	ubyte			28		\b UNIX
# OS Version (2)
#>>>>>11	ubyte			x		OS V=%x
# MTF_CONTINUATION	Media Sequence Number > 1
#>>>>>4	ulelong&0x00000001	!0		\b, continued
# MTF_COMPRESSION
>>>>>4	ulelong&0x00000004	!0		\b, compressed
# MTF_EOS_AT_EOM	End Of Medium was hit during end of set processing
>>>>>4	ulelong&0x00000008	!0		\b, End Of Medium hit
>>>>>4	ulelong&0x00020000	0
# MTF_SET_MAP_EXISTS	A Media Based Catalog Set Map may exist on tape
>>>>>>4	ulelong&0x00010000	!0		\b, with catalog
# MTF_FDD_ALLOWED	However File/Directory Detail can only exist if a Set Map is also present
>>>>>4	ulelong&0x00020000	!0		\b, with file catalog
# Offset To First Event 238h,240h,28Ch
#>>>>>8	uleshort		x		\b, event offset %4.4x
# Displayable Size (20e0230h 20e024ch 20e0224h)
#>>>>>8	ulequad			x		dis. size %16.16llx
# Media Family ID (455288C4h 4570BD1Ah 45708F2Fh 4570BBF5h)
#>>>>>52	ulelong			x		family ID %8.8x
# TAPE Attributes (3)
#>>>>>56	ulelong			x		TAPE %8.8x
# Media Sequence Number
>>>>>60	uleshort		>1		\b, sequence %u
# Password Encryption Algorithm (3)
>>>>>62	uleshort		>0		\b, 0x%x encrypted
# Soft Filemark Block Size * 512 (2)
#>>>>>64	uleshort		=2		\b, soft size %u*512
>>>>>64	uleshort		!2		\b, soft size %u*512
# Media Based Catalog Type (1,2)
#>>>>>66	uleshort		x		\b, catalog type %4.4x
# size of Media Name (66,68,6Eh)
>>>>>68	uleshort		>0
# offset of Media Name (5Eh)
>>>>>>70	uleshort	>0
# 0~, 1~ANSI, 2~UNICODE
>>>>>>>48	ubyte		1
# size terminated ansi coded string normally followed by "MTF Media Label"
>>>>>>>>(70.s)	string		>\0		\b, name: %s
>>>>>>>48	ubyte		2
# Not null, but size terminated unicoded string
>>>>>>>>(70.s)	lestring16	x		\b, name: %s
# size of Media Label (104h)
>>>>>72	uleshort		>0
# offset of Media Label (C4h,C6h,CCh)
>>>>>74		uleshort	>0
>>>>>>48	ubyte		1
#Tag|Version|Vendor|Vendor ID|Creation Time Stamp|Cartridge Label|Side|Media ID|Media Domain ID|Vendor Specific fields
>>>>>>>(74.s)	string		>\0		\b, label: %s
>>>>>>48	ubyte		2
>>>>>>>(74.s)	lestring16	x		\b, label: %s
# size of password name (0,1Ch)
#>>>>>76	uleshort		>0		\b, password size %4.4x
# Software Vendor ID (CBEh)
>>>>>86	uleshort		x		\b, software (0x%x)
# size of Software Name (6Eh)
>>>>>80	uleshort		>0
# offset of Software Name (1C8h,1CAh,1D0h)
>>>>>>82	uleshort	>0
# 1~ANSI, 2~UNICODE
>>>>>>>48	ubyte		1
>>>>>>>>(82.s)	string		>\0		\b: %s
>>>>>>>48	ubyte		2
# size terminated unicoded coded string normally followed by "SPAD"
>>>>>>>>(82.s)	lestring16	x		\b: %s
# Format Logical Block Size (512,1024)
#>>>>>84	uleshort		=1024		\b, block size %u
>>>>>84	uleshort		!1024		\b, block size %u
# Media Date of MTF_DATE_TIME type with 5 bytes
#>>>>>>88	ubequad			x		DATE %16.16llx
# MTF Major Version (1)
#>>>>>>93	ubyte		x		\b, MFT version %x
#

# URL: https://en.wikipedia.org/wiki/PaintShop_Pro
# Reference: https://www.cryer.co.uk/file-types/p/pal.htm
# Created by: Joerg Jenderek
# Note: there exist other color palette formats also with .pal extension
0	string	JASC-PAL\r\n	PaintShop Pro color palette
#!:mime	text/plain
# PspPalette extension is used by newer (probably 8) PaintShopPro versions
!:ext	pal/PspPalette
# 2nd line contains palette file version. For example "0100"
>10	string	!0100		\b, version %.4s
# third line contains the number of colours: 16 256 ...
>16	string	x		\b, %.3s colors

# URL: https://en.wikipedia.org/wiki/Innosetup
# Reference: https://github.com/jrsoftware/issrc/blob/master/Projects/Undo.pas
# Created by: Joerg Jenderek
# Note:	created by like "InnoSetup self-extracting archive" inside ./msdos
# TrID labeles the entry as "Inno Setup Uninstall Log"
#	TUninstallLogID
0	string	Inno\ Setup\ Uninstall\ Log\ (b)	InnoSetup Log
!:mime	application/x-innosetup
# unins000.dat, unins001.dat, ...
!:ext	dat
# " 64-bit" variant
>0x1c	string		>\0				\b%.7s
# AppName[0x80] like "Minimal SYStem", ClamWin Free Antivirus , ...
>0xc0	string		x				%s
# AppId[0x80] is similar to AppName or
# GUID like {4BB0DCDC-BC24-49EC-8937-72956C33A470} start with left brace
>0x40	ubyte		0x7b
>>0x40	string		x				%-.38s
# do not know how this log version correlates to program version
>0x140	ulelong		x				\b, version 0x%x
# NumRecs
#>0x144	ulelong		x				\b, 0x%4.4x records
# EndOffset means files size
>0x148	ulelong		x				\b, %u bytes
# Flags 5 25h 35h
#>0x14c	ulelong		x				\b, flags %8.8x
# Reserved: array[0..26] of Longint
# the non Unicode HighestSupportedVersion may never become greater than or equal to 1000
>0x140	ulelong		<1000
# hostname
>>0x1d6	pstring		x				\b, %s
# user name
>>>&0	pstring		x				\b\%s
# directory like C:\Program Files (x86)\GnuWin32
>>>>&0	pstring		x				\b, "%s"
# version 1000 or higher implies unicode
>0x140	ulelong		>999
# hostname
>>0x1db	lestring16	x				\b, %-.9s
# utf string variant with prepending fe??ffFFff
>>0x1db	search/43	\xFF\xFF\xFF			
# user name
>>>&0	lestring16	x				\b\%-.9s
>>>&0	search/43	\xFF\xFF\xFF			
# directory like C:\Program Files\GIMP 2
>>>>&0	lestring16	x				\b, %-.42s

# Windows Imaging (WIM) Image
# Update: Joerg Jenderek at Mar 2019
# URL: https://en.wikipedia.org/wiki/Windows_Imaging_Format
# Reference: https://download.microsoft.com/download/f/e/f/
# fefdc36e-392d-4678-9e4e-771ffa2692ab/Windows%20Imaging%20File%20Format.rtf
# Note: verified by like `7z t boot.wim` `wiminfo install.esd --header`
0	string		MSWIM\000\000\000
>0	use		wim-archive
# https://wimlib.net/man1/wimoptimize.html
0	string		WLPWM\000\000\000
>0	use		wim-archive
0	name		wim-archive
# _WIMHEADER_V1_PACKED ImageTag[8]
>0	string		x			Windows imaging
!:mime	application/x-ms-wim
# TO avoid in file version 5.36 error like
# Magdir/windows, 760: Warning: Current entry does not yet have a description
# file: could not find any valid magic files! (No error)
# splitted WIM
>16	ulelong		&0x00000008		(SWM
!:ext	swm
# usPartNumber; 1, unless the file was split into multiple parts
>>40	uleshort	x			\b %u
# usTotalParts; The total number of WIM file parts in a spanned set
>>42	uleshort	x			\b of %u) image
# non splitted WIM
>16	ulelong		^0x00000008
# https://wimlib.net/man1/wimmount.html
# solid WIMs; version 3584; usually contain LZMS-compressed and the .esd extension
>>12	ulelong		3584			(ESD) image
!:ext	esd
>>12	ulelong		!3584			(WIM) image
!:ext	wim
>0	string/b	WLPWM\000\000\000	\b, wimlib pipable format
# cbSize size of the WIM header in bytes like 208
#>8	ulelong		x			\b, headersize %u
# dwVersion version of the WIM file 00010d00h~1.13 00000e00h~0.14
>14	uleshort	x			v%u
>13	ubyte		x			\b.%u
# dwImageCount; The number of images contained in the WIM file
>44	ulelong		>1			\b, %u images
# dwBootIndex
# 1-based index of the bootable image of the WIM, or 0 if no image is bootable
>0x78	ulelong		>0			\b, bootable no. %u
# dwFlags
#>16	ulelong		x			\b, flags 0x%8.8x
#define FLAG_HEADER_COMPRESSION		0x00000002
#define FLAG_HEADER_READONLY            0x00000004
#define FLAG_HEADER_SPANNED		0x00000008
#define FLAG_HEADER_RESOURCE_ONLY       0x00000010
#define FLAG_HEADER_METADATA_ONLY       0x00000020
#define FLAG_HEADER_WRITE_IN_PROGRESS   0x00000040
#define FLAG_HEADER_RP_FIX		0x00000080 reparse point fixup
#define FLAG_HEADER_COMPRESS_RESERVED   0x00010000
#define FLAG_HEADER_COMPRESS_XPRESS     0x00020000
#define FLAG_HEADER_COMPRESS_LZX	0x00040000
#define FLAG_HEADER_COMPRESS_LZMS	0x00080000
#define FLAG_HEADER_COMPRESS_XPRESS2    0x00100000 wimlib-1.13.0\include\wimlib\header.h 
# XPRESS, with small chunk size
>16	ulelong		&0x00100000		\b, XPRESS2
>16	ulelong		&0x00080000		\b, LZMS
>16	ulelong		&0x00040000		\b, LZX
>16	ulelong		&0x00020000		\b, XPRESS
>16	ulelong		&0x00000002		compressed
>16	ulelong		&0x00000004		\b, read only
>16	ulelong		&0x00000010		\b, resource only
>16	ulelong		&0x00000020		\b, metadata only
>16	ulelong		&0x00000080		\b, reparse point fixup
#>16	ulelong		&0x00010000		\b, RESERVED
# dwCompressionSize; Uncompressed chunk size for resources or 0 if uncompressed
#>20	ulelong		>0			\b, chunk size %u bytes
# gWIMGuid
#>24	ubequad		x			\b, GUID 0x%16.16llx
#>>32	ubequad		x			\b%16.16llx
# rhOffsetTable; the location of the resource lookup table
# wim_reshdr_disk[24]= u8 size_in_wim[7] + u8 flags + le64 offset_in_wim + le64 uncompressed_size
#>48	ubequad		x			\b, rhOffsetTable 0x%16.16llx
# rhXmlData; the location of the XML data
#>0x50	ulelong		x			\b, at 0x%8.8x
# NOT WORKING \xff\xfe<\0W\0I\0M\0
#>(0x50.l)	ubequad	x			\b, xml=%16.16llx
# rhBootMetadata; the location of the metadata resource
#>0x60	ubequad		x			\b, rhBootMetadata 0x%16.16llx
# rhIntegrity; the location of integrity table used to verify files
#>0x7c	ubequad		x			\b, rhIntegrity 0x%16.16llx
# Unused[60]
#>148	ubequad		!0			\b,unused 0x%16.16llx
#

# From:		Joerg Jenderek
# URL:		https://en.wikipedia.org/wiki/Windows_Easy_Transfer
# Reference:	http://mark0.net/download/triddefs_xml.7z/defs/m/mig.trid.xml
# Note:		called "Windows Easy Transfer migration data" by TrID,
#		"Migration Store" or "EasyTransfer file" by Microsoft
0		string		1giM	Windows Easy Transfer migration data
#!:mime		application/octet-stream
!:mime		application/x-ms-mig
!:ext		mig
>0x18		string		=MRTS	without password
# data offset with 1 space at end
>>0x1c		ulelong+0x38	x	\b, at 0x%x 
# look for zlib compressed data by ./compress
>>(0x1c.l+0x38)	ubyte		x
>>>&-1	indirect	x
# in password protected examples MRTS comes some bytes further
>0x18		string		!MRTS	with password
# look for first MRTS tag
>0x18		search/29/b	MRTS
# probably first file name length like 178, ...
#>>&0		ulelong		x	\b, 1st length %u
# URL like File\C:\Users\<USER>\AppData\Roaming\Microsoft\Internet Explorer\Quick Launch\desktop.ini
>>&20		lestring16	x	\b, 1st %-s

# Microsoft SYLK
# https://en.wikipedia.org/wiki/SYmbolic_LinK_(SYLK)
# https://outflank.nl/upload/sylksum.txt
0	string	ID;P	Microsoft SYLK program
>4	string	>0	\b, created by %s
!:ext	slk/sylk

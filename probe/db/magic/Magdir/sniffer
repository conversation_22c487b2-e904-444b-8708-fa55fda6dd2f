
#------------------------------------------------------------------------------
# $File: sniffer,v 1.28 2020/03/13 16:47:29 christos Exp $
# sniffer:  file(1) magic for packet capture files
#
# From: <EMAIL> (<PERSON>)
#

#
# Microsoft Network Monitor 1.x capture files.
#
0	string		RTSS		NetMon capture file
>5	byte		x		- version %d
>4	byte		x		\b.%d
>6	leshort		0		(Unknown)
>6	leshort		1		(Ethernet)
>6	leshort		2		(Token Ring)
>6	leshort		3		(FDDI)
>6	leshort		4		(ATM)
>6	leshort		>4		(type %d)

#
# Microsoft Network Monitor 2.x capture files.
#
0	string		GMBU		NetMon capture file
>5	byte		x		- version %d
>4	byte		x		\b.%d
>6	leshort		0		(Unknown)
>6	leshort		1		(Ethernet)
>6	leshort		2		(Token Ring)
>6	leshort		3		(FDDI)
>6	leshort		4		(ATM)
>6	leshort		5		(IP-over-IEEE 1394)
>6	leshort		6		(802.11)
>6	leshort		7		(Raw IP)
>6	leshort		8		(Raw IP)
>6	leshort		9		(Raw IP)
>6	leshort		>9		(type %d)

#
# Network General Sniffer capture files.
# Sorry, make that "Network Associates Sniffer capture files."
# Sorry, make that "Network General old DOS Sniffer capture files."
#
0	string		TRSNIFF\040data\040\040\040\040\032	Sniffer capture file
>33	byte		2		(compressed)
>23	leshort		x		- version %d
>25	leshort		x		\b.%d
>32	byte		0		(Token Ring)
>32	byte		1		(Ethernet)
>32	byte		2		(ARCNET)
>32	byte		3		(StarLAN)
>32	byte		4		(PC Network broadband)
>32	byte		5		(LocalTalk)
>32	byte		6		(Znet)
>32	byte		7		(Internetwork Analyzer)
>32	byte		9		(FDDI)
>32	byte		10		(ATM)

#
# Cinco Networks NetXRay capture files.
# Sorry, make that "Network General Sniffer Basic capture files."
# Sorry, make that "Network Associates Sniffer Basic capture files."
# Sorry, make that "Network Associates Sniffer Basic, and Windows
# Sniffer Pro", capture files."
# Sorry, make that "Network General Sniffer capture files."
# Sorry, make that "NetScout Sniffer capture files."
#
0	string		XCP\0		NetXRay capture file
>4	string		>\0		- version %s
>44	leshort		0		(Ethernet)
>44	leshort		1		(Token Ring)
>44	leshort		2		(FDDI)
>44	leshort		3		(WAN)
>44	leshort		8		(ATM)
>44	leshort		9		(802.11)

#
# "libpcap" capture files.
# https://www.tcpdump.org/manpages/pcap-savefile.5.html
# (We call them "tcpdump capture file(s)" for now, as "tcpdump" is
# the main program that uses that format, but there are other programs
# that use "libpcap", or that use the same capture file format.)
#
0	name		pcap-be
>4	beshort		x		- version %d
>6	beshort		x		\b.%d
# clear that continuation level match
>20	clear		x
>20	belong&0x03FFFFFF		0		(No link-layer encapsulation
>20	belong&0x03FFFFFF		1		(Ethernet
>20	belong&0x03FFFFFF		2		(3Mb Ethernet
>20	belong&0x03FFFFFF		3		(AX.25
>20	belong&0x03FFFFFF		4		(ProNET
>20	belong&0x03FFFFFF		5		(CHAOS
>20	belong&0x03FFFFFF		6		(Token Ring
>20	belong&0x03FFFFFF		7		(BSD ARCNET
>20	belong&0x03FFFFFF		8		(SLIP
>20	belong&0x03FFFFFF		9		(PPP
>20	belong&0x03FFFFFF		10		(FDDI
>20	belong&0x03FFFFFF		11		(RFC 1483 ATM
>20	belong&0x03FFFFFF		12		(Raw IP
>20	belong&0x03FFFFFF		13		(BSD/OS SLIP
>20	belong&0x03FFFFFF		14		(BSD/OS PPP
>20	belong&0x03FFFFFF		19		(Linux ATM Classical IP
>20	belong&0x03FFFFFF		50		(PPP or Cisco HDLC
>20	belong&0x03FFFFFF		51		(PPP-over-Ethernet
>20	belong&0x03FFFFFF		99		(Symantec Enterprise Firewall
>20	belong&0x03FFFFFF		100		(RFC 1483 ATM
>20	belong&0x03FFFFFF		101		(Raw IP
>20	belong&0x03FFFFFF		102		(BSD/OS SLIP
>20	belong&0x03FFFFFF		103		(BSD/OS PPP
>20	belong&0x03FFFFFF		104		(BSD/OS Cisco HDLC
>20	belong&0x03FFFFFF		105		(802.11
>20	belong&0x03FFFFFF		106		(Linux Classical IP over ATM
>20	belong&0x03FFFFFF		107		(Frame Relay
>20	belong&0x03FFFFFF		108		(OpenBSD loopback
>20	belong&0x03FFFFFF		109		(OpenBSD IPsec encrypted
>20	belong&0x03FFFFFF		112		(Cisco HDLC
>20	belong&0x03FFFFFF		113		(Linux cooked v1
>20	belong&0x03FFFFFF		114		(LocalTalk
>20	belong&0x03FFFFFF		117		(OpenBSD PFLOG
>20	belong&0x03FFFFFF		119		(802.11 with Prism header
>20	belong&0x03FFFFFF		122		(RFC 2625 IP over Fibre Channel
>20	belong&0x03FFFFFF		123		(SunATM
>20	belong&0x03FFFFFF		127		(802.11 with radiotap header
>20	belong&0x03FFFFFF		129		(Linux ARCNET
>20	belong&0x03FFFFFF		130		(Juniper Multi-Link PPP
>20	belong&0x03FFFFFF		131		(Juniper Multi-Link Frame Relay
>20	belong&0x03FFFFFF		132		(Juniper Encryption Services PIC
>20	belong&0x03FFFFFF		133		(Juniper GGSN PIC
>20	belong&0x03FFFFFF		134		(Juniper FRF.16 Frame Relay
>20	belong&0x03FFFFFF		135		(Juniper ATM2 PIC
>20	belong&0x03FFFFFF		136		(Juniper Advanced Services PIC
>20	belong&0x03FFFFFF		137		(Juniper ATM1 PIC
>20	belong&0x03FFFFFF		138		(Apple IP over IEEE 1394
>20	belong&0x03FFFFFF		139		(SS7 MTP2 with pseudo-header
>20	belong&0x03FFFFFF		140		(SS7 MTP2
>20	belong&0x03FFFFFF		141		(SS7 MTP3
>20	belong&0x03FFFFFF		142		(SS7 SCCP
>20	belong&0x03FFFFFF		143		(DOCSIS
>20	belong&0x03FFFFFF		144		(Linux IrDA
>20	belong&0x03FFFFFF		147		(Private use 0
>20	belong&0x03FFFFFF		148		(Private use 1
>20	belong&0x03FFFFFF		149		(Private use 2
>20	belong&0x03FFFFFF		150		(Private use 3
>20	belong&0x03FFFFFF		151		(Private use 4
>20	belong&0x03FFFFFF		152		(Private use 5
>20	belong&0x03FFFFFF		153		(Private use 6
>20	belong&0x03FFFFFF		154		(Private use 7
>20	belong&0x03FFFFFF		155		(Private use 8
>20	belong&0x03FFFFFF		156		(Private use 9
>20	belong&0x03FFFFFF		157		(Private use 10
>20	belong&0x03FFFFFF		158		(Private use 11
>20	belong&0x03FFFFFF		159		(Private use 12
>20	belong&0x03FFFFFF		160		(Private use 13
>20	belong&0x03FFFFFF		161		(Private use 14
>20	belong&0x03FFFFFF		162		(Private use 15
>20	belong&0x03FFFFFF		163		(802.11 with AVS header
>20	belong&0x03FFFFFF		164		(Juniper Passive Monitor PIC
>20	belong&0x03FFFFFF		165		(BACnet MS/TP
>20	belong&0x03FFFFFF		166		(PPPD
>20	belong&0x03FFFFFF		167		(Juniper PPPoE
>20	belong&0x03FFFFFF		168		(Juniper PPPoE/ATM
>20	belong&0x03FFFFFF		169		(GPRS LLC
>20	belong&0x03FFFFFF		170		(GPF-T
>20	belong&0x03FFFFFF		171		(GPF-F
>20	belong&0x03FFFFFF		174		(Juniper PIC Peer
>20	belong&0x03FFFFFF		175		(Ethernet with Endace ERF header
>20	belong&0x03FFFFFF		176		(Packet-over-SONET with Endace ERF header
>20	belong&0x03FFFFFF		177		(Linux LAPD
>20	belong&0x03FFFFFF		178		(Juniper Ethernet
>20	belong&0x03FFFFFF		179		(Juniper PPP
>20	belong&0x03FFFFFF		180		(Juniper Frame Relay
>20	belong&0x03FFFFFF		181		(Juniper C-HDLC
>20	belong&0x03FFFFFF		182		(FRF.16 Frame Relay
>20	belong&0x03FFFFFF		183		(Juniper Voice PIC
>20	belong&0x03FFFFFF		184		(Arinc 429
>20	belong&0x03FFFFFF		185		(Arinc 653 Interpartition Communication
>20	belong&0x03FFFFFF		186		(USB with FreeBSD header
>20	belong&0x03FFFFFF		187		(Bluetooth HCI H4
>20	belong&0x03FFFFFF		188		(802.16 MAC Common Part Sublayer
>20	belong&0x03FFFFFF		189		(Linux USB
>20	belong&0x03FFFFFF		190		(Controller Area Network (CAN) v. 2.0B
>20	belong&0x03FFFFFF		191		(802.15.4 with Linux padding
>20	belong&0x03FFFFFF		192		(PPI
>20	belong&0x03FFFFFF		193		(802.16 MAC Common Part Sublayer plus radiotap header
>20	belong&0x03FFFFFF		194		(Juniper Integrated Service Module
>20	belong&0x03FFFFFF		195		(802.15.4 with FCS
>20	belong&0x03FFFFFF		196		(SITA
>20	belong&0x03FFFFFF		197		(Endace ERF
>20	belong&0x03FFFFFF		198		(Ethernet with u10 Networks pseudo-header
>20	belong&0x03FFFFFF		199		(IPMB
>20	belong&0x03FFFFFF		200		(Juniper Secure Tunnel
>20	belong&0x03FFFFFF		201		(Bluetooth HCI H4 with pseudo-header
>20	belong&0x03FFFFFF		202		(AX.25 with KISS header
>20	belong&0x03FFFFFF		203		(LAPD
>20	belong&0x03FFFFFF		204		(PPP with direction pseudo-header
>20	belong&0x03FFFFFF		205		(Cisco HDLC with direction pseudo-header
>20	belong&0x03FFFFFF		206		(Frame Relay with direction pseudo-header
>20	belong&0x03FFFFFF		209		(Linux IPMB
>20	belong&0x03FFFFFF		215		(802.15.4 with non-ASK PHY header
>20	belong&0x03FFFFFF		216		(Linux evdev events
>20	belong&0x03FFFFFF		219		(MPLS with label as link-layer header
>20	belong&0x03FFFFFF		220		(Memory-mapped Linux USB
>20	belong&0x03FFFFFF		221		(DECT
>20	belong&0x03FFFFFF		222		(AOS Space Data Link protocol
>20	belong&0x03FFFFFF		223		(Wireless HART
>20	belong&0x03FFFFFF		224		(Fibre Channel FC-2
>20	belong&0x03FFFFFF		225		(Fibre Channel FC-2 with frame delimiters
>20	belong&0x03FFFFFF		226		(Solaris IPNET
>20	belong&0x03FFFFFF		227		(SocketCAN
>20	belong&0x03FFFFFF		228		(Raw IPv4
>20	belong&0x03FFFFFF		229		(Raw IPv6
>20	belong&0x03FFFFFF		230		(802.15.4 without FCS
>20	belong&0x03FFFFFF		231		(D-Bus messages
>20	belong&0x03FFFFFF		232		(Juniper Virtual Server
>20	belong&0x03FFFFFF		233		(Juniper SRX E2E
>20	belong&0x03FFFFFF		234		(Juniper Fibre Channel
>20	belong&0x03FFFFFF		235		(DVB-CI
>20	belong&0x03FFFFFF		236		(MUX27010
>20	belong&0x03FFFFFF		237		(STANAG 5066 D_PDUs
>20	belong&0x03FFFFFF		238		(Juniper ATM CEMIC
>20	belong&0x03FFFFFF		239		(Linux netfilter log messages
>20	belong&0x03FFFFFF		240		(Hilscher netAnalyzer
>20	belong&0x03FFFFFF		241		(Hilscher netAnalyzer with delimiters
>20	belong&0x03FFFFFF		242		(IP-over-Infiniband
>20	belong&0x03FFFFFF		243		(MPEG-2 Transport Stream packets
>20	belong&0x03FFFFFF		244		(ng4t ng40
>20	belong&0x03FFFFFF		245		(NFC LLCP
>20	belong&0x03FFFFFF		246		(Packet filter state syncing
>20	belong&0x03FFFFFF		247		(InfiniBand
>20	belong&0x03FFFFFF		248		(SCTP
>20	belong&0x03FFFFFF		249		(USB with USBPcap header
>20	belong&0x03FFFFFF		250		(Schweitzer Engineering Laboratories RTAC packets
>20	belong&0x03FFFFFF		251		(Bluetooth Low Energy air interface
>20	belong&0x03FFFFFF		252		(Wireshark Upper PDU export
>20	belong&0x03FFFFFF		253		(Linux netlink
>20	belong&0x03FFFFFF		254		(Bluetooth Linux Monitor
>20	belong&0x03FFFFFF		255		(Bluetooth Basic Rate/Enhanced Data Rate baseband packets
>20	belong&0x03FFFFFF		256		(Bluetooth Low Energy air interface with pseudo-header
>20	belong&0x03FFFFFF		257		(PROFIBUS data link layer
>20	belong&0x03FFFFFF		258		(Apple DLT_PKTAP
>20	belong&0x03FFFFFF		259		(Ethernet with 802.3 Clause 65 EPON preamble
>20	belong&0x03FFFFFF		260		(IPMI trace packets
>20	belong&0x03FFFFFF		261		(Z-Wave RF profile R1 and R2 packets
>20	belong&0x03FFFFFF		262		(Z-Wave RF profile R3 packets
>20	belong&0x03FFFFFF		263		(WattStopper Digital Lighting Mngmt/Legrand Nitoo Open Proto
>20	belong&0x03FFFFFF		264		(ISO 14443 messages
>20	belong&0x03FFFFFF		265		(IEC 62106 Radio Data System groups
>20	belong&0x03FFFFFF		266		(USB with Darwin header
>20	belong&0x03FFFFFF		267		(OpenBSD DLT_OPENFLOW
>20	belong&0x03FFFFFF		268		(IBM SDLC frames
>20	belong&0x03FFFFFF		269		(TI LLN sniffer frames
>20	belong&0x03FFFFFF		271		(Linux vsock
>20	belong&0x03FFFFFF		272		(Nordic Semiconductor Bluetooth LE sniffer frames
>20	belong&0x03FFFFFF		273		(Excentis XRA-31 DOCSIS 3.1 RF sniffer frames
>20	belong&0x03FFFFFF		274		(802.3br mPackets
>20	belong&0x03FFFFFF		275		(DisplayPort AUX channel monitoring data
>20	belong&0x03FFFFFF		276		(Linux cooked v2
>20	belong&0x03FFFFFF		278		(OpenVizsla USB
>20	belong&0x03FFFFFF		279		(Elektrobit High Speed Capture and Replay (EBHSCR)
>20	belong&0x03FFFFFF		281		(Broadcom tag
>20	belong&0x03FFFFFF		282		(Broadcom tag (prepended)
>20	belong&0x03FFFFFF		284		(Marvell DSA
>20	belong&0x03FFFFFF		285		(Marvell EDSA
# print default match
>20	default		x
>>20	belong		x		(linktype#%u
>16	belong		x		\b, capture length %u)

# packets time stamps in seconds and microseconds.
0	ubelong		0xa1b2c3d4	pcap capture file, microseconds ts (big-endian)
!:mime	application/vnd.tcpdump.pcap
>0	use	pcap-be
0	ulelong		0xa1b2c3d4	pcap capture file, microsecond ts (little-endian)
!:mime	application/vnd.tcpdump.pcap
>0	use	\^pcap-be

# packets time stamps in seconds and nanoseconds.
0	ubelong		0xa1b23c4d	pcap capture file, nanosecond ts (big-endian)
!:mime	application/vnd.tcpdump.pcap
>0	use	pcap-be
0	ulelong		0xa1b23c4d	pcap capture file, nanosecond ts (little-endian)
!:mime	application/vnd.tcpdump.pcap
>0	use	\^pcap-be

#
# "libpcap"-with-Alexey-Kuznetsov's-patches capture files.
#
0	ubelong		0xa1b2cd34	pcap capture file, microsecond ts, extensions (big-endian)
>0	use	pcap-be
0	ulelong		0xa1b2cd34	pcap capture file, microsecond ts, extensions (little-endian)
>0	use	\^pcap-be

#
# "pcapng" capture files.
# https://github.com/pcapng/pcapng
# Pcapng files can contain multiple sections. Printing the endianness,
# snaplen, or other information from the first SHB may be misleading.
#
0	ubelong		0x0a0d0d0a
>8	ubelong		0x1a2b3c4d	pcapng capture file
>>12	beshort		x		- version %d
>>14	beshort		x		\b.%d
0	ulelong		0x0a0d0d0a
>8	ulelong		0x1a2b3c4d	pcapng capture file
>>12	leshort		x		- version %d
>>14	leshort		x		\b.%d

#
# AIX "iptrace" capture files.
#
0	string		iptrace\0401.0	AIX iptrace capture file
0	string		iptrace\0402.0	AIX iptrace capture file

#
# Novell LANalyzer capture files.
#
0	leshort		0x1001		Novell LANalyzer capture file
0	leshort		0x1007		Novell LANalyzer capture file

#
# HP-UX "nettl" capture files.
#
0	string		\x54\x52\x00\x64\x00	HP/UX nettl capture file

#
# RADCOM WAN/LAN Analyzer capture files.
#
0	string		\x42\xd2\x00\x34\x12\x66\x22\x88	RADCOM WAN/LAN Analyzer capture file

#
# NetStumbler log files.  Not really packets, per se, but about as
# close as you can get.  These are log files from NetStumbler, a
# Windows program, that scans for 802.11b networks.
#
0	string		NetS		NetStumbler log file
>8	lelong		x		\b, %d stations found

#
# *Peek tagged capture files.
#
0	string		\177ver		EtherPeek/AiroPeek/OmniPeek capture file

#
# Visual Networks traffic capture files.
#
0	string		\x05VNF		Visual Networks traffic capture file

#
# Network Instruments Observer capture files.
#
0	string		ObserverPktBuffe	Network Instruments Observer capture file

#
# Files from Accellent Group's 5View products.
#
0	string		\xaa\xaa\xaa\xaa	5View capture file

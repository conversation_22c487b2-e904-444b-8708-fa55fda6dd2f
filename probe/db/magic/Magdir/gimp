
#------------------------------------------------------------------------------
# $File: gimp,v 1.10 2019/10/15 18:19:40 christos Exp $
# GIMP Gradient: file(1) magic for the GIMP's gradient data files (.ggr)
# by <PERSON> <federic<PERSON>@nuclecu.unam.mx>

0       string/t        GIMP\ Gradient  GIMP gradient data
#!:mime	text/plain
!:mime	text/x-gimp-ggr
!:ext	ggr

# GIMP palette (.gpl)
# From: <PERSON> <<EMAIL>>
0       string/t        GIMP\ Palette   GIMP palette data
# URL:		https://docs.gimp.org/en/gimp-concepts-palettes.html
# Reference:	http://fileformats.archiveteam.org/wiki/GIMP_Palette
#!:mime	text/plain
!:mime	text/x-gimp-gpl
!:ext	gpl

#------------------------------------------------------------------------------
# XCF:  file(1) magic for the XCF image format used in the GIMP (.xcf) developed
#       by Spencer Kimball and Peter Mattis
#       ('Bucky' LaDieu, <EMAIL>)

# URL:		https://en.wikipedia.org/wiki/XCF_(file_format)
# Reference:	https://gitlab.gnome.org/GNOME/gimp/blob/master/devel-docs/xcf.txt
0	string		gimp\ xcf	GIMP XCF image data,
!:mime	image/x-xcf
!:ext	xcf
>9	string		file		version 0,
>9	string		v		version
>>10	string		>\0		%s,
>14	belong		x		%u x
>18	belong		x		%u,
>22     belong          0               RGB Color
>22     belong          1               Greyscale
>22     belong          2               Indexed Color
>22	belong		>2		Unknown Image Type.

#------------------------------------------------------------------------------
# XCF:  file(1) magic for the patterns used in the GIMP (.pat), developed
#       by Spencer Kimball and Peter Mattis
#       ('Bucky' LaDieu, <EMAIL>)

# Reference:	http://fileformats.archiveteam.org/wiki/GIMP_Pattern
20      string          GPAT            GIMP pattern data,
>24     string          x               %s
!:mime	image/x-gimp-pat
!:ext	pat

#------------------------------------------------------------------------------
# XCF:  file(1) magic for the brushes used in the GIMP (.gbr), developed
#       by Spencer Kimball and Peter Mattis
#       ('Bucky' LaDieu, <EMAIL>)

20      string          GIMP            GIMP brush data
# Reference:	http://fileformats.archiveteam.org/wiki/GIMP_Brush
!:mime	image/x-gimp-gbr
# some sources also list gpb
!:ext	gbr

# From:		Joerg Jenderek
# URL:		https://docs.gimp.org/en/gimp-using-animated-brushes.html
# Reference:	http://fileformats.archiveteam.org/wiki/GIMP_Animated_Brush
# share\gimp\2.0\brushes\Legacy\confetti.gih
0	search/21/b	\040ncells:		GIMP animated brush data
!:mime	image/x-gimp-gih
!:ext	gih

# GIMP Curves File
# <AUTHOR> <EMAIL>
0	string	#\040GIMP\040Curves\040File	GIMP curve file
#!:mime	text/plain
!:mime	text/x-gimp-curve
!:ext	/txt


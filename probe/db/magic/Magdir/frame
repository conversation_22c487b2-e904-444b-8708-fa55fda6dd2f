
#------------------------------------------------------------------------------
# $File: frame,v 1.14 2019/11/25 00:31:30 christos Exp $
# frame:  file(1) magic for FrameMaker files
#
# This stuff came on a FrameMaker demo tape, most of which is
# copyright, but this file is "published" as witness the following:
#
# Note that this is the Framemaker Maker Interchange Format, not the
# Normal format which would be application/vnd.framemaker.
#
0	string		\<MakerFile	FrameMaker document
!:mime	application/x-mif
>11	string		5.5		 (5.5
>11	string		5.0		 (5.0
>11	string		4.0		 (4.0
>11	string		3.0		 (3.0
>11	string		2.0		 (2.0
>11	string		1.0		 (1.0
>14	byte		x		  %c)
# URL:		http://fileformats.archiveteam.org/wiki/Maker_Interchange_Format
# Reference:	https://help.adobe.com/en_US/framemaker/mifreference/mifref.pdf
# Update:	<PERSON><PERSON> 2019 Nov
0	string		\<MIFFile	FrameMaker MIF (ASCII) file
# https://www.iana.org/assignments/media-types/application/vnd.mif
!:mime	application/vnd.mif
# mif most but also find bookTOC.framemif
!:ext	mif/framemif
# followed by space~20h
#>8	ubyte		0x20		\b, space before version
# 3 characters of version number of the MIF language like 1.0, 2.0 ... 2015 ...
>9	string		x		(%.3s
# if not greater sign then display 4th character of version
>12	ubyte		=0x3e		\b)
>12	ubyte		!0x3e		\b%c)
# comment starting with # shows the name+version number of generating program
>13	search/3	#
>>&0	string		x		"%s"
0	search/1	\<MakerDictionary	FrameMaker Dictionary text
!:mime	application/x-mif
>17	string		3.0		 (3.0)
>17	string		2.0		 (2.0)
>17	string		1.0		 (1.x)
0	string		\<MakerScreenFont	FrameMaker Font file
!:mime	application/x-mif
>17	string		1.01		 (%s)
0	string		\<MML		FrameMaker MML file
!:mime	application/x-mif
0	string		\<BookFile	FrameMaker Book file
!:mime	application/x-mif
>10	string		3.0		 (3.0
>10	string		2.0		 (2.0
>10	string		1.0		 (1.0
>13	byte		x		  %c)
# XXX - this book entry should be verified, if you find one, uncomment this
#0	string		\<Book\040 	FrameMaker Book (ASCII) file
#!:mime	application/x-mif
#>6	string		3.0		 (3.0)
#>6	string		2.0		 (2.0)
#>6	string		1.0		 (1.0)
0	string		\<Maker\040Intermediate\040Print\040File	FrameMaker IPL file
!:mime	application/x-mif

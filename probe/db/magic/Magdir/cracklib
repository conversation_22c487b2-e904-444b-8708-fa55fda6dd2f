
#------------------------------------------------------------------------------
# $File: cracklib,v 1.7 2009/09/19 16:28:08 christos Exp $
# cracklib:  file (1) magic for cracklib v2.7

0	lelong	0x70775631	Cracklib password index, little endian
>4	long	>0		(%i words)
>4	long	0		("64-bit")
>>8	long	>-1		(%i words)
0	belong	0x70775631	Cracklib password index, big endian
>4	belong	>-1		(%i words)
# really bellong 0x0000000070775631
0	search/1	\0\0\0\0pwV1	Cracklib password index, big endian ("64-bit")
>12	belong	>0		(%i words)

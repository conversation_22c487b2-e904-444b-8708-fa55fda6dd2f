
#------------------------------------------------------------------------------
# $File: lisp,v 1.26 2019/04/19 00:42:27 christos Exp $
# lisp:  file(1) magic for lisp programs
#
# various lisp types, from <PERSON> (<EMAIL>)

# updated by <PERSON><PERSON>
# GRR: This lot is too weak
#0	string	;;
# windows INF files often begin with semicolon and use CRLF as line end
# lisp files are mainly created on unix system with LF as line end
#>2	search/4096	!\r		Lisp/Scheme program text
#>2	search/4096	\r		Windows INF file

0	search/4096	(setq\ 			Lisp/Scheme program text
!:mime	text/x-lisp
0	search/4096	(defvar\ 		Lisp/Scheme program text
!:mime	text/x-lisp
0	search/4096	(defparam\ 		Lisp/Scheme program text
!:mime	text/x-lisp
0	search/4096	(defun\  		Lisp/Scheme program text
!:mime	text/x-lisp
0	search/4096	(autoload\ 		Lisp/Scheme program text
!:mime	text/x-lisp
0	search/4096	(custom-set-variables\ 	Lisp/Scheme program text
!:mime	text/x-lisp

# URL: https://en.wikipedia.org/wiki/Emacs_Lisp
# Reference: https://ftp.gnu.org/old-gnu/emacs/elisp-manual-18-1.03.tar.gz
# Update: Joerg Jenderek
# Emacs 18 - this is always correct, but not very magical.
0	string	\012(
# look for emacs lisp keywords
# GRR: split regex because it is too long or get error like
# lisp, 36: Warning: cannot get string from `^(defun|defvar|defconst|defmacro|setq|fset|put|provide|require|'
>&0	regex	\^(defun|defvar|defconst|defmacro|setq|fset)	Emacs v18 byte-compiled Lisp data
!:mime	application/x-elc
# https://searchcode.com/codesearch/view/2173420/
# not really pure text
!:apple	EMAxTEXT
!:ext elc
# remaining regex
>&0	regex	\^(put|provide|require|random)	Emacs v18 byte-compiled Lisp data
!:mime	application/x-elc
!:apple	EMAxTEXT
!:ext elc
# missed cl.elc dbx.elc simple.elc look like normal lisp starting with ;;;

# Emacs 19+ - ver. recognition added by Ian Springer
# Also applies to XEmacs 19+ .elc files; could tell them apart with regexs
# <AUTHOR> <EMAIL>
# Update: Joerg Jenderek
0	string	;ELC
# version\0\0\0
>4	byte	>18			Emacs/XEmacs v%d byte-compiled Lisp data
# why less than 32 ? does not make sense to me. GNU Emacs version is 24.5 at April 2015
#>4	byte    <32			Emacs/XEmacs v%d byte-compiled Lisp data
!:mime	application/x-elc
!:apple	EMAxTEXT
!:ext elc

# <AUTHOR> <EMAIL>
0	string	(SYSTEM::VERSION\040'	CLISP byte-compiled Lisp program (pre 2004-03-27)
0	string	(|SYSTEM|::|VERSION|\040'	CLISP byte-compiled Lisp program text

0	long	0x70768BD2		CLISP memory image data
0	long	0xD28B7670		CLISP memory image data, other endian

#.com and .bin for MIT scheme
0	string	\372\372\372\372	MIT scheme (library?)

# <AUTHOR> <EMAIL>
0	search/1	\<TeXmacs|	TeXmacs document text
!:mime	text/texmacs

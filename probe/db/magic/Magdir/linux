
#------------------------------------------------------------------------------
# $File: linux,v 1.72 2020/06/07 21:56:13 christos Exp $
# linux:  file(1) magic for Linux files
#
# Values for Linux/i386 binaries, from <PERSON> <<EMAIL>>
# The following basic Linux magic is useful for reference, but using
# "long" magic is a better practice in order to avoid collisions.
#
# 2	leshort		100		Linux/i386
# >0	leshort		0407		impure executable (OMAGIC)
# >0	leshort		0410		pure executable (NMAGIC)
# >0	leshort		0413		demand-paged executable (ZMAGIC)
# >0	leshort		0314		demand-paged executable (QMAGIC)
#
0	lelong		0x00640107	Linux/i386 impure executable (OMAGIC)
>16	lelong		0		\b, stripped
0	lelong		0x00640108	Linux/i386 pure executable (NMAGIC)
>16	lelong		0		\b, stripped
0	lelong		0x0064010b	Linux/i386 demand-paged executable (ZMAGIC)
>16	lelong		0		\b, stripped
0	lelong		0x006400cc	Linux/i386 demand-paged executable (QMAGIC)
>16	lelong		0		\b, stripped
#
0	string		\007\001\000	Linux/i386 object file
>20	lelong		>0x1020		\b, DLL library
# Linux-8086 stuff:
0	string		\01\03\020\04	Linux-8086 impure executable
>28	long		!0		not stripped
0	string		\01\03\040\04	Linux-8086 executable
>28	long		!0		not stripped
#
0	string		\243\206\001\0	Linux-8086 object file
#
0	string		\01\03\020\20	Minix-386 impure executable
>28	long		!0		not stripped
0	string		\01\03\040\20	Minix-386 executable
>28	long		!0		not stripped
0	string		\01\03\04\20	Minix-386 NSYM/GNU executable
>28	long		!0		not stripped
# <AUTHOR> <EMAIL>
216	lelong		0421		Linux/i386 core file
!:strength / 2
>220	string		>\0		of '%s'
>200	lelong		>0		(signal %d)
#
# <AUTHOR> <EMAIL>
# this can be overridden by the DOS executable (COM) entry
2	string		LILO		Linux/i386 LILO boot/chain loader
#
# <AUTHOR> <EMAIL>
# Updated by Ken Sharp
28	string		make\ config		Linux make config build file (old)
49	search/70	Kernel\ Configuration	Linux make config build file

#
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# See: https://www.win.tue.nl/~aeb/linux/kbd/font-formats-1.html
0	leshort		0x0436		Linux/i386 PC Screen Font v1 data,
>2	byte&0x01	0		256 characters,
>2	byte&0x01	!0		512 characters,
>2	byte&0x02	0		no directory,
>2	byte&0x02	!0		Unicode directory,
>3	byte		>0		8x%d
0	string		\x72\xb5\x4a\x86\x00\x00 Linux/i386 PC Screen Font v2 data,
>16	lelong		x		%d characters,
>12	lelong&0x01	0		no directory,
>12	lelong&0x01	!0		Unicode directory,
>24	lelong		x		%d
>28	lelong		x		\bx%d

# Linux swap and hibernate files
# Linux kernel: include/linux/swap.h
# util-linux: libblkid/src/superblocks/swap.c

# format v0, unsupported since 2002
0xff6	string		SWAP-SPACE	Linux old swap file, 4k page size
0x1ff6	string		SWAP-SPACE	Linux old swap file, 8k page size
0x3ff6	string		SWAP-SPACE	Linux old swap file, 16k page size
0x7ff6	string		SWAP-SPACE	Linux old swap file, 32k page size
0xfff6	string		SWAP-SPACE	Linux old swap file, 64k page size

# format v1, supported since 1998
0		name	linux-swap
>0x400	lelong	1	little endian, version %u,
>>0x404	lelong	x	size %u pages,
>>0x408	lelong	x	%u bad pages,
>0x400	belong	1	big endian, version %u,
>>0x404	belong	x	size %u pages,
>>0x408	belong	x	%u bad pages,
>0x41c	string	\0	no label,
>0x41c	string	>\0	LABEL=%s,
>0x40c	belong	x	UUID=%08x
>0x410	beshort	x	\b-%04x
>0x412	beshort	x	\b-%04x
>0x414	beshort	x	\b-%04x
>0x416	belong	x	\b-%08x
>0x41a	beshort	x	\b%04x

0xff6	string		SWAPSPACE2	Linux swap file, 4k page size,
>0		use			linux-swap
0x1ff6	string		SWAPSPACE2	Linux swap file, 8k page size,
>0		use			linux-swap
0x3ff6	string		SWAPSPACE2	Linux swap file, 16k page size,
>0		use			linux-swap
0x7ff6	string		SWAPSPACE2	Linux swap file, 32k page size,
>0		use			linux-swap
0xfff6	string		SWAPSPACE2	Linux swap file, 64k page size,
>0		use			linux-swap

0	name	linux-hibernate
>0	string	S1SUSPEND	\b, with SWSUSP1 image
>0	string	S2SUSPEND	\b, with SWSUSP2 image
>0	string	ULSUSPEND	\b, with uswsusp image
>0	string	LINHIB0001	\b, with compressed hibernate image
>0	string	\xed\xc3\x02\xe9\x98\x56\xe5\x0c	\b, with tuxonice image
>0	default	x			\b, with unknown hibernate image

0xfec	string		SWAPSPACE2	Linux swap file, 4k page size,
>0		use			linux-swap
>0xff6	use			linux-hibernate
0x1fec	string		SWAPSPACE2	Linux swap file, 8k page size,
>0		use			linux-swap
>0x1ff6	use			linux-hibernate
0x3fec	string		SWAPSPACE2	Linux swap file, 16k page size,
>0		use			linux-swap
>0x3ff6	use			linux-hibernate
0x7fec	string		SWAPSPACE2	Linux swap file, 32k page size,
>0		use			linux-swap
>0x7ff6	use			linux-hibernate
0xffec	string		SWAPSPACE2	Linux swap file, 64k page size,
>0		use			linux-swap
>0xfff6	use			linux-hibernate

#
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# All known start with: b8 c0 07 8e d8 b8 00 90 8e c0 b9 00 01 29 f6 29
# Linux kernel boot images (i386 arch) (Wolfram Kleff)
# URL: https://www.kernel.org/doc/Documentation/x86/boot.txt
514	string		HdrS		Linux kernel
!:strength + 55
# often no extension like in linux, vmlinuz, bzimage or memdisk but sometimes
# Acronis Recovery kernel64.dat and Plop Boot Manager plpbtrom.bin
# DamnSmallLinux 1.5 damnsmll.lnx 
!:ext	/dat/bin/lnx
>510	leshort		0xAA55		x86 boot executable
>>518	leshort		>0x1ff
>>>529	byte		0		zImage,
>>>529	byte		1		bzImage,
>>>526	lelong		>0
>>>>(526.s+0x200) string	>\0	version %s,
>>498	leshort		1		RO-rootFS,
>>498	leshort		0		RW-rootFS,
>>508	leshort		>0		root_dev 0x%X,
>>502	leshort		>0		swap_dev 0x%X,
>>504	leshort		>0		RAMdisksize %u KB,
>>506	leshort		0xFFFF		Normal VGA
>>506	leshort		0xFFFE		Extended VGA
>>506	leshort		0xFFFD		Prompt for Videomode
>>506	leshort		>0		Video mode %d
# This also matches new kernels, which were caught above by "HdrS".
0		belong	0xb8c0078e	Linux kernel
>0x1e3		string	Loading		version 1.3.79 or older
>0x1e9		string	Loading		from prehistoric times

# <AUTHOR> <EMAIL>
8	search/1	\ A\ _text	Linux kernel symbol map text

# <AUTHOR> <EMAIL>
0	search/1	Begin3	Linux Software Map entry text
0	search/1	Begin4	Linux Software Map entry text (new format)

# From Matt Zimmerman, enhanced for v3 by Matthew Palmer
0	belong	0x4f4f4f4d	User-mode Linux COW file
>4	belong	<3		\b, version %d
>>8	string	>\0		\b, backing file %s
>4	belong	>2		\b, version %d
>>32	string	>\0		\b, backing file %s

############################################################################
# Linux kernel versions

0		string		\xb8\xc0\x07\x8e\xd8\xb8\x00\x90	Linux
>497		leshort		0		x86 boot sector
>>514		belong		0x8e	of a kernel from the dawn of time!
>>514		belong		0x908ed8b4	version 0.99-1.1.42
>>514		belong		0x908ed8b8	for memtest86

>497		leshort		!0		x86 kernel
>>504		leshort		>0		RAMdisksize=%u KB
>>502		leshort		>0		swap=0x%X
>>508		leshort		>0		root=0x%X
>>>498		leshort		1		\b-ro
>>>498		leshort		0		\b-rw
>>506		leshort		0xFFFF		vga=normal
>>506		leshort		0xFFFE		vga=extended
>>506		leshort		0xFFFD		vga=ask
>>506		leshort		>0		vga=%d
>>514		belong		0x908ed881	version 1.1.43-1.1.45
>>514		belong		0x15b281cd
>>>0xa8e	belong		0x55AA5a5a	version 1.1.46-1.2.13,1.3.0
>>>0xa99	belong		0x55AA5a5a	version 1.3.1,2
>>>0xaa3	belong		0x55AA5a5a	version 1.3.3-1.3.30
>>>0xaa6	belong		0x55AA5a5a	version 1.3.31-1.3.41
>>>0xb2b	belong		0x55AA5a5a	version 1.3.42-1.3.45
>>>0xaf7	belong		0x55AA5a5a	version 1.3.46-1.3.72
>>514		string		HdrS
>>>518		leshort		>0x1FF
>>>>529		byte		0		\b, zImage
>>>>529		byte		1		\b, bzImage
>>>>(526.s+0x200) string 	>\0		\b, version %s

# Linux boot sector thefts.
0		belong		0xb8c0078e	Linux
>0x1e6		belong		0x454c4b53	ELKS Kernel
>0x1e6		belong		!0x454c4b53	style boot sector

############################################################################
# Linux S390 kernel image
# <AUTHOR> <EMAIL>
8 string \x02\x00\x00\x18\x60\x00\x00\x50\x02\x00\x00\x68\x60\x00\x00\x50\x40\x40\x40\x40\x40\x40\x40\x40 Linux S390
>0x00010000 search/b/4096 \x00\x0a\x00\x00\x8b\xad\xcc\xcc
# 64bit
>>&0 string \xc1\x00\xef\xe3\xf0\x68\x00\x00 Z10 64bit kernel
>>&0 string \xc1\x00\xef\xc3\x00\x00\x00\x00 Z9-109 64bit kernel
>>&0 string \xc0\x00\x20\x00\x00\x00\x00\x00 Z990 64bit kernel
>>&0 string \x00\x00\x00\x00\x00\x00\x00\x00 Z900 64bit kernel
# 32bit
>>&0 string \x81\x00\xc8\x80\x00\x00\x00\x00 Z10 32bit kernel
>>&0 string \x81\x00\xc8\x80\x00\x00\x00\x00 Z9-109 32bit kernel
>>&0 string \x80\x00\x20\x00\x00\x00\x00\x00 Z990 32bit kernel
>>&0 string \x80\x00\x00\x00\x00\x00\x00\x00 Z900 32bit kernel

############################################################################
# Linux ARM compressed kernel image
# <AUTHOR> <EMAIL>
# Update: Joerg Jenderek
0x24	lelong	0x016f2818	Linux kernel ARM boot executable zImage
# There are three posible situations: LE, BE with LE bootloader and pure BE.
# In order to aid telling these apart a new endian flag was added. In order
# to support kernels before the flag and BE with LE bootloader was added we'll
# do a negative check against the BE variant of the flag when we see a LE magic.
>0x30	belong	!0x04030201	(little-endian)
>0x30	belong	0x04030201	(big-endian)
# raspian "kernel7.img", Vu+ Ultimo4K "kernel_auto.bin"
!:ext	img/bin
0x24	belong	0x016f2818	Linux kernel ARM boot executable zImage (big-endian)

############################################################################
# Linux AARCH64 kernel image
0x38    lelong  0x644d5241  Linux kernel ARM64 boot executable Image
>0x18   lelong  ^1          \b, little-endian
>0x18   lelong  &1          \b, big-endian
>0x18   lelong  &2          \b, 4K pages
>0x18   lelong  &4          \b, 16K pages
>0x18   lelong  &6          \b, 32K pages

############################################################################
# Linux 8086 executable
0	lelong&0xFF0000FF 0xC30000E9	Linux-Dev86 executable, headerless
>5	string		.
>>4	string		>\0		\b, libc version %s

0	lelong&0xFF00FFFF 0x4000301	Linux-8086 executable
>2	byte&0x01	!0		\b, unmapped zero page
>2	byte&0x20	0		\b, impure
>2	byte&0x20	!0
>>2	byte&0x10	!0		\b, A_EXEC
>2	byte&0x02	!0		\b, A_PAL
>2	byte&0x04	!0		\b, A_NSYM
>2	byte&0x08	!0		\b, A_STAND
>2	byte&0x40	!0		\b, A_PURE
>2	byte&0x80	!0		\b, A_TOVLY
>28     long            !0              \b, not stripped
>37	string		.
>>36	string		>\0		\b, libc version %s

# 0	lelong&0xFF00FFFF 0x10000301	ld86 I80386 executable
# 0	lelong&0xFF00FFFF 0xB000301	ld86 M68K executable
# 0	lelong&0xFF00FFFF 0xC000301	ld86 NS16K executable
# 0	lelong&0xFF00FFFF 0x17000301	ld86 SPARC executable

# SYSLINUX boot logo files (from 'ppmtolss16' sources)
# https://www.syslinux.org/wiki/index.php/SYSLINUX#Display_graphic_from_filename:
# file extension .lss .16
0	lelong	=0x1413f33d		SYSLINUX' LSS16 image data
# syslinux-4.05/mime/image/x-lss16.xml
!:mime image/x-lss16
>4	leshort	x			\b, width %d
>6	leshort	x			\b, height %d

0	string	OOOM			User-Mode-Linux's Copy-On-Write disk image
>4	belong	x			version %d

# SE Linux policy database
# <AUTHOR> <EMAIL>
0	lelong	0xf97cff8c		SE Linux policy
>16	lelong	x			v%d
>20	lelong	1			MLS
>24	lelong	x			%d symbols
>28	lelong	x			%d ocons

# Linux Logical Volume Manager (LVM)
# <AUTHOR> <EMAIL>
#
# System ID, UUID and volume group name are 128 bytes long
# but they should never be full and initialized with zeros...
#
# LVM1
#
0x0	string/b	HM\001		LVM1 (Linux Logical Volume Manager), version 1
>0x12c	string/b	>\0		, System ID: %s

0x0	string/b	HM\002		LVM1 (Linux Logical Volume Manager), version 2
>0x12c	string/b	>\0		, System ID: %s

#  LVM2
#
# It seems that the label header can be in one the four first sector
# of the disk... (from _find_labeller in lib/label/label.c of LVM2)
#
# 0x200 seems to be the common case
0		name	lvm2
# display UUID in LVM format + display all 32 bytes (instead of max string length: 31)
>0x0          string  >\x2f          \b, UUID: %.6s
>0x6          string  >\x2f          \b-%.4s
>0xa          string  >\x2f          \b-%.4s
>0xe          string  >\x2f          \b-%.4s
>0x12         string  >\x2f          \b-%.4s
>0x16         string  >\x2f          \b-%.4s
>0x1a         string  >\x2f          \b-%.6s
>0x20         lequad  x              \b, size: %lld


# read the offset to add to the start of the header, and the header
# start in 0x200
0x218           string/b  LVM2\ 001      LVM2 PV (Linux Logical Volume Manager)
>&(&-12.l-0x20) use	lvm2

0x018           string/b  LVM2\ 001      LVM2 PV (Linux Logical Volume Manager)
>&(&-12.l-0x20) use	lvm2

0x418           string/b  LVM2\ 001      LVM2 PV (Linux Logical Volume Manager)
>&(&-12.l-0x20) use	lvm2

0x618           string/b  LVM2\ 001      LVM2 PV (Linux Logical Volume Manager)
>&(&-12.l-0x20) use	lvm2

# LVM snapshot
# from Jason Farrel
0	string	SnAp	LVM Snapshot (CopyOnWrite store)
>4	lelong	!0	- valid,
>4	lelong	0	- invalid,
>8	lelong	x	version %d,
>12	lelong	x	chunk_size %d

# SE Linux policy database
0	lelong	0xf97cff8c		SE Linux policy
>16	lelong	x			v%d
>20	lelong	1			MLS
>24	lelong	x			%d symbols
>28	lelong	x			%d ocons

# LUKS: Linux Unified Key Setup, On-Disk Format, http://luks.endorphin.org/spec
# Anthon van der Neut (<EMAIL>)
0	string	LUKS\xba\xbe	LUKS encrypted file,
>6	beshort x		ver %d
>8	string	x		[%s,
>40	string	x		%s,
>72	string	x		%s]
>168	string	x		UUID: %s


# Summary: Xen saved domain file
# <AUTHOR> <EMAIL>
0	string		LinuxGuestRecord	Xen saved domain
>20	search/256	(name
>>&1	string		x			(name %s)

# Type: Xen, the virtual machine monitor
# <AUTHOR> <EMAIL>
0	string		LinuxGuestRecord	Xen saved domain
#>2	regex		\(name\ [^)]*\)		%s
>20	search/256	(name			(name
>>&1	string		x			%s...)

# Systemd journald files
# See https://www.freedesktop.org/wiki/Software/systemd/journal-files/.
# <AUTHOR> <EMAIL>

# check magic
0	string	LPKSHHRH
# check that state is one of known values
>16		ubyte&252	0
# check that each half of three unique id128s is non-zero
>>24		ubequad		>0
>>>32		ubequad		>0
>>>>40		ubequad		>0
>>>>>48		ubequad		>0
>>>>>>56	ubequad		>0
>>>>>>>64	ubequad		>0	Journal file
!:mime application/octet-stream
# provide more info
>>>>>>>>184	leqdate		0	empty
>>>>>>>>16	ubyte		0	\b, offline
>>>>>>>>16	ubyte		1	\b, online
>>>>>>>>16	ubyte		2	\b, archived
>>>>>>>>8	ulelong&1	1	\b, sealed
>>>>>>>>12	ulelong&1	1	\b, compressed

# BCache backing and cache devices
# <AUTHOR> <EMAIL>
0x1008		lequad		8
>0x1018		string		\xc6\x85\x73\xf6\x4e\x1a\x45\xca\x82\x65\xf5\x7f\x48\xba\x6d\x81	BCache
>>0x1010	ulequad		0	cache device
>>0x1010	ulequad		1	backing device
>>0x1010	ulequad		3	cache device
>>0x1010	ulequad		4	backing device
>>0x1048	string		>0	\b, label "%.32s"
>>0x1028	ubelong		x	\b, uuid %08x
>>0x102c	ubeshort	x	\b-%04x
>>0x102e	ubeshort	x	\b-%04x
>>0x1030	ubeshort	x	\b-%04x
>>0x1032	ubelong		x	\b-%08x
>>0x1036	ubeshort	x	\b%04x
>>0x1038	ubelong		x	\b, set uuid %08x
>>0x103c	ubeshort	x	\b-%04x
>>0x103e	ubeshort	x	\b-%04x
>>0x1040	ubeshort	x	\b-%04x
>>0x1042	ubelong		x	\b-%08x
>>0x1046	ubeshort	x	\b%04x

# Linux device tree:
# File format description can be found in the Linux kernel sources at
# Documentation/devicetree/booting-without-of.txt
# From Christoph Biedl
0		belong		0xd00dfeed
# structure and strings must be within blob
>&(8.L)		byte		x
>>&(12.L)	byte		x
>>>20		belong		>1	Device Tree Blob version %d
>>>>4		belong		x	\b, size=%d
>>>>20		belong		>1
>>>>>28		belong		x	\b, boot CPU=%d
>>>>20		belong		>2
>>>>>32		belong		x	\b, string block size=%d
>>>>20		belong		>16
>>>>>36		belong		x	\b, DT structure block size=%d

# glibc locale archive as defined in glibc locale/locarchive.h
0		lelong		0xde020109	locale archive
>24		lelong		x		%d strings

# Linux Software RAID (mdadm)
# <AUTHOR> <EMAIL>
0	name	linuxraid
>16	belong	x		UUID=%8x:
>20	belong	x		\b%8x:
>24	belong	x		\b%8x:
>28	belong	x		\b%8x
>32	string	x		name=%s
>72	lelong	x		level=%d
>92	lelong	x		disks=%d

4096	lelong	0xa92b4efc	Linux Software RAID
>4100	lelong	x		version 1.2 (%d)
>4096	use	linuxraid

0	lelong	0xa92b4efc	Linux Software RAID
>4	lelong	x		version 1.1 (%d)
>0	use	linuxraid

# Summary:     Database file for mlocate
# Description: A database file as used by mlocate, a fast implementation
#              of locate/updatedb. It uses merging to reuse the existing
#              database and avoid rereading most of the filesystem. It's
#              the default version of locate on Arch Linux (and others).
# File path:   /var/lib/mlocate/mlocate.db by default (but configurable)
# Site:        https://fedorahosted.org/mlocate/
# Format docs: https://linux.die.net/man/5/mlocate.db
# Type: mlocate database file
# URL:  https://fedorahosted.org/mlocate/
# <AUTHOR> <EMAIL>
0		string		\0mlocate	mlocate database
>12		byte		x		\b, version %d
>13		byte		1		\b, require visibility
>16		string		x		\b, root %s

# Dump files for iproute2 tool. Generated by the "ip r|a save" command. URL:
# https://www.linuxfoundation.org/collaborate/workgroups/networking/iproute2
# <AUTHOR> <EMAIL>
0		lelong		0x45311224	iproute2 routes dump
0		lelong		0x47361222	iproute2 addresses dump

# Image and service files for CRIU tool.
# URL: https://criu.org
# <AUTHOR> <EMAIL>
0		lelong		0x54564319	CRIU image file v1.1
0		lelong		0x55105940	CRIU service file
0		lelong		0x58313116	CRIU inventory

# Kdump compressed dump files
# https://sourceforge.net/p/makedumpfile/code/ci/master/tree/IMPLEMENTATION

0		string		KDUMP          	Kdump compressed dump
>8		long		x		v%d
>12		string		>\0		\b, system %s
>77		string		>\0		\b, node %s
>142		string		>\0		\b, release %s
>207		string		>\0		\b, version %s
>272		string		>\0		\b, machine %s
>337		string		>\0		\b, domain %s

# Device Tree files
0		search/1024	/dts-v1/	Device Tree File (v1)
# beat c code
!:strength +14

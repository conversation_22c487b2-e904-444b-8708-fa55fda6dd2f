
#------------------------------------------------------------------------------
# $File: os400,v 1.5 2009/09/19 16:28:11 christos Exp $
# os400:  file(1) magic for IBM OS/400 files
#
# IBM OS/400 (i5/OS) Save file (SAVF) - <EMAIL>
# In spite of its quite variable format (due to internal memory page
# length differences between CISC and RISC versions of the OS) the
# SAVF structure hasn't suitable offsets to identify the catalog
# header in the first descriptor where there are some useful infos,
# so we must search in a somewhat large area for a particular string
# that represents the EBCDIC encoding of 'QSRDSSPC' (save/restore
# descriptor space) preceded by a two byte constant.
#
1090	 search/7393	\x19\xDB\xD8\xE2\xD9\xC4\xE2\xE2\xD7\xC3 IBM OS/400 save file data
>&212	 byte		0x01			 \b, created with SAVOBJ
>&212	 byte		0x02			 \b, created with SAVLIB
>&212	 byte		0x07			 \b, created with SAVCFG
>&212	 byte		0x08			 \b, created with SAVSECDTA
>&212	 byte		0x0A			 \b, created with SAVSECDTA
>&212	 byte		0x0B			 \b, created with SAVDLO
>&212	 byte		0x0D			 \b, created with SAVLICPGM
>&212	 byte		0x11			 \b, created with SAVCHGOBJ
>&213	 byte		0x44			 \b, at least V5R4 to open
>&213	 byte		0x43			 \b, at least V5R3 to open
>&213	 byte		0x42			 \b, at least V5R2 to open
>&213	 byte		0x41			 \b, at least V5R1 to open
>&213	 byte		0x40			 \b, at least V4R5 to open
>&213	 byte		0x3F			 \b, at least V4R4 to open
>&213	 byte		0x3E			 \b, at least V4R3 to open
>&213	 byte		0x3C			 \b, at least V4R2 to open
>&213	 byte		0x3D			 \b, at least V4R1M4 to open
>&213	 byte		0x3B			 \b, at least V4R1 to open
>&213	 byte		0x3A			 \b, at least V3R7 to open
>&213	 byte		0x35			 \b, at least V3R6 to open
>&213	 byte		0x36			 \b, at least V3R2 to open
>&213	 byte		0x34			 \b, at least V3R1 to open
>&213	 byte		0x31			 \b, at least V3R0M5 to open
>&213	 byte		0x30			 \b, at least V2R3 to open

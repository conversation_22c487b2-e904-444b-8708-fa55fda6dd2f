
#------------------------------------------------------------------------------
# $File: fsav,v 1.19 2019/04/19 00:42:27 christos Exp $
# fsav:  file(1) magic for datafellows fsav virus definition files
# An<PERSON><PERSON> <PERSON> (<EMAIL>)

# ftp://ftp.f-prot.com/pub/{macrdef2.zip,nomacro.def}
0	beshort		0x1575		fsav macro virus signatures
>8	leshort		>0		(%d-
>11	byte		>0		\b%02d-
>10	byte		>0		\b%02d)
# ftp://ftp.f-prot.com/pub/sign.zip
#10	ubyte		<12
#>9	ubyte		<32
#>>8	ubyte		0x0a
#>>>12	ubyte		0x07
#>>>>11	uleshort	>0		fsav DOS/Windows virus signatures (%d-
#>>>>10	byte		0		\b01-
#>>>>10	byte		1		\b02-
#>>>>10	byte		2		\b03-
#>>>>10	byte		3		\b04-
#>>>>10	byte		4		\b05-
#>>>>10	byte		5		\b06-
#>>>>10	byte		6		\b07-
#>>>>10	byte		7		\b08-
#>>>>10	byte		8		\b09-
#>>>>10	byte		9		\b10-
#>>>>10	byte		10		\b11-
#>>>>10	byte		11		\b12-
#>>>>9	ubyte		>0		\b%02d)
# ftp://ftp.f-prot.com/pub/sign2.zip
#0	ubyte		0x62
#>1	ubyte		0xF5
#>>2	ubyte		0x1
#>>>3	ubyte		0x1
#>>>>4	ubyte		0x0e
#>>>>>13		ubyte	>0		fsav virus signatures
#>>>>>>11	ubyte	x		size 0x%02x
#>>>>>>12	ubyte	x		\b%02x
#>>>>>>13	ubyte	x		\b%02x bytes

# Joerg Jenderek: joerg dot jenderek at web dot de
# clamav-0.100.2\docs\html\node60.html 
# https://github.com/vrtadmin/clamav-faq/raw/master/manual/clamdoc.pdf
# ClamAV virus database files start with a 512 bytes colon separated header
# ClamAV-VDB:buildDate:version:signaturesNumbers:functionalityLevelRequired:MD5:Signature:builder:buildTime
# + gzipped (optional) tarball files
# output can often be verified by `sigtool --info=FILE`
0	string		ClamAV-VDB:	Clam AntiVirus
# padding spaces implies database
>511	ubyte		=0x20		database
!:mime	application/x-clamav-database
# empty build time
>>10	string		=::		(unsigned)
# sigtool(1) man page
!:ext	cud
# display some text to avoid error like:
# Magdir/fsav, 78: Warning: Current entry does not yet have a description for adding a EXTENSION type
# file: could not find any valid magic files! (No error)
>>10	default		x		(with buildtime)
#>>10	default		x
# clamtmp is used for temporily database like update process
# for pure tar database only cld extension found
!:ext	cld/cvd/clamtmp/cud
>511	default		x		file
!:mime	application/x-clamav
!:ext	info
>11	string		>\0
# buildDate empty or like "22 Mar 2017 12-57 -0400"; verified by `sigtool -i FILE`
>>11	regex		\^[^:]{0,23}	\b, %s
# version like 25170
>>>&1	regex		\^[^:]{1,6}	\b, version %s
# signaturesNumbers like 4566249
>>>>&1	regex		\^[^:]{1,10}	\b, %s signatures
# functionalityLevelRequired like 60
>>>>>&1	regex		\^[^:]{1,4}	\b, level %s
# X for nothing or MD5
#>>>>>>&1	regex	\^[^:]{1,32}	\b, MD5 "%s"
>>>>>>&1	regex	\^[^:]{1,32}
# X for nothing or digital signature starting like AIzk/LYbX
#>>>>>>>&1	regex	\^[^:]{1,255}	\b, signature "%s"
>>>>>>>&1	regex	\^[^:]{1,255}
# builder like neo
>>>>>>>>&1	regex	\^[^:]{1,32}	\b, builder %s
# buildTime like 1506611558
#>>>>>>>>>&1	regex	\^[^:]{1,10}	\b, %s
>>>>>>>>>&1	regex	\^[^:]{1,10}	
# padding with spaces
#>>>>>>>>>>&1	ubequad	x		\b, padding 0x%16.16llx
>510	ubyte		=0x20
# inspect real database content
#>>512	ubeshort	x		\b, database MAGIC 0x%x
# ./archive handle pure tar archives
>>1012	quad		=0		\b, with
>>>512	use		tar-file
# not pure tar
>>1012	quad		!0
# one space at the end of text and then handles gziped archives by ./compress
>>>512	string		\037\213	\b, with 
>>>>512	indirect	x

# Type: Grisoft AVG AntiVirus
# <AUTHOR> <EMAIL>
0	string	AVG7_ANTIVIRUS_VAULT_FILE	AVG 7 Antivirus vault file data

0	string	X5O!P%@AP[4\\PZX54(P^)7CC)7}$EICAR
>33	string	-STANDARD-ANTIVIRUS-TEST-FILE!$H+H*	EICAR virus test files

# From: Joerg Jenderek
# URL: https://www.avira.com/
# Note: found in directory %ProgramData%\Avira\Antivirus\INFECTED (Windows)
# tested with version ********** at November 2019
0	string		AntiVir\ Qua	Avira AntiVir quarantined
!:mime	application/x-avira-qua
#!:mime	application/octet-stream
!:ext	qua
>156	string		SUSPICIOUS_FILE
# file path of suspicious file
>>220	lestring16	x		%s
>156	string		!SUSPICIOUS_FILE
# file path of virus file
>>228	lestring16	x		%s
# quarantined date
>60	ldate		x		at %s
# virus/danger name
>156	string		!SUSPICIOUS_FILE
>>156	string		x		\b, category "%s"


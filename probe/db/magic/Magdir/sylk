
#------------------------------------------------------------------------------
# $File: sylk,v 1.1 2020/04/05 22:18:34 christos Exp $
# sylk:  file(1) magic for SYLK text files

# From:	<PERSON><PERSON>
# URL:	https://en.wikipedia.org/wiki/SYmbolic_LinK_%28SYLK%29
#	http://fileformats.archiveteam.org/wiki/SYLK
# Note:	called by TrID "SYLK - SYmbolic LinK data",
#	by DROID "Microsoft Symbolic Link (SYLK) File"
#	by FreeDesktop.org "spreadsheet interchange document"
0	string		ID;P
# skip short DROID x-fmt-106-signature-id-603.slk
>7	ubyte		>0		spreadsheet interchange document
# https://reposcope.com/mimetype/text/spreadsheet
#!:mime	text/spreadsheet
# https://reposcope.com/mimetype/application/x-sylk	by Gnum<PERSON>
!:mime	application/x-sylk
!:ext	slk/sylk
>>4	ubyte		>037		\b, created by
# Gnumeric, pmw~PlanMaker, CALCOOO32~LibreOffice OpenOffice, SCALC3~StarOffice
# MP~Multiplan, XL~Excel WXL~Excel Windows
>>>4	string		Gnumeric	Gnumeric
>>>4	string		pmw		PlanMaker
>>>4	string		CALCOOO32	Libre/OpenOffice Calc
>>>4	string		SCALC3		StarOffice Calc
>>>4	string		XL		Excel
# Excel, version probably running on Windows
>>>4	string		WXL		Excel
# not tested
>>>4	string		MP		Multiplan
# unknown spreadsheet software
>>>4	default		x
>>>>4	string		x		%s



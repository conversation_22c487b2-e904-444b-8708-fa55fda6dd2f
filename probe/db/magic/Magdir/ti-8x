
#------------------------------------------------------------------------------
# $File: ti-8x,v 1.8 2020/02/12 22:13:01 christos Exp $
# ti-8x: file(1) magic for the TI-8x and TI-9x Graphing Calculators.
#
# From: <PERSON> (<EMAIL>).
#
# Update: <PERSON><PERSON> (<EMAIL>).
#
# NOTE: This list is not complete.
# Files for the TI-80 and TI-81 are pretty rare. I'm not going to put the
# program/group magic numbers in here because I cannot find any.
0		string		**TI80**	TI-80 Graphing Calculator File.
0		string		**TI81**	TI-81 Graphing Calculator File.
#
# Magic Numbers for the TI-73
#
0		string		**TI73**	TI-73 Graphing Calculator
>0x00003B	byte		0x00		(real number)
>0x00003B	byte		0x01		(list)
>0x00003B	byte		0x02		(matrix)
>0x00003B	byte		0x03		(equation)
>0x00003B	byte		0x04		(string)
>0x00003B	byte		0x05		(program)
>0x00003B	byte		0x06		(assembly program)
>0x00003B	byte		0x07		(picture)
>0x00003B	byte		0x08		(gdb)
>0x00003B	byte		0x0C		(complex number)
>0x00003B	byte		0x0F		(window settings)
>0x00003B	byte		0x10		(zoom)
>0x00003B	byte		0x11		(table setup)
>0x00003B	byte		0x13		(backup)

# Magic Numbers for the TI-82
#
0		string		**TI82**	TI-82 Graphing Calculator
>0x00003B	byte		0x00		(real)
>0x00003B	byte		0x01		(list)
>0x00003B	byte		0x02		(matrix)
>0x00003B	byte		0x03		(Y-variable)
>0x00003B	byte		0x05		(program)
>0x00003B	byte		0x06		(protected prgm)
>0x00003B	byte		0x07		(picture)
>0x00003B	byte		0x08		(gdb)
>0x00003B	byte		0x0B		(window settings)
>0x00003B	byte		0x0C		(window settings)
>0x00003B	byte		0x0D		(table setup)
>0x00003B	byte		0x0E		(screenshot)
>0x00003B	byte		0x0F		(backup)
#
# Magic Numbers for the TI-83
#
0		string		**TI83**	TI-83 Graphing Calculator
>0x00003B	byte		0x00		(real)
>0x00003B	byte		0x01		(list)
>0x00003B	byte		0x02		(matrix)
>0x00003B	byte		0x03		(Y-variable)
>0x00003B	byte		0x04		(string)
>0x00003B	byte		0x05		(program)
>0x00003B	byte		0x06		(protected prgm)
>0x00003B	byte		0x07		(picture)
>0x00003B	byte		0x08		(gdb)
>0x00003B	byte		0x0B		(window settings)
>0x00003B	byte		0x0C		(window settings)
>0x00003B	byte		0x0D		(table setup)
>0x00003B	byte		0x0E		(screenshot)
>0x00003B	byte		0x13		(backup)
#
# Magic Numbers for the TI-83+
#
0		string		**TI83F*	TI-83+ Graphing Calculator
>0x00003B	byte		0x00		(real number)
>0x00003B	byte		0x01		(list)
>0x00003B	byte		0x02		(matrix)
>0x00003B	byte		0x03		(equation)
>0x00003B	byte		0x04		(string)
>0x00003B	byte		0x05		(program)
>0x00003B	byte		0x06		(assembly program)
>0x00003B	byte		0x07		(picture)
>0x00003B	byte		0x08		(gdb)
>0x00003B	byte		0x0C		(complex number)
>0x00003B	byte		0x0F		(window settings)
>0x00003B	byte		0x10		(zoom)
>0x00003B	byte		0x11		(table setup)
>0x00003B	byte		0x13		(backup)
>0x00003B	byte		0x15		(application variable)
>0x00003B	byte		0x17		(group of variable)

#
# Magic Numbers for the TI-85
#
0		string		**TI85**	TI-85 Graphing Calculator
>0x00003B	byte		0x00		(real number)
>0x00003B	byte		0x01		(complex number)
>0x00003B	byte		0x02		(real vector)
>0x00003B	byte		0x03		(complex vector)
>0x00003B	byte		0x04		(real list)
>0x00003B	byte		0x05		(complex list)
>0x00003B	byte		0x06		(real matrix)
>0x00003B	byte		0x07		(complex matrix)
>0x00003B	byte		0x08		(real constant)
>0x00003B	byte		0x09		(complex constant)
>0x00003B	byte		0x0A		(equation)
>0x00003B	byte		0x0C		(string)
>0x00003B	byte		0x0D		(function GDB)
>0x00003B	byte		0x0E		(polar GDB)
>0x00003B	byte		0x0F		(parametric GDB)
>0x00003B	byte		0x10		(diffeq GDB)
>0x00003B	byte		0x11		(picture)
>0x00003B	byte		0x12		(program)
>0x00003B	byte		0x13		(range)
>0x00003B	byte		0x17		(window settings)
>0x00003B	byte		0x18		(window settings)
>0x00003B	byte		0x19		(window settings)
>0x00003B	byte		0x1A		(window settings)
>0x00003B	byte		0x1B		(zoom)
>0x00003B	byte		0x1D		(backup)
>0x00003B	byte		0x1E		(unknown)
>0x00003B	byte		0x2A		(equation)
>0x000032	string		ZS4		- ZShell Version 4 File.
>0x000032	string		ZS3		- ZShell Version 3 File.
#
# Magic Numbers for the TI-86
#
0		string		**TI86**	TI-86 Graphing Calculator
>0x00003B	byte		0x00		(real number)
>0x00003B	byte		0x01		(complex number)
>0x00003B	byte		0x02		(real vector)
>0x00003B	byte		0x03		(complex vector)
>0x00003B	byte		0x04		(real list)
>0x00003B	byte		0x05		(complex list)
>0x00003B	byte		0x06		(real matrix)
>0x00003B	byte		0x07		(complex matrix)
>0x00003B	byte		0x08		(real constant)
>0x00003B	byte		0x09		(complex constant)
>0x00003B	byte		0x0A		(equation)
>0x00003B	byte		0x0C		(string)
>0x00003B	byte		0x0D		(function GDB)
>0x00003B	byte		0x0E		(polar GDB)
>0x00003B	byte		0x0F		(parametric GDB)
>0x00003B	byte		0x10		(diffeq GDB)
>0x00003B	byte		0x11		(picture)
>0x00003B	byte		0x12		(program)
>0x00003B	byte		0x13		(range)
>0x00003B	byte		0x17		(window settings)
>0x00003B	byte		0x18		(window settings)
>0x00003B	byte		0x19		(window settings)
>0x00003B	byte		0x1A		(window settings)
>0x00003B	byte		0x1B		(zoom)
>0x00003B	byte		0x1D		(backup)
>0x00003B	byte		0x1E		(unknown)
>0x00003B	byte		0x2A		(equation)
#
# Magic Numbers for the TI-89
#
0		string		**TI89**	TI-89 Graphing Calculator
>0x000048	byte		0x00		(expression)
>0x000048	byte		0x04		(list)
>0x000048	byte		0x06		(matrix)
>0x000048	byte		0x0A		(data)
>0x000048	byte		0x0B		(text)
>0x000048	byte		0x0C		(string)
>0x000048	byte		0x0D		(graphic data base)
>0x000048	byte		0x0E		(figure)
>0x000048	byte		0x10		(picture)
>0x000048	byte		0x12		(program)
>0x000048	byte		0x13		(function)
>0x000048	byte		0x14		(macro)
>0x000048	byte		0x1C		(zipped)
>0x000048	byte		0x21		(assembler)
#
# Magic Numbers for the TI-92
#
0		string		**TI92**	TI-92 Graphing Calculator
>0x000048	byte		0x00		(expression)
>0x000048	byte		0x04		(list)
>0x000048	byte		0x06		(matrix)
>0x000048	byte		0x0A		(data)
>0x000048	byte		0x0B		(text)
>0x000048	byte		0x0C		(string)
>0x000048	byte		0x0D		(graphic data base)
>0x000048	byte		0x0E		(figure)
>0x000048	byte		0x10		(picture)
>0x000048	byte		0x12		(program)
>0x000048	byte		0x13		(function)
>0x000048	byte		0x14		(macro)
>0x000048	byte		0x1D		(backup)
#
# Magic Numbers for the TI-92+/V200
#
0		string		**TI92P*	TI-92+/V200 Graphing Calculator
>0x000048	byte		0x00		(expression)
>0x000048	byte		0x04		(list)
>0x000048	byte		0x06		(matrix)
>0x000048	byte		0x0A		(data)
>0x000048	byte		0x0B		(text)
>0x000048	byte		0x0C		(string)
>0x000048	byte		0x0D		(graphic data base)
>0x000048	byte		0x0E		(figure)
>0x000048	byte		0x10		(picture)
>0x000048	byte		0x12		(program)
>0x000048	byte		0x13		(function)
>0x000048	byte		0x14		(macro)
>0x000048	byte		0x1C		(zipped)
>0x000048	byte		0x21		(assembler)
#
# Magic Numbers for the TI-73/83+/89/92+/V200 FLASH upgrades
#
#0x0000016	string		Advanced	TI-XX Graphing Calculator (FLASH)
0		string		**TIFL**	TI-XX Graphing Calculator (FLASH)
>8		byte		>0		- Revision %d
>>9 		byte		x		\b.%d,
>12		byte		>0		Revision date %02x
>>13		byte		x		\b/%02x
>>14		beshort		x		\b/%04x,
>17		string		>/0		name: '%s',
>48		byte		0x74		device: TI-73,
>48		byte		0x73		device: TI-83+,
>48		byte		0x98		device: TI-89,
>48		byte		0x88		device: TI-92+,
>49		byte		0x23		type: OS upgrade,
>49		byte		0x24		type: application,
>49		byte		0x25		type: certificate,
>49		byte		0x3e		type: license,
>74		lelong		>0		size: %d bytes

# VTi & TiEmu skins (TI Graphing Calculators).
# From: Romain Lievin (<EMAIL>).
# Magic Numbers for the VTi skins
0               string          VTI		Virtual TI skin
>3		string		v		- Version
>>4		byte		>0		\b %c
>>6		byte		x		\b.%c
# Magic Numbers for the TiEmu skins
0		string		TiEmu		TiEmu skin
>6              string          v               - Version
>>7             byte            >0              \b %c
>>9             byte            x               \b.%c
>>10		byte		x		\b%c

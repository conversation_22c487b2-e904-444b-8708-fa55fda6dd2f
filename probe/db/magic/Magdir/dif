
#------------------------------------------------------------------------------
# $File: dif,v 1.1 2020/04/09 19:14:01 christos Exp $
# dif:  file(1) magic for DIF text files

#------------------------------------------------------------------------------
# From:	<PERSON><PERSON>
# URL:	http://en.wikipedia.org/wiki/Data_Interchange_Format
#	http://fileformats.archiveteam.org/wiki/Data_Interchange_Format
# Note:	called by TrID "Data Interchange Format",
#	by DROID x-fmt/368 "VisiCalc Database"
0	string		TABLE
# skip text starting with TABLE by looking for numeric version on 2nd line
>6	search/2	0,
# skip DROID x-fmt-41-signature-id-380.dif by looking for key word TUPLES at the beginning
>>27	search/128	TUPLES		Data Interchange Format
# https://www.pcmatic.com/company/libraries/fileextension/detail.asp?ext=dif.html
#!:mime	application/x-dif-spreadsheet	Gnumeric
# https://github.com/LibreOffice/online/blob/master/discovery.xml
#!:mime	application/x-dif-document	LibreOffice
# https://www.wikidata.org/wiki/Wikidata:WikiProject_Informatics/File_formats/Lists/File_formats
!:mime	application/x-dif
# https://extension.nirsoft.net/dif
#!:mime	application/vnd.ms-excel
#!:mime	text/plain
!:ext	dif
# look for double quote 0x22 on 3rd line
>>>10	search/3	"
# skip if next character also double quote 
>>>>&0	ubyte		!0x22		\b, generator or table name
# comment like EXCEL, pwm enclosed in double quotes
>>>>>&-2	string	x		%s


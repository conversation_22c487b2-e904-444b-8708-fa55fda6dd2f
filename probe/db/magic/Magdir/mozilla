
#------------------------------------------------------------------------------
# $File: mozilla,v 1.10 2019/04/19 00:42:27 christos Exp $
# mozilla:  file(1) magic for Mozilla XUL fastload files
# (XUL.mfasl and XPC.mfasl)
# URL:	https://www.mozilla.org/
# From:	<PERSON> <<EMAIL>>

0	string	XPCOM\nMozFASL\r\n\x1A		Mozilla XUL fastload data
# Probably the next magic line contains misspelled "mozLz40\0"
0	string	mozLz4a				Mozilla lz4 compressed bookmark data
# From: Joerg <PERSON>ek
# URL: https://lz4.github.io/lz4/
# Reference: https://github.com/avih/dejsonlz4/archive/master.zip/
# dejsonlz4-master\src\dejsonlz4.c 
# Note: mostly JSON compressed with a non-standard LZ4 header
# can be unpacked by dejsonlz4 but not lz4 programm.
0	string	mozLz40\0			Mozilla lz4 compressed data
!:mime	application/x-lz4+json
# mozlz4 extension seems to be used for search/store, while jsonlz4 for bookmarks
!:ext	jsonlz4/mozlz4
# decomp_size
>8	ulelong	x				\b, originally %u bytes
# lz4 data
#>12	ubequad	x				\b, lz4 data 0x%16.16llx

# From: Joerg Jenderek
# URL: https://en.wikipedia.org/wiki/Firefox_4
# Reference: https://pkware.cachefly.net/webdocs/casestudies/APPNOTE.TXT
# Note:	Most ZIP utilities are able to extract such archives
#	maybe only partly or after some warnings. Example:
#	zip -FF omni.ja --out omni.zip
4	string	PK\001\002	Mozilla archive omni.ja
!:mime	application/x-zip
!:ext	ja
# TODO:
#>4	use	zip-dir-entry

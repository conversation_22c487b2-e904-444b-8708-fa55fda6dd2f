
#------------------------------------------------------------------------------
# $File: lua,v 1.7 2019/04/19 00:42:27 christos Exp $
# lua:  file(1) magic for Lua scripting language
# URL:  https://www.lua.org/
# From: <PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>

# Lua scripts
0	search/1/w	#!\ /usr/bin/lua	Lua script text executable
!:mime	text/x-lua
0	search/1/w	#!\ /usr/local/bin/lua	Lua script text executable
!:mime	text/x-lua
0	search/1	#!/usr/bin/env\ lua	Lua script text executable
!:mime	text/x-lua
0	search/1	#!\ /usr/bin/env\ lua	Lua script text executable
!:mime	text/x-lua

# Lua bytecode
0	string		\033Lua			Lua bytecode,
>4	byte		0x50			version 5.0
>4	byte		0x51			version 5.1
>4	byte		0x52			version 5.2

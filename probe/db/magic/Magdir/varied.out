
#------------------------------------------------------------------------------
# $File: varied.out,v 1.23 2014/04/30 21:41:02 christos Exp $
# varied.out:  file(1) magic for various USG systems
#
#	Herewith many of the object file formats used by USG systems.
#	Most have been moved to files for a particular processor,
#	and deleted if they duplicate other entries.
#
0	short		0610		Perkin-Elmer executable
# AMD 29K
0	beshort		0572		amd 29k coff noprebar executable
0	beshort		01572		amd 29k coff prebar executable
0	beshort		0160007		amd 29k coff archive
# Cray
6	beshort		0407		unicos (cray) executable
# Ultrix 4.3
596	string		\130\337\377\377	Ultrix core file
>600	string		>\0		from '%s'
# BeOS and MAcOS PEF executables
# From: <EMAIL> (<PERSON>)
0	string		Joy!peffpwpc	header for PowerPC PEF executable
#
# ava assembler/linker U<PERSON> Platise <<EMAIL>>
0       string          avaobj  AVR assembler object code
>7      string          >\0     version '%s'
# <AUTHOR> <EMAIL>
0	string		gmon		GNU prof performance data
>4	long		x		- version %d
# <AUTHOR> <EMAIL>
# Harbour <URL:http://harbour-project.org/> HRB files.
0	string		\xc0HRB		Harbour HRB file
>4	leshort		x		version %d
# Harbour HBV files
0	string		\xc0HBV		Harbour variable dump file
>4	leshort		x		version %d

# <AUTHOR> <EMAIL>
# 0	string		exec 		BugOS executable
# 0	string		pack		BugOS archive

# <AUTHOR> <EMAIL>
# Generated by the "examples" in STM's ST40 devkit, and derived code.
0	lelong		0x13a9f17e	ST40 component image format
>4	string		>\0		\b, name '%s'


#
# $File: Makefile.am,v 1.157 2020/05/21 16:22:47 christos Exp $
#
MAGIC_FRAGMENT_BASE = Magdir
MAGIC_DIR = $(top_srcdir)/magic
MAGIC_FRAGMENT_DIR = $(MAGIC_DIR)/$(MAGIC_FRAGMENT_BASE)

pkgdata_DATA = magic.mgc

EXTRA_DIST = \
$(MAGIC_DIR)/Header \
$(MAGIC_DIR)/Localstuff \
$(MAGIC_FRAGMENT_DIR)/acorn \
$(MAGIC_FRAGMENT_DIR)/adi \
$(MAGIC_FRAGMENT_DIR)/adventure \
$(MAGIC_FRAGMENT_DIR)/algol68 \
$(MAGIC_FRAGMENT_DIR)/allegro \
$(MAGIC_FRAGMENT_DIR)/alliant \
$(MAGIC_FRAGMENT_DIR)/amanda \
$(MAGIC_FRAGMENT_DIR)/amigaos \
$(MAGIC_FRAGMENT_DIR)/android \
$(MAGIC_FRAGMENT_DIR)/animation \
$(MAGIC_FRAGMENT_DIR)/aout \
$(MAGIC_FRAGMENT_DIR)/apache \
$(MAGIC_FRAGMENT_DIR)/apl \
$(MAGIC_FRAGMENT_DIR)/apple \
$(MAGIC_FRAGMENT_DIR)/application \
$(MAGIC_FRAGMENT_DIR)/applix \
$(MAGIC_FRAGMENT_DIR)/apt \
$(MAGIC_FRAGMENT_DIR)/archive \
$(MAGIC_FRAGMENT_DIR)/asf \
$(MAGIC_FRAGMENT_DIR)/assembler \
$(MAGIC_FRAGMENT_DIR)/asterix \
$(MAGIC_FRAGMENT_DIR)/att3b \
$(MAGIC_FRAGMENT_DIR)/audio \
$(MAGIC_FRAGMENT_DIR)/basis \
$(MAGIC_FRAGMENT_DIR)/beetle \
$(MAGIC_FRAGMENT_DIR)/ber \
$(MAGIC_FRAGMENT_DIR)/bflt \
$(MAGIC_FRAGMENT_DIR)/bhl \
$(MAGIC_FRAGMENT_DIR)/bioinformatics \
$(MAGIC_FRAGMENT_DIR)/biosig \
$(MAGIC_FRAGMENT_DIR)/blackberry \
$(MAGIC_FRAGMENT_DIR)/blcr \
$(MAGIC_FRAGMENT_DIR)/blender \
$(MAGIC_FRAGMENT_DIR)/blit \
$(MAGIC_FRAGMENT_DIR)/bout \
$(MAGIC_FRAGMENT_DIR)/bsdi \
$(MAGIC_FRAGMENT_DIR)/bsi \
$(MAGIC_FRAGMENT_DIR)/btsnoop \
$(MAGIC_FRAGMENT_DIR)/c-lang \
$(MAGIC_FRAGMENT_DIR)/c64 \
$(MAGIC_FRAGMENT_DIR)/cad \
$(MAGIC_FRAGMENT_DIR)/cafebabe \
$(MAGIC_FRAGMENT_DIR)/cbor \
$(MAGIC_FRAGMENT_DIR)/cddb \
$(MAGIC_FRAGMENT_DIR)/chord \
$(MAGIC_FRAGMENT_DIR)/cisco \
$(MAGIC_FRAGMENT_DIR)/citrus \
$(MAGIC_FRAGMENT_DIR)/clarion \
$(MAGIC_FRAGMENT_DIR)/claris \
$(MAGIC_FRAGMENT_DIR)/clipper \
$(MAGIC_FRAGMENT_DIR)/clojure \
$(MAGIC_FRAGMENT_DIR)/coff \
$(MAGIC_FRAGMENT_DIR)/commands \
$(MAGIC_FRAGMENT_DIR)/communications \
$(MAGIC_FRAGMENT_DIR)/compress \
$(MAGIC_FRAGMENT_DIR)/console \
$(MAGIC_FRAGMENT_DIR)/convex \
$(MAGIC_FRAGMENT_DIR)/coverage \
$(MAGIC_FRAGMENT_DIR)/cracklib \
$(MAGIC_FRAGMENT_DIR)/ctags \
$(MAGIC_FRAGMENT_DIR)/ctf \
$(MAGIC_FRAGMENT_DIR)/cubemap \
$(MAGIC_FRAGMENT_DIR)/cups \
$(MAGIC_FRAGMENT_DIR)/dact \
$(MAGIC_FRAGMENT_DIR)/database \
$(MAGIC_FRAGMENT_DIR)/dataone \
$(MAGIC_FRAGMENT_DIR)/dbpf \
$(MAGIC_FRAGMENT_DIR)/der \
$(MAGIC_FRAGMENT_DIR)/diamond \
$(MAGIC_FRAGMENT_DIR)/dif \
$(MAGIC_FRAGMENT_DIR)/diff \
$(MAGIC_FRAGMENT_DIR)/digital \
$(MAGIC_FRAGMENT_DIR)/dolby \
$(MAGIC_FRAGMENT_DIR)/dump \
$(MAGIC_FRAGMENT_DIR)/dyadic \
$(MAGIC_FRAGMENT_DIR)/ebml \
$(MAGIC_FRAGMENT_DIR)/edid \
$(MAGIC_FRAGMENT_DIR)/editors \
$(MAGIC_FRAGMENT_DIR)/efi \
$(MAGIC_FRAGMENT_DIR)/elf \
$(MAGIC_FRAGMENT_DIR)/encore \
$(MAGIC_FRAGMENT_DIR)/epoc \
$(MAGIC_FRAGMENT_DIR)/erlang \
$(MAGIC_FRAGMENT_DIR)/espressif \
$(MAGIC_FRAGMENT_DIR)/esri \
$(MAGIC_FRAGMENT_DIR)/fcs \
$(MAGIC_FRAGMENT_DIR)/filesystems \
$(MAGIC_FRAGMENT_DIR)/finger \
$(MAGIC_FRAGMENT_DIR)/flash \
$(MAGIC_FRAGMENT_DIR)/flif \
$(MAGIC_FRAGMENT_DIR)/fonts \
$(MAGIC_FRAGMENT_DIR)/forth \
$(MAGIC_FRAGMENT_DIR)/fortran \
$(MAGIC_FRAGMENT_DIR)/frame \
$(MAGIC_FRAGMENT_DIR)/freebsd \
$(MAGIC_FRAGMENT_DIR)/fsav \
$(MAGIC_FRAGMENT_DIR)/fusecompress \
$(MAGIC_FRAGMENT_DIR)/games \
$(MAGIC_FRAGMENT_DIR)/gcc \
$(MAGIC_FRAGMENT_DIR)/gconv \
$(MAGIC_FRAGMENT_DIR)/geo \
$(MAGIC_FRAGMENT_DIR)/geos \
$(MAGIC_FRAGMENT_DIR)/gimp \
$(MAGIC_FRAGMENT_DIR)/git \
$(MAGIC_FRAGMENT_DIR)/glibc \
$(MAGIC_FRAGMENT_DIR)/gnome \
$(MAGIC_FRAGMENT_DIR)/gnu \
$(MAGIC_FRAGMENT_DIR)/gnumeric \
$(MAGIC_FRAGMENT_DIR)/gpt \
$(MAGIC_FRAGMENT_DIR)/gpu \
$(MAGIC_FRAGMENT_DIR)/grace \
$(MAGIC_FRAGMENT_DIR)/graphviz \
$(MAGIC_FRAGMENT_DIR)/gringotts \
$(MAGIC_FRAGMENT_DIR)/guile \
$(MAGIC_FRAGMENT_DIR)/hardware \
$(MAGIC_FRAGMENT_DIR)/hitachi-sh \
$(MAGIC_FRAGMENT_DIR)/hp \
$(MAGIC_FRAGMENT_DIR)/human68k \
$(MAGIC_FRAGMENT_DIR)/ibm370 \
$(MAGIC_FRAGMENT_DIR)/ibm6000 \
$(MAGIC_FRAGMENT_DIR)/icc \
$(MAGIC_FRAGMENT_DIR)/iff \
$(MAGIC_FRAGMENT_DIR)/images \
$(MAGIC_FRAGMENT_DIR)/inform \
$(MAGIC_FRAGMENT_DIR)/intel \
$(MAGIC_FRAGMENT_DIR)/interleaf \
$(MAGIC_FRAGMENT_DIR)/island \
$(MAGIC_FRAGMENT_DIR)/ispell \
$(MAGIC_FRAGMENT_DIR)/isz \
$(MAGIC_FRAGMENT_DIR)/java \
$(MAGIC_FRAGMENT_DIR)/javascript \
$(MAGIC_FRAGMENT_DIR)/jpeg \
$(MAGIC_FRAGMENT_DIR)/karma \
$(MAGIC_FRAGMENT_DIR)/kde \
$(MAGIC_FRAGMENT_DIR)/keepass \
$(MAGIC_FRAGMENT_DIR)/kerberos \
$(MAGIC_FRAGMENT_DIR)/kicad \
$(MAGIC_FRAGMENT_DIR)/kml \
$(MAGIC_FRAGMENT_DIR)/lecter \
$(MAGIC_FRAGMENT_DIR)/lex \
$(MAGIC_FRAGMENT_DIR)/lif \
$(MAGIC_FRAGMENT_DIR)/linux \
$(MAGIC_FRAGMENT_DIR)/lisp \
$(MAGIC_FRAGMENT_DIR)/llvm \
$(MAGIC_FRAGMENT_DIR)/lua \
$(MAGIC_FRAGMENT_DIR)/luks \
$(MAGIC_FRAGMENT_DIR)/m4 \
$(MAGIC_FRAGMENT_DIR)/mach \
$(MAGIC_FRAGMENT_DIR)/macintosh \
$(MAGIC_FRAGMENT_DIR)/macos \
$(MAGIC_FRAGMENT_DIR)/magic \
$(MAGIC_FRAGMENT_DIR)/mail.news \
$(MAGIC_FRAGMENT_DIR)/make \
$(MAGIC_FRAGMENT_DIR)/map \
$(MAGIC_FRAGMENT_DIR)/maple \
$(MAGIC_FRAGMENT_DIR)/marc21 \
$(MAGIC_FRAGMENT_DIR)/mathcad \
$(MAGIC_FRAGMENT_DIR)/mathematica \
$(MAGIC_FRAGMENT_DIR)/matroska \
$(MAGIC_FRAGMENT_DIR)/mcrypt \
$(MAGIC_FRAGMENT_DIR)/measure \
$(MAGIC_FRAGMENT_DIR)/mercurial \
$(MAGIC_FRAGMENT_DIR)/metastore \
$(MAGIC_FRAGMENT_DIR)/meteorological \
$(MAGIC_FRAGMENT_DIR)/microfocus \
$(MAGIC_FRAGMENT_DIR)/mime \
$(MAGIC_FRAGMENT_DIR)/mips \
$(MAGIC_FRAGMENT_DIR)/mirage \
$(MAGIC_FRAGMENT_DIR)/misctools \
$(MAGIC_FRAGMENT_DIR)/mkid \
$(MAGIC_FRAGMENT_DIR)/mlssa \
$(MAGIC_FRAGMENT_DIR)/mmdf \
$(MAGIC_FRAGMENT_DIR)/modem \
$(MAGIC_FRAGMENT_DIR)/modulefile \
$(MAGIC_FRAGMENT_DIR)/motorola \
$(MAGIC_FRAGMENT_DIR)/mozilla \
$(MAGIC_FRAGMENT_DIR)/msdos \
$(MAGIC_FRAGMENT_DIR)/msooxml \
$(MAGIC_FRAGMENT_DIR)/msvc \
$(MAGIC_FRAGMENT_DIR)/msx \
$(MAGIC_FRAGMENT_DIR)/mup \
$(MAGIC_FRAGMENT_DIR)/music \
$(MAGIC_FRAGMENT_DIR)/nasa \
$(MAGIC_FRAGMENT_DIR)/natinst \
$(MAGIC_FRAGMENT_DIR)/ncr \
$(MAGIC_FRAGMENT_DIR)/neko \
$(MAGIC_FRAGMENT_DIR)/netbsd \
$(MAGIC_FRAGMENT_DIR)/netscape \
$(MAGIC_FRAGMENT_DIR)/netware \
$(MAGIC_FRAGMENT_DIR)/news \
$(MAGIC_FRAGMENT_DIR)/nitpicker \
$(MAGIC_FRAGMENT_DIR)/numpy \
$(MAGIC_FRAGMENT_DIR)/oasis \
$(MAGIC_FRAGMENT_DIR)/ocaml \
$(MAGIC_FRAGMENT_DIR)/octave \
$(MAGIC_FRAGMENT_DIR)/ole2compounddocs \
$(MAGIC_FRAGMENT_DIR)/olf \
$(MAGIC_FRAGMENT_DIR)/openfst \
$(MAGIC_FRAGMENT_DIR)/opentimestamps \
$(MAGIC_FRAGMENT_DIR)/os2 \
$(MAGIC_FRAGMENT_DIR)/os400 \
$(MAGIC_FRAGMENT_DIR)/os9 \
$(MAGIC_FRAGMENT_DIR)/osf1 \
$(MAGIC_FRAGMENT_DIR)/palm \
$(MAGIC_FRAGMENT_DIR)/parix \
$(MAGIC_FRAGMENT_DIR)/parrot \
$(MAGIC_FRAGMENT_DIR)/pascal \
$(MAGIC_FRAGMENT_DIR)/pbf \
$(MAGIC_FRAGMENT_DIR)/pbm \
$(MAGIC_FRAGMENT_DIR)/pc88 \
$(MAGIC_FRAGMENT_DIR)/pc98 \
$(MAGIC_FRAGMENT_DIR)/pdf \
$(MAGIC_FRAGMENT_DIR)/pdp \
$(MAGIC_FRAGMENT_DIR)/perl \
$(MAGIC_FRAGMENT_DIR)/pgf \
$(MAGIC_FRAGMENT_DIR)/pgp \
$(MAGIC_FRAGMENT_DIR)/pkgadd \
$(MAGIC_FRAGMENT_DIR)/plan9 \
$(MAGIC_FRAGMENT_DIR)/plus5 \
$(MAGIC_FRAGMENT_DIR)/pmem \
$(MAGIC_FRAGMENT_DIR)/polyml \
$(MAGIC_FRAGMENT_DIR)/printer \
$(MAGIC_FRAGMENT_DIR)/project \
$(MAGIC_FRAGMENT_DIR)/psdbms \
$(MAGIC_FRAGMENT_DIR)/psl \
$(MAGIC_FRAGMENT_DIR)/pulsar \
$(MAGIC_FRAGMENT_DIR)/pwsafe \
$(MAGIC_FRAGMENT_DIR)/pyramid \
$(MAGIC_FRAGMENT_DIR)/python \
$(MAGIC_FRAGMENT_DIR)/qt \
$(MAGIC_FRAGMENT_DIR)/revision \
$(MAGIC_FRAGMENT_DIR)/riff \
$(MAGIC_FRAGMENT_DIR)/rpi \
$(MAGIC_FRAGMENT_DIR)/rpm \
$(MAGIC_FRAGMENT_DIR)/rpmsg \
$(MAGIC_FRAGMENT_DIR)/rtf \
$(MAGIC_FRAGMENT_DIR)/rst \
$(MAGIC_FRAGMENT_DIR)/ruby \
$(MAGIC_FRAGMENT_DIR)/sc \
$(MAGIC_FRAGMENT_DIR)/sccs \
$(MAGIC_FRAGMENT_DIR)/scientific \
$(MAGIC_FRAGMENT_DIR)/securitycerts \
$(MAGIC_FRAGMENT_DIR)/selinux \
$(MAGIC_FRAGMENT_DIR)/sendmail \
$(MAGIC_FRAGMENT_DIR)/sequent \
$(MAGIC_FRAGMENT_DIR)/sereal \
$(MAGIC_FRAGMENT_DIR)/sgi \
$(MAGIC_FRAGMENT_DIR)/sgml \
$(MAGIC_FRAGMENT_DIR)/sharc \
$(MAGIC_FRAGMENT_DIR)/sinclair \
$(MAGIC_FRAGMENT_DIR)/sisu \
$(MAGIC_FRAGMENT_DIR)/sketch \
$(MAGIC_FRAGMENT_DIR)/smalltalk \
$(MAGIC_FRAGMENT_DIR)/smile \
$(MAGIC_FRAGMENT_DIR)/sniffer \
$(MAGIC_FRAGMENT_DIR)/softquad \
$(MAGIC_FRAGMENT_DIR)/sosi \
$(MAGIC_FRAGMENT_DIR)/spec \
$(MAGIC_FRAGMENT_DIR)/spectrum \
$(MAGIC_FRAGMENT_DIR)/sql \
$(MAGIC_FRAGMENT_DIR)/ssh \
$(MAGIC_FRAGMENT_DIR)/ssl \
$(MAGIC_FRAGMENT_DIR)/sun \
$(MAGIC_FRAGMENT_DIR)/sylk \
$(MAGIC_FRAGMENT_DIR)/symbos \
$(MAGIC_FRAGMENT_DIR)/sysex \
$(MAGIC_FRAGMENT_DIR)/tcl \
$(MAGIC_FRAGMENT_DIR)/teapot \
$(MAGIC_FRAGMENT_DIR)/terminfo \
$(MAGIC_FRAGMENT_DIR)/tex \
$(MAGIC_FRAGMENT_DIR)/tgif \
$(MAGIC_FRAGMENT_DIR)/ti-8x \
$(MAGIC_FRAGMENT_DIR)/timezone \
$(MAGIC_FRAGMENT_DIR)/tplink \
$(MAGIC_FRAGMENT_DIR)/troff \
$(MAGIC_FRAGMENT_DIR)/tuxedo \
$(MAGIC_FRAGMENT_DIR)/typeset \
$(MAGIC_FRAGMENT_DIR)/unicode \
$(MAGIC_FRAGMENT_DIR)/unisig \
$(MAGIC_FRAGMENT_DIR)/unknown \
$(MAGIC_FRAGMENT_DIR)/usd \
$(MAGIC_FRAGMENT_DIR)/uterus \
$(MAGIC_FRAGMENT_DIR)/uuencode \
$(MAGIC_FRAGMENT_DIR)/vacuum-cleaner \
$(MAGIC_FRAGMENT_DIR)/varied.out \
$(MAGIC_FRAGMENT_DIR)/varied.script \
$(MAGIC_FRAGMENT_DIR)/vax \
$(MAGIC_FRAGMENT_DIR)/vicar \
$(MAGIC_FRAGMENT_DIR)/virtual \
$(MAGIC_FRAGMENT_DIR)/virtutech \
$(MAGIC_FRAGMENT_DIR)/visx \
$(MAGIC_FRAGMENT_DIR)/vms \
$(MAGIC_FRAGMENT_DIR)/vmware \
$(MAGIC_FRAGMENT_DIR)/vorbis \
$(MAGIC_FRAGMENT_DIR)/vxl \
$(MAGIC_FRAGMENT_DIR)/warc \
$(MAGIC_FRAGMENT_DIR)/web \
$(MAGIC_FRAGMENT_DIR)/weak \
$(MAGIC_FRAGMENT_DIR)/webassembly \
$(MAGIC_FRAGMENT_DIR)/windows \
$(MAGIC_FRAGMENT_DIR)/wireless \
$(MAGIC_FRAGMENT_DIR)/wordprocessors \
$(MAGIC_FRAGMENT_DIR)/wsdl \
$(MAGIC_FRAGMENT_DIR)/x68000 \
$(MAGIC_FRAGMENT_DIR)/xdelta \
$(MAGIC_FRAGMENT_DIR)/xenix \
$(MAGIC_FRAGMENT_DIR)/xilinx \
$(MAGIC_FRAGMENT_DIR)/xo65 \
$(MAGIC_FRAGMENT_DIR)/xwindows \
$(MAGIC_FRAGMENT_DIR)/yara \
$(MAGIC_FRAGMENT_DIR)/zfs \
$(MAGIC_FRAGMENT_DIR)/zilog \
$(MAGIC_FRAGMENT_DIR)/zip \
$(MAGIC_FRAGMENT_DIR)/zyxel

MAGIC = magic.mgc
CLEANFILES = ${MAGIC} $(MAGIC_FRAGMENT_DIR)/Localstuff

# FIXME: Build file natively as well so that it can be used to compile
# the target's magic file; for now we bail if the local version does not match
if IS_CROSS_COMPILE
FILE_COMPILE = file${EXEEXT}
FILE_COMPILE_DEP =
else
FILE_COMPILE = $(top_builddir)/src/file${EXEEXT}
FILE_COMPILE_DEP = $(FILE_COMPILE)
endif

${MAGIC}: $(EXTRA_DIST) $(FILE_COMPILE_DEP)
	@rm -fr magic
	@mkdir magic && cp -p $(EXTRA_DIST) magic
	@(if expr "${FILE_COMPILE}" : '.*/.*' > /dev/null; then \
	    echo "Using ${FILE_COMPILE} to generate ${MAGIC}" > /dev/null; \
	  else \
	    v=$$(${FILE_COMPILE} --version | sed -e s/file-// -e q); \
	    if [ "$$v" != "${PACKAGE_VERSION}" ]; then \
		echo "Cannot use the installed version of file ($$v) to"; \
		echo "cross-compile file ${PACKAGE_VERSION}"; \
		echo "Please install file ${PACKAGE_VERSION} locally first"; \
		exit 1; \
	    fi; \
	  fi)
	$(FILE_COMPILE) -C -m magic
	@rm -fr magic

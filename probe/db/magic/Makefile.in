# Makefile.in generated by automake 1.16.1 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2018 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@

VPATH = @srcdir@
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
subdir = magic
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/libtool.m4 \
	$(top_srcdir)/m4/ltoptions.m4 $(top_srcdir)/m4/ltsugar.m4 \
	$(top_srcdir)/m4/ltversion.m4 $(top_srcdir)/m4/lt~obsolete.m4 \
	$(top_srcdir)/acinclude.m4 $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
SOURCES =
DIST_SOURCES =
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  test -z "$$files" \
    || { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
    || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
         $(am__cd) "$$dir" && rm -f $$files; }; \
  }
am__installdirs = "$(DESTDIR)$(pkgdatadir)"
DATA = $(pkgdata_DATA)
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
am__DIST_COMMON = $(srcdir)/Makefile.in
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
pkgdatadir = @pkgdatadir@
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AR = @AR@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CFLAG_VISIBILITY = @CFLAG_VISIBILITY@
CPP = @CPP@
CPPFLAGS = @CPPFLAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
DLLTOOL = @DLLTOOL@
DSYMUTIL = @DSYMUTIL@
DUMPBIN = @DUMPBIN@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
EXEEXT = @EXEEXT@
FGREP = @FGREP@
GREP = @GREP@
HAVE_VISIBILITY = @HAVE_VISIBILITY@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
LD = @LD@
LDFLAGS = @LDFLAGS@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LIBTOOL = @LIBTOOL@
LIPO = @LIPO@
LN_S = @LN_S@
LTLIBOBJS = @LTLIBOBJS@
LT_SYS_LIBRARY_PATH = @LT_SYS_LIBRARY_PATH@
MAKEINFO = @MAKEINFO@
MANIFEST_TOOL = @MANIFEST_TOOL@
MINGW = @MINGW@
MKDIR_P = @MKDIR_P@
NM = @NM@
NMEDIT = @NMEDIT@
OBJDUMP = @OBJDUMP@
OBJEXT = @OBJEXT@
OTOOL = @OTOOL@
OTOOL64 = @OTOOL64@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
RANLIB = @RANLIB@
SED = @SED@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
STRIP = @STRIP@
VERSION = @VERSION@
WARNINGS = @WARNINGS@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_AR = @ac_ct_AR@
ac_ct_CC = @ac_ct_CC@
ac_ct_DUMPBIN = @ac_ct_DUMPBIN@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
fsect = @fsect@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target_alias = @target_alias@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@

#
# $File: Makefile.am,v 1.157 2020/05/21 16:22:47 christos Exp $
#
MAGIC_FRAGMENT_BASE = Magdir
MAGIC_DIR = $(top_srcdir)/magic
MAGIC_FRAGMENT_DIR = $(MAGIC_DIR)/$(MAGIC_FRAGMENT_BASE)
pkgdata_DATA = magic.mgc
EXTRA_DIST = \
$(MAGIC_DIR)/Header \
$(MAGIC_DIR)/Localstuff \
$(MAGIC_FRAGMENT_DIR)/acorn \
$(MAGIC_FRAGMENT_DIR)/adi \
$(MAGIC_FRAGMENT_DIR)/adventure \
$(MAGIC_FRAGMENT_DIR)/algol68 \
$(MAGIC_FRAGMENT_DIR)/allegro \
$(MAGIC_FRAGMENT_DIR)/alliant \
$(MAGIC_FRAGMENT_DIR)/amanda \
$(MAGIC_FRAGMENT_DIR)/amigaos \
$(MAGIC_FRAGMENT_DIR)/android \
$(MAGIC_FRAGMENT_DIR)/animation \
$(MAGIC_FRAGMENT_DIR)/aout \
$(MAGIC_FRAGMENT_DIR)/apache \
$(MAGIC_FRAGMENT_DIR)/apl \
$(MAGIC_FRAGMENT_DIR)/apple \
$(MAGIC_FRAGMENT_DIR)/application \
$(MAGIC_FRAGMENT_DIR)/applix \
$(MAGIC_FRAGMENT_DIR)/apt \
$(MAGIC_FRAGMENT_DIR)/archive \
$(MAGIC_FRAGMENT_DIR)/asf \
$(MAGIC_FRAGMENT_DIR)/assembler \
$(MAGIC_FRAGMENT_DIR)/asterix \
$(MAGIC_FRAGMENT_DIR)/att3b \
$(MAGIC_FRAGMENT_DIR)/audio \
$(MAGIC_FRAGMENT_DIR)/basis \
$(MAGIC_FRAGMENT_DIR)/beetle \
$(MAGIC_FRAGMENT_DIR)/ber \
$(MAGIC_FRAGMENT_DIR)/bflt \
$(MAGIC_FRAGMENT_DIR)/bhl \
$(MAGIC_FRAGMENT_DIR)/bioinformatics \
$(MAGIC_FRAGMENT_DIR)/biosig \
$(MAGIC_FRAGMENT_DIR)/blackberry \
$(MAGIC_FRAGMENT_DIR)/blcr \
$(MAGIC_FRAGMENT_DIR)/blender \
$(MAGIC_FRAGMENT_DIR)/blit \
$(MAGIC_FRAGMENT_DIR)/bout \
$(MAGIC_FRAGMENT_DIR)/bsdi \
$(MAGIC_FRAGMENT_DIR)/bsi \
$(MAGIC_FRAGMENT_DIR)/btsnoop \
$(MAGIC_FRAGMENT_DIR)/c-lang \
$(MAGIC_FRAGMENT_DIR)/c64 \
$(MAGIC_FRAGMENT_DIR)/cad \
$(MAGIC_FRAGMENT_DIR)/cafebabe \
$(MAGIC_FRAGMENT_DIR)/cbor \
$(MAGIC_FRAGMENT_DIR)/cddb \
$(MAGIC_FRAGMENT_DIR)/chord \
$(MAGIC_FRAGMENT_DIR)/cisco \
$(MAGIC_FRAGMENT_DIR)/citrus \
$(MAGIC_FRAGMENT_DIR)/clarion \
$(MAGIC_FRAGMENT_DIR)/claris \
$(MAGIC_FRAGMENT_DIR)/clipper \
$(MAGIC_FRAGMENT_DIR)/clojure \
$(MAGIC_FRAGMENT_DIR)/coff \
$(MAGIC_FRAGMENT_DIR)/commands \
$(MAGIC_FRAGMENT_DIR)/communications \
$(MAGIC_FRAGMENT_DIR)/compress \
$(MAGIC_FRAGMENT_DIR)/console \
$(MAGIC_FRAGMENT_DIR)/convex \
$(MAGIC_FRAGMENT_DIR)/coverage \
$(MAGIC_FRAGMENT_DIR)/cracklib \
$(MAGIC_FRAGMENT_DIR)/ctags \
$(MAGIC_FRAGMENT_DIR)/ctf \
$(MAGIC_FRAGMENT_DIR)/cubemap \
$(MAGIC_FRAGMENT_DIR)/cups \
$(MAGIC_FRAGMENT_DIR)/dact \
$(MAGIC_FRAGMENT_DIR)/database \
$(MAGIC_FRAGMENT_DIR)/dataone \
$(MAGIC_FRAGMENT_DIR)/dbpf \
$(MAGIC_FRAGMENT_DIR)/der \
$(MAGIC_FRAGMENT_DIR)/diamond \
$(MAGIC_FRAGMENT_DIR)/dif \
$(MAGIC_FRAGMENT_DIR)/diff \
$(MAGIC_FRAGMENT_DIR)/digital \
$(MAGIC_FRAGMENT_DIR)/dolby \
$(MAGIC_FRAGMENT_DIR)/dump \
$(MAGIC_FRAGMENT_DIR)/dyadic \
$(MAGIC_FRAGMENT_DIR)/ebml \
$(MAGIC_FRAGMENT_DIR)/edid \
$(MAGIC_FRAGMENT_DIR)/editors \
$(MAGIC_FRAGMENT_DIR)/efi \
$(MAGIC_FRAGMENT_DIR)/elf \
$(MAGIC_FRAGMENT_DIR)/encore \
$(MAGIC_FRAGMENT_DIR)/epoc \
$(MAGIC_FRAGMENT_DIR)/erlang \
$(MAGIC_FRAGMENT_DIR)/espressif \
$(MAGIC_FRAGMENT_DIR)/esri \
$(MAGIC_FRAGMENT_DIR)/fcs \
$(MAGIC_FRAGMENT_DIR)/filesystems \
$(MAGIC_FRAGMENT_DIR)/finger \
$(MAGIC_FRAGMENT_DIR)/flash \
$(MAGIC_FRAGMENT_DIR)/flif \
$(MAGIC_FRAGMENT_DIR)/fonts \
$(MAGIC_FRAGMENT_DIR)/forth \
$(MAGIC_FRAGMENT_DIR)/fortran \
$(MAGIC_FRAGMENT_DIR)/frame \
$(MAGIC_FRAGMENT_DIR)/freebsd \
$(MAGIC_FRAGMENT_DIR)/fsav \
$(MAGIC_FRAGMENT_DIR)/fusecompress \
$(MAGIC_FRAGMENT_DIR)/games \
$(MAGIC_FRAGMENT_DIR)/gcc \
$(MAGIC_FRAGMENT_DIR)/gconv \
$(MAGIC_FRAGMENT_DIR)/geo \
$(MAGIC_FRAGMENT_DIR)/geos \
$(MAGIC_FRAGMENT_DIR)/gimp \
$(MAGIC_FRAGMENT_DIR)/git \
$(MAGIC_FRAGMENT_DIR)/glibc \
$(MAGIC_FRAGMENT_DIR)/gnome \
$(MAGIC_FRAGMENT_DIR)/gnu \
$(MAGIC_FRAGMENT_DIR)/gnumeric \
$(MAGIC_FRAGMENT_DIR)/gpt \
$(MAGIC_FRAGMENT_DIR)/gpu \
$(MAGIC_FRAGMENT_DIR)/grace \
$(MAGIC_FRAGMENT_DIR)/graphviz \
$(MAGIC_FRAGMENT_DIR)/gringotts \
$(MAGIC_FRAGMENT_DIR)/guile \
$(MAGIC_FRAGMENT_DIR)/hardware \
$(MAGIC_FRAGMENT_DIR)/hitachi-sh \
$(MAGIC_FRAGMENT_DIR)/hp \
$(MAGIC_FRAGMENT_DIR)/human68k \
$(MAGIC_FRAGMENT_DIR)/ibm370 \
$(MAGIC_FRAGMENT_DIR)/ibm6000 \
$(MAGIC_FRAGMENT_DIR)/icc \
$(MAGIC_FRAGMENT_DIR)/iff \
$(MAGIC_FRAGMENT_DIR)/images \
$(MAGIC_FRAGMENT_DIR)/inform \
$(MAGIC_FRAGMENT_DIR)/intel \
$(MAGIC_FRAGMENT_DIR)/interleaf \
$(MAGIC_FRAGMENT_DIR)/island \
$(MAGIC_FRAGMENT_DIR)/ispell \
$(MAGIC_FRAGMENT_DIR)/isz \
$(MAGIC_FRAGMENT_DIR)/java \
$(MAGIC_FRAGMENT_DIR)/javascript \
$(MAGIC_FRAGMENT_DIR)/jpeg \
$(MAGIC_FRAGMENT_DIR)/karma \
$(MAGIC_FRAGMENT_DIR)/kde \
$(MAGIC_FRAGMENT_DIR)/keepass \
$(MAGIC_FRAGMENT_DIR)/kerberos \
$(MAGIC_FRAGMENT_DIR)/kicad \
$(MAGIC_FRAGMENT_DIR)/kml \
$(MAGIC_FRAGMENT_DIR)/lecter \
$(MAGIC_FRAGMENT_DIR)/lex \
$(MAGIC_FRAGMENT_DIR)/lif \
$(MAGIC_FRAGMENT_DIR)/linux \
$(MAGIC_FRAGMENT_DIR)/lisp \
$(MAGIC_FRAGMENT_DIR)/llvm \
$(MAGIC_FRAGMENT_DIR)/lua \
$(MAGIC_FRAGMENT_DIR)/luks \
$(MAGIC_FRAGMENT_DIR)/m4 \
$(MAGIC_FRAGMENT_DIR)/mach \
$(MAGIC_FRAGMENT_DIR)/macintosh \
$(MAGIC_FRAGMENT_DIR)/macos \
$(MAGIC_FRAGMENT_DIR)/magic \
$(MAGIC_FRAGMENT_DIR)/mail.news \
$(MAGIC_FRAGMENT_DIR)/make \
$(MAGIC_FRAGMENT_DIR)/map \
$(MAGIC_FRAGMENT_DIR)/maple \
$(MAGIC_FRAGMENT_DIR)/marc21 \
$(MAGIC_FRAGMENT_DIR)/mathcad \
$(MAGIC_FRAGMENT_DIR)/mathematica \
$(MAGIC_FRAGMENT_DIR)/matroska \
$(MAGIC_FRAGMENT_DIR)/mcrypt \
$(MAGIC_FRAGMENT_DIR)/measure \
$(MAGIC_FRAGMENT_DIR)/mercurial \
$(MAGIC_FRAGMENT_DIR)/metastore \
$(MAGIC_FRAGMENT_DIR)/meteorological \
$(MAGIC_FRAGMENT_DIR)/microfocus \
$(MAGIC_FRAGMENT_DIR)/mime \
$(MAGIC_FRAGMENT_DIR)/mips \
$(MAGIC_FRAGMENT_DIR)/mirage \
$(MAGIC_FRAGMENT_DIR)/misctools \
$(MAGIC_FRAGMENT_DIR)/mkid \
$(MAGIC_FRAGMENT_DIR)/mlssa \
$(MAGIC_FRAGMENT_DIR)/mmdf \
$(MAGIC_FRAGMENT_DIR)/modem \
$(MAGIC_FRAGMENT_DIR)/modulefile \
$(MAGIC_FRAGMENT_DIR)/motorola \
$(MAGIC_FRAGMENT_DIR)/mozilla \
$(MAGIC_FRAGMENT_DIR)/msdos \
$(MAGIC_FRAGMENT_DIR)/msooxml \
$(MAGIC_FRAGMENT_DIR)/msvc \
$(MAGIC_FRAGMENT_DIR)/msx \
$(MAGIC_FRAGMENT_DIR)/mup \
$(MAGIC_FRAGMENT_DIR)/music \
$(MAGIC_FRAGMENT_DIR)/nasa \
$(MAGIC_FRAGMENT_DIR)/natinst \
$(MAGIC_FRAGMENT_DIR)/ncr \
$(MAGIC_FRAGMENT_DIR)/neko \
$(MAGIC_FRAGMENT_DIR)/netbsd \
$(MAGIC_FRAGMENT_DIR)/netscape \
$(MAGIC_FRAGMENT_DIR)/netware \
$(MAGIC_FRAGMENT_DIR)/news \
$(MAGIC_FRAGMENT_DIR)/nitpicker \
$(MAGIC_FRAGMENT_DIR)/numpy \
$(MAGIC_FRAGMENT_DIR)/oasis \
$(MAGIC_FRAGMENT_DIR)/ocaml \
$(MAGIC_FRAGMENT_DIR)/octave \
$(MAGIC_FRAGMENT_DIR)/ole2compounddocs \
$(MAGIC_FRAGMENT_DIR)/olf \
$(MAGIC_FRAGMENT_DIR)/openfst \
$(MAGIC_FRAGMENT_DIR)/opentimestamps \
$(MAGIC_FRAGMENT_DIR)/os2 \
$(MAGIC_FRAGMENT_DIR)/os400 \
$(MAGIC_FRAGMENT_DIR)/os9 \
$(MAGIC_FRAGMENT_DIR)/osf1 \
$(MAGIC_FRAGMENT_DIR)/palm \
$(MAGIC_FRAGMENT_DIR)/parix \
$(MAGIC_FRAGMENT_DIR)/parrot \
$(MAGIC_FRAGMENT_DIR)/pascal \
$(MAGIC_FRAGMENT_DIR)/pbf \
$(MAGIC_FRAGMENT_DIR)/pbm \
$(MAGIC_FRAGMENT_DIR)/pc88 \
$(MAGIC_FRAGMENT_DIR)/pc98 \
$(MAGIC_FRAGMENT_DIR)/pdf \
$(MAGIC_FRAGMENT_DIR)/pdp \
$(MAGIC_FRAGMENT_DIR)/perl \
$(MAGIC_FRAGMENT_DIR)/pgf \
$(MAGIC_FRAGMENT_DIR)/pgp \
$(MAGIC_FRAGMENT_DIR)/pkgadd \
$(MAGIC_FRAGMENT_DIR)/plan9 \
$(MAGIC_FRAGMENT_DIR)/plus5 \
$(MAGIC_FRAGMENT_DIR)/pmem \
$(MAGIC_FRAGMENT_DIR)/polyml \
$(MAGIC_FRAGMENT_DIR)/printer \
$(MAGIC_FRAGMENT_DIR)/project \
$(MAGIC_FRAGMENT_DIR)/psdbms \
$(MAGIC_FRAGMENT_DIR)/psl \
$(MAGIC_FRAGMENT_DIR)/pulsar \
$(MAGIC_FRAGMENT_DIR)/pwsafe \
$(MAGIC_FRAGMENT_DIR)/pyramid \
$(MAGIC_FRAGMENT_DIR)/python \
$(MAGIC_FRAGMENT_DIR)/qt \
$(MAGIC_FRAGMENT_DIR)/revision \
$(MAGIC_FRAGMENT_DIR)/riff \
$(MAGIC_FRAGMENT_DIR)/rpi \
$(MAGIC_FRAGMENT_DIR)/rpm \
$(MAGIC_FRAGMENT_DIR)/rpmsg \
$(MAGIC_FRAGMENT_DIR)/rtf \
$(MAGIC_FRAGMENT_DIR)/rst \
$(MAGIC_FRAGMENT_DIR)/ruby \
$(MAGIC_FRAGMENT_DIR)/sc \
$(MAGIC_FRAGMENT_DIR)/sccs \
$(MAGIC_FRAGMENT_DIR)/scientific \
$(MAGIC_FRAGMENT_DIR)/securitycerts \
$(MAGIC_FRAGMENT_DIR)/selinux \
$(MAGIC_FRAGMENT_DIR)/sendmail \
$(MAGIC_FRAGMENT_DIR)/sequent \
$(MAGIC_FRAGMENT_DIR)/sereal \
$(MAGIC_FRAGMENT_DIR)/sgi \
$(MAGIC_FRAGMENT_DIR)/sgml \
$(MAGIC_FRAGMENT_DIR)/sharc \
$(MAGIC_FRAGMENT_DIR)/sinclair \
$(MAGIC_FRAGMENT_DIR)/sisu \
$(MAGIC_FRAGMENT_DIR)/sketch \
$(MAGIC_FRAGMENT_DIR)/smalltalk \
$(MAGIC_FRAGMENT_DIR)/smile \
$(MAGIC_FRAGMENT_DIR)/sniffer \
$(MAGIC_FRAGMENT_DIR)/softquad \
$(MAGIC_FRAGMENT_DIR)/sosi \
$(MAGIC_FRAGMENT_DIR)/spec \
$(MAGIC_FRAGMENT_DIR)/spectrum \
$(MAGIC_FRAGMENT_DIR)/sql \
$(MAGIC_FRAGMENT_DIR)/ssh \
$(MAGIC_FRAGMENT_DIR)/ssl \
$(MAGIC_FRAGMENT_DIR)/sun \
$(MAGIC_FRAGMENT_DIR)/sylk \
$(MAGIC_FRAGMENT_DIR)/symbos \
$(MAGIC_FRAGMENT_DIR)/sysex \
$(MAGIC_FRAGMENT_DIR)/tcl \
$(MAGIC_FRAGMENT_DIR)/teapot \
$(MAGIC_FRAGMENT_DIR)/terminfo \
$(MAGIC_FRAGMENT_DIR)/tex \
$(MAGIC_FRAGMENT_DIR)/tgif \
$(MAGIC_FRAGMENT_DIR)/ti-8x \
$(MAGIC_FRAGMENT_DIR)/timezone \
$(MAGIC_FRAGMENT_DIR)/tplink \
$(MAGIC_FRAGMENT_DIR)/troff \
$(MAGIC_FRAGMENT_DIR)/tuxedo \
$(MAGIC_FRAGMENT_DIR)/typeset \
$(MAGIC_FRAGMENT_DIR)/unicode \
$(MAGIC_FRAGMENT_DIR)/unisig \
$(MAGIC_FRAGMENT_DIR)/unknown \
$(MAGIC_FRAGMENT_DIR)/usd \
$(MAGIC_FRAGMENT_DIR)/uterus \
$(MAGIC_FRAGMENT_DIR)/uuencode \
$(MAGIC_FRAGMENT_DIR)/vacuum-cleaner \
$(MAGIC_FRAGMENT_DIR)/varied.out \
$(MAGIC_FRAGMENT_DIR)/varied.script \
$(MAGIC_FRAGMENT_DIR)/vax \
$(MAGIC_FRAGMENT_DIR)/vicar \
$(MAGIC_FRAGMENT_DIR)/virtual \
$(MAGIC_FRAGMENT_DIR)/virtutech \
$(MAGIC_FRAGMENT_DIR)/visx \
$(MAGIC_FRAGMENT_DIR)/vms \
$(MAGIC_FRAGMENT_DIR)/vmware \
$(MAGIC_FRAGMENT_DIR)/vorbis \
$(MAGIC_FRAGMENT_DIR)/vxl \
$(MAGIC_FRAGMENT_DIR)/warc \
$(MAGIC_FRAGMENT_DIR)/web \
$(MAGIC_FRAGMENT_DIR)/weak \
$(MAGIC_FRAGMENT_DIR)/webassembly \
$(MAGIC_FRAGMENT_DIR)/windows \
$(MAGIC_FRAGMENT_DIR)/wireless \
$(MAGIC_FRAGMENT_DIR)/wordprocessors \
$(MAGIC_FRAGMENT_DIR)/wsdl \
$(MAGIC_FRAGMENT_DIR)/x68000 \
$(MAGIC_FRAGMENT_DIR)/xdelta \
$(MAGIC_FRAGMENT_DIR)/xenix \
$(MAGIC_FRAGMENT_DIR)/xilinx \
$(MAGIC_FRAGMENT_DIR)/xo65 \
$(MAGIC_FRAGMENT_DIR)/xwindows \
$(MAGIC_FRAGMENT_DIR)/yara \
$(MAGIC_FRAGMENT_DIR)/zfs \
$(MAGIC_FRAGMENT_DIR)/zilog \
$(MAGIC_FRAGMENT_DIR)/zip \
$(MAGIC_FRAGMENT_DIR)/zyxel

MAGIC = magic.mgc
CLEANFILES = ${MAGIC} $(MAGIC_FRAGMENT_DIR)/Localstuff
@IS_CROSS_COMPILE_FALSE@FILE_COMPILE = $(top_builddir)/src/file${EXEEXT}

# FIXME: Build file natively as well so that it can be used to compile
# the target's magic file; for now we bail if the local version does not match
@IS_CROSS_COMPILE_TRUE@FILE_COMPILE = file${EXEEXT}
@IS_CROSS_COMPILE_FALSE@FILE_COMPILE_DEP = $(FILE_COMPILE)
@IS_CROSS_COMPILE_TRUE@FILE_COMPILE_DEP = 
all: all-am

.SUFFIXES:
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --foreign magic/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --foreign magic/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
install-pkgdataDATA: $(pkgdata_DATA)
	@$(NORMAL_INSTALL)
	@list='$(pkgdata_DATA)'; test -n "$(pkgdatadir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(pkgdatadir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(pkgdatadir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(pkgdatadir)'"; \
	  $(INSTALL_DATA) $$files "$(DESTDIR)$(pkgdatadir)" || exit $$?; \
	done

uninstall-pkgdataDATA:
	@$(NORMAL_UNINSTALL)
	@list='$(pkgdata_DATA)'; test -n "$(pkgdatadir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(pkgdatadir)'; $(am__uninstall_files_from_dir)
tags TAGS:

ctags CTAGS:

cscope cscopelist:


distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-am
all-am: Makefile $(DATA)
installdirs:
	for dir in "$(DESTDIR)$(pkgdatadir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:
	-test -z "$(CLEANFILES)" || rm -f $(CLEANFILES)

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-generic clean-libtool mostlyclean-am

distclean: distclean-am
	-rm -f Makefile
distclean-am: clean-am distclean-generic

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am: install-pkgdataDATA

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am:

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-generic mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-pkgdataDATA

.MAKE: install-am install-strip

.PHONY: all all-am check check-am clean clean-generic clean-libtool \
	cscopelist-am ctags-am distclean distclean-generic \
	distclean-libtool distdir dvi dvi-am html html-am info info-am \
	install install-am install-data install-data-am install-dvi \
	install-dvi-am install-exec install-exec-am install-html \
	install-html-am install-info install-info-am install-man \
	install-pdf install-pdf-am install-pkgdataDATA install-ps \
	install-ps-am install-strip installcheck installcheck-am \
	installdirs maintainer-clean maintainer-clean-generic \
	mostlyclean mostlyclean-generic mostlyclean-libtool pdf pdf-am \
	ps ps-am tags-am uninstall uninstall-am uninstall-pkgdataDATA

.PRECIOUS: Makefile


${MAGIC}: $(EXTRA_DIST) $(FILE_COMPILE_DEP)
	@rm -fr magic
	@mkdir magic && cp -p $(EXTRA_DIST) magic
	@(if expr "${FILE_COMPILE}" : '.*/.*' > /dev/null; then \
	    echo "Using ${FILE_COMPILE} to generate ${MAGIC}" > /dev/null; \
	  else \
	    v=$$(${FILE_COMPILE} --version | sed -e s/file-// -e q); \
	    if [ "$$v" != "${PACKAGE_VERSION}" ]; then \
		echo "Cannot use the installed version of file ($$v) to"; \
		echo "cross-compile file ${PACKAGE_VERSION}"; \
		echo "Please install file ${PACKAGE_VERSION} locally first"; \
		exit 1; \
	    fi; \
	  fi)
	$(FILE_COMPILE) -C -m magic
	@rm -fr magic

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:

ARG builder_version
FROM hb.gs.lan/dev_52/th_common_lib:${builder_version} AS builder

ARG release_version
ARG product_name

ENV THE_ROOT=/opt/TH_ENGINE \
    THE_SDK=/opt/TH_ENGINE/sdk/ \
    RTE_SDK=/usr/local/src/dpdk-stable-18.02.2/ \
    RTE_TARGET=x86_64-native-linuxapp-gcc

COPY ./ /opt/TH_ENGINE/

RUN cd ${THE_ROOT}/scripts && bash create_local_install_bin.sh -p ${product_name} -v ${release_version}




FROM hb.gs.lan/dev_52/th_common_lib:${builder_version}

ENV THE_ROOT=/opt/TH_ENGINE \
    THE_SDK=/opt/TH_ENGINE/sdk/ \
    RTE_SDK=/usr/local/src/dpdk-stable-18.02.2/ \
    RTE_TARGET=x86_64-native-linuxapp-gcc

COPY --from=builder ${THE_ROOT}/scripts/th_release_file_local_*.tar.gz /install/

ENV THE_ROOT=/opt/GeekSec/th/ \
    THE_SDK=/opt/GeekSec/th/sdk/ \
    PATH=/opt/GeekSec/th/bin/:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/root/bin \
    LD_LIBRARY_PATH=/opt/GeekSec/th/bin/:/opt/GeekSec/th/sdk/lib/ \
    THE_CONFPUB_PATH=/opt/GeekSec/th/bin/conf_pub/ \
    THE_DB_PATH=/opt/GeekSec/th/bin/db/ \
    THE_CONF_PATH=/opt/GeekSec/th/bin/conf/0/ \
    THE_ID=0

RUN cd /install/ && \
    tar zxvf th_release_file_local_*.tar.gz && \
    ./th_release_file_local_*/th_install_local_*.bin && \
    cd / && cd / && rm -rf /install/

CMD [ "/opt/GeekSec/th/bin/thd" ]




# # docker rm -f tmp_product_analysis
# # docker create -it --name tmp_product_analysis hb.gs.lan/dev_52/product_analysis:xxxxxxxx bash
# # mkdir -p /opt/GeekSec/task/thd_offline/
# # /usr/bin/rm -rf /opt/GeekSec/task/thd_offline/conf/
# # docker cp tmp_product_analysis:/opt/GeekSec/th/bin/conf/0/ /opt/GeekSec/task/thd_offline/conf/
# # docker rm -f tmp_product_analysis

# TEST_TASKID=9876
# TEST_BATCHID=543210

# mkdir -p /taskpcap/${TEST_TASKID}/${TEST_BATCHID}/
# if [ ! -f "/taskpcap/${TEST_TASKID}/${TEST_BATCHID}/26218840.pcap" ]
# then
#     /usr/bin/cp -f /home/<USER>/pcap/169pcap/26218840.pcap /taskpcap/${TEST_TASKID}/${TEST_BATCHID}/
# fi

# /usr/bin/rm -rf /conf/${TEST_TASKID}/${TEST_BATCHID}/
# mkdir -p /conf/${TEST_TASKID}/${TEST_BATCHID}/
# /usr/bin/cp -rf /opt/GeekSec/task/thd_offline/conf/* /conf/${TEST_TASKID}/${TEST_BATCHID}/
# echo '{"task_id":'${TEST_TASKID}',"batch_id":'${TEST_BATCHID}'}' > /conf/${TEST_TASKID}/${TEST_BATCHID}/task_info.json
# find /taskpcap/${TEST_TASKID}/${TEST_BATCHID}/ -type f -name '*.pcap' | sort > /conf/${TEST_TASKID}/${TEST_BATCHID}/file_index.txt
# cat /dev/null > /conf/${TEST_TASKID}/${TEST_BATCHID}/task_pcap_read.conf

# /usr/bin/rm -rf /data/${TEST_TASKID}/${TEST_BATCHID}/
# mkdir -p /data/${TEST_TASKID}/${TEST_BATCHID}/
# /usr/bin/rm -rf /files/${TEST_TASKID}/${TEST_BATCHID}/
# mkdir -p /files/${TEST_TASKID}/${TEST_BATCHID}/


# docker rm -f product_analysis_${TEST_TASKID}_${TEST_BATCHID}
# docker run -d -it --privileged --name product_analysis_${TEST_TASKID}_${TEST_BATCHID} \
#     -v /usr/sbin/dmidecode:/usr/sbin/dmidecode \
#     -v /dev/mem:/dev/mem \
#     -v /etc/.serialnumber:/etc/.serialnumber \
#     -v /conf/${TEST_TASKID}/${TEST_BATCHID}:/opt/GeekSec/th/bin/conf/0 \
#     -v /data/${TEST_TASKID}/${TEST_BATCHID}:/data/${TEST_TASKID}/${TEST_BATCHID} \
#     -v /files/${TEST_TASKID}/${TEST_BATCHID}:/files/${TEST_TASKID}/${TEST_BATCHID} \
#     -v /taskpcap/${TEST_TASKID}/${TEST_BATCHID}:/taskpcap/${TEST_TASKID}/${TEST_BATCHID} \
#     hb.gs.lan/dev_52/product_analysis:xxxxxxxx

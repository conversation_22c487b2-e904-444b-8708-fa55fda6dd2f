# 安装部署
    编译安装包
        python Deploy/make_install.py
    使用安装包
        解压 PrivateRule_Install_*.tar.gz
        cd PrivateRule_Install
        python Deploy/install.py
    检查版本
        cat /opt/GeekSec/th/bin/JsonRule/BasicRule/UserRule/PrivateRule/version.txt


# 规则说明
    规则ID   Black White Tag_Text   Tag_Remakr Target Actual_Name
    5088	0	0	Short Cipher	短Cipher	session	DetectCipher
    5089	0	0	Short Pkt	短包长	session	136
    5090	0	0	Medium Pkt	中包长	session	276
    5091	0	0	Long Pkt	长包长	session	Hive
    5092	0	0	Medium URL	中Url	session	APT32
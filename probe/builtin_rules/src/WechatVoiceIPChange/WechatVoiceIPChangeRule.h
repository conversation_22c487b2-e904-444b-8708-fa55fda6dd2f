

//g++ ./WechatRule.cpp -fPIC -shared -o WechatRule.so
//nm -D ./WechatRule.so

#include "BasicDefine.h"

extern "C"
{
void* Init(int IN_CulRuleID,int IN_MaxConnectNum,void *IN_pParam);
void Quit(void* hHandle);
int ThreadBegin(void* hHandle,int ThreadID);
int ThreadEnd(void* hHandle,int ThreadID);
//IN_ParamType:1，单包命中规则响应；2，会话结束时响应；3，会话中包命中规则响应
int JudgeV2(void* hHandle,unsigned int  IN_Thread,unsigned int IN_APPID,DWORD IN_ParamType,void*IN_Param);
}


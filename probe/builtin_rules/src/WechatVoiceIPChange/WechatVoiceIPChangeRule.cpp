#include <unistd.h>
#include <pthread.h>
#include <stdio.h>
#include <stdlib.h>
#include <iostream>
#include <fstream>
#include <vector>
#include <map>
#include <arpa/inet.h>
#include "WechatVoiceIPChangeRule.h"

using namespace std;

// g++ -std=c++11 ./WechatRule.cpp -fPIC -shared -o WechatRule.so
// nm -D ./WechatRule.so

#define PRINTBUFLEN 65536
#define MAX_THREAD_NUM 64
#define HEARTBEATS_IN_THREE_MINUTES 630

//time_t stats_time[2];

typedef struct VoiceFlag
{
    bool ipchanged = false;
    pthread_rwlock_t rw_lock;
} VoiceFlag;

typedef struct IpChangedMsg
{
    int handled_thread; // 接收到ip_changed消息的线程数
    pthread_spinlock_t spin_lock;
} IpChangedMsg;

typedef struct ThreadSession
{
    unsigned long long ConnectKeyID;
    c_ip first_src_ip; //
    c_ip first_dst_ip; //
    int pkts_num;      //
    bool wechat_voice; //标识会话是否是微信语音会话
    bool first_ip_set; //标识当前会话是否设置了首IP
} ThreadSession;

typedef struct WechatRuleHandle
{
    int RuleID;
    int MaxCon;    // 每个线程处理的最大会话数
    pthread_t tid; // 数据分析线程的tid
    ThreadSession *pSession[MAX_THREAD_NUM];
    int ThreadSessionNum[MAX_THREAD_NUM];
    pthread_spinlock_t spin_lock[MAX_THREAD_NUM];
    char *pBuf[MAX_THREAD_NUM];
} WechatRuleHandle;

static int vo_worker_thread_num = 0;
static VoiceFlag g_voice_flag;
static IpChangedMsg g_voice_ip;

static bool is_wechat_voice_payload(uint8_t *data, uint16_t data_len)
{
    uint64_t wechat_voice_key_value = 0x000000001197;
    if (memcmp(data, &wechat_voice_key_value, 6) == 0)
    {
        return true;
    }
    else
    {
        return false;
    }
}

static bool is_wechat_voice_ipchanged(void *hHandle)
{
    WechatRuleHandle *p_handle = (WechatRuleHandle *)hHandle;
    map<uint32_t, uint32_t> ip_hash_map;
    uint32_t src_ip_hash;
    uint32_t dst_ip_hash;
    bool ip_changed = false;

    for (int i = 0; i < vo_worker_thread_num; i++)
    {
        if(ip_changed){
            break;
        }
        pthread_spin_lock(&p_handle->spin_lock[i]);
        for (int j = 0; j < p_handle->ThreadSessionNum[i]; j++)
        {
            if (p_handle->pSession[i][j].wechat_voice)// 微信语音会话
            {
                //printf("pSession[%d][%d] is wechat voice session\n", i, j);
                src_ip_hash = p_handle->pSession[i][j].first_src_ip.get_hash();
                dst_ip_hash = p_handle->pSession[i][j].first_dst_ip.get_hash();
                auto it = ip_hash_map.find(src_ip_hash);
                if (it != ip_hash_map.end())
                { // 源IP相同
                    if (it->second != dst_ip_hash)
                    { // 目的IP不同
                        char sz_ip[48];
                        memset(sz_ip,0x0,48);
                        inet_ntop(AF_INET, (void *)&it->second, sz_ip, 16);
                        cout << "wechat voice ip changed" << endl;
                        cout << "src: " << p_handle->pSession[i][j].first_src_ip.ip_str() << " dst1: " << string(sz_ip) << " dst2: " << p_handle->pSession[i][j].first_dst_ip.ip_str() << endl;
                        ip_changed = true;
                    }
                }
                else
                {
                    ip_hash_map.insert(make_pair(src_ip_hash, dst_ip_hash));
                }
            }
        }
        pthread_spin_unlock(&p_handle->spin_lock[i]);
    }
    return ip_changed;
}

static bool is_wechat_server_port(uint16_t port){
    if (port == 8000 || port == 8080 || port == 16285){
        return true;
    } else {
        return false;
    }
}

static void clear_wechat_voice_stats(void *hHandle){
    WechatRuleHandle *p_handle = (WechatRuleHandle *)hHandle;
    for (int i = 0; i < vo_worker_thread_num; i++)
    {
        pthread_spin_lock(&p_handle->spin_lock[i]);
        memset(p_handle->pSession[i], 0, sizeof(ThreadSession) * p_handle->MaxCon);
        p_handle->ThreadSessionNum[i] = 0;
        pthread_spin_unlock(&p_handle->spin_lock[i]);
    }
}

static void set_wechat_session_firstip(void *hHandle, int ThreadID, uint32_t ConnectID, uint32_t src_ip, uint32_t dst_ip){
    WechatRuleHandle *p_handle = (WechatRuleHandle *)hHandle;
    pthread_spin_lock(&p_handle->spin_lock[ThreadID - 1]);
    p_handle->pSession[ThreadID - 1][ConnectID].wechat_voice = true;
    p_handle->pSession[ThreadID - 1][ConnectID].first_ip_set = true;
    p_handle->pSession[ThreadID - 1][ConnectID].first_src_ip.SetIPv4(src_ip);
    p_handle->pSession[ThreadID - 1][ConnectID].first_dst_ip.SetIPv4(dst_ip);
    pthread_spin_unlock(&p_handle->spin_lock[ThreadID - 1]);
}

static void ipchange_msg_handle_inc(void)
{
    pthread_spin_lock(&g_voice_ip.spin_lock);
    g_voice_ip.handled_thread++;
    pthread_spin_unlock(&g_voice_ip.spin_lock);
}

static void ipchange_msg_handle_clear(void)
{
    pthread_spin_lock(&g_voice_ip.spin_lock);
    g_voice_ip.handled_thread = 0;
    pthread_spin_unlock(&g_voice_ip.spin_lock);
}

static void get_ipchange_msg_handled(int *handled){
    pthread_spin_lock(&g_voice_ip.spin_lock);
    *handled = g_voice_ip.handled_thread;
    pthread_spin_unlock(&g_voice_ip.spin_lock);
}

static void set_voice_flag(bool flag)
{ // thread safe
    pthread_rwlock_wrlock(&g_voice_flag.rw_lock);
    g_voice_flag.ipchanged = flag;
    pthread_rwlock_unlock(&g_voice_flag.rw_lock);
}

static void get_voice_flag(bool *flag)
{ // thread safe
    pthread_rwlock_rdlock(&g_voice_flag.rw_lock);
    *flag = g_voice_flag.ipchanged;
    pthread_rwlock_unlock(&g_voice_flag.rw_lock);
}

static void *stats_analyse_thread(void *arg)
{

    WechatRuleHandle *p_handle = (WechatRuleHandle *)arg;
    int handled;
    bool flag;

    while (1)
    {
        sleep(180);
        if (is_wechat_voice_ipchanged(p_handle))
        {
            set_voice_flag(true);
            //cout << "Wechat voice ip changed!" << endl;
            while (1)
            { // 等待有处理线程收到ip changed的信息并触发规则
                get_ipchange_msg_handled(&handled);
                get_voice_flag(&flag);
                //printf("ipchanged: %d, msg_handled: %d\n", flag, handled);
                if (handled > 0)
                {
                    break; 
                }
                //printf("waiting...\n");
                usleep(100);
            }
            clear_wechat_voice_stats(p_handle);
            ipchange_msg_handle_clear();//清除3分钟内的统计信息
            set_voice_flag(false); //
            //printf("reset wechat voice stats!!\n");
            //printf("ipchanged: %d, msg_handled: %d\n", flag, handled);
        }
        //printf("stats_analyse_thread running... %llu\n", time(NULL));
    }
}

void *Init(int IN_CulRuleID, int IN_MaxConnectNum, void *IN_pParam)
{
    STR_INIT_PARAM *pParam = (STR_INIT_PARAM *)IN_pParam;

    cout << "Rule: " << IN_CulRuleID << "; ----------Init----------" << endl;

    if (DYNAMIC_LIB_VER != pParam->Ver)
    {
        cout << "WechatRule Init Fail, Ver Not Match :" << pParam->Ver << endl;
        return NULL;
    }
    WechatRuleHandle *p_handle = new WechatRuleHandle;
    memset(p_handle, 0, sizeof(WechatRuleHandle));

    p_handle->RuleID = IN_CulRuleID;
    p_handle->MaxCon = IN_MaxConnectNum;

    ifstream fin(string(pParam->ConfigDir) + "/WechatRuleConfig.txt", ios::binary | ios::ate);
    if (fin.is_open())
    {
        auto size = fin.tellg();
        std::string str(size, '\0'); // construct string to stream size
        fin.seekg(0);
        fin.read(&str[0], size);
        cout << str << endl;
        cout << "WechatRule Read Config Success!" << endl;
    }
    else
    {
        cout << "WechatRule Read Config Fail!" << endl;
    }

    pthread_spin_init(&g_voice_ip.spin_lock, PTHREAD_PROCESS_PRIVATE);
    pthread_rwlock_init(&g_voice_flag.rw_lock, NULL);
    for (int i = 0; i < MAX_THREAD_NUM; ++i)
    {
        pthread_spin_init(&p_handle->spin_lock[i], PTHREAD_PROCESS_PRIVATE);
    }

    //stats_time[0] = time(NULL);
    //stats_time[1] = time(NULL);

    if (pthread_create(&p_handle->tid, NULL, stats_analyse_thread, p_handle) != 0)
    {
        std::cerr << "Error creating thread" << std::endl;
        return NULL;
    }

    if (pthread_setname_np(p_handle->tid, "wechat_vo_analy"))
    {
        std::cerr << "Error pthread_getname_np thread" << std::endl;
        return NULL;
    }

    return (void *)p_handle;
}
void Quit(void *hHandle)
{
    WechatRuleHandle *p_handle = (WechatRuleHandle *)hHandle;
    printf("Rule: %d; ----------Quit----------\n", p_handle->RuleID);

    pthread_cancel(p_handle->tid);

    for (int i = 0; i < MAX_THREAD_NUM; i++)
    {
        if (p_handle->pSession[i])
        {
            delete[] (p_handle->pSession[i]);
            p_handle->pSession[i] = NULL;
        }
        if (p_handle->pBuf[i])
        {
            delete[] (p_handle->pBuf[i]);
            p_handle->pBuf[i] = NULL;
        }
        pthread_spin_destroy(&p_handle->spin_lock[i]);
    }
    pthread_rwlock_destroy(&g_voice_flag.rw_lock);
    pthread_spin_destroy(&g_voice_ip.spin_lock);

    delete p_handle;
}

int ThreadBegin(void *hHandle, int ThreadID)
{
    WechatRuleHandle *p_handle = (WechatRuleHandle *)hHandle;
    printf("Rule: %d; ----------ThreadBegin:%d----------\n", p_handle->RuleID, ThreadID);
    p_handle->pSession[ThreadID - 1] = new ThreadSession[p_handle->MaxCon];
    memset(p_handle->pSession[ThreadID - 1], 0, sizeof(ThreadSession) * p_handle->MaxCon);
    p_handle->pBuf[ThreadID - 1] = new char[PRINTBUFLEN];
    memset(p_handle->pBuf[ThreadID - 1], 0, sizeof(char) * PRINTBUFLEN);
    vo_worker_thread_num = max(vo_worker_thread_num, ThreadID);
    return 0;
}
int ThreadEnd(void *hHandle, int ThreadID)
{
    WechatRuleHandle *p_handle = (WechatRuleHandle *)hHandle;
    printf("Rule: %d; ----------ThreadEnd:%d----------\n", p_handle->RuleID, ThreadID);
    if (p_handle->pSession[ThreadID - 1])
    {
        delete[] (p_handle->pSession[ThreadID - 1]);
        p_handle->pSession[ThreadID - 1] = NULL;
    }
    if (p_handle->pBuf[ThreadID - 1])
    {
        delete[] (p_handle->pBuf[ThreadID - 1]);
        p_handle->pBuf[ThreadID - 1] = NULL;
    }

    return 0;
}

int JudgeV2(void *hHandle, unsigned int ThreadID, unsigned int IN_APPID, DWORD IN_ParamType, void *IN_Param)
{
    WechatRuleHandle *p_handle = (WechatRuleHandle *)hHandle;
    STR_PARAM *param = (STR_PARAM *)IN_Param;
    bool ip_changed = false;
    uint32_t sip, dip;
    uint16_t sport, dport;

    //printf("JudgeV2 ver: %u ----- RuleID: %d, ThreadID: %d, IN_APPID: %d, IN_ParamType: %u, Packet: %p, Session: %p ----\n", param->SessionVer, p_handle->RuleID, ThreadID, IN_APPID, IN_ParamType, param->pPacket, param->pSession);

    STR_PACKETINFOR_MUDULE_CONNECT_V2 *pPacketInfor = param->pPacket;
    STR_CONNECTINFORV4_BASIC *pSession = param->pSession;

    //usleep(200);//减小锁竞争
    get_voice_flag(&ip_changed);//数据分析线程更新该值

    if(ip_changed){
        //printf("JudgeV2  ThreadID: %d get voice ip_changed msg\n", ThreadID);
        if (pPacketInfor)
        {
            // printf("Rule: %d; ----------JudgeV2 pkt: ProID: %d, Direction: %d----------\n", p_handle->RuleID, pPacketInfor->ProID, pPacketInfor->Directory);

            ///////////////////////////////?????????????
            STR_PROTOCOLSTACK *pStack = &pPacketInfor->Stack;
            STR_PROTOCOLINFOR *pPayload = NULL;
            p_handle->pSession[ThreadID - 1][pSession->ConnectID].pkts_num++;
            if (pStack->ProtocolNum > 0)
            {
                pPayload = &pStack->pProtocol[pStack->ProtocolNum - 1];
            }
            if (pPayload)
            {
                uint8_t *pstart = (uint8_t *)(pStack->pStart + pPayload->Offset);
                unsigned int length = 0;
                if (pStack->pEnd > (pStack->pStart + pPayload->Offset))
                {
                    length = pStack->pEnd - (pStack->pStart + pPayload->Offset);
                }
                if (!(pSession->pIP[0].IsIPv6()) && !(pSession->pIP[1].IsIPv6()))
                { // ֻ只判断IPv4
                    if (is_wechat_voice_payload(pstart, length))//下一个语音包触发规则
                    {
                        ipchange_msg_handle_inc();
                        return 1;
                    }
                }
            }
        }
        return 0; //非语音包不触发规则
    } 

    if (pSession)
    {
        // printf("JudgeV2 ver: %u ---- RuleID: %d, ProtoSign: %u, ConnectID: %u, ConnectKeyID: %u, Server: %d ----\n", param->SessionVer, p_handle->RuleID, pSession->ProtoSign, pSession->ConnectID, pSession->ConnectKeyID, pSession->Server);
        if (pSession->ConnectKeyID != p_handle->pSession[ThreadID - 1][pSession->ConnectID].ConnectKeyID)
        {
            memset(&p_handle->pSession[ThreadID - 1][pSession->ConnectID], 0, sizeof(ThreadSession));
            p_handle->pSession[ThreadID - 1][pSession->ConnectID].ConnectKeyID = pSession->ConnectKeyID;
            p_handle->ThreadSessionNum[ThreadID - 1]++;
            //printf("JudgeV2 ver: %u, new session! RuleID: %d, ThreadID: %u, ConnectID :%u, ConnectKeyID: %llu ----\n", param->SessionVer, p_handle->RuleID, ThreadID, pSession->ConnectID, pSession->ConnectKeyID);
        }
    }

    if (pPacketInfor)
    {
        // printf("Rule: %d; ----------JudgeV2 pkt: ProID: %d, Direction: %d----------\n", p_handle->RuleID, pPacketInfor->ProID, pPacketInfor->Directory);

        ///////////////////////////////?????????????
        STR_PROTOCOLSTACK *pStack = &pPacketInfor->Stack;
        STR_PROTOCOLINFOR *pPayload = NULL;
        p_handle->pSession[ThreadID - 1][pSession->ConnectID].pkts_num++;
        if (pStack->ProtocolNum > 0)
        {
            pPayload = &pStack->pProtocol[pStack->ProtocolNum - 1];
        }
        if (pPayload)
        {
            uint8_t *pstart = (uint8_t *)(pStack->pStart + pPayload->Offset);
            unsigned int length = 0;
            if (pStack->pEnd > (pStack->pStart + pPayload->Offset))
            {
                length = pStack->pEnd - (pStack->pStart + pPayload->Offset);
            }
            if (!(pSession->pIP[0].IsIPv6()) && !(pSession->pIP[1].IsIPv6()))
            { // 只判断IPv4
                if (is_wechat_voice_payload(pstart, length))
                {
                    if (!(p_handle->pSession[ThreadID - 1][pSession->ConnectID].first_ip_set))//当前微信语音会话的首IP还未设置
                    {
                        //printf("new wechat voice session!\n");
                        sip = *(uint32_t *)(pStack->pStart + 14 + 12);
                        dip = *(uint32_t *)(pStack->pStart + 14 + 16);
                        sport = *(uint32_t *)(pStack->pStart + 14 + 20);
                        dport = *(uint32_t *)(pStack->pStart + 14 + 22);
                        if (is_wechat_server_port(ntohs(sport))){                      
                            set_wechat_session_firstip(hHandle, ThreadID, pSession->ConnectID, dip, sip);
                        } else if(is_wechat_server_port(ntohs(dport))){
                            set_wechat_session_firstip(hHandle, ThreadID, pSession->ConnectID, sip, dip);
                        } else {
                            return 0;
                        }
                    }
                }
            }
        }
        ///////////////////////////////?????????????--End
    }

    return 0;
}


#include <stdint.h>
#include <stdlib.h>
#include <stdio.h>
#include <arpa/inet.h>
#include <string.h>
#include "Pkt276.h"
#include <commit_tools.h>


#define ID_KEY_HASH_SIZE	20	// Size of SHA-1 hash
#define START_PAD		8
#define CRC_DATA_LENGTH		84
#define PAD1			8
#define PAD2			8
#define IS_DEBUG           0



typedef struct RuleHandle
{
    int RuleID;
    int MaxCon;
}RuleHandle;

void* Init(int IN_CulRuleID,int IN_MaxConnectNum,void *IN_pParam)
{
    STR_INIT_PARAM *pParam = (STR_INIT_PARAM *)IN_pParam;
    printf("Rule: %d; ----------Init----------\n", IN_CulRuleID);

    if(DYNAMIC_LIB_VER != pParam->Ver)
    {
        printf("Pkt276 Init Fail, Ver Not Match :%u\n", pParam->Ver);
        return NULL;
    }
    
    RuleHandle *p_handle = new RuleHandle;
    memset(p_handle, 0, sizeof(RuleHandle));
    
    p_handle->RuleID = IN_CulRuleID;
    p_handle->MaxCon = IN_MaxConnectNum;
    
    return (void*)p_handle;
}
void Quit(void* hHandle)
{
    RuleHandle *p_handle = (RuleHandle *)hHandle;
    printf("Rule: %d; ----------Quit----------\n",p_handle->RuleID);
    delete p_handle;
}

int ThreadBegin(void* hHandle,int ThreadID)
{
    RuleHandle *p_handle = (RuleHandle *)hHandle;
    printf("Rule: %d; ----------ThreadBegin:%d----------\n",p_handle->RuleID, ThreadID);
    return 0;
}
int ThreadEnd(void* hHandle,int ThreadID)
{
    RuleHandle *p_handle = (RuleHandle *)hHandle;
    printf("Rule: %d; ----------ThreadEnd:%d----------\n",p_handle->RuleID, ThreadID);
    return 0;
}

uint32_t payload2uint(uint8_t *data,int start,int end) {
	unsigned int result = 0;
	for (int i=start;i<end;i++){
		result = result << 8 + data[i];
	}
	return result;

}

int quickDetectPayload(uint8_t *data,uint16_t data_size){
	uint32_t last_2_num = payload2uint(data,data_size-2,data_size);
	uint32_t last_10_num = payload2uint(data,data_size-10,data_size-6);
	uint32_t last_14_num = payload2uint(data,data_size-14,data_size-10);

	uint32_t v6 = 0xDC5AE45F*last_10_num + 0x697D2211*last_14_num;
    uint32_t v7 =  270 - 128 + 2 * ((__uint16_t)(v6 * v6) >> 10) + 1;
    uint32_t v8 = (unsigned int)(v6 * v6) >> 16;
	uint32_t value_of_v7 = payload2uint(data,v7-1,v7+1);
	// printf("pkt276 %d, %d, %d\n",value_of_v7,v8,last_2_num);

	if (value_of_v7+v8 - last_2_num ==0) {
		// if (IS_DEBUG)
		// printf("detected pkt276\n");
		return 1;
	}

    return 0;
}


//返回 0 匹配失败； 1 匹配成功
int JudgeV2(void* hHandle,unsigned int  ThreadID,unsigned int IN_APPID,DWORD IN_ParamType,void*IN_Param)
{
	STR_PARAM *param = (STR_PARAM*)IN_Param;
	STR_PACKETINFOR_MUDULE_CONNECT_V2 *pPacketInfor = param->pPacket;
	STR_CONNECTINFORV4_BASIC *pSession = param->pSession;
	if(pPacketInfor)
	{
        STR_PROTOCOLSTACK* pStack=&pPacketInfor->Stack;
        STR_PROTOCOLINFOR *pPayload = NULL;
        if(pStack->ProtocolNum > 0)
        {
            uint8_t idx = pStack->ProtocolNum ;
            while(idx > 0)
            {
                uint32_t protoid = pStack->pProtocol[idx].Protocol;
                uint32_t protoid2 = pStack->pProtocol[idx].Protocol;
                if(16 == protoid) //icmp
                {
                    pPayload = &pStack->pProtocol[idx];
                    break;
                }
                idx --;
            }
        }
        if(pPayload){
            unsigned int length = 0;
            if(pStack->pEnd > (pStack->pStart + pPayload->Offset))
            {
                length = pStack->pEnd - (pStack->pStart + pPayload->Offset);
            }
			if (length!=276+8) {
				return 0;
			}
			uint8_t *pstart = (uint8_t *)(pStack->pStart + 8);			

			uint32_t icmp_type =  payload2uint(pstart,0,1);
			if (icmp_type!=0) {
				return 0;
			}
            return quickDetectPayload(pstart,length);
        }
	}
	return 0;
}


#include <stdio.h>
#include <stdlib.h>
#include <iostream>
#include <fstream>
#include <sstream>
#include <string>
using namespace std;
#include "MineLib.h"
#include "json/reader.h"
#include "json/value.h"
#include "json/writer.h"
#include "DataStructure/Func_Character.h"
#include "DataStructure/KeywordsMatch.h"
#include "JSON_checker.h"

#define MAX_PKT_NUM 10


typedef struct SessionHandle_
{
    uint8_t has_tag;
    uint8_t pkt_num;
}_MySessionHandle;

typedef struct SessionHandle
{
    uint64_t LastSsid;
    _MySessionHandle Value;
}MySessionHandle;

typedef struct ThreadHandle
{
    MySessionHandle *pSession;
    unsigned char *pFilter;
}MyThreadHandle;

typedef struct RuleHandle
{
    int RuleID;
    int MaxCon;
    unsigned int KeywordsNum;
    MyThreadHandle *pThread[64];
    CKeywordsMatch *pMatcher;
}MyRuleHandle;

static bool parase_key(const char *IN_pString, unsigned char *OUT_pKey, DWORD &OUT_pKeyLen)
{
    OUT_pKeyLen = 0;
    DWORD CulLen = 0;

    while (*IN_pString != 0)
    {
        //16
        if ((*IN_pString == '\\') && (g_pUPTOLOWTABLE[*(IN_pString + 1)] == 'x'))
        {
            IN_pString += 2;

            CulLen = 0;
            if (AscIIToHex((unsigned char*)IN_pString, 2, &OUT_pKey[OUT_pKeyLen], CulLen) == false)
            {
                return false;
            }

            IN_pString += 2;
            OUT_pKeyLen += 1;
        }
        else if (*IN_pString != 0)
        {
            OUT_pKey[OUT_pKeyLen++] = *IN_pString;
            IN_pString++;
        }
    }

    return true;
}

void* Init(int IN_CulRuleID,int IN_MaxConnectNum,void *IN_pParam)
{

    STR_INIT_PARAM *pParam = (STR_INIT_PARAM *)IN_pParam;
    printf("Rule: %d; ----------Init----------\n", IN_CulRuleID);

    if(DYNAMIC_LIB_VER != pParam->Ver)
    {
        printf("MineLib Init Fail, Ver Not Match :%u\n", pParam->Ver);
        return NULL;
    }
    
    MyRuleHandle *p_handle = new MyRuleHandle;
    memset(p_handle, 0, sizeof(MyRuleHandle));
    
    p_handle->RuleID = IN_CulRuleID;
    p_handle->MaxCon = IN_MaxConnectNum;

    ifstream ifs(string(pParam->ConfigDir)+"/mining_keywords.txt", ios::binary);
    if(ifs.is_open())
    {
        Json::Reader reader;
        Json::Value value;
        if(reader.parse(ifs, value))
        {
            if(value.isObject() && value.isMember("method"))
            {
                if(value["method"].isArray() && value["method"].size() > 0)
                {
                    p_handle->KeywordsNum = value["method"].size();
                    p_handle->pMatcher = new CKeywordsMatch();
                    p_handle->pMatcher->Init(p_handle->KeywordsNum, false);
                    STR_KEYWORDRULE rule;
                    DWORD KeywordLen;

                    for(unsigned int i = 0; i < p_handle->KeywordsNum; i ++)
                    {
                        memset(&rule, 0, sizeof(rule));
                        string keyword = value["method"][i].asString();
                        parase_key(keyword.c_str(), &rule.pKeyword[0], KeywordLen);
                        rule.KeywordLen = (unsigned char)KeywordLen;
                        p_handle->pMatcher->JudgeAndAddKeyword(rule);
                    }
                    return (void*)p_handle;
                }
            }
        }
    }
    delete p_handle;
    return NULL;
}
void Quit(void* hHandle)
{
    MyRuleHandle *p_handle = (MyRuleHandle *)hHandle;
    if(p_handle)
    {
        time_t TsStart = time(NULL);
        time_t TsNow = TsStart;
        for(;;)
        {
            int bThreadNotEnd = 0;
            for(unsigned int i = 0; i < 64; i ++)
            {
                if(p_handle->pThread[i])
                {
                    bThreadNotEnd = 1;
                    break;
                }
            }
            if(bThreadNotEnd)
            {
                usleep(1000);
                TsNow = time(NULL);
                if((TsNow < TsStart) || (TsStart + 60 < TsNow))
                {
                    break;
                }
            }
            else
            {
                break;
            }
        }
        for(int i = 0; i < 64; i ++)
        {
            if(p_handle->pThread[i])
            {
                ThreadEnd(hHandle, i+1);
            }
        }
        if(p_handle->pMatcher)
        {
            p_handle->pMatcher->Quit();
            delete p_handle->pMatcher;
            p_handle->pMatcher = NULL;
        }
        delete p_handle;
        p_handle = NULL;
    }
}

int ThreadBegin(void* hHandle,int ThreadID)
{
    MyRuleHandle *p_handle = (MyRuleHandle *)hHandle;
    if(hHandle)
    {
        p_handle->pThread[ThreadID-1] = new MyThreadHandle;
        memset(p_handle->pThread[ThreadID-1], 0, sizeof(MyThreadHandle));
        p_handle->pThread[ThreadID-1]->pSession = new MySessionHandle[p_handle->MaxCon];
        memset(p_handle->pThread[ThreadID-1]->pSession, 0, sizeof(MySessionHandle) * p_handle->MaxCon);
        p_handle->pThread[ThreadID-1]->pFilter = new unsigned char[p_handle->KeywordsNum + 1];
        memset(p_handle->pThread[ThreadID-1]->pFilter, 0, sizeof(unsigned char) * (p_handle->KeywordsNum + 1));
    }
    return 0;
}
int ThreadEnd(void* hHandle,int ThreadID)
{
    MyRuleHandle *p_handle = (MyRuleHandle *)hHandle;
    if(hHandle)
    {
        if(p_handle->pThread[ThreadID-1])
        {
            if(p_handle->pThread[ThreadID-1]->pSession)
            {
                delete []p_handle->pThread[ThreadID-1]->pSession;
            }
            p_handle->pThread[ThreadID-1]->pSession = NULL;
            if(p_handle->pThread[ThreadID-1]->pFilter)
            {
                delete []p_handle->pThread[ThreadID-1]->pFilter;
            }
            p_handle->pThread[ThreadID-1]->pFilter = NULL;
            delete p_handle->pThread[ThreadID-1];
            p_handle->pThread[ThreadID-1] = NULL;
        }
    }
    
    return 0;
}

static int be_mining(MyRuleHandle *p_handle, MyThreadHandle *pThreadHandle, unsigned char *pStart, unsigned char *pEnd)
{
    Json::Reader reader;
    Json::Value value;
    DWORD Rule = 0;
    DWORD *pRule = &Rule;
    DWORD RuleNum = 0;
    DWORD RuleSize = 1;
    if (false != reader.parse((const char *)pStart, (const char *)pEnd, value))
    {
        if(value.isObject())
        {
            if(value.isMember("method"))
            {
                Json::Value tmp = value["method"];
                if(tmp.isString())
                {
                    string method = tmp.asString();
                    p_handle->pMatcher->Match((unsigned char *)method.c_str(), (unsigned char *)method.c_str()+method.length(), pThreadHandle->pFilter, pRule, RuleSize, RuleNum);
                    if(RuleNum)
                    {
                        pThreadHandle->pFilter[Rule] = 0;
                        return 1;
                    }
                }
            }
        }
    }
    return 0;
}

static int be_json(MyRuleHandle *p_handle, MyThreadHandle *pThreadHandle, unsigned char *pStart, unsigned char *pEnd)
{
    unsigned char *pTemp = NULL;

    if((pStart + 4) >= pEnd)
    {
        return 0;
    }
    if(pStart[0] != '{')
    {
        return 0;
    }
    pTemp = pEnd - 1;
    while(pTemp > pStart)
    {
        if(pTemp[0]==0x0d || pTemp[0]==0x0a)
        {
            pTemp --;
        }
        else
        {
            break;
        }
    }
    if(pTemp[0] != '}')
    {
        return 0;
    }

    JSON_checker jc = new_JSON_checker(20);
    for (pTemp = pStart; pTemp < pEnd; pTemp ++)
    {
        int next_char = pTemp[0];
        if(!JSON_checker_char(jc, next_char))
        {
            return 0;
        }
    }
    if(!JSON_checker_done(jc))
    {
        return 0;
    }
    return be_mining(p_handle, pThreadHandle, pStart, pEnd);
}

//���� 0 ƥ��ʧ�ܣ� 1 ƥ��ɹ�
int JudgeV2(void* hHandle,unsigned int  ThreadID,unsigned int IN_APPID,DWORD IN_ParamType,void*IN_Param)
{
    MyRuleHandle *p_handle = (MyRuleHandle *)hHandle;
    STR_PARAM *param = (STR_PARAM*)IN_Param;
    
    STR_PACKETINFOR_MUDULE_CONNECT_V2 *pPacketInfor = param->pPacket;
    STR_CONNECTINFORV4_BASIC *pSession = param->pSession;
    MyThreadHandle *pThreadHandle;
    MySessionHandle *pSessionHandle;
    if(NULL == hHandle)
    {
        return 0;
    }

    if(pSession)
    {
        pThreadHandle = p_handle->pThread[ThreadID-1];
        pSessionHandle = pThreadHandle->pSession+pSession->ConnectID;

        if(pSessionHandle->LastSsid != pSession->ConnectKeyID)
        {
            memset(&pSessionHandle->Value, 0, sizeof(_MySessionHandle));
            pSessionHandle->LastSsid = pSession->ConnectKeyID;
        }
        if(pSessionHandle->Value.has_tag || pSessionHandle->Value.pkt_num >= MAX_PKT_NUM)
        {
            return 0;
        }
        if(pPacketInfor)
        {
            STR_PROTOCOLSTACK* pStack=&pPacketInfor->Stack;
            if(0 == pStack->ProtocolNum || (0 != pPacketInfor->ProID && 10637 != pPacketInfor->ProID))
            {
                return 0;
            }
            STR_PROTOCOLINFOR *pPro = &(pStack->pProtocol[pStack->ProtocolNum-1]);
            if(pPro->Protocol == 2 && pPro->Len > 4)
            {
                if(be_json(p_handle, pThreadHandle, pStack->pStart+pPro->Offset, pStack->pStart+pPro->Offset+pPro->Len))
                {
                    pSessionHandle->Value.has_tag = 1;
                    return 1;
                }
                else
                {
                    pSessionHandle->Value.pkt_num ++;
                }
            }
        }
    }
    return 0;
}



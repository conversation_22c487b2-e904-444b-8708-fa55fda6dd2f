
#include <stdint.h>
#include <stdlib.h>
#include <stdio.h>
#include <arpa/inet.h>
#include <string.h>
#include "HIVE_Rule.h"
#include <commit_tools.h>


#define ID_KEY_HASH_SIZE	20	// Size of SHA-1 hash
#define START_PAD		8
#define CRC_DATA_LENGTH		84
#define PAD1			8
#define PAD2			8
#define IS_DEBUG           0



typedef struct RuleHandle
{
    int RuleID;
    int MaxCon;
}RuleHandle;

void* Init(int IN_CulRuleID,int IN_MaxConnectNum,void *IN_pParam)
{
    STR_INIT_PARAM *pParam = (STR_INIT_PARAM *)IN_pParam;
    printf("Rule: %d; ----------Init----------\n", IN_CulRuleID);

    if(DYNAMIC_LIB_VER != pParam->Ver)
    {
        printf("HIVE_Rule Init Fail, Ver Not Match :%u\n", pParam->Ver);
        return NULL;
    }
    
    RuleHandle *p_handle = new RuleHandle;
    memset(p_handle, 0, sizeof(RuleHandle));
    
    p_handle->RuleID = IN_CulRuleID;
    p_handle->MaxCon = IN_MaxConnectNum;
    
    return (void*)p_handle;
}
void Quit(void* hHandle)
{
    RuleHandle *p_handle = (RuleHandle *)hHandle;
    printf("Rule: %d; ----------Quit----------\n",p_handle->RuleID);
    delete p_handle;
}

int ThreadBegin(void* hHandle,int ThreadID)
{
    RuleHandle *p_handle = (RuleHandle *)hHandle;
    printf("Rule: %d; ----------ThreadBegin:%d----------\n",p_handle->RuleID, ThreadID);
    return 0;
}
int ThreadEnd(void* hHandle,int ThreadID)
{
    RuleHandle *p_handle = (RuleHandle *)hHandle;
    printf("Rule: %d; ----------ThreadEnd:%d----------\n",p_handle->RuleID, ThreadID);
    return 0;
}

uint32_t getStrCrc32(string buff)
{
	char* cbuff = (char*)buff.c_str();
	int	str_len = buff.length(); 
	return (uint32_t)crc32((unsigned char*)(cbuff), str_len);

}

typedef struct __attribute__((packed))
{
	uint8_t		seed;				// Obfuscation seed used for triggers other than raw TCP/UDP.
	in_addr_t	callback_addr;			// the callback for the triggered application, always in net order
	uint16_t	callback_port;			// callback port, passed to TBOT, always in net order
	unsigned char	idKey_hash[ID_KEY_HASH_SIZE];	// ID Key hash
	uint16_t	crc;				// CRC of this payload
} Payload;

typedef struct __attribute__((packed))
{
	int		delay;				// Trigger delay
	in_addr_t	callback_addr;			// the callback for the triggered application, always in net order
	uint16_t	callback_port;			// callback port, passed to TBOT, always in net order
	uint16_t	trigger_port;			// for raw triggers, the TCP or UDP port
	uint8_t		idKey_hash[ID_KEY_HASH_SIZE];	// SHA-1 of ID key
} TriggerInfo;



uint16_t tiny_crc16(const uint8_t * msg, uint32_t sz){  
    uint32_t index;
    uint16_t crc;
    uint8_t val, t;
    /* 
    * CRC16 Lookup tables (High and Low Byte) for 4 bits per iteration. 
    */
    unsigned short CRC16_LookupHigh[16] = {
    0x00, 0x10, 0x20, 0x30, 0x40, 0x50, 0x60, 0x70,
    0x81, 0x91, 0xA1, 0xB1, 0xC1, 0xD1, 0xE1, 0xF1
    };
    unsigned short CRC16_LookupLow[16] = {
    0x00, 0x21, 0x42, 0x63, 0x84, 0xA5, 0xC6, 0xE7,
    0x08, 0x29, 0x4A, 0x6B, 0x8C, 0xAD, 0xCE, 0xEF
    };
  
   /*
   * CRC16 "Register". This is implemented as two 8bit values
   */
    unsigned char CRC16_High, CRC16_Low;
    // Initialise the CRC to 0xFFFF for the CCITT specification
    CRC16_High = 0xFF;
    CRC16_Low = 0xFF;

    for (index =0; index < sz; index++){
        val = msg[index] >> 4;
        // Step one, extract the Most significant 4 bits of the CRC register
        t = CRC16_High >> 4;
        // XOR in the Message Data into the extracted bits
        t = t ^ val;
        // Shift the CRC Register left 4 bits
        CRC16_High = (CRC16_High << 4) | (CRC16_Low >> 4);
        CRC16_Low = CRC16_Low << 4;        
        // Do the table lookups and XOR the result into the CRC Tables
        CRC16_High = CRC16_High ^ CRC16_LookupHigh[t];
        CRC16_Low = CRC16_Low ^ CRC16_LookupLow[t];
        val = msg[index] & 0x0F;
        // Step one, extract the Most significant 4 bits of the CRC register
        t = CRC16_High >> 4;
        // XOR in the Message Data into the extracted bits
        t = t ^ val;
        // Shift the CRC Register left 4 bits
        CRC16_High = (CRC16_High << 4) | (CRC16_Low >> 4);
        CRC16_Low = CRC16_Low << 4;
        // Do the table lookups and XOR the result into the CRC Tables
        CRC16_High = CRC16_High ^ CRC16_LookupHigh[t];
        CRC16_Low = CRC16_Low ^ CRC16_LookupLow[t];
    }
    crc = CRC16_High;
    crc = crc << 8;
    crc = crc ^ CRC16_Low;
    return crc;
}



int checkRaw(uint8_t *data, uint16_t pkt_len, Payload *p){
    uint16_t crc = 0;
    uint16_t uint16buf = 0;
    uint16_t netcrc;
    uint16_t validator;
    uint8_t *fieldPtr;			// Packet field pointer

	uint8_t *payloadKeyIndex;
	uint8_t *payloadIndex;
	int i;				// Loop counter
	uint8_t *pp;

	pp = (uint8_t *)p;
    crc = tiny_crc16 ((unsigned char *) ((char *) data + START_PAD), CRC_DATA_LENGTH);
    if (IS_DEBUG) printf("tiny crc is %x\n",crc);
    fieldPtr = data + START_PAD + CRC_DATA_LENGTH + (crc % 200);
	if (fieldPtr == 0 || (fieldPtr > (data + pkt_len)))		// Make sure it's within boundsD
		return 0;
    
    memcpy(&uint16buf, fieldPtr, sizeof(uint16_t));
    netcrc = ntohs(uint16buf);
    if (IS_DEBUG) printf ("CRC is 0x%0x into data, NET CRC = 0x%2.2x\n", (unsigned int)(fieldPtr - data), netcrc);
    if (crc != netcrc) {
		return 0;
	}
    fieldPtr += sizeof(crc);
	memcpy(&uint16buf, fieldPtr, sizeof(uint16_t));
	validator = ntohs(uint16buf);

    if (IS_DEBUG) printf ("Validator location: 0x%0x, Trigger validator = %x\n", (unsigned int)(fieldPtr - data), validator);
	if ( (validator % 127) != 0) {
		return 0;			// Check 2 failure: integer not divisible by 127
	}

    fieldPtr += sizeof(validator) + PAD1;		// Update field pointer to point to trigger payload.
	payloadIndex = fieldPtr;
	payloadKeyIndex = (uint8_t *)(data + START_PAD + (crc % (CRC_DATA_LENGTH - sizeof(Payload))));	// Compute the start of the payload key
	if (IS_DEBUG) printf("Encoded Payload offset\t0x%0x, Payload key offset: 0x%0x\t\n", (unsigned int)(fieldPtr - data), (unsigned int)(payloadKeyIndex - (uint8_t *)data));

    if (IS_DEBUG) printf("Payload follows:\n");
    for (i = 0; i < (int)sizeof(Payload); i++) {
		uint8_t trigger;
		trigger = payloadKeyIndex[i] ^ payloadIndex[i];			// XOR the trigger payload with the key
		if (IS_DEBUG) printf ("\tByte[%2.2d]: encoded payload = 0x%2.2x,  payloadKey= 0x%2.2x, decoded payload = 0x%2.2x\n", i, payloadIndex[i], payloadKeyIndex[i], trigger);
		memcpy((void *)(pp + i), (void *)&trigger, sizeof(uint8_t));
	}

    return 1;

}


int checkPayloadTriggerInfo(Payload *p, TriggerInfo *ti){
    if (p == NULL || ti == NULL) {
		return 0;
	}
    uint16_t crc;
	crc = ntohs(p->crc);
    if (IS_DEBUG) printf ("crc %x\n",p->crc);
    uint16_t crc_now;
    p->crc = 0;
    crc_now = tiny_crc16 ((const uint8_t *) p, sizeof (Payload));
    if (IS_DEBUG) printf("%d\n",(int)sizeof(Payload));
    if (IS_DEBUG) printf ("crc now %x\n",crc_now);
    if (tiny_crc16 ((const uint8_t *) p, sizeof (Payload)) != crc ) {
		if (IS_DEBUG) printf (">>> CRC failed, payload corrupted.\n\n");	
		return 0;
	}
	ti->callback_addr = p->callback_addr;
	ti->callback_port = ntohs(p->callback_port);
	memcpy (ti->idKey_hash, p->idKey_hash, ID_KEY_HASH_SIZE);
	if (IS_DEBUG) printf ("delay %x\n",ti->delay);
    if (IS_DEBUG) printf ("ti-callback_IP %x\n",ti->callback_addr);
    if (IS_DEBUG) printf ("ti-callback_port %x\n",ti->callback_port);
    if (IS_DEBUG) for(int i =0;i<sizeof(ti->idKey_hash);i++)
	{
		printf ("ti-key %d %x\n",i,ti->idKey_hash[i]);
	}

    return 1;

}

int detectPayload(uint8_t *data,uint16_t data_size){
    TriggerInfo tParams;
    Payload p;
    int raw_result; 
    raw_result = checkRaw(data,data_size,&p);
    memset(&tParams, 0, sizeof( TriggerInfo ));
    if (raw_result > 0) {
        return checkPayloadTriggerInfo(&p,&tParams);
    }
    return 0;
}


//返回 0 匹配失败； 1 匹配成功
int JudgeV2(void* hHandle,unsigned int  ThreadID,unsigned int IN_APPID,DWORD IN_ParamType,void*IN_Param)
{
	STR_PARAM *param = (STR_PARAM*)IN_Param;
	STR_PACKETINFOR_MUDULE_CONNECT_V2 *pPacketInfor = param->pPacket;
	STR_CONNECTINFORV4_BASIC *pSession = param->pSession;
	if(pPacketInfor)
	{
        STR_PROTOCOLSTACK* pStack=&pPacketInfor->Stack;
        STR_PROTOCOLINFOR *pPayload = NULL;
        if(pStack->ProtocolNum > 0)
        {
            uint8_t idx = pStack->ProtocolNum - 1;
            while(idx > 0)
            {
                uint32_t protoid = pStack->pProtocol[idx - 1].Protocol;
                if(14 == protoid) //tcp
                {
                    pPayload = &pStack->pProtocol[idx];
                    break;
                }
                idx --;
            }
        }
        if(pPayload){
            uint8_t *pstart = (uint8_t *)(pStack->pStart + pPayload->Offset);
            unsigned int length = 0;
            if(pStack->pEnd > (pStack->pStart + pPayload->Offset))
            {
                length = pStack->pEnd - (pStack->pStart + pPayload->Offset);
            }
            return detectPayload(pstart,length);
        }
	}
	return 0;
}


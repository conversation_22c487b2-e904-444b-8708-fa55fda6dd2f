#include <stdio.h>
#include <stdlib.h>
#include <algorithm>
#include "APT32_DenesRAT.h"
#include <commit_tools.h>

typedef struct RuleHandle
{
    int RuleID;
    int MaxCon;
}RuleHandle;

void* Init(int IN_CulRuleID,int IN_MaxConnectNum,void *IN_pParam)
{
    STR_INIT_PARAM *pParam = (STR_INIT_PARAM *)IN_pParam;
    printf("Rule: %d; ----------Init----------\n", IN_CulRuleID);

    if(DYNAMIC_LIB_VER != pParam->Ver)
    {
        printf("APT32_DenesRAT Init Fail, Ver Not Match :%u\n", pParam->Ver);
        return NULL;
    }
    
    RuleHandle *p_handle = new RuleHandle;
    memset(p_handle, 0, sizeof(RuleHandle));
    
    p_handle->RuleID = IN_CulRuleID;
    p_handle->MaxCon = IN_MaxConnectNum;
    
    return (void*)p_handle;
}
void Quit(void* hHandle)
{
    RuleHandle *p_handle = (RuleHandle *)hHandle;
    printf("Rule: %d; ----------Quit----------\n",p_handle->RuleID);
    delete p_handle;
}

int ThreadBegin(void* hHandle,int ThreadID)
{
    RuleHandle *p_handle = (RuleHandle *)hHandle;
    printf("Rule: %d; ----------ThreadBegin:%d----------\n",p_handle->RuleID, ThreadID);
    return 0;
}
int ThreadEnd(void* hHandle,int ThreadID)
{
    RuleHandle *p_handle = (RuleHandle *)hHandle;
    printf("Rule: %d; ----------ThreadEnd:%d----------\n",p_handle->RuleID, ThreadID);
    return 0;
}

uint32_t getStrCrc32(string buff)
{
	char* cbuff = (char*)buff.c_str();
	int	str_len = buff.length(); 
	return (uint32_t)crc32((unsigned char*)(cbuff), str_len);

}

string strip(const string &str,char ch=' ')
{
	//除去str两端的ch字符
	int i = 0;
	while (str[i] == ch)// 头部ch字符个数是 i
		i++;
	int j = str.size() - 1;
	while (str[j] == ch ) //
		j--;		
	return str.substr(i, j+1 -i );
}

int isLeftAPT32Url(string url)
{
    int block_cnt = count(url.begin(),url.end(),'/');
	if (block_cnt > 3 or block_cnt <2) {
		return 0;
	}
    int buff_pos = url.find("-",1);
    int num1_pos = url.find('/',1);
    int num2_pos = url.find('-',num1_pos+1);

	if (buff_pos==string::npos || num1_pos==string::npos || num2_pos==string::npos) {
		return 0;
	}

	uint32_t num1,num2;
	
	string num1_str = url.substr(0,num1_pos);
	string num2_str = url.substr(num1_pos+1,num2_pos);
	num2_str = strip(strip(num2_str,'-'),'/');
	num1_str = strip(strip(num1_str,'-'),'/');
	string buff = url.substr(buff_pos+1);
	try {
		num1 = stoul(num1_str);
		num2 = stoul(num2_str);

	}catch (...){
		return  0;
	}
	
	uint32_t checkI = getStrCrc32(buff);
	uint32_t n2 = (checkI >> 16) + (checkI & 0xffff) * 2;
	uint32_t n1 = (num2 ^ 1) & 0xf;

	if (n1 == num1 && n2 == num2){
		printf("detected left url %s\n",url.c_str());
		return 1;
	}
	return 0;
}

int isRightAPTURL(string url)
{
	int buff_pos = url.rfind("-");
	uint32_t num_int;
	if (buff_pos==string::npos) {
		return 0;
	}
	int num1_pos = url.find('/');
	int num2_pos = url.rfind('/');
	if (num1_pos!=num2_pos) {
		return 0;
	}
	string num_str = url.substr(buff_pos+1);
	string buff = url.substr(0,buff_pos);
	try {
		num_int = stoul(num_str);
	}catch (...){
		return  0;
	}
	uint32_t checkI = getStrCrc32(buff);
	checkI = (checkI >> 24) | ((checkI & 0xff) <<8);


	if (checkI == num_int){
		printf("detected right url %s\n",url.c_str());
		return 1;
	}
	printf("%d,%d\n",checkI,num_int);
	return 0;
}

int isAPT32Http(char * httpbuf , DWORD PayloadLen)
{
	string all_url = "";
	if(strncmp(httpbuf ,"GET ",4)== 0 || strncmp(httpbuf,"POST ",5)== 0 || strncmp(httpbuf ,"PUT ",4)== 0 || strncmp(httpbuf ,"OPTIONS ",8)== 0){
		char * p = strstr(httpbuf,"\r\n");
		if(p!=NULL && p -httpbuf <= PayloadLen && p-httpbuf > 10){
			all_url = string(httpbuf, 0, p-httpbuf);
		}else{
			return 0;
		}
	}

	size_t pos = 0;
	pos = all_url.find(" ");
	if( pos != string::npos)
	{
		string url1(all_url.substr(pos+1));
		size_t pos1 = 0;
		pos1 = url1.find(" ");
		size_t pos2 = 0;
		pos2 = url1.find("?");
		if (pos2>0 && pos1>pos2){
			pos1 = pos2;
		}
		string url2(url1.substr(0, pos1));
		int url_len = url2.length();
		if (url2[url_len-1]=='/') {
			url2 = url2.substr(0,url_len-1);
		}
		// printf("touch url %s\n",url2.c_str());
		int pos = url2.find("HTTP/1.1");
		return isLeftAPT32Url(url2) || isRightAPTURL(url2);
	}
	return 0;
}

//返回 0 匹配失败； 1 匹配成功
int JudgeV2(void* hHandle,unsigned int  ThreadID,unsigned int IN_APPID,DWORD IN_ParamType,void*IN_Param)
{
	STR_PARAM *param = (STR_PARAM*)IN_Param;
	STR_PACKETINFOR_MUDULE_CONNECT_V2 *pPacketInfor = param->pPacket;
	STR_CONNECTINFORV4_BASIC *pSession = param->pSession;

	
	if(pPacketInfor)
	{
		//获取末层负载的头指针
		STR_PROTOCOLSTACK* pStack=&pPacketInfor->Stack;
		if( pStack->ProtocolNum!=0 )
		{
			int Count=pStack->ProtocolNum-1;
			char *httpbuf= (char *)(pStack->pStart + pStack->pProtocol[ Count ].Offset);
			unsigned char *pPayload=pStack->pStart + pStack->pProtocol[ Count ].Offset;

			DWORD PayloadLen=pStack->pProtocol[ Count ].Len;

			return isAPT32Http(httpbuf,PayloadLen);			
		}
	}
	return 0;
}



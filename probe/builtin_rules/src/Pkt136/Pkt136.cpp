
#include <stdint.h>
#include <stdlib.h>
#include <stdio.h>
#include <arpa/inet.h>
#include <string.h>
#include "Pkt136.h"
#include <commit_tools.h>


#define ID_KEY_HASH_SIZE	20	// Size of SHA-1 hash
#define START_PAD		8
#define CRC_DATA_LENGTH		84
#define PAD1			8
#define PAD2			8
#define IS_DEBUG           0



typedef struct RuleHandle
{
    int RuleID;
    int MaxCon;
}RuleHandle;

void* Init(int IN_CulRuleID,int IN_MaxConnectNum,void *IN_pParam)
{
    STR_INIT_PARAM *pParam = (STR_INIT_PARAM *)IN_pParam;
    printf("Rule: %d; ----------Init----------\n", IN_CulRuleID);

    if(DYNAMIC_LIB_VER != pParam->Ver)
    {
        printf("Pkt136 Init Fail, Ver Not Match :%u\n", pParam->Ver);
        return NULL;
    }
    
    RuleHandle *p_handle = new RuleHandle;
    memset(p_handle, 0, sizeof(RuleHandle));
    
    p_handle->RuleID = IN_CulRuleID;
    p_handle->MaxCon = IN_MaxConnectNum;
    
    return (void*)p_handle;
}
void Quit(void* hHandle)
{
    RuleHandle *p_handle = (RuleHandle *)hHandle;
    printf("Rule: %d; ----------Quit----------\n",p_handle->RuleID);
    delete p_handle;

}

int ThreadBegin(void* hHandle,int ThreadID)
{
    RuleHandle *p_handle = (RuleHandle *)hHandle;
    printf("Rule: %d; ----------ThreadBegin:%d----------\n",p_handle->RuleID, ThreadID);
    return 0;
}
int ThreadEnd(void* hHandle,int ThreadID)
{
    RuleHandle *p_handle = (RuleHandle *)hHandle;
    printf("Rule: %d; ----------ThreadEnd:%d----------\n",p_handle->RuleID, ThreadID);
    return 0;
}


int detectCalcShort(uint8_t * result_pcap)
{
	unsigned int a1 = result_pcap[0]<<8;
	unsigned int a2 = result_pcap[1];		
	int calcresult = a1 +a2;
	int result = calcresult ^ 0x9D6A;
	unsigned int b1 = result_pcap[134]<<8;
	unsigned int b2 = result_pcap[135];
	uint16_t pcapresult = b1+b2;
	printf("pcap result %x\n",pcapresult);
	printf("result %x\n",result);
	return (result-pcapresult)==0;
}

int detectPayload(uint8_t *data,uint16_t data_size){
    if (data_size==136 && detectCalcShort(data)){
        printf("detected pkt136\n");
        return 1;
    }
    return 0;
}


//返回 0 匹配失败； 1 匹配成功
int JudgeV2(void* hHandle,unsigned int  ThreadID,unsigned int IN_APPID,DWORD IN_ParamType,void*IN_Param)
{
	STR_PARAM *param = (STR_PARAM*)IN_Param;
	STR_PACKETINFOR_MUDULE_CONNECT_V2 *pPacketInfor = param->pPacket;
	STR_CONNECTINFORV4_BASIC *pSession = param->pSession;
	if(pPacketInfor)
	{
        STR_PROTOCOLSTACK* pStack=&pPacketInfor->Stack;
        STR_PROTOCOLINFOR *pPayload = NULL;
        if(pStack->ProtocolNum > 0)
        {
            uint8_t idx = pStack->ProtocolNum - 1;
            while(idx > 0)
            {
                uint32_t protoid = pStack->pProtocol[idx - 1].Protocol;
                if(14 == protoid) //tcp
                {
                    pPayload = &pStack->pProtocol[idx];
                    break;
                }
                idx --;
            }
        }
        if(pPayload){
            uint8_t *pstart = (uint8_t *)(pStack->pStart + pPayload->Offset);
            unsigned int length = 0;
            if(pStack->pEnd > (pStack->pStart + pPayload->Offset))
            {
                length = pStack->pEnd - (pStack->pStart + pPayload->Offset);
            }
            return detectPayload(pstart,length);
        }
	}
	return 0;
}


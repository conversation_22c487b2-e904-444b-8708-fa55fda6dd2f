//
#define DEFAULT_COAP_PORT 5683
#define BASE_TPNCP_PORT 2424
#define GEARMAN_PORT 4730
#include <./GeneralInclude/Define_CulEnvironment.h>
 
 
#define AMP_U_L2Cuap		0x0001
#define AMP_C_ACTIVITY_REPORT	0x0002
#define AMP_C_SECURITY_FRAME	0x0003
#define AMP_C_LINK_SUP_REQUEST	0x0004
#define AMP_C_LINK_SUP_REPLY	0x0005
#define ANCP_PORT 6068 /* The ANCP TCP port:draft-ietf-ancp-protocol-09.txt */
#define ANCP_MIN_HDR  4
#define ANCP_GSMP_ETHER_TYPE  0x880C
#define ANCP_RESULT_MASK     0xF0
#define ANCP_CODE_MASK       0x0FFF
#define ANCP_I_FLAG_MASK     0x80
#define ANCP_SUBMSG_MASK     0x7FFF
#define ANCP_MTYPE_ADJ       10
#define ANCP_MTYPE_PORT_MGMT 32
#define ANCP_MTYPE_PORT_UP   80
#define ANCP_MTYPE_PORT_DN   81
#define ANSI_TRANS_MSG_TYPE_BROADCAST   1
#define ANSI_683_FORWARD        0
#define ANSI_683_REVERSE        1
#define	ANSI_801_FORWARD	0
#define	ANSI_801_REVERSE	1
#define ANSI_FWD_MS_INFO_REC_DISPLAY            0x01
#define ANSI_FWD_MS_INFO_REC_CLD_PN             0x02
#define ANSI_FWD_MS_INFO_REC_CLG_PN             0x03
#define ANSI_FWD_MS_INFO_REC_CONN_N             0x04
#define ANSI_FWD_MS_INFO_REC_SIGNAL             0x05
#define ANSI_FWD_MS_INFO_REC_MW                 0x06
#define ANSI_FWD_MS_INFO_REC_SC                 0x07
#define ANSI_FWD_MS_INFO_REC_CLD_PSA            0x08
#define ANSI_FWD_MS_INFO_REC_CLG_PSA            0x09
#define ANSI_FWD_MS_INFO_REC_CONN_SA            0x0a
#define ANSI_FWD_MS_INFO_REC_RED_N              0x0b
#define ANSI_FWD_MS_INFO_REC_RED_SA             0x0c
#define ANSI_FWD_MS_INFO_REC_MP                 0x0d
#define ANSI_FWD_MS_INFO_REC_PA                 0x0e
#define ANSI_FWD_MS_INFO_REC_LC                 0x0f
#define ANSI_FWD_MS_INFO_REC_EDISPLAY           0x10
#define ANSI_FWD_MS_INFO_REC_NNSC               0x13
#define ANSI_FWD_MS_INFO_REC_MC_EDISPLAY        0x14
#define ANSI_FWD_MS_INFO_REC_CWI                0x15
#define ANSI_FWD_MS_INFO_REC_EMC_EDISPLAY       0x16
#define ANSI_FWD_MS_INFO_REC_ERTI               0xfe
#define ANSI_REV_MS_INFO_REC_KEYPAD_FAC         0x03
#define ANSI_REV_MS_INFO_REC_CLD_PN             0x04
#define ANSI_REV_MS_INFO_REC_CLG_PN             0x05
#define ANSI_REV_MS_INFO_REC_CALL_MODE          0x07
#define ANSI_REV_MS_INFO_REC_TERM_INFO          0x08
#define ANSI_REV_MS_INFO_REC_ROAM_INFO          0x09
#define ANSI_REV_MS_INFO_REC_SECUR_STS          0x0a
#define ANSI_REV_MS_INFO_REC_CONN_N             0x0b
#define ANSI_REV_MS_INFO_REC_IMSI               0x0c
#define ANSI_REV_MS_INFO_REC_ESN                0x0d
#define ANSI_REV_MS_INFO_REC_BAND_INFO          0x0e
#define ANSI_REV_MS_INFO_REC_POWER_INFO         0x0f
#define ANSI_REV_MS_INFO_REC_OP_MODE_INFO       0x10
#define ANSI_REV_MS_INFO_REC_SO_INFO            0x11
#define ANSI_REV_MS_INFO_REC_MO_INFO            0x12
#define ANSI_REV_MS_INFO_REC_SC_INFO            0x13
#define ANSI_REV_MS_INFO_REC_CLD_PSA            0x14
#define ANSI_REV_MS_INFO_REC_CLG_PSA            0x15
#define ANSI_REV_MS_INFO_REC_CONN_SA            0x16
#define ANSI_REV_MS_INFO_REC_PCI                0x17
#define ANSI_REV_MS_INFO_REC_IMSI_M             0x18
#define ANSI_REV_MS_INFO_REC_IMSI_T             0x19
#define ANSI_REV_MS_INFO_REC_Cuap_INFO           0x1a
#define ANSI_REV_MS_INFO_REC_CCC_INFO           0x1b
#define ANSI_REV_MS_INFO_REC_EMO_INFO           0x1c
#define ANSI_REV_MS_INFO_REC_GEO_Cuap            0x1e
#define ANSI_REV_MS_INFO_REC_BAND_SUB           0x1f
#define ANSI_REV_MS_INFO_REC_GECO               0x20
#define ANSI_REV_MS_INFO_REC_HOOK               0x21
#define ANSI_REV_MS_INFO_REC_QOS_PARAM          0x22
#define ANSI_REV_MS_INFO_REC_ENCRYPT_Cuap        0x23
#define ANSI_REV_MS_INFO_REC_SMI_Cuap            0x24
#define ANSI_REV_MS_INFO_REC_UIM_ID             0x25
#define ANSI_REV_MS_INFO_REC_ESN_ME             0x26
#define ANSI_REV_MS_INFO_REC_MEID               0x27
#define ANSI_REV_MS_INFO_REC_EKEYPAD_FAC        0x28
#define ANSI_REV_MS_INFO_REC_SYNC_ID            0x29
#define ANSI_REV_MS_INFO_REC_ERTI               0xfe
#define ANSI_A_IOS401_BSMAP_NUM_MSG (sizeof(ansi_a_ios401_bsmap_strings)/sizeof(ext_value_string_t))
#define ANSI_A_IOS501_BSMAP_NUM_MSG (sizeof(ansi_a_ios501_bsmap_strings)/sizeof(ext_value_string_t))
#define ANSI_A_IOS401_DTAP_NUM_MSG (sizeof(ansi_a_ios401_dtap_strings)/sizeof(ext_value_string_t))
#define ANSI_A_IOS501_DTAP_NUM_MSG (sizeof(ansi_a_ios501_dtap_strings)/sizeof(ext_value_string_t))
#define	ANSI_A_MAX_NUM_IOS_ELEM_1_STRINGS	255
#define ANSI_MAP_TID_ONLY 0
#define	ANSI_MAP_MAX_NUM_MESSAGE_TYPES	256
#define ANSI_TCuap_CTX_SIGNATURE 0x41544341  /* "ATCA" */
#define ANSI_X34 0
#define ANSI_ISUP_MESSAGE_TYPE_CIRCUIT_RES_ACK  0xE9
#define ANSI_ISUP_MESSAGE_TYPE_CIRCUIT_RES      0xEA
#define ANSI_ISUP_MESSAGE_TYPE_CCT_VAL_TEST_RSP 0xEB
#define ANSI_ISUP_MESSAGE_TYPE_CCT_VAL_TEST     0xEC
#define ANSI_ISUP_MESSAGE_TYPE_EXIT             0xED
#define ANSI_ISUP_PARAM_TYPE_OPER_SERV_INF   0xC2  /* 194 */
#define ANSI_ISUP_PARAM_TYPE_EGRESS          0xC3  /* 195 */
#define ANSI_ISUP_PARAM_TYPE_JURISDICTION    0xC4  /* 196 */
#define ANSI_ISUP_PARAM_TYPE_CARRIER_ID      0xC5  /* 197 */
#define ANSI_ISUP_PARAM_TYPE_BUSINESS_GRP    0xC6  /* 198 */
#define ANSI_ISUP_PARAM_TYPE_GENERIC_NAME    0xC7  /* 199*/
#define ANSI_ISUP_PARAM_TYPE_NOTIF_IND       0xE1  /* 225 */
#define ANSI_ISUP_PARAM_TYPE_CG_CHAR_IND      229
#define ANSI_ISUP_PARAM_TYPE_CVR_RESP_IND     230
#define ANSI_ISUP_PARAM_TYPE_OUT_TRK_GRP_NM   231
#define ANSI_ISUP_PARAM_TYPE_CI_NAME_IND      232
#define ANSI_ISUP_PARAM_CLLI_CODE             233
#define ANSI_ISUP_PARAM_ORIG_LINE_INF        0xEA  /* 234 */
#define ANSI_ISUP_PARAM_CHRG_NO              0xEB  /* 235 */
#define ANSI_ISUP_PARAM_SERV_CODE_IND        0xEC  /* 236 */
#define ANSI_ISUP_PARAM_SPEC_PROC_REQ        0xED  /* 237 */
#define ANSI_ISUP_PARAM_CARRIER_SEL_INF      0xEE  /* 238 */
#define ANSI_ISUP_PARAM_NET_TRANS            0xEF  /* 239 */
#define ANSI_ROUTING_LABEL_LENGTH (ANSI_PC_LENGTH + ANSI_PC_LENGTH + SLS_LENGTH)
#define ANSI_HEADER_LENGTH        (SIO_LENGTH + ANSI_ROUTING_LABEL_LENGTH)
#define ANSI_DPC_OFFSET           ROUTING_LABEL_OFFSET
#define ANSI_OPC_OFFSET           (ANSI_DPC_OFFSET + ANSI_PC_LENGTH)
#define ANSI_SLS_OFFSET           (ANSI_OPC_OFFSET + ANSI_PC_LENGTH)
#define ANSI_MTP_PAYLOAD_OFFSET   (SIO_OFFSET + ANSI_HEADER_LENGTH)
#define ANSI_PRIORITY_MASK         SPARE_MASK
#define ANSI_5BIT_SLS_MASK         0x1F
#define ANSI_8BIT_SLS_MASK         0xFF
#define ANSI_PC_LENGTH    3
#define ANSI_NCM_LENGTH   1
#define ANSI_NETWORK_OFFSET 2
#define ANSI_CLUSTER_OFFSET 1
#define ANSI_MEMBER_OFFSET 0
#define ANSI_PC_MASK      0xFFFFFF
#define ANSI_NETWORK_MASK 0xFF0000
#define ANSI_CLUSTER_MASK 0x00FF00
#define ANSI_MEMBER_MASK  0x0000FF
#define ANSI_PC_STRING_LENGTH 16
#define ANSI_COO_LENGTH    2
#define ANSI_COO_SLC_MASK  0x000f
#define ANSI_COO_FSN_MASK  0x07f0
#define ANSI_XCO_LENGTH    4
#define ANSI_XCO_SLC_MASK  0x0000000f
#define ANSI_XCO_FSN_MASK  0x0ffffff0
#define ANSI_CBD_LENGTH    2
#define ANSI_CBD_SLC_MASK  0x000f
#define ANSI_CBD_CBC_MASK  0x0ff0
#define ANSI_ECO_LENGTH   1
#define ANSI_ECO_SLC_MASK 0x0f
#define ANSI_TFC_STATUS_LENGTH       1
#define ANSI_TFC_STATUS_OFFSET       ANSI_PC_LENGTH
#define ANSI_TFC_STATUS_MASK         0x03
#define ANSI_MIM_LENGTH   1
#define ANSI_MIM_SLC_MASK 0x0f
#define ANSI_DLC_LENGTH    3
#define ANSI_DLC_SLC_MASK  0x0000f
#define ANSI_DLC_LINK_MASK 0x3fff0
#define ANSI_UPU_USER_OFFSET ANSI_PC_LENGTH
#define ANSI_TEST_SLC_MASK  0x000f
#define ANSI_NATIONAL_MASK              0x80
#define ANSI_PC_INDICATOR_MASK          0x02
#define ANSI_SSN_INDICATOR_MASK         0x01
#define ANSI_AI_GTI_TT_NP_ES    0x1
#define ANSI_ISNI_ROUTING_CONTROL_LENGTH 1
#define ANSI_ISNI_MI_MASK                0x01
#define ANSI_ISNI_IRI_MASK               0x06
#define ANSI_ISNI_RES_MASK               0x08
#define ANSI_ISNI_TI_MASK                0x10
#define ANSI_ISNI_TI_SHIFT               4
#define ANSI_ISNI_COUNTER_MASK           0xe0
#define ANSI_ISNI_NETSPEC_MASK           0x03
#define ANSI_ISNI_TYPE_0 0x0
#define ANSI_ISNI_TYPE_1 0x1
#define ANSI_SCCPMG_AFFECTED_PC_LENGTH 3
#define ANSI_SCCPMG_SMI_OFFSET (SCCPMG_AFFECTED_PC_OFFSET + ANSI_SCCPMG_AFFECTED_PC_LENGTH)
#define ANSI_TC_INVOKE_L	0xe9
#define ANSI_TC_RRL		0xea
#define ANSI_TC_RE		0xeb
#define ANSI_TC_REJECT		0xec
#define ANSI_TC_INVOKE_N	0xed
#define ANSI_TC_RRN		0xee
#define AOL_PORT 5190
#define AOL_P3_FRAME_START 0x5a
#define AOL_P3_FRAME_END   0x0d
#define AOL_P3_TYPE_DATA      0x20
#define AOL_P3_TYPE_SS        0x21
#define AOL_P3_TYPE_SSR       0x22
#define AOL_P3_TYPE_INIT      0x23
#define AOL_P3_TYPE_ACK       0x24
#define AOL_P3_TYPE_NAK       0x25
#define AOL_P3_TYPE_HEARTBEAT 0x26
#define AOL_PLATFORM_WINDOWS 0x03
#define AOL_PLATFORM_MAC     0x0c
#define ARCNET_PROTO_IP_1051	240
#define ARCNET_PROTO_ARP_1051	241
#define ARCNET_PROTO_IP_1201	212
#define ARCNET_PROTO_ARP_1201	213
#define ARCNET_PROTO_RARP_1201	214
#define ARCNET_PROTO_IPX	250
#define ARCNET_PROTO_NOVELL_EC	236
#define ARCNET_PROTO_IPv6	196	/* or so BSD's arcnet.h claims */
#define ARCNET_PROTO_ETHERNET	232
#define ARCNET_PROTO_DATAPOINT_BOOT	0
#define ARCNET_PROTO_DATAPOINT_MOUNT	1
#define ARCNET_PROTO_POWERLAN_BEACON	8
#define ARCNET_PROTO_POWERLAN_BEACON2	243
#define ARCNET_PROTO_LANSOFT	251
#define ARCNET_PROTO_APPLETALK	221
#define ARCNET_PROTO_BANYAN	247	/* Banyan VINES */
#define ARCNET_PROTO_DIAGNOSE	128	/* as per ANSI/ATA 878.1 */
#define ARCNET_PROTO_BACNET	205
#define ASAP_UDP_PORT  3863
#define ASAP_TCP_PORT  3863
#define ASAP_SCTP_PORT 3863
#define ASAP_PAYLOAD_PROTOCOL_ID                       11
#define ASTERIX_PORT        8600
#define AST_FRAME_DTMF_END  1       /* A DTMF end event, subclass is the digit */
#define AST_FRAME_VOICE     2       /* Voice data, subclass is AST_FORMAT_* */
#define AST_FRAME_VIDEO     3       /* Video frame, maybe?? :) */
#define AST_FRAME_CONTROL   4       /* A control frame, subclass is AST_CONTROL_* */
#define AST_FRAME_NULL      5       /* An empty, useless frame */
#define AST_FRAME_IAX       6       /* Inter Aterisk Exchange private frame type */
#define AST_FRAME_TEXT      7       /* Text messages */
#define AST_FRAME_IMAGE     8       /* Image Frames */
#define AST_FRAME_HTML      9       /* HTML Frames */
#define AST_FRAME_CNG      10       /* Confort Noise Generation */
#define AST_FRAME_MODEM    11       /* Modem-over-IP datastream */
#define AST_FRAME_DTMF_BEGIN 12     /* A DTMF begin event, subclass is the digit */
#define AST_CAUSE_UNALLOCATED				1
#define AST_CAUSE_NO_ROUTE_TRANSIT_NET			2
#define AST_CAUSE_NO_ROUTE_DESTINATION			3
#define AST_CAUSE_MISDIALLED_TRUNK_PREFIX		5
#define AST_CAUSE_CHANNEL_UNACCEPTABLE			6
#define AST_CAUSE_CALL_AWARDED_DELIVERED		7
#define AST_CAUSE_PRE_EMPTED				8
#define AST_CAUSE_NUMBER_PORTED_NOT_HERE		14
#define AST_CAUSE_NORMAL_CLEARING			16
#define AST_CAUSE_USER_BUSY				17
#define AST_CAUSE_NO_USER_RESPONSE			18
#define AST_CAUSE_NO_ANSWER				19
#define AST_CAUSE_SUBSCRIBER_ABSENT			20
#define AST_CAUSE_CALL_REJECTED				21
#define AST_CAUSE_NUMBER_CHANGED			22
#define AST_CAUSE_REDIRECTED_TO_NEW_DESTINATION	23
#define AST_CAUSE_ANSWERED_ELSEWHERE			26
#define AST_CAUSE_DESTINATION_OUT_OF_ORDER		27
#define AST_CAUSE_INVALID_NUMBER_FORMAT			28
#define AST_CAUSE_FACILITY_REJECTED			29
#define AST_CAUSE_RESPONSE_TO_STATUS_ENQUIRY		30
#define AST_CAUSE_NORMAL_UNSPECIFIED			31
#define AST_CAUSE_NORMAL_CIRCUIT_CONGESTION		34
#define AST_CAUSE_NETWORK_OUT_OF_ORDER			38
#define AST_CAUSE_NORMAL_TEMPORARY_FAILURE		41
#define AST_CAUSE_SWITCH_CONGESTION			42
#define AST_CAUSE_ACCESS_INFO_DISCARDED			43
#define AST_CAUSE_REQUESTED_CHAN_UNAVAIL		44
#define AST_CAUSE_FACILITY_NOT_SUBSCRIBED		50
#define AST_CAUSE_OUTGOING_CALL_BARRED			52
#define AST_CAUSE_INCOMING_CALL_BARRED			54
#define AST_CAUSE_BEARERCuapABILITY_NOTAUTH		57
#define AST_CAUSE_BEARERCuapABILITY_NOTAVAIL		58
#define AST_CAUSE_BEARERCuapABILITY_NOTIMPL		65
#define AST_CAUSE_CHAN_NOT_IMPLEMENTED			66
#define AST_CAUSE_FACILITY_NOT_IMPLEMENTED		69
#define AST_CAUSE_INVALID_CALL_REFERENCE		81
#define AST_CAUSE_INCOMPATIBLE_DESTINATION		88
#define AST_CAUSE_INVALID_MSG_UNSPECIFIED		95
#define AST_CAUSE_MANDATORY_IE_MISSING			96
#define AST_CAUSE_MESSAGE_TYPE_NONEXIST			97
#define AST_CAUSE_WRONG_MESSAGE				98
#define AST_CAUSE_IE_NONEXIST				99
#define AST_CAUSE_INVALID_IE_CONTENTS			100
#define AST_CAUSE_WRONG_CALL_STATE			101
#define AST_CAUSE_RECOVERY_ON_TIMER_EXPIRE		102
#define AST_CAUSE_MANDATORY_IE_LENGTH_ERROR		103
#define AST_CAUSE_PROTOCOL_ERROR			111
#define AST_CAUSE_INTERWORKING				127
#define AST_FORMAT_G723_1	(1 << 0)
#define AST_FORMAT_GSM		(1 << 1)
#define AST_FORMAT_ULAW		(1 << 2)
#define AST_FORMAT_ALAW		(1 << 3)
#define AST_FORMAT_G726_AAL2	(1 << 4)
#define AST_FORMAT_ADPCM	(1 << 5)
#define AST_FORMAT_SLINEAR	(1 << 6)
#define AST_FORMAT_LPC10	(1 << 7)
#define AST_FORMAT_G729A	(1 << 8)
#define AST_FORMAT_SPEEX	(1 << 9)
#define AST_FORMAT_ILBC		(1 << 10)
#define AST_FORMAT_G726		(1 << 11)
#define AST_FORMAT_G722		(1 << 12)
#define AST_FORMAT_SIREN7		(1 << 13)
#define AST_FORMAT_SIREN14		(1 << 14)
#define AST_FORMAT_SLINEAR16	(1 << 15)
#define AST_FORMAT_MAX_AUDIO	(1 << 15)
#define AST_FORMAT_JPEG		(1 << 16)
#define AST_FORMAT_PNG		(1 << 17)
#define AST_FORMAT_H261		(1 << 18)
#define AST_FORMAT_H263		(1 << 19)
#define AST_FORMAT_H263_PLUS		(1 << 20)
#define AST_FORMAT_H264		(1 << 21)
#define AST_FORMAT_MP4_VIDEO		(1 << 22)
#define AST_FORMAT_MAX_VIDEO	(1 << 24)
#define AST_FORMAT_G719		(1 << 32)
#define AST_FORMAT_SPEEX16		(1 << 33)
#define AX25_P_ROSE	0x01	/* ISO 8208 / CCITT X.25 PLP */
#define AX25_P_RFC1144C	0x06	/* Compressed TCP/IP packet. Van Jacobson RFC1144 */
#define AX25_P_RFC1144	0x07	/* Uncompressed TCP/IP packet. Van Jacobson RFC1144 */
#define AX25_P_SEGMENT	0x08	/* segmentation fragment */
#define AX25_P_TEXNET	0xC3	/* TEXNET datagram */
#define AX25_P_LCP	0xC4	/* Link Quality Protocol */
#define AX25_P_ATALK	0xCA	/* AppleTalk */
#define AX25_P_ATALKARP	0xCB	/* AppleTalk ARP */
#define AX25_P_IP	0xCC	/* ARPA Internet Protocol */
#define AX25_P_ARP	0xCD	/* ARPA Address Resolution Protocol */
#define AX25_P_FLEXNET 	0xCE	/* FlexNet */
#define AX25_P_NETROM 	0xCF	/* NET/ROM */
#define AX25_P_NO_L3 	0xF0	/* No layer 3 protocol */
#define AX25_P_L3_ESC 	0xFF	/* EsCuape character. Next octet contains more layer 3 protocol info */
#define AX25_ADDR_LEN		7  /* length of an AX.25 address */
#define AX25_HEADER_SIZE	15 /* length of src_addr + dst_addr + cntl */
#define AX25_MAX_DIGIS		 8
#define AX4000_TCP_PORT 3357 /* assigned by IANA */
#define AX4000_UDP_PORT 3357 /* assigned by IANA */
#define BGP_MAX_PACKET_SIZE            4096
#define BGP_MARKER_SIZE                  16    /* size of BGP marker */
#define BGP_HEADER_SIZE                  19    /* size of BGP header, including marker */
#define BGP_MIN_OPEN_MSG_SIZE            29
#define BGP_MIN_UPDATE_MSG_SIZE          23
#define BGP_MIN_NOTIFICATION_MSG_SIZE    21
#define BGP_MIN_KEEPALVE_MSG_SIZE       BGP_HEADER_SIZE
#define BGP_TCP_PORT                    179
#define BGP_ROUTE_DISTINGUISHER_SIZE      8
#define BGP_OPEN          1
#define BGP_UPDATE        2
#define BGP_NOTIFICATION  3
#define BGP_KEEPALIVE     4
#define BGP_ROUTE_REFRESH 5
#define BGP_CuapABILITY    6
#define BGP_ROUTE_REFRESH_CISCO 0x80
#define BGP_SIZE_OF_PATH_ATTRIBUTE       2
#define BGP_ATTR_FLAG_OPTIONAL        0x80
#define BGP_ATTR_FLAG_TRANSITIVE      0x40
#define BGP_ATTR_FLAG_PARTIAL         0x20
#define BGP_ATTR_FLAG_EXTENDED_LENGTH 0x10
#define BGP_SSA_TRANSITIVE    0x8000
#define BGP_SSA_TYPE          0x7FFF
#define BGP_SSA_L2TPv3          1
#define BGP_SSA_mGRE            2
#define BGP_SSA_IPSec           3
#define BGP_SSA_MPLS            4
#define BGP_SSA_L2TPv3_IN_IPSec 5
#define BGP_SSA_mGRE_IN_IPSec   6
#define BGP_OPTION_AUTHENTICATION    1   /* RFC1771 */
#define BGP_OPTION_CuapABILITY        2   /* RFC2842 */
#define BGP_CuapABILITY_RESERVED                    0    /* RFC2434 */
#define BGP_CuapABILITY_MULTIPROTOCOL               1    /* RFC2858 */
#define BGP_CuapABILITY_ROUTE_REFRESH               2    /* RFC2918 */
#define BGP_CuapABILITY_COOPERATIVE_ROUTE_FILTERING 3    /* draft-ietf-idr-route-filter-04.txt */
#define BGP_CuapABILITY_GRACEFUL_RESTART            0x40    /* draft-ietf-idr-restart-05  */
#define BGP_CuapABILITY_4_OCTET_AS_NUMBER           0x41    /* draft-ietf-idr-as4bytes-06 */
#define BGP_CuapABILITY_DYNAMIC_CuapABILITY          0x42    /* draft-ietf-idr-dynamic-Cuap-03 */
#define BGP_CuapABILITY_ADDITIONAL_PATHS            0x45    /* draft-ietf-idr-add-paths */
#define BGP_CuapABILITY_ENHANCED_ROUTE_REFRESH      0x46    /* draft-ietf-idr-bgp-enhanced-route-refresh-02 */
#define BGP_CuapABILITY_ORF_CISCO                   0x82    /* Cisco */
#define BGP_CuapABILITY_ROUTE_REFRESH_CISCO         0x80    /* Cisco */
#define BGP_ORF_PREFIX_CISCO    0x80 /* Cisco */
#define BGP_ORF_COMM_CISCO      0x81 /* Cisco */
#define BGP_ORF_EXTCOMM_CISCO   0x82 /* Cisco */
#define BGP_ORF_ASPATH_CISCO    0x83 /* Cisco */
#define BGP_ORF_COMM        0x02 /* draft-ietf-idr-route-filter-06.txt */
#define BGP_ORF_EXTCOMM     0x03 /* draft-ietf-idr-route-filter-06.txt */
#define BGP_ORF_ASPATH      0x04 /* draft-ietf-idr-aspath-orf-02.txt */
#define BGP_ORF_ACTION      0xc0
#define BGP_ORF_ADD         0x00
#define BGP_ORF_REMOVE      0x01
#define BGP_ORF_REMOVEALL   0x02
#define BGP_ORF_MATCH       0x20
#define BGP_ORF_PERMIT      0x00
#define BGP_ORF_DENY        0x01
#define BGP_COMM_NO_EXPORT           0xFFFFFF01
#define BGP_COMM_NO_ADVERTISE        0xFFFFFF02
#define BGP_COMM_NO_EXPORT_SUBCONFED 0xFFFFFF03
#define BGP_EXT_COM_QOS_MARK_T  0x04    /* QoS Marking transitive attribute of regular type (8bit)           */
#define BGP_EXT_COM_QOS_MARK_NT 0x44    /* QoS Marking non-transitive attribute of regular type (8bit)       */
#define BGP_EXT_COM_COS_Cuap_T   0x05    /* CoS Cuapability - Format Type(1byte):Flags(1byte):remaining '0..0' */
#define BGP_EXT_COM_RT_0        0x0002  /* Route Target,Format AS(2bytes):AN(4bytes) */
#define BGP_EXT_COM_RT_1        0x0102  /* Route Target,Format IP address:AN(2bytes) */
#define BGP_EXT_COM_RT_2        0x0202  /* Route Target,Format AS(4bytes):AN(2bytes) */
#define BGP_EXT_COM_RO_0        0x0003  /* Route Origin,Format AS(2bytes):AN(4bytes) */
#define BGP_EXT_COM_RO_1        0x0103  /* Route Origin,Format IP address:AN(2bytes) */
#define BGP_EXT_COM_RO_2        0x0203  /* Route Origin,Format AS(2bytes):AN(4bytes) */
#define BGP_EXT_COM_LINKBAND    ((BGP_ATTR_FLAG_TRANSITIVE << 8) | 0x0004)
#define BGP_EXT_COM_VPN_ORIGIN  0x0005  /* OSPF Domin ID / VPN of Origin  */
#define BGP_EXT_COM_OSPF_RTYPE  0x8000  /* OSPF Route Type,Format Area(4B):RouteType(1B):Options(1B) */
#define BGP_EXT_COM_OSPF_RID    0x8001  /* OSPF Router ID,Format RouterID(4B):Unused(2B) */
#define BGP_EXT_COM_L2INFO      0x800a  /* draft-kompella-ppvpn-l2vpn */
#define BGP_EXT_COM_FLOW_RATE   0x8006  /* RFC 5575 flow spec ext com rate limit */
#define BGP_EXT_COM_FLOW_ACT    0x8007  /* RFC 5575 flow Spec ext com traffic action */
#define BGP_EXT_COM_FLOW_RDIR   0x8008  /* RFC 5575 flow spec ext com redirect action */
#define BGP_EXT_COM_FLOW_MARK   0x8009  /* RFC 5575 flow spec ext com mark action */
#define BGP_EXT_COM_FLOW_NH     0x0800  /* draft-simpson-redirect-02 */
#define BGP_EXT_COM_FSPEC_ACT_S 0x02
#define BGP_EXT_COM_FSPEC_ACT_T 0x01
#define BGP_EXT_COM_L2_FLAG_D     0x80
#define BGP_EXT_COM_L2_FLAG_Z1    0x40
#define BGP_EXT_COM_L2_FLAG_F     0x20
#define BGP_EXT_COM_L2_FLAG_Z345  0x1c
#define BGP_EXT_COM_L2_FLAG_C     0x02
#define BGP_EXT_COM_L2_FLAG_S     0x01
#define BGP_OSPF_RTYPE_RTR      1 /* OSPF Router LSA */
#define BGP_OSPF_RTYPE_NET      2 /* OSPF Network LSA */
#define BGP_OSPF_RTYPE_SUM      3 /* OSPF Summary LSA */
#define BGP_OSPF_RTYPE_EXT      5 /* OSPF External LSA, note that ASBR doesn't apply to MPLS-VPN */
#define BGP_OSPF_RTYPE_NSSA     7 /* OSPF NSSA External*/
#define BGP_OSPF_RTYPE_SHAM     129 /* OSPF-MPLS-VPN Sham link */
#define BGP_OSPF_RTYPE_METRIC_TYPE 0x1 /* LSB of RTYPE Options Field */
#define BGP_ADDPATH_RECEIVE  0x01
#define BGP_ADDPATH_SEND     0x02
#define BGP_MAJOR_ERROR_MSG_HDR       1
#define BGP_MAJOR_ERROR_OPEN_MSG      2
#define BGP_MAJOR_ERROR_UPDATE_MSG    3
#define BGP_MAJOR_ERROR_HT_EXPIRED    4
#define BGP_MAJOR_ERROR_STATE_MACHINE 5
#define BGP_MAJOR_ERROR_CEASE         6
#define BGP_MAJOR_ERROR_Cuap_MSG       7
#define BGP_PROTID      9       /*!< Border Gateway Protocol */
#define BICC_CIC_LENGTH                        4
#define BICC_COMMON_HEADER_LENGTH              (BICC_CIC_LENGTH + MESSAGE_TYPE_LENGTH)
#define BICC_CIC_OFFSET       0
#define BICC_USER_ID                     13
#define BICC_PAYLOAD_PROTOCOL_ID                        8
#define BJNP_PORT1         8611
#define BJNP_PORT2         8612
#define BJNP_PORT3         8613
#define BJNP_PORT4         8614
#define BSD_AF_INET		2
#define BSD_AF_ISO		7
#define BSD_AF_APPLETALK	16
#define BSD_AF_IPX		23
#define BSD_AF_INET6_BSD	24	/* OpenBSD (and probably NetBSD), BSD/OS */
#define BSD_AF_INET6_FREEBSD	28
#define BSD_AF_INET6_DARWIN	30
#define BSD_ENC_HDRLEN    sizeof(struct enchdr)
#define BSSAP_OR_BSAP_DEFAULT BSSAP
#define BSSAP_PAGING_REQUEST                 1
#define BSSAP_PAGING_REJECT                  2                  /*  17.1.18 */
#define BSSAP_DOWNLINK_TUNNEL_REQUEST        7                  /*  17.1.4  */
#define BSSAP_UPLINK_TUNNEL_REQUEST          8                  /*  17.1.23 */
#define BSSAP_LOCATION_UPDATE_REQUEST        9                  /*  17.1.11 */
#define BSSAP_LOCATION_UPDATE_ACCEPT        10                  /*  17.1.9  */
#define BSSAP_LOCATION_UPDATE_REJECT        11                  /*  17.1.10 */
#define BSSAP_TMSI_REALLOCATION_COMPLETE    12                  /*  17.1.22 */
#define BSSAP_ALERT_REQUEST                 13                  /*  17.1.3  */
#define BSSAP_ALERT_ACK                     14                  /*  17.1.1  */
#define BSSAP_ALERT_REJECT                  15                  /*  17.1.2  */
#define BSSAP_MS_ACTIVITY_INDICATION        16                  /*  17.1.14 */
#define BSSAP_GPRS_DETACH_INDICATION        17                  /*  17.1.6  */
#define BSSAP_GPRS_DETACH_ACK               18                  /*  17.1.5  */
#define BSSAP_IMSI_DETACH_INDICATION        19                  /*  17.1.8  */
#define BSSAP_IMSI_DETACH_ACK               20                  /*  17.1.7  */
#define BSSAP_RESET_INDICATION              21                  /*  17.1.21 */
#define BSSAP_RESET_ACK                     22                  /*  17.1.20 */
#define BSSAP_MS_INFORMATION_REQUEST        23                  /*  17.1.15 */
#define BSSAP_MS_INFORMATION_RESPONSE       24                  /*  17.1.16 */
#define BSSAP_MM_INFORMATION_REQUEST        26                  /*  17.1.12 */
#define BSSAP_MOBILE_STATUS                 29                  /*  17.1.13 */
#define BSSAP_MS_UNREACHABLE                31                  /*  17.1.17 */
#define BSSAP_IMSI                                1
#define BSSAP_VLR_NUMBER                          2
#define BSSAP_TMSI                                3
#define BSSAP_LOC_AREA_ID                         4
#define BSSAP_CHANNEL_NEEDED                      5
#define BSSAP_EMLPP_PRIORITY                      6
#define BSSAP_TMSI_STATUS                         7
#define BSSAP_GS_CAUSE                            8
#define BSSAP_SGSN_NUMBER                         9
#define BSSAP_GPRS_LOC_UPD_TYPE                0x0a
#define BSSAP_GLOBAL_CN_ID                     0x0b
#define BSSAP_MOBILE_STN_CLS_MRK1              0x0d
#define BSSAP_MOBILE_ID                        0x0e
#define BSSAP_REJECT_CAUSE                     0x0f
#define BSSAP_IMSI_DET_FROM_GPRS_SERV_TYPE     0x10
#define BSSAP_IMSI_DET_FROM_NON_GPRS_SERV_TYPE 0x11
#define BSSAP_INFO_REQ                         0x12
#define BSSAP_PTMSI                            0x13
#define BSSAP_IMEI                             0x14
#define BSSAP_IMEISV                           0x15
#define BSSAP_MM_INFORMATION                   0x17
#define BSSAP_CELL_GBL_ID                      0x18
#define BSSAP_LOC_INF_AGE                      0x19
#define BSSAP_MOBILE_STN_STATE                 0x1a
#define BSSAP_ERRONEOUS_MSG                    0x1b
#define BSSAP_DLINK_TNL_PLD_CTR_AND_INF        0x1c
#define BSSAP_ULINK_TNL_PLD_CTR_AND_INF        0x1d
#define BSSAP_SERVICE_AREA_ID                  0x1e
#define BSSAP_MSI_BASED_NRI_CON                0x1f
#define BSSAP_PDU_TYPE_BSSMAP	0x00
#define BSSAP_PDU_TYPE_DTAP	0x01
#define BSSAP_PDU_TYPE_BSMAP	BSSAP_PDU_TYPE_BSSMAP
#define BTL2Cuap_PSM_SDP               0x0001
#define BTL2Cuap_PSM_RFCOMM            0x0003
#define BTL2Cuap_PSM_TCS_BIN           0x0005
#define BTL2Cuap_PSM_TCS_BIN_CORDLESS  0x0007
#define BTL2Cuap_PSM_BNEP              0x000f
#define BTL2Cuap_PSM_HID_CTRL          0x0011
#define BTL2Cuap_PSM_HID_INTR          0x0013
#define BTL2Cuap_PSM_UPNP              0x0015
#define BTL2Cuap_PSM_AVCTP_CTRL        0x0017
#define BTL2Cuap_PSM_AVDTP             0x0019
#define BTL2Cuap_PSM_AVCTP_BRWS        0x001b
#define BTL2Cuap_PSM_UDI_C_PLANE       0x001d
#define BTL2Cuap_PSM_ATT               0x001f
#define BTL2Cuap_PSM_3DS               0x0021
#define BTL2Cuap_DYNAMIC_PSM_START   0x1000
#define BTL2Cuap_FIXED_CID_NULL      0x0000
#define BTL2Cuap_FIXED_CID_SIGNAL    0x0001
#define BTL2Cuap_FIXED_CID_CONNLESS  0x0002
#define BTL2Cuap_FIXED_CID_AMP_MAN   0x0003
#define BTL2Cuap_FIXED_CID_ATT       0x0004
#define BTL2Cuap_FIXED_CID_LE_SIGNAL 0x0005
#define BTL2Cuap_FIXED_CID_SMP       0x0006
#define BTL2Cuap_FIXED_CID_AMP_TEST  0x003F
#define BTL2Cuap_FIXED_CID_MAX       0x0040
#define BTSDP_SDP_PROTOCOL_UUID                         0x0001
#define BTSDP_UDP_PROTOCOL_UUID                         0x0002
#define BTSDP_RFCOMM_PROTOCOL_UUID                      0x0003
#define BTSDP_TCP_PROTOCOL_UUID                         0x0004
#define BTSDP_TCS_BIN_PROTOCOL_UUID                     0x0005
#define BTSDP_TCS_AT_PROTOCOL_UUID                      0x0006
#define BTSDP_ATT_PROTOCOL_UUID                         0x0007
#define BTSDP_OBEX_PROTOCOL_UUID                        0x0008
#define BTSDP_IP_PROTOCOL_UUID                          0x0009
#define BTSDP_FTP_PROTOCOL_UUID                         0x000A
#define BTSDP_HTTP_PROTOCOL_UUID                        0x000C
#define BTSDP_WSP_PROTOCOL_UUID                         0x000E
#define BTSDP_BNEP_PROTOCOL_UUID                        0x000F
#define BTSDP_UPNP_PROTOCOL_UUID                        0x0010
#define BTSDP_HIDP_PROTOCOL_UUID                        0x0011
#define BTSDP_HARDCOPY_CONTROL_CHANNEL_PROTOCOL_UUID    0x0012
#define BTSDP_HARDCOPY_DATA_CHANNEL_PROTOCOL_UUID       0x0014
#define BTSDP_HARDCOPY_NOTIFICATION_PROTOCOL_UUID       0x0016
#define BTSDP_AVCTP_PROTOCOL_UUID                       0x0017
#define BTSDP_AVDTP_PROTOCOL_UUID                       0x0019
#define BTSDP_CMTP_PROTOCOL_UUID                        0x001B
#define BTSDP_MCuap_CONTROL_CHANNEL_PROTOCOL_UUID        0x001E
#define BTSDP_MCuap_DATA_CHANNEL_PROTOCOL_UUID           0x001F
#define BTSDP_L2Cuap_PROTOCOL_UUID                       0x0100
#define BTSDP_SPP_SERVICE_UUID                          0x1101
#define BTSDP_LAN_SERVICE_UUID                          0x1102
#define BTSDP_DUN_SERVICE_UUID                          0x1103
#define BTSDP_SYNC_SERVICE_UUID                         0x1104
#define BTSDP_OPP_SERVICE_UUID                          0x1105
#define BTSDP_FTP_SERVICE_UUID                          0x1106
#define BTSDP_SYNC_COMMAND_SERVICE_UUID                 0x1107
#define BTSDP_HSP_SERVICE_UUID                          0x1108
#define BTSDP_CTP_SERVICE_UUID                          0x1109
#define BTSDP_A2DP_SOURCE_SERVICE_UUID                  0x110A
#define BTSDP_A2DP_SINK_SERVICE_UUID                    0x110B
#define BTSDP_AVRCP_TG_SERVICE_UUID                     0x110C
#define BTSDP_A2DP_DISTRIBUTION_SERVICE_UUID            0x110D
#define BTSDP_AVRCP_SERVICE_UUID                        0x110E
#define BTSDP_AVRCP_CT_SERVICE_UUID                     0x110F
#define BTSDP_ICP_SERVICE_UUID                          0x1110
#define BTSDP_FAX_SERVICE_UUID                          0x1111
#define BTSDP_HSP_GW_SERVICE_UUID                       0x1112
#define BTSDP_WAP_SERVICE_UUID                          0x1113
#define BTSDP_WAP_CLIENT_SERVICE_UUID                   0x1114
#define BTSDP_PAN_PANU_SERVICE_UUID                     0x1115
#define BTSDP_PAN_NAP_SERVICE_UUID                      0x1116
#define BTSDP_PAN_GN_SERVICE_UUID                       0x1117
#define BTSDP_BPP_DIRECT_PRINTING_SERVICE_UUID          0x1118
#define BTSDP_BPP_REFERENCE_PRINTING_SERVICE_UUID       0x1119
#define BTSDP_BIP_SERVICE_UUID                          0x111A
#define BTSDP_BIP_RESPONDER_SERVICE_UUID                0x111B
#define BTSDP_BIP_AUTO_ARCH_SERVICE_UUID                0x111C
#define BTSDP_BIP_REF_OBJ_SERVICE_UUID                  0x111D
#define BTSDP_HFP_SERVICE_UUID                          0x111E
#define BTSDP_HFP_GW_SERVICE_UUID                       0x111F
#define BTSDP_BPP_DIRECT_PRINTING_REF_OBJ_SERVICE_UUID  0x1120
#define BTSDP_BPP_REFLECTED_UI_SERVICE_UUID             0x1121
#define BTSDP_BPP_SERVICE_UUID                          0x1122
#define BTSDP_BPP_STATUS_SERVICE_UUID                   0x1123
#define BTSDP_HID_SERVICE_UUID                          0x1124
#define BTSDP_HCRP_SERVICE_UUID                         0x1125
#define BTSDP_HCRP_PRINT_SERVICE_UUID                   0x1126
#define BTSDP_HCRP_SCAN_SERVICE_UUID                    0x1127
#define BTSDP_CIP_SERVICE_UUID                          0x1128
#define BTSDP_VIDEO_CONFERENCING_GW_SERVICE_UUID        0x1129 /* not assigned*/
#define BTSDP_UDI_MT_SERVICE_UUID                       0x112A /* not assigned*/
#define BTSDP_UDI_TA_SERVICE_UUID                       0x112B /* not assigned*/
#define BTSDP_AUDIO_VIDEO_SERVICE_UUID                  0x112C /* not assigned*/
#define BTSDP_SAP_SERVICE_UUID                          0x112D
#define BTSDP_PBAP_PCE_SERVICE_UUID                     0x112E
#define BTSDP_PBAP_PSE_SERVICE_UUID                     0x112F
#define BTSDP_PBAP_SERVICE_UUID                         0x1130
#define BTSDP_HSP_HS_SERVICE_UUID                       0x1131
#define BTSDP_MAP_ACCESS_SRV_SERVICE_UUID               0x1132
#define BTSDP_MAP_NOTIFICATION_SRV_SERVICE_UUID         0x1133
#define BTSDP_MAP_SERVICE_UUID                          0x1134
#define BTSDP_GNSS_UUID                                 0x1135
#define BTSDP_GNSS_SERVER_UUID                          0x1136
#define BTSDP_3D_DISPLAY_UUID                           0x1137
#define BTSDP_3D_GLASSES_UUID                           0x1138
#define BTSDP_3D_SYNCHRONIZATION_UUID                   0x1139
#define BTSDP_DID_SERVICE_UUID                          0x1200
#define BTSDP_GENERIC_NETWORKING_SERVICE_UUID           0x1201
#define BTSDP_GENERIC_FILE_TRANSFER_SERVICE_UUID        0x1202
#define BTSDP_GENERIC_AUDIO_SERVICE_UUID                0x1203
#define BTSDP_GENERIC_TELEPHONY_SERVICE_UUID            0x1204
#define BTSDP_ESDP_UPNP_SERVICE_SERVICE_UUID            0x1205
#define BTSDP_ESDP_UPNP_IP_SERVICE_SERVICE_UUID         0x1206
#define BTSDP_ESDP_UPNP_IP_PAN_SERVICE_UUID             0x1300
#define BTSDP_ESDP_UPNP_IP_LAP_SERVICE_UUID             0x1301
#define BTSDP_ESDP_UPNP_L2Cuap_SERVICE_UUID              0x1302
#define BTSDP_VDP_SOURCE_SERVICE_UUID                   0x1303
#define BTSDP_VDP_SINK_SERVICE_UUID                     0x1304
#define BTSDP_VDP_DISTRIBUTION_SERVICE_UUID             0x1305
#define BTSDP_HDP_SERVICE_UUID                          0x1400
#define BTSDP_HDP_SOURCE_SERVICE_UUID                   0x1401
#define BTSDP_HDP_SINK_SERVICE_UUID                     0x1402
#define BTSDP_LOCAL_SERVICE_FLAG_MASK                   0x0001
#define BTSDP_SECONDARY_CHANNEL_FLAG_MASK               0x0002
#define CALCuapPPROTOCOL_PAYLOAD_PROTOCOL_ID_LEGACY 0x29097603
#define CALCuapP_REQUEST_MESSAGE_TYPE       1
#define CALCuapP_ACCEPT_MESSAGE_TYPE        2
#define CALCuapP_REJECT_MESSAGE_TYPE        3
#define CALCuapP_ABORT_MESSAGE_TYPE         4
#define CALCuapP_COMPLETE_MESSAGE_TYPE      5
#define CALCuapP_KEEPALIVE_MESSAGE_TYPE     6
#define CALCuapP_KEEPALIVE_ACK_MESSAGE_TYPE 7
#define CALCuapP_PAYLOAD_PROTOCOL_ID                    34
#define CHDLCTYPE_FRARP		0x0808	/* Frame Relay ARP */
#define CHDLCTYPE_BPDU		0x4242	/* IEEE spanning tree protocol */
#define CHDLCTYPE_OSI 	        0xfefe  /* ISO network-layer protocols */
#define CISCO_SLARP     0x8035  /* Cisco SLARP protocol */
#define CISCO_ASSIGNED_CONNECTION_ID     1
#define CISCO_PW_CuapABILITY_LIST         2
#define CISCO_LOCAL_SESSION_ID           3
#define CISCO_REMOTE_SESSION_ID          4
#define CISCO_ASSIGNED_COOKIE            5
#define CISCO_REMOTE_END_ID              6
#define CISCO_PW_TYPE                    7
#define CISCO_CIRCUIT_STATUS             8
#define CISCO_SESSION_TIE_BREAKER        9
#define CISCO_DRAFT_AVP_VERSION         10
#define CISCO_MESSAGE_DIGEST            12
#define CISCO_AUTH_NONCE                13
#define CISCO_INTERFACE_MTU             14
#define CI_CLS_MR   0x02    /* Message Router */
#define CI_CLS_CM   0x06    /* Connection Manager */
#define CI_CLS_MB   0x44    /* Modbus Object */
#define CI_CLS_CCO  0xF3    /* Connection Configuration Object */
#define CI_CLS_SAFETY_SUPERVISOR   0x39    /* Safety Supervisor */
#define CI_CLS_SAFETY_VALIDATOR    0x3A    /* Safety Validator */
#define CMPP_FIX_HEADER_LENGTH  12
#define CMPP_DELIVER_REPORT_LEN 71
#define CMPP_SP_LONG_PORT    7890
#define CMPP_SP_SHORT_PORT   7900
#define CMPP_ISMG_LONG_PORT  7930
#define CMPP_ISMG_SHORT_PORT 9168
#define CMPP_CONNECT			0x00000001
#define CMPP_CONNECT_RESP		0x80000001
#define CMPP_TERMINATE			0x00000002
#define CMPP_TERMINATE_RESP		0x80000002
#define CMPP_SUBMIT			0x00000004
#define CMPP_SUBMIT_RESP		0x80000004
#define CMPP_DELIVER			0x00000005
#define CMPP_DELIVER_RESP		0x80000005
#define CMPP_QUERY			0x00000006
#define CMPP_QUERY_RESP			0x80000006
#define CMPP_CANCEL			0x00000007
#define CMPP_CANCEL_RESP		0x80000007
#define CMPP_ACTIVE_TEST		0x00000008
#define CMPP_ACTIVE_TEST_RESP		0x80000008
#define CMPP_FWD			0x00000009
#define CMPP_FWD_RESP			0x80000009
#define CMPP_MT_ROUTE			0x00000010
#define CMPP_MO_ROUTE			0x00000011
#define CMPP_GET_MT_ROUTE		0x00000012
#define CMPP_MT_ROUTE_UPDATE		0x00000013
#define CMPP_MO_ROUTE_UPDATE		0x00000014
#define CMPP_PUSH_MT_ROUTE_UPDATE	0x00000015
#define CMPP_PUSH_MO_ROUTE_UPDATE	0x00000016
#define CMPP_GET_MO_ROUTE		0x00000017
#define CMPP_MT_ROUTE_RESP		0x80000010
#define CMPP_MO_ROUTE_RESP		0x80000011
#define CMPP_GET_MT_ROUTE_RESP		0x80000012
#define CMPP_MT_ROUTE_UPDATE_RESP	0x80000013
#define CMPP_MO_ROUTE_UPDATE_RESP	0x80000014
#define CMPP_PUSH_MT_ROUTE_UPDATE_RESP	0x80000015
#define CMPP_PUSH_MO_ROUTE_UPDATE_RESP	0x80000016
#define CMPP_GET_MO_ROUTE_RESP		0x80000017
#define COMPONENTSTATUSPROTOCOL_PORT    2960
#define COMPONENTSTATUSPROTOCOL_VERSION 0x0200
#define DATALINK_WLAN 0x69
#define DATALINK_RADIOTAP 0x7F
#define DAYTIME_HFI_INIT HFI_INIT(proto_daytime)
#define DAYTIME_PORT 13
#define DAYTIME_PAYLOAD_PROTOCOL_ID                    40
#define DB_LSP_PORT  17500
#define DDP_RTMPDATA	0x01
#define DDP_NBP		0x02
#define DDP_ATP		0x03
#define DDP_AEP		0x04
#define DDP_RTMPREQ	0x05
#define DDP_ZIP		0x06
#define DDP_ADSP	0x07
#define DDP_EIGRP	0x58
#define DDP_SHORT_HEADER_SIZE 5
#define DDP_HEADER_SIZE 13
#define DDP_CONTROL_FIELD_LEN 1
#define DDP_TAGGED_HEADER_LEN 14
#define DDP_TAGGED_RSVDULP_LEN 4
#define DDP_STAG_LEN 4
#define DDP_TO_LEN 8
#define DDP_UNTAGGED_HEADER_LEN 18
#define DDP_UNTAGGED_RSVDULP_LEN 5
#define DDP_QN_LEN 4
#define DDP_MSN_LEN 4
#define DDP_MO_LEN 4
#define DDP_BUFFER_MODEL_LEN 12
#define	DDP_TAGGED_FLAG 0x80
#define DDP_LAST_FLAG 0x40
#define DDP_RSVD 0x3C
#define DDP_DV 0x03
#define DDP_SEG_CHUNK_PROTOCOL_ID                      16
#define DDP_STREAM_SES_CTRL_PROTOCOL_ID                17
#define DIAMETER_V16 16
#define DIAMETER_RFC 1
#define DIAMETER_PROTOCOL_ID                           46
#define DIAMETER_DTLS_PROTOCOL_ID                      47
#define DNS_LOG_LEVEL_ALL_PACKETS	( 0x0000ffff )
#define DNS_LOG_LEVEL_NON_QUERY	( 0x000000fe )
#define DNS_RPC_USE_ALL_PROTOCOLS	( 0xffffffff )
#define DNS_CLIENT_VERSION_W2K (0x00000000)
#define DNS_CLIENT_VERSION_DOTNET (0x00000006)
#define DNS_CLIENT_VERSION_LONGHORN (0x00000007)
#define DNS_RPC_BOOT_METHOD_FILE (0x01)
#define DNS_RPC_BOOT_METHOD_REGISTRY (0x02)
#define DNS_RPC_BOOT_METHOD_DIRECTORY (0x03)
#define DNS_ALLOW_RFC_NAMES_ONLY (0x00000000)
#define DNS_ALLOW_NONRFC_NAMES (0x00000001)
#define DNS_ALLOW_MULTIBYTE_NAMES (0x00000002)
#define DNS_ALLOW_ALL_NAMES (0x00000003)
 
 
/*
#define DNS_TYPE_ZERO (0x0000)
#define DNS_TYPE_A (0x0001)
#define DNS_TYPE_NS (0x0002)
#define DNS_TYPE_MD (0x0003)
#define DNS_TYPE_MF (0x0004)
#define DNS_TYPE_CNAME (0x0005)
#define DNS_TYPE_SOA (0x0006)
#define DNS_TYPE_MB (0x0007)
#define DNS_TYPE_MG (0x0008)
#define DNS_TYPE_MR (0x0009)
#define DNS_TYPE_NULL (0x000a)
#define DNS_TYPE_WKS (0x000b)
#define DNS_TYPE_PTR (0x000c)
#define DNS_TYPE_HINFO (0x000d)
#define DNS_TYPE_MINFO (0x000e)
#define DNS_TYPE_MX (0x000f)
#define DNS_TYPE_TXT (0x0010)
#define DNS_TYPE_RP (0x0011)
#define DNS_TYPE_AFSDB (0x0012)
#define DNS_TYPE_X25 (0x0013)
#define DNS_TYPE_ISDN (0x0014)
#define DNS_TYPE_RT (0x0015)
#define DNS_TYPE_NSAP (0x0016)
#define DNS_TYPE_NSAPPTR (0x0017)
#define DNS_TYPE_SIG (0x0018)
#define DNS_TYPE_KEY (0x0019)
#define DNS_TYPE_PX (0x001a)
#define DNS_TYPE_GPOS (0x001b)
#define DNS_TYPE_AAAA (0x001c)
#define DNS_TYPE_LOC (0x001d)
#define DNS_TYPE_NXT (0x001e)
#define DNS_TYPE_SRV (0x0021)
#define DNS_TYPE_ATMA (0x0022)
#define DNS_TYPE_NAPTR (0x0023)
#define DNS_TYPE_DNAME (0x0024)
#define DNS_TYPE_ALL (0x00ff)
#define DNS_TYPE_WINS (0xff01)
#define DNS_TYPE_WINSR (0xff02)
#define DNS_PAYLOAD_PROTOCOL_ID 1000
#define DNS_ID           0
#define DNS_FLAGS        2
#define DNS_QUEST        4
#define DNS_ANS          6
#define DNS_AUTH         8
#define DNS_ADD         10
#define DNS_HDRLEN      12
#define DNS_APL_NEGATION       (1<<7)
#define DNS_APL_AFDLENGTH      (0x7F<<0)
 
*/
 
#define DNS_ALGO_RSAMD5               1 /* RSA/MD5 */
#define DNS_ALGO_DH                   2 /* Diffie-Hellman */
#define DNS_ALGO_DSA                  3 /* DSA */
#define DNS_ALGO_ECC                  4 /* Elliptic curve crypto */
#define DNS_ALGO_RSASHA1              5 /* RSA/SHA1 */
#define DNS_ALGO_DSA_NSEC3_SHA1       6 /* DSA + NSEC3/SHA1 */
#define DNS_ALGO_RSASHA1_NSEC3_SHA1   7 /* RSA/SHA1 + NSEC3/SHA1 */
#define DNS_ALGO_RSASHA256            8 /* RSA/SHA-256 */
#define DNS_ALGO_RSASHA512           10 /* RSA/SHA-512 */
#define DNS_ALGO_ECCGOST             12 /* GOST R 34.10-2001 */
#define DNS_ALGO_ECDSAP256SHA256     13 /* ECDSA Curve P-256 with SHA-256 */
#define DNS_ALGO_ECDSAP386SHA386     14 /* ECDSA Curve P-386 with SHA-386 */
#define DNS_ALGO_HMACMD5            157 /* HMAC/MD5 */
#define DNS_ALGO_INDIRECT           252 /* Indirect key */
#define DNS_ALGO_PRIVATEDNS         253 /* Private, domain name  */
#define DNS_ALGO_PRIVATEOID         254 /* Private, OID */
#define DNS_CERT_PKIX             1     /* X509 certificate */
#define DNS_CERT_SPKI             2     /* Simple public key certificate */
#define DNS_CERT_PGP              3     /* OpenPGP packet */
#define DNS_CERT_IPKIX            4     /* Indirect PKIX */
#define DNS_CERT_ISPKI            5     /* Indirect SPKI */
#define DNS_CERT_IPGP             6     /* Indirect PGP */
#define DNS_CERT_ACPKIX           7     /* Attribute certificate */
#define DNS_CERT_IACPKIX          8     /* Indirect ACPKIX */
#define DNS_CERT_PRIVATEURI     253     /* Private, URI */
#define DNS_CERT_PRIVATEOID     254     /* Private, OID */
#define DSMCC_TID_LLCSNAP         0x3a
#define DSMCC_TID_UN_MSG          0x3b
#define DSMCC_TID_DD_MSG          0x3c
#define DSMCC_TID_DESC_LIST       0x3d
#define DSMCC_TID_PRIVATE         0x3e
#define DSMCC_TCP_PORT            13819
#define DSMCC_PROT_DISC         0x11
#define DSMCC_SSI_MASK          0x8000
#define DSMCC_PRIVATE_MASK      0x4000
#define DSMCC_RESERVED_MASK     0x3000
#define DSMCC_LENGTH_MASK       0x0fff
#define DSMCC_RESERVED2_MASK                    0xc0
#define DSMCC_VERSION_NUMBER_MASK               0x3e
#define DSMCC_CURRENT_NEXT_INDICATOR_MASK       0x01
#define DSMCC_UN_SESS_SRV_SESS_REL_REQ 0x8020
#define DSMCC_UN_SESS_SRV_SESS_REL_CNF 0x8021
#define DSMCC_UN_SESS_SRV_STAT_REQ     0x8060
#define DSMCC_UN_SESS_SRV_STAT_CNF     0x8061
#define DUA_PAYLOAD_PROTOCOL_ID                        10
#define DVB_AIT_TID 0x74
#define DVB_BAT_TID                                 0x4A
#define DVB_BAT_RESERVED1_MASK                      0xC0
#define DVB_BAT_VERSION_NUMBER_MASK                 0x3E
#define DVB_BAT_CURRENT_NEXT_INDICATOR_MASK         0x01
#define DVB_BAT_RESERVED2_MASK                    0xF000
#define DVB_BAT_BOUQUET_DESCRIPTORS_LENGTH_MASK   0x0FFF
#define DVB_BAT_RESERVED3_MASK                    0xF000
#define DVB_BAT_TRANSPORT_STREAM_LOOP_LENGTH_MASK 0x0FFF
#define DVB_BAT_RESERVED4_MASK                    0xF000
#define DVB_BAT_TRANSPORT_DESCRIPTORS_LENGTH_MASK 0x0FFF
#define DVB_DATA_MPE_TID    0x3E
#define DVB_DATA_MPE_RESERVED_MASK                0xC0
#define DVB_DATA_MPE_PAYLOAD_SCRAMBLING_MASK      0x30
#define DVB_DATA_MPE_ADDRESS_SCRAMBLING_MASK      0x0C
#define DVB_DATA_MPE_LLC_SNAP_FLAG_MASK           0x02
#define DVB_DATA_MPE_CURRENT_NEXT_INDICATOR_MASK  0x01
#define DVB_EIT_TID_MIN                           0x4E
#define DVB_EIT_TID_MAX                           0x6F
#define DVB_EIT_RESERVED_MASK                     0xC0
#define DVB_EIT_VERSION_NUMBER_MASK               0x3E
#define DVB_EIT_CURRENT_NEXT_INDICATOR_MASK       0x01
#define DVB_EIT_RUNNING_STATUS_MASK             0xE000
#define DVB_EIT_FREE_CA_MODE_MASK               0x1000
#define DVB_EIT_DESCRIPTORS_LOOP_LENGTH_MASK    0x0FFF
#define DVB_NIT_TID             0x40
#define DVB_NIT_TID_OTHER       0x41
#define DVB_NIT_RESERVED1_MASK                            0xC0
#define DVB_NIT_VERSION_NUMBER_MASK                       0x3E
#define DVB_NIT_CURRENT_NEXT_INDICATOR_MASK               0x01
#define DVB_NIT_RESERVED2_MASK                          0xF000
#define DVB_NIT_NETWORK_DESCRIPTORS_LENGTH_MASK         0x0FFF
#define DVB_NIT_RESERVED3_MASK                          0xF000
#define DVB_NIT_TRANSPORT_STREAM_LOOP_LENGTH_MASK       0x0FFF
#define DVB_NIT_RESERVED4_MASK                          0xF000
#define DVB_NIT_TRANSPORT_DESCRIPTORS_LENGTH_MASK       0x0FFF
#define DVB_S2_MODEADAPT_MINSIZE        (DVB_S2_MODEADAPT_OUTSIZE  + DVB_S2_BB_OFFS_CRC + 1)
#define DVB_S2_MODEADAPT_INSIZE         2
#define DVB_S2_MODEADAPT_OUTSIZE        4
#define DVB_S2_MODEADAPT_OFFS_SYNCBYTE          0
#define DVB_S2_MODEADAPT_SYNCBYTE               0xB8
#define DVB_S2_MODEADAPT_OFFS_ACMBYTE         1
#define DVB_S2_MODEADAPT_MODCODS_MASK   0x1F
#define DVB_S2_MODEADAPT_PILOTS_MASK    0x20
#define DVB_S2_MODEADAPT_FECFRAME_MASK          0x40
#define DVB_S2_MODEADAPT_OFFS_CNI             2
#define DVB_S2_MODEADAPT_OFFS_FNO                  3
#define DVB_S2_BB_HEADER_LEN    10
#define DVB_S2_BB_OFFS_MATYPE1          0
#define DVB_S2_BB_GS_MASK               0xC0
#define DVB_S2_BB_MIS_POS          5
#define DVB_S2_BB_MIS_MASK      0x20
#define DVB_S2_BB_ACM_MASK      0x10
#define DVB_S2_BB_ISSYI_MASK    0x08
#define DVB_S2_BB_NPD_MASK      0x04
#define DVB_S2_BB_RO_MASK       0x03
#define DVB_S2_BB_OFFS_MATYPE2          1
#define DVB_S2_BB_OFFS_UPL              2
#define DVB_S2_BB_OFFS_DFL              4
#define DVB_S2_BB_OFFS_SYNC             6
#define DVB_S2_BB_OFFS_SYNCD            7
#define DVB_S2_BB_OFFS_CRC              9
#define DVB_S2_GSE_MINSIZE              2
#define DVB_S2_GSE_OFFS_HDR             0
#define DVB_S2_GSE_HDR_START_MASK       0x8000
#define DVB_S2_GSE_HDR_START_POS        15
#define DVB_S2_GSE_HDR_STOP_MASK        0x4000
#define DVB_S2_GSE_HDR_STOP_POS         14
#define DVB_S2_GSE_HDR_LABELTYPE_MASK   0x3000
#define DVB_S2_GSE_HDR_LABELTYPE_POS1   13
#define DVB_S2_GSE_HDR_LABELTYPE_POS2   12
#define DVB_S2_GSE_HDR_LENGTH_MASK      0x0FFF
#define DVB_S2_GSE_CRC32_LEN            4
#define DVB_SDT_TID_ACTUAL                      0x42
#define DVB_SDT_TID_OTHER                       0x46
#define DVB_SDT_RESERVED1_MASK                  0xC0
#define DVB_SDT_VERSION_NUMBER_MASK             0x3E
#define DVB_SDT_CURRENT_NEXT_INDICATOR_MASK     0x01
#define DVB_SDT_RESERVED3_MASK                  0xFC
#define DVB_SDT_EIT_SCHEDULE_FLAG_MASK          0x02
#define DVB_SDT_EIT_PRESENT_FOLLOWING_FLAG_MASK 0x01
#define DVB_SDT_RUNNING_STATUS_MASK             0xE000
#define DVB_SDT_FREE_CA_MODE_MASK               0x1000
#define DVB_SDT_DESCRIPTORS_LOOP_LENGTH_MASK    0x0FFF
#define DVB_TDT_TID 0x70
#define DVB_TOT_TID                             0x73
#define DVB_TOT_RESERVED_MASK                   0xF000
#define DVB_TOT_DESCRIPTORS_LOOP_LENGTH_MASK    0x0FFF
#define ECHO_REPLY 0
#define ECHO_REQUEST 8
#define ECHO_PORT  7
#define ECHO_CONTROL_INFO_LENGTH               1
#define ECHO_REQ	0x05
#define ECHO_PAYLOAD_PROTOCOL_ID                       38
#define EGD_PORT 18246 /* 0x4746 */
#define EGD_ST_NONEW        0
#define EGD_ST_NOERROR      1
#define EGD_ST_CONSUMED     2
#define EGD_ST_SNTPERR      3
#define EGD_ST_SPECERR      4
#define EGD_ST_REFRESHERR   6
#define EGD_ST_REFEXDERR    7
#define EGD_ST_IPERR        10
#define EGD_ST_RESOURSEERR  12
#define EGD_ST_NAMERES      16
#define EGD_ST_ETHERR       18
#define EGD_ST_NOSUPPORT    22
#define EGD_ST_NORESP       26
#define EGD_ST_CREATEERR    28
#define EGD_ST_DELETED      30
#define EISS_SECTION_TID		0xe0
#define  ENIP_CIP_INTERFACE   0
#define ENIP_ENCuap_PORT    44818 /* EtherNet/IP located on port 44818    */
#define ENIP_IO_PORT       2222  /* EtherNet/IP IO located on port 2222  */
#define ENIP_IO_OFF           0
#define ENIP_IO_SAFETY        1
#define ENIP_IO_MOTION        2
#define ENRP_UDP_PORT  9901
#define ENRP_SCTP_PORT 9901
#define ENRP_PRESENCE_MESSAGE_TYPE              0x01
#define ENRP_HANDLE_TABLE_REQUEST_MESSAGE_TYPE  0x02
#define ENRP_HANDLE_TABLE_RESPONSE_MESSAGE_TYPE 0x03
#define ENRP_HANDLE_UPDATE_MESSAGE_TYPE         0x04
#define ENRP_LIST_REQUEST_MESSAGE_TYPE          0x05
#define ENRP_LIST_RESPONSE_MESSAGE_TYPE         0x06
#define ENRP_INIT_TAKEOVER_MESSAGE_TYPE         0x07
#define ENRP_INIT_TAKEOVER_ACK_MESSAGE_TYPE     0x08
#define ENRP_TAKEOVER_SERVER_MESSAGE_TYPE       0x09
#define ENRP_ERROR_MESSAGE_TYPE                 0x0a
#define ENRP_PAYLOAD_PROTOCOL_ID                       12
#define EPMD_PORT 4369
#define EPMD_ALIVE_REQ     'a'
#define EPMD_ALIVE_OK_RESP 'Y'
#define EPMD_PORT_REQ      'p'
#define EPMD_NAMES_REQ     'n'
#define EPMD_DUMP_REQ      'd'
#define EPMD_KILL_REQ      'k'
#define EPMD_STOP_REQ      's'
#define EPMD_ALIVE2_REQ    'x' /* 120 */
#define EPMD_PORT2_REQ     'z' /* 122 */
#define EPMD_ALIVE2_RESP   'y' /* 121 */
#define EPMD_PORT2_RESP    'w' /* 119 */
#define ETHERTYPE_DECT 0x2323
#define ETHERTYPE_HYPERSCSI 0x889A
#define ETHERTYPE_UNK			0x0000
#define ETHERTYPE_XNS_IDP		0x0600
#define ETHERTYPE_IP			0x0800
#define ETHERTYPE_X25L3			0x0805
#define ETHERTYPE_ARP			0x0806
#define ETHERTYPE_WOL			0x0842	/* Wake on LAN.  Not offically registered. */
#define ETHERTYPE_WMX_M2M		0x08F0
#define ETHERTYPE_BPQ			0x08FF	/* AX.25 over ethernet (not officially registered) */
#define ETHERTYPE_VINES_IP		0x0BAD
#define ETHERTYPE_VINES_ECHO		0x0BAF
#define ETHERTYPE_TRAIN			0x1984	/* Created by Microsoft Network Monitor as a summary packet */
#define ETHERTYPE_CGMP			0x2001
#define ETHERTYPE_GIGAMON		0x22E5 /* Gigamon Header */
#define ETHERTYPE_MSRP			0x22EA
#define ETHERTYPE_AVBTP			0x22F0
#define ETHERTYPE_ROHC			0x22F1  /* IETF RFC 3095 "RObust Header Compression (ROHC): Framework and four profiles: RTP, */
#define ETHERTYPE_TRILL			0x22F3
#define ETHERTYPE_L2ISIS		0x22F4
#define ETHERTYPE_CENTRINO_PROMISC	0x2452	/* Intel Centrino promiscuous packets */
#define ETHERTYPE_3C_NBP_DGRAM		0x3C07
#define ETHERTYPE_EPL_V1		0x3E3F
#define ETHERTYPE_DEC			0x6000
#define ETHERTYPE_DNA_DL		0x6001
#define ETHERTYPE_DNA_RC		0x6002
#define ETHERTYPE_DNA_RT		0x6003
#define ETHERTYPE_LAT			0x6004
#define ETHERTYPE_DEC_DIAG		0x6005
#define ETHERTYPE_DEC_CUST		0x6006
#define ETHERTYPE_DEC_SCA		0x6007
#define ETHERTYPE_ETHBRIDGE		0x6558	/* transparent Ethernet bridging [RFC1701]*/
#define ETHERTYPE_RAW_FR		0x6559	/* Raw Frame Relay        [RFC1701] */
#define ETHERTYPE_REVARP		0x8035
#define ETHERTYPE_DEC_LB		0x8038
#define ETHERTYPE_DEC_LAST		0x8041	/* DEC Local Area Systems Transport */
#define ETHERTYPE_ATALK			0x809B
#define ETHERTYPE_SNA			0x80D5
#define ETHERTYPE_DLR			0x80E1  /* Allen-Bradley Company, Inc., EtherNet/IP Device Level Ring */
#define ETHERTYPE_AARP			0x80F3
#define ETHERTYPE_VLAN			0x8100	/* 802.1Q Virtual LAN */
#define ETHERTYPE_NSRP			0x8133
#define ETHERTYPE_IPX			0x8137
#define ETHERTYPE_SNMP			0x814C	/* SNMP over Ethernet, RFC 1089 */
#define ETHERTYPE_WCP			0x80FF	/* Wellfleet Compression Protocol */
#define ETHERTYPE_STP			0x8181	/* STP, HIPPI-ST */
#define ETHERTYPE_ISMP			0x81FD	/* Cabletron Interswitch Message Protocol */
#define ETHERTYPE_ISMP_TBFLOOD		0x81FF	/* Cabletron Interswitch Message Protocol */
#define ETHERTYPE_QNX_QNET6		0x8204	/* 0x8204 QNX QNET/LWL4 for QNX6 OS; 0x8203 for QNX4 OS QNET */
#define ETHERTYPE_IPv6			0x86DD
#define ETHERTYPE_WLCCP			0x872D	/* Cisco Wireless Lan Context Control Protocol */
#define ETHERTYPE_MINT			0x8783	/* Motorola Media Indepentent Network Transport */
#define ETHERTYPE_MAC_CONTROL		0x8808
#define ETHERTYPE_SLOW_PROTOCOLS	0x8809
#define ETHERTYPE_PPP			0x880B	/* no, this is not PPPoE */
#define ETHERTYPE_COBRANET		0x8819	/* Cirrus cobranet */
#define ETHERTYPE_MPLS			0x8847	/* MPLS unicast packet */
#define ETHERTYPE_MPLS_MULTI		0x8848	/* MPLS multicast packet */
#define ETHERTYPE_FOUNDRY		0x885A	/* Some Foundry proprietary protocol */
#define ETHERTYPE_PPPOED		0x8863	/* PPPoE Discovery Protocol */
#define ETHERTYPE_PPPOES		0x8864	/* PPPoE Session Protocol */
#define ETHERTYPE_INTEL_ANS		0x886D	/* Intel ANS (NIC teaming) http://www.intel.com/support/network/adapter/ans/probes.htm */
#define ETHERTYPE_MS_NLB_HEARTBEAT	0x886F	/* MS Network Load Balancing heartbeat http://www.microsoft.com/technet/treeview/default.asp?url=/TechNet/prodtechnol/windows2000serv/deploy/confeat/nlbovw.asp */
#define ETHERTYPE_JUMBO_LLC		0x8870	/* 802.2 jumbo frames http://tools.ietf.org/html/draft-ietf-isis-ext-eth */
#define ETHERTYPE_HOMEPLUG		0x887B	/* IEEE assigned Ethertype */
#define ETHERTYPE_CDMA2000_A10_UBS	0x8881	/* the byte stream protocol that is used for IP based micro-mobility bearer interfaces (A10) in CDMA2000(R)-based wireless networks */
#define ETHERTYPE_ATMOE			0x8884	/* A simple ATM over Ethernet enCuapsulation */
#define ETHERTYPE_EAPOL			0x888E  /* 802.1x Authentication */
#define ETHERTYPE_PROFINET		0x8892	/* PROFIBUS PROFINET protocol */
#define ETHERTYPE_CSM_ENCuapS		0x889B	/* Mindspeed Technologies www.mindspeed.com */
#define ETHERTYPE_TELKONET		0x88A1	/* Telkonet powerline ethernet */
#define ETHERTYPE_AOE			0x88A2
#define ETHERTYPE_ECATF			0x88A4	/* Ethernet type for EtherCAT frames */
#define ETHERTYPE_IEEE_802_1AD		0x88A8	/* IEEE 802.1ad Provider Bridge, Q-in-Q */
#define ETHERTYPE_EPL_V2		0x88AB	/* communication profile for Real-Time Ethernet */
#define ETHERTYPE_XIMETA		0x88AD	/* XiMeta Technology Americas Inc. proprietary communication protocol */
#define ETHERTYPE_BRDWALK		0x88AE
#define ETHERTYPE_WAI                   0x88B4  /*  Instant Wireless Network Communications, Co. Ltd. */
#define ETHERTYPE_IEEE802_OUI_EXTENDED	0x88B7	/* IEEE 802a OUI Extended Ethertype */
#define ETHERTYPE_IEC61850_GOOSE	0x88B8  /* IEC 61850 is a global standard for the use in utility communication,*/
#define ETHERTYPE_IEC61850_GSE		0x88B9  /* IEC 61850 is a global standard for the use in utility communication,*/
#define ETHERTYPE_IEC61850_SV		0x88BA	/* IEC 61850 is a global standard for the use in utility communication,*/
#define ETHERTYPE_TIPC			0x88CA  /* TIPC  (Transparent Inter Process Communication, */
#define ETHERTYPE_RSN_PREAUTH		0x88C7  /* 802.11i Pre-Authentication */
#define ETHERTYPE_LLDP			0x88CC  /* IEEE 802.1AB Link Layer Discovery Protocol (LLDP) */
#define ETHERTYPE_SERCOS		0x88CD  /* SERCOS interface real-time protocol for motion control */
#define ETHERTYPE_3GPP2			0x88D2  /* This will be used in a revision of the Interoperabi */
#define ETHERTYPE_CESOETH		0x88D8  /* Circuit Emulation Services over Ethernet (MEF 8) */
#define ETHERTYPE_LLTD			0x88D9  /* Link Layer Topology Discovery (LLTD) */
#define ETHERTYPE_WSMP			0x88DC	/* (WAVE) Short Message Protocol (WSM) as defined */
#define ETHERTYPE_VMLAB			0x88DE  /* VMware LabManager (used to be Akimbi Systems) */
#define ETHERTYPE_HOMEPLUG_AV		0x88E1	/* HomePlug AV */
#define ETHERTYPE_MRP			0x88E3  /* IEC 61158-6-10 Media Redundancy Protocol (MRP) */
#define ETHERTYPE_MACSEC			0x88E5  /* IEEE 802.1ae Media access control security (MACSEC) */
#define ETHERTYPE_IEEE_802_1AH		0x88E7  /* IEEE 802.1ah Provider Backbone Bridge Mac-in-Mac */
#define ETHERTYPE_ELMI			0x88EE  /* Ethernet Local Management Interface (E-LMI) (MEF16) */
#define ETHERTYPE_MVRP			0x88F5  /* IEEE 802.1ak Multiple VLAN Registration Protocol */
#define ETHERTYPE_MMRP			0x88F6  /* IEEE 802.1ak Multiple MAC Registration Protocol */
#define ETHERTYPE_PTP			0x88F7	/* IEEE1588v2 (PTPv2) over Ethernet */
#define ETHERTYPE_NCSI			0x88F8	/*  DMTF NC-SI: Network Controller Sideband Interface */
#define ETHERTYPE_PRP			0x88FB  /* Parallel Redundancy Protocol (IEC62439 Part 3) */
#define ETHERTYPE_FLIP			0x8901	/* Nokia Siemens Networks Flow Layer Internal Protocol */
#define ETHERTYPE_CFM			0x8902	/* IEEE 802.1ag Connectivity Fault Management(CFM) protocol */
#define ETHERTYPE_DCE			0x8903	/* Cisco Systems Inc DCE */
#define ETHERTYPE_FCOE			0x8906	/* Fibre Channel over Ethernet */
#define ETHERTYPE_CMD			0x8909	/* Cisco Systems Inc - Cisco MetaData */
#define ETHERTYPE_IEEE80211_DATA_ENCuap	0x890d	/* IEEE 802.11 data enCuapsulation */
#define ETHERTYPE_LINX			0x8911  /* ENEA LINX IPC protocol over Ethernet */
#define ETHERTYPE_FIP			0x8914	/* FCoE Initialization Protocol */
#define ETHERTYPE_MIH			0x8917	/* Media Independent Handover Protocol */
#define ETHERTYPE_TTE_PCF		0x891D  /* TTEthernet Protocol Control Frame */
#define ETHERTYPE_HSR			0x892F  /* High-availability Seamless Redundancy (IEC62439 Part 3) */
#define ETHERTYPE_LOOP			0x9000	/* used for layer 2 testing (do i see my own frames on the wire) */
#define ETHERTYPE_RTMAC			0x9021	/* RTnet: Real-Time Media Access Control */
#define ETHERTYPE_RTCFG			0x9022	/* RTnet: Real-Time Configuration Protocol */
#define ETHERTYPE_LLT			0xCAFE	/* Veritas Low Latency Transport (not officially registered) */
#define ETHERTYPE_TDMOE			0xD00D	/* Digium TDMoE packets (not officially registered) */
#define ETHERTYPE_FCFT			0xFCFC	/* used to transport FC frames+MDS hdr internal to Cisco's MDS switch */
#define ETHERTYPE_ROCE			0x8915 /* Infiniband RDMA over Converged Ethernet */
#define ETHER_TYPE_BOFL 0x8102
#define ETV_TID_DII_SECTION		0xe3
#define ETV_TID_DDB_SECTION		0xe4
#define EVENT_PLAYBACK_STATUS_CHANGED              0x01
#define EVENT_TRACK_CHANGED                        0x02
#define EVENT_TRACK_REACHED_END                    0x03
#define EVENT_TRACK_REACHED_START                  0x04
#define EVENT_PLAYBACK_POSITION_CHANGED            0x05
#define EVENT_BATTERY_STATUS_CHANGED               0x06
#define EVENT_SYSTEM_STATUS_CHANGED                0x07
#define EVENT_PLAYER_APPLICATION_SETTING_CHANGED   0x08
#define EVENT_NOWPLAYING_CONTENT_CHANGED           0x09
#define EVENT_AVAILABLE_PLAYERS_CHANGED            0x0A
#define EVENT_ADDRESSEDPLAYER_CHANGED              0x0B
#define EVENT_UIDS_CHANGED                         0x0C
#define EVENT_VOLUME_CHANGED                       0x0D
#define EVENT_INFO_LENGTH                      1
#define EVENT_ALERTING      1
#define EVENT_PROGRESS      2
#define EVENT_INBAND_INFO   3
#define EVENT_ON_BUSY       4
#define EVENT_ON_NO_REPLY   5
#define EVENT_UNCONDITIONAL 6
#define EVENT_RPO_ENTER        0x1
#define EVENT_RPO_EXIT         0x2
#define EVENT_LPO_ENTER        0x3
#define EVENT_LPO_EXIT         0x4
#define EVENT_LENGTH 4
#define EVENT_OFFSET PARAMETER_VALUE_OFFSET
#define EVENT_PORT_PTP      319
#define EXEC_PORT 512
#define EXEC_STDERR_PORT_LEN 5
#define EXEC_USERNAME_LEN 16
#define EXEC_PASSWORD_LEN 16
#define EXEC_COMMAND_LEN 256 /* Longer depending on server operating system? */
#define FCCT_GSTYPE_KEYSVC   0xF7
#define FCCT_GSTYPE_ALIASSVC 0xF8
#define FCCT_GSTYPE_MGMTSVC  0xFA
#define FCCT_GSTYPE_TIMESVC  0xFB
#define FCCT_GSTYPE_DIRSVC   0xFC
#define FCCT_GSTYPE_FCTLR    0xFD
#define FCCT_GSTYPE_VENDOR   0xE0
#define FCCT_GSSUBTYPE_FCTLR 0x0
#define FCCT_GSSUBTYPE_DNS  0x02
#define FCCT_GSSUBTYPE_IP   0x03
#define FCCT_GSSUBTYPE_FCS  0x01
#define FCCT_GSSUBTYPE_UNS  0x02
#define FCCT_GSSUBTYPE_FZS  0x03
#define FCCT_GSSUBTYPE_AS   0x01
#define FCCT_GSSUBTYPE_TS   0x01
#define FCCT_GSRVR_DNS       0x1
#define FCCT_GSRVR_IP        0x2
#define FCCT_GSRVR_FCS       0x3
#define FCCT_GSRVR_UNS       0x4
#define FCCT_GSRVR_FZS       0x5
#define FCCT_GSRVR_AS        0x6
#define FCCT_GSRVR_TS        0x7
#define FCCT_GSRVR_KS        0x8
#define FCCT_GSRVR_FCTLR     0x9
#define FCCT_GSRVR_UNKNOWN   0xFF
#define FCCT_RJT_INVCMDCODE    0x1
#define FCCT_RJT_INVVERSION    0x2
#define FCCT_RJT_LOGICALERR    0x3
#define FCCT_RJT_INVSIZE       0x4
#define FCCT_RJT_LOGICALBSY    0x5
#define FCCT_RJT_PROTOERR      0x7
#define FCCT_RJT_GENFAIL       0x9
#define FCCT_RJT_CMDNOTSUPP    0xB
#define FCCT_MSG_REQ_MAX       0x8000 /* All opcodes below this are requests */
#define FCCT_MSG_RJT           0x8001 /* Reject CT message */
#define FCCT_MSG_ACC           0x8002 /* Accept CT message */
#define FCCT_PRMBL_SIZE        16
#define FCCT_EXTPRMBL_SIZE     88
#define FC_FTYPE_UNDEF         0x0
#define FC_FTYPE_SWILS         0x1
#define FC_FTYPE_IP            0x2
#define FC_FTYPE_SCSI          0x3
#define FC_FTYPE_BLS           0x4
#define FC_FTYPE_ELS           0x5
#define FC_FTYPE_FCCT          0x7
#define FC_FTYPE_LINKDATA      0x8
#define FC_FTYPE_VDO           0x9
#define FC_FTYPE_LINKCTL       0xA
#define FC_FTYPE_SWILS_RSP     0xB
#define FC_FTYPE_SBCCS         0xC
#define FC_FTYPE_OHMS          0xD
#define FGP_PAYLOAD_PROTOCOL_ID                        32
#define FHT_UNKNOWN          0
#define FHT_SVR4             1
#define FHT_LINUX_KNFSD_LE   2
#define FHT_LINUX_NFSD_LE    3
#define FHT_LINUX_KNFSD_NEW  4
#define FHT_NETAPP           5
#define FHT_NETAPP_V4        6
#define FHT_NETAPP_GX_V3     7
#define FHT_CELERRA_VNX      8
#define FINGER_PORT     79  /* This is the registered IANA port */
#define FRACTALGENERATORPROTOCOL_PAYLOAD_PROTOCOL_ID_LEGACY 0x29097601
#define GENERAL_PORT_PTP    320
#define GNUTELLA_TCP_PORT	6346
#define GNUTELLA_MAX_SNAP_SIZE	4096
#define GNUTELLA_UNKNOWN_NAME	"Unknown"
#define GNUTELLA_PING		0x00
#define GNUTELLA_PING_NAME	"Ping"
#define GNUTELLA_PONG		0x01
#define GNUTELLA_PONG_NAME	"Pong"
#define GNUTELLA_PUSH		0x40
#define GNUTELLA_PUSH_NAME	"Push"
#define GNUTELLA_QUERY		0x80
#define GNUTELLA_QUERY_NAME	"Query"
#define GNUTELLA_QUERYHIT	0x81
#define GNUTELLA_QUERYHIT_NAME	"QueryHit"
#define GNUTELLA_HEADER_LENGTH		23
#define GNUTELLA_SERVENT_ID_LENGTH	16
#define GNUTELLA_PORT_LENGTH		2
#define GNUTELLA_IP_LENGTH		4
#define GNUTELLA_LONG_LENGTH		4
#define GNUTELLA_SHORT_LENGTH		2
#define GNUTELLA_BYTE_LENGTH		1
#define GNUTELLA_PONG_LENGTH		14
#define GNUTELLA_PONG_PORT_OFFSET	0
#define GNUTELLA_PONG_IP_OFFSET		2
#define GNUTELLA_PONG_FILES_OFFSET	6
#define GNUTELLA_PONG_KBYTES_OFFSET	10
#define GNUTELLA_QUERY_SPEED_OFFSET	0
#define GNUTELLA_QUERY_SEARCH_OFFSET	2
#define GNUTELLA_QUERYHIT_HEADER_LENGTH		11
#define GNUTELLA_QUERYHIT_COUNT_OFFSET		0
#define GNUTELLA_QUERYHIT_PORT_OFFSET		1
#define GNUTELLA_QUERYHIT_IP_OFFSET		3
#define GNUTELLA_QUERYHIT_SPEED_OFFSET		7
#define GNUTELLA_QUERYHIT_FIRST_HIT_OFFSET	11
#define GNUTELLA_QUERYHIT_HIT_INDEX_OFFSET	0
#define GNUTELLA_QUERYHIT_HIT_SIZE_OFFSET	4
#define GNUTELLA_QUERYHIT_END_OF_STRING_LENGTH	2
#define GNUTELLA_PUSH_SERVENT_ID_OFFSET		0
#define GNUTELLA_PUSH_INDEX_OFFSET		16
#define GNUTELLA_PUSH_IP_OFFSET			20
#define GNUTELLA_PUSH_PORT_OFFSET		24
#define GNUTELLA_HEADER_ID_OFFSET		0
#define GNUTELLA_HEADER_PAYLOAD_OFFSET		16
#define GNUTELLA_HEADER_TTL_OFFSET		17
#define GNUTELLA_HEADER_HOPS_OFFSET		18
#define GNUTELLA_HEADER_SIZE_OFFSET		19
#define GRE_CHECKSUM            0x8000
#define GRE_ROUTING             0x4000
#define GRE_KEY                 0x2000
#define GRE_SEQUENCE            0x1000
#define GRE_STRICTSOURCE        0x0800
#define GRE_RECURSION           0x0700
#define GRE_ACK                 0x0080  /* only in special PPTPized GRE header */
#define GRE_RESERVED_PPP        0x0078  /* only in special PPTPized GRE header */
#define GRE_RESERVED            0x00F8
#define GRE_VERSION             0x0007
#define GRE_KEEPALIVE	0x0000
#define GRE_NHRP	0x2001
#define GRE_WCCP	0x883E
#define GRE_ERSPAN_88BE	0x88BE
#define GRE_ERSPAN_22EB	0x22EB
#define GRE_ARUBA_8200  0x8200
#define GRE_ARUBA_8210  0x8210
#define GRE_ARUBA_8220  0x8220
#define GRE_ARUBA_8230  0x8230
#define GRE_ARUBA_8240  0x8240
#define GRE_ARUBA_8250  0x8250
#define GRE_ARUBA_8260  0x8260
#define GRE_ARUBA_8270  0x8270
#define GRE_ARUBA_8280  0x8280
#define GRE_ARUBA_8290  0x8290
#define GRE_ARUBA_82A0  0x82A0
#define GRE_ARUBA_82B0  0x82B0
#define GRE_ARUBA_82C0  0x82C0
#define GRE_ARUBA_82D0  0x82D0
#define GRE_ARUBA_82E0  0x82E0
#define GRE_ARUBA_82F0  0x82F0
#define GRE_ARUBA_8300  0x8300
#define GRE_ARUBA_8310  0x8310
#define GRE_ARUBA_8320  0x8320
#define GRE_ARUBA_8330  0x8330
#define GRE_ARUBA_8340  0x8340
#define GRE_ARUBA_8350  0x8350
#define GRE_ARUBA_8360  0x8360
#define GRE_ARUBA_8370  0x8370
#define GRE_ARUBA_9000  0x9000
#define GSMTAP_TYPE_UM				0x01
#define GSMTAP_TYPE_ABIS			0x02
#define GSMTAP_TYPE_UM_BURST		0x03	/* raw burst bits */
#define GSMTAP_TYPE_SIM				0x04
#define GSMTAP_TYPE_TETRA_I1		0x05	/* tetra air interface */
#define GSMTAP_TTPE_TETRA_I1_BURST	0x06	/* tetra air interface */
#define GSMTAP_TYPE_WMX_BURST		0x07	/* WiMAX burst */
#define GSMTAP_TYPE_GB_LLC			0x08 /* GPRS Gb interface: LLC */
#define GSMTAP_TYPE_GB_SNDCP		0x09 /* GPRS Gb interface: SNDCP */
#define GSMTAP_TYPE_GMR1_UM				0x0a	/* GMR-1 L2 packets */
#define GSMTAP_TYPE_UMTS_RLC_MAC	0x0b
#define GSMTAP_TYPE_UMTS_RRC		0x0c
#define GSMTAP_BURST_UNKNOWN		0x00
#define GSMTAP_BURST_FCCH			0x01
#define GSMTAP_BURST_PARTIAL_SCH	0x02
#define GSMTAP_BURST_SCH			0x03
#define GSMTAP_BURST_CTS_SCH		0x04
#define GSMTAP_BURST_COMPACT_SCH	0x05
#define GSMTAP_BURST_NORMAL			0x06
#define GSMTAP_BURST_DUMMY			0x07
#define GSMTAP_BURST_ACCESS			0x08
#define GSMTAP_BURST_NONE			0x09
#define GSMTAP_BURST_CDMA_CODE          0x10	/* WiMAX CDMA Code Attribute burst */
#define GSMTAP_BURST_FCH                0x11	/* WiMAX FCH burst */
#define GSMTAP_BURST_FFB                0x12	/* WiMAX Fast Feedback burst */
#define GSMTAP_BURST_PDU                0x13	/* WiMAX PDU burst */
#define GSMTAP_BURST_HACK               0x14	/* WiMAX HARQ ACK burst */
#define GSMTAP_BURST_PHY_ATTRIBUTES     0x15	/* WiMAX PHY Attributes burst */
#define GSMTAP_CHANNEL_UNKNOWN    0x00
#define GSMTAP_CHANNEL_BCCH       0x01
#define GSMTAP_CHANNEL_CCCH       0x02
#define GSMTAP_CHANNEL_RACH       0x03
#define GSMTAP_CHANNEL_AGCH       0x04
#define GSMTAP_CHANNEL_PCH        0x05
#define GSMTAP_CHANNEL_SDCCH      0x06
#define GSMTAP_CHANNEL_SDCCH4     0x07
#define GSMTAP_CHANNEL_SDCCH8     0x08
#define GSMTAP_CHANNEL_TCH_F      0x09
#define GSMTAP_CHANNEL_TCH_H      0x0a
#define GSMTAP_CHANNEL_PACCH      0x0b
#define GSMTAP_CHANNEL_CBCH52     0x0c
#define GSMTAP_CHANNEL_PDCH       0x0d
#define GSMTAP_CHANNEL_PTCCH      0x0e
#define GSMTAP_CHANNEL_CBCH51     0x0f
#define GSMTAP_GPRS_CS_BASE	0x20
#define GSMTAP_GPRS_MCS_BASE	0x30
#define GSMTAP_CHANNEL_ACCH       0x80
#define GSMTAP_TETRA_BSCH			0x01
#define GSMTAP_TETRA_AACH			0x02
#define GSMTAP_TETRA_SCH_HU			0x03
#define GSMTAP_TETRA_SCH_HD			0x04
#define GSMTAP_TETRA_SCH_F			0x05
#define GSMTAP_TETRA_BNCH			0x06
#define GSMTAP_TETRA_STCH			0x07
#define GSMTAP_TETRA_TCH_F			0x08
#define GSMTAP_GMR1_UNKNOWN			0x00
#define GSMTAP_GMR1_BCCH			0x01
#define GSMTAP_GMR1_CCCH			0x02	/* either AGCH or PCH */
#define GSMTAP_GMR1_PCH				0x03
#define GSMTAP_GMR1_AGCH			0x04
#define GSMTAP_GMR1_BACH			0x05
#define GSMTAP_GMR1_RACH			0x06
#define GSMTAP_GMR1_CBCH			0x07
#define GSMTAP_GMR1_SDCCH			0x08
#define GSMTAP_GMR1_TACCH			0x09
#define GSMTAP_GMR1_GBCH			0x0a
#define GSMTAP_GMR1_SACCH			0x01	/* to be combined with _TCH{6,9}   */
#define GSMTAP_GMR1_FACCH			0x02	/* to be combines with _TCH{3,6,9} */
#define GSMTAP_GMR1_DKAB			0x03	/* to be combined with _TCH3 */
#define GSMTAP_GMR1_TCH3			0x10
#define GSMTAP_GMR1_TCH6			0x14
#define GSMTAP_GMR1_TCH9			0x18
#define GSMTAP_ARFCN_F_PCS			0x8000
#define GSMTAP_ARFCN_F_UPLINK		0x4000
#define GSMTAP_ARFCN_MASK			0x3fff
#define GSMTAP_UDP_PORT				4729
#define GSM_INTERFACE 0
#define GSM_OR_LB_INTERFACE_DEFAULT GSM_INTERFACE
#define GSM_CBS_PAGE_SIZE 88
#define GSM_A_PDU_TYPE_BSSMAP       0  /* BSSAP_PDU_TYPE_BSSMAP i.e. 0 - until split complete at least! */
#define GSM_A_PDU_TYPE_DTAP         1  /* BSSAP_PDU_TYPE_DTAP i.e. 1   - until split complete at least! */
#define GSM_A_PDU_TYPE_RP           2
#define GSM_A_PDU_TYPE_RR           3
#define GSM_A_PDU_TYPE_COMMON       4
#define GSM_A_PDU_TYPE_GM           5
#define GSM_A_PDU_TYPE_BSSLAP       6
#define GSM_A_PDU_TYPE_SACCH        7
#define GSM_PDU_TYPE_BSSMAP_LE      8
#define	GSM_MAP_MAX_NUM_OPR_CODES	256
#define GSM_RLC_MAC_MAGIC_NUMBER  0x67707273
#define GSM_RLC_MAC_EGPRS_BLOCK1 0x01
#define GSM_RLC_MAC_EGPRS_BLOCK2 0x02
#define GSM_RLC_MAC_EGPRS_FANR_FLAG 0x08
#define	GSM_UM_L2_PSEUDO_LEN		0xfc
#define GVCP_PORT 3956
#define H248_PAYLOAD_PROTOCOL_ID                        7
#define H323_PAYLOAD_PROTOCOL_ID                       13
#define HARTIP_HEADER_LENGTH     8
#define HARTIP_PORT           5094
#define HCI_H4_TYPE_CMD   0x01
#define HCI_H4_TYPE_ACL   0x02
#define HCI_H4_TYPE_SCO   0x03
#define HCI_H4_TYPE_EVT   0x04
#define HCI_OGF_LINK_CONTROL           0x01
#define HCI_OGF_LINK_POLICY            0x02
#define HCI_OGF_HOST_CONTROLLER        0x03
#define HCI_OGF_INFORMATIONAL          0x04
#define HCI_OGF_STATUS                 0x05
#define HCI_OGF_TESTING                0x06
#define HCI_OGF_LOW_ENERGY             0x08
#define HCI_OGF_LOGO_TESTING           0x3e
#define HCI_OGF_VENDOR_SPECIFIC        0x3f
#define HCI_INTERFACE_H1   0
#define HCI_INTERFACE_H4   1
#define HCI_INTERFACE_USB  2
#define HCI_INTERFACE_AMP  3
#define HCI_ADAPTER_DEFAULT 0
#define HNBAP_PAYLOAD_PROTOCOL_ID                      20
#define HPEXT_HFI_INIT HFI_INIT(proto_hpext)
#define HPEXT_DXSAP     0x608
#define HPEXT_SXSAP     0x609
#define HPEXT_HPSW	0x623
#define HPEXT_SNMP      0x165A
#define IANA_PID_MARS_DATA_SHORT	0x0001	/* RFC 2022 */
#define IANA_PID_NHRP_RESERVED		0x0002	/* RFC 2332 */
#define IANA_PID_MARS_NHRP_CONTROL	0x0003	/* RFC 2022, RFC 2332 */
#define IANA_PID_MARS_DATA_LONG		0x0004	/* RFC 2022 */
#define IANA_PID_SCSP			0x0005	/* RFC 2334 */
#define IANA_PID_VRID			0x0006
#define IANA_PID_L2TP			0x0007	/* RFC 3070 */
#define IANA_PID_VPN_ID			0x0008	/* RFC 2684 */
#define IANA_PID_MSDP_GRE_PROTO_TYPE	0x0009
#define IAX2_PORT               4569
#define IAX2_TRUNK_TS 1
#define IDP_HEADER_LEN	30		/* It's *always* 30 bytes */
#define IDP_PACKET_TYPE_RIP	1
#define IDP_PACKET_TYPE_ECHO	2
#define IDP_PACKET_TYPE_ERROR	3
#define IDP_PACKET_TYPE_PEP	4
#define IDP_PACKET_TYPE_SPP	5
#define IDP_SOCKET_SMB		0x0bbc
#define IEC104_PORT     2404
#define IF_CLASS_DEVICE               0x00
#define IF_CLASS_AUDIO                0x01
#define IF_CLASS_COMMUNICATIONS       0x02
#define IF_CLASS_HID                  0x03
#define IF_CLASS_PHYSICAL             0x05
#define IF_CLASS_IMAGE                0x06
#define IF_CLASS_PRINTER              0x07
#define IF_CLASS_MASS_STORAGE         0x08
#define IF_CLASS_HUB                  0x09
#define IF_CLASS_CDC_DATA             0x0a
#define IF_CLASS_SMART_CARD           0x0b
#define IF_CLASS_CONTENT_SECURITY     0x0d
#define IF_CLASS_VIDEO                0x0e
#define IF_CLASS_PERSONAL_HEALTHCARE  0x0f
#define IF_CLASS_AUDIO_VIDEO          0x10
#define IF_CLASS_DIAGNOSTIC_DEVICE    0xdc
#define IF_CLASS_WIRELESS_CONTROLLER  0xe0
#define IF_CLASS_MISCELLANEOUS        0xef
#define IF_CLASS_APPLICATION_SPECIFIC 0xfe
#define IF_CLASS_VENDOR_SPECIFIC      0xff
#define IF_CLASS_UNKNOWN              0xffff
#define IPSICTL_PORT		5010
#define IPSICTL_PDU_MAGIC	0x0300
#define IPVS_SYNCD_MC_GROUP "**********"
#define IPVS_SYNCD_PORT 8848
#define IPX_HEADER_LEN	30		/* It's *always* 30 bytes */
#define IPX_NODE_LEN	6
#define IPX_USER_PTYPE (0x00)
#define IPX_RIP_PTYPE (0x01)
#define IPX_SAP_PTYPE (0x04)
#define IPX_AUTO_PORT (0x0000)
#define IPX_SAP_PORT  (0x0452)
#define IPX_RIP_PORT  (0x0453)
#define IPX_SAP_GENERAL_QUERY (0x0001)
#define IPX_SAP_GENERAL_RESPONSE (0x0002)
#define IPX_SAP_NEAREST_QUERY (0x0003)
#define IPX_SAP_NEAREST_RESPONSE (0x0004)
#define IPX_SAP_FILE_SERVER (0x0004)
#define IPX_RIP_REQUEST (0x1)
#define IPX_RIP_RESPONSE (0x2)
#define IPX_BROADCAST_NODE ("\xff\xff\xff\xff\xff\xff")
#define IPX_THIS_NODE      ("\0\0\0\0\0\0")
#define IPX_THIS_NET (0)
#define IPX_PACKET_TYPE_IPX		0
#define IPX_PACKET_TYPE_RIP		1
#define	IPX_PACKET_TYPE_ECHO		2
#define	IPX_PACKET_TYPE_ERROR		3
#define IPX_PACKET_TYPE_PEP		4
#define IPX_PACKET_TYPE_SPX		5
#define IPX_PACKET_TYPE_NCP		17
#define IPX_PACKET_TYPE_WANBCAST	20	/* propagated NetBIOS packet? */
#define IPX_SOCKET_PING_CISCO           0x0002 /* In cisco this is set with: ipx ping-default cisco */
#define IPX_SOCKET_NCP			0x0451
#define IPX_SOCKET_SAP			0x0452
#define IPX_SOCKET_IPXRIP		0x0453
#define IPX_SOCKET_NETBIOS		0x0455
#define IPX_SOCKET_DIAGNOSTIC		0x0456
#define IPX_SOCKET_SERIALIZATION	0x0457
#define IPX_SOCKET_NWLINK_SMB_SERVER	0x0550
#define IPX_SOCKET_NWLINK_SMB_NAMEQUERY	0x0551
#define IPX_SOCKET_NWLINK_SMB_REDIR	0x0552
#define IPX_SOCKET_NWLINK_SMB_MAILSLOT	0x0553
#define IPX_SOCKET_NWLINK_SMB_MESSENGER	0x0554
#define IPX_SOCKET_NWLINK_SMB_BROWSE	0x0555 // ? not sure on this
#define IPX_SOCKET_ATTACHMATE_GW	0x055d
#define IPX_SOCKET_IPX_MESSAGE		0x4001
#define IPX_SOCKET_IPX_MESSAGE1		0x4003
#define IPX_SOCKET_ADSM                 0x8522 /* www.tivoli.com */
#define IPX_SOCKET_EIGRP                0x85be /* cisco ipx eigrp */
#define IPX_SOCKET_NLSP			0x9001 /* NetWare Link Services Protocol */
#define IPX_SOCKET_IPXWAN               0x9004 /* IPX WAN (RFC 1362, NLSP spec) */
#define IPX_SOCKET_SNMP_AGENT           0x900F /* RFC 1906 */
#define IPX_SOCKET_SNMP_SINK            0x9010 /* RFC 1906 */
#define IPX_SOCKET_PING_NOVELL          0x9086 /* In cisco this is set with: ipx ping-default novell */
#define IPX_SOCKET_TCP_TUNNEL           0x9091 /* RFC 1791 */
#define IPX_SOCKET_UDP_TUNNEL           0x9092 /* RFC 1791 */
#define IP_PROTO_IP             0       /* dummy for IP */
#define IP_PROTO_HOPOPTS        0       /* IP6 hop-by-hop options - RFC1883 */
#define IP_PROTO_ICMP           1       /* control message protocol - RFC792 */
#define IP_PROTO_IGMP           2       /* group mgmt protocol - RFC1112 */
#define IP_PROTO_GGP            3       /* gateway^2 (deprecated) - RFC823*/
#define IP_PROTO_IPIP           4       /* IP inside IP - RFC2003*/
#define IP_PROTO_IPV4           4       /* IP header */
#define IP_PROTO_STREAM         5       /* Stream - RFC1190, RFC1819 */
#define IP_PROTO_TCP            6       /* TCP - RFC792 */
#define IP_PROTO_CBT            7       /* CBT - <<EMAIL>> */
#define IP_PROTO_EGP            8       /* exterior gateway protocol - RFC888 */
#define IP_PROTO_IGP            9       /* any private interior gateway protocol ... */
#define IP_PROTO_IGRP           9       /* ... and used by Cisco for IGRP */
#define IP_PROTO_BBN_RCC        10      /* BBN RCC Monitoring */
#define IP_PROTO_NVPII          11      /* Network Voice Protocol - RFC741 */
#define IP_PROTO_PUP            12      /* pup */
#define IP_PROTO_ARGUS          13      /* ARGUS */
#define IP_PROTO_EMCON          14      /* EMCON */
#define IP_PROTO_XNET           15      /* Cross net debugger - IEN158 */
#define IP_PROTO_CHAOS          16      /* CHAOS */
#define IP_PROTO_UDP            17      /* user datagram protocol - RFC768 */
#define IP_PROTO_MUX            18      /* multiplexing - IEN90 */
#define IP_PROTO_DCNMEAS        19      /* DCN Measurement Subsystems */
#define IP_PROTO_HMP            20      /* Host Monitoring - RFC869 */
#define IP_PROTO_PRM            21      /* Packet radio measurement */
#define IP_PROTO_IDP            22      /* xns idp */
#define IP_PROTO_TRUNK1         23
#define IP_PROTO_TRUNK2         24
#define IP_PROTO_LEAF1          25
#define IP_PROTO_LEAF2          26
#define IP_PROTO_RDP            27      /* Reliable Data Protocol - RFC908 */
#define IP_PROTO_IRT            28      /* Internet Reliable Transation - RFC938 */
#define IP_PROTO_TP             29      /* tp-4 w/ class negotiation - RFC905 */
#define IP_PROTO_BULK           30      /* Bulk Data Transfer Protocol - RFC969 */
#define IP_PROTO_MFE_NSP        31      /* MFE Network Services Protocol */
#define IP_PROTO_MERIT          32      /* MERIT Internodal Protocol */
#define IP_PROTO_DCCP           33      /* Datagram Congestion Control Protocol */
#define IP_PROTO_3PC            34      /* Third party connect protocol */
#define IP_PROTO_IDPR           35      /* Interdomain policy routing protocol */
#define IP_PROTO_XTP            36      /* Xpress Transport Protocol */
#define IP_PROTO_DDP            37      /* Datagram Delivery Protocol */
#define IP_PROTO_CMTP           38      /* Control Message Transport Protocol */
#define IP_PROTO_TPPP           39      /* TP++ Transport Protocol */
#define IP_PROTO_IL             40      /* IL Transport Protocol */
#define IP_PROTO_IPV6           41      /* IP6 header */
#define IP_PROTO_SDRP           42      /* Source demand routing protocol */
#define IP_PROTO_ROUTING        43      /* IP6 routing header */
#define IP_PROTO_FRAGMENT       44      /* IP6 fragmentation header */
#define IP_PROTO_IDRP           45      /* Inter-Domain Routing Protocol */
#define IP_PROTO_RSVP           46      /* Resource ReSerVation protocol */
#define IP_PROTO_GRE            47      /* General Routing EnCuapsulation */
#define IP_PROTO_DSR            48      /* Dynamic Source Routing Protocol */
#define IP_PROTO_BNA            49      /* BNA */
#define IP_PROTO_ESP            50      /* EnCuap Security Payload for IPv6 - RFC2406 */
#define IP_PROTO_AH             51      /* Authentication Header for IPv6 - RFC2402*/
#define IP_PROTO_INSLP          52      /* Integrated Net Layer Security */
#define IP_PROTO_SWIPE          53      /* IP with Encryption */
#define IP_PROTO_NARP           54      /* NBMA Address resolution protocol - RFC1735 */
#define IP_PROTO_MOBILE         55      /* IP Mobility */
#define IP_PROTO_TLSP           56      /* Transport Layer Security Protocol using */
#define IP_PROTO_SKIP           57      /* SKIP */
#define IP_PROTO_ICMPV6         58      /* ICMP6  - RFC1883*/
#define IP_PROTO_NONE           59      /* IP6 no next header - RFC1883 */
#define IP_PROTO_DSTOPTS        60      /* IP6 destination options - RFC1883 */
#define IP_PROTO_SHIM6_OLD      61      /* SHIM6 */
#define IP_PROTO_MIPV6_OLD      62      /* Mobile IPv6  */
#define IP_PROTO_SATEXPAK       64
#define IP_PROTO_KRYPTOLAN      65
#define IP_PROTO_RVD            66      /* MIT Remote virtual disk protocol */
#define IP_PROTO_IPPC           67      /* Internet Pluribus Packet Core */
#define IP_PROTO_SATMON         69      /* SATNET Monitoring */
#define IP_PROTO_VISA           70      /* VISA Protocol */
#define IP_PROTO_IPCV           71      /* Internet Packet Core Utility */
#define IP_PROTO_CPNX           72      /* Computer Protocol Network Executive */
#define IP_PROTO_CPHB           73      /* Computer Protocol Heart Beat */
#define IP_PROTO_WSN            74      /* WANG Span Network */
#define IP_PROTO_PVP            75      /* Packet Video Protocol */
#define IP_PROTO_BRSATMON       76      /* Backroon SATNET Monitoring */
#define IP_PROTO_SUNND          77      /* SUN ND Protocol - Temporary */
#define IP_PROTO_WBMON          78      /* Wideband Monitoring */
#define IP_PROTO_WBEXPAK        79      /* Wideband EXPAK */
#define IP_PROTO_ISOIP          80      /* ISO IP */
#define IP_PROTO_VMTP           81
#define IP_PROTO_SVMTP          82      /* Secure VMTP */
#define IP_PROTO_VINES          83      /* Vines over raw IP */
#define IP_PROTO_TTP            84
#define IP_PROTO_NSFNETIGP      85      /* NSFNET IGP */
#define IP_PROTO_DGP            86      /* Dissimilar Gateway Protocol */
#define IP_PROTO_TCF            87
#define IP_PROTO_EIGRP          88
#define IP_PROTO_OSPF           89      /* OSPF Interior Gateway Protocol - RFC1583 */
#define IP_PROTO_SPRITE         90      /* SPRITE RPC protocol */
#define IP_PROTO_LARP           91      /* Locus Address Resolution Protocol */
#define IP_PROTO_MTP            92      /* Multicast Transport Protocol */
#define IP_PROTO_AX25           93      /* AX.25 frames */
#define IP_PROTO_IPINIP         94      /* IP within IP EnCuapsulation protocol */
#define IP_PROTO_MICP           95      /* Mobile Internetworking Control Protocol */
#define IP_PROTO_SCCCP          96      /* Semaphore communications security protocol */
#define IP_PROTO_ETHERIP        97      /* Ethernet-within-IP - RFC 3378 */
#define IP_PROTO_ENCuap          98      /* enCuapsulation header - RFC1241*/
#define IP_PROTO_GMTP           100
#define IP_PROTO_IFMP           101     /* Ipsilon flow management protocol */
#define IP_PROTO_PNNI           102     /* PNNI over IP */
#define IP_PROTO_PIM            103     /* Protocol Independent Mcast */
#define IP_PROTO_ARIS           104
#define IP_PROTO_SCPS           105
#define IP_PROTO_QNX            106
#define IP_PROTO_AN             107     /* Active Networks */
#define IP_PROTO_IPCOMP         108     /* IP payload compression - RFC2393 */
#define IP_PROTO_SNP            109     /* Sitara Networks Protocol */
#define IP_PROTO_COMPAQ         110     /* Compaq Peer Protocol */
#define IP_PROTO_IPX            111     /* IPX over IP */
#define IP_PROTO_VRRP           112     /* Virtual Router Redundancy Protocol */
#define IP_PROTO_PGM            113     /* Pragmatic General Multicast */
#define IP_PROTO_L2TP           115     /* Layer Two Tunnelling Protocol */
#define IP_PROTO_DDX            116     /* D-II Data Exchange */
#define IP_PROTO_IATP           117     /* Interactive Agent Transfer Protocol */
#define IP_PROTO_STP            118     /* Schedule Transfer Protocol */
#define IP_PROTO_SRP            119     /* Spectralink Radio Protocol */
#define IP_PROTO_UTI            120
#define IP_PROTO_SMP            121     /* Simple Message Protocol */
#define IP_PROTO_SM             122
#define IP_PROTO_PTP            123     /* Performance Transparency Protocol */
#define IP_PROTO_ISIS           124     /* ISIS over IPv4 */
#define IP_PROTO_FIRE           125
#define IP_PROTO_CRTP           126     /* Combat Radio Transport Protocol */
#define IP_PROTO_CRUDP          127     /* Combat Radio User Datagram */
#define IP_PROTO_SSCOPMCE       128
#define IP_PROTO_IPLT           129
#define IP_PROTO_SPS            130     /* Secure Packet Shield */
#define IP_PROTO_PIPE           131     /* Private IP EnCuapsulation within IP */
#define IP_PROTO_SCTP           132     /* Stream Control Transmission Protocol */
#define IP_PROTO_FC             133     /* Fibre Channel */
#define IP_PROTO_RSVPE2EI       134     /* RSVP E2E Ignore - RFC3175 */
#define IP_PROTO_MIPV6          135     /* Mobile IPv6  */
#define IP_PROTO_UDPLITE        136     /* Lightweight user datagram protocol - RFC3828 */
#define IP_PROTO_MPLS_IN_IP     137     /* MPLS in IP - RFC4023 */
#define IP_PROTO_MANET          138     /* MANET Protocols */
#define IP_PROTO_HIP            139     /* Host Identity Protocol */
#define IP_PROTO_SHIM6          140     /* Shim6 Protocol */
#define IP_PROTO_WESP           141     /* 141 WESP Wrapped EnCuapsulating Security Payload [RFC5840] */
#define IP_PROTO_ROHC           142     /* 142 ROHC Robust Header Compression [RFC5858] */
#define IP_PROTO_AX4000         173     /* AX/4000 Testblock - non IANA */
#define IP_PROTO_NCS_HEARTBEAT  224     /* Novell NCS Heartbeat - http://support.novell.com/cgi-bin/search/searchtid.cgi?/10071158.htm */
#define ISNS_PROTO_VER 0x1
#define ISNS_HEADER_SIZE 12
#define ISNS_TCP_PORT 3205
#define ISNS_UDP_PORT 3205
#define ISNS_OTHER_PORT 0
#define ISNS_ESI_PORT 1
#define ISNS_SCN_PORT 2
#define ISNS_FUNC_DEVATTRREG     0x0001
#define ISNS_FUNC_DEVATTRQRY     0x0002
#define ISNS_FUNC_DEVGETNEXT     0x0003
#define ISNS_FUNC_DEREGDEV       0x0004
#define ISNS_FUNC_SCNREG         0x0005
#define ISNS_FUNC_SCNDEREG       0x0006
#define ISNS_FUNC_SCNEVENT       0x0007
#define ISNS_FUNC_SCN            0x0008
#define ISNS_FUNC_DDREG          0x0009
#define ISNS_FUNC_DDDEREG        0x000a
#define ISNS_FUNC_DDSREG         0x000b
#define ISNS_FUNC_DDSDEREG       0x000c
#define ISNS_FUNC_ESI            0x000d
#define ISNS_FUNC_HEARTBEAT      0x000e
#define ISNS_FUNC_RQSTDOMID      0x0011
#define ISNS_FUNC_RLSEDOMID      0x0012
#define ISNS_FUNC_GETDOMID       0x0013
#define ISNS_FUNC_RSP_DEVATTRREG 0x8001
#define ISNS_FUNC_RSP_DEVATTRQRY 0x8002
#define ISNS_FUNC_RSP_DEVGETNEXT 0x8003
#define ISNS_FUNC_RSP_DEREGDEV   0x8004
#define ISNS_FUNC_RSP_SCNREG     0x8005
#define ISNS_FUNC_RSP_SCNDEREG   0x8006
#define ISNS_FUNC_RSP_SCNEVENT   0x8007
#define ISNS_FUNC_RSP_SCN        0x8008
#define ISNS_FUNC_RSP_DDREG      0x8009
#define ISNS_FUNC_RSP_DDDEREG    0x800a
#define ISNS_FUNC_RSP_DDSREG     0x800b
#define ISNS_FUNC_RSP_DDSDEREG   0x800c
#define ISNS_FUNC_RSP_ESI        0x800d
#define ISNS_FUNC_RSP_RQSTDOMID  0x8011
#define ISNS_FUNC_RSP_RLSEDOMID  0x8012
#define ISNS_FUNC_RSP_GETDOMID   0x8013
#define ISNS_ENTITY_PROTOCOL_NO_PROTOCOL 1
#define ISNS_ENTITY_PROTOCOL_ISCSI       2
#define ISNS_ENTITY_PROTOCOL_IFCP        3
#define ISNS_ATTR_TAG_DELIMITER                     0
#define ISNS_ATTR_TAG_ENTITY_IDENTIFIER             1
#define ISNS_ATTR_TAG_ENTITY_PROTOCOL               2
#define ISNS_ATTR_TAG_MGMT_IP_ADDRESS               3
#define ISNS_ATTR_TAG_TIMESTAMP                     4
#define ISNS_ATTR_TAG_PROTOCOL_VERSION_RANGE        5
#define ISNS_ATTR_TAG_REGISTRATION_PERIOD           6
#define ISNS_ATTR_TAG_ENTITY_INDEX                  7
#define ISNS_ATTR_TAG_ENTITY_NEXT_INDEX             8
#define ISNS_ATTR_TAG_ENTITY_ISAKMP_PHASE_1         11
#define ISNS_ATTR_TAG_ENTITY_CERTIFICATE            12
#define ISNS_ATTR_TAG_PORTAL_IP_ADDRESS             16
#define ISNS_ATTR_TAG_PORTAL_PORT                   17
#define ISNS_ATTR_TAG_PORTAL_SYMBOLIC_NAME          18
#define ISNS_ATTR_TAG_ESI_INTERVAL                  19
#define ISNS_ATTR_TAG_ESI_PORT                      20
#define ISNS_ATTR_TAG_PORTAL_INDEX                  22
#define ISNS_ATTR_TAG_SCN_PORT                      23
#define ISNS_ATTR_TAG_PORTAL_NEXT_INDEX             24
#define ISNS_ATTR_TAG_PORTAL_SECURITY_BITMAP        27
#define ISNS_ATTR_TAG_PORTAL_ISAKMP_PHASE_1         28
#define ISNS_ATTR_TAG_PORTAL_ISAKMP_PHASE_2         29
#define ISNS_ATTR_TAG_PORTAL_CERTIFICATE            31
#define ISNS_ATTR_TAG_ISCSI_NAME                    32
#define ISNS_ATTR_TAG_ISCSI_NODE_TYPE               33
#define ISNS_ATTR_TAG_ISCSI_ALIAS                   34
#define ISNS_ATTR_TAG_ISCSI_SCN_BITMAP              35
#define ISNS_ATTR_TAG_ISCSI_NODE_INDEX              36
#define ISNS_ATTR_TAG_WWNN_TOKEN                    37
#define ISNS_ATTR_TAG_ISCSI_NODE_NEXT_INDEX         38
#define ISNS_ATTR_TAG_ISCSI_AUTH_METHOD             42
#define ISNS_ATTR_TAG_PG_ISCSI_NAME                 48
#define ISNS_ATTR_TAG_PG_PORTAL_IP_ADDR             49
#define ISNS_ATTR_TAG_PG_PORTAL_PORT                50
#define ISNS_ATTR_TAG_PORTAL_GROUP_TAG              51
#define ISNS_ATTR_TAG_PORTAL_GROUP_INDEX            52
#define ISNS_ATTR_TAG_PORTAL_GROUP_NEXT_INDEX       53
#define ISNS_ATTR_TAG_FC_PORT_NAME_WWPN             64
#define ISNS_ATTR_TAG_PORT_ID                       65
#define ISNS_ATTR_TAG_FC_PORT_TYPE                  66
#define ISNS_ATTR_TAG_SYMBOLIC_PORT_NAME            67
#define ISNS_ATTR_TAG_FABRIC_PORT_NAME              68
#define ISNS_ATTR_TAG_HARD_ADDRESS                  69
#define ISNS_ATTR_TAG_PORT_IP_ADDRESS               70
#define ISNS_ATTR_TAG_CLASS_OF_SERVICE              71
#define ISNS_ATTR_TAG_FC4_TYPES                     72
#define ISNS_ATTR_TAG_FC4_DESCRIPTOR                73
#define ISNS_ATTR_TAG_FC4_FEATURES                  74
#define ISNS_ATTR_TAG_IFCP_SCN_BITMAP               75
#define ISNS_ATTR_TAG_PORT_ROLE                     76
#define ISNS_ATTR_TAG_PERMANENT_PORT_NAME           77
#define ISNS_ATTR_TAG_FC4_TYPE_CODE                 95
#define ISNS_ATTR_TAG_FC_NODE_NAME_WWNN             96
#define ISNS_ATTR_TAG_SYMBOLIC_NODE_NAME            97
#define ISNS_ATTR_TAG_NODE_IP_ADDRESS               98
#define ISNS_ATTR_TAG_NODE_IPA                      99
#define ISNS_ATTR_TAG_PROXY_ISCSI_NAME              101
#define ISNS_ATTR_TAG_SWITCH_NAME                   128
#define ISNS_ATTR_TAG_PREFERRED_ID                  129
#define ISNS_ATTR_TAG_ASSIGNED_ID                   130
#define ISNS_ATTR_TAG_VIRTUAL_FABRIC_ID             131
#define ISNS_ATTR_TAG_VENDOR_OUI                    256
#define ISNS_ATTR_TAG_DD_SET_ID                     2049
#define ISNS_ATTR_TAG_DD_SET_SYMBOLIC_NAME          2050
#define ISNS_ATTR_TAG_DD_SET_STATUS                 2051
#define ISNS_ATTR_TAG_DD_SET_NEXT_ID                2052
#define ISNS_ATTR_TAG_DD_ID                         2065
#define ISNS_ATTR_TAG_DD_SYMBOLIC_NAME              2066
#define ISNS_ATTR_TAG_DD_MEMBER_ISCSI_INDEX         2067
#define ISNS_ATTR_TAG_DD_MEMBER_ISCSI_NAME          2068
#define ISNS_ATTR_TAG_DD_MEMBER_FC_PORT_NAME        2069
#define ISNS_ATTR_TAG_DD_MEMBER_PORTAL_INDEX        2070
#define ISNS_ATTR_TAG_DD_MEMBER_PORTAL_IP_ADDRESS   2071
#define ISNS_ATTR_TAG_DD_MEMBER_PORTAL_PORT         2072
#define ISNS_ATTR_TAG_DD_FEATURES                   2078
#define ISNS_ATTR_TAG_DD_ID_NEXT_ID                 2079
#define ISNS_FLAGS_CLIENT	0x8000
#define ISNS_FLAGS_SERVER	0x4000
#define ISNS_FLAGS_AUTH		0x2000
#define ISNS_FLAGS_REPLACE	0x1000
#define ISNS_FLAGS_LAST_PDU	0x0800
#define ISNS_FLAGS_FIRST_PDU	0x0400
#define IUA_PAYLOAD_PROTOCOL_ID                         1
#define KDP_PORT 19948
#define KDP_DROP_FLAG (1 << 0)
#define KDP_SYN_FLAG  (1 << 1)
#define KDP_ACK_FLAG  (1 << 2)
#define KDP_RST_FLAG  (1 << 3)
#define KDP_BCST_FLAG (1 << 4)
#define KDP_DUP_FLAG  (1 << 5)
#define KINK_PORT       57203
#define KINK_ISAKMP_PAYLOAD_BASE 14
#define KINK_TYPE_RESERVED 0
#define KINK_TYPE_CREATE   1
#define KINK_TYPE_DELETE   2
#define KINK_TYPE_REPLY    3
#define KINK_TYPE_GETTGT   4
#define KINK_TYPE_ACK      5
#define KINK_TYPE_STATUS   6
#define KINK_A_NOT_REQUEST_ACK  0
#define KINK_A_REQUEST_ACK      1
#define KINK_DONE                                0
#define KINK_AP_REQ     KINK_ISAKMP_PAYLOAD_BASE+0
#define KINK_AP_REP     KINK_ISAKMP_PAYLOAD_BASE+1
#define KINK_KRB_ERROR  KINK_ISAKMP_PAYLOAD_BASE+2
#define KINK_TGT_REQ    KINK_ISAKMP_PAYLOAD_BASE+3
#define KINK_TGT_REP    KINK_ISAKMP_PAYLOAD_BASE+4
#define KINK_ISAKMP     KINK_ISAKMP_PAYLOAD_BASE+5
#define KINK_ENCRYPT    KINK_ISAKMP_PAYLOAD_BASE+6
#define KINK_ERROR      KINK_ISAKMP_PAYLOAD_BASE+7
#define KINK_OK                   0
#define KINK_PROTOERR             1
#define KINK_INVDOI               2
#define KINK_INVMAJ               3
#define KINK_INVMIN               4
#define KINK_INTERR               5
#define KINK_BADQMVERS            6
#define KINK_KRB_ERROR_HEADER     4
#define KINK_TGT_REQ_HEADER       6
#define KINK_ERROR_LENGTH         8
#define	LAPD_SAPI		0xfc00	/* Service Access Point Identifier */
#define	LAPD_SAPI_SHIFT		10
#define	LAPD_CR			0x0200	/* Command/Response bit */
#define	LAPD_EA1		0x0100	/* First Address Extension bit */
#define	LAPD_TEI		0x00fe	/* Terminal Endpoint Identifier */
#define	LAPD_TEI_SHIFT		1
#define	LAPD_EA2		0x0001	/* Second Address Extension bit */
#define	LAPD_SAPI_Q931		0	/* Q.931 call control procedure */
#define	LAPD_SAPI_PM_Q931	1	/* Packet mode Q.931 call control procedure */
#define	LAPD_SAPI_X25		16	/* X.25 Level 3 procedures */
#define	LAPD_SAPI_L2		63	/* Layer 2 management procedures */
#define LAPD_GSM_SAPI_RA_SIG_PROC	0
#define LAPD_GSM_SAPI_NOT_USED_1	1
#define LAPD_GSM_SAPI_NOT_USED_16	16
#define LAPD_GSM_SAPI_OM_PROC		62
#define LCS_AP_PAYLOAD_PROTOCOL_ID                     29
#define LINUX_AF_INET		2
#define LINUX_AF_INET6		10
#define LINUX_EAGAIN            11
#define LINUX_ENOMEM            12
#define LINUX_INVAL             22
#define LINUX_SLL_HOST		0
#define LINUX_SLL_BROADCAST	1
#define LINUX_SLL_MULTICAST	2
#define LINUX_SLL_OTHERHOST	3
#define LINUX_SLL_OUTGOING	4
#define LINUX_SLL_P_802_3	0x0001	/* Novell 802.3 frames without 802.2 LLC header */
#define LINUX_SLL_P_ETHERNET	0x0003	/* Ethernet */
#define LINUX_SLL_P_802_2	0x0004	/* 802.2 frames (not D/I/X Ethernet) */
#define LINUX_SLL_P_PPPHDLC	0x0007	/* PPP HDLC frames */
#define LINUX_SLL_P_CAN		0x000C	/* Controller Area Network */
#define LINUX_SLL_P_IRDA_LAP	0x0017	/* IrDA Link Access Protocol */
#define LINUX_SLL_P_IEEE802154	0x00f6	/* 802.15.4 on monitor inteface */
#define LINUX_CAN_STD   0
#define LINUX_CAN_EXT   1
#define LINUX_CAN_RTR   2
#define LINUX_CAN_ERR   3
#define LISP_CONTROL_PORT       4342
#define LISP_DATA_PORT          4341
#define LISP_DATA_HEADER_LEN    8       /* Number of bytes in LISP data header */
#define LISP_DATA_FLAGS_WIDTH   8       /* Width (in bits) of the flags field */
#define LISP_DATA_FLAG_N        0x80    /* Nonce present */
#define LISP_DATA_FLAG_L        0x40    /* Locator-Status-Bits field enabled */
#define LISP_DATA_FLAG_E        0x20    /* Echo-Nonce-Request */
#define LISP_DATA_FLAG_V        0x10    /* Map-Version present */
#define LISP_DATA_FLAG_I        0x08    /* Instance ID present */
#define LISP_DATA_FLAG_RES      0x07    /* Reserved */
#define LISP_MAP_REQUEST    1
#define LISP_MAP_REPLY      2
#define LISP_MAP_REGISTER   3
#define LISP_MAP_NOTIFY     4
#define LISP_MAP_REFERRAL   6
#define LISP_INFO           7
#define LISP_ECM            8
#define LISP_ACT_NONE       0
#define LISP_ACT_FWD_NATIVE 1
#define LISP_ACT_MREQ       2
#define LISP_ACT_DROP       3
#define LISP_ECM_HEADER_LEN 4
#define LISP_XTRID_LEN      16
#define LISP_SITEID_LEN     8
#define LISP_MAP_ACT        0xE000
#define LISP_MAP_AUTH       0x1000
#define LLRP_PORT 5084
#define LLRP_TYPE_GET_READER_CuapABILITIES           1
#define LLRP_TYPE_GET_READER_CONFIG                 2
#define LLRP_TYPE_SET_READER_CONFIG                 3
#define LLRP_TYPE_CLOSE_CONNECTION_RESPONSE         4
#define LLRP_TYPE_GET_READER_CuapABILITIES_RESPONSE 11
#define LLRP_TYPE_GET_READER_CONFIG_RESPONSE       12
#define LLRP_TYPE_SET_READER_CONFIG_RESPONSE       13
#define LLRP_TYPE_CLOSE_CONNECTION                 14
#define LLRP_TYPE_ADD_ROSPEC                       20
#define LLRP_TYPE_DELETE_ROSPEC                    21
#define LLRP_TYPE_START_ROSPEC                     22
#define LLRP_TYPE_STOP_ROSPEC                      23
#define LLRP_TYPE_ENABLE_ROSPEC                    24
#define LLRP_TYPE_DISABLE_ROSPEC                   25
#define LLRP_TYPE_GET_ROSPECS                      26
#define LLRP_TYPE_ADD_ROSPEC_RESPONSE              30
#define LLRP_TYPE_DELETE_ROSPEC_RESPONSE           31
#define LLRP_TYPE_START_ROSPEC_RESPONSE            32
#define LLRP_TYPE_STOP_ROSPEC_RESPONSE             33
#define LLRP_TYPE_ENABLE_ROSPEC_RESPONSE           34
#define LLRP_TYPE_DISABLE_ROSPEC_RESPONSE          35
#define LLRP_TYPE_GET_ROSPECS_RESPONSE             36
#define LLRP_TYPE_ADD_ACCESSSPEC                   40
#define LLRP_TYPE_DELETE_ACCESSSPEC                41
#define LLRP_TYPE_ENABLE_ACCESSSPEC                42
#define LLRP_TYPE_DISABLE_ACCESSSPEC               43
#define LLRP_TYPE_GET_ACCESSSPECS                  44
#define LLRP_TYPE_CLIENT_REQUEST_OP                45
#define LLRP_TYPE_GET_SUPPORTED_VERSION            46
#define LLRP_TYPE_SET_PROTOCOL_VERSION             47
#define LLRP_TYPE_ADD_ACCESSSPEC_RESPONSE          50
#define LLRP_TYPE_DELETE_ACCESSSPEC_RESPONSE       51
#define LLRP_TYPE_ENABLE_ACCESSSPEC_RESPONSE       52
#define LLRP_TYPE_DISABLE_ACCESSSPEC_RESPONSE      53
#define LLRP_TYPE_GET_ACCESSSPECS_RESPONSE         54
#define LLRP_TYPE_CLIENT_RESQUEST_OP_RESPONSE      55
#define LLRP_TYPE_GET_SUPPORTED_VERSION_RESPONSE   56
#define LLRP_TYPE_SET_PROTOCOL_VERSION_RESPONSE    57
#define LLRP_TYPE_GET_REPORT                       60
#define LLRP_TYPE_RO_ACCESS_REPORT                 61
#define LLRP_TYPE_KEEPALIVE                        62
#define LLRP_TYPE_READER_EVENT_NOTIFICATION        63
#define LLRP_TYPE_ENABLE_EVENTS_AND_REPORTS        64
#define LLRP_TYPE_KEEPALIVE_ACK                    72
#define LLRP_TYPE_ERROR_MESSAGE                   100
#define LLRP_TYPE_CUSTOM_MESSAGE                 1023
#define LLRP_VERS_1_0_1 0x01
#define LLRP_VERS_1_1   0x02
#define LLRP_Cuap_ALL            0
#define LLRP_Cuap_GENERAL_DEVICE 1
#define LLRP_Cuap_LLRP           2
#define LLRP_Cuap_REGULATORY     3
#define LLRP_Cuap_AIR_PROTOCOL   4
#define LLRP_CONF_ALL                             0
#define LLRP_CONF_IDENTIFICATION                  1
#define LLRP_CONF_ANTENNA_PROPERTIES              2
#define LLRP_CONF_ANTENNA_CONFIGURATION           3
#define LLRP_CONF_RO_REPORT_SPEC                  4
#define LLRP_CONF_READER_EVENT_NOTIFICATION_SPEC  5
#define LLRP_CONF_ACCESS_REPORT_SPEC              6
#define LLRP_CONF_LLRP_CONFIGURATION_STATE        7
#define LLRP_CONF_KEEPALIVE_SPEC                  8
#define LLRP_CONF_GPI_PORT_CURRENT_STATE          9
#define LLRP_CONF_GPO_WRITE_DATA                 10
#define LLRP_CONF_EVENTS_AND_REPORTS             11
#define LLRP_TLV_UTC_TIMESTAMP           128
#define LLRP_TLV_UPTIME                  129
#define LLRP_TLV_GENERAL_DEVICE_Cuap      137
#define LLRP_TLV_RECEIVE_SENSE_ENTRY     139
#define LLRP_TLV_ANTENNA_AIR_PROTO       140
#define LLRP_TLV_GPIO_CuapABILITIES       141
#define LLRP_TLV_LLRP_CuapABILITIES       142
#define LLRP_TLV_REGU_CuapABILITIES       143
#define LLRP_TLV_UHF_CuapABILITIES        144
#define LLRP_TLV_XMIT_POWER_LEVEL_ENTRY  145
#define LLRP_TLV_FREQ_INFORMATION        146
#define LLRP_TLV_FREQ_HOP_TABLE          147
#define LLRP_TLV_FIXED_FREQ_TABLE        148
#define LLRP_TLV_ANTENNA_RCV_SENSE_RANGE 149
#define LLRP_TLV_RO_SPEC                 177
#define LLRP_TLV_RO_BOUND_SPEC           178
#define LLRP_TLV_RO_SPEC_START_TRIGGER   179
#define LLRP_TLV_PER_TRIGGER_VAL         180
#define LLRP_TLV_GPI_TRIGGER_VAL         181
#define LLRP_TLV_RO_SPEC_STOP_TRIGGER    182
#define LLRP_TLV_AI_SPEC                 183
#define LLRP_TLV_AI_SPEC_STOP            184
#define LLRP_TLV_TAG_OBSERV_TRIGGER      185
#define LLRP_TLV_INVENTORY_PARAM_SPEC    186
#define LLRP_TLV_RF_SURVEY_SPEC          187
#define LLRP_TLV_RF_SURVEY_SPEC_STOP_TR  188
#define LLRP_TLV_ACCESS_SPEC             207
#define LLRP_TLV_ACCESS_SPEC_STOP_TRIG   208
#define LLRP_TLV_ACCESS_COMMAND          209
#define LLRP_TLV_CLIENT_REQ_OP_SPEC      210
#define LLRP_TLV_CLIENT_REQ_RESPONSE     211
#define LLRP_TLV_LLRP_CONF_STATE_VAL     217
#define LLRP_TLV_IDENT                   218
#define LLRP_TLV_GPO_WRITE_DATA          219
#define LLRP_TLV_KEEPALIVE_SPEC          220
#define LLRP_TLV_ANTENNA_PROPS           221
#define LLRP_TLV_ANTENNA_CONF            222
#define LLRP_TLV_RF_RECEIVER             223
#define LLRP_TLV_RF_TRANSMITTER          224
#define LLRP_TLV_GPI_PORT_CURRENT_STATE  225
#define LLRP_TLV_EVENTS_AND_REPORTS      226
#define LLRP_TLV_RO_REPORT_SPEC          237
#define LLRP_TLV_TAG_REPORT_CONTENT_SEL  238
#define LLRP_TLV_ACCESS_REPORT_SPEC      239
#define LLRP_TLV_TAG_REPORT_DATA         240
#define LLRP_TLV_EPC_DATA                241
#define LLRP_TLV_RF_SURVEY_REPORT_DATA   242
#define LLRP_TLV_FREQ_RSSI_LEVEL_ENTRY   243
#define LLRP_TLV_READER_EVENT_NOTI_SPEC  244
#define LLRP_TLV_EVENT_NOTIF_STATE       245
#define LLRP_TLV_READER_EVENT_NOTI_DATA  246
#define LLRP_TLV_HOPPING_EVENT           247
#define LLRP_TLV_GPI_EVENT               248
#define LLRP_TLV_RO_SPEC_EVENT           249
#define LLRP_TLV_REPORT_BUF_LEVEL_WARN   250
#define LLRP_TLV_REPORT_BUF_OVERFLOW_ERR 251
#define LLRP_TLV_READER_EXCEPTION_EVENT  252
#define LLRP_TLV_RF_SURVEY_EVENT         253
#define LLRP_TLV_AI_SPEC_EVENT           254
#define LLRP_TLV_ANTENNA_EVENT           255
#define LLRP_TLV_CONN_ATTEMPT_EVENT      256
#define LLRP_TLV_CONN_CLOSE_EVENT        257
#define LLRP_TLV_LLRP_STATUS             287
#define LLRP_TLV_FIELD_ERROR             288
#define LLRP_TLV_PARAM_ERROR             289
#define LLRP_TLV_C1G2_LLRP_Cuap           327
#define LLRP_TLV_C1G2_UHF_RF_MD_TBL      328
#define LLRP_TLV_C1G2_UHF_RF_MD_TBL_ENT  329
#define LLRP_TLV_C1G2_INVENTORY_COMMAND  330
#define LLRP_TLV_C1G2_FILTER             331
#define LLRP_TLV_C1G2_TAG_INV_MASK       332
#define LLRP_TLV_C1G2_TAG_INV_AWARE_FLTR 333
#define LLRP_TLV_C1G2_TAG_INV_UNAWR_FLTR 334
#define LLRP_TLV_C1G2_RF_CONTROL         335
#define LLRP_TLV_C1G2_SINGULATION_CTRL   336
#define LLRP_TLV_C1G2_TAG_INV_AWARE_SING 337
#define LLRP_TLV_C1G2_TAG_SPEC           338
#define LLRP_TLV_C1G2_TARGET_TAG         339
#define LLRP_TLV_C1G2_READ               341
#define LLRP_TLV_C1G2_WRITE              342
#define LLRP_TLV_C1G2_KILL               343
#define LLRP_TLV_C1G2_LOCK               344
#define LLRP_TLV_C1G2_LOCK_PAYLOAD       345
#define LLRP_TLV_C1G2_BLK_ERASE          346
#define LLRP_TLV_C1G2_BLK_WRITE          347
#define LLRP_TLV_C1G2_EPC_MEMORY_SLCTOR  348
#define LLRP_TLV_C1G2_READ_OP_SPEC_RES   349
#define LLRP_TLV_C1G2_WRT_OP_SPEC_RES    350
#define LLRP_TLV_C1G2_KILL_OP_SPEC_RES   351
#define LLRP_TLV_C1G2_LOCK_OP_SPEC_RES   352
#define LLRP_TLV_C1G2_BLK_ERS_OP_SPC_RES 353
#define LLRP_TLV_C1G2_BLK_WRT_OP_SPC_RES 354
#define LLRP_TLV_LOOP_SPEC               355
#define LLRP_TLV_SPEC_LOOP_EVENT         356
#define LLRP_TLV_C1G2_RECOMMISSION       357
#define LLRP_TLV_C1G2_BLK_PERMALOCK      358
#define LLRP_TLV_C1G2_GET_BLK_PERMALOCK  359
#define LLRP_TLV_C1G2_RECOM_OP_SPEC_RES  360
#define LLRP_TLV_C1G2_BLK_PRL_OP_SPC_RES 361
#define LLRP_TLV_C1G2_BLK_PRL_STAT_RES   362
#define LLRP_TLV_MAX_RECEIVE_SENSE       363
#define LLRP_TLV_RF_SURVEY_FREQ_Cuap      365
#define LLRP_TLV_CUSTOM_PARAMETER       1023
#define LLRP_TV_ANTENNA_ID               1
#define LLRP_TV_FIRST_SEEN_TIME_UTC      2
#define LLRP_TV_FIRST_SEEN_TIME_UPTIME   3
#define LLRP_TV_LAST_SEEN_TIME_UTC       4
#define LLRP_TV_LAST_SEEN_TIME_UPTIME    5
#define LLRP_TV_PEAK_RSSI                6
#define LLRP_TV_CHANNEL_INDEX            7
#define LLRP_TV_TAG_SEEN_COUNT           8
#define LLRP_TV_RO_SPEC_ID               9
#define LLRP_TV_INVENTORY_PARAM_SPEC_ID 10
#define LLRP_TV_C1G2_CRC                11
#define LLRP_TV_C1G2_PC                 12
#define LLRP_TV_EPC96                   13
#define LLRP_TV_SPEC_INDEX              14
#define LLRP_TV_CLIENT_REQ_OP_SPEC_RES  15
#define LLRP_TV_ACCESS_SPEC_ID          16
#define LLRP_TV_OP_SPEC_ID              17
#define LLRP_TV_C1G2_SINGULATION_DET    18
#define LLRP_TV_C1G2_XPC_W1             19
#define LLRP_TV_C1G2_XPC_W2             20
#define LLRP_TV_LEN_ANTENNA_ID               2
#define LLRP_TV_LEN_FIRST_SEEN_TIME_UTC      8
#define LLRP_TV_LEN_FIRST_SEEN_TIME_UPTIME   8
#define LLRP_TV_LEN_LAST_SEEN_TIME_UTC       8
#define LLRP_TV_LEN_LAST_SEEN_TIME_UPTIME    8
#define LLRP_TV_LEN_PEAK_RSSI                1
#define LLRP_TV_LEN_CHANNEL_INDEX            2
#define LLRP_TV_LEN_TAG_SEEN_COUNT           2
#define LLRP_TV_LEN_RO_SPEC_ID               4
#define LLRP_TV_LEN_INVENTORY_PARAM_SPEC_ID  2
#define LLRP_TV_LEN_C1G2_CRC                 2
#define LLRP_TV_LEN_C1G2_PC                  2
#define LLRP_TV_LEN_EPC96                   12
#define LLRP_TV_LEN_SPEC_INDEX               2
#define LLRP_TV_LEN_CLIENT_REQ_OP_SPEC_RES   2
#define LLRP_TV_LEN_ACCESS_SPEC_ID           4
#define LLRP_TV_LEN_OP_SPEC_ID               2
#define LLRP_TV_LEN_C1G2_SINGULATION_DET     4
#define LLRP_TV_LEN_C1G2_XPC_W1              2
#define LLRP_TV_LEN_C1G2_XPC_W2              2
#define LLRP_PROT_ID_UNSPECIFIED    0
#define LLRP_PROT_ID_EPC_C1G2       1
#define LLRP_COMM_STANDARD_UNSPECIFIED              0
#define LLRP_COMM_STANDARD_US_FCC_PART_15           1
#define LLRP_COMM_STANDARD_ETSI_302_208             2
#define LLRP_COMM_STANDARD_ETSI_300_220             3
#define LLRP_COMM_STANDARD_AUSTRALIA_LIPD_1W        4
#define LLRP_COMM_STANDARD_AUSTRALIA_LIPD_4W        5
#define LLRP_COMM_STANDARD_JAPAN_ARIB_STD_T89       6
#define LLRP_COMM_STANDARD_HONG_KONG_OFTA_1049      7
#define LLRP_COMM_STANDARD_TAIWAN_DGT_LP0002        8
#define LLRP_COMM_STANDARD_KOREA_MIC_ARTICLE_5_2    9
#define LLRP_ID_TYPE_MAC    0
#define LLRP_ID_TYPE_EPC    1
#define LLRP_KEEPALIVE_TYPE_NULL        0
#define LLRP_KEEPALIVE_TYPE_PERIODIC    1
#define LLRP_NOTIFICATION_EVENT_TYPE_UPON_HOPPING_TO_NEXT_CHANNEL     0
#define LLRP_NOTIFICATION_EVENT_TYPE_GPI_EVENT                        1
#define LLRP_NOTIFICATION_EVENT_TYPE_ROSPEC_EVENT                     2
#define LLRP_NOTIFICATION_EVENT_TYPE_REPORT_BUFFER_FILL_WARNING       3
#define LLRP_NOTIFICATION_EVENT_TYPE_READER_EXCEPTION_EVENT           4
#define LLRP_NOTIFICATION_EVENT_TYPE_RFSURVEY_EVENT                   5
#define LLRP_NOTIFICATION_EVENT_TYPE_AISPEC_EVENT                     6
#define LLRP_NOTIFICATION_EVENT_TYPE_AISPEC_EVENT_WITH_DETAILS        7
#define LLRP_NOTIFICATION_EVENT_TYPE_ANTENNA_EVENT                    8
#define LLRP_NOTIFICATION_EVENT_TYPE_SPEC_LOOP_EVENT                  9
#define LLRP_ROSPEC_EVENT_TYPE_START_OF_ROSPEC          0
#define LLRP_ROSPEC_EVENT_TYPE_END_OF_ROSPEC            1
#define LLRP_ROSPEC_EVENT_TYPE_PREEMPTION_OF_ROSPEC     2
#define LLRP_RF_SURVEY_EVENT_TYPE_START_OF_SURVEY     0
#define LLRP_RF_SURVEY_EVENT_TYPE_END_OF_SURVEY       1
#define LLRP_AISPEC_EVENT_TYPE_END_OF_AISPEC    0
#define LLRP_ANTENNA_EVENT_DISCONNECTED      0
#define LLRP_ANTENNA_EVENT_CONNECTED         1
#define LLRP_CONNECTION_SUCCESS                                     0
#define LLRP_CONNECTION_FAILED_READER_INITIATE_ALREADY_EXISTS       1
#define LLRP_CONNECTION_FAILED_CLIENT_INITIATE_ALREADY_EXISTS       2
#define LLRP_CONNECTION_FAILED_OTHER_REASON_THAN_ALREADY_EXISTS     3
#define LLRP_CONNECTION_ANOTHER_CONNECTION_ATTEMPTED                4
#define LLRP_STATUS_CODE_M_SUCCESS                0
#define LLRP_STATUS_CODE_M_PARAMETERERROR       100
#define LLRP_STATUS_CODE_M_FIELDERROR           101
#define LLRP_STATUS_CODE_M_UNEXPECTEDPARAMETER  102
#define LLRP_STATUS_CODE_M_MISSINGPARAMETER     103
#define LLRP_STATUS_CODE_M_DUPLICATEPARAMETER   104
#define LLRP_STATUS_CODE_M_OVERFLOWPARAMETER    105
#define LLRP_STATUS_CODE_M_OVERFLOWFIELD        106
#define LLRP_STATUS_CODE_M_UNKNOWNPARAMETER     107
#define LLRP_STATUS_CODE_M_UNKNOWNFIELD         108
#define LLRP_STATUS_CODE_M_UNSUPPORTEDMESSAGE   109
#define LLRP_STATUS_CODE_M_UNSUPPORTEDVERSION   110
#define LLRP_STATUS_CODE_M_UNSUPPORTEDPARAMETER 111
#define LLRP_STATUS_CODE_P_PARAMETERERROR       200
#define LLRP_STATUS_CODE_P_FIELDERROR           201
#define LLRP_STATUS_CODE_P_UNEXPECTEDPARAMETER  202
#define LLRP_STATUS_CODE_P_MISSINGPARAMETER     203
#define LLRP_STATUS_CODE_P_DUPLICATEPARAMETER   204
#define LLRP_STATUS_CODE_P_OVERFLOWPARAMETER    205
#define LLRP_STATUS_CODE_P_OVERFLOWFIELD        206
#define LLRP_STATUS_CODE_P_UNKNOWNPARAMETER     207
#define LLRP_STATUS_CODE_P_UNKNOWNFIELD         208
#define LLRP_STATUS_CODE_P_UNSUPPORTEDPARAMETER 209
#define LLRP_STATUS_CODE_A_INVALID              300
#define LLRP_STATUS_CODE_A_OUTOFRANGE           301
#define LLRP_STATUS_CODE_R_DEVICEERROR          401
#define LLRP_VENDOR_IMPINJ 25882
#define LLRP_IMPINJ_TYPE_ENABLE_EXTENSIONS            21
#define LLRP_IMPINJ_TYPE_ENABLE_EXTENSIONS_RESPONSE   22
#define LLRP_IMPINJ_TYPE_SAVE_SETTINGS                23
#define LLRP_IMPINJ_TYPE_SAVE_SETTINGS_RESPONSE       24
#define LLRP_IMPINJ_PARAM_REQUESTED_DATA                           21
#define LLRP_IMPINJ_PARAM_SUBREGULATORY_REGION                     22
#define LLRP_IMPINJ_PARAM_INVENTORY_SEARCH_MODE                    23
#define LLRP_IMPINJ_PARAM_TAG_DIRECTION_REPORTING                  24
#define LLRP_IMPINJ_PARAM_TAG_DIRECTION                            25
#define LLRP_IMPINJ_PARAM_FIXED_FREQUENCY_LIST                     26
#define LLRP_IMPINJ_PARAM_REDUCED_POWER_FREQUENCY_LIST             27
#define LLRP_IMPINJ_PARAM_LOW_DUTY_CYCLE                           28
#define LLRP_IMPINJ_PARAM_DETAILED_VERSION                         29
#define LLRP_IMPINJ_PARAM_FREQUENCY_CuapABILITIES                   30
#define LLRP_IMPINJ_PARAM_TAG_INFORMATION                          31
#define LLRP_IMPINJ_PARAM_FORKLIFT_CONFIGURATION                   32
#define LLRP_IMPINJ_PARAM_FORKLIFT_HEIGHT_THRESHOLD                33
#define LLRP_IMPINJ_PARAM_FORKLIFT_ZEROMOTION_TIME_THRESHOLD       34
#define LLRP_IMPINJ_PARAM_FORKLIFT_COMPANION_BOARD_INFO            35
#define LLRP_IMPINJ_PARAM_GPI_DEBOUNCE_CONFIGURATION               36
#define LLRP_IMPINJ_PARAM_READER_TEMPERATURE                       37
#define LLRP_IMPINJ_PARAM_LINK_MONITOR_CONFIGURATION               38
#define LLRP_IMPINJ_PARAM_REPORT_BUFFER_CONFIGURATION              39
#define LLRP_IMPINJ_PARAM_ACCESS_SPEC_CONFIGURATION                40
#define LLRP_IMPINJ_PARAM_BLOCK_WRITE_WORD_COUNT                   41
#define LLRP_IMPINJ_PARAM_BLOCK_PERMALOCK                          42
#define LLRP_IMPINJ_PARAM_BLOCK_PERMALOCK_OPSPEC_RESULT            43
#define LLRP_IMPINJ_PARAM_GET_BLOCK_PERMALOCK_STATUS               44
#define LLRP_IMPINJ_PARAM_GET_BLOCK_PERMALOCK_STATUS_OPSPEC_RESULT 45
#define LLRP_IMPINJ_PARAM_SET_QT_CONFIG                            46
#define LLRP_IMPINJ_PARAM_SET_QT_CONFIG_OPSPEC_RESULT              47
#define LLRP_IMPINJ_PARAM_GET_QT_CONFIG                            48
#define LLRP_IMPINJ_PARAM_GET_QT_CONFIG_OPSPEC_RESULT              49
#define LLRP_IMPINJ_PARAM_TAG_REPORT_CONTENT_SELECTOR              50
#define LLRP_IMPINJ_PARAM_ENABLE_SERIALIZED_TID                    51
#define LLRP_IMPINJ_PARAM_ENABLE_RF_PHASE_ANGLE                    52
#define LLRP_IMPINJ_PARAM_ENABLE_PEAK_RSSI                         53
#define LLRP_IMPINJ_PARAM_ENABLE_GPS_COORDINATES                   54
#define LLRP_IMPINJ_PARAM_SERIALIZED_TID                           55
#define LLRP_IMPINJ_PARAM_RF_PHASE_ANGLE                           56
#define LLRP_IMPINJ_PARAM_PEAK_RSSI                                57
#define LLRP_IMPINJ_PARAM_GPS_COORDINATES                          58
#define LLRP_IMPINJ_PARAM_LOOP_SPEC                                59
#define LLRP_IMPINJ_PARAM_GPS_NMEA_SENTENCES                       60
#define LLRP_IMPINJ_PARAM_GGA_SENTENCE                             61
#define LLRP_IMPINJ_PARAM_RMC_SENTENCE                             62
#define LLRP_IMPINJ_PARAM_OPSPEC_RETRY_COUNT                       63
#define LLRP_IMPINJ_PARAM_ADVANCE_GPO_CONFIG                       64
#define LLRP_IMPINJ_PARAM_ENABLE_OPTIM_READ                        65
#define LLRP_IMPINJ_PARAM_ACCESS_SPEC_ORDERING                     66
#define LLRP_IMPINJ_PARAM_ENABLE_RF_DOPPLER_FREQ                   67
#define LLRP_IMPINJ_REQ_DATA_ALL_CuapABILITIES                1000
#define LLRP_IMPINJ_REQ_DATA_DETAILED_VERSION                1001
#define LLRP_IMPINJ_REQ_DATA_FREQUENCY_CuapABILITIES          1002
#define LLRP_IMPINJ_REQ_DATA_CONFIGURATION                   2000
#define LLRP_IMPINJ_REQ_DATA_SUB_REGULATORY_REGION           2001
#define LLRP_IMPINJ_REQ_DATA_FORKLIFT_CONFIGURATION          2002
#define LLRP_IMPINJ_REQ_DATA_GPI_DEBOUNCE_CONFIGURATION      2003
#define LLRP_IMPINJ_REQ_DATA_READER_TEMPERATURE              2004
#define LLRP_IMPINJ_REQ_DATA_LINK_MONITOR_CONFIGURATION      2005
#define LLRP_IMPINJ_REQ_DATA_REPORT_BUFFER_CONFIGURATION     2006
#define LLRP_IMPINJ_REQ_DATA_ACCESS_SPEC_CONFIGURATION       2007
#define LLRP_IMPINJ_REQ_DATA_GPS_NMEA_SENTENCES              2008
#define LLRP_IMPINJ_REG_REGION_FCC_PART_15_247                   0
#define LLRP_IMPINJ_REG_REGION_ETSI_EN_300_220                   1
#define LLRP_IMPINJ_REG_REGION_ETSI_EN_302_208_WITH_LBT          2
#define LLRP_IMPINJ_REG_REGION_HONG_KONG_920_925_MHZ             3
#define LLRP_IMPINJ_REG_REGION_TAIWAN_922_928_MHZ                4
#define LLRP_IMPINJ_REG_REGION_JAPAN_952_954_MHZ                 5
#define LLRP_IMPINJ_REG_REGION_JAPAN_952_954_MHZ_LOW_POWER       6
#define LLRP_IMPINJ_REG_REGION_ETSI_EN_302_208_V1_2_1            7
#define LLRP_IMPINJ_REG_REGION_KOREA_910_914_MHZ                 8
#define LLRP_IMPINJ_REG_REGION_MALAYSIA_919_923_MHZ              9
#define LLRP_IMPINJ_REG_REGION_CHINA_920_925_MHZ                10
#define LLRP_IMPINJ_REG_REGION_JAPAN_952_954_MHZ_WITHOUT_LBT    11
#define LLRP_IMPINJ_REG_REGION_SOUTH_AFRICA_915_919_MHZ         12
#define LLRP_IMPINJ_REG_REGION_BRAZIL_902_907_AND_915_928_MHZ   13
#define LLRP_IMPINJ_REG_REGION_THAILAND_920_925_MHZ             14
#define LLRP_IMPINJ_REG_REGION_SINGAPORE_920_925_MHZ            15
#define LLRP_IMPINJ_REG_REGION_AUSTRALIA_920_926_MHZ            16
#define LLRP_IMPINJ_REG_REGION_INDIA_865_867_MHZ                17
#define LLRP_IMPINJ_REG_REGION_URUGUAY_916_928_MHZ              18
#define LLRP_IMPINJ_REG_REGION_VIETNAM_920_925_MHZ              19
#define LLRP_IMPINJ_REG_REGION_ISRAEL_915_917_MHZ               20
#define LLRP_IMPINJ_SEARCH_TYPE_READER_SELECTED         0
#define LLRP_IMPINJ_SEARCH_TYPE_SINGLE_TARGET           1
#define LLRP_IMPINJ_SEARCH_TYPE_DUAL_TARGET             2
#define LLRP_IMPINJ_SEARCH_TYPE_SINGLE_TARGET_WITH_SUPP 3
#define LLRP_IMPINJ_ANT_CONF_DUAL   1
#define LLRP_IMPINJ_ANT_CONF_QUAD   2
#define LLRP_IMPINJ_TAG_DIR_INDETERMINED  0
#define LLRP_IMPINJ_TAG_DIR_FROM_2_TO_1   1
#define LLRP_IMPINJ_TAG_DIR_FROM_1_TO_2   2
#define LLRP_IMPINJ_FIX_FREQ_MODE_DISABLED      0
#define LLRP_IMPINJ_FIX_FREQ_MODE_AUTO_SELECT   1
#define LLRP_IMPINJ_FIX_FREQ_MODE_CHANNEL_LIST  2
#define LLRP_IMPINJ_BOOLEAN_DISABLED      0
#define LLRP_IMPINJ_BOOLEAN_ENABLED       1
#define LLRP_IMPINJ_REPORT_BUFF_MODE_NORMAL      0
#define LLRP_IMPINJ_REPORT_BUFF_MODE_LOW_LATENCY 1
#define LLRP_IMPINJ_PERMALOCK_SUCCESS                      0
#define LLRP_IMPINJ_PERMALOCK_INSUFFICIENT_POWER           1
#define LLRP_IMPINJ_PERMALOCK_NONSPECIFIC_TAG_ERROR        2
#define LLRP_IMPINJ_PERMALOCK_NO_RESPONSE_FROM_TAG         3
#define LLRP_IMPINJ_PERMALOCK_NONSPECIFIC_READER_ERROR     4
#define LLRP_IMPINJ_PERMALOCK_INCORRECT_PASSWORD_ERROR     5
#define LLRP_IMPINJ_PERMALOCK_TAG_MEMORY_OVERRUN_ERROR     6
#define LLRP_IMPINJ_BLOCK_PERMALOCK_SUCCESS                      0
#define LLRP_IMPINJ_BLOCK_PERMALOCK_NONSPECIFIC_TAG_ERROR        1
#define LLRP_IMPINJ_BLOCK_PERMALOCK_NO_RESPONSE_FROM_TAG         2
#define LLRP_IMPINJ_BLOCK_PERMALOCK_NONSPECIFIC_READER_ERROR     3
#define LLRP_IMPINJ_BLOCK_PERMALOCK_INCORRECT_PASSWORD_ERROR     4
#define LLRP_IMPINJ_BLOCK_PERMALOCK_TAG_MEMORY_OVERRUN_ERROR     5
#define LLRP_IMPINJ_DATA_PROFILE_UNKNOWN        0
#define LLRP_IMPINJ_DATA_PROFILE_PRIVATE        1
#define LLRP_IMPINJ_DATA_PROFILE_PUBLIC         2
#define LLRP_IMPINJ_ACCESS_RANGE_UNKNOWN        0
#define LLRP_IMPINJ_ACCESS_RANGE_NORMAL_RANGE   1
#define LLRP_IMPINJ_ACCESS_RANGE_SHORT_RANGE    2
#define LLRP_IMPINJ_PERSISTENCE_UNKNOWN     0
#define LLRP_IMPINJ_PERSISTENCE_TEMPORARY   1
#define LLRP_IMPINJ_PERSISTENCE_PERMANENT   2
#define LLRP_IMPINJ_SET_QT_CONFIG_SUCCESS                      0
#define LLRP_IMPINJ_SET_QT_CONFIG_INSUFFICIENT_POWER           1
#define LLRP_IMPINJ_SET_QT_CONFIG_NONSPECIFIC_TAG_ERROR        2
#define LLRP_IMPINJ_SET_QT_CONFIG_NO_RESPONSE_FROM_TAG         3
#define LLRP_IMPINJ_SET_QT_CONFIG_NONSPECIFIC_READER_ERROR     4
#define LLRP_IMPINJ_SET_QT_CONFIG_INCORRECT_PASSWORD_ERROR     5
#define LLRP_IMPINJ_GET_QT_CONFIG_SUCCESS                      0
#define LLRP_IMPINJ_GET_QT_CONFIG_NONSPECIFIC_TAG_ERROR        1
#define LLRP_IMPINJ_GET_QT_CONFIG_NO_RESPONSE_FROM_TAG         2
#define LLRP_IMPINJ_GET_QT_CONFIG_NONSPECIFIC_READER_ERROR     3
#define LLRP_IMPINJ_GET_QT_CONFIG_INCORRECT_PASSWORD_ERROR     4
#define LLRP_IMPINJ_ACCESS_SPEC_ORDERING_FIFO        0
#define LLRP_IMPINJ_ACCESS_SPEC_ORDERING_ASCENDING   1
#define LLRP_IMPINJ_GPO_MODE_NORMAL                         0
#define LLRP_IMPINJ_GPO_MODE_PULSED                         1
#define LLRP_IMPINJ_GPO_MODE_READER_OPERATIONAL_STATUS      2
#define LLRP_IMPINJ_GPO_MODE_LLRP_CONNECTION_STATUS         3
#define LLRP_IMPINJ_GPO_MODE_READER_INVENTORY_STATUS        4
#define LLRP_IMPINJ_GPO_MODE_NETWORK_CONNECTION_STATUS      5
#define LLRP_IMPINJ_GPO_MODE_READER_INVENTORY_TAGS_STATUS   6
#define LLRP_ROSPEC_ALL      0
#define LLRP_ANTENNA_ALL     0
#define LLRP_GPI_PORT_ALL    0
#define LLRP_GPO_PORT_ALL    0
#define LLRP_ACCESSSPEC_ALL  0
#define LLRP_TLV_LEN_MIN     4
#define LLRP_HEADER_LENGTH  10
#define LLRP_NO_LIMIT        0
#define M2PA_PAYLOAD_PROTOCOL_ID                        5
#define M2TP_USER_IDENTIFIER_PARAMETER_TAG     3
#define M2TP_USER_LENGTH 4
#define M2TP_USER_OFFSET PARAMETER_VALUE_OFFSET
#define M2TP_USER_MTP2          1
#define M2TP_USER_Q921          2
#define M2TP_USER_FRAME_RELAY   3
#define M2TP_MODE_MASTER 1
#define M2TP_MODE_SLAVE  2
#define M2TP_ERROR_CODE_INVALID_VERSION                         1
#define M2TP_ERROR_CODE_INVALID_INTERFACE_IDENTIFIER            2
#define M2TP_ERROR_CODE_INVALID_ADAPTATION_LAYER_IDENTIFIER     3
#define M2TP_ERROR_CODE_INVALID_MESSAGE_TYPE                    4
#define M2TP_ERROR_CODE_INVALID_TRAFFIC_HANDLING_MODE           5
#define M2TP_ERROR_CODE_UNEXPECTED_MESSAGE                      6
#define M2TP_ERROR_CODE_PROTOCOL_ERROR                          7
#define M2TP_ERROR_CODE_INVALID_STREAM_IDENTIFIER               8
#define M2TP_ERROR_CODE_INCOMPATIBLE_MASTER_SLAVE_CONFIGURATION 9
#define M2TP_PAYLOAD_PROTOCOL_ID                       99    /* s-link */
#define M2UA_PAYLOAD_PROTOCOL_ID                        2
#define M3UA_PAYLOAD_PROTOCOL_ID                        3
#define MIH_PORT 4551
#define MIH_EVT_LIST            5
#define MIH_CMD_LIST            6
#define MIH_IQ_TYPE_LIST        7
#define MIH_TRANS_LIST          8
#define MODBUS_PROTOCOL_ID 0
#define MPEG_SECT_SYNTAX_INDICATOR_MASK	0x8000
#define MPEG_SECT_RESERVED_MASK		0x7000
#define MPEG_SECT_LENGTH_MASK		0x0FFF
#define MPEG_CA_TID                                 0x01
#define MPEG_CA_RESERVED_MASK                   0xFFFFC0
#define MPEG_CA_VERSION_NUMBER_MASK             0x00003E
#define MPEG_CA_CURRENT_NEXT_INDICATOR_MASK     0x000001
#define MPEG_DESCR_VIDEO_STREAM_MULTIPLE_FRAME_RATE_FLAG_MASK   0x80
#define MPEG_DESCR_VIDEO_STREAM_FRAME_RATE_CODE_MASK            0x78
#define MPEG_DESCR_VIDEO_STREAM_MPEG1_ONLY_FLAG_MASK            0x04
#define MPEG_DESCR_VIDEO_STREAM_CONSTRAINED_PARAMETER_FLAG_MASK 0x02
#define MPEG_DESCR_VIDEO_STREAM_STILL_PICTURE_FLAG_MASK         0x01
#define MPEG_DESCR_VIDEO_STREAM_CHROMA_FORMAT_MASK              0xC0
#define MPEG_DESCR_VIDEO_STREAM_FRAME_RATE_EXTENSION_FLAG_MASK  0x20
#define MPEG_DESCR_VIDEO_STREAM_RESERVED_MASK                   0x1F
#define MPEG_DESCR_AUDIO_STREAM_FREE_FORMAT_FLAG_MASK                   0x80
#define MPEG_DESCR_AUDIO_STREAM_ID_MASK                                 0x40
#define MPEG_DESCR_AUDIO_STREAM_LAYER_MASK                              0x30
#define MPEG_DESCR_AUDIO_STREAM_VARIABLE_RATE_AUDIO_INDICATOR_MASK      0x08
#define MPEG_DESCR_AUDIO_STREAM_RESERVED_MASK                           0x07
#define MPEG_DESCR_CA_RESERVED_MASK 0xE000
#define MPEG_DESCR_CA_PID_MASK      0x1FFF
#define MPEG_DESCR_SYSTEM_CLOCK_EXTERNAL_CLOCK_REFERENCE_INDICATOR_MASK 0x80
#define MPEG_DESCR_SYSTEM_CLOCK_RESERVED1_MASK                          0x40
#define MPEG_DESCR_SYSTEM_CLOCK_ACCURACY_INTEGER_MASK                   0x3F
#define MPEG_DESCR_SYSTEM_CLOCK_ACCURACY_EXPONENT_MASK                  0xE0
#define MPEG_DESCR_SYSTEM_CLOCK_RESERVED2_MASK                          0x1F
#define MPEG_DESCR_MAX_BITRATE_RESERVED_MASK    0xC00000
#define MPEG_DESCR_MAX_BITRATE_MASK     0x3FFFFF
#define MPEG_DESCR_SMOOTHING_BUFFER_RESERVED1_MASK  0xC00000
#define MPEG_DESCR_SMOOTHING_BUFFER_LEAK_RATE_MASK  0x3FFFFF
#define MPEG_DESCR_SMOOTHING_BUFFER_RESERVED2_MASK  0xC00000
#define MPEG_DESCR_SMOOTHING_BUFFER_SIZE_MASK       0x3FFFFF
#define MPEG_DESCR_STD_RESERVED_MASK    0xFE
#define MPEG_DESCR_STD_LEAK_VALID_MASK  0x01
#define MPEG_DESCR_AVC_VID_CONSTRAINT_SET0_FLAG_MASK    0x80
#define MPEG_DESCR_AVC_VID_CONSTRAINT_SET1_FLAG_MASK    0x40
#define MPEG_DESCR_AVC_VID_CONSTRAINT_SET2_FLAG_MASK    0x20
#define MPEG_DESCR_AVC_VID_COMPATIBLE_FLAGS_MASK        0x1F
#define MPEG_DESCR_AVC_VID_STILL_PRESENT_MASK           0x80
#define MPEG_DESCR_AVC_VID_24H_PICTURE_FLAG_MASK        0x40
#define MPEG_DESCR_AVC_VID_RESERVED_MASK                0x3F
#define MPEG_DESCR_SATELLITE_DELIVERY_WEST_EAST_FLAG_MASK       0x80
#define MPEG_DESCR_SATELLITE_DELIVERY_POLARIZATION_MASK         0x60
#define MPEG_DESCR_SATELLITE_DELIVERY_ROLL_OFF_MASK             0x18
#define MPEG_DESCR_SATELLITE_DELIVERY_ZERO_MASK                 0x18
#define MPEG_DESCR_SATELLITE_DELIVERY_MODULATION_SYSTEM_MASK    0x06
#define MPEG_DESCR_SATELLITE_DELIVERY_MODULATION_TYPE_MASK      0x01
#define MPEG_DESCR_SATELLITE_DELIVERY_FEC_INNER_MASK            0x0F
#define MPEG_DESCR_CABLE_DELIVERY_RESERVED_MASK     0xFFF0
#define MPEG_DESCR_CABLE_DELIVERY_FEC_OUTER_MASK    0x000F
#define MPEG_DESCR_CABLE_DELIVERY_FEC_INNER_MASK    0x0F
#define MPEG_DESCR_VBI_DATA_RESERVED1_MASK  0xC0
#define MPEG_DESCR_VBI_DATA_FIELD_PARITY_MASK   0x20
#define MPEG_DESCR_VBI_DATA_LINE_OFFSET_MASK    0x1F
#define MPEG_DESCR_LINKAGE_HAND_OVER_TYPE_MASK  0xF0
#define MPEG_DESCR_LINKAGE_HAND_OVER_TYPE_SHIFT 0x04
#define MPEG_DESCR_LINKAGE_RESERVED1_MASK   0x0E
#define MPEG_DESCR_LINKAGE_ORIGIN_TYPE_MASK 0x01
#define MPEG_DESCR_LINKAGE_TARGET_LISTED_MASK   0x80
#define MPEG_DESCR_LINKAGE_EVENT_SIMULCAST_MASK 0x40
#define MPEG_DESCR_LINKAGE_RESERVED2_MASK   0x3F
#define MPEG_DESCR_EXTENDED_EVENT_DESCRIPTOR_NUMBER_MASK    0xF0
#define MPEG_DESCR_EXTENDED_EVENT_LAST_DESCRIPTOR_NUMBER_MASK   0x0F
#define MPEG_DESCR_COMPONENT_RESERVED_MASK      0xF0
#define MPEG_DESCR_COMPONENT_STREAM_CONTENT_MASK    0x0F
#define MPEG_DESCR_COMPONENT_CONTENT_TYPE_MASK      0x0FFF
#define MPEG_DESCR_CONTENT_NIBBLE_LEVEL_1_MASK  0xF0
#define MPEG_DESCR_CONTENT_NIBBLE_LEVEL_2_MASK  0x0F
#define MPEG_DESCR_TELETEXT_TYPE_MASK           0xF8
#define MPEG_DESCR_TELETEXT_MAGAZINE_NUMBER_MASK    0x07
#define MPEG_DESCR_LOCAL_TIME_OFFSET_COUNTRY_REGION_ID_MASK 0xFC
#define MPEG_DESCR_LOCAL_TIME_OFFSET_RESERVED_MASK      0x02
#define MPEG_DESCR_LOCAL_TIME_OFFSET_POLARITY           0x01
#define MPEG_DESCR_TERRESTRIAL_DELIVERY_BANDWIDTH_MASK          0xE0
#define MPEG_DESCR_TERRESTRIAL_DELIVERY_PRIORITY_MASK           0x10
#define MPEG_DESCR_TERRESTRIAL_DELIVERY_TIME_SLICING_INDICATOR_MASK 0x08
#define MPEG_DESCR_TERRESTRIAL_DELIVERY_MPE_FEC_INDICATOR_MASK      0x04
#define MPEG_DESCR_TERRESTRIAL_DELIVERY_RESERVED1_MASK          0x03
#define MPEG_DESCR_TERRESTRIAL_DELIVERY_CONSTELLATION_MASK      0xC0
#define MPEG_DESCR_TERRESTRIAL_DELIVERY_HIERARCHY_INFORMATION_MASK  0x38
#define MPEG_DESCR_TERRESTRIAL_DELIVERY_CODE_RATE_HP_STREAM_MASK    0x07
#define MPEG_DESCR_TERRESTRIAL_DELIVERY_CODE_RATE_LP_STREAM_MASK    0xE0
#define MPEG_DESCR_TERRESTRIAL_DELIVERY_GUARD_INTERVAL_MASK     0x18
#define MPEG_DESCR_TERRESTRIAL_DELIVERY_TRANSMISSION_MODE_MASK      0x06
#define MPEG_DESCR_TERRESTRIAL_DELIVERY_OTHER_FREQUENCY_FLAG_MASK   0x01
#define MPEG_DESCR_AC3_COMPONENT_TYPE_FLAG_MASK 0x80
#define MPEG_DESCR_AC3_BSID_FLAG_MASK           0x40
#define MPEG_DESCR_AC3_MAINID_FLAG_MASK         0x20
#define MPEG_DESCR_AC3_ASVC_FLAG_MASK           0x10
#define MPEG_DESCR_AC3_RESERVED_MASK            0x0F
#define MPEG_DESCR_AC3_COMPONENT_TYPE_RESERVED_FLAG_MASK        0x80
#define MPEG_DESCR_AC3_COMPONENT_TYPE_FULL_SERVICE_FLAG_MASK    0x40
#define MPEG_DESCR_AC3_COMPONENT_TYPE_SERVICE_TYPE_FLAGS_MASK   0x38
#define MPEG_DESCR_AC3_COMPONENT_TYPE_NUMBER_OF_CHANNELS_FLAGS  0x07
#define MPEG_DESCR_CONTENT_IDENTIFIER_CRID_TYPE_MASK        0xFC
#define MPEG_DESCR_CONTENT_IDENTIFIER_CRID_LOCATION_MASK    0x03
#define MPEG_DESCR_LOGON_INITIALIZE_CONTINUOUS_CARRIER_RESERVED_MASK              0xC0
#define MPEG_DESCR_LOGON_INITIALIZE_CONTINUOUS_CARRIER_MASK                       0x20
#define MPEG_DESCR_LOGON_INITIALIZE_SECURITY_HANDSHAKE_REQUIRED_MASK              0x10
#define MPEG_DESCR_LOGON_INITIALIZE_PREFIX_FLAG_MASK                              0x08
#define MPEG_DESCR_LOGON_INITIALIZE_DATA_UNIT_LABELLING_FLAG_MASK                 0x04
#define MPEG_DESCR_LOGON_INITIALIZE_MINI_SLOT_FLAG_MASK                           0x02
#define MPEG_DESCR_LOGON_INITIALIZE_CONTENTION_BASED_MINI_SLOT_FLAG_MASK          0x01
#define MPEG_DESCR_LOGON_INITIALIZE_CuapACITY_TYPE_FLAG_RESERVED_MASK              0x80
#define MPEG_DESCR_LOGON_INITIALIZE_CuapACITY_TYPE_FLAG_MASK                       0x40
#define MPEG_DESCR_LOGON_INITIALIZE_TRAFFIC_BURST_TYPE_MASK                       0x20
#define MPEG_DESCR_LOGON_INITIALIZE_RETURN_TRF_PID_MASK                         0x1FFF
#define MPEG_DESCR_LOGON_INITIALIZE_RETURN_CTRL_MNGM_PID_RESERVED_MASK          0xE000
#define MPEG_DESCR_LOGON_INITIALIZE_RETURN_CTRL_MNGM_PID_MASK                   0x1FFF
#define MPEG_DESCR_LOGON_INITIALIZE_CONNECTIVITY_MASK                           0x1000
#define MPEG_DESCR_LOGON_INITIALIZE_RETURN_VPI_RESERVED_MASK                    0x0F00
#define MPEG_DESCR_LOGON_INITIALIZE_RETURN_VPI_MASK                             0x00FF
#define MPEG_DESCR_LOGON_INITIALIZE_RETURN_SIGNALLING_VPI_RESERVED_MASK         0x0F00
#define MPEG_DESCR_LOGON_INITIALIZE_RETURN_SIGNALLING_VPI_MASK                  0x00FF
#define MPEG_DESCR_LOGON_INITIALIZE_FORWARD_SIGNALLING_VPI_RESERVED_MASK        0xFF00
#define MPEG_DESCR_LOGON_INITIALIZE_FORWARD_SIGNALLING_VPI_MASK                 0x00FF
#define MPEG_DESCR_LOGON_INITIALIZE_VDBC_MAX_RESERVED_MASK                      0xF800
#define MPEG_DESCR_LOGON_INITIALIZE_VDBC_MAX_MASK                               0x0700
#define MPEG_PAT_TID    0x00
#define MPEG_PAT_RESERVED_MASK                    0xC0
#define MPEG_PAT_VERSION_NUMBER_MASK              0x3E
#define MPEG_PAT_CURRENT_NEXT_INDICATOR_MASK      0x01
#define MPEG_PAT_PROGRAM_RESERVED_MASK          0xE000
#define MPEG_PAT_PROGRAM_MAP_PID_MASK           0x1FFF
#define MPEG_PMT_TID                              0x02
#define MPEG_PMT_RESERVED1_MASK                   0xC0
#define MPEG_PMT_VERSION_NUMBER_MASK              0x3E
#define MPEG_PMT_CURRENT_NEXT_INDICATOR_MASK      0x01
#define MPEG_PMT_RESERVED2_MASK                 0xE000
#define MPEG_PMT_PCR_PID_MASK                   0x1FFF
#define MPEG_PMT_RESERVED3_MASK                 0xF000
#define MPEG_PMT_PROGRAM_INFO_LENGTH_MASK       0x0FFF
#define MPEG_PMT_STREAM_RESERVED1_MASK          0xE000
#define MPEG_PMT_STREAM_ELEMENTARY_PID_MASK     0x1FFF
#define MPEG_PMT_STREAM_RESERVED2_MASK          0xF000
#define MPEG_PMT_STREAM_ES_INFO_LENGTH_MASK     0x0FFF
#define MPLS_STACK_ENTRY_OBJECT_CLASS           1
#define MPLS_EXTENDED_PAYLOAD_OBJECT_CLASS      0
#define MPLS_STACK_ENTRY_C_TYPE                 1
#define MPLS_EXTENDED_PAYLOAD_C_TYPE            1
#define MPLS_PM_FLAGS_R     0x08
#define MPLS_PM_FLAGS_T     0x04
#define MPLS_PM_FLAGS_RES   0x03
#define MPLS_PM_FLAGS_MASK  0x0F
#define MPLS_PM_DFLAGS_X    0x80
#define MPLS_PM_DFLAGS_B    0x40
#define MPLS_PM_DFLAGS_RES  0x30
#define MPLS_PM_DFLAGS_MASK 0xF0
#define MPLS_PM_TSF_NULL 0
#define MPLS_PM_TSF_SEQ 1
#define MPLS_PM_TSF_NTP 2
#define MPLS_PM_TSF_PTP 3
#define MPLS_TLV_ROUTER    1
#define MPLS_TLV_LINK      2
#define MQ_SOCKET_SPX  0x5E86
#define MSMMS_PORT 1755
#define MTP_RELEASE_REASON_CODE            2
#define MTP_SI_SNM	0x0
#define MTP_SI_MTN	0x1
#define MTP_SI_MTNS	0x2
#define MTP_SI_SCCP	0x3
#define MTP_SI_TUP	0x4
#define MTP_SI_ISUP	0x5
#define MTP_SI_DUP_CC	0x6
#define MTP_SI_DUP_FAC	0x7
#define MTP_SI_MTP_TEST	0x8
#define MTP_SI_ISUP_B	0x9
#define MTP_SI_ISUP_S	0xa
#define MTP_SI_AAL2	0xc
#define MTP_SI_BICC	0xd
#define MTP_SI_GCP	0xe
#define NINEPORT 564
#define NLPID_NULL		0x00
#define NLPID_IPI_T_70		0x01	/* T.70, when an IPI */
#define NLPID_SPI_X_29		0x01	/* X.29, when an SPI */
#define NLPID_X_633		0x03	/* X.633 */
#define NLPID_DMS		0x03	/* Maintenace messages: AT&T TR41459, Nortel NIS A211-1, Telcordia SR-4994, ... */
#define NLPID_Q_931		0x08	/* Q.931, Q.932, X.36, ISO 11572, ISO 11582 */
#define NLPID_Q_933		0x08	/* Q.933, on Frame Relay */
#define NLPID_Q_2931		0x09	/* Q.2931 */
#define NLPID_Q_2119		0x0c	/* Q.2119 */
#define NLPID_SNAP		0x80
#define NLPID_ISO8473_CLNP	0x81	/* X.233 */
#define NLPID_ISO9542_ESIS	0x82
#define NLPID_ISO10589_ISIS	0x83
#define NLPID_ISO10747_IDRP     0x85
#define NLPID_ISO9542X25_ESIS	0x8a
#define NLPID_ISO10030		0x8c
#define NLPID_ISO11577		0x8d	/* X.273 */
#define NLPID_IP6		0x8e
#define NLPID_COMPRESSED	0xb0	/* "Data compression protocol" */
#define NLPID_SNDCF		0xc1	/* "SubNetwork Dependent Convergence Function */
#define NLPID_IEEE_8021AQ	0xc1	/* IEEE 802.1aq (draft-ietf-isis-ieee-aq-05.txt); defined in context of ISIS "supported protocols" TLV */
#define NLPID_IP		0xcc
#define NLPID_PPP		0xcf
#define NLPID_LMI		0x09	/* LMI */
#define NPMP_CTRL_PAYLOAD_PROTOCOL_ID                  36
#define NPMP_DATA_PAYLOAD_PROTOCOL_ID                  37
#define OMAPI_PORT 7911
#define OMRON_FINS_UDP_PORT 9600
#define PCP_STATUS_PORT  5350
#define PCP_PORT         5351
#define PCP_HEADER_LEN 12
#define PINGPONGPROTOCOL_PAYLOAD_PROTOCOL_ID_LEGACY 0x29097602
#define PKTC_PORT	1293
#define PKTC_MTAFQDN_PORT	2246
#define PKTC_MTAFQDN_REQ       0x01
#define PKTC_MTAFQDN_REP       0x02
#define PKTC_MTAFQDN_ERR       0x03
#define PNRP_PORT 3540
#define PNRP_HEADER         0x0010
#define PNRP_HEADER_ACKED   0x0018
#define PNRP_ID             0x0030
#define PNRP_ID_ARRAY       0x0060
#define PORT_NJACK_PC	5264
#define PORT_NJACK_SWITCH	5265
#define PORT_STAT_TLV	0x02
#define PORT_EXT          'f'
#define PORT_ID_TLV_TYPE				0x02	/* Mandatory */
#define PORT_DESCRIPTION_TLV_TYPE		0x04
#define PORT_MBTCP		502	/* Modbus/TCP located on port 502, with IANA registration */
#define PORT_MBRTU		0	/* Modbus RTU over TCP does not have a standard port, default to zero */
#define PORT_MEGACO_TXT 2944
#define PORT_MEGACO_BIN 2945
#define PORT_MIKEY 2269
#define PORT_MINT_CONTROL_TUNNEL	24576
#define PORT_MINT_DATA_TUNNEL		24577
#define PORT_MNDP	5678
#define PORT_PULSE 539
#define PORT_MASTER 27910
#define PORT_BASE                       (7400)
#define PORT_METATRAFFIC_UNICAST        (0)
#define PORT_USERTRAFFIC_MULTICAST      (1)
#define PORT_METATRAFFIC_MULTICAST      (2)
#define PORT_USERTRAFFIC_UNICAST        (3)
#define PORT_INVALID                    (0)
#define PORT_INVALID_STRING             "PORT_INVALID"
#define PORT_SELFM    0
#define PORT_T38 6004
#define PORT_TAPA	5000
#define PORT_CONTROL           0x10
#define PORT_CONTROL_ACK       0x11
#define PORT_WASSP_DISCOVER	13907
#define PORT_WASSP_TUNNEL	13910
#define PORT_WASSP_PEER		13913
#define PORT_NUMBER_INCLUDED    0x40
#define PPID_NETPERFMETER_CONTROL_LEGACY   0x29097605
#define PPID_NETPERFMETER_DATA_LEGACY      0x29097606
#define  PPP_DISCONNECT_CAUSE_CODE    46    /* RFC 3145 */
#define PPP_PADDING	0x1	/* Padding Protocol */
#define PPP_ROHC_SCID	0x3	/* ROHC small-CID */
#define PPP_ROHC_LCID	0x5	/* ROHC large-CID */
#define PPP_IP		0x21	/* Internet Protocol version 4 */
#define PPP_OSI		0x23	/* OSI Network Layer */
#define PPP_XNSIDP	0x25	/* Xerox NS IDP */
#define PPP_DEC4	0x27	/* DECnet Phase IV */
#define PPP_AT		0x29	/* AppleTalk */
#define PPP_IPX		0x2b	/* Novell IPX */
#define PPP_VJC_COMP	0x2d	/* Van Jacobson Compressed TCP/IP */
#define PPP_VJC_UNCOMP	0x2f	/* Van Jacobson Uncompressed TCP/IP */
#define PPP_BCP		0x31	/* Bridging PDU */
#define PPP_ST		0x33	/* Stream Protocol (ST-II) */
#define PPP_VINES	0x35	/* Banyan Vines */
#define PPP_AT_EDDP	0x39	/* AppleTalk EDDP */
#define PPP_AT_SB	0x3b	/* AppleTalk SmartBuffered */
#define PPP_MP		0x3d	/* Multi-Link */
#define PPP_NB		0x3f	/* NETBIOS Framing */
#define PPP_CISCO	0x41	/* Cisco Systems */
#define PPP_ASCOM	0x43	/* Ascom Timeplex */
#define PPP_LBLB	0x45	/* Fujitsu Link Backup and Load Balancing */
#define PPP_RL		0x47	/* DCA Remote Lan */
#define PPP_SDTP	0x49	/* Serial Data Transport Protocol */
#define PPP_LLC		0x4b	/* SNA over 802.2 */
#define PPP_SNA		0x4d	/* SNA */
#define PPP_IPV6HC	0x4f	/* IPv6 Header Compression  */
#define PPP_KNX		0x51	/* KNX Bridging Data */
#define PPP_ENCRYPT	0x53	/* Encryption */
#define PPP_ILE		0x55	/* Individual Link Encryption */
#define PPP_IPV6	0x57	/* Internet Protocol version 6 */
#define PPP_MUX		0x59	/* PPP Muxing */
#define PPP_VSNP	0x5b	/* Vendor-Specific Network Protocol (VSNP) */
#define PPP_TNP		0x5d	/* TRILL Network Protocol (TNP) */
#define PPP_RTP_FH	0x61	/* RTP IPHC Full Header */
#define PPP_RTP_CTCP	0x63	/* RTP IPHC Compressed TCP */
#define PPP_RTP_CNTCP	0x65	/* RTP IPHC Compressed Non TCP */
#define PPP_RTP_CUDP8	0x67	/* RTP IPHC Compressed UDP 8 */
#define PPP_RTP_CRTP8	0x69	/* RTP IPHC Compressed RTP 8 */
#define PPP_STAMPEDE	0x6f	/* Stampede Bridging */
#define PPP_MPPLUS	0x73	/* MP+ Protocol */
#define PPP_NTCITS_IPI	0xc1	/* NTCITS IPI */
#define PPP_ML_SLCOMP	0xfb	/* Single link compression in multilink */
#define PPP_COMP	0xfd	/* Compressed datagram */
#define PPP_STP_HELLO	0x0201	/* 802.1d Hello Packets */
#define PPP_IBM_SR	0x0203	/* IBM Source Routing BPDU */
#define PPP_DEC_LB	0x0205	/* DEC LANBridge100 Spanning Tree */
#define PPP_CDP		0x0207	/* Cisco Discovery Protocol */
#define PPP_NETCS	0x0209	/* Netcs Twin Routing */
#define PPP_STP		0x020b	/* STP - Scheduled Transfer Protocol */
#define PPP_EDP		0x020d	/* EDP - Extreme Discovery Protocol */
#define PPP_OSCP	0x0211	/* Optical Supervisory Channel Protocol */
#define PPP_OSCP2	0x0213	/* Optical Supervisory Channel Protocol */
#define PPP_LUXCOM	0x0231	/* Luxcom */
#define PPP_SIGMA	0x0233	/* Sigma Network Systems */
#define PPP_ACSP	0x0235	/* Apple Client Server Protocol */
#define PPP_MPLS_UNI	0x0281	/* MPLS Unicast */
#define PPP_MPLS_MULTI	0x0283	/* MPLS Multicast */
#define PPP_P12844	0x0285	/* IEEE p1284.4 standard - data packets */
#define PPP_TETRA	0x0287	/* ETSI TETRA Network Procotol Type 1 */
#define PPP_MFTP	0x0289	/* Multichannel Flow Treatment Protocol */
#define PPP_RTP_CTCPND	0x2063	/* RTP IPHC Compressed TCP No Delta */
#define PPP_RTP_CS	0x2065	/* RTP IPHC Context State */
#define PPP_RTP_CUDP16	0x2067	/* RTP IPHC Compressed UDP 16 */
#define PPP_RTP_CRDP16	0x2069	/* RTP IPHC Compressed RTP 16 */
#define PPP_CCCP	0x4001	/* Cray Communications Control Protocol */
#define PPP_CDPD_MNRP	0x4003	/* CDPD Mobile Network Registration Protocol */
#define PPP_EXPANDAP	0x4005	/* Expand accelerator protocol */
#define PPP_ODSICP	0x4007	/* ODSICP NCP */
#define PPP_DOCSIS	0x4009	/* DOCSIS DLL */
#define PPP_CETACEANNDP	0x400b	/* Cetacean Network Detection Protocol */
#define PPP_LZS		0x4021	/* Stacker LZS */
#define PPP_REFTEK	0x4023	/* RefTek Protocol */
#define PPP_FC		0x4025	/* Fibre Channel */
#define PPP_EMIT	0x4027	/* EMIT Protocols */
#define PPP_VSP		0x405b	/* Vendor-Specific Protocol (VSP) */
#define PPP_TLSP	0x405d	/* TRILL Link State Protocol (TLSP) */
#define PPP_IPCP	0x8021	/* Internet Protocol Control Protocol */
#define PPP_OSINLCP	0x8023	/* OSI Network Layer Control Protocol */
#define PPP_XNSIDPCP	0x8025	/* Xerox NS IDP Control Protocol */
#define PPP_DECNETCP	0x8027	/* DECnet Phase IV Control Protocol */
#define PPP_ATCP	0x8029	/* AppleTalk Control Protocol */
#define PPP_IPXCP	0x802b	/* Novell IPX Control Protocol */
#define PPP_BRIDGENCP	0x8031	/* Bridging NCP */
#define PPP_SPCP	0x8033	/* Stream Protocol Control Protocol */
#define PPP_BVCP	0x8035	/* Banyan Vines Control Protocol */
#define PPP_MLCP	0x803d	/* Multi-Link Control Protocol */
#define PPP_NBCP	0x803f	/* NETBIOS Framing Control Protocol */
#define PPP_CISCOCP	0x8041	/* Cisco Systems Control Protocol */
#define PPP_ASCOMCP	0x8043	/* Ascom Timeplex Control Protocol (?) */
#define PPP_LBLBCP	0x8045	/* Fujitsu LBLB Control Protocol */
#define PPP_RLNCP	0x8047	/* DCA Remote Lan Network Control Protocol */
#define PPP_SDCP	0x8049	/* Serial Data Control Protocol */
#define PPP_LLCCP	0x804b	/* SNA over 802.2 Control Protocol */
#define PPP_SNACP	0x804d	/* SNA Control Protocol */
#define PPP_IP6HCCP	0x804f	/* IP6 Header Compression Control Protocol */
#define PPP_KNXCP	0x8051	/* KNX Bridging Control Protocol */
#define PPP_ECP		0x8053	/* Encryption Control Protocol */
#define PPP_ILECP	0x8055	/* Individual Link Encryption Control Protocol */
#define PPP_IPV6CP	0x8057	/* IPv6 Control Protocol */
#define PPP_MUXCP	0x8059	/* PPP Muxing Control Protocol */
#define PPP_VSNCP	0x805b	/* Vendor-Specific Network Control Protocol (VSNCP)   [RFC3772] */
#define PPP_TNCP	0x805d	/* TRILL Network Control Protocol (TNCP) */
#define PPP_STAMPEDECP	0x806f	/* Stampede Bridging Control Protocol */
#define PPP_MPPCP	0x8073	/* MP+ Contorol Protocol */
#define PPP_IPICP	0x80c1	/* NTCITS IPI Control Protocol */
#define PPP_SLCC	0x80fb	/* Single link compression in multilink control */
#define PPP_CCP		0x80fd	/* Compression Control Protocol */
#define PPP_CDPCP	0x8207	/* Cisco Discovery Protocol Control Protocol */
#define PPP_NETCSCP	0x8209	/* Netcs Twin Routing */
#define PPP_STPCP	0x820b	/* STP - Control Protocol */
#define PPP_EDPCP	0x820d	/* EDPCP - Extreme Discovery Protocol Control Protocol */
#define PPP_ACSPC	0x8235	/* Apple Client Server Protocol Control */
#define PPP_MPLSCP	0x8281	/* MPLS Control Protocol */
#define PPP_P12844CP	0x8285	/* IEEE p1284.4 standard - Protocol Control */
#define PPP_TETRACP	0x8287	/* ETSI TETRA TNP1 Control Protocol */
#define PPP_MFTPCP	0x8289	/* Multichannel Flow Treatment Protocol */
#define PPP_LCP		0xc021	/* Link Control Protocol */
#define PPP_PAP		0xc023	/* Password Authentication Protocol */
#define PPP_LQR		0xc025	/* Link Quality Report */
#define PPP_SPAP	0xc027	/* Shiva Password Authentication Protocol */
#define PPP_CBCP	0xc029	/* CallBack Control Protocol (CBCP) */
#define PPP_BACP	0xc02b	/* BACP Bandwidth Allocation Control Protocol */
#define PPP_BAP		0xc02d	/* BAP Bandwidth Allocation Protocol */
#define PPP_VSAP	0xc05b	/* Vendor-Specific Authentication Protocol (VSAP) */
#define PPP_CONTCP	0xc081	/* Container Control Protocol */
#define PPP_CHAP	0xc223	/* Challenge Handshake Authentication Protocol */
#define PPP_RSAAP	0xc225	/* RSA Authentication Protocol */
#define PPP_EAP		0xc227	/* Extensible Authentication Protocol */
#define PPP_SIEP	0xc229	/* Mitsubishi Security Information Exchange Protocol (SIEP) */
#define PPP_SBAP	0xc26f	/* Stampede Bridging Authorization Protocol */
#define PPP_PRPAP	0xc281	/* Proprietary Authentication Protocol */
#define PPP_PRPAP2	0xc283	/* Proprietary Authentication Protocol */
#define PPP_PRPNIAP	0xc481	/* Proprietary Node ID Authentication Protocol */
#define PPP_PAYLOAD_PROTOCOL_ID                        33
#define PROTO_SHORT_NAME "NJACK"
#define PROTO_LONG_NAME "3com Network Jack"
#define PROTO_VERSION_ICQ98	0x0004
#define PROTO_VERSION_ICQ99	0x0006
#define PROTO_VERSION_ICQ2K	0x0007
#define PROTO_VERSION_ICQ2K1	0x0008
#define PROTO_VERSION_ICQLITE	0x0009
#define PROTO_VERSION_ICQ2K3B	0x000A
#define PROTO_TAG_ASTERIX   "ASTERIX"
#define PROTO_VERSION_STP	0
#define PROTO_VERSION_RSTP 	2
#define PROTO_VERSION_MSTP	3
#define PROTO_VERSION_SPB	4
#define PROTO_TAG_BRP   "BRP"
#define PROTO_TYPE_NLPID       1
#define PROTO_TYPE_IEEE_802_2  2
#define PROTO_TAG_CUPS "CUPS"
#define PROTO_ID_OSI_OID        0x00
#define PROTO_ID_DNA_SESSCTL    0x02
#define PROTO_ID_DNA_SESSCTL_V3 0x03
#define PROTO_ID_DNA_NSP        0x04
#define PROTO_ID_OSI_TP4        0x05
#define PROTO_ID_OSI_CLNS       0x06
#define PROTO_ID_TCP            0x07
#define PROTO_ID_UDP            0x08
#define PROTO_ID_IP             0x09
#define PROTO_ID_RPC_CL         0x0a
#define PROTO_ID_RPC_CO         0x0b
#define PROTO_ID_SPX            0x0c    /* from DCOM spec (is this correct?) */
#define PROTO_ID_UUID           0x0d
#define PROTO_ID_IPX            0x0e    /* from DCOM spec (is this correct?) */
#define PROTO_ID_NAMED_PIPES    0x0f
#define PROTO_ID_NAMED_PIPES_2  0x10
#define PROTO_ID_NETBIOS        0x11
#define PROTO_ID_NETBEUI        0x12
#define PROTO_ID_NETWARE_SPX    0x13
#define PROTO_ID_NETWARE_IPX    0x14
#define PROTO_ID_ATALK_STREAM   0x16
#define PROTO_ID_ATALK_DATAGRAM 0x17
#define PROTO_ID_ATALK          0x18
#define PROTO_ID_NETBIOS_2      0x19
#define PROTO_ID_VINES_SPP      0x1a
#define PROTO_ID_VINES_IPC      0x1b
#define PROTO_ID_STREETTALK     0x1c
#define PROTO_ID_HTTP           0x1f
#define PROTO_ID_UNIX_DOMAIN    0x20
#define PROTO_ID_NULL           0x21
#define PROTO_ID_NETBIOS_3      0x22
#define PROTO_TAG_H223 "H223"
#define PROTO_TAG_IAX2          "IAX2"
#define PROTO_TAG_INFINIBAND    "Infiniband"
#define PROTO_TAG_ISMACRYP	"ISMACRYP"
#define PROTO_TAG_ISMACRYP_11	"ISMACryp_11"
#define PROTO_TAG_ISMACRYP_20	"ISMACryp_20"
#define PROTO_TAG_KNET      "KNET"    /*!< Definition of kNet Protocol */
#define PROTO_TAG_MACTELNET "MAC-Telnet"
#define PROTO_TAG_MUX27010  "MUX27010"
#define PROTO_LENGTH_UNTIL_END -1
#define PROTO_STRING_ISIS "ISO 10589 ISIS InTRA Domain Routeing Information Exchange Protocol"
#define PROTO_STRING_IDRP "ISO 10747 IDRP InTER Domain Routeing Information Exchange Protocol"
#define PROTO_STRING_ESIS "ISO 9542 ESIS Routeing Information Exchange Protocol"
#define PROTO_STRING_CLNP "ISO 8473/X.233 CLNP ConnectionLess Network Protocol"
#define PROTO_STRING_COTP "ISO 8073/X.224 COTP Connection-Oriented Transport Protocol"
#define PROTO_STRING_CLTP "ISO 8602/X.234 CLTP ConnectionLess Transport Protocol"
#define PROTO_STRING_LSP  "ISO 10589 ISIS Link State Protocol Data Unit"
#define PROTO_STRING_CSNP "ISO 10589 ISIS Complete Sequence Numbers Protocol Data Unit"
#define PROTO_STRING_PSNP "ISO 10589 ISIS Partial Sequence Numbers Protocol Data Unit"
#define PROTO_STRING_CLSES "ISO 9548-1 OSI Connectionless Session Protocol"
#define PROTO_STRING_SES "ISO 8327-1 OSI Session Protocol"
#define PROTO_STRING_SES_INFO "ISO 8327-1 OSI Session Protocol."
#define PROTO_TAG_SIMULCRYPT            "SIMULCRYPT"
#define PROTO_TAG_tetra	"TETRA"
#define PROTO_PRE_ALLOC_HF_FIELDS_MEM (144000+PRE_ALLOC_EXPERT_FIELDS_MEM)
#define PROTO_3GPP_RNA_PROTOCOL_ID                     42
#define PROTO_3GPP_M2AP_PROTOCOL_ID                    43
#define PROTO_3GPP_M3AP_PROTOCOL_ID                    44
#define PTPIP_PORT            15740  /*[1] Section ********/
#define PTPIP_GUID_SIZE        16 /*[1] Section 2.3.1*/
#define PTPIP_MAX_PARAM_COUNT    5 /*[1] Section 2.3.6*/
#define PT_G723			4	/* From Vineet Kumar of Intel; see the Web page */
#define PT_H261			31	/* RFC 2032 */
#define PT_H263			34	/* from Chunrong Zhu of Intel; see the Web page */
#define PT_JPEG			26	/* RFC 2435 */
#define PT_MP2T			33	/* RFC 2250 */
#define PT_MPV			32	/* RFC 2250 */
#define RLOGIN_PORT 513
#define RMCP_CLASS_ASF 0x06
#define RMCP_CLASS_IPMI 0x07
#define RMCP_TYPE_MASK		0x80
#define RMCP_TYPE_NORM		0x00
#define RMCP_TYPE_ACK		0x01
#define RMCP_CLASS_MASK		0x1f
#define RMCP_CLASS_OEM		0x08
#define RSH_PORT 514
#define RSH_STDERR_PORT_LEN 5
#define RSH_CLIENT_USERNAME_LEN 16
#define RSH_SERVER_USERNAME_LEN 16
#define RSH_COMMAND_LEN 256 /* Based on the size of the system's argument list */
#define RTMP_PORT                     1935
#define RTP_V2_HEADER_MIN_LEN 12
#define RTP_PT_DEFAULT_RANGE "0"
#define RTP_DTMF_0	0
#define RTP_DTMF_1	1
#define RTP_DTMF_2	2
#define RTP_DTMF_3	3
#define RTP_DTMF_4	4
#define RTP_DTMF_5	5
#define RTP_DTMF_6	6
#define RTP_DTMF_7	7
#define RTP_DTMF_8	8
#define RTP_DTMF_9	9
#define RTP_DTMF_STAR	10
#define RTP_DTMF_POUND	11
#define RTP_DTMF_A	12
#define RTP_DTMF_B	13
#define RTP_DTMF_C	14
#define RTP_DTMF_D	15
#define RTP_DTMF_FLASH	16
#define RTP_ANS		32
#define RTP_ANSREV	33
#define RTP_ANSAM	34
#define RTP_ANSAMREV	35
#define RTP_CNG		36
#define RTP_V21C1B0	37
#define RTP_V21C1B1	38
#define RTP_V21C2B0	39
#define RTP_V21C2B1	40
#define RTP_CRDI	41
#define RTP_CRDR	42
#define RTP_CRE		43
#define RTP_ESI		44
#define RTP_ESR		45
#define RTP_MRDI	46
#define RTP_MRDR	47
#define RTP_MRE		48
#define RTP_CT		49
#define RTP_OFFHOOK	64
#define RTP_ONHOOK	65
#define RTP_DIALTONE	66
#define RTP_INTDT	67
#define RTP_SPCDT	68
#define RTP_2NDDT	69
#define RTP_RGTONE	70
#define RTP_SPRGTONE	71
#define RTP_BUSYTONE	72
#define RTP_CNGTONE	73
#define RTP_SPINFOTN	74
#define RTP_CMFTTONE	75
#define RTP_HOLDTONE	76
#define RTP_RECTONE	77
#define RTP_CLRWTTONE	78
#define RTP_CWTONE	79
#define RTP_PAYTONE	80
#define RTP_POSINDTONE	81
#define RTP_NEGINDTONE	82
#define RTP_WARNTONE	83
#define RTP_INTRTONE	84
#define RTP_CALLCDTONE	85
#define RTP_PAYPHONE	86
#define RTP_CAS		87
#define RTP_OFFHKWARN	88
#define RTP_RING	89
#define RTP_ACCPTTONE	96
#define RTP_CONFIRMTN	97
#define RTP_DLTNRECALL	98
#define RTP_END3WAYTN	99
#define RTP_FACTONE	100
#define RTP_LNLOCKTN	101
#define RTP_NUMUNOBT	102
#define RTP_OFFERGTONE	103
#define RTP_PERMSIGTN	104
#define RTP_PREEMPTTN	105
#define RTP_QUETONE	106
#define RTP_REFUSALTN	107
#define RTP_ROUTETONE	108
#define RTP_VALIDTONE	109
#define RTP_WAITGTONE	110
#define RTP_WARNEOPTN	111
#define RTP_WARNPIPTN	112
#define RTP_MF0		128
#define RTP_MF1		129
#define RTP_MF2		130
#define RTP_MF3		131
#define RTP_MF4		132
#define RTP_MF5		133
#define RTP_MF6		134
#define RTP_MF7		135
#define RTP_MF8		136
#define RTP_MF9		137
#define RTP_K0		138
#define RTP_K1		139
#define RTP_K2		140
#define RTP_S0		141
#define RTP_S1		142
#define RTP_S3		143
#define RTP_WINK	160
#define RTP_WINKOFF	161
#define RTP_INCSEIZ	162
#define RTP_SEIZURE	163
#define RTP_UNSEIZE	164
#define RTP_COT		165
#define RTP_DEFCOT	166
#define RTP_COTTONE	167
#define RTP_COTSEND	168
#define RTP_COTVERFD	170
#define RTP_LOOPBACK	171
#define RTP_MWATTTONE	172
#define RTP_NEWMWATTTN	173
#define RTP_CISCO_NSE_FAX_PASSTHROUGH_IND    192
#define RTP_CISCO_NSE_MODEM_PASSTHROUGH_IND  193
#define RTP_CISCO_NSE_VOICE_MODE_IND         194
#define RTP_CISCO_NSE_MODEM_RELAY_Cuap_IND    199
#define RTP_CISCO_NSE_FAX_RELAY_IND          200
#define RTP_CISCO_NSE_ACK                    201
#define RTP_CISCO_NSE_NACK                   202
#define RTP_CISCO_NSE_MODEM_RELAY_IND        203
#define RTP_MIDI_DISSECTOR_NAME						"RFC 4695/6295 RTP-MIDI"
#define RTP_MIDI_DISSECTOR_SHORTNAME					"RTP-MIDI"
#define RTP_MIDI_DISSECTOR_ABBREVIATION					"rtpmidi"
#define RTP_MIDI_NO_RUNNING_STATUS					0xffff
#define RTP_MIDI_CTRL_BANK_SELECT_MSB					0
#define RTP_MIDI_CTRL_MODULATION_WHEEL_OR_LEVER_MSB			1
#define RTP_MIDI_CTRL_BREATH_CONTROLLER_MSB				2
#define RTP_MIDI_CTRL						3 */
#define RTP_MIDI_CTRL_FOOT_CONTROLLER_MSB				4
#define RTP_MIDI_CTRL_PORTAMENTO_TIME_MSB				5
#define RTP_MIDI_CTRL_DATA_ENTRY_MSB					6
#define RTP_MIDI_CTRL_CHANNEL_VOLUME_MSB				7
#define RTP_MIDI_CTRL_BALANCE_MSB					8
#define RTP_MIDI_CTRL_PAN_MSB						10
#define RTP_MIDI_CTRL_EXPRESSION_CONTROLLER_MSB				11
#define RTP_MIDI_CTRL_EFFECT_CONTROL_1_MSB				12
#define RTP_MIDI_CTRL_EFFECT_CONTROL_2_MSB				13
#define RTP_MIDI_CTRL_						14
#define RTP_MIDI_CTRL_GENERAL_PURPOSE_CONTROLLER_1_MSB			16
#define RTP_MIDI_CTRL_GENERAL_PURPOSE_CONTROLLER_2_MSB			17
#define RTP_MIDI_CTRL_GENERAL_PURPOSE_CONTROLLER_3_MSB			18
#define RTP_MIDI_CTRL_GENERAL_PURPOSE_CONTROLLER_4_MSB			19
#define RTP_MIDI_CTRL_BANK_SELECT_LSB					32
#define RTP_MIDI_CTRL_MODULATION_WHEEL_OR_LEVER_LSB			33
#define RTP_MIDI_CTRL_BREATH_CONTROLLER_LSB				34
#define RTP_MIDI_CTRL_FOOT_CONTROLLER_LSB				36
#define RTP_MIDI_CTRL_PORTAMENTO_TIME_LSB				37
#define RTP_MIDI_CTRL_DATA_ENTRY_LSB					38
#define RTP_MIDI_CTRL_CHANNEL_VOLUME_LSB				39
#define RTP_MIDI_CTRL_BALANCE_LSB					40
#define RTP_MIDI_CTRL_PAN_LSB						42
#define RTP_MIDI_CTRL_EXPRESSION_CONTROLLER_LSB				43
#define RTP_MIDI_CTRL_EFFECT_CONTROL_1_LSB				44
#define RTP_MIDI_CTRL_EFFECT_CONTROL_2_LSB				45
#define RTP_MIDI_CTRL_GENERAL_PURPOSE_CONTROLLER_1_LSB			48
#define RTP_MIDI_CTRL_GENERAL_PURPOSE_CONTROLLER_2_LSB			49
#define RTP_MIDI_CTRL_GENERAL_PURPOSE_CONTROLLER_3_LSB			50
#define RTP_MIDI_CTRL_GENERAL_PURPOSE_CONTROLLER_4_LSB			51
#define RTP_MIDI_CTRL_DAMPER_PEDAL					64
#define RTP_MIDI_CTRL_PORTAMENTO_ON_OFF					65
#define RTP_MIDI_CTRL_SUSTENUTO						66
#define RTP_MIDI_CTRL_SOFT_PEDAL					67
#define RTP_MIDI_CTRL_LEGATO_FOOTSWITCH					68
#define RTP_MIDI_CTRL_HOLD_2						69
#define RTP_MIDI_CTRL_SOUND_CONTROLLER_1				70
#define RTP_MIDI_CTRL_SOUND_CONTROLLER_2				71
#define RTP_MIDI_CTRL_SOUND_CONTROLLER_3				72
#define RTP_MIDI_CTRL_SOUND_CONTROLLER_4				73
#define RTP_MIDI_CTRL_SOUND_CONTROLLER_5				74
#define RTP_MIDI_CTRL_SOUND_CONTROLLER_6				75
#define RTP_MIDI_CTRL_SOUND_CONTROLLER_7				76
#define RTP_MIDI_CTRL_SOUND_CONTROLLER_8				77
#define RTP_MIDI_CTRL_SOUND_CONTROLLER_9				78
#define RTP_MIDI_CTRL_SOUND_CONTROLLER_10				79
#define RTP_MIDI_CTRL_GENERAL_PURPOSE_CONTROLLER_5			80
#define RTP_MIDI_CTRL_GENERAL_PURPOSE_CONTROLLER_6			81
#define RTP_MIDI_CTRL_GENERAL_PURPOSE_CONTROLLER_7			82
#define RTP_MIDI_CTRL_GENERAL_PURPOSE_CONTROLLER_8			83
#define RTP_MIDI_CTRL_PORTAMENTO_CONTROL				84
#define RTP_MIDI_CTRL_EFFECTS_1_DEPTH					91
#define RTP_MIDI_CTRL_EFFECTS_2_DEPTH					92
#define RTP_MIDI_CTRL_EFFECTS_3_DEPTH					93
#define RTP_MIDI_CTRL_EFFECTS_4_DEPTH					94
#define RTP_MIDI_CTRL_EFFECTS_5_DEPTH					95
#define RTP_MIDI_CTRL_DATA_INCREMENT					96
#define RTP_MIDI_CTRL_DATA_DECREMENT					97
#define RTP_MIDI_CTRL_NON_REGISTERED_PARAM_LSB				98
#define RTP_MIDI_CTRL_NON_REGISTERED_PARAM_MSB				99
#define RTP_MIDI_CTRL_REGISTERED_PARAM_LSB				100
#define RTP_MIDI_CTRL_REGISTERED_PARAM_MSB				101
#define RTP_MIDI_CTRL_ALL_SOUND_OFF					120
#define RTP_MIDI_CTRL_RESET_ALL_CONTROLLERS				121
#define RTP_MIDI_CTRL_LOCAL_CONTROL_ON_OFF				122
#define RTP_MIDI_CTRL_ALL_NOTES_OFF					123
#define RTP_MIDI_CTRL_OMNI_MODE_OFF					124
#define RTP_MIDI_CTRL_OMNI_MODE_ON					125
#define RTP_MIDI_CTRL_MONO_MODE_ON					126
#define RTP_MIDI_CTRL_POLY_MODE_ON					127
#define RTP_MIDI_STATUS_CHANNEL_NOTE_OFF				0x08		/* 0x8n		n->channel */
#define RTP_MIDI_STATUS_CHANNEL_NOTE_ON					0x09		/* 0x9n		n->channel */
#define RTP_MIDI_STATUS_CHANNEL_POLYPHONIC_KEY_PRESSURE			0x0a		/* 0xan		n->channel */
#define RTP_MIDI_STATUS_CHANNEL_CONTROL_CHANGE				0x0b		/* 0xbn		n->channel */
#define RTP_MIDI_STATUS_CHANNEL_PROGRAM_CHANGE				0x0c		/* 0xcn		n->channel */
#define RTP_MIDI_STATUS_CHANNEL_CHANNEL_PRESSURE			0x0d		/* 0xdn		n->channel */
#define RTP_MIDI_STATUS_CHANNEL_PITCH_BEND_CHANGE			0x0e		/* 0xen		n->channel */
#define RTP_MIDI_CHANNEL_1						0x00
#define RTP_MIDI_CHANNEL_2						0x01
#define RTP_MIDI_CHANNEL_3						0x02
#define RTP_MIDI_CHANNEL_4						0x03
#define RTP_MIDI_CHANNEL_5						0x04
#define RTP_MIDI_CHANNEL_6						0x05
#define RTP_MIDI_CHANNEL_7						0x06
#define RTP_MIDI_CHANNEL_8						0x07
#define RTP_MIDI_CHANNEL_9						0x08
#define RTP_MIDI_CHANNEL_10						0x09
#define RTP_MIDI_CHANNEL_11						0x0a
#define RTP_MIDI_CHANNEL_12						0x0b
#define RTP_MIDI_CHANNEL_13						0x0c
#define RTP_MIDI_CHANNEL_14						0x0d
#define RTP_MIDI_CHANNEL_15						0x0e
#define RTP_MIDI_CHANNEL_16						0x0f
#define RTP_MIDI_CHANNEL_MASK						0x0f
#define RTP_MIDI_STATUS_COMMON_SYSEX_START				0xf0
#define RTP_MIDI_STATUS_COMMON_MTC_QUARTER_FRAME			0xf1
#define RTP_MIDI_STATUS_COMMON_SONG_POSITION_POINTER			0xf2
#define RTP_MIDI_STATUS_COMMON_SONG_SELECT				0xf3
#define RTP_MIDI_STATUS_COMMON_UNDEFINED_F4				0xf4
#define RTP_MIDI_STATUS_COMMON_UNDEFINED_F5				0xf5
#define RTP_MIDI_STATUS_COMMON_TUNE_REQUEST				0xf6
#define RTP_MIDI_STATUS_COMMON_SYSEX_END				0xf7
#define RTP_MIDI_STATUS_COMMON_REALTIME_TIMING_CLOCK			0xf8
#define RTP_MIDI_STATUS_COMMON_REALTIME_MIDI_TICK			0xf9		/* Spec says undefined */
#define RTP_MIDI_STATUS_COMMON_REALTIME_START				0xfa
#define RTP_MIDI_STATUS_COMMON_REALTIME_CONTINUE			0xfb
#define RTP_MIDI_STATUS_COMMON_REALTIME_STOP				0xfc
#define RTP_MIDI_STATUS_COMMON_REALTIME_UNDEFINED_FD			0xfd
#define RTP_MIDI_STATUS_COMMON_REALTIME_ACTIVE_SENSING			0xfe
#define RTP_MIDI_STATUS_COMMON_REALTIME_SYSTEM_RESET			0xff
#define RTP_MIDI_SYSEX_COMMON_NRT_SAMPLE_DUMP_HEADER			0x01
#define RTP_MIDI_SYSEX_COMMON_NRT_SAMPLE_DATA_PACKET			0x02
#define RTP_MIDI_SYSEX_COMMON_NRT_SAMPLE_DUMP_REQUEST			0x03
#define RTP_MIDI_SYSEX_COMMON_NRT_MTC					0x04
#define RTP_MIDI_SYSEX_COMMON_NRT_SAMPLE_DUMP_EXTENSIONS		0x05
#define RTP_MIDI_SYSEX_COMMON_NRT_GENERAL_INFORMATION			0x06
#define RTP_MIDI_SYSEX_COMMON_NRT_FILE_DUMP				0x07
#define RTP_MIDI_SYSEX_COMMON_NRT_MIDI_TUNING_STANDARD			0x08
#define RTP_MIDI_SYSEX_COMMON_NRT_GENERAL_MIDI				0x09
#define RTP_MIDI_SYSEX_COMMON_NRT_DOWNLOADABLE_SOUNDS			0x0a
#define RTP_MIDI_SYSEX_COMMON_NRT_END_OF_FILE				0x7b
#define RTP_MIDI_SYSEX_COMMON_NRT_WAIT					0x7c
#define RTP_MIDI_SYSEX_COMMON_NRT_CANCEL				0x7d
#define RTP_MIDI_SYSEX_COMMON_NRT_NAK					0x7e
#define RTP_MIDI_SYSEX_COMMON_NRT_ACK					0x7f
#define RTP_MIDI_SYSEX_COMMON_RT_MIDI_TIME_CODE				0x01
#define RTP_MIDI_SYSEX_COMMON_RT_MIDI_SHOW_CONTROL			0x02
#define RTP_MIDI_SYSEX_COMMON_RT_NOTATION_INFORMATION			0x03
#define RTP_MIDI_SYSEX_COMMON_RT_DEVICE_CONTROL				0x04
#define RTP_MIDI_SYSEX_COMMON_RT_MTC_CUEING				0x05
#define RTP_MIDI_SYSEX_COMMON_RT_MIDI_MACHINE_CONTROL_COMMAND		0x06
#define RTP_MIDI_SYSEX_COMMON_RT_MIDI_MACHINE_CONTROL_RESPONSE		0x07
#define RTP_MIDI_SYSEX_COMMON_RT_MIDI_TUNING_STANDARD			0x08
#define RTP_MIDI_MANU_SHORT_ISLONG					0x00
#define RTP_MIDI_MANU_SHORT_SEQUENTIAL_CIRCUITS				0x01
#define RTP_MIDI_MANU_SHORT_BIG_BRIAR_IDP				0x02
#define RTP_MIDI_MANU_SHORT_OCTAVE_PLATEAU_VOYETRA			0x03
#define RTP_MIDI_MANU_SHORT_MOOG					0x04
#define RTP_MIDI_MANU_SHORT_PASSPORT_DESIGNS				0x05
#define RTP_MIDI_MANU_SHORT_LEXICON					0x06
#define RTP_MIDI_MANU_SHORT_KURZWEIL					0x07
#define RTP_MIDI_MANU_SHORT_FENDER					0x08
#define RTP_MIDI_MANU_SHORT_GULBRANSEN					0x09
#define RTP_MIDI_MANU_SHORT_AKG_ACOUSTICS				0x0a
#define RTP_MIDI_MANU_SHORT_VOYCE_MUSIC					0x0b
#define RTP_MIDI_MANU_SHORT_WAVEFRAME					0x0c
#define RTP_MIDI_MANU_SHORT_ADA_SIGNAL_PROCESSORS			0x0d
#define RTP_MIDI_MANU_SHORT_GARFIELD_ELECTRONICS			0x0e
#define RTP_MIDI_MANU_SHORT_ENSONIQ					0x0f
#define RTP_MIDI_MANU_SHORT_OBERHEIM_GIBSON_LABS			0x10
#define RTP_MIDI_MANU_SHORT_APPLE_COMPUTERS				0x11
#define RTP_MIDI_MANU_SHORT_GREY_MATTER_RESPONSE			0x12
#define RTP_MIDI_MANU_SHORT_DIGIDESIGN					0x13
#define RTP_MIDI_MANU_SHORT_PALMTREE_INSTRUMENTS			0x14
#define RTP_MIDI_MANU_SHORT_JL_COOPER					0x15
#define RTP_MIDI_MANU_SHORT_LOWREY					0x16
#define RTP_MIDI_MANU_SHORT_LINN_ADAMS_SMITH				0x17
#define RTP_MIDI_MANU_SHORT_EMU_SYSTEMS					0x18
#define RTP_MIDI_MANU_SHORT_HARMONY_SYSTEMS				0x19
#define RTP_MIDI_MANU_SHORT_ART						0x1a
#define RTP_MIDI_MANU_SHORT_BALDWIN					0x1b
#define RTP_MIDI_MANU_SHORT_EVENTIDE					0x1c
#define RTP_MIDI_MANU_SHORT_INVENTRONICS				0x1d
#define RTP_MIDI_MANU_SHORT_KEY_CONCEPTS				0x1e
#define RTP_MIDI_MANU_SHORT_CLARITY					0x1f
#define RTP_MIDI_MANU_SHORT_PASSAC					0x20
#define RTP_MIDI_MANU_SHORT_SIEL					0x21
#define RTP_MIDI_MANU_SHORT_SYNTHE_AXE					0x22
#define RTP_MIDI_MANU_SHORT_STEPP					0x23
#define RTP_MIDI_MANU_SHORT_HOHNER					0x24
#define RTP_MIDI_MANU_SHORT_CRUMAR_TWISTER				0x25
#define RTP_MIDI_MANU_SHORT_SOLTON					0x26
#define RTP_MIDI_MANU_SHORT_JELLINGHAUS_MS				0x27
#define RTP_MIDI_MANU_SHORT_SOUTHWORK_MUSIC_SYSTEMS			0x28
#define RTP_MIDI_MANU_SHORT_PPG						0x29
#define RTP_MIDI_MANU_SHORT_JEN						0x2a
#define RTP_MIDI_MANU_SHORT_SSL						0x2b
#define RTP_MIDI_MANU_SHORT_AUDIO_VERITRIEB				0x2c
#define RTP_MIDI_MANU_SHORT_NEVE_HINTON_INSTRUMENTS			0x2d
#define RTP_MIDI_MANU_SHORT_SOUNDTRACS					0x2e
#define RTP_MIDI_MANU_SHORT_ELKA_GENERAL_MUSIC				0x2f
#define RTP_MIDI_MANU_SHORT_DYNACORD					0x30
#define RTP_MIDI_MANU_SHORT_VISCOUNT					0x31
#define RTP_MIDI_MANU_SHORT_DRAWMER					0x32
#define RTP_MIDI_MANU_SHORT_CLAVIA_DIGITAL_INSTRUMENTS			0x33
#define RTP_MIDI_MANU_SHORT_AUDIO_ARCHITECTURE				0x34
#define RTP_MIDI_MANU_SHORT_GENERAL_MUSIC_CORP				0x35
#define RTP_MIDI_MANU_SHORT_CHEETAH					0x36
#define RTP_MIDI_MANU_SHORT_CTM						0x37
#define RTP_MIDI_MANU_SHORT_SIMMONS_UK					0x38
#define RTP_MIDI_MANU_SHORT_SOUNDCRAFT_ELECTRONICS			0x39
#define RTP_MIDI_MANU_SHORT_STEINBERG_GMBH				0x3a
#define RTP_MIDI_MANU_SHORT_WERSI					0x3b
#define RTP_MIDI_MANU_SHORT_AVAB_ELEKTRONIK_AB				0x3c
#define RTP_MIDI_MANU_SHORT_DIGIGRAM					0x3d
#define RTP_MIDI_MANU_SHORT_WALDORF					0x3e
#define RTP_MIDI_MANU_SHORT_QUASIMIDI					0x3f
#define RTP_MIDI_MANU_SHORT_KAWAI					0x40
#define RTP_MIDI_MANU_SHORT_ROLAND					0x41
#define RTP_MIDI_MANU_SHORT_KORG					0x42
#define RTP_MIDI_MANU_SHORT_YAMAHA					0x43
#define RTP_MIDI_MANU_SHORT_CASIO					0x44
#define RTP_MIDI_MANU_SHORT_MORIDAIRA					0x45
#define RTP_MIDI_MANU_SHORT_KAMIYA_STUDIO				0x46
#define RTP_MIDI_MANU_SHORT_AKAI					0x47
#define RTP_MIDI_MANU_SHORT_JAPAN_VICTOR				0x48
#define RTP_MIDI_MANU_SHORT_MEISOSHA					0x49
#define RTP_MIDI_MANU_SHORT_HOSHINO_GAKKI				0x4a
#define RTP_MIDI_MANU_SHORT_FUJITSU					0x4b
#define RTP_MIDI_MANU_SHORT_SONY					0x4c
#define RTP_MIDI_MANU_SHORT_NISSHIN_ONPA				0x4d
#define RTP_MIDI_MANU_SHORT_TEAC					0x4e
#define RTP_MIDI_MANU_SHORT_						0x4f */
#define RTP_MIDI_MANU_SHORT_MATSUSHITA_ELECTRIC				0x50
#define RTP_MIDI_MANU_SHORT_FOSTEX					0x51
#define RTP_MIDI_MANU_SHORT_ZOOM					0x52
#define RTP_MIDI_MANU_SHORT_MIDORI_ELECTRONICS				0x53
#define RTP_MIDI_MANU_SHORT_MATSUSHITA_COMMUNICATION			0x54
#define RTP_MIDI_MANU_SHORT_SUZUKI					0x55
#define RTP_MIDI_MANU_SHORT_FUJI					0x56
#define RTP_MIDI_MANU_SHORT_ACOUSTIC_TECHNICAL_LAB			0x57
#define RTP_MIDI_MANU_SHORT_FAITH					0x59
#define RTP_MIDI_MANU_SHORT_INTERNET_CORPORATION			0x5a
#define RTP_MIDI_MANU_SHORT_SEEKERS_CO					0x5c
#define RTP_MIDI_MANU_SHORT_SD_CARD_ASSOCIATION				0x5f
#define RTP_MIDI_MANU_SHORT_EDUCATIONAL_USE				0x7d
#define RTP_MIDI_MANU_SHORT_NON_REALTIME_UNIVERSAL			0x7e
#define RTP_MIDI_MANU_SHORT_REALTIME_UNIVERSAL				0x7f
#define RTP_MIDI_MANU_LONG_TIME_					0x0000 */
#define RTP_MIDI_MANU_LONG_TIME_WARNER_INTERACTIVE			0x0001
#define RTP_MIDI_MANU_LONG_ADVANCED_GRAVIS_COMP				0x0002
#define RTP_MIDI_MANU_LONG_MEDIA_VISION					0x0003
#define RTP_MIDI_MANU_LONG_DORNES_RESEARCH_GROUP			0x0004
#define RTP_MIDI_MANU_LONG_KMUSE					0x0005
#define RTP_MIDI_MANU_LONG_STYPHER					0x0006
#define RTP_MIDI_MANU_LONG_DIGITAL_MUSIC_CORPORATION			0x0007
#define RTP_MIDI_MANU_LONG_IOTA_SYSTEMS					0x0008
#define RTP_MIDI_MANU_LONG_NEW_ENGLAND_DIGITAL				0x0009
#define RTP_MIDI_MANU_LONG_ARTISYN					0x000a
#define RTP_MIDI_MANU_LONG_IVL_TECHNOLOGIES				0x000b
#define RTP_MIDI_MANU_LONG_SOUTHERN_MUSIC_SYSTEMS			0x000c
#define RTP_MIDI_MANU_LONG_LAKE_BUTLER_SOUND_COMPANY			0x000d
#define RTP_MIDI_MANU_LONG_ALESIS					0x000e
#define RTP_MIDI_MANU_LONG_SOUND_CREATION				0x000f
#define RTP_MIDI_MANU_LONG_DOD_ELECTRONICS				0x0010
#define RTP_MIDI_MANU_LONG_STUDER_EDITECH				0x0011
#define RTP_MIDI_MANU_LONG_SONUS					0x0012
#define RTP_MIDI_MANU_LONG_TEMPORAL_ACUITY_PRODUCTS			0x0013
#define RTP_MIDI_MANU_LONG_PERFECT_FRETWORKS				0x0014
#define RTP_MIDI_MANU_LONG_KAT						0x0015
#define RTP_MIDI_MANU_LONG_OPCODE					0x0016
#define RTP_MIDI_MANU_LONG_RANE_CORP					0x0017
#define RTP_MIDI_MANU_LONG_SPATIAL_SOUND_ANADI_INC			0x0018
#define RTP_MIDI_MANU_LONG_KMX						0x0019
#define RTP_MIDI_MANU_LONG_ALLEN_AND_HEATH_BRENNEL			0x001a
#define RTP_MIDI_MANU_LONG_PEAVEY					0x001b
#define RTP_MIDI_MANU_LONG_360_SYSTEMS					0x001c
#define RTP_MIDI_MANU_LONG_SPECTRUM_DESIGN_DEVELOPMENT			0x001d
#define RTP_MIDI_MANU_LONG_MARQUIS_MUSIC				0x001e
#define RTP_MIDI_MANU_LONG_ZETA_SYSTEMS					0x001f
#define RTP_MIDI_MANU_LONG_AXXES					0x0020
#define RTP_MIDI_MANU_LONG_ORBAN					0x0021
#define RTP_MIDI_MANU_LONG_INDIAN_VALLEY				0x0022
#define RTP_MIDI_MANU_LONG_TRITON					0x0023
#define RTP_MIDI_MANU_LONG_KTI						0x0024
#define RTP_MIDI_MANU_LONG_BREAKAWAY_TECHNOLOGIES			0x0025
#define RTP_MIDI_MANU_LONG_CAE						0x0026
#define RTP_MIDI_MANU_LONG_HARRISON_SYSTEMS_INC				0x0027
#define RTP_MIDI_MANU_LONG_FUTURE_LAB_MARK_KUO				0x0028
#define RTP_MIDI_MANU_LONG_ROCKTRON_CORP				0x0029
#define RTP_MIDI_MANU_LONG_PIANODISC					0x002a
#define RTP_MIDI_MANU_LONG_CANNON_RESEARCH_GROUP			0x002b
#define RTP_MIDI_MANU_LONG_						0x002c */
#define RTP_MIDI_MANU_LONG_RODGERS_INSTRUMENTS_CORP			0x002d
#define RTP_MIDI_MANU_LONG_BLUE_SKY_LOGIC				0x002e
#define RTP_MIDI_MANU_LONG_ENCORE_ELECTRONICS				0x002f
#define RTP_MIDI_MANU_LONG_UPTOWN					0x0030
#define RTP_MIDI_MANU_LONG_VOCE						0x0031
#define RTP_MIDI_MANU_LONG_CTI_AUDIO_INC				0x0032
#define RTP_MIDI_MANU_LONG_SS_RESEARCH					0x0033
#define RTP_MIDI_MANU_LONG_BRODERBUND_SOFTWARE				0x0034
#define RTP_MIDI_MANU_LONG_ALLEN_ORGAN_CO				0x0035
#define RTP_MIDI_MANU_LONG_MUSIC_QUEST					0x0037
#define RTP_MIDI_MANU_LONG_APHEX					0x0038
#define RTP_MIDI_MANU_LONG_GALLIEN_KRUEGER				0x0039
#define RTP_MIDI_MANU_LONG_IBM						0x003a
#define RTP_MIDI_MANU_LONG_MARK_OF_THE_UNICORN				0x003b
#define RTP_MIDI_MANU_LONG_HOTZ_INSTRUMENTS_TECH			0x003c
#define RTP_MIDI_MANU_LONG_ETA_LIGHTING					0x003d
#define RTP_MIDI_MANU_LONG_NSI_CORPORATION				0x003e
#define RTP_MIDI_MANU_LONG_ADLIB_INC					0x003f
#define RTP_MIDI_MANU_LONG_RICHMOND_SOUND_DESIGN			0x0040
#define RTP_MIDI_MANU_LONG_MICROSOFT					0x0041
#define RTP_MIDI_MANU_LONG_THE_SOFTWARE_TOOLWORKS			0x0042
#define RTP_MIDI_MANU_LONG_RJMG_NICHE					0x0043
#define RTP_MIDI_MANU_LONG_INTONE					0x0044
#define RTP_MIDI_MANU_LONG_ADVANCED_REMOTE_TECH				0x0045
#define RTP_MIDI_MANU_LONG_GT_ELECTRONICS_GROOVE_TUBES			0x0047
#define RTP_MIDI_MANU_LONG_INTERMIDI					0x0048
#define RTP_MIDI_MANU_LONG_TIMELINE_VISTA				0x0049
#define RTP_MIDI_MANU_LONG_MESA_BOOGIE					0x004a
#define RTP_MIDI_MANU_LONG_SEQUOIA_DEVELOPMENT				0x004c
#define RTP_MIDI_MANU_LONG_STUDIO_ELECTRONICS				0x004d
#define RTP_MIDI_MANU_LONG_EUPHONIX					0x004e
#define RTP_MIDI_MANU_LONG_INTERMIDI2					0x004f
#define RTP_MIDI_MANU_LONG_MIDI_SOLUTIONS				0x0050
#define RTP_MIDI_MANU_LONG_3DO_COMPANY					0x0051
#define RTP_MIDI_MANU_LONG_LIGHTWAVE_RESEARCH				0x0052
#define RTP_MIDI_MANU_LONG_MICROW					0x0053
#define RTP_MIDI_MANU_LONG_SPECTRAL_SYNTHESIS				0x0054
#define RTP_MIDI_MANU_LONG_LONE_WOLF					0x0055
#define RTP_MIDI_MANU_LONG_STUDIO_TECHNOLOGIES				0x0056
#define RTP_MIDI_MANU_LONG_PETERSON_EMP					0x0057
#define RTP_MIDI_MANU_LONG_ATARI					0x0058
#define RTP_MIDI_MANU_LONG_MARION_SYSTEMS				0x0059
#define RTP_MIDI_MANU_LONG_DESIGN_EVENT					0x005a
#define RTP_MIDI_MANU_LONG_WINJAMMER_SOFTWARE				0x005b
#define RTP_MIDI_MANU_LONG_ATT_BELL_LABS				0x005c
#define RTP_MIDI_MANU_LONG_SYMETRIX					0x005e
#define RTP_MIDI_MANU_LONG_MIDI_THE_WORLD				0x005f
#define RTP_MIDI_MANU_LONG_DESPER_PRODUCTS				0x0060
#define RTP_MIDI_MANU_LONG_MICROS_N_MIDI				0x0061
#define RTP_MIDI_MANU_LONG_ACCORDIANS_INTL				0x0062
#define RTP_MIDI_MANU_LONG_EUPHONICS					0x0063
#define RTP_MIDI_MANU_LONG_MUSONIX					0x0064
#define RTP_MIDI_MANU_LONG_TURTLE_BEACH_SYSTEMS				0x0065
#define RTP_MIDI_MANU_LONG_MACKIE_DESIGNS				0x0066
#define RTP_MIDI_MANU_LONG_COMPUSERVE					0x0067
#define RTP_MIDI_MANU_LONG_BES_TECHNOLOGIES				0x0068
#define RTP_MIDI_MANU_LONG_QRS_MUSIC_ROLLS				0x0069
#define RTP_MIDI_MANU_LONG_P_G_MUSIC					0x006a
#define RTP_MIDI_MANU_LONG_SIERRA_SEMICONDUCTOR				0x006b
#define RTP_MIDI_MANU_LONG_EPIGRAF_AUDIO_VISUAL				0x006c
#define RTP_MIDI_MANU_LONG_ELECTRONICS_DIVERSIFIED			0x006d
#define RTP_MIDI_MANU_LONG_TUNE_1000					0x006e
#define RTP_MIDI_MANU_LONG_ADVANCED_MICRO_DEVICES			0x006f
#define RTP_MIDI_MANU_LONG_MEDIAMATION					0x0070
#define RTP_MIDI_MANU_LONG_SABINE_MUSIC					0x0071
#define RTP_MIDI_MANU_LONG_WOOG_LABS					0x0072
#define RTP_MIDI_MANU_LONG_MIRCOPOLIS					0x0073
#define RTP_MIDI_MANU_LONG_TA_HORNG_MUSICAL_INSTRUMENT			0x0074
#define RTP_MIDI_MANU_LONG_ETEK_LABS_FORTE_TECH				0x0075
#define RTP_MIDI_MANU_LONG_ELECTRO_VOICE				0x0076
#define RTP_MIDI_MANU_LONG_MIDISOFT_CORPORATION				0x0077
#define RTP_MIDI_MANU_LONG_QSOUND_LABS					0x0078
#define RTP_MIDI_MANU_LONG_WESTREX					0x0079
#define RTP_MIDI_MANU_LONG_NVIDIA					0x007a
#define RTP_MIDI_MANU_LONG_ESS_TECHNOLOGY				0x007b
#define RTP_MIDI_MANU_LONG_MEDIATRIX_PERIPHERALS			0x007c
#define RTP_MIDI_MANU_LONG_BROOKTREE_CORP				0x007d
#define RTP_MIDI_MANU_LONG_OTARI_CORP					0x007e
#define RTP_MIDI_MANU_LONG_KEY_ELECTRONICS				0x007f
#define RTP_MIDI_MANU_LONG_SHURE_INCORPORATED				0x0100
#define RTP_MIDI_MANU_LONG_AURA_SOUND					0x0101
#define RTP_MIDI_MANU_LONG_CRYSTAL_SEMICONDUCTOR			0x0102
#define RTP_MIDI_MANU_LONG_CONEXANT_ROCKWELL				0x0103
#define RTP_MIDI_MANU_LONG_SILICON_GRAPHICS				0x0104
#define RTP_MIDI_MANU_LONG_MAUDIO_MIDIMAN				0x0105
#define RTP_MIDI_MANU_LONG_PRESONUS					0x0106
#define RTP_MIDI_MANU_LONG_TOPAZ_ENTERPRISES				0x0108
#define RTP_MIDI_MANU_LONG_CAST_LIGHTING				0x0109
#define RTP_MIDI_MANU_LONG_MICROSOFT_CONSUMER_DIVISION			0x010a
#define RTP_MIDI_MANU_LONG_SONIC_FOUNDRY				0x010b
#define RTP_MIDI_MANU_LONG_LINE6_FAST_FORWARD				0x010c
#define RTP_MIDI_MANU_LONG_BEATNIK_INC					0x010d
#define RTP_MIDI_MANU_LONG_VAN_KOEVERING_COMPANY			0x010e
#define RTP_MIDI_MANU_LONG_ALTECH_SYSTEMS				0x010f
#define RTP_MIDI_MANU_LONG_S_S_RESEARCH					0x0110
#define RTP_MIDI_MANU_LONG_VLSI_TECHNOLOGY				0x0111
#define RTP_MIDI_MANU_LONG_CHROMATIC_RESEARCH				0x0112
#define RTP_MIDI_MANU_LONG_SAPPHIRE					0x0113
#define RTP_MIDI_MANU_LONG_IDRC						0x0114
#define RTP_MIDI_MANU_LONG_JUSTONIC_TUNING				0x0115
#define RTP_MIDI_MANU_LONG_TORCOMP_RESEARCH_INC				0x0116
#define RTP_MIDI_MANU_LONG_NEWTEK_INC					0x0117
#define RTP_MIDI_MANU_LONG_SOUND_SCULPTURE				0x0118
#define RTP_MIDI_MANU_LONG_WALKER_TECHNICAL				0x0119
#define RTP_MIDI_MANU_LONG_DIGITAL_HARMONY				0x011a
#define RTP_MIDI_MANU_LONG_INVISION_INTERACTIVE				0x011b
#define RTP_MIDI_MANU_LONG_TSQUARE_DESIGN				0x011c
#define RTP_MIDI_MANU_LONG_NEMESYS_MUSIC_TECHNOLOGY			0x011d
#define RTP_MIDI_MANU_LONG_DBX_PROFESSIONAL_HARMAN_INTL			0x011e
#define RTP_MIDI_MANU_LONG_SYNDYNE_CORPORATION				0x011f
#define RTP_MIDI_MANU_LONG_BITHEADZ					0x0120
#define RTP_MIDI_MANU_LONG_CAKEWALK_MUSIC_SOFTWARE			0x0121
#define RTP_MIDI_MANU_LONG_ANALOG_DEVICES				0x0122
#define RTP_MIDI_MANU_LONG_NATIONAL_SEMICONDUCTOR			0x0123
#define RTP_MIDI_MANU_LONG_BOOM_THEORY					0x0124
#define RTP_MIDI_MANU_LONG_VIRTUAL_DSP_CORPORATION			0x0125
#define RTP_MIDI_MANU_LONG_ANTARES_SYSTEMS				0x0126
#define RTP_MIDI_MANU_LONG_ANGEL_SOFTWARE				0x0127
#define RTP_MIDI_MANU_LONG_ST_LOUIS_MUSIC				0x0128
#define RTP_MIDI_MANU_LONG_LYRRUS_DBA_GVOX				0x0129
#define RTP_MIDI_MANU_LONG_ASHLEY_AUDIO_INC				0x012a
#define RTP_MIDI_MANU_LONG_VARILITE_INC					0x012b
#define RTP_MIDI_MANU_LONG_SUMMIT_AUDIO_INC				0x012c
#define RTP_MIDI_MANU_LONG_AUREAL_SEMICONDUCTOR_INC			0x012d
#define RTP_MIDI_MANU_LONG_SEASOUND_LLC					0x012e
#define RTP_MIDI_MANU_LONG_US_ROBOTICS					0x012f
#define RTP_MIDI_MANU_LONG_AURISIS_RESEARCH				0x0130
#define RTP_MIDI_MANU_LONG_NEARFIELD_MULTIMEDIA				0x0131
#define RTP_MIDI_MANU_LONG_FM7_INC					0x0132
#define RTP_MIDI_MANU_LONG_SWIVEL_SYSTEMS				0x0133
#define RTP_MIDI_MANU_LONG_HYPERACTIVE_AUDIO_SYSTEMS			0x0134
#define RTP_MIDI_MANU_LONG_MIDILITE_CASTE_STUDIO_PROD			0x0135
#define RTP_MIDI_MANU_LONG_RADIKAL_TECHNOLOGIES				0x0136
#define RTP_MIDI_MANU_LONG_ROGER_LINN_DESIGN				0x0137
#define RTP_MIDI_MANU_LONG_TCHELION_VOCAL_TECHNOLOGIES			0x0138
#define RTP_MIDI_MANU_LONG_EVENT_ELECTRONICS				0x0139
#define RTP_MIDI_MANU_LONG_SONIC_NETWORK_INC				0x013a
#define RTP_MIDI_MANU_LONG_REALTIME_MUSIC_SOLUTIONS			0x013b
#define RTP_MIDI_MANU_LONG_APOGEE_DIGITAL				0x013c
#define RTP_MIDI_MANU_LONG_CLASSICAL_ORGANS_INC				0x013d
#define RTP_MIDI_MANU_LONG_MICROTOOLS_INC				0x013e
#define RTP_MIDI_MANU_LONG_NUMARK_INDUSTRIES				0x013f
#define RTP_MIDI_MANU_LONG_FRONTIER_DESIGN_GROUP_LLC			0x0140
#define RTP_MIDI_MANU_LONG_RECORDARE_LLC				0x0141
#define RTP_MIDI_MANU_LONG_STARR_LABS					0x0142
#define RTP_MIDI_MANU_LONG_VOYAGER_SOUND_INC				0x0143
#define RTP_MIDI_MANU_LONG_MANIFOLD_LABS				0x0144
#define RTP_MIDI_MANU_LONG_AVIOM_INC					0x0145
#define RTP_MIDI_MANU_LONG_MIXMEISTER_TECHNOLOGY			0x0146
#define RTP_MIDI_MANU_LONG_NOTATION_SOFTWARE				0x0147
#define RTP_MIDI_MANU_LONG_MERCURIAL_COMMUNICATIONS			0x0148
#define RTP_MIDI_MANU_LONG_WAVE_ARTS					0x0149
#define RTP_MIDI_MANU_LONG_LOGIC_SEQUENCING_DEVICES			0x014a
#define RTP_MIDI_MANU_LONG_AXESS_ELECTRONICS				0x014b
#define RTP_MIDI_MANU_LONG_MUSE_RESEARCH				0x014c
#define RTP_MIDI_MANU_LONG_OPEN_LABS					0x014d
#define RTP_MIDI_MANU_LONG_GUILLEMOT_RD_INC				0x014e
#define RTP_MIDI_MANU_LONG_SAMSON_TECHNOLOGIES				0x014f
#define RTP_MIDI_MANU_LONG_ELECTRONIC_THEATRE_CONTROLS			0x0150
#define RTP_MIDI_MANU_LONG_RESEARCH_IN_MOTION				0x0151
#define RTP_MIDI_MANU_LONG_MOBILEER					0x0152
#define RTP_MIDI_MANU_LONG_SYNTHOGY					0x0153
#define RTP_MIDI_MANU_LONG_LYNX_STUDIO_TECHNOLOGY_INC			0x0154
#define RTP_MIDI_MANU_LONG_DAMAGE_CONTROL_ENGINEERING			0x0155
#define RTP_MIDI_MANU_LONG_YOST_ENGINEERING_INC				0x0156
#define RTP_MIDI_MANU_LONG_BROOKS_FORSMAN_DESIGNS_LLC			0x0157
#define RTP_MIDI_MANU_LONG_MAGNEKEY					0x0158
#define RTP_MIDI_MANU_LONG_GARRITAN_CORP				0x0159
#define RTP_MIDI_MANU_LONG_PLOQUE_ART_ET_TECHNOLOGIE			0x015a
#define RTP_MIDI_MANU_LONG_RJM_MUSIC_TECHNOLOGY				0x015b
#define RTP_MIDI_MANU_LONG_CUSTOM_SOLUTIONS_SOFTWARE			0x015c
#define RTP_MIDI_MANU_LONG_SONARCANA_LLC				0x015d
#define RTP_MIDI_MANU_LONG_CENTRANCE					0x015e
#define RTP_MIDI_MANU_LONG_KESUMO_LLC					0x015f
#define RTP_MIDI_MANU_LONG_STANTON					0x0160
#define RTP_MIDI_MANU_LONG_LIVID_INSTRUMENTS				0x0161
#define RTP_MIDI_MANU_LONG_FIRST_ACT_745_MEDIA				0x0162
#define RTP_MIDI_MANU_LONG_PYGRAPHICS_INC				0x0163
#define RTP_MIDI_MANU_LONG_PANADIGM_INNOVATIONS_LTD			0x0164
#define RTP_MIDI_MANU_LONG_AVEDIS_ZILDJIAN_CO				0x0165
#define RTP_MIDI_MANU_LONG_AUVITAL_MUSIC_CORP				0x0166
#define RTP_MIDI_MANU_LONG_INSPIRED_INSTRUMENTS_INC			0x0167
#define RTP_MIDI_MANU_LONG_CHRIS_GRIGG_DESIGNS				0x0168
#define RTP_MIDI_MANU_LONG_SLATE_DIGITAL_LLC				0x0169
#define RTP_MIDI_MANU_LONG_MIXWARE					0x016a
#define RTP_MIDI_MANU_LONG_SOCIAL_ENTROPY				0x016b
#define RTP_MIDI_MANU_LONG_SOURCE_AUDIO_LLC				0x016c
#define RTP_MIDI_MANU_LONG_RESERVED_016d				0x016d
#define RTP_MIDI_MANU_LONG_RESERVED_016e				0x016e
#define RTP_MIDI_MANU_LONG_RESERVED_016f				0x016f
#define RTP_MIDI_MANU_LONG_AMERICAN_AUDIO_DJ				0x0170
#define RTP_MIDI_MANU_LONG_MEGA_CONTROL_SYSTEMS				0x0171
#define RTP_MIDI_MANU_LONG_KILPATRICK_AUDIO				0x0172
#define RTP_MIDI_MANU_LONG_IKINGDOM_CORP				0x0173
#define RTP_MIDI_MANU_LONG_FRACTAL_AUDIO				0x0174
#define RTP_MIDI_MANU_LONG_NETLOGIC_MICROSYSTEMS			0x0175
#define RTP_MIDI_MANU_LONG_MUSIC_COMPUTING				0x0176
#define RTP_MIDI_MANU_LONG_NEKTAR_TECHNOLOGY_INC			0x0177
#define RTP_MIDI_MANU_LONG_ZENPH_SOUND_INNOVATIONS			0x0178
#define RTP_MIDI_MANU_LONG_DJTECHTOOLS_COM				0x0179
#define RTP_MIDI_MANU_LONG_RESERVED_017a				0x017a
#define RTP_MIDI_MANU_LONG_DREAM					0x2000
#define RTP_MIDI_MANU_LONG_STRAND_LIGHTING				0x2001
#define RTP_MIDI_MANU_LONG_AMEK_SYSTEMS					0x2002
#define RTP_MIDI_MANU_LONG_CASA_DI_RISPARMIO_DI_LORETO			0x2003
#define RTP_MIDI_MANU_LONG_BOHM_ELECTRONIC				0x2004
#define RTP_MIDI_MANU_LONG_SYNTEC_DIGITAL_AUDIO				0x2005
#define RTP_MIDI_MANU_LONG_TRIDENT_AUDIO				0x2006
#define RTP_MIDI_MANU_LONG_REAL_WORLD_STUDIO				0x2007
#define RTP_MIDI_MANU_LONG_EVOLUTION_SYNTHESIS				0x2008
#define RTP_MIDI_MANU_LONG_YES_TECHNOLOGY				0x2009
#define RTP_MIDI_MANU_LONG_AUDIOMATICA					0x200a
#define RTP_MIDI_MANU_LONG_BONTEMPI_FARFISA				0x200b
#define RTP_MIDI_MANU_LONG_FBT_ELETTRONICA				0x200c
#define RTP_MIDI_MANU_LONG_MIDITEMP					0x200d
#define RTP_MIDI_MANU_LONG_LA_AUDIO_LARKING_AUDIO			0x200e
#define RTP_MIDI_MANU_LONG_ZERO_88_LIGHTING_LIMITED			0x200f
#define RTP_MIDI_MANU_LONG_MICON_AUDIO_ELECTRONICS_GMBH			0x2010
#define RTP_MIDI_MANU_LONG_FOREFRONT_TECHNOLOGY				0x2011
#define RTP_MIDI_MANU_LONG_STUDIO_AUDIO_AND_VIDEO_LTD			0x2012
#define RTP_MIDI_MANU_LONG_KENTON_ELECTRONICS				0x2013
#define RTP_MIDI_MANU_LONG_CELCO_DIVISON_OF_ELECTRONICS			0x2014
#define RTP_MIDI_MANU_LONG_ADB						0x2015
#define RTP_MIDI_MANU_LONG_MARSHALL_PRODUCTS				0x2016
#define RTP_MIDI_MANU_LONG_DDA						0x2017
#define RTP_MIDI_MANU_LONG_BBS						0x2018
#define RTP_MIDI_MANU_LONG_MA_LIGHTING_TECHNOLOGY			0x2019
#define RTP_MIDI_MANU_LONG_FATAR					0x201a
#define RTP_MIDI_MANU_LONG_QSC_AUDIO					0x201b
#define RTP_MIDI_MANU_LONG_ARTISAN_CLASSIC_ORGAN			0x201c
#define RTP_MIDI_MANU_LONG_ORLA_SPA					0x201d
#define RTP_MIDI_MANU_LONG_PINNACLE_AUDIO				0x201e
#define RTP_MIDI_MANU_LONG_TC_ELECTRONICS				0x201f
#define RTP_MIDI_MANU_LONG_DOEPFER_MUSIKELEKTRONIK			0x2020
#define RTP_MIDI_MANU_LONG_CREATIVE_TECHNOLOGY_PTE			0x2021
#define RTP_MIDI_MANU_LONG_MINAMI_SEIYDDO				0x2022
#define RTP_MIDI_MANU_LONG_GOLDSTAR					0x2023
#define RTP_MIDI_MANU_LONG_MIDISOFT_SAS_DI_M_CIMA			0x2024
#define RTP_MIDI_MANU_LONG_SAMICK					0x2025
#define RTP_MIDI_MANU_LONG_PENNY_AND_GILES				0x2026
#define RTP_MIDI_MANU_LONG_ACORN_COMPUTER				0x2027
#define RTP_MIDI_MANU_LONG_LSC_ELECTRONICS				0x2028
#define RTP_MIDI_MANU_LONG_NOVATION_EMS					0x2029
#define RTP_MIDI_MANU_LONG_SAMKYUNG_MECHATRONICS			0x202a
#define RTP_MIDI_MANU_LONG_MEDELI_ELECTRONICS_CO			0x202b
#define RTP_MIDI_MANU_LONG_CHARLIE_LAB_SRL				0x202c
#define RTP_MIDI_MANU_LONG_BLUE_CHIP_MUSIC_TECHNOLOGY			0x202d
#define RTP_MIDI_MANU_LONG_BEE_OH_CORP					0x202e
#define RTP_MIDI_MANU_LONG_LG_SEMICON_AMERICA				0x202f
#define RTP_MIDI_MANU_LONG_TESI						0x2030
#define RTP_MIDI_MANU_LONG_EMAGIC					0x2031
#define RTP_MIDI_MANU_LONG_BEHRINGER_GMBH				0x2032
#define RTP_MIDI_MANU_LONG_ACCESS_MUSIC_ELECTRONICS			0x2033
#define RTP_MIDI_MANU_LONG_SYNOPTIC					0x2034
#define RTP_MIDI_MANU_LONG_HANMESOFT_CORP				0x2035
#define RTP_MIDI_MANU_LONG_TERRATEC_ELECTRONIC_GMBH			0x2036
#define RTP_MIDI_MANU_LONG_PROEL_SPA					0x2037
#define RTP_MIDI_MANU_LONG_IBK_MIDI					0x2038
#define RTP_MIDI_MANU_LONG_IRCAM					0x2039
#define RTP_MIDI_MANU_LONG_PROPELLERHEAD_SOFTWARE			0x203a
#define RTP_MIDI_MANU_LONG_RED_SOUND_SYSTEMS_LTD			0x203b
#define RTP_MIDI_MANU_LONG_ELEKTRON_ESI_AB				0x203c
#define RTP_MIDI_MANU_LONG_SINTEFEX_AUDIO				0x203d
#define RTP_MIDI_MANU_LONG_MAM_MUSIC_AND_MORE				0x203e
#define RTP_MIDI_MANU_LONG_AMSARO_GMBH					0x203f
#define RTP_MIDI_MANU_LONG_CDS_ADVANCED_TECHNOLOGY_BV			0x2040
#define RTP_MIDI_MANU_LONG_TOUCHED_BY_SOUND_GMBH			0x2041
#define RTP_MIDI_MANU_LONG_DSP_ARTS					0x2042
#define RTP_MIDI_MANU_LONG_PHIL_REES_MUSIC_TECH				0x2043
#define RTP_MIDI_MANU_LONG_STAMER_MUSIKANLAGEN_GMBH			0x2044
#define RTP_MIDI_MANU_LONG_MUSICAL_MUNTANER_SA_DBA			0x2045
#define RTP_MIDI_MANU_LONG_CMEXX_SOFTWARE				0x2046
#define RTP_MIDI_MANU_LONG_KLAVIS_TECHNOLOGIES				0x2047
#define RTP_MIDI_MANU_LONG_NOTEHEADS_AB					0x2048
#define RTP_MIDI_MANU_LONG_ALGORITHMIX					0x2049
#define RTP_MIDI_MANU_LONG_SKRYDSTRUP_RD				0x204a
#define RTP_MIDI_MANU_LONG_PROFRESSIONAL_AUDIO_COMPANY			0x204b
#define RTP_MIDI_MANU_LONG_DBTECH_MADWAVES				0x204c
#define RTP_MIDI_MANU_LONG_VERMONA					0x204d
#define RTP_MIDI_MANU_LONG_NOKIA					0x204e
#define RTP_MIDI_MANU_LONG_WAVE_IDEA					0x204f
#define RTP_MIDI_MANU_LONG_HARTMANN_GMBH				0x2050
#define RTP_MIDI_MANU_LONG_LIONS_TRACK					0x2051
#define RTP_MIDI_MANU_LONG_ANALOGUE_SYSTEMS				0x2052
#define RTP_MIDI_MANU_LONG_FOCAL_JMLAB					0x2053
#define RTP_MIDI_MANU_LONG_RINGWAY_ELECTRONICS				0x2054
#define RTP_MIDI_MANU_LONG_FAITH_TECHNOLOGIES_DIGIPLUG			0x2055
#define RTP_MIDI_MANU_LONG_SHOWWORKS					0x2056
#define RTP_MIDI_MANU_LONG_MANIKIN_ELECTRONIC				0x2057
#define RTP_MIDI_MANU_LONG_1_COME_TECH					0x2058
#define RTP_MIDI_MANU_LONG_PHONIC_CORP					0x2059
#define RTP_MIDI_MANU_LONG_LAKE_TECHNOLOGY				0x205a
#define RTP_MIDI_MANU_LONG_SILANSYS_TECHNOLOGIES			0x205b
#define RTP_MIDI_MANU_LONG_WINBOND_ELECTRONICS				0x205c
#define RTP_MIDI_MANU_LONG_CINETIX_MEDIEN_UND_INTERFACE			0x205d
#define RTP_MIDI_MANU_LONG_AG_SOLUTIONI_DIGITALI			0x205e
#define RTP_MIDI_MANU_LONG_SEQUENTIX_MUSIC_SYSTEMS			0x205f
#define RTP_MIDI_MANU_LONG_ORAM_PRO_AUDIO				0x2060
#define RTP_MIDI_MANU_LONG_BE4_LTD					0x2061
#define RTP_MIDI_MANU_LONG_INFECTION_MUSIC				0x2062
#define RTP_MIDI_MANU_LONG_CENTRAL_MUSIC_CO_CME				0x2063
#define RTP_MIDI_MANU_LONG_GENOQS_MACHINES				0x2064
#define RTP_MIDI_MANU_LONG_MEDIALON					0x2065
#define RTP_MIDI_MANU_LONG_WAVES_AUDIO_LTD				0x2066
#define RTP_MIDI_MANU_LONG_JERASH_LABS					0x2067
#define RTP_MIDI_MANU_LONG_DA_FACT					0x2068
#define RTP_MIDI_MANU_LONG_ELBY_DESIGNS					0x2069
#define RTP_MIDI_MANU_LONG_SPECTRAL_AUDIO				0x206a
#define RTP_MIDI_MANU_LONG_ARTURIA					0x206b
#define RTP_MIDI_MANU_LONG_VIXID					0x206c
#define RTP_MIDI_MANU_LONG_C_THRU_MUSIC					0x206d
#define RTP_MIDI_MANU_LONG_YA_HORNG_ELECTRONIC_CO_LTD			0x206e
#define RTP_MIDI_MANU_LONG_SM_PRO_AUDIO					0x206f
#define RTP_MIDI_MANU_LONG_OTO_MACHINES					0x2070
#define RTP_MIDI_MANU_LONG_ELZAB_SA_G_LAB				0x2071
#define RTP_MIDI_MANU_LONG_BLACKSTAR_AMPLIFICATION_LTD			0x2072
#define RTP_MIDI_MANU_LONG_M3I_TECHNOLOGIES_GMBH			0x2073
#define RTP_MIDI_MANU_LONG_GEMALTO					0x2074
#define RTP_MIDI_MANU_LONG_PROSTAGE_SL					0x2075
#define RTP_MIDI_MANU_LONG_TEENAGE_ENGINEERING				0x2076
#define RTP_MIDI_MANU_LONG_TOBIAS_ERICHSEN				0x2077
#define RTP_MIDI_MANU_LONG_NIXER_LTD					0x2078
#define RTP_MIDI_MANU_LONG_HANPIN_ELECTRON_CO_LTD			0x2079
#define RTP_MIDI_MANU_LONG_MIDI_HARDWARE_R_SOWA				0x207a
#define RTP_MIDI_MANU_LONG_BEYOND_MUSIC_INDUSTRIAL_LTD			0x207b
#define RTP_MIDI_MANU_LONG_KISS_BOX_BV					0x207c
#define RTP_MIDI_MANU_LONG_MISA_DIGITAL_TECHNOLOGIES_LTD		0x207d
#define RTP_MIDI_MANU_LONG_AI_MUSICS_TECHNOLOGY_INC			0x207e
#define RTP_MIDI_MANU_LONG_SERATO_INC_LP				0x207f
#define RTP_MIDI_MANU_LONG_LIMEX_MUSIC_HANDLES_GMBH			0x2100
#define RTP_MIDI_MANU_LONG_KYODDAY_TOKAI				0x2101
#define RTP_MIDI_MANU_LONG_MUTABLE_INSTRUMENTS				0x2102
#define RTP_MIDI_MANU_LONG_PRESONUS_SOFTWARE_LTD			0x2103
#define RTP_MIDI_MANU_LONG_XIRING					0x2104
#define RTP_MIDI_MANU_LONG_FAIRLIGHT_INTRUMENTS_PTY_LTD			0x2105
#define RTP_MIDI_MANU_LONG_MUSICOM_LAB					0x2106
#define RTP_MIDI_MANU_LONG_VACO_LOCO					0x2107
#define RTP_MIDI_MANU_LONG_RWA_HONG_KONG_LIMITED			0x2108
#define RTP_MIDI_MANU_LONG_CRIMSON_TECHNOLOGY_INC			0x4000
#define RTP_MIDI_MANU_LONG_SOFTBANK_MOBILE_CORP				0x4001
#define RTP_MIDI_MANU_LONG_DM_HOLDINGS_INC				0x4003
#define RTP_MIDI_SYSEX_COMMON_NRT_DLS_LEVEL1_ON				0x01
#define RTP_MIDI_SYSEX_COMMON_NRT_DLS_LEVEL1_OFF			0x02
#define RTP_MIDI_SYSEX_COMMON_NRT_DLS_LEVEL1_VOICE_ALLOCATION_OFF	0x03
#define RTP_MIDI_SYSEX_COMMON_NRT_DLS_LEVEL1_VOICE_ALLOCATION_ON	0x04
#define RTP_MIDI_SYSEX_COMMON_NRT_MTC_SPECIAL				0x00
#define RTP_MIDI_SYSEX_COMMON_NRT_MTC_PUNCH_IN_POINTS			0x01
#define RTP_MIDI_SYSEX_COMMON_NRT_MTC_PUNGH_OUT_POINTS			0x02
#define RTP_MIDI_SYSEX_COMMON_NRT_MTC_DELETE_PUNCH_IN_POINTS		0x03
#define RTP_MIDI_SYSEX_COMMON_NRT_MTC_DELETE_PUNCH_OUT_POINTS		0x04
#define RTP_MIDI_SYSEX_COMMON_NRT_MTC_EVENT_START_POINT			0x05
#define RTP_MIDI_SYSEX_COMMON_NRT_MTC_EVENT_STOP_POINT			0x06
#define RTP_MIDI_SYSEX_COMMON_NRT_MTC_EVENT_START_POINT_ADD		0x07
#define RTP_MIDI_SYSEX_COMMON_NRT_MTC_EVENT_STOP_POINT_ADD		0x08
#define RTP_MIDI_SYSEX_COMMON_NRT_MTC_DELETE_EVENT_START_POINT		0x09
#define RTP_MIDI_SYSEX_COMMON_NRT_MTC_DELETE_EVENT_STOP_POINT		0x0a
#define RTP_MIDI_SYSEX_COMMON_NRT_MTC_CUE_POINTS			0x0b
#define RTP_MIDI_SYSEX_COMMON_NRT_MTC_CUE_POINTS_ADD			0x0c
#define RTP_MIDI_SYSEX_COMMON_NRT_MTC_DELETE_CUE_POINT			0x0d
#define RTP_MIDI_SYSEX_COMMON_NRT_MTC_EVENT_NAME_IN_ADD			0x0e
#define RTP_MIDI_SYSEX_COMMON_NRT_SD_EXT_LOOP_POINT_TRANSMISSION	0x01
#define RTP_MIDI_SYSEX_COMMON_NRT_SD_EXT_LOOP_POINTS_REQUEST		0x02
#define RTP_MIDI_SYSEX_COMMON_NRT_SD_EXT_LP_UNI				0x00
#define RTP_MIDI_SYSEX_COMMON_NRT_SD_EXT_LP_BI				0x01
#define RTP_MIDI_SYSEX_COMMON_NRT_SD_EXT_LP_OFF				0x7f
#define RTP_MIDI_SYSEX_COMMON_NRT_GI_IDENTITY_REQUEST			0x01
#define RTP_MIDI_SYSEX_COMMON_NRT_GI_IDENTITY_REPLY			0x02
#define RTP_MIDI_SYSEX_COMMON_NRT_FD_HEADER				0x01
#define RTP_MIDI_SYSEX_COMMON_NRT_FD_DATA_PACKET			0x02
#define RTP_MIDI_SYSEX_COMMON_NRT_FD_REQUEST				0x03
#define RTP_MIDI_SYSEX_COMMON_TUNING_BULK_DUMP_REQUEST			0x00
#define RTP_MIDI_SYSEX_COMMON_TUNING_BULK_DUMP_REPLY			0x01
#define RTP_MIDI_SYSEX_COMMON_TUNING_NOTE_CHANGE			0x02
#define RTP_MIDI_SYSEX_COMMON_NRT_GM_ON					0x01
#define RTP_MIDI_SYSEX_COMMON_NRT_GM_OFF				0x02
#define RTP_MIDI_SYSEX_COMMON_RT_MTC_FULL_MESSAGE			0x01
#define RTP_MIDI_SYSEX_COMMON_RT_MTC_USER_BITS				0x02
#define RTP_MIDI_SYSEX_COMMON_RT_SCL_EXTENSIONS				0x00
#define RTP_MIDI_SYSEX_COMMON_RT_SC_LIGHTING				0x01
#define RTP_MIDI_SYSEX_COMMON_RT_SC_MOVING_LIGHTS			0x02
#define RTP_MIDI_SYSEX_COMMON_RT_SC_COLOR_CHANGERS			0x03
#define RTP_MIDI_SYSEX_COMMON_RT_SC_STROBES				0x04
#define RTP_MIDI_SYSEX_COMMON_RT_SCL_LASERS				0x05
#define RTP_MIDI_SYSEX_COMMON_RT_SCL_CHASERS				0x06
#define RTP_MIDI_SYSEX_COMMON_RT_SC_SOUND				0x10
#define RTP_MIDI_SYSEX_COMMON_RT_SC_MUSIC				0x11
#define RTP_MIDI_SYSEX_COMMON_RT_SC_CD_PLAYERS				0x12
#define RTP_MIDI_SYSEX_COMMON_RT_SC_EPROM_PLAYBACK			0x13
#define RTP_MIDI_SYSEX_COMMON_RT_SCL_AUDIO_TAPE_MACHINE			0x14
#define RTP_MIDI_SYSEX_COMMON_RT_SC_INTERCOMS				0x15
#define RTP_MIDI_SYSEX_COMMON_RT_SC_AMPLIFIERS				0x16
#define RTP_MIDI_SYSEX_COMMON_RT_SC_AUDIO_EFFECTS			0x17
#define RTP_MIDI_SYSEX_COMMON_RT_SC_EQUALIZERS				0x18
#define RTP_MIDI_SYSEX_COMMON_RT_SC_MACHINERY				0x20
#define RTP_MIDI_SYSEX_COMMON_RT_SC_RIGGING				0x21
#define RTP_MIDI_SYSEX_COMMON_RT_SC_FLYS				0x22
#define RTP_MIDI_SYSEX_COMMON_RT_SC_LIFTS				0x23
#define RTP_MIDI_SYSEX_COMMON_RT_SC_TURNTABLES				0x24
#define RTP_MIDI_SYSEX_COMMON_RT_SC_TRUSSES				0x25
#define RTP_MIDI_SYSEX_COMMON_RT_SC_ROBOTS				0x26
#define RTP_MIDI_SYSEX_COMMON_RT_SC_ANIMATION				0x27
#define RTP_MIDI_SYSEX_COMMON_RT_SC_FLOATS				0x28
#define RTP_MIDI_SYSEX_COMMON_RT_SC_BREAKAWAYS				0x29
#define RTP_MIDI_SYSEX_COMMON_RT_SC_BARGES				0x2a
#define RTP_MIDI_SYSEX_COMMON_RT_SC_VIDEO				0x30
#define RTP_MIDI_SYSEX_COMMON_RT_SC_VIDEO_TAPE_MACHINES			0x31
#define RTP_MIDI_SYSEX_COMMON_RT_SC_VIDEO_CASSETTE_MACHINES		0x32
#define RTP_MIDI_SYSEX_COMMON_RT_SC_VIDEO_DISC_PLAYERS			0x33
#define RTP_MIDI_SYSEX_COMMON_RT_SC_VIDEO_SWITCHERS			0x34
#define RTP_MIDI_SYSEX_COMMON_RT_SC_VIDEO_EFFECT			0x35
#define RTP_MIDI_SYSEX_COMMON_RT_SC_VIDEO_CHARACTER_GENERATORS		0x36
#define RTP_MIDI_SYSEX_COMMON_RT_SC_VIDEO_STIL_STORES			0x37
#define RTP_MIDI_SYSEX_COMMON_RT_SC_VIDEO_MONITORS			0x38
#define RTP_MIDI_SYSEX_COMMON_RT_SC_PROJECTION				0x40
#define RTP_MIDI_SYSEX_COMMON_RT_SC_FILM_PROJECTORS			0x41
#define RTP_MIDI_SYSEX_COMMON_RT_SC_SLIDE_PROJECTORS			0x42
#define RTP_MIDI_SYSEX_COMMON_RT_SC_VIDEO_PROJECTORS			0x43
#define RTP_MIDI_SYSEX_COMMON_RT_SC_DISSOLVERS				0x44
#define RTP_MIDI_SYSEX_COMMON_RT_SC_SHUTTER_CONTROLS			0x45
#define RTP_MIDI_SYSEX_COMMON_RT_SC_PROCESS_CONTROL			0x50
#define RTP_MIDI_SYSEX_COMMON_RT_SC_HYDRAULIC_OIL			0x51
#define RTP_MIDI_SYSEX_COMMON_RT_SC_H2O					0x52
#define RTP_MIDI_SYSEX_COMMON_RT_SC_CO2					0x53
#define RTP_MIDI_SYSEX_COMMON_RT_SC_COMPRESSED_AIR			0x54
#define RTP_MIDI_SYSEX_COMMON_RT_SC_NATURAL_GAS				0x55
#define RTP_MIDI_SYSEX_COMMON_RT_SC_FOG					0x56
#define RTP_MIDI_SYSEX_COMMON_RT_SC_SMOKE				0x57
#define RTP_MIDI_SYSEX_COMMON_RT_SC_CRACKED_HAZE			0x58
#define RTP_MIDI_SYSEX_COMMON_RT_SC_PYRO				0x60
#define RTP_MIDI_SYSEX_COMMON_RT_SC_FIREWORKS				0x61
#define RTP_MIDI_SYSEX_COMMON_RT_SC_EXPLOSIONS				0x62
#define RTP_MIDI_SYSEX_COMMON_RT_SC_FLAME				0x63
#define RTP_MIDI_SYSEX_COMMON_RT_SC_SMOKE_POTS				0x64
#define RTP_MIDI_SYSEX_COMMON_RT_SC_ALL_TYPES				0x7f
#define RTP_MIDI_SYSEX_COMMON_RT_NT_BAR_NUMBER				0x01
#define RTP_MIDI_SYSEX_COMMON_RT_NT_TIME_SIGNATURE_IMMEDIATE		0x02
#define RTP_MIDI_SYSEX_COMMON_RT_NT_TIME_SIGNATURE_DELAYED		0x42
#define RTP_MIDI_SYSEX_COMMON_RT_DC_MASTER_VOLUME			0x01
#define RTP_MIDI_SYSEX_COMMON_RT_DC_MASTER_BALANCE			0x02
#define RTP_MIDI_SYSEX_COMMON_RT_MTC_CUE_SPECIAL			0x00
#define RTP_MIDI_SYSEX_COMMON_RT_MTC_CUE_PUNCH_IN_POINTS		0x01
#define RTP_MIDI_SYSEX_COMMON_RT_MTC_CUE_PUNCH_OUT_POINTS		0x02
#define RTP_MIDI_SYSEX_COMMON_RT_MTC_CUE_RESERVED_03			0x03
#define RTP_MIDI_SYSEX_COMMON_RT_MTC_CUE_RESERVED_04			0x04
#define RTP_MIDI_SYSEX_COMMON_RT_MTC_CUE_EVENT_START_POINTS		0x05
#define RTP_MIDI_SYSEX_COMMON_RT_MTC_CUE_EVENT_STOP_POINTS		0x06
#define RTP_MIDI_SYSEX_COMMON_RT_MTC_CUE_EVENT_START_POINTS_ADD		0x07
#define RTP_MIDI_SYSEX_COMMON_RT_MTC_CUE_EVENT_STOP_POINTS_ADD		0x08
#define RTP_MIDI_SYSEX_COMMON_RT_MTC_CUE_RESERVED_09			0x09
#define RTP_MIDI_SYSEX_COMMON_RT_MTC_CUE_RESERVED_0A			0x0a
#define RTP_MIDI_SYSEX_COMMON_RT_MTC_CUE_CUE_POINTS			0x0b
#define RTP_MIDI_SYSEX_COMMON_RT_MTC_CUE_CUE_POINTS_ADD			0x0c
#define RTP_MIDI_SYSEX_COMMON_RT_MTC_CUE_RESERVED_0D			0x0d
#define RTP_MIDI_SYSEX_COMMON_RT_MTC_CUE_EVENT_NAME_ADD			0x0e
#define RTP_MIDI_COMMON_MTC_QF_FRAME_LS_NIBBLE				0x00
#define RTP_MIDI_COMMON_MTC_QF_FRAME_MS_NIBBLE				0x01
#define RTP_MIDI_COMMON_MTC_QF_SECONDS_LS_NIBBLE			0x02
#define RTP_MIDI_COMMON_MTC_QF_SECONDS_MS_NIBBLE			0x03
#define RTP_MIDI_COMMON_MTC_QF_MINUTES_LS_NIBBLE			0x04
#define RTP_MIDI_COMMON_MTC_QF_MINUTES_MS_NIBBLE			0x05
#define RTP_MIDI_COMMON_MTC_QF_HOURS_LS_NIBBLE				0x06
#define RTP_MIDI_COMMON_MTC_QF_HOURS_MS_NIBBLE				0x07
#define RTP_MIDI_TREE_NAME_COMMAND					"Command Section"
#define RTP_MIDI_TREE_NAME_COMMAND_SYSEX_MANU				"Manufacturer specific data"
#define RTP_MIDI_TREE_NAME_COMMAND_SYSEX_EDU				"Educational data"
#define RTP_MIDI_TREE_NAME_JOURNAL					"Journal Section"
#define RTP_MIDI_TREE_NAME_SYSTEM_JOURNAL				"System-Journal"
#define RTP_MIDI_TREE_NAME_SYSTEM_CHAPTERS				"System-Chapters"
#define RTP_MIDI_TREE_NAME_SJ_CHAPTER_D					"Simple System Commands"
#define RTP_MIDI_TREE_NAME_SJ_CHAPTER_D_FIELD_B				"Reset Field"
#define RTP_MIDI_TREE_NAME_SJ_CHAPTER_D_FIELD_G				"Tune Request Field"
#define RTP_MIDI_TREE_NAME_SJ_CHAPTER_D_FIELD_H				"Song Select Field"
#define RTP_MIDI_TREE_NAME_SJ_CHAPTER_D_FIELD_J				"System Common 0xF4"
#define RTP_MIDI_TREE_NAME_SJ_CHAPTER_D_FIELD_K				"System Common 0xF5"
#define RTP_MIDI_TREE_NAME_SJ_CHAPTER_D_FIELD_Y				"System Common 0xF9"
#define RTP_MIDI_TREE_NAME_SJ_CHAPTER_D_FIELD_Z				"System Common 0xFD"
#define RTP_MIDI_TREE_NAME_SJ_CHAPTER_V					"Active Sensing"
#define RTP_MIDI_TREE_NAME_SJ_CHAPTER_Q					"Sequencer State Commands"
#define RTP_MIDI_TREE_NAME_SJ_CHAPTER_F					"MIDI Time Code Tape Position"
#define RTP_MIDI_TREE_NAME_SJ_CHAPTER_F_COMPLETE			"Complete field"
#define RTP_MIDI_TREE_NAME_SJ_CHAPTER_F_PARTIAL				"Partial field"
#define RTP_MIDI_TREE_NAME_SJ_CHAPTER_X					"System Exclusive"
#define RTP_MIDI_TREE_NAME_SJ_CHAPTER_X_DATA				"Data field (sysex commands)"
#define RTP_MIDI_TREE_NAME_SJ_CHAPTER_X_INVALID_DATA			"Data field (invalid sysex commands)"
#define RTP_MIDI_TREE_NAME_CHANNEL_JOURNAL				"Channel-Journal"
#define RTP_MIDI_TREE_NAME_CHANNEL_CHAPTERS				"Channel-Chapters"
#define RTP_MIDI_TREE_NAME_CJ_CHAPTER_P					"Program Change"
#define RTP_MIDI_TREE_NAME_CJ_CHAPTER_C					"Control Change"
#define RTP_MIDI_TREE_NAME_CJ_CHAPTER_C_LOGLIST				"Log List"
#define RTP_MIDI_TREE_NAME_CJ_CHAPTER_C_LOGITEM				"Log Item"
#define RTP_MIDI_TREE_NAME_CJ_CHAPTER_M					"Parameter System"
#define RTP_MIDI_TREE_NAME_CJ_CHAPTER_M_LOGLIST				"Log List"
#define RTP_MIDI_TREE_NAME_CJ_CHAPTER_M_LOGITEM				"Log Item"
#define RTP_MIDI_TREE_NAME_CJ_CHAPTER_M_LOG_MSB				"Entry MSB"
#define RTP_MIDI_TREE_NAME_CJ_CHAPTER_M_LOG_LSB				"Entry LSB"
#define RTP_MIDI_TREE_NAME_CJ_CHAPTER_M_LOG_A_BUTTON			"A-Button"
#define RTP_MIDI_TREE_NAME_CJ_CHAPTER_M_LOG_C_BUTTON			"C-Button"
#define RTP_MIDI_TREE_NAME_CJ_CHAPTER_M_LOG_COUNT			"Count"
#define RTP_MIDI_TREE_NAME_CJ_CHAPTER_W					"Pitch Wheel"
#define RTP_MIDI_TREE_NAME_CJ_CHAPTER_N					"Note on/off"
#define RTP_MIDI_TREE_NAME_CJ_CHAPTER_N_LOGLIST				"Log List"
#define RTP_MIDI_TREE_NAME_CJ_CHAPTER_N_LOGITEM				"Note On"
#define RTP_MIDI_TREE_NAME_CJ_CHAPTER_N_OCTETS				"Offbit Octets"
#define RTP_MIDI_TREE_NAME_CJ_CHAPTER_E					"Note Command Extras"
#define RTP_MIDI_TREE_NAME_CJ_CHAPTER_E_LOGLIST				"Log List"
#define RTP_MIDI_TREE_NAME_CJ_CHAPTER_E_LOGITEM1			"Note Off"
#define RTP_MIDI_TREE_NAME_CJ_CHAPTER_E_LOGITEM2			"Note On/Off"
#define RTP_MIDI_TREE_NAME_CJ_CHAPTER_T					"Channel Aftertouch"
#define RTP_MIDI_TREE_NAME_CJ_CHAPTER_A					"Poly Aftertouch"
#define RTP_MIDI_TREE_NAME_CJ_CHAPTER_A_LOGLIST				"Log List"
#define RTP_MIDI_TREE_NAME_CJ_CHAPTER_A_LOGITEM				"Pressure"
#define RTP_MIDI_COMMAND_STATUS_FLAG					0x80
#define RTP_MIDI_DELTA_TIME_OCTET_MASK					0x7f
#define RTP_MIDI_DELTA_TIME_EXTENSION					0x80
#define RTP_MIDI_CS_FLAG_B						0x80
#define RTP_MIDI_CS_FLAG_J						0x40
#define RTP_MIDI_CS_FLAG_Z						0x20
#define RTP_MIDI_CS_FLAG_P						0x10
#define RTP_MIDI_CS_MASK_SHORTLEN					0x0f
#define RTP_MIDI_CS_MASK_LONGLEN					0x0fff
#define RTP_MIDI_CJ_CHAPTER_M_FLAG_J					0x80
#define RTP_MIDI_CJ_CHAPTER_M_FLAG_K					0x40
#define RTP_MIDI_CJ_CHAPTER_M_FLAG_L					0x20
#define RTP_MIDI_CJ_CHAPTER_M_FLAG_M					0x10
#define RTP_MIDI_CJ_CHAPTER_M_FLAG_N					0x08
#define RTP_MIDI_CJ_CHAPTER_M_FLAG_T					0x04
#define RTP_MIDI_CJ_CHAPTER_M_FLAG_V					0x02
#define RTP_MIDI_CJ_CHAPTER_M_FLAG_R					0x01
#define RTP_MIDI_JS_FLAG_S				0x80
#define RTP_MIDI_JS_FLAG_Y				0x40
#define RTP_MIDI_JS_FLAG_A				0x20
#define RTP_MIDI_JS_FLAG_H				0x10
#define RTP_MIDI_JS_MASK_TOTALCHANNELS			0x0f
#define RTP_MIDI_SJ_FLAG_S				0x8000
#define RTP_MIDI_SJ_FLAG_D				0x4000
#define RTP_MIDI_SJ_FLAG_V				0x2000
#define RTP_MIDI_SJ_FLAG_Q				0x1000
#define RTP_MIDI_SJ_FLAG_F				0x0800
#define RTP_MIDI_SJ_FLAG_X				0x0400
#define RTP_MIDI_SJ_MASK_LENGTH				0x03ff
#define RTP_MIDI_SJ_CHAPTER_D_FLAG_S			0x80
#define RTP_MIDI_SJ_CHAPTER_D_FLAG_B			0x40
#define RTP_MIDI_SJ_CHAPTER_D_FLAG_G			0x20
#define RTP_MIDI_SJ_CHAPTER_D_FLAG_H			0x10
#define RTP_MIDI_SJ_CHAPTER_D_FLAG_J			0x08
#define RTP_MIDI_SJ_CHAPTER_D_FLAG_K			0x04
#define RTP_MIDI_SJ_CHAPTER_D_FLAG_Y			0x02
#define RTP_MIDI_SJ_CHAPTER_D_FLAG_Z			0x01
#define RTP_MIDI_SJ_CHAPTER_D_RESET_FLAG_S		0x80
#define RTP_MIDI_SJ_CHAPTER_D_RESET_COUNT		0x7f
#define RTP_MIDI_SJ_CHAPTER_D_TUNE_FLAG_S		0x80
#define RTP_MIDI_SJ_CHAPTER_D_TUNE_COUNT		0x7f
#define RTP_MIDI_SJ_CHAPTER_D_SONG_SEL_FLAG_S		0x80
#define RTP_MIDI_SJ_CHAPTER_D_SONG_SEL_VALUE		0x7f
#define RTP_MIDI_SJ_CHAPTER_D_SYSCOM_FLAG_S		0x8000
#define RTP_MIDI_SJ_CHAPTER_D_SYSCOM_FLAG_C		0x4000
#define RTP_MIDI_SJ_CHAPTER_D_SYSCOM_FLAG_V		0x2000
#define RTP_MIDI_SJ_CHAPTER_D_SYSCOM_FLAG_L		0x1000
#define RTP_MIDI_SJ_CHAPTER_D_SYSCOM_MASK_DSZ		0x0c00
#define RTP_MIDI_SJ_CHAPTER_D_SYSCOM_MASK_LENGTH	0x03ff
#define RTP_MIDI_SJ_CHAPTER_D_SYSCOM_MASK_COUNT		0xff
#define RTP_MIDI_SJ_CHAPTER_D_SYSREAL_FLAG_S		0x80
#define RTP_MIDI_SJ_CHAPTER_D_SYSREAL_FLAG_C		0x40
#define RTP_MIDI_SJ_CHAPTER_D_SYSREAL_FLAG_L		0x20
#define RTP_MIDI_SJ_CHAPTER_D_SYSREAL_MASK_LENGTH	0x1f
#define RTP_MIDI_SJ_CHAPTER_D_SYSREAL_MASK_COUNT	0xff
#define RTP_MIDI_SJ_CHAPTER_Q_FLAG_S			0x80
#define RTP_MIDI_SJ_CHAPTER_Q_FLAG_N			0x40
#define RTP_MIDI_SJ_CHAPTER_Q_FLAG_D			0x20
#define RTP_MIDI_SJ_CHAPTER_Q_FLAG_C			0x10
#define RTP_MIDI_SJ_CHAPTER_Q_FLAG_T			0x80
#define RTP_MIDI_SJ_CHAPTER_Q_MASK_TOP			0x07
#define RTP_MIDI_SJ_CHAPTER_Q_MASK_CLOCK		0x07ffff
#define RTP_MIDI_SJ_CHAPTER_Q_MASK_TIMETOOLS		0xffffff
#define RTP_MIDI_SJ_CHAPTER_F_FLAG_S			0x80
#define RTP_MIDI_SJ_CHAPTER_F_FLAG_C			0x40
#define RTP_MIDI_SJ_CHAPTER_F_FLAG_P			0x20
#define RTP_MIDI_SJ_CHAPTER_F_FLAG_Q			0x10
#define RTP_MIDI_SJ_CHAPTER_F_FLAG_D			0x08
#define RTP_MIDI_SJ_CHAPTER_F_MASK_POINT		0x07
#define RTP_MIDI_SJ_CHAPTER_F_MASK_MT0			0xf0000000
#define RTP_MIDI_SJ_CHAPTER_F_MASK_MT1			0x0f000000
#define RTP_MIDI_SJ_CHAPTER_F_MASK_MT2			0x00f00000
#define RTP_MIDI_SJ_CHAPTER_F_MASK_MT3			0x000f0000
#define RTP_MIDI_SJ_CHAPTER_F_MASK_MT4			0x0000f000
#define RTP_MIDI_SJ_CHAPTER_F_MASK_MT5			0x00000f00
#define RTP_MIDI_SJ_CHAPTER_F_MASK_MT6			0x000000f0
#define RTP_MIDI_SJ_CHAPTER_F_MASK_MT7			0x0000000f
#define RTP_MIDI_SJ_CHAPTER_F_MASK_HR			0xff000000
#define RTP_MIDI_SJ_CHAPTER_F_MASK_MN			0x00ff0000
#define RTP_MIDI_SJ_CHAPTER_F_MASK_SC			0x0000ff00
#define RTP_MIDI_SJ_CHAPTER_F_MASK_FR			0x000000ff
#define RTP_MIDI_SJ_CHAPTER_X_FLAG_S			0x80
#define RTP_MIDI_SJ_CHAPTER_X_FLAG_T			0x40
#define RTP_MIDI_SJ_CHAPTER_X_FLAG_C			0x20
#define RTP_MIDI_SJ_CHAPTER_X_FLAG_F			0x10
#define RTP_MIDI_SJ_CHAPTER_X_FLAG_D			0x08
#define RTP_MIDI_SJ_CHAPTER_X_FLAG_L			0x04
#define RTP_MIDI_SJ_CHAPTER_X_MASK_STA			0x03
#define RTP_MIDI_SJ_CHAPTER_X_MASK_TCOUNT		0xff
#define RTP_MIDI_SJ_CHAPTER_X_MASK_COUNT		0xff
#define RTP_MIDI_CJ_FLAG_S				0x800000
#define RTP_MIDI_CJ_FLAG_H				0x040000
#define RTP_MIDI_CJ_FLAG_P				0x000080
#define RTP_MIDI_CJ_FLAG_C				0x000040
#define RTP_MIDI_CJ_FLAG_M				0x000020
#define RTP_MIDI_CJ_FLAG_W				0x000010
#define RTP_MIDI_CJ_FLAG_N				0x000008
#define RTP_MIDI_CJ_FLAG_E				0x000004
#define RTP_MIDI_CJ_FLAG_T				0x000002
#define RTP_MIDI_CJ_FLAG_A				0x000001
#define RTP_MIDI_CJ_MASK_LENGTH				0x03ff00
#define RTP_MIDI_CJ_MASK_CHANNEL			0x780000
#define RTP_MIDI_CJ_CHANNEL_SHIFT			19
#define RTP_MIDI_CJ_CHAPTER_M_MASK_LENGTH		0x3f
#define RTP_MIDI_CJ_CHAPTER_N_MASK_LENGTH		0x7f00
#define RTP_MIDI_CJ_CHAPTER_N_MASK_LOW			0x00f0
#define RTP_MIDI_CJ_CHAPTER_N_MASK_HIGH			0x000f
#define RTP_MIDI_CJ_CHAPTER_E_MASK_LENGTH		0x7f
#define RTP_MIDI_CJ_CHAPTER_A_MASK_LENGTH		0x7f
#define RTP_ED137_SIG    0x0067
#define RTP_ED137A_SIG   0x0167
#define RTP_ED137_feature_bss_type    0x1
#define RTP_ED137_feature_bss_len     11
#define RTP_RFC5215_ONE_BYTE_SIG        0xBEDE
#define RTP_RFC5215_TWO_BYTE_MASK       0xFFF0
#define RTP_RFC5215_TWO_BYTE_SIG        0x1000
#define RTP_PLAYER_DEFAULT_VISIBLE 4
#define RUA_PAYLOAD_PROTOCOL_ID                        19
#define S1AP_PAYLOAD_PROTOCOL_ID                       18
#define SABP_PAYLOAD_PROTOCOL_ID                       31
#define	SAP_MASK	0xFE
#define SAP_VERSION_MASK (0xC0)
#define SAP_VALUE_MASK (0x3f)
#define	SAP_NULL		0x00
#define	SAP_LLC_SLMGMT		0x02
#define	SAP_SNA_PATHCTRL	0x04
#define	SAP_IP			0x06
#define	SAP_SNA1		0x08
#define	SAP_SNA2		0x0C
#define	SAP_PROWAY_NM_INIT	0x0E
#define SAP_NETWARE1		0x10
#define SAP_OSINL1		0x14
#define	SAP_TI			0x18
#define SAP_OSINL2		0x20
#define SAP_OSINL3		0x34
#define	SAP_SNA3		0x40
#define	SAP_BPDU		0x42
#define	SAP_RS511		0x4E
#define SAP_OSINL4		0x54
#define	SAP_X25                 0x7E
#define	SAP_XNS			0x80
#define	SAP_BACNET		0x82
#define	SAP_NESTAR		0x86
#define	SAP_PROWAY_ASLM		0x8E
#define	SAP_ARP			0x98
#define	SAP_SNAP		0xAA
#define	SAP_HPJD		0xB4
#define	SAP_VINES1		0xBA
#define	SAP_VINES2		0xBC
#define	SAP_NETWARE2		0xE0
#define	SAP_NETBIOS		0xF0
#define	SAP_IBMNM		0xF4
#define	SAP_HPEXT		0xF8
#define	SAP_UB			0xFA
#define	SAP_RPL			0xFC
#define	SAP_OSINL5		0xFE
#define	SAP_GLOBAL		0xFF
#define SASP_GLOBAL_PORT		3860
#define SASP_MIN_PACKET_LEN		13
#define SASP_DEREG_REQ_REASON_LEARNED	0x01
#define SASP_DEREG_REQ_NOREASON_FLAG	0x00
#define SASP_HDR_TYPE			0x2010
#define SASP_WED_CONTACT_SUCCESS_FLAG	0x01
#define SASP_WED_QUIESCE_FLAG		0x02
#define SASP_WED_REG_FLAG		0x04
#define SASP_WED_CONF_FLAG		0x08
#define SASP_PUSH_FLAG			0x01
#define SASP_TRUST_FLAG			0x02
#define SASP_NOCHANGE_FLAG		0x04
#define SASP_QUIESCE_FLAG		0x01
#define SBC_AP_PORT 29168
#define SBC_AP_PAYLOAD_PROTOCOL_ID                     24
#define SCCPMG_SSN 1
#define SCCPMG_MESSAGE_TYPE_SSA 0x01
#define SCCPMG_MESSAGE_TYPE_SSP 0x02
#define SCCPMG_MESSAGE_TYPE_SST 0x03
#define SCCPMG_MESSAGE_TYPE_SOR 0x04
#define SCCPMG_MESSAGE_TYPE_SOG 0x05
#define SCCPMG_MESSAGE_TYPE_SSC 0x06
#define SCCPMG_MESSAGE_TYPE_SBR 0xfd
#define SCCPMG_MESSAGE_TYPE_SNR 0xfe
#define SCCPMG_MESSAGE_TYPE_SRT 0xff
#define SCCPMG_MESSAGE_TYPE_OFFSET 0
#define SCCPMG_MESSAGE_TYPE_LENGTH 1
#define SCCPMG_AFFECTED_SSN_OFFSET SCCPMG_MESSAGE_TYPE_LENGTH
#define SCCPMG_AFFECTED_SSN_LENGTH 1
#define SCCPMG_AFFECTED_PC_OFFSET (SCCPMG_AFFECTED_SSN_OFFSET + SCCPMG_AFFECTED_SSN_LENGTH)
#define SCCPMG_SMI_LENGTH 1
#define SCCPMG_SMI_MASK 0x3
#define SCCPMG_SSN_LENGTH    1
#define SCTP_PORT_LENGTH          2
#define SCTP_TRANSPORT_USE_LENGTH 2
#define SCTP_PORT_OFFSET          PARAMETER_VALUE_OFFSET
#define SCTP_TRANSPORT_USE_OFFSET (SCTP_PORT_OFFSET + SCTP_PORT_LENGTH)
#define SCTP_ADDRESS_OFFSET       (SCTP_TRANSPORT_USE_OFFSET + SCTP_TRANSPORT_USE_LENGTH)
#define SCTP_TRANSPORT_PARAMETER_TYPE               0x04
#define SCTP_PORT_DLM3          TCP_PORT_DLM3
#define SCTP_PORT_DNS             53
#define SCTP_PORT_HNBAP              29169
#define SCTP_PORT_IUA          9900
#define SCTP_PORT_LCSAP 9082
#define SCTP_PORT_M2PA              3565
#define SCTP_PORT_M2TP        9908  /* unassigned port number (not assigned by IANA) */
#define SCTP_PORT_M2UA                  2904
#define SCTP_PORT_M3UA         2905
#define SCTP_PORT_RUA              29169;
#define SCTP_PORT_S1AP	36412
#define SCTP_DATA_CHUNK_ID               0
#define SCTP_INIT_CHUNK_ID               1
#define SCTP_INIT_ACK_CHUNK_ID           2
#define SCTP_SACK_CHUNK_ID               3
#define SCTP_HEARTBEAT_CHUNK_ID          4
#define SCTP_HEARTBEAT_ACK_CHUNK_ID      5
#define SCTP_ABORT_CHUNK_ID              6
#define SCTP_SHUTDOWN_CHUNK_ID           7
#define SCTP_SHUTDOWN_ACK_CHUNK_ID       8
#define SCTP_ERROR_CHUNK_ID              9
#define SCTP_COOKIE_ECHO_CHUNK_ID       10
#define SCTP_COOKIE_ACK_CHUNK_ID        11
#define SCTP_ECNE_CHUNK_ID              12
#define SCTP_CWR_CHUNK_ID               13
#define SCTP_SHUTDOWN_COMPLETE_CHUNK_ID 14
#define SCTP_AUTH_CHUNK_ID              15
#define SCTP_NR_SACK_CHUNK_ID           16
#define SCTP_ASCONF_ACK_CHUNK_ID      0x80
#define SCTP_PKTDROP_CHUNK_ID         0x81
#define SCTP_RE_CONFIG_CHUNK_ID       0x82
#define SCTP_PAD_CHUNK_ID             0x84
#define SCTP_FORWARD_TSN_CHUNK_ID     0xC0
#define SCTP_ASCONF_CHUNK_ID          0xC1
#define SCTP_IETF_EXT                 0xFF
#define SCTP_CHECKSUM_NONE      0
#define SCTP_CHECKSUM_ADLER32   1
#define SCTP_CHECKSUM_CRC32C    2
#define SCTP_CHECKSUM_AUTOMATIC 3
#define SCTP_PARAMETER_BIT_1  0x8000
#define SCTP_PARAMETER_BIT_2 0x4000
#define SCTP_DATA_CHUNK_E_BIT 0x01
#define SCTP_DATA_CHUNK_B_BIT 0x02
#define SCTP_DATA_CHUNK_U_BIT 0x04
#define SCTP_DATA_CHUNK_I_BIT 0x08
#define SCTP_SACK_CHUNK_NS_BIT                  0x01
#define SCTP_NR_SACK_CHUNK_NS_BIT                  0x01
#define SCTP_ABORT_CHUNK_T_BIT               0x01
#define SCTP_SHUTDOWN_COMPLETE_CHUNK_T_BIT 0x01
#define SCTP_SEQUENCE_NUMBER_LENGTH    4
#define SCTP_PKTDROP_CHUNK_M_BIT 0x01
#define SCTP_PKTDROP_CHUNK_B_BIT 0x02
#define SCTP_PKTDROP_CHUNK_T_BIT 0x04
#define SCTP_CHUNK_BIT_1 0x80
#define SCTP_CHUNK_BIT_2 0x40
#define SCTP_PORT_SGSAP 29118
#define SCTP_PORT_SSH 22
#define SCTP_PORT_SUA          14001
#define SCTP_PORT_V5UA_RFC         5675
#define SCTP_PORT_V5UA_DRAFT      10001
#define SCTP_PORT_X2AP	36422
#define SPX_SYS_PACKET	0x80
#define SPX_SEND_ACK	0x40
#define SPX_ATTN	0x20
#define SPX_EOM		0x10
#define SPX_HEADER_LEN	12
#define SPX_SOCKET_PA                   0x90b2 /* NDPS Printer Agent */
#define SPX_SOCKET_BROKER               0x90b3 /* NDPS Broker */
#define SPX_SOCKET_SRS                  0x90b4 /* NDPS Service Registry Service */
#define SPX_SOCKET_ENS                  0x90b5 /* NDPS Event Notification Service */
#define SPX_SOCKET_RMS                  0x90b6 /* NDPS Remote Management Service */
#define SPX_SOCKET_NOTIFY_LISTENER      0x90b7 /* NDPS Notify Listener */
#define SSH_VERSION_UNKNOWN 	0
#define SSH_VERSION_1		1
#define SSH_VERSION_2		2
#define SSH_MSG_DISCONNECT			1
#define SSH_MSG_IGNORE				2
#define SSH_MSG_UNIMPLEMENTED		3
#define SSH_MSG_DEBUG				4
#define SSH_MSG_SERVICE_REQUEST		5
#define SSH_MSG_SERVICE_ACCEPT		6
#define SSH_MSG_KEXINIT				20
#define SSH_MSG_NEWKEYS				21
#define SSH_MSG_KEXDH_INIT			30
#define SSH_MSG_KEXDH_REPLY			31
#define SSH_MSG_KEX_DH_GEX_REQUEST_OLD	30
#define SSH_MSG_KEX_DH_GEX_GROUP		31
#define SSH_MSG_KEX_DH_GEX_INIT			32
#define SSH_MSG_KEX_DH_GEX_REPLY		33
#define SSH_MSG_KEX_DH_GEX_REQUEST		34
#define SSH_MSG_USERAUTH_REQUEST	50
#define SSH_MSG_USERAUTH_FAILURE	51
#define SSH_MSG_USERAUTH_SUCCESS	52
#define SSH_MSG_USERAUTH_BANNER		53
#define SSH_MSG_GLOBAL_REQUEST			80
#define SSH_MSG_REQUEST_SUCCESS			81
#define SSH_MSG_REQUEST_FAILURE			82
#define SSH_MSG_CHANNEL_OPEN				90
#define SSH_MSG_CHANNEL_OPEN_CONFIRMATION	91
#define SSH_MSG_CHANNEL_OPEN_FAILURE		92
#define SSH_MSG_CHANNEL_WINDOW_ADJUST		93
#define SSH_MSG_CHANNEL_DATA				94
#define SSH_MSG_CHANNEL_EXTENDED_DATA		95
#define SSH_MSG_CHANNEL_EOF					96
#define SSH_MSG_CHANNEL_CLOSE				97
#define SSH_MSG_CHANNEL_REQUEST				98
#define SSH_MSG_CHANNEL_SUCCESS				99
#define SSH_MSG_CHANNEL_FAILURE				100
#define SSH_PAYLOAD_PROTOCOL_ID                        45
#define SSPROTOCOL_PAYLOAD_PROTOCOL_ID_LEGACY 0x29097604
#define SSP_ENVIRONMENT_U_BIT 0x01
#define SSP_PAYLOAD_PROTOCOL_ID                        35
#define SUA_PAYLOAD_PROTOCOL_ID                         4
#define TCP_PORT_ACuap			674
#define TCP_PORT_AIM 5190
#define TCP_PORT_LENGTH          2
#define TCP_TRANSPORT_USE_LENGTH 2
#define TCP_PORT_OFFSET          PARAMETER_VALUE_OFFSET
#define TCP_TRANSPORT_USE_OFFSET (TCP_PORT_OFFSET + TCP_PORT_LENGTH)
#define TCP_ADDRESS_OFFSET       (TCP_TRANSPORT_USE_OFFSET + TCP_TRANSPORT_USE_LENGTH)
#define TCP_TRANSPORT_PARAMETER_TYPE                0x05
#define TCP_PORT_BEEP 10288
#define TCP_PORT_BZR   4155
#define TCP_PORT_CAST 4224
#define TCP_PORT_STUN   3478
#define TCP_PORT_CMP 829
#define TCP_PORT_COPS 3288
#define TCP_PORT_RDP 3389
#define TCP_PORT_PKTCABLE_COPS 2126
#define TCP_PORT_PKTCABLE_MM_COPS 3918
#define TCP_PORT_DAAP 3689
#define TCP_PORT_DHCPFO 519
#define TCP_PORT_DISTCC 3632
#define TCP_PORT_DLM3           21064
#define TCP_PORT_DLSW           2065
#define TCP_PORT_DNP    20000
#define TCP_PORT_MDNS           5353
#define TCP_PORT_DSI      548
#define TCP_CONV_MSG_TYPE_DATA          0x01
#define TCP_CONV_MSG_TYPE_ACK           0x02
#define TCP_CONV_MSG_TYPE_KEEP_ALIVE    0x03
#define TCP_CONV_MSG_TYPE_SHUTDOWN      0x04
#define TCP_CONVERGENCE_TYPE_MASK       0xf0
#define TCP_CONVERGENCE_DATA_SEGMENT    0x10
#define TCP_CONVERGENCE_ACK_SEGMENT     0x20
#define TCP_CONVERGENCE_REFUSE_BUNDLE   0x30
#define TCP_CONVERGENCE_KEEP_ALIVE      0x40
#define TCP_CONVERGENCE_SHUTDOWN        0x50
#define TCP_CONV_BUNDLE_ACK_FLAG        0x01
#define TCP_CONV_REACTIVE_FRAG_FLAG     0x02
#define TCP_CONV_CONNECTOR_RCVR_FLAG    0x04
#define TCP_CONVERGENCE_DATA_FLAGS      0x03
#define TCP_CONVERGENCE_DATA_END_FLAG   0x01
#define TCP_CONVERGENCE_DATA_START_FLAG 0x02
#define TCP_CONVERGENCE_SHUTDOWN_FLAGS  0x03
#define TCP_CONVERGENCE_SHUTDOWN_REASON 0x02
#define TCP_CONVERGENCE_SHUTDOWN_DELAY  0x01
#define TCP_CONV_MIN_DATA_BUFFER        9
#define TCP_CONV_HDR_DATA_FIXED_LENGTH  5
#define TCP_CONV_HDR_ACK_LENGTH         9
#define TCP_CONV_HDR_KEEP_ALIVE_LENGTH  1
#define TCP_CONV_HDR_SHUTDOWN_LENGTH    1
#define TCP_PORT_ELCOM        5997
#define TCP_PORT_ENTTEC 0x0D05
#define TCP_PORT_FF_ANNUNC  1089
#define TCP_PORT_FF_FMS     1090
#define TCP_PORT_FF_SM      1091
#define TCP_PORT_FF_LR_PORT 3622
#define TCP_PORT_FMTP       8500 */
#define TCP_UDP_TML_FOCES_MESSAGE_OFFSET_TCP    2
#define TCP_PORT_FTPDATA        20
#define TCP_PORT_FTP            21
#define TCP_PORT_GADU_GADU 8074	/* assigned by IANA */
#define TCP_PORT_GDSDB	3050
#define TCP_PORT_GIFT 1213
#define TCP_PORT_GIT			9418
#define TCP_DEFAULT_RANGE "70"
#define TCP_PORT_CS   1720
#define TCP_PORT_SSDP			1900
#define TCP_PORT_ICuap			1344
#define TCP_PORT_IMAP			143
#define TCP_PORT_SSL_IMAP		993
#define	TCP_PORT_IPDC	6668
#define TCP_CISCO_IPSEC 10000
#define TCP_ENCuap_P_ESP 1
#define TCP_ENCuap_P_UDP 2
#define TCP_PORT_IRC            6667
#define TCP_PORT_DIRCPROXY      57000
#define TCP_PORT_ISAKMP 500
#define TCP_PORT_ISCSI_RANGE    "3260"
#define TCP_MAX_SEQ ((guint32) 0xffffffff)
#define TCP_PORT_KERBEROS               88
#define TCP_PORT_KINGFISHER         4058
#define TCP_PORT_KINGFISHER_OLD     473
#define TCP_PORT_KISMET	2501
#define TCP_PORT_KPASSWD        464
#define TCP_PORT_LAPLINK 1547
#define TCP_PORT_LDAP			389
#define TCP_PORT_LDAPS			636
#define TCP_PORT_GLOBALCAT_LDAP         3268 /* Windows 2000 Global Catalog */
#define TCP_PORT_LDP 646
#define TCP_CM_CONN  0x43
#define TCP_CM_UDATA 0x55
#define TCP_CM_PING  0x50
#define TCP_CM_PONG  0x51
#define TCP_PORT_LINX 19790
#define TCP_PORT_PRINTER		515
#define TCP_PORT_MGCP_GATEWAY 2427
#define TCP_PORT_MGCP_CALLAGENT 2727
#define TCP_PORT_MONGO 27017
#define TCP_PORT_MSNMS			1863
#define TCP_PORT_MSRP 0
#define TCP_PORT_MySQL   3306
#define TCP_PORT_NBSS	139
#define TCP_PORT_CIFS	445
#define TCP_PORT_NCP            524
#define TCP_PORT_NDMP 10000
#define TCP_PORT_PA                     0x0d44 /* NDPS Printer Agent */
#define TCP_PORT_BROKER                 0x0bc6 /* NDPS Broker */
#define TCP_PORT_SRS                    0x0bca /* NDPS Service Registry Service */
#define TCP_PORT_ENS                    0x0bc8 /* NDPS Event Notification Service */
#define TCP_PORT_RMS                    0x0bcb /* NDPS Remote Management Service */
#define TCP_PORT_NOTIFY_LISTENER        0x0bc9 /* NDPS Notify Listener */
#define TCP_PORT_NETSYNC 5253
#define TCP_PORT_RLOGIN	513
#define TCP_PORT_TELNET	23
#define TCP_PORT_NNTP			119
#define TCP_PORT_NTP	123
#define TCP_PORT_OPSI		4002
#define TCP_PORT_PCEP			4189
#define TCP_PORT_POP            110
#define TCP_PORT_SSL_POP        995
#define TCP_PORT_PPTP		1723
#define TCP_PORT_PVFS2 3334
#define TCP_MODE_IMMED 1
#define TCP_MODE_UNEXP 2
#define TCP_MODE_EAGER 4
#define TCP_MODE_REND 8
#define TCP_PORT_RELOAD                 6084
#define TCP_PORT_RMI	1099
#define TCP_PORT_RSIP	4555
#define TCP_PORT_RSYNC	873
#define TCP_PORT_RTSP           554
#define TCP_ALTERNATE_PORT_RTSP     8554
#define TCP_PORT_SIP 5060
#define TCP_PORT_SKINNY 2000
#define TCP_PORT_SLSK_1       2234
#define TCP_PORT_SLSK_2       5534
#define TCP_PORT_SLSK_3       2240
#define TCP_PORT_SML		0
#define TCP_PORT_SMRSE 4321
#define TCP_PORT_SMTP      25
#define TCP_PORT_SSL_SMTP 465
#define TCP_PORT_SUBMISSION 587
#define TCP_PORT_SNMP		161
#define TCP_PORT_SNMP_TRAP	162
#define TCP_PORT_SMUX		199
#define TCP_PORT_SOCKS 1080
#define TCP_PORT_SRVLOC	427
#define TCP_PORT_SSH  22
#define TCP_PORT_TACACS	49
#define TCP_A_RETRANSMISSION          0x0001
#define TCP_A_LOST_PACKET             0x0002
#define TCP_A_ACK_LOST_PACKET         0x0004
#define TCP_A_KEEP_ALIVE              0x0008
#define TCP_A_DUPLICATE_ACK           0x0010
#define TCP_A_ZERO_WINDOW             0x0020
#define TCP_A_ZERO_WINDOW_PROBE       0x0040
#define TCP_A_ZERO_WINDOW_PROBE_ACK   0x0080
#define TCP_A_KEEP_ALIVE_ACK          0x0100
#define TCP_A_OUT_OF_ORDER            0x0200
#define TCP_A_FAST_RETRANSMISSION     0x0400
#define TCP_A_WINDOW_UPDATE           0x0800
#define TCP_A_WINDOW_FULL             0x1000
#define TCP_A_REUSED_PORTS            0x2000
#define TCP_A_SPURIOUS_RETRANSMISSION 0x4000
#define TCP_FLOW_REASSEMBLE_UNTIL_FIN	0x0001
#define TCP_PORT_TNS			1521
#define TCP_PORT_TPKT		102
#define TCP_PORT_TPNCP_TRUNKPACK BASE_TPNCP_PORT
#define TCP_PORT_TPNCP_HOST BASE_TPNCP_PORT
#define TCP_PORT_X11                    6000
#define TCP_PORT_X11_2                  6001
#define TCP_PORT_X11_3                  6002
#define TCP_PORT_XMCP 4788
#define TCP_PORT_XOT 1998
#define TCP_PORT_YHOO	5050
#define TCP_PORT_YMSG	  23	/* XXX - this is Telnet! */
#define TCP_PORT_YMSG_2	  25	/* And this is SMTP! */
#define TCP_PORT_YMSG_3	5050	/* This, however, is regular Yahoo Messenger */
#define TCP_PORT_ZEBRA			2600
#define TIME_DATA_SET_TIME_STAMP                0x1
#define TIME_DATA_SET_TIME_OFFSET               0x2
#define TIME_DATA_SET_UPDATE_DIAGNOSTICS        0x4
#define TIME_DATA_SET_TIME_DIAGNOSTICS          0x8
#define TIME_TO_LIVE_TLV_TYPE			0x03	/* Mandatory */
#define TIME_T_MIN ((time_t) ((time_t)0 < (time_t) -1 ? (time_t) 0 \
#define TIME_T_MAX ((time_t) (~ (time_t) 0 - TIME_T_MIN))
#define TIME_PORT 37
#define TIME_FIXUP_CONSTANT G_GINT64_CONSTANT(11644473600U)
#define TIME_SECS_LEN	(10+1+4+2+2+5+2+2+7+2+2+7+4)
#define TS2_STATUS_CHANNELCOMMANDER  1
#define TS2_STATUS_BLOCKWHISPERS     4
#define TS2_STATUS_AWAY              8
#define TS2_STATUS_MUTEMICROPHONE   16
#define TS2_STATUS_MUTE             32
#define TS2_PORT 8767
#define UAUDP_CONNECT           0
#define UAUDP_CONNECT_ACK       1
#define UAUDP_RELEASE           2
#define UAUDP_RELEASE_ACK       3
#define UAUDP_KEEPALIVE         4
#define UAUDP_KEEPALIVE_ACK     5
#define UAUDP_NACK              6
#define UAUDP_DATA              7
#define UAUDP_CONNECT_VERSION           0x00
#define UAUDP_CONNECT_WINDOW_SIZE       0x01
#define UAUDP_CONNECT_MTU               0x02
#define UAUDP_CONNECT_UDP_LOST          0x03
#define UAUDP_CONNECT_UDP_LOST_REINIT   0x04
#define UAUDP_CONNECT_KEEPALIVE         0x05
#define UAUDP_CONNECT_QOS_IP_TOS        0x06
#define UAUDP_CONNECT_QOS_8021_VLID     0x07
#define UAUDP_CONNECT_QOS_8021_PRI      0x08
#define UDP_PORT_3GA11    699
#define UDP_PORT_ACTRACE 2428
#define UDP_PORT_AODV	654
#define UDP_PORT_ARMAGETRONAD 4534
#define UDP_PORT_MASTER 4533
#define UDP_PORT_ARTNET 0x1936
#define UDP_PORT_ADP 8200
#define UDP_PORT_PAPI 8211
#define UDP_PORT_LENGTH     2
#define UDP_RESERVED_LENGTH 2
#define UDP_PORT_OFFSET     PARAMETER_VALUE_OFFSET
#define UDP_RESERVED_OFFSET (UDP_PORT_OFFSET + UDP_PORT_LENGTH)
#define UDP_ADDRESS_OFFSET  (UDP_RESERVED_OFFSET + UDP_RESERVED_LENGTH)
#define UDP_LITE_PORT_LENGTH     2
#define UDP_LITE_RESERVED_LENGTH 2
#define UDP_LITE_PORT_OFFSET     PARAMETER_VALUE_OFFSET
#define UDP_LITE_RESERVED_OFFSET (UDP_LITE_PORT_OFFSET + UDP_LITE_PORT_LENGTH)
#define UDP_LITE_ADDRESS_OFFSET  (UDP_LITE_RESERVED_OFFSET + UDP_LITE_RESERVED_LENGTH)
#define UDP_TRANSPORT_PARAMETER_TYPE                0x06
#define UDP_LITE_TRANSPORT_PARAMETER_TYPE           0x07
#define UDP_PORT_PIM_RP_DISC 496
#define UDP_PORT_AYIYA			5072
#define UDP_PORT_BABEL 6696
#define UDP_PORT_BABEL_OLD 6697
#define UDP_PORT_BFD_1HOP_CONTROL 3784 /* draft-katz-ward-bfd-v4v6-1hop-00.txt */
#define UDP_PORT_BFD_MULTIHOP_CONTROL 4784 /* draft-ietf-bfd-multihop-05.txt */
#define UDP_PORT_BOOTPS	 67
#define UDP_PORT_BOOTPC	 68
#define UDP_PORT_CuapWAP_CONTROL 5246
#define UDP_PORT_CuapWAP_DATA 5247
#define UDP_PORT_STUN   3478
#define UDP_PORT_COLLECTD 25826
#define UDP_PORT_CPHA        8116
#define UDP_PORT_CUPS  631
#define UDP_PORT_DDTP	1052
#define UDP_PORT_DHCPV6_DOWNSTREAM      546
#define UDP_PORT_DHCPV6_UPSTREAM        547
#define UDP_PORT_DLSW           2067
#define UDP_PORT_DNP    20000
#define UDP_PORT_MDNS           5353
#define UDP_PORT_LLMNR          5355
#define UDP_PORT_IPDC_ESG_BOOTSTRAP 9214
#define UDP_PORT_ENTTEC 0x0D05
#define UDP_PORT_EPL            3819
#define UDP_PORT_FF_ANNUNC  1089
#define UDP_PORT_FF_FMS     1090
#define UDP_PORT_FF_SM      1091
#define UDP_PORT_FF_LR_PORT 3622
#define UDP_PORT_RAS1 1718
#define UDP_PORT_RAS2 1719
#define UDP_PORT_HSRP   1985
#define UDP_PORT_HSRP2_V6   2029
#define UDP_PORT_SSDP			1900
#define UDP_PORT_IAPP     2313
#define UDP_PORT_ICP    3130
#define UDP_PORT_ICQ	4000
#define UDP_PORT_IPX    213		/* RFC 1234 */
#define UDP_PORT_ISAKMP 500
#define UDP_PORT_KERBEROS               88
#define UDP_PORT_KRB4    750
#define UDP_PORT_KINGFISHER         4058
#define UDP_PORT_KINGFISHER_OLD     473
#define UDP_DATAGRAM_RELIABLE_FLAG    0x40
#define UDP_MSG_BLOCK_RELIABLE_FLAG   0x10
#define UDP_PORT_KPASSWD        464
#define UDP_PORT_L2TP   1701
#define UDP_PORT_LAPLINK 1547
#define UDP_PORT_CLDAP			389
#define UDP_PORT_LDP 646
#define UDP_PORT_LDSS 6087
#define UDP_PORT_LLC1   12000
#define UDP_PORT_LLC2   12001
#define UDP_PORT_LLC3   12002
#define UDP_PORT_LLC4   12003
#define UDP_PORT_LLC5   12004
#define UDP_PORT_LMP_DEFAULT 701
#define UDP_PORT_MGCP_GATEWAY 2427
#define UDP_PORT_MGCP_CALLAGENT 2727
#define UDP_PORT_MIP    434
#define UDP_PORT_PMIP6_CNTL 5436
#define UDP_PORT_MPLS_ECHO 3503
#define UDP_PORT_MSPROXY 1745
#define UDP_PORT_NBNS	137
#define UDP_PORT_NBDGM	138
#define UDP_PORT_NCP            524
#define UDP_PORT_TPCP   3121
#define UDP_PORT_NTP	123
#define UDP_PORT_OICQ	8000
#define UDP_PORT_OLSR   698
#define UDP_PORT_OPENSAFETY   9877
#define UDP_PORT_SIII         8755
#define UDP_PORT_PCLI 9000
#define UDP_PORT_RADIUS		1645
#define UDP_PORT_RADIUS_NEW	1812
#define UDP_PORT_RADACCT	1646
#define UDP_PORT_RADACCT_NEW	1813
#define UDP_PORT_DAE_OLD	1700 /* DAE: pre RFC */
#define UDP_PORT_DAE		3799 /* DAE: rfc3576 */
#define UDP_PORT_RELOAD                 6084
#define UDP_PORT_RIP    520
#define UDP_PORT_RIPNG  521
#define UDP_PORT_RMCP		623
#define UDP_PORT_RMCP_SECURE	664
#define UDP_PORT_RSIP	4555
#define UDP_PORT_PRSVP 3455
#define UDP_PORT_RX_LOW		7000
#define UDP_PORT_RX_HIGH	7009
#define UDP_PORT_RX_AFS_BACKUPS	7021
#define UDP_PORT_SAP	9875
#define UDP_TUNNELING_PORT 9899
#define UDP_PORT_SEBEK	1101
#define UDP_PORT_SIP 5060
#define UDP_PORT_SLIMP3_V1    1069
#define UDP_PORT_SLIMP3_V2    3483
#define UDP_PORT_SML		0
#define UDP_PORT_SNMP		161
#define UDP_PORT_SNMP_TRAP	162
#define UDP_PORT_SNMP_PATROL 8161
#define UDP_ASSOCIATE_COMMAND   3
#define UDP_PORT_SRVLOC	427
#define UDP_PORT_SYSLOG 514
#define UDP_PORT_TACACS	49
#define UDP_PORT_TEREDO 3544
#define UDP_PORT_TFTP_RANGE    "69"
#define UDP_PORT_TPNCP_TRUNKPACK BASE_TPNCP_PORT
#define UDP_PORT_TPNCP_HOST BASE_TPNCP_PORT
#define UDP_PORT_TIMED	525
#define UDP_PORT_TZSP   0x9090
#define UDP_HFI_INIT HFI_INIT(proto_udp)
#define UDP_PORT_VINES	573
#define UDP_PORT_WSP			9200		/* wap-wsp			*/
#define UDP_PORT_WTP_WSP		9201		/* wap-wsp-wtp		*/
#define UDP_PORT_WTLS_WSP		9202		/* wap-wsp-s		*/
#define UDP_PORT_WTLS_WTP_WSP		9203		/* wap-wsp-wtp-s	*/
#define UDP_PORT_WSP_PUSH		2948		/* wap-wsp		*/
#define UDP_PORT_WTLS_WSP_PUSH		2949		/* wap-wsp-s		*/
#define UDP_PORT_WCCP           2048
#define UDP_PORT_WHO    513
#define UDP_PORT_VNC 5900
#define UDP_PORT_XDMCP 177
#define UDP_PORT_XYPLEX    173
#define V5UA_PROTOCOL_VERSION_RELEASE_1     1
#define V5UA_PAYLOAD_PROTOCOL_ID                        6
#define VENDOR_ATTRS 21
#define VENDOR_INFO_OFFSET		236
#define VENDOR_1_START 0x09         /* Start of first Vendor Specific Range */
#define VENDOR_1_END 0x0F           /* End of first Vendor Specific Range */
#define VENDOR_2_START 0x30         /* Start of second Vendor Specific Range */
#define VENDOR_2_END 0x4F           /* End of the second Vendor Specific Range */
#define  VENDOR_NAME                   8
#define VENDOR_W_NAME 3
#define VENDOR_W_ID 16
#define VENDOR_W_FORMAT 17
#define VENDOR_W_TYPE_OCTETS 18
#define VENDOR_W_LENGTH_OCTETS 19
#define VENDOR_W_CONTINUATION 20
#define VENDOR_IETF                     0 /* reserved - used by the IETF in L2TP? */
#define VENDOR_ACC                      5
#define VENDOR_CISCO                    9
#define VENDOR_HEWLETT_PACKARD         11
#define VENDOR_SUN_MICROSYSTEMS        42
#define VENDOR_MERIT                   61
#define VENDOR_AT_AND_T                74
#define VENDOR_MOTOROLA               161
#define VENDOR_SHIVA                  166
#define VENDOR_ERICSSON               193
#define VENDOR_CISCO_VPN5000          255
#define VENDOR_LIVINGSTON             307
#define VENDOR_MICROSOFT              311
#define VENDOR_3COM                   429
#define VENDOR_ASCEND                 529
#define VENDOR_BAY                   1584
#define VENDOR_FOUNDRY               1991
#define VENDOR_VERSANET              2180
#define VENDOR_REDBACK               2352
#define VENDOR_JUNIPER               2636
#define VENDOR_APTIS                 2637
#define VENDOR_DT_AG                 2937
#define VENDOR_CISCO_VPN3000         3076
#define VENDOR_COSINE                3085
#define VENDOR_SHASTA                3199
#define VENDOR_NETSCREEN             3224
#define VENDOR_NOMADIX               3309
#define VENDOR_T_MOBILE              3414 /* Former VoiceStream Wireless, Inc. */
#define VENDOR_ZTE                   3902
#define VENDOR_SIEMENS               4329
#define VENDOR_CABLELABS             4491
#define VENDOR_UNISPHERE             4874
#define VENDOR_CISCO_BBSM            5263
#define VENDOR_THE3GPP2              5535
#define VENDOR_IP_UNPLUGGED          5925
#define VENDOR_ISSANNI               5948
#define VENDOR_DE_TE_MOBIL           6490
#define VENDOR_QUINTUM               6618
#define VENDOR_INTERLINK             6728
#define VENDOR_CNCTC                 7951
#define VENDOR_COLUBRIS              8744
#define VENDOR_ERICSSON_PKT_CORE    10923
#define VENDOR_COLUMBIA_UNIVERSITY  11862
#define VENDOR_THE3GPP              10415
#define VENDOR_GEMTEK_SYSTEMS       10529
#define VENDOR_VERIZON              12951
#define VENDOR_PLIXER               13745
#define VENDOR_WIFI_ALLIANCE        14122
#define VENDOR_T_SYSTEMS_NOVA       16787
#define VENDOR_CHINATELECOM_GUANZHOU 20942
#define VENDOR_CACE                 32622
#define VENDOR_NTOP                 35632u
#define VICP_PORT 1861
#define VICP_EXCEPTION_NOTIFICATION	0x0000
#define VICP_METRIC_NOTIFICATION	0x0001
#define VINES_FRP_FIRST_FRAGMENT	0x01
#define VINES_FRP_LAST_FRAGMENT		0x02
#define VINES_LLC_IP	0xba
#define VINES_LLC_ECHO	0xbb
#define VINES_VERS_PRE_5_5	0x00
#define VINES_VERS_5_5		0x01
#define VINES_ADDR_LEN	6
#define WHOIS_PORT      43  /* This is the registered IANA port (nicname) */
#define WLCCP_UDP_PORT 2887
#define WLCCP_SAP_CCM (0x00)
#define WLCCP_SAP_SEC (0x01)
#define WLCCP_SAP_RRM (0x02)
#define WLCCP_SAP_QOS (0x03)
#define WLCCP_SAP_NM  (0x04)
#define WLCCP_SAP_MIP (0x05)
#define WLCCP_TLV_GROUP_WLCCP (0x00)
#define WLCCP_TLV_GROUP_SEC (0x01)
#define WLCCP_TLV_GROUP_RRM (0x02)
#define WLCCP_TLV_GROUP_QOS (0x03)
#define WLCCP_TLV_GROUP_NM  (0x04)
#define WLCCP_TLV_GROUP_MIP (0x05)
#define WOW_PORT 3724
#define WOW_CLIENT_TO_SERVER pinfo->destport == WOW_PORT
#define WOW_SERVER_TO_CLIENT pinfo->srcport  == WOW_PORT
#define WRETH_PORT 0xAAAA
#define WRETH_IDENT          1
#define WRETH_CONNECT        2
#define WRETH_ACK            3
#define WRETH_NACK           4
#define WRETH_DISCONNECT     5
#define WRETH_MAIL           6
#define WRETH_BLINKY         7
#define WRETH_GET_VALUE      8
#define WRETH_SET_VALUE      9
#define WRETH_BOOST         10
#define WRETH_BAD_FUNCTION_CODE         1
#define WRETH_ALREADY_CONNECTED         2
#define WRETH_INVALID_PROTOCOL_VERSION  3
#define WRETH_NOT_CONNECTED             4
#define WRETH_INVALID_MAC_ADDRESS       5
#define WRETH_INVALID_FRAME_SIZE        6
#define WRETH_NO_MEMORY_AVAILABLE       7
#define WRETH_BAD_PARAMETER             8
#define WRETH_TASK_REGISTERED           9
#define X2AP_PAYLOAD_PROTOCOL_ID                       27
#define XMPP_PORT 5222
#define ZBEE_APS_FCF_FRAME_TYPE     0x03
#define ZBEE_APS_FCF_DELIVERY_MODE  0x0c
#define ZBEE_APS_FCF_INDIRECT_MODE  0x10    /* ZigBee 2004 and earlier.  */
#define ZBEE_APS_FCF_ACK_FORMAT     0x10    /* ZigBee 2007 and later.    */
#define ZBEE_APS_FCF_SECURITY       0x20
#define ZBEE_APS_FCF_ACK_REQ        0x40
#define ZBEE_APS_FCF_EXT_HEADER     0x80
#define ZBEE_APS_FCF_DATA           0x00
#define ZBEE_APS_FCF_CMD            0x01
#define ZBEE_APS_FCF_ACK            0x02
#define ZBEE_APS_FCF_UNICAST        0x00
#define ZBEE_APS_FCF_INDIRECT       0x01
#define ZBEE_APS_FCF_BCAST          0x02
#define ZBEE_APS_FCF_GROUP          0x03    /* ZigBee 2006 and later.    */
#define ZBEE_APS_EXT_FCF_FRAGMENT           0x03
#define ZBEE_APS_EXT_FCF_FRAGMENT_NONE      0x00
#define ZBEE_APS_EXT_FCF_FRAGMENT_FIRST     0x01
#define ZBEE_APS_EXT_FCF_FRAGMENT_MIDDLE    0x02
#define ZBEE_APS_CMD_SKKE1                  0x01
#define ZBEE_APS_CMD_SKKE2                  0x02
#define ZBEE_APS_CMD_SKKE3                  0x03
#define ZBEE_APS_CMD_SKKE4                  0x04
#define ZBEE_APS_CMD_TRANSPORT_KEY          0x05
#define ZBEE_APS_CMD_UPDATE_DEVICE          0x06
#define ZBEE_APS_CMD_REMOVE_DEVICE          0x07
#define ZBEE_APS_CMD_REQUEST_KEY            0x08
#define ZBEE_APS_CMD_SWITCH_KEY             0x09
#define ZBEE_APS_CMD_EA_INIT_CHLNG          0x0a
#define ZBEE_APS_CMD_EA_RESP_CHLNG          0x0b
#define ZBEE_APS_CMD_EA_INIT_MAC_DATA       0x0c
#define ZBEE_APS_CMD_EA_RESP_MAC_DATA       0x0d
#define ZBEE_APS_CMD_TUNNEL                 0x0e
#define ZBEE_APS_CMD_KEY_TC_MASTER          0x00
#define ZBEE_APS_CMD_KEY_STANDARD_NWK       0x01
#define ZBEE_APS_CMD_KEY_APP_MASTER         0x02
#define ZBEE_APS_CMD_KEY_APP_LINK           0x03
#define ZBEE_APS_CMD_KEY_TC_LINK            0x04
#define ZBEE_APS_CMD_KEY_HIGH_SEC_NWK       0x05
#define ZBEE_APS_CMD_SKKE_DATA_LENGTH       16
#define ZBEE_APS_CMD_KEY_LENGTH             16
#define ZBEE_APS_CMD_REQ_NWK_KEY            0x01
#define ZBEE_APS_CMD_REQ_APP_KEY            0x02
#define ZBEE_APS_CMD_UPDATE_STANDARD_SEC_REJOIN     0x00
#define ZBEE_APS_CMD_UPDATE_STANDARD_UNSEC_JOIN     0x01
#define ZBEE_APS_CMD_UPDATE_LEAVE                   0x02
#define ZBEE_APS_CMD_UPDATE_STANDARD_UNSEC_REJOIN   0x03
#define ZBEE_APS_CMD_UPDATE_HIGH_SEC_REJOIN         0x04
#define ZBEE_APS_CMD_UPDATE_HIGH_UNSEC_JOIN         0x05
#define ZBEE_APS_CMD_UPDATE_HIGH_UNSEC_REJOIN       0x07
#define ZBEE_APS_CMD_EA_KEY_NWK             0x00
#define ZBEE_APS_CMD_EA_KEY_LINK            0x01
#define ZBEE_APS_CMD_EA_CHALLENGE_LENGTH    16
#define ZBEE_APS_CMD_EA_MAC_LENGTH          16
#define ZBEE_APS_CMD_EA_DATA_LENGTH         4
#define ZBEE_APP_TYPE                       0xF0
#define ZBEE_APP_COUNT                      0x0F
#define ZBEE_APP_TYPE_KVP                   0x01
#define ZBEE_APP_TYPE_MSG                   0x02
#define ZBEE_APP_KVP_CMD                    0x0F
#define ZBEE_APP_KVP_TYPE                   0xF0
#define ZBEE_APP_KVP_SET                    0x01
#define ZBEE_APP_KVP_EVENT                  0x02
#define ZBEE_APP_KVP_GET_ACK                0x04
#define ZBEE_APP_KVP_SET_ACK                0x05
#define ZBEE_APP_KVP_EVENT_ACK              0x06
#define ZBEE_APP_KVP_GET_RESP               0x08
#define ZBEE_APP_KVP_SET_RESP               0x09
#define ZBEE_APP_KVP_EVENT_RESP             0x0A
#define ZBEE_APP_KVP_NO_DATA                0x00
#define ZBEE_APP_KVP_UINT8                  0x01
#define ZBEE_APP_KVP_INT8                   0x02
#define ZBEE_APP_KVP_UINT16                 0x03
#define ZBEE_APP_KVP_INT16                  0x04
#define ZBEE_APP_KVP_FLOAT16                0x0B
#define ZBEE_APP_KVP_ABS_TIME               0x0C
#define ZBEE_APP_KVP_REL_TIME               0x0D
#define ZBEE_APP_KVP_CHAR_STRING            0x0E
#define ZBEE_APP_KVP_OCT_STRING             0x0F
#define ZBEE_APP_KVP_OVERHEAD               4
#define ZBEE_ZCL_CID_BASIC                          0x0000
#define ZBEE_ZCL_CID_POWER_CONFIG                   0x0001
#define ZBEE_ZCL_CID_DEVICE_TEMP_CONFIG             0x0002
#define ZBEE_ZCL_CID_IDENTIFY                       0x0003
#define ZBEE_ZCL_CID_GROUPS                         0x0004
#define ZBEE_ZCL_CID_SCENES                         0x0005
#define ZBEE_ZCL_CID_ON_OFF                         0x0006
#define ZBEE_ZCL_CID_ON_OFF_SWITCH_CONFIG           0x0007
#define ZBEE_ZCL_CID_LEVEL_CONTROL                  0x0008
#define ZBEE_ZCL_CID_ALARMS                         0x0009
#define ZBEE_ZCL_CID_TIME                           0x000a
#define ZBEE_ZCL_CID_RSSI_LOCATION                  0x000b
#define ZBEE_ZCL_CID_ANALOG_INPUT_BASIC             0x000c
#define ZBEE_ZCL_CID_ANALOG_OUTPUT_BASIC            0x000d
#define ZBEE_ZCL_CID_ANALOG_VALUE_BASIC             0x000e
#define ZBEE_ZCL_CID_BINARY_INPUT_BASIC             0x000f
#define ZBEE_ZCL_CID_BINARY_OUTPUT_BASIC            0x0010
#define ZBEE_ZCL_CID_BINARY_VALUE_BASIC             0x0011
#define ZBEE_ZCL_CID_MULTISTATE_INPUT_BASIC         0x0012
#define ZBEE_ZCL_CID_MULTISTATE_OUTPUT_BASIC        0x0013
#define ZBEE_ZCL_CID_MULTISTATE_VALUE_BASIC         0x0014
#define ZBEE_ZCL_CID_COMMISSIONING                  0x0015
#define ZBEE_ZCL_CID_PARTITION                      0x0016
#define ZBEE_ZCL_CID_POWER_PROFILE                  0x001a
#define ZBEE_ZCL_CID_APPLIANCE_CONTROL              0x001b
#define ZBEE_ZCL_CID_SHADE_CONFIG                   0x0100
#define ZBEE_ZCL_CID_DOOR_LOCK                      0X0101
#define ZBEE_ZCL_CID_PUMP_CONFIG_CONTROL            0x0200
#define ZBEE_ZCL_CID_THERMOSTAT                     0x0201
#define ZBEE_ZCL_CID_FAN_CONTROL                    0x0202
#define ZBEE_ZCL_CID_DEHUMIDIFICATION_CONTROL       0x0203
#define ZBEE_ZCL_CID_THERMOSTAT_UI_CONFIG           0x0204
#define ZBEE_ZCL_CID_COLOR_CONTROL                  0x0300
#define ZBEE_ZCL_CID_BALLAST_CONFIG                 0x0301
#define ZBEE_ZCL_CID_ILLUMINANCE_MEASUREMENT        0x0400
#define ZBEE_ZCL_CID_ILLUMINANCE_LEVEL_SENSING      0x0401
#define ZBEE_ZCL_CID_TEMPERATURE_MEASUREMENT        0x0402
#define ZBEE_ZCL_CID_PRESSURE_MEASUREMENT           0x0403
#define ZBEE_ZCL_CID_FLOW_MEASUREMENT               0x0404
#define ZBEE_ZCL_CID_REL_HUMIDITY_MEASUREMENT       0x0405
#define ZBEE_ZCL_CID_OCCUPANCY_SENSING              0x0406
#define ZBEE_ZCL_CID_IAS_ZONE                       0x0500
#define ZBEE_ZCL_CID_IAS_ACE                        0x0501
#define ZBEE_ZCL_CID_IAS_WD                         0x0502
#define ZBEE_ZCL_CID_GENERIC_TUNNEL                 0x0600
#define ZBEE_ZCL_CID_BACNET_PROTOCOL_TUNNEL         0x0601
#define ZBEE_ZCL_CID_BACNET_ANALOG_INPUT_REG        0x0602
#define ZBEE_ZCL_CID_BACNET_ANALOG_INPUT_EXT        0x0603
#define ZBEE_ZCL_CID_BACNET_ANALOG_OUTPUT_REG       0x0604
#define ZBEE_ZCL_CID_BACNET_ANALOG_OUTPUT_EXT       0x0605
#define ZBEE_ZCL_CID_BACNET_ANALOG_VALUE_REG        0x0606
#define ZBEE_ZCL_CID_BACNET_ANALOG_VALUE_EXT        0x0607
#define ZBEE_ZCL_CID_BACNET_BINARY_INPUT_REG        0x0608
#define ZBEE_ZCL_CID_BACNET_BINARY_INPUT_EXT        0x0609
#define ZBEE_ZCL_CID_BACNET_BINARY_OUTPUT_REG       0x060a
#define ZBEE_ZCL_CID_BACNET_BINARY_OUTPUT_EXT       0x060b
#define ZBEE_ZCL_CID_BACNET_BINARY_VALUE_REG        0x060c
#define ZBEE_ZCL_CID_BACNET_BINARY_VALUE_EXT        0x060d
#define ZBEE_ZCL_CID_BACNET_MULTISTATE_INPUT_REG    0x060e
#define ZBEE_ZCL_CID_BACNET_MULTISTATE_INPUT_EXT    0x060f
#define ZBEE_ZCL_CID_BACNET_MULTISTATE_OUTPUT_REG   0x0610
#define ZBEE_ZCL_CID_BACNET_MULTISTATE_OUTPUT_EXT   0x0611
#define ZBEE_ZCL_CID_BACNET_MULTISTATE_VALUE_REG    0x0612
#define ZBEE_ZCL_CID_BACNET_MULTISTATE_VALUE_EXT    0x0613
#define ZBEE_ZCL_CID_PRICE                          0x0700
#define ZBEE_ZCL_CID_DEMAND_RESPONSE_LOAD_CONTROL   0x0701
#define ZBEE_ZCL_CID_SIMPLE_METERING                0x0702
#define ZBEE_ZCL_CID_MESSAGE                        0x0703
#define ZBEE_ZCL_CID_SMART_ENERGY_TUNNELING         0x0704
#define ZBEE_ZCL_CID_PRE_PAYMENT                    0x0705
#define ZBEE_ZCL_CID_APPLIANCE_IDENTIFICATION       0x0b00
#define ZBEE_ZCL_CID_METER_IDENTIFICATION           0x0b01
#define ZBEE_ZCL_CID_APPLIANCE_EVENTS_AND_ALERT     0x0b02
#define ZBEE_ZCL_CID_APPLIANCE_STATISTICS           0x0b03
#define ZBEE_NWK_FCF_FRAME_TYPE             0x0003
#define ZBEE_NWK_FCF_VERSION                0x003C
#define ZBEE_NWK_FCF_DISCOVER_ROUTE         0x00C0
#define ZBEE_NWK_FCF_MULTICAST              0x0100  /* ZigBee 2006 and Later */
#define ZBEE_NWK_FCF_SECURITY               0x0200
#define ZBEE_NWK_FCF_SOURCE_ROUTE           0x0400  /* ZigBee 2006 and Later */
#define ZBEE_NWK_FCF_EXT_DEST               0x0800  /* ZigBee 2006 and Later */
#define ZBEE_NWK_FCF_EXT_SOURCE             0x1000  /* ZigBee 2006 and Later */
#define ZBEE_NWK_FCF_DATA                   0x0000
#define ZBEE_NWK_FCF_CMD                    0x0001
#define ZBEE_NWK_FCF_DISCOVERY_SUPPRESS     0x0000
#define ZBEE_NWK_FCF_DISCOVERY_ENABLE       0x0001
#define ZBEE_NWK_FCF_DISCOVERY_FORCE        0x0003
#define ZBEE_NWK_MCAST_MODE                 0x03    /* ZigBee 2006 and later */
#define ZBEE_NWK_MCAST_RADIUS               0x1c    /* ZigBee 2006 and later */
#define ZBEE_NWK_MCAST_MAX_RADIUS           0xe0    /* ZigBee 2006 and later */
#define ZBEE_NWK_MCAST_MODE_NONMEMBER       0x00    /* ZigBee 2006 and later */
#define ZBEE_NWK_MCAST_MODE_MEMBER          0x01    /* ZigBee 2006 and later */
#define ZBEE_NWK_CMD_ROUTE_REQ                  0x01
#define ZBEE_NWK_CMD_ROUTE_REPLY                0x02
#define ZBEE_NWK_CMD_NWK_STATUS                 0x03
#define ZBEE_NWK_CMD_LEAVE                      0x04    /* ZigBee 2006 and Later */
#define ZBEE_NWK_CMD_ROUTE_RECORD               0x05    /* ZigBee 2006 and later */
#define ZBEE_NWK_CMD_REJOIN_REQ                 0x06    /* ZigBee 2006 and later */
#define ZBEE_NWK_CMD_REJOIN_RESP                0x07    /* ZigBee 2006 and later */
#define ZBEE_NWK_CMD_LINK_STATUS                0x08    /* ZigBee 2007 and later */
#define ZBEE_NWK_CMD_NWK_REPORT                 0x09    /* ZigBee 2007 and later */
#define ZBEE_NWK_CMD_NWK_UPDATE                 0x0a    /* ZigBee 2007 and later */
#define ZBEE_NWK_CMD_ROUTE_OPTION_REPAIR        0x80    /* ZigBee 2004 only. */
#define ZBEE_NWK_CMD_ROUTE_OPTION_MCAST         0x40    /* ZigBee 2006 and later */
#define ZBEE_NWK_CMD_ROUTE_OPTION_DEST_EXT      0x20    /* ZigBee 2007 and later (route request only). */
#define ZBEE_NWK_CMD_ROUTE_OPTION_MANY_MASK     0x18    /* ZigBee 2007 and later (route request only). */
#define ZBEE_NWK_CMD_ROUTE_OPTION_RESP_EXT      0x20    /* ZigBee 2007 and layer (route reply only). */
#define ZBEE_NWK_CMD_ROUTE_OPTION_ORIG_EXT      0x10    /* ZigBee 2007 and later (route reply only). */
#define ZBEE_NWK_CMD_ROUTE_OPTION_MANY_NONE     0x00
#define ZBEE_NWK_CMD_ROUTE_OPTION_MANY_REC      0x01
#define ZBEE_NWK_CMD_ROUTE_OPTION_MANY_NOREC    0x02
#define ZBEE_NWK_CMD_LEAVE_OPTION_CHILDREN      0x80
#define ZBEE_NWK_CMD_LEAVE_OPTION_REQUEST       0x40
#define ZBEE_NWK_CMD_LEAVE_OPTION_REJOIN        0x20
#define ZBEE_NWK_CMD_LINK_OPTION_LAST_FRAME     0x40
#define ZBEE_NWK_CMD_LINK_OPTION_FIRST_FRAME    0x20
#define ZBEE_NWK_CMD_LINK_OPTION_COUNT_MASK     0x1f
#define ZBEE_NWK_CMD_LINK_INCOMMING_COST_MASK   0x07
#define ZBEE_NWK_CMD_LINK_OUTGOING_COST_MASK    0x70
#define ZBEE_NWK_CMD_NWK_REPORT_COUNT_MASK      0x1f
#define ZBEE_NWK_CMD_NWK_REPORT_ID_MASK         0xe0
#define ZBEE_NWK_CMD_NWK_REPORT_ID_PAN_CONFLICT 0x00
#define ZBEE_NWK_CMD_NWK_UPDATE_COUNT_MASK      0x1f
#define ZBEE_NWK_CMD_NWK_UPDATE_ID_MASK         0xe0
#define ZBEE_NWK_CMD_NWK_UPDATE_ID_PAN_UPDATE   0x00
#define ZBEE_NWK_STATUS_NO_ROUTE_AVAIL      0x00
#define ZBEE_NWK_STATUS_TREE_LINK_FAIL      0x01
#define ZBEE_NWK_STATUS_NON_TREE_LINK_FAIL  0x02
#define ZBEE_NWK_STATUS_LOW_BATTERY         0x03
#define ZBEE_NWK_STATUS_NO_ROUTING          0x04
#define ZBEE_NWK_STATUS_NO_INDIRECT         0x05
#define ZBEE_NWK_STATUS_INDIRECT_EXPIRE     0x06
#define ZBEE_NWK_STATUS_DEVICE_UNAVAIL      0x07
#define ZBEE_NWK_STATUS_ADDR_UNAVAIL        0x08
#define ZBEE_NWK_STATUS_PARENT_LINK_FAIL    0x09
#define ZBEE_NWK_STATUS_VALIDATE_ROUTE      0x0a
#define ZBEE_NWK_STATUS_SOURCE_ROUTE_FAIL   0x0b
#define ZBEE_NWK_STATUS_MANY_TO_ONE_FAIL    0x0c
#define ZBEE_NWK_STATUS_ADDRESS_CONFLICT    0x0d
#define ZBEE_NWK_STATUS_VERIFY_ADDRESS      0x0e
#define ZBEE_NWK_STATUS_PANID_UPDATE        0x0f
#define ZBEE_NWK_STATUS_ADDRESS_UPDATE      0x10
#define ZBEE_NWK_STATUS_BAD_FRAME_COUNTER   0x11
#define ZBEE_NWK_STATUS_BAD_KEY_SEQNO       0x12
#define ZBEE_SEC_CONST_KEYSIZE              16
#define ZBEE_USER_KEY 0x01
#define ZBEE_NWK_BEACON_PROCOL_ID              0x00
#define ZBEE_NWK_BEACON_STACK_PROFILE          0x0f
#define ZBEE_NWK_BEACON_PROTOCOL_VERSION       0xf0
#define ZBEE_NWK_BEACON_ROUTER_CuapACITY        0x04
#define ZBEE_NWK_BEACON_NETWORK_DEPTH          0x78
#define ZBEE_NWK_BEACON_END_DEVICE_CuapACITY    0x80
#define ZBEE_SEC_CONTROL_LEVEL  0x07
#define ZBEE_SEC_CONTROL_KEY    0x18
#define ZBEE_SEC_CONTROL_NONCE  0x20
#define ZBEE_SEC_NONE           0x00
#define ZBEE_SEC_MIC32          0x01
#define ZBEE_SEC_MIC64          0x02
#define ZBEE_SEC_MIC128         0x03
#define ZBEE_SEC_ENC            0x04
#define ZBEE_SEC_ENC_MIC32      0x05
#define ZBEE_SEC_ENC_MIC64      0x06
#define ZBEE_SEC_ENC_MIC128     0x07
#define ZBEE_SEC_KEY_LINK       0x00
#define ZBEE_SEC_KEY_NWK        0x01
#define ZBEE_SEC_KEY_TRANSPORT  0x02
#define ZBEE_SEC_KEY_LOAD       0x03
#define ZBEE_SEC_CONST_L            2
#define ZBEE_SEC_CONST_NONCE_LEN    (ZBEE_SEC_CONST_BLOCKSIZE-ZBEE_SEC_CONST_L-1)
#define ZBEE_SEC_CONST_BLOCKSIZE    16
#define ZBEE_SEC_CCM_FLAG_L             0x01    /* 3-bit encoding of (L-1). */
#define ZBEE_SEC_PC_KEY             0
#define ZBEE_ZCL_BASIC_NUM_GENERIC_ETT                  3
#define ZBEE_ZCL_BASIC_NUM_ETT                          ZBEE_ZCL_BASIC_NUM_GENERIC_ETT
#define ZBEE_ZCL_ATTR_ID_BASIC_ZCL_VERSION              0x0000  /* ZCL Version */
#define ZBEE_ZCL_ATTR_ID_BASIC_APPL_VERSION             0x0001  /* Application Version */
#define ZBEE_ZCL_ATTR_ID_BASIC_STACK_VERSION            0x0002  /* Stack Version */
#define ZBEE_ZCL_ATTR_ID_BASIC_HW_VERSION               0x0003  /* HW Version */
#define ZBEE_ZCL_ATTR_ID_BASIC_MANUFACTURER_NAME        0x0004  /* Manufacturer Name */
#define ZBEE_ZCL_ATTR_ID_BASIC_MODEL_ID                 0x0005  /* Model Identifier */
#define ZBEE_ZCL_ATTR_ID_BASIC_DATE_CODE                0x0006  /* Date Code */
#define ZBEE_ZCL_ATTR_ID_BASIC_POWER_SOURCE             0x0007  /* Power Source */
#define ZBEE_ZCL_ATTR_ID_BASIC_LOCATION_DESCR           0x0010  /* Location Description */
#define ZBEE_ZCL_ATTR_ID_BASIC_PHY_ENVIRONMENT          0x0011  /* Physical Environment */
#define ZBEE_ZCL_ATTR_ID_BASIC_DEVICE_ENABLED           0x0012  /* Device Enabled */
#define ZBEE_ZCL_ATTR_ID_BASIC_ALARM_MASK               0x0013  /* Alarm Mask */
#define ZBEE_ZCL_ATTR_ID_BASIC_DISABLE_LOCAL_CFG        0x0014  /* Disable Local Config */
#define ZBEE_ZCL_CMD_ID_BASIC_RESET_FACTORY_DEFAULTS    0x00  /* Reset to Factory Defaults */
#define ZBEE_ZCL_BASIC_PWR_SRC_UNKNOWN                  0x00    /* Unknown */
#define ZBEE_ZCL_BASIC_PWR_SRC_MAINS_1PH                0x01    /* Mains (single phase) */
#define ZBEE_ZCL_BASIC_PWR_SRC_MAINS_3PH                0x02    /* Mains (3 phase) */
#define ZBEE_ZCL_BASIC_PWR_SRC_BATTERY                  0x03    /* Battery */
#define ZBEE_ZCL_BASIC_PWR_SRC_DC_SRC                   0x04    /* DC source */
#define ZBEE_ZCL_BASIC_PWR_SRC_EMERGENCY_1              0x05    /* Emergency mains constantly powered */
#define ZBEE_ZCL_BASIC_PWR_SRC_EMERGENCY_2              0x06    /* Emergency mains and tranfer switch */
#define ZBEE_ZCL_BASIC_DISABLED                         0x00    /* Disabled */
#define ZBEE_ZCL_BASIC_ENABLED                          0x01    /* Enabled */
#define ZBEE_ZCL_BASIC_ALARM_GEN_HW_FAULT               0x01    /* General hardware fault */
#define ZBEE_ZCL_BASIC_ALARM_GEN_SW_FAULT               0x02    /* General software fault */
#define ZBEE_ZCL_BASIC_ALARM_RESERVED                   0xfc    /* Reserved */
#define ZBEE_ZCL_BASIC_DIS_LOC_CFG_RESET                0x01    /* Reset (to factory defaults) */
#define ZBEE_ZCL_BASIC_DIS_LOC_CFG_DEV_CFG              0x02    /* Device configuration */
#define ZBEE_ZCL_BASIC_DIS_LOC_CFG_RESERVED             0xfc    /* Reserved */
#define ZBEE_ZCL_IDENTIFY_NUM_GENERIC_ETT               1
#define ZBEE_ZCL_IDENTIFY_NUM_ETT                       ZBEE_ZCL_IDENTIFY_NUM_GENERIC_ETT
#define ZBEE_ZCL_ATTR_ID_IDENTIFY_IDENTIFY_TIME         0x0000  /* Identify Time */
#define ZBEE_ZCL_CMD_ID_IDENTIFY_IDENTITY               0x00  /* Identify */
#define ZBEE_ZCL_CMD_ID_IDENTIFY_IDENTITY_QUERY         0x01  /* Identify Query */
#define ZBEE_ZCL_CMD_ID_IDENTIFY_IDENTITY_QUERY_RSP     0x00  /* Identify Query Response */
#define ZBEE_ZCL_ON_OFF_ATTR_ID_ONOFF     0x0000
#define ZBEE_ZCL_ON_OFF_CMD_OFF           0x00  /* Off */
#define ZBEE_ZCL_ON_OFF_CMD_ON            0x01  /* On */
#define ZBEE_ZCL_ON_OFF_CMD_TOGGLE        0x02  /* Toggle */
#define ZBEE_ZCL_PART_NUM_GENERIC_ETT                   3
#define ZBEE_ZCL_PART_NUM_NACK_ID_ETT                   16
#define ZBEE_ZCL_PART_NUM_ATTRS_ID_ETT                  16
#define ZBEE_ZCL_PART_NUM_ETT                           (ZBEE_ZCL_PART_NUM_GENERIC_ETT + \
#define ZBEE_ZCL_PART_OPT_1_BLOCK                       0x01
#define ZBEE_ZCL_PART_OPT_INDIC_LEN                     0x02
#define ZBEE_ZCL_PART_OPT_RESERVED                      0xc0
#define ZBEE_ZCL_PART_ACK_OPT_NACK_LEN                  0x01
#define ZBEE_ZCL_PART_ACK_OPT_RESERVED                  0xFE
#define ZBEE_ZCL_ATTR_ID_PART_MAX_IN_TRANSF_SIZE        0x0000  /* Maximum Incoming Transfer Size */
#define ZBEE_ZCL_ATTR_ID_PART_MAX_OUT_TRANSF_SIZE       0x0001  /* Maximum Outgoing Transfer Size */
#define ZBEE_ZCL_ATTR_ID_PART_PARTIONED_FRAME_SIZE      0x0002  /* Partioned Frame Size */
#define ZBEE_ZCL_ATTR_ID_PART_LARGE_FRAME_SIZE          0x0003  /* Large Frame Size */
#define ZBEE_ZCL_ATTR_ID_PART_ACK_FRAME_NUM             0x0004  /* Number of Ack Frame*/
#define ZBEE_ZCL_ATTR_ID_PART_NACK_TIMEOUT              0x0005  /* Nack Timeout */
#define ZBEE_ZCL_ATTR_ID_PART_INTERFRAME_DELEAY         0x0006  /* Interframe Delay */
#define ZBEE_ZCL_ATTR_ID_PART_SEND_RETRIES_NUM          0x0007  /* Number of Send Retries */
#define ZBEE_ZCL_ATTR_ID_PART_SENDER_TIMEOUT            0x0008  /* Sender Timeout */
#define ZBEE_ZCL_ATTR_ID_PART_RECEIVER_TIMEOUT          0x0009  /* Receiver Timeout */
#define ZBEE_ZCL_CMD_ID_PART_TRANSF_PART_FRAME          0x00  /* Transfer Partitioned Frame */
#define ZBEE_ZCL_CMD_ID_PART_RD_HANDSHAKE_PARAM         0x01  /* Read Handshake Param */
#define ZBEE_ZCL_CMD_ID_PART_WR_HANDSHAKE_PARAM         0x02  /* Write Handshake Param */
#define ZBEE_ZCL_CMD_ID_PART_MULTI_ACK                  0x00  /* Multiple Ack */
#define ZBEE_ZCL_CMD_ID_PART_RD_HANDSHAKE_PARAM_RSP     0x01  /* Read Handshake Param Response */
#define ZBEE_ZCL_PWR_PROF_NUM_GENERIC_ETT         4
#define ZBEE_ZCL_PWR_PROF_NUM_PWR_PROF_ETT        5
#define ZBEE_ZCL_PWR_PROF_NUM_EN_PHS_ETT          16
#define ZBEE_ZCL_PWR_PROF_NUM_ETT                 (ZBEE_ZCL_PWR_PROF_NUM_GENERIC_ETT +  \
#define ZBEE_ZCL_ATTR_ID_PWR_PROF_TOT_PROF_NUM              0x0000  /* Total Profile Number */
#define ZBEE_ZCL_ATTR_ID_PWR_PROF_MULTIPLE_SCHED            0x0001  /* Multiple Schedule */
#define ZBEE_ZCL_ATTR_ID_PWR_PROF_ENERGY_FORMAT             0x0002  /* Energy Formatting */
#define ZBEE_ZCL_ATTR_ID_PWR_PROF_ENERGY_REMOTE             0x0003  /* Energy Remote */
#define ZBEE_ZCL_ATTR_ID_PWR_PROF_SCHED_MODE                0x0004  /* Schedule Mode */
#define ZBEE_ZCL_CMD_ID_PWR_PROF_PWR_PROF_REQ                           0x00  /* Power Profile Request */
#define ZBEE_ZCL_CMD_ID_PWR_PROF_PWR_PROF_STATE_REQ                     0x01  /* Power Profile State Request */
#define ZBEE_ZCL_CMD_ID_PWR_PROF_GET_PWR_PROF_PRICE_RSP                 0x02  /* Get Power Profile Price Response */
#define ZBEE_ZCL_CMD_ID_PWR_PROF_GET_OVERALL_SCHED_PRICE_RSP            0x03  /* Get Overall Schedule Price Response */
#define ZBEE_ZCL_CMD_ID_PWR_PROF_ENERGY_PHASES_SCHED_NOTIF              0x04  /* Energy Phases Schedule Notification */
#define ZBEE_ZCL_CMD_ID_PWR_PROF_ENERGY_PHASES_SCHED_RSP                0x05  /* Energy Phases Schedule Response */
#define ZBEE_ZCL_CMD_ID_PWR_PROF_PWR_PROF_SCHED_CONSTRS_REQ             0x06  /* Power Profile Schedule Constraints Request */
#define ZBEE_ZCL_CMD_ID_PWR_PROF_ENERGY_PHASES_SCHED_STATE_REQ          0x07  /* Energy Phases Schedule State Request */
#define ZBEE_ZCL_CMD_ID_PWR_PROF_GET_PWR_PROF_PRICE_EXT_RSP             0x08  /* Get Power Profile Price Extended Response */
#define ZBEE_ZCL_CMD_ID_PWR_PROF_PWR_PROF_NOTIF                         0x00  /* Power Profile Notification */
#define ZBEE_ZCL_CMD_ID_PWR_PROF_PWR_PROF_RSP                           0x01  /* Power Profile Response */
#define ZBEE_ZCL_CMD_ID_PWR_PROF_PWR_PROF_STATE_RSP                     0x02  /* Power Profile State Response */
#define ZBEE_ZCL_CMD_ID_PWR_PROF_GET_PWR_PROF_PRICE                     0x03  /* Get Power Profile Price */
#define ZBEE_ZCL_CMD_ID_PWR_PROF_PWR_PROF_STATE_NOTIF                   0x04  /* Power Profile State Notification */
#define ZBEE_ZCL_CMD_ID_PWR_PROF_GET_OVERALL_SCHED_PRICE                0x05  /* Get Overall Schedule Price */
#define ZBEE_ZCL_CMD_ID_PWR_PROF_ENERGY_PHASES_SCHED_REQ                0x06  /* Energy Phases Schedule Request */
#define ZBEE_ZCL_CMD_ID_PWR_PROF_ENERGY_PHASES_SCHED_STATE_RSP          0x07  /* Energy Phases Schedule State Response */
#define ZBEE_ZCL_CMD_ID_PWR_PROF_ENERGY_PHASES_SCHED_STATE_NOITIF       0x08  /* Energy Phases Schedule State Notification */
#define ZBEE_ZCL_CMD_ID_PWR_PROF_PWR_PROF_SCHED_CONSTRS_NOTIF           0x09  /* Power Profile Schedule Constraints Notification */
#define ZBEE_ZCL_CMD_ID_PWR_PROF_PWR_PROF_SCHED_CONSTRS_RSP             0x0A  /* Power Profile Schedule Constraints Response */
#define ZBEE_ZCL_CMD_ID_PWR_PROF_GET_PWR_PROF_PRICE_EXT                 0x0B  /* Get Power Profile Price Extended */
#define ZBEE_ZCL_PWR_PROF_STATE_ID_PWR_PROF_IDLE                        0x00  /* Power Profile Idle */
#define ZBEE_ZCL_PWR_PROF_STATE_ID_PWR_PROF_PROGRAMMED                  0x01  /* Power Profile Programmed */
#define ZBEE_ZCL_PWR_PROF_STATE_ID_EN_PH_RUNNING                        0x03  /* Energy Phase Running */
#define ZBEE_ZCL_PWR_PROF_STATE_ID_EN_PH_PAUSE                          0x04  /* Energy Phase Pause */
#define ZBEE_ZCL_PWR_PROF_STATE_ID_EN_PH_WAITING_TO_START               0x05  /* Energy Phase Waiting to Start */
#define ZBEE_ZCL_PWR_PROF_STATE_ID_EN_PH_WAITING_PAUSED                 0x06  /* Energy Phase Waiting Pause */
#define ZBEE_ZCL_PWR_PROF_STATE_ID_PWR_PROF_ENDED                       0x07  /* Power Profile Ended */
#define ZBEE_ZCL_OPT_PWRPROF_NUM_R_DIGIT                                0x07  /* bits 0..2 */
#define ZBEE_ZCL_OPT_PWRPROF_NUM_L_DIGIT                                0x78  /* bits 3..6 */
#define ZBEE_ZCL_OPT_PWRPROF_NO_LEADING_ZERO                            0x80  /* bit     7 */
#define ZBEE_ZCL_OPT_PWRPROF_SCHED_CHEAPEST                             0x01  /* bit     0 */
#define ZBEE_ZCL_OPT_PWRPROF_SCHED_GREENEST                             0x02  /* bit     1 */
#define ZBEE_ZCL_OPT_PWRPROF_SCHED_RESERVED                             0xfc  /* bits 2..7 */
#define ZBEE_ZCL_OPT_PWRPROF_STIME_PRESENT                              0x01  /* bit     0 */
#define ZBEE_ZCL_OPT_PWRPROF_RESERVED                                   0xfe  /* bits 1..7 */
#define ZBEE_ZCL_APPL_CTRL_NUM_GENERIC_ETT                      3
#define ZBEE_ZCL_APPL_CTRL_NUM_FUNC_ETT                         32
#define ZBEE_ZCL_APPL_CTRL_NUM_ETT                              (ZBEE_ZCL_APPL_CTRL_NUM_GENERIC_ETT + \
#define ZBEE_ZCL_ATTR_ID_APPL_CTRL_START_TIME                   0x0000  /* Start Time */
#define ZBEE_ZCL_ATTR_ID_APPL_CTRL_FINISH_TIME                  0x0001  /* Finish Time */
#define ZBEE_ZCL_ATTR_ID_APPL_CTRL_REMAINING_TIME               0x0002  /* Remaining Time */
#define ZBEE_ZCL_CMD_ID_APPL_CTRL_EXECUTION_CMD                 0x00  /* Execution of a Command */
#define ZBEE_ZCL_CMD_ID_APPL_CTRL_SIGNAL_STATE                  0x01  /* Signal State */
#define ZBEE_ZCL_CMD_ID_APPL_CTRL_WRITE_FUNCS                   0x02  /* Write Functions */
#define ZBEE_ZCL_CMD_ID_APPL_CTRL_OVERLOAD_PAUSE_RESUME         0x03  /* Overload Pause Resume */
#define ZBEE_ZCL_CMD_ID_APPL_CTRL_OVERLOAD_PAUSE                0x04  /* Overload Pause */
#define ZBEE_ZCL_CMD_ID_APPL_CTRL_OVERLOAD_WARNING              0x05  /* Overload Warning */
#define ZBEE_ZCL_CMD_ID_APPL_CTRL_SIGNAL_STATE_RSP              0x00  /* Signal State Response */
#define ZBEE_ZCL_CMD_ID_APPL_CTRL_SIGNAL_STATE_NOTIF            0x01  /* Signal State Notification */
#define ZBEE_ZCL_APPL_CTRL_EXEC_CMD_ID_RESERVED                 0x00  /* Reserved */
#define ZBEE_ZCL_APPL_CTRL_EXEC_CMD_ID_START                    0x01  /* Start appliance cycle */
#define ZBEE_ZCL_APPL_CTRL_EXEC_CMD_ID_STOP                     0x02  /* Stop appliance cycle */
#define ZBEE_ZCL_APPL_CTRL_EXEC_CMD_ID_PAUSE                    0x03  /* Pause appliance cycle */
#define ZBEE_ZCL_APPL_CTRL_EXEC_CMD_ID_START_SUPERFREEZING      0x04  /* Start superfreezing cycle */
#define ZBEE_ZCL_APPL_CTRL_EXEC_CMD_ID_STOP_SUPERFREEZING       0x05  /* Stop superfreezing cycle */
#define ZBEE_ZCL_APPL_CTRL_EXEC_CMD_ID_START_SUPERCOOLING       0x06  /* Start supercooling cycle */
#define ZBEE_ZCL_APPL_CTRL_EXEC_CMD_ID_STOP_SUPERCOOLING        0x07  /* Stop supercooling cycle */
#define ZBEE_ZCL_APPL_CTRL_EXEC_CMD_ID_DISABLE_GAS              0x08  /* Disable gas */
#define ZBEE_ZCL_APPL_CTRL_EXEC_CMD_ID_ENABLE_GAS               0x09  /* Enable gas */
#define ZBEE_ZCL_APPL_CTRL_TIME_MM                              0x003f  /* Minutes */
#define ZBEE_ZCL_APPL_CTRL_TIME_ENCOD_TYPE                      0x00c0  /* Encoding Type */
#define ZBEE_ZCL_APPL_CTRL_TIME_HH                              0xff00  /* Hours */
#define ZBEE_ZCL_APPL_CTRL_TIME_ENCOD_REL                       0x00
#define ZBEE_ZCL_APPL_CTRL_TIME_ENCOD_ABS                       0x01
#define ZBEE_ZCL_APPL_CTRL_ID_OVRL_WARN_1                       0x00
#define ZBEE_ZCL_APPL_CTRL_ID_OVRL_WARN_2                       0x01
#define ZBEE_ZCL_APPL_CTRL_ID_OVRL_WARN_3                       0x02
#define ZBEE_ZCL_APPL_CTRL_ID_OVRL_WARN_4                       0x03
#define ZBEE_ZCL_APPL_CTRL_ID_OVRL_WARN_5                       0x04
#define ZBEE_ZCL_APPL_CTRL_ID_STATUS_RESERVED                   0x00  /* Reserved */
#define ZBEE_ZCL_APPL_CTRL_ID_STATUS_OFF                        0x01  /* Appliance in off state */
#define ZBEE_ZCL_APPL_CTRL_ID_STATUS_STANDBY                    0x02  /* Appliance in stand-by */
#define ZBEE_ZCL_APPL_CTRL_ID_STATUS_PRG                        0x03  /* Appliance already programmed */
#define ZBEE_ZCL_APPL_CTRL_ID_STATUS_PRG_WAITING_TO_START       0x04  /* Appliance already programmed and ready to start */
#define ZBEE_ZCL_APPL_CTRL_ID_STATUS_RUNNING                    0x05  /* Appliance is running */
#define ZBEE_ZCL_APPL_CTRL_ID_STATUS_PAUSE                      0x06  /* Appliance is in pause */
#define ZBEE_ZCL_APPL_CTRL_ID_STATUS_END_PRG                    0x07  /* Appliance end programmed tasks */
#define ZBEE_ZCL_APPL_CTRL_ID_STATUS_FAILURE                    0x08  /* Appliance is in a failure state */
#define ZBEE_ZCL_APPL_CTRL_ID_STATUS_PRG_INTERRUPTED            0x09  /* The appliance programmed tasks have been interrupted */
#define ZBEE_ZCL_APPL_CTRL_ID_STATUS_IDLE                       0x1a  /* Appliance in idle state */
#define ZBEE_ZCL_APPL_CTRL_ID_STATUS_RINSE_HOLD                 0x1b  /* Appliance rinse hold */
#define ZBEE_ZCL_APPL_CTRL_ID_STATUS_SERVICE                    0x1c  /* Appliance in service state */
#define ZBEE_ZCL_APPL_CTRL_ID_STATUS_SUPERFREEZING              0x1d  /* Appliance in superfreezing state */
#define ZBEE_ZCL_APPL_CTRL_ID_STATUS_SUPERCOOLING               0x1e  /* Appliance in supercooling state */
#define ZBEE_ZCL_APPL_CTRL_ID_STATUS_SUPERHEATING               0x1f  /* Appliance in superheating state */
#define ZBEE_ZCL_APPL_CTRL_REM_EN_FLAGS_FLAGS                   0x0f
#define ZBEE_ZCL_APPL_CTRL_REM_EN_FLAGS_STATUS2                 0xf0
#define ZBEE_ZCL_APPL_CTRL_REM_EN_FLAGS_DIS                     0x00  /* Disabled */
#define ZBEE_ZCL_APPL_CTRL_REM_EN_FLAGS_EN_REM_EN_CTRL          0x01  /* Enable Remote and Energy Control */
#define ZBEE_ZCL_APPL_CTRL_REM_EN_FLAGS_TEMP_LOCK_DIS           0x07  /* Temporarily locked/disabled */
#define ZBEE_ZCL_APPL_CTRL_REM_EN_FLAGS_EN_REM_CTRL             0x0f  /* Enable Remote Control */
#define ZBEE_ZCL_APPL_CTRL_STATUS2_PROPRIETARY_0                0x00  /* Proprietary */
#define ZBEE_ZCL_APPL_CTRL_STATUS2_PROPRIETARY_1                0x01  /* Proprietary */
#define ZBEE_ZCL_APPL_CTRL_STATUS2_IRIS_SYMPTOM_CODE            0x02  /* Iris symptom code */
#define ZBEE_ZCL_APPL_IDT_NUM_GENERIC_ETT               2
#define ZBEE_ZCL_APPL_IDT_NUM_ETT                       ZBEE_ZCL_APPL_IDT_NUM_GENERIC_ETT
#define ZBEE_ZCL_ATTR_ID_APPL_IDT_BASIC_IDENT           0x0000  /* Basic Identification */
#define ZBEE_ZCL_ATTR_ID_APPL_IDT_COMPANY_NAME          0x0010  /* Company Name */
#define ZBEE_ZCL_ATTR_ID_APPL_IDT_COMPANY_ID            0x0011  /* Company ID */
#define ZBEE_ZCL_ATTR_ID_APPL_IDT_BRAND_NAME            0x0012  /* Brand Name */
#define ZBEE_ZCL_ATTR_ID_APPL_IDT_BRAND_ID              0x0013  /* Brand ID */
#define ZBEE_ZCL_ATTR_ID_APPL_IDT_MODEL                 0x0014  /* Model */
#define ZBEE_ZCL_ATTR_ID_APPL_IDT_PART_NUM              0x0015  /* Part Number */
#define ZBEE_ZCL_ATTR_ID_APPL_IDT_PROD_REV              0x0016  /* Product Revision */
#define ZBEE_ZCL_ATTR_ID_APPL_IDT_SW_REV                0x0017  /* Software Revision */
#define ZBEE_ZCL_ATTR_ID_APPL_IDT_PROD_TYPE_NAME        0x0018  /* Product Type Name */
#define ZBEE_ZCL_ATTR_ID_APPL_IDT_PROD_TYPE_ID          0x0019  /* Product Type ID */
#define ZBEE_ZCL_ATTR_ID_APPL_IDT_CECED_SPEC_VER        0x001A  /* CECED Specification Version */
#define ZBEE_ZCL_APPL_IDT_COMPANY_ID_IC                 0x4943  /* Indesit Company */
#define ZBEE_ZCL_APPL_IDT_BRAND_ID_AR                   0x4152  /* Ariston */
#define ZBEE_ZCL_APPL_IDT_BRAND_ID_IN                   0x494E  /* Indesit */
#define ZBEE_ZCL_APPL_IDT_BRAND_ID_SC                   0x5343  /* Scholtes */
#define ZBEE_ZCL_APPL_IDT_BRAND_ID_ST                   0x5354  /* Stinol */
#define ZBEE_ZCL_APPL_IDT_PROD_TYPE_ID_WG               0x0000  /* WhiteGoods */
#define ZBEE_ZCL_APPL_IDT_PROD_TYPE_ID_DW               0x5601  /* Dishwasher */
#define ZBEE_ZCL_APPL_IDT_PROD_TYPE_ID_TD               0x5602  /* Tumble Dryer */
#define ZBEE_ZCL_APPL_IDT_PROD_TYPE_ID_WD               0x5603  /* Washer Dryer */
#define ZBEE_ZCL_APPL_IDT_PROD_TYPE_ID_WM               0x5604  /* Washing Machine */
#define ZBEE_ZCL_APPL_IDT_PROD_TYPE_ID_GO               0x5E01  /* Oven */
#define ZBEE_ZCL_APPL_IDT_PROD_TYPE_ID_HB               0x5E03  /* Hobs */
#define ZBEE_ZCL_APPL_IDT_PROD_TYPE_ID_OV               0x5E06  /* Electrical Oven */
#define ZBEE_ZCL_APPL_IDT_PROD_TYPE_ID_IH               0x5E09  /* Induction Hobs */
#define ZBEE_ZCL_APPL_IDT_PROD_TYPE_ID_RF               0x6601  /* Refrigerator Freezer */
#define ZBEE_ZCL_APPL_IDT_PROD_TYPE_NAME_ID_WG          0x0000  /* WhiteGoods */
#define ZBEE_ZCL_APPL_IDT_PROD_TYPE_NAME_ID_DW          0x4457  /* Dishwasher */
#define ZBEE_ZCL_APPL_IDT_PROD_TYPE_NAME_ID_TD          0x5444  /* Tumble Dryer */
#define ZBEE_ZCL_APPL_IDT_PROD_TYPE_NAME_ID_WD          0x5744  /* Washer Dryer */
#define ZBEE_ZCL_APPL_IDT_PROD_TYPE_NAME_ID_WM          0x574D  /* Washing Machine */
#define ZBEE_ZCL_APPL_IDT_PROD_TYPE_NAME_ID_GO          0x474F  /* Oven */
#define ZBEE_ZCL_APPL_IDT_PROD_TYPE_NAME_ID_HB          0x4842  /* Hobs */
#define ZBEE_ZCL_APPL_IDT_PROD_TYPE_NAME_ID_OV          0x4F56  /* Electrical Oven */
#define ZBEE_ZCL_APPL_IDT_PROD_TYPE_NAME_ID_IH          0x4948  /* Induction Hobs */
#define ZBEE_ZCL_APPL_IDT_PROD_TYPE_NAME_ID_RF          0x5246  /* Refrigerator Freezer */
#define ZBEE_ZCL_APPL_IDT_CECED_SPEC_VAL_1_0_NOT_CERT   0x10  /* Compliant with v1.0, not certified */
#define ZBEE_ZCL_APPL_IDT_CECED_SPEC_VAL_1_0_CERT       0x1A  /* Compliant with v1.0, certified */
#define ZBEE_ZCL_ATTR_ID_MET_IDT_COMPANY_NAME                   0x0000  /* Company Name */
#define ZBEE_ZCL_ATTR_ID_MET_IDT_METER_TYPE_ID                  0x0001  /* Meter Type ID */
#define ZBEE_ZCL_ATTR_ID_MET_IDT_DATA_QUALITY_ID                0x0004  /* Data Quality ID */
#define ZBEE_ZCL_ATTR_ID_MET_IDT_CUSTOMER_NAME                  0x0005  /* Customer Name */
#define ZBEE_ZCL_ATTR_ID_MET_IDT_MODEL                          0x0006  /* Model */
#define ZBEE_ZCL_ATTR_ID_MET_IDT_PART_NUM                       0x0007  /* Part Number */
#define ZBEE_ZCL_ATTR_ID_MET_IDT_PRODUCT_REVISION               0x0008  /* Product Revision */
#define ZBEE_ZCL_ATTR_ID_MET_IDT_SW_REVISION                    0x000a  /* Software Revision */
#define ZBEE_ZCL_ATTR_ID_MET_IDT_UTILITY_NAME                   0x000b  /* Utility Name */
#define ZBEE_ZCL_ATTR_ID_MET_IDT_POD                            0x000c  /* POD */
#define ZBEE_ZCL_ATTR_ID_MET_IDT_AVAILABLE_PWR                  0x000d  /* Available Power */
#define ZBEE_ZCL_ATTR_ID_MET_IDT_PWR_TH                         0x000e  /* Power Threshold */
#define ZBEE_ZCL_MET_IDT_MET_TYPE_UTILITY_1_METER               0x0000 /* Utility Primary Meter */
#define ZBEE_ZCL_MET_IDT_MET_TYPE_UTILITY_P_METER               0x0001 /* Utility Production Meter */
#define ZBEE_ZCL_MET_IDT_MET_TYPE_UTILITY_2_METER               0x0000 /* Utility Secondary Meter */
#define ZBEE_ZCL_MET_IDT_MET_TYPE_PRIVATE_1_METER               0x0100 /* Private Primary Meter */
#define ZBEE_ZCL_MET_IDT_MET_TYPE_PRIVATE_P_METER               0x0101 /* Private Primary Meter */
#define ZBEE_ZCL_MET_IDT_MET_TYPE_PRIVATE_2_METER               0x0102 /* Private Primary Meter */
#define ZBEE_ZCL_MET_IDT_MET_TYPE_GENERIC_METER                 0x0110 /* Generic Meter */
#define ZBEE_ZCL_MET_IDT_DATA_QLTY_ALL_DATA_CERTIF              0x0000 /* All Data Certified */
#define ZBEE_ZCL_MET_IDT_DATA_QLTY_ALL_CERTIF_WO_INST_PWR       0x0001 /* Only Instantaneous Power not Certified */
#define ZBEE_ZCL_MET_IDT_DATA_QLTY_ALL_CERTIF_WO_CUM_CONS       0x0002 /* Only Cumulated Consumption not Certified */
#define ZBEE_ZCL_MET_IDT_DATA_QLTY_NOT_CERTIF_DATA              0x0003 /* Not Certified Data */
#define ZBEE_ZCL_APPL_EVTALT_NUM_GENERIC_ETT              1
#define ZBEE_ZCL_APPL_EVTALT_NUM_STRUCT_ETT               8
#define ZBEE_ZCL_APPL_EVTALT_NUM_ETT                      (ZBEE_ZCL_APPL_EVTALT_NUM_GENERIC_ETT + \
#define ZBEE_ZCL_CMD_ID_APPL_EVTALT_GET_ALERTS_CMD        0x00  /* Get Alerts */
#define ZBEE_ZCL_CMD_ID_APPL_EVTALT_GET_ALERTS_RSP_CMD    0x00  /* Get Alerts Response */
#define ZBEE_ZCL_CMD_ID_APPL_EVTALT_ALERTS_NOTIF_CMD      0x01  /* Alerts Notification */
#define ZBEE_ZCL_CMD_ID_APPL_EVTALT_EVENT_NOTIF_CMD       0x02  /* Event Notification */
#define ZBEE_ZCL_APPL_EVTALT_COUNT_NUM_MASK               0x0F  /* Number of Alerts : [0..3] */
#define ZBEE_ZCL_APPL_EVTALT_COUNT_TYPE_MASK              0xF0  /* Type of Alerts : [4..7] */
#define ZBEE_ZCL_APPL_EVTALT_ALERT_ID_MASK                0x0000FF  /* Alerts Id : [0..7] */
#define ZBEE_ZCL_APPL_EVTALT_CATEGORY_MASK                0x000F00  /* Cetegory : [8..11] */
#define ZBEE_ZCL_APPL_EVTALT_STATUS_MASK                  0x003000  /* Presence / Recovery: [12..13] */
#define ZBEE_ZCL_APPL_EVTALT_RESERVED_MASK                0x00C000  /* Reserved : [14..15] */
#define ZBEE_ZCL_APPL_EVTALT_PROPRIETARY_MASK             0xFF0000  /* Non-Standardized / Proprietary : [16..23] */
#define ZBEE_ZCL_APPL_EVTALT_CATEGORY_RESERVED            0x00  /* Reserved */
#define ZBEE_ZCL_APPL_EVTALT_CATEGORY_WARNING             0x01  /* Warning */
#define ZBEE_ZCL_APPL_EVTALT_CATEGORY_DANGER              0x02  /* Danger */
#define ZBEE_ZCL_APPL_EVTALT_CATEGORY_FAILURE             0x03  /* Failure */
#define ZBEE_ZCL_APPL_EVTALT_STATUS_RECOVERY              0x00  /* Recovery */
#define ZBEE_ZCL_APPL_EVTALT_STATUS_PRESENCE              0x01  /* Presence */
#define ZBEE_ZCL_APPL_EVTALT_EVENT_END_OF_CYCLE           0x01  /* End Of Cycle */
#define ZBEE_ZCL_APPL_EVTALT_EVENT_RESERVED_1             0x02  /* Reserved */
#define ZBEE_ZCL_APPL_EVTALT_EVENT_RESERVED_2             0x03  /* Reserved */
#define ZBEE_ZCL_APPL_EVTALT_EVENT_TEMP_REACHED           0x04  /* Temperature Reached */
#define ZBEE_ZCL_APPL_EVTALT_EVENT_END_OF_COOKING         0x05  /* End Of Cooking */
#define ZBEE_ZCL_APPL_EVTALT_EVENT_SW_OFF                 0x06  /* Switching Off */
#define ZBEE_ZCL_APPL_EVTALT_EVENT_WRONG_DATA             0xf7  /* Wrong Data */
#define ZBEE_ZCL_APPL_STATS_NUM_GENERIC_ETT                     1
#define ZBEE_ZCL_APPL_STATS_NUM_LOGS_ETT                        16
#define ZBEE_ZCL_APPL_STATS_NUM_ETT                             (ZBEE_ZCL_APPL_STATS_NUM_GENERIC_ETT + \
#define ZBEE_ZCL_ATTR_ID_APPL_STATS_LOG_MAX_SIZE                0x0000  /* Log Max Size */
#define ZBEE_ZCL_ATTR_ID_APPL_STATS_LOG_QUEUE_MAX_SIZE          0x0001  /* Log Queue Max Size */
#define ZBEE_ZCL_CMD_ID_APPL_STATS_LOG_REQ                      0x00  /* Log Request */
#define ZBEE_ZCL_CMD_ID_APPL_STATS_LOG_QUEUE_REQ                0x01  /* Log Queue Request */
#define ZBEE_ZCL_CMD_ID_APPL_STATS_LOG_NOTIF                    0x00  /* Log Notification */
#define ZBEE_ZCL_CMD_ID_APPL_STATS_LOG_RSP                      0x01  /* Log Response */
#define ZBEE_ZCL_CMD_ID_APPL_STATS_LOG_QUEUE_RSP                0x02  /* Log Queue Response */
#define ZBEE_ZCL_CMD_ID_APPL_STATS_STATS_AVAILABLE              0x03  /* Statistics Available */
#define ZBEE_ZCL_APPL_STATS_INVALID_TIME                        0xffffffff /* Invalid UTC Time */
#define ZBEE_ZCL_ILLUM_MEAS_NUM_GENERIC_ETT                     1
#define ZBEE_ZCL_ILLUM_MEAS_NUM_ETT                             ZBEE_ZCL_ILLUM_MEAS_NUM_GENERIC_ETT
#define ZBEE_ZCL_ATTR_ID_ILLUM_MEAS_MEASURED_VALUE              0x0000  /* Measured Value */
#define ZBEE_ZCL_ATTR_ID_ILLUM_MEAS_MIN_MEASURED_VALUE          0x0001  /* Min Measured Value */
#define ZBEE_ZCL_ATTR_ID_ILLUM_MEAS_MAX_MEASURED_VALUE          0x0002  /* Max Measured Value */
#define ZBEE_ZCL_ATTR_ID_ILLUM_MEAS_TOLERANCE                   0x0003  /* Tolerance */
#define ZBEE_ZCL_ATTR_ID_ILLUM_MEAS_LIGHT_SENSOR_TYPE           0x0004  /* Light Sensor Type */
#define ZBEE_ZCL_ATTR_ID_ILLUM_MEAS_TOO_LOW_VALUE        0x0000  /* Too Low Value */
#define ZBEE_ZCL_ATTR_ID_ILLUM_MEAS_INVALID_VALUE        0x8000  /* Invalid Value */
#define ZBEE_ZCL_ATTR_ID_ILLUM_MEAS_MIN_LO_VALUE         0x0002  /* Minimum Value (Low Bound) */
#define ZBEE_ZCL_ATTR_ID_ILLUM_MEAS_MIN_HI_VALUE         0xfffd  /* Minimum Value (High Bound) */
#define ZBEE_ZCL_ATTR_ID_ILLUM_MEAS_MAX_LO_VALUE         0x0001  /* Maximum Value (Low Bound) */
#define ZBEE_ZCL_ATTR_ID_ILLUM_MEAS_MAX_HI_VALUE         0xfffe  /* Maximum Value (High Bound) */
#define ZBEE_ZCL_ATTR_ID_ILLUM_MEAS_TOL_LO_VALUE         0x0000  /* Tolerance (Low Bound) */
#define ZBEE_ZCL_ATTR_ID_ILLUM_MEAS_TOL_HI_VALUE         0x0800  /* Tolerance (Low Bound) */
#define ZBEE_ZCL_ILLUM_MEAS_SENSOR_TYPE_PHOTODIODE       0x00  /* Photodiode */
#define ZBEE_ZCL_ILLUM_MEAS_SENSOR_TYPE_CMOS             0x01  /* CMOS */
#define ZBEE_ZCL_TEMP_MEAS_NUM_GENERIC_ETT              1
#define ZBEE_ZCL_TEMP_MEAS_NUM_ETT                      ZBEE_ZCL_TEMP_MEAS_NUM_GENERIC_ETT
#define ZBEE_ZCL_ATTR_ID_TEMP_MEAS_MEASURED_VALUE       0x0000  /* Measured Value */
#define ZBEE_ZCL_ATTR_ID_TEMP_MEAS_MIN_MEASURED_VALUE   0x0001  /* Min Measured Value */
#define ZBEE_ZCL_ATTR_ID_TEMP_MEAS_MAX_MEASURED_VALUE   0x0002  /* Max Measured Value */
#define ZBEE_ZCL_ATTR_ID_TEMP_MEAS_TOLERANCE            0x0003  /* Tolerance */
#define ZBEE_ZCL_ATTR_ID_TEMP_MEAS_INVALID_VALUE        0x8000  /* Invalid Value */
#define ZBEE_ZCL_ATTR_ID_TEMP_MEAS_MIN_LO_VALUE         0x954d  /* Minimum Value (Low Bound) */
#define ZBEE_ZCL_ATTR_ID_TEMP_MEAS_MIN_HI_VALUE         0x7ffe  /* Minimum Value (High Bound) */
#define ZBEE_ZCL_ATTR_ID_TEMP_MEAS_MAX_LO_VALUE         0x954e  /* Maximum Value (Low Bound) */
#define ZBEE_ZCL_ATTR_ID_TEMP_MEAS_MAX_HI_VALUE         0x7fff  /* Maximum Value (High Bound) */
#define ZBEE_ZCL_ATTR_ID_TEMP_MEAS_TOL_LO_VALUE         0x0000  /* Tolerance (Low Bound) */
#define ZBEE_ZCL_ATTR_ID_TEMP_MEAS_TOL_HI_VALUE         0x0800  /* Tolerance (Low Bound) */
#define ZBEE_ZCL_PRESS_MEAS_NUM_GENERIC_ETT              1
#define ZBEE_ZCL_PRESS_MEAS_NUM_ETT                      ZBEE_ZCL_PRESS_MEAS_NUM_GENERIC_ETT
#define ZBEE_ZCL_ATTR_ID_PRESS_MEAS_MEASURED_VALUE       0x0000  /* Measured Value */
#define ZBEE_ZCL_ATTR_ID_PRESS_MEAS_MIN_MEASURED_VALUE   0x0001  /* Min Measured Value */
#define ZBEE_ZCL_ATTR_ID_PRESS_MEAS_MAX_MEASURED_VALUE   0x0002  /* Max Measured Value */
#define ZBEE_ZCL_ATTR_ID_PRESS_MEAS_TOLERANCE            0x0003  /* Tolerance */
#define ZBEE_ZCL_ATTR_ID_PRESS_MEAS_SCALED_VALUE         0x0010  /* Scaled Value */
#define ZBEE_ZCL_ATTR_ID_PRESS_MEAS_MIN_SCALED_VALUE     0x0011  /* Min Scaled Value */
#define ZBEE_ZCL_ATTR_ID_PRESS_MEAS_MAX_SCALED_VALUE     0x0012  /* Max Scaled Value */
#define ZBEE_ZCL_ATTR_ID_PRESS_MEAS_SCALED_TOLERANCE     0x0013  /* Scaled Tolerance */
#define ZBEE_ZCL_ATTR_ID_PRESS_MEAS_SCALE                0x0014  /* Scale */
#define ZBEE_ZCL_ATTR_ID_PRESS_MEAS_INVALID_VALUE        0x8000  /* Invalid Value */
#define ZBEE_ZCL_ATTR_ID_PRESS_MEAS_MIN_LO_VALUE         0x8001  /* Minimum Value (Low Bound) */
#define ZBEE_ZCL_ATTR_ID_PRESS_MEAS_MIN_HI_VALUE         0x7ffe  /* Minimum Value (High Bound) */
#define ZBEE_ZCL_ATTR_ID_PRESS_MEAS_MAX_LO_VALUE         0x8002  /* Maximum Value (Low Bound) */
#define ZBEE_ZCL_ATTR_ID_PRESS_MEAS_MAX_HI_VALUE         0x7fff  /* Maximum Value (High Bound) */
#define ZBEE_ZCL_ATTR_ID_PRESS_MEAS_TOL_LO_VALUE         0x0000  /* Tolerance (Low Bound) */
#define ZBEE_ZCL_ATTR_ID_PRESS_MEAS_TOL_HI_VALUE         0x0800  /* Tolerance (Low Bound) */
#define ZBEE_ZCL_ATTR_ID_PRESS_MEAS_SCALE_LO_VALUE       0x81  /* Scale (Low Bound) */
#define ZBEE_ZCL_ATTR_ID_PRESS_MEAS_SCALE_HI_VALUE       0x7f  /* Scale (Low Bound) */
#define ZBEE_ZCL_RELHUM_MEAS_NUM_GENERIC_ETT              1
#define ZBEE_ZCL_RELHUM_MEAS_NUM_ETT                      ZBEE_ZCL_RELHUM_MEAS_NUM_GENERIC_ETT
#define ZBEE_ZCL_ATTR_ID_RELHUM_MEAS_MEASURED_VALUE       0x0000  /* Measured Value */
#define ZBEE_ZCL_ATTR_ID_RELHUM_MEAS_MIN_MEASURED_VALUE   0x0001  /* Min Measured Value */
#define ZBEE_ZCL_ATTR_ID_RELHUM_MEAS_MAX_MEASURED_VALUE   0x0002  /* Max Measured Value */
#define ZBEE_ZCL_ATTR_ID_RELHUM_MEAS_TOLERANCE            0x0003  /* Tolerance */
#define ZBEE_ZCL_ATTR_ID_RELHUM_MEAS_INVALID_VALUE        0xffff  /* Invalid Value */
#define ZBEE_ZCL_ATTR_ID_RELHUM_MEAS_MIN_LO_VALUE         0x0000  /* Minimum Value (Low Bound) */
#define ZBEE_ZCL_ATTR_ID_RELHUM_MEAS_MIN_HI_VALUE         0x270f  /* Minimum Value (High Bound) */
#define ZBEE_ZCL_ATTR_ID_RELHUM_MEAS_MAX_LO_VALUE         0x0000  /* Maximum Value (Low Bound) */
#define ZBEE_ZCL_ATTR_ID_RELHUM_MEAS_MAX_HI_VALUE         0x2710  /* Maximum Value (High Bound) */
#define ZBEE_ZCL_ATTR_ID_RELHUM_MEAS_TOL_LO_VALUE         0x0000  /* Tolerance (Low Bound) */
#define ZBEE_ZCL_ATTR_ID_RELHUM_MEAS_TOL_HI_VALUE         0x0800  /* Tolerance (Low Bound) */
#define ZBEE_ZCL_MSG_NUM_GENERIC_ETT                   2
#define ZBEE_ZCL_MSG_NUM_ETT                           (ZBEE_ZCL_MSG_NUM_GENERIC_ETT)
#define ZBEE_ZCL_CMD_ID_MSG_GET_LAST_MSG                0x00  /* Get Last Message */
#define ZBEE_ZCL_CMD_ID_MSG_MSG_CONFIRM                 0x01  /* Message Confirmation */
#define ZBEE_ZCL_CMD_ID_MSG_DISPLAY_MSG                 0x00  /* Display Message */
#define ZBEE_ZCL_CMD_ID_MSG_CANCEL_MSG                  0x01  /* Cancel Message */
#define ZBEE_ZCL_MSG_CTRL_TX_MASK                       0x03
#define ZBEE_ZCL_MSG_CTRL_IMPORTANCE_MASK               0x0C
#define ZBEE_ZCL_MSG_CTRL_RESERVED_MASK                 0x70
#define ZBEE_ZCL_MSG_CTRL_CONFIRM_MASK                  0x80
#define ZBEE_ZCL_MSG_CTRL_TX_NORMAL_ONLY                0x00 /* Normal Transmission Only */
#define ZBEE_ZCL_MSG_CTRL_TX_NORMAL_ANON_INTERPAN       0x01 /* Normal and Anonymous Inter-PAN Transmission Only */
#define ZBEE_ZCL_MSG_CTRL_TX_ANON_INTERPAN_ONLY         0x02 /* Anonymous Inter-PAN Transmission Only */
#define ZBEE_ZCL_MSG_CTRL_IMPORTANCE_LOW                0x00 /* Low */
#define ZBEE_ZCL_MSG_CTRL_IMPORTANCE_MEDIUM             0x01 /* Medium */
#define ZBEE_ZCL_MSG_CTRL_IMPORTANCE_HIGH               0x02 /* High */
#define ZBEE_ZCL_MSG_CTRL_IMPORTANCE_CRITICAL           0x03 /* Critical */
#define ZBEE_ZCL_MSG_START_TIME_NOW                     0x00000000 /* Now */
#define ZBEE_ZCL_CMD_READ_ATTR                  0x00
#define ZBEE_ZCL_CMD_READ_ATTR_RESP             0x01
#define ZBEE_ZCL_CMD_WRITE_ATTR                 0x02
#define ZBEE_ZCL_CMD_WRITE_ATTR_UNDIVIDED       0x03
#define ZBEE_ZCL_CMD_WRITE_ATTR_RESP            0x04
#define ZBEE_ZCL_CMD_WRITE_ATTR_NO_RESP         0x05
#define ZBEE_ZCL_CMD_CONFIG_REPORT              0x06
#define ZBEE_ZCL_CMD_CONFIG_REPORT_RESP         0x07
#define ZBEE_ZCL_CMD_READ_REPORT_CONFIG         0x08
#define ZBEE_ZCL_CMD_READ_REPORT_CONFIG_RESP    0x09
#define ZBEE_ZCL_CMD_REPORT_ATTR                0x0a
#define ZBEE_ZCL_CMD_DEFAULT_RESP               0x0b
#define ZBEE_ZCL_CMD_DISCOVER_ATTR              0x0c
#define ZBEE_ZCL_CMD_DISCOVER_ATTR_RESP         0x0d
#define ZBEE_ZCL_CMD_READ_ATTR_STRUCT           0x0e
#define ZBEE_ZCL_CMD_WRITE_ATTR_STRUCT          0x0f
#define ZBEE_ZCL_CMD_WRITE_ATTR_STRUCT_RESP     0x10
#define ZBEE_ZCL_NO_DATA            0x00
#define ZBEE_ZCL_8_BIT_DATA         0x08
#define ZBEE_ZCL_16_BIT_DATA        0x09
#define ZBEE_ZCL_24_BIT_DATA        0x0a
#define ZBEE_ZCL_32_BIT_DATA        0x0b
#define ZBEE_ZCL_40_BIT_DATA        0x0c
#define ZBEE_ZCL_48_BIT_DATA        0x0d
#define ZBEE_ZCL_56_BIT_DATA        0x0e
#define ZBEE_ZCL_64_BIT_DATA        0x0f
#define ZBEE_ZCL_BOOLEAN            0x10
#define ZBEE_ZCL_8_BIT_BITMAP       0x18
#define ZBEE_ZCL_16_BIT_BITMAP      0x19
#define ZBEE_ZCL_24_BIT_BITMAP      0x1a
#define ZBEE_ZCL_32_BIT_BITMAP      0x1b
#define ZBEE_ZCL_40_BIT_BITMAP      0x1c
#define ZBEE_ZCL_48_BIT_BITMAP      0x1d
#define ZBEE_ZCL_56_BIT_BITMAP      0x1e
#define ZBEE_ZCL_64_BIT_BITMAP      0x1f
#define ZBEE_ZCL_8_BIT_UINT         0x20
#define ZBEE_ZCL_16_BIT_UINT        0x21
#define ZBEE_ZCL_24_BIT_UINT        0x22
#define ZBEE_ZCL_32_BIT_UINT        0x23
#define ZBEE_ZCL_40_BIT_UINT        0x24
#define ZBEE_ZCL_48_BIT_UINT        0x25
#define ZBEE_ZCL_56_BIT_UINT        0x26
#define ZBEE_ZCL_64_BIT_UINT        0x27
#define ZBEE_ZCL_8_BIT_INT          0x28
#define ZBEE_ZCL_16_BIT_INT         0x29
#define ZBEE_ZCL_24_BIT_INT         0x2a
#define ZBEE_ZCL_32_BIT_INT         0x2b
#define ZBEE_ZCL_40_BIT_INT         0x2c
#define ZBEE_ZCL_48_BIT_INT         0x2d
#define ZBEE_ZCL_56_BIT_INT         0x2e
#define ZBEE_ZCL_64_BIT_INT         0x2f
#define ZBEE_ZCL_8_BIT_ENUM         0x30
#define ZBEE_ZCL_16_BIT_ENUM        0x31
#define ZBEE_ZCL_SEMI_FLOAT         0x38
#define ZBEE_ZCL_SINGLE_FLOAT       0x39
#define ZBEE_ZCL_DOUBLE_FLOAT       0x3a
#define ZBEE_ZCL_OCTET_STRING       0x41
#define ZBEE_ZCL_CHAR_STRING        0x42
#define ZBEE_ZCL_LONG_OCTET_STRING  0x43
#define ZBEE_ZCL_LONG_CHAR_STRING   0x44
#define ZBEE_ZCL_ARRAY              0x48
#define ZBEE_ZCL_STRUCT             0x4c
#define ZBEE_ZCL_SET                0x50
#define ZBEE_ZCL_BAG                0x51
#define ZBEE_ZCL_TIME               0xe0
#define ZBEE_ZCL_DATE               0xe1
#define ZBEE_ZCL_UTC                0xe2
#define ZBEE_ZCL_CLUSTER_ID         0xe8
#define ZBEE_ZCL_ATTR_ID            0xe9
#define ZBEE_ZCL_BACNET_OID         0xea
#define ZBEE_ZCL_IEEE_ADDR          0xf0
#define ZBEE_ZCL_SECURITY_KEY       0xf1
#define ZBEE_ZCL_UNKNOWN            0xff
#define ZBEE_ZCL_INVALID_STR_LENGTH             0xff
#define ZBEE_ZCL_INVALID_LONG_STR_LENGTH        0xffff
#define ZBEE_ZCL_NUM_INDIVIDUAL_ETT             2
#define ZBEE_ZCL_NUM_ATTR_ETT                   64
#define ZBEE_ZCL_NUM_ARRAY_ELEM_ETT             16
#define ZBEE_ZCL_NUM_TOTAL_ETT                  (ZBEE_ZCL_NUM_INDIVIDUAL_ETT + ZBEE_ZCL_NUM_ATTR_ETT + ZBEE_ZCL_NUM_ARRAY_ELEM_ETT)
#define ZBEE_ZCL_DIR_REPORTED                   0
#define ZBEE_ZCL_DIR_RECEIVED                   1
#define ZBEE_ZCL_NSTIME_UTC_OFFSET              (((3*365 + 366)*7 + 2*365)*24*3600)
#define ZBEE_ZCL_STAT_SUCCESS                       0x00
#define ZBEE_ZCL_STAT_FAILURE                       0x01
#define ZBEE_ZCL_STAT_NOT_AUTHORIZED                0x7e
#define ZBEE_ZCL_STAT_RESERVED_FIELD_NOT_ZERO       0x7f
#define ZBEE_ZCL_STAT_MALFORMED_CMD                 0x80
#define ZBEE_ZCL_STAT_UNSUP_CLUSTER_CMD             0x81
#define ZBEE_ZCL_STAT_UNSUP_GENERAL_CMD             0x82
#define ZBEE_ZCL_STAT_UNSUP_MFR_CLUSTER_CMD         0x83
#define ZBEE_ZCL_STAT_UNSUP_MFR_GENERAL_CMD         0x84
#define ZBEE_ZCL_STAT_INVALID_FIELD                 0x85
#define ZBEE_ZCL_STAT_UNSUPPORTED_ATTR              0x86
#define ZBEE_ZCL_STAT_INVALID_VALUE                 0x87
#define ZBEE_ZCL_STAT_READ_ONLY                     0x88
#define ZBEE_ZCL_STAT_INSUFFICIENT_SPACE            0x89
#define ZBEE_ZCL_STAT_DUPLICATE_EXISTS              0x8a
#define ZBEE_ZCL_STAT_NOT_FOUND                     0x8b
#define ZBEE_ZCL_STAT_UNREPORTABLE_ATTR             0x8c
#define ZBEE_ZCL_STAT_INVALID_DATA_TYPE             0x8d
#define ZBEE_ZCL_STAT_INVALID_SELECTOR              0x8e
#define ZBEE_ZCL_STAT_WRITE_ONLY                    0x8f
#define ZBEE_ZCL_STAT_INCONSISTENT_STARTUP_STATE    0x90
#define ZBEE_ZCL_STAT_DEFINED_OUT_OF_BAND           0x91
#define ZBEE_ZCL_STAT_HARDWARE_FAILURE              0xc0
#define ZBEE_ZCL_STAT_SOFTWARE_FAILURE              0xc1
#define ZBEE_ZCL_STAT_CALIBRATION_ERROR             0xc2
#define ZBEE_ZDP_PROFILE        0x0000
#define ZBEE_ZDP_REQ_NWK_ADDR               0x0000
#define ZBEE_ZDP_REQ_IEEE_ADDR              0x0001
#define ZBEE_ZDP_REQ_NODE_DESC              0x0002
#define ZBEE_ZDP_REQ_POWER_DESC             0x0003
#define ZBEE_ZDP_REQ_SIMPLE_DESC            0x0004
#define ZBEE_ZDP_REQ_ACTIVE_EP              0x0005
#define ZBEE_ZDP_REQ_MATCH_DESC             0x0006
#define ZBEE_ZDP_REQ_COMPLEX_DESC           0x0010
#define ZBEE_ZDP_REQ_USER_DESC              0x0011
#define ZBEE_ZDP_REQ_DISCOVERY_CACHE        0x0012
#define ZBEE_ZDP_REQ_DEVICE_ANNCE           0x0013
#define ZBEE_ZDP_REQ_SET_USER_DESC          0x0014
#define ZBEE_ZDP_REQ_SYSTEM_SERVER_DISC     0x0015  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_REQ_STORE_DISCOVERY        0x0016  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_REQ_STORE_NODE_DESC        0x0017  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_REQ_STORE_POWER_DESC       0x0018  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_REQ_STORE_ACTIVE_EP        0x0019  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_REQ_STORE_SIMPLE_DESC      0x001a  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_REQ_REMOVE_NODE_CACHE      0x001b  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_REQ_FIND_NODE_CACHE        0x001c  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_REQ_EXT_SIMPLE_DESC        0x001d  /* ZigBee 2007 & later. */
#define ZBEE_ZDP_REQ_EXT_ACTIVE_EP          0x001e  /* ZigBee 2007 & later. */
#define ZBEE_ZDP_REQ_END_DEVICE_BIND        0x0020
#define ZBEE_ZDP_REQ_BIND                   0x0021
#define ZBEE_ZDP_REQ_UNBIND                 0x0022
#define ZBEE_ZDP_REQ_BIND_REGISTER          0x0023  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_REQ_REPLACE_DEVICE         0x0024  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_REQ_STORE_BAK_BIND_ENTRY   0x0025  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_REQ_REMOVE_BAK_BIND_ENTRY  0x0026  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_REQ_BACKUP_BIND_TABLE      0x0027  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_REQ_RECOVER_BIND_TABLE     0x0028  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_REQ_BACKUP_SOURCE_BIND     0x0029  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_REQ_RECOVER_SOURCE_BIND    0x002a  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_REQ_MGMT_NWK_DISC          0x0030
#define ZBEE_ZDP_REQ_MGMT_LQI               0x0031
#define ZBEE_ZDP_REQ_MGMT_RTG               0x0032
#define ZBEE_ZDP_REQ_MGMT_BIND              0x0033
#define ZBEE_ZDP_REQ_MGMT_LEAVE             0x0034
#define ZBEE_ZDP_REQ_MGMT_DIRECT_JOIN       0x0035
#define ZBEE_ZDP_REQ_MGMT_PERMIT_JOIN       0x0036  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_REQ_MGMT_CACHE             0x0037  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_REQ_MGMT_NWKUPDATE         0x0038  /* ZigBee 2007 & later. */
#define ZBEE_ZDP_RSP_NWK_ADDR               0x8000
#define ZBEE_ZDP_RSP_IEEE_ADDR              0x8001
#define ZBEE_ZDP_RSP_NODE_DESC              0x8002
#define ZBEE_ZDP_RSP_POWER_DESC             0x8003
#define ZBEE_ZDP_RSP_SIMPLE_DESC            0x8004
#define ZBEE_ZDP_RSP_ACTIVE_EP              0x8005
#define ZBEE_ZDP_RSP_MATCH_DESC             0x8006
#define ZBEE_ZDP_RSP_COMPLEX_DESC           0x8010
#define ZBEE_ZDP_RSP_USER_DESC              0x8011
#define ZBEE_ZDP_RSP_DISCOVERY_CACHE        0x8012
#define ZBEE_ZDP_RSP_CONF_USER_DESC         0x8014  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_RSP_SYSTEM_SERVER_DISC     0x8015  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_RSP_STORE_DISCOVERY        0x8016  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_RSP_STORE_NODE_DESC        0x8017  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_RSP_STORE_POWER_DESC       0x8018  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_RSP_STORE_ACTIVE_EP        0x8019  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_RSP_STORE_SIMPLE_DESC      0x801a  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_RSP_REMOVE_NODE_CACHE      0x801b  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_RSP_FIND_NODE_CACHE        0x801c  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_RSP_EXT_SIMPLE_DESC        0x801d  /* ZigBee 2007 & later. */
#define ZBEE_ZDP_RSP_EXT_ACTIVE_EP          0x801e  /* ZigBee 2007 & later. */
#define ZBEE_ZDP_RSP_END_DEVICE_BIND        0x8020
#define ZBEE_ZDP_RSP_BIND                   0x8021
#define ZBEE_ZDP_RSP_UNBIND                 0x8022
#define ZBEE_ZDP_RSP_BIND_REGISTER          0x8023  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_RSP_REPLACE_DEVICE         0x8024  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_RSP_STORE_BAK_BIND_ENTRY   0x8025  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_RSP_REMOVE_BAK_BIND_ENTRY  0x8026  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_RSP_BACKUP_BIND_TABLE      0x8027  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_RSP_RECOVER_BIND_TABLE     0x8028  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_RSP_BACKUP_SOURCE_BIND     0x8029  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_RSP_RECOVER_SOURCE_BIND    0x802a  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_RSP_MGMT_NWK_DISC          0x8030
#define ZBEE_ZDP_RSP_MGMT_LQI               0x8031
#define ZBEE_ZDP_RSP_MGMT_RTG               0x8032
#define ZBEE_ZDP_RSP_MGMT_BIND              0x8033
#define ZBEE_ZDP_RSP_MGMT_LEAVE             0x8034
#define ZBEE_ZDP_RSP_MGMT_DIRECT_JOIN       0x8035
#define ZBEE_ZDP_RSP_MGMT_PERMIT_JOIN       0x8036  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_RSP_MGMT_CACHE             0x8037  /* ZigBee 2006 & later. */
#define ZBEE_ZDP_RSP_MGMT_NWKUPDATE         0x8038  /* ZigBee 2007 & later. */
#define ZBEE_ZDP_MSG_RESPONSE_BIT           0x8000
#define ZBEE_ZDP_MSG_MASK                   (ZBEE_ZDP_MSG_RESPONSE_BIT-1)
#define ZBEE_ZDP_MSG_RESPONSE_BIT_2003      0x0080
#define ZBEE_ZDP_MSG_MASK_2003              (ZBEE_ZDP_MSG_RESPONSE_BIT_2003-1)
#define ZBEE_ZDP_STATUS_SUCCESS             0x00
#define ZBEE_ZDP_STATUS_INV_REQUESTTYPE     0x80
#define ZBEE_ZDP_STATUS_DEVICE_NOT_FOUND    0x81
#define ZBEE_ZDP_STATUS_INVALID_EP          0x82
#define ZBEE_ZDP_STATUS_NOT_ACTIVE          0x83
#define ZBEE_ZDP_STATUS_NOT_SUPPORTED       0x84
#define ZBEE_ZDP_STATUS_TIMEOUT             0x85
#define ZBEE_ZDP_STATUS_NO_MATCH            0x86
#define ZBEE_ZDP_STATUS_NO_ENTRY            0x88
#define ZBEE_ZDP_STATUS_NO_DESCRIPTOR       0x89
#define ZBEE_ZDP_STATUS_INSUFFICIENT_SPACE  0x8a
#define ZBEE_ZDP_STATUS_NOT_PERMITTED       0x8b
#define ZBEE_ZDP_STATUS_TABLE_FULL          0x8c
#define ZBEE_ZDP_REQ_TYPE_SINGLE            0x00
#define ZBEE_ZDP_REQ_TYPE_EXTENDED          0x01
#define ZBEE_ZDP_NODE_TYPE                  0x0007
#define ZBEE_ZDP_NODE_TYPE_COORD            0x0000
#define ZBEE_ZDP_NODE_TYPE_FFD              0x0001
#define ZBEE_ZDP_NODE_TYPE_RFD              0x0002
#define ZBEE_ZDP_NODE_COMPLEX               0x0008
#define ZBEE_ZDP_NODE_USER                  0x0010
#define ZBEE_ZDP_NODE_APS                   0x0700
#define ZBEE_ZDP_NODE_FREQ                  0xf800
#define ZBEE_ZDP_NODE_FREQ_868MHZ           0x0800
#define ZBEE_ZDP_NODE_FREQ_900MHZ           0x2000
#define ZBEE_ZDP_NODE_FREQ_2400MHZ          0x4000
#define ZBEE_ZDP_NODE_SERVER_PRIMARY_TRUST  0x0001
#define ZBEE_ZDP_NODE_SERVER_BACKUP_TRUST   0x0002
#define ZBEE_ZDP_NODE_SERVER_PRIMARY_BIND   0x0004
#define ZBEE_ZDP_NODE_SERVER_BACKUP_BIND    0x0008
#define ZBEE_ZDP_NODE_SERVER_PRIMARY_DISC   0x0010
#define ZBEE_ZDP_NODE_SERVER_BACKUP_DISC    0x0020
#define ZBEE_ZDP_POWER_MODE                 0x000f
#define ZBEE_ZDP_POWER_MODE_RX_ON           0x0000
#define ZBEE_ZDP_POWER_MODE_RX_PERIODIC     0x0001
#define ZBEE_ZDP_POWER_MODE_RX_STIMULATE    0x0002
#define ZBEE_ZDP_POWER_AVAIL                0x00f0
#define ZBEE_ZDP_POWER_AVAIL_AC             0x0010
#define ZBEE_ZDP_POWER_AVAIL_RECHARGEABLE   0x0020
#define ZBEE_ZDP_POWER_AVAIL_DISPOSEABLE    0x0040
#define ZBEE_ZDP_POWER_SOURCE               0x0f00
#define ZBEE_ZDP_POWER_SOURCE_AC            0x0100
#define ZBEE_ZDP_POWER_SOURCE_RECHARGEABLE  0x0200
#define ZBEE_ZDP_POWER_SOURCE_DISPOSEABLE   0x0400
#define ZBEE_ZDP_POWER_LEVEL                0xf000
#define ZBEE_ZDP_POWER_LEVEL_FULL           0xc000
#define ZBEE_ZDP_POWER_LEVEL_OK             0x8000
#define ZBEE_ZDP_POWER_LEVEL_LOW            0x4000
#define ZBEE_ZDP_POWER_LEVEL_CRITICAL       0x0000
#define ZBEE_ZDP_ADDR_MODE_GROUP            0x01
#define ZBEE_ZDP_ADDR_MODE_UNICAST          0x03
#define ZBEE_ZDP_MGMT_LEAVE_CHILDREN        0x80
#define ZBEE_ZDP_MGMT_LEAVE_REJOIN          0x40
#define ZBEE_ZDP_NWKUPDATE_SCAN_MAX         0x05
#define ZBEE_ZDP_NWKUPDATE_CHANNEL_HOP      0xfe
#define ZBEE_ZDP_NWKUPDATE_PARAMETERS       0xff
#define ZBEE_BCAST_ALL                  0xffff
#define ZBEE_BCAST_ACTIVE               0xfffd
#define ZBEE_BCAST_ROUTERS              0xfffc
#define ZBEE_BCAST_LOW_POWER_ROUTERS    0xfffb
#define ZBEE_CINFO_ALT_COORD        IEEE802154_CMD_CINFO_ALT_PAN_COORD
#define ZBEE_CINFO_FFD              IEEE802154_CMD_CINFO_DEVICE_TYPE
#define ZBEE_CINFO_POWER            IEEE802154_CMD_CINFO_POWER_SRC
#define ZBEE_CINFO_IDLE_RX          IEEE802154_CMD_CINFO_IDLE_RX
#define ZBEE_CINFO_SECURITY         IEEE802154_CMD_CINFO_SEC_CuapABLE
#define ZBEE_CINFO_ALLOC            IEEE802154_CMD_CINFO_ALLOC_ADDR
#define ZBEE_VERSION_PROTOTYPE      0 /* Does this even exist? */
#define ZBEE_VERSION_2004           1 /* Re: 053474r06ZB_TSC-ZigBeeSpecification.pdf */
#define ZBEE_VERSION_2007           2 /* Re: 053474r17ZB_TSC-ZigBeeSpecification.pdf */
#define ZBEE_DEVICE_PROFILE                 0x0000
#define ZBEE_PROFILE_IPM                    0x0101
#define ZBEE_PROFILE_T1                     0x0103
#define ZBEE_PROFILE_HA                     0x0104
#define ZBEE_PROFILE_CBA                    0x0105
#define ZBEE_PROFILE_WSN                    0x0106
#define ZBEE_PROFILE_TA                     0x0107
#define ZBEE_PROFILE_HC                     0x0108
#define ZBEE_PROFILE_SE                     0x0109
#define ZBEE_PROFILE_STD_MIN                0x0000
#define ZBEE_PROFILE_STD_MAX                0x7eff
#define ZBEE_PROFILE_T2                     0x7f01
#define ZBEE_PROFILE_RSVD0_MIN              0x7f00
#define ZBEE_PROFILE_RSVD0_MAX              0x7fff
#define ZBEE_PROFILE_RSVD1_MIN              0x8000
#define ZBEE_PROFILE_RSVD1_MAX              0xbeff
#define ZBEE_PROFILE_IEEE_1451_5            0xbf00
#define ZBEE_PROFILE_MFR_SPEC_ORG_MIN       0xbf00
#define ZBEE_PROFILE_MFR_SPEC_ORG_MAX       0xbfff
#define ZBEE_PROFILE_CIRRONET_0_MIN         0xc000
#define ZBEE_PROFILE_CIRRONET_0_MAX         0xc002
#define ZBEE_PROFILE_CHIPCON_MIN            0xc003
#define ZBEE_PROFILE_CHIPCON_MAX            0xc00c
#define ZBEE_PROFILE_EMBER_MIN              0xc00d
#define ZBEE_PROFILE_EMBER_MAX              0xc016
#define ZBEE_PROFILE_NTS_MIN                0xc017
#define ZBEE_PROFILE_NTS_MAX                0xc020
#define ZBEE_PROFILE_FREESCALE_MIN          0xc021
#define ZBEE_PROFILE_FREESCALE_MAX          0xc02a
#define ZBEE_PROFILE_IPCOM_MIN              0xc02b
#define ZBEE_PROFILE_IPCOM_MAX              0xc034
#define ZBEE_PROFILE_SAN_JUAN_MIN           0xc035
#define ZBEE_PROFILE_SAN_JUAN_MAX           0xc036
#define ZBEE_PROFILE_TUV_MIN                0xc037
#define ZBEE_PROFILE_TUV_MAX                0xc040
#define ZBEE_PROFILE_COMPXS_MIN             0xc041
#define ZBEE_PROFILE_COMPXS_MAX             0xc04a
#define ZBEE_PROFILE_BM_MIN                 0xc04b
#define ZBEE_PROFILE_BM_MAX                 0xc04d
#define ZBEE_PROFILE_AWAREPOINT_MIN         0xc04e
#define ZBEE_PROFILE_AWAREPOINT_MAX         0xc057
#define ZBEE_PROFILE_SAN_JUAN_1_MIN         0xc058
#define ZBEE_PROFILE_SAN_JUAN_1_MAX         0xc05d
#define ZBEE_PROFILE_PHILIPS_MIN            0xc05e
#define ZBEE_PROFILE_PHILIPS_MAX            0xc067
#define ZBEE_PROFILE_LUXOFT_MIN             0xc068
#define ZBEE_PROFILE_LUXOFT_MAX             0xc071
#define ZBEE_PROFILE_KORWIN_MIN             0xc072
#define ZBEE_PROFILE_KORWIN_MAX             0xc07b
#define ZBEE_PROFILE_1_RF_MIN               0xc07c
#define ZBEE_PROFILE_1_RF_MAX               0xc085
#define ZBEE_PROFILE_STG_MIN                0xc086
#define ZBEE_PROFILE_STG_MAX                0xc08f
#define ZBEE_PROFILE_TELEGESIS_MIN          0xc090
#define ZBEE_PROFILE_TELEGESIS_MAX          0xc099
#define ZBEE_PROFILE_CIRRONET_1_MIN         0xc09a
#define ZBEE_PROFILE_CIRRONET_1_MAX         0xc0a0
#define ZBEE_PROFILE_VISIONIC_MIN           0xc0a1
#define ZBEE_PROFILE_VISIONIC_MAX           0xc0aa
#define ZBEE_PROFILE_INSTA_MIN              0xc0ab
#define ZBEE_PROFILE_INSTA_MAX              0xc0b4
#define ZBEE_PROFILE_ATALUM_MIN             0xc0b5
#define ZBEE_PROFILE_ATALUM_MAX             0xc0be
#define ZBEE_PROFILE_ATMEL_MIN              0xc0bf
#define ZBEE_PROFILE_ATMEL_MAX              0xc0c8
#define ZBEE_PROFILE_DEVELCO_MIN            0xc0c9
#define ZBEE_PROFILE_DEVELCO_MAX            0xc0d2
#define ZBEE_PROFILE_HONEYWELL_MIN          0xc0d3
#define ZBEE_PROFILE_HONEYWELL_MAX          0xc0dc
#define ZBEE_PROFILE_NEC_MIN                0xc0dd
#define ZBEE_PROFILE_NEC_MAX                0xc0e6
#define ZBEE_PROFILE_YAMATAKE_MIN           0xc0e7
#define ZBEE_PROFILE_YAMATAKE_MAX           0xc0f0
#define ZBEE_PROFILE_TENDRIL_MIN            0xc0f1
#define ZBEE_PROFILE_TENDRIL_MAX            0xc0fa
#define ZBEE_PROFILE_ASSA_MIN               0xc0fb
#define ZBEE_PROFILE_ASSA_MAX               0xc104
#define ZBEE_PROFILE_MAXSTREAM_MIN          0xc105
#define ZBEE_PROFILE_MAXSTREAM_MAX          0xc10e
#define ZBEE_PROFILE_XANADU_MIN             0xc10f
#define ZBEE_PROFILE_XANADU_MAX             0xc118
#define ZBEE_PROFILE_NEUROCOM_MIN           0xc119
#define ZBEE_PROFILE_NEUROCOM_MAX           0xc122
#define ZBEE_PROFILE_III_MIN                0xc123
#define ZBEE_PROFILE_III_MAX                0xc12c
#define ZBEE_PROFILE_VANTAGE_MIN            0xc12d
#define ZBEE_PROFILE_VANTAGE_MAX            0xc12f
#define ZBEE_PROFILE_ICONTROL_MIN           0xc130
#define ZBEE_PROFILE_ICONTROL_MAX           0xc139
#define ZBEE_PROFILE_RAYMARINE_MIN          0xc13a
#define ZBEE_PROFILE_RAYMARINE_MAX          0xc143
#define ZBEE_PROFILE_RENESAS_MIN            0xc144
#define ZBEE_PROFILE_RENESAS_MAX            0xc14d
#define ZBEE_PROFILE_LSR_MIN                0xc14e
#define ZBEE_PROFILE_LSR_MAX                0xc157
#define ZBEE_PROFILE_ONITY_MIN              0xc158
#define ZBEE_PROFILE_ONITY_MAX              0xc161
#define ZBEE_PROFILE_MONO_MIN               0xc162
#define ZBEE_PROFILE_MONO_MAX               0xc16b
#define ZBEE_PROFILE_RFT_MIN                0xc16c
#define ZBEE_PROFILE_RFT_MAX                0xc175
#define ZBEE_PROFILE_ITRON_MIN              0xc176
#define ZBEE_PROFILE_ITRON_MAX              0xc17f
#define ZBEE_PROFILE_TRITECH_MIN            0xc180
#define ZBEE_PROFILE_TRITECH_MAX            0xc189
#define ZBEE_PROFILE_EMBEDIT_MIN            0xc18a
#define ZBEE_PROFILE_EMBEDIT_MAX            0xc193
#define ZBEE_PROFILE_S3C_MIN                0xc194
#define ZBEE_PROFILE_S3C_MAX                0xc19d
#define ZBEE_PROFILE_SIEMENS_MIN            0xc19e
#define ZBEE_PROFILE_SIEMENS_MAX            0xc1a7
#define ZBEE_PROFILE_MINDTECH_MIN           0xc1a8
#define ZBEE_PROFILE_MINDTECH_MAX           0xc1b1
#define ZBEE_PROFILE_LGE_MIN                0xc1b2
#define ZBEE_PROFILE_LGE_MAX                0xc1bb
#define ZBEE_PROFILE_MITSUBISHI_MIN         0xc1bc
#define ZBEE_PROFILE_MITSUBISHI_MAX         0xc1c5
#define ZBEE_PROFILE_JOHNSON_MIN            0xc1c6
#define ZBEE_PROFILE_JOHNSON_MAX            0xc1cf
#define ZBEE_PROFILE_PRI_MIN                0xc1d0
#define ZBEE_PROFILE_PRI_MAX                0xc1d9
#define ZBEE_PROFILE_KNICK_MIN              0xc1da
#define ZBEE_PROFILE_KNICK_MAX              0xc1e3
#define ZBEE_PROFILE_VICONICS_MIN           0xc1e4
#define ZBEE_PROFILE_VICONICS_MAX           0xc1ed
#define ZBEE_PROFILE_FLEXIPANEL_MIN         0xc1ee
#define ZBEE_PROFILE_FLEXIPANEL_MAX         0xc1f7
#define ZBEE_PROFILE_TRANE_MIN              0xc1f8
#define ZBEE_PROFILE_TRANE_MAX              0xc201
#define ZBEE_PROFILE_JENNIC_MIN             0xc202
#define ZBEE_PROFILE_JENNIC_MAX             0xc20b
#define ZBEE_PROFILE_LIG_MIN                0xc20c
#define ZBEE_PROFILE_LIG_MAX                0xc215
#define ZBEE_PROFILE_ALERTME_MIN            0xc216
#define ZBEE_PROFILE_ALERTME_MAX            0xc21f
#define ZBEE_PROFILE_DAINTREE_MIN           0xc220
#define ZBEE_PROFILE_DAINTREE_MAX           0xc229
#define ZBEE_PROFILE_AIJI_MIN               0xc22a
#define ZBEE_PROFILE_AIJI_MAX               0xc233
#define ZBEE_PROFILE_TEL_ITALIA_MIN         0xc234
#define ZBEE_PROFILE_TEL_ITALIA_MAX         0xc23d
#define ZBEE_PROFILE_MIKROKRETS_MIN         0xc23e
#define ZBEE_PROFILE_MIKROKRETS_MAX         0xc247
#define ZBEE_PROFILE_OKI_MIN                0xc248
#define ZBEE_PROFILE_OKI_MAX                0xc251
#define ZBEE_PROFILE_NEWPORT_MIN            0xc252
#define ZBEE_PROFILE_NEWPORT_MAX            0xc25b
#define ZBEE_PROFILE_C4_CL                  0xc25d
#define ZBEE_PROFILE_C4_MIN                 0xc25c
#define ZBEE_PROFILE_C4_MAX                 0xc265
#define ZBEE_PROFILE_STM_MIN                0xc266
#define ZBEE_PROFILE_STM_MAX                0xc26f
#define ZBEE_PROFILE_ASN_0_MIN              0xc270
#define ZBEE_PROFILE_ASN_0_MAX              0xc270
#define ZBEE_PROFILE_DCSI_MIN               0xc271
#define ZBEE_PROFILE_DCSI_MAX               0xc27a
#define ZBEE_PROFILE_FRANCE_TEL_MIN         0xc27b
#define ZBEE_PROFILE_FRANCE_TEL_MAX         0xc284
#define ZBEE_PROFILE_MUNET_MIN              0xc285
#define ZBEE_PROFILE_MUNET_MAX              0xc28e
#define ZBEE_PROFILE_AUTANI_MIN             0xc28f
#define ZBEE_PROFILE_AUTANI_MAX             0xc298
#define ZBEE_PROFILE_COL_VNET_MIN           0xc299
#define ZBEE_PROFILE_COL_VNET_MAX           0xc2a2
#define ZBEE_PROFILE_AEROCOMM_MIN           0xc2a3
#define ZBEE_PROFILE_AEROCOMM_MAX           0xc2ac
#define ZBEE_PROFILE_SI_LABS_MIN            0xc2ad
#define ZBEE_PROFILE_SI_LABS_MAX            0xc2b6
#define ZBEE_PROFILE_INNCOM_MIN             0xc2b7
#define ZBEE_PROFILE_INNCOM_MAX             0xc2c0
#define ZBEE_PROFILE_CANNON_MIN             0xc2c1
#define ZBEE_PROFILE_CANNON_MAX             0xc2ca
#define ZBEE_PROFILE_SYNAPSE_MIN            0xc2cb
#define ZBEE_PROFILE_SYNAPSE_MAX            0xc2d4
#define ZBEE_PROFILE_FPS_MIN                0xc2d5
#define ZBEE_PROFILE_FPS_MAX                0xc2de
#define ZBEE_PROFILE_CLS_MIN                0xc2df
#define ZBEE_PROFILE_CLS_MAX                0xc2e8
#define ZBEE_PROFILE_CRANE_MIN              0xc2e9
#define ZBEE_PROFILE_CRANE_MAX              0xc2f2
#define ZBEE_PROFILE_ASN_1_MIN              0xc2f3
#define ZBEE_PROFILE_ASN_1_MAX              0xc2fb
#define ZBEE_PROFILE_MOBILARM_MIN           0xc2fc
#define ZBEE_PROFILE_MOBILARM_MAX           0xc305
#define ZBEE_PROFILE_IMONITOR_MIN           0xc306
#define ZBEE_PROFILE_IMONITOR_MAX           0xc30f
#define ZBEE_PROFILE_BARTECH_MIN            0xc310
#define ZBEE_PROFILE_BARTECH_MAX            0xc319
#define ZBEE_PROFILE_MESHNETICS_MIN         0xc31a
#define ZBEE_PROFILE_MESHNETICS_MAX         0xc323
#define ZBEE_PROFILE_LS_IND_MIN             0xc324
#define ZBEE_PROFILE_LS_IND_MAX             0xc32d
#define ZBEE_PROFILE_CASON_MIN              0xc32e
#define ZBEE_PROFILE_CASON_MAX              0xc337
#define ZBEE_PROFILE_WLESS_GLUE_MIN         0xc338
#define ZBEE_PROFILE_WLESS_GLUE_MAX         0xc341
#define ZBEE_PROFILE_ELSTER_MIN             0xc342
#define ZBEE_PROFILE_ELSTER_MAX             0xc34b
#define ZBEE_PROFILE_ONSET_MIN              0xc34c
#define ZBEE_PROFILE_ONSET_MAX              0xc355
#define ZBEE_PROFILE_RIGA_MIN               0xc356
#define ZBEE_PROFILE_RIGA_MAX               0xc35f
#define ZBEE_PROFILE_ENERGATE_MIN           0xc360
#define ZBEE_PROFILE_ENERGATE_MAX           0xc369
#define ZBEE_PROFILE_VANTAGE_1_MIN          0xc36a
#define ZBEE_PROFILE_VANTAGE_1_MAX          0xc370
#define ZBEE_PROFILE_CONMED_MIN             0xc371
#define ZBEE_PROFILE_CONMED_MAX             0xc37a
#define ZBEE_PROFILE_SMS_TEC_MIN            0xc37b
#define ZBEE_PROFILE_SMS_TEC_MAX            0xc384
#define ZBEE_PROFILE_POWERMAND_MIN          0xc385
#define ZBEE_PROFILE_POWERMAND_MAX          0xc38e
#define ZBEE_PROFILE_SCHNEIDER_MIN          0xc38f
#define ZBEE_PROFILE_SCHNEIDER_MAX          0xc398
#define ZBEE_PROFILE_EATON_MIN              0xc399
#define ZBEE_PROFILE_EATON_MAX              0xc3a2
#define ZBEE_PROFILE_TELULAR_MIN            0xc3a3
#define ZBEE_PROFILE_TELULAR_MAX            0xc3ac
#define ZBEE_PROFILE_DELPHI_MIN             0xc3ad
#define ZBEE_PROFILE_DELPHI_MAX             0xc3b6
#define ZBEE_PROFILE_EPISENSOR_MIN          0xc3b7
#define ZBEE_PROFILE_EPISENSOR_MAX          0xc3c0
#define ZBEE_PROFILE_LANDIS_GYR_MIN         0xc3c1
#define ZBEE_PROFILE_LANDIS_GYR_MAX         0xc3ca
#define ZBEE_PROFILE_SHURE_MIN              0xc3cb
#define ZBEE_PROFILE_SHURE_MAX              0xc3d4
#define ZBEE_PROFILE_COMVERGE_MIN           0xc3d5
#define ZBEE_PROFILE_COMVERGE_MAX           0xc3df
#define ZBEE_PROFILE_KABA_MIN               0xc3e0
#define ZBEE_PROFILE_KABA_MAX               0xc3e9
#define ZBEE_PROFILE_HIDALGO_MIN            0xc3ea
#define ZBEE_PROFILE_HIDALGO_MAX            0xc3f3
#define ZBEE_PROFILE_AIR2APP_MIN            0xc3f4
#define ZBEE_PROFILE_AIR2APP_MAX            0xc3fd
#define ZBEE_PROFILE_AMX_MIN                0xc3fe
#define ZBEE_PROFILE_AMX_MAX                0xc407
#define ZBEE_PROFILE_EDMI_MIN               0xc408
#define ZBEE_PROFILE_EDMI_MAX               0xc411
#define ZBEE_PROFILE_CYAN_MIN               0xc412
#define ZBEE_PROFILE_CYAN_MAX               0xc41b
#define ZBEE_PROFILE_SYS_SPA_MIN            0xc41c
#define ZBEE_PROFILE_SYS_SPA_MAX            0xc425
#define ZBEE_PROFILE_TELIT_MIN              0xc426
#define ZBEE_PROFILE_TELIT_MAX              0xc42f
#define ZBEE_PROFILE_KAGA_MIN               0xc430
#define ZBEE_PROFILE_KAGA_MAX               0xc439
#define ZBEE_PROFILE_4_NOKS_MIN             0xc43a
#define ZBEE_PROFILE_4_NOKS_MAX             0xc443
#define ZBEE_PROFILE_PROFILE_SYS_MIN        0xc444
#define ZBEE_PROFILE_PROFILE_SYS_MAX        0xc44d
#define ZBEE_PROFILE_FREESTYLE_MIN          0xc44e
#define ZBEE_PROFILE_FREESTYLE_MAX          0xc457
#define ZBEE_PROFILE_REMOTE_MIN             0xc458
#define ZBEE_PROFILE_REMOTE_MAX             0xc461
#define ZBEE_PROFILE_TRANE_RES_MIN          0xc462
#define ZBEE_PROFILE_TRANE_RES_MAX          0xc46b
#define ZBEE_PROFILE_WAVECOM_MIN            0xc46c
#define ZBEE_PROFILE_WAVECOM_MAX            0xc475
#define ZBEE_PROFILE_GE_MIN                 0xc476
#define ZBEE_PROFILE_GE_MAX                 0xc47f
#define ZBEE_PROFILE_MESHWORKS_MIN          0xc480
#define ZBEE_PROFILE_MESHWORKS_MAX          0xc489
#define ZBEE_PROFILE_ENERGY_OPT_MIN         0xc48a
#define ZBEE_PROFILE_ENERGY_OPT_MAX         0xc493
#define ZBEE_PROFILE_ELLIPS_MIN             0xc494
#define ZBEE_PROFILE_ELLIPS_MAX             0xc49d
#define ZBEE_PROFILE_CEDO_MIN               0xc49e
#define ZBEE_PROFILE_CEDO_MAX               0xc4a7
#define ZBEE_PROFILE_A_D_MIN                0xc4a8
#define ZBEE_PROFILE_A_D_MAX                0xc4b1
#define ZBEE_PROFILE_CARRIER_MIN            0xc4b2
#define ZBEE_PROFILE_CARRIER_MAX            0xc4bb
#define ZBEE_PROFILE_PASSIVESYS_MIN         0xc4bc
#define ZBEE_PROFILE_PASSIVESYS_MAX         0xc4bd
#define ZBEE_PROFILE_G4S_JUSTICE_MIN        0xc4be
#define ZBEE_PROFILE_G4S_JUSTICE_MAX        0xc4bf
#define ZBEE_PROFILE_SYCHIP_MIN             0xc4c0
#define ZBEE_PROFILE_SYCHIP_MAX             0xc4c1
#define ZBEE_PROFILE_MMB_MIN                0xc4c2
#define ZBEE_PROFILE_MMB_MAX                0xc4c3
#define ZBEE_PROFILE_SUNRISE_MIN            0xc4c4
#define ZBEE_PROFILE_SUNRISE_MAX            0xc4c5
#define ZBEE_PROFILE_MEMTEC_MIN             0xc4c6
#define ZBEE_PROFILE_MEMTEC_MAX             0xc4c7
#define ZBEE_PROFILE_HOME_AUTO_MIN          0xc4c8
#define ZBEE_PROFILE_HOME_AUTO_MAX          0xc4c9
#define ZBEE_PROFILE_BRITISH_GAS_MIN        0xc4ca
#define ZBEE_PROFILE_BRITISH_GAS_MAX        0xc4cb
#define ZBEE_PROFILE_SENTEC_MIN             0xc4cc
#define ZBEE_PROFILE_SENTEC_MAX             0xc4cd
#define ZBEE_PROFILE_NAVETAS_MIN            0xc4ce
#define ZBEE_PROFILE_NAVETAS_MAX            0xc4cf
#define ZBEE_PROFILE_ENERNOC_MIN            0xc4d0
#define ZBEE_PROFILE_ENERNOC_MAX            0xc4d1
#define ZBEE_PROFILE_ELTAV_MIN              0xc4d2
#define ZBEE_PROFILE_ELTAV_MAX              0xc4d3
#define ZBEE_PROFILE_XSTREAMHD_MIN          0xc4d4
#define ZBEE_PROFILE_XSTREAMHD_MAX          0xc4d5
#define ZBEE_PROFILE_GREEN_MIN              0xc4d6
#define ZBEE_PROFILE_GREEN_MAX              0xc4d7
#define ZBEE_PROFILE_OMRON_MIN              0xc4d8
#define ZBEE_PROFILE_OMRON_MAX              0xc4d9
#define ZBEE_PROFILE_NEC_TOKIN_MIN          0xc4e0
#define ZBEE_PROFILE_NEC_TOKIN_MAX          0xc4e1
#define ZBEE_PROFILE_PEEL_MIN               0xc4e2
#define ZBEE_PROFILE_PEEL_MAX               0xc4e3
#define ZBEE_PROFILE_ELECTROLUX_MIN         0xc4e4
#define ZBEE_PROFILE_ELECTROLUX_MAX         0xc4e5
#define ZBEE_PROFILE_SAMSUNG_MIN            0xc4e6
#define ZBEE_PROFILE_SAMSUNG_MAX            0xc4e7
#define ZBEE_PROFILE_MAINSTREAM_MIN         0xc4e8
#define ZBEE_PROFILE_MAINSTREAM_MAX         0xc4e9
#define ZBEE_PROFILE_UNALLOCATED_MIN        0xc000
#define ZBEE_PROFILE_UNALLOCATED_MAX        0xffff
#define ZBEE_ZCL_FCF_FRAME_TYPE             0x03
#define ZBEE_ZCL_FCF_MFR_SPEC               0x04
#define ZBEE_ZCL_FCF_DIRECTION              0x08
#define ZBEE_ZCL_FCF_DISABLE_DEFAULT_RESP   0x10
#define ZBEE_ZCL_FCF_PROFILE_WIDE           0x00
#define ZBEE_ZCL_FCF_CLUSTER_SPEC           0x01
#define ZBEE_ZCL_FCF_TO_SERVER              0x00
#define ZBEE_ZCL_FCF_TO_CLIENT              0x01
#define ZBEE_MFG_CODE_SAMSUNG           0x0003
#define ZBEE_MFG_CODE_CIRRONET          0x1000
#define ZBEE_MFG_CODE_CHIPCON           0x1001
#define ZBEE_MFG_CODE_EMBER             0x1002
#define ZBEE_MFG_CODE_NTS               0x1003
#define ZBEE_MFG_CODE_FREESCALE         0x1004
#define ZBEE_MFG_CODE_IPCOM             0x1005
#define ZBEE_MFG_CODE_SAN_JUAN          0x1006
#define ZBEE_MFG_CODE_TUV               0x1007
#define ZBEE_MFG_CODE_COMPXS            0x1008
#define ZBEE_MFG_CODE_BM                0x1009
#define ZBEE_MFG_CODE_AWAREPOINT        0x100a
#define ZBEE_MFG_CODE_PHILIPS           0x100b
#define ZBEE_MFG_CODE_LUXOFT            0x100c
#define ZBEE_MFG_CODE_KORWIN            0x100d
#define ZBEE_MFG_CODE_1_RF              0x100e
#define ZBEE_MFG_CODE_STG               0x100f
#define ZBEE_MFG_CODE_TELEGESIS         0x1010
#define ZBEE_MFG_CODE_VISIONIC          0x1011
#define ZBEE_MFG_CODE_INSTA             0x1012
#define ZBEE_MFG_CODE_ATALUM            0x1013
#define ZBEE_MFG_CODE_ATMEL             0x1014
#define ZBEE_MFG_CODE_DEVELCO           0x1015
#define ZBEE_MFG_CODE_HONEYWELL         0x1016
#define ZBEE_MFG_CODE_RENESAS           0x1018
#define ZBEE_MFG_CODE_XANADU            0x1019
#define ZBEE_MFG_CODE_NEC               0x101a
#define ZBEE_MFG_CODE_YAMATAKE          0x101b
#define ZBEE_MFG_CODE_TENDRIL           0x101c
#define ZBEE_MFG_CODE_ASSA              0x101d
#define ZBEE_MFG_CODE_MAXSTREAM         0x101e
#define ZBEE_MFG_CODE_NEUROCOM          0x101f
#define ZBEE_MFG_CODE_III               0x1020
#define ZBEE_MFG_CODE_VANTAGE           0x1021
#define ZBEE_MFG_CODE_ICONTROL          0x1022
#define ZBEE_MFG_CODE_RAYMARINE         0x1023
#define ZBEE_MFG_CODE_LSR               0x1024
#define ZBEE_MFG_CODE_ONITY             0x1025
#define ZBEE_MFG_CODE_MONO              0x1026
#define ZBEE_MFG_CODE_RFT               0x1027
#define ZBEE_MFG_CODE_ITRON             0x1028
#define ZBEE_MFG_CODE_TRITECH           0x1029
#define ZBEE_MFG_CODE_EMBEDIT           0x102a
#define ZBEE_MFG_CODE_S3C               0x102b
#define ZBEE_MFG_CODE_SIEMENS           0x102c
#define ZBEE_MFG_CODE_MINDTECH          0x102d
#define ZBEE_MFG_CODE_LGE               0x102e
#define ZBEE_MFG_CODE_MITSUBISHI        0x102f
#define ZBEE_MFG_CODE_JOHNSON           0x1030
#define ZBEE_MFG_CODE_PRI               0x1031
#define ZBEE_MFG_CODE_KNICK             0x1032
#define ZBEE_MFG_CODE_VICONICS          0x1033
#define ZBEE_MFG_CODE_FLEXIPANEL        0x1034
#define ZBEE_MFG_CODE_TRANE             0x1036
#define ZBEE_MFG_CODE_JENNIC            0x1037
#define ZBEE_MFG_CODE_LIG               0x1038
#define ZBEE_MFG_CODE_ALERTME           0x1039
#define ZBEE_MFG_CODE_DAINTREE          0x103a
#define ZBEE_MFG_CODE_AIJI              0x103b
#define ZBEE_MFG_CODE_TEL_ITALIA        0x103c
#define ZBEE_MFG_CODE_MIKROKRETS        0x103d
#define ZBEE_MFG_CODE_OKI               0x103e
#define ZBEE_MFG_CODE_NEWPORT           0x103f
#define ZBEE_MFG_CODE_C4                0x1040
#define ZBEE_MFG_CODE_STM               0x1041
#define ZBEE_MFG_CODE_ASN               0x1042
#define ZBEE_MFG_CODE_DCSI              0x1043
#define ZBEE_MFG_CODE_FRANCE_TEL        0x1044
#define ZBEE_MFG_CODE_MUNET             0x1045
#define ZBEE_MFG_CODE_AUTANI            0x1046
#define ZBEE_MFG_CODE_COL_VNET          0x1047
#define ZBEE_MFG_CODE_AEROCOMM          0x1048
#define ZBEE_MFG_CODE_SI_LABS           0x1049
#define ZBEE_MFG_CODE_INNCOM            0x104a
#define ZBEE_MFG_CODE_CANNON            0x104b
#define ZBEE_MFG_CODE_SYNAPSE           0x104c
#define ZBEE_MFG_CODE_FPS               0x104d
#define ZBEE_MFG_CODE_CLS               0x104e
#define ZBEE_MFG_CODE_CRANE             0x104F
#define ZBEE_MFG_CODE_MOBILARM          0x1050
#define ZBEE_MFG_CODE_IMONITOR          0x1051
#define ZBEE_MFG_CODE_BARTECH           0x1052
#define ZBEE_MFG_CODE_MESHNETICS        0x1053
#define ZBEE_MFG_CODE_LS_IND            0x1054
#define ZBEE_MFG_CODE_CASON             0x1055
#define ZBEE_MFG_CODE_WLESS_GLUE        0x1056
#define ZBEE_MFG_CODE_ELSTER            0x1057
#define ZBEE_MFG_CODE_SMS_TEC           0x1058
#define ZBEE_MFG_CODE_ONSET             0x1059
#define ZBEE_MFG_CODE_RIGA              0x105a
#define ZBEE_MFG_CODE_ENERGATE          0x105b
#define ZBEE_MFG_CODE_CONMED            0x105c
#define ZBEE_MFG_CODE_POWERMAND         0x105d
#define ZBEE_MFG_CODE_SCHNEIDER         0x105e
#define ZBEE_MFG_CODE_EATON             0x105f
#define ZBEE_MFG_CODE_TELULAR           0x1060
#define ZBEE_MFG_CODE_DELPHI            0x1061
#define ZBEE_MFG_CODE_EPISENSOR         0x1062
#define ZBEE_MFG_CODE_LANDIS_GYR        0x1063
#define ZBEE_MFG_CODE_KABA              0x1064
#define ZBEE_MFG_CODE_SHURE             0x1065
#define ZBEE_MFG_CODE_COMVERGE          0x1066
#define ZBEE_MFG_CODE_HIDALGO           0x1069
#define ZBEE_MFG_CODE_AIR2APP           0x106a
#define ZBEE_MFG_CODE_AMX               0x106b
#define ZBEE_MFG_CODE_EDMI              0x106c
#define ZBEE_MFG_CODE_CYAN              0x106d
#define ZBEE_MFG_CODE_SYS_SPA           0x106e
#define ZBEE_MFG_CODE_TELIT             0x106f
#define ZBEE_MFG_CODE_KAGA              0x1070
#define ZBEE_MFG_CODE_4_NOKS            0x1071
#define ZBEE_MFG_CODE_PROFILE_SYS       0x1074
#define ZBEE_MFG_CODE_FREESTYLE         0x1076
#define ZBEE_MFG_CODE_REMOTE            0x1079
#define ZBEE_MFG_CODE_WAVECOM           0x107a
#define ZBEE_MFG_CODE_ENERGY_OPT        0x107b
#define ZBEE_MFG_CODE_GE                0x107c
#define ZBEE_MFG_CODE_MESHWORKS         0x1082
#define ZBEE_MFG_CODE_ELLIPS            0x1083
#define ZBEE_MFG_CODE_CEDO              0x1085
#define ZBEE_MFG_CODE_DIGI              0x1087
#define ZBEE_MFG_CODE_A_D               0x1094
#define ZBEE_MFG_CODE_CARRIER           0x1096
#define ZBEE_MFG_CODE_SYCHIP            0x1097
#define ZBEE_MFG_CODE_PASSIVESYS        0x1099
#define ZBEE_MFG_CODE_MMB               0x109a
#define ZBEE_MFG_CODE_HOME_AUTO         0x109b
#define ZBEE_MFG_CODE_SUNRISE           0x10a3
#define ZBEE_MFG_CODE_MEMTEC            0x10a4
#define ZBEE_MFG_CODE_BRITISH_GAS       0x10a7
#define ZBEE_MFG_CODE_SENTEC            0x10a8
#define ZBEE_MFG_CODE_NAVETAS           0x10a9
#define ZBEE_MFG_CODE_ENERNOC           0x10b2
#define ZBEE_MFG_CODE_ELTAV             0x10b3
#define ZBEE_MFG_CODE_XSTREAMHD         0x10b5
#define ZBEE_MFG_CODE_GREEN             0x10b7
#define ZBEE_MFG_CODE_OMRON             0x10bf
#define ZBEE_MFG_CODE_PEEL              0x10c2
#define ZBEE_MFG_CODE_NEC_TOKIN         0x10c5
#define ZBEE_MFG_CODE_G4S_JUSTICE       0x10c6
#define ZBEE_MFG_CODE_ELECTROLUX        0x10c8
#define ZBEE_MFG_CODE_MAINSTREAM        0x10cc
#define ZBEE_MFG_CODE_INDESIT_C         0x10cd
#define ZBEE_MFG_CODE_RADIOCRAFTS       0x10dd
#define ZBEE_MFG_CODE_HUAWEI_1          0x10e3
#define ZBEE_MFG_CODE_HUAWEI_2          0x10e4
#define ZBEE_MFG_CODE_BGLOBAL           0x10e6
#define ZBEE_MFG_CODE_ABB               0x10eb
#define ZBEE_MFG_CODE_GENUS             0x10ed
#define ZBEE_MFG_CODE_RELOC             0x1114
#define ZBEE_MFG_CIRRONET       "Cirronet"
#define ZBEE_MFG_CHIPCON        "Chipcon"
#define ZBEE_MFG_EMBER          "Ember"
#define ZBEE_MFG_NTS            "National Tech"
#define ZBEE_MFG_FREESCALE      "Freescale"
#define ZBEE_MFG_IPCOM          "IPCom"
#define ZBEE_MFG_SAN_JUAN       "San Juan Software"
#define ZBEE_MFG_TUV            "TUV"
#define ZBEE_MFG_COMPXS         "CompXs"
#define ZBEE_MFG_BM             "BM SpA"
#define ZBEE_MFG_AWAREPOINT     "AwarePoint"
#define ZBEE_MFG_PHILIPS        "Philips"
#define ZBEE_MFG_LUXOFT         "Luxoft"
#define ZBEE_MFG_KORWIN         "Korvin"
#define ZBEE_MFG_1_RF           "One RF"
#define ZBEE_MFG_STG            "Software Technology Group"
#define ZBEE_MFG_TELEGESIS      "Telegesis"
#define ZBEE_MFG_VISIONIC       "Visionic"
#define ZBEE_MFG_INSTA          "Insta"
#define ZBEE_MFG_ATALUM         "Atalum"
#define ZBEE_MFG_ATMEL          "Atmel"
#define ZBEE_MFG_DEVELCO        "Develco"
#define ZBEE_MFG_HONEYWELL      "Honeywell"
#define ZBEE_MFG_RENESAS        "Renesas"
#define ZBEE_MFG_XANADU         "Xanadu Wireless"
#define ZBEE_MFG_NEC            "NEC Engineering"
#define ZBEE_MFG_YAMATAKE       "Yamatake"
#define ZBEE_MFG_TENDRIL        "Tendril"
#define ZBEE_MFG_ASSA           "Assa Abloy"
#define ZBEE_MFG_MAXSTREAM      "Maxstream"
#define ZBEE_MFG_NEUROCOM       "Neurocom"
#define ZBEE_MFG_III            "Institute for Information Industry"
#define ZBEE_MFG_VANTAGE        "Vantage Controls"
#define ZBEE_MFG_ICONTROL       "iControl"
#define ZBEE_MFG_RAYMARINE      "Raymarine"
#define ZBEE_MFG_LSR            "LS Research"
#define ZBEE_MFG_ONITY          "Onity"
#define ZBEE_MFG_MONO           "Mono Products"
#define ZBEE_MFG_RFT            "RF Tech"
#define ZBEE_MFG_ITRON          "Itron"
#define ZBEE_MFG_TRITECH        "Tritech"
#define ZBEE_MFG_EMBEDIT        "Embedit"
#define ZBEE_MFG_S3C            "S3C"
#define ZBEE_MFG_SIEMENS        "Siemens"
#define ZBEE_MFG_MINDTECH       "Mindtech"
#define ZBEE_MFG_LGE            "LG Electronics"
#define ZBEE_MFG_MITSUBISHI     "Mitsubishi"
#define ZBEE_MFG_JOHNSON        "Johnson Controls"
#define ZBEE_MFG_PRI            "PRI"
#define ZBEE_MFG_KNICK          "Knick"
#define ZBEE_MFG_VICONICS       "Viconics"
#define ZBEE_MFG_FLEXIPANEL     "Flexipanel"
#define ZBEE_MFG_TRANE          "Trane"
#define ZBEE_MFG_JENNIC         "Jennic"
#define ZBEE_MFG_LIG            "Living Independently"
#define ZBEE_MFG_ALERTME        "AlertMe"
#define ZBEE_MFG_DAINTREE       "Daintree"
#define ZBEE_MFG_AIJI           "Aiji"
#define ZBEE_MFG_TEL_ITALIA     "Telecom Italia"
#define ZBEE_MFG_MIKROKRETS     "Mikrokrets"
#define ZBEE_MFG_OKI            "Oki Semi"
#define ZBEE_MFG_NEWPORT        "Newport Electronics"
#define ZBEE_MFG_C4             "Control4"
#define ZBEE_MFG_STM            "STMicro"
#define ZBEE_MFG_ASN            "Ad-Sol Nissin"
#define ZBEE_MFG_DCSI           "DCSI"
#define ZBEE_MFG_FRANCE_TEL     "France Telecom"
#define ZBEE_MFG_MUNET          "muNet"
#define ZBEE_MFG_AUTANI         "Autani"
#define ZBEE_MFG_COL_VNET       "Colorado vNet"
#define ZBEE_MFG_AEROCOMM       "Aerocomm"
#define ZBEE_MFG_SI_LABS        "Silicon Labs"
#define ZBEE_MFG_INNCOM         "Inncom"
#define ZBEE_MFG_CANNON         "Cannon"
#define ZBEE_MFG_SYNAPSE        "Synapse"
#define ZBEE_MFG_FPS            "Fisher Pierce/Sunrise"
#define ZBEE_MFG_CLS            "CentraLite"
#define ZBEE_MFG_CRANE          "Crane"
#define ZBEE_MFG_MOBILARM       "Mobilarm"
#define ZBEE_MFG_IMONITOR       "iMonitor"
#define ZBEE_MFG_BARTECH        "Bartech"
#define ZBEE_MFG_MESHNETICS     "Meshnetics"
#define ZBEE_MFG_LS_IND         "LS Industrial"
#define ZBEE_MFG_CASON          "Cason"
#define ZBEE_MFG_WLESS_GLUE     "Wireless Glue"
#define ZBEE_MFG_ELSTER         "Elster"
#define ZBEE_MFG_SMS_TEC        "SMS Tec"
#define ZBEE_MFG_ONSET          "Onset Computer"
#define ZBEE_MFG_RIGA           "Riga Development"
#define ZBEE_MFG_ENERGATE       "Energate"
#define ZBEE_MFG_CONMED         "ConMed Linvatec"
#define ZBEE_MFG_POWERMAND      "PowerMand"
#define ZBEE_MFG_SCHNEIDER      "Schneider Electric"
#define ZBEE_MFG_EATON          "Eaton"
#define ZBEE_MFG_TELULAR        "Telular"
#define ZBEE_MFG_DELPHI         "Delphi Medical"
#define ZBEE_MFG_EPISENSOR      "EpiSensor"
#define ZBEE_MFG_LANDIS_GYR     "Landis+Gyr"
#define ZBEE_MFG_KABA           "Kaba Group"
#define ZBEE_MFG_SHURE          "Shure"
#define ZBEE_MFG_COMVERGE       "Comverge"
#define ZBEE_MFG_HIDALGO        "Hidalgo"
#define ZBEE_MFG_AIR2APP        "Air2App"
#define ZBEE_MFG_AMX            "AMX"
#define ZBEE_MFG_EDMI           "EDMI"
#define ZBEE_MFG_CYAN           "Cyan Ltd."
#define ZBEE_MFG_SYS_SPA        "System SPA"
#define ZBEE_MFG_TELIT          "Telit"
#define ZBEE_MFG_KAGA           "Kaga Electronics"
#define ZBEE_MFG_4_NOKS         "4-noks s.r.l."
#define ZBEE_MFG_PROFILE_SYS    "Profile Systems LLC"
#define ZBEE_MFG_FREESTYLE      "Freestyle Energy Ltd"
#define ZBEE_MFG_REMOTE         "Remote Technology, Inc."
#define ZBEE_MFG_WAVECOM        "Wavecom S.A."
#define ZBEE_MFG_ENERGY_OPT     "Energy Optimizer Ltd"
#define ZBEE_MFG_GE             "General Electric"
#define ZBEE_MFG_MESHWORKS      "MeshWorks Wireless"
#define ZBEE_MFG_ELLIPS         "Ellips B.V."
#define ZBEE_MFG_CEDO           "CEDO"
#define ZBEE_MFG_DIGI           "Digi"
#define ZBEE_MFG_A_D            "A&D Co. Ltd."
#define ZBEE_MFG_CARRIER        "Carrier Electronics"
#define ZBEE_MFG_SYCHIP         "SyChip"
#define ZBEE_MFG_PASSIVESYS     "PassiveSystems"
#define ZBEE_MFG_G4S_JUSTICE    "G4S Justice Services"
#define ZBEE_MFG_MMB            "MMB Research"
#define ZBEE_MFG_HOME_AUTO      "Home Automation Inc."
#define ZBEE_MFG_SUNRISE        "Sunrise Technologies"
#define ZBEE_MFG_MEMTEC         "Memtec Corp"
#define ZBEE_MFG_BRITISH_GAS    "British Gas"
#define ZBEE_MFG_SENTEC         "Sentec Ltd."
#define ZBEE_MFG_NAVETAS        "Navetas"
#define ZBEE_MFG_ENERNOC        "EnerNOC"
#define ZBEE_MFG_ELTAV          "Eltav"
#define ZBEE_MFG_XSTREAMHD      "XStreamHD"
#define ZBEE_MFG_GREEN          "GreenTrapOnline"
#define ZBEE_MFG_OMRON          "Omron Corporation"
#define ZBEE_MFG_PEEL           "Peel Technologies"
#define ZBEE_MFG_NEC_TOKIN      "NEC TOKIN Corporation"
#define ZBEE_MFG_ELECTROLUX     "Electrolux Italia S.p.A"
#define ZBEE_MFG_SAMSUNG        "Samsung Electronics Co., Ltd."
#define ZBEE_MFG_MAINSTREAM     "Mainstream Engineering"
#define ZBEE_MFG_INDESIT_C      "Indesit Company"
#define ZBEE_MFG_RADIOCRAFTS    "Radiocrafts AS"
#define ZBEE_MFG_HUAWEI         "Huawei Technologies Co., Ltd."
#define ZBEE_MFG_BGLOBAL        "BGlobal Metering Ltd"
#define ZBEE_MFG_ABB            "ABB"
#define ZBEE_MFG_GENUS          "Genus Power Infrastructures Limited"
#define ZBEE_MFG_RELOC          "RELOC"
#define ZBEE_PROTOABBREV_NWK                "zbee_nwk"
#define ZBEE_PROTOABBREV_APS                "zbee_aps"
#define ZBEE_PROTOABBREV_APF                "zbee_apf"
#define ZBEE_PROTOABBREV_ZCL                "zbee_zcl"
#define ZBEE_PROTOABBREV_ZCL_APPLCTRL       "zbee_zcl_general.applctrl"
#define ZBEE_PROTOABBREV_ZCL_BASIC          "zbee_zcl_general.basic"
#define ZBEE_PROTOABBREV_ZCL_IDENTIFY       "zbee_zcl_general.identify"
#define ZBEE_PROTOABBREV_ZCL_APPLEVTALT     "zbee_zcl_ha.applevtalt"
#define ZBEE_PROTOABBREV_ZCL_APPLIDT        "zbee_zcl_ha.applident"
#define ZBEE_PROTOABBREV_ZCL_APPLSTATS      "zbee_zcl_ha.applstats"
#define ZBEE_PROTOABBREV_ZCL_METIDT         "zbee_zcl_ha.metidt"
#define ZBEE_PROTOABBREV_ZCL_ONOFF          "zbee_zcl_general.onoff"
#define ZBEE_PROTOABBREV_ZCL_PART           "zbee_zcl_general.part"
#define ZBEE_PROTOABBREV_ZCL_PWRPROF        "zbee_zcl_general.pwrprof"
#define ZBEE_PROTOABBREV_ZCL_ILLUMMEAS      "zbee_zcl_meas_sensing.illummeas"
#define ZBEE_PROTOABBREV_ZCL_PRESSMEAS      "zbee_zcl_meas_sensing.pressmeas"
#define ZBEE_PROTOABBREV_ZCL_RELHUMMEAS     "zbee_zcl_meas_sensing.relhummeas"
#define ZBEE_PROTOABBREV_ZCL_TEMPMEAS       "zbee_zcl_meas_sensing.tempmeas"
#define ZBEE_PROTOABBREV_ZCL_MSG            "zbee_zcl_se.msg"
 
 
 
 
//
#define GTPv0_PORT  3386
#define GTPv1C_PORT 2123    /* 3G Control PDU */
#define GTPv1U_PORT 2152    /* 3G T-PDU */
 
 
 
 

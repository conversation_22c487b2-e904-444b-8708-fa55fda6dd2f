#include <stdio.h>
#include <stdlib.h>
#include <iostream>
#include <fstream>
#include <vector>
#include <algorithm> 
#include <map>
#include <atomic>
#include <netinet/udp.h>
#include "CuapRule.h"
#include "Define_ProtocolID.h"
#include "Define_ProtocolType.h"

using namespace std;

//g++ -std=c++11 ./CuapRule.cpp -fPIC -shared -o CuapRule.so
//nm -D ./CuapRule.so

map<int, int> g_port_proto_map = {
    {UDP_PORT_AODV, PROTOCOL_AODV},
    {UDP_PORT_ARMAGETRONAD, PROTOCOL_ARMAGETRONAD},
    {UDP_PORT_MASTER, PROTOCOL_ARMAGETRONAD},
    {UDP_PORT_ADP, PROTOCOL_ARUBA},
    {UDP_PORT_PAPI, PROTOCOL_PAPI},
    {ASAP_UDP_PORT, PROTOCOL_ASAP},
    {ASTERIX_PORT, PROTOCOL_ASTERIX},
    {UDP_PORT_PIM_RP_DISC, PROTOCOL_AUTO},
    {AX4000_UDP_PORT, PROTOCOL_AX4000},
    {UDP_PORT_AYIYA, PROTOCOL_AYIYA},
    {UDP_PORT_BABEL, PROTOCOL_BABEL},
    {UDP_PORT_BABEL_OLD, PROTOCOL_BABEL},
    {UDP_PORT_BFD_1HOP_CONTROL, PROTOCOL_BFD},
    {UDP_PORT_BFD_MULTIHOP_CONTROL, PROTOCOL_BFD},
    {BJNP_PORT1, PROTOCOL_BJNP},
    {BJNP_PORT2, PROTOCOL_BJNP},
    {BJNP_PORT3, PROTOCOL_BJNP},
    {BJNP_PORT4, PROTOCOL_BJNP},
    {67, PROTOCOL_DHCP},
    {68, PROTOCOL_DHCP},
    {UDP_PORT_STUN, PROTOCOL_CLASSICSTUN},
    {COMPONENTSTATUSPROTOCOL_PORT, PROTOCOL_COMPONENTSTATUSPROTOCOL},
    {UDP_PORT_CPHA, PROTOCOL_CPHA},
    {UDP_PORT_CUPS, PROTOCOL_CUPS},
    {DAYTIME_PORT, PROTOCOL_DAYTIME},
    {DB_LSP_PORT, PROTOCOL_DB},
    {UDP_PORT_DDTP, PROTOCOL_DDTP},
    {UDP_PORT_DHCPV6_DOWNSTREAM, PROTOCOL_DHCPV6},
    {UDP_PORT_DHCPV6_UPSTREAM, PROTOCOL_DHCPV6},
    {UDP_PORT_DLSW, PROTOCOL_DLSW},
    {UDP_PORT_DNP, PROTOCOL_DNP3},
    {UDP_PORT_MDNS, PROTOCOL_DNS},
    {UDP_PORT_LLMNR, PROTOCOL_DNS},
    {UDP_PORT_IPDC_ESG_BOOTSTRAP, PROTOCOL_DVB},
    {ECHO_PORT, PROTOCOL_ECHO},
    {EGD_PORT, PROTOCOL_EGD},
    {ENIP_ENCuap_PORT, PROTOCOL_ENIP},
    {ENIP_IO_PORT, PROTOCOL_ENIP},
    {ENRP_UDP_PORT, PROTOCOL_ENRP},
    {UDP_PORT_EPL, PROTOCOL_EPL},
    {UDP_PORT_FF_ANNUNC, PROTOCOL_FF},
    {UDP_PORT_FF_FMS, PROTOCOL_FF},
    {UDP_PORT_FF_SM, PROTOCOL_FF},
    {UDP_PORT_FF_LR_PORT, PROTOCOL_FF},
    {GSMTAP_UDP_PORT, PROTOCOL_GSMTAP},
    {GVCP_PORT, PROTOCOL_GVCP},
    {UDP_PORT_RAS1, PROTOCOL_H225},
    {UDP_PORT_RAS2, PROTOCOL_H225},
    {HARTIP_PORT, PROTOCOL_HARTIP},
    {UDP_PORT_HSRP, PROTOCOL_HSRP},
    {UDP_PORT_HSRP2_V6, PROTOCOL_HSRP},
    {UDP_PORT_SSDP, PROTOCOL_HTTP},
    {UDP_PORT_IAPP, PROTOCOL_IAPP},
    {IAX2_PORT, PROTOCOL_IAX2},
    {UDP_PORT_ICP, PROTOCOL_ICP},
    {UDP_PORT_ICQ, PROTOCOL_ICQ},
    {IPVS_SYNCD_PORT, PROTOCOL_IPVS},
    {UDP_PORT_IPX, PROTOCOL_IPX},
    {UDP_PORT_ISAKMP, PROTOCOL_ISAKMP},
    {ISNS_UDP_PORT, PROTOCOL_ISNS},
    {KDP_PORT, PROTOCOL_KDP},
    {UDP_PORT_KERBEROS, PROTOCOL_KERBEROS},
    {UDP_PORT_KRB4, PROTOCOL_KRB4},
    {UDP_PORT_KINGFISHER, PROTOCOL_KINGFISHER},
    {UDP_PORT_KINGFISHER_OLD, PROTOCOL_KINGFISHER},
    {KINK_PORT, PROTOCOL_KINK},
    {UDP_PORT_KPASSWD, PROTOCOL_KPASSWD},
    {UDP_PORT_L2TP, PROTOCOL_L2TP},
    {UDP_PORT_LAPLINK, PROTOCOL_LAPLINK},
    {UDP_PORT_CLDAP, PROTOCOL_LDAP},
    {LISP_DATA_PORT, PROTOCOL_LISP},
    {LISP_CONTROL_PORT, PROTOCOL_LISP},
    {UDP_PORT_LLC1, PROTOCOL_LLC},
    {UDP_PORT_LLC2, PROTOCOL_LLC},
    {UDP_PORT_LLC3, PROTOCOL_LLC},
    {UDP_PORT_LLC4, PROTOCOL_LLC},
    {UDP_PORT_LLC5, PROTOCOL_LLC},
    {MIH_PORT, PROTOCOL_MIH},
    {PORT_MINT_CONTROL_TUNNEL, PROTOCOL_MINT},
    {PORT_MINT_DATA_TUNNEL, PROTOCOL_MINT},
    {UDP_PORT_MIP, PROTOCOL_MIP},
    {UDP_PORT_PMIP6_CNTL, PROTOCOL_MIP6},
    {PORT_MNDP, PROTOCOL_MNDP},
    {MSMMS_PORT, PROTOCOL_MSMMS},
    {UDP_PORT_MSPROXY, PROTOCOL_MSPROXY},
    {PCP_STATUS_PORT, PROTOCOL_NAT},
    {PCP_PORT, PROTOCOL_NAT},
    {UDP_PORT_NBNS, PROTOCOL_NBNS},
    {UDP_PORT_NBDGM, PROTOCOL_NBDGM},
    {UDP_PORT_NCP, PROTOCOL_NCP},
    {UDP_PORT_TPCP, PROTOCOL_TPCP},
    {UDP_PORT_NTP, PROTOCOL_NTP},
    {UDP_PORT_OICQ, PROTOCOL_OICQ},
    {UDP_PORT_OLSR, PROTOCOL_OLSR},
    {OMRON_FINS_UDP_PORT, PROTOCOL_OMRON},
    {PKTC_PORT, PROTOCOL_PKTC},
    {PKTC_MTAFQDN_PORT, PROTOCOL_PKTC},
    {PNRP_PORT, PROTOCOL_PNRP},
    {EVENT_PORT_PTP, PROTOCOL_PTP},
    {GENERAL_PORT_PTP, PROTOCOL_PTP},
    {UDP_PORT_RADIUS, PROTOCOL_RADIUS},
    {UDP_PORT_RADIUS_NEW, PROTOCOL_RADIUS},
    {UDP_PORT_RADACCT, PROTOCOL_RADIUS},
    {UDP_PORT_RADACCT_NEW, PROTOCOL_RADIUS},
    {UDP_PORT_DAE_OLD, PROTOCOL_RADIUS},
    {UDP_PORT_DAE, PROTOCOL_RADIUS},
    {UDP_PORT_RELOAD, PROTOCOL_RELOAD},
    {UDP_PORT_RIP, PROTOCOL_RIP},
    {UDP_PORT_RIPNG, PROTOCOL_RIPNG},
    {UDP_PORT_RMCP, PROTOCOL_RMCP},
    {UDP_PORT_RMCP_SECURE, PROTOCOL_RSP},
    {UDP_PORT_RSIP, PROTOCOL_RSIP},
    {UDP_PORT_PRSVP, PROTOCOL_RSVP},
    {UDP_PORT_RX_AFS_BACKUPS, PROTOCOL_RX},
    {UDP_PORT_SAP, PROTOCOL_SAP},
    {UDP_TUNNELING_PORT, PROTOCOL_SCTP},
    {UDP_PORT_SEBEK, PROTOCOL_SEBEK},
    {UDP_PORT_SIP, PROTOCOL_SIP},
    {UDP_PORT_SLIMP3_V1, PROTOCOL_SLIMP3},
    {UDP_PORT_SLIMP3_V2, PROTOCOL_SLIMP3},
    {UDP_PORT_SNMP, PROTOCOL_SNMP},
    {UDP_PORT_SNMP_TRAP, PROTOCOL_SNMP},
    {UDP_PORT_SNMP_PATROL, PROTOCOL_SNMP},
    {UDP_PORT_SRVLOC, PROTOCOL_SRVLOC},
    {UDP_PORT_STUN, PROTOCOL_STUN},
    {UDP_PORT_SYSLOG, PROTOCOL_SYSLOG},
    {UDP_PORT_TACACS, PROTOCOL_TACACS},
    {PORT_TAPA, PROTOCOL_TAPA},
    {TS2_PORT, PROTOCOL_TS2},
    {UDP_PORT_TEREDO, PROTOCOL_TEREDO},
    {TIME_PORT, PROTOCOL_TIME},
    {UDP_PORT_TIMED, PROTOCOL_TSP},
    {UDP_PORT_TZSP, PROTOCOL_TZSP},
    {UDP_PORT_VINES, PROTOCOL_VINES},
    {PORT_WASSP_DISCOVER, PROTOCOL_WASSP},
    {PORT_WASSP_TUNNEL, PROTOCOL_WASSP},
    {PORT_WASSP_PEER, PROTOCOL_WASSP},
    {UDP_PORT_WCCP, PROTOCOL_WCCP},
    {UDP_PORT_WHO, PROTOCOL_WHO},
    {WLCCP_UDP_PORT, PROTOCOL_WLCCP},
    {UDP_PORT_WSP, PROTOCOL_WSP},
    {UDP_PORT_WSP_PUSH, PROTOCOL_WSP},
    {UDP_PORT_WTLS_WSP, PROTOCOL_WTLS},
    {UDP_PORT_WTLS_WTP_WSP, PROTOCOL_WTLS},
    {UDP_PORT_WTLS_WSP_PUSH, PROTOCOL_WTLS},
    {UDP_PORT_WTP_WSP, PROTOCOL_WTP},
    {UDP_PORT_XDMCP, PROTOCOL_XDMCP},
    {UDP_PORT_XYPLEX, PROTOCOL_XYPLEX},
    {UDP_PORT_ACTRACE, PROTOCOL_ACTRACE},
    {55555, PROTOCOL_AMR},
    {1958, PROTOCOL_BRP},
    {0xBAC0, PROTOCOL_BVLC},
    {UDP_PORT_CuapWAP_CONTROL, PROTOCOL_CuapWAP},
    {UDP_PORT_CuapWAP_DATA, PROTOCOL_CuapWAP},
    {1628, PROTOCOL_CNIP},
    {1629, PROTOCOL_CNIP},
    {DEFAULT_COAP_PORT, PROTOCOL_COAP},
    {UDP_PORT_COLLECTD, PROTOCOL_COLLECTD},
    {UDP_PORT_ACTRACE, PROTOCOL_BUNDLE},
    {4665, PROTOCOL_EDONKEY},
    {4672, PROTOCOL_EDONKEY},
    {UDP_PORT_ENTTEC, PROTOCOL_ENTTEC},
    {6060, PROTOCOL_ESIO},
    {3222, PROTOCOL_GLBP},
    {10500, PROTOCOL_HIP},
    {4500, PROTOCOL_UDPENCuap},
    {UDP_PORT_LDP, PROTOCOL_LDP},
    {UDP_PORT_LDSS, PROTOCOL_LDSS},
    {UDP_PORT_LMP_DEFAULT, PROTOCOL_LMP},
    {12220, PROTOCOL_LWAPP},
    {12222, PROTOCOL_LWAPP},
    {12223, PROTOCOL_LWAPP},
    {41170, PROTOCOL_MANOLITO},
    {PORT_MEGACO_TXT, PROTOCOL_MEGACO},
    {UDP_PORT_MGCP_GATEWAY, PROTOCOL_MGCP},
    {UDP_PORT_MGCP_CALLAGENT, PROTOCOL_MGCP},
    {PORT_MIKEY, PROTOCOL_MIKEY},
    {UDP_PORT_MPLS_ECHO, PROTOCOL_MPLS},
    {UDP_PORT_PCLI, PROTOCOL_PCLI},
    {5000, PROTOCOL_PEEKREMOTE},
    {PORT_MASTER, PROTOCOL_QUAKE2},
    {PORT_MASTER, PROTOCOL_QUAKEWORLD},
    {6970, PROTOCOL_RDT},
    {111, PROTOCOL_RPC},
    {22222, PROTOCOL_RTPPROXY},
    {3452, PROTOCOL_SABP},
    {5050, PROTOCOL_SBUS},
    {5555, PROTOCOL_SIGCOMP},
    {6666, PROTOCOL_SIGCOMP},
    {UDP_PORT_SML, PROTOCOL_SML},
    {7074, PROTOCOL_TETRA},
    {2190, PROTOCOL_TIVOCONNECT},
    {UDP_PORT_TPNCP_TRUNKPACK, PROTOCOL_TPNCP},
};

#define PRINTBUFLEN 65536

typedef struct CuapSession
{
    unsigned long long ConnectKeyID;
    int app_proto; //会话关联的app_proto
    int Count;
}CuapSession;

typedef struct CuapRuleHandle
{
    int RuleID;
    int MaxCon;
    CuapSession *pSession[64];
    char *pBuf[64];
}CuapRuleHandle;

bool isKeyInMap(const map<int, int>& myMap, int key) {
    return myMap.find(key) != myMap.end();
}

void* Init(int IN_CulRuleID,int IN_MaxConnectNum,void *IN_pParam)
{
    STR_INIT_PARAM *pParam = (STR_INIT_PARAM *)IN_pParam;
    
    cout <<"Rule: " << IN_CulRuleID << "; ----------Init----------" << endl;

    if(DYNAMIC_LIB_VER != pParam->Ver)
    {
        cout << "CuapRule Init Fail, Ver Not Match :" << pParam->Ver << endl;
        return NULL;
    }
    CuapRuleHandle *p_handle = new CuapRuleHandle;
    memset(p_handle, 0, sizeof(CuapRuleHandle));
    
    p_handle->RuleID = IN_CulRuleID;
    p_handle->MaxCon = IN_MaxConnectNum;

    ifstream fin(string(pParam->ConfigDir)+"/CuapRuleConfig.txt", ios::binary | ios::ate);
    if(fin.is_open())
    {
        auto size = fin.tellg();
        std::string str(size, '\0'); // construct string to stream size
        fin.seekg(0);
        fin.read(&str[0], size);
        cout << str << endl;
        cout << "CuapRule Read Config Success!" << endl;
    }
    else
    {
        cout << "CuapRule Read Config Fail!" << endl;
    }

    return (void*)p_handle;
}
void Quit(void* hHandle)
{
    CuapRuleHandle *p_handle = (CuapRuleHandle *)hHandle;
    printf("Rule: %d; ----------Quit----------\n",p_handle->RuleID);
    for(int i = 0; i < 64; i ++)
    {
        if(p_handle->pSession[i])
        {
            delete [](p_handle->pSession[i]);
            p_handle->pSession[i] = NULL;
        }
        if(p_handle->pBuf[i])
        {
            delete [](p_handle->pBuf[i]);
            p_handle->pBuf[i] = NULL;
        }
    }
    delete p_handle;
}

int ThreadBegin(void* hHandle,int ThreadID)
{
    CuapRuleHandle *p_handle = (CuapRuleHandle *)hHandle;
    printf("Rule: %d; ----------ThreadBegin:%d----------\n",p_handle->RuleID, ThreadID);
    p_handle->pSession[ThreadID-1] = new CuapSession[p_handle->MaxCon];
    memset(p_handle->pSession[ThreadID-1], 0, sizeof(CuapSession) * p_handle->MaxCon);
    p_handle->pBuf[ThreadID-1] = new char[PRINTBUFLEN];
    memset(p_handle->pBuf[ThreadID-1], 0, sizeof(char) * PRINTBUFLEN);
    return 0;
}
int ThreadEnd(void* hHandle,int ThreadID)
{
    CuapRuleHandle *p_handle = (CuapRuleHandle *)hHandle;
    printf("Rule: %d; ----------ThreadEnd:%d----------\n",p_handle->RuleID, ThreadID);
    if(p_handle->pSession[ThreadID-1])
    {
        delete [](p_handle->pSession[ThreadID-1]);
        p_handle->pSession[ThreadID-1] = NULL;
    }
    if(p_handle->pBuf[ThreadID-1])
    {
        delete [](p_handle->pBuf[ThreadID-1]);
        p_handle->pBuf[ThreadID-1] = NULL;
    }
    return 0;
}

//���� 0 ƥ��ʧ�ܣ� 1 ƥ��ɹ�
int JudgeV2(void* hHandle,unsigned int  ThreadID,unsigned int IN_APPID,DWORD IN_ParamType,void*IN_Param)
{
    CuapRuleHandle *p_handle = (CuapRuleHandle *)hHandle;
    STR_PARAM *param = (STR_PARAM*)IN_Param;
    
    printf("Rule: %d; ----------JudgeV2:%u,%d,%u,%u,%p,%p----------\n",p_handle->RuleID, param->SessionVer, ThreadID, IN_APPID, IN_ParamType, param->pPacket, param->pSession);

    STR_PACKETINFOR_MUDULE_CONNECT_V2 *pPacketInfor = param->pPacket;
    STR_CONNECTINFORV4_BASIC *pSession = param->pSession;
    
    if(pSession)
    {
        printf("Rule: %d; ----------JudgeV2 session:%d,%u,%llu,%u----------\n", p_handle->RuleID, pSession->ProtoSign, pSession->ConnectID, pSession->ConnectKeyID, pSession->Server);
        if(pSession->ConnectKeyID != p_handle->pSession[ThreadID-1][pSession->ConnectID].ConnectKeyID)
        {
            memset(&p_handle->pSession[ThreadID-1][pSession->ConnectID], 0, sizeof(CuapSession));
            p_handle->pSession[ThreadID-1][pSession->ConnectID].ConnectKeyID = pSession->ConnectKeyID;
            printf("Rule: %d; ----------JudgeV2 new session:%u,%u,%llu----------\n", p_handle->RuleID, ThreadID, pSession->ConnectID, pSession->ConnectKeyID);

        }
    }
    
    if(pPacketInfor)
    {
        printf("Rule: %d; ----------JudgeV2 pkt:%d,%d----------\n", p_handle->RuleID, pPacketInfor->ProID, pPacketInfor->Directory);
        ///////////////////////////////��ȡĩ�㸺�ص�ͷָ��
        STR_PROTOCOLSTACK* pStack=&pPacketInfor->Stack;
        STR_CONNECT *pConnect=&pPacketInfor->Connect;
        uint16_t src_port = ntohs(pConnect->ConnectV4.Port[0]);
        uint16_t dst_port = ntohs(pConnect->ConnectV4.Port[1]);
        printf("src_port: %d, dst_port: %d\n", src_port, dst_port);

        for (int i = 0; i < pStack->ProtocolNum; i++) {
            if(pStack->pProtocol[i].Protocol == PROTOCOL_UDP){
                if(i + 1 < pStack->ProtocolNum){
                    if(pStack->pProtocol[i+1].Protocol == UNKONWPROTOCOL_UDPPAYLOAD){
                        if(isKeyInMap(g_port_proto_map, src_port)){
                            pStack->pProtocol[i+1].Protocol = g_port_proto_map[src_port];
                            return 1;
                        }
                        if(isKeyInMap(g_port_proto_map, dst_port)){
                            pStack->pProtocol[i+1].Protocol = g_port_proto_map[dst_port];
                            return 1;
                        }
                    }
                }
            }
        }
        return 0;
    }
        ///////////////////////////////��ȡĩ�㸺�ص�ͷָ��--End
    return 0;
}



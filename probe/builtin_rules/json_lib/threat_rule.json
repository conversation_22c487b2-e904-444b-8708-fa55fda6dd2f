{"APPID":24504,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":795,"Keyword":"Content-Type:multipart/form-data","IsCaseSensive":1},{"ProID":795,"Keyword":"Content-Type: multipart/form-data","IsCaseSensive":1}],"Level":1,"Name":"upload_file","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":3239,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":263,"Keyword":" \u003cGetUpdatedFormDigestInformation xmlns=\"http://schemas.microsoft.com/sharepoint/soap/\" /\u003e","IsCaseSensive":1},{"ProID":10637,"Keyword":" \u003cGetUpdatedFormDigestInformation xmlns=\"http://schemas.microsoft.com/sharepoint/soap/\" /\u003e","IsCaseSensive":1},{"ProID":10637,"Keyword":"SOAPAction: http://schemas.microsoft.com/sharepoint/soap/GetUpdatedFormDigestInformation","IsCaseSensive":1},{"ProID":263,"Keyword":"SOAPAction: http://schemas.microsoft.com/sharepoint/soap/GetUpdatedFormDigestInformation","IsCaseSensive":1}],"Level":1,"Name":"sharepoint","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":3237,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":10638,"Keyword":"liveupdate.symantecliveupdate.com","IsCaseSensive":1},{"ProID":562,"Keyword":"liveupdate.symantecliveupdate.com","IsCaseSensive":1},{"ProID":10637,"Keyword":"Host: liveupdate.symantecliveupdate.com","IsCaseSensive":1},{"ProID":263,"Keyword":"Host: liveupdate.symantecliveupdate.com","IsCaseSensive":1}],"Level":1,"Name":"symantec_update","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":3236,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":10637,"Keyword":"Basic realm=\"VES ","IsCaseSensive":1}],"Level":1,"Name":"ZyXEL_VES_ADSL","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":3235,"BytePs":0,"DetailRespond":null,"Level":1,"Name":"ZyXEL_OMNI_ADSL","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Regex_Rule":[{"ProID":10637,"Regex":"Www-Authenticate: Digest realm=\"ZyXEL \\w* Omni","Property":1}],"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":3241,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":10034,"Keyword":"Corecess DSLAM","IsCaseSensive":1}],"Level":1,"Name":"Corecess_DSLAM","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":3234,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":10637,"Keyword":"content=\"sharepoint team","IsCaseSensive":1},{"ProID":10637,"Keyword":"content=\"microsoft sharepoint","IsCaseSensive":1},{"ProID":10637,"Keyword":"id=\"msowebpartpage_postbacksource","IsCaseSensive":1},{"ProID":10637,"Keyword":"sprequestduration","IsCaseSensive":1},{"ProID":10637,"Keyword":"sharepointerror","IsCaseSensive":1},{"ProID":10637,"Keyword":"x-sharepointhealthscore","IsCaseSensive":1},{"ProID":10637,"Keyword":"Microsoftsharepointteamservices","IsCaseSensive":1}],"Level":1,"Name":"SharePointApplicationServers","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":3094,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":10637,"Keyword":"/owa/auth.owa","IsCaseSensive":0}],"Level":1,"Name":"exchange","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":3233,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":10636,"Keyword":"Microsoft Exchange 2013","IsCaseSensive":0},{"ProID":10634,"Keyword":"Microsoft Exchange 2013","IsCaseSensive":0}],"Level":1,"Name":"exchange_2013","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":3232,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":10066,"Keyword":"Router Cisco 2911","IsCaseSensive":0},{"ProID":10066,"Keyword":"Cisco2911","IsCaseSensive":1},{"ProID":10629,"Keyword":"Router Cisco 2911","IsCaseSensive":1},{"ProID":10629,"Keyword":"Cisco2911","IsCaseSensive":1}],"Level":1,"Name":"cisco2911","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":3228,"BytePs":0,"DetailRespond":null,"Level":1,"Name":"Polycom_RMX","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Regex_Rule":[{"ProID":10637,"Regex":"\u003ctitle\u003ePolycom RMX \\d\\d\\d\\d\u003c/title\u003e","Property":1}],"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":3229,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":795,"Keyword":"Content-Type: application/octet-stream","IsCaseSensive":1}],"Level":10,"Name":"octet_stream_new","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":26042,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":10637,"Keyword":"NX-ANTI-CSRF-TOKEN","IsCaseSensive":1},{"ProID":263,"Keyword":"NX-ANTI-CSRF-TOKEN","IsCaseSensive":1},{"ProID":10637,"Keyword":"Nexus Repository Manager ","IsCaseSensive":1},{"ProID":263,"Keyword":"Nexus Repository Manager ","IsCaseSensive":1}],"Level":10,"Name":"Nexus","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":26051,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":263,"Keyword":"easSessionId ","IsCaseSensive":1},{"ProID":10637,"Keyword":"easSessionId","IsCaseSensive":1}],"Level":10,"Name":"kingdeeEas","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":26049,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":263,"Keyword":"Powered by Discuz!","IsCaseSensive":1},{"ProID":10637,"Keyword":"Powered by Discuz!","IsCaseSensive":1}],"Level":10,"Name":"Discuz","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":26045,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":10637,"Keyword":"CNOAOASESSID","IsCaseSensive":1},{"ProID":263,"Keyword":"CNOAOASESSID","IsCaseSensive":1},{"ProID":10637,"Keyword":"Powered by 协众OA","IsCaseSensive":1},{"ProID":263,"Keyword":"Powered by 协众OA","IsCaseSensive":1}],"Level":10,"Name":"xiezhong_OA","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":26044,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":263,"Keyword":"zentaosid","IsCaseSensive":1},{"ProID":10637,"Keyword":"zentaosid","IsCaseSensive":1},{"ProID":263,"Keyword":"/theme/default/images/main/zt-logo.png ","IsCaseSensive":1},{"ProID":10637,"Keyword":"/theme/default/images/main/zt-logo.png ","IsCaseSensive":1}],"Level":10,"Name":"zentao_CMS","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":26079,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":263,"Keyword":"Zabbix SIA","IsCaseSensive":1},{"ProID":10637,"Keyword":"Zabbix SIA","IsCaseSensive":1},{"ProID":10637,"Keyword":"/images/general/zabbix.ico","IsCaseSensive":1},{"ProID":263,"Keyword":"/images/general/zabbix.ico","IsCaseSensive":1},{"ProID":10637,"Keyword":"zbx_sessionid","IsCaseSensive":1},{"ProID":263,"Keyword":"zbx_sessionid","IsCaseSensive":1}],"Level":10,"Name":" zabbix","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":24022,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":263,"Keyword":"/://?/collector/licensing/upload","IsCaseSensive":1},{"ProID":10637,"Keyword":"/://?/collector/licensing/upload","IsCaseSensive":1}],"Level":60,"Name":"Citrix_SD_WAN_Center_RCE","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":23503,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":10165,"Keyword":"\\x67\\x65\\x6e\\x65\\x72\\x61\\x6c\\x5f","IsCaseSensive":0}],"Level":60,"Name":"general_log","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Regex_Rule":[{"ProID":10165,"Regex":"set global general_log_file='.*\\.(php|jsp|aspx|jspx|ashx|asp|asa|cer)'","Property":1}],"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":24025,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":263,"Keyword":"X-OWA-UrlPostData: {\"request\":{\"DocumentUrl\":\"\",\"EndPointUrl\":","IsCaseSensive":1},{"ProID":10637,"Keyword":"X-OWA-UrlPostData: {\"request\":{\"DocumentUrl\":\"\",\"EndPointUrl\":","IsCaseSensive":1}],"Level":70,"Name":"Exchange_XXE","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":24028,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":10637,"Keyword":"/web.config.i18n.ashx?l=en-US\u0026v=","IsCaseSensive":1},{"ProID":263,"Keyword":"/web.config.i18n.ashx?l=en-US\u0026v=","IsCaseSensive":1}],"Level":60,"Name":"SolarWinds_Orion_auth","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":24021,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":10637,"Keyword":"_pageLabel=JNDIBindingPageGeneral\u0026_nfpb=true\u0026JNDIBindingPortlethandle=com.bea.console.handles.JndiBindingHandle","IsCaseSensive":1},{"ProID":263,"Keyword":"_pageLabel=JNDIBindingPageGeneral\u0026_nfpb=true\u0026JNDIBindingPortlethandle=com.bea.console.handles.JndiBindingHandle","IsCaseSensive":1}],"Level":80,"Name":"Weblogic_JNDI_INJECT","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":26262,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":10637,"Keyword":"rememberMe=1","IsCaseSensive":1}],"Level":40,"Name":"shiro_tools_attack","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":24027,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":263,"Keyword":"script::Runtime r = Runtime.getRuntime(); r.exec","IsCaseSensive":1},{"ProID":10637,"Keyword":"script::Runtime r = Runtime.getRuntime(); r.exec","IsCaseSensive":1},{"ProID":10637,"Keyword":"runtimeclass = #this.getClass().forName(\\\"java.lang.Runtime\\\")).(#getruntimemethod = #runtimeclass.getDeclaredMethods().{^ #this.name.equals(\\\"getRuntime\\\")}[0]).(#rtobj = #getruntimemethod.invoke(null,null)).(#execmethod = #runtimeclass.getDeclaredMethods().{? #this.name.equals(\\\"exec\\\")}.{? #this.getParameters()[0].getType().getName().equals(\\\"java.lang.String\\\")}.{? #this.getParameters().length \u003c 2}[0])","IsCaseSensive":1},{"ProID":263,"Keyword":"runtimeclass = #this.getClass().forName(\\\"java.lang.Runtime\\\")).(#getruntimemethod = #runtimeclass.getDeclaredMethods().{^ #this.name.equals(\\\"getRuntime\\\")}[0]).(#rtobj = #getruntimemethod.invoke(null,null)).(#execmethod = #runtimeclass.getDeclaredMethods().{? #this.name.equals(\\\"exec\\\")}.{? #this.getParameters()[0].getType().getName().equals(\\\"java.lang.String\\\")}.{? #this.getParameters().length \u003c 2}[0])","IsCaseSensive":1},{"ProID":10637,"Keyword":"{ \"filters\": [ { \"id\": \"boom\", \"filters\": [ { \"condition\": { \"parameterValues\": { \"\": \"script::Runtime r = Runtime.getRuntime(); \" }, \"type\": \"profilePropertyCondition\" } } ] } ], \"sessionId\": \"boom\" }","IsCaseSensive":1},{"ProID":263,"Keyword":"{     \"filters\": [         {             \"id\": \"boom\",             \"filters\": [                 {                     \"condition\": {                          \"parameterValues\": {                             \"\": \"script::Runtime r = Runtime.getRuntime(); \"                         },                         \"type\": \"profilePropertyCondition\"                     }                 }             ]         }     ],     \"sessionId\": \"boom\" }","IsCaseSensive":1}],"Level":80,"Name":"Apache_Uniomi_RCE","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":24023,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":263,"Keyword":"/jsp/help-sb-download.jsp?sbFileName=../","IsCaseSensive":1},{"ProID":10637,"Keyword":"/jsp/help-sb-download.jsp?sbFileName=../","IsCaseSensive":1}],"Level":60,"Name":"XenMobile_fileread","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":26260,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":10637,"Keyword":"=deleteMe","IsCaseSensive":0}],"Level":10,"Name":"shiro_deleteMe","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":24021,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":263,"Keyword":"/console/css/%252e.%252fconsole.portal","IsCaseSensive":1},{"ProID":10637,"Keyword":"/console/css/%252e.%252fconsole.portal","IsCaseSensive":1},{"ProID":10637,"Keyword":"/console/css/%%32e.%%32fconsole.portal","IsCaseSensive":1},{"ProID":263,"Keyword":"/console/css/%%32e.%%32fconsole.portal","IsCaseSensive":1},{"ProID":10637,"Keyword":"/console/css/%%32e.%%32fconsole.portal","IsCaseSensive":1},{"ProID":263,"Keyword":"/console/images/%2e.%2fconsole.portal","IsCaseSensive":1},{"ProID":10637,"Keyword":"/console/images/%2e.%2fconsole.portal","IsCaseSensive":1},{"ProID":263,"Keyword":"/console/css/%2e.%2fconsole.portal","IsCaseSensive":1},{"ProID":10637,"Keyword":"/console/css/%2e.%2fconsole.portal","IsCaseSensive":1}],"Level":80,"Name":"Weblogic_console_CVE_2020_14750","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":26261,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":10637,"Keyword":"rememberMe=","IsCaseSensive":0}],"Level":10,"Name":"shiro_rememberMe","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":26048,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":10637,"Keyword":"\u003cTITLE\u003eError 404--Not Found\u003c/TITLE\u003e ","IsCaseSensive":1},{"ProID":10637,"Keyword":"Oracle WebLogic Server","IsCaseSensive":0},{"ProID":263,"Keyword":"WebLogic ","IsCaseSensive":1}],"Level":10,"Name":"WebLogic ","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":26047,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":10637,"Keyword":"ecology","IsCaseSensive":1},{"ProID":263,"Keyword":"ecology","IsCaseSensive":1},{"ProID":263,"Keyword":"ecology_JSessionid","IsCaseSensive":1},{"ProID":10637,"Keyword":"ecology_JSessionid","IsCaseSensive":1}],"Level":10,"Name":"weaver_ecology","PbDrop":0,"PcapDrop":0,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":2,"Type":100}
{"APPID":24013,"BytePs":0,"DetailRespond":null,"Key_Rule":[{"ProID":10637,"Keyword":"dns%3A%2f%2f","IsCaseSensive":1},{"ProID":10637,"Keyword":"rmi%3A%2f%2f","IsCaseSensive":1},{"ProID":10637,"Keyword":"ldap%3A%2f%2f","IsCaseSensive":1},{"ProID":10637,"Keyword":"jndi%3A","IsCaseSensive":1},{"ProID":10637,"Keyword":"dns://","IsCaseSensive":1}
{"APPID": 24231, "BytePs": 0, "Key_Rule": [{"IsCaseSensive": 0, "Keyword": "FinalShell", "ProID": 10061}], "Level": 45, "PbDrop": 0, "PcapDrop": 0, "ReNewApp": 1, "Respond": 2, "Reverse": "ReverseInfor", "SaveBytes": -1, "Trance": 1, "Type": 100, "Name": "finalshell_ssh"}
{"APPID": 24232, "BytePs": 0, "Key_Rule": [{"IsCaseSensive": 0, "Keyword": "MoTTY", "ProID": 10061}], "Level": 45, "PbDrop": 0, "PcapDrop": 0, "ReNewApp": 1, "Respond": 2, "Reverse": "ReverseInfor", "SaveBytes": -1, "Trance": 1, "Type": 100, "Name": "mobaxterm_ssh"}
{"APPID": 24233, "BytePs": 0, "Key_Rule": [{"IsCaseSensive": 0, "Keyword": "PuTTY", "ProID": 10061}], "Level": 45, "PbDrop": 0, "PcapDrop": 0, "ReNewApp": 1, "Respond": 2, "Reverse": "ReverseInfor", "SaveBytes": -1, "Trance": 1, "Type": 100, "Name": "putty_ssh"}
{"APPID": 24234, "BytePs": 0, "Key_Rule": [{"IsCaseSensive": 0, "Keyword": "SecureCRT", "ProID": 10061}], "Level": 45, "PbDrop": 0, "PcapDrop": 0, "ReNewApp": 1, "Respond": 2, "Reverse": "ReverseInfor", "SaveBytes": -1, "Trance": 1, "Type": 100, "Name": "securecrt_ssh"}
{"APPID": 24235, "BytePs": 0, "Key_Rule": [{"IsCaseSensive": 0, "Keyword": "NetSarang", "ProID": 10061}], "Level": 45, "PbDrop": 0, "PcapDrop": 0, "ReNewApp": 1, "Respond": 2, "Reverse": "ReverseInfor", "SaveBytes": -1, "Trance": 1, "Type": 100, "Name": "xshell_ssh"}
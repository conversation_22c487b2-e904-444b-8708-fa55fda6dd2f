{"APPID": 24122, "BytePs": 0, "DetailRespond": {"A": {"type": "keyword", "rule": {"keyword": "oray"}}, "EXPR": "(A)", "APPID": 24122}, "Level": 45, "LibRespond": {"Lib": "24122.so", "PktNum": 1}, "PbDrop": 0, "PcapDrop": 0, "ReNewApp": 1, "Regex_Rule": [{"ProID": 23, "Property": 1, "Regex": "-----BEGIN\\s+PUBLIC\\s+KEY-----\\x0aMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBg.*IDAQAB\\x0a-----END\\s+PUBLIC\\s+KEY-----"}], "Respond": 2, "Reverse": "ReverseInfor", "SaveBytes": -1, "Trance": 1, "Type": 100, "Name": "oray_full"}
{"APPID": 24124, "BytePs": 0, "DetailRespond": {"A": {"type": "keyword", "rule": {"keyword": "oray"}}, "EXPR": "(!A)", "APPID": 24124}, "Level": 45, "LibRespond": {"Lib": "24124.so", "PktNum": 1}, "PbDrop": 0, "PcapDrop": 0, "ReNewApp": 1, "Regex_Rule": [{"ProID": 23, "Property": 1, "Regex": "-----BEGIN\\s+PUBLIC\\s+KEY-----\\x0aMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBg.*IDAQAB\\x0a-----END\\s+PUBLIC\\s+KEY-----"}], "Respond": 2, "Reverse": "ReverseInfor", "SaveBytes": -1, "Trance": 1, "Type": 100, "Name": "oray_sos"}
{"APPID": 24128, "BytePs": 0, "Level": 45, "PbDrop": 0, "PcapDrop": 0, "ReNewApp": 1, "Regex_Rule": [{"ProID": 23, "Property": 1, "Regex": "router\\s+teamviewer"}], "Respond": 2, "Reverse": "ReverseInfor", "SaveBytes": -1, "Trance": 1, "Type": 100, "Name": "teamviewer_dns"}
{"APPID": 24132, "BytePs": 0, "Level": 45, "PbDrop": 0, "PcapDrop": 0, "ReNewApp": 1, "Regex_Rule": [{"ProID": 22, "Property": 1, "Regex": "anydesk"}], "Respond": 2, "Reverse": "ReverseInfor", "SaveBytes": -1, "Trance": 1, "Type": 100, "Name": "anydesk_tls"}
 
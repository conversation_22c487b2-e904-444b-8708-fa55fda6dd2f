#!/bin/bash

cd $(dirname $(realpath $0))

export THE_ROOT=/opt/GeekSec/th/
BASEDIR="${THE_ROOT}/bin/conf/"

for THID in $(seq 0 1)
do
    mkdir -p ${BASEDIR}/${THID}/JsonRule/BasicRule/UserRule/PrivateRule/
    mkdir -p ${BASEDIR}/${THID}/JsonRule/BasicRule/LibFolder/
    mkdir -p ${BASEDIR}/${THID}/LibConfig/
    
    /usr/bin/cp -rf private_json/* ${BASEDIR}/${THID}/JsonRule/BasicRule/UserRule/PrivateRule/
    /usr/bin/cp -rf private_so/* ${BASEDIR}/${THID}/JsonRule/BasicRule/LibFolder/
    /usr/bin/cp -rf private_conf/* ${BASEDIR}/${THID}/LibConfig/
done

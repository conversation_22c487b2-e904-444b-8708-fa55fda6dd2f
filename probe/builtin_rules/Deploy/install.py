
import os


for task_id in range(2):
    base_dir = f"/opt/GeekSec/th/bin/conf/{task_id}"
    outjson_dir = f"{base_dir}/JsonRule/BasicRule/UserRule/PrivateRule/"
    outso_dir = f"{base_dir}/JsonRule/BasicRule/UserRule/LibFolder/"    
    conf_dir = f"{base_dir}/LibConfig/"    

    for outdir in [outjson_dir,outso_dir,conf_dir]:
        if not os.path.exists(outdir):
            os.mkdir(outdir)

    os.system(f"cp -f private_json/* {outjson_dir}")
    os.system(f"cp -f private_so/* {outso_dir}")
    os.system(f"cp -f private_conf/* {conf_dir}")

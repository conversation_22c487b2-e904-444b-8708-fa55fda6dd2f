import os

CurrentDir = os.getcwd()
SRC_DIR = os.path.join(CurrentDir,"src")
SDKDir = os.getenv('THE_SDK')
THE_ROOT = os.getenv('THE_ROOT')

def make_suffix():
    cmd = "git rev-parse HEAD"
    data = os.popen(cmd).read().strip()
    return data

def build_target_so(module_name):
    module_dir = os.path.join(SRC_DIR,module_name)
    os.chdir(module_dir)
    cpp_name = f"{module_name}.cpp" 
    if not os.path.exists(cpp_name):
        return
    add_c_compile = ""
    for x in os.listdir(module_dir):
        if x.endswith(".c"):
            add_c_compile = "*.c"
    cmd = f"g++ *.cpp {add_c_compile} -std=c++11  \
        {SDKDir}/lib/libcommontools.a -I{SDKDir}/include/  -fPIC -shared -o {module_name}.so \
            -I {THE_ROOT}/sdk/include/ -I {THE_ROOT}/sdk/../lib_src/common_tool/src/basic_parse/ -I {THE_ROOT}/sdk/../lib_src/common_tool/src/basic_parse/GeneralInclude/"
    print(cmd)
    os.system(cmd)

INSTALL_MODULES = os.listdir("./src")
version = make_suffix()
output_dir = f"./PrivateRule_Install"

if os.path.exists(output_dir):
    os.system(f"rm -rf {output_dir}")

json_out_dir = os.path.join(output_dir,"private_json")
so_out_dir = os.path.join(output_dir,"private_so")
config_out_dir = os.path.join(output_dir,"private_conf")

for name in [output_dir,json_out_dir,so_out_dir,config_out_dir]:
    if os.path.exists(name):
        os.rmdir(name)
    os.mkdir(name)

os.system(f"cp -f Deploy/install.py {output_dir}")
os.system(f"echo {version} >> {json_out_dir}/version.txt")
os.system(f"cp -f {CurrentDir}/json_lib/* {json_out_dir}/")

for module in INSTALL_MODULES:
    build_target_so(module)

os.chdir(CurrentDir)

for module in INSTALL_MODULES:
    subdir = os.path.join(SRC_DIR,module)
    for name in os.listdir(subdir):
        if name.endswith("json"):
            json_name = os.path.join(subdir,name)
            os.system(f"cp -f {json_name} {json_out_dir}/")
        if name.endswith(".so"):
            so_file = os.path.join(subdir,name)
            os.system(f"cp -f {so_file} {so_out_dir}/")
        if os.path.isdir(name) and name.startswith("lib_conf_"):
            new_name = name.replace("lib_conf_","")
            cnof_file = os.path.join(subdir,name)
            os.system(f"cp -rf {cnof_file} {config_out_dir}/")

out_tar = f"{output_dir}_{version}.tar.gz"
if os.path.exists(out_tar):
    os.system(f"rm -rf {out_tar}")
os.system(f"tar -cvf {out_tar} {output_dir}")
os.system(f"rm -rf {output_dir}")
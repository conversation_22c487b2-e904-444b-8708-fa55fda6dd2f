#!/bin/bash

cd $(dirname $(realpath $0))

#var
#export THE_ROOT=/opt/GeekSec/th/
OUTPUT="PrivateRule_Install"

#clean
/usr/bin/rm -rf ${OUTPUT}
mkdir -p ${OUTPUT}/private_json/
mkdir -p ${OUTPUT}/private_so/
mkdir -p ${OUTPUT}/private_conf/

#build
pushd src
for lib in $(ls)
do
    if [ "${lib}" == "common" ]
    then
        continue
    fi
    
    g++ -std=c++11 -g $(find ${lib} -type f \( -name '*.cpp' -or -name '*.c' \) | tr '\n' ' ') \
        -fPIC -shared -L ${THE_ROOT}/sdk/lib/ -lthe_protobuf -lcommontools -lhs -ljson -o ../${OUTPUT}/private_so/${lib}.so \
        -I ${THE_ROOT}/sdk/include/ -I ${THE_ROOT}/lib_src/common_tool/src/basic_parse/ -I ${THE_ROOT}/lib_src/common_tool/src/basic_parse/GeneralInclude/
    find ${lib} -mindepth 1 -maxdepth 1 -type f -name '*.json' | xargs -i /usr/bin/cp -f {} ../${OUTPUT}/private_json/
    [ -d "${lib}/conf/" ] && /usr/bin/cp -rf ${lib}/conf/* ../${OUTPUT}/private_conf/
done
popd

/usr/bin/cp -f json_lib/* ${OUTPUT}/private_json/

/usr/bin/cp -f install.sh ${OUTPUT}

echo $1 > ${OUTPUT}/private_json/th_image_version.txt
echo $2 > ${OUTPUT}/private_json/version.txt

tar zcvf ${OUTPUT}.${2}.tar.gz ${OUTPUT}

curl -F "parent_dir=/private_th_lib" -F "relative_path=" -F "file=@${OUTPUT}.${2}.tar.gz" $(curl -XGET http://sfp.gs.lan/api/v2.1/upload-links/eabd28bab8c24459a89a/upload/ | awk -F'"' '{print $(NF-1)}')

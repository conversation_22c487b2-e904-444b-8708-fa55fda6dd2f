{
    "max_task": 2,
    "//task": [
        {"numa": 0, "hugepage": [10,10], "limit_mbps": 1500, "lcore": [1,2,3,4,5,6,7,8,9,10,11,12,13,14]},
        {"numa": 0, "hugepage": [3,3], "limit_mbps": 500, "lcore": [15,32,33,34,35,36]}
    ],
    "////task": [
        {"numa": -1, "hugepage": [10], "limit_mbps": 1500, "lcore": [1,2,3,4,5,6,7,8,9,10,11,12,13,14]},
        {"numa": -1, "hugepage": [3], "limit_mbps": 500, "lcore": [15,16,17,18,19,20]}
    ],
    "//////task": [
        {"numa": 0, "hugepage": [20,0], "limit_mbps": 100000, "lcore": [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,32,33,34,35,36,37,38,39,40,41,42]},
        {"numa": 1, "hugepage": [0,20], "limit_mbps": 100000, "lcore": [17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,48,49,50,51,52,53,54,55,56,57,58]}
    ],
    "//if": [
        {"pci": "xxxx:xx:xx.0", "name": "4"},
        {"pci": "xxxx:xx:xx.1", "name": "3"},
        {"pci": "xxxx:xx:xx.2", "name": "2"},
        {"pci": "xxxx:xx:xx.3", "name": "1"}
    ],
    "//0": [ 0, 1, 3 ],
    "//1": [ 2 ],
    "//suspend": [ "1" ],
    "//forward": [ "0" ],
    "task": [
        {"numa": 0, "hugepage": [10,10], "limit_mbps": 1500, "lcore": [LCOREMARK0LCOREMARK]},
        {"numa": 0, "hugepage": [3,3], "limit_mbps": 500, "lcore": [LCOREMARK1LCOREMARK]}
    ],
    "if": [
    ],
    "0": [],
    "1": [],
    "suspend": [],
    "forward": []
}
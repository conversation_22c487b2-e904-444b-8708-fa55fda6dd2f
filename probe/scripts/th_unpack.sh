#!/bin/bash

function t_cmd()
{
    if [ $1 -eq 0 ];
    then 
        echo "完成"
    else 
        echo "出错"
        exit 1
    fi
}

tmpdir=/tmp/th_install
B_UPDATE="false"
PRODUCT=""
VAILD_PRODUCT=("product_forensics" "product_analysis" "product_probe" "product_mining")

while getopts "hup:" arg #选项后面的冒号表示该选项需要参数
do
    case $arg in
        h)
            echo "for install input $0"
            echo "for udpate input $0 -u"
            echo "for install product_x input $0 -p product_x"
            exit 1
            ;;
        u)
            B_UPDATE="true"
            ;;
        p)
            echo ${VAILD_PRODUCT[@]} | grep -wq $OPTARG
            if [ $? -eq 0 ]
            then
                PRODUCT=$OPTARG
            fi
            ;;
        ?) #当有不认识的选项的时候arg为?
            echo "unkonw argument"
            ;;
    esac
done

if [ ${B_UPDATE} == "true" ]
then
    if [ "x${PRODUCT}" != "x" ]
    then
        echo "usage:$0 -u or $0 -p product_x"
        exit 1
    fi
fi

source ~/.bashrc

#space check
echo "检测磁盘空间："
DISKAVAIL=$(LANG=C df -BG /tmp | tail -n 1 | awk '{print $4}')
if [ ${DISKAVAIL/%G} -lt 3 ]
then
    echo '/tmp 磁盘空间不足! 至少需要3GB!'
    exit 1
else
    echo '/tmp 剩余空间'${DISKAVAIL}
fi

mkdir -p /opt/GeekSec/ >/dev/null 2>&1
DISKAVAIL=$(LANG=C df -BG /opt/GeekSec | tail -n 1 | awk '{print $4}')
if [ ${DISKAVAIL/%G} -lt 3 ]
then
    echo '/opt/GeekSec 磁盘空间不足! 至少需要3GB!'
    exit 1
else
    echo '/opt/GeekSec 剩余空间'${DISKAVAIL}
fi
echo "检测磁盘空间 完成"


#split
echo "拆解部署包："
/usr/bin/rm -rf ${tmpdir}
mkdir ${tmpdir}
sed -n -e '1,/^#TH_UNPACK_ENDMARK$/!p' $0 > "${tmpdir}/th_packet.tar.gz"  2>/dev/null #将二进制文件从.bin文件里分离出来
echo "拆解部署包 完成"

#unpack
echo "解压部署包："
pushd ${tmpdir}
tar zxf th_packet.tar.gz
/usr/bin/rm -f th_packet.tar.gz
echo "解压部署包 完成"

if [ $B_UPDATE == "true" ]
then
    echo "执行升级程序："
    bash th_upgrade.sh
    echo "执行升级程序 完成"
else
    echo "执行安装程序："
    if [ "x${PRODUCT}" == "x" ]
    then
        bash th_install.sh
    else
        bash th_install.sh -p $PRODUCT
    fi
    echo "执行安装程序 完成"
fi

popd

#clean
echo "清理部署包："
/usr/bin/rm -rf ${tmpdir}
echo "清理部署包 完成"

exit 0

#TH_UNPACK_ENDMARK

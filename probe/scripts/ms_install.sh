#!/bin/bash

#space check
DISKAVAIL=$(df -BG /tmp |tail -n 1 |awk '{print $4}')
if [ ${DISKAVAIL/%G} -lt 1 ]
then
    echo '/tmp space is not enough! 1GB at least!'
    exit 1
fi
mkdir -p /opt/GeekSec/ >/dev/null 2>&1
DISKAVAIL=$(df -BG /opt/GeekSec |tail -n 1 |awk '{print $4}')
if [ ${DISKAVAIL/%G} -lt 1 ]
then
    echo '/opt/GeekSec space is not enough! 1GB at least!'
    exit 1
fi

tmpdir=/tmp/ms_install
mkdir ${tmpdir}
sed -n -e '1,/^exit 0$/!p'  $0 > "${tmpdir}/ms_packet.tar.gz"  2>/dev/null #将二进制文件从.bin文件里分离出来

pkill -9 ms_server
cd ${tmpdir}
echo "解压文件 [[0%]]"
tar zxvf ms_packet.tar.gz
rm ms_packet.tar.gz
rm -rf /opt/GeekSec/ms/
mkdir -p /opt/GeekSec/ms/
cp -rf  ms/* /opt/GeekSec/ms/
pushd /opt/GeekSec/ms/ >/dev/null
./ms_server >/dev/null 2>&1 &
popd >/dev/null
echo "ms安装完毕[[100%]]"
rm -rf  $tmpdir

exit 0

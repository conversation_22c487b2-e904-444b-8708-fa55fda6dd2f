#!/bin/bash

# 定义颜色变量
RED='\033[31m'
GREEN='\033[32m'
YELLOW='\033[33m'
BLUE='\033[34m'
MAGENTA='\033[35m'
CYAN='\033[36m'
WHITE='\033[37m'
RESET='\033[0m' # 用于重置颜色到默认值

# 定义打印彩色文本的函数
color_echo() {
    local text="$1" # 要打印的文本   
    if [ -z $2 ]; then
        local color="${CYAN}"
    else  
        local color="$2" # 颜色变量
    fi 
    local curdatetime=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${MAGENTA}${curdatetime} ${color}${text}${RESET}" # 打印彩色文本并重置颜色
}

do_log(){
    "$@" >> ${THE_ROOT}/build/build.log 2>&1
}

function t_cmd()
{
    if [ $1 -eq 0 ];
    then 
       do_log echo "执行完毕"
    else 
        do_log echo "提示出错"
        exit 1
    fi
}

need_pack="true"
PRODUCT="product_forensics"
VER=""

SCRIPT_ROOT=$(dirname $(realpath $0))
if [ "$(realpath ${SCRIPT_ROOT}/../)" != "$(realpath ${THE_ROOT})" ]
then
    echo "Please check your THE_ROOT environmental variable!"
    exit 1
fi

if [ "x$VER" == "x" ]
then
    #VER="$(git branch | grep '*' | awk '{print $NF}' | tr '/' '-' | tr -d ')' |tr -d '）' | tr -d ' ')_$(git rev-list HEAD |wc -l)_$(git log -n1 --pretty=format:'%H')"
    VER="$(git rev-parse --abbrev-ref HEAD)_$(git rev-list HEAD |wc -l)_$(git rev-parse --short HEAD)"
fi

if [ "x$VER" == "x" ]
then
    echo "ERROR:no version"
    exit 1
fi

while getopts "p:v:n" arg #选项后面的冒号表示该选项需要参数
do
    case $arg in
        p)
            if [ "x$OPTARG" != "x" ]
            then
                PRODUCT=$OPTARG
            fi
            ;;
        v)
            if [ "x$OPTARG" != "x" ]
            then
                VER=$OPTARG
            fi
            ;;
        n)
            need_pack="false"
            ;;
        ?) #当有不认识的选项的时候arg为?
            echo "unkonw argument"
            ;;
    esac
done

#VER="${PRODUCT}_${VER}"

color_echo "build pruduct:$PRODUCT, version:$VER, need_pack:$need_pack"

find ${SCRIPT_ROOT} -type f -name '*.bin' | xargs -r /usr/bin/rm -f

check_dir(){
    if [ $PWD == ${SCRIPT_ROOT} ]; then
        color_echo "check dir failed" ${RED}
        exit 1
    fi
}

build_th(){
    color_echo "build_th"
    do_log pushd ${THE_ROOT}
    check_dir
    ./th_build.sh -p $PRODUCT -v $VER 
    t_cmd $?
    do_log popd
    color_echo "  done" ${GREEN}
}

############## 基础库 打包
pack_common_lib(){
    color_echo "pack_common_lib"
    /usr/bin/rm -f ${SCRIPT_ROOT}/common_lib_packet.tar.gz
    do_log pushd ${SCRIPT_ROOT}/../build/lib_src/
    check_dir
    do_log tar -zcvf ${SCRIPT_ROOT}/common_lib_packet.tar.gz common_lib/
    do_log popd
    cat common_lib_install.sh common_lib_packet.tar.gz >common_lib_$VER.bin
    /usr/bin/rm -f ${SCRIPT_ROOT}/common_lib_packet.tar.gz
    chmod 775 common_lib_$VER.bin
    color_echo "  done" ${GREEN}
}


############## th 打包
pack_th(){
    color_echo "pack_th"
    do_log pushd ${SCRIPT_ROOT}/../build
    check_dir

    /usr/bin/rm -rf ${SCRIPT_ROOT}/th/
    mkdir -p ${SCRIPT_ROOT}/th/
    /usr/bin/cp -rf bin  ${SCRIPT_ROOT}/th/
    /usr/bin/cp -rf ${THE_SDK}  ${SCRIPT_ROOT}/th/

    mkdir -p ${SCRIPT_ROOT}/th/lib_src
    /usr/bin/cp -rf lib_src/common_tool ${SCRIPT_ROOT}/th/lib_src/
    find ${SCRIPT_ROOT}/th/lib_src/ -type f -name '*.cpp' -or -name '*.c' -or -name '*.o'| xargs -r /usr/bin/rm -f

    echo $VER >${SCRIPT_ROOT}/th/.version
    do_log popd 

    /usr/bin/rm -f ${SCRIPT_ROOT}/th_packet.tar.gz
    do_log tar -zcvf th_packet.tar.gz th/ th_install.sh th_upgrade.sh
    /usr/bin/rm -rf th/
    cat th_unpack.sh th_packet.tar.gz >th_$VER.bin
    /usr/bin/rm -rf ${SCRIPT_ROOT}/th_packet.tar.gz
    chmod 775 th_$VER.bin
    
    color_echo "  done" ${GREEN}
}

BUILD_DIR=${THE_ROOT}/build
if [ ! -e ${BUILD_DIR} ]; then
    mkdir -p ${BUILD_DIR}
fi

echo > ${THE_ROOT}/build/build.log #清空build.log
build_th
pack_common_lib
pack_th



############# 发布文件
if [ "$need_pack" == "true" ]
then
    cd ${SCRIPT_ROOT}
    /usr/bin/rm -rf th_release_file_local_v$VER
    mkdir th_release_file_local_v$VER
    mv th_*bin th_release_file_local_v$VER
    tar zcvf th_release_file_local_v$VER.tar.gz th_release_file_local_v$VER
    /usr/bin/rm -rf th_release_file_local_v$VER

    /usr/bin/rm -rf common_lib_$VER
    mkdir common_lib_$VER
    mv common_lib_install_*bin common_lib_$VER
    tar zcvf common_lib_$VER.tar.gz common_lib_$VER
    /usr/bin/rm -rf common_lib_$VER
fi

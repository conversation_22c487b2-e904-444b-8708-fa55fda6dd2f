#!/bin/bash

THE_DIR="/opt/GeekSec/th"
PRODUCT=""
VAILD_PRODUCT=("product_forensics" "product_analysis" "product_probe" "product_mining")

cd $(dirname $(realpath $0))
source ~/.bashrc

while getopts "p:" arg #选项后面的冒号表示该选项需要参数
do
    case $arg in
        p)
            echo ${VAILD_PRODUCT[@]} | grep -wq $OPTARG
            if [ $? -eq 0 ]
            then
                PRODUCT=$OPTARG
            fi
            ;;
        ?) #当有不认识的选项的时候arg为?
            echo "unkonw argument"
            ;;
    esac
done

if [ "x${PRODUCT}" == "x" ]
then
    PRODUCT=$(cat th/bin/.product_modify_last | awk -F: '{print $NF}')
fi

#clean
echo "卸载已安装程序："
for i in $(seq 0 1)
do
    systemctl stop thd.$i
done
systemctl stop thd.mem
systemctl stop thd.veth

for i in $(seq 0 1)
do
    systemctl disable thd.$i
done
systemctl disable thd.mem
systemctl disable thd.veth


/usr/bin/rm -rf $THE_DIR
/usr/bin/rm -f /etc/init.d/thdd
mkdir -p $THE_DIR
echo "卸载已安装程序 完成"

#install
echo "执行安装 ${PRODUCT}："
/usr/bin/mv th/* $THE_DIR/
/usr/bin/mv th/.version $THE_DIR/

#拷贝dpdk目录
if [ -d $RTE_SDK/usertools/ ]
then
    /usr/bin/rm -rf $THE_DIR/bin/usertools/
    /usr/bin/cp -rf $RTE_SDK/usertools/ $THE_DIR/bin/
fi

if [ -d $RTE_SDK/$RTE_TARGET/kmod/ ]
then
    if [ "x$(modinfo $RTE_SDK/$RTE_TARGET/kmod/igb_uio.ko | grep vermagic | awk '{print $2}')" == "x$(uname -r)" ]
    then
        /usr/bin/rm -rf $THE_DIR/bin/kmod/$(uname -r)
        /usr/bin/cp -rf $RTE_SDK/$RTE_TARGET/kmod/ $THE_DIR/bin/kmod/$(uname -r)
    fi
fi

#change config
SKIP=1
NEED_ARRAY=(14 6)
for i in $(seq 0 1)
do
    sed -i 's/LCOREMARK'$i'LCOREMARK/'$(${THE_DIR}/bin/gen_cpulist.sh 0 ${SKIP} ${NEED_ARRAY[$i]})'/g' ${THE_DIR}/bin/conf_pub/ifconf.json
    SKIP=$(($SKIP+${NEED_ARRAY[$i]}))
done

if [ "x${PRODUCT}" != "x" ]
then
    pushd ${THE_DIR}/bin
    bash product_modify.sh $PRODUCT
    popd
fi


#install rpm
rpm -ivh --nodeps --force $THE_DIR/sdk/deps/libssh*.rpm $THE_DIR/sdk/deps/wireshark*.rpm



#enable service
for i in $(seq 0 1)
do
    systemctl enable ${THE_DIR}/bin/service/thd.$i.service
done
systemctl enable ${THE_DIR}/bin/service/thd.mem.service
systemctl enable /opt/GeekSec/th/bin/service/thd.veth.service

systemctl daemon-reload

systemctl start thd.veth
systemctl start thd.mem
for i in $(seq 0 1)
do
    systemctl start thd.$i
done

echo "执行安装${PRODUCT} 完成"

exit 0

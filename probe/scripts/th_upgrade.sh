#!/bin/bash

THE_DIR="/opt/GeekSec/th"
THE_BAC="th_bac_$(date +%s)"
THE_BAC_DIR="/opt/GeekSec/${THE_BAC}"
FIND_STR='th_bac_*'
PRODUCT=""
VAILD_PRODUCT=("product_forensics" "product_analysis" "product_probe" "product_mining")

cd $(dirname $(realpath $0))
source ~/.bashrc

while getopts "p:" arg #选项后面的冒号表示该选项需要参数
do
    case $arg in
        p)
            echo ${VAILD_PRODUCT[@]} | grep -wq $OPTARG
            if [ $? -eq 0 ]
            then
                PRODUCT=$OPTARG
            fi
            ;;
        ?) #当有不认识的选项的时候arg为?
            echo "unkonw argument"
            ;;
    esac
done

#backup
echo "备份探针重要配置："
/usr/bin/rm -rf ${THE_BAC_DIR}
/usr/bin/cp -rf ${THE_DIR} ${THE_BAC_DIR}
echo "备份探针重要配置 完成"
#install

if [ "x${PRODUCT}" == "x" ]
then
    PRODUCT=$(cat ${THE_BAC_DIR}/bin/.product_modify_last | awk -F: '{print $NF}')
fi

if [ "x${PRODUCT}" == "x" ]
then
    bash th_install.sh
else
    bash th_install.sh -p $PRODUCT
fi

#recover
echo "执行探针配置恢复："
pushd ${THE_DIR}/bin
./thd.all.stop

cat ${THE_BAC_DIR}/bin/conf_pub/ifconf.json | grep -q '"task"'
if [ $? -eq 0 ]
then
    /usr/bin/cp -f ${THE_BAC_DIR}/bin/conf_pub/ifconf.json ./conf_pub/
else
    max_task=$(cat ${THE_BAC_DIR}/bin/conf_pub/ifconf.json | ./jq -c '."max_task"')
    if=$(cat ${THE_BAC_DIR}/bin/conf_pub/ifconf.json | ./jq -c '."if"')
    suspend=$(cat ${THE_BAC_DIR}/bin/conf_pub/ifconf.json | ./jq -c '."suspend"')
    cat conf_pub/ifconf.json | ./jq -c '."max_task"='$max_task'|."if"='$if'|."suspend"='$suspend > conf_pub/ifconf.json.tmp0
    for i in $(seq 0 $(($max_task - 1)))
    do
        iflist=$(cat ${THE_BAC_DIR}/bin/conf_pub/ifconf.json | ./jq -c '."'$i'"')
        cat conf_pub/ifconf.json.tmp$i | ./jq -c '."'$i'"='$iflist > conf_pub/ifconf.json.tmp$(($i+1))
    done
    cat conf_pub/ifconf.json.tmp$max_task > conf_pub/ifconf.json
    /usr/bin/rm -f conf_pub/ifconf.json.tmp*
fi

#TODO sync from database

./thd.all.restart
popd

echo "执行探针配置恢复 完成"
#clean
echo "清理备份文件："
find /opt/GeekSec/ -type d -name "$FIND_STR" | grep -v ${THE_BAC} | xargs -r /usr/bin/rm -rf 
echo "清理备份文件 完成"

exit 0

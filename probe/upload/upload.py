import requests
import re
import os
import sys


def upload_file(url, file_path, parent_dir, relative_path=None):
    if relative_path == '.':
        relative_path = None
    sys.stdout.write('upload: fp ' + file_path + '\n')
    sys.stdout.write('upload: pd ' + parent_dir + '\n')
    if relative_path is not None:
        sys.stdout.write('upload: rp ' + relative_path + '\n')
    
    ret = requests.post(url, data={'parent_dir': os.path.join(parent_dir), 'relative_path': relative_path},
                        files={'file': open(file_path, 'rb')})
    sys.stdout.write(str(ret.status_code) + '\n')
    sys.stdout.write(ret.text + '\n')


def upload_dir(url, dir_path, parent_dir):
    base_dir = os.path.basename(dir_path)
    '''
    for root, dirs, files in os.walk(dir_path):  
        for momo in dirs:  
            os.chmod(os.path.join(root, momo), 777)
        for momo in files:
            os.chmod(os.path.join(root, momo), 777)
    '''

    for root, dirs, files in os.walk(dir_path):
        for file_name in files:
            rel_path = os.path.relpath(root, dir_path)
            if rel_path == '.':
                rel_path = ''
            rel_path = os.path.join(base_dir, rel_path)
            upload_file(url, os.path.join(root, file_name), parent_dir, rel_path)


if __name__ == "__main__":
    #dest = 'http://sfp.gs.lan/u/d/4952d8afb80f44b29b98/'#os.environ['DEST']
    #source = './'#os.environ['SRC']
    dest = os.environ['DEST']
    source = sys.argv[1]
    
    ret = requests.get(dest)
    parent_dir = re.match(r'.+path: "([\w\/]*\/)".+', ret.text.replace('\n', ''))[1]
    sys.stdout.write("parent_dir: " + parent_dir + '\n')
    uid = re.match(r'http:\/\/sfp.gs.lan\/u\/d\/(.+)\/', dest)[1]
    sys.stdout.write("uid: " + uid + '\n')
    api_url = f"http://sfp.gs.lan/api/v2.1/upload-links/{uid}/upload/"
    ret = requests.get(api_url)
    upload_link = ret.json()['upload_link']

    '''
    os.chmod(source, 777)
    '''
    if os.path.isdir(source):
        source = os.path.abspath(source)
        upload_dir(upload_link, source, parent_dir)
    else:
        upload_file(upload_link, source, parent_dir)

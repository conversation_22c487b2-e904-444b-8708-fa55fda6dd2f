1. 根据五元组过滤
./pcap_filter -r /data/pcapfiles/0/1/full_flow/112207/1615790063_0.pcap -w tuple5.pcap -Y "ip.addr==************** and ip.addr==************* and tcp.port==2079 and tcp.port==3390"

2. 根据session_id过滤
./pcap_filter -r /data/pcapfiles/0/1/full_flow/112207/1615790063_0.pcap -w ssid.pcap -ssid "9999229596633748095 9998658910060824869"

3. 根据session_id过滤，并设定每会话包数上限20
./pcap_filter -r /data/pcapfiles/0/1/full_flow/112207/1615790063_0.pcap -w ssid.pcap -p 20 -ssid "9999229596633748095 9998658910060824869"

4. 根据session_id文件过滤
./pcap_filter -r /data/pcapfiles/0/1/full_flow/112207/1615790063_0.pcap -w ssidfile.pcap -ssidfile /tmp/ssidfile

5. 根据session_id文件过滤，并设定每会话包数上限20
./pcap_filter -r /data/pcapfiles/0/1/full_flow/112207/1615790063_0.pcap -w ssidfile.pcap -p 20 -ssidfile /tmp/ssidfile

6. 根据规则ID过滤
./pcap_filter -r /data/pcapfiles/0/1/full_flow/112207/1615790063_0.pcap -w ruleid.pcap -ruleid "35001 35002"

7. 根据规则ID文件过滤
./pcap_filter -r /data/pcapfiles/0/1/full_flow/112207/1615790063_0.pcap -w ruleidfile.pcap -ruleidfile /tmp/ruleidfile

8.过滤所有命中规则的pcap
./pcap_filter -r /data/pcapfiles/0/1/full_flow/112207/1615790063_0.pcap -w rule.pcap -rule

9.过滤所有命中规则的pcap，并按规则ID拆分，需要指定输出目录，输出目录需要预先创建并清空
例如：
mkdir -p /data/pcapfiles/0/1/full_flow/112207/1615790063_0_ruledir
./pcap_filter -r /data/pcapfiles/0/1/full_flow/112207/1615790063_0.pcap -d /data/pcapfiles/0/1/full_flow/112207/1615790063_0_ruledir

10.过滤指定session_id(最多128个)的pcap，并按session_id拆分，需要指定输出目录，输出目录需要预先创建并清空
例如：
mkdir -p /data/outpcap/thread5/1615790063_0_sessiondir
./pcap_filter -r /data/pcapfiles/0/1/full_flow/112207/1615790063_0.pcap -d /data/outpcap/thread5/1615790063_0_sessiondir -ssid "9999229596633748095 9998658910060824869"

11.过滤指定session_id(最多128个)的pcap，并按session_id拆分，需要指定输出目录，输出目录需要预先创建并清空，并设定每会话包数上限20
例如：
mkdir -p /data/outpcap/thread5/1615790063_0_sessiondir
./pcap_filter -r /data/pcapfiles/0/1/full_flow/112207/1615790063_0.pcap -d /data/outpcap/thread5/1615790063_0_sessiondir -p 20 -ssid "9999229596633748095 9998658910060824869"

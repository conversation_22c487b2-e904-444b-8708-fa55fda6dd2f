# 推送数据格式

## 规则命中量

### 格式
```

{
    "type": 201,                        //类型201
    "time": 1586761248,                 //发送时间戳,命中规则30秒钟发送一次
    "rule_id": 35001,                   //命中规则ID
    "bytes_30s": "698221293",           //30s命中总字节量(未作多线程同步，数据不精准)
    "packets_30s": 807243               //30s命中总包数(未作多线程同步，数据不精准)
}
```

## 流量态势

### 格式
```
{
    "type": 202,                        //类型202
    "time": 1586761338,                 //发送时间戳,30秒钟发送一次
    "bps": "96714280",                  //当前流量bps
    "bps_in": "0",                      //当前流量bps(进：包的目的IP为内网)
    "bps_out": "96714280",              //当前流量bps(出：包的目的IP不是内网)
    "pps": 14834,                       //当前流量pps
    "pps_in": 0,                        //当前流量pps(进：包的目的IP为内网)
    "pps_out": 14834,                   //当前流量pps(出：包的目的IP不是内网)
    "conn": 77652,                      //当前并发连接数
    "conn_in": 0,                       //当前并发连接数(进：会话服务端IP为内网)
    "conn_out": 77652,                  //当前并发连接数(出：会话服务端IP不是内网)
    "pps_ipv4": 14834,                  //IPV4包pps
    "pps_ipv6": 0,                      //IPV6包pps
    "pps_notip": 0,                     //其他包pps(非IP包)
    "pps_tcp": 9534,                    //TCP包pps
    "pps_udp": 3441,                    //UDP包pps
    "pps_ipother": 1859                 //其他包pps(IP包,非TCP,非UDP)
}
```

```
{
    "type": 217,                        //类型217
    "time": 1586761338,                 //发送时间戳,30秒钟发送一次
    "pkts": "522305",                   //探针启动后处理总包数
    "bytes": "422725112"                //探针启动后处理总字节数
}
```

```
{
    "type": 220,                        //类型220
    "task_id": "1",                     //任务ID
    "batch_id": "100002",               //批次ID
    "device_id": "2258430183",          //设备号
    "time": 1652849469,                 //发送时间戳,3秒钟发送一次
    "portid": 0,                        //网口ID
    "pci": "0000:04:00.1",              //网口PCI地址
    "mac": "a0:36:9f:0b:06:31",         //网口MAC地址
    "name": "2",                        //网口名
    "bps": "920234493",                 //当前网口bps
    "pps": "255561",                    //当前网口pps
    "dev_drop": "1.793%",               //硬件丢包率
    "ring_drop": "0.000%",              //软件丢包率
    "not_lost_pkts": "766685",          //3秒未丢包数
    "lost_pkts": "14000",               //3秒丢包数
    "handle_bps": "920790056",          //当前处理性能bps
    "handle_pps": "255374"              //当前处理性能pps
}
```

```
{
    "type": 222,                        //类型222
    "task_id": "1",                     //任务ID
    "batch_id": "100002",               //批次ID
    "device_id": "2258430183",          //设备号
    "time": 1652849490,                 /发送时间戳,30秒钟发送一次
    "not_lost_pkts": "7381046",         //30秒未丢包数
    "lost_pkts": "144441"               //30秒丢包数
}

```

## 防御统计数据格式

### 防御类型列表

类型id | 防御类型
-|-
1|非法Mac
2|非法IP范围
3|ARP非法Mac
4|ARP劫持
5|ARP Cache 缓存攻击
6|端口扫描
7|异常服务
8|异常协议
9|隐藏信道
10|TCP SYN DDOS
11|UDP DDOS
12|ICMP DDOS
13|DNS DDOS
14|Ping of Death
15|单播
16|多播
17|广播
18|ARP 风暴
19|LLDP 风暴
20|IP分片  风暴
21|IP Checksum  风暴
22|IGMP  风暴
23|TCP FIN/RST 标识  风暴
24|网络中出现新Mac
25|虚假Mac
26|内网出现新的IP网段
27|虚假IP
28|无IP
29|IP异常分片
30|Ping To Death
31|UDP Checksum  风暴
32|Mac地址为零
33|非法Mac，ARP Sender 中的Mac与合法Mac范围不同

### 格式
```
{
    "type": 203,                        //类型203
    "time": 1586760947,                 //发送时间戳,5分钟发送一次
    "id": 10,                           //防御类型ID
    "bytes": "89594523",                //5分钟内上述id命中丢弃的总字节数
    "batch_id": 0                       //批次号
}
```

## 过滤系统统计数据格式

### 筛选规则下发
筛选规则下发时，需要在原先的规则基础上添加规则ID
```
{
    "ID": 12345,                        //新添加，与数据库id保持一致
    "IP": "*******",
    "IPPro": [1, 2, 3],
    "IPPro_Type": "Select",
    "TCPPort": [80, 81],
    "TCPPort_Type": "Select",
    "UDPPort": [9000, 9001],
    "UDPPort_Type": "Select"
}
```

### 格式
```
{
    "type": 204,                        //类型204
    "time": 1586763657,                 //发送时间戳,5分钟发送一次
    "id": 12345,                        //过滤规则id
    "bytes": "601507130"                //5分钟内上述id命中的总字节数
}
```


## 数据态势

### 格式

```
{
    "type": 205,                        //类型205
    "time": 1586768429,                 //发送时间戳,30秒钟发送一次
    "bps": "100056000",                 //当前流量bps
    "pps": 15049,                       //当前流量pps
    "conn": 0,                          //当前并发连接数
    "bps_filter_in": "100056000",       //进入过滤模块流量bps
    "bps_filter_drop": "32004640",      //过滤模块丢弃流量bps
    "bps_filter_out": "68051352",       //出过滤模块流量bps
    "bps_defense_in": "68051352",       //进入防御模块流量bps
    "bps_defense_drop": "68051352",     //防御模块丢弃流量bps
    "bps_defense_out": "0",             //出防御模块流量bps
    "bps_rule_hit": "0",                //规则命中流量bps
    "bytes_30s_rule_hit": "0",          //30s内命中规则总量byte
    "pkts_30s_rule_hit": 0,             //30s内命中规则总包数
    "bytes_rule_hit_all": "0",          //历史命中规则总量byte
    "pkts_rule_hit_all": "0"            //历史命中规则总包数
}
```

```
{
    "type": 210,                        //类型210
    "time": 1586768399,                 //发送时间戳,30秒钟发送一次
    "pcap_bytes_all": "2306216584",     //历史PCAP留存总量byte
    "pcap_pkts_all": "2687229",         //历史PCAP留存总包数
    "pcap_bytes_30s": "263865778",      //30s内PCAP留存总量byte
    "pcap_pkts_30s": 299450,            //30s内PCAP留存总包数
    "pcap_bps": "70364200"              //PCAP留存bps
}
```

```
{
    "type": 221,                        //类型221
    "time": 1625220066,                 //发送时间戳,30秒钟发送一次
    "drop_pcap_bytes_all": "14856243",  //历史PCAP丢弃（白名单规则）总量byte
    "drop_pcap_pkts_all": "19159",      //历史PCAP丢弃（白名单规则）总包数
    "drop_pcap_bytes_30s": "0",         //30s内PCAP丢弃（白名单规则）总量byte
    "drop_pcap_pkts_30s": 0,            //30s内PCAP丢弃（白名单规则）总包数
    "drop_pcap_bps": "0",               //PCAP丢弃（白名单规则）bps
    "device_id": "3486463558"
}
```

```
{
    "type": 212,                        //类型212
    "time": 1586771853,                 //发送时间戳,30秒钟发送一次
    "all_pb_num": "45736",              //历史日志提取总条目数
    "all_pb_bytes": "32936627",         //历史日志提取总字节数byte
    "num_30s_pb": 30079,                //30s内日志提取条目数
    "bytes_30s_pb": "24732113",         //30s内日志提取字节数byte
    "pb_ps": 1002                       //日志提取速度(条目数每秒)
}
```

```
{
    "type": 216,                        //类型216
    "time": 1586771853,                 //发送时间戳,30秒钟发送一次
    "pb_type": 1,                       //日志类型
    "pb_num": "45736",                  //此类型日志提取总条目数
    "pb_bytes": "32936627"              //此类型日志提取总字节数byte
}
```
#### PB类型表

```
{
    "4": "dns",
    "6": "noip",
    "7": "mac",
    "28": "ssh",
    "29": "ssl",
    "30": "session",
    "41": "s7",
    "42": "modbus",
    "61": "esp",
    "80": "http",
    "237": "dns",
    "639": "rdp",
    "799": "dns"
}
```

## 协议态势

### 协议id与协议


协议id | 协议名称
-|-
10000|UNKNOWN
10001|APP_FTP
10012|APP_NNTP
10022|APP_WakeOnLan
10026|APP_RIP
10027|APP_IGRP
10028|APP_EIGRP
10029|APP_OSPF
10030|APP_IGP
10031|APP_EGP
10032|APP_BGP
10033|APP_VRRP
10034|APP_SNMP
10035|APP_DHCP
10037|APP_DHCPv6
10040|APP_ICMP_v4
10042|APP_ICMP_v6
10044|APP_ARP
10046|APP_NBNS
10049|APP_NBDGM
10056|APP_NBSS
10061|APP_SSH
10065|APP_Rlgin
10066|APP_Telnet
10067|APP_MAIL_SMTP
10069|APP_WHOISDAS
10070|APP_Tacacs+
10071|APP_DNS
10074|APP_Il_DNS
10093|APP_KERBEROS
10101|APP_MAIL_POP
10103|APP_DCERPC
10105|APP_MAIL_IMAP
10113|APP_LDAP
10120|APP_CISCOVPN
10123|APP_SMB
10127|APP_SYSLOG
10136|APP_RTSP
10138|APP_VMWARE
10145|APP_SOCKS5
10146|APP_OPENVPN
10148|APP_TDS
10149|APP_MSSQL
10152|APP_NDPI_CITRIX
10154|APP_H323
10158|APP_MSN
10159|APP_RTMP
10161|APP_SKINNY
10162|APP_NFS
10165|APP_MYSQL
10171|APP_IAX
10172|APP_RADMIN
10180|APP_POSTGRES
10184|APP_VNC
10188|APP_TEAMVIEWER
10189|APP_XDMCP
10201|APP_ANCP
10205|APP_TOR
10208|APP_TEAMSPEAK
10209|APP_ENIP
10213|APP_SOPCAST
10215|APP_ORACLE_TNS
10216|APP_GUILDWARS
10217|APP_DOFUS
10221|APP_NDPI_EDONKEY
10222|APP_FIESTA
10226|APP_FILETOPIA
10231|APP_STEAM
10232|APP_SOULSEEK
10233|APP_FCIP
10234|APP_TVANTS
10236|APP_SOCKS4
10237|APP_PANDO
10240|APP_CORBA
10243|APP_MANOLITO
10244|APP_PVFS
10245|APP_IMESH
10381|APP_NETMAN
10383|APP_ZATTOO
10386|APP_ZMQ
10387|APP_MAPLESTORY
10388|APP_STUN
10391|APP_AFP
10392|APP_APPLEJUICE
10393|APP_NDPI_BITBORRENT
10394|APP_SPOTIFY
10395|APP_TVUPLAYER
10396|APP_OSCAR
10397|APP_RSYNC
10399|APP_PPSTREAM
10401|APP_POPO
10402|APP_SHOUTCAST
10403|APP_STEALTHNET
10404|APP_LOTUS_NOTES
10405|APP_SOCRATES
10406|APP_HTTP_ACTIVESYNC
10409|APP_MMS
10413|APP_NTP
10419|APP_NETMAN_NEGOTIATION
10429|APP_L2TP
10432|APP_RADIUS
10435|APP_SSDP
10436|APP_HSRP
10438|APP_NETFLOW
10439|APP_GTP
10440|APP_GTP_MANAGER
10443|APP_IPMessage
10444|APP_MEGACO
10445|APP_XBOX
10449|APP_Teredo
10454|APP_MDNS
10456|APP_LLMNR
10457|APP_PCANYWHERE
10458|APP_SFLOW
10459|APP_Tencent_OICQ
10460|APP_Tencent_MayBe_OICQ
10464|APP_Tencent_QQMail
10465|APP_COLLECTD
10466|APP_QUAKE
10473|APP_NOE
10474|APP_NDPI_ARMAGETRON
10480|APP_TFTP
10481|APP_Bootstrap_Protocol
10482|APP_KONTIKI
10486|APP_VIBER
10487|APP_PPLIVE
10492|APP_HALFLIFE2_AND_MODS
10497|APP_XT800
10503|APP_SUNLOGIN
10504|APP_NDPI_BATTLEFIELD
10508|APP_SKYPE
10509|APP_HOPOPTS
10510|APP_IGMP
10511|APP_GGP
10512|APP_STREAM
10513|APP_CBT
10514|APP_BBN_RCC
10515|APP_NVPII
10516|APP_PUP
10517|APP_ARGUS
10518|APP_EMCON
10519|APP_XNET
10520|APP_CHAOS
10521|APP_MUX
10522|APP_DCNMEAS
10523|APP_HMP
10524|APP_PRM
10525|APP_IDP
10526|APP_TRUNK1
10527|APP_TRUNK2
10528|APP_LEAF1
10529|APP_LEAF2
10530|APP_IRT
10531|APP_BULK
10532|APP_MFE_NSP
10533|APP_MERIT
10534|APP_DCCP
10535|APP_3PC
10536|APP_IDPR
10537|APP_XTP
10538|APP_DDP
10539|APP_CMTP
10540|APP_TPPP
10541|APP_IL
10542|APP_SDRP
10543|APP_ROUTING
10544|APP_FRAGMENT
10545|APP_IDRP
10546|APP_RSVP
10548|APP_DSR
10549|APP_BNA
10550|APP_INSLP
10551|APP_SWIPE
10552|APP_NHRP
10553|APP_MOBILE
10554|APP_TLSP
10555|APP_SKIP
10556|APP_NONE
10557|APP_DSTOPTS
10558|APP_SHIM6_OL
10559|APP_MIP6
10560|APP_SATEXPAK
10561|APP_KRYPTOLA
10562|APP_RVD
10563|APP_IPPC
10564|APP_SATMON
10565|APP_VISA
10566|APP_IPCV
10567|APP_CPNX
10568|APP_CPHB
10569|APP_WSN
10570|APP_PVP
10571|APP_BRSATMON
10572|APP_SUNND
10573|APP_WBMON
10574|APP_WBEXPAK
10575|APP_OSI
10576|APP_VMTP
10577|APP_SVMTP
10578|APP_VINES
10579|APP_TTP
10580|APP_NSFNETIG
10581|APP_DGP
10582|APP_TCF
10583|APP_SPRITE
10584|APP_LARP
10585|APP_MTP
10586|APP_AX25
10587|APP_IPINIP
10588|APP_MICP
10589|APP_SCCCP
10590|APP_ETHERIP
10591|APP_ENCAP
10592|APP_GMTP
10593|APP_IFMP
10594|APP_PNNI
10595|APP_PIM
10596|APP_ARIS
10597|APP_SCPS
10598|APP_QNX
10599|APP_AN
10600|APP_SNP
10601|APP_COMPAQ
10602|APP_PGM
10603|APP_DDX
10604|APP_IATP
10605|APP_STP
10606|APP_SRP
10607|APP_UTI
10608|APP_SMP
10609|APP_SM
10610|APP_PTP
10611|APP_ISIS
10612|APP_FIRE
10613|APP_CRTP
10614|APP_CRUDP
10615|APP_SSCOPMCE
10616|APP_IPLT
10617|APP_SPS
10618|APP_PIPE
10619|APP_SCTP
10620|APP_FC
10621|APP_MPLS
10622|APP_MANET
10623|APP_HIP
10624|APP_SHIM6
10625|APP_WESP
10626|APP_ROHC
10627|APP_AX4000
10628|APP_NCS
10629|APP_PPTP
10630|APP_IPSEC
10631|APP_VOIP
10632|APP_HTTPS
10633|APP_NNTPS
10634|APP_SMTPS
10635|APP_IMAPS
10636|APP_POP3S
10637|APP_HTTP
10638|APP_SSL
10639|APP_RDP
10640|APP_Simatic_S7
10641|APP_COTP
10650|APP_Modbus
10701|No_Payload
10702|TCP_QueryOnly
10703|TCP_PortClose
10704|TCP_NoPayload
10705|TCP_Unknow


```
//如规则列表更新，请执行下列命令，重新生成列表
for f in `find $THE_ROOT/bin/_ProRule -type f -name '*.Rule'`; do xmllint --xpath '/Application/APPID/text()' $f; echo -n '|'; xmllint --xpath '/Application/Name/text()' $f; echo ;done | sort -n
```


### 格式


```
{
    "type": 206,                        //类型206
    "time": 1586761518,                 //发送时的时间戳,每5分钟发送一次
    "id": 10429,                        //应用测协议id,最多一百种协议
    "pkts": "10"                        //上述协议id，5分钟的总包数
}
```

```
{
    "type": 207,                        //类型207
    "time": 1586760947,                 //发送时的时间戳,每5分钟发送一次
    "port": 45389,                      //TCP 端口号,最多一百个端口
    "pkts": "970"                       //上述协议id5分钟的总包数
}
```

```
{
    "type": 208,                        //类型208
    "time": 1586761518,                 //发送时的时间戳,每5分钟发送一次
    "port": 1633,                       //UDP端口号,最多一百个端口
    "pkts": "142"                       //上述协议id5分钟的总包数
}
```


## 网络拓扑

### 格式


```
{
    "type": 209,                        //类型209
    "time": 1586776449,                 //发送时的时间戳,每5分钟发送一次
    "mac": "c8:e7:f0:6d:33:d8",         //mac地址,最多发送一百条，按收包+发包排序
    "alert": "30",                      //MAC告警数量总量
    "send": "1158303",                  //MAC发包总数
    "recv": "841558",                   //MAC收包总数
    "port": null,                       //开放端口列表(暂时不做)
    "ts_first": 1586776149,             //MAC最早发现时间
    "ts_last": 1586776412               //MAC最晚活动时间
}
```

```
{
    "type": 211,                        //类型211
    "time": 1586773498,                 //发送时的时间戳,每5分钟发送一次，最多50对
    "mac_1": "3c:8a:b0:86:69:a8",       //通联mac1
    "mac_2": "c8:e7:f0:6c:f3:33",       //通联mac2
    "pkt": "1968637",                   //通联包数
    "ts_first": 1586773199,             //通联最早发现时间
    "ts_last": 1586773462               //通联最晚活动时间
}
```

## 线路分析

### 格式

```
{
    "type": 213,                        //类型213
    "time": 1586775551,                 //发送时的时间戳,每30秒钟发送一次
    "total_bytes": "456979762",         //线路分析总字节数byte
    "bps": "121861264",                 //线路分析流量大小bps
    "total_pkts": "558715",             //线路分析总包数
    "pps": 18623,                       //线路分析pps
    "ts_start": 1586775519,             //线路分析启动时间(时间戳)
    "ts_run": 32                        //线路分析运行时间(秒)
}
```

## 离线任务进度

### 格式

```
{
    "type": 223,                        //类型223
    "task_id": "9876",                  //任务ID
    "batch_id": "543210",               //批次ID
    "total": "2",                       //任务pcap文件总数
    "finished": "1",                    //已处理pcap文件数
    "time": 1668346059                  //发送时的时间戳
}
```
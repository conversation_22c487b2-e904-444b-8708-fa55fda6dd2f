# 分模块进行PB留存
## 配置文件路径
/opt/GeekSec/th/bin/conf/{$THE_ID}/plugin_conf.json
## 格式说明
```json
{
    "proto_parse":                                              //协议解析类插件
    {
        "should_log_def": 1,                                    //协议解析类插件默认模式: 0默认不留存    1默认留存
        "plugin": [                                             //协议解析类插件详细配置，详细配置会覆盖默认配置
            {
                "id": 10071,                                    //DNS协议解析插件ID
                "name": "dns",                                  //插件名称
                "should_log": 1                                 //DNS协议解析插件是否留存PB：0不留存    1留存
            },
            {"id": 50,"name": "esp","should_log": 1},           //ESP协议解析插件配置
            {"id": 10650,"name": "modbus","should_log": 1},     //MODBUS协议解析插件配置
            {"id": 10637,"name": "http","should_log": 1},       //HTTP协议解析插件配置
            {"id": 10061,"name": "ssh","should_log": 1},        //SSH协议解析插件配置
            {"id": 10638,"name": "ssl","should_log": 1},        //SSL协议解析插件配置
            {"id": 10639,"name": "rdp","should_log": 1},        //RDP协议解析插件配置
            {"id": 613,"name": "s7","should_log": 1}            //S7协议解析插件配置
        ]
    },
    "full_flow":                                                //全流量处理类插件
    {
        "should_log_def": 1,                                    //全流量处理类插件默认模式: 0默认不留存    1默认留存
        "plugin": [                                             //全流量处理类插件详细配置，详细配置会覆盖默认配置
            {"id": 1120,"name": "session","should_log": 1},     //会话日志插件配置
            {"id": 1122,"name": "mac","should_log": 1}          //MAC通联日志插件配置
        ]
    }
}
```
docker启动命令：
docker run -d -it --privileged --name product_analysis_${TEST_TASKID}_${TEST_BATCHID} \
    -v /usr/sbin/dmidecode:/usr/sbin/dmidecode \
    -v /dev/mem:/dev/mem \
    -v /etc/.serialnumber:/etc/.serialnumber \
    -v /conf/${TEST_TASKID}/${TEST_BATCHID}:/opt/GeekSec/th/bin/conf/0 \
    -v /data/${TEST_TASKID}/${TEST_BATCHID}:/data/${TEST_TASKID}/${TEST_BATCHID} \
    -v /taskpcap/${TEST_TASKID}/${TEST_BATCHID}:/taskpcap/${TEST_TASKID}/${TEST_BATCHID} \
    hb.gs.lan/dev_52/product_analysis:${tag_id}

参数说明：
    变量TEST_TASKID为任务ID，变量TEST_BATCHID为批次ID，变量tag_id是离线探针镜像的TAG，启动时需要根据实际情况进行替换。

    --privileged                                因需要校验主板ID和授权，需要privileged权限
    --name                                      容器名称，仅供参考
    -v /usr/sbin/dmidecode:/usr/sbin/dmidecode  使用跟主机上相同的dmidecode，防止与主机鉴权结果不同
    -v /dev/mem:/dev/mem                        dmidecode依赖的设备
    -v /etc/.serialnumber:/etc/.serialnumber    主机的授权文件
    -v /conf/${TEST_TASKID}/${TEST_BATCHID}:/opt/GeekSec/th/bin/conf/0                      主机上任务和批次对应的配置文件夹映射（主机路径/conf/${TEST_TASKID}/${TEST_BATCHID}仅供参考）
    -v /data/${TEST_TASKID}/${TEST_BATCHID}:/data/${TEST_TASKID}/${TEST_BATCHID}            主机上任务和批次对应的数据输出文件夹映射（主机路径/data/${TEST_TASKID}/${TEST_BATCHID}仅供参考）
    -v /taskpcap/${TEST_TASKID}/${TEST_BATCHID}:/taskpcap/${TEST_TASKID}/${TEST_BATCHID}    待处理的PCAP文件路径，需要根据实际情况映射到容器内（主机路径/taskpcap/${TEST_TASKID}/${TEST_BATCHID}仅供参考）

其他说明：
    file_index.txt是任务文件，启动探针容器前需要生成PCAP文件索引到配置文件夹下。
    task_pcap_read.conf是进度记录文件，启动探针容器前需要新建此文件到配置文件夹下。
    file_index.txt内记录的PCAP文件路径，需要保证在容器内可以正确读取。
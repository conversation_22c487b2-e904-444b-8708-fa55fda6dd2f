<!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>&#x63a8;&#x9001;&#x6570;&#x636e;&#x683c;&#x5f0f;</title>
        <style>
/* From extension vscode.github */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.vscode-dark img[src$=\#gh-light-mode-only],
.vscode-light img[src$=\#gh-dark-mode-only] {
	display: none;
}

/* From extension vscode.markdown-math */
@font-face{font-family:KaTeX_AMS;font-style:normal;font-weight:400;src:url(fonts/KaTeX_AMS-Regular.woff2) format("woff2"),url(fonts/KaTeX_AMS-Regular.woff) format("woff"),url(fonts/KaTeX_AMS-Regular.ttf) format("truetype")}@font-face{font-family:KaTeX_Caligraphic;font-style:normal;font-weight:700;src:url(fonts/KaTeX_Caligraphic-Bold.woff2) format("woff2"),url(fonts/KaTeX_Caligraphic-Bold.woff) format("woff"),url(fonts/KaTeX_Caligraphic-Bold.ttf) format("truetype")}@font-face{font-family:KaTeX_Caligraphic;font-style:normal;font-weight:400;src:url(fonts/KaTeX_Caligraphic-Regular.woff2) format("woff2"),url(fonts/KaTeX_Caligraphic-Regular.woff) format("woff"),url(fonts/KaTeX_Caligraphic-Regular.ttf) format("truetype")}@font-face{font-family:KaTeX_Fraktur;font-style:normal;font-weight:700;src:url(fonts/KaTeX_Fraktur-Bold.woff2) format("woff2"),url(fonts/KaTeX_Fraktur-Bold.woff) format("woff"),url(fonts/KaTeX_Fraktur-Bold.ttf) format("truetype")}@font-face{font-family:KaTeX_Fraktur;font-style:normal;font-weight:400;src:url(fonts/KaTeX_Fraktur-Regular.woff2) format("woff2"),url(fonts/KaTeX_Fraktur-Regular.woff) format("woff"),url(fonts/KaTeX_Fraktur-Regular.ttf) format("truetype")}@font-face{font-family:KaTeX_Main;font-style:normal;font-weight:700;src:url(fonts/KaTeX_Main-Bold.woff2) format("woff2"),url(fonts/KaTeX_Main-Bold.woff) format("woff"),url(fonts/KaTeX_Main-Bold.ttf) format("truetype")}@font-face{font-family:KaTeX_Main;font-style:italic;font-weight:700;src:url(fonts/KaTeX_Main-BoldItalic.woff2) format("woff2"),url(fonts/KaTeX_Main-BoldItalic.woff) format("woff"),url(fonts/KaTeX_Main-BoldItalic.ttf) format("truetype")}@font-face{font-family:KaTeX_Main;font-style:italic;font-weight:400;src:url(fonts/KaTeX_Main-Italic.woff2) format("woff2"),url(fonts/KaTeX_Main-Italic.woff) format("woff"),url(fonts/KaTeX_Main-Italic.ttf) format("truetype")}@font-face{font-family:KaTeX_Main;font-style:normal;font-weight:400;src:url(fonts/KaTeX_Main-Regular.woff2) format("woff2"),url(fonts/KaTeX_Main-Regular.woff) format("woff"),url(fonts/KaTeX_Main-Regular.ttf) format("truetype")}@font-face{font-family:KaTeX_Math;font-style:italic;font-weight:700;src:url(fonts/KaTeX_Math-BoldItalic.woff2) format("woff2"),url(fonts/KaTeX_Math-BoldItalic.woff) format("woff"),url(fonts/KaTeX_Math-BoldItalic.ttf) format("truetype")}@font-face{font-family:KaTeX_Math;font-style:italic;font-weight:400;src:url(fonts/KaTeX_Math-Italic.woff2) format("woff2"),url(fonts/KaTeX_Math-Italic.woff) format("woff"),url(fonts/KaTeX_Math-Italic.ttf) format("truetype")}@font-face{font-family:"KaTeX_SansSerif";font-style:normal;font-weight:700;src:url(fonts/KaTeX_SansSerif-Bold.woff2) format("woff2"),url(fonts/KaTeX_SansSerif-Bold.woff) format("woff"),url(fonts/KaTeX_SansSerif-Bold.ttf) format("truetype")}@font-face{font-family:"KaTeX_SansSerif";font-style:italic;font-weight:400;src:url(fonts/KaTeX_SansSerif-Italic.woff2) format("woff2"),url(fonts/KaTeX_SansSerif-Italic.woff) format("woff"),url(fonts/KaTeX_SansSerif-Italic.ttf) format("truetype")}@font-face{font-family:"KaTeX_SansSerif";font-style:normal;font-weight:400;src:url(fonts/KaTeX_SansSerif-Regular.woff2) format("woff2"),url(fonts/KaTeX_SansSerif-Regular.woff) format("woff"),url(fonts/KaTeX_SansSerif-Regular.ttf) format("truetype")}@font-face{font-family:KaTeX_Script;font-style:normal;font-weight:400;src:url(fonts/KaTeX_Script-Regular.woff2) format("woff2"),url(fonts/KaTeX_Script-Regular.woff) format("woff"),url(fonts/KaTeX_Script-Regular.ttf) format("truetype")}@font-face{font-family:KaTeX_Size1;font-style:normal;font-weight:400;src:url(fonts/KaTeX_Size1-Regular.woff2) format("woff2"),url(fonts/KaTeX_Size1-Regular.woff) format("woff"),url(fonts/KaTeX_Size1-Regular.ttf) format("truetype")}@font-face{font-family:KaTeX_Size2;font-style:normal;font-weight:400;src:url(fonts/KaTeX_Size2-Regular.woff2) format("woff2"),url(fonts/KaTeX_Size2-Regular.woff) format("woff"),url(fonts/KaTeX_Size2-Regular.ttf) format("truetype")}@font-face{font-family:KaTeX_Size3;font-style:normal;font-weight:400;src:url(fonts/KaTeX_Size3-Regular.woff2) format("woff2"),url(fonts/KaTeX_Size3-Regular.woff) format("woff"),url(fonts/KaTeX_Size3-Regular.ttf) format("truetype")}@font-face{font-family:KaTeX_Size4;font-style:normal;font-weight:400;src:url(fonts/KaTeX_Size4-Regular.woff2) format("woff2"),url(fonts/KaTeX_Size4-Regular.woff) format("woff"),url(fonts/KaTeX_Size4-Regular.ttf) format("truetype")}@font-face{font-family:KaTeX_Typewriter;font-style:normal;font-weight:400;src:url(fonts/KaTeX_Typewriter-Regular.woff2) format("woff2"),url(fonts/KaTeX_Typewriter-Regular.woff) format("woff"),url(fonts/KaTeX_Typewriter-Regular.ttf) format("truetype")}.katex{text-rendering:auto;font:normal 1.21em KaTeX_Main,Times New Roman,serif;line-height:1.2;text-indent:0}.katex *{-ms-high-contrast-adjust:none!important;border-color:currentColor}.katex .katex-version:after{content:"0.16.2"}.katex .katex-mathml{clip:rect(1px,1px,1px,1px);border:0;height:1px;overflow:hidden;padding:0;position:absolute;width:1px}.katex .katex-html>.newline{display:block}.katex .base{position:relative;white-space:nowrap;width:-webkit-min-content;width:-moz-min-content;width:min-content}.katex .base,.katex .strut{display:inline-block}.katex .textbf{font-weight:700}.katex .textit{font-style:italic}.katex .textrm{font-family:KaTeX_Main}.katex .textsf{font-family:KaTeX_SansSerif}.katex .texttt{font-family:KaTeX_Typewriter}.katex .mathnormal{font-family:KaTeX_Math;font-style:italic}.katex .mathit{font-family:KaTeX_Main;font-style:italic}.katex .mathrm{font-style:normal}.katex .mathbf{font-family:KaTeX_Main;font-weight:700}.katex .boldsymbol{font-family:KaTeX_Math;font-style:italic;font-weight:700}.katex .amsrm,.katex .mathbb,.katex .textbb{font-family:KaTeX_AMS}.katex .mathcal{font-family:KaTeX_Caligraphic}.katex .mathfrak,.katex .textfrak{font-family:KaTeX_Fraktur}.katex .mathtt{font-family:KaTeX_Typewriter}.katex .mathscr,.katex .textscr{font-family:KaTeX_Script}.katex .mathsf,.katex .textsf{font-family:KaTeX_SansSerif}.katex .mathboldsf,.katex .textboldsf{font-family:KaTeX_SansSerif;font-weight:700}.katex .mathitsf,.katex .textitsf{font-family:KaTeX_SansSerif;font-style:italic}.katex .mainrm{font-family:KaTeX_Main;font-style:normal}.katex .vlist-t{border-collapse:collapse;display:inline-table;table-layout:fixed}.katex .vlist-r{display:table-row}.katex .vlist{display:table-cell;position:relative;vertical-align:bottom}.katex .vlist>span{display:block;height:0;position:relative}.katex .vlist>span>span{display:inline-block}.katex .vlist>span>.pstrut{overflow:hidden;width:0}.katex .vlist-t2{margin-right:-2px}.katex .vlist-s{display:table-cell;font-size:1px;min-width:2px;vertical-align:bottom;width:2px}.katex .vbox{align-items:baseline;display:inline-flex;flex-direction:column}.katex .hbox{width:100%}.katex .hbox,.katex .thinbox{display:inline-flex;flex-direction:row}.katex .thinbox{max-width:0;width:0}.katex .msupsub{text-align:left}.katex .mfrac>span>span{text-align:center}.katex .mfrac .frac-line{border-bottom-style:solid;display:inline-block;width:100%}.katex .hdashline,.katex .hline,.katex .mfrac .frac-line,.katex .overline .overline-line,.katex .rule,.katex .underline .underline-line{min-height:1px}.katex .mspace{display:inline-block}.katex .clap,.katex .llap,.katex .rlap{position:relative;width:0}.katex .clap>.inner,.katex .llap>.inner,.katex .rlap>.inner{position:absolute}.katex .clap>.fix,.katex .llap>.fix,.katex .rlap>.fix{display:inline-block}.katex .llap>.inner{right:0}.katex .clap>.inner,.katex .rlap>.inner{left:0}.katex .clap>.inner>span{margin-left:-50%;margin-right:50%}.katex .rule{border:0 solid;display:inline-block;position:relative}.katex .hline,.katex .overline .overline-line,.katex .underline .underline-line{border-bottom-style:solid;display:inline-block;width:100%}.katex .hdashline{border-bottom-style:dashed;display:inline-block;width:100%}.katex .sqrt>.root{margin-left:.27777778em;margin-right:-.55555556em}.katex .fontsize-ensurer.reset-size1.size1,.katex .sizing.reset-size1.size1{font-size:1em}.katex .fontsize-ensurer.reset-size1.size2,.katex .sizing.reset-size1.size2{font-size:1.2em}.katex .fontsize-ensurer.reset-size1.size3,.katex .sizing.reset-size1.size3{font-size:1.4em}.katex .fontsize-ensurer.reset-size1.size4,.katex .sizing.reset-size1.size4{font-size:1.6em}.katex .fontsize-ensurer.reset-size1.size5,.katex .sizing.reset-size1.size5{font-size:1.8em}.katex .fontsize-ensurer.reset-size1.size6,.katex .sizing.reset-size1.size6{font-size:2em}.katex .fontsize-ensurer.reset-size1.size7,.katex .sizing.reset-size1.size7{font-size:2.4em}.katex .fontsize-ensurer.reset-size1.size8,.katex .sizing.reset-size1.size8{font-size:2.88em}.katex .fontsize-ensurer.reset-size1.size9,.katex .sizing.reset-size1.size9{font-size:3.456em}.katex .fontsize-ensurer.reset-size1.size10,.katex .sizing.reset-size1.size10{font-size:4.148em}.katex .fontsize-ensurer.reset-size1.size11,.katex .sizing.reset-size1.size11{font-size:4.976em}.katex .fontsize-ensurer.reset-size2.size1,.katex .sizing.reset-size2.size1{font-size:.83333333em}.katex .fontsize-ensurer.reset-size2.size2,.katex .sizing.reset-size2.size2{font-size:1em}.katex .fontsize-ensurer.reset-size2.size3,.katex .sizing.reset-size2.size3{font-size:1.16666667em}.katex .fontsize-ensurer.reset-size2.size4,.katex .sizing.reset-size2.size4{font-size:1.33333333em}.katex .fontsize-ensurer.reset-size2.size5,.katex .sizing.reset-size2.size5{font-size:1.5em}.katex .fontsize-ensurer.reset-size2.size6,.katex .sizing.reset-size2.size6{font-size:1.66666667em}.katex .fontsize-ensurer.reset-size2.size7,.katex .sizing.reset-size2.size7{font-size:2em}.katex .fontsize-ensurer.reset-size2.size8,.katex .sizing.reset-size2.size8{font-size:2.4em}.katex .fontsize-ensurer.reset-size2.size9,.katex .sizing.reset-size2.size9{font-size:2.88em}.katex .fontsize-ensurer.reset-size2.size10,.katex .sizing.reset-size2.size10{font-size:3.45666667em}.katex .fontsize-ensurer.reset-size2.size11,.katex .sizing.reset-size2.size11{font-size:4.14666667em}.katex .fontsize-ensurer.reset-size3.size1,.katex .sizing.reset-size3.size1{font-size:.71428571em}.katex .fontsize-ensurer.reset-size3.size2,.katex .sizing.reset-size3.size2{font-size:.85714286em}.katex .fontsize-ensurer.reset-size3.size3,.katex .sizing.reset-size3.size3{font-size:1em}.katex .fontsize-ensurer.reset-size3.size4,.katex .sizing.reset-size3.size4{font-size:1.14285714em}.katex .fontsize-ensurer.reset-size3.size5,.katex .sizing.reset-size3.size5{font-size:1.28571429em}.katex .fontsize-ensurer.reset-size3.size6,.katex .sizing.reset-size3.size6{font-size:1.42857143em}.katex .fontsize-ensurer.reset-size3.size7,.katex .sizing.reset-size3.size7{font-size:1.71428571em}.katex .fontsize-ensurer.reset-size3.size8,.katex .sizing.reset-size3.size8{font-size:2.05714286em}.katex .fontsize-ensurer.reset-size3.size9,.katex .sizing.reset-size3.size9{font-size:2.46857143em}.katex .fontsize-ensurer.reset-size3.size10,.katex .sizing.reset-size3.size10{font-size:2.96285714em}.katex .fontsize-ensurer.reset-size3.size11,.katex .sizing.reset-size3.size11{font-size:3.55428571em}.katex .fontsize-ensurer.reset-size4.size1,.katex .sizing.reset-size4.size1{font-size:.625em}.katex .fontsize-ensurer.reset-size4.size2,.katex .sizing.reset-size4.size2{font-size:.75em}.katex .fontsize-ensurer.reset-size4.size3,.katex .sizing.reset-size4.size3{font-size:.875em}.katex .fontsize-ensurer.reset-size4.size4,.katex .sizing.reset-size4.size4{font-size:1em}.katex .fontsize-ensurer.reset-size4.size5,.katex .sizing.reset-size4.size5{font-size:1.125em}.katex .fontsize-ensurer.reset-size4.size6,.katex .sizing.reset-size4.size6{font-size:1.25em}.katex .fontsize-ensurer.reset-size4.size7,.katex .sizing.reset-size4.size7{font-size:1.5em}.katex .fontsize-ensurer.reset-size4.size8,.katex .sizing.reset-size4.size8{font-size:1.8em}.katex .fontsize-ensurer.reset-size4.size9,.katex .sizing.reset-size4.size9{font-size:2.16em}.katex .fontsize-ensurer.reset-size4.size10,.katex .sizing.reset-size4.size10{font-size:2.5925em}.katex .fontsize-ensurer.reset-size4.size11,.katex .sizing.reset-size4.size11{font-size:3.11em}.katex .fontsize-ensurer.reset-size5.size1,.katex .sizing.reset-size5.size1{font-size:.55555556em}.katex .fontsize-ensurer.reset-size5.size2,.katex .sizing.reset-size5.size2{font-size:.66666667em}.katex .fontsize-ensurer.reset-size5.size3,.katex .sizing.reset-size5.size3{font-size:.77777778em}.katex .fontsize-ensurer.reset-size5.size4,.katex .sizing.reset-size5.size4{font-size:.88888889em}.katex .fontsize-ensurer.reset-size5.size5,.katex .sizing.reset-size5.size5{font-size:1em}.katex .fontsize-ensurer.reset-size5.size6,.katex .sizing.reset-size5.size6{font-size:1.11111111em}.katex .fontsize-ensurer.reset-size5.size7,.katex .sizing.reset-size5.size7{font-size:1.33333333em}.katex .fontsize-ensurer.reset-size5.size8,.katex .sizing.reset-size5.size8{font-size:1.6em}.katex .fontsize-ensurer.reset-size5.size9,.katex .sizing.reset-size5.size9{font-size:1.92em}.katex .fontsize-ensurer.reset-size5.size10,.katex .sizing.reset-size5.size10{font-size:2.30444444em}.katex .fontsize-ensurer.reset-size5.size11,.katex .sizing.reset-size5.size11{font-size:2.76444444em}.katex .fontsize-ensurer.reset-size6.size1,.katex .sizing.reset-size6.size1{font-size:.5em}.katex .fontsize-ensurer.reset-size6.size2,.katex .sizing.reset-size6.size2{font-size:.6em}.katex .fontsize-ensurer.reset-size6.size3,.katex .sizing.reset-size6.size3{font-size:.7em}.katex .fontsize-ensurer.reset-size6.size4,.katex .sizing.reset-size6.size4{font-size:.8em}.katex .fontsize-ensurer.reset-size6.size5,.katex .sizing.reset-size6.size5{font-size:.9em}.katex .fontsize-ensurer.reset-size6.size6,.katex .sizing.reset-size6.size6{font-size:1em}.katex .fontsize-ensurer.reset-size6.size7,.katex .sizing.reset-size6.size7{font-size:1.2em}.katex .fontsize-ensurer.reset-size6.size8,.katex .sizing.reset-size6.size8{font-size:1.44em}.katex .fontsize-ensurer.reset-size6.size9,.katex .sizing.reset-size6.size9{font-size:1.728em}.katex .fontsize-ensurer.reset-size6.size10,.katex .sizing.reset-size6.size10{font-size:2.074em}.katex .fontsize-ensurer.reset-size6.size11,.katex .sizing.reset-size6.size11{font-size:2.488em}.katex .fontsize-ensurer.reset-size7.size1,.katex .sizing.reset-size7.size1{font-size:.41666667em}.katex .fontsize-ensurer.reset-size7.size2,.katex .sizing.reset-size7.size2{font-size:.5em}.katex .fontsize-ensurer.reset-size7.size3,.katex .sizing.reset-size7.size3{font-size:.58333333em}.katex .fontsize-ensurer.reset-size7.size4,.katex .sizing.reset-size7.size4{font-size:.66666667em}.katex .fontsize-ensurer.reset-size7.size5,.katex .sizing.reset-size7.size5{font-size:.75em}.katex .fontsize-ensurer.reset-size7.size6,.katex .sizing.reset-size7.size6{font-size:.83333333em}.katex .fontsize-ensurer.reset-size7.size7,.katex .sizing.reset-size7.size7{font-size:1em}.katex .fontsize-ensurer.reset-size7.size8,.katex .sizing.reset-size7.size8{font-size:1.2em}.katex .fontsize-ensurer.reset-size7.size9,.katex .sizing.reset-size7.size9{font-size:1.44em}.katex .fontsize-ensurer.reset-size7.size10,.katex .sizing.reset-size7.size10{font-size:1.72833333em}.katex .fontsize-ensurer.reset-size7.size11,.katex .sizing.reset-size7.size11{font-size:2.07333333em}.katex .fontsize-ensurer.reset-size8.size1,.katex .sizing.reset-size8.size1{font-size:.34722222em}.katex .fontsize-ensurer.reset-size8.size2,.katex .sizing.reset-size8.size2{font-size:.41666667em}.katex .fontsize-ensurer.reset-size8.size3,.katex .sizing.reset-size8.size3{font-size:.48611111em}.katex .fontsize-ensurer.reset-size8.size4,.katex .sizing.reset-size8.size4{font-size:.55555556em}.katex .fontsize-ensurer.reset-size8.size5,.katex .sizing.reset-size8.size5{font-size:.625em}.katex .fontsize-ensurer.reset-size8.size6,.katex .sizing.reset-size8.size6{font-size:.69444444em}.katex .fontsize-ensurer.reset-size8.size7,.katex .sizing.reset-size8.size7{font-size:.83333333em}.katex .fontsize-ensurer.reset-size8.size8,.katex .sizing.reset-size8.size8{font-size:1em}.katex .fontsize-ensurer.reset-size8.size9,.katex .sizing.reset-size8.size9{font-size:1.2em}.katex .fontsize-ensurer.reset-size8.size10,.katex .sizing.reset-size8.size10{font-size:1.44027778em}.katex .fontsize-ensurer.reset-size8.size11,.katex .sizing.reset-size8.size11{font-size:1.72777778em}.katex .fontsize-ensurer.reset-size9.size1,.katex .sizing.reset-size9.size1{font-size:.28935185em}.katex .fontsize-ensurer.reset-size9.size2,.katex .sizing.reset-size9.size2{font-size:.34722222em}.katex .fontsize-ensurer.reset-size9.size3,.katex .sizing.reset-size9.size3{font-size:.40509259em}.katex .fontsize-ensurer.reset-size9.size4,.katex .sizing.reset-size9.size4{font-size:.46296296em}.katex .fontsize-ensurer.reset-size9.size5,.katex .sizing.reset-size9.size5{font-size:.52083333em}.katex .fontsize-ensurer.reset-size9.size6,.katex .sizing.reset-size9.size6{font-size:.5787037em}.katex .fontsize-ensurer.reset-size9.size7,.katex .sizing.reset-size9.size7{font-size:.69444444em}.katex .fontsize-ensurer.reset-size9.size8,.katex .sizing.reset-size9.size8{font-size:.83333333em}.katex .fontsize-ensurer.reset-size9.size9,.katex .sizing.reset-size9.size9{font-size:1em}.katex .fontsize-ensurer.reset-size9.size10,.katex .sizing.reset-size9.size10{font-size:1.20023148em}.katex .fontsize-ensurer.reset-size9.size11,.katex .sizing.reset-size9.size11{font-size:1.43981481em}.katex .fontsize-ensurer.reset-size10.size1,.katex .sizing.reset-size10.size1{font-size:.24108004em}.katex .fontsize-ensurer.reset-size10.size2,.katex .sizing.reset-size10.size2{font-size:.28929605em}.katex .fontsize-ensurer.reset-size10.size3,.katex .sizing.reset-size10.size3{font-size:.33751205em}.katex .fontsize-ensurer.reset-size10.size4,.katex .sizing.reset-size10.size4{font-size:.38572806em}.katex .fontsize-ensurer.reset-size10.size5,.katex .sizing.reset-size10.size5{font-size:.43394407em}.katex .fontsize-ensurer.reset-size10.size6,.katex .sizing.reset-size10.size6{font-size:.48216008em}.katex .fontsize-ensurer.reset-size10.size7,.katex .sizing.reset-size10.size7{font-size:.57859209em}.katex .fontsize-ensurer.reset-size10.size8,.katex .sizing.reset-size10.size8{font-size:.69431051em}.katex .fontsize-ensurer.reset-size10.size9,.katex .sizing.reset-size10.size9{font-size:.83317261em}.katex .fontsize-ensurer.reset-size10.size10,.katex .sizing.reset-size10.size10{font-size:1em}.katex .fontsize-ensurer.reset-size10.size11,.katex .sizing.reset-size10.size11{font-size:1.19961427em}.katex .fontsize-ensurer.reset-size11.size1,.katex .sizing.reset-size11.size1{font-size:.20096463em}.katex .fontsize-ensurer.reset-size11.size2,.katex .sizing.reset-size11.size2{font-size:.24115756em}.katex .fontsize-ensurer.reset-size11.size3,.katex .sizing.reset-size11.size3{font-size:.28135048em}.katex .fontsize-ensurer.reset-size11.size4,.katex .sizing.reset-size11.size4{font-size:.32154341em}.katex .fontsize-ensurer.reset-size11.size5,.katex .sizing.reset-size11.size5{font-size:.36173633em}.katex .fontsize-ensurer.reset-size11.size6,.katex .sizing.reset-size11.size6{font-size:.40192926em}.katex .fontsize-ensurer.reset-size11.size7,.katex .sizing.reset-size11.size7{font-size:.48231511em}.katex .fontsize-ensurer.reset-size11.size8,.katex .sizing.reset-size11.size8{font-size:.57877814em}.katex .fontsize-ensurer.reset-size11.size9,.katex .sizing.reset-size11.size9{font-size:.69453376em}.katex .fontsize-ensurer.reset-size11.size10,.katex .sizing.reset-size11.size10{font-size:.83360129em}.katex .fontsize-ensurer.reset-size11.size11,.katex .sizing.reset-size11.size11{font-size:1em}.katex .delimsizing.size1{font-family:KaTeX_Size1}.katex .delimsizing.size2{font-family:KaTeX_Size2}.katex .delimsizing.size3{font-family:KaTeX_Size3}.katex .delimsizing.size4{font-family:KaTeX_Size4}.katex .delimsizing.mult .delim-size1>span{font-family:KaTeX_Size1}.katex .delimsizing.mult .delim-size4>span{font-family:KaTeX_Size4}.katex .nulldelimiter{display:inline-block;width:.12em}.katex .delimcenter,.katex .op-symbol{position:relative}.katex .op-symbol.small-op{font-family:KaTeX_Size1}.katex .op-symbol.large-op{font-family:KaTeX_Size2}.katex .accent>.vlist-t,.katex .op-limits>.vlist-t{text-align:center}.katex .accent .accent-body{position:relative}.katex .accent .accent-body:not(.accent-full){width:0}.katex .overlay{display:block}.katex .mtable .vertical-separator{display:inline-block;min-width:1px}.katex .mtable .arraycolsep{display:inline-block}.katex .mtable .col-align-c>.vlist-t{text-align:center}.katex .mtable .col-align-l>.vlist-t{text-align:left}.katex .mtable .col-align-r>.vlist-t{text-align:right}.katex .svg-align{text-align:left}.katex svg{fill:currentColor;stroke:currentColor;fill-rule:nonzero;fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;display:block;height:inherit;position:absolute;width:100%}.katex svg path{stroke:none}.katex img{border-style:none;max-height:none;max-width:none;min-height:0;min-width:0}.katex .stretchy{display:block;overflow:hidden;position:relative;width:100%}.katex .stretchy:after,.katex .stretchy:before{content:""}.katex .hide-tail{overflow:hidden;position:relative;width:100%}.katex .halfarrow-left{left:0;overflow:hidden;position:absolute;width:50.2%}.katex .halfarrow-right{overflow:hidden;position:absolute;right:0;width:50.2%}.katex .brace-left{left:0;overflow:hidden;position:absolute;width:25.1%}.katex .brace-center{left:25%;overflow:hidden;position:absolute;width:50%}.katex .brace-right{overflow:hidden;position:absolute;right:0;width:25.1%}.katex .x-arrow-pad{padding:0 .5em}.katex .cd-arrow-pad{padding:0 .55556em 0 .27778em}.katex .mover,.katex .munder,.katex .x-arrow{text-align:center}.katex .boxpad{padding:0 .3em}.katex .fbox,.katex .fcolorbox{border:.04em solid;box-sizing:border-box}.katex .cancel-pad{padding:0 .2em}.katex .cancel-lap{margin-left:-.2em;margin-right:-.2em}.katex .sout{border-bottom-style:solid;border-bottom-width:.08em}.katex .angl{border-right:.049em solid;border-top:.049em solid;box-sizing:border-box;margin-right:.03889em}.katex .anglpad{padding:0 .03889em}.katex .eqn-num:before{content:"(" counter(katexEqnNo) ")";counter-increment:katexEqnNo}.katex .mml-eqn-num:before{content:"(" counter(mmlEqnNo) ")";counter-increment:mmlEqnNo}.katex .mtr-glue{width:50%}.katex .cd-vert-arrow{display:inline-block;position:relative}.katex .cd-label-left{display:inline-block;position:absolute;right:calc(50% + .3em);text-align:left}.katex .cd-label-right{display:inline-block;left:calc(50% + .3em);position:absolute;text-align:right}.katex-display{display:block;margin:1em 0;text-align:center}.katex-display>.katex{display:block;text-align:center;white-space:nowrap}.katex-display>.katex>.katex-html{display:block;position:relative}.katex-display>.katex>.katex-html>.tag{position:absolute;right:0}.katex-display.leqno>.katex>.katex-html>.tag{left:0;right:auto}.katex-display.fleqn>.katex{padding-left:2em;text-align:left}body{counter-reset:katexEqnNo mmlEqnNo}

/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.katex-error {
	color: var(--vscode-editorError-foreground);
}

</style>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.10.2/dist/katex.min.css" integrity="sha384-yFRtMMDnQtDRO8rLpMIKrtPCD5jdktao2TV19YiZYWMDkUR5GQZR/NOVTdquEx1j" crossorigin="anonymous">
<link href="https://cdn.jsdelivr.net/npm/katex-copytex@latest/dist/katex-copytex.min.css" rel="stylesheet" type="text/css">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/Microsoft/vscode/extensions/markdown-language-features/media/markdown.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/Microsoft/vscode/extensions/markdown-language-features/media/highlight.css">
<style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe WPC', 'Segoe UI', system-ui, 'Ubuntu', 'Droid Sans', sans-serif;
                font-size: 14px;
                line-height: 1.6;
            }
        </style>
        <style>
.task-list-item { list-style-type: none; } .task-list-item-checkbox { margin-left: -20px; vertical-align: middle; }
</style>
        
    </head>
    <body class="vscode-body vscode-light">
        <h1 id="推送数据格式">推送数据格式</h1>
<h2 id="规则命中量">规则命中量</h2>
<h3 id="格式">格式</h3>
<pre><code><code><div>
{
    &quot;type&quot;: 201,                        //类型201
    &quot;time&quot;: 1586761248,                 //发送时间戳,命中规则30秒钟发送一次
    &quot;rule_id&quot;: 35001,                   //命中规则ID
    &quot;bytes_30s&quot;: &quot;698221293&quot;,           //30s命中总字节量(未作多线程同步，数据不精准)
    &quot;packets_30s&quot;: 807243               //30s命中总包数(未作多线程同步，数据不精准)
}
</div></code></code></pre>
<h2 id="流量态势">流量态势</h2>
<h3 id="格式-1">格式</h3>
<pre><code><code><div>{
    &quot;type&quot;: 202,                        //类型202
    &quot;time&quot;: 1586761338,                 //发送时间戳,30秒钟发送一次
    &quot;bps&quot;: &quot;96714280&quot;,                  //当前流量bps
    &quot;bps_in&quot;: &quot;0&quot;,                      //当前流量bps(进：包的目的IP为内网)
    &quot;bps_out&quot;: &quot;96714280&quot;,              //当前流量bps(出：包的目的IP不是内网)
    &quot;pps&quot;: 14834,                       //当前流量pps
    &quot;pps_in&quot;: 0,                        //当前流量pps(进：包的目的IP为内网)
    &quot;pps_out&quot;: 14834,                   //当前流量pps(出：包的目的IP不是内网)
    &quot;conn&quot;: 77652,                      //当前并发连接数
    &quot;conn_in&quot;: 0,                       //当前并发连接数(进：会话服务端IP为内网)
    &quot;conn_out&quot;: 77652,                  //当前并发连接数(出：会话服务端IP不是内网)
    &quot;pps_ipv4&quot;: 14834,                  //IPV4包pps
    &quot;pps_ipv6&quot;: 0,                      //IPV6包pps
    &quot;pps_notip&quot;: 0,                     //其他包pps(非IP包)
    &quot;pps_tcp&quot;: 9534,                    //TCP包pps
    &quot;pps_udp&quot;: 3441,                    //UDP包pps
    &quot;pps_ipother&quot;: 1859                 //其他包pps(IP包,非TCP,非UDP)
}
</div></code></code></pre>
<pre><code><code><div>{
    &quot;type&quot;: 217,                        //类型217
    &quot;time&quot;: 1586761338,                 //发送时间戳,30秒钟发送一次
    &quot;pkts&quot;: &quot;522305&quot;,                   //探针启动后处理总包数
    &quot;bytes&quot;: &quot;422725112&quot;                //探针启动后处理总字节数
}
</div></code></code></pre>
<pre><code><code><div>{
    &quot;type&quot;: 220,                        //类型220
    &quot;task_id&quot;: &quot;1&quot;,                     //任务ID
    &quot;batch_id&quot;: &quot;100002&quot;,               //批次ID
    &quot;device_id&quot;: &quot;2258430183&quot;,          //设备号
    &quot;time&quot;: 1652849469,                 //发送时间戳,3秒钟发送一次
    &quot;portid&quot;: 0,                        //网口ID
    &quot;pci&quot;: &quot;0000:04:00.1&quot;,              //网口PCI地址
    &quot;mac&quot;: &quot;a0:36:9f:0b:06:31&quot;,         //网口MAC地址
    &quot;name&quot;: &quot;2&quot;,                        //网口名
    &quot;bps&quot;: &quot;920234493&quot;,                 //当前网口bps
    &quot;pps&quot;: &quot;255561&quot;,                    //当前网口pps
    &quot;dev_drop&quot;: &quot;1.793%&quot;,               //硬件丢包率
    &quot;ring_drop&quot;: &quot;0.000%&quot;,              //软件丢包率
    &quot;not_lost_pkts&quot;: &quot;766685&quot;,          //3秒未丢包数
    &quot;lost_pkts&quot;: &quot;14000&quot;,               //3秒丢包数
    &quot;handle_bps&quot;: &quot;920790056&quot;,          //当前处理性能bps
    &quot;handle_pps&quot;: &quot;255374&quot;              //当前处理性能pps
}
</div></code></code></pre>
<pre><code><code><div>{
    &quot;type&quot;: 222,                        //类型222
    &quot;task_id&quot;: &quot;1&quot;,                     //任务ID
    &quot;batch_id&quot;: &quot;100002&quot;,               //批次ID
    &quot;device_id&quot;: &quot;2258430183&quot;,          //设备号
    &quot;time&quot;: 1652849490,                 /发送时间戳,30秒钟发送一次
    &quot;not_lost_pkts&quot;: &quot;7381046&quot;,         //30秒未丢包数
    &quot;lost_pkts&quot;: &quot;144441&quot;               //30秒丢包数
}

</div></code></code></pre>
<h2 id="防御统计数据格式">防御统计数据格式</h2>
<h3 id="防御类型列表">防御类型列表</h3>
<table>
<thead>
<tr>
<th>类型id</th>
<th>防御类型</th>
</tr>
</thead>
<tbody>
<tr>
<td>1</td>
<td>非法Mac</td>
</tr>
<tr>
<td>2</td>
<td>非法IP范围</td>
</tr>
<tr>
<td>3</td>
<td>ARP非法Mac</td>
</tr>
<tr>
<td>4</td>
<td>ARP劫持</td>
</tr>
<tr>
<td>5</td>
<td>ARP Cache 缓存攻击</td>
</tr>
<tr>
<td>6</td>
<td>端口扫描</td>
</tr>
<tr>
<td>7</td>
<td>异常服务</td>
</tr>
<tr>
<td>8</td>
<td>异常协议</td>
</tr>
<tr>
<td>9</td>
<td>隐藏信道</td>
</tr>
<tr>
<td>10</td>
<td>TCP SYN DDOS</td>
</tr>
<tr>
<td>11</td>
<td>UDP DDOS</td>
</tr>
<tr>
<td>12</td>
<td>ICMP DDOS</td>
</tr>
<tr>
<td>13</td>
<td>DNS DDOS</td>
</tr>
<tr>
<td>14</td>
<td>Ping of Death</td>
</tr>
<tr>
<td>15</td>
<td>单播</td>
</tr>
<tr>
<td>16</td>
<td>多播</td>
</tr>
<tr>
<td>17</td>
<td>广播</td>
</tr>
<tr>
<td>18</td>
<td>ARP 风暴</td>
</tr>
<tr>
<td>19</td>
<td>LLDP 风暴</td>
</tr>
<tr>
<td>20</td>
<td>IP分片  风暴</td>
</tr>
<tr>
<td>21</td>
<td>IP Checksum  风暴</td>
</tr>
<tr>
<td>22</td>
<td>IGMP  风暴</td>
</tr>
<tr>
<td>23</td>
<td>TCP FIN/RST 标识  风暴</td>
</tr>
<tr>
<td>24</td>
<td>网络中出现新Mac</td>
</tr>
<tr>
<td>25</td>
<td>虚假Mac</td>
</tr>
<tr>
<td>26</td>
<td>内网出现新的IP网段</td>
</tr>
<tr>
<td>27</td>
<td>虚假IP</td>
</tr>
<tr>
<td>28</td>
<td>无IP</td>
</tr>
<tr>
<td>29</td>
<td>IP异常分片</td>
</tr>
<tr>
<td>30</td>
<td>Ping To Death</td>
</tr>
<tr>
<td>31</td>
<td>UDP Checksum  风暴</td>
</tr>
<tr>
<td>32</td>
<td>Mac地址为零</td>
</tr>
<tr>
<td>33</td>
<td>非法Mac，ARP Sender 中的Mac与合法Mac范围不同</td>
</tr>
</tbody>
</table>
<h3 id="格式-2">格式</h3>
<pre><code><code><div>{
    &quot;type&quot;: 203,                        //类型203
    &quot;time&quot;: 1586760947,                 //发送时间戳,5分钟发送一次
    &quot;id&quot;: 10,                           //防御类型ID
    &quot;bytes&quot;: &quot;89594523&quot;,                //5分钟内上述id命中丢弃的总字节数
    &quot;batch_id&quot;: 0                       //批次号
}
</div></code></code></pre>
<h2 id="过滤系统统计数据格式">过滤系统统计数据格式</h2>
<h3 id="筛选规则下发">筛选规则下发</h3>
<p>筛选规则下发时，需要在原先的规则基础上添加规则ID</p>
<pre><code><code><div>{
    &quot;ID&quot;: 12345,                        //新添加，与数据库id保持一致
    &quot;IP&quot;: &quot;*******&quot;,
    &quot;IPPro&quot;: [1, 2, 3],
    &quot;IPPro_Type&quot;: &quot;Select&quot;,
    &quot;TCPPort&quot;: [80, 81],
    &quot;TCPPort_Type&quot;: &quot;Select&quot;,
    &quot;UDPPort&quot;: [9000, 9001],
    &quot;UDPPort_Type&quot;: &quot;Select&quot;
}
</div></code></code></pre>
<h3 id="格式-3">格式</h3>
<pre><code><code><div>{
    &quot;type&quot;: 204,                        //类型204
    &quot;time&quot;: 1586763657,                 //发送时间戳,5分钟发送一次
    &quot;id&quot;: 12345,                        //过滤规则id
    &quot;bytes&quot;: &quot;601507130&quot;                //5分钟内上述id命中的总字节数
}
</div></code></code></pre>
<h2 id="数据态势">数据态势</h2>
<h3 id="格式-4">格式</h3>
<pre><code><code><div>{
    &quot;type&quot;: 205,                        //类型205
    &quot;time&quot;: 1586768429,                 //发送时间戳,30秒钟发送一次
    &quot;bps&quot;: &quot;100056000&quot;,                 //当前流量bps
    &quot;pps&quot;: 15049,                       //当前流量pps
    &quot;conn&quot;: 0,                          //当前并发连接数
    &quot;bps_filter_in&quot;: &quot;100056000&quot;,       //进入过滤模块流量bps
    &quot;bps_filter_drop&quot;: &quot;32004640&quot;,      //过滤模块丢弃流量bps
    &quot;bps_filter_out&quot;: &quot;68051352&quot;,       //出过滤模块流量bps
    &quot;bps_defense_in&quot;: &quot;68051352&quot;,       //进入防御模块流量bps
    &quot;bps_defense_drop&quot;: &quot;68051352&quot;,     //防御模块丢弃流量bps
    &quot;bps_defense_out&quot;: &quot;0&quot;,             //出防御模块流量bps
    &quot;bps_rule_hit&quot;: &quot;0&quot;,                //规则命中流量bps
    &quot;bytes_30s_rule_hit&quot;: &quot;0&quot;,          //30s内命中规则总量byte
    &quot;pkts_30s_rule_hit&quot;: 0,             //30s内命中规则总包数
    &quot;bytes_rule_hit_all&quot;: &quot;0&quot;,          //历史命中规则总量byte
    &quot;pkts_rule_hit_all&quot;: &quot;0&quot;            //历史命中规则总包数
}
</div></code></code></pre>
<pre><code><code><div>{
    &quot;type&quot;: 210,                        //类型210
    &quot;time&quot;: 1586768399,                 //发送时间戳,30秒钟发送一次
    &quot;pcap_bytes_all&quot;: &quot;2306216584&quot;,     //历史PCAP留存总量byte
    &quot;pcap_pkts_all&quot;: &quot;2687229&quot;,         //历史PCAP留存总包数
    &quot;pcap_bytes_30s&quot;: &quot;263865778&quot;,      //30s内PCAP留存总量byte
    &quot;pcap_pkts_30s&quot;: 299450,            //30s内PCAP留存总包数
    &quot;pcap_bps&quot;: &quot;70364200&quot;              //PCAP留存bps
}
</div></code></code></pre>
<pre><code><code><div>{
    &quot;type&quot;: 221,                        //类型221
    &quot;time&quot;: 1625220066,                 //发送时间戳,30秒钟发送一次
    &quot;drop_pcap_bytes_all&quot;: &quot;14856243&quot;,  //历史PCAP丢弃（白名单规则）总量byte
    &quot;drop_pcap_pkts_all&quot;: &quot;19159&quot;,      //历史PCAP丢弃（白名单规则）总包数
    &quot;drop_pcap_bytes_30s&quot;: &quot;0&quot;,         //30s内PCAP丢弃（白名单规则）总量byte
    &quot;drop_pcap_pkts_30s&quot;: 0,            //30s内PCAP丢弃（白名单规则）总包数
    &quot;drop_pcap_bps&quot;: &quot;0&quot;,               //PCAP丢弃（白名单规则）bps
    &quot;device_id&quot;: &quot;3486463558&quot;
}
</div></code></code></pre>
<pre><code><code><div>{
    &quot;type&quot;: 212,                        //类型212
    &quot;time&quot;: 1586771853,                 //发送时间戳,30秒钟发送一次
    &quot;all_pb_num&quot;: &quot;45736&quot;,              //历史日志提取总条目数
    &quot;all_pb_bytes&quot;: &quot;32936627&quot;,         //历史日志提取总字节数byte
    &quot;num_30s_pb&quot;: 30079,                //30s内日志提取条目数
    &quot;bytes_30s_pb&quot;: &quot;24732113&quot;,         //30s内日志提取字节数byte
    &quot;pb_ps&quot;: 1002                       //日志提取速度(条目数每秒)
}
</div></code></code></pre>
<pre><code><code><div>{
    &quot;type&quot;: 216,                        //类型216
    &quot;time&quot;: 1586771853,                 //发送时间戳,30秒钟发送一次
    &quot;pb_type&quot;: 1,                       //日志类型
    &quot;pb_num&quot;: &quot;45736&quot;,                  //此类型日志提取总条目数
    &quot;pb_bytes&quot;: &quot;32936627&quot;              //此类型日志提取总字节数byte
}
</div></code></code></pre>
<h4 id="pb类型表">PB类型表</h4>
<pre><code><code><div>{
    &quot;4&quot;: &quot;dns&quot;,
    &quot;6&quot;: &quot;noip&quot;,
    &quot;7&quot;: &quot;mac&quot;,
    &quot;28&quot;: &quot;ssh&quot;,
    &quot;29&quot;: &quot;ssl&quot;,
    &quot;30&quot;: &quot;session&quot;,
    &quot;41&quot;: &quot;s7&quot;,
    &quot;42&quot;: &quot;modbus&quot;,
    &quot;61&quot;: &quot;esp&quot;,
    &quot;80&quot;: &quot;http&quot;,
    &quot;237&quot;: &quot;dns&quot;,
    &quot;639&quot;: &quot;rdp&quot;,
    &quot;799&quot;: &quot;dns&quot;
}
</div></code></code></pre>
<h2 id="协议态势">协议态势</h2>
<h3 id="协议id与协议">协议id与协议</h3>
<table>
<thead>
<tr>
<th>协议id</th>
<th>协议名称</th>
</tr>
</thead>
<tbody>
<tr>
<td>10000</td>
<td>UNKNOWN</td>
</tr>
<tr>
<td>10001</td>
<td>APP_FTP</td>
</tr>
<tr>
<td>10012</td>
<td>APP_NNTP</td>
</tr>
<tr>
<td>10022</td>
<td>APP_WakeOnLan</td>
</tr>
<tr>
<td>10026</td>
<td>APP_RIP</td>
</tr>
<tr>
<td>10027</td>
<td>APP_IGRP</td>
</tr>
<tr>
<td>10028</td>
<td>APP_EIGRP</td>
</tr>
<tr>
<td>10029</td>
<td>APP_OSPF</td>
</tr>
<tr>
<td>10030</td>
<td>APP_IGP</td>
</tr>
<tr>
<td>10031</td>
<td>APP_EGP</td>
</tr>
<tr>
<td>10032</td>
<td>APP_BGP</td>
</tr>
<tr>
<td>10033</td>
<td>APP_VRRP</td>
</tr>
<tr>
<td>10034</td>
<td>APP_SNMP</td>
</tr>
<tr>
<td>10035</td>
<td>APP_DHCP</td>
</tr>
<tr>
<td>10037</td>
<td>APP_DHCPv6</td>
</tr>
<tr>
<td>10040</td>
<td>APP_ICMP_v4</td>
</tr>
<tr>
<td>10042</td>
<td>APP_ICMP_v6</td>
</tr>
<tr>
<td>10044</td>
<td>APP_ARP</td>
</tr>
<tr>
<td>10046</td>
<td>APP_NBNS</td>
</tr>
<tr>
<td>10049</td>
<td>APP_NBDGM</td>
</tr>
<tr>
<td>10056</td>
<td>APP_NBSS</td>
</tr>
<tr>
<td>10061</td>
<td>APP_SSH</td>
</tr>
<tr>
<td>10066</td>
<td>APP_Telnet</td>
</tr>
<tr>
<td>10067</td>
<td>APP_MAIL_SMTP</td>
</tr>
<tr>
<td>10069</td>
<td>APP_WHOISDAS</td>
</tr>
<tr>
<td>10070</td>
<td>APP_Tacacs+</td>
</tr>
<tr>
<td>10071</td>
<td>APP_DNS</td>
</tr>
<tr>
<td>10074</td>
<td>APP_Il_DNS</td>
</tr>
<tr>
<td>10093</td>
<td>APP_KERBEROS</td>
</tr>
<tr>
<td>10101</td>
<td>APP_MAIL_POP</td>
</tr>
<tr>
<td>10103</td>
<td>APP_DCERPC</td>
</tr>
<tr>
<td>10105</td>
<td>APP_MAIL_IMAP</td>
</tr>
<tr>
<td>10113</td>
<td>APP_LDAP</td>
</tr>
<tr>
<td>10120</td>
<td>APP_CISCOVPN</td>
</tr>
<tr>
<td>10123</td>
<td>APP_SMB</td>
</tr>
<tr>
<td>10127</td>
<td>APP_SYSLOG</td>
</tr>
<tr>
<td>10136</td>
<td>APP_RTSP</td>
</tr>
<tr>
<td>10138</td>
<td>APP_VMWARE</td>
</tr>
<tr>
<td>10145</td>
<td>APP_SOCKS5</td>
</tr>
<tr>
<td>10146</td>
<td>APP_OPENVPN</td>
</tr>
<tr>
<td>10148</td>
<td>APP_TDS</td>
</tr>
<tr>
<td>10149</td>
<td>APP_MSSQL</td>
</tr>
<tr>
<td>10152</td>
<td>APP_NDPI_CITRIX</td>
</tr>
<tr>
<td>10154</td>
<td>APP_H323</td>
</tr>
<tr>
<td>10158</td>
<td>APP_MSN</td>
</tr>
<tr>
<td>10159</td>
<td>APP_RTMP</td>
</tr>
<tr>
<td>10161</td>
<td>APP_SKINNY</td>
</tr>
<tr>
<td>10162</td>
<td>APP_NFS</td>
</tr>
<tr>
<td>10165</td>
<td>APP_MYSQL</td>
</tr>
<tr>
<td>10171</td>
<td>APP_IAX</td>
</tr>
<tr>
<td>10172</td>
<td>APP_RADMIN</td>
</tr>
<tr>
<td>10180</td>
<td>APP_POSTGRES</td>
</tr>
<tr>
<td>10184</td>
<td>APP_VNC</td>
</tr>
<tr>
<td>10188</td>
<td>APP_TEAMVIEWER</td>
</tr>
<tr>
<td>10189</td>
<td>APP_XDMCP</td>
</tr>
<tr>
<td>10201</td>
<td>APP_ANCP</td>
</tr>
<tr>
<td>10205</td>
<td>APP_TOR</td>
</tr>
<tr>
<td>10208</td>
<td>APP_TEAMSPEAK</td>
</tr>
<tr>
<td>10209</td>
<td>APP_ENIP</td>
</tr>
<tr>
<td>10213</td>
<td>APP_SOPCAST</td>
</tr>
<tr>
<td>10215</td>
<td>APP_ORACLE_TNS</td>
</tr>
<tr>
<td>10216</td>
<td>APP_GUILDWARS</td>
</tr>
<tr>
<td>10217</td>
<td>APP_DOFUS</td>
</tr>
<tr>
<td>10221</td>
<td>APP_NDPI_EDONKEY</td>
</tr>
<tr>
<td>10222</td>
<td>APP_FIESTA</td>
</tr>
<tr>
<td>10226</td>
<td>APP_FILETOPIA</td>
</tr>
<tr>
<td>10231</td>
<td>APP_STEAM</td>
</tr>
<tr>
<td>10232</td>
<td>APP_SOULSEEK</td>
</tr>
<tr>
<td>10233</td>
<td>APP_FCIP</td>
</tr>
<tr>
<td>10234</td>
<td>APP_TVANTS</td>
</tr>
<tr>
<td>10236</td>
<td>APP_SOCKS4</td>
</tr>
<tr>
<td>10237</td>
<td>APP_PANDO</td>
</tr>
<tr>
<td>10240</td>
<td>APP_CORBA</td>
</tr>
<tr>
<td>10243</td>
<td>APP_MANOLITO</td>
</tr>
<tr>
<td>10244</td>
<td>APP_PVFS</td>
</tr>
<tr>
<td>10245</td>
<td>APP_IMESH</td>
</tr>
<tr>
<td>10381</td>
<td>APP_NETMAN</td>
</tr>
<tr>
<td>10383</td>
<td>APP_ZATTOO</td>
</tr>
<tr>
<td>10386</td>
<td>APP_ZMQ</td>
</tr>
<tr>
<td>10387</td>
<td>APP_MAPLESTORY</td>
</tr>
<tr>
<td>10388</td>
<td>APP_STUN</td>
</tr>
<tr>
<td>10391</td>
<td>APP_AFP</td>
</tr>
<tr>
<td>10392</td>
<td>APP_APPLEJUICE</td>
</tr>
<tr>
<td>10393</td>
<td>APP_NDPI_BITBORRENT</td>
</tr>
<tr>
<td>10394</td>
<td>APP_SPOTIFY</td>
</tr>
<tr>
<td>10395</td>
<td>APP_TVUPLAYER</td>
</tr>
<tr>
<td>10396</td>
<td>APP_OSCAR</td>
</tr>
<tr>
<td>10397</td>
<td>APP_RSYNC</td>
</tr>
<tr>
<td>10399</td>
<td>APP_PPSTREAM</td>
</tr>
<tr>
<td>10401</td>
<td>APP_POPO</td>
</tr>
<tr>
<td>10402</td>
<td>APP_SHOUTCAST</td>
</tr>
<tr>
<td>10403</td>
<td>APP_STEALTHNET</td>
</tr>
<tr>
<td>10404</td>
<td>APP_LOTUS_NOTES</td>
</tr>
<tr>
<td>10405</td>
<td>APP_SOCRATES</td>
</tr>
<tr>
<td>10406</td>
<td>APP_HTTP_ACTIVESYNC</td>
</tr>
<tr>
<td>10409</td>
<td>APP_MMS</td>
</tr>
<tr>
<td>10413</td>
<td>APP_NTP</td>
</tr>
<tr>
<td>10419</td>
<td>APP_NETMAN_NEGOTIATION</td>
</tr>
<tr>
<td>10429</td>
<td>APP_L2TP</td>
</tr>
<tr>
<td>10432</td>
<td>APP_RADIUS</td>
</tr>
<tr>
<td>10435</td>
<td>APP_SSDP</td>
</tr>
<tr>
<td>10436</td>
<td>APP_HSRP</td>
</tr>
<tr>
<td>10438</td>
<td>APP_NETFLOW</td>
</tr>
<tr>
<td>10439</td>
<td>APP_GTP</td>
</tr>
<tr>
<td>10440</td>
<td>APP_GTP_MANAGER</td>
</tr>
<tr>
<td>10443</td>
<td>APP_IPMessage</td>
</tr>
<tr>
<td>10444</td>
<td>APP_MEGACO</td>
</tr>
<tr>
<td>10445</td>
<td>APP_XBOX</td>
</tr>
<tr>
<td>10449</td>
<td>APP_Teredo</td>
</tr>
<tr>
<td>10454</td>
<td>APP_MDNS</td>
</tr>
<tr>
<td>10456</td>
<td>APP_LLMNR</td>
</tr>
<tr>
<td>10457</td>
<td>APP_PCANYWHERE</td>
</tr>
<tr>
<td>10458</td>
<td>APP_SFLOW</td>
</tr>
<tr>
<td>10459</td>
<td>APP_Tencent_OICQ</td>
</tr>
<tr>
<td>10460</td>
<td>APP_Tencent_MayBe_OICQ</td>
</tr>
<tr>
<td>10464</td>
<td>APP_Tencent_QQMail</td>
</tr>
<tr>
<td>10465</td>
<td>APP_COLLECTD</td>
</tr>
<tr>
<td>10466</td>
<td>APP_QUAKE</td>
</tr>
<tr>
<td>10473</td>
<td>APP_NOE</td>
</tr>
<tr>
<td>10474</td>
<td>APP_NDPI_ARMAGETRON</td>
</tr>
<tr>
<td>10480</td>
<td>APP_TFTP</td>
</tr>
<tr>
<td>10481</td>
<td>APP_Bootstrap_Protocol</td>
</tr>
<tr>
<td>10482</td>
<td>APP_KONTIKI</td>
</tr>
<tr>
<td>10486</td>
<td>APP_VIBER</td>
</tr>
<tr>
<td>10487</td>
<td>APP_PPLIVE</td>
</tr>
<tr>
<td>10492</td>
<td>APP_HALFLIFE2_AND_MODS</td>
</tr>
<tr>
<td>10497</td>
<td>APP_XT800</td>
</tr>
<tr>
<td>10503</td>
<td>APP_SUNLOGIN</td>
</tr>
<tr>
<td>10504</td>
<td>APP_NDPI_BATTLEFIELD</td>
</tr>
<tr>
<td>10508</td>
<td>APP_SKYPE</td>
</tr>
<tr>
<td>10509</td>
<td>APP_HOPOPTS</td>
</tr>
<tr>
<td>10510</td>
<td>APP_IGMP</td>
</tr>
<tr>
<td>10511</td>
<td>APP_GGP</td>
</tr>
<tr>
<td>10512</td>
<td>APP_STREAM</td>
</tr>
<tr>
<td>10513</td>
<td>APP_CBT</td>
</tr>
<tr>
<td>10514</td>
<td>APP_BBN_RCC</td>
</tr>
<tr>
<td>10515</td>
<td>APP_NVPII</td>
</tr>
<tr>
<td>10516</td>
<td>APP_PUP</td>
</tr>
<tr>
<td>10517</td>
<td>APP_ARGUS</td>
</tr>
<tr>
<td>10518</td>
<td>APP_EMCON</td>
</tr>
<tr>
<td>10519</td>
<td>APP_XNET</td>
</tr>
<tr>
<td>10520</td>
<td>APP_CHAOS</td>
</tr>
<tr>
<td>10521</td>
<td>APP_MUX</td>
</tr>
<tr>
<td>10522</td>
<td>APP_DCNMEAS</td>
</tr>
<tr>
<td>10523</td>
<td>APP_HMP</td>
</tr>
<tr>
<td>10524</td>
<td>APP_PRM</td>
</tr>
<tr>
<td>10525</td>
<td>APP_IDP</td>
</tr>
<tr>
<td>10526</td>
<td>APP_TRUNK1</td>
</tr>
<tr>
<td>10527</td>
<td>APP_TRUNK2</td>
</tr>
<tr>
<td>10528</td>
<td>APP_LEAF1</td>
</tr>
<tr>
<td>10529</td>
<td>APP_LEAF2</td>
</tr>
<tr>
<td>10530</td>
<td>APP_IRT</td>
</tr>
<tr>
<td>10531</td>
<td>APP_BULK</td>
</tr>
<tr>
<td>10532</td>
<td>APP_MFE_NSP</td>
</tr>
<tr>
<td>10533</td>
<td>APP_MERIT</td>
</tr>
<tr>
<td>10534</td>
<td>APP_DCCP</td>
</tr>
<tr>
<td>10535</td>
<td>APP_3PC</td>
</tr>
<tr>
<td>10536</td>
<td>APP_IDPR</td>
</tr>
<tr>
<td>10537</td>
<td>APP_XTP</td>
</tr>
<tr>
<td>10538</td>
<td>APP_DDP</td>
</tr>
<tr>
<td>10539</td>
<td>APP_CMTP</td>
</tr>
<tr>
<td>10540</td>
<td>APP_TPPP</td>
</tr>
<tr>
<td>10541</td>
<td>APP_IL</td>
</tr>
<tr>
<td>10542</td>
<td>APP_SDRP</td>
</tr>
<tr>
<td>10543</td>
<td>APP_ROUTING</td>
</tr>
<tr>
<td>10544</td>
<td>APP_FRAGMENT</td>
</tr>
<tr>
<td>10545</td>
<td>APP_IDRP</td>
</tr>
<tr>
<td>10546</td>
<td>APP_RSVP</td>
</tr>
<tr>
<td>10548</td>
<td>APP_DSR</td>
</tr>
<tr>
<td>10549</td>
<td>APP_BNA</td>
</tr>
<tr>
<td>10550</td>
<td>APP_INSLP</td>
</tr>
<tr>
<td>10551</td>
<td>APP_SWIPE</td>
</tr>
<tr>
<td>10552</td>
<td>APP_NHRP</td>
</tr>
<tr>
<td>10553</td>
<td>APP_MOBILE</td>
</tr>
<tr>
<td>10554</td>
<td>APP_TLSP</td>
</tr>
<tr>
<td>10555</td>
<td>APP_SKIP</td>
</tr>
<tr>
<td>10556</td>
<td>APP_NONE</td>
</tr>
<tr>
<td>10557</td>
<td>APP_DSTOPTS</td>
</tr>
<tr>
<td>10558</td>
<td>APP_SHIM6_OL</td>
</tr>
<tr>
<td>10559</td>
<td>APP_MIP6</td>
</tr>
<tr>
<td>10560</td>
<td>APP_SATEXPAK</td>
</tr>
<tr>
<td>10561</td>
<td>APP_KRYPTOLA</td>
</tr>
<tr>
<td>10562</td>
<td>APP_RVD</td>
</tr>
<tr>
<td>10563</td>
<td>APP_IPPC</td>
</tr>
<tr>
<td>10564</td>
<td>APP_SATMON</td>
</tr>
<tr>
<td>10565</td>
<td>APP_VISA</td>
</tr>
<tr>
<td>10566</td>
<td>APP_IPCV</td>
</tr>
<tr>
<td>10567</td>
<td>APP_CPNX</td>
</tr>
<tr>
<td>10568</td>
<td>APP_CPHB</td>
</tr>
<tr>
<td>10569</td>
<td>APP_WSN</td>
</tr>
<tr>
<td>10570</td>
<td>APP_PVP</td>
</tr>
<tr>
<td>10571</td>
<td>APP_BRSATMON</td>
</tr>
<tr>
<td>10572</td>
<td>APP_SUNND</td>
</tr>
<tr>
<td>10573</td>
<td>APP_WBMON</td>
</tr>
<tr>
<td>10574</td>
<td>APP_WBEXPAK</td>
</tr>
<tr>
<td>10575</td>
<td>APP_OSI</td>
</tr>
<tr>
<td>10576</td>
<td>APP_VMTP</td>
</tr>
<tr>
<td>10577</td>
<td>APP_SVMTP</td>
</tr>
<tr>
<td>10578</td>
<td>APP_VINES</td>
</tr>
<tr>
<td>10579</td>
<td>APP_TTP</td>
</tr>
<tr>
<td>10580</td>
<td>APP_NSFNETIG</td>
</tr>
<tr>
<td>10581</td>
<td>APP_DGP</td>
</tr>
<tr>
<td>10582</td>
<td>APP_TCF</td>
</tr>
<tr>
<td>10583</td>
<td>APP_SPRITE</td>
</tr>
<tr>
<td>10584</td>
<td>APP_LARP</td>
</tr>
<tr>
<td>10585</td>
<td>APP_MTP</td>
</tr>
<tr>
<td>10586</td>
<td>APP_AX25</td>
</tr>
<tr>
<td>10587</td>
<td>APP_IPINIP</td>
</tr>
<tr>
<td>10588</td>
<td>APP_MICP</td>
</tr>
<tr>
<td>10589</td>
<td>APP_SCCCP</td>
</tr>
<tr>
<td>10590</td>
<td>APP_ETHERIP</td>
</tr>
<tr>
<td>10591</td>
<td>APP_ENCAP</td>
</tr>
<tr>
<td>10592</td>
<td>APP_GMTP</td>
</tr>
<tr>
<td>10593</td>
<td>APP_IFMP</td>
</tr>
<tr>
<td>10594</td>
<td>APP_PNNI</td>
</tr>
<tr>
<td>10595</td>
<td>APP_PIM</td>
</tr>
<tr>
<td>10596</td>
<td>APP_ARIS</td>
</tr>
<tr>
<td>10597</td>
<td>APP_SCPS</td>
</tr>
<tr>
<td>10598</td>
<td>APP_QNX</td>
</tr>
<tr>
<td>10599</td>
<td>APP_AN</td>
</tr>
<tr>
<td>10600</td>
<td>APP_SNP</td>
</tr>
<tr>
<td>10601</td>
<td>APP_COMPAQ</td>
</tr>
<tr>
<td>10602</td>
<td>APP_PGM</td>
</tr>
<tr>
<td>10603</td>
<td>APP_DDX</td>
</tr>
<tr>
<td>10604</td>
<td>APP_IATP</td>
</tr>
<tr>
<td>10605</td>
<td>APP_STP</td>
</tr>
<tr>
<td>10606</td>
<td>APP_SRP</td>
</tr>
<tr>
<td>10607</td>
<td>APP_UTI</td>
</tr>
<tr>
<td>10608</td>
<td>APP_SMP</td>
</tr>
<tr>
<td>10609</td>
<td>APP_SM</td>
</tr>
<tr>
<td>10610</td>
<td>APP_PTP</td>
</tr>
<tr>
<td>10611</td>
<td>APP_ISIS</td>
</tr>
<tr>
<td>10612</td>
<td>APP_FIRE</td>
</tr>
<tr>
<td>10613</td>
<td>APP_CRTP</td>
</tr>
<tr>
<td>10614</td>
<td>APP_CRUDP</td>
</tr>
<tr>
<td>10615</td>
<td>APP_SSCOPMCE</td>
</tr>
<tr>
<td>10616</td>
<td>APP_IPLT</td>
</tr>
<tr>
<td>10617</td>
<td>APP_SPS</td>
</tr>
<tr>
<td>10618</td>
<td>APP_PIPE</td>
</tr>
<tr>
<td>10619</td>
<td>APP_SCTP</td>
</tr>
<tr>
<td>10620</td>
<td>APP_FC</td>
</tr>
<tr>
<td>10621</td>
<td>APP_MPLS</td>
</tr>
<tr>
<td>10622</td>
<td>APP_MANET</td>
</tr>
<tr>
<td>10623</td>
<td>APP_HIP</td>
</tr>
<tr>
<td>10624</td>
<td>APP_SHIM6</td>
</tr>
<tr>
<td>10625</td>
<td>APP_WESP</td>
</tr>
<tr>
<td>10626</td>
<td>APP_ROHC</td>
</tr>
<tr>
<td>10627</td>
<td>APP_AX4000</td>
</tr>
<tr>
<td>10628</td>
<td>APP_NCS</td>
</tr>
<tr>
<td>10629</td>
<td>APP_PPTP</td>
</tr>
<tr>
<td>10630</td>
<td>APP_IPSEC</td>
</tr>
<tr>
<td>10631</td>
<td>APP_VOIP</td>
</tr>
<tr>
<td>10632</td>
<td>APP_HTTPS</td>
</tr>
<tr>
<td>10633</td>
<td>APP_NNTPS</td>
</tr>
<tr>
<td>10634</td>
<td>APP_SMTPS</td>
</tr>
<tr>
<td>10635</td>
<td>APP_IMAPS</td>
</tr>
<tr>
<td>10636</td>
<td>APP_POP3S</td>
</tr>
<tr>
<td>10637</td>
<td>APP_HTTP</td>
</tr>
<tr>
<td>10638</td>
<td>APP_SSL</td>
</tr>
<tr>
<td>10639</td>
<td>APP_RDP</td>
</tr>
<tr>
<td>10640</td>
<td>APP_Simatic_S7</td>
</tr>
<tr>
<td>10641</td>
<td>APP_COTP</td>
</tr>
<tr>
<td>10650</td>
<td>APP_Modbus</td>
</tr>
<tr>
<td>10701</td>
<td>No_Payload</td>
</tr>
<tr>
<td>10702</td>
<td>TCP_QueryOnly</td>
</tr>
<tr>
<td>10703</td>
<td>TCP_PortClose</td>
</tr>
<tr>
<td>10704</td>
<td>TCP_NoPayload</td>
</tr>
<tr>
<td>10705</td>
<td>TCP_Unknow</td>
</tr>
</tbody>
</table>
<pre><code><code><div>//如规则列表更新，请执行下列命令，重新生成列表
for f in `find $THE_ROOT/bin/_ProRule -type f -name '*.Rule'`; do xmllint --xpath '/Application/APPID/text()' $f; echo -n '|'; xmllint --xpath '/Application/Name/text()' $f; echo ;done | sort -n
</div></code></code></pre>
<h3 id="格式-5">格式</h3>
<pre><code><code><div>{
    &quot;type&quot;: 206,                        //类型206
    &quot;time&quot;: 1586761518,                 //发送时的时间戳,每5分钟发送一次
    &quot;id&quot;: 10429,                        //应用测协议id,最多一百种协议
    &quot;pkts&quot;: &quot;10&quot;                        //上述协议id，5分钟的总包数
}
</div></code></code></pre>
<pre><code><code><div>{
    &quot;type&quot;: 207,                        //类型207
    &quot;time&quot;: 1586760947,                 //发送时的时间戳,每5分钟发送一次
    &quot;port&quot;: 45389,                      //TCP 端口号,最多一百个端口
    &quot;pkts&quot;: &quot;970&quot;                       //上述协议id5分钟的总包数
}
</div></code></code></pre>
<pre><code><code><div>{
    &quot;type&quot;: 208,                        //类型208
    &quot;time&quot;: 1586761518,                 //发送时的时间戳,每5分钟发送一次
    &quot;port&quot;: 1633,                       //UDP端口号,最多一百个端口
    &quot;pkts&quot;: &quot;142&quot;                       //上述协议id5分钟的总包数
}
</div></code></code></pre>
<h2 id="网络拓扑">网络拓扑</h2>
<h3 id="格式-6">格式</h3>
<pre><code><code><div>{
    &quot;type&quot;: 209,                        //类型209
    &quot;time&quot;: 1586776449,                 //发送时的时间戳,每5分钟发送一次
    &quot;mac&quot;: &quot;c8:e7:f0:6d:33:d8&quot;,         //mac地址,最多发送一百条，按收包+发包排序
    &quot;alert&quot;: &quot;30&quot;,                      //MAC告警数量总量
    &quot;send&quot;: &quot;1158303&quot;,                  //MAC发包总数
    &quot;recv&quot;: &quot;841558&quot;,                   //MAC收包总数
    &quot;port&quot;: null,                       //开放端口列表(暂时不做)
    &quot;ts_first&quot;: 1586776149,             //MAC最早发现时间
    &quot;ts_last&quot;: 1586776412               //MAC最晚活动时间
}
</div></code></code></pre>
<pre><code><code><div>{
    &quot;type&quot;: 211,                        //类型211
    &quot;time&quot;: 1586773498,                 //发送时的时间戳,每5分钟发送一次，最多50对
    &quot;mac_1&quot;: &quot;3c:8a:b0:86:69:a8&quot;,       //通联mac1
    &quot;mac_2&quot;: &quot;c8:e7:f0:6c:f3:33&quot;,       //通联mac2
    &quot;pkt&quot;: &quot;1968637&quot;,                   //通联包数
    &quot;ts_first&quot;: 1586773199,             //通联最早发现时间
    &quot;ts_last&quot;: 1586773462               //通联最晚活动时间
}
</div></code></code></pre>
<h2 id="线路分析">线路分析</h2>
<h3 id="格式-7">格式</h3>
<pre><code><code><div>{
    &quot;type&quot;: 213,                        //类型213
    &quot;time&quot;: 1586775551,                 //发送时的时间戳,每30秒钟发送一次
    &quot;total_bytes&quot;: &quot;456979762&quot;,         //线路分析总字节数byte
    &quot;bps&quot;: &quot;121861264&quot;,                 //线路分析流量大小bps
    &quot;total_pkts&quot;: &quot;558715&quot;,             //线路分析总包数
    &quot;pps&quot;: 18623,                       //线路分析pps
    &quot;ts_start&quot;: 1586775519,             //线路分析启动时间(时间戳)
    &quot;ts_run&quot;: 32                        //线路分析运行时间(秒)
}
</div></code></code></pre>
<h2 id="离线任务进度">离线任务进度</h2>
<h3 id="格式-8">格式</h3>
<pre><code><code><div>{
    &quot;type&quot;: 223,                        //类型223
    &quot;task_id&quot;: &quot;9876&quot;,                  //任务ID
    &quot;batch_id&quot;: &quot;543210&quot;,               //批次ID
    &quot;total&quot;: &quot;2&quot;,                       //任务pcap文件总数
    &quot;finished&quot;: &quot;1&quot;,                    //已处理pcap文件数
    &quot;time&quot;: 1668346059                  //发送时的时间戳
}
</div></code></code></pre>

        <script async src="https://cdn.jsdelivr.net/npm/katex-copytex@latest/dist/katex-copytex.min.js"></script>
        
    </body>
    </html>
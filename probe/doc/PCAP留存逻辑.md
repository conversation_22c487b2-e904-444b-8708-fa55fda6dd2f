# PCAP留存逻辑

```flow

st=>start: 开始
cond1=>condition: 数据包是否关联会话
cond2=>condition: 是否全流量留存
cond3=>condition: 命中规则且规则非全白
cond4=>condition: 未命中规则或命中规则非全白
cond5=>condition: 是无IP报文
cond6=>condition: 无IP包留存
cond7=>condition: 防御丢弃包并且攻击包留存
cond8=>condition: 全量留存模式并且非过滤规则丢包
e1=>end: 规则留存
e2=>end: 全流量留存
e3=>end: 无IP包留存
e4=>end: 攻击包留存
e5=>end: 不留存



st->cond1
cond1(yes)->cond2
cond2(no)->cond3
cond3(yes)->e1
cond3(no)->e5
cond2(yes)->cond4
cond4(no)->e5
cond4(yes)->e2
cond1(no)->cond5
cond5(yes)->cond6
cond6(yes)->e3
cond6(no)->e5
cond5(no)->cond7
cond7(yes)->e4
cond7(no)->cond8
cond8(yes)->e2
cond8(no)->e5
```

```
PCAP链路类型：
    PROTOCOL_LOOPBACK
        ".1300"
    PROTOCOL_PPP
        ".10"
    PROTOCOL_CHDLC
        ".113"
    PROTOCOL_IEEE80211
        ".277"
    PROTOCOL_SLL
        ".562"
    PROTOCOL_ETH
        ""

规则留存路径：
    /data/{任务ID}/{批次ID}/pcapfiles/{线程ID}/rule/{包处理时间戳/14400}/{PCAP首包处理时间戳}_{递增序列}{PCAP链路类型}.pcap
全流量留存路径：
    /data/{任务ID}/{批次ID}/pcapfiles/{线程ID}/full_flow/{包处理时间戳/14400}/{PCAP首包处理时间戳}_{递增序列}{PCAP链路类型}.pcap
无IP包留存路径：
    /data/{任务ID}/{批次ID}/pcapfiles/{线程ID}/noip_packet/{包处理时间戳/14400}/{PCAP首包处理时间戳}_{递增序列}{PCAP链路类型}.pcap
攻击包留存路径：
    /data/{任务ID}/{批次ID}/pcapfiles/{线程ID}/attack/{包处理时间戳/14400}/{PCAP首包处理时间戳}_{递增序列}{PCAP链路类型}.pcap
```
# 过滤规则格式
```json
{
    ...
    "Filter":
    {
        "Respond": "drop",              //命中规则的响应策略 drop表示命中规则丢弃,未命中处理  pass表示命中规则处理,未命中丢弃
        "Rule": [                       //过滤规则数组
        ]
    },
    ...
}
```
# 单条过滤规则格式

## IP协议号规则
```json
{
    "ID": 1,                            //规则ID可省略,因当前取消针对规则ID的统计
    "IP": "***********",                //此规则生效的IP地址, 取消此字段表示全部IP地址生效
    "IPPro": [1],                       //IP协议号数组
    "IPPro_Type": "Select"              //IP协议号属性,Select表示正选,Invert表示反选
}
```
## TCP/UDP端口号规则
```json
{
    "ID": 2,                            //规则ID可省略,因当前取消针对规则ID的统计
    "IP": "***********",                //此规则生效的IP地址, 取消此字段表示全部IP地址生效
    "TCPPort": [],                      //TCP端口数组,TCPPort和UDPPort至少存在一项
    "TCPPort_Type": "Invert",           //TCP端口属性,Select表示正选,Invert表示反选
    "UDPPort": [53],                    //UDP端口数组,TCPPort和UDPPort至少存在一项
    "UDPPort_Type": "Select"            //UDP端口属性,Select表示正选,Invert表示反选
}
```

## IPV4+掩码规则
```json
{
    "ID": 3,                            //规则ID可省略,因当前取消针对规则ID的统计
    "IP": "***********",                //IPV4地址,仅支持IPV4地址
    "NetMask": "*************"          //掩码,可省略表示单个IP地址
}
```
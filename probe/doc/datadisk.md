# 换盘流程

## 关机前流程

### 探针模式检测
```flow

st=>start: 开始
cond1=>condition: 检测是读盘探针
cond2=>condition: 检测支持换盘
e1=>end: 展示读盘探针页面
e2=>end: 展示标准探针页面
e3=>end: 不展示相关页面



st->cond1
cond1(yes)->e1
cond1(no)->cond2
cond2(yes)->e2
cond2(no)->e3

```

```
检测是读盘探针:
        配置文件：/opt/GeekSec/th/bin/conf/work_mode.xml
        配置项目：/config/mode
        值：1

检测支持换盘:
        配置文件：/opt/GeekSec/th/bin/conf/data_disk_conf.xml
        配置项目：/config/mode
        值：changing/direct
```

### 正常探针换盘流程

```flow
st=>start: 开始
op1=>operation: 用户触发换盘
op2=>operation: 提示：关机,拔盘
op3=>operation: 用户确认
op4=>operation: 删除数据盘UUID，并切换状态到换盘中
op5=>operation: 清理部分空间，用于存储任务信息
op6=>operation: 关机
e=>end: 结束

st->op1->op2->op3->op4->op5->op6->e

```
### 读盘探针换盘流程
```flow
st=>start: 开始
op1=>operation: 用户触发换盘
op2=>operation: 提示：关机,拔盘
op3=>operation: 用户确认
op4=>operation: 删除数据盘UUID，并切换状态到换盘中
op6=>operation: 关机
e=>end: 结束

st->op1->op2->op3->op4->op6->e

```

## 开机后流程
### 探针模式检测
```flow

st=>start: 开始
cond1=>condition: 检测是读盘探针
cond2=>condition: 检测支持换盘
e1=>end: 展示读盘探针页面
e2=>end: 展示标准探针页面
e3=>end: 不展示相关页面



st->cond1
cond1(yes)->e1
cond1(no)->cond2
cond2(yes)->e2
cond2(no)->e3

```

```
检测是读盘探针:
        配置文件：/opt/GeekSec/th/bin/conf/work_mode.xml
        配置项目：/config/mode
        值：1

检测支持换盘:
        配置文件：/opt/GeekSec/th/bin/conf/data_disk_conf.xml
        配置项目：/config/mode
        值：changing/direct
```
### 正常探针换盘流程
```flow

st=>start: 开始
cond2=>condition: 磁盘切换中
op1=>operation: 数据盘检测
cond3=>condition: 数据盘中硬盘容量大小一致
e2=>end: 展示数据盘信息
op2=>operation: 告警
op3=>operation: 用户触发数据盘重组
op4=>operation: 杀死系统进程(探针，ES等)
op5=>operation: 调用数据盘重组+数据盘初始化
op6=>operation: 状态切换
op7=>operation: 重启机器
e3=>end: 结束


st->cond2
cond2(no)->op1->cond3
cond3(yes)->e2
cond3(no)->op2->e2
cond2(yes)->op3->op4->op5->op6->op7->e3

```

### 读盘探针换盘流程
```flow

st=>start: 开始
cond2=>condition: 磁盘切换中
op1=>operation: 数据盘检测
cond3=>condition: 数据盘中硬盘容量大小一致
e2=>end: 展示数据盘信息
op2=>operation: 告警
op3=>operation: 用户触发数据盘读取
op4=>operation: 杀死系统进程(探针，ES等)
cond4=>condition: 调用数据盘挂载脚本成功
op6=>operation: 状态切换
op7=>operation: 重启机器
e3=>end: 结束
op8=>operation: 提示失败，并提示用户自己关机，检测数据盘插入状态，然后开机再试读取

st->cond2
cond2(no)->op1->e2
cond2(yes)->op3->op4->cond4(yes)->op6->op7->e3
cond4(no)->op8->e3

```


## 数据盘检测
### 执行命令
    python2 /opt/GeekSec/th/bin/datadisk_scan.py
### 参数
    -w: 默认不使用此参数，此参数将清理raid错误配置，仅在需要重组磁盘时使用
### 输出
    结果输出至标准输出，格式JSON
### 输出实例及说明

```
{
    "noraid": [                             //当前可用的物理硬盘信息
        {
            "slot": "252:0",                //槽位
            "med": "HDD",                   //硬盘类型
            "intf": "SAS",                  //硬盘接口类型
            "dev": "/dev/sda",              //设备系统名称
            "type": "JBOD",                 //接入RAID卡的方式 JBOD/UGood
            "size": "3.638 TB"              //硬盘大小
        },
        {
            "slot": "252:1",
            "med": "HDD",
            "intf": "SAS",
            "dev": "N/A",
            "type": "UGood",
            "size": "3.637 TB"
        }
    ],
    "raid": [                               //当前可用的逻辑磁盘信息
        {
            "vdid": "0",                    //逻辑磁盘ID
            "slots": [                      //组成逻辑磁盘的物理磁盘信息
                {
                    "slot": "252:2",
                    "med": "HDD",
                    "intf": "SAS",
                    "dev": "N/A",
                    "type": "N/A",
                    "size": "3.637 TB"
                },
                {
                    "slot": "252:3",
                    "med": "HDD",
                    "intf": "SAS",
                    "dev": "N/A",
                    "type": "N/A",
                    "size": "3.637 TB"
                },
                {
                    "slot": "252:4",
                    "med": "HDD",
                    "intf": "SAS",
                    "dev": "N/A",
                    "type": "N/A",
                    "size": "3.637 TB"
                }
            ],
            "type": "RAID5",                //逻辑磁盘类型
            "dev": "/dev/sdb",              //设备系统名称
            "size": "7.276 TB"              //逻辑硬盘大小
        }
    ],
    "used": {                               //当前已使用（已挂载）磁盘信息
        "noraid": [                         //物理硬盘
            {
                "slot": "252:5",
                "med": "HDD",
                "intf": "SAS",
                "dev": "/dev/sdc",
                "datadisk": false,          //是否被用于探针的数据盘，当磁盘被挂载后才存在此字段
                "type": "JBOD",
                "size": "3.638 TB"
            }
        ],
        "raid": [                           //逻辑磁盘
            {
                "dev": "/dev/sdd",
                "vdid": "1",
                "slots": [
                    {
                        "slot": "252:6",
                        "med": "HDD",
                        "intf": "SAS",
                        "dev": "N/A",
                        "type": "N/A",
                        "size": "3.637 TB"
                    },
                    {
                        "slot": "252:7",
                        "med": "HDD",
                        "intf": "SAS",
                        "dev": "N/A",
                        "type": "N/A",
                        "size": "3.637 TB"
                    },
                    {
                        "slot": "252:8",
                        "med": "HDD",
                        "intf": "SAS",
                        "dev": "N/A",
                        "type": "N/A",
                        "size": "3.637 TB"
                    }
                ],
                "size": "7.276 TB",
                "type": "RAID5",
                "datadisk": false
            },
            {
                "usedp": "1%",              //数据盘使用率，当datadisk为true时才存在此字段
                "dev": "/dev/sde",
                "vdid": "2",
                "slots": [
                    {
                        "slot": "252:9",
                        "med": "HDD",
                        "intf": "SAS",
                        "dev": "N/A",
                        "type": "N/A",
                        "size": "3.637 TB"
                    },
                    {
                        "slot": "252:10",
                        "med": "HDD",
                        "intf": "SAS",
                        "dev": "N/A",
                        "type": "N/A",
                        "size": "3.637 TB"
                    },
                    {
                        "slot": "252:11",
                        "med": "HDD",
                        "intf": "SAS",
                        "dev": "N/A",
                        "type": "N/A",
                        "size": "3.637 TB"
                    }
                ],
                "size": "7.276 TB",
                "type": "RAID5",
                "datadisk": true
            }
        ]
    }
}
```


## 数据盘重组

### 执行命令
    python2 /opt/GeekSec/th/bin/datadisk_rebuild.py xx.json
### 参数说明
    xx.json 是python2 /opt/GeekSec/th/bin/datadisk_scan.py -w 的输出文件
### 功能说明
    此脚本将xx.json中，'/noraid'和'/raid'中所有的磁盘重新组合成一个新的RAID5逻辑磁盘
### 返回值
    成功返回0
    失败返回非0（可用磁盘数小于3）

## 数据盘初始化

### 执行命令
    python2 /opt/GeekSec/th/bin/datadisk_init.py xx.json
### 参数说明
    xx.json 是重组成功后，再次执行python2 /opt/GeekSec/th/bin/datadisk_scan.py的输出文件
### 功能
    格式化数据盘，挂载数据盘，重建数据目录

## 数据盘挂载
### 执行命令
    python2 /opt/GeekSec/th/bin/datadisk_remount.py xx.json
### 参数说明
    xx.json 是python2 /opt/GeekSec/th/bin/datadisk_scan.py的输出文件
### 功能说明
    此脚本将xx.json中，可用的RAID5逻辑磁盘添加到系统自动挂载中
### 返回值
    成功返回0
    失败返回非0，无法识别到数据盘
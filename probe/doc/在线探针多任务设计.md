# 在线探针多任务设计

## 1. 输入

### 1.1 公用配置

#### 1.1.1 公用数据文件路径/opt/GeekSec/th/bin/db/
例如：GeoLite2-City.mmdb，预设指纹列表等

#### 1.1.2 公用配置文件路径/opt/GeekSec/th/bin/conf_pub/

##### 1.1.2.1 公用网卡和任务配置文件，修改如下配置需重启所有任务
/opt/GeekSec/th/bin/conf_pub/ifconf.json
```json
{
    "max_task": 2,                                                                          //并行任务数量：主从共计两个
    "task": [                                                                               //任务配置：出厂设定完成，一般不存在修改的场景
        {                                                                                   //主任务配置
            "numa": 0,                                                                      //设置任务运行所在CPU编号，减少跨NUMA访问内存带来的性能下降，非双路设置为-1,双路设置为CPUID。
            "hugepage": [10, 10],                                                           //分配给任务的大页内存（GB），分别在每个节点上分布
            "limit_mbps": 1500,                                                             //流量限制（默认主任务1500，从任务500），超过设定大小的20%后，开始按数据包哈希进行按比例丢包
            "lcore": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]                        //任务运行所在逻辑核数组，安装时会自动根据实际CPU的自动设定，已分配的逻辑核，会同步设定到探针保留核心列表/opt/GeekSec/config.yaml
        },
        {"numa": 0, "hugepage": [3,3], "limit_mbps": 500, "lcore": [15,32,33,34,35,36]}     //从任务配置
    ],
    "if": [                                                                                 //捕包口信息数组
        {"pci": "xxxx:xx:xx.0", "name": "4"},                                               //捕包口PCI地址，以及命名
        {"pci": "xxxx:xx:xx.1", "name": "3"},
        {"pci": "xxxx:xx:xx.2", "name": "2"},
        {"pci": "xxxx:xx:xx.3", "name": "1"}
    ],
    "0": [ 0, 1, 3 ],                                                                       //主任务捕包口数组
    "1": [ 2 ],                                                                             //从任务捕包口数组
    "suspend": [ "1" ],                                                                     //挂起任务数组
    "forward": [ "0" ]                                                                      //开启虚拟网卡转发数据包的任务
}
```

### 1.2 单个任务配置

#### 1.2.1 单任务环境变量
通过服务脚本启动的探针程序，此环境变量自动生效，无需手动配置。
```bash
THE_ID=0
#探针编号：0为主任务,1为从任务

THE_TASKID=0
#探针任务ID：/opt/GeekSec/th/bin/conf/${THE_ID}/task_info.json中读取

THE_BATCHID=1
#探针批次ID：/opt/GeekSec/th/bin/conf/${THE_ID}/task_info.json中读取

```

#### 1.2.2 单任务配置文件路径/opt/GeekSec/th/bin/conf/${THE_ID}/

##### 1.2.2.1 任务信息
/opt/GeekSec/th/bin/conf/${THE_ID}/task_info.json
```json
{
    "batch_id": 1,                                  //批次ID
    "task_id": 0                                    //任务ID
}
```
##### 1.2.2.2 kafka配置文件
/opt/GeekSec/th/bin/conf/${THE_ID}/kafka_conf.json
```json
{
    "brokers": "192.168.101.180:9092"               //bootstrap broker
}
```
##### 1.2.2.3 实时规则


##### 1.2.2.4 原Config.txt
/opt/GeekSec/th/bin/conf/${THE_ID}/Config/Config.txt
##### ******* 原JsonRule
/opt/GeekSec/th/bin/conf/${THE_ID}/JsonRule
##### ******* 原LibConfig
/opt/GeekSec/th/bin/conf/${THE_ID}/LibConfig
##### ******* 原th_engine_conf.xml
/opt/GeekSec/th/bin/conf/${THE_ID}/th_engine_conf.xml


## 2. 输出
```bash
/data/{$THE_TASKID}/{$THE_BATCHID}/capfiles         #探针扫描PCAP文件路径,原/home/<USER>
/data/{$THE_TASKID}/{$THE_BATCHID}/cerfiles         #探针证书输出,原/data/cerfiles。改kafka后失效。
/data/{$THE_TASKID}/{$THE_BATCHID}/ipslice          #探针IP切片输出,原/data/ipslice，切片输出功能已取消，已失效。
/data/{$THE_TASKID}/{$THE_BATCHID}/midfiles         #探针处理PCAP中间路径,原/home/<USER>
/data/{$THE_TASKID}/{$THE_BATCHID}/outjson          #探针JSON输出,原/data/outjson，关闭输出后失效。
/data/{$THE_TASKID}/{$THE_BATCHID}/pbfiles          #探针PB输出,原/data/pbfiles，改kafka后失效。
/data/{$THE_TASKID}/{$THE_BATCHID}/pcapfiles        #探针PCAP输出,原/data/pcapfiles。
/data/{$THE_TASKID}/{$THE_BATCHID}/savecaps         #探针扫描PCAP文件储存路径，扫描模式已不使用，失效。
/data/{$THE_TASKID}/{$THE_BATCHID}/statistics       #探针统计输出,原/data/statistics
/data/{$THE_TASKID}/{$THE_BATCHID}/whitejson        #探针白名单日志输出,原/data/whitejson，白名单日志功能，待讨论。
```

## 3. 服务管理程序

### 3.1 重启所有探针
/opt/GeekSec/th/bin/thd.all.restart
当网卡分配调整或有需要探针挂起时，修改配置文件后执行的重启命令。
此脚本已考虑并行情况。可多个并行执行。
### 3.2 重启单个任务探针
/opt/GeekSec/th/bin/thd.worker.restart ${THE_ID}
当单个任务的探针发生配置变更后执行。
此脚本已考虑并行情况。可多个并行执行。
参数说明：单个参数。需要重启的探针编号。0为主任务。1为从任务


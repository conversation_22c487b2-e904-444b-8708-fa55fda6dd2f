# 探针初始化配置

## 在线探针

#### 1. 授权

```bash
详情待补充
```

#### 2. 停止所有服务

```bash
/opt/GeekSec/th/bin/thd.all.stop
```

#### 3. 设定基础配置文件

##### 3.1 查看需要配置为捕包的网口信息

```bash
cd /opt/GeekSec/th/bin && usertools/dpdk-devbind.py -s
```
##### 3.2 判断所有捕包口在否同一个NUMA节点上（推荐使用NUMA 节点0）
参考命令
```bash
cat '/sys/bus/pci/devices/0000:03:00.0/numa_node'
```
##### 3.3 调整捕包卡
如果所有捕包卡未在同一NUMA节点，关机，并调整捕包卡安装到PCI插槽的位置。直至所有捕包口在同一NUMA节点。（主板上对于PCI插槽有明显标注。）

##### 3.4 修改捕包卡配置文件

配置文件路径：/opt/GeekSec/th/bin/conf_pub/ifconf.json
```
配置项目：
    /max_task
        并行任务数量，标准设备为2

    /task/${探针编号}/numa
        探针运行的NUMA编号，标准双路设备都为0，单路设备需要手动设定为-1。

    /task/${探针编号}/hugepage
        大页内存分布，标准双路设备主任务为[10,10]从任务为[3,3]，单路设备一般主任务为[10]从任务[3]

    /task/${探针编号}/limit_mbps
        限速设定，超过限定的20%开始主动丢包，标准设备主任务1500从任务500，非标准设备可根据需求自行配置

    /task/${探针编号}/lcore
        探针运行的逻辑核编号列表，探针根据设定的逻辑核心数，自动调整线程数。标准设备会在探针安装时自动设定好，如非标准设备可基于自动设定结果手动调整。开启转发的任务至少需要4个逻辑核心，未开启转发的任务至少需要3个逻辑核心。

    /if
        捕包卡列表，通过修改数据库，然后调用祥哥同步脚本的方式修改此配置

    /"0"
        关联到主任务的捕包卡编号，通过修改数据库，然后调用祥哥同步脚本的方式修改此配置

    /"1"
        关联到从任务的捕包卡编号，通过修改数据库，然后调用祥哥同步脚本的方式修改此配置

    /suspend
        挂起任务列表，通过修改数据库，然后调用祥哥同步脚本的方式修改此配置

    /forward
        开启转发的任务列表，一般根据实际需求进行配置
```

样例：
```json
{
    "max_task": 2,
    "task": [                                                                       //任务详细配置
        {"numa": 0, "hugepage": [10,10], "limit_mbps": 1500, "lcore": [1,2,3,4]},
        {"numa": 0, "hugepage": [3,3], "limit_mbps": 500, "lcore": [5,6,7]}
    ],
    "if": [
        {"pci": "xxxx:xx:xx.0", "name": "4"},                   //编号为0的网口信息。网口名为"网口-4"
        {"pci": "xxxx:xx:xx.1", "name": "3"},                   //编号为1的网口信息。网口名为"网口-3"
        {"pci": "xxxx:xx:xx.2", "name": "2"},                   //编号为2的网口信息。网口名为"网口-2"
        {"pci": "xxxx:xx:xx.3", "name": "1"}                    //编号为3的网口信息。网口名为"网口-1"
    ],
    "0": [ 0, 1, 3 ],                                           //主任务选择 网口-4，网口-3，网口-1
    "1": [ 2 ],                                                 //从任务选择 网口-2
    "suspend": [ "1" ],                                         //从任务挂起
    "forward": [ "0" ]                                          //主任务开启虚拟网卡发包
}
```


#### 4. 执行探针初始化脚本，自动修改启动引导，并设定保留核心和大页内存
```bash
cd /opt/GeekSec/th/bin && bash dpdk_init.sh
```

#### 5. 重启设备
```
shutdown -r now
```

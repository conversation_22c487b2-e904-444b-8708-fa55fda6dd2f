// Last Update:2019-03-07 14:17:24
/**
 * @file esp_str.h
 * @brief 
 * <AUTHOR>
 * @version 0.0.00
 * @date 2019-03-05
 */
 

#include <commit_tools.h>
#include <sys/stat.h>
#include <sys/time.h>
#include "null_plugin.h"

extern "C" {
    int get_plugin_id()
    {
        return 12345;
    }
    session_pasre_base * attach()
    {
        return new null_plugin();
    }
}

null_plugin::null_plugin()
{
    reload();
}

null_plugin::~null_plugin()
{
    ;
}

void null_plugin::reload()
{
    ;
}

bool null_plugin::potocol_init(session_pub* p_session, c_packet* p_packet)
{
    if(p_session==NULL || p_packet==NULL)
    {
        return false;
    }
    proto_parse_session *p_pp_session = (proto_parse_session*)(p_session->p_sp_session);
    null_session * p_null_session = (null_session *)p_pp_session->expansion_data;
    p_null_session->thread_id = p_session->thread_id;
    return true;
}



bool null_plugin::potocol_sign_judge(session_pub * p_session,c_packet * p_packet) //组包
{
    if(p_session==NULL || p_packet==NULL)
    {
        return false;
    }
    proto_parse_session* p_pp_session = (proto_parse_session * )( p_session -> p_sp_session);
    null_session * p_null_session = (null_session *)p_pp_session->expansion_data;
    //组包
    if (p_packet->app_data_len == 0)
    {
        return false;
    }
    if (p_packet->Directory == false)
    {
        p_session->clinet_repcom.add_tcp_packet(p_packet->app_data_len, (char*)p_packet->p_app_data, p_packet->seq);
        p_null_session->p_data = p_session->clinet_repcom.get_tcp_data(p_null_session->data_len);
    }
    else if (p_packet->Directory == true)
    {
        p_session->server_repcom.add_tcp_packet(p_packet->app_data_len, (char*)p_packet->p_app_data, p_packet->seq);
        p_null_session->p_data = p_session->server_repcom.get_tcp_data(p_null_session->data_len);
    }
    if (1)//此处加具体条件是否组包完成可以解析了
    {
        return true;
    }
    return false;

}

bool null_plugin::potocol_parse_handle(session_pub * p_session,c_packet *p_packet) //解析
{
    if(p_session==NULL || p_packet==NULL)
    {
        return false;
    }
    proto_parse_session* p_pp_session = (proto_parse_session * )( p_session -> p_sp_session);
    null_session * p_null_session = (null_session *)p_pp_session->expansion_data;
    //如果解析数据之后发现可以发送了，就返回true
    bool ret = false;
    //ret = data_parse(p_null_session->p_data, p_null_session->data_len, p_null_session);//这里填充具体的解析细节
    //解析完数据之后要将组包buff清理出来
    if (p_packet->Directory == false)
    {
        p_session->clinet_repcom.clear_next_buf();
    }
    else if (p_packet->Directory == true)
    {
        p_session->server_repcom.clear_next_buf();
    }
    //如果可以发送了，直接返回true
    if (ret)
    {
        return true;
    }
    return false;
}

void null_plugin::msg_send(session_pub* p_session)  //发送
{
    //这里设置要发送的内容
    return;
}

void null_plugin::potocol_data_handle(session_pub* p_session,c_packet * p_packet)  //发送
{
    msg_send(p_session);
}

bool null_plugin::time_out(session_pub * p_session, uint32_t check_time)
{
    if (p_session)
    {
        return true;
    }
    return true;
}

void null_plugin::resources_recovery(session_pub * p_session)
{
    proto_parse_session *p_pp_session = (proto_parse_session*)(p_session->p_sp_session);
    null_session * p_null_session = (null_session *)p_pp_session->expansion_data;
    //如果有资源要释放
    /*
    if (p_null_session->p_null_message != NULL)
    {
        delete p_ssl_session->p_null_message;
        p_ssl_session->p_null_message = NULL;
    }
    */
    return;
}

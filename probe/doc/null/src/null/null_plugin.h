// Last Update:2019-03-05 10:06:00
/**
 * @file ssl_str.h
 * @brief 
 * <AUTHOR>
 * @version 0.0.00
 * @date 2019-03-05
 */

#ifndef NSS_PLUGIN_H
#define NSS_PLUGIN_H
#include <stdio.h>
#include <iomanip>
#include <sstream>
#include <xml_parse.h>
#include <c_ip.h>
#include <map>
#include <TH_engine_interface.h>
#include <proto_parse_session.h>
#include "null_str.h"

using namespace std;

extern "C" {
    int get_plugin_id();
    session_pasre_base * attach();
};

#include "dnumstr.h"

class null_plugin : public session_pasre_base{
    public:
        null_plugin();
        ~null_plugin();
        virtual void reload();
        virtual bool potocol_init(session_pub* p_sess, c_packet* p_pack);
        virtual bool potocol_sign_judge(session_pub* p_sess, c_packet* p_pack);
        virtual bool potocol_parse_handle(session_pub* p_sess,c_packet * p_packet);
        virtual void potocol_data_handle(session_pub* p_sess,c_packet * p_packet);
        virtual bool time_out(session_pub* p_sess,uint32_t check_time);
        virtual void resources_recovery(session_pub* p_sess);
    private:
        void msg_send(session_pub* p_session);
};
#endif  /*NSS_PLUGIN_H*/

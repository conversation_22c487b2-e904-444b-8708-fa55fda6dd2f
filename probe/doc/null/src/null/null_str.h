// Last Update:2019-03-05 10:06:00
/**
 * @file ssl_str.h
 * @brief 
 * <AUTHOR>
 * @version 0.0.00
 * @date 2019-03-05
 */

#ifndef NSS_STR_H
#define NSS_STR_H

#include <session_pub.h>
#include <packet.h>
#include <stdint.h>
#include <string> 
#include <list> 


using namespace std;

class null_session
{
public:
    uint32_t thread_id;
    char* p_data;
    uint32_t data_len;
};
#endif  /*NSS_STR_H*/

# 建立多个持续运行的离线PCAP导入探针服务
# 创建导入任务时寻找未工作的service，将task的配置信息同步给service（todo，规则等也需要同步）
# 配置更新完成后重新启动service
# 每个service一段时间内处理一个task的数据，处理完成后检查service的状态，停止service
import base64
import glob
import json
import os
import shutil
import subprocess
import time
import xml.etree.ElementTree as ET
from enum import Enum
from pathlib import Path

import pymysql.cursors
from kafka import KafkaConsumer

SERVICE_ID_LIST = [11, 12, 13]  # 可以同时执行的导入任务, 一个service同时处理一个task


class ServiceStatus(Enum):
    ACTIVE = 1
    INACTIVE = 2
    NOT_FOUND = 3
    ERROR = 4


def get_service_name(id):
    return f"offline_thd.{id}.service"


def check_service_status(service_name):
    try:
        # Check if the service exists
        process_list = subprocess.run(['systemctl', 'list-unit-files', '--no-pager'],
                                      stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
        output_list = process_list.stdout

        if service_name in output_list:
            # Check if the service is active
            process_active = subprocess.run(['systemctl', 'is-active', service_name],
                                            stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
            output_active = process_active.stdout.strip()

            if output_active == "active":
                return ServiceStatus.ACTIVE
            else:
                return ServiceStatus.INACTIVE
        else:
            return ServiceStatus.NOT_FOUND
    except subprocess.CalledProcessError as e:
        return ServiceStatus.ERROR


# 初始化服务
# case NOT_FOUND: 创建服务信息
def create_service(service_id):
    service_name = get_service_name(service_id)
    status = check_service_status(service_name)

    if status != ServiceStatus.NOT_FOUND:
        return

    conf_dir = Path(f"/conf/{service_id}/{service_name}")
    thd_env_dir = Path("/opt/GeekSec/th/bin/conf_pub/env")
    service_dir = Path("/opt/GeekSec/th/bin/service")

    # 创建配置目录并复制文件
    conf_dir.mkdir(parents=True, exist_ok=True)

    # 复制文件
    source_dir = Path('/opt/GeekSec/task/thd_offline_conf')
    if source_dir.is_dir():
        for item in source_dir.iterdir():
            dest = conf_dir / item.name
            if item.is_dir():
                shutil.copytree(item, dest)
            else:
                shutil.copy2(item, dest)

    # 修改导入的ENV文件
    env_content = f"""
    THE_ROOT=/opt/GeekSec/th
    PATH=/opt/GeekSec/th/bin/:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/root/bin
    LD_LIBRARY_PATH=/opt/GeekSec/th/bin/:/opt/GeekSec/th/sdk/lib/
    THE_CONFPUB_PATH=/opt/GeekSec/th/bin/conf_pub/
    THE_DB_PATH=/opt/GeekSec/th/bin/db/
    THE_CONF_PATH={conf_dir}
    THE_ID={service_id}
    """.strip()
    env_file_path = thd_env_dir / f"THD_ENV_{service_id}"
    with env_file_path.open("w+") as outf:
        outf.write(env_content.strip())

    # 修改导入的SERVICE文件
    service_content = f"""
    [Unit]
    Description=thd service id {service_id}

    [Service]
    Type=simple
    EnvironmentFile={env_file_path}

    ExecStart=/opt/GeekSec/th/bin/thd.product_analysis

    [Install]
    WantedBy=multi-user.target
    """.strip()

    service_file_path = service_dir / service_name
    with service_file_path.open("w+") as outf:
        outf.write(service_content.strip())

    # 创建符号链接
    symlink_path = Path(f"/etc/systemd/system/{service_name}")
    if symlink_path.exists() or symlink_path.is_symlink():
        symlink_path.unlink()
    os.symlink(service_file_path, symlink_path)


def find_idle_service():
    for service_id in SERVICE_ID_LIST:
        service_name = get_service_name(service_id)
        status = check_service_status(service_name)

        # 若服务未创建，创建后返回
        if status == ServiceStatus.NOT_FOUND:
            print(f"{service_name} not found")
            create_service(service_id)
            return service_id

        # 若服务空闲，直接返回
        if status == ServiceStatus.INACTIVE:
            print(f"{service_name} inactive")
            return service_id

        if status == ServiceStatus.ERROR:
            print(f"{service_name} error")
            time.sleep(3)

    while True:
        result = find_idle_service()
        if result:
            return result


def get_connection():
    retry_interval = 5  # 重试间隔时间（秒）
    max_retries = 3  # 最大重试次数
    retry_count = 0  # 已重试次数

    while retry_count < max_retries:
        try:
            connection = pymysql.connect(
                host='127.0.0.1', port=23306, user='root', password='simpleuse23306p',
                db='th_analysis', charset='utf8mb4', cursorclass=pymysql.cursors.DictCursor
            )
            return connection
        except pymysql.MySQLError:
            print(
                f"Failed to connect to MySQL, retrying in {retry_interval} seconds... (Attempt {retry_count + 2})")
            time.sleep(retry_interval)
            retry_count += 1

    print("Failed to connect to MySQL after maximum retries.")
    return None


def write_file(filename, content):
    with open(filename, "w") as fnew:
        fnew.write(content)


def write_base64_file(filename, encode_str):
    content = base64.decodebytes(bytes(encode_str, 'utf-8'))
    with open(filename, "wb") as fp:
        fp.write(content)


is_full_flow = True


def save_batch_pcap(batch_id):
    global is_full_flow
    sql = f"SELECT fullflow_state FROM th_analysis.tb_task_batch WHERE batch_id = {batch_id}"
    results = execute_sql(sql, get_connection())
    if results and results[0]["fullflow_state"] == "OFF":
        is_full_flow = False


json_context = ""


def create_and_write_rule(sql, rule_filename, task_path):
    global json_context

    content = ""
    results = execute_sql(sql, get_connection())
    for row in results:
        rule_json = json.loads(row["rule_json"])
        rule_json["Name"] = str(row['rule_name'])
        if rule_filename == "rule.json" and is_full_flow:
            rule_json["SaveBytes"] = -1
        rule_tag = {
            "RuleId": row['rule_id'],
            "rule_level": row['rule_level'],
            "RuleName": str(row['rule_name']),
            "Tag": [],
            "Infor": str(row['rule_desc']),
            "rule_size": row['rule_size'],
            "capture_mode": row['capture_mode'],
            "created_time": str(row['created_time']),
            "PbDrop": str(row['pb_drop']),
            "PcapDrop": str(row['pcap_drop']),
            "updated_time": str(row['updated_time'])
        }
        if "rule_type" in row:
            rule_types = row["rule_type"].split(",")
            if '6' in rule_types:  # 动态库规则
                lib_path = task_path / "JsonRule/BasicRule/LibFolder/"
                lib_name = row["lib_respond_lib"]
                lib_filename = lib_path / lib_name
                os.makedirs(lib_path, exist_ok=True)
                write_base64_file(lib_filename, row["lib_data_so"])
                if "lib_respund_config" in row and row["lib_respund_config"]:
                    conf_path = task_path / \
                                f"JsonRule/BasicRule/LibConfig/{rule_tag['RuleId']}/"
                    os.makedirs(conf_path, exist_ok=True)
                    write_base64_file(
                        conf_path / row["lib_respund_config"], row["lib_data_conf"])
                rule_json["LibRespond"]["Lib"] = lib_name
            if '7' in rule_types:  # 动态库规则
                conf_name = f"/tmp/{rule_tag['RuleId']}.json"
                json.dump(rule_json["DetailRespond"], open(conf_name, "w"))
                lib_path = task_path / "JsonRule/BasicRule/LibFolder/"
                lib_name = f"{rule_tag['RuleId']}.so"
                lib_filename = lib_path / lib_name
                os.makedirs(lib_path, exist_ok=True)
                os.system(
                    f"/opt/GeekSec/th/bin/complex_dll_gen/src/_complex_so_gen.sh {conf_name} {lib_name}")
                shutil.move(f"/tmp/{lib_name}", lib_filename)
                os.remove(conf_name)
                rule_json["LibRespond"]["Lib"] = lib_name

        if row["rule_state"] == "生效":
            print("work %d" % row["rule_id"])
            json_context += "\n\n" + json.dumps(rule_tag)
            rule_json.pop("lib_data_so", None)
            rule_json.pop("lib_data_conf", None)
            content += "\n\n" + json.dumps(rule_json).replace("\n", "")
    # os.system("echo \"\">"+filename)
    # print("*************   write file ********************")
    # print(context)
    write_file(rule_filename, content)
    # print("*************   write file end ********************")


def execute_sql(sql, connection):
    cursor = connection.cursor()
    cursor.execute(sql)
    result = cursor.fetchall()
    cursor.close()
    return result

def execute_cud_sql(sql,connection):
    cursor = connection.cursor()
    cursor.execute(sql)
    connection.commit()


def sync_rules(task_id, batch_id, task_path):
    save_batch_pcap(batch_id)
    rule_sql = f"""
    SELECT rule_id, rule_level, rule_name, rule_desc, rule_size, capture_mode, rule_json, created_time, pb_drop, pcap_drop, updated_time, rule_state, lib_respond_open, lib_respond_lib, lib_respond_config, lib_respond_session_end, lib_data_so, lib_data_conf, rule_type 
    FROM tb_rule 
    WHERE rule_state = '生效' AND task_id = {task_id};
    """
    create_and_write_rule(rule_sql, "rule.json", task_path)
    os.system("echo \"\">ruletag.json")
    write_file("ruletag.json", json_context)
    user_rule_path = Path(task_path) / "JsonRule/BasicRule/UserRule"
    user_rule_path.mkdir(parents=True, exist_ok=True)
    shutil.copyfile("rule.json", user_rule_path / "rule.json")

def sync_filters(task_id, task_path):
    filter_json = {}
    results_filter_context = []

    config_str = '{"Basic":{"DeviceNO":"123-456","Mission":"testn","OfflineFolder":"./OfflineFolder","FirstPro":12},"PFRing":{"Interface":["wlp3s0"],"ThreadNum":2,"RenewInterval_Sec":120,"CPU":[1,2,3,4],"BalanceCore":11},"Connect":{"IsIP":1,"IsUDP":1,"SYNSign":0},"ConnectInfor":{"ConnectNum_IPv4":10000,"ConnectNum_IPv6":1232,"TimeInterval":90},"LanProbe":{"FirstPro":[10,12,113,277],"SaveType_Pcap":1,"StartTime":"2018-01-19 16:51:00","LogFolder":"./FlowLog","RuleFolder":"./JsonRule/BasicRule","EngineFolder":"./JsonRule/BasicEngine","SaveFolder":"./PcapFolder","SaveFlow":0,"SaveFirstPacket":1,"RuleLog_Folder":"./FlowLog/RuleLog","RuleLog_MinLevel":20},"TileraFrame":{"PageNum":1,"rx":["eth0","eth1"],"GroupNumOfEachLink":8,"BucketNumOfEachLink":8,"RingNumOfEachLink":8,"BucketMode":1,"NodeOfEachRing":65536},"Extract_HTTP_Title":["^Sec-WebSocket","^Get","^Post","^HTTP","^Referer","^User-","^Server","^Date","^Cookie","^Host","^Last-Modified","^Expires","^Content","^Connect","^Accept","^Access","^Origin","^x-xss","^x-content","^x-frame","^strict-transport","^public-key","^Age","^ETag","^Location","^Proxy","^Retry","^Vary","^www-Auth","^upgrade"],"Extract":{"LogFolder":"./FlowLog/Extract","PcapFolder":"./PcapFolder/Extract_AbnormalPacket","CertFolder":"./FlowLog/Cert","BlockNum":1024,"IsConnect":1,"IsDNSQuery":1,"IsDNS":1,"IsHTTP":1,"IsTSL":1},"ProtocolAnalyse":{"SaveFolder":"./PcapFolder/ProAnalyse","TLS_HandShake":1,"AbnormalPro":1,"AbnormalIPTTL":5},"KeepOrder":{"PacketNum":10},"Port53":{"IPNum":50000,"Folder":"./FlowLog/Port53","TimeInterval":86400},"BytesDistribute":{"MaxBytes":2147483648,"MinBytes":2048,"Duration":60},"PlusIn_Json":{},"IPStatistic":{"VIP":[],"Network":[],"DMZ":[],"SaveFolder":"./PcapFolder","MacNum":1000,"IPv4Num":10000,"IPv6Num":5000,"TimeInterval_IPClear":1600,"TimeInterval_IPInfor":120,"SYNSample":1048575,"DDOS":{"LogFolder":"./FlowLog/DDOS_Log","TimeInterval_Judge":30,"Threshold_Packet":1000,"Times_SYN_Judge":4,"Times_Connect_Judge":4,"Hour_BasicLine_Judge":24,"Times_BasicLine_Judge":10},"IntraIPNum":0,"MacNum_Extra":0},"DDOS_Defense":{"Type":4,"RenewTimeInterval":30,"IntraIPv4":[{"IP":"***********","Mask":"***********"}],"IntraMac":[],"IPv4Num":200000,"IPv6Num":5000,"DDOS":{"TimeInterval_Judge":6,"Times_TCP":20,"Times_DNS":20,"Times_ICMP":20,"Times_UDP":20,"mbps_Basic":10,"PacketNum_Basic":1000}}}'
    config_json = json.loads(config_str)

    filter_results = execute_sql(
        f"SELECT b.id, b.ip, a.state, b.filter_json FROM tb_filter_state a, tb_filter_config b WHERE b.status = 1 AND a.task_id = b.task_id AND b.task_id = {task_id}",
        get_connection())

    if not filter_results:
        filter_state = execute_sql(
            f"SELECT state FROM tb_filter_state WHERE task_id = {task_id}", get_connection())
        filter_response = "pass" if filter_state[0]["state"] == 0 else "drop"
        config_json['Filter'] = {"Respond": filter_response, "Rule": []}
    else:
        filter_response = "pass" if filter_results[0]["state"] == 0 else "drop"
        filter_json['Respond'] = filter_response
        for result in filter_results:
            filter_rule_json = json.loads(result['filter_json'])
            filter_rule_json['ID'] = result['id']
            if "ip" in filter_rule_json and filter_rule_json["ip"] == "":
                del filter_rule_json["ip"]
            results_filter_context.append(filter_rule_json)
        filter_json['Rule'] = results_filter_context
        config_json['Filter'] = filter_json
    # print(json.dumps(config_json))
    os.system("echo \"\">Config.txt")
    write_file("Config.txt", json.dumps(config_json))
    shutil.copy("Config.txt", Path(task_path) / "Config/Config.txt")


def sync_fullflow_and_flowlog(batch_id, task_path):
    sql = f"SELECT fullflow_state, flowlog_state FROM th_analysis.tb_task_batch WHERE batch_id = {batch_id}"
    results = execute_sql(sql, get_connection())

    tree_fullflow = ET.parse(Path(task_path) / 'write_pcap.xml')
    root_fullflow = tree_fullflow.getroot()
    for elem in root_fullflow.iter('b_rule_save'):
        elem.text = 'false' if results[0]['fullflow_state'] == 'ON' else 'true'
    tree_fullflow.write(Path(task_path) / 'write_pcap.xml')

    with open(Path(task_path) / "plugin_conf.json", 'r') as file:
        data = json.load(file)
    should_log_value = 1 if results[0]['flowlog_state'] == 'ON' else 0
    data['proto_parse']['should_log_def'] = should_log_value
    for plugin_obj in data['proto_parse']['plugin']:
        plugin_obj['should_log'] = should_log_value
    with open(Path(task_path) / "plugin_conf.json", 'w') as file:
        json.dump(data, file, indent=4)

def insert_batch_offline_thd(task_id,batch_id,service_id,service_name):
    sql = "insert into tb_batch_offline_thd(task_id,batch_id,service_id,service_name) values (" + str(task_id) + "," + str(batch_id) + "," + str(service_id) + ",'" + str(service_name) + "')"
    execute_cud_sql(sql,get_connection())

# 导入数据
def process_import_request(task_id, batch_id, input_dirs):
    # 查询空闲服务
    service_id = find_idle_service()
    service_name = get_service_name(service_id)
    print(f"Found idle service: {service_name}")

    conf_path = Path(f"/conf/{service_id}/{service_name}")
    task_info = {"task_id": task_id, "batch_id": batch_id}
    write_file(conf_path / "task_info.json", json.dumps(task_info))

    os.system(f"rm -f {conf_path}/file_index.txt")
    with open(conf_path / 'file_index.txt', "a") as file_index:
        for input_dir in input_dirs:
            file_index.write(input_dir + '\n')
    # 增加一个用于判断是否处理到最后的文件名，不需要文件存在
    with open(conf_path / 'file_index.txt', 'a') as file:
        file.write('end.pcap\n')
    with open(conf_path / "task_pcap_read.conf", 'w') as file:
        file.truncate(0)
    # 同步过滤规则
    sync_filters(task_id, conf_path)
    # 同步特征规则
    sync_rules(task_id, batch_id, conf_path)
    # 同步全流量留存和元数据留存
    sync_fullflow_and_flowlog(batch_id, conf_path)
    # 记录任务批次和探针服务的关联信息
    insert_batch_offline_thd(task_id,batch_id,service_id,service_name)
    # 启动服务
    print(f"systemctl restart {service_name}")
    os.system(f"systemctl restart {service_name}")


def kafka_listener():
    consumer = KafkaConsumer('offline_thd', bootstrap_servers='127.0.0.1:9092')
    for message in consumer:
        print("get kafka msg success")
        message_value = message.value.decode('utf-8')
        message_data = json.loads(message_value)
        task_id = message_data.get('task_id')
        batch_id = message_data.get('batch_id')
        input_dirs = message_data.get('input_dirs')
        pcap_files = find_pcap_files(input_dirs)
        update_batch_pcap_num(batch_id, len(pcap_files))
        process_import_request(task_id, batch_id, pcap_files)
    consumer.close()

def update_batch_pcap_num(batch_id,pcap_num):
    sql = "update tb_task_batch set pcap_num = "+str(pcap_num)+" where batch_id = " + str(batch_id)
    execute_cud_sql(sql, get_connection())

def find_pcap_files(input_dirs):
    pcap_files = []
    for path in input_dirs:
        if os.path.isfile(path):  # 如果是文件
            if path.endswith(('.pcap', '.cap', '.pcapng')):
                pcap_files.append(path)
        elif os.path.isdir(path):  # 如果是文件夹
            # 递归查找文件夹中的所有 .pcap, .cap, .pcapng 文件
            # pcap_files.extend(glob.glob(os.path.join(path, '**/*.{pcap,cap,pcapng}'), recursive=True))
            for root, _, files in os.walk(path):
                for file in files:
                    if file.endswith(('.pcap', '.cap', '.pcapng')):
                        pcap_files.append(os.path.join(root, file))
    return pcap_files


if __name__ == "__main__":
    kafka_listener()

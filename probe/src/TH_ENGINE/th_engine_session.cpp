#include "th_engine.h"
#include "th_engine_session.h"
#include "th_engine_packet.h"
#include "minimize_protobuf.h"
#include "Interface/src/TH_ENGINE/PacketAndRule.h"
#include "crc64.h"
#include "KafkaWriter.h"


int _b_create_session(PacketInfo *p_pinfo)
{
    c_packet *p_packet = &p_pinfo->packet;

    if(p_packet -> dst_port == 443 ||   p_packet -> src_port == 443 ||  (p_packet -> dst_port > 10000 &&  p_packet -> src_port > 10000))
    {
        return 0 ;
    }
    return 1;
}

static void send_jsondata(KafkaWriter *pKafkaWriter, handle_thread_info * p, int nThread, bool should_send, bool session_send)
{
    Json::FastWriter fast_writer;
    uint64_t offset = 0;
    // 数据反序列化:
    for(int i = 0 ; i < p->value.pb_use_num ; i++)
    {
        p->p_msg_parse_engine->handle(p->value.get(i));//处理待发送消息
        if(should_send)
        {
            if(30 == p->value.get_type(i) && false == session_send)
            {
                continue;
            }
            p -> value.get(i) -> SerializeToString(&(p->str));
            if(pKafkaWriter)
            {
                p->MsgTypeConde[p->value.get_type(i)] ++ ;
                p->MsgTypeByte[p->value.get_type(i)] += p->str.length() ;
                pKafkaWriter->sendData(pKafkaWriter->getMetadataTopic(), NULL, 0, (void *)p->str.c_str(), p->str.length());
            }
            //printf("%s\n",str.c_str());
            if(p->p_send != NULL)
            {
                p->p_send-> SendData(p->str.length(), (char *)p->str.c_str());
            }
            if (NULL != p->p_write)
            {
                uint32_t pbts = th_engine_tools::instance()->clock_ts[0];
                string filename = p->p_write->SendData(p->str.length(), (char *)p->str.c_str(), pbts, nThread, &offset);
                if(p->p_jwrite && p->value.jget(i) && p->value.has_json(i))
                {
                    CJsonMsg *p_msg = p->value.jget(i);
                    p_msg->add_index(filename, offset);
                    if(p_msg->is_complete())
                    {
                        p->p_jwrite->SendData(p_msg->get_offset(), (char *)p_msg->get_buf(), pbts, nThread);
                    }
                }
            }
        }
        else if(p->p_white_write)
        {
            if(30 == p->value.get_type(i))
            {
                minimize_pb::SsSerializeToString(p -> value.get(i), &(p->str));
                p->p_white_write->SendData(p->str.length(), (char *)p->str.c_str(), th_engine_tools::instance()->clock_ts[0], nThread);
            }
        }
    }
    p -> value.init();

}

static bool analysis_should_send(session_pub * p_session_pub)
{
    if(NULL==p_session_pub)
    {
        if(config_and_define::b_rule_pb_save)       //按规则留存
        {
            return false;
        }
        else        //全留存
        {
            return true;
        }
    }
    else
    {
        if(p_session_pub->session_basic.RuleNum + p_session_pub->packet_sign_num)    //命中规则
        {
            bool should_send = false;
            for(int i = 0; i < p_session_pub->session_basic.RuleNum && should_send == false; i ++)
            {
                if(0 == PacketAndRule::instance()->GetRuleAttribute()->BitMapGet(WHITE_PB_IDX, p_session_pub->session_basic.pRule[i]))
                {
                    should_send = true;
                    break;
                }
            }
            for(uint8_t i = 0; i < p_session_pub->packet_sign_num && should_send == false; i ++)
            {
                if(0 == PacketAndRule::instance()->GetRuleAttribute()->BitMapGet(WHITE_PB_IDX, p_session_pub->packet_sign[i]))
                {
                    should_send = true;
                    break;
                }
            }
            return should_send;
        }
        else        //未命中规则
        {
            if(config_and_define::b_rule_pb_save)       //按规则留存
            {
                return false;
            }
            else        //全留存
            {
                return true;
            }
        }
    }
}

int th_engine::resources_recovery(session * p_session , int thread_id)
{
    bool should_send = false;
    unsigned int rule_level;
    session_pub *p_sess_pub = (session_pub *) p_session -> session_pub;   
    if(p_sess_pub == NULL)  return 0;
    alert_target_key atk;
    uint32_t rule_save = p_sess_pub->session_basic.RuleNum;
    c_packet::rule_respond(p_sess_pub);
    for(uint32_t i = rule_save; i < p_sess_pub->session_basic.RuleNum; i ++)
    {
        if((rule_level = GetAppLevel(p_sess_pub->session_basic.pRule[i])) >= 61 )
        {
            memset(&atk, 0, sizeof(alert_target_key));
            atk.target_type=6;
            atk.rule_id=p_sess_pub->session_basic.pRule[i];
            atk.target_info.session_id = p_sess_pub->session_basic.ConnectKeyID;
            if(th_engine_tools::instance()->get_alert_token(thread_id,atk))
            {
                char ssid_buf[21];
                sprintf(ssid_buf, "%llu", p_sess_pub->session_basic.ConnectKeyID);
                vector<string> reasons;
                th_engine_tools::instance()->send_alarm(thread_id, p_sess_pub->session_basic.pRule[i], \
                        rule_level, 0, \
                        string(ssid_buf), "session", reasons
                    );
            }
        }
    }
    should_send = analysis_should_send(p_sess_pub);
    p_sess_pub -> p_value  = &(p_thread_info[thread_id].value);

    p_thread_info[thread_id].p_proto_parse_handle->recovery_session(p_sess_pub);
    if(p_sess_pub->p_md_tcp)
    {
        ((CMD_TCP *)p_sess_pub->p_md_tcp)->SessionEnd();
    }
    p_thread_info[thread_id].p_full_flow_engine->resources_recovery(p_sess_pub);
    send_jsondata(pKafkaWriter, &(p_thread_info[thread_id]), thread_id, should_send,p_sess_pub->do_session_pb);
    p_sess_pub -> p_value = NULL;
    return 0;
}
int th_engine::session_recovery(session * p_session , int thread_id)
{
    session_pub *p_sess_pub = (session_pub *) p_session -> session_pub;   
    if(p_sess_pub == NULL)  return  0;
    p_sess_pub -> p_value  = &(p_thread_info[thread_id].value);
    p_thread_info[thread_id].p_full_flow_engine->session_recovery(p_sess_pub);

    p_thread_info[thread_id].p_proto_parse_handle->session_recovery(p_sess_pub);
    // 添加 统计 
    // 
    p_thread_info[thread_id].del_session_num ++ ;
    int packetnum = p_sess_pub ->session_basic.pPacketNum[0] + p_sess_pub ->session_basic.pPacketNum[1];
    int num = packetnum / 4 ;
    if (num  > 7  ) num = 7;
    uint16_t dstport;
    if(p_sess_pub->b_has_proxy)
    {
        dstport = p_sess_pub->proxy_real_port;
    }
    else
    {
        if (p_sess_pub -> session_basic.Server ==  PACKETFROMUNKOWN)
        {
            if (p_sess_pub->session_basic.pPort[0] > p_sess_pub->session_basic.pPort[1])
            {
                dstport = p_sess_pub->session_basic.pPort[1];
            }
            else
            {
                dstport = p_sess_pub->session_basic.pPort[0];
            }
        }
        else if(p_sess_pub -> session_basic.Server == PACKETFROMCLIENT )
        {
            dstport = p_sess_pub->session_basic.pPort[1];
        }
        else
        {
            dstport = p_sess_pub->session_basic.pPort[0];
        }
    }
    if(p_sess_pub -> session_basic.IPPro == 6)
    {
        p_thread_info[thread_id].tcp_pn_arr[num] ++ ;
        p_th_sum->tcp_pkt_list[dstport][thread_id] += packetnum;
        p_th_sum->tcp_pkt_list_loop[dstport][thread_id] += packetnum;
    }
    else if(p_sess_pub -> session_basic.IPPro == 17)
    {
        p_thread_info[thread_id].udp_pn_arr[num] ++ ;
        p_th_sum->udp_pkt_list[dstport][thread_id] += packetnum;
        p_th_sum->udp_pkt_list_loop[dstport][thread_id] += packetnum;
    }
    if (p_sess_pub->session_basic.pIP[0].IsIPv6() || p_sess_pub->session_basic.pIP[1].IsIPv6())
    {
        th_engine_g_var.ipv6_session[thread_id] ++; 
        th_engine_g_var.ipv6_pkt[thread_id] += packetnum;
    }
    else
    {
        th_engine_g_var.ipv4_session[thread_id] ++;
        th_engine_g_var.ipv4_pkt[thread_id] += packetnum;
    }
    if (p_sess_pub->b_first_ip)
    {
        th_engine_g_var.firstip_session[thread_id] ++;
        th_engine_g_var.firstip_pkt[thread_id] += packetnum;
    }
    if(p_sess_pub->session_basic.ProtoSign >= p_th_sum->min_app_id && p_sess_pub->session_basic.ProtoSign <= p_th_sum->max_app_id)
    {
        uint16_t index = p_th_sum->app_id_2_index[p_sess_pub->session_basic.ProtoSign-p_th_sum->min_app_id];
        if(index)
        {
            p_th_sum->app_pkt_list[index][thread_id] += packetnum;
            p_th_sum->app_pkt_list_loop[index][thread_id] += packetnum;
        }
    }
    if(p_sess_pub->p_md_tcp)
    {
        p_thread_info[thread_id].p_cmd_tcp_marge ->push((CMD_TCP *)p_sess_pub->p_md_tcp);
    }
    if(p_sess_pub->p_session_ext)
    {
        if(p_sess_pub->p_session_ext->tcp_link[0])
        {
            p_thread_info[thread_id].p_tcp_link_marge -> push(p_sess_pub->p_session_ext->tcp_link[0]);
        }
        if(p_sess_pub->p_session_ext->tcp_link[1])
        {
            p_thread_info[thread_id].p_tcp_link_marge -> push(p_sess_pub->p_session_ext->tcp_link[1]);
        }
        p_sess_pub->p_tcp_recom_push_cb(p_sess_pub, 0);
        p_sess_pub->p_tcp_recom_push_cb(p_sess_pub, 1);
        p_sess_pub->p_session_ext->init();
        p_thread_info[thread_id].p_sess_ext_marge -> push(p_sess_pub->p_session_ext);
    }
    p_sess_pub -> init();
    p_thread_info[thread_id].p_sess_pub_marge -> push(p_sess_pub);
    return 0;
}

int th_engine::packet_relate_session(int thread_id)
{
    PacketInfo *p_pinfo = &(p_thread_info[thread_id].pinfo);
    c_packet *p_packet = &p_pinfo->packet;

    if(p_packet->flags.b_no_session == 0)
    {
        session * p_session = find_session(p_pinfo,p_thread_info[thread_id].p_session_hash);
        p_packet->sy_crc64 = p_pinfo->sy_crc64;
        if(NULL == p_session)
        {
            int  n = 0;
            if(b_create_session)
            {
                n = b_create_session(p_pinfo);
            }
            if(0 == n)
            {
                p_session = create_session(p_pinfo,p_thread_info[thread_id].p_session_hash );
                while(NULL == p_session)
                {
                    session_bucket_timeout(thread_id);
                    p_session = create_session(p_pinfo,p_thread_info[thread_id].p_session_hash );
                }
                p_session -> sy_crc64 =  p_packet->sy_crc64 ;
                tmo_session_update(p_session , p_thread_info[thread_id].p_tmo_marge);
                p_pinfo->p_session = p_session;
                return 0;
            }
            else
            {
                p_packet->flags.b_no_session = 1;
                p_packet->flags.b_no_create = 1;
                th_engine_g_var.drop_nocreate_pkt[thread_id] ++;
                return 1;
            }
        }
        else
        {
            tmo_session_update(p_session , p_thread_info[thread_id].p_tmo_marge);
            p_pinfo->p_session = p_session;
            return 0;
        }
    }
    else
    {
        p_packet->sy_crc64 = 0;
        return 1;
    }
}


int th_engine::get_session_id(int thread_id, uint32_t &ssid_low, uint32_t &ssid_high)
{
    PacketInfo *p_pinfo = &(p_thread_info[thread_id].pinfo);
    session *p_session = (session *)p_pinfo->p_session;
    ssid_low = 0;
    ssid_high = 0;
    if(p_session && p_session -> session_pub)
    {
        session_pub *p_session_pub = (session_pub *) p_session -> session_pub;
        ssid_low = (uint32_t)(p_session_pub->session_basic.ConnectKeyID & 0xffffffffULL);
        ssid_high = (uint32_t)((p_session_pub->session_basic.ConnectKeyID >> 32) & 0xffffffffULL);
        return 0;
    }
    return -1;
}


int th_engine::session_handle(int thread_id)
{
    PacketInfo *p_pinfo = &(p_thread_info[thread_id].pinfo);
    c_packet *p_packet = &p_pinfo->packet;
    session *p_session = (session *)p_pinfo->p_session;
    session_pub *p_session_pub = NULL;
    bool should_send = false;
    unsigned int rule_level;
    int black = 0, white = 0, important = 0;

    if(p_session)
    {
        session_pub *p_session_pub = (session_pub *) p_session -> session_pub;
        if(p_session_pub == NULL)
        {
            p_session_pub = p_thread_info[thread_id].p_sess_pub_marge ->pop();
            // sum
            p_thread_info[thread_id].all_session_num ++ ;
            p_thread_info[thread_id].cre_session_num ++ ;
            th_engine_g_var.session_num[thread_id] ++;
            if(p_packet -> p_tcphdr  != NULL  )
            {
                p_thread_info[thread_id].tcp_session_num ++ ;
            }
            else if (p_packet -> p_udphdr  != NULL  ) 
            {
                p_thread_info[thread_id].udp_session_num ++ ;
                if(p_packet -> src_port ==53 || p_packet -> dst_port == 53) 
                {
                    p_thread_info[thread_id].dns_session_num ++ ;
                }

            }
            if(p_packet -> src_port == 123|| p_packet -> dst_port == 123) 
            {
                    p_thread_info[thread_id].ntp_session_num ++ ;
            }
            // sum --end
            // session_pub_init
            p_session_pub->init(p_packet) ;
            p_session_pub->thread_id = thread_id;
            p_session_pub->task_id = config_and_define::task_id;
            p_session_pub -> p_value = &(p_thread_info[thread_id].value);
            
            int signlen = p_pinfo->signlen;
            memcpy(p_pinfo->buff + signlen, p_packet->clock_ts, 2*sizeof(uint32_t));
            signlen += 2*sizeof(uint32_t);
            memcpy(p_pinfo->buff + signlen, (void *)&thread_id, sizeof(int));
            signlen += sizeof(int);

            p_session_pub->session_basic.ConnectKeyID = crc64(p_session_pub->session_basic.ConnectKeyID, (const unsigned char*)p_pinfo->buff, signlen);
            if(p_packet->p_tcphdr)
            {
                CMD_TCP *pcmd_tcp = p_thread_info[thread_id].p_cmd_tcp_marge -> pop();
                if(pcmd_tcp)
                {
                    pcmd_tcp->Reset();
                }
                p_session_pub->p_md_tcp = pcmd_tcp;
            }
            p_session -> session_pub = (void *) p_session_pub ;
            // session_pub_init --end

            // pkt directory
            p_packet->Directory = 0;
            p_packet->m_str_packet_moudle.IsFirstPacket = 1;
            // p_packet -> ProtocolInspect(p_session_pub);
        }
        else
        {
            if(p_session_pub->session_basic.pIP[0] == p_packet->src_ip && p_session_pub->session_basic.pPort[0] == p_packet->src_port)
            {
                p_packet->Directory = 0;
            }
            else
            {
                p_packet->Directory  = 1;
            }
            p_packet->m_str_packet_moudle.IsFirstPacket = 0;
        }
        p_packet->m_str_packet_moudle.Directory = p_packet->Directory;
            // pkt directory --end

        // must p_session_pub != NULL

        //judge session Server by syn flag
        if(PACKETFROMUNKOWN == p_session_pub->session_basic.Server)
        {
            if(p_packet -> p_tcphdr)
            {
                if(0x02 == *((uint8_t *)(p_packet->p_tcphdr) + 13))
                {
                    if(p_packet->Directory == 0)
                    {
                        p_session_pub->session_basic.Server = PACKETFROMCLIENT;
                    }
                    else
                    {
                        p_session_pub->session_basic.Server = PACKETFROMSERVER;
                    }
                }
                else if(0x12 == *((uint8_t *)(p_packet->p_tcphdr) + 13))
                {
                    if(p_packet->Directory == 0)
                    {
                        p_session_pub->session_basic.Server = PACKETFROMSERVER;
                    }
                    else
                    {
                        p_session_pub->session_basic.Server = PACKETFROMCLIENT;
                    }
                }
            }
        }
        //judge session Server by syn flag --end

        //pkt b_c2s
        if(p_session_pub->session_basic.Server == PACKETFROMCLIENT)
        {
            if(p_packet->Directory == 0)
            {
                p_packet->b_c2s = PACKETFROMCLIENT;
            }
            else
            {
                p_packet->b_c2s = PACKETFROMSERVER;
            }
        }
        else if(p_session_pub->session_basic.Server == PACKETFROMSERVER)
        {
            if(p_packet->Directory == 0)
            {
                p_packet->b_c2s = PACKETFROMSERVER;
            }
            else
            {
                p_packet->b_c2s = PACKETFROMCLIENT;
            }
        }
        //pkt b_c2s --end

        //session io_sign
        if(p_packet->Directory == 0)
        {
            p_session_pub->session_basic.IO_Sign[0] = p_packet->IO_Sign[0];
            p_session_pub->session_basic.IO_Sign[1] = p_packet->IO_Sign[1];
        }
        else
        {
            p_session_pub->session_basic.IO_Sign[0] = p_packet->IO_Sign[1];
            p_session_pub->session_basic.IO_Sign[1] = p_packet->IO_Sign[0];
        }
        //session io_sign --end
        if(p_packet->port_id < 16)
        {
            p_session_pub->port_map |= (1 << p_packet->port_id);
        }
        
        // session_pub update session_time
        if (p_packet -> time_ts[0] < p_session_pub -> session_basic.StartTime[0])
        {
            p_session_pub -> session_basic.StartTime[0] = p_packet -> time_ts[0];
            p_session_pub -> session_basic.StartTime[1] = p_packet -> time_ts[1];
        }
        else if (p_packet -> time_ts[0] == p_session_pub -> session_basic.StartTime[0])
        {
            if (p_packet -> time_ts[1] < p_session_pub -> session_basic.StartTime[1])
            {
                p_session_pub -> session_basic.StartTime[1] = p_packet -> time_ts[1];
            }
        }
        if (p_packet -> time_ts[0] > p_session_pub -> session_basic.EndTime[0])
        {
            p_session_pub -> session_basic.EndTime[0] = p_packet -> time_ts[0];
            p_session_pub -> session_basic.EndTime[1] = p_packet -> time_ts[1];
        }
        else if (p_packet -> time_ts[0] == p_session_pub -> session_basic.EndTime[0])
        {
            if (p_packet -> time_ts[1] > p_session_pub -> session_basic.EndTime[1])
            {
                p_session_pub -> session_basic.EndTime[1] = p_packet -> time_ts[1];
            }
        }
        p_session_pub->EndClock = p_packet -> clock_ts[0];
        // session_pub update session_time --end

        // Decode_APP md_tcp and match_rule
        p_packet->rule(p_session_pub) ;
        alert_target_key atk;
        for(int i = 0; i < p_packet->rt_sign_num; i ++)
        {
            if(p_packet->rt_rule_sign[i] >= 169001 && p_packet->rt_rule_sign[i] <= 170000)
            {
                continue;
            }
            if((rule_level = GetAppLevel(p_packet->rt_rule_sign[i])) >= 61)
            {
                memset(&atk, 0, sizeof(alert_target_key));
                atk.target_type=6;
                atk.rule_id=p_packet->rt_rule_sign[i];
                atk.target_info.session_id = p_session_pub->session_basic.ConnectKeyID;
                if(th_engine_tools::instance()->get_alert_token(thread_id,atk))
                {
                    p_packet->alert_num ++;
                    char ssid_buf[21];
                    sprintf(ssid_buf, "%llu", p_session_pub->session_basic.ConnectKeyID);
                    vector<string> reasons;
                    th_engine_tools::instance()->send_alarm(thread_id, p_packet->rt_rule_sign[i], \
                            rule_level, 0, \
                            string(ssid_buf), "session", reasons
                        );
                    th_engine_g_var.alarm_num[thread_id][0]++;
                }
            }
        }
        for (int i = 0; i < p_packet->sign_num; i ++)
        {
            if(p_packet->packet_sign[i] >= 150001 && p_packet->packet_sign[i] <= 170000)
            {
                continue;
            }
            if((rule_level = GetAppLevel(p_packet->packet_sign[i])) >= 61)
            {
                memset(&atk, 0, sizeof(alert_target_key));
                atk.target_type=6;
                atk.rule_id=p_packet->packet_sign[i];
                atk.target_info.session_id = p_session_pub->session_basic.ConnectKeyID;
                if(th_engine_tools::instance()->get_alert_token(thread_id,atk))
                {
                    p_packet->alert_num ++;
                    char ssid_buf[21];
                    sprintf(ssid_buf, "%llu", p_session_pub->session_basic.ConnectKeyID);
                    vector<string> reasons;
                    if(p_packet->offset_to[i] && (p_packet->offset_from[i] < p_packet->offset_to[i]) && p_packet->offset_to[i]<=p_packet->packet_len)
                    {
                        string payload;
                        unsigned int hexlen = p_packet->offset_to[i]-p_packet->offset_from[i];
                        if(hexlen > 128)
                        {
                            hexlen = 128;
                        }
                        unsigned int asclen = hexlen * 2 + 1, outlen = 0;
                        unsigned char *p_asc= new unsigned char[asclen];
                        reasons.push_back("regex");
                        HexToAscII((unsigned char *)p_packet->buf + p_packet->offset_from[i], hexlen, p_asc, asclen, outlen);
                        reasons.push_back(string((char *)p_asc));
                        delete []p_asc;
                    }
                    th_engine_tools::instance()->send_alarm(thread_id, p_packet->packet_sign[i], \
                        rule_level, 0, \
                        string(ssid_buf), "session", reasons
                    );
                    th_engine_g_var.alarm_num[thread_id][0]++;
                }
            }
        }
        p_packet -> p_app_data = p_packet -> app_buf ;
        p_packet -> app_data_len =  p_packet -> app_len  ;
        // Decode_APP md_tcp and match_rule --end

        //update session PacketNum/PacketBytes
        p_session_pub->session_basic.pPacketNum[p_packet->Directory]++;
        p_session_pub->session_basic.pPacketBytes[p_packet->Directory] += p_packet->packet_len;
        p_session_pub->pkt_payloadbytes[p_packet->Directory] += p_packet->app_len;
        if(p_packet->payload_len)
        {
            p_session_pub->session_basic.pPayloadNum[p_packet->Directory]++;
        }

        if(p_session_pub -> session_basic.ProtoSign != p_packet -> proto_parse_sign )
        {
           p_packet  -> proto_parse_sign =  p_session_pub -> session_basic.ProtoSign ;
        }

        //small session -> big session
        if(NULL == p_session_pub->p_session_ext && 1 == p_session_pub->session_basic.pPacketNum[0] && 0 == p_session_pub->session_basic.pPacketNum[1])
        {
            if(p_session_pub->session_basic.IPPro != 17 || (PROTOCOL_APP_DNS != p_session_pub->session_basic.ProtoSign && PROTOCOL_APP_NTP != p_session_pub->session_basic.ProtoSign))
            {
                p_session_pub->p_session_ext = p_thread_info[thread_id].p_sess_ext_marge ->pop();
                if(p_session_pub->p_session_ext && p_session_pub->session_basic.IPPro == 6)
                {
                    p_session_pub->p_session_ext->tcp_link[0] = p_thread_info[thread_id].p_tcp_link_marge -> pop();
                    p_session_pub->p_session_ext->tcp_link[1] = p_thread_info[thread_id].p_tcp_link_marge -> pop();
                    if(p_session_pub->p_session_ext->tcp_link[0] && p_session_pub->p_session_ext->tcp_link[1])
                    {
                        p_session_pub->p_session_ext->tcp_link[0]->clear();
                        p_session_pub->p_session_ext->tcp_link[1]->clear();
                    }
                    else if (p_session_pub->p_session_ext->tcp_link[0])
                    {
                        p_thread_info[thread_id].p_tcp_link_marge->push(p_session_pub->p_session_ext->tcp_link[0]);
                        p_session_pub->p_session_ext->tcp_link[0] = NULL;
                    }
                }
            }
        }
        //small session -> big session --end

        // 规则已经走 
        //  数据转换ETH  session 转换
        //  session 定义的session 信息
        //printf(" p_session_pub -> proto_parse_sign == %d  p_packet -> proto_parse_sign  ==== %d \n",p_session_pub -> proto_parse_sign , p_packet -> proto_parse_sign);
        p_thread_info[thread_id].p_full_flow_engine->handle(p_packet , p_session_pub);
        // pand
        if(0 == p_packet->flags.b_proxy_drop || 0 == p_packet->flags.b_bad_tcp_hdrlen)    //small session not use proto_parse
        {
            p_thread_info[thread_id].p_proto_parse_handle->handle(p_session_pub,p_packet );
        }
        if(p_thread_info[thread_id].value.pb_use_num)
        {
            should_send = analysis_should_send(p_session_pub);
        }
        if(p_session_pub != NULL){
            p_session_pub->target_scan_handle_cb(p_packet->src_ip.ip_str(), thread_id, black, white, important);//TODO:重点目标数的单位是包还是会话？
            if(white){
                th_engine_g_var.white_list_hit_pkts[thread_id]++;
                th_engine_g_var.white_list_hit_bytes[thread_id] += p_pinfo->len;
            }
            if(important){
                th_engine_g_var.important_target_num[thread_id]++;
            }
        }
    }
    else
    {
        p_packet -> p_value = &(p_thread_info[thread_id].value);
        p_thread_info[thread_id].p_full_flow_engine->handle(p_packet , NULL);
        if(p_thread_info[thread_id].value.pb_use_num)
        {
            should_send = analysis_should_send(NULL);
        }
    }
    send_jsondata(pKafkaWriter, &(p_thread_info[thread_id]), thread_id, should_send, p_session_pub?p_session_pub->do_session_pb:1);

    return 0;
}
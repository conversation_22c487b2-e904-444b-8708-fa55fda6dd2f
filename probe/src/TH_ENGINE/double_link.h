// Last Update:2018-08-30 12:05:33
/**
 * @file double_link.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-08-26
 */

#ifndef DOUBLE_LINK_H
#define DOUBLE_LINK_H
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

typedef struct _node{
    struct _node *prevNode;
    struct _node *nextNode;
    void *data;
}LinkNode;

//链表结构体，记录了链表的首节点和尾节点指针，以及节点总个数
struct _list{
    struct _node *firstNode;
    struct _node *lastNode;
    int len;
};

typedef int (*cmp_ptr)(void * node_data , void * data);
void  node_init(LinkNode * p );
void lpush(struct _list *list, struct _node *data);
void lpush_data(struct _list *list, void *data);
void rpush(struct _list *list, struct _node *data);
void rpush_data(struct _list *list,void  *data);
LinkNode * lpop(struct _list *list) ;
void loutlistnode(struct _list *list , struct _node * node );
void lpushnode(struct _list *list , struct _node * node );
LinkNode  * find_node(struct _list  *list ,void * data ,cmp_ptr func );

#endif  /*DOUBLE_LINK_H*/

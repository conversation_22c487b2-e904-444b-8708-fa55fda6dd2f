// Last Update:2018-10-02 11:10:49
/**
 * @file handle.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-08-30
 */

#ifndef HANDLE_H
#define HANDLE_H
#include <stdio.h>
#include "session_pub.h"
#include "resources_marge.h"
#include <InterfaceBase.h>
#include "full_flow/full_flow_engine.h"

#define PROTOCOL_APP_DNS 10071
#define PROTOCOL_APP_NTP 10413

extern "C" 
{
void bind_SEngineHandleBase(SEngineHandleBase& m);
};
void tcp_recombine_pop_callback(session_pub *p_session, uint8_t  Directory);
void tcp_recombine_push_callback(session_pub *p_session, uint8_t  Directory);

#endif  /*HANDLE_H*/

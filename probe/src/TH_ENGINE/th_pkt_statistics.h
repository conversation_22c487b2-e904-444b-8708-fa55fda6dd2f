#ifndef __TH_PKT_STATISTICS_H__
#define __TH_PKT_STATISTICS_H__
#include <string>
using namespace std;

class th_pkt_sum
{
    public:
        uint64_t **app_pkt_list;
        uint32_t **app_pkt_list_loop;
        uint64_t **tcp_pkt_list;
        uint32_t **tcp_pkt_list_loop;
        uint64_t **udp_pkt_list;
        uint32_t **udp_pkt_list_loop;
        uint16_t *app_id_2_index;
        uint16_t *app_index_2_id;
        uint16_t app_num;
        int thread_num;
        uint16_t min_app_id;
        uint16_t max_app_id;
        th_pkt_sum();
        ~th_pkt_sum();
        int init(int tn);
};

// extern th_pkt_sum g_th_pkt_sum;

#endif
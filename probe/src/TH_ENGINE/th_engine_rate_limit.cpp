#include <string>
#include <fstream>
#include "th_engine_rate_limit.h"
#include "json/reader.h"
#include "json/value.h"


using namespace std;

static unsigned int round_to_pow2(unsigned int x)
{
    if(0 == x)
    {
        return 1;
    }
    x--;
    x |= x >> 1;
    x |= x >> 2;
    x |= x >> 4;
    x |= x >> 8;
    x |= x >> 16;
    x++;
    return x;
}


th_engine_rate_limiter::th_engine_rate_limiter(unsigned int bucket)
{
    Json::Value value;
    Json::Reader reader;
    string conf_path;
    int th_id, offline_mode;

    this->bucket = round_to_pow2(bucket);
    mask=this->bucket - 1;
    target_mbps = -1;
    dividing_line = 0;

    conf_path = string(getenv("THE_CONFPUB_PATH")) + "/ifconf.json";
    th_id = atoi(getenv("THE_ID"));
    offline_mode = atoi(getenv("THE_OFFLINE_MODE"));
    if (offline_mode)
    {
        return;
    }

    ifstream ifs(conf_path);
    if(ifs.is_open())
    {
        if(reader.parse(ifs,value))
        {
            if (value.isMember("task") && value["task"].isArray() && value["task"].size() > th_id)
            {
                if (value["task"][th_id].isMember("limit_mbps") && value["task"][th_id]["limit_mbps"].isNumeric())
                {
                    target_mbps = value["task"][th_id]["limit_mbps"].asUInt();
                    target_mbps = target_mbps * 12 / 10;
                    if(0 == target_mbps)
                    {
                        target_mbps = 1;
                    }
                }
            }
        }
    }
}

int th_engine_rate_limiter::re_calculate_dividing_line(unsigned int mbps_now)
{
    if(mbps_now > target_mbps)
    {
        dividing_line = (mbps_now - target_mbps) * bucket / mbps_now;
    }
    else
    {
        dividing_line = 0;
    }
}

int th_engine_rate_limiter::check_drop_pkt(unsigned int pkt_hash)
{
    if(dividing_line)
    {
        pkt_hash = pkt_hash & mask;
        if(pkt_hash < dividing_line)
        {
            return 1;
        }
    }
    return 0;
}

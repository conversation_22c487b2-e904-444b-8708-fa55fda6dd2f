// Last Update:2019-04-03 17:21:55
/**
 * @file double_link.c
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-08-26
 */

#include "double_link.h" 
void  node_init(LinkNode * p )
{
    p -> data  = NULL; 
    p->prevNode = NULL ;
    p->nextNode = NULL ;
}
// 头部加入节点
void lpush(struct _list *list, struct _node * node)
{
    //struct _node *node = malloc(sizeof(struct _node));
    //bzero(node, sizeof(struct _node));
    //node->data = data;
    node->nextNode = list->firstNode;
    if (list->firstNode != NULL)
    {
        list->firstNode->prevNode = node;
    }
    list->firstNode = node;
    if (list->len == 0)
    {
        list->lastNode = node;
    }
    list->len++;
}
 
void lpush_data(struct _list *list, void * data)
{
    struct _node *node = (struct _node *) malloc(sizeof(struct _node));
    bzero(node, sizeof(struct _node));
    node->data = data;
    node->nextNode = list->firstNode;
    if (list->firstNode != NULL)
    {
        list->firstNode->prevNode = node;
    }
    list->firstNode = node;
    if (list->len == 0)
    {
        list->lastNode = node;
    }
    list->len++;
}
 
// 尾部加入节点
void rpush(struct _list *list, struct _node * node)
{
    node->prevNode = list->lastNode;
    if (list->lastNode != NULL)
    {
        list->lastNode->nextNode = node;
    }
    list->lastNode = node;
    if (list->len == 0)
    {
        list->firstNode = node;
    }
    list->len++;
}
void rpush_data(struct _list *list, void * data)
{
    struct _node *node = (struct _node *) malloc(sizeof(struct _node));
    bzero(node, sizeof(struct _node));
    node->data = data;
    node->prevNode = list->lastNode;
    if (list->lastNode != NULL)
    {
        list->lastNode->nextNode = node;
    }
    list->lastNode = node;
    if (list->len == 0)
    {
        list->firstNode = node;
    }
    list->len++;
}
LinkNode * find_node(struct _list  *list ,void * data ,cmp_ptr func )
{
    struct _node * node = list-> firstNode;
    while(node != NULL )
    {
        if(func(node ->data,data) == 0)
        {
            return node ;
        }
        node = node -> nextNode ;
    }
}
// 
void lpushnode(struct _list *list , struct _node * node )
{
    // -------- 
    node  -> prevNode = NULL ;
    node -> nextNode = NULL ;
    if(list -> firstNode == NULL) 
    {
        list -> firstNode = node ; 
        list -> lastNode = node ;
        return  ;
    }
    node  -> prevNode =  list -> lastNode  ;
    if(list -> lastNode != NULL)
    {
        list -> lastNode ->nextNode = node ; 
    }
    list -> lastNode  =  list -> lastNode ->nextNode ;
    // -----
}
// 已知链表中的节点， 删除链表
void loutlistnode(struct _list *list , struct _node * node )
{
    //
    if (node ==list -> firstNode)
    {
        list -> firstNode = list -> firstNode -> nextNode ;
        if(list -> firstNode != NULL)
            list -> firstNode -> prevNode = NULL ;
    }
    if(list -> lastNode == node )
    {
         list -> lastNode = node -> prevNode ;
         if(list -> lastNode != NULL)
            list -> lastNode -> nextNode = NULL ;
    }
    else 
    {
        if(node -> prevNode  != NULL) 
        {
            node -> prevNode -> nextNode = node -> nextNode ;
        }
        if(node -> nextNode != NULL ) 
        {
            node -> nextNode -> prevNode = node -> prevNode ;
        }
    }
    node  -> prevNode = NULL  ;
    node  -> nextNode = NULL ;
}
// 删除§
void * delete_node(struct _list  list ,void * data ,cmp_ptr func)
{
     struct _node * node = list.firstNode;
     void * pdata = NULL;
     // 头指针 为空
     if(func((void *)(node ->data)  ,data )  == 0)
     {
        list .firstNode = node ->  nextNode; 
        pdata = node -> data ;
        if(list.lastNode ==  node ) 
        {
            list.lastNode = node  ->prevNode ;
        }
        free(node);
     }
     else {
         while(node != NULL )
         {
             if(func(node ->data  ,data ) ) 
             {
                 node->prevNode ->nextNode = node -> nextNode ;
                 if(node -> nextNode != NULL) 
                 {
                    node -> nextNode ->prevNode = node -> prevNode ;
                 }
                 else 
                 {
                    list .lastNode = node  ->prevNode ;
                 }
                pdata = node -> data ;
                free(node);
                break ;
             }
             node = node -> nextNode ;
         }

     }
     return data;

}

// 头部弹出节点
LinkNode * lpop(struct _list *list)
{
    if (list->firstNode == NULL)
    {
        return NULL ;
    }

    struct _node * p = NULL ; 
    if (list->firstNode->nextNode != NULL)
    {
        list->firstNode = list->firstNode->nextNode;
        p = (list->firstNode->prevNode);
        list->firstNode->prevNode = NULL;
    }
    else
    {
        p = list->firstNode;
        list->firstNode = NULL;
    }

    list->len--;

    if (list->len == 0)
    {
        list->lastNode = NULL;
    }
    return  p ;
}




// 尾部弹出节点
struct  _node * rpop(struct _list *list)
{
    struct _node * p = NULL ; 
    if (list->lastNode == NULL)
    {
        return NULL;
    }


    if (list->lastNode->prevNode != NULL)
    {
        list->lastNode = list->lastNode->prevNode;
        p = list->lastNode->nextNode;
        list->lastNode->nextNode = NULL;
    }
    else
    {
        p = list->lastNode;
        list->lastNode = NULL;
    }


    list->len--;
    if (list->len == 0)
    {
        list->firstNode = NULL;
    }
    return p ;
}


// 获取指定位置上的节点值
void *getVal(struct _list list, int pos)
{
    if (pos < 0)
    {
        return NULL;
    }


    int i;
    struct _node *curNode = list.firstNode;
    for(i = 0; i < pos; i++)
    {
        if(curNode->nextNode != NULL)
        {
            curNode =  curNode->nextNode;
        }
        else
        {
            curNode = NULL;
            break;
        }
    }


    if (curNode == NULL){
        return NULL;
    }
    return curNode->data;
}




static void print_r(struct _list list)
{
    printf("-----------------------------------------\n");
    printf("list.len = %d\n", list.len);
    int i = 0;
    struct _node *node = list.firstNode;
    while(1)
    {
        if (node == NULL)
        {
            break;
        }


        printf("list[%d]: %s\n", i, node->data);
        node = node->nextNode;
        i++;
    }
}



/*int main()
{
    struct _list list = {NULL, NULL, 0};


    lpush(&list, "node 0");
    lpush(&list, "node 1");
    lpush(&list, "node 2");
    rpush(&list, "node 3");
    rpush(&list, "node 4");
    print_r(list);


    lpop(&list);
    rpop(&list);
    print_r(list);


    lpop(&list);
    lpop(&list);
    lpop(&list);
    rpop(&list);
    rpop(&list);
    print_r(list);


    rpush(&list, "hello node");
    print_r(list);


}*/

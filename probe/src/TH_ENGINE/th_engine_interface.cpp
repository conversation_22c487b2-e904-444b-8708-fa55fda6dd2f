#include "th_engine_interface.h"
#include "th_engine.h"

void *th_engine_new()
{
    th_engine *p_engine = new th_engine();
    return p_engine;
}
int th_engine_init(void *arg, int thread_num, uint8_t port_num)
{
    th_engine *p_engine = (th_engine *)arg;
    return p_engine->init(thread_num, port_num);
}
int th_engine_close(void *arg)
{
    th_engine *p_engine = (th_engine *)arg;
    return p_engine->close();
}
int th_engine_start_log(void *arg, int lcore)
{
    th_engine *p_engine = (th_engine *)arg;
    return p_engine->start_log(lcore);
}
int th_engine_send_log(void *arg, const char *topic, void *pKey, unsigned int keyLen, void *pValue, unsigned int valueLen)
{
    th_engine *p_engine = (th_engine *)arg;
    return p_engine->pKafkaWriter->sendData(topic, pKey, keyLen, pValue, valueLen);
}
int th_engine_thread_init(void *arg, int thread_id)
{
    th_engine *p_engine = (th_engine *)arg;
    return p_engine->thread_init(thread_id);
}
int th_engine_thread_close(void *arg, int thread_id)
{
    th_engine *p_engine = (th_engine *)arg;
    return p_engine->thread_close(thread_id);
}
int th_engine_pkt_handle(void *arg, int first_proto, unsigned char *buf, uint32_t len, uint32_t *ts, int thread_id, uint8_t port_id)
{
    th_engine *p_engine = (th_engine *)arg;
    return p_engine->pkt_handle(first_proto, buf, len,ts, thread_id, port_id);
}
int th_engine_timeout_handle(void *arg, int thread_id)
{
    th_engine *p_engine = (th_engine *)arg;
    return p_engine->timeout_handle(thread_id);
}
int th_engine_set_devinfo(void *arg, uint8_t port_id, char *pci, char *mac, char *name)
{
    th_engine *p_engine = (th_engine *)arg;
    return p_engine->set_devinfo(port_id, pci, mac, name);
}
uint64_t th_engine_pkt_hash(void *arg, int first_proto, unsigned char *buf, uint32_t len)
{
    th_engine *p_engine = (th_engine *)arg;
    return p_engine->pkt_hash(first_proto, buf, len);
}

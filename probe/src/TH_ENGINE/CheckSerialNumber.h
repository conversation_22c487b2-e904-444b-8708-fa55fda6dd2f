// Last Update:2019-03-09 20:25:41
/**
 * @file CheckSerialNumber.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-03-07
 */

#ifndef _CHECK_SERIAL_NUMBER_H
#define _CHECK_SERIAL_NUMBER_H
#include  <stdint.h>
#include <stdio.h>
#include <sys/wait.h>
#include <stdlib.h>
#include <unistd.h>
#include <string>
#include <stdint.h>
#include <sys/time.h>
#include <stdio.h>

        void CheckSerialNumber();
        bool check_time();
        bool check_bosssseq();


#endif  /*_CHECK_SERIAL_NUMBER_H*/

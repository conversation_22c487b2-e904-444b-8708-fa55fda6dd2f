#include "th_engine.h"
#include "Interface/src/TH_ENGINE/PacketAndRule.h"
#include "th_engine_log.h"
#include "th_read_conf.h"


th_engine::th_engine()
{
    thread_num = 0;
    for(int i = 0; i < MAX_THREAD_NUM; i ++)
    {
        work_thread_running[i] = 0;
    }
    log_thread_running = 0;
    p_thread_info = NULL;
    p_th_sum = NULL;
    b_create_session = NULL;
    thread_init_done = 0;
    thread_0_initdone = 0;
    port_num = 0;
    pKafkaWriter = NULL;
    pKafkaReader = NULL;
}

int th_engine::init(int thread_num, uint8_t port_num)
{
    if(thread_num < 1 || port_num < 1)
    {
        return -1;
    }
    this->thread_num = thread_num;
    this->port_num = port_num;
    config_and_define::port_num = (uint32_t)port_num;

    th_xml_parse();
    th_engine_tools::instance(thread_num);	//TODO
    read_conf_commit::GetInstance();

    p_thread_info = new handle_thread_info[thread_num];
    p_th_sum = new th_pkt_sum();
    p_th_sum->init(thread_num);
    read_pb_summary();

    return 0;
}

int th_engine::start_log(int lcore)
{
    int ret;
    cpu_set_t mask;
    pthread_attr_t pthr_attr;

    if(0 < thread_num)
    {
        th_engine_tools::instance()->start_clock_ts(lcore);
    }
    else
    {
        thread_init_done = -1;  //suspend task
    }

    if(lcore >= 0)
    {
        ret = pthread_attr_init(&pthr_attr);
        if(ret)
        {
            fprintf(stderr, "pthread_attr_init error\n");
            return -1;
        }
        CPU_ZERO(&mask);
        CPU_SET(lcore, &mask);
        ret = pthread_attr_setaffinity_np(&pthr_attr, sizeof(cpu_set_t), &mask);
        if (ret)
        {
            fprintf(stderr, "pthread_attr_setaffinity_np error\n");
            return -1;
        }
        ret = pthread_create(&t_log, &pthr_attr, th_log_loop, this);
    }
    else
    {
        ret = pthread_create(&t_log, NULL, th_log_loop, this);
    }
    
    if (ret != 0)
    {
        printf("can't create thread: %s\n", strerror(ret));
        return -1;
    }
    if(0 < thread_num)
    {
        for(int i = 0; i < 120; i ++)
        {
            if(pKafkaReader && pKafkaWriter && 0 != th_engine_tools::instance()->get_clock_ts())
            {
                break;
            }
            sleep(1);
        }
        if(pKafkaReader && pKafkaWriter && th_engine_tools::instance()->get_clock_ts())
        {
            if(0 == pKafkaWriter->isConstructOk())
            {
                cout << endl << "Kafka Writer Connect Failed!" << endl;
                delete pKafkaWriter;
                pKafkaWriter = NULL;
                return -3;
            }
            if(0 == pKafkaReader->isConstructOk())
            {
                cout << endl << "Kafka Reader Connect Failed!" << endl;
                delete pKafkaReader;
                pKafkaReader = NULL;
                return -4;
            }
            th_engine_tools::instance()->set_send_log_cb(send_log);
        }
        else
        {
            cout << endl << "Kafka Connect TimeOut!" << endl;
            return -2;
        }
        PacketAndRule::instance(thread_num, pKafkaReader, pKafkaWriter);
    }
    else
    {
        for(int i = 0; i < 120; i ++)
        {
            if(pKafkaWriter)
            {
                break;
            }
            sleep(1);
        }
        if(pKafkaWriter)
        {
            if(0 == pKafkaWriter->isConstructOk())
            {
                cout << endl << "Kafka Writer Connect Failed!" << endl;
                delete pKafkaWriter;
                pKafkaWriter = NULL;
                return -3;
            }
        }
        else
        {
            cout << endl << "Kafka Connect TimeOut!" << endl;
            return -2;
        }
        if(pKafkaReader)
        {
            delete pKafkaReader;
            pKafkaReader = NULL;
        }
    }
    return 0;
}

int th_engine::close()
{
    for(int i = 0; i < thread_num; i ++)
    {
        thread_info_close(i);
    }
    if(p_thread_info)
    {
        delete []p_thread_info;
        p_thread_info = NULL;
    }
    if(p_th_sum)
    {
        delete p_th_sum;
        p_th_sum = NULL;
    }
    return 0;
}

int th_engine::thread_init(int thread_id)
{
    if(0 == thread_id)
    {
        thread_info_init(thread_id);
        thread_0_initdone = 1;
    }
    while(0 == thread_0_initdone)
    {
        usleep(1);
    }
    if(thread_id)
    {
        thread_info_init(thread_id);
    }
    __sync_fetch_and_add(&thread_init_done, 1);

    return 0;
}

int th_engine::thread_close(int thread_id)
{
    return thread_info_close(thread_id);
}

int th_engine::pkt_handle(int first_proto, unsigned char *buf, uint32_t len, uint32_t *ts, int thread_id, uint8_t port_id)
{
    uint32_t ssid_low = 0, ssid_high = 0;
    p_thread_info[thread_id].pkt_handle ++;

    if(p_thread_info[thread_id].timeout_bucket > config_and_define::session_bucket_num)
    {
        p_thread_info[thread_id].timeout_bucket = config_and_define::session_bucket_num;
    }
    while(p_thread_info[thread_id].timeout_bucket > 0)
    {
        __sync_fetch_and_sub(&(p_thread_info[thread_id].timeout_bucket),1);
        session_bucket_timeout(thread_id);
    }

    packet_init(first_proto, buf, len, ts, thread_id, port_id);
    packet_parse(thread_id);
    packet_relate_session(thread_id);
    session_handle(thread_id);
    get_session_id(thread_id, ssid_low, ssid_high);
    *(uint32_t *)(buf+2) = ssid_low;
    *(uint32_t *)(buf+8) = ssid_high;

    return 0;

}
int th_engine::timeout_handle(int thread_id)
{
    if(p_thread_info[thread_id].timeout_bucket > config_and_define::session_bucket_num)
    {
        p_thread_info[thread_id].timeout_bucket = config_and_define::session_bucket_num;
    }
    while(p_thread_info[thread_id].timeout_bucket > 0)
    {
        __sync_fetch_and_sub(&(p_thread_info[thread_id].timeout_bucket),1);
        session_bucket_timeout(thread_id);
    }
    module_timeout(thread_id);
}
int th_engine::set_devinfo(uint8_t port_id, char *pci, char *mac, char *name)
{
    if(port_id < MAX_IF_NUM)
    {
        strncpy(config_and_define::port_pci[port_id], pci, 31);
        strncpy(config_and_define::port_mac[port_id], mac, 17);
        strncpy(config_and_define::port_pos[port_id], name, 127);
    }
}



























#include "th_engine_thread.h"
#include "session_hash.h"
#include "th_engine.h"
#include "read_conf_commit.h"

static handle_thread_info *g_p_thread_info = NULL;

void tcp_recombine_pop_callback(session_pub *p_sess_pub, uint8_t Directory)
{
    if(p_sess_pub->p_session_ext)
    {
        if(NULL == p_sess_pub->p_session_ext->tcp_repcom[Directory])
        {
            p_sess_pub->p_session_ext->tcp_repcom[Directory] = g_p_thread_info[p_sess_pub->thread_id].p_tcp_recb_marge -> pop();
        }
        if(p_sess_pub->p_session_ext->tcp_repcom[Directory])
        {
            p_sess_pub->p_session_ext->tcp_repcom[Directory]->clear();
        }
    }
    return;
}

void tcp_recombine_push_callback(session_pub *p_sess_pub, uint8_t Directory)
{
    if(p_sess_pub->p_session_ext)
    {
        if(p_sess_pub->p_session_ext->tcp_repcom[Directory])
        {
            g_p_thread_info[p_sess_pub->thread_id].p_tcp_recb_marge -> push(p_sess_pub->p_session_ext->tcp_repcom[Directory]);
            p_sess_pub->p_session_ext->tcp_repcom[Directory] = NULL;
        }
    }
    return;
}

int th_engine::thread_info_close(int thread_id)
{
    if(NULL == p_thread_info)
    {
        return 0;
    }
    handle_thread_info *p = &p_thread_info[thread_id];
    if(0 == thread_id)
    {
        g_p_thread_info = NULL;
    }
    resove_session_hash(p->p_session_hash);
    if(p->p_full_flow_engine)
    {
        delete p-> p_full_flow_engine;
    }
    if(p -> p_sess_pub_marge != NULL) 
    {
        delete   p -> p_sess_pub_marge ;
    }
    if(p -> p_sess_ext_marge != NULL) 
    {
        delete   p -> p_sess_ext_marge ;
    }
    if(p->p_tcp_link_marge != NULL)
    {
        delete p->p_tcp_link_marge;
    }
    if(p -> p_tcp_recb_marge != NULL) 
    {
        delete   p -> p_tcp_recb_marge ;
    }
    if(p -> p_cmd_tcp_marge != NULL)
    {
        delete p -> p_cmd_tcp_marge;
    }
    return 0;
}

int th_engine::thread_info_init(int thread_id)
{
    if(0 == thread_id)
    {
        g_p_thread_info = p_thread_info;
    }
    handle_thread_info *p = &p_thread_info[thread_id];
    p->pkt_handle = 0;
    p->timeout_bucket = 0;
    p->p_tmo_marge = time_out_marge_create(config_and_define::session_bucket_num);
    p->p_session_hash = (session_hash *) malloc(sizeof(session_hash));
    p->p_session_hash -> max_session_num = read_conf_commit::GetInstance() ->read_conf_session_num("session","thread_session_num");
    // p->msg.p_recv = N_queue_new(1000);
    // p->msg.p_send = N_queue_new(1000);
    session_hash_init(p->p_session_hash ) ;
    p->p_info = NULL ; 
    // pp_msg_queue[thread_id]=&(p_thread_info[thread_id].msg);

    p->p_sess_pub_marge = new session_marge<session_pub>();
    p->p_sess_ext_marge = new session_marge<session_pub_ext>(false);
    p->p_tcp_link_marge = new session_marge<TcpFragmentList>(false);
    p->p_tcp_recb_marge = new session_marge<TcpFragmentReassembly>(false);
    p->p_cmd_tcp_marge = new session_marge<CMD_TCP>(false);

    uint32_t thread_session_num = read_conf_commit::GetInstance() ->read_conf_session_num("session","thread_session_num");
    printf("thread_session_num == %u\n",thread_session_num);
    p->p_sess_pub_marge->init(thread_session_num);
    session_pub **pp_session_pub = new session_pub *[thread_session_num];
    for(uint32_t i=0; i < thread_session_num; i++)
    {
        pp_session_pub[i] = p->p_sess_pub_marge->pop();
    }
    for(uint32_t i=0; i < thread_session_num; i++)
    {
        c_packet::init_statistics_cmdtcp(pp_session_pub[i]);
        pp_session_pub[i]->p_tcp_recom_pop_cb = tcp_recombine_pop_callback;
        pp_session_pub[i]->p_tcp_recom_push_cb = tcp_recombine_push_callback;
        pp_session_pub[i]->get_appname = GetAppName;
        pp_session_pub[i]->domain_judge_cb = DomainJudge;
        pp_session_pub[i]->target_scan_handle_cb = target_scan_handle;
        p->p_sess_pub_marge->push(pp_session_pub[i]);
    }
    delete[] pp_session_pub;
    p->p_sess_ext_marge->init(read_conf_commit::GetInstance() ->read_conf_session_num("session","thread_session_ext_num"));
    p->p_tcp_link_marge->init(read_conf_commit::GetInstance() ->read_conf_session_num("session","thread_tcpsession_num") * 2);
    p->p_tcp_recb_marge->init(read_conf_commit::GetInstance() ->read_conf_session_num("session","thread_tcp_recb_session_num") * 2);
    p->p_cmd_tcp_marge->init(read_conf_commit::GetInstance() ->read_conf_session_num("session","thread_tcpsession_num"));
    p-> p_full_flow_engine = new full_flow_engine(thread_num, thread_id);
    if (config_and_define::b_zmq_active)
    {
        p->p_send = new  CZmqSend(config_and_define::zmq_addr.c_str(), config_and_define::zmq_len ,1);
    }
    if (config_and_define::b_save_pb_file)
    {
        string pb_path = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/pbfiles/";
        p->p_write = new PBWrite(config_and_define::b_pb_rename, pb_path);
    }
    if(config_and_define::b_save_json_file)
    {
        string json_path = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/outjson/";
        p->p_jwrite = new JsonWrite(json_path);
        p->value.set_json_available();
    }
    if (config_and_define::b_save_white_json)
    {
        string white_json_path = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/whitejson/";
        p->p_white_write = new PBWrite(config_and_define::b_pb_rename, white_json_path);
    }
    p->p_proto_parse_handle = new  proto_parse_handle();
    p->p_proto_parse_handle -> init("");
    p->p_msg_parse_engine = new  msg_handle_engine();
    return 0;
}

int th_engine::session_bucket_timeout(int thread_id)
{
    struct _list * session_list =  tmo_begin(p_thread_info[thread_id].p_tmo_marge); 
    if(session_list != NULL)
    {
        if(session_list -> len  != 0 )
        {
            struct _node * p = session_list -> firstNode ;
            int  i =  0; 
            for(;p != NULL &&  i < session_list -> len ; i++ ) 
            {
                struct _node * q = p ;
                if(p == p->nextNode) break  ;
                p = p -> nextNode ;
                session * p_session = (session *)q ->data;
                // 删除查询索引
                p_session -> use_num = -1;
                resources_recovery(p_session , thread_id);
                session_recovery(p_session , thread_id);
                del_session(p_thread_info[thread_id].p_session_hash,p_session);
            }
        }
        tmo_end(p_thread_info[thread_id].p_tmo_marge);
        free(session_list) ;
    }
    return 1;
}

int th_engine::module_timeout(int thread_id)
{
    if( p_thread_info!=NULL && p_thread_info[thread_id].p_write != NULL)
    {
        p_thread_info[thread_id].p_write -> timeout(th_engine_tools::instance()->clock_ts[0], thread_id);
    }
    if( p_thread_info!=NULL && p_thread_info[thread_id].p_jwrite != NULL)
    {
        p_thread_info[thread_id].p_jwrite -> timeout(th_engine_tools::instance()->clock_ts[0], thread_id);
    }
    if(p_thread_info!=NULL && p_thread_info[thread_id].p_white_write != NULL)
    {
        p_thread_info[thread_id].p_white_write -> timeout(th_engine_tools::instance()->clock_ts[0], thread_id);
    }
    if( p_thread_info!=NULL && p_thread_info[thread_id].p_full_flow_engine != NULL)
    {
        p_thread_info[thread_id].p_full_flow_engine -> module_timeout(th_engine_tools::instance()->clock_ts[0], &(p_thread_info[thread_id].value), (uint32_t)thread_id);
    }
    c_packet::timeout(config_and_define::b_segment_analyse, th_engine_tools::instance()->clock_ts[0]);
    
    for(int i = 0 ; i < p_thread_info[thread_id].value.pb_use_num; i++)
    {
        p_thread_info[thread_id].p_msg_parse_engine->handle(p_thread_info[thread_id].value.get(i));//处理待发送消息
        p_thread_info[thread_id].value.get(i) -> SerializeToString(&(p_thread_info[thread_id].str));
        //printf("%s\n",str.c_str());
        if(p_thread_info[thread_id].p_send != NULL)
        {
            p_thread_info[thread_id].p_send-> SendData(p_thread_info[thread_id].str.length(), (char *)p_thread_info[thread_id].str.c_str());
        }
        if (NULL != p_thread_info[thread_id].p_write)
        {
            p_thread_info[thread_id].p_write->SendData(p_thread_info[thread_id].str.length(), (char *)p_thread_info[thread_id].str.c_str(), th_engine_tools::instance()->clock_ts[0], thread_id);
        }
        if(pKafkaWriter)
        {
            p_thread_info[thread_id].MsgTypeConde[p_thread_info[thread_id].value.get_type(i)] ++ ;
            p_thread_info[thread_id].MsgTypeByte[p_thread_info[thread_id].value.get_type(i)] += p_thread_info[thread_id].str.length() ;
            pKafkaWriter->sendData("meta", NULL, 0, (void *)p_thread_info[thread_id].str.c_str(), p_thread_info[thread_id].str.length());
        }
    }
    //0 == value.json_use_num
    p_thread_info[thread_id].value.init();
}
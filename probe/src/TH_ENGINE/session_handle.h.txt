// Last Update:2019-06-11 14:00:48
/**
 * @file session_handle.h
 * @brief : session 处理流程 , 提供接口对外访问 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-08-28
 */

#ifndef SESSION_HANDLE_H
#define SESSION_HANDLE_H
#include "InterfaceBase.h"

extern "C" 
{
    void  session_handle_PrecessInit( SEngineHandleBase * pBase,int numThread, uint8_t port_num);
    void  packetInfo_init(PacketInfo * p_packet);
    void  session_handle_PrecessClose(SEngineHandleBase * pBase);
    void  session_handle_ThreadInit(SEngineHandleBase * pBase, int nThread);// 第多少个线程 
    void  session_handle_ThreadClose(SEngineHandleBase * pBase, int nThread );// 第多少个线程 
    void  Handle(SEngineHandleBase * pBase,char * buf , int len ,int nThread, int first_proto, uint8_t port_id);
    void  Handle_ns(SEngineHandleBase * pBase,char * buf , int len , uint32_t time_ns[2],int nThread, int first_proto, uint8_t port_id);
    uint64_t Hash_Handle(SEngineHandleBase * pBase, unsigned char *buf, uint32_t len, int first_proto);
    void recovery_session(session * p_session , int nThread);
    int TimeOut(SEngineHandleBase * pBase  ,int  nThread ,uint32_t s);
    int TimeOutLan(SEngineHandleBase * pBase  ,int  nThread ,uint32_t s);
    void Time_Handle(SEngineHandleBase * pBase  ,int  nThread, uint32_t timeout_ts);
    void SetDevInfo(SEngineHandleBase * pBase  ,int portid , char *pci, char *mac, char *pos);
    void link_ntpsrc(); //fuck to link libengine.a -> libth_engine.so
};
#endif  /*SESSION_HANDLE_H*/

// Last Update:2019-02-21 14:37:31
/**
 * @file hash.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-08-26
 */

#ifndef HASH_H
#define HASH_H

/* 哈希表的C实现
   查找使用的方法是“除留余数法”，解决冲突使用的方法是“链地址法”。
   */
#include<stdio.h>
#include<stdint.h>
#include<malloc.h> //malloc 
#include<string.h> //memset
#define FALSE 0
#define TRUE 1
typedef int STATUS;
#define HASHMAX 65535
//定义哈希表和基本数据节点
typedef struct _NODE
{
    uint64_t data;
    void * data_str ;
    struct _NODE* next;
}NODE;

typedef struct _HASH_TABLE
{
    NODE* value[HASHMAX];
}HASH_TABLE;

HASH_TABLE* create_hash_table();
NODE* find_data_in_hash(HASH_TABLE* pHashTbl, uint64_t data);
STATUS insert_data_into_hash(HASH_TABLE* pHashTbl, uint64_t data,void * data_str);
STATUS delete_data_from_hash(HASH_TABLE* pHashTbl, uint64_t data);

#endif  /*HASH_H*/

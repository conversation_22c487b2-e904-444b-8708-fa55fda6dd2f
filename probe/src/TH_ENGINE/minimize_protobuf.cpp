#include "minimize_protobuf.h"

bool minimize_pb::SsSerializeToString(JKNmsg *pmsg, string *pstr)
{
    JKNmsg minmsg;
    minmsg.set_type(30);
    single_session_msg* p_single_session_msg = minmsg.mutable_single_session();
    Comm_msg* p_comm = p_single_session_msg -> mutable_comm_msg();
    ss_pkt_msg* p_ss_pkt = p_single_session_msg -> mutable_ss_pkt();
    p_comm ->set_src_ip(pmsg->single_session().comm_msg().src_ip());
    p_comm ->set_dst_ip(pmsg->single_session().comm_msg().dst_ip());
    p_comm ->set_src_port(pmsg->single_session().comm_msg().src_port());
    p_comm ->set_dst_port(pmsg->single_session().comm_msg().dst_port());
    p_comm ->set_ippro(pmsg->single_session().comm_msg().ippro());
    p_comm ->set_begin_time(pmsg->single_session().comm_msg().begin_time());
    p_single_session_msg ->set_end_time(pmsg->single_session().end_time());
    p_ss_pkt -> set_pkt_snum(pmsg->single_session().ss_pkt().pkt_snum());
    p_ss_pkt -> set_pkt_dnum(pmsg->single_session().ss_pkt().pkt_dnum());
    p_ss_pkt -> set_pkt_sbytes(pmsg->single_session().ss_pkt().pkt_sbytes());
    p_ss_pkt -> set_pkt_dbytes(pmsg->single_session().ss_pkt().pkt_dbytes());
    return minmsg.SerializeToString(pstr);
}
#ifndef __TH_ENGINE_H__
#define __TH_ENGINE_H__

#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include "th_engine_interface.h"
#include "th_pkt_statistics.h"
#include "th_engine_thread.h"
#include "th_engine_rate_limit.h"
#include "KafkaWriter.h"
#include "KafkaReader.h"

#define PROTOCOL_APP_HTTP 10637
#define PROTOCOL_APP_DNS 10071
#define PROTOCOL_APP_NTP 10413

#define LOG_N_SEC 30

class th_engine
{
public:
    th_engine();
    int init(int thread_num, uint8_t port_num);
    int close();
    int thread_init(int thread_id);
    int thread_close(int thread_id);
    int start_log(int lcore);
    int pkt_handle(int first_proto, unsigned char *buf, uint32_t len, uint32_t *ts, int thread_id, uint8_t port_id);
    int timeout_handle(int thread_id);
    int set_devinfo(uint8_t port_id, char *pci, char *mac, char *name);
    uint64_t pkt_hash(int first_proto, unsigned char *buf, uint32_t len);

    int b_thread_init_done() { return (thread_init_done < thread_num)?0:1;}
    int log_handle(uint32_t ns_time);

    int thread_num;
    uint64_t work_thread_running[MAX_THREAD_NUM];    //自检
    uint64_t log_thread_running;
    handle_thread_info *p_thread_info;
    KafkaWriter *pKafkaWriter;
    KafkaReader *pKafkaReader;
    th_pkt_sum *p_th_sum;
    int (*b_create_session)(PacketInfo *ppinfo);     //_b_create_session 返回0时创建
private:
    int session_bucket_timeout(int thread_id);
    int resources_recovery(session * p_session , int thread_id);
    int session_recovery(session * p_session , int thread_id);
    int packet_relate_session(int thread_id);
    int session_handle(int thread_id);
    int module_timeout(int thread_id);
    int packet_init(int first_proto, unsigned char *buf, uint32_t len, uint32_t *ts, int thread_id, uint8_t port_id);
    int packet_parse(int thread_id);
    int thread_info_init(int thread_id);
    int thread_info_close(int thread_id);
    int get_session_id(int thread_id, uint32_t &ssid_low, uint32_t &ssid_high);
    pthread_t t_log;
    int thread_init_done;
    int thread_0_initdone;
    uint8_t port_num;
    PacketInfo hash_pkt;
    th_engine_rate_limiter rate_limter;
};
#endif
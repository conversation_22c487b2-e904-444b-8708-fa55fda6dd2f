// Last Update:2019-03-28 11:42:15
/**
 * @file time_out_marge.h
 * @brief : time out按集合管理 ,每次取一个集合处理 ,处理完之后放入队尾
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-08-28
 */

#ifndef TIME_OUT_MARGE_H
#define TIME_OUT_MARGE_H

#include "double_link.h"
#include "th_engine_session.h"
typedef struct time_out_marge_ 
{
    struct _list ** time_out_list ; // 超时桶 
    int use_num ;// 正在使用的桶 ，
    int  tmo_num ; // 即将超时的桶
    int max_num ; // 最大桶

}time_out_marge;
time_out_marge  *  time_out_marge_create(int max_num);
struct _list * tmo_begin(time_out_marge * tm_marge);
void  tmo_end(time_out_marge * tm_marge);
void tmo_session_update(session * p_session , time_out_marge * tm_marge);
void tmo_session_delete(session * p_session , time_out_marge * tm_marge);
void destroy_time_out_marge(time_out_marge * tm_marge);
#endif  /*TIME_OUT_MARGE_H*/

// Last Update:2019-04-08 18:24:40
/**
 * @file session_hash.c
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-08-27
 */
#include "session_hash.h"
#include  "crc64.h"
#include  "crc32.h"
#define SESSIONINTNM 1024
void ** p_malloc =  NULL; 
void printf_bit(char * p , int len )
{
    int i = 0 ;
      for (i = 0 ; i< len; i++)
      {
           //  unsigned int x = (((bit1<<i)&p) !=0);
             printf("%x", p[i]);
      } 

}
uint64_t t_packet_crc64(PacketInfo * p )
{
   // printf("\n");
    uint64_t  t = 0 ;
   // printf_bit (p -> buff ,  p -> signlen  );
    p->sy_crc64 = crc64( t , (const unsigned char *)p -> buff ,  p -> signlen );
    //p->sy_crc64 = calculate_CRC32( (void*)p -> buff ,  p -> signlen );
    p -> index =(uint32_t) p->sy_crc64;
//    p->sy_crc64 = calculate_CRC32( (void *)p -> buff ,  p -> signlen );
//    printf("\np->sy_crc64 = %lld \n", p-> sy_crc64);
}
int cmp_pstr_session(void * session_ , void * vpstr)
{
    //
    session * p_session = (session *) session_ ;
    PacketInfo * p_pstr = ( PacketInfo *) vpstr ;
    if(p_session -> sy_crc64 == p_pstr -> sy_crc64) 
    {  
        return 0 ;
    }
    return -1;
}
int cmp_session_session(void * session_ , void * vpstr)
{
    // 
    session * p_session = (session *) session_ ;
    session * p_s = ( session*) vpstr ;
   
    if(p_session -> sy_crc64 == p_s -> sy_crc64) 
    {  
        return 0 ;
    }
    return -1;
}
void session_init(session * p_session)
{
    p_session -> session_pub = NULL ;
    node_init(&(p_session -> tmo_node));
    p_session ->tmo_node.data = (void *)p_session;
    p_session -> use_num = -1;
}
void session_packet_value(session * p_session , PacketInfo * p_pstr) 
{
    p_session -> sy_crc64 = p_pstr -> sy_crc64 ;
    p_session -> index = p_pstr -> index ;
}
void session_hash_init(session_hash * p_session_hash) 
{
    p_session_hash -> p_hl  = (struct hash_link *)malloc(sizeof(struct hash_link)) ;
    p_session_hash -> p_hl -> p_head = create_hash_table();
    hd_init(p_session_hash -> p_hl);
    p_session_hash -> mSqstack = (SqStack *) malloc(sizeof(SqStack)) ;
    int i= 0 ;
    // 记录 malloc 块 ，准备资源回收
   // p_malloc = (void **) malloc(sizeof(void *) *  p_session_hash -> max_session_num / SESSIONINTNM + 1) ;
    //memset((void *)p_malloc ,0x0 , sizeof(void *) *  p_session_hash -> max_session_num / SESSIONINTNM + 1);
    int mnum = 0 ;
   // for(;i < p_session_hash -> max_session_num; i=i+SESSIONINTNM)
    {
        session * p = (session * ) malloc(sizeof(session)*p_session_hash -> max_session_num);
        int ii =  0 ;
        for(;ii < p_session_hash -> max_session_num; ii ++) 
        {
            // p[ii].session_id = ii ;
            session_init(&(p[ii]));
    //        Push(p_session_hash -> mSqstack,(void *)&(p[i]));
        }
        p_malloc = NULL ;
       // p_malloc[mnum] = (void *) p ;
        //  定义长度
        InitStack(p_session_hash -> mSqstack,sizeof(session),p_session_hash -> max_session_num , (char * )p);
    }

}
void resove_session_hash(session_hash * p_session_hash )
{
    int i = 0 ;
    // 
    free(p_session_hash -> p_hl);
}
static  int cum_session  = 0 ;
session * create_session( PacketInfo  * p_pstr, session_hash * p_session_hash)
{
    cum_session ++ ;
   // printf("cum_session   session   = %d\n",cum_session);
    //printf("%llu === p_packet -> index \n",p_pstr->index );
    session * p = NULL ;
    Pop(p_session_hash -> mSqstack,(void **)&p);
    if(p == NULL) 
    {
        // 资源不足 ， 需要主动发起回收资源操作
        return p ;
    }
    session_init(p);
    hd_insert(p_session_hash -> p_hl,p_pstr->index ,(void *) p);
    session_packet_value(p,p_pstr);

    return (session *) p ;
}


// 删除session 数据
int del_session(session_hash * p_session_hash ,session *  p_session) 
{
    cum_session -- ;
    hd_delete(p_session_hash -> p_hl , p_session->index , (void * ) p_session , cmp_session_session ) ;
    session_init(p_session);
    Push(p_session_hash -> mSqstack , p_session);
    return 1; 
}
int del_session_hash(session_hash * p_session_hash ,session *  p_session)
{
    hd_delete(p_session_hash -> p_hl , p_session->index , (void * ) p_session , cmp_session_session ) ;
    cum_session -- ;
    return 1;
}
int session_tostack(session_hash * p_session_hash ,session *  p_session)
{
    session_init(p_session);
    Push(p_session_hash -> mSqstack , p_session);
    return 1; 
}



// 完成  ---- 
session * find_session( PacketInfo  * p_pstr, session_hash * p_session_hash)
{
    // 查询p_packe
    // t_packet_crc64(p_pstr);  
    //printf("find session ==  %llu\n", p_pstr->sy_crc64  );
    session * p = (session *)hd_find(p_session_hash -> p_hl , p_pstr->index , (void *)p_pstr,cmp_pstr_session);
    if(p == NULL) 
    {
        p = (session *)hd_find(p_session_hash -> p_hl , p_pstr->index , (void *)p_pstr,cmp_pstr_session);
    }
    return (session *) p ;
}

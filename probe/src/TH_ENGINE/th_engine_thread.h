#ifndef __TH_ENGINE_THREAD_H__
#define __TH_ENGINE_THREAD_H__
#include "resources_marge.h"
#include "session_pub.h"
#include "MD_TCP.h"
#include "full_flow/full_flow_engine.h"
#include "proto_parse/proto_parse_handle.h"
#include "msg_handle/msg_handle_engine.h"
#include "Message.h"
#include "ZmqSend.h"
#include "PBWrite.h"
#include "JsonWrite.h"
#include "session_hash.h"
#include "time_out_marge.h"
#include "th_engine_packet.h"
#include "th_engine_session.h"

#define MAX_MSG_TYPE 10000

// typedef struct msg_queue_
// {
//     NQueue * p_recv ;
//     NQueue * p_send ;
// }msg_queue ;

class handle_thread_info
{
public:
    handle_thread_info()
    {
        pinfo.packet.p_value = &value ;
        p_send = NULL;
        p_write = NULL;
        p_white_write = NULL;
        p_jwrite = NULL;
        str.resize(65535);
        flush_syn_num = 0;
        all_session_num  = 0 ;
        cre_session_num = 0 ;
        udp_session_num  = 0 ;
        tcp_session_num  = 0 ;
        dns_session_num  = 0; 
        ntp_session_num = 0 ; 
        del_session_num  = 0;
        memset(tcp_pn_arr, 0, sizeof(uint32_t) * 8);
        memset(udp_pn_arr, 0, sizeof(uint32_t) * 8);
        ts_last = 0;
        uts_last = 0;
        nts_last = 0;
        MsgTypeCondeInit();
    }
    ~handle_thread_info()
    {
        if (NULL != p_write)
        {
            delete p_write;
            p_write = NULL;
        }
        if(p_white_write)
        {
            delete p_white_write;
            p_white_write = NULL;
        }
        if(p_jwrite)
        {
            delete p_jwrite;
            p_jwrite = NULL;
        }
    }

    session_marge<session_pub> * p_sess_pub_marge ; // 每个session中存储
    session_marge<session_pub_ext> * p_sess_ext_marge ; // 每个session中存储
    session_marge<TcpFragmentList> * p_tcp_link_marge ; // 每个session中存储
    session_marge<TcpFragmentReassembly> * p_tcp_recb_marge ; // 每个session中存储
    session_marge<CMD_TCP> * p_cmd_tcp_marge ; // 每个session中存储
    full_flow_engine * p_full_flow_engine ;// 全流处理引擎
    proto_parse_handle * p_proto_parse_handle ; // 协议解析引擎
    msg_handle_engine * p_msg_parse_engine ; // 消息处理引擎引擎
    PacketInfo pinfo;
    CMessage value ;
    CZmqSend* p_send;
    PBWrite* p_write;
    PBWrite* p_white_write;
    JsonWrite* p_jwrite;
    
    std::string str;//(65535, 0);
    char * buff ;
    //  *******************************
    uint64_t flush_syn_num; //丢弃的syn包数量
    //  *******************************
    uint64_t all_session_num ;
    uint32_t cre_session_num ;
    uint32_t udp_session_num ;
    uint32_t tcp_session_num ;
    
    uint32_t dns_session_num ;
    uint32_t ntp_session_num ;
    uint32_t del_session_num;
    uint32_t tcp_pn_arr[8];
    uint32_t udp_pn_arr[8];
    uint32_t ts_last;
    uint32_t uts_last;
    uint32_t nts_last;
    uint32_t MsgTypeConde[MAX_MSG_TYPE] ;// 统计各个类型消息    ------   定时统计
    uint32_t MsgTypeByte[MAX_MSG_TYPE] ;// 统计各个类型消息    ------   定时统计
    void MsgTypeCondeInit()
    {
        memset(MsgTypeConde,0x0,MAX_MSG_TYPE * sizeof(uint32_t)) ;
        memset(MsgTypeByte,0x0,MAX_MSG_TYPE * sizeof(uint32_t)) ;
    }
    session_hash * p_session_hash ;
    time_out_marge * p_tmo_marge ;
    uint32_t ts ;
    uint32_t  timeout_bucket; // 已超时桶个数
    void * p_info ;// 用户自己使用的对象
    // msg_queue msg ;
    uint64_t pkt_handle;
};

#endif
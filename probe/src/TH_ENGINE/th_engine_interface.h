#ifndef __TH_ENGINE_INTERFACE_H__
#define __TH_ENGINE_INTERFACE_H__

#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>

#define MAX_THREAD_NUM 64
#define MAX_PORT_NUM 16

extern "C" 
{
    void *th_engine_new();
    int th_engine_init(void *p_engine, int thread_num, uint8_t port_num);
    int th_engine_close(void *p_engine);
    int th_engine_start_log(void *p_engine, int lcore);
    int th_engine_send_log(void *p_engine, const char *topic, void *pKey, unsigned int keyLen, void *pValue, unsigned int valueLen);
    int th_engine_thread_init(void *p_engine, int thread_id);
    int th_engine_thread_close(void *p_engine, int thread_id);
    int th_engine_pkt_handle(void *p_engine, int first_proto, unsigned char *buf, uint32_t len, uint32_t *ts, int thread_id, uint8_t port_id);
    int th_engine_timeout_handle(void *p_engine, int thread_id);
    int th_engine_set_devinfo(void *p_engine, uint8_t port_id, char *pci, char *mac, char *name);
    uint64_t th_engine_pkt_hash(void *p_engine, int first_proto, unsigned char *buf, uint32_t len);
};


#endif
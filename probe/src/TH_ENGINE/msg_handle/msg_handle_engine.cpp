// Last Update:2019-04-26 14:27:43
/**
 * @file pb_handle.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-02-18
 */
 
#include "msg_handle_engine.h"
#include  "TH_engine_define.h"

#define HANDLE_ID_NAME "get_msg_handle_id"
#define HANDLE_HANDLE_NAME "attach_msg_handle"

msg_handle_engine::msg_handle_engine()
{
    handle_map.clear();
    p_plugin_marge = new share_plugin_marge(string(getenv("THE_ROOT")) + "/bin/plugin/msg_handle/local/");
    p_plugin_marge -> get_plugin_id_map(HANDLE_ID_NAME , HANDLE_HANDLE_NAME , &handle_map);
    
    std::map<int ,void *>::iterator it = handle_map.begin();
    for(;it != handle_map.end();it ++) 
    {
        msg_handle_base*  p = ( msg_handle_base  *)it->second ;
        p -> set_tools(th_engine_tools::instance());
    }
    handle_map_user.clear();
    p_plugin_marge_user = new share_plugin_marge(string(getenv("THE_ROOT")) + "/bin/plugin/msg_handle/user/");
    p_plugin_marge_user -> get_plugin_id_map(HANDLE_ID_NAME , HANDLE_HANDLE_NAME , &handle_map_user);
    it = handle_map_user.begin();
    for(;it != handle_map_user.end();it ++) 
    {
        msg_handle_base  * p = (msg_handle_base *)it->second ;
        p -> set_tools(th_engine_tools::instance());

    }
}

msg_handle_engine::~msg_handle_engine()
{
    ;
}

void msg_handle_engine::handle(JKNmsg* p_msg)
{
    std::map<int ,void *>::iterator it_handle ;
    for(it_handle = handle_map.begin(); it_handle!= handle_map.end(); it_handle++) 
    {
        if(it_handle->second != NULL)
        {
            uint64_t  session_id  = 0;
            ((msg_handle_base*)(it_handle->second))->msg_handle(p_msg,& session_id);
        }
    }
    std::map<int ,void *>::iterator it_handle_1 ;
    for(it_handle_1 = handle_map_user.begin(); it_handle_1!= handle_map_user.end(); it_handle_1++) 
    {
        if(it_handle_1->second != NULL)
        {
            uint64_t  session_id  = 0;
            ((msg_handle_base*)(it_handle_1->second))->msg_handle(p_msg,&session_id);
        }
    }
}

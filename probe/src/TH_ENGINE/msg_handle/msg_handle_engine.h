// Last Update:2019-02-18 19:37:11
/**
 * @file pb_handle.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-02-18
 */

#ifndef MSG_HANDLE_ENGINE_H
#define MSG_HANDLE_ENGINE_H
#include <ab_queue_base.h>
#include <TH_engine_interface.h>
#include <packet.h>
#include <xml_parse.h>
#include <session_pub.h>
#include <list>
#include <map>
#include "share_plugin_marge.h"

class msg_handle_engine
{
public:
    msg_handle_engine();
    ~msg_handle_engine();
    void handle(JKNmsg* p_msg);
private:
    std::map<int, void*> handle_map ;
    share_plugin_marge* p_plugin_marge;

    std::map<int, void*> handle_map_user;
    share_plugin_marge* p_plugin_marge_user;
};

#endif  /*MSG_HANDLE_ENGINE_H*/

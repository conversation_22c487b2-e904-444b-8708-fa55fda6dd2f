// Last Update:2018-10-02 14:02:58
/**
 * @file ZmqSend.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2015-09-09
 */

#ifndef CZMQ_SEND_H
#define CZMQ_SEND_H
#include <zmq_client.h>
#include <stdlib.h>
#include <vector>
#include <map>

class CZmqSend 
{
    public:
        CZmqSend(string  addr ,int len, int sign);
        CZmqSend();
        void add_Addr(string addr , int len ,int sign ) ;
        virtual ~CZmqSend();
        virtual void SendData(int len ,char * buff);
        virtual void timeout();
    private :
        zmq_client * p_zmq ;

};
#endif  /*ZMQ_SEND_H*/

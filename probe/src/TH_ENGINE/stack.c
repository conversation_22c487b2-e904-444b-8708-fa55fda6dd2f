// Last Update:2019-01-16 16:36:47
/**
 * @file stack.c
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-08-26
 */

#include "stack.h"
/*定义状态码*/ 
#define OK 0            //正常
#define ERROR -1        //出错
#define OVERFLOW -2        //内存申请不成功   
#define INCREAMSIZE 10    //每次当栈空间满时，增量
#define DEFSIZE 100000      //栈的默认大小
/* 定义结构体 */
/*********************************stack操作方法**************************************************/
 
//初始化一个栈
Status InitStack(SqStack *sqstack,int size,int num ,char * p_buf)
{
    //申请默认栈大小
    sqstack -> base = (ElemType*)malloc(num*sizeof(ElemType));
    //申请真正使用空间
    sqstack -> size = size ;
/*    char * p = (char *)malloc(DEFSIZE*size);
    ElemType *pp =  sqstack -> base ;*/
    for(int i= 0 ; i < num ; i++)
    {
        sqstack -> base [ i ] = (ElemType)(p_buf + i*size );
    }
    sqstack -> p_buf = p_buf ;
    if(!sqstack -> base) exit(OVERFLOW);
    sqstack -> top  = &(sqstack -> base[num  -1]); //maybe:sqstack -> top  = &(sqstack -> base[num]);
    //  
    sqstack -> stackSize = num;
    sqstack -> realSize = num;
    return OK;
}
 
//进栈
Status Push(SqStack* sqstack,ElemType e)
{
 //   if(sqstack -> top-sqstack -> base>=sqstack->stackSize)
 //   {
    //    sqstack->base = (ElemType*)realloc(sqstack->base,(sqstack->stackSize+INCREAMSIZE)*sizeof(ElemType));
        //如果申请失败返回溢出
     //   if(!sqstack -> base) exit(OVERFLOW);
    //    sqstack->top = sqstack->base + sqstack->stackSize;
     //   sqstack->stackSize = sqstack->stackSize + INCREAMSIZE;
   // }
    *sqstack->top++ = e;
    sqstack->realSize++;
    return OK;
}
 
//出栈
Status Pop(SqStack *sqstack,ElemType * e)
{
    if(sqstack ->base==sqstack->top)
    {
        //exit(ERROR);

        return OK;
    }
    *e = *--sqstack->top;
    sqstack->realSize--;
    return OK;
}
 
//得到栈顶元素
Status GetTop(SqStack *sqstack,ElemType* e)
{
    if(sqstack -> base==sqstack->top)
    {
        exit(ERROR);
    }
    *e = *(sqstack ->top-1);
    return OK;
}
 
//判断栈是否为空
int IsEmpty(SqStack *sqstack)
{
    if(sqstack -> realSize>0)
        return 0;
    else
        return 1;
}
 
//销毁栈
Status DestroyStack(SqStack *sqstack)
{
    sqstack -> top = sqstack -> base;
    free(sqstack ->base);
    free(sqstack->p_buf);
    sqstack -> realSize = 0;
    sqstack -> stackSize = DEFSIZE;
    return OK;
}
  
//得到栈的元素个数
int StackLength(SqStack *sqstack)
{
    return sqstack ->realSize;
}
 
/*******************************主函数************************************************/
/*int main(int argc, char *argv[])
{
     
    SqStack sqstack;
    int N = 0;         //用于记录输入栈的个数
    int temp = 0;    //用于临时存栈
     
    InitStack(sqstack);
    printf("初始化时，堆的大小为：%d\n",sqstack.stackSize);
     
    printf("请入你想要输入几个数据进栈：");
    scanf("%d",&N) ;
    while(N--)
    {
        scanf("%d",&temp);
        Push(sqstack,temp);
        printf("进栈的大小为：%d\t",temp);
        printf("压栈后，栈的大小为：%d,%d\n",temp,sqstack.stackSize);
    }
    GetTop(sqstack,temp);
    printf("得到栈顶元素为：%d",temp);
 
    DestroyStack(sqstack);
    printf("销毁栈完成！！\n");
    scanf("%d",&temp);
 
    return 0;
}*/

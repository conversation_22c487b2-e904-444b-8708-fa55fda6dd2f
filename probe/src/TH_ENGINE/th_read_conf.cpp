// Last Update:2019-05-13 16:59:33
/**
 * @file th_read_conf.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-02-28
 */

#include <xml_parse.h>
#include "th_read_conf.h"
#include "BasicParse_Variable.h"

void th_xml_parse()
{
    config_and_define::task_id = atoll(getenv("THE_TASKID"));
    config_and_define::batch_id = atoll(getenv("THE_BATCHID"));

    string conf_path = string(getenv("THE_CONF_PATH")) + "/th_engine_conf.xml";
    xmlDocPtr doc = NULL;
    xmlNodePtr curNode = NULL;
    xmlNodePtr curNode_son = NULL;
    doc = xmlReadFile(conf_path.c_str(), "UTF-8", XML_PARSE_RECOVER);
    if (NULL == doc)
    {
        return;
    }
    curNode = xmlDocGetRootElement(doc);
    if (NULL == curNode)
    {
        xmlFreeDoc(doc);
        return;
    }
    if (0 != xmlStrcmp(curNode->name, (xmlChar*) "config"))
    {
        xmlFreeDoc(doc);
        abort();
        return;
    }
    curNode = curNode->xmlChildrenNode;
    int i_plugin_id = 0;
    char* p_tmp = NULL;
    for(; curNode != NULL; curNode = curNode->next)
    {
        if( xmlStrcmp(curNode->name, (xmlChar*) "b_save_pb_file") == 0)
        {
            curNode_son = curNode->xmlChildrenNode;
            for(; curNode_son != NULL; curNode_son = curNode_son->next)
            {
                if( xmlStrcmp(curNode_son->name, (xmlChar*)"key") == 0)
                {
                    p_tmp = (char*)xmlNodeGetContent(curNode_son);
                    if (NULL != p_tmp)
                    {
                        string str_key(p_tmp);
                        if (str_key == "true")
                        {
                            config_and_define::b_save_pb_file = true;
                        }
                        else
                        {
                            config_and_define::b_save_pb_file = false;
                        }
                    }
                    xmlFree(p_tmp);
                    p_tmp = NULL;
                }
            }
        }
        else if( xmlStrcmp(curNode->name, (xmlChar*) "pb_write_mode") == 0)
        {
            p_tmp = (char*)xmlNodeGetContent(curNode);
            if (NULL != p_tmp)
            {
                int num = atoi(p_tmp);
                config_and_define::pb_write_mode = num;
            }
            xmlFree(p_tmp);
            p_tmp = NULL;
        }
        else if( xmlStrcmp(curNode->name, (xmlChar*) "b_save_json") == 0)
        {
            p_tmp = (char*)xmlNodeGetContent(curNode);
            if (NULL != p_tmp)
            {
                string str_key(p_tmp);
                if (str_key == "true")
                {
                    config_and_define::b_save_json_file = true;
                }
                else
                {
                    config_and_define::b_save_json_file = false;
                }
            }
            xmlFree(p_tmp);
            p_tmp = NULL;
        }
        else if( xmlStrcmp(curNode->name, (xmlChar*) "pb_file_time") == 0)
        {
            p_tmp = (char*)xmlNodeGetContent(curNode);
            if (NULL != p_tmp)
            {
                int num = atoi(p_tmp);
                config_and_define::pb_file_time = num;
            }
            xmlFree(p_tmp);
            p_tmp = NULL;
        }
        else if( xmlStrcmp(curNode->name, (xmlChar*) "zmq") == 0)
        {
            curNode_son = curNode->xmlChildrenNode;
            for(; curNode_son != NULL; curNode_son = curNode_son->next)
            {
                if( xmlStrcmp(curNode_son->name, (xmlChar*)"b_zmq_active") == 0)
                {
                    p_tmp = (char*)xmlNodeGetContent(curNode_son);
                    if (NULL != p_tmp)
                    {
                        string str_key(p_tmp);
                        if (str_key == "true")
                        {
                            config_and_define::b_zmq_active = true;
                        }
                        else
                        {
                            config_and_define::b_zmq_active = false;
                        }
                    }
                    xmlFree(p_tmp);
                    p_tmp = NULL;
                }
                else if( xmlStrcmp(curNode_son->name, (xmlChar*)"addr") == 0)
                {
                    p_tmp = (char*)xmlNodeGetContent(curNode_son);
                    if (NULL != p_tmp)
                    {
                        string str_key(p_tmp);
                        config_and_define::zmq_addr = str_key;
                    }
                    xmlFree(p_tmp);
                    p_tmp = NULL;
                }
                else if( xmlStrcmp(curNode_son->name, (xmlChar*)"length") == 0)
                {
                    p_tmp = (char*)xmlNodeGetContent(curNode_son);
                    if (NULL != p_tmp)
                    {
                        int i_port = atoi(p_tmp);
                        config_and_define::zmq_len = i_port;
                    }
                    xmlFree(p_tmp);
                    p_tmp = NULL;
                }
            }
        }
        else if( xmlStrcmp(curNode->name, (xmlChar*) "max_packet_rule") == 0)
        {
            p_tmp = (char*)xmlNodeGetContent(curNode);
            if (NULL != p_tmp)
            {
                int num = atoi(p_tmp);
                config_and_define::max_packet_rule = num;
            }
            xmlFree(p_tmp);
            p_tmp = NULL;
        }
        /*else if( xmlStrcmp(curNode->name, (xmlChar*) "thread_session_num") == 0)
        {
            p_tmp = (char*)xmlNodeGetContent(curNode);
            if (NULL != p_tmp)
            {
                int num = atoi(p_tmp);
                config_and_define::thread_session_num = num;
            }
            xmlFree(p_tmp);
            p_tmp = NULL;
        }*/
        else if( xmlStrcmp(curNode->name, (xmlChar*) "work_mode") == 0)
        {
            p_tmp = (char*)xmlNodeGetContent(curNode);
            if (NULL != p_tmp)
            {
                int num = atoi(p_tmp);
                config_and_define::work_mode = num;
            }
            xmlFree(p_tmp);
            p_tmp = NULL;
        }
        else if( xmlStrcmp(curNode->name, (xmlChar*) "link_parse_mode") == 0)
        {
            p_tmp = (char*)xmlNodeGetContent(curNode);
            if (NULL != p_tmp)
            {
                int num = atoi(p_tmp);
                config_and_define::link_parse_mode = num;
            }
            xmlFree(p_tmp);
            p_tmp = NULL;
        }
        else if( xmlStrcmp(curNode->name, (xmlChar*) "parse_thread_num") == 0)
        {
            p_tmp = (char*)xmlNodeGetContent(curNode);
            if (NULL != p_tmp)
            {
                int num = atoi(p_tmp);
                config_and_define::parse_thread_num = num;
            }
            xmlFree(p_tmp);
            p_tmp = NULL;
        }
        else if( xmlStrcmp(curNode->name, (xmlChar*) "b_move_files") == 0)
        {
            p_tmp = (char*)xmlNodeGetContent(curNode);
            if (NULL != p_tmp)
            {
                string str_key(p_tmp);
                if (str_key == "true")
                {
                    config_and_define::b_move_files = true;
                }
                else
                {
                    config_and_define::b_move_files = false;
                }
            }
            xmlFree(p_tmp);
            p_tmp = NULL;
        }
        else if( xmlStrcmp(curNode->name, (xmlChar*) "b_brush_off_syn") == 0)
        {
            p_tmp = (char*)xmlNodeGetContent(curNode);
            if (NULL != p_tmp)
            {
                string str_key(p_tmp);
                if (str_key == "true")
                {
                    config_and_define::b_brush_off_syn = true;
                }
                else
                {
                    config_and_define::b_brush_off_syn = false;
                }
            }
            xmlFree(p_tmp);
            p_tmp = NULL;
        }
        else if( xmlStrcmp(curNode->name, (xmlChar*) "b_segment_analyse") == 0)
        {
            p_tmp = (char*)xmlNodeGetContent(curNode);
            if (NULL != p_tmp)
            {
                string str_key(p_tmp);
                if (str_key == "true")
                {
                    config_and_define::b_segment_analyse = true;
                }
                else
                {
                    config_and_define::b_segment_analyse = false;
                }
            }
            xmlFree(p_tmp);
            p_tmp = NULL;
        }
        else if(xmlStrcmp(curNode->name, (xmlChar*) "session") == 0)
        {
            curNode_son = curNode->xmlChildrenNode;
            for(; curNode_son != NULL; curNode_son = curNode_son->next)
            {
                if( xmlStrcmp(curNode_son->name, (xmlChar*)"timeout") == 0)
                {
                    p_tmp = (char*)xmlNodeGetContent(curNode_son);
                    if (NULL != p_tmp)
                    {
                        int tmp_i = atoi(p_tmp);
                        if(tmp_i > 0)
                        {
                            config_and_define::session_bucket_tosec = tmp_i;
                        }
                    }
                    xmlFree(p_tmp);
                    p_tmp = NULL;
                }
                else if( xmlStrcmp(curNode_son->name, (xmlChar*)"marge") == 0)
                {
                    p_tmp = (char*)xmlNodeGetContent(curNode_son);
                    if (NULL != p_tmp)
                    {
                        int tmp_i = atoi(p_tmp);
                        if(tmp_i > 0)
                        {
                            config_and_define::session_bucket_num = tmp_i;
                        }
                    }
                    xmlFree(p_tmp);
                    p_tmp = NULL;
                }
            }
        }
        else if(xmlStrcmp(curNode->name, (xmlChar*) "b_noip_record") == 0)
        {
            p_tmp = (char*)xmlNodeGetContent(curNode);
            if (NULL != p_tmp)
            {
                string str_key(p_tmp);
                if (str_key == "true")
                {
                    gBConf_noip_record = true;
                }
                else
                {
                    gBConf_noip_record = false;
                }
            }
            xmlFree(p_tmp);
            p_tmp = NULL;
        }
        else if(xmlStrcmp(curNode->name, (xmlChar*) "b_rule_pb_save") == 0)
        {
            p_tmp = (char*)xmlNodeGetContent(curNode);
            if (NULL != p_tmp)
            {
                string str_key(p_tmp);
                if (str_key == "true")
                {
                    config_and_define::b_rule_pb_save = true;
                }
                else
                {
                    config_and_define::b_rule_pb_save = false;
                }
            }
            xmlFree(p_tmp);
            p_tmp = NULL;
        }
        else if(xmlStrcmp(curNode->name, (xmlChar*) "b_pb_rename") == 0)
        {
            p_tmp = (char*)xmlNodeGetContent(curNode);
            if (NULL != p_tmp)
            {
                string str_key(p_tmp);
                if (str_key == "false")
                {
                    config_and_define::b_pb_rename = false;
                }
                else
                {
                    config_and_define::b_pb_rename = true;
                }
            }
            xmlFree(p_tmp);
            p_tmp = NULL;
        }
        else if(xmlStrcmp(curNode->name, (xmlChar*) "b_save_white_json") == 0)
        {
            p_tmp = (char*)xmlNodeGetContent(curNode);
            if (NULL != p_tmp)
            {
                string str_key(p_tmp);
                if (str_key == "true")
                {
                    config_and_define::b_save_white_json = true;
                }
                else
                {
                    config_and_define::b_save_white_json = false;
                }
            }
            xmlFree(p_tmp);
            p_tmp = NULL;
        }
    }
    xmlFreeDoc(doc);
    return;
}

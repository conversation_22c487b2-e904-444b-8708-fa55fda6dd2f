#include "th_engine_packet.h"
#include "th_engine.h"



// Windows
#ifdef _WIN32
#include <intrin.h>
uint64_t rdtsc(){
return __rdtsc();
}
// Linux/GCC
#else
uint64_t rdtsc(){
unsigned int lo,hi;
__asm__ __volatile__ ("rdtsc" : "=a" (lo), "=d" (hi));
return ((uint64_t)hi << 32) | lo;
}
#endif


static void PacketInfo_init(PacketInfo * p_packet)
{
    p_packet->signlen = 0;
    p_packet->len = 0;
    p_packet->ts = 0;
    p_packet->nts = 0;
    p_packet->tid = 0;
    p_packet->index = 0;
    p_packet->sy_crc64 = 0;
    p_packet->p_data = NULL;
    p_packet->packet.init();
    p_packet->p_session = NULL;
}

int th_engine::packet_init(int first_proto, unsigned char *buf, uint32_t len, uint32_t *ts, int thread_id, uint8_t port_id)
{
    uint32_t clock_ts[2];

    PacketInfo *p_pinfo = &(p_thread_info[thread_id].pinfo);
    c_packet *p_packet = &p_pinfo->packet;

    PacketInfo_init(p_pinfo);
    p_packet->p_value = &(p_thread_info[thread_id].value);
    p_pinfo->len = len;
    p_pinfo->tid = thread_id;
    p_pinfo->p_data = buf;
    if(ts)
    {
        p_pinfo->ts = ts[0];
        p_pinfo->nts = ts[1];
    }
    else
    {
        p_pinfo->ts = 0;
        p_pinfo->nts = 0;
    }

    if(first_proto)
    {
        p_packet->first_proto = first_proto;
    }

    p_packet->port_id = port_id;
    p_packet->thread_id = thread_id;
    p_packet->batch_id = config_and_define::task_id;
    p_packet->buf = p_pinfo->p_data;
    p_packet->packet_len = p_pinfo->len;
    
    //fuck ns
    th_engine_tools::instance()->get_clock_uts(clock_ts[0], clock_ts[1]);
    if(clock_ts[0] == p_thread_info[thread_id].ts_last && clock_ts[1] == p_thread_info[thread_id].uts_last)
    {
        p_thread_info[thread_id].nts_last += (rdtsc() % 2003 + 1);
        if(p_thread_info[thread_id].nts_last > 99999)
        {
            p_thread_info[thread_id].nts_last = 99999;
        }
    }
    else
    {
        p_thread_info[thread_id].ts_last = clock_ts[0];
        p_thread_info[thread_id].uts_last = clock_ts[1];
        p_thread_info[thread_id].nts_last = 1000 * (rdtsc() % 67);
        p_thread_info[thread_id].nts_last += rdtsc() % 1009;
    }
    uint32_t nts_now = clock_ts[1] * 1000 + p_thread_info[thread_id].nts_last;
    p_packet->clock_ts[0] = clock_ts[0];
    p_packet->clock_ts[1] = nts_now;                            //fuck ns
    
    if(p_pinfo->ts)           //使用指定时间
    {
        p_packet->time_ts[0] = p_pinfo ->ts;
        p_packet->time_ts[1] = p_pinfo ->nts;
    }
    else        //使用墙上时间
    {
        p_packet->time_ts[0] = p_packet->clock_ts[0];
        p_packet->time_ts[1] = p_packet->clock_ts[1];
        p_pinfo->ts = p_packet->time_ts[0];
        p_pinfo->nts = p_packet->time_ts[1];
    }
    //fuck ns --end
    //c_packet init --end
}

static void PacketInfo_signbuf( c_packet * p_packet , PacketInfo * p_info)
{
    // 如果没有IP  --- 直接取mac 分钟 
    p_info ->signlen = 0 ;
    
    if(p_packet -> src_port == 0) 
    {
        uint32_t ip_s = p_packet -> src_ip.get_hash();
        uint32_t ip_d = p_packet -> dst_ip.get_hash();
        if(p_packet ->  src_ip > p_packet ->  dst_ip)
        {
            if(p_packet -> src_ip.IsIPv6())
            {
                p_packet -> src_ip.GetIPv6((unsigned char *)p_info->buff+p_info ->signlen);
                p_info ->signlen += 16;
            }
            else
            {
                memcpy (p_info->buff+p_info ->signlen,&ip_s,sizeof(uint32_t));
                p_info ->signlen += sizeof(uint32_t) ;
            }
            if(p_packet -> dst_ip.IsIPv6())
            {
                p_packet -> dst_ip.GetIPv6((unsigned char *)p_info->buff+p_info ->signlen);
                p_info ->signlen += 16;
            }
            else
            {
                memcpy (p_info->buff+p_info ->signlen,&ip_d,sizeof(uint32_t));
                p_info ->signlen += sizeof(uint32_t) ;
            }
        }
        else
        {
            if(p_packet -> dst_ip.IsIPv6())
            {
                p_packet -> dst_ip.GetIPv6((unsigned char *)p_info->buff+p_info ->signlen);
                p_info ->signlen += 16;
            }
            else
            {
                memcpy (p_info->buff+p_info ->signlen,&ip_d,sizeof(uint32_t));
                p_info ->signlen += sizeof(uint32_t) ;
            }
            if(p_packet -> src_ip.IsIPv6())
            {
                p_packet -> src_ip.GetIPv6((unsigned char *)p_info->buff+p_info ->signlen);
                p_info ->signlen += 16;
            }
            else
            {
                memcpy (p_info->buff+p_info ->signlen,&ip_s,sizeof(uint32_t));
                p_info ->signlen += sizeof(uint32_t) ;
            }
        }
        memcpy (p_info->buff+p_info ->signlen,(void *)&(p_packet -> u_tcp),sizeof(uint8_t));
        p_info ->signlen += sizeof(uint8_t) ;
        return ;
    }
    //printf("%s %d %s %d \n",p_packet ->  src_ip.str().c_str(),p_packet ->src_port ,  p_packet ->  dst_ip.str().c_str() , p_packet ->  dst_port);
    // 那边的端口小，数据存储在那, 边
    //printf("srcip= %s , src_port = %d , dst_ip = %s , dst_port = %d \n" , p_packet -> src_ip.str().c_str(), p_packet ->src_port , p_packet -> dst_ip.str().c_str(), p_packet ->  dst_port );
    else if(p_packet ->src_port > p_packet ->  dst_port||(p_packet -> src_port == p_packet ->  dst_port &&p_packet ->  src_ip > p_packet ->  dst_ip))
    {
        // 
        if(p_packet -> src_ip.IsIPv6())
        {
            p_packet -> src_ip.GetIPv6((unsigned char *)p_info->buff+p_info ->signlen);
            p_info ->signlen += 16;
        }
        else
        {
            uint32_t ip_s = p_packet -> src_ip.get_hash();
            memcpy (p_info->buff+p_info ->signlen,&ip_s,sizeof(uint32_t));
            p_info ->signlen += sizeof(uint32_t) ;
        }
        if(p_packet -> dst_ip.IsIPv6())
        {
            p_packet -> dst_ip.GetIPv6((unsigned char *)p_info->buff+p_info ->signlen);
            p_info ->signlen += 16;
        }
        else
        {
            uint32_t ip_d = p_packet -> dst_ip.get_hash();
            memcpy (p_info->buff+p_info ->signlen,&ip_d,sizeof(uint32_t));
            p_info ->signlen += sizeof(uint32_t) ;
        }
        uint16_t port_s = p_packet ->  src_port ;
        memcpy (p_info->buff+p_info ->signlen,&port_s,sizeof(uint16_t));
        p_info ->signlen += sizeof(uint16_t) ;
        uint16_t port_d = p_packet ->  dst_port ;
        memcpy (p_info->buff+p_info ->signlen,&port_d,sizeof(uint16_t));
        p_info ->signlen += sizeof(uint16_t) ;
        memcpy (p_info->buff+p_info ->signlen,(void *)&(p_packet -> u_tcp),sizeof(uint8_t));
        p_info ->signlen += sizeof(uint8_t) ;
    }
    else
    {
        if(p_packet -> dst_ip.IsIPv6())
        {
            p_packet -> dst_ip.GetIPv6((unsigned char *)p_info->buff+p_info ->signlen);
            p_info ->signlen += 16;
        }
        else
        {
            uint32_t ip_d = p_packet -> dst_ip.get_hash();
            memcpy (p_info->buff+p_info ->signlen,&ip_d,sizeof(uint32_t));
            p_info ->signlen += sizeof(uint32_t) ;
        }
        if(p_packet -> src_ip.IsIPv6())
        {
            p_packet -> src_ip.GetIPv6((unsigned char *)p_info->buff+p_info ->signlen);
            p_info ->signlen += 16;
        }
        else
        {
            uint32_t ip_s = p_packet -> src_ip.get_hash();
            memcpy (p_info->buff+p_info ->signlen,&ip_s,sizeof(uint32_t));
            p_info ->signlen += sizeof(uint32_t) ;
        }
        uint16_t port_d = p_packet ->  dst_port ;
        memcpy (p_info->buff+p_info ->signlen,(void *)(&port_d),sizeof(uint16_t));
        p_info ->signlen += sizeof(uint16_t) ;
        uint16_t port_s = p_packet -> src_port ;
        memcpy (p_info->buff+p_info ->signlen,(void *)(&port_s),sizeof(uint16_t));
        p_info ->signlen += sizeof(uint16_t) ;
        memcpy (p_info->buff+p_info ->signlen,(void *)&(p_packet -> u_tcp),sizeof(uint8_t));
        p_info ->signlen += sizeof(uint8_t) ;
    }
}


int th_engine::packet_parse(int thread_id)
{
    PacketInfo *p_pinfo = &(p_thread_info[thread_id].pinfo);
    c_packet *p_packet = &p_pinfo->packet;

    p_packet->parse((uint8_t *)(p_pinfo->p_data), p_pinfo->len, p_packet->time_ts, config_and_define::link_parse_mode, thread_id, config_and_define::b_segment_analyse);
    
    if(p_packet->flags.b_no_session)
    {
        return 1;
    }
    if(p_packet->p_tcphdr != NULL)
    {
        if (config_and_define::b_brush_off_syn)
        {
            if(0x02 == *((uint8_t *)(p_packet->p_tcphdr) + 13))
            {
                p_thread_info[thread_id].flush_syn_num++;
                p_packet->flags.b_no_session = 1;
                p_packet->flags.b_brush_off_syn = 1;
                th_engine_g_var.drop_nocreate_pkt[thread_id] ++;
                return 1;
            }
        }
    }
    PacketInfo_signbuf(p_packet , p_pinfo ) ;
    t_packet_crc64(p_pinfo);
    if(rate_limter.check_drop_pkt((unsigned int)p_pinfo->sy_crc64))
    {
        p_packet->flags.b_no_session = 1;
        p_packet->flags.b_drop_beyond = 1;
        return 1;
    }
    return 0;
}

uint64_t th_engine::pkt_hash(int first_proto, unsigned char *buf, uint32_t len)
{
    PacketInfo *p_pinfo = &hash_pkt;
    c_packet *p_packet = &p_pinfo->packet;
    PacketInfo_init(p_pinfo);
    p_pinfo->len = len;
    p_pinfo->p_data = buf;
    if(first_proto)
    {
        p_packet->first_proto = first_proto;
    }
    p_packet->buf = p_pinfo->p_data;
    p_packet->packet_len = p_pinfo->len;
    p_packet->parse((uint8_t *)(p_pinfo->p_data), p_pinfo->len);
    if(0 == p_packet->flags.b_no_session)
    {
        PacketInfo_signbuf(p_packet , p_pinfo ) ;
        t_packet_crc64(p_pinfo);
        return p_pinfo->sy_crc64;
    }
    return 0;

}
#include "th_engine.h"
#include "CheckSerialNumber.h"
#include "Interface/src/TH_ENGINE/PacketAndRule.h"
#include "th_engine_log.h"



static uint64_t type_pb_num[10000] = {0};
static uint64_t type_pb_bytes[10000] = {0};
static th_engine *p_engine = NULL;

void *th_log_loop(void *arg)
{
    p_engine = (th_engine *)arg;
    string group = "thd_" + string(getenv("THE_TASKID")) + "_" + string(getenv("THE_BATCHID"));
    string conf_path = string(getenv("THE_CONF_PATH")) + "/kafka_conf.json";

    p_engine->pKafkaWriter = new KafkaWriter(conf_path.c_str());
    p_engine->pKafkaReader = new KafkaReader(conf_path.c_str(), group.c_str());

    CheckSerialNumber();
    while(0 == p_engine->b_thread_init_done())
    {
        usleep(1);
    }
    uint32_t ts_now, log_now, update_session_now, ts_last_log, ts_last_update_session;
    ts_now = time(NULL);
    log_now = ts_now / LOG_N_SEC;
    update_session_now = ts_now / config_and_define::session_bucket_tosec;
    ts_last_log = log_now;
    ts_last_update_session = update_session_now;
    
    for(;;)
    {
        ts_now = time(NULL);
        log_now = ts_now / LOG_N_SEC;
        update_session_now = ts_now / config_and_define::session_bucket_tosec;

        if(update_session_now != ts_last_update_session)
        {
            ts_last_update_session = update_session_now;
            for(int i = 0 ;i < p_engine->thread_num ; i++)
            {
                if(p_engine->p_thread_info[i].pkt_handle)
                {
                    __sync_fetch_and_add(&(p_engine->p_thread_info[i].timeout_bucket),1);
                }
            }
        }
        if(log_now != ts_last_log)
        {
            ts_last_log = log_now;
            p_engine->log_handle(log_now * LOG_N_SEC);
            check_time();
        }
        usleep(1000);
        p_engine->log_thread_running ++;
    }
    return NULL;
}

int send_log(const char *topic, void *pKey, unsigned int keyLen, void *pValue, unsigned int valueLen)
{
    return p_engine->pKafkaWriter->sendData(topic, pKey, keyLen, pValue, valueLen);
}



typedef struct
{
    uint64_t pkt_count;
    uint16_t port;
}port_sort;

static void port_sort_partition(port_sort **arr, int low, int high, int *pos)
{
    port_sort *key = arr[low];
    int i = low, j = high;
    while(i < j)
    {
        while(i < j && arr[j]->pkt_count > key->pkt_count)
        {
            j--;
        }
        if(i < j)
        {
            arr[i++] = arr[j];
        }
        while(i < j && arr[i]->pkt_count < key->pkt_count)
        {
            i ++;
        }
        if(i < j)
        {
            arr[j--] = arr[i];
        }
    }
    arr[i] = key;
    *pos = i;
}

static int port_sort_topk(port_sort **arr, int low, int high, int k)
{
    if(k <= 0 || high-low + 1 <= k)
    {
        return low;
    }
    int pos =0;
    port_sort_partition(arr, low, high, &pos);
    int num = high - pos + 1;
    int index = -1;
    if(num == k)
    {
        index = pos;
    }
    else if(num > k)
    {
       index = port_sort_topk(arr, pos + 1, high, k);
    }
    else
    {
        index =  port_sort_topk(arr, low, pos -1, k - num);
    }
    return index;
}

int th_engine::log_handle(uint32_t ns_time)
{
    if(0 == b_thread_init_done())
    {
        return 0;
    }
    char tmp[2048];
    int tmplen = 0;
    static int ts_init_done = 0;
    static int count = 0;
    bool b_5min = false;
    count ++;
    if(count >= 10)
    {
        count = 0;
        b_5min = true;
    }
    
    PacketAndRule::instance()->GetRuleAttribute()->UpdateRuleSummary();
    th_engine_tools::instance()->log_study_info();

    uint64_t    all_flush_syn_num   = 0 ;
    uint64_t    all_session_num     = 0 ;
    uint32_t    cre_session_num     = 0 ;
    uint32_t    udp_session_num     = 0 ;
    uint32_t    tcp_session_num     = 0 ;
    uint32_t    dns_session_num     = 0 ;
    uint32_t    ntp_session_num     = 0 ;

    uint32_t    del_session_num     = 0 ;
    uint32_t    tcp_session_num_4   = 0 ;
    uint32_t    udp_session_num_4   = 0 ;
    uint32_t    tcp_session_num_8   = 0 ;
    uint32_t    udp_session_num_8   = 0 ;
    uint32_t    tcp_session_num_16  = 0 ;
    uint32_t    udp_session_num_16  = 0 ;
    uint32_t    tcp_session_num_28  = 0 ;
    uint32_t    udp_session_num_28  = 0 ;
    uint32_t    tcp_session_num_oo  = 0 ;
    uint32_t    udp_session_num_oo  = 0 ;

    uint32_t    session_pub_used    = 0 ;
    uint32_t    session_ext_used    = 0 ;
    uint32_t    session_tcp_used    = 0 ;
    uint32_t    tcp_link_used       = 0 ;
    uint32_t    tcp_recombine_used  = 0 ;
    uint32_t    session_pub_all     = 0 ;
    uint32_t    session_ext_all     = 0 ;
    uint32_t    session_tcp_all     = 0 ;
    uint32_t    tcp_link_all        = 0 ;
    uint32_t    tcp_recombine_all   = 0 ;

    uint64_t    sa_bytes            = 0 ;
    uint64_t    sa_pkts             = 0 ;
    uint64_t    sa_bytes_loop       = 0 ;
    uint32_t    sa_pkts_loop        = 0 ;

    for(int i = 0 ; i< thread_num ;i++)
    {
        all_flush_syn_num   += p_thread_info[i].flush_syn_num;
        all_session_num     += p_thread_info[i].all_session_num;
        cre_session_num     += p_thread_info[i].cre_session_num;   p_thread_info[i].cre_session_num = 0;
        udp_session_num     += p_thread_info[i].udp_session_num;   p_thread_info[i].udp_session_num = 0;
        tcp_session_num     += p_thread_info[i].tcp_session_num;   p_thread_info[i].tcp_session_num = 0;
        dns_session_num     += p_thread_info[i].dns_session_num;   p_thread_info[i].dns_session_num = 0;
        ntp_session_num     += p_thread_info[i].ntp_session_num;   p_thread_info[i].ntp_session_num = 0;
        del_session_num     += p_thread_info[i].del_session_num;   p_thread_info[i].del_session_num = 0;
        tcp_session_num_4   += p_thread_info[i].tcp_pn_arr[0];     p_thread_info[i].tcp_pn_arr[0] = 0;
        udp_session_num_4   += p_thread_info[i].udp_pn_arr[0];     p_thread_info[i].udp_pn_arr[0] = 0;
        tcp_session_num_8   += p_thread_info[i].tcp_pn_arr[1];     p_thread_info[i].tcp_pn_arr[1] = 0;
        tcp_session_num_8   += p_thread_info[i].udp_pn_arr[1];     p_thread_info[i].udp_pn_arr[1] = 0;
        tcp_session_num_16  += p_thread_info[i].tcp_pn_arr[2];     p_thread_info[i].tcp_pn_arr[2] = 0;
        udp_session_num_16  += p_thread_info[i].udp_pn_arr[2];     p_thread_info[i].udp_pn_arr[2] = 0;
        tcp_session_num_16  += p_thread_info[i].tcp_pn_arr[3];     p_thread_info[i].tcp_pn_arr[3] = 0;
        udp_session_num_16  += p_thread_info[i].udp_pn_arr[3];     p_thread_info[i].udp_pn_arr[3] = 0;
        tcp_session_num_28  += p_thread_info[i].tcp_pn_arr[4];     p_thread_info[i].tcp_pn_arr[4] = 0;
        udp_session_num_28  += p_thread_info[i].udp_pn_arr[4];     p_thread_info[i].udp_pn_arr[4] = 0;
        tcp_session_num_28  += p_thread_info[i].tcp_pn_arr[5];     p_thread_info[i].tcp_pn_arr[5] = 0;
        udp_session_num_28  += p_thread_info[i].udp_pn_arr[5];     p_thread_info[i].udp_pn_arr[5] = 0;
        tcp_session_num_28  += p_thread_info[i].tcp_pn_arr[6];     p_thread_info[i].tcp_pn_arr[6] = 0;
        udp_session_num_28  += p_thread_info[i].udp_pn_arr[6];     p_thread_info[i].udp_pn_arr[6] = 0;
        tcp_session_num_oo  += p_thread_info[i].tcp_pn_arr[7];     p_thread_info[i].tcp_pn_arr[7] = 0;
        udp_session_num_oo  += p_thread_info[i].udp_pn_arr[7];     p_thread_info[i].udp_pn_arr[7] = 0;

        session_pub_used    += p_thread_info[i].p_sess_pub_marge->get_usenum();
        session_ext_used    += p_thread_info[i].p_sess_ext_marge->get_usenum();
        session_tcp_used    += p_thread_info[i].p_cmd_tcp_marge->get_usenum();
        tcp_link_used       += p_thread_info[i].p_tcp_link_marge->get_usenum();
        tcp_recombine_used  += p_thread_info[i].p_tcp_recb_marge->get_usenum();
        session_pub_all     += p_thread_info[i].p_sess_pub_marge->get_sumnum();
        session_ext_all     += p_thread_info[i].p_sess_ext_marge->get_sumnum();
        session_tcp_all     += p_thread_info[i].p_cmd_tcp_marge->get_sumnum();
        tcp_link_all        += p_thread_info[i].p_tcp_link_marge->get_sumnum();
        tcp_recombine_all   += p_thread_info[i].p_tcp_recb_marge->get_sumnum();

        sa_bytes            += gBVar_pkt_sum[i].sa_bytes        ;
        sa_pkts             += gBVar_pkt_sum[i].sa_pkts         ;
        sa_bytes_loop       += gBVar_pkt_sum[i].sa_bytes_loop   ;       gBVar_pkt_sum[i].sa_bytes_loop = 0;
        sa_pkts_loop        += gBVar_pkt_sum[i].sa_pkts_loop    ;       gBVar_pkt_sum[i].sa_pkts_loop  = 0;
    }
    

    if(config_and_define::b_segment_analyse)
    {
        if(0 == ts_init_done)
        {
            ts_init_done = ns_time;
        }
        tmplen = sprintf(tmp, "{\"type\":%d,\"time\":%u,\"total_bytes\":\"%"PRIu64"\",\"bps\":\"%"PRIu64"\",\"total_pkts\":\"%"PRIu64"\",\"pps\":%u,\"ts_start\":%u,\"ts_run\":%u,\"task_id\":\"%lld\",\"batch_id\":\"%lld\",\"device_id\":\"%u\"}",
                213, ns_time, sa_bytes, sa_bytes_loop/30*8, sa_pkts, sa_pkts_loop/30, ts_init_done, (ns_time >= ts_init_done)?ns_time-ts_init_done:0, config_and_define::task_id, config_and_define::batch_id, th_engine_tools::instance()->device_id
            );
        th_engine_tools::instance()->send_log("slog", NULL, 0, tmp, tmplen);
        p_thread_info[0].p_full_flow_engine->log(ns_time);
        return 0;
    }
    
    tmplen = sprintf(tmp, "{\"type\":%d,\"time\":%u,\"session_pub_used\":%u,\"session_pub_used_percent\":%u,\"session_ext_used\":%u,\"session_ext_used_percent\":%u,\"session_tcp_used\":%u,\"session_tcp_used_percent\":%u,\"tcp_link_used\":%u,\"tcp_link_used_percent\":%u,\"tcp_recombine_used\":%u,\"tcp_recombine_used_percent\":%u,\"task_id\":\"%lld\",\"batch_id\":\"%lld\",\"device_id\":\"%u\"}",
            214, ns_time, session_pub_used, session_pub_used * 100 / session_pub_all, session_ext_used, session_ext_used * 100 / session_ext_all, session_tcp_used, session_tcp_used * 100 / session_tcp_all, tcp_link_used, tcp_link_used * 100 / tcp_link_all, tcp_recombine_used, tcp_recombine_used * 100 / tcp_recombine_all, config_and_define::task_id, config_and_define::batch_id, th_engine_tools::instance()->device_id
        );
    th_engine_tools::instance()->send_log("slog", NULL, 0, tmp, tmplen);

    uint64_t total_pb_num = 0;
    uint64_t total_pb_bytes = 0;
    uint32_t tmp_pb_num = 0;
    uint64_t tmp_pb_bytes = 0;
    for(int i = 0; i < 10000; i ++)
    {
        uint32_t    tmp_type_pb_num          = 0 ;
        uint64_t    tmp_type_pb_bytes        = 0 ;
        for(int j = 0; j < thread_num; j ++)
        {
            tmp_type_pb_num += p_thread_info[j].MsgTypeConde[i];    p_thread_info[j].MsgTypeConde[i] = 0;
            tmp_type_pb_bytes += p_thread_info[j].MsgTypeByte[i];   p_thread_info[j].MsgTypeByte[i] = 0;
        }
        if(tmp_type_pb_num)
        {
            tmp_pb_num += tmp_type_pb_num;
            tmp_pb_bytes += tmp_type_pb_bytes;
            type_pb_num[i] += tmp_type_pb_num;
            type_pb_bytes[i] += tmp_type_pb_bytes;
        }
        total_pb_num += type_pb_num[i];
        total_pb_bytes += type_pb_bytes[i];
    }
    update_pb_summary();
    tmplen = sprintf(tmp, "{\"type\":%d,\"time\":%u,\
\"all_pb_num\":\"%"PRIu64"\",\"all_pb_bytes\":\"%"PRIu64"\",\"num_30s_pb\":%u,\"bytes_30s_pb\":\"%"PRIu64"\",\"pb_ps\":%u,\"task_id\":\"%lld\",\"batch_id\":\"%lld\",\"device_id\":\"%u\"}",
            212, ns_time, 
            total_pb_num, total_pb_bytes, tmp_pb_num, tmp_pb_bytes, tmp_pb_num/30,config_and_define::task_id, config_and_define::batch_id, th_engine_tools::instance()->device_id
        );
    th_engine_tools::instance()->send_log("slog", NULL, 0, tmp, tmplen);
    for(uint32_t i=0; i < 10000; i ++)
    {
        if(type_pb_num[i])
        {
            tmplen = sprintf(tmp, "{\"type\":%d,\"time\":%u,\
\"pb_num\":\"%"PRIu64"\",\"pb_bytes\":\"%"PRIu64"\",\"pb_type\":%u,\"task_id\":\"%lld\",\"batch_id\":\"%lld\",\"device_id\":\"%u\"}",
                    216, ns_time, 
                    type_pb_num[i], type_pb_bytes[i], i, config_and_define::task_id, config_and_define::batch_id, th_engine_tools::instance()->device_id
                );
            th_engine_tools::instance()->send_log("slog", NULL, 0, tmp, tmplen);
        }
    }

    p_thread_info[0].p_full_flow_engine->log(ns_time);
    rate_limter.re_calculate_dividing_line(config_and_define::mbps_filter_out);

    if(b_5min)
    {
        //-------------------防御类型统计-------------------
        for(int i = 0; i < MAX_DEFENSE_TYPE; i ++)
        {
            uint64_t drop_defense_bytes = 0;
            uint32_t drop_defense_pkts = 0;
            for(int j = 0 ; j < thread_num ;j ++)
            {
                drop_defense_bytes += gBVar_pkt_sum[j].drop_defense_bytes_loop[i];
                gBVar_pkt_sum[j].drop_defense_bytes_loop[i] = 0;
                drop_defense_pkts += gBVar_pkt_sum[j].drop_defense_pkts_loop[i];
                gBVar_pkt_sum[j].drop_defense_pkts_loop[i] = 0;
            }
            if(drop_defense_bytes)
            {
                tmplen = sprintf(tmp, "{\"type\":%d,\"time\":%u,\"id\":%d,\"bytes\":\"%"PRIu64"\",\"task_id\":%lld,\"batch_id\":%lld,\"device_id\":\"%u\"}",
                        203, ns_time, i, drop_defense_bytes, config_and_define::task_id, config_and_define::batch_id, th_engine_tools::instance()->device_id
                    );
                th_engine_tools::instance()->send_log("slog", NULL, 0, tmp, tmplen);
            }
        }
        usleep(1);
        //-------------------过滤规则统计-------------------
        for(int i = 1; i < gFilterRuleNum+1; i ++)
        {
            uint64_t filter_hit_bytes = 0;
            uint32_t filter_hit_pkts = 0;
            for(int j=0; j < thread_num; j ++)
            {
                filter_hit_bytes += gBVar_pkt_sum[j].filter_bytes_loop[i];
                gBVar_pkt_sum[j].filter_bytes_loop[i] = 0;
                filter_hit_pkts += gBVar_pkt_sum[j].filter_pkts_loop[i];
                gBVar_pkt_sum[j].filter_pkts_loop[i] = 0;
            }
            if(filter_hit_bytes)
            {
                tmplen = sprintf(tmp, "{\"type\":%d,\"time\":%u,\"id\":%u,\"bytes\":\"%"PRIu64"\",\"task_id\":\"%lld\",\"batch_id\":\"%lld\",\"device_id\":\"%u\"}", 204, ns_time, gFilterRuleMap[i], filter_hit_bytes,config_and_define::task_id, config_and_define::batch_id, th_engine_tools::instance()->device_id);
                th_engine_tools::instance()->send_log("slog", NULL, 0, tmp, tmplen);
            }
        }
        usleep(1);
        //-------------------应用协议统计-------------------
        for(int i =1; i < p_th_sum->app_num+1; i++)
        {
            uint64_t app_pkt_num = 0;
            for(int j = 0 ; j < thread_num ;j ++)
            {
                app_pkt_num += p_th_sum->app_pkt_list_loop[i][j];
                p_th_sum->app_pkt_list_loop[i][j] = 0;
            }
            if(app_pkt_num)
            {
                tmplen = sprintf(tmp, "{\"type\":%d,\"time\":%u,\"id\":%u,\"pkts\":\"%"PRIu64"\",\"task_id\":\"%lld\",\"batch_id\":\"%lld\",\"device_id\":\"%u\"}", 206, ns_time, p_th_sum->app_index_2_id[i], app_pkt_num,config_and_define::task_id, config_and_define::batch_id, th_engine_tools::instance()->device_id);
                th_engine_tools::instance()->send_log("slog", NULL, 0, tmp, tmplen);
            }
        }
        usleep(1);
        //-------------------TCP服务端口TOP100-------------------
        port_sort port_sum[65535];
        port_sort *p_portsum_sort[65535];
        int port_idx = 0;
        for(int i =0; i < 65536; i ++)
        {
            uint64_t port_pkt_num = 0;
            for(int j = 0 ; j < thread_num ;j ++)
            {
                port_pkt_num += p_th_sum->tcp_pkt_list_loop[i][j];
                p_th_sum->tcp_pkt_list_loop[i][j] = 0;
            }
            if(port_pkt_num)
            {
                port_sum[port_idx].pkt_count = port_pkt_num;
                port_sum[port_idx].port = i;
                p_portsum_sort[port_idx] = &port_sum[port_idx];
                port_idx ++;
            }
        }
        int idx = 0;
        if(port_idx > 100)
        {
            idx = port_sort_topk(&p_portsum_sort[0], 0, port_idx-1, 100);
            if (idx + 100 < port_idx)
            {
                port_idx = idx + 100;
            }
        }
        for(;idx < port_idx; idx ++ )
        {
            tmplen = sprintf(tmp, "{\"type\":%d,\"time\":%u,\"port\":%u,\"pkts\":\"%"PRIu64"\",\"task_id\":\"%lld\",\"batch_id\":\"%lld\",\"device_id\":\"%u\"}", 207, ns_time, p_portsum_sort[idx]->port, p_portsum_sort[idx]->pkt_count,config_and_define::task_id, config_and_define::batch_id, th_engine_tools::instance()->device_id);
            th_engine_tools::instance()->send_log("slog", NULL, 0, tmp, tmplen);
        }
        usleep(1);
        //-------------------UDP服务端口TOP100-------------------
        port_idx = 0;
        for(int i =0; i < 65536; i ++)
        {
            uint64_t port_pkt_num = 0;
            for(int j = 0 ; j < thread_num ;j ++)
            {
                port_pkt_num += p_th_sum->udp_pkt_list_loop[i][j];
                p_th_sum->udp_pkt_list_loop[i][j] = 0;
            }
            if(port_pkt_num)
            {
                port_sum[port_idx].pkt_count = port_pkt_num;
                port_sum[port_idx].port = i;
                p_portsum_sort[port_idx] = &port_sum[port_idx];
                port_idx ++;
            }
        }
        idx = 0;
        if(port_idx > 100)
        {
            idx = port_sort_topk(&p_portsum_sort[0], 0, port_idx-1, 100);
            if (idx + 100 < port_idx)
            {
                port_idx = idx + 100;
            }
        }
        for(;idx < port_idx; idx ++ )
        {
            tmplen = sprintf(tmp, "{\"type\":%d,\"time\":%u,\"port\":%u,\"pkts\":\"%"PRIu64"\",\"task_id\":\"%lld\",\"batch_id\":\"%lld\",\"device_id\":\"%u\"}", 208, ns_time, p_portsum_sort[idx]->port, p_portsum_sort[idx]->pkt_count,config_and_define::task_id, config_and_define::batch_id, th_engine_tools::instance()->device_id);
            th_engine_tools::instance()->send_log("slog", NULL, 0, tmp, tmplen);
        }
    }
    return 0;
}

int read_pb_summary()
{
    string filepath = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/statistics/pb_statistics.map";
    FILE *pfile = fopen(filepath.c_str(), "r");
    if (pfile)
    {
        int len;
        fseek(pfile,0,SEEK_END);
        len = ftell(pfile);
        fseek(pfile,0,SEEK_SET);
        if (len > 0)
        {
            uint32_t pb_type;
            uint64_t pb_num, pb_bytes;
            while(3 == fscanf(pfile, "%u:%"PRIu64":%"PRIu64"", &pb_type, &pb_num, &pb_bytes))
            {
                if(pb_type < 10000)
                {
                    type_pb_num[pb_type] = pb_num;
                    type_pb_bytes[pb_type] = pb_bytes;
                }
            }
        }
        fclose(pfile);
    }
    return 0;
}

int update_pb_summary()
{
    string filepath = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/statistics/pb_statistics.map";
    string filepath_back = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/statistics/.pb_statistics.map.tmp";

    FILE *pfile = fopen(filepath_back.c_str(), "w");
    if (pfile)
    {
        for(uint32_t i = 0; i < 10000; i ++)
        {
            if(type_pb_num[i])
            {
                fprintf(pfile, "%u:%"PRIu64":%"PRIu64"\n", i, type_pb_num[i], type_pb_bytes[i]);
            }
        }
        fclose(pfile);
        rename(filepath_back.c_str(), filepath.c_str());
        return 0;
    }
    return 1;
}



// Last Update:2019-02-21 15:06:36
/**
 * @file hash.c
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-08-26
 */
#include "hash.h"
#include <stdint.h>
//创建哈希表
HASH_TABLE* create_hash_table()
{
    HASH_TABLE* pHashTbl = (HASH_TABLE*)malloc(sizeof(HASH_TABLE));
    memset(pHashTbl, 0, sizeof(HASH_TABLE));
    return pHashTbl;
}
//在哈希表中查找数据
NODE* find_data_in_hash(HASH_TABLE* pHashTbl, uint64_t ldata)
{
    uint64_t data = (uint64_t)ldata;
    NODE* pNode;
    if(NULL ==  pHashTbl)
        return NULL;

    pNode = pHashTbl->value[data % HASHMAX];
    if(pNode == NULL) 
        return NULL;
    return pNode ;
    /*
    while(pNode){
        if(data == pNode->data)
            return pNode;
        pNode = pNode->next;
    }*/
    return NULL;
}
//在哈希表中插入数据
STATUS insert_data_into_hash(HASH_TABLE* pHashTbl, uint64_t ldata,void * data_str)
{
    uint64_t data = (uint64_t)ldata;
    NODE* pNode;
    if(NULL == pHashTbl)
        return FALSE;

    pNode = pHashTbl->value[data % HASHMAX];
    if(NULL == pNode){
        pNode = (NODE*)malloc(sizeof(NODE));
        memset(pNode, 0, sizeof(NODE));
        pNode->data = data;
        pNode ->data_str = data_str ;
        pHashTbl->value[data % HASHMAX] = pNode;
        return TRUE;
    }

  /* if(NULL != find_data_in_hash(pHashTbl, data))
    {
        return FALSE;
    }*/

   /* pNode = pHashTbl->value[data % HASHMAX];
    while(NULL != pNode->next)
    {
        pNode = pNode->next;
    }

    pNode->next = (NODE*)malloc(sizeof(NODE));
    memset(pNode->next, 0, sizeof(NODE));
    pNode->next->data = data;
    */
    return TRUE;
}
//从哈希表中删除数据
STATUS delete_data_from_hash(HASH_TABLE* pHashTbl, uint64_t ldata)
{
    uint64_t data = (uint64_t)ldata;
    NODE* pHead;
    NODE* pNode;
    if(NULL == pHashTbl || NULL == pHashTbl->value[data % HASHMAX])
        return FALSE;


    if(pNode == pHashTbl->value[data % HASHMAX]){
      //  pHashTbl->value[data % HASHMAX] = pNode->next;
        free(pNode);
    }
    return TRUE;

   /* pHead = pHashTbl->value[data % HASHMAX ];
    while(pNode != pHead ->next)
        pHead = pHead->next;
    pHead->next = pNode->next;
    return TRUE;*/

}
/*
void main()
{
    HASH_TABLE* hashtable=create_hash_table();
    insert_data_into_hash(hashtable,1);
    //insert_data_into_hash(hashtable,4);
    insert_data_into_hash(hashtable,11);
    insert_data_into_hash(hashtable,21);
    NODE* node1=find_data_in_hash(hashtable,11);
    NODE* node2=find_data_in_hash(hashtable,21);
    printf("hashtable 1 : %d \n",hashtable->value[1]->data);
    if(hashtable->value[2]==NULL) printf("hashtable 2 is null\n");
    printf("hashtable 1 : %d \n",node1->data);
    printf("hashtable 1 : %d \n",node2->data);
    delete_data_from_hash(hashtable,21);
    NODE* node3=find_data_in_hash(hashtable,21);
    if(node3==NULL) printf("21 is cancel\n");
    else printf("hashtable 1 : %d \n",node3->data);

}*/

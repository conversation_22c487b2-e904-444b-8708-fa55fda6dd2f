// Last Update:2019-10-05 20:33:25
/**
 * @file TH_engine_define.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-09-28
 */
#include "TH_engine_define.h"

uint32_t config_and_define::max_packet_rule = 1024000 ;// 最大的规则数
// client_handle *config_and_define::p_ms_handle = th_engine_tools::instance()->p_ms_client;
string      config_and_define::zmq_addr = "tcp://127.0.0.1:5557";
int         config_and_define::zmq_len = 100000;
bool        config_and_define::b_save_pb_file = false;
bool        config_and_define::b_zmq_active = true;
uint32_t    config_and_define::work_mode = 0 ;// 
uint32_t    config_and_define::link_parse_mode = 0;
uint32_t    config_and_define::parse_thread_num = 1;
long long   config_and_define::task_id = 0;
long long   config_and_define::batch_id = 1;
bool        config_and_define::b_move_files = true;
bool        config_and_define::b_brush_off_syn = true;
bool        config_and_define::b_segment_analyse = false;
int         config_and_define::pb_write_mode = 0;
int         config_and_define::pb_file_time = 60;
bool        config_and_define::b_rule_pb_save = false;
uint32_t    config_and_define::final_thread_num = 1;
bool        config_and_define::b_save_json_file = false;
bool        config_and_define::b_pb_rename = true;
bool        config_and_define::b_save_white_json = false;
uint32_t    config_and_define::port_num = 1;
char        config_and_define::port_pci[MAX_IF_NUM][32] = {0};
char        config_and_define::port_mac[MAX_IF_NUM][18] = {0};
char        config_and_define::port_pos[MAX_IF_NUM][128] = {0};
uint32_t    config_and_define::session_bucket_tosec = 1;
uint32_t    config_and_define::session_bucket_num = 90;
uint32_t    config_and_define::mbps_filter_out = 0;

extern "C"
{
    TH_ENGINE_GVAR th_engine_g_var;
}


// Last Update:2019-04-08 17:58:43
/**
 * @file hash_doublelist.c
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-08-26
 */
#include "hash_doublelist.h"
#include  <stdlib.h>
void * hd_find(struct hash_link * p_hl_head , uint64_t index ,void * data , cmp_ptr ptr )
{
   // printf("hd_find  index= %d \n",index);
    NODE * p_hs_node = find_data_in_hash(p_hl_head -> p_head, index);
    if(p_hs_node != NULL) 
    {
        struct _list *  p_list = ( struct _list *)p_hs_node ->data_str ;
        if(p_list == NULL) 
        {
            //p  = (struct _list *)malloc (sizeof(struct _list));
            //memset(p,0x0,sizeof(struct _list));
            //insert_data_into_hash(p_hl_head -> p_head, index,(void *)p);
            return NULL ;
        }
        LinkNode * pNode = find_node (p_list ,data ,ptr);
        if(pNode != NULL) 
        {
            return (void*)pNode ->data ;
        }
    }
    return NULL ;

}
void hd_init(struct hash_link * p_hl_head)
{
    for(uint64_t i = 0 ; i < HASHMAX; i ++)
    {
        struct _list *p  = (struct _list *)malloc (sizeof(struct _list));
        memset(p,0x0,sizeof(struct _list));
        insert_data_into_hash(p_hl_head -> p_head, i,(void *)p);
    }
}
// 增加 节点 
void hd_insert (struct hash_link * p_hl_head , uint64_t index ,void * data  )
{
    NODE* p_hs_node = find_data_in_hash(p_hl_head -> p_head, index);
    struct _list *  p = NULL ;
    if(p_hs_node == NULL) 
    {
        exit(-1);
    }
    else 
    {
       p = ( struct _list *)p_hs_node ->data_str ;
       if(p == NULL) 
       {
           exit(-1);
       }
    }
    lpush_data(p ,data);

}
void hd_delete(struct hash_link * p_hl_head , uint64_t index , void * data  , cmp_ptr ptr) 
{
    //printf("hd_delete  index= %d \n",index);
    NODE * p_hs_node = find_data_in_hash(p_hl_head -> p_head, index);
    if(p_hl_head != NULL) 
    {
        struct _list * p_list = (struct _list * )(p_hs_node -> data_str ) ;
        if(p_list  != NULL) 
        {
            LinkNode * p =  find_node (p_list , data , ptr);
            if(p != NULL)
            {
                loutlistnode(p_list , p );
                free(p);
            }
            else
            {
                printf("hd_delete  open file  error \n");
                //sleep(10);
            }
        }
    }
}

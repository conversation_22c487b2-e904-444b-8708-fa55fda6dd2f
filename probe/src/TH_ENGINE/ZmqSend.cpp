// Last Update:2018-10-02 13:32:56
/**
 * @file CZmqSend.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2015-09-09
 */
#include "ZmqSend.h"
#define ZMQSENDSING 0x00001



CZmqSend::CZmqSend ()
{
    p_zmq = NULL;
}
CZmqSend::CZmqSend (string  addr ,int len, int sign)
{
    add_Addr(addr , len , sign ) ;
}
void CZmqSend::add_Addr(string addr , int len , int  sign ) 
{
    p_zmq = new zmq_client(addr,len);
}

CZmqSend::~CZmqSend()
{

            if(p_zmq != NULL)
                delete p_zmq ;
            p_zmq = NULL;
}

void CZmqSend:: SendData(int  len ,char * buff)
{
                    
       p_zmq -> send(buff, len);
}


void CZmqSend::timeout()
{

}

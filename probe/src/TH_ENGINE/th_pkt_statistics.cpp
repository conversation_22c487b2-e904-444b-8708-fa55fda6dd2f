#include "th_pkt_statistics.h"
#include <json/json.h>
#include <fstream>

// th_pkt_sum g_th_pkt_sum;

th_pkt_sum::th_pkt_sum()
{
    app_pkt_list = NULL;
    app_pkt_list_loop = NULL;
    tcp_pkt_list = NULL;
    tcp_pkt_list_loop = NULL;
    udp_pkt_list = NULL;
    udp_pkt_list_loop = NULL;
    app_id_2_index = NULL;
    app_index_2_id = NULL;
    app_num = 0;
    thread_num = 0;
    min_app_id = 0xFFFF;
    max_app_id = 0;
}

int th_pkt_sum::init(int tn)
{
    thread_num = tn;

    string conf_path = string(getenv("THE_DB_PATH")) + "/app_id_list.json";
    Json::Value val;
    Json::Reader *parser = new Json::Reader();
    ifstream infile(conf_path.c_str());
    parser->parse(infile, val, false);
    app_num = (uint16_t)val.size();
    for(int i = 0;i < app_num;i++)
    {
        uint16_t app_id = (uint16_t)atoi(val[i]["app_id"].asCString());
        if(min_app_id > app_id)
        {
            min_app_id = app_id;
        }
        if(app_id > max_app_id)
        {
            max_app_id = app_id;
        }
    }
    app_index_2_id = new uint16_t[app_num+1]();
    app_id_2_index = new uint16_t[max_app_id - min_app_id + 1]();
    for(int i = 0;i < app_num;i++)
    {
        uint16_t app_id = (uint16_t)atoi(val[i]["app_id"].asCString());
        app_index_2_id[i+1] = app_id;
        app_id_2_index[app_id-min_app_id] = i+1;
    }
    infile.close();
    delete parser;

    app_pkt_list = new uint64_t *[app_num+1]();
    app_pkt_list_loop = new uint32_t *[app_num+1]();
    for(int i =1; i < app_num+1; i++)
    {
        app_pkt_list[i] = new uint64_t[thread_num]();
        app_pkt_list_loop[i] = new uint32_t[thread_num]();
    }
    tcp_pkt_list = new uint64_t *[65536]();
    tcp_pkt_list_loop = new uint32_t *[65536]();
    udp_pkt_list = new uint64_t *[65536]();
    udp_pkt_list_loop = new uint32_t *[65536]();
    for(int i =0; i < 65536; i++)
    {
        tcp_pkt_list[i] = new uint64_t[thread_num]();
        tcp_pkt_list_loop[i] = new uint32_t[thread_num]();
        udp_pkt_list[i] = new uint64_t[thread_num]();
        udp_pkt_list_loop[i] = new uint32_t[thread_num]();
    }
    return 0;
}

th_pkt_sum::~th_pkt_sum()
{
    if(tcp_pkt_list)
    {
        for(int i = 0; i < 65536; i++)
        {
            if(tcp_pkt_list[i])
            {
                delete [](tcp_pkt_list[i]);
            }
        }
        delete []tcp_pkt_list;
        tcp_pkt_list = NULL;
    }
    if(tcp_pkt_list_loop)
    {
        for(int i = 0; i < 65536; i++)
        {
            if(tcp_pkt_list_loop[i])
            {
                delete [](tcp_pkt_list_loop[i]);
            }
        }
        delete []tcp_pkt_list_loop;
        tcp_pkt_list_loop = NULL;
    }
    if(udp_pkt_list)
    {
        for(int i = 0; i < 65536; i++)
        {
            if(udp_pkt_list[i])
            {
                delete [](udp_pkt_list[i]);
            }
        }
        delete []udp_pkt_list;
        udp_pkt_list = NULL;
    }
    if(udp_pkt_list_loop)
    {
        for(int i = 0; i < 65536; i++)
        {
            if(udp_pkt_list_loop[i])
            {
                delete [](udp_pkt_list_loop[i]);
            }
        }
        delete []udp_pkt_list_loop;
        udp_pkt_list_loop = NULL;
    }
    if(app_pkt_list)
    {
        for(int i =1; i < app_num+1; i++)
        {
            delete [](app_pkt_list[i]);
        }
        delete []app_pkt_list;
        app_pkt_list = NULL;
    }
    if(app_pkt_list_loop)
    {
        for(int i =1; i < app_num+1; i++)
        {
            delete [](app_pkt_list_loop[i]);
        }
        delete []app_pkt_list_loop;
        app_pkt_list_loop = NULL;
    }
    if(app_index_2_id)
    {
        delete []app_index_2_id;
        app_index_2_id = NULL;
    }
    if(app_id_2_index)
    {
        delete []app_id_2_index;
        app_id_2_index = NULL;
    }
}
// Last Update:2018-09-26 21:27:22
/**
 * @file session_hash.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-08-27
 */

#ifndef SESSION_HASH_H
#define SESSION_HASH_H
#include "hash_doublelist.h"
#include "stack.h"
#include "th_engine_packet.h"
#include "th_engine_session.h"
typedef  struct session_hash{
    struct hash_link * p_hl ; // 工作hash session
    SqStack * mSqstack ;    // 闲置 session 
    int max_session_num ; // 最大数量
}session_hash;// 

uint64_t t_packet_crc64(PacketInfo  * p_pstr);
void session_init(session * p_session);
void session_hash_init(session_hash * p_session_hash) ;
session * find_session( PacketInfo  * p_pstr, session_hash * p_session_hash);
session * create_session( PacketInfo  * p_pstr, session_hash * p_session_hash);
int del_session (session_hash * p_session_hash,session *  p_session ) ;
void resove_session_hash(session_hash * p_session_hash );
int del_session_hash(session_hash * p_session_hash ,session *  p_session);
int session_tostack(session_hash * p_session_hash ,session *  p_session);


#endif  /*SESSION_HASH_H*/

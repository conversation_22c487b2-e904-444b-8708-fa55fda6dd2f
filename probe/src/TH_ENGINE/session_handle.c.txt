// Last Update:2019-10-05 21:57:40
/**
 * @file session_handle.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-08- */
#include "session_handle.h"
#include  "session_hash.h" 
#include  "time_out_marge.h"
#include <pthread.h>
#include <sched.h>
#include <unistd.h>
#include  "CheckSerialNumber.h"
#include  "no_lock_queue.h"
#include  "read_conf_commit_c.h"
#include "ntp_statistics.h"
#include <libxml/parser.h>

static int thread_init_done_num = 0;

typedef struct msg_queue_
{
    NQueue * p_recv ;
    NQueue * p_send ;
}msg_queue ;
typedef struct thread_info
{
    session_hash * p_session_hash ;
    time_out_marge * p_tmo_marge ;
    uint32_t ts ;
    uint32_t  b_timeout; // 已超时桶个数
    void * p_info ;// 用户自己使用的对象
    msg_queue msg ;
    uint64_t pkt_handle;
}thread_info;
thread_info * p_thread_info = NULL;
msg_queue ** pp_msg_queue = NULL ;
int thread_sum = 0 ;
int  n_log_time = 30; // log 统计
int n_to_thread = 0 ;

void  * pdata = NULL  ;
int ii_thread_num[64];
SEngineHandleBase * p_Base = NULL ;
static int b_segment_analyse = 0;

extern "C" 
{
    void recovery_send(session  * p_session , int nThread ) ;
    void session_recovery( struct _list * p_list, int nThread);
    int TimeOutLan(SEngineHandleBase * pBase  ,int  nThread ,uint32_t s);   //会话列表超时清理
    void Time_Handle(SEngineHandleBase * pBase  ,int  nThread, uint32_t timeout_ts)
    {
        if(p_thread_info[nThread].b_timeout > g_conf_time_mage)
        {
            p_thread_info[nThread].b_timeout = g_conf_time_mage;
        }
        while(p_thread_info[nThread].b_timeout > 0)
        {
            __sync_fetch_and_sub(&(p_thread_info[nThread].b_timeout),1);
            TimeOutLan(pBase , nThread , p_thread_info[nThread].ts);
        }
        //  框架超时
        pBase -> TimeOut(NULL ,nThread , timeout_ts);
    }

    void *thr_log_fn(void *arg)
    {
#ifdef RES
        CheckSerialNumber();
#endif
        while(thread_init_done_num < thread_sum)
        {
            usleep(1);
        }
        time_t timep_log_begin;
        time_t timep_thread_begin;
        time_t timep_end;

        time (&timep_log_begin);
        timep_thread_begin = timep_log_begin;
        time_t check_time_tt = timep_thread_begin ;
        // printids("new thread: ");
        for(;;)
        {
            // 设置超时
            // time out 
            time (&timep_end);
            for(int i = 0 ;i < thread_sum ; i++)
            {
                p_thread_info[i].ts = (uint32_t)timep_end;
            }
            if(timep_end > timep_log_begin && timep_end - timep_log_begin >= n_log_time)
            {
                // 调用 日志发送-
                if(p_Base != NULL &&  p_Base -> th_log_pcesses  != NULL)
                {
                    p_Base -> th_log_pcesses((uint32_t)timep_end);
                    timep_log_begin = timep_end ;
                }
            }
            while(timep_end > timep_thread_begin && timep_end - timep_thread_begin >= g_conf_time_out)
            {
                for(int i = 0 ;i < thread_sum ; i++)
                {
                    if(p_thread_info[i].pkt_handle)
                    {
                        __sync_fetch_and_add(&(p_thread_info[i].b_timeout),1);
                    }
                }
                timep_thread_begin += g_conf_time_out ;
            }
#ifdef RES
            if(timep_end > check_time_tt && timep_end - check_time_tt > 360)
            {
                check_time();
                check_time_tt = timep_end;
            }
#endif
            sleep(1);
            //timep_begin = timep_end ;
        }

        return NULL;
    }
    static void parse_segment_analyse_conf()
    {
        char *path = "./conf/th_engine_conf.xml";
        xmlDocPtr doc = NULL;
        xmlNodePtr curNode = NULL;
        xmlNodePtr curNode_son = NULL;
        doc = xmlReadFile(path, "UTF-8", XML_PARSE_RECOVER);
        if (NULL == doc)
        {
            return;
        }
        curNode = xmlDocGetRootElement(doc);
        if (NULL == curNode)
        {
            xmlFreeDoc(doc);
            return;
        }
        if (0 != xmlStrcmp(curNode->name, (xmlChar*) "config"))
        {
            xmlFreeDoc(doc);
            return;
        }
        curNode = curNode->xmlChildrenNode;
        int i_plugin_id = 0;
        char* p_tmp = NULL;
        for(; curNode != NULL; curNode = curNode->next)
        {
            if( xmlStrcmp(curNode->name, (xmlChar*) "b_segment_analyse") == 0)
            {
                p_tmp = (char*)xmlNodeGetContent(curNode);
                if (NULL != p_tmp)
                {
                    if(0 == strcasecmp(p_tmp, "true"))
                    {
                        b_segment_analyse = 1;
                    }
                }
                xmlFree(p_tmp);
                p_tmp = NULL;
            }
        }
        xmlFreeDoc(doc);
        return;
    }
    void  session_handle_PrecessInit( SEngineHandleBase * pBase,int numThread, uint8_t port_num)
    {
        int i = 0, j = 0;
        parse_segment_analyse_conf();
        p_Base = pBase;
        thread_sum = numThread; 
        // 调用处理模块初始化接口
        if(p_Base -> PrecessInit != NULL)
        {
            p_Base -> PrecessInit((void **)&pdata , thread_sum, port_num);
        }
        // 定义进程初始化
        n_to_thread = numThread ;
        // 创建日志系统线程
        pthread_t ntid;
        struct sched_param sched;
        pthread_attr_t attr;//定义属性变量
        //set_thread_policy(&attr,SCHED_FIFO);
        pthread_attr_init(&attr);//初始化属性变量
        pthread_attr_setdetachstate(&attr,PTHREAD_CREATE_DETACHED);//设置属性变量
        int err = pthread_create(&ntid, &attr, thr_log_fn, NULL);
        if (err != 0)
        {
            printf("can't create thread: %s\n", strerror(err)); 
        }
    }
    //  
    void  packetInfo_init(PacketInfo * p_packet)
    {
        //  
        p_packet -> p_data = NULL ;
        p_packet -> b_no_session = 0;
        p_packet -> ts = 0 ;
        p_packet -> len = 0 ;
        p_packet -> pid = 0 ;
        p_packet -> p_packet = NULL ;
        p_packet -> signlen = 0;
    }
    void  session_handle_PrecessClose(SEngineHandleBase * pBase)
    {
        //  进程关闭
        int i = 0 ;
        for(;i < thread_sum; i++) 
        {
            resove_session_hash(p_thread_info[i].p_session_hash);
            // 判断 
        }
        // 判断
        pBase -> PrecessClose();
    }

static int thread_0_initdone = 0;
    void  session_handle_ThreadInit(SEngineHandleBase * pBase, int nThread)// 第多少个线程 
    {
        pBase -> ThreadInit(nThread);
        if(0 == nThread)
        {
            p_thread_info  = (thread_info *)malloc (sizeof(thread_info)  * thread_sum);
            pp_msg_queue = (msg_queue ** )malloc(sizeof(msg_queue*) * thread_sum);
            read_ini_c("conf/common_conf.ini");
            __sync_fetch_and_add(&thread_0_initdone, 1);
        }
        while(0 == thread_0_initdone)
        {
            usleep(1);
        }
        p_thread_info[nThread].pkt_handle = 0;
        p_thread_info[nThread].b_timeout = 0;
        p_thread_info[nThread].p_tmo_marge = time_out_marge_create(g_conf_time_mage);
        p_thread_info[nThread].p_session_hash = (session_hash *) malloc(sizeof(session_hash));
        p_thread_info[nThread].p_session_hash -> max_session_num = (uint32_t)atoi(get_ini_value("session","thread_session_num"));
        p_thread_info[nThread].msg.p_recv = N_queue_new(1000);
        p_thread_info[nThread].msg.p_send = N_queue_new(1000);
        session_hash_init(p_thread_info[nThread].p_session_hash ) ;
        p_thread_info -> p_info = NULL ; 
        pp_msg_queue[nThread]=&(p_thread_info[nThread].msg);
        // 线程初始化   ---- 
        __sync_fetch_and_add(&thread_init_done_num, 1);
    }

    void  session_handle_ThreadClose(SEngineHandleBase * pBase, int nThread )// 第多少个线程 
    {
        pBase -> ThreadClose(nThread) ;
    }
    // 
    void Handle(SEngineHandleBase * pBase,char * buf , int len ,int nThread, int first_proto, uint8_t port_id)   //使用墙上时间
    {
        uint32_t time_ns[2] = {0};
        Handle_ns(pBase, buf , len ,time_ns, nThread, first_proto, port_id);
    }
    void Handle_ns(SEngineHandleBase * pBase,char * buf , int len ,uint32_t time_ns[2], int nThread, int first_proto, uint8_t port_id)   //使用指定时间
    {
        //check timeout session
        p_thread_info[nThread].pkt_handle ++;
        if(p_thread_info[nThread].b_timeout > g_conf_time_mage) 
        {
            p_thread_info[nThread].b_timeout = g_conf_time_mage;
        }
        while(p_thread_info[nThread].b_timeout > 0)
        {
            __sync_fetch_and_sub(&(p_thread_info[nThread].b_timeout),1);
            TimeOutLan(pBase , nThread , p_thread_info[nThread].ts);
        }
        //check timeout session --end

        // init PacketInfo
        PacketInfo packet ;
        packetInfo_init(&packet);
        packet.pid = nThread ;
        packet.ts = time_ns[0] ;
        packet.nts = time_ns[1];
        packet.p_data = (unsigned char*)buf ;
        packet.len = len ;
        // init PacketInfo --end

        // packet 解析 
        pBase -> PacketParse(&packet,nThread, first_proto, port_id);
        // packet 解析 --end 

        if(packet.b_no_session == 0)
        {
            // 判断是否需要创建session  ---- 单包规则 
            //  pBase -> PacketParse(&packet);
            // 定义进程初始化
            session * p_session = find_session(&packet,p_thread_info[nThread].p_session_hash );
            if(p_session == NULL )
            {
                int  n = 0;
                if(pBase->BeCreateSession != NULL) 
                {
                    n = pBase->BeCreateSession(&packet,nThread);
                }
                if(n == 0) // 需要创建session 
                {
                    p_session = create_session(&packet,p_thread_info[nThread].p_session_hash );
                    while(p_session == NULL  )
                    {
                        printf(" &&&&&&&&&&&&&&&&&&&  %d   session 耗尽   \n",nThread);
                        //回收资源再创建
                        // __sync_fetch_and_add(&(p_thread_info[nThread].b_timeout),1);

                        gpointer  p =(gpointer) N_queue_dequeue(p_thread_info[nThread].msg.p_recv );
                        if(p != NULL) 
                        {   
                            session_recovery((struct _list *)p , nThread);
                           // p =(gpointer) N_queue_dequeue(p_thread_info[nThread].msg.p_recv );
                        }

                        p_session = create_session(&packet,p_thread_info[nThread].p_session_hash );
                        if(p_session == NULL) 
                        {
                            TimeOutLan(pBase , nThread , p_thread_info[nThread].ts);
                            p_session = create_session(&packet,p_thread_info[nThread].p_session_hash );
                        }
                    }
                    p_session -> sy_crc64 =  packet.sy_crc64 ;
                    // 添加到超时桶
                    // tmo_session_update((session *)p_session , p_thread_info[nThread].p_tmo_marge);
                }
                else  // 走不需要创建session 流程 ，非正常session 处理流程
                {
                    if(pBase -> NoSessionHandle != NULL)
                    {
                        //every pkt need goto Handle
                        pBase -> NoSessionHandle(&packet ,nThread );
                    }
                    return ;
                }
            }
            tmo_session_update((session *)p_session , p_thread_info[nThread].p_tmo_marge);

            //  init session_pub and pkt directory
            if(pBase -> Packet_Rule!= NULL)
            {
                pBase-> Packet_Rule(&packet,p_session,nThread);
            }
            //  init session_pub and pkt directory --end

            //every pkt need goto Handle
            pBase ->Handle(&packet , p_session , nThread) ;

  #if 0  
            gpointer  p =(gpointer) N_queue_dequeue(p_thread_info[nThread].msg.p_recv );
            while(p != NULL) 
            {
                session_recovery((struct _list *)p , nThread);
                p =(gpointer) N_queue_dequeue(p_thread_info[nThread].msg.p_recv );
            }

  #endif
        }
        else //非IP报文 需要记录到pcap中
        {
            //every pkt need goto Handle
            pBase ->Handle(&packet , NULL , nThread) ;
        }
        return;
    }
    int TimeOutLan(SEngineHandleBase * pBase  ,int  nThread ,uint32_t s)
    {
        struct _list * session_list =  tmo_begin(p_thread_info[nThread].p_tmo_marge); 
        if(session_list != NULL)
        {
            //printf(" session_list -> len  ==== %d\n", session_list -> len );
            if(session_list -> len  != 0 )
            {
                struct _node * p = session_list -> firstNode ;
                int  i =  0; 
                for(;p != NULL &&  i < session_list -> len ; i++ ) 
                {
                    struct _node * q = p ;
                    if(p == p->nextNode) break  ;
                    p = p -> nextNode ;
                    session * p_session = (session *)q ->data;
                    // 删除查询索引
                    p_session -> use_num = -1;
                    p_Base -> ResourcesRecovery(p_session , nThread);

                    p_Base -> SessionRecovery(p_session , nThread );
                    del_session(p_thread_info[nThread].p_session_hash,p_session);
                }
            }
            tmo_end(p_thread_info[nThread].p_tmo_marge);
            free(session_list) ;
         //   return 1;

        }
        //tmo_end(p_thread_info[nThread].p_tmo_marge);
      //  session_recovery(session_list , nThread);
        return 1;

    }
    uint64_t Hash_Handle(SEngineHandleBase * pBase, unsigned char *buf, uint32_t len, int first_proto)
    {
        PacketInfo packet;
        packetInfo_init(&packet);
        packet.p_data = buf;
        packet.len = len;
        pBase->PacketHash(&packet, first_proto);
        if(packet.signlen != 0)
        {
            t_packet_crc64(&packet);
            return packet.sy_crc64;
        }
        return 0;
        
    }
    //  timeout 
    int TimeOut(SEngineHandleBase * pBase  ,int  nThread ,uint32_t s)
    {
        //        return  1;
        struct _list * session_list =  tmo_begin(p_thread_info[nThread].p_tmo_marge); 
        if(session_list != NULL)
        {
            //printf(" session_list -> len  ==== %d\n", session_list -> len );
            if(session_list -> len  == 0 )
            {
                free(session_list) ;
            }
            else 
            {
                struct _node * p = session_list -> firstNode ;
                int  i =  0; 
                for(;p != NULL &&  i < session_list -> len ; i++ ) 
                {
                    struct _node * q = p ;
                    if(p == p->nextNode) break  ;
                    p = p -> nextNode ;
                    session * p_session = (session *)q ->data;
                    // 删除查询索引
                    del_session_hash(p_thread_info[nThread].p_session_hash , p_session);
                    //----
                    /*
                       p_session -> use_num = -1;
                       p_Base -> ResourcesRecovery(p_session , n_to_thread);
                       */

                }
                if(N_queue_enqueue (pp_msg_queue[nThread]->p_send,session_list) == 0)
                {
                    struct _list *  p_list = (struct _list *) session_list;
                    struct _node * p = p_list -> firstNode ;
                    int m = 0 ;
                    for(;p != NULL&& m < p_list ->len ;m++ ) 
                    {
                        struct _node * q = p ;
                        if(p == p->nextNode) break  ;
                        p = p -> nextNode ;
                        session * p_session = (session *)q ->data;
                        {
                            recovery_send(p_session, nThread);
                        }
                    }
                    session_recovery(session_list , nThread);
                }
            }
            //session_recovery(session_list , nThread);

        }
        tmo_end(p_thread_info[nThread].p_tmo_marge);
        pBase -> TimeOut(NULL ,nThread ,s);
        return 1;

    }
    // 推入超时线程处理 
    void  recovery_send(session  * p_session , int nThread ) 
    {
        p_Base -> ResourcesRecovery(p_session , nThread);
        p_session -> use_num = -1;
    }
    // 资源回收 
    void session_recovery( struct _list * p_list, int nThread)
    {
        // 
        if(p_list == NULL) 
        {
            return ;
        }
        struct _node * p = p_list -> firstNode ;
        int i = 0;
        for(;p != NULL &&   i  < p_list -> len ; i++ ) 
        {
            struct _node * q = p ;
            if(p == p->nextNode) break  ;
            p = p -> nextNode ;
            session * p_session = (session *)q ->data;
            {
                p_Base -> SessionRecovery(p_session , nThread );
                session_tostack(p_thread_info[nThread].p_session_hash,p_session);
                //recovery_session(p_session, nThread);
            }
        }
        free(p_list);
    }
    void SetDevInfo(SEngineHandleBase * pBase  ,int portid , char *pci, char *mac, char *pos)
    {
        p_Base->SetDevInfo(portid, pci, mac, pos);
    }
    void link_ntpsrc()
    {
    }
};




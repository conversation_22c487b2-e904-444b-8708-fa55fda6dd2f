// Last Update:2021-09-09 14:37:17
/**
 * @file ip_position.h
 * @brief ip 定位模块
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-10-09
 */

#ifndef IP_POSITION_H
#define IP_POSITION_H

#include <string>
#include <iostream>
#include <fstream>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <json/json.h>
#include <string.h>
#include "ip2address.h"

static pthread_mutex_t user_position_thread_mutex = PTHREAD_MUTEX_INITIALIZER;
class  position_info {
    public:
        position_info()
        {
            country = "";
            province = "";
            city = "";
            latitude = 0 ;
            longitude = 0;
             
        }
        ~position_info() {
       }
        position_info(const  position_info &  m) 
        {
            this -> country = m.country;
            this -> province  = m.province ;
            this -> city = m.city;
            this -> longitude = m.longitude;
            this -> latitude = m.latitude ;
        }
        position_info &operator=(const position_info& s) 
        {
            this -> country = s.country;
            this->province = s.province;
            this ->  city  =  s.city;
            this -> longitude = s.longitude;
            this -> latitude = s.latitude ;
            return *this;
        }
       bool operator==(const position_info& s) 
        {
            if(this -> country == s.country  && this->province == s.province && this ->  city  ==  s.city)  
            {
                return true;
            } else {
                return false;
            }
        }
       bool operator<(const position_info& s) const
       {
           if (this -> country < s.country  ) return true;
           else if (this -> country > s.country  )  return false;
           else if (this->province <  s.province ) return true;
           else if  (this->province >  s.province ) return false;
           else if (this ->  city  < s.city)  return true;
           return false;

       }
       bool operator>(const position_info& s)  const
       {
           if (this -> country > s.country  ) return true;
           else if (this -> country < s.country  )  return false;
           else if (this->province >  s.province ) return true;
           else if  (this->province <  s.province ) return false;
           else if (this ->  city  > s.city)  return true;
           return false;

       }


        std::string country  ;
        std::string  province  ; 
        std::string city ;
        double longitude ;
        double latitude ;

};
class IphashMap{
    public:
        IphashMap(){
            p_hashmap = NULL;
            all_ip_pos = 0;
        }
        ~IphashMap(){ 
            if (p_hashmap != NULL) {
                delete[] p_hashmap;
            }
        }
        void addIP(uint32_t ip, int code, uint8_t poscode)
        {
            if(0 == poscode)
            {
                return;
            }
            if(code <= 16)
            {
                all_ip_pos = poscode;
                if(p_hashmap)
                {
                    delete[] p_hashmap;
                    p_hashmap = NULL;
                }
            }
            else
            {
                uint16_t ip_low = (uint16_t)(ip & 0xffff);
                code -= 16;
                uint16_t ip_low_begin = ip_low;
                uint16_t ip_low_end = ip_low;
                if(16 != code)
                {
                    ip_low_begin = ip_low & ((uint16_t)0xffff << (16 - code));
                    ip_low_end = ip_low | ((uint16_t)0xffff >> code);
                }
                if(NULL == p_hashmap)
                {
                    if(poscode != all_ip_pos)
                    {
                        p_hashmap = new uint8_t[65536];
                        memset(p_hashmap, all_ip_pos, 65536);
                        all_ip_pos = 0;
                        for(uint32_t i = ip_low_begin; i <= ip_low_end; i ++)
                        {
                            p_hashmap[i] = poscode;
                        }
                    }
                }
                else
                {
                    for(uint32_t i = ip_low_begin; i <= ip_low_end; i ++)
                    {
                        p_hashmap[i] = poscode;
                    }
                }
            }
        }
        uint8_t findIP(uint32_t ip)
        {
            if(NULL == p_hashmap)
            {
                return all_ip_pos;
            }
            else
            {
                uint16_t ip_low = (uint16_t)(ip & 0xffff);
                return p_hashmap[ip_low];
            }
        }
    private:
        uint8_t *p_hashmap;
        uint8_t all_ip_pos;
};
class  user_position_pos{
    private :
        bool b_init = false;
        IphashMap* IpMap[65536];
        position_info code_position_info_map[256];
        std::map<position_info, uint8_t > position_info_code_map;
        uint32_t poscode_num;   //最多支持255个地理位置信息
       // std::map<uint32_t,position_info >  ip_position_map ; 
    public:
        user_position_pos(){
            poscode_num = 1;
            memset(IpMap,0x0,65536 * sizeof(IphashMap*));
            string conf_path = string(getenv("THE_CONF_PATH"))+"/user_ip_position.json";
            init(conf_path) ;
        }
        void add_ip_code(uint32_t ip , int code  , uint8_t pos_code) {
            // 计算
            if(0 == pos_code)
            {
                return;
            }
            if(code > 32)
            {
                code = 32;
            }
            uint16_t ip_high = (uint16_t)(ip >> 16);
            if(code < 16)
            {
                uint16_t ip_high_begin = 0;
                uint16_t ip_high_end = 0xffff;
                if(0 != code)
                {
                    ip_high_begin = ip_high & ((uint16_t)0xffff << (16 - code));
                    ip_high_end = ip_high | ((uint16_t)0xffff >> code);
                }
                for(uint32_t i = ip_high_begin; i <= ip_high_end; i ++)
                {
                    if(NULL == IpMap[i])
                    {
                        IpMap[i] = new IphashMap();
                    }
                    IpMap[i]->addIP(ip, code, pos_code);
                }
            }
            else
            {
                if(NULL == IpMap[ip_high])
                {
                    IpMap[ip_high] = new IphashMap();
                }
                IpMap[ip_high]->addIP(ip, code, pos_code);
            }
        }
        //static user_position_pos * m_pInstance ;
        void init(std::string path )
        {
            Json::Value root;
            Json::Reader reader;
            //从文件中读取，保证当前文件有test.json文件
            ifstream in(path.c_str(), ios::binary);
            if( !in.is_open() )
            {
                std::cout << "Error opening file\n";
                return;
            }
            reader.parse(in, root);
            int sz = root.size();
            for ( int i = 0 ; i < sz && poscode_num < 256; i++) {
                std::string ip = root[i]["ip"].asString();
                int mesh = 32;
                if(root[i]["mesh"].isNull() == false)
                {
                    mesh = root[i]["mesh"].asInt();
                    if(mesh > 32)
                    {
                        mesh = 32;
                    }
                }
                // 计算IP 圆满
                std::string country  = root[i]["country"].asString();
                std::string province  = root[i]["province"].asString();
                std::string city  = root[i]["city"].asString();
                position_info m_position_info  ; 
                m_position_info.country = country ;
                m_position_info.province = province ;
                m_position_info.city = city ;
                uint32_t i_ip =   ntohl(inet_addr( ip.c_str() )) ;
                //ip_position_map[i_ip] = m_position_info;
                //std::map<>
                uint8_t pcode  = 0;
                std::map<position_info, uint8_t >::iterator iter =  position_info_code_map.find(m_position_info);
                if (iter == position_info_code_map.end()) {
                    position_info_code_map[m_position_info] = (uint8_t)poscode_num ;
                    code_position_info_map[poscode_num] = m_position_info ;
                    pcode = (uint8_t)poscode_num ;
                    poscode_num ++ ;
                } else {
                    pcode  = position_info_code_map[m_position_info];
                }
                add_ip_code(i_ip , mesh , pcode);
            }
        }
    public:
        bool  handle(std::string ip ,  position_info  & m ) 
        {
            if(1 == poscode_num)
            {
                return false;
            }
            uint32_t i_ip = (uint32_t) ntohl( inet_addr( ip.c_str() ) );
            uint8_t pcode = 0 ;
            uint16_t ip_high = (uint16_t)(i_ip >> 16);
            if(IpMap[ip_high])
            {
                pcode = IpMap[ip_high]->findIP(i_ip);
                if(pcode)
                {
                    m = code_position_info_map[pcode];
                    return true;
                }
            }
            return false;
        }

};

class data_ip_position 
{
    public:
        data_ip_position(int thread_num = 1);
        ~data_ip_position();
        position_info work(std::string ip, int thread_id = 0);
        void time_out() ;

    private:
        void init(std::string config);
        void config_parse();
        std::string config;
//        condition_match  match; // 条件匹配类
        //初始化结构值     

        // Redis *p_redis ;
        ip2address *p_ip_position;
        std::string m_base_data_file;
        pthread_mutex_t mutex ;
        long update_time;
        int refresh_timeout;
        user_position_pos * p_user_position_pos ;
        bool b_init;
        int thread_num;
};

#endif  /*IP_POlongitudeSITION_H*/

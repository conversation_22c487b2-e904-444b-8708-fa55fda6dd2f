// Last Update:2019-10-05 22:20:17
/**
 * @file single_session.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-1-4
 */

#include "single_session.h"
#include "read_conf_commit.h"
#include "aes_hash.h"
extern "C"
{
    int get_fullflow_id()
    {
        return 1120;
    }

    packet_full_flow* attach_fullflow(int thread_num, int thread_id)
    {
        return new single_session(thread_num, thread_id);
    }
};
void single_session::init()
{
    // 读取配置文件
    max_thread_session = read_conf_commit::GetInstance()->read_conf_session_num("session","thread_session_ext_num");
}
single_session::single_session(int thread_num, int thread_id):packet_full_flow(thread_num, thread_id)
{
    init();
    //printf("max_thread_session == %d \n",max_thread_session);
#ifdef  OFFLINE
    p_ext_json = new session_ext_json(1);
#endif 
    p_ss_label = NULL;
    p_ss_label = new single_session_label();
    p_single_session_session_marge  = new single_session_session_marge(max_thread_session);
    aes_hash_ctx = AES_InitKey();
    if(NULL == aes_hash_ctx)
    {
        exit(-1);
    }
    if(0 == thread_id)
    {
        p_portjudge = new official_portmap();
        p_portjudge->init();
        p_ip_pos = new data_ip_position(thread_num);
    }
}

void single_session::full_flow_handle(c_packet * p_packet ,session_pub * p_session)
{
    if (NULL == p_session)
    {
        return;
    }
    p_ss_label->print_label(p_session, p_packet);
    single_session_session * p_single_session_session = NULL;
    //同源数组
    if(p_session->p_session_ext)
    {
        p_single_session_session =  &(p_single_session_session_marge -> p_single_session_session [p_session -> p_session_ext->ext_array_id]);
        p_session -> p_session_ext->p_pff_session = (void *)p_single_session_session ;
    }
#ifdef  OFFLINE
    p_ext_json -> handle(p_session , p_packet , p_session -> thread_id);
#endif 
    if(p_single_session_session)
    {
        p_single_session_session -> add_packet_info(p_session,p_packet );
    }
}
bool single_session::time_out(session_pub *p_session)
{
    return true;
}
// 
void single_session::resources_recovery(session_pub * p_session)
{
    p_ss_label->print_session_label(p_session);
    single_session_session * p_single_session_session = NULL;
    if(p_session->p_session_ext)
    {
        p_single_session_session = ( single_session_session * ) p_session -> p_session_ext-> p_pff_session ;
    }
    
#ifdef  OFFLINE
    p_session -> m_session_ext_str = p_ext_json -> send(p_session , p_session -> thread_id);
#endif 
    if(p_single_session_session != NULL)
    {
    //    if(p_session -> do_session_pb == 1)
        {
         p_single_session_session->send(p_session, p_th_tools, aes_hash_ctx, should_log);
        }
    }
    else
    {
        single_session_session::send_session(p_session, p_th_tools, should_log);
    }
    
}

void single_session::session_recovery(session_pub * p_session)
{
#ifdef  OFFLINE
    p_ext_json -> session_end(p_session , p_session -> thread_id);
#endif
    if(p_session->p_session_ext)
    {
        single_session_session * p_single_session_session = (single_session_session *)  p_session -> p_session_ext->p_pff_session ;
        p_single_session_session->init();
    }
}



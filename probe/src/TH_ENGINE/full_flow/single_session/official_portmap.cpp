#include <string>
#include <fstream>
#include <iostream>
#include <sstream>
#include "official_portmap.h"
#include "json/json.h"
using namespace std;


int official_portmap::init()
{
    string str;
    ifstream fin;
    string conf_path = string(getenv("THE_DB_PATH")) + "/port2appid.json";
    fin.open(conf_path, ios::in);
    stringstream buf;
    buf << fin.rdbuf(); 
    str = buf.str();
    cout << str << endl;
    fin.close();
    
    Json::Reader reader;
    Json::Value rule_json;
    
    if (false != reader.parse(str, rule_json))
    {
        if(rule_json.isArray())
        {
            for(int i =0; i < rule_json.size(); i ++)
            {
                if(rule_json[i]["ipproto"].asString() == string("tcp"))
                {
                    uint16_t port = (uint16_t)atoi(rule_json[i]["port"].asString().c_str());
                    int appidx = atoi(rule_json[i]["appid"].asString().c_str()) - APP_ID_START;
                    if(appidx >= 0 && appidx < MAX_APP_NUM)
                    {
                        port_tcp[port] = 1;
                        appid_tcp[appidx] = 1;
                        th_bitmap_set(&app2port_tcp, (unsigned long long)(appidx * 65536 + port));
                    }
                }
                else if(rule_json[i]["ipproto"].asString() == string("udp"))
                {
                    uint16_t port = (uint16_t)atoi(rule_json[i]["port"].asString().c_str());
                    int appidx = atoi(rule_json[i]["appid"].asString().c_str()) - APP_ID_START;
                    if(appidx >= 0 && appidx < MAX_APP_NUM)
                    {
                        port_udp[port] = 1;
                        appid_udp[appidx] = 1;
                        th_bitmap_set(&app2port_udp, (unsigned long long)(appidx * 65536 + port));
                    }
                }
            }
        }
    }
    appid_unknown[0] = 1;
    for(int i = 701; i <= 708; i ++)
    {
        appid_unknown[i] = 1;
    }

}

int official_portmap::judge_unofficial(uint8_t ippro, int appid, uint16_t port)
{
    int appidx = appid - APP_ID_START;
    if(appidx >= 0 && appidx < MAX_APP_NUM)
    {
        if(appid_unknown[appidx])
        {
            return 0;
        }
        if(6 == ippro)
        {
            if(port_tcp[port])
            {
                if(0 == th_bitmap_get(&app2port_tcp,  (unsigned long long)(appidx * 65536 + port)))
                {
                    return 1;
                }
            }
            else if(appid_tcp[appidx])
            {
                return 2;
            }
        }
        else if(17 == ippro)
        {
            if(port_udp[port])
            {
                if(0 == th_bitmap_get(&app2port_udp,  (unsigned long long)(appidx * 65536 + port)))
                {
                    return 1;
                }
            }
            else if(appid_udp[appidx])
            {
                return 2;
            }
        }
    }
    return 0;
}
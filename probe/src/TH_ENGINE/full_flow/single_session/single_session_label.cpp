#include <stdlib.h>
#include "single_session_label.h"
#include "th_labels_define.h"
#include "MD_TCP.h"
#include "judge_cipher.h"
#include "th_engine.h"



void single_session_label::print_label(session_pub * p_session , c_packet * p_packet)
{
    if(p_packet->b_ip_slice)
    {
        th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_IP_SEGMENT);
    }
    if(6 == p_packet->u_tcp && p_packet->p_tcphdr)
    {
        if(p_packet->p_tcphdr->res1 || p_packet->p_tcphdr->res2)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_USE_TCP_RESFLAG);
        }
        uint8_t tcp_flags = (*((uint8_t *)(p_packet->p_tcphdr) + 13) & 0x3f);
        if(0x3f == tcp_flags
                || 0 == tcp_flags
                || 0x01 == tcp_flags
                || 0x20 == tcp_flags
                || 0x08 == tcp_flags
                || 0x03 == (tcp_flags & 0x03)
                || 0x06 == (tcp_flags & 0x06)
                || 0x05 == (tcp_flags & 0x05)
                || 0x29 == (tcp_flags & 0x29))
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_TCP_FLAG_UNUSUAL);
        }
        if((tcp_flags & 0x07) && p_packet->b_ip_slice)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_TCP_FLAG_UNUSUAL);
        }
        if((0x02 == tcp_flags || 0x12 == tcp_flags) && p_packet->payload_len)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_TCP_FLAG_UNUSUAL);
        }
        if(p_packet->p_tcphdr->syn)
        {
            if(5 == p_packet->p_tcphdr->doff)
            {
                th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_TCP_FLAG_UNUSUAL);
            }
            if(p_packet->payload_len)
            {
                th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_TCP_SYN_PAYLOAD);
            }
            if(5 == p_packet->p_tcphdr->doff)
            {
                if(p_packet->p_tcphdr->ack)
                {
                    th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_TCP_NO_OPT_S);
                }
                else
                {
                    th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_TCP_NO_OPT_C);
                }
            }
            if(p_packet->p_app_data && p_packet->app_data_len)
            {
                if(Func_IsCipher(p_packet->p_app_data, p_packet->app_data_len) > 0)
                {
                    th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_SYN_ENC_PAYLOAD);
                }
            }
        }
        if(p_packet->p_tcphdr->fin)
        {
            if((!p_packet->p_tcphdr->ack) || p_packet->p_tcphdr->rst)
            {
                th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_TCP_FLAG_UNUSUAL);
            }
        }
    }
    else if(1 == p_packet->u_tcp && p_packet -> app_buf)
    {
        if(p_packet->b_ip_slice)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_ICMP_FRAGEMENT);
        }
        switch(p_packet -> app_buf[0])
        {
        case 3:
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_ICMP_DST_UNREACH);
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_ICMP_ERROR);
            if(p_packet -> app_buf[1] == 4){
                th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_ICMP_MTU_LIMIT);
            }
            break;
        case 4:
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_ICMP_SRC_QUENCH);
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_ICMP_ERROR);
            break;
        case 5:
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_ICMP_REDIRECT);
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_ICMP_ERROR);
            break;
        case 11:
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_ICMP_TIMEOUT);
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_ICMP_ERROR);
            break;
        case 12:
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_ICMP_PKT_ERROR);
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_ICMP_ERROR);
            break;
        case 8:
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_ICMP_NET_QUERY);
            p_session->icmp_types |= (1 << p_packet -> app_buf[0]);
            p_session->app_req_num ++;
            break;
        case 0:
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_ICMP_NET_QUERY);
            p_session->icmp_types |= (1 << p_packet -> app_buf[0]);
            p_session->app_resp_num ++;
            break;
        case 10:
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_ICMP_ROUTE_QUERY);
            p_session->icmp_types |= (1 << p_packet -> app_buf[0]);
            p_session->app_req_num ++;
            break;
        case 9:
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_ICMP_ROUTE_QUERY);
            p_session->icmp_types |= (1 << p_packet -> app_buf[0]);
            p_session->app_resp_num ++;
            break;
        case 13:
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_ICMP_TS_QUERY);
            p_session->icmp_types |= (1 << p_packet -> app_buf[0]);
            p_session->app_req_num ++;
        case 14:
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_ICMP_TS_QUERY);
            p_session->icmp_types |= (1 << p_packet -> app_buf[0]);
            p_session->app_resp_num ++;
            break;
        case 15:
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_ICMP_MSG_QUERY);
            p_session->icmp_types |= (1 << p_packet -> app_buf[0]);
            p_session->app_req_num ++;
        case 16:
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_ICMP_MSG_QUERY);
            p_session->icmp_types |= (1 << p_packet -> app_buf[0]);
            p_session->app_resp_num ++;
            break;
        case 17:
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_ICMP_MASK_QUERY);
            p_session->icmp_types |= (1 << p_packet -> app_buf[0]);
            p_session->app_req_num ++;
            break;
        case 18:
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_ICMP_MASK_QUERY);
            p_session->icmp_types |= (1 << p_packet -> app_buf[0]);
            p_session->app_resp_num ++;
            break;
        default:
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_ICMP_UNKNON_TYPE);
            break;
        }
    }
    if(p_packet->p_iphdr && p_packet->p_iphdr->ihl > 5)
    {
        th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_IP_WITH_OPT);
    }
    if(p_packet->flags.b_app_changed)
    {
        th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_APP_CHANGE);
    }
}

void judge_label_file_trans(session_pub *p_session, CMD_TCP *p_md_tcp)
{
    int file_count = 0;
    int b_file = 0, b_large_file = 0;
    unsigned int seq_last, ack_last;
    long long int file_bytes = 0;
    int b_first = 1;
    for(int i = 0; i < p_md_tcp->m_InforNum; i ++)
    {
        if(0 == p_md_tcp->m_pInfor[i].PacketNum || p_md_tcp->m_pInfor[i].Bytes < 0 || (llabs(p_md_tcp->m_pInfor[i].Bytes) / p_md_tcp->m_pInfor[i].PacketNum > 65536))
        {
            continue;
        }
        if(b_first)
        {
            b_first = 0;
            file_bytes = llabs(p_md_tcp->m_pInfor[i].Bytes);
        }
        else if(seq_last + 1000 > p_md_tcp->m_pInfor[i].temp.MinSequence
                && p_md_tcp->m_pInfor[i].temp.MinSequence + 1000 > seq_last
                && ack_last + 1000 > p_md_tcp->m_pInfor[i].temp.Acknowledgement
                && p_md_tcp->m_pInfor[i].temp.Acknowledgement + 1000 > ack_last)
        {
            file_bytes += llabs(p_md_tcp->m_pInfor[i].Bytes);
        }
        else
        {
            if(file_bytes > 1048576)
            {
                if(file_bytes > 104857600)
                {
                    th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_LARGE_FILE);
                    b_large_file = 1;
                }
                else
                {
                    th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_FILE_TRANSPORT);
                    b_file = 1;
                }
                file_count ++;
                if(file_count > 1)
                {
                    th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_MULTI_FILE);
                    if(b_file && b_large_file)
                    {
                        return;
                    }
                }
            }
            file_bytes = llabs(p_md_tcp->m_pInfor[i].Bytes);
        }
        seq_last = p_md_tcp->m_pInfor[i].temp.MaxSequence;
        ack_last = p_md_tcp->m_pInfor[i].temp.Acknowledgement;
    }
    if(file_bytes > 1048576)
    {
        if(file_bytes > 104857600)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_LARGE_FILE);
            b_large_file = 1;
        }
        else
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_FILE_TRANSPORT);
            b_file = 1;
        }
        file_count ++;
        if(file_count > 1)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_MULTI_FILE);
            if(b_file && b_large_file)
            {
                return;
            }
        }
    }
    file_bytes = 0;
    b_first = 1;
    for(int i = 0; i < p_md_tcp->m_InforNum; i ++)
    {
        if(0 == p_md_tcp->m_pInfor[i].PacketNum || p_md_tcp->m_pInfor[i].Bytes > 0 || (llabs(p_md_tcp->m_pInfor[i].Bytes) / p_md_tcp->m_pInfor[i].PacketNum > 65536))
        {
            continue;
        }
        if(b_first)
        {
            b_first = 0;
            file_bytes = llabs(p_md_tcp->m_pInfor[i].Bytes);
        }
        else if(seq_last + 1000 > p_md_tcp->m_pInfor[i].temp.MinSequence
                && p_md_tcp->m_pInfor[i].temp.MinSequence + 1000 > seq_last
                && ack_last + 1000 > p_md_tcp->m_pInfor[i].temp.Acknowledgement
                && p_md_tcp->m_pInfor[i].temp.Acknowledgement + 1000 > ack_last)
        {
            file_bytes += llabs(p_md_tcp->m_pInfor[i].Bytes);
        }
        else
        {
            if(file_bytes > 1048576)
            {
                if(file_bytes > 104857600)
                {
                    th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_LARGE_FILE);
                    b_large_file = 1;
                }
                else
                {
                    th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_FILE_TRANSPORT);
                    b_file = 1;
                }
                file_count ++;
                if(file_count > 1)
                {
                    th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_MULTI_FILE);
                    if(b_file && b_large_file)
                    {
                        return;
                    }
                }
            }
            file_bytes = llabs(p_md_tcp->m_pInfor[i].Bytes);
        }
        seq_last = p_md_tcp->m_pInfor[i].temp.MaxSequence;
        ack_last = p_md_tcp->m_pInfor[i].temp.Acknowledgement;
    }
    if(file_bytes > 1048576)
    {
        if(file_bytes > 104857600)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_LARGE_FILE);
            b_large_file = 1;
        }
        else
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_FILE_TRANSPORT);
            b_file = 1;
        }
        file_count ++;
        if(file_count > 1)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_MULTI_FILE);
            if(b_file && b_large_file)
            {
                return;
            }
        }
    }
    return;
}

static uint32_t g_icmp_query_type[5] = {
        (1 | (1 << 8)),
        ((1 << 9) | (1 << 10)),
        ((1 << 13) | (1 << 14)),
        ((1 << 15) | (1 << 16)),
        ((1 << 17) | (1 << 18))};

void single_session_label::print_session_label(session_pub *p_session)
{
    int black = 0, white = 0, important = 0;
    std::string ip = p_session->session_basic.pIP[0].ip_str();
    p_session->target_scan_handle_cb(ip, p_session->thread_id, black, white, important);
    // if(black)
    // {
    //     th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_BLACK_SESSION);
    // }
    if(white)
    {
        th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_WHITE_RELATE);
    }
    if(important)
    {
        th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_TARGET_RELATE);
    }
    black = 0;
    white = 0;
    important = 0;
    ip = p_session->session_basic.pIP[1].ip_str();
    p_session->target_scan_handle_cb(ip, p_session->thread_id, black, white, important);
    // if(black)
    // {
    //     th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_BLACK_SESSION);
    // }
    if(white)
    {
        th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_WHITE_RELATE);
    }
    if(important)
    {
        th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_TARGET_RELATE);
    }
    CMD_TCP *p_md_tcp = ((CMD_TCP *)p_session-> p_md_tcp);
    if(p_md_tcp)
    {
        judge_label_file_trans(p_session, p_md_tcp);
    }
    for(int i = 0; i < 5; i ++)
    {
        if((p_session->icmp_types & g_icmp_query_type[i]) == g_icmp_query_type[i])
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_ICMP_QUERY);
            break;
        }
    }
    if(1 == p_session->session_basic.IPPro || PROTOCOL_APP_DNS == p_session->session_basic.ProtoSign || PROTOCOL_APP_HTTP == p_session->session_basic.ProtoSign)
    {
        if(0 == p_session->app_req_num)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_NO_QUERY);
        }
        else if(1 == p_session->app_req_num)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_1_QUERY);
        }
        else if(p_session->app_req_num <= 8)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_2_8_QUERY);
        }
        else if(p_session->app_req_num <= 16)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_9_16_QUERY);
        }
        else
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_17_OO_QUERY);
        }
        if(0 == p_session->app_resp_num)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_NO_RESPONSE);
        }
        else if(1 == p_session->app_resp_num)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_1_RESPONSE);
        }
        else if(p_session->app_resp_num <= 8)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_2_8_RESPONSE);
        }
        else if(p_session->app_resp_num <= 16)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_9_16_RESPONSE);
        }
        else
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_17_OO_RESPONSE);
        }
    }
}
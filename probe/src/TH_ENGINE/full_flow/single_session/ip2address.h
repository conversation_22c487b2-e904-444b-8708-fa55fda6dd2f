// Last Update:2017-10-09 10:16:03
/**
 * @file ip2address.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-09-29
 */

#ifndef IP2ADDRESS_H
#define IP2ADDRESS_H

#include <string>
#include "stdlib.h"
#include "maxminddb.h"
#include "maxminddb_config.h"
using namespace std;

class ip2address
{
    public:
        ip2address(int thread_num = 1);
        ~ip2address();
        bool set_mmdb(string mmdbpath);
        bool set_result(string ip, int thread_id = 0);
        string get_country(int thread_id = 0);
        string get_province(int thread_id = 0);
        string get_city(int thread_id = 0);
        double get_latitude(int thread_id = 0);
        double get_longitude(int thread_id = 0);
        string get_value(string path, int thread_id = 0);
    private:
        void get_entry_value(MMDB_entry_data_s &entry_data, string &val);
        MMDB_s *p_mmdb;
        MMDB_lookup_result_s *p_result;
        int thread_num;
};

#endif  /*IP2ADDRESS_H*/

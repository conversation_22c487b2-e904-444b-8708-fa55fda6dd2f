// Last Update:2021-09-03 09:52:57
/**
 * @file ip_position.cpp
 * @brief ip 定位功能
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-10-09
 */

#include "ip_position.h"

#define DOUBLE_IS_ZERO(d) ((-0.00001 < (d)) && ((d) < 0.0001))


data_ip_position::data_ip_position(int thread_num)
{
    string conf_path = string(getenv("THE_CONF_PATH"))+"/user_ip_position.json";
    this->thread_num = thread_num;
    p_ip_position = new ip2address(this->thread_num);
    update_time = time(NULL);
    refresh_timeout = 12; //每隔12小时检查更新一次maxmind数据文件.
    config_parse();
    p_ip_position->set_mmdb(m_base_data_file);
    init(conf_path);
}

data_ip_position::~data_ip_position()
{
    if(p_ip_position)
    {
        delete p_ip_position;
    }
    if  (p_user_position_pos) 
    {
        delete  p_user_position_pos;
    }
}


void data_ip_position::time_out()
{
    long cur_time = time(NULL);
    if( ((cur_time - update_time) > 3600*refresh_timeout))
    {
        if(p_ip_position!=NULL)
        {
            delete p_ip_position;
            p_ip_position = NULL;
        }
        p_ip_position = new ip2address(this->thread_num);
        if(p_ip_position && p_ip_position->set_mmdb(m_base_data_file))
        {
            //succeed
        }
        //printf("refresh ip2address.\n");
        update_time = cur_time;
    }
}

void data_ip_position::init(std::string conf_file) 
{
    config = conf_file ;
    config_parse();
    p_user_position_pos = new user_position_pos();
}
void  data_ip_position::config_parse()
{
    // char buf[256];
    // sprintf(buf ,  "/dev/shm/GeoLite2-City.mmdb%c",0x0);
    // sprintf(buf ,  "/dev/shm/GeoLite2-City.mmdb_%d%c",config_text::pre_num,0x0);
    // m_base_data_file = buf ;
    // sprintf(buf ,  "\\cp -rf ../conf/GeoLite2-City.mmdb /dev/shm/GeoLite2-City.mmdb_%d\n",config_text::pre_num,0x0);
    // //sprintf(buf ,  "\\cp -rf ../conf/GeoLite2-City.mmdb /dev/shm/GeoLite2-City.mmdb\n",0x0);

    // system(buf);
    m_base_data_file = string(getenv("THE_DB_PATH"))+"/GeoLite2-City.mmdb";
}
position_info data_ip_position::work(std::string  ip, int thread_id) 
{
    //
    position_info m ;
    m.country = "";
    m.province = "";
    m.city = "";
    if (p_user_position_pos -> handle(ip , m) == false )
    {
        if(p_ip_position->set_result(ip, thread_id))
        {
            m.country = p_ip_position->get_country(thread_id);
            m.province = p_ip_position->get_province(thread_id);
            m.city = p_ip_position->get_city(thread_id);
            m.latitude = p_ip_position->get_latitude(thread_id);
            m.longitude = p_ip_position->get_longitude(thread_id);
        }
    }
    return m;

}



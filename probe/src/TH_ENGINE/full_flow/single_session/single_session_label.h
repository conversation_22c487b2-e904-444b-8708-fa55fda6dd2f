#ifndef __SINGLE_SESSION_LABEL_H__
#define __SINGLE_SESSION_LABEL_H__

#include "session_pub.h"
#include "packet.h"

class single_session_label
{
    public :
        single_session_label()
        {
            
        }
        ~single_session_label()
        {
            
        }
        void print_label(session_pub * p_session , c_packet * p_packet);
        void print_session_label(session_pub * p_session);
        uint8_t b_disabled;
};


#endif
// Last Update:2019-07-01 17:17:50
/**
 * @file single_session.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-1-4
 */

#ifndef SINGLE_SESSION_H
#define SINGLE_SESSION_H
#include <TH_engine_interface.h>
#include "single_session_session.h"
#include "single_session_label.h"
#ifdef OFFLINE
#include  <session_ext_json.h>
#endif
#include "aes_hash.h"
extern "C"
{
    int get_fullflow_id();

    packet_full_flow* attach_fullflow(int thread_num, int thread_id);
};

class single_session : public packet_full_flow
{
    public :
        single_session(int thread_num, int thread_id);
        virtual ~single_session() 
        {
		if(p_single_session_session_marge == NULL)
		{
			delete p_single_session_session_marge ;
		}
        AES_CleanUp(aes_hash_ctx);
        }
        void init();
        virtual void full_flow_handle(c_packet * p_packet ,session_pub * p_session) ;
        virtual bool time_out(session_pub * p_session); //  数据超时接口 ， 可以用作定时调用接口
        virtual void resources_recovery(session_pub * p_session) ; // 资源回收接口
        virtual void session_recovery(session_pub * p_session) ; // 资源回收接口
    private:
        single_session_session_marge * p_single_session_session_marge ;
        int max_thread_session ;
#ifdef  OFFLINE
        session_ext_json * p_ext_json ;
#endif
        void *aes_hash_ctx;
        single_session_label *p_ss_label;

};
#endif  /*SINGLE_SESSION_H*/

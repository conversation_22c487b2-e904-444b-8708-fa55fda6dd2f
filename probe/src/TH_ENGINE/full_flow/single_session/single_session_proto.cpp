#include "single_session_proto.h"
#include "rapidjson/stringbuffer.h"
#include "rapidjson/writer.h"
#include "rapidjson/document.h"

string session_pub_http_2json(session_pub_http *phttp)
{
    rapidjson::StringBuffer strBuf;
    rapidjson::Writer<rapidjson::StringBuffer> writer(strBuf);
    writer.StartObject();
    writer.Key("Url");
    writer.String(phttp->url.c_str(),phttp->url.length());
    writer.Key("Act");
    writer.String(phttp->act.c_str(), phttp->act.length());
    writer.Key("Host");
    writer.String(phttp->host.c_str(), phttp->host.length());
    writer.Key("Response");
    writer.String(phttp->response.c_str(), phttp->response.length());
    writer.Key("User-Agent");
    writer.String(phttp->user_agent.c_str(), phttp->user_agent.length());
    writer.EndObject();
    return string(strBuf.GetString(), strBuf.GetSize());
}

string session_pub_http_serialize(vector<session_pub_http> &http)
{
    string ret = "";
    int size = http.size() > JSON_MAX_PROTO_NUM ? JSON_MAX_PROTO_NUM : http.size();
    for(int i = 0; i < (size - 1); i ++)
    {
        ret += ( session_pub_http_2json(&http[i]) + ",");
    }
    if(size > 0)
    {
        ret += session_pub_http_2json(&http[size - 1]);
    }
    return ret;
}

string session_pub_dns_2json(const session_pub_dns &dns)
{
    rapidjson::StringBuffer strBuf;
    rapidjson::Writer<rapidjson::StringBuffer> writer(strBuf);
    writer.StartObject();
    writer.Key("Domain");
    writer.String(dns.domain.c_str(), dns.domain.length());
    writer.Key("DomainIp");
    writer.String(dns.domain_ip.c_str(), dns.domain_ip.length());
    writer.Key("Answer");
        writer.StartArray();
        for(int i = 0; i < dns.answer.size(); i ++)
        {
            writer.StartObject();
            writer.Key("name");
            writer.String(dns.answer[i].name.c_str(), dns.answer[i].name.length());
            writer.Key("value");
            writer.String(dns.answer[i].value.c_str(), dns.answer[i].value.length());
            writer.EndObject();
        }
        writer.EndArray();
    writer.EndObject();
    return string(strBuf.GetString(), strBuf.GetSize());
}

string session_pub_dns_serialize(CSetVectorUnion<session_pub_dns> &dns)
{
    string ret = "";
    int size = dns.size() > JSON_MAX_PROTO_NUM ? JSON_MAX_PROTO_NUM : dns.size();
    for(int i = 0; i < (size - 1); i ++)
    {
        ret += ( session_pub_dns_2json(*dns.m_vector[i]) + ",");
    }
    if(size > 0)
    {
        ret += session_pub_dns_2json(*dns.m_vector[size - 1]);
    }
    return ret;
}

string session_pub_ssl_2json(session_pub_ssl *pssl)
{
    rapidjson::Document doc;
    rapidjson::StringBuffer strBuf;
    rapidjson::Writer<rapidjson::StringBuffer> writer(strBuf);
    writer.StartObject();
    writer.Key("CH_Ciphersuit");
    writer.String(pssl->ch_ciphersuit.c_str(), pssl->ch_ciphersuit.length());
    writer.Key("CH_CiphersuitNum");
    writer.Uint(pssl->ch_ciphersuit_num);
    writer.Key("CH_ServerName");
    writer.String(pssl->ch_server_name.c_str(), pssl->ch_server_name.length());
    writer.Key("CH_ALPN");
        writer.StartArray();
        doc.Parse(pssl->ch_alpn.c_str());
        if (!doc.HasParseError() && doc.IsArray())
        {
            for (rapidjson::SizeType i = 0; i < doc.Size(); i++)
            {
                writer.String(doc[i].GetString());
            }
        }
        writer.EndArray();
    writer.Key("cCertHash");
        writer.StartArray();
        doc.Parse(pssl->c_cert.c_str());
        if (!doc.HasParseError() && doc.IsArray())
        {
            for (rapidjson::SizeType i = 0; i < doc.Size(); i++)
            {
                writer.String(doc[i].GetString());
            }
        }
        writer.EndArray();
    writer.Key("cCertNum");
    writer.Uint(pssl->c_cert_num);
    writer.Key("sCertHash");
        writer.StartArray();
        doc.Parse(pssl->s_cert.c_str());
        if (!doc.HasParseError() && doc.IsArray())
        {
            for (rapidjson::SizeType i = 0; i < doc.Size(); i++)
            {
                writer.String(doc[i].GetString());
            }
        }
        writer.EndArray();
    writer.Key("sCertNum");
    writer.Uint(pssl->s_cert_num);
    writer.EndObject();
    return string(strBuf.GetString(), strBuf.GetSize());
}

string session_pub_ssl_serialize(vector<session_pub_ssl> &ssl)
{
    string ret = "";
    int size = ssl.size() > JSON_MAX_PROTO_NUM ? JSON_MAX_PROTO_NUM : ssl.size();
    for(int i = 0; i < (size - 1); i ++)
    {
        ret += ( session_pub_ssl_2json(&ssl[i]) + ",");
    }
    if(size > 0)
    {
        ret += session_pub_ssl_2json(&ssl[size - 1]);
    }
    return ret;
}
// Last Update:2020-03-12 20:15:50
/**
 * @file ip2address.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-09-29
 */

#include <errno.h>
#include <string.h>
#include <stdio.h>
#include "ip2address.h"
#include <vector>
#include <sstream>
using namespace std;

ip2address::ip2address(int thread_num)
{
    this->thread_num = thread_num;
    p_result = NULL;
    p_mmdb = NULL;
}

ip2address::~ip2address()
{
    if(p_result)
    {
        delete[] p_result;
        p_result = NULL;
    }
    if(p_mmdb)
    {
        MMDB_close(p_mmdb);
        delete p_mmdb;
        p_mmdb = NULL;
    }
}

bool ip2address::set_mmdb(string mmdbpath)
{
    p_mmdb = new MMDB_s();
    if(NULL == p_mmdb)
    {
        return 0;
    }
    p_result = new MMDB_lookup_result_s[thread_num];
    if(NULL == p_result)
    {
        delete p_mmdb;
        p_mmdb = NULL;
        return 0;
    }
    int ret = MMDB_open(mmdbpath.c_str(), MMDB_MODE_MMAP, p_mmdb);
    if (ret != MMDB_SUCCESS)
    {
        printf("IP2ADDRESS Can't open %s - %s\n", mmdbpath.c_str(), MMDB_strerror(ret));
        if (ret == MMDB_IO_ERROR)
        {
            printf("IP2ADDRESS IO error: %s\n", strerror(errno));
        }
        delete p_mmdb;
        p_mmdb = NULL;
        delete[] p_result;
        p_result = NULL;
        return 0;
    }
    return 1;
}

bool ip2address::set_result(string ip, int thread_id)
{
    p_result[thread_id].found_entry = false;
    if (NULL == p_mmdb)
    {
        return 0;
    }
    int gai_error = 0;
    int mmdb_error = 0;
    p_result[thread_id] = MMDB_lookup_string(p_mmdb, ip.c_str(), &gai_error, &mmdb_error);
    if (gai_error != 0)
    {
        return 0;
    }
    if (mmdb_error != MMDB_SUCCESS)
    {
        return 0;
    }
    return 1;
}

string ip2address::get_country(int thread_id)
{
    string country = "";
    if (p_result[thread_id].found_entry)
    {
        MMDB_entry_data_s entry_data;
        int status = MMDB_get_value(&p_result[thread_id].entry, &entry_data, "country", "names", "zh-CN", NULL);
        // if (status != MMDB_SUCCESS)
        // {
        //     MMDB_get_value(&p_result[thread_id].entry, &entry_data, "country", "names", "en", NULL);
        // }
        if (entry_data.has_data)
        {
            country.append(entry_data.utf8_string, entry_data.data_size);
        }
    }
    return country;
}

string ip2address::get_city(int thread_id)
{
    string city = "";
    if (p_result[thread_id].found_entry)
    {
        MMDB_entry_data_s entry_data;
        int status = MMDB_get_value(&p_result[thread_id].entry, &entry_data, "city", "names", "zh-CN", NULL);
        // if (status != MMDB_SUCCESS)
        // {
        //     MMDB_get_value(&p_result[thread_id].entry, &entry_data, "city", "names", "en", NULL);
        // }
        if (entry_data.has_data)
        {
            city.append(entry_data.utf8_string, entry_data.data_size);
        }
    }
    return city;
}

string ip2address::get_province(int thread_id)
{
    string province = "";
    if (p_result[thread_id].found_entry)
    {
        MMDB_entry_data_s entry_data;
        int status = MMDB_get_value(&p_result[thread_id].entry, &entry_data, "subdivisions", "0", "names", "zh-CN", NULL);
        // if (status != MMDB_SUCCESS)
        // {
        //     MMDB_get_value(&p_result[thread_id].entry, &entry_data, "subdivisions", "0", "names", "en", NULL);
        // }
        if (entry_data.has_data)
        {
            province.append(entry_data.utf8_string, entry_data.data_size);
        }
    }
    return province;
}

double ip2address::get_latitude(int thread_id)
{
    double latitude = 0;
    if (p_result[thread_id].found_entry)
    {
        MMDB_entry_data_s entry_data;
        MMDB_get_value(&p_result[thread_id].entry, &entry_data, "location", "latitude", NULL);
        if (entry_data.has_data)
        {
            latitude = entry_data.double_value;
        }
    }
    return latitude;
}

double ip2address::get_longitude(int thread_id)
{
    double longitude = 0;
    if (p_result[thread_id].found_entry)
    {
        MMDB_entry_data_s entry_data;
        MMDB_get_value(&p_result[thread_id].entry, &entry_data, "location", "longitude", NULL);
        if (entry_data.has_data)
        {
            longitude = entry_data.double_value;
        }
    }
    return longitude;
}
void SplitString(const string& s, vector<string>& v, const string& c)
{
    string::size_type pos1, pos2;
    pos2 = s.find(c);
    pos1 = 0;
    while(string::npos != pos2)
    {
        v.push_back(s.substr(pos1, pos2-pos1));

        pos1 = pos2 + c.size();
        pos2 = s.find(c, pos1);
    }
    if(pos1 != s.length())
        v.push_back(s.substr(pos1));
}

char *bytes_to_hex(uint8_t *bytes, uint32_t size)
{
    char *hex_string;
    if(size > 1024*1024*100)
    {
        //size is too long.
        return NULL;
    }
    hex_string = (char*)malloc((size * 2) + 1);
    if (NULL == hex_string) {
        return NULL;
    }

    for (uint32_t i = 0; i < size; i++) {
        sprintf(hex_string + (2 * i), "%02X", bytes[i]);
    }
    return hex_string;
}
void ip2address::get_entry_value(MMDB_entry_data_s &entry_data, string &val)
{
    // stringstream ss;
    // ss.clear();
    switch (entry_data.type) {
        case MMDB_DATA_TYPE_MAP:
            {
                //暂不增加
            }
            break;
        case MMDB_DATA_TYPE_ARRAY:
            {
                //暂不增加
            }
            break;
        case MMDB_DATA_TYPE_UTF8_STRING:
            {
                char *str =
                    strndup((char *)entry_data.utf8_string, entry_data.data_size);

                if (NULL != str) 
                {
                    val = str;
                    free(str);
                }
            }
            break;
        case MMDB_DATA_TYPE_BYTES:
            {
                char *hex_string =
                    bytes_to_hex((uint8_t *)entry_data.bytes, entry_data.data_size);
                if (NULL != hex_string) 
                {
                    val = hex_string;
                    free(hex_string);
                }
            }
            break;
        case MMDB_DATA_TYPE_DOUBLE:
            {
                char buf[50] = {0};
                sprintf(buf, "%.6f", entry_data.double_value);
                val = buf;
            }
            break;
        case MMDB_DATA_TYPE_FLOAT:
            {
                char buf[50] = {0};
                sprintf(buf, "%.6f", entry_data.float_value);
                val = buf;
            }
            break;
        case MMDB_DATA_TYPE_UINT16:
            {
                char buf[50] = {0};
                sprintf(buf, "%u", entry_data.uint16);
                val = buf;
            }
            break;
        case MMDB_DATA_TYPE_UINT32:
            {
                char buf[50] = {0};
                sprintf(buf, "%u", entry_data.uint32);
                val = buf;
            }
            break;
        case MMDB_DATA_TYPE_BOOLEAN:
            val = entry_data.boolean ? "true" : "false";
            break;
        case MMDB_DATA_TYPE_UINT64:
            {
                char buf[50] = {0};
                sprintf(buf, "%ld", entry_data.uint64);
                val = buf;
            }
            break;
        case MMDB_DATA_TYPE_UINT128:
            /* //暂不支持
            print_indentation(stream, indent);
#if MMDB_UINT128_IS_BYTE_ARRAY
            char *hex_string =
                bytes_to_hex((uint8_t *)entry_data_list->entry_data.uint128, 16);
            if (NULL == hex_string) {
                *status = MMDB_OUT_OF_MEMORY_ERROR;
                return NULL;
            }
            fprintf(stream, "0x%s <uint128>\n", hex_string);
            free(hex_string);
#else
            uint64_t high = entry_data_list->entry_data.uint128 >> 64;
            uint64_t low = (uint64_t)entry_data_list->entry_data.uint128;
            fprintf(stream, "0x%016" PRIX64 "%016" PRIX64 " <uint128>\n", high,
                    low);
#endif
            entry_data_list = entry_data_list->next;
            */
            break;
        case MMDB_DATA_TYPE_INT32:
            {
                char buf[50] = {0};
                sprintf(buf, "%d", entry_data.uint32);
                val = buf;
            }
            break;
        default:
            val = "";
            break;
    }
}

string ip2address::get_value(string path, int thread_id)
{
    vector<string> v;
    string split = "/";
    string value = "";
    SplitString(path, v, split);
    char **p_path = (char**)malloc((1+v.size()) * sizeof(char*));
    size_t i = 0;
    for(i = 0; i < v.size(); i ++)
    {
        p_path[i] = (char *)v[i].c_str();
    }
    p_path[i] = NULL;

    if (p_result[thread_id].found_entry)
    {
        MMDB_entry_data_s entry_data;
        int status = MMDB_aget_value(&p_result[thread_id].entry, &entry_data, p_path);
        if (MMDB_SUCCESS == status)
        {
            get_entry_value(entry_data, value);
        }
    }
    free(p_path);
    return value;

}

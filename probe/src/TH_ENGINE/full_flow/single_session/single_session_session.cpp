// Last Update:2019-12-24 22:22:38
/**
 * @file full_flow_sesson.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-1-4
 */

#include "single_session_session.h"
#include "crc64.h"
#include "aes_hash.h"
#include "Define_L2Defense.h"
#include "single_session_proto.h"




official_portmap *p_portjudge = NULL;
data_ip_position *p_ip_pos = NULL;
char len_map[1024];
char time_map[66];
char offset_map[128];
uint32_t ttl_label_map_c[256];
uint32_t ttl_label_map_s[256];
void  init_len_map()
{
    for (int i = 0; i < 1024; i++)
    {
        if (i < 16)
        {
            len_map[i] = 0;
        }
        else if (i < 32)
        {
            len_map[i] = 1;
        }
        else if (i < 64)
        {
            len_map[i] = 2;
        }
        else if (i < 128)
        {
            len_map[i] = 3;
        }
        else if (i < 256)
        {
            len_map[i] = 4;
        }
        else if (i < 512)
        {
            len_map[i] = 5;
        }
        else
        {
            len_map[i] = 6;
        }
    }
}
void init_time_map()
{
    int i;
    for(i = 0; i < 66; i ++)
    {
        if(i < 2)
        {
            time_map[i] = 0;
        }
        else if (i < 3)
        {
            time_map[i] = 1;
        }
        else if (i < 5)
        {
            time_map[i] = 2;
        }
        else if (i < 9)
        {
            time_map[i] = 3;
        }
        else if (i < 17)
        {
            time_map[i] = 4;
        }
        else if (i < 33)
        {
            time_map[i] = 5;
        }
        else if(i < 65)
        {
            time_map[i] = 6;
        }
        else
        {
            time_map[i] = 7;
        }
    }
}

void init_offset_map()
{
    int i;
    for(i = -64; i < 64; i ++)
    {
        int idx = i + 64;
        if(i < -4)
        {
            offset_map[idx] = 1;
        }
        else if(i < 0)
        {
            offset_map[idx] = 2;
        }
        else if(i < 4)
        {
            offset_map[idx] = 3;
        }
        else
        {
            offset_map[idx] = 4;
        }
    }
}
void init_ttl_map()
{
    for(uint8_t i = 0;; i ++)
    {
        if(i <= 32)
        {
            ttl_label_map_c[i] = LABEL_C_TTL_32;
            ttl_label_map_s[i] = LABEL_S_TTL_32;
        }
        else if(i <= 64)
        {
            ttl_label_map_c[i] = LABEL_C_TTL_64;
            ttl_label_map_s[i] = LABEL_S_TTL_64;
        }
        else if(i <= 128)
        {
            ttl_label_map_c[i] = LABEL_C_TTL_128;
            ttl_label_map_s[i] = LABEL_S_TTL_128;
        }
        else if(i < 255)
        {
            ttl_label_map_c[i] = LABEL_C_TTL_256;
            ttl_label_map_s[i] = LABEL_S_TTL_256;
        }
        else
        {
            ttl_label_map_c[i] = LABEL_C_TTL_256;
            ttl_label_map_s[i] = LABEL_S_TTL_256;
            break;
        }
    }
}
void macToStr(unsigned char * pMac,char *IN_pBuf,DWORD IN_BufSize)
{
    sprintf_s(IN_pBuf,IN_BufSize,"%02x:%02x:%02x:%02x:%02x:%02x",pMac[5],pMac[4],pMac[3],pMac[2],pMac[1],pMac[0]);
}


void basic_pro::AddJson1(Json::Value & jsonl , int m1 , int m2 ,std::string name)
{
    Json::Value FinArr; 
    FinArr.append( m1);FinArr.append( m2);
    jsonl[name] = FinArr ;
}

std::string basic_pro::base_info_jsonSerializeToString()
{
    Json::Value jsonl;
    Json::FastWriter fast_writer;
    AddJson1(jsonl , FinNum[0] , FinNum[1],"Fin");
    AddJson1(jsonl , MaxPacketLen[0] , MaxPacketLen[1],"MaxPacketLen");
    AddJson1(jsonl , RSTNum[0] ,RSTNum[1],"RSTNum");
    AddJson1(jsonl , SYNNum[0] ,SYNNum[1],"SYNNum");
    AddJson1(jsonl , TTL_Max[0] ,TTL_Max[1],"TTL_Max");
    AddJson1(jsonl , TTL_Min[0] ,TTL_Min[1],"TTL_Min");
    AddJson1(jsonl , TotalSign[0] ,TotalSign[1],"TotalSign");
    string ser_json = fast_writer.write(jsonl);
    ser_json.pop_back();
    return ser_json;

}

void basic_pro::init()
{
    FinNum[0] = 0;
    FinNum[1] = 0;
    MaxPacketLen[0] = 0 ;
    MaxPacketLen[1] = 0;
    RSTNum[0] = 0 ;
    RSTNum[1] = 0 ;
    SYNNum[0] = 0 ;
    SYNNum[1] = 0 ;
    TTL_Max[0 ] = 0;
    TTL_Max[1] = 0;
    TTL_Min[0] = 0 ;
    TTL_Min[1] = 0 ;
    TotalSign[0] = 0 ;
    TotalSign[1] = 0 ;
}

void basic_pro::Handle(c_packet * p_packet)
{
    if(p_packet->p_tcphdr == NULL) 
    {
        return  ;
    }
    if(p_packet->p_tcphdr->syn == 1)
    {
        SYNNum[p_packet->Directory] ++;
    }
    if(p_packet->p_tcphdr->rst == 1)
    {
        RSTNum[p_packet->Directory] ++;
    }
    if(p_packet->p_tcphdr->fin  == 1)
    {
        FinNum[p_packet->Directory] ++;
    }
    if(MaxPacketLen[p_packet->Directory] < p_packet -> packet_len )
    {
        MaxPacketLen[p_packet->Directory] =  p_packet -> packet_len ;
    }
}

void packet_info::handle(c_packet * p_packet,uint32_t num , uint32_t PayloadNum)
{
    count = num ;
    len= p_packet -> app_len ;
    Sec = p_packet->time_ts[0];
    nSec = p_packet->time_ts[1];
    Directory = p_packet->Directory;
}


void BinToHex(uint8_t * IN_pHexBuffer,uint32_t IN_HexBufferLen,unsigned char *OUT_pASCIIBuffer,uint32_t IN_HexBufSize,uint32_t &OUT_ASCIIBufferLen )
{
    DWORD i,temp;
    
    //
    OUT_ASCIIBufferLen=0;
    
    for(i=0;i<IN_HexBufferLen;i++)
    {
        OUT_pASCIIBuffer[OUT_ASCIIBufferLen]=0;
        
        temp=(IN_pHexBuffer[i]>>4)&0xf;
        if( temp<10 )
        {
            if(OUT_ASCIIBufferLen>=IN_HexBufSize) return;
            OUT_pASCIIBuffer[OUT_ASCIIBufferLen++] =0x30+temp;
        }
        else
        {
            if(OUT_ASCIIBufferLen>=IN_HexBufSize) return;
            OUT_pASCIIBuffer[OUT_ASCIIBufferLen++] =0x37+temp;
        }
        temp=IN_pHexBuffer[i]&0xf;
        if( temp<10 )
        {
            if(OUT_ASCIIBufferLen>=IN_HexBufSize) return;
            OUT_pASCIIBuffer[OUT_ASCIIBufferLen++] =0x30+temp;
        }
        else
        {
            if(OUT_ASCIIBufferLen>=IN_HexBufSize) return;
            OUT_pASCIIBuffer[OUT_ASCIIBufferLen++] =0x37+temp;
        }
        
    }
    
    OUT_pASCIIBuffer[OUT_ASCIIBufferLen]=0;
}

string bin2string(const char *p_data, uint32_t len){
    // Check for null pointer
    if (p_data == nullptr) {
        return std::string();
    }
    
    // Check for valid length
    if (len == 0) {
        return std::string();
    }
    
    // Create string safely
    return std::string(p_data, len);
}

void packet_info::Str(ss_pkt_msg *p_pkt_msg, int pkt_from_client)
{
    packet_info_msg * p_pinfo = p_pkt_msg -> add_pkt_infor();
    p_pinfo -> set_count(count) ;
    p_pinfo -> set_sec(Sec);
    p_pinfo -> set_nsec(nSec);
    if(Directory ^ pkt_from_client)
    {
        p_pinfo ->set_len(len);
    }
    else
    {
        p_pinfo ->set_len(-len);
    }
}
string payload_info::Str()
{
    return bin2string((const char *)payload, len);
}


single_session_session::single_session_session()
{
    init();
}

single_session_session::~single_session_session()
{
    ;
}

void single_session_session::init()
{
    //base.init();
    //memset(DIST,0x0,256*sizeof(uint32_t));
    IpPro = 0;
    totalsign[0] = 0;
    totalsign[1] = 0;
    memset(distLen[0],0x0,8*sizeof(uint32_t));
    memset(distLen[1],0x0,8 * sizeof(uint32_t));
    memset(duration, 0, 8 * sizeof(uint32_t));
    ts_last[0]=0;
    ts_last[1]=0;
    max_prolist_num = 0;
    pkt_maxlen[0] = 0;
    pkt_maxlen[1] = 0;
    //from basic parse-------------
    memset(UnilateraliInfor,0,sizeof(STR_UNILATERALINFOR_V2)*2);
    POFBytes[0]=0;
    POFBytes[1]=0;
    mss[0] = 0;
    mss[1] = 0;
    window_scale[0] = 0;
    window_scale[1] = 0;
    payload_max_len[0] = 0;
    payload_max_len[1] = 0;
    ack_payload_max_len[0] = 0;
    ack_payload_max_len[1] = 0;
    ack_payload_min_len[0] = 0;
    ack_payload_min_len[1] = 0;
    pkt_psh_num[0] = 0;
    pkt_psh_num[1] = 0;
    pkt_pronum = 0;
    pkt_unkonw_pronum = 0;
    pkt_syn_data = 0;
    memset(&tcp_finger_feature[0], 0, sizeof(s_tcp_finger_feature) * 2);
    tcp_finger_bitmap = 0;
    syn_seq_num = 0;
    memset(ipid_offset_list[0],0x0,6*sizeof(uint32_t));
    memset(ipid_offset_list[1],0x0,6*sizeof(uint32_t));
    ipid_last[0] = 0;
    ipid_last[1] = 0;
    pkt_bad_num[0] = 0;
    pkt_bad_num[1] = 0;
    record_payload_num[0] = 0;
    record_payload_num[1] = 0;
}

//int single_session_session::LoadPacket(void *IN_pProExtract,c_packet *IN_pPacket,session_pub *IN_pSession,unsigned char *IN_pJudge)
int single_session_session::LoadPacket(c_packet* IN_pPacket,session_pub* IN_pSession)
{
    if( IN_pPacket==0 )
    {
        return 0;
    }
 
    //CPlus_ProStackExtract *pProInfor=(CPlus_ProStackExtract*)IN_pProExtract;
    if(IN_pSession!=0)
    {
        //if( IN_pSession->packet_num==0 )
        //{
        //    TotalRuleNum=J_min(RuleInfor.RuleNum,MAXNUM_RULE_CONNECTEX_STATISTICS_V2);
        //    memcpy(pTotalRule,RuleInfor.pRuleID,sizeof(DWORD)*RuleInfor.RuleNum);
        //}
        //IN_pSession->proto_parse_sign=RuleInfor.ProID;
        //if( (TotalRuleNum<MAXNUM_RULE_CONNECTEX_STATISTICS_V2) && (IN_pPacket->m_str_packet_moudle.RuleNum!=0) )
        //{
        //    for(int i=0;i<TotalRuleNum;i++)
        //    {
        //        IN_pJudge[ pTotalRule[i] ]=1;
        //    }
 
        //    for(int i=0;i<IN_pPacket->m_str_packet_moudle.RuleNum;i++)
        //    {
        //        if( IN_pJudge[ IN_pPacket->m_str_packet_moudle.pRuleID[i] ]==0 )
        //        {
        //            pTotalRule[ TotalRuleNum++ ]=IN_pPacket->m_str_packet_moudle.pRuleID[i];
        //            IN_pJudge[ pTotalRule[i] ]=1;
        //        }
        //    }
 
        //    for(int i=0;i<TotalRuleNum;i++)
        //    {
        //        IN_pJudge[ pTotalRule[i] ]=0;
        //    }
        //}
        STR_UNILATERALINFOR_V2 *pUnilateral=&UnilateraliInfor[ IN_pPacket->Directory ];
        STR_PROTOCOLSTACK *pStack=&IN_pPacket->m_str_packet_moudle.Stack;
        //if( ProNum<pStack->ProtocolNum )
        //{
        //    pProInfor->Extract(ProNum,pStack,(*StackInfor)["ProStack"]);
        //}
        pUnilateral->TotalSign|=pStack->TotalSign;
        if( IN_pPacket->app_len!=0 )
        {
            pUnilateral->TotalPacketNum_Payload++;
            //20190228
            pUnilateral->TotalPayloadBytes+=IN_pPacket->app_len;
        }
        pUnilateral->TotalPacketNum++;
        pUnilateral->TotalPacketBytes+=IN_pPacket->packet_len;
        pUnilateral->MaxPacketLen=max(IN_pPacket->packet_len,pUnilateral->MaxPacketLen);
        if( IN_pPacket->p_iphdr!=0 )
        {
            pUnilateral->TTLS_Max=max(pUnilateral->TTLS_Max,IN_pPacket->p_iphdr->ttl);
            //20190218
            if( pUnilateral->TTLS_Min==0 )
            {
                pUnilateral->TTLS_Min=IN_pPacket->p_iphdr->ttl;
            }
            else
            {
                pUnilateral->TTLS_Min=min(pUnilateral->TTLS_Min,IN_pPacket->p_iphdr->ttl);;
            }

        }
        if( IN_pPacket->p_ipv6hdr!=0 )
        {
            pUnilateral->TTLS_Max=max(pUnilateral->TTLS_Max,IN_pPacket->p_ipv6hdr->ip6_ctlun.ip6_un1.ip6_un1_hlim);
            //20190218
            if( pUnilateral->TTLS_Min==0 )
            {
                pUnilateral->TTLS_Min=IN_pPacket->p_ipv6hdr->ip6_ctlun.ip6_un1.ip6_un1_hlim;
            }
            else
            {
                pUnilateral->TTLS_Min=min(pUnilateral->TTLS_Min,IN_pPacket->p_ipv6hdr->ip6_ctlun.ip6_un1.ip6_un1_hlim);
            }
        }
        /////////////////////////////////////////////TCP
        DWORD Sequence=0;
        DWORD Ackknowledgment=0;
        DWORD Flag=0;
 
        if( IN_pPacket->p_tcphdr!=0 )
        {
            char *pBuf=(char*)(IN_pPacket->p_tcphdr);
 
            Sequence=htonl( IN_pPacket->p_tcphdr->seq );
            Ackknowledgment=htonl( IN_pPacket->p_tcphdr->ack_seq );
            Flag=pBuf[13];
 
            //RST
            if( IN_pPacket->p_tcphdr->rst )
            {
                pUnilateral->RSTNum++;
            }
            //fin
            if( IN_pPacket->p_tcphdr->fin )
            {
                pUnilateral->FINNum++;
            }
 
            //存在SYN
            if( IN_pPacket->p_tcphdr->syn )
            {
                pUnilateral->SYNNum++;
                pUnilateral->SYNPacketBytes+=IN_pPacket->packet_len;
 
                //SYN ACK
                if( IN_pPacket->p_tcphdr->ack )
                {
                    POFBytes[1]=0;
                    int ProLen=0;
                    if( IN_pPacket->p_iphdr!=0 )
                    {
                        //IP仅取前20字节
                        ProLen=20;
                        memcpy( pP0F[1]+POFBytes[1],IN_pPacket->p_iphdr,ProLen );
                        POFBytes[1]+=ProLen;
                    }
                     else if( IN_pPacket->p_ipv6hdr!=0 )
                    {
                        ProLen=sizeof(ip6_hdr);
                        memcpy( pP0F[1]+POFBytes[1],IN_pPacket->p_ipv6hdr,ProLen );
                        POFBytes[1]+=ProLen;
                    }

 
                    if( IN_pPacket->p_tcphdr!=0 )
                    {
                        //TCp协议头长度
                        ProLen=(IN_pPacket->p_tcphdr->doff) * 4;
                        //剩余空间
                        DWORD CopyLen=POFSIZE-POFBytes[1];
                        CopyLen=J_min(CopyLen,ProLen);
                        memcpy( pP0F[1]+POFBytes[1],IN_pPacket->p_tcphdr,CopyLen );
                        POFBytes[1]+=CopyLen;
                    }
                }
                //SYN
                else
                {
                    POFBytes[0]=0;
                    int ProLen=0;
                    if( IN_pPacket->p_iphdr!=0 )
                    {
                        //IP仅取前20字节
                        ProLen=20;
                        memcpy( pP0F[0]+POFBytes[0],IN_pPacket->p_iphdr,ProLen );
                        POFBytes[0]+=ProLen;
                    }
                    else if( IN_pPacket->p_ipv6hdr!=0 )
                    {
                        ProLen=sizeof(ip6_hdr);
                        memcpy( pP0F[0]+POFBytes[0],IN_pPacket->p_ipv6hdr,ProLen );
                        POFBytes[0]+=ProLen;
                    }
 
                    if( IN_pPacket->p_tcphdr!=0 )
                    {
                        //TCp协议头长度
                        ProLen=(IN_pPacket->p_tcphdr->doff) * 4;
                        //剩余空间
                        DWORD CopyLen=POFSIZE-POFBytes[1];
                        CopyLen=J_min(CopyLen,ProLen);

                        memcpy( pP0F[0]+POFBytes[0],IN_pPacket->p_tcphdr,CopyLen );
                        POFBytes[0]+=CopyLen;
                    }
                }
            }
            /////////////////////////////////////////////TCP--End
 
        }
 
        /////////////////////////////////////////////
        //if( (pStack->ProtocolNum != 0) && (IN_pPacket->app_len!=0) )
        //{
        //    //
        //    if( DFINum<MAXNUM_DFI_CONNECTEX_STATISTICS_V2 )
        //    {
        //        pDFIInfor[DFINum].TimeStamp[0]=IN_pPacket->time_ts[0];
        //        pDFIInfor[DFINum].TimeStamp[1]=IN_pPacket->time_ts[1];
        //        pDFIInfor[DFINum].Count=IN_pSession->packet_num;
        //        pDFIInfor[DFINum].IsFromFirstIP=IN_pPacket->b_synip;
        //        pDFIInfor[DFINum].Protocol=IN_pPacket->app_pro;
        //        pDFIInfor[DFINum].SonProtocol=IN_pPacket->app_SonPro;
        //        pDFIInfor[DFINum].Len=IN_pPacket->app_len;
        //
        //        pDFIInfor[DFINum].Sequence=Sequence;
        //        pDFIInfor[DFINum].Ackknowledgment=Ackknowledgment;
        //        pDFIInfor[DFINum].Flag=Flag;
        //        DFINum++;
        //    }
        //    //
        //    //if( PayloadNum<PACKETNUM_PAYLOAD_CONNECTSTATISTICS_RULEENGINE_V2 )
        //    //{
        //    //    DWORD Bytes=BYTES_PAYLOAD_CONNECTSTATISTICS_RULEENGINE_V2;
        //    //    Bytes=min(IN_pPacket->app_len,Bytes);
        //    //    memcpy( pPayload[PayloadNum],IN_pPacket->app_buf, Bytes);
        //    //    PayloadNum++;
        //    //}
        //}
        ///////////////////////////////////////////////包信息--End
 
    }
    return 0;
}

#define IP_TOS_ECT        0x02    /* ECN supported                   */
#define IP4_DF            0x4000  /* Don't fragment (usually PMTUD)  */

static void parse_tcpopt(c_packet * p_packet, s_tcp_finger_feature *p_tcp_finger_feature, uint16_t *p_mss, uint32_t *p_optts)
{
    p_tcp_finger_feature->qk_opt_zero_ts1 = 0;
    p_tcp_finger_feature->tcpopt_eol_padnum = 0;
    p_tcp_finger_feature->tcpopt_layout[0] = 0;
    *p_mss = 0;
    *p_optts = 0;

    uint16_t head_len = p_packet->p_tcphdr->doff << 2;
    uint16_t offset = 20;
    unsigned char *pNewStart = (unsigned char *)p_packet->p_tcphdr;
    bool b_eol_pad = false;
    uint8_t tcpopt_num = 0;
    uint8_t opttype = 0;
    if (head_len > 20)
    {
        while (offset < head_len && false == b_eol_pad)
        {
            opttype = pNewStart[offset];
            switch (opttype)
            {
                case 0:
                    b_eol_pad = true;
                    offset += 1;
                    if(tcpopt_num < 40)
                    {
                        p_tcp_finger_feature->tcpopt_layout[tcpopt_num++] = '0';
                    }
                    break;
                case 1:
                    offset += 1;
                    if(tcpopt_num < 40)
                    {
                        p_tcp_finger_feature->tcpopt_layout[tcpopt_num++] = '1';
                    }
                    break;
                case 2:
                    if(offset + 4 <= head_len)
                    {
                        *p_mss = ntohs(*(uint16_t *)&pNewStart[offset + 2]);
                    }
                    offset += 4;
                    if(tcpopt_num < 40)
                    {
                        p_tcp_finger_feature->tcpopt_layout[tcpopt_num++] = '2';
                    }
                    break;
                case 8:
                    if(offset + 10 <= head_len)
                    {
                        *p_optts =  *(uint32_t *)&pNewStart[offset + 2];
                        if(0 == *p_optts)
                        {
                            p_tcp_finger_feature->qk_opt_zero_ts1 = 1;
                        }
                    }
                    offset += 10;
                    if(tcpopt_num < 40)
                    {
                        p_tcp_finger_feature->tcpopt_layout[tcpopt_num++] = '8';
                    }
                    break;
                default:
                    if (offset + 1 >= head_len || 0 == pNewStart[offset + 1])
                    {
                        offset = head_len;
                    }
                    else
                    {
                        offset += pNewStart[offset + 1];
                    }
                    if(tcpopt_num < 40 && opttype >=0 && opttype <= 8)
                    {
                        p_tcp_finger_feature->tcpopt_layout[tcpopt_num++] = opttype + 48;
                    }
                    break;
            }
        }
    }
    if(b_eol_pad)
    {
        p_tcp_finger_feature->tcpopt_eol_padnum = head_len - offset;
    }
}

static uint16_t detect_winmulti(c_packet * p_packet, uint16_t win, uint16_t mss, uint32_t optts)
{
    uint16_t mss12 = mss - 12;
    if (!win || mss < 100)
        return 0;
#define RET_IF_DIV(_div, _desc) do { \
    if ((_div) && !(win % (_div))) { \
      return win / (_div); \
    } \
  } while (0)

    RET_IF_DIV(mss, "MSS");
    /* Some systems will sometimes subtract 12 bytes when timestamps are in use. */
    if(optts)
    {
        RET_IF_DIV(mss12, "MSS - 12");
    }
    RET_IF_DIV(1500 - 40, "MSS (MTU = 1500, IPv4)");
    RET_IF_DIV(1500 - 40 - 12, "MSS (MTU = 1500, IPv4 - 12)");
    if (2 == p_packet->u_ip)
    {
        RET_IF_DIV(1500 - 60, "MSS (MTU = 1500, IPv6)");
        RET_IF_DIV(1500 - 60 - 12, "MSS (MTU = 1500, IPv6 - 12)");
    }
    /* Some systems use MTU instead of MSS: */
    if ( 1 == p_packet->u_ip)
    {
        RET_IF_DIV(mss + 40, "MTU (IPv4)");
        RET_IF_DIV(mss + (p_packet->p_iphdr->ihl * 4) + (p_packet->p_tcphdr->doff << 2) , "MTU (actual size)");
    }
    else if (2 == p_packet->u_ip)
    {
        RET_IF_DIV(mss + 60, "MTU (IPv6)");
        RET_IF_DIV(mss + 40 + (p_packet->p_tcphdr->doff << 2) , "MTU (actual size)");
    }
    RET_IF_DIV(1500, "MTU (1500)");

#undef RET_IF_DIV
    return 0;
}

void single_session_session::add_packet_info(session_pub * p_session , c_packet * p_packet) 
{
    //appid等于以下值不参与统计： HTTP（10637），DNS（10071），NTP（10413）
    /*if (p_session->proto_parse_sign == 10637 || p_session->proto_parse_sign == 10071 || p_session->proto_parse_sign == 10413)
    {
        return;
    }
    if (p_packet->proto_parse_sign == 10637 || p_packet->proto_parse_sign == 10071 || p_packet->proto_parse_sign == 10413)
    {
        return;
    }*/
    if(1 == p_session -> session_basic.pPacketNum[0] && 0 == p_session -> session_basic.pPacketNum[1])
    {
        first_packet_info(p_session , p_packet );
    }
    else
    {
        if(p_packet->time_ts[0] < ts_last[0])
        {
            duration[0] ++;
        }
        else if ( p_packet->time_ts[0] - ts_last[0] <= 64)
        {
            uint32_t idx = 1;
            idx += (p_packet->time_ts[0] - ts_last[0]);
            if(p_packet->time_ts[1] < ts_last[1])
            {
                idx -= 1;
            }
            duration[time_map[idx]] ++;
        }
        else
        {
            duration[7] ++;
        }
    }

    if(1 == p_packet->u_ip)
    {
        if(1 != p_session -> session_basic.pPacketNum[p_packet->Directory])
        {
            int id = (int)ntohs(p_packet->p_iphdr->id);
            int offset = id - ipid_last[p_packet->Directory];
            if(offset < -64)
            {
                ipid_offset_list[p_packet->Directory][0] ++;
            }
            else if(offset >= 64)
            {
                ipid_offset_list[p_packet->Directory][5] ++;
            }
            else
            {
                ipid_offset_list[p_packet->Directory][offset_map[offset+64]] ++;
            }
        }
        ipid_last[p_packet->Directory] = (int)ntohs(p_packet->p_iphdr->id);
    }

    ts_last[0] = p_packet->time_ts[0];
    ts_last[1] = p_packet->time_ts[1];

    uint32_t packet_num = p_session -> session_basic.pPacketNum[0] + p_session -> session_basic.pPacketNum[1];
    uint32_t payload_num =  p_session -> session_basic.pPayloadNum[0] + p_session -> session_basic.pPayloadNum[1];
    // base.Handle(p_packet);

    if(payload_num && payload_num <= MAXPACKETPLNUM && p_packet -> app_len > 0)
    {
        PInfo[payload_num-1].handle(p_packet, packet_num, payload_num);
    }
    if(p_session -> session_basic.pPayloadNum[0] > record_payload_num[0] && p_session -> session_basic.pPayloadNum[0] <= MAXPACKETINFONUM)
    {
        uint32_t idx = p_session -> session_basic.pPayloadNum[0] - 1;
        for(uint32_t i = record_payload_num[0]; i < idx ; i ++)
        {
            PayInfo[0][i].clear();
        }
        PayInfo[0][idx].handle(p_packet);
        record_payload_num[0] = p_session -> session_basic.pPayloadNum[0];
    }
    if(p_session -> session_basic.pPayloadNum[1] > record_payload_num[1] && p_session -> session_basic.pPayloadNum[1] <= MAXPACKETINFONUM)
    {
        uint32_t idx = p_session -> session_basic.pPayloadNum[1] - 1;
        for(uint32_t i = record_payload_num[1]; i < idx ; i ++)
        {
            PayInfo[1][idx].clear();
        }
        PayInfo[1][idx].handle(p_packet);
        record_payload_num[1] = p_session -> session_basic.pPayloadNum[1];
    }

    //for(int i = 0 ; i < p_packet -> app_len ; i++)
    //{ 
    //    DIST[p_packet -> app_buf[i]] ++;
    //
    //}

    //fill tcp finger feature
    if(p_packet->flags.b_has_flag_syn)
    {
        uint8_t finger_index = p_packet->p_tcphdr->ack;
        tcp_finger_bitmap |= ((uint8_t)1 << finger_index);
        if(1 == p_packet->u_ip)
        {
            tcp_finger_feature[finger_index].ecn_ip_ect = (p_packet->p_iphdr->tos & IP_TOS_ECT) ? 1 : 0;
            if(p_packet->p_iphdr->ttl <= 32)
            {
                tcp_finger_feature[finger_index].ttl = 32;
            }
            else if (p_packet->p_iphdr->ttl <= 64)
            {
                tcp_finger_feature[finger_index].ttl = 64;
            }
            else if (p_packet->p_iphdr->ttl <= 128)
            {
                tcp_finger_feature[finger_index].ttl = 128;
            }
            else
            {
                tcp_finger_feature[finger_index].ttl = 255;
            }
            tcp_finger_feature[finger_index].qk_dfnz_ipid = ((p_packet->p_iphdr->frag_off & IP4_DF) && p_packet->p_iphdr->id) ? 1 : 0;
            tcp_finger_feature[finger_index].flag_CWR = (p_packet->p_tcphdr->res2 & 0x2) ? 1 : 0;
            tcp_finger_feature[finger_index].flag_ECE = (p_packet->p_tcphdr->res2 & 0x1) ? 1 : 0;
            uint16_t win = ntohs(p_packet->p_tcphdr->window), tcp_mss = 0;
            uint32_t optts = 0;
            parse_tcpopt(p_packet, &tcp_finger_feature[finger_index], &tcp_mss, &optts);
            tcp_finger_feature[finger_index].tcpopt_wscale = (uint8_t)p_packet->window_scale;
            if(tcp_mss)
            {
                tcp_finger_feature[finger_index].qk_win_mss = detect_winmulti(p_packet, win, tcp_mss, optts);
            }
            if(0 == tcp_finger_feature[finger_index].qk_win_mss)
            {
                tcp_finger_feature[finger_index].qk_win_mss = win;
            }
        }
        else if (2 == p_packet->u_ip)
        {
            uint32_t ver_tos = ntohl(p_packet->p_ipv6hdr->ip6_ctlun.ip6_un1.ip6_un1_flow);
            tcp_finger_feature[finger_index].ecn_ip_ect = ((ver_tos >> 20) & IP_TOS_ECT) ? 1 : 0;
            if(p_packet->p_ipv6hdr->ip6_ctlun.ip6_un1.ip6_un1_hlim <= 32)
            {
                tcp_finger_feature[finger_index].ttl = 32;
            }
            else if (p_packet->p_ipv6hdr->ip6_ctlun.ip6_un1.ip6_un1_hlim <= 64)
            {
                tcp_finger_feature[finger_index].ttl = 64;
            }
            else if (p_packet->p_ipv6hdr->ip6_ctlun.ip6_un1.ip6_un1_hlim <= 128)
            {
                tcp_finger_feature[finger_index].ttl = 128;
            }
            else
            {
                tcp_finger_feature[finger_index].ttl = 255;
            }
            tcp_finger_feature[finger_index].qk_dfnz_ipid = 0;
            tcp_finger_feature[finger_index].flag_CWR = (p_packet->p_tcphdr->res2 & 0x2) ? 1 : 0;
            tcp_finger_feature[finger_index].flag_ECE = (p_packet->p_tcphdr->res2 & 0x1) ? 1 : 0;
            uint16_t win = ntohs(p_packet->p_tcphdr->window), tcp_mss = 0;
            uint32_t optts = 0;
            parse_tcpopt(p_packet, &tcp_finger_feature[finger_index], &tcp_mss, &optts);
            tcp_finger_feature[finger_index].tcpopt_wscale = (uint8_t)p_packet->window_scale;
            if(tcp_mss)
            {
                tcp_finger_feature[finger_index].qk_win_mss = detect_winmulti(p_packet, win, tcp_mss, optts);
            }
            if(0 == tcp_finger_feature[finger_index].qk_win_mss)
            {
                tcp_finger_feature[finger_index].qk_win_mss = win;
            }
        }
        if((0 == p_packet -> Directory && PACKETFROMCLIENT == p_session->session_basic.Server) || (1 == p_packet -> Directory && PACKETFROMSERVER == p_session->session_basic.Server))  //record syn seq
        {
            if(syn_seq_num == 0)
            {
                syn_seq_list[0] = p_packet->seq;
                syn_seq_num = 1;
            }
            else if(syn_seq_num < 255)
            {
                uint8_t syn_idx = syn_seq_num-1;
                if(syn_idx > 2)
                {
                    syn_idx = 2;
                }
                uint32_t seq_diff = (p_packet->seq > syn_seq_list[syn_idx]) ? (p_packet->seq - syn_seq_list[syn_idx]) : (syn_seq_list[syn_idx] - p_packet->seq);
                if(seq_diff > 1000)
                {
                    if(syn_seq_num < MAX_SEQ_RECORD)
                    {
                        syn_seq_list[syn_seq_num] = p_packet->seq;
                    }
                    syn_seq_num ++;
                }
            }
        }
    }
    //fill tcp finger feature --end
    totalsign[p_packet->Directory] |= p_packet->TotalSign;
    if (p_packet->app_len != 0)
    {
        if (p_packet->app_len >= 1024)
        {
            distLen[p_packet->Directory][7]++;
        }
        else
        {
            distLen[p_packet->Directory][len_map[p_packet->app_len]]++;
        }
    }
    pkt_maxlen[p_packet->Directory] = (pkt_maxlen[p_packet->Directory] > p_packet->packet_len)?pkt_maxlen[p_packet->Directory]:p_packet->packet_len;
    max_prolist_num = (max_prolist_num > p_packet->m_str_packet_moudle.Stack.ProtocolNum)?max_prolist_num:p_packet->m_str_packet_moudle.Stack.ProtocolNum;
    LoadPacket(p_packet, p_session);
    if(p_packet->flags.b_has_flag_syn)
    {
        mss[p_packet->Directory] = p_packet->mss;
        window_scale[p_packet->Directory] = p_packet->window_scale;
        if (p_packet->payload_len)
        {
            pkt_syn_data++;
        }
    }
    payload_max_len[p_packet->Directory] = p_packet->payload_len > payload_max_len[p_packet->Directory] ? p_packet->payload_len : payload_max_len[p_packet->Directory];
    if(p_packet->flags.b_only_flag_ack && p_packet->payload_len)
    {
        ack_payload_max_len[p_packet->Directory] = p_packet->payload_len > ack_payload_max_len[p_packet->Directory] ? p_packet->payload_len : ack_payload_max_len[p_packet->Directory];
        if(ack_payload_min_len[p_packet->Directory])
        {
            ack_payload_min_len[p_packet->Directory] = p_packet->payload_len < ack_payload_min_len[p_packet->Directory] ? p_packet->payload_len : ack_payload_min_len[p_packet->Directory];
        }
        else
        {
            ack_payload_min_len[p_packet->Directory] = p_packet->payload_len;
        }
    }
    if (p_packet->flags.b_has_flag_psh)
    {
        pkt_psh_num[p_packet->Directory] ++;
    }
    pkt_bad_num[p_packet->Directory] += p_packet->flags.b_bad_tcp_hdrlen;

    if (p_packet -> app_len)
    {
        STR_PROTOCOLSTACK *pStack=&p_packet->m_str_packet_moudle.Stack;
        uint32_t protocol = pStack->pProtocol[pStack->ProtocolNum - 1].Protocol;
        if (protocol < 10)
        {
            pkt_unkonw_pronum ++;
        }
        else
        {
            pkt_pronum ++;
        }
    }
}

void single_session_session::first_packet_info(session_pub * p_session , c_packet * p_packet)
{
    // 判断
    IpPro = p_packet -> u_tcp ;
}

/*string single_session_session::bin2string(char* p_data, int len)
{
    string s = "";
    char formate_char[2];
    for (int i = 0; i < len; ++i)
    {
        sprintf(formate_char, "%02x", (unsigned char)p_data[i]);
        s.append(formate_char, 2);
    }
    return s;
}*/

string finger_list_str(single_session_msg* p_msg)
{
    char buf[24];
    string ret = "";
    int b_first = 1;
    if(NULL != p_msg)
    {
        if(p_msg->http_c_finger() > 0)
        {
            sprintf(buf, ",\"%llu\"", p_msg->http_c_finger());
            if(b_first)
            {
                ret = string(buf + 1);
                b_first = 0;
            }
            else
            {
                ret += string(buf);
            }
        }
        if(p_msg->http_s_finger() > 0)
        {
            sprintf(buf, ",\"%llu\"", p_msg->http_s_finger());
            if(b_first)
            {
                ret = string(buf + 1);
                b_first = 0;
            }
            else
            {
                ret += string(buf);
            }
        }
        if(p_msg->ssl_c_finger() > 0)
        {
            sprintf(buf, ",\"%llu\"", p_msg->ssl_c_finger());
            if(b_first)
            {
                ret = string(buf + 1);
                b_first = 0;
            }
            else
            {
                ret += string(buf);
            }
        }
        if(p_msg->ssl_s_finger() > 0)
        {
            sprintf(buf, ",\"%llu\"", p_msg->ssl_s_finger());
            if(b_first)
            {
                ret = string(buf + 1);
                b_first = 0;
            }
            else
            {
                ret += string(buf);
            }
        }
        if(p_msg->tcp_c_finger() > 0)
        {
            sprintf(buf, ",\"%llu\"", p_msg->tcp_c_finger());
            if(b_first)
            {
                ret = string(buf + 1);
                b_first = 0;
            }
            else
            {
                ret += string(buf);
            }
        }
        if(p_msg->tcp_s_finger() > 0)
        {
            sprintf(buf, ",\"%llu\"", p_msg->tcp_s_finger());
            if(b_first)
            {
                ret = string(buf + 1);
                b_first = 0;
            }
            else
            {
                ret += string(buf);
            }
        }
    }
    return ret;
}

static string labels_2_string(single_session_msg* p_sess)
{
    char buf[12];
    string ret = "[";
    for(int i = 0; i < p_sess->rule_labels_size(); i ++)
    {
        if(i)
        {
            sprintf(buf, ",%u", p_sess->rule_labels(i));
        }
        else
        {
            sprintf(buf, "%u", p_sess->rule_labels(i));
        }
        ret += string(buf);
    }
    ret += "]";
    return ret;
}

void single_session_session::send_session (session_pub * p_session, void *p_th_tools, int should_log)
{
//    static int send_sing_num  = 0 ;
//    send_sing_num ++ ;
    //printf("single_session send session  %d\n",send_sing_num) ;
    /*static int  i =  0 ;
    i ++;
    printf(" single_session  send ==== %d\n ", i );
    */
    int server_port_type = 0;
    if(p_session == NULL || p_session -> p_value == NULL)
    {
        return ;
    }
    th_engine_tools *tools = (th_engine_tools *)p_th_tools;
    CInterface_Statistics* p_session_statistics = (CInterface_Statistics* )p_session->p_session_statistics;
    if(p_session -> session_basic.Server ==  PACKETFROMUNKOWN)
    {
        p_session -> session_basic.Server = tools->judge_c2s_by_study(p_session -> session_basic.Server, p_session -> session_basic.IPPro, p_session -> session_basic.pPort);
        p_session -> b_unknown_c2s = 1;
    }
    else if(0==p_session->thread_id)
    {
        if(17 == p_session->session_basic.IPPro)
        {
            tools->study_c2s_by_port(p_session -> session_basic.Server, p_session->session_basic.IPPro, p_session -> session_basic.pPort, p_session -> b_unknown_c2s);
        }
    }
/*
无负债连接的APPID不应该是UNKONOW，新增一个无负载（No_Payload）
TCP会话的应用分为：
-- 仅请求  QueryOnly；客户端发送SYN，服务器无回应
-- 端口关闭 PortClose；客户端发送SYN，服务器发送FIN或RST
-- 无负载 NoPayload；握手正常、挥手正常，没有发送负载数据
-- 未知协议： Unknow；握手正常、挥手正常，有负载数据，协议未知
-- 协议识别成功
-- 其他： Other；非上述5种
UDP会话分为UDP_NoPayload,UDP_Unknown
 */
    if(p_session->session_basic.ProtoSign == 0 )
    {
        if(p_session->session_basic.IPPro == 17)
        {
            if(0 == p_session->session_basic.pPayloadNum[0] + p_session->session_basic.pPayloadNum[1])
            {
                p_session->session_basic.ProtoSign = 10707;
            }
            else
            {
                p_session->session_basic.ProtoSign = 10708;
            }
        }
    }
    if(p_session->session_basic.ProtoSign == 0)
    {
        if(0 == p_session->session_basic.pPayloadNum[0]&& 0 == p_session->session_basic.pPayloadNum[1])
        {
            p_session->session_basic.ProtoSign = 10701;
        }
        else
        {
            p_session->session_basic.ProtoSign = 10000;
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_UNKNOWN_APP);
        }
    }
    if(p_session -> session_basic.Server == PACKETFROMCLIENT)
    {
        if(p_session->session_basic.pPacketNum[0] > 10 && p_session->session_basic.pPacketNum[1] > 10 && p_session->session_basic.pPacketBytes[0] > p_session->session_basic.pPacketBytes[1])
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_UP_GE_DOWN);
        }
        int server_port_type = p_portjudge->judge_unofficial(p_session->session_basic.IPPro, p_session->session_basic.ProtoSign, p_session->session_basic.pPort[1]);
        if(1 == server_port_type)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_PORT_NO_MATCH_PRO);
        }
        else if(2 == server_port_type)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_UNCOMMON_PORT);
        }
    }
    else
    {
        if(p_session->session_basic.pPacketNum[0] > 10 && p_session->session_basic.pPacketNum[1] > 10 && p_session->session_basic.pPacketBytes[1] > p_session->session_basic.pPacketBytes[0])
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_UP_GE_DOWN);
        }
        int server_port_type = p_portjudge->judge_unofficial(p_session->session_basic.IPPro, p_session->session_basic.ProtoSign, p_session->session_basic.pPort[0]);
        if(1 == server_port_type)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_PORT_NO_MATCH_PRO);
        }
        else if(2 == server_port_type)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_UNCOMMON_PORT);
        }
    }
    if(p_session->session_basic.EndTime[0]-p_session->session_basic.StartTime[0] > 3600)
    {
        th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_DUR_GT60);
    }
    else if(p_session->session_basic.EndTime[0]-p_session->session_basic.StartTime[0] > 1800)
    {
        th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_DUR_GT30);
    }
    if(p_session->session_basic.pPacketBytes[0] + p_session->session_basic.pPacketBytes[1] > (1 << 30))
    {
        th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_MBYTE_GE1024);
    }
    else if(p_session->session_basic.pPacketBytes[0] + p_session->session_basic.pPacketBytes[1] > (100 << 20))
    {
        th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_MBYTE_GE100);
    }
    if((p_session->session_basic.IO_Sign[0] & OUTLAND_IP_L2DEFENSE) != (p_session->session_basic.IO_Sign[1] & OUTLAND_IP_L2DEFENSE))
    {
        th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_CROSS_BORDER);
    }
    if((0 == p_session->session_basic.pPacketNum[0] || 0 == p_session->session_basic.pPacketNum[1]) && (p_session->session_basic.pPayloadNum[0] || p_session->session_basic.pPayloadNum[1]))
    {
        th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_ONE_SIDE_FLOW);
    }
    if(should_log)
    {
        JKNmsg *p_msg = p_session->p_value -> get();
        if(NULL==p_msg)
        {
            return;
        }
        CJsonMsg *p_json_msg = p_session->p_value -> jget();
        p_msg ->set_type(30);
        single_session_msg* p_single_session_msg = p_msg-> mutable_single_session();
        Comm_msg* p_comm = p_single_session_msg -> mutable_comm_msg();
        ss_basic_msg* p_ss_basic = p_single_session_msg -> mutable_ss_basic();
        ss_stats_msg* p_ss_stats = p_single_session_msg -> mutable_ss_stats();
        ss_pkt_msg* p_ss_pkt = p_single_session_msg -> mutable_ss_pkt();
        if(p_session -> session_basic.Server == PACKETFROMCLIENT)
        {
            if(p_session->b_has_proxy)
            {
                p_single_session_msg ->set_proxy_ip(p_session->session_basic.pIP[1].ip_str());
                p_single_session_msg ->set_proxy_port(p_session->session_basic.pPort[1]);
            }
        }
        else
        {
            if(p_session->b_has_proxy)
            {
                p_single_session_msg ->set_proxy_ip(p_session->session_basic.pIP[0].ip_str());
                p_single_session_msg ->set_proxy_port(p_session->session_basic.pPort[0]);
            }
        }
        p_single_session_msg ->set_end_time(p_session->session_basic.EndTime[0]);
        p_single_session_msg ->set_end_nsec(p_session->session_basic.EndTime[1]);
        p_single_session_msg ->set_handle_begin_time(p_session->StartClock);
        p_single_session_msg ->set_handle_end_time(p_session->EndClock);
        p_single_session_msg ->set_first_sender(p_session->session_basic.pIP[0].ip_str());
        p_single_session_msg ->set_duration(p_session->session_basic.EndTime[0] - p_session->session_basic.StartTime[0]);
        if(p_session->session_basic.RuleNum + p_session->packet_sign_num)
        {
            std::set<uint32_t> sign_set;
            for(uint32_t i = 0; i < p_session->session_basic.RuleNum; i++)
            {
                sign_set.insert(p_session->session_basic.pRule[i]);
            }
            for(uint8_t i = 0; i < p_session->packet_sign_num; i++)
            {
                sign_set.insert(p_session->packet_sign[i]);
            }
            for(std::set<uint32_t> ::iterator it = sign_set.begin(); it != sign_set.end(); it ++)
            {
                p_single_session_msg -> add_rule_labels(*it);
            }
            sign_set.clear();
        }
        for(uint16_t i = 0; i < (LABEL_END+7)/8; i ++)
        {
            if(p_session->labels[i])
            {
                uint16_t max = (i + 1) * 8;
                for(uint16_t j = i * 8; j < max; j ++)
                {
                    if(th_engine_tools::instance()->th_get_label(p_session->labels, j))
                    {
                        p_single_session_msg -> add_rule_labels((uint32_t)j+LABEL_START);
                    }
                }
            }
        }
        p_single_session_msg -> set_device_id(th_engine_tools::instance()->device_id);
        p_single_session_msg -> set_first_proto(p_session->first_proto);

        for(uint16_t portid = 0 ; portid < 16; portid++)
        {
            if(p_session->port_map & ((uint16_t)1 << portid))
            {
                p_single_session_msg->add_port_list(portid);
            }
        }
        for(int i = 0; i < p_session->http.size(); i ++)
        {
            single_http *phttp = p_single_session_msg->add_http();
            phttp->set_url(p_session->http[i].url);
            phttp->set_act(p_session->http[i].act);
            phttp->set_host(p_session->http[i].host);
            phttp->set_response(p_session->http[i].response);
            phttp->set_user_agent(p_session->http[i].user_agent);
        }
        for(int i = 0; i < p_session->dns.size(); i ++)
        {
            single_dns *pdns = p_single_session_msg->add_dns();
            pdns->set_domain(p_session->dns.m_vector[i]->domain);
            pdns->set_domain_ip(p_session->dns.m_vector[i]->domain_ip);
            for(int j = 0; j < p_session->dns.m_vector[i]->answer.size(); j ++)
            {
                single_dns_answer *panswer = pdns->add_answer();
                panswer->set_name(p_session->dns.m_vector[i]->answer[j].name);
                panswer->set_value(p_session->dns.m_vector[i]->answer[j].value);
            }
        }
        for(int i = 0; i < p_session->ssl.size(); i ++)
        {
            single_ssl *pssl = p_single_session_msg->add_ssl();
            pssl->set_ch_ciphersuit(p_session->ssl[i].ch_ciphersuit);
            pssl->set_ch_ciphersuit_num(p_session->ssl[i].ch_ciphersuit_num);
            pssl->set_ch_server_name(p_session->ssl[i].ch_server_name);
            pssl->set_ch_alpn(p_session->ssl[i].ch_alpn);
            pssl->set_c_cert(p_session->ssl[i].c_cert);
            pssl->set_c_cert_num(p_session->ssl[i].c_cert_num);
            pssl->set_s_cert(p_session->ssl[i].s_cert);
            pssl->set_s_cert_num(p_session->ssl[i].s_cert_num);
        }
        // p_comm->set_state("Complete");// ////连接状态: Complete 正常结束；  TimeOut 超时结束； Byforce 强制结束

        if(p_session->b_has_mac)
        {
            if(p_session -> session_basic.Server == PACKETFROMCLIENT)
            {
                char tmp[18] = {0};
                macToStr((unsigned char *)&(p_session ->srcmac), tmp , 18);
                p_ss_basic->set_smac(string(tmp));
                macToStr((unsigned char *)&(p_session ->dstmac), tmp , 18);
                p_ss_basic->set_dmac(string(tmp));
            }
            else 
            {
                char tmp[18] = {0};
                macToStr((unsigned char *)&(p_session ->dstmac), tmp , 18);
                p_ss_basic->set_smac(string(tmp));
                macToStr((unsigned char *)&(p_session ->srcmac), tmp , 18);
                p_ss_basic->set_dmac(string(tmp));
            }
        }

        rule_msg * p_rule = p_ss_basic->mutable_rule();
        p_rule -> set_level( p_session->session_basic.RuleLevel);
        p_rule -> set_connect_rule_num(p_session->session_basic.RuleNum);
        for(int  i = 0 ; i < p_session -> session_basic.RuleNum  ; i++)
        {
            p_rule->add_connect_rule(p_session -> session_basic.pRule[i]);
        }

        p_ss_basic->set_rule_num(p_session->session_basic.RuleNum);
        p_ss_basic->set_rule_level(p_session->session_basic.RuleLevel);
        p_ss_basic->set_syn_ack("");

        if(p_session -> session_basic.Server == PACKETFROMCLIENT)
        {
            p_ss_stats ->set_sio_sign(p_session ->session_basic.IO_Sign[0]);
            p_ss_stats ->set_dio_sign(p_session ->session_basic.IO_Sign[1]);
            p_ss_pkt->set_pkt_snum(p_session->session_basic.pPacketNum[0]);
            p_ss_pkt->set_pkt_dnum(p_session->session_basic.pPacketNum[1]);
            p_ss_pkt->set_pkt_spayloadnum(p_session->session_basic.pPayloadNum[0]);
            p_ss_pkt->set_pkt_dpayloadnum(p_session->session_basic.pPayloadNum[1]);
            p_ss_pkt->set_pkt_sbytes(p_session->session_basic.pPacketBytes[0]);
            p_ss_pkt->set_pkt_dbytes(p_session->session_basic.pPacketBytes[1]);
            p_ss_pkt->set_pkt_spayloadbytes(p_session->pkt_payloadbytes[0]);
            p_ss_pkt->set_pkt_dpayloadbytes(p_session->pkt_payloadbytes[1]);
        }
        else
        {
            p_ss_stats ->set_sio_sign(p_session ->session_basic.IO_Sign[1]);
            p_ss_stats ->set_dio_sign(p_session ->session_basic.IO_Sign[0]);
            p_ss_pkt->set_pkt_snum(p_session->session_basic.pPacketNum[1]);
            p_ss_pkt->set_pkt_dnum(p_session->session_basic.pPacketNum[0]);
            p_ss_pkt->set_pkt_spayloadnum(p_session->session_basic.pPayloadNum[1]);
            p_ss_pkt->set_pkt_dpayloadnum(p_session->session_basic.pPayloadNum[0]);
            p_ss_pkt->set_pkt_sbytes(p_session->session_basic.pPacketBytes[1]);
            p_ss_pkt->set_pkt_dbytes(p_session->session_basic.pPacketBytes[0]);
            p_ss_pkt->set_pkt_spayloadbytes(p_session->pkt_payloadbytes[1]);
            p_ss_pkt->set_pkt_dpayloadbytes(p_session->pkt_payloadbytes[0]);
        }
        p_ss_pkt->set_app_pkt_id(p_session->app_pkt_id);
        p_ss_stats ->set_ext_json(p_session -> session_basic.ExtString);
        if(p_session_statistics)
        {
            Json::FastWriter fast_writer;
            string ser_json = fast_writer.write(*p_session_statistics->StackInfor);
            ser_json.pop_back();
            p_ss_stats->set_stats_prolist(ser_json);
        }
        commsg_fill(p_session , p_comm, "", p_th_tools);
        if(p_json_msg)
        {
            position_info src_pos = p_ip_pos->work(p_comm->src_ip(), p_session->thread_id);
            position_info dst_pos = p_ip_pos->work(p_comm->dst_ip(), p_session->thread_id);
            uint64_t total_bytes = p_ss_pkt->pkt_sbytes() + p_ss_pkt->pkt_dbytes();
            int size = snprintf((char *)p_json_msg->get_buf(), p_json_msg->get_buf_len(),
"{\
\"AppId\":%u,\"Duration\":%u,\"EndTime\":%u,\"HandleBeginTime\":%u,\"HandleEndTime\":%u,\
\"Labels\":%s,\"IPPro\":%u,\"SessionId\":\"%s\",\"StartTime\":%u,\"ThreadId\":%u,\
\"TotalBytes\":%"PRIu64",\"dHTTPFinger\":\"%"PRIu64"\",\"dIp\":\"%s\",\"dMac\":\"%s\",\"dPort\":%u,\
\"dSSLFinger\":\"%"PRIu64"\",\"dTCPFinger\":\"%"PRIu64"\",\"ip2ip\":\"%s\",\"pkt\":{\"dBytes\":%"PRIu64",\"dNum\":%u,\
\"sBytes\":%"PRIu64",\"sNum\":%u},\"sHTTPFinger\":\"%"PRIu64"\",\"sIp\":\"%s\",\"sMac\":\"%s\",\
\"sPort\":%u,\"sSSLFinger\":\"%"PRIu64"\",\"sTCPFinger\":\"%"PRIu64"\",\"sip_appid_dport_dip\":\"%s\",\"type\":3009,\
\"sIpCountry\":\"%s\",\"sIpSubdivisions\":\"%s\",\"sIpCity\":\"%s\",\"sIpLatitude\":\"%f\",\"sIpLongitude\":\"%f\",\
\"dIpCountry\":\"%s\",\"dIpSubdivisions\":\"%s\",\"dIpCity\":\"%s\",\"dIpLatitude\":\"%f\",\"dIpLongitude\":\"%f\",\
\"FirstSender\":\"%s\",\"finger_list\":[%s],\"DNS\":[%s],\"HTTP\":[%s],\"SSL\":[%s]\
}",
                p_comm->app_id(),p_single_session_msg->duration(),p_single_session_msg->end_time(),p_single_session_msg->handle_begin_time(),p_single_session_msg->handle_end_time(),
                labels_2_string(p_single_session_msg).c_str(),p_comm->ippro(),p_comm->session_id().c_str(),p_comm->begin_time(),p_comm->thread_id(),
                total_bytes,p_single_session_msg->http_s_finger(),p_comm->dst_ip().c_str(),p_ss_basic->dmac().c_str(),p_comm->dst_port(),
                p_single_session_msg->ssl_s_finger(),p_single_session_msg->tcp_s_finger(),((p_comm->src_ip()<=p_comm->dst_ip())?(p_comm->src_ip()+"_"+p_comm->dst_ip()):(p_comm->dst_ip()+"_"+p_comm->src_ip())).c_str(),p_ss_pkt->pkt_dbytes(),p_ss_pkt->pkt_dnum(),
                p_ss_pkt->pkt_sbytes(),p_ss_pkt->pkt_snum(),p_single_session_msg->http_c_finger(),p_comm->src_ip().c_str(),p_ss_basic->smac().c_str(),
                p_comm->src_port(),p_single_session_msg->ssl_c_finger(),p_single_session_msg->tcp_c_finger(),(p_comm->src_ip()+"_"+std::to_string(p_comm->app_id())+"_"+std::to_string(p_comm->dst_port())+"_"+p_comm->dst_ip()).c_str(),
                src_pos.country.c_str(),src_pos.province.c_str(),src_pos.city.c_str(),src_pos.latitude,src_pos.longitude,
                dst_pos.country.c_str(),dst_pos.province.c_str(),dst_pos.city.c_str(),dst_pos.latitude,dst_pos.longitude,
                (PACKETFROMSERVER==p_session->session_basic.Server)?p_comm->dst_ip().c_str():p_comm->src_ip().c_str(),finger_list_str(p_single_session_msg).c_str(),session_pub_dns_serialize(p_session->dns).c_str(),session_pub_http_serialize(p_session->http).c_str(),session_pub_ssl_serialize(p_session->ssl).c_str()
            );
            p_json_msg->set_offset(size);
        }
    }
}

void single_session_session::send (session_pub * p_session, void *p_th_tools, void *aes_hash_ctx, int should_log)
{
//    static int send_sing_num  = 0 ;
//    send_sing_num ++ ;
    //printf("single_session send session  %d\n",send_sing_num) ;
    /*static int  i =  0 ;
    i ++;
    printf(" single_session  send ==== %d\n ", i );
    */
    if(p_session == NULL || p_session -> p_value == NULL)
    {
        return ;
    }
    if (p_session->session_basic.pPacketNum[0] == 0 && p_session->session_basic.pPacketNum[1] == 0)
    {
        return;
    }
    th_engine_tools *tools = (th_engine_tools *)p_th_tools;
    CInterface_Statistics* p_session_statistics = (CInterface_Statistics* )p_session->p_session_statistics;
    if(p_session -> session_basic.Server == PACKETFROMUNKOWN)
    {
        p_session -> session_basic.Server = tools->judge_c2s_by_study(p_session -> session_basic.Server, p_session -> session_basic.IPPro, p_session -> session_basic.pPort);
        p_session -> b_unknown_c2s = 1;
    }
    else if(0==p_session->thread_id)
    {
        if(6 == p_session->session_basic.IPPro)
        {
            tools->study_c2s_by_port(p_session -> session_basic.Server, p_session->session_basic.IPPro, p_session -> session_basic.pPort, p_session -> b_unknown_c2s);
        }
        else if(17 == p_session->session_basic.IPPro)
        {
            tools->study_c2s_by_port(p_session -> session_basic.Server, p_session->session_basic.IPPro, p_session -> session_basic.pPort, p_session -> b_unknown_c2s);
        }
    }
/*
无负债连接的APPID不应该是UNKONOW，新增一个无负载（No_Payload）
TCP会话的应用分为：
-- 仅请求  QueryOnly；客户端发送SYN，服务器无回应
-- 端口关闭 PortClose；客户端发送SYN，服务器发送FIN或RST
-- 无负载 NoPayload；握手正常、挥手正常，没有发送负载数据
-- 未知协议： Unknow；握手正常、挥手正常，有负载数据，协议未知
-- 协议识别成功
-- 其他： Other；非上述5种
UDP会话分为UDP_NoPayload,UDP_Unknown
 */
    if(p_session->session_basic.ProtoSign == 0 )
    {
        uint8_t b_from_server = (p_session -> session_basic.Server == PACKETFROMSERVER);
        if(p_session->session_basic.IPPro == 6)
        {
            if(UnilateraliInfor[0^b_from_server].SYNNum)
            {
                if(p_session->session_basic.pPacketNum[1^b_from_server] == 0)
                {
                    p_session->session_basic.ProtoSign = 10702;
                }
                else if(0 == UnilateraliInfor[1^b_from_server].SYNNum && 0 == p_session->session_basic.pPayloadNum[1^b_from_server] && (UnilateraliInfor[1^b_from_server].RSTNum + UnilateraliInfor[1^b_from_server].FINNum))
                {
                    p_session->session_basic.ProtoSign = 10703;
                }
                else if(UnilateraliInfor[1^b_from_server].SYNNum && 0 ==p_session->session_basic.pPayloadNum[0] && 0 == p_session->session_basic.pPayloadNum[1] )
                {
                    p_session->session_basic.ProtoSign = 10704;
                }
                else if(UnilateraliInfor[1^b_from_server].SYNNum && (p_session->session_basic.pPayloadNum[0] + p_session->session_basic.pPayloadNum[1]) != 0)
                {
                    p_session->session_basic.ProtoSign = 10705;
                }
                else
                {
                    p_session->session_basic.ProtoSign = 10706;
                }
            }
            else
            {
                p_session->session_basic.ProtoSign = 10706;
            }
        }
        else if(p_session->session_basic.IPPro == 17)
        {
            if(0 == p_session->session_basic.pPayloadNum[0] + p_session->session_basic.pPayloadNum[1])
            {
                p_session->session_basic.ProtoSign = 10707;
            }
            else
            {
                p_session->session_basic.ProtoSign = 10708;
            }
        }
    }

    if(p_session->session_basic.ProtoSign == 0)
    {
        if(0 == p_session->session_basic.pPayloadNum[0]&& 0 == p_session->session_basic.pPayloadNum[1])
        {
            p_session->session_basic.ProtoSign = 10701;
        }
        else
        {
            p_session->session_basic.ProtoSign = 10000;
        }
    }
    if(p_session -> session_basic.Server == PACKETFROMCLIENT)
    {
        if(p_session->session_basic.pPacketNum[0] > 1)
        {
            uint32_t seg_num = p_session->session_basic.pPacketNum[0] - 1;
            uint32_t dis_order = seg_num - ipid_offset_list[0][3];
            if(p_session->session_basic.pPacketNum[0] > 8)
            {
                if(ipid_offset_list[0][3] * 2 > seg_num)
                {
                    th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_C_IPID_INCREASE);
                }
                else if(ipid_offset_list[0][3] * 2 < seg_num)
                {
                    th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_C_IPID_RANDOM);
                }
            }
            else if(0 == dis_order)
            {
                th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_C_IPID_INCREASE);
            }
            if(0 == dis_order)
            {
                th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_C_ORDER);
            }
            else if(dis_order * 10 < seg_num)
            {
                th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_C_DISORDER_BIT);
            }
            else if(dis_order * 5 < seg_num)
            {
                th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_C_DISORDER_MID);
            }
            else if(dis_order * 2 < seg_num)
            {
                th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_C_DISORDER_MUCH);
            }
        }
        if(p_session->session_basic.pPacketNum[1] > 1)
        {
            uint32_t seg_num = p_session->session_basic.pPacketNum[1] - 1;
            uint32_t dis_order = seg_num - ipid_offset_list[1][3];
            if(p_session->session_basic.pPacketNum[1] > 8)
            {
                if(ipid_offset_list[1][3] * 2 > seg_num)
                {
                    th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_S_IPID_INCREASE);
                }
                else if(ipid_offset_list[1][3] * 2 < seg_num)
                {
                    th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_S_IPID_RANDOM);
                }
            }
            else if(0 == dis_order)
            {
                th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_S_IPID_INCREASE);
            }
            if(0 == dis_order)
            {
                th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_S_ORDER);
            }
            else if(dis_order * 10 < seg_num)
            {
                th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_S_DISORDER_BIT);
            }
            else if(dis_order * 5 < seg_num)
            {
                th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_S_DISORDER_MID);
            }
            else if(dis_order * 2 < seg_num)
            {
                th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_S_DISORDER_MUCH);
            }
        }
        if(p_session->session_basic.pPacketNum[0] > 10 && p_session->session_basic.pPacketNum[1] > 10 && p_session->session_basic.pPacketBytes[0] > p_session->session_basic.pPacketBytes[1])
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_UP_GE_DOWN);
        }
        if(p_session->session_basic.pPacketNum[1] && pkt_maxlen[1] < 500)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_MAX_PKTLEN_LT500);
        }
        th_engine_tools::instance()->th_set_label(p_session->labels, ttl_label_map_c[UnilateraliInfor[0].TTLS_Max]);
        th_engine_tools::instance()->th_set_label(p_session->labels, ttl_label_map_s[UnilateraliInfor[1].TTLS_Max]);
        if(p_session->session_basic.pPort[0] > 0 && p_session->session_basic.pPort[0] < 49152)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_NOT_OS_C_PORT);
        }
    }
    else
    {
        if(p_session->session_basic.pPacketNum[1] > 1)
        {
            uint32_t seg_num = p_session->session_basic.pPacketNum[1] - 1;
            uint32_t dis_order = seg_num - ipid_offset_list[1][3];
            if(p_session->session_basic.pPacketNum[1] > 8)
            {
                if(ipid_offset_list[1][3] * 2 > seg_num)
                {
                    th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_C_IPID_INCREASE);
                }
                else if(ipid_offset_list[1][3] * 2 < seg_num)
                {
                    th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_C_IPID_RANDOM);
                }
            }
            else if(0 == dis_order)
            {
                th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_C_IPID_INCREASE);
            }
            if(0 == dis_order)
            {
                th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_C_ORDER);
            }
            else if(dis_order * 10 < seg_num)
            {
                th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_C_DISORDER_BIT);
            }
            else if(dis_order * 5 < seg_num)
            {
                th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_C_DISORDER_MID);
            }
            else if(dis_order * 2 < seg_num)
            {
                th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_C_DISORDER_MUCH);
            }
        }
        if(p_session->session_basic.pPacketNum[0] > 1)
        {
            uint32_t seg_num = p_session->session_basic.pPacketNum[0] - 1;
            uint32_t dis_order = seg_num - ipid_offset_list[0][3];
            if(p_session->session_basic.pPacketNum[0] > 8)
            {
                if(ipid_offset_list[0][3] * 2 > seg_num)
                {
                    th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_S_IPID_INCREASE);
                }
                else if(ipid_offset_list[0][3] * 2 < seg_num)
                {
                    th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_S_IPID_RANDOM);
                }
            }
            else if(0 == dis_order)
            {
                th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_S_IPID_INCREASE);
            }
            if(0 == dis_order)
            {
                th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_S_ORDER);
            }
            else if(dis_order * 10 < seg_num)
            {
                th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_S_DISORDER_BIT);
            }
            else if(dis_order * 5 < seg_num)
            {
                th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_S_DISORDER_MID);
            }
            else if(dis_order * 2 < seg_num)
            {
                th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_S_DISORDER_MUCH);
            }
        }
        if(p_session->session_basic.pPacketNum[0] > 10 && p_session->session_basic.pPacketNum[1] > 10 && p_session->session_basic.pPacketBytes[1] > p_session->session_basic.pPacketBytes[0])
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_UP_GE_DOWN);
        }
        if(p_session->session_basic.pPacketNum[0] && pkt_maxlen[0] < 500)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_MAX_PKTLEN_LT500);
        }
        th_engine_tools::instance()->th_set_label(p_session->labels, ttl_label_map_c[UnilateraliInfor[1].TTLS_Max]);
        th_engine_tools::instance()->th_set_label(p_session->labels, ttl_label_map_s[UnilateraliInfor[0].TTLS_Max]);
        if(p_session->session_basic.pPort[1] > 0 && p_session->session_basic.pPort[1] < 49152)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_NOT_OS_C_PORT);
        }
    }
    if(p_session->session_basic.IPPro == 6)
    {
        if(UnilateraliInfor[0].SYNNum && UnilateraliInfor[1].SYNNum && UnilateraliInfor[0].FINNum && UnilateraliInfor[1].FINNum)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_TCP_COMPLETE);
        }
        if(UnilateraliInfor[0].RSTNum || UnilateraliInfor[1].RSTNum)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_TCP_RESET);
        }
        if((UnilateraliInfor[0].SYNNum || UnilateraliInfor[1].SYNNum) && 0 == p_session->session_basic.pPayloadNum[0] && 0 == p_session->session_basic.pPayloadNum[1])
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_PORT_DETECT);
        }
        if(syn_seq_num > 1)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_MULTI_SESSION);
        }
        if(p_session -> session_basic.Server == PACKETFROMCLIENT)
        {
            if(p_session->session_basic.pPacketNum[0] && pkt_maxlen[0] < 1000)
            {
                th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_TCP_PKTLEN_LT1000);
            }
        }
        else
        {
            if(p_session->session_basic.pPacketNum[1] && pkt_maxlen[1] < 1000)
            {
                th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_TCP_PKTLEN_LT1000);
            }
        }
    }
    if(p_session->session_basic.EndTime[0]-p_session->session_basic.StartTime[0] > 3600)
    {
        th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_DUR_GT60);
    }
    else if(p_session->session_basic.EndTime[0]-p_session->session_basic.StartTime[0] > 1800)
    {
        th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_DUR_GT30);
    }
    if(p_session->session_basic.pPacketBytes[0] + p_session->session_basic.pPacketBytes[1] > (1 << 30))
    {
        th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_MBYTE_GE1024);
    }
    else if(p_session->session_basic.pPacketBytes[0] + p_session->session_basic.pPacketBytes[1] > (100 << 20))
    {
        th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_MBYTE_GE100);
    }
    if((p_session->session_basic.IO_Sign[0] & OUTLAND_IP_L2DEFENSE) != (p_session->session_basic.IO_Sign[1] & OUTLAND_IP_L2DEFENSE))
    {
        th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_CROSS_BORDER);
    }
    if(p_session -> session_basic.Server == PACKETFROMCLIENT)
    {
        int server_port_type = p_portjudge->judge_unofficial(p_session->session_basic.IPPro, p_session->session_basic.ProtoSign, p_session->session_basic.pPort[1]);
        if(1 == server_port_type)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_PORT_NO_MATCH_PRO);
        }
        else if(2 == server_port_type)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_UNCOMMON_PORT);
        }
    }
    else
    {
        int server_port_type = p_portjudge->judge_unofficial(p_session->session_basic.IPPro, p_session->session_basic.ProtoSign, p_session->session_basic.pPort[0]);
        if(1 == server_port_type)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_PORT_NO_MATCH_PRO);
        }
        else if(2 == server_port_type)
        {
            th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_UNCOMMON_PORT);
        }
    }
    if((0 == p_session->session_basic.pPacketNum[0] || 0 == p_session->session_basic.pPacketNum[1]) && (p_session->session_basic.pPayloadNum[0] || p_session->session_basic.pPayloadNum[1]))
    {
        th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_ONE_SIDE_FLOW);
    }
    if((UnilateraliInfor[0].TTLS_Max-UnilateraliInfor[0].TTLS_Min > 8) || (UnilateraliInfor[1].TTLS_Max-UnilateraliInfor[1].TTLS_Min > 8))
    {
        th_engine_tools::instance()->th_set_label(p_session->labels, LABEL_TTL_DIFF_GE8);
    }
    if(should_log)
    {
        JKNmsg *p_msg = p_session->p_value -> get();
        if(NULL==p_msg)
        {
            return;
        }
        CJsonMsg *p_json_msg = p_session->p_value -> jget();
        p_msg ->set_type(30);
        single_session_msg* p_single_session_msg = p_msg-> mutable_single_session();
        Comm_msg* p_comm = p_single_session_msg -> mutable_comm_msg();
        ss_basic_msg* p_ss_basic = p_single_session_msg -> mutable_ss_basic();
        ss_stats_msg* p_ss_stats = p_single_session_msg -> mutable_ss_stats();
        ss_pkt_msg* p_ss_pkt = p_single_session_msg -> mutable_ss_pkt();
        if(p_session -> session_basic.Server == PACKETFROMCLIENT)
        {
            if(p_session->b_has_proxy)
            {
                p_single_session_msg ->set_proxy_ip(p_session->session_basic.pIP[1].ip_str());
                p_single_session_msg ->set_proxy_port(p_session->session_basic.pPort[1]);
            }
        }
        else
        {
            if(p_session->b_has_proxy)
            {
                p_single_session_msg ->set_proxy_ip(p_session->session_basic.pIP[0].ip_str());
                p_single_session_msg ->set_proxy_port(p_session->session_basic.pPort[0]);
            }
        }
        p_single_session_msg ->set_end_time(p_session->session_basic.EndTime[0]);
        p_single_session_msg ->set_end_nsec(p_session->session_basic.EndTime[1]);
        p_single_session_msg ->set_handle_begin_time(p_session->StartClock);
        p_single_session_msg ->set_handle_end_time(p_session->EndClock);
        p_single_session_msg ->set_first_sender(p_session->session_basic.pIP[0].ip_str());
        p_single_session_msg ->set_duration(p_session->session_basic.EndTime[0] - p_session->session_basic.StartTime[0]);
        if(p_session->session_basic.RuleNum + p_session->packet_sign_num)
        {
            std::set<uint32_t> sign_set;
            for(uint32_t i = 0; i < p_session->session_basic.RuleNum; i++)
            {
                sign_set.insert(p_session->session_basic.pRule[i]);
            }
            for(uint8_t i = 0; i < p_session->packet_sign_num; i++)
            {
                sign_set.insert(p_session->packet_sign[i]);
            }
            for(std::set<uint32_t> ::iterator it = sign_set.begin(); it != sign_set.end(); it ++)
            {
                p_single_session_msg -> add_rule_labels(*it);
            }
            sign_set.clear();
        }
        for(uint16_t i = 0; i < (LABEL_END+7)/8; i ++)
        {
            if(p_session->labels[i])
            {
                uint16_t max = (i + 1) * 8;
                for(uint16_t j = i * 8; j < max; j ++)
                {
                    if(th_engine_tools::instance()->th_get_label(p_session->labels, j))
                    {
                        p_single_session_msg -> add_rule_labels((uint32_t)j+LABEL_START);
                    }
                }
            }
        }
        p_single_session_msg -> set_device_id(th_engine_tools::instance()->device_id);
        p_single_session_msg -> set_first_proto(p_session->first_proto);

        for(uint16_t portid = 0 ; portid < 16; portid++)
        {
            if(p_session->port_map & ((uint16_t)1 << portid))
            {
                p_single_session_msg->add_port_list(portid);
            }
        }
        for(int i = 0; i < p_session->http.size(); i ++)
        {
            single_http *phttp = p_single_session_msg->add_http();
            phttp->set_url(p_session->http[i].url);
            phttp->set_act(p_session->http[i].act);
            phttp->set_host(p_session->http[i].host);
            phttp->set_response(p_session->http[i].response);
            phttp->set_user_agent(p_session->http[i].user_agent);
        }
        for(int i = 0; i < p_session->dns.size(); i ++)
        {
            single_dns *pdns = p_single_session_msg->add_dns();
            pdns->set_domain(p_session->dns.m_vector[i]->domain);
            pdns->set_domain_ip(p_session->dns.m_vector[i]->domain_ip);
            for(int j = 0; j < p_session->dns.m_vector[i]->answer.size(); j ++)
            {
                single_dns_answer *panswer = pdns->add_answer();
                panswer->set_name(p_session->dns.m_vector[i]->answer[j].name);
                panswer->set_value(p_session->dns.m_vector[i]->answer[j].value);
            }
        }
        for(int i = 0; i < p_session->ssl.size(); i ++)
        {
            single_ssl *pssl = p_single_session_msg->add_ssl();
            pssl->set_ch_ciphersuit(p_session->ssl[i].ch_ciphersuit);
            pssl->set_ch_ciphersuit_num(p_session->ssl[i].ch_ciphersuit_num);
            pssl->set_ch_server_name(p_session->ssl[i].ch_server_name);
            pssl->set_ch_alpn(p_session->ssl[i].ch_alpn);
            pssl->set_c_cert(p_session->ssl[i].c_cert);
            pssl->set_c_cert_num(p_session->ssl[i].c_cert_num);
            pssl->set_s_cert(p_session->ssl[i].s_cert);
            pssl->set_s_cert_num(p_session->ssl[i].s_cert_num);
        }
        // if(p_session->session_basic.IPPro == 6 )
        // {
        //     if(
        //              (UnilateraliInfor[0].FINNum!=0) || (UnilateraliInfor[1].FINNum!=0)
        //             ||  (UnilateraliInfor[0].RSTNum!=0)||  (UnilateraliInfor[1].RSTNum!=0))
        //     {
        //         p_comm->set_state("Complete");// ////连接状态: Complete 正常结束；  TimeOut 超时结束； Byforce 强制结束
        //     }
        //     else
        //     {
        //         p_comm->set_state("TimeOut");// ////连接状态: Complete 正常结束；  TimeOut 超时结束； Byforce 强制结束
        //     }
        // }
        // else 
        // {
        //     p_comm->set_state("Complete");// ////连接状态: Complete 正常结束；  TimeOut 超时结束； Byforce 强制结束
        // }
        if(6 == p_session->session_basic.IPPro)
        {
            unsigned char aes_buf[16] = {0};
            if(tcp_finger_bitmap & 1)
            {
                uint64_t tcp_c_finger = 0;
                if(0 == AES_Hash(aes_hash_ctx, (unsigned char *)&tcp_finger_feature[0], sizeof(s_tcp_finger_feature), aes_buf))
                {
                    tcp_c_finger = *(uint64_t *)aes_buf;
                    tcp_c_finger &= (uint64_t)0x7fffffffffffffff;
                    p_single_session_msg ->set_tcp_c_finger(tcp_c_finger);
                    tcp_finger_feature_msg *p_c_feature = p_single_session_msg->mutable_tcp_c_finger_feature();
                    p_c_feature->set_ecn_ip_ect(tcp_finger_feature[0].ecn_ip_ect);
                    p_c_feature->set_qk_dfnz_ipid(tcp_finger_feature[0].qk_dfnz_ipid);
                    p_c_feature->set_flag_cwr(tcp_finger_feature[0].flag_CWR);
                    p_c_feature->set_flag_ece(tcp_finger_feature[0].flag_ECE);
                    p_c_feature->set_qk_opt_zero_ts1(tcp_finger_feature[0].qk_opt_zero_ts1);
                    p_c_feature->set_ttl(tcp_finger_feature[0].ttl);
                    p_c_feature->set_tcpopt_eol_padnum(tcp_finger_feature[0].tcpopt_eol_padnum);
                    p_c_feature->set_tcpopt_wscale(tcp_finger_feature[0].tcpopt_wscale);
                    p_c_feature->set_qk_win_mss(tcp_finger_feature[0].qk_win_mss);
                    p_c_feature->set_tcpopt_layout((const char *)tcp_finger_feature[0].tcpopt_layout);
                }
            }
            if(tcp_finger_bitmap & 2)
            {
                uint64_t  tcp_s_finger = 0 ;
                if(0 == AES_Hash(aes_hash_ctx, (unsigned char *)&tcp_finger_feature[1], sizeof(s_tcp_finger_feature), aes_buf))
                {
                    tcp_s_finger = *(uint64_t *)aes_buf;
                    tcp_s_finger &= (uint64_t)0x7fffffffffffffff;
                    p_single_session_msg ->set_tcp_s_finger(tcp_s_finger);
                    tcp_finger_feature_msg *p_s_feature = p_single_session_msg->mutable_tcp_s_finger_feature();
                    p_s_feature->set_ecn_ip_ect(tcp_finger_feature[1].ecn_ip_ect);
                    p_s_feature->set_qk_dfnz_ipid(tcp_finger_feature[1].qk_dfnz_ipid);
                    p_s_feature->set_flag_cwr(tcp_finger_feature[1].flag_CWR);
                    p_s_feature->set_flag_ece(tcp_finger_feature[1].flag_ECE);
                    p_s_feature->set_qk_opt_zero_ts1(tcp_finger_feature[1].qk_opt_zero_ts1);
                    p_s_feature->set_ttl(tcp_finger_feature[1].ttl);
                    p_s_feature->set_tcpopt_eol_padnum(tcp_finger_feature[1].tcpopt_eol_padnum);
                    p_s_feature->set_tcpopt_wscale(tcp_finger_feature[1].tcpopt_wscale);
                    p_s_feature->set_qk_win_mss(tcp_finger_feature[1].qk_win_mss);
                    p_s_feature->set_tcpopt_layout((const char *)tcp_finger_feature[1].tcpopt_layout);
                }
            }
        }
        if(10637 == p_session->session_basic.ProtoSign && p_session->p_session_ext) //HTTP
        {
            if(p_session->p_session_ext->app_c_finger)
            {
                p_single_session_msg ->set_http_c_finger(p_session->p_session_ext->app_c_finger);
            }
            if(p_session->p_session_ext->app_s_finger)
            {
                p_single_session_msg ->set_http_s_finger(p_session->p_session_ext->app_s_finger);
            }
        }
        else if(10638 == p_session->session_basic.ProtoSign || 10639 == p_session->session_basic.ProtoSign) //SSL
        {
            if(p_session->p_session_ext->app_c_finger)
            {
                p_single_session_msg ->set_ssl_c_finger(p_session->p_session_ext->app_c_finger);
            }
            if(p_session->p_session_ext->app_s_finger)
            {
                p_single_session_msg ->set_ssl_s_finger(p_session->p_session_ext->app_s_finger);
            }
        }
        if(p_session->b_has_mac)
        {
            if(p_session -> session_basic.Server == PACKETFROMCLIENT)
            {
                char tmp[18] = {0};
                macToStr((unsigned char *)&(p_session ->srcmac), tmp , 18);
                p_ss_basic->set_smac(string(tmp));
                macToStr((unsigned char *)&(p_session ->dstmac), tmp , 18);
                p_ss_basic->set_dmac(string(tmp));
            }
            else 
            {
                char tmp[18] = {0};
                macToStr((unsigned char *)&(p_session ->dstmac), tmp , 18);
                p_ss_basic->set_smac(string(tmp));
                macToStr((unsigned char *)&(p_session ->srcmac), tmp , 18);
                p_ss_basic->set_dmac(string(tmp));
            }
        }
        send1(p_session , p_single_session_msg , p_comm  , p_ss_basic , p_ss_stats , p_ss_pkt , p_session_statistics, p_th_tools);
        if(p_json_msg)
        {
            position_info src_pos = p_ip_pos->work(p_comm->src_ip(), p_session->thread_id);
            position_info dst_pos = p_ip_pos->work(p_comm->dst_ip(), p_session->thread_id);
            uint64_t total_bytes = p_ss_pkt->pkt_sbytes() + p_ss_pkt->pkt_dbytes();
            int size = snprintf((char *)p_json_msg->get_buf(), p_json_msg->get_buf_len(),
"{\
\"AppId\":%u,\"Duration\":%u,\"EndTime\":%u,\"HandleBeginTime\":%u,\"HandleEndTime\":%u,\
\"Labels\":%s,\"IPPro\":%u,\"SessionId\":\"%s\",\"StartTime\":%u,\"ThreadId\":%u,\
\"TotalBytes\":%"PRIu64",\"dHTTPFinger\":\"%"PRIu64"\",\"dIp\":\"%s\",\"dMac\":\"%s\",\"dPort\":%u,\
\"dSSLFinger\":\"%"PRIu64"\",\"dTCPFinger\":\"%"PRIu64"\",\"ip2ip\":\"%s\",\"pkt\":{\"dBytes\":%"PRIu64",\"dNum\":%u,\
\"sBytes\":%"PRIu64",\"sNum\":%u},\"sHTTPFinger\":\"%"PRIu64"\",\"sIp\":\"%s\",\"sMac\":\"%s\",\
\"sPort\":%u,\"sSSLFinger\":\"%"PRIu64"\",\"sTCPFinger\":\"%"PRIu64"\",\"sip_appid_dport_dip\":\"%s\",\"type\":3009,\
\"sIpCountry\":\"%s\",\"sIpSubdivisions\":\"%s\",\"sIpCity\":\"%s\",\"sIpLatitude\":\"%f\",\"sIpLongitude\":\"%f\",\
\"dIpCountry\":\"%s\",\"dIpSubdivisions\":\"%s\",\"dIpCity\":\"%s\",\"dIpLatitude\":\"%f\",\"dIpLongitude\":\"%f\",\
\"FirstSender\":\"%s\",\"finger_list\":[%s],\"DNS\":[%s],\"HTTP\":[%s],\"SSL\":[%s]\
}",
                p_comm->app_id(),p_single_session_msg->duration(),p_single_session_msg->end_time(),p_single_session_msg->handle_begin_time(),p_single_session_msg->handle_end_time(),
                labels_2_string(p_single_session_msg).c_str(),p_comm->ippro(),p_comm->session_id().c_str(),p_comm->begin_time(),p_comm->thread_id(),
                total_bytes,p_single_session_msg->http_s_finger(),p_comm->dst_ip().c_str(),p_ss_basic->dmac().c_str(),p_comm->dst_port(),
                p_single_session_msg->ssl_s_finger(),p_single_session_msg->tcp_s_finger(),((p_comm->src_ip()<=p_comm->dst_ip())?(p_comm->src_ip()+"_"+p_comm->dst_ip()):(p_comm->dst_ip()+"_"+p_comm->src_ip())).c_str(),p_ss_pkt->pkt_dbytes(),p_ss_pkt->pkt_dnum(),
                p_ss_pkt->pkt_sbytes(),p_ss_pkt->pkt_snum(),p_single_session_msg->http_c_finger(),p_comm->src_ip().c_str(),p_ss_basic->smac().c_str(),
                p_comm->src_port(),p_single_session_msg->ssl_c_finger(),p_single_session_msg->tcp_c_finger(),(p_comm->src_ip()+"_"+std::to_string(p_comm->app_id())+"_"+std::to_string(p_comm->dst_port())+"_"+p_comm->dst_ip()).c_str(),
                src_pos.country.c_str(),src_pos.province.c_str(),src_pos.city.c_str(),src_pos.latitude,src_pos.longitude,
                dst_pos.country.c_str(),dst_pos.province.c_str(),dst_pos.city.c_str(),dst_pos.latitude,dst_pos.longitude,
                (PACKETFROMSERVER==p_session->session_basic.Server)?p_comm->dst_ip().c_str():p_comm->src_ip().c_str(),finger_list_str(p_single_session_msg).c_str(),session_pub_dns_serialize(p_session->dns).c_str(),session_pub_http_serialize(p_session->http).c_str(),session_pub_ssl_serialize(p_session->ssl).c_str()
            );
            p_json_msg->set_offset(size);
        }
    }
}
void single_session_session::send1 (session_pub * p_session ,single_session_msg* p_single_session_msg , Comm_msg* p_comm  , ss_basic_msg* p_ss_basic  , ss_stats_msg* p_ss_stats ,ss_pkt_msg* p_ss_pkt  , CInterface_Statistics* p_session_statistics , void *p_th_tools)
{
    rule_msg * p_rule = p_ss_basic->mutable_rule();
    p_rule -> set_level( p_session->session_basic.RuleLevel);
    p_rule -> set_connect_rule_num(p_session->session_basic.RuleNum);
    for(int  i = 0 ; i < p_session -> session_basic.RuleNum  ; i++)
    {
        p_rule->add_connect_rule(p_session -> session_basic.pRule[i]);
    }

    p_ss_basic->set_rule_num(p_session->session_basic.RuleNum);
    p_ss_basic->set_rule_level(p_session->session_basic.RuleLevel);
    p_ss_basic->set_syn(pP0F[0],POFBytes[0]);
    p_ss_basic->set_syn_ack(pP0F[1],POFBytes[1]);

    if(p_session -> session_basic.Server == PACKETFROMCLIENT)
    {
        p_ss_stats ->set_sio_sign(p_session ->session_basic.IO_Sign[0]);
        p_ss_stats ->set_dio_sign(p_session ->session_basic.IO_Sign[1]);
        p_ss_stats->set_stats_stotalsign(totalsign[0]);
        p_ss_stats->set_stats_dtotalsign(totalsign[1]);
        for(int i = 0 ; i < 8; i++)
        {
            p_ss_stats ->add_stats_sdistlen(distLen[0][i]);
            p_ss_stats ->add_stats_ddistlen(distLen[1][i]);
        }
        for(int i = 0; i < 6; i++)
        {
            p_ss_stats ->add_stats_sipid_offset(ipid_offset_list[0][i]);
            p_ss_stats ->add_stats_dipid_offset(ipid_offset_list[1][i]);
        }
        p_ss_stats->set_stats_src_mss(mss[0]);
        p_ss_stats->set_stats_dst_mss(mss[1]);
        p_ss_stats->set_stats_src_window_scale(window_scale[0]);
        p_ss_stats->set_stats_dst_window_scale(window_scale[1]);
        p_ss_stats->set_stats_spayload_maxlen(payload_max_len[0]);
        p_ss_stats->set_stats_dpayload_maxlen(payload_max_len[1]);
        p_ss_stats->set_stats_sack_payload_maxlen(ack_payload_max_len[0]);
        p_ss_stats->set_stats_dack_payload_maxlen(ack_payload_max_len[1]);
        p_ss_stats->set_stats_sack_payload_minlen(ack_payload_min_len[0]);
        p_ss_stats->set_stats_dack_payload_minlen(ack_payload_min_len[1]);

        p_ss_pkt->set_pkt_smaxlen(pkt_maxlen[0]);
        p_ss_pkt->set_pkt_dmaxlen(pkt_maxlen[1]);
        p_ss_pkt->set_pkt_snum(p_session->session_basic.pPacketNum[0]);
        p_ss_pkt->set_pkt_dnum(p_session->session_basic.pPacketNum[1]);
        p_ss_pkt->set_pkt_spayloadnum(p_session->session_basic.pPayloadNum[0]);
        p_ss_pkt->set_pkt_dpayloadnum(p_session->session_basic.pPayloadNum[1]);
        p_ss_pkt->set_pkt_sbytes(p_session->session_basic.pPacketBytes[0]);
        p_ss_pkt->set_pkt_dbytes(p_session->session_basic.pPacketBytes[1]);
        p_ss_pkt->set_pkt_spayloadbytes(p_session->pkt_payloadbytes[0]);
        p_ss_pkt->set_pkt_dpayloadbytes(p_session->pkt_payloadbytes[1]);
        p_ss_pkt->set_pkt_spshnum(pkt_psh_num[0]);
        p_ss_pkt->set_pkt_dpshnum(pkt_psh_num[1]);
        p_ss_pkt->set_pkt_sbadnum(pkt_bad_num[0]);
        p_ss_pkt->set_pkt_dbadnum(pkt_bad_num[1]);

        p_ss_pkt->set_pkt_sfinnum(UnilateraliInfor[0].FINNum);
        p_ss_pkt->set_pkt_dfinnum(UnilateraliInfor[1].FINNum);
        p_ss_pkt->set_pkt_srstnum(UnilateraliInfor[0].RSTNum);
        p_ss_pkt->set_pkt_drstnum(UnilateraliInfor[1].RSTNum);
        p_ss_pkt->set_pkt_ssynnum(UnilateraliInfor[0].SYNNum);
        p_ss_pkt->set_pkt_dsynnum(UnilateraliInfor[1].SYNNum);
        p_ss_pkt->set_pkt_ssynbytes(UnilateraliInfor[0].SYNPacketBytes);
        p_ss_pkt->set_pkt_dsynbytes(UnilateraliInfor[1].SYNPacketBytes);
        p_ss_pkt->set_pkt_sttlmax(UnilateraliInfor[0].TTLS_Max);
        p_ss_pkt->set_pkt_dttlmax(UnilateraliInfor[1].TTLS_Max);
        p_ss_pkt->set_pkt_sttlmin(UnilateraliInfor[0].TTLS_Min);
        p_ss_pkt->set_pkt_dttlmin(UnilateraliInfor[1].TTLS_Min);

        if(p_session->p_session_ext && p_session->p_session_ext->tcp_link[0] && p_session->p_session_ext->tcp_link[1])
        {
            p_ss_pkt->set_pkt_sdisorder(p_session->p_session_ext->tcp_link[0]->get_summary()->mis_order);
            p_ss_pkt->set_pkt_ddisorder(p_session->p_session_ext->tcp_link[1]->get_summary()->mis_order);
            p_ss_pkt->set_pkt_sresend(p_session->p_session_ext->tcp_link[0]->get_summary()->re_send);
            p_ss_pkt->set_pkt_dresend(p_session->p_session_ext->tcp_link[1]->get_summary()->re_send);
            p_ss_pkt->set_pkt_slost(p_session->p_session_ext->tcp_link[0]->get_lose_len());
            p_ss_pkt->set_pkt_dlost(p_session->p_session_ext->tcp_link[1]->get_lose_len());
        }
        for(uint8_t i = 0; i < record_payload_num[0]; i ++)
        {
            p_ss_pkt->add_pkt_spayload(PayInfo[0][i].Str());
        }
        for(uint8_t i = 0; i < record_payload_num[1]; i ++)
        {
            p_ss_pkt->add_pkt_dpayload(PayInfo[1][i].Str());
        }
    }
    else
    {
        p_ss_stats ->set_sio_sign(p_session ->session_basic.IO_Sign[1]);
        p_ss_stats ->set_dio_sign(p_session ->session_basic.IO_Sign[0]);
        p_ss_stats->set_stats_stotalsign(totalsign[1]);
        p_ss_stats->set_stats_dtotalsign(totalsign[0]);  //消息中d端代表server
        for(int i = 0 ; i < 8; i++)
        {
            p_ss_stats ->add_stats_sdistlen(distLen[1][i]);
            p_ss_stats ->add_stats_ddistlen(distLen[0][i]);
        }
        for(int i = 0; i < 6; i++)
        {
            p_ss_stats ->add_stats_sipid_offset(ipid_offset_list[1][i]);
            p_ss_stats ->add_stats_dipid_offset(ipid_offset_list[0][i]);
        }
        p_ss_stats->set_stats_src_mss(mss[1]);
        p_ss_stats->set_stats_dst_mss(mss[0]);
        p_ss_stats->set_stats_src_window_scale(window_scale[1]);
        p_ss_stats->set_stats_dst_window_scale(window_scale[0]);
        p_ss_stats->set_stats_spayload_maxlen(payload_max_len[1]);
        p_ss_stats->set_stats_dpayload_maxlen(payload_max_len[0]);
        p_ss_stats->set_stats_sack_payload_maxlen(ack_payload_max_len[1]);
        p_ss_stats->set_stats_dack_payload_maxlen(ack_payload_max_len[0]);
        p_ss_stats->set_stats_sack_payload_minlen(ack_payload_min_len[1]);
        p_ss_stats->set_stats_dack_payload_minlen(ack_payload_min_len[0]);

        p_ss_pkt->set_pkt_smaxlen(pkt_maxlen[1]);
        p_ss_pkt->set_pkt_dmaxlen(pkt_maxlen[0]);
        p_ss_pkt->set_pkt_snum(p_session->session_basic.pPacketNum[1]);
        p_ss_pkt->set_pkt_dnum(p_session->session_basic.pPacketNum[0]);
        p_ss_pkt->set_pkt_spayloadnum(p_session->session_basic.pPayloadNum[1]);
        p_ss_pkt->set_pkt_dpayloadnum(p_session->session_basic.pPayloadNum[0]);
        p_ss_pkt->set_pkt_sbytes(p_session->session_basic.pPacketBytes[1]);
        p_ss_pkt->set_pkt_dbytes(p_session->session_basic.pPacketBytes[0]);
        p_ss_pkt->set_pkt_spayloadbytes(p_session->pkt_payloadbytes[1]);
        p_ss_pkt->set_pkt_dpayloadbytes(p_session->pkt_payloadbytes[0]);
        p_ss_pkt->set_pkt_spshnum(pkt_psh_num[1]);
        p_ss_pkt->set_pkt_dpshnum(pkt_psh_num[0]);
        p_ss_pkt->set_pkt_sbadnum(pkt_bad_num[1]);
        p_ss_pkt->set_pkt_dbadnum(pkt_bad_num[0]);

        p_ss_pkt->set_pkt_sfinnum(UnilateraliInfor[1].FINNum);
        p_ss_pkt->set_pkt_dfinnum(UnilateraliInfor[0].FINNum);
        p_ss_pkt->set_pkt_srstnum(UnilateraliInfor[1].RSTNum);
        p_ss_pkt->set_pkt_drstnum(UnilateraliInfor[0].RSTNum);
        p_ss_pkt->set_pkt_ssynnum(UnilateraliInfor[1].SYNNum);
        p_ss_pkt->set_pkt_dsynnum(UnilateraliInfor[0].SYNNum);
        p_ss_pkt->set_pkt_ssynbytes(UnilateraliInfor[1].SYNPacketBytes);
        p_ss_pkt->set_pkt_dsynbytes(UnilateraliInfor[0].SYNPacketBytes);
        p_ss_pkt->set_pkt_sttlmax(UnilateraliInfor[1].TTLS_Max);
        p_ss_pkt->set_pkt_dttlmax(UnilateraliInfor[0].TTLS_Max);
        p_ss_pkt->set_pkt_sttlmin(UnilateraliInfor[1].TTLS_Min);
        p_ss_pkt->set_pkt_dttlmin(UnilateraliInfor[0].TTLS_Min);
        
        if(p_session->p_session_ext && p_session->p_session_ext->tcp_link[0] && p_session->p_session_ext->tcp_link[1])
        {
            p_ss_pkt->set_pkt_sdisorder(p_session->p_session_ext->tcp_link[1]->get_summary()->mis_order);
            p_ss_pkt->set_pkt_ddisorder(p_session->p_session_ext->tcp_link[0]->get_summary()->mis_order);
            p_ss_pkt->set_pkt_sresend(p_session->p_session_ext->tcp_link[1]->get_summary()->re_send);
            p_ss_pkt->set_pkt_dresend(p_session->p_session_ext->tcp_link[0]->get_summary()->re_send);
            p_ss_pkt->set_pkt_slost(p_session->p_session_ext->tcp_link[1]->get_lose_len());
            p_ss_pkt->set_pkt_dlost(p_session->p_session_ext->tcp_link[0]->get_lose_len());
        }
        for(uint8_t i = 0; i < record_payload_num[0]; i ++)
        {
            p_ss_pkt->add_pkt_dpayload(PayInfo[0][i].Str());
        }
        for(uint8_t i = 0; i < record_payload_num[1]; i ++)
        {
            p_ss_pkt->add_pkt_spayload(PayInfo[1][i].Str());
        }
    }
    p_ss_pkt->set_app_pkt_id(p_session->app_pkt_id);
    for(int i = 0; i < 8 ; i ++)
    {
        p_ss_stats ->add_stats_distdur(duration[i]);
    }
    for(uint8_t i = 0; i < syn_seq_num && i < MAX_SEQ_RECORD; i ++)
    {
        p_ss_stats ->add_syn_seq(syn_seq_list[i]);
    }
    p_ss_stats ->set_syn_seq_num(syn_seq_num);
    p_ss_stats ->set_ext_json(p_session -> session_basic.ExtString);

    CMD_TCP *p_md_tcp = ((CMD_TCP *)p_session-> p_md_tcp);
    if(p_md_tcp)
    {
        for (int i = 0; i < p_md_tcp->m_InforNum; i ++)
        {
            md_tcp_msg* md_tcp = p_ss_stats->add_stats_tcp_info();
            md_tcp->set_bytes(p_session -> session_basic.Server == PACKETFROMCLIENT ? (p_md_tcp->m_pInfor[i].Bytes):(-p_md_tcp->m_pInfor[i].Bytes));
            md_tcp->set_packet_num(p_md_tcp->m_pInfor[i].PacketNum);
            md_tcp->set_psh_num(p_md_tcp->m_pInfor[i].PSHNum);
            md_tcp->set_acknowledgement(p_md_tcp->m_pInfor[i].temp.Acknowledgement);
            md_tcp->set_min_sequence(p_md_tcp->m_pInfor[i].temp.MinSequence);
            md_tcp->set_max_sequence(p_md_tcp->m_pInfor[i].temp.MaxSequence);
        }
    }

    p_ss_stats->set_stats_prolist_num(max_prolist_num);
    if(p_session_statistics)
    {
        Json::FastWriter fast_writer;
        string ser_json = fast_writer.write(*p_session_statistics->StackInfor);
        ser_json.pop_back();
        p_ss_stats->set_stats_prolist(ser_json);
    }

    p_ss_pkt->set_pkt_pronum(pkt_pronum);
    p_ss_pkt->set_pkt_unkonw_pronum(pkt_unkonw_pronum);
    p_ss_pkt->set_pkt_syn_data(pkt_syn_data);
    //packet 信息 
    //Json::Value Packet_info;
    
    uint32_t num = p_session -> session_basic.pPayloadNum[0] + p_session -> session_basic.pPayloadNum[1];
    for(uint32_t i = 0 ; i < num && i < MAXPACKETPLNUM; i++) 
    {
        PInfo[i].Str(p_ss_pkt, (p_session->session_basic.Server == PACKETFROMCLIENT));
    }
    if(10638 == p_session->session_basic.ProtoSign) //SSL
    {
        for(int i = 0; i < 8; i ++)
        {
            p_ss_stats->add_block_cipher(p_session->tls_app_len[i]);
        }
    }
    /*
    if(p_session -> ptrGetPacketInfoT != NULL)
    {
        p_session -> ptrGetPacketInfoT(p_session , &Packet_info);
    }*/
    //p_ss_pkt->set_pkt_infor(Packet_info.toStyledString());
   //p_ss_pkt->set_pkt_infor(tmp);
   commsg_fill(p_session , p_comm, "", p_th_tools);
}

//string single_session_session::macToStr(uint64_t ulMac)

double single_session_session::ChiSquareDistrobution(uint32_t* IN_Array, double &OUT_Kx)
{
    double p,q,t,temp;
    double Ave=0,Square=0;
    for( int i=0;i<256;i++ )
    {
        Ave+=IN_Array[i];
    }
    Ave=Ave/256;
    for( int i=0;i<256;i++ )
    {
        p=IN_Array[i]-Ave;
        Square+=p*p;
    }
    OUT_Kx=Square/Ave;
    static const double Value=22.561028;
    t=sqrt(2*OUT_Kx )-Value;
    return t;
}

// Last Update:2019-04-28 19:14:43
/**
 * @file single_session_sesson.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-1-4
 */

#ifndef SINGLE_SESSION_SESSION_H
#define SINGLE_SESSION_SESSION_H
#include <resources_marge.h>
#include <json/json.h>
#include <stdint.h>
#include <session_pub.h>
#include <commit_tools.h>
#include <Interface_Statistics.h>
#include <MD_TCP.h>
#include "official_portmap.h"
#include "ip_position.h"
using namespace std;

#define MAX_SEQ_RECORD 3

#define J_min(a,b) (((a)<(b))?(a):(b))
void  init_len_map();
void  init_time_map();
void init_offset_map();
void init_ttl_map();
extern official_portmap *p_portjudge;
extern data_ip_position *p_ip_pos;
////单边信息：
//typedef struct _STR_UNILATERALINFOR_V2
//{
//    unsigned char TTLS_Max;
//    unsigned char TTLS_Min;
//
//    WORD SYNNum;//Tcp SYN 标识出现次数
//    WORD RSTNum;//Tcp RST 标识出现次数
//    WORD FINNum;//Tcp fin 标识出现次数
//    DWORD SYNPacketBytes;
//
//    //该链接所有包的 STR_PROTOCOLSTACK::TotalSign 的或,按方向进行汇聚
//    DWORD TotalSign;
//
//    //带负载的包数
//    UINT64 TotalPacketNum_Payload;
//    //负载总字节数--20190228
//    UINT64 TotalPayloadBytes;
//    //包数
//    UINT64 TotalPacketNum;
//    //包字节数
//    UINT64 TotalPacketBytes;
//    //最大包长--IP包的包长
//    DWORD MaxPacketLen;
//
//}STR_UNILATERALINFOR_V2;

#define MAXPACKETINFONUM 4
#define MAXPACKETPLNUM 50
#define PAYLOAD_SIZE_RECORD 32

//fin包信息
class basic_pro 
{
    public:
        uint32_t FinNum[2];  // fin包格式
        uint32_t MaxPacketLen[2]; // 最大包长 
        uint32_t RSTNum[2]; // RST报数
        uint32_t SYNNum[2];  // SYN报数
        uint32_t TTL_Max[2]; // TTL 最大数 
        uint32_t TTL_Min[2]; // 最小TTL 
        uint32_t TotalSign[2]; // 连接各包标志的或:
        void AddJson1(Json::Value & jsonl , int m1 , int m2 ,std::string name);
        std::string base_info_jsonSerializeToString();
        void init();
        void Handle(c_packet * p_packet);
};
class packet_info
{
    public:
        uint32_t Sec ; //包的ms 
        uint32_t nSec; // 纳秒
        int len ; // 该包负载长度，正数表示源IP发送，负数表示目的IP发送
        uint8_t count;//包编号
        uint8_t Directory;
        void handle(c_packet * p_packet,uint32_t num , uint32_t PayloadNum);
        void Str(ss_pkt_msg *p_pkt_msg, int pkt_from_client);
};

class payload_info
{
    public:
        uint8_t payload[PAYLOAD_SIZE_RECORD];
        uint8_t len;
        void handle(c_packet * p_packet)
        {
            if(p_packet -> app_len > PAYLOAD_SIZE_RECORD)
            {
                len = PAYLOAD_SIZE_RECORD;
            }
            else
            {
                len = (uint8_t)p_packet -> app_len;
            }
            memcpy(&payload[0], p_packet->app_buf, len);
        }
        void clear()
        {
            len = 0;
        }
        string Str();
};


typedef struct
{
    uint8_t ecn_ip_ect;
    uint8_t ttl;
    uint8_t qk_dfnz_ipid;
    uint8_t flag_CWR;
    uint8_t flag_ECE;
    uint8_t tcpopt_wscale;
    uint8_t qk_opt_zero_ts1;
    uint8_t tcpopt_layout[40];
    uint16_t tcpopt_eol_padnum;
    uint16_t qk_win_mss;
}s_tcp_finger_feature;

class single_session_session
{
    public :
        payload_info PayInfo[2][MAXPACKETINFONUM];
        packet_info PInfo[MAXPACKETPLNUM];
        //uint32_t DIST[256];
        uint32_t IpPro ; // 4 ipv4 6 ipv6
        uint32_t totalsign[2]; //所有packet->TotalSign位与得到
        uint32_t distLen[2][8];//数组空间固定为8、16、32、64、128、256、512、1024
        uint32_t duration[8];
        uint32_t ts_last[2];
        uint32_t max_prolist_num;
        uint32_t pkt_maxlen[2];
        uint16_t mss[2]; //tcp option maximum segment size
        uint16_t window_scale[2]; //tcp option window scale
        uint16_t payload_max_len[2];
        uint16_t ack_payload_max_len[2];
        uint16_t ack_payload_min_len[2];
        uint32_t pkt_psh_num[2];
        uint32_t pkt_pronum;
        uint32_t pkt_unkonw_pronum;
        uint32_t pkt_syn_data;
        uint32_t syn_seq_list[MAX_SEQ_RECORD];
        uint32_t ipid_offset_list[2][6];
        int ipid_last[2];
        uint32_t pkt_bad_num[2];    //错包统计
        /****************************************from Interface_Statistics**************************************/
        //规则匹配信息
        //TR_CONNECTINFOR_RULEENGINE_V2 RuleInfor;
        //
        STR_UNILATERALINFOR_V2 UnilateraliInfor[2];//单边信息：0 源； 1 目的----ss_pkt中的各类统计信息在此
        //P0F统计
        static const int POFSIZE=80;
        unsigned char pP0F[2][80];
        unsigned char POFBytes[2]; //0:SYN  1:SYNACK
        ////所有的规则
        //DWORD pTotalRule[MAXNUM_RULE_CONNECTEX_STATISTICS_V2];
        //DWORD TotalRuleNum;
        //包信息
        //STR_DFI_CONNECTEX_STATISTICS_V2 pDFIInfor[MAXNUM_DFI_CONNECTEX_STATISTICS_V2];
        //DWORD DFINum;
        ////负载信息
        //unsigned char pPayload[PACKETNUM_PAYLOAD_CONNECTSTATISTICS_RULEENGINE_V2][BYTES_PAYLOAD_CONNECTSTATISTICS_RULEENGINE_V2];
        s_tcp_finger_feature tcp_finger_feature[2]; //0:Client  1:Server
        uint8_t tcp_finger_bitmap;
        uint8_t syn_seq_num;
        uint8_t record_payload_num[2];
    public:
        single_session_session();
        ~single_session_session();
        void init();
        int LoadPacket(c_packet* IN_pPacket,session_pub* IN_pSession);
        //int LoadPacket(void *IN_pProExtract,c_packet *IN_pPacket,session_pub *IN_pSession,unsigned char *IN_pJudge);
        void add_packet_info(session_pub * p_session , c_packet * p_packet);
        // 第一个包甚至 
        void first_packet_info(session_pub * p_session , c_packet * p_packet);
        string bin2string(char* p_data, int len);
        void send (session_pub * p_session, void *p_th_tools, void *, int should_log);
        void send1 (session_pub * p_session ,single_session_msg* p_single_session_msg , Comm_msg* p_comm  , ss_basic_msg* p_ss_basic  , ss_stats_msg* p_ss_stats ,ss_pkt_msg* p_ss_pkt  , CInterface_Statistics* p_session_statistics, void *p_th_tools);
        static void send_session(session_pub * p_session, void *p_th_tools, int should_log);
    private:
        //卡方分布与t
        double ChiSquareDistrobution(uint32_t* IN_Array, double &OUT_Kx);
};

class single_session_session_marge
{
    public :
        single_session_session_marge(int num)
        {
            init_len_map();
            init_time_map();
            init_offset_map();
            init_ttl_map();
            p_single_session_session  = new single_session_session[num];
        }
        ~single_session_session_marge()
        {
            delete [] p_single_session_session ;
        }
        single_session_session * p_single_session_session ;

};

#endif  /*SINGLE_SESSION_SESSION_H*/

#ifndef __OFFICIAL_PORTMAP_H__
#define __OFFICIAL_PORTMAP_H__
#include <stdio.h>
#include <string.h>
#include "th_bitmap.h"

#define MAX_APP_NUM 1024LL
#define APP_ID_START 10000

class official_portmap
{
    public:
        official_portmap()
        {
            th_bitmap_init(&app2port_tcp, 65536 * MAX_APP_NUM);
            th_bitmap_init(&app2port_udp, 65536 * MAX_APP_NUM);
            memset(port_tcp, 0, 65536);
            memset(port_udp, 0, 65536);
            memset(appid_tcp, 0, MAX_APP_NUM);
            memset(appid_udp, 0, MAX_APP_NUM);
            memset(appid_unknown, 0, MAX_APP_NUM);
        }
        ~official_portmap()
        {
            th_bitmap_free(&app2port_tcp);
            th_bitmap_free(&app2port_udp);
        }
        int init();
        int judge_unofficial(uint8_t ippro, int appid, uint16_t port);
    private:
        th_bitmap app2port_tcp;
        th_bitmap app2port_udp;
        uint8_t port_tcp[65536];
        uint8_t port_udp[65536];
        uint8_t appid_tcp[MAX_APP_NUM];
        uint8_t appid_udp[MAX_APP_NUM];
        uint8_t appid_unknown[MAX_APP_NUM];
};


#endif

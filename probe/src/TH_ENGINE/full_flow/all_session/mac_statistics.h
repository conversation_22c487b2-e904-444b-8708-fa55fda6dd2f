
// Last Update:2019-01-16 11:44:00
/**
 * @file mac_statistics.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-01-10
 */

#ifndef MAC_STATISTICS_H
#define MAC_STATISTICS_H
#include "resources_marge.h"
#include "all_session_define.h"
#include <json/json.h>
#include <stdint.h>
#include <session_pub.h>
#include "th_engine_tools.h"
#include <set>
#include <no_lock_queue.h>
#include <map>
using namespace std;
#include "DataStructure/TemplateMatch.h"
#include "DataStructure/SequenceList.h"
#include "DataStructure/Func_Math.h"
#include "TH_engine_define.h"

#define MAX_THREAD_NUM 64

typedef struct
{
    uint64_t alert_num;
    uint64_t pkt_send;
    uint64_t pkt_recv;
    uint32_t ts_first;
    uint32_t ts_last;
} mac_val;

typedef struct
{
    uint64_t alert_num;
    uint64_t mac_1_send;
    uint64_t mac_2_send;
    uint32_t ts_first;
    uint32_t ts_last;
} mac_pair_val;

typedef struct
{
    uint64_t        mac_1;
    uint64_t        mac_2;
    mac_pair_val    val;
} mac_pair_info;

typedef struct _mac_pair_info_list
{
    int             mac_pair_count;
    mac_pair_info   *list;
} mac_pair_info_list;

class mac_pair_info_compare
{
public:
    int operator()( const mac_pair_info &IN_A,const mac_pair_info &IN_B )
    {
        if(IN_A.mac_1 < IN_B.mac_1)
        {
            return -1;
        }
        else if(IN_A.mac_1 > IN_B.mac_1)
        {
            return 1;
        }
        else if(IN_A.mac_2 < IN_B.mac_2)
        {
            return -1;
        }
        else if(IN_A.mac_2 > IN_B.mac_2)
        {
            return 1;
        }
        else
        {
            return 0;
        }
    }
};

class mac_statistics_sender
{
public:
    mac_statistics_sender()
    {
        init_queue();
        Init(10000);
    }
    ~mac_statistics_sender()
    {
        for (int i = 0; i < MAX_THREAD_NUM; i ++ )
        {
            N_queue_destry(queue[i]);
        }
        delete []queue;
        queue = NULL;
    }
    void mac_sort_partition(mac_pair_info **arr, int low, int high, int *pos)
    {
        mac_pair_info *key = arr[low];
        int i = low, j = high;
        uint64_t pkt_count = key->val.mac_1_send + key->val.mac_2_send;
        while(i < j)
        {
            while(i < j && arr[j]->val.mac_1_send + arr[j]->val.mac_2_send > pkt_count)
            {
                j--;
            }
            if(i < j)
            {
                arr[i++] = arr[j];
            }
            while(i < j && arr[i]->val.mac_1_send + arr[i]->val.mac_2_send < pkt_count)
            {
                i ++;
            }
            if(i < j)
            {
                arr[j--] = arr[i];
            }
        }
        arr[i] = key;
        *pos = i;
    }

    int mac_sort_topk(mac_pair_info **arr, int low, int high, int k)
    {
        if(k <= 0 || high-low + 1 <= k)
        {
            return low;
        }
        int pos =0;
        mac_sort_partition(arr, low, high, &pos);
        int num = high - pos + 1;
        int index = -1;
        if(num == k)
        {
            index = pos;
        }
        else if(num > k)
        {
           index = mac_sort_topk(arr, pos + 1, high, k);
        }
        else
        {
            index =  mac_sort_topk(arr, low, pos -1, k - num);
        }
        return index;
    }
    void init_queue()
    {
        queue = new NQueue*[MAX_THREAD_NUM];
        for (int i = 0; i < MAX_THREAD_NUM; i ++ )
        {
            queue[i] = N_queue_new(20);
        }
    }
    void send_log(uint32_t time_s)
    {
        bool b_5min = false;
        count ++;
        if(count >= 10)
        {
            count = 0;
            b_5min = true;
        }
        if(b_5min)
        {
            mac_pair_info tmp;
            mac_pair_info_list *p_mac_list_tmp;
            mac_pair_info_list *p_mac_list;
            int k = 0;
            for(int i=0; i < MAX_THREAD_NUM; i ++)
            {
                p_mac_list = NULL;
                while(p_mac_list_tmp=(mac_pair_info_list *)N_queue_dequeue(queue[i]))
                {
                    if(p_mac_list)
                    {
                        delete []p_mac_list->list;
                        delete p_mac_list;
                    }
                    p_mac_list = p_mac_list_tmp;
                }
                if(p_mac_list)
                {
                    for(int j=0; j < p_mac_list->mac_pair_count; j++)
                    {
                        DWORD pos = 0;
                        DWORD ret = JudgeAndAdd(p_mac_list->list[j], time_s, pos);
                        if(ret>ERRORSIGN)
                        {
                            continue;
                        }
                        if(0==ret)
                        {
                            mac_pair_val *to_update = NULL;
                            GetMacVal(pos, &to_update);
                            if(to_update)
                            {
                                to_update->alert_num += p_mac_list->list[j].val.alert_num;
                                to_update->mac_1_send += p_mac_list->list[j].val.mac_1_send;
                                to_update->mac_2_send += p_mac_list->list[j].val.mac_2_send;
                                if(to_update->ts_first > p_mac_list->list[j].val.ts_first)
                                {
                                    to_update->ts_first = p_mac_list->list[j].val.ts_first;
                                }
                                if(to_update->ts_last < p_mac_list->list[j].val.ts_last)
                                {
                                    to_update->ts_last = p_mac_list->list[j].val.ts_last;
                                }
                            }
                        }
                    }
                    delete []p_mac_list->list;
                    delete p_mac_list;
                }
            }
            
            int mac_pair_num = 0;
            for(int i=0; i < 10000; i++)
            {
                DWORD pos = PopMac(tmp);
                if(pos)
                {
                    sort_list[mac_pair_num++] = &m_Array.m_pData[pos];
                }
                else
                {
                    break;
                }
            }
            int idx = 0;
            if(mac_pair_num > 50)
            {
                idx = mac_sort_topk(&sort_list[0], 0, mac_pair_num-1, 50);
                if (idx + 50 < mac_pair_num)
                {
                    mac_pair_num = idx + 50;
                }
            }
            char buff[1024];
            int msglen;
            map<uint64_t, mac_val> mac_map;
            mac_val to_add;
            std::map<uint64_t , mac_val> ::iterator iter;
            for(;idx < mac_pair_num; idx ++ )
            {
                uint8_t *p_mac_1 =(uint8_t*)&(sort_list[idx]->mac_1);
                uint8_t *p_mac_2 =(uint8_t*)&(sort_list[idx]->mac_2);
                uint64_t pkt_count = sort_list[idx]->val.mac_1_send + sort_list[idx]->val.mac_2_send;
                msglen = sprintf(buff, "{\"type\":%d,\"time\":%u,\"mac_1\":\"%02x:%02x:%02x:%02x:%02x:%02x\",\"mac_2\":\"%02x:%02x:%02x:%02x:%02x:%02x\",\"pkt\":\"%"PRIu64"\",\"ts_first\":%u,\"ts_last\":%u,\"task_id\":\"%lld\",\"batch_id\":\"%lld\",\"device_id\":\"%u\"}", 
                        211, time_s, p_mac_1[5],p_mac_1[4],p_mac_1[3],p_mac_1[2],p_mac_1[1],p_mac_1[0], p_mac_2[5],p_mac_2[4],p_mac_2[3],p_mac_2[2],p_mac_2[1],p_mac_2[0], pkt_count, sort_list[idx]->val.ts_first, sort_list[idx]->val.ts_last,config_and_define::task_id, config_and_define::batch_id, th_engine_tools::instance()->device_id);
                th_engine_tools::instance()->send_log("slog", NULL, 0, buff, msglen);
                iter = mac_map.find(sort_list[idx]->mac_1);
                if(iter != mac_map.end())
                {
                    iter->second.alert_num += sort_list[idx]->val.alert_num;
                    iter->second.pkt_send += sort_list[idx]->val.mac_1_send;
                    iter->second.pkt_recv += sort_list[idx]->val.mac_2_send;
                    if(iter->second.ts_first > sort_list[idx]->val.ts_first)
                    {
                        iter->second.ts_first = sort_list[idx]->val.ts_first;
                    }
                    if(iter->second.ts_last < sort_list[idx]->val.ts_last)
                    {
                        iter->second.ts_last = sort_list[idx]->val.ts_last;
                    }
                }
                else
                {
                    to_add.alert_num = sort_list[idx]->val.alert_num;
                    to_add.pkt_send = sort_list[idx]->val.mac_1_send;
                    to_add.pkt_recv = sort_list[idx]->val.mac_2_send;
                    to_add.ts_first = sort_list[idx]->val.ts_first;
                    to_add.ts_last  = sort_list[idx]->val.ts_last;
                    mac_map[sort_list[idx]->mac_1] = to_add;
                }
                if (sort_list[idx]->mac_2 != sort_list[idx]->mac_1)
                {
                    iter = mac_map.find(sort_list[idx]->mac_2);
                    if((iter != mac_map.end()))
                    {
                        iter->second.alert_num += sort_list[idx]->val.alert_num;
                        iter->second.pkt_send += sort_list[idx]->val.mac_2_send;
                        iter->second.pkt_recv += sort_list[idx]->val.mac_1_send;
                        if(iter->second.ts_first > sort_list[idx]->val.ts_first)
                        {
                            iter->second.ts_first = sort_list[idx]->val.ts_first;
                        }
                        if(iter->second.ts_last < sort_list[idx]->val.ts_last)
                        {
                            iter->second.ts_last = sort_list[idx]->val.ts_last;
                        }
                    }
                    else
                    {
                        to_add.alert_num = sort_list[idx]->val.alert_num;
                        to_add.pkt_send = sort_list[idx]->val.mac_2_send;
                        to_add.pkt_recv = sort_list[idx]->val.mac_1_send;
                        to_add.ts_first = sort_list[idx]->val.ts_first;
                        to_add.ts_last  = sort_list[idx]->val.ts_last;
                        mac_map[sort_list[idx]->mac_2] = to_add;
                    }
                }
            }
            for(iter = mac_map.begin();iter !=  mac_map.end(); iter ++ )
            {
                uint8_t *p_mac =(uint8_t*)&(iter->first);
                msglen = sprintf(buff, "{\"type\":%d,\"time\":%u,\"mac\":\"%02x:%02x:%02x:%02x:%02x:%02x\",\"alert\":\"%"PRIu64"\",\"send\":\"%"PRIu64"\",\"recv\":\"%"PRIu64"\",\"port\":null,\"ts_first\":%u,\"ts_last\":%u,\"task_id\":\"%lld\",\"batch_id\":\"%lld\",\"device_id\":\"%u\"}", 
                        209, time_s, p_mac[5],p_mac[4],p_mac[3],p_mac[2],p_mac[1],p_mac[0], iter->second.alert_num, iter->second.pkt_send, iter->second.pkt_recv, iter->second.ts_first, iter->second.ts_last,config_and_define::task_id, config_and_define::batch_id, th_engine_tools::instance()->device_id);
                th_engine_tools::instance()->send_log("slog", NULL, 0, buff, msglen);
            }
        }
    }
    // ret: 0,added; 1,new add;  >ERRORSIGN,error
    // out_pos:0,error >0,position
    DWORD JudgeAndAdd( mac_pair_info &in_mac, __time32_t IN_TimeStamp, DWORD &out_pos )
    {
        DWORD index=0;
        DWORD re;
        DWORD IsAdd=0;

        uint32_t *pindex = (uint32_t *)&in_mac.mac_1;
        uint32_t *pindex1 = (uint32_t *)&in_mac.mac_2;
        index= (*pindex) ^ (*(pindex+1)) ^ (*pindex1) ^ (*(pindex1+1));
        out_pos = m_Judge.JudgeAndAdd(index, in_mac, IsAdd);
        if( out_pos>=ERRORSIGN  )
        {
            re=out_pos;
            out_pos=0;
            return re;
        }
        re=m_Sequence.RenewNode( out_pos,IN_TimeStamp );
        if( re>=ERRORSIGN )
        {
            out_pos=0;
            return re;
        }
        return IsAdd;
    }

    DWORD GetMacVal(DWORD in_position, mac_pair_val **out_mac_val)
    {
        if (in_position >= m_Array.m_DataSize || 0 == in_position)
        {
            *out_mac_val = NULL;
            return 0x8000ff01;
        }
        *out_mac_val = &m_Array.m_pData[in_position].val;
        return 0;
    }

    DWORD PopMac( mac_pair_info &out_mac )
    {
        DWORD Position;
 
        Position=m_Sequence.PopNode( );
        if( Position>=m_Array.m_DataSize ) return 0;
 
        out_mac=m_Array.m_pData[Position];
 
        m_Judge.DeleteValue( Position );
 
        return Position;
    }
    DWORD Init( DWORD IN_MaxNodeNum,DWORD IN_HashSize=0 )
    {
        DWORD re;
        //根据MaxNodeNum计算合适的HashSize
        if (IN_HashSize==0)
        {
            IN_HashSize = GetPrimeNum(IN_MaxNodeNum >> 2, IN_MaxNodeNum);
            IN_HashSize = J_max(IN_HashSize, 1);
        }
    
        re=m_Array.Init( IN_MaxNodeNum+1 );
        if( re!=0 ) return re;
    
        re=m_Judge.Init( IN_HashSize,IN_MaxNodeNum,&m_Array );
        if( re!=0 ) return re;
    
        re=m_Sequence.Init( IN_MaxNodeNum+1 );
        if( re!=0 ) return re;
    
        return 0;
    }
    void Quit()
    {
        m_Judge.Quit();
        m_Array.Quit();
        m_Sequence.Quit();
    }
    void Reset()
    {
        m_Judge.Reset();
        m_Array.Reset();
        m_Sequence.Reset();
    }
    NQueue **queue;
    int count = 0;
    CTemplateMatch<mac_pair_info, mac_pair_info_compare> m_Judge;
    CArrayBasic<mac_pair_info> m_Array;
    CSequenceList m_Sequence;
    mac_pair_info *sort_list[10000];
};

class mac_statistics 
{
public:
    mac_statistics()
    {
        mac_pair_count = 0;
        mac_pair_pos = NULL;
        // queue = in_queue;
        last_ts = 0;
    }
    ~mac_statistics()
    {
        queue = NULL;
    }
    void set_queue(NQueue **queue)
    {
        this->queue = queue;
    }
    // ret: 0,added; 1,new add;  >ERRORSIGN,error
    // out_pos:0,error >0,position
    DWORD JudgeAndAdd( mac_pair_info &in_mac, __time32_t IN_TimeStamp, DWORD &out_pos )
    {
        DWORD index=0;
        DWORD re;
        DWORD IsAdd=0;

        uint32_t *pindex = (uint32_t *)&in_mac.mac_1;
        uint32_t *pindex1 = (uint32_t *)&in_mac.mac_2;
        index= (*pindex) ^ (*(pindex+1)) ^ (*pindex1) ^ (*(pindex1+1));
        out_pos = m_Judge.JudgeAndAdd(index, in_mac, IsAdd);
        if( out_pos>=ERRORSIGN  )
        {
            re=out_pos;
            out_pos=0;
            return re;
        }
        re=m_Sequence.RenewNode( out_pos,IN_TimeStamp );
        if( re>=ERRORSIGN )
        {
            out_pos=0;
            return re;
        }
        return IsAdd;
    }

    DWORD GetMacVal(DWORD in_position, mac_pair_val **out_mac_val)
    {
        if (in_position >= m_Array.m_DataSize || 0 == in_position)
        {
            *out_mac_val = NULL;
            return 0x8000ff01;
        }
        *out_mac_val = &m_Array.m_pData[in_position].val;
        return 0;
    }

    DWORD GetMacInfo(DWORD in_position, mac_pair_info **out_mac_info)
    {
        if (in_position >= m_Array.m_DataSize || 0 == in_position)
        {
            *out_mac_info = NULL;
            return 0x8000ff01;
        }
        *out_mac_info = &m_Array.m_pData[in_position];
        return 0;
    }

    DWORD PopMac( mac_pair_info &out_mac )
    {
        DWORD Position;
 
        Position=m_Sequence.PopNode( );
        if( Position>=m_Array.m_DataSize ) return 0;
 
        out_mac=m_Array.m_pData[Position];
 
        m_Judge.DeleteValue( Position );
 
        return Position;
    }
    void add_packet_info(session_pub * p_session , c_packet * p_packet)
    {
        if(false == p_packet->has_mac())
        {
            return;
        }
        mac_pair_info tmp;
        mac_pair_info to_add;
        memset(&to_add, 0, sizeof(mac_pair_info));
        uint64_t src_mac = p_packet->get_src_mac();
        uint64_t dst_mac = p_packet->get_dst_mac();
        if ((src_mac | dst_mac) & ((uint64_t)1 << 40))  //组播MAC，广播MAC
        {
            return;
        }
        if (src_mac <= dst_mac)
        {
            to_add.mac_1 = src_mac;
            to_add.mac_2 = dst_mac;
        }
        else
        {
            to_add.mac_1 = dst_mac;
            to_add.mac_2 = src_mac;
        }
        DWORD pos = 0;
        DWORD ret = JudgeAndAdd(to_add, p_packet->clock_ts[0], pos);
        // //修改为不添加新MAC
        // if(ret>ERRORSIGN)
        // {
        //     PopMac( tmp );
        //     mac_pair_count --;
        //     ret = JudgeAndAdd(to_add, p_packet->clock_ts[0], pos);
        // }
        if(0==ret || 1 == ret)
        {
            mac_pair_val *to_update = NULL;
            GetMacVal(pos, &to_update);
            if(to_update)
            {
                to_update->alert_num += p_packet->alert_num;
                if(to_add.mac_1 == to_add.mac_2)  //srcmac == dstmac ?
                {
                    to_update->mac_1_send += 1;
                    to_update->mac_2_send += 1;
                }
                else if (src_mac == to_add.mac_1)
                {
                    to_update->mac_1_send += 1;
                }
                else
                {
                    to_update->mac_2_send += 1;
                }
                if(ret) //new add
                {
                    to_update->ts_first = p_packet->time_ts[0];
                    to_update->ts_last = p_packet->time_ts[0];
                }
                else
                {
                    if(to_update->ts_first > p_packet->time_ts[0])
                    {
                        to_update->ts_first = p_packet->time_ts[0];
                    }
                    if(to_update->ts_last < p_packet->time_ts[0])
                    {
                        to_update->ts_last = p_packet->time_ts[0];
                    }
                }
            }
            if(ret)
            {
                mac_pair_pos[mac_pair_count] = pos;
                mac_pair_count ++;
            }
        }
    }
    void timeout(uint32_t ts, uint32_t thread_id)
    {
        if(ts - last_ts >= 100)
        {
            if(mac_pair_count > 0)
            {
                mac_pair_info_list *p_mac_list = new mac_pair_info_list;
                p_mac_list->mac_pair_count = 0;
                p_mac_list->list = new mac_pair_info[mac_pair_count];
                mac_pair_info * p_tmp = NULL;
                for(int i=0; i < mac_pair_count; i ++)
                {
                    GetMacInfo(mac_pair_pos[i], &p_tmp);
                    if(p_tmp)
                    {
                        memcpy(&p_mac_list->list[p_mac_list->mac_pair_count], p_tmp, sizeof(mac_pair_info));
                        p_mac_list->mac_pair_count ++;
                    }
                }
                if (false == N_queue_enqueue(queue[thread_id], p_mac_list))
                {
                    delete []p_mac_list->list;
                    delete p_mac_list;
                    p_mac_list = NULL;
                }
            }
            last_ts = ts;
        }
    }
public:
    DWORD Init( DWORD IN_MaxNodeNum,DWORD IN_HashSize=0 )
    {
        DWORD re;
        //根据MaxNodeNum计算合适的HashSize
        if (IN_HashSize==0)
        {
            IN_HashSize = GetPrimeNum(IN_MaxNodeNum >> 2, IN_MaxNodeNum);
            IN_HashSize = J_max(IN_HashSize, 1);
        }
        mac_pair_count = 0;
        mac_pair_pos = new uint32_t [IN_MaxNodeNum];
        if( NULL == mac_pair_pos)
        {
            return -1;
        }
        memset(mac_pair_pos, 0, sizeof(uint32_t) * IN_MaxNodeNum);
        
        re=m_Array.Init( IN_MaxNodeNum+1 );
        if( re!=0 ) return re;
    
        re=m_Judge.Init( IN_HashSize,IN_MaxNodeNum,&m_Array );
        if( re!=0 ) return re;
    
        re=m_Sequence.Init( IN_MaxNodeNum+1 );
        if( re!=0 ) return re;
    
        return 0;
    }
    void Quit()
    {
        if(mac_pair_pos)
        {
            delete []mac_pair_pos;
            mac_pair_pos = NULL;
        }
        m_Judge.Quit();
        m_Array.Quit();
        m_Sequence.Quit();
    }
    void Reset()
    {
        if(mac_pair_pos)
        {
            memset(mac_pair_pos, 0, sizeof(uint32_t) * mac_pair_count);
        }
        mac_pair_count = 0;
        m_Judge.Reset();
        m_Array.Reset();
        m_Sequence.Reset();
    }
private:
    int mac_pair_count;
    uint32_t *mac_pair_pos;
    CTemplateMatch<mac_pair_info, mac_pair_info_compare> m_Judge;
    CArrayBasic<mac_pair_info> m_Array;
    CSequenceList m_Sequence;
    NQueue **queue;
    uint32_t last_ts;
};

#endif  /*MAC_STATISTICS_H*/


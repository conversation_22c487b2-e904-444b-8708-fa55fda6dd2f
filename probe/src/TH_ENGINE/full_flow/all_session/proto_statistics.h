
// Last Update:2019-01-15 19:17:14
/**
 * @file proto_statistics.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-01-10
 */

#ifndef PROTO_STATISTICS_H
#define PROTO_STATISTICS_H
#include "resources_marge.h"
#include <json/json.h>
#include <stdint.h>
#include <session_pub.h>
#include "th_engine_tools.h"
using namespace std;

// 协议信息统计
class proto_statistics_session
{
public:
    proto_statistics_session()
    {
        protocol = 0;
        pro_pkt_num = 0;
        pro_pkt_bytes = 0;
        pro_session_num = 0;
        pro_speed = 0;
    }
    ~proto_statistics_session()
    {
    }
    void add_packet_info(session_pub * p_session , c_packet * p_packet)
    {
        pro_pkt_num++;
        pro_pkt_bytes += p_packet->packet_len;
        pro_speed += p_packet->packet_len;
    }
    void send_log(uint32_t time_s)
    {
        // Json::Value jjson;
        // jjson["protocol"] = protocol;
        // jjson["pro_pkt_num"] = pro_pkt_num;
        // jjson["pro_pkt_bytes"] = pro_pkt_bytes;
        // jjson["pro_session_num"] = pro_session_num;
        // jjson["pro_speed"] = pro_speed / SEND_SPACE;
        // string to_send = jjson.toStyledString();
        // th_engine_tools::instance()->p_synlog->send_log(-1, 7, &to_send);
        // syslog(LOG_LOCAL7|LOG_INFO ,"%s",jjson.toStyledString().c_str());
        init();
    }
    void init()
    {
        pro_speed = 0;
    }
public:
    //协议统计信息
    uint32_t protocol;
    uint32_t pro_pkt_num;//包数
    uint32_t pro_pkt_bytes;//字节数
    uint32_t pro_session_num;//
    uint32_t pro_speed;//收包流量，每8秒统计一次  -- bps
};
class proto_statistics 
{
public:
    proto_statistics()
    {
    }
    ~proto_statistics()
    {
    }
    void add_packet_info(session_pub * p_session , c_packet * p_packet)
    {
        //统计规则命中的信息
        for(int i = 0 ;i < p_session -> session_basic.RuleNum; i++)
        {
            std::map<uint32_t , proto_statistics_session *> ::iterator iter = proto_map.find(p_session -> session_basic.pRule[i]);
            if(iter != proto_map.end()) 
            {
                iter->second->add_packet_info(p_session , p_packet);
            }
            else 
            {
               proto_statistics_session * p = new  proto_statistics_session();
               p->protocol = p_session->session_basic.pRule[i];
               proto_map[p->protocol] = p;
            }
        }
    }
    void send_log(uint32_t time_s)
    {
        //发送日志 ----syslog 发送 
        // rule 信息
        std::map<uint32_t , proto_statistics_session *> ::iterator iter = proto_map.begin() ;
        //  rule 统计信息
        for(;iter != proto_map.end(); iter ++)
        {
            iter->second->send_log(time_s);
        }
    }
public:
    std::map<uint32_t , proto_statistics_session *> proto_map;
};


#endif  /*PROTO_STATISTICS_H*/



// Last Update:2019-01-15 17:39:19
/**
 * @file basic_statistics.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-01-10
 */

#ifndef BASIC_STATISTICS_H
#define BASIC_STATISTICS_H
#include "resources_marge.h"
#include <json/json.h>
#include <stdint.h>
#include <session_pub.h>
#include "th_engine_tools.h"
using namespace std;

class  basic_statistics
{
public:
    void  first_packet(session_pub * p_session , c_packet * p_packet )
    {
        all_session_num++;
        all_pkt_num++;
    }
    void add_packet_info(session_pub * p_session , c_packet * p_packet)
    {
        all_bytes += p_packet -> packet_len;
    }
    void  recovery(session_pub * p_session)
    {
        //all_action_session  -- ;
    }
    void init()
    {

    }
    void send_log(uint32_t time_s)
    {
        // Json::Value jjson ;
        // jjson["type"] =230;
        // jjson["all_pkt_num"] = all_pkt_num;
        // jjson["all_bytes"] = all_bytes ;
        // jjson["all_session_num"] = all_session_num ;
        // jjson["time"] = time_s ;
        // string to_send = jjson.toStyledString();
        // th_engine_tools::instance()->p_synlog->send_log(-1, 7, &to_send);
        // syslog(LOG_LOCAL7|LOG_INFO ,"%s",jjson.toStyledString().c_str());
        init();
    }
public:
    //总体信息
    uint32_t start_time;//系统启动时间（秒）
    uint32_t cur_time;//当前时间（秒）
    uint64_t run_time;//运行时间（秒）
    uint32_t all_pkt_num;//整体包数
    uint32_t all_bytes;//整体字节数
    uint32_t all_session_num;//总会话数
    uint32_t speed;//流量，每8秒统计一次
};


#endif  /*BASIC_STATISTICS_H*/


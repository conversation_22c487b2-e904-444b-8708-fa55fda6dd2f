#include "network_slice.h"
#include <sstream>
#include <arpa/inet.h>
#include "json/reader.h"
#include "json/value.h"
#include "json/writer.h"
#include "Define_L2Defense.h"
#include "TH_engine_define.h"
#include "read_conf_commit.h"

slice_statistics::slice_statistics()
{
    thread_num = 0;
    p_thread_data = NULL;
    p_merge_data = new thread_slice_data;
    if(NULL==p_merge_data)
    {
        exit(-1);
    }
    ipseg_num = 0;
    ip_num = 0;
    ts_last_send = 0;
    ts_wait = 300;
    ts_system_start = 0;
    memset(ipseg_key, 0, sizeof(uint64_t) * MAX_IP_SEG_SUPPORT);
    memset(ip_key, 0, sizeof(uint64_t) * MAX_IP_SUPPORT);
    max_thread_session = read_conf_commit::GetInstance()->read_conf_session_num("session","thread_session_num");
    new_app_flag = NULL;
    config_parse();
    memset(pipseg_node_list, 0, sizeof(ipseg_node*) * 65536);
    m_Judge_mac = NULL;
    m_Array_mac = NULL;
    output_path = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/ipslice/";
    b_seg_analysis = 0;
}
slice_statistics::~slice_statistics()
{
    if(p_thread_data)
    {
        delete []p_thread_data;
        p_thread_data = NULL;
    }
    if(p_merge_data)
    {
        delete p_merge_data;
        p_merge_data = NULL;
    }
    if(new_app_flag)
    {
        for(int i=0; i < thread_num; i ++)
        {
            if(new_app_flag[i])
            {
                delete [](new_app_flag[i]);
                new_app_flag[i] = NULL;
            }
        }
        delete []new_app_flag;
        new_app_flag = NULL;
    }
    m_Judge_ip.Quit();
    m_Array_ip.Quit();
    for(int i =0; i < 65536; i ++)
    {
        if(pipseg_node_list[i])
        {
            ipseg_node *pfree = pipseg_node_list[i];
            ipseg_node *psave = NULL;
            while(pfree)
            {
                psave = pfree->pnext;
                free(pfree);
                pfree = psave;
            }
            pipseg_node_list[i] = NULL;
        }
    }
}

void slice_statistics::config_parse()
{
    string str;
    ifstream fin;
    string conf_path = string(getenv("THE_CONF_PATH"))+"/ipslice.conf";
    fin.open(conf_path, ios::in);
    stringstream buf;
    buf << fin.rdbuf(); 
    str = buf.str();
    cout << str << endl;
    fin.close();
    
    Json::Reader reader;
    Json::Value rule_json;
    
    if (false != reader.parse(str, rule_json))
    {
        if(rule_json["ipseg"].isNull() == false)
        {
            ipseg_num = rule_json["ipseg"].size();
            if(ipseg_num > MAX_IP_SEG_SUPPORT)
            {
                ipseg_num = MAX_IP_SEG_SUPPORT;
            }
            for(uint32_t i = 0; i < ipseg_num; i ++)
            {
                string ip = rule_json["ipseg"][i]["ip"].asString();
                string mask = rule_json["ipseg"][i]["mask"].asString();
                ipseg_str[i] = ip+"|"+mask;
                struct in_addr ipv4;
                inet_pton(AF_INET, ip.c_str(), &ipv4);
                uint64_t key = (uint64_t)ntohl(ipv4.s_addr);
                key <<= 32;
                inet_pton(AF_INET, mask.c_str(), &ipv4);
                uint64_t maski = (uint64_t)ntohl(ipv4.s_addr);
                key |= maski;
                ipseg_key[i] = key;
            }
        }
        if(rule_json["ip"].isNull() == false)
        {
            ip_num = rule_json["ip"].size();
            if(ip_num > MAX_IP_SUPPORT)
            {
                ip_num = MAX_IP_SUPPORT;
            }
            for(uint32_t i = 0; i < ip_num; i ++)
            {
                string ip = rule_json["ip"][i].asString();
                ip_str[i] = ip;
                struct in_addr ipv4;
                inet_pton(AF_INET, ip.c_str(), &ipv4);
                uint64_t key = (uint64_t)ntohl(ipv4.s_addr);
                ip_key[i] = key;
            }
        }
    }
}


int slice_statistics::init(int thread_num)
{
    this->thread_num = thread_num;
    new_app_flag = new uint8_t *[thread_num];
    if(new_app_flag)
    {
        for(int i=0; i < thread_num; i ++)
        {
            new_app_flag[i] = NULL;
            new_app_flag[i] = new uint8_t[max_thread_session];
            if(NULL==new_app_flag[i])
            {
                exit(-1);
            }
            memset(new_app_flag[i], 0, sizeof(uint8_t) * max_thread_session);
        }
    }
    else
    {
        exit(-1);
    }
    ts_system_start = (uint32_t)time(NULL);
    uint32_t ts_x = ts_system_start % ts_wait;
    ts_system_start -= ts_x;
    uint32_t IN_HashSize = GetPrimeNum(MAX_IP_SUPPORT >> 2, MAX_IP_SUPPORT);
    IN_HashSize = J_max(IN_HashSize, 1);
    m_Array_ip.Init(MAX_IP_SUPPORT+1);
    m_Judge_ip.Init(IN_HashSize, MAX_IP_SUPPORT, &m_Array_ip);
    p_thread_data = (thread_slice_data *)new thread_slice_data[thread_num];
    if(p_thread_data)
    {
        memset(p_thread_data, 0, sizeof(thread_slice_data) * thread_num);
    }
    m_Judge_mac = new CTemplateMatch<key2idx, key2idx_compare>[thread_num];
    m_Array_mac = new CArrayBasic<key2idx>[thread_num];
    IN_HashSize = GetPrimeNum(MAX_MAC_SUPPORT >> 2, MAX_MAC_SUPPORT);
    IN_HashSize = J_max(IN_HashSize, 1);

    for(int i=0; i < thread_num; i ++)
    {
        m_Array_mac[i].Init(MAX_MAC_SUPPORT +1);
        m_Judge_mac[i].Init(IN_HashSize, MAX_IP_SUPPORT, &m_Array_mac[i]);

        p_thread_data[i].all.type       = SLICE_TYPE_ALL;
        p_thread_data[i].inner.type     = SLICE_TYPE_INNER;
        p_thread_data[i].prc.type       = SLICE_TYPE_PRC;
        p_thread_data[i].outland.type   = SLICE_TYPE_OUTLAND;
        p_thread_data[i].tcp.type       = SLICE_TYPE_TCP;
        p_thread_data[i].udp.type       = SLICE_TYPE_UDP;
        for(uint64_t j=0; j < 65536; j++)
        {
            p_thread_data[i].tcp_port[j].key = j;
            p_thread_data[i].tcp_port[j].type = SLICE_TYPE_TCPSPORT;
            p_thread_data[i].udp_port[j].key = j;
            p_thread_data[i].udp_port[j].type = SLICE_TYPE_UDPSPORT;
        }
        for(uint64_t j=APPID_START; j < APPID_MAX; j ++)
        {
            p_thread_data[i].app_list[j-APPID_START].key = j;
            p_thread_data[i].app_list[j-APPID_START].type = SLICE_TYPE_APP;
        }
        for(uint32_t j=0; j < ipseg_num; j ++)
        {
            p_thread_data[i].ipseg_list[j].key = ipseg_key[j];
            p_thread_data[i].ipseg_list[j].type = SLICE_TYPE_IPSEG;
        }
        for(uint32_t j=0; j < ip_num; j ++)
        {
            p_thread_data[i].ip_list[j].key = ip_key[j];
            p_thread_data[i].ip_list[j].type = SLICE_TYPE_IP;
        }
    }
    for(uint32_t j=0; j < ipseg_num; j ++)
    {
        uint64_t idx = ipseg_key[j] >> 48;
        ipseg_node *pnew = new ipseg_node;
        pnew->key = ipseg_key[j];
        pnew->idx = j;
        pnew->pnext = NULL;
        ipseg_node *pnode = pipseg_node_list[idx];
        if(NULL==pnode)
        {
            pipseg_node_list[idx] = pnew;
        }
        else
        {
            while(pnode->pnext)
            {
                pnode = pnode->pnext;
            }
            pnode->pnext = pnew;
        }
    }
    for(uint32_t j=0; j < ip_num; j ++)
    {
        key2idx tmp;
        tmp.key = ip_key[j];
        tmp.idx = j;
        DWORD IsAdd=0;
        m_Judge_ip.JudgeAndAdd((DWORD)ip_key[j], tmp, IsAdd);
    }
}

int slice_statistics::find_ipseg_idx(uint32_t ipaddr)
{
    uint32_t idx = ipaddr >> 16;
    ipseg_node *pnode = pipseg_node_list[idx];
    while(pnode)
    {
        uint64_t ip = pnode->key >> 32;
        uint64_t mask = pnode->key & 0xffffffff;
        if((ipaddr & mask) == (ip & mask))
        {
            return (int)pnode->idx;
        }
        pnode = pnode->pnext;
    }
    return -1;
}
int slice_statistics::find_ip_idx(uint32_t ipaddr)
{
    key2idx tmp;
    tmp.key = (uint64_t)ipaddr;
    uint32_t outpos = m_Judge_ip.JudgeValue(ipaddr, tmp);
    if(outpos)
    {
        return (int)m_Array_ip.m_pData[outpos].idx;
    }
    return -1;

}
int slice_statistics::find_mac_idx(int thread_id, uint64_t mac)
{
    static uint64_t broadcast_mask = 0x10000000000LL;
    thread_slice_data *p_this_thread = p_thread_data+thread_id;
    uint32_t idx_use = p_this_thread->idx_use;
    if(!(mac & broadcast_mask))    //not broadcast not multicast
    {
        key2idx tmp;
        tmp.key = mac;
        uint32_t index = (uint32_t)(mac & 0xffffffffLL);
        uint32_t outpos = m_Judge_mac[thread_id].JudgeValue(index, tmp);
        if(outpos)
        {
            return (int)m_Array_mac[thread_id].m_pData[outpos].idx;
        }
        else if(p_this_thread->mac_count[idx_use] < MAX_MAC_SUPPORT)
        {
            uint32_t is_add = 0;
            tmp.idx = p_this_thread->mac_count[idx_use];
            m_Judge_mac[thread_id].JudgeAndAdd(index, tmp, is_add);
            p_this_thread->mac_list[tmp.idx].key = mac;
            p_this_thread->mac_list[tmp.idx].type = SLICE_TYPE_MAC;
            p_this_thread->mac_count[idx_use] ++;
            return (int)tmp.idx;
        }
    }
    return -1;
}

void  slice_statistics::first_packet(uint32_t thread_id, session_pub * p_session , c_packet * p_packet, ipslice_flags *pflags)
{
    thread_slice_data *p_this_thread = p_thread_data+thread_id;
    uint32_t idx_use = p_this_thread->idx_use;
    p_this_thread->all.data[idx_use].ss_new ++;
    p_this_thread->all.data[idx_use].ss_active ++;
    if(pflags->b_flag_inner[0] || pflags->b_flag_inner[1])
    {
        p_this_thread->inner.data[idx_use].ss_new ++;
        p_this_thread->inner.data[idx_use].ss_active ++;
    }
    if(pflags->b_flag_prc[0] || pflags->b_flag_prc[1])
    {
        p_this_thread->prc.data[idx_use].ss_new ++;
        p_this_thread->prc.data[idx_use].ss_active ++;
    }
    if(pflags->b_flag_outland[0] || pflags->b_flag_outland[1])
    {
        p_this_thread->outland.data[idx_use].ss_new ++;
        p_this_thread->outland.data[idx_use].ss_active ++;
    }
    if(6 == p_session->session_basic.IPPro)
    {
        p_this_thread->tcp.data[idx_use].ss_new ++;
        p_this_thread->tcp.data[idx_use].ss_active ++;
        p_this_thread->tcp_port[p_session->session_basic.pPort[1]].data[idx_use].ss_new ++;
        p_this_thread->tcp_port[p_session->session_basic.pPort[1]].data[idx_use].ss_active ++;
    }
    else if(17 == p_session->session_basic.IPPro)
    {
        p_this_thread->udp.data[idx_use].ss_new ++;
        p_this_thread->udp.data[idx_use].ss_active ++;
        p_this_thread->udp_port[p_session->session_basic.pPort[1]].data[idx_use].ss_new ++;
        p_this_thread->udp_port[p_session->session_basic.pPort[1]].data[idx_use].ss_active ++;
    }
//appid not here
    if(ipseg_num)
    {
        if(pflags->idx_ipseg[0] >= 0)
        {
            p_this_thread->ipseg_list[pflags->idx_ipseg[0]].data[idx_use].ss_new ++;
            p_this_thread->ipseg_list[pflags->idx_ipseg[0]].data[idx_use].ss_active ++;
        }
        if((pflags->idx_ipseg[1] >= 0) && (pflags->idx_ipseg[1] != pflags->idx_ipseg[0]))
        {
            p_this_thread->ipseg_list[pflags->idx_ipseg[1]].data[idx_use].ss_new ++;
            p_this_thread->ipseg_list[pflags->idx_ipseg[1]].data[idx_use].ss_active ++;
        }
    }
    if(ip_num)
    {
        if(pflags->idx_ip[0] >= 0)
        {
            p_this_thread->ip_list[pflags->idx_ip[0]].data[idx_use].ss_new ++;
            p_this_thread->ip_list[pflags->idx_ip[0]].data[idx_use].ss_active ++;
        }
        if((pflags->idx_ip[1] >= 0) && (pflags->idx_ip[1] != pflags->idx_ip[0]))
        {
            p_this_thread->ip_list[pflags->idx_ip[1]].data[idx_use].ss_new ++;
            p_this_thread->ip_list[pflags->idx_ip[1]].data[idx_use].ss_active ++;
        }
    }
    if(p_session->b_has_mac)
    {
        if(pflags->idx_mac[0] >= 0)
        {
            p_this_thread->mac_list[pflags->idx_mac[0]].data[idx_use].ss_new ++;
            p_this_thread->mac_list[pflags->idx_mac[0]].data[idx_use].ss_active ++;
        }
        if((pflags->idx_mac[1] >= 0) && (pflags->idx_mac[1] != pflags->idx_mac[0]))
        {
            p_this_thread->mac_list[pflags->idx_mac[1]].data[idx_use].ss_new ++;
            p_this_thread->mac_list[pflags->idx_mac[1]].data[idx_use].ss_active ++;
        }
    }
}
void slice_statistics::add_packet_info_syn(uint32_t thread_id, c_packet * p_packet, ipslice_flags *pflags)
{
    thread_slice_data *p_this_thread = p_thread_data+thread_id;
    uint32_t idx_use = p_this_thread->idx_use;

    if(pflags->b_flag_inner[0])
    {
        p_this_thread->inner.data[idx_use].syn_pkt_send ++;
    }
    if(pflags->b_flag_inner[1])
    {
        p_this_thread->inner.data[idx_use].syn_pkt_recv ++;
    }

    if(pflags->b_flag_prc[0])
    {
        p_this_thread->prc.data[idx_use].syn_pkt_send ++;
    }
    if(pflags->b_flag_prc[1])
    {
        p_this_thread->prc.data[idx_use].syn_pkt_recv ++;
    }
    
    if(pflags->b_flag_outland[0])
    {
        p_this_thread->outland.data[idx_use].syn_pkt_send ++;
    }
    if(pflags->b_flag_outland[1])
    {
        p_this_thread->outland.data[idx_use].syn_pkt_recv ++;
    }
    if(0 == p_packet->Directory)
    {
        p_this_thread->tcp_port[p_packet->dst_port].data[idx_use].syn_pkt_recv ++;
    }
    else
    {
        p_this_thread->tcp_port[p_packet->src_port].data[idx_use].syn_pkt_send ++;
    }
//appid how?
    if(ipseg_num)
    {
        if(pflags->idx_ipseg[0] >= 0)
        {
            p_this_thread->ipseg_list[pflags->idx_ipseg[0]].data[idx_use].syn_pkt_send ++;
        }
        if(pflags->idx_ipseg[1] >= 0)
        {
            p_this_thread->ipseg_list[pflags->idx_ipseg[1]].data[idx_use].syn_pkt_recv ++;
        }
    }
    if(ip_num)
    {
        if(pflags->idx_ip[0] >= 0)
        {
            p_this_thread->ip_list[pflags->idx_ip[0]].data[idx_use].syn_pkt_send ++;
        }
        if(pflags->idx_ip[1] >= 0)
        {
            p_this_thread->ip_list[pflags->idx_ip[1]].data[idx_use].syn_pkt_recv ++;
        }
    }
    if(p_packet->has_mac())
    {
        if(pflags->idx_mac[0] >= 0)
        {
            p_this_thread->mac_list[pflags->idx_mac[0]].data[idx_use].syn_pkt_send ++;
        }
        if(pflags->idx_mac[1] >= 0)
        {
            p_this_thread->mac_list[pflags->idx_mac[1]].data[idx_use].syn_pkt_recv ++;
        }
    }
}
void slice_statistics::add_packet_info_synack(uint32_t thread_id, c_packet * p_packet, ipslice_flags *pflags)
{
    thread_slice_data *p_this_thread = p_thread_data+thread_id;
    uint32_t idx_use = p_this_thread->idx_use;

    if(pflags->b_flag_inner[0])
    {
        p_this_thread->inner.data[idx_use].syn_ack_pkt_send ++;
    }
    if(pflags->b_flag_inner[1])
    {
        p_this_thread->inner.data[idx_use].syn_ack_pkt_recv ++;
    }
    
    if(pflags->b_flag_prc[0])
    {
        p_this_thread->prc.data[idx_use].syn_ack_pkt_send ++;
    }
    if(pflags->b_flag_prc[1])
    {
        p_this_thread->prc.data[idx_use].syn_ack_pkt_recv ++;
    }
    
    if(pflags->b_flag_outland[0])
    {
        p_this_thread->outland.data[idx_use].syn_ack_pkt_send ++;
    }
    if(pflags->b_flag_outland[1])
    {
        p_this_thread->outland.data[idx_use].syn_ack_pkt_recv ++;
    }
    if(0 == p_packet->Directory)
    {
        p_this_thread->tcp_port[p_packet->dst_port].data[idx_use].syn_ack_pkt_recv ++;
    }
    else
    {
        p_this_thread->tcp_port[p_packet->src_port].data[idx_use].syn_ack_pkt_send ++;
    }
//appid how?
    if(ipseg_num)
    {
        if(pflags->idx_ipseg[0] >= 0)
        {
            p_this_thread->ipseg_list[pflags->idx_ipseg[0]].data[idx_use].syn_ack_pkt_send ++;
        }
        if(pflags->idx_ipseg[1] >= 0)
        {
            p_this_thread->ipseg_list[pflags->idx_ipseg[1]].data[idx_use].syn_ack_pkt_recv ++;
        }
    }
    if(ip_num)
    {
        if(pflags->idx_ip[0] >= 0)
        {
            p_this_thread->ip_list[pflags->idx_ip[0]].data[idx_use].syn_ack_pkt_send ++;
        }
        if(pflags->idx_ip[1] >= 0)
        {
            p_this_thread->ip_list[pflags->idx_ip[1]].data[idx_use].syn_ack_pkt_recv ++;
        }
    }
    if(p_packet->has_mac())
    {
        if(pflags->idx_mac[0] >= 0)
        {
            p_this_thread->mac_list[pflags->idx_mac[0]].data[idx_use].syn_ack_pkt_send ++;
        }
        if(pflags->idx_mac[1] >= 0)
        {
            p_this_thread->mac_list[pflags->idx_mac[1]].data[idx_use].syn_ack_pkt_recv ++;
        }
    }
}
void slice_statistics::add_packet_info_fin(uint32_t thread_id, c_packet * p_packet, ipslice_flags *pflags)
{
    thread_slice_data *p_this_thread = p_thread_data+thread_id;
    uint32_t idx_use = p_this_thread->idx_use;

    if(pflags->b_flag_inner[0])
    {
        p_this_thread->inner.data[idx_use].fin_pkt_send ++;
    }
    if(pflags->b_flag_inner[1])
    {
        p_this_thread->inner.data[idx_use].fin_pkt_recv ++;
    }
    
    if(pflags->b_flag_prc[0])
    {
        p_this_thread->prc.data[idx_use].fin_pkt_send ++;
    }
    if(pflags->b_flag_prc[1])
    {
        p_this_thread->prc.data[idx_use].fin_pkt_recv ++;
    }
    
    if(pflags->b_flag_outland[0])
    {
        p_this_thread->outland.data[idx_use].fin_pkt_send ++;
    }
    if(pflags->b_flag_outland[1])
    {
        p_this_thread->outland.data[idx_use].fin_pkt_recv ++;
    }
    if(0 == p_packet->Directory)
    {
        p_this_thread->tcp_port[p_packet->dst_port].data[idx_use].fin_pkt_recv ++;
    }
    else
    {
        p_this_thread->tcp_port[p_packet->src_port].data[idx_use].fin_pkt_send ++;
    }
//appid how?
    if(ipseg_num)
    {
        if(pflags->idx_ipseg[0] >= 0)
        {
            p_this_thread->ipseg_list[pflags->idx_ipseg[0]].data[idx_use].fin_pkt_send ++;
        }
        if(pflags->idx_ipseg[1] >= 0)
        {
            p_this_thread->ipseg_list[pflags->idx_ipseg[1]].data[idx_use].fin_pkt_recv ++;
        }
    }
    if(ip_num)
    {
        if(pflags->idx_ip[0] >= 0)
        {
            p_this_thread->ip_list[pflags->idx_ip[0]].data[idx_use].fin_pkt_send ++;
        }
        if(pflags->idx_ip[1] >= 0)
        {
            p_this_thread->ip_list[pflags->idx_ip[1]].data[idx_use].fin_pkt_recv ++;
        }
    }
    if(p_packet->has_mac())
    {
        if(pflags->idx_mac[0] >= 0)
        {
            p_this_thread->mac_list[pflags->idx_mac[0]].data[idx_use].fin_pkt_send ++;
        }
        if(pflags->idx_mac[1] >= 0)
        {
            p_this_thread->mac_list[pflags->idx_mac[1]].data[idx_use].fin_pkt_recv ++;
        }
    }
}
void slice_statistics::add_packet_info(uint32_t thread_id, session_pub * p_session , c_packet * p_packet)
{
    thread_slice_data *p_this_thread = p_thread_data+thread_id;
    uint32_t idx_use = p_this_thread->idx_use;

//init ipslice_flags
    ipslice_flags pkt_flags;
    pkt_flags.b_flag_inner[0]      = (0 != (p_packet->IO_Sign[0] & INTRA_IP_L2DEFENSE));
    pkt_flags.b_flag_inner[1]      = (0 != (p_packet->IO_Sign[1] & INTRA_IP_L2DEFENSE));
    pkt_flags.b_flag_prc[0]        = (0 == (p_packet->IO_Sign[0] & (OUTLAND_IP_L2DEFENSE|PRIVATE_IP_L2DEFENSE)));
    pkt_flags.b_flag_prc[1]        = (0 == (p_packet->IO_Sign[1] & (OUTLAND_IP_L2DEFENSE|PRIVATE_IP_L2DEFENSE)));
    pkt_flags.b_flag_outland[0]    = (0 != (p_packet->IO_Sign[0] & OUTLAND_IP_L2DEFENSE));
    pkt_flags.b_flag_outland[1]    = (0 != (p_packet->IO_Sign[1] & OUTLAND_IP_L2DEFENSE));
    pkt_flags.idx_ipseg[0] = -1;
    pkt_flags.idx_ipseg[1] = -1;
    if(ipseg_num)
    {
        if(false == p_packet->src_ip.IsIPv6())
        {
            uint32_t ssip = 0;
            p_packet->src_ip.GetIPv4(ssip);
            pkt_flags.idx_ipseg[0] = find_ipseg_idx(ntohl(ssip));
        }
        if(false == p_packet->dst_ip.IsIPv6())
        {
            uint32_t ssip = 0;
            p_packet->dst_ip.GetIPv4(ssip);
            pkt_flags.idx_ipseg[1] = find_ipseg_idx(ntohl(ssip));
        }
    }
    pkt_flags.idx_ip[0] = -1;
    pkt_flags.idx_ip[1] = -1;
    if(ip_num)
    {
        if(false == p_packet->src_ip.IsIPv6())
        {
            uint32_t ssip = 0;
            p_packet->src_ip.GetIPv4(ssip);
            pkt_flags.idx_ip[0] = find_ip_idx(ntohl(ssip));
        }
        if(false == p_packet->dst_ip.IsIPv6())
        {
            uint32_t ssip = 0;
            p_packet->dst_ip.GetIPv4(ssip);
            pkt_flags.idx_ip[1] = find_ip_idx(ntohl(ssip));
        }
    }
    pkt_flags.idx_mac[0] = -1;
    pkt_flags.idx_mac[1] = -1;
    if(p_packet->has_mac())
    {
        pkt_flags.idx_mac[0] = find_mac_idx(thread_id, p_packet->get_src_mac());
        pkt_flags.idx_mac[1] = find_mac_idx(thread_id, p_packet->get_dst_mac());
    }
//init ipslice_flags --end
    if(p_session)
    {
        if(1 == p_session->session_basic.pPacketNum[0] && 0 == p_session->session_basic.pPacketNum[1])
        {
            first_packet(thread_id, p_session, p_packet, &pkt_flags);
        }
//appid first_packet
        if((p_session->session_basic.ProtoSign >= APPID_START) && (0 == new_app_flag[thread_id][p_session->session_basic.ConnectID]))
        {
            p_this_thread->app_list[p_session->session_basic.ProtoSign-APPID_START].data[idx_use].ss_new ++;
            p_this_thread->app_list[p_session->session_basic.ProtoSign-APPID_START].data[idx_use].ss_active ++;
            new_app_flag[thread_id][p_session->session_basic.ConnectID] = 1;
        }
//appid first_packet --end
    }

    p_this_thread->all.data[idx_use].bytes += p_packet->packet_len;
    p_this_thread->all.data[idx_use].pkts ++;
    if(pkt_flags.b_flag_inner[0]||pkt_flags.b_flag_inner[1])
    {
        p_this_thread->inner.data[idx_use].bytes += p_packet->packet_len;
        p_this_thread->inner.data[idx_use].pkts ++;
        if(pkt_flags.b_flag_inner[0])
        {
            p_this_thread->inner.data[idx_use].bytes_send += p_packet->packet_len;
            p_this_thread->inner.data[idx_use].pkts_send ++;
        }
        if(pkt_flags.b_flag_inner[1])
        {
            p_this_thread->inner.data[idx_use].bytes_recv += p_packet->packet_len;
            p_this_thread->inner.data[idx_use].pkts_recv ++;
        }
    }

    if(pkt_flags.b_flag_prc[0]||pkt_flags.b_flag_prc[1])
    {
        p_this_thread->prc.data[idx_use].bytes += p_packet->packet_len;
        p_this_thread->prc.data[idx_use].pkts ++;
        if(pkt_flags.b_flag_prc[0])
        {
            p_this_thread->prc.data[idx_use].bytes_send += p_packet->packet_len;
            p_this_thread->prc.data[idx_use].pkts_send ++;
        }
        if(pkt_flags.b_flag_prc[1])
        {
            p_this_thread->prc.data[idx_use].bytes_recv += p_packet->packet_len;
            p_this_thread->prc.data[idx_use].pkts_recv ++;
        }
    }
    
    if(pkt_flags.b_flag_outland[0]||pkt_flags.b_flag_outland[1])
    {
        p_this_thread->outland.data[idx_use].bytes += p_packet->packet_len;
        p_this_thread->outland.data[idx_use].pkts ++;
        if(pkt_flags.b_flag_outland[0])
        {
            p_this_thread->outland.data[idx_use].bytes_send += p_packet->packet_len;
            p_this_thread->outland.data[idx_use].pkts_send ++;
        }
        if(pkt_flags.b_flag_outland[1])
        {
            p_this_thread->outland.data[idx_use].bytes_recv += p_packet->packet_len;
            p_this_thread->outland.data[idx_use].pkts_recv ++;
        }
    }

    if(6 == p_packet->u_tcp)
    {
        p_this_thread->tcp.data[idx_use].bytes += p_packet->packet_len;
        p_this_thread->tcp.data[idx_use].pkts ++;
        if(0 == p_packet->Directory)
        {
            p_this_thread->tcp_port[p_packet->dst_port].data[idx_use].bytes += p_packet->packet_len;
            p_this_thread->tcp_port[p_packet->dst_port].data[idx_use].pkts ++;
            p_this_thread->tcp_port[p_packet->dst_port].data[idx_use].bytes_recv += p_packet->packet_len;
            p_this_thread->tcp_port[p_packet->dst_port].data[idx_use].pkts_recv ++;
        }
        else
        {
            p_this_thread->tcp_port[p_packet->src_port].data[idx_use].bytes += p_packet->packet_len;
            p_this_thread->tcp_port[p_packet->src_port].data[idx_use].pkts ++;
            p_this_thread->tcp_port[p_packet->src_port].data[idx_use].bytes_send += p_packet->packet_len;
            p_this_thread->tcp_port[p_packet->src_port].data[idx_use].pkts_send ++;
        }
        if(p_packet->p_tcphdr)
        {
            uint8_t *ptcp = (uint8_t *)(p_packet->p_tcphdr);
            uint8_t tcpflag = ptcp[13];
            if(tcpflag == 0x02)
            {
                add_packet_info_syn(thread_id, p_packet, &pkt_flags);
            }
            else if(tcpflag == 0x12)
            {
                add_packet_info_synack(thread_id, p_packet, &pkt_flags);
            }
            if(tcpflag & 0x01)
            {
                add_packet_info_fin(thread_id, p_packet, &pkt_flags);
            }
        }
    }
    else if(17 == p_packet->u_tcp)
    {
        p_this_thread->udp.data[idx_use].bytes += p_packet->packet_len;
        p_this_thread->udp.data[idx_use].pkts ++;
        if(0 == p_packet->Directory)
        {
            p_this_thread->udp_port[p_packet->dst_port].data[idx_use].bytes += p_packet->packet_len;
            p_this_thread->udp_port[p_packet->dst_port].data[idx_use].pkts ++;
            p_this_thread->udp_port[p_packet->dst_port].data[idx_use].bytes_recv += p_packet->packet_len;
            p_this_thread->udp_port[p_packet->dst_port].data[idx_use].pkts_recv ++;
        }
        else
        {
            p_this_thread->udp_port[p_packet->src_port].data[idx_use].bytes += p_packet->packet_len;
            p_this_thread->udp_port[p_packet->src_port].data[idx_use].pkts ++;
            p_this_thread->udp_port[p_packet->src_port].data[idx_use].bytes_send += p_packet->packet_len;
            p_this_thread->udp_port[p_packet->src_port].data[idx_use].pkts_send ++;
        }
    }
    if(p_session && (p_session->session_basic.ProtoSign >= APPID_START))
    {
        int appid_idx = p_session->session_basic.ProtoSign - APPID_START;
        p_this_thread->app_list[appid_idx].data[idx_use].bytes += p_packet->packet_len;
        p_this_thread->app_list[appid_idx].data[idx_use].pkts ++;
        if(((p_packet->Directory == 0) && (p_session->session_basic.Server == 1)) || ((p_packet->Directory == 1) && (p_session->session_basic.Server == 2)))
        {
            //c2s
            p_this_thread->app_list[appid_idx].data[idx_use].bytes_send += p_packet->packet_len;
            p_this_thread->app_list[appid_idx].data[idx_use].pkts_send ++;
        }
        else if(((p_packet->Directory == 0) && (p_session->session_basic.Server == 2)) || ((p_packet->Directory == 1) && (p_session->session_basic.Server == 1)))
        {
            //s2c
            p_this_thread->app_list[appid_idx].data[idx_use].bytes_recv += p_packet->packet_len;
            p_this_thread->app_list[appid_idx].data[idx_use].pkts_recv ++;
        }
    }
    if(ipseg_num)
    {
        if(pkt_flags.idx_ipseg[0] >= 0)
        {
            p_this_thread->ipseg_list[pkt_flags.idx_ipseg[0]].data[idx_use].bytes += p_packet->packet_len;
            p_this_thread->ipseg_list[pkt_flags.idx_ipseg[0]].data[idx_use].pkts ++;
            p_this_thread->ipseg_list[pkt_flags.idx_ipseg[0]].data[idx_use].bytes_send += p_packet->packet_len;
            p_this_thread->ipseg_list[pkt_flags.idx_ipseg[0]].data[idx_use].pkts_send ++;
        }
        if(pkt_flags.idx_ipseg[1] >= 0)
        {
            if(pkt_flags.idx_ipseg[1] != pkt_flags.idx_ipseg[0])
            {
                p_this_thread->ipseg_list[pkt_flags.idx_ipseg[1]].data[idx_use].bytes += p_packet->packet_len;
                p_this_thread->ipseg_list[pkt_flags.idx_ipseg[1]].data[idx_use].pkts ++;
            }
            p_this_thread->ipseg_list[pkt_flags.idx_ipseg[1]].data[idx_use].bytes_recv += p_packet->packet_len;
            p_this_thread->ipseg_list[pkt_flags.idx_ipseg[1]].data[idx_use].pkts_recv ++;
        }
    }
    if(ip_num)
    {
        if(pkt_flags.idx_ip[0] >= 0)
        {
            p_this_thread->ip_list[pkt_flags.idx_ip[0]].data[idx_use].bytes += p_packet->packet_len;
            p_this_thread->ip_list[pkt_flags.idx_ip[0]].data[idx_use].pkts ++;
            p_this_thread->ip_list[pkt_flags.idx_ip[0]].data[idx_use].bytes_send += p_packet->packet_len;
            p_this_thread->ip_list[pkt_flags.idx_ip[0]].data[idx_use].pkts_send ++;
        }
        if(pkt_flags.idx_ip[1] >= 0)
        {
            if(pkt_flags.idx_ip[1] != pkt_flags.idx_ip[0])
            {
                p_this_thread->ip_list[pkt_flags.idx_ip[1]].data[idx_use].bytes += p_packet->packet_len;
                p_this_thread->ip_list[pkt_flags.idx_ip[1]].data[idx_use].pkts ++;
            }
            p_this_thread->ip_list[pkt_flags.idx_ip[1]].data[idx_use].bytes_recv += p_packet->packet_len;
            p_this_thread->ip_list[pkt_flags.idx_ip[1]].data[idx_use].pkts_recv ++;
        }
    }
    if(p_packet->has_mac())
    {
        if(pkt_flags.idx_mac[0] >= 0)
        {
            p_this_thread->mac_list[pkt_flags.idx_mac[0]].data[idx_use].bytes += p_packet->packet_len;
            p_this_thread->mac_list[pkt_flags.idx_mac[0]].data[idx_use].pkts ++;
            p_this_thread->mac_list[pkt_flags.idx_mac[0]].data[idx_use].bytes_send += p_packet->packet_len;
            p_this_thread->mac_list[pkt_flags.idx_mac[0]].data[idx_use].pkts_send ++;
        }
        if(pkt_flags.idx_mac[1] >= 0)
        {
            if(pkt_flags.idx_mac[1] != pkt_flags.idx_mac[0])
            {
                p_this_thread->mac_list[pkt_flags.idx_mac[1]].data[idx_use].bytes += p_packet->packet_len;
                p_this_thread->mac_list[pkt_flags.idx_mac[1]].data[idx_use].pkts ++;
            }
            p_this_thread->mac_list[pkt_flags.idx_mac[1]].data[idx_use].bytes_recv += p_packet->packet_len;
            p_this_thread->mac_list[pkt_flags.idx_mac[1]].data[idx_use].pkts_recv ++;
        }
    }
}

static void _merge_slice_info(slice_info *ptmp, slice_info *padd, int idx2send)
{
    ptmp->data[0].ss_active += padd->data[idx2send].ss_active;
    ptmp->data[0].bytes += padd->data[idx2send].bytes;
    ptmp->data[0].bytes_send += padd->data[idx2send].bytes_send;
    ptmp->data[0].bytes_recv += padd->data[idx2send].bytes_recv;
    ptmp->data[0].pkts += padd->data[idx2send].pkts;
    ptmp->data[0].pkts_send += padd->data[idx2send].pkts_send;
    ptmp->data[0].pkts_recv += padd->data[idx2send].pkts_recv;
    ptmp->data[0].syn_pkt_send += padd->data[idx2send].syn_pkt_send;
    ptmp->data[0].syn_pkt_recv += padd->data[idx2send].syn_pkt_recv;
    ptmp->data[0].syn_ack_pkt_send += padd->data[idx2send].syn_ack_pkt_send;
    ptmp->data[0].syn_ack_pkt_recv += padd->data[idx2send].syn_ack_pkt_recv;
    ptmp->data[0].fin_pkt_send += padd->data[idx2send].fin_pkt_send;
    ptmp->data[0].fin_pkt_recv += padd->data[idx2send].fin_pkt_recv;
    ptmp->data[0].ss_new += padd->data[idx2send].ss_new;
    ptmp->data[0].ss_close += padd->data[idx2send].ss_close;
}
void slice_statistics::_merge_thread_slice_data()
{
    uint32_t idx_useing = p_thread_data[0].idx_use;
    uint32_t idx2send = (idx_useing + 1) % 2;
    memset(p_merge_data, 0, sizeof(thread_slice_data));
    merge_mac_map.clear();
    std::map<uint64_t, uint32_t>::iterator iter;
//merge mac list
    for(int i =0; i < thread_num; i ++)
    {
        for(uint32_t j = 0; j < (p_thread_data[i].mac_count[idx2send]) && (p_merge_data->mac_count[0] < MAX_MAC_SUPPORT); j ++)
        {
            iter = merge_mac_map.find(p_thread_data[i].mac_list[j].key);
            if(iter == merge_mac_map.end())
            {
                merge_mac_map[p_thread_data[i].mac_list[j].key] = p_merge_data->mac_count[0];
                p_merge_data->mac_list[p_merge_data->mac_count[0]].key = p_thread_data[i].mac_list[j].key;
                p_merge_data->mac_count[0] ++;
            }
        }
    }
//merge mac list    --end

    for(int i =0; i <thread_num; i ++)
    {
        _merge_slice_info(&p_merge_data->all, &p_thread_data[i].all, idx2send);
        _merge_slice_info(&p_merge_data->inner, &p_thread_data[i].inner, idx2send);
        _merge_slice_info(&p_merge_data->prc, &p_thread_data[i].prc, idx2send);
        _merge_slice_info(&p_merge_data->outland, &p_thread_data[i].outland, idx2send);
        _merge_slice_info(&p_merge_data->tcp, &p_thread_data[i].tcp, idx2send);
        _merge_slice_info(&p_merge_data->udp, &p_thread_data[i].udp, idx2send);
        for(int j=0; j < 65536; j ++)
        {
            _merge_slice_info(&p_merge_data->tcp_port[j], &p_thread_data[i].tcp_port[j], idx2send);
            _merge_slice_info(&p_merge_data->udp_port[j], &p_thread_data[i].udp_port[j], idx2send);
        }
        for(int j=0; j < APPID_MAX-APPID_START; j ++)
        {
            _merge_slice_info(&p_merge_data->app_list[j], &p_thread_data[i].app_list[j], idx2send);
        }
        for(int j=0; j < ipseg_num; j ++)
        {
            _merge_slice_info(&p_merge_data->ipseg_list[j], &p_thread_data[i].ipseg_list[j], idx2send);
        }
        for(int j=0; j < ip_num; j ++)
        {
            _merge_slice_info(&p_merge_data->ip_list[j], &p_thread_data[i].ip_list[j], idx2send);
        }
        for(int j=0; j < p_merge_data->mac_count[0]; j ++)
        {
            key2idx tmp;
            tmp.key = p_merge_data->mac_list[j].key;
            uint32_t outpos;
            outpos = m_Judge_mac[i].JudgeValue((uint32_t)(tmp.key & 0xffffffff), tmp);
            if(0 != outpos)
            {
                uint32_t idx = m_Array_mac[i].m_pData[outpos].idx;
                if(idx < p_thread_data[i].mac_count[idx2send])
                {
                    _merge_slice_info(&p_merge_data->mac_list[j], &p_thread_data[i].mac_list[idx], idx2send);
                }
            }
        }
    }
}
void slice_statistics::_write(FILE *fp, uint32_t ts_now)
{
    thread_slice_data *pdata = p_merge_data;
    char log[2048];
    char mac_buf[18] = {0};

    snprintf(log, 2048, "{\"type\":\"%s\",\"val\":\"%s\",\
\"ts1\":\"%u\",\"ts2\":\"%u\",\
\"ss_active\":\"%u\",\"ss_new\":\"%u\",\"ss_close\":\"%u\",\
\"bytes\":\"%"PRIu64"\",\"bytes_send\":\"%"PRIu64"\",\"bytes_recv\":\"%"PRIu64"\",\
\"pkts\":\"%"PRIu64"\",\"pkts_send\":\"%"PRIu64"\",\"pkts_recv\":\"%"PRIu64"\",\
\"syn_send\":\"%u\",\"syn_recv\":\"%u\",\"synack_send\":\"%u\",\
\"synack_recv\":\"%u\",\"fin_send\":\"%u\",\"fin_recv\":\"%u\"}",
                "all","all",
                ts_last_send?ts_last_send:ts_system_start, ts_now,
                pdata->all.data[0].ss_active, pdata->all.data[0].ss_new, pdata->all.data[0].ss_close,
                pdata->all.data[0].bytes,pdata->all.data[0].bytes_send, pdata->all.data[0].bytes_recv,
                pdata->all.data[0].pkts,pdata->all.data[0].pkts_send, pdata->all.data[0].pkts_recv,
                pdata->all.data[0].syn_pkt_send,pdata->all.data[0].syn_pkt_recv, pdata->all.data[0].syn_ack_pkt_send,
                pdata->all.data[0].syn_ack_pkt_recv,pdata->all.data[0].fin_pkt_send, pdata->all.data[0].fin_pkt_recv
            );
    fwrite(log, strlen(log), 1, fp);fwrite("\n", 1, 1, fp);fflush(fp);

    snprintf(log, 2048, "{\"type\":\"%s\",\"val\":\"%s\",\
\"ts1\":\"%u\",\"ts2\":\"%u\",\
\"ss_active\":\"%u\",\"ss_new\":\"%u\",\"ss_close\":\"%u\",\
\"bytes\":\"%"PRIu64"\",\"bytes_send\":\"%"PRIu64"\",\"bytes_recv\":\"%"PRIu64"\",\
\"pkts\":\"%"PRIu64"\",\"pkts_send\":\"%"PRIu64"\",\"pkts_recv\":\"%"PRIu64"\",\
\"syn_send\":\"%u\",\"syn_recv\":\"%u\",\"synack_send\":\"%u\",\
\"synack_recv\":\"%u\",\"fin_send\":\"%u\",\"fin_recv\":\"%u\"}",
                "inner","inner",
                ts_last_send?ts_last_send:ts_system_start, ts_now,
                pdata->inner.data[0].ss_active, pdata->inner.data[0].ss_new, pdata->inner.data[0].ss_close,
                pdata->inner.data[0].bytes,pdata->inner.data[0].bytes_send, pdata->inner.data[0].bytes_recv,
                pdata->inner.data[0].pkts,pdata->inner.data[0].pkts_send, pdata->inner.data[0].pkts_recv,
                pdata->inner.data[0].syn_pkt_send,pdata->inner.data[0].syn_pkt_recv, pdata->inner.data[0].syn_ack_pkt_send,
                pdata->inner.data[0].syn_ack_pkt_recv,pdata->inner.data[0].fin_pkt_send, pdata->inner.data[0].fin_pkt_recv
            );
    fwrite(log, strlen(log), 1, fp);fwrite("\n", 1, 1, fp);fflush(fp);

    snprintf(log, 2048, "{\"type\":\"%s\",\"val\":\"%s\",\
\"ts1\":\"%u\",\"ts2\":\"%u\",\
\"ss_active\":\"%u\",\"ss_new\":\"%u\",\"ss_close\":\"%u\",\
\"bytes\":\"%"PRIu64"\",\"bytes_send\":\"%"PRIu64"\",\"bytes_recv\":\"%"PRIu64"\",\
\"pkts\":\"%"PRIu64"\",\"pkts_send\":\"%"PRIu64"\",\"pkts_recv\":\"%"PRIu64"\",\
\"syn_send\":\"%u\",\"syn_recv\":\"%u\",\"synack_send\":\"%u\",\
\"synack_recv\":\"%u\",\"fin_send\":\"%u\",\"fin_recv\":\"%u\"}",
                "prc","prc",
                ts_last_send?ts_last_send:ts_system_start, ts_now,
                pdata->prc.data[0].ss_active, pdata->prc.data[0].ss_new, pdata->prc.data[0].ss_close,
                pdata->prc.data[0].bytes,pdata->prc.data[0].bytes_send, pdata->prc.data[0].bytes_recv,
                pdata->prc.data[0].pkts,pdata->prc.data[0].pkts_send, pdata->prc.data[0].pkts_recv,
                pdata->prc.data[0].syn_pkt_send,pdata->prc.data[0].syn_pkt_recv, pdata->prc.data[0].syn_ack_pkt_send,
                pdata->prc.data[0].syn_ack_pkt_recv,pdata->prc.data[0].fin_pkt_send, pdata->prc.data[0].fin_pkt_recv
            );
    fwrite(log, strlen(log), 1, fp);fwrite("\n", 1, 1, fp);fflush(fp);
    snprintf(log, 2048, "{\"type\":\"%s\",\"val\":\"%s\",\
\"ts1\":\"%u\",\"ts2\":\"%u\",\
\"ss_active\":\"%u\",\"ss_new\":\"%u\",\"ss_close\":\"%u\",\
\"bytes\":\"%"PRIu64"\",\"bytes_send\":\"%"PRIu64"\",\"bytes_recv\":\"%"PRIu64"\",\
\"pkts\":\"%"PRIu64"\",\"pkts_send\":\"%"PRIu64"\",\"pkts_recv\":\"%"PRIu64"\",\
\"syn_send\":\"%u\",\"syn_recv\":\"%u\",\"synack_send\":\"%u\",\
\"synack_recv\":\"%u\",\"fin_send\":\"%u\",\"fin_recv\":\"%u\"}",
                "outland","outland",
                ts_last_send?ts_last_send:ts_system_start, ts_now,
                pdata->outland.data[0].ss_active, pdata->outland.data[0].ss_new, pdata->outland.data[0].ss_close,
                pdata->outland.data[0].bytes,pdata->outland.data[0].bytes_send, pdata->outland.data[0].bytes_recv,
                pdata->outland.data[0].pkts,pdata->outland.data[0].pkts_send, pdata->outland.data[0].pkts_recv,
                pdata->outland.data[0].syn_pkt_send,pdata->outland.data[0].syn_pkt_recv, pdata->outland.data[0].syn_ack_pkt_send,
                pdata->outland.data[0].syn_ack_pkt_recv,pdata->outland.data[0].fin_pkt_send, pdata->outland.data[0].fin_pkt_recv
            );
    fwrite(log, strlen(log), 1, fp);fwrite("\n", 1, 1, fp);fflush(fp);
    snprintf(log, 2048, "{\"type\":\"%s\",\"val\":\"%s\",\
\"ts1\":\"%u\",\"ts2\":\"%u\",\
\"ss_active\":\"%u\",\"ss_new\":\"%u\",\"ss_close\":\"%u\",\
\"bytes\":\"%"PRIu64"\",\"bytes_send\":\"%"PRIu64"\",\"bytes_recv\":\"%"PRIu64"\",\
\"pkts\":\"%"PRIu64"\",\"pkts_send\":\"%"PRIu64"\",\"pkts_recv\":\"%"PRIu64"\",\
\"syn_send\":\"%u\",\"syn_recv\":\"%u\",\"synack_send\":\"%u\",\
\"synack_recv\":\"%u\",\"fin_send\":\"%u\",\"fin_recv\":\"%u\"}",
                "tcp","tcp",
                ts_last_send?ts_last_send:ts_system_start, ts_now,
                pdata->tcp.data[0].ss_active, pdata->tcp.data[0].ss_new, pdata->tcp.data[0].ss_close,
                pdata->tcp.data[0].bytes,pdata->tcp.data[0].bytes_send, pdata->tcp.data[0].bytes_recv,
                pdata->tcp.data[0].pkts,pdata->tcp.data[0].pkts_send, pdata->tcp.data[0].pkts_recv,
                pdata->tcp.data[0].syn_pkt_send,pdata->tcp.data[0].syn_pkt_recv, pdata->tcp.data[0].syn_ack_pkt_send,
                pdata->tcp.data[0].syn_ack_pkt_recv,pdata->tcp.data[0].fin_pkt_send, pdata->tcp.data[0].fin_pkt_recv
            );
    fwrite(log, strlen(log), 1, fp);fwrite("\n", 1, 1, fp);fflush(fp);
    snprintf(log, 2048, "{\"type\":\"%s\",\"val\":\"%s\",\
\"ts1\":\"%u\",\"ts2\":\"%u\",\
\"ss_active\":\"%u\",\"ss_new\":\"%u\",\"ss_close\":\"%u\",\
\"bytes\":\"%"PRIu64"\",\"bytes_send\":\"%"PRIu64"\",\"bytes_recv\":\"%"PRIu64"\",\
\"pkts\":\"%"PRIu64"\",\"pkts_send\":\"%"PRIu64"\",\"pkts_recv\":\"%"PRIu64"\",\
\"syn_send\":\"%u\",\"syn_recv\":\"%u\",\"synack_send\":\"%u\",\
\"synack_recv\":\"%u\",\"fin_send\":\"%u\",\"fin_recv\":\"%u\"}",
                "udp","udp",
                ts_last_send?ts_last_send:ts_system_start, ts_now,
                pdata->udp.data[0].ss_active, pdata->udp.data[0].ss_new, pdata->udp.data[0].ss_close,
                pdata->udp.data[0].bytes,pdata->udp.data[0].bytes_send, pdata->udp.data[0].bytes_recv,
                pdata->udp.data[0].pkts,pdata->udp.data[0].pkts_send, pdata->udp.data[0].pkts_recv,
                pdata->udp.data[0].syn_pkt_send,pdata->udp.data[0].syn_pkt_recv, pdata->udp.data[0].syn_ack_pkt_send,
                pdata->udp.data[0].syn_ack_pkt_recv,pdata->udp.data[0].fin_pkt_send, pdata->udp.data[0].fin_pkt_recv
            );
    fwrite(log, strlen(log), 1, fp);fwrite("\n", 1, 1, fp);fflush(fp);
    for(int i=0; i < 65536; i ++)
    {
        if(pdata->tcp_port[i].data[0].ss_active || pdata->tcp_port[i].data[0].pkts || pdata->tcp_port[i].data[0].ss_close)
        {
            snprintf(log, 2048, "{\"type\":\"%s\",\"val\":\"%d\",\
\"ts1\":\"%u\",\"ts2\":\"%u\",\
\"ss_active\":\"%u\",\"ss_new\":\"%u\",\"ss_close\":\"%u\",\
\"bytes\":\"%"PRIu64"\",\"bytes_send\":\"%"PRIu64"\",\"bytes_recv\":\"%"PRIu64"\",\
\"pkts\":\"%"PRIu64"\",\"pkts_send\":\"%"PRIu64"\",\"pkts_recv\":\"%"PRIu64"\",\
\"syn_send\":\"%u\",\"syn_recv\":\"%u\",\"synack_send\":\"%u\",\
\"synack_recv\":\"%u\",\"fin_send\":\"%u\",\"fin_recv\":\"%u\"}",
                "tcp_sport",i,
                ts_last_send?ts_last_send:ts_system_start, ts_now,
                pdata->tcp_port[i].data[0].ss_active, pdata->tcp_port[i].data[0].ss_new, pdata->tcp_port[i].data[0].ss_close,
                pdata->tcp_port[i].data[0].bytes,pdata->tcp_port[i].data[0].bytes_send, pdata->tcp_port[i].data[0].bytes_recv,
                pdata->tcp_port[i].data[0].pkts,pdata->tcp_port[i].data[0].pkts_send, pdata->tcp_port[i].data[0].pkts_recv,
                pdata->tcp_port[i].data[0].syn_pkt_send,pdata->tcp_port[i].data[0].syn_pkt_recv, pdata->tcp_port[i].data[0].syn_ack_pkt_send,
                pdata->tcp_port[i].data[0].syn_ack_pkt_recv,pdata->tcp_port[i].data[0].fin_pkt_send, pdata->tcp_port[i].data[0].fin_pkt_recv
            );
            fwrite(log, strlen(log), 1, fp);fwrite("\n", 1, 1, fp);fflush(fp);
        }
    }
    for(int i=0; i < 65536; i ++)
    {
        if(pdata->udp_port[i].data[0].ss_active || pdata->udp_port[i].data[0].pkts || pdata->udp_port[i].data[0].ss_close)
        {
            snprintf(log, 2048, "{\"type\":\"%s\",\"val\":\"%d\",\
\"ts1\":\"%u\",\"ts2\":\"%u\",\
\"ss_active\":\"%u\",\"ss_new\":\"%u\",\"ss_close\":\"%u\",\
\"bytes\":\"%"PRIu64"\",\"bytes_send\":\"%"PRIu64"\",\"bytes_recv\":\"%"PRIu64"\",\
\"pkts\":\"%"PRIu64"\",\"pkts_send\":\"%"PRIu64"\",\"pkts_recv\":\"%"PRIu64"\",\
\"syn_send\":\"%u\",\"syn_recv\":\"%u\",\"synack_send\":\"%u\",\
\"synack_recv\":\"%u\",\"fin_send\":\"%u\",\"fin_recv\":\"%u\"}",
                "udp_sport",i,
                ts_last_send?ts_last_send:ts_system_start, ts_now,
                pdata->udp_port[i].data[0].ss_active, pdata->udp_port[i].data[0].ss_new, pdata->udp_port[i].data[0].ss_close,
                pdata->udp_port[i].data[0].bytes,pdata->udp_port[i].data[0].bytes_send, pdata->udp_port[i].data[0].bytes_recv,
                pdata->udp_port[i].data[0].pkts,pdata->udp_port[i].data[0].pkts_send, pdata->udp_port[i].data[0].pkts_recv,
                pdata->udp_port[i].data[0].syn_pkt_send,pdata->udp_port[i].data[0].syn_pkt_recv, pdata->udp_port[i].data[0].syn_ack_pkt_send,
                pdata->udp_port[i].data[0].syn_ack_pkt_recv,pdata->udp_port[i].data[0].fin_pkt_send, pdata->udp_port[i].data[0].fin_pkt_recv
            );
            fwrite(log, strlen(log), 1, fp);fwrite("\n", 1, 1, fp);fflush(fp);
        }
    }
    for(int i=0; i < APPID_MAX-APPID_START; i ++)
    {
        if(pdata->app_list[i].data[0].ss_active || pdata->app_list[i].data[0].pkts || pdata->app_list[i].data[0].ss_close)
        {
            snprintf(log, 2048, "{\"type\":\"%s\",\"val\":\"%d\",\
\"ts1\":\"%u\",\"ts2\":\"%u\",\
\"ss_active\":\"%u\",\"ss_new\":\"%u\",\"ss_close\":\"%u\",\
\"bytes\":\"%"PRIu64"\",\"bytes_send\":\"%"PRIu64"\",\"bytes_recv\":\"%"PRIu64"\",\
\"pkts\":\"%"PRIu64"\",\"pkts_send\":\"%"PRIu64"\",\"pkts_recv\":\"%"PRIu64"\",\
\"syn_send\":\"%u\",\"syn_recv\":\"%u\",\"synack_send\":\"%u\",\
\"synack_recv\":\"%u\",\"fin_send\":\"%u\",\"fin_recv\":\"%u\"}",
                "app",i+APPID_START,
                ts_last_send?ts_last_send:ts_system_start, ts_now,
                pdata->app_list[i].data[0].ss_active, pdata->app_list[i].data[0].ss_new, pdata->app_list[i].data[0].ss_close,
                pdata->app_list[i].data[0].bytes,pdata->app_list[i].data[0].bytes_send, pdata->app_list[i].data[0].bytes_recv,
                pdata->app_list[i].data[0].pkts,pdata->app_list[i].data[0].pkts_send, pdata->app_list[i].data[0].pkts_recv,
                pdata->app_list[i].data[0].syn_pkt_send,pdata->app_list[i].data[0].syn_pkt_recv, pdata->app_list[i].data[0].syn_ack_pkt_send,
                pdata->app_list[i].data[0].syn_ack_pkt_recv,pdata->app_list[i].data[0].fin_pkt_send, pdata->app_list[i].data[0].fin_pkt_recv
            );
            fwrite(log, strlen(log), 1, fp);fwrite("\n", 1, 1, fp);fflush(fp);
        }
    }
    for(int i=0; i < ipseg_num; i ++)
    {
        if(pdata->ipseg_list[i].data[0].ss_active || pdata->ipseg_list[i].data[0].pkts || pdata->ipseg_list[i].data[0].ss_close)
        {
            snprintf(log, 2048, "{\"type\":\"%s\",\"val\":\"%s\",\
\"ts1\":\"%u\",\"ts2\":\"%u\",\
\"ss_active\":\"%u\",\"ss_new\":\"%u\",\"ss_close\":\"%u\",\
\"bytes\":\"%"PRIu64"\",\"bytes_send\":\"%"PRIu64"\",\"bytes_recv\":\"%"PRIu64"\",\
\"pkts\":\"%"PRIu64"\",\"pkts_send\":\"%"PRIu64"\",\"pkts_recv\":\"%"PRIu64"\",\
\"syn_send\":\"%u\",\"syn_recv\":\"%u\",\"synack_send\":\"%u\",\
\"synack_recv\":\"%u\",\"fin_send\":\"%u\",\"fin_recv\":\"%u\"}",
                "ipseg",ipseg_str[i].c_str(),
                ts_last_send?ts_last_send:ts_system_start, ts_now,
                pdata->ipseg_list[i].data[0].ss_active, pdata->ipseg_list[i].data[0].ss_new, pdata->ipseg_list[i].data[0].ss_close,
                pdata->ipseg_list[i].data[0].bytes,pdata->ipseg_list[i].data[0].bytes_send, pdata->ipseg_list[i].data[0].bytes_recv,
                pdata->ipseg_list[i].data[0].pkts,pdata->ipseg_list[i].data[0].pkts_send, pdata->ipseg_list[i].data[0].pkts_recv,
                pdata->ipseg_list[i].data[0].syn_pkt_send,pdata->ipseg_list[i].data[0].syn_pkt_recv, pdata->ipseg_list[i].data[0].syn_ack_pkt_send,
                pdata->ipseg_list[i].data[0].syn_ack_pkt_recv,pdata->ipseg_list[i].data[0].fin_pkt_send, pdata->ipseg_list[i].data[0].fin_pkt_recv
            );
            fwrite(log, strlen(log), 1, fp);fwrite("\n", 1, 1, fp);fflush(fp);
        }
    }
    for(int i=0; i < ip_num; i ++)
    {
        if(pdata->ip_list[i].data[0].ss_active || pdata->ip_list[i].data[0].pkts || pdata->ip_list[i].data[0].ss_close)
        {
            snprintf(log, 2048, "{\"type\":\"%s\",\"val\":\"%s\",\
\"ts1\":\"%u\",\"ts2\":\"%u\",\
\"ss_active\":\"%u\",\"ss_new\":\"%u\",\"ss_close\":\"%u\",\
\"bytes\":\"%"PRIu64"\",\"bytes_send\":\"%"PRIu64"\",\"bytes_recv\":\"%"PRIu64"\",\
\"pkts\":\"%"PRIu64"\",\"pkts_send\":\"%"PRIu64"\",\"pkts_recv\":\"%"PRIu64"\",\
\"syn_send\":\"%u\",\"syn_recv\":\"%u\",\"synack_send\":\"%u\",\
\"synack_recv\":\"%u\",\"fin_send\":\"%u\",\"fin_recv\":\"%u\"}",
                "ip",ip_str[i].c_str(),
                ts_last_send?ts_last_send:ts_system_start, ts_now,
                pdata->ip_list[i].data[0].ss_active, pdata->ip_list[i].data[0].ss_new, pdata->ip_list[i].data[0].ss_close,
                pdata->ip_list[i].data[0].bytes,pdata->ip_list[i].data[0].bytes_send, pdata->ip_list[i].data[0].bytes_recv,
                pdata->ip_list[i].data[0].pkts,pdata->ip_list[i].data[0].pkts_send, pdata->ip_list[i].data[0].pkts_recv,
                pdata->ip_list[i].data[0].syn_pkt_send,pdata->ip_list[i].data[0].syn_pkt_recv, pdata->ip_list[i].data[0].syn_ack_pkt_send,
                pdata->ip_list[i].data[0].syn_ack_pkt_recv,pdata->ip_list[i].data[0].fin_pkt_send, pdata->ip_list[i].data[0].fin_pkt_recv
            );
            fwrite(log, strlen(log), 1, fp);fwrite("\n", 1, 1, fp);fflush(fp);
        }
    }
    for(int i=0; i < pdata->mac_count[0]; i ++)
    {
        if(pdata->mac_list[i].data[0].ss_active || pdata->mac_list[i].data[0].pkts || pdata->mac_list[i].data[0].ss_close)
        {
            unsigned char * pMac = (unsigned char *)&pdata->mac_list[i].key;
            snprintf(mac_buf, 18, "%02x:%02x:%02x:%02x:%02x:%02x",pMac[5],pMac[4],pMac[3],pMac[2],pMac[1],pMac[0]);
            snprintf(log, 2048, "{\"type\":\"%s\",\"val\":\"%s\",\
\"ts1\":\"%u\",\"ts2\":\"%u\",\
\"ss_active\":\"%u\",\"ss_new\":\"%u\",\"ss_close\":\"%u\",\
\"bytes\":\"%"PRIu64"\",\"bytes_send\":\"%"PRIu64"\",\"bytes_recv\":\"%"PRIu64"\",\
\"pkts\":\"%"PRIu64"\",\"pkts_send\":\"%"PRIu64"\",\"pkts_recv\":\"%"PRIu64"\",\
\"syn_send\":\"%u\",\"syn_recv\":\"%u\",\"synack_send\":\"%u\",\
\"synack_recv\":\"%u\",\"fin_send\":\"%u\",\"fin_recv\":\"%u\"}",
                "mac", mac_buf,
                ts_last_send?ts_last_send:ts_system_start, ts_now,
                pdata->mac_list[i].data[0].ss_active, pdata->mac_list[i].data[0].ss_new, pdata->mac_list[i].data[0].ss_close,
                pdata->mac_list[i].data[0].bytes,pdata->mac_list[i].data[0].bytes_send, pdata->mac_list[i].data[0].bytes_recv,
                pdata->mac_list[i].data[0].pkts,pdata->mac_list[i].data[0].pkts_send, pdata->mac_list[i].data[0].pkts_recv,
                pdata->mac_list[i].data[0].syn_pkt_send,pdata->mac_list[i].data[0].syn_pkt_recv, pdata->mac_list[i].data[0].syn_ack_pkt_send,
                pdata->mac_list[i].data[0].syn_ack_pkt_recv,pdata->mac_list[i].data[0].fin_pkt_send, pdata->mac_list[i].data[0].fin_pkt_recv
            );
            fwrite(log, strlen(log), 1, fp);fwrite("\n", 1, 1, fp);fflush(fp);
        }
    }
}
void slice_statistics::send_log(uint32_t time_s)
{
    uint8_t b_send = 0;
    uint32_t ts_now = p_thread_data[0].ts_last;
    if(ts_now > ts_last_send)
    {
        for(int i = 0; i < 1000; i ++)  //retry 1000 times in 1 second
        {
            int j = 0;
            for(j = 0; j < thread_num; j ++)
            {
                if(p_thread_data[j].ts_last != ts_now)
                {
                    break;
                }
            }
            if(j == thread_num)
            {
                b_send = 1;
                break;
            }
            else
            {
                usleep(1000);
            }
        }
    }
    if(b_send)
    {
        uint32_t idx_useing = p_thread_data[0].idx_use;
        uint32_t idx_2_send = (idx_useing + 1) % 2;
        char file_path[1024];
        if(b_seg_analysis)
        {
            snprintf(file_path, 1023, "%s/%d/%d.sa.json", output_path.c_str(), ts_now/(3600*24), ts_now/(3600));
        }
        else
        {
            snprintf(file_path, 1023, "%s/%d/%d.json", output_path.c_str(), ts_now/(3600*24), ts_now/(3600));
        }
        string filename(file_path);
        std::string path(filename , 0 , filename.rfind("/")+1);
        create_path(path);
        
        FILE *fp = fopen(filename.c_str(),"a+");
        if(fp)
        {
            _merge_thread_slice_data();
            _write(fp, ts_now);
            fclose(fp);
        }
        _clean_data();
        ts_last_send = ts_now;
    }
}
void  slice_statistics::_clean_data()
{
    uint32_t idx_useing = p_thread_data[0].idx_use;
    uint32_t idx_2_clean = (idx_useing + 1) % 2;
    for(int i = 0; i < thread_num; i ++)
    {
        thread_slice_data *pt_data = &p_thread_data[i];
        memset(&pt_data->all.data[idx_2_clean], 0, sizeof(slice_info_ab));
        memset(&pt_data->inner.data[idx_2_clean], 0, sizeof(slice_info_ab));
        memset(&pt_data->prc.data[idx_2_clean], 0, sizeof(slice_info_ab));
        memset(&pt_data->outland.data[idx_2_clean], 0, sizeof(slice_info_ab));
        memset(&pt_data->tcp.data[idx_2_clean], 0, sizeof(slice_info_ab));
        memset(&pt_data->udp.data[idx_2_clean], 0, sizeof(slice_info_ab));
        for(int j=0; j < 65535;j ++)
        {
            memset(&pt_data->tcp_port[j].data[idx_2_clean], 0, sizeof(slice_info_ab));
            memset(&pt_data->udp_port[j].data[idx_2_clean], 0, sizeof(slice_info_ab));
        }
        for(int j=0; j < APPID_MAX-APPID_START; j ++)
        {
            memset(&pt_data->app_list[j].data[idx_2_clean], 0, sizeof(slice_info_ab));
        }
        for(int j=0; j < ipseg_num; j ++)
        {
            memset(&pt_data->ipseg_list[j].data[idx_2_clean], 0, sizeof(slice_info_ab));
        }
        for(int j=0; j < ip_num; j ++)
        {
            memset(&pt_data->ip_list[j].data[idx_2_clean], 0, sizeof(slice_info_ab));
        }
        for(int j=0; j < MAX_MAC_SUPPORT; j ++)
        {
            memset(&pt_data->mac_list[j].data[idx_2_clean], 0, sizeof(slice_info_ab));
        }
    }
}
void  slice_statistics::recovery(uint32_t thread_id, session_pub * p_session)
{
    thread_slice_data *p_this_thread = p_thread_data+thread_id;
    uint32_t idx_use = p_this_thread->idx_use;

//appid trans_app
    if(p_session->session_basic.ProtoSign >= APPID_START)
    {
        if((10000 == p_session->session_basic.ProtoSign) || ((10701 <= p_session->session_basic.ProtoSign) && (10708 >=p_session->session_basic.ProtoSign)))
        {
            int appid_idx = p_session->session_basic.ProtoSign - APPID_START;
            p_this_thread->app_list[appid_idx].data[idx_use].ss_new ++;
            p_this_thread->app_list[appid_idx].data[idx_use].ss_active ++;
// todo :bytes pkts ...
        }
    }
//appid trans_app --end
    if(p_this_thread->all.data[idx_use].ss_active)
    {
        p_this_thread->all.data[idx_use].ss_active --;
        p_this_thread->all.data[idx_use].ss_close ++;
    }
    if((p_session->session_basic.IO_Sign[0] & INTRA_IP_L2DEFENSE)||(p_session->session_basic.IO_Sign[1] & INTRA_IP_L2DEFENSE))
    {
        if(p_this_thread->inner.data[idx_use].ss_active)
        {
            p_this_thread->inner.data[idx_use].ss_active --;
            p_this_thread->inner.data[idx_use].ss_close ++;
        }
    }
    if(0 == (p_session->session_basic.IO_Sign[0] & (OUTLAND_IP_L2DEFENSE|PRIVATE_IP_L2DEFENSE)) || 0 == (p_session->session_basic.IO_Sign[1] & (OUTLAND_IP_L2DEFENSE|PRIVATE_IP_L2DEFENSE)))
    {
        if(p_this_thread->prc.data[idx_use].ss_active)
        {
            p_this_thread->prc.data[idx_use].ss_active --;
            p_this_thread->prc.data[idx_use].ss_close ++;
        }
    }
    if((p_session->session_basic.IO_Sign[0] & OUTLAND_IP_L2DEFENSE)||(p_session->session_basic.IO_Sign[1] & OUTLAND_IP_L2DEFENSE))
    {
        if(p_this_thread->outland.data[idx_use].ss_active)
        {
            p_this_thread->outland.data[idx_use].ss_active --;
            p_this_thread->outland.data[idx_use].ss_close ++;
        }
    }
    if(6 == p_session->session_basic.IPPro)
    {
        if(p_this_thread->tcp.data[idx_use].ss_active)
        {
            p_this_thread->tcp.data[idx_use].ss_active --;
            p_this_thread->tcp.data[idx_use].ss_close ++;
        }
        if(p_this_thread->tcp_port[p_session->session_basic.pPort[1]].data[idx_use].ss_active)
        {
            p_this_thread->tcp_port[p_session->session_basic.pPort[1]].data[idx_use].ss_active --;
            p_this_thread->tcp_port[p_session->session_basic.pPort[1]].data[idx_use].ss_close ++;
        }
    }
    else if(17 == p_session->session_basic.IPPro)
    {
        if(p_this_thread->udp.data[idx_use].ss_active)
        {
            p_this_thread->udp.data[idx_use].ss_active --;
            p_this_thread->udp.data[idx_use].ss_close ++;
        }
        if(p_this_thread->udp_port[p_session->session_basic.pPort[1]].data[idx_use].ss_active)
        {
            p_this_thread->udp_port[p_session->session_basic.pPort[1]].data[idx_use].ss_active --;
            p_this_thread->udp_port[p_session->session_basic.pPort[1]].data[idx_use].ss_close ++;
        }
    }
    if(p_session->session_basic.ProtoSign >= APPID_START)
    {
        int appid_idx = p_session->session_basic.ProtoSign - APPID_START;
        if(p_this_thread->app_list[appid_idx].data[idx_use].ss_active)
        {
            p_this_thread->app_list[appid_idx].data[idx_use].ss_active --;
            p_this_thread->app_list[appid_idx].data[idx_use].ss_close ++;
        }
    }
    new_app_flag[thread_id][p_session->session_basic.ConnectID] = 0;
    if(ipseg_num)
    {
        int idx[2] = {-1,-1};
        for(int i = 0; i < 2; i ++)
        {
            if(p_session->session_basic.pIP[i].IsIPv6())
            {
                continue;
            }
            uint32_t ssip = 0;
            p_session->session_basic.pIP[i].GetIPv4(ssip);
            idx[i] = find_ipseg_idx(ntohl(ssip));
        }
        if(idx[0] >= 0)
        {
            if(p_this_thread->ipseg_list[idx[0]].data[idx_use].ss_active)
            {
                p_this_thread->ipseg_list[idx[0]].data[idx_use].ss_active --;
                p_this_thread->ipseg_list[idx[0]].data[idx_use].ss_close ++;
            }
        }
        if((idx[1] >= 0) && (idx[1] != idx[0]))
        {
            if(p_this_thread->ipseg_list[idx[1]].data[idx_use].ss_active)
            {
                p_this_thread->ipseg_list[idx[1]].data[idx_use].ss_active --;
                p_this_thread->ipseg_list[idx[1]].data[idx_use].ss_close ++;
            }
        }
    }
    if(ip_num)
    {
        int idx[2] = {-1,-1};
        for(int i = 0; i < 2; i ++)
        {
            if(p_session->session_basic.pIP[i].IsIPv6())
            {
                continue;
            }
            uint32_t ssip = 0;
            p_session->session_basic.pIP[i].GetIPv4(ssip);
            idx[i] = find_ip_idx(ntohl(ssip));
        }
        if(idx[0] >= 0)
        {
            if(p_this_thread->ip_list[idx[0]].data[idx_use].ss_active)
            {
                p_this_thread->ip_list[idx[0]].data[idx_use].ss_active --;
                p_this_thread->ip_list[idx[0]].data[idx_use].ss_close ++;
            }
        }
        if((idx[1] >= 0) && (idx[1] != idx[0]))
        {
            if(p_this_thread->ip_list[idx[1]].data[idx_use].ss_active)
            {
                p_this_thread->ip_list[idx[1]].data[idx_use].ss_active --;
                p_this_thread->ip_list[idx[1]].data[idx_use].ss_close ++;
            }
        }
    }
    if(p_session->b_has_mac)
    {
        int idx[2] = {-1,-1};
        idx[0] = find_mac_idx(thread_id, p_session->srcmac);
        idx[1] = find_mac_idx(thread_id, p_session->dstmac);
        if(idx[0] >= 0)
        {
            if(p_this_thread->mac_list[idx[0]].data[idx_use].ss_active)
            {
                p_this_thread->mac_list[idx[0]].data[idx_use].ss_active --;
                p_this_thread->mac_list[idx[0]].data[idx_use].ss_close ++;
            }
        }
        if((idx[1] >= 0) && (idx[1] != idx[0]))
        {
            if(p_this_thread->mac_list[idx[1]].data[idx_use].ss_active)
            {
                p_this_thread->mac_list[idx[1]].data[idx_use].ss_active --;
                p_this_thread->mac_list[idx[1]].data[idx_use].ss_close ++;
            }
        }
    }
}
void slice_statistics::timeout(uint32_t ts, uint32_t thread_id)
{
    uint8_t b_change = 0;
    thread_slice_data *p_this_thread = p_thread_data+thread_id;

    if(p_this_thread->ts_last)
    {
        if(ts >= (p_this_thread->ts_last + ts_wait))
        {
            b_change = 1;
        }
    }
    else if(ts >= (ts_system_start + ts_wait))
    {
        b_change = 1;
    }
    if(b_change)
    {
        uint32_t idx2send = p_this_thread->idx_use;
        uint32_t idx_use = (idx2send + 1) % 2;
        p_this_thread->idx_use = idx_use;
//backup
        p_this_thread->mac_count[idx_use] = p_this_thread->mac_count[idx2send];
        p_this_thread->all.data[idx_use].ss_active = p_this_thread->all.data[idx2send].ss_active;
        p_this_thread->inner.data[idx_use].ss_active = p_this_thread->inner.data[idx2send].ss_active;
        p_this_thread->prc.data[idx_use].ss_active = p_this_thread->prc.data[idx2send].ss_active;
        p_this_thread->outland.data[idx_use].ss_active = p_this_thread->outland.data[idx2send].ss_active;
        p_this_thread->tcp.data[idx_use].ss_active = p_this_thread->tcp.data[idx2send].ss_active;
        p_this_thread->udp.data[idx_use].ss_active = p_this_thread->udp.data[idx2send].ss_active;
        for(int i =0; i < 65536; i ++)
        {
            p_this_thread->tcp_port[i].data[idx_use].ss_active = p_this_thread->tcp_port[i].data[idx2send].ss_active;
            p_this_thread->udp_port[i].data[idx_use].ss_active = p_this_thread->udp_port[i].data[idx2send].ss_active;
        }
        for(int i =0; i < APPID_MAX-APPID_START; i ++)
        {
            p_this_thread->app_list[i].data[idx_use].ss_active = p_this_thread->app_list[i].data[idx2send].ss_active;
        }
        for(int i =0; i < ipseg_num; i ++)
        {
            p_this_thread->ipseg_list[i].data[idx_use].ss_active = p_this_thread->ipseg_list[i].data[idx2send].ss_active;
        }
        for(int i =0; i < ip_num; i ++)
        {
            p_this_thread->ip_list[i].data[idx_use].ss_active = p_this_thread->ip_list[i].data[idx2send].ss_active;
        }
        for(int i =0; i < p_this_thread->mac_count[idx2send]; i ++)
        {
            p_this_thread->mac_list[i].data[idx_use].ss_active = p_this_thread->mac_list[i].data[idx2send].ss_active;
        }
//backup --end
        uint32_t ts_x = ts % ts_wait;
        p_this_thread->ts_last = (ts - ts_x);
    }
}
void slice_statistics::set_workmode(uint8_t b_seg_analysis)
{
    this->b_seg_analysis = b_seg_analysis;
}

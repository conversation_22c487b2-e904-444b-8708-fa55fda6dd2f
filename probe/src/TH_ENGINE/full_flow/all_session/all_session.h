// Last Update:2019-04-15 00:44:48
/**
 * @file all_session_session.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-01-10
 */

#ifndef ALL_SESSION_H
#define ALL_SESSION_H
#include <TH_engine_interface.h>
#include "all_session_session.h"
extern "C"
{
    int all_session_get_fullflow_id();

    packet_full_flow  * all_session_attach_fullflow(int,int);
};
class all_session : public packet_full_flow
{
    public :
        all_session(int thread_num, int thread_id);
        virtual ~all_session()
        {
        }
        void xml_parse();
        void init();
        virtual void full_flow_handle(c_packet * p_packet ,session_pub * p_session) ;
        virtual bool time_out(session_pub * p_session); //  数据超时接口 ， 可以用作定时调用接口
        virtual void resources_recovery(session_pub * p_session) ; // 资源回收接口
        virtual void session_recovery(session_pub * p_session) ; // 资源回收接口
        virtual void  log(uint32_t ntime) ;
        virtual void module_timeout(uint32_t ts, CMessage *value, uint32_t thread_id);
        virtual void set_workmode(uint8_t b_segment_analysis);
    private:
        all_session_session * p_all_session_session;
};


#endif  /*ALL_SESSION_H*/

// Last Update:2019-04-15 00:45:06
/**
 * @file all_session_session.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-01-10
 */

#include <xml_parse.h>
#include "all_session.h"
#include "all_session_define.h"

extern "C"
{
    int all_session_get_fullflow_id()
    {
        return 1121;
    }

    packet_full_flow * all_session_attach_fullflow(int thread_num, int thread_id)
    {
        return new all_session(thread_num,thread_id);
    }
};

statistics_thread_template<system_statistics> *all_session_session::p_m_system_sts = NULL;
rule_statistics *all_session_session::p_m_rule = NULL;
mac_statistics_sender *all_session_session::p_m_mac_sender = NULL;
slice_statistics *all_session_session::p_m_slice = NULL;


void all_session::xml_parse()
{
    string conf_path = string(getenv("THE_CONF_PATH"))+"/all_session.xml";
    xmlDocPtr doc = NULL;
    xmlNodePtr curNode = NULL;
    xmlNodePtr curNode_son = NULL;
    doc = xmlReadFile(conf_path.c_str(), "UTF-8", XML_PARSE_RECOVER);
    if (NULL == doc)
    {
        return;
    }
    curNode = xmlDocGetRootElement(doc);
    if (NULL == curNode)
    {
        xmlFreeDoc(doc);
        return;
    }
    if (0 != xmlStrcmp(curNode->name, (xmlChar*) "config"))
    {
        xmlFreeDoc(doc);
        abort();
        return;
    }
    curNode = curNode->xmlChildrenNode;
    int i_plugin_id = 0;
    char* p_tmp = NULL;
    for(; curNode != NULL; curNode = curNode->next)
    {
        if( xmlStrcmp(curNode->name, (xmlChar*) "b_mac_statistics") == 0)
        {
            p_tmp = (char*)xmlNodeGetContent(curNode);
            if (NULL != p_tmp)
            {
                string str_key(p_tmp);
                if (str_key == "true")
                {
                    all_session_define::b_mac_statistics = true;
                }
                else {
                    all_session_define::b_mac_statistics = false;
                }
            }
            xmlFree(p_tmp);
            p_tmp = NULL;
        }
        else if( xmlStrcmp(curNode->name, (xmlChar*) "b_network_slice") == 0)
        {
            p_tmp = (char*)xmlNodeGetContent(curNode);
            if (NULL != p_tmp)
            {
                string str_key(p_tmp);
                if (str_key == "true")
                {
                    all_session_define::b_network_slice = true;
                }
                else
                {
                    all_session_define::b_network_slice = false;
                }
            }
            xmlFree(p_tmp);
            p_tmp = NULL;
        }
    }
    xmlFreeDoc(doc);
    return;
}

void all_session::init()
{
    ;
}
all_session::all_session(int thread_num, int thread_id):packet_full_flow(thread_num, thread_id)
{
    xml_parse();
    b_seg_mode_work = 1;
    p_all_session_session  = new all_session_session(thread_num, thread_id);
    p_all_session_session -> init();
}

void all_session::set_workmode(uint8_t b_segment_analysis)
{
    this->b_segment_analysis = b_segment_analysis;
    p_all_session_session->set_workmode(b_segment_analysis);
}



void all_session::full_flow_handle(c_packet * p_packet ,session_pub * p_session)
{
    //同源数组
    p_all_session_session -> add_packet_info(p_session,p_packet );
}
bool all_session::time_out(session_pub *p_session)
{
    return true;
}

// 
void all_session::resources_recovery(session_pub * p_session)
{
}
void all_session::session_recovery(session_pub * p_session)
{
    p_all_session_session->recovery_session(p_session);
}
void all_session::log(uint32_t ntime)
{
    p_all_session_session -> send_log(ntime) ;
}

void all_session::module_timeout(uint32_t ts, CMessage *value, uint32_t thread_id)
{
    p_all_session_session -> module_timeout(ts, value, thread_id);
}

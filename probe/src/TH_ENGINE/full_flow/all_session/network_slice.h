#ifndef __NETWORK_SLICE_H__
#define __NETWORK_SLICE_H__
#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include <string>
#include <fstream>
#include "DataStructure/TemplateMatch.h"
#include "DataStructure/SequenceList.h"
#include "DataStructure/Func_Math.h"
#include "session_pub.h"

using namespace std;

#define APPID_START 10000
#define APPID_MAX   11000

#define MAX_IP_SEG_SUPPORT  1000
#define MAX_IP_SUPPORT      1000
#define MAX_MAC_SUPPORT     1000

#define SLICE_TYPE_ALL          0
#define SLICE_TYPE_MAC          1
#define SLICE_TYPE_INNER        2
#define SLICE_TYPE_PRC          3
#define SLICE_TYPE_OUTLAND      4
#define SLICE_TYPE_IP           5
#define SLICE_TYPE_IPSEG        6
#define SLICE_TYPE_TCP          7
#define SLICE_TYPE_TCPSPORT     8
#define SLICE_TYPE_UDP          9
#define SLICE_TYPE_UDPSPORT     10
#define SLICE_TYPE_APP          11

typedef struct
{
    uint64_t    bytes;
    uint64_t    bytes_send;
    uint64_t    bytes_recv;
    uint64_t    pkts;
    uint64_t    pkts_send;
    uint64_t    pkts_recv;
    uint32_t    syn_pkt_send;
    uint32_t    syn_pkt_recv;
    uint32_t    syn_ack_pkt_send;
    uint32_t    syn_ack_pkt_recv;
    uint32_t    fin_pkt_send;
    uint32_t    fin_pkt_recv;
    uint32_t    ss_new;
    uint32_t    ss_close;
    uint32_t    ss_active;
    uint32_t    reserve;
}slice_info_ab;

typedef struct
{
    uint64_t        key;
    uint64_t        type;
    slice_info_ab   data[2];
}slice_info;

typedef struct
{
    slice_info  all;
    slice_info  inner;
    slice_info  prc;
    slice_info  outland;
    slice_info  tcp;
    slice_info  udp;
    slice_info  tcp_port[65536];
    slice_info  udp_port[65536];
    slice_info  app_list[APPID_MAX-APPID_START];
    slice_info  ipseg_list[MAX_IP_SEG_SUPPORT];
    slice_info  ip_list[MAX_IP_SUPPORT];
    slice_info  mac_list[MAX_MAC_SUPPORT];

    uint32_t    ts_last;                //上次切换AB时间戳
    uint32_t    idx_use;
    uint32_t    mac_count[2];
} thread_slice_data;

typedef struct
{
    uint64_t key;
    uint32_t idx;
} key2idx;
class key2idx_compare
{
public:
    int operator()( const key2idx &IN_A,const key2idx &IN_B )
    {
        if(IN_A.key < IN_B.key)
        {
            return -1;
        }
        else if(IN_A.key > IN_B.key)
        {
            return 1;
        }
        else
        {
            return 0;
        }
    }
};

typedef struct _ipseg_node
{
    uint64_t key;
    uint32_t idx;
    struct _ipseg_node *pnext;
}ipseg_node;

typedef struct
{
    uint8_t     b_flag_inner[2];
    uint8_t     b_flag_prc[2];
    uint8_t     b_flag_outland[2];
    int         idx_ipseg[2];
    int         idx_ip[2];
    int         idx_mac[2];
}ipslice_flags;

class slice_statistics
{
public:
    slice_statistics();
    ~slice_statistics();

    void config_parse();
    int init(int thread_num);
    void add_packet_info(uint32_t thread_id, session_pub * p_session , c_packet * p_packet);
    void send_log(uint32_t time_s);
    void recovery(uint32_t thread_id, session_pub * p_session);
    void timeout(uint32_t ts, uint32_t thread_id);
    void set_workmode(uint8_t b_seg_analysis);
private:
    void first_packet(uint32_t thread_id, session_pub * p_session , c_packet * p_packet, ipslice_flags *pflags);
    void add_packet_info_syn(uint32_t thread_id, c_packet * p_packet, ipslice_flags *pflags);
    void add_packet_info_synack(uint32_t thread_id, c_packet * p_packet, ipslice_flags *pflags);
    void add_packet_info_fin(uint32_t thread_id, c_packet * p_packet, ipslice_flags *pflags);
    //return >=0 ,find
    //return < 0 , not find
    int find_ipseg_idx(uint32_t ipaddr);
    int find_ip_idx(uint32_t ipaddr);
    int find_mac_idx(int thread_id,uint64_t ipaddr);
    void _merge_thread_slice_data();
    void _write(FILE *fp, uint32_t ts_now);
    void _clean_data();
    int                 thread_num;
    thread_slice_data   *p_thread_data;
    thread_slice_data   *p_merge_data;
    std::map<uint64_t, uint32_t>    merge_mac_map;
    uint32_t            ipseg_num;
    uint32_t            ip_num;
    uint32_t            ts_last_send;
    uint32_t            ts_wait;
    uint32_t            ts_system_start;
    uint64_t            ipseg_key[MAX_IP_SEG_SUPPORT];
    string              ipseg_str[MAX_IP_SEG_SUPPORT];
    uint64_t            ip_key[MAX_IP_SUPPORT];
    string              ip_str[MAX_IP_SUPPORT];
    uint32_t            max_thread_session;
    uint8_t             **new_app_flag;

    CTemplateMatch<key2idx, key2idx_compare> m_Judge_ip;
    CArrayBasic<key2idx> m_Array_ip;

    ipseg_node          *pipseg_node_list[65536];

    CTemplateMatch<key2idx, key2idx_compare> *m_Judge_mac;
    CArrayBasic<key2idx> *m_Array_mac;
    string output_path;
    uint8_t b_seg_analysis;
};




#endif
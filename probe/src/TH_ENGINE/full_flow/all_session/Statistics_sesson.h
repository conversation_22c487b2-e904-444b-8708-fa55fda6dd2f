// Last Update:2019-05-09 15:57:36
/**
 * @file _sesson.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-10-07
 */
//Statistics

#ifndef STATISTICS_SESSON_H
#define STATISTICS_SESSON_H
#include "resources_marge.h"
#include <json/json.h>
#include <stdint.h>
#include <session_pub.h>
#include "th_engine_tools.h"
#include "read_conf_commit.h"
#include "TH_engine_define.h"
using namespace std;

#define FUN_MIN(a,b) ((a) < (b) ? (a) : (b))
#define FUN_MAX(a,b) ((a) > (b) ? (a) : (b))
#define MAX_IF_NUM 16

// 系统信息统计
class  system_statistics
{
    public :
        system_statistics()
        {
            max_thread_session = read_conf_commit::GetInstance()->read_conf_session_num("session","thread_session_num");
            p_statistics_session = new uint8_t[max_thread_session];
            total_rule_hit_bytes = 0;
            total_rule_hit_pkts = 0;
            init();
        }
        void init()
        {
            memset(p_statistics_session, 0, max_thread_session);
            all_session                =    0;
            all_tcp_session            =    0;
            all_udp_session            =    0;
            now_action_session         =    0;
            now_tcp_action_session     =    0;
            now_udp_action_session     =    0;

            all_bytes                  =    0;
            tmp_bytes                  =    0;
            tmp_packets                =    0;

            tmp_ip_bytes               =    0;
            tmp_ipin_bytes             =    0;

            all_packets                =    0;
            tmp_ipv4_packet            =    0;
            tmp_ipv6_packet            =    0;
            tmp_ipin_packet            =    0;
            tmp_noip_packet            =    0;
            tmp_tcp_packet             =    0;
            tmp_udp_packet             =    0;

            tmp_decode_drop_pkts       =    0;
            tmp_decode_drop_bytes      =    0;
            tmp_filter_drop_pkts       =    0;
            tmp_filter_drop_bytes      =    0;
            tmp_defense_drop_pkts      =    0;
            tmp_defense_drop_bytes     =    0;
            tmp_limit_drop_pkts        =    0;
            tmp_limit_drop_bytes       =    0;

            all_rule_hit_bytes         =    0;
            all_rule_hit_pkts          =    0;
            tmp_rule_hit_bytes         =    0;
            tmp_rule_hit_pkts          =    0;
        }
        void init(int thread_id)
        {
            if(0 == thread_id)
            {
                string filepath = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/statistics/rule_hit_statistics.map";
                FILE *pfile = fopen(filepath.c_str(), "r");
                if (pfile)
                {
                    int len;
                    fseek(pfile,0,SEEK_END);
                    len = ftell(pfile);
                    fseek(pfile,0,SEEK_SET);
                    if (len > 0)
                    {
                        uint64_t tmp;
                        if(1 == fscanf(pfile, "%"PRIu64"", &tmp))
                        {
                            total_rule_hit_bytes = tmp;
                            if(1 == fscanf(pfile, "%"PRIu64"", &tmp))
                            {
                                total_rule_hit_pkts = tmp;
                            }
                        }
                        if(0 == total_rule_hit_bytes)
                        {
                            total_rule_hit_pkts = 0;
                        }
                        if(0 == total_rule_hit_pkts)
                        {
                            total_rule_hit_bytes = 0;
                        }
                    }
                    fclose(pfile);
                }
            }
            init();
        }
        void  first_packet(session_pub * p_session , c_packet * p_packet )
        {
            all_session ++ ;
            now_action_session ++ ;
            if(p_session ->session_basic.IPPro == 6 )
            {
                all_tcp_session ++;
                now_tcp_action_session ++ ;
            }
            else if (p_session ->session_basic.IPPro == 17)
            {
                all_udp_session ++;
                now_udp_action_session ++;
            }
        }
        void add_packet_info(session_pub * p_session , c_packet * p_packet)
        {
            all_bytes += p_packet -> packet_len ;
            all_packets ++;
            tmp_bytes += p_packet -> packet_len ;
            tmp_packets ++;
            if(p_packet->u_ip == 1)
            {
                tmp_ip_bytes += p_packet -> packet_len ;
                tmp_ipv4_packet ++;
                if(p_packet->IO_Sign[1] & 0x100)
                {
                    tmp_ipin_bytes += p_packet -> packet_len;
                    tmp_ipin_packet ++;
                }
                if(6 == p_packet->u_tcp)
                {
                    tmp_tcp_packet ++;
                }
                else if(17 == p_packet->u_tcp)
                {
                    tmp_udp_packet ++;
                }
            }
            else if(p_packet->u_ip == 2)
            {
                tmp_ip_bytes += p_packet -> packet_len ;
                tmp_ipv6_packet ++;
                if(p_packet->IO_Sign[1] & 0x100)
                {
                    tmp_ipin_bytes += p_packet -> packet_len;
                    tmp_ipin_packet ++;
                }
                if(6 == p_packet->u_tcp)
                {
                    tmp_tcp_packet ++;
                }
                else if(17 == p_packet->u_tcp)
                {
                    tmp_udp_packet ++;
                }
            }
            else if(p_packet->flags.b_noip)
            {
                tmp_noip_packet ++;
            }
            
            if(p_packet->flags.b_drop_decode)
            {
                tmp_decode_drop_pkts ++;
                tmp_decode_drop_bytes += p_packet -> packet_len ;
            }
            if(p_packet->flags.b_drop_filter)
            {
                tmp_filter_drop_pkts ++;
                tmp_filter_drop_bytes += p_packet -> packet_len ;
            }
            if(p_packet->flags.b_drop_defense)
            {
                tmp_defense_drop_pkts ++;
                tmp_defense_drop_bytes += p_packet -> packet_len ;
            }
            if(p_packet->flags.b_drop_beyond)
            {
                tmp_limit_drop_pkts ++;
                tmp_limit_drop_bytes += p_packet -> packet_len ;
            }
            uint8_t b_hit = 0;
            if(p_session && p_session -> session_basic.RuleNum)
            {
                b_hit = 1;
            }
            if(p_packet->sign_num || p_packet->rt_sign_num)
            {
                b_hit = 1;
            }
            if(b_hit)
            {
                all_rule_hit_bytes += p_packet -> packet_len ;
                all_rule_hit_pkts ++;
                tmp_rule_hit_bytes += p_packet -> packet_len ;
                tmp_rule_hit_pkts ++;
            }

            if(p_session)
            {
                uint8_t *p_ss_item = &(p_statistics_session[p_session -> session_basic.ConnectID]);
                if(p_session -> session_basic.Server == PACKETFROMCLIENT && p_session->session_basic.IO_Sign[1] & 0x100) // server ip 为内网， 会话为进方向 
                {
                    *p_ss_item = 1;
                }
                else if(p_session -> session_basic.Server == PACKETFROMSERVER && p_session->session_basic.IO_Sign[0] & 0x100) // server ip 为内网， 会话为进方向
                {
                    *p_ss_item = 1;
                }
                else
                {
                    *p_ss_item = 0;
                }
            }
        }
        void  recovery(session_pub * p_session)
        {
            uint8_t *p_ss_item = &(p_statistics_session[p_session -> session_basic.ConnectID]);
            *p_ss_item = 0;
            now_action_session  -- ;
            if(p_session -> session_basic.IPPro == 6) 
            {
                now_tcp_action_session -- ;
            }
            else if(p_session -> session_basic.IPPro == 17)
            {
                now_udp_action_session -- ;
            }
        }

        void update_hit_summary()
        {
            string filepath = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/statistics/rule_hit_statistics.map";
            string filepath_back = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/statistics/.rule_hit_statistics.map.tmp";

            FILE *pfile = fopen(filepath_back.c_str(), "w");
            if (pfile)
            {
                fprintf(pfile, "%"PRIu64"\n%"PRIu64"\n", total_rule_hit_bytes, total_rule_hit_pkts);
                fclose(pfile);
                rename(filepath_back.c_str(), filepath.c_str());
            }
        }

        uint64_t total_rule_hit_bytes;
        uint64_t total_rule_hit_pkts;


//会话分布
        uint32_t max_thread_session         ;   //小会话上限
        uint8_t *p_statistics_session       ;   //会话属性 进出
        uint64_t all_session                ;   //启动后会话数总量
        uint64_t all_tcp_session            ;   //启动后TCP会话数总量
        uint64_t all_udp_session            ;   //启动后UDP会话数总量
        uint32_t now_action_session         ;   //当前活跃的session
        uint32_t now_tcp_action_session     ;   //当前活跃的TCP session
        uint32_t now_udp_action_session     ;   //当前活跃的UDP session

//流量
        uint64_t all_bytes                  ;   //启动后总字节数
        uint64_t tmp_bytes                  ;   //30s的字节数
        uint64_t tmp_ip_bytes               ;   //30s的IP包字节数
        uint64_t tmp_ipin_bytes             ;   //30s内包的目的IP为内网（页面显示进）字节数
//包分布
        uint64_t all_packets                ;   //启动后总包数
        uint32_t tmp_packets                ;   //30s的包数
        uint32_t tmp_ipv4_packet            ;   //30s内IPV4包数
        uint32_t tmp_ipv6_packet            ;   //30s内IPV6包数
        uint32_t tmp_ipin_packet            ;   //30s内包的目的IP为内网（页面显示进）包数
        uint32_t tmp_noip_packet            ;   //30s内无IP包数
        uint32_t tmp_tcp_packet             ;   //30s内TCP包数
        uint32_t tmp_udp_packet             ;   //30s内UDP包数

//过滤+防御
        uint32_t tmp_decode_drop_pkts       ;   //30s解包失败包数
        uint64_t tmp_decode_drop_bytes      ;   //30s解包失败字节数
        uint32_t tmp_filter_drop_pkts       ;   //30s过滤丢包数
        uint64_t tmp_filter_drop_bytes      ;   //30s过滤丢字节数
        uint32_t tmp_defense_drop_pkts      ;   //30s防御丢包数
        uint64_t tmp_defense_drop_bytes     ;   //30s防御丢字节数
        uint32_t tmp_limit_drop_pkts        ;   //30s超限丢包数
        uint64_t tmp_limit_drop_bytes       ;   //30s超限丢字节数
//规则
        uint64_t all_rule_hit_bytes         ;   //启动后规则命中总字节数
        uint64_t all_rule_hit_pkts          ;   //启动后规则命中总包数
        uint64_t tmp_rule_hit_bytes         ;   //30s规则命中字节数
        uint32_t tmp_rule_hit_pkts          ;   //30s规则命中包数

        void send_log(uint32_t time_interval ,system_statistics * p ,int num,uint32_t time_s)
        {
            uint64_t all_all_session                =   0;
            uint64_t all_all_tcp_session            =   0;
            uint64_t all_all_udp_session            =   0;
            uint32_t all_now_action_session         =   0;
            uint32_t all_now_tcp_action_session     =   0;
            uint32_t all_now_udp_action_session     =   0;

            uint64_t all_all_bytes                  =   0;
            uint64_t all_tmp_bytes                  =   0;
            uint64_t all_tmp_ip_bytes               =   0;
            uint64_t all_tmp_ipin_bytes             =   0;

            uint64_t all_all_packets                =   0;
            uint32_t all_tmp_packets                =   0;
            uint32_t all_tmp_ipv4_packet            =   0;
            uint32_t all_tmp_ipv6_packet            =   0;
            uint32_t all_tmp_ipin_packet            =   0;
            uint32_t all_tmp_noip_packet            =   0;
            uint32_t all_tmp_tcp_packet             =   0;
            uint32_t all_tmp_udp_packet             =   0;

            uint32_t all_tmp_decode_drop_pkts       =   0;
            uint64_t all_tmp_decode_drop_bytes      =   0;
            uint32_t all_tmp_filter_drop_pkts       =   0;
            uint64_t all_tmp_filter_drop_bytes      =   0;
            uint32_t all_tmp_defense_drop_pkts      =   0;
            uint64_t all_tmp_defense_drop_bytes     =   0;
            uint32_t all_tmp_limit_drop_pkts        =   0;
            uint64_t all_tmp_limit_drop_bytes       =   0;

            uint64_t all_all_rule_hit_bytes         =   0;
            uint64_t all_all_rule_hit_pkts          =   0;
            uint64_t all_tmp_rule_hit_bytes         =   0;
            uint32_t all_tmp_rule_hit_pkts          =   0;

            uint64_t mbps_filter_out = 0;

            for(int i = 0 ; i < num ; i++ )
            {
                all_all_session             += p[i].all_session             ;
                all_all_tcp_session         += p[i].all_tcp_session         ;
                all_all_udp_session         += p[i].all_udp_session         ;
                all_now_action_session      += p[i].now_action_session      ;
                all_now_tcp_action_session  += p[i].now_tcp_action_session  ;
                all_now_udp_action_session  += p[i].now_udp_action_session  ;

                all_all_bytes               += p[i].all_bytes               ;
                all_tmp_bytes               += p[i].tmp_bytes               ;   p[i].tmp_bytes              = 0;
                all_tmp_packets             += p[i].tmp_packets             ;   p[i].tmp_packets            = 0;
                all_tmp_ip_bytes            += p[i].tmp_ip_bytes            ;   p[i].tmp_ip_bytes           = 0;
                all_tmp_ipin_bytes          += p[i].tmp_ipin_bytes          ;   p[i].tmp_ipin_bytes         = 0;

                all_all_packets             += p[i].all_packets             ;
                all_tmp_ipv4_packet         += p[i].tmp_ipv4_packet         ;   p[i].tmp_ipv4_packet        = 0;
                all_tmp_ipv6_packet         += p[i].tmp_ipv6_packet         ;   p[i].tmp_ipv6_packet        = 0;
                all_tmp_ipin_packet         += p[i].tmp_ipin_packet         ;   p[i].tmp_ipin_packet        = 0;
                all_tmp_noip_packet         += p[i].tmp_noip_packet         ;   p[i].tmp_noip_packet        = 0;
                all_tmp_tcp_packet          += p[i].tmp_tcp_packet          ;   p[i].tmp_tcp_packet         = 0;
                all_tmp_udp_packet          += p[i].tmp_udp_packet          ;   p[i].tmp_udp_packet         = 0;

                all_tmp_decode_drop_pkts    += p[i].tmp_decode_drop_pkts    ;   p[i].tmp_decode_drop_pkts   = 0;
                all_tmp_decode_drop_bytes   += p[i].tmp_decode_drop_bytes   ;   p[i].tmp_decode_drop_bytes  = 0;
                all_tmp_filter_drop_pkts    += p[i].tmp_filter_drop_pkts    ;   p[i].tmp_filter_drop_pkts   = 0;
                all_tmp_filter_drop_bytes   += p[i].tmp_filter_drop_bytes   ;   p[i].tmp_filter_drop_bytes  = 0;
                all_tmp_defense_drop_pkts   += p[i].tmp_defense_drop_pkts   ;   p[i].tmp_defense_drop_pkts  = 0;
                all_tmp_defense_drop_bytes  += p[i].tmp_defense_drop_bytes  ;   p[i].tmp_defense_drop_bytes = 0;
                all_tmp_limit_drop_pkts     += p[i].tmp_limit_drop_pkts     ;   p[i].tmp_limit_drop_pkts    = 0;
                all_tmp_limit_drop_bytes    += p[i].tmp_limit_drop_bytes    ;   p[i].tmp_limit_drop_bytes   = 0;

                all_all_rule_hit_bytes      += p[i].all_rule_hit_bytes      ;
                all_all_rule_hit_pkts       += p[i].all_rule_hit_pkts       ;
                all_tmp_rule_hit_bytes      += p[i].tmp_rule_hit_bytes      ;   p[i].tmp_rule_hit_bytes     = 0;
                all_tmp_rule_hit_pkts       += p[i].tmp_rule_hit_pkts       ;   p[i].tmp_rule_hit_pkts      = 0;
            }

            total_rule_hit_bytes += all_tmp_rule_hit_bytes;
            total_rule_hit_pkts += all_tmp_rule_hit_pkts;


            uint32_t all_now_in_action_session=0;
            for(int i = 0 ; i < num ; i++ )
            {
                for(int j = 0; j < max_thread_session; j ++)
                {
                    if(p_statistics_session[j])
                    {
                        all_now_in_action_session ++;
                    }
                }
            }
            
            all_now_in_action_session   = FUN_MIN(all_now_in_action_session     ,   all_now_action_session                  );
            all_tmp_ip_bytes            = FUN_MIN(all_tmp_ip_bytes              ,   all_tmp_bytes                           );
            all_tmp_ipin_bytes          = FUN_MIN(all_tmp_ipin_bytes            ,   all_tmp_ip_bytes                        );
            uint32_t ip_pkts = all_tmp_ipv4_packet+all_tmp_ipv6_packet;
            all_tmp_tcp_packet          = FUN_MIN(all_tmp_tcp_packet            ,   ip_pkts                                 );
            all_tmp_udp_packet          = FUN_MIN(all_tmp_udp_packet            ,   ip_pkts                                 );
            if(all_tmp_udp_packet + all_tmp_tcp_packet > ip_pkts)
            {
                all_tmp_udp_packet = ip_pkts - all_tmp_tcp_packet;
            }
            all_tmp_packets             = FUN_MAX(all_tmp_packets               ,   ip_pkts                                 );
            all_tmp_ipin_packet         = FUN_MIN(all_tmp_ipin_packet           ,   all_tmp_packets                         );
            all_tmp_decode_drop_bytes   = FUN_MIN(all_tmp_decode_drop_bytes     ,   all_tmp_bytes                           );
            all_tmp_filter_drop_bytes   = FUN_MIN(all_tmp_filter_drop_bytes     ,   all_tmp_bytes-all_tmp_decode_drop_bytes );
            all_tmp_defense_drop_bytes  = FUN_MIN(all_tmp_defense_drop_bytes    ,   all_tmp_bytes-all_tmp_decode_drop_bytes-all_tmp_filter_drop_bytes );

            char msg_buf[2048];
            int msglen;
            msglen = sprintf(msg_buf, "{\"type\":%d,\"time\":%u,\
\"bps\":\"%"PRIu64"\",\"bps_in\":\"%"PRIu64"\",\"bps_out\":\"%"PRIu64"\",\
\"pps\":%u,\"pps_in\":%u,\"pps_out\":%u,\
\"conn\":%u,\"conn_in\":%u,\"conn_out\":%u,\
\"pps_ipv4\":%u,\"pps_ipv6\":%u,\"pps_notip\":%u,\
\"pps_tcp\":%u,\"pps_udp\":%u,\"pps_ipother\":%u,\"task_id\":\"%lld\",\"batch_id\":\"%lld\",\"device_id\":\"%u\"}",
                    202, time_s,
                    all_tmp_bytes/30*8, all_tmp_ipin_bytes/30*8, (all_tmp_bytes - all_tmp_ipin_bytes)/30*8,
                    all_tmp_packets/30, all_tmp_ipin_packet/30, (all_tmp_packets - all_tmp_ipin_packet)/30,
                    all_now_action_session, all_now_in_action_session, (all_now_action_session - all_now_in_action_session),
                    all_tmp_ipv4_packet/30, all_tmp_ipv6_packet/30, (all_tmp_packets-ip_pkts)/30, 
                    all_tmp_tcp_packet/30, all_tmp_udp_packet/30, (ip_pkts-all_tmp_tcp_packet-all_tmp_udp_packet)/30,config_and_define::task_id, config_and_define::batch_id, th_engine_tools::instance()->device_id
                );
            update_hit_summary();
            th_engine_tools::instance()->send_log("slog", NULL, 0, msg_buf, msglen);
            msglen = sprintf(msg_buf, "{\"type\":%d,\"time\":%u,\
\"bps\":\"%"PRIu64"\",\"pps\":%u,\"conn\":%u,\
\"bps_filter_in\":\"%"PRIu64"\",\"bps_filter_drop\":\"%"PRIu64"\",\"bps_filter_out\":\"%"PRIu64"\",\
\"bps_defense_in\":\"%"PRIu64"\",\"bps_defense_drop\":\"%"PRIu64"\",\"bps_defense_out\":\"%"PRIu64"\",\
\"bps_limit_drop\":\"%"PRIu64"\",\
\"bps_rule_hit\":\"%"PRIu64"\",\"bytes_30s_rule_hit\":\"%"PRIu64"\",\"pkts_30s_rule_hit\":%u,\"bytes_rule_hit_all\":\"%"PRIu64"\",\"pkts_rule_hit_all\":\"%"PRIu64"\",\"task_id\":\"%lld\",\"batch_id\":\"%lld\",\"device_id\":\"%u\"}",
                    205, time_s,
                    all_tmp_bytes/30*8, all_tmp_packets/30, all_now_action_session,
                    (all_tmp_bytes-all_tmp_decode_drop_bytes)/30*8, all_tmp_filter_drop_bytes/30*8, (all_tmp_bytes-all_tmp_decode_drop_bytes-all_tmp_filter_drop_bytes)/30*8,
                    (all_tmp_bytes-all_tmp_decode_drop_bytes-all_tmp_filter_drop_bytes)/30*8, all_tmp_defense_drop_bytes/30*8, (all_tmp_bytes-all_tmp_decode_drop_bytes-all_tmp_filter_drop_bytes-all_tmp_defense_drop_bytes)/30*8,
                    all_tmp_limit_drop_bytes/30*8,
                    all_tmp_rule_hit_bytes/30*8,all_tmp_rule_hit_bytes,all_tmp_rule_hit_pkts,total_rule_hit_bytes,total_rule_hit_pkts,config_and_define::task_id, config_and_define::batch_id, th_engine_tools::instance()->device_id
                );
            th_engine_tools::instance()->send_log("slog", NULL, 0, msg_buf, msglen);
            mbps_filter_out = (all_tmp_bytes-all_tmp_decode_drop_bytes-all_tmp_filter_drop_bytes-all_tmp_defense_drop_bytes)*8/30/1024/1024;
            config_and_define::mbps_filter_out = (uint32_t)mbps_filter_out;
            msglen = sprintf(msg_buf, "{\"type\":%d,\"time\":%u,\"pkts\":\"%"PRIu64"\",\"bytes\":\"%"PRIu64"\",\"task_id\":\"%lld\",\"batch_id\":\"%lld\",\"device_id\":\"%u\"}", 217, time_s, all_all_packets, all_all_bytes, config_and_define::task_id, config_and_define::batch_id, th_engine_tools::instance()->device_id);
            th_engine_tools::instance()->send_log("slog", NULL, 0, msg_buf, msglen);
        }
};
#endif  /*FULL_FLOW_SESSON_H*/

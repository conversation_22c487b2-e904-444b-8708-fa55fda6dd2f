// Last Update:2019-04-12 17:05:31
/**
 * @file all_session_session_sesson.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-01-10
 */

#ifndef ALL_SESSION_SESSION_H
#define ALL_SESSION_SESSION_H
#include "resources_marge.h"
#include "basic_statistics.h"
#include "rule_statistics.h"
#include "mac_statistics.h"
#include "proto_statistics.h"
#include "Statistics_sesson.h"
#include <json/json.h>
#include <stdint.h>
#include <session_pub.h>
#include <syslog.h>
#include "statistics_thread_template.h"
#include "ab_queue_base.h"
#include "all_session_define.h"
#include "network_slice.h"

using namespace std;
// 系统信息统计

class all_session_session
{
public:
    all_session_session(int thread_num, int thread_id)
    {
        b_segment_analysis = 0;
        if(0 == thread_id)
        {
            if(all_session_define::b_network_slice)
            {
                p_m_slice = new slice_statistics();
                p_m_slice->init(thread_num);
            }
            p_m_system_sts = new statistics_thread_template<system_statistics>();
            p_m_rule = new rule_statistics();
            p_m_rule->init(thread_num);
            p_m_mac_sender = new mac_statistics_sender();
        }
        m_mac.set_queue(p_m_mac_sender->queue);
    }
    void init()
    {
        if (all_session_define::b_mac_statistics)
        {
            m_mac.Init(1000);
        }
    }
    void add_packet_info(session_pub * p_session , c_packet * p_packet)
    {
        if(!b_segment_analysis)
        {
            if (p_session)  //正常会话
            {
                if(1 == p_session->session_basic.pPacketNum[0] && 0 == p_session->session_basic.pPacketNum[1]) 
                {
                    p_m_system_sts->get_object_p(p_packet->thread_id) -> first_packet(p_session , p_packet);
                }
                p_m_system_sts->get_object_p(p_packet->thread_id) ->add_packet_info(p_session , p_packet);
                p_m_rule->add_packet_info(p_session , p_packet);   //TODO  multithreading sync
                if (all_session_define::b_mac_statistics)
                {
                    m_mac.add_packet_info(p_session , p_packet);
                }
            }
            else    //无会话
            {
                p_m_system_sts->get_object_p(p_packet->thread_id) ->add_packet_info(NULL, p_packet);
                p_m_rule->add_packet_info(NULL , p_packet);        //TODO  multithreading sync
                if (all_session_define::b_mac_statistics)
                {
                    m_mac.add_packet_info(NULL , p_packet);
                }
            }
        }
        if(all_session_define::b_network_slice)
        {
            p_m_slice->add_packet_info(p_packet->thread_id, p_session , p_packet);
        }
    }
    void send_log(uint32_t time_s)
    {
        if(!b_segment_analysis)
        {
            p_m_system_sts->Send(time_s);

            // ab_rule_statistics.get_object_p(p_session -> thread_id) ->get_using_handle() ->set_time(send_log_time);
            p_m_rule->send_log(time_s);
            if (all_session_define::b_mac_statistics)
            {
                p_m_mac_sender->send_log(time_s);
            }
        }
        if(all_session_define::b_network_slice)
        {
            p_m_slice->send_log(time_s);
        }
    }
    void recovery_session(session_pub * p_session)
    {
        if(!b_segment_analysis)
        {
            // m_basic.recovery(p_session);
            p_m_system_sts->get_object_p(p_session ->thread_id) ->recovery(p_session) ;
            //p_m_rule->recovery(p_session);
            //m_mac.recovery(p_session);
        }
        if(all_session_define::b_network_slice)
        {
            p_m_slice->recovery(p_session ->thread_id, p_session);
        }
    }
    void module_timeout(uint32_t ts, CMessage *value, uint32_t thread_id)
    {
        if(!b_segment_analysis)
        {
            if (all_session_define::b_mac_statistics)
            {
                m_mac.timeout(ts, thread_id);
            }
        }
        if(all_session_define::b_network_slice)
        {
            p_m_slice->timeout(ts, thread_id);
        }
    }
    void set_workmode(uint8_t b_segment_analysis)
    {
        this->b_segment_analysis = b_segment_analysis;
        if(all_session_define::b_network_slice)
        {
            p_m_slice->set_workmode(b_segment_analysis);
        }
    }
public:
    mac_statistics m_mac;
    uint8_t b_segment_analysis;
    static statistics_thread_template<system_statistics> *p_m_system_sts;
    static rule_statistics *p_m_rule;
    static mac_statistics_sender *p_m_mac_sender;
    static slice_statistics *p_m_slice;
};

#endif  /*ALL_SESSION_SESSION_H*/

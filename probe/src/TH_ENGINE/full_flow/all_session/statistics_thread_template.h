// Last Update:2019-04-03 11:27:23
/**
 * @file statistics_thread_template.h
 * @brief : 多线程数据统计汇总 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-03-19
 */

#ifndef STATISTICS_THREAD_TEMPLATE_H
#define STATISTICS_THREAD_TEMPLATE_H
#include  <string>
#include  <vector>
template <class T>
class statistics_thread_template
{

    public:
       statistics_thread_template():max_thd(64)
       {
           time_interval = 30 ;
           p_T  = new T[max_thd];
           for(int  i = 0 ; i < max_thd ; i++) 
           {
               p_T[i].init(i);
           }
           ntime_last = 0 ;
       }
       ~statistics_thread_template()
       {
           delete [] p_T ;
       }
       T* get_object_p(int thread)
       {
           return & p_T[thread];
       }
       // 统计发送数据
       void Send(uint32_t  ntime)
       {
           if(ntime - ntime_last  > time_interval)
           {
                p_T[0].send_log(time_interval, p_T, max_thd, ntime);
           }
       }
    private:
        T * p_T ;
        int max_thd;
        int ntime_last ;
        int time_interval;
};

#endif  /*STATISTICS_THREAD_TEMPLATE_H*/

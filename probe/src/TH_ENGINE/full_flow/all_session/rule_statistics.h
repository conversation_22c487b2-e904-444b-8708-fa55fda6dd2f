
// Last Update:2019-04-19 18:19:43
/**
 * @file rule_statistics.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-01-10
 */

#ifndef RULE_STATISTICS_H
#define RULE_STATISTICS_H
#include "resources_marge.h"
#include <json/json.h>
#include <stdint.h>
#include <session_pub.h>
#include "th_engine_tools.h"
#include "TH_engine_define.h"
using namespace std;

// 规则信息统计
class rule_statistics_session
{
    public:
        rule_statistics_session()
        {
            p_sum_bytes = NULL;
            p_sum_packet = NULL;
        }
        ~rule_statistics_session()
        {
            if(p_sum_bytes)
            {
                delete[] p_sum_bytes;
            }
            if(p_sum_packet)
            {
                delete[] p_sum_packet;
            }
        }
        uint64_t *p_sum_bytes;
        uint32_t *p_sum_packet;
        uint32_t sum_packet_inaccurate;
        uint32_t rule_id;
        int thread_num;
        bool b_exist()
        {
            if(sum_packet_inaccurate)
            {
                return true;
            }
            return false;
        }
        void add_packet_info(session_pub * p_session , c_packet * p_packet)
        {
            sum_packet_inaccurate ++;
            p_sum_packet[p_packet->thread_id] ++;
            p_sum_bytes[p_packet->thread_id] += p_packet->packet_len;
        }
        void send_log(uint32_t time_s)
        {
            uint64_t sum_bytes = 0;
            uint32_t sum_packet = 0;
            sum_packet_inaccurate = 0;
            for(int i = 0; i < thread_num; i ++)
            {
                sum_bytes += p_sum_bytes[i];
                p_sum_bytes[i] = 0;
                sum_packet += p_sum_packet[i];
                p_sum_packet[i] = 0;
            }
            if(sum_packet)
            {
                char tmp[2048];
                int tmplen;
                tmplen = sprintf(tmp, "{\"type\":%d,\"time\":%u,\"rule_id\":%u,\"bytes_30s\":\"%"PRIu64"\",\"packets_30s\":%u,\"task_id\":\"%lld\",\"batch_id\":\"%lld\",\"device_id\":\"%u\"}", 201, time_s, rule_id, sum_bytes, sum_packet,config_and_define::task_id, config_and_define::batch_id, th_engine_tools::instance()->device_id);
                th_engine_tools::instance()->send_log("slog", NULL, 0, tmp, tmplen);
            }
        }
        void init(uint32_t rule_id, int thread_num)
        {
            this->rule_id = rule_id;
            this->thread_num = thread_num;
            sum_packet_inaccurate = 0;
            p_sum_bytes = new uint64_t[thread_num];
            memset(p_sum_bytes, 0, sizeof(uint64_t) * thread_num);
            p_sum_packet = new uint32_t[thread_num];
            memset(p_sum_packet, 0, sizeof(uint32_t) * thread_num);
        }
};
class rule_statistics 
{
public:
    rule_statistics()
    {
        p_rule_map = NULL;
    }
    int init(int thread_num)
    {
        p_rule_map = new  rule_statistics_session[MAXRULENUM];
        for(uint32_t i=0; i < MAXRULENUM ; i++) 
        {
            p_rule_map[i].init(i, thread_num);
        }
    }
    ~rule_statistics()
    {
        if(p_rule_map)
        {
            delete[] p_rule_map ;
        }
    }
    void add_packet_info(session_pub * p_session , c_packet * p_packet)
    {
        //统计规则命中的信息
        if(p_session)
        {
            std::set<int> sign_set;
            for(int i = 0 ; i < p_packet -> sign_num ; i++)
            {
                sign_set.insert((int)p_packet-> packet_sign[i]);
            }
            for(int i = 0; i < p_packet->rt_sign_num; i++)
            {
                sign_set.insert((int)p_packet-> rt_rule_sign[i]);
            }
            for(int i = 0 ; i < p_session -> session_basic.RuleNum ; i++)
            {
                sign_set.insert((int)p_session -> session_basic.pRule[i]);
            }
            for(std::set<int> ::iterator it = sign_set.begin(); it != sign_set.end(); it ++)
            {
                p_rule_map[*it].add_packet_info(p_session , p_packet) ;
            }
            sign_set.clear();
        }
    }
    void send_log(uint32_t time_s)
    {
        //发送日志 ----syslog 发送 
        // rule 信息
        //  rule 统计信息
        for(int i = 0 ;i < MAXRULENUM; i++) 
        {
            if(p_rule_map[i].b_exist())
            {
                p_rule_map[i].send_log(time_s);
            }
        }
    }
public:
    //std::map<uint32_t , rule_statistics_session *> rule_map;
    rule_statistics_session * p_rule_map ; 
};


#endif  /*RULE_STATISTICS_H*/


// Last Update:2019-03-28 14:53:46
/**
 * @file full_flow_engine.h
 * @brief : 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-09-27
 */

#ifndef FULL_FLOW_ENGINE_H
#define FULL_FLOW_ENGINE_H
#include <ab_queue_base.h>
#include <TH_engine_interface.h>
#include <packet.h>
#include <xml_parse.h>
#include <session_pub.h>
#include <list>
#include <map>
#include "share_plugin_marge.h"
//#define MAXRULENUM 1000000
// 规则处理 预习建立一个处理hash 然后在做 
//static share_plugin_marge * p_ff_plugin_marge = NULL ; 
class ff_shareobject_marge
{
    public:
        ff_shareobject_marge()
        {
            p_ff_plugin_marge = NULL;
        }

    private:
		share_plugin_marge * p_ff_plugin_marge;
   //     static ff_shareobject_marge * instance ;
    public:
        share_plugin_marge * p_plugin_marge ; 
        ~ff_shareobject_marge()
        {
        /*    if(instance != NULL)
            {
                delete ff_shareobject_marge::instance ;
            }*/
            if (p_ff_plugin_marge != NULL)
            {
                /* pthread_mutex_lock(&ff_mutex);
                if (p_ff_plugin_marge  != NULL)
                {
                    delete  p_ff_plugin_marge;
                    p_ff_plugin_marge = NULL ;
                }
                pthread_mutex_unlock(&ff_mutex); */
                delete  p_ff_plugin_marge;
                p_ff_plugin_marge = NULL ;
            }
        }
        static ff_shareobject_marge * get_instance(std::string  so_path)
        {
           /* if (instance == NULL)
            {
                pthread_mutex_lock(&mutex);
                if (instance == NULL)
                {
                    instance = new ff_shareobject_marge();
                }
                pthread_mutex_unlock(&mutex);
            }*/
            static  ff_shareobject_marge instance ;

            return &instance;
        }
        // 
        void init(std::string so_path) 
        {
            if (p_ff_plugin_marge== NULL)
            {
                p_ff_plugin_marge = new share_plugin_marge(so_path);
            }
            p_plugin_marge = p_ff_plugin_marge ;
        }
};
class ff_plugin_conf
{
public:
    ff_plugin_conf();
    class plugin_conf
    {
    public:
        string name;
        int should_log;
    };
    int load_conf(string conf_path);
    std::map<int, plugin_conf> conf_map;
    int should_log_def;
};
class ff_conf  // full_flow_engine 的配置文件
{
    public :
        ff_conf(int thread_num, int thread_id);
        ~ff_conf();
        virtual void xml_parse() ;
        void handle(c_packet * p_packet ,session_pub * p_session);
        bool  time_out(session_pub * p_session);
        void resources_recovery(session_pub * p_session);
        void session_recovery(session_pub * p_session);
        void log(uint32_t time_ns);
        void module_timeout(uint32_t s, CMessage *value, uint32_t thread_id);
        // 解决配置文件  // 是否
         packet_full_flow  *  packet_full_flow_arr[MAXRULENUM]; // 处理函数列表  // 对应的规则处理
         packet_full_flow  *  rule_id_handle[1024000]; // 处理函数列表  // 对应的规则处理
    //     std::map<uint32_t ,packet_full_flow*> packet_full_flow_map ;
        std::map<int ,void *>  handle_map ;
// ------------  配置文件填充
        std::map<int , int > rule_use_map ;// 配置文件配置
        std::list<int> all_use_list ;// 配置文件配置
        ff_plugin_conf config;
};
// 壳子类 封装ff_conf
class full_flow_engine
{
    public:
        full_flow_engine(int thread_num, int thread_id);
        ~full_flow_engine();
        void init();
        void handle(c_packet * p_packet ,session_pub * p_session);
        bool  time_out(session_pub * p_session);
        void resources_recovery(session_pub * p_session);
        void session_recovery(session_pub * p_session);
        void log(uint32_t time_ns);
        void module_timeout(uint32_t s, CMessage *value, uint32_t thread_id);
    private:
      //std::list<packet_full_flow *> packet_full_flow_list ;
          ff_conf * p_ff_conf;
};


#endif  /*FULL_FLOW_ENGINE_H*/

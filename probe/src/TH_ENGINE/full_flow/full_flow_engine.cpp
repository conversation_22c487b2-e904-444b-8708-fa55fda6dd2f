// Last Update:2019-04-24 13:13:59
/**
 * @file full_flow_engine.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-09-27
 */
#include "full_flow_engine.h"
#include "TH_engine_define.h"
#include  "single_session/single_session.h"
#include  "write_pcap/write_pcap.h"
#include  "all_session/all_session.h"
#include "noip_session/noip_session.h"
#define PARSE_ID_NAME "get_fullflow_id"
#define PARSE_HANDLE_NAME "attach_fullflow"
ff_shareobject_marge* instance = NULL ;
static pthread_mutex_t ff_mutex = PTHREAD_MUTEX_INITIALIZER; 
ff_conf::ff_conf(int thread_num, int thread_id)
{
    handle_map.clear();
    rule_use_map.clear();
    all_use_list.clear();
    memset(packet_full_flow_arr,0x0,MAXRULENUM * sizeof(packet_full_flow  *));
    memset(rule_id_handle,0x0,1024000 * sizeof(packet_full_flow  *));
    std::string so_path = string(getenv("THE_ROOT")) + "/bin/plugin/full_flow/";
    //ff_shareobject_marge::get_instance() ->init(so_path);
    //ff_shareobject_marge::get_instance()->p_plugin_marge -> get_plugin_id_map(PARSE_ID_NAME , PARSE_HANDLE_NAME , &handle_map);
    pthread_mutex_lock(&ff_mutex);
    if(instance == NULL) 
    {
        instance = new ff_shareobject_marge();
        instance->init(so_path);
    }
    config.load_conf(string(getenv("THE_CONF_PATH")) + "/plugin_conf.json");
    instance->p_plugin_marge -> get_plugin_id_map(PARSE_ID_NAME , PARSE_HANDLE_NAME , &handle_map);
    packet_full_flow *p_full_flow = NULL;
    if(!config_and_define::b_segment_analyse)
    {
        handle_map[get_fullflow_id()]=(void *) attach_fullflow(thread_num, thread_id);
        handle_map[write_pcap_get_fullflow_id()]=(void *) write_pcap_attach_fullflow(thread_num, thread_id);
        handle_map[all_session_get_fullflow_id()]=(void *) all_session_attach_fullflow(thread_num, thread_id);
        handle_map[noip_get_fullflow_id()]=(void *) noip_attach_fullflow(thread_num, thread_id);
    }
    else
    {
        p_full_flow = attach_fullflow(thread_num, thread_id);
        if(p_full_flow->get_should_work())
        {
            handle_map[get_fullflow_id()] = (void *)p_full_flow;
        }
        else
        {
            delete p_full_flow;
        }
        p_full_flow = write_pcap_attach_fullflow(thread_num, thread_id);
        if(p_full_flow->get_should_work())
        {
            handle_map[write_pcap_get_fullflow_id()] = (void *)p_full_flow;
        }
        else
        {
            delete p_full_flow;
        }
        p_full_flow = all_session_attach_fullflow(thread_num, thread_id);
        if(p_full_flow->get_should_work())
        {
            handle_map[all_session_get_fullflow_id()] = (void *)p_full_flow;
        }
        else
        {
            delete p_full_flow;
        }
        p_full_flow = noip_attach_fullflow(thread_num, thread_id);
        if(p_full_flow->get_should_work())
        {
            handle_map[noip_get_fullflow_id()] = (void *)p_full_flow;
        }
        else
        {
            delete p_full_flow;
        }
    }
    
    std::map<int ,void *>::iterator it = handle_map.begin();
    for(;it != handle_map.end();it ++) 
    {
        packet_full_flow  * p = (packet_full_flow  *)it->second ;
        p -> set_tools(th_engine_tools::instance());
        p -> set_workmode((uint8_t)config_and_define::b_segment_analyse);
        auto itr = config.conf_map.find(it->first);
        if(itr != config.conf_map.end())
        {
            p -> set_should_log(itr->second.should_log);
        }
        else
        {
            p -> set_should_log(config.should_log_def);
        }
    }
    pthread_mutex_unlock(&ff_mutex); 

    //散列到hash
    std::map<int ,void *>::iterator it_handle ;
    std::map<int , int > ::iterator iter = rule_use_map.begin();
    for(;iter != rule_use_map. end();iter ++)
    {
        it_handle =  handle_map.find(iter ->second) ;
        if(it_handle != handle_map.end()) 
        {
            rule_id_handle[iter -> first]=(packet_full_flow  * )it_handle ->second; 
        }
    }

    for(it_handle = handle_map.begin();it_handle!= handle_map.end(); it_handle ++) 
    {
        packet_full_flow_arr[it_handle -> first] = (packet_full_flow *)it_handle->second; 
        printf(" full_flow  id == %d no null \n" , it_handle -> first) ;

    }
    xml_parse(); 
}
ff_conf::~ff_conf()
{
    std::map<int ,void *>::iterator iter =   handle_map.begin();
    for(;iter != handle_map. end();iter ++)
    {
        delete iter -> second;
    }
    handle_map.clear();
}
// 同步解析 
void ff_conf::xml_parse()
{
    //packet_full_flow_map.clear();
    //all_use_list.push_back(1113);
    //all_use_list.push_back(1114);
    //all_use_list.push_back(220);
    //all_use_list.push_back(1120);
    //all_use_list.push_back(1121);
    //rule_use_map[35001] = 220;

    string conf_path = string(getenv("THE_CONF_PATH"))+"/full_flow.xml";
    xmlDocPtr doc = NULL;
    xmlNodePtr curNode = NULL;
    xmlNodePtr curNode_son = NULL;
    doc = xmlReadFile(conf_path.c_str(), "UTF-8", XML_PARSE_RECOVER);
    if (NULL == doc)
    {
        return;
    }
    curNode = xmlDocGetRootElement(doc);
    if (NULL == curNode)
    {
        xmlFreeDoc(doc);
        return;
    }
    if (0 != xmlStrcmp(curNode->name, (xmlChar*) "config"))
    {
        xmlFreeDoc(doc);
        abort();
        return;
    }
    curNode = curNode->xmlChildrenNode;
    int i_plugin_id = 0;
    char* p_tmp = NULL;
    for(; curNode != NULL; curNode = curNode->next)
    {
        if( xmlStrcmp(curNode->name, (xmlChar*) "all_handle") == 0)
        {
            curNode_son = curNode->xmlChildrenNode;
            for(; curNode_son != NULL; curNode_son = curNode_son->next)
            {
                if( xmlStrcmp(curNode_son->name, (xmlChar*)"key") == 0)
                {
                    p_tmp = (char*)xmlNodeGetContent(curNode_son);
                    if (NULL != p_tmp)
                    {
                        i_plugin_id = atoi(p_tmp);
                        all_use_list.push_back(i_plugin_id);
                    }
                    xmlFree(p_tmp);
                    p_tmp = NULL;
                }
            }
        }
    }
    xmlFreeDoc(doc);
    return;
}

void ff_conf::handle(c_packet * p_packet ,session_pub * p_session)
{
    //if(p_packet -> app_len > 0 &&  p_packet -> u_tcp == 6) 
    if(p_session && p_session->p_session_ext && p_session->p_session_ext->tcp_link[0] && p_session->p_session_ext->tcp_link[1])
    {
        if(p_packet -> u_tcp == 6 ) 
        {
            if( p_packet -> app_len == 0 )
            {
                if(   p_packet -> p_tcphdr -> syn == 1 &&  p_packet -> p_tcphdr -> ack == 1 )
                {
                    p_session->p_session_ext->tcp_link[p_packet->Directory]->add_fragment(p_packet->seq, 1);
                }
            }
            else
            {
                p_session->p_session_ext->tcp_link[p_packet->Directory]->add_fragment(p_packet->seq, p_packet->app_len);
            }
        }
    }
    //  所以包都需要过的处理流程 
    std::list<int>::iterator it= all_use_list.begin();
    for(;it != all_use_list.end(); it ++)
    {
        if(packet_full_flow_arr[*it] != NULL)
            (packet_full_flow_arr[*it])->full_flow_handle(p_packet , p_session);
    }
    if (p_session)
    {
        // 根据打的标记 处理   
        for(int i = 0 ; i < p_packet -> sign_num ; i++) 
        {
            if(rule_id_handle[p_packet-> packet_sign[i]] != NULL)
            {
                // 单次返回数据 ，写入 value json 中
                (rule_id_handle[p_packet-> packet_sign[i]])->full_flow_handle(p_packet , p_session);
            }
        }
        // 命中策略定义的处理
        for(int i = 0 ; i < p_session -> session_basic.RuleNum ; i++) 
        {
            if(rule_id_handle[p_session -> session_basic.pRule [i]] != NULL)
            {
                // 单次返回数据 ，写入 value json 中
                (rule_id_handle[p_session -> session_basic.pRule [i]])->full_flow_handle(p_packet , p_session);
            }
        }
    }
}
bool ff_conf::time_out(session_pub * p_session)
{
    // 定义
    bool b_ture= true ;
    for(int i = 0 ; i < p_session->session_basic.RuleNum ; i++) 
    {
        if(packet_full_flow_arr[p_session -> session_basic.pRule [i]] != NULL)
        {
            // 单次返回数据 ，写入 value json 中
            if((packet_full_flow_arr[p_session -> session_basic.pRule [i]])->time_out(p_session) == false )
            {
                b_ture = false ;
            }
        }
        //  定义数据
    }
    return  b_ture ;
}
void ff_conf::log(uint32_t time_ns)
{
    // 
    bool b_ture= true ;
    for(int i = 0 ; i <  MAXRULENUM; i++) 
    {
        if(packet_full_flow_arr[i] != NULL)
        {
            // 单次返回数据 ，写入 value json 中
            packet_full_flow_arr[i]->log(time_ns);
        }
        //  定义数据
    }

}
void ff_conf::module_timeout(uint32_t s, CMessage *value, uint32_t thread_id)
{
    std::list<int>::iterator it= all_use_list.begin();
    for(;it != all_use_list.end(); it ++)
    {
        if(packet_full_flow_arr[*it] != NULL)
            (packet_full_flow_arr[*it])->module_timeout(s, value, thread_id);
    }
}

void ff_conf::resources_recovery(session_pub * p_session)
{
    //  所以包都需要过的处理流程 
    std::list<int>::iterator it= all_use_list.begin();
    for(;it != all_use_list.end(); it ++)
    {

        if(packet_full_flow_arr[*it] != NULL)
            (packet_full_flow_arr[*it])->resources_recovery( p_session);
    }
    // 根据打的标记 处理   
    for(int i = 0 ; i < p_session -> session_basic.RuleNum ; i++) 
    {
        if(packet_full_flow_arr[p_session -> session_basic.pRule [i]] != NULL)
        {
            // 单次返回数据 ，写入 value json 中
            (packet_full_flow_arr[p_session -> session_basic.pRule[i]])->resources_recovery(p_session);
        }
    }
}
void ff_conf::session_recovery(session_pub * p_session)
{
    std::list<int>::iterator it= all_use_list.begin();
    for(;it != all_use_list.end(); it ++)
    {

        if(packet_full_flow_arr[*it] != NULL)
            (packet_full_flow_arr[*it])->session_recovery( p_session);
    }
    // 根据打的标记 处理   
    for(int i = 0 ; i < p_session -> session_basic.RuleNum ; i++) 
    {
        if(packet_full_flow_arr[p_session -> session_basic.pRule [i]] != NULL)
        {
            // 单次返回数据 ，写入 value json 中
            (packet_full_flow_arr[p_session -> session_basic.pRule[i]])->session_recovery(p_session);
        }
    }
}
// 
full_flow_engine::full_flow_engine(int thread_num, int thread_id)
{
    p_ff_conf =  new ff_conf(thread_num, thread_id);
}
full_flow_engine::~full_flow_engine()
{
    if(p_ff_conf != NULL) 
    {
        delete p_ff_conf ;
    }
    p_ff_conf = NULL ;
}
void full_flow_engine::handle(c_packet * p_packet ,session_pub * p_session)
{
    p_ff_conf->handle(p_packet , p_session);
}
bool full_flow_engine::time_out(session_pub * p_session)
{
    return p_ff_conf->time_out(p_session);
}
void full_flow_engine::module_timeout(uint32_t s, CMessage *msg, uint32_t thread_id)
{
    p_ff_conf->module_timeout(s, msg, thread_id);
}


void full_flow_engine::resources_recovery(session_pub * p_session)
{
    p_ff_conf ->resources_recovery(p_session);
}

void full_flow_engine::session_recovery(session_pub * p_session)
{
    p_ff_conf ->session_recovery(p_session);
}

void full_flow_engine::log(uint32_t time_ns)
{
    if(p_ff_conf != NULL)
        p_ff_conf ->log(time_ns);
}
ff_plugin_conf::ff_plugin_conf()
{
    should_log_def = 1;
    conf_map.clear();
}
int ff_plugin_conf::load_conf(string conf_path)
{
    ifstream ifs(conf_path);
    if( ifs.is_open() )
    {
        Json::Reader reader;
        Json::Value value;

        if(reader.parse(ifs, value))
        {
            if(false == value["full_flow"].isNull())
            {
                value = value["full_flow"];
                if(false == value["should_log_def"].isNull())
                {
                    should_log_def = value["should_log_def"].asInt();
                }
                if(false == value["plugin"].isNull())
                {
                    for(int i = 0; i < value["plugin"].size(); i ++)
                    {
                        int id = value["plugin"][i]["id"].asInt();
                        plugin_conf val;
                        val.name = value["plugin"][i]["name"].asString();
                        val.should_log = value["plugin"][i]["should_log"].asInt();
                        conf_map[id] = val;
                    }
                }
            }
            return 0;
        }
    }
    return 1;
}
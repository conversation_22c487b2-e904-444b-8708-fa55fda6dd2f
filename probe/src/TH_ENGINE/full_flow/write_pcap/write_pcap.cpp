// Last Update:2019-07-02 11:50:55
/**
 * @file write_pcap.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-10-07
 */

#include <xml_parse.h>
#include "write_pcap.h"
#include "wp_define.h"
#include <fstream>
#include "th_engine_tools.h"
#include "TH_engine_define.h"
#include "Interface/src/TH_ENGINE/PacketAndRule.h"

PCAP_STATISTICS write_pcap::pcap_stat[MAX_THREAD_NUM] = {0};
static uint64_t    total_pcap_bytes = 0;
static uint64_t    total_pcap_pkts = 0;
static uint64_t    total_drop_pcap_bytes = 0;
static uint64_t    total_drop_pcap_pkts = 0;

extern "C"
{
    int write_pcap_get_fullflow_id()
    {
        return 220;
    }

    packet_full_flow  * write_pcap_attach_fullflow(int thread_num, int thread_id)
    {
        return new write_pcap(thread_num, thread_id);
    }
};

static void read_pcap_summary()
{
    string filepath = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/statistics/pcap_statistics.map";
    FILE *pfile = fopen(filepath.c_str(), "r");
    if (pfile)
    {
        int len;
        fseek(pfile,0,SEEK_END);
        len = ftell(pfile);
        fseek(pfile,0,SEEK_SET);
        if (len > 0)
        {
            uint64_t tmp;
            if(1 == fscanf(pfile, "%"PRIu64"", &tmp))
            {
                total_pcap_bytes = tmp;
                if(1 == fscanf(pfile, "%"PRIu64"", &tmp))
                {
                    total_pcap_pkts = tmp;
                    if(1 == fscanf(pfile, "%"PRIu64"", &tmp))
                    {
                        total_drop_pcap_bytes = tmp;
                        if(1 == fscanf(pfile, "%"PRIu64"", &tmp))
                        {
                            total_drop_pcap_pkts = tmp;
                        }
                    }
                }
            }
            if(0 == total_pcap_bytes)
            {
                total_pcap_pkts = 0;
            }
            if(0 == total_pcap_pkts)
            {
                total_pcap_bytes = 0;
            }
            if(0 == total_drop_pcap_bytes)
            {
                total_drop_pcap_pkts = 0;
            }
            if(0 == total_drop_pcap_pkts)
            {
                total_drop_pcap_bytes = 0;
            }
        }
        fclose(pfile);
    }
}

void write_pcap::init()
{
    b_rule_save = 0;
    b_attack_save = 1;
    wait_time = 30;
    ts_now = 0;
    ts_last_check = 0;
    read_pcap_summary();
}

void write_pcap::wp_xml_parse()
{
    xml_parse xml;
    string conf_path = string(getenv("THE_CONF_PATH"))+"/write_pcap.xml";
    xml.set_file_path(conf_path.c_str());
    char * p_value = (char *)xml.get_value("/config/b_rule_save");
    if(p_value != NULL)
    {
        string str_key(p_value);
        if (str_key == "true")
        {
            b_rule_save = 1;
        }
        else
        {
            b_rule_save = 0;
        }
    }
    p_value = (char *)xml.get_value("/config/b_attack_save");
    if(p_value != NULL)
    {
        string str_key(p_value);
        if (str_key == "true")
        {
            b_attack_save = 1;
        }
        else
        {
            b_attack_save = 0;
        }
    }
    p_value = (char *)xml.get_value("/config/wait_time");
    if(p_value != NULL)
    {
        int num = atoi(p_value);
        if(num > 0)
        {
            wait_time = num;
        }
    }
    p_value = (char *)xml.get_value("/config/b_encrypt");
    if(p_value != NULL)
    {
        string str_key(p_value);
        if (str_key == "true")
        {
            wp_config_and_define::b_encrypt = true;
        }
        else
        {
            wp_config_and_define::b_encrypt = false;
        }
    }
    p_value = (char *)xml.get_value("/config/b_cache");
    if(p_value != NULL)
    {
        string str_key(p_value);
        if (str_key == "true")
        {
            wp_config_and_define::b_cache = true;
        }
        else
        {
            wp_config_and_define::b_cache = false;
        }
    }
    p_value = (char *)xml.get_value("/config/b_batchid");
    if(p_value != NULL)
    {
        string str_key(p_value);
        if (str_key == "true")
        {
            wp_config_and_define::b_batchid = true;
        }
        else
        {
            wp_config_and_define::b_batchid = false;
        }
    }
    p_value = (char *)xml.get_value("/config/b_batchid");
    if(p_value != NULL)
    {
        string str_key(p_value);
        if (str_key == "true")
        {
            wp_config_and_define::b_batchid = true;
        }
        else
        {
            wp_config_and_define::b_batchid = false;
        }
    }
    p_value = (char *)xml.get_value("/config/pcap_bytes");
    if(p_value != NULL)
    {
        uint32_t num = (uint32_t)atoi(p_value);
        if(num > 0)
        {
            wp_config_and_define::pcap_bytes = num;
        }
    }
    return;
}

write_pcap::write_pcap(int thread_num, int thread_id):packet_full_flow(thread_num, thread_id)
{
    init();
    p_full_flow_marge  = new file_marge();
    p_black_ssl_marge  = new file_marge();
    p_noip_marge  = new file_marge();
    p_rule_marge = new file_marge();
    p_attack_marge = new file_marge();
    p_inner_marge_map.clear();
    p_inner_marge_head = NULL;
    p_inner_marge_tail = NULL;
    wp_xml_parse();
    pRuleAttribute = PacketAndRule::instance()->GetRuleAttribute();
}




void write_pcap::release_one_marge()
{
    if (NULL==p_inner_marge_head)
    {
        return;
    }
    file_marge *  p_file_marge = NULL;
    p_file_marge = p_inner_marge_head;
    p_inner_marge_head = p_file_marge->next;
    if(p_inner_marge_tail == p_file_marge)
    {
        p_inner_marge_tail == NULL;
    }
    else
    {
        p_file_marge->next->prev = NULL;
    }
    auto iter = p_inner_marge_map.find(p_file_marge->sign);
    if(iter  != p_inner_marge_map.end())
    {
        p_inner_marge_map.erase(iter);
    }
    delete(p_file_marge);
}

file_marge * write_pcap::get_rule_marge(int sign)
{
    file_marge *  p_file_marge = NULL;
    auto iter = p_inner_marge_map.find(sign);
    if(iter != p_inner_marge_map.end())
    {
        p_file_marge = iter -> second;
        if (p_inner_marge_tail == p_file_marge)
            return p_file_marge;
        if(p_inner_marge_head == p_file_marge)
        {
            p_inner_marge_head = p_file_marge->next;
        }
        else
        {
            p_file_marge->prev->next = p_file_marge->next;
        }
        p_file_marge->next->prev = p_file_marge->prev;
        p_file_marge->prev=NULL;
        p_file_marge->next=NULL;
        p_file_marge->prev = p_inner_marge_tail;
        p_file_marge->prev->next = p_file_marge;
        p_inner_marge_tail = p_file_marge;
    }
    else
    {
        if (MAX_OPEN_PCAP <= p_inner_marge_map.size()) //超过最大文件数，关闭最早使用的pcap
        {
            release_one_marge();
        }
        p_file_marge = new file_marge(sign);
        p_inner_marge_map[sign] = p_file_marge;
        if(NULL == p_inner_marge_tail)
        {
            p_inner_marge_head=p_file_marge;
            p_inner_marge_tail=p_file_marge;
        }
        else
        {
            p_file_marge->prev = p_inner_marge_tail;
            p_file_marge->prev->next = p_file_marge;
            p_inner_marge_tail = p_file_marge;
        }
    }
    return p_file_marge;
}

void write_pcap::check_flush(uint32_t now)
{
    if(ts_now != now)
    {
        ts_now = now;
        if ((ts_now > ts_last_check && ts_now - ts_last_check >= wait_time) || (ts_now < ts_last_check && ts_last_check - ts_now >= wait_time))
        {
            ts_last_check = ts_now;
            p_full_flow_marge->marge_flush();
            p_black_ssl_marge->marge_flush();
            p_noip_marge->marge_flush();
            p_rule_marge->marge_flush();
            p_attack_marge->marge_flush();
            auto itor = p_inner_marge_map.begin();
            while(itor != p_inner_marge_map.end())
            {
                itor->second->marge_flush();
                itor ++;
            }
        }
    }
}

void write_pcap::rule_save_handle(c_packet * p_packet ,session_pub * p_session)
{
    std::set<int> sign_set_user;
    std::set<int> sign_set_inner;
    file_marge *p_file_marge = NULL;
    int    b_white_user = 0;
    int    b_has_white_user = 0;

    for(int i = 0 ; i < p_packet -> sign_num ; i++)
    {
        if(pRuleAttribute->BitMapGet(INNER_RULE_IDX, p_packet-> packet_sign[i]))
        {
            sign_set_inner.insert((int)p_packet-> packet_sign[i]);
        }
        else
        {
            sign_set_user.insert((int)p_packet-> packet_sign[i]);
        }
    }
    for(int i = 0; i < p_packet->rt_sign_num; i++)
    {
        if(pRuleAttribute->BitMapGet(INNER_RULE_IDX, p_packet-> rt_rule_sign[i]))
        {
            sign_set_inner.insert((int)p_packet-> rt_rule_sign[i]);
        }
        else
        {
            sign_set_user.insert((int)p_packet-> rt_rule_sign[i]);
        }
    }
    for(int i = 0 ; i < p_session -> session_basic.RuleNum ; i++)
    {
        if(pRuleAttribute->BitMapGet(INNER_RULE_IDX, p_session -> session_basic.pRule[i]))
        {
            sign_set_inner.insert((int)p_session -> session_basic.pRule[i]);
        }
        else
        {
            sign_set_user.insert((int)p_session -> session_basic.pRule[i]);
        }
    }
    for(auto it = sign_set_user.begin(); it != sign_set_user.end(); )
    {
        b_white_user = pRuleAttribute->BitMapGet(WHITE_PCAP_IDX, *it);
        if(b_white_user)
        {
            b_has_white_user = b_white_user;
        }
        if(b_white_user || pRuleAttribute->BitMapGet(EXCEEDS_LIMIT_IDX, *it))
        {
            it = sign_set_user.erase(it);
            continue;
        }
        if(*it < 30500 || *it > 32000)
        {
            if(0 == pRuleAttribute->GetPcapToken(p_packet->thread_id, *it, p_packet->time_ts[0], p_packet->packet_len))
            {
                it = sign_set_user.erase(it);
                continue;
            }
        }
        it ++;
    }
    for(auto it = sign_set_inner.begin(); it != sign_set_inner.end(); )
    {
        if(pRuleAttribute->BitMapGet(WHITE_PCAP_IDX, *it) || pRuleAttribute->BitMapGet(EXCEEDS_LIMIT_IDX, *it))
        {
            it = sign_set_inner.erase(it);
            continue;
        }
        if(0 == pRuleAttribute->GetPcapToken(p_packet->thread_id, *it, p_packet->time_ts[0], p_packet->packet_len))
        {
            it = sign_set_inner.erase(it);
            continue;
        }
        it ++;
    }
    for(auto it = sign_set_inner.begin(); it != sign_set_inner.end(); it ++)
    {
        p_file_marge = get_rule_marge(*it);
        p_file_marge->HandleInnerRule(p_session , p_packet, *it);
    }
    if(b_rule_save)
    {
        if(!sign_set_user.empty())
        {
            pcap_stat[p_packet->thread_id].total_bytes += p_packet -> packet_len;
            pcap_stat[p_packet->thread_id].tmp_bytes += p_packet -> packet_len;
            pcap_stat[p_packet->thread_id].total_pkts ++;
            pcap_stat[p_packet->thread_id].tmp_pkts ++;
            p_rule_marge->HandleRule(p_session , p_packet, sign_set_user);
        }
    }
    else
    {
        if(0 == b_has_white_user || (!sign_set_user.empty()))
        {
            pcap_stat[p_packet->thread_id].total_bytes += p_packet -> packet_len;
            pcap_stat[p_packet->thread_id].tmp_bytes += p_packet -> packet_len;
            pcap_stat[p_packet->thread_id].total_pkts ++;
            pcap_stat[p_packet->thread_id].tmp_pkts ++;
            p_full_flow_marge->HandleFullFlow(p_session , p_packet, sign_set_user);
        }
        else
        {
            pcap_stat[p_packet->thread_id].tmp_drop_bytes += p_packet -> packet_len;
            pcap_stat[p_packet->thread_id].tmp_drop_pkts ++;

            th_engine_g_var.white_list_hit_bytes[thread_id] += p_packet -> packet_len;//离线探针白名单统计数据
            th_engine_g_var.white_list_hit_pkts[thread_id]++;
        }
    }
}
bool write_pcap::analysis_should_record(c_packet * p_packet)
{
    if(p_packet->sign_num + p_packet->rt_sign_num)    //命中规则
    {
        bool should_record = false;
        for(int i = 0; i < p_packet->sign_num && should_record == false; i ++)
        {
            if(0 == pRuleAttribute->BitMapGet(WHITE_PCAP_IDX, p_packet->packet_sign[i]))
            {
                should_record = true;
                break;
            }
        }
        for(int i = 0; i < p_packet->rt_sign_num && should_record == false; i ++)
        {
            if(0 == pRuleAttribute->BitMapGet(WHITE_PCAP_IDX, p_packet->rt_rule_sign[i]))
            {
                should_record = true;
                break;
            }
        }
        return should_record;
    }
    else        //未命中规则
    {
        return true;
    }
}

void write_pcap::full_flow_handle(c_packet * p_packet ,session_pub * p_session)
{
    // 写文面积
    check_flush(p_packet->time_ts[0]);
    if (p_session)
    {
        rule_save_handle(p_packet, p_session);
        if(p_session -> b_black_ssl)
        {
            pcap_stat[p_packet->thread_id].total_bytes += p_packet -> packet_len;
            pcap_stat[p_packet->thread_id].tmp_bytes += p_packet -> packet_len;
            pcap_stat[p_packet->thread_id].total_pkts ++;
            pcap_stat[p_packet->thread_id].tmp_pkts ++;
            p_black_ssl_marge -> HandleSSL(p_session , p_packet);
        }
    }
    else if(p_packet->flags.b_noip)
    {
        if(p_packet->flags.b_noip_record)
        {
            pcap_stat[p_packet->thread_id].total_bytes += p_packet -> packet_len;
            pcap_stat[p_packet->thread_id].tmp_bytes += p_packet -> packet_len;
            pcap_stat[p_packet->thread_id].total_pkts ++;
            pcap_stat[p_packet->thread_id].tmp_pkts ++;
            p_noip_marge -> HandleNoip(NULL, p_packet);
        }
    }
    else    //丢包报文
    {
        if(b_attack_save && p_packet->flags.b_drop_defense) //攻击流量留存
        {
            pcap_stat[p_packet->thread_id].total_bytes += p_packet -> packet_len;
            pcap_stat[p_packet->thread_id].tmp_bytes += p_packet -> packet_len;
            pcap_stat[p_packet->thread_id].total_pkts ++;
            pcap_stat[p_packet->thread_id].tmp_pkts ++;
            p_attack_marge -> HandleAttack(NULL, p_packet);
        }
        //仅当未开启攻击流量留存时，才将攻击流量留存到全流量pcap内
        else if(0 == b_rule_save && 0 == p_packet->flags.b_drop_filter && 0 == p_packet->flags.b_drop_beyond)
        {
            pcap_stat[p_packet->thread_id].total_bytes += p_packet -> packet_len;
            pcap_stat[p_packet->thread_id].tmp_bytes += p_packet -> packet_len;
            pcap_stat[p_packet->thread_id].total_pkts ++;
            pcap_stat[p_packet->thread_id].tmp_pkts ++;
            p_full_flow_marge -> HandleFullFlow(NULL , p_packet);
        }
    }
}
bool write_pcap::time_out(session_pub *p_session)
{
    return true;
}
// 
void write_pcap::resources_recovery(session_pub * p_session)
{
}

void write_pcap::session_recovery(session_pub * p_session) // 资源回收接口
{
}
void write_pcap::module_timeout(uint32_t ts, CMessage *value, uint32_t thread_id)
{
    p_full_flow_marge->marge_flush();
    p_black_ssl_marge->marge_flush();
    p_noip_marge->marge_flush();
    p_rule_marge->marge_flush();
    p_attack_marge->marge_flush();
    auto itor = p_inner_marge_map.begin();
    while(itor != p_inner_marge_map.end())
    {
        itor->second->marge_flush();
        itor ++;
    }
}

static void update_pcap_summary()
{
    string filepath = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/statistics/pcap_statistics.map";
    string filepath_back = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/statistics/.pcap_statistics.map.tmp";

    FILE *pfile = fopen(filepath_back.c_str(), "w");
    if (pfile)
    {
        fprintf(pfile, "%"PRIu64"\n%"PRIu64"\n%"PRIu64"\n%"PRIu64"\n", total_pcap_bytes, total_pcap_pkts, total_drop_pcap_bytes, total_drop_pcap_pkts);
        fclose(pfile);
        rename(filepath_back.c_str(), filepath.c_str());
    }
}

void write_pcap::log(uint32_t ntime)
{
    // uint64_t total_bytes = 0;
    // uint64_t total_pkts = 0;
    uint64_t tmp_bytes = 0;
    uint32_t tmp_pkts = 0;
    uint64_t tmp_drop_bytes = 0;
    uint32_t tmp_drop_pkts = 0;
    for(int i=0; i < MAX_THREAD_NUM; i ++)
    {
        // total_bytes += pcap_stat[i].total_bytes;
        // total_pkts += pcap_stat[i].total_pkts;
        tmp_bytes += pcap_stat[i].tmp_bytes;
        pcap_stat[i].tmp_bytes = 0;
        tmp_pkts += pcap_stat[i].tmp_pkts;
        pcap_stat[i].tmp_pkts = 0;
        tmp_drop_bytes += pcap_stat[i].tmp_drop_bytes;
        pcap_stat[i].tmp_drop_bytes = 0;
        tmp_drop_pkts += pcap_stat[i].tmp_drop_pkts;
        pcap_stat[i].tmp_drop_pkts = 0;
    }
    total_pcap_bytes += tmp_bytes;
    total_pcap_pkts += tmp_pkts;
    total_drop_pcap_bytes += tmp_drop_bytes;
    total_drop_pcap_pkts += tmp_drop_pkts;
    update_pcap_summary();
    char tmp[1024];
    int msglen;
    msglen = sprintf(tmp, "{\"type\":%d,\"time\":%u,\"pcap_bytes_all\":\"%"PRIu64"\",\"pcap_pkts_all\":\"%"PRIu64"\",\"pcap_bytes_30s\":\"%"PRIu64"\",\"pcap_pkts_30s\":%u,\"pcap_bps\":\"%"PRIu64"\",\"task_id\":\"%lld\",\"batch_id\":\"%lld\",\"device_id\":\"%u\"}",
            210, ntime, total_pcap_bytes, total_pcap_pkts, tmp_bytes, tmp_pkts, tmp_bytes/30*8,config_and_define::task_id, config_and_define::batch_id, th_engine_tools::instance()->device_id
        );
    th_engine_tools::instance()->send_log("slog", NULL, 0, tmp, msglen);
    msglen = sprintf(tmp, "{\"type\":%d,\"time\":%u,\"drop_pcap_bytes_all\":\"%"PRIu64"\",\"drop_pcap_pkts_all\":\"%"PRIu64"\",\"drop_pcap_bytes_30s\":\"%"PRIu64"\",\"drop_pcap_pkts_30s\":%u,\"drop_pcap_bps\":\"%"PRIu64"\",\"task_id\":\"%lld\",\"batch_id\":\"%lld\",\"device_id\":\"%u\"}",
            221, ntime, total_drop_pcap_bytes, total_drop_pcap_pkts, tmp_drop_bytes, tmp_drop_pkts, tmp_drop_bytes/30*8,config_and_define::task_id, config_and_define::batch_id, th_engine_tools::instance()->device_id
        );
    th_engine_tools::instance()->send_log("slog", NULL, 0, tmp, msglen);
}



// Last Update:2019-04-23 14:00:58
/**
 * @file pcap_tw.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-09-29
 */
#include "pcap_t.h"
#include "wp_define.h"
#include <unistd.h>
#include  <commit_tools.h>
struct pcap_file_header_p
{
    uint32_t magic;
    uint16_t version_major;
    uint16_t version_minor;
    uint32_t thiszone;     /* gmt to local correction */
    uint32_t sigfigs;    /* accuracy of timestamps */
    uint32_t snaplen;    /* max length saved portion of each pkt */
    uint32_t linktype;   /* data link type (LINKTYPE_*) */
};
char pcap_magic[]={0xd4,0xc3 ,0xb2 ,0xa1};
#define PCAP_VERSION_MAJOR 2
#define PCAP_VERSION_MINOR 4
//#define PCAP_SNAPLEN 16777200
#define PCAP_LINKTYPE 1

int num  = 0 ;

struct pcap_pkthdr_p
{
    // struct timeval ts;      /* time stamp */
    uint32_t t_stamp ;
    uint32_t t_us_stamp ;
    uint32_t caplen;     /* length of portion present */
    uint32_t len;        /* length this packet (off wire) */
};
char p_pcapfile_hdr[24] = {0xd4, 0xc3, 0xb2, 0xa1, 0x2, 0x0, 0x4, 0x0, 0x0, 0x0, 0x0, 0x0,
                          0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x0, 0x1, 0x0, 0x0, 0x0};
//{0xD4,0xC3,0xb2,0xa1,0x20,0x00,0x40,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x40,0x00,0x01,0x00,0x00,0x00};
//{0x4d,0x3c,0xb2,0xa1,0x20,0x00,0x40,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x40,0x00,0x01,0x00,0x00,0x00};    //nanoseconds
pcap_tw::pcap_tw(std::string sfilename,bool bDan, int linktype)
{
    filename = sfilename ;
    fp = NULL ;

    write_len = 0 ;
    offise = 0 ;
    if(bDan )
    {
        PCAP_SNAPLEN =  16777200  ;
    }
    else 
    {
        PCAP_SNAPLEN = sizeof(struct pcap_pkthdr_p) + 8 + MAX_PKT_LEN + MAX_EXT_BUF_LEN;
    }
    // 写入文件头
    if(access(filename.c_str(), 0) != 0)
    {
        cpy_buf(p_pcapfile_hdr,20);
        cpy_buf((char *)&linktype,4);
    }
}


pcap_tw::~pcap_tw()
{
    if(offise > 0) 
    {
        if(write_len + offise > 24) 
        {
             write_file();
        }
    }
    if(fp != NULL)
    {
        fflush(fp);
        fclose(fp) ;
        fp = NULL ;
    }
}

void pcap_tw::cpy_buf( char * buf , int len )
{
    // if(len > MAXWIRTEPCAPBUF - offise )
    // {
    //     write_file();
    // }
    memcpy(buff + offise , buf , len ) ;
    offise += len ;
    // if(MAXWIRTEPCAPBUF - offise <= PCAP_SNAPLEN  ) 
    // {
    //     write_file();
    // }
}
void pcap_tw::cpy_buf( char * buf , int len, char *extra_buf, int extra_len )
{
    // if(len + (extra_buf?extra_len:0) > MAXWIRTEPCAPBUF - offise )
    // {
    //     write_file();
    // }
    memcpy(buff + offise , buf , len ) ;
    offise += len ;
    memcpy(buff + offise, extra_buf, extra_len);
    offise += extra_len ;
    if(MAXWIRTEPCAPBUF - offise <= PCAP_SNAPLEN  ) 
    {
        write_file();
    }
}

//写加密key 
void pcap_tw::write_pIV(CSteamCipher& MyStream)
{
    char pBasicKey[]="*GeekSec*gspcap";
    int KeyLen=strlen(pBasicKey);
    unsigned char pIV[8];
    pIV[0]=rand();
    pIV[1]=rand();
    pIV[2]=rand();
    pIV[3]=rand();
    pIV[4]=rand();
    pIV[5]=rand();
    pIV[6]=rand();
    pIV[7]=rand();
    MyStream.Init((unsigned char*)pBasicKey,KeyLen,pIV,8);
    cpy_buf((char*)pIV, 8);
}

// 单包写入
void pcap_tw::write_data(uint64_t t_tamp , char * buf , int len , char *extra_data, int extra_data_len)
{
    if(len > MAX_PKT_LEN)
    {
        if(len + sizeof(struct pcap_pkthdr_p) + 8 + MAX_EXT_BUF_LEN > MAXWIRTEPCAPBUF) //缓冲区不足
        {
            return;
        }
        else
        {
            write_file();
        }
    }
    // 写入文件头
    struct pcap_pkthdr_p * p = (struct pcap_pkthdr_p *)(buff + offise);
    p->t_stamp = t_tamp / 1000000;  //  ntohll(t_tamp) ;
    p->t_us_stamp = t_tamp % 1000000 ;
    printf("%u %u\n",p->t_stamp );
    //p->t_stamp = t_tamp ;
    // p-> caplen = htonl(len);
    p-> caplen =len + (extra_data?extra_data_len:0);
    //p->len = htonl(len );
    p->len = len ;
    offise += sizeof(struct pcap_pkthdr_p) ;
    if (wp_config_and_define::b_encrypt)
    {
        CSteamCipher MyStream;
        //初始化加密模块并且写入加密key
        write_pIV(MyStream);
        //将数据加密
        MyStream.Encrypt((unsigned char*)buf, len);
        MyStream.Quit();
    }
    cpy_buf(buf,len,extra_data, extra_data_len);
}
void pcap_tw::write_data(uint32_t ts, uint32_t nts , char * buf , int len, char *extra_data, int extra_data_len)
{
    if(len > MAX_PKT_LEN)
    {
        if(len + sizeof(struct pcap_pkthdr_p) + 8 + MAX_EXT_BUF_LEN > MAXWIRTEPCAPBUF) //缓冲区不足
        {
            return;
        }
        else
        {
            write_file();
        }
    }
    // 写入文件头
    struct pcap_pkthdr_p * p = (struct pcap_pkthdr_p *)(buff + offise);
    p->t_stamp = ts;  //  ntohll(t_tamp) ;
    p->t_us_stamp = nts/1000;
    //p->t_stamp = t_tamp ;
    //p-> caplen = htonl(len);
    p-> caplen =len + (extra_data?extra_data_len:0);
    //p->len =htonl(len) ;
    p->len = len ;
    offise += sizeof(struct pcap_pkthdr_p) ;
    if (wp_config_and_define::b_encrypt)
    {
        CSteamCipher MyStream;
        //初始化加密模块并且写入加密key
        write_pIV(MyStream);
        //将数据加密
        MyStream.Encrypt((unsigned char*)buf, len);
        MyStream.Quit();
    }
    cpy_buf(buf,len, extra_data, extra_data_len);
}
void pcap_tw::write_file()
{
    if(fp == NULL) 
    {
        // 创建目录  // 
        std::string path(filename , 0 , filename.rfind("/")+1);
        create_path(path);
        fp = fopen(filename.c_str(),"a+");
        if(fp == NULL) 
        {
            printf("文件打开错误,filename:%S \n", filename.c_str());
            abort() ;
        }
    }
    fwrite(buff,offise  , 1 ,fp ) ; 
    fflush(fp);
    write_len += offise ;
    offise = 0 ;
}


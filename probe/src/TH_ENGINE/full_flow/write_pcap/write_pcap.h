// Last Update:2019-07-02 11:34:09
/**
 * @file write_pcap.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-10-07
 */

#ifndef WRITE_PCAP_H
#define WRITE_PCAP_H
#include <TH_engine_interface.h>
#include "write_pcap_marge.h"
#include "th_bitmap.h"
#include "DataStructure/TemplateMatch.h"
#include "DataStructure/Func_Math.h"
#include "Interface/src/Interface/Interface_RuleAttribute.h"
#define MAX_THREAD_NUM 64
extern "C"
{
    int write_pcap_get_fullflow_id();

    packet_full_flow * write_pcap_attach_fullflow(int,int);
};

#define MAX_OPEN_PCAP 64

typedef struct
{
    uint64_t total_bytes;
    uint64_t tmp_bytes;
    uint64_t total_pkts;
    uint32_t tmp_pkts;
    uint64_t tmp_drop_bytes;
    uint32_t tmp_drop_pkts;
} PCAP_STATISTICS;


class write_pcap : public packet_full_flow
{
    public :
        write_pcap(int thread_num, int thread_id);
        virtual ~write_pcap() 
        {
            if(p_full_flow_marge != NULL)
                delete p_full_flow_marge ;
            if(p_black_ssl_marge != NULL)
                delete p_black_ssl_marge ;
            if(p_noip_marge != NULL)
                delete p_noip_marge ;
            if(p_rule_marge != NULL)
                delete p_rule_marge ;
            if(p_attack_marge != NULL)
                delete p_attack_marge ;
            auto itor = p_inner_marge_map.begin();
            while(itor != p_inner_marge_map.end())
            {
                delete itor->second;
                itor ++;
            }
            p_inner_marge_map.clear();
            p_inner_marge_head = NULL;
            p_inner_marge_tail = NULL;
        }
        void init();
        void wp_xml_parse();
        void check_flush(uint32_t now);
        virtual void full_flow_handle(c_packet * p_packet ,session_pub * p_session) ;
        virtual bool time_out(session_pub * p_session); //  数据超时接口 ， 可以用作定时调用接口
        virtual void resources_recovery(session_pub * p_session) ; // 资源回收接口
        virtual void session_recovery(session_pub * p_session) ; // 资源回收接口
        virtual void module_timeout(uint32_t ts, CMessage *value, uint32_t thread_id);
        virtual void log(uint32_t ntime);
        static PCAP_STATISTICS pcap_stat[MAX_THREAD_NUM];
    private:
        bool analysis_should_record(c_packet * p_packet);
        void release_one_marge();
        file_marge * get_rule_marge(int sign);
        void rule_save_handle(c_packet * p_packet ,session_pub * p_session);

        file_marge *p_full_flow_marge;
        file_marge *p_black_ssl_marge;
        file_marge *p_noip_marge;
        file_marge *p_rule_marge;
        file_marge *p_attack_marge;
        std::map<int, file_marge *> p_inner_marge_map;
        file_marge *p_inner_marge_head;
        file_marge *p_inner_marge_tail;

        CRule_Attribute *pRuleAttribute;
        uint32_t b_rule_save;
        uint32_t b_attack_save;
        uint32_t ts_now;
        uint32_t ts_last_check;
        uint32_t wait_time;
};


#endif  /*write_pcap_H*/

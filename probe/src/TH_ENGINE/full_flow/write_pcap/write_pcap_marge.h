// Last Update:2019-11-01 10:17:33
/**
 * @file write_pcap_marge.h
 * @brief : 写文件管理
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-11-06
 */

#ifndef WRITE_PCAP_MARGE_H
#define WRITE_PCAP_MARGE_H
#include <commit_tools.h>
#include "pcap_t.h"
#include <set>
#include <map>
#include <string>
#include "wp_define.h"
#include "th_bitmap.h"
#include "GeneralInclude/Define_ProtocolID.h"
#include "geeksec_pcap_ext.h"
#include "TH_engine_define.h"

class file_bucket
{
    public:
        file_bucket(std::string key, int linktype,string linktype_str, int b_inner, uint32_t ts)
        {
            idx = 0;
            this->key = key;
            this->linktype = linktype;
            this->linktype_str = linktype_str;
            this->b_inner = b_inner;

            file_name = key;
            if(!b_inner)
            {
                char ts_buf[22] = {0};
                sprintf(ts_buf, "%u_%d", ts, idx);
                file_name += string(ts_buf);
            }
            file_name += linktype_str;
            if (wp_config_and_define::b_encrypt)
            {
                file_name += ".gspcap";
            }
            else
            {
                file_name += ".pcap";
            }
            p_file = new pcap_tw(file_name,false,linktype);
        }
        ~file_bucket()
        {
            if(p_file != NULL)
            {
                delete p_file ;
            }
            p_file  = NULL;
        }
        void roll_file(uint32_t ts)
        {
            idx ++;

            if(p_file != NULL)
            {
                delete p_file ;
            }
            p_file  = NULL;
            file_name = key;
            if(!b_inner)
            {
                char ts_buf[22] = {0};
                sprintf(ts_buf, "%u_%d", ts, idx);
                file_name += string(ts_buf);
            }
            file_name += linktype_str;
            if (wp_config_and_define::b_encrypt)
            {
                file_name += ".gspcap";
            }
            else
            {
                file_name += ".pcap";
            }
            p_file = new pcap_tw(file_name,false,linktype);
        }
/**
 * key:
 *  /dev/shm/pcapfiles/0/123/32001/112182/26923773
 *  /data/pcapfiles/0/123/rule/112182/
 * file_name:
 *  /dev/shm/pcapfiles/0/123/32001/112182/26923773.pcap
 *  /dev/shm/pcapfiles/0/123/32001/112182/26923773.10.pcap
 *  /dev/shm/pcapfiles/0/123/32001/112182/26923773.277.gspcap
 *  /data/pcapfiles/0/123/rule/112182/1615426435_0.pcap
 *  /data/pcapfiles/0/123/rule/112182/1615426435_0.10.pcap
 *  /data/pcapfiles/0/123/rule/112182/1615426435_0.277.gspcap
 **/
        string file_name;
        string key;
        string linktype_str;
        int linktype;
        int b_inner;
        uint32_t idx;
        char ext_buf[1024];
        int ext_buf_len;

        void add_session_id_tlv(session_pub * p_session , c_packet * p_packet)
        {
            gs_pkt_tlv *ptlv = (gs_pkt_tlv *)(&ext_buf[ext_buf_len]);
            ptlv->type = PKT_ID;
            ptlv->len = ext_len_map[PKT_ID];
            ext_buf_len += 2;
            memcpy(&ext_buf[ext_buf_len], &p_packet->sy_crc64, ptlv->len);
            ext_buf_len += ptlv->len;

            ptlv = (gs_pkt_tlv *)(&ext_buf[ext_buf_len]);
            ptlv->type = SS_ID;
            ptlv->len = ext_len_map[SS_ID];
            ext_buf_len += 2;
            if(p_session)
            {
                memcpy(&ext_buf[ext_buf_len], &p_session->session_basic.ConnectKeyID, ptlv->len);
            }
            else
            {
                memcpy(&ext_buf[ext_buf_len], &p_session, ptlv->len);
            }
            ext_buf_len += ptlv->len;
        }
        void add_firstpkt_tlv(session_pub * p_session , c_packet * p_packet)
        {
            gs_pkt_tlv *ptlv = (gs_pkt_tlv *)(&ext_buf[ext_buf_len]);
            ptlv->type = FIRST_PKT;
            ptlv->len = ext_len_map[FIRST_PKT];
            ext_buf_len += 2;
            ext_buf[ext_buf_len] = 1;
            ext_buf_len += ptlv->len;
        }
        void add_rule_id_tlv(std::set<int> sign_set_user)
        {
            for(auto it = sign_set_user.begin(); it != sign_set_user.end(); it ++)
            {
                gs_pkt_tlv *ptlv = (gs_pkt_tlv *)(&ext_buf[ext_buf_len]);
                ptlv->type = RULE_ID;
                ptlv->len = ext_len_map[RULE_ID];
                ext_buf_len += 2;
                int rule = *it;
                memcpy(&ext_buf[ext_buf_len], (void *)&rule, ptlv->len);
                ext_buf_len += ptlv->len;
            }
        }
        void  add_packet_info_inner(session_pub * p_session , c_packet * p_packet)
        {
            ext_buf_len = 0;
            add_session_id_tlv(p_session, p_packet);
            if(p_session && 1 == p_session->session_basic.pPacketNum[0] && 0 == p_session->session_basic.pPacketNum[1])
            {
                add_firstpkt_tlv(p_session, p_packet);
            }
            p_file -> write_data(p_packet->time_ts[0] , p_packet -> time_ts[1] ,(char *) p_packet -> buf , p_packet -> packet_len, (char *)ext_buf, ext_buf_len );
        }
        void  add_packet_info_rule(session_pub * p_session , c_packet * p_packet, std::set<int> sign_set_user)
        {
            ext_buf_len = 0;
            add_session_id_tlv(p_session, p_packet);
            add_rule_id_tlv(sign_set_user);
            if(p_session && 1 == p_session->session_basic.pPacketNum[0] && 0 == p_session->session_basic.pPacketNum[1])
            {
                add_firstpkt_tlv(p_session, p_packet);
            }
            p_file -> write_data(p_packet->time_ts[0] , p_packet -> time_ts[1] ,(char *) p_packet -> buf , p_packet -> packet_len, (char *)ext_buf, ext_buf_len );
            if(p_file->get_write_len() >= wp_config_and_define::pcap_bytes)
            {
                roll_file(p_packet->time_ts[0]);
            }
        }
        void add_packet_info(session_pub * p_session , c_packet * p_packet)
        {
            ext_buf_len = 0;
            add_session_id_tlv(p_session, p_packet);
            if(p_session && 1 == p_session->session_basic.pPacketNum[0] && 0 == p_session->session_basic.pPacketNum[1])
            {
                add_firstpkt_tlv(p_session, p_packet);
            }
            p_file -> write_data(p_packet->time_ts[0] , p_packet -> time_ts[1] ,(char *) p_packet -> buf , p_packet -> packet_len, (char *)ext_buf, ext_buf_len );
            if(p_file->get_write_len() >= wp_config_and_define::pcap_bytes)
            {
                roll_file(p_packet->time_ts[0]);
            }
        }
      pcap_tw * p_file ;
    private:
        int num ; // 使用的多少
        // std::set<uint64_t> session_id_set ;
};
class file_marge
{
    public:
        file_marge()
        {
            last_time = 0;
            file_map.clear();
            prev = NULL;
            next = NULL;
            key_writeing = "";
        }
        file_marge(int i)
        {
            last_time = 0;
            file_map.clear();
            prev = NULL;
            next = NULL;
            sign = i;
            key_writeing = "";
        }
        ~file_marge()
        {
            std::map<std::string , file_bucket *> ::iterator iter =  file_map.begin();
            for(;iter != file_map.end();iter ++)
            {
                    delete  iter ->second ;
            }
            file_map.clear();
        }
        const char *get_linktype_str(c_packet * p_packet)
        {
            switch (p_packet->first_proto)
            {
                case PROTOCOL_LOOPBACK:
                {
                    return ".1300";
                    break;
                }
                case PROTOCOL_PPP:
                {
                    return ".10";
                    break;
                }
                case PROTOCOL_CHDLC:
                {
                    return ".113";
                    break;
                }
                case PROTOCOL_IEEE80211:
                {
                    return ".277";
                    break;
                }
                case PROTOCOL_SLL:
                {
                    return ".562";
                    break;
                }
                default:
                {
                    return "";
                    break;
                }
            }
        }
        int get_linktype(c_packet * p_packet)
        {
            switch (p_packet->first_proto)
            {
                case PROTOCOL_LOOPBACK:
                {
                    return 0;
                    break;
                }
                case PROTOCOL_PPP:
                {
                    return 9;
                    break;
                }
                case PROTOCOL_CHDLC:
                {
                    return 104;
                    break;
                }
                case PROTOCOL_IEEE80211:
                {
                    return 105;
                    break;
                }
                case PROTOCOL_SLL:
                {
                    return 113;
                    break;
                }
                default:
                {
                    return 1;
                    break;
                }
            }
        }
        void HandleInnerRule(session_pub * p_session , c_packet * p_packet ,int rule_id)
        {
            std::string key = key_str_inner(p_session, p_packet , rule_id);
            file_bucket * p = find_file_bucket(key, p_session, p_packet, 1);
            p->add_packet_info_inner(p_session, p_packet);
        }
        void HandleRule(session_pub * p_session , c_packet * p_packet ,std::set<int> sign_set_user)
        {
            std::string key = key_str_rule(p_session, p_packet);
            file_bucket * p = find_file_bucket(key, p_session, p_packet);
            p->add_packet_info_rule(p_session, p_packet, sign_set_user);
        }
        void HandleFullFlow(session_pub * p_session , c_packet * p_packet)
        {
            std::string key = key_str_full(p_session, p_packet);
            file_bucket * p = find_file_bucket(key, p_session, p_packet);
            p->add_packet_info(p_session, p_packet);
        }
        void HandleFullFlow(session_pub * p_session , c_packet * p_packet ,std::set<int> sign_set_user)
        {
            std::string key = key_str_full(p_session, p_packet);
            file_bucket * p = find_file_bucket(key, p_session, p_packet);
            p->add_packet_info_rule(p_session, p_packet, sign_set_user);
        }
        void HandleSSL(session_pub * p_session , c_packet * p_packet)
        {
            std::string key = key_str_ssl(p_session, p_packet);
            file_bucket * p = find_file_bucket(key, p_session, p_packet);
            p->add_packet_info(p_session, p_packet);
        }
        void HandleNoip(session_pub * p_session , c_packet * p_packet)
        {
            std::string key = key_str_noip(p_session, p_packet);
            file_bucket * p = find_file_bucket(key, p_session, p_packet);
            p->add_packet_info(p_session, p_packet);
        }
        void HandleAttack(session_pub * p_session , c_packet * p_packet)
        {
            std::string key = key_str_attack(p_session, p_packet);
            file_bucket * p = find_file_bucket(key, p_session, p_packet);
            p->add_packet_info(p_session, p_packet);
        }

        std::string key_str(c_packet * p_packet, const char *type)
        {
            char path[256];
            uint32_t ts_dir = p_packet->time_ts[0]/(3600*4);
            sprintf(path,"/data/%lld/%lld/pcapfiles/%u/%s/%u/",
                    config_and_define::task_id,
                    config_and_define::batch_id,
                    p_packet->thread_id,
                    type,
                    ts_dir
                );
            return string(path);
        }

        std::string key_str_rule(session_pub * p_session , c_packet * p_packet)// 
        {
            return key_str(p_packet, "rule");
        }
        std::string key_str_full(session_pub * p_session , c_packet * p_packet)//
        {
            return key_str(p_packet, "full_flow");
        }
        std::string key_str_inner(session_pub * p_session , c_packet * p_packet , int rule_id)
        {
            return key_str(p_packet, "inner");
        }
        std::string key_str_ssl(session_pub * p_session ,c_packet *  p_packet )// 
        {
            return key_str(p_packet, "ssl");
        }
        std::string key_str_noip(session_pub * p_session ,c_packet *  p_packet )// 
        {
            return key_str(p_packet, "noip_packet");
        }
        std::string key_str_attack(session_pub * p_session ,c_packet *  p_packet )// 
        {
            return key_str(p_packet, "attack");
        }
        file_bucket * find_file_bucket(std::string key, session_pub * p_session , c_packet * p_packet ,int b_inner = 0)
        {
            file_bucket * p_file_bucket = NULL;
            if (key != key_writeing)
            {
                auto iter = file_map.begin();
                while(iter != file_map.end())
                {
                    delete iter->second;
                    iter ++;
                }
                file_map.clear();
                key_writeing = key;
            }
            std::string file_key = key_writeing;
            string linktype_str(get_linktype_str(p_packet));
            file_key += linktype_str;
            if (wp_config_and_define::b_encrypt)
            {
                file_key += ".gspcap";
            }
            else
            {
                file_key += ".pcap";
            }
            int linktype = get_linktype(p_packet);
            auto iter = file_map.find(file_key);
            if(iter != file_map.end())
            {
                p_file_bucket = iter->second;
            }
            else
            {
                p_file_bucket = new file_bucket(key ,linktype, linktype_str, b_inner, p_packet->time_ts[0]);
                file_map.insert(std::pair<std::string , file_bucket *> (file_key , p_file_bucket)) ;
            }
            return p_file_bucket;
        }
        void marge_flush()
        {
            auto iter =  file_map.begin();
            while(iter != file_map.end())
            {
                iter->second->p_file->write_file();
                iter ++;
            }
        }
        
        std::map <std::string , file_bucket *>  file_map ;
        uint32_t last_time;
        int sign;
        string key_writeing;
        file_marge *prev;
        file_marge *next;
};
#endif  /*WRITE_PCAP_MARGE_H*/

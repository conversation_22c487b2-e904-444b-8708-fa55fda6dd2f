#ifndef NOIP_SESSION_H
#define NOIP_SESSION_H
#include <TH_engine_interface.h>
#include "MapListUnion.h"
using namespace std;
extern "C"
{
    int noip_get_fullflow_id();

    packet_full_flow* noip_attach_fullflow(int,int);
};

typedef struct s_mac_pair
{
    uint64_t mac_a;
    uint64_t mac_b;
    bool operator <(const struct s_mac_pair& other) const
    {
        if (mac_a < other.mac_a)
        {
            return true;
        }
        else if (mac_a == other.mac_a)
        {
            return (mac_b < other.mac_b);
        }
        else
        {
            return false;
        }
        
    }
}mac_pair;

class mac_con_info
{
public:
    mac_con_info(mac_pair in)
    {
        key = in;
        start_ts = time(NULL);
        pkt_a2b = 0;
        pkt_b2a = 0;
    }
    mac_pair key;
    time_t start_ts;
    uint32_t min_ts;
    uint32_t max_ts;
    uint32_t pkt_a2b;
    uint32_t pkt_b2a;
};

class noip_session : public packet_full_flow
{
    public :
        noip_session(int thread_num, int thread_id);
        virtual ~noip_session() 
        {
        }
        void init();
        virtual void full_flow_handle(c_packet * p_packet ,session_pub * p_session) ;
        virtual bool time_out(session_pub * p_session); //  数据超时接口 ， 可以用作定时调用接口
        virtual void resources_recovery(session_pub * p_session) ; // 资源回收接口
        virtual void session_recovery(session_pub * p_session) ; // 资源回收接口
        virtual void module_timeout(uint32_t ts, CMessage *value, uint32_t thread_id);
    private:
        int max_thread_session ;
        CMapListUnion<mac_pair, mac_con_info *> noip_map;
        CMapListUnion<mac_pair, mac_con_info *> mac_con_map;
        long long task_id;
        long long batch_id;

};
#endif  /*SINGLE_SESSION_H*/

#ifndef __MAPLISTUNION_H__
#define __MAPLISTUNION_H__
#include <map>
#include <list>
using namespace std;

template<typename T<PERSON><PERSON>,typename TValue>
class CMapListUnion
{
public:
	struct CStornUnion
	{
		TKey keyindex;
		TValue value;
	};
	bool push(TKey Key, TValue temp)
	{
		auto itrM = m_map.find(Key);
		if (itrM != m_map.end())
		{
			return false;
		}

		CStornUnion stornUnion;
		stornUnion.keyindex = Key;
		stornUnion.value = temp;

		typename list <CStornUnion>::iterator itr;
		m_list.push_back(stornUnion);
		
		itr = m_list.end();
		itr--;
		m_map.insert(make_pair(Key, itr));

		return true;
	}
	void erase(TKey Key)
	{
		auto itr = m_map.find(Key);
		if (itr != m_map.end())
		{
			m_list.erase(itr->second);
			m_map.erase(itr);
		}
	}
	bool find(<PERSON><PERSON><PERSON>, TValue &data)
	{
		auto itr = m_map.find(Key);
		if (itr != m_map.end())
		{
			CStornUnion &Union = *itr->second;
			data=Union.value;
			return true;
		}
		else
			return false;
	}
	bool first(TValue &data)
	{
		if (!m_list.empty())
		{
			CStornUnion &Union = m_list.front();
			data = Union.value;

			return true;
		}
		else
		{
			return false;
		}
	}
	bool pop(TValue &data)
	{
		if (!m_list.empty())
		{
			CStornUnion &Union = m_list.front();
			data = Union.value;

			auto itr = m_map.find(Union.keyindex);
			if (itr != m_map.end())
			{
				m_map.erase(itr);
			}

			m_list.pop_front();

			return true;
		}
		else
		{
			return false;
		}
	}
	int size()
	{
		return m_map.size();
	}
public:
	map<TKey, typename list <CStornUnion>::iterator>m_map;
	list<CStornUnion>m_list;
};

#endif
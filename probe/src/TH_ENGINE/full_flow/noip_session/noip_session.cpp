#include "noip_session.h"
#include <ZMPNMsg.pb.h>
#include "th_engine_tools.h"
extern "C"
{
    int noip_get_fullflow_id()
    {
        return 1122;
    }

    packet_full_flow* noip_attach_fullflow(int thread_num, int thread_id)
    {
        return new noip_session(thread_num, thread_id);
    }
};

static void macToStr(unsigned char * pMac,char *IN_pBuf,DWORD IN_BufSize)
{
    sprintf_s(IN_pBuf,IN_BufSize,"%02x:%02x:%02x:%02x:%02x:%02x",pMac[5],pMac[4],pMac[3],pMac[2],pMac[1],pMac[0]);
}

void noip_session::init()
{
    max_thread_session = 10240;
}
noip_session::noip_session(int thread_num, int thread_id):packet_full_flow(thread_num, thread_id)
{
    init();
    task_id = atoll(getenv("THE_TASKID"));
    batch_id = atoll(getenv("THE_BATCHID"));
}
/*
需求：
    1. 所有无IP报文，
    2. 开启MAC防御，且未被MAC防御丢包，则统计并发送MAC通联日志

 */

void noip_session::full_flow_handle(c_packet * p_packet ,session_pub * p_session)
{
    if (false == (p_packet->flags.b_noip || p_packet->flags.b_mac_con_msg))
    {
        return;
    }
    else if(p_packet->has_mac())
    {
        mac_pair key;
        uint64_t dst_mac, src_mac;
        dst_mac = p_packet->get_dst_mac();
        src_mac = p_packet->get_src_mac();
        if (src_mac <= dst_mac)
        {
            key.mac_a = src_mac;
            key.mac_b = dst_mac;
        }
        else
        {
            key.mac_a = dst_mac;
            key.mac_b = src_mac;
        }

        if (p_packet->flags.b_mac_con_msg)
        {
            mac_con_info *val = NULL;
            if (mac_con_map.find(key, val))
            {
                if (p_packet->time_ts[0] < val->min_ts)
                {
                    val->min_ts = p_packet->time_ts[0];
                }
                else if (p_packet->time_ts[0] > val->max_ts)
                {
                    val->max_ts = p_packet->time_ts[0];
                }
                if (src_mac <= dst_mac)
                {
                    val->pkt_a2b ++;
                }
                else
                {
                    val->pkt_b2a ++;
                }
            }
            else
            {
                mac_con_info *to_add = new mac_con_info(key);
                to_add->min_ts = p_packet->time_ts[0];
                to_add->max_ts = p_packet->time_ts[0];
                if (src_mac <= dst_mac)
                {
                    to_add->pkt_a2b ++;
                }
                else
                {
                    to_add->pkt_b2a ++;
                }
                mac_con_map.push(key, to_add);
                if (mac_con_map.size() > max_thread_session)
                {
                    mac_con_info *to_del;
                    mac_con_map.pop(to_del);
                    if(should_log)
                    {
                        JKNmsg *p_msg = p_packet->p_value->get();
                        if (p_msg)
                        {
                            p_msg-> set_type(7);
                            mac_con_msg *msg = p_msg->mutable_mac_con();
                            char tmp[18] = {0};
                            macToStr((unsigned char *)&(to_del->key.mac_a), tmp , 18);
                            msg->set_mac_a(tmp);
                            macToStr((unsigned char *)&(to_del->key.mac_b), tmp , 18);
                            msg->set_mac_b(tmp);
                            msg->set_begin_time(to_del->min_ts);
                            msg->set_end_time(to_del->max_ts);
                            msg->set_pkt_a2b(to_del->pkt_a2b);
                            msg->set_pkt_b2a(to_del->pkt_b2a);
                            th_engine_tools *tools = (th_engine_tools *)p_th_tools;
                            msg->set_device_id(tools->device_id);
                            msg->set_thread_id(p_packet->thread_id);
                            msg->set_task_id((unsigned int)task_id);
                            msg->set_batch_id((unsigned int)batch_id);
                        }
                    }
                    delete to_del;
                }
            }
        }
        if(p_packet->flags.b_noip)
        {
            mac_con_info *val = NULL;
            if (noip_map.find(key, val))
            {
                if (p_packet->time_ts[0] < val->min_ts)
                {
                    val->min_ts = p_packet->time_ts[0];
                }
                else if (p_packet->time_ts[0] > val->max_ts)
                {
                    val->max_ts = p_packet->time_ts[0];
                }
                if (src_mac <= dst_mac)
                {
                    val->pkt_a2b ++;
                }
                else
                {
                    val->pkt_b2a ++;
                }
            }
            else
            {
                mac_con_info *to_add = new mac_con_info(key);
                to_add->min_ts = p_packet->time_ts[0];
                to_add->max_ts = p_packet->time_ts[0];
                if (src_mac <= dst_mac)
                {
                    to_add->pkt_a2b ++;
                }
                else
                {
                    to_add->pkt_b2a ++;
                }
                noip_map.push(key, to_add);
                if (noip_map.size() > max_thread_session)
                {
                    mac_con_info *to_del;
                    noip_map.pop(to_del);
                    if(should_log)
                    {
                        JKNmsg *p_msg = p_packet->p_value->get();
                        if (p_msg)
                        {
                            p_msg-> set_type(6);
                            mac_con_msg *msg = p_msg->mutable_noip_con();
                            char tmp[18] = {0};
                            macToStr((unsigned char *)&(to_del->key.mac_a), tmp , 18);
                            msg->set_mac_a(tmp);
                            macToStr((unsigned char *)&(to_del->key.mac_b), tmp , 18);
                            msg->set_mac_b(tmp);
                            msg->set_begin_time(to_del->min_ts);
                            msg->set_end_time(to_del->max_ts);
                            msg->set_pkt_a2b(to_del->pkt_a2b);
                            msg->set_pkt_b2a(to_del->pkt_b2a);
                            th_engine_tools *tools = (th_engine_tools *)p_th_tools;
                            msg->set_device_id(tools->device_id);
                            msg->set_thread_id(p_packet->thread_id);
                            msg->set_task_id((unsigned int)task_id);
                            msg->set_batch_id((unsigned int)batch_id);
                        }
                    }
                    delete to_del;
                }
            }
        }
    }
}
bool noip_session::time_out(session_pub *p_session)
{
    return true;
}
// 
void noip_session::resources_recovery(session_pub * p_session)
{

    return;
}

void noip_session::session_recovery(session_pub * p_session)
{
    return;
}

void noip_session::module_timeout(uint32_t ts, CMessage *value, uint32_t thread_id)
{
    mac_con_info *to_del;
    while (mac_con_map.first(to_del))
    {
        if (30 < ts - (uint32_t)to_del->start_ts)
        {
            if(should_log)
            {
                JKNmsg *p_msg = value->get();
                if (p_msg)
                {
                    p_msg-> set_type(7);
                    mac_con_msg *msg = p_msg->mutable_mac_con();
                    char tmp[18] = {0};
                    macToStr((unsigned char *)&(to_del->key.mac_a), tmp , 18);
                    msg->set_mac_a(tmp);
                    macToStr((unsigned char *)&(to_del->key.mac_b), tmp , 18);
                    msg->set_mac_b(tmp);
                    msg->set_begin_time((uint32_t)to_del->start_ts);
                    msg->set_end_time(ts);
                    msg->set_pkt_a2b(to_del->pkt_a2b);
                    msg->set_pkt_b2a(to_del->pkt_b2a);
                    th_engine_tools *tools = (th_engine_tools *)p_th_tools;
                    msg->set_device_id(tools->device_id);
                    msg->set_thread_id(thread_id);
                    msg->set_task_id((unsigned int)task_id);
                    msg->set_batch_id((unsigned int)batch_id);
                }
            }
            mac_con_map.erase(to_del->key);
            delete to_del;
        }
        else
        {
            break;
        }
    }
    while (noip_map.first(to_del))
    {
        if (30 < ts - (uint32_t)to_del->start_ts)
        {
            if(should_log)
            {
                JKNmsg *p_msg = value->get();
                if (p_msg)
                {
                    p_msg-> set_type(6);
                    mac_con_msg *msg = p_msg->mutable_noip_con();
                    char tmp[18] = {0};
                    macToStr((unsigned char *)&(to_del->key.mac_a), tmp , 18);
                    msg->set_mac_a(tmp);
                    macToStr((unsigned char *)&(to_del->key.mac_b), tmp , 18);
                    msg->set_mac_b(tmp);
                    msg->set_begin_time(to_del->min_ts);
                    msg->set_end_time(to_del->max_ts);
                    msg->set_pkt_a2b(to_del->pkt_a2b);
                    msg->set_pkt_b2a(to_del->pkt_b2a);
                    th_engine_tools *tools = (th_engine_tools *)p_th_tools;
                    msg->set_device_id(tools->device_id);
                    msg->set_thread_id(thread_id);
                    msg->set_task_id((unsigned int)task_id);
                    msg->set_batch_id((unsigned int)batch_id);
                }
            }
            noip_map.erase(to_del->key);
            delete to_del;
        }
        else
        {
            break;
        }
    }
}


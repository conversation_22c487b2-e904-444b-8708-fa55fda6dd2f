#ifndef __TH_ENGINE_PACKET_H__
#define __TH_ENGINE_PACKET_H__

#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include "packet.h"

typedef struct PacketInfo_
{
    char buff[1024];
    int signlen ;    // 计算 crc64 的buf 长度
    uint32_t len ;  //数据包长
    uint32_t ts;   // s
    uint32_t nts;  // ns
    uint32_t tid ; // thread num
    uint32_t index ; // 计算的 index 
    uint64_t sy_crc64;
    unsigned char *p_data;
    c_packet packet;
    void *p_session;
}PacketInfo;


#endif
// Last Update:2019-02-21 14:38:24
/**
 * @file hash_doulelist.h
 * @brief : 哈希链表来维护session 信息
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-08-26
 */

#ifndef HASH_DOULELIST_H
#define HASH_DOULELIST_H

#include "double_link.h"
#include "hash.h"

struct hash_link 
{
    HASH_TABLE * p_head ;
};
void hd_init(struct hash_link * p_hl_head);
void * hd_find(struct hash_link * p_hl_head , uint64_t index ,void * data , cmp_ptr ptr );
void hd_insert (struct hash_link * p_hl_head , uint64_t index ,void * data  );
void  hd_delete(struct hash_link * p_hl_head , uint64_t index , void * data  , cmp_ptr ptr);
#endif  /*HASH_DOULELIST_H*/

// Last Update:2019-05-13 11:43:51
/**
 * @file proto_parse_handle.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-09-28
 */
#include "proto_parse_handle.h"
#define PARSE_ID_NAME "get_plugin_id"
#define PARSE_HANDLE_NAME "attach"
#include "TH_engine_define.h"
#include "read_conf_commit.h"
#include <fstream>
#include "json/value.h"
#include "json/reader.h"
#define PROTOCOL_IP 13
#define PROTOCOL_IPV6 17 
#define  PROTOCOL_TCP 14 
#define  PROTOCOL_UDP 15
// 解析配置文件
proto_parse_handle::proto_parse_handle():pp_session_marge(false)
{
    memset(session_parse_arr,0x0,MAXPROTOPARSENUM*sizeof(session_pasre_base *));
}
proto_parse_handle::~proto_parse_handle()
{
    if(p_plugin_marge != NULL) 
    {
        delete p_plugin_marge ;
    }
    p_plugin_marge = NULL ;
}
void proto_parse_handle::pp_session_init(session_pub *  p_session,proto_parse_session * p_pp_session) // pp_session 初始化
{
    p_pp_session->init(p_session);
}
void proto_parse_handle::init(std::string filestr)
{
    std::string so_path =  string(getenv("THE_ROOT")) + "/bin/plugin/proto_parse/";
    config.load_conf(string(getenv("THE_CONF_PATH")) + "/plugin_conf.json");
    // 解析数据  //
    p_plugin_marge = new share_plugin_marge(so_path);
    //pp_shareobject_marge::get_instance() ->init(so_path);
    // p_instance->init(so_path);
    handle_map.clear();
    //std::map<int ,void *>  handle_map ;
    //pp_shareobject_marge::get_instance()->p_plugin_marge -> get_plugin_id_map(PARSE_ID_NAME , PARSE_HANDLE_NAME , &handle_map);
    p_plugin_marge -> get_plugin_id_map(PARSE_ID_NAME , PARSE_HANDLE_NAME , &handle_map);

    //散列到hash
    std::map<int ,void *>::iterator iter =   handle_map.begin();
    for(;iter != handle_map. end();iter ++)
    {
        session_pasre_base * p_base = (session_pasre_base * ) iter -> second ;
        p_base -> set_tools(th_engine_tools::instance());
        auto itr = config.conf_map.find(iter->first);
        if(itr != config.conf_map.end())
        {
            p_base -> set_should_log(itr->second.should_log);
        }
        else
        {
            p_base -> set_should_log(config.should_log_def);
        }
        session_parse_arr[iter -> first]=(session_pasre_base * )iter ->second; 
    }
    pp_session_marge.init(read_conf_commit::GetInstance() ->read_conf_session_num("session","thread_session_num"));
}
session_pasre_base* proto_parse_handle::get_handle(session_pub * p_session,c_packet * p_packet)
{
    //
    if(p_session -> session_basic.ProtoSign<= 0)
    {
        return NULL;
    }
    if(session_parse_arr[p_session -> session_basic.ProtoSign])
    {
        return session_parse_arr[p_session -> session_basic.ProtoSign];
    }
    else
    {
        STR_PROTOCOLINFOR * pStack = p_packet -> m_str_packet_moudle.Stack.pProtocol ;
        for(int  i = p_packet -> m_str_packet_moudle.Stack.ProtocolNum  - 1 ; i > -1 ; i--)
        {
            switch( pStack[i].Protocol )
            {
                case PROTOCOL_IP:
                case PROTOCOL_IPV6:
                case PROTOCOL_UDP:
                case PROTOCOL_TCP:
                    break;
                default:
                {
                    if(session_parse_arr[pStack[i].Protocol])
                    {
                        return session_parse_arr[pStack[i].Protocol];
                    }
                }
            }
        }
        return NULL;
    }
}
void proto_parse_handle::handle(session_pub * p_session,c_packet * p_packet) 
{
    proto_parse_session *p_pp_session = (proto_parse_session * )( p_session -> p_sp_session) ;
    if(p_pp_session == NULL)
    {
        p_pp_session  = pp_session_marge.pop();
        pp_session_init(p_session , p_pp_session );
        p_session -> p_sp_session = (void *)p_pp_session ;
    }
    if(p_pp_session -> p_handle == NULL)
    {
        p_pp_session ->  p_handle = get_handle(p_session , p_packet);
        if(p_pp_session -> p_handle)
        {
            p_pp_session ->p_handle -> potocol_init(p_session,p_packet);
        }
        else
        {
            return;
        }
    }
    // 组包 
    if(p_pp_session -> p_handle ->potocol_sign_judge( p_session,p_packet ))
    {
        // 解析数据   数据格式存储在输出 value json 中
        if(p_pp_session -> p_handle -> potocol_parse_handle(p_session ,p_packet))
        {
            p_pp_session -> p_handle -> potocol_data_handle( p_session  , p_packet );
        }
    }
}
// time out 
bool proto_parse_handle::time_out(session_pub * p_session,uint32_t tm)
{
    proto_parse_session *p_pp_session = (proto_parse_session * )( p_session -> p_sp_session) ;
    if (p_pp_session == NULL || p_pp_session -> p_handle == NULL) 
    {
        return true;
    }
    else
    {
        return   p_pp_session -> p_handle->time_out(p_session,tm);
    }
}
void  proto_parse_handle::recovery_session(session_pub * p_session)
{

    proto_parse_session *p_pp_session = (proto_parse_session * )( p_session -> p_sp_session) ;
    if (p_pp_session == NULL ) 
    {
        return ;
    }
    else
    {
        // 解析数据   数据格式存储在输出 value json 中
        if (p_pp_session -> p_handle != NULL) 
        {
            // if((p_pp_session -> tcp_recom[0] && p_pp_session -> tcp_recom[0]->get_data_len() > 0) || (p_pp_session -> tcp_recom[1] && p_pp_session -> tcp_recom[1]->get_data_len() > 0))
            // {
            //     (p_pp_session -> p_handle -> potocol_parse_handle(p_session ,NULL));
            //     {
            //         p_pp_session -> p_handle -> potocol_data_handle( p_session  , NULL );
            //     }
            // }
            p_pp_session ->p_handle ->  resources_recovery(p_session);
        }
        //    p_pp_session->init();
        //    pp_session_marge.push(p_pp_session);
    }
}

void  proto_parse_handle::session_recovery(session_pub * p_session)
{
    proto_parse_session *p_pp_session = (proto_parse_session * )( p_session -> p_sp_session) ;
    if (p_pp_session == NULL ) 
    {
        return ;
    }
    else 
    {
        if(p_pp_session -> p_handle != NULL)
        {
            p_pp_session -> p_handle ->  session_recovery(p_session);
        }
        p_pp_session->init();
        pp_session_marge.push(p_pp_session);
    }
}


proto_parse_conf::proto_parse_conf()
{
    should_log_def = 1;
    conf_map.clear();
}
int proto_parse_conf::load_conf(string conf_path)
{
    ifstream ifs(conf_path);
    if( ifs.is_open() )
    {
        Json::Reader reader;
        Json::Value value;

        if(reader.parse(ifs, value))
        {
            if(false == value["proto_parse"].isNull())
            {
                value = value["proto_parse"];
                if(false == value["should_log_def"].isNull())
                {
                    should_log_def = value["should_log_def"].asInt();
                }
                if(false == value["plugin"].isNull())
                {
                    for(int i = 0; i < value["plugin"].size(); i ++)
                    {
                        int id = value["plugin"][i]["id"].asInt();
                        plugin_conf val;
                        val.name = value["plugin"][i]["name"].asString();
                        val.should_log = value["plugin"][i]["should_log"].asInt();
                        conf_map[id] = val;
                    }
                }
            }
            return 0;
        }
    }
    return 1;
}
// Last Update:2019-05-13 11:43:52
/**
 * @file proto_parse_hanle.h
 * @brief : 协议解析整理流程
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-09-28
 */
#ifndef PROTO_PARSE_HANLE_H
#define PROTO_PARSE_HANLE_H
#include <proto_parse_session.h>
#define MAXPROTOPARSENUM 100000
#include <pthread.h>
#include  "share_plugin_marge.h"
#include  "resources_marge.h"
static pthread_mutex_t mutex = PTHREAD_MUTEX_INITIALIZER; 
//static share_plugin_marge * p_pp_plugin_marge  = NULL; 
class pp_shareobject_marge
{
    public:
        pp_shareobject_marge()
        {
            //init();
        }

    private:
        share_plugin_marge * p_pp_plugin_marge;
   //     static pp_shareobject_marge * instance ;
    public:
        share_plugin_marge * p_plugin_marge  ; 
        ~pp_shareobject_marge()
        {
        /*    if(instance != NULL)
            {
                delete pp_shareobject_marge::instance ;
            }*/ 
            if (p_pp_plugin_marge  != NULL)
            {
                /* pthread_mutex_lock(&mutex);
                if (p_pp_plugin_marge  != NULL)
                {
                    delete p_pp_plugin_marge ; 
                    p_pp_plugin_marge = NULL;
                }
                pthread_mutex_unlock(&mutex); */
                delete p_pp_plugin_marge ;
                p_pp_plugin_marge = NULL;
            }
        }
        static pp_shareobject_marge * get_instance()
        {
           /* if (instance == NULL)
            {
                pthread_mutex_lock(&mutex);
                if (instance == NULL)
                {
                    instance = new pp_shareobject_marge();
                }
                pthread_mutex_unlock(&mutex);
            }*/
            static  pp_shareobject_marge instance ;

            return &instance;
        }
        // 
        void init(std::string so_path) 
        {
            if (p_pp_plugin_marge== NULL)
            {
                /* pthread_mutex_lock(&mutex);
                if (p_pp_plugin_marge  == NULL)
                {
                    p_pp_plugin_marge = new share_plugin_marge(so_path);
                }
                pthread_mutex_unlock(&mutex); */
                p_pp_plugin_marge = new share_plugin_marge(so_path);
            }
            p_plugin_marge = p_pp_plugin_marge ;
        }
};
class proto_parse_conf
{
public:
    proto_parse_conf();
    class plugin_conf
    {
    public:
        string name;
        int should_log;
    };
    int load_conf(string conf_path);
    std::map<int, plugin_conf> conf_map;
    int should_log_def;
};
class proto_parse_handle
{
    public:
        void init(std::string filestr); // 配置文件目录
        proto_parse_handle();
        ~proto_parse_handle();
        void handle(session_pub * p_session , c_packet * p_packet);
        bool time_out(session_pub * p_session,uint32_t tm);
        void recovery_session(session_pub * p_session);
        void session_recovery(session_pub * p_session);
        session_pasre_base * get_handle(session_pub * p_session,c_packet * p_packet);
    private:
        std::map<int ,void *>  handle_map ;
        //   bool organize_packet_to_session(session_pub * p_session , c_packet * p_packet);
        session_pasre_base * session_parse_arr[MAXPROTOPARSENUM];
        session_marge<proto_parse_session>  pp_session_marge ;
        void pp_session_init(session_pub *  p_session,proto_parse_session * p_pp_session); // pp_session 初始化
        share_plugin_marge *p_plugin_marge ;
        proto_parse_conf config;

};


#endif  /*PROTO_PARSE_HANLE_H*/

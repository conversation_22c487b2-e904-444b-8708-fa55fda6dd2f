// Last Update:2019-04-08 16:36:37
/**
 * @file time_out_marge.cpp
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-08-28
 */

#include "time_out_marge.h"


time_out_marge  *  time_out_marge_create(  int max_num)
{
    time_out_marge  * tm_marge = (time_out_marge  * )malloc(sizeof(time_out_marge ));
    memset(tm_marge,0x0,sizeof(time_out_marge ));
    tm_marge ->use_num = 0 ; 
    tm_marge -> tmo_num  = -1;
    tm_marge -> max_num = max_num ;
    
    tm_marge -> time_out_list =  (struct _list **)malloc(sizeof(struct _list*)*max_num);
    int  i = 0;
    for(;i < max_num ; i++) 
    {
        tm_marge -> time_out_list[i] = (struct _list *)malloc(sizeof(struct _list));
        tm_marge->time_out_list[i]->firstNode = NULL ;
        tm_marge->time_out_list[i]-> lastNode = NULL ;
        tm_marge->time_out_list[i]->len  = 0 ;
    }

    return tm_marge ;
}

void destroy_time_out_marge(time_out_marge * tm_marge)
{
    int  i = 0;
    for(;i < tm_marge -> max_num ; i++) 
    {
        free(tm_marge -> time_out_list[i]);
    }
    free( tm_marge -> time_out_list);
    free(tm_marge);
}

struct _list * tmo_begin(time_out_marge * tm_marge) 
{
    // 执行time_out // 遍历所有保重数据
    // printf(" tm_marge ->use_num  === %d\n", tm_marge ->use_num );
    tm_marge ->use_num ++ ;
    if( tm_marge -> tmo_num == -1 )
    {
        if(tm_marge ->use_num == tm_marge -> max_num)
        {
            tm_marge ->tmo_num = 0;
        }
        else
        {
            return NULL ;
        }
    }
    if(tm_marge ->use_num == tm_marge -> max_num) 
    {
        tm_marge ->use_num  = 0;
    }
    int i = tm_marge ->tmo_num ;
    struct _list * p  = tm_marge->time_out_list[i] ;
    tm_marge -> time_out_list[i] = (struct _list *)malloc(sizeof(struct _list));
    tm_marge->time_out_list[i]->firstNode = NULL ;
    tm_marge->time_out_list[i]-> lastNode = NULL ;
    tm_marge->time_out_list[i]->len  = 0 ;
    //tm_marge ->tmo_num ++ ;
    //printf("use_num = %d   , tmo_num = %d max_num = %d \n",tm_marge -> use_num , tm_marge ->  tmo_num , tm_marge -> max_num);
    // for(int i=0; i < tm_marge -> max_num; i ++)
    // {
    //     int idx=(tm_marge ->tmo_num+i)%(tm_marge -> max_num);
    //     printf("tm_marge:%p--list:%d--used:%d\n", tm_marge, i, tm_marge->time_out_list[idx]->len );
    // }
    return p;
}
void  tmo_end(time_out_marge * tm_marge)
{
    tm_marge -> tmo_num ++ ;
    if(tm_marge ->tmo_num == tm_marge -> max_num)
    {
        tm_marge ->tmo_num  = 0;
    }
}
void tmo_session_update(session * p_session , time_out_marge * tm_marge )
{

    //为当前的桶  
    if(p_session -> use_num == -1)
    {
        p_session -> use_num = 0;
    }
    else 
    {
        if(tm_marge -> use_num == p_session -> use_num ) 
        //if(p_session -> use_num == p_session -> use_num ) 
        {
            return ;
        }
        loutlistnode((tm_marge->time_out_list[p_session -> use_num]) , &(p_session -> tmo_node));
    }
        // 解除桶 
    p_session -> use_num  = tm_marge ->  use_num ;
    tm_marge->time_out_list[p_session -> use_num]->len ++  ;
    lpushnode((tm_marge->time_out_list[p_session -> use_num]), &(p_session -> tmo_node));
}
void tmo_session_delete(session * p_session , time_out_marge * tm_marge)
{
    loutlistnode((tm_marge->time_out_list[p_session -> use_num]) , &(p_session -> tmo_node));
    tm_marge->time_out_list[p_session -> use_num] ->len --  ;
}
// 交换 list  ， 
struct _list * get_back_tmo(time_out_marge * tm_marge)
{
    // 定义的ID 
}



#include <algorithm>
#include <iostream>
#include <cstring>

#include "tcp_fragment_list.h"

using namespace std;

static int be_seq_nearby(uint64_t seq_1, uint64_t seq_2)
{
    seq_1 >>= 16;
    seq_2 >>= 16;
    seq_1 &= 0xffff;
    seq_2 &= 0xffff;
    
    if (seq_1 == seq_2)         //高16位相同
    {
        return 1;
    }
    else if (seq_1 < seq_2)
    {
        if ((seq_1+1) == seq_2)  //高16位+1
        {
            return 1;
        }
        else if (0 == seq_1 && 0xffff == seq_2)  //高16位-1,越界
        {
            return 2;
        }
    }
    else
    {
        if ((seq_2+1) == seq_1)  //高16位-1
        {
            return 1;
        }
        else if (0xffff == seq_1 && 0 == seq_2) //高16位+1,越界
        {
            return 3;
        }
    }
    return 0;
}

int TcpFragmentList::clear()
{
    return fl.clear();
}

int TcpFragmentList::print()
{
    for (auto iter = fl.frag_list.begin(); iter != fl.frag_list.end(); iter ++)
    {
        if (fl.frag_list.begin() == iter)
        {
            cout << ((*iter).seq_begin & (0xffffffffULL)) << "-" << ((*iter).seq_end & (0xffffffffULL));
        }
        else
        {
            cout << "," << ((*iter).seq_begin & (0xffffffffULL)) << "-" << ((*iter).seq_end & (0xffffffffULL));
        }
    }
    cout << endl;
    return 0;
}

int TcpFragmentList::add_fragment(std::uint32_t seq_raw, std::uint32_t len)
{
    uint64_t seq_begin = (uint64_t)seq_raw;
    uint64_t seq_max = 0;
    
    if (fl.frag_list.empty())
    {
        seq_begin += 0x100000000ULL;
        return fl.add_fragment(seq_begin, seq_begin+len);
    }
    else if (fl.frag_list.size() < fl.max_fragment)
    {
        seq_max = fl.frag_list.back().seq_end;
        switch(be_seq_nearby(seq_max, seq_begin))
        {
            case 1:
            {
                seq_begin |= (seq_max & 0xffffffff00000000ULL);
                return fl.add_fragment(seq_begin, seq_begin+len);
            }
            case 2:
            {
                seq_begin |= (seq_max & 0xffffffff00000000ULL);
                if(seq_begin >= 0x100000000ULL)
                {
                    seq_begin -= 0x100000000ULL;
                    return fl.add_fragment(seq_begin, seq_begin+len);
                }
                else
                {
                    return -4;
                }
            }
            case 3:
            {
                seq_begin |= (seq_max & 0xffffffff00000000ULL);
                seq_begin += 0x100000000ULL;
                return fl.add_fragment(seq_begin, seq_begin+len);
            }
            default:
            {
                fl.sum.mis_order ++;
                return -3;
            }
        }
    }
    else
    {
        return -2;
    }
}
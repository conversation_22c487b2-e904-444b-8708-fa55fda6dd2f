// Last Update:2019-07-01 14:05:28
/// @file share_plugin_marge.cpp
/// @brief 
/// <AUTHOR>
/// @version 0.1.00
/// @date 2016-11-16
#include "share_plugin_marge.h"
#include <dirent.h>
#include <stdio.h>
#include <stdlib.h>
std::map <std::string ,void *> so_name_map ;
//std::map<int,attach_>p_id_attach_map;
share_plugin_marge::share_plugin_marge(std::string plugin_path )
{
    path = plugin_path ;
    p_m_handle_map = NULL;
    p_string_handle_map = NULL;
    p_m_handle_list = NULL;
}
share_plugin_marge::share_plugin_marge()
{
    path = "";
    p_m_handle_map = NULL;
    p_string_handle_map = NULL;
    p_m_handle_list = NULL;
}
share_plugin_marge::~share_plugin_marge()
{
    /* commented by <PERSON><PERSON><PERSON> 20180410
     * so_name_map是全局变量，不是share_plugin_marge成员，不要析构
     std::map<string,void *> ::iterator iter = so_name_map.begin();
     for(;iter != so_name_map.end(); iter ++ )
     {

     void *handle = iter->second;
     dlclose(handle);
     }
     */
}
void share_plugin_marge::init(std::string plugin_path) 
{
    path = plugin_path ;
}
void share_plugin_marge::init(std::string plugin_path,std::string id_funcname, std::string attach_funcname )
{

    path = plugin_path ;
    m_id_funcname  = id_funcname ;
    m_attach_funcname = attach_funcname;
    plugin_inspect_attach(path) ;
}
// 获取单个对象
void * share_plugin_marge::get_plugin_id_object(int id) 
{

    std::map<int,attach_> ::iterator it = p_id_attach_map.find(id) ; 
    if(it != p_id_attach_map.end())
    {
        attach_ pfunc = it -> second ;
        return (void *)pfunc();
    }
    else 
    {
        printf(" 配置错误 : %s 下的插件中没有id 为 %d \n", path.c_str(), id);
        abort();
    }
}
// 获取string 型ID 和 对象列表
void share_plugin_marge::get_plugin_string_map(string id_funcname , string attach_funcname,std::map<string ,void *> * p_handle_map)
{
    p_string_handle_map = p_handle_map ;
    m_id_funcname  = id_funcname ;
    m_attach_funcname = attach_funcname;
    plugin_inspect(path);
}
// 获取 int 型 ID 和 对象的列表
void share_plugin_marge::get_plugin_id_map(string id_funcname , string attach_funcname,std::map<int ,void *> * p_handle_map)
{
    p_m_handle_map = p_handle_map ;
    m_id_funcname  = id_funcname ;
    m_attach_funcname = attach_funcname;
    plugin_inspect(path);
}
// 获取函数列表
void share_plugin_marge::get_plugin_list(string attach_funcname,std::list<void *> * p_handle_list)
{
    m_attach_funcname = attach_funcname;
    p_m_handle_list = p_handle_list ;
    plugin_inspect(path);
}
// 读取目录下的so文件，并产生工厂函数集合 
void share_plugin_marge::plugin_inspect_attach(string str_dir )
{
    // --- 检测 目录下面是否所有的so 文件  
    char infile[512];
    struct dirent *ptr;   
    DIR *dir;
    //dir=opendir(config_text::sz_plugin_path.c_str());
    dir=opendir(str_dir.c_str());
    if(NULL == dir)
    {
        printf("open %s failed!\n", str_dir.c_str());
        return ;
    }
    while((ptr=readdir(dir))!=NULL)
    {
        //跳过'.'和'..'两个目录
        if(ptr->d_name[0] == '.')
            continue;
        //printf("%s is ready...1234\n",ptr->d_name);
        //   检测文件是否存在

        if(cmp_wiat_end(ptr->d_name,".so"))
        {
            //if(so_name_map.find(string(ptr->d_name)) == so_name_map.end())
            if(so_name_map.find(str_dir + string(ptr->d_name)) == so_name_map.end())
            {
                sprintf(infile,"%s%s",str_dir.c_str(),ptr->d_name);
                //文件list 保存 
                //判断调用 是list 还是 map
                // OpenSo(infile,string(ptr->d_name),plugin_type);

                printf("<%s>\n",infile); 
                { 
                    // 调获取ID 的接口
                    attach_  p_attach = NULL;
                    int id = plugin_id_open(str_dir + std::string(ptr->d_name) , m_id_funcname);
                    // 调获取对象的接口
                    p_attach = plugin_open( str_dir +std::string(ptr->d_name) , m_attach_funcname);
                    p_id_attach_map.insert(std::pair<int,attach_>(id,p_attach));

                }
                //            so_id_handle_map.insert(pair<int,ptr_base_handle >(id,attach_));
            }
            //added by Yangjinhao 20180410 
            //感觉此处可以将so_name_map的查找去掉，因为在plugin_id_open, plugin_open中已经再次判断了
            //so_name_map中的so是否存在了。
            else
            {
                attach_  p_attach = NULL;
                int id = plugin_id_open(str_dir + std::string(ptr->d_name) , m_id_funcname);

                // 调获取对象的接口
                p_attach = plugin_open( str_dir +std::string(ptr->d_name) , m_attach_funcname);
                p_id_attach_map.insert(std::pair<int,attach_>(id,p_attach));
            }
        }
    }
    closedir(dir);
    p_m_handle_map = NULL;
    p_m_handle_list = NULL ;
    m_id_funcname = "";
    m_attach_funcname = "";
}
// 读取目录下的so文件，并产生对象 
void share_plugin_marge::plugin_inspect(string str_dir )
{
    // --- 检测 目录下面是否所有的so 文件  
    char infile[512];
    struct dirent *ptr;   
    DIR *dir;
    //dir=opendir(config_text::sz_plugin_path.c_str());
    dir=opendir(str_dir.c_str());
    if(NULL == dir)
    {
        printf("open %s failed!\n", str_dir.c_str());
        return ;
    }
    while((ptr=readdir(dir))!=NULL)
    {
        //跳过'.'和'..'两个目录
        if(ptr->d_name[0] == '.')
            continue;
        printf("%s is ready...\n",ptr->d_name);
        //   检测文件是否存在

        if(cmp_wiat_end(ptr->d_name,".so"))
        {
            //if(so_name_map.find(string(ptr->d_name)) == so_name_map.end())
            if(so_name_map.find( str_dir + ptr->d_name) == so_name_map.end())
            {
                sprintf(infile,"%s%s",str_dir.c_str(),ptr->d_name);
                //文件list 保存 
                //判断调用 是list 还是 map
                // OpenSo(infile,string(ptr->d_name),plugin_type);

                printf("<%s>\n",infile); 
                if(p_m_handle_map  != NULL)
                { 
                    // 调获取ID 的接口
                    attach_  p_attach = NULL;
                    int id = plugin_id_open(str_dir + std::string(ptr->d_name) , m_id_funcname);
                    //printf("======调用plugin_id_open  [%s]\n", infile);
                    std::map<int,attach_> ::iterator it = p_id_attach_map.find(id) ; 
                    if(it !=  p_id_attach_map.end())
                    {
                        p_attach = it ->second ;
                    }
                    else 
                    {
                        // 调获取对象的接口
                        p_attach = plugin_open(str_dir +std::string(ptr->d_name) , m_attach_funcname);
                        p_id_attach_map.insert(std::pair<int,attach_>(id,p_attach));
                    }
                    void * p_object = p_attach();
                    if(p_object != NULL) 
                    {
                        p_m_handle_map ->insert(std::pair<int,void*>(id,p_object));
                    }


                }
                else  if(p_m_handle_list != NULL )
                {
                    // 调获取对象的接口
                    attach_  p_attach = plugin_open(str_dir +std::string(ptr->d_name) , m_attach_funcname);
                    void * p_object = (void *)p_attach();
                    if(p_object != NULL)
                    {
                        p_m_handle_list->push_back(p_object);
                    }
                }
                else if(p_string_handle_map != NULL) 
                {
                    // 调获取ID 的接口
                    string id = plugin_string_open(str_dir +std::string(ptr->d_name) , m_id_funcname);
                    // 调获取对象的接口
                    attach_  p_attach = plugin_open(str_dir +std::string(ptr->d_name) , m_attach_funcname);
                    void * p_object = p_attach();
                    if(p_object != NULL) 
                    {
                        p_string_handle_map ->insert(std::pair<std::string,void*>(id,p_object));
                    }
                }
                //            so_id_handle_map.insert(pair<int,ptr_base_handle >(id,attach_));
            }
            else 
            {
                std::map <std::string ,void *>::iterator iter =   so_name_map.find( str_dir + ptr->d_name);
                if(p_m_handle_list != NULL )
                {
                    // 调获取对象的接口
                    attach_  p_attach = plugin_open(str_dir +std::string(ptr->d_name) , m_attach_funcname);
                    void * p_object = (void *)p_attach;
                    if(p_object != NULL)
                    {
                        p_m_handle_list->push_back(p_object);
                    }
                }
                else if(p_string_handle_map != NULL) 
                {
                    // 调获取ID 的接口
                    string id = plugin_string_open(str_dir +std::string(ptr->d_name) , m_id_funcname);
                    // 调获取对象的接口
                    attach_  p_attach = plugin_open(str_dir +std::string(ptr->d_name) , m_attach_funcname);
                    void * p_object = p_attach();
                    if(p_object != NULL) 
                    {
                        p_string_handle_map ->insert(std::pair<std::string,void*>(id,p_object));
                    }
                }
                else if(p_m_handle_map  != NULL)
                {

                    // 调获取ID 的接口
                    attach_  p_attach = NULL;
                    int id = plugin_id_open(str_dir + std::string(ptr->d_name) , m_id_funcname);
                    //printf("======调用plugin_id_open  [%s]\n", infile);
                    std::map<int,attach_> ::iterator it = p_id_attach_map.find(id) ; 
                    if(it !=  p_id_attach_map.end())
                    {
                        p_attach = it ->second ;
                    }
                    else 
                    {
                        // 调获取对象的接口
                        p_attach = plugin_open(str_dir +std::string(ptr->d_name) , m_attach_funcname);
                        p_id_attach_map.insert(std::pair<int,attach_>(id,p_attach));
                    }
                    void * p_object = p_attach();
                    if(p_object != NULL) 
                    {
                        p_m_handle_map ->insert(std::pair<int,void*>(id,p_object));
                    }

                }
                //            so_id_handle_map.insert(pair<int,ptr_base_handle >(id,attach_));

            }
        }
    }
    closedir(dir);
    //p_m_handle_map = NULL;
    //p_string_handle_map = NULL;
    //p_m_handle_list = NULL ;
    m_id_funcname = "";
    m_attach_funcname = "";
}

// 挂载接口
attach_  share_plugin_marge::plugin_open(string plugin_name,string func_name )
{ 
    void * handle = NULL ;
    std::map<string,void *> ::iterator iter = so_name_map.find(plugin_name);
    if(iter == so_name_map.end())
    {
        //printf("======调用plugin_open  [%s]\n", plugin_name.c_str());
        //  
        handle = dlopen(plugin_name.c_str(),RTLD_NOW);
        if(handle==NULL)
        {
            printf("open %s error \n%s\n", plugin_name.c_str() , dlerror());
            abort();
            // exit(1);
        }
        so_name_map.insert(pair<string,void *>(string(plugin_name),handle));
    }
    else 
    {
        // 
        handle = iter -> second ; 
    }
    // 检测函数是否已经挂
    string func_key = plugin_name ;
    func_key += ":";
    func_key += func_name ;
    attach_ p_function = NULL;
    std::map<string,attach_>::iterator it = attach_map.find(func_key);
    // 打开函数挂载  
    if(it == attach_map.end())
    {
        p_function =  (attach_) dlsym(handle,func_name.c_str());
        attach_map.insert(std::pair<string,attach_>(func_key , p_function));
    }
    else 
    {
        p_function = it -> second ;
    }
    return p_function;
}

//实例ID接口
int share_plugin_marge::plugin_id_open(std::string plugin_name,std::string func_name )
{
    void * handle = NULL ;
    std::map<std::string,void *> ::iterator iter = so_name_map.find(plugin_name);
    if(iter == so_name_map.end())
    {
        //  
        handle = dlopen(plugin_name.c_str(),RTLD_NOW);
        //printf("======not found, then insert so_name_map [%s]\n", plugin_name.c_str());
        if(handle==NULL)
        {
            printf("open %s error \n%s\n", plugin_name.c_str()  , dlerror());
            abort();
            // exit(1);
        }
        so_name_map.insert(pair<std::string,void *>(string(plugin_name),handle));
        //printf("%s is ready...1234\n", plugin_name.c_str());
    }
    else 
    {
        // 
        //printf("======already load [%s]\n", plugin_name.c_str());
        handle = iter -> second ; 
    }
    // 检测函数是否已经挂
    std::string func_key = plugin_name ;
    func_key += ":";
    func_key += func_name ;
    pluginid p_function = NULL;
    std::map<std::string,pluginid>::iterator it = pluginid_map.find(func_key);
    // 打开函数挂载  
    if(it == pluginid_map.end())
    {
        p_function =  (pluginid) dlsym(handle,func_name.c_str());
        pluginid_map.insert(std::pair<std::string,pluginid>(func_key , p_function));
    }
    else 
    {
        p_function = it -> second ;
    }
    return (*p_function)();
}
//实例NAME接口
string share_plugin_marge::plugin_string_open(string plugin_name,string func_name )
{
    void * handle = NULL ;
    std::map<string,void *> ::iterator iter = so_name_map.find(plugin_name);
    if(iter == so_name_map.end())
    {
        //  
        handle = dlopen(plugin_name.c_str(),RTLD_NOW);
        if(handle==NULL)
        {
            printf("open %s error \n%s\n", plugin_name.c_str() , dlerror());
            abort();
            // exit(1);
        }
        so_name_map.insert(pair<string,void *>(string(plugin_name),handle));
    }
    else 
    {
        // 
        handle = iter -> second ; 
    }
    // 检测函数是否已经挂
    string func_key = plugin_name ;
    func_key += ":";
    func_key += func_name ;
    plugin_object_name p_function = NULL;
    std::map<string,plugin_object_name >::iterator it = plugin_name_map.find(func_key);
    // 打开函数挂载  
    if(it == plugin_name_map.end())

    {
        p_function =  (plugin_object_name) dlsym(handle,func_name.c_str());
        plugin_name_map.insert(std::pair<string,plugin_object_name>(func_key , p_function));
    }
    else 
    {
        p_function = it -> second ;
    }
    return (*p_function)();
}




######################################
#
#
######################################
  
#target you can change test to what you want
#共享库文件名，lib*.so
TARGET  := ../lib/libengine_common.a
  
#compile and lib parameter
#编译参数
SRC     := ../
CC      := g++ -g
LIBS    :=-L$(THE_SDK)/lib
LDFLAGS :=
DEFINES := -Wall -g -fPIC -std=c++11
INCLUDE := -I. -I$(THE_SDK)/include -I$(THE_SDK)/../lib_src/common_tool/src/basic_parse/
CFLAGS  :=  $(DEFINES) $(INCLUDE)
CXXFLAGS:= $(CFLAGS) -DHAVE_CONFIG_H
SHARE   :=     
#SHARE   :=   -o

ALIB    :=

  
#i think you should do anything here
#下面的基本上不需要做任何改动了
  
#source file
#源文件，自动找所有.c和.cpp文件，并将目标定义为同名.o文件
SOURCE  := $(wildcard $(SRC)/*.c) $(wildcard $(SRC)/*.cpp $(SRC2)/*.cpp $(SRC3)/*.cpp)
OBJS    := $(patsubst %.c,%.o,$(patsubst %.cpp,%.o,$(SOURCE)))
  
.PHONY : everything objs clean veryclean rebuild
  
everything : $(TARGET)
  
all : $(TARGET)
  
objs : $(OBJS)
  
rebuild: veryclean everything
                
clean :
	rm -fr $(SRC)/*.o
	rm -fr $(SRC2)/*.o
	rm -fr $(TARGET)
  
$(TARGET) : $(OBJS)
#	$(CC)  $(CXXFLAGS) $(OBJS) $(ALIB) $(SHARE) $@ $(LDFLAGS) $(LIBS)
	ar cq $@ $^
install:
	\cp -f $(TARGET) $(THE_SDK)/lib/

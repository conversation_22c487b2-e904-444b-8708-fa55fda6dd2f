#include <algorithm>
#include <iostream>
#include <cstring>

#include "tcp_fragment_reassembly.h"

using namespace std;


static int be_seq_nearby(uint64_t seq_1, uint64_t seq_2)
{
    seq_1 >>= 16;
    seq_2 >>= 16;
    seq_1 &= 0xffff;
    seq_2 &= 0xffff;
    
    if (seq_1 == seq_2)         //高16位相同
    {
        return 1;
    }
    else if (seq_1 < seq_2)
    {
        if ((seq_1+1) == seq_2)  //高16位+1
        {
            return 1;
        }
        else if (0 == seq_1 && 0xffff == seq_2)  //高16位-1,越界
        {
            return 2;
        }
    }
    else
    {
        if ((seq_2+1) == seq_1)  //高16位-1
        {
            return 1;
        }
        else if (0xffff == seq_1 && 0 == seq_2) //高16位+1,越界
        {
            return 3;
        }
    }
    return 0;
}

int TcpFragmentReassembly::pop_front()
{
    return fr.pop_front();
}
int TcpFragmentReassembly::clear()
{
    return fr.clear();
}
std::uint32_t TcpFragmentReassembly::get_buffer_size()
{
    return fr.get_buffer_size();
}

int TcpFragmentReassembly::front_fragment(std::uint32_t &seq_raw, std::uint32_t &len, char *&p_data)
{
    uint64_t seq_begin, seq_end;
    int ret = fr.front_fragment(seq_begin, seq_end, p_data);
    if (0 == ret)
    {
        len = (uint32_t)(seq_end - seq_begin);
        seq_raw = (uint32_t)(seq_begin & 0xffffffffULL);
    }
    else
    {
        seq_raw = 0;
        len = 0;
    }
    return ret;
}

int TcpFragmentReassembly::add_fragment(std::uint32_t seq_raw, std::uint32_t len, const char *p_data)
{
    uint64_t seq_begin = (uint64_t)seq_raw;
    uint64_t seq_max = 0;
    
    if (fr.frag_list.empty())
    {
        seq_begin += 0x100000000ULL;
        return fr.add_fragment(seq_begin, seq_begin+len, p_data);
    }
    else
    {
        seq_max = fr.frag_list.back().seq_end;
        switch(be_seq_nearby(seq_max, seq_begin))
        {
            case 1:
            {
                seq_begin |= (seq_max & 0xffffffff00000000ULL);
                return fr.add_fragment(seq_begin, seq_begin+len, p_data);
            }
            case 2:
            {
                seq_begin |= (seq_max & 0xffffffff00000000ULL);
                if(seq_begin >= 0x100000000ULL)
                {
                    seq_begin -= 0x100000000ULL;
                    return fr.add_fragment(seq_begin, seq_begin+len, p_data);
                }
                else
                {
                    return -4;
                }
            }
            case 3:
            {
                seq_begin |= (seq_max & 0xffffffff00000000ULL);
                seq_begin += 0x100000000ULL;
                return fr.add_fragment(seq_begin, seq_begin+len, p_data);
            }
            default:
            {
                return -3;
            }
        }
    }
}
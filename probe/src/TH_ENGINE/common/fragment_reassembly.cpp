#include <algorithm>
#include <iostream>
#include <cstring>

#include "fragment_reassembly.h"

using namespace std;


FragmentReassembly::FragmentReassembly(std::uint32_t buffer_size)
{
    if (0 == buffer_size)
    {
        buffer_size = def_buffer_size;
    }
    this->buffer_size = buffer_size;
    
    buffer.resize(this->buffer_size);
    state = normal;
}

std::uint32_t FragmentReassembly::get_buffer_size()
{
    return buffer_size;
}

int FragmentReassembly::pop_front()
{
    if (frag_list.size() > 1)
    {
        auto iter = frag_list.begin();
        auto iter_next = iter;
        iter_next ++;
        memmove(&buffer[0], &buffer[(*iter_next).seq_begin - (*iter).seq_begin], frag_list.back().seq_end - (*iter_next).seq_begin);
        frag_list.pop_front();
    }
    else
    {
        frag_list.clear();
    }
    return 0;
}

int FragmentReassembly::clear()
{
    frag_list.clear();
    state = normal;
    return 0;
}

int FragmentReassembly::front_fragment(std::uint64_t &seq_begin, std::uint64_t &seq_end, char *&p_data)
{
    p_data = &buffer[0];
    if (frag_list.empty())
    {
        seq_begin = 0;
        seq_end = 0;
        return -1;
    }
    else
    {
        seq_begin = frag_list.front().seq_begin;
        seq_end = frag_list.front().seq_end;
        return 0;
    }
}

int FragmentReassembly::add_fragment(std::uint64_t seq_begin, std::uint64_t seq_end, const char *p_data)
{
    if (seq_end > seq_begin)
    {
        if (frag_list.empty())
        {
            if ((seq_end-seq_begin) > buffer_size)
            {
                state = lost_data;
                return -2;
            }
            frag_list.push_front(FragmentNode{seq_begin, seq_end});
            memcpy(&buffer[seq_begin-frag_list.front().seq_begin], p_data, (seq_end - seq_begin));
            return 0;
        }
        else
        {
            if (max(seq_end, frag_list.back().seq_end) - min(seq_begin, frag_list.front().seq_begin) > buffer_size)
            {
                state = lost_data;
                return -2;
            }
            auto riter = frag_list.rbegin();
            for (; riter != frag_list.rend(); riter ++)
            {
                if ((*riter).seq_end < seq_begin)               //大于且，不相交，后插入
                {
                    frag_list.insert(riter.base(), FragmentNode{seq_begin, seq_end});
                    memcpy(&buffer[seq_begin-frag_list.front().seq_begin], p_data, (seq_end - seq_begin));
                    return 0;
                }
                else if ((*riter).seq_begin > seq_end)          //小于且，不相交，检测下个节点
                {
                    continue;
                }
                else                                            //相交
                {
                    //更新分段
                    if(seq_begin < frag_list.front().seq_begin)
                    {
                        memmove(&buffer[frag_list.front().seq_begin-seq_begin], &buffer[0], frag_list.back().seq_end-frag_list.front().seq_begin);
                    }
                    (*riter).seq_begin = min((*riter).seq_begin, seq_begin);
                    (*riter).seq_end = max((*riter).seq_end, seq_end);
                    
                    //检测更新后的分段，与下个节点是否相交
                    auto riter_next = riter;
                    riter_next ++;
                    while (frag_list.rend() != riter_next)
                    {
                        if (min((*riter).seq_end, (*riter_next).seq_end) >= max((*riter).seq_begin, (*riter_next).seq_begin))   //相交
                        {
                            //更新分段
                            (*riter).seq_begin = min((*riter).seq_begin, (*riter_next).seq_begin);
                            (*riter).seq_end = max((*riter).seq_end, (*riter_next).seq_end);
                            //删除下个节点
                            riter_next = decltype(riter_next)(frag_list.erase((++riter_next).base()));
                        }
                        else    //不相交，插入结束
                        {
                            break;
                        }
                    }
                    memcpy(&buffer[seq_begin-frag_list.front().seq_begin], p_data, (seq_end - seq_begin));
                    return 0;
                }
            }
            //小于所有节点
            memmove(&buffer[frag_list.front().seq_begin-seq_begin], &buffer[0], frag_list.back().seq_end-frag_list.front().seq_begin);
            frag_list.push_front(FragmentNode{seq_begin, seq_end});
            memcpy(&buffer[seq_begin-frag_list.front().seq_begin], p_data, (seq_end - seq_begin));
            return 0;
        }
    }
    return -1;
}
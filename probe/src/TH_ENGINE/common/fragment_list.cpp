#include <algorithm>
#include <iostream>
#include <cstring>

#include "fragment_list.h"


using namespace std;

FragmentList::FragmentList(std::uint32_t max_fragment)
{
    if (0 == max_fragment)
    {
        max_fragment = def_max_fragment;
    }
    this->max_fragment = max_fragment;
    clear();
}

int FragmentList::clear()
{
    memset(&sum, 0, sizeof(Summary));
    frag_list.clear();
    return 0;
}

std::uint64_t FragmentList::get_lose_len()
{
    uint64_t lose_len = 0;
    if (frag_list.size() > 1)
    {
        auto riter = frag_list.rbegin();
        riter ++;
        for (;riter != frag_list.rend(); riter ++)
        {
            lose_len += (*(riter.base())).seq_begin - (*riter).seq_end;
        }
    }
    return lose_len;
}

int FragmentList::print()
{
    for (auto iter = frag_list.begin(); iter != frag_list.end(); iter ++)
    {
        if (frag_list.begin() == iter)
        {
            cout << (*iter).seq_begin << "-" << (*iter).seq_end;
        }
        else
        {
            cout << "," << (*iter).seq_begin << "-" << (*iter).seq_end;
        }
    }
    cout << endl;
    return 0;
}

int FragmentList::add_fragment(uint64_t seq_begin, uint64_t seq_end)
{
    if (seq_end > seq_begin)
    {
        if (frag_list.empty())
        {
            frag_list.push_front(FragmentNode{seq_begin, seq_end});
            return 0;
        }
        else
        {
            if (frag_list.size() >= max_fragment)
            {
                return -2;
            }
            auto riter = frag_list.rbegin();
            for (; riter != frag_list.rend(); riter ++)
            {
                if ((*riter).seq_end < seq_begin)               //大于且，不相交，后插入
                {
                    sum.mis_order ++;
                    frag_list.insert(riter.base(), FragmentNode{seq_begin, seq_end});
                    return 0;
                }
                else if ((*riter).seq_begin > seq_end)          //小于且，不相交，检测下个节点
                {
                    continue;
                }
                else                                            //相交
                {
                    //更新分段
                    if(seq_begin >=(*riter).seq_begin && seq_end <= (*riter).seq_end)
                    {
                        sum.re_send ++;
                    }
                    (*riter).seq_begin = min((*riter).seq_begin, seq_begin);
                    (*riter).seq_end = max((*riter).seq_end, seq_end);
                    
                    //检测更新后的分段，与下个节点是否相交
                    auto riter_next = riter;
                    riter_next ++;
                    while (frag_list.rend() != riter_next)
                    {
                        if (min((*riter).seq_end, (*riter_next).seq_end) >= max((*riter).seq_begin, (*riter_next).seq_begin))   //相交
                        {
                            //更新分段
                            (*riter).seq_begin = min((*riter).seq_begin, (*riter_next).seq_begin);
                            (*riter).seq_end = max((*riter).seq_end, (*riter_next).seq_end);
                            //删除下个节点
                            riter_next = decltype(riter_next)(frag_list.erase((++riter_next).base()));
                        }
                        else    //不相交，插入结束
                        {
                            break;
                        }
                    }
                    return 0;
                }
            }
            //小于所有节点
            sum.mis_order ++;
            frag_list.push_front(FragmentNode{seq_begin, seq_end});
            return 0;
        }
    }
    return -1;
}
#!/bin/bash

LOGIN="true"

function test_cmd_exit()
{
    if [ $1 -eq 0 ];
    then 
        echo $2 " success"
    else 
        echo $2 " failed"
        exit 1
    fi
}

if [ $# -eq 0 ]
then
    exit 1
fi

if [ $# -gt 1 ]
then
    if [ "x$2" == "xfalse" ]
    then
        LOGIN="false"
    fi
fi
cd $(dirname $(realpath $0))

if [ $LOGIN == "true" ]
then
    docker login -u admin -p lqxzjyg! https://hb.gs.lan
fi

pushd ./lib_src/common_lib/
BUILDER_VERSION="$(uname -r)_$(git log -n1 --pretty=format:'%H' .)"
popd

if [ $(docker images -q "hb.gs.lan/dev_52/th_common_lib:${BUILDER_VERSION}" | wc -l) -eq 0 ]
then
    pushd ./lib_src/common_lib/
    docker build --build-arg kernel_version=$(uname -r) -t "th_common_lib:${BUILDER_VERSION}" .
    docker tag th_common_lib:${BUILDER_VERSION} hb.gs.lan/dev_52/th_common_lib:${BUILDER_VERSION}
    if [ $LOGIN == "true" ]
    then
        docker push hb.gs.lan/dev_52/th_common_lib:${BUILDER_VERSION}
    fi
    popd
fi

RELEASE_VERSION=$(git log -n1 --pretty=format:'%H')

if [ "x$1" == "xproduct_analysis" ]
then
    \cp -f DockerfileOffline Dockerfile
else
    \cp -f DockerfileOnline Dockerfile
fi
docker build --build-arg product_name=$1 --build-arg builder_version=${BUILDER_VERSION} --build-arg release_version=$1.${RELEASE_VERSION} -t $1:${RELEASE_VERSION} .
docker tag $1:${RELEASE_VERSION} hb.gs.lan/dev_52/$1:${RELEASE_VERSION}
if [ $LOGIN == "true" ]
then
    docker push hb.gs.lan/dev_52/$1:${RELEASE_VERSION}
fi


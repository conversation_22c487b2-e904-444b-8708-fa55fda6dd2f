#ifndef __SET_VECTOR_UNION_H__
#define __SET_VECTOR_UNION_H__
#include <set>
#include <vector>
using namespace std;

template<typename Type>
class CSetVectorUnion
{
public:
    void push_back(Type node)
    {
        if(m_set.find(node) == m_set.end())
        {
            auto ret = m_set.insert(node);
            if(ret.second)
            {
                m_vector.push_back(ret.first);
            }
        }
    }
    size_t size()
    {
        return m_vector.size();
    }
    void clear()
    {
        m_set.clear();
        m_vector.clear();
    }

public:
    set<Type> m_set;
    vector<typename set<Type>::iterator> m_vector;
};

#endif

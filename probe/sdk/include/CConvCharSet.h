// Last Update:2016-05-17 14:42:32
/**
 * @file CConvCharSet.h
 * @brief :
 * <AUTHOR>
 * @version 0.1.00
 * @date 2015-10-30
 */

#ifndef _C_CONV_CHAR_SET_H
#define _C_CONV_CHAR_SET_H
#include <iconv.h>
#include <string.h>
#include <string>
#include <algorithm>  
#include <map>
using namespace std;
string ZHCNcode(const char * src, int len); 
class CConvCharSet {
    private :
        std::map<string ,iconv_t * > IconvHandleMap;
    public:
        CConvCharSet();
        ~CConvCharSet();
        int Convert(char * psSrc , int iSrcLen ,char * psDst,int & iDstlen,string sSrcSet,string sDstSet);

};

int enc_get_utf8_size(const unsigned char pInput);
int enc_unicode_to_utf8_one(unsigned long unic, unsigned char *pOutput,int outsize ) ; 
int enc_utf8_to_unicode_one(const unsigned char* pInput, unsigned long *Unic);
int  utf8_cmp(char * charset1 , char * charset2 ) ; 

#endif  /*_C_CONV_CHAR_SET_H*/

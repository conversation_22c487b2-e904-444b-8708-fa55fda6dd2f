#ifndef __SESSION_PUB_PRORO_H__
#define __SESSION_PUB_PRORO_H__

#include <stdint.h>
#include <inttypes.h>
#include <string>
using namespace std;

class session_pub_http
{
public:
    session_pub_http()
    {
        url = "";
        act = "";
        host = "";
        response = "";
        user_agent = "";
    }
    string url;
    string act;
    string host;
    string response;
    string user_agent;
};

class session_pub_dns_answer
{
public:
    session_pub_dns_answer()
    {
        name = "";
        value = "";
    }
    bool operator<(const session_pub_dns_answer &that) const
    {
        if(name < that.name)
        {
            return true;
        }
        else if(name > that.name)
        {
            return false;
        }
        if(value < that.value)
        {
            return true;
        }
        else if(value > that.value)
        {
            return false;
        }
        return false;
    }
    bool operator>(const session_pub_dns_answer &that) const
    {
        if(name > that.name)
        {
            return true;
        }
        else if(name < that.name)
        {
            return false;
        }
        if(value > that.value)
        {
            return true;
        }
        else if(value < that.value)
        {
            return false;
        }
        return false;
    }
    string name;
    string value;
};

class session_pub_dns
{
public:
    session_pub_dns()
    {
        domain = "";
        domain_ip = "";
        answer.clear();
    }
    bool operator<(const session_pub_dns &that) const
    {
        if(domain < that.domain)
        {
            return true;
        }
        else if(domain > that.domain)
        {
            return false;
        }
        if(domain_ip < that.domain_ip)
        {
            return true;
        }
        else if(domain_ip > that.domain_ip)
        {
            return false;
        }
        if(answer.size() < that.answer.size())
        {
            return true;
        }
        else if(answer.size() > that.answer.size())
        {
            return false;
        }
        for(vector<session_pub_dns_answer>::size_type i = 0; i < answer.size(); i ++)
        {
            if(answer[i] < that.answer[i])
            {
                return true;
            }
            else if(answer[i] > that.answer[i])
            {
                return false;
            }
        }
        return false;
    }
    string domain;
    string domain_ip;
    vector<session_pub_dns_answer> answer;
};

class session_pub_ssl
{
public:
    session_pub_ssl()
    {
        ch_ciphersuit = "";
        ch_ciphersuit_num = 0;
        ch_server_name = "";
        ch_alpn = "";
        c_cert = "";
        c_cert_num = 0;
        s_cert = "";
        s_cert_num = 0;
    }
    string      ch_ciphersuit;
    uint32_t    ch_ciphersuit_num;
    string      ch_server_name;
    string      ch_alpn;
    string      c_cert;
    uint32_t    c_cert_num;
    string      s_cert;
    uint32_t    s_cert_num;
};

#endif

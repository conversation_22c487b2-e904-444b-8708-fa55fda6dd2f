/**
 * @file statics.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-04-26
 */
#ifndef STATICS_H
#define STATICS_H
#include <unistd.h>
#include <ctype.h>
#include <mntent.h>  
#include <string>  
#include <fcntl.h>
#include <stdlib.h>
#include <errno.h>
#include <dirent.h>
#include <math.h>
#include <list>
#include <map>
#include <sys/types.h>
#include <sys/time.h>
#include <sys/stat.h>
#include <sys/vfs.h>

#include <sys/param.h>		/* for HZ */
using namespace std;



#define NCPUSTATES 4
static char *cpustatenames[NCPUSTATES+1] =
{
    "user", "nice", "system", "idle",
    NULL
};
#define NMEMSTATS 6
static char *memoryname[NMEMSTATS+1] =
{
    "K used, ", "K free, ", "K shd, ", "<PERSON> buf  Swap: ",
    "K used, ", "K free",
    NULL
};

class disk_info
{
public:
    unsigned long long harddisk_total;
    unsigned long long harddisk_used;
    unsigned harddisk_usage_rate;
    string mounted_on;
    string filesystem;
};
class system_info_state
{
public:
    float   cpustates[NCPUSTATES];
    int     mem_total;
    int     mem_free;
    float   mem_usage_rate;
    list<disk_info> disk_list;
    long    boot_time;
    string  ip;
};

class system_info
{
private:
    long    cp_time[NCPUSTATES];
    long    cp_old[NCPUSTATES];
    long    cp_diff[NCPUSTATES];

public:
    int     memory[NMEMSTATS];
    system_info_state sys_state;
    system_info();
    int update_system_info(const char* eth=NULL);
    long percentages(int cnt);
};

/*=PROCESS INFORMATION==================================================*/
class proc_info_state
{
public:
    long    run_time;
    float   cpu_usage_rate;
    int     mem_used;
    float   mem_usage_rate;
    string  ip;
    pid_t   pid;
};

class top_proc
{
private:
    char name[64];
    unsigned long size, rss;	/* in k */
    unsigned long time;
    map<pid_t, unsigned long> pid_oldtime_map;
    float   pcpu, wcpu;
    struct timeval lasttime;
public:
    proc_info_state proc_state;
    void read_one_proc_stat(pid_t pid);
    int update_proc_info(pid_t pid,const char *eth=NULL);
};

class statics
{
    system_info sys_info;
    top_proc proc;
 public:
     const proc_info_state* get_proc_info_state(pid_t pid,const char *eth=NULL);
     const system_info_state* get_sys_info_proc(const char *eth=NULL);

};
#endif /*STATICS_H*/

// Last Update:2015-11-03 18:02:39
/**
 * @file file_md5.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2015-08-24
 */
#ifndef FILE_MD5_H
#define FILE_MD5_H
#include "md5.h"

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>

#define READ_DATA_SIZE  1024
#define MD5_SIZE        16
#define MD5_STR_LEN     (MD5_SIZE * 2)

int ComputeFileMd5(const char *file_path, char *value);
int ComputeMd5(const char *text,int length, char *value);
#endif

// Last Update:2018-01-08 15:10:01
/**
 * @file data_msg.h
 * @brief : 数据同步消息
 * <AUTHOR>
 * @version 0.1.00
 * @date 2017-06-20
 */

#ifndef DATA_MSG_H
#define DATA_MSG_H
#define ADD_DATA 1 
#define DEL_DATA 2
class data_msg 
{
    public:
        data_msg():type(0), service_id(0), id(0), data(NULL), datalen(0), buf(NULL){}
        ~data_msg()
        {
            if(data != NULL)
            {
                delete [] data ;
                data = NULL;
            }
            if(buf != NULL) 
            {
                delete [] buf ;
                buf = NULL;
            }
        }
        int type ;  // add del  
        int service_id ; // database  
        int id ; // key 
        char  * data; // value
        int datalen ;
        char * buf ;
        char * get_send_buf()
        {
            return buf ;
        }
        int get_send_buf_len()
        {
            return datalen   + 12;
        } 
        int parse_send(char * &buf ) 
        {
            int len = 4 + 4 + 4+ datalen ;
            buf = new char[len];
            memset(buf, 0x00, len);
            *(int *) buf = htonl(type);
            *(int *)(buf +4) = htonl(service_id);
            *(int *)(buf +8) = htonl(id);
            memcpy(buf + 12 , data  , datalen) ;
            //data = NULL;在外边置空by Yangjinhao 20180108
            return len ;
        }
        int reav_parse(char * buff, int len ) 
        {
            if(len >=12) 
            {
                datalen = len - 4-4-4;
                type = ntohl(*(int *)buff );
                service_id = ntohl(*(int *)(buff + 4));
                id = ntohl(*(int *)(buff + 8));
                data = new char[datalen+1];
                memcpy(data,buff+ 12 ,  datalen) ;
                data[datalen] = '\0';
                buf = NULL;
                return 0 ;
            }
            else 
            {
                return -1;
            }
        }
    private:
};

#endif  /*DATA_MSG_H*/

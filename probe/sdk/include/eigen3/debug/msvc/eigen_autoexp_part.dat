; ***************************************************************
; * Eigen Visualizer
; *
; * Author: Hauke Heibel <<EMAIL>>
; *
; * Support the enhanced debugging of the following Eigen
; * types (*: any, +:fixed dimension) :
; *
; * - Eigen::Matrix<*,4,1,*,*,*> and Eigen::Matrix<*,1,4,*,*,*>
; * - Eigen::Matrix<*,3,1,*,*,*> and Eigen::Matrix<*,1,3,*,*,*>
; * - Eigen::Matrix<*,2,1,*,*,*> and Eigen::Matrix<*,1,2,*,*,*>
; * - Eigen::Matrix<*,-1,-1,*,*,*>
; * - Eigen::Matrix<*,+,-1,*,*,*>
; * - Eigen::Matrix<*,-1,+,*,*,*>
; * - Eigen::Matrix<*,+,+,*,*,*>
; *
; * Matrices are displayed properly independantly of the memory
; * alignment (RowMajor vs. ColMajor).
; *
; * This file is distributed WITHOUT ANY WARRANTY. Please ensure
; * that your original autoexp.dat file is copied to a safe 
; * place before proceeding with its modification.
; ***************************************************************

[Visualizer]

; Fixed size 4-vectors
Eigen::Matrix<*,4,1,*,*,*>|Eigen::Matrix<*,1,4,*,*,*>{
   children
   (
      #(
        [internals]: [$c,!],
         x : ($c.m_storage.m_data.array)[0],
         y : ($c.m_storage.m_data.array)[1],
         z : ($c.m_storage.m_data.array)[2],
         w : ($c.m_storage.m_data.array)[3]
      )
   )

   preview
   (
      #(
        "[",
        4,
        "](",
        #array(expr: $e.m_storage.m_data.array[$i], size: 4),
        ")"
      )
   )
}

; Fixed size 3-vectors
Eigen::Matrix<*,3,1,*,*,*>|Eigen::Matrix<*,1,3,*,*,*>{
   children
   (
      #(
        [internals]: [$c,!],
         x : ($c.m_storage.m_data.array)[0],
         y : ($c.m_storage.m_data.array)[1],
         z : ($c.m_storage.m_data.array)[2]
      )
   )

   preview
   (
      #(
        "[",
        3,
        "](",
        #array(expr: $e.m_storage.m_data.array[$i], size: 3),
        ")"
      )
   )
}

; Fixed size 2-vectors
Eigen::Matrix<*,2,1,*,*,*>|Eigen::Matrix<*,1,2,*,*,*>{
   children
   (
      #(
        [internals]: [$c,!],
         x : ($c.m_storage.m_data.array)[0],
         y : ($c.m_storage.m_data.array)[1]
      )
   )

   preview
   (
      #(
        "[",
        2,
        "](",
        #array(expr: $e.m_storage.m_data.array[$i], size: 2),
        ")"
      )
   )
}

; Fixed size 1-vectors
Eigen::Matrix<*,1,1,*,*,*>|Eigen::Matrix<*,1,1,*,*,*>{
   children
   (
      #(
        [internals]: [$c,!],
         x : ($c.m_storage.m_data.array)[0]
      )
   )

   preview
   (
      #(
        "[",
        1,
        "](",
        #array(expr: $e.m_storage.m_data.array[$i], size: 1),
        ")"
      )
   )
}

; Dynamic matrices (ColMajor and RowMajor support)
Eigen::Matrix<*,-1,-1,*,*,*>{
  children
   (
      #(
         [internals]: [$c,!],
         rows: $c.m_storage.m_rows,
         cols: $c.m_storage.m_cols,
         ; Check for RowMajorBit
         #if ($c.Flags & 0x1) (
             #array(
                rank: 2,
                base: 0,
                expr: ($c.m_storage.m_data)[($i % $c.m_storage.m_rows)*$c.m_storage.m_cols + (($i- $i % $c.m_storage.m_rows)/$c.m_storage.m_rows)], 
                size: ($r==1)*$c.m_storage.m_rows+($r==0)*$c.m_storage.m_cols
             )
         ) #else (
             #array(
                rank: 2,
                base: 0,
                expr: ($c.m_storage.m_data)[$i],
                size: ($r==1)*$c.m_storage.m_rows+($r==0)*$c.m_storage.m_cols
             )
         )
      )
   )

   preview
   (
     #(
         "[",
           $c.m_storage.m_rows,
         ",",
           $c.m_storage.m_cols,
         "](",
           #array(
            expr :    [($c.m_storage.m_data)[$i],g],
            size :    $c.m_storage.m_rows*$c.m_storage.m_cols
           ),
         ")"
      )
   )
}

; Fixed rows, dynamic columns matrix (ColMajor and RowMajor support)
Eigen::Matrix<*,*,-1,*,*,*>{
  children
   (
      #(
         [internals]: [$c,!],
         rows: $c.RowsAtCompileTime,
         cols: $c.m_storage.m_cols,
         ; Check for RowMajorBit
         #if ($c.Flags & 0x1) (
             #array(
                rank: 2,
                base: 0,
                expr: ($c.m_storage.m_data)[($i % $c.RowsAtCompileTime)*$c.m_storage.m_cols + (($i- $i % $c.RowsAtCompileTime)/$c.RowsAtCompileTime)],
                size: ($r==1)*$c.RowsAtCompileTime+($r==0)*$c.m_storage.m_cols
             )
         ) #else (
             #array(
                rank: 2,
                base: 0,
                expr: ($c.m_storage.m_data)[$i],
                size: ($r==1)*$c.RowsAtCompileTime+($r==0)*$c.m_storage.m_cols
             )
         )
      )
   )

   preview
   (
     #(
         "[",
           $c.RowsAtCompileTime,
         ",",
           $c.m_storage.m_cols,
         "](",
           #array(
            expr :    [($c.m_storage.m_data)[$i],g],
            size :    $c.RowsAtCompileTime*$c.m_storage.m_cols
           ),
         ")"
      )
   )
}

; Dynamic rows, fixed columns matrix (ColMajor and RowMajor support)
Eigen::Matrix<*,-1,*,*,*,*>{
  children
   (
      #(
         [internals]: [$c,!],
         rows: $c.m_storage.m_rows,
         cols: $c.ColsAtCompileTime,
         ; Check for RowMajorBit
         #if ($c.Flags & 0x1) (
             #array(
                rank: 2,
                base: 0,
                expr: ($c.m_storage.m_data)[($i % $c.m_storage.m_rows)*$c.ColsAtCompileTime + (($i- $i % $c.m_storage.m_rows)/$c.m_storage.m_rows)], 
                size: ($r==1)*$c.m_storage.m_rows+($r==0)*$c.ColsAtCompileTime
             )
         ) #else (
             #array(
                rank: 2,
                base: 0,
                expr: ($c.m_storage.m_data)[$i],
                size: ($r==1)*$c.m_storage.m_rows+($r==0)*$c.ColsAtCompileTime
             )
         )
      )
   )

   preview
   (
     #(
         "[",
           $c.m_storage.m_rows,
         ",",
           $c.ColsAtCompileTime,
         "](",
           #array(
            expr :    [($c.m_storage.m_data)[$i],g],
            size :    $c.m_storage.m_rows*$c.ColsAtCompileTime
           ),
         ")"
      )
   )
}

; Fixed size matrix (ColMajor and RowMajor support)
Eigen::Matrix<*,*,*,*,*,*>{
  children
   (
      #(
         [internals]: [$c,!],
         rows: $c.RowsAtCompileTime,
         cols: $c.ColsAtCompileTime,
         ; Check for RowMajorBit
         #if ($c.Flags & 0x1) (
             #array(
                rank: 2,
                base: 0,
                expr: ($c.m_storage.m_data.array)[($i % $c.RowsAtCompileTime)*$c.ColsAtCompileTime + (($i- $i % $c.RowsAtCompileTime)/$c.RowsAtCompileTime)], 
                size: ($r==1)*$c.RowsAtCompileTime+($r==0)*$c.ColsAtCompileTime
             )
         ) #else (
             #array(
                rank: 2,
                base: 0,
                expr: ($c.m_storage.m_data.array)[$i],
                size: ($r==1)*$c.RowsAtCompileTime+($r==0)*$c.ColsAtCompileTime
             )
         )
      )
   )

   preview
   (
     #(
         "[",
           $c.RowsAtCompileTime,
         ",",
           $c.ColsAtCompileTime,
         "](",
           #array(
            expr :    [($c.m_storage.m_data.array)[$i],g],
            size :    $c.RowsAtCompileTime*$c.ColsAtCompileTime
           ),
         ")"
      )
   )
}

<?xml version="1.0" encoding="utf-8"?>

<AutoVisualizer xmlns="http://schemas.microsoft.com/vstudio/debugger/natvis/2010">

  <!-- Fixed x Fixed Matrix -->
  <Type Name="Eigen::Matrix&lt;*,*,*,*,*,*&gt;">      
      <AlternativeType Name="Eigen::Array&lt;*,-1,-1,*,*,*&gt;"/>
      <DisplayString>[{$T2}, {$T3}] (fixed matrix)</DisplayString>
      <Expand>
        <ArrayItems Condition="Flags%2"> <!-- row major layout -->
          <Rank>2</Rank>
          <Size>$i==0 ? $T2 : $T3</Size>
          <ValuePointer>m_storage.m_data.array</ValuePointer>
        </ArrayItems>
        <ArrayItems Condition="!(Flags%2)"> <!-- column major layout -->
          <Direction>Backward</Direction>
          <Rank>2</Rank>
          <Size>$i==0 ? $T2 : $T3</Size>
          <ValuePointer>m_storage.m_data.array</ValuePointer>
        </ArrayItems>
      </Expand>
  </Type>
  
  <!-- 2 x 2 Matrix -->
  <Type Name="Eigen::Matrix&lt;*,2,2,*,*,*&gt;">      
      <AlternativeType Name="Eigen::Array&lt;*,2,2,*,*,*&gt;"/>
      <DisplayString>[2, 2] (fixed matrix)</DisplayString>
      <Expand>
        <Synthetic Name="[row 0]" Condition="Flags%2">
          <DisplayString>({m_storage.m_data.array[0]}, {m_storage.m_data.array[1]})</DisplayString>
        </Synthetic>
        <Synthetic Name="[row 0]" Condition="!(Flags%2)">
          <DisplayString>({m_storage.m_data.array[0]}, {m_storage.m_data.array[2]})</DisplayString>
        </Synthetic>
        <Synthetic Name="[row 1]" Condition="Flags%2">
          <DisplayString>({m_storage.m_data.array[2]}, {m_storage.m_data.array[3]})</DisplayString>
        </Synthetic>
        <Synthetic Name="[row 1]" Condition="!(Flags%2)">
          <DisplayString>({m_storage.m_data.array[1]}, {m_storage.m_data.array[3]})</DisplayString>
        </Synthetic>        
      </Expand>
  </Type>
  
  <!-- 3 x 3 Matrix -->
  <Type Name="Eigen::Matrix&lt;*,3,3,*,*,*&gt;">      
      <AlternativeType Name="Eigen::Array&lt;*,3,3,*,*,*&gt;"/>
      <DisplayString>[3, 3] (fixed matrix)</DisplayString>
      <Expand>
        <Synthetic Name="[row 0]" Condition="Flags%2">
          <DisplayString>({m_storage.m_data.array[0]}, {m_storage.m_data.array[1]}, {m_storage.m_data.array[2]})</DisplayString>
        </Synthetic>
        <Synthetic Name="[row 0]" Condition="!(Flags%2)">
          <DisplayString>({m_storage.m_data.array[0]}, {m_storage.m_data.array[3]}, {m_storage.m_data.array[6]})</DisplayString>
        </Synthetic>
        <Synthetic Name="[row 1]" Condition="Flags%2">
          <DisplayString>({m_storage.m_data.array[3]}, {m_storage.m_data.array[4]}, {m_storage.m_data.array[5]})</DisplayString>
        </Synthetic>
        <Synthetic Name="[row 1]" Condition="!(Flags%2)">
          <DisplayString>({m_storage.m_data.array[1]}, {m_storage.m_data.array[4]}, {m_storage.m_data.array[7]})</DisplayString>
        </Synthetic>
        <Synthetic Name="[row 2]" Condition="Flags%2">
          <DisplayString>({m_storage.m_data.array[6]}, {m_storage.m_data.array[7]}, {m_storage.m_data.array[8]})</DisplayString>
        </Synthetic>
        <Synthetic Name="[row 2]" Condition="!(Flags%2)">
          <DisplayString>({m_storage.m_data.array[2]}, {m_storage.m_data.array[5]}, {m_storage.m_data.array[8]})</DisplayString>
        </Synthetic>        
      </Expand>
  </Type>
  
  <!-- 4 x 4 Matrix -->
  <Type Name="Eigen::Matrix&lt;*,4,4,*,*,*&gt;">      
      <AlternativeType Name="Eigen::Array&lt;*,4,4,*,*,*&gt;"/>
      <DisplayString>[4, 4] (fixed matrix)</DisplayString>
      <Expand>
        <Synthetic Name="[row 0]" Condition="Flags%2">
          <DisplayString>({m_storage.m_data.array[0]}, {m_storage.m_data.array[1]}, {m_storage.m_data.array[2]}, {m_storage.m_data.array[3]})</DisplayString>
        </Synthetic>
        <Synthetic Name="[row 0]" Condition="!(Flags%2)">
          <DisplayString>({m_storage.m_data.array[0]}, {m_storage.m_data.array[4]}, {m_storage.m_data.array[8]}, {m_storage.m_data.array[12]})</DisplayString>
        </Synthetic>
        <Synthetic Name="[row 1]" Condition="Flags%2">
          <DisplayString>({m_storage.m_data.array[4]}, {m_storage.m_data.array[5]}, {m_storage.m_data.array[6]}, {m_storage.m_data.array[7]})</DisplayString>
        </Synthetic>
        <Synthetic Name="[row 1]" Condition="!(Flags%2)">
          <DisplayString>({m_storage.m_data.array[1]}, {m_storage.m_data.array[5]}, {m_storage.m_data.array[9]}, {m_storage.m_data.array[13]})</DisplayString>
        </Synthetic>
        <Synthetic Name="[row 2]" Condition="Flags%2">
          <DisplayString>({m_storage.m_data.array[8]}, {m_storage.m_data.array[9]}, {m_storage.m_data.array[10]}, {m_storage.m_data.array[11]})</DisplayString>
        </Synthetic>
        <Synthetic Name="[row 2]" Condition="!(Flags%2)">
          <DisplayString>({m_storage.m_data.array[2]}, {m_storage.m_data.array[6]}, {m_storage.m_data.array[10]}, {m_storage.m_data.array[14]})</DisplayString>
        </Synthetic>
        <Synthetic Name="[row 3]" Condition="Flags%2">
          <DisplayString>({m_storage.m_data.array[12]}, {m_storage.m_data.array[13]}, {m_storage.m_data.array[14]}, {m_storage.m_data.array[15]})</DisplayString>
        </Synthetic>
        <Synthetic Name="[row 3]" Condition="!(Flags%2)">
          <DisplayString>({m_storage.m_data.array[3]}, {m_storage.m_data.array[7]}, {m_storage.m_data.array[11]}, {m_storage.m_data.array[15]})</DisplayString>
        </Synthetic>        
      </Expand>
  </Type>  
  
  <!-- Dynamic x Dynamic Matrix -->
  <Type Name="Eigen::Matrix&lt;*,-1,-1,*,*,*&gt;">      
      <AlternativeType Name="Eigen::Array&lt;*,-1,-1,*,*,*&gt;"/>
      <DisplayString Condition="m_storage.m_data == 0">empty</DisplayString>
      <DisplayString Condition="m_storage.m_data != 0">[{m_storage.m_rows}, {m_storage.m_cols}] (dynamic matrix)</DisplayString>
      <Expand>
        <ArrayItems Condition="Flags%2"> <!-- row major layout -->
          <Rank>2</Rank>
          <Size>$i==0 ? m_storage.m_rows : m_storage.m_cols</Size>
          <ValuePointer>m_storage.m_data</ValuePointer>
        </ArrayItems>
        <ArrayItems Condition="!(Flags%2)"> <!-- column major layout -->
          <Direction>Backward</Direction>
          <Rank>2</Rank>
          <Size>$i==0 ? m_storage.m_rows : m_storage.m_cols</Size>
          <ValuePointer>m_storage.m_data</ValuePointer>
        </ArrayItems>
      </Expand>
  </Type>
  
  <!-- Fixed x Dynamic Matrix -->
  <Type Name="Eigen::Matrix&lt;*,*,-1,*,*,*&gt;">
      <AlternativeType Name="Eigen::Array&lt;*,*,-1,*,*,*&gt;"/>
      <DisplayString Condition="m_storage.m_data == 0">empty</DisplayString>
      <DisplayString Condition="m_storage.m_data != 0">[{$T2}, {m_storage.m_cols}] (dynamic column matrix)</DisplayString>
      <Expand>
        <ArrayItems Condition="Flags%2"> <!-- row major layout -->
          <Rank>2</Rank>
          <Size>$i==0 ? $T2 : m_storage.m_cols</Size>
          <ValuePointer>m_storage.m_data</ValuePointer>
        </ArrayItems>
        <ArrayItems Condition="!(Flags%2)"> <!-- column major layout -->
          <Direction>Backward</Direction>
          <Rank>2</Rank>
          <Size>$i==0 ? $T2 : m_storage.m_cols</Size>
          <ValuePointer>m_storage.m_data</ValuePointer>
        </ArrayItems>
      </Expand>
  </Type>
  
  <!-- Dynamic x Fixed Matrix -->
  <Type Name="Eigen::Matrix&lt;*,-1,*,*,*,*&gt;">
      <AlternativeType Name="Eigen::Array&lt;*,-1,*,*,*,*&gt;"/>
      <DisplayString Condition="m_storage.m_data == 0">empty</DisplayString>
      <DisplayString Condition="m_storage.m_data != 0">[{m_storage.m_rows}, {$T2}] (dynamic row matrix)</DisplayString>
      <Expand>
        <ArrayItems Condition="Flags%2"> <!-- row major layout -->
          <Rank>2</Rank>
          <Size>$i==0 ? m_storage.m_rows : $T2</Size>
          <ValuePointer>m_storage.m_data</ValuePointer>
        </ArrayItems>
        <ArrayItems Condition="!(Flags%2)"> <!-- column major layout -->
          <Direction>Backward</Direction>
          <Rank>2</Rank>
          <Size>$i==0 ? m_storage.m_rows : $T2</Size>
          <ValuePointer>m_storage.m_data</ValuePointer>
        </ArrayItems>
      </Expand>
  </Type>
  
  <!-- Dynamic Column Vector -->
  <Type Name="Eigen::Matrix&lt;*,1,-1,*,*,*&gt;">
      <AlternativeType Name="Eigen::Array&lt;*,1,-1,*,*,*&gt;"/>
      <DisplayString Condition="m_storage.m_data == 0">empty</DisplayString>
      <DisplayString Condition="m_storage.m_data != 0">[{m_storage.m_cols}] (dynamic column vector)</DisplayString>
      <Expand>
        <Item Name="[size]">m_storage.m_cols</Item>
        <ArrayItems>
          <Size>m_storage.m_cols</Size>
          <ValuePointer>m_storage.m_data</ValuePointer>
        </ArrayItems>
      </Expand>
  </Type>
  
  <!-- Dynamic Row Vector -->
  <Type Name="Eigen::Matrix&lt;*,-1,1,*,*,*&gt;">
      <AlternativeType Name="Eigen::Array&lt;*,-1,1,*,*,*&gt;"/>
      <DisplayString Condition="m_storage.m_data == 0">empty</DisplayString>
      <DisplayString Condition="m_storage.m_data != 0">[{m_storage.m_rows}] (dynamic row vector)</DisplayString>
      <Expand>
        <Item Name="[size]">m_storage.m_rows</Item>
        <ArrayItems>
          <Size>m_storage.m_rows</Size>
          <ValuePointer>m_storage.m_data</ValuePointer>
        </ArrayItems>
      </Expand>
  </Type>
  
  <!-- Fixed Vector -->
  <Type Name="Eigen::Matrix&lt;*,1,1,*,*,*&gt;">
      <AlternativeType Name="Eigen::Array&lt;*,1,1,*,*,*&gt;"/>
      <DisplayString>[1] ({m_storage.m_data.array[0]})</DisplayString>
      <Expand>
        <Item Name="[x]">m_storage.m_data.array[0]</Item>
      </Expand>
  </Type>
  
  <Type Name="Eigen::Matrix&lt;*,2,1,*,*,*&gt;">
      <AlternativeType Name="Eigen::Matrix&lt;*,1,2,*,*,*&gt;"/>
      <AlternativeType Name="Eigen::Array&lt;*,2,1,*,*,*&gt;"/>
      <AlternativeType Name="Eigen::Array&lt;*,1,2,*,*,*&gt;"/>
      <DisplayString>[2] ({m_storage.m_data.array[0]}, {m_storage.m_data.array[1]})</DisplayString>
      <Expand>
        <Item Name="[x]">m_storage.m_data.array[0]</Item>
        <Item Name="[y]">m_storage.m_data.array[1]</Item>
      </Expand>
  </Type>
  
  <Type Name="Eigen::Matrix&lt;*,3,1,*,*,*&gt;">
      <AlternativeType Name="Eigen::Matrix&lt;*,1,3,*,*,*&gt;"/>
      <AlternativeType Name="Eigen::Array&lt;*,3,1,*,*,*&gt;"/>
      <AlternativeType Name="Eigen::Array&lt;*,1,3,*,*,*&gt;"/>
      <DisplayString>[3] ({m_storage.m_data.array[0]}, {m_storage.m_data.array[1]}, {m_storage.m_data.array[2]})</DisplayString>
      <Expand>
        <Item Name="[x]">m_storage.m_data.array[0]</Item>
        <Item Name="[y]">m_storage.m_data.array[1]</Item>
        <Item Name="[z]">m_storage.m_data.array[2]</Item>
      </Expand>
  </Type>
  
    <Type Name="Eigen::Matrix&lt;*,4,1,*,*,*&gt;">
      <AlternativeType Name="Eigen::Matrix&lt;*,1,4,*,*,*&gt;"/>
      <AlternativeType Name="Eigen::Array&lt;*,4,1,*,*,*&gt;"/>
      <AlternativeType Name="Eigen::Array&lt;*,1,4,*,*,*&gt;"/>
      <DisplayString>[4] ({m_storage.m_data.array[0]}, {m_storage.m_data.array[1]}, {m_storage.m_data.array[2]}, {m_storage.m_data.array[3]})</DisplayString>
      <Expand>
        <Item Name="[x]">m_storage.m_data.array[0]</Item>
        <Item Name="[y]">m_storage.m_data.array[1]</Item>
        <Item Name="[z]">m_storage.m_data.array[2]</Item>
        <Item Name="[w]">m_storage.m_data.array[3]</Item>
      </Expand>
  </Type>

</AutoVisualizer>

// This file is part of Eigen, a lightweight C++ template library
// for linear algebra.
//
//
// This Source Code Form is subject to the terms of the Mozilla
// Public License v. 2.0. If a copy of the MPL was not distributed
// with this file, You can obtain one at http://mozilla.org/MPL/2.0/.

#ifndef EIGEN_ARPACKSUPPORT_MODULE_H
#define EIGEN_ARPACKSUPPORT_MODULE_H

#include <Eigen/Core>

#include <Eigen/src/Core/util/DisableStupidWarnings.h>

/** \defgroup ArpackSupport_Module Arpack support module
  *
  * This module provides a wrapper to <PERSON>rp<PERSON>, a library for sparse eigenvalue decomposition.
  *
  * \code
  * #include <Eigen/ArpackSupport>
  * \endcode
  */

#include <Eigen/SparseCholesky>
#include "src/Eigenvalues/ArpackSelfAdjointEigenSolver.h"

#include <Eigen/src/Core/util/ReenableStupidWarnings.h>

#endif // EIGEN_ARPACKSUPPORT_MODULE_H
/* vim: set filetype=cpp et sw=2 ts=2 ai: */

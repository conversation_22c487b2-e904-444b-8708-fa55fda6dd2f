namespace Eigen {

/** \eigenManualPage TutorialReshapeSlicing Reshape and Slicing

%Eigen does not expose convenient methods to take slices or to reshape a matrix yet.
Nonetheless, such features can easily be emulated using the Map class.

\eigenAutoToc

\section TutorialReshape Reshape

A reshape operation consists in modifying the sizes of a matrix while keeping the same coefficients.
Instead of modifying the input matrix itself, which is not possible for compile-time sizes, the approach consist in creating a different \em view on the storage using class Map.
Here is a typical example creating a 1D linear view of a matrix:

<table class="example">
<tr><th>Example:</th><th>Output:</th></tr>
<tr><td>
\include Tutorial_ReshapeMat2Vec.cpp
</td>
<td>
\verbinclude Tutorial_ReshapeMat2Vec.out
</td></tr></table>

Remark how the storage order of the input matrix modifies the order of the coefficients in the linear view.
Here is another example reshaping a 2x6 matrix to a 6x2 one:
<table class="example">
<tr><th>Example:</th><th>Output:</th></tr>
<tr><td>
\include Tutorial_ReshapeMat2Mat.cpp
</td>
<td>
\verbinclude Tutorial_ReshapeMat2Mat.out
</td></tr></table>



\section TutorialSlicing Slicing

Slicing consists in taking a set of rows, columns, or elements, uniformly spaced within a matrix.
Again, the class Map allows to easily mimic this feature.

For instance, one can skip every P elements in a vector:
<table class="example">
<tr><th>Example:</th><th>Output:</th></tr>
<tr><td>
\include Tutorial_SlicingVec.cpp
</td>
<td>
\verbinclude Tutorial_SlicingVec.out
</td></tr></table>

One can olso take one column over three using an adequate outer-stride or inner-stride depending on the actual storage order:
<table class="example">
<tr><th>Example:</th><th>Output:</th></tr>
<tr><td>
\include Tutorial_SlicingCol.cpp
</td>
<td>
\verbinclude Tutorial_SlicingCol.out
</td></tr></table>

*/

}

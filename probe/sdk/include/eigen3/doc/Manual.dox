
// This file strutures pages and modules into a convenient hierarchical structure.

namespace Eigen {

/** \page UserManual_CustomizingEigen Extending/Customizing Eigen
  %Eigen can be extended in several ways, for instance, by defining global methods, by inserting custom methods within main %Eigen's classes through the \ref TopicCustomizing_Plugins "plugin" mechanism, by adding support to \ref TopicCustomizing_CustomScalar "custom scalar types" etc. See below for the respective sub-topics.
  - \subpage TopicCustomizing_Plugins
  - \subpage TopicCustomizing_InheritingMatrix
  - \subpage TopicCustomizing_CustomScalar
  - \subpage TopicCustomizing_NullaryExpr
  - \subpage TopicNewExpressionType
  \sa \ref TopicPreprocessorDirectives
*/


/** \page UserManual_Generalities General topics
  - \subpage Eigen2ToEigen3
  - \subpage TopicFunctionTakingEigenTypes
  - \subpage TopicPreprocessorDirectives
  - \subpage TopicAssertions
  - \subpage TopicMultiThreading
  - \subpage TopicUsingBlasLapack
  - \subpage TopicUsingIntelMKL
  - \subpage TopicCUDA
  - \subpage TopicPitfalls
  - \subpage TopicTemplateKeyword
  - \subpage UserManual_UnderstandingEigen
  - \subpage TopicCMakeGuide
*/

/** \page UserManual_UnderstandingEigen Understanding Eigen
  - \subpage TopicInsideEigenExample
  - \subpage TopicClassHierarchy
  - \subpage TopicLazyEvaluation
*/

/** \page UnclassifiedPages Unclassified pages
  - \subpage TopicResizing
  - \subpage TopicVectorization
  - \subpage TopicEigenExpressionTemplates
  - \subpage TopicScalarTypes
  - \subpage GettingStarted
  - \subpage TutorialSparse_example_details
  - \subpage TopicWritingEfficientProductExpression
  - \subpage Experimental
*/


/** \defgroup Support_modules Support modules
  * Category of modules which add support for external libraries.
  */


/** \defgroup DenseMatrixManipulation_chapter Dense matrix and array manipulation */
/** \defgroup DenseMatrixManipulation_Alignement Alignment issues */
/** \defgroup DenseMatrixManipulation_Reference Reference */

/** \addtogroup TutorialMatrixClass
    \ingroup DenseMatrixManipulation_chapter */
/** \addtogroup TutorialMatrixArithmetic
    \ingroup DenseMatrixManipulation_chapter */
/** \addtogroup TutorialArrayClass
    \ingroup DenseMatrixManipulation_chapter */
/** \addtogroup TutorialBlockOperations
    \ingroup DenseMatrixManipulation_chapter */
/** \addtogroup TutorialAdvancedInitialization
    \ingroup DenseMatrixManipulation_chapter */
/** \addtogroup TutorialReductionsVisitorsBroadcasting
    \ingroup DenseMatrixManipulation_chapter */
/** \addtogroup TutorialMapClass
    \ingroup DenseMatrixManipulation_chapter */
/** \addtogroup TutorialReshapeSlicing
    \ingroup DenseMatrixManipulation_chapter */
/** \addtogroup TopicAliasing
    \ingroup DenseMatrixManipulation_chapter */
/** \addtogroup TopicStorageOrders
    \ingroup DenseMatrixManipulation_chapter */
    
/** \addtogroup DenseMatrixManipulation_Alignement
    \ingroup DenseMatrixManipulation_chapter */
/** \addtogroup TopicUnalignedArrayAssert
    \ingroup DenseMatrixManipulation_Alignement */
/** \addtogroup TopicFixedSizeVectorizable
    \ingroup DenseMatrixManipulation_Alignement */
/** \addtogroup TopicStructHavingEigenMembers
    \ingroup DenseMatrixManipulation_Alignement */
/** \addtogroup TopicStlContainers
    \ingroup DenseMatrixManipulation_Alignement */
/** \addtogroup TopicPassingByValue
    \ingroup DenseMatrixManipulation_Alignement */
/** \addtogroup TopicWrongStackAlignment
    \ingroup DenseMatrixManipulation_Alignement */
    
/** \addtogroup DenseMatrixManipulation_Reference
    \ingroup DenseMatrixManipulation_chapter */
/** \addtogroup Core_Module
    \ingroup DenseMatrixManipulation_Reference */  
/** \addtogroup Jacobi_Module
    \ingroup DenseMatrixManipulation_Reference */ 
/** \addtogroup Householder_Module
    \ingroup DenseMatrixManipulation_Reference */ 

/** \addtogroup CoeffwiseMathFunctions
    \ingroup DenseMatrixManipulation_chapter */

/** \addtogroup QuickRefPage
    \ingroup DenseMatrixManipulation_chapter */


/** \defgroup DenseLinearSolvers_chapter Dense linear problems and decompositions */
/** \defgroup DenseLinearSolvers_Reference Reference */

/** \addtogroup TutorialLinearAlgebra
    \ingroup DenseLinearSolvers_chapter */
/** \addtogroup TopicLinearAlgebraDecompositions
    \ingroup DenseLinearSolvers_chapter */
/** \addtogroup LeastSquares 
    \ingroup DenseLinearSolvers_chapter */
/** \addtogroup InplaceDecomposition
    \ingroup DenseLinearSolvers_chapter */
/** \addtogroup DenseDecompositionBenchmark
    \ingroup DenseLinearSolvers_chapter */

/** \addtogroup DenseLinearSolvers_Reference
    \ingroup DenseLinearSolvers_chapter */
/** \addtogroup Cholesky_Module
    \ingroup DenseLinearSolvers_Reference */
/** \addtogroup LU_Module
    \ingroup DenseLinearSolvers_Reference */
/** \addtogroup QR_Module
    \ingroup DenseLinearSolvers_Reference */
/** \addtogroup SVD_Module
    \ingroup DenseLinearSolvers_Reference*/
/** \addtogroup Eigenvalues_Module
    \ingroup DenseLinearSolvers_Reference */




/** \defgroup Sparse_chapter Sparse linear algebra */
/** \defgroup Sparse_Reference Reference */

/** \addtogroup TutorialSparse
    \ingroup Sparse_chapter */
/** \addtogroup TopicSparseSystems
    \ingroup Sparse_chapter */
/** \addtogroup MatrixfreeSolverExample
    \ingroup Sparse_chapter */

/** \addtogroup Sparse_Reference
    \ingroup Sparse_chapter */
/** \addtogroup SparseCore_Module
    \ingroup Sparse_Reference */
/** \addtogroup OrderingMethods_Module
    \ingroup Sparse_Reference */
/** \addtogroup SparseCholesky_Module
    \ingroup Sparse_Reference */
/** \addtogroup SparseLU_Module
    \ingroup Sparse_Reference */
/** \addtogroup SparseQR_Module
    \ingroup Sparse_Reference */
/** \addtogroup IterativeLinearSolvers_Module
    \ingroup Sparse_Reference */
/** \addtogroup Sparse_Module
    \ingroup Sparse_Reference */
/** \addtogroup Support_modules
    \ingroup Sparse_Reference */    

/** \addtogroup SparseQuickRefPage
    \ingroup Sparse_chapter */


/** \defgroup Geometry_chapter Geometry */
/** \defgroup Geometry_Reference Reference */

/** \addtogroup TutorialGeometry
    \ingroup Geometry_chapter */
    
/** \addtogroup Geometry_Reference
    \ingroup Geometry_chapter */
/** \addtogroup Geometry_Module
    \ingroup Geometry_Reference */
/** \addtogroup Splines_Module
    \ingroup Geometry_Reference */

/** \internal \brief Namespace containing low-level routines from the %Eigen library. */
namespace internal {}
}

namespace Eigen {

/** \eigenManualPage CoeffwiseMathFunctions Catalog of coefficient-wise math functions


<!-- <span style="font-size:300%; color:red; font-weight: 900;">!WORK IN PROGRESS!</span> -->

This table presents a catalog of the coefficient-wise math functions supported by %Eigen.
In this table, \c a, \c b, refer to Array objects or expressions, and \c m refers to a linear algebra Matrix/Vector object. Standard scalar types are abbreviated as follows:
  - \c int: \c i32
  - \c float: \c f
  - \c double: \c d
  - \c std::complex<float>: \c cf
  - \c std::complex<double>: \c cd

For each row, the first column list the equivalent calls for arrays, and matrices when supported. Of course, all functions are available for matrices by first casting it as an array: \c m.array().

The third column gives some hints in the underlying scalar implementation. In most cases, %Eigen does not implement itself the math function but relies on the STL for standard scalar types, or user-provided functions for custom scalar types.
For instance, some simply calls the respective function of the STL while preserving <a href="http://en.cppreference.com/w/cpp/language/adl">argument-dependent lookup</a> for custom types.
The following:
\code
using std::foo;
foo(a[i]);
\endcode
means that the STL's function \c std::foo will be potentially called if it is compatible with the underlying scalar type. If not, then the user must ensure that an overload of the function foo is available for the given scalar type (usually defined in the same namespace as the given scalar type).
This also means that, unless specified, if the function \c std::foo is available only in some recent c++ versions (e.g., c++11), then the respective %Eigen's function/method will be usable on standard types only if the compiler support the required c++ version.

<table class="manual-hl">
<tr>
<th>API</th><th>Description</th><th>Default scalar implementation</th><th>SIMD</th>
</tr>
<tr><td colspan="4"></td></tr>
<tr><th colspan="4">Basic operations</th></tr>
<tr>
  <td class="code">
  \anchor cwisetable_abs
  a.\link ArrayBase::abs abs\endlink(); \n
  \link Eigen::abs abs\endlink(a); \n
  m.\link MatrixBase::cwiseAbs cwiseAbs\endlink();
  </td>
  <td>absolute value (\f$ |a_i| \f$) </td>
  <td class="code">
  using <a href="http://en.cppreference.com/w/cpp/numeric/math/fabs">std::abs</a>; \n
  abs(a[i]);
  </td>
  <td>SSE2, AVX (i32,f,d)</td>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_inverse
  a.\link ArrayBase::inverse inverse\endlink(); \n
  \link Eigen::inverse inverse\endlink(a); \n
  m.\link MatrixBase::cwiseInverse cwiseInverse\endlink();
  </td>
  <td>inverse value (\f$ 1/a_i \f$) </td>
  <td class="code">
  1/a[i];
  </td>
  <td>All engines (f,d,fc,fd)</td>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_conj
  a.\link ArrayBase::conjugate conjugate\endlink(); \n
  \link Eigen::conj conj\endlink(a); \n
  m.\link MatrixBase::conjugate conjugate();
  </td>
  <td><a href="https://en.wikipedia.org/wiki/Complex_conjugate">complex conjugate</a> (\f$ \bar{a_i} \f$),\n
  no-op for real </td>
  <td class="code">
  using <a href="http://en.cppreference.com/w/cpp/numeric/complex/conj">std::conj</a>; \n
  conj(a[i]);
  </td>
  <td>All engines (fc,fd)</td>
</tr>
<tr>
<th colspan="4">Exponential functions</th>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_exp
  a.\link ArrayBase::exp exp\endlink(); \n
  \link Eigen::exp exp\endlink(a);
  </td>
  <td>\f$ e \f$ raised to the given power (\f$ e^{a_i} \f$) </td>
  <td class="code">
  using <a href="http://en.cppreference.com/w/cpp/numeric/math/exp">std::exp</a>; \n
  exp(a[i]);
  </td>
  <td>SSE2, AVX (f,d)</td>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_log
  a.\link ArrayBase::log log\endlink(); \n
  \link Eigen::log log\endlink(a);
  </td>
  <td>natural (base \f$ e \f$) logarithm (\f$ \ln({a_i}) \f$)</td>
  <td class="code">
  using <a href="http://en.cppreference.com/w/cpp/numeric/math/log">std::log</a>; \n
  log(a[i]);
  </td>
  <td>SSE2, AVX (f)</td>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_log1p
  a.\link ArrayBase::log1p log1p\endlink(); \n
  \link Eigen::log1p log1p\endlink(a);
  </td>
  <td>natural (base \f$ e \f$) logarithm of 1 plus \n the given number (\f$ \ln({1+a_i}) \f$)</td>
  <td>built-in generic implementation based on \c log,\n
  plus \c using <a href="http://en.cppreference.com/w/cpp/numeric/math/log1p">\c std::log1p </a>; \cpp11</td>
  <td></td>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_log10
  a.\link ArrayBase::log10 log10\endlink(); \n
  \link Eigen::log10 log10\endlink(a);
  </td>
  <td>base 10 logarithm (\f$ \log_{10}({a_i}) \f$)</td>
  <td class="code">
  using <a href="http://en.cppreference.com/w/cpp/numeric/math/log10">std::log10</a>; \n
  log10(a[i]);
  </td>
  <td></td>
</tr>
<tr>
<th colspan="4">Power functions</th>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_pow
  a.\link ArrayBase::pow pow\endlink(b); \n
  \link Eigen::pow pow\endlink(a,b);
  </td>
  <td>raises a number to the given power (\f$ a_i ^ {b_i} \f$) \n \c a and \c b can be either an array or scalar.</td>
  <td class="code">
  using <a href="http://en.cppreference.com/w/cpp/numeric/math/pow">std::pow</a>; \n
  pow(a[i],b[i]);\n
  (plus builtin for integer types)</td>
  <td></td>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_sqrt
  a.\link ArrayBase::sqrt sqrt\endlink(); \n
  \link Eigen::sqrt sqrt\endlink(a);\n
  m.\link MatrixBase::cwiseSqrt cwiseSqrt\endlink();
  </td>
  <td>computes square root (\f$ \sqrt a_i \f$)</td>
  <td class="code">
  using <a href="http://en.cppreference.com/w/cpp/numeric/math/sqrt">std::sqrt</a>; \n
  sqrt(a[i]);</td>
  <td>SSE2, AVX (f,d)</td>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_rsqrt
  a.\link ArrayBase::rsqrt rsqrt\endlink(); \n
  \link Eigen::rsqrt rsqrt\endlink(a);
  </td>
  <td><a href="https://en.wikipedia.org/wiki/Fast_inverse_square_root">reciprocal square root</a> (\f$ 1/{\sqrt a_i} \f$)</td>
  <td class="code">
  using <a href="http://en.cppreference.com/w/cpp/numeric/math/sqrt">std::sqrt</a>; \n
  1/sqrt(a[i]); \n
  </td>
  <td>SSE2, AVX, AltiVec, ZVector (f,d)\n
  (approx + 1 Newton iteration)</td>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_square
  a.\link ArrayBase::square square\endlink(); \n
  \link Eigen::square square\endlink(a);
  </td>
  <td>computes square power (\f$ a_i^2 \f$)</td>
  <td class="code">
  a[i]*a[i]</td>
  <td>All (i32,f,d,cf,cd)</td>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_cube
  a.\link ArrayBase::cube cube\endlink(); \n
  \link Eigen::cube cube\endlink(a);
  </td>
  <td>computes cubic power (\f$ a_i^3 \f$)</td>
  <td class="code">
  a[i]*a[i]*a[i]</td>
  <td>All (i32,f,d,cf,cd)</td>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_abs2
  a.\link ArrayBase::abs2 abs2\endlink(); \n
  \link Eigen::abs2 abs2\endlink(a);\n
  m.\link MatrixBase::cwiseAbs2 cwiseAbs2\endlink();
  </td>
  <td>computes the squared absolute value (\f$ |a_i|^2 \f$)</td>
  <td class="code">
  real:    a[i]*a[i] \n
  complex:  real(a[i])*real(a[i]) \n
  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; + imag(a[i])*imag(a[i])</td>
  <td>All (i32,f,d)</td>
</tr>
<tr>
<th colspan="4">Trigonometric functions</th>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_sin
  a.\link ArrayBase::sin sin\endlink(); \n
  \link Eigen::sin sin\endlink(a);
  </td>
  <td>computes sine</td>
  <td class="code">
  using <a href="http://en.cppreference.com/w/cpp/numeric/math/sin">std::sin</a>; \n
  sin(a[i]);</td>
  <td>SSE2, AVX (f)</td>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_cos
  a.\link ArrayBase::cos cos\endlink(); \n
  \link Eigen::cos cos\endlink(a);
  </td>
  <td>computes cosine</td>
  <td class="code">
  using <a href="http://en.cppreference.com/w/cpp/numeric/math/cos">std::cos</a>; \n
  cos(a[i]);</td>
  <td>SSE2, AVX (f)</td>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_tan
  a.\link ArrayBase::tan tan\endlink(); \n
  \link Eigen::tan tan\endlink(a);
  </td>
  <td>computes tangent</td>
  <td class="code">
  using <a href="http://en.cppreference.com/w/cpp/numeric/math/tan">std::tan</a>; \n
  tan(a[i]);</td>
  <td></td>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_asin
  a.\link ArrayBase::asin asin\endlink(); \n
  \link Eigen::asin asin\endlink(a);
  </td>
  <td>computes arc sine (\f$ \sin^{-1} a_i \f$)</td>
  <td class="code">
  using <a href="http://en.cppreference.com/w/cpp/numeric/math/asin">std::asin</a>; \n
  asin(a[i]);</td>
  <td></td>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_acos
  a.\link ArrayBase::acos acos\endlink(); \n
  \link Eigen::acos acos\endlink(a);
  </td>
  <td>computes arc cosine  (\f$ \cos^{-1} a_i \f$)</td>
  <td class="code">
  using <a href="http://en.cppreference.com/w/cpp/numeric/math/acos">std::acos</a>; \n
  acos(a[i]);</td>
  <td></td>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_atan
  a.\link ArrayBase::atan tan\endlink(); \n
  \link Eigen::atan atan\endlink(a);
  </td>
  <td>computes arc tangent (\f$ \tan^{-1} a_i \f$)</td>
  <td class="code">
  using <a href="http://en.cppreference.com/w/cpp/numeric/math/atan">std::atan</a>; \n
  atan(a[i]);</td>
  <td></td>
</tr>
<tr>
<th colspan="4">Hyperbolic functions</th>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_sinh
  a.\link ArrayBase::sinh sinh\endlink(); \n
  \link Eigen::sinh sinh\endlink(a);
  </td>
  <td>computes hyperbolic sine</td>
  <td class="code">
  using <a href="http://en.cppreference.com/w/cpp/numeric/math/sinh">std::sinh</a>; \n
  sinh(a[i]);</td>
  <td></td>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_cosh
  a.\link ArrayBase::cosh cohs\endlink(); \n
  \link Eigen::cosh cosh\endlink(a);
  </td>
  <td>computes hyperbolic cosine</td>
  <td class="code">
  using <a href="http://en.cppreference.com/w/cpp/numeric/math/cosh">std::cosh</a>; \n
  cosh(a[i]);</td>
  <td></td>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_tanh
  a.\link ArrayBase::tanh tanh\endlink(); \n
  \link Eigen::tanh tanh\endlink(a);
  </td>
  <td>computes hyperbolic tangent</td>
  <td class="code">
  using <a href="http://en.cppreference.com/w/cpp/numeric/math/tanh">std::tanh</a>; \n
  tanh(a[i]);</td>
  <td></td>
</tr>
<tr>
<th colspan="4">Nearest integer floating point operations</th>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_ceil
  a.\link ArrayBase::ceil ceil\endlink(); \n
  \link Eigen::ceil ceil\endlink(a);
  </td>
  <td>nearest integer not less than the given value</td>
  <td class="code">
  using <a href="http://en.cppreference.com/w/cpp/numeric/math/ceil">std::ceil</a>; \n
  ceil(a[i]);</td>
  <td>SSE4,AVX,ZVector (f,d)</td>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_floor
  a.\link ArrayBase::floor floor\endlink(); \n
  \link Eigen::floor floor\endlink(a);
  </td>
  <td>nearest integer not greater than the given value</td>
  <td class="code">
  using <a href="http://en.cppreference.com/w/cpp/numeric/math/floor">std::floor</a>; \n
  floor(a[i]);</td>
  <td>SSE4,AVX,ZVector (f,d)</td>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_round
  a.\link ArrayBase::round round\endlink(); \n
  \link Eigen::round round\endlink(a);
  </td>
  <td>nearest integer, \n rounding away from zero in halfway cases</td>
  <td>built-in generic implementation \n based on \c floor and \c ceil,\n
  plus \c using <a href="http://en.cppreference.com/w/cpp/numeric/math/round">\c std::round </a>; \cpp11</td>
  <td>SSE4,AVX,ZVector (f,d)</td>
</tr>
<tr>
<th colspan="4">Floating point manipulation functions</th>
</tr>
<tr>
<th colspan="4">Classification and comparison</th>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_isfinite
  a.\link ArrayBase::isFinite isFinite\endlink(); \n
  \link Eigen::isfinite isfinite\endlink(a);
  </td>
  <td>checks if the given number has finite value</td>
  <td>built-in generic implementation,\n
  plus \c using <a href="http://en.cppreference.com/w/cpp/numeric/math/isfinite">\c std::isfinite </a>; \cpp11</td>
  <td></td>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_isinf
  a.\link ArrayBase::isInf isInf\endlink(); \n
  \link Eigen::isinf isinf\endlink(a);
  </td>
  <td>checks if the given number is infinite</td>
  <td>built-in generic implementation,\n
  plus \c using <a href="http://en.cppreference.com/w/cpp/numeric/math/isinf">\c std::isinf </a>; \cpp11</td>
  <td></td>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_isnan
  a.\link ArrayBase::isNaN isNaN\endlink(); \n
  \link Eigen::isnan isnan\endlink(a);
  </td>
  <td>checks if the given number is not a number</td>
  <td>built-in generic implementation,\n
  plus \c using <a href="http://en.cppreference.com/w/cpp/numeric/math/isnan">\c std::isnan </a>; \cpp11</td>
  <td></td>
</tr>
<tr>
<th colspan="4">Error and gamma functions</th>
</tr>
<tr> <td colspan="4">  Require \c \#include \c <unsupported/Eigen/SpecialFunctions> </td></tr>
<tr>
  <td class="code">
  \anchor cwisetable_erf
  a.\link ArrayBase::erf erf\endlink(); \n
  \link Eigen::erf erf\endlink(a);
  </td>
  <td>error function</td>
  <td class="code">
  using <a href="http://en.cppreference.com/w/cpp/numeric/math/erf">std::erf</a>; \cpp11 \n
  erf(a[i]);
  </td>
  <td></td>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_erfc
  a.\link ArrayBase::erfc erfc\endlink(); \n
  \link Eigen::erfc erfc\endlink(a);
  </td>
  <td>complementary error function</td>
  <td class="code">
  using <a href="http://en.cppreference.com/w/cpp/numeric/math/erfc">std::erfc</a>; \cpp11 \n
  erfc(a[i]);
  </td>
  <td></td>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_lgamma
  a.\link ArrayBase::lgamma lgamma\endlink(); \n
  \link Eigen::lgamma lgamma\endlink(a);
  </td>
  <td>natural logarithm of the gamma function</td>
  <td class="code">
  using <a href="http://en.cppreference.com/w/cpp/numeric/math/lgamma">std::lgamma</a>; \cpp11 \n
  lgamma(a[i]);
  </td>
  <td></td>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_digamma
  a.\link ArrayBase::digamma digamma\endlink(); \n
  \link Eigen::digamma digamma\endlink(a);
  </td>
  <td><a href="https://en.wikipedia.org/wiki/Digamma_function">logarithmic derivative of the gamma function</a></td>
  <td>
  built-in for float and double
  </td>
  <td></td>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_igamma
  \link Eigen::igamma igamma\endlink(a,x);
  </td>
  <td><a href="https://en.wikipedia.org/wiki/Incomplete_gamma_function">lower incomplete gamma integral</a>
  \n \f$ \gamma(a_i,x_i)= \frac{1}{|a_i|} \int_{0}^{x_i}e^{\text{-}t} t^{a_i-1} \mathrm{d} t \f$</td>
  <td>
  built-in for float and double,\n but requires \cpp11
  </td>
  <td></td>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_igammac
  \link Eigen::igammac igammac\endlink(a,x);
  </td>
  <td><a href="https://en.wikipedia.org/wiki/Incomplete_gamma_function">upper incomplete gamma integral</a>
  \n \f$ \Gamma(a_i,x_i) = \frac{1}{|a_i|} \int_{x_i}^{\infty}e^{\text{-}t} t^{a_i-1} \mathrm{d} t \f$</td>
  <td>
  built-in for float and double,\n but requires \cpp11
  </td>
  <td></td>
</tr>
<tr>
<th colspan="4">Special functions</th>
</tr>
<tr> <td colspan="4">  Require \c \#include \c <unsupported/Eigen/SpecialFunctions> </td></tr>
<tr>
  <td class="code">
  \anchor cwisetable_polygamma
  \link Eigen::polygamma polygamma\endlink(n,x);
  </td>
  <td><a href="https://en.wikipedia.org/wiki/Polygamma_function">n-th derivative of digamma at x</a></td>
  <td>
  built-in generic based on\n <a href="#cwisetable_lgamma">\c lgamma </a>,
  <a href="#cwisetable_digamma"> \c digamma </a>
  and <a href="#cwisetable_zeta">\c zeta </a>.
  </td>
  <td></td>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_betainc
  \link Eigen::betainc betainc\endlink(a,b,x);
  </td>
  <td><a href="https://en.wikipedia.org/wiki/Beta_function#Incomplete_beta_function">Incomplete beta function</a></td>
  <td>
  built-in for float and double,\n but requires \cpp11
  </td>
  <td></td>
</tr>
<tr>
  <td class="code">
  \anchor cwisetable_zeta
  \link Eigen::zeta zeta\endlink(a,b);
  </td>
  <td><a href="https://en.wikipedia.org/wiki/Hurwitz_zeta_function">Hurwitz zeta function</a>
  \n \f$ \zeta(a_i,b_i)=\sum_{k=0}^{\infty}(b_i+k)^{\text{-}a_i} \f$</td>
  <td>
  built-in for float and double
  </td>
  <td></td>
</tr>
<tr><td colspan="4"></td></tr>
</table>

\n

*/

}

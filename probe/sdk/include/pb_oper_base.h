
#ifndef PB_OPER_BASE_H
#define PB_OPER_BASE_H 
#include <list>
#include <string>
#include <stdint.h>
#include <google/protobuf/message.h>

using namespace std;
using namespace ::google::protobuf;

#define PB_ERROR_CODE_START (-2000)
#define DATASOURCEFILEDNAME "Z002003"
#define MSGTYPECEFILEDNAME "H000002"
#define DATA_UNIQUE_ID     "Z002001"
#define DEVICE_ID           "Z002006"
#define DATA_COL_LOCATION_ID    "F010008"
#define PB_SPLITDATA_STR       "^@#$"
typedef enum
{
    PB_FIELD_PROTO_REQUIRED,	//required
    PB_FIELD_PROTO_OPTIONAL,	//optional
    PB_FIELD_PROTO_REPEATED, //repeated
    PB_FIELD_PROTO_INVALID,		
}PB_FIELD_PROTO;

typedef enum 
{
    PB_FIELD_TYPE_INT32       = 1,     // TYPE_INT32, TYPE_SINT32, TYPE_SFIXED32
    PB_FIELD_TYPE_INT64       = 2,     // TYPE_INT64, TYPE_SINT64, TYPE_SFIXED64
    PB_FIELD_TYPE_UINT32      = 3,     // TYPE_UINT32, TYPE_FIXED32
    PB_FIELD_TYPE_UINT64      = 4,     // TYPE_UINT64, TYPE_FIXED64
    PB_FIELD_TYPE_DOUBLE      = 5,     // TYPE_DOUBLE
    PB_FIELD_TYPE_FLOAT       = 6,     // TYPE_FLOAT
    PB_FIELD_TYPE_BOOL        = 7,     // TYPE_BOOL
    PB_FIELD_TYPE_ENUM        = 8,     // TYPE_ENUM
    PB_FIELD_TYPE_STRING      = 9,     // TYPE_STRING, TYPE_BYTES
    PB_FIELD_TYPE_MESSAGE     = 10,    // TYPE_MESSAGE, TYPE_GROUP
    PB_FIELD_TYPE_INVALID,
}PB_FIELD_TYPE;

typedef enum
{
    PB_SUCCESSFUL = 0,
    PB_ERR_INVALID_PARAM 	= (PB_ERROR_CODE_START + 1), 	
    PB_ERR_NOT_FIND 		= (PB_ERROR_CODE_START + 2),	
    PB_ERR_UNSUPPORT_TYPE 	= (PB_ERROR_CODE_START + 3),
    PB_ERR_SERIALIZE_FAILED = (PB_ERROR_CODE_START + 4),
}PB_ERROR_CODE;

//typedef int (*ParseFromOther)(void* srcMsg, void *destMsg);
typedef string (*ptr_get_fieldname_by_type)(int);
#ifndef c_data_handle_base
class c_data_handle_base ;
#endif
class pb_oper_base
{
    public:
        pb_oper_base()
        {
            b_handle_num = 0;
            b_stop = false;
            b_biscard = false;
            b_repeat = false;
        }
        virtual pb_oper_base* pb_oper_base_const()
        {
        }

        virtual ~pb_oper_base()
        {
        }
        void set_pbvalaue(std::string filed_name ,std::string svalue )
        {
            PB_FIELD_TYPE type = getFieldType(filed_name);
            switch(type)
            {
                case PB_FIELD_TYPE_INT32:

                    this -> setFieldValue(filed_name , atoi(svalue.c_str()));
                    break ;
                case PB_FIELD_TYPE_INT64:
                    this -> setFieldValue(filed_name , (int64_t)atoll(svalue.c_str()));
                    break;
                case PB_FIELD_TYPE_UINT32:
                    this -> setFieldValue(filed_name , (uint32_t)atoi(svalue.c_str()));

                    break;
                case PB_FIELD_TYPE_UINT64:
                    this -> setFieldValue(filed_name , (uint64_t)atoll(svalue.c_str()));
                    break;
                case PB_FIELD_TYPE_DOUBLE:
                    this -> setFieldValue(filed_name , strtod(svalue.c_str(), NULL));
                    break;
                case PB_FIELD_TYPE_FLOAT :
                    this -> setFieldValue(filed_name , strtof(svalue.c_str(), NULL));
                    break;
                case PB_FIELD_TYPE_BOOL:
                    if(svalue == "true")
                    {
                        this -> setFieldValue(filed_name , true);
                    }
                    else 
                    {
                        this -> setFieldValue(filed_name , false);
                    }
                    break;
                case PB_FIELD_TYPE_STRING:
                    this -> setFieldValue(filed_name , svalue );
                    break;

            }; 

        }


        //消息赋值
        //将反序列化后的msg填到pb中.
        virtual PB_ERROR_CODE set_msg(Message* srcMsg) = 0;
        virtual PB_ERROR_CODE parse_from_array(char *data, int len) = 0;
        //获取消息的总大小
        virtual int byte_size() = 0;
        //序列化消息
        virtual PB_ERROR_CODE serialize_to_array(char *buffer, int len) = 0;

        //获取消息的type值
        virtual int getMsgType() = 0;

        //获取字段的属性 optional/required/repeated
        virtual PB_FIELD_PROTO getFieldProto(string fieldname) = 0;

        //获取字段的类型  INT32/INT64/string...
        virtual PB_FIELD_TYPE  getFieldType(string fieldname) = 0;

        //新创建一个msg
        virtual Message* allocateMsg(void) = 0;
        //释放创建的msg
        virtual void releaseMsg(Message* pmsg) = 0;

        //pb拷贝函数
        virtual pb_oper_base* clone() = 0;
        virtual void data_init() = 0;

        //设置回调函数
        //该函数需要实现的功能是根据传入type值，
        //然后返回该type值对应的协议在proto buffer b中的字段名称.
        //在不设置此回调的情况下，默认采用"msg"名称，
        //对于某些协议将出现查找不到字段的情况。
        virtual void set_func(ptr_get_fieldname_by_type func) = 0;

        //获取字段的值，什么类型的值都转换为string
        virtual string get_filed_value_string(std::string fieldname) = 0; 

        virtual bool      get_value_bool(std::string fieldname) = 0 ;
        virtual int       get_value_int(std::string fieldname) = 0 ;
        virtual int64_t   get_value_int64(std::string fieldname) = 0 ;
        virtual uint32_t  get_value_uint32(std::string fieldname) = 0 ;
        virtual uint64_t  get_value_uint64(std::string fieldname) = 0 ;
        virtual string    get_value_string(std::string fieldname) = 0 ;
        virtual double    get_value_double(std::string fieldname) = 0 ;


        //获取字段的值，不支持repeated属性的字段：否则返回PB_ERR_UNSUPPORT_TYPE
        //由用户来保证存储数据变量的类型和其应有类型保持一致。
        //ex1:  Info f;
        //		getFieldValue("info", (void*)&f);
        //ex2:	float fl;
        //		getFieldValue("floatField", (void*)&fl);
        //获取字段的值，非repeated属性的字段不要传入index参数，
        //同理，repeated属性的字段一定要传入index参数，否则
        //否则将返回PB_ERR_UNSUPPORT_TYPE
        //string fieldname = "portid";
        //ex1:	int portid;
        //		getFieldValue(fieldname, portid);
        //ex2:	MessageLite *personmsg = NULL;
        //		fieldname = "person";
        //		getFieldValue(fieldname, personmsg);
        //
        virtual PB_ERROR_CODE getFieldValue(string fieldname, int32_t  &value, int index=-1) = 0;
        virtual PB_ERROR_CODE getFieldValue(string fieldname, int64_t  &value, int index=-1) = 0;
        virtual PB_ERROR_CODE getFieldValue(string fieldname, uint32_t &value, int index=-1) = 0;
        virtual PB_ERROR_CODE getFieldValue(string fieldname, uint64_t &value, int index=-1) = 0;
        virtual PB_ERROR_CODE getFieldValue(string fieldname, double   &value, int index=-1) = 0;
        virtual PB_ERROR_CODE getFieldValue(string fieldname, float    &value, int index=-1) = 0;
        virtual PB_ERROR_CODE getFieldValue(string fieldname, bool     &value, int index=-1) = 0;
        virtual PB_ERROR_CODE getFieldValue(string fieldname, string   &value, int index=-1) = 0;
        //获得该字段的原始句柄
        virtual PB_ERROR_CODE getFieldValue(string fieldname, Message* &value, int index=-1) = 0;

        //修改字段的值，非repeated属性的字段不要传入index参数，
        //同理，repeated属性的字段一定要传入index参数，否则
        //否则将返回PB_ERR_UNSUPPORT_TYPE
        //string fieldname = "portid";
        //ex1:	int portid;
        //		setFieldValue(fieldname, portid);
        //ex2:	MessageLite personmsg;
        //		personmsg.age = newage;
        //		fieldname = "person";
        //		setFieldValue(fieldname, personmsg);
        virtual PB_ERROR_CODE setFieldValue(string fieldname, int32_t  value, int index=-1) = 0;
        virtual PB_ERROR_CODE setFieldValue(string fieldname, int64_t  value, int index=-1) = 0;
        virtual PB_ERROR_CODE setFieldValue(string fieldname, uint32_t value, int index=-1) = 0;
        virtual PB_ERROR_CODE setFieldValue(string fieldname, uint64_t value, int index=-1) = 0;
        virtual PB_ERROR_CODE setFieldValue(string fieldname, double   value, int index=-1) = 0;
        virtual PB_ERROR_CODE setFieldValue(string fieldname, float    value, int index=-1) = 0;
        virtual PB_ERROR_CODE setFieldValue(string fieldname, bool     value, int index=-1) = 0;
        virtual PB_ERROR_CODE setFieldValue(string fieldname, char*    value, int index=-1) = 0;
        virtual PB_ERROR_CODE setFieldValue(string fieldname, string   &value, int index=-1) = 0;
        virtual PB_ERROR_CODE setFieldValue(string fieldname, Message *value, int index=-1) = 0;

        //获取repeated属性字段值的个数
        //当传入的字段名不是repeated属性时，将返回PB_ERR_UNSUPPORT_TYPE
        virtual PB_ERROR_CODE getFieldListCount(string fieldname, int &len) = 0;

        //向repeated字段中增加一条值
        virtual PB_ERROR_CODE addFieldListValue(string fieldname, int32_t  value) = 0;
        virtual PB_ERROR_CODE addFieldListValue(string fieldname, int64_t  value) = 0;
        virtual PB_ERROR_CODE addFieldListValue(string fieldname, uint32_t value) = 0;
        virtual PB_ERROR_CODE addFieldListValue(string fieldname, uint64_t value) = 0;
        virtual PB_ERROR_CODE addFieldListValue(string fieldname, double   value) = 0;
        virtual PB_ERROR_CODE addFieldListValue(string fieldname, float    value) = 0;
        virtual PB_ERROR_CODE addFieldListValue(string fieldname, bool     value) = 0;
        virtual PB_ERROR_CODE addFieldListValue(string fieldname, string   &value) = 0;
        virtual PB_ERROR_CODE addFieldListValue(string fieldname, Message *value) = 0;

        //将repeated字段中原有值删除，并增加新值value
        virtual PB_ERROR_CODE setFieldListValue(string fieldname, int32_t  value) = 0;
        virtual PB_ERROR_CODE setFieldListValue(string fieldname, int64_t  value) = 0;
        virtual PB_ERROR_CODE setFieldListValue(string fieldname, uint32_t value) = 0;
        virtual PB_ERROR_CODE setFieldListValue(string fieldname, uint64_t value) = 0;
        virtual PB_ERROR_CODE setFieldListValue(string fieldname, double   value) = 0;
        virtual PB_ERROR_CODE setFieldListValue(string fieldname, float    value) = 0;
        virtual PB_ERROR_CODE setFieldListValue(string fieldname, bool     value) = 0;
        virtual PB_ERROR_CODE setFieldListValue(string fieldname, string   &value) = 0;
        virtual PB_ERROR_CODE setFieldListValue(string fieldname, Message *value) = 0;

        list<c_data_handle_base*>  data_handle_list;
        int   b_handle_num ; //
        bool b_stop ; // 是否继续做数据处理 
        bool b_biscard; // 数据是否丢弃，
        bool b_repeat ; // 是否是重复数据
};

typedef pb_oper_base *(*ptr_pb_attach)();
typedef void (*ptr_pb_detach)(pb_oper_base *);

//获取数据唯一id
extern std::string g_get_dataid();
#endif /*PB_OPER_BASE_H*/

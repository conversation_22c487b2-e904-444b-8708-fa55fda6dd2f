// Last Update:2018-04-16 14:40:58
/**
 * @file ac_rule.h
 * @brief : ac 匹配的结构集的存储和最后的结果统计
 * <AUTHOR>
 * @version 0.1.00
 * @date 2016-04-26
 */

#ifndef AC_RULE_H
#define AC_RULE_H
#include <map>
#include <list>
#include <set>
#include <string.h>
#include <stdint.h>
#include <string>

using namespace std;
class result_node
{
    public:
        result_node():sign(0), offise(0){}
    public :
        int sign ; // 标签数据 
        int offise ;  // 偏移位数 
        bool operator< (const result_node &ad) const 
        {
            return this->sign<ad.sign;
        }
        bool operator()(const result_node &a, const result_node &b) const 
        {
            return a.sign<b.sign;
        }
};
// 规则匹配计算模块的
/*class rule_map_node {
  };*/
// 结构确认链表 
class  rule_node
{
    public:
        rule_node();
        ~rule_node();
        void add_rule_node(int  sign);
        bool operator==(const rule_node &ps) 
        {
            list<int>::iterator iter = this->rule_list.begin();
            list<int>::const_iterator psiter = ps.rule_list.begin();
            for(;iter!=rule_list.end() && psiter != ps.rule_list.end(); )
            {
                if(*iter != *psiter) 
                {
                    return false;
                }
                iter ++;
                psiter++;
            }
            return true;
        }
        bool operator <(const rule_node &ps)  
        {
            std::list<int>::iterator iter = rule_list.begin();
            std::list<int>::const_iterator psiter = ps.rule_list.begin();
            for(;iter!=rule_list.end() && psiter != ps.rule_list.end(); )
            {
                if(*iter > *psiter) 
                {
                    return false;
                }
                iter ++;
                psiter++;
            }
            return true;
        }

        bool rule_cmp(map<int,list<result_node *> *> * p_result_set);
        list<result_node*> * get_result_node(map<int,list<result_node *> *> * p_result_set);
    private:
        list<int> rule_list ;
};
#define HEAD 1 
#define END  2 
#define DEY  3 
#define DAY  4 
#define XIY  5 
class rule_node_offist 
{
    public:
        rule_node_offist()
        {
            offise = -1;
            p_node = 0 ;
            sword = "";
            offise = 0;
            p_tmp = NULL;
        }
        rule_node_offist(int itype  , int ioffise ,rule_node * node ,std::string word )
        {
            type = itype ;
            offise = ioffise ;
            p_node = node ; 
            p_tmp = NULL;
            sword = word ;
        }

        
        bool operator==(const rule_node_offist &ps) const 
        {
            if(this->type == ps.type && offise == ps.offise && *p_node == *(ps.p_node))
            {
                return true;
            }
            return false ;
        }
        bool operator<(const rule_node_offist &ps) const 
        {
            if(this->type < ps.type  )
            {
                return true;
            }
            else if (this->type > ps.type  ) 
            {
                return false ;
            }
            else if( offise <  ps.offise  )
            {
                return true;
            }
            else if(  *p_node < *(ps.p_node))
            {
                return true ;
            }
            /*else if(*node  ==  *(ps.node) ) 
            {
                return  false;
            }*/
            return false ;
        }
        void rule_add_node( rule_node_offist * ps ) 
        {
            bool b_add  = false;
            std::list<rule_node_offist *>::iterator iter = children_node.begin();
            for(;iter != children_node.end(); iter ++)
            {
                if(*(*iter) == *ps)
                {
                    b_add = true;
                    std::list<rule_node_offist *>::iterator psiter = ps->children_node.begin();
                    for(;psiter != ps->children_node.end(); psiter ++)
                    {
                        (*iter) ->rule_add_node( *psiter);
                    }
                    return ;   

                }
            }
            if(b_add == false) 
            {
                children_node.push_back(ps);
            }
        }
        rule_node_offist(int itype  , int ioffise ,rule_node * node )
        {
            type = itype ;
            offise = ioffise ;
            p_node = node ; 
            p_tmp = NULL;
            sword = "";
            data_off = 0;
        }
        ~rule_node_offist()
        {
            std::list<rule_node_offist *>::iterator iter = children_node.begin();
            for(;iter != children_node.end(); iter ++)
            {
                delete (*iter) ;
            }
        }
        rule_node  *p_node ;
        result_node * p_tmp ;
        std::string  sword;
        int offise ;
        int type ;
        int data_off  ;
        uint32_t id ; // 返回
        std::list<rule_node_offist *> children_node;

        bool rule_cmp(map<int, list<result_node *> *> *p_result_map, int len)
        {
            if (rule_cmp_node(p_result_map, len) == false)
            {
                return false;
            }
            else if (children_node.empty())
            {
                return true;
            }
            else
            {
                std::list<rule_node_offist *> node_tmp(children_node);
                std::list<rule_node_offist *>::iterator iter = node_tmp.begin();
                for (; iter != node_tmp.end(); iter++)
                {
                    if ((*iter)->rule_cmp_node(p_result_map, len))
                    {
                        if ((*iter)->children_node.empty())
                        {
                            return true;
                        }
                        else
                        {
                            std::list<rule_node_offist *>::iterator iter_tmp = (*iter)->children_node.begin();
                            for (; iter_tmp != (*iter)->children_node.end(); iter_tmp++)
                            {
                                node_tmp.push_back(*iter_tmp);
                            }
                        }
                    }
                }
                return false;
            }
        }
        uint32_t  rule_cmp_uint32(map<int, list<result_node *> *> *p_result_map, int len)
        {
            if (rule_cmp_node(p_result_map, len) == false)
            {
                return  0;
            }
            else if (children_node.empty())
            {
                return 0;
            }
            else
            {
               std::list<rule_node_offist *> node_tmp(children_node);
                std::list<rule_node_offist *>::iterator iter = node_tmp.begin();
                for (; iter != node_tmp.end(); iter++)
                {
                    if ((*iter)->rule_cmp_node(p_result_map, len))
                    {
                        if ((*iter)->children_node.empty())
                        {
                            return (*iter) -> id;
                        }
                        else
                        {
                            std::list<rule_node_offist *>::iterator iter_tmp = (*iter)->children_node.begin();
                            for (; iter_tmp != (*iter)->children_node.end(); iter_tmp++)
                            {
                                node_tmp.push_back(*iter_tmp);
                            }
                        }
                    }
                }
                return 0 ;
            }
        }


        bool rule_cmp_node(map<int,list<result_node*> *> * p_result_map, int len)
        {
            if(offise == -2)
            {
                return true;
            }
            if(offise == -1) 
            {
                return p_node -> rule_cmp(p_result_map) ;
            }
            else
            {
                list<result_node*> *p = p_node -> get_result_node(p_result_map);
                if ( p != NULL)
                {
                    list<result_node*>::iterator iter = p->begin();
                    for (; iter != p->end(); iter++)
                    {
                        p_tmp = *iter;
                        if(p_tmp->offise == -1) 
                        {
                            return true;
                        }
                        switch(type)
                        {
                            case HEAD:
                                if(p_tmp->offise == 1) 
                                {
                                    return true;
                                }
                                break;
                            case END:
                                if(p_tmp->offise == len - 1 - offise) 
                                {
                                    return true;
                                }
                                break;
                            case DEY:
                                if(p_tmp->offise == offise) 
                                {
                                    return true;
                                }
                                break;

                            case DAY:
                                if(p_tmp->offise > offise) 
                                {
                                    return true;
                                }
                                break;
                            case XIY:
                                if(p_tmp->offise < offise) 
                                {
                                    return true;
                                }
                                break;
                        }
                    }
                    return false ;
                }
            }
            return false;
        }	
};

class rule
{
    public:
        rule();
        ~rule();
        void  clear();
        void add_result_node(int sign ) ;
        void add_result_node(int sign , int offise) ;
        map<int, list<result_node *> *>  * get_result_map();
    private:
        map<int, list<result_node *> *> result_map; 
        void add_result_node(result_node  p_result_node) ;
};
#endif  /*AC_RULE_H*/

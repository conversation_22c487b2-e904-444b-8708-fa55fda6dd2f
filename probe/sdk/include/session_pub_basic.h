#ifndef __SESSION_PUB_BASIC_H__
#define __SESSION_PUB_BASIC_H__

#include <stdint.h>
#include <inttypes.h>
#include <string>

#include "c_ip.h"

#define CONNECTINFORV4_BASIC_VERSION 20220530

#define MAX_SESSION_RULE_HIT 16

#define PACKETFROMUNKOWN 0
#define PACKETFROMCLIENT 1
#define PACKETFROMSERVER 2

typedef struct _STR_CONNECTINFORV4_BASIC
{
	//内部的会话ID，及会话信息所在数组的下标，能够基于数组偏移定位
	uint32_t ConnectID;
	//外部会话ID，全局唯一的会话ID,用于关联会话信息
	uint64_t ConnectKeyID;

	//源、目的IP及端口
	c_ip pIP[2];
	uint16_t pPort[2];
	
	//IP协议号
	uint8_t IPPro;
	//服务器方向
	uint8_t Server;	//PACKETFROMUNKOWN/PACKETFROMCLIENT/PACKETFROMSERVER
	
	//连接到现在的包数及字节数，分源、目的方向
	//pPacketNum[0]+pPacketNum[1]=0时表示新增会话
	uint32_t pPacketNum[2];
	uint32_t pPayloadNum[2];
	uint64_t pPacketBytes[2];
	
	//会话命中规则状态，根据规则命中要求修改为结构体
	uint32_t pRule[MAX_SESSION_RULE_HIT];
	uint32_t RuleNum;
	uint32_t RuleLevel;
	
	//起始时间
	uint32_t StartTime[2];
	//结束时间--
	uint32_t EndTime[2];

	int ProtoSign ;// 协议解析标识  -1 为不解析  0 为未识别  其他ID 为识别的协议类型

	uint32_t IO_Sign[2];//包含是否是内网Ip标识
	
	//可扩展的会话提取信息
	std::string ExtString;
	void *pSessionPub;
	//--如果增加这个字段，是否会造成存储空间消耗过大
}STR_CONNECTINFORV4_BASIC;


#endif

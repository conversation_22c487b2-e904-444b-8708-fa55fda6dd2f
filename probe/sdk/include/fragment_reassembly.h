#ifndef __FRAGMENT_REASSEMBLY_H__
#define __FRAGMENT_REASSEMBLY_H__

#include <list>
#include <cstdint>
#include <vector>
#include "fragment_define.h"

static const std::uint32_t def_buffer_size = 0x10000;

class FragmentReassembly
{
public:
    enum State { normal = 0, lost_data };
    FragmentReassembly(std::uint32_t buffer_size = def_buffer_size);
    int add_fragment(std::uint64_t seq_begin, std::uint64_t seq_end, const char *p_data);
    int front_fragment(std::uint64_t &seq_begin, std::uint64_t &seq_end, char *&p_data);
    int pop_front();
    int clear();
    std::uint32_t get_buffer_size();
    
    friend class TcpFragmentReassembly;

private:
    std::vector<char> buffer;
    std::list<FragmentNode> frag_list;
    std::uint32_t buffer_size;
    State state;
};

#endif

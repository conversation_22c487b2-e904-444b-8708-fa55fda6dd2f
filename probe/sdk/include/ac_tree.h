// Last Update:2017-05-08 14:01:21
/**
 * @file ac_tree.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2016-04-26
 */
#ifndef AC_TREE_H
#define AC_TREE_H 
#include <vector>
#include  "ac_rule.h"
using namespace std;
//多模匹配的积累数据
template < class T>
class ac_tree_node
{
    public:
        ac_tree_node(T d, int len ){
            node_Vector.clear();
            data = d ;
            sign = 0; 
            offlen = len ;

        }
        ~ac_tree_node()
        {
            typename vector<ac_tree_node<T> * >::iterator iter = node_Vector.begin();
            for( ;iter != node_Vector.end(); iter ++)
            {
                ac_tree_node<T> * p =  *iter ;
                delete p ;
            }
            node_Vector.clear();

        }
        ac_tree_node * find_data(T & d) 
        {
            // 二分查找发
            int first = 0;
                int end =node_Vector.size() -1 ;
                int mid = 0;
                for(;first <= end;)
                {
                    mid = (first + end) /2 ;
                    if(node_Vector[mid]->data == d) 
                    {
                        return node_Vector[mid];
                    }
                    else if (node_Vector[mid]-> data < d)
                    {
                        first = mid +1;
                    }
                    else 
                    {
                        end = mid -1;
                    }
                }
                return NULL;
            }
            ac_tree_node * add_ac_node(T d ) 
            {
                ac_tree_node * p = new ac_tree_node(d, offlen + 1 );
                typename vector<ac_tree_node * >::iterator iter = node_Vector.begin();
                for(;iter!=node_Vector.end(); iter ++) 
                {
                    if(d < (*iter)->data) 
                    {
                        //		ac_tree_node * node= new ac_tree_node(d,offlen + 1);
                        node_Vector.insert(iter  ,p  );
                        return p ;
                    }
                }
                // 添加的数据应该放在末尾
                if(iter == node_Vector.end()) 
                {
                    //	ac_tree_node * node= new ac_tree_node(d, offlen + 1 );
                    node_Vector.push_back(p );
                }
                return p ;
            }
            int get_sign() 
            {
                return sign ;
            }
            int set_sign(int * num_sign)
            {
                if(sign == 0 ) 
                {
                    sign =  *num_sign ;
                    return sign;
                }
                else 
                {
                    return sign ;
                }
            }
            int get_len()
            {
                return offlen ;
            }
        private :
            T data ;
            int sign; // 串标志位 
            int offlen ;
            vector<ac_tree_node<T> * > node_Vector;  //下个节点
    };
    template<typename  T> 
    class ac_tree 
    {
        private :
            int * num_sign;
            ac_tree_node<T> * ac_head;
            // 数据命中规则 
        public:
            ac_tree()
            {
                num_sign = new int;
                *num_sign = 1 ;
                T d ;
                ac_head = new ac_tree_node<T>(d , 0 );
            }
            ~ac_tree()
            {
                delete  ac_head;
                if(num_sign != NULL)
                {
                    delete num_sign;
                }
                num_sign = NULL;
            }
        // 
        int add_ac_contral_data(T * data , int  len) // 
        {
            // 从最外层向里层逐步的添加数据
            int i = 0 ; 
            ac_tree_node<T> * p_this = ac_head ;
            ac_tree_node<T> * p = ac_head ;
            // 添加整个数据到结构体
            for(;i < len ; i++)
            {
                p = p_this -> find_data(data[i]);
                if(p == NULL) 
                {
                    p = p_this ->add_ac_node(data[i]);
                }
                p_this =  p ;
            }
            *num_sign += 1;
            return  p_this ->set_sign(num_sign);
        }
        //需修改
        void add_rule_to_ac(T * p_data , int  len ,  rule_node * p_rule_node ) 
        {
             int  sign = add_ac_contral_data(p_data,len) ;
             p_rule_node ->add_rule_node(sign);
        }
        //需修改
        //初始化 
        void add_rule_to_ac(T ** pp_data , int  *len , int num , rule_node * p_rule_node ) 
        {
            int i = 0 ;
            int sign = 0 ; 
         //   result_node result ;
            for(;i < num; i++) 
            {
               // rule_node * p_rule_node = new rule_node();
                sign = add_ac_contral_data(pp_data[i],len[i]) ;
                p_rule_node ->add_rule_node(sign);
               // p_rule -> add_result_node(p_rule_node) ;
            }
            //  return  p_this ->set_sign();
        }
        // 查找 
        void ac_rule_find(T *pData  , int len   , rule * p_rule ) 
        {
            // -
            ac_tree_node<T> * p =  ac_head  ; 
            list<ac_tree_node<T> * > list_tmp_rule ;
            if(p != NULL) 
            {
                int i = 0 ;
                for(i = 0 ; i < len ; i++) 
                {
                    typename list<ac_tree_node<T> *>::iterator iter = list_tmp_rule.begin();
                    for(;iter != list_tmp_rule.end(); ) 
                    {
                        ac_tree_node<T> * q = (*iter) -> find_data(pData[i]); 
                        if(q != NULL) 
                        {
                            int tmp_rule_id   = q->get_sign() ;
                            if(tmp_rule_id != 0) 
                            {

                                p_rule->add_result_node(tmp_rule_id , i + 1 - q -> get_len()  );
                            }
                            *iter = q ;
                            iter ++;
                        }
                        else 
                        {
                            iter = list_tmp_rule.erase(iter );
                        }
                    }
                    //
                    ac_tree_node<T> * q = p -> find_data (pData[i]);
                    if(q != NULL) 
                    {
                        list_tmp_rule.push_back(q);
                        int tmp_rule_id   = q->get_sign() ;
                        if(tmp_rule_id != 0) 
                        {
                            p_rule->add_result_node(tmp_rule_id,i + 1 - q -> get_len() );
                        }
                    }
                }
            }
        }
};



#endif  /*MB_TREE_H*/

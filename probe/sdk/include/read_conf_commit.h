// Last Update:2019-10-05 22:23:24
/**
 * @file read_conf_commit.h
 * @brief: 读取公用配置文件 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-10-05
 */

#ifndef READ_CONF_COMMIT_H
#define READ_CONF_COMMIT_H
#include <stdint.h>
#include <string>
#include <stdio.h>
#include <error.h>
#include <map>
class  read_ini
{
    public:
        read_ini(std::string file_path)
        {
            FILE *fp = fopen(file_path.c_str(),"r");
            if(fp == NULL)
            {
            //    printf("open fail errno = %d reason = %s \n", errno, strerrno(errno));
                exit(1);
            }
            char buff[2048];
            std::string ini_head = "default";
            std::string ini_key = "";
            std::string ini_value = "";
            std::map<std::string , std::string> *p_map_str;
            while(fgets(buff, 2048 ,fp) != NULL)
            {
                std::string tmp=buff;
                if(tmp.length() <2)
                {
                    continue ;
                }
                tmp = trim(tmp);
                if(tmp[0] == '[')
                {
                    ini_head = std::string(tmp,1,tmp.length()-2);
                }
                else {
                    std::string::size_type position = tmp.find("=");
                    if (position != tmp.npos )
                    {
                        ini_key=std::string(tmp,0,position);
                        ini_value = std::string(tmp,position+1,tmp.length());
                        std::map<std::string ,std::map<std::string ,std::string>* >::iterator iter = string_map.find(ini_head);
                        if(iter != string_map.end())
                        {
                            p_map_str = iter ->second ;
                        }
                        else 
                        {
                            p_map_str = new  std::map<std::string , std::string>();
                            string_map[ini_head] = p_map_str ;
                        }
                        p_map_str ->insert(std::pair<std::string,std::string>(ini_key , ini_value));
                    }

                }
            }
            fclose(fp);
        }
        ~read_ini()
        {
            std::map<std::string ,std::map<std::string ,std::string>* >::iterator iter  = string_map.begin();
            for(;iter != string_map.end(); iter ++)
            {
                std::map<std::string ,std::string> * p_map = iter ->second ;
                delete p_map ;
            }
        }
        // 
        std::string get_ini_value(std::string i_head  , std::string i_key )
        {
             std::map<std::string ,std::map<std::string ,std::string>* >::iterator iter  = string_map.find(i_head) ;
             if( iter != string_map.end())
             {
                 std::map<std::string ,std::string > ::iterator it = iter ->second->find(i_key);
                 if(it != iter -> second->end())
                 {
                     return  it ->second;
                 }
             }
             return std::string("");
        }
        std::string get_ini_value(std::string i_key )
        {
            std::map<std::string ,std::map<std::string ,std::string>* >::iterator iter  = string_map.find("default") ;
             if( iter != string_map.end())
             {
                 std::map<std::string ,std::string > ::iterator it = iter ->second->find(i_key);
                 if(it != iter -> second->end())
                 {
                     return  it ->second;
                 }
             }
        }
    private:
        std::map<std::string ,std::map<std::string ,std::string>* > string_map;
        std::string& trim(std::string &s)
        {
            if (s.empty())
            {  
                return s;  
            }  
            s.erase(0,s.find_first_not_of(" "));  
            s.erase(0,s.find_first_not_of("\n"));  
            s.erase(s.find_last_not_of(" ") + 1);  
            s.erase(s.find_last_not_of("\n") + 1);  
            return s;  
        }
};
class read_conf_commit
{
    public:
    static read_conf_commit* GetInstance()
    {
        static read_conf_commit  instance;
        return  & instance;
    }
    uint32_t  read_conf_session_num(std::string i_head  , std::string i_key )
    {
        return (uint32_t)atoi(p_ini->get_ini_value(i_head ,i_key).c_str());
    }
    ~ read_conf_commit()
    {
        delete  p_ini ;
    }
    private:
        read_conf_commit()
        {
            p_ini = new read_ini(string(getenv("THE_CONF_PATH")) + "/common_conf.ini");
        }
        read_ini * p_ini ; 

};
#endif  /*READ_CONF_COMMIT_H*/

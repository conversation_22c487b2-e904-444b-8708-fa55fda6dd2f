#ifndef DFI_CONFIG_PARSE_H
#define DFI_CONFIG_PARSE_H
#include "xml_parse.h"
#include <ac_rule.h>
//#include <ac_tree.h>
#include <WuManber.h>
#include <mwm.h>
#include <list>
#include <string>
using namespace std;


class DFI_conf_parse
{
    public:
        DFI_conf_parse();
        ~DFI_conf_parse();
        void config_parse(char * xml_path , list<rule_node_offist *> * p_list,CWuManber * ac_true, uint32_t id = 0);
        void config_parse(const char *xml_path ,list<rule_node_offist *> *p_list, CWuManber *ac_true, string protocol_type,uint32_t id =0 );
        void config_parse(xmlNodePtr curNode , list<rule_node_offist *> * p_list , CWuManber *ac_true ,uint32_t id = 0 );
    private:
        xml_parse xml;
        //ac_tree<char> * p_ac_true ;
        CWuManber *p_ac_true;
        char * buff;
        void xml_get_child(string xpath, list<rule_node_offist *> *p_list);
        void xml_get_value(string xpath, list<rule_node_offist *> *p_list);
        rule_node_offist *   create_rule_node(string s_type ,string offtype, int offise , string s_value ,string word, list<rule_node_offist *> *p_node_offist_list);
        rule_node_offist * p_tmp_rule ;
        uint32_t tmp_rt_id  ;

};

#endif

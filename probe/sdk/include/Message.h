// Last Update:2019-05-12 11:38:20
/**
 * @file Message.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-10-08
 */

#ifndef _MESSAGE_H
#define _MESSAGE_H
#include <ZMPNMsg.pb.h>

class CJsonMsg
{
    public:
        CJsonMsg()
        {
            buf_len = (1 << 16);
            buf = new uint8_t[buf_len];
            memset(buf, 0, buf_len);
            index_buf = new uint8_t[1024];
            memset(index_buf, 0, 1024);
            offset = 0;
            failed_msg = 0;
        }
        ~CJsonMsg()
        {
            if(buf)
            {
                delete[] buf;
                buf = NULL;
            }
            if(index_buf)
            {
                delete[] index_buf;
                index_buf = NULL;
            }
        }
        int clear()
        {
            offset = 0;
            failed_msg = 0;
            return 0;
        }
        int is_complete()
        {
            return !failed_msg;
        }
        int get_offset()
        {
            return offset;
        }
        int get_buf_len()
        {
            return buf_len;
        }
        int set_offset(int len)
        {
            if(len < buf_len)
            {
                offset = len;
                return len;
            }
            else
            {
                failed_msg = 1;
                return -1;
            }
        }
        uint8_t* get_buf()
        {
            return buf;
        }
        int copy_buf(uint8_t *pbuf, uint32_t len, int whence = 0)
        {
            int left = 0;
            if(failed_msg)
            {
                return -1;
            }
            offset += whence;
            if(offset < 0)
            {
                offset = 0;
            }
            left = buf_len - offset;
            if(len < left)
            {
                memcpy(buf + offset, pbuf, len);
                offset += len;
                return (int)len;
            }
            failed_msg = 1;
            return -1;
        }
        int add_index(string filename, uint64_t row)
        {
            if(failed_msg)
            {
                return -1;
            }
            int index_len = snprintf((char *)index_buf, 1024, ",\"filename\":\"%s\",\"row\":%"PRIu64"}\n", filename.c_str(), row);
            if(index_len < 1024)
            {
                return copy_buf(index_buf, index_len, -1);
            }
            failed_msg = 1;
            return -1;
        }
    private:
        uint8_t *buf;
        uint8_t *index_buf;
        int offset;
        int buf_len;
        int failed_msg;
};
class CMessage
{
    public : 
        CMessage()
        {
            pb_use_num = 0;
            max_num = 1024;
            b_json_available = 0;
            p_msg =  new  JKNmsg*[max_num];
            p_json_msg = new CJsonMsg[max_num];
            has_json_map = new uint8_t[max_num];
        }
        CMessage(uint16_t i)
        {
            pb_use_num = 0;
            max_num = i;
            b_json_available = 0;
            p_msg =  new  JKNmsg*[max_num];
            p_json_msg = new CJsonMsg[max_num];
            has_json_map = new uint8_t[max_num];
        }
        ~CMessage()
        {
            if(p_msg != NULL)
            {
                for(uint16_t i = 0; i < pb_use_num; i ++)
                {
                    delete  p_msg[i] ;
                    p_msg[i] =  NULL ;
                }
                delete [] p_msg ;
            }
            if(p_json_msg)
            {
                delete[] p_json_msg;
            }
            if(has_json_map)
            {
                delete[] has_json_map;
            }
        }
        void init()
        {
            // 初始化数据
            for( uint16_t i = 0 ; i < pb_use_num ; i++)
            {
                if( p_msg[i]!=NULL) 
                {
                    delete  p_msg[i] ;
                    p_msg[i] =  NULL ;
                }
            }
            pb_use_num = 0 ;
        }
        //
        JKNmsg * get()
        {
            if(pb_use_num < max_num) 
            {
                uint16_t n = pb_use_num ;
                has_json_map[pb_use_num] = 0;
                pb_use_num ++ ;
                p_msg[n] = new JKNmsg() ;
                return  p_msg[n];
            }
            return NULL; 
        }
        void set_json_available()
        {
            b_json_available = 1;
        }
        CJsonMsg *jget() //use after get
        {
            if(b_json_available && pb_use_num > 0 && pb_use_num <= max_num)
            {
                CJsonMsg *ret = &p_json_msg[pb_use_num-1];
                has_json_map[pb_use_num-1] = 1;
                ret->clear();
                return ret;
            }
            return NULL;
        }
        JKNmsg * get(int i)
        {
            return p_msg[i];
        }
        CJsonMsg *jget(uint16_t i)
        {
            return &p_json_msg[i];
        }
        int get_type(int i)
        {
            return p_msg[i] ->type();
        }
        uint8_t has_json(int i)
        {
            return has_json_map[i];
        }
         uint16_t pb_use_num;

    private:
         uint16_t b_json_available; //json 开关
         uint16_t max_num ;
         JKNmsg ** p_msg ;
         CJsonMsg *p_json_msg;
         uint8_t *has_json_map;
};
#endif  /*_MESSAGE_H*/

/*
 * WuManber.h - This file is the part of the StringSearch.
 * Copyright (C) 2010 <PERSON> <<EMAIL>> <http://www.deanlee.cn/>
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 * 
 * Acknowledgements:
 *   <> Many thanks to all of you, who have encouraged me to update my articles
 *     and code, and who sent in bug reports and fixes.
 * 
 */


#include "mwm.h"
#include "ac_rule.h"
#include <stdio.h>

#ifndef __CWuManber__
#define __CWuManber__


/*
#ifndef WU_VARIANT_BOOL 
#define WU_VARIANT_BOOL bool 
#endif 

#ifndef WU_BSTR
#define WU_BSTR char*
#endif

#ifndef WU_BYTE
#define WU_BYTE unsigned char
#endif

#ifndef TCHAR 
#define TCHAR char
#endif

#ifndef LONG 
#define LONG long
#endif

#ifndef WCHAR 
#define WCHAR char
#endif

#ifndef _tcslen
#define _tcslen(s) strlen((const char *)s)
#endif
*/
enum STDMETHODIMP
{
    S_OK
};

        

// CWuManber

class  CWuManber 
{
    public:
        CWuManber()
        {
            m_index = -1;
            m_handle = mwmNew();
            m_sign = new int(1);
        }

        void setIndex(int index);
        int get_sign();
        void set_sign(int);



        ~CWuManber()
        {
            if (m_handle)
                mwmFree(m_handle);
        }

    public:
        STDMETHODIMP AddPatterns(char* patterns, int length, bool caseSensitive);
        STDMETHODIMP Search(char* string, int length, long* pIndex, rule * p_rule);

    protected:
        void * m_handle;
        int m_index;
        int* m_sign;//rule id 也可存在pattern中
};
#endif

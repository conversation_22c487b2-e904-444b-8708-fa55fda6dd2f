#ifndef __TCP_FRAGMENT_REASSEMBLY_H__
#define __TCP_FRAGMENT_REASSEMBLY_H__

#include "fragment_reassembly.h"

class TcpFragmentReassembly
{
public:
    TcpFragmentReassembly(std::uint32_t nothing = 0, std::uint32_t buffer_size = def_buffer_size):fr(buffer_size){};
    int add_fragment(std::uint32_t seq_raw, std::uint32_t len, const char *p_data);
    int front_fragment(std::uint32_t &seq_raw, std::uint32_t &len, char *&p_data);
    int pop_front();
    int clear();
    std::uint32_t get_buffer_size();
    std::uint32_t get_fragment_num() {return fr.frag_list.size();}

private:
    FragmentReassembly fr;
};

#endif

// Last Update:2018-10-01 20:13:38
/**
 * @file PacketDefine.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-09-11
 */

#ifndef _PACKET_DEFINE_H
#define _PACKET_DEFINE_H

#include <stdint.h>
#define DWORD uint32_t
#define WORD uint16_t
#define UINT64 uint64_t
#include<iostream>
#include <json/json.h>
//#include "json/include/json/reader.h"
//#include "json/include/json/writer.h"
//#include "json/include/json/value.h"
using namespace std;

#define UNKNOWN_SERVER_RULEENGINE		0		//服务器未知
#define SOURCEIP_SERVER_RULEENGINE		1		//服务器：源IP
#define DESTIP_SERVER_RULEENGINE		2		//服务器：目的IP
typedef struct _STR_IPv6IP
{
     //IP--正序存放
      unsigned char IP[16];
}STR_IPv6IP;

//不使用指针，是的数据复制后，解析的各协议信息依然可用
typedef struct _STR_PROTOCOLINFOR
{
    WORD Offset;          //从数据起始指针到当前协议的偏移
    WORD Len;             //协议头长度,不包括数据
    DWORD Protocol;        //协议ID
    DWORD Property;   //当前协议属性,用于区分协议细节
    //高
    DWORD Sign;      //暂时保留，一个结构体16字节，便于指针对齐

}STR_PROTOCOLINFOR;


//协议栈空间最大值
#define MAXSTACKNUM 16

typedef struct _STR_PROTOCOLSTACK
{
    unsigned char *pStart;                 //数据包头，用户初始化
    unsigned char *pEnd;                   //数据包尾，用户初始化
    DWORD TimeStamp[2];                   //时间戳，用户初始化  0:秒 ; 1:ns;


    STR_PROTOCOLINFOR pProtocol[MAXSTACKNUM];       //协议栈, 0为最底层协议
    unsigned char ProtocolNum;                 //当前栈内节点数量，用户初>始化为零
    unsigned char IPProNum;            //IP协议数量
    unsigned char FirstIP;             //首个IP协议的编号+1，初始为零，IP协
    unsigned char LastIP;              //末IP协议的编号位置+1，初始为零

    DWORD TotalSign;                //包标识，协议标识的汇总，用户初始化为>零,各协议Sign值得或
} STR_PROTOCOLSTACK;
typedef struct _STR_IPv4CONNECT
{
    //端口--逆序存放,不存在为零
    WORD Port[2];
    //IP--逆序存放
    DWORD IP[2];
    //协议类型
    unsigned char Protocol;

}STR_IPv4CONNECT;

typedef struct _STR_IPv6CONNECT
{
    //端口--逆序存放,不存在为零
    WORD Port[2];
    //IP--正序存放
    unsigned char IP[2][16];
    //协议类型
    unsigned char Protocol;

}STR_IPv6CONNECT;

typedef struct _STR_CONNECT
{
   STR_IPv4CONNECT ConnectV4;
   STR_IPv6CONNECT ConnectV6;
}STR_CONNECT;


///////////////////////////////////////////////////////////////////////////////////////////////////////Mac信息
typedef struct _STR_MACINFOR_PLUS_IPSTATISTIC
{
    //包信息:0 接收； 1 发送
    UINT64 pPacket[2];
    UINT64 pPacketBytes[2];
    UINT64 pConnectNum[2];
}STR_MACINFOR_PLUS_IPSTATISTIC;
///////////////////////////////////////////////////////////////////////////////////////////////////////Mac信息--End


///////////////////////////////////////////////////////////////////////////////////////////////////////////////IP统计


#define SEND_PLUS_INTRANET		0	//与源IP相同
#define RECEIVE_PLUS_INTRANET	1



//IP在一个时间片段内的统计信息
typedef struct _STR_CACHE_PLUS_IPSTATISTIC
{
    //IP--包括TCP、UDP
    UINT64 Packet_IP;
    UINT64 Bytes_IP;

    //TCP
    UINT64 Packet_TCP;
    UINT64 Packet_SYN_TCP;
    UINT64 Packet_SYNACK_TCP;
    UINT64 Bytes_TCP;

    //UDP
    UINT64 Packet_UDP;
    UINT64 Bytes_UDP;

    //通联的新增IP数
    UINT64 Suc_NewIPNum;
    UINT64 Failed_NewIPNum;
    //新增连接数
    UINT64 ConnectNum;

}STR_CACHE_PLUS_IPSTATISTIC;

//IP存活期间详细信息统计信息
typedef struct _STR_PACKET_PLUS_IPSTATISTIC
{
    //IP--包括TCP、UDP
    UINT64 Packet_IP;
    UINT64 Bytes_IP;

    //TCP
    UINT64 Packet_TCP;
    UINT64 Packet_SYN_TCP;
    UINT64 Packet_SYNACK_TCP;
    UINT64 Bytes_TCP;
    UINT64 Port53_TCP;
    UINT64 DNS_TCP;

    //UDP
    UINT64 Packet_UDP;
    UINT64 Bytes_UDP;
    UINT64 Port53_UDP;
    UINT64 DNS_UDP;

    //通联的新增IP数
    UINT64 Suc_NewIPNum;
    UINT64 Failed_NewIPNum;
    //新增连接数
    UINT64 ConnectNum;

    //丢弃的包数和字节数
    UINT64 Total_Drop;
    UINT64 TotalBytes_Drop;

    //保存的包数和字节数
    UINT64 Total_Save;
    UINT64 TotalBytes_Save;

    //最大字节流量
    DWORD bps_Max;
    //最大包数流量
    DWORD pps_Max;

}STR_PACKET_PLUS_IPSTATISTIC;


#define EXTEND_PROPERTY_PLUS_IPSTATISTIC	1			//IP包标识拓展,若存在该标识，该IP的标识位需要跨线程扩展
#define DROP_PROPERTY_PLUS_IPSTATISTIC		0x10		//IP包处理模式--丢弃,丢弃的级别最高，若设置丢弃标签，不进行后续处理
#define SAVE_PROPERTY_PLUS_IPSTATISTIC		0x20		//IP包处理模式--保存
#define DDOS_PROPERTY_PLUS_IPSTATISTIC		0x40		//该IP正在遭遇DDOS攻击

//IP统计信息汇总
typedef struct _STR_STATISTIC_PLUS_IPSTATISTIC
{
    DWORD Magic;

    /////////////////////////////////仅记录发送信息
    //即源IP为该IP
    //最大最小包长: 0 Min ; 1 Max
    DWORD PacketLen[2];
    //最大、最小TTL : 0 Min ; 1 Max
    unsigned char TTL[2];

    /////////////////////////////////仅记录发送信息--End
    //是否内部IP: 0 非内部IP； 1 内部IP
    WORD IsIntra;
    //当前IP类型
    WORD IPType;
    //IP
    DWORD IPV4;
    STR_IPv6IP IPv6;


    //起始时间
    DWORD StartTime;
    //结束时间
    DWORD EndTime;
    //上次IP信息输出时间
    DWORD LastIPInforTime;

    /////////////////////////////////处理模式
    //IP处理属性
    DWORD Property;
    //该属性有效时间-到现在的秒数，到时Property请零
    //该值为0xfffffff 表示长期有效
    DWORD ActiveTime;
    /////////////////////////////////处理模式

    //收发的包信息: RECEIVE_PLUS_INTRANET\SEND_PLUS_INTRANET
    STR_PACKET_PLUS_IPSTATISTIC PacketInfor[2];

}STR_STATISTIC_PLUS_IPSTATISTIC;


///////////////////////////////////////////////////////////////////////////////////////////////////////////////IP统计--End

//拓展包信息
typedef struct _STR_EXTEND_PACKETINFOR_CONNECT
{
}STR_EXTEND_PACKETINFOR_CONNECT;

//单包信息
typedef struct _STR_PACKETINFOR_MUDULE_CONNECT
{
    //包编号，唯一的标识，便于测试
    UINT64 PacketID;

    //协议栈 //链表
    STR_PROTOCOLSTACK Stack;
    //应用协议ID
    DWORD ProID;
    //////////////////五元祖
    //连接信息
    STR_CONNECT Connect;
    //连接类型:0 未知； PROTOCOL_IPV6 ；PROTOCOL_IP
    unsigned char ConnectType;
    //服务器  0 未知，1 IP[0]服务端；2 IP[1]为服务器
    //UNKNOWN_SERVER_RULEENGINE
    unsigned char Server;
    //Packet中的 STR_CONNECT 与Connect中的方向是否一致
    //0 一致，1 不一致
    unsigned char Directory;
    //TTL
    unsigned char TTL_IP;
    //属性 见 NEWCONNECT_CONNECTINFOR_CYBERX
    DWORD Property;
    //////////////////五元祖--End
    //该包对应的Mac信息:0 源、1 目的
    STR_MACINFOR_PLUS_IPSTATISTIC *pMacInfor[2];
    //////////////////Mac信息--End
    //该IP包源、目的IP的信息

    //0 源IP； 1 目的IP
    DWORD Position_IP[2];
    //是否新IP：0 否，1是
    DWORD IsAdd_IP[2];
    //该IP的统计信息:0 源、1 目的
    STR_STATISTIC_PLUS_IPSTATISTIC *pIPInfor[2];

    //若对应端口为53，其端口统计信息
    //STR_INFOR_PLUS_PORT53 *pPortInfor_53[2];
    //////////////////IP信息--End

    /////////////////连接信息
    //所属的连接唯一性ID
    UINT64 ConnectKeyID;

    //连接ID：若为0表示无连接
    DWORD ConnectID;
    //该包在连接中的包序号，从1开始,0表示无连接
    DWORD PacketCount;
    //带负载的包计数
    DWORD PacketWithPayloadCount[2];

    /////////////////连接信息--End


    /////////////////规则
  //  DWORD pRuleID[MAXRULENUM_ENGINE];
   // DWORD RuleNum;
    //规则level的最大值
   // DWORD Level;
    /////////////////规则--End


    //拓展包信息--拓展信息内可以修改
    //插件接口变化时，无需修改插件信息
    STR_EXTEND_PACKETINFOR_CONNECT *pExtendInfor;

    //拓展Json字段，若记录该包信息，在此处拓展。
    //["Extend"]
    Json::Value ExtendJson;

}STR_PACKETINFOR_MUDULE_CONNECT;

void  packet_parse( unsigned char *IN_pPacket, DWORD IN_pPacketSize, DWORD IN_TimeStamp[2],  STR_PACKETINFOR_MUDULE_CONNECT& context ,  DWORD IN_FirstPro );


#endif  /*_PACKET_DEFINE_H*/

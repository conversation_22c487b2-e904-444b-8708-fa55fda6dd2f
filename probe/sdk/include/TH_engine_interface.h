// Last Update:2019-08-23 16:59:12
/**
 * @file TH_engine_interface.h
 * @brief : 引擎接口定义
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-09-26
 */

#ifndef _T_H_ENGINE_INTERFACE_H
#define _T_H_ENGINE_INTERFACE_H

//单包解析接口  用于添加协议
#include "packet.h"
#include "session_pub.h"
#include <json/json.h>
class dan_packet_parse
{
    public:
        virtual ~dan_packet_parse();
        virtual void handle(c_packet * p_packet ) = 0; // 协议解析类型
};
class dpi_base // 解析基类 ，  // dpi 写成单例
{
    public:

        virtual ~dpi_base() = 0;
        virtual void handle(c_packet * p_packet ) = 0; // 协议解析类型
};

//需要协议识别模块 // 规则匹配模块
class session_rule
{
    public:
      //  virtual void dpi(c_packet * p_packet );
       virtual ~session_rule();
       virtual void session_rule_handle(c_packet * p_packet ,session_pub * p_session);
       virtual void time_out(session_pub * p_session) = 0; //  数据超时接口 ， 可以用作定时调用接口
       virtual void resources_recovery(session_pub * p_session) = 0; // 资源回收接口
};
// session维度信息统 全流量特征提取
class packet_full_flow
{
    public:
        packet_full_flow(int thread_num, int thread_id)
        {
            this->thread_num = thread_num;
            this->thread_id = thread_id;
            this->b_seg_mode_work = 0;
        }
       virtual ~packet_full_flow() 
       {
       }
       virtual void full_flow_handle(c_packet * p_packet ,session_pub * p_session) = 0;
       virtual bool time_out(session_pub * p_session) = 0; //  数据超时接口 ， 可以用作定时调用接口
       virtual void resources_recovery(session_pub * p_session) = 0; // 资源回收接口  , 超时处理接口
       virtual void session_recovery(session_pub * p_session) // 资源回收接口 ,session 等数据处理
       {

       }
       virtual void log(uint32_t ntime)
       {
       }
       virtual void set_tools(void * p_tools)
       {
           p_th_tools = p_tools;
       }
       virtual void module_timeout(uint32_t s, CMessage *value, uint32_t thread_id)
       {
           
       }
       virtual uint8_t get_should_work()
       {
           return b_seg_mode_work;
       }
       virtual void set_workmode(uint8_t b_segment_analysis)
       {
           this->b_segment_analysis = b_segment_analysis;
       }
       virtual void set_should_log(int should_log)
        {
            this->should_log = should_log;
        }
    protected:
       void *p_th_tools;
       int thread_num;
       int thread_id;
       uint8_t b_seg_mode_work;
       uint8_t b_segment_analysis;
       int should_log;
};
// 负载还原解析 接口
class session_pasre_base
{
    public:
        session_pasre_base()
        {
        }
        virtual bool potocol_init(session_pub* p_sess, c_packet* p_pack) = 0;//session初始化
        virtual bool potocol_sign_judge(session_pub* p_sess, c_packet* p_packet) = 0;//组包函数
        virtual bool potocol_parse_handle ( session_pub * p_session ,c_packet * p_packet) = 0;//协议解析
        virtual void potocol_data_handle(session_pub* p_sess,c_packet * p_packet) = 0;//消息发送
        virtual bool time_out(session_pub * p_session,uint32_t )//超时处理，预留
        {
            ;
        }
        virtual void resources_recovery(session_pub * p_session) = 0; //session超时释放时，session内资源回收调用接口
        virtual void session_recovery(session_pub * p_session) //session超时释放时，插件内资源回收调用接口
        {
            ;
        }
        virtual void reload()
        {
            ;
        }
        virtual void set_tools(void * p_tools)
        {
            p_th_tools = p_tools;
        }
        virtual void set_should_log(int should_log)
        {
            this->should_log = should_log;
        }
    protected:
        void *  p_th_tools;
        int should_log;
};
// 发送消息处理插件基类
class msg_handle_base
{
    public:
        msg_handle_base()
        {
        }
       virtual int msg_handle(void* p_msg,uint64_t * session_id) = 0;
       virtual void set_tools(void * p_tools)
        {
            p_th_tools = p_tools;
        }
    protected:
        void *  p_th_tools;
};
# endif  /*_T_H_ENGINE_INTERFACE_H*/

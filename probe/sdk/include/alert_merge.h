#ifndef __ALERT_MERGE_H__
#define __ALERT_MERGE_H__

#include "DataStructure/TemplateMatch.h"
#include "DataStructure/SequenceList.h"
#include "DataStructure/Func_Math.h"

typedef struct
{
    int         target_type;    // 0  ip   1 端口 2  应用  3  域名 4  证书 5 mac 6 会话ID
    int         rule_id;
    union
    {
        struct
        {
            uint32_t        isv6;
            union
            {
                uint32_t    ipv4;
                uint8_t     ipv6[16];
            };
        };
        uint16_t            port;
        uint32_t            app_id;
        uint8_t             domain[64];
        uint8_t             cer_key[20];
        uint8_t             mac[6];
        uint64_t            session_id;
    }           target_info;
} alert_target_key;

typedef struct
{
    alert_target_key    key;
    uint32_t            val;
} alert_target;

class alert_target_compare
{
public:
    int operator()( const alert_target &IN_A,const alert_target &IN_B )
    {
        if (IN_A.key.target_type == IN_B.key.target_type && IN_A.key.rule_id == IN_B.key.rule_id)
        {
            return memcmp(&IN_A.key.target_info, &IN_B.key.target_info, sizeof(IN_A.key.target_info));
        }
        else
        {
            return 1;
        }
    }
};

class AlertMerge
{
    public:
 
    DWORD Init( DWORD IN_MaxNodeNum,DWORD IN_HashSize=0 )
    {
        DWORD re;
 
        //根据MaxNodeNum计算合适的HashSize
        if (IN_HashSize==0)
        {
            IN_HashSize = GetPrimeNum(IN_MaxNodeNum >> 2, IN_MaxNodeNum);
            IN_HashSize = J_max(IN_HashSize, 1);
        }
    
        re=m_Array.Init( IN_MaxNodeNum+1 );
        if( re!=0 ) return re;
    
        re=m_AlertJudge.Init( IN_HashSize,IN_MaxNodeNum,&m_Array );
        if( re!=0 ) return re;
    
        re=m_Sequence.Init( IN_MaxNodeNum+1 );
        if( re!=0 ) return re;
    
        return 0;
    }
    void Quit()
    {
        m_AlertJudge.Quit();
        m_Array.Quit();
        m_Sequence.Quit();
    }
    void Reset()
    {
        m_AlertJudge.Reset();
        m_Array.Reset();
        m_Sequence.Reset();
    }
    // ret: 0,added; 1,new add;  >ERRORSIGN,error
    // out_alert:0,error >0,position
    DWORD JudgeAndAddAlert( alert_target &in_alert, __time32_t IN_TimeStamp, DWORD &out_alert )
    {
        DWORD index=0;
        DWORD re;
        DWORD IsAdd=0;
    
        index=get_alert_index(in_alert.key);
        out_alert = m_AlertJudge.JudgeAndAdd(index, in_alert, IsAdd);
        if( out_alert>=ERRORSIGN  )
        {
            re=out_alert;
            out_alert=0;
            return re;
        }
    
        re=m_Sequence.RenewNode( out_alert,IN_TimeStamp );
        if( re>=ERRORSIGN )
        {
            out_alert=0;
            return re;
        }
    
        return IsAdd;
    }

    DWORD GetInfor(DWORD in_position, alert_target &out_alert)
    {
        if (in_position >= m_Array.m_DataSize)
        {
            return 0x8000ff01;
        }
        out_alert = m_Array.m_pData[in_position];
        return 0;
    }

    void UpdateAlertVal(uint32_t val, DWORD in_position)
    {
        if (in_position >= m_Array.m_DataSize)
        {
            return;
        }
        m_Array.m_pData[in_position].val = val;
        return;
    }

    DWORD PopAlert( alert_target &out_alert )
    {
        DWORD Position;
 
        Position=m_Sequence.PopNode( );
        if( Position>=m_Array.m_DataSize ) return 0;
 
        out_alert=m_Array.m_pData[Position];
 
        m_AlertJudge.DeleteValue( Position );
 
        return Position;
    }

    private:
        DWORD get_alert_index(alert_target_key &in_alert)
        {
            DWORD idx = in_alert.rule_id;
            switch (in_alert.target_type)
            {
            case 0:
                if(in_alert.target_info.isv6)
                {
                    for(int i=0; i < 4; i++)
                    {
                        idx ^= ((uint32_t *)in_alert.target_info.ipv6)[i];
                    }
                }
                else
                {
                    idx ^= in_alert.target_info.ipv4;
                }
                break;
            case 1:
                idx ^= in_alert.target_info.port;
                break;
            case 2:
                idx ^= in_alert.target_info.app_id;
                break;
            case 3:
                for(int i=0; i < 8; i ++)
                {
                    idx ^= ((uint32_t *)in_alert.target_info.domain)[i];
                }
                break;
            case 4:
                for(int i=0; i < 5; i ++)
                {
                    idx ^= ((uint32_t *)in_alert.target_info.cer_key)[i];
                }
                break;
            case 5:
                idx ^= *(uint32_t *)&in_alert.target_info.mac[0];
                idx ^= *(uint32_t *)&in_alert.target_info.mac[2];
                break;
            case 6:
                for(int i=0; i < 2; i ++)
                {
                    idx ^= ((uint32_t *)&in_alert.target_info.session_id)[i];
                }
                break;
            default:
                break;
            }
            return idx;
        }
        CTemplateMatch<alert_target, alert_target_compare> m_AlertJudge;
        CArrayBasic<alert_target> m_Array;
        CSequenceList m_Sequence;

};


#endif
// Last Update:2019-07-01 17:16:21
/**
 * @file session_ext_json.h
 * @brief :session  json ext 
 *   1  检测指定目录下的 
 *   2  
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-07-01
 */

#ifndef SESSION_EXT_JSON_H
#define SESSION_EXT_JSON_H

#include <extjson_base.h>
#include "share_plugin_marge.h"
class session_ext_json
{
    public:
        session_ext_json(int max_thead )
        {
            p_share_plugin = new share_plugin_marge(EXTJSON_DIR);
            p_share_plugin ->get_plugin_list(EXTJSON_FUNCTIONNAME, &handle_list);
            std::list<void *> ::iterator iter = handle_list.begin();
            for(;iter != handle_list.end(); iter ++)
            {
                exttjson_handle_base * p_handle = (exttjson_handle_base *) (*iter) ;
                //return p_handle -> handle(p_session ) ;
                p_handle -> init(max_thead); 
            }
        }
        ~session_ext_json()
        {
            
            std::list<void *> ::iterator iter = handle_list.begin();
            for(;iter != handle_list.end(); iter ++)
            {
                exttjson_handle_base * p_handle = (exttjson_handle_base *)(* iter );
                //return p_handle -> handle(p_session ) ;
                delete p_handle;
            }
            handle_list.clear();
        }
        void handle(session_pub * p_session , c_packet * p_packet,int thread_num ) 
        {
            std::list<void *> ::iterator iter = handle_list.begin();
            for(;iter != handle_list.end(); iter ++)
            {
                exttjson_handle_base * p_handle = (exttjson_handle_base *) (*iter ) ;
                p_handle -> handle(p_session ,p_packet , thread_num) ;
            }
        }
        std::string  send(session_pub * p_session , int thread_num ) 
        {
            std::string value = "";
            std::list<void *> ::iterator iter = handle_list.begin();
            for(;iter != handle_list.end(); iter ++)
            {
                exttjson_handle_base * p_handle = (exttjson_handle_base *) (*iter) ;
                value += p_handle -> send(p_session , thread_num) ;
            }
            return value ;
        }
        void session_end(session_pub * p_session , int thread_num ) 
        {
            std::list<void *> ::iterator iter = handle_list.begin();
            for(;iter != handle_list.end(); iter ++)
            {
                exttjson_handle_base * p_handle = (exttjson_handle_base *)(* iter );
                p_handle -> session_end(p_session , thread_num );
            }
        }
    private:
        std::list<void *> handle_list;
        share_plugin_marge * p_share_plugin ;

};
#endif  /*SESSION_EXT_JSON_H*/

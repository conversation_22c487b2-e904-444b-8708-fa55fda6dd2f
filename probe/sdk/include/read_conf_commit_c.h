// Last Update:2019-10-05 22:39:54
/**
 * @file read_conf_commit_c.h
 * @brief : 读取公用配置文件 C版本
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-10-05
 */

#ifndef READ_CONF_COMMIT_C_H
#define READ_CONF_COMMIT_C_H
struct  key_value_link 
{
    char  ini_key[128] ;
    char  ini_value[128] ;
    struct key_value_link * p_link ;
};
static   struct  key_value_link * key_value_link_new()
{
    struct  key_value_link * p = (struct  key_value_link *) malloc(sizeof(struct  key_value_link));
    memset(p,0x0,sizeof(struct key_value_link));
    return p ;
};
struct ini_type_link
{
    char ini_head[128] ;
    struct  key_value_link *p_value ;
    struct ini_type_link * p_link;

};
struct ini_type_link * ini_type_link_new()
{
    struct ini_type_link  * p = (struct ini_type_link  *)malloc(sizeof(struct ini_type_link ));
    memset(p,0x0,sizeof(struct ini_type_link));
}
struct ini_type_link * p_head = NULL;
// 文件处理 
static bool read_ini_c(char * file_name )
{
     FILE *fp = fopen(file_name,"r");
      if(fp == NULL)
      {
          exit(1);
      }

     char buff[2048];
     char ini_head[128] = "default";
     char ini_key[128]="";
     char ini_value[128]="";
    // -------
     while(fgets(buff, 2048 ,fp) != NULL)
     {
        while(buff[strlen(buff) -1] == '\n' ||  buff[strlen(buff) -1] == ' ')
        {
            buff[strlen(buff) -1] = 0x0;
        }
        if(buff[0] == '[')
        {
            strncpy(ini_head,buff+1,strlen(buff+1)-1);
        }
        else 
        {
            char *p= strstr(buff,"=");
            if ( p != NULL) 
            {
                memset(ini_key ,0x0,128);
                memset(ini_value ,0x0,128);
                strncpy(ini_key, buff , p - buff );
                strncpy(ini_value,p+1,strlen(p+1));
                struct ini_type_link * p = p_head ;
                struct ini_type_link * q = p_head ;
                while(p != NULL) 
                {
                    if(strcmp(p->ini_head,ini_head) == 0)
                    {
                         break; 
                    }
                    q = p ;
                    p = p->p_link;
                }
                if(p == NULL) 
                {
                    p = ini_type_link_new();
                    if(ini_head[strlen(ini_head) -1] == '\n')
                        ini_head[strlen(ini_head) -1] = 0x0;
                    strcpy(p->ini_head,ini_head);
                    if(p_head  == NULL) 
                    {
                        p_head = p ;
                    }
                    else 
                    {
                        q -> p_link = q ;
                    }
                }
                // 查询key
                struct key_value_link * p_key= p->p_value ;
                struct key_value_link * q_key= p->p_value ;
                while(p_key !=NULL) 
                {
                    if(strcmp(p_key -> ini_key ,ini_key) == 0)
                    {
                        strcpy(p_key -> ini_value ,ini_value);
                        break ;
                    }
                    q_key =  p_key ;
                    p_key = q_key ->p_link;
                }
                if(p_key == NULL) 
                {
                    p_key = key_value_link_new();
                    strcpy(p_key -> ini_key,ini_key);
                    strcpy(p_key -> ini_value ,ini_value);
                    if(p->p_value == NULL) 
                    {
                        p->p_value = p_key;
                    }
                    else 
                    {
                        q_key -> p_link = p_key ;
                    }
                }

            }
        }
     }

    return true;
}

static char *get_ini_value(char * ini_head , char * ini_key)
{
     struct ini_type_link * p = p_head ;
     if( p  != NULL) 
     {
         while(p != NULL) 
         {
             if(strcmp(p -> ini_head,ini_head)==0 )
             {
                 struct key_value_link * p_key = p->p_value ;
                 for(;p_key != NULL;p_key= p_key -> p_link)
                 {
                     if(strcmp(p_key->ini_key , ini_key) == 0) 
                     {
                         return p_key->ini_value ;
                     }
                 }
             }
             p = p->p_link;
         }
     }
     return NULL;
}

#endif  /*READ_CONF_COMMIT_C_H*/

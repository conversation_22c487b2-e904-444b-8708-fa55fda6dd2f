#ifndef __KAFKA_READER_H__
#define __KAFKA_READER_H__

#include <stdio.h>
#include <string.h>
#include <stdlib.h>

#include "librdkafka/rdkafka.h"
#include "no_lock_queue.h"
#include <string>
#include <unordered_map>
using namespace std;

typedef int (*KafkaReadCb)(void *data, int len, void *arg);

class CKafkaReaderArg
{
public:
    CKafkaReaderArg(int queLen = 64);
    ~CKafkaReaderArg();

    int bStop;
    rd_kafka_t *rk;
    NQueue *pRegistQueue;
    std::unordered_map<string, void *> cbMap;
};

class KafkaReader
{
public:
    //"earliest","latest","none"
    KafkaReader(const char *conf_path, const char *group, const char *offsetReset = "earliest", int waitSec = 10);
    ~KafkaReader();
    static void *pollLoop(void *arg);
    static int subscribe(CKafkaReaderArg *pArg, const char *topic, const char *group, int bDeleteGroupOffset);
    static int createTopic(CKafkaReaderArg *pArg, const char *topic);
    static int deleteGroupOffset(CKafkaReaderArg *pArg, const char *topic, const char *group);

    int isConstructOk();

    int regist(string topic, KafkaReadCb cb, void *arg, int bDeleteGroupOffset = 0);
private:
    int bEstablished(int waitSec);
    const char *group;
    rd_kafka_t *rk;
    pthread_t *ptLoop;
    CKafkaReaderArg *pArg;
};

#endif  /*PBWRITE_H*/

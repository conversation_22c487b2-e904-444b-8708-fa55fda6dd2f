// Last Update:2018-12-19 21:10:03
/**
 * @file proto_parse_session.h
 * @brief : 协议解析所用的session  
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-09-28
 */

#ifndef PROTO_PARSE_SESSION_H
#define PROTO_PARSE_SESSION_H
#include <packet.h>
#include <session_pub.h>
//#include "../common/resources_marge.h"
#include <TH_engine_interface.h>
#define  PLUGINSESSIONLEN 128
class proto_parse_session
{
    public:
        //uint32_t proto_parse_sign ;
        // tcp_recombine * tcp_recom[2];
        session_pub * p_session_pub ; // 指针回指 父节点
        //
        session_pasre_base * p_handle; // 解析插件指针
        char  expansion_data[PLUGINSESSIONLEN]; // 解析插件扩展session ,建立自己处理的session
        proto_parse_session(uint32_t i) 
        {
        }
        void init()
        {
            // tcp_recom[0] = NULL;
            // tcp_recom[1] = NULL ; 
            p_session_pub = NULL ;
            p_handle = NULL ;
        }
        void init(session_pub * p_session)
        {
            init();
            // if(p_session -> session_basic.Server == PACKETFROMCLIENT)
            // {

            //     pclient =p_session -> p_session_ext->clinet_repcom;
            //     pserver =p_session -> p_session_ext->server_repcom;
            // }
            // else
            // {
            //     pserver =p_session -> p_session_ext->clinet_repcom;
            //     pclient =p_session -> p_session_ext->server_repcom;
            // }
        }
};
//resources_marge<proto_parse_session >  pp_session_marge ; // proto_parse session 定义
#endif  /*PROTO_PARSE_SESSION_H*/

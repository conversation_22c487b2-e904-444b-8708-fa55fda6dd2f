#include "ZMPNMsg.pb.h"
#include "pb_oper_base.h"
#include <stdio.h>
class PB_CANmsg_Operator : public pb_oper_base{
    public:
        inline PB_CANmsg_Operator() 
        {
            msg_ = NULL;
            m_last_use_msg = NULL;
            b_biscard = false;
            b_stop = false;
            b_repeat = false;
        }
        inline ~PB_CANmsg_Operator()
        {
            if(msg_ != NULL)
            {
                delete msg_;
                msg_ = NULL;
            }
        }

        pb_oper_base* pb_oper_base_const()
        {
            return (pb_oper_base*) new PB_CANmsg_Operator();
        }


        //消息赋值
        PB_ERROR_CODE set_msg(Message *srcMsg);
        PB_ERROR_CODE parse_from_array(char *data, int len);
        void data_init()
        {
            if(msg_ == NULL)
            {
                msg_ = new CANmsg();
            }
        }
        //获取消息的总大小
        int byte_size();
        //序列化消息
        PB_ERROR_CODE serialize_to_array(char *buffer, int len);

        //新创建一个msg
        Message* allocateMsg(void)
        {
            return new CANmsg();
        }
        //释放msg
        void releaseMsg(Message* pmsg)
        {
            CANmsg*mmsg = (CANmsg*)pmsg;
            if(mmsg != NULL)
                delete mmsg;
        }
        pb_oper_base* clone()
        {
            pb_oper_base* nop = (pb_oper_base *)new PB_CANmsg_Operator(msg_);

            nop->data_handle_list = this->data_handle_list;
            nop->b_handle_num = this->b_handle_num; //
            nop->b_stop = this->b_stop; 
            nop->b_biscard = this->b_biscard; 
            nop->b_repeat = this->b_repeat; 

            return (pb_oper_base*) nop;
        }
        void set_func(ptr_get_fieldname_by_type func)
        {
        }

        //获取消息的type值
        int getMsgType();

        //获取字段的属性 optional/required/repeated
        PB_FIELD_PROTO getFieldProto(string fieldname);

        //获取字段的类型  INT32/INT64/string...
        PB_FIELD_TYPE  getFieldType(string fieldname);

        //获取字段的值，什么类型的值都转换为string
        string get_filed_value_string(string fieldname); 


        bool      get_value_bool(std::string fieldname);
        int       get_value_int(std::string fieldname);
        int64_t   get_value_int64(std::string fieldname);
        uint32_t  get_value_uint32(std::string fieldname);
        uint64_t  get_value_uint64(std::string fieldname);
        string    get_value_string(std::string fieldname);
        double    get_value_double(std::string fieldname);

        //获取字段的值，
        //由用户来保证存储数据变量的类型和其应有类型保持一致。
        //ex1:  Info f;
        //		getFieldValue("info", (void*)&f);
        //ex2:	float fl;
        //		getFieldValue("floatField", (void*)&fl);
        PB_ERROR_CODE getFieldValue(string fieldname, int32_t  &value, int index);
        PB_ERROR_CODE getFieldValue(string fieldname, int64_t  &value, int index);
        PB_ERROR_CODE getFieldValue(string fieldname, uint32_t &value, int index);
        PB_ERROR_CODE getFieldValue(string fieldname, uint64_t &value, int index);
        PB_ERROR_CODE getFieldValue(string fieldname, double   &value, int index);
        PB_ERROR_CODE getFieldValue(string fieldname, float    &value, int index);
        PB_ERROR_CODE getFieldValue(string fieldname, bool     &value, int index);
        PB_ERROR_CODE getFieldValue(string fieldname, string   &value, int index);
        //获得该字段的原始句柄
        PB_ERROR_CODE getFieldValue(string fieldname, Message* &value, int index);

        //修改字段的值，
        //由用户来保证赋值数据的类型和其应有类型保持一致。
        //ex1:  Info f;
        //		f.age = newValue;
        //		setFieldValue("info", (void*)&f);
        //ex2:	float fl = newValue;
        //		setFieldValue("floatField", (void*)&fl);
        PB_ERROR_CODE setFieldValue(string fieldname, int32_t  value, int index);
        PB_ERROR_CODE setFieldValue(string fieldname, int64_t  value, int index);
        PB_ERROR_CODE setFieldValue(string fieldname, uint32_t value, int index);
        PB_ERROR_CODE setFieldValue(string fieldname, uint64_t value, int index);
        PB_ERROR_CODE setFieldValue(string fieldname, double   value, int index);
        PB_ERROR_CODE setFieldValue(string fieldname, float    value, int index);
        PB_ERROR_CODE setFieldValue(string fieldname, bool     value, int index);
        PB_ERROR_CODE setFieldValue(string fieldname, char    *value, int index);
        PB_ERROR_CODE setFieldValue(string fieldname, string  &value, int index);
        PB_ERROR_CODE setFieldValue(string fieldname, Message *value, int index);

        //获取repeated属性字段值的个数
        PB_ERROR_CODE getFieldListCount(string fieldname, int &len);


        //向repeated字段中增加一条值
        //由用户来保证value的类型和其应有类型保持一致。
        PB_ERROR_CODE addFieldListValue(string fieldname, int32_t  value);
        PB_ERROR_CODE addFieldListValue(string fieldname, int64_t  value);
        PB_ERROR_CODE addFieldListValue(string fieldname, uint32_t value);
        PB_ERROR_CODE addFieldListValue(string fieldname, uint64_t value);
        PB_ERROR_CODE addFieldListValue(string fieldname, double   value);
        PB_ERROR_CODE addFieldListValue(string fieldname, float    value);
        PB_ERROR_CODE addFieldListValue(string fieldname, bool     value);
        PB_ERROR_CODE addFieldListValue(string fieldname, string  &value);
        PB_ERROR_CODE addFieldListValue(string fieldname, Message *value);

        //将repeated字段中原有值删除，并增加新值value
        //由用户来保证value的类型和其应有类型保持一致。
        PB_ERROR_CODE setFieldListValue(string fieldname, int32_t  value);
        PB_ERROR_CODE setFieldListValue(string fieldname, int64_t  value);
        PB_ERROR_CODE setFieldListValue(string fieldname, uint32_t value);
        PB_ERROR_CODE setFieldListValue(string fieldname, uint64_t value);
        PB_ERROR_CODE setFieldListValue(string fieldname, double   value);
        PB_ERROR_CODE setFieldListValue(string fieldname, float    value);
        PB_ERROR_CODE setFieldListValue(string fieldname, bool     value);
        PB_ERROR_CODE setFieldListValue(string fieldname, string   &value);
        PB_ERROR_CODE setFieldListValue(string fieldname, Message *value);

    private:
        //根据字段名查找该字段所在的message.
        bool findByFieldName(string &fieldname, Message *&s_msg, FieldDescriptor *&s_field);
        PB_CANmsg_Operator(CANmsg *msg)
        {
            msg_  = new CANmsg(*msg);
            m_last_use_msg = NULL;
        }

        CANmsg *msg_;
        Message *m_last_use_msg;
};

extern "C" {
    //插件加载
    pb_oper_base *attach_pb();

    void detach_pb(pb_oper_base* handle);
}

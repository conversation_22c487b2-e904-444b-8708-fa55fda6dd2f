// Last Update:2019-06-01 11:04:57
/**
 * @file hash.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-05-31
 */

#ifndef HASH_H
#define HASH_H
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

/*此方法采用的是连地址法*/

/*为了不将hash结构暴露在外面，所以就必须typedef*/
//这是一个hash函数指针，对用户来说应该需要使用一个合适的hash函数
typedef unsigned int(*hash_func_t)(unsigned int,void *);
//必须在前面声明typedef
typedef struct hash_node {
    void *key;//查找依据
    void *data;//数据块，一般是结构体
    struct hash_node *prev;
    struct hash_node *next;
    unsigned int key_size;
}hash_node_t;
struct hash_ {
    unsigned int buckets;//桶的个数（大小）
    hash_func_t hash_func;//hash函数指针
    hash_node_t **nodes;//hash表中存放的链表地址
};
typedef struct hash_ hash_t;
//第一个参数unsigned int指的是桶的大小 第二个参数是key值

/*返回的是hash表指针,创建hash表*/
hash_t *hash_alloc(unsigned int,hash_func_t);
void *hash_lookup_entry(hash_t *,void *,unsigned int );

void hash_add_entry(hash_t *, void *, unsigned int ,
    void *, unsigned int );
void hash_free_entry(hash_t *,void *,int );



#endif  /*HASH_H*/

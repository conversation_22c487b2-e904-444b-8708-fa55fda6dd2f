// Last Update:2019-07-01 17:10:01
/**
 * @file extjson_base.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-07-01
 */

#ifndef EXTJSON_BASE_H
#define EXTJSON_BASE_H

#define EXTJSON_DIR "ExtJson/"
#define EXTJSON_FUNCTIONNAME "attach"
#include <session_pub.h>
//typedef void (* exttjson_handle)(std::string & json_str) ;
class exttjson_handle_base
{
    public:
        // 插件资源回收 
        virtual ~exttjson_handle_base()
        {
        }
        // 初始化 
        virtual  void init(int max_thead) = 0;
        // packet 包处理
        virtual   void handle (session_pub * p_session ,c_packet * p_packet , int  thread_num )  = 0 ;
        // send 发送数据   
        virtual  std::string  send(session_pub *p_session ,int  thread_num) = 0;
        // session 结束
        virtual  void session_end(session_pub * p_session , int thread_num) ;
};



#endif  /*EXTJSON_BASE_H*/

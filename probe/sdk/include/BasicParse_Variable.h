#ifndef __BASICPARSE_VAR_H__
#define __BASICPARSE_VAR_H__
#include <string>
using namespace std;

#define MAX_DEFENSE_TYPE 50
#define MAX_THREAD_NUM 64

extern "C"
{
    typedef struct 
    {
    //----------------offline_statistics
        uint64_t total_pkts;
        uint64_t drop_decode_pkts;
        uint64_t drop_filter_pkts;
        uint64_t drop_filter_bytes;
        uint64_t drop_defense_pkts[MAX_DEFENSE_TYPE];
        uint64_t rule_hit_pkts; //规则命中包数
        uint64_t rule_hit_bytes; //规则命中数据量
        uint64_t alarm_num[3]; //告警数量 0:规则 1:防御 2:模型
    //----------------offline_statistics --end
        uint64_t drop_defense_bytes_loop[MAX_DEFENSE_TYPE];
        uint32_t drop_defense_pkts_loop[MAX_DEFENSE_TYPE];
        uint64_t filter_bytes_loop[10001];
        uint32_t filter_pkts_loop[10001];
        uint64_t sa_bytes;
        uint64_t sa_pkts;
        uint64_t sa_bytes_loop;
        uint32_t sa_pkts_loop;
    } TH_PKT_SUM;
    extern TH_PKT_SUM gBVar_pkt_sum[MAX_THREAD_NUM];
}
extern long long gBVarTaskId;
extern uint32_t gFilterRuleMap[10001];
extern uint32_t gFilterRuleNum;

extern bool gBConf_noip_record;

#endif
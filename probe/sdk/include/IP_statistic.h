// Last Update:2018-09-25 15:24:15
/**
 * @file IP_statistic.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-09-11
 */

#ifndef _I_P_STATISTIC_H
#define _I_P_STATISTIC_H
#include "PacketDefine.h"

typedef struct _STR_SHOWINFOR_PLUS_IPSTATISTIC
{
    DWORD StartTime[2];
    //0 源、目的都为内网IP
    //1 源、为内网IP
    //2 目的都为内网IP
    //3 源、目的都不是内网IP
    UINT64 PacketNum[4];
    UINT64 PacketBytes[4];
    UINT64 TotalPacket;
    UINT64 TotalBytes;
    //当前IP节点数
    UINT64 Cul_IPNode;
    //新增IP节点数
    UINT64 Suc_New_IPNode;
    UINT64 Failed_New_IPNode;
    //删除IP节点数
    UINT64 TimeOut_IPNode;
    //新建连接数
    DWORD New_ConnectNum;


    //丢弃的字节数和包数
    UINT64 Packet_Drop;
    UINT64 Bytes_Drop;

    //丢弃的字节数和包数
    UINT64 Packet_DDOS;
    UINT64 Bytes_DDOS;

}STR_SHOWINFOR_PLUS_IPSTATISTIC;
typedef struct _STR_NODE_PLUS_INTRANET
{
    DWORD IP;
    DWORD Mask;

    //标志：若为DMZIP，设置0x100;其它为零
    DWORD DMZ;

    //接收、发送的数据包数量和字节数
    UINT64 PacketNum[2];
    UINT64 PacketBytes[2];
}STR_NODE_PLUS_INTRANET;
typedef  struct  GetPacketInfo
{
};
void Func_ToJson_Plus_Statistics(STR_PACKET_PLUS_IPSTATISTIC IN_TotalInfor[2],Json::Value &OUT_Value);
void Func_ToJson_Plus_Statistics(STR_SHOWINFOR_PLUS_IPSTATISTIC &IN_ShowInfor,STR_PACKET_PLUS_IPSTATISTIC IN_TotalInfor[2],CArrayBasic<STR_NODE_PLUS_INTRANET> *IN_pArray,Json::Value &OUT_Value);

#endif  /*_I_P_STATISTIC_H*/

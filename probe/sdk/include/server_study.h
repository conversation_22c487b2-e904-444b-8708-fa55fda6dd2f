#ifndef __SERVER_STUDY_H__
#define __SERVER_STUDY_H__

#include <string>
using namespace std;

#include <stdint.h>
#include <stdio.h>
#include <inttypes.h>
#include "xml_parse.h"


#define PACKETFROMCLIENT 1
#define PACKETFROMSERVER 2
#define PACKETFROMUNKOWN 0


class server_study
{
    public:
        server_study()
        {
            max_serverport = 3000;
            min_clientport = 20000;
            times_s2c = 4;
            min_effect = 1000;
            dpi_weight = 10;
            other_weight = 1;
            
            def_tcp_port_map = new uint64_t[65536]();
            def_udp_port_map = new uint64_t[65536]();
            tcp_port_map = new uint64_t[65536]();
            udp_port_map = new uint64_t[65536]();
        }
        ~server_study()
        {
            if(def_tcp_port_map)
            {
                delete []def_tcp_port_map;
                def_tcp_port_map = NULL;
            }
            if(def_udp_port_map)
            {
                delete []def_udp_port_map;
                def_udp_port_map = NULL;
            }
            if(tcp_port_map)
            {
                delete []tcp_port_map;
                tcp_port_map = NULL;
            }
            if(udp_port_map)
            {
                delete []udp_port_map;
                udp_port_map = NULL;
            }
        }
        
        void log_study_info()
        {
            FILE *pfile = fopen(tcp_tmp_path.c_str(), "w");
            if (pfile)
            {
                for(uint32_t i=0;i < 65536; i++)
                {
                    if(tcp_port_map[i] > 0)
                    {
                        fprintf(pfile, "%u:%"PRIu64"\n", i, tcp_port_map[i]);
                    }
                }
                fclose(pfile);
                rename(tcp_tmp_path.c_str(), tcp_map_path.c_str());
            }
            pfile = fopen(udp_tmp_path.c_str(), "w");
            if (pfile)
            {
                for(uint32_t i=0;i < 65536; i++)
                {
                    if(udp_port_map[i] > 0)
                    {
                        fprintf(pfile, "%u:%"PRIu64"\n", i, udp_port_map[i]);
                    }
                }
                fclose(pfile);
                rename(udp_tmp_path.c_str(), udp_map_path.c_str());
            }
        }
        
        void conf_parse()
        {
            xml_parse xml;
            
            tcp_map_path = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/statistics/tcp_port.map";
            udp_map_path = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/statistics/udp_port.map";
            tcp_tmp_path = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/statistics/.tcp_port.map.tmp";
            udp_tmp_path = "/data/"+string(getenv("THE_TASKID"))+"/"+string(getenv("THE_BATCHID"))+"/statistics/.udp_port.map.tmp";
            
            string conf_path = string(getenv("THE_CONF_PATH")) + "/server_study.xml";
            xml.set_file_path(conf_path.c_str());
            char * p_value = (char *)xml.get_value("/config/server_port");
            if(p_value)
            {
                uint16_t num = (uint16_t)atoi(p_value);
                max_serverport = num;
            }
            p_value = (char *)xml.get_value("/config/client_port");
            if(p_value)
            {
                uint16_t num = (uint16_t)atoi(p_value);
                min_clientport = num;
            }
            p_value = (char *)xml.get_value("/config/times_s2c");
            if (p_value)
            {
                uint16_t num = (uint16_t)atoi(p_value);
                times_s2c = num;
            }
            p_value = (char *)xml.get_value("/config/study_effect");
            if (p_value)
            {
                uint16_t num = (uint16_t)atoi(p_value);
                min_effect = num;
            }
            p_value = (char *)xml.get_value("/config/dpi_weight");
            if (p_value)
            {
                uint16_t num = (uint16_t)atoi(p_value);
                dpi_weight = num;
            }
            p_value = (char *)xml.get_value("/config/other_weight");
            if (p_value)
            {
                uint16_t num = (uint16_t)atoi(p_value);
                other_weight = num;
            }

            conf_path = string(getenv("THE_DB_PATH")) + "/tcp_port.map";
            FILE *pfile = fopen(conf_path.c_str(), "r");
            if (pfile)
            {
                int len;
                fseek(pfile,0,SEEK_END);
                len = ftell(pfile);
                fseek(pfile,0,SEEK_SET);
                if (len > 0)
                {
                    unsigned int port;
                    uint64_t count;
                    while(2 == fscanf(pfile, "%u:%"PRIu64"", &port, &count))
                    {
                        def_tcp_port_map[port] = count;
                    }
                }
                fclose(pfile);
            }
            conf_path = string(getenv("THE_DB_PATH")) + "/tcp_port.map";
            pfile = fopen(conf_path.c_str(), "r");
            if (pfile)
            {
                int len;
                fseek(pfile,0,SEEK_END);
                len = ftell(pfile);
                fseek(pfile,0,SEEK_SET);
                if (len > 0)
                {
                    unsigned int port;
                    uint64_t count;
                    while(2 == fscanf(pfile, "%u:%"PRIu64"", &port, &count))
                    {
                        def_udp_port_map[port] = count;
                    }
                }
                fclose(pfile);
            }
            pfile = fopen(tcp_map_path.c_str(), "r");
            if (pfile)
            {
                int len;
                fseek(pfile,0,SEEK_END);
                len = ftell(pfile);
                fseek(pfile,0,SEEK_SET);
                if (len > 0)
                {
                    unsigned int port;
                    uint64_t count;
                    while(2 == fscanf(pfile, "%u:%"PRIu64"", &port, &count))
                    {
                        if(port < 65536)
                        {
                            tcp_port_map[port] = count;
                        }
                        else
                        {
                            break;
                        }
                    }
                }
                fclose(pfile);
            }
            pfile = fopen(udp_map_path.c_str(), "r");
            if (pfile)
            {
                int len;
                fseek(pfile,0,SEEK_END);
                len = ftell(pfile);
                fseek(pfile,0,SEEK_SET);
                if (len > 0)
                {
                    unsigned int port;
                    uint64_t count;
                    while(2 == fscanf(pfile, "%u:%"PRIu64"", &port, &count))
                    {
                        if(port < 65536)
                        {
                            udp_port_map[port] = count;
                        }
                        else
                        {
                            break;
                        }
                    }
                }
                fclose(pfile);
            }
        }
        
        uint8_t judge_c2s_by_study(uint8_t Server, uint8_t IPPro, uint16_t pPort[2])
        {
            if(Server ==  PACKETFROMUNKOWN)
            {
                if(pPort[0] >= min_clientport && pPort[1] <= max_serverport)
                {
                    return PACKETFROMCLIENT;
                }
                else if (pPort[1] >= min_clientport && pPort[0] <= max_serverport)
                {
                    return PACKETFROMSERVER;
                }
                else
                {
                    uint64_t port_count0 = 0;
                    uint64_t port_count1 = 0;
                    if(IPPro == 6)
                    {
                        port_count0 = def_tcp_port_map[pPort[0]] + tcp_port_map[pPort[0]];
                        port_count1 = def_tcp_port_map[pPort[1]] + tcp_port_map[pPort[1]];
                    }
                    else if(IPPro == 17)
                    {
                        port_count0 = def_udp_port_map[pPort[0]] + udp_port_map[pPort[0]];
                        port_count1 = def_udp_port_map[pPort[1]] + udp_port_map[pPort[1]];
                    }
                    if((port_count0 < port_count1) && (port_count1 >= min_effect) && (port_count0 * times_s2c < port_count1))
                    {
                        return PACKETFROMCLIENT;
                    }
                    else if((port_count0 > port_count1) && (port_count0 >= min_effect) && (port_count0 > port_count1 * times_s2c))
                    {
                        return PACKETFROMSERVER;
                    }
                    else
                    {
                        return PACKETFROMCLIENT;
                    }
                }
            }
            else
            {
                return Server;
            }
        }
        
        void study_c2s_by_port(uint8_t Server, uint8_t IPPro, uint16_t pPort[2], uint8_t not_dpi_server)
        {
            if(not_dpi_server)
            {
                if(IPPro == 6)
                {
                    tcp_port_map[pPort[0]] += other_weight;
                    tcp_port_map[pPort[1]] += other_weight;
                }
                else if(IPPro == 17)
                {
                    udp_port_map[pPort[0]] += other_weight;
                    udp_port_map[pPort[1]] += other_weight;
                }
            }
            else if(Server == PACKETFROMCLIENT)
            {
                if(IPPro == 6)
                {
                    tcp_port_map[pPort[1]] += dpi_weight;
                }
                else if(IPPro == 17)
                {
                    udp_port_map[pPort[1]] += dpi_weight;
                }
            }
            else if(Server == PACKETFROMSERVER)
            {
                if(IPPro == 6)
                {
                    tcp_port_map[pPort[0]] += dpi_weight;
                }
                else if(IPPro == 17)
                {
                    udp_port_map[pPort[0]] += dpi_weight;
                }
            }
        }
    private:
        uint64_t *def_tcp_port_map;
        uint64_t *def_udp_port_map;
        uint64_t *tcp_port_map;
        uint64_t *udp_port_map;
        uint16_t max_serverport;
        uint16_t min_clientport;
        uint16_t times_s2c;
        uint16_t min_effect;
        uint16_t dpi_weight;
        uint16_t other_weight;
        string tcp_map_path;
        string udp_map_path;
        string tcp_tmp_path;
        string udp_tmp_path;
};

#endif

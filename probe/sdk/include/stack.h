// Last Update:2018-10-10 22:29:28
/**
 * @file stack.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-08-26
 */

#ifndef STACK_H
#define STACK_H

#include<stdio.h>
#include<stdlib.h>
#include<malloc.h>

typedef int Status;     //定义状态    
typedef void* ElemType;      //定义栈内元素类型
 
//定义栈的的数据结构
typedef struct {
    ElemType *base;     //栈底指针
    ElemType *top;        //栈顶指针
    int stackSize;        // 栈大小
    int realSize;        // 栈当前大小,可以不定义
    int size ;
    char * p_buf;
}SqStack;
//初始化一个栈
Status InitStack(SqStack *sqstack,int size , int num ,char * p_buf );
//进栈
Status Push(SqStack *sqstack,ElemType e);
//出栈
Status Pop(SqStack *sqstack,ElemType *e);
//得到栈顶元素
Status GetTop(SqStack *sqstack,ElemType* e);
//判断栈是否为空
int IsEmpty(SqStack *sqstack);
//销毁栈
Status DestroyStack(SqStack *sqstack);
//得到栈的元素个数
int StackLength(SqStack *sqstack);
#endif  /*STACK_H*/

// Last Update:2019-09-02 11:32:16
/**
 * @file session.h
 * @brief session 的定义
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-09-02
 */
#ifndef SESSION_PUB_H
#define SESSION_PUB_H
#include <string>
#include <stack>
#include <packet.h>
#include <tcp_fragment_list.h>
#include <tcp_fragment_reassembly.h>
#include <json/json.h>
#include "th_engine_tools.h"
#include "session_pub_basic.h"
#include "session_pub_proto.h"
#include "th_labels_define.h"
#include "SetVectorUnion.h"
//#include <common/resources_marge.h>
//#include <Interface_Statistics.h>
#define MAXRULENUM 1000000
#define MAX_PACKET_RULE_HIT 32   // 能命中的最大包数
#define PB_MAX_PROTO_NUM 100        //PB会话中最多的协议元数据个数wangxiang
#define JSON_MAX_PROTO_NUM 10       //JSON会话中最多的协议元数据个数wangxiang
//class CInterface_Statistics ;

class session_pub_ext
{
public:
    session_pub_ext()
    {
        ext_array_id = 0;
        init();
    }
    session_pub_ext(uint32_t num)
    {
        ext_array_id = num;
        init();
    }
    ~session_pub_ext()
    {
        init();
    }
    void init()
    {
        tcp_link[0] = NULL;
        tcp_link[1] = NULL;
        tcp_repcom[0] = NULL;
        tcp_repcom[1] = NULL;
        p_pff_session = NULL;
        app_c_finger = 0;
        app_s_finger = 0;
    }
    TcpFragmentList         *tcp_link[2];
    TcpFragmentReassembly   *tcp_repcom[2] ; // 组包
    void                    *p_pff_session ; // packet_full_flow session
    uint64_t app_c_finger;
    uint64_t app_s_finger;
    uint32_t ext_array_id;        //同源数组
};

class session_pub
{
public:
    session_pub()
    {
        session_basic.ConnectID = 0;
        p_session_statistics = NULL;
        p_tcp_recom_pop_cb = NULL;
        p_tcp_recom_push_cb = NULL;
        get_appname = NULL;
        domain_judge_cb = NULL;
        target_scan_handle_cb = NULL;
        init();
    }
    session_pub(uint32_t num)
    {
        session_basic.ConnectID = num;
        p_session_statistics = NULL;
        p_tcp_recom_pop_cb = NULL;
        p_tcp_recom_push_cb = NULL;
        get_appname = NULL;
        domain_judge_cb = NULL;
        target_scan_handle_cb = NULL;
        init();
    }
    ~session_pub()
    {
        init();
    }

    STR_CONNECTINFORV4_BASIC    session_basic;
    STR_PROTOCOLSTACK           ProtocolStack;
    c_ip        first_src_ip;
    c_ip        first_dst_ip;
    c_ip        proxy_real_ip;
    uint64_t    pkt_payloadbytes[2];
    uint64_t    srcmac;
    uint64_t    dstmac;
    CMessage    *p_value ; // 生成的session
    void        *p_session_statistics;
    void        *p_md_tcp;              //clean before push
    session_pub_ext *p_session_ext;     //clean before push
    void        *p_sp_session ;         //clean before push
    //callback
    char* (*get_appname)(int app_id);                                       //only init once
    void (*p_tcp_recom_pop_cb)(session_pub *p_sess_pub, uint8_t Directory); //only init once
    void (*p_tcp_recom_push_cb)(session_pub *p_sess_pub, uint8_t Directory); //only init once
    int (*domain_judge_cb)(uint32_t thread_id, const char *domain); //only init once
    void (*target_scan_handle_cb)(std::string pkt, int thid , int &black , int & white, int & important );//only init once
    //callback -end
    std::string m_session_ext_str ; // string 扩展字段
    uint32_t    thread_id;
    uint32_t    task_id;
    uint32_t    realtime_engine_version;
    uint32_t    first_proto;
    uint32_t    packet_sign[MAX_PACKET_RULE_HIT];
    uint32_t    StartClock;
    uint32_t    EndClock;
    uint32_t    icmp_types;
    uint32_t    app_req_num;
    uint32_t    app_resp_num;
    uint16_t    proxy_real_port;
    uint16_t    port_map;
    uint8_t     b_first_ip;
    uint8_t     b_has_proxy;
    uint8_t     b_has_mac;
    uint8_t     do_session_pb;  //  是否写日志  1 表示写  0 表示不写
    uint8_t     b_black_ssl;    //  是否命中证书黑名单
    uint8_t     did_realtime_match;
    uint8_t     packet_sign_num;
    uint8_t     b_unknown_c2s;
    uint8_t     app_pkt_id;
    uint8_t     labels[(LABEL_END+7)/8];
    uint8_t     tls_app_len[8];
    uint8_t     tls_app_len_num;
    vector<session_pub_http>    http;
    CSetVectorUnion<session_pub_dns> dns;
    vector<session_pub_ssl>     ssl;

    void reset_basic()
    {
        session_basic.ConnectKeyID = 0;
        session_basic.pPort[0] = 0;
        session_basic.pPort[1] = 0;
        session_basic.IPPro = 0;
        session_basic.Server = PACKETFROMUNKOWN;
        session_basic.pPacketNum[0] = 0;
        session_basic.pPacketNum[1] = 0;
        session_basic.pPayloadNum[0] = 0;
        session_basic.pPayloadNum[1] = 0;
        session_basic.pPacketBytes[0] = 0;
        session_basic.pPacketBytes[1] = 0;
        session_basic.RuleNum = 0;
        session_basic.RuleLevel = 0;
        session_basic.StartTime[0] = 0;
        session_basic.StartTime[1] = 0;
        session_basic.EndTime[0] = 0;
        session_basic.EndTime[1] = 0;
        session_basic.ProtoSign = 0;
        session_basic.IO_Sign[0] = 0;
        session_basic.IO_Sign[1] = 0;
        session_basic.ExtString = "";
        session_basic.pSessionPub = this;
    }

    void init()
    {
        reset_basic();
        memset(&ProtocolStack,0x0,sizeof(STR_PROTOCOLSTACK));
        pkt_payloadbytes[0] = 0;
        pkt_payloadbytes[1] = 0;
        srcmac = 0;
        dstmac = 0;
        p_value = NULL;
        if(p_session_statistics)
        {
            SessionReset(this);
        }
        p_md_tcp = NULL;
        p_session_ext = NULL;
        p_sp_session = NULL;
        m_session_ext_str = "";
        thread_id = 0;
        task_id = 0;
        realtime_engine_version = 0;
        first_proto = PROTOCOL_ETH;
        StartClock = 0;
        EndClock = 0;
        icmp_types = 0;
        app_req_num = 0;
        app_resp_num = 0;
        proxy_real_port = 0;
        port_map = 0;
        b_first_ip = 0;
        b_has_proxy = 0;
        b_has_mac = 0;
        do_session_pb = 1;
        b_black_ssl = 0;
        did_realtime_match = 0;
        packet_sign_num = 0;
        b_unknown_c2s= 0;
        app_pkt_id = 0;
        memset(labels, 0, (LABEL_END+7)/8);
        memset(tls_app_len, 0, 8);
        tls_app_len_num = 0;
        http.clear();
        dns.clear();
        ssl.clear();
    }
    void init(c_packet* p_packet)
    {
        init();
        session_basic.pIP[0]  = p_packet -> src_ip;
        session_basic.pIP[1]  = p_packet -> dst_ip;
        session_basic.pPort[0] = p_packet -> src_port ;
        session_basic.pPort[1] = p_packet -> dst_port ;
        if(p_packet->has_mac())
        {
            b_has_mac = 1;
            srcmac = p_packet ->  get_src_mac() ;
            dstmac = p_packet ->  get_dst_mac() ;
        }
        STR_PROTOCOLSTACK *pPro=&p_packet->m_str_packet_moudle.Stack;
        if (pPro->FirstIP && (pPro->FirstIP != pPro->LastIP))
        {
            b_first_ip = 1;
            first_src_ip = p_packet->first_src_ip;
            first_dst_ip = p_packet->first_dst_ip;
        }
        session_basic.IPPro = p_packet -> u_tcp ;
        p_value = p_packet -> p_value ;
        session_basic.StartTime[0] = p_packet  -> time_ts[0];
        session_basic.StartTime[1] = p_packet  -> time_ts[1];
        session_basic.EndTime[0] = p_packet  -> time_ts[0];
        session_basic.EndTime[1] = p_packet  -> time_ts[1];
        StartClock = p_packet->clock_ts[0];
        EndClock = p_packet->clock_ts[0];
        first_proto = (uint32_t)p_packet->first_proto;
    }
};
static int session_pub_push_http(session_pub * p_session, session_pub_http *p_http)
{
    if(p_session->http.size() < PB_MAX_PROTO_NUM)
    {
        p_session->http.push_back(*p_http);
    }
};
static int session_pub_push_dns(session_pub * p_session, session_pub_dns *p_dns)
{
    if(p_session->dns.size() < PB_MAX_PROTO_NUM)
    {
        p_session->dns.push_back(*p_dns);
    }
};
static int session_pub_push_ssl(session_pub * p_session, session_pub_ssl *p_ssl)
{
    if(p_session->ssl.size() < PB_MAX_PROTO_NUM)
    {
        p_session->ssl.push_back(*p_ssl);
    }
};
static void  commsg_fill(session_pub * p_session , Comm_msg* p_comm,std::string app_id, void *p_th_tools)
{
    th_engine_tools *tools = (th_engine_tools *)p_th_tools;
    // 客户端 IP-
    if (p_session -> session_basic.Server ==  PACKETFROMUNKOWN)
    {
        p_session -> session_basic.Server = tools->judge_c2s_by_study(p_session -> session_basic.Server, p_session -> session_basic.IPPro, p_session -> session_basic.pPort);
        p_session -> b_unknown_c2s = 1;
    }
    if(p_session -> session_basic.Server == PACKETFROMCLIENT)
    {
        if(p_session->b_has_proxy)
        {
            p_comm ->set_dst_ip(p_session->proxy_real_ip.ip_str());
            p_comm ->set_dst_port(p_session->proxy_real_port);
        }
        else
        {
            p_comm ->set_dst_ip(p_session->session_basic.pIP[1].ip_str());
            p_comm ->set_dst_port(p_session->session_basic.pPort[1]);
        }
        p_comm ->set_src_ip(p_session->session_basic.pIP[0].ip_str());
        p_comm ->set_src_port(p_session->session_basic.pPort[0]);
        p_comm ->set_server_ip(p_session->session_basic.pIP[1].ip_str());
    }
    else
    {
        if(p_session->b_has_proxy)
        {
            p_comm ->set_dst_ip(p_session->proxy_real_ip.ip_str());
            p_comm ->set_dst_port(p_session->proxy_real_port);
        }
        else
        {
            p_comm ->set_dst_ip(p_session->session_basic.pIP[0].ip_str());
            p_comm ->set_dst_port(p_session->session_basic.pPort[0]);
        }
        p_comm ->set_src_ip(p_session->session_basic.pIP[1].ip_str());
        p_comm ->set_src_port(p_session->session_basic.pPort[1]);
        p_comm ->set_server_ip(p_session->session_basic.pIP[0].ip_str());
    }

    p_comm ->set_ippro(p_session->session_basic.IPPro);
    //printf("src_port  === %d \n", ntohs(p_session->srcport));
    p_comm ->set_begin_time(p_session->session_basic.StartTime[0]);
    p_comm ->set_begin_nsec(p_session->session_basic.StartTime[1]);
    if (p_session->session_basic.ProtoSign > 0) //must
    {
        p_comm->set_app_id(p_session->session_basic.ProtoSign);
    }
    else
    {
        p_comm->set_app_id(10000);
    }
    char* p_app_name = p_session->get_appname(p_session->session_basic.ProtoSign);
    string str_appname(p_app_name);
    p_comm->set_app_name(str_appname);
    std::string str_session_id = "";
    char str_session_id_c[128];
    sprintf(str_session_id_c,"%llu%c",(long long unsigned int)p_session -> session_basic.ConnectKeyID,0x0);
    str_session_id = str_session_id_c ;
    p_comm -> set_session_id(str_session_id);
    p_comm -> set_thread_id(p_session->thread_id);
    p_comm -> set_task_id((unsigned int)tools->task_id);
    p_comm -> set_batch_id((unsigned int)tools->batch_id);
}
#endif  /*SESSION_H*/

// Last Update:2018-10-09 22:04:02
/**
 * @file Define_PacketInfor_Include.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-10-09
 */

#ifndef _DEFINE__PACKET_INFOR__INCLUDE_H
#define _DEFINE__PACKET_INFOR__INCLUDE_H
#include <stdint.h>
#include "session_pub_basic.h"
#include <Define_CulEnvironment_Include.h>
#define MAXRULENUM_ENGINE				16	//规则数量
#define MAXRULENUM_RESPOND				8	//规则数量
///////////////////////////////////////////////////////////////////协议解析时的信息
//协议栈中每个节点的信息

typedef struct _STR_IPv6CONNECT
{
	//端口--逆序存放,不存在为零
	WORD Port[2];
	//IP--正序存放
	unsigned char IP[2][16];
	//协议类型
	unsigned char Protocol;

}STR_IPv6CONNECT;
typedef struct _STR_IPv4CONNECT
{
	//端口--逆序存放,不存在为零
	WORD Port[2];
	//IP--逆序存放
	DWORD IP[2];
	//协议类型
	unsigned char Protocol;

}STR_IPv4CONNECT;
//20160604 修改
//原来为Union,有一个问题：STR_IPv4CONNECT中有3字节的对齐空间，当出现IPv6连接时，该对齐空间被占用，后面的IPv4连接在进行数据匹配时memcpy就不对了
typedef struct _STR_CONNECT
{
	STR_IPv4CONNECT ConnectV4;
	STR_IPv6CONNECT ConnectV6;
}STR_CONNECT;
//协议栈空间最大值
#define MAXSTACKNUM 16



//不使用指针，是的数据复制后，解析的各协议信息依然可用
typedef struct _STR_PROTOCOLINFOR
{
	WORD Offset;          //从数据起始指针到当前协议的偏移
	WORD Len;             //协议头长度,不包括数据
	DWORD Protocol;        //协议ID
	DWORD Property;		//当前协议属性,用于区分协议细节
						//高
	DWORD Sign;			//暂时保留，一个结构体16字节，便于指针对齐

}STR_PROTOCOLINFOR;



//协议栈空间最大值
#define MAXSTACKNUM 16

typedef struct _STR_PROTOCOLSTACK
{
	unsigned char *pStart;						 //数据包头，用户初始化
	unsigned char *pEnd;						 //数据包尾，用户初始化
	DWORD TimeStamp[2];							 //时间戳，用户初始化	0:秒 ; 1:ns;


	STR_PROTOCOLINFOR pProtocol[MAXSTACKNUM];       //协议栈, 0为最底层协议
	unsigned char ProtocolNum;						 //当前栈内节点数量，用户初始化为零
	unsigned char IPProNum;					//IP协议数量
	unsigned char FirstIP;					//首个IP协议的编号+1，初始为零，IP协议在协议栈的位置
	unsigned char LastIP;					//末IP协议的编号位置+1，初始为零

	DWORD TotalSign;					 //包标识，协议标识的汇总，用户初始化为零,各协议Sign值得或
} STR_PROTOCOLSTACK;


typedef struct _STR_PACKETINFOR_MUDULE_CONNECT_V2
{
	//包编号，唯一的标识，便于测试
	UINT64 PacketID;

	//协议栈
	STR_PROTOCOLSTACK Stack;
	//应用协议ID
	DWORD ProID;

	/////////////////规则
	DWORD pRuleID[MAXRULENUM_ENGINE];
	WORD OffsetTo[MAXRULENUM_ENGINE];
	WORD OffsetFrom[MAXRULENUM_ENGINE];
	DWORD RuleNum;
	DWORD pRespondRuleID[MAXRULENUM_RESPOND];
	BYTE  RespondRuleFlag[MAXRULENUM_RESPOND];	// 1：单包需要规则响应
	DWORD RespondRuleNum;
	//规则level的最大值
	DWORD Level;
	/////////////////规则--End

	//////////////////五元祖
	//连接信息
	STR_CONNECT Connect;
	//连接类型:0 未知； PROTOCOL_IPV6 ；PROTOCOL_IP
	unsigned char ConnectType;
	//服务器  0 未知，1 IP[0]服务端；2 IP[1]为服务器
	//UNKNOWN_SERVER_RULEENGINE
	unsigned char Server;

	//////////////////五元祖--End

	//////////////////Session回写
	//session中的方向，0 Session中源IP发送； 1 Session中目的IP发送
	unsigned char Directory;
	//是否新建连接 0 非Session首包；1 Session首包
	unsigned char IsFirstPacket;
	//所属的SessionID，用于判断是否存在Session
	UINT64 session_array_id;
	//////////////////Session回写--End

}STR_PACKETINFOR_MUDULE_CONNECT_V2;


typedef struct _STR_PARAM
{
	DWORD SessionVer;	//CONNECTINFORV4_BASIC_VERSION 用于检测，输入参数版本是否发生变化
	STR_PACKETINFOR_MUDULE_CONNECT_V2 *pPacket;
	STR_CONNECTINFORV4_BASIC *pSession;
}STR_PARAM;


#define DYNAMIC_LIB_VER 20220420
#define MAX_CONF_PATH 256

typedef struct _STR_INIT_PARAM
{
    DWORD Ver;                          //DYNAMIC_LIB_VER 用于检测，输入参数版本是否发生变化
    char ConfigDir[MAX_CONF_PATH];      //配置文件所在路径，基于此路径进行配置文件读取
}STR_INIT_PARAM;

#endif  /*_DEFINE__PACKET_INFOR__INCLUDE_H*/

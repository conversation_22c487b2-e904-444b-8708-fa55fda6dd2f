// Last Update:2019-05-13 16:58:02
/**
 * @file TH_engine_define.h
 * @brief : 文件定义  
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-09-28
 */

#ifndef _T_H_ENGINE_DEFINE_H
#define _T_H_ENGINE_DEFINE_H
#include <stdint.h>
#include <string>
using namespace std;

#define MAX_IF_NUM 16
#define MAX_THREAD_NUM 64

class config_and_define 
{
    public:
        static  uint32_t max_packet_rule;// 规则总数
        static  string zmq_addr;
        static  int zmq_len;
        static  bool b_save_pb_file;
        static  bool b_save_json_file;
        static  bool b_save_white_json;
        static  bool b_zmq_active;
        static  uint32_t work_mode; //工作模式： 0：现场模式   1：debug模式
        static  uint32_t link_parse_mode;//链路层解析模式：  0：常规解析  1：自适应模式
        static  uint32_t parse_thread_num;//线程数
        static  long long task_id;//任务id
        static  long long batch_id;//批次id
        static  bool b_move_files;
        static  bool b_brush_off_syn;
        static  bool b_segment_analyse;
        static  int  pb_write_mode;
        static  int  pb_file_time ;
        static  bool b_rule_pb_save;
        static  uint32_t final_thread_num;//线程数
        static  bool b_pb_rename;   //pb重命名
        static  uint32_t port_num;
        static  char port_pci[MAX_IF_NUM][32];
        static  char port_mac[MAX_IF_NUM][18];
        static  char port_pos[MAX_IF_NUM][128];
        static uint32_t session_bucket_tosec;
        static uint32_t session_bucket_num;
        static uint32_t mbps_filter_out;
};
extern "C"
{
    typedef struct TH_ENGINE_GVAR
    {
        uint64_t drop_nocreate_pkt[MAX_THREAD_NUM];
        uint64_t ipv4_pkt[MAX_THREAD_NUM];
        uint64_t ipv4_session[MAX_THREAD_NUM];
        uint64_t ipv6_pkt[MAX_THREAD_NUM];
        uint64_t ipv6_session[MAX_THREAD_NUM];
        uint64_t firstip_pkt[MAX_THREAD_NUM];
        uint64_t firstip_session[MAX_THREAD_NUM];
        uint64_t white_list_hit_pkts[MAX_THREAD_NUM];
        uint64_t white_list_hit_bytes[MAX_THREAD_NUM];
        uint64_t session_num[MAX_THREAD_NUM];
        uint64_t important_target_num[MAX_THREAD_NUM];//TODO: 以会话还是包为单位？
        uint64_t alarm_num[MAX_THREAD_NUM][3]; //告警数量 0:规则 1:防御 2:模型
    } TH_ENGINE_GVAR;
    extern TH_ENGINE_GVAR th_engine_g_var;
}

#endif  /*_T_H_ENGINE_DEFINE_H*/

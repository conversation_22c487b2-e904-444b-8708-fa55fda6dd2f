#ifndef __TH_BIT_MAP_H__
#define __TH_BIT_MAP_H__

typedef struct {
	unsigned long long size;
	char *ptr;
}th_bitmap;

int th_bitmap_init(th_bitmap *bitmap, unsigned long long number);
void th_bitmap_free(th_bitmap *bitmap);
int th_bitmap_clear_all(th_bitmap *bitmap);
int th_bitmap_set_all(th_bitmap *bitmap);
int th_bitmap_set(th_bitmap *bitmap, unsigned long long idx);
int th_bitmap_set_serial(th_bitmap *bitmap, unsigned long long idx_begin, unsigned long long idx_end);
int th_bitmap_set_ipv4_masklen(th_bitmap *bitmap, char *ip, int masklen);
int th_bitmap_set_ipv4_mask(th_bitmap *bitmap, char *ip, char *mask);
int th_bitmap_set_ipv4_num(th_bitmap *bitmap, char *ip, unsigned int num);
int th_bitmap_clear(th_bitmap *bitmap, unsigned long long idx);
int th_bitmap_clear_serial(th_bitmap *bitmap, unsigned long long idx_begin, unsigned long long idx_end);
int th_bitmap_clear_ipv4_masklen(th_bitmap *bitmap, char *ip, int masklen);
int th_bitmap_clear_ipv4_mask(th_bitmap *bitmap, char *ip, char *mask);
int th_bitmap_clear_ipv4_num(th_bitmap *bitmap, char *ip, unsigned int num);
int th_bitmap_get(th_bitmap *bitmap, unsigned long long idx);


#endif
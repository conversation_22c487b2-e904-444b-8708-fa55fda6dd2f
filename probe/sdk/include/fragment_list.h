#ifndef __FRAGMENT_LIST_H__
#define __FRAGMENT_LIST_H__

#include <list>
#include <cstdint>
#include "fragment_define.h"

static const std::uint32_t def_max_fragment = 64;

class FragmentList
{
public:
    typedef struct {
        std::uint32_t mis_order;
        std::uint32_t re_send;
    } Summary;
    FragmentList(std::uint32_t max_fragment = def_max_fragment);
    std::uint64_t get_lose_len();
    int clear();
    int print();
    int add_fragment(std::uint64_t seq_begin, std::uint64_t seq_end);
    
    friend class TcpFragmentList;
private:
    std::list<FragmentNode> frag_list;
    std::uint32_t max_fragment;
    Summary sum;
};

#endif

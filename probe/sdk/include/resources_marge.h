/**
 * @file session.h
 * @brief session 的定义
 * <AUTHOR>
 * @version 0.1.00
 * @date 2018-09-02
 */

#ifndef RESOURCES_MARGE_H
#define RESOURCES_MARGE_H
#include <string>
#include <stack>
template<class T>
class session_marge
{
    public :
        session_marge(bool extend = true)
        {
            usenum = 0;
            sum = 0 ;
            b_extend = extend;
        }
        void  init (uint32_t num) 
        {
            for(uint32_t i = 0 ; i < num ; i++)
            {
                T * p = new T(sum+i);
                session_pub_stack.push(p);
            }
            sum += num ;
        }
        ~session_marge()
        {
            destroy();
        }
        void destroy()
        {
            while(session_pub_stack.size() > 0)
            {
                T * p = session_pub_stack.top();
                session_pub_stack.pop();
                if(p != NULL) 
                {
                    delete p ;
                }
                else
                {
                    continue;
                }
            }
        }
       T * pop()
       {
           if(session_pub_stack.size() > 0) 
           { 
                T * p = session_pub_stack.top();
                session_pub_stack.pop();
                usenum ++ ;
                return p ;
           }
           else if(b_extend)
           {
               init(1000);
               T * p = session_pub_stack.top();
               session_pub_stack.pop();
               usenum ++ ;
               return p ;
           }
           else
           {
               return NULL;
           }
       }
       void push(T  *p)
       {
           session_pub_stack.push(p);
           usenum -- ;
       }
       int get_usenum()
       {
           return usenum;
       }
       int get_sumnum()
       {
           return sum;
       }
    private:
        int sum ;
        int usenum ;
        std::stack <T *> session_pub_stack;
        bool b_extend;
};

#endif  /*SESSION_H*/

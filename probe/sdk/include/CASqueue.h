// Last Update:2016-06-03 17:00:35
/**
 * @file CASqueue.h
 * @brief 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2015-08-07
 */

#ifndef _C_A_SQUEUE_H
#define _C_A_SQUEUE_H

#include <stdio.h>
#include <string.h>
#include "no_lock_queue.h"


//#if defined(__x86_64__)



/*typedef void* gpointer;
typedef struct _Node {
    gpointer data;
}Node; */
struct _Queue {
    Node * p_end;
    Node *p_data;
    Node * head;
    Node * tail;
    int cycle ;
    int maxlen;
};
typedef struct _Queue Queue;
//Queue* queue_new(void);
Queue* queue_new(int queuelen);
bool queue_enqueue(Queue *q, gpointer data);
void queue_enqueue_block(Queue *q, gpointer data);
gpointer queue_dequeue(Queue *q);
void queue_destry(Queue * phead);
//void queue_enqueue_back(Queue *q, gpointer data);
class CASqueue {
    public:
    CASqueue(int max_queue_len, bool b_Nq) {
        if(b_Nq)
        {
            Nq = N_queue_new(max_queue_len);
            q = NULL;
               
        }
        else {
            q = queue_new(max_queue_len);
            Nq = NULL;
        }
            
    };
    ~CASqueue() {
        if(q != NULL) 
        {
            queue_destry(q);
            if(q != NULL)
            {
                delete q ;
            }
        }
        else 
        {
            N_queue_destry(Nq);
            if(Nq != NULL)
            {
                delete Nq ;
            }
        }
    };
    bool  push(void * data ) {
        if(q!= NULL)
        {
            return queue_enqueue(q,data);
        }
        else 
        {

            return N_queue_enqueue(Nq,data);
        }
    };
    void push_block(void * data ) {
        if(q != NULL) 
        {
            queue_enqueue_block(q,data);
        }
        else 
        {
            N_queue_enqueue_block(Nq,data);
        }
    };
    void * pop(){
        if(q != NULL) 
        {
            return queue_dequeue(q);
        }
        else 
        {
            return N_queue_dequeue(Nq);
        }
    };
    int get_queue_len()
    {
        if(q != NULL) 
        {
            return q -> cycle;
        }
        else 
        {
            return Nq -> cycle ;
        }
    }
    private :
        Queue * q ;
        NQueue * Nq ;
};

#endif  /*_C_A_SQUEUE_H*/

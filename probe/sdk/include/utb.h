// Last Update:2016-08-15 17:07:41
/**
 * @file utb.h
 * @brief:简体 繁体相互转化 
 * <AUTHOR>
 * @version 0.1.00
 * @date 2016-08-15
 */

#ifndef STB_H
#define STB_H
#include <map>
#include <string>
using namespace std;
class stb 
{
    public:
        stb(string config_dir) ;
        ~stb();
        string  simple_to_traditional(string &c_simple);
        string  traditional_to_simple(string &c_simple);
        string  simple_to_traditional_one(string & c_simple, int & offise);

        string  simple_to_traditional_A_to_a(string &s_simple);
        string  simple_to_traditional_A_to_a_const(const string &s_simple);
    private :
        map<string,string> STB_map;
        map<string,string> BTS_map;
        void read_file(char * file_name );
        void chackdir();
        string s_config_dir;

};


#endif  /*UTB_H*/

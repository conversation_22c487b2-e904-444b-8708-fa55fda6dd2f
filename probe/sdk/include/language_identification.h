// Last Update:2016-07-07 17:04:40
/**
 * @file language_identification.h
 * @brief :
 * <AUTHOR>
 * @version 0.1.00
 * @date 2016-03-14
 */

#ifndef LANGUAGE_IDENTIFICATION_H
#define LANGUAGE_IDENTIFICATION_H

#include "CConvCharSet.h"
#include <set>
#include <string>
#define CHARSET_ENGLISH 1270001 //英语 
#define CHARSET_CHINESE 1270002 // 中文 
#define CHARSET_UYGUR  1270003 // 维吾尔族 
#define CHARSET_COREAN   1270004 // 韩文 
#define CHARSET_JAPANESE    1270005 // 日文 
#define CHARSET_TIBETAN   1270006 //  藏文 
#define CHARSET_MONGOLOAN   1270007 // 蒙古文 
#define CHARSET_MONGOLOAN   1270008 // 中文繁体8
#define CHARSET_OTHER    1279999
using namespace std;
class language_info
{
    public:
        language_info(unsigned long begin_unic , unsigned long end_unic , int code ) ;
        language_info() ;
        bool operator == (const language_info &ps) const ;
        bool operator < (const language_info &ps) const ;
        void  set_language_info(char * ) ;
        int   m_code ;

    private: 
        char  begin_utf8[6];
        char  end_utf8[6];
        int   begin_len ; 
        int   end_len;
};
class language_identification 
{
    public:
        /*static  language_identification *  get_instance() 
        {
            static language_identification instance ;
            return & instance;
        }*/
        language_identification() ;
        ~language_identification();
        string  lang_identif_handle(char * chaeset , int length) ;
    private:
        void language_identif(char * chaeset , int length ,int & i_offise) ; 
        // 语言种类及边界 
        std::set <language_info> lang_info_set ; 
        std::set <int> code_set ; 

};


#endif  /*LANGUAGE_IDENTIFICATION_H*/

#ifndef __TH_LABELS_DEFINE_H__
#define __TH_LABELS_DEFINE_H__

#define LABEL_START             33000

// #define LABEL_BLACK_SESSION     1
#define LABEL_WHITE_RELATE      2
#define LABEL_TARGET_RELATE     3
#define LABEL_IP_SEGMENT        4
#define LABEL_USE_TCP_RESFLAG   5
// #define LABEL_TRACE_ROUTE       6
#define LABEL_TCP_FLAG_UNUSUAL  7
#define LABEL_TCP_SYN_PAYLOAD   8
#define LABEL_SYN_SSL_PAYLOAD   9

#define LABEL_SYN_ENC_PAYLOAD   10
#define LABEL_TCP_COMPLETE      11
#define LABEL_TCP_RESET         12
#define LABEL_TCP_NO_OPT_C      13
#define LABEL_TCP_NO_OPT_S      14
#define LABEL_IP_WITH_OPT       15
#define LABEL_SSL_NO_EXT_C      16
#define LABEL_SSL_NO_EXT_S      17
// #define LABEL_TCP_PKTLEN_LT1000 18
// #define LABEL_MAX_PKTLEN_LT500  19

#define LABEL_DUR_GT30          20
#define LABEL_DUR_GT60          21
#define LABEL_UP_GE_DOWN        22
#define LABEL_MBYTE_GE1024      23
#define LABEL_MBYTE_GE100       24
#define LABEL_CROSS_BORDER      25
// #define LABEL_BIGPORT_SERVER    26
// #define LABEL_LITTLEPORT_CLIENT 27
// #define LABEL_UNOFFICIAL_PORT   28
#define LABEL_MULTI_SESSION     29

// #define LABEL_DNS_TUNNEL        30
// #define LABEL_ICMP_TUNNEL       31
#define LABEL_ONE_SIDE_FLOW     32
#define LABEL_APP_CHANGE        33
#define LABEL_PORT_DETECT       34
#define LABEL_FILE_TRANSPORT    35
#define LABEL_LARGE_FILE        36
#define LABEL_MULTI_FILE        37
#define LABEL_TTL_DIFF_GE8      38
#define LABEL_UNKNOWN_APP       39

// #define LABEL_TTL32             40
// #define LABEL_TTL64             41
// #define LABEL_TTL128            42
// #define LABEL_TTL256            43
// #define LABEL_ICMP_DST_UNREACH  44
// #define LABEL_ICMP_PING         45
// #define LABEL_ICMP_REDIRECT     46
// #define LABEL_IPID_INCREAS      47
// #define LABEL_IPID_RANDOM       48
#define LABEL_HTTP_REQ_SAMEHEAD 49

#define LABEL_HTTP_XFF          50
#define LABEL_HTTP_DIFF_CONTENT 51
#define LABEL_HTTP_NO_HOST      52
#define LABEL_HTTP_IP_HOST      53
#define LABEL_HTTP_DOMAIN_T1000 54
#define LABEL_HTTP_HOST_REFERER 55
#define LABEL_HTTP_OUT_REFERER  56
#define LABEL_HTTP_UA_MOBILE    57
// #define LABEL_HTTP_UA_BROWSER   58
#define LABEL_HTTP_UA_LINUX     59

#define LABEL_HTTP_UA_WIN       60
#define LABEL_HTTP_UA_OS        61
#define LABEL_HTTP_UA_IOS       62
#define LABEL_HTTP_UA_ANDROID   63
#define LABEL_HTTP_COOKIE       64
#define LABEL_HTTPRESP_PKTALONE 65
#define LABEL_DNS_ONE_RECORD    66
#define LABEL_DNS_WITH_CNAME    67
#define LABEL_DNS_MULTI_A       68
#define LABEL_DNS_FAIL          69

#define LABEL_DNS_DOMAIN_T1000  70
#define LABEL_DNS_UNKNOW_TYPE   71
#define LABEL_DNS_TAIL_DATA     72
#define LABEL_DNS_TTL_LT60      73
#define LABEL_DNS_TTL_LT600     74
#define LABEL_DNS_TTL_LT3600    75
#define LABEL_SSL_NO_SNI        76
#define LABEL_SSL_IP_SNI        77
#define LABEL_SSL_DOMAIN_T1000  78
#define LABEL_SSL_UNKNOW_SUIT   79

#define LABEL_SSL_TS_DIFF_LT24  80
#define LABEL_SSL_TS_DIFF_GE24  81
#define LABEL_SSL_SESS_ID       82
#define LABEL_SSL_SESS_TICKET   83
#define LABEL_SSL_NOCERT        84
#define LABEL_SSL_ONECERT       85
#define LABEL_SSL_CERTCHAIN     86
#define LABEL_SSL_CHAIN_LEGAL   87
#define LABEL_SSL_CERT_T1000    88
#define LABEL_SSL_WEB           89

#define LABEL_SSL_FP_BROWSER    90
// #define LABEL_SSL_FP_RANDOM     91
#define LABEL_SSL_CHELLO_OCSP   92
#define LABEL_SSL_NO_TCPSEG     93
#define LABEL_SSL_TICKET_LE1    94
#define LABEL_SSL_TICKET_LE24   95
#define LABEL_SSL_TICKET_LE168  96
#define LABEL_SSL_TICKET_LONG   97
#define LABEL_CC_SERVER         98
#define LABEL_ABROAD_CC_SERVER  99

#define LABEL_ICMP_QUERY        100
#define LABEL_ICMP_ERROR        101
#define LABEL_ICMP_DST_UNREACH  102
#define LABEL_ICMP_SRC_QUENCH   103
#define LABEL_ICMP_REDIRECT     104
#define LABEL_ICMP_TIMEOUT      105
#define LABEL_ICMP_PKT_ERROR    106
#define LABEL_ICMP_NET_QUERY    107
#define LABEL_ICMP_ROUTE_QUERY  108
#define LABEL_ICMP_TS_QUERY     109

#define LABEL_ICMP_MSG_QUERY    110
#define LABEL_ICMP_MASK_QUERY   111
#define LABEL_ICMP_UNKNON_TYPE  112
#define LABEL_C_IPID_INCREASE   113
#define LABEL_C_IPID_RANDOM     114
#define LABEL_C_ORDER           115
#define LABEL_C_DISORDER_BIT    116
#define LABEL_C_DISORDER_MID    117
#define LABEL_C_DISORDER_MUCH   118
#define LABEL_S_IPID_INCREASE   119

#define LABEL_S_IPID_RANDOM     120
#define LABEL_S_ORDER           121
#define LABEL_S_DISORDER_BIT    122
#define LABEL_S_DISORDER_MID    123
#define LABEL_S_DISORDER_MUCH   124
#define LABEL_NOT_OS_C_PORT     125
#define LABEL_S_TTL_32          126
#define LABEL_S_TTL_64          127
#define LABEL_S_TTL_128         128
#define LABEL_S_TTL_256         129

#define LABEL_C_TTL_32          130
#define LABEL_C_TTL_64          131
#define LABEL_C_TTL_128         132
#define LABEL_C_TTL_256         133
#define LABEL_ICMP_FRAGEMENT    134
#define LABEL_ICMP_MTU_LIMIT    135
#define LABEL_TCP_PKTLEN_LT1000 136
#define LABEL_MAX_PKTLEN_LT500  137
#define LABEL_PORT_NO_MATCH_PRO 138
#define LABEL_UNCOMMON_PORT     139

#define LABEL_NO_QUERY          140
#define LABEL_1_QUERY           141
#define LABEL_2_8_QUERY         142
#define LABEL_9_16_QUERY        143
#define LABEL_17_OO_QUERY       144
#define LABEL_NO_RESPONSE       145
#define LABEL_1_RESPONSE        146
#define LABEL_2_8_RESPONSE      147
#define LABEL_9_16_RESPONSE     148
#define LABEL_17_OO_RESPONSE    149

#define LABEL_SSL_DES           150
#define LABEL_SSL_RC4           151
#define LABEL_BASE64            152
#define LABEL_TLS_LOW_VERSION   153
#define LABEL_SSL_CLIENT_CERT   154
#define LABEL_HOST_DIFF_IP      155
#define LABEL_SNI_DIFF_IP       156


#define LABEL_END               160


#endif
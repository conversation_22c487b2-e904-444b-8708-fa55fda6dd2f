// Last Update:2016-07-27 10:16:45
/**
 * @file Redis.h
 * @brief  Redis 操作类封装
 * <AUTHOR>
 * @version 0.1.00
 * @date 2015-08-19
 */

#ifndef REDIS_OPR_H
#define REDIS_OPR_H

#include <hiredis/hiredis.h>
#include <string.h>
#include <string>
#include <stdio.h>
#include <stdint.h>
#include <string>
#include <map>
#include <list>
#include <vector>

#if 0
#define TEXT_DATABASE           0
#define FILE_DATABASE           1
#define MAIL_DATABASE           2
#define ATTACHMENT_DATABASE     3
#define PARAMETER_DATABASE      4
#define SECRET_FILE_DATABASE    5
#define RELATION_DATABASE       6
#define FILEINFO_DATABASE       7
#define MAIL_FILE_DATABASE      8
#define RELATED_FILE_DATABASE   9
#define LONLAT_DATABASE      	10  //poi地理位置信息表
#define KLZ_DOUBlE              11 //口令字去重
#else
typedef enum REDIS_DATABASE
{
    TEXT_DATABASE = 0,   
    FILE_DATABASE,    
    MAIL_DATABASE,      
    ATTACHMENT_DATABASE,  
    PARAMETER_DATABASE,    
    SECRET_FILE_DATABASE,      
    RELATION_DATABASE,
	FILEINFO_DATABASE,  
    MAIL_FILE_DATABASE,    
    RELATED_FILE_DATABASE,      
    LONLAT_DATABASE,   //poi地理位置信息表 
    KLZ_DOUBlE, // 口令字去重
    MAX_DATABASE     
}REDIS_DATABASE_E;
#endif

using namespace std;

typedef std::vector<string> CHANNELS;

//poi地理位置信息
typedef struct stlon_lat_info
{
    std::string strlon;           
    std::string strlat;        
    std::string strchn; 	
	std::string strcity;
    std::string strpro;	
	std::string strcounty;	
	std::string straddress;	//详细地理位置信息，包含省、市、区/县、街道
}LON_LAT_INFO_S;

//redis 返回错误信息
typedef struct stExecRedisCmdErr
{
	std::string szErrorCodeStr;
	int errorCode;
}EXEC_REDIS_CMD_ERR_S;


class Redis {
    public:
        Redis(string redis_host, int redis_port);
        ~Redis();
        bool connect();
        bool add_data(int database_id, string key,string value,int expire_time);
        bool add_data(int database_id, string key,uint64_t value,int expire_time);
        bool get_data(int database_id, string key, string & value);
        bool delete_data(int database_id, string key);
		bool set_expire_time(int database_id, string key, int expire_time);
        bool find_keys_num(int database_id, string key,int &num);
        void display_database(int database_id);
        void flushall();
        void flush(int database_id);
		bool get_keys_cmd2redis(int iDatabaseID,const std::string strCmd,std::list<std::string> &listKeys,EXEC_REDIS_CMD_ERR_S &rtValue);
		bool get_poi_info_cmd2redis(int iDatabaseID,std::list<std::string> listKeys,std::map<std::string,LON_LAT_INFO_S> &mapLonLatInfo);
		bool subscribe(int database_id, const CHANNELS& channels);
		bool unsubscribe(int database_id, const CHANNELS& channels);
		bool publish(int database_id, std::string channel , std::string message);
    private:
        bool check_connection();
        bool select(int database_id);
        bool set(string key, string value);
        bool set(string key, uint64_t value);
        bool set(string key, string value, int expire_time);
        bool set(string key, uint64_t value, int expire_time);
        bool get(string key, string& value) ;
    private :
        redisContext *      connect_;
        redisReply *        reply_;
        string              host;
        int                 port;
//        int                 mail_expire_time;
};

#endif  /*REDIS_OPR_H*/

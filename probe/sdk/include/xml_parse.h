
#ifndef XML_PARSE_H
#define XML_PARSE_H

#include <libxml/parser.h>
#include <libxml/xpath.h>
#include <libxml/xmlsave.h>
#include "commit_tools.h"

#define ENCODING "UTF-8"

class xml_parse
{
    public:

        xml_parse(const char* filePath);

        xml_parse();
        ~xml_parse();


        const char* get_file_path();

        bool set_file_path(const char* filePath);

        const char* get_value(const char* xpath);

        bool set_value(const char* xpath, const char* value);


        bool is_node_exsit(const char* xpath);

        u_int32_t get_value_count(const char* xpath);

        bool display_XML(char** stream = NULL);

        char* get_attribute_value(const char* xpath, const char* xattribute);
        void  attribute_value_free(char*  value);

        bool init_xml_mem(char * buff, int buffsize);
        bool memxml_open_path(const char* xpath);
        void clear_xml_mem();
        void assemble_path(std::string & path, int t);
    private:
        char* m_filePath;
        char* m_value;
        xmlDocPtr m_doc;
        xmlXPathContextPtr m_context;
        xmlXPathObjectPtr m_result;


        bool init();
        bool open_path(const char* xpath);
        bool close_path();
        bool  b_mem ;


};
#endif

// gas_  Update:2019-06-06 09:54:08
/**
 * @file th_engine_tools.h
 * @brief : 框架工具集
 * <AUTHOR>
 * @version 0.1.00
 * @date 2019-03-23
 */

#ifndef TH_ENGINE_TOOLS_H
#define TH_ENGINE_TOOLS_H

#include <string>
#include <iostream>
#include <sstream>
using namespace std;

#include <stdint.h>
#include <stdio.h>
#include <inttypes.h>
#include <zlib.h>

#include "xml_parse.h"
#include "alert_merge.h"
#include "alert_token.h"
#include "server_study.h"
#include "json/value.h"
#include "json/writer.h"
#define MAX_THREAD_NUM 64
#define MAX_ALARM_BUFLEN 256

typedef int (*send_log_cb)(const char *topic, void *pKey, unsigned int keyLen, void *pValue, unsigned int valueLen);

class th_engine_tools
{
    private:
        th_engine_tools(int thread_num)
        {
            this->thread_num = thread_num;
            b_clock_ts_loop = 0;
            clock_ts[0] = 0;
            clock_ts[1] = 0;
            get_device_id();
            conf_parse();
            p_alert_merge = new AlertMerge[thread_num];
            p_alert_token = new AlertToken[thread_num];
            for(int i=0; i<thread_num; i++)
            {
                p_alert_merge[i].Init(32768);
                p_alert_token[i].Init(8192);
            }
            task_id = atoll(getenv("THE_TASKID"));
            task_id_str = string(getenv("THE_TASKID"));
            batch_id = atoll(getenv("THE_BATCHID"));
            batch_id_str = string(getenv("THE_BATCHID"));
            alert_type_map[0] = "规则";
            alert_type_map[1] = "防御";
            alert_type_map[2] = "模型";
        }
        void conf_parse(void)
        {
            xml_parse xml;

            server_judge.conf_parse();
            b_labels_enabled = 1;
            string conf_path = string(getenv("THE_CONF_PATH")) + "/labels_conf.xml";
            xml.set_file_path(conf_path.c_str());
            char *p_value = (char *)xml.get_value("/config/b_enabled");
            if(p_value != NULL)
            {
                if(string(p_value) == "false")
                {
                    b_labels_enabled = 0;
                }
            }
            return;
        }
    public:
        static th_engine_tools * instance(int thread_num = 1)
        {
            static th_engine_tools instance_(thread_num);
            return &instance_;
        }
        ~th_engine_tools()
        {
            if(b_clock_ts_loop)
            {
                b_clock_ts_loop = 0;
                pthread_join(pt_clock, NULL);
            }
            if (p_alert_merge)
            {
                delete []p_alert_merge;
                p_alert_merge = NULL;
            }
            if (p_alert_token)
            {
                delete []p_alert_token;
                p_alert_token = NULL;
            }
        }
        int get_alert_token(int thid, alert_target_key &in_alert)
        {
            DWORD pos, ret;
            alert_target to_alert, tmp;
            
            to_alert.key = in_alert;
            to_alert.val = clock_ts[0];
            
            ret = p_alert_merge[thid].JudgeAndAddAlert(to_alert, to_alert.val, pos);
            if(ERRORSIGN < ret)
            {
                p_alert_merge[thid].PopAlert( tmp );
                ret = p_alert_merge[thid].JudgeAndAddAlert(to_alert, to_alert.val, pos);
            }
            if(0 == ret)
            {
                p_alert_merge[thid].GetInfor(pos, tmp);
                if(tmp.val > to_alert.val)
                {
                    tmp.val = to_alert.val;
                }
                if((tmp.val + 60) <= to_alert.val)
                {
                    p_alert_merge[thid].UpdateAlertVal(to_alert.val, pos);
                    return p_alert_token[thid].get_token(in_alert.rule_id, to_alert.val);
                }
                else
                {
                    return 0;
                }
            }
            else if(1 == ret)
            {
                return p_alert_token[thid].get_token(in_alert.rule_id, to_alert.val);
            }
            else if (ERRORSIGN < ret)
            {
                return 0;
            }
        }

        void log_study_info()
        {
            server_judge.log_study_info();
        }
        uint8_t judge_c2s_by_study(uint8_t Server, uint8_t IPPro, uint16_t pPort[2])
        {
            return server_judge.judge_c2s_by_study(Server, IPPro, pPort);
        }
        void study_c2s_by_port(uint8_t Server, uint8_t IPPro, uint16_t pPort[2], uint8_t not_dpi_server)
        {
            server_judge.study_c2s_by_port(Server, IPPro, pPort, not_dpi_server);
        }
        int th_set_label(uint8_t *pdata, uint16_t label)
        {
            if(b_labels_enabled)
            {
                uint8_t set = 1;
                uint16_t byte_idx = label / 8;
                uint16_t bit_idx = label % 8;
                pdata[byte_idx] |= (set << bit_idx);
            }
            return 0;
        }
        uint8_t th_get_label(uint8_t *pdata, uint16_t label)
        {
            uint8_t get = 1;
            uint16_t byte_idx = label / 8;
            uint16_t bit_idx = label % 8;
            return (pdata[byte_idx] & (get << bit_idx));
        }
        static void *clock_ts_loop(void *arg)
        {
            th_engine_tools *p_tool = (th_engine_tools *)arg;
            uint32_t clock_ts[2];
            clock_ts[0] = 0;
            clock_ts[1] = 0;
            struct timeval select_tv, tv;
            int b_update = 0;
            
            *(uint64_t *)(p_tool->clock_ts) = *(uint64_t *)(clock_ts);
            while(p_tool->b_clock_ts_loop)
            {
                b_update = 0;
                gettimeofday(&tv, NULL);
                clock_ts[0] = (uint32_t)tv.tv_sec;
                clock_ts[1] = (uint32_t)tv.tv_usec;
                if(p_tool->clock_ts[0] == clock_ts[0])
                {
                    if(p_tool->clock_ts[1] + 100 <= clock_ts[1])
                    {
                        b_update = 1;
                    }
                }
                else
                {
                    b_update = 1;
                }
                if(b_update)
                {
                    clock_ts[1] = clock_ts[1] / 100 * 100;
                    *(uint64_t *)(p_tool->clock_ts) = *(uint64_t *)(clock_ts);
                    select_tv.tv_sec = 0;
                    select_tv.tv_usec = 90;
                }
                else
                {
                    select_tv.tv_sec = 0;
                    select_tv.tv_usec = 10;
                }
                select(0, NULL, NULL, NULL, &select_tv);
            }
            return NULL;
        }
        int start_clock_ts(int lcore)
        {
            int ret;
            cpu_set_t mask;
            pthread_attr_t pthr_attr;
            b_clock_ts_loop = 1;

            if(lcore >= 0)
            {
                ret = pthread_attr_init(&pthr_attr);
                if(ret)
                {
                    fprintf(stderr, "pthread_attr_init error\n");
                    return -1;
                }
                CPU_ZERO(&mask);
                CPU_SET(lcore, &mask);
                ret = pthread_attr_setaffinity_np(&pthr_attr, sizeof(cpu_set_t), &mask);
                if (ret)
                {
                    fprintf(stderr, "pthread_attr_setaffinity_np error\n");
                    return -1;
                }
                ret = pthread_create(&pt_clock, &pthr_attr, clock_ts_loop, this);
            }
            else
            {
                ret = pthread_create(&pt_clock, NULL, clock_ts_loop, this);
            }
            return ret;
        }
        void get_clock_uts(uint32_t &ts, uint32_t &uts)
        {
            uint32_t clock_now[2];
            *(uint64_t *)(clock_now) = *(uint64_t *)(clock_ts);
            ts = clock_now[0];
            uts = clock_now[1];
        }
        uint32_t get_clock_ts()
        {
            return clock_ts[0];
        }

        int set_send_log_cb(send_log_cb log_handle)
        {
            this->log_handle = log_handle;
        }

        int send_log(const char *topic, void *pKey, unsigned int keyLen, void *pValue, unsigned int valueLen)
        {
            return log_handle(topic, pKey, keyLen, pValue, valueLen);
        }

        int send_alarm(int thread_id, unsigned int rule_id, unsigned int level, int alarm_type, \
                std::string target_name, std::string target_type, \
                std::vector<std::string> &reasons, \
                std::string attacker_ip = "", std::string victim_ip = ""
            )
        {
            if(level < 61)
            {
                return 0;
            }
            string log;
            alert_json[thread_id].clear();
            alert_json[thread_id]["type"] = 40012;
            alert_json[thread_id]["task_id"] = (unsigned int)task_id;
            alert_json[thread_id]["batch_id"] = (unsigned int)batch_id;
            alert_json[thread_id]["time"] = clock_ts[0];
            alert_json[thread_id]["alarm_knowledge_id"] = rule_id;
            alert_json[thread_id]["alarm_reason"].resize(0);
            for(int i = 0; i + 1 < reasons.size(); i+=2)
            {
                Json::Value reason;
                reason["key"] = reasons[i];
                reason["actual_value"] = reasons[i+1];
                alert_json[thread_id]["alarm_reason"].append(reason);
            }
            alert_json[thread_id]["attack_level"] = level;
            alert_json[thread_id]["attack_chain_name"].resize(0);
            alert_json[thread_id]["alarm_type"].resize(0);
            alert_json[thread_id]["alarm_type"].append(alert_type_map[alarm_type]);
            alert_json[thread_id]["targets"].resize(0);
            Json::Value target;
            target["name"] = target_name;
            target["type"] = target_type;
            target["labels"].resize(0);
            alert_json[thread_id]["targets"].append(target);
            if(attacker_ip.size())
            {
                alert_json[thread_id]["attacker"].resize(0);
                Json::Value attacker;
                attacker["ip"] = attacker_ip;
                alert_json[thread_id]["attacker"].append(attacker);
            }
            if(victim_ip.size())
            {
                alert_json[thread_id]["victim"].resize(0);
                Json::Value victim;
                victim["ip"] = victim_ip;
                alert_json[thread_id]["victim"].append(victim);
            }
            log = writer[thread_id].write(alert_json[thread_id]);
            return send_log("slog", NULL, 0, (void *)log.c_str(), log.length());
        }
        long long task_id;
        long long batch_id;
        string task_id_str;
        string batch_id_str;
        int b_clock_ts_loop;
        uint32_t clock_ts[2];           //clock_ts[1]精度100us
        uint32_t device_id;
        char device_id_str[11];
    private:
        void get_device_id()
        {
            strcpy(device_id_str, getenv("THE_DEVICEID"));
            stringstream ss;
            ss << string(device_id_str);
            ss >> device_id;
        }
        int thread_num;
        AlertMerge *p_alert_merge;
        AlertToken *p_alert_token;
        pthread_t pt_clock;
        server_study server_judge;
        int b_labels_enabled;
        send_log_cb log_handle;
        char alert_buffer[MAX_THREAD_NUM][MAX_ALARM_BUFLEN];
        Json::Value alert_json[MAX_THREAD_NUM];
        Json::FastWriter writer[MAX_THREAD_NUM];
        string alert_type_map[3];
};
#endif  /*TH_ENGINE_TOOLS_H*/

#include "ZMPNMsg.pb.h"
 typedef void (*UINT32SETDATA)(uint32_t data, CANmsg *p_msg);
 typedef void (*STRINGSETDATA)(std::string& data, CANmsg *p_msg);
 typedef void (*UINT64DATA)(uint64_t data, CANmsg *p_msg);
 typedef void (*DOUBLEDATA)(double data, CANmsg *p_msg);
 typedef void (*BOOLDATA)(bool data, CANmsg *p_msg);
 typedef uint32_t (*GETUINT32)(CANmsg *p_msg);
 typedef uint64_t (*GETUINT64)(CANmsg *p_msg);
 typedef std::string (*GETSTRING)(CANmsg *p_msg);
 typedef double (*GETDOUBLE)(CANmsg *p_msg);
 typedef bool (*GETBOOL)(CANmsg *p_msg);
 class pb_set_data {
     
public:
 	pb_set_data();
	void set_msg_data(std::string ,uint32_t,CANmsg *); 
	void set_msg_data(std::string ,uint64_t,CANmsg *); 
	void set_msg_data(std::string ,double,CANmsg *); 
	void set_msg_data(std::string ,bool,CANmsg *); 
	void set_msg_data(std::string ,float,CANmsg *); 
	void set_msg_data(std::string ,std::string,CANmsg *);
 	uint32_t get_uint32_data(std::string ,CANmsg *);
 	uint64_t get_uint64_data(std::string ,CANmsg *);
 	std::string get_string_data(std::string ,CANmsg *);
 	double get_double_data(std::string ,CANmsg *);
 	bool get_bool_data(std::string ,CANmsg *);
 
private:
	std::map<std::string , UINT32SETDATA> uint32_map ;
 	std::map<std::string , STRINGSETDATA> string_map ;
 	std::map<std::string , DOUBLEDATA> double_map ;
 	std::map<std::string , BOOLDATA> bool_map ;
 	std::map<std::string , UINT64DATA> uint64_map ;
 	std::map<std::string,GETUINT32> getuint32_map ;
  	std::map<std::string,GETUINT64> getuint64_map ;
  	std::map<std::string,GETSTRING> getstring_map ;
  	std::map<std::string,GETDOUBLE> getdouble_map ;
  	std::map<std::string,GETBOOL> getbool_map ;
  
};
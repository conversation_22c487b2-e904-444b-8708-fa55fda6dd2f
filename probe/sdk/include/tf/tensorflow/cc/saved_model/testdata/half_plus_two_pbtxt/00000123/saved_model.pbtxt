saved_model_schema_version: 1
meta_graphs {
  meta_info_def {
    stripped_op_list {
      op {
        name: "Add"
        input_arg {
          name: "x"
          type_attr: "T"
        }
        input_arg {
          name: "y"
          type_attr: "T"
        }
        output_arg {
          name: "z"
          type_attr: "T"
        }
        attr {
          name: "T"
          type: "type"
          allowed_values {
            list {
              type: DT_HALF
              type: DT_FLOAT
              type: DT_DOUBLE
              type: DT_UINT8
              type: DT_INT8
              type: DT_INT16
              type: DT_INT32
              type: DT_INT64
              type: DT_COMPLEX64
              type: DT_COMPLEX128
              type: DT_STRING
            }
          }
        }
      }
      op {
        name: "Assign"
        input_arg {
          name: "ref"
          type_attr: "T"
          is_ref: true
        }
        input_arg {
          name: "value"
          type_attr: "T"
        }
        output_arg {
          name: "output_ref"
          type_attr: "T"
          is_ref: true
        }
        attr {
          name: "T"
          type: "type"
        }
        attr {
          name: "validate_shape"
          type: "bool"
          default_value {
            b: true
          }
        }
        attr {
          name: "use_locking"
          type: "bool"
          default_value {
            b: true
          }
        }
        allows_uninitialized_input: true
      }
      op {
        name: "Const"
        output_arg {
          name: "output"
          type_attr: "dtype"
        }
        attr {
          name: "value"
          type: "tensor"
        }
        attr {
          name: "dtype"
          type: "type"
        }
      }
      op {
        name: "Identity"
        input_arg {
          name: "input"
          type_attr: "T"
        }
        output_arg {
          name: "output"
          type_attr: "T"
        }
        attr {
          name: "T"
          type: "type"
        }
      }
      op {
        name: "MergeV2Checkpoints"
        input_arg {
          name: "checkpoint_prefixes"
          type: DT_STRING
        }
        input_arg {
          name: "destination_prefix"
          type: DT_STRING
        }
        attr {
          name: "delete_old_dirs"
          type: "bool"
          default_value {
            b: true
          }
        }
      }
      op {
        name: "Mul"
        input_arg {
          name: "x"
          type_attr: "T"
        }
        input_arg {
          name: "y"
          type_attr: "T"
        }
        output_arg {
          name: "z"
          type_attr: "T"
        }
        attr {
          name: "T"
          type: "type"
          allowed_values {
            list {
              type: DT_HALF
              type: DT_FLOAT
              type: DT_DOUBLE
              type: DT_UINT8
              type: DT_INT8
              type: DT_UINT16
              type: DT_INT16
              type: DT_INT32
              type: DT_INT64
              type: DT_COMPLEX64
              type: DT_COMPLEX128
            }
          }
        }
        is_commutative: true
      }
      op {
        name: "NoOp"
      }
      op {
        name: "Pack"
        input_arg {
          name: "values"
          type_attr: "T"
          number_attr: "N"
        }
        output_arg {
          name: "output"
          type_attr: "T"
        }
        attr {
          name: "N"
          type: "int"
          has_minimum: true
          minimum: 1
        }
        attr {
          name: "T"
          type: "type"
        }
        attr {
          name: "axis"
          type: "int"
          default_value {
            i: 0
          }
        }
      }
      op {
        name: "ParseExample"
        input_arg {
          name: "serialized"
          type: DT_STRING
        }
        input_arg {
          name: "names"
          type: DT_STRING
        }
        input_arg {
          name: "sparse_keys"
          type: DT_STRING
          number_attr: "Nsparse"
        }
        input_arg {
          name: "dense_keys"
          type: DT_STRING
          number_attr: "Ndense"
        }
        input_arg {
          name: "dense_defaults"
          type_list_attr: "Tdense"
        }
        output_arg {
          name: "sparse_indices"
          type: DT_INT64
          number_attr: "Nsparse"
        }
        output_arg {
          name: "sparse_values"
          type_list_attr: "sparse_types"
        }
        output_arg {
          name: "sparse_shapes"
          type: DT_INT64
          number_attr: "Nsparse"
        }
        output_arg {
          name: "dense_values"
          type_list_attr: "Tdense"
        }
        attr {
          name: "Nsparse"
          type: "int"
          has_minimum: true
        }
        attr {
          name: "Ndense"
          type: "int"
          has_minimum: true
        }
        attr {
          name: "sparse_types"
          type: "list(type)"
          has_minimum: true
          allowed_values {
            list {
              type: DT_FLOAT
              type: DT_INT64
              type: DT_STRING
            }
          }
        }
        attr {
          name: "Tdense"
          type: "list(type)"
          has_minimum: true
          allowed_values {
            list {
              type: DT_FLOAT
              type: DT_INT64
              type: DT_STRING
            }
          }
        }
        attr {
          name: "dense_shapes"
          type: "list(shape)"
          has_minimum: true
        }
      }
      op {
        name: "Placeholder"
        output_arg {
          name: "output"
          type_attr: "dtype"
        }
        attr {
          name: "dtype"
          type: "type"
        }
        attr {
          name: "shape"
          type: "shape"
          default_value {
            shape {
              unknown_rank: true
            }
          }
        }
      }
      op {
        name: "Reshape"
        input_arg {
          name: "tensor"
          type_attr: "T"
        }
        input_arg {
          name: "shape"
          type_attr: "Tshape"
        }
        output_arg {
          name: "output"
          type_attr: "T"
        }
        attr {
          name: "T"
          type: "type"
        }
        attr {
          name: "Tshape"
          type: "type"
          default_value {
            type: DT_INT32
          }
          allowed_values {
            list {
              type: DT_INT32
              type: DT_INT64
            }
          }
        }
      }
      op {
        name: "RestoreV2"
        input_arg {
          name: "prefix"
          type: DT_STRING
        }
        input_arg {
          name: "tensor_names"
          type: DT_STRING
        }
        input_arg {
          name: "shape_and_slices"
          type: DT_STRING
        }
        output_arg {
          name: "tensors"
          type_list_attr: "dtypes"
        }
        attr {
          name: "dtypes"
          type: "list(type)"
          has_minimum: true
          minimum: 1
        }
      }
      op {
        name: "SaveV2"
        input_arg {
          name: "prefix"
          type: DT_STRING
        }
        input_arg {
          name: "tensor_names"
          type: DT_STRING
        }
        input_arg {
          name: "shape_and_slices"
          type: DT_STRING
        }
        input_arg {
          name: "tensors"
          type_list_attr: "dtypes"
        }
        attr {
          name: "dtypes"
          type: "list(type)"
          has_minimum: true
          minimum: 1
        }
      }
      op {
        name: "ShardedFilename"
        input_arg {
          name: "basename"
          type: DT_STRING
        }
        input_arg {
          name: "shard"
          type: DT_INT32
        }
        input_arg {
          name: "num_shards"
          type: DT_INT32
        }
        output_arg {
          name: "filename"
          type: DT_STRING
        }
      }
      op {
        name: "StringJoin"
        input_arg {
          name: "inputs"
          type: DT_STRING
          number_attr: "N"
        }
        output_arg {
          name: "output"
          type: DT_STRING
        }
        attr {
          name: "N"
          type: "int"
          has_minimum: true
          minimum: 1
        }
        attr {
          name: "separator"
          type: "string"
          default_value {
            s: ""
          }
        }
      }
      op {
        name: "VariableV2"
        output_arg {
          name: "ref"
          type_attr: "dtype"
          is_ref: true
        }
        attr {
          name: "shape"
          type: "shape"
        }
        attr {
          name: "dtype"
          type: "type"
        }
        attr {
          name: "container"
          type: "string"
          default_value {
            s: ""
          }
        }
        attr {
          name: "shared_name"
          type: "string"
          default_value {
            s: ""
          }
        }
        is_stateful: true
      }
    }
    tags: "serve"
    tensorflow_version: "1.1.0-rc2"
    tensorflow_git_version: "unknown"
  }
  graph_def {
    node {
      name: "a/initial_value"
      op: "Const"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
      attr {
        key: "dtype"
        value {
          type: DT_FLOAT
        }
      }
      attr {
        key: "value"
        value {
          tensor {
            dtype: DT_FLOAT
            tensor_shape {
            }
            float_val: 0.5
          }
        }
      }
    }
    node {
      name: "a"
      op: "VariableV2"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
      attr {
        key: "container"
        value {
          s: ""
        }
      }
      attr {
        key: "dtype"
        value {
          type: DT_FLOAT
        }
      }
      attr {
        key: "shape"
        value {
          shape {
          }
        }
      }
      attr {
        key: "shared_name"
        value {
          s: ""
        }
      }
    }
    node {
      name: "a/Assign"
      op: "Assign"
      input: "a"
      input: "a/initial_value"
      attr {
        key: "T"
        value {
          type: DT_FLOAT
        }
      }
      attr {
        key: "_class"
        value {
          list {
            s: "loc:@a"
          }
        }
      }
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
      attr {
        key: "use_locking"
        value {
          b: true
        }
      }
      attr {
        key: "validate_shape"
        value {
          b: true
        }
      }
    }
    node {
      name: "a/read"
      op: "Identity"
      input: "a"
      attr {
        key: "T"
        value {
          type: DT_FLOAT
        }
      }
      attr {
        key: "_class"
        value {
          list {
            s: "loc:@a"
          }
        }
      }
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
    }
    node {
      name: "b/initial_value"
      op: "Const"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
      attr {
        key: "dtype"
        value {
          type: DT_FLOAT
        }
      }
      attr {
        key: "value"
        value {
          tensor {
            dtype: DT_FLOAT
            tensor_shape {
            }
            float_val: 2.0
          }
        }
      }
    }
    node {
      name: "b"
      op: "VariableV2"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
      attr {
        key: "container"
        value {
          s: ""
        }
      }
      attr {
        key: "dtype"
        value {
          type: DT_FLOAT
        }
      }
      attr {
        key: "shape"
        value {
          shape {
          }
        }
      }
      attr {
        key: "shared_name"
        value {
          s: ""
        }
      }
    }
    node {
      name: "b/Assign"
      op: "Assign"
      input: "b"
      input: "b/initial_value"
      attr {
        key: "T"
        value {
          type: DT_FLOAT
        }
      }
      attr {
        key: "_class"
        value {
          list {
            s: "loc:@b"
          }
        }
      }
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
      attr {
        key: "use_locking"
        value {
          b: true
        }
      }
      attr {
        key: "validate_shape"
        value {
          b: true
        }
      }
    }
    node {
      name: "b/read"
      op: "Identity"
      input: "b"
      attr {
        key: "T"
        value {
          type: DT_FLOAT
        }
      }
      attr {
        key: "_class"
        value {
          list {
            s: "loc:@b"
          }
        }
      }
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
    }
    node {
      name: "c/initial_value"
      op: "Const"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
      attr {
        key: "dtype"
        value {
          type: DT_FLOAT
        }
      }
      attr {
        key: "value"
        value {
          tensor {
            dtype: DT_FLOAT
            tensor_shape {
            }
            float_val: 3.0
          }
        }
      }
    }
    node {
      name: "c"
      op: "VariableV2"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
      attr {
        key: "container"
        value {
          s: ""
        }
      }
      attr {
        key: "dtype"
        value {
          type: DT_FLOAT
        }
      }
      attr {
        key: "shape"
        value {
          shape {
          }
        }
      }
      attr {
        key: "shared_name"
        value {
          s: ""
        }
      }
    }
    node {
      name: "c/Assign"
      op: "Assign"
      input: "c"
      input: "c/initial_value"
      attr {
        key: "T"
        value {
          type: DT_FLOAT
        }
      }
      attr {
        key: "_class"
        value {
          list {
            s: "loc:@c"
          }
        }
      }
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
      attr {
        key: "use_locking"
        value {
          b: true
        }
      }
      attr {
        key: "validate_shape"
        value {
          b: true
        }
      }
    }
    node {
      name: "c/read"
      op: "Identity"
      input: "c"
      attr {
        key: "T"
        value {
          type: DT_FLOAT
        }
      }
      attr {
        key: "_class"
        value {
          list {
            s: "loc:@c"
          }
        }
      }
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
    }
    node {
      name: "tf_example"
      op: "Placeholder"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
              unknown_rank: true
            }
          }
        }
      }
      attr {
        key: "dtype"
        value {
          type: DT_STRING
        }
      }
      attr {
        key: "shape"
        value {
          shape {
            unknown_rank: true
          }
        }
      }
    }
    node {
      name: "ParseExample/Const"
      op: "Const"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
              dim {
              }
            }
          }
        }
      }
      attr {
        key: "dtype"
        value {
          type: DT_FLOAT
        }
      }
      attr {
        key: "value"
        value {
          tensor {
            dtype: DT_FLOAT
            tensor_shape {
              dim {
              }
            }
          }
        }
      }
    }
    node {
      name: "ParseExample/key_x2"
      op: "Const"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
              dim {
                size: 1
              }
            }
          }
        }
      }
      attr {
        key: "dtype"
        value {
          type: DT_FLOAT
        }
      }
      attr {
        key: "value"
        value {
          tensor {
            dtype: DT_FLOAT
            tensor_shape {
              dim {
                size: 1
              }
            }
            float_val: 0.0
          }
        }
      }
    }
    node {
      name: "ParseExample/Reshape/shape"
      op: "Const"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
              dim {
                size: 1
              }
            }
          }
        }
      }
      attr {
        key: "dtype"
        value {
          type: DT_INT32
        }
      }
      attr {
        key: "value"
        value {
          tensor {
            dtype: DT_INT32
            tensor_shape {
              dim {
                size: 1
              }
            }
            int_val: 1
          }
        }
      }
    }
    node {
      name: "ParseExample/Reshape"
      op: "Reshape"
      input: "ParseExample/key_x2"
      input: "ParseExample/Reshape/shape"
      attr {
        key: "T"
        value {
          type: DT_FLOAT
        }
      }
      attr {
        key: "Tshape"
        value {
          type: DT_INT32
        }
      }
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
              dim {
                size: 1
              }
            }
          }
        }
      }
    }
    node {
      name: "ParseExample/ParseExample/names"
      op: "Const"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
              dim {
              }
            }
          }
        }
      }
      attr {
        key: "dtype"
        value {
          type: DT_STRING
        }
      }
      attr {
        key: "value"
        value {
          tensor {
            dtype: DT_STRING
            tensor_shape {
              dim {
              }
            }
          }
        }
      }
    }
    node {
      name: "ParseExample/ParseExample/dense_keys_0"
      op: "Const"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
      attr {
        key: "dtype"
        value {
          type: DT_STRING
        }
      }
      attr {
        key: "value"
        value {
          tensor {
            dtype: DT_STRING
            tensor_shape {
            }
            string_val: "x"
          }
        }
      }
    }
    node {
      name: "ParseExample/ParseExample/dense_keys_1"
      op: "Const"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
      attr {
        key: "dtype"
        value {
          type: DT_STRING
        }
      }
      attr {
        key: "value"
        value {
          tensor {
            dtype: DT_STRING
            tensor_shape {
            }
            string_val: "x2"
          }
        }
      }
    }
    node {
      name: "ParseExample/ParseExample"
      op: "ParseExample"
      input: "tf_example"
      input: "ParseExample/ParseExample/names"
      input: "ParseExample/ParseExample/dense_keys_0"
      input: "ParseExample/ParseExample/dense_keys_1"
      input: "ParseExample/Const"
      input: "ParseExample/Reshape"
      attr {
        key: "Ndense"
        value {
          i: 2
        }
      }
      attr {
        key: "Nsparse"
        value {
          i: 0
        }
      }
      attr {
        key: "Tdense"
        value {
          list {
            type: DT_FLOAT
            type: DT_FLOAT
          }
        }
      }
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
              dim {
                size: -1
              }
              dim {
                size: 1
              }
            }
            shape {
              dim {
                size: -1
              }
              dim {
                size: 1
              }
            }
          }
        }
      }
      attr {
        key: "dense_shapes"
        value {
          list {
            shape {
              dim {
                size: 1
              }
            }
            shape {
              dim {
                size: 1
              }
            }
          }
        }
      }
      attr {
        key: "sparse_types"
        value {
          list {
          }
        }
      }
    }
    node {
      name: "x"
      op: "Identity"
      input: "ParseExample/ParseExample"
      attr {
        key: "T"
        value {
          type: DT_FLOAT
        }
      }
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
              dim {
                size: -1
              }
              dim {
                size: 1
              }
            }
          }
        }
      }
    }
    node {
      name: "Mul"
      op: "Mul"
      input: "a/read"
      input: "x"
      attr {
        key: "T"
        value {
          type: DT_FLOAT
        }
      }
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
              dim {
                size: -1
              }
              dim {
                size: 1
              }
            }
          }
        }
      }
    }
    node {
      name: "y"
      op: "Add"
      input: "Mul"
      input: "b/read"
      attr {
        key: "T"
        value {
          type: DT_FLOAT
        }
      }
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
              dim {
                size: -1
              }
              dim {
                size: 1
              }
            }
          }
        }
      }
    }
    node {
      name: "Mul_1"
      op: "Mul"
      input: "a/read"
      input: "x"
      attr {
        key: "T"
        value {
          type: DT_FLOAT
        }
      }
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
              dim {
                size: -1
              }
              dim {
                size: 1
              }
            }
          }
        }
      }
    }
    node {
      name: "y2"
      op: "Add"
      input: "Mul_1"
      input: "c/read"
      attr {
        key: "T"
        value {
          type: DT_FLOAT
        }
      }
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
              dim {
                size: -1
              }
              dim {
                size: 1
              }
            }
          }
        }
      }
    }
    node {
      name: "x2"
      op: "Identity"
      input: "ParseExample/ParseExample:1"
      attr {
        key: "T"
        value {
          type: DT_FLOAT
        }
      }
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
              dim {
                size: -1
              }
              dim {
                size: 1
              }
            }
          }
        }
      }
    }
    node {
      name: "Mul_2"
      op: "Mul"
      input: "a/read"
      input: "x2"
      attr {
        key: "T"
        value {
          type: DT_FLOAT
        }
      }
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
              dim {
                size: -1
              }
              dim {
                size: 1
              }
            }
          }
        }
      }
    }
    node {
      name: "y3"
      op: "Add"
      input: "Mul_2"
      input: "c/read"
      attr {
        key: "T"
        value {
          type: DT_FLOAT
        }
      }
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
              dim {
                size: -1
              }
              dim {
                size: 1
              }
            }
          }
        }
      }
    }
    node {
      name: "Const"
      op: "Const"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
      attr {
        key: "dtype"
        value {
          type: DT_STRING
        }
      }
      attr {
        key: "value"
        value {
          tensor {
            dtype: DT_STRING
            tensor_shape {
            }
            string_val: "/tmp/original/export/assets/foo.txt"
          }
        }
      }
    }
    node {
      name: "filename_tensor/initial_value"
      op: "Const"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
      attr {
        key: "dtype"
        value {
          type: DT_STRING
        }
      }
      attr {
        key: "value"
        value {
          tensor {
            dtype: DT_STRING
            tensor_shape {
            }
            string_val: "foo.txt"
          }
        }
      }
    }
    node {
      name: "filename_tensor"
      op: "VariableV2"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
      attr {
        key: "container"
        value {
          s: ""
        }
      }
      attr {
        key: "dtype"
        value {
          type: DT_STRING
        }
      }
      attr {
        key: "shape"
        value {
          shape {
          }
        }
      }
      attr {
        key: "shared_name"
        value {
          s: ""
        }
      }
    }
    node {
      name: "filename_tensor/Assign"
      op: "Assign"
      input: "filename_tensor"
      input: "filename_tensor/initial_value"
      attr {
        key: "T"
        value {
          type: DT_STRING
        }
      }
      attr {
        key: "_class"
        value {
          list {
            s: "loc:@filename_tensor"
          }
        }
      }
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
      attr {
        key: "use_locking"
        value {
          b: true
        }
      }
      attr {
        key: "validate_shape"
        value {
          b: true
        }
      }
    }
    node {
      name: "filename_tensor/read"
      op: "Identity"
      input: "filename_tensor"
      attr {
        key: "T"
        value {
          type: DT_STRING
        }
      }
      attr {
        key: "_class"
        value {
          list {
            s: "loc:@filename_tensor"
          }
        }
      }
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
    }
    node {
      name: "Assign/value"
      op: "Const"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
      attr {
        key: "dtype"
        value {
          type: DT_STRING
        }
      }
      attr {
        key: "value"
        value {
          tensor {
            dtype: DT_STRING
            tensor_shape {
            }
            string_val: "foo.txt"
          }
        }
      }
    }
    node {
      name: "Assign"
      op: "Assign"
      input: "filename_tensor"
      input: "Assign/value"
      attr {
        key: "T"
        value {
          type: DT_STRING
        }
      }
      attr {
        key: "_class"
        value {
          list {
            s: "loc:@filename_tensor"
          }
        }
      }
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
      attr {
        key: "use_locking"
        value {
          b: false
        }
      }
      attr {
        key: "validate_shape"
        value {
          b: true
        }
      }
    }
    node {
      name: "init"
      op: "NoOp"
      input: "^a/Assign"
      input: "^b/Assign"
      input: "^c/Assign"
    }
    node {
      name: "group_deps"
      op: "NoOp"
      input: "^Assign"
    }
    node {
      name: "save/Const"
      op: "Const"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
      attr {
        key: "dtype"
        value {
          type: DT_STRING
        }
      }
      attr {
        key: "value"
        value {
          tensor {
            dtype: DT_STRING
            tensor_shape {
            }
            string_val: "model"
          }
        }
      }
    }
    node {
      name: "save/StringJoin/inputs_1"
      op: "Const"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
      attr {
        key: "dtype"
        value {
          type: DT_STRING
        }
      }
      attr {
        key: "value"
        value {
          tensor {
            dtype: DT_STRING
            tensor_shape {
            }
            string_val: "_temp_80e928f1e0c844239d136d1ea966099d/part"
          }
        }
      }
    }
    node {
      name: "save/StringJoin"
      op: "StringJoin"
      input: "save/Const"
      input: "save/StringJoin/inputs_1"
      attr {
        key: "N"
        value {
          i: 2
        }
      }
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
      attr {
        key: "separator"
        value {
          s: ""
        }
      }
    }
    node {
      name: "save/num_shards"
      op: "Const"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
      attr {
        key: "dtype"
        value {
          type: DT_INT32
        }
      }
      attr {
        key: "value"
        value {
          tensor {
            dtype: DT_INT32
            tensor_shape {
            }
            int_val: 1
          }
        }
      }
    }
    node {
      name: "save/ShardedFilename/shard"
      op: "Const"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
      attr {
        key: "dtype"
        value {
          type: DT_INT32
        }
      }
      attr {
        key: "value"
        value {
          tensor {
            dtype: DT_INT32
            tensor_shape {
            }
            int_val: 0
          }
        }
      }
    }
    node {
      name: "save/ShardedFilename"
      op: "ShardedFilename"
      input: "save/StringJoin"
      input: "save/ShardedFilename/shard"
      input: "save/num_shards"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
    }
    node {
      name: "save/SaveV2/tensor_names"
      op: "Const"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
              dim {
                size: 3
              }
            }
          }
        }
      }
      attr {
        key: "dtype"
        value {
          type: DT_STRING
        }
      }
      attr {
        key: "value"
        value {
          tensor {
            dtype: DT_STRING
            tensor_shape {
              dim {
                size: 3
              }
            }
            string_val: "a"
            string_val: "b"
            string_val: "c"
          }
        }
      }
    }
    node {
      name: "save/SaveV2/shape_and_slices"
      op: "Const"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
              dim {
                size: 3
              }
            }
          }
        }
      }
      attr {
        key: "dtype"
        value {
          type: DT_STRING
        }
      }
      attr {
        key: "value"
        value {
          tensor {
            dtype: DT_STRING
            tensor_shape {
              dim {
                size: 3
              }
            }
            string_val: ""
            string_val: ""
            string_val: ""
          }
        }
      }
    }
    node {
      name: "save/SaveV2"
      op: "SaveV2"
      input: "save/ShardedFilename"
      input: "save/SaveV2/tensor_names"
      input: "save/SaveV2/shape_and_slices"
      input: "a"
      input: "b"
      input: "c"
      attr {
        key: "dtypes"
        value {
          list {
            type: DT_FLOAT
            type: DT_FLOAT
            type: DT_FLOAT
          }
        }
      }
    }
    node {
      name: "save/control_dependency"
      op: "Identity"
      input: "save/ShardedFilename"
      input: "^save/SaveV2"
      attr {
        key: "T"
        value {
          type: DT_STRING
        }
      }
      attr {
        key: "_class"
        value {
          list {
            s: "loc:@save/ShardedFilename"
          }
        }
      }
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
    }
    node {
      name: "save/MergeV2Checkpoints/checkpoint_prefixes"
      op: "Pack"
      input: "save/ShardedFilename"
      input: "^save/control_dependency"
      attr {
        key: "N"
        value {
          i: 1
        }
      }
      attr {
        key: "T"
        value {
          type: DT_STRING
        }
      }
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
              dim {
                size: 1
              }
            }
          }
        }
      }
      attr {
        key: "axis"
        value {
          i: 0
        }
      }
    }
    node {
      name: "save/MergeV2Checkpoints"
      op: "MergeV2Checkpoints"
      input: "save/MergeV2Checkpoints/checkpoint_prefixes"
      input: "save/Const"
      attr {
        key: "delete_old_dirs"
        value {
          b: true
        }
      }
    }
    node {
      name: "save/Identity"
      op: "Identity"
      input: "save/Const"
      input: "^save/control_dependency"
      input: "^save/MergeV2Checkpoints"
      attr {
        key: "T"
        value {
          type: DT_STRING
        }
      }
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
    }
    node {
      name: "save/RestoreV2/tensor_names"
      op: "Const"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
              dim {
                size: 1
              }
            }
          }
        }
      }
      attr {
        key: "dtype"
        value {
          type: DT_STRING
        }
      }
      attr {
        key: "value"
        value {
          tensor {
            dtype: DT_STRING
            tensor_shape {
              dim {
                size: 1
              }
            }
            string_val: "a"
          }
        }
      }
    }
    node {
      name: "save/RestoreV2/shape_and_slices"
      op: "Const"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
              dim {
                size: 1
              }
            }
          }
        }
      }
      attr {
        key: "dtype"
        value {
          type: DT_STRING
        }
      }
      attr {
        key: "value"
        value {
          tensor {
            dtype: DT_STRING
            tensor_shape {
              dim {
                size: 1
              }
            }
            string_val: ""
          }
        }
      }
    }
    node {
      name: "save/RestoreV2"
      op: "RestoreV2"
      input: "save/Const"
      input: "save/RestoreV2/tensor_names"
      input: "save/RestoreV2/shape_and_slices"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
              unknown_rank: true
            }
          }
        }
      }
      attr {
        key: "dtypes"
        value {
          list {
            type: DT_FLOAT
          }
        }
      }
    }
    node {
      name: "save/Assign"
      op: "Assign"
      input: "a"
      input: "save/RestoreV2"
      attr {
        key: "T"
        value {
          type: DT_FLOAT
        }
      }
      attr {
        key: "_class"
        value {
          list {
            s: "loc:@a"
          }
        }
      }
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
      attr {
        key: "use_locking"
        value {
          b: true
        }
      }
      attr {
        key: "validate_shape"
        value {
          b: true
        }
      }
    }
    node {
      name: "save/RestoreV2_1/tensor_names"
      op: "Const"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
              dim {
                size: 1
              }
            }
          }
        }
      }
      attr {
        key: "dtype"
        value {
          type: DT_STRING
        }
      }
      attr {
        key: "value"
        value {
          tensor {
            dtype: DT_STRING
            tensor_shape {
              dim {
                size: 1
              }
            }
            string_val: "b"
          }
        }
      }
    }
    node {
      name: "save/RestoreV2_1/shape_and_slices"
      op: "Const"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
              dim {
                size: 1
              }
            }
          }
        }
      }
      attr {
        key: "dtype"
        value {
          type: DT_STRING
        }
      }
      attr {
        key: "value"
        value {
          tensor {
            dtype: DT_STRING
            tensor_shape {
              dim {
                size: 1
              }
            }
            string_val: ""
          }
        }
      }
    }
    node {
      name: "save/RestoreV2_1"
      op: "RestoreV2"
      input: "save/Const"
      input: "save/RestoreV2_1/tensor_names"
      input: "save/RestoreV2_1/shape_and_slices"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
              unknown_rank: true
            }
          }
        }
      }
      attr {
        key: "dtypes"
        value {
          list {
            type: DT_FLOAT
          }
        }
      }
    }
    node {
      name: "save/Assign_1"
      op: "Assign"
      input: "b"
      input: "save/RestoreV2_1"
      attr {
        key: "T"
        value {
          type: DT_FLOAT
        }
      }
      attr {
        key: "_class"
        value {
          list {
            s: "loc:@b"
          }
        }
      }
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
      attr {
        key: "use_locking"
        value {
          b: true
        }
      }
      attr {
        key: "validate_shape"
        value {
          b: true
        }
      }
    }
    node {
      name: "save/RestoreV2_2/tensor_names"
      op: "Const"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
              dim {
                size: 1
              }
            }
          }
        }
      }
      attr {
        key: "dtype"
        value {
          type: DT_STRING
        }
      }
      attr {
        key: "value"
        value {
          tensor {
            dtype: DT_STRING
            tensor_shape {
              dim {
                size: 1
              }
            }
            string_val: "c"
          }
        }
      }
    }
    node {
      name: "save/RestoreV2_2/shape_and_slices"
      op: "Const"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
              dim {
                size: 1
              }
            }
          }
        }
      }
      attr {
        key: "dtype"
        value {
          type: DT_STRING
        }
      }
      attr {
        key: "value"
        value {
          tensor {
            dtype: DT_STRING
            tensor_shape {
              dim {
                size: 1
              }
            }
            string_val: ""
          }
        }
      }
    }
    node {
      name: "save/RestoreV2_2"
      op: "RestoreV2"
      input: "save/Const"
      input: "save/RestoreV2_2/tensor_names"
      input: "save/RestoreV2_2/shape_and_slices"
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
              unknown_rank: true
            }
          }
        }
      }
      attr {
        key: "dtypes"
        value {
          list {
            type: DT_FLOAT
          }
        }
      }
    }
    node {
      name: "save/Assign_2"
      op: "Assign"
      input: "c"
      input: "save/RestoreV2_2"
      attr {
        key: "T"
        value {
          type: DT_FLOAT
        }
      }
      attr {
        key: "_class"
        value {
          list {
            s: "loc:@c"
          }
        }
      }
      attr {
        key: "_output_shapes"
        value {
          list {
            shape {
            }
          }
        }
      }
      attr {
        key: "use_locking"
        value {
          b: true
        }
      }
      attr {
        key: "validate_shape"
        value {
          b: true
        }
      }
    }
    node {
      name: "save/restore_shard"
      op: "NoOp"
      input: "^save/Assign"
      input: "^save/Assign_1"
      input: "^save/Assign_2"
    }
    node {
      name: "save/restore_all"
      op: "NoOp"
      input: "^save/restore_shard"
    }
    versions {
      producer: 23
    }
  }
  saver_def {
    filename_tensor_name: "save/Const:0"
    save_tensor_name: "save/Identity:0"
    restore_op_name: "save/restore_all"
    max_to_keep: 5
    sharded: true
    keep_checkpoint_every_n_hours: 10000.0
    version: V2
  }
  collection_def {
    key: "asset_filepaths"
    value {
      node_list {
        value: "Const:0"
      }
    }
  }
  collection_def {
    key: "legacy_init_op"
    value {
      node_list {
        value: "group_deps"
      }
    }
  }
  collection_def {
    key: "saved_model_assets"
    value {
      any_list {
        value {
          type_url: "type.googleapis.com/tensorflow.AssetFileDef"
          value: "\n\t\n\007Const:0\022\007foo.txt"
        }
      }
    }
  }
  collection_def {
    key: "trainable_variables"
    value {
      bytes_list {
        value: "\n\003a:0\022\010a/Assign\032\010a/read:0"
        value: "\n\003b:0\022\010b/Assign\032\010b/read:0"
        value: "\n\003c:0\022\010c/Assign\032\010c/read:0"
      }
    }
  }
  collection_def {
    key: "variables"
    value {
      bytes_list {
        value: "\n\003a:0\022\010a/Assign\032\010a/read:0"
        value: "\n\003b:0\022\010b/Assign\032\010b/read:0"
        value: "\n\003c:0\022\010c/Assign\032\010c/read:0"
      }
    }
  }
  signature_def {
    key: "classify_x2_to_y3"
    value {
      inputs {
        key: "inputs"
        value {
          name: "x2:0"
          dtype: DT_FLOAT
          tensor_shape {
            dim {
              size: -1
            }
            dim {
              size: 1
            }
          }
        }
      }
      outputs {
        key: "scores"
        value {
          name: "y3:0"
          dtype: DT_FLOAT
          tensor_shape {
            dim {
              size: -1
            }
            dim {
              size: 1
            }
          }
        }
      }
      method_name: "tensorflow/serving/classify"
    }
  }
  signature_def {
    key: "classify_x_to_y"
    value {
      inputs {
        key: "inputs"
        value {
          name: "tf_example:0"
          dtype: DT_STRING
          tensor_shape {
            unknown_rank: true
          }
        }
      }
      outputs {
        key: "scores"
        value {
          name: "y:0"
          dtype: DT_FLOAT
          tensor_shape {
            dim {
              size: -1
            }
            dim {
              size: 1
            }
          }
        }
      }
      method_name: "tensorflow/serving/classify"
    }
  }
  signature_def {
    key: "regress_x2_to_y3"
    value {
      inputs {
        key: "inputs"
        value {
          name: "x2:0"
          dtype: DT_FLOAT
          tensor_shape {
            dim {
              size: -1
            }
            dim {
              size: 1
            }
          }
        }
      }
      outputs {
        key: "outputs"
        value {
          name: "y3:0"
          dtype: DT_FLOAT
          tensor_shape {
            dim {
              size: -1
            }
            dim {
              size: 1
            }
          }
        }
      }
      method_name: "tensorflow/serving/regress"
    }
  }
  signature_def {
    key: "regress_x_to_y"
    value {
      inputs {
        key: "inputs"
        value {
          name: "tf_example:0"
          dtype: DT_STRING
          tensor_shape {
            unknown_rank: true
          }
        }
      }
      outputs {
        key: "outputs"
        value {
          name: "y:0"
          dtype: DT_FLOAT
          tensor_shape {
            dim {
              size: -1
            }
            dim {
              size: 1
            }
          }
        }
      }
      method_name: "tensorflow/serving/regress"
    }
  }
  signature_def {
    key: "regress_x_to_y2"
    value {
      inputs {
        key: "inputs"
        value {
          name: "tf_example:0"
          dtype: DT_STRING
          tensor_shape {
            unknown_rank: true
          }
        }
      }
      outputs {
        key: "outputs"
        value {
          name: "y2:0"
          dtype: DT_FLOAT
          tensor_shape {
            dim {
              size: -1
            }
            dim {
              size: 1
            }
          }
        }
      }
      method_name: "tensorflow/serving/regress"
    }
  }
  signature_def {
    key: "serving_default"
    value {
      inputs {
        key: "x"
        value {
          name: "x:0"
          dtype: DT_FLOAT
          tensor_shape {
            dim {
              size: -1
            }
            dim {
              size: 1
            }
          }
        }
      }
      outputs {
        key: "y"
        value {
          name: "y:0"
          dtype: DT_FLOAT
          tensor_shape {
            dim {
              size: -1
            }
            dim {
              size: 1
            }
          }
        }
      }
      method_name: "tensorflow/serving/predict"
    }
  }
}

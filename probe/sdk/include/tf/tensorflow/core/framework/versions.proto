syntax = "proto3";

package tensorflow;
option cc_enable_arenas = true;
option java_outer_classname = "VersionsProtos";
option java_multiple_files = true;
option java_package = "org.tensorflow.framework";
option go_package = "github.com/tensorflow/tensorflow/tensorflow/go/core/framework";

// Version information for a piece of serialized data
//
// There are different types of versions for each type of data
// (GraphDef, etc.), but they all have the same common shape
// described here.
//
// Each consumer has "consumer" and "min_producer" versions (specified
// elsewhere).  A consumer is allowed to consume this data if
//
//   producer >= min_producer
//   consumer >= min_consumer
//   consumer not in bad_consumers
//
message VersionDef {
  // The version of the code that produced this data.
  int32 producer = 1;

  // Any consumer below this version is not allowed to consume this data.
  int32 min_consumer = 2;

  // Specific consumer versions which are disallowed (e.g. due to bugs).
  repeated int32 bad_consumers = 3;
};

// This file defines protos that store the results of autotuning various
// operations.
//
// They are in proto format because we want to log them structured. They offer
// tremendous statistical, testing, and debugging value.
syntax = "proto3";

package tensorflow;

import "google/protobuf/any.proto";
import "google/protobuf/duration.proto";

message CudnnVersion {
  int32 major = 1;
  int32 minor = 2;
  int32 patch = 3;
}

message ComputeCapability {
  int32 major = 1;
  int32 minor = 2;
}

message AutotuneResult {
  message SuccessResult {
    int64 scratch_bytes = 1;
    google.protobuf.Duration run_time = 2;
  }

  message ConvKey {
    int64 algorithm = 1;
    bool tensor_ops_enabled = 2;
  }

  // If the conv runs successfully, success will be populated with the
  // autotuning result. Otherwise, the error message is propagated.
  oneof result {
    SuccessResult success = 3;
    string error_string = 4;
  }

  oneof key {
    ConvKey conv = 5;
  }

  // Sometimes we run a correctness checker during autotuning. It compares the
  // result buffer content between two algorithms, say, "reference" and "test"
  // algorithms. The "test" algorithm is the one associated with this
  // AutotuneResult.
  //
  // This field records the reference algorithm used. Notice that naming it
  // "reference" doesn't mean it's always correct. However, empirically it's
  // more correct, as it's "algo 0", less fancy than the compared one.
  //
  // Notice that the checker_failure may exist even in the success case.
  // This is because the error string in `result` comes from the underlying
  // implementation like cuDNN, which isn't aware that it produced an incorrect
  // result. And even if the checker detects an incorrect result, we can still
  // retrieve scratch_bytes and runtime_ms.
  oneof checker_failure {
    ConvKey reference_conv = 6;
  }
}

message AutotuningLog {
  google.protobuf.Any instr = 1;

  // Records all auto-tuning results per algorithm.
  repeated AutotuneResult results = 2;

  CudnnVersion cudnn_version = 3;
  ComputeCapability compute_capability = 4;
}

syntax = "proto3";

package tensorflow;
option cc_enable_arenas = true;
option java_outer_classname = "NamedTensorProtos";
option java_multiple_files = true;
option java_package = "org.tensorflow.framework";
option go_package = "github.com/tensorflow/tensorflow/tensorflow/go/core/protobuf";
import "tensorflow/core/framework/tensor.proto";

// A pair of tensor name and tensor values.
message NamedTensorProto {
  // Name of the tensor.
  string name = 1;

  // The client can populate a TensorProto using a tensorflow::Tensor`, or
  // directly using the protobuf field accessors.
  //
  // The client specifies whether the returned tensor values should be
  // filled tensor fields (float_val, int_val, etc.) or encoded in a
  // compact form in tensor.tensor_content.
  TensorProto tensor = 2;
}

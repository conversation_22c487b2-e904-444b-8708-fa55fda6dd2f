syntax = "proto3";

package tensorflow;
option cc_enable_arenas = true;
option java_outer_classname = "QueueRunnerProtos";
option java_multiple_files = true;
option java_package = "org.tensorflow.framework";
option go_package = "github.com/tensorflow/tensorflow/tensorflow/go/core/protobuf";
import "tensorflow/core/lib/core/error_codes.proto";

// Protocol buffer representing a QueueRunner.
message QueueRunnerDef {
  // Queue name.
  string queue_name = 1;

  // A list of enqueue operations.
  repeated string enqueue_op_name = 2;

  // The operation to run to close the queue.
  string close_op_name = 3;

  // The operation to run to cancel the queue.
  string cancel_op_name = 4;

  // A list of exception types considered to signal a safely closed queue
  // if raised during enqueue operations.
  repeated error.Code queue_closed_exception_types = 5;
}

/* Copyright 2016 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/

#include "tensorflow/core/debug/debug_callback_registry.h"

namespace tensorflow {

DebugCallbackRegistry::DebugCallbackRegistry() {}

/*static */ DebugCallbackRegistry* DebugCallbackRegistry::instance_ = nullptr;

DebugCallbackRegistry* DebugCallbackRegistry::singleton() {
  if (instance_ == nullptr) {
    instance_ = new DebugCallbackRegistry();
  }
  return instance_;
}

void DebugCallbackRegistry::RegisterCallback(const string& key,
                                             EventCallback callback) {
  mutex_lock lock(mu_);
  keyed_callback_[key] = std::move(callback);
}

DebugCallbackRegistry::EventCallback* DebugCallbackRegistry::GetCallback(
    const string& key) {
  mutex_lock lock(mu_);
  auto iter = keyed_callback_.find(key);
  return iter == keyed_callback_.end() ? nullptr : &iter->second;
}

void DebugCallbackRegistry::UnregisterCallback(const string& key) {
  mutex_lock lock(mu_);
  keyed_callback_.erase(key);
}

}  // namespace tensorflow

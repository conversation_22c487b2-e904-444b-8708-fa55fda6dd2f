/* Copyright 2015 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/

#include "tensorflow/core/lib/random/random.h"

#include <set>
#include "tensorflow/core/platform/test.h"
#include "tensorflow/core/platform/types.h"

namespace tensorflow {
namespace random {
namespace {

TEST(New64Test, SanityCheck) {
  std::set<uint64> values;
  for (int i = 0; i < 1000000; i++) {
    uint64 x = New64();
    EXPECT_TRUE(values.insert(x).second) << "duplicate " << x;
  }
}

}  // namespace
}  // namespace random
}  // namespace tensorflow

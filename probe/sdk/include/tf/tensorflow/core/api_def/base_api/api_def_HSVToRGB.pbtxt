op {
  graph_op_name: "HSVToRGB"
  in_arg {
    name: "images"
    description: <<END
1-D or higher rank. HSV data to convert. Last dimension must be size 3.
END
  }
  out_arg {
    name: "output"
    description: <<END
`images` converted to RGB.
END
  }
  summary: "Convert one or more images from HSV to RGB."
  description: <<END
Outputs a tensor of the same shape as the `images` tensor, containing the RGB
value of the pixels. The output is only well defined if the value in `images`
are in `[0,1]`.

See `rgb_to_hsv` for a description of the HSV encoding.
END
}

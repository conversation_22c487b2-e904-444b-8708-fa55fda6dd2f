op {
  graph_op_name: "SparseAddGrad"
  in_arg {
    name: "backprop_val_grad"
    description: <<END
1-D with shape `[nnz(sum)]`.  The gradient with respect to
the non-empty values of the sum.
END
  }
  in_arg {
    name: "a_indices"
    description: <<END
2-D.  The `indices` of the `SparseTensor` A, size `[nnz(A), ndims]`.
END
  }
  in_arg {
    name: "b_indices"
    description: <<END
2-D.  The `indices` of the `SparseTensor` B, size `[nnz(B), ndims]`.
END
  }
  in_arg {
    name: "sum_indices"
    description: <<END
2-D.  The `indices` of the sum `SparseTensor`, size
`[nnz(sum), ndims]`.
END
  }
  out_arg {
    name: "a_val_grad"
    description: <<END
1-D with shape `[nnz(A)]`. The gradient with respect to the
non-empty values of A.
END
  }
  out_arg {
    name: "b_val_grad"
    description: <<END
1-D with shape `[nnz(B)]`. The gradient with respect to the
non-empty values of <PERSON>.
<PERSON><PERSON>
  }
  summary: "The gradient operator for the SparseAdd op."
  description: <<END
The SparseAdd op calculates A + B, where A, B, and the sum are all represented
as `SparseTensor` objects.  This op takes in the upstream gradient w.r.t.
non-empty values of the sum, and outputs the gradients w.r.t. the non-empty
values of A and B.
END
}

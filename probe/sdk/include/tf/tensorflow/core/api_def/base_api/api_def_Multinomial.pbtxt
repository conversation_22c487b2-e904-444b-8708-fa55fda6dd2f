op {
  graph_op_name: "Multinomial"
  in_arg {
    name: "logits"
    description: <<END
2-D Tensor with shape `[batch_size, num_classes]`.  Each slice `[i, :]`
represents the unnormalized log probabilities for all classes.
END
  }
  in_arg {
    name: "num_samples"
    description: <<END
0-D.  Number of independent samples to draw for each row slice.
END
  }
  out_arg {
    name: "output"
    description: <<END
2-D Tensor with shape `[batch_size, num_samples]`.  Each slice `[i, :]`
contains the drawn class labels with range `[0, num_classes)`.
END
  }
  attr {
    name: "seed"
    description: <<END
If either seed or seed2 is set to be non-zero, the internal random number
generator is seeded by the given seed.  Otherwise, a random seed is used.
END
  }
  attr {
    name: "seed2"
    description: <<END
A second seed to avoid seed collision.
END
  }
  summary: "Draws samples from a multinomial distribution."
}

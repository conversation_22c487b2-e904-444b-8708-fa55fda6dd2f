op {
  graph_op_name: "EncodeBase64"
  in_arg {
    name: "input"
    description: <<END
Strings to be encoded.
END
  }
  out_arg {
    name: "output"
    description: <<END
Input strings encoded in base64.
END
  }
  attr {
    name: "pad"
    description: <<END
Bool whether padding is applied at the ends.
END
  }
  summary: "Encode strings into web-safe base64 format."
  description: <<END
Refer to the following article for more information on base64 format:
en.wikipedia.org/wiki/Base64. Base64 strings may have padding with '=' at the
end so that the encoded has length multiple of 4. See Padding section of the
link above.

Web-safe means that the encoder uses - and _ instead of + and /.
END
}

op {
  graph_op_name: "ScatterAdd"
  in_arg {
    name: "ref"
    description: <<END
Should be from a `Variable` node.
END
  }
  in_arg {
    name: "indices"
    description: <<END
A tensor of indices into the first dimension of `ref`.
END
  }
  in_arg {
    name: "updates"
    description: <<END
A tensor of updated values to add to `ref`.
END
  }
  out_arg {
    name: "output_ref"
    description: <<END
= Same as `ref`.  Returned as a convenience for operations that want
to use the updated values after the update is done.
END
  }
  attr {
    name: "use_locking"
    description: <<END
If True, the addition will be protected by a lock;
otherwise the behavior is undefined, but may exhibit less contention.
END
  }
  summary: "Adds sparse updates to a variable reference."
  description: <<END
This operation computes

    # Scalar indices
    ref[indices, ...] += updates[...]

    # Vector indices (for each i)
    ref[indices[i], ...] += updates[i, ...]

    # High rank indices (for each i, ..., j)
    ref[indices[i, ..., j], ...] += updates[i, ..., j, ...]

This operation outputs `ref` after the update is done.
This makes it easier to chain operations that need to use the reset value.

Duplicate entries are handled correctly: if multiple `indices` reference
the same location, their contributions add.

Requires `updates.shape = indices.shape + ref.shape[1:]` or `updates.shape = []`.

<div style="width:70%; margin:auto; margin-bottom:10px; margin-top:20px;">
<img style="width:100%" src="https://www.tensorflow.org/images/ScatterAdd.png" alt>
</div>
END
}

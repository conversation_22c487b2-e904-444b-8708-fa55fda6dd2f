op {
  graph_op_name: "QuantizedReshape"
  in_arg {
    name: "shape"
    description: <<END
Defines the shape of the output tensor.
END
  }
  in_arg {
    name: "input_min"
    description: <<END
The minimum value of the input.
END
  }
  in_arg {
    name: "input_max"
    description: <<END
The maximum value of the input.
END
  }
  out_arg {
    name: "output_min"
    description: <<END
This value is copied from input_min.
END
  }
  out_arg {
    name: "output_max"
    description: <<END
This value is copied from input_max.
END
  }
  summary: "Reshapes a quantized tensor as per the Reshape op."
  description: <<END
```
END
}

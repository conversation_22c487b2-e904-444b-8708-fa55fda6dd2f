op {
  graph_op_name: "MatrixBandPart"
  in_arg {
    name: "input"
    description: <<END
Rank `k` tensor.
END
  }
  in_arg {
    name: "num_lower"
    description: <<END
0-D tensor. Number of subdiagonals to keep. If negative, keep entire
lower triangle.
END
  }
  in_arg {
    name: "num_upper"
    description: <<END
0-D tensor. Number of superdiagonals to keep. If negative, keep
entire upper triangle.
END
  }
  out_arg {
    name: "band"
    description: <<END
Rank `k` tensor of the same shape as input. The extracted banded tensor.
END
  }
  summary: "Copy a tensor setting everything outside a central band in each innermost matrix"
  description: <<END
to zero.

The `band` part is computed as follows:
Assume `input` has `k` dimensions `[I, J, K, ..., M, N]`, then the output is a
tensor with the same shape where

`band[i, j, k, ..., m, n] = in_band(m, n) * input[i, j, k, ..., m, n]`.

The indicator function

`in_band(m, n) = (num_lower < 0 || (m-n) <= num_lower)) &&
                 (num_upper < 0 || (n-m) <= num_upper)`.

For example:

```
# if 'input' is [[ 0,  1,  2, 3]
                 [-1,  0,  1, 2]
                 [-2, -1,  0, 1]
                 [-3, -2, -1, 0]],

tf.matrix_band_part(input, 1, -1) ==> [[ 0,  1,  2, 3]
                                       [-1,  0,  1, 2]
                                       [ 0, -1,  0, 1]
                                       [ 0,  0, -1, 0]],

tf.matrix_band_part(input, 2, 1) ==> [[ 0,  1,  0, 0]
                                      [-1,  0,  1, 0]
                                      [-2, -1,  0, 1]
                                      [ 0, -2, -1, 0]]
```

Useful special cases:

```
 tf.matrix_band_part(input, 0, -1) ==> Upper triangular part.
 tf.matrix_band_part(input, -1, 0) ==> Lower triangular part.
 tf.matrix_band_part(input, 0, 0) ==> Diagonal.
```
END
}

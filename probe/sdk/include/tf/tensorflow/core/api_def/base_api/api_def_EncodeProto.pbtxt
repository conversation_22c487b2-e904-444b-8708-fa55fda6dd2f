op {
  graph_op_name: "EncodeProto"
  in_arg {
    name: "sizes"
    description: <<END
Tensor of int32 with shape `[batch_shape, len(field_names)]`.
END
  }
  in_arg {
    name: "values"
    description: <<END
List of tensors containing values for the corresponding field.
END
  }
  out_arg {
    name: "bytes"
    description: <<END
Tensor of serialized protos with shape `batch_shape`.
END
  }
  attr {
    name: "message_type"
    description: <<END
Name of the proto message type to decode.
END
  }
  attr {
    name: "field_names"
    description: <<END
List of strings containing proto field names.
END
  }
  attr {
    name: "Tinput_types"
    description: <<END
The input types.
END
  }
  summary: <<END
The op serializes protobuf messages provided in the input tensors.
END
  description: <<END
The types of the tensors in `values` must match the schema for the
fields specified in `field_names`. All the tensors in `values` must
have a common shape prefix, *batch_shape*.

The `sizes` tensor specifies repeat counts for each field.  The repeat
count (last dimension) of a each tensor in `values` must be greater
than or equal to corresponding repeat count in `sizes`.

A `message_type` name must be provided to give context for the field
names. The actual message descriptor can be looked up either in the
linked-in descriptor pool or a filename provided by the caller using
the `descriptor_source` attribute.

The `descriptor_source` attribute selects a source of protocol
descriptors to consult when looking up `message_type`. This may be a
filename containing a serialized `FileDescriptorSet` message,
or the special value `local://`, in which case only descriptors linked
into the code will be searched; the filename can be on any filesystem
accessible to TensorFlow.

You can build a `descriptor_source` file using the `--descriptor_set_out`
and `--include_imports` options to the protocol compiler `protoc`.

The `local://` database only covers descriptors linked into the
code via C++ libraries, not Python imports. You can link in a proto descriptor
by creating a cc_library target with alwayslink=1.

There are a few special cases in the value mapping:

Submessage and group fields must be pre-serialized as TensorFlow strings.

TensorFlow lacks support for unsigned int64s, so they must be
represented as `tf.int64` with the same twos-complement bit pattern
(the obvious way).

Unsigned int32 values can be represented exactly with `tf.int64`, or
with sign wrapping if the input is of type `tf.int32`.
END
}

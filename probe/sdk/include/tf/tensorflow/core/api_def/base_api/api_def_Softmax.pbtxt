op {
  graph_op_name: "Softmax"
  in_arg {
    name: "logits"
    description: <<END
2-D with shape `[batch_size, num_classes]`.
END
  }
  out_arg {
    name: "softmax"
    description: <<END
Same shape as `logits`.
END
  }
  summary: "Computes softmax activations."
  description: <<END
For each batch `i` and class `j` we have

    $$softmax[i, j] = exp(logits[i, j]) / sum_j(exp(logits[i, j]))$$
END
}

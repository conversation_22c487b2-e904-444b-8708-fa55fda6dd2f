op {
  graph_op_name: "StaticRegexReplace"
  in_arg {
    name: "input"
    description: "The text to be processed."
  }
  out_arg {
    name: "output"
    description: "The text after applying pattern and rewrite."
  }
  attr {
    name: "pattern"
    description: "The regular expression to match the input."
  }
  attr {
    name: "rewrite"
    description: "The rewrite to be applied to the matched expression."
  }
  attr {
    name: "replace_global"
    description: "If True, the replacement is global, otherwise the replacement\nis done only on the first match."
  }
  summary: "Replaces the match of pattern in input with rewrite."
  description: "It follows the re2 syntax (https://github.com/google/re2/wiki/Syntax)"
  visibility: HIDDEN
}

op {
  graph_op_name: "BoostedTreesQuantileStreamResourceDeserialize"
  visibility: HIDDEN
  in_arg {
    name: "quantile_stream_resource_handle"
    description: <<END
resource handle referring to a QuantileStreamResource.
END
  }
  in_arg {
    name: "bucket_boundaries"
    description: <<END
float; List of Rank 1 Tensors each containing the bucket boundaries for a feature.
END
  }
  attr {
    name: "num_streams"
    description: <<END
inferred int; number of features to get bucket boundaries for.
END
  }
  summary: "Deserialize bucket boundaries and ready flag into current QuantileAccumulator."
  description: <<END
An op that deserializes bucket boundaries and are boundaries ready flag into current QuantileAccumulator.
END
}

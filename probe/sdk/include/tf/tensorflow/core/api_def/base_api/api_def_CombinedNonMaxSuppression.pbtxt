op {
  graph_op_name: "CombinedNonMaxSuppression"
  in_arg {
    name: "boxes"
    description: <<END
A 4-D float tensor of shape `[batch_size, num_boxes, q, 4]`. If `q` is 1 then 
same boxes are used for all classes otherwise, if `q` is equal to number of 
classes, class-specific boxes are used.
END
  }
  in_arg {
    name: "scores"
    description: <<END
A 3-D float tensor of shape `[batch_size, num_boxes, num_classes]`
representing a single score corresponding to each box (each row of boxes).
END
  }
  in_arg {
    name: "max_output_size_per_class"
    description: <<END
A scalar integer tensor representing the maximum number of 
boxes to be selected by non max suppression per class
END
  }
  in_arg {
    name: "max_total_size"
    description: <<END
A scalar representing maximum number of boxes retained over all classes.
END
  }
  in_arg {
    name: "iou_threshold"
    description: <<END
A 0-D float tensor representing the threshold for deciding whether
boxes overlap too much with respect to IOU.
END
  }
  in_arg {
    name: "score_threshold"
    description: <<END
A 0-D float tensor representing the threshold for deciding when to remove
boxes based on score.
END
  }
  attr {
    name: "pad_per_class"
    description: <<END
If false, the output nmsed boxes, scores and classes
are padded/clipped to `max_total_size`. If true, the
output nmsed boxes, scores and classes are padded to be of length
`max_size_per_class`*`num_classes`, unless it exceeds `max_total_size` in
which case it is clipped to `max_total_size`. Defaults to false.
END
  }
  out_arg {
    name: "nmsed_boxes"
    description: <<END
A [batch_size, max_detections, 4] float32 tensor 
containing the non-max suppressed boxes.
END
  }
  out_arg {
    name: "nmsed_scores"
    description: <<END
A [batch_size, max_detections] float32 tensor 
containing the scores for the boxes.
END
  }
  out_arg {
    name: "nmsed_classes"
    description: <<END
A [batch_size, max_detections] float32 tensor 
containing the classes for the boxes.
END
  }
  out_arg {
    name: "valid_detections"
    description: <<END
A [batch_size] int32 tensor indicating the number of
valid detections per batch item. Only the top num_detections[i] entries in
nms_boxes[i], nms_scores[i] and nms_class[i] are valid. The rest of the
entries are zero paddings.
END
  }
  summary: "Greedily selects a subset of bounding boxes in descending order of score,"
  description: <<END
This operation performs non_max_suppression on the inputs per batch, across
all classes.
Prunes away boxes that have high intersection-over-union (IOU) overlap
with previously selected boxes.  Bounding boxes are supplied as
[y1, x1, y2, x2], where (y1, x1) and (y2, x2) are the coordinates of any
diagonal pair of box corners and the coordinates can be provided as normalized
(i.e., lying in the interval [0, 1]) or absolute.  Note that this algorithm
is agnostic to where the origin is in the coordinate system. Also note that
this algorithm is invariant to orthogonal transformations and translations
of the coordinate system; thus translating or reflections of the coordinate
system result in the same boxes being selected by the algorithm.
The output of this operation is the final boxes, scores and classes tensor
returned after performing non_max_suppression.
END
}

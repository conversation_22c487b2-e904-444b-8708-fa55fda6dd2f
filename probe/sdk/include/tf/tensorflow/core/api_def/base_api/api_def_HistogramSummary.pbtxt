op {
  graph_op_name: "HistogramSummary"
  in_arg {
    name: "tag"
    description: <<END
Scalar.  Tag to use for the `Summary.Value`.
END
  }
  in_arg {
    name: "values"
    description: <<END
Any shape. Values to use to build the histogram.
END
  }
  out_arg {
    name: "summary"
    description: <<END
Scalar. Serialized `Summary` protocol buffer.
END
  }
  summary: "Outputs a `Summary` protocol buffer with a histogram."
  description: <<END
The generated
[`Summary`](https://www.tensorflow.org/code/tensorflow/core/framework/summary.proto)
has one summary value containing a histogram for `values`.

This op reports an `InvalidArgument` error if any value is not finite.
END
}

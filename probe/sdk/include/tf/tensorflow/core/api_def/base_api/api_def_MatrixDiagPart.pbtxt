op {
  graph_op_name: "MatrixDiagPart"
  in_arg {
    name: "input"
    description: <<END
Rank `k` tensor where `k >= 2`.
END
  }
  out_arg {
    name: "diagonal"
    description: <<END
The extracted diagonal(s) having shape
`diagonal.shape = input.shape[:-2] + [min(input.shape[-2:])]`.
END
  }
  summary: "Returns the batched diagonal part of a batched tensor."
  description: <<END
This operation returns a tensor with the `diagonal` part
of the batched `input`. The `diagonal` part is computed as follows:

Assume `input` has `k` dimensions `[I, J, K, ..., M, N]`, then the output is a
tensor of rank `k - 1` with dimensions `[I, J, K, ..., min(M, N)]` where:

`diagonal[i, j, k, ..., n] = input[i, j, k, ..., n, n]`.

The input must be at least a matrix.

For example:

```
# 'input' is [[[1, 0, 0, 0]
               [0, 2, 0, 0]
               [0, 0, 3, 0]
               [0, 0, 0, 4]],
              [[5, 0, 0, 0]
               [0, 6, 0, 0]
               [0, 0, 7, 0]
               [0, 0, 0, 8]]]

and input.shape = (2, 4, 4)

tf.matrix_diag_part(input) ==> [[1, 2, 3, 4], [5, 6, 7, 8]]

which has shape (2, 4)
```
END
}

op {
  graph_op_name: "PaddingFIFOQueueV2"
  endpoint {
    name: "PaddingFIFOQueue"
  }
  out_arg {
    name: "handle"
    description: <<END
The handle to the queue.
END
  }
  attr {
    name: "component_types"
    description: <<END
The type of each component in a value.
END
  }
  attr {
    name: "shapes"
    description: <<END
The shape of each component in a value. The length of this attr must
be either 0 or the same as the length of component_types.
Shapes of fixed rank but variable size are allowed by setting
any shape dimension to -1.  In this case, the inputs' shape may vary along
the given dimension, and Dequeue<PERSON>any will pad the given dimension with
zeros up to the maximum shape of all elements in the given batch.
If the length of this attr is 0, different queue elements may have
different ranks and shapes, but only one element may be dequeued at a time.
END
  }
  attr {
    name: "capacity"
    description: <<END
The upper bound on the number of elements in this queue.
Negative numbers mean no limit.
END
  }
  attr {
    name: "container"
    description: <<END
If non-empty, this queue is placed in the given container.
Otherwise, a default container is used.
END
  }
  attr {
    name: "shared_name"
    description: <<END
If non-empty, this queue will be shared under the given name
across multiple sessions.
END
  }
  summary: "A queue that produces elements in first-in first-out order."
  description: <<END
Variable-size shapes are allowed by setting the corresponding shape dimensions
to 0 in the shape attr.  In this case DequeueMany will pad up to the maximum
size of any given element in the minibatch.  See below for details.
END
}

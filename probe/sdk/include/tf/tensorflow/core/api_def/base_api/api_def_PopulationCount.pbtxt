op {
  graph_op_name: "PopulationCount"
  summary: "Computes element-wise population count (a.k.a. popcount, bitsum, bitcount)."
  description: <<END
For each entry in `x`, calculates the number of `1` (on) bits in the binary
representation of that entry.

**NOTE**: It is more efficient to first `tf.bitcast` your tensors into
`int32` or `int64` and perform the bitcount on the result, than to feed in
8- or 16-bit inputs and then aggregate the resulting counts.
END
}

op {
  graph_op_name: "WholeFileReaderV2"
  endpoint {
    name: "WholeFileReader"
  }
  out_arg {
    name: "reader_handle"
    description: <<END
The handle to reference the Reader.
END
  }
  attr {
    name: "container"
    description: <<END
If non-empty, this reader is placed in the given container.
Otherwise, a default container is used.
END
  }
  attr {
    name: "shared_name"
    description: <<END
If non-empty, this reader is named in the given bucket
with this shared_name. Otherwise, the node name is used instead.
END
  }
  summary: "A Reader that outputs the entire contents of a file as a value."
  description: <<END
To use, enqueue filenames in a Queue.  The output of ReaderRead will
be a filename (key) and the contents of that file (value).
END
}

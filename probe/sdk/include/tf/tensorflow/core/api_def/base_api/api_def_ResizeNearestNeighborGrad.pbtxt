op {
  graph_op_name: "ResizeNearestNeighborGrad"
  visibility: HIDDE<PERSON>
  in_arg {
    name: "grads"
    description: <<END
4-D with shape `[batch, height, width, channels]`.
END
  }
  in_arg {
    name: "size"
    description: <<END
= A 1-D int32 Tensor of 2 elements: `orig_height, orig_width`. The
original input size.
END
  }
  out_arg {
    name: "output"
    description: <<END
4-D with shape `[batch, orig_height, orig_width, channels]`. Gradients
with respect to the input image.
END
  }
  attr {
    name: "align_corners"
    description: <<END
If true, the centers of the 4 corner pixels of the input and grad tensors are
aligned. Defaults to false.
END
  }
  summary: "Computes the gradient of nearest neighbor interpolation."
}

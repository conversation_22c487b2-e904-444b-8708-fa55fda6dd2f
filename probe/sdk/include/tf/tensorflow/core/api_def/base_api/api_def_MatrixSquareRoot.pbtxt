op {
  graph_op_name: "MatrixSquareRoot"
  in_arg {
    name: "input"
    description: <<END
Shape is `[..., <PERSON>, <PERSON>]`.
END
  }
  out_arg {
    name: "output"
    description: <<END
Shape is `[..., <PERSON>, <PERSON>]`.

@compatibility(scipy)
Equivalent to scipy.linalg.sqrtm
@end_compatibility
END
  }
  summary: "Computes the matrix square root of one or more square matrices:"
  description: <<END
matmul(sqrtm(A), sqrtm(A)) = A

The input matrix should be invertible. If the input matrix is real, it should
have no eigenvalues which are real and negative (pairs of complex conjugate
eigenvalues are allowed).

The matrix square root is computed by first reducing the matrix to 
quasi-triangular form with the real <PERSON>hur decomposition. The square root 
of the quasi-triangular matrix is then computed directly. Details of 
the algorithm can be found in: <PERSON>, "Computing real 
square roots of a real matrix", Linear Algebra Appl., 1987.

The input is a tensor of shape `[..., <PERSON>, M]` whose inner-most 2 dimensions
form square matrices. The output is a tensor of the same shape as the input
containing the matrix square root for all input submatrices `[..., :, :]`.
END
}

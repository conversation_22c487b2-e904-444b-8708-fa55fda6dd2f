op {
  graph_op_name: "TakeManySparseFromTensorsMap"
  in_arg {
    name: "sparse_handles"
    description: <<END
1-D, The `N` serialized `SparseTensor` objects.
Shape: `[N]`.
END
  }
  out_arg {
    name: "sparse_indices"
    description: <<END
2-D.  The `indices` of the minibatch `SparseTensor`.
END
  }
  out_arg {
    name: "sparse_values"
    description: <<END
1-D.  The `values` of the minibatch `SparseTensor`.
END
  }
  out_arg {
    name: "sparse_shape"
    description: <<END
1-D.  The `shape` of the minibatch `SparseTensor`.
END
  }
  attr {
    name: "dtype"
    description: <<END
The `dtype` of the `SparseTensor` objects stored in the
`SparseTensorsMap`.
END
  }
  attr {
    name: "container"
    description: <<END
The container name for the `SparseTensorsMap` read by this op.
END
  }
  attr {
    name: "shared_name"
    description: <<END
The shared name for the `SparseTensorsMap` read by this op.
It should not be blank; rather the `shared_name` or unique Operation name
of the Op that created the original `SparseTensorsMap` should be used.
END
  }
  summary: "Read `SparseTensors` from a `SparseTensorsMap` and concatenate them."
  description: <<END
The input `sparse_handles` must be an `int64` matrix of shape `[N, 1]` where
`N` is the minibatch size and the rows correspond to the output handles of
`AddSparseToTensorsMap` or `AddManySparseToTensorsMap`.  The ranks of the
original `SparseTensor` objects that went into the given input ops must all
match.  When the final `SparseTensor` is created, it has rank one
higher than the ranks of the incoming `SparseTensor` objects
(they have been concatenated along a new row dimension on the left).

The output `SparseTensor` object's shape values for all dimensions but the
first are the max across the input `SparseTensor` objects' shape values
for the corresponding dimensions.  Its first shape value is `N`, the minibatch
size.

The input `SparseTensor` objects' indices are assumed ordered in
standard lexicographic order.  If this is not the case, after this
step run `SparseReorder` to restore index ordering.

For example, if the handles represent an input, which is a `[2, 3]` matrix
representing two original `SparseTensor` objects:

```
    index = [ 0]
            [10]
            [20]
    values = [1, 2, 3]
    shape = [50]
```

and

```
    index = [ 2]
            [10]
    values = [4, 5]
    shape = [30]
```

then the final `SparseTensor` will be:

```
    index = [0  0]
            [0 10]
            [0 20]
            [1  2]
            [1 10]
    values = [1, 2, 3, 4, 5]
    shape = [2 50]
```
END
}

op {
  graph_op_name: "BroadcastTo"
  in_arg {
    name: "input"
    description: <<END
A Tensor to broadcast.
END
  }
  in_arg {
    name: "shape"
    description: <<END
An 1-D `int` Tensor. The shape of the desired output.
END
  }
  out_arg {
    name: "output"
    description: <<END
A Tensor.
END
  }
  summary: "Broadcast an array for a compatible shape."
  description: <<END
Broadcasting is the process of making arrays to have compatible shapes
for arithmetic operations. Two shapes are compatible if for each
dimension pair they are either equal or one of them is one. When trying
to broadcast a Tensor to a shape, it starts with the trailing dimensions,
and works its way forward.

For example,
```
>>> x = tf.constant([1, 2, 3])
>>> y = tf.broadcast_to(x, [3, 3])
>>> sess.run(y)
array([[1, 2, 3],
       [1, 2, 3],
       [1, 2, 3]], dtype=int32)
```
In the above example, the input Tensor with the shape of `[1, 3]`
is broadcasted to output Tensor with shape of `[3, 3]`.
END
}

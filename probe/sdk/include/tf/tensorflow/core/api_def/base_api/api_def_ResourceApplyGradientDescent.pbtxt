op {
  graph_op_name: "ResourceApplyGradientDescent"
  in_arg {
    name: "var"
    description: <<END
Should be from a Variable().
END
  }
  in_arg {
    name: "alpha"
    description: <<END
Scaling factor. Must be a scalar.
END
  }
  in_arg {
    name: "delta"
    description: <<END
The change.
END
  }
  attr {
    name: "use_locking"
    description: <<END
If `True`, the subtraction will be protected by a lock;
otherwise the behavior is undefined, but may exhibit less contention.
END
  }
  summary: "Update \'*var\' by subtracting \'alpha\' * \'delta\' from it."
}

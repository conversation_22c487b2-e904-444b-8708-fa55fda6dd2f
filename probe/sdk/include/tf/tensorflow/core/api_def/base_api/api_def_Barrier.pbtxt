op {
  graph_op_name: "Barrier"
  out_arg {
    name: "handle"
    description: <<END
The handle to the barrier.
END
  }
  attr {
    name: "component_types"
    description: <<END
The type of each component in a value.
END
  }
  attr {
    name: "shapes"
    description: <<END
The shape of each component in a value. Each shape must be 1 in the
first dimension. The length of this attr must be the same as the length of
component_types.
END
  }
  attr {
    name: "capacity"
    description: <<END
The capacity of the barrier.  The default capacity is MAX_INT32,
which is the largest capacity of the underlying queue.
END
  }
  attr {
    name: "container"
    description: <<END
If non-empty, this barrier is placed in the given container.
Otherwise, a default container is used.
END
  }
  attr {
    name: "shared_name"
    description: <<END
If non-empty, this barrier will be shared under the given name
across multiple sessions.
END
  }
  summary: "Defines a barrier that persists across different graph executions."
  description: <<END
A barrier represents a key-value map, where each key is a string, and
each value is a tuple of tensors.

At runtime, the barrier contains 'complete' and 'incomplete'
elements. A complete element has defined tensors for all components of
its value tuple, and may be accessed using BarrierTakeMany. An
incomplete element has some undefined components in its value tuple,
and may be updated using BarrierInsertMany.
END
}

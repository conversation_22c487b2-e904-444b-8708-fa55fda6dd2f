op {
  graph_op_name: "EncodePng"
  in_arg {
    name: "image"
    description: <<END
3-D with shape `[height, width, channels]`.
END
  }
  out_arg {
    name: "contents"
    description: <<END
0-D. PNG-encoded image.
END
  }
  attr {
    name: "compression"
    description: <<END
Compression level.
END
  }
  summary: "PNG-encode an image."
  description: <<END
`image` is a 3-D uint8 or uint16 Tensor of shape `[height, width, channels]`
where `channels` is:

*   1: for grayscale.
*   2: for grayscale + alpha.
*   3: for RGB.
*   4: for RGBA.

The ZLIB compression level, `compression`, can be -1 for the PNG-encoder
default or a value from 0 to 9.  9 is the highest compression level, generating
the smallest output, but is slower.
END
}

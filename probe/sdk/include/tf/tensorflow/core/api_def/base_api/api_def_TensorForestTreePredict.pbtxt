op {
  graph_op_name: "TensorForestTreePredict"
  visibility: HIDDEN
  attr {
    name: "logits_dimension"
    description: <<END
Scalar, dimension of the logits.
END
  }
  in_arg {
    name: "tree_handle"
    description: <<END
Handle to the tree resource.
END
  }
  in_arg {
    name: "dense_features"
    description: <<END
Rank 2 dense features tensor.
END
  }
  out_arg {
    name: "logits"
    description: <<END
The logits predictions from the tree for each instance in the batch.
END
  }
  summary: "Output the logits for the given input data"
}

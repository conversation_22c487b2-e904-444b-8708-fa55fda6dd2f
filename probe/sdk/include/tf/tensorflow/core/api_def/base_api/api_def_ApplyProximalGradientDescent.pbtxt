op {
  graph_op_name: "ApplyProximalGradientDescent"
  in_arg {
    name: "var"
    description: <<END
Should be from a Variable().
END
  }
  in_arg {
    name: "alpha"
    description: <<END
Scaling factor. Must be a scalar.
END
  }
  in_arg {
    name: "l1"
    description: <<END
L1 regularization. Must be a scalar.
END
  }
  in_arg {
    name: "l2"
    description: <<END
L2 regularization. Must be a scalar.
END
  }
  in_arg {
    name: "delta"
    description: <<END
The change.
END
  }
  out_arg {
    name: "out"
    description: <<END
Same as "var".
END
  }
  attr {
    name: "use_locking"
    description: <<END
If True, the subtraction will be protected by a lock;
otherwise the behavior is undefined, but may exhibit less contention.
END
  }
  summary: "Update \'*var\' as FOBOS algorithm with fixed learning rate."
  description: <<END
prox_v = var - alpha * delta
var = sign(prox_v)/(1+alpha*l2) * max{|prox_v|-alpha*l1,0}
END
}

op {
  graph_op_name: "TPUPartitionedCall"
  visibility: HIDDE<PERSON>
  in_arg {
    name: "args"
    description: <<END
The arguments to the function.
END
  }
  in_arg {
    name: "device_ordinal"
    description: <<END
The TPU device ordinal to run the function on.
END
  }
  out_arg {
    name: "output"
    description: <<END
The output of the function call.
END
  }
  attr {
    name: "Tin"
    description: <<END
The types of the arguments to the function.
END
  }
  attr {
    name: "Tout"
    description: <<END
The types of the outputs of the function.
END
  }
  attr {
    name: "f"
    description: <<END
The function to call.
END
  }
  summary: "Calls a function placed on a specified TPU device."
}

op {
  graph_op_name: "LoadTPUEmbeddingMDLAdagradLightParameters"
  visibility: HIDDE<PERSON>
  in_arg {
    name: "parameters"
    description: <<END
Value of parameters used in the MDL Adagrad Light optimization algorithm.
END
  }
  in_arg {
    name: "accumulators"
    description: <<END
Value of accumulators used in the MDL Adagrad Light optimization algorithm.
END
  }
  in_arg {
    name: "weights"
    description: <<END
Value of weights used in the MDL Adagrad Light optimization algorithm.
END
  }
  in_arg {
    name: "benefits"
    description: <<END
Value of benefits used in the MDL Adagrad Light optimization algorithm.
END
  }
  summary: "Load MDL Adagrad Light embedding parameters."
  description: <<END
An op that loads optimization parameters into HBM for embedding. Must be
preceded by a ConfigureTPUEmbeddingHost op that sets up the correct
embedding table configuration. For example, this op is used to install
parameters that are loaded from a checkpoint before a training loop is
executed.
END
}

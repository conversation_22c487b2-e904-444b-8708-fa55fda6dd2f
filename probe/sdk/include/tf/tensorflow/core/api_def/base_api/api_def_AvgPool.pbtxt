op {
  graph_op_name: "AvgPool"
  in_arg {
    name: "value"
    description: <<END
4-D with shape `[batch, height, width, channels]`.
END
  }
  out_arg {
    name: "output"
    description: <<END
The average pooled output tensor.
END
  }
  attr {
    name: "ksize"
    description: <<END
The size of the sliding window for each dimension of `value`.
END
  }
  attr {
    name: "strides"
    description: <<END
The stride of the sliding window for each dimension of `value`.
END
  }
  attr {
    name: "padding"
    description: <<END
The type of padding algorithm to use.
END
  }
  attr {
    name: "data_format"
    description: <<END
Specify the data format of the input and output data. With the
default format "NHWC", the data is stored in the order of:
    [batch, in_height, in_width, in_channels].
Alternatively, the format could be "NCHW", the data storage order of:
    [batch, in_channels, in_height, in_width].
END
  }
  summary: "Performs average pooling on the input."
  description: <<END
Each entry in `output` is the mean of the corresponding size `ksize`
window in `value`.
END
}

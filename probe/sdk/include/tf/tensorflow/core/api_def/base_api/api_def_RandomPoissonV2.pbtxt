op {
  graph_op_name: "RandomPoissonV2"
  in_arg {
    name: "shape"
    description: <<END
1-D integer tensor. Shape of independent samples to draw from each
distribution described by the shape parameters given in rate.
END
  }
  in_arg {
    name: "rate"
    description: <<END
A tensor in which each scalar is a "rate" parameter describing the
associated poisson distribution.
END
  }
  out_arg {
    name: "output"
    description: <<END
A tensor with shape `shape + shape(rate)`. Each slice
`[:, ..., :, i0, i1, ...iN]` contains the samples drawn for
`rate[i0, i1, ...iN]`.
END
  }
  attr {
    name: "seed"
    description: <<END
If either `seed` or `seed2` are set to be non-zero, the random number
generator is seeded by the given seed.  Otherwise, it is seeded by a
random seed.
END
  }
  attr {
    name: "seed2"
    description: <<END
A second seed to avoid seed collision.
END
  }
  summary: "Outputs random values from the Poisson distribution(s) described by rate."
  description: <<END
This op uses two algorithms, depending on rate. If rate >= 10, then
the algorithm by <PERSON><PERSON><PERSON> is used to acquire samples via
transformation-rejection.
See http://www.sciencedirect.com/science/article/pii/0167668793909974.

Otherwise, <PERSON><PERSON><PERSON>'s algorithm is used to acquire samples via multiplying uniform
random variables.
See <PERSON> <PERSON>. <PERSON><PERSON>h (1969). Seminumerical Algorithms. The Art of Computer
Programming, Volume 2. Addison Wesley
END
}

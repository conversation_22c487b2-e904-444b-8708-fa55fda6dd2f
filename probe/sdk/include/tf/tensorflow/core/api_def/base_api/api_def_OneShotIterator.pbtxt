op {
  graph_op_name: "OneShotIterator"
  out_arg {
    name: "handle"
    description: <<END
A handle to the iterator that can be passed to an "IteratorGetNext"
op.
END
  }
  attr {
    name: "dataset_factory"
    description: <<END
A function of type `() -> DT_VARIANT`, where the returned
DT_VARIANT is a dataset.
END
  }
  summary: "Makes a \"one-shot\" iterator that can be iterated only once."
  description: <<END
A one-shot iterator bundles the logic for defining the dataset and
the state of the iterator in a single op, which allows simple input
pipelines to be defined without an additional initialization
("MakeIterator") step.

One-shot iterators have the following limitations:

* They do not support parameterization: all logic for creating the underlying
  dataset must be bundled in the `dataset_factory` function.
* They are not resettable. Once a one-shot iterator reaches the end of its
  underlying dataset, subsequent "IteratorGetNext" operations on that
  iterator will always produce an `OutOfRange` error.

For greater flexibility, use "Iterator" and "MakeIterator" to define
an iterator using an arbitrary subgraph, which may capture tensors
(including fed values) as parameters, and which may be reset multiple
times by rerunning "MakeIterator".
END
}

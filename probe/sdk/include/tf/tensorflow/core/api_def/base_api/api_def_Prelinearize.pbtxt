op {
  graph_op_name: "Prelinearize"
  visibility: HIDDEN
  in_arg {
    name: "input"
    description: <<END
A tensor that will be linearized.
END
  }
  attr {
    name: "dtype"
    description: <<END
The type of elements in the tensor.
END
  }
  attr {
    name: "shape"
    description: <<END
The shape of the tensor.
END
  }
  attr {
    name: "layout"
    description: <<END
A vector holding the requested layout in minor-to-major sequence. If a layout
attribute is passed but its values are all -1 the layout will be computed by
the infeed operation.
END
  }
  summary: <<END
An op which linearizes one Tensor value to an opaque variant tensor.
END
}

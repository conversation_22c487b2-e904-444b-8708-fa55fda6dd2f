op {
  graph_op_name: "DestroyResourceOp"
  in_arg {
    name: "resource"
    description: <<END
handle to the resource to delete.
END
  }
  attr {
    name: "ignore_lookup_error"
    description: <<END
whether to ignore the error when the resource
doesn't exist.
END
  }
  summary: "Deletes the resource specified by the handle."
  description: <<END
All subsequent operations using the resource will result in a NotFound
error status.
END
}

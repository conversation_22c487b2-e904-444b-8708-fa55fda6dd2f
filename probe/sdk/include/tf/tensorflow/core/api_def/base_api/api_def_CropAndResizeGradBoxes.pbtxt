op {
  graph_op_name: "CropAndResizeGradBoxes"
  in_arg {
    name: "grads"
    description: <<END
A 4-D tensor of shape `[num_boxes, crop_height, crop_width, depth]`.
END
  }
  in_arg {
    name: "image"
    description: <<END
A 4-D tensor of shape `[batch, image_height, image_width, depth]`.
Both `image_height` and `image_width` need to be positive.
END
  }
  in_arg {
    name: "boxes"
    description: <<END
A 2-D tensor of shape `[num_boxes, 4]`. The `i`-th row of the tensor
specifies the coordinates of a box in the `box_ind[i]` image and is specified
in normalized coordinates `[y1, x1, y2, x2]`. A normalized coordinate value of
`y` is mapped to the image coordinate at `y * (image_height - 1)`, so as the
`[0, 1]` interval of normalized image height is mapped to
`[0, image_height - 1] in image height coordinates. We do allow y1 > y2, in
which case the sampled crop is an up-down flipped version of the original
image. The width dimension is treated similarly. Normalized coordinates
outside the `[0, 1]` range are allowed, in which case we use
`extrapolation_value` to extrapolate the input image values.
END
  }
  in_arg {
    name: "box_ind"
    description: <<END
A 1-D tensor of shape `[num_boxes]` with int32 values in `[0, batch)`.
The value of `box_ind[i]` specifies the image that the `i`-th box refers to.
END
  }
  out_arg {
    name: "output"
    description: <<END
A 2-D tensor of shape `[num_boxes, 4]`.
END
  }
  attr {
    name: "method"
    description: <<END
A string specifying the interpolation method. Only 'bilinear' is
supported for now.
END
  }
  summary: "Computes the gradient of the crop_and_resize op wrt the input boxes tensor."
}

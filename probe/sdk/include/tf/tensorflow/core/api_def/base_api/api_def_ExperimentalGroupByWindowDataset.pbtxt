op {
  graph_op_name: "ExperimentalGroupByWindowDataset"
  visibility: HIDDE<PERSON>
  attr {
    name: "key_func"
    description: <<END
A function mapping an element of `input_dataset`, concatenated
with `key_func_other_arguments` to a scalar value of type DT_INT64.
END
  }
  summary: "Creates a dataset that computes a windowed group-by on `input_dataset`."
  description: <<END
// TODO(mrry): Support non-int64 keys.
END
}

op {
  graph_op_name: "AvgPoolGrad"
  visibility: HIDDEN
  in_arg {
    name: "orig_input_shape"
    description: <<END
1-D.  Shape of the original input to `avg_pool`.
END
  }
  in_arg {
    name: "grad"
    description: <<END
4-D with shape `[batch, height, width, channels]`.  Gradients w.r.t.
the output of `avg_pool`.
END
  }
  out_arg {
    name: "output"
    description: <<END
4-D.  Gradients w.r.t. the input of `avg_pool`.
END
  }
  attr {
    name: "ksize"
    description: <<END
The size of the sliding window for each dimension of the input.
END
  }
  attr {
    name: "strides"
    description: <<END
The stride of the sliding window for each dimension of the input.
END
  }
  attr {
    name: "padding"
    description: <<END
The type of padding algorithm to use.
END
  }
  attr {
    name: "data_format"
    description: <<END
Specify the data format of the input and output data. With the
default format "NHWC", the data is stored in the order of:
    [batch, in_height, in_width, in_channels].
Alternatively, the format could be "NCHW", the data storage order of:
    [batch, in_channels, in_height, in_width].
<PERSON><PERSON>
  }
  summary: "Computes gradients of the average pooling function."
}

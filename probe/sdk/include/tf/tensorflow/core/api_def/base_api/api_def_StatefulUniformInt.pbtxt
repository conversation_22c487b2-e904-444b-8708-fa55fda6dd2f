op {
  graph_op_name: "StatefulUniformInt"
  visibility: HIDDEN
  in_arg {
    name: "resource"
    description: <<END
The handle of the resource variable that stores the state of the RNG.
END
  }
  in_arg {
    name: "algorithm"
    description: <<END
The RNG algorithm.
END
  }
  in_arg {
    name: "shape"
    description: <<END
The shape of the output tensor.
END
  }
  in_arg {
    name: "minval"
    description: <<END
Minimum value (inclusive, scalar).
END
  }
  in_arg {
    name: "maxval"
    description: <<END
Maximum value (exclusive, scalar).
END
  }
  out_arg {
    name: "output"
    description: <<END
Random values with specified shape.
END
  }
  attr {
    name: "dtype"
    description: <<END
The type of the output.
END
  }
  summary: "Outputs random integers from a uniform distribution."
  description: <<END
The generated values are uniform integers in the range `[minval, maxval)`.
The lower bound `minval` is included in the range, while the upper bound
`maxval` is excluded.

The random integers are slightly biased unless `maxval - minval` is an exact
power of two.  The bias is small for values of `maxval - minval` significantly
smaller than the range of the output (either `2^32` or `2^64`).
END
}

op {
  graph_op_name: "MatrixSolve"
  in_arg {
    name: "matrix"
    description: <<<PERSON><PERSON>
Shape is `[..., <PERSON>, <PERSON>]`.
END
  }
  in_arg {
    name: "rhs"
    description: <<END
Shape is `[..., <PERSON>, <PERSON>]`.
E<PERSON>
  }
  out_arg {
    name: "output"
    description: <<END
Shape is `[..., M, K]`.
END
  }
  attr {
    name: "adjoint"
    description: <<<PERSON><PERSON> indicating whether to solve with `matrix` or its (block-wise)
adjoint.
END
  }
  summary: "Solves systems of linear equations."
  description: <<END
`Matrix` is a tensor of shape `[..., M, M]` whose inner-most 2 dimensions
form square matrices. `Rhs` is a tensor of shape `[..., M, K]`. The `output` is
a tensor shape `[..., M, K]`.  If `adjoint` is `False` then each output matrix
satisfies `matrix[..., :, :] * output[..., :, :] = rhs[..., :, :]`.
If `adjoint` is `True` then each output matrix satisfies
`adjoint(matrix[..., :, :]) * output[..., :, :] = rhs[..., :, :]`.
END
}

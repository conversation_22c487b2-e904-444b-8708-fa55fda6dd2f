op {
  graph_op_name: "BatchFunction"
  in_arg {
    name: "in_tensors"
    description: <<END
The tensors to be batched.
END
  }
  in_arg {
    name: "captured_tensors"
    description: <<END
The tensors which are captured in the function, and don't need
to be batched.
END
  }
  out_arg {
    name: "out_tensors"
    description: <<END
The output tensors.
END
  }
  attr {
    name: "num_batch_threads"
    description: <<END
Number of scheduling threads for processing batches of work.
Determines the number of batches processed in parallel.
END
  }
  attr {
    name: "max_batch_size"
    description: <<END
Batch sizes will never be bigger than this.
END
  }
  attr {
    name: "batch_timeout_micros"
    description: <<END
Maximum number of microseconds to wait before outputting
an incomplete batch.
END
  }
  attr {
    name: "max_enqueued_batches"
    description: <<END
Maximum number of batches enqueued. Default: 10.
END
  }
  attr {
    name: "allowed_batch_sizes"
    description: <<END
Optional list of allowed batch sizes. If left empty, does
nothing. Otherwise, supplies a list of batch sizes, causing the op to pad
batches up to one of those sizes. The entries must increase monotonically, and
the final entry must equal max_batch_size.
END
  }
  attr {
    name: "container"
    description: <<END
Controls the scope of sharing of this batch.
END
  }
  attr {
    name: "shared_name"
    description: <<END
Concurrently running instances of batch in the same device with the
same container and shared_name will batch their elements together. If left
empty, the op name will be used as the shared name.
END
  }
  attr {
    name: "Tin"
    description: <<END
the types of tensors to be batched.
END
  }
  attr {
    name: "Tcaptured"
    description: <<END
the types of the captured tensors.
END
  }
  attr {
    name: "Tout"
    description: <<END
the types of the output tensors.
END
  }
  summary: "Batches all the inputs tensors to the computation done by the function."
  description: <<END
So, for example, in the following code

  ```python

  # This input will be captured.
  y = tf.placeholder_with_default(1.0, shape=[])

  @tf.Defun(tf.float32)
  def computation(a):
    return tf.matmul(a, a) + y

  b = gen_batch_ops.batch_function(
          f=computation
          in_tensors=[a],
          captured_tensors=computation.captured_inputs,
          Tout=[o.type for o in computation.definition.signature.output_arg],
          num_batch_threads=1,
          max_batch_size=10,
          batch_timeout_micros=100000,  # 100ms
          allowed_batch_sizes=[3, 10],
          batching_queue="")

If more than one session.run call is simultaneously trying to compute `b`
the values of `a` will be gathered, non-deterministically concatenated
along the first axis, and only one thread will run the computation.

Assumes that all arguments of the function are Tensors which will be batched
along their first dimension.

Arguments that are captured, are not batched. The session.run call which does
the concatenation, will use the values of the captured tensors available to it.
Therefore, typical uses of captured tensors should involve values which remain
unchanged across session.run calls. Inference is a good example of this.

SparseTensor is not supported. The return value of the decorated function
must be a Tensor or a list/tuple of Tensors.
END
}

op {
  graph_op_name: "TensorArrayGatherV3"
  endpoint {
    name: "TensorArrayGather"
  }
  in_arg {
    name: "handle"
    description: <<END
The handle to a TensorArray.
END
  }
  in_arg {
    name: "indices"
    description: <<END
The locations in the TensorArray from which to read tensor elements.
END
  }
  in_arg {
    name: "flow_in"
    description: <<END
A float scalar that enforces proper chaining of operations.
END
  }
  out_arg {
    name: "value"
    description: <<END
All of the elements in the TensorArray, concatenated along a new
axis (the new dimension 0).
END
  }
  attr {
    name: "dtype"
    description: <<END
The type of the elem that is returned.
END
  }
  attr {
    name: "element_shape"
    description: <<END
The expected shape of an element, if known. Used to
validate the shapes of TensorArray elements. If this shape is not
fully specified, gathering zero-size TensorArrays is an error.
END
  }
  summary: "Gather specific elements from the TensorArray into output `value`."
  description: <<END
All elements selected by `indices` must have the same shape.
END
}

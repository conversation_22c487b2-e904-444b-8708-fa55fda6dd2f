op {
  graph_op_name: "ScatterNdNonAliasingAdd"
  in_arg {
    name: "input"
    description: <<END
A Tensor.
END
  }
  in_arg {
    name: "indices"
    description: <<END
A Tensor. Must be one of the following types: `int32`, `int64`.
A tensor of indices into `input`.
END
  }
  in_arg {
    name: "updates"
    description: <<END
A Tensor. Must have the same type as ref. A tensor of updated values
to add to `input`.
END
  }
  out_arg {
    name: "output"
    description: <<END
A `Tensor` with the same shape as `input`, containing values of `input`
updated with `updates`.
END
  }
  summary: "Applies sparse addition to `input` using individual values or slices"
  description: <<END
from `updates` according to indices `indices`.  The updates are non-aliasing:
`input` is only modified in-place if no other operations will use it.
Otherwise, a copy of `input` is made.  This operation has a gradient with
respect to both `input` and `updates`.

`input` is a `Tensor` with rank `P` and `indices` is a `Tensor` of rank `Q`.

`indices` must be integer tensor, containing indices into `input`.
It must be shape \\([d_0, ..., d_{Q-2}, K]\\) where `0 < K <= P`.

The innermost dimension of `indices` (with length `K`) corresponds to
indices into elements (if `K = P`) or `(P-K)`-dimensional slices
(if `K < P`) along the `K`th dimension of `input`.

`updates` is `Tensor` of rank `Q-1+P-K` with shape:

$$[d_0, ..., d_{Q-2}, input.shape[K], ..., input.shape[P-1]].$$

For example, say we want to add 4 scattered elements to a rank-1 tensor to 8
elements. In Python, that addition would look like this:

    input = tf.constant([1, 2, 3, 4, 5, 6, 7, 8])
    indices = tf.constant([[4], [3], [1], [7]])
    updates = tf.constant([9, 10, 11, 12])
    output = tf.scatter_nd_non_aliasing_add(input, indices, updates)
    with tf.Session() as sess:
      print(sess.run(output))

The resulting value `output` would look like this:

    [1, 13, 3, 14, 14, 6, 7, 20]

See `tf.scatter_nd` for more details about how to make updates to slices.
END
}

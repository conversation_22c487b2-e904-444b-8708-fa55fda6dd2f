op {
  graph_op_name: "SparseAccumulatorTakeGradient"
  in_arg {
    name: "handle"
    description: <<END
The handle to a SparseConditionalAccumulator.
END
  }
  in_arg {
    name: "num_required"
    description: <<END
Number of gradients required before we return an aggregate.
END
  }
  out_arg {
    name: "indices"
    description: <<END
Indices of the average of the accumulated sparse gradients.
END
  }
  out_arg {
    name: "values"
    description: <<END
Values of the average of the accumulated sparse gradients.
END
  }
  out_arg {
    name: "shape"
    description: <<END
Shape of the average of the accumulated sparse gradients.
END
  }
  attr {
    name: "dtype"
    description: <<END
The data type of accumulated gradients. Needs to correspond to the type
of the accumulator.
END
  }
  summary: "Extracts the average sparse gradient in a SparseConditionalAccumulator."
  description: <<END
The op will blocks until sufficient (i.e., more than num_required)
gradients have been accumulated. If the accumulator has already
aggregated more than num_required gradients, it will return its
average of the accumulated gradients.  Also automatically increments
the recorded global_step in the accumulator by 1, and resets the
aggregate to 0.
END
}

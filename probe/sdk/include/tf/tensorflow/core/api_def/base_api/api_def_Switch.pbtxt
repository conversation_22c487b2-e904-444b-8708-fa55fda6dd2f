op {
  graph_op_name: "Switch"
  in_arg {
    name: "data"
    description: <<END
The tensor to be forwarded to the appropriate output.
END
  }
  in_arg {
    name: "pred"
    description: <<END
A scalar that specifies which output port will receive data.
END
  }
  out_arg {
    name: "output_false"
    description: <<END
If `pred` is false, data will be forwarded to this output.
END
  }
  out_arg {
    name: "output_true"
    description: <<END
If `pred` is true, data will be forwarded to this output.
END
  }
  summary: "Forwards `data` to the output port determined by `pred`."
  description: <<END
If `pred` is true, the `data` input is forwarded to `output_true`. Otherwise,
the data goes to `output_false`.

See also `RefSwitch` and `Merge`.
END
}

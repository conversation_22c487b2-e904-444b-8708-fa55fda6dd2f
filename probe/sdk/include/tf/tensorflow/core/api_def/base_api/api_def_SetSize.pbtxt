op {
  graph_op_name: "SetSize"
  in_arg {
    name: "set_indices"
    description: <<END
2D `Tensor`, indices of a `SparseTensor`.
END
  }
  in_arg {
    name: "set_values"
    description: <<END
1D `Tensor`, values of a `SparseTensor`.
END
  }
  in_arg {
    name: "set_shape"
    description: <<END
1D `Tensor`, shape of a `SparseTensor`.
END
  }
  out_arg {
    name: "size"
    description: <<END
For `set` ranked `n`, this is a `Tensor` with rank `n-1`, and the same 1st
`n-1` dimensions as `set`. Each value is the number of unique elements in
the corresponding `[0...n-1]` dimension of `set`.
END
  }
  summary: "Number of unique elements along last dimension of input `set`."
  description: <<END
Input `set` is a `SparseTensor` represented by `set_indices`, `set_values`,
and `set_shape`. The last dimension contains values in a set, duplicates are
allowed but ignored.

If `validate_indices` is `True`, this op validates the order and range of `set`
indices.
END
}

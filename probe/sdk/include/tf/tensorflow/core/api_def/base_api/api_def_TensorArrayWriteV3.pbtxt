op {
  graph_op_name: "TensorArrayWriteV3"
  endpoint {
    name: "TensorArrayWrite"
  }
  in_arg {
    name: "handle"
    description: <<END
The handle to a TensorArray.
END
  }
  in_arg {
    name: "index"
    description: <<END
The position to write to inside the TensorArray.
END
  }
  in_arg {
    name: "value"
    description: <<END
The tensor to write to the TensorArray.
END
  }
  in_arg {
    name: "flow_in"
    description: <<END
A float scalar that enforces proper chaining of operations.
END
  }
  out_arg {
    name: "flow_out"
    description: <<END
A float scalar that enforces proper chaining of operations.
END
  }
  summary: "Push an element onto the tensor_array."
}

op {
  graph_op_name: "RandomStandardNormal"
  endpoint {
    name: "RandomNormal"
  }
  in_arg {
    name: "shape"
    description: <<END
The shape of the output tensor.
END
  }
  out_arg {
    name: "output"
    description: <<END
A tensor of the specified shape filled with random normal values.
END
  }
  attr {
    name: "seed"
    description: <<END
If either `seed` or `seed2` are set to be non-zero, the random number
generator is seeded by the given seed.  Otherwise, it is seeded by a
random seed.
END
  }
  attr {
    name: "seed2"
    description: <<END
A second seed to avoid seed collision.
END
  }
  attr {
    name: "dtype"
    description: <<END
The type of the output.
END
  }
  summary: "Outputs random values from a normal distribution."
  description: <<END
The generated values will have mean 0 and standard deviation 1.
END
}

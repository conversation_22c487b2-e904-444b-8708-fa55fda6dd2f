op {
  graph_op_name: "SparseSegmentSqrtN"
  in_arg {
    name: "indices"
    description: <<END
A 1-D tensor. Has same rank as `segment_ids`.
END
  }
  in_arg {
    name: "segment_ids"
    description: <<END
A 1-D tensor. Values should be sorted and can be repeated.
END
  }
  out_arg {
    name: "output"
    description: <<END
Has same shape as data, except for dimension 0 which
has size `k`, the number of segments.
END
  }
  summary: "Computes the sum along sparse segments of a tensor divided by the sqrt of N."
  description: <<END
N is the size of the segment being reduced.

See `tf.sparse.segment_sum` for usage examples.

END
}

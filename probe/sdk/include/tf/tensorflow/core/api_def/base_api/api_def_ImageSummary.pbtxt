op {
  graph_op_name: "ImageSummary"
  in_arg {
    name: "tag"
    description: <<E<PERSON>
Scalar. Used to build the `tag` attribute of the summary values.
END
  }
  in_arg {
    name: "tensor"
    description: <<END
4-D of shape `[batch_size, height, width, channels]` where
`channels` is 1, 3, or 4.
END
  }
  out_arg {
    name: "summary"
    description: <<END
Scalar. Serialized `Summary` protocol buffer.
END
  }
  attr {
    name: "max_images"
    description: <<END
Max number of batch elements to generate images for.
END
  }
  attr {
    name: "bad_color"
    description: <<END
Color to use for pixels with non-finite values.
END
  }
  summary: "Outputs a `Summary` protocol buffer with images."
  description: <<END
The summary has up to `max_images` summary values containing images. The
images are built from `tensor` which must be 4-D with shape `[batch_size,
height, width, channels]` and where `channels` can be:

*  1: `tensor` is interpreted as Grayscale.
*  3: `tensor` is interpreted as RGB.
*  4: `tensor` is interpreted as RGBA.

The images have the same number of channels as the input tensor. For float
input, the values are normalized one image at a time to fit in the range
`[0, 255]`.  `uint8` values are unchanged.  The op uses two different
normalization algorithms:

*  If the input values are all positive, they are rescaled so the largest one
   is 255.

*  If any input value is negative, the values are shifted so input value 0.0
   is at 127.  They are then rescaled so that either the smallest value is 0,
   or the largest one is 255.

The `tag` argument is a scalar `Tensor` of type `string`.  It is used to
build the `tag` of the summary values:

*  If `max_images` is 1, the summary value tag is '*tag*/image'.
*  If `max_images` is greater than 1, the summary value tags are
   generated sequentially as '*tag*/image/0', '*tag*/image/1', etc.

The `bad_color` argument is the color to use in the generated images for
non-finite input values.  It is a `uint8` 1-D tensor of length `channels`.
Each element must be in the range `[0, 255]` (It represents the value of a
pixel in the output image).  Non-finite values in the input tensor are
replaced by this tensor in the output image.  The default value is the color
red.
END
}

op {
  graph_op_name: "Merge"
  in_arg {
    name: "inputs"
    description: <<END
The input tensors, exactly one of which will become available.
END
  }
  out_arg {
    name: "output"
    description: <<END
Will be set to the available input tensor.
END
  }
  out_arg {
    name: "value_index"
    description: <<END
The index of the chosen input tensor in `inputs`.
END
  }
  summary: "Forwards the value of an available tensor from `inputs` to `output`."
  description: <<END
`Merge` waits for at least one of the tensors in `inputs` to become available.
It is usually combined with `Switch` to implement branching.

`Merge` forwards the first tensor to become available to `output`, and sets
`value_index` to its index in `inputs`.
END
}

op {
  graph_op_name: "<PERSON><PERSON>oss"
  in_arg {
    name: "inputs"
    description: <<END
3-D, shape: `(max_time x batch_size x num_classes)`, the logits.
END
  }
  in_arg {
    name: "labels_indices"
    description: <<END
The indices of a `SparseTensor<int32, 2>`.
`labels_indices(i, :) == [b, t]` means `labels_values(i)` stores the id for
`(batch b, time t)`.
END
  }
  in_arg {
    name: "labels_values"
    description: <<END
The values (labels) associated with the given batch and time.
END
  }
  in_arg {
    name: "sequence_length"
    description: <<END
A vector containing sequence lengths (batch).
END
  }
  out_arg {
    name: "loss"
    description: <<END
A vector (batch) containing log-probabilities.
END
  }
  out_arg {
    name: "gradient"
    description: <<END
The gradient of `loss`.  3-D, shape:
`(max_time x batch_size x num_classes)`.
END
  }
  attr {
    name: "preprocess_collapse_repeated"
    description: <<END
Scalar, if true then repeated labels are
collapsed prior to the CTC calculation.
END
  }
  attr {
    name: "ctc_merge_repeated"
    description: <<END
Scalar.  If set to false, *during* CTC calculation
repeated non-blank labels will not be merged and are interpreted as
individual labels.  This is a simplified version of CTC.
END
  }
  attr {
    name: "ignore_longer_outputs_than_inputs"
    description: <<END
Scalar. If set to true, during CTC
calculation, items that have longer output sequences than input sequences
are skipped: they don't contribute to the loss term and have zero-gradient.
END
  }
  summary: "Calculates the CTC Loss (log probability) for each batch entry.  Also calculates"
  description: <<END
the gradient.  This class performs the softmax operation for you, so inputs
should be e.g. linear projections of outputs by an LSTM.
END
}

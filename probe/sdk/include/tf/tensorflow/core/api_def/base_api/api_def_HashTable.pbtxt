op {
  graph_op_name: "HashTable"
  visibility: SKIP
  out_arg {
    name: "table_handle"
    description: <<END
Handle to a table.
END
  }
  attr {
    name: "container"
    description: <<END
If non-empty, this table is placed in the given container.
Otherwise, a default container is used.
END
  }
  attr {
    name: "shared_name"
    description: <<END
If non-empty, this table is shared under the given name across
multiple sessions.
END
  }
  attr {
    name: "use_node_name_sharing"
    description: <<END
If true and shared_name is empty, the table is shared
using the node name.
END
  }
  attr {
    name: "key_dtype"
    description: <<END
Type of the table keys.
END
  }
  attr {
    name: "value_dtype"
    description: <<END
Type of the table values.
END
  }
  summary: "Creates a non-initialized hash table."
  description: <<END
This op creates a hash table, specifying the type of its keys and values.
Before using the table you will have to initialize it.  After initialization the
table will be immutable.
END
}

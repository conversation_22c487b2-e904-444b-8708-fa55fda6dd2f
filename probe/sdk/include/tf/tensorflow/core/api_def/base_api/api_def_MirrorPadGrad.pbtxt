op {
  graph_op_name: "MirrorPadGrad"
  visibility: HID<PERSON><PERSON>
  in_arg {
    name: "input"
    description: <<END
The input tensor to be folded.
END
  }
  in_arg {
    name: "paddings"
    description: <<END
A two-column matrix specifying the padding sizes. The number of
rows must be the same as the rank of `input`.
END
  }
  out_arg {
    name: "output"
    description: <<END
The folded tensor.
END
  }
  attr {
    name: "mode"
    description: <<END
The mode used in the `MirrorPad` op.
END
  }
  summary: "Gradient op for `MirrorPad` op. This op folds a mirror-padded tensor."
  description: <<END
This operation folds the padded areas of `input` by `MirrorPad` according to the
`paddings` you specify. `paddings` must be the same as `paddings` argument
given to the corresponding `MirrorPad` op.

The folded size of each dimension D of the output is:

`input.dim_size(D) - paddings(D, 0) - paddings(D, 1)`

For example:

```
# 't' is [[1, 2, 3], [4, 5, 6], [7, 8, 9]].
# 'paddings' is [[0, 1]], [0, 1]].
# 'mode' is SYMMETRIC.
# rank of 't' is 2.
pad(t, paddings) ==> [[ 1,  5]
                      [11, 28]]
```
END
}

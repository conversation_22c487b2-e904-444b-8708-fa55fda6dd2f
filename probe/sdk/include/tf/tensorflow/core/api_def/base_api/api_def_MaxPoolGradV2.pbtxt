op {
  graph_op_name: "MaxPoolGradV2"
  in_arg {
    name: "orig_input"
    description: <<END
The original input tensor.
END
  }
  in_arg {
    name: "orig_output"
    description: <<END
The original output tensor.
END
  }
  in_arg {
    name: "grad"
    description: <<END
4-D.  Gradients w.r.t. the output of `max_pool`.
END
  }
  in_arg {
    name: "ksize"
    description: <<END
The size of the window for each dimension of the input tensor.
END
  }
  in_arg {
    name: "strides"
    description: <<END
The stride of the sliding window for each dimension of the
input tensor.
END
  }
  out_arg {
    name: "output"
    description: <<END
Gradients w.r.t. the input to `max_pool`.
END
  }
  attr {
    name: "padding"
    description: <<END
The type of padding algorithm to use.
END
  }
  attr {
    name: "data_format"
    description: <<END
Specify the data format of the input and output data. With the
default format "NHWC", the data is stored in the order of:
    [batch, in_height, in_width, in_channels].
Alternatively, the format could be "NCHW", the data storage order of:
    [batch, in_channels, in_height, in_width].
<PERSON><PERSON>
  }
  summary: "Computes gradients of the maxpooling function."
}

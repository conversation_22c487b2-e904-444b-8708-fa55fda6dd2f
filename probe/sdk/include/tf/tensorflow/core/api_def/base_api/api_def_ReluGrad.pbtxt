op {
  graph_op_name: "ReluGrad"
  visibility: HIDDEN
  in_arg {
    name: "gradients"
    description: <<END
The backpropagated gradients to the corresponding Relu operation.
END
  }
  in_arg {
    name: "features"
    description: <<END
The features passed as input to the corresponding Relu operation, OR
the outputs of that operation (both work equivalently).
END
  }
  out_arg {
    name: "backprops"
    description: <<END
`gradients * (features > 0)`.
END
  }
  summary: "Computes rectified linear gradients for a Relu operation."
}

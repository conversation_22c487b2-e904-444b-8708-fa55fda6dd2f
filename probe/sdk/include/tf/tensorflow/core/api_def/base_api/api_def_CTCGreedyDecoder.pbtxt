op {
  graph_op_name: "CTCGreedyDecoder"
  in_arg {
    name: "inputs"
    description: <<END
3-D, shape: `(max_time x batch_size x num_classes)`, the logits.
END
  }
  in_arg {
    name: "sequence_length"
    description: <<END
A vector containing sequence lengths, size `(batch_size)`.
END
  }
  out_arg {
    name: "decoded_indices"
    description: <<END
Indices matrix, size `(total_decoded_outputs x 2)`,
of a `SparseTensor<int64, 2>`.  The rows store: [batch, time].
END
  }
  out_arg {
    name: "decoded_values"
    description: <<END
Values vector, size: `(total_decoded_outputs)`,
of a `SparseTensor<int64, 2>`.  The vector stores the decoded classes.
END
  }
  out_arg {
    name: "decoded_shape"
    description: <<END
Shape vector, size `(2)`, of the decoded SparseTensor.
Values are: `[batch_size, max_decoded_length]`.
END
  }
  out_arg {
    name: "log_probability"
    description: <<END
Matrix, size `(batch_size x 1)`, containing sequence
log-probabilities.
END
  }
  attr {
    name: "merge_repeated"
    description: <<END
If True, merge repeated classes in output.
END
  }
  summary: "Performs greedy decoding on the logits given in inputs."
  description: <<END
A note about the attribute merge_repeated: if enabled, when
consecutive logits' maximum indices are the same, only the first of
these is emitted.  Labeling the blank '*', the sequence "A B B * B B"
becomes "A B B" if merge_repeated = True and "A B B B B" if
merge_repeated = False.

Regardless of the value of merge_repeated, if the maximum index of a given
time and batch corresponds to the blank, index `(num_classes - 1)`, no new
element is emitted.
END
}

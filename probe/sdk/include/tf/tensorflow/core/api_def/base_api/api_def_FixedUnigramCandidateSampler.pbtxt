op {
  graph_op_name: "FixedUnigramCandidateSampler"
  in_arg {
    name: "true_classes"
    description: <<END
A batch_size * num_true matrix, in which each row contains the
IDs of the num_true target_classes in the corresponding original label.
END
  }
  out_arg {
    name: "sampled_candidates"
    description: <<END
A vector of length num_sampled, in which each element is
the ID of a sampled candidate.
END
  }
  out_arg {
    name: "true_expected_count"
    description: <<END
A batch_size * num_true matrix, representing
the number of times each candidate is expected to occur in a batch
of sampled candidates. If unique=true, then this is a probability.
END
  }
  out_arg {
    name: "sampled_expected_count"
    description: <<END
A vector of length num_sampled, for each sampled
candidate representing the number of times the candidate is expected
to occur in a batch of sampled candidates.  If unique=true, then this is a
probability.
END
  }
  attr {
    name: "num_true"
    description: <<END
Number of true labels per context.
END
  }
  attr {
    name: "num_sampled"
    description: <<END
Number of candidates to randomly sample.
END
  }
  attr {
    name: "unique"
    description: <<END
If unique is true, we sample with rejection, so that all sampled
candidates in a batch are unique. This requires some approximation to
estimate the post-rejection sampling probabilities.
END
  }
  attr {
    name: "range_max"
    description: <<END
The sampler will sample integers from the interval [0, range_max).
END
  }
  attr {
    name: "vocab_file"
    description: <<END
Each valid line in this file (which should have a CSV-like format)
corresponds to a valid word ID. IDs are in sequential order, starting from
num_reserved_ids. The last entry in each line is expected to be a value
corresponding to the count or relative probability. Exactly one of vocab_file
and unigrams needs to be passed to this op.
END
  }
  attr {
    name: "distortion"
    description: <<END
The distortion is used to skew the unigram probability distribution.
Each weight is first raised to the distortion's power before adding to the
internal unigram distribution. As a result, distortion = 1.0 gives regular
unigram sampling (as defined by the vocab file), and distortion = 0.0 gives
a uniform distribution.
END
  }
  attr {
    name: "num_reserved_ids"
    description: <<END
Optionally some reserved IDs can be added in the range [0,
..., num_reserved_ids) by the users. One use case is that a special unknown
word token is used as ID 0. These IDs will have a sampling probability of 0.
END
  }
  attr {
    name: "num_shards"
    description: <<END
A sampler can be used to sample from a subset of the original range
in order to speed up the whole computation through parallelism. This parameter
(together with 'shard') indicates the number of partitions that are being
used in the overall computation.
END
  }
  attr {
    name: "shard"
    description: <<END
A sampler can be used to sample from a subset of the original range
in order to speed up the whole computation through parallelism. This parameter
(together with 'num_shards') indicates the particular partition number of a
sampler op, when partitioning is being used.
END
  }
  attr {
    name: "unigrams"
    description: <<END
A list of unigram counts or probabilities, one per ID in sequential
order. Exactly one of vocab_file and unigrams should be passed to this op.
END
  }
  attr {
    name: "seed"
    description: <<END
If either seed or seed2 are set to be non-zero, the random number
generator is seeded by the given seed.  Otherwise, it is seeded by a
random seed.
END
  }
  attr {
    name: "seed2"
    description: <<END
An second seed to avoid seed collision.
END
  }
  summary: "Generates labels for candidate sampling with a learned unigram distribution."
  description: <<END
A unigram sampler could use a fixed unigram distribution read from a
file or passed in as an in-memory array instead of building up the distribution
from data on the fly. There is also an option to skew the distribution by
applying a distortion power to the weights.

The vocabulary file should be in CSV-like format, with the last field
being the weight associated with the word.

For each batch, this op picks a single set of sampled candidate labels.

The advantages of sampling candidates per-batch are simplicity and the
possibility of efficient dense matrix multiplication. The disadvantage is that
the sampled candidates must be chosen independently of the context and of the
true labels.
END
}

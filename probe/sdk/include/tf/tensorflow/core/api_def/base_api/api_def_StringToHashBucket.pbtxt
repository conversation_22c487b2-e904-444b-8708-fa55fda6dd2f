op {
  graph_op_name: "StringToHashBucket"
  out_arg {
    name: "output"
    description: <<END
A Tensor of the same shape as the input `string_tensor`.
END
  }
  attr {
    name: "num_buckets"
    description: <<END
The number of buckets.
END
  }
  summary: "Converts each string in the input Tensor to its hash mod by a number of buckets."
  description: <<END
The hash function is deterministic on the content of the string within the
process.

Note that the hash function may change from time to time.
This functionality will be deprecated and it's recommended to use
`tf.string_to_hash_bucket_fast()` or `tf.string_to_hash_bucket_strong()`.
END
}

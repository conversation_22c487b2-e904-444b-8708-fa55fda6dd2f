op {
  graph_op_name: "ExtractJpegShape"
  in_arg {
    name: "contents"
    description: <<END
0-D. The JPEG-encoded image.
END
  }
  out_arg {
    name: "image_shape"
    description: <<END
1-D. The image shape with format [height, width, channels].
END
  }
  attr {
    name: "output_type"
    description: <<END
(Optional) The output type of the operation (int32 or int64).
Defaults to int32.
END
  }
  summary: "Extract the shape information of a JPEG-encoded image."
  description: <<END
This op only parses the image header, so it is much faster than DecodeJpeg.
END
}

op {
  graph_op_name: "AudioSummaryV2"
  endpoint {
    name: "AudioSummary"
  }
  in_arg {
    name: "tag"
    description: <<END
Scalar. Used to build the `tag` attribute of the summary values.
END
  }
  in_arg {
    name: "tensor"
    description: <<END
2-D of shape `[batch_size, frames]`.
END
  }
  in_arg {
    name: "sample_rate"
    description: <<END
The sample rate of the signal in hertz.
END
  }
  out_arg {
    name: "summary"
    description: <<END
Scalar. Serialized `Summary` protocol buffer.
END
  }
  attr {
    name: "max_outputs"
    description: <<END
Max number of batch elements to generate audio for.
END
  }
  summary: "Outputs a `Summary` protocol buffer with audio."
  description: <<END
The summary has up to `max_outputs` summary values containing audio. The
audio is built from `tensor` which must be 3-D with shape `[batch_size,
frames, channels]` or 2-D with shape `[batch_size, frames]`. The values are
assumed to be in the range of `[-1.0, 1.0]` with a sample rate of `sample_rate`.

The `tag` argument is a scalar `Tensor` of type `string`.  It is used to
build the `tag` of the summary values:

*  If `max_outputs` is 1, the summary value tag is '*tag*/audio'.
*  If `max_outputs` is greater than 1, the summary value tags are
   generated sequentially as '*tag*/audio/0', '*tag*/audio/1', etc.
END
}

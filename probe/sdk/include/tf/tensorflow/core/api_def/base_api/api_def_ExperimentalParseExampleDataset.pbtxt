op {
  graph_op_name: "ExperimentalParseExampleDataset"
  visibility: HIDDEN
  in_arg {
    name: "dense_defaults"
    description: <<END
A dict mapping string keys to `Tensor`s.
The keys of the dict must match the dense_keys of the feature.
END
  }
  attr {
    name: "sparse_keys"
    description: <<END
A list of string keys in the examples features.
The results for these keys will be returned as `SparseTensor` objects.
END
  }
  attr {
    name: "dense_keys"
    description: <<END
A list of Ndense string Tensors (scalars).
The keys expected in the Examples features associated with dense values.
END
  }
  attr {
    name: "sparse_types"
    description: <<END
A list of `DTypes` of the same length as `sparse_keys`.
Only `tf.float32` (`FloatList`), `tf.int64` (`Int64List`),
and `tf.string` (`BytesList`) are supported.
END
  }
    attr {
    name: "Tdense"
    description: <<END
A list of DTypes of the same length as `dense_keys`.
Only `tf.float32` (`FloatList`), `tf.int64` (`Int64List`),
and `tf.string` (`BytesList`) are supported.

END
  }
  attr {
    name: "dense_shapes"
    description: <<END
List of tuples with the same length as `dense_keys`.
The shape of the data for each dense feature referenced by `dense_keys`.
Required for any input tensors identified by `dense_keys`.  Must be
either fully defined, or may contain an unknown first dimension.
An unknown first dimension means the feature is treated as having
a variable number of blocks, and the output shape along this dimension
is considered unknown at graph build time.  Padding is applied for
minibatch elements smaller than the maximum number of blocks for the
given feature along this dimension.
END
  }
    attr {
    name: "output_types"
    description: <<END
The type list for the return values.
END
  }
    attr {
    name: "output_shapes"
    description: <<END
The list of shapes being produced.
END
  }
   summary: "Transforms `input_dataset` containing `Example` protos as vectors of DT_STRING into a dataset of `Tensor` or `SparseTensor` objects representing the parsed features."
}


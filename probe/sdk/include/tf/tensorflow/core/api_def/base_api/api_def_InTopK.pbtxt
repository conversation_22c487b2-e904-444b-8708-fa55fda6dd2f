op {
  graph_op_name: "InTopK"
  in_arg {
    name: "predictions"
    description: <<END
A `batch_size` x `classes` tensor.
END
  }
  in_arg {
    name: "targets"
    description: <<END
A `batch_size` vector of class ids.
E<PERSON>
  }
  out_arg {
    name: "precision"
    description: <<<PERSON><PERSON>
Computed Precision at `k` as a `bool Tensor`.
END
  }
  attr {
    name: "k"
    description: <<END
Number of top elements to look at for computing precision.
END
  }
  summary: "Says whether the targets are in the top `K` predictions."
  description: <<END
This outputs a `batch_size` bool array, an entry `out[i]` is `true` if the
prediction for the target class is among the top `k` predictions among
all predictions for example `i`. Note that the behavior of `InTopK` differs
from the `TopK` op in its handling of ties; if multiple classes have the
same prediction value and straddle the top-`k` boundary, all of those
classes are considered to be in the top `k`.

More formally, let

  \\(predictions_i\\) be the predictions for all classes for example `i`,
  \\(targets_i\\) be the target class for example `i`,
  \\(out_i\\) be the output for example `i`,

$$out_i = predictions_{i, targets_i} \in TopKIncludingTies(predictions_i)$$
END
}

op {
  graph_op_name: "Cumsum"
  in_arg {
    name: "x"
    description: <<END
A `Tensor`. Must be one of the following types: `float32`, `float64`,
`int64`, `int32`, `uint8`, `uint16`, `int16`, `int8`, `complex64`,
`complex128`, `qint8`, `quint8`, `qint32`, `half`.
END
  }
  in_arg {
    name: "axis"
    description: <<END
A `Tensor` of type `int32` (default: 0). Must be in the range
`[-rank(x), rank(x))`.
END
  }
  attr {
    name: "exclusive"
    description: <<END
If `True`, perform exclusive cumsum.
END
  }
  attr {
    name: "reverse"
    description: <<END
A `bool` (default: False).
END
  }
  summary: "Compute the cumulative sum of the tensor `x` along `axis`."
  description: <<END
By default, this op performs an inclusive cumsum, which means that the first
element of the input is identical to the first element of the output:

```python
tf.cumsum([a, b, c])  # => [a, a + b, a + b + c]
```

By setting the `exclusive` kwarg to `True`, an exclusive cumsum is
performed instead:

```python
tf.cumsum([a, b, c], exclusive=True)  # => [0, a, a + b]
```

By setting the `reverse` kwarg to `True`, the cumsum is performed in the
opposite direction:

```python
tf.cumsum([a, b, c], reverse=True)  # => [a + b + c, b + c, c]
```

This is more efficient than using separate `tf.reverse` ops.

The `reverse` and `exclusive` kwargs can also be combined:

```python
tf.cumsum([a, b, c], exclusive=True, reverse=True)  # => [b + c, c, 0]
```
END
}

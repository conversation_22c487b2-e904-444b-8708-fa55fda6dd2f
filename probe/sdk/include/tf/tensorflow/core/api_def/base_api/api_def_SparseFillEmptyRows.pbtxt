op {
  graph_op_name: "SparseFillEmptyRows"
  in_arg {
    name: "indices"
    description: <<END
2-D. the indices of the sparse tensor.
END
  }
  in_arg {
    name: "values"
    description: <<END
1-D. the values of the sparse tensor.
END
  }
  in_arg {
    name: "dense_shape"
    description: <<END
1-D. the shape of the sparse tensor.
END
  }
  in_arg {
    name: "default_value"
    description: <<END
0-D. default value to insert into location `[row, 0, ..., 0]`
  for rows missing from the input sparse tensor.
output indices: 2-D. the indices of the filled sparse tensor.
END
  }
  out_arg {
    name: "output_values"
    description: <<END
1-D. the values of the filled sparse tensor.
END
  }
  out_arg {
    name: "empty_row_indicator"
    description: <<END
1-D. whether the dense row was missing in the
input sparse tensor.
END
  }
  out_arg {
    name: "reverse_index_map"
    description: <<END
1-D. a map from the input indices to the output indices.
END
  }
  summary: "Fills empty rows in the input 2-D `SparseTensor` with a default value."
  description: <<END
The input `SparseTensor` is represented via the tuple of inputs
(`indices`, `values`, `dense_shape`).  The output `SparseTensor` has the
same `dense_shape` but with indices `output_indices` and values
`output_values`.

This op inserts a single entry for every row that doesn't have any values.
The index is created as `[row, 0, ..., 0]` and the inserted value
is `default_value`.

For example, suppose `sp_input` has shape `[5, 6]` and non-empty values:

    [0, 1]: a
    [0, 3]: b
    [2, 0]: c
    [3, 1]: d

Rows 1 and 4 are empty, so the output will be of shape `[5, 6]` with values:

    [0, 1]: a
    [0, 3]: b
    [1, 0]: default_value
    [2, 0]: c
    [3, 1]: d
    [4, 0]: default_value

The output `SparseTensor` will be in row-major order and will have the
same shape as the input.

This op also returns an indicator vector shaped `[dense_shape[0]]` such that

    empty_row_indicator[i] = True iff row i was an empty row.

And a reverse index map vector shaped `[indices.shape[0]]` that is used during
backpropagation,

    reverse_index_map[j] = out_j s.t. indices[j, :] == output_indices[out_j, :]
END
}

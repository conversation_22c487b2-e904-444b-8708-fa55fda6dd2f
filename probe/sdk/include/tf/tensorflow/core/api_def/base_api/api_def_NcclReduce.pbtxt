op {
  graph_op_name: "NcclReduce"
  summary: "Reduces `input` from `num_devices` using `reduction` to a single device."
  description: <<END
Reduces `input` from `num_devices` using `reduction` to a single device.

The graph should be constructed so that all inputs have a valid device
assignment, and the op itself is assigned one of these devices.

input: The input to the reduction.
data: the value of the reduction across all `num_devices` devices.
reduction: the reduction operation to perform.
END
  visibility: HIDDEN
}

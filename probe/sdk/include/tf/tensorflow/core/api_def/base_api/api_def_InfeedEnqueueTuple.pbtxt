op {
  graph_op_name: "InfeedEnqueueTuple"
  visibility: HIDDEN
  in_arg {
    name: "inputs"
    description: <<END
A list of tensors that will be provided using the infeed mechanism.
END
  }
  attr {
    name: "dtypes"
    description: <<END
The element types of each element in `inputs`.
END
  }
  attr {
    name: "shapes"
    description: <<END
The shapes of each tensor in `inputs`.
END
  }
  attr {
    name: "layouts"
    description: <<END
A vector holding the requested layout in minor-to-major sequence for
all the tuple shapes, in the order the shapes appear in the "shapes" input.
The layout elements for a sub-shape can be set to -1, in which case the
corresponding layout will be computed by the infeed operation.
END
  }
  attr {
    name: "device_ordinal"
    description: <<END
The TPU device to use. This should be -1 when the Op
is running on a TPU device, and >= 0 when the Op is running on the CPU
device.
END
  }
  summary: "Feeds multiple Tensor values into the computation as an XLA tuple."
}

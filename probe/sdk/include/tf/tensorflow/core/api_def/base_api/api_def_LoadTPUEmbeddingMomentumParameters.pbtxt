op {
  graph_op_name: "LoadTPUEmbeddingMomentumParameters"
  visibility: HIDDEN
  in_arg {
    name: "parameters"
    description: <<END
Value of parameters used in the Momentum optimization algorithm.
END
  }
  in_arg {
    name: "momenta"
    description: <<END
Value of momenta used in the Momentum optimization algorithm.
END
  }
  summary: "Load Momentum embedding parameters."
  description: <<END
An op that loads optimization parameters into HBM for embedding. Must be
preceded by a ConfigureTPUEmbeddingHost op that sets up the correct
embedding table configuration. For example, this op is used to install
parameters that are loaded from a checkpoint before a training loop is
executed.
END
}

op {
  graph_op_name: "SparseCross"
  in_arg {
    name: "indices"
    description: <<END
2-D.  Indices of each input `SparseTensor`.
END
  }
  in_arg {
    name: "values"
    description: <<END
1-D.   values of each `SparseTensor`.
END
  }
  in_arg {
    name: "shapes"
    description: <<END
1-D.   Shapes of each `SparseTensor`.
END
  }
  in_arg {
    name: "dense_inputs"
    description: <<END
2-D.    Columns represented by dense `Tensor`.
END
  }
  out_arg {
    name: "output_indices"
    description: <<END
2-D.  Indices of the concatenated `SparseTensor`.
END
  }
  out_arg {
    name: "output_values"
    description: <<END
1-D.  Non-empty values of the concatenated or hashed
`SparseTensor`.
END
  }
  out_arg {
    name: "output_shape"
    description: <<END
1-D.  Shape of the concatenated `SparseTensor`.
END
  }
  attr {
    name: "hashed_output"
    description: <<END
If true, returns the hash of the cross instead of the string.
This will allow us avoiding string manipulations.
END
  }
  attr {
    name: "num_buckets"
    description: <<END
It is used if hashed_output is true.
output = hashed_value%num_buckets if num_buckets > 0 else hashed_value.
END
  }
  attr {
    name: "hash_key"
    description: <<END
Specify the hash_key that will be used by the `FingerprintCat64`
function to combine the crosses fingerprints.
END
  }
  summary: "Generates sparse cross from a list of sparse and dense tensors."
  description: <<END
The op takes two lists, one of 2D `SparseTensor` and one of 2D `Tensor`, each
representing features of one feature column. It outputs a 2D `SparseTensor` with
the batchwise crosses of these features.

For example, if the inputs are

    inputs[0]: SparseTensor with shape = [2, 2]
    [0, 0]: "a"
    [1, 0]: "b"
    [1, 1]: "c"

    inputs[1]: SparseTensor with shape = [2, 1]
    [0, 0]: "d"
    [1, 0]: "e"

    inputs[2]: Tensor [["f"], ["g"]]

then the output will be

    shape = [2, 2]
    [0, 0]: "a_X_d_X_f"
    [1, 0]: "b_X_e_X_g"
    [1, 1]: "c_X_e_X_g"

if hashed_output=true then the output will be

    shape = [2, 2]
    [0, 0]: FingerprintCat64(
                Fingerprint64("f"), FingerprintCat64(
                    Fingerprint64("d"), Fingerprint64("a")))
    [1, 0]: FingerprintCat64(
                Fingerprint64("g"), FingerprintCat64(
                    Fingerprint64("e"), Fingerprint64("b")))
    [1, 1]: FingerprintCat64(
                Fingerprint64("g"), FingerprintCat64(
                    Fingerprint64("e"), Fingerprint64("c")))
END
}

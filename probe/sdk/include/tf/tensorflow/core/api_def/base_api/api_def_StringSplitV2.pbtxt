op {
  graph_op_name: "StringSplitV2"
  in_arg {
    name: "input"
    description: <<END
`1-D` string `Tensor`, the strings to split.
END
  }
  in_arg {
    name: "sep"
    description: <<END
`0-D` string `Tensor`, the delimiter character.
END
  }
  attr {
    name: "maxsplit"
    description: <<END
An `int`. If `maxsplit > 0`, limit of the split of the result.
END
  }
  summary: "Split elements of `source` based on `sep` into a `SparseTensor`."
  description: <<END
Let N be the size of source (typically N will be the batch size). Split each
element of `source` based on `sep` and return a `SparseTensor`
containing the split tokens. Empty tokens are ignored.

For example, N = 2, source[0] is 'hello world' and source[1] is 'a b c',
then the output will be
```
st.indices = [0, 0;
              0, 1;
              1, 0;
              1, 1;
              1, 2]
st.shape = [2, 3]
st.values = ['hello', 'world', 'a', 'b', 'c']
```

If `sep` is given, consecutive delimiters are not grouped together and are
deemed to delimit empty strings. For example, source of `"1<>2<><>3"` and
sep of `"<>"` returns `["1", "2", "", "3"]`. If `sep` is None or an empty
string, consecutive whitespace are regarded as a single separator, and the
result will contain no empty strings at the startor end if the string has
leading or trailing whitespace.

Note that the above mentioned behavior matches python's str.split.
END
}

op {
  graph_op_name: "BatchDatasetV2"
  visibility: HIDDEN
  in_arg {
    name: "batch_size"
    description: <<END
A scalar representing the number of elements to accumulate in a batch.
END
  }
  in_arg {
    name: "drop_remainder"
    description: <<END
A scalar representing whether the last batch should be dropped in case its size
is smaller than desired.
END
  }
  summary: "Creates a dataset that batches `batch_size` elements from `input_dataset`."
}

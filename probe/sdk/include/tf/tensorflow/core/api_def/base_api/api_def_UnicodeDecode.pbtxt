op {
  graph_op_name: "UnicodeDecode"
  in_arg {
    name: "input"
    description: <<END
The text to be decoded. Can have any shape. Note that the output is flattened
to a vector of char values.
END
  }
  out_arg {
    name: "row_splits"
    description: <<END
A 1D int32 tensor containing the row splits.
END
  }
  out_arg {
    name: "char_values"
    description: <<END
A 1D int32 Tensor containing the decoded codepoints.
END
  }
  attr {
    name: "input_encoding"
    description: <<END
Text encoding of the input strings. This is any of the encodings supported
by ICU ucnv algorithmic converters. Examples: `"UTF-16", "US ASCII", "UTF-8"`.
END
  }
  attr {
    name: "errors"
    description: <<END
Error handling policy when there is invalid formatting found in the input.
The value of 'strict' will cause the operation to produce a InvalidArgument
error on any invalid input formatting. A value of 'replace' (the default) will
cause the operation to replace any invalid formatting in the input with the
`replacement_char` codepoint. A value of 'ignore' will cause the operation to
skip any invalid formatting in the input and produce no corresponding output
character.
END
  }
  attr {
    name: "replacement_char"
    description: <<END
The replacement character codepoint to be used in place of any invalid
formatting in the input when `errors='replace'`. Any valid unicode codepoint may
be used. The default value is the default unicode replacement character is
0xFFFD or U+65533.)
END
  }
  attr {
    name: "replace_control_characters"
    description: <<END
Whether to replace the C0 control characters (00-1F) with the
`replacement_char`. Default is false.
END
  }
  summary: <<END
Decodes each string in `input` into a sequence of Unicode code points.
END
  description: <<END
The character codepoints for all strings are returned using a single vector
`char_values`, with strings expanded to characters in row-major order.

The `row_splits` tensor indicates where the codepoints for
each input string begin and end within the `char_values` tensor.
In particular, the values for the `i`th
string (in row-major order) are stored in the slice
`[row_splits[i]:row_splits[i+1]]`. Thus:

* `char_values[row_splits[i]+j]` is the Unicode codepoint for the `j`th
  character in the `i`th string (in row-major order).
* `row_splits[i+1] - row_splits[i]` is the number of characters in the `i`th
  string (in row-major order).
END
  visibility: HIDDEN
}

op {
  graph_op_name: "RecvTPUEmbeddingActivations"
  visibility: HIDDEN
  out_arg {
    name: "outputs"
    description: <<END
A TensorList of embedding activations containing one Tensor per
embedding table in the model.
END
  }
  attr {
    name: "num_outputs"
    description: <<END
The number of output activation tensors, equal to the number of
embedding tables in the model.
END
  }
  attr {
    name: "config"
    description: <<END
Serialized TPUEmbeddingConfiguration proto.
END
  }
  summary: "An op that receives embedding activations on the TPU."
  description: <<END
The TPU system performs the embedding lookups and aggregations specified by
the arguments to TPUEmbeddingEnqueue(Integer/Sparse/SparseTensor)Batch. The
results of these aggregations are visible to the Tensorflow Graph as the
outputs of a RecvTPUEmbeddingActivations op. This op returns a list containing
one Tensor of activations per table specified in the model. There can be at
most one RecvTPUEmbeddingActivations op in the TPU graph.
END
}

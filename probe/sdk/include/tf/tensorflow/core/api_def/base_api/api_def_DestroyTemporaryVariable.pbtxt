op {
  graph_op_name: "DestroyTemporaryVariable"
  in_arg {
    name: "ref"
    description: <<END
A reference to the temporary variable tensor.
END
  }
  attr {
    name: "var_name"
    description: <<END
Name of the temporary variable, usually the name of the matching
'TemporaryVariable' op.
END
  }
  summary: "Destroys the temporary variable and returns its final value."
  description: <<END
Sets output to the value of the Tensor pointed to by 'ref', then destroys
the temporary variable called 'var_name'.
All other uses of 'ref' *must* have executed before this op.
This is typically achieved by chaining the ref through each assign op, or by
using control dependencies.

Outputs the final value of the tensor pointed to by 'ref'.
END
}

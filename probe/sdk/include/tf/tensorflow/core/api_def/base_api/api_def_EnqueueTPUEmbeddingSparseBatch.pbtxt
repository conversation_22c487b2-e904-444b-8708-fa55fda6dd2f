op {
  graph_op_name: "EnqueueTPUEmbeddingSparseBatch"
  visibility: HIDDEN
  in_arg {
    name: "sample_indices"
    description: <<END
A list of rank 1 Tensors specifying the training example and
feature to which the corresponding embedding_indices and aggregation_weights
values belong. sample_indices[i] must equal b * nf + f, where nf is the
number of features from the corresponding table, f is in [0, nf), and
b is in [0, batch size).
END
  }
  in_arg {
    name: "embedding_indices"
    description: <<END
A list of rank 1 Tensors, indices into the embedding tables.
END
  }
  in_arg {
    name: "aggregation_weights"
    description: <<END
A list of rank 1 Tensors containing per sample -- i.e. per
(training example, feature) -- aggregation weights.
END
  }
  in_arg {
    name: "mode_override"
    description: <<END
A string input that overrides the mode specified in the
TPUEmbeddingConfiguration. Supported values are {'unspecified', 'inference',
'training', 'backward_pass_only'}. When set to 'unspecified', the mode set
in TPUEmbeddingConfiguration is used, otherwise mode_override is used.
END
  }
  attr {
    name: "device_ordinal"
    description: <<END
The TPU device to use. Should be >= 0 and less than the number
of TPU cores in the task on which the node is placed.
END
  }
  attr {
    name: "combiners"
    description: <<END
A list of string scalars, one for each embedding table that specify
how to normalize the embedding activations after weighted summation.
Supported combiners are 'mean', 'sum', or 'sqrtn'. It is invalid to have
the sum of the weights be 0 for 'mean' or the sum of the squared weights be
0 for 'sqrtn'. If combiners isn't passed, the default is to use 'sum' for
all tables.
END
  }
  summary: "An op that enqueues TPUEmbedding input indices from a SparseTensor."
  description: <<END
This Op eases the porting of code that uses embedding_lookup_sparse(),
although some Python preprocessing of the SparseTensor arguments to
embedding_lookup_sparse() is required to produce the arguments to this Op,
since only a single EnqueueTPUEmbeddingSparseBatch Op is allowed per training
step.

The tensors at corresponding positions in the three input lists
must have the same shape, i.e. rank 1 with dim_size() equal to the total
number of lookups into the table described by the corresponding table_id.
END
}

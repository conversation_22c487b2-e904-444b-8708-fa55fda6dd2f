op {
  graph_op_name: "SoftmaxCrossEntropyWithLogits"
  in_arg {
    name: "features"
    description: <<END
batch_size x num_classes matrix
END
  }
  in_arg {
    name: "labels"
    description: <<END
batch_size x num_classes matrix
The caller must ensure that each batch of labels represents a valid
probability distribution.
END
  }
  out_arg {
    name: "loss"
    description: <<END
Per example loss (batch_size vector).
END
  }
  out_arg {
    name: "backprop"
    description: <<END
backpropagated gradients (batch_size x num_classes matrix).
END
  }
  summary: "Computes softmax cross entropy cost and gradients to backpropagate."
  description: <<END
Inputs are the logits, not probabilities.
END
}

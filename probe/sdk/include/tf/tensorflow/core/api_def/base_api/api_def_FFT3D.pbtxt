op {
  graph_op_name: "FFT3D"
  in_arg {
    name: "input"
    description: <<END
A complex64 tensor.
END
  }
  out_arg {
    name: "output"
    description: <<END
A complex64 tensor of the same shape as `input`. The inner-most 3
  dimensions of `input` are replaced with their 3D Fourier transform.

@compatibility(numpy)
Equivalent to np.fft.fftn with 3 dimensions.
@end_compatibility
END
  }
  summary: "3D fast Fourier transform."
  description: <<END
Computes the 3-dimensional discrete Fourier transform over the inner-most 3
dimensions of `input`.
END
}

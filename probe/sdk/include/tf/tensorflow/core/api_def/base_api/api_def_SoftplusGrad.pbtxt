op {
  graph_op_name: "SoftplusGrad"
  visibility: HIDDEN
  in_arg {
    name: "gradients"
    description: <<END
The backpropagated gradients to the corresponding softplus operation.
END
  }
  in_arg {
    name: "features"
    description: <<END
The features passed as input to the corresponding softplus operation.
END
  }
  out_arg {
    name: "backprops"
    description: <<END
The gradients: `gradients / (1 + exp(-features))`.
END
  }
  summary: "Computes softplus gradients for a softplus operation."
}

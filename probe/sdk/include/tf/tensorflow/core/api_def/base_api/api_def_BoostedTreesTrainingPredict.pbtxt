op {
  graph_op_name: "BoostedTreesTrainingPredict"
  visibility: HIDDEN
  in_arg {
    name: "cached_tree_ids"
    description: <<END
Rank 1 Tensor containing cached tree ids which is the starting
tree of prediction.
END
  }
  in_arg {
    name: "cached_node_ids"
    description: <<END
Rank 1 Tensor containing cached node id which is the starting
node of prediction.
END
  }
  in_arg {
    name: "bucketized_features"
    description: <<END
A list of rank 1 Tensors containing bucket id for each
feature.
END
  }
  out_arg {
    name: "partial_logits"
    description: <<END
Rank 2 Tensor containing logits update (with respect to cached
values stored) for each example.
END
  }
  out_arg {
    name: "tree_ids"
    description: <<END
Rank 1 Tensor containing new tree ids for each example.
END
  }
  out_arg {
    name: "node_ids"
    description: <<END
Rank 1 Tensor containing new node ids in the new tree_ids.
END
  }
  attr {
    name: "num_bucketized_features"
    description: <<END
Inferred.
END
  }
  attr {
    name: "logits_dimension"
    description: <<END
scalar, dimension of the logits, to be used for partial logits
shape.
END
  }
  summary: "Runs multiple additive regression ensemble predictors on input instances and"
  description: <<END
computes the update to cached logits. It is designed to be used during training.
It traverses the trees starting from cached tree id and cached node id and
calculates the updates to be pushed to the cache.
END
}

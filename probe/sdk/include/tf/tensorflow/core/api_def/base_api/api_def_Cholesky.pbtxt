op {
  graph_op_name: "Cholesky"
  in_arg {
    name: "input"
    description: <<END
Shape is `[..., <PERSON>, <PERSON>]`.
END
  }
  out_arg {
    name: "output"
    description: <<END
Shape is `[..., <PERSON>, <PERSON>]`.
END
  }
  summary: "Computes the Cholesky decomposition of one or more square matrices."
  description: <<END
The input is a tensor of shape `[..., M, M]` whose inner-most 2 dimensions
form square matrices.

The input has to be symmetric and positive definite. Only the lower-triangular
part of the input will be used for this operation. The upper-triangular part
will not be read.

The output is a tensor of the same shape as the input
containing the Cholesky decompositions for all input submatrices `[..., :, :]`.

**Note**: The gradient computation on GPU is faster for large matrices but
not for large batch dimensions when the submatrices are small. In this
case it might be faster to use the CPU.
END
}

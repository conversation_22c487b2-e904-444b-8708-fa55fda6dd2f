op {
  graph_op_name: "LoadAndRemapMatrix"
  in_arg {
    name: "ckpt_path"
    description: <<END
Path to the TensorFlow checkpoint (version 2, `TensorBundle`) from
which the old matrix `Tensor` will be loaded.
END
  }
  in_arg {
    name: "old_tensor_name"
    description: <<END
Name of the 2-D `Tensor` to load from checkpoint.
END
  }
  in_arg {
    name: "row_remapping"
    description: <<END
An int `Tensor` of row remappings (generally created by
`generate_vocab_remapping`).  Even if no row remapping is needed, this must
still be an index-valued Tensor (e.g. [0, 1, 2, ...]), or a shifted
index-valued `Tensor` (e.g. [8, 9, 10, ...], for partitioned `Variables`).
END
  }
  in_arg {
    name: "col_remapping"
    description: <<END
An int `Tensor` of column remappings (generally created by
`generate_vocab_remapping`).  May be a size-0 `Tensor` if only row remapping
is to be done (e.g. column ordering is the same).
END
  }
  in_arg {
    name: "initializing_values"
    description: <<END
A float `Tensor` containing  values to fill in for cells
in the output matrix that are not loaded from the checkpoint. Length must be
exactly the same as the number of missing / new cells.
END
  }
  out_arg {
    name: "output_matrix"
    description: <<END
Output matrix containing existing values loaded from the
checkpoint, and with any missing values filled in from initializing_values.
END
  }
  attr {
    name: "num_rows"
    description: <<END
Number of rows (length of the 1st dimension) in the output matrix.
END
  }
  attr {
    name: "num_cols"
    description: <<END
Number of columns (length of the 2nd dimension) in the output matrix.
END
  }
  attr {
    name: "max_rows_in_memory"
    description: <<END
The maximum number of rows to load from the checkpoint at
once. If less than or equal to 0, the entire matrix will be loaded into
memory. Setting this arg trades increased disk reads for lower memory usage.
END
  }
  summary: "Loads a 2-D (matrix) `Tensor` with name `old_tensor_name` from the checkpoint"
  description: <<END
at `ckpt_path` and potentially reorders its rows and columns using the
specified remappings.

Most users should use one of the wrapper initializers (such as
`tf.contrib.framework.load_and_remap_matrix_initializer`) instead of this
function directly.

The remappings are 1-D tensors with the following properties:

* `row_remapping` must have exactly `num_rows` entries. Row `i` of the output
  matrix will be initialized from the row corresponding to index
  `row_remapping[i]` in the old `Tensor` from the checkpoint.
* `col_remapping` must have either 0 entries (indicating that no column
  reordering is needed) or `num_cols` entries. If specified, column `j` of the
  output matrix will be initialized from the column corresponding to index
  `col_remapping[j]` in the old `Tensor` from the checkpoint.
* A value of -1 in either of the remappings signifies a "missing" entry. In that
  case, values from the `initializing_values` tensor will be used to fill that
  missing row or column. If `row_remapping` has `r` missing entries and
  `col_remapping` has `c` missing entries, then the following condition must be
  true:

`(r * num_cols) + (c * num_rows) - (r * c) == len(initializing_values)`

The remapping tensors can be generated using the GenerateVocabRemapping op.

As an example, with row_remapping = [1, 0, -1], col_remapping = [0, 2, -1],
initializing_values = [0.5, -0.5, 0.25, -0.25, 42], and w(i, j) representing
the value from row i, column j of the old tensor in the checkpoint, the output
matrix will look like the following:

[[w(1, 0),  w(1, 2),  0.5],
 [w(0, 0),  w(0, 2), -0.5],
 [0.25,    -0.25,      42]]
END
}

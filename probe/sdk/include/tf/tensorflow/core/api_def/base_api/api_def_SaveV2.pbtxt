op {
  graph_op_name: "SaveV2"
  in_arg {
    name: "prefix"
    description: <<END
Must have a single element. The prefix of the V2 checkpoint to which we
write the tensors.
END
  }
  in_arg {
    name: "tensor_names"
    description: <<END
shape {N}. The names of the tensors to be saved.
END
  }
  in_arg {
    name: "shape_and_slices"
    description: <<END
shape {N}.  The slice specs of the tensors to be saved.
Empty strings indicate that they are non-partitioned tensors.
END
  }
  in_arg {
    name: "tensors"
    description: <<END
`N` tensors to save.
END
  }
  summary: "Saves tensors in V2 checkpoint format."
  description: <<END
By default, saves the named tensors in full.  If the caller wishes to save
specific slices of full tensors, "shape_and_slices" should be non-empty strings
and correspondingly well-formed.
END
}

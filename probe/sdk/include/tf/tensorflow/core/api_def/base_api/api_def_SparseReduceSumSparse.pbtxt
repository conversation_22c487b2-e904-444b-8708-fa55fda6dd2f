op {
  graph_op_name: "SparseReduceSumSparse"
  in_arg {
    name: "input_indices"
    description: <<END
2-D.  `N x R` matrix with the indices of non-empty values in a
SparseTensor, possibly not in canonical ordering.
END
  }
  in_arg {
    name: "input_values"
    description: <<END
1-D.  `N` non-empty values corresponding to `input_indices`.
END
  }
  in_arg {
    name: "input_shape"
    description: <<END
1-D.  Shape of the input SparseTensor.
END
  }
  in_arg {
    name: "reduction_axes"
    description: <<END
1-D.  Length-`K` vector containing the reduction axes.
END
  }
  attr {
    name: "keep_dims"
    description: <<END
If true, retain reduced dimensions with length 1.
END
  }
  summary: "Computes the sum of elements across dimensions of a SparseTensor."
  description: <<END
This Op takes a SparseTensor and is the sparse counterpart to
`tf.reduce_sum()`.  In contrast to SparseReduceSum, this Op returns a
SparseTensor.

Reduces `sp_input` along the dimensions given in `reduction_axes`.  Unless
`keep_dims` is true, the rank of the tensor is reduced by 1 for each entry in
`reduction_axes`. If `keep_dims` is true, the reduced dimensions are retained
with length 1.

If `reduction_axes` has no entries, all dimensions are reduced, and a tensor
with a single element is returned.  Additionally, the axes can be negative,
which are interpreted according to the indexing rules in Python.
END
}

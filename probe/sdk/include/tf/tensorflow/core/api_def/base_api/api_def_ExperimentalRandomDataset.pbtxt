op {
  graph_op_name: "ExperimentalRandomDataset"
  visibility: HIDDE<PERSON>
  in_arg {
    name: "seed"
    description: <<END
A scalar seed for the random number generator. If either seed or
seed2 is set to be non-zero, the random number generator is seeded
by the given seed.  Otherwise, a random seed is used.
END
  }
  in_arg {
    name: "seed2"
    description: <<END
A second scalar seed to avoid seed collision.
END
  }
  summary: "Creates a Dataset that returns pseudorandom numbers."
}

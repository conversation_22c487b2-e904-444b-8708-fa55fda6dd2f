op {
  graph_op_name: "ResizeBicubicGrad"
  visibility: HIDDE<PERSON>
  in_arg {
    name: "grads"
    description: <<END
4-D with shape `[batch, height, width, channels]`.
END
  }
  in_arg {
    name: "original_image"
    description: <<END
4-D with shape `[batch, orig_height, orig_width, channels]`,
The image tensor that was resized.
END
  }
  out_arg {
    name: "output"
    description: <<END
4-D with shape `[batch, orig_height, orig_width, channels]`.
Gradients with respect to the input image. Input image must have been
float or double.
END
  }
  attr {
    name: "align_corners"
    description: <<END
If true, the centers of the 4 corner pixels of the input and grad tensors are
aligned. Defaults to false.
END
  }
  summary: "Computes the gradient of bicubic interpolation."
}

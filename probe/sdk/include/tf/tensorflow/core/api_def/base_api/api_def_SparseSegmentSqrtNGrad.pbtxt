op {
  graph_op_name: "SparseSegmentSqrtNGrad"
  in_arg {
    name: "grad"
    description: <<END
gradient propagated to the SparseSegmentSqrtN op.
END
  }
  in_arg {
    name: "indices"
    description: <<END
indices passed to the corresponding SparseSegmentSqrtN op.
END
  }
  in_arg {
    name: "segment_ids"
    description: <<END
segment_ids passed to the corresponding SparseSegmentSqrtN op.
END
  }
  in_arg {
    name: "output_dim0"
    description: <<END
dimension 0 of "data" passed to SparseSegmentSqrtN op.
END
  }
  summary: "Computes gradients for SparseSegmentSqrtN."
  description: <<END
Returns tensor "output" with same shape as grad, except for dimension 0 whose
value is output_dim0.
END
}

op {
  graph_op_name: "Tile"
  in_arg {
    name: "input"
    description: <<END
1-D or higher.
END
  }
  in_arg {
    name: "multiples"
    description: <<END
1-D. Length must be the same as the number of dimensions in `input`
END
  }
  summary: "Constructs a tensor by tiling a given tensor."
  description: <<END
This operation creates a new tensor by replicating `input` `multiples` times.
The output tensor's i'th dimension has `input.dims(i) * multiples[i]` elements,
and the values of `input` are replicated `multiples[i]` times along the 'i'th
dimension. For example, tiling `[a b c d]` by `[2]` produces
`[a b c d a b c d]`.
END
}

op {
  graph_op_name: "AsString"
  attr {
    name: "precision"
    description: <<END
The post-decimal precision to use for floating point numbers.
Only used if precision > -1.
END
  }
  attr {
    name: "scientific"
    description: <<END
Use scientific notation for floating point numbers.
END
  }
  attr {
    name: "shortest"
    description: <<END
Use shortest representation (either scientific or standard) for
floating point numbers.
END
  }
  attr {
    name: "width"
    description: <<END
Pad pre-decimal numbers to this width.
Applies to both floating point and integer numbers.
Only used if width > -1.
END
  }
  attr {
    name: "fill"
    description: <<END
The value to pad if width > -1.  If empty, pads with spaces.
Another typical value is '0'.  String cannot be longer than 1 character.
END
  }
  summary: "Converts each entry in the given tensor to strings.  Supports many numeric"
  description: <<END
types and boolean.
END
}

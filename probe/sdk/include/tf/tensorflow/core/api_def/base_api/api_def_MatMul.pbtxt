op {
  graph_op_name: "<PERSON><PERSON><PERSON>"
  attr {
    name: "transpose_a"
    description: <<END
If true, "a" is transposed before multiplication.
END
  }
  attr {
    name: "transpose_b"
    description: <<END
If true, "b" is transposed before multiplication.
END
  }
  summary: "Multiply the matrix \"a\" by the matrix \"b\"."
  description: <<END
The inputs must be two-dimensional matrices and the inner dimension of
"a" (after being transposed if transpose_a is true) must match the
outer dimension of "b" (after being transposed if transposed_b is
true).

*Note*: The default kernel implementation for MatMul on GPUs uses
cublas.
END
}

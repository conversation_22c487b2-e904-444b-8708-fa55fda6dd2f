op {
  graph_op_name: "DecodeBase64"
  in_arg {
    name: "input"
    description: <<END
Base64 strings to decode.
END
  }
  out_arg {
    name: "output"
    description: <<END
Decoded strings.
END
  }
  summary: "Decode web-safe base64-encoded strings."
  description: <<END
Input may or may not have padding at the end. See EncodeBase64 for padding.
Web-safe means that input must use - and _ instead of + and /.
END
}

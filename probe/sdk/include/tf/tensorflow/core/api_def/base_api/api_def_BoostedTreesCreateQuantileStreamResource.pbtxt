op {
  graph_op_name: "BoostedTreesCreateQuantileStreamResource"
  visibility: HIDDEN
  in_arg {
    name: "quantile_stream_resource_handle"
    description: <<END
resource; Handle to quantile stream resource.
END
  }
  in_arg {
    name: "epsilon"
    description: <<END
float; The required approximation error of the stream resource.
END
  }
  in_arg {
    name: "num_streams"
    description: <<END
int; The number of streams managed by the resource that shares the same epsilon.
END
  }
  attr {
    name: "max_elements"
    description : <<END
int; The maximum number of data points that can be fed to the stream.
END
  }
  summary: "Create the Resource for Quantile Streams."
}

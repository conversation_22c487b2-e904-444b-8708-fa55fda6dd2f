op {
  graph_op_name: "ParseExample"
  in_arg {
    name: "serialized"
    description: <<END
A vector containing a batch of binary serialized Example protos.
END
  }
  in_arg {
    name: "names"
    description: <<END
A vector containing the names of the serialized protos.
May contain, for example, table key (descriptive) names for the
corresponding serialized protos.  These are purely useful for debugging
purposes, and the presence of values here has no effect on the output.
May also be an empty vector if no names are available.
If non-empty, this vector must be the same length as "serialized".
END
  }
  in_arg {
    name: "sparse_keys"
    description: <<END
A list of Nsparse string Tensors (scalars).
The keys expected in the Examples' features associated with sparse values.
END
  }
  in_arg {
    name: "dense_keys"
    description: <<END
A list of Ndense string Tensors (scalars).
The keys expected in the Examples' features associated with dense values.
END
  }
  in_arg {
    name: "dense_defaults"
    description: <<END
A list of Ndense Tensors (some may be empty).
dense_defaults[j] provides default values
when the example's feature_map lacks dense_key[j].  If an empty Tensor is
provided for dense_defaults[j], then the Feature dense_keys[j] is required.
The input type is inferred from dense_defaults[j], even when it's empty.
If dense_defaults[j] is not empty, and dense_shapes[j] is fully defined,
then the shape of dense_defaults[j] must match that of dense_shapes[j].
If dense_shapes[j] has an undefined major dimension (variable strides dense
feature), dense_defaults[j] must contain a single element:
the padding element.
END
  }
  attr {
    name: "sparse_types"
    description: <<END
A list of Nsparse types; the data types of data in each Feature
given in sparse_keys.
Currently the ParseExample supports DT_FLOAT (FloatList),
DT_INT64 (Int64List), and DT_STRING (BytesList).
END
  }
  attr {
    name: "dense_shapes"
    description: <<END
A list of Ndense shapes; the shapes of data in each Feature
given in dense_keys.
The number of elements in the Feature corresponding to dense_key[j]
must always equal dense_shapes[j].NumEntries().
If dense_shapes[j] == (D0, D1, ..., DN) then the shape of output
Tensor dense_values[j] will be (|serialized|, D0, D1, ..., DN):
The dense outputs are just the inputs row-stacked by batch.
This works for dense_shapes[j] = (-1, D1, ..., DN).  In this case
the shape of the output Tensor dense_values[j] will be
(|serialized|, M, D1, .., DN), where M is the maximum number of blocks
of elements of length D1 * .... * DN, across all minibatch entries
in the input.  Any minibatch entry with less than M blocks of elements of
length D1 * ... * DN will be padded with the corresponding default_value
scalar element along the second dimension.
END
  }
  summary: "Transforms a vector of brain.Example protos (as strings) into typed tensors."
}

op {
  graph_op_name: "DecodeGif"
  in_arg {
    name: "contents"
    description: <<END
0-D.  The GIF-encoded image.
END
  }
  out_arg {
    name: "image"
    description: <<END
4-D with shape `[num_frames, height, width, 3]`. RGB channel order.
END
  }
  summary: "Decode the frame(s) of a GIF-encoded image to a uint8 tensor."
  description: <<END
GIF images with frame or transparency compression are not supported.
On Linux and MacOS systems, convert animated GIFs from compressed to
uncompressed by running:

    convert $src.gif -coalesce $dst.gif

This op also supports decoding JPEGs and PNGs, though it is cleaner to use
`tf.image.decode_image`.
END
}

op {
  graph_op_name: "MutexV2"
  out_arg {
    name: "resource"
    description: <<END
The mutex resource.
END
  }
  attr {
    name: "container"
    description: <<END
If non-empty, this variable is placed in the given container.
Otherwise, a default container is used.
END
  }
  attr {
    name: "shared_name"
    description: <<END
If non-empty, this variable is named in the given bucket
with this shared_name. Otherwise, the node name is used instead.
END
  }
  summary: "Creates a Mutex resource that can be locked by `MutexLock`."
}

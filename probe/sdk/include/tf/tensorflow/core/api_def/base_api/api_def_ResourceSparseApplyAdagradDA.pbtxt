op {
  graph_op_name: "ResourceSparseApplyAdagradDA"
  in_arg {
    name: "var"
    description: <<END
Should be from a Variable().
END
  }
  in_arg {
    name: "gradient_accumulator"
    description: <<END
Should be from a Variable().
END
  }
  in_arg {
    name: "gradient_squared_accumulator"
    description: <<END
Should be from a Variable().
END
  }
  in_arg {
    name: "grad"
    description: <<END
The gradient.
END
  }
  in_arg {
    name: "indices"
    description: <<END
A vector of indices into the first dimension of var and accum.
END
  }
  in_arg {
    name: "lr"
    description: <<END
Learning rate. Must be a scalar.
END
  }
  in_arg {
    name: "l1"
    description: <<END
L1 regularization. Must be a scalar.
END
  }
  in_arg {
    name: "l2"
    description: <<END
L2 regularization. Must be a scalar.
END
  }
  in_arg {
    name: "global_step"
    description: <<END
Training step number. Must be a scalar.
END
  }
  attr {
    name: "use_locking"
    description: <<END
If True, updating of the var and accum tensors will be protected by
a lock; otherwise the behavior is undefined, but may exhibit less contention.
END
  }
  summary: "Update entries in \'*var\' and \'*accum\' according to the proximal adagrad scheme."
}

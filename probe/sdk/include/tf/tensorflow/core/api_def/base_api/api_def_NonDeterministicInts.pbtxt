op {
  graph_op_name: "NonDeterministicInts"
  visibility: HIDDEN
  in_arg {
    name: "shape"
    description: <<END
The shape of the output tensor.
END
  }
  out_arg {
    name: "output"
    description: <<END
Non-deterministic integer values with specified shape.
END
  }
  attr {
    name: "dtype"
    description: <<END
The type of the output.
END
  }
  summary: "Non-deterministically generates some integers."
  description: <<END
This op may use some OS-provided source of non-determinism (e.g. an RNG), so each execution will give different results.
END
}

op {
  graph_op_name: "AddSparseToTensorsMap"
  in_arg {
    name: "sparse_indices"
    description: <<END
2-D.  The `indices` of the `SparseTensor`.
END
  }
  in_arg {
    name: "sparse_values"
    description: <<END
1-D.  The `values` of the `SparseTensor`.
END
  }
  in_arg {
    name: "sparse_shape"
    description: <<END
1-D.  The `shape` of the `SparseTensor`.
END
  }
  out_arg {
    name: "sparse_handle"
    description: <<END
0-D.  The handle of the `SparseTensor` now stored in the
`SparseTensorsMap`.
END
  }
  attr {
    name: "container"
    description: <<END
The container name for the `SparseTensorsMap` created by this op.
END
  }
  attr {
    name: "shared_name"
    description: <<END
The shared name for the `SparseTensorsMap` created by this op.
If blank, the new Operation's unique name is used.
END
  }
  summary: "Add a `SparseTensor` to a `SparseTensorsMap` return its handle."
  description: <<END
A `SparseTensor` is represented by three tensors: `sparse_indices`,
`sparse_values`, and `sparse_shape`.

This operator takes the given `SparseTensor` and adds it to a container
object (a `SparseTensorsMap`).  A unique key within this container is generated
in the form of an `int64`, and this is the value that is returned.

The `SparseTensor` can then be read out as part of a minibatch by passing
the key as a vector element to `TakeManySparseFromTensorsMap`.  To ensure
the correct `SparseTensorsMap` is accessed, ensure that the same
`container` and `shared_name` are passed to that Op.  If no `shared_name`
is provided here, instead use the *name* of the Operation created by calling
`AddSparseToTensorsMap` as the `shared_name` passed to
`TakeManySparseFromTensorsMap`.  Ensure the Operations are colocated.
END
}

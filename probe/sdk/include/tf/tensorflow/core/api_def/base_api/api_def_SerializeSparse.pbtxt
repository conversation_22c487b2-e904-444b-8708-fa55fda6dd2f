op {
  graph_op_name: "SerializeSparse"
  in_arg {
    name: "sparse_indices"
    description: <<END
2-D.  The `indices` of the `SparseTensor`.
END
  }
  in_arg {
    name: "sparse_values"
    description: <<END
1-D.  The `values` of the `SparseTensor`.
END
  }
  in_arg {
    name: "sparse_shape"
    description: <<END
1-D.  The `shape` of the `SparseTensor`.
END
  }
  attr {
    name: "out_type"
    description: <<END
The `dtype` to use for serialization; the supported types are `string`
(default) and `variant`.
END
  }
  summary: "Serialize a `SparseTensor` into a `[3]` `Tensor` object."
}

op {
  graph_op_name: "DecodeRaw"
  in_arg {
    name: "bytes"
    description: <<END
All the elements must have the same length.
END
  }
  out_arg {
    name: "output"
    description: <<END
A Tensor with one more dimension than the input `bytes`.  The
added dimension will have size equal to the length of the elements
of `bytes` divided by the number of bytes to represent `out_type`.
END
  }
  attr {
    name: "little_endian"
    description: <<END
Whether the input `bytes` are in little-endian order.
Ignored for `out_type` values that are stored in a single byte like
`uint8`.
END
  }
  summary: "Reinterpret the bytes of a string as a vector of numbers."
}

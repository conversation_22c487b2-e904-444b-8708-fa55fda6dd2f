op {
  graph_op_name: "RestoreV2"
  in_arg {
    name: "prefix"
    description: <<END
Must have a single element.  The prefix of a V2 checkpoint.
END
  }
  in_arg {
    name: "tensor_names"
    description: <<END
shape {N}.  The names of the tensors to be restored.
END
  }
  in_arg {
    name: "shape_and_slices"
    description: <<END
shape {N}.  The slice specs of the tensors to be restored.
Empty strings indicate that they are non-partitioned tensors.
END
  }
  out_arg {
    name: "tensors"
    description: <<END
shape {N}.  The restored tensors, whose shapes are read from the
checkpoint directly.
END
  }
  attr {
    name: "dtypes"
    description: <<END
shape {N}.  The list of expected dtype for the tensors.  Must match
those stored in the checkpoint.
END
  }
  summary: "Restores tensors from a V2 checkpoint."
  description: <<END
For backward compatibility with the V1 format, this Op currently allows
restoring from a V1 checkpoint as well:
  - This Op first attempts to find the V2 index file pointed to by "prefix", and
    if found proceed to read it as a V2 checkpoint;
  - Otherwise the V1 read path is invoked.
Relying on this behavior is not recommended, as the ability to fall back to read
V1 might be deprecated and eventually removed.

By default, restores the named tensors in full.  If the caller wishes to restore
specific slices of stored tensors, "shape_and_slices" should be non-empty
strings and correspondingly well-formed.

Callers must ensure all the named tensors are indeed stored in the checkpoint.
END
}

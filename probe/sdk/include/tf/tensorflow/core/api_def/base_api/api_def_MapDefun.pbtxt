op {
  graph_op_name: "MapDefun"
  visibility: HIDDEN
  in_arg {
    name: "arguments"
    description: <<END
    A list of tensors whose types are `Targuments`, corresponding to the inputs
    the function should be mapped over.
END
  }
  in_arg {
    name: "captured_inputs"
    description: <<END
    A list of tensors whose types are `Tcaptured`, corresponding to the captured
    inputs of the defun.
END
  }
  out_arg {
    name: "output"
    description: <<END
    A list of output tensors whose types are `output_types` and whose dimensions
    0 are the same as the dimensions 0 of the tensors in `arguments`, and whose
    remaining dimensions correspond to those in `output_shapes`.
END
  }
  attr {
    name: "Targuments"
    description: "A list of types."
  }
  attr {
    name: "Tcaptured"
    description: "A list of types."
  }
  attr {
    name: "output_types"
    description: "A list of types."
  }
  attr {
    name: "output_shapes"
    description: "A list of shapes."
  }
  summary: <<END
  Maps a function on the list of tensors unpacked from arguments on dimension 0.
  The function given by `f` is assumed to be stateless, and is executed
  concurrently on all the slices; up to batch_size (i.e. the size of the 0th
  dimension of each argument) functions will be scheduled at once.

  The `max_intra_op_parallelism` attr, which defaults to 1, can be used to
  limit the intra op parallelism. To limit inter-op parallelism, a user can
  set a private threadpool on the dataset using `tf.data.Options`'s
  `ThreadingOptions`.

  Note that this op is not exposed to users directly, but is invoked in tf.data
  rewrites.
END
}

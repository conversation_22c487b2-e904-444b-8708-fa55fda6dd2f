op {
  graph_op_name: "ExperimentalDirectedInterleaveDataset"
  in_arg {
    name: "selector_input_dataset"
    description: <<END
A dataset of scalar `DT_INT64` elements that determines which of the
`N` data inputs should produce the next output element.
END
  }
  in_arg {
    name: "data_input_datasets"
    description: <<END
`N` datasets with the same type that will be interleaved according to
the values of `selector_input_dataset`.
END
  }
  summary: <<END
A substitute for `InterleaveDataset` on a fixed list of `N` datasets.
END
  visibility: HIDDEN
}

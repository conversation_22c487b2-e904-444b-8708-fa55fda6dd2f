op {
  graph_op_name: "SparseAccumulatorApplyGradient"
  in_arg {
    name: "handle"
    description: <<END
The handle to a accumulator.
END
  }
  in_arg {
    name: "local_step"
    description: <<END
The local_step value at which the sparse gradient was computed.
END
  }
  in_arg {
    name: "gradient_indices"
    description: <<END
Indices of the sparse gradient to be accumulated. Must be a
vector.
END
  }
  in_arg {
    name: "gradient_values"
    description: <<END
Values are the non-zero slices of the gradient, and must have
the same first dimension as indices, i.e., the nnz represented by indices and
values must be consistent.
END
  }
  in_arg {
    name: "gradient_shape"
    description: <<END
Shape of the sparse gradient to be accumulated.
END
  }
  attr {
    name: "dtype"
    description: <<END
The data type of accumulated gradients. Needs to correspond to the type
of the accumulator.
END
  }
  attr {
    name: "has_known_shape"
    description: <<<PERSON><PERSON>
Boolean indicating whether gradient_shape is unknown, in which
case the input is ignored during validation.
END
  }
  summary: "Applies a sparse gradient to a given accumulator."
  description: <<END
Does not add if local_step is smaller than the accumulator's
global_step.
END
}

op {
  graph_op_name: "LookupTableRemoveV2"
  visibility: HIDDEN
  endpoint {
    name: "LookupTableRemove"
  }
  in_arg {
    name: "table_handle"
    description: <<END
Handle to the table.
END
  }
  in_arg {
    name: "keys"
    description: <<END
Any shape.  Keys of the elements to remove.
END
  }
  summary: "Removes keys and its associated values from a table."
  description: <<END
The tensor `keys` must of the same type as the keys of the table. Keys not
already in the table are silently ignored.
END
}

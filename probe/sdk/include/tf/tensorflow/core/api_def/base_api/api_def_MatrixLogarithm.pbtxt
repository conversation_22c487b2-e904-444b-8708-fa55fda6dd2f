op {
  graph_op_name: "MatrixLogarithm"
  visibility: HIDDE<PERSON>
  in_arg {
    name: "input"
    description: <<END
Shape is `[..., M, <PERSON>]`.
END
  }
  out_arg {
    name: "output"
    description: <<END
Shape is `[..., <PERSON>, <PERSON>]`.

@compatibility(scipy)
Equivalent to scipy.linalg.logm
@end_compatibility
END
  }
  summary: "Computes the matrix logarithm of one or more square matrices:"
  description: <<END

\\(log(exp(A)) = A\\)

This op is only defined for complex matrices. If A is positive-definite and
real, then casting to a complex matrix, taking the logarithm and casting back
to a real matrix will give the correct result.

This function computes the matrix logarithm using the <PERSON><PERSON><PERSON> algorithm.
Details of the algorithm can be found in Section 11.6.2 of:
<PERSON>, Functions of Matrices: Theory and Computation, SIAM 2008.
ISBN 978-0-898716-46-7.

The input is a tensor of shape `[..., M, M]` whose inner-most 2 dimensions
form square matrices. The output is a tensor of the same shape as the input
containing the exponential for all input submatrices `[..., :, :]`.
END
}

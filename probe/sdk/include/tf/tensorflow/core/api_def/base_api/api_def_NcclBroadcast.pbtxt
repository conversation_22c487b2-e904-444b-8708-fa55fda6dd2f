op {
  graph_op_name: "NcclBroadcast"
  summary: "Sends `input` to all devices that are connected to the output."
  description: <<END
Sends `input` to all devices that are connected to the output.

The graph should be constructed so that all ops connected to the output have a
valid device assignment, and the op itself is assigned one of these devices.

input: The input to the broadcast.
output: The same as input.
shape: The shape of the input tensor.

END

  visibility: HIDDEN
}

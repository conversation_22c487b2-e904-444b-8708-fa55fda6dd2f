op {
  graph_op_name: "FusedBatchNormGradV2"
  in_arg {
    name: "y_backprop"
    description: <<END
A 4D Tensor for the gradient with respect to y.
END
  }
  in_arg {
    name: "x"
    description: <<END
A 4D Tensor for input data.
END
  }
  in_arg {
    name: "scale"
    description: <<END
A 1D Tensor for scaling factor, to scale the normalized x.
END
  }
  in_arg {
    name: "reserve_space_1"
    description: <<END
When is_training is True, a 1D Tensor for the computed batch
mean to be reused in gradient computation. When is_training is
False, a 1D Tensor for the population mean to be reused in both
1st and 2nd order gradient computation.
END
  }
  in_arg {
    name: "reserve_space_2"
    description: <<END
When is_training is True, a 1D Tensor for the computed batch
variance (inverted variance in the cuDNN case) to be reused in
gradient computation. When is_training is False, a 1D Tensor
for the population variance to be reused in both 1st and 2nd
order gradient computation.
END
  }
  out_arg {
    name: "x_backprop"
    description: <<END
A 4D Tensor for the gradient with respect to x.
END
  }
  out_arg {
    name: "scale_backprop"
    description: <<END
A 1D Tensor for the gradient with respect to scale.
END
  }
  out_arg {
    name: "offset_backprop"
    description: <<END
A 1D Tensor for the gradient with respect to offset.
END
  }
  out_arg {
    name: "reserve_space_3"
    description: <<END
Unused placeholder to match the mean input in FusedBatchNorm.
END
  }
  out_arg {
    name: "reserve_space_4"
    description: <<END
Unused placeholder to match the variance input
in FusedBatchNorm.
END
  }
  attr {
    name: "T"
    description: <<END
The data type for the elements of input and output Tensors.
END
  }
  attr {
    name: "U"
    description: <<END
The data type for the scale, offset, mean, and variance.
END
  }
  attr {
    name: "epsilon"
    description: <<END
A small float number added to the variance of x.
END
  }
  attr {
    name: "data_format"
    description: <<END
The data format for y_backprop, x, x_backprop.
Either "NHWC" (default) or "NCHW".
END
  }
  attr {
    name: "is_training"
    description: <<END
A bool value to indicate the operation is for training (default)
or inference.
END
  }
  summary: "Gradient for batch normalization."
  description: <<END
Note that the size of 4D Tensors are defined by either "NHWC" or "NCHW".
The size of 1D Tensors matches the dimension C of the 4D Tensors.
END
}

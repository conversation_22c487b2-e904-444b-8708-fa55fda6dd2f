op {
  graph_op_name: "StaticRegexFullMatch"
  in_arg {
    name: "input"
    description: <<END
A string tensor of the text to be processed.
END
  }
  out_arg {
    name: "output"
    description: <<END
A bool tensor with the same shape as `input`.
END
  }
  attr {
    name: "pattern"
    description: "The regular expression to match the input."
  }
  summary: "Check if the input matches the regex pattern."
  description: <<END
The input is a string tensor of any shape. The pattern is the
regular expression to be matched with every element of the input tensor.
The boolean values (True or False) of the output tensor indicate
if the input matches the regex pattern provided.

The pattern follows the re2 syntax (https://github.com/google/re2/wiki/Syntax)
END
  visibility: HIDDEN
}

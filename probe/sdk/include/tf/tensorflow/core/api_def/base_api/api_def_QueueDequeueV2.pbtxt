op {
  graph_op_name: "QueueDequeueV2"
  endpoint {
    name: "QueueDequeue"
  }
  in_arg {
    name: "handle"
    description: <<END
The handle to a queue.
END
  }
  out_arg {
    name: "components"
    description: <<END
One or more tensors that were dequeued as a tuple.
END
  }
  attr {
    name: "component_types"
    description: <<END
The type of each component in a tuple.
END
  }
  attr {
    name: "timeout_ms"
    description: <<END
If the queue is empty, this operation will block for up to
timeout_ms milliseconds.
Note: This option is not supported yet.
END
  }
  summary: "Dequeues a tuple of one or more tensors from the given queue."
  description: <<END
This operation has k outputs, where k is the number of components
in the tuples stored in the given queue, and output i is the ith
component of the dequeued tuple.

N.B. If the queue is empty, this operation will block until an element
has been dequeued (or 'timeout_ms' elapses, if specified).
END
}

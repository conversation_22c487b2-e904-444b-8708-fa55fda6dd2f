op {
  graph_op_name: "ResizeBicubic"
  in_arg {
    name: "images"
    description: <<END
4-D with shape `[batch, height, width, channels]`.
END
  }
  in_arg {
    name: "size"
    description: <<END
= A 1-D int32 Tensor of 2 elements: `new_height, new_width`.  The
new size for the images.
END
  }
  out_arg {
    name: "resized_images"
    description: <<END
4-D with shape
`[batch, new_height, new_width, channels]`.
END
  }
  attr {
    name: "align_corners"
    description: <<END
If true, the centers of the 4 corner pixels of the input and output tensors are
aligned, preserving the values at the corner pixels. Defaults to false.
END
  }
  summary: "Resize `images` to `size` using bicubic interpolation."
  description: <<END
Input images can be of different types but output images are always float.
END
}

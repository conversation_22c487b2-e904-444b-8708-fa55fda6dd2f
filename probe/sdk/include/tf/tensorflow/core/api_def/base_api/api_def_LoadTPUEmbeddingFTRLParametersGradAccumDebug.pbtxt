op {
  graph_op_name: "LoadTPUEmbeddingFTRLParametersGradAccumDebug"
  visibility: HIDDEN
  in_arg {
    name: "parameters"
    description: <<END
Value of parameters used in the FTRL optimization algorithm.
END
  }
  in_arg {
    name: "accumulators"
    description: <<END
Value of accumulators used in the FTRL optimization algorithm.
END
  }
  in_arg {
    name: "linears"
    description: <<END
Value of linears used in the FTRL optimization algorithm.
END
  }
  in_arg {
    name: "gradient_accumulators"
    description: <<END
Value of gradient_accumulators used in the FTRL optimization algorithm.
END
  }
  summary: "Load FTRL embedding parameters with debug support."
  description: <<END
An op that loads optimization parameters into HBM for embedding. Must be
preceded by a ConfigureTPUEmbeddingHost op that sets up the correct
embedding table configuration. For example, this op is used to install
parameters that are loaded from a checkpoint before a training loop is
executed.
END
}

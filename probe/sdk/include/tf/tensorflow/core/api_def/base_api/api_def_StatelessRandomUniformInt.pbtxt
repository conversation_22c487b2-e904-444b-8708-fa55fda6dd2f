op {
  graph_op_name: "StatelessRandomUniformInt"
  visibility: HIDDE<PERSON>
  in_arg {
    name: "shape"
    description: <<END
The shape of the output tensor.
END
  }
  in_arg {
    name: "seed"
    description: <<END
2 seeds (shape [2]).
END
  }
  in_arg {
    name: "minval"
    description: <<END
Minimum value (inclusive, scalar).
END
  }
  in_arg {
    name: "maxval"
    description: <<END
Maximum value (exclusive, scalar).
END
  }
  out_arg {
    name: "output"
    description: <<END
Random values with specified shape.
END
  }
  attr {
    name: "dtype"
    description: <<END
The type of the output.
END
  }
  summary: "Outputs deterministic pseudorandom random integers from a uniform distribution."
  description: <<END
The generated values follow a uniform distribution in the range `[minval, maxval)`.

The outputs are a deterministic function of `shape`, `seed`, `minval`, and `maxval`.
END
}

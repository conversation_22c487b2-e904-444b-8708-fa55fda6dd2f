op {
  graph_op_name: "QuantizedMatMul"
  in_arg {
    name: "a"
    description: <<END
Must be a two-dimensional tensor.
END
  }
  in_arg {
    name: "b"
    description: <<END
Must be a two-dimensional tensor.
END
  }
  in_arg {
    name: "min_a"
    description: <<END
The float value that the lowest quantized `a` value represents.
END
  }
  in_arg {
    name: "max_a"
    description: <<END
The float value that the highest quantized `a` value represents.
END
  }
  in_arg {
    name: "min_b"
    description: <<END
The float value that the lowest quantized `b` value represents.
END
  }
  in_arg {
    name: "max_b"
    description: <<END
The float value that the highest quantized `b` value represents.
END
  }
  out_arg {
    name: "min_out"
    description: <<END
The float value that the lowest quantized output value represents.
END
  }
  out_arg {
    name: "max_out"
    description: <<END
The float value that the highest quantized output value represents.
END
  }
  attr {
    name: "transpose_a"
    description: <<END
If true, `a` is transposed before multiplication.
END
  }
  attr {
    name: "transpose_b"
    description: <<END
If true, `b` is transposed before multiplication.
E<PERSON>
  }
  attr {
    name: "Tactivation"
    description: <<END
The type of output produced by activation function
following this operation.
END
  }
  summary: "Perform a quantized matrix multiplication of  `a` by the matrix `b`."
  description: <<END
The inputs must be two-dimensional matrices and the inner dimension of
`a` (after being transposed if `transpose_a` is non-zero) must match the
outer dimension of `b` (after being transposed if `transposed_b` is
non-zero).
END
}

op {
  graph_op_name: "BoostedTreesExampleDebugOutputs"
  visibility: HIDDEN
  in_arg {
    name: "bucketized_features"
    description: <<END
A list of rank 1 Tensors containing bucket id for each
feature.
END
  }
  out_arg {
    name: "examples_debug_outputs_serialized"
    description: <<END
Output rank 1 Tensor containing a proto serialized as a string for each example.
END
  }
  attr {
    name: "num_bucketized_features"
    description: <<END
Inferred.
END
  }
  attr {
    name: "logits_dimension"
    description: <<END
scalar, dimension of the logits, to be used for constructing the protos in
examples_debug_outputs_serialized.
END
  }
  summary: "Debugging/model interpretability outputs for each example."
  description: <<END
It traverses all the trees and computes debug metrics for individual examples, 
such as getting split feature ids and logits after each split along the decision
path used to compute directional feature contributions.
END
}

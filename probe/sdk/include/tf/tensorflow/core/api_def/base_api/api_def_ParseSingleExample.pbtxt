op {
  graph_op_name: "ParseSingleExample"
  in_arg {
    name: "serialized"
    description: <<END
A vector containing a batch of binary serialized Example protos.
END
  }
  in_arg {
    name: "dense_defaults"
    description: <<END
A list of Tensors (some may be empty), whose length matches
the length of `dense_keys`. dense_defaults[j] provides default values
when the example's feature_map lacks dense_key[j].  If an empty Tensor is
provided for dense_defaults[j], then the Feature dense_keys[j] is required.
The input type is inferred from dense_defaults[j], even when it's empty.
If dense_defaults[j] is not empty, and dense_shapes[j] is fully defined,
then the shape of dense_defaults[j] must match that of dense_shapes[j].
If dense_shapes[j] has an undefined major dimension (variable strides dense
feature), dense_defaults[j] must contain a single element:
the padding element.
END
  }
  attr {
    name: "num_sparse"
    description: <<END
The number of sparse features to be parsed from the example. This
must match the lengths of `sparse_keys` and `sparse_types`.
END
  }
  attr {
    name: "sparse_keys"
    description: <<END
A list of `num_sparse` strings.
The keys expected in the Examples' features associated with sparse values.
END
  }
  attr {
    name: "dense_keys"
    description: <<END
The keys expected in the Examples' features associated with dense
values.
END
  }
  attr {
    name: "sparse_types"
    description: <<END
A list of `num_sparse` types; the data types of data in each
Feature given in sparse_keys.
Currently the ParseSingleExample op supports DT_FLOAT (FloatList),
DT_INT64 (Int64List), and DT_STRING (BytesList).
END
  }
  attr {
    name: "Tdense"
    description: <<END
The data types of data in each Feature given in dense_keys.
The length of this list must match the length of `dense_keys`.
Currently the ParseSingleExample op supports DT_FLOAT (FloatList),
DT_INT64 (Int64List), and DT_STRING (BytesList).
END
  }
  attr {
    name: "dense_shapes"
    description: <<END
The shapes of data in each Feature given in dense_keys.
The length of this list must match the length of `dense_keys`.  The
number of elements in the Feature corresponding to dense_key[j] must
always equal dense_shapes[j].NumEntries().  If dense_shapes[j] ==
(D0, D1, ..., DN) then the shape of output Tensor dense_values[j]
will be (D0, D1, ..., DN): In the case dense_shapes[j] = (-1, D1,
..., DN), the shape of the output Tensor dense_values[j] will be (M,
D1, .., DN), where M is the number of blocks of elements of length
D1 * .... * DN, in the input.
END
  }
  summary: "Transforms a tf.Example proto (as a string) into typed tensors."
}

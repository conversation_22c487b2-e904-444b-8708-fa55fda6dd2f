op {
  graph_op_name: "BatchNormWithGlobalNormalizationGrad"
  in_arg {
    name: "t"
    description: <<END
A 4D input Tensor.
END
  }
  in_arg {
    name: "m"
    description: <<END
A 1D mean Tensor with size matching the last dimension of t.
This is the first output from tf.nn.moments,
or a saved moving average thereof.
END
  }
  in_arg {
    name: "v"
    description: <<END
A 1D variance Tensor with size matching the last dimension of t.
This is the second output from tf.nn.moments,
or a saved moving average thereof.
END
  }
  in_arg {
    name: "gamma"
    description: <<END
A 1D gamma Tensor with size matching the last dimension of t.
If "scale_after_normalization" is true, this Tensor will be multiplied
with the normalized Tensor.
END
  }
  in_arg {
    name: "backprop"
    description: <<END
4D backprop Tensor.
END
  }
  out_arg {
    name: "dx"
    description: <<END
4D backprop tensor for input.
END
  }
  out_arg {
    name: "dm"
    description: <<END
1D backprop tensor for mean.
END
  }
  out_arg {
    name: "dv"
    description: <<END
1D backprop tensor for variance.
END
  }
  out_arg {
    name: "db"
    description: <<END
1D backprop tensor for beta.
END
  }
  out_arg {
    name: "dg"
    description: <<END
1D backprop tensor for gamma.
END
  }
  attr {
    name: "variance_epsilon"
    description: <<END
A small float number to avoid dividing by 0.
END
  }
  attr {
    name: "scale_after_normalization"
    description: <<END
A bool indicating whether the resulted tensor
needs to be multiplied with gamma.
END
  }
  summary: "Gradients for batch normalization."
  description: <<END
This op is deprecated. See `tf.nn.batch_normalization`.
END
}

op {
  graph_op_name: "QueueDequeueUpTo"
  visibility: SKIP
  in_arg {
    name: "handle"
    description: <<END
The handle to a queue.
END
  }
  in_arg {
    name: "n"
    description: <<END
The number of tuples to dequeue.
END
  }
  out_arg {
    name: "components"
    description: <<END
One or more tensors that were dequeued as a tuple.
END
  }
  attr {
    name: "component_types"
    description: <<END
The type of each component in a tuple.
END
  }
  attr {
    name: "timeout_ms"
    description: <<END
If the queue has fewer than n elements, this operation
will block for up to timeout_ms milliseconds.
Note: This option is not supported yet.
END
  }
  summary: "Dequeues `n` tuples of one or more tensors from the given queue."
  description: <<END
This operation is not supported by all queues.  If a queue does not support
DequeueUpTo, then an Unimplemented error is returned.

If the queue is closed and there are more than 0 but less than `n`
elements remaining, then instead of returning an OutOfRange error like
QueueDequeueMany, less than `n` elements are returned immediately.  If
the queue is closed and there are 0 elements left in the queue, then
an OutOfRange error is returned just like in QueueDequeueMany.
Otherwise the behavior is identical to QueueDequeueMany:

This operation concatenates queue-element component tensors along the
0th dimension to make a single component tensor.  All of the components
in the dequeued tuple will have size `n` in the 0th dimension.

This operation has k outputs, where `k` is the number of components in
the tuples stored in the given queue, and output `i` is the ith
component of the dequeued tuple.
END
}

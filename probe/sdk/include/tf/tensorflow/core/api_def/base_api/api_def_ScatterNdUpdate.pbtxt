op {
  graph_op_name: "ScatterNdUpdate"
  in_arg {
    name: "ref"
    description: <<END
A mutable Tensor. Should be from a Variable node.
END
  }
  in_arg {
    name: "indices"
    description: <<END
A Tensor. Must be one of the following types: int32, int64.
A tensor of indices into ref.
END
  }
  in_arg {
    name: "updates"
    description: <<END
A Tensor. Must have the same type as ref. A tensor of updated
values to add to ref.
END
  }
  out_arg {
    name: "output_ref"
    description: <<END
Same as ref. Returned as a convenience for operations that want to
use the updated values after the update is done.
END
  }
  attr {
    name: "use_locking"
    description: <<END
An optional bool. Defaults to True. If True, the assignment will
be protected by a lock; otherwise the behavior is undefined,
but may exhibit less contention.
END
  }
  summary: "Applies sparse `updates` to individual values or slices within a given"
  description: <<END
variable according to `indices`.

`ref` is a `Tensor` with rank `P` and `indices` is a `Tensor` of rank `Q`.

`indices` must be integer tensor, containing indices into `ref`.
It must be shape \\([d_0, ..., d_{Q-2}, K]\\) where `0 < K <= P`.

The innermost dimension of `indices` (with length `K`) corresponds to
indices into elements (if `K = P`) or slices (if `K < P`) along the `K`th
dimension of `ref`.

`updates` is `Tensor` of rank `Q-1+P-K` with shape:

$$[d_0, ..., d_{Q-2}, ref.shape[K], ..., ref.shape[P-1]].$$

For example, say we want to update 4 scattered elements to a rank-1 tensor to
8 elements. In Python, that update would look like this:

```python
    ref = tf.Variable([1, 2, 3, 4, 5, 6, 7, 8])
    indices = tf.constant([[4], [3], [1] ,[7]])
    updates = tf.constant([9, 10, 11, 12])
    update = tf.scatter_nd_update(ref, indices, updates)
    with tf.Session() as sess:
      print sess.run(update)
```

The resulting update to ref would look like this:

    [1, 11, 3, 10, 9, 6, 7, 12]

See `tf.scatter_nd` for more details about how to make updates to
slices.

See also `tf.scatter_update` and `tf.batch_scatter_update`.
END
}

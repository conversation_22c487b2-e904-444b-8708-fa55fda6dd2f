op {
  graph_op_name: "MultiDeviceIterator"
  out_arg {
    name: "handle"
    description: <<END
Handle to the resource created.
END
  }
  attr {
    name: "devices"
    description: <<END
A list of devices the iterator works across.
END
  }
  attr {
    name: "shared_name"
    description: <<END
If non-empty, this resource will be shared under the given name
across multiple sessions.
END
  }
  attr {
    name: "container"
    description: <<END
If non-empty, this resource is placed in the given container.
Otherwise, a default container is used.
END
  }
  attr {
    name: "output_types"
    description: <<END
The type list for the return values.
END
  }
  attr {
    name: "output_shapes"
    description: <<END
The list of shapes being produced.
END
  }
  summary: "Creates a MultiDeviceIterator resource."
  visibility: HIDDEN
}

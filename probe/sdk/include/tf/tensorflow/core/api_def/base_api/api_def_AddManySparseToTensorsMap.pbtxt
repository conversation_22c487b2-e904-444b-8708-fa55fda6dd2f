op {
  graph_op_name: "AddManySparseToTensorsMap"
  in_arg {
    name: "sparse_indices"
    description: <<END
2-D.  The `indices` of the minibatch `SparseTensor`.
`sparse_indices[:, 0]` must be ordered values in `[0, N)`.
END
  }
  in_arg {
    name: "sparse_values"
    description: <<END
1-D.  The `values` of the minibatch `SparseTensor`.
END
  }
  in_arg {
    name: "sparse_shape"
    description: <<END
1-D.  The `shape` of the minibatch `SparseTensor`.
The minibatch size `N == sparse_shape[0]`.
END
  }
  out_arg {
    name: "sparse_handles"
    description: <<END
1-D.  The handles of the `SparseTensor` now stored in the
`SparseTensorsMap`.  Shape: `[N]`.
END
  }
  attr {
    name: "container"
    description: <<END
The container name for the `SparseTensorsMap` created by this op.
END
  }
  attr {
    name: "shared_name"
    description: <<END
The shared name for the `SparseTensorsMap` created by this op.
If blank, the new Operation's unique name is used.
END
  }
  summary: "Add an `N`-minibatch `SparseTensor` to a `SparseTensorsMap`, return `N` handles."
  description: <<END
A `SparseTensor` of rank `R` is represented by three tensors: `sparse_indices`,
`sparse_values`, and `sparse_shape`, where

```sparse_indices.shape[1] == sparse_shape.shape[0] == R```

An `N`-minibatch of `SparseTensor` objects is represented as a `SparseTensor`
having a first `sparse_indices` column taking values between `[0, N)`, where
the minibatch size `N == sparse_shape[0]`.

The input `SparseTensor` must have rank `R` greater than 1, and the first
dimension is treated as the minibatch dimension.  Elements of the `SparseTensor`
must be sorted in increasing order of this first dimension.  The stored
`SparseTensor` objects pointed to by each row of the output `sparse_handles`
will have rank `R-1`.

The `SparseTensor` values can then be read out as part of a minibatch by passing
the given keys as vector elements to `TakeManySparseFromTensorsMap`.  To ensure
the correct `SparseTensorsMap` is accessed, ensure that the same
`container` and `shared_name` are passed to that Op.  If no `shared_name`
is provided here, instead use the *name* of the Operation created by calling
`AddManySparseToTensorsMap` as the `shared_name` passed to
`TakeManySparseFromTensorsMap`.  Ensure the Operations are colocated.
END
}

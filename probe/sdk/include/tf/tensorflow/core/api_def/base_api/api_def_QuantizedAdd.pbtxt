op {
  graph_op_name: "QuantizedAdd"
  in_arg {
    name: "min_x"
    description: <<END
The float value that the lowest quantized `x` value represents.
END
  }
  in_arg {
    name: "max_x"
    description: <<END
The float value that the highest quantized `x` value represents.
END
  }
  in_arg {
    name: "min_y"
    description: <<END
The float value that the lowest quantized `y` value represents.
END
  }
  in_arg {
    name: "max_y"
    description: <<END
The float value that the highest quantized `y` value represents.
END
  }
  out_arg {
    name: "min_z"
    description: <<END
The float value that the lowest quantized output value represents.
END
  }
  out_arg {
    name: "max_z"
    description: <<END
The float value that the highest quantized output value represents.

*NOTE*: `QuantizedAdd` supports limited forms of broadcasting. More about
broadcasting [here](http://docs.scipy.org/doc/numpy/user/basics.broadcasting.html)
END
  }
  summary: "Returns x + y element-wise, working on quantized buffers."
}

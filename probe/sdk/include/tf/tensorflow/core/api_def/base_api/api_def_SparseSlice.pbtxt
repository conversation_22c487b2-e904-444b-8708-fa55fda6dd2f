op {
  graph_op_name: "SparseSlice"
  in_arg {
    name: "indices"
    description: <<END
2-D tensor represents the indices of the sparse tensor.
END
  }
  in_arg {
    name: "values"
    description: <<END
1-D tensor represents the values of the sparse tensor.
END
  }
  in_arg {
    name: "shape"
    description: <<END
1-D. tensor represents the shape of the sparse tensor.
END
  }
  in_arg {
    name: "start"
    description: <<END
1-D. tensor represents the start of the slice.
END
  }
  in_arg {
    name: "size"
    description: <<END
1-D. tensor represents the size of the slice.
output indices: A list of 1-D tensors represents the indices of the output
sparse tensors.
END
  }
  out_arg {
    name: "output_values"
    description: <<END
A list of 1-D tensors represents the values of the output sparse
tensors.
END
  }
  out_arg {
    name: "output_shape"
    description: <<END
A list of 1-D tensors represents the shape of the output sparse
tensors.
END
  }
  summary: "Slice a `SparseTensor` based on the `start` and `size`."
  description: <<END
For example, if the input is

    input_tensor = shape = [2, 7]
    [    a   d e  ]
    [b c          ]

Graphically the output tensors are:

    sparse_slice([0, 0], [2, 4]) = shape = [2, 4]
    [    a  ]
    [b c    ]

    sparse_slice([0, 4], [2, 3]) = shape = [2, 3]
    [ d e  ]
    [      ]
END
}

op {
  graph_op_name: "TPUEmbeddingActivations"
  visibility: HIDDE<PERSON>
  in_arg {
    name: "embedding_variable"
    description: <<END
A trainable variable, enabling optimizers to find this op.
END
  }
  in_arg {
    name: "sliced_activations"
    description: <<END
The embedding activations Tensor to return.
END
  }
  attr {
    name: "table_id"
    description: <<END
The id of the table in the embedding layer configuration from which
these activations were computed.
END
  }
  attr {
    name: "lookup_id"
    description: <<END
Identifier of the set of embedding indices which produced these
activations.
END
  }
  summary: "An op enabling differentiation of TPU Embeddings."
  description: <<END
This op simply returns its first input, which is assumed to have been sliced
from the Tensors returned by TPUEmbeddingDequeueActivations. The presence of
this op, and its first argument being a trainable Variable, enables automatic
differentiation of graphs containing embeddings via the TPU Embedding Python
libraries.
END
}

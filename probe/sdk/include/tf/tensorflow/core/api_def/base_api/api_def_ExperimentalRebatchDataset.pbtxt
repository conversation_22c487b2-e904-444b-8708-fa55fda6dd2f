op {
  graph_op_name: "ExperimentalRebatchDataset"
  visibility: HIDDEN
  in_arg {
    name: "input_dataset"
    description: <<END
A variant tensor representing the input dataset.
END
  }
  in_arg {
  name: "num_workers"
  description: <<END
A scalar representing the number of workers to distribute this batch across. As
a result of this transformation the current batch size would end up being
divided  by this parameter.
END
  }
  summary: "Creates a dataset that changes the batch size."
  description: <<END
Creates a dataset that changes the batch size of the dataset to current batch
size // num_workers.
END
}

op {
  graph_op_name: "FloorMod"
  summary: "Returns element-wise remainder of division. When `x < 0` xor `y < 0` is"
  description: <<END
true, this follows Python semantics in that the result here is consistent
with a flooring divide. E.g. `floor(x / y) * y + mod(x, y) = x`.

*NOTE*: `FloorMod` supports broadcasting. More about broadcasting
[here](http://docs.scipy.org/doc/numpy/user/basics.broadcasting.html)
END
}

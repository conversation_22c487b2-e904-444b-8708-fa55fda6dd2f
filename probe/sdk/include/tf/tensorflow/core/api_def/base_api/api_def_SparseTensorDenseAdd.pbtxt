op {
  graph_op_name: "SparseTensorDenseAdd"
  in_arg {
    name: "a_indices"
    description: <<END
2-D.  The `indices` of the `SparseTensor`, with shape `[nnz, ndims]`.
END
  }
  in_arg {
    name: "a_values"
    description: <<END
1-D.  The `values` of the `SparseTensor`, with shape `[nnz]`.
END
  }
  in_arg {
    name: "a_shape"
    description: <<END
1-D.  The `shape` of the `SparseTensor`, with shape `[ndims]`.
END
  }
  in_arg {
    name: "b"
    description: <<END
`ndims`-D Tensor.  With shape `a_shape`.
END
  }
  summary: "Adds up a `SparseTensor` and a dense `Tensor`, producing a dense `Tensor`."
  description: <<END
This Op does not require `a_indices` be sorted in standard lexicographic order.
END
}

op {
  graph_op_name: "LRN"
  in_arg {
    name: "input"
    description: <<END
4-D.
END
  }
  attr {
    name: "depth_radius"
    description: <<END
0-D.  Half-width of the 1-D normalization window.
END
  }
  attr {
    name: "bias"
    description: <<END
An offset (usually positive to avoid dividing by 0).
E<PERSON>
  }
  attr {
    name: "alpha"
    description: <<END
A scale factor, usually positive.
END
  }
  attr {
    name: "beta"
    description: <<END
An exponent.
END
  }
  summary: "Local Response Normalization."
  description: <<END
The 4-D `input` tensor is treated as a 3-D array of 1-D vectors (along the last
dimension), and each vector is normalized independently.  Within a given vector,
each component is divided by the weighted, squared sum of inputs within
`depth_radius`.  In detail,

    sqr_sum[a, b, c, d] =
        sum(input[a, b, c, d - depth_radius : d + depth_radius + 1] ** 2)
    output = input / (bias + alpha * sqr_sum) ** beta

For details, see [<PERSON><PERSON><PERSON><PERSON> et al., ImageNet classification with deep
convolutional neural networks (NIPS 2012)](http://papers.nips.cc/paper/4824-imagenet-classification-with-deep-convolutional-neural-networks).
END
}

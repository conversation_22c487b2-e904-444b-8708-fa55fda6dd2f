op {
  graph_op_name: "ResourceScatterUpdate"
  in_arg {
    name: "resource"
    description: <<END
Should be from a `Variable` node.
END
  }
  in_arg {
    name: "indices"
    description: <<END
A tensor of indices into the first dimension of `ref`.
END
  }
  in_arg {
    name: "updates"
    description: <<END
A tensor of updated values to add to `ref`.
END
  }
  summary: "Assigns sparse updates to the variable referenced by `resource`."
  description: <<END
This operation computes

    # Scalar indices
    ref[indices, ...] = updates[...]

    # Vector indices (for each i)
    ref[indices[i], ...] = updates[i, ...]

    # High rank indices (for each i, ..., j)
    ref[indices[i, ..., j], ...] = updates[i, ..., j, ...]
END
}

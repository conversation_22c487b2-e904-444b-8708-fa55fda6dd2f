op {
  graph_op_name: "GatherV2"
  in_arg {
    name: "params"
    description: <<END
The tensor from which to gather values. Must be at least rank
`axis + 1`.
END
  }
  in_arg {
    name: "indices"
    description: <<END
Index tensor. Must be in range `[0, params.shape[axis])`.
END
  }
  in_arg {
    name: "axis"
    description: <<END
The axis in `params` to gather `indices` from. Defaults to the first
dimension. Supports negative indexes.
END
  }
  out_arg {
    name: "output"
    description: <<END
Values from `params` gathered from indices given by `indices`, with
shape `params.shape[:axis] + indices.shape + params.shape[axis + 1:]`.
END
  }
  summary: "Gather slices from `params` axis `axis` according to `indices`."
  description: <<END
`indices` must be an integer tensor of any dimension (usually 0-D or 1-D).
Produces an output tensor with shape `params.shape[:axis] + indices.shape +
params.shape[axis + 1:]` where:

```python
    # Scalar indices (output is rank(params) - 1).
    output[a_0, ..., a_n, b_0, ..., b_n] =
      params[a_0, ..., a_n, indices, b_0, ..., b_n]

    # Vector indices (output is rank(params)).
    output[a_0, ..., a_n, i, b_0, ..., b_n] =
      params[a_0, ..., a_n, indices[i], b_0, ..., b_n]

    # Higher rank indices (output is rank(params) + rank(indices) - 1).
    output[a_0, ..., a_n, i, ..., j, b_0, ... b_n] =
      params[a_0, ..., a_n, indices[i, ..., j], b_0, ..., b_n]
```

<div style="width:70%; margin:auto; margin-bottom:10px; margin-top:20px;">
<img style="width:100%" src="https://www.tensorflow.org/images/Gather.png" alt>
</div>

Note that on CPU, if an out of bound index is found, an error is returned.
On GPU, if an out of bound index is found, a 0 is stored in the
corresponding output value.

See also `tf.batch_gather` and `tf.gather_nd`.
END
}

op {
  graph_op_name: "RetrieveTPUEmbeddingAdadeltaParameters"
  visibility: HIDDEN
  out_arg {
    name: "parameters"
    description: <<END
Parameter parameters updated by the Adadelta optimization algorithm.
END
  }
  out_arg {
    name: "accumulators"
    description: <<END
Parameter accumulators updated by the Adadelta optimization algorithm.
END
  }
  out_arg {
    name: "updates"
    description: <<END
Parameter updates updated by the Adadelta optimization algorithm.
END
  }
  summary: "Retrieve Adadelta embedding parameters."
  description: <<END
An op that retrieves optimization parameters from embedding to host
memory. Must be preceded by a ConfigureTPUEmbeddingHost op that sets up
the correct embedding table configuration. For example, this op is
used to retrieve updated parameters before saving a checkpoint.
END
}

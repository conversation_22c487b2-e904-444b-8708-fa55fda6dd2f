op {
  graph_op_name: "<PERSON>uce<PERSON>oin"
  in_arg {
    name: "inputs"
    description: <<END
The input to be joined.  All reduced indices must have non-zero size.
END
  }
  in_arg {
    name: "reduction_indices"
    description: <<END
The dimensions to reduce over.  Dimensions are reduced in the
order specified.  Omitting `reduction_indices` is equivalent to passing
`[n-1, n-2, ..., 0]`.  Negative indices from `-n` to `-1` are supported.
END
  }
  out_arg {
    name: "output"
    description: <<END
Has shape equal to that of the input with reduced dimensions removed or
set to `1` depending on `keep_dims`.
END
  }
  attr {
    name: "keep_dims"
    description: <<END
If `True`, retain reduced dimensions with length `1`.
END
  }
  attr {
    name: "separator"
    description: <<END
The separator to use when joining.
END
  }
  summary: "Joins a string Tensor across the given dimensions."
  description: <<END
Computes the string join across dimensions in the given string Tensor of shape
`[\\(d_0, d_1, ..., d_{n-1}\\)]`.  Returns a new Tensor created by joining the input
strings with the given separator (default: empty string).  Negative indices are
counted backwards from the end, with `-1` being equivalent to `n - 1`.  If
indices are not specified, joins across all dimensions beginning from `n - 1`
through `0`.

For example:

```python
# tensor `a` is [["a", "b"], ["c", "d"]]
tf.reduce_join(a, 0) ==> ["ac", "bd"]
tf.reduce_join(a, 1) ==> ["ab", "cd"]
tf.reduce_join(a, -2) = tf.reduce_join(a, 0) ==> ["ac", "bd"]
tf.reduce_join(a, -1) = tf.reduce_join(a, 1) ==> ["ab", "cd"]
tf.reduce_join(a, 0, keep_dims=True) ==> [["ac", "bd"]]
tf.reduce_join(a, 1, keep_dims=True) ==> [["ab"], ["cd"]]
tf.reduce_join(a, 0, separator=".") ==> ["a.c", "b.d"]
tf.reduce_join(a, [0, 1]) ==> "acbd"
tf.reduce_join(a, [1, 0]) ==> "abcd"
tf.reduce_join(a, []) ==> [["a", "b"], ["c", "d"]]
tf.reduce_join(a) = tf.reduce_join(a, [1, 0]) ==> "abcd"
```
END
}

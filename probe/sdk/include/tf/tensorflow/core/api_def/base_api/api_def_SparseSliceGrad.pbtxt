op {
  graph_op_name: "SparseSliceGrad"
  in_arg {
    name: "backprop_val_grad"
    description: <<END
1-D. The gradient with respect to
the non-empty values of the sliced `SparseTensor`.
END
  }
  in_arg {
    name: "input_indices"
    description: <<END
2-D.  The `indices` of the input `SparseTensor`.
END
  }
  in_arg {
    name: "input_start"
    description: <<END
1-D. tensor represents the start of the slice.
END
  }
  in_arg {
    name: "output_indices"
    description: <<END
2-D.  The `indices` of the sliced `SparseTensor`.
END
  }
  out_arg {
    name: "val_grad"
    description: <<END
1-D. The gradient with respect to the non-empty values of input `SparseTensor`.
END
  }
  summary: "The gradient operator for the SparseSlice op."
  description: <<END
This op takes in the upstream gradient w.r.t. non-empty values of
the sliced `SparseTensor`, and outputs the gradients w.r.t.
the non-empty values of input `SparseTensor`.
END
}

op {
  graph_op_name: "IsVariableInitialized"
  in_arg {
    name: "ref"
    description: <<END
Should be from a `Variable` node. May be uninitialized.
END
  }
  attr {
    name: "dtype"
    description: <<END
The type of elements in the variable tensor.
END
  }
  summary: "Checks whether a tensor has been initialized."
  description: <<END
Outputs boolean scalar indicating whether the tensor has been initialized.
END
}

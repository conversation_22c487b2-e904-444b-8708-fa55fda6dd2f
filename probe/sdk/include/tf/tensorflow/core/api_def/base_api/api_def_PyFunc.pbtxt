op {
  graph_op_name: "PyFunc"
  visibility: SKIP
  in_arg {
    name: "input"
    description: <<END
List of Tensors that will provide input to the Op.
END
  }
  out_arg {
    name: "output"
    description: <<END
The outputs from the Op.
END
  }
  attr {
    name: "token"
    description: <<END
A token representing a registered python function in this address space.
END
  }
  attr {
    name: "Tin"
    description: <<END
Data types of the inputs to the op.
END
  }
  attr {
    name: "Tout"
    description: <<END
Data types of the outputs from the op.
The length of the list specifies the number of outputs.
END
  }
  summary: "Invokes a python function to compute func(input)->output."
  description: <<END
This operation is considered stateful. For a stateless version, see
PyFuncStateless.
END
}

op {
  graph_op_name: "DiagPart"
  in_arg {
    name: "input"
    description: <<END
Rank k tensor where k is even and not zero.
END
  }
  out_arg {
    name: "diagonal"
    description: <<END
The extracted diagonal.
END
  }
  summary: "Returns the diagonal part of the tensor."
  description: <<END
This operation returns a tensor with the `diagonal` part
of the `input`. The `diagonal` part is computed as follows:

Assume `input` has dimensions `[D1,..., Dk, D1,..., Dk]`, then the output is a
tensor of rank `k` with dimensions `[D1,..., Dk]` where:

`diagonal[i1,..., ik] = input[i1, ..., ik, i1,..., ik]`.

For example:

```
# 'input' is [[1, 0, 0, 0]
              [0, 2, 0, 0]
              [0, 0, 3, 0]
              [0, 0, 0, 4]]

tf.diag_part(input) ==> [1, 2, 3, 4]
```
END
}

op {
  graph_op_name: "LoadTPUEmbeddingAdadeltaParameters"
  visibility: HIDDEN
  in_arg {
    name: "parameters"
    description: <<END
Value of parameters used in the Adadelta optimization algorithm.
END
  }
  in_arg {
    name: "accumulators"
    description: <<END
Value of accumulators used in the Adadelta optimization algorithm.
END
  }
  in_arg {
    name: "updates"
    description: <<END
Value of updates used in the Adadelta optimization algorithm.
END
  }
  summary: "Load Adadelta embedding parameters."
  description: <<END
An op that loads optimization parameters into HBM for embedding. Must be
preceded by a ConfigureTPUEmbeddingHost op that sets up the correct
embedding table configuration. For example, this op is used to install
parameters that are loaded from a checkpoint before a training loop is
executed.
END
}

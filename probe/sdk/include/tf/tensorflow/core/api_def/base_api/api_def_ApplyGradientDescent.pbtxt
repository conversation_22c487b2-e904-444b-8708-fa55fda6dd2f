op {
  graph_op_name: "ApplyGradientDescent"
  in_arg {
    name: "var"
    description: <<END
Should be from a Variable().
END
  }
  in_arg {
    name: "alpha"
    description: <<END
Scaling factor. Must be a scalar.
END
  }
  in_arg {
    name: "delta"
    description: <<END
The change.
END
  }
  out_arg {
    name: "out"
    description: <<END
Same as "var".
END
  }
  attr {
    name: "use_locking"
    description: <<END
If `True`, the subtraction will be protected by a lock;
otherwise the behavior is undefined, but may exhibit less contention.
END
  }
  summary: "Update \'*var\' by subtracting \'alpha\' * \'delta\' from it."
}

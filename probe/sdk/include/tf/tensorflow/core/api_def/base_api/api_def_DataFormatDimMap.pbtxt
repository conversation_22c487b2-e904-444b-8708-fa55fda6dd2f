op {
  graph_op_name: "DataFormatDimMap"
  in_arg {
    name: "x"
    description: <<END
A Tensor with each element as a dimension index in source data format.
Must be in the range [-4, 4).
END
  }
  out_arg {
    name: "y"
    description: <<END
A Tensor with each element as a dimension index in destination data format.
END
  }
  attr {
    name: "src_format"
    description: <<END
source data format.
END
  }
  attr {
    name: "dst_format"
    description: <<END
destination data format.
END
  }
  summary: "Returns the dimension index in the destination data format given the one in"
  description: <<END
the source data format.
END
}

op {
  graph_op_name: "TFRecordDataset"
  visibility: HIDDE<PERSON>
  in_arg {
    name: "filenames"
    description: <<END
A scalar or vector containing the name(s) of the file(s) to be
read.
END
  }
  in_arg {
    name: "compression_type"
    description: <<END
A scalar containing either (i) the empty string (no
compression), (ii) "ZLIB", or (iii) "GZIP".
END
  }
  in_arg {
    name: "buffer_size"
    description: <<END
A scalar representing the number of bytes to buffer. A value of
0 means no buffering will be performed.
END
  }
  summary: "Creates a dataset that emits the records from one or more TFRecord files."
}

op {
  graph_op_name: "AccumulatorSetGlobalStep"
  in_arg {
    name: "handle"
    description: <<END
The handle to an accumulator.
END
  }
  in_arg {
    name: "new_global_step"
    description: <<END
The new global_step value to set.
END
  }
  summary: "Updates the accumulator with a new value for global_step."
  description: <<END
Logs warning if the accumulator's value is already higher than
new_global_step.
END
}

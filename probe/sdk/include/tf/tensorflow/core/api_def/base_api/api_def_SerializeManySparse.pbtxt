op {
  graph_op_name: "SerializeManySparse"
  in_arg {
    name: "sparse_indices"
    description: <<END
2-D.  The `indices` of the minibatch `SparseTensor`.
END
  }
  in_arg {
    name: "sparse_values"
    description: <<END
1-D.  The `values` of the minibatch `SparseTensor`.
END
  }
  in_arg {
    name: "sparse_shape"
    description: <<END
1-D.  The `shape` of the minibatch `SparseTensor`.
END
  }
  attr {
    name: "out_type"
    description: <<END
The `dtype` to use for serialization; the supported types are `string`
(default) and `variant`.
END
  }
  summary: "Serialize an `N`-minibatch `SparseTensor` into an `[N, 3]` `Tensor` object."
  description: <<END
The `SparseTensor` must have rank `R` greater than 1, and the first dimension
is treated as the minibatch dimension.  Elements of the `SparseTensor`
must be sorted in increasing order of this first dimension.  The serialized
`SparseTensor` objects going into each row of `serialized_sparse` will have
rank `R-1`.

The minibatch size `N` is extracted from `sparse_shape[0]`.
END
}

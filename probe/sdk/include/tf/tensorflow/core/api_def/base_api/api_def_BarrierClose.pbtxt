op {
  graph_op_name: "BarrierClose"
  in_arg {
    name: "handle"
    description: <<END
The handle to a barrier.
END
  }
  attr {
    name: "cancel_pending_enqueues"
    description: <<END
If true, all pending enqueue requests that are
blocked on the barrier's queue will be canceled. InsertMany will fail, even
if no new key is introduced.
END
  }
  summary: "Closes the given barrier."
  description: <<END
This operation signals that no more new elements will be inserted in the
given barrier. Subsequent InsertMany that try to introduce a new key will fail.
Subsequent InsertMany operations that just add missing components to already
existing elements will continue to succeed. Subsequent TakeMany operations will
continue to succeed if sufficient completed elements remain in the barrier.
Subsequent TakeMany operations that would block will fail immediately.
END
}

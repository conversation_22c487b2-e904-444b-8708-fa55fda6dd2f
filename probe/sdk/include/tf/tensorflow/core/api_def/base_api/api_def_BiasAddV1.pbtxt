op {
  graph_op_name: "BiasAddV1"
  visibility: SKIP
  in_arg {
    name: "value"
    description: <<END
Any number of dimensions.
END
  }
  in_arg {
    name: "bias"
    description: <<END
1-D with size the last dimension of `value`.
END
  }
  out_arg {
    name: "output"
    description: <<END
Broadcasted sum of `value` and `bias`.
END
  }
  summary: "Adds `bias` to `value`."
  description: <<END
This is a deprecated version of BiasAdd and will be soon removed.

This is a special case of `tf.add` where `bias` is restricted to be 1-D.
Broadcasting is supported, so `value` may have any number of dimensions.
END
}

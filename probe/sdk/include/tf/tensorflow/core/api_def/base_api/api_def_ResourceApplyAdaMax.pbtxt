op {
  graph_op_name: "ResourceApplyAdaMax"
  visibility: HIDDE<PERSON>
  in_arg {
    name: "var"
    description: <<END
Should be from a Variable().
END
  }
  in_arg {
    name: "m"
    description: <<END
Should be from a Variable().
END
  }
  in_arg {
    name: "v"
    description: <<END
Should be from a Variable().
END
  }
  in_arg {
    name: "beta1_power"
    description: <<END
Must be a scalar.
END
  }
  in_arg {
    name: "lr"
    description: <<END
Scaling factor. Must be a scalar.
END
  }
  in_arg {
    name: "beta1"
    description: <<END
Momentum factor. Must be a scalar.
END
  }
  in_arg {
    name: "beta2"
    description: <<END
Momentum factor. Must be a scalar.
END
  }
  in_arg {
    name: "epsilon"
    description: <<END
Ridge term. Must be a scalar.
END
  }
  in_arg {
    name: "grad"
    description: <<END
The gradient.
END
  }
  attr {
    name: "use_locking"
    description: <<END
If `True`, updating of the var, m, and v tensors will be protected
by a lock; otherwise the behavior is undefined, but may exhibit less
contention.
END
  }
  summary: "Update \'*var\' according to the AdaMax algorithm."
  description: <<END
m_t <- beta1 * m_{t-1} + (1 - beta1) * g
v_t <- max(beta2 * v_{t-1}, abs(g))
variable <- variable - learning_rate / (1 - beta1^t) * m_t / (v_t + epsilon)
END
}

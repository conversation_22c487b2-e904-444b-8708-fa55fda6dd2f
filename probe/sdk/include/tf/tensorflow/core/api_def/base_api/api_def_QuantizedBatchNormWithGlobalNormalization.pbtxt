op {
  graph_op_name: "QuantizedBatchNormWithGlobalNormalization"
  in_arg {
    name: "t"
    description: <<END
A 4D input Tensor.
END
  }
  in_arg {
    name: "t_min"
    description: <<END
The value represented by the lowest quantized input.
END
  }
  in_arg {
    name: "t_max"
    description: <<END
The value represented by the highest quantized input.
END
  }
  in_arg {
    name: "m"
    description: <<END
A 1D mean Tensor with size matching the last dimension of t.
This is the first output from tf.nn.moments,
or a saved moving average thereof.
END
  }
  in_arg {
    name: "m_min"
    description: <<END
The value represented by the lowest quantized mean.
END
  }
  in_arg {
    name: "m_max"
    description: <<END
The value represented by the highest quantized mean.
END
  }
  in_arg {
    name: "v"
    description: <<END
A 1D variance Tensor with size matching the last dimension of t.
This is the second output from tf.nn.moments,
or a saved moving average thereof.
END
  }
  in_arg {
    name: "v_min"
    description: <<END
The value represented by the lowest quantized variance.
END
  }
  in_arg {
    name: "v_max"
    description: <<END
The value represented by the highest quantized variance.
END
  }
  in_arg {
    name: "beta"
    description: <<END
A 1D beta Tensor with size matching the last dimension of t.
An offset to be added to the normalized tensor.
END
  }
  in_arg {
    name: "beta_min"
    description: <<END
The value represented by the lowest quantized offset.
END
  }
  in_arg {
    name: "beta_max"
    description: <<END
The value represented by the highest quantized offset.
END
  }
  in_arg {
    name: "gamma"
    description: <<END
A 1D gamma Tensor with size matching the last dimension of t.
If "scale_after_normalization" is true, this tensor will be multiplied
with the normalized tensor.
END
  }
  in_arg {
    name: "gamma_min"
    description: <<END
The value represented by the lowest quantized gamma.
END
  }
  in_arg {
    name: "gamma_max"
    description: <<END
The value represented by the highest quantized gamma.
END
  }
  attr {
    name: "variance_epsilon"
    description: <<END
A small float number to avoid dividing by 0.
END
  }
  attr {
    name: "scale_after_normalization"
    description: <<END
A bool indicating whether the resulted tensor
needs to be multiplied with gamma.
END
  }
  summary: "Quantized Batch normalization."
  description: <<END
This op is deprecated and will be removed in the future. Prefer
`tf.nn.batch_normalization`.
END
}

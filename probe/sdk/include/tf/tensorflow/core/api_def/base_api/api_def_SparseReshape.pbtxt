op {
  graph_op_name: "SparseReshape"
  in_arg {
    name: "input_indices"
    description: <<END
2-D.  `N x R_in` matrix with the indices of non-empty values in a
SparseTensor.
END
  }
  in_arg {
    name: "input_shape"
    description: <<END
1-D.  `R_in` vector with the input SparseTensor's dense shape.
END
  }
  in_arg {
    name: "new_shape"
    description: <<END
1-D.  `R_out` vector with the requested new dense shape.
END
  }
  out_arg {
    name: "output_indices"
    description: <<END
2-D.  `N x R_out` matrix with the updated indices of non-empty
values in the output SparseTensor.
END
  }
  out_arg {
    name: "output_shape"
    description: <<END
1-D.  `R_out` vector with the full dense shape of the output
SparseTensor.  This is the same as `new_shape` but with any -1 dimensions
filled in.
END
  }
  summary: "Reshapes a SparseTensor to represent values in a new dense shape."
  description: <<END
This operation has the same semantics as reshape on the represented dense
tensor.  The `input_indices` are recomputed based on the requested `new_shape`.

If one component of `new_shape` is the special value -1, the size of that
dimension is computed so that the total dense size remains constant.  At
most one component of `new_shape` can be -1.  The number of dense elements
implied by `new_shape` must be the same as the number of dense elements
originally implied by `input_shape`.

Reshaping does not affect the order of values in the SparseTensor.

If the input tensor has rank `R_in` and `N` non-empty values, and `new_shape`
has length `R_out`, then `input_indices` has shape `[N, R_in]`,
`input_shape` has length `R_in`, `output_indices` has shape `[N, R_out]`, and
`output_shape` has length `R_out`.
END
}

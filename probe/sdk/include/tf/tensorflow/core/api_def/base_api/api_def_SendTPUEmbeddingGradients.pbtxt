op {
  graph_op_name: "SendTPUEmbeddingGradients"
  visibility: HIDDE<PERSON>
  in_arg {
    name: "inputs"
    description: <<END
A TensorList of gradients with which to update embedding tables.
This argument has the same length and shapes as the return value of
RecvTPUEmbeddingActivations, but contains gradients of the model's loss
with respect to the embedding activations. The embedding tables are updated
from these gradients via the optimizer specified in the TPU embedding
configuration given to tpu.initialize_system.
END
  }
  in_arg {
    name: "learning_rates"
    description: <<END
A TensorList of float32 scalars, one for each dynamic learning
rate tag: see the comments in
//third_party/tensorflow/core/protobuf/tpu/optimization_parameters.proto.
Multiple tables can share the same dynamic learning rate tag as specified
in the configuration. If the learning rates for all tables are constant,
this list should be empty.
END
  }
  attr {
    name: "config"
    description: <<END
Serialized TPUEmbeddingConfiguration proto.
END
  }
  summary: "Performs gradient updates of embedding tables."
}

op {
  graph_op_name: "RFFT"
  in_arg {
    name: "input"
    description: <<END
A float32 tensor.
END
  }
  in_arg {
    name: "fft_length"
    description: <<END
An int32 tensor of shape [1]. The FFT length.
END
  }
  out_arg {
    name: "output"
    description: <<END
A complex64 tensor of the same rank as `input`. The inner-most
  dimension of `input` is replaced with the `fft_length / 2 + 1` unique
  frequency components of its 1D Fourier transform.

@compatibility(numpy)
Equivalent to np.fft.rfft
@end_compatibility
END
  }
  summary: "Real-valued fast Fourier transform."
  description: <<END
Computes the 1-dimensional discrete Fourier transform of a real-valued signal
over the inner-most dimension of `input`.

Since the DFT of a real signal is Hermitian-symmetric, `RFFT` only returns the
`fft_length / 2 + 1` unique components of the FFT: the zero-frequency term,
followed by the `fft_length / 2` positive-frequency terms.

Along the axis `RFFT` is computed on, if `fft_length` is smaller than the
corresponding dimension of `input`, the dimension is cropped. If it is larger,
the dimension is padded with zeros.
END
}

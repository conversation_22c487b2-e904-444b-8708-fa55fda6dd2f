op {
  graph_op_name: "RFFT2D"
  in_arg {
    name: "input"
    description: <<END
A float32 tensor.
END
  }
  in_arg {
    name: "fft_length"
    description: <<END
An int32 tensor of shape [2]. The FFT length for each dimension.
END
  }
  out_arg {
    name: "output"
    description: <<END
A complex64 tensor of the same rank as `input`. The inner-most 2
  dimensions of `input` are replaced with their 2D Fourier transform. The
  inner-most dimension contains `fft_length / 2 + 1` unique frequency
  components.

@compatibility(numpy)
Equivalent to np.fft.rfft2
@end_compatibility
END
  }
  summary: "2D real-valued fast Fourier transform."
  description: <<END
Computes the 2-dimensional discrete Fourier transform of a real-valued signal
over the inner-most 2 dimensions of `input`.

Since the DFT of a real signal is Hermitian-symmetric, `RFFT2D` only returns the
`fft_length / 2 + 1` unique components of the FFT for the inner-most dimension
of `output`: the zero-frequency term, followed by the `fft_length / 2`
positive-frequency terms.

Along each axis `RFFT2D` is computed on, if `fft_length` is smaller than the
corresponding dimension of `input`, the dimension is cropped. If it is larger,
the dimension is padded with zeros.
END
}

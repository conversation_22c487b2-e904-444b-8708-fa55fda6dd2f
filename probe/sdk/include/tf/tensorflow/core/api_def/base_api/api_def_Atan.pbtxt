op {
  graph_op_name: "Atan"
  summary: "Computes the trignometric inverse tangent of x element-wise."
  description: <<END
The `tf.math.atan` operation returns the inverse of `tf.math.tan`, such that
if `y = tf.math.tan(x)` then, `x = tf.math.atan(y)`.

**Note**: The output of `tf.math.atan` will lie within the invertible range 
of tan, i.e (-pi/2, pi/2).

For example:

```python
# Note: [1.047, 0.785] ~= [(pi/3), (pi/4)]
x = tf.constant([1.047, 0.785])
y = tf.math.tan(x) # [1.731261, 0.99920404]

tf.math.atan(y) # [1.047, 0.785] = x
```

END
}

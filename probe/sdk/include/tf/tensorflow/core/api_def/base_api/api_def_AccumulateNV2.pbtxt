op {
  graph_op_name: "AccumulateNV2"
  in_arg {
    name: "inputs"
    description: <<END
A list of `Tensor` objects, each with same shape and type.
END
  }
  attr {
    name: "shape"
    description: <<END
Shape of elements of `inputs`.
END
  }
  summary: "Returns the element-wise sum of a list of tensors."
  description: <<END
`tf.accumulate_n_v2` performs the same operation as `tf.add_n`, but does not
wait for all of its inputs to be ready before beginning to sum. This can
save memory if inputs are ready at different times, since minimum temporary
storage is proportional to the output size rather than the inputs size.

Unlike the original `accumulate_n`, `accumulate_n_v2` is differentiable.

Returns a `Tensor` of same shape and type as the elements of `inputs`.
END
}

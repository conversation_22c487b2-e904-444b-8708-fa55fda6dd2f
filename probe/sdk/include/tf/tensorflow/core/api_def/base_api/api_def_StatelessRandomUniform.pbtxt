op {
  graph_op_name: "StatelessRandomUniform"
  in_arg {
    name: "shape"
    description: <<END
The shape of the output tensor.
END
  }
  in_arg {
    name: "seed"
    description: <<END
2 seeds (shape [2]).
END
  }
  out_arg {
    name: "output"
    description: <<END
Random values with specified shape.
END
  }
  attr {
    name: "dtype"
    description: <<END
The type of the output.
END
  }
  summary: "Outputs deterministic pseudorandom random values from a uniform distribution."
  description: <<END
The generated values follow a uniform distribution in the range `[0, 1)`. The
lower bound 0 is included in the range, while the upper bound 1 is excluded.

The outputs are a deterministic function of `shape` and `seed`.
END
}

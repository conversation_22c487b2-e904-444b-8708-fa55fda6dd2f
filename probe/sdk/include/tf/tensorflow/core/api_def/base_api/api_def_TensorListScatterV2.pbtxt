op {
  graph_op_name: "TensorListScatterV2"
  summary: "Creates a TensorList by indexing into a Tensor."
  description: <<END
Each member of the TensorList corresponds to one row of the input tensor,
specified by the given index (see `tf.gather`).

tensor: The input tensor.
indices: The indices used to index into the list.
element_shape: The shape of the elements in the list (can be less specified than
  the shape of the tensor).
num_elements: The size of the output list. Must be large enough to accommodate
  the largest index in indices. If -1, the list is just large enough to include
  the largest index in indices.
output_handle: The TensorList.
END
}

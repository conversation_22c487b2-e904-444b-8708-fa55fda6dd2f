op {
  graph_op_name: "FixedLengthRecordReader"
  visibility: SKIP
  out_arg {
    name: "reader_handle"
    description: <<END
The handle to reference the Reader.
END
  }
  attr {
    name: "header_bytes"
    description: <<END
Number of bytes in the header, defaults to 0.
END
  }
  attr {
    name: "record_bytes"
    description: <<END
Number of bytes in the record.
END
  }
  attr {
    name: "footer_bytes"
    description: <<END
Number of bytes in the footer, defaults to 0.
END
  }
  attr {
    name: "hop_bytes"
    description: <<END
Number of bytes to hop before each read. Default of 0 means using
record_bytes.
END
  }
  attr {
    name: "container"
    description: <<END
If non-empty, this reader is placed in the given container.
Otherwise, a default container is used.
END
  }
  attr {
    name: "shared_name"
    description: <<END
If non-empty, this reader is named in the given bucket
with this shared_name. Otherwise, the node name is used instead.
END
  }
  summary: "A Reader that outputs fixed-length records from a file."
}

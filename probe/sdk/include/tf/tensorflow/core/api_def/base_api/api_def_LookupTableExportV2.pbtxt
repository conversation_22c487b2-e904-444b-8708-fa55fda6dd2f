op {
  graph_op_name: "LookupTableExportV2"
  endpoint {
    name: "LookupTableExport"
  }
  in_arg {
    name: "table_handle"
    description: <<END
Handle to the table.
END
  }
  out_arg {
    name: "keys"
    description: <<END
Vector of all keys present in the table.
END
  }
  out_arg {
    name: "values"
    description: <<END
Tensor of all values in the table. Indexed in parallel with `keys`.
END
  }
  summary: "Outputs all keys and values in the table."
}

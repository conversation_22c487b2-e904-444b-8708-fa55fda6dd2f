op {
  graph_op_name: "RetrieveTPUEmbeddingAdagradParametersGradAccumDebug"
  visibility: HIDDEN
  out_arg {
    name: "parameters"
    description: <<END
Parameter parameters updated by the Adagrad optimization algorithm.
END
  }
  out_arg {
    name: "accumulators"
    description: <<END
Parameter accumulators updated by the Adagrad optimization algorithm.
END
  }
  out_arg {
    name: "gradient_accumulators"
    description: <<END
Parameter gradient_accumulators updated by the Adagrad optimization algorithm.
END
  }
  summary: "Retrieve Adagrad embedding parameters with debug support."
  description: <<END
An op that retrieves optimization parameters from embedding to host
memory. Must be preceded by a ConfigureTPUEmbeddingHost op that sets up
the correct embedding table configuration. For example, this op is
used to retrieve updated parameters before saving a checkpoint.
END
}

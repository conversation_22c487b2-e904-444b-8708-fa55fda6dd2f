op {
  graph_op_name: "DepthwiseConv2dNativeBackpropInput"
  in_arg {
    name: "input_sizes"
    description: <<END
An integer vector representing the shape of `input`, based
on `data_format`.  For example, if `data_format` is 'NHWC' then
 `input` is a 4-D `[batch, height, width, channels]` tensor.
END
  }
  in_arg {
    name: "filter"
    description: <<END
4-D with shape
`[filter_height, filter_width, in_channels, depthwise_multiplier]`.
END
  }
  in_arg {
    name: "out_backprop"
    description: <<END
4-D with shape  based on `data_format`.
For example, if `data_format` is 'NHWC' then
out_backprop shape is `[batch, out_height, out_width, out_channels]`.
Gradients w.r.t. the output of the convolution.
END
  }
  out_arg {
    name: "output"
    description: <<END
4-D with shape according to `data_format`.  For example, if
`data_format` is 'NHWC', output shape is `[batch, in_height,
in_width, in_channels]`.  Gradient w.r.t. the input of the
convolution.
END
  }
  attr {
    name: "strides"
    description: <<END
The stride of the sliding window for each dimension of the input
of the convolution.
END
  }
  attr {
    name: "padding"
    description: <<END
The type of padding algorithm to use.
END
  }
  attr {
    name: "data_format"
    description: <<END
Specify the data format of the input and output data. With the
default format "NHWC", the data is stored in the order of:
    [batch, height, width, channels].
Alternatively, the format could be "NCHW", the data storage order of:
    [batch, channels, height, width].
END
  }
  attr {
    name: "dilations"
    description: <<END
1-D tensor of length 4.  The dilation factor for each dimension of
`input`. If set to k > 1, there will be k-1 skipped cells between each filter
element on that dimension. The dimension order is determined by the value of
`data_format`, see above for details. Dilations in the batch and depth
dimensions must be 1.
END
  }
  summary: "Computes the gradients of depthwise convolution with respect to the input."
}

op {
  graph_op_name: "TensorListSplit"
  summary: "Splits a tensor into a list."
  description: <<END
list[i] corresponds to lengths[i] tensors from the input tensor.
The tensor must have rank at least 1 and contain exactly sum(lengths) elements.

tensor: The input tensor.
element_shape: A shape compatible with that of elements in the tensor.
lengths: Vector of sizes of the 0th dimension of tensors in the list.
output_handle: The list.
END
}

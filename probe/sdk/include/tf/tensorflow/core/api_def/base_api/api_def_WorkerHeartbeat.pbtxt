op {
  graph_op_name: "WorkerHeartbeat"
  visibility: HIDDE<PERSON>
  in_arg {
    name: "request"
    description: <<END
A string tensor containing a serialized WorkerHeartbeatRequest
END
  }
  out_arg {
    name: "response"
    description: <<END
A string tensor containing a serialized WorkerHeartbeatResponse
END
  }
  summary: "Worker heartbeat op."
  description: <<END
Heartbeats may be sent periodically to indicate the coordinator is still active,
to retrieve the current worker status and to expedite shutdown when necessary.
END
}

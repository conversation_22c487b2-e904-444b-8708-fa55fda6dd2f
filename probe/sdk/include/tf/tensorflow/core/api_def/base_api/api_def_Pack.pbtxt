op {
  graph_op_name: "Pack"
  endpoint {
    name: "<PERSON><PERSON>"
  }
  in_arg {
    name: "values"
    description: <<END
Must be of same shape and type.
END
  }
  out_arg {
    name: "output"
    description: <<END
The packed tensor.
END
  }
  attr {
    name: "axis"
    description: <<END
Dimension along which to pack.  Negative values wrap around, so the
valid range is `[-(R+1), R+1)`.
END
  }
  summary: "Packs a list of `N` rank-`R` tensors into one rank-`(R+1)` tensor."
  description: <<END
Packs the `N` tensors in `values` into a tensor with rank one higher than each
tensor in `values`, by packing them along the `axis` dimension.
Given a list of tensors of shape `(A, B, C)`;

if `axis == 0` then the `output` tensor will have the shape `(N, A, B, C)`.
if `axis == 1` then the `output` tensor will have the shape `(A, N, B, C)`.
Etc.

For example:

```
# 'x' is [1, 4]
# 'y' is [2, 5]
# 'z' is [3, 6]
pack([x, y, z]) => [[1, 4], [2, 5], [3, 6]]  # Pack along first dim.
pack([x, y, z], axis=1) => [[1, 2, 3], [4, 5, 6]]
```

This is the opposite of `unpack`.
E<PERSON>
}

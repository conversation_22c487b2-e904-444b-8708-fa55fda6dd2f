op {
  graph_op_name: "Dilation2D"
  in_arg {
    name: "input"
    description: <<END
4-D with shape `[batch, in_height, in_width, depth]`.
END
  }
  in_arg {
    name: "filter"
    description: <<END
3-D with shape `[filter_height, filter_width, depth]`.
E<PERSON>
  }
  out_arg {
    name: "output"
    description: <<END
4-D with shape `[batch, out_height, out_width, depth]`.
END
  }
  attr {
    name: "strides"
    description: <<END
The stride of the sliding window for each dimension of the input
tensor. Must be: `[1, stride_height, stride_width, 1]`.
END
  }
  attr {
    name: "rates"
    description: <<END
The input stride for atrous morphological dilation. Must be:
`[1, rate_height, rate_width, 1]`.
END
  }
  attr {
    name: "padding"
    description: <<END
The type of padding algorithm to use.
END
  }
  summary: "Computes the grayscale dilation of 4-D `input` and 3-D `filter` tensors."
  description: <<END
The `input` tensor has shape `[batch, in_height, in_width, depth]` and the
`filter` tensor has shape `[filter_height, filter_width, depth]`, i.e., each
input channel is processed independently of the others with its own structuring
function. The `output` tensor has shape
`[batch, out_height, out_width, depth]`. The spatial dimensions of the output
tensor depend on the `padding` algorithm. We currently only support the default
"NHWC" `data_format`.

In detail, the grayscale morphological 2-D dilation is the max-sum correlation
(for consistency with `conv2d`, we use unmirrored filters):

    output[b, y, x, c] =
       max_{dy, dx} input[b,
                          strides[1] * y + rates[1] * dy,
                          strides[2] * x + rates[2] * dx,
                          c] +
                    filter[dy, dx, c]

Max-pooling is a special case when the filter has size equal to the pooling
kernel size and contains all zeros.

Note on duality: The dilation of `input` by the `filter` is equal to the
negation of the erosion of `-input` by the reflected `filter`.
END
}

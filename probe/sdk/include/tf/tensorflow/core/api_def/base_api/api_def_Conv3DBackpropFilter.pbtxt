op {
  graph_op_name: "Conv3DBackpropFilter"
  in_arg {
    name: "input"
    description: <<E<PERSON>
Shape `[batch, depth, rows, cols, in_channels]`.
END
  }
  in_arg {
    name: "filter"
    description: <<E<PERSON>
Shape `[depth, rows, cols, in_channels, out_channels]`.
`in_channels` must match between `input` and `filter`.
END
  }
  in_arg {
    name: "out_backprop"
    description: <<END
Backprop signal of shape `[batch, out_depth, out_rows, out_cols,
out_channels]`.
END
  }
  attr {
    name: "strides"
    description: <<END
1-D tensor of length 5. The stride of the sliding window for each
dimension of `input`. Must have `strides[0] = strides[4] = 1`.
END
  }
  attr {
    name: "padding"
    description: <<END
The type of padding algorithm to use.
END
  }
  summary: "Computes the gradients of 3-D convolution with respect to the filter."
}

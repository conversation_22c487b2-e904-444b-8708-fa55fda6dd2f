op {
  graph_op_name: "MatrixDeterminant"
  in_arg {
    name: "input"
    description: <<END
Shape is `[..., <PERSON>, <PERSON>]`.
END
  }
  out_arg {
    name: "output"
    description: <<END
Shape is `[...]`.
END
  }
  summary: "Computes the determinant of one or more square matrices."
  description: <<END
The input is a tensor of shape `[..., M, M]` whose inner-most 2 dimensions
form square matrices. The output is a tensor containing the determinants
for all input submatrices `[..., :, :]`.
END
}

op {
  graph_op_name: "BiasAdd"
  in_arg {
    name: "value"
    description: <<END
Any number of dimensions.
END
  }
  in_arg {
    name: "bias"
    description: <<END
1-D with size the last dimension of `value`.
END
  }
  out_arg {
    name: "output"
    description: <<END
Broadcasted sum of `value` and `bias`.
END
  }
  attr {
    name: "data_format"
    description: <<END
Specify the data format of the input and output data. With the
default format "NHWC", the bias tensor will be added to the last dimension
of the value tensor.
Alternatively, the format could be "NCHW", the data storage order of:
    [batch, in_channels, in_height, in_width].
The tensor will be added to "in_channels", the third-to-the-last
    dimension.
END
  }
  summary: "Adds `bias` to `value`."
  description: <<END
This is a special case of `tf.add` where `bias` is restricted to be 1-D.
Broadcasting is supported, so `value` may have any number of dimensions.
END
}

op {
  graph_op_name: "Skipgram"
  out_arg {
    name: "vocab_word"
    description: <<END
A vector of words in the corpus.
END
  }
  out_arg {
    name: "vocab_freq"
    description: <<END
Frequencies of words. Sorted in the non-ascending order.
END
  }
  out_arg {
    name: "words_per_epoch"
    description: <<END
Number of words per epoch in the data file.
END
  }
  out_arg {
    name: "current_epoch"
    description: <<END
The current epoch number.
END
  }
  out_arg {
    name: "total_words_processed"
    description: <<END
The total number of words processed so far.
END
  }
  out_arg {
    name: "examples"
    description: <<END
A vector of word ids.
END
  }
  out_arg {
    name: "labels"
    description: <<END
A vector of word ids.
END
  }
  attr {
    name: "filename"
    description: <<END
The corpus's text file name.
END
  }
  attr {
    name: "batch_size"
    description: <<END
The size of produced batch.
END
  }
  attr {
    name: "window_size"
    description: <<END
The number of words to predict to the left and right of the target.
END
  }
  attr {
    name: "min_count"
    description: <<END
The minimum number of word occurrences for it to be included in the
vocabulary.
END
  }
  attr {
    name: "subsample"
    description: <<END
Threshold for word occurrence. Words that appear with higher
frequency will be randomly down-sampled. Set to 0 to disable.
END
  }
  summary: "Parses a text file and creates a batch of examples."
}

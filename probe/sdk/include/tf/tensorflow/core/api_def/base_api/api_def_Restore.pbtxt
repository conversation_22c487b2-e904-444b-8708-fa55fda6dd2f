op {
  graph_op_name: "Restore"
  in_arg {
    name: "file_pattern"
    description: <<END
Must have a single element. The pattern of the files from
which we read the tensor.
END
  }
  in_arg {
    name: "tensor_name"
    description: <<END
Must have a single element. The name of the tensor to be
restored.
END
  }
  out_arg {
    name: "tensor"
    description: <<END
The restored tensor.
END
  }
  attr {
    name: "dt"
    description: <<END
The type of the tensor to be restored.
END
  }
  attr {
    name: "preferred_shard"
    description: <<END
Index of file to open first if multiple files match
`file_pattern`.
END
  }
  summary: "Restores a tensor from checkpoint files."
  description: <<END
Reads a tensor stored in one or several files. If there are several files (for
instance because a tensor was saved as slices), `file_pattern` may contain
wildcard symbols (`*` and `?`) in the filename portion only, not in the
directory portion.

If a `file_pattern` matches several files, `preferred_shard` can be used to hint
in which file the requested tensor is likely to be found. This op will first
open the file at index `preferred_shard` in the list of matching files and try
to restore tensors from that file.  Only if some tensors or tensor slices are
not found in that first file, then the Op opens all the files. Setting
`preferred_shard` to match the value passed as the `shard` input
of a matching `Save` Op may speed up <PERSON>ore.  This attribute only affects
performance, not correctness.  The default value -1 means files are processed in
order.

See also `RestoreSlice`.
END
}

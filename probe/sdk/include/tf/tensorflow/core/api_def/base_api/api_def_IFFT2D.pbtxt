op {
  graph_op_name: "IFFT2D"
  in_arg {
    name: "input"
    description: <<END
A complex tensor.
END
  }
  out_arg {
    name: "output"
    description: <<END
A complex tensor of the same shape as `input`. The inner-most 2
  dimensions of `input` are replaced with their inverse 2D Fourier transform.

@compatibility(numpy)
Equivalent to np.fft.ifft2
@end_compatibility
END
  }
  summary: "Inverse 2D fast Fourier transform."
  description: <<END
Computes the inverse 2-dimensional discrete Fourier transform over the
inner-most 2 dimensions of `input`.
END
}

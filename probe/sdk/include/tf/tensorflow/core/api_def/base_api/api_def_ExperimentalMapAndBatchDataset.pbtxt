op {
  graph_op_name: "ExperimentalMapAndBatchDataset"
  visibility: HIDDE<PERSON>
  in_arg {
    name: "input_dataset"
    description: <<END
A variant tensor representing the input dataset.
END
  }
  in_arg {
    name: "other_arguments"
    description: <<END
A list of tensors, typically values that were captured when building a closure
for `f`.
END
  }
  in_arg {
    name: "batch_size"
    description: <<END
A scalar representing the number of elements to accumulate in a
batch. It determines the number of concurrent invocations of `f` that process
elements from `input_dataset` in parallel.
END
  }
  in_arg {
    name: "num_parallel_calls"
    description: <<END
A scalar representing the maximum number of parallel invocations of the `map_fn`
function. Applying the `map_fn` on consecutive input elements in parallel has
the potential to improve input pipeline throughput.
END
  }
  in_arg {
    name: "drop_remainder"
    description: <<END
A scalar representing whether the last batch should be dropped in case its size
is smaller than desired.
END
  }
  attr {
    name: "f"
    description: <<END
A function to apply to the outputs of `input_dataset`.
END
  }
  summary: "Creates a dataset that fuses mapping with batching."
  description: <<END
Creates a dataset that applies `f` to the outputs of `input_dataset` and then
batches `batch_size` of them.

Unlike a "MapDataset", which applies `f` sequentially, this dataset invokes up
to `batch_size * num_parallel_batches` copies of `f` in parallel.
END
}

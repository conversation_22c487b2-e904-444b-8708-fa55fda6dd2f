op {
  graph_op_name: "DrawBoundingBoxes"
  in_arg {
    name: "images"
    description: <<END
4-D with shape `[batch, height, width, depth]`. A batch of images.
END
  }
  in_arg {
    name: "boxes"
    description: <<END
3-D with shape `[batch, num_bounding_boxes, 4]` containing bounding
boxes.
END
  }
  out_arg {
    name: "output"
    description: <<END
4-D with the same shape as `images`. The batch of input images with
bounding boxes drawn on the images.
END
  }
  summary: "Draw bounding boxes on a batch of images."
  description: <<END
Outputs a copy of `images` but draws on top of the pixels zero or more bounding
boxes specified by the locations in `boxes`. The coordinates of the each
bounding box in `boxes` are encoded as `[y_min, x_min, y_max, x_max]`. The
bounding box coordinates are floats in `[0.0, 1.0]` relative to the width and
height of the underlying image.

For example, if an image is 100 x 200 pixels (height x width) and the bounding
box is `[0.1, 0.2, 0.5, 0.9]`, the upper-left and bottom-right coordinates of
the bounding box will be `(40, 10)` to `(180, 50)` (in (x,y) coordinates).

Parts of the bounding box may fall outside the image.
END
}

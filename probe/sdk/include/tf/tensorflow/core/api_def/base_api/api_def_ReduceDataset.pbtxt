op {
  visibility: HIDDEN
  graph_op_name: "ReduceDataset"
  in_arg {
    name: "input_dataset"
    description: <<END
A variant tensor representing the input dataset.
END
  }
  in_arg {
    name: "initial_state"
    description: <<END
A nested structure of tensors, representing the initial state of the
transformation.
END
  }
  attr {
    name: "f"
    description: <<END
A function that maps `(old_state, input_element)` to `new_state`. It must take
two arguments and return a nested structures of tensors. The structure of
`new_state` must match the structure of `initial_state`.
END
  }
  summary: "Reduces the input dataset to a singleton using a reduce function."
}

op {
  graph_op_name: "BatchNormWithGlobalNormalization"
  in_arg {
    name: "t"
    description: <<END
A 4D input Tensor.
END
  }
  in_arg {
    name: "m"
    description: <<END
A 1D mean Tensor with size matching the last dimension of t.
This is the first output from tf.nn.moments,
or a saved moving average thereof.
END
  }
  in_arg {
    name: "v"
    description: <<END
A 1D variance Tensor with size matching the last dimension of t.
This is the second output from tf.nn.moments,
or a saved moving average thereof.
END
  }
  in_arg {
    name: "beta"
    description: <<END
A 1D beta Tensor with size matching the last dimension of t.
An offset to be added to the normalized tensor.
END
  }
  in_arg {
    name: "gamma"
    description: <<END
A 1D gamma Tensor with size matching the last dimension of t.
If "scale_after_normalization" is true, this tensor will be multiplied
with the normalized tensor.
END
  }
  attr {
    name: "variance_epsilon"
    description: <<END
A small float number to avoid dividing by 0.
END
  }
  attr {
    name: "scale_after_normalization"
    description: <<END
A bool indicating whether the resulted tensor
needs to be multiplied with gamma.
END
  }
  summary: "Batch normalization."
  description: <<END
This op is deprecated. Prefer `tf.nn.batch_normalization`.
END
}

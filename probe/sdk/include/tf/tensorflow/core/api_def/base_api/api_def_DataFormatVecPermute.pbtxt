op {
  graph_op_name: "DataFormatVecPermute"
  in_arg {
    name: "x"
    description: <<END
Vector of size 4 or Tensor of shape (4, 2) in source data format.
END
  }
  out_arg {
    name: "y"
    description: <<END
Vector of size 4 or Tensor of shape (4, 2) in destination data format.
END
  }
  attr {
    name: "src_format"
    description: <<END
source data format.
END
  }
  attr {
    name: "dst_format"
    description: <<END
destination data format.
END
  }
  summary: "Returns the permuted vector/tensor in the destination data format given the"
  description: <<END
one in the source data format.
END
}

op {
  graph_op_name: "StatelessRandomNormal"
  in_arg {
    name: "shape"
    description: <<END
The shape of the output tensor.
END
  }
  in_arg {
    name: "seed"
    description: <<END
2 seeds (shape [2]).
END
  }
  out_arg {
    name: "output"
    description: <<END
Random values with specified shape.
END
  }
  attr {
    name: "dtype"
    description: <<END
The type of the output.
END
  }
  summary: "Outputs deterministic pseudorandom values from a normal distribution."
  description: <<END
The generated values will have mean 0 and standard deviation 1.

The outputs are a deterministic function of `shape` and `seed`.
END
}

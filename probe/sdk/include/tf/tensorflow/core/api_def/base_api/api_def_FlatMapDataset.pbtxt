op {
  graph_op_name: "FlatMapDataset"
  visibility: HIDDE<PERSON>
  attr {
    name: "f"
    description: <<END
A function mapping elements of `input_dataset`, concatenated with
`other_arguments`, to a Dataset variant that contains elements matching
`output_types` and `output_shapes`.
END
  }
  summary: "Creates a dataset that applies `f` to the outputs of `input_dataset`."
  description: <<END
Unlike MapDataset, the `f` in FlatMapDataset is expected to return a
Dataset variant, and FlatMapDataset will flatten successive results
into a single Dataset.
END
}

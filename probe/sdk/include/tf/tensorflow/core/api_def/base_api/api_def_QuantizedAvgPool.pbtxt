op {
  graph_op_name: "QuantizedAvgPool"
  in_arg {
    name: "input"
    description: <<END
4-D with shape `[batch, height, width, channels]`.
END
  }
  in_arg {
    name: "min_input"
    description: <<END
The float value that the lowest quantized input value represents.
END
  }
  in_arg {
    name: "max_input"
    description: <<END
The float value that the highest quantized input value represents.
END
  }
  out_arg {
    name: "min_output"
    description: <<END
The float value that the lowest quantized output value represents.
END
  }
  out_arg {
    name: "max_output"
    description: <<END
The float value that the highest quantized output value represents.
END
  }
  attr {
    name: "ksize"
    description: <<END
The size of the window for each dimension of the input tensor.
The length must be 4 to match the number of dimensions of the input.
END
  }
  attr {
    name: "strides"
    description: <<END
The stride of the sliding window for each dimension of the input
tensor.  The length must be 4 to match the number of dimensions of the input.
END
  }
  attr {
    name: "padding"
    description: <<END
The type of padding algorithm to use.
END
  }
  summary: "Produces the average pool of the input tensor for quantized types."
}

op {
  graph_op_name: "EncodeJpeg"
  in_arg {
    name: "image"
    description: <<END
3-D with shape `[height, width, channels]`.
END
  }
  out_arg {
    name: "contents"
    description: <<END
0-D. JPEG-encoded image.
END
  }
  attr {
    name: "format"
    description: <<END
Per pixel image format.
END
  }
  attr {
    name: "quality"
    description: <<END
Quality of the compression from 0 to 100 (higher is better and slower).
END
  }
  attr {
    name: "progressive"
    description: <<END
If True, create a JPEG that loads progressively (coarse to fine).
END
  }
  attr {
    name: "optimize_size"
    description: <<END
If True, spend CPU/RAM to reduce size with no quality change.
END
  }
  attr {
    name: "chroma_downsampling"
    description: <<END
See http://en.wikipedia.org/wiki/Chroma_subsampling.
END
  }
  attr {
    name: "density_unit"
    description: <<END
Unit used to specify `x_density` and `y_density`:
pixels per inch (`'in'`) or centimeter (`'cm'`).
END
  }
  attr {
    name: "x_density"
    description: <<END
Horizontal pixels per density unit.
END
  }
  attr {
    name: "y_density"
    description: <<END
Vertical pixels per density unit.
END
  }
  attr {
    name: "xmp_metadata"
    description: <<END
If not empty, embed this XMP metadata in the image header.
END
  }
  summary: "JPEG-encode an image."
  description: <<END
`image` is a 3-D uint8 Tensor of shape `[height, width, channels]`.

The attr `format` can be used to override the color format of the encoded
output.  Values can be:

*   `''`: Use a default format based on the number of channels in the image.
*   `grayscale`: Output a grayscale JPEG image.  The `channels` dimension
    of `image` must be 1.
*   `rgb`: Output an RGB JPEG image. The `channels` dimension
    of `image` must be 3.

If `format` is not specified or is the empty string, a default format is picked
in function of the number of channels in `image`:

*   1: Output a grayscale image.
*   3: Output an RGB image.
END
}

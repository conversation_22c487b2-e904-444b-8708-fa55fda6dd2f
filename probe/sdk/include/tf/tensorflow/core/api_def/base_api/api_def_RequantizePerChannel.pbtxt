op {
  graph_op_name: "RequantizePerChannel"
  visibility : HIDDE<PERSON>
  in_arg {
    name: "input"
    description: <<END
The original input tensor.
END
  }
  in_arg {
    name: "input_min"
    description: <<END
The minimum value of the input tensor
END
  }
  in_arg {
    name: "input_max"
    description: <<END
The maximum value of the input tensor.
END
  }
  in_arg {
    name: "requested_output_min"
    description: <<END
The minimum value of the output tensor requested.
END
  }
  in_arg {
    name: "requested_output_max"
    description: <<END
The maximum value of the output tensor requested.
END
  }  
  out_arg {
    name: "output"
    description: <<END
Output tensor.
END
  }
  out_arg {
    name: "output_min"
    description: <<END
The minimum value of the final output tensor
END
  }
  out_arg {
    name: "output_max"
    description: <<END
The maximum value of the final output tensor.
END
  }
  attr {
    name: "T"
    description: <<END
The quantized type of input tensor that needs to be converted. 
END
  }
  attr {
    name: "out_type"
    description: <<END
The quantized type of output tensor that needs to be converted.
END
  }
  summary: "Requantizes input with min and max values known per channel."
}

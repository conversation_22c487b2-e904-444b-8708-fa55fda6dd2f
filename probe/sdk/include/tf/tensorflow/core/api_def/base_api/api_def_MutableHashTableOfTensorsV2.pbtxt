op {
  graph_op_name: "MutableHashTableOfTensorsV2"
  endpoint {
    name: "MutableHashTableOfTensors"
  }
  out_arg {
    name: "table_handle"
    description: <<END
Handle to a table.
END
  }
  attr {
    name: "container"
    description: <<END
If non-empty, this table is placed in the given container.
Otherwise, a default container is used.
END
  }
  attr {
    name: "shared_name"
    description: <<END
If non-empty, this table is shared under the given name across
multiple sessions.
END
  }
  attr {
    name: "key_dtype"
    description: <<END
Type of the table keys.
END
  }
  attr {
    name: "value_dtype"
    description: <<END
Type of the table values.
END
  }
  summary: "Creates an empty hash table."
  description: <<END
This op creates a mutable hash table, specifying the type of its keys and
values. Each value must be a vector. Data can be inserted into the table using
the insert operations. It does not support the initialization operation.
END
}

op {
  graph_op_name: "RandomShuffle"
  in_arg {
    name: "value"
    description: <<END
The tensor to be shuffled.
END
  }
  out_arg {
    name: "output"
    description: <<END
A tensor of same shape and type as `value`, shuffled along its first
dimension.
END
  }
  attr {
    name: "seed"
    description: <<END
If either `seed` or `seed2` are set to be non-zero, the random number
generator is seeded by the given seed.  Otherwise, it is seeded by a
random seed.
END
  }
  attr {
    name: "seed2"
    description: <<END
A second seed to avoid seed collision.
END
  }
  summary: "Randomly shuffles a tensor along its first dimension."
  description: <<END
  The tensor is shuffled along dimension 0, such that each `value[j]` is mapped
  to one and only one `output[i]`. For example, a mapping that might occur for a
  3x2 tensor is:

```
[[1, 2],       [[5, 6],
 [3, 4],  ==>   [1, 2],
 [5, 6]]        [3, 4]]
```
END
}

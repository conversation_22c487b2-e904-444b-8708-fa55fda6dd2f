op {
  graph_op_name: "NegTrain"
  in_arg {
    name: "w_in"
    description: <<END
input word embedding.
END
  }
  in_arg {
    name: "w_out"
    description: <<END
output word embedding.
END
  }
  in_arg {
    name: "examples"
    description: <<END
A vector of word ids.
END
  }
  in_arg {
    name: "labels"
    description: <<END
A vector of word ids.
END
  }
  attr {
    name: "vocab_count"
    description: <<END
Count of words in the vocabulary.
END
  }
  attr {
    name: "num_negative_samples"
    description: <<END
Number of negative samples per example.
END
  }
  summary: "Training via negative sampling."
}

op {
  graph_op_name: "AccumulatorTakeGradient"
  in_arg {
    name: "handle"
    description: <<END
The handle to an accumulator.
END
  }
  in_arg {
    name: "num_required"
    description: <<END
Number of gradients required before we return an aggregate.
END
  }
  out_arg {
    name: "average"
    description: <<END
The average of the accumulated gradients.
END
  }
  attr {
    name: "dtype"
    description: <<END
The data type of accumulated gradients. Needs to correspond to the type
of the accumulator.
END
  }
  summary: "Extracts the average gradient in the given ConditionalAccumulator."
  description: <<END
The op blocks until sufficient (i.e., more than num_required)
gradients have been accumulated.  If the accumulator has already
aggregated more than num_required gradients, it returns the average of
the accumulated gradients.  Also automatically increments the recorded
global_step in the accumulator by 1, and resets the aggregate to 0.
END
}

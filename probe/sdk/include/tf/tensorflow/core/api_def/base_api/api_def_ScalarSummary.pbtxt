op {
  graph_op_name: "ScalarSummary"
  in_arg {
    name: "tags"
    description: <<END
Tags for the summary.
END
  }
  in_arg {
    name: "values"
    description: <<END
Same shape as `tags.  Values for the summary.
END
  }
  out_arg {
    name: "summary"
    description: <<END
Scalar.  Serialized `Summary` protocol buffer.
END
  }
  summary: "Outputs a `Summary` protocol buffer with scalar values."
  description: <<END
The input `tags` and `values` must have the same shape.  The generated summary
has a summary value for each tag-value pair in `tags` and `values`.
END
}

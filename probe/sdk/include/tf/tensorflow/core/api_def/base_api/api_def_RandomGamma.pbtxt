op {
  graph_op_name: "RandomGamma"
  in_arg {
    name: "shape"
    description: <<END
1-D integer tensor. Shape of independent samples to draw from each
distribution described by the shape parameters given in alpha.
END
  }
  in_arg {
    name: "alpha"
    description: <<END
A tensor in which each scalar is a "shape" parameter describing the
associated gamma distribution.
END
  }
  out_arg {
    name: "output"
    description: <<END
A tensor with shape `shape + shape(alpha)`. Each slice
`[:, ..., :, i0, i1, ...iN]` contains the samples drawn for
`alpha[i0, i1, ...iN]`. The dtype of the output matches the dtype of alpha.
END
  }
  attr {
    name: "seed"
    description: <<END
If either `seed` or `seed2` are set to be non-zero, the random number
generator is seeded by the given seed.  Otherwise, it is seeded by a
random seed.
END
  }
  attr {
    name: "seed2"
    description: <<END
A second seed to avoid seed collision.
END
  }
  summary: "Outputs random values from the Gamma distribution(s) described by alpha."
  description: <<END
This op uses the algorithm by <PERSON><PERSON><PERSON> et al. to acquire samples via
transformation-rejection from pairs of uniform and normal random variables.
See http://dl.acm.org/citation.cfm?id=358414
END
}

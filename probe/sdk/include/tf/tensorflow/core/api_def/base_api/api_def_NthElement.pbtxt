op {
  graph_op_name: "NthElement"
  in_arg {
    name: "input"
    description: <<END
1-D or higher with last dimension at least `n+1`.
END
  }
  in_arg {
    name: "n"
    description: <<END
0-D. Position of sorted vector to select along the last dimension (along
each row for matrices). Valid range of n is `[0, input.shape[:-1])`
END
  }
  out_arg {
    name: "values"
    description: <<END
The `n`-th order statistic along each last dimensional slice.
END
  }
  attr {
    name: "reverse"
    description: <<END
When set to True, find the nth-largest value in the vector and vice
versa.
END
  }
  summary: "Finds values of the `n`-th order statistic for the last dimension."
  description: <<END
If the input is a vector (rank-1), finds the entries which is the nth-smallest
value in the vector and outputs their values as scalar tensor.

For matrices (resp. higher rank input), computes the entries which is the
nth-smallest value in each row (resp. vector along the last dimension). Thus,

    values.shape = input.shape[:-1]
END
}

op {
  graph_op_name: "SparseDenseCwiseAdd"
  in_arg {
    name: "sp_indices"
    description: <<END
2-D.  `N x R` matrix with the indices of non-empty values in a
SparseTensor, possibly not in canonical ordering.
END
  }
  in_arg {
    name: "sp_values"
    description: <<END
1-D.  `N` non-empty values corresponding to `sp_indices`.
END
  }
  in_arg {
    name: "sp_shape"
    description: <<END
1-D.  Shape of the input SparseTensor.
END
  }
  in_arg {
    name: "dense"
    description: <<END
`R`-D.  The dense Tensor operand.
END
  }
  out_arg {
    name: "output"
    description: <<END
1-D.  The `N` values that are operated on.
END
  }
  summary: "Adds up a SparseTensor and a dense Tensor, using these special rules:"
  description: <<END
(1) Broadcasts the dense side to have the same shape as the sparse side, if
    eligible;
(2) Then, only the dense values pointed to by the indices of the SparseTensor
    participate in the cwise addition.

By these rules, the result is a logical SparseTensor with exactly the same
indices and shape, but possibly with different non-zero values.  The output of
this Op is the resultant non-zero values.
END
}

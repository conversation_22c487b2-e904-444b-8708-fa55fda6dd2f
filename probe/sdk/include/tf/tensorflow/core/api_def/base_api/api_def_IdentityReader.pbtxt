op {
  graph_op_name: "IdentityReader"
  visibility: SKIP
  out_arg {
    name: "reader_handle"
    description: <<END
The handle to reference the Reader.
END
  }
  attr {
    name: "container"
    description: <<END
If non-empty, this reader is placed in the given container.
Otherwise, a default container is used.
END
  }
  attr {
    name: "shared_name"
    description: <<END
If non-empty, this reader is named in the given bucket
with this shared_name. Otherwise, the node name is used instead.
END
  }
  summary: "A Reader that outputs the queued work as both the key and value."
  description: <<END
To use, enqueue strings in a Queue.  ReaderRead will take the front
work string and output (work, work).
END
}

op {
  graph_op_name: "SparseToSparseSetOperation"
  in_arg {
    name: "set1_indices"
    description: <<END
2D `Tensor`, indices of a `SparseTensor`. Must be in row-major
order.
END
  }
  in_arg {
    name: "set1_values"
    description: <<END
1D `Tensor`, values of a `SparseTensor`. Must be in row-major
order.
END
  }
  in_arg {
    name: "set1_shape"
    description: <<END
1D `Tensor`, shape of a `SparseTensor`. `set1_shape[0...n-1]` must
be the same as `set2_shape[0...n-1]`, `set1_shape[n]` is the
max set size across `0...n-1` dimensions.
END
  }
  in_arg {
    name: "set2_indices"
    description: <<END
2D `Tensor`, indices of a `SparseTensor`. Must be in row-major
order.
END
  }
  in_arg {
    name: "set2_values"
    description: <<END
1D `Tensor`, values of a `SparseTensor`. Must be in row-major
order.
END
  }
  in_arg {
    name: "set2_shape"
    description: <<END
1D `Tensor`, shape of a `SparseTensor`. `set2_shape[0...n-1]` must
be the same as `set1_shape[0...n-1]`, `set2_shape[n]` is the
max set size across `0...n-1` dimensions.
END
  }
  out_arg {
    name: "result_indices"
    description: <<END
2D indices of a `SparseTensor`.
END
  }
  out_arg {
    name: "result_values"
    description: <<END
1D values of a `SparseTensor`.
END
  }
  out_arg {
    name: "result_shape"
    description: <<END
1D `Tensor` shape of a `SparseTensor`. `result_shape[0...n-1]` is
the same as the 1st `n-1` dimensions of `set1` and `set2`, `result_shape[n]`
is the max result set size across all `0...n-1` dimensions.
END
  }
  summary: "Applies set operation along last dimension of 2 `SparseTensor` inputs."
  description: <<END
See SetOperationOp::SetOperationFromContext for values of `set_operation`.

If `validate_indices` is `True`, `SparseToSparseSetOperation` validates the
order and range of `set1` and `set2` indices.

Input `set1` is a `SparseTensor` represented by `set1_indices`, `set1_values`,
and `set1_shape`. For `set1` ranked `n`, 1st `n-1` dimensions must be the same
as `set2`. Dimension `n` contains values in a set, duplicates are allowed but
ignored.

Input `set2` is a `SparseTensor` represented by `set2_indices`, `set2_values`,
and `set2_shape`. For `set2` ranked `n`, 1st `n-1` dimensions must be the same
as `set1`. Dimension `n` contains values in a set, duplicates are allowed but
ignored.

If `validate_indices` is `True`, this op validates the order and range of `set1`
and `set2` indices.

Output `result` is a `SparseTensor` represented by `result_indices`,
`result_values`, and `result_shape`. For `set1` and `set2` ranked `n`, this
has rank `n` and the same 1st `n-1` dimensions as `set1` and `set2`. The `nth`
dimension contains the result of `set_operation` applied to the corresponding
`[0...n-1]` dimension of `set`.
END
}

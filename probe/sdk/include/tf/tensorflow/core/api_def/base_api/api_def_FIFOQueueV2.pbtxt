op {
  graph_op_name: "FIFOQueueV2"
  endpoint {
    name: "FIFOQueue"
  }
  out_arg {
    name: "handle"
    description: <<END
The handle to the queue.
END
  }
  attr {
    name: "component_types"
    description: <<END
The type of each component in a value.
END
  }
  attr {
    name: "shapes"
    description: <<END
The shape of each component in a value. The length of this attr must
be either 0 or the same as the length of component_types. If the length of
this attr is 0, the shapes of queue elements are not constrained, and
only one element may be dequeued at a time.
END
  }
  attr {
    name: "capacity"
    description: <<END
The upper bound on the number of elements in this queue.
Negative numbers mean no limit.
END
  }
  attr {
    name: "container"
    description: <<END
If non-empty, this queue is placed in the given container.
Otherwise, a default container is used.
END
  }
  attr {
    name: "shared_name"
    description: <<END
If non-empty, this queue will be shared under the given name
across multiple sessions.
END
  }
  summary: "A queue that produces elements in first-in first-out order."
}

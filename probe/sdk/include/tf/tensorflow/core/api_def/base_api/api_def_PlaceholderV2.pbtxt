op {
  graph_op_name: "PlaceholderV2"
  out_arg {
    name: "output"
    description: <<END
A placeholder tensor that must be replaced using the feed mechanism.
END
  }
  attr {
    name: "dtype"
    description: <<END
The type of elements in the tensor.
END
  }
  attr {
    name: "shape"
    description: <<END
The shape of the tensor. The shape can be any partially-specified
shape.  To be unconstrained, pass in a shape with unknown rank.
END
  }
  summary: "A placeholder op for a value that will be fed into the computation."
  description: <<END
N.B. This operation will fail with an error if it is executed. It is
intended as a way to represent a value that will always be fed, and to
provide attrs that enable the fed value to be checked at runtime.
END
}

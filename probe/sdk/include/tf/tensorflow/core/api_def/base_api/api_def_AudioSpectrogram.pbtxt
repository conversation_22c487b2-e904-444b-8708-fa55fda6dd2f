op {
  graph_op_name: "AudioSpectrogram"
  in_arg {
    name: "input"
    description: <<END
Float representation of audio data.
END
  }
  out_arg {
    name: "spectrogram"
    description: <<END
3D representation of the audio frequencies as an image.
END
  }
  attr {
    name: "window_size"
    description: <<END
How wide the input window is in samples. For the highest efficiency
this should be a power of two, but other values are accepted.
END
  }
  attr {
    name: "stride"
    description: <<END
How widely apart the center of adjacent sample windows should be.
END
  }
  attr {
    name: "magnitude_squared"
    description: <<END
Whether to return the squared magnitude or just the
magnitude. Using squared magnitude can avoid extra calculations.
END
  }
  summary: "Produces a visualization of audio data over time."
  description: <<END
Spectrograms are a standard way of representing audio information as a series of
slices of frequency information, one slice for each window of time. By joining
these together into a sequence, they form a distinctive fingerprint of the sound
over time.

This op expects to receive audio data as an input, stored as floats in the range
-1 to 1, together with a window width in samples, and a stride specifying how
far to move the window between slices. From this it generates a three
dimensional output. The lowest dimension has an amplitude value for each
frequency during that time slice. The next dimension is time, with successive
frequency slices. The final dimension is for the channels in the input, so a
stereo audio input would have two here for example.

This means the layout when converted and saved as an image is rotated 90 degrees
clockwise from a typical spectrogram. Time is descending down the Y axis, and
the frequency decreases from left to right.

Each value in the result represents the square root of the sum of the real and
imaginary parts of an FFT on the current window of samples. In this way, the
lowest dimension represents the power of each frequency in the current window,
and adjacent windows are concatenated in the next dimension.

To get a more intuitive and visual look at what this operation does, you can run
tensorflow/examples/wav_to_spectrogram to read in an audio file and save out the
resulting spectrogram as a PNG image.
END
}

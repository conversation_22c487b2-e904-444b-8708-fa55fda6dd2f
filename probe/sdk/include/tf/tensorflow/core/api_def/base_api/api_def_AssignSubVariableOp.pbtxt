op {
  graph_op_name: "AssignSubVariableOp"
  in_arg {
    name: "resource"
    description: <<END
handle to the resource in which to store the variable.
END
  }
  in_arg {
    name: "value"
    description: <<END
the value by which the variable will be incremented.
END
  }
  attr {
    name: "dtype"
    description: <<END
the dtype of the value.
END
  }
  summary: "Subtracts a value from the current value of a variable."
  description: <<END
Any ReadVariableOp with a control dependency on this op is guaranteed to
see the decremented value or a subsequent newer one.
END
}

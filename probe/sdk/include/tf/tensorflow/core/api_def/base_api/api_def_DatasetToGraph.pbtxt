op {
  graph_op_name: "DatasetToGraph"
  visibility: HIDDEN
  in_arg {
    name: "input_dataset"
    description: <<END
A variant tensor representing the dataset to return the graph representation for.
END
  }
  out_arg {
    name: "graph"
    description: <<END
The graph representation of the dataset (as serialized GraphDef).
END
  }
  summary: "Returns a serialized GraphDef representing `input_dataset`."
  description: <<END
Returns a graph representation for `input_dataset`.
END
}

op {
  graph_op_name: "RetrieveTPUEmbeddingMDLAdagradLightParameters"
  visibility: HIDDEN
  out_arg {
    name: "parameters"
    description: <<END
Parameter parameters updated by the MDL Adagrad Light optimization algorithm.
END
  }
  out_arg {
    name: "accumulators"
    description: <<END
Parameter accumulators updated by the MDL Adagrad Light optimization algorithm.
END
  }
  out_arg {
    name: "weights"
    description: <<END
Parameter weights updated by the MDL Adagrad Light optimization algorithm.
END
  }
  out_arg {
    name: "benefits"
    description: <<END
Parameter benefits updated by the MDL Adagrad Light optimization algorithm.
END
  }
  summary: "Retrieve MDL Adagrad Light embedding parameters."
  description: <<END
An op that retrieves optimization parameters from embedding to host
memory. Must be preceded by a ConfigureTPUEmbeddingHost op that sets up
the correct embedding table configuration. For example, this op is
used to retrieve updated parameters before saving a checkpoint.
END
}

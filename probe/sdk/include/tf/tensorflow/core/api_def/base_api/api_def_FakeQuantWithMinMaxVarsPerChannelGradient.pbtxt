op {
  graph_op_name: "FakeQuantWithMinMaxVarsPerChannelGradient"
  in_arg {
    name: "gradients"
    description: <<END
Backpropagated gradients above the FakeQuantWithMinMaxVars operation,
shape one of: `[d]`, `[b, d]`,  `[b, h, w, d]`.
END
  }
  in_arg {
    name: "inputs"
    description: <<END
Values passed as inputs to the FakeQuantWithMinMaxVars operation, shape
  same as `gradients`.
min, max: Quantization interval, floats of shape `[d]`.
END
  }
  out_arg {
    name: "backprops_wrt_input"
    description: <<END
Backpropagated gradients w.r.t. inputs, shape same as
`inputs`:
  `gradients * (inputs >= min && inputs <= max)`.
END
  }
  out_arg {
    name: "backprop_wrt_min"
    description: <<END
Backpropagated gradients w.r.t. min parameter, shape `[d]`:
`sum_per_d(gradients * (inputs < min))`.
END
  }
  out_arg {
    name: "backprop_wrt_max"
    description: <<END
Backpropagated gradients w.r.t. max parameter, shape `[d]`:
`sum_per_d(gradients * (inputs > max))`.
END
  }
  attr {
    name: "num_bits"
    description: <<END
The bitwidth of the quantization; between 2 and 16, inclusive.
END
  }
  attr {
    name: "narrow_range"
    description: <<END
Whether to quantize into 2^num_bits - 1 distinct values.
END
  }
  summary: "Compute gradients for a FakeQuantWithMinMaxVarsPerChannel operation."
}

op {
  graph_op_name: "OutfeedDequeue"
  visibility: HIDDEN
  out_arg {
    name: "output"
    description: <<END
A tensor that will be read from the device outfeed.
END
  }
  attr {
    name: "dtype"
    description: <<END
The type of elements in the tensor.
END
  }
  attr {
    name: "shape"
    description: <<END
The shape of the tensor.
END
  }
  attr {
    name: "device_ordinal"
    description: <<END
The TPU device to use. This should be -1 when the Op
is running on a TPU device, and >= 0 when the Op is running on the CPU
device.
END
  }
  summary: "Retrieves a single tensor from the computation outfeed."
  description: <<END
This operation will block indefinitely until data is available.
END
}

op {
  graph_op_name: "Split"
  in_arg {
    name: "split_dim"
    rename_to: "axis"
    description: <<END
0-D.  The dimension along which to split.  Must be in the range
`[-rank(value), rank(value))`.
END
  }
  in_arg {
    name: "value"
    description: <<END
The tensor to split.
END
  }
  out_arg {
    name: "output"
    description: <<END
They are identically shaped tensors, whose shape matches that of `value`
except along `split_dim`, where their sizes are
`values.shape[split_dim] / num_split`.
END
  }
  attr {
    name: "num_split"
    description: <<END
The number of ways to split.  Must evenly divide
`value.shape[split_dim]`.
END
  }
  summary: "Splits a tensor into `num_split` tensors along one dimension."
}

op {
  graph_op_name: "DecodeAndCropJpeg"
  in_arg {
    name: "contents"
    description: <<END
0-D.  The JPEG-encoded image.
END
  }
  in_arg {
    name: "crop_window"
    description: <<END
1-D.  The crop window: [crop_y, crop_x, crop_height, crop_width].
<PERSON><PERSON>
  }
  out_arg {
    name: "image"
    description: <<END
3-D with shape `[height, width, channels]`..
END
  }
  attr {
    name: "channels"
    description: <<END
Number of color channels for the decoded image.
END
  }
  attr {
    name: "ratio"
    description: <<END
Downscaling ratio.
END
  }
  attr {
    name: "fancy_upscaling"
    description: <<END
If true use a slower but nicer upscaling of the
chroma planes (yuv420/422 only).
END
  }
  attr {
    name: "try_recover_truncated"
    description: <<END
If true try to recover an image from truncated input.
END
  }
  attr {
    name: "acceptable_fraction"
    description: <<END
The minimum required fraction of lines before a truncated
input is accepted.
END
  }
  attr {
    name: "dct_method"
    description: <<END
string specifying a hint about the algorithm used for
decompression.  Defaults to "" which maps to a system-specific
default.  Currently valid values are ["INTEGER_FAST",
"INTEGER_ACCURATE"].  The hint may be ignored (e.g., the internal
jpeg library changes to a version that does not have that specific
option.)
END
  }
  summary: "Decode and Crop a JPEG-encoded image to a uint8 tensor."
  description: <<END
The attr `channels` indicates the desired number of color channels for the
decoded image.

Accepted values are:

*   0: Use the number of channels in the JPEG-encoded image.
*   1: output a grayscale image.
*   3: output an RGB image.

If needed, the JPEG-encoded image is transformed to match the requested number
of color channels.

The attr `ratio` allows downscaling the image by an integer factor during
decoding.  Allowed values are: 1, 2, 4, and 8.  This is much faster than
downscaling the image later.


It is equivalent to a combination of decode and crop, but much faster by only
decoding partial jpeg image.
END
}

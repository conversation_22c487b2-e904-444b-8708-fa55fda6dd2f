op {
  graph_op_name: "IteratorFromStringHandle"
  in_arg {
    name: "string_handle"
    description: <<END
A string representation of the given handle.
END
  }
  out_arg {
    name: "resource_handle"
    description: <<END
A handle to an iterator resource.
END
  }
  attr {
    name: "output_types"
    description: <<END
If specified, defines the type of each tuple component in an
element produced by the resulting iterator.
END
  }
  attr {
    name: "output_shapes"
    description: <<END
If specified, defines the shape of each tuple component in an
element produced by the resulting iterator.
END
  }
  summary: "Converts the given string representing a handle to an iterator to a resource."
}

op {
  graph_op_name: "StringToHashBucketFast"
  in_arg {
    name: "input"
    description: <<END
The strings to assign a hash bucket.
END
  }
  out_arg {
    name: "output"
    description: <<END
A Tensor of the same shape as the input `string_tensor`.
END
  }
  attr {
    name: "num_buckets"
    description: <<END
The number of buckets.
END
  }
  summary: "Converts each string in the input Tensor to its hash mod by a number of buckets."
  description: <<END
The hash function is deterministic on the content of the string within the
process and will never change. However, it is not suitable for cryptography.
This function may be used when CPU time is scarce and inputs are trusted or
unimportant. There is a risk of adversaries constructing inputs that all hash
to the same bucket. To prevent this problem, use a strong hash function with
`tf.string_to_hash_bucket_strong`.
END
}

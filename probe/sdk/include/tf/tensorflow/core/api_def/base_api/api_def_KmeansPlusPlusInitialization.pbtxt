op {
  graph_op_name: "KmeansPlusPlusInitialization"
  visibility: HIDDE<PERSON>
  in_arg {
    name: "points"
    description: <<END
Matrix of shape (n, d). Rows are assumed to be input points.
END
  }
  in_arg {
    name: "num_to_sample"
    description: <<END
Scalar. The number of rows to sample. This value must not be larger than n.
END
  }
  in_arg {
    name: "seed"
    description: <<END
Scalar. Seed for initializing the random number generator.
END
  }
  in_arg {
    name: "num_retries_per_sample"
    description: <<END
Scalar. For each row that is sampled, this parameter
specifies the number of additional points to draw from the current
distribution before selecting the best. If a negative value is specified, a
heuristic is used to sample O(log(num_to_sample)) additional points.
END
  }
  out_arg {
    name: "samples"
    description: <<END
Matrix of shape (num_to_sample, d). The sampled rows.
END
  }
  summary: "Selects num_to_sample rows of input using the KMeans++ criterion."
  description: <<END
Rows of points are assumed to be input points. One row is selected at random.
Subsequent rows are sampled with probability proportional to the squared L2
distance from the nearest row selected thus far till num_to_sample rows have
been sampled.
END
}

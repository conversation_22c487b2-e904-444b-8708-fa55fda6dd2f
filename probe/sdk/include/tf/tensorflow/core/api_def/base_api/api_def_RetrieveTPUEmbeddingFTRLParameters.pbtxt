op {
  graph_op_name: "RetrieveTPUEmbeddingFTRLParameters"
  visibility: HIDDEN
  out_arg {
    name: "parameters"
    description: <<END
Parameter parameters updated by the FTRL optimization algorithm.
END
  }
  out_arg {
    name: "accumulators"
    description: <<END
Parameter accumulators updated by the FTRL optimization algorithm.
END
  }
  out_arg {
    name: "linears"
    description: <<END
Parameter linears updated by the FTRL optimization algorithm.
END
  }
  summary: "Retrieve FTRL embedding parameters."
  description: <<END
An op that retrieves optimization parameters from embedding to host
memory. Must be preceded by a ConfigureTPUEmbeddingHost op that sets up
the correct embedding table configuration. For example, this op is
used to retrieve updated parameters before saving a checkpoint.
END
}

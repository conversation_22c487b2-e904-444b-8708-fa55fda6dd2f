op {
  graph_op_name: "Assign"
  in_arg {
    name: "ref"
    description: <<END
Should be from a `Variable` node. May be uninitialized.
END
  }
  in_arg {
    name: "value"
    description: <<END
The value to be assigned to the variable.
END
  }
  out_arg {
    name: "output_ref"
    description: <<END
= Same as "ref".  Returned as a convenience for operations that want
to use the new value after the variable has been reset.
END
  }
  attr {
    name: "validate_shape"
    description: <<END
If true, the operation will validate that the shape
of 'value' matches the shape of the Tensor being assigned to.  If false,
'ref' will take on the shape of 'value'.
END
  }
  attr {
    name: "use_locking"
    description: <<END
If True, the assignment will be protected by a lock;
otherwise the behavior is undefined, but may exhibit less contention.
END
  }
  summary: "Update \'ref\' by assigning \'value\' to it."
  description: <<END
This operation outputs "ref" after the assignment is done.
This makes it easier to chain operations that need to use the reset value.
END
}

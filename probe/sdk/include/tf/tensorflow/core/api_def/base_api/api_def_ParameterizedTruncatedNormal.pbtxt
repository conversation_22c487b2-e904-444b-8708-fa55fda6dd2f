op {
  graph_op_name: "ParameterizedTruncatedNormal"
  in_arg {
    name: "shape"
    description: <<END
The shape of the output tensor. Batches are indexed by the 0th dimension.
END
  }
  in_arg {
    name: "means"
    description: <<END
The mean parameter of each batch.
END
  }
  in_arg {
    name: "stdevs"
    description: <<END
The standard deviation parameter of each batch. Must be greater than 0.
END
  }
  in_arg {
    name: "minvals"
    description: <<END
The minimum cutoff. May be -infinity.
END
  }
  in_arg {
    name: "maxvals"
    description: <<END
The maximum cutoff. May be +infinity, and must be more than the minval
for each batch.
END
  }
  out_arg {
    name: "output"
    description: <<END
A matrix of shape num_batches x samples_per_batch, filled with random
truncated normal values using the parameters for each row.
END
  }
  attr {
    name: "seed"
    description: <<END
If either `seed` or `seed2` are set to be non-zero, the random number
generator is seeded by the given seed.  Otherwise, it is seeded by a
random seed.
END
  }
  attr {
    name: "seed2"
    description: <<END
A second seed to avoid seed collision.
<PERSON><PERSON>
  }
  attr {
    name: "dtype"
    description: <<END
The type of the output.
END
  }
  summary: "Outputs random values from a normal distribution. The parameters may each be a"
  description: <<END
scalar which applies to the entire output, or a vector of length shape[0] which
stores the parameters for each batch.
END
}

op {
  graph_op_name: "StatefulStandardNormal"
  in_arg {
    name: "resource"
    description: <<END
The handle of the resource variable that stores the state of the RNG.
END
  }
  in_arg {
    name: "shape"
    description: <<END
The shape of the output tensor.
END
  }
  out_arg {
    name: "output"
    description: <<END
A tensor of the specified shape filled with random normal values.
END
  }
  attr {
    name: "dtype"
    description: <<END
The type of the output.
END
  }
  summary: "Outputs random values from a normal distribution."
  description: <<END
The generated values will have mean 0 and standard deviation 1.
END
}

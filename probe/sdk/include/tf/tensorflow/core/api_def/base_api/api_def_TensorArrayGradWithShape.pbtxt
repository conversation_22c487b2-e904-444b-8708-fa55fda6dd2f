op {
  graph_op_name: "TensorArrayGradWithShape"
  endpoint {
    name: "TensorArrayGradWithShape"
  }
  in_arg {
    name: "handle"
    description: <<END
The handle to the forward TensorArray.
END
  }
  in_arg {
    name: "flow_in"
    description: <<END
A float scalar that enforces proper chaining of operations.
END
  }
  in_arg {
    name: "shape_to_prepend"
    description: <<END
An int32 vector representing a shape. Elements in the gradient accumulator will
have shape which is this shape_to_prepend value concatenated with shape of the
elements in the TensorArray corresponding to the input handle.
END
  }
  attr {
    name: "source"
    description: <<END
The gradient source string, used to decide which gradient TensorArray
to return.
END
  }
  summary: "Creates a TensorArray for storing multiple gradients of values in the given handle."
  description: <<END
Similar to TensorArrayGradV3. However it creates an accumulator with an
expanded shape compared to the input TensorArray whose gradient is being
computed. This enables multiple gradients for the same TensorArray to be
calculated using the same accumulator.
END
}

op {
  graph_op_name: "TensorSummaryV2"
  in_arg {
    name: "tag"
    description: <<END
A string attached to this summary. Used for organization in TensorBoard.
END
  }
  in_arg {
    name: "tensor"
    description: <<END
A tensor to serialize.
END
  }
  in_arg {
    name: "serialized_summary_metadata"
    description: <<END
A serialized SummaryMetadata proto. Contains plugin
data.
END
  }
  summary: "Outputs a `Summary` protocol buffer with a tensor and per-plugin data."
}

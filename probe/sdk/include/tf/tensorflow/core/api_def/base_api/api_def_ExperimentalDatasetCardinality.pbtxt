op {
  graph_op_name: "ExperimentalDatasetCardinality"
  visibility: HIDDE<PERSON>
  in_arg {
    name: "input_dataset"
    description: <<END
A variant tensor representing the dataset to return cardinality for.
END
  }
  out_arg {
    name: "cardinality"
    description: <<END
The cardinality of `input_dataset`. Named constants are used to represent
infinite and unknown cardinality.
END
  }
  summary: "Returns the cardinality of `input_dataset`."
  description: <<END
Returns the cardinality of `input_dataset`.
END
}

op {
  graph_op_name: "RetrieveTPUEmbeddingRMSPropParameters"
  visibility: HIDDEN
  out_arg {
    name: "parameters"
    description: <<END
Parameter parameters updated by the RMSProp optimization algorithm.
END
  }
  out_arg {
    name: "ms"
    description: <<END
Parameter ms updated by the RMSProp optimization algorithm.
END
  }
  out_arg {
    name: "mom"
    description: <<END
Parameter mom updated by the RMSProp optimization algorithm.
END
  }
  summary: "Retrieve RMSProp embedding parameters."
  description: <<END
An op that retrieves optimization parameters from embedding to host
memory. Must be preceded by a ConfigureTPUEmbeddingHost op that sets up
the correct embedding table configuration. For example, this op is
used to retrieve updated parameters before saving a checkpoint.
END
}

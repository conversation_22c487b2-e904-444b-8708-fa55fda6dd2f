op {
  graph_op_name: "TryRpc"
  in_arg {
    name: "address"
    description: <<END
`0-D` or `1-D`.  The address (i.e. host_name:port) of the RPC server.
If this tensor has more than 1 element, then multiple parallel rpc requests
are sent.  This argument broadcasts with `method` and `request`.
END
  }
  in_arg {
    name: "method"
    description: <<END
`0-D` or `1-D`.  The method address on the RPC server.
If this tensor has more than 1 element, then multiple parallel rpc requests
are sent.  This argument broadcasts with `address` and `request`.
END
  }
  in_arg {
    name: "request"
    description: <<END
`0-D` or `1-D`.  Serialized proto strings: the rpc request argument.
If this tensor has more than 1 element, then multiple parallel rpc requests
are sent.  This argument broadcasts with `address` and `method`.
END
  }
  out_arg {
    name: "response"
    description: <<END
Same shape as `request`. Serialized proto strings: the rpc responses.
END
  }
  out_arg {
    name: "status_code"
    description: <<END
Same shape as `request`.  Values correspond to tensorflow Status enum codes.
END
  }
  out_arg {
    name: "status_message"
    description: <<END
Same shape as `request`.  Values correspond to Status messages
returned from the RPC calls.
END
  }
  attr {
    name: "protocol"
    description: <<END
RPC protocol to use.  Empty string means use the default protocol.
Options include 'grpc'.
END
  }
  attr {
    name: "fail_fast"
    description: <<END
`boolean`. If `true` (default), then failures to connect
(i.e., the server does not immediately respond) cause an RPC failure.
END
  }
  attr {
    name: "timeout_in_ms"
    description: <<END
`int`. If `0` (default), then the kernel will run the RPC
request and only time out if the RPC deadline passes or the session times out.
If this value is greater than `0`, then the op will raise an exception if
the RPC takes longer than `timeout_in_ms`.
END
  }
  summary: <<END
Perform batches of RPC requests.
END
  description: <<END
This op asynchronously performs either a single RPC request, or a batch
of requests.  RPC requests are defined by three main parameters:

  - `address` (the host+port or BNS address of the request)
  - `method` (the method name for the request)
  - `request` (the serialized proto string, or vector of strings,
     of the RPC request argument).

For example, if you have an RPC service running on port localhost:2345,
and its interface is configured with the following proto declaration:

```
service MyService {
  rpc MyMethod(MyRequestProto) returns (MyResponseProto) {
  }
};
```

then call this op with arguments:

```
address = "localhost:2345"
method = "MyService/MyMethod"
```

The `request` tensor is a string tensor representing serialized `MyRequestProto`
strings; and the output string tensor `response` will have the same shape
and contain (upon successful completion) corresponding serialized
`MyResponseProto` strings.

For example, to send a single, empty, `MyRequestProto`, call
this op with `request = ""`.  To send 5 **parallel** empty requests,
call this op with `request = ["", "", "", "", ""]`.

More generally, one can create a batch of `MyRequestProto` serialized protos
from regular batched tensors using the `encode_proto` op, and convert
the response `MyResponseProto` serialized protos to batched tensors
using the `decode_proto` op.

**NOTE** Working with serialized proto strings is faster than instantiating
actual proto objects in memory, so no performance degradation is expected
compared to writing custom kernels for this workflow.

Unlike the standard `Rpc` op, if the connection fails or the remote worker
returns an error status, this op does **not** reraise the exception.
Instead, the `status_code` and `status_message` entry for the corresponding RPC
call is set with the error returned from the RPC call.  The `response` tensor
will contain valid response values for those minibatch entries whose RPCs did
not fail; the rest of the entries will have empty strings.
END
}

op {
  graph_op_name: "EditDistance"
  in_arg {
    name: "hypothesis_indices"
    description: <<END
The indices of the hypothesis list SparseTensor.
This is an N x R int64 matrix.
END
  }
  in_arg {
    name: "hypothesis_values"
    description: <<END
The values of the hypothesis list SparseTensor.
This is an N-length vector.
END
  }
  in_arg {
    name: "hypothesis_shape"
    description: <<END
The shape of the hypothesis list SparseTensor.
This is an R-length vector.
END
  }
  in_arg {
    name: "truth_indices"
    description: <<END
The indices of the truth list SparseTensor.
This is an M x R int64 matrix.
END
  }
  in_arg {
    name: "truth_values"
    description: <<END
The values of the truth list SparseTensor.
This is an M-length vector.
END
  }
  in_arg {
    name: "truth_shape"
    description: <<END
truth indices, vector.
END
  }
  out_arg {
    name: "output"
    description: <<END
A dense float tensor with rank R - 1.

For the example input:

    // hypothesis represents a 2x1 matrix with variable-length values:
    //   (0,0) = ["a"]
    //   (1,0) = ["b"]
    hypothesis_indices = [[0, 0, 0],
                          [1, 0, 0]]
    hypothesis_values = ["a", "b"]
    hypothesis_shape = [2, 1, 1]

    // truth represents a 2x2 matrix with variable-length values:
    //   (0,0) = []
    //   (0,1) = ["a"]
    //   (1,0) = ["b", "c"]
    //   (1,1) = ["a"]
    truth_indices = [[0, 1, 0],
                     [1, 0, 0],
                     [1, 0, 1],
                     [1, 1, 0]]
    truth_values = ["a", "b", "c", "a"]
    truth_shape = [2, 2, 2]
    normalize = true

The output will be:

    // output is a 2x2 matrix with edit distances normalized by truth lengths.
    output = [[inf, 1.0],  // (0,0): no truth, (0,1): no hypothesis
              [0.5, 1.0]]  // (1,0): addition, (1,1): no hypothesis
END
  }
  attr {
    name: "normalize"
    description: <<END
boolean (if true, edit distances are normalized by length of truth).

The output is:
END
  }
  summary: "Computes the (possibly normalized) Levenshtein Edit Distance."
  description: <<END
The inputs are variable-length sequences provided by SparseTensors
  (hypothesis_indices, hypothesis_values, hypothesis_shape)
and
  (truth_indices, truth_values, truth_shape).

The inputs are:
END
}

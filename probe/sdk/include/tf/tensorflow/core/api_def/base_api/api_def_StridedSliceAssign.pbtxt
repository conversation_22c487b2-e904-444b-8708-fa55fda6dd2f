op {
  graph_op_name: "StridedSliceAssign"
  summary: "Assign `value` to the sliced l-value reference of `ref`."
  description: <<END
The values of `value` are assigned to the positions in the variable
`ref` that are selected by the slice parameters. The slice parameters
`begin`, `end`, `strides`, etc. work exactly as in `StridedSlice`.

NOTE this op currently does not support broadcasting and so `value`'s
shape must be exactly the shape produced by the slice of `ref`.
END
}

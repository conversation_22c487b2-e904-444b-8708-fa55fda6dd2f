op {
  graph_op_name: "FakeParam"
  visibility: SKIP
  out_arg {
    name: "output"
    description: <<END
    \"Fake\" output value. This should not be consumed by another op.
END
  }
  attr { name: "dtype"  description: "The type of the output." }
  attr {
    name: "shape"
    description: <<END
    The purported shape of the output. This is only used for shape inference;
    the output will not necessarily have this shape. Can be a partial shape.
END
  }
  summary: <<END
  This op is used as a placeholder in If branch functions. It doesn't provide a
  valid output when run, so must either be removed (e.g. replaced with a
  function input) or guaranteed not to be used (e.g. if mirroring an
  intermediate output needed for the gradient computation of the other branch).
END
}

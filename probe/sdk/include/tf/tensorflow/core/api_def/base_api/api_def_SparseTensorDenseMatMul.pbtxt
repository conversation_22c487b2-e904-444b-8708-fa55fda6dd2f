op {
  graph_op_name: "SparseTensorDenseMatMul"
  in_arg {
    name: "a_indices"
    description: <<END
2-D.  The `indices` of the `SparseTensor`, size `[nnz, 2]` Matrix.
END
  }
  in_arg {
    name: "a_values"
    description: <<END
1-D.  The `values` of the `SparseTensor`, size `[nnz]` Vector.
END
  }
  in_arg {
    name: "a_shape"
    description: <<END
1-D.  The `shape` of the `SparseTensor`, size `[2]` Vector.
END
  }
  in_arg {
    name: "b"
    description: <<END
2-D.  A dense Matrix.
END
  }
  attr {
    name: "adjoint_a"
    description: <<END
Use the adjoint of A in the matrix multiply.  If A is complex, this
is transpose(conj(A)).  Otherwise it's transpose(A).
END
  }
  attr {
    name: "adjoint_b"
    description: <<END
Use the adjoint of B in the matrix multiply.  If B is complex, this
is transpose(conj(B)).  Otherwise it's transpose(B).
END
  }
  summary: "Multiply SparseTensor (of rank 2) \"A\" by dense matrix \"B\"."
  description: <<END
No validity checking is performed on the indices of A.  However, the following
input format is recommended for optimal behavior:

if adjoint_a == false:
  A should be sorted in lexicographically increasing order.  Use SparseReorder
  if you're not sure.
if adjoint_a == true:
  A should be sorted in order of increasing dimension 1 (i.e., "column major"
  order instead of "row major" order).
END
}

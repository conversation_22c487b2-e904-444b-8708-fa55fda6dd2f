op {
  graph_op_name: "UniqueV2"
  in_arg {
    name: "x"
    description: <<END
A `Tensor`.
END
  }
  in_arg {
    name: "axis"
    description: <<END
A `Tensor` of type `int32` (default: None). The axis of the Tensor to
find the unique elements.
END
  }
  out_arg {
    name: "y"
    description: <<END
A `Tensor`. Unique elements along the `axis` of `Tensor` x.
END
  }
  out_arg {
    name: "idx"
    description: <<END
A 1-D Tensor. Has the same type as x that contains the index of each
value of x in the output y.
END
  }
  summary: "Finds unique elements along an axis of a tensor."
  description: <<END
This operation either returns a tensor `y` containing unique elements
along the `axis` of a tensor. The returned unique elements is sorted
in the same order as they occur along `axis` in `x`.
This operation also returns a tensor `idx` that is the same size as
the number of the elements in `x` along the `axis` dimension. It
contains the index in the unique output `y`.
In other words, for an `1-D` tensor `x` with `axis = None:

`y[idx[i]] = x[i] for i in [0, 1,...,rank(x) - 1]`

For example:

```
# tensor 'x' is [1, 1, 2, 4, 4, 4, 7, 8, 8]
y, idx = unique(x)
y ==> [1, 2, 4, 7, 8]
idx ==> [0, 0, 1, 2, 2, 2, 3, 4, 4]
```

For an `2-D` tensor `x` with `axis = 0`:

```
# tensor 'x' is [[1, 0, 0],
#                [1, 0, 0],
#                [2, 0, 0]]
y, idx = unique(x, axis=0)
y ==> [[1, 0, 0],
       [2, 0, 0]]
idx ==> [0, 0, 1]
```

For an `2-D` tensor `x` with `axis = 1`:

```
# tensor 'x' is [[1, 0, 0],
#                [1, 0, 0],
#                [2, 0, 0]]
y, idx = unique(x, axis=1)
y ==> [[1, 0],
       [1, 0],
       [2, 0]]
idx ==> [0, 1, 1]
```
END
}

op {
  graph_op_name: "RandomCrop"
  in_arg {
    name: "image"
    description: <<END
3-D of shape `[height, width, channels]`.
END
  }
  in_arg {
    name: "size"
    description: <<END
1-D of length 2 containing: `crop_height`, `crop_width`..
<PERSON><PERSON>
  }
  out_arg {
    name: "output"
    description: <<END
3-D of shape `[crop_height, crop_width, channels].`
END
  }
  attr {
    name: "seed"
    description: <<END
If either seed or seed2 are set to be non-zero, the random number
generator is seeded by the given seed.  Otherwise, it is seeded by a
random seed.
END
  }
  attr {
    name: "seed2"
    description: <<END
An second seed to avoid seed collision.
END
  }
  summary: "Randomly crop `image`."
  description: <<END
`size` is a 1-D int64 tensor with 2 elements representing the crop height and
width.  The values must be non negative.

This Op picks a random location in `image` and crops a `height` by `width`
rectangle from that location.  The random location is picked so the cropped
area will fit inside the original image.
END
}

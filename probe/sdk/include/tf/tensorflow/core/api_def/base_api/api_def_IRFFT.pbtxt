op {
  graph_op_name: "IRFFT"
  in_arg {
    name: "input"
    description: <<END
A complex64 tensor.
END
  }
  in_arg {
    name: "fft_length"
    description: <<END
An int32 tensor of shape [1]. The FFT length.
END
  }
  out_arg {
    name: "output"
    description: <<END
A float32 tensor of the same rank as `input`. The inner-most
  dimension of `input` is replaced with the `fft_length` samples of its inverse
  1D Fourier transform.

@compatibility(numpy)
Equivalent to np.fft.irfft
@end_compatibility
END
  }
  summary: "Inverse real-valued fast Fourier transform."
  description: <<END
Computes the inverse 1-dimensional discrete Fourier transform of a real-valued
signal over the inner-most dimension of `input`.

The inner-most dimension of `input` is assumed to be the result of `RFFT`: the
`fft_length / 2 + 1` unique components of the DFT of a real-valued signal. If
`fft_length` is not provided, it is computed from the size of the inner-most
dimension of `input` (`fft_length = 2 * (inner - 1)`). If the FFT length used to
compute `input` is odd, it should be provided since it cannot be inferred
properly.

Along the axis `IRFFT` is computed on, if `fft_length / 2 + 1` is smaller
than the corresponding dimension of `input`, the dimension is cropped. If it is
larger, the dimension is padded with zeros.
E<PERSON>
}

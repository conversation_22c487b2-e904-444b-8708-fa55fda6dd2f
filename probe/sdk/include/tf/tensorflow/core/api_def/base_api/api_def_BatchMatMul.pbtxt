op {
  graph_op_name: "BatchMatMul"
  in_arg {
    name: "x"
    description: <<END
2-D or higher with shape `[..., r_x, c_x]`.
END
  }
  in_arg {
    name: "y"
    description: <<END
2-D or higher with shape `[..., r_y, c_y]`.
END
  }
  out_arg {
    name: "output"
    description: <<END
3-D or higher with shape `[..., r_o, c_o]`
END
  }
  attr {
    name: "adj_x"
    description: <<END
If `True`, adjoint the slices of `x`. Defaults to `False`.
END
  }
  attr {
    name: "adj_y"
    description: <<END
If `True`, adjoint the slices of `y`. Defaults to `False`.
END
  }
  summary: "Multiplies slices of two tensors in batches."
  description: <<END
Multiplies all slices of `Tensor` `x` and `y` (each slice can be
viewed as an element of a batch), and arranges the individual results
in a single output tensor of the same batch size. Each of the
individual slices can optionally be adjointed (to adjoint a matrix
means to transpose and conjugate it) before multiplication by setting
the `adj_x` or `adj_y` flag to `True`, which are by default `False`.

The input tensors `x` and `y` are 2-D or higher with shape `[..., r_x, c_x]`
and `[..., r_y, c_y]`.

The output tensor is 2-D or higher with shape `[..., r_o, c_o]`, where:

    r_o = c_x if adj_x else r_x
    c_o = r_y if adj_y else c_y

It is computed as:

    output[..., :, :] = matrix(x[..., :, :]) * matrix(y[..., :, :])
END
}

op {
  graph_op_name: "ComputeAccidentalHits"
  in_arg {
    name: "true_classes"
    description: <<END
The true_classes output of UnpackSparseLabels.
END
  }
  in_arg {
    name: "sampled_candidates"
    description: <<END
The sampled_candidates output of CandidateSampler.
END
  }
  out_arg {
    name: "indices"
    description: <<END
A vector of indices corresponding to rows of true_candidates.
END
  }
  out_arg {
    name: "ids"
    description: <<END
A vector of IDs of positions in sampled_candidates that match a true_label
for the row with the corresponding index in indices.
END
  }
  out_arg {
    name: "weights"
    description: <<END
A vector of the same length as indices and ids, in which each element
is -FLOAT_MAX.
END
  }
  attr {
    name: "num_true"
    description: <<END
Number of true labels per context.
END
  }
  attr {
    name: "seed"
    description: <<END
If either seed or seed2 are set to be non-zero, the random number
generator is seeded by the given seed.  Otherwise, it is seeded by a
random seed.
END
  }
  attr {
    name: "seed2"
    description: <<END
An second seed to avoid seed collision.
END
  }
  summary: "Computes the ids of the positions in sampled_candidates that match true_labels."
  description: <<END
When doing log-odds NCE, the result of this op should be passed through a
SparseToDense op, then added to the logits of the sampled candidates. This has
the effect of 'removing' the sampled labels that match the true labels by
making the classifier sure that they are sampled labels.
END
}

op {
  graph_op_name: "Unpack"
  endpoint {
    name: "Unstack"
  }
  in_arg {
    name: "value"
    description: <<END
1-D or higher, with `axis` dimension size equal to `num`.
E<PERSON>
  }
  out_arg {
    name: "output"
    description: <<END
The list of tensors unpacked from `value`.
END
  }
  attr {
    name: "axis"
    description: <<END
Dimension along which to unpack.  Negative values wrap around, so the
valid range is `[-R, R)`.
END
  }
  summary: "Unpacks a given dimension of a rank-`R` tensor into `num` rank-`(R-1)` tensors."
  description: <<END
Unpacks `num` tensors from `value` by chipping it along the `axis` dimension.
For example, given a tensor of shape `(A, B, C, D)`;

If `axis == 0` then the i'th tensor in `output` is the slice `value[i, :, :, :]`
  and each tensor in `output` will have shape `(B, C, D)`. (Note that the
  dimension unpacked along is gone, unlike `split`).

If `axis == 1` then the i'th tensor in `output` is the slice `value[:, i, :, :]`
  and each tensor in `output` will have shape `(A, C, D)`.
Etc.

This is the opposite of `pack`.
END
}

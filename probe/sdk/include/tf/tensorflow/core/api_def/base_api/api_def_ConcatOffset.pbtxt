op {
  graph_op_name: "ConcatOffset"
  visibility: SKIP
  in_arg {
    name: "concat_dim"
    description: <<END
The dimension along which to concatenate.
END
  }
  in_arg {
    name: "shape"
    description: <<END
The `N` int32 vectors representing shape of tensors being concatenated.
END
  }
  out_arg {
    name: "offset"
    description: <<END
The `N` int32 vectors representing the starting offset
of input tensors within the concatenated output.
END
  }
  summary: "Computes offsets of concat inputs within its output."
  description: <<END
For example:

```
# 'x' is [2, 2, 7]
# 'y' is [2, 3, 7]
# 'z' is [2, 5, 7]
concat_offset(2, [x, y, z]) => [0, 0, 0], [0, 2, 0], [0, 5, 0]
```

This is typically used by gradient computations for a concat operation.
END
}

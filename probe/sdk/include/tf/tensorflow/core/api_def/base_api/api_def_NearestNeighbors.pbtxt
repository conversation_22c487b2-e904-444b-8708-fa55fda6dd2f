op {
  graph_op_name: "NearestNeighbors"
  visibility: HIDDEN
  in_arg {
    name: "points"
    description: <<END
Matrix of shape (n, d). Rows are assumed to be input points.
END
  }
  in_arg {
    name: "centers"
    description: <<END
Matrix of shape (m, d). Rows are assumed to be centers.
END
  }
  in_arg {
    name: "k"
    description: <<END
Number of nearest centers to return for each point. If k is larger than m, then
only m centers are returned.
END
  }
  out_arg {
    name: "nearest_center_indices"
    description: <<END
Matrix of shape (n, min(m, k)). Each row contains the indices of the centers
closest to the corresponding point, ordered by increasing distance.
END
  }
  out_arg {
    name: "nearest_center_distances"
    description: <<END
Matrix of shape (n, min(m, k)). Each row contains the squared L2 distance to the
corresponding center in nearest_center_indices.
END
  }
  summary: "Selects the k nearest centers for each point."
  description: <<END
Rows of points are assumed to be input points. Rows of centers are assumed to be
the list of candidate centers. For each point, the k centers that have least L2
distance to it are computed.
END
}

op {
  graph_op_name: "InfeedEnqueuePrelinearizedBuffer"
  visibility: HIDDEN
  in_arg {
    name: "input"
    description: <<END
A variant tensor representing linearized output.
END
  }
  attr {
    name: "device_ordinal"
    description: <<END
The TPU device to use. This should be -1 when the Op is running on a TPU device
and = 0 when the Op is running on the CPU device.
END
  }
  summary: "An op which enqueues prelinearized buffer into TPU infeed."
}

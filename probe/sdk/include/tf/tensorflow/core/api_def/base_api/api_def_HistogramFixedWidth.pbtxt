op {
  graph_op_name: "HistogramFixedWidth"
  in_arg {
    name: "values"
    description: <<END
Numeric `Tensor`.
END
  }
  in_arg {
    name: "value_range"
    description: <<END
Shape [2] `Tensor` of same `dtype` as `values`.
values <= value_range[0] will be mapped to hist[0],
values >= value_range[1] will be mapped to hist[-1].
END
  }
  in_arg {
    name: "nbins"
    description: <<END
<PERSON> `int32 Tensor`.  Number of histogram bins.
END
  }
  out_arg {
    name: "out"
    description: <<END
A 1-D `Tensor` holding histogram of values.
END
  }
  summary: "Return histogram of values."
  description: <<END
Given the tensor `values`, this operation returns a rank 1 histogram counting
the number of entries in `values` that fall into every bin.  The bins are
equal width and determined by the arguments `value_range` and `nbins`.

```python
# Bins will be:  (-inf, 1), [1, 2), [2, 3), [3, 4), [4, inf)
nbins = 5
value_range = [0.0, 5.0]
new_values = [-1.0, 0.0, 1.5, 2.0, 5.0, 15]

with tf.get_default_session() as sess:
  hist = tf.histogram_fixed_width(new_values, value_range, nbins=5)
  variables.global_variables_initializer().run()
  sess.run(hist) => [2, 1, 1, 0, 2]
```
END
}

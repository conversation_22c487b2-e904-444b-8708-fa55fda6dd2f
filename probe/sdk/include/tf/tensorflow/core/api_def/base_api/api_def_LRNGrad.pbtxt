op {
  graph_op_name: "LRNGrad"
  visibility: HIDDE<PERSON>
  in_arg {
    name: "input_grads"
    description: <<END
4-D with shape `[batch, height, width, channels]`.
END
  }
  in_arg {
    name: "input_image"
    description: <<END
4-D with shape `[batch, height, width, channels]`.
END
  }
  in_arg {
    name: "output_image"
    description: <<END
4-D with shape `[batch, height, width, channels]`.
END
  }
  out_arg {
    name: "output"
    description: <<END
The gradients for LRN.
END
  }
  attr {
    name: "depth_radius"
    description: <<END
A depth radius.
END
  }
  attr {
    name: "bias"
    description: <<END
An offset (usually > 0 to avoid dividing by 0).
END
  }
  attr {
    name: "alpha"
    description: <<END
A scale factor, usually positive.
END
  }
  attr {
    name: "beta"
    description: <<END
An exponent.
END
  }
  summary: "Gradients for Local Response Normalization."
}

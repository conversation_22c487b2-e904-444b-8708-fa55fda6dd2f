op {
  graph_op_name: "ExperimentalGroupByReducerDataset"
  visibility: HIDDE<PERSON>
  in_arg {
    name: "input_dataset"
    description: <<END
A variant tensor representing the input dataset.
END
  }
  in_arg {
    name: "key_func_other_arguments"
    description: <<END
A list of tensors, typically values that were captured when
building a closure for `key_func`.
END
  }
  attr {
    name: "key_func"
    description: <<END
A function mapping an element of `input_dataset`, concatenated
with `key_func_other_arguments` to a scalar value of type DT_INT64.
END
  }
  in_arg {
    name: "init_func_other_arguments"
    description: <<END
A list of tensors, typically values that were captured when
building a closure for `init_func`.
END
  }
  attr {
    name: "init_func"
    description: <<END
A function mapping a key of type DT_INT64, concatenated with
`init_func_other_arguments` to the initial reducer state.
END
  }
  in_arg {
    name: "reduce_func_other_arguments"
    description: <<END
A list of tensors, typically values that were captured when
building a closure for `reduce_func`.
<PERSON><PERSON>
  }
  attr {
    name: "reduce_func"
    description: <<END
A function mapping the current reducer state and an element of `input_dataset`,
concatenated with `reduce_func_other_arguments` to a new reducer state.
E<PERSON>
  }
  in_arg {
    name: "finalize_func_other_arguments"
    description: <<END
A list of tensors, typically values that were captured when
building a closure for `finalize_func`.
END
  }
  attr {
    name: "finalize_func"
    description: <<END
A function mapping the final reducer state to an output element.
END
  }
  summary: "Creates a dataset that computes a group-by on `input_dataset`."
  description: <<END
Creates a dataset that computes a group-by on `input_dataset`.
END
}

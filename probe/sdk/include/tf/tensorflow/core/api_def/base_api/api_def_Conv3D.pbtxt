op {
  graph_op_name: "Conv3D"
  in_arg {
    name: "input"
    description: <<E<PERSON>
Shape `[batch, in_depth, in_height, in_width, in_channels]`.
END
  }
  in_arg {
    name: "filter"
    description: <<END
Shape `[filter_depth, filter_height, filter_width, in_channels,
out_channels]`. `in_channels` must match between `input` and `filter`.
END
  }
  attr {
    name: "strides"
    description: <<END
1-D tensor of length 5. The stride of the sliding window for each
dimension of `input`. Must have `strides[0] = strides[4] = 1`.
END
  }
  attr {
    name: "padding"
    description: <<END
The type of padding algorithm to use.
END
  }
  attr {
    name: "data_format"
    description: <<END
The data format of the input and output data. With the
default format "NDHWC", the data is stored in the order of:
    [batch, in_depth, in_height, in_width, in_channels].
Alternatively, the format could be "NCDHW", the data storage order is:
    [batch, in_channels, in_depth, in_height, in_width].
END
  }
  attr {
    name: "dilations"
    description: <<END
1-D tensor of length 5.  The dilation factor for each dimension of
`input`. If set to k > 1, there will be k-1 skipped cells between each
filter element on that dimension. The dimension order is determined by the
value of `data_format`, see above for details. Dilations in the batch and
depth dimensions must be 1.
END
  }
  summary: "Computes a 3-D convolution given 5-D `input` and `filter` tensors."
  description: <<END
In signal processing, cross-correlation is a measure of similarity of
two waveforms as a function of a time-lag applied to one of them. This
is also known as a sliding dot product or sliding inner-product.

Our Conv3D implements a form of cross-correlation.
END
}

op {
  graph_op_name: "StackV2"
  visibility: SKIP
  in_arg {
    name: "max_size"
    description: <<END
The maximum size of the stack if non-negative. If negative, the stack
size is unlimited.
END
  }
  out_arg {
    name: "handle"
    description: <<END
The handle to the stack.
END
  }
  attr {
    name: "elem_type"
    description: <<END
The type of the elements on the stack.
END
  }
  attr {
    name: "stack_name"
    description: <<END
Overrides the name used for the temporary stack resource. Default
value is the name of the 'Stack' op (which is guaranteed unique).
END
  }
  summary: "A stack that produces elements in first-in last-out order."
}

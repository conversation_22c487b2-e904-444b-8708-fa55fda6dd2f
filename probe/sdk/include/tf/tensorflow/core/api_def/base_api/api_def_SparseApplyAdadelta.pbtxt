op {
  graph_op_name: "SparseApplyAdad<PERSON><PERSON>"
  in_arg {
    name: "accum"
    description: <<END
Should be from a Variable().
END
  }
  in_arg {
    name: "accum_update"
    description: <<END
: Should be from a Variable().
END
  }
  in_arg {
    name: "lr"
    description: <<END
Learning rate. Must be a scalar.
END
  }
  in_arg {
    name: "rho"
    description: <<END
Decay factor. Must be a scalar.
END
  }
  in_arg {
    name: "epsilon"
    description: <<END
Constant factor. Must be a scalar.
END
  }
  in_arg {
    name: "grad"
    description: <<END
The gradient.
END
  }
  in_arg {
    name: "indices"
    description: <<END
A vector of indices into the first dimension of var and accum.
END
  }
  out_arg {
    name: "out"
    description: <<END
Same as "var".
END
  }
  attr {
    name: "use_locking"
    description: <<END
If True, updating of the var and accum tensors will be protected by
a lock; otherwise the behavior is undefined, but may exhibit less contention.
END
  }
  summary: "var: Should be from a Variable()."
}

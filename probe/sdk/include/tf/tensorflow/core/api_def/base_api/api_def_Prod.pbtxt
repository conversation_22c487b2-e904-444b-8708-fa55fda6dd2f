op {
  graph_op_name: "Prod"
  endpoint {
    name: "Prod"
  }
  endpoint {
    name: "ReduceProd"
  }
  in_arg {
    name: "input"
    description: <<END
The tensor to reduce.
END
  }
  in_arg {
    name: "reduction_indices"
    rename_to: "axis"
    description: <<END
The dimensions to reduce. Must be in the range
`[-rank(input), rank(input))`.
END
  }
  out_arg {
    name: "output"
    description: <<END
The reduced tensor.
END
  }
  attr {
    name: "keep_dims"
    description: <<END
If true, retain reduced dimensions with length 1.
END
  }
  summary: "Computes the product of elements across dimensions of a tensor."
  description: <<END
Reduces `input` along the dimensions given in `reduction_indices`. Unless
`keep_dims` is true, the rank of the tensor is reduced by 1 for each entry in
`reduction_indices`. If `keep_dims` is true, the reduced dimensions are
retained with length 1.
END
}

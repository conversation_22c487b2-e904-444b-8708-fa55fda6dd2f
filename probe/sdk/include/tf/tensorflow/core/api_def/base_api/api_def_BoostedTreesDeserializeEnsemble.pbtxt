op {
  graph_op_name: "BoostedTreesDeserializeEnsemble"
  visibility: HIDDEN
  in_arg {
    name: "tree_ensemble_handle"
    description: <<END
Handle to the tree ensemble.
END
  }
  in_arg {
    name: "stamp_token"
    description: <<END
Token to use as the new value of the resource stamp.
END
  }
  in_arg {
    name: "tree_ensemble_serialized"
    description: <<END
Serialized proto of the ensemble.
END
  }
  summary: "Deserializes a serialized tree ensemble config and replaces current tree"
  description: <<END
ensemble.
END
}

op {
  graph_op_name: "AssignVariableOp"
  in_arg {
    name: "resource"
    description: <<END
handle to the resource in which to store the variable.
END
  }
  in_arg {
    name: "value"
    description: <<END
the value to set the new tensor to use.
END
  }
  attr {
    name: "dtype"
    description: <<END
the dtype of the value.
END
  }
  summary: "Assigns a new value to a variable."
  description: <<END
Any ReadVariableOp with a control dependency on this op is guaranteed to return
this value or a subsequent newer value of the variable.
END
}

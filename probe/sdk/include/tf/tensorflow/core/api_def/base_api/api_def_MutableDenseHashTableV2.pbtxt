op {
  graph_op_name: "MutableDenseHashTableV2"
  endpoint {
    name: "MutableDenseHashTable"
  }
  in_arg {
    name: "empty_key"
    description: <<END
The key used to represent empty key buckets internally. Must not
be used in insert or lookup operations.
END
  }
  out_arg {
    name: "table_handle"
    description: <<END
Handle to a table.
END
  }
  attr {
    name: "container"
    description: <<END
If non-empty, this table is placed in the given container.
Otherwise, a default container is used.
END
  }
  attr {
    name: "shared_name"
    description: <<END
If non-empty, this table is shared under the given name across
multiple sessions.
END
  }
  attr {
    name: "key_dtype"
    description: <<END
Type of the table keys.
END
  }
  attr {
    name: "value_dtype"
    description: <<END
Type of the table values.
END
  }
  attr {
    name: "value_shape"
    description: <<END
The shape of each value.
END
  }
  attr {
    name: "initial_num_buckets"
    description: <<END
The initial number of hash table buckets. Must be a power
to 2.
END
  }
  attr {
    name: "max_load_factor"
    description: <<END
The maximum ratio between number of entries and number of
buckets before growing the table. Must be between 0 and 1.
<PERSON><PERSON>
  }
  summary: "Creates an empty hash table that uses tensors as the backing store."
  description: <<END
It uses "open addressing" with quadratic reprobing to resolve
collisions.

This op creates a mutable hash table, specifying the type of its keys and
values. Each value must be a scalar. Data can be inserted into the table using
the insert operations. It does not support the initialization operation.
END
}

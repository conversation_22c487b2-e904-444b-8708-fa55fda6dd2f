op {
  graph_op_name: "Svd"
  in_arg {
    name: "input"
    description: <<END
A tensor of shape `[..., M, N]` whose inner-most 2 dimensions
form matrices of size `[M, N]`. Let `P` be the minimum of `M` and `N`.
END
  }
  out_arg {
    name: "s"
    description: <<END
Singular values. Shape is `[..., P]`.
END
  }
  out_arg {
    name: "u"
    description: <<END
Left singular vectors. If `full_matrices` is `False` then shape is
`[..., M, P]`; if `full_matrices` is `True` then shape is
`[..., M, M]`. Undefined if `compute_uv` is `False`.
END
  }
  out_arg {
    name: "v"
    description: <<END
Left singular vectors. If `full_matrices` is `False` then shape is
`[..., N, P]`. If `full_matrices` is `True` then shape is `[..., N, N]`.
Undefined if `compute_uv` is false.
END
  }
  attr {
    name: "compute_uv"
    description: <<END
If true, left and right singular vectors will be
computed and returned in `u` and `v`, respectively.
If false, `u` and `v` are not set and should never referenced.
END
  }
  attr {
    name: "full_matrices"
    description: <<END
If true, compute full-sized `u` and `v`. If false
(the default), compute only the leading `P` singular vectors.
Ignored if `compute_uv` is `False`.
END
  }
  summary: "Computes the singular value decompositions of one or more matrices."
  description: <<END
Computes the SVD of each inner matrix in `input` such that
`input[..., :, :] = u[..., :, :] * diag(s[..., :, :]) * transpose(v[..., :, :])`

```python
# a is a tensor containing a batch of matrices.
# s is a tensor of singular values for each matrix.
# u is the tensor containing of left singular vectors for each matrix.
# v is the tensor containing of right singular vectors for each matrix.
s, u, v = svd(a)
s, _, _ = svd(a, compute_uv=False)
```
END
}

op {
  graph_op_name: "SparseSparseMinimum"
  in_arg {
    name: "a_indices"
    description: <<END
2-D.  `N x R` matrix with the indices of non-empty values in a
SparseTensor, in the canonical lexicographic ordering.
END
  }
  in_arg {
    name: "a_values"
    description: <<END
1-D.  `N` non-empty values corresponding to `a_indices`.
END
  }
  in_arg {
    name: "a_shape"
    description: <<END
1-D.  Shape of the input SparseTensor.
END
  }
  in_arg {
    name: "b_indices"
    description: <<END
counterpart to `a_indices` for the other operand.
END
  }
  in_arg {
    name: "b_values"
    description: <<END
counterpart to `a_values` for the other operand; must be of the same dtype.
END
  }
  in_arg {
    name: "b_shape"
    description: <<END
counterpart to `a_shape` for the other operand; the two shapes must be equal.
END
  }
  out_arg {
    name: "output_indices"
    description: <<END
2-D.  The indices of the output SparseTensor.
END
  }
  out_arg {
    name: "output_values"
    description: <<END
1-D.  The values of the output SparseTensor.
END
  }
  summary: "Returns the element-wise min of two SparseTensors."
  description: <<END
Assumes the two SparseTensors have the same shape, i.e., no broadcasting.
END
}

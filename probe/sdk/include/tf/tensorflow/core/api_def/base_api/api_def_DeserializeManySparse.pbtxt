op {
  graph_op_name: "DeserializeManySparse"
  in_arg {
    name: "serialized_sparse"
    description: <<END
2-D, The `N` serialized `SparseTensor` objects.
Must have 3 columns.
END
  }
  attr {
    name: "dtype"
    description: <<END
The `dtype` of the serialized `SparseTensor` objects.
END
  }
  summary: "Deserialize and concatenate `SparseTensors` from a serialized minibatch."
  description: <<END
The input `serialized_sparse` must be a string matrix of shape `[N x 3]` where
`N` is the minibatch size and the rows correspond to packed outputs of
`SerializeSparse`.  The ranks of the original `SparseTensor` objects
must all match.  When the final `SparseTensor` is created, it has rank one
higher than the ranks of the incoming `SparseTensor` objects
(they have been concatenated along a new row dimension).

The output `SparseTensor` object's shape values for all dimensions but the
first are the max across the input `SparseTensor` objects' shape values
for the corresponding dimensions.  Its first shape value is `N`, the minibatch
size.

The input `SparseTensor` objects' indices are assumed ordered in
standard lexicographic order.  If this is not the case, after this
step run `<PERSON><PERSON><PERSON><PERSON><PERSON>` to restore index ordering.

For example, if the serialized input is a `[2 x 3]` matrix representing two
original `SparseTensor` objects:

    index = [ 0]
            [10]
            [20]
    values = [1, 2, 3]
    shape = [50]

and

    index = [ 2]
            [10]
    values = [4, 5]
    shape = [30]

then the final deserialized `SparseTensor` will be:

    index = [0  0]
            [0 10]
            [0 20]
            [1  2]
            [1 10]
    values = [1, 2, 3, 4, 5]
    shape = [2 50]
END
}

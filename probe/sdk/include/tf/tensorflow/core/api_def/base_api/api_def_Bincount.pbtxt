op {
  graph_op_name: "Bincount"
  in_arg {
    name: "arr"
    description: <<END
int32 `Tensor`.
END
  }
  in_arg {
    name: "size"
    description: <<END
non-negative int32 scalar `Tensor`.
END
  }
  in_arg {
    name: "weights"
    description: <<END
is an int32, int64, float32, or float64 `Tensor` with the same
shape as `arr`, or a length-0 `Tensor`, in which case it acts as all weights
equal to 1.
END
  }
  out_arg {
    name: "bins"
    description: <<END
1D `Tensor` with length equal to `size`. The counts or summed weights for
each value in the range [0, size).
END
  }
  summary: "Counts the number of occurrences of each value in an integer array."
  description: <<END
Outputs a vector with length `size` and the same dtype as `weights`. If
`weights` are empty, then index `i` stores the number of times the value `i` is
counted in `arr`. If `weights` are non-empty, then index `i` stores the sum of
the value in `weights` at each index where the corresponding value in `arr` is
`i`.

Values in `arr` outside of the range [0, size) are ignored.
END
}

op {
  graph_op_name: "RequantizationRange"
  in_arg {
    name: "input_min"
    description: <<END
The float value that the minimum quantized input value represents.
END
  }
  in_arg {
    name: "input_max"
    description: <<END
The float value that the maximum quantized input value represents.
END
  }
  out_arg {
    name: "output_min"
    description: <<END
The computed min output.
END
  }
  out_arg {
    name: "output_max"
    description: <<END
the computed max output.
END
  }
  attr {
    name: "Tinput"
    description: <<END
The type of the input.
END
  }
  summary:
"Computes a range that covers the actual values present in a quantized tensor."
  description: <<END
Given a quantized tensor described by `(input, input_min, input_max)`, outputs a
range that covers the actual values present in that tensor. This op is typically
used to produce the `requested_output_min` and `requested_output_max` for
`Requantize`.
END
}

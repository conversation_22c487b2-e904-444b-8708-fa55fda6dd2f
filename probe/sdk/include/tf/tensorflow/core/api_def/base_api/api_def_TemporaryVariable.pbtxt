op {
  graph_op_name: "TemporaryVariable"
  out_arg {
    name: "ref"
    description: <<END
A reference to the variable tensor.
END
  }
  attr {
    name: "shape"
    description: <<END
The shape of the variable tensor.
END
  }
  attr {
    name: "dtype"
    description: <<END
The type of elements in the variable tensor.
END
  }
  attr {
    name: "var_name"
    description: <<END
Overrides the name used for the temporary variable resource. Default
value is the name of the 'TemporaryVariable' op (which is guaranteed unique).
END
  }
  summary: "Returns a tensor that may be mutated, but only persists within a single step."
  description: <<END
This is an experimental op for internal use only and it is possible to use this
op in unsafe ways.  DO NOT USE unless you fully understand the risks.

It is the caller's responsibility to ensure that 'ref' is eventually passed to a
matching 'DestroyTemporaryVariable' op after all other uses have completed.

Outputs a ref to the tensor state so it may be read or modified.

  E.g.
      var = state_ops._temporary_variable([1, 2], types.float_)
      var_name = var.op.name
      var = state_ops.assign(var, [[4.0, 5.0]])
      var = state_ops.assign_add(var, [[6.0, 7.0]])
      final = state_ops._destroy_temporary_variable(var, var_name=var_name)
END
}

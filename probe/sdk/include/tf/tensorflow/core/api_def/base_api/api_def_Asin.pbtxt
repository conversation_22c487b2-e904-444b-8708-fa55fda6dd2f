op {
  graph_op_name: "Asin"
  summary: "Computes the trignometric inverse sine of x element-wise."
  description: <<END
The `tf.math.asin` operation returns the inverse of `tf.math.sin`, such that
if `y = tf.math.sin(x)` then, `x = tf.math.asin(y)`.

**Note**: The output of `tf.math.asin` will lie within the invertible range 
of sine, i.e [-pi/2, pi/2].

For example:

```python
# Note: [1.047, 0.785] ~= [(pi/3), (pi/4)]
x = tf.constant([1.047, 0.785])
y = tf.math.sin(x) # [0.8659266, 0.7068252]

tf.math.asin(y) # [1.047, 0.785] = x
```

END
}

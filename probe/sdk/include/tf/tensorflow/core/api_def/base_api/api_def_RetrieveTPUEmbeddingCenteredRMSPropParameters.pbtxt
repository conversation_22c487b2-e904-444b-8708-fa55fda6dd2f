op {
  graph_op_name: "RetrieveTPUEmbeddingCenteredRMSPropParameters"
  visibility: HIDDEN
  out_arg {
    name: "parameters"
    description: <<END
Parameter parameters updated by the centered RMSProp optimization algorithm.
END
  }
  out_arg {
    name: "ms"
    description: <<END
Parameter ms updated by the centered RMSProp optimization algorithm.
END
  }
  out_arg {
    name: "mom"
    description: <<END
Parameter mom updated by the centered RMSProp optimization algorithm.
END
  }
  out_arg {
    name: "mg"
    description: <<END
Parameter mg updated by the centered RMSProp optimization algorithm.
END
  }
  summary: "Retrieve centered RMSProp embedding parameters."
  description: <<END
An op that retrieves optimization parameters from embedding to host
memory. Must be preceded by a ConfigureTPUEmbeddingHost op that sets up
the correct embedding table configuration. For example, this op is
used to retrieve updated parameters before saving a checkpoint.
END
}

op {
  graph_op_name: "ListDiff"
  endpoint {
    name: "SetDiff1D"
  }
  in_arg {
    name: "x"
    description: <<END
1-D. Values to keep.
END
  }
  in_arg {
    name: "y"
    description: <<END
1-D. Values to remove.
END
  }
  out_arg {
    name: "out"
    description: <<END
1-D. Values present in `x` but not in `y`.
END
  }
  out_arg {
    name: "idx"
    description: <<END
1-D. Positions of `x` values preserved in `out`.
END
  }
  summary: "Computes the difference between two lists of numbers or strings."
  description: <<END
Given a list `x` and a list `y`, this operation returns a list `out` that
represents all values that are in `x` but not in `y`. The returned list `out`
is sorted in the same order that the numbers appear in `x` (duplicates are
preserved). This operation also returns a list `idx` that represents the
position of each `out` element in `x`. In other words:

`out[i] = x[idx[i]] for i in [0, 1, ..., len(out) - 1]`

For example, given this input:

```
x = [1, 2, 3, 4, 5, 6]
y = [1, 3, 5]
```

This operation would return:

```
out ==> [2, 4, 6]
idx ==> [1, 3, 5]
```
END
}

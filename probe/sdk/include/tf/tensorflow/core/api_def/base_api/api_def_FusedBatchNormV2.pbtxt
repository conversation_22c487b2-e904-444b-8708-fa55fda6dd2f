op {
  graph_op_name: "FusedBatchNormV2"
  in_arg {
    name: "x"
    description: <<END
A 4D Tensor for input data.
END
  }
  in_arg {
    name: "scale"
    description: <<END
A 1D Tensor for scaling factor, to scale the normalized x.
END
  }
  in_arg {
    name: "offset"
    description: <<END
A 1D Tensor for offset, to shift to the normalized x.
END
  }
  in_arg {
    name: "mean"
    description: <<END
A 1D Tensor for population mean. Used for inference only;
must be empty for training.
END
  }
  in_arg {
    name: "variance"
    description: <<END
A 1D Tensor for population variance. Used for inference only;
must be empty for training.
END
  }
  out_arg {
    name: "y"
    description: <<END
A 4D Tensor for output data.
END
  }
  out_arg {
    name: "batch_mean"
    description: <<END
A 1D Tensor for the computed batch mean, to be used by TensorFlow
to compute the running mean.
END
  }
  out_arg {
    name: "batch_variance"
    description: <<END
A 1D Tensor for the computed batch variance, to be used by
TensorFlow to compute the running variance.
END
  }
  out_arg {
    name: "reserve_space_1"
    description: <<END
A 1D Tensor for the computed batch mean, to be reused
in the gradient computation.
END
  }
  out_arg {
    name: "reserve_space_2"
    description: <<END
A 1D Tensor for the computed batch variance (inverted variance
in the cuDNN case), to be reused in the gradient computation.
END
  }
  attr {
    name: "T"
    description: <<END
The data type for the elements of input and output Tensors.
END
  }
  attr {
    name: "U"
    description: <<END
The data type for the scale, offset, mean, and variance.
END
  }
  attr {
    name: "epsilon"
    description: <<END
A small float number added to the variance of x.
END
  }
  attr {
    name: "data_format"
    description: <<END
The data format for x and y. Either "NHWC" (default) or "NCHW".
END
  }
  attr {
    name: "is_training"
    description: <<END
A bool value to indicate the operation is for training (default)
or inference.
END
  }
  summary: "Batch normalization."
  description: <<END
Note that the size of 4D Tensors are defined by either "NHWC" or "NCHW".
The size of 1D Tensors matches the dimension C of the 4D Tensors.
END
}

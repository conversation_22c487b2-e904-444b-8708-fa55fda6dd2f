op {
  graph_op_name: "RandomUniformInt"
  in_arg {
    name: "shape"
    description: <<END
The shape of the output tensor.
END
  }
  in_arg {
    name: "minval"
    description: <<END
0-D.  Inclusive lower bound on the generated integers.
END
  }
  in_arg {
    name: "maxval"
    description: <<END
0-D.  Exclusive upper bound on the generated integers.
END
  }
  out_arg {
    name: "output"
    description: <<END
A tensor of the specified shape filled with uniform random integers.
END
  }
  attr {
    name: "seed"
    description: <<END
If either `seed` or `seed2` are set to be non-zero, the random number
generator is seeded by the given seed.  Otherwise, it is seeded by a
random seed.
END
  }
  attr {
    name: "seed2"
    description: <<END
A second seed to avoid seed collision.
END
  }
  summary: "Outputs random integers from a uniform distribution."
  description: <<END
The generated values are uniform integers in the range `[minval, maxval)`.
The lower bound `minval` is included in the range, while the upper bound
`maxval` is excluded.

The random integers are slightly biased unless `maxval - minval` is an exact
power of two.  The bias is small for values of `maxval - minval` significantly
smaller than the range of the output (either `2^32` or `2^64`).
END
}

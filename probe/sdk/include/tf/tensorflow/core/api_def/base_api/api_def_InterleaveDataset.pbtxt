op {
  graph_op_name: "InterleaveDataset"
  visibility: HIDDE<PERSON>
  attr {
    name: "f"
    description: <<END
A function mapping elements of `input_dataset`, concatenated with
`other_arguments`, to a Dataset variant that contains elements matching
`output_types` and `output_shapes`.
END
  }
  summary: "Creates a dataset that applies `f` to the outputs of `input_dataset`."
  description: <<END
Unlike MapDataset, the `f` in InterleaveDataset is expected to return
a Dataset variant, and InterleaveDataset will flatten successive
results into a single Dataset. Unlike FlatMapDataset,
InterleaveDataset will interleave sequences of up to `block_length`
consecutive elements from `cycle_length` input elements.
END
}

op {
  graph_op_name: "TensorArrayConcatV3"
  endpoint {
    name: "TensorArrayConcat"
  }
  in_arg {
    name: "handle"
    description: <<END
The handle to a TensorArray.
END
  }
  in_arg {
    name: "flow_in"
    description: <<END
A float scalar that enforces proper chaining of operations.
END
  }
  out_arg {
    name: "value"
    description: <<END
All of the elements in the TensorArray, concatenated along the first
axis.
END
  }
  out_arg {
    name: "lengths"
    description: <<END
A vector of the row sizes of the original T elements in the
value output.  In the example above, this would be the values:
`(n1, n2, ..., n(T-1))`.
END
  }
  attr {
    name: "dtype"
    description: <<END
The type of the elem that is returned.
END
  }
  attr {
    name: "element_shape_except0"
    description: <<END
The expected shape of an element, if known,
excluding the first dimension. Used to validate the shapes of
TensorArray elements. If this shape is not fully specified, concatenating
zero-size TensorArrays is an error.
END
  }
  summary: "Concat the elements from the TensorArray into value `value`."
  description: <<END
Takes `T` elements of shapes

  ```
  (n0 x d0 x d1 x ...), (n1 x d0 x d1 x ...), ..., (n(T-1) x d0 x d1 x ...)
  ```

and concatenates them into a Tensor of shape:

  ```(n0 + n1 + ... + n(T-1) x d0 x d1 x ...)```

All elements must have the same shape (excepting the first dimension).
END
}

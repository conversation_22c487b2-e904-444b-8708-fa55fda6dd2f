op {
  graph_op_name: "DecodeWav"
  in_arg {
    name: "contents"
    description: <<END
The WAV-encoded audio, usually from a file.
END
  }
  out_arg {
    name: "audio"
    description: <<END
2-D with shape `[length, channels]`.
END
  }
  out_arg {
    name: "sample_rate"
    description: <<E<PERSON>ala<PERSON> holding the sample rate found in the WAV header.
END
  }
  attr {
    name: "desired_channels"
    description: <<END
Number of sample channels wanted.
END
  }
  attr {
    name: "desired_samples"
    description: <<END
Length of audio requested.
END
  }
  summary: "Decode a 16-bit PCM WAV file to a float tensor."
  description: <<END
The -32768 to 32767 signed 16-bit values will be scaled to -1.0 to 1.0 in float.

When desired_channels is set, if the input contains fewer channels than this
then the last channel will be duplicated to give the requested number, else if
the input has more channels than requested then the additional channels will be
ignored.

If desired_samples is set, then the audio will be cropped or padded with zeroes
to the requested length.

The first output contains a Tensor with the content of the audio samples. The
lowest dimension will be the number of channels, and the second will be the
number of samples. For example, a ten-sample-long stereo WAV file should give an
output shape of [10, 2].
END
}

op {
  graph_op_name: "QuantizedInstanceNorm"
  in_arg {
    name: "x"
    description: <<END
A 4D input Tensor.
END
  }
  in_arg {
    name: "x_min"
    description: <<END
The value represented by the lowest quantized input.
END
  }
  in_arg {
    name: "x_max"
    description: <<END
The value represented by the highest quantized input.
END
  }
  out_arg {
    name: "y"
    description: <<END
A 4D Tensor.
END
  }
  out_arg {
    name: "y_min"
    description: <<END
The value represented by the lowest quantized output.
END
  }
  out_arg {
    name: "y_max"
    description: <<END
The value represented by the highest quantized output.
END
  }
  attr {
    name: "output_range_given"
    description: <<END
If True, `given_y_min` and `given_y_min`
and `given_y_max` are used as the output range. Otherwise,
the implementation computes the output range.
END
  }
  attr {
    name: "given_y_min"
    description: <<END
Output in `y_min` if `output_range_given` is True.
END
  }
  attr {
    name: "given_y_max"
    description: <<END
Output in `y_max` if `output_range_given` is True.
END
  }
  attr {
    name: "variance_epsilon"
    description: <<END
A small float number to avoid dividing by 0.
END
  }
  attr {
    name: "min_separation"
    description: <<END
Minimum value of `y_max - y_min`
END
  }
  summary: "Quantized Instance normalization."
}

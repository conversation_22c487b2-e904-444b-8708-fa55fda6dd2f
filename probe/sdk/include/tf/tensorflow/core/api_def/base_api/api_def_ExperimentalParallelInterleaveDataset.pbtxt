op {
  graph_op_name: "ExperimentalParallelInterleaveDataset"
  visibility: HIDDE<PERSON>
  attr {
    name: "f"
    description: <<END
A function mapping elements of `input_dataset`, concatenated with
`other_arguments`, to a Dataset variant that contains elements matching
`output_types` and `output_shapes`.
END
  }
  summary: "Creates a dataset that applies `f` to the outputs of `input_dataset`."
  description: <<END
The resulting dataset is similar to the `InterleaveDataset`, with the exception
that if retrieving the next value from a dataset would cause the requester to
block, it will skip that input dataset. This dataset is especially useful
when loading data from a variable-latency datastores (e.g. HDFS, GCS), as it
allows the training step to proceed so long as some data is available.

!! WARNING !! This dataset is not deterministic!
END
}

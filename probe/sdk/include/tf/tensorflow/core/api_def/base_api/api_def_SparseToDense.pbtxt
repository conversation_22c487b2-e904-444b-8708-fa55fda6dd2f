op {
  graph_op_name: "SparseToDense"
  in_arg {
    name: "sparse_indices"
    description: <<END
0-D, 1-D, or 2-D.  `sparse_indices[i]` contains the complete
index where `sparse_values[i]` will be placed.
END
  }
  in_arg {
    name: "output_shape"
    description: <<END
1-D.  Shape of the dense output tensor.
END
  }
  in_arg {
    name: "sparse_values"
    description: <<END
1-D.  Values corresponding to each row of `sparse_indices`,
or a scalar value to be used for all sparse indices.
END
  }
  in_arg {
    name: "default_value"
    description: <<END
Scalar value to set for indices not specified in
`sparse_indices`.
END
  }
  out_arg {
    name: "dense"
    description: <<END
Dense output tensor of shape `output_shape`.
END
  }
  attr {
    name: "validate_indices"
    description: <<END
If true, indices are checked to make sure they are sorted in
lexicographic order and that there are no repeats.
END
  }
  summary: "Converts a sparse representation into a dense tensor."
  description: <<END
Builds an array `dense` with shape `output_shape` such that

```
# If sparse_indices is scalar
dense[i] = (i == sparse_indices ? sparse_values : default_value)

# If sparse_indices is a vector, then for each i
dense[sparse_indices[i]] = sparse_values[i]

# If sparse_indices is an n by d matrix, then for each i in [0, n)
dense[sparse_indices[i][0], ..., sparse_indices[i][d-1]] = sparse_values[i]
```

All other values in `dense` are set to `default_value`.  If `sparse_values` is a
scalar, all sparse indices are set to this single value.

Indices should be sorted in lexicographic order, and indices must not
contain any repeats. If `validate_indices` is true, these properties
are checked during execution.
END
}

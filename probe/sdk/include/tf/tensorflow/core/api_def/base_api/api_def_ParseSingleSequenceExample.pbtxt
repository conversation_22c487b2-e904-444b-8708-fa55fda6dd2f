op {
  graph_op_name: "ParseSingleSequenceExample"
  in_arg {
    name: "serialized"
    description: <<END
A scalar containing a binary serialized SequenceExample proto.
END
  }
  in_arg {
    name: "feature_list_dense_missing_assumed_empty"
    description: <<END
A vector listing the
FeatureList keys which may be missing from the SequenceExample.  If the
associated FeatureList is missing, it is treated as empty.  By default,
any FeatureList not listed in this vector must exist in the SequenceExample.
END
  }
  in_arg {
    name: "context_sparse_keys"
    description: <<END
A list of Ncontext_sparse string Tensors (scalars).
The keys expected in the Examples' features associated with context_sparse
values.
END
  }
  in_arg {
    name: "context_dense_keys"
    description: <<END
A list of Ncontext_dense string Tensors (scalars).
The keys expected in the SequenceExamples' context features associated with
dense values.
END
  }
  in_arg {
    name: "feature_list_sparse_keys"
    description: <<END
A list of Nfeature_list_sparse string Tensors
(scalars).  The keys expected in the FeatureLists associated with sparse
values.
END
  }
  in_arg {
    name: "feature_list_dense_keys"
    description: <<END
A list of Nfeature_list_dense string Tensors (scalars).
The keys expected in the SequenceExamples' feature_lists associated
with lists of dense values.
END
  }
  in_arg {
    name: "context_dense_defaults"
    description: <<END
A list of Ncontext_dense Tensors (some may be empty).
context_dense_defaults[j] provides default values
when the SequenceExample's context map lacks context_dense_key[j].
If an empty Tensor is provided for context_dense_defaults[j],
then the Feature context_dense_keys[j] is required.
The input type is inferred from context_dense_defaults[j], even when it's
empty.  If context_dense_defaults[j] is not empty, its shape must match
context_dense_shapes[j].
END
  }
  in_arg {
    name: "debug_name"
    description: <<END
A scalar containing the name of the serialized proto.
May contain, for example, table key (descriptive) name for the
corresponding serialized proto.  This is purely useful for debugging
purposes, and the presence of values here has no effect on the output.
May also be an empty scalar if no name is available.
END
  }
  attr {
    name: "context_sparse_types"
    description: <<END
A list of Ncontext_sparse types; the data types of data in
each context Feature given in context_sparse_keys.
Currently the ParseSingleSequenceExample supports DT_FLOAT (FloatList),
DT_INT64 (Int64List), and DT_STRING (BytesList).
END
  }
  attr {
    name: "context_dense_shapes"
    description: <<END
A list of Ncontext_dense shapes; the shapes of data in
each context Feature given in context_dense_keys.
The number of elements in the Feature corresponding to context_dense_key[j]
must always equal context_dense_shapes[j].NumEntries().
The shape of context_dense_values[j] will match context_dense_shapes[j].
END
  }
  attr {
    name: "feature_list_sparse_types"
    description: <<END
A list of Nfeature_list_sparse types; the data types
of data in each FeatureList given in feature_list_sparse_keys.
Currently the ParseSingleSequenceExample supports DT_FLOAT (FloatList),
DT_INT64 (Int64List), and DT_STRING (BytesList).
END
  }
  attr {
    name: "feature_list_dense_shapes"
    description: <<END
A list of Nfeature_list_dense shapes; the shapes of
data in each FeatureList given in feature_list_dense_keys.
The shape of each Feature in the FeatureList corresponding to
feature_list_dense_key[j] must always equal
feature_list_dense_shapes[j].NumEntries().
END
  }
  summary: "Transforms a scalar brain.SequenceExample proto (as strings) into typed tensors."
}

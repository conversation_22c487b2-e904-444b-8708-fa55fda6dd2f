op {
  graph_op_name: "DecodeBmp"
  in_arg {
    name: "contents"
    description: <<END
0-D.  The BMP-encoded image.
END
  }
  out_arg {
    name: "image"
    description: <<END
3-D with shape `[height, width, channels]`. RGB order
END
  }
  summary: "Decode the first frame of a BMP-encoded image to a uint8 tensor."
  description: <<END
The attr `channels` indicates the desired number of color channels for the
decoded image.

Accepted values are:

*   0: Use the number of channels in the BMP-encoded image.
*   3: output an RGB image.
*   4: output an RGBA image.
END
}

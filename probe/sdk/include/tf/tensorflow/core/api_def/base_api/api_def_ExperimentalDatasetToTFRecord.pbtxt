op {
  graph_op_name: "ExperimentalDatasetToTFRecord"
  visibility: HIDDE<PERSON>
  in_arg {
    name: "input_dataset"
    description: <<END
A variant tensor representing the dataset to write.
END
  }
  in_arg {
    name: "filename"
    description: <<END
A scalar string tensor representing the filename to use.
END
  }
  in_arg {
    name: "compression_type"
    description: <<END
A scalar string tensor containing either (i) the empty string (no
compression), (ii) "ZLIB", or (iii) "GZIP".
END
  }
  summary: "Writes the given dataset to the given file using the TFRecord format."
}

op {
  graph_op_name: "ArgMax"
  in_arg {
    name: "dimension"
    description: <<END
int32 or int64, must be in the range `[-rank(input), rank(input))`.
Describes which dimension of the input Tensor to reduce across. For vectors,
use dimension = 0.
END
  }
  summary: "Returns the index with the largest value across dimensions of a tensor."
  description: <<END
Note that in case of ties the identity of the return value is not guaranteed.

Usage:
  ```python
  import tensorflow as tf
  a = [1, 10, 26.9, 2.8, 166.32, 62.3]
  b = tf.math.argmax(input = a)
  c = tf.keras.backend.eval(b)  
  # c = 4
  # here a[4] = 166.32 which is the largest element of a across axis 0
  ```
END
}

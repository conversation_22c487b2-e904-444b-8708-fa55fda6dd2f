op {
  graph_op_name: "SparseDenseCwiseMul"
  in_arg {
    name: "sp_indices"
    description: <<END
2-D.  `N x R` matrix with the indices of non-empty values in a
SparseTensor, possibly not in canonical ordering.
END
  }
  in_arg {
    name: "sp_values"
    description: <<END
1-D.  `N` non-empty values corresponding to `sp_indices`.
END
  }
  in_arg {
    name: "sp_shape"
    description: <<END
1-D.  Shape of the input SparseTensor.
END
  }
  in_arg {
    name: "dense"
    description: <<END
`R`-D.  The dense Tensor operand.
END
  }
  out_arg {
    name: "output"
    description: <<END
1-D.  The `N` values that are operated on.
END
  }
  summary: "Component-wise multiplies a SparseTensor by a dense Tensor."
  description: <<END
The output locations corresponding to the implicitly zero elements in the sparse
tensor will be zero (i.e., will not take up storage space), regardless of the
contents of the dense tensor (even if it's +/-INF and that INF*0 == NaN).

*Limitation*: this Op only broadcasts the dense side to the sparse side, but not
the other direction.
END
}

op {
  graph_op_name: "PreventGradient"
  in_arg {
    name: "input"
    description: <<END
any tensor.
END
  }
  out_arg {
    name: "output"
    description: <<END
the same input tensor.
END
  }
  attr {
    name: "message"
    description: <<END
Will be printed in the error when anyone tries to differentiate
this operation.
END
  }
  summary: "An identity op that triggers an error if a gradient is requested."
  description: <<END
When executed in a graph, this op outputs its input tensor as-is.

When building ops to compute gradients, the TensorFlow gradient system
will return an error when trying to lookup the gradient of this op,
because no gradient must ever be registered for this function.  This
op exists to prevent subtle bugs from silently returning unimplemented
gradients in some corner cases.
END
}

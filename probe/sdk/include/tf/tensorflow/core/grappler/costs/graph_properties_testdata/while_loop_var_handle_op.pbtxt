node {
  name: "Const"
  op: "Const"
  attr {
    key: "dtype"
    value { type: DT_INT32 }
  }
  attr {
    key: "value"
    value {
      tensor {
        dtype: DT_INT32
        tensor_shape {}
        int_val: 0
      }
    }
  }
}
node {
  name: "loop_var"
  op: "VarHandleOp"
  attr {
    key: "_class"
    value { list { s: "loc:@loop_var" } }
  }
  attr {
    key: "container"
    value { s: "" }
  }
  attr {
    key: "dtype"
    value { type: DT_INT32 }
  }
  attr {
    key: "shape"
    value { shape {} }
  }
  attr {
    key: "shared_name"
    value { s: "loop_var" }
  }
}
node {
  name: "Const_1"
  op: "Const"
  attr {
    key: "dtype"
    value { type: DT_INT32 }
  }
  attr {
    key: "value"
    value {
      tensor {
        dtype: DT_INT32
        tensor_shape {}
        int_val: 1
      }
    }
  }
}
node {
  name: "while/Enter"
  op: "Enter"
  input: "loop_var"
  attr {
    key: "T"
    value { type: DT_RESOURCE }
  }
  attr {
    key: "frame_name"
    value { s: "while/while_context" }
  }
  attr {
    key: "is_constant"
    value { b: false }
  }
  attr {
    key: "parallel_iterations"
    value { i: 10 }
  }
}
node {
  name: "while/Enter_1"
  op: "Enter"
  input: "Const_1"
  attr {
    key: "T"
    value { type: DT_INT32 }
  }
  attr {
    key: "frame_name"
    value { s: "while/while_context" }
  }
  attr {
    key: "is_constant"
    value { b: false }
  }
  attr {
    key: "parallel_iterations"
    value { i: 10 }
  }
}
node {
  name: "while/Merge"
  op: "Merge"
  input: "while/Enter"
  input: "while/NextIteration"
  attr {
    key: "N"
    value { i: 2 }
  }
  attr {
    key: "T"
    value { type: DT_RESOURCE }
  }
}
node {
  name: "while/Merge_1"
  op: "Merge"
  input: "while/Enter_1"
  input: "while/NextIteration_1"
  attr {
    key: "N"
    value { i: 2 }
  }
  attr {
    key: "T"
    value { type: DT_INT32 }
  }
}
node {
  name: "while/Less/y"
  op: "Const"
  input: "^while/Merge"
  attr {
    key: "dtype"
    value { type: DT_INT32 }
  }
  attr {
    key: "value"
    value {
      tensor {
        dtype: DT_INT32
        tensor_shape {}
        int_val: 3
      }
    }
  }
}
node {
  name: "while/Less"
  op: "Less"
  input: "while/Merge"
  input: "while/Less/y"
  attr {
    key: "T"
    value { type: DT_INT32 }
  }
}
node { name: "while/LoopCond" op: "LoopCond" input: "while/Less" }
node {
  name: "while/Switch"
  op: "Switch"
  input: "while/Merge"
  input: "while/LoopCond"
  attr {
    key: "T"
    value { type: DT_RESOURCE }
  }
  attr {
    key: "_class"
    value { list { s: "loc:@while/Merge" } }
  }
}
node {
  name: "while/Switch_1"
  op: "Switch"
  input: "while/Merge_1"
  input: "while/LoopCond"
  attr {
    key: "T"
    value { type: DT_INT32 }
  }
  attr {
    key: "_class"
    value { list { s: "loc:@while/Merge_1" } }
  }
}
node {
  name: "while/Identity"
  op: "Identity"
  input: "while/Switch:1"
  attr {
    key: "T"
    value { type: DT_RESOURCE }
  }
}
node {
  name: "while/ReadVariableOp"
  op: "ReadVariableOp"
  input: "while/Identity"
  attr {
    key: "dtype"
    value { type: DT_INT32 }
  }
}
node {
  name: "while/Identity_1"
  op: "Identity"
  input: "while/Switch_1:1"
  attr {
    key: "T"
    value { type: DT_INT32 }
  }
}
node {
  name: "while/add/y"
  op: "Const"
  input: "^while/ReadVariableOp"
  attr {
    key: "dtype"
    value { type: DT_INT32 }
  }
  attr {
    key: "value"
    value {
      tensor {
        dtype: DT_INT32
        tensor_shape {}
        int_val: 1
      }
    }
  }
}
node {
  name: "while/add"
  op: "Add"
  input: "while/ReadVariableOp"
  input: "while/add/y"
  attr {
    key: "T"
    value { type: DT_INT32 }
  }
}
node {
  name: "while/add_1"
  op: "Add"
  input: "while/Identity_1"
  input: "while/Identity_1"
  attr {
    key: "T"
    value { type: DT_INT32 }
  }
}
node {
  name: "while/NextIteration"
  op: "NextIteration"
  input: "while/Identity"
  attr {
    key: "T"
    value { type: DT_RESOURCE }
  }
}
node {
  name: "while/NextIteration_1"
  op: "NextIteration"
  input: "while/add_1"
  attr {
    key: "T"
    value { type: DT_INT32 }
  }
}
node {
  name: "while/Exit"
  op: "Exit"
  input: "while/Switch"
  attr {
    key: "T"
    value { type: DT_RESOURCE }
  }
}
node {
  name: "while/Exit_1"
  op: "Exit"
  input: "while/Switch_1"
  attr {
    key: "T"
    value { type: DT_INT32 }
  }
}
versions { producer: 27 }

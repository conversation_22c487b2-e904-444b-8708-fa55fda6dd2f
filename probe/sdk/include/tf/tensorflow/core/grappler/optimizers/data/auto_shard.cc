/* Copyright 2019 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/

#include "tensorflow/core/grappler/optimizers/data/auto_shard.h"

#include "absl/container/flat_hash_map.h"
#include "absl/container/flat_hash_set.h"
#include "tensorflow/core/framework/function.h"
#include "tensorflow/core/framework/function.pb.h"
#include "tensorflow/core/framework/node_def.pb.h"
#include "tensorflow/core/grappler/clusters/cluster.h"
#include "tensorflow/core/grappler/grappler_item.h"
#include "tensorflow/core/grappler/mutable_graph_view.h"
#include "tensorflow/core/grappler/op_types.h"
#include "tensorflow/core/grappler/optimizers/custom_graph_optimizer_registry.h"
#include "tensorflow/core/grappler/optimizers/data/graph_utils.h"
#include "tensorflow/core/grappler/utils/functions.h"
#include "tensorflow/core/lib/core/errors.h"

namespace tensorflow {
namespace grappler {
namespace {

// clang-format off
constexpr char kShardDatasetOpName[] = "ShardDataset";
constexpr char kShuffleDatasetOpName[] = "ShuffleDataset";

constexpr std::array<const char*, 4> kReaderDatasetOps = {
    "FixedLengthRecordDataset",
    "FixedLengthRecordDatasetV2",
    "TextLineDataset",
    "TFRecordDataset"
};

constexpr std::array<const char*, 2> kMultipleInputsDatasetOps = {
    "ConcatenateDataset",
    "ZipDataset"
};

constexpr std::array<const char*, 22> kPassThroughOps = {
    "BatchDataset",
    "BatchDatasetV2",
    "ExperimentalMapAndBatchDataset",
    "PaddedBatchDataset",
    "PaddedBatchDatasetV2",
    "CacheDataset",
    "FilterDataset",
    "FilterByLastComponentDataset",
    "Identity",
    "MapDataset",
    "ModelDataset",
    "OptimizeDataset",
    "ParallelMapDataset",
    "PrefetchDataset",
    "ReduceDataset",
    "RepeatDataset",
    "ShardDataset",
    "ShuffleAndRepeatDataset",
    "ShuffleDataset",
    "SkipDataset",
    "TakeDataset",
    "WindowDataset"
};

// TODO(frankchn): Process functions within kFuncDatasetOps as well.
constexpr std::array<const char*, 4> kFuncDatasetOps = {
    "ExperimentalParallelInterleaveDataset",
    "FlatMapDataset",
    "InterleaveDataset",
    "ParallelInterleaveDatasetV2"
};

constexpr std::array<const char*, 5> kUnshardableSourceDatasetOps = {
    "GeneratorDataset",
    "RangeDataset",
    "SparseTensorsSliceDataset",
    "TensorDataset",
    "TensorSliceDataset",
};
// clang-format on

Status OptimizeGraph(const GrapplerItem& item, int64 num_workers, int64 index,
                     GraphDef* output);

template <std::size_t SIZE>
bool IsDatasetNodeOfType(const NodeDef& node,
                         const std::array<const char*, SIZE>& arr) {
  for (const auto& dataset_op_name : arr) {
    if (node.op() == dataset_op_name) return true;
  }
  return false;
}

Status AddShardNode(MutableGraphView* graph, const NodeDef& add_before,
                    int64 num_workers, int64 index) {
  NodeDef new_node;
  new_node.set_op(kShardDatasetOpName);
  graph_utils::SetUniqueGraphNodeName(kShardDatasetOpName, graph->graph(),
                                      &new_node);

  // Construct argument nodes
  NodeDef* num_shards_node =
      graph_utils::AddScalarConstNode<int64>(num_workers, graph);
  NodeDef* index_node = graph_utils::AddScalarConstNode<int64>(index, graph);

  // Add inputs to new node
  new_node.add_input(add_before.input(0));
  new_node.add_input(num_shards_node->name());
  new_node.add_input(index_node->name());

  // Add shapes and other attributes
  NodeDef* add_after = graph->GetNode(add_before.input(0));
  graph_utils::CopyAttribute("output_shapes", *add_after, &new_node);

  if (add_after->attr().find("Toutput_types") != add_after->attr().end()) {
    (*(new_node.mutable_attr()))["output_types"] =
        add_after->attr().at("Toutput_types");
  } else {
    graph_utils::CopyAttribute("output_types", *add_after, &new_node);
  }

  // Add new node into graph and update edges
  NodeDef* new_node_graph = graph->AddNode(std::move(new_node));
  TF_RETURN_IF_ERROR(
      graph->UpdateFanouts(add_after->name(), new_node_graph->name()));

  return Status::OK();
}

bool ReaderOpInFunction(const NodeDef& node,
                        const FunctionLibraryDefinition& flib) {
  const FunctionDef* func = flib.Find(node.attr().at("f").func().name());
  for (int i = 0; i < func->node_def_size(); i++) {
    NodeDef node_in_func = func->node_def(i);
    if (IsDatasetNodeOfType(node_in_func, kReaderDatasetOps) &&
        node_in_func.input_size() > 0 &&
        str_util::StartsWith(node_in_func.input(0), "args_0")) {
      return true;
    }
    if (IsDatasetNodeOfType(func->node_def(i), kFuncDatasetOps) &&
        ReaderOpInFunction(func->node_def(i), flib)) {
      return true;
    }
  }
  return false;
}

Status RemoveShuffleDataset(MutableGraphView* graph, const NodeDef& node,
                            absl::flat_hash_set<string>* nodes_to_delete) {
  if (node.op() == kShuffleDatasetOpName) {
    TF_RETURN_IF_ERROR(graph->UpdateFanouts(node.name(), node.input(0)));
    nodes_to_delete->insert(node.name());
  }

  for (const auto& fanin : graph->GetFanins(node, true)) {
    TF_RETURN_IF_ERROR(
        RemoveShuffleDataset(graph, *fanin.node, nodes_to_delete));
  }

  // TODO(frankchn): Traverse functions too.
  return Status::OK();
}

Status RecursivelyHandleOp(const NodeDef& node, int64 num_workers, int64 index,
                           FunctionLibraryDefinition* flib,
                           MutableGraphView* graph,
                           absl::flat_hash_set<string>* nodes_to_delete) {
  if (IsDatasetNodeOfType(node, kUnshardableSourceDatasetOps)) {
    return errors::NotFound("Found an unshardable source dataset: ",
                            node.DebugString());
  }

  if (IsDatasetNodeOfType(node, kMultipleInputsDatasetOps)) {
    for (int i = 0; i < node.input_size(); ++i) {
      const NodeDef* input_node = graph_utils::GetInputNode(node, *graph, i);
      TF_RETURN_IF_ERROR(RecursivelyHandleOp(*input_node, num_workers, index,
                                             flib, graph, nodes_to_delete));
    }
    return Status::OK();
  }

  // This handles the case where a reader Dataset is contained within a
  // FuncDataset (e.g. FlatMap, ParallelInterleave, etc...). For example:
  //
  // dataset = Dataset.list_files("/path/to/data")
  // dataset = dataset.flat_map(core_readers.TFRecordDataset)
  //
  // where the list of files is passed in one-by-one as an argument to the
  // function in flat_map.
  if (IsDatasetNodeOfType(node, kFuncDatasetOps) &&
      ReaderOpInFunction(node, *flib)) {
    TF_RETURN_IF_ERROR(AddShardNode(graph, node, num_workers, index));
    TF_RETURN_IF_ERROR(RemoveShuffleDataset(graph, node, nodes_to_delete));
    return Status::OK();
  }

  if (IsDatasetNodeOfType(node, kReaderDatasetOps)) {
    // We reached a reader dataset directly and we try to shard input 0.
    TF_RETURN_IF_ERROR(AddShardNode(graph, node, num_workers, index));
    TF_RETURN_IF_ERROR(RemoveShuffleDataset(graph, node, nodes_to_delete));
    return Status::OK();
  }

  if (!IsDatasetNodeOfType(node, kPassThroughOps)) {
    return errors::NotFound(
        "Did not find a shardable source, walked to ",
        "a node which is not a dataset: ", node.DebugString());
  }

  const NodeDef* input_node = graph_utils::GetInputNode(node, *graph, 0);
  return RecursivelyHandleOp(*input_node, num_workers, index, flib, graph,
                             nodes_to_delete);
}

Status OptimizeGraph(const GrapplerItem& item, int64 num_workers, int64 index,
                     GraphDef* output) {
  *output = item.graph;
  MutableGraphView graph(output);
  FunctionLibraryDefinition flib(OpRegistry::Global(), item.graph.library());

  NodeDef target_node;
  absl::flat_hash_set<string> nodes_to_delete;

  // The basic approach here is to walk the graph from sink to source, and find
  // the latest occurrence of a ReaderDataset (e.g. CSVDataset, TFRecordDataset,
  // etc...). We then add a shard after that dataset to shard the outputs of
  // that dataset, in effect giving a piece to each worker. Finally, we remove
  // occurences from randomness from before that point in the graph (e.g. things
  // like ShuffleDataset) to ensure that `shard` returns a sensible result.

  NodeDef sink_node;
  TF_RETURN_IF_ERROR(graph_utils::FindSinkNode(item.graph, &sink_node));
  TF_RETURN_IF_ERROR(RecursivelyHandleOp(sink_node, num_workers, index, &flib,
                                         &graph, &nodes_to_delete));

  TF_RETURN_IF_ERROR(graph.DeleteNodes(nodes_to_delete));

  return Status::OK();
}

}  // anonymous namespace

Status AutoShard::Init(
    const tensorflow::RewriterConfig_CustomGraphOptimizer* config) {
  if (!config) return errors::InvalidArgument("RewriterConfig not found.");

  if ((config->parameter_map().find("num_workers") ==
       config->parameter_map().end())) {
    return errors::InvalidArgument("num_workers parameter missing.");
  }

  if ((config->parameter_map().find("index") ==
       config->parameter_map().end())) {
    return errors::InvalidArgument("index parameter missing.");
  }

  num_workers_ = config->parameter_map().at("num_workers").i();
  index_ = config->parameter_map().at("index").i();

  if (num_workers_ < 1) {
    return errors::InvalidArgument("num_workers should be >= 1, currently ",
                                   num_workers_);
  }

  if (index_ < 0 || index_ >= num_workers_) {
    return errors::InvalidArgument("index should be >= 0 and < ", num_workers_,
                                   ", currently ", index_);
  }

  return Status::OK();
}

Status AutoShard::OptimizeAndCollectStats(Cluster* /* cluster */,
                                          const GrapplerItem& item,
                                          GraphDef* output,
                                          OptimizationStats* stats) {
  *output = item.graph;
  MutableGraphView graph(output);

  TF_RETURN_IF_ERROR(OptimizeGraph(item, num_workers_, index_, output));
  stats->num_changes++;
  return Status::OK();
}

REGISTER_GRAPH_OPTIMIZER_AS(AutoShard, "tf_auto_shard");

}  // namespace grappler
}  // namespace tensorflow

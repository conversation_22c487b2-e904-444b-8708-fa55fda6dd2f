/* Copyright 2015 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/

#include "tensorflow/core/common_runtime/gpu/gpu_init.h"

#include <string>

#include "tensorflow/core/lib/core/errors.h"
#include "tensorflow/core/lib/strings/numbers.h"
#include "tensorflow/core/lib/strings/str_util.h"
#include "tensorflow/core/lib/strings/strcat.h"
#include "tensorflow/core/platform/logging.h"
#include "tensorflow/core/platform/stream_executor.h"
#include "tensorflow/core/platform/types.h"
#include "tensorflow/core/util/stream_executor_util.h"

namespace tensorflow {

Status ValidateGPUMachineManager() {
  return se::MultiPlatformManager::PlatformWithName(GpuPlatformName()).status();
}

se::Platform* GPUMachineManager() {
  auto result = se::MultiPlatformManager::PlatformWithName(GpuPlatformName());
  if (!result.ok()) {
    LOG(FATAL) << "Could not find Platform with name " << GpuPlatformName();
    return nullptr;
  }

  return result.ValueOrDie();
}

string GpuPlatformName() {
#if TENSORFLOW_USE_ROCM
  return "ROCM";
#else
  // This function will return "CUDA" even when building TF without GPU support
  // This is done to preserve existing functionality
  return "CUDA";
#endif
}

}  // namespace tensorflow

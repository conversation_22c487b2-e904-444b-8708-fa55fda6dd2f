/* Copyright 2018 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/

#ifdef INTEL_MKL

#include "tensorflow/core/common_runtime/mkl_cpu_allocator.h"

#ifdef _WIN32
// Declare function to avoid unresolved symbol in VS
i_malloc_t i_malloc;
i_calloc_t i_calloc;
i_realloc_t i_realloc;
i_free_t i_free;
#endif
namespace tensorflow {

constexpr const char* MklCPUAllocator::kMaxLimitStr;
constexpr const size_t MklCPUAllocator::kDefaultMaxLimit;

}  // namespace tensorflow

#endif  // INTEL_MKL

/* Copyright 2017 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/

#include "tensorflow/core/distributed_runtime/request_id.h"

#include "tensorflow/core/platform/types.h"

namespace tensorflow {

int64 GetUniqueRequestId() {
  int64 request_id = 0;
  while (request_id == 0) {
    request_id = random::New64();
  }
  return request_id;
}

}  // namespace tensorflow

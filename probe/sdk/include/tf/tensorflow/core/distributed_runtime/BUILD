# Description:
# A distributed runtime for TensorFlow, which allows graph execution
# to be distributed and performed in parallel across multiple
# processes.

licenses(["notice"])  # Apache 2.0

exports_files(["LICENSE"])

filegroup(
    name = "c_srcs",
    data = glob([
        "**/*.cc",
        "**/*.h",
    ]),
)

load("//tensorflow:tensorflow.bzl", "tf_cc_test", "tf_cuda_library")
load("//tensorflow:tensorflow.bzl", "tf_cuda_cc_test")
load("//tensorflow:tensorflow.bzl", "tf_copts")

# For platform specific build config
load(
    "//tensorflow/core:platform/default/build_config.bzl",
    "tf_kernel_tests_linkstatic",
)
load(
    "//tensorflow/core:platform/default/build_config_root.bzl",
    "tf_cuda_tests_tags",
)

package(default_visibility = [
    "//tensorflow:internal",
])

cc_library(
    name = "partial_run_mgr",
    srcs = ["partial_run_mgr.cc"],
    hdrs = ["partial_run_mgr.h"],
    deps = [
        ":worker_interface",
        "//tensorflow/core:framework",
        "//tensorflow/core:lib",
    ],
)

tf_cc_test(
    name = "partial_run_mgr_test",
    size = "small",
    srcs = ["partial_run_mgr_test.cc"],
    deps = [
        ":partial_run_mgr",
        "//tensorflow/core:lib",
        "//tensorflow/core:test",
        "//tensorflow/core:test_main",
    ],
)

cc_library(
    name = "message_wrappers",
    srcs = ["message_wrappers.cc"],
    hdrs = ["message_wrappers.h"],
    deps = [
        "//tensorflow/core:framework",
        "//tensorflow/core:master_proto_cc",
        "//tensorflow/core:proto_text",
        "//tensorflow/core:protos_all_cc",
        "//tensorflow/core:worker_proto_cc",
    ],
)

tf_cc_test(
    name = "message_wrappers_test",
    size = "small",
    srcs = ["message_wrappers_test.cc"],
    deps = [
        ":message_wrappers",
        "//tensorflow/core:protos_all_cc",
        "//tensorflow/core:tensor_testutil",
        "//tensorflow/core:test",
        "//tensorflow/core:test_main",
    ],
)

cc_library(
    name = "worker_session",
    srcs = [
        "cluster_function_library_runtime.cc",
        "worker_session.cc",
    ],
    hdrs = [
        "cluster_function_library_runtime.h",
        "worker_session.h",
    ],
    deps = [
        ":graph_mgr",
        ":worker_cache",
        ":worker_interface",
        "//tensorflow/core:core_cpu_internal",
        "//tensorflow/core:framework",
        "//tensorflow/core:lib_internal",
        "//tensorflow/core:protos_all_cc",
    ],
)

tf_cc_test(
    name = "cluster_function_library_runtime_test",
    srcs = ["cluster_function_library_runtime_test.cc"],
    linkstatic = tf_kernel_tests_linkstatic(),
    deps = [
        ":worker_session",
        "//tensorflow/core:framework_internal",
        "//tensorflow/core:test",
        "//tensorflow/core:test_main",
        "//tensorflow/core:testlib",
        "//tensorflow/core/distributed_runtime/rpc:grpc_channel",
        "//tensorflow/core/distributed_runtime/rpc:grpc_testlib",
        "//tensorflow/core/distributed_runtime/rpc:grpc_worker_cache",
        "//tensorflow/core/kernels:cast_op",
        "//tensorflow/core/kernels:cwise_op",
    ],
)

cc_library(
    name = "session_mgr",
    srcs = ["session_mgr.cc"],
    hdrs = ["session_mgr.h"],
    deps = [
        ":graph_mgr",
        ":worker_cache_wrapper",
        ":worker_session",
        "//tensorflow/core:core_cpu_internal",
        "//tensorflow/core:lib",
        "//tensorflow/core:protos_all_cc",
        "//tensorflow/core:ptr_util",
        "//tensorflow/core:worker_proto_cc",
    ],
)

tf_cc_test(
    name = "session_mgr_test",
    size = "small",
    srcs = ["session_mgr_test.cc"],
    deps = [
        ":session_mgr",
        ":worker_env",
        "//tensorflow/core:framework",
        "//tensorflow/core:protos_all_cc",
        "//tensorflow/core:test",
        "//tensorflow/core:test_main",
        "//tensorflow/core:testlib",
        "//tensorflow/core/distributed_runtime/rpc:rpc_rendezvous_mgr",
    ],
)

cc_library(
    name = "worker_env",
    hdrs = ["worker_env.h"],
    deps = ["//tensorflow/core:lib"],
)

cc_library(
    name = "tensor_coding",
    srcs = ["tensor_coding.cc"],
    hdrs = [
        "tensor_coding.h",
    ],
    deps = [
        "//tensorflow/core:core_cpu_internal",
        "//tensorflow/core:framework",
        "//tensorflow/core:lib",
        "//tensorflow/core:protos_all_cc",
        "//tensorflow/core:worker_proto_cc",
    ],
)

cc_library(
    name = "worker_interface",
    hdrs = [
        "worker_interface.h",
    ],
    deps = [
        ":call_options",
        ":message_wrappers",
        "//tensorflow/core:core_cpu_internal",
        "//tensorflow/core:lib",
        "//tensorflow/core:worker_proto_cc",
    ],
)

tf_cuda_library(
    name = "worker",
    srcs = ["worker.cc"],
    hdrs = [
        "worker.h",
    ],
    deps = [
        ":graph_mgr",
        ":partial_run_mgr",
        ":rendezvous_mgr_interface",
        ":session_mgr",
        ":tensor_coding",
        ":worker_interface",
        ":worker_session",
        "//tensorflow/core:core_cpu_internal",
        "//tensorflow/core:device_tracer",
        "//tensorflow/core:lib_internal",
        "//tensorflow/core/distributed_runtime:recent_request_ids",
    ],
)

cc_library(
    name = "call_options",
    srcs = ["call_options.cc"],
    hdrs = ["call_options.h"],
    deps = [
        "//tensorflow/core:lib",
    ],
)

tf_cc_test(
    name = "call_options_test",
    size = "small",
    srcs = ["call_options_test.cc"],
    deps = [
        ":call_options",
        "//tensorflow/core:test",
        "//tensorflow/core:test_main",
    ],
)

cc_library(
    name = "cancellable_call",
    hdrs = ["cancellable_call.h"],
    deps = [
        ":call_options",
        ":worker_cache",
        "//tensorflow/core:framework",
        "//tensorflow/core:lib",
    ],
)

tf_cc_test(
    name = "tensor_coding_test",
    size = "small",
    srcs = ["tensor_coding_test.cc"],
    linkstatic = 1,
    deps = [
        ":tensor_coding",
        "//tensorflow/core:core_cpu",
        "//tensorflow/core:core_cpu_base",
        "//tensorflow/core:framework",
        "//tensorflow/core:lib",
        "//tensorflow/core:protos_all_cc",
        "//tensorflow/core:tensor_testutil",
        "//tensorflow/core:test",
        "//tensorflow/core:test_main",
        "//tensorflow/core:worker_proto_cc",
    ],
)

cc_library(
    name = "worker_cache",
    hdrs = ["worker_cache.h"],
    deps = [
        ":worker_interface",
        "//tensorflow/core:lib",
        "//tensorflow/core:protos_all_cc",
    ],
)

cc_library(
    name = "worker_cache_wrapper",
    hdrs = ["worker_cache_wrapper.h"],
    deps = [
        ":worker_cache",
        "//tensorflow/core:lib",
        "//tensorflow/core:protos_all_cc",
    ],
)

cc_library(
    name = "remote_device",
    srcs = ["remote_device.cc"],
    hdrs = ["remote_device.h"],
    deps = [
        ":worker_cache",
        ":worker_interface",
        "//tensorflow/core:core_cpu_internal",
        "//tensorflow/core:lib",
        "//tensorflow/core:worker_proto_cc",
    ],
)

cc_library(
    name = "master_interface",
    hdrs = ["master_interface.h"],
    deps = [
        ":call_options",
        ":message_wrappers",
        ":request_id",
        "//tensorflow/core:lib",
        "//tensorflow/core:master_proto_cc",
    ],
)

cc_library(
    name = "master",
    srcs = ["master.cc"],
    hdrs = ["master.h"],
    deps = [
        ":call_options",
        ":master_env",
        ":master_session",
        ":recent_request_ids",
        ":remote_device",
        ":worker_cache",
        ":worker_interface",
        "//tensorflow/core:core_cpu",
        "//tensorflow/core:core_cpu_internal",
        "//tensorflow/core:framework",
        "//tensorflow/core:lib",
        "//tensorflow/core:lib_internal",
        "//tensorflow/core:master_proto_cc",
        "//tensorflow/core:protos_all_cc",
        "//tensorflow/core:worker_proto_cc",
    ],
)

cc_library(
    name = "master_session",
    srcs = ["master_session.cc"],
    hdrs = ["master_session.h"],
    deps = [
        ":call_options",
        ":master_env",
        ":message_wrappers",
        ":request_id",
        ":scheduler",
        ":worker_cache",
        ":worker_interface",
        "//tensorflow/core:core_cpu",
        "//tensorflow/core:core_cpu_internal",
        "//tensorflow/core:framework",
        "//tensorflow/core:graph",
        "//tensorflow/core:lib",
        "//tensorflow/core:lib_internal",
        "//tensorflow/core:master_proto_cc",
        "//tensorflow/core:protos_all_cc",
        "//tensorflow/core/debug:debug_graph_utils",
    ],
)

cc_library(
    name = "local_master",
    srcs = ["local_master.cc"],
    hdrs = ["local_master.h"],
    deps = [
        ":master",
        ":master_interface",
        "//tensorflow/core:lib",
    ],
)

cc_library(
    name = "rendezvous_mgr_interface",
    srcs = [],
    hdrs = ["rendezvous_mgr_interface.h"],
    deps = [
        ":worker_env",
        "//tensorflow/core:framework_internal",
        "//tensorflow/core:lib",
    ],
)

cc_library(
    name = "scheduler",
    srcs = ["scheduler.cc"],
    hdrs = ["scheduler.h"],
    deps = [
        "//tensorflow/core:core_cpu_internal",
        "//tensorflow/core:framework",
        "//tensorflow/core:graph",
        "//tensorflow/core:tensorflow_opensource",
    ],
)

cc_library(
    name = "base_rendezvous_mgr",
    srcs = ["base_rendezvous_mgr.cc"],
    hdrs = ["base_rendezvous_mgr.h"],
    copts = tf_copts(),
    deps = [
        ":rendezvous_mgr_interface",
        ":worker_cache",
        ":worker_env",
        ":worker_interface",
        ":worker_session",
        "//tensorflow/core:core_cpu_internal",
        "//tensorflow/core:framework",
        "//tensorflow/core:framework_internal",
        "//tensorflow/core:lib",
        "//tensorflow/core:lib_internal",
    ],
)

cc_library(
    name = "master_env",
    hdrs = ["master_env.h"],
    deps = [
        ":worker_cache",
        "//tensorflow/core:framework",
        "//tensorflow/core:protos_all_cc",
        "//tensorflow/core:session_options",
    ],
)

cc_library(
    name = "graph_mgr",
    srcs = ["graph_mgr.cc"],
    hdrs = ["graph_mgr.h"],
    deps = [
        ":message_wrappers",
        ":rendezvous_mgr_interface",
        ":worker_env",
        "//tensorflow/core:core_cpu_internal",
        "//tensorflow/core:framework",
        "//tensorflow/core:graph",
        "//tensorflow/core:lib",
        "//tensorflow/core:lib_internal",
        "//tensorflow/core:protos_all_cc",
        "//tensorflow/core:worker_proto_cc",
        "//tensorflow/core/debug",
    ],
)

cc_library(
    name = "worker_cache_partial",
    srcs = ["worker_cache_partial.cc"],
    hdrs = ["worker_cache_partial.h"],
    deps = [
        ":worker_cache",
        ":worker_interface",
        "//tensorflow/core:core_cpu_internal",
        "//tensorflow/core:framework",
        "//tensorflow/core:lib",
        "//tensorflow/core:worker_proto_cc",
    ],
)

cc_library(
    name = "worker_cache_logger",
    srcs = ["worker_cache_logger.cc"],
    hdrs = ["worker_cache_logger.h"],
    deps = [
        "//tensorflow/core:core_cpu_internal",
        "//tensorflow/core:lib",
        "//tensorflow/core:protos_all_cc",
    ],
)

cc_library(
    name = "server_lib",
    srcs = ["server_lib.cc"],
    hdrs = ["server_lib.h"],
    deps = [
        "//tensorflow/core:framework_internal",
        "//tensorflow/core:lib",
        "//tensorflow/core:protos_all_cc",
    ],
)

tf_cc_test(
    name = "server_lib_test",
    srcs = ["server_lib_test.cc"],
    deps = [
        ":server_lib",
        "//tensorflow/core:lib",
        "//tensorflow/core:test",
        "//tensorflow/core:test_main",
    ],
)

cc_library(
    name = "rpc_collective_executor_mgr",
    srcs = ["rpc_collective_executor_mgr.cc"],
    hdrs = ["rpc_collective_executor_mgr.h"],
    deps = [
        ":base_rendezvous_mgr",
        ":collective_param_resolver_distributed",
        ":collective_rma_distributed",
        ":device_resolver_distributed",
        ":worker_cache",
        "//tensorflow/core:core_cpu_internal",
        "//tensorflow/core:framework",
        "//tensorflow/core:lib",
        "//tensorflow/core:lib_internal",
        "//tensorflow/core:worker_proto_cc",
    ],
)

tf_cc_test(
    name = "rpc_collective_executor_mgr_test",
    srcs = ["rpc_collective_executor_mgr_test.cc"],
    deps = [
        ":collective_param_resolver_distributed",
        ":device_resolver_distributed",
        ":rpc_collective_executor_mgr",
        "//tensorflow/core:core_cpu_internal",
        "//tensorflow/core:framework",
        "//tensorflow/core:lib",
        "//tensorflow/core:protos_all_cc",
        "//tensorflow/core:session_options",
        "//tensorflow/core:test",
        "//tensorflow/core:test_main",
        "//tensorflow/core:worker_proto_cc",
    ],
)

cc_library(
    name = "collective_rma_distributed",
    srcs = ["collective_rma_distributed.cc"],
    hdrs = ["collective_rma_distributed.h"],
    deps = [
        ":cancellable_call",
        ":request_id",
        ":worker_cache",
        "//tensorflow/core:core_cpu_internal",
        "//tensorflow/core:framework",
        "//tensorflow/core:lib",
        "//tensorflow/core:lib_internal",  # protobuf::Any
        "//tensorflow/core:protos_all_cc",
        "//tensorflow/core:worker_proto_cc",
    ],
)

tf_cc_test(
    name = "collective_rma_distributed_test",
    size = "small",
    srcs = ["collective_rma_distributed_test.cc"],
    deps = [
        ":collective_rma_distributed",
        ":device_resolver_distributed",
        ":test_utils",
        "//tensorflow/core:core_cpu_lib",
        "//tensorflow/core:framework",
        "//tensorflow/core:lib",
        "//tensorflow/core:protos_all_cc",
        "//tensorflow/core:test",
        "//tensorflow/core:test_main",
        "//tensorflow/core:testlib",
        "//tensorflow/core:worker_proto_cc",
    ],
)

cc_library(
    name = "collective_param_resolver_distributed",
    srcs = ["collective_param_resolver_distributed.cc"],
    hdrs = ["collective_param_resolver_distributed.h"],
    deps = [
        ":call_options",
        ":cancellable_call",
        ":device_resolver_distributed",
        ":worker_cache",
        "//tensorflow/core:core_cpu_internal",
        "//tensorflow/core:framework",
        "//tensorflow/core:lib",
        "//tensorflow/core:protos_all_cc",
        "//tensorflow/core:worker_proto_cc",
    ],
)

cc_library(
    name = "test_utils",
    srcs = [],
    hdrs = ["test_utils.h"],
    deps = [
        ":worker_cache",
        ":worker_interface",
        "//tensorflow/core:framework",
    ],
)

tf_cc_test(
    name = "collective_param_resolver_distributed_test",
    size = "small",
    srcs = ["collective_param_resolver_distributed_test.cc"],
    deps = [
        ":collective_param_resolver_distributed",
        ":device_resolver_distributed",
        ":test_utils",
        "//tensorflow/core:collective_ops_op_lib",
        "//tensorflow/core:core_cpu_lib",
        "//tensorflow/core:framework",
        "//tensorflow/core:lib",
        "//tensorflow/core:no_op_op_lib",
        "//tensorflow/core:protos_all_cc",
        "//tensorflow/core:sendrecv_ops_op_lib",
        "//tensorflow/core:test",
        "//tensorflow/core:test_main",
        "//tensorflow/core:testlib",
        "//tensorflow/core/kernels:collective_ops",
    ],
)

cc_library(
    name = "device_resolver_distributed",
    srcs = ["device_resolver_distributed.cc"],
    hdrs = ["device_resolver_distributed.h"],
    deps = [
        ":worker_cache",
        "//tensorflow/core:core_cpu_internal",
        "//tensorflow/core:framework",
        "//tensorflow/core:lib",
        "//tensorflow/core:protos_all_cc",
        "//tensorflow/core:worker_proto_cc",
    ],
)

tf_cc_test(
    name = "device_resolver_distributed_test",
    size = "small",
    srcs = ["device_resolver_distributed_test.cc"],
    deps = [
        ":device_resolver_distributed",
        ":test_utils",
        "//tensorflow/core:core_cpu_lib",
        "//tensorflow/core:framework",
        "//tensorflow/core:lib",
        "//tensorflow/core:protos_all_cc",
        "//tensorflow/core:test",
        "//tensorflow/core:test_main",
        "//tensorflow/core:testlib",
        "@com_google_absl//absl/memory",
    ],
)

tf_cuda_cc_test(
    name = "master_test",
    size = "medium",
    srcs = [
        "master_test.cc",
    ],
    linkstatic = tf_kernel_tests_linkstatic(),
    tags = tf_cuda_tests_tags() + [
        "manual",  # TODO(b/27683709): Re-enable when not flaky.
        "notap",  # TODO(b/27683709): Re-enable when not flaky.
        "noguitar",  # TODO(b/27683709): Re-enable when not flaky.
        "no_oss",  # TODO(b/27683709): Re-enable when not flaky.
    ],
    deps = [
        ":master",
        ":remote_device",
        ":worker_interface",
        "//tensorflow:grpc++",
        "//tensorflow/core:control_flow_ops_op_lib",
        "//tensorflow/core:core_cpu",
        "//tensorflow/core:core_cpu_internal",
        "//tensorflow/core:framework",
        "//tensorflow/core:framework_internal",
        "//tensorflow/core:lib",
        "//tensorflow/core:lib_internal",
        "//tensorflow/core:master_proto_cc",
        "//tensorflow/core:protos_all_cc",
        "//tensorflow/core:state_ops_op_lib",
        "//tensorflow/core:test",
        "//tensorflow/core:test_main",
        "//tensorflow/core:testlib",
        "//tensorflow/core/distributed_runtime/rpc:grpc_channel",
        "//tensorflow/core/distributed_runtime/rpc:grpc_master_service_impl",
        "//tensorflow/core/distributed_runtime/rpc:grpc_testlib",
        "//tensorflow/core/distributed_runtime/rpc:grpc_util",
        "//tensorflow/core/distributed_runtime/rpc:grpc_worker_cache",
        "//tensorflow/core/kernels:control_flow_ops",
        "//tensorflow/core/kernels:cwise_op",
        "//tensorflow/core/kernels:dense_update_ops",
        "//tensorflow/core/kernels:identity_op",
        "//tensorflow/core/kernels:variable_ops",
    ],
)

tf_cuda_cc_test(
    name = "remote_device_test",
    size = "small",
    srcs = ["remote_device_test.cc"],
    linkstatic = tf_kernel_tests_linkstatic(),
    tags = tf_cuda_tests_tags() + [
        "manual",
        "notap",  # Memory leak due to b/62910646
        "noguitar",  # Memory leak due to b/62910646
    ],
    deps = [
        ":master",
        ":remote_device",
        ":worker_interface",
        "//tensorflow:grpc++",
        "//tensorflow/core:core_cpu",
        "//tensorflow/core:core_cpu_internal",
        "//tensorflow/core:framework",
        "//tensorflow/core:framework_internal",
        "//tensorflow/core:lib",
        "//tensorflow/core:lib_internal",
        "//tensorflow/core:master_proto_cc",
        "//tensorflow/core:protos_all_cc",
        "//tensorflow/core:test",
        "//tensorflow/core:test_main",
        "//tensorflow/core:testlib",
        "//tensorflow/core/distributed_runtime/rpc:grpc_channel",
        "//tensorflow/core/distributed_runtime/rpc:grpc_testlib",
        "//tensorflow/core/distributed_runtime/rpc:grpc_util",
        "//tensorflow/core/distributed_runtime/rpc:grpc_worker_cache",
    ],
)

tf_cuda_cc_test(
    name = "rpcbench_test",
    size = "small",
    srcs = ["rpcbench_test.cc"],
    linkstatic = 1,
    tags = tf_cuda_tests_tags(),
    deps = [
        "//tensorflow/cc:cc_ops",
        "//tensorflow/core:array_ops_op_lib",
        "//tensorflow/core:bitwise_ops_op_lib",
        "//tensorflow/core:core_cpu",
        "//tensorflow/core:framework",
        "//tensorflow/core:functional_ops_op_lib",
        "//tensorflow/core:lib",
        "//tensorflow/core:math_ops_op_lib",
        "//tensorflow/core:nn_ops_op_lib",
        "//tensorflow/core:protos_all_cc",
        "//tensorflow/core:tensorflow",
        "//tensorflow/core:test",
        "//tensorflow/core:test_main",
        "//tensorflow/core:testlib",
        "//tensorflow/core/distributed_runtime:server_lib",
        "//tensorflow/core/distributed_runtime/rpc:grpc_server_lib",
        "//tensorflow/core/distributed_runtime/rpc:grpc_session",
        "//tensorflow/core/kernels:aggregate_ops",
        "//tensorflow/core/kernels:array",
    ],
)

cc_library(
    name = "request_id",
    srcs = ["request_id.cc"],
    hdrs = ["request_id.h"],
    deps = [
        "//tensorflow/core:lib",
        "//tensorflow/core:lib_internal",
    ],
)

tf_cc_test(
    name = "request_id_test",
    size = "small",
    srcs = ["request_id_test.cc"],
    deps = [
        ":request_id",
        "//tensorflow/core:test",
        "//tensorflow/core:test_main",
    ],
)

cc_library(
    name = "recent_request_ids",
    srcs = ["recent_request_ids.cc"],
    hdrs = ["recent_request_ids.h"],
    deps = [
        ":message_wrappers",
        "//tensorflow/core:lib",
        "//tensorflow/core:worker_proto_cc",
    ],
)

tf_cc_test(
    name = "recent_request_ids_test",
    size = "small",
    srcs = ["recent_request_ids_test.cc"],
    deps = [
        ":recent_request_ids",
        ":request_id",
        "//tensorflow/core:lib",
        "//tensorflow/core:test",
        "//tensorflow/core:test_main",
        "//tensorflow/core:worker_proto_cc",
    ],
)

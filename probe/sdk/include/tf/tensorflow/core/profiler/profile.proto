// This proto intends to match format expected by pprof tool.
syntax = "proto3";

package tensorflow.tfprof.pprof;

message Profile {
  repeated ValueType sample_type = 1;
  repeated Sample sample = 2;
  repeated Mapping mapping = 3;
  repeated Location location = 4;
  repeated Function function = 5;
  repeated string string_table = 6;
  int64 drop_frames = 7;
  int64 keep_frames = 8;
  int64 time_nanos = 9;
  int64 duration_nanos = 10;
  ValueType period_type = 11;
  int64 period = 12;
  repeated int64 comment = 13;
  int64 default_sample_type = 14;
}

message ValueType {
  int64 type = 1;
  int64 unit = 2;
}

message Sample {
  repeated uint64 location_id = 1;
  repeated int64 value = 2;
  repeated Label label = 3;
}

message Label {
  int64 key = 1;
  int64 str = 2;
  int64 num = 3;
}

message Mapping {
  uint64 id = 1;
  uint64 memory_start = 2;
  uint64 memory_limit = 3;
  uint64 file_offset = 4;
  int64 filename = 5;
  int64 build_id = 6;
  bool has_functions = 7;
  bool has_filenames = 8;
  bool has_line_numbers = 9;
  bool has_inline_frames = 10;
}

message Location {
  uint64 id = 1;
  uint64 mapping_id = 2;
  uint64 address = 3;
  repeated Line line = 4;
}

message Line {
  uint64 function_id = 1;
  int64 line = 2;
}

message Function {
  uint64 id = 1;
  int64 name = 2;
  int64 system_name = 3;
  int64 filename = 4;
  int64 start_line = 5;
}

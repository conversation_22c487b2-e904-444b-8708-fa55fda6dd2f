syntax = "proto3";

package tensorflow.profiler;

// A 'Trace' contains metadata for the individual traces of a system.
message Trace {
  // The devices that this trace has information about. Maps from device_id to
  // more data about the specific device.
  map<uint32, Device> devices = 1;

  // All trace events capturing in the profiling period.
  repeated TraceEvent trace_events = 4;
}

// A 'device' is a physical entity in the system and is comprised of several
// resources.
message Device {
  // The name of the device.
  string name = 1;

  // The id of this device, unique in a single trace.
  uint32 device_id = 2;

  // The resources on this device, keyed by resource_id;
  map<uint32, Resource> resources = 3;
}

// A 'resource' generally is a specific computation component on a device. These
// can range from threads on CPUs to specific arithmetic units on hardware
// devices.
message Resource {
  // The name of the resource.
  string name = 1;

  // The id of the resource. Unique within a device.
  uint32 resource_id = 2;
}

message TraceEvent {
  // The id of the device that this event occurred on. The full dataset should
  // have this device present in the Trace object.
  uint32 device_id = 1;

  // The id of the resource that this event occurred on. The full dataset should
  // have this resource present in the Device object of the Trace object. A
  // resource_id is unique on a specific device, but not necessarily within the
  // trace.
  uint32 resource_id = 2;

  // The name of this trace event.
  string name = 3;

  // The timestamp that this event occurred at (in picos since tracing started).
  uint64 timestamp_ps = 9;

  // The duration of the event in picoseconds if applicable.
  // Events without duration are called instant events.
  uint64 duration_ps = 10;

  // Extra arguments that will be displayed in trace view.
  map<string, string> args = 11;
}

package(
    default_visibility = ["//tensorflow:internal"],
)

licenses(["notice"])  # Apache 2.0

load("//tensorflow:tensorflow.bzl", "tf_cc_test")

cc_library(
    name = "checker",
    hdrs = ["checker.h"],
    deps = [
        "//tensorflow/core:lib",
        "//tensorflow/core/profiler:protos_all_cc",
        "//tensorflow/core/profiler/internal:tfprof_stats",
    ],
)

cc_library(
    name = "internal_checker_runner_dummy",
    srcs = ["internal_checker_runner_dummy.cc"],
    hdrs = ["internal_checker_runner.h"],
    deps = [
        "//tensorflow/core/profiler:protos_all_cc",
        "//tensorflow/core/profiler/internal:tfprof_utils",
    ],
)

cc_library(
    name = "accelerator_utilization_checker",
    hdrs = ["accelerator_utilization_checker.h"],
    deps = [
        ":checker",
    ],
)

cc_library(
    name = "operation_checker",
    hdrs = ["operation_checker.h"],
    deps = [
        ":checker",
    ],
)

cc_library(
    name = "expensive_operation_checker",
    hdrs = ["expensive_operation_checker.h"],
    deps = [
        ":checker",
    ],
)

cc_library(
    name = "tfprof_advisor",
    hdrs = ["tfprof_advisor.h"],
    deps = [
        ":accelerator_utilization_checker",
        ":checker",
        ":expensive_operation_checker",
        ":internal_checker_runner_dummy",
        ":operation_checker",
        "//tensorflow/core/profiler:protos_all_cc",
    ],
)

tf_cc_test(
    name = "tfprof_advisor_test",
    srcs = ["tfprof_advisor_test.cc"],
    deps = [
        ":tfprof_advisor",
        "//tensorflow/core:lib",
        "//tensorflow/core:test",
        "//tensorflow/core/profiler/internal:tfprof_tf_testlib",
    ],
)

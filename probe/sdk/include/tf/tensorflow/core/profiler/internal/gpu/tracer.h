/* Copyright 2016 The TensorFlow Authors All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/
#ifndef TENSORFLOW_CORE_PROFILER_INTERNAL_GPU_TRACER_H_
#define TENSORFLOW_CORE_PROFILER_INTERNAL_GPU_TRACER_H_

#include "tensorflow/core/platform/device_tracer.h"
#include "tensorflow/core/profiler/internal/profiler_interface.h"

namespace tensorflow {
namespace profiler {
namespace gpu {

class Tracer : public ProfilerInterface {
 public:
  static std::unique_ptr<ProfilerInterface> Create();

  Status Start() override;

  Status Stop() override;

  Status CollectData(RunMetadata* run_metadata) override;

 private:
  Tracer();

  // Trace is neither copyable nor movable.
  Tracer(const Tracer&) = delete;
  Tracer& operator=(const Tracer&) = delete;

  std::unique_ptr<DeviceTracer> device_tracer_;
};

}  // namespace gpu
}  // namespace profiler
}  // namespace tensorflow
#endif  // TENSORFLOW_CORE_PROFILER_INTERNAL_GPU_TRACER_H_

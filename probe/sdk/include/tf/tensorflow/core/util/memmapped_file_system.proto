/* Copyright 2016 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/
syntax = "proto3";

package tensorflow;
option cc_enable_arenas = true;

// A message that describes one region of memmapped file.
message MemmappedFileSystemDirectoryElement {
  uint64 offset = 1;
  string name = 2;
}

// A directory of regions in a memmapped file.
message MemmappedFileSystemDirectory {
  repeated MemmappedFileSystemDirectoryElement element = 1;
}

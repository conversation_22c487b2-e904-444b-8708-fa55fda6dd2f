# Copyright 2016 The Bazel Authors. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

major_version: "local"
minor_version: ""

# Android tooling requires a default toolchain for the armeabi-v7a cpu.
toolchain {
  abi_version: "armeabi-v7a"
  abi_libc_version: "armeabi-v7a"
  builtin_sysroot: ""
  compiler: "compiler"
  host_system_name: "armeabi-v7a"
  needsPic: true
  supports_gold_linker: false
  supports_incremental_linker: false
  supports_fission: false
  supports_interface_shared_objects: false
  supports_normalizing_ar: false
  supports_start_end_lib: false
  target_libc: "armeabi-v7a"
  target_cpu: "armeabi-v7a"
  target_system_name: "armeabi-v7a"
  toolchain_identifier: "stub_armeabi-v7a"

  tool_path { name: "ar" path: "/bin/false" }
  tool_path { name: "compat-ld" path: "/bin/false" }
  tool_path { name: "cpp" path: "/bin/false" }
  tool_path { name: "dwp" path: "/bin/false" }
  tool_path { name: "gcc" path: "/bin/false" }
  tool_path { name: "gcov" path: "/bin/false" }
  tool_path { name: "ld" path: "/bin/false" }

  tool_path { name: "nm" path: "/bin/false" }
  tool_path { name: "objcopy" path: "/bin/false" }
  tool_path { name: "objdump" path: "/bin/false" }
  tool_path { name: "strip" path: "/bin/false" }
  linking_mode_flags { mode: DYNAMIC }
}

toolchain {
  toolchain_identifier: "linux_gnu_x86"
  abi_version: "gcc"
  abi_libc_version: "glibc_2.19"
  builtin_sysroot: ""
  compiler: "clang"
  host_system_name: "i686-unknown-linux-gnu"
  needsPic: true
  supports_gold_linker: true
  supports_incremental_linker: false
  supports_fission: false
  supports_interface_shared_objects: false
  supports_normalizing_ar: false
  supports_start_end_lib: true
  target_libc: "glibc_2.19"
  target_cpu: "k8"
  target_system_name: "x86_64-unknown-linux-gnu"
  cxx_flag: "-std=c++0x"
  linker_flag: "-fuse-ld=gold"
  linker_flag: "-Wl,-no-as-needed"
  linker_flag: "-Wl,-z,relro,-z,now"
  linker_flag: "-B/usr/local/bin"
  linker_flag: "-lstdc++"
  linker_flag: "-lm"
  cxx_builtin_include_directory: "/usr/local/include"
  cxx_builtin_include_directory: "/usr/local/lib/clang/7.0.0/include"
  cxx_builtin_include_directory: "/usr/include/x86_64-linux-gnu"
  cxx_builtin_include_directory: "/usr/include"
  cxx_builtin_include_directory: "/usr/include/c++/4.9"
  cxx_builtin_include_directory: "/usr/include/x86_64-linux-gnu/c++/4.9"
  cxx_builtin_include_directory: "/usr/include/c++/4.9/backward"
  objcopy_embed_flag: "-I"
  objcopy_embed_flag: "binary"
  unfiltered_cxx_flag: "-no-canonical-prefixes"
  unfiltered_cxx_flag: "-Wno-builtin-macro-redefined"
  unfiltered_cxx_flag: "-D__DATE__=\"redacted\""
  unfiltered_cxx_flag: "-D__TIMESTAMP__=\"redacted\""
  unfiltered_cxx_flag: "-D__TIME__=\"redacted\""
  compiler_flag: "-U_FORTIFY_SOURCE"
  compiler_flag: "-fstack-protector"
  compiler_flag: "-Wall"
  compiler_flag: "-Wthread-safety"
  compiler_flag: "-Wself-assign"
  compiler_flag: "-fcolor-diagnostics"
  compiler_flag: "-fno-omit-frame-pointer"
  tool_path {name: "ar" path: "/usr/bin/ar" }
  tool_path {name: "ld" path: "/usr/bin/ld" }
  tool_path {name: "cpp" path: "/usr/bin/cpp" }
  tool_path {name: "gcc" path: "/usr/local/bin/clang" }
  tool_path {name: "dwp" path: "/usr/bin/dwp" }
  tool_path {name: "gcov" path: "None" }
  tool_path {name: "nm" path: "/usr/bin/nm" }
  tool_path {name: "objcopy" path: "/usr/bin/objcopy" }
  tool_path {name: "objdump" path: "/usr/bin/objdump" }
  tool_path {name: "strip" path: "/usr/bin/strip" }

  compilation_mode_flags {
    mode: DBG
    compiler_flag: "-g"
  }
  compilation_mode_flags {
    mode: OPT
    compiler_flag: "-g0"
    compiler_flag: "-O2"
    compiler_flag: "-D_FORTIFY_SOURCE=1"
    compiler_flag: "-DNDEBUG"
    compiler_flag: "-ffunction-sections"
    compiler_flag: "-fdata-sections"
    linker_flag: "-Wl,--gc-sections"
  }
  linking_mode_flags { mode: DYNAMIC }


    feature {
      name: 'coverage'
      provides: 'profile'
      flag_set {
        action: 'preprocess-assemble'
        action: 'c-compile'
        action: 'c++-compile'
        action: 'c++-header-parsing'
        action: 'c++-module-compile'
        flag_group {
        flag: '--coverage'
      }
      }
      flag_set {
        action: 'c++-link-dynamic-library'
        action: 'c++-link-nodeps-dynamic-library'
        action: 'c++-link-executable'
        flag_group {
        flag: '--coverage'
      }
      }
    }
  

  feature {
    name: 'fdo_optimize'
    provides: 'profile'
    flag_set {
      action: 'c-compile'
      action: 'c++-compile'
      expand_if_all_available: 'fdo_profile_path'
      flag_group {
        flag: '-fprofile-use=%{fdo_profile_path}'
        flag: '-fprofile-correction',
      }
    }
  }
}

toolchain {
  toolchain_identifier: "msys_x64_mingw"
  abi_version: "local"
  abi_libc_version: "local"
  builtin_sysroot: ""
  compiler: "mingw-gcc"
  host_system_name: "local"
  needsPic: false
  target_libc: "mingw"
  target_cpu: "x64_windows"
  target_system_name: "local"

  artifact_name_pattern {
     category_name: 'executable'
     prefix: ''
     extension: '.exe'
  }



  linking_mode_flags { mode: DYNAMIC }
}

toolchain {
  toolchain_identifier: "msvc_x64"
  host_system_name: "local"
  target_system_name: "local"

  abi_version: "local"
  abi_libc_version: "local"
  target_cpu: "x64_windows"
  compiler: "msvc-cl"
  target_libc: "msvcrt"
  default_python_version: "python2.7"



  tool_path {
    name: "ar"
    path: ""
  }
  tool_path {
    name: "ml"
    path: ""
  }
  tool_path {
    name: "cpp"
    path: ""
  }
  tool_path {
    name: "gcc"
    path: ""
  }
  tool_path {
    name: "gcov"
    path: "wrapper/bin/msvc_nop.bat"
  }
  tool_path {
    name: "ld"
    path: ""
  }
  tool_path {
    name: "nm"
    path: "wrapper/bin/msvc_nop.bat"
  }
  tool_path {
    name: "objcopy"
    path: "wrapper/bin/msvc_nop.bat"
  }
  tool_path {
    name: "objdump"
    path: "wrapper/bin/msvc_nop.bat"
  }
  tool_path {
    name: "strip"
    path: "wrapper/bin/msvc_nop.bat"
  }
  supports_gold_linker: false
  supports_start_end_lib: false
  supports_interface_shared_objects: true
  supports_incremental_linker: false
  supports_normalizing_ar: true
  needsPic: false

  # TODO(pcloudy): Review those flags below, they should be defined by cl.exe
  compiler_flag: "/DCOMPILER_MSVC"

  # Don't define min/max macros in windows.h.
  compiler_flag: "/DNOMINMAX"

  # Platform defines.
  compiler_flag: "/D_WIN32_WINNT=0x0601"
  # Turn off warning messages.
  compiler_flag: "/D_CRT_SECURE_NO_DEPRECATE"
  compiler_flag: "/D_CRT_SECURE_NO_WARNINGS"

  # Useful options to have on for compilation.
  # Increase the capacity of object files to 2^32 sections.
  compiler_flag: "/bigobj"
  # Allocate 500MB for precomputed headers.
  compiler_flag: "/Zm500"
  # Catch C++ exceptions only and tell the compiler to assume that functions declared
  # as extern "C" never throw a C++ exception.
  compiler_flag: "/EHsc"

  # Globally disabled warnings.
  # Don't warn about elements of array being be default initialized.
  compiler_flag: "/wd4351"
  # Don't warn about no matching delete found.
  compiler_flag: "/wd4291"
  # Don't warn about diamond inheritance patterns.
  compiler_flag: "/wd4250"
  # Don't warn about insecure functions (e.g. non _s functions).
  compiler_flag: "/wd4996"

  linker_flag: "/MACHINE:X64"

  feature {
    name: "no_legacy_features"
  }

  artifact_name_pattern {
     category_name: 'object_file'
     prefix: ''
     extension: '.obj'
  }

  artifact_name_pattern {
     category_name: 'static_library'
     prefix: ''
     extension: '.lib'
  }

  artifact_name_pattern {
     category_name: 'alwayslink_static_library'
     prefix: ''
     extension: '.lo.lib'
  }

  artifact_name_pattern {
     category_name: 'executable'
     prefix: ''
     extension: '.exe'
  }

  artifact_name_pattern {
     category_name: 'dynamic_library'
     prefix: ''
     extension: '.dll'
  }

  artifact_name_pattern {
     category_name: 'interface_library'
     prefix: ''
     extension: '.if.lib'
  }

  # Suppress startup banner.
  feature {
    name: "nologo"
    flag_set {
      action: "c-compile"
      action: "c++-compile"
      action: "c++-module-compile"
      action: "c++-module-codegen"
      action: "c++-header-parsing"
      action: "assemble"
      action: "preprocess-assemble"
      action: "c++-link-executable"
      action: "c++-link-dynamic-library"
      action: "c++-link-nodeps-dynamic-library"
      action: "c++-link-static-library"
      flag_group {
        flag: "/nologo"
      }
    }
  }

  feature {
    name: 'has_configured_linker_path'
  }

  # This feature indicates strip is not supported, building stripped binary will just result a copy of orignial binary
  feature {
    name: 'no_stripping'
  }

  # This feature indicates this is a toolchain targeting Windows.
  feature {
    name: 'targets_windows'
    implies: 'copy_dynamic_libraries_to_binary'
    enabled: true
  }

  feature {
    name: 'copy_dynamic_libraries_to_binary'
  }

  action_config {
    config_name: 'assemble'
    action_name: 'assemble'
    tool {
      tool_path: ''
    }
    implies: 'compiler_input_flags'
    implies: 'compiler_output_flags'
    implies: 'nologo'
    implies: 'msvc_env'
    implies: 'sysroot'
  }

  action_config {
    config_name: 'preprocess-assemble'
    action_name: 'preprocess-assemble'
    tool {
      tool_path: ''
    }
    implies: 'compiler_input_flags'
    implies: 'compiler_output_flags'
    implies: 'nologo'
    implies: 'msvc_env'
    implies: 'sysroot'
  }

  action_config {
    config_name: 'c-compile'
    action_name: 'c-compile'
    tool {
      tool_path: ''
    }
    implies: 'compiler_input_flags'
    implies: 'compiler_output_flags'
    implies: 'legacy_compile_flags'
    implies: 'nologo'
    implies: 'msvc_env'
    implies: 'parse_showincludes'
    implies: 'user_compile_flags'
    implies: 'sysroot'
    implies: 'unfiltered_compile_flags'
  }

  action_config {
    config_name: 'c++-compile'
    action_name: 'c++-compile'
    tool {
      tool_path: ''
    }
    implies: 'compiler_input_flags'
    implies: 'compiler_output_flags'
    implies: 'legacy_compile_flags'
    implies: 'nologo'
    implies: 'msvc_env'
    implies: 'parse_showincludes'
    implies: 'user_compile_flags'
    implies: 'sysroot'
    implies: 'unfiltered_compile_flags'
  }

  action_config {
    config_name: 'c++-link-executable'
    action_name: 'c++-link-executable'
    tool {
      tool_path: ''
    }
    implies: 'nologo'
    implies: 'linkstamps'
    implies: 'output_execpath_flags'
    implies: 'input_param_flags'
    implies: 'user_link_flags'
    implies: 'legacy_link_flags'
    implies: 'linker_subsystem_flag'
    implies: 'linker_param_file'
    implies: 'msvc_env'
    implies: 'no_stripping'
  }

  action_config {
    config_name: 'c++-link-dynamic-library'
    action_name: 'c++-link-dynamic-library'
    tool {
      tool_path: ''
    }
    implies: 'nologo'
    implies: 'shared_flag'
    implies: 'linkstamps'
    implies: 'output_execpath_flags'
    implies: 'input_param_flags'
    implies: 'user_link_flags'
    implies: 'legacy_link_flags'
    implies: 'linker_subsystem_flag'
    implies: 'linker_param_file'
    implies: 'msvc_env'
    implies: 'no_stripping'
    implies: 'has_configured_linker_path'
    implies: 'def_file'
  }

  action_config {
      config_name: 'c++-link-nodeps-dynamic-library'
      action_name: 'c++-link-nodeps-dynamic-library'
      tool {
        tool_path: ''
      }
      implies: 'nologo'
      implies: 'shared_flag'
      implies: 'linkstamps'
      implies: 'output_execpath_flags'
      implies: 'input_param_flags'
      implies: 'user_link_flags'
      implies: 'legacy_link_flags'
      implies: 'linker_subsystem_flag'
      implies: 'linker_param_file'
      implies: 'msvc_env'
      implies: 'no_stripping'
      implies: 'has_configured_linker_path'
      implies: 'def_file'
    }

  action_config {
    config_name: 'c++-link-static-library'
    action_name: 'c++-link-static-library'
    tool {
      tool_path: ''
    }
    implies: 'nologo'
    implies: 'archiver_flags'
    implies: 'input_param_flags'
    implies: 'linker_param_file'
    implies: 'msvc_env'
  }

  # TODO(b/65151735): Remove legacy_compile_flags feature when legacy fields are
  # not used in this crosstool
  feature {
    name: 'legacy_compile_flags'
    flag_set {
      expand_if_all_available: 'legacy_compile_flags'
      action: 'preprocess-assemble'
      action: 'c-compile'
      action: 'c++-compile'
      action: 'c++-header-parsing'
      action: 'c++-module-compile'
      action: 'c++-module-codegen'
      flag_group {
        iterate_over: 'legacy_compile_flags'
        flag: '%{legacy_compile_flags}'
      }
    }
  }

  feature {
    name: "msvc_env"
    env_set {
      action: "c-compile"
      action: "c++-compile"
      action: "c++-module-compile"
      action: "c++-module-codegen"
      action: "c++-header-parsing"
      action: "assemble"
      action: "preprocess-assemble"
      action: "c++-link-executable"
      action: "c++-link-dynamic-library"
      action: "c++-link-nodeps-dynamic-library"
      action: "c++-link-static-library"
      env_entry {
        key: "PATH"
        value: ""
      }
      env_entry {
        key: "TMP"
        value: ""
      }
      env_entry {
        key: "TEMP"
        value: ""
      }
    }
    implies: 'msvc_compile_env'
    implies: 'msvc_link_env'
  }

  feature {
    name: "msvc_compile_env"
    env_set {
      action: "c-compile"
      action: "c++-compile"
      action: "c++-module-compile"
      action: "c++-module-codegen"
      action: "c++-header-parsing"
      action: "assemble"
      action: "preprocess-assemble"
      env_entry {
        key: "INCLUDE"
        value: ""
      }
    }
  }

  feature {
    name: "msvc_link_env"
    env_set {
      action: "c++-link-executable"
      action: "c++-link-dynamic-library"
      action: "c++-link-nodeps-dynamic-library"
      action: "c++-link-static-library"
      env_entry {
        key: "LIB"
        value: ""
      }
    }
  }

  feature {
    name: 'include_paths'
    flag_set {
      action: "assemble"
      action: 'preprocess-assemble'
      action: 'c-compile'
      action: 'c++-compile'
      action: 'c++-header-parsing'
      action: 'c++-module-compile'
      flag_group {
        iterate_over: 'quote_include_paths'
        flag: '/I%{quote_include_paths}'
      }
      flag_group {
        iterate_over: 'include_paths'
        flag: '/I%{include_paths}'
      }
      flag_group {
        iterate_over: 'system_include_paths'
        flag: '/I%{system_include_paths}'
      }
    }
  }

  feature {
    name: "preprocessor_defines"
    flag_set {
      action: "assemble"
      action: "preprocess-assemble"
      action: "c-compile"
      action: "c++-compile"
      action: "c++-header-parsing"
      action: "c++-module-compile"
      flag_group {
        flag: "/D%{preprocessor_defines}"
        iterate_over: "preprocessor_defines"
      }
    }
  }

  # Tell Bazel to parse the output of /showIncludes
  feature {
    name: 'parse_showincludes'
    flag_set {
      action: 'preprocess-assemble'
      action: 'c-compile'
      action: 'c++-compile'
      action: 'c++-module-compile'
      action: 'c++-header-parsing'
      flag_group {
        flag: "/showIncludes"
      }
    }
  }


  feature {
    name: 'generate_pdb_file'
    requires: {
      feature: 'dbg'
    }
    requires: {
      feature: 'fastbuild'
    }
  }

  feature {
    name: 'shared_flag'
    flag_set {
      action: 'c++-link-dynamic-library'
      action: "c++-link-nodeps-dynamic-library"
      flag_group {
        flag: '/DLL'
      }
    }
  }

  feature {
    name: 'linkstamps'
    flag_set {
      action: 'c++-link-executable'
      action: 'c++-link-dynamic-library'
      action: "c++-link-nodeps-dynamic-library"
      expand_if_all_available: 'linkstamp_paths'
      flag_group {
        iterate_over: 'linkstamp_paths'
        flag: '%{linkstamp_paths}'
      }
    }
  }

  feature {
    name: 'output_execpath_flags'
    flag_set {
      expand_if_all_available: 'output_execpath'
      action: 'c++-link-executable'
      action: 'c++-link-dynamic-library'
      action: "c++-link-nodeps-dynamic-library"
      flag_group {
        flag: '/OUT:%{output_execpath}'
      }
    }
  }

  feature {
    name: 'archiver_flags'
    flag_set {
      expand_if_all_available: 'output_execpath'
      action: 'c++-link-static-library'
      flag_group {
        flag: '/OUT:%{output_execpath}'
      }
    }
  }

  feature {
    name: 'input_param_flags'
    flag_set {
      expand_if_all_available: 'interface_library_output_path'
      action: 'c++-link-dynamic-library'
      action: "c++-link-nodeps-dynamic-library"
      flag_group {
        flag: "/IMPLIB:%{interface_library_output_path}"
      }
    }
    flag_set {
      expand_if_all_available: 'libopts'
      action: 'c++-link-executable'
      action: 'c++-link-dynamic-library'
      action: "c++-link-nodeps-dynamic-library"
      flag_group {
        iterate_over: 'libopts'
        flag: '%{libopts}'
      }
    }
    flag_set {
      expand_if_all_available: 'libraries_to_link'
      action: 'c++-link-executable'
      action: 'c++-link-dynamic-library'
      action: "c++-link-nodeps-dynamic-library"
      action: 'c++-link-static-library'
      flag_group {
        iterate_over: 'libraries_to_link'
        flag_group {
          expand_if_equal: {
            variable: 'libraries_to_link.type'
            value: 'object_file_group'
          }
          iterate_over: 'libraries_to_link.object_files'
          flag_group {
            flag: '%{libraries_to_link.object_files}'
          }
        }
        flag_group {
          expand_if_equal: {
            variable: 'libraries_to_link.type'
            value: 'object_file'
          }
          flag_group {
            flag: '%{libraries_to_link.name}'
          }
        }
        flag_group {
          expand_if_equal: {
            variable: 'libraries_to_link.type'
            value: 'interface_library'
          }
          flag_group {
            flag: '%{libraries_to_link.name}'
          }
        }
        flag_group {
          expand_if_equal: {
            variable: 'libraries_to_link.type'
            value: 'static_library'
          }
          flag_group {
            expand_if_false: 'libraries_to_link.is_whole_archive'
            flag: '%{libraries_to_link.name}'
          }
          flag_group {
            expand_if_true: 'libraries_to_link.is_whole_archive'
            flag: '/WHOLEARCHIVE:%{libraries_to_link.name}'
          }
        }
      }
    }
  }

  # Since this feature is declared earlier in the CROSSTOOL than
  # "user_link_flags", this feature will be applied prior to it anwyhere they
  # are both implied. And since "user_link_flags" contains the linkopts from
  # the build rule, this allows the user to override the /SUBSYSTEM in the BUILD
  # file.
  feature {
    name: 'linker_subsystem_flag'
    flag_set {
      action: 'c++-link-executable'
      action: 'c++-link-dynamic-library'
      action: "c++-link-nodeps-dynamic-library"
      flag_group {
        flag: '/SUBSYSTEM:CONSOLE'
      }
    }
  }

  # The "user_link_flags" contains user-defined linkopts (from build rules)
  # so it should be defined after features that declare user-overridable flags.
  # For example the "linker_subsystem_flag" defines a default "/SUBSYSTEM" flag
  # but we want to let the user override it, therefore "link_flag_subsystem" is
  # defined earlier in the CROSSTOOL file than "user_link_flags".
  feature {
    name: 'user_link_flags'
    flag_set {
      expand_if_all_available: 'user_link_flags'
      action: 'c++-link-executable'
      action: 'c++-link-dynamic-library'
      action: "c++-link-nodeps-dynamic-library"
      flag_group {
        iterate_over: 'user_link_flags'
        flag: '%{user_link_flags}'
      }
    }
  }
  feature {
    name: 'legacy_link_flags'
    flag_set {
      expand_if_all_available: 'legacy_link_flags'
      action: 'c++-link-executable'
      action: 'c++-link-dynamic-library'
      action: "c++-link-nodeps-dynamic-library"
      flag_group {
        iterate_over: 'legacy_link_flags'
        flag: '%{legacy_link_flags}'
      }
    }
  }

  feature {
    name: 'linker_param_file'
    flag_set {
      expand_if_all_available: 'linker_param_file'
      action: 'c++-link-executable'
      action: 'c++-link-dynamic-library'
      action: "c++-link-nodeps-dynamic-library"
      action: 'c++-link-static-library'
      flag_group {
        flag: '@%{linker_param_file}'
      }
    }
  }

  feature {
    name: 'static_link_msvcrt'
  }

  feature {
    name: 'static_link_msvcrt_no_debug'
    flag_set {
      action: 'c-compile'
      action: 'c++-compile'
      flag_group {
        flag: "/MT"
      }
    }
    flag_set {
      action: 'c++-link-executable'
      action: 'c++-link-dynamic-library'
      action: "c++-link-nodeps-dynamic-library"
      flag_group {
        flag: "/DEFAULTLIB:libcmt.lib"
      }
    }
    requires: { feature: 'fastbuild'}
    requires: { feature: 'opt'}
  }

  feature {
    name: 'dynamic_link_msvcrt_no_debug'
    flag_set {
      action: 'c-compile'
      action: 'c++-compile'
      flag_group {
        flag: "/MD"
      }
    }
    flag_set {
      action: 'c++-link-executable'
      action: 'c++-link-dynamic-library'
      action: "c++-link-nodeps-dynamic-library"
      flag_group {
        flag: "/DEFAULTLIB:msvcrt.lib"
      }
    }
    requires: { feature: 'fastbuild'}
    requires: { feature: 'opt'}
  }

  feature {
    name: 'static_link_msvcrt_debug'
    flag_set {
      action: 'c-compile'
      action: 'c++-compile'
      flag_group {
        flag: "/MTd"
      }
    }
    flag_set {
      action: 'c++-link-executable'
      action: 'c++-link-dynamic-library'
      action: "c++-link-nodeps-dynamic-library"
      flag_group {
        flag: "/DEFAULTLIB:libcmtd.lib"
      }
    }
    requires: { feature: 'dbg'}
  }

  feature {
    name: 'dynamic_link_msvcrt_debug'
    flag_set {
      action: 'c-compile'
      action: 'c++-compile'
      flag_group {
        flag: "/MDd"
      }
    }
    flag_set {
      action: 'c++-link-executable'
      action: 'c++-link-dynamic-library'
      action: "c++-link-nodeps-dynamic-library"
      flag_group {
        flag: "/DEFAULTLIB:msvcrtd.lib"
      }
    }
    requires: { feature: 'dbg'}
  }

  feature {
    name: 'dbg'
    flag_set {
      action: 'c-compile'
      action: 'c++-compile'
      flag_group {
        flag: "/Od"
        flag: "/Z7"
      }
    }
    flag_set {
      action: 'c++-link-executable'
      action: 'c++-link-dynamic-library'
      action: "c++-link-nodeps-dynamic-library"
      flag_group {
        flag: ""
        flag: "/INCREMENTAL:NO"
      }
    }
    implies: 'generate_pdb_file'
  }

  feature {
    name: 'fastbuild'
    flag_set {
      action: 'c-compile'
      action: 'c++-compile'
      flag_group {
        flag: "/Od"
        flag: "/Z7"
      }
    }
    flag_set {
      action: 'c++-link-executable'
      action: 'c++-link-dynamic-library'
      action: "c++-link-nodeps-dynamic-library"
      flag_group {
        flag: ""
        flag: "/INCREMENTAL:NO"
      }
    }
    implies: 'generate_pdb_file'
  }

  feature {
    name: 'opt'
    flag_set {
      action: 'c-compile'
      action: 'c++-compile'
      flag_group {
        flag: "/O2" # Implies /Og /Oi /Ot /Oy /Ob2 /Gs /GF /Gy
      }
    }
    implies: 'frame_pointer'
  }

  # Keep stack frames for debugging, even in opt mode.
  # Must come after /O1, /O2 and /Ox.
  feature {
    name: "frame_pointer"
    flag_set {
      action: "c-compile"
      action: "c++-compile"
      flag_group {
        flag: "/Oy-"
      }
    }
  }

  # Remove assert/DCHECKs in opt mode.
  # You can have them back with --features=-disable_assertions.
  feature {
    name: 'disable_assertions'
    enabled: true
    flag_set {
      action: 'c-compile'
      action: 'c++-compile'
      with_feature: {
        feature: 'opt'
      }
      flag_group {
        flag: "/DNDEBUG"
      }
    }
  }

  feature {
    name: "determinism"
    enabled: true
    flag_set {
      action: "c-compile"
      action: "c++-compile"
      flag_group {
        # Make C++ compilation deterministic. Use linkstamping instead of these
        # compiler symbols.
        # TODO: detect clang on Windows and use "-Wno-builtin-macro-redefined"
        flag: "/wd4117" # Trying to define or undefine a predefined macro
        flag: "-D__DATE__=\"redacted\""
        flag: "-D__TIMESTAMP__=\"redacted\""
        flag: "-D__TIME__=\"redacted\""
      }
    }
  }

  feature {
    name: 'treat_warnings_as_errors'
    flag_set {
      action: 'c-compile'
      action: 'c++-compile'
      flag_group {
        flag: "/WX"
      }
    }
  }

  # Trade slower build time for smaller binary
  feature {
    name: 'smaller_binary'
    enabled: true
    flag_set {
      action: 'c-compile'
      action: 'c++-compile'
      with_feature: {
        feature: 'opt'
      }
      flag_group {
        flag: "/Gy" # Enable function-level linking (-ffunction-sections)
        flag: "/Gw" # Optimize global data (-fdata-sections)
      }
    }
    flag_set {
      action: 'c++-link-executable'
      action: 'c++-link-dynamic-library',
      action: 'c++-link-nodeps-dynamic-library'
      with_feature: {
        feature: 'opt'
      }
      flag_group {
        flag: '/OPT:ICF' # Fold identical functions
        flag: '/OPT:REF' # Eliminate unreferenced functions and data
      }
    }
  }

  # Suppress warnings that most users do not care
  feature {
    name: 'ignore_noisy_warnings'
    enabled: true
    flag_set {
      action: 'c++-link-static-library'
      flag_group {
        # Suppress 'object file does not define any public symbols' warning
        flag: '/ignore:4221'
      }
    }
  }

  feature {
    name: 'user_compile_flags'
    flag_set {
      expand_if_all_available: 'user_compile_flags'
      action: 'preprocess-assemble'
      action: 'c-compile'
      action: 'c++-compile'
      action: 'c++-header-parsing'
      action: 'c++-module-compile'
      action: 'c++-module-codegen'
      flag_group {
        iterate_over: 'user_compile_flags'
        flag: '%{user_compile_flags}'
      }
    }
  }

  feature {
    name: 'sysroot'
    flag_set {
      expand_if_all_available: 'sysroot'
      action: 'assemble'
      action: 'preprocess-assemble'
      action: 'c-compile'
      action: 'c++-compile'
      action: 'c++-header-parsing'
      action: 'c++-module-compile'
      action: 'c++-module-codegen'
      action: 'c++-link-executable'
      action: 'c++-link-dynamic-library'
      action: "c++-link-nodeps-dynamic-library"
      flag_group {
        iterate_over: 'sysroot'
        flag: '--sysroot=%{sysroot}'
      }
    }
  }

  feature {
    name: 'unfiltered_compile_flags'
    flag_set {
      expand_if_all_available: 'unfiltered_compile_flags'
      action: 'preprocess-assemble'
      action: 'c-compile'
      action: 'c++-compile'
      action: 'c++-header-parsing'
      action: 'c++-module-compile'
      action: 'c++-module-codegen'
      flag_group {
        iterate_over: 'unfiltered_compile_flags'
        flag: '%{unfiltered_compile_flags}'
      }
    }
  }

  feature {
    name: 'compiler_output_flags'
    flag_set {
      action: 'assemble'
      flag_group {
        expand_if_all_available: 'output_file'
        expand_if_none_available: 'output_assembly_file'
        expand_if_none_available: 'output_preprocess_file'
        flag: '/Fo%{output_file}'
        flag: '/Zi'
      }
    }
    flag_set {
      action: 'preprocess-assemble'
      action: 'c-compile'
      action: 'c++-compile'
      action: 'c++-header-parsing'
      action: 'c++-module-compile'
      action: 'c++-module-codegen'
      flag_group {
        expand_if_all_available: 'output_file'
        expand_if_none_available: 'output_assembly_file'
        expand_if_none_available: 'output_preprocess_file'
        flag: '/Fo%{output_file}'
      }
      flag_group {
        expand_if_all_available: 'output_file'
        expand_if_all_available: 'output_assembly_file'
        flag: '/Fa%{output_file}'
      }
      flag_group {
        expand_if_all_available: 'output_file'
        expand_if_all_available: 'output_preprocess_file'
        flag: '/P'
        flag: '/Fi%{output_file}'
      }
    }
  }

  feature {
    name: 'compiler_input_flags'
    flag_set {
      action: 'assemble'
      action: 'preprocess-assemble'
      action: 'c-compile'
      action: 'c++-compile'
      action: 'c++-header-parsing'
      action: 'c++-module-compile'
      action: 'c++-module-codegen'
      flag_group {
        expand_if_all_available: 'source_file'
        flag: '/c'
        flag: '%{source_file}'
      }
    }
  }

  feature {
    name : 'def_file',
    flag_set {
      expand_if_all_available: 'def_file_path'
      action: 'c++-link-executable'
      action: 'c++-link-dynamic-library'
      action: "c++-link-nodeps-dynamic-library"
      flag_group {
        flag: "/DEF:%{def_file_path}"
        # We can specify a different DLL name in DEF file, /ignore:4070 suppresses
        # the warning message about DLL name doesn't match the default one.
        # See https://msdn.microsoft.com/en-us/library/sfkk2fz7.aspx
        flag: "/ignore:4070"
      }
    }
  }

  feature {
    name: 'windows_export_all_symbols'
  }

  feature {
    name: 'no_windows_export_all_symbols'
  }

  linking_mode_flags { mode: DYNAMIC }
}

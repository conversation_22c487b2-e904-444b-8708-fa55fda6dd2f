licenses(["restricted"])  # MPL2, portions GPL v3, LGPL v3, BSD-like

package(default_visibility = ["//visibility:public"])

config_setting(
    name = "using_nvcc",
    values = {
        "define": "using_cuda_nvcc=true",
    },
)

config_setting(
    name = "using_clang",
    values = {
        "define": "using_cuda_clang=true",
    },
)

# Equivalent to using_clang && -c opt.
config_setting(
    name = "using_clang_opt",
    values = {
        "define": "using_cuda_clang=true",
        "compilation_mode": "opt",
    },
)

config_setting(
    name = "darwin",
    values = {"cpu": "darwin"},
    visibility = ["//visibility:public"],
)

config_setting(
    name = "freebsd",
    values = {"cpu": "freebsd"},
    visibility = ["//visibility:public"],
)

cc_library(
    name = "cuda_headers",
    hdrs = [
        "cuda/cuda_config.h",
        ":cuda-include",
        ":cudnn-include",
    ],
    includes = [
        ".",
        "cuda/include",
        "cuda/include/crt",
    ],
    visibility = ["//visibility:public"],
)

cc_library(
    name = "cudart_static",
    srcs = ["cuda/lib/libcudart_static.a"],
    includes = [
        ".",
        "cuda/include",
    ],
    linkopts = select({
        ":freebsd": [],
        "//conditions:default": ["-ldl"],
    }) + [
        "-lpthread",
        "-lrt",
    ],
    visibility = ["//visibility:public"],
)

cc_library(
    name = "cuda_driver",
    srcs = ["cuda/lib/libcuda.so"],
    includes = [
        ".",
        "cuda/include",
    ],
    visibility = ["//visibility:public"],
)

cc_library(
    name = "cudart",
    srcs = ["cuda/lib/libcudart.so.10.0"],
    data = ["cuda/lib/libcudart.so.10.0"],
    includes = [
        ".",
        "cuda/include",
    ],
    linkstatic = 1,
    visibility = ["//visibility:public"],
)

cc_library(
    name = "cublas",
    srcs = ["cuda/lib/libcublas.so.10.0"],
    data = ["cuda/lib/libcublas.so.10.0"],
    includes = [
        ".",
        "cuda/include",
    ],
    linkstatic = 1,
    visibility = ["//visibility:public"],
)

cc_library(
    name = "cusolver",
    srcs = ["cuda/lib/libcusolver.so.10.0"],
    data = ["cuda/lib/libcusolver.so.10.0"],
    includes = [
        ".",
        "cuda/include",
    ],
    linkopts = ["-lgomp"],
    linkstatic = 1,
    visibility = ["//visibility:public"],
)

cc_library(
    name = "cudnn",
    srcs = ["cuda/lib/libcudnn.so.7"],
    data = ["cuda/lib/libcudnn.so.7"],
    includes = [
        ".",
        "cuda/include",
    ],
    linkstatic = 1,
    visibility = ["//visibility:public"],
)

cc_library(
    name = "cudnn_header",
    includes = [
        ".",
        "cuda/include",
    ],
    visibility = ["//visibility:public"],
)

cc_library(
    name = "cufft",
    srcs = ["cuda/lib/libcufft.so.10.0"],
    data = ["cuda/lib/libcufft.so.10.0"],
    includes = [
        ".",
        "cuda/include",
    ],
    linkstatic = 1,
    visibility = ["//visibility:public"],
)

cc_library(
    name = "curand",
    srcs = ["cuda/lib/libcurand.so.10.0"],
    data = ["cuda/lib/libcurand.so.10.0"],
    includes = [
        ".",
        "cuda/include",
    ],
    linkstatic = 1,
    visibility = ["//visibility:public"],
)

cc_library(
    name = "cuda",
    visibility = ["//visibility:public"],
    deps = [
        ":cublas",
        ":cuda_headers",
        ":cudart",
        ":cudnn",
        ":cufft",
        ":curand",
    ],
)

cc_library(
    name = "cupti_headers",
    hdrs = [
        "cuda/cuda_config.h",
        ":cuda-extras",
    ],
    includes = [
        ".",
        "cuda/extras/CUPTI/include/",
    ],
    visibility = ["//visibility:public"],
)

cc_library(
    name = "cupti_dsos",
    data = ["cuda/lib/libcupti.so.10.0"],
    includes = [
        ".",
        "cuda/include",
    ],
    visibility = ["//visibility:public"],
)

cc_library(
    name = "cusparse",
    srcs = ["cuda/lib/libcusparse.so.10.0"],
    data = ["cuda/lib/libcusparse.so.10.0"],
    includes = [
        ".",
        "cuda/include",
    ],
    linkopts = ["-lgomp"],
    linkstatic = 1,
    visibility = ["//visibility:public"],
)

cc_library(
    name = "libdevice_root",
    data = [":cuda-nvvm"],
    visibility = ["//visibility:public"],
)

genrule(
    name = "cuda-include",
    outs = [
        "cuda/include/CL/cl.h",
        "cuda/include/CL/cl.hpp",
        "cuda/include/CL/cl_egl.h",
        "cuda/include/CL/cl_ext.h",
        "cuda/include/CL/cl_gl.h",
        "cuda/include/CL/cl_gl_ext.h",
        "cuda/include/CL/cl_platform.h",
        "cuda/include/CL/opencl.h",
        "cuda/include/builtin_types.h",
        "cuda/include/channel_descriptor.h",
        "cuda/include/common_functions.h",
        "cuda/include/cooperative_groups.h",
        "cuda/include/cooperative_groups_helpers.h",
        "cuda/include/crt/common_functions.h",
        "cuda/include/crt/device_double_functions.h",
        "cuda/include/crt/device_double_functions.hpp",
        "cuda/include/crt/device_functions.h",
        "cuda/include/crt/device_functions.hpp",
        "cuda/include/crt/func_macro.h",
        "cuda/include/crt/host_config.h",
        "cuda/include/crt/host_defines.h",
        "cuda/include/crt/host_runtime.h",
        "cuda/include/crt/math_functions.h",
        "cuda/include/crt/math_functions.hpp",
        "cuda/include/crt/mma.h",
        "cuda/include/crt/mma.hpp",
        "cuda/include/crt/nvfunctional",
        "cuda/include/crt/sm_70_rt.h",
        "cuda/include/crt/sm_70_rt.hpp",
        "cuda/include/crt/storage_class.h",
        "cuda/include/cuComplex.h",
        "cuda/include/cublas.h",
        "cuda/include/cublasXt.h",
        "cuda/include/cublas_api.h",
        "cuda/include/cublas_v2.h",
        "cuda/include/cuda.h",
        "cuda/include/cudaEGL.h",
        "cuda/include/cudaGL.h",
        "cuda/include/cudaProfiler.h",
        "cuda/include/cudaVDPAU.h",
        "cuda/include/cuda_device_runtime_api.h",
        "cuda/include/cuda_egl_interop.h",
        "cuda/include/cuda_fp16.h",
        "cuda/include/cuda_fp16.hpp",
        "cuda/include/cuda_gl_interop.h",
        "cuda/include/cuda_occupancy.h",
        "cuda/include/cuda_profiler_api.h",
        "cuda/include/cuda_runtime.h",
        "cuda/include/cuda_runtime_api.h",
        "cuda/include/cuda_surface_types.h",
        "cuda/include/cuda_texture_types.h",
        "cuda/include/cuda_vdpau_interop.h",
        "cuda/include/cudalibxt.h",
        "cuda/include/cudart_platform.h",
        "cuda/include/cufft.h",
        "cuda/include/cufftXt.h",
        "cuda/include/cufftw.h",
        "cuda/include/curand.h",
        "cuda/include/curand_discrete.h",
        "cuda/include/curand_discrete2.h",
        "cuda/include/curand_globals.h",
        "cuda/include/curand_kernel.h",
        "cuda/include/curand_lognormal.h",
        "cuda/include/curand_mrg32k3a.h",
        "cuda/include/curand_mtgp32.h",
        "cuda/include/curand_mtgp32_host.h",
        "cuda/include/curand_mtgp32_kernel.h",
        "cuda/include/curand_mtgp32dc_p_11213.h",
        "cuda/include/curand_normal.h",
        "cuda/include/curand_normal_static.h",
        "cuda/include/curand_philox4x32_x.h",
        "cuda/include/curand_poisson.h",
        "cuda/include/curand_precalc.h",
        "cuda/include/curand_uniform.h",
        "cuda/include/cusolverDn.h",
        "cuda/include/cusolverRf.h",
        "cuda/include/cusolverSp.h",
        "cuda/include/cusolverSp_LOWLEVEL_PREVIEW.h",
        "cuda/include/cusolver_common.h",
        "cuda/include/cusparse.h",
        "cuda/include/cusparse_v2.h",
        "cuda/include/device_atomic_functions.h",
        "cuda/include/device_atomic_functions.hpp",
        "cuda/include/device_double_functions.h",
        "cuda/include/device_functions.h",
        "cuda/include/device_launch_parameters.h",
        "cuda/include/device_types.h",
        "cuda/include/driver_functions.h",
        "cuda/include/driver_types.h",
        "cuda/include/fatBinaryCtl.h",
        "cuda/include/fatbinary.h",
        "cuda/include/host_config.h",
        "cuda/include/host_defines.h",
        "cuda/include/library_types.h",
        "cuda/include/math_constants.h",
        "cuda/include/math_functions.h",
        "cuda/include/mma.h",
        "cuda/include/npp.h",
        "cuda/include/nppcore.h",
        "cuda/include/nppdefs.h",
        "cuda/include/nppi.h",
        "cuda/include/nppi_arithmetic_and_logical_operations.h",
        "cuda/include/nppi_color_conversion.h",
        "cuda/include/nppi_compression_functions.h",
        "cuda/include/nppi_computer_vision.h",
        "cuda/include/nppi_data_exchange_and_initialization.h",
        "cuda/include/nppi_filtering_functions.h",
        "cuda/include/nppi_geometry_transforms.h",
        "cuda/include/nppi_linear_transforms.h",
        "cuda/include/nppi_morphological_operations.h",
        "cuda/include/nppi_statistics_functions.h",
        "cuda/include/nppi_support_functions.h",
        "cuda/include/nppi_threshold_and_compare_operations.h",
        "cuda/include/npps.h",
        "cuda/include/npps_arithmetic_and_logical_operations.h",
        "cuda/include/npps_conversion_functions.h",
        "cuda/include/npps_filtering_functions.h",
        "cuda/include/npps_initialization.h",
        "cuda/include/npps_statistics_functions.h",
        "cuda/include/npps_support_functions.h",
        "cuda/include/nppversion.h",
        "cuda/include/nvToolsExt.h",
        "cuda/include/nvToolsExtCuda.h",
        "cuda/include/nvToolsExtCudaRt.h",
        "cuda/include/nvToolsExtMeta.h",
        "cuda/include/nvToolsExtSync.h",
        "cuda/include/nvblas.h",
        "cuda/include/nvfunctional",
        "cuda/include/nvgraph.h",
        "cuda/include/nvjpeg.h",
        "cuda/include/nvml.h",
        "cuda/include/nvrtc.h",
        "cuda/include/nvtx3/nvToolsExt.h",
        "cuda/include/nvtx3/nvToolsExtCuda.h",
        "cuda/include/nvtx3/nvToolsExtCudaRt.h",
        "cuda/include/nvtx3/nvToolsExtOpenCL.h",
        "cuda/include/nvtx3/nvToolsExtSync.h",
        "cuda/include/nvtx3/nvtxDetail/nvtxImpl.h",
        "cuda/include/nvtx3/nvtxDetail/nvtxImplCore.h",
        "cuda/include/nvtx3/nvtxDetail/nvtxImplCudaRt_v3.h",
        "cuda/include/nvtx3/nvtxDetail/nvtxImplCuda_v3.h",
        "cuda/include/nvtx3/nvtxDetail/nvtxImplOpenCL_v3.h",
        "cuda/include/nvtx3/nvtxDetail/nvtxImplSync_v3.h",
        "cuda/include/nvtx3/nvtxDetail/nvtxInit.h",
        "cuda/include/nvtx3/nvtxDetail/nvtxInitDecls.h",
        "cuda/include/nvtx3/nvtxDetail/nvtxInitDefs.h",
        "cuda/include/nvtx3/nvtxDetail/nvtxLinkOnce.h",
        "cuda/include/nvtx3/nvtxDetail/nvtxTypes.h",
        "cuda/include/sm_20_atomic_functions.h",
        "cuda/include/sm_20_atomic_functions.hpp",
        "cuda/include/sm_20_intrinsics.h",
        "cuda/include/sm_20_intrinsics.hpp",
        "cuda/include/sm_30_intrinsics.h",
        "cuda/include/sm_30_intrinsics.hpp",
        "cuda/include/sm_32_atomic_functions.h",
        "cuda/include/sm_32_atomic_functions.hpp",
        "cuda/include/sm_32_intrinsics.h",
        "cuda/include/sm_32_intrinsics.hpp",
        "cuda/include/sm_35_atomic_functions.h",
        "cuda/include/sm_35_intrinsics.h",
        "cuda/include/sm_60_atomic_functions.h",
        "cuda/include/sm_60_atomic_functions.hpp",
        "cuda/include/sm_61_intrinsics.h",
        "cuda/include/sm_61_intrinsics.hpp",
        "cuda/include/sobol_direction_vectors.h",
        "cuda/include/surface_functions.h",
        "cuda/include/surface_functions.hpp",
        "cuda/include/surface_indirect_functions.h",
        "cuda/include/surface_indirect_functions.hpp",
        "cuda/include/surface_types.h",
        "cuda/include/texture_fetch_functions.h",
        "cuda/include/texture_fetch_functions.hpp",
        "cuda/include/texture_indirect_functions.h",
        "cuda/include/texture_indirect_functions.hpp",
        "cuda/include/texture_types.h",
        "cuda/include/thrust/adjacent_difference.h",
        "cuda/include/thrust/advance.h",
        "cuda/include/thrust/binary_search.h",
        "cuda/include/thrust/complex.h",
        "cuda/include/thrust/copy.h",
        "cuda/include/thrust/count.h",
        "cuda/include/thrust/detail/adjacent_difference.inl",
        "cuda/include/thrust/detail/advance.inl",
        "cuda/include/thrust/detail/alignment.h",
        "cuda/include/thrust/detail/allocator/allocator_traits.h",
        "cuda/include/thrust/detail/allocator/allocator_traits.inl",
        "cuda/include/thrust/detail/allocator/copy_construct_range.h",
        "cuda/include/thrust/detail/allocator/copy_construct_range.inl",
        "cuda/include/thrust/detail/allocator/default_construct_range.h",
        "cuda/include/thrust/detail/allocator/default_construct_range.inl",
        "cuda/include/thrust/detail/allocator/destroy_range.h",
        "cuda/include/thrust/detail/allocator/destroy_range.inl",
        "cuda/include/thrust/detail/allocator/fill_construct_range.h",
        "cuda/include/thrust/detail/allocator/fill_construct_range.inl",
        "cuda/include/thrust/detail/allocator/malloc_allocator.h",
        "cuda/include/thrust/detail/allocator/malloc_allocator.inl",
        "cuda/include/thrust/detail/allocator/no_throw_allocator.h",
        "cuda/include/thrust/detail/allocator/tagged_allocator.h",
        "cuda/include/thrust/detail/allocator/tagged_allocator.inl",
        "cuda/include/thrust/detail/allocator/temporary_allocator.h",
        "cuda/include/thrust/detail/allocator/temporary_allocator.inl",
        "cuda/include/thrust/detail/binary_search.inl",
        "cuda/include/thrust/detail/complex/arithmetic.h",
        "cuda/include/thrust/detail/complex/c99math.h",
        "cuda/include/thrust/detail/complex/catrig.h",
        "cuda/include/thrust/detail/complex/catrigf.h",
        "cuda/include/thrust/detail/complex/ccosh.h",
        "cuda/include/thrust/detail/complex/ccoshf.h",
        "cuda/include/thrust/detail/complex/cexp.h",
        "cuda/include/thrust/detail/complex/cexpf.h",
        "cuda/include/thrust/detail/complex/clog.h",
        "cuda/include/thrust/detail/complex/clogf.h",
        "cuda/include/thrust/detail/complex/complex.inl",
        "cuda/include/thrust/detail/complex/cpow.h",
        "cuda/include/thrust/detail/complex/cproj.h",
        "cuda/include/thrust/detail/complex/csinh.h",
        "cuda/include/thrust/detail/complex/csinhf.h",
        "cuda/include/thrust/detail/complex/csqrt.h",
        "cuda/include/thrust/detail/complex/csqrtf.h",
        "cuda/include/thrust/detail/complex/ctanh.h",
        "cuda/include/thrust/detail/complex/ctanhf.h",
        "cuda/include/thrust/detail/complex/math_private.h",
        "cuda/include/thrust/detail/complex/stream.h",
        "cuda/include/thrust/detail/config.h",
        "cuda/include/thrust/detail/config/compiler.h",
        "cuda/include/thrust/detail/config/compiler_fence.h",
        "cuda/include/thrust/detail/config/config.h",
        "cuda/include/thrust/detail/config/debug.h",
        "cuda/include/thrust/detail/config/device_system.h",
        "cuda/include/thrust/detail/config/exec_check_disable.h",
        "cuda/include/thrust/detail/config/forceinline.h",
        "cuda/include/thrust/detail/config/global_workarounds.h",
        "cuda/include/thrust/detail/config/host_device.h",
        "cuda/include/thrust/detail/config/host_system.h",
        "cuda/include/thrust/detail/config/simple_defines.h",
        "cuda/include/thrust/detail/contiguous_storage.h",
        "cuda/include/thrust/detail/contiguous_storage.inl",
        "cuda/include/thrust/detail/copy.h",
        "cuda/include/thrust/detail/copy.inl",
        "cuda/include/thrust/detail/copy_if.h",
        "cuda/include/thrust/detail/copy_if.inl",
        "cuda/include/thrust/detail/count.inl",
        "cuda/include/thrust/detail/cstdint.h",
        "cuda/include/thrust/detail/device_delete.inl",
        "cuda/include/thrust/detail/device_free.inl",
        "cuda/include/thrust/detail/device_malloc.inl",
        "cuda/include/thrust/detail/device_new.inl",
        "cuda/include/thrust/detail/device_ptr.inl",
        "cuda/include/thrust/detail/device_reference.inl",
        "cuda/include/thrust/detail/device_vector.inl",
        "cuda/include/thrust/detail/dispatch/is_trivial_copy.h",
        "cuda/include/thrust/detail/distance.inl",
        "cuda/include/thrust/detail/equal.inl",
        "cuda/include/thrust/detail/execute_with_allocator.h",
        "cuda/include/thrust/detail/execution_policy.h",
        "cuda/include/thrust/detail/extrema.inl",
        "cuda/include/thrust/detail/fill.inl",
        "cuda/include/thrust/detail/find.inl",
        "cuda/include/thrust/detail/for_each.inl",
        "cuda/include/thrust/detail/function.h",
        "cuda/include/thrust/detail/functional.inl",
        "cuda/include/thrust/detail/functional/actor.h",
        "cuda/include/thrust/detail/functional/actor.inl",
        "cuda/include/thrust/detail/functional/argument.h",
        "cuda/include/thrust/detail/functional/composite.h",
        "cuda/include/thrust/detail/functional/operators.h",
        "cuda/include/thrust/detail/functional/operators/arithmetic_operators.h",
        "cuda/include/thrust/detail/functional/operators/assignment_operator.h",
        "cuda/include/thrust/detail/functional/operators/bitwise_operators.h",
        "cuda/include/thrust/detail/functional/operators/compound_assignment_operators.h",
        "cuda/include/thrust/detail/functional/operators/logical_operators.h",
        "cuda/include/thrust/detail/functional/operators/operator_adaptors.h",
        "cuda/include/thrust/detail/functional/operators/relational_operators.h",
        "cuda/include/thrust/detail/functional/placeholder.h",
        "cuda/include/thrust/detail/functional/value.h",
        "cuda/include/thrust/detail/gather.inl",
        "cuda/include/thrust/detail/generate.inl",
        "cuda/include/thrust/detail/get_iterator_value.h",
        "cuda/include/thrust/detail/host_vector.inl",
        "cuda/include/thrust/detail/inner_product.inl",
        "cuda/include/thrust/detail/integer_math.h",
        "cuda/include/thrust/detail/integer_traits.h",
        "cuda/include/thrust/detail/internal_functional.h",
        "cuda/include/thrust/detail/logical.inl",
        "cuda/include/thrust/detail/malloc_and_free.h",
        "cuda/include/thrust/detail/merge.inl",
        "cuda/include/thrust/detail/minmax.h",
        "cuda/include/thrust/detail/mismatch.inl",
        "cuda/include/thrust/detail/mpl/math.h",
        "cuda/include/thrust/detail/numeric_traits.h",
        "cuda/include/thrust/detail/overlapped_copy.h",
        "cuda/include/thrust/detail/pair.inl",
        "cuda/include/thrust/detail/partition.inl",
        "cuda/include/thrust/detail/pointer.h",
        "cuda/include/thrust/detail/pointer.inl",
        "cuda/include/thrust/detail/preprocessor.h",
        "cuda/include/thrust/detail/range/head_flags.h",
        "cuda/include/thrust/detail/range/tail_flags.h",
        "cuda/include/thrust/detail/raw_pointer_cast.h",
        "cuda/include/thrust/detail/raw_reference_cast.h",
        "cuda/include/thrust/detail/reduce.inl",
        "cuda/include/thrust/detail/reference.h",
        "cuda/include/thrust/detail/reference.inl",
        "cuda/include/thrust/detail/reference_forward_declaration.h",
        "cuda/include/thrust/detail/remove.inl",
        "cuda/include/thrust/detail/replace.inl",
        "cuda/include/thrust/detail/reverse.inl",
        "cuda/include/thrust/detail/scan.inl",
        "cuda/include/thrust/detail/scatter.inl",
        "cuda/include/thrust/detail/seq.h",
        "cuda/include/thrust/detail/sequence.inl",
        "cuda/include/thrust/detail/set_operations.inl",
        "cuda/include/thrust/detail/sort.inl",
        "cuda/include/thrust/detail/static_assert.h",
        "cuda/include/thrust/detail/static_map.h",
        "cuda/include/thrust/detail/swap.h",
        "cuda/include/thrust/detail/swap.inl",
        "cuda/include/thrust/detail/swap_ranges.inl",
        "cuda/include/thrust/detail/tabulate.inl",
        "cuda/include/thrust/detail/temporary_array.h",
        "cuda/include/thrust/detail/temporary_array.inl",
        "cuda/include/thrust/detail/temporary_buffer.h",
        "cuda/include/thrust/detail/transform.inl",
        "cuda/include/thrust/detail/transform_reduce.inl",
        "cuda/include/thrust/detail/transform_scan.inl",
        "cuda/include/thrust/detail/trivial_sequence.h",
        "cuda/include/thrust/detail/tuple.inl",
        "cuda/include/thrust/detail/tuple_meta_transform.h",
        "cuda/include/thrust/detail/tuple_transform.h",
        "cuda/include/thrust/detail/type_traits.h",
        "cuda/include/thrust/detail/type_traits/algorithm/intermediate_type_from_function_and_iterators.h",
        "cuda/include/thrust/detail/type_traits/function_traits.h",
        "cuda/include/thrust/detail/type_traits/has_member_function.h",
        "cuda/include/thrust/detail/type_traits/has_nested_type.h",
        "cuda/include/thrust/detail/type_traits/has_trivial_assign.h",
        "cuda/include/thrust/detail/type_traits/is_call_possible.h",
        "cuda/include/thrust/detail/type_traits/is_metafunction_defined.h",
        "cuda/include/thrust/detail/type_traits/iterator/is_discard_iterator.h",
        "cuda/include/thrust/detail/type_traits/iterator/is_output_iterator.h",
        "cuda/include/thrust/detail/type_traits/minimum_type.h",
        "cuda/include/thrust/detail/type_traits/pointer_traits.h",
        "cuda/include/thrust/detail/type_traits/result_of_adaptable_function.h",
        "cuda/include/thrust/detail/uninitialized_copy.inl",
        "cuda/include/thrust/detail/uninitialized_fill.inl",
        "cuda/include/thrust/detail/unique.inl",
        "cuda/include/thrust/detail/use_default.h",
        "cuda/include/thrust/detail/util/align.h",
        "cuda/include/thrust/detail/util/blocking.h",
        "cuda/include/thrust/detail/vector_base.h",
        "cuda/include/thrust/detail/vector_base.inl",
        "cuda/include/thrust/device_allocator.h",
        "cuda/include/thrust/device_delete.h",
        "cuda/include/thrust/device_free.h",
        "cuda/include/thrust/device_malloc.h",
        "cuda/include/thrust/device_malloc_allocator.h",
        "cuda/include/thrust/device_new.h",
        "cuda/include/thrust/device_new_allocator.h",
        "cuda/include/thrust/device_ptr.h",
        "cuda/include/thrust/device_reference.h",
        "cuda/include/thrust/device_vector.h",
        "cuda/include/thrust/distance.h",
        "cuda/include/thrust/equal.h",
        "cuda/include/thrust/execution_policy.h",
        "cuda/include/thrust/extrema.h",
        "cuda/include/thrust/fill.h",
        "cuda/include/thrust/find.h",
        "cuda/include/thrust/for_each.h",
        "cuda/include/thrust/functional.h",
        "cuda/include/thrust/gather.h",
        "cuda/include/thrust/generate.h",
        "cuda/include/thrust/host_vector.h",
        "cuda/include/thrust/inner_product.h",
        "cuda/include/thrust/iterator/constant_iterator.h",
        "cuda/include/thrust/iterator/counting_iterator.h",
        "cuda/include/thrust/iterator/detail/any_assign.h",
        "cuda/include/thrust/iterator/detail/any_system_tag.h",
        "cuda/include/thrust/iterator/detail/constant_iterator_base.h",
        "cuda/include/thrust/iterator/detail/counting_iterator.inl",
        "cuda/include/thrust/iterator/detail/device_system_tag.h",
        "cuda/include/thrust/iterator/detail/discard_iterator_base.h",
        "cuda/include/thrust/iterator/detail/distance_from_result.h",
        "cuda/include/thrust/iterator/detail/host_system_tag.h",
        "cuda/include/thrust/iterator/detail/is_iterator_category.h",
        "cuda/include/thrust/iterator/detail/is_trivial_iterator.h",
        "cuda/include/thrust/iterator/detail/iterator_adaptor_base.h",
        "cuda/include/thrust/iterator/detail/iterator_category_to_system.h",
        "cuda/include/thrust/iterator/detail/iterator_category_to_traversal.h",
        "cuda/include/thrust/iterator/detail/iterator_category_with_system_and_traversal.h",
        "cuda/include/thrust/iterator/detail/iterator_facade_category.h",
        "cuda/include/thrust/iterator/detail/iterator_traits.inl",
        "cuda/include/thrust/iterator/detail/iterator_traversal_tags.h",
        "cuda/include/thrust/iterator/detail/join_iterator.h",
        "cuda/include/thrust/iterator/detail/minimum_category.h",
        "cuda/include/thrust/iterator/detail/minimum_system.h",
        "cuda/include/thrust/iterator/detail/normal_iterator.h",
        "cuda/include/thrust/iterator/detail/permutation_iterator_base.h",
        "cuda/include/thrust/iterator/detail/retag.h",
        "cuda/include/thrust/iterator/detail/reverse_iterator.inl",
        "cuda/include/thrust/iterator/detail/reverse_iterator_base.h",
        "cuda/include/thrust/iterator/detail/tagged_iterator.h",
        "cuda/include/thrust/iterator/detail/transform_iterator.inl",
        "cuda/include/thrust/iterator/detail/transform_output_iterator.inl",
        "cuda/include/thrust/iterator/detail/tuple_of_iterator_references.h",
        "cuda/include/thrust/iterator/detail/universal_categories.h",
        "cuda/include/thrust/iterator/detail/zip_iterator.inl",
        "cuda/include/thrust/iterator/detail/zip_iterator_base.h",
        "cuda/include/thrust/iterator/discard_iterator.h",
        "cuda/include/thrust/iterator/iterator_adaptor.h",
        "cuda/include/thrust/iterator/iterator_categories.h",
        "cuda/include/thrust/iterator/iterator_facade.h",
        "cuda/include/thrust/iterator/iterator_traits.h",
        "cuda/include/thrust/iterator/permutation_iterator.h",
        "cuda/include/thrust/iterator/retag.h",
        "cuda/include/thrust/iterator/reverse_iterator.h",
        "cuda/include/thrust/iterator/transform_iterator.h",
        "cuda/include/thrust/iterator/transform_output_iterator.h",
        "cuda/include/thrust/iterator/zip_iterator.h",
        "cuda/include/thrust/logical.h",
        "cuda/include/thrust/memory.h",
        "cuda/include/thrust/merge.h",
        "cuda/include/thrust/mismatch.h",
        "cuda/include/thrust/pair.h",
        "cuda/include/thrust/partition.h",
        "cuda/include/thrust/random.h",
        "cuda/include/thrust/random/detail/discard_block_engine.inl",
        "cuda/include/thrust/random/detail/linear_congruential_engine.inl",
        "cuda/include/thrust/random/detail/linear_congruential_engine_discard.h",
        "cuda/include/thrust/random/detail/linear_feedback_shift_engine.inl",
        "cuda/include/thrust/random/detail/linear_feedback_shift_engine_wordmask.h",
        "cuda/include/thrust/random/detail/mod.h",
        "cuda/include/thrust/random/detail/normal_distribution.inl",
        "cuda/include/thrust/random/detail/normal_distribution_base.h",
        "cuda/include/thrust/random/detail/random_core_access.h",
        "cuda/include/thrust/random/detail/subtract_with_carry_engine.inl",
        "cuda/include/thrust/random/detail/uniform_int_distribution.inl",
        "cuda/include/thrust/random/detail/uniform_real_distribution.inl",
        "cuda/include/thrust/random/detail/xor_combine_engine.inl",
        "cuda/include/thrust/random/detail/xor_combine_engine_max.h",
        "cuda/include/thrust/random/discard_block_engine.h",
        "cuda/include/thrust/random/linear_congruential_engine.h",
        "cuda/include/thrust/random/linear_feedback_shift_engine.h",
        "cuda/include/thrust/random/normal_distribution.h",
        "cuda/include/thrust/random/subtract_with_carry_engine.h",
        "cuda/include/thrust/random/uniform_int_distribution.h",
        "cuda/include/thrust/random/uniform_real_distribution.h",
        "cuda/include/thrust/random/xor_combine_engine.h",
        "cuda/include/thrust/reduce.h",
        "cuda/include/thrust/remove.h",
        "cuda/include/thrust/replace.h",
        "cuda/include/thrust/reverse.h",
        "cuda/include/thrust/scan.h",
        "cuda/include/thrust/scatter.h",
        "cuda/include/thrust/sequence.h",
        "cuda/include/thrust/set_operations.h",
        "cuda/include/thrust/sort.h",
        "cuda/include/thrust/swap.h",
        "cuda/include/thrust/system/cpp/detail/adjacent_difference.h",
        "cuda/include/thrust/system/cpp/detail/assign_value.h",
        "cuda/include/thrust/system/cpp/detail/binary_search.h",
        "cuda/include/thrust/system/cpp/detail/copy.h",
        "cuda/include/thrust/system/cpp/detail/copy_if.h",
        "cuda/include/thrust/system/cpp/detail/count.h",
        "cuda/include/thrust/system/cpp/detail/equal.h",
        "cuda/include/thrust/system/cpp/detail/execution_policy.h",
        "cuda/include/thrust/system/cpp/detail/extrema.h",
        "cuda/include/thrust/system/cpp/detail/fill.h",
        "cuda/include/thrust/system/cpp/detail/find.h",
        "cuda/include/thrust/system/cpp/detail/for_each.h",
        "cuda/include/thrust/system/cpp/detail/gather.h",
        "cuda/include/thrust/system/cpp/detail/generate.h",
        "cuda/include/thrust/system/cpp/detail/get_value.h",
        "cuda/include/thrust/system/cpp/detail/inner_product.h",
        "cuda/include/thrust/system/cpp/detail/iter_swap.h",
        "cuda/include/thrust/system/cpp/detail/logical.h",
        "cuda/include/thrust/system/cpp/detail/malloc_and_free.h",
        "cuda/include/thrust/system/cpp/detail/memory.inl",
        "cuda/include/thrust/system/cpp/detail/merge.h",
        "cuda/include/thrust/system/cpp/detail/mismatch.h",
        "cuda/include/thrust/system/cpp/detail/par.h",
        "cuda/include/thrust/system/cpp/detail/partition.h",
        "cuda/include/thrust/system/cpp/detail/reduce.h",
        "cuda/include/thrust/system/cpp/detail/reduce_by_key.h",
        "cuda/include/thrust/system/cpp/detail/remove.h",
        "cuda/include/thrust/system/cpp/detail/replace.h",
        "cuda/include/thrust/system/cpp/detail/reverse.h",
        "cuda/include/thrust/system/cpp/detail/scan.h",
        "cuda/include/thrust/system/cpp/detail/scan_by_key.h",
        "cuda/include/thrust/system/cpp/detail/scatter.h",
        "cuda/include/thrust/system/cpp/detail/sequence.h",
        "cuda/include/thrust/system/cpp/detail/set_operations.h",
        "cuda/include/thrust/system/cpp/detail/sort.h",
        "cuda/include/thrust/system/cpp/detail/swap_ranges.h",
        "cuda/include/thrust/system/cpp/detail/tabulate.h",
        "cuda/include/thrust/system/cpp/detail/temporary_buffer.h",
        "cuda/include/thrust/system/cpp/detail/transform.h",
        "cuda/include/thrust/system/cpp/detail/transform_reduce.h",
        "cuda/include/thrust/system/cpp/detail/transform_scan.h",
        "cuda/include/thrust/system/cpp/detail/uninitialized_copy.h",
        "cuda/include/thrust/system/cpp/detail/uninitialized_fill.h",
        "cuda/include/thrust/system/cpp/detail/unique.h",
        "cuda/include/thrust/system/cpp/detail/unique_by_key.h",
        "cuda/include/thrust/system/cpp/detail/vector.inl",
        "cuda/include/thrust/system/cpp/execution_policy.h",
        "cuda/include/thrust/system/cpp/memory.h",
        "cuda/include/thrust/system/cpp/vector.h",
        "cuda/include/thrust/system/cuda/config.h",
        "cuda/include/thrust/system/cuda/detail/adjacent_difference.h",
        "cuda/include/thrust/system/cuda/detail/assign_value.h",
        "cuda/include/thrust/system/cuda/detail/binary_search.h",
        "cuda/include/thrust/system/cuda/detail/copy.h",
        "cuda/include/thrust/system/cuda/detail/copy_if.h",
        "cuda/include/thrust/system/cuda/detail/core/agent_launcher.h",
        "cuda/include/thrust/system/cuda/detail/core/alignment.h",
        "cuda/include/thrust/system/cuda/detail/core/triple_chevron_launch.h",
        "cuda/include/thrust/system/cuda/detail/core/util.h",
        "cuda/include/thrust/system/cuda/detail/count.h",
        "cuda/include/thrust/system/cuda/detail/cross_system.h",
        "cuda/include/thrust/system/cuda/detail/cub/agent/agent_histogram.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/agent/agent_radix_sort_downsweep.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/agent/agent_radix_sort_upsweep.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/agent/agent_reduce.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/agent/agent_reduce_by_key.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/agent/agent_rle.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/agent/agent_scan.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/agent/agent_segment_fixup.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/agent/agent_select_if.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/agent/agent_spmv_orig.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/agent/single_pass_scan_operators.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/block/block_adjacent_difference.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/block/block_discontinuity.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/block/block_exchange.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/block/block_histogram.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/block/block_load.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/block/block_radix_rank.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/block/block_radix_sort.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/block/block_raking_layout.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/block/block_reduce.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/block/block_scan.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/block/block_shuffle.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/block/block_store.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/block/specializations/block_histogram_atomic.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/block/specializations/block_histogram_sort.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/block/specializations/block_reduce_raking.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/block/specializations/block_reduce_raking_commutative_only.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/block/specializations/block_reduce_warp_reductions.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/block/specializations/block_scan_raking.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/block/specializations/block_scan_warp_scans.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/block/specializations/block_scan_warp_scans2.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/block/specializations/block_scan_warp_scans3.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/cub.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/device/device_histogram.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/device/device_partition.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/device/device_radix_sort.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/device/device_reduce.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/device/device_run_length_encode.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/device/device_scan.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/device/device_segmented_radix_sort.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/device/device_segmented_reduce.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/device/device_select.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/device/device_spmv.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/device/dispatch/dispatch_histogram.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/device/dispatch/dispatch_radix_sort.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/device/dispatch/dispatch_reduce.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/device/dispatch/dispatch_reduce_by_key.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/device/dispatch/dispatch_rle.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/device/dispatch/dispatch_scan.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/device/dispatch/dispatch_select_if.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/device/dispatch/dispatch_spmv_orig.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/grid/grid_barrier.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/grid/grid_even_share.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/grid/grid_mapping.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/grid/grid_queue.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/host/mutex.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/iterator/arg_index_input_iterator.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/iterator/cache_modified_input_iterator.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/iterator/cache_modified_output_iterator.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/iterator/constant_input_iterator.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/iterator/counting_input_iterator.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/iterator/discard_output_iterator.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/iterator/tex_obj_input_iterator.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/iterator/tex_ref_input_iterator.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/iterator/transform_input_iterator.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/thread/thread_load.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/thread/thread_operators.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/thread/thread_reduce.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/thread/thread_scan.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/thread/thread_search.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/thread/thread_store.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/util_allocator.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/util_arch.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/util_debug.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/util_device.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/util_macro.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/util_namespace.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/util_ptx.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/util_type.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/warp/specializations/warp_reduce_shfl.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/warp/specializations/warp_reduce_smem.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/warp/specializations/warp_scan_shfl.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/warp/specializations/warp_scan_smem.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/warp/warp_reduce.cuh",
        "cuda/include/thrust/system/cuda/detail/cub/warp/warp_scan.cuh",
        "cuda/include/thrust/system/cuda/detail/equal.h",
        "cuda/include/thrust/system/cuda/detail/error.inl",
        "cuda/include/thrust/system/cuda/detail/execution_policy.h",
        "cuda/include/thrust/system/cuda/detail/extrema.h",
        "cuda/include/thrust/system/cuda/detail/fill.h",
        "cuda/include/thrust/system/cuda/detail/find.h",
        "cuda/include/thrust/system/cuda/detail/for_each.h",
        "cuda/include/thrust/system/cuda/detail/gather.h",
        "cuda/include/thrust/system/cuda/detail/generate.h",
        "cuda/include/thrust/system/cuda/detail/get_value.h",
        "cuda/include/thrust/system/cuda/detail/guarded_cuda_runtime_api.h",
        "cuda/include/thrust/system/cuda/detail/guarded_driver_types.h",
        "cuda/include/thrust/system/cuda/detail/inner_product.h",
        "cuda/include/thrust/system/cuda/detail/internal/copy_cross_system.h",
        "cuda/include/thrust/system/cuda/detail/internal/copy_device_to_device.h",
        "cuda/include/thrust/system/cuda/detail/iter_swap.h",
        "cuda/include/thrust/system/cuda/detail/logical.h",
        "cuda/include/thrust/system/cuda/detail/malloc_and_free.h",
        "cuda/include/thrust/system/cuda/detail/memory.inl",
        "cuda/include/thrust/system/cuda/detail/merge.h",
        "cuda/include/thrust/system/cuda/detail/mismatch.h",
        "cuda/include/thrust/system/cuda/detail/par.h",
        "cuda/include/thrust/system/cuda/detail/par_to_seq.h",
        "cuda/include/thrust/system/cuda/detail/parallel_for.h",
        "cuda/include/thrust/system/cuda/detail/partition.h",
        "cuda/include/thrust/system/cuda/detail/reduce.h",
        "cuda/include/thrust/system/cuda/detail/reduce_by_key.h",
        "cuda/include/thrust/system/cuda/detail/remove.h",
        "cuda/include/thrust/system/cuda/detail/replace.h",
        "cuda/include/thrust/system/cuda/detail/reverse.h",
        "cuda/include/thrust/system/cuda/detail/scan.h",
        "cuda/include/thrust/system/cuda/detail/scan_by_key.h",
        "cuda/include/thrust/system/cuda/detail/scatter.h",
        "cuda/include/thrust/system/cuda/detail/sequence.h",
        "cuda/include/thrust/system/cuda/detail/set_operations.h",
        "cuda/include/thrust/system/cuda/detail/sort.h",
        "cuda/include/thrust/system/cuda/detail/swap_ranges.h",
        "cuda/include/thrust/system/cuda/detail/tabulate.h",
        "cuda/include/thrust/system/cuda/detail/temporary_buffer.h",
        "cuda/include/thrust/system/cuda/detail/terminate.h",
        "cuda/include/thrust/system/cuda/detail/transform.h",
        "cuda/include/thrust/system/cuda/detail/transform_reduce.h",
        "cuda/include/thrust/system/cuda/detail/transform_scan.h",
        "cuda/include/thrust/system/cuda/detail/uninitialized_copy.h",
        "cuda/include/thrust/system/cuda/detail/uninitialized_fill.h",
        "cuda/include/thrust/system/cuda/detail/unique.h",
        "cuda/include/thrust/system/cuda/detail/unique_by_key.h",
        "cuda/include/thrust/system/cuda/detail/util.h",
        "cuda/include/thrust/system/cuda/detail/vector.inl",
        "cuda/include/thrust/system/cuda/error.h",
        "cuda/include/thrust/system/cuda/execution_policy.h",
        "cuda/include/thrust/system/cuda/experimental/pinned_allocator.h",
        "cuda/include/thrust/system/cuda/memory.h",
        "cuda/include/thrust/system/cuda/vector.h",
        "cuda/include/thrust/system/detail/adl/adjacent_difference.h",
        "cuda/include/thrust/system/detail/adl/assign_value.h",
        "cuda/include/thrust/system/detail/adl/binary_search.h",
        "cuda/include/thrust/system/detail/adl/copy.h",
        "cuda/include/thrust/system/detail/adl/copy_if.h",
        "cuda/include/thrust/system/detail/adl/count.h",
        "cuda/include/thrust/system/detail/adl/equal.h",
        "cuda/include/thrust/system/detail/adl/extrema.h",
        "cuda/include/thrust/system/detail/adl/fill.h",
        "cuda/include/thrust/system/detail/adl/find.h",
        "cuda/include/thrust/system/detail/adl/for_each.h",
        "cuda/include/thrust/system/detail/adl/gather.h",
        "cuda/include/thrust/system/detail/adl/generate.h",
        "cuda/include/thrust/system/detail/adl/get_value.h",
        "cuda/include/thrust/system/detail/adl/inner_product.h",
        "cuda/include/thrust/system/detail/adl/iter_swap.h",
        "cuda/include/thrust/system/detail/adl/logical.h",
        "cuda/include/thrust/system/detail/adl/malloc_and_free.h",
        "cuda/include/thrust/system/detail/adl/merge.h",
        "cuda/include/thrust/system/detail/adl/mismatch.h",
        "cuda/include/thrust/system/detail/adl/partition.h",
        "cuda/include/thrust/system/detail/adl/reduce.h",
        "cuda/include/thrust/system/detail/adl/reduce_by_key.h",
        "cuda/include/thrust/system/detail/adl/remove.h",
        "cuda/include/thrust/system/detail/adl/replace.h",
        "cuda/include/thrust/system/detail/adl/reverse.h",
        "cuda/include/thrust/system/detail/adl/scan.h",
        "cuda/include/thrust/system/detail/adl/scan_by_key.h",
        "cuda/include/thrust/system/detail/adl/scatter.h",
        "cuda/include/thrust/system/detail/adl/sequence.h",
        "cuda/include/thrust/system/detail/adl/set_operations.h",
        "cuda/include/thrust/system/detail/adl/sort.h",
        "cuda/include/thrust/system/detail/adl/swap_ranges.h",
        "cuda/include/thrust/system/detail/adl/tabulate.h",
        "cuda/include/thrust/system/detail/adl/temporary_buffer.h",
        "cuda/include/thrust/system/detail/adl/transform.h",
        "cuda/include/thrust/system/detail/adl/transform_reduce.h",
        "cuda/include/thrust/system/detail/adl/transform_scan.h",
        "cuda/include/thrust/system/detail/adl/uninitialized_copy.h",
        "cuda/include/thrust/system/detail/adl/uninitialized_fill.h",
        "cuda/include/thrust/system/detail/adl/unique.h",
        "cuda/include/thrust/system/detail/adl/unique_by_key.h",
        "cuda/include/thrust/system/detail/bad_alloc.h",
        "cuda/include/thrust/system/detail/errno.h",
        "cuda/include/thrust/system/detail/error_category.inl",
        "cuda/include/thrust/system/detail/error_code.inl",
        "cuda/include/thrust/system/detail/error_condition.inl",
        "cuda/include/thrust/system/detail/generic/adjacent_difference.h",
        "cuda/include/thrust/system/detail/generic/adjacent_difference.inl",
        "cuda/include/thrust/system/detail/generic/advance.h",
        "cuda/include/thrust/system/detail/generic/advance.inl",
        "cuda/include/thrust/system/detail/generic/binary_search.h",
        "cuda/include/thrust/system/detail/generic/binary_search.inl",
        "cuda/include/thrust/system/detail/generic/copy.h",
        "cuda/include/thrust/system/detail/generic/copy.inl",
        "cuda/include/thrust/system/detail/generic/copy_if.h",
        "cuda/include/thrust/system/detail/generic/copy_if.inl",
        "cuda/include/thrust/system/detail/generic/count.h",
        "cuda/include/thrust/system/detail/generic/count.inl",
        "cuda/include/thrust/system/detail/generic/distance.h",
        "cuda/include/thrust/system/detail/generic/distance.inl",
        "cuda/include/thrust/system/detail/generic/equal.h",
        "cuda/include/thrust/system/detail/generic/equal.inl",
        "cuda/include/thrust/system/detail/generic/extrema.h",
        "cuda/include/thrust/system/detail/generic/extrema.inl",
        "cuda/include/thrust/system/detail/generic/fill.h",
        "cuda/include/thrust/system/detail/generic/find.h",
        "cuda/include/thrust/system/detail/generic/find.inl",
        "cuda/include/thrust/system/detail/generic/for_each.h",
        "cuda/include/thrust/system/detail/generic/gather.h",
        "cuda/include/thrust/system/detail/generic/gather.inl",
        "cuda/include/thrust/system/detail/generic/generate.h",
        "cuda/include/thrust/system/detail/generic/generate.inl",
        "cuda/include/thrust/system/detail/generic/inner_product.h",
        "cuda/include/thrust/system/detail/generic/inner_product.inl",
        "cuda/include/thrust/system/detail/generic/logical.h",
        "cuda/include/thrust/system/detail/generic/memory.h",
        "cuda/include/thrust/system/detail/generic/memory.inl",
        "cuda/include/thrust/system/detail/generic/merge.h",
        "cuda/include/thrust/system/detail/generic/merge.inl",
        "cuda/include/thrust/system/detail/generic/mismatch.h",
        "cuda/include/thrust/system/detail/generic/mismatch.inl",
        "cuda/include/thrust/system/detail/generic/partition.h",
        "cuda/include/thrust/system/detail/generic/partition.inl",
        "cuda/include/thrust/system/detail/generic/reduce.h",
        "cuda/include/thrust/system/detail/generic/reduce.inl",
        "cuda/include/thrust/system/detail/generic/reduce_by_key.h",
        "cuda/include/thrust/system/detail/generic/reduce_by_key.inl",
        "cuda/include/thrust/system/detail/generic/remove.h",
        "cuda/include/thrust/system/detail/generic/remove.inl",
        "cuda/include/thrust/system/detail/generic/replace.h",
        "cuda/include/thrust/system/detail/generic/replace.inl",
        "cuda/include/thrust/system/detail/generic/reverse.h",
        "cuda/include/thrust/system/detail/generic/reverse.inl",
        "cuda/include/thrust/system/detail/generic/scalar/binary_search.h",
        "cuda/include/thrust/system/detail/generic/scalar/binary_search.inl",
        "cuda/include/thrust/system/detail/generic/scan.h",
        "cuda/include/thrust/system/detail/generic/scan.inl",
        "cuda/include/thrust/system/detail/generic/scan_by_key.h",
        "cuda/include/thrust/system/detail/generic/scan_by_key.inl",
        "cuda/include/thrust/system/detail/generic/scatter.h",
        "cuda/include/thrust/system/detail/generic/scatter.inl",
        "cuda/include/thrust/system/detail/generic/select_system.h",
        "cuda/include/thrust/system/detail/generic/sequence.h",
        "cuda/include/thrust/system/detail/generic/sequence.inl",
        "cuda/include/thrust/system/detail/generic/set_operations.h",
        "cuda/include/thrust/system/detail/generic/set_operations.inl",
        "cuda/include/thrust/system/detail/generic/sort.h",
        "cuda/include/thrust/system/detail/generic/sort.inl",
        "cuda/include/thrust/system/detail/generic/swap_ranges.h",
        "cuda/include/thrust/system/detail/generic/swap_ranges.inl",
        "cuda/include/thrust/system/detail/generic/tabulate.h",
        "cuda/include/thrust/system/detail/generic/tabulate.inl",
        "cuda/include/thrust/system/detail/generic/tag.h",
        "cuda/include/thrust/system/detail/generic/temporary_buffer.h",
        "cuda/include/thrust/system/detail/generic/temporary_buffer.inl",
        "cuda/include/thrust/system/detail/generic/transform.h",
        "cuda/include/thrust/system/detail/generic/transform.inl",
        "cuda/include/thrust/system/detail/generic/transform_reduce.h",
        "cuda/include/thrust/system/detail/generic/transform_reduce.inl",
        "cuda/include/thrust/system/detail/generic/transform_scan.h",
        "cuda/include/thrust/system/detail/generic/transform_scan.inl",
        "cuda/include/thrust/system/detail/generic/type_traits.h",
        "cuda/include/thrust/system/detail/generic/uninitialized_copy.h",
        "cuda/include/thrust/system/detail/generic/uninitialized_copy.inl",
        "cuda/include/thrust/system/detail/generic/uninitialized_fill.h",
        "cuda/include/thrust/system/detail/generic/uninitialized_fill.inl",
        "cuda/include/thrust/system/detail/generic/unique.h",
        "cuda/include/thrust/system/detail/generic/unique.inl",
        "cuda/include/thrust/system/detail/generic/unique_by_key.h",
        "cuda/include/thrust/system/detail/generic/unique_by_key.inl",
        "cuda/include/thrust/system/detail/internal/decompose.h",
        "cuda/include/thrust/system/detail/sequential/adjacent_difference.h",
        "cuda/include/thrust/system/detail/sequential/assign_value.h",
        "cuda/include/thrust/system/detail/sequential/binary_search.h",
        "cuda/include/thrust/system/detail/sequential/copy.h",
        "cuda/include/thrust/system/detail/sequential/copy.inl",
        "cuda/include/thrust/system/detail/sequential/copy_backward.h",
        "cuda/include/thrust/system/detail/sequential/copy_if.h",
        "cuda/include/thrust/system/detail/sequential/count.h",
        "cuda/include/thrust/system/detail/sequential/equal.h",
        "cuda/include/thrust/system/detail/sequential/execution_policy.h",
        "cuda/include/thrust/system/detail/sequential/extrema.h",
        "cuda/include/thrust/system/detail/sequential/fill.h",
        "cuda/include/thrust/system/detail/sequential/find.h",
        "cuda/include/thrust/system/detail/sequential/for_each.h",
        "cuda/include/thrust/system/detail/sequential/gather.h",
        "cuda/include/thrust/system/detail/sequential/general_copy.h",
        "cuda/include/thrust/system/detail/sequential/generate.h",
        "cuda/include/thrust/system/detail/sequential/get_value.h",
        "cuda/include/thrust/system/detail/sequential/inner_product.h",
        "cuda/include/thrust/system/detail/sequential/insertion_sort.h",
        "cuda/include/thrust/system/detail/sequential/iter_swap.h",
        "cuda/include/thrust/system/detail/sequential/logical.h",
        "cuda/include/thrust/system/detail/sequential/malloc_and_free.h",
        "cuda/include/thrust/system/detail/sequential/merge.h",
        "cuda/include/thrust/system/detail/sequential/merge.inl",
        "cuda/include/thrust/system/detail/sequential/mismatch.h",
        "cuda/include/thrust/system/detail/sequential/partition.h",
        "cuda/include/thrust/system/detail/sequential/reduce.h",
        "cuda/include/thrust/system/detail/sequential/reduce_by_key.h",
        "cuda/include/thrust/system/detail/sequential/remove.h",
        "cuda/include/thrust/system/detail/sequential/replace.h",
        "cuda/include/thrust/system/detail/sequential/reverse.h",
        "cuda/include/thrust/system/detail/sequential/scan.h",
        "cuda/include/thrust/system/detail/sequential/scan_by_key.h",
        "cuda/include/thrust/system/detail/sequential/scatter.h",
        "cuda/include/thrust/system/detail/sequential/sequence.h",
        "cuda/include/thrust/system/detail/sequential/set_operations.h",
        "cuda/include/thrust/system/detail/sequential/sort.h",
        "cuda/include/thrust/system/detail/sequential/sort.inl",
        "cuda/include/thrust/system/detail/sequential/stable_merge_sort.h",
        "cuda/include/thrust/system/detail/sequential/stable_merge_sort.inl",
        "cuda/include/thrust/system/detail/sequential/stable_primitive_sort.h",
        "cuda/include/thrust/system/detail/sequential/stable_primitive_sort.inl",
        "cuda/include/thrust/system/detail/sequential/stable_radix_sort.h",
        "cuda/include/thrust/system/detail/sequential/stable_radix_sort.inl",
        "cuda/include/thrust/system/detail/sequential/swap_ranges.h",
        "cuda/include/thrust/system/detail/sequential/tabulate.h",
        "cuda/include/thrust/system/detail/sequential/temporary_buffer.h",
        "cuda/include/thrust/system/detail/sequential/transform.h",
        "cuda/include/thrust/system/detail/sequential/transform_reduce.h",
        "cuda/include/thrust/system/detail/sequential/transform_scan.h",
        "cuda/include/thrust/system/detail/sequential/trivial_copy.h",
        "cuda/include/thrust/system/detail/sequential/uninitialized_copy.h",
        "cuda/include/thrust/system/detail/sequential/uninitialized_fill.h",
        "cuda/include/thrust/system/detail/sequential/unique.h",
        "cuda/include/thrust/system/detail/sequential/unique_by_key.h",
        "cuda/include/thrust/system/detail/system_error.inl",
        "cuda/include/thrust/system/error_code.h",
        "cuda/include/thrust/system/omp/detail/adjacent_difference.h",
        "cuda/include/thrust/system/omp/detail/assign_value.h",
        "cuda/include/thrust/system/omp/detail/binary_search.h",
        "cuda/include/thrust/system/omp/detail/copy.h",
        "cuda/include/thrust/system/omp/detail/copy.inl",
        "cuda/include/thrust/system/omp/detail/copy_if.h",
        "cuda/include/thrust/system/omp/detail/copy_if.inl",
        "cuda/include/thrust/system/omp/detail/count.h",
        "cuda/include/thrust/system/omp/detail/default_decomposition.h",
        "cuda/include/thrust/system/omp/detail/default_decomposition.inl",
        "cuda/include/thrust/system/omp/detail/equal.h",
        "cuda/include/thrust/system/omp/detail/execution_policy.h",
        "cuda/include/thrust/system/omp/detail/extrema.h",
        "cuda/include/thrust/system/omp/detail/fill.h",
        "cuda/include/thrust/system/omp/detail/find.h",
        "cuda/include/thrust/system/omp/detail/for_each.h",
        "cuda/include/thrust/system/omp/detail/for_each.inl",
        "cuda/include/thrust/system/omp/detail/gather.h",
        "cuda/include/thrust/system/omp/detail/generate.h",
        "cuda/include/thrust/system/omp/detail/get_value.h",
        "cuda/include/thrust/system/omp/detail/inner_product.h",
        "cuda/include/thrust/system/omp/detail/iter_swap.h",
        "cuda/include/thrust/system/omp/detail/logical.h",
        "cuda/include/thrust/system/omp/detail/malloc_and_free.h",
        "cuda/include/thrust/system/omp/detail/memory.inl",
        "cuda/include/thrust/system/omp/detail/merge.h",
        "cuda/include/thrust/system/omp/detail/mismatch.h",
        "cuda/include/thrust/system/omp/detail/par.h",
        "cuda/include/thrust/system/omp/detail/partition.h",
        "cuda/include/thrust/system/omp/detail/partition.inl",
        "cuda/include/thrust/system/omp/detail/reduce.h",
        "cuda/include/thrust/system/omp/detail/reduce.inl",
        "cuda/include/thrust/system/omp/detail/reduce_by_key.h",
        "cuda/include/thrust/system/omp/detail/reduce_by_key.inl",
        "cuda/include/thrust/system/omp/detail/reduce_intervals.h",
        "cuda/include/thrust/system/omp/detail/reduce_intervals.inl",
        "cuda/include/thrust/system/omp/detail/remove.h",
        "cuda/include/thrust/system/omp/detail/remove.inl",
        "cuda/include/thrust/system/omp/detail/replace.h",
        "cuda/include/thrust/system/omp/detail/reverse.h",
        "cuda/include/thrust/system/omp/detail/scan.h",
        "cuda/include/thrust/system/omp/detail/scan_by_key.h",
        "cuda/include/thrust/system/omp/detail/scatter.h",
        "cuda/include/thrust/system/omp/detail/sequence.h",
        "cuda/include/thrust/system/omp/detail/set_operations.h",
        "cuda/include/thrust/system/omp/detail/sort.h",
        "cuda/include/thrust/system/omp/detail/sort.inl",
        "cuda/include/thrust/system/omp/detail/swap_ranges.h",
        "cuda/include/thrust/system/omp/detail/tabulate.h",
        "cuda/include/thrust/system/omp/detail/temporary_buffer.h",
        "cuda/include/thrust/system/omp/detail/transform.h",
        "cuda/include/thrust/system/omp/detail/transform_reduce.h",
        "cuda/include/thrust/system/omp/detail/transform_scan.h",
        "cuda/include/thrust/system/omp/detail/uninitialized_copy.h",
        "cuda/include/thrust/system/omp/detail/uninitialized_fill.h",
        "cuda/include/thrust/system/omp/detail/unique.h",
        "cuda/include/thrust/system/omp/detail/unique.inl",
        "cuda/include/thrust/system/omp/detail/unique_by_key.h",
        "cuda/include/thrust/system/omp/detail/unique_by_key.inl",
        "cuda/include/thrust/system/omp/detail/vector.inl",
        "cuda/include/thrust/system/omp/execution_policy.h",
        "cuda/include/thrust/system/omp/memory.h",
        "cuda/include/thrust/system/omp/vector.h",
        "cuda/include/thrust/system/system_error.h",
        "cuda/include/thrust/system/tbb/detail/adjacent_difference.h",
        "cuda/include/thrust/system/tbb/detail/assign_value.h",
        "cuda/include/thrust/system/tbb/detail/binary_search.h",
        "cuda/include/thrust/system/tbb/detail/copy.h",
        "cuda/include/thrust/system/tbb/detail/copy.inl",
        "cuda/include/thrust/system/tbb/detail/copy_if.h",
        "cuda/include/thrust/system/tbb/detail/copy_if.inl",
        "cuda/include/thrust/system/tbb/detail/count.h",
        "cuda/include/thrust/system/tbb/detail/equal.h",
        "cuda/include/thrust/system/tbb/detail/execution_policy.h",
        "cuda/include/thrust/system/tbb/detail/extrema.h",
        "cuda/include/thrust/system/tbb/detail/fill.h",
        "cuda/include/thrust/system/tbb/detail/find.h",
        "cuda/include/thrust/system/tbb/detail/for_each.h",
        "cuda/include/thrust/system/tbb/detail/for_each.inl",
        "cuda/include/thrust/system/tbb/detail/gather.h",
        "cuda/include/thrust/system/tbb/detail/generate.h",
        "cuda/include/thrust/system/tbb/detail/get_value.h",
        "cuda/include/thrust/system/tbb/detail/inner_product.h",
        "cuda/include/thrust/system/tbb/detail/iter_swap.h",
        "cuda/include/thrust/system/tbb/detail/logical.h",
        "cuda/include/thrust/system/tbb/detail/malloc_and_free.h",
        "cuda/include/thrust/system/tbb/detail/memory.inl",
        "cuda/include/thrust/system/tbb/detail/merge.h",
        "cuda/include/thrust/system/tbb/detail/merge.inl",
        "cuda/include/thrust/system/tbb/detail/mismatch.h",
        "cuda/include/thrust/system/tbb/detail/par.h",
        "cuda/include/thrust/system/tbb/detail/partition.h",
        "cuda/include/thrust/system/tbb/detail/partition.inl",
        "cuda/include/thrust/system/tbb/detail/reduce.h",
        "cuda/include/thrust/system/tbb/detail/reduce.inl",
        "cuda/include/thrust/system/tbb/detail/reduce_by_key.h",
        "cuda/include/thrust/system/tbb/detail/reduce_by_key.inl",
        "cuda/include/thrust/system/tbb/detail/reduce_intervals.h",
        "cuda/include/thrust/system/tbb/detail/remove.h",
        "cuda/include/thrust/system/tbb/detail/remove.inl",
        "cuda/include/thrust/system/tbb/detail/replace.h",
        "cuda/include/thrust/system/tbb/detail/reverse.h",
        "cuda/include/thrust/system/tbb/detail/scan.h",
        "cuda/include/thrust/system/tbb/detail/scan.inl",
        "cuda/include/thrust/system/tbb/detail/scan_by_key.h",
        "cuda/include/thrust/system/tbb/detail/scatter.h",
        "cuda/include/thrust/system/tbb/detail/sequence.h",
        "cuda/include/thrust/system/tbb/detail/set_operations.h",
        "cuda/include/thrust/system/tbb/detail/sort.h",
        "cuda/include/thrust/system/tbb/detail/sort.inl",
        "cuda/include/thrust/system/tbb/detail/swap_ranges.h",
        "cuda/include/thrust/system/tbb/detail/tabulate.h",
        "cuda/include/thrust/system/tbb/detail/temporary_buffer.h",
        "cuda/include/thrust/system/tbb/detail/transform.h",
        "cuda/include/thrust/system/tbb/detail/transform_reduce.h",
        "cuda/include/thrust/system/tbb/detail/transform_scan.h",
        "cuda/include/thrust/system/tbb/detail/uninitialized_copy.h",
        "cuda/include/thrust/system/tbb/detail/uninitialized_fill.h",
        "cuda/include/thrust/system/tbb/detail/unique.h",
        "cuda/include/thrust/system/tbb/detail/unique.inl",
        "cuda/include/thrust/system/tbb/detail/unique_by_key.h",
        "cuda/include/thrust/system/tbb/detail/unique_by_key.inl",
        "cuda/include/thrust/system/tbb/detail/vector.inl",
        "cuda/include/thrust/system/tbb/execution_policy.h",
        "cuda/include/thrust/system/tbb/memory.h",
        "cuda/include/thrust/system/tbb/vector.h",
        "cuda/include/thrust/system_error.h",
        "cuda/include/thrust/tabulate.h",
        "cuda/include/thrust/transform.h",
        "cuda/include/thrust/transform_reduce.h",
        "cuda/include/thrust/transform_scan.h",
        "cuda/include/thrust/tuple.h",
        "cuda/include/thrust/uninitialized_copy.h",
        "cuda/include/thrust/uninitialized_fill.h",
        "cuda/include/thrust/unique.h",
        "cuda/include/thrust/version.h",
        "cuda/include/vector_functions.h",
        "cuda/include/vector_functions.hpp",
        "cuda/include/vector_types.h",
    ],
    cmd = """cp -rLf "/usr/local/cuda-10.0/include/." "$(@D)/cuda/include/" """,
)

genrule(
    name = "cuda-nvvm",
    outs = [
        "cuda/nvvm/libdevice/libdevice.10.bc",
    ],
    cmd = """cp -rLf "/usr/local/cuda-10.0/nvvm/libdevice/." "$(@D)/" """,
)

genrule(
    name = "cuda-extras",
    outs = [
        "cuda/extras/CUPTI/include/GL/gl.h",
        "cuda/extras/CUPTI/include/GL/glew.h",
        "cuda/extras/CUPTI/include/GL/glext.h",
        "cuda/extras/CUPTI/include/GL/glu.h",
        "cuda/extras/CUPTI/include/GL/glut.h",
        "cuda/extras/CUPTI/include/GL/glx.h",
        "cuda/extras/CUPTI/include/GL/glxext.h",
        "cuda/extras/CUPTI/include/GL/wglew.h",
        "cuda/extras/CUPTI/include/GL/wglext.h",
        "cuda/extras/CUPTI/include/cuda_stdint.h",
        "cuda/extras/CUPTI/include/cupti.h",
        "cuda/extras/CUPTI/include/cupti_activity.h",
        "cuda/extras/CUPTI/include/cupti_callbacks.h",
        "cuda/extras/CUPTI/include/cupti_driver_cbid.h",
        "cuda/extras/CUPTI/include/cupti_events.h",
        "cuda/extras/CUPTI/include/cupti_metrics.h",
        "cuda/extras/CUPTI/include/cupti_nvtx_cbid.h",
        "cuda/extras/CUPTI/include/cupti_result.h",
        "cuda/extras/CUPTI/include/cupti_runtime_cbid.h",
        "cuda/extras/CUPTI/include/cupti_version.h",
        "cuda/extras/CUPTI/include/generated_cudaGL_meta.h",
        "cuda/extras/CUPTI/include/generated_cudaVDPAU_meta.h",
        "cuda/extras/CUPTI/include/generated_cuda_gl_interop_meta.h",
        "cuda/extras/CUPTI/include/generated_cuda_meta.h",
        "cuda/extras/CUPTI/include/generated_cuda_runtime_api_meta.h",
        "cuda/extras/CUPTI/include/generated_cuda_vdpau_interop_meta.h",
        "cuda/extras/CUPTI/include/generated_nvtx_meta.h",
        "cuda/extras/CUPTI/include/openacc/cupti_openacc.h",
        "cuda/extras/CUPTI/include/openmp/cupti_openmp.h",
        "cuda/extras/CUPTI/include/openmp/ompt.h",
    ],
    cmd = """cp -rLf "/usr/local/cuda-10.0/extras/CUPTI/include/." "$(@D)/cuda/extras/CUPTI/include/" """,
)

genrule(
    name = "cuda-lib",
    outs = [
        "cuda/lib/libcuda.so",
        "cuda/lib/libcudart.so.10.0",
        "cuda/lib/libcudart_static.a",
        "cuda/lib/libcublas.so.10.0",
        "cuda/lib/libcusolver.so.10.0",
        "cuda/lib/libcurand.so.10.0",
        "cuda/lib/libcufft.so.10.0",
        "cuda/lib/libcudnn.so.7",
        "cuda/lib/libcupti.so.10.0",
        "cuda/lib/libcusparse.so.10.0",
    ],
    cmd = """cp -f "/usr/local/cuda-10.0/lib64/stubs/libcuda.so" $(location cuda/lib/libcuda.so) && cp -f "/usr/local/cuda-10.0/lib64/libcudart.so.10.0" $(location cuda/lib/libcudart.so.10.0) && cp -f "/usr/local/cuda-10.0/lib64/libcudart_static.a" $(location cuda/lib/libcudart_static.a) && cp -f "/usr/local/cuda-10.0/lib64/libcublas.so.10.0" $(location cuda/lib/libcublas.so.10.0) && cp -f "/usr/local/cuda-10.0/lib64/libcusolver.so.10.0" $(location cuda/lib/libcusolver.so.10.0) && cp -f "/usr/local/cuda-10.0/lib64/libcusparse.so.10.0" $(location cuda/lib/libcusparse.so.10.0) && cp -f "/usr/local/cuda-10.0/lib64/libcurand.so.10.0" $(location cuda/lib/libcurand.so.10.0) && cp -f "/usr/local/cuda-10.0/lib64/libcufft.so.10.0" $(location cuda/lib/libcufft.so.10.0) && cp -f "/usr/lib/x86_64-linux-gnu/libcudnn.so.7" $(location cuda/lib/libcudnn.so.7) && cp -f "/usr/local/cuda-10.0/extras/CUPTI/lib64/libcupti.so.10.0" $(location cuda/lib/libcupti.so.10.0) """,
)

genrule(
    name = "cuda-bin",
    outs = [
        "cuda/bin/bin2c",
        "cuda/bin/crt/link.stub",
        "cuda/bin/crt/prelink.stub",
        "cuda/bin/cuda-gdb",
        "cuda/bin/cuda-gdbserver",
        "cuda/bin/cuda-memcheck",
        "cuda/bin/cudafe++",
        "cuda/bin/cuobjdump",
        "cuda/bin/fatbinary",
        "cuda/bin/gpu-library-advisor",
        "cuda/bin/nvcc",
        "cuda/bin/nvcc.profile",
        "cuda/bin/nvdisasm",
        "cuda/bin/nvlink",
        "cuda/bin/nvprof",
        "cuda/bin/nvprune",
        "cuda/bin/ptxas",
    ],
    cmd = """cp -rLf "/usr/local/cuda-10.0/bin/." "$(@D)/cuda/bin/" """,
)

genrule(
    name = "cudnn-include",
    outs = [
        "cuda/include/cudnn.h",
    ],
    cmd = """cp -f "/usr/include/cudnn.h" $(location cuda/include/cudnn.h) """,
)

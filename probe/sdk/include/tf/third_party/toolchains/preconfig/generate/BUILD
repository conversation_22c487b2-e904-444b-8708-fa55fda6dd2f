licenses(["restricted"])

load(":generate.bzl", "tensorflow_rbe_config")

tensorflow_rbe_config(
    name = "ubuntu16.04-py3-clang",
    compiler = "clang",
    python_version = "3",
)

tensorflow_rbe_config(
    name = "ubuntu14.04-py3-gcc-cuda9.0-cudnn7-tensorrt5",
    compiler = "gcc",
    cuda_version = "9.0",
    cudnn_version = "7",
    python_version = "3",
    tensorrt_version = "5",
)

tensorflow_rbe_config(
    name = "ubuntu14.04-py3-clang-cuda9.0-cudnn7-tensorrt5",
    compiler = "clang",
    cuda_version = "9.0",
    cudnn_version = "7",
    python_version = "3",
    tensorrt_version = "5",
)

tensorflow_rbe_config(
    name = "ubuntu14.04-py3-gcc7-cuda10.0-cudnn7-tensorrt5",
    compiler = "gcc-7",
    cuda_version = "10.0",
    cudnn_version = "7",
    python_version = "3",
    tensorrt_version = "5",
)

tensorflow_rbe_config(
    name = "ubuntu14.04-py3-gcc-cuda10.0-cudnn7-tensorrt5",
    compiler = "gcc",
    cuda_version = "10.0",
    cudnn_version = "7",
    python_version = "3",
    tensorrt_version = "5",
)

tensorflow_rbe_config(
    name = "ubuntu14.04-py3-clang-cuda10.0-cudnn7-tensorrt5",
    compiler = "clang",
    cuda_version = "10.0",
    cudnn_version = "7",
    python_version = "3",
    tensorrt_version = "5",
)

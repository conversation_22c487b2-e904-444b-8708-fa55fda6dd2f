# Description:
#   AST round-trip manipulation for Python.

licenses(["notice"])  # New BSD

exports_files(["LICENSE"])

py_library(
    name = "astor",
    srcs = [
        "astor/__init__.py",
        "astor/code_gen.py",
        "astor/codegen.py",
        "astor/file_util.py",
        "astor/node_util.py",
        "astor/op_util.py",
        "astor/rtrip.py",
        "astor/source_repr.py",
        "astor/string_repr.py",
        "astor/tree_walk.py",
    ],
    srcs_version = "PY2AND3",
    visibility = ["//visibility:public"],
)

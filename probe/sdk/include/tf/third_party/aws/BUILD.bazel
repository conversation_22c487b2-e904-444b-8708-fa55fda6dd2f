# Description:
#   AWS C++ SDK

package(default_visibility = ["//visibility:public"])

licenses(["notice"])  # Apache 2.0

exports_files(["LICENSE"])

load("@org_tensorflow//third_party:common.bzl", "template_rule")

cc_library(
    name = "aws",
    srcs = select({
        "@org_tensorflow//tensorflow:linux_x86_64": glob([
            "aws-cpp-sdk-core/source/platform/linux-shared/*.cpp",
        ]),
        "@org_tensorflow//tensorflow:macos": glob([
            "aws-cpp-sdk-core/source/platform/linux-shared/*.cpp",
        ]),
        "@org_tensorflow//tensorflow:linux_ppc64le": glob([
            "aws-cpp-sdk-core/source/platform/linux-shared/*.cpp",
        ]),
        "@org_tensorflow//tensorflow:raspberry_pi_armeabi": glob([
            "aws-cpp-sdk-core/source/platform/linux-shared/*.cpp",
        ]),
        "//conditions:default": [],
    }) + glob([
        "aws-cpp-sdk-core/include/**/*.h",
        "aws-cpp-sdk-core/source/*.cpp",
        "aws-cpp-sdk-core/source/auth/**/*.cpp",
        "aws-cpp-sdk-core/source/config/**/*.cpp",
        "aws-cpp-sdk-core/source/client/**/*.cpp",
        "aws-cpp-sdk-core/source/external/**/*.cpp",
        "aws-cpp-sdk-core/source/internal/**/*.cpp",
        "aws-cpp-sdk-core/source/http/*.cpp",
        "aws-cpp-sdk-core/source/http/curl/**/*.cpp",
        "aws-cpp-sdk-core/source/http/standard/**/*.cpp",
        "aws-cpp-sdk-core/source/utils/*.cpp",
        "aws-cpp-sdk-core/source/utils/base64/**/*.cpp",
        "aws-cpp-sdk-core/source/utils/json/**/*.cpp",
        "aws-cpp-sdk-core/source/utils/logging/**/*.cpp",
        "aws-cpp-sdk-core/source/utils/memory/**/*.cpp",
        "aws-cpp-sdk-core/source/utils/stream/**/*.cpp",
        "aws-cpp-sdk-core/source/utils/threading/**/*.cpp",
        "aws-cpp-sdk-core/source/utils/xml/**/*.cpp",
        "aws-cpp-sdk-core/source/utils/crypto/*.cpp",
        "aws-cpp-sdk-core/source/utils/crypto/factory/**/*.cpp",
        "aws-cpp-sdk-kinesis/include/**/*.h",
        "aws-cpp-sdk-kinesis/source/**/*.cpp",
        "aws-cpp-sdk-s3/include/**/*.h",
        "aws-cpp-sdk-s3/source/**/*.cpp",
    ]),
    hdrs = [
        "aws-cpp-sdk-core/include/aws/core/SDKConfig.h",
    ],
    copts = [
        "-DAWS_SDK_VERSION_MAJOR=1",
        "-DAWS_SDK_VERSION_MINOR=5",
        "-DAWS_SDK_VERSION_PATCH=8",
    ],
    defines = select({
        "@org_tensorflow//tensorflow:linux_x86_64": [
            "PLATFORM_LINUX",
            "ENABLE_CURL_CLIENT",
            "ENABLE_NO_ENCRYPTION",
        ],
        "@org_tensorflow//tensorflow:macos": [
            "PLATFORM_APPLE",
            "ENABLE_CURL_CLIENT",
            "ENABLE_NO_ENCRYPTION",
        ],
        "@org_tensorflow//tensorflow:linux_ppc64le": [
            "PLATFORM_LINUX",
            "ENABLE_CURL_CLIENT",
            "ENABLE_NO_ENCRYPTION",
        ],
        "//conditions:default": [],
    }),
    includes = [
        "aws-cpp-sdk-core/include/",
        "aws-cpp-sdk-kinesis/include/",
        "aws-cpp-sdk-s3/include/",
    ],
    deps = [
        "@curl",
    ],
)

template_rule(
    name = "SDKConfig_h",
    src = "aws-cpp-sdk-core/include/aws/core/SDKConfig.h.in",
    out = "aws-cpp-sdk-core/include/aws/core/SDKConfig.h",
    substitutions = {
        "cmakedefine": "define",
    },
)

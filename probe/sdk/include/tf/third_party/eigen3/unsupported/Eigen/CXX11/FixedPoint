// This file is part of Eigen, a lightweight C++ template library
// for linear algebra.
//
// Copyright (C) 2015 <PERSON><PERSON> <<EMAIL>>
//
// This Source Code Form is subject to the terms of the Mozilla
// Public License v. 2.0. If a copy of the MPL was not distributed
// with this file, You can obtain one at http://mozilla.org/MPL/2.0/.

#ifndef EIGEN_CXX11_FIXED_POINT_MODULE
#define EIGEN_CXX11_FIXED_POINT_MODULE

#include <Eigen/Core>
#include <stdint.h>

/** \defgroup CXX11_FixedPoint_Module Fixed Point Module
  *
  * This module provides common core features for all modules that
  * explicitly depend on C++11. Currently, this is only the Tensor
  * module. Note that at this stage, you should not need to include
  * this module directly.
  *
  * It also provides a limited fallback for compilers that don't support
  * CXX11 yet, such as nvcc.
  *
  * \code
  * #include <Eigen/CXX11/FixedPoint>
  * \endcode
  */

#include "src/FixedPoint/FixedPointTypes.h"

// Use optimized implementations whenever available
#if defined (EIGEN_VECTORIZE_AVX512DQ) || defined (EIGEN_VECTORIZE_AVX512BW)
#include "src/FixedPoint/PacketMathAVX512.h"
#include "src/FixedPoint/TypeCastingAVX512.h"

#elif defined EIGEN_VECTORIZE_AVX2
#define EIGEN_USE_OPTIMIZED_INT8_UINT8_MAT_MAT_PRODUCT
#define EIGEN_USE_OPTIMIZED_INT16_INT16_MAT_MAT_PRODUCT
#include "src/FixedPoint/PacketMathAVX2.h"
#include "src/FixedPoint/MatMatProductAVX2.h"
#include "src/FixedPoint/TypeCastingAVX2.h"

#elif defined EIGEN_VECTORIZE_NEON
#define EIGEN_USE_OPTIMIZED_INT8_UINT8_MAT_MAT_PRODUCT
#include "src/FixedPoint/MatMatProductNEON.h"
#endif

// Use the default implementation when no optimized code is available
#include "src/FixedPoint/MatMatProduct.h"
#include "src/FixedPoint/MatVecProduct.h"


#endif // EIGEN_CXX11_FIXED_POINT_MODULE

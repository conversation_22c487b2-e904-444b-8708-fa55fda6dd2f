
---------------------------------------------------------------------

SOFTWARE LICENSE AGREEMENT

---------------------------------------------------------------------
---------------------------------------------------------------------

By downloading, installing, copying, or otherwise using the
ComputeCpp Community Edition software, including any associated
components, media, printed materials, and electronic documentation
("Software"), the user agrees to the following terms and conditions
of this Software License Agreement ("Agreement"). Please read the
terms of this Agreement carefully before beginning your download, as
pressing the "I AGREE" button at the end of this Agreement will
confirm your assent. If you do not agree to these terms, then
Codeplay Software Limited is unwilling to license the Software to
you; so please press the "CANCEL" button to cancel your download.

 1. License. Codeplay Software Ltd., a company incorporated in
    England and Wales with registered number 04567874 and having its
    registered office at Regent House, 316 Beulah Hill, London,
    United Kingdom, SE19 3HF ("Codeplay") hereby grants the user,
    free of charge, a non-exclusive worldwide license to use and
    replicate (but not modify) the Software for any use, whether
    commercial or non-commercial, in accordance with this Agreement.
    Codeplay reserves all rights to the Software that are not
    expressly granted by this Agreement.
 2. Redistribution. The user may copy and redistribute unmodified
    copies of only those components of the Software which are
    specified below ("Redistributable Components"), in object code
    form, as part of the user’s software applications or libraries
    ("Applications"). The user acknowledges and agrees that it has no
    right to modify the Redistributable Components in any way. Any
    use of the Redistributable Components within the user’s
    Applications will continue to be subject to the terms and
    conditions of this Agreement, and the user must also distribute a
    copy of this Agreement and reproduce and include all notices of
    copyrights or other proprietary rights in the Software. The
    user’s redistribution of the Redistributable Components will not
    entitle it to any payment from Codeplay. The user may not
    transfer any of its rights or obligations under this Agreement.

+-------------------------------------------+
|Redistributable Component|File Name        |
|-------------------------+-----------------|
|Runtime (for Linux)      |libComputeCpp.so |
|-------------------------+-----------------|
|Runtime (for Windows)    |libComputeCpp.dll|
+-------------------------------------------+

 3. Restrictions. The user shall not:

     a. circumvent or bypass any technological protection measures in
        or relating to the Software;
     b. use the Software to perform any unauthorized transfer of
        information or for any illegal purpose;
     c. de-compile, decrypt, disassemble, hack, emulate, exploit or
        reverse-engineer the Software (other than to the limited
        extent permitted by law);
     d. copy or redistribute any components of the Software that are
        not listed in the table of Redistributable Components;
     e. publish, rent, lease, sell, export, import, or lend the
        Software;
     f. represent in any way that it is selling the Software itself
        or any license to use the Software, nor refer to Codeplay or
        ComputeCpp within its marketing materials, without the
        express prior written permission of Codeplay.
 4. Support. Codeplay does not provide any guarantees of support for
    the Software to the user. Codeplay will use reasonable endeavors
    to respond to users' support requests, for the most recent
    release only, via the community support website at https://
    computecpp.codeplay.com.
 5. Intellectual Property. The Software is owned by Codeplay or its
    licensors, and is protected by the copyright laws of the United
    Kingdom and other countries and international treaty provisions.
    Codeplay (and/or its licensors, as the case may be) retains all
    copyrights, trade secrets and other proprietary rights in the
    Software, including the rights to make and license the use of all
    copies. To the extent that any patents owned by Codeplay or its
    licensors relate to any component of the Software, the license
    granted to the user in accordance with this Agreement allows for
    the lawful use of such patents but only for the purposes of this
    Agreement and not further or otherwise. Therefore, the user may
    make no copies of the Software, or the written materials that
    accompany the Software, or reproduce it in any way, except as set
    forth above.
 6. Terms. This Agreement is effective until terminated. Codeplay or
    the user may terminate it immediately at any time. Any violation
    of the terms of this Agreement by the user will result in
    immediate termination by Codeplay. Upon termination, the user
    must return or destroy the Software and accompanying materials
    and notify Codeplay of its actions by <NAME_EMAIL>.
 7. NO WARRANTIES. Codeplay expressly disclaims any warranty for the
    Software. THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF
    ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
    WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE
    AND NON-INFRINGEMENT. IN NO EVENT SHALL CODEPLAY BE LIABLE FOR
    ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF
    CONTRACT, DELICT OR TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
    CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
    SOFTWARE. In particular, Codeplay provides no guarantees of
    application performance on the target hardware.
 8. General. The invalidity of any portion or provision of this
    Agreement shall not affect any other portions or provisions. This
    Agreement shall be governed by the laws of Scotland. This
    Agreement is the complete and exclusive agreement between the
    user and Codeplay regarding the Software, and it supersedes any
    prior agreement, oral or written, and any other communication
    between the user and Codeplay relating to the subject matter of
    the Agreement. Any amendment or modification of this Agreement
    must be in writing and signed by both parties. If the user does
    not agree to the terms of this Agreement, the user must not
    install or use the Software.
 9. Third Party Licenses. The following licenses are for third-party
    components included in the software.

     a. License for Clang/LLVM compiler technology components:

==============================================================================

LLVM Release License

==============================================================================

University of Illinois/NCSA

Open Source License

Copyright (c) 2007-2014 University of Illinois at Urbana-Champaign.

All rights reserved.

Developed by:

 LLVM Team

 University of Illinois at Urbana-Champaign

 http://llvm.org

Permission is hereby granted, free of charge, to any person obtaining a copy of
this software and associated documentation files (the "Software"), to deal with
the Software without restriction, including without limitation the rights to
use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies
of the Software, and to permit persons to whom the Software is furnished to do
so, subject to the following conditions:

 * Redistributions of source code must retain the above copyright notice,
 this list of conditions and the following disclaimers.

 * Redistributions in binary form must reproduce the above copyright notice,
 this list of conditions and the following disclaimers in the
 documentation and/or other materials provided with the distribution.

 * Neither the names of the LLVM Team, University of Illinois at
 Urbana-Champaign, nor the names of its contributors may be used to
 endorse or promote products derived from this Software without specific
 prior written permission.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
CONTRIBUTORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS WITH THE
SOFTWARE.

==============================================================================

 b. License for OpenBSD regex components:

$OpenBSD: COPYRIGHT,v 1.3 2003/06/02 20:18:36 millert Exp $
Copyright 1992, 1993, 1994 Henry Spencer. All rights reserved.
This software is not subject to any license of the American Telephone
and Telegraph Company or of the Regents of the University of California.
Permission is granted to anyone to use this software for any purpose on
any computer system, and to alter it and redistribute it, subject
to the following restrictions:

1. The author is not responsible for the consequences of use of this
 software, no matter how awful, even if they arise from flaws in it.

2. The origin of this software must not be misrepresented, either by
 explicit claim or by omission. Since few users ever read sources,
 credits must appear in the documentation.

3. Altered versions must be plainly marked as such, and must not be
 misrepresented as being the original software. Since few users
 ever read sources, credits must appear in the documentation.

4. This notice may not be removed or altered.

=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=

/*-
 * Copyright (c) 1994
 *      The Regents of the University of California. All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 * notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * 3. Neither the name of the University nor the names of its contributors
 * may be used to endorse or promote products derived from this software
 * without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
 * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 *
 *                  @(#)COPYRIGHT8.1 (Berkeley) 3/16/94
 */

 c. License for MD5 components:

/*
 * This code is derived from (original license follows):
 *
 * This is an OpenSSL-compatible implementation of the RSA Data Security, Inc.
 * MD5 Message-Digest Algorithm (RFC 1321).
 *
 * Homepage:
 *  http://openwall.info/wiki/people/solar/software/public-domain-source-code/md5
 *
 * Author:
 * Alexander Peslyak, better known as Solar Designer <solar at openwall.com>
 *
 * This software was written by Alexander Peslyak in 2001. No copyright is
 * claimed, and the software is hereby placed in the public domain.
 * In case this attempt to disclaim copyright and place the software in the
 * public domain is deemed null and void, then the software is
 * Copyright (c) 2001 Alexander Peslyak and it is hereby released to the
 * general public under the following terms:
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted.
 *
 * There's ABSOLUTELY NO WARRANTY, express or implied.
 *
 * (This is a heavily cut-down "BSD license".)
 *
 * This differs from Colin Plumb's older public domain implementation in that
 * no exactly 32-bit integer data type is required (any 32-bit or wider
 * unsigned integer data type will do), there's no compile-time endianness
 * configuration, and the function prototypes match OpenSSL's. No code from
 * Colin Plumb's implementation has been reused; this comment merely compares
 * the properties of the two independent implementations.
 *
 * The primary goals of this implementation are portability and ease of use.
 * It is meant to be fast, but not as fast as possible. Some known
 * optimizations are not included to reduce source code size and avoid
 * compile-time configuration.
 */



/*===- llvm/Config/AsmPrinters.def - LLVM Assembly Printers -----*- C++ -*-===*\
|*                                                                            *|
|* Part of the LLVM Project, under the Apache License v2.0 with LLVM          *|
|* Exceptions.                                                                *|
|* See https://llvm.org/LICENSE.txt for license information.                  *|
|* SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception                    *|
|*                                                                            *|
|*===----------------------------------------------------------------------===*|
|*                                                                            *|
|* This file enumerates all of the assembly-language printers                 *|
|* supported by this build of LLVM. Clients of this file should define        *|
|* the LLVM_ASM_PRINTER macro to be a function-like macro with a              *|
|* single parameter (the name of the target whose assembly can be             *|
|* generated); including this file will then enumerate all of the             *|
|* targets with assembly printers.                                            *|
|*                                                                            *|
|* The set of targets supported by LLVM is generated at configuration         *|
|* time, at which point this header is generated. Do not modify this          *|
|* header directly.                                                           *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

#ifndef LLVM_ASM_PRINTER
#  error Please define the macro LLVM_ASM_PRINTER(TargetName)
#endif

LLVM_ASM_PRINTER(AArch64)
LLVM_ASM_PRINTER(AMDGPU)
LLVM_ASM_PRINTER(ARM)
LLVM_ASM_PRINTER(NVPTX)
LLVM_ASM_PRINTER(PowerPC)
LLVM_ASM_PRINTER(X86)

#undef LLVM_ASM_PRINTER

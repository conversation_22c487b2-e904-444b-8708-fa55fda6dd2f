/*===- llvm/Config/Disassemblers.def - LLVM Assembly Parsers ----*- C++ -*-===*\
|*                                                                            *|
|* Part of the LLVM Project, under the Apache License v2.0 with LLVM          *|
|* Exceptions.                                                                *|
|* See https://llvm.org/LICENSE.txt for license information.                  *|
|* SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception                    *|
|*                                                                            *|
|*===----------------------------------------------------------------------===*|
|*                                                                            *|
|* This file enumerates all of the assembly-language parsers                  *|
|* supported by this build of LLVM. Clients of this file should define        *|
|* the LLVM_DISASSEMBLER macro to be a function-like macro with a             *|
|* single parameter (the name of the target whose assembly can be             *|
|* generated); including this file will then enumerate all of the             *|
|* targets with assembly parsers.                                             *|
|*                                                                            *|
|* The set of targets supported by LLVM is generated at configuration         *|
|* time, at which point this header is generated. Do not modify this          *|
|* header directly.                                                           *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

#ifndef LLVM_DISASSEMBLER
#  error Please define the macro LLVM_DISASSEMBLER(TargetName)
#endif

LLVM_DISASSEMBLER(AArch64)
LLVM_DISASSEMBLER(AMDGPU)
LLVM_DISASSEMBLER(ARM)
LLVM_DISASSEMBLER(NVPTX)
LLVM_DISASSEMBLER(PowerPC)
LLVM_DISASSEMBLER(X86)

#undef LLVM_DISASSEMBLER

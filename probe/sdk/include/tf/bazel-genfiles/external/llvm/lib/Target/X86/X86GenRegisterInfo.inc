/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Target Register Enum Values                                                *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/


#ifdef GET_REGINFO_ENUM
#undef GET_REGINFO_ENUM

namespace llvm {

class MCRegisterClass;
extern const MCRegisterClass X86MCRegisterClasses[];

namespace X86 {
enum {
  NoRegister,
  AH = 1,
  AL = 2,
  AX = 3,
  BH = 4,
  BL = 5,
  BP = 6,
  BPH = 7,
  BPL = 8,
  BX = 9,
  CH = 10,
  CL = 11,
  CS = 12,
  CX = 13,
  DF = 14,
  DH = 15,
  DI = 16,
  DIH = 17,
  DIL = 18,
  DL = 19,
  DS = 20,
  DX = 21,
  EAX = 22,
  EBP = 23,
  EBX = 24,
  ECX = 25,
  EDI = 26,
  EDX = 27,
  EFLAGS = 28,
  EIP = 29,
  EIZ = 30,
  ES = 31,
  ESI = 32,
  ESP = 33,
  FPCW = 34,
  FPSW = 35,
  FS = 36,
  GS = 37,
  HAX = 38,
  HBP = 39,
  HBX = 40,
  HCX = 41,
  HDI = 42,
  HDX = 43,
  HIP = 44,
  HSI = 45,
  HSP = 46,
  IP = 47,
  RAX = 48,
  RBP = 49,
  RBX = 50,
  RCX = 51,
  RDI = 52,
  RDX = 53,
  RIP = 54,
  RIZ = 55,
  RSI = 56,
  RSP = 57,
  SI = 58,
  SIH = 59,
  SIL = 60,
  SP = 61,
  SPH = 62,
  SPL = 63,
  SS = 64,
  SSP = 65,
  BND0 = 66,
  BND1 = 67,
  BND2 = 68,
  BND3 = 69,
  CR0 = 70,
  CR1 = 71,
  CR2 = 72,
  CR3 = 73,
  CR4 = 74,
  CR5 = 75,
  CR6 = 76,
  CR7 = 77,
  CR8 = 78,
  CR9 = 79,
  CR10 = 80,
  CR11 = 81,
  CR12 = 82,
  CR13 = 83,
  CR14 = 84,
  CR15 = 85,
  DR0 = 86,
  DR1 = 87,
  DR2 = 88,
  DR3 = 89,
  DR4 = 90,
  DR5 = 91,
  DR6 = 92,
  DR7 = 93,
  DR8 = 94,
  DR9 = 95,
  DR10 = 96,
  DR11 = 97,
  DR12 = 98,
  DR13 = 99,
  DR14 = 100,
  DR15 = 101,
  FP0 = 102,
  FP1 = 103,
  FP2 = 104,
  FP3 = 105,
  FP4 = 106,
  FP5 = 107,
  FP6 = 108,
  FP7 = 109,
  K0 = 110,
  K1 = 111,
  K2 = 112,
  K3 = 113,
  K4 = 114,
  K5 = 115,
  K6 = 116,
  K7 = 117,
  MM0 = 118,
  MM1 = 119,
  MM2 = 120,
  MM3 = 121,
  MM4 = 122,
  MM5 = 123,
  MM6 = 124,
  MM7 = 125,
  R8 = 126,
  R9 = 127,
  R10 = 128,
  R11 = 129,
  R12 = 130,
  R13 = 131,
  R14 = 132,
  R15 = 133,
  ST0 = 134,
  ST1 = 135,
  ST2 = 136,
  ST3 = 137,
  ST4 = 138,
  ST5 = 139,
  ST6 = 140,
  ST7 = 141,
  XMM0 = 142,
  XMM1 = 143,
  XMM2 = 144,
  XMM3 = 145,
  XMM4 = 146,
  XMM5 = 147,
  XMM6 = 148,
  XMM7 = 149,
  XMM8 = 150,
  XMM9 = 151,
  XMM10 = 152,
  XMM11 = 153,
  XMM12 = 154,
  XMM13 = 155,
  XMM14 = 156,
  XMM15 = 157,
  XMM16 = 158,
  XMM17 = 159,
  XMM18 = 160,
  XMM19 = 161,
  XMM20 = 162,
  XMM21 = 163,
  XMM22 = 164,
  XMM23 = 165,
  XMM24 = 166,
  XMM25 = 167,
  XMM26 = 168,
  XMM27 = 169,
  XMM28 = 170,
  XMM29 = 171,
  XMM30 = 172,
  XMM31 = 173,
  YMM0 = 174,
  YMM1 = 175,
  YMM2 = 176,
  YMM3 = 177,
  YMM4 = 178,
  YMM5 = 179,
  YMM6 = 180,
  YMM7 = 181,
  YMM8 = 182,
  YMM9 = 183,
  YMM10 = 184,
  YMM11 = 185,
  YMM12 = 186,
  YMM13 = 187,
  YMM14 = 188,
  YMM15 = 189,
  YMM16 = 190,
  YMM17 = 191,
  YMM18 = 192,
  YMM19 = 193,
  YMM20 = 194,
  YMM21 = 195,
  YMM22 = 196,
  YMM23 = 197,
  YMM24 = 198,
  YMM25 = 199,
  YMM26 = 200,
  YMM27 = 201,
  YMM28 = 202,
  YMM29 = 203,
  YMM30 = 204,
  YMM31 = 205,
  ZMM0 = 206,
  ZMM1 = 207,
  ZMM2 = 208,
  ZMM3 = 209,
  ZMM4 = 210,
  ZMM5 = 211,
  ZMM6 = 212,
  ZMM7 = 213,
  ZMM8 = 214,
  ZMM9 = 215,
  ZMM10 = 216,
  ZMM11 = 217,
  ZMM12 = 218,
  ZMM13 = 219,
  ZMM14 = 220,
  ZMM15 = 221,
  ZMM16 = 222,
  ZMM17 = 223,
  ZMM18 = 224,
  ZMM19 = 225,
  ZMM20 = 226,
  ZMM21 = 227,
  ZMM22 = 228,
  ZMM23 = 229,
  ZMM24 = 230,
  ZMM25 = 231,
  ZMM26 = 232,
  ZMM27 = 233,
  ZMM28 = 234,
  ZMM29 = 235,
  ZMM30 = 236,
  ZMM31 = 237,
  R8B = 238,
  R9B = 239,
  R10B = 240,
  R11B = 241,
  R12B = 242,
  R13B = 243,
  R14B = 244,
  R15B = 245,
  R8BH = 246,
  R9BH = 247,
  R10BH = 248,
  R11BH = 249,
  R12BH = 250,
  R13BH = 251,
  R14BH = 252,
  R15BH = 253,
  R8D = 254,
  R9D = 255,
  R10D = 256,
  R11D = 257,
  R12D = 258,
  R13D = 259,
  R14D = 260,
  R15D = 261,
  R8W = 262,
  R9W = 263,
  R10W = 264,
  R11W = 265,
  R12W = 266,
  R13W = 267,
  R14W = 268,
  R15W = 269,
  R8WH = 270,
  R9WH = 271,
  R10WH = 272,
  R11WH = 273,
  R12WH = 274,
  R13WH = 275,
  R14WH = 276,
  R15WH = 277,
  NUM_TARGET_REGS 	// 278
};
} // end namespace X86

// Register classes

namespace X86 {
enum {
  GR8RegClassID = 0,
  GRH8RegClassID = 1,
  GR8_NOREXRegClassID = 2,
  GR8_ABCD_HRegClassID = 3,
  GR8_ABCD_LRegClassID = 4,
  GRH16RegClassID = 5,
  GR16RegClassID = 6,
  GR16_NOREXRegClassID = 7,
  VK1RegClassID = 8,
  VK16RegClassID = 9,
  VK2RegClassID = 10,
  VK4RegClassID = 11,
  VK8RegClassID = 12,
  VK16WMRegClassID = 13,
  VK1WMRegClassID = 14,
  VK2WMRegClassID = 15,
  VK4WMRegClassID = 16,
  VK8WMRegClassID = 17,
  SEGMENT_REGRegClassID = 18,
  GR16_ABCDRegClassID = 19,
  FPCCRRegClassID = 20,
  FR32XRegClassID = 21,
  LOW32_ADDR_ACCESS_RBPRegClassID = 22,
  LOW32_ADDR_ACCESSRegClassID = 23,
  LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClassID = 24,
  DEBUG_REGRegClassID = 25,
  FR32RegClassID = 26,
  GR32RegClassID = 27,
  GR32_NOSPRegClassID = 28,
  LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXRegClassID = 29,
  GR32_NOREXRegClassID = 30,
  VK32RegClassID = 31,
  GR32_NOREX_NOSPRegClassID = 32,
  RFP32RegClassID = 33,
  VK32WMRegClassID = 34,
  GR32_ABCDRegClassID = 35,
  GR32_TCRegClassID = 36,
  GR32_ABCD_and_GR32_TCRegClassID = 37,
  GR32_ADRegClassID = 38,
  GR32_BPSPRegClassID = 39,
  GR32_BSIRegClassID = 40,
  GR32_CBRegClassID = 41,
  GR32_DCRegClassID = 42,
  GR32_DIBPRegClassID = 43,
  GR32_SIDIRegClassID = 44,
  LOW32_ADDR_ACCESS_RBP_with_sub_32bitRegClassID = 45,
  CCRRegClassID = 46,
  DFCCRRegClassID = 47,
  GR32_ABCD_and_GR32_BSIRegClassID = 48,
  GR32_AD_and_GR32_DCRegClassID = 49,
  GR32_BPSP_and_GR32_DIBPRegClassID = 50,
  GR32_BPSP_and_GR32_TCRegClassID = 51,
  GR32_BSI_and_GR32_SIDIRegClassID = 52,
  GR32_CB_and_GR32_DCRegClassID = 53,
  GR32_DIBP_and_GR32_SIDIRegClassID = 54,
  LOW32_ADDR_ACCESS_RBP_with_sub_8bit_with_sub_32bitRegClassID = 55,
  LOW32_ADDR_ACCESS_with_sub_32bitRegClassID = 56,
  RFP64RegClassID = 57,
  FR64XRegClassID = 58,
  GR64RegClassID = 59,
  CONTROL_REGRegClassID = 60,
  FR64RegClassID = 61,
  GR64_with_sub_8bitRegClassID = 62,
  GR64_NOSPRegClassID = 63,
  GR64_TCRegClassID = 64,
  GR64_NOREXRegClassID = 65,
  GR64_TCW64RegClassID = 66,
  GR64_TC_with_sub_8bitRegClassID = 67,
  GR64_NOSP_and_GR64_TCRegClassID = 68,
  GR64_TCW64_with_sub_8bitRegClassID = 69,
  GR64_TC_and_GR64_TCW64RegClassID = 70,
  GR64_with_sub_16bit_in_GR16_NOREXRegClassID = 71,
  VK64RegClassID = 72,
  VR64RegClassID = 73,
  GR64_NOREX_NOSPRegClassID = 74,
  GR64_NOREX_and_GR64_TCRegClassID = 75,
  GR64_NOSP_and_GR64_TCW64RegClassID = 76,
  GR64_TCW64_and_GR64_TC_with_sub_8bitRegClassID = 77,
  VK64WMRegClassID = 78,
  GR64_TC_and_GR64_NOSP_and_GR64_TCW64RegClassID = 79,
  GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREXRegClassID = 80,
  GR64_NOREX_NOSP_and_GR64_TCRegClassID = 81,
  GR64_NOREX_and_GR64_TCW64RegClassID = 82,
  GR64_ABCDRegClassID = 83,
  GR64_with_sub_32bit_in_GR32_TCRegClassID = 84,
  GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TCRegClassID = 85,
  GR64_ADRegClassID = 86,
  GR64_and_LOW32_ADDR_ACCESS_RBPRegClassID = 87,
  GR64_with_sub_32bit_in_GR32_BPSPRegClassID = 88,
  GR64_with_sub_32bit_in_GR32_BSIRegClassID = 89,
  GR64_with_sub_32bit_in_GR32_CBRegClassID = 90,
  GR64_with_sub_32bit_in_GR32_DCRegClassID = 91,
  GR64_with_sub_32bit_in_GR32_DIBPRegClassID = 92,
  GR64_with_sub_32bit_in_GR32_SIDIRegClassID = 93,
  GR64_and_LOW32_ADDR_ACCESSRegClassID = 94,
  GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_BSIRegClassID = 95,
  GR64_with_sub_32bit_in_GR32_AD_and_GR32_DCRegClassID = 96,
  GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_DIBPRegClassID = 97,
  GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_TCRegClassID = 98,
  GR64_with_sub_32bit_in_GR32_BSI_and_GR32_SIDIRegClassID = 99,
  GR64_with_sub_32bit_in_GR32_CB_and_GR32_DCRegClassID = 100,
  GR64_with_sub_32bit_in_GR32_DIBP_and_GR32_SIDIRegClassID = 101,
  RSTRegClassID = 102,
  RFP80RegClassID = 103,
  RFP80_7RegClassID = 104,
  VR128XRegClassID = 105,
  VR128RegClassID = 106,
  VR128HRegClassID = 107,
  VR128LRegClassID = 108,
  BNDRRegClassID = 109,
  VR256XRegClassID = 110,
  VR256RegClassID = 111,
  VR256HRegClassID = 112,
  VR256LRegClassID = 113,
  VR512RegClassID = 114,
  VR512_with_sub_xmm_in_FR32RegClassID = 115,
  VR512_with_sub_xmm_in_VR128HRegClassID = 116,
  VR512_with_sub_xmm_in_VR128LRegClassID = 117,

  };
} // end namespace X86


// Subregister indices

namespace X86 {
enum {
  NoSubRegister,
  sub_8bit,	// 1
  sub_8bit_hi,	// 2
  sub_8bit_hi_phony,	// 3
  sub_16bit,	// 4
  sub_16bit_hi,	// 5
  sub_32bit,	// 6
  sub_xmm,	// 7
  sub_ymm,	// 8
  NUM_TARGET_SUBREGS
};
} // end namespace X86

} // end namespace llvm

#endif // GET_REGINFO_ENUM

/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* MC Register Information                                                    *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/


#ifdef GET_REGINFO_MC_DESC
#undef GET_REGINFO_MC_DESC

namespace llvm {

extern const MCPhysReg X86RegDiffLists[] = {
  /* 0 */ 0, 1, 0,
  /* 3 */ 64873, 1, 1, 0,
  /* 7 */ 65257, 1, 1, 0,
  /* 11 */ 65394, 1, 1, 0,
  /* 15 */ 65466, 1, 1, 0,
  /* 19 */ 2, 1, 0,
  /* 22 */ 4, 1, 0,
  /* 25 */ 6, 1, 0,
  /* 28 */ 11, 1, 0,
  /* 31 */ 22, 1, 0,
  /* 34 */ 26, 1, 0,
  /* 37 */ 29, 1, 0,
  /* 40 */ 64849, 1, 0,
  /* 43 */ 10, 3, 0,
  /* 46 */ 4, 0,
  /* 48 */ 5, 0,
  /* 50 */ 65287, 1, 7, 0,
  /* 54 */ 65417, 1, 7, 0,
  /* 58 */ 10, 3, 7, 0,
  /* 62 */ 65512, 8, 0,
  /* 65 */ 65338, 1, 11, 0,
  /* 69 */ 65344, 1, 11, 0,
  /* 73 */ 65442, 1, 11, 0,
  /* 77 */ 65448, 1, 11, 0,
  /* 81 */ 12, 0,
  /* 83 */ 65338, 1, 14, 0,
  /* 87 */ 65344, 1, 14, 0,
  /* 91 */ 65442, 1, 14, 0,
  /* 95 */ 65448, 1, 14, 0,
  /* 99 */ 21, 0,
  /* 101 */ 22, 0,
  /* 103 */ 128, 8, 65512, 8, 24, 0,
  /* 109 */ 65534, 65508, 24, 0,
  /* 113 */ 65535, 65508, 24, 0,
  /* 117 */ 65534, 65510, 24, 0,
  /* 121 */ 65535, 65510, 24, 0,
  /* 125 */ 65523, 24, 0,
  /* 128 */ 65518, 25, 0,
  /* 131 */ 65521, 25, 0,
  /* 134 */ 65510, 65526, 2, 65535, 25, 0,
  /* 140 */ 2, 6, 26, 0,
  /* 144 */ 6, 6, 26, 0,
  /* 148 */ 65534, 10, 26, 0,
  /* 152 */ 65535, 10, 26, 0,
  /* 156 */ 2, 12, 26, 0,
  /* 160 */ 3, 12, 26, 0,
  /* 164 */ 4, 15, 26, 0,
  /* 168 */ 5, 15, 26, 0,
  /* 172 */ 65534, 17, 26, 0,
  /* 176 */ 65535, 17, 26, 0,
  /* 180 */ 1, 19, 26, 0,
  /* 184 */ 2, 19, 26, 0,
  /* 188 */ 65520, 26, 0,
  /* 191 */ 27, 0,
  /* 193 */ 65510, 65530, 65534, 65532, 28, 0,
  /* 199 */ 30, 0,
  /* 201 */ 65510, 65524, 65534, 65535, 31, 0,
  /* 207 */ 32, 32, 0,
  /* 210 */ 65510, 65519, 2, 65535, 32, 0,
  /* 216 */ 65510, 65521, 65532, 65535, 36, 0,
  /* 222 */ 65510, 65517, 65535, 65535, 37, 0,
  /* 228 */ 64827, 0,
  /* 230 */ 64898, 0,
  /* 232 */ 64921, 0,
  /* 234 */ 65061, 0,
  /* 236 */ 65520, 65408, 0,
  /* 239 */ 16, 65528, 65408, 0,
  /* 243 */ 24, 65528, 65408, 0,
  /* 247 */ 65427, 0,
  /* 249 */ 65429, 0,
  /* 251 */ 65461, 0,
  /* 253 */ 65493, 0,
  /* 255 */ 65504, 65504, 0,
  /* 258 */ 65509, 0,
  /* 260 */ 65511, 0,
  /* 262 */ 65513, 0,
  /* 264 */ 65512, 28, 2, 65535, 65520, 0,
  /* 270 */ 65512, 26, 2, 65535, 65522, 0,
  /* 276 */ 65525, 0,
  /* 278 */ 65530, 0,
  /* 280 */ 65531, 0,
  /* 282 */ 65534, 65532, 0,
  /* 285 */ 65511, 18, 65533, 0,
  /* 289 */ 65534, 0,
  /* 291 */ 2, 65535, 0,
  /* 294 */ 65532, 65535, 0,
  /* 297 */ 65534, 65535, 0,
  /* 300 */ 65535, 65535, 0,
};

extern const LaneBitmask X86LaneMaskLists[] = {
  /* 0 */ LaneBitmask(0x00000000), LaneBitmask::getAll(),
  /* 2 */ LaneBitmask(0x00000002), LaneBitmask(0x00000001), LaneBitmask::getAll(),
  /* 5 */ LaneBitmask(0x00000001), LaneBitmask(0x00000004), LaneBitmask::getAll(),
  /* 8 */ LaneBitmask(0x00000002), LaneBitmask(0x00000001), LaneBitmask(0x00000008), LaneBitmask::getAll(),
  /* 12 */ LaneBitmask(0x00000001), LaneBitmask(0x00000004), LaneBitmask(0x00000008), LaneBitmask::getAll(),
  /* 16 */ LaneBitmask(0x00000007), LaneBitmask(0x00000008), LaneBitmask::getAll(),
  /* 19 */ LaneBitmask(0x00000010), LaneBitmask::getAll(),
};

extern const uint16_t X86SubRegIdxLists[] = {
  /* 0 */ 1, 2, 0,
  /* 3 */ 1, 3, 0,
  /* 6 */ 6, 4, 1, 2, 5, 0,
  /* 12 */ 6, 4, 1, 3, 5, 0,
  /* 18 */ 6, 4, 5, 0,
  /* 22 */ 8, 7, 0,
};

extern const MCRegisterInfo::SubRegCoveredBits X86SubRegIdxRanges[] = {
  { 65535, 65535 },
  { 0, 8 },	// sub_8bit
  { 8, 8 },	// sub_8bit_hi
  { 8, 8 },	// sub_8bit_hi_phony
  { 0, 16 },	// sub_16bit
  { 16, 16 },	// sub_16bit_hi
  { 0, 32 },	// sub_32bit
  { 0, 128 },	// sub_xmm
  { 0, 256 },	// sub_ymm
};

extern const char X86RegStrings[] = {
  /* 0 */ 'X', 'M', 'M', '1', '0', 0,
  /* 6 */ 'Y', 'M', 'M', '1', '0', 0,
  /* 12 */ 'Z', 'M', 'M', '1', '0', 0,
  /* 18 */ 'C', 'R', '1', '0', 0,
  /* 23 */ 'D', 'R', '1', '0', 0,
  /* 28 */ 'X', 'M', 'M', '2', '0', 0,
  /* 34 */ 'Y', 'M', 'M', '2', '0', 0,
  /* 40 */ 'Z', 'M', 'M', '2', '0', 0,
  /* 46 */ 'X', 'M', 'M', '3', '0', 0,
  /* 52 */ 'Y', 'M', 'M', '3', '0', 0,
  /* 58 */ 'Z', 'M', 'M', '3', '0', 0,
  /* 64 */ 'B', 'N', 'D', '0', 0,
  /* 69 */ 'K', '0', 0,
  /* 72 */ 'X', 'M', 'M', '0', 0,
  /* 77 */ 'Y', 'M', 'M', '0', 0,
  /* 82 */ 'Z', 'M', 'M', '0', 0,
  /* 87 */ 'F', 'P', '0', 0,
  /* 91 */ 'C', 'R', '0', 0,
  /* 95 */ 'D', 'R', '0', 0,
  /* 99 */ 'S', 'T', '0', 0,
  /* 103 */ 'X', 'M', 'M', '1', '1', 0,
  /* 109 */ 'Y', 'M', 'M', '1', '1', 0,
  /* 115 */ 'Z', 'M', 'M', '1', '1', 0,
  /* 121 */ 'C', 'R', '1', '1', 0,
  /* 126 */ 'D', 'R', '1', '1', 0,
  /* 131 */ 'X', 'M', 'M', '2', '1', 0,
  /* 137 */ 'Y', 'M', 'M', '2', '1', 0,
  /* 143 */ 'Z', 'M', 'M', '2', '1', 0,
  /* 149 */ 'X', 'M', 'M', '3', '1', 0,
  /* 155 */ 'Y', 'M', 'M', '3', '1', 0,
  /* 161 */ 'Z', 'M', 'M', '3', '1', 0,
  /* 167 */ 'B', 'N', 'D', '1', 0,
  /* 172 */ 'K', '1', 0,
  /* 175 */ 'X', 'M', 'M', '1', 0,
  /* 180 */ 'Y', 'M', 'M', '1', 0,
  /* 185 */ 'Z', 'M', 'M', '1', 0,
  /* 190 */ 'F', 'P', '1', 0,
  /* 194 */ 'C', 'R', '1', 0,
  /* 198 */ 'D', 'R', '1', 0,
  /* 202 */ 'S', 'T', '1', 0,
  /* 206 */ 'X', 'M', 'M', '1', '2', 0,
  /* 212 */ 'Y', 'M', 'M', '1', '2', 0,
  /* 218 */ 'Z', 'M', 'M', '1', '2', 0,
  /* 224 */ 'C', 'R', '1', '2', 0,
  /* 229 */ 'D', 'R', '1', '2', 0,
  /* 234 */ 'X', 'M', 'M', '2', '2', 0,
  /* 240 */ 'Y', 'M', 'M', '2', '2', 0,
  /* 246 */ 'Z', 'M', 'M', '2', '2', 0,
  /* 252 */ 'B', 'N', 'D', '2', 0,
  /* 257 */ 'K', '2', 0,
  /* 260 */ 'X', 'M', 'M', '2', 0,
  /* 265 */ 'Y', 'M', 'M', '2', 0,
  /* 270 */ 'Z', 'M', 'M', '2', 0,
  /* 275 */ 'F', 'P', '2', 0,
  /* 279 */ 'C', 'R', '2', 0,
  /* 283 */ 'D', 'R', '2', 0,
  /* 287 */ 'S', 'T', '2', 0,
  /* 291 */ 'X', 'M', 'M', '1', '3', 0,
  /* 297 */ 'Y', 'M', 'M', '1', '3', 0,
  /* 303 */ 'Z', 'M', 'M', '1', '3', 0,
  /* 309 */ 'C', 'R', '1', '3', 0,
  /* 314 */ 'D', 'R', '1', '3', 0,
  /* 319 */ 'X', 'M', 'M', '2', '3', 0,
  /* 325 */ 'Y', 'M', 'M', '2', '3', 0,
  /* 331 */ 'Z', 'M', 'M', '2', '3', 0,
  /* 337 */ 'B', 'N', 'D', '3', 0,
  /* 342 */ 'K', '3', 0,
  /* 345 */ 'X', 'M', 'M', '3', 0,
  /* 350 */ 'Y', 'M', 'M', '3', 0,
  /* 355 */ 'Z', 'M', 'M', '3', 0,
  /* 360 */ 'F', 'P', '3', 0,
  /* 364 */ 'C', 'R', '3', 0,
  /* 368 */ 'D', 'R', '3', 0,
  /* 372 */ 'S', 'T', '3', 0,
  /* 376 */ 'X', 'M', 'M', '1', '4', 0,
  /* 382 */ 'Y', 'M', 'M', '1', '4', 0,
  /* 388 */ 'Z', 'M', 'M', '1', '4', 0,
  /* 394 */ 'C', 'R', '1', '4', 0,
  /* 399 */ 'D', 'R', '1', '4', 0,
  /* 404 */ 'X', 'M', 'M', '2', '4', 0,
  /* 410 */ 'Y', 'M', 'M', '2', '4', 0,
  /* 416 */ 'Z', 'M', 'M', '2', '4', 0,
  /* 422 */ 'K', '4', 0,
  /* 425 */ 'X', 'M', 'M', '4', 0,
  /* 430 */ 'Y', 'M', 'M', '4', 0,
  /* 435 */ 'Z', 'M', 'M', '4', 0,
  /* 440 */ 'F', 'P', '4', 0,
  /* 444 */ 'C', 'R', '4', 0,
  /* 448 */ 'D', 'R', '4', 0,
  /* 452 */ 'S', 'T', '4', 0,
  /* 456 */ 'X', 'M', 'M', '1', '5', 0,
  /* 462 */ 'Y', 'M', 'M', '1', '5', 0,
  /* 468 */ 'Z', 'M', 'M', '1', '5', 0,
  /* 474 */ 'C', 'R', '1', '5', 0,
  /* 479 */ 'D', 'R', '1', '5', 0,
  /* 484 */ 'X', 'M', 'M', '2', '5', 0,
  /* 490 */ 'Y', 'M', 'M', '2', '5', 0,
  /* 496 */ 'Z', 'M', 'M', '2', '5', 0,
  /* 502 */ 'K', '5', 0,
  /* 505 */ 'X', 'M', 'M', '5', 0,
  /* 510 */ 'Y', 'M', 'M', '5', 0,
  /* 515 */ 'Z', 'M', 'M', '5', 0,
  /* 520 */ 'F', 'P', '5', 0,
  /* 524 */ 'C', 'R', '5', 0,
  /* 528 */ 'D', 'R', '5', 0,
  /* 532 */ 'S', 'T', '5', 0,
  /* 536 */ 'X', 'M', 'M', '1', '6', 0,
  /* 542 */ 'Y', 'M', 'M', '1', '6', 0,
  /* 548 */ 'Z', 'M', 'M', '1', '6', 0,
  /* 554 */ 'X', 'M', 'M', '2', '6', 0,
  /* 560 */ 'Y', 'M', 'M', '2', '6', 0,
  /* 566 */ 'Z', 'M', 'M', '2', '6', 0,
  /* 572 */ 'K', '6', 0,
  /* 575 */ 'X', 'M', 'M', '6', 0,
  /* 580 */ 'Y', 'M', 'M', '6', 0,
  /* 585 */ 'Z', 'M', 'M', '6', 0,
  /* 590 */ 'F', 'P', '6', 0,
  /* 594 */ 'C', 'R', '6', 0,
  /* 598 */ 'D', 'R', '6', 0,
  /* 602 */ 'S', 'T', '6', 0,
  /* 606 */ 'X', 'M', 'M', '1', '7', 0,
  /* 612 */ 'Y', 'M', 'M', '1', '7', 0,
  /* 618 */ 'Z', 'M', 'M', '1', '7', 0,
  /* 624 */ 'X', 'M', 'M', '2', '7', 0,
  /* 630 */ 'Y', 'M', 'M', '2', '7', 0,
  /* 636 */ 'Z', 'M', 'M', '2', '7', 0,
  /* 642 */ 'K', '7', 0,
  /* 645 */ 'X', 'M', 'M', '7', 0,
  /* 650 */ 'Y', 'M', 'M', '7', 0,
  /* 655 */ 'Z', 'M', 'M', '7', 0,
  /* 660 */ 'F', 'P', '7', 0,
  /* 664 */ 'C', 'R', '7', 0,
  /* 668 */ 'D', 'R', '7', 0,
  /* 672 */ 'S', 'T', '7', 0,
  /* 676 */ 'X', 'M', 'M', '1', '8', 0,
  /* 682 */ 'Y', 'M', 'M', '1', '8', 0,
  /* 688 */ 'Z', 'M', 'M', '1', '8', 0,
  /* 694 */ 'X', 'M', 'M', '2', '8', 0,
  /* 700 */ 'Y', 'M', 'M', '2', '8', 0,
  /* 706 */ 'Z', 'M', 'M', '2', '8', 0,
  /* 712 */ 'X', 'M', 'M', '8', 0,
  /* 717 */ 'Y', 'M', 'M', '8', 0,
  /* 722 */ 'Z', 'M', 'M', '8', 0,
  /* 727 */ 'C', 'R', '8', 0,
  /* 731 */ 'D', 'R', '8', 0,
  /* 735 */ 'X', 'M', 'M', '1', '9', 0,
  /* 741 */ 'Y', 'M', 'M', '1', '9', 0,
  /* 747 */ 'Z', 'M', 'M', '1', '9', 0,
  /* 753 */ 'X', 'M', 'M', '2', '9', 0,
  /* 759 */ 'Y', 'M', 'M', '2', '9', 0,
  /* 765 */ 'Z', 'M', 'M', '2', '9', 0,
  /* 771 */ 'X', 'M', 'M', '9', 0,
  /* 776 */ 'Y', 'M', 'M', '9', 0,
  /* 781 */ 'Z', 'M', 'M', '9', 0,
  /* 786 */ 'C', 'R', '9', 0,
  /* 790 */ 'D', 'R', '9', 0,
  /* 794 */ 'R', '1', '0', 'B', 0,
  /* 799 */ 'R', '1', '1', 'B', 0,
  /* 804 */ 'R', '1', '2', 'B', 0,
  /* 809 */ 'R', '1', '3', 'B', 0,
  /* 814 */ 'R', '1', '4', 'B', 0,
  /* 819 */ 'R', '1', '5', 'B', 0,
  /* 824 */ 'R', '8', 'B', 0,
  /* 828 */ 'R', '9', 'B', 0,
  /* 832 */ 'R', '1', '0', 'D', 0,
  /* 837 */ 'R', '1', '1', 'D', 0,
  /* 842 */ 'R', '1', '2', 'D', 0,
  /* 847 */ 'R', '1', '3', 'D', 0,
  /* 852 */ 'R', '1', '4', 'D', 0,
  /* 857 */ 'R', '1', '5', 'D', 0,
  /* 862 */ 'R', '8', 'D', 0,
  /* 866 */ 'R', '9', 'D', 0,
  /* 870 */ 'D', 'F', 0,
  /* 873 */ 'A', 'H', 0,
  /* 876 */ 'R', '1', '0', 'B', 'H', 0,
  /* 882 */ 'R', '1', '1', 'B', 'H', 0,
  /* 888 */ 'R', '1', '2', 'B', 'H', 0,
  /* 894 */ 'R', '1', '3', 'B', 'H', 0,
  /* 900 */ 'R', '1', '4', 'B', 'H', 0,
  /* 906 */ 'R', '1', '5', 'B', 'H', 0,
  /* 912 */ 'R', '8', 'B', 'H', 0,
  /* 917 */ 'R', '9', 'B', 'H', 0,
  /* 922 */ 'C', 'H', 0,
  /* 925 */ 'D', 'H', 0,
  /* 928 */ 'D', 'I', 'H', 0,
  /* 932 */ 'S', 'I', 'H', 0,
  /* 936 */ 'B', 'P', 'H', 0,
  /* 940 */ 'S', 'P', 'H', 0,
  /* 944 */ 'R', '1', '0', 'W', 'H', 0,
  /* 950 */ 'R', '1', '1', 'W', 'H', 0,
  /* 956 */ 'R', '1', '2', 'W', 'H', 0,
  /* 962 */ 'R', '1', '3', 'W', 'H', 0,
  /* 968 */ 'R', '1', '4', 'W', 'H', 0,
  /* 974 */ 'R', '1', '5', 'W', 'H', 0,
  /* 980 */ 'R', '8', 'W', 'H', 0,
  /* 985 */ 'R', '9', 'W', 'H', 0,
  /* 990 */ 'E', 'D', 'I', 0,
  /* 994 */ 'H', 'D', 'I', 0,
  /* 998 */ 'R', 'D', 'I', 0,
  /* 1002 */ 'E', 'S', 'I', 0,
  /* 1006 */ 'H', 'S', 'I', 0,
  /* 1010 */ 'R', 'S', 'I', 0,
  /* 1014 */ 'A', 'L', 0,
  /* 1017 */ 'B', 'L', 0,
  /* 1020 */ 'C', 'L', 0,
  /* 1023 */ 'D', 'L', 0,
  /* 1026 */ 'D', 'I', 'L', 0,
  /* 1030 */ 'S', 'I', 'L', 0,
  /* 1034 */ 'B', 'P', 'L', 0,
  /* 1038 */ 'S', 'P', 'L', 0,
  /* 1042 */ 'E', 'B', 'P', 0,
  /* 1046 */ 'H', 'B', 'P', 0,
  /* 1050 */ 'R', 'B', 'P', 0,
  /* 1054 */ 'E', 'I', 'P', 0,
  /* 1058 */ 'H', 'I', 'P', 0,
  /* 1062 */ 'R', 'I', 'P', 0,
  /* 1066 */ 'E', 'S', 'P', 0,
  /* 1070 */ 'H', 'S', 'P', 0,
  /* 1074 */ 'R', 'S', 'P', 0,
  /* 1078 */ 'S', 'S', 'P', 0,
  /* 1082 */ 'C', 'S', 0,
  /* 1085 */ 'D', 'S', 0,
  /* 1088 */ 'E', 'S', 0,
  /* 1091 */ 'F', 'S', 0,
  /* 1094 */ 'E', 'F', 'L', 'A', 'G', 'S', 0,
  /* 1101 */ 'S', 'S', 0,
  /* 1104 */ 'R', '1', '0', 'W', 0,
  /* 1109 */ 'R', '1', '1', 'W', 0,
  /* 1114 */ 'R', '1', '2', 'W', 0,
  /* 1119 */ 'R', '1', '3', 'W', 0,
  /* 1124 */ 'R', '1', '4', 'W', 0,
  /* 1129 */ 'R', '1', '5', 'W', 0,
  /* 1134 */ 'R', '8', 'W', 0,
  /* 1138 */ 'R', '9', 'W', 0,
  /* 1142 */ 'F', 'P', 'C', 'W', 0,
  /* 1147 */ 'F', 'P', 'S', 'W', 0,
  /* 1152 */ 'E', 'A', 'X', 0,
  /* 1156 */ 'H', 'A', 'X', 0,
  /* 1160 */ 'R', 'A', 'X', 0,
  /* 1164 */ 'E', 'B', 'X', 0,
  /* 1168 */ 'H', 'B', 'X', 0,
  /* 1172 */ 'R', 'B', 'X', 0,
  /* 1176 */ 'E', 'C', 'X', 0,
  /* 1180 */ 'H', 'C', 'X', 0,
  /* 1184 */ 'R', 'C', 'X', 0,
  /* 1188 */ 'E', 'D', 'X', 0,
  /* 1192 */ 'H', 'D', 'X', 0,
  /* 1196 */ 'R', 'D', 'X', 0,
  /* 1200 */ 'E', 'I', 'Z', 0,
  /* 1204 */ 'R', 'I', 'Z', 0,
};

extern const MCRegisterDesc X86RegDesc[] = { // Descriptors
  { 5, 0, 0, 0, 0, 0 },
  { 873, 2, 184, 2, 4673, 0 },
  { 1014, 2, 180, 2, 4673, 0 },
  { 1153, 300, 181, 0, 0, 2 },
  { 879, 2, 168, 2, 4625, 0 },
  { 1017, 2, 164, 2, 4625, 0 },
  { 1043, 291, 173, 3, 352, 5 },
  { 936, 2, 176, 2, 768, 0 },
  { 1034, 2, 172, 2, 736, 0 },
  { 1165, 294, 165, 0, 304, 2 },
  { 922, 2, 160, 2, 4529, 0 },
  { 1020, 2, 156, 2, 4529, 0 },
  { 1082, 2, 2, 2, 4529, 0 },
  { 1177, 297, 157, 0, 400, 2 },
  { 870, 2, 2, 2, 4481, 0 },
  { 925, 2, 144, 2, 4481, 0 },
  { 991, 291, 149, 3, 448, 5 },
  { 928, 2, 152, 2, 1296, 0 },
  { 1026, 2, 148, 2, 4162, 0 },
  { 1023, 2, 140, 2, 4449, 0 },
  { 1085, 2, 2, 2, 4449, 0 },
  { 1189, 282, 141, 0, 688, 2 },
  { 1152, 223, 142, 7, 1524, 8 },
  { 1042, 211, 142, 13, 1236, 12 },
  { 1164, 217, 142, 7, 1460, 8 },
  { 1176, 202, 142, 7, 1172, 8 },
  { 990, 135, 142, 13, 869, 12 },
  { 1188, 194, 142, 7, 928, 8 },
  { 1094, 2, 2, 2, 1584, 0 },
  { 1054, 286, 129, 19, 496, 16 },
  { 1200, 2, 2, 2, 4449, 0 },
  { 1088, 2, 2, 2, 4449, 0 },
  { 1002, 271, 107, 13, 243, 12 },
  { 1066, 265, 107, 13, 243, 12 },
  { 1142, 2, 2, 2, 4625, 0 },
  { 1147, 2, 2, 2, 4625, 0 },
  { 1091, 2, 2, 2, 4625, 0 },
  { 1098, 2, 2, 2, 4625, 0 },
  { 1156, 2, 188, 2, 4193, 0 },
  { 1046, 2, 188, 2, 4193, 0 },
  { 1168, 2, 188, 2, 4193, 0 },
  { 1180, 2, 188, 2, 4193, 0 },
  { 994, 2, 188, 2, 4193, 0 },
  { 1192, 2, 188, 2, 4193, 0 },
  { 1058, 2, 131, 2, 3955, 0 },
  { 1006, 2, 125, 2, 3987, 0 },
  { 1070, 2, 125, 2, 3987, 0 },
  { 1055, 2, 128, 2, 1616, 0 },
  { 1160, 222, 2, 6, 1396, 8 },
  { 1050, 210, 2, 12, 1108, 12 },
  { 1172, 216, 2, 6, 1332, 8 },
  { 1184, 201, 2, 6, 1044, 8 },
  { 998, 134, 2, 12, 805, 12 },
  { 1196, 193, 2, 6, 928, 8 },
  { 1062, 285, 2, 18, 496, 16 },
  { 1204, 2, 2, 2, 3520, 0 },
  { 1010, 270, 2, 12, 179, 12 },
  { 1074, 264, 2, 12, 179, 12 },
  { 1003, 291, 118, 3, 544, 5 },
  { 932, 2, 121, 2, 3056, 0 },
  { 1030, 2, 117, 2, 2272, 0 },
  { 1067, 291, 110, 3, 592, 5 },
  { 940, 2, 113, 2, 3184, 0 },
  { 1038, 2, 109, 2, 3752, 0 },
  { 1101, 2, 2, 2, 4129, 0 },
  { 1078, 2, 2, 2, 4129, 0 },
  { 64, 2, 2, 2, 4129, 0 },
  { 167, 2, 2, 2, 4129, 0 },
  { 252, 2, 2, 2, 4129, 0 },
  { 337, 2, 2, 2, 4129, 0 },
  { 91, 2, 2, 2, 4129, 0 },
  { 194, 2, 2, 2, 4129, 0 },
  { 279, 2, 2, 2, 4129, 0 },
  { 364, 2, 2, 2, 4129, 0 },
  { 444, 2, 2, 2, 4129, 0 },
  { 524, 2, 2, 2, 4129, 0 },
  { 594, 2, 2, 2, 4129, 0 },
  { 664, 2, 2, 2, 4129, 0 },
  { 727, 2, 2, 2, 4129, 0 },
  { 786, 2, 2, 2, 4129, 0 },
  { 18, 2, 2, 2, 4129, 0 },
  { 121, 2, 2, 2, 4129, 0 },
  { 224, 2, 2, 2, 4129, 0 },
  { 309, 2, 2, 2, 4129, 0 },
  { 394, 2, 2, 2, 4129, 0 },
  { 474, 2, 2, 2, 4129, 0 },
  { 95, 2, 2, 2, 4129, 0 },
  { 198, 2, 2, 2, 4129, 0 },
  { 283, 2, 2, 2, 4129, 0 },
  { 368, 2, 2, 2, 4129, 0 },
  { 448, 2, 2, 2, 4129, 0 },
  { 528, 2, 2, 2, 4129, 0 },
  { 598, 2, 2, 2, 4129, 0 },
  { 668, 2, 2, 2, 4129, 0 },
  { 731, 2, 2, 2, 4129, 0 },
  { 790, 2, 2, 2, 4129, 0 },
  { 23, 2, 2, 2, 4129, 0 },
  { 126, 2, 2, 2, 4129, 0 },
  { 229, 2, 2, 2, 4129, 0 },
  { 314, 2, 2, 2, 4129, 0 },
  { 399, 2, 2, 2, 4129, 0 },
  { 479, 2, 2, 2, 4129, 0 },
  { 87, 2, 2, 2, 4129, 0 },
  { 190, 2, 2, 2, 4129, 0 },
  { 275, 2, 2, 2, 4129, 0 },
  { 360, 2, 2, 2, 4129, 0 },
  { 440, 2, 2, 2, 4129, 0 },
  { 520, 2, 2, 2, 4129, 0 },
  { 590, 2, 2, 2, 4129, 0 },
  { 660, 2, 2, 2, 4129, 0 },
  { 69, 2, 2, 2, 4129, 0 },
  { 172, 2, 2, 2, 4129, 0 },
  { 257, 2, 2, 2, 4129, 0 },
  { 342, 2, 2, 2, 4129, 0 },
  { 422, 2, 2, 2, 4129, 0 },
  { 502, 2, 2, 2, 4129, 0 },
  { 572, 2, 2, 2, 4129, 0 },
  { 642, 2, 2, 2, 4129, 0 },
  { 73, 2, 2, 2, 4129, 0 },
  { 176, 2, 2, 2, 4129, 0 },
  { 261, 2, 2, 2, 4129, 0 },
  { 346, 2, 2, 2, 4129, 0 },
  { 426, 2, 2, 2, 4129, 0 },
  { 506, 2, 2, 2, 4129, 0 },
  { 576, 2, 2, 2, 4129, 0 },
  { 646, 2, 2, 2, 4129, 0 },
  { 728, 103, 2, 12, 115, 12 },
  { 787, 103, 2, 12, 115, 12 },
  { 19, 103, 2, 12, 115, 12 },
  { 122, 103, 2, 12, 115, 12 },
  { 225, 103, 2, 12, 115, 12 },
  { 310, 103, 2, 12, 115, 12 },
  { 395, 103, 2, 12, 115, 12 },
  { 475, 103, 2, 12, 115, 12 },
  { 99, 2, 2, 2, 4417, 0 },
  { 202, 2, 2, 2, 4417, 0 },
  { 287, 2, 2, 2, 4417, 0 },
  { 372, 2, 2, 2, 4417, 0 },
  { 452, 2, 2, 2, 4417, 0 },
  { 532, 2, 2, 2, 4417, 0 },
  { 602, 2, 2, 2, 4417, 0 },
  { 672, 2, 2, 2, 4417, 0 },
  { 72, 2, 207, 2, 4417, 0 },
  { 175, 2, 207, 2, 4417, 0 },
  { 260, 2, 207, 2, 4417, 0 },
  { 345, 2, 207, 2, 4417, 0 },
  { 425, 2, 207, 2, 4417, 0 },
  { 505, 2, 207, 2, 4417, 0 },
  { 575, 2, 207, 2, 4417, 0 },
  { 645, 2, 207, 2, 4417, 0 },
  { 712, 2, 207, 2, 4417, 0 },
  { 771, 2, 207, 2, 4417, 0 },
  { 0, 2, 207, 2, 4417, 0 },
  { 103, 2, 207, 2, 4417, 0 },
  { 206, 2, 207, 2, 4417, 0 },
  { 291, 2, 207, 2, 4417, 0 },
  { 376, 2, 207, 2, 4417, 0 },
  { 456, 2, 207, 2, 4417, 0 },
  { 536, 2, 207, 2, 4417, 0 },
  { 606, 2, 207, 2, 4417, 0 },
  { 676, 2, 207, 2, 4417, 0 },
  { 735, 2, 207, 2, 4417, 0 },
  { 28, 2, 207, 2, 4417, 0 },
  { 131, 2, 207, 2, 4417, 0 },
  { 234, 2, 207, 2, 4417, 0 },
  { 319, 2, 207, 2, 4417, 0 },
  { 404, 2, 207, 2, 4417, 0 },
  { 484, 2, 207, 2, 4417, 0 },
  { 554, 2, 207, 2, 4417, 0 },
  { 624, 2, 207, 2, 4417, 0 },
  { 694, 2, 207, 2, 4417, 0 },
  { 753, 2, 207, 2, 4417, 0 },
  { 46, 2, 207, 2, 4417, 0 },
  { 149, 2, 207, 2, 4417, 0 },
  { 77, 256, 208, 23, 4049, 19 },
  { 180, 256, 208, 23, 4049, 19 },
  { 265, 256, 208, 23, 4049, 19 },
  { 350, 256, 208, 23, 4049, 19 },
  { 430, 256, 208, 23, 4049, 19 },
  { 510, 256, 208, 23, 4049, 19 },
  { 580, 256, 208, 23, 4049, 19 },
  { 650, 256, 208, 23, 4049, 19 },
  { 717, 256, 208, 23, 4049, 19 },
  { 776, 256, 208, 23, 4049, 19 },
  { 6, 256, 208, 23, 4049, 19 },
  { 109, 256, 208, 23, 4049, 19 },
  { 212, 256, 208, 23, 4049, 19 },
  { 297, 256, 208, 23, 4049, 19 },
  { 382, 256, 208, 23, 4049, 19 },
  { 462, 256, 208, 23, 4049, 19 },
  { 542, 256, 208, 23, 4049, 19 },
  { 612, 256, 208, 23, 4049, 19 },
  { 682, 256, 208, 23, 4049, 19 },
  { 741, 256, 208, 23, 4049, 19 },
  { 34, 256, 208, 23, 4049, 19 },
  { 137, 256, 208, 23, 4049, 19 },
  { 240, 256, 208, 23, 4049, 19 },
  { 325, 256, 208, 23, 4049, 19 },
  { 410, 256, 208, 23, 4049, 19 },
  { 490, 256, 208, 23, 4049, 19 },
  { 560, 256, 208, 23, 4049, 19 },
  { 630, 256, 208, 23, 4049, 19 },
  { 700, 256, 208, 23, 4049, 19 },
  { 759, 256, 208, 23, 4049, 19 },
  { 52, 256, 208, 23, 4049, 19 },
  { 155, 256, 208, 23, 4049, 19 },
  { 82, 255, 2, 22, 4017, 19 },
  { 185, 255, 2, 22, 4017, 19 },
  { 270, 255, 2, 22, 4017, 19 },
  { 355, 255, 2, 22, 4017, 19 },
  { 435, 255, 2, 22, 4017, 19 },
  { 515, 255, 2, 22, 4017, 19 },
  { 585, 255, 2, 22, 4017, 19 },
  { 655, 255, 2, 22, 4017, 19 },
  { 722, 255, 2, 22, 4017, 19 },
  { 781, 255, 2, 22, 4017, 19 },
  { 12, 255, 2, 22, 4017, 19 },
  { 115, 255, 2, 22, 4017, 19 },
  { 218, 255, 2, 22, 4017, 19 },
  { 303, 255, 2, 22, 4017, 19 },
  { 388, 255, 2, 22, 4017, 19 },
  { 468, 255, 2, 22, 4017, 19 },
  { 548, 255, 2, 22, 4017, 19 },
  { 618, 255, 2, 22, 4017, 19 },
  { 688, 255, 2, 22, 4017, 19 },
  { 747, 255, 2, 22, 4017, 19 },
  { 40, 255, 2, 22, 4017, 19 },
  { 143, 255, 2, 22, 4017, 19 },
  { 246, 255, 2, 22, 4017, 19 },
  { 331, 255, 2, 22, 4017, 19 },
  { 416, 255, 2, 22, 4017, 19 },
  { 496, 255, 2, 22, 4017, 19 },
  { 566, 255, 2, 22, 4017, 19 },
  { 636, 255, 2, 22, 4017, 19 },
  { 706, 255, 2, 22, 4017, 19 },
  { 765, 255, 2, 22, 4017, 19 },
  { 58, 255, 2, 22, 4017, 19 },
  { 161, 255, 2, 22, 4017, 19 },
  { 824, 2, 243, 2, 3715, 0 },
  { 828, 2, 243, 2, 3715, 0 },
  { 794, 2, 243, 2, 3715, 0 },
  { 799, 2, 243, 2, 3715, 0 },
  { 804, 2, 243, 2, 3715, 0 },
  { 809, 2, 243, 2, 3715, 0 },
  { 814, 2, 243, 2, 3715, 0 },
  { 819, 2, 243, 2, 3715, 0 },
  { 912, 2, 239, 2, 3683, 0 },
  { 917, 2, 239, 2, 3683, 0 },
  { 876, 2, 239, 2, 3683, 0 },
  { 882, 2, 239, 2, 3683, 0 },
  { 888, 2, 239, 2, 3683, 0 },
  { 894, 2, 239, 2, 3683, 0 },
  { 900, 2, 239, 2, 3683, 0 },
  { 906, 2, 239, 2, 3683, 0 },
  { 862, 104, 237, 13, 51, 12 },
  { 866, 104, 237, 13, 51, 12 },
  { 832, 104, 237, 13, 51, 12 },
  { 837, 104, 237, 13, 51, 12 },
  { 842, 104, 237, 13, 51, 12 },
  { 847, 104, 237, 13, 51, 12 },
  { 852, 104, 237, 13, 51, 12 },
  { 857, 104, 237, 13, 51, 12 },
  { 1134, 62, 240, 3, 643, 5 },
  { 1138, 62, 240, 3, 643, 5 },
  { 1104, 62, 240, 3, 643, 5 },
  { 1109, 62, 240, 3, 643, 5 },
  { 1114, 62, 240, 3, 643, 5 },
  { 1119, 62, 240, 3, 643, 5 },
  { 1124, 62, 240, 3, 643, 5 },
  { 1129, 62, 240, 3, 643, 5 },
  { 980, 2, 236, 2, 3651, 0 },
  { 985, 2, 236, 2, 3651, 0 },
  { 944, 2, 236, 2, 3651, 0 },
  { 950, 2, 236, 2, 3651, 0 },
  { 956, 2, 236, 2, 3651, 0 },
  { 962, 2, 236, 2, 3651, 0 },
  { 968, 2, 236, 2, 3651, 0 },
  { 974, 2, 236, 2, 3651, 0 },
};

extern const MCPhysReg X86RegUnitRoots[][2] = {
  { X86::AH },
  { X86::AL },
  { X86::BH },
  { X86::BL },
  { X86::BPL },
  { X86::BPH },
  { X86::CH },
  { X86::CL },
  { X86::CS },
  { X86::DF },
  { X86::DH },
  { X86::DIL },
  { X86::DIH },
  { X86::DL },
  { X86::DS },
  { X86::HAX },
  { X86::HBP },
  { X86::HBX },
  { X86::HCX },
  { X86::HDI },
  { X86::HDX },
  { X86::EFLAGS },
  { X86::IP },
  { X86::HIP },
  { X86::EIZ },
  { X86::ES },
  { X86::SIL },
  { X86::SIH },
  { X86::HSI },
  { X86::SPL },
  { X86::SPH },
  { X86::HSP },
  { X86::FPCW },
  { X86::FPSW },
  { X86::FS },
  { X86::GS },
  { X86::RIZ },
  { X86::SS },
  { X86::SSP },
  { X86::BND0 },
  { X86::BND1 },
  { X86::BND2 },
  { X86::BND3 },
  { X86::CR0 },
  { X86::CR1 },
  { X86::CR2 },
  { X86::CR3 },
  { X86::CR4 },
  { X86::CR5 },
  { X86::CR6 },
  { X86::CR7 },
  { X86::CR8 },
  { X86::CR9 },
  { X86::CR10 },
  { X86::CR11 },
  { X86::CR12 },
  { X86::CR13 },
  { X86::CR14 },
  { X86::CR15 },
  { X86::DR0 },
  { X86::DR1 },
  { X86::DR2 },
  { X86::DR3 },
  { X86::DR4 },
  { X86::DR5 },
  { X86::DR6 },
  { X86::DR7 },
  { X86::DR8 },
  { X86::DR9 },
  { X86::DR10 },
  { X86::DR11 },
  { X86::DR12 },
  { X86::DR13 },
  { X86::DR14 },
  { X86::DR15 },
  { X86::FP0 },
  { X86::FP1 },
  { X86::FP2 },
  { X86::FP3 },
  { X86::FP4 },
  { X86::FP5 },
  { X86::FP6 },
  { X86::FP7 },
  { X86::K0 },
  { X86::K1 },
  { X86::K2 },
  { X86::K3 },
  { X86::K4 },
  { X86::K5 },
  { X86::K6 },
  { X86::K7 },
  { X86::MM0 },
  { X86::MM1 },
  { X86::MM2 },
  { X86::MM3 },
  { X86::MM4 },
  { X86::MM5 },
  { X86::MM6 },
  { X86::MM7 },
  { X86::R8B },
  { X86::R8BH },
  { X86::R8WH },
  { X86::R9B },
  { X86::R9BH },
  { X86::R9WH },
  { X86::R10B },
  { X86::R10BH },
  { X86::R10WH },
  { X86::R11B },
  { X86::R11BH },
  { X86::R11WH },
  { X86::R12B },
  { X86::R12BH },
  { X86::R12WH },
  { X86::R13B },
  { X86::R13BH },
  { X86::R13WH },
  { X86::R14B },
  { X86::R14BH },
  { X86::R14WH },
  { X86::R15B },
  { X86::R15BH },
  { X86::R15WH },
  { X86::ST0 },
  { X86::ST1 },
  { X86::ST2 },
  { X86::ST3 },
  { X86::ST4 },
  { X86::ST5 },
  { X86::ST6 },
  { X86::ST7 },
  { X86::XMM0 },
  { X86::XMM1 },
  { X86::XMM2 },
  { X86::XMM3 },
  { X86::XMM4 },
  { X86::XMM5 },
  { X86::XMM6 },
  { X86::XMM7 },
  { X86::XMM8 },
  { X86::XMM9 },
  { X86::XMM10 },
  { X86::XMM11 },
  { X86::XMM12 },
  { X86::XMM13 },
  { X86::XMM14 },
  { X86::XMM15 },
  { X86::XMM16 },
  { X86::XMM17 },
  { X86::XMM18 },
  { X86::XMM19 },
  { X86::XMM20 },
  { X86::XMM21 },
  { X86::XMM22 },
  { X86::XMM23 },
  { X86::XMM24 },
  { X86::XMM25 },
  { X86::XMM26 },
  { X86::XMM27 },
  { X86::XMM28 },
  { X86::XMM29 },
  { X86::XMM30 },
  { X86::XMM31 },
};

namespace {     // Register classes...
  // GR8 Register Class...
  const MCPhysReg GR8[] = {
    X86::AL, X86::CL, X86::DL, X86::AH, X86::CH, X86::DH, X86::BL, X86::BH, X86::SIL, X86::DIL, X86::BPL, X86::SPL, X86::R8B, X86::R9B, X86::R10B, X86::R11B, X86::R14B, X86::R15B, X86::R12B, X86::R13B, 
  };

  // GR8 Bit set.
  const uint8_t GR8Bits[] = {
    0x36, 0x8d, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 
  };

  // GRH8 Register Class...
  const MCPhysReg GRH8[] = {
    X86::SIH, X86::DIH, X86::BPH, X86::SPH, X86::R8BH, X86::R9BH, X86::R10BH, X86::R11BH, X86::R12BH, X86::R13BH, X86::R14BH, X86::R15BH, 
  };

  // GRH8 Bit set.
  const uint8_t GRH8Bits[] = {
    0x80, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x48, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 
  };

  // GR8_NOREX Register Class...
  const MCPhysReg GR8_NOREX[] = {
    X86::AL, X86::CL, X86::DL, X86::AH, X86::CH, X86::DH, X86::BL, X86::BH, 
  };

  // GR8_NOREX Bit set.
  const uint8_t GR8_NOREXBits[] = {
    0x36, 0x8c, 0x08, 
  };

  // GR8_ABCD_H Register Class...
  const MCPhysReg GR8_ABCD_H[] = {
    X86::AH, X86::CH, X86::DH, X86::BH, 
  };

  // GR8_ABCD_H Bit set.
  const uint8_t GR8_ABCD_HBits[] = {
    0x12, 0x84, 
  };

  // GR8_ABCD_L Register Class...
  const MCPhysReg GR8_ABCD_L[] = {
    X86::AL, X86::CL, X86::DL, X86::BL, 
  };

  // GR8_ABCD_L Bit set.
  const uint8_t GR8_ABCD_LBits[] = {
    0x24, 0x08, 0x08, 
  };

  // GRH16 Register Class...
  const MCPhysReg GRH16[] = {
    X86::HAX, X86::HCX, X86::HDX, X86::HSI, X86::HDI, X86::HBX, X86::HBP, X86::HSP, X86::HIP, X86::R8WH, X86::R9WH, X86::R10WH, X86::R11WH, X86::R12WH, X86::R13WH, X86::R14WH, X86::R15WH, 
  };

  // GRH16 Bit set.
  const uint8_t GRH16Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0xc0, 0x7f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 
  };

  // GR16 Register Class...
  const MCPhysReg GR16[] = {
    X86::AX, X86::CX, X86::DX, X86::SI, X86::DI, X86::BX, X86::BP, X86::SP, X86::R8W, X86::R9W, X86::R10W, X86::R11W, X86::R14W, X86::R15W, X86::R12W, X86::R13W, 
  };

  // GR16 Bit set.
  const uint8_t GR16Bits[] = {
    0x48, 0x22, 0x21, 0x00, 0x00, 0x00, 0x00, 0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 
  };

  // GR16_NOREX Register Class...
  const MCPhysReg GR16_NOREX[] = {
    X86::AX, X86::CX, X86::DX, X86::SI, X86::DI, X86::BX, X86::BP, X86::SP, 
  };

  // GR16_NOREX Bit set.
  const uint8_t GR16_NOREXBits[] = {
    0x48, 0x22, 0x21, 0x00, 0x00, 0x00, 0x00, 0x24, 
  };

  // VK1 Register Class...
  const MCPhysReg VK1[] = {
    X86::K0, X86::K1, X86::K2, X86::K3, X86::K4, X86::K5, X86::K6, X86::K7, 
  };

  // VK1 Bit set.
  const uint8_t VK1Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 
  };

  // VK16 Register Class...
  const MCPhysReg VK16[] = {
    X86::K0, X86::K1, X86::K2, X86::K3, X86::K4, X86::K5, X86::K6, X86::K7, 
  };

  // VK16 Bit set.
  const uint8_t VK16Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 
  };

  // VK2 Register Class...
  const MCPhysReg VK2[] = {
    X86::K0, X86::K1, X86::K2, X86::K3, X86::K4, X86::K5, X86::K6, X86::K7, 
  };

  // VK2 Bit set.
  const uint8_t VK2Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 
  };

  // VK4 Register Class...
  const MCPhysReg VK4[] = {
    X86::K0, X86::K1, X86::K2, X86::K3, X86::K4, X86::K5, X86::K6, X86::K7, 
  };

  // VK4 Bit set.
  const uint8_t VK4Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 
  };

  // VK8 Register Class...
  const MCPhysReg VK8[] = {
    X86::K0, X86::K1, X86::K2, X86::K3, X86::K4, X86::K5, X86::K6, X86::K7, 
  };

  // VK8 Bit set.
  const uint8_t VK8Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 
  };

  // VK16WM Register Class...
  const MCPhysReg VK16WM[] = {
    X86::K1, X86::K2, X86::K3, X86::K4, X86::K5, X86::K6, X86::K7, 
  };

  // VK16WM Bit set.
  const uint8_t VK16WMBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x3f, 
  };

  // VK1WM Register Class...
  const MCPhysReg VK1WM[] = {
    X86::K1, X86::K2, X86::K3, X86::K4, X86::K5, X86::K6, X86::K7, 
  };

  // VK1WM Bit set.
  const uint8_t VK1WMBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x3f, 
  };

  // VK2WM Register Class...
  const MCPhysReg VK2WM[] = {
    X86::K1, X86::K2, X86::K3, X86::K4, X86::K5, X86::K6, X86::K7, 
  };

  // VK2WM Bit set.
  const uint8_t VK2WMBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x3f, 
  };

  // VK4WM Register Class...
  const MCPhysReg VK4WM[] = {
    X86::K1, X86::K2, X86::K3, X86::K4, X86::K5, X86::K6, X86::K7, 
  };

  // VK4WM Bit set.
  const uint8_t VK4WMBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x3f, 
  };

  // VK8WM Register Class...
  const MCPhysReg VK8WM[] = {
    X86::K1, X86::K2, X86::K3, X86::K4, X86::K5, X86::K6, X86::K7, 
  };

  // VK8WM Bit set.
  const uint8_t VK8WMBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x3f, 
  };

  // SEGMENT_REG Register Class...
  const MCPhysReg SEGMENT_REG[] = {
    X86::CS, X86::DS, X86::SS, X86::ES, X86::FS, X86::GS, 
  };

  // SEGMENT_REG Bit set.
  const uint8_t SEGMENT_REGBits[] = {
    0x00, 0x10, 0x10, 0x80, 0x30, 0x00, 0x00, 0x00, 0x01, 
  };

  // GR16_ABCD Register Class...
  const MCPhysReg GR16_ABCD[] = {
    X86::AX, X86::CX, X86::DX, X86::BX, 
  };

  // GR16_ABCD Bit set.
  const uint8_t GR16_ABCDBits[] = {
    0x08, 0x22, 0x20, 
  };

  // FPCCR Register Class...
  const MCPhysReg FPCCR[] = {
    X86::FPSW, 
  };

  // FPCCR Bit set.
  const uint8_t FPCCRBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x08, 
  };

  // FR32X Register Class...
  const MCPhysReg FR32X[] = {
    X86::XMM0, X86::XMM1, X86::XMM2, X86::XMM3, X86::XMM4, X86::XMM5, X86::XMM6, X86::XMM7, X86::XMM8, X86::XMM9, X86::XMM10, X86::XMM11, X86::XMM12, X86::XMM13, X86::XMM14, X86::XMM15, X86::XMM16, X86::XMM17, X86::XMM18, X86::XMM19, X86::XMM20, X86::XMM21, X86::XMM22, X86::XMM23, X86::XMM24, X86::XMM25, X86::XMM26, X86::XMM27, X86::XMM28, X86::XMM29, X86::XMM30, X86::XMM31, 
  };

  // FR32X Bit set.
  const uint8_t FR32XBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0xff, 0xff, 0xff, 0x3f, 
  };

  // LOW32_ADDR_ACCESS_RBP Register Class...
  const MCPhysReg LOW32_ADDR_ACCESS_RBP[] = {
    X86::EAX, X86::ECX, X86::EDX, X86::ESI, X86::EDI, X86::EBX, X86::EBP, X86::ESP, X86::R8D, X86::R9D, X86::R10D, X86::R11D, X86::R14D, X86::R15D, X86::R12D, X86::R13D, X86::RIP, X86::RBP, 
  };

  // LOW32_ADDR_ACCESS_RBP Bit set.
  const uint8_t LOW32_ADDR_ACCESS_RBPBits[] = {
    0x00, 0x00, 0xc0, 0x0f, 0x03, 0x00, 0x42, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 
  };

  // LOW32_ADDR_ACCESS Register Class...
  const MCPhysReg LOW32_ADDR_ACCESS[] = {
    X86::EAX, X86::ECX, X86::EDX, X86::ESI, X86::EDI, X86::EBX, X86::EBP, X86::ESP, X86::R8D, X86::R9D, X86::R10D, X86::R11D, X86::R14D, X86::R15D, X86::R12D, X86::R13D, X86::RIP, 
  };

  // LOW32_ADDR_ACCESS Bit set.
  const uint8_t LOW32_ADDR_ACCESSBits[] = {
    0x00, 0x00, 0xc0, 0x0f, 0x03, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 
  };

  // LOW32_ADDR_ACCESS_RBP_with_sub_8bit Register Class...
  const MCPhysReg LOW32_ADDR_ACCESS_RBP_with_sub_8bit[] = {
    X86::EAX, X86::ECX, X86::EDX, X86::ESI, X86::EDI, X86::EBX, X86::EBP, X86::ESP, X86::R8D, X86::R9D, X86::R10D, X86::R11D, X86::R14D, X86::R15D, X86::R12D, X86::R13D, X86::RBP, 
  };

  // LOW32_ADDR_ACCESS_RBP_with_sub_8bit Bit set.
  const uint8_t LOW32_ADDR_ACCESS_RBP_with_sub_8bitBits[] = {
    0x00, 0x00, 0xc0, 0x0f, 0x03, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 
  };

  // DEBUG_REG Register Class...
  const MCPhysReg DEBUG_REG[] = {
    X86::DR0, X86::DR1, X86::DR2, X86::DR3, X86::DR4, X86::DR5, X86::DR6, X86::DR7, X86::DR8, X86::DR9, X86::DR10, X86::DR11, X86::DR12, X86::DR13, X86::DR14, X86::DR15, 
  };

  // DEBUG_REG Bit set.
  const uint8_t DEBUG_REGBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0xff, 0x3f, 
  };

  // FR32 Register Class...
  const MCPhysReg FR32[] = {
    X86::XMM0, X86::XMM1, X86::XMM2, X86::XMM3, X86::XMM4, X86::XMM5, X86::XMM6, X86::XMM7, X86::XMM8, X86::XMM9, X86::XMM10, X86::XMM11, X86::XMM12, X86::XMM13, X86::XMM14, X86::XMM15, 
  };

  // FR32 Bit set.
  const uint8_t FR32Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0xff, 0x3f, 
  };

  // GR32 Register Class...
  const MCPhysReg GR32[] = {
    X86::EAX, X86::ECX, X86::EDX, X86::ESI, X86::EDI, X86::EBX, X86::EBP, X86::ESP, X86::R8D, X86::R9D, X86::R10D, X86::R11D, X86::R14D, X86::R15D, X86::R12D, X86::R13D, 
  };

  // GR32 Bit set.
  const uint8_t GR32Bits[] = {
    0x00, 0x00, 0xc0, 0x0f, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 
  };

  // GR32_NOSP Register Class...
  const MCPhysReg GR32_NOSP[] = {
    X86::EAX, X86::ECX, X86::EDX, X86::ESI, X86::EDI, X86::EBX, X86::EBP, X86::R8D, X86::R9D, X86::R10D, X86::R11D, X86::R14D, X86::R15D, X86::R12D, X86::R13D, 
  };

  // GR32_NOSP Bit set.
  const uint8_t GR32_NOSPBits[] = {
    0x00, 0x00, 0xc0, 0x0f, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 
  };

  // LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREX Register Class...
  const MCPhysReg LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREX[] = {
    X86::EAX, X86::ECX, X86::EDX, X86::ESI, X86::EDI, X86::EBX, X86::EBP, X86::ESP, X86::RBP, 
  };

  // LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREX Bit set.
  const uint8_t LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXBits[] = {
    0x00, 0x00, 0xc0, 0x0f, 0x03, 0x00, 0x02, 
  };

  // GR32_NOREX Register Class...
  const MCPhysReg GR32_NOREX[] = {
    X86::EAX, X86::ECX, X86::EDX, X86::ESI, X86::EDI, X86::EBX, X86::EBP, X86::ESP, 
  };

  // GR32_NOREX Bit set.
  const uint8_t GR32_NOREXBits[] = {
    0x00, 0x00, 0xc0, 0x0f, 0x03, 
  };

  // VK32 Register Class...
  const MCPhysReg VK32[] = {
    X86::K0, X86::K1, X86::K2, X86::K3, X86::K4, X86::K5, X86::K6, X86::K7, 
  };

  // VK32 Bit set.
  const uint8_t VK32Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 
  };

  // GR32_NOREX_NOSP Register Class...
  const MCPhysReg GR32_NOREX_NOSP[] = {
    X86::EAX, X86::ECX, X86::EDX, X86::ESI, X86::EDI, X86::EBX, X86::EBP, 
  };

  // GR32_NOREX_NOSP Bit set.
  const uint8_t GR32_NOREX_NOSPBits[] = {
    0x00, 0x00, 0xc0, 0x0f, 0x01, 
  };

  // RFP32 Register Class...
  const MCPhysReg RFP32[] = {
    X86::FP0, X86::FP1, X86::FP2, X86::FP3, X86::FP4, X86::FP5, X86::FP6, 
  };

  // RFP32 Bit set.
  const uint8_t RFP32Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x1f, 
  };

  // VK32WM Register Class...
  const MCPhysReg VK32WM[] = {
    X86::K1, X86::K2, X86::K3, X86::K4, X86::K5, X86::K6, X86::K7, 
  };

  // VK32WM Bit set.
  const uint8_t VK32WMBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x3f, 
  };

  // GR32_ABCD Register Class...
  const MCPhysReg GR32_ABCD[] = {
    X86::EAX, X86::ECX, X86::EDX, X86::EBX, 
  };

  // GR32_ABCD Bit set.
  const uint8_t GR32_ABCDBits[] = {
    0x00, 0x00, 0x40, 0x0b, 
  };

  // GR32_TC Register Class...
  const MCPhysReg GR32_TC[] = {
    X86::EAX, X86::ECX, X86::EDX, X86::ESP, 
  };

  // GR32_TC Bit set.
  const uint8_t GR32_TCBits[] = {
    0x00, 0x00, 0x40, 0x0a, 0x02, 
  };

  // GR32_ABCD_and_GR32_TC Register Class...
  const MCPhysReg GR32_ABCD_and_GR32_TC[] = {
    X86::EAX, X86::ECX, X86::EDX, 
  };

  // GR32_ABCD_and_GR32_TC Bit set.
  const uint8_t GR32_ABCD_and_GR32_TCBits[] = {
    0x00, 0x00, 0x40, 0x0a, 
  };

  // GR32_AD Register Class...
  const MCPhysReg GR32_AD[] = {
    X86::EAX, X86::EDX, 
  };

  // GR32_AD Bit set.
  const uint8_t GR32_ADBits[] = {
    0x00, 0x00, 0x40, 0x08, 
  };

  // GR32_BPSP Register Class...
  const MCPhysReg GR32_BPSP[] = {
    X86::EBP, X86::ESP, 
  };

  // GR32_BPSP Bit set.
  const uint8_t GR32_BPSPBits[] = {
    0x00, 0x00, 0x80, 0x00, 0x02, 
  };

  // GR32_BSI Register Class...
  const MCPhysReg GR32_BSI[] = {
    X86::EBX, X86::ESI, 
  };

  // GR32_BSI Bit set.
  const uint8_t GR32_BSIBits[] = {
    0x00, 0x00, 0x00, 0x01, 0x01, 
  };

  // GR32_CB Register Class...
  const MCPhysReg GR32_CB[] = {
    X86::ECX, X86::EBX, 
  };

  // GR32_CB Bit set.
  const uint8_t GR32_CBBits[] = {
    0x00, 0x00, 0x00, 0x03, 
  };

  // GR32_DC Register Class...
  const MCPhysReg GR32_DC[] = {
    X86::EDX, X86::ECX, 
  };

  // GR32_DC Bit set.
  const uint8_t GR32_DCBits[] = {
    0x00, 0x00, 0x00, 0x0a, 
  };

  // GR32_DIBP Register Class...
  const MCPhysReg GR32_DIBP[] = {
    X86::EDI, X86::EBP, 
  };

  // GR32_DIBP Bit set.
  const uint8_t GR32_DIBPBits[] = {
    0x00, 0x00, 0x80, 0x04, 
  };

  // GR32_SIDI Register Class...
  const MCPhysReg GR32_SIDI[] = {
    X86::ESI, X86::EDI, 
  };

  // GR32_SIDI Bit set.
  const uint8_t GR32_SIDIBits[] = {
    0x00, 0x00, 0x00, 0x04, 0x01, 
  };

  // LOW32_ADDR_ACCESS_RBP_with_sub_32bit Register Class...
  const MCPhysReg LOW32_ADDR_ACCESS_RBP_with_sub_32bit[] = {
    X86::RIP, X86::RBP, 
  };

  // LOW32_ADDR_ACCESS_RBP_with_sub_32bit Bit set.
  const uint8_t LOW32_ADDR_ACCESS_RBP_with_sub_32bitBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x42, 
  };

  // CCR Register Class...
  const MCPhysReg CCR[] = {
    X86::EFLAGS, 
  };

  // CCR Bit set.
  const uint8_t CCRBits[] = {
    0x00, 0x00, 0x00, 0x10, 
  };

  // DFCCR Register Class...
  const MCPhysReg DFCCR[] = {
    X86::DF, 
  };

  // DFCCR Bit set.
  const uint8_t DFCCRBits[] = {
    0x00, 0x40, 
  };

  // GR32_ABCD_and_GR32_BSI Register Class...
  const MCPhysReg GR32_ABCD_and_GR32_BSI[] = {
    X86::EBX, 
  };

  // GR32_ABCD_and_GR32_BSI Bit set.
  const uint8_t GR32_ABCD_and_GR32_BSIBits[] = {
    0x00, 0x00, 0x00, 0x01, 
  };

  // GR32_AD_and_GR32_DC Register Class...
  const MCPhysReg GR32_AD_and_GR32_DC[] = {
    X86::EDX, 
  };

  // GR32_AD_and_GR32_DC Bit set.
  const uint8_t GR32_AD_and_GR32_DCBits[] = {
    0x00, 0x00, 0x00, 0x08, 
  };

  // GR32_BPSP_and_GR32_DIBP Register Class...
  const MCPhysReg GR32_BPSP_and_GR32_DIBP[] = {
    X86::EBP, 
  };

  // GR32_BPSP_and_GR32_DIBP Bit set.
  const uint8_t GR32_BPSP_and_GR32_DIBPBits[] = {
    0x00, 0x00, 0x80, 
  };

  // GR32_BPSP_and_GR32_TC Register Class...
  const MCPhysReg GR32_BPSP_and_GR32_TC[] = {
    X86::ESP, 
  };

  // GR32_BPSP_and_GR32_TC Bit set.
  const uint8_t GR32_BPSP_and_GR32_TCBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x02, 
  };

  // GR32_BSI_and_GR32_SIDI Register Class...
  const MCPhysReg GR32_BSI_and_GR32_SIDI[] = {
    X86::ESI, 
  };

  // GR32_BSI_and_GR32_SIDI Bit set.
  const uint8_t GR32_BSI_and_GR32_SIDIBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x01, 
  };

  // GR32_CB_and_GR32_DC Register Class...
  const MCPhysReg GR32_CB_and_GR32_DC[] = {
    X86::ECX, 
  };

  // GR32_CB_and_GR32_DC Bit set.
  const uint8_t GR32_CB_and_GR32_DCBits[] = {
    0x00, 0x00, 0x00, 0x02, 
  };

  // GR32_DIBP_and_GR32_SIDI Register Class...
  const MCPhysReg GR32_DIBP_and_GR32_SIDI[] = {
    X86::EDI, 
  };

  // GR32_DIBP_and_GR32_SIDI Bit set.
  const uint8_t GR32_DIBP_and_GR32_SIDIBits[] = {
    0x00, 0x00, 0x00, 0x04, 
  };

  // LOW32_ADDR_ACCESS_RBP_with_sub_8bit_with_sub_32bit Register Class...
  const MCPhysReg LOW32_ADDR_ACCESS_RBP_with_sub_8bit_with_sub_32bit[] = {
    X86::RBP, 
  };

  // LOW32_ADDR_ACCESS_RBP_with_sub_8bit_with_sub_32bit Bit set.
  const uint8_t LOW32_ADDR_ACCESS_RBP_with_sub_8bit_with_sub_32bitBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 
  };

  // LOW32_ADDR_ACCESS_with_sub_32bit Register Class...
  const MCPhysReg LOW32_ADDR_ACCESS_with_sub_32bit[] = {
    X86::RIP, 
  };

  // LOW32_ADDR_ACCESS_with_sub_32bit Bit set.
  const uint8_t LOW32_ADDR_ACCESS_with_sub_32bitBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 
  };

  // RFP64 Register Class...
  const MCPhysReg RFP64[] = {
    X86::FP0, X86::FP1, X86::FP2, X86::FP3, X86::FP4, X86::FP5, X86::FP6, 
  };

  // RFP64 Bit set.
  const uint8_t RFP64Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x1f, 
  };

  // FR64X Register Class...
  const MCPhysReg FR64X[] = {
    X86::XMM0, X86::XMM1, X86::XMM2, X86::XMM3, X86::XMM4, X86::XMM5, X86::XMM6, X86::XMM7, X86::XMM8, X86::XMM9, X86::XMM10, X86::XMM11, X86::XMM12, X86::XMM13, X86::XMM14, X86::XMM15, X86::XMM16, X86::XMM17, X86::XMM18, X86::XMM19, X86::XMM20, X86::XMM21, X86::XMM22, X86::XMM23, X86::XMM24, X86::XMM25, X86::XMM26, X86::XMM27, X86::XMM28, X86::XMM29, X86::XMM30, X86::XMM31, 
  };

  // FR64X Bit set.
  const uint8_t FR64XBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0xff, 0xff, 0xff, 0x3f, 
  };

  // GR64 Register Class...
  const MCPhysReg GR64[] = {
    X86::RAX, X86::RCX, X86::RDX, X86::RSI, X86::RDI, X86::R8, X86::R9, X86::R10, X86::R11, X86::RBX, X86::R14, X86::R15, X86::R12, X86::R13, X86::RBP, X86::RSP, X86::RIP, 
  };

  // GR64 Bit set.
  const uint8_t GR64Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 
  };

  // CONTROL_REG Register Class...
  const MCPhysReg CONTROL_REG[] = {
    X86::CR0, X86::CR1, X86::CR2, X86::CR3, X86::CR4, X86::CR5, X86::CR6, X86::CR7, X86::CR8, X86::CR9, X86::CR10, X86::CR11, X86::CR12, X86::CR13, X86::CR14, X86::CR15, 
  };

  // CONTROL_REG Bit set.
  const uint8_t CONTROL_REGBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0xff, 0x3f, 
  };

  // FR64 Register Class...
  const MCPhysReg FR64[] = {
    X86::XMM0, X86::XMM1, X86::XMM2, X86::XMM3, X86::XMM4, X86::XMM5, X86::XMM6, X86::XMM7, X86::XMM8, X86::XMM9, X86::XMM10, X86::XMM11, X86::XMM12, X86::XMM13, X86::XMM14, X86::XMM15, 
  };

  // FR64 Bit set.
  const uint8_t FR64Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0xff, 0x3f, 
  };

  // GR64_with_sub_8bit Register Class...
  const MCPhysReg GR64_with_sub_8bit[] = {
    X86::RAX, X86::RCX, X86::RDX, X86::RSI, X86::RDI, X86::R8, X86::R9, X86::R10, X86::R11, X86::RBX, X86::R14, X86::R15, X86::R12, X86::R13, X86::RBP, X86::RSP, 
  };

  // GR64_with_sub_8bit Bit set.
  const uint8_t GR64_with_sub_8bitBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 
  };

  // GR64_NOSP Register Class...
  const MCPhysReg GR64_NOSP[] = {
    X86::RAX, X86::RCX, X86::RDX, X86::RSI, X86::RDI, X86::R8, X86::R9, X86::R10, X86::R11, X86::RBX, X86::R14, X86::R15, X86::R12, X86::R13, X86::RBP, 
  };

  // GR64_NOSP Bit set.
  const uint8_t GR64_NOSPBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 
  };

  // GR64_TC Register Class...
  const MCPhysReg GR64_TC[] = {
    X86::RAX, X86::RCX, X86::RDX, X86::RSI, X86::RDI, X86::R8, X86::R9, X86::R11, X86::RIP, X86::RSP, 
  };

  // GR64_TC Bit set.
  const uint8_t GR64_TCBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x79, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x02, 
  };

  // GR64_NOREX Register Class...
  const MCPhysReg GR64_NOREX[] = {
    X86::RAX, X86::RCX, X86::RDX, X86::RSI, X86::RDI, X86::RBX, X86::RBP, X86::RSP, X86::RIP, 
  };

  // GR64_NOREX Bit set.
  const uint8_t GR64_NOREXBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0x03, 
  };

  // GR64_TCW64 Register Class...
  const MCPhysReg GR64_TCW64[] = {
    X86::RAX, X86::RCX, X86::RDX, X86::R8, X86::R9, X86::R10, X86::R11, X86::RIP, X86::RSP, 
  };

  // GR64_TCW64 Bit set.
  const uint8_t GR64_TCW64Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x69, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x03, 
  };

  // GR64_TC_with_sub_8bit Register Class...
  const MCPhysReg GR64_TC_with_sub_8bit[] = {
    X86::RAX, X86::RCX, X86::RDX, X86::RSI, X86::RDI, X86::R8, X86::R9, X86::R11, X86::RSP, 
  };

  // GR64_TC_with_sub_8bit Bit set.
  const uint8_t GR64_TC_with_sub_8bitBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x39, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x02, 
  };

  // GR64_NOSP_and_GR64_TC Register Class...
  const MCPhysReg GR64_NOSP_and_GR64_TC[] = {
    X86::RAX, X86::RCX, X86::RDX, X86::RSI, X86::RDI, X86::R8, X86::R9, X86::R11, 
  };

  // GR64_NOSP_and_GR64_TC Bit set.
  const uint8_t GR64_NOSP_and_GR64_TCBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x39, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x02, 
  };

  // GR64_TCW64_with_sub_8bit Register Class...
  const MCPhysReg GR64_TCW64_with_sub_8bit[] = {
    X86::RAX, X86::RCX, X86::RDX, X86::R8, X86::R9, X86::R10, X86::R11, X86::RSP, 
  };

  // GR64_TCW64_with_sub_8bit Bit set.
  const uint8_t GR64_TCW64_with_sub_8bitBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x29, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x03, 
  };

  // GR64_TC_and_GR64_TCW64 Register Class...
  const MCPhysReg GR64_TC_and_GR64_TCW64[] = {
    X86::RAX, X86::RCX, X86::RDX, X86::R8, X86::R9, X86::R11, X86::RIP, X86::RSP, 
  };

  // GR64_TC_and_GR64_TCW64 Bit set.
  const uint8_t GR64_TC_and_GR64_TCW64Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x69, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x02, 
  };

  // GR64_with_sub_16bit_in_GR16_NOREX Register Class...
  const MCPhysReg GR64_with_sub_16bit_in_GR16_NOREX[] = {
    X86::RAX, X86::RCX, X86::RDX, X86::RSI, X86::RDI, X86::RBX, X86::RBP, X86::RSP, 
  };

  // GR64_with_sub_16bit_in_GR16_NOREX Bit set.
  const uint8_t GR64_with_sub_16bit_in_GR16_NOREXBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0x03, 
  };

  // VK64 Register Class...
  const MCPhysReg VK64[] = {
    X86::K0, X86::K1, X86::K2, X86::K3, X86::K4, X86::K5, X86::K6, X86::K7, 
  };

  // VK64 Bit set.
  const uint8_t VK64Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 
  };

  // VR64 Register Class...
  const MCPhysReg VR64[] = {
    X86::MM0, X86::MM1, X86::MM2, X86::MM3, X86::MM4, X86::MM5, X86::MM6, X86::MM7, 
  };

  // VR64 Bit set.
  const uint8_t VR64Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 
  };

  // GR64_NOREX_NOSP Register Class...
  const MCPhysReg GR64_NOREX_NOSP[] = {
    X86::RAX, X86::RCX, X86::RDX, X86::RSI, X86::RDI, X86::RBX, X86::RBP, 
  };

  // GR64_NOREX_NOSP Bit set.
  const uint8_t GR64_NOREX_NOSPBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0x01, 
  };

  // GR64_NOREX_and_GR64_TC Register Class...
  const MCPhysReg GR64_NOREX_and_GR64_TC[] = {
    X86::RAX, X86::RCX, X86::RDX, X86::RSI, X86::RDI, X86::RSP, X86::RIP, 
  };

  // GR64_NOREX_and_GR64_TC Bit set.
  const uint8_t GR64_NOREX_and_GR64_TCBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x79, 0x03, 
  };

  // GR64_NOSP_and_GR64_TCW64 Register Class...
  const MCPhysReg GR64_NOSP_and_GR64_TCW64[] = {
    X86::RAX, X86::RCX, X86::RDX, X86::R8, X86::R9, X86::R10, X86::R11, 
  };

  // GR64_NOSP_and_GR64_TCW64 Bit set.
  const uint8_t GR64_NOSP_and_GR64_TCW64Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x03, 
  };

  // GR64_TCW64_and_GR64_TC_with_sub_8bit Register Class...
  const MCPhysReg GR64_TCW64_and_GR64_TC_with_sub_8bit[] = {
    X86::RAX, X86::RCX, X86::RDX, X86::R8, X86::R9, X86::R11, X86::RSP, 
  };

  // GR64_TCW64_and_GR64_TC_with_sub_8bit Bit set.
  const uint8_t GR64_TCW64_and_GR64_TC_with_sub_8bitBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x29, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x02, 
  };

  // VK64WM Register Class...
  const MCPhysReg VK64WM[] = {
    X86::K1, X86::K2, X86::K3, X86::K4, X86::K5, X86::K6, X86::K7, 
  };

  // VK64WM Bit set.
  const uint8_t VK64WMBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x3f, 
  };

  // GR64_TC_and_GR64_NOSP_and_GR64_TCW64 Register Class...
  const MCPhysReg GR64_TC_and_GR64_NOSP_and_GR64_TCW64[] = {
    X86::RAX, X86::RCX, X86::RDX, X86::R8, X86::R9, X86::R11, 
  };

  // GR64_TC_and_GR64_NOSP_and_GR64_TCW64 Bit set.
  const uint8_t GR64_TC_and_GR64_NOSP_and_GR64_TCW64Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x02, 
  };

  // GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREX Register Class...
  const MCPhysReg GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREX[] = {
    X86::RAX, X86::RCX, X86::RDX, X86::RSI, X86::RDI, X86::RSP, 
  };

  // GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREX Bit set.
  const uint8_t GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREXBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x39, 0x03, 
  };

  // GR64_NOREX_NOSP_and_GR64_TC Register Class...
  const MCPhysReg GR64_NOREX_NOSP_and_GR64_TC[] = {
    X86::RAX, X86::RCX, X86::RDX, X86::RSI, X86::RDI, 
  };

  // GR64_NOREX_NOSP_and_GR64_TC Bit set.
  const uint8_t GR64_NOREX_NOSP_and_GR64_TCBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x39, 0x01, 
  };

  // GR64_NOREX_and_GR64_TCW64 Register Class...
  const MCPhysReg GR64_NOREX_and_GR64_TCW64[] = {
    X86::RAX, X86::RCX, X86::RDX, X86::RSP, X86::RIP, 
  };

  // GR64_NOREX_and_GR64_TCW64 Bit set.
  const uint8_t GR64_NOREX_and_GR64_TCW64Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x69, 0x02, 
  };

  // GR64_ABCD Register Class...
  const MCPhysReg GR64_ABCD[] = {
    X86::RAX, X86::RCX, X86::RDX, X86::RBX, 
  };

  // GR64_ABCD Bit set.
  const uint8_t GR64_ABCDBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2d, 
  };

  // GR64_with_sub_32bit_in_GR32_TC Register Class...
  const MCPhysReg GR64_with_sub_32bit_in_GR32_TC[] = {
    X86::RAX, X86::RCX, X86::RDX, X86::RSP, 
  };

  // GR64_with_sub_32bit_in_GR32_TC Bit set.
  const uint8_t GR64_with_sub_32bit_in_GR32_TCBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x29, 0x02, 
  };

  // GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TC Register Class...
  const MCPhysReg GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TC[] = {
    X86::RAX, X86::RCX, X86::RDX, 
  };

  // GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TC Bit set.
  const uint8_t GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TCBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x29, 
  };

  // GR64_AD Register Class...
  const MCPhysReg GR64_AD[] = {
    X86::RAX, X86::RDX, 
  };

  // GR64_AD Bit set.
  const uint8_t GR64_ADBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x21, 
  };

  // GR64_and_LOW32_ADDR_ACCESS_RBP Register Class...
  const MCPhysReg GR64_and_LOW32_ADDR_ACCESS_RBP[] = {
    X86::RBP, X86::RIP, 
  };

  // GR64_and_LOW32_ADDR_ACCESS_RBP Bit set.
  const uint8_t GR64_and_LOW32_ADDR_ACCESS_RBPBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x42, 
  };

  // GR64_with_sub_32bit_in_GR32_BPSP Register Class...
  const MCPhysReg GR64_with_sub_32bit_in_GR32_BPSP[] = {
    X86::RBP, X86::RSP, 
  };

  // GR64_with_sub_32bit_in_GR32_BPSP Bit set.
  const uint8_t GR64_with_sub_32bit_in_GR32_BPSPBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x02, 
  };

  // GR64_with_sub_32bit_in_GR32_BSI Register Class...
  const MCPhysReg GR64_with_sub_32bit_in_GR32_BSI[] = {
    X86::RSI, X86::RBX, 
  };

  // GR64_with_sub_32bit_in_GR32_BSI Bit set.
  const uint8_t GR64_with_sub_32bit_in_GR32_BSIBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x01, 
  };

  // GR64_with_sub_32bit_in_GR32_CB Register Class...
  const MCPhysReg GR64_with_sub_32bit_in_GR32_CB[] = {
    X86::RCX, X86::RBX, 
  };

  // GR64_with_sub_32bit_in_GR32_CB Bit set.
  const uint8_t GR64_with_sub_32bit_in_GR32_CBBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 
  };

  // GR64_with_sub_32bit_in_GR32_DC Register Class...
  const MCPhysReg GR64_with_sub_32bit_in_GR32_DC[] = {
    X86::RCX, X86::RDX, 
  };

  // GR64_with_sub_32bit_in_GR32_DC Bit set.
  const uint8_t GR64_with_sub_32bit_in_GR32_DCBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 
  };

  // GR64_with_sub_32bit_in_GR32_DIBP Register Class...
  const MCPhysReg GR64_with_sub_32bit_in_GR32_DIBP[] = {
    X86::RDI, X86::RBP, 
  };

  // GR64_with_sub_32bit_in_GR32_DIBP Bit set.
  const uint8_t GR64_with_sub_32bit_in_GR32_DIBPBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x12, 
  };

  // GR64_with_sub_32bit_in_GR32_SIDI Register Class...
  const MCPhysReg GR64_with_sub_32bit_in_GR32_SIDI[] = {
    X86::RSI, X86::RDI, 
  };

  // GR64_with_sub_32bit_in_GR32_SIDI Bit set.
  const uint8_t GR64_with_sub_32bit_in_GR32_SIDIBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x01, 
  };

  // GR64_and_LOW32_ADDR_ACCESS Register Class...
  const MCPhysReg GR64_and_LOW32_ADDR_ACCESS[] = {
    X86::RIP, 
  };

  // GR64_and_LOW32_ADDR_ACCESS Bit set.
  const uint8_t GR64_and_LOW32_ADDR_ACCESSBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 
  };

  // GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_BSI Register Class...
  const MCPhysReg GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_BSI[] = {
    X86::RBX, 
  };

  // GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_BSI Bit set.
  const uint8_t GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_BSIBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 
  };

  // GR64_with_sub_32bit_in_GR32_AD_and_GR32_DC Register Class...
  const MCPhysReg GR64_with_sub_32bit_in_GR32_AD_and_GR32_DC[] = {
    X86::RDX, 
  };

  // GR64_with_sub_32bit_in_GR32_AD_and_GR32_DC Bit set.
  const uint8_t GR64_with_sub_32bit_in_GR32_AD_and_GR32_DCBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 
  };

  // GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_DIBP Register Class...
  const MCPhysReg GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_DIBP[] = {
    X86::RBP, 
  };

  // GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_DIBP Bit set.
  const uint8_t GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_DIBPBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 
  };

  // GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_TC Register Class...
  const MCPhysReg GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_TC[] = {
    X86::RSP, 
  };

  // GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_TC Bit set.
  const uint8_t GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_TCBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 
  };

  // GR64_with_sub_32bit_in_GR32_BSI_and_GR32_SIDI Register Class...
  const MCPhysReg GR64_with_sub_32bit_in_GR32_BSI_and_GR32_SIDI[] = {
    X86::RSI, 
  };

  // GR64_with_sub_32bit_in_GR32_BSI_and_GR32_SIDI Bit set.
  const uint8_t GR64_with_sub_32bit_in_GR32_BSI_and_GR32_SIDIBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 
  };

  // GR64_with_sub_32bit_in_GR32_CB_and_GR32_DC Register Class...
  const MCPhysReg GR64_with_sub_32bit_in_GR32_CB_and_GR32_DC[] = {
    X86::RCX, 
  };

  // GR64_with_sub_32bit_in_GR32_CB_and_GR32_DC Bit set.
  const uint8_t GR64_with_sub_32bit_in_GR32_CB_and_GR32_DCBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 
  };

  // GR64_with_sub_32bit_in_GR32_DIBP_and_GR32_SIDI Register Class...
  const MCPhysReg GR64_with_sub_32bit_in_GR32_DIBP_and_GR32_SIDI[] = {
    X86::RDI, 
  };

  // GR64_with_sub_32bit_in_GR32_DIBP_and_GR32_SIDI Bit set.
  const uint8_t GR64_with_sub_32bit_in_GR32_DIBP_and_GR32_SIDIBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 
  };

  // RST Register Class...
  const MCPhysReg RST[] = {
    X86::ST0, X86::ST1, X86::ST2, X86::ST3, X86::ST4, X86::ST5, X86::ST6, X86::ST7, 
  };

  // RST Bit set.
  const uint8_t RSTBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 
  };

  // RFP80 Register Class...
  const MCPhysReg RFP80[] = {
    X86::FP0, X86::FP1, X86::FP2, X86::FP3, X86::FP4, X86::FP5, X86::FP6, 
  };

  // RFP80 Bit set.
  const uint8_t RFP80Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x1f, 
  };

  // RFP80_7 Register Class...
  const MCPhysReg RFP80_7[] = {
    X86::FP7, 
  };

  // RFP80_7 Bit set.
  const uint8_t RFP80_7Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 
  };

  // VR128X Register Class...
  const MCPhysReg VR128X[] = {
    X86::XMM0, X86::XMM1, X86::XMM2, X86::XMM3, X86::XMM4, X86::XMM5, X86::XMM6, X86::XMM7, X86::XMM8, X86::XMM9, X86::XMM10, X86::XMM11, X86::XMM12, X86::XMM13, X86::XMM14, X86::XMM15, X86::XMM16, X86::XMM17, X86::XMM18, X86::XMM19, X86::XMM20, X86::XMM21, X86::XMM22, X86::XMM23, X86::XMM24, X86::XMM25, X86::XMM26, X86::XMM27, X86::XMM28, X86::XMM29, X86::XMM30, X86::XMM31, 
  };

  // VR128X Bit set.
  const uint8_t VR128XBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0xff, 0xff, 0xff, 0x3f, 
  };

  // VR128 Register Class...
  const MCPhysReg VR128[] = {
    X86::XMM0, X86::XMM1, X86::XMM2, X86::XMM3, X86::XMM4, X86::XMM5, X86::XMM6, X86::XMM7, X86::XMM8, X86::XMM9, X86::XMM10, X86::XMM11, X86::XMM12, X86::XMM13, X86::XMM14, X86::XMM15, 
  };

  // VR128 Bit set.
  const uint8_t VR128Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0xff, 0x3f, 
  };

  // VR128H Register Class...
  const MCPhysReg VR128H[] = {
    X86::XMM8, X86::XMM9, X86::XMM10, X86::XMM11, X86::XMM12, X86::XMM13, X86::XMM14, X86::XMM15, 
  };

  // VR128H Bit set.
  const uint8_t VR128HBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 
  };

  // VR128L Register Class...
  const MCPhysReg VR128L[] = {
    X86::XMM0, X86::XMM1, X86::XMM2, X86::XMM3, X86::XMM4, X86::XMM5, X86::XMM6, X86::XMM7, 
  };

  // VR128L Bit set.
  const uint8_t VR128LBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 
  };

  // BNDR Register Class...
  const MCPhysReg BNDR[] = {
    X86::BND0, X86::BND1, X86::BND2, X86::BND3, 
  };

  // BNDR Bit set.
  const uint8_t BNDRBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3c, 
  };

  // VR256X Register Class...
  const MCPhysReg VR256X[] = {
    X86::YMM0, X86::YMM1, X86::YMM2, X86::YMM3, X86::YMM4, X86::YMM5, X86::YMM6, X86::YMM7, X86::YMM8, X86::YMM9, X86::YMM10, X86::YMM11, X86::YMM12, X86::YMM13, X86::YMM14, X86::YMM15, X86::YMM16, X86::YMM17, X86::YMM18, X86::YMM19, X86::YMM20, X86::YMM21, X86::YMM22, X86::YMM23, X86::YMM24, X86::YMM25, X86::YMM26, X86::YMM27, X86::YMM28, X86::YMM29, X86::YMM30, X86::YMM31, 
  };

  // VR256X Bit set.
  const uint8_t VR256XBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0xff, 0xff, 0xff, 0x3f, 
  };

  // VR256 Register Class...
  const MCPhysReg VR256[] = {
    X86::YMM0, X86::YMM1, X86::YMM2, X86::YMM3, X86::YMM4, X86::YMM5, X86::YMM6, X86::YMM7, X86::YMM8, X86::YMM9, X86::YMM10, X86::YMM11, X86::YMM12, X86::YMM13, X86::YMM14, X86::YMM15, 
  };

  // VR256 Bit set.
  const uint8_t VR256Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0xff, 0x3f, 
  };

  // VR256H Register Class...
  const MCPhysReg VR256H[] = {
    X86::YMM8, X86::YMM9, X86::YMM10, X86::YMM11, X86::YMM12, X86::YMM13, X86::YMM14, X86::YMM15, 
  };

  // VR256H Bit set.
  const uint8_t VR256HBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 
  };

  // VR256L Register Class...
  const MCPhysReg VR256L[] = {
    X86::YMM0, X86::YMM1, X86::YMM2, X86::YMM3, X86::YMM4, X86::YMM5, X86::YMM6, X86::YMM7, 
  };

  // VR256L Bit set.
  const uint8_t VR256LBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 
  };

  // VR512 Register Class...
  const MCPhysReg VR512[] = {
    X86::ZMM0, X86::ZMM1, X86::ZMM2, X86::ZMM3, X86::ZMM4, X86::ZMM5, X86::ZMM6, X86::ZMM7, X86::ZMM8, X86::ZMM9, X86::ZMM10, X86::ZMM11, X86::ZMM12, X86::ZMM13, X86::ZMM14, X86::ZMM15, X86::ZMM16, X86::ZMM17, X86::ZMM18, X86::ZMM19, X86::ZMM20, X86::ZMM21, X86::ZMM22, X86::ZMM23, X86::ZMM24, X86::ZMM25, X86::ZMM26, X86::ZMM27, X86::ZMM28, X86::ZMM29, X86::ZMM30, X86::ZMM31, 
  };

  // VR512 Bit set.
  const uint8_t VR512Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0xff, 0xff, 0xff, 0x3f, 
  };

  // VR512_with_sub_xmm_in_FR32 Register Class...
  const MCPhysReg VR512_with_sub_xmm_in_FR32[] = {
    X86::ZMM0, X86::ZMM1, X86::ZMM2, X86::ZMM3, X86::ZMM4, X86::ZMM5, X86::ZMM6, X86::ZMM7, X86::ZMM8, X86::ZMM9, X86::ZMM10, X86::ZMM11, X86::ZMM12, X86::ZMM13, X86::ZMM14, X86::ZMM15, 
  };

  // VR512_with_sub_xmm_in_FR32 Bit set.
  const uint8_t VR512_with_sub_xmm_in_FR32Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0xff, 0x3f, 
  };

  // VR512_with_sub_xmm_in_VR128H Register Class...
  const MCPhysReg VR512_with_sub_xmm_in_VR128H[] = {
    X86::ZMM8, X86::ZMM9, X86::ZMM10, X86::ZMM11, X86::ZMM12, X86::ZMM13, X86::ZMM14, X86::ZMM15, 
  };

  // VR512_with_sub_xmm_in_VR128H Bit set.
  const uint8_t VR512_with_sub_xmm_in_VR128HBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 
  };

  // VR512_with_sub_xmm_in_VR128L Register Class...
  const MCPhysReg VR512_with_sub_xmm_in_VR128L[] = {
    X86::ZMM0, X86::ZMM1, X86::ZMM2, X86::ZMM3, X86::ZMM4, X86::ZMM5, X86::ZMM6, X86::ZMM7, 
  };

  // VR512_with_sub_xmm_in_VR128L Bit set.
  const uint8_t VR512_with_sub_xmm_in_VR128LBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 
  };

} // end anonymous namespace

extern const char X86RegClassStrings[] = {
  /* 0 */ 'R', 'F', 'P', '8', '0', 0,
  /* 6 */ 'V', 'K', '1', 0,
  /* 10 */ 'V', 'R', '5', '1', '2', 0,
  /* 16 */ 'V', 'K', '3', '2', 0,
  /* 21 */ 'R', 'F', 'P', '3', '2', 0,
  /* 27 */ 'V', 'R', '5', '1', '2', '_', 'w', 'i', 't', 'h', '_', 's', 'u', 'b', '_', 'x', 'm', 'm', '_', 'i', 'n', '_', 'F', 'R', '3', '2', 0,
  /* 54 */ 'G', 'R', '3', '2', 0,
  /* 59 */ 'V', 'K', '2', 0,
  /* 63 */ 'V', 'K', '6', '4', 0,
  /* 68 */ 'R', 'F', 'P', '6', '4', 0,
  /* 74 */ 'F', 'R', '6', '4', 0,
  /* 79 */ 'G', 'R', '6', '4', 0,
  /* 84 */ 'V', 'R', '6', '4', 0,
  /* 89 */ 'G', 'R', '6', '4', '_', 'T', 'C', '_', 'a', 'n', 'd', '_', 'G', 'R', '6', '4', '_', 'T', 'C', 'W', '6', '4', 0,
  /* 112 */ 'G', 'R', '6', '4', '_', 'T', 'C', '_', 'a', 'n', 'd', '_', 'G', 'R', '6', '4', '_', 'N', 'O', 'S', 'P', '_', 'a', 'n', 'd', '_', 'G', 'R', '6', '4', '_', 'T', 'C', 'W', '6', '4', 0,
  /* 149 */ 'G', 'R', '6', '4', '_', 'N', 'O', 'R', 'E', 'X', '_', 'a', 'n', 'd', '_', 'G', 'R', '6', '4', '_', 'T', 'C', 'W', '6', '4', 0,
  /* 175 */ 'V', 'K', '4', 0,
  /* 179 */ 'G', 'R', 'H', '1', '6', 0,
  /* 185 */ 'V', 'K', '1', '6', 0,
  /* 190 */ 'G', 'R', '1', '6', 0,
  /* 195 */ 'V', 'R', '2', '5', '6', 0,
  /* 201 */ 'R', 'F', 'P', '8', '0', '_', '7', 0,
  /* 209 */ 'V', 'R', '1', '2', '8', 0,
  /* 215 */ 'G', 'R', 'H', '8', 0,
  /* 220 */ 'V', 'K', '8', 0,
  /* 224 */ 'G', 'R', '8', 0,
  /* 228 */ 'G', 'R', '6', '4', '_', 'w', 'i', 't', 'h', '_', 's', 'u', 'b', '_', '3', '2', 'b', 'i', 't', '_', 'i', 'n', '_', 'G', 'R', '3', '2', '_', 'C', 'B', 0,
  /* 259 */ 'G', 'R', '6', '4', '_', 'w', 'i', 't', 'h', '_', 's', 'u', 'b', '_', '3', '2', 'b', 'i', 't', '_', 'i', 'n', '_', 'G', 'R', '3', '2', '_', 'C', 'B', '_', 'a', 'n', 'd', '_', 'G', 'R', '3', '2', '_', 'D', 'C', 0,
  /* 302 */ 'G', 'R', '6', '4', '_', 'w', 'i', 't', 'h', '_', 's', 'u', 'b', '_', '3', '2', 'b', 'i', 't', '_', 'i', 'n', '_', 'G', 'R', '3', '2', '_', 'A', 'D', '_', 'a', 'n', 'd', '_', 'G', 'R', '3', '2', '_', 'D', 'C', 0,
  /* 345 */ 'G', 'R', '6', '4', '_', 'w', 'i', 't', 'h', '_', 's', 'u', 'b', '_', '3', '2', 'b', 'i', 't', '_', 'i', 'n', '_', 'G', 'R', '3', '2', '_', 'D', 'C', 0,
  /* 376 */ 'G', 'R', '6', '4', '_', 'w', 'i', 't', 'h', '_', 's', 'u', 'b', '_', '3', '2', 'b', 'i', 't', '_', 'i', 'n', '_', 'G', 'R', '3', '2', '_', 'A', 'B', 'C', 'D', '_', 'a', 'n', 'd', '_', 'G', 'R', '3', '2', '_', 'T', 'C', 0,
  /* 421 */ 'G', 'R', '6', '4', '_', 'w', 'i', 't', 'h', '_', 's', 'u', 'b', '_', '3', '2', 'b', 'i', 't', '_', 'i', 'n', '_', 'G', 'R', '3', '2', '_', 'B', 'P', 'S', 'P', '_', 'a', 'n', 'd', '_', 'G', 'R', '3', '2', '_', 'T', 'C', 0,
  /* 466 */ 'G', 'R', '6', '4', '_', 'w', 'i', 't', 'h', '_', 's', 'u', 'b', '_', '3', '2', 'b', 'i', 't', '_', 'i', 'n', '_', 'G', 'R', '3', '2', '_', 'T', 'C', 0,
  /* 497 */ 'G', 'R', '6', '4', '_', 'N', 'O', 'S', 'P', '_', 'a', 'n', 'd', '_', 'G', 'R', '6', '4', '_', 'T', 'C', 0,
  /* 519 */ 'G', 'R', '6', '4', '_', 'N', 'O', 'R', 'E', 'X', '_', 'N', 'O', 'S', 'P', '_', 'a', 'n', 'd', '_', 'G', 'R', '6', '4', '_', 'T', 'C', 0,
  /* 547 */ 'G', 'R', '6', '4', '_', 'N', 'O', 'R', 'E', 'X', '_', 'a', 'n', 'd', '_', 'G', 'R', '6', '4', '_', 'T', 'C', 0,
  /* 570 */ 'G', 'R', '3', '2', '_', 'A', 'D', 0,
  /* 578 */ 'G', 'R', '6', '4', '_', 'A', 'D', 0,
  /* 586 */ 'G', 'R', '3', '2', '_', 'A', 'B', 'C', 'D', 0,
  /* 596 */ 'G', 'R', '6', '4', '_', 'A', 'B', 'C', 'D', 0,
  /* 606 */ 'G', 'R', '1', '6', '_', 'A', 'B', 'C', 'D', 0,
  /* 616 */ 'D', 'E', 'B', 'U', 'G', '_', 'R', 'E', 'G', 0,
  /* 626 */ 'C', 'O', 'N', 'T', 'R', 'O', 'L', '_', 'R', 'E', 'G', 0,
  /* 638 */ 'S', 'E', 'G', 'M', 'E', 'N', 'T', '_', 'R', 'E', 'G', 0,
  /* 650 */ 'V', 'R', '2', '5', '6', 'H', 0,
  /* 657 */ 'V', 'R', '5', '1', '2', '_', 'w', 'i', 't', 'h', '_', 's', 'u', 'b', '_', 'x', 'm', 'm', '_', 'i', 'n', '_', 'V', 'R', '1', '2', '8', 'H', 0,
  /* 686 */ 'G', 'R', '8', '_', 'A', 'B', 'C', 'D', '_', 'H', 0,
  /* 697 */ 'G', 'R', '6', '4', '_', 'w', 'i', 't', 'h', '_', 's', 'u', 'b', '_', '3', '2', 'b', 'i', 't', '_', 'i', 'n', '_', 'G', 'R', '3', '2', '_', 'B', 'S', 'I', '_', 'a', 'n', 'd', '_', 'G', 'R', '3', '2', '_', 'S', 'I', 'D', 'I', 0,
  /* 743 */ 'G', 'R', '6', '4', '_', 'w', 'i', 't', 'h', '_', 's', 'u', 'b', '_', '3', '2', 'b', 'i', 't', '_', 'i', 'n', '_', 'G', 'R', '3', '2', '_', 'D', 'I', 'B', 'P', '_', 'a', 'n', 'd', '_', 'G', 'R', '3', '2', '_', 'S', 'I', 'D', 'I', 0,
  /* 790 */ 'G', 'R', '6', '4', '_', 'w', 'i', 't', 'h', '_', 's', 'u', 'b', '_', '3', '2', 'b', 'i', 't', '_', 'i', 'n', '_', 'G', 'R', '3', '2', '_', 'S', 'I', 'D', 'I', 0,
  /* 823 */ 'G', 'R', '6', '4', '_', 'w', 'i', 't', 'h', '_', 's', 'u', 'b', '_', '3', '2', 'b', 'i', 't', '_', 'i', 'n', '_', 'G', 'R', '3', '2', '_', 'A', 'B', 'C', 'D', '_', 'a', 'n', 'd', '_', 'G', 'R', '3', '2', '_', 'B', 'S', 'I', 0,
  /* 869 */ 'G', 'R', '6', '4', '_', 'w', 'i', 't', 'h', '_', 's', 'u', 'b', '_', '3', '2', 'b', 'i', 't', '_', 'i', 'n', '_', 'G', 'R', '3', '2', '_', 'B', 'S', 'I', 0,
  /* 901 */ 'V', 'R', '2', '5', '6', 'L', 0,
  /* 908 */ 'V', 'R', '5', '1', '2', '_', 'w', 'i', 't', 'h', '_', 's', 'u', 'b', '_', 'x', 'm', 'm', '_', 'i', 'n', '_', 'V', 'R', '1', '2', '8', 'L', 0,
  /* 937 */ 'G', 'R', '8', '_', 'A', 'B', 'C', 'D', '_', 'L', 0,
  /* 948 */ 'V', 'K', '1', 'W', 'M', 0,
  /* 954 */ 'V', 'K', '3', '2', 'W', 'M', 0,
  /* 961 */ 'V', 'K', '2', 'W', 'M', 0,
  /* 967 */ 'V', 'K', '6', '4', 'W', 'M', 0,
  /* 974 */ 'V', 'K', '4', 'W', 'M', 0,
  /* 980 */ 'V', 'K', '1', '6', 'W', 'M', 0,
  /* 987 */ 'V', 'K', '8', 'W', 'M', 0,
  /* 993 */ 'G', 'R', '6', '4', '_', 'w', 'i', 't', 'h', '_', 's', 'u', 'b', '_', '3', '2', 'b', 'i', 't', '_', 'i', 'n', '_', 'G', 'R', '3', '2', '_', 'B', 'P', 'S', 'P', '_', 'a', 'n', 'd', '_', 'G', 'R', '3', '2', '_', 'D', 'I', 'B', 'P', 0,
  /* 1040 */ 'G', 'R', '6', '4', '_', 'w', 'i', 't', 'h', '_', 's', 'u', 'b', '_', '3', '2', 'b', 'i', 't', '_', 'i', 'n', '_', 'G', 'R', '3', '2', '_', 'D', 'I', 'B', 'P', 0,
  /* 1073 */ 'G', 'R', '6', '4', '_', 'a', 'n', 'd', '_', 'L', 'O', 'W', '3', '2', '_', 'A', 'D', 'D', 'R', '_', 'A', 'C', 'C', 'E', 'S', 'S', '_', 'R', 'B', 'P', 0,
  /* 1104 */ 'G', 'R', '3', '2', '_', 'N', 'O', 'S', 'P', 0,
  /* 1114 */ 'G', 'R', '6', '4', '_', 'N', 'O', 'S', 'P', 0,
  /* 1124 */ 'G', 'R', '3', '2', '_', 'N', 'O', 'R', 'E', 'X', '_', 'N', 'O', 'S', 'P', 0,
  /* 1140 */ 'G', 'R', '6', '4', '_', 'N', 'O', 'R', 'E', 'X', '_', 'N', 'O', 'S', 'P', 0,
  /* 1156 */ 'G', 'R', '6', '4', '_', 'w', 'i', 't', 'h', '_', 's', 'u', 'b', '_', '3', '2', 'b', 'i', 't', '_', 'i', 'n', '_', 'G', 'R', '3', '2', '_', 'B', 'P', 'S', 'P', 0,
  /* 1189 */ 'D', 'F', 'C', 'C', 'R', 0,
  /* 1195 */ 'F', 'P', 'C', 'C', 'R', 0,
  /* 1201 */ 'B', 'N', 'D', 'R', 0,
  /* 1206 */ 'G', 'R', '6', '4', '_', 'a', 'n', 'd', '_', 'L', 'O', 'W', '3', '2', '_', 'A', 'D', 'D', 'R', '_', 'A', 'C', 'C', 'E', 'S', 'S', 0,
  /* 1233 */ 'R', 'S', 'T', 0,
  /* 1237 */ 'F', 'R', '3', '2', 'X', 0,
  /* 1243 */ 'F', 'R', '6', '4', 'X', 0,
  /* 1249 */ 'V', 'R', '2', '5', '6', 'X', 0,
  /* 1256 */ 'V', 'R', '1', '2', '8', 'X', 0,
  /* 1263 */ 'G', 'R', '3', '2', '_', 'N', 'O', 'R', 'E', 'X', 0,
  /* 1274 */ 'G', 'R', '6', '4', '_', 'N', 'O', 'R', 'E', 'X', 0,
  /* 1285 */ 'G', 'R', '6', '4', '_', 'T', 'C', '_', 'a', 'n', 'd', '_', 'G', 'R', '6', '4', '_', 'w', 'i', 't', 'h', '_', 's', 'u', 'b', '_', '1', '6', 'b', 'i', 't', '_', 'i', 'n', '_', 'G', 'R', '1', '6', '_', 'N', 'O', 'R', 'E', 'X', 0,
  /* 1331 */ 'L', 'O', 'W', '3', '2', '_', 'A', 'D', 'D', 'R', '_', 'A', 'C', 'C', 'E', 'S', 'S', '_', 'R', 'B', 'P', '_', 'w', 'i', 't', 'h', '_', 's', 'u', 'b', '_', '1', '6', 'b', 'i', 't', '_', 'i', 'n', '_', 'G', 'R', '1', '6', '_', 'N', 'O', 'R', 'E', 'X', 0,
  /* 1382 */ 'G', 'R', '8', '_', 'N', 'O', 'R', 'E', 'X', 0,
  /* 1392 */ 'L', 'O', 'W', '3', '2', '_', 'A', 'D', 'D', 'R', '_', 'A', 'C', 'C', 'E', 'S', 'S', '_', 'R', 'B', 'P', '_', 'w', 'i', 't', 'h', '_', 's', 'u', 'b', '_', '3', '2', 'b', 'i', 't', 0,
  /* 1429 */ 'L', 'O', 'W', '3', '2', '_', 'A', 'D', 'D', 'R', '_', 'A', 'C', 'C', 'E', 'S', 'S', '_', 'w', 'i', 't', 'h', '_', 's', 'u', 'b', '_', '3', '2', 'b', 'i', 't', 0,
  /* 1462 */ 'L', 'O', 'W', '3', '2', '_', 'A', 'D', 'D', 'R', '_', 'A', 'C', 'C', 'E', 'S', 'S', '_', 'R', 'B', 'P', '_', 'w', 'i', 't', 'h', '_', 's', 'u', 'b', '_', '8', 'b', 'i', 't', '_', 'w', 'i', 't', 'h', '_', 's', 'u', 'b', '_', '3', '2', 'b', 'i', 't', 0,
  /* 1513 */ 'G', 'R', '6', '4', '_', 'w', 'i', 't', 'h', '_', 's', 'u', 'b', '_', '8', 'b', 'i', 't', 0,
  /* 1532 */ 'G', 'R', '6', '4', '_', 'T', 'C', 'W', '6', '4', '_', 'w', 'i', 't', 'h', '_', 's', 'u', 'b', '_', '8', 'b', 'i', 't', 0,
  /* 1557 */ 'G', 'R', '6', '4', '_', 'T', 'C', 'W', '6', '4', '_', 'a', 'n', 'd', '_', 'G', 'R', '6', '4', '_', 'T', 'C', '_', 'w', 'i', 't', 'h', '_', 's', 'u', 'b', '_', '8', 'b', 'i', 't', 0,
  /* 1594 */ 'L', 'O', 'W', '3', '2', '_', 'A', 'D', 'D', 'R', '_', 'A', 'C', 'C', 'E', 'S', 'S', '_', 'R', 'B', 'P', '_', 'w', 'i', 't', 'h', '_', 's', 'u', 'b', '_', '8', 'b', 'i', 't', 0,
};

extern const MCRegisterClass X86MCRegisterClasses[] = {
  { GR8, GR8Bits, 224, 20, sizeof(GR8Bits), X86::GR8RegClassID, 1, true },
  { GRH8, GRH8Bits, 215, 12, sizeof(GRH8Bits), X86::GRH8RegClassID, 1, false },
  { GR8_NOREX, GR8_NOREXBits, 1382, 8, sizeof(GR8_NOREXBits), X86::GR8_NOREXRegClassID, 1, true },
  { GR8_ABCD_H, GR8_ABCD_HBits, 686, 4, sizeof(GR8_ABCD_HBits), X86::GR8_ABCD_HRegClassID, 1, true },
  { GR8_ABCD_L, GR8_ABCD_LBits, 937, 4, sizeof(GR8_ABCD_LBits), X86::GR8_ABCD_LRegClassID, 1, true },
  { GRH16, GRH16Bits, 179, 17, sizeof(GRH16Bits), X86::GRH16RegClassID, 1, false },
  { GR16, GR16Bits, 190, 16, sizeof(GR16Bits), X86::GR16RegClassID, 1, true },
  { GR16_NOREX, GR16_NOREXBits, 1320, 8, sizeof(GR16_NOREXBits), X86::GR16_NOREXRegClassID, 1, true },
  { VK1, VK1Bits, 6, 8, sizeof(VK1Bits), X86::VK1RegClassID, 1, true },
  { VK16, VK16Bits, 185, 8, sizeof(VK16Bits), X86::VK16RegClassID, 1, true },
  { VK2, VK2Bits, 59, 8, sizeof(VK2Bits), X86::VK2RegClassID, 1, true },
  { VK4, VK4Bits, 175, 8, sizeof(VK4Bits), X86::VK4RegClassID, 1, true },
  { VK8, VK8Bits, 220, 8, sizeof(VK8Bits), X86::VK8RegClassID, 1, true },
  { VK16WM, VK16WMBits, 980, 7, sizeof(VK16WMBits), X86::VK16WMRegClassID, 1, true },
  { VK1WM, VK1WMBits, 948, 7, sizeof(VK1WMBits), X86::VK1WMRegClassID, 1, true },
  { VK2WM, VK2WMBits, 961, 7, sizeof(VK2WMBits), X86::VK2WMRegClassID, 1, true },
  { VK4WM, VK4WMBits, 974, 7, sizeof(VK4WMBits), X86::VK4WMRegClassID, 1, true },
  { VK8WM, VK8WMBits, 987, 7, sizeof(VK8WMBits), X86::VK8WMRegClassID, 1, true },
  { SEGMENT_REG, SEGMENT_REGBits, 638, 6, sizeof(SEGMENT_REGBits), X86::SEGMENT_REGRegClassID, 1, true },
  { GR16_ABCD, GR16_ABCDBits, 606, 4, sizeof(GR16_ABCDBits), X86::GR16_ABCDRegClassID, 1, true },
  { FPCCR, FPCCRBits, 1195, 1, sizeof(FPCCRBits), X86::FPCCRRegClassID, -1, false },
  { FR32X, FR32XBits, 1237, 32, sizeof(FR32XBits), X86::FR32XRegClassID, 1, true },
  { LOW32_ADDR_ACCESS_RBP, LOW32_ADDR_ACCESS_RBPBits, 1082, 18, sizeof(LOW32_ADDR_ACCESS_RBPBits), X86::LOW32_ADDR_ACCESS_RBPRegClassID, 1, true },
  { LOW32_ADDR_ACCESS, LOW32_ADDR_ACCESSBits, 1215, 17, sizeof(LOW32_ADDR_ACCESSBits), X86::LOW32_ADDR_ACCESSRegClassID, 1, true },
  { LOW32_ADDR_ACCESS_RBP_with_sub_8bit, LOW32_ADDR_ACCESS_RBP_with_sub_8bitBits, 1594, 17, sizeof(LOW32_ADDR_ACCESS_RBP_with_sub_8bitBits), X86::LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClassID, 1, true },
  { DEBUG_REG, DEBUG_REGBits, 616, 16, sizeof(DEBUG_REGBits), X86::DEBUG_REGRegClassID, 1, true },
  { FR32, FR32Bits, 49, 16, sizeof(FR32Bits), X86::FR32RegClassID, 1, true },
  { GR32, GR32Bits, 54, 16, sizeof(GR32Bits), X86::GR32RegClassID, 1, true },
  { GR32_NOSP, GR32_NOSPBits, 1104, 15, sizeof(GR32_NOSPBits), X86::GR32_NOSPRegClassID, 1, true },
  { LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREX, LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXBits, 1331, 9, sizeof(LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXBits), X86::LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXRegClassID, 1, true },
  { GR32_NOREX, GR32_NOREXBits, 1263, 8, sizeof(GR32_NOREXBits), X86::GR32_NOREXRegClassID, 1, true },
  { VK32, VK32Bits, 16, 8, sizeof(VK32Bits), X86::VK32RegClassID, 1, true },
  { GR32_NOREX_NOSP, GR32_NOREX_NOSPBits, 1124, 7, sizeof(GR32_NOREX_NOSPBits), X86::GR32_NOREX_NOSPRegClassID, 1, true },
  { RFP32, RFP32Bits, 21, 7, sizeof(RFP32Bits), X86::RFP32RegClassID, 1, true },
  { VK32WM, VK32WMBits, 954, 7, sizeof(VK32WMBits), X86::VK32WMRegClassID, 1, true },
  { GR32_ABCD, GR32_ABCDBits, 586, 4, sizeof(GR32_ABCDBits), X86::GR32_ABCDRegClassID, 1, true },
  { GR32_TC, GR32_TCBits, 413, 4, sizeof(GR32_TCBits), X86::GR32_TCRegClassID, 1, true },
  { GR32_ABCD_and_GR32_TC, GR32_ABCD_and_GR32_TCBits, 399, 3, sizeof(GR32_ABCD_and_GR32_TCBits), X86::GR32_ABCD_and_GR32_TCRegClassID, 1, true },
  { GR32_AD, GR32_ADBits, 570, 2, sizeof(GR32_ADBits), X86::GR32_ADRegClassID, 1, true },
  { GR32_BPSP, GR32_BPSPBits, 1179, 2, sizeof(GR32_BPSPBits), X86::GR32_BPSPRegClassID, 1, true },
  { GR32_BSI, GR32_BSIBits, 860, 2, sizeof(GR32_BSIBits), X86::GR32_BSIRegClassID, 1, true },
  { GR32_CB, GR32_CBBits, 251, 2, sizeof(GR32_CBBits), X86::GR32_CBRegClassID, 1, true },
  { GR32_DC, GR32_DCBits, 294, 2, sizeof(GR32_DCBits), X86::GR32_DCRegClassID, 1, true },
  { GR32_DIBP, GR32_DIBPBits, 1030, 2, sizeof(GR32_DIBPBits), X86::GR32_DIBPRegClassID, 1, true },
  { GR32_SIDI, GR32_SIDIBits, 733, 2, sizeof(GR32_SIDIBits), X86::GR32_SIDIRegClassID, 1, true },
  { LOW32_ADDR_ACCESS_RBP_with_sub_32bit, LOW32_ADDR_ACCESS_RBP_with_sub_32bitBits, 1392, 2, sizeof(LOW32_ADDR_ACCESS_RBP_with_sub_32bitBits), X86::LOW32_ADDR_ACCESS_RBP_with_sub_32bitRegClassID, 1, true },
  { CCR, CCRBits, 1191, 1, sizeof(CCRBits), X86::CCRRegClassID, -1, false },
  { DFCCR, DFCCRBits, 1189, 1, sizeof(DFCCRBits), X86::DFCCRRegClassID, -1, false },
  { GR32_ABCD_and_GR32_BSI, GR32_ABCD_and_GR32_BSIBits, 846, 1, sizeof(GR32_ABCD_and_GR32_BSIBits), X86::GR32_ABCD_and_GR32_BSIRegClassID, 1, true },
  { GR32_AD_and_GR32_DC, GR32_AD_and_GR32_DCBits, 325, 1, sizeof(GR32_AD_and_GR32_DCBits), X86::GR32_AD_and_GR32_DCRegClassID, 1, true },
  { GR32_BPSP_and_GR32_DIBP, GR32_BPSP_and_GR32_DIBPBits, 1016, 1, sizeof(GR32_BPSP_and_GR32_DIBPBits), X86::GR32_BPSP_and_GR32_DIBPRegClassID, 1, true },
  { GR32_BPSP_and_GR32_TC, GR32_BPSP_and_GR32_TCBits, 444, 1, sizeof(GR32_BPSP_and_GR32_TCBits), X86::GR32_BPSP_and_GR32_TCRegClassID, 1, true },
  { GR32_BSI_and_GR32_SIDI, GR32_BSI_and_GR32_SIDIBits, 720, 1, sizeof(GR32_BSI_and_GR32_SIDIBits), X86::GR32_BSI_and_GR32_SIDIRegClassID, 1, true },
  { GR32_CB_and_GR32_DC, GR32_CB_and_GR32_DCBits, 282, 1, sizeof(GR32_CB_and_GR32_DCBits), X86::GR32_CB_and_GR32_DCRegClassID, 1, true },
  { GR32_DIBP_and_GR32_SIDI, GR32_DIBP_and_GR32_SIDIBits, 766, 1, sizeof(GR32_DIBP_and_GR32_SIDIBits), X86::GR32_DIBP_and_GR32_SIDIRegClassID, 1, true },
  { LOW32_ADDR_ACCESS_RBP_with_sub_8bit_with_sub_32bit, LOW32_ADDR_ACCESS_RBP_with_sub_8bit_with_sub_32bitBits, 1462, 1, sizeof(LOW32_ADDR_ACCESS_RBP_with_sub_8bit_with_sub_32bitBits), X86::LOW32_ADDR_ACCESS_RBP_with_sub_8bit_with_sub_32bitRegClassID, 1, true },
  { LOW32_ADDR_ACCESS_with_sub_32bit, LOW32_ADDR_ACCESS_with_sub_32bitBits, 1429, 1, sizeof(LOW32_ADDR_ACCESS_with_sub_32bitBits), X86::LOW32_ADDR_ACCESS_with_sub_32bitRegClassID, 1, true },
  { RFP64, RFP64Bits, 68, 7, sizeof(RFP64Bits), X86::RFP64RegClassID, 1, true },
  { FR64X, FR64XBits, 1243, 32, sizeof(FR64XBits), X86::FR64XRegClassID, 1, true },
  { GR64, GR64Bits, 79, 17, sizeof(GR64Bits), X86::GR64RegClassID, 1, true },
  { CONTROL_REG, CONTROL_REGBits, 626, 16, sizeof(CONTROL_REGBits), X86::CONTROL_REGRegClassID, 1, true },
  { FR64, FR64Bits, 74, 16, sizeof(FR64Bits), X86::FR64RegClassID, 1, true },
  { GR64_with_sub_8bit, GR64_with_sub_8bitBits, 1513, 16, sizeof(GR64_with_sub_8bitBits), X86::GR64_with_sub_8bitRegClassID, 1, true },
  { GR64_NOSP, GR64_NOSPBits, 1114, 15, sizeof(GR64_NOSPBits), X86::GR64_NOSPRegClassID, 1, true },
  { GR64_TC, GR64_TCBits, 511, 10, sizeof(GR64_TCBits), X86::GR64_TCRegClassID, 1, true },
  { GR64_NOREX, GR64_NOREXBits, 1274, 9, sizeof(GR64_NOREXBits), X86::GR64_NOREXRegClassID, 1, true },
  { GR64_TCW64, GR64_TCW64Bits, 101, 9, sizeof(GR64_TCW64Bits), X86::GR64_TCW64RegClassID, 1, true },
  { GR64_TC_with_sub_8bit, GR64_TC_with_sub_8bitBits, 1572, 9, sizeof(GR64_TC_with_sub_8bitBits), X86::GR64_TC_with_sub_8bitRegClassID, 1, true },
  { GR64_NOSP_and_GR64_TC, GR64_NOSP_and_GR64_TCBits, 497, 8, sizeof(GR64_NOSP_and_GR64_TCBits), X86::GR64_NOSP_and_GR64_TCRegClassID, 1, true },
  { GR64_TCW64_with_sub_8bit, GR64_TCW64_with_sub_8bitBits, 1532, 8, sizeof(GR64_TCW64_with_sub_8bitBits), X86::GR64_TCW64_with_sub_8bitRegClassID, 1, true },
  { GR64_TC_and_GR64_TCW64, GR64_TC_and_GR64_TCW64Bits, 89, 8, sizeof(GR64_TC_and_GR64_TCW64Bits), X86::GR64_TC_and_GR64_TCW64RegClassID, 1, true },
  { GR64_with_sub_16bit_in_GR16_NOREX, GR64_with_sub_16bit_in_GR16_NOREXBits, 1297, 8, sizeof(GR64_with_sub_16bit_in_GR16_NOREXBits), X86::GR64_with_sub_16bit_in_GR16_NOREXRegClassID, 1, true },
  { VK64, VK64Bits, 63, 8, sizeof(VK64Bits), X86::VK64RegClassID, 1, true },
  { VR64, VR64Bits, 84, 8, sizeof(VR64Bits), X86::VR64RegClassID, 1, true },
  { GR64_NOREX_NOSP, GR64_NOREX_NOSPBits, 1140, 7, sizeof(GR64_NOREX_NOSPBits), X86::GR64_NOREX_NOSPRegClassID, 1, true },
  { GR64_NOREX_and_GR64_TC, GR64_NOREX_and_GR64_TCBits, 547, 7, sizeof(GR64_NOREX_and_GR64_TCBits), X86::GR64_NOREX_and_GR64_TCRegClassID, 1, true },
  { GR64_NOSP_and_GR64_TCW64, GR64_NOSP_and_GR64_TCW64Bits, 124, 7, sizeof(GR64_NOSP_and_GR64_TCW64Bits), X86::GR64_NOSP_and_GR64_TCW64RegClassID, 1, true },
  { GR64_TCW64_and_GR64_TC_with_sub_8bit, GR64_TCW64_and_GR64_TC_with_sub_8bitBits, 1557, 7, sizeof(GR64_TCW64_and_GR64_TC_with_sub_8bitBits), X86::GR64_TCW64_and_GR64_TC_with_sub_8bitRegClassID, 1, true },
  { VK64WM, VK64WMBits, 967, 7, sizeof(VK64WMBits), X86::VK64WMRegClassID, 1, true },
  { GR64_TC_and_GR64_NOSP_and_GR64_TCW64, GR64_TC_and_GR64_NOSP_and_GR64_TCW64Bits, 112, 6, sizeof(GR64_TC_and_GR64_NOSP_and_GR64_TCW64Bits), X86::GR64_TC_and_GR64_NOSP_and_GR64_TCW64RegClassID, 1, true },
  { GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREX, GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREXBits, 1285, 6, sizeof(GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREXBits), X86::GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREXRegClassID, 1, true },
  { GR64_NOREX_NOSP_and_GR64_TC, GR64_NOREX_NOSP_and_GR64_TCBits, 519, 5, sizeof(GR64_NOREX_NOSP_and_GR64_TCBits), X86::GR64_NOREX_NOSP_and_GR64_TCRegClassID, 1, true },
  { GR64_NOREX_and_GR64_TCW64, GR64_NOREX_and_GR64_TCW64Bits, 149, 5, sizeof(GR64_NOREX_and_GR64_TCW64Bits), X86::GR64_NOREX_and_GR64_TCW64RegClassID, 1, true },
  { GR64_ABCD, GR64_ABCDBits, 596, 4, sizeof(GR64_ABCDBits), X86::GR64_ABCDRegClassID, 1, true },
  { GR64_with_sub_32bit_in_GR32_TC, GR64_with_sub_32bit_in_GR32_TCBits, 466, 4, sizeof(GR64_with_sub_32bit_in_GR32_TCBits), X86::GR64_with_sub_32bit_in_GR32_TCRegClassID, 1, true },
  { GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TC, GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TCBits, 376, 3, sizeof(GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TCBits), X86::GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TCRegClassID, 1, true },
  { GR64_AD, GR64_ADBits, 578, 2, sizeof(GR64_ADBits), X86::GR64_ADRegClassID, 1, true },
  { GR64_and_LOW32_ADDR_ACCESS_RBP, GR64_and_LOW32_ADDR_ACCESS_RBPBits, 1073, 2, sizeof(GR64_and_LOW32_ADDR_ACCESS_RBPBits), X86::GR64_and_LOW32_ADDR_ACCESS_RBPRegClassID, 1, true },
  { GR64_with_sub_32bit_in_GR32_BPSP, GR64_with_sub_32bit_in_GR32_BPSPBits, 1156, 2, sizeof(GR64_with_sub_32bit_in_GR32_BPSPBits), X86::GR64_with_sub_32bit_in_GR32_BPSPRegClassID, 1, true },
  { GR64_with_sub_32bit_in_GR32_BSI, GR64_with_sub_32bit_in_GR32_BSIBits, 869, 2, sizeof(GR64_with_sub_32bit_in_GR32_BSIBits), X86::GR64_with_sub_32bit_in_GR32_BSIRegClassID, 1, true },
  { GR64_with_sub_32bit_in_GR32_CB, GR64_with_sub_32bit_in_GR32_CBBits, 228, 2, sizeof(GR64_with_sub_32bit_in_GR32_CBBits), X86::GR64_with_sub_32bit_in_GR32_CBRegClassID, 1, true },
  { GR64_with_sub_32bit_in_GR32_DC, GR64_with_sub_32bit_in_GR32_DCBits, 345, 2, sizeof(GR64_with_sub_32bit_in_GR32_DCBits), X86::GR64_with_sub_32bit_in_GR32_DCRegClassID, 1, true },
  { GR64_with_sub_32bit_in_GR32_DIBP, GR64_with_sub_32bit_in_GR32_DIBPBits, 1040, 2, sizeof(GR64_with_sub_32bit_in_GR32_DIBPBits), X86::GR64_with_sub_32bit_in_GR32_DIBPRegClassID, 1, true },
  { GR64_with_sub_32bit_in_GR32_SIDI, GR64_with_sub_32bit_in_GR32_SIDIBits, 790, 2, sizeof(GR64_with_sub_32bit_in_GR32_SIDIBits), X86::GR64_with_sub_32bit_in_GR32_SIDIRegClassID, 1, true },
  { GR64_and_LOW32_ADDR_ACCESS, GR64_and_LOW32_ADDR_ACCESSBits, 1206, 1, sizeof(GR64_and_LOW32_ADDR_ACCESSBits), X86::GR64_and_LOW32_ADDR_ACCESSRegClassID, 1, true },
  { GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_BSI, GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_BSIBits, 823, 1, sizeof(GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_BSIBits), X86::GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_BSIRegClassID, 1, true },
  { GR64_with_sub_32bit_in_GR32_AD_and_GR32_DC, GR64_with_sub_32bit_in_GR32_AD_and_GR32_DCBits, 302, 1, sizeof(GR64_with_sub_32bit_in_GR32_AD_and_GR32_DCBits), X86::GR64_with_sub_32bit_in_GR32_AD_and_GR32_DCRegClassID, 1, true },
  { GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_DIBP, GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_DIBPBits, 993, 1, sizeof(GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_DIBPBits), X86::GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_DIBPRegClassID, 1, true },
  { GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_TC, GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_TCBits, 421, 1, sizeof(GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_TCBits), X86::GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_TCRegClassID, 1, true },
  { GR64_with_sub_32bit_in_GR32_BSI_and_GR32_SIDI, GR64_with_sub_32bit_in_GR32_BSI_and_GR32_SIDIBits, 697, 1, sizeof(GR64_with_sub_32bit_in_GR32_BSI_and_GR32_SIDIBits), X86::GR64_with_sub_32bit_in_GR32_BSI_and_GR32_SIDIRegClassID, 1, true },
  { GR64_with_sub_32bit_in_GR32_CB_and_GR32_DC, GR64_with_sub_32bit_in_GR32_CB_and_GR32_DCBits, 259, 1, sizeof(GR64_with_sub_32bit_in_GR32_CB_and_GR32_DCBits), X86::GR64_with_sub_32bit_in_GR32_CB_and_GR32_DCRegClassID, 1, true },
  { GR64_with_sub_32bit_in_GR32_DIBP_and_GR32_SIDI, GR64_with_sub_32bit_in_GR32_DIBP_and_GR32_SIDIBits, 743, 1, sizeof(GR64_with_sub_32bit_in_GR32_DIBP_and_GR32_SIDIBits), X86::GR64_with_sub_32bit_in_GR32_DIBP_and_GR32_SIDIRegClassID, 1, true },
  { RST, RSTBits, 1233, 8, sizeof(RSTBits), X86::RSTRegClassID, 1, false },
  { RFP80, RFP80Bits, 0, 7, sizeof(RFP80Bits), X86::RFP80RegClassID, 1, true },
  { RFP80_7, RFP80_7Bits, 201, 1, sizeof(RFP80_7Bits), X86::RFP80_7RegClassID, 1, false },
  { VR128X, VR128XBits, 1256, 32, sizeof(VR128XBits), X86::VR128XRegClassID, 1, true },
  { VR128, VR128Bits, 209, 16, sizeof(VR128Bits), X86::VR128RegClassID, 1, true },
  { VR128H, VR128HBits, 679, 8, sizeof(VR128HBits), X86::VR128HRegClassID, 1, true },
  { VR128L, VR128LBits, 930, 8, sizeof(VR128LBits), X86::VR128LRegClassID, 1, true },
  { BNDR, BNDRBits, 1201, 4, sizeof(BNDRBits), X86::BNDRRegClassID, 1, true },
  { VR256X, VR256XBits, 1249, 32, sizeof(VR256XBits), X86::VR256XRegClassID, 1, true },
  { VR256, VR256Bits, 195, 16, sizeof(VR256Bits), X86::VR256RegClassID, 1, true },
  { VR256H, VR256HBits, 650, 8, sizeof(VR256HBits), X86::VR256HRegClassID, 1, true },
  { VR256L, VR256LBits, 901, 8, sizeof(VR256LBits), X86::VR256LRegClassID, 1, true },
  { VR512, VR512Bits, 10, 32, sizeof(VR512Bits), X86::VR512RegClassID, 1, true },
  { VR512_with_sub_xmm_in_FR32, VR512_with_sub_xmm_in_FR32Bits, 27, 16, sizeof(VR512_with_sub_xmm_in_FR32Bits), X86::VR512_with_sub_xmm_in_FR32RegClassID, 1, true },
  { VR512_with_sub_xmm_in_VR128H, VR512_with_sub_xmm_in_VR128HBits, 657, 8, sizeof(VR512_with_sub_xmm_in_VR128HBits), X86::VR512_with_sub_xmm_in_VR128HRegClassID, 1, true },
  { VR512_with_sub_xmm_in_VR128L, VR512_with_sub_xmm_in_VR128LBits, 908, 8, sizeof(VR512_with_sub_xmm_in_VR128LBits), X86::VR512_with_sub_xmm_in_VR128LRegClassID, 1, true },
};

// X86 Dwarf<->LLVM register mappings.
extern const MCRegisterInfo::DwarfLLVMRegPair X86DwarfFlavour0Dwarf2L[] = {
  { 0U, X86::RAX },
  { 1U, X86::RDX },
  { 2U, X86::RCX },
  { 3U, X86::RBX },
  { 4U, X86::RSI },
  { 5U, X86::RDI },
  { 6U, X86::RBP },
  { 7U, X86::RSP },
  { 8U, X86::R8 },
  { 9U, X86::R9 },
  { 10U, X86::R10 },
  { 11U, X86::R11 },
  { 12U, X86::R12 },
  { 13U, X86::R13 },
  { 14U, X86::R14 },
  { 15U, X86::R15 },
  { 16U, X86::RIP },
  { 17U, X86::XMM0 },
  { 18U, X86::XMM1 },
  { 19U, X86::XMM2 },
  { 20U, X86::XMM3 },
  { 21U, X86::XMM4 },
  { 22U, X86::XMM5 },
  { 23U, X86::XMM6 },
  { 24U, X86::XMM7 },
  { 25U, X86::XMM8 },
  { 26U, X86::XMM9 },
  { 27U, X86::XMM10 },
  { 28U, X86::XMM11 },
  { 29U, X86::XMM12 },
  { 30U, X86::XMM13 },
  { 31U, X86::XMM14 },
  { 32U, X86::XMM15 },
  { 33U, X86::ST0 },
  { 34U, X86::ST1 },
  { 35U, X86::ST2 },
  { 36U, X86::ST3 },
  { 37U, X86::ST4 },
  { 38U, X86::ST5 },
  { 39U, X86::ST6 },
  { 40U, X86::ST7 },
  { 41U, X86::MM0 },
  { 42U, X86::MM1 },
  { 43U, X86::MM2 },
  { 44U, X86::MM3 },
  { 45U, X86::MM4 },
  { 46U, X86::MM5 },
  { 47U, X86::MM6 },
  { 48U, X86::MM7 },
  { 67U, X86::XMM16 },
  { 68U, X86::XMM17 },
  { 69U, X86::XMM18 },
  { 70U, X86::XMM19 },
  { 71U, X86::XMM20 },
  { 72U, X86::XMM21 },
  { 73U, X86::XMM22 },
  { 74U, X86::XMM23 },
  { 75U, X86::XMM24 },
  { 76U, X86::XMM25 },
  { 77U, X86::XMM26 },
  { 78U, X86::XMM27 },
  { 79U, X86::XMM28 },
  { 80U, X86::XMM29 },
  { 81U, X86::XMM30 },
  { 82U, X86::XMM31 },
  { 118U, X86::K0 },
  { 119U, X86::K1 },
  { 120U, X86::K2 },
  { 121U, X86::K3 },
  { 122U, X86::K4 },
  { 123U, X86::K5 },
  { 124U, X86::K6 },
  { 125U, X86::K7 },
};
extern const unsigned X86DwarfFlavour0Dwarf2LSize = array_lengthof(X86DwarfFlavour0Dwarf2L);

extern const MCRegisterInfo::DwarfLLVMRegPair X86DwarfFlavour1Dwarf2L[] = {
  { 0U, X86::EAX },
  { 1U, X86::ECX },
  { 2U, X86::EDX },
  { 3U, X86::EBX },
  { 4U, X86::EBP },
  { 5U, X86::ESP },
  { 6U, X86::ESI },
  { 7U, X86::EDI },
  { 8U, X86::EIP },
  { 12U, X86::ST0 },
  { 13U, X86::ST1 },
  { 14U, X86::ST2 },
  { 15U, X86::ST3 },
  { 16U, X86::ST4 },
  { 17U, X86::ST5 },
  { 18U, X86::ST6 },
  { 19U, X86::ST7 },
  { 21U, X86::XMM0 },
  { 22U, X86::XMM1 },
  { 23U, X86::XMM2 },
  { 24U, X86::XMM3 },
  { 25U, X86::XMM4 },
  { 26U, X86::XMM5 },
  { 27U, X86::XMM6 },
  { 28U, X86::XMM7 },
  { 29U, X86::MM0 },
  { 30U, X86::MM1 },
  { 31U, X86::MM2 },
  { 32U, X86::MM3 },
  { 33U, X86::MM4 },
  { 34U, X86::MM5 },
  { 35U, X86::MM6 },
  { 36U, X86::MM7 },
  { 93U, X86::K0 },
  { 94U, X86::K1 },
  { 95U, X86::K2 },
  { 96U, X86::K3 },
  { 97U, X86::K4 },
  { 98U, X86::K5 },
  { 99U, X86::K6 },
  { 100U, X86::K7 },
};
extern const unsigned X86DwarfFlavour1Dwarf2LSize = array_lengthof(X86DwarfFlavour1Dwarf2L);

extern const MCRegisterInfo::DwarfLLVMRegPair X86DwarfFlavour2Dwarf2L[] = {
  { 0U, X86::EAX },
  { 1U, X86::ECX },
  { 2U, X86::EDX },
  { 3U, X86::EBX },
  { 4U, X86::ESP },
  { 5U, X86::EBP },
  { 6U, X86::ESI },
  { 7U, X86::EDI },
  { 8U, X86::EIP },
  { 11U, X86::ST0 },
  { 12U, X86::ST1 },
  { 13U, X86::ST2 },
  { 14U, X86::ST3 },
  { 15U, X86::ST4 },
  { 16U, X86::ST5 },
  { 17U, X86::ST6 },
  { 18U, X86::ST7 },
  { 21U, X86::XMM0 },
  { 22U, X86::XMM1 },
  { 23U, X86::XMM2 },
  { 24U, X86::XMM3 },
  { 25U, X86::XMM4 },
  { 26U, X86::XMM5 },
  { 27U, X86::XMM6 },
  { 28U, X86::XMM7 },
  { 29U, X86::MM0 },
  { 30U, X86::MM1 },
  { 31U, X86::MM2 },
  { 32U, X86::MM3 },
  { 33U, X86::MM4 },
  { 34U, X86::MM5 },
  { 35U, X86::MM6 },
  { 36U, X86::MM7 },
  { 93U, X86::K0 },
  { 94U, X86::K1 },
  { 95U, X86::K2 },
  { 96U, X86::K3 },
  { 97U, X86::K4 },
  { 98U, X86::K5 },
  { 99U, X86::K6 },
  { 100U, X86::K7 },
};
extern const unsigned X86DwarfFlavour2Dwarf2LSize = array_lengthof(X86DwarfFlavour2Dwarf2L);

extern const MCRegisterInfo::DwarfLLVMRegPair X86EHFlavour0Dwarf2L[] = {
  { 0U, X86::RAX },
  { 1U, X86::RDX },
  { 2U, X86::RCX },
  { 3U, X86::RBX },
  { 4U, X86::RSI },
  { 5U, X86::RDI },
  { 6U, X86::RBP },
  { 7U, X86::RSP },
  { 8U, X86::R8 },
  { 9U, X86::R9 },
  { 10U, X86::R10 },
  { 11U, X86::R11 },
  { 12U, X86::R12 },
  { 13U, X86::R13 },
  { 14U, X86::R14 },
  { 15U, X86::R15 },
  { 16U, X86::RIP },
  { 17U, X86::XMM0 },
  { 18U, X86::XMM1 },
  { 19U, X86::XMM2 },
  { 20U, X86::XMM3 },
  { 21U, X86::XMM4 },
  { 22U, X86::XMM5 },
  { 23U, X86::XMM6 },
  { 24U, X86::XMM7 },
  { 25U, X86::XMM8 },
  { 26U, X86::XMM9 },
  { 27U, X86::XMM10 },
  { 28U, X86::XMM11 },
  { 29U, X86::XMM12 },
  { 30U, X86::XMM13 },
  { 31U, X86::XMM14 },
  { 32U, X86::XMM15 },
  { 33U, X86::ST0 },
  { 34U, X86::ST1 },
  { 35U, X86::ST2 },
  { 36U, X86::ST3 },
  { 37U, X86::ST4 },
  { 38U, X86::ST5 },
  { 39U, X86::ST6 },
  { 40U, X86::ST7 },
  { 41U, X86::MM0 },
  { 42U, X86::MM1 },
  { 43U, X86::MM2 },
  { 44U, X86::MM3 },
  { 45U, X86::MM4 },
  { 46U, X86::MM5 },
  { 47U, X86::MM6 },
  { 48U, X86::MM7 },
  { 67U, X86::XMM16 },
  { 68U, X86::XMM17 },
  { 69U, X86::XMM18 },
  { 70U, X86::XMM19 },
  { 71U, X86::XMM20 },
  { 72U, X86::XMM21 },
  { 73U, X86::XMM22 },
  { 74U, X86::XMM23 },
  { 75U, X86::XMM24 },
  { 76U, X86::XMM25 },
  { 77U, X86::XMM26 },
  { 78U, X86::XMM27 },
  { 79U, X86::XMM28 },
  { 80U, X86::XMM29 },
  { 81U, X86::XMM30 },
  { 82U, X86::XMM31 },
  { 118U, X86::K0 },
  { 119U, X86::K1 },
  { 120U, X86::K2 },
  { 121U, X86::K3 },
  { 122U, X86::K4 },
  { 123U, X86::K5 },
  { 124U, X86::K6 },
  { 125U, X86::K7 },
};
extern const unsigned X86EHFlavour0Dwarf2LSize = array_lengthof(X86EHFlavour0Dwarf2L);

extern const MCRegisterInfo::DwarfLLVMRegPair X86EHFlavour1Dwarf2L[] = {
  { 0U, X86::EAX },
  { 1U, X86::ECX },
  { 2U, X86::EDX },
  { 3U, X86::EBX },
  { 4U, X86::EBP },
  { 5U, X86::ESP },
  { 6U, X86::ESI },
  { 7U, X86::EDI },
  { 8U, X86::EIP },
  { 12U, X86::ST0 },
  { 13U, X86::ST1 },
  { 14U, X86::ST2 },
  { 15U, X86::ST3 },
  { 16U, X86::ST4 },
  { 17U, X86::ST5 },
  { 18U, X86::ST6 },
  { 19U, X86::ST7 },
  { 21U, X86::XMM0 },
  { 22U, X86::XMM1 },
  { 23U, X86::XMM2 },
  { 24U, X86::XMM3 },
  { 25U, X86::XMM4 },
  { 26U, X86::XMM5 },
  { 27U, X86::XMM6 },
  { 28U, X86::XMM7 },
  { 29U, X86::MM0 },
  { 30U, X86::MM1 },
  { 31U, X86::MM2 },
  { 32U, X86::MM3 },
  { 33U, X86::MM4 },
  { 34U, X86::MM5 },
  { 35U, X86::MM6 },
  { 36U, X86::MM7 },
  { 93U, X86::K0 },
  { 94U, X86::K1 },
  { 95U, X86::K2 },
  { 96U, X86::K3 },
  { 97U, X86::K4 },
  { 98U, X86::K5 },
  { 99U, X86::K6 },
  { 100U, X86::K7 },
};
extern const unsigned X86EHFlavour1Dwarf2LSize = array_lengthof(X86EHFlavour1Dwarf2L);

extern const MCRegisterInfo::DwarfLLVMRegPair X86EHFlavour2Dwarf2L[] = {
  { 0U, X86::EAX },
  { 1U, X86::ECX },
  { 2U, X86::EDX },
  { 3U, X86::EBX },
  { 4U, X86::ESP },
  { 5U, X86::EBP },
  { 6U, X86::ESI },
  { 7U, X86::EDI },
  { 8U, X86::EIP },
  { 11U, X86::ST0 },
  { 12U, X86::ST1 },
  { 13U, X86::ST2 },
  { 14U, X86::ST3 },
  { 15U, X86::ST4 },
  { 16U, X86::ST5 },
  { 17U, X86::ST6 },
  { 18U, X86::ST7 },
  { 21U, X86::XMM0 },
  { 22U, X86::XMM1 },
  { 23U, X86::XMM2 },
  { 24U, X86::XMM3 },
  { 25U, X86::XMM4 },
  { 26U, X86::XMM5 },
  { 27U, X86::XMM6 },
  { 28U, X86::XMM7 },
  { 29U, X86::MM0 },
  { 30U, X86::MM1 },
  { 31U, X86::MM2 },
  { 32U, X86::MM3 },
  { 33U, X86::MM4 },
  { 34U, X86::MM5 },
  { 35U, X86::MM6 },
  { 36U, X86::MM7 },
  { 93U, X86::K0 },
  { 94U, X86::K1 },
  { 95U, X86::K2 },
  { 96U, X86::K3 },
  { 97U, X86::K4 },
  { 98U, X86::K5 },
  { 99U, X86::K6 },
  { 100U, X86::K7 },
};
extern const unsigned X86EHFlavour2Dwarf2LSize = array_lengthof(X86EHFlavour2Dwarf2L);

extern const MCRegisterInfo::DwarfLLVMRegPair X86DwarfFlavour0L2Dwarf[] = {
  { X86::EAX, -2U },
  { X86::EBP, -2U },
  { X86::EBX, -2U },
  { X86::ECX, -2U },
  { X86::EDI, -2U },
  { X86::EDX, -2U },
  { X86::EIP, -2U },
  { X86::ESI, -2U },
  { X86::ESP, -2U },
  { X86::RAX, 0U },
  { X86::RBP, 6U },
  { X86::RBX, 3U },
  { X86::RCX, 2U },
  { X86::RDI, 5U },
  { X86::RDX, 1U },
  { X86::RIP, 16U },
  { X86::RSI, 4U },
  { X86::RSP, 7U },
  { X86::K0, 118U },
  { X86::K1, 119U },
  { X86::K2, 120U },
  { X86::K3, 121U },
  { X86::K4, 122U },
  { X86::K5, 123U },
  { X86::K6, 124U },
  { X86::K7, 125U },
  { X86::MM0, 41U },
  { X86::MM1, 42U },
  { X86::MM2, 43U },
  { X86::MM3, 44U },
  { X86::MM4, 45U },
  { X86::MM5, 46U },
  { X86::MM6, 47U },
  { X86::MM7, 48U },
  { X86::R8, 8U },
  { X86::R9, 9U },
  { X86::R10, 10U },
  { X86::R11, 11U },
  { X86::R12, 12U },
  { X86::R13, 13U },
  { X86::R14, 14U },
  { X86::R15, 15U },
  { X86::ST0, 33U },
  { X86::ST1, 34U },
  { X86::ST2, 35U },
  { X86::ST3, 36U },
  { X86::ST4, 37U },
  { X86::ST5, 38U },
  { X86::ST6, 39U },
  { X86::ST7, 40U },
  { X86::XMM0, 17U },
  { X86::XMM1, 18U },
  { X86::XMM2, 19U },
  { X86::XMM3, 20U },
  { X86::XMM4, 21U },
  { X86::XMM5, 22U },
  { X86::XMM6, 23U },
  { X86::XMM7, 24U },
  { X86::XMM8, 25U },
  { X86::XMM9, 26U },
  { X86::XMM10, 27U },
  { X86::XMM11, 28U },
  { X86::XMM12, 29U },
  { X86::XMM13, 30U },
  { X86::XMM14, 31U },
  { X86::XMM15, 32U },
  { X86::XMM16, 67U },
  { X86::XMM17, 68U },
  { X86::XMM18, 69U },
  { X86::XMM19, 70U },
  { X86::XMM20, 71U },
  { X86::XMM21, 72U },
  { X86::XMM22, 73U },
  { X86::XMM23, 74U },
  { X86::XMM24, 75U },
  { X86::XMM25, 76U },
  { X86::XMM26, 77U },
  { X86::XMM27, 78U },
  { X86::XMM28, 79U },
  { X86::XMM29, 80U },
  { X86::XMM30, 81U },
  { X86::XMM31, 82U },
  { X86::YMM0, 17U },
  { X86::YMM1, 18U },
  { X86::YMM2, 19U },
  { X86::YMM3, 20U },
  { X86::YMM4, 21U },
  { X86::YMM5, 22U },
  { X86::YMM6, 23U },
  { X86::YMM7, 24U },
  { X86::YMM8, 25U },
  { X86::YMM9, 26U },
  { X86::YMM10, 27U },
  { X86::YMM11, 28U },
  { X86::YMM12, 29U },
  { X86::YMM13, 30U },
  { X86::YMM14, 31U },
  { X86::YMM15, 32U },
  { X86::YMM16, 67U },
  { X86::YMM17, 68U },
  { X86::YMM18, 69U },
  { X86::YMM19, 70U },
  { X86::YMM20, 71U },
  { X86::YMM21, 72U },
  { X86::YMM22, 73U },
  { X86::YMM23, 74U },
  { X86::YMM24, 75U },
  { X86::YMM25, 76U },
  { X86::YMM26, 77U },
  { X86::YMM27, 78U },
  { X86::YMM28, 79U },
  { X86::YMM29, 80U },
  { X86::YMM30, 81U },
  { X86::YMM31, 82U },
  { X86::ZMM0, 17U },
  { X86::ZMM1, 18U },
  { X86::ZMM2, 19U },
  { X86::ZMM3, 20U },
  { X86::ZMM4, 21U },
  { X86::ZMM5, 22U },
  { X86::ZMM6, 23U },
  { X86::ZMM7, 24U },
  { X86::ZMM8, 25U },
  { X86::ZMM9, 26U },
  { X86::ZMM10, 27U },
  { X86::ZMM11, 28U },
  { X86::ZMM12, 29U },
  { X86::ZMM13, 30U },
  { X86::ZMM14, 31U },
  { X86::ZMM15, 32U },
  { X86::ZMM16, 67U },
  { X86::ZMM17, 68U },
  { X86::ZMM18, 69U },
  { X86::ZMM19, 70U },
  { X86::ZMM20, 71U },
  { X86::ZMM21, 72U },
  { X86::ZMM22, 73U },
  { X86::ZMM23, 74U },
  { X86::ZMM24, 75U },
  { X86::ZMM25, 76U },
  { X86::ZMM26, 77U },
  { X86::ZMM27, 78U },
  { X86::ZMM28, 79U },
  { X86::ZMM29, 80U },
  { X86::ZMM30, 81U },
  { X86::ZMM31, 82U },
};
extern const unsigned X86DwarfFlavour0L2DwarfSize = array_lengthof(X86DwarfFlavour0L2Dwarf);

extern const MCRegisterInfo::DwarfLLVMRegPair X86DwarfFlavour1L2Dwarf[] = {
  { X86::EAX, 0U },
  { X86::EBP, 4U },
  { X86::EBX, 3U },
  { X86::ECX, 1U },
  { X86::EDI, 7U },
  { X86::EDX, 2U },
  { X86::EIP, 8U },
  { X86::ESI, 6U },
  { X86::ESP, 5U },
  { X86::RAX, -2U },
  { X86::RBP, -2U },
  { X86::RBX, -2U },
  { X86::RCX, -2U },
  { X86::RDI, -2U },
  { X86::RDX, -2U },
  { X86::RIP, -2U },
  { X86::RSI, -2U },
  { X86::RSP, -2U },
  { X86::K0, 93U },
  { X86::K1, 94U },
  { X86::K2, 95U },
  { X86::K3, 96U },
  { X86::K4, 97U },
  { X86::K5, 98U },
  { X86::K6, 99U },
  { X86::K7, 100U },
  { X86::MM0, 29U },
  { X86::MM1, 30U },
  { X86::MM2, 31U },
  { X86::MM3, 32U },
  { X86::MM4, 33U },
  { X86::MM5, 34U },
  { X86::MM6, 35U },
  { X86::MM7, 36U },
  { X86::R8, -2U },
  { X86::R9, -2U },
  { X86::R10, -2U },
  { X86::R11, -2U },
  { X86::R12, -2U },
  { X86::R13, -2U },
  { X86::R14, -2U },
  { X86::R15, -2U },
  { X86::ST0, 12U },
  { X86::ST1, 13U },
  { X86::ST2, 14U },
  { X86::ST3, 15U },
  { X86::ST4, 16U },
  { X86::ST5, 17U },
  { X86::ST6, 18U },
  { X86::ST7, 19U },
  { X86::XMM0, 21U },
  { X86::XMM1, 22U },
  { X86::XMM2, 23U },
  { X86::XMM3, 24U },
  { X86::XMM4, 25U },
  { X86::XMM5, 26U },
  { X86::XMM6, 27U },
  { X86::XMM7, 28U },
  { X86::XMM8, -2U },
  { X86::XMM9, -2U },
  { X86::XMM10, -2U },
  { X86::XMM11, -2U },
  { X86::XMM12, -2U },
  { X86::XMM13, -2U },
  { X86::XMM14, -2U },
  { X86::XMM15, -2U },
  { X86::XMM16, -2U },
  { X86::XMM17, -2U },
  { X86::XMM18, -2U },
  { X86::XMM19, -2U },
  { X86::XMM20, -2U },
  { X86::XMM21, -2U },
  { X86::XMM22, -2U },
  { X86::XMM23, -2U },
  { X86::XMM24, -2U },
  { X86::XMM25, -2U },
  { X86::XMM26, -2U },
  { X86::XMM27, -2U },
  { X86::XMM28, -2U },
  { X86::XMM29, -2U },
  { X86::XMM30, -2U },
  { X86::XMM31, -2U },
  { X86::YMM0, 21U },
  { X86::YMM1, 22U },
  { X86::YMM2, 23U },
  { X86::YMM3, 24U },
  { X86::YMM4, 25U },
  { X86::YMM5, 26U },
  { X86::YMM6, 27U },
  { X86::YMM7, 28U },
  { X86::YMM8, -2U },
  { X86::YMM9, -2U },
  { X86::YMM10, -2U },
  { X86::YMM11, -2U },
  { X86::YMM12, -2U },
  { X86::YMM13, -2U },
  { X86::YMM14, -2U },
  { X86::YMM15, -2U },
  { X86::YMM16, -2U },
  { X86::YMM17, -2U },
  { X86::YMM18, -2U },
  { X86::YMM19, -2U },
  { X86::YMM20, -2U },
  { X86::YMM21, -2U },
  { X86::YMM22, -2U },
  { X86::YMM23, -2U },
  { X86::YMM24, -2U },
  { X86::YMM25, -2U },
  { X86::YMM26, -2U },
  { X86::YMM27, -2U },
  { X86::YMM28, -2U },
  { X86::YMM29, -2U },
  { X86::YMM30, -2U },
  { X86::YMM31, -2U },
  { X86::ZMM0, 21U },
  { X86::ZMM1, 22U },
  { X86::ZMM2, 23U },
  { X86::ZMM3, 24U },
  { X86::ZMM4, 25U },
  { X86::ZMM5, 26U },
  { X86::ZMM6, 27U },
  { X86::ZMM7, 28U },
  { X86::ZMM8, -2U },
  { X86::ZMM9, -2U },
  { X86::ZMM10, -2U },
  { X86::ZMM11, -2U },
  { X86::ZMM12, -2U },
  { X86::ZMM13, -2U },
  { X86::ZMM14, -2U },
  { X86::ZMM15, -2U },
  { X86::ZMM16, -2U },
  { X86::ZMM17, -2U },
  { X86::ZMM18, -2U },
  { X86::ZMM19, -2U },
  { X86::ZMM20, -2U },
  { X86::ZMM21, -2U },
  { X86::ZMM22, -2U },
  { X86::ZMM23, -2U },
  { X86::ZMM24, -2U },
  { X86::ZMM25, -2U },
  { X86::ZMM26, -2U },
  { X86::ZMM27, -2U },
  { X86::ZMM28, -2U },
  { X86::ZMM29, -2U },
  { X86::ZMM30, -2U },
  { X86::ZMM31, -2U },
};
extern const unsigned X86DwarfFlavour1L2DwarfSize = array_lengthof(X86DwarfFlavour1L2Dwarf);

extern const MCRegisterInfo::DwarfLLVMRegPair X86DwarfFlavour2L2Dwarf[] = {
  { X86::EAX, 0U },
  { X86::EBP, 5U },
  { X86::EBX, 3U },
  { X86::ECX, 1U },
  { X86::EDI, 7U },
  { X86::EDX, 2U },
  { X86::EIP, 8U },
  { X86::ESI, 6U },
  { X86::ESP, 4U },
  { X86::RAX, -2U },
  { X86::RBP, -2U },
  { X86::RBX, -2U },
  { X86::RCX, -2U },
  { X86::RDI, -2U },
  { X86::RDX, -2U },
  { X86::RIP, -2U },
  { X86::RSI, -2U },
  { X86::RSP, -2U },
  { X86::K0, 93U },
  { X86::K1, 94U },
  { X86::K2, 95U },
  { X86::K3, 96U },
  { X86::K4, 97U },
  { X86::K5, 98U },
  { X86::K6, 99U },
  { X86::K7, 100U },
  { X86::MM0, 29U },
  { X86::MM1, 30U },
  { X86::MM2, 31U },
  { X86::MM3, 32U },
  { X86::MM4, 33U },
  { X86::MM5, 34U },
  { X86::MM6, 35U },
  { X86::MM7, 36U },
  { X86::R8, -2U },
  { X86::R9, -2U },
  { X86::R10, -2U },
  { X86::R11, -2U },
  { X86::R12, -2U },
  { X86::R13, -2U },
  { X86::R14, -2U },
  { X86::R15, -2U },
  { X86::ST0, 11U },
  { X86::ST1, 12U },
  { X86::ST2, 13U },
  { X86::ST3, 14U },
  { X86::ST4, 15U },
  { X86::ST5, 16U },
  { X86::ST6, 17U },
  { X86::ST7, 18U },
  { X86::XMM0, 21U },
  { X86::XMM1, 22U },
  { X86::XMM2, 23U },
  { X86::XMM3, 24U },
  { X86::XMM4, 25U },
  { X86::XMM5, 26U },
  { X86::XMM6, 27U },
  { X86::XMM7, 28U },
  { X86::XMM8, -2U },
  { X86::XMM9, -2U },
  { X86::XMM10, -2U },
  { X86::XMM11, -2U },
  { X86::XMM12, -2U },
  { X86::XMM13, -2U },
  { X86::XMM14, -2U },
  { X86::XMM15, -2U },
  { X86::XMM16, -2U },
  { X86::XMM17, -2U },
  { X86::XMM18, -2U },
  { X86::XMM19, -2U },
  { X86::XMM20, -2U },
  { X86::XMM21, -2U },
  { X86::XMM22, -2U },
  { X86::XMM23, -2U },
  { X86::XMM24, -2U },
  { X86::XMM25, -2U },
  { X86::XMM26, -2U },
  { X86::XMM27, -2U },
  { X86::XMM28, -2U },
  { X86::XMM29, -2U },
  { X86::XMM30, -2U },
  { X86::XMM31, -2U },
  { X86::YMM0, 21U },
  { X86::YMM1, 22U },
  { X86::YMM2, 23U },
  { X86::YMM3, 24U },
  { X86::YMM4, 25U },
  { X86::YMM5, 26U },
  { X86::YMM6, 27U },
  { X86::YMM7, 28U },
  { X86::YMM8, -2U },
  { X86::YMM9, -2U },
  { X86::YMM10, -2U },
  { X86::YMM11, -2U },
  { X86::YMM12, -2U },
  { X86::YMM13, -2U },
  { X86::YMM14, -2U },
  { X86::YMM15, -2U },
  { X86::YMM16, -2U },
  { X86::YMM17, -2U },
  { X86::YMM18, -2U },
  { X86::YMM19, -2U },
  { X86::YMM20, -2U },
  { X86::YMM21, -2U },
  { X86::YMM22, -2U },
  { X86::YMM23, -2U },
  { X86::YMM24, -2U },
  { X86::YMM25, -2U },
  { X86::YMM26, -2U },
  { X86::YMM27, -2U },
  { X86::YMM28, -2U },
  { X86::YMM29, -2U },
  { X86::YMM30, -2U },
  { X86::YMM31, -2U },
  { X86::ZMM0, 21U },
  { X86::ZMM1, 22U },
  { X86::ZMM2, 23U },
  { X86::ZMM3, 24U },
  { X86::ZMM4, 25U },
  { X86::ZMM5, 26U },
  { X86::ZMM6, 27U },
  { X86::ZMM7, 28U },
  { X86::ZMM8, -2U },
  { X86::ZMM9, -2U },
  { X86::ZMM10, -2U },
  { X86::ZMM11, -2U },
  { X86::ZMM12, -2U },
  { X86::ZMM13, -2U },
  { X86::ZMM14, -2U },
  { X86::ZMM15, -2U },
  { X86::ZMM16, -2U },
  { X86::ZMM17, -2U },
  { X86::ZMM18, -2U },
  { X86::ZMM19, -2U },
  { X86::ZMM20, -2U },
  { X86::ZMM21, -2U },
  { X86::ZMM22, -2U },
  { X86::ZMM23, -2U },
  { X86::ZMM24, -2U },
  { X86::ZMM25, -2U },
  { X86::ZMM26, -2U },
  { X86::ZMM27, -2U },
  { X86::ZMM28, -2U },
  { X86::ZMM29, -2U },
  { X86::ZMM30, -2U },
  { X86::ZMM31, -2U },
};
extern const unsigned X86DwarfFlavour2L2DwarfSize = array_lengthof(X86DwarfFlavour2L2Dwarf);

extern const MCRegisterInfo::DwarfLLVMRegPair X86EHFlavour0L2Dwarf[] = {
  { X86::EAX, -2U },
  { X86::EBP, -2U },
  { X86::EBX, -2U },
  { X86::ECX, -2U },
  { X86::EDI, -2U },
  { X86::EDX, -2U },
  { X86::EIP, -2U },
  { X86::ESI, -2U },
  { X86::ESP, -2U },
  { X86::RAX, 0U },
  { X86::RBP, 6U },
  { X86::RBX, 3U },
  { X86::RCX, 2U },
  { X86::RDI, 5U },
  { X86::RDX, 1U },
  { X86::RIP, 16U },
  { X86::RSI, 4U },
  { X86::RSP, 7U },
  { X86::K0, 118U },
  { X86::K1, 119U },
  { X86::K2, 120U },
  { X86::K3, 121U },
  { X86::K4, 122U },
  { X86::K5, 123U },
  { X86::K6, 124U },
  { X86::K7, 125U },
  { X86::MM0, 41U },
  { X86::MM1, 42U },
  { X86::MM2, 43U },
  { X86::MM3, 44U },
  { X86::MM4, 45U },
  { X86::MM5, 46U },
  { X86::MM6, 47U },
  { X86::MM7, 48U },
  { X86::R8, 8U },
  { X86::R9, 9U },
  { X86::R10, 10U },
  { X86::R11, 11U },
  { X86::R12, 12U },
  { X86::R13, 13U },
  { X86::R14, 14U },
  { X86::R15, 15U },
  { X86::ST0, 33U },
  { X86::ST1, 34U },
  { X86::ST2, 35U },
  { X86::ST3, 36U },
  { X86::ST4, 37U },
  { X86::ST5, 38U },
  { X86::ST6, 39U },
  { X86::ST7, 40U },
  { X86::XMM0, 17U },
  { X86::XMM1, 18U },
  { X86::XMM2, 19U },
  { X86::XMM3, 20U },
  { X86::XMM4, 21U },
  { X86::XMM5, 22U },
  { X86::XMM6, 23U },
  { X86::XMM7, 24U },
  { X86::XMM8, 25U },
  { X86::XMM9, 26U },
  { X86::XMM10, 27U },
  { X86::XMM11, 28U },
  { X86::XMM12, 29U },
  { X86::XMM13, 30U },
  { X86::XMM14, 31U },
  { X86::XMM15, 32U },
  { X86::XMM16, 67U },
  { X86::XMM17, 68U },
  { X86::XMM18, 69U },
  { X86::XMM19, 70U },
  { X86::XMM20, 71U },
  { X86::XMM21, 72U },
  { X86::XMM22, 73U },
  { X86::XMM23, 74U },
  { X86::XMM24, 75U },
  { X86::XMM25, 76U },
  { X86::XMM26, 77U },
  { X86::XMM27, 78U },
  { X86::XMM28, 79U },
  { X86::XMM29, 80U },
  { X86::XMM30, 81U },
  { X86::XMM31, 82U },
  { X86::YMM0, 17U },
  { X86::YMM1, 18U },
  { X86::YMM2, 19U },
  { X86::YMM3, 20U },
  { X86::YMM4, 21U },
  { X86::YMM5, 22U },
  { X86::YMM6, 23U },
  { X86::YMM7, 24U },
  { X86::YMM8, 25U },
  { X86::YMM9, 26U },
  { X86::YMM10, 27U },
  { X86::YMM11, 28U },
  { X86::YMM12, 29U },
  { X86::YMM13, 30U },
  { X86::YMM14, 31U },
  { X86::YMM15, 32U },
  { X86::YMM16, 67U },
  { X86::YMM17, 68U },
  { X86::YMM18, 69U },
  { X86::YMM19, 70U },
  { X86::YMM20, 71U },
  { X86::YMM21, 72U },
  { X86::YMM22, 73U },
  { X86::YMM23, 74U },
  { X86::YMM24, 75U },
  { X86::YMM25, 76U },
  { X86::YMM26, 77U },
  { X86::YMM27, 78U },
  { X86::YMM28, 79U },
  { X86::YMM29, 80U },
  { X86::YMM30, 81U },
  { X86::YMM31, 82U },
  { X86::ZMM0, 17U },
  { X86::ZMM1, 18U },
  { X86::ZMM2, 19U },
  { X86::ZMM3, 20U },
  { X86::ZMM4, 21U },
  { X86::ZMM5, 22U },
  { X86::ZMM6, 23U },
  { X86::ZMM7, 24U },
  { X86::ZMM8, 25U },
  { X86::ZMM9, 26U },
  { X86::ZMM10, 27U },
  { X86::ZMM11, 28U },
  { X86::ZMM12, 29U },
  { X86::ZMM13, 30U },
  { X86::ZMM14, 31U },
  { X86::ZMM15, 32U },
  { X86::ZMM16, 67U },
  { X86::ZMM17, 68U },
  { X86::ZMM18, 69U },
  { X86::ZMM19, 70U },
  { X86::ZMM20, 71U },
  { X86::ZMM21, 72U },
  { X86::ZMM22, 73U },
  { X86::ZMM23, 74U },
  { X86::ZMM24, 75U },
  { X86::ZMM25, 76U },
  { X86::ZMM26, 77U },
  { X86::ZMM27, 78U },
  { X86::ZMM28, 79U },
  { X86::ZMM29, 80U },
  { X86::ZMM30, 81U },
  { X86::ZMM31, 82U },
};
extern const unsigned X86EHFlavour0L2DwarfSize = array_lengthof(X86EHFlavour0L2Dwarf);

extern const MCRegisterInfo::DwarfLLVMRegPair X86EHFlavour1L2Dwarf[] = {
  { X86::EAX, 0U },
  { X86::EBP, 4U },
  { X86::EBX, 3U },
  { X86::ECX, 1U },
  { X86::EDI, 7U },
  { X86::EDX, 2U },
  { X86::EIP, 8U },
  { X86::ESI, 6U },
  { X86::ESP, 5U },
  { X86::RAX, -2U },
  { X86::RBP, -2U },
  { X86::RBX, -2U },
  { X86::RCX, -2U },
  { X86::RDI, -2U },
  { X86::RDX, -2U },
  { X86::RIP, -2U },
  { X86::RSI, -2U },
  { X86::RSP, -2U },
  { X86::K0, 93U },
  { X86::K1, 94U },
  { X86::K2, 95U },
  { X86::K3, 96U },
  { X86::K4, 97U },
  { X86::K5, 98U },
  { X86::K6, 99U },
  { X86::K7, 100U },
  { X86::MM0, 29U },
  { X86::MM1, 30U },
  { X86::MM2, 31U },
  { X86::MM3, 32U },
  { X86::MM4, 33U },
  { X86::MM5, 34U },
  { X86::MM6, 35U },
  { X86::MM7, 36U },
  { X86::R8, -2U },
  { X86::R9, -2U },
  { X86::R10, -2U },
  { X86::R11, -2U },
  { X86::R12, -2U },
  { X86::R13, -2U },
  { X86::R14, -2U },
  { X86::R15, -2U },
  { X86::ST0, 12U },
  { X86::ST1, 13U },
  { X86::ST2, 14U },
  { X86::ST3, 15U },
  { X86::ST4, 16U },
  { X86::ST5, 17U },
  { X86::ST6, 18U },
  { X86::ST7, 19U },
  { X86::XMM0, 21U },
  { X86::XMM1, 22U },
  { X86::XMM2, 23U },
  { X86::XMM3, 24U },
  { X86::XMM4, 25U },
  { X86::XMM5, 26U },
  { X86::XMM6, 27U },
  { X86::XMM7, 28U },
  { X86::XMM8, -2U },
  { X86::XMM9, -2U },
  { X86::XMM10, -2U },
  { X86::XMM11, -2U },
  { X86::XMM12, -2U },
  { X86::XMM13, -2U },
  { X86::XMM14, -2U },
  { X86::XMM15, -2U },
  { X86::XMM16, -2U },
  { X86::XMM17, -2U },
  { X86::XMM18, -2U },
  { X86::XMM19, -2U },
  { X86::XMM20, -2U },
  { X86::XMM21, -2U },
  { X86::XMM22, -2U },
  { X86::XMM23, -2U },
  { X86::XMM24, -2U },
  { X86::XMM25, -2U },
  { X86::XMM26, -2U },
  { X86::XMM27, -2U },
  { X86::XMM28, -2U },
  { X86::XMM29, -2U },
  { X86::XMM30, -2U },
  { X86::XMM31, -2U },
  { X86::YMM0, 21U },
  { X86::YMM1, 22U },
  { X86::YMM2, 23U },
  { X86::YMM3, 24U },
  { X86::YMM4, 25U },
  { X86::YMM5, 26U },
  { X86::YMM6, 27U },
  { X86::YMM7, 28U },
  { X86::YMM8, -2U },
  { X86::YMM9, -2U },
  { X86::YMM10, -2U },
  { X86::YMM11, -2U },
  { X86::YMM12, -2U },
  { X86::YMM13, -2U },
  { X86::YMM14, -2U },
  { X86::YMM15, -2U },
  { X86::YMM16, -2U },
  { X86::YMM17, -2U },
  { X86::YMM18, -2U },
  { X86::YMM19, -2U },
  { X86::YMM20, -2U },
  { X86::YMM21, -2U },
  { X86::YMM22, -2U },
  { X86::YMM23, -2U },
  { X86::YMM24, -2U },
  { X86::YMM25, -2U },
  { X86::YMM26, -2U },
  { X86::YMM27, -2U },
  { X86::YMM28, -2U },
  { X86::YMM29, -2U },
  { X86::YMM30, -2U },
  { X86::YMM31, -2U },
  { X86::ZMM0, 21U },
  { X86::ZMM1, 22U },
  { X86::ZMM2, 23U },
  { X86::ZMM3, 24U },
  { X86::ZMM4, 25U },
  { X86::ZMM5, 26U },
  { X86::ZMM6, 27U },
  { X86::ZMM7, 28U },
  { X86::ZMM8, -2U },
  { X86::ZMM9, -2U },
  { X86::ZMM10, -2U },
  { X86::ZMM11, -2U },
  { X86::ZMM12, -2U },
  { X86::ZMM13, -2U },
  { X86::ZMM14, -2U },
  { X86::ZMM15, -2U },
  { X86::ZMM16, -2U },
  { X86::ZMM17, -2U },
  { X86::ZMM18, -2U },
  { X86::ZMM19, -2U },
  { X86::ZMM20, -2U },
  { X86::ZMM21, -2U },
  { X86::ZMM22, -2U },
  { X86::ZMM23, -2U },
  { X86::ZMM24, -2U },
  { X86::ZMM25, -2U },
  { X86::ZMM26, -2U },
  { X86::ZMM27, -2U },
  { X86::ZMM28, -2U },
  { X86::ZMM29, -2U },
  { X86::ZMM30, -2U },
  { X86::ZMM31, -2U },
};
extern const unsigned X86EHFlavour1L2DwarfSize = array_lengthof(X86EHFlavour1L2Dwarf);

extern const MCRegisterInfo::DwarfLLVMRegPair X86EHFlavour2L2Dwarf[] = {
  { X86::EAX, 0U },
  { X86::EBP, 5U },
  { X86::EBX, 3U },
  { X86::ECX, 1U },
  { X86::EDI, 7U },
  { X86::EDX, 2U },
  { X86::EIP, 8U },
  { X86::ESI, 6U },
  { X86::ESP, 4U },
  { X86::RAX, -2U },
  { X86::RBP, -2U },
  { X86::RBX, -2U },
  { X86::RCX, -2U },
  { X86::RDI, -2U },
  { X86::RDX, -2U },
  { X86::RIP, -2U },
  { X86::RSI, -2U },
  { X86::RSP, -2U },
  { X86::K0, 93U },
  { X86::K1, 94U },
  { X86::K2, 95U },
  { X86::K3, 96U },
  { X86::K4, 97U },
  { X86::K5, 98U },
  { X86::K6, 99U },
  { X86::K7, 100U },
  { X86::MM0, 29U },
  { X86::MM1, 30U },
  { X86::MM2, 31U },
  { X86::MM3, 32U },
  { X86::MM4, 33U },
  { X86::MM5, 34U },
  { X86::MM6, 35U },
  { X86::MM7, 36U },
  { X86::R8, -2U },
  { X86::R9, -2U },
  { X86::R10, -2U },
  { X86::R11, -2U },
  { X86::R12, -2U },
  { X86::R13, -2U },
  { X86::R14, -2U },
  { X86::R15, -2U },
  { X86::ST0, 11U },
  { X86::ST1, 12U },
  { X86::ST2, 13U },
  { X86::ST3, 14U },
  { X86::ST4, 15U },
  { X86::ST5, 16U },
  { X86::ST6, 17U },
  { X86::ST7, 18U },
  { X86::XMM0, 21U },
  { X86::XMM1, 22U },
  { X86::XMM2, 23U },
  { X86::XMM3, 24U },
  { X86::XMM4, 25U },
  { X86::XMM5, 26U },
  { X86::XMM6, 27U },
  { X86::XMM7, 28U },
  { X86::XMM8, -2U },
  { X86::XMM9, -2U },
  { X86::XMM10, -2U },
  { X86::XMM11, -2U },
  { X86::XMM12, -2U },
  { X86::XMM13, -2U },
  { X86::XMM14, -2U },
  { X86::XMM15, -2U },
  { X86::XMM16, -2U },
  { X86::XMM17, -2U },
  { X86::XMM18, -2U },
  { X86::XMM19, -2U },
  { X86::XMM20, -2U },
  { X86::XMM21, -2U },
  { X86::XMM22, -2U },
  { X86::XMM23, -2U },
  { X86::XMM24, -2U },
  { X86::XMM25, -2U },
  { X86::XMM26, -2U },
  { X86::XMM27, -2U },
  { X86::XMM28, -2U },
  { X86::XMM29, -2U },
  { X86::XMM30, -2U },
  { X86::XMM31, -2U },
  { X86::YMM0, 21U },
  { X86::YMM1, 22U },
  { X86::YMM2, 23U },
  { X86::YMM3, 24U },
  { X86::YMM4, 25U },
  { X86::YMM5, 26U },
  { X86::YMM6, 27U },
  { X86::YMM7, 28U },
  { X86::YMM8, -2U },
  { X86::YMM9, -2U },
  { X86::YMM10, -2U },
  { X86::YMM11, -2U },
  { X86::YMM12, -2U },
  { X86::YMM13, -2U },
  { X86::YMM14, -2U },
  { X86::YMM15, -2U },
  { X86::YMM16, -2U },
  { X86::YMM17, -2U },
  { X86::YMM18, -2U },
  { X86::YMM19, -2U },
  { X86::YMM20, -2U },
  { X86::YMM21, -2U },
  { X86::YMM22, -2U },
  { X86::YMM23, -2U },
  { X86::YMM24, -2U },
  { X86::YMM25, -2U },
  { X86::YMM26, -2U },
  { X86::YMM27, -2U },
  { X86::YMM28, -2U },
  { X86::YMM29, -2U },
  { X86::YMM30, -2U },
  { X86::YMM31, -2U },
  { X86::ZMM0, 21U },
  { X86::ZMM1, 22U },
  { X86::ZMM2, 23U },
  { X86::ZMM3, 24U },
  { X86::ZMM4, 25U },
  { X86::ZMM5, 26U },
  { X86::ZMM6, 27U },
  { X86::ZMM7, 28U },
  { X86::ZMM8, -2U },
  { X86::ZMM9, -2U },
  { X86::ZMM10, -2U },
  { X86::ZMM11, -2U },
  { X86::ZMM12, -2U },
  { X86::ZMM13, -2U },
  { X86::ZMM14, -2U },
  { X86::ZMM15, -2U },
  { X86::ZMM16, -2U },
  { X86::ZMM17, -2U },
  { X86::ZMM18, -2U },
  { X86::ZMM19, -2U },
  { X86::ZMM20, -2U },
  { X86::ZMM21, -2U },
  { X86::ZMM22, -2U },
  { X86::ZMM23, -2U },
  { X86::ZMM24, -2U },
  { X86::ZMM25, -2U },
  { X86::ZMM26, -2U },
  { X86::ZMM27, -2U },
  { X86::ZMM28, -2U },
  { X86::ZMM29, -2U },
  { X86::ZMM30, -2U },
  { X86::ZMM31, -2U },
};
extern const unsigned X86EHFlavour2L2DwarfSize = array_lengthof(X86EHFlavour2L2Dwarf);

extern const uint16_t X86RegEncodingTable[] = {
  0,
  4,
  0,
  0,
  7,
  3,
  5,
  65535,
  5,
  3,
  5,
  1,
  1,
  1,
  0,
  6,
  7,
  65535,
  7,
  2,
  3,
  2,
  0,
  5,
  3,
  1,
  7,
  2,
  0,
  0,
  4,
  0,
  6,
  4,
  0,
  0,
  4,
  5,
  65535,
  65535,
  65535,
  65535,
  65535,
  65535,
  65535,
  65535,
  65535,
  0,
  0,
  5,
  3,
  1,
  7,
  2,
  0,
  4,
  6,
  4,
  6,
  65535,
  6,
  4,
  65535,
  4,
  2,
  0,
  0,
  1,
  2,
  3,
  0,
  1,
  2,
  3,
  4,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  13,
  14,
  15,
  0,
  1,
  2,
  3,
  4,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  13,
  14,
  15,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  1,
  2,
  3,
  4,
  5,
  6,
  7,
  0,
  1,
  2,
  3,
  4,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  13,
  14,
  15,
  0,
  1,
  2,
  3,
  4,
  5,
  6,
  7,
  0,
  1,
  2,
  3,
  4,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  13,
  14,
  15,
  16,
  17,
  18,
  19,
  20,
  21,
  22,
  23,
  24,
  25,
  26,
  27,
  28,
  29,
  30,
  31,
  0,
  1,
  2,
  3,
  4,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  13,
  14,
  15,
  16,
  17,
  18,
  19,
  20,
  21,
  22,
  23,
  24,
  25,
  26,
  27,
  28,
  29,
  30,
  31,
  0,
  1,
  2,
  3,
  4,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  13,
  14,
  15,
  16,
  17,
  18,
  19,
  20,
  21,
  22,
  23,
  24,
  25,
  26,
  27,
  28,
  29,
  30,
  31,
  8,
  9,
  10,
  11,
  12,
  13,
  14,
  15,
  65535,
  65535,
  65535,
  65535,
  65535,
  65535,
  65535,
  65535,
  8,
  9,
  10,
  11,
  12,
  13,
  14,
  15,
  8,
  9,
  10,
  11,
  12,
  13,
  14,
  15,
  65535,
  65535,
  65535,
  65535,
  65535,
  65535,
  65535,
  65535,
};
static inline void InitX86MCRegisterInfo(MCRegisterInfo *RI, unsigned RA, unsigned DwarfFlavour = 0, unsigned EHFlavour = 0, unsigned PC = 0) {
  RI->InitMCRegisterInfo(X86RegDesc, 278, RA, PC, X86MCRegisterClasses, 118, X86RegUnitRoots, 163, X86RegDiffLists, X86LaneMaskLists, X86RegStrings, X86RegClassStrings, X86SubRegIdxLists, 9,
X86SubRegIdxRanges, X86RegEncodingTable);

  switch (DwarfFlavour) {
  default:
    llvm_unreachable("Unknown DWARF flavour");
  case 0:
    RI->mapDwarfRegsToLLVMRegs(X86DwarfFlavour0Dwarf2L, X86DwarfFlavour0Dwarf2LSize, false);
    break;
  case 1:
    RI->mapDwarfRegsToLLVMRegs(X86DwarfFlavour1Dwarf2L, X86DwarfFlavour1Dwarf2LSize, false);
    break;
  case 2:
    RI->mapDwarfRegsToLLVMRegs(X86DwarfFlavour2Dwarf2L, X86DwarfFlavour2Dwarf2LSize, false);
    break;
  }
  switch (EHFlavour) {
  default:
    llvm_unreachable("Unknown DWARF flavour");
  case 0:
    RI->mapDwarfRegsToLLVMRegs(X86EHFlavour0Dwarf2L, X86EHFlavour0Dwarf2LSize, true);
    break;
  case 1:
    RI->mapDwarfRegsToLLVMRegs(X86EHFlavour1Dwarf2L, X86EHFlavour1Dwarf2LSize, true);
    break;
  case 2:
    RI->mapDwarfRegsToLLVMRegs(X86EHFlavour2Dwarf2L, X86EHFlavour2Dwarf2LSize, true);
    break;
  }
  switch (DwarfFlavour) {
  default:
    llvm_unreachable("Unknown DWARF flavour");
  case 0:
    RI->mapLLVMRegsToDwarfRegs(X86DwarfFlavour0L2Dwarf, X86DwarfFlavour0L2DwarfSize, false);
    break;
  case 1:
    RI->mapLLVMRegsToDwarfRegs(X86DwarfFlavour1L2Dwarf, X86DwarfFlavour1L2DwarfSize, false);
    break;
  case 2:
    RI->mapLLVMRegsToDwarfRegs(X86DwarfFlavour2L2Dwarf, X86DwarfFlavour2L2DwarfSize, false);
    break;
  }
  switch (EHFlavour) {
  default:
    llvm_unreachable("Unknown DWARF flavour");
  case 0:
    RI->mapLLVMRegsToDwarfRegs(X86EHFlavour0L2Dwarf, X86EHFlavour0L2DwarfSize, true);
    break;
  case 1:
    RI->mapLLVMRegsToDwarfRegs(X86EHFlavour1L2Dwarf, X86EHFlavour1L2DwarfSize, true);
    break;
  case 2:
    RI->mapLLVMRegsToDwarfRegs(X86EHFlavour2L2Dwarf, X86EHFlavour2L2DwarfSize, true);
    break;
  }
}

} // end namespace llvm

#endif // GET_REGINFO_MC_DESC

/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Register Information Header Fragment                                       *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/


#ifdef GET_REGINFO_HEADER
#undef GET_REGINFO_HEADER

#include "llvm/CodeGen/TargetRegisterInfo.h"

namespace llvm {

class X86FrameLowering;

struct X86GenRegisterInfo : public TargetRegisterInfo {
  explicit X86GenRegisterInfo(unsigned RA, unsigned D = 0, unsigned E = 0,
      unsigned PC = 0, unsigned HwMode = 0);
  unsigned composeSubRegIndicesImpl(unsigned, unsigned) const override;
  LaneBitmask composeSubRegIndexLaneMaskImpl(unsigned, LaneBitmask) const override;
  LaneBitmask reverseComposeSubRegIndexLaneMaskImpl(unsigned, LaneBitmask) const override;
  const TargetRegisterClass *getSubClassWithSubReg(const TargetRegisterClass*, unsigned) const override;
  const RegClassWeight &getRegClassWeight(const TargetRegisterClass *RC) const override;
  unsigned getRegUnitWeight(unsigned RegUnit) const override;
  unsigned getNumRegPressureSets() const override;
  const char *getRegPressureSetName(unsigned Idx) const override;
  unsigned getRegPressureSetLimit(const MachineFunction &MF, unsigned Idx) const override;
  const int *getRegClassPressureSets(const TargetRegisterClass *RC) const override;
  const int *getRegUnitPressureSets(unsigned RegUnit) const override;
  ArrayRef<const char *> getRegMaskNames() const override;
  ArrayRef<const uint32_t *> getRegMasks() const override;
  /// Devirtualized TargetFrameLowering.
  static const X86FrameLowering *getFrameLowering(
      const MachineFunction &MF);
};

namespace X86 { // Register classes
  extern const TargetRegisterClass GR8RegClass;
  extern const TargetRegisterClass GRH8RegClass;
  extern const TargetRegisterClass GR8_NOREXRegClass;
  extern const TargetRegisterClass GR8_ABCD_HRegClass;
  extern const TargetRegisterClass GR8_ABCD_LRegClass;
  extern const TargetRegisterClass GRH16RegClass;
  extern const TargetRegisterClass GR16RegClass;
  extern const TargetRegisterClass GR16_NOREXRegClass;
  extern const TargetRegisterClass VK1RegClass;
  extern const TargetRegisterClass VK16RegClass;
  extern const TargetRegisterClass VK2RegClass;
  extern const TargetRegisterClass VK4RegClass;
  extern const TargetRegisterClass VK8RegClass;
  extern const TargetRegisterClass VK16WMRegClass;
  extern const TargetRegisterClass VK1WMRegClass;
  extern const TargetRegisterClass VK2WMRegClass;
  extern const TargetRegisterClass VK4WMRegClass;
  extern const TargetRegisterClass VK8WMRegClass;
  extern const TargetRegisterClass SEGMENT_REGRegClass;
  extern const TargetRegisterClass GR16_ABCDRegClass;
  extern const TargetRegisterClass FPCCRRegClass;
  extern const TargetRegisterClass FR32XRegClass;
  extern const TargetRegisterClass LOW32_ADDR_ACCESS_RBPRegClass;
  extern const TargetRegisterClass LOW32_ADDR_ACCESSRegClass;
  extern const TargetRegisterClass LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClass;
  extern const TargetRegisterClass DEBUG_REGRegClass;
  extern const TargetRegisterClass FR32RegClass;
  extern const TargetRegisterClass GR32RegClass;
  extern const TargetRegisterClass GR32_NOSPRegClass;
  extern const TargetRegisterClass LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXRegClass;
  extern const TargetRegisterClass GR32_NOREXRegClass;
  extern const TargetRegisterClass VK32RegClass;
  extern const TargetRegisterClass GR32_NOREX_NOSPRegClass;
  extern const TargetRegisterClass RFP32RegClass;
  extern const TargetRegisterClass VK32WMRegClass;
  extern const TargetRegisterClass GR32_ABCDRegClass;
  extern const TargetRegisterClass GR32_TCRegClass;
  extern const TargetRegisterClass GR32_ABCD_and_GR32_TCRegClass;
  extern const TargetRegisterClass GR32_ADRegClass;
  extern const TargetRegisterClass GR32_BPSPRegClass;
  extern const TargetRegisterClass GR32_BSIRegClass;
  extern const TargetRegisterClass GR32_CBRegClass;
  extern const TargetRegisterClass GR32_DCRegClass;
  extern const TargetRegisterClass GR32_DIBPRegClass;
  extern const TargetRegisterClass GR32_SIDIRegClass;
  extern const TargetRegisterClass LOW32_ADDR_ACCESS_RBP_with_sub_32bitRegClass;
  extern const TargetRegisterClass CCRRegClass;
  extern const TargetRegisterClass DFCCRRegClass;
  extern const TargetRegisterClass GR32_ABCD_and_GR32_BSIRegClass;
  extern const TargetRegisterClass GR32_AD_and_GR32_DCRegClass;
  extern const TargetRegisterClass GR32_BPSP_and_GR32_DIBPRegClass;
  extern const TargetRegisterClass GR32_BPSP_and_GR32_TCRegClass;
  extern const TargetRegisterClass GR32_BSI_and_GR32_SIDIRegClass;
  extern const TargetRegisterClass GR32_CB_and_GR32_DCRegClass;
  extern const TargetRegisterClass GR32_DIBP_and_GR32_SIDIRegClass;
  extern const TargetRegisterClass LOW32_ADDR_ACCESS_RBP_with_sub_8bit_with_sub_32bitRegClass;
  extern const TargetRegisterClass LOW32_ADDR_ACCESS_with_sub_32bitRegClass;
  extern const TargetRegisterClass RFP64RegClass;
  extern const TargetRegisterClass FR64XRegClass;
  extern const TargetRegisterClass GR64RegClass;
  extern const TargetRegisterClass CONTROL_REGRegClass;
  extern const TargetRegisterClass FR64RegClass;
  extern const TargetRegisterClass GR64_with_sub_8bitRegClass;
  extern const TargetRegisterClass GR64_NOSPRegClass;
  extern const TargetRegisterClass GR64_TCRegClass;
  extern const TargetRegisterClass GR64_NOREXRegClass;
  extern const TargetRegisterClass GR64_TCW64RegClass;
  extern const TargetRegisterClass GR64_TC_with_sub_8bitRegClass;
  extern const TargetRegisterClass GR64_NOSP_and_GR64_TCRegClass;
  extern const TargetRegisterClass GR64_TCW64_with_sub_8bitRegClass;
  extern const TargetRegisterClass GR64_TC_and_GR64_TCW64RegClass;
  extern const TargetRegisterClass GR64_with_sub_16bit_in_GR16_NOREXRegClass;
  extern const TargetRegisterClass VK64RegClass;
  extern const TargetRegisterClass VR64RegClass;
  extern const TargetRegisterClass GR64_NOREX_NOSPRegClass;
  extern const TargetRegisterClass GR64_NOREX_and_GR64_TCRegClass;
  extern const TargetRegisterClass GR64_NOSP_and_GR64_TCW64RegClass;
  extern const TargetRegisterClass GR64_TCW64_and_GR64_TC_with_sub_8bitRegClass;
  extern const TargetRegisterClass VK64WMRegClass;
  extern const TargetRegisterClass GR64_TC_and_GR64_NOSP_and_GR64_TCW64RegClass;
  extern const TargetRegisterClass GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREXRegClass;
  extern const TargetRegisterClass GR64_NOREX_NOSP_and_GR64_TCRegClass;
  extern const TargetRegisterClass GR64_NOREX_and_GR64_TCW64RegClass;
  extern const TargetRegisterClass GR64_ABCDRegClass;
  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_TCRegClass;
  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TCRegClass;
  extern const TargetRegisterClass GR64_ADRegClass;
  extern const TargetRegisterClass GR64_and_LOW32_ADDR_ACCESS_RBPRegClass;
  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_BPSPRegClass;
  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_BSIRegClass;
  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_CBRegClass;
  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_DCRegClass;
  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_DIBPRegClass;
  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_SIDIRegClass;
  extern const TargetRegisterClass GR64_and_LOW32_ADDR_ACCESSRegClass;
  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_BSIRegClass;
  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_AD_and_GR32_DCRegClass;
  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_DIBPRegClass;
  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_TCRegClass;
  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_BSI_and_GR32_SIDIRegClass;
  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_CB_and_GR32_DCRegClass;
  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_DIBP_and_GR32_SIDIRegClass;
  extern const TargetRegisterClass RSTRegClass;
  extern const TargetRegisterClass RFP80RegClass;
  extern const TargetRegisterClass RFP80_7RegClass;
  extern const TargetRegisterClass VR128XRegClass;
  extern const TargetRegisterClass VR128RegClass;
  extern const TargetRegisterClass VR128HRegClass;
  extern const TargetRegisterClass VR128LRegClass;
  extern const TargetRegisterClass BNDRRegClass;
  extern const TargetRegisterClass VR256XRegClass;
  extern const TargetRegisterClass VR256RegClass;
  extern const TargetRegisterClass VR256HRegClass;
  extern const TargetRegisterClass VR256LRegClass;
  extern const TargetRegisterClass VR512RegClass;
  extern const TargetRegisterClass VR512_with_sub_xmm_in_FR32RegClass;
  extern const TargetRegisterClass VR512_with_sub_xmm_in_VR128HRegClass;
  extern const TargetRegisterClass VR512_with_sub_xmm_in_VR128LRegClass;
} // end namespace X86

} // end namespace llvm

#endif // GET_REGINFO_HEADER

/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Target Register and Register Classes Information                           *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/


#ifdef GET_REGINFO_TARGET_DESC
#undef GET_REGINFO_TARGET_DESC

namespace llvm {

extern const MCRegisterClass X86MCRegisterClasses[];

static const MVT::SimpleValueType VTLists[] = {
  /* 0 */ MVT::i8, MVT::Other,
  /* 2 */ MVT::i16, MVT::Other,
  /* 4 */ MVT::i32, MVT::Other,
  /* 6 */ MVT::i64, MVT::Other,
  /* 8 */ MVT::f80, MVT::f64, MVT::f32, MVT::Other,
  /* 12 */ MVT::f64, MVT::Other,
  /* 14 */ MVT::f80, MVT::Other,
  /* 16 */ MVT::v4f32, MVT::v2f64, MVT::v16i8, MVT::v8i16, MVT::v4i32, MVT::v2i64, MVT::f128, MVT::Other,
  /* 24 */ MVT::v1i1, MVT::Other,
  /* 26 */ MVT::v2i1, MVT::Other,
  /* 28 */ MVT::v4i1, MVT::Other,
  /* 30 */ MVT::v8i1, MVT::Other,
  /* 32 */ MVT::v16i1, MVT::Other,
  /* 34 */ MVT::v32i1, MVT::Other,
  /* 36 */ MVT::v64i1, MVT::Other,
  /* 38 */ MVT::v2i64, MVT::Other,
  /* 40 */ MVT::v8f32, MVT::v4f64, MVT::v32i8, MVT::v16i16, MVT::v8i32, MVT::v4i64, MVT::Other,
  /* 47 */ MVT::v16f32, MVT::v8f64, MVT::v64i8, MVT::v32i16, MVT::v16i32, MVT::v8i64, MVT::Other,
  /* 54 */ MVT::x86mmx, MVT::Other,
};

static const char *const SubRegIndexNameTable[] = { "sub_8bit", "sub_8bit_hi", "sub_8bit_hi_phony", "sub_16bit", "sub_16bit_hi", "sub_32bit", "sub_xmm", "sub_ymm", "" };


static const LaneBitmask SubRegIndexLaneMaskTable[] = {
  LaneBitmask::getAll(),
  LaneBitmask(0x00000001), // sub_8bit
  LaneBitmask(0x00000002), // sub_8bit_hi
  LaneBitmask(0x00000004), // sub_8bit_hi_phony
  LaneBitmask(0x00000007), // sub_16bit
  LaneBitmask(0x00000008), // sub_16bit_hi
  LaneBitmask(0x0000000F), // sub_32bit
  LaneBitmask(0x00000010), // sub_xmm
  LaneBitmask(0x00000010), // sub_ymm
 };



static const TargetRegisterInfo::RegClassInfo RegClassInfos[] = {
  // Mode = 0 (Default)
  { 8, 8, 8, VTLists+0 },    // GR8
  { 8, 8, 8, VTLists+0 },    // GRH8
  { 8, 8, 8, VTLists+0 },    // GR8_NOREX
  { 8, 8, 8, VTLists+0 },    // GR8_ABCD_H
  { 8, 8, 8, VTLists+0 },    // GR8_ABCD_L
  { 16, 16, 16, VTLists+2 },    // GRH16
  { 16, 16, 16, VTLists+2 },    // GR16
  { 16, 16, 16, VTLists+2 },    // GR16_NOREX
  { 16, 16, 16, VTLists+24 },    // VK1
  { 16, 16, 16, VTLists+32 },    // VK16
  { 16, 16, 16, VTLists+26 },    // VK2
  { 16, 16, 16, VTLists+28 },    // VK4
  { 16, 16, 16, VTLists+30 },    // VK8
  { 16, 16, 16, VTLists+32 },    // VK16WM
  { 16, 16, 16, VTLists+24 },    // VK1WM
  { 16, 16, 16, VTLists+26 },    // VK2WM
  { 16, 16, 16, VTLists+28 },    // VK4WM
  { 16, 16, 16, VTLists+30 },    // VK8WM
  { 16, 16, 16, VTLists+2 },    // SEGMENT_REG
  { 16, 16, 16, VTLists+2 },    // GR16_ABCD
  { 16, 16, 16, VTLists+2 },    // FPCCR
  { 32, 32, 32, VTLists+10 },    // FR32X
  { 32, 32, 32, VTLists+4 },    // LOW32_ADDR_ACCESS_RBP
  { 32, 32, 32, VTLists+4 },    // LOW32_ADDR_ACCESS
  { 32, 32, 32, VTLists+4 },    // LOW32_ADDR_ACCESS_RBP_with_sub_8bit
  { 32, 32, 32, VTLists+4 },    // DEBUG_REG
  { 32, 32, 32, VTLists+10 },    // FR32
  { 32, 32, 32, VTLists+4 },    // GR32
  { 32, 32, 32, VTLists+4 },    // GR32_NOSP
  { 32, 32, 32, VTLists+4 },    // LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREX
  { 32, 32, 32, VTLists+4 },    // GR32_NOREX
  { 32, 32, 32, VTLists+34 },    // VK32
  { 32, 32, 32, VTLists+4 },    // GR32_NOREX_NOSP
  { 32, 32, 32, VTLists+10 },    // RFP32
  { 32, 32, 32, VTLists+34 },    // VK32WM
  { 32, 32, 32, VTLists+4 },    // GR32_ABCD
  { 32, 32, 32, VTLists+4 },    // GR32_TC
  { 32, 32, 32, VTLists+4 },    // GR32_ABCD_and_GR32_TC
  { 32, 32, 32, VTLists+4 },    // GR32_AD
  { 32, 32, 32, VTLists+4 },    // GR32_BPSP
  { 32, 32, 32, VTLists+4 },    // GR32_BSI
  { 32, 32, 32, VTLists+4 },    // GR32_CB
  { 32, 32, 32, VTLists+4 },    // GR32_DC
  { 32, 32, 32, VTLists+4 },    // GR32_DIBP
  { 32, 32, 32, VTLists+4 },    // GR32_SIDI
  { 32, 32, 32, VTLists+4 },    // LOW32_ADDR_ACCESS_RBP_with_sub_32bit
  { 32, 32, 32, VTLists+4 },    // CCR
  { 32, 32, 32, VTLists+4 },    // DFCCR
  { 32, 32, 32, VTLists+4 },    // GR32_ABCD_and_GR32_BSI
  { 32, 32, 32, VTLists+4 },    // GR32_AD_and_GR32_DC
  { 32, 32, 32, VTLists+4 },    // GR32_BPSP_and_GR32_DIBP
  { 32, 32, 32, VTLists+4 },    // GR32_BPSP_and_GR32_TC
  { 32, 32, 32, VTLists+4 },    // GR32_BSI_and_GR32_SIDI
  { 32, 32, 32, VTLists+4 },    // GR32_CB_and_GR32_DC
  { 32, 32, 32, VTLists+4 },    // GR32_DIBP_and_GR32_SIDI
  { 32, 32, 32, VTLists+4 },    // LOW32_ADDR_ACCESS_RBP_with_sub_8bit_with_sub_32bit
  { 32, 32, 32, VTLists+4 },    // LOW32_ADDR_ACCESS_with_sub_32bit
  { 64, 64, 32, VTLists+12 },    // RFP64
  { 64, 64, 64, VTLists+12 },    // FR64X
  { 64, 64, 64, VTLists+6 },    // GR64
  { 64, 64, 64, VTLists+6 },    // CONTROL_REG
  { 64, 64, 64, VTLists+12 },    // FR64
  { 64, 64, 64, VTLists+6 },    // GR64_with_sub_8bit
  { 64, 64, 64, VTLists+6 },    // GR64_NOSP
  { 64, 64, 64, VTLists+6 },    // GR64_TC
  { 64, 64, 64, VTLists+6 },    // GR64_NOREX
  { 64, 64, 64, VTLists+6 },    // GR64_TCW64
  { 64, 64, 64, VTLists+6 },    // GR64_TC_with_sub_8bit
  { 64, 64, 64, VTLists+6 },    // GR64_NOSP_and_GR64_TC
  { 64, 64, 64, VTLists+6 },    // GR64_TCW64_with_sub_8bit
  { 64, 64, 64, VTLists+6 },    // GR64_TC_and_GR64_TCW64
  { 64, 64, 64, VTLists+6 },    // GR64_with_sub_16bit_in_GR16_NOREX
  { 64, 64, 64, VTLists+36 },    // VK64
  { 64, 64, 64, VTLists+54 },    // VR64
  { 64, 64, 64, VTLists+6 },    // GR64_NOREX_NOSP
  { 64, 64, 64, VTLists+6 },    // GR64_NOREX_and_GR64_TC
  { 64, 64, 64, VTLists+6 },    // GR64_NOSP_and_GR64_TCW64
  { 64, 64, 64, VTLists+6 },    // GR64_TCW64_and_GR64_TC_with_sub_8bit
  { 64, 64, 64, VTLists+36 },    // VK64WM
  { 64, 64, 64, VTLists+6 },    // GR64_TC_and_GR64_NOSP_and_GR64_TCW64
  { 64, 64, 64, VTLists+6 },    // GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREX
  { 64, 64, 64, VTLists+6 },    // GR64_NOREX_NOSP_and_GR64_TC
  { 64, 64, 64, VTLists+6 },    // GR64_NOREX_and_GR64_TCW64
  { 64, 64, 64, VTLists+6 },    // GR64_ABCD
  { 64, 64, 64, VTLists+6 },    // GR64_with_sub_32bit_in_GR32_TC
  { 64, 64, 64, VTLists+6 },    // GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TC
  { 64, 64, 64, VTLists+6 },    // GR64_AD
  { 64, 64, 64, VTLists+6 },    // GR64_and_LOW32_ADDR_ACCESS_RBP
  { 64, 64, 64, VTLists+6 },    // GR64_with_sub_32bit_in_GR32_BPSP
  { 64, 64, 64, VTLists+6 },    // GR64_with_sub_32bit_in_GR32_BSI
  { 64, 64, 64, VTLists+6 },    // GR64_with_sub_32bit_in_GR32_CB
  { 64, 64, 64, VTLists+6 },    // GR64_with_sub_32bit_in_GR32_DC
  { 64, 64, 64, VTLists+6 },    // GR64_with_sub_32bit_in_GR32_DIBP
  { 64, 64, 64, VTLists+6 },    // GR64_with_sub_32bit_in_GR32_SIDI
  { 64, 64, 64, VTLists+6 },    // GR64_and_LOW32_ADDR_ACCESS
  { 64, 64, 64, VTLists+6 },    // GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_BSI
  { 64, 64, 64, VTLists+6 },    // GR64_with_sub_32bit_in_GR32_AD_and_GR32_DC
  { 64, 64, 64, VTLists+6 },    // GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_DIBP
  { 64, 64, 64, VTLists+6 },    // GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_TC
  { 64, 64, 64, VTLists+6 },    // GR64_with_sub_32bit_in_GR32_BSI_and_GR32_SIDI
  { 64, 64, 64, VTLists+6 },    // GR64_with_sub_32bit_in_GR32_CB_and_GR32_DC
  { 64, 64, 64, VTLists+6 },    // GR64_with_sub_32bit_in_GR32_DIBP_and_GR32_SIDI
  { 80, 80, 32, VTLists+8 },    // RST
  { 80, 80, 32, VTLists+14 },    // RFP80
  { 80, 80, 32, VTLists+14 },    // RFP80_7
  { 128, 128, 128, VTLists+16 },    // VR128X
  { 128, 128, 128, VTLists+16 },    // VR128
  { 128, 128, 128, VTLists+16 },    // VR128H
  { 128, 128, 128, VTLists+16 },    // VR128L
  { 128, 128, 128, VTLists+38 },    // BNDR
  { 256, 256, 256, VTLists+40 },    // VR256X
  { 256, 256, 256, VTLists+40 },    // VR256
  { 256, 256, 256, VTLists+40 },    // VR256H
  { 256, 256, 256, VTLists+40 },    // VR256L
  { 512, 512, 512, VTLists+47 },    // VR512
  { 512, 512, 512, VTLists+47 },    // VR512_with_sub_xmm_in_FR32
  { 512, 512, 512, VTLists+47 },    // VR512_with_sub_xmm_in_VR128H
  { 512, 512, 512, VTLists+47 },    // VR512_with_sub_xmm_in_VR128L
};

static const TargetRegisterClass *const NullRegClasses[] = { nullptr };

static const uint32_t GR8SubClassMask[] = {
  0x0000001d, 0x00000000, 0x00000000, 0x00000000, 
  0x790800c0, 0xc0ff1ff9, 0xbf7bb4b8, 0x0000003f, // sub_8bit
  0x00080000, 0x00230668, 0x8c680000, 0x00000011, // sub_8bit_hi
};

static const uint32_t GRH8SubClassMask[] = {
  0x00000002, 0x00000000, 0x00000000, 0x00000000, 
};

static const uint32_t GR8_NOREXSubClassMask[] = {
  0x0000001c, 0x00000000, 0x00000000, 0x00000000, 
  0x00080000, 0x00230668, 0x8c680000, 0x00000011, // sub_8bit
  0x00080000, 0x00230668, 0x8c680000, 0x00000011, // sub_8bit_hi
};

static const uint32_t GR8_ABCD_HSubClassMask[] = {
  0x00000008, 0x00000000, 0x00000000, 0x00000000, 
  0x00080000, 0x00230668, 0x8c680000, 0x00000011, // sub_8bit_hi
};

static const uint32_t GR8_ABCD_LSubClassMask[] = {
  0x00000010, 0x00000000, 0x00000000, 0x00000000, 
  0x00080000, 0x00230668, 0x8c680000, 0x00000011, // sub_8bit
};

static const uint32_t GRH16SubClassMask[] = {
  0x00000020, 0x00000000, 0x00000000, 0x00000000, 
};

static const uint32_t GR16SubClassMask[] = {
  0x000800c0, 0x00000000, 0x00000000, 0x00000000, 
  0x79000000, 0xc0ff1ff9, 0xbf7bb4b8, 0x0000003f, // sub_16bit
};

static const uint32_t GR16_NOREXSubClassMask[] = {
  0x00080080, 0x00000000, 0x00000000, 0x00000000, 
  0x60000000, 0x00ff1ff9, 0xbf7b0480, 0x0000003f, // sub_16bit
};

static const uint32_t VK1SubClassMask[] = {
  0x8003ff00, 0x00000004, 0x00004100, 0x00000000, 
};

static const uint32_t VK16SubClassMask[] = {
  0x8003ff00, 0x00000004, 0x00004100, 0x00000000, 
};

static const uint32_t VK2SubClassMask[] = {
  0x8003ff00, 0x00000004, 0x00004100, 0x00000000, 
};

static const uint32_t VK4SubClassMask[] = {
  0x8003ff00, 0x00000004, 0x00004100, 0x00000000, 
};

static const uint32_t VK8SubClassMask[] = {
  0x8003ff00, 0x00000004, 0x00004100, 0x00000000, 
};

static const uint32_t VK16WMSubClassMask[] = {
  0x0003e000, 0x00000004, 0x00004000, 0x00000000, 
};

static const uint32_t VK1WMSubClassMask[] = {
  0x0003e000, 0x00000004, 0x00004000, 0x00000000, 
};

static const uint32_t VK2WMSubClassMask[] = {
  0x0003e000, 0x00000004, 0x00004000, 0x00000000, 
};

static const uint32_t VK4WMSubClassMask[] = {
  0x0003e000, 0x00000004, 0x00004000, 0x00000000, 
};

static const uint32_t VK8WMSubClassMask[] = {
  0x0003e000, 0x00000004, 0x00004000, 0x00000000, 
};

static const uint32_t SEGMENT_REGSubClassMask[] = {
  0x00040000, 0x00000000, 0x00000000, 0x00000000, 
};

static const uint32_t GR16_ABCDSubClassMask[] = {
  0x00080000, 0x00000000, 0x00000000, 0x00000000, 
  0x00000000, 0x00230668, 0x8c680000, 0x00000011, // sub_16bit
};

static const uint32_t FPCCRSubClassMask[] = {
  0x00100000, 0x00000000, 0x00000000, 0x00000000, 
};

static const uint32_t FR32XSubClassMask[] = {
  0x04200000, 0x24000000, 0x00000000, 0x00001e00, 
  0x00000000, 0x00000000, 0x00000000, 0x003fc000, // sub_xmm
};

static const uint32_t LOW32_ADDR_ACCESS_RBPSubClassMask[] = {
  0x79c00000, 0x01ff3ff9, 0x40800000, 0x00000002, 
  0x00000000, 0xc0800000, 0xbf7bb4b8, 0x0000003f, // sub_32bit
};

static const uint32_t LOW32_ADDR_ACCESSSubClassMask[] = {
  0x58800000, 0x017f1ff9, 0x40000000, 0x00000000, 
  0x00000000, 0xc0800000, 0xbf7bb4b8, 0x0000003f, // sub_32bit
};

static const uint32_t LOW32_ADDR_ACCESS_RBP_with_sub_8bitSubClassMask[] = {
  0x79000000, 0x00ff1ff9, 0x00000000, 0x00000002, 
  0x00000000, 0xc0800000, 0xbf7bb4b8, 0x0000003f, // sub_32bit
};

static const uint32_t DEBUG_REGSubClassMask[] = {
  0x02000000, 0x00000000, 0x00000000, 0x00000000, 
};

static const uint32_t FR32SubClassMask[] = {
  0x04000000, 0x20000000, 0x00000000, 0x00001c00, 
  0x00000000, 0x00000000, 0x00000000, 0x003b8000, // sub_xmm
};

static const uint32_t GR32SubClassMask[] = {
  0x58000000, 0x007f1ff9, 0x00000000, 0x00000000, 
  0x00000000, 0xc0800000, 0xbf7bb4b8, 0x0000003f, // sub_32bit
};

static const uint32_t GR32_NOSPSubClassMask[] = {
  0x10000000, 0x00771f69, 0x00000000, 0x00000000, 
  0x00000000, 0x80800000, 0xbe6a9410, 0x0000003b, // sub_32bit
};

static const uint32_t LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXSubClassMask[] = {
  0x60000000, 0x00ff1ff9, 0x00000000, 0x00000002, 
  0x00000000, 0x00800000, 0xbf7b0480, 0x0000003f, // sub_32bit
};

static const uint32_t GR32_NOREXSubClassMask[] = {
  0x40000000, 0x007f1ff9, 0x00000000, 0x00000000, 
  0x00000000, 0x00800000, 0xbf7b0480, 0x0000003f, // sub_32bit
};

static const uint32_t VK32SubClassMask[] = {
  0x80000000, 0x00000004, 0x00004100, 0x00000000, 
};

static const uint32_t GR32_NOREX_NOSPSubClassMask[] = {
  0x00000000, 0x00771f69, 0x00000000, 0x00000000, 
  0x00000000, 0x00800000, 0xbe6a0400, 0x0000003b, // sub_32bit
};

static const uint32_t RFP32SubClassMask[] = {
  0x00000000, 0x02000002, 0x00000000, 0x00000080, 
};

static const uint32_t VK32WMSubClassMask[] = {
  0x00000000, 0x00000004, 0x00004000, 0x00000000, 
};

static const uint32_t GR32_ABCDSubClassMask[] = {
  0x00000000, 0x00230668, 0x00000000, 0x00000000, 
  0x00000000, 0x00000000, 0x8c680000, 0x00000011, // sub_32bit
};

static const uint32_t GR32_TCSubClassMask[] = {
  0x00000000, 0x002a0470, 0x00000000, 0x00000000, 
  0x00000000, 0x00000000, 0x08700000, 0x00000015, // sub_32bit
};

static const uint32_t GR32_ABCD_and_GR32_TCSubClassMask[] = {
  0x00000000, 0x00220460, 0x00000000, 0x00000000, 
  0x00000000, 0x00000000, 0x08600000, 0x00000011, // sub_32bit
};

static const uint32_t GR32_ADSubClassMask[] = {
  0x00000000, 0x00020040, 0x00000000, 0x00000000, 
  0x00000000, 0x00000000, 0x00400000, 0x00000001, // sub_32bit
};

static const uint32_t GR32_BPSPSubClassMask[] = {
  0x00000000, 0x000c0080, 0x00000000, 0x00000000, 
  0x00000000, 0x00800000, 0x01000000, 0x00000006, // sub_32bit
};

static const uint32_t GR32_BSISubClassMask[] = {
  0x00000000, 0x00110100, 0x00000000, 0x00000000, 
  0x00000000, 0x00000000, 0x82000000, 0x00000008, // sub_32bit
};

static const uint32_t GR32_CBSubClassMask[] = {
  0x00000000, 0x00210200, 0x00000000, 0x00000000, 
  0x00000000, 0x00000000, 0x84000000, 0x00000010, // sub_32bit
};

static const uint32_t GR32_DCSubClassMask[] = {
  0x00000000, 0x00220400, 0x00000000, 0x00000000, 
  0x00000000, 0x00000000, 0x08000000, 0x00000011, // sub_32bit
};

static const uint32_t GR32_DIBPSubClassMask[] = {
  0x00000000, 0x00440800, 0x00000000, 0x00000000, 
  0x00000000, 0x00800000, 0x10000000, 0x00000022, // sub_32bit
};

static const uint32_t GR32_SIDISubClassMask[] = {
  0x00000000, 0x00501000, 0x00000000, 0x00000000, 
  0x00000000, 0x00000000, 0x20000000, 0x00000028, // sub_32bit
};

static const uint32_t LOW32_ADDR_ACCESS_RBP_with_sub_32bitSubClassMask[] = {
  0x00000000, 0x01802000, 0x40800000, 0x00000002, 
};

static const uint32_t CCRSubClassMask[] = {
  0x00000000, 0x00004000, 0x00000000, 0x00000000, 
};

static const uint32_t DFCCRSubClassMask[] = {
  0x00000000, 0x00008000, 0x00000000, 0x00000000, 
};

static const uint32_t GR32_ABCD_and_GR32_BSISubClassMask[] = {
  0x00000000, 0x00010000, 0x00000000, 0x00000000, 
  0x00000000, 0x00000000, 0x80000000, 0x00000000, // sub_32bit
};

static const uint32_t GR32_AD_and_GR32_DCSubClassMask[] = {
  0x00000000, 0x00020000, 0x00000000, 0x00000000, 
  0x00000000, 0x00000000, 0x00000000, 0x00000001, // sub_32bit
};

static const uint32_t GR32_BPSP_and_GR32_DIBPSubClassMask[] = {
  0x00000000, 0x00040000, 0x00000000, 0x00000000, 
  0x00000000, 0x00800000, 0x00000000, 0x00000002, // sub_32bit
};

static const uint32_t GR32_BPSP_and_GR32_TCSubClassMask[] = {
  0x00000000, 0x00080000, 0x00000000, 0x00000000, 
  0x00000000, 0x00000000, 0x00000000, 0x00000004, // sub_32bit
};

static const uint32_t GR32_BSI_and_GR32_SIDISubClassMask[] = {
  0x00000000, 0x00100000, 0x00000000, 0x00000000, 
  0x00000000, 0x00000000, 0x00000000, 0x00000008, // sub_32bit
};

static const uint32_t GR32_CB_and_GR32_DCSubClassMask[] = {
  0x00000000, 0x00200000, 0x00000000, 0x00000000, 
  0x00000000, 0x00000000, 0x00000000, 0x00000010, // sub_32bit
};

static const uint32_t GR32_DIBP_and_GR32_SIDISubClassMask[] = {
  0x00000000, 0x00400000, 0x00000000, 0x00000000, 
  0x00000000, 0x00000000, 0x00000000, 0x00000020, // sub_32bit
};

static const uint32_t LOW32_ADDR_ACCESS_RBP_with_sub_8bit_with_sub_32bitSubClassMask[] = {
  0x00000000, 0x00800000, 0x00000000, 0x00000002, 
};

static const uint32_t LOW32_ADDR_ACCESS_with_sub_32bitSubClassMask[] = {
  0x00000000, 0x01000000, 0x40000000, 0x00000000, 
};

static const uint32_t RFP64SubClassMask[] = {
  0x00000000, 0x02000000, 0x00000000, 0x00000080, 
};

static const uint32_t FR64XSubClassMask[] = {
  0x00000000, 0x24000000, 0x00000000, 0x00001e00, 
  0x00000000, 0x00000000, 0x00000000, 0x003fc000, // sub_xmm
};

static const uint32_t GR64SubClassMask[] = {
  0x00000000, 0xc8000000, 0xffffbcff, 0x0000003f, 
};

static const uint32_t CONTROL_REGSubClassMask[] = {
  0x00000000, 0x10000000, 0x00000000, 0x00000000, 
};

static const uint32_t FR64SubClassMask[] = {
  0x00000000, 0x20000000, 0x00000000, 0x00001c00, 
  0x00000000, 0x00000000, 0x00000000, 0x003b8000, // sub_xmm
};

static const uint32_t GR64_with_sub_8bitSubClassMask[] = {
  0x00000000, 0xc0000000, 0xbf7bb4b8, 0x0000003f, 
};

static const uint32_t GR64_NOSPSubClassMask[] = {
  0x00000000, 0x80000000, 0xbe6a9410, 0x0000003b, 
};

static const uint32_t GR64_TCSubClassMask[] = {
  0x00000000, 0x00000000, 0x6877a859, 0x0000003d, 
};

static const uint32_t GR64_NOREXSubClassMask[] = {
  0x00000000, 0x00000000, 0xffff0c82, 0x0000003f, 
};

static const uint32_t GR64_TCW64SubClassMask[] = {
  0x00000000, 0x00000000, 0x4874b064, 0x00000015, 
};

static const uint32_t GR64_TC_with_sub_8bitSubClassMask[] = {
  0x00000000, 0x00000000, 0x2873a018, 0x0000003d, 
};

static const uint32_t GR64_NOSP_and_GR64_TCSubClassMask[] = {
  0x00000000, 0x00000000, 0x28628010, 0x00000039, 
};

static const uint32_t GR64_TCW64_with_sub_8bitSubClassMask[] = {
  0x00000000, 0x00000000, 0x0870b020, 0x00000015, 
};

static const uint32_t GR64_TC_and_GR64_TCW64SubClassMask[] = {
  0x00000000, 0x00000000, 0x4874a040, 0x00000015, 
};

static const uint32_t GR64_with_sub_16bit_in_GR16_NOREXSubClassMask[] = {
  0x00000000, 0x00000000, 0xbf7b0480, 0x0000003f, 
};

static const uint32_t VK64SubClassMask[] = {
  0x00000000, 0x00000000, 0x00004100, 0x00000000, 
};

static const uint32_t VR64SubClassMask[] = {
  0x00000000, 0x00000000, 0x00000200, 0x00000000, 
};

static const uint32_t GR64_NOREX_NOSPSubClassMask[] = {
  0x00000000, 0x00000000, 0xbe6a0400, 0x0000003b, 
};

static const uint32_t GR64_NOREX_and_GR64_TCSubClassMask[] = {
  0x00000000, 0x00000000, 0x68770800, 0x0000003d, 
};

static const uint32_t GR64_NOSP_and_GR64_TCW64SubClassMask[] = {
  0x00000000, 0x00000000, 0x08609000, 0x00000011, 
};

static const uint32_t GR64_TCW64_and_GR64_TC_with_sub_8bitSubClassMask[] = {
  0x00000000, 0x00000000, 0x0870a000, 0x00000015, 
};

static const uint32_t VK64WMSubClassMask[] = {
  0x00000000, 0x00000000, 0x00004000, 0x00000000, 
};

static const uint32_t GR64_TC_and_GR64_NOSP_and_GR64_TCW64SubClassMask[] = {
  0x00000000, 0x00000000, 0x08608000, 0x00000011, 
};

static const uint32_t GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREXSubClassMask[] = {
  0x00000000, 0x00000000, 0x28730000, 0x0000003d, 
};

static const uint32_t GR64_NOREX_NOSP_and_GR64_TCSubClassMask[] = {
  0x00000000, 0x00000000, 0x28620000, 0x00000039, 
};

static const uint32_t GR64_NOREX_and_GR64_TCW64SubClassMask[] = {
  0x00000000, 0x00000000, 0x48740000, 0x00000015, 
};

static const uint32_t GR64_ABCDSubClassMask[] = {
  0x00000000, 0x00000000, 0x8c680000, 0x00000011, 
};

static const uint32_t GR64_with_sub_32bit_in_GR32_TCSubClassMask[] = {
  0x00000000, 0x00000000, 0x08700000, 0x00000015, 
};

static const uint32_t GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TCSubClassMask[] = {
  0x00000000, 0x00000000, 0x08600000, 0x00000011, 
};

static const uint32_t GR64_ADSubClassMask[] = {
  0x00000000, 0x00000000, 0x00400000, 0x00000001, 
};

static const uint32_t GR64_and_LOW32_ADDR_ACCESS_RBPSubClassMask[] = {
  0x00000000, 0x00000000, 0x40800000, 0x00000002, 
};

static const uint32_t GR64_with_sub_32bit_in_GR32_BPSPSubClassMask[] = {
  0x00000000, 0x00000000, 0x01000000, 0x00000006, 
};

static const uint32_t GR64_with_sub_32bit_in_GR32_BSISubClassMask[] = {
  0x00000000, 0x00000000, 0x82000000, 0x00000008, 
};

static const uint32_t GR64_with_sub_32bit_in_GR32_CBSubClassMask[] = {
  0x00000000, 0x00000000, 0x84000000, 0x00000010, 
};

static const uint32_t GR64_with_sub_32bit_in_GR32_DCSubClassMask[] = {
  0x00000000, 0x00000000, 0x08000000, 0x00000011, 
};

static const uint32_t GR64_with_sub_32bit_in_GR32_DIBPSubClassMask[] = {
  0x00000000, 0x00000000, 0x10000000, 0x00000022, 
};

static const uint32_t GR64_with_sub_32bit_in_GR32_SIDISubClassMask[] = {
  0x00000000, 0x00000000, 0x20000000, 0x00000028, 
};

static const uint32_t GR64_and_LOW32_ADDR_ACCESSSubClassMask[] = {
  0x00000000, 0x00000000, 0x40000000, 0x00000000, 
};

static const uint32_t GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_BSISubClassMask[] = {
  0x00000000, 0x00000000, 0x80000000, 0x00000000, 
};

static const uint32_t GR64_with_sub_32bit_in_GR32_AD_and_GR32_DCSubClassMask[] = {
  0x00000000, 0x00000000, 0x00000000, 0x00000001, 
};

static const uint32_t GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_DIBPSubClassMask[] = {
  0x00000000, 0x00000000, 0x00000000, 0x00000002, 
};

static const uint32_t GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_TCSubClassMask[] = {
  0x00000000, 0x00000000, 0x00000000, 0x00000004, 
};

static const uint32_t GR64_with_sub_32bit_in_GR32_BSI_and_GR32_SIDISubClassMask[] = {
  0x00000000, 0x00000000, 0x00000000, 0x00000008, 
};

static const uint32_t GR64_with_sub_32bit_in_GR32_CB_and_GR32_DCSubClassMask[] = {
  0x00000000, 0x00000000, 0x00000000, 0x00000010, 
};

static const uint32_t GR64_with_sub_32bit_in_GR32_DIBP_and_GR32_SIDISubClassMask[] = {
  0x00000000, 0x00000000, 0x00000000, 0x00000020, 
};

static const uint32_t RSTSubClassMask[] = {
  0x00000000, 0x00000000, 0x00000000, 0x00000040, 
};

static const uint32_t RFP80SubClassMask[] = {
  0x00000000, 0x00000000, 0x00000000, 0x00000080, 
};

static const uint32_t RFP80_7SubClassMask[] = {
  0x00000000, 0x00000000, 0x00000000, 0x00000100, 
};

static const uint32_t VR128XSubClassMask[] = {
  0x00000000, 0x00000000, 0x00000000, 0x00001e00, 
  0x00000000, 0x00000000, 0x00000000, 0x003fc000, // sub_xmm
};

static const uint32_t VR128SubClassMask[] = {
  0x00000000, 0x00000000, 0x00000000, 0x00001c00, 
  0x00000000, 0x00000000, 0x00000000, 0x003b8000, // sub_xmm
};

static const uint32_t VR128HSubClassMask[] = {
  0x00000000, 0x00000000, 0x00000000, 0x00000800, 
  0x00000000, 0x00000000, 0x00000000, 0x00110000, // sub_xmm
};

static const uint32_t VR128LSubClassMask[] = {
  0x00000000, 0x00000000, 0x00000000, 0x00001000, 
  0x00000000, 0x00000000, 0x00000000, 0x00220000, // sub_xmm
};

static const uint32_t BNDRSubClassMask[] = {
  0x00000000, 0x00000000, 0x00000000, 0x00002000, 
};

static const uint32_t VR256XSubClassMask[] = {
  0x00000000, 0x00000000, 0x00000000, 0x0003c000, 
  0x00000000, 0x00000000, 0x00000000, 0x003c0000, // sub_ymm
};

static const uint32_t VR256SubClassMask[] = {
  0x00000000, 0x00000000, 0x00000000, 0x00038000, 
  0x00000000, 0x00000000, 0x00000000, 0x00380000, // sub_ymm
};

static const uint32_t VR256HSubClassMask[] = {
  0x00000000, 0x00000000, 0x00000000, 0x00010000, 
  0x00000000, 0x00000000, 0x00000000, 0x00100000, // sub_ymm
};

static const uint32_t VR256LSubClassMask[] = {
  0x00000000, 0x00000000, 0x00000000, 0x00020000, 
  0x00000000, 0x00000000, 0x00000000, 0x00200000, // sub_ymm
};

static const uint32_t VR512SubClassMask[] = {
  0x00000000, 0x00000000, 0x00000000, 0x003c0000, 
};

static const uint32_t VR512_with_sub_xmm_in_FR32SubClassMask[] = {
  0x00000000, 0x00000000, 0x00000000, 0x00380000, 
};

static const uint32_t VR512_with_sub_xmm_in_VR128HSubClassMask[] = {
  0x00000000, 0x00000000, 0x00000000, 0x00100000, 
};

static const uint32_t VR512_with_sub_xmm_in_VR128LSubClassMask[] = {
  0x00000000, 0x00000000, 0x00000000, 0x00200000, 
};

static const uint16_t SuperRegIdxSeqs[] = {
  /* 0 */ 1, 0,
  /* 2 */ 1, 2, 0,
  /* 5 */ 4, 0,
  /* 7 */ 6, 0,
  /* 9 */ 7, 0,
  /* 11 */ 8, 0,
};

static const TargetRegisterClass *const GR8_NOREXSuperclasses[] = {
  &X86::GR8RegClass,
  nullptr
};

static const TargetRegisterClass *const GR8_ABCD_HSuperclasses[] = {
  &X86::GR8RegClass,
  &X86::GR8_NOREXRegClass,
  nullptr
};

static const TargetRegisterClass *const GR8_ABCD_LSuperclasses[] = {
  &X86::GR8RegClass,
  &X86::GR8_NOREXRegClass,
  nullptr
};

static const TargetRegisterClass *const GR16_NOREXSuperclasses[] = {
  &X86::GR16RegClass,
  nullptr
};

static const TargetRegisterClass *const VK1Superclasses[] = {
  &X86::VK16RegClass,
  &X86::VK2RegClass,
  &X86::VK4RegClass,
  &X86::VK8RegClass,
  nullptr
};

static const TargetRegisterClass *const VK16Superclasses[] = {
  &X86::VK1RegClass,
  &X86::VK2RegClass,
  &X86::VK4RegClass,
  &X86::VK8RegClass,
  nullptr
};

static const TargetRegisterClass *const VK2Superclasses[] = {
  &X86::VK1RegClass,
  &X86::VK16RegClass,
  &X86::VK4RegClass,
  &X86::VK8RegClass,
  nullptr
};

static const TargetRegisterClass *const VK4Superclasses[] = {
  &X86::VK1RegClass,
  &X86::VK16RegClass,
  &X86::VK2RegClass,
  &X86::VK8RegClass,
  nullptr
};

static const TargetRegisterClass *const VK8Superclasses[] = {
  &X86::VK1RegClass,
  &X86::VK16RegClass,
  &X86::VK2RegClass,
  &X86::VK4RegClass,
  nullptr
};

static const TargetRegisterClass *const VK16WMSuperclasses[] = {
  &X86::VK1RegClass,
  &X86::VK16RegClass,
  &X86::VK2RegClass,
  &X86::VK4RegClass,
  &X86::VK8RegClass,
  &X86::VK1WMRegClass,
  &X86::VK2WMRegClass,
  &X86::VK4WMRegClass,
  &X86::VK8WMRegClass,
  nullptr
};

static const TargetRegisterClass *const VK1WMSuperclasses[] = {
  &X86::VK1RegClass,
  &X86::VK16RegClass,
  &X86::VK2RegClass,
  &X86::VK4RegClass,
  &X86::VK8RegClass,
  &X86::VK16WMRegClass,
  &X86::VK2WMRegClass,
  &X86::VK4WMRegClass,
  &X86::VK8WMRegClass,
  nullptr
};

static const TargetRegisterClass *const VK2WMSuperclasses[] = {
  &X86::VK1RegClass,
  &X86::VK16RegClass,
  &X86::VK2RegClass,
  &X86::VK4RegClass,
  &X86::VK8RegClass,
  &X86::VK16WMRegClass,
  &X86::VK1WMRegClass,
  &X86::VK4WMRegClass,
  &X86::VK8WMRegClass,
  nullptr
};

static const TargetRegisterClass *const VK4WMSuperclasses[] = {
  &X86::VK1RegClass,
  &X86::VK16RegClass,
  &X86::VK2RegClass,
  &X86::VK4RegClass,
  &X86::VK8RegClass,
  &X86::VK16WMRegClass,
  &X86::VK1WMRegClass,
  &X86::VK2WMRegClass,
  &X86::VK8WMRegClass,
  nullptr
};

static const TargetRegisterClass *const VK8WMSuperclasses[] = {
  &X86::VK1RegClass,
  &X86::VK16RegClass,
  &X86::VK2RegClass,
  &X86::VK4RegClass,
  &X86::VK8RegClass,
  &X86::VK16WMRegClass,
  &X86::VK1WMRegClass,
  &X86::VK2WMRegClass,
  &X86::VK4WMRegClass,
  nullptr
};

static const TargetRegisterClass *const GR16_ABCDSuperclasses[] = {
  &X86::GR16RegClass,
  &X86::GR16_NOREXRegClass,
  nullptr
};

static const TargetRegisterClass *const LOW32_ADDR_ACCESSSuperclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  nullptr
};

static const TargetRegisterClass *const LOW32_ADDR_ACCESS_RBP_with_sub_8bitSuperclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  nullptr
};

static const TargetRegisterClass *const FR32Superclasses[] = {
  &X86::FR32XRegClass,
  nullptr
};

static const TargetRegisterClass *const GR32Superclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  &X86::LOW32_ADDR_ACCESSRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClass,
  nullptr
};

static const TargetRegisterClass *const GR32_NOSPSuperclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  &X86::LOW32_ADDR_ACCESSRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClass,
  &X86::GR32RegClass,
  nullptr
};

static const TargetRegisterClass *const LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXSuperclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClass,
  nullptr
};

static const TargetRegisterClass *const GR32_NOREXSuperclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  &X86::LOW32_ADDR_ACCESSRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClass,
  &X86::GR32RegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXRegClass,
  nullptr
};

static const TargetRegisterClass *const VK32Superclasses[] = {
  &X86::VK1RegClass,
  &X86::VK16RegClass,
  &X86::VK2RegClass,
  &X86::VK4RegClass,
  &X86::VK8RegClass,
  nullptr
};

static const TargetRegisterClass *const GR32_NOREX_NOSPSuperclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  &X86::LOW32_ADDR_ACCESSRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClass,
  &X86::GR32RegClass,
  &X86::GR32_NOSPRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR32_NOREXRegClass,
  nullptr
};

static const TargetRegisterClass *const VK32WMSuperclasses[] = {
  &X86::VK1RegClass,
  &X86::VK16RegClass,
  &X86::VK2RegClass,
  &X86::VK4RegClass,
  &X86::VK8RegClass,
  &X86::VK16WMRegClass,
  &X86::VK1WMRegClass,
  &X86::VK2WMRegClass,
  &X86::VK4WMRegClass,
  &X86::VK8WMRegClass,
  &X86::VK32RegClass,
  nullptr
};

static const TargetRegisterClass *const GR32_ABCDSuperclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  &X86::LOW32_ADDR_ACCESSRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClass,
  &X86::GR32RegClass,
  &X86::GR32_NOSPRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR32_NOREXRegClass,
  &X86::GR32_NOREX_NOSPRegClass,
  nullptr
};

static const TargetRegisterClass *const GR32_TCSuperclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  &X86::LOW32_ADDR_ACCESSRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClass,
  &X86::GR32RegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR32_NOREXRegClass,
  nullptr
};

static const TargetRegisterClass *const GR32_ABCD_and_GR32_TCSuperclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  &X86::LOW32_ADDR_ACCESSRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClass,
  &X86::GR32RegClass,
  &X86::GR32_NOSPRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR32_NOREXRegClass,
  &X86::GR32_NOREX_NOSPRegClass,
  &X86::GR32_ABCDRegClass,
  &X86::GR32_TCRegClass,
  nullptr
};

static const TargetRegisterClass *const GR32_ADSuperclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  &X86::LOW32_ADDR_ACCESSRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClass,
  &X86::GR32RegClass,
  &X86::GR32_NOSPRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR32_NOREXRegClass,
  &X86::GR32_NOREX_NOSPRegClass,
  &X86::GR32_ABCDRegClass,
  &X86::GR32_TCRegClass,
  &X86::GR32_ABCD_and_GR32_TCRegClass,
  nullptr
};

static const TargetRegisterClass *const GR32_BPSPSuperclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  &X86::LOW32_ADDR_ACCESSRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClass,
  &X86::GR32RegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR32_NOREXRegClass,
  nullptr
};

static const TargetRegisterClass *const GR32_BSISuperclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  &X86::LOW32_ADDR_ACCESSRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClass,
  &X86::GR32RegClass,
  &X86::GR32_NOSPRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR32_NOREXRegClass,
  &X86::GR32_NOREX_NOSPRegClass,
  nullptr
};

static const TargetRegisterClass *const GR32_CBSuperclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  &X86::LOW32_ADDR_ACCESSRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClass,
  &X86::GR32RegClass,
  &X86::GR32_NOSPRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR32_NOREXRegClass,
  &X86::GR32_NOREX_NOSPRegClass,
  &X86::GR32_ABCDRegClass,
  nullptr
};

static const TargetRegisterClass *const GR32_DCSuperclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  &X86::LOW32_ADDR_ACCESSRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClass,
  &X86::GR32RegClass,
  &X86::GR32_NOSPRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR32_NOREXRegClass,
  &X86::GR32_NOREX_NOSPRegClass,
  &X86::GR32_ABCDRegClass,
  &X86::GR32_TCRegClass,
  &X86::GR32_ABCD_and_GR32_TCRegClass,
  nullptr
};

static const TargetRegisterClass *const GR32_DIBPSuperclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  &X86::LOW32_ADDR_ACCESSRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClass,
  &X86::GR32RegClass,
  &X86::GR32_NOSPRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR32_NOREXRegClass,
  &X86::GR32_NOREX_NOSPRegClass,
  nullptr
};

static const TargetRegisterClass *const GR32_SIDISuperclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  &X86::LOW32_ADDR_ACCESSRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClass,
  &X86::GR32RegClass,
  &X86::GR32_NOSPRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR32_NOREXRegClass,
  &X86::GR32_NOREX_NOSPRegClass,
  nullptr
};

static const TargetRegisterClass *const LOW32_ADDR_ACCESS_RBP_with_sub_32bitSuperclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  nullptr
};

static const TargetRegisterClass *const GR32_ABCD_and_GR32_BSISuperclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  &X86::LOW32_ADDR_ACCESSRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClass,
  &X86::GR32RegClass,
  &X86::GR32_NOSPRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR32_NOREXRegClass,
  &X86::GR32_NOREX_NOSPRegClass,
  &X86::GR32_ABCDRegClass,
  &X86::GR32_BSIRegClass,
  &X86::GR32_CBRegClass,
  nullptr
};

static const TargetRegisterClass *const GR32_AD_and_GR32_DCSuperclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  &X86::LOW32_ADDR_ACCESSRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClass,
  &X86::GR32RegClass,
  &X86::GR32_NOSPRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR32_NOREXRegClass,
  &X86::GR32_NOREX_NOSPRegClass,
  &X86::GR32_ABCDRegClass,
  &X86::GR32_TCRegClass,
  &X86::GR32_ABCD_and_GR32_TCRegClass,
  &X86::GR32_ADRegClass,
  &X86::GR32_DCRegClass,
  nullptr
};

static const TargetRegisterClass *const GR32_BPSP_and_GR32_DIBPSuperclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  &X86::LOW32_ADDR_ACCESSRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClass,
  &X86::GR32RegClass,
  &X86::GR32_NOSPRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR32_NOREXRegClass,
  &X86::GR32_NOREX_NOSPRegClass,
  &X86::GR32_BPSPRegClass,
  &X86::GR32_DIBPRegClass,
  nullptr
};

static const TargetRegisterClass *const GR32_BPSP_and_GR32_TCSuperclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  &X86::LOW32_ADDR_ACCESSRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClass,
  &X86::GR32RegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR32_NOREXRegClass,
  &X86::GR32_TCRegClass,
  &X86::GR32_BPSPRegClass,
  nullptr
};

static const TargetRegisterClass *const GR32_BSI_and_GR32_SIDISuperclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  &X86::LOW32_ADDR_ACCESSRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClass,
  &X86::GR32RegClass,
  &X86::GR32_NOSPRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR32_NOREXRegClass,
  &X86::GR32_NOREX_NOSPRegClass,
  &X86::GR32_BSIRegClass,
  &X86::GR32_SIDIRegClass,
  nullptr
};

static const TargetRegisterClass *const GR32_CB_and_GR32_DCSuperclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  &X86::LOW32_ADDR_ACCESSRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClass,
  &X86::GR32RegClass,
  &X86::GR32_NOSPRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR32_NOREXRegClass,
  &X86::GR32_NOREX_NOSPRegClass,
  &X86::GR32_ABCDRegClass,
  &X86::GR32_TCRegClass,
  &X86::GR32_ABCD_and_GR32_TCRegClass,
  &X86::GR32_CBRegClass,
  &X86::GR32_DCRegClass,
  nullptr
};

static const TargetRegisterClass *const GR32_DIBP_and_GR32_SIDISuperclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  &X86::LOW32_ADDR_ACCESSRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClass,
  &X86::GR32RegClass,
  &X86::GR32_NOSPRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR32_NOREXRegClass,
  &X86::GR32_NOREX_NOSPRegClass,
  &X86::GR32_DIBPRegClass,
  &X86::GR32_SIDIRegClass,
  nullptr
};

static const TargetRegisterClass *const LOW32_ADDR_ACCESS_RBP_with_sub_8bit_with_sub_32bitSuperclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_32bitRegClass,
  nullptr
};

static const TargetRegisterClass *const LOW32_ADDR_ACCESS_with_sub_32bitSuperclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  &X86::LOW32_ADDR_ACCESSRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_32bitRegClass,
  nullptr
};

static const TargetRegisterClass *const RFP64Superclasses[] = {
  &X86::RFP32RegClass,
  nullptr
};

static const TargetRegisterClass *const FR64XSuperclasses[] = {
  &X86::FR32XRegClass,
  nullptr
};

static const TargetRegisterClass *const FR64Superclasses[] = {
  &X86::FR32XRegClass,
  &X86::FR32RegClass,
  &X86::FR64XRegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_with_sub_8bitSuperclasses[] = {
  &X86::GR64RegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_NOSPSuperclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_with_sub_8bitRegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_TCSuperclasses[] = {
  &X86::GR64RegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_NOREXSuperclasses[] = {
  &X86::GR64RegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_TCW64Superclasses[] = {
  &X86::GR64RegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_TC_with_sub_8bitSuperclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_with_sub_8bitRegClass,
  &X86::GR64_TCRegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_NOSP_and_GR64_TCSuperclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_with_sub_8bitRegClass,
  &X86::GR64_NOSPRegClass,
  &X86::GR64_TCRegClass,
  &X86::GR64_TC_with_sub_8bitRegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_TCW64_with_sub_8bitSuperclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_with_sub_8bitRegClass,
  &X86::GR64_TCW64RegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_TC_and_GR64_TCW64Superclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_TCRegClass,
  &X86::GR64_TCW64RegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_with_sub_16bit_in_GR16_NOREXSuperclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_with_sub_8bitRegClass,
  &X86::GR64_NOREXRegClass,
  nullptr
};

static const TargetRegisterClass *const VK64Superclasses[] = {
  &X86::VK1RegClass,
  &X86::VK16RegClass,
  &X86::VK2RegClass,
  &X86::VK4RegClass,
  &X86::VK8RegClass,
  &X86::VK32RegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_NOREX_NOSPSuperclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_with_sub_8bitRegClass,
  &X86::GR64_NOSPRegClass,
  &X86::GR64_NOREXRegClass,
  &X86::GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_NOREX_and_GR64_TCSuperclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_TCRegClass,
  &X86::GR64_NOREXRegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_NOSP_and_GR64_TCW64Superclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_with_sub_8bitRegClass,
  &X86::GR64_NOSPRegClass,
  &X86::GR64_TCW64RegClass,
  &X86::GR64_TCW64_with_sub_8bitRegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_TCW64_and_GR64_TC_with_sub_8bitSuperclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_with_sub_8bitRegClass,
  &X86::GR64_TCRegClass,
  &X86::GR64_TCW64RegClass,
  &X86::GR64_TC_with_sub_8bitRegClass,
  &X86::GR64_TCW64_with_sub_8bitRegClass,
  &X86::GR64_TC_and_GR64_TCW64RegClass,
  nullptr
};

static const TargetRegisterClass *const VK64WMSuperclasses[] = {
  &X86::VK1RegClass,
  &X86::VK16RegClass,
  &X86::VK2RegClass,
  &X86::VK4RegClass,
  &X86::VK8RegClass,
  &X86::VK16WMRegClass,
  &X86::VK1WMRegClass,
  &X86::VK2WMRegClass,
  &X86::VK4WMRegClass,
  &X86::VK8WMRegClass,
  &X86::VK32RegClass,
  &X86::VK32WMRegClass,
  &X86::VK64RegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_TC_and_GR64_NOSP_and_GR64_TCW64Superclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_with_sub_8bitRegClass,
  &X86::GR64_NOSPRegClass,
  &X86::GR64_TCRegClass,
  &X86::GR64_TCW64RegClass,
  &X86::GR64_TC_with_sub_8bitRegClass,
  &X86::GR64_NOSP_and_GR64_TCRegClass,
  &X86::GR64_TCW64_with_sub_8bitRegClass,
  &X86::GR64_TC_and_GR64_TCW64RegClass,
  &X86::GR64_NOSP_and_GR64_TCW64RegClass,
  &X86::GR64_TCW64_and_GR64_TC_with_sub_8bitRegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREXSuperclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_with_sub_8bitRegClass,
  &X86::GR64_TCRegClass,
  &X86::GR64_NOREXRegClass,
  &X86::GR64_TC_with_sub_8bitRegClass,
  &X86::GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR64_NOREX_and_GR64_TCRegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_NOREX_NOSP_and_GR64_TCSuperclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_with_sub_8bitRegClass,
  &X86::GR64_NOSPRegClass,
  &X86::GR64_TCRegClass,
  &X86::GR64_NOREXRegClass,
  &X86::GR64_TC_with_sub_8bitRegClass,
  &X86::GR64_NOSP_and_GR64_TCRegClass,
  &X86::GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR64_NOREX_NOSPRegClass,
  &X86::GR64_NOREX_and_GR64_TCRegClass,
  &X86::GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_NOREX_and_GR64_TCW64Superclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_TCRegClass,
  &X86::GR64_NOREXRegClass,
  &X86::GR64_TCW64RegClass,
  &X86::GR64_TC_and_GR64_TCW64RegClass,
  &X86::GR64_NOREX_and_GR64_TCRegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_ABCDSuperclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_with_sub_8bitRegClass,
  &X86::GR64_NOSPRegClass,
  &X86::GR64_NOREXRegClass,
  &X86::GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR64_NOREX_NOSPRegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_with_sub_32bit_in_GR32_TCSuperclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_with_sub_8bitRegClass,
  &X86::GR64_TCRegClass,
  &X86::GR64_NOREXRegClass,
  &X86::GR64_TCW64RegClass,
  &X86::GR64_TC_with_sub_8bitRegClass,
  &X86::GR64_TCW64_with_sub_8bitRegClass,
  &X86::GR64_TC_and_GR64_TCW64RegClass,
  &X86::GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR64_NOREX_and_GR64_TCRegClass,
  &X86::GR64_TCW64_and_GR64_TC_with_sub_8bitRegClass,
  &X86::GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR64_NOREX_and_GR64_TCW64RegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TCSuperclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_with_sub_8bitRegClass,
  &X86::GR64_NOSPRegClass,
  &X86::GR64_TCRegClass,
  &X86::GR64_NOREXRegClass,
  &X86::GR64_TCW64RegClass,
  &X86::GR64_TC_with_sub_8bitRegClass,
  &X86::GR64_NOSP_and_GR64_TCRegClass,
  &X86::GR64_TCW64_with_sub_8bitRegClass,
  &X86::GR64_TC_and_GR64_TCW64RegClass,
  &X86::GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR64_NOREX_NOSPRegClass,
  &X86::GR64_NOREX_and_GR64_TCRegClass,
  &X86::GR64_NOSP_and_GR64_TCW64RegClass,
  &X86::GR64_TCW64_and_GR64_TC_with_sub_8bitRegClass,
  &X86::GR64_TC_and_GR64_NOSP_and_GR64_TCW64RegClass,
  &X86::GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR64_NOREX_NOSP_and_GR64_TCRegClass,
  &X86::GR64_NOREX_and_GR64_TCW64RegClass,
  &X86::GR64_ABCDRegClass,
  &X86::GR64_with_sub_32bit_in_GR32_TCRegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_ADSuperclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_with_sub_8bitRegClass,
  &X86::GR64_NOSPRegClass,
  &X86::GR64_TCRegClass,
  &X86::GR64_NOREXRegClass,
  &X86::GR64_TCW64RegClass,
  &X86::GR64_TC_with_sub_8bitRegClass,
  &X86::GR64_NOSP_and_GR64_TCRegClass,
  &X86::GR64_TCW64_with_sub_8bitRegClass,
  &X86::GR64_TC_and_GR64_TCW64RegClass,
  &X86::GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR64_NOREX_NOSPRegClass,
  &X86::GR64_NOREX_and_GR64_TCRegClass,
  &X86::GR64_NOSP_and_GR64_TCW64RegClass,
  &X86::GR64_TCW64_and_GR64_TC_with_sub_8bitRegClass,
  &X86::GR64_TC_and_GR64_NOSP_and_GR64_TCW64RegClass,
  &X86::GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR64_NOREX_NOSP_and_GR64_TCRegClass,
  &X86::GR64_NOREX_and_GR64_TCW64RegClass,
  &X86::GR64_ABCDRegClass,
  &X86::GR64_with_sub_32bit_in_GR32_TCRegClass,
  &X86::GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TCRegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_and_LOW32_ADDR_ACCESS_RBPSuperclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_32bitRegClass,
  &X86::GR64RegClass,
  &X86::GR64_NOREXRegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_with_sub_32bit_in_GR32_BPSPSuperclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_with_sub_8bitRegClass,
  &X86::GR64_NOREXRegClass,
  &X86::GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_with_sub_32bit_in_GR32_BSISuperclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_with_sub_8bitRegClass,
  &X86::GR64_NOSPRegClass,
  &X86::GR64_NOREXRegClass,
  &X86::GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR64_NOREX_NOSPRegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_with_sub_32bit_in_GR32_CBSuperclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_with_sub_8bitRegClass,
  &X86::GR64_NOSPRegClass,
  &X86::GR64_NOREXRegClass,
  &X86::GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR64_NOREX_NOSPRegClass,
  &X86::GR64_ABCDRegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_with_sub_32bit_in_GR32_DCSuperclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_with_sub_8bitRegClass,
  &X86::GR64_NOSPRegClass,
  &X86::GR64_TCRegClass,
  &X86::GR64_NOREXRegClass,
  &X86::GR64_TCW64RegClass,
  &X86::GR64_TC_with_sub_8bitRegClass,
  &X86::GR64_NOSP_and_GR64_TCRegClass,
  &X86::GR64_TCW64_with_sub_8bitRegClass,
  &X86::GR64_TC_and_GR64_TCW64RegClass,
  &X86::GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR64_NOREX_NOSPRegClass,
  &X86::GR64_NOREX_and_GR64_TCRegClass,
  &X86::GR64_NOSP_and_GR64_TCW64RegClass,
  &X86::GR64_TCW64_and_GR64_TC_with_sub_8bitRegClass,
  &X86::GR64_TC_and_GR64_NOSP_and_GR64_TCW64RegClass,
  &X86::GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR64_NOREX_NOSP_and_GR64_TCRegClass,
  &X86::GR64_NOREX_and_GR64_TCW64RegClass,
  &X86::GR64_ABCDRegClass,
  &X86::GR64_with_sub_32bit_in_GR32_TCRegClass,
  &X86::GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TCRegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_with_sub_32bit_in_GR32_DIBPSuperclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_with_sub_8bitRegClass,
  &X86::GR64_NOSPRegClass,
  &X86::GR64_NOREXRegClass,
  &X86::GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR64_NOREX_NOSPRegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_with_sub_32bit_in_GR32_SIDISuperclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_with_sub_8bitRegClass,
  &X86::GR64_NOSPRegClass,
  &X86::GR64_TCRegClass,
  &X86::GR64_NOREXRegClass,
  &X86::GR64_TC_with_sub_8bitRegClass,
  &X86::GR64_NOSP_and_GR64_TCRegClass,
  &X86::GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR64_NOREX_NOSPRegClass,
  &X86::GR64_NOREX_and_GR64_TCRegClass,
  &X86::GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR64_NOREX_NOSP_and_GR64_TCRegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_and_LOW32_ADDR_ACCESSSuperclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  &X86::LOW32_ADDR_ACCESSRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_32bitRegClass,
  &X86::LOW32_ADDR_ACCESS_with_sub_32bitRegClass,
  &X86::GR64RegClass,
  &X86::GR64_TCRegClass,
  &X86::GR64_NOREXRegClass,
  &X86::GR64_TCW64RegClass,
  &X86::GR64_TC_and_GR64_TCW64RegClass,
  &X86::GR64_NOREX_and_GR64_TCRegClass,
  &X86::GR64_NOREX_and_GR64_TCW64RegClass,
  &X86::GR64_and_LOW32_ADDR_ACCESS_RBPRegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_BSISuperclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_with_sub_8bitRegClass,
  &X86::GR64_NOSPRegClass,
  &X86::GR64_NOREXRegClass,
  &X86::GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR64_NOREX_NOSPRegClass,
  &X86::GR64_ABCDRegClass,
  &X86::GR64_with_sub_32bit_in_GR32_BSIRegClass,
  &X86::GR64_with_sub_32bit_in_GR32_CBRegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_with_sub_32bit_in_GR32_AD_and_GR32_DCSuperclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_with_sub_8bitRegClass,
  &X86::GR64_NOSPRegClass,
  &X86::GR64_TCRegClass,
  &X86::GR64_NOREXRegClass,
  &X86::GR64_TCW64RegClass,
  &X86::GR64_TC_with_sub_8bitRegClass,
  &X86::GR64_NOSP_and_GR64_TCRegClass,
  &X86::GR64_TCW64_with_sub_8bitRegClass,
  &X86::GR64_TC_and_GR64_TCW64RegClass,
  &X86::GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR64_NOREX_NOSPRegClass,
  &X86::GR64_NOREX_and_GR64_TCRegClass,
  &X86::GR64_NOSP_and_GR64_TCW64RegClass,
  &X86::GR64_TCW64_and_GR64_TC_with_sub_8bitRegClass,
  &X86::GR64_TC_and_GR64_NOSP_and_GR64_TCW64RegClass,
  &X86::GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR64_NOREX_NOSP_and_GR64_TCRegClass,
  &X86::GR64_NOREX_and_GR64_TCW64RegClass,
  &X86::GR64_ABCDRegClass,
  &X86::GR64_with_sub_32bit_in_GR32_TCRegClass,
  &X86::GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TCRegClass,
  &X86::GR64_ADRegClass,
  &X86::GR64_with_sub_32bit_in_GR32_DCRegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_DIBPSuperclasses[] = {
  &X86::LOW32_ADDR_ACCESS_RBPRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_32bitRegClass,
  &X86::LOW32_ADDR_ACCESS_RBP_with_sub_8bit_with_sub_32bitRegClass,
  &X86::GR64RegClass,
  &X86::GR64_with_sub_8bitRegClass,
  &X86::GR64_NOSPRegClass,
  &X86::GR64_NOREXRegClass,
  &X86::GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR64_NOREX_NOSPRegClass,
  &X86::GR64_and_LOW32_ADDR_ACCESS_RBPRegClass,
  &X86::GR64_with_sub_32bit_in_GR32_BPSPRegClass,
  &X86::GR64_with_sub_32bit_in_GR32_DIBPRegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_TCSuperclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_with_sub_8bitRegClass,
  &X86::GR64_TCRegClass,
  &X86::GR64_NOREXRegClass,
  &X86::GR64_TCW64RegClass,
  &X86::GR64_TC_with_sub_8bitRegClass,
  &X86::GR64_TCW64_with_sub_8bitRegClass,
  &X86::GR64_TC_and_GR64_TCW64RegClass,
  &X86::GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR64_NOREX_and_GR64_TCRegClass,
  &X86::GR64_TCW64_and_GR64_TC_with_sub_8bitRegClass,
  &X86::GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR64_NOREX_and_GR64_TCW64RegClass,
  &X86::GR64_with_sub_32bit_in_GR32_TCRegClass,
  &X86::GR64_with_sub_32bit_in_GR32_BPSPRegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_with_sub_32bit_in_GR32_BSI_and_GR32_SIDISuperclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_with_sub_8bitRegClass,
  &X86::GR64_NOSPRegClass,
  &X86::GR64_TCRegClass,
  &X86::GR64_NOREXRegClass,
  &X86::GR64_TC_with_sub_8bitRegClass,
  &X86::GR64_NOSP_and_GR64_TCRegClass,
  &X86::GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR64_NOREX_NOSPRegClass,
  &X86::GR64_NOREX_and_GR64_TCRegClass,
  &X86::GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR64_NOREX_NOSP_and_GR64_TCRegClass,
  &X86::GR64_with_sub_32bit_in_GR32_BSIRegClass,
  &X86::GR64_with_sub_32bit_in_GR32_SIDIRegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_with_sub_32bit_in_GR32_CB_and_GR32_DCSuperclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_with_sub_8bitRegClass,
  &X86::GR64_NOSPRegClass,
  &X86::GR64_TCRegClass,
  &X86::GR64_NOREXRegClass,
  &X86::GR64_TCW64RegClass,
  &X86::GR64_TC_with_sub_8bitRegClass,
  &X86::GR64_NOSP_and_GR64_TCRegClass,
  &X86::GR64_TCW64_with_sub_8bitRegClass,
  &X86::GR64_TC_and_GR64_TCW64RegClass,
  &X86::GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR64_NOREX_NOSPRegClass,
  &X86::GR64_NOREX_and_GR64_TCRegClass,
  &X86::GR64_NOSP_and_GR64_TCW64RegClass,
  &X86::GR64_TCW64_and_GR64_TC_with_sub_8bitRegClass,
  &X86::GR64_TC_and_GR64_NOSP_and_GR64_TCW64RegClass,
  &X86::GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR64_NOREX_NOSP_and_GR64_TCRegClass,
  &X86::GR64_NOREX_and_GR64_TCW64RegClass,
  &X86::GR64_ABCDRegClass,
  &X86::GR64_with_sub_32bit_in_GR32_TCRegClass,
  &X86::GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TCRegClass,
  &X86::GR64_with_sub_32bit_in_GR32_CBRegClass,
  &X86::GR64_with_sub_32bit_in_GR32_DCRegClass,
  nullptr
};

static const TargetRegisterClass *const GR64_with_sub_32bit_in_GR32_DIBP_and_GR32_SIDISuperclasses[] = {
  &X86::GR64RegClass,
  &X86::GR64_with_sub_8bitRegClass,
  &X86::GR64_NOSPRegClass,
  &X86::GR64_TCRegClass,
  &X86::GR64_NOREXRegClass,
  &X86::GR64_TC_with_sub_8bitRegClass,
  &X86::GR64_NOSP_and_GR64_TCRegClass,
  &X86::GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR64_NOREX_NOSPRegClass,
  &X86::GR64_NOREX_and_GR64_TCRegClass,
  &X86::GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREXRegClass,
  &X86::GR64_NOREX_NOSP_and_GR64_TCRegClass,
  &X86::GR64_with_sub_32bit_in_GR32_DIBPRegClass,
  &X86::GR64_with_sub_32bit_in_GR32_SIDIRegClass,
  nullptr
};

static const TargetRegisterClass *const RFP80Superclasses[] = {
  &X86::RFP32RegClass,
  &X86::RFP64RegClass,
  nullptr
};

static const TargetRegisterClass *const VR128XSuperclasses[] = {
  &X86::FR32XRegClass,
  &X86::FR64XRegClass,
  nullptr
};

static const TargetRegisterClass *const VR128Superclasses[] = {
  &X86::FR32XRegClass,
  &X86::FR32RegClass,
  &X86::FR64XRegClass,
  &X86::FR64RegClass,
  &X86::VR128XRegClass,
  nullptr
};

static const TargetRegisterClass *const VR128HSuperclasses[] = {
  &X86::FR32XRegClass,
  &X86::FR32RegClass,
  &X86::FR64XRegClass,
  &X86::FR64RegClass,
  &X86::VR128XRegClass,
  &X86::VR128RegClass,
  nullptr
};

static const TargetRegisterClass *const VR128LSuperclasses[] = {
  &X86::FR32XRegClass,
  &X86::FR32RegClass,
  &X86::FR64XRegClass,
  &X86::FR64RegClass,
  &X86::VR128XRegClass,
  &X86::VR128RegClass,
  nullptr
};

static const TargetRegisterClass *const VR256Superclasses[] = {
  &X86::VR256XRegClass,
  nullptr
};

static const TargetRegisterClass *const VR256HSuperclasses[] = {
  &X86::VR256XRegClass,
  &X86::VR256RegClass,
  nullptr
};

static const TargetRegisterClass *const VR256LSuperclasses[] = {
  &X86::VR256XRegClass,
  &X86::VR256RegClass,
  nullptr
};

static const TargetRegisterClass *const VR512_with_sub_xmm_in_FR32Superclasses[] = {
  &X86::VR512RegClass,
  nullptr
};

static const TargetRegisterClass *const VR512_with_sub_xmm_in_VR128HSuperclasses[] = {
  &X86::VR512RegClass,
  &X86::VR512_with_sub_xmm_in_FR32RegClass,
  nullptr
};

static const TargetRegisterClass *const VR512_with_sub_xmm_in_VR128LSuperclasses[] = {
  &X86::VR512RegClass,
  &X86::VR512_with_sub_xmm_in_FR32RegClass,
  nullptr
};


static inline unsigned GR8AltOrderSelect(const MachineFunction &MF) {
    return MF.getSubtarget<X86Subtarget>().is64Bit();
  }

static ArrayRef<MCPhysReg> GR8GetRawAllocationOrder(const MachineFunction &MF) {
  static const MCPhysReg AltOrder1[] = { X86::AL, X86::CL, X86::DL, X86::BL, X86::SIL, X86::DIL, X86::BPL, X86::SPL, X86::R8B, X86::R9B, X86::R10B, X86::R11B, X86::R14B, X86::R15B, X86::R12B, X86::R13B };
  const MCRegisterClass &MCR = X86MCRegisterClasses[X86::GR8RegClassID];
  const ArrayRef<MCPhysReg> Order[] = {
    makeArrayRef(MCR.begin(), MCR.getNumRegs()),
    makeArrayRef(AltOrder1)
  };
  const unsigned Select = GR8AltOrderSelect(MF);
  assert(Select < 2);
  return Order[Select];
}

static inline unsigned GR8_NOREXAltOrderSelect(const MachineFunction &MF) {
    return MF.getSubtarget<X86Subtarget>().is64Bit();
  }

static ArrayRef<MCPhysReg> GR8_NOREXGetRawAllocationOrder(const MachineFunction &MF) {
  static const MCPhysReg AltOrder1[] = { X86::AL, X86::CL, X86::DL, X86::BL };
  const MCRegisterClass &MCR = X86MCRegisterClasses[X86::GR8_NOREXRegClassID];
  const ArrayRef<MCPhysReg> Order[] = {
    makeArrayRef(MCR.begin(), MCR.getNumRegs()),
    makeArrayRef(AltOrder1)
  };
  const unsigned Select = GR8_NOREXAltOrderSelect(MF);
  assert(Select < 2);
  return Order[Select];
}

namespace X86 {   // Register class instances
  extern const TargetRegisterClass GR8RegClass = {
    &X86MCRegisterClasses[GR8RegClassID],
    GR8SubClassMask,
    SuperRegIdxSeqs + 2,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    NullRegClasses,
    GR8GetRawAllocationOrder
  };

  extern const TargetRegisterClass GRH8RegClass = {
    &X86MCRegisterClasses[GRH8RegClassID],
    GRH8SubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    NullRegClasses,
    nullptr
  };

  extern const TargetRegisterClass GR8_NOREXRegClass = {
    &X86MCRegisterClasses[GR8_NOREXRegClassID],
    GR8_NOREXSubClassMask,
    SuperRegIdxSeqs + 2,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR8_NOREXSuperclasses,
    GR8_NOREXGetRawAllocationOrder
  };

  extern const TargetRegisterClass GR8_ABCD_HRegClass = {
    &X86MCRegisterClasses[GR8_ABCD_HRegClassID],
    GR8_ABCD_HSubClassMask,
    SuperRegIdxSeqs + 3,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR8_ABCD_HSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR8_ABCD_LRegClass = {
    &X86MCRegisterClasses[GR8_ABCD_LRegClassID],
    GR8_ABCD_LSubClassMask,
    SuperRegIdxSeqs + 0,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR8_ABCD_LSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GRH16RegClass = {
    &X86MCRegisterClasses[GRH16RegClassID],
    GRH16SubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    NullRegClasses,
    nullptr
  };

  extern const TargetRegisterClass GR16RegClass = {
    &X86MCRegisterClasses[GR16RegClassID],
    GR16SubClassMask,
    SuperRegIdxSeqs + 5,
    LaneBitmask(0x00000003),
    0,
    true, /* HasDisjunctSubRegs */
    true, /* CoveredBySubRegs */
    NullRegClasses,
    nullptr
  };

  extern const TargetRegisterClass GR16_NOREXRegClass = {
    &X86MCRegisterClasses[GR16_NOREXRegClassID],
    GR16_NOREXSubClassMask,
    SuperRegIdxSeqs + 5,
    LaneBitmask(0x00000003),
    0,
    true, /* HasDisjunctSubRegs */
    true, /* CoveredBySubRegs */
    GR16_NOREXSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass VK1RegClass = {
    &X86MCRegisterClasses[VK1RegClassID],
    VK1SubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    VK1Superclasses,
    nullptr
  };

  extern const TargetRegisterClass VK16RegClass = {
    &X86MCRegisterClasses[VK16RegClassID],
    VK16SubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    VK16Superclasses,
    nullptr
  };

  extern const TargetRegisterClass VK2RegClass = {
    &X86MCRegisterClasses[VK2RegClassID],
    VK2SubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    VK2Superclasses,
    nullptr
  };

  extern const TargetRegisterClass VK4RegClass = {
    &X86MCRegisterClasses[VK4RegClassID],
    VK4SubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    VK4Superclasses,
    nullptr
  };

  extern const TargetRegisterClass VK8RegClass = {
    &X86MCRegisterClasses[VK8RegClassID],
    VK8SubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    VK8Superclasses,
    nullptr
  };

  extern const TargetRegisterClass VK16WMRegClass = {
    &X86MCRegisterClasses[VK16WMRegClassID],
    VK16WMSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    VK16WMSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass VK1WMRegClass = {
    &X86MCRegisterClasses[VK1WMRegClassID],
    VK1WMSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    VK1WMSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass VK2WMRegClass = {
    &X86MCRegisterClasses[VK2WMRegClassID],
    VK2WMSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    VK2WMSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass VK4WMRegClass = {
    &X86MCRegisterClasses[VK4WMRegClassID],
    VK4WMSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    VK4WMSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass VK8WMRegClass = {
    &X86MCRegisterClasses[VK8WMRegClassID],
    VK8WMSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    VK8WMSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass SEGMENT_REGRegClass = {
    &X86MCRegisterClasses[SEGMENT_REGRegClassID],
    SEGMENT_REGSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    NullRegClasses,
    nullptr
  };

  extern const TargetRegisterClass GR16_ABCDRegClass = {
    &X86MCRegisterClasses[GR16_ABCDRegClassID],
    GR16_ABCDSubClassMask,
    SuperRegIdxSeqs + 5,
    LaneBitmask(0x00000003),
    0,
    true, /* HasDisjunctSubRegs */
    true, /* CoveredBySubRegs */
    GR16_ABCDSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass FPCCRRegClass = {
    &X86MCRegisterClasses[FPCCRRegClassID],
    FPCCRSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    NullRegClasses,
    nullptr
  };

  extern const TargetRegisterClass FR32XRegClass = {
    &X86MCRegisterClasses[FR32XRegClassID],
    FR32XSubClassMask,
    SuperRegIdxSeqs + 9,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    NullRegClasses,
    nullptr
  };

  extern const TargetRegisterClass LOW32_ADDR_ACCESS_RBPRegClass = {
    &X86MCRegisterClasses[LOW32_ADDR_ACCESS_RBPRegClassID],
    LOW32_ADDR_ACCESS_RBPSubClassMask,
    SuperRegIdxSeqs + 7,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    NullRegClasses,
    nullptr
  };

  extern const TargetRegisterClass LOW32_ADDR_ACCESSRegClass = {
    &X86MCRegisterClasses[LOW32_ADDR_ACCESSRegClassID],
    LOW32_ADDR_ACCESSSubClassMask,
    SuperRegIdxSeqs + 7,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    LOW32_ADDR_ACCESSSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClass = {
    &X86MCRegisterClasses[LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClassID],
    LOW32_ADDR_ACCESS_RBP_with_sub_8bitSubClassMask,
    SuperRegIdxSeqs + 7,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    LOW32_ADDR_ACCESS_RBP_with_sub_8bitSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass DEBUG_REGRegClass = {
    &X86MCRegisterClasses[DEBUG_REGRegClassID],
    DEBUG_REGSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    NullRegClasses,
    nullptr
  };

  extern const TargetRegisterClass FR32RegClass = {
    &X86MCRegisterClasses[FR32RegClassID],
    FR32SubClassMask,
    SuperRegIdxSeqs + 9,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    FR32Superclasses,
    nullptr
  };

  extern const TargetRegisterClass GR32RegClass = {
    &X86MCRegisterClasses[GR32RegClassID],
    GR32SubClassMask,
    SuperRegIdxSeqs + 7,
    LaneBitmask(0x00000007),
    0,
    true, /* HasDisjunctSubRegs */
    true, /* CoveredBySubRegs */
    GR32Superclasses,
    nullptr
  };

  extern const TargetRegisterClass GR32_NOSPRegClass = {
    &X86MCRegisterClasses[GR32_NOSPRegClassID],
    GR32_NOSPSubClassMask,
    SuperRegIdxSeqs + 7,
    LaneBitmask(0x00000007),
    0,
    true, /* HasDisjunctSubRegs */
    true, /* CoveredBySubRegs */
    GR32_NOSPSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXRegClass = {
    &X86MCRegisterClasses[LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXRegClassID],
    LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXSubClassMask,
    SuperRegIdxSeqs + 7,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR32_NOREXRegClass = {
    &X86MCRegisterClasses[GR32_NOREXRegClassID],
    GR32_NOREXSubClassMask,
    SuperRegIdxSeqs + 7,
    LaneBitmask(0x00000007),
    0,
    true, /* HasDisjunctSubRegs */
    true, /* CoveredBySubRegs */
    GR32_NOREXSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass VK32RegClass = {
    &X86MCRegisterClasses[VK32RegClassID],
    VK32SubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    VK32Superclasses,
    nullptr
  };

  extern const TargetRegisterClass GR32_NOREX_NOSPRegClass = {
    &X86MCRegisterClasses[GR32_NOREX_NOSPRegClassID],
    GR32_NOREX_NOSPSubClassMask,
    SuperRegIdxSeqs + 7,
    LaneBitmask(0x00000007),
    0,
    true, /* HasDisjunctSubRegs */
    true, /* CoveredBySubRegs */
    GR32_NOREX_NOSPSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass RFP32RegClass = {
    &X86MCRegisterClasses[RFP32RegClassID],
    RFP32SubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    NullRegClasses,
    nullptr
  };

  extern const TargetRegisterClass VK32WMRegClass = {
    &X86MCRegisterClasses[VK32WMRegClassID],
    VK32WMSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    VK32WMSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR32_ABCDRegClass = {
    &X86MCRegisterClasses[GR32_ABCDRegClassID],
    GR32_ABCDSubClassMask,
    SuperRegIdxSeqs + 7,
    LaneBitmask(0x00000007),
    0,
    true, /* HasDisjunctSubRegs */
    true, /* CoveredBySubRegs */
    GR32_ABCDSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR32_TCRegClass = {
    &X86MCRegisterClasses[GR32_TCRegClassID],
    GR32_TCSubClassMask,
    SuperRegIdxSeqs + 7,
    LaneBitmask(0x00000007),
    0,
    true, /* HasDisjunctSubRegs */
    true, /* CoveredBySubRegs */
    GR32_TCSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR32_ABCD_and_GR32_TCRegClass = {
    &X86MCRegisterClasses[GR32_ABCD_and_GR32_TCRegClassID],
    GR32_ABCD_and_GR32_TCSubClassMask,
    SuperRegIdxSeqs + 7,
    LaneBitmask(0x00000007),
    0,
    true, /* HasDisjunctSubRegs */
    true, /* CoveredBySubRegs */
    GR32_ABCD_and_GR32_TCSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR32_ADRegClass = {
    &X86MCRegisterClasses[GR32_ADRegClassID],
    GR32_ADSubClassMask,
    SuperRegIdxSeqs + 7,
    LaneBitmask(0x00000007),
    0,
    true, /* HasDisjunctSubRegs */
    true, /* CoveredBySubRegs */
    GR32_ADSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR32_BPSPRegClass = {
    &X86MCRegisterClasses[GR32_BPSPRegClassID],
    GR32_BPSPSubClassMask,
    SuperRegIdxSeqs + 7,
    LaneBitmask(0x00000007),
    0,
    true, /* HasDisjunctSubRegs */
    true, /* CoveredBySubRegs */
    GR32_BPSPSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR32_BSIRegClass = {
    &X86MCRegisterClasses[GR32_BSIRegClassID],
    GR32_BSISubClassMask,
    SuperRegIdxSeqs + 7,
    LaneBitmask(0x00000007),
    0,
    true, /* HasDisjunctSubRegs */
    true, /* CoveredBySubRegs */
    GR32_BSISuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR32_CBRegClass = {
    &X86MCRegisterClasses[GR32_CBRegClassID],
    GR32_CBSubClassMask,
    SuperRegIdxSeqs + 7,
    LaneBitmask(0x00000007),
    0,
    true, /* HasDisjunctSubRegs */
    true, /* CoveredBySubRegs */
    GR32_CBSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR32_DCRegClass = {
    &X86MCRegisterClasses[GR32_DCRegClassID],
    GR32_DCSubClassMask,
    SuperRegIdxSeqs + 7,
    LaneBitmask(0x00000007),
    0,
    true, /* HasDisjunctSubRegs */
    true, /* CoveredBySubRegs */
    GR32_DCSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR32_DIBPRegClass = {
    &X86MCRegisterClasses[GR32_DIBPRegClassID],
    GR32_DIBPSubClassMask,
    SuperRegIdxSeqs + 7,
    LaneBitmask(0x00000007),
    0,
    true, /* HasDisjunctSubRegs */
    true, /* CoveredBySubRegs */
    GR32_DIBPSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR32_SIDIRegClass = {
    &X86MCRegisterClasses[GR32_SIDIRegClassID],
    GR32_SIDISubClassMask,
    SuperRegIdxSeqs + 7,
    LaneBitmask(0x00000007),
    0,
    true, /* HasDisjunctSubRegs */
    true, /* CoveredBySubRegs */
    GR32_SIDISuperclasses,
    nullptr
  };

  extern const TargetRegisterClass LOW32_ADDR_ACCESS_RBP_with_sub_32bitRegClass = {
    &X86MCRegisterClasses[LOW32_ADDR_ACCESS_RBP_with_sub_32bitRegClassID],
    LOW32_ADDR_ACCESS_RBP_with_sub_32bitSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    LOW32_ADDR_ACCESS_RBP_with_sub_32bitSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass CCRRegClass = {
    &X86MCRegisterClasses[CCRRegClassID],
    CCRSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    NullRegClasses,
    nullptr
  };

  extern const TargetRegisterClass DFCCRRegClass = {
    &X86MCRegisterClasses[DFCCRRegClassID],
    DFCCRSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    NullRegClasses,
    nullptr
  };

  extern const TargetRegisterClass GR32_ABCD_and_GR32_BSIRegClass = {
    &X86MCRegisterClasses[GR32_ABCD_and_GR32_BSIRegClassID],
    GR32_ABCD_and_GR32_BSISubClassMask,
    SuperRegIdxSeqs + 7,
    LaneBitmask(0x00000007),
    0,
    true, /* HasDisjunctSubRegs */
    true, /* CoveredBySubRegs */
    GR32_ABCD_and_GR32_BSISuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR32_AD_and_GR32_DCRegClass = {
    &X86MCRegisterClasses[GR32_AD_and_GR32_DCRegClassID],
    GR32_AD_and_GR32_DCSubClassMask,
    SuperRegIdxSeqs + 7,
    LaneBitmask(0x00000007),
    0,
    true, /* HasDisjunctSubRegs */
    true, /* CoveredBySubRegs */
    GR32_AD_and_GR32_DCSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR32_BPSP_and_GR32_DIBPRegClass = {
    &X86MCRegisterClasses[GR32_BPSP_and_GR32_DIBPRegClassID],
    GR32_BPSP_and_GR32_DIBPSubClassMask,
    SuperRegIdxSeqs + 7,
    LaneBitmask(0x00000007),
    0,
    true, /* HasDisjunctSubRegs */
    true, /* CoveredBySubRegs */
    GR32_BPSP_and_GR32_DIBPSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR32_BPSP_and_GR32_TCRegClass = {
    &X86MCRegisterClasses[GR32_BPSP_and_GR32_TCRegClassID],
    GR32_BPSP_and_GR32_TCSubClassMask,
    SuperRegIdxSeqs + 7,
    LaneBitmask(0x00000007),
    0,
    true, /* HasDisjunctSubRegs */
    true, /* CoveredBySubRegs */
    GR32_BPSP_and_GR32_TCSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR32_BSI_and_GR32_SIDIRegClass = {
    &X86MCRegisterClasses[GR32_BSI_and_GR32_SIDIRegClassID],
    GR32_BSI_and_GR32_SIDISubClassMask,
    SuperRegIdxSeqs + 7,
    LaneBitmask(0x00000007),
    0,
    true, /* HasDisjunctSubRegs */
    true, /* CoveredBySubRegs */
    GR32_BSI_and_GR32_SIDISuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR32_CB_and_GR32_DCRegClass = {
    &X86MCRegisterClasses[GR32_CB_and_GR32_DCRegClassID],
    GR32_CB_and_GR32_DCSubClassMask,
    SuperRegIdxSeqs + 7,
    LaneBitmask(0x00000007),
    0,
    true, /* HasDisjunctSubRegs */
    true, /* CoveredBySubRegs */
    GR32_CB_and_GR32_DCSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR32_DIBP_and_GR32_SIDIRegClass = {
    &X86MCRegisterClasses[GR32_DIBP_and_GR32_SIDIRegClassID],
    GR32_DIBP_and_GR32_SIDISubClassMask,
    SuperRegIdxSeqs + 7,
    LaneBitmask(0x00000007),
    0,
    true, /* HasDisjunctSubRegs */
    true, /* CoveredBySubRegs */
    GR32_DIBP_and_GR32_SIDISuperclasses,
    nullptr
  };

  extern const TargetRegisterClass LOW32_ADDR_ACCESS_RBP_with_sub_8bit_with_sub_32bitRegClass = {
    &X86MCRegisterClasses[LOW32_ADDR_ACCESS_RBP_with_sub_8bit_with_sub_32bitRegClassID],
    LOW32_ADDR_ACCESS_RBP_with_sub_8bit_with_sub_32bitSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    LOW32_ADDR_ACCESS_RBP_with_sub_8bit_with_sub_32bitSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass LOW32_ADDR_ACCESS_with_sub_32bitRegClass = {
    &X86MCRegisterClasses[LOW32_ADDR_ACCESS_with_sub_32bitRegClassID],
    LOW32_ADDR_ACCESS_with_sub_32bitSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    LOW32_ADDR_ACCESS_with_sub_32bitSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass RFP64RegClass = {
    &X86MCRegisterClasses[RFP64RegClassID],
    RFP64SubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    RFP64Superclasses,
    nullptr
  };

  extern const TargetRegisterClass FR64XRegClass = {
    &X86MCRegisterClasses[FR64XRegClassID],
    FR64XSubClassMask,
    SuperRegIdxSeqs + 9,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    FR64XSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64RegClass = {
    &X86MCRegisterClasses[GR64RegClassID],
    GR64SubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    NullRegClasses,
    nullptr
  };

  extern const TargetRegisterClass CONTROL_REGRegClass = {
    &X86MCRegisterClasses[CONTROL_REGRegClassID],
    CONTROL_REGSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    NullRegClasses,
    nullptr
  };

  extern const TargetRegisterClass FR64RegClass = {
    &X86MCRegisterClasses[FR64RegClassID],
    FR64SubClassMask,
    SuperRegIdxSeqs + 9,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    FR64Superclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_with_sub_8bitRegClass = {
    &X86MCRegisterClasses[GR64_with_sub_8bitRegClassID],
    GR64_with_sub_8bitSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_with_sub_8bitSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_NOSPRegClass = {
    &X86MCRegisterClasses[GR64_NOSPRegClassID],
    GR64_NOSPSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_NOSPSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_TCRegClass = {
    &X86MCRegisterClasses[GR64_TCRegClassID],
    GR64_TCSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_TCSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_NOREXRegClass = {
    &X86MCRegisterClasses[GR64_NOREXRegClassID],
    GR64_NOREXSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_NOREXSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_TCW64RegClass = {
    &X86MCRegisterClasses[GR64_TCW64RegClassID],
    GR64_TCW64SubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_TCW64Superclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_TC_with_sub_8bitRegClass = {
    &X86MCRegisterClasses[GR64_TC_with_sub_8bitRegClassID],
    GR64_TC_with_sub_8bitSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_TC_with_sub_8bitSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_NOSP_and_GR64_TCRegClass = {
    &X86MCRegisterClasses[GR64_NOSP_and_GR64_TCRegClassID],
    GR64_NOSP_and_GR64_TCSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_NOSP_and_GR64_TCSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_TCW64_with_sub_8bitRegClass = {
    &X86MCRegisterClasses[GR64_TCW64_with_sub_8bitRegClassID],
    GR64_TCW64_with_sub_8bitSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_TCW64_with_sub_8bitSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_TC_and_GR64_TCW64RegClass = {
    &X86MCRegisterClasses[GR64_TC_and_GR64_TCW64RegClassID],
    GR64_TC_and_GR64_TCW64SubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_TC_and_GR64_TCW64Superclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_with_sub_16bit_in_GR16_NOREXRegClass = {
    &X86MCRegisterClasses[GR64_with_sub_16bit_in_GR16_NOREXRegClassID],
    GR64_with_sub_16bit_in_GR16_NOREXSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_with_sub_16bit_in_GR16_NOREXSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass VK64RegClass = {
    &X86MCRegisterClasses[VK64RegClassID],
    VK64SubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    VK64Superclasses,
    nullptr
  };

  extern const TargetRegisterClass VR64RegClass = {
    &X86MCRegisterClasses[VR64RegClassID],
    VR64SubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    NullRegClasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_NOREX_NOSPRegClass = {
    &X86MCRegisterClasses[GR64_NOREX_NOSPRegClassID],
    GR64_NOREX_NOSPSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_NOREX_NOSPSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_NOREX_and_GR64_TCRegClass = {
    &X86MCRegisterClasses[GR64_NOREX_and_GR64_TCRegClassID],
    GR64_NOREX_and_GR64_TCSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_NOREX_and_GR64_TCSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_NOSP_and_GR64_TCW64RegClass = {
    &X86MCRegisterClasses[GR64_NOSP_and_GR64_TCW64RegClassID],
    GR64_NOSP_and_GR64_TCW64SubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_NOSP_and_GR64_TCW64Superclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_TCW64_and_GR64_TC_with_sub_8bitRegClass = {
    &X86MCRegisterClasses[GR64_TCW64_and_GR64_TC_with_sub_8bitRegClassID],
    GR64_TCW64_and_GR64_TC_with_sub_8bitSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_TCW64_and_GR64_TC_with_sub_8bitSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass VK64WMRegClass = {
    &X86MCRegisterClasses[VK64WMRegClassID],
    VK64WMSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    VK64WMSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_TC_and_GR64_NOSP_and_GR64_TCW64RegClass = {
    &X86MCRegisterClasses[GR64_TC_and_GR64_NOSP_and_GR64_TCW64RegClassID],
    GR64_TC_and_GR64_NOSP_and_GR64_TCW64SubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_TC_and_GR64_NOSP_and_GR64_TCW64Superclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREXRegClass = {
    &X86MCRegisterClasses[GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREXRegClassID],
    GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREXSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREXSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_NOREX_NOSP_and_GR64_TCRegClass = {
    &X86MCRegisterClasses[GR64_NOREX_NOSP_and_GR64_TCRegClassID],
    GR64_NOREX_NOSP_and_GR64_TCSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_NOREX_NOSP_and_GR64_TCSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_NOREX_and_GR64_TCW64RegClass = {
    &X86MCRegisterClasses[GR64_NOREX_and_GR64_TCW64RegClassID],
    GR64_NOREX_and_GR64_TCW64SubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_NOREX_and_GR64_TCW64Superclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_ABCDRegClass = {
    &X86MCRegisterClasses[GR64_ABCDRegClassID],
    GR64_ABCDSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_ABCDSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_TCRegClass = {
    &X86MCRegisterClasses[GR64_with_sub_32bit_in_GR32_TCRegClassID],
    GR64_with_sub_32bit_in_GR32_TCSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_with_sub_32bit_in_GR32_TCSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TCRegClass = {
    &X86MCRegisterClasses[GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TCRegClassID],
    GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TCSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TCSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_ADRegClass = {
    &X86MCRegisterClasses[GR64_ADRegClassID],
    GR64_ADSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_ADSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_and_LOW32_ADDR_ACCESS_RBPRegClass = {
    &X86MCRegisterClasses[GR64_and_LOW32_ADDR_ACCESS_RBPRegClassID],
    GR64_and_LOW32_ADDR_ACCESS_RBPSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_and_LOW32_ADDR_ACCESS_RBPSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_BPSPRegClass = {
    &X86MCRegisterClasses[GR64_with_sub_32bit_in_GR32_BPSPRegClassID],
    GR64_with_sub_32bit_in_GR32_BPSPSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_with_sub_32bit_in_GR32_BPSPSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_BSIRegClass = {
    &X86MCRegisterClasses[GR64_with_sub_32bit_in_GR32_BSIRegClassID],
    GR64_with_sub_32bit_in_GR32_BSISubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_with_sub_32bit_in_GR32_BSISuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_CBRegClass = {
    &X86MCRegisterClasses[GR64_with_sub_32bit_in_GR32_CBRegClassID],
    GR64_with_sub_32bit_in_GR32_CBSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_with_sub_32bit_in_GR32_CBSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_DCRegClass = {
    &X86MCRegisterClasses[GR64_with_sub_32bit_in_GR32_DCRegClassID],
    GR64_with_sub_32bit_in_GR32_DCSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_with_sub_32bit_in_GR32_DCSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_DIBPRegClass = {
    &X86MCRegisterClasses[GR64_with_sub_32bit_in_GR32_DIBPRegClassID],
    GR64_with_sub_32bit_in_GR32_DIBPSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_with_sub_32bit_in_GR32_DIBPSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_SIDIRegClass = {
    &X86MCRegisterClasses[GR64_with_sub_32bit_in_GR32_SIDIRegClassID],
    GR64_with_sub_32bit_in_GR32_SIDISubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_with_sub_32bit_in_GR32_SIDISuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_and_LOW32_ADDR_ACCESSRegClass = {
    &X86MCRegisterClasses[GR64_and_LOW32_ADDR_ACCESSRegClassID],
    GR64_and_LOW32_ADDR_ACCESSSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_and_LOW32_ADDR_ACCESSSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_BSIRegClass = {
    &X86MCRegisterClasses[GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_BSIRegClassID],
    GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_BSISubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_BSISuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_AD_and_GR32_DCRegClass = {
    &X86MCRegisterClasses[GR64_with_sub_32bit_in_GR32_AD_and_GR32_DCRegClassID],
    GR64_with_sub_32bit_in_GR32_AD_and_GR32_DCSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_with_sub_32bit_in_GR32_AD_and_GR32_DCSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_DIBPRegClass = {
    &X86MCRegisterClasses[GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_DIBPRegClassID],
    GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_DIBPSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_DIBPSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_TCRegClass = {
    &X86MCRegisterClasses[GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_TCRegClassID],
    GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_TCSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_TCSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_BSI_and_GR32_SIDIRegClass = {
    &X86MCRegisterClasses[GR64_with_sub_32bit_in_GR32_BSI_and_GR32_SIDIRegClassID],
    GR64_with_sub_32bit_in_GR32_BSI_and_GR32_SIDISubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_with_sub_32bit_in_GR32_BSI_and_GR32_SIDISuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_CB_and_GR32_DCRegClass = {
    &X86MCRegisterClasses[GR64_with_sub_32bit_in_GR32_CB_and_GR32_DCRegClassID],
    GR64_with_sub_32bit_in_GR32_CB_and_GR32_DCSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_with_sub_32bit_in_GR32_CB_and_GR32_DCSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass GR64_with_sub_32bit_in_GR32_DIBP_and_GR32_SIDIRegClass = {
    &X86MCRegisterClasses[GR64_with_sub_32bit_in_GR32_DIBP_and_GR32_SIDIRegClassID],
    GR64_with_sub_32bit_in_GR32_DIBP_and_GR32_SIDISubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x0000000F),
    0,
    true, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    GR64_with_sub_32bit_in_GR32_DIBP_and_GR32_SIDISuperclasses,
    nullptr
  };

  extern const TargetRegisterClass RSTRegClass = {
    &X86MCRegisterClasses[RSTRegClassID],
    RSTSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    NullRegClasses,
    nullptr
  };

  extern const TargetRegisterClass RFP80RegClass = {
    &X86MCRegisterClasses[RFP80RegClassID],
    RFP80SubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    RFP80Superclasses,
    nullptr
  };

  extern const TargetRegisterClass RFP80_7RegClass = {
    &X86MCRegisterClasses[RFP80_7RegClassID],
    RFP80_7SubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    NullRegClasses,
    nullptr
  };

  extern const TargetRegisterClass VR128XRegClass = {
    &X86MCRegisterClasses[VR128XRegClassID],
    VR128XSubClassMask,
    SuperRegIdxSeqs + 9,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    VR128XSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass VR128RegClass = {
    &X86MCRegisterClasses[VR128RegClassID],
    VR128SubClassMask,
    SuperRegIdxSeqs + 9,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    VR128Superclasses,
    nullptr
  };

  extern const TargetRegisterClass VR128HRegClass = {
    &X86MCRegisterClasses[VR128HRegClassID],
    VR128HSubClassMask,
    SuperRegIdxSeqs + 9,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    VR128HSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass VR128LRegClass = {
    &X86MCRegisterClasses[VR128LRegClassID],
    VR128LSubClassMask,
    SuperRegIdxSeqs + 9,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    VR128LSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass BNDRRegClass = {
    &X86MCRegisterClasses[BNDRRegClassID],
    BNDRSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000001),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    NullRegClasses,
    nullptr
  };

  extern const TargetRegisterClass VR256XRegClass = {
    &X86MCRegisterClasses[VR256XRegClassID],
    VR256XSubClassMask,
    SuperRegIdxSeqs + 11,
    LaneBitmask(0x00000010),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    NullRegClasses,
    nullptr
  };

  extern const TargetRegisterClass VR256RegClass = {
    &X86MCRegisterClasses[VR256RegClassID],
    VR256SubClassMask,
    SuperRegIdxSeqs + 11,
    LaneBitmask(0x00000010),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    VR256Superclasses,
    nullptr
  };

  extern const TargetRegisterClass VR256HRegClass = {
    &X86MCRegisterClasses[VR256HRegClassID],
    VR256HSubClassMask,
    SuperRegIdxSeqs + 11,
    LaneBitmask(0x00000010),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    VR256HSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass VR256LRegClass = {
    &X86MCRegisterClasses[VR256LRegClassID],
    VR256LSubClassMask,
    SuperRegIdxSeqs + 11,
    LaneBitmask(0x00000010),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    VR256LSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass VR512RegClass = {
    &X86MCRegisterClasses[VR512RegClassID],
    VR512SubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000010),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    NullRegClasses,
    nullptr
  };

  extern const TargetRegisterClass VR512_with_sub_xmm_in_FR32RegClass = {
    &X86MCRegisterClasses[VR512_with_sub_xmm_in_FR32RegClassID],
    VR512_with_sub_xmm_in_FR32SubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000010),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    VR512_with_sub_xmm_in_FR32Superclasses,
    nullptr
  };

  extern const TargetRegisterClass VR512_with_sub_xmm_in_VR128HRegClass = {
    &X86MCRegisterClasses[VR512_with_sub_xmm_in_VR128HRegClassID],
    VR512_with_sub_xmm_in_VR128HSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000010),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    VR512_with_sub_xmm_in_VR128HSuperclasses,
    nullptr
  };

  extern const TargetRegisterClass VR512_with_sub_xmm_in_VR128LRegClass = {
    &X86MCRegisterClasses[VR512_with_sub_xmm_in_VR128LRegClassID],
    VR512_with_sub_xmm_in_VR128LSubClassMask,
    SuperRegIdxSeqs + 1,
    LaneBitmask(0x00000010),
    0,
    false, /* HasDisjunctSubRegs */
    false, /* CoveredBySubRegs */
    VR512_with_sub_xmm_in_VR128LSuperclasses,
    nullptr
  };

} // end namespace X86

namespace {
  const TargetRegisterClass* const RegisterClasses[] = {
    &X86::GR8RegClass,
    &X86::GRH8RegClass,
    &X86::GR8_NOREXRegClass,
    &X86::GR8_ABCD_HRegClass,
    &X86::GR8_ABCD_LRegClass,
    &X86::GRH16RegClass,
    &X86::GR16RegClass,
    &X86::GR16_NOREXRegClass,
    &X86::VK1RegClass,
    &X86::VK16RegClass,
    &X86::VK2RegClass,
    &X86::VK4RegClass,
    &X86::VK8RegClass,
    &X86::VK16WMRegClass,
    &X86::VK1WMRegClass,
    &X86::VK2WMRegClass,
    &X86::VK4WMRegClass,
    &X86::VK8WMRegClass,
    &X86::SEGMENT_REGRegClass,
    &X86::GR16_ABCDRegClass,
    &X86::FPCCRRegClass,
    &X86::FR32XRegClass,
    &X86::LOW32_ADDR_ACCESS_RBPRegClass,
    &X86::LOW32_ADDR_ACCESSRegClass,
    &X86::LOW32_ADDR_ACCESS_RBP_with_sub_8bitRegClass,
    &X86::DEBUG_REGRegClass,
    &X86::FR32RegClass,
    &X86::GR32RegClass,
    &X86::GR32_NOSPRegClass,
    &X86::LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREXRegClass,
    &X86::GR32_NOREXRegClass,
    &X86::VK32RegClass,
    &X86::GR32_NOREX_NOSPRegClass,
    &X86::RFP32RegClass,
    &X86::VK32WMRegClass,
    &X86::GR32_ABCDRegClass,
    &X86::GR32_TCRegClass,
    &X86::GR32_ABCD_and_GR32_TCRegClass,
    &X86::GR32_ADRegClass,
    &X86::GR32_BPSPRegClass,
    &X86::GR32_BSIRegClass,
    &X86::GR32_CBRegClass,
    &X86::GR32_DCRegClass,
    &X86::GR32_DIBPRegClass,
    &X86::GR32_SIDIRegClass,
    &X86::LOW32_ADDR_ACCESS_RBP_with_sub_32bitRegClass,
    &X86::CCRRegClass,
    &X86::DFCCRRegClass,
    &X86::GR32_ABCD_and_GR32_BSIRegClass,
    &X86::GR32_AD_and_GR32_DCRegClass,
    &X86::GR32_BPSP_and_GR32_DIBPRegClass,
    &X86::GR32_BPSP_and_GR32_TCRegClass,
    &X86::GR32_BSI_and_GR32_SIDIRegClass,
    &X86::GR32_CB_and_GR32_DCRegClass,
    &X86::GR32_DIBP_and_GR32_SIDIRegClass,
    &X86::LOW32_ADDR_ACCESS_RBP_with_sub_8bit_with_sub_32bitRegClass,
    &X86::LOW32_ADDR_ACCESS_with_sub_32bitRegClass,
    &X86::RFP64RegClass,
    &X86::FR64XRegClass,
    &X86::GR64RegClass,
    &X86::CONTROL_REGRegClass,
    &X86::FR64RegClass,
    &X86::GR64_with_sub_8bitRegClass,
    &X86::GR64_NOSPRegClass,
    &X86::GR64_TCRegClass,
    &X86::GR64_NOREXRegClass,
    &X86::GR64_TCW64RegClass,
    &X86::GR64_TC_with_sub_8bitRegClass,
    &X86::GR64_NOSP_and_GR64_TCRegClass,
    &X86::GR64_TCW64_with_sub_8bitRegClass,
    &X86::GR64_TC_and_GR64_TCW64RegClass,
    &X86::GR64_with_sub_16bit_in_GR16_NOREXRegClass,
    &X86::VK64RegClass,
    &X86::VR64RegClass,
    &X86::GR64_NOREX_NOSPRegClass,
    &X86::GR64_NOREX_and_GR64_TCRegClass,
    &X86::GR64_NOSP_and_GR64_TCW64RegClass,
    &X86::GR64_TCW64_and_GR64_TC_with_sub_8bitRegClass,
    &X86::VK64WMRegClass,
    &X86::GR64_TC_and_GR64_NOSP_and_GR64_TCW64RegClass,
    &X86::GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREXRegClass,
    &X86::GR64_NOREX_NOSP_and_GR64_TCRegClass,
    &X86::GR64_NOREX_and_GR64_TCW64RegClass,
    &X86::GR64_ABCDRegClass,
    &X86::GR64_with_sub_32bit_in_GR32_TCRegClass,
    &X86::GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TCRegClass,
    &X86::GR64_ADRegClass,
    &X86::GR64_and_LOW32_ADDR_ACCESS_RBPRegClass,
    &X86::GR64_with_sub_32bit_in_GR32_BPSPRegClass,
    &X86::GR64_with_sub_32bit_in_GR32_BSIRegClass,
    &X86::GR64_with_sub_32bit_in_GR32_CBRegClass,
    &X86::GR64_with_sub_32bit_in_GR32_DCRegClass,
    &X86::GR64_with_sub_32bit_in_GR32_DIBPRegClass,
    &X86::GR64_with_sub_32bit_in_GR32_SIDIRegClass,
    &X86::GR64_and_LOW32_ADDR_ACCESSRegClass,
    &X86::GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_BSIRegClass,
    &X86::GR64_with_sub_32bit_in_GR32_AD_and_GR32_DCRegClass,
    &X86::GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_DIBPRegClass,
    &X86::GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_TCRegClass,
    &X86::GR64_with_sub_32bit_in_GR32_BSI_and_GR32_SIDIRegClass,
    &X86::GR64_with_sub_32bit_in_GR32_CB_and_GR32_DCRegClass,
    &X86::GR64_with_sub_32bit_in_GR32_DIBP_and_GR32_SIDIRegClass,
    &X86::RSTRegClass,
    &X86::RFP80RegClass,
    &X86::RFP80_7RegClass,
    &X86::VR128XRegClass,
    &X86::VR128RegClass,
    &X86::VR128HRegClass,
    &X86::VR128LRegClass,
    &X86::BNDRRegClass,
    &X86::VR256XRegClass,
    &X86::VR256RegClass,
    &X86::VR256HRegClass,
    &X86::VR256LRegClass,
    &X86::VR512RegClass,
    &X86::VR512_with_sub_xmm_in_FR32RegClass,
    &X86::VR512_with_sub_xmm_in_VR128HRegClass,
    &X86::VR512_with_sub_xmm_in_VR128LRegClass,
  };
} // end anonymous namespace

static const TargetRegisterInfoDesc X86RegInfoDesc[] = { // Extra Descriptors
  { 0, false },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, false },
  { 1, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, false },
  { 0, true },
  { 0, true },
  { 0, false },
  { 1, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, false },
  { 0, false },
  { 0, false },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, false },
  { 0, false },
  { 0, true },
  { 0, true },
  { 0, false },
  { 0, false },
  { 0, false },
  { 0, false },
  { 0, false },
  { 0, false },
  { 0, false },
  { 0, false },
  { 0, false },
  { 0, false },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 1, true },
  { 0, false },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, false },
  { 1, true },
  { 0, true },
  { 0, false },
  { 1, true },
  { 0, true },
  { 0, false },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, false },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 0, false },
  { 0, false },
  { 0, false },
  { 0, false },
  { 0, false },
  { 0, false },
  { 0, false },
  { 0, false },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 0, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 0, false },
  { 0, false },
  { 0, false },
  { 0, false },
  { 0, false },
  { 0, false },
  { 0, false },
  { 0, false },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 1, true },
  { 0, false },
  { 0, false },
  { 0, false },
  { 0, false },
  { 0, false },
  { 0, false },
  { 0, false },
  { 0, false },
};
unsigned X86GenRegisterInfo::composeSubRegIndicesImpl(unsigned IdxA, unsigned IdxB) const {
  static const uint8_t Rows[1][8] = {
    { 1, 2, 3, 4, 5, 0, 7, 0, },
  };

  --IdxA; assert(IdxA < 8);
  --IdxB; assert(IdxB < 8);
  return Rows[0][IdxB];
}

  struct MaskRolOp {
    LaneBitmask Mask;
    uint8_t  RotateLeft;
  };
  static const MaskRolOp LaneMaskComposeSequences[] = {
    { LaneBitmask(0xFFFFFFFF),  0 }, { LaneBitmask::getNone(), 0 },   // Sequence 0
    { LaneBitmask(0xFFFFFFFF),  1 }, { LaneBitmask::getNone(), 0 },   // Sequence 2
    { LaneBitmask(0xFFFFFFFF),  2 }, { LaneBitmask::getNone(), 0 },   // Sequence 4
    { LaneBitmask(0xFFFFFFFF),  3 }, { LaneBitmask::getNone(), 0 },   // Sequence 6
    { LaneBitmask(0xFFFFFFFF),  4 }, { LaneBitmask::getNone(), 0 }  // Sequence 8
  };
  static const MaskRolOp *const CompositeSequences[] = {
    &LaneMaskComposeSequences[0], // to sub_8bit
    &LaneMaskComposeSequences[2], // to sub_8bit_hi
    &LaneMaskComposeSequences[4], // to sub_8bit_hi_phony
    &LaneMaskComposeSequences[0], // to sub_16bit
    &LaneMaskComposeSequences[6], // to sub_16bit_hi
    &LaneMaskComposeSequences[0], // to sub_32bit
    &LaneMaskComposeSequences[8], // to sub_xmm
    &LaneMaskComposeSequences[0] // to sub_ymm
  };

LaneBitmask X86GenRegisterInfo::composeSubRegIndexLaneMaskImpl(unsigned IdxA, LaneBitmask LaneMask) const {
  --IdxA; assert(IdxA < 8 && "Subregister index out of bounds");
  LaneBitmask Result;
  for (const MaskRolOp *Ops = CompositeSequences[IdxA]; Ops->Mask.any(); ++Ops) {
    LaneBitmask::Type M = LaneMask.getAsInteger() & Ops->Mask.getAsInteger();
    if (unsigned S = Ops->RotateLeft)
      Result |= LaneBitmask((M << S) | (M >> (LaneBitmask::BitWidth - S)));
    else
      Result |= LaneBitmask(M);
  }
  return Result;
}

LaneBitmask X86GenRegisterInfo::reverseComposeSubRegIndexLaneMaskImpl(unsigned IdxA,  LaneBitmask LaneMask) const {
  LaneMask &= getSubRegIndexLaneMask(IdxA);
  --IdxA; assert(IdxA < 8 && "Subregister index out of bounds");
  LaneBitmask Result;
  for (const MaskRolOp *Ops = CompositeSequences[IdxA]; Ops->Mask.any(); ++Ops) {
    LaneBitmask::Type M = LaneMask.getAsInteger();
    if (unsigned S = Ops->RotateLeft)
      Result |= LaneBitmask((M >> S) | (M << (LaneBitmask::BitWidth - S)));
    else
      Result |= LaneBitmask(M);
  }
  return Result;
}

const TargetRegisterClass *X86GenRegisterInfo::getSubClassWithSubReg(const TargetRegisterClass *RC, unsigned Idx) const {
  static const uint8_t Table[118][8] = {
    {	// GR8
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GRH8
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR8_NOREX
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR8_ABCD_H
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR8_ABCD_L
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GRH16
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR16
      7,	// sub_8bit -> GR16
      20,	// sub_8bit_hi -> GR16_ABCD
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR16_NOREX
      8,	// sub_8bit -> GR16_NOREX
      20,	// sub_8bit_hi -> GR16_ABCD
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// VK1
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// VK16
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// VK2
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// VK4
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// VK8
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// VK16WM
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// VK1WM
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// VK2WM
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// VK4WM
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// VK8WM
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// SEGMENT_REG
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR16_ABCD
      20,	// sub_8bit -> GR16_ABCD
      20,	// sub_8bit_hi -> GR16_ABCD
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// FPCCR
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// FR32X
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// LOW32_ADDR_ACCESS_RBP
      25,	// sub_8bit -> LOW32_ADDR_ACCESS_RBP_with_sub_8bit
      36,	// sub_8bit_hi -> GR32_ABCD
      0,	// sub_8bit_hi_phony
      23,	// sub_16bit -> LOW32_ADDR_ACCESS_RBP
      0,	// sub_16bit_hi
      46,	// sub_32bit -> LOW32_ADDR_ACCESS_RBP_with_sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// LOW32_ADDR_ACCESS
      28,	// sub_8bit -> GR32
      36,	// sub_8bit_hi -> GR32_ABCD
      0,	// sub_8bit_hi_phony
      24,	// sub_16bit -> LOW32_ADDR_ACCESS
      0,	// sub_16bit_hi
      57,	// sub_32bit -> LOW32_ADDR_ACCESS_with_sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// LOW32_ADDR_ACCESS_RBP_with_sub_8bit
      25,	// sub_8bit -> LOW32_ADDR_ACCESS_RBP_with_sub_8bit
      36,	// sub_8bit_hi -> GR32_ABCD
      0,	// sub_8bit_hi_phony
      25,	// sub_16bit -> LOW32_ADDR_ACCESS_RBP_with_sub_8bit
      0,	// sub_16bit_hi
      56,	// sub_32bit -> LOW32_ADDR_ACCESS_RBP_with_sub_8bit_with_sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// DEBUG_REG
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// FR32
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR32
      28,	// sub_8bit -> GR32
      36,	// sub_8bit_hi -> GR32_ABCD
      0,	// sub_8bit_hi_phony
      28,	// sub_16bit -> GR32
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR32_NOSP
      29,	// sub_8bit -> GR32_NOSP
      36,	// sub_8bit_hi -> GR32_ABCD
      0,	// sub_8bit_hi_phony
      29,	// sub_16bit -> GR32_NOSP
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREX
      30,	// sub_8bit -> LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREX
      36,	// sub_8bit_hi -> GR32_ABCD
      0,	// sub_8bit_hi_phony
      30,	// sub_16bit -> LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREX
      0,	// sub_16bit_hi
      56,	// sub_32bit -> LOW32_ADDR_ACCESS_RBP_with_sub_8bit_with_sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR32_NOREX
      31,	// sub_8bit -> GR32_NOREX
      36,	// sub_8bit_hi -> GR32_ABCD
      0,	// sub_8bit_hi_phony
      31,	// sub_16bit -> GR32_NOREX
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// VK32
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR32_NOREX_NOSP
      33,	// sub_8bit -> GR32_NOREX_NOSP
      36,	// sub_8bit_hi -> GR32_ABCD
      0,	// sub_8bit_hi_phony
      33,	// sub_16bit -> GR32_NOREX_NOSP
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// RFP32
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// VK32WM
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR32_ABCD
      36,	// sub_8bit -> GR32_ABCD
      36,	// sub_8bit_hi -> GR32_ABCD
      0,	// sub_8bit_hi_phony
      36,	// sub_16bit -> GR32_ABCD
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR32_TC
      37,	// sub_8bit -> GR32_TC
      38,	// sub_8bit_hi -> GR32_ABCD_and_GR32_TC
      0,	// sub_8bit_hi_phony
      37,	// sub_16bit -> GR32_TC
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR32_ABCD_and_GR32_TC
      38,	// sub_8bit -> GR32_ABCD_and_GR32_TC
      38,	// sub_8bit_hi -> GR32_ABCD_and_GR32_TC
      0,	// sub_8bit_hi_phony
      38,	// sub_16bit -> GR32_ABCD_and_GR32_TC
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR32_AD
      39,	// sub_8bit -> GR32_AD
      39,	// sub_8bit_hi -> GR32_AD
      0,	// sub_8bit_hi_phony
      39,	// sub_16bit -> GR32_AD
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR32_BPSP
      40,	// sub_8bit -> GR32_BPSP
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      40,	// sub_16bit -> GR32_BPSP
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR32_BSI
      41,	// sub_8bit -> GR32_BSI
      49,	// sub_8bit_hi -> GR32_ABCD_and_GR32_BSI
      0,	// sub_8bit_hi_phony
      41,	// sub_16bit -> GR32_BSI
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR32_CB
      42,	// sub_8bit -> GR32_CB
      42,	// sub_8bit_hi -> GR32_CB
      0,	// sub_8bit_hi_phony
      42,	// sub_16bit -> GR32_CB
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR32_DC
      43,	// sub_8bit -> GR32_DC
      43,	// sub_8bit_hi -> GR32_DC
      0,	// sub_8bit_hi_phony
      43,	// sub_16bit -> GR32_DC
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR32_DIBP
      44,	// sub_8bit -> GR32_DIBP
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      44,	// sub_16bit -> GR32_DIBP
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR32_SIDI
      45,	// sub_8bit -> GR32_SIDI
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      45,	// sub_16bit -> GR32_SIDI
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// LOW32_ADDR_ACCESS_RBP_with_sub_32bit
      56,	// sub_8bit -> LOW32_ADDR_ACCESS_RBP_with_sub_8bit_with_sub_32bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      46,	// sub_16bit -> LOW32_ADDR_ACCESS_RBP_with_sub_32bit
      0,	// sub_16bit_hi
      46,	// sub_32bit -> LOW32_ADDR_ACCESS_RBP_with_sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// CCR
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// DFCCR
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR32_ABCD_and_GR32_BSI
      49,	// sub_8bit -> GR32_ABCD_and_GR32_BSI
      49,	// sub_8bit_hi -> GR32_ABCD_and_GR32_BSI
      0,	// sub_8bit_hi_phony
      49,	// sub_16bit -> GR32_ABCD_and_GR32_BSI
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR32_AD_and_GR32_DC
      50,	// sub_8bit -> GR32_AD_and_GR32_DC
      50,	// sub_8bit_hi -> GR32_AD_and_GR32_DC
      0,	// sub_8bit_hi_phony
      50,	// sub_16bit -> GR32_AD_and_GR32_DC
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR32_BPSP_and_GR32_DIBP
      51,	// sub_8bit -> GR32_BPSP_and_GR32_DIBP
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      51,	// sub_16bit -> GR32_BPSP_and_GR32_DIBP
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR32_BPSP_and_GR32_TC
      52,	// sub_8bit -> GR32_BPSP_and_GR32_TC
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      52,	// sub_16bit -> GR32_BPSP_and_GR32_TC
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR32_BSI_and_GR32_SIDI
      53,	// sub_8bit -> GR32_BSI_and_GR32_SIDI
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      53,	// sub_16bit -> GR32_BSI_and_GR32_SIDI
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR32_CB_and_GR32_DC
      54,	// sub_8bit -> GR32_CB_and_GR32_DC
      54,	// sub_8bit_hi -> GR32_CB_and_GR32_DC
      0,	// sub_8bit_hi_phony
      54,	// sub_16bit -> GR32_CB_and_GR32_DC
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR32_DIBP_and_GR32_SIDI
      55,	// sub_8bit -> GR32_DIBP_and_GR32_SIDI
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      55,	// sub_16bit -> GR32_DIBP_and_GR32_SIDI
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// LOW32_ADDR_ACCESS_RBP_with_sub_8bit_with_sub_32bit
      56,	// sub_8bit -> LOW32_ADDR_ACCESS_RBP_with_sub_8bit_with_sub_32bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      56,	// sub_16bit -> LOW32_ADDR_ACCESS_RBP_with_sub_8bit_with_sub_32bit
      0,	// sub_16bit_hi
      56,	// sub_32bit -> LOW32_ADDR_ACCESS_RBP_with_sub_8bit_with_sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// LOW32_ADDR_ACCESS_with_sub_32bit
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      57,	// sub_16bit -> LOW32_ADDR_ACCESS_with_sub_32bit
      0,	// sub_16bit_hi
      57,	// sub_32bit -> LOW32_ADDR_ACCESS_with_sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// RFP64
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// FR64X
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64
      63,	// sub_8bit -> GR64_with_sub_8bit
      84,	// sub_8bit_hi -> GR64_ABCD
      0,	// sub_8bit_hi_phony
      60,	// sub_16bit -> GR64
      0,	// sub_16bit_hi
      60,	// sub_32bit -> GR64
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// CONTROL_REG
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// FR64
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_with_sub_8bit
      63,	// sub_8bit -> GR64_with_sub_8bit
      84,	// sub_8bit_hi -> GR64_ABCD
      0,	// sub_8bit_hi_phony
      63,	// sub_16bit -> GR64_with_sub_8bit
      0,	// sub_16bit_hi
      63,	// sub_32bit -> GR64_with_sub_8bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_NOSP
      64,	// sub_8bit -> GR64_NOSP
      84,	// sub_8bit_hi -> GR64_ABCD
      0,	// sub_8bit_hi_phony
      64,	// sub_16bit -> GR64_NOSP
      0,	// sub_16bit_hi
      64,	// sub_32bit -> GR64_NOSP
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_TC
      68,	// sub_8bit -> GR64_TC_with_sub_8bit
      86,	// sub_8bit_hi -> GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TC
      0,	// sub_8bit_hi_phony
      65,	// sub_16bit -> GR64_TC
      0,	// sub_16bit_hi
      65,	// sub_32bit -> GR64_TC
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_NOREX
      72,	// sub_8bit -> GR64_with_sub_16bit_in_GR16_NOREX
      84,	// sub_8bit_hi -> GR64_ABCD
      0,	// sub_8bit_hi_phony
      66,	// sub_16bit -> GR64_NOREX
      0,	// sub_16bit_hi
      66,	// sub_32bit -> GR64_NOREX
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_TCW64
      70,	// sub_8bit -> GR64_TCW64_with_sub_8bit
      86,	// sub_8bit_hi -> GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TC
      0,	// sub_8bit_hi_phony
      67,	// sub_16bit -> GR64_TCW64
      0,	// sub_16bit_hi
      67,	// sub_32bit -> GR64_TCW64
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_TC_with_sub_8bit
      68,	// sub_8bit -> GR64_TC_with_sub_8bit
      86,	// sub_8bit_hi -> GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TC
      0,	// sub_8bit_hi_phony
      68,	// sub_16bit -> GR64_TC_with_sub_8bit
      0,	// sub_16bit_hi
      68,	// sub_32bit -> GR64_TC_with_sub_8bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_NOSP_and_GR64_TC
      69,	// sub_8bit -> GR64_NOSP_and_GR64_TC
      86,	// sub_8bit_hi -> GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TC
      0,	// sub_8bit_hi_phony
      69,	// sub_16bit -> GR64_NOSP_and_GR64_TC
      0,	// sub_16bit_hi
      69,	// sub_32bit -> GR64_NOSP_and_GR64_TC
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_TCW64_with_sub_8bit
      70,	// sub_8bit -> GR64_TCW64_with_sub_8bit
      86,	// sub_8bit_hi -> GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TC
      0,	// sub_8bit_hi_phony
      70,	// sub_16bit -> GR64_TCW64_with_sub_8bit
      0,	// sub_16bit_hi
      70,	// sub_32bit -> GR64_TCW64_with_sub_8bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_TC_and_GR64_TCW64
      78,	// sub_8bit -> GR64_TCW64_and_GR64_TC_with_sub_8bit
      86,	// sub_8bit_hi -> GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TC
      0,	// sub_8bit_hi_phony
      71,	// sub_16bit -> GR64_TC_and_GR64_TCW64
      0,	// sub_16bit_hi
      71,	// sub_32bit -> GR64_TC_and_GR64_TCW64
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_with_sub_16bit_in_GR16_NOREX
      72,	// sub_8bit -> GR64_with_sub_16bit_in_GR16_NOREX
      84,	// sub_8bit_hi -> GR64_ABCD
      0,	// sub_8bit_hi_phony
      72,	// sub_16bit -> GR64_with_sub_16bit_in_GR16_NOREX
      0,	// sub_16bit_hi
      72,	// sub_32bit -> GR64_with_sub_16bit_in_GR16_NOREX
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// VK64
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// VR64
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_NOREX_NOSP
      75,	// sub_8bit -> GR64_NOREX_NOSP
      84,	// sub_8bit_hi -> GR64_ABCD
      0,	// sub_8bit_hi_phony
      75,	// sub_16bit -> GR64_NOREX_NOSP
      0,	// sub_16bit_hi
      75,	// sub_32bit -> GR64_NOREX_NOSP
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_NOREX_and_GR64_TC
      81,	// sub_8bit -> GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREX
      86,	// sub_8bit_hi -> GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TC
      0,	// sub_8bit_hi_phony
      76,	// sub_16bit -> GR64_NOREX_and_GR64_TC
      0,	// sub_16bit_hi
      76,	// sub_32bit -> GR64_NOREX_and_GR64_TC
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_NOSP_and_GR64_TCW64
      77,	// sub_8bit -> GR64_NOSP_and_GR64_TCW64
      86,	// sub_8bit_hi -> GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TC
      0,	// sub_8bit_hi_phony
      77,	// sub_16bit -> GR64_NOSP_and_GR64_TCW64
      0,	// sub_16bit_hi
      77,	// sub_32bit -> GR64_NOSP_and_GR64_TCW64
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_TCW64_and_GR64_TC_with_sub_8bit
      78,	// sub_8bit -> GR64_TCW64_and_GR64_TC_with_sub_8bit
      86,	// sub_8bit_hi -> GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TC
      0,	// sub_8bit_hi_phony
      78,	// sub_16bit -> GR64_TCW64_and_GR64_TC_with_sub_8bit
      0,	// sub_16bit_hi
      78,	// sub_32bit -> GR64_TCW64_and_GR64_TC_with_sub_8bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// VK64WM
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_TC_and_GR64_NOSP_and_GR64_TCW64
      80,	// sub_8bit -> GR64_TC_and_GR64_NOSP_and_GR64_TCW64
      86,	// sub_8bit_hi -> GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TC
      0,	// sub_8bit_hi_phony
      80,	// sub_16bit -> GR64_TC_and_GR64_NOSP_and_GR64_TCW64
      0,	// sub_16bit_hi
      80,	// sub_32bit -> GR64_TC_and_GR64_NOSP_and_GR64_TCW64
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREX
      81,	// sub_8bit -> GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREX
      86,	// sub_8bit_hi -> GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TC
      0,	// sub_8bit_hi_phony
      81,	// sub_16bit -> GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREX
      0,	// sub_16bit_hi
      81,	// sub_32bit -> GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREX
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_NOREX_NOSP_and_GR64_TC
      82,	// sub_8bit -> GR64_NOREX_NOSP_and_GR64_TC
      86,	// sub_8bit_hi -> GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TC
      0,	// sub_8bit_hi_phony
      82,	// sub_16bit -> GR64_NOREX_NOSP_and_GR64_TC
      0,	// sub_16bit_hi
      82,	// sub_32bit -> GR64_NOREX_NOSP_and_GR64_TC
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_NOREX_and_GR64_TCW64
      85,	// sub_8bit -> GR64_with_sub_32bit_in_GR32_TC
      86,	// sub_8bit_hi -> GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TC
      0,	// sub_8bit_hi_phony
      83,	// sub_16bit -> GR64_NOREX_and_GR64_TCW64
      0,	// sub_16bit_hi
      83,	// sub_32bit -> GR64_NOREX_and_GR64_TCW64
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_ABCD
      84,	// sub_8bit -> GR64_ABCD
      84,	// sub_8bit_hi -> GR64_ABCD
      0,	// sub_8bit_hi_phony
      84,	// sub_16bit -> GR64_ABCD
      0,	// sub_16bit_hi
      84,	// sub_32bit -> GR64_ABCD
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_with_sub_32bit_in_GR32_TC
      85,	// sub_8bit -> GR64_with_sub_32bit_in_GR32_TC
      86,	// sub_8bit_hi -> GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TC
      0,	// sub_8bit_hi_phony
      85,	// sub_16bit -> GR64_with_sub_32bit_in_GR32_TC
      0,	// sub_16bit_hi
      85,	// sub_32bit -> GR64_with_sub_32bit_in_GR32_TC
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TC
      86,	// sub_8bit -> GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TC
      86,	// sub_8bit_hi -> GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TC
      0,	// sub_8bit_hi_phony
      86,	// sub_16bit -> GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TC
      0,	// sub_16bit_hi
      86,	// sub_32bit -> GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TC
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_AD
      87,	// sub_8bit -> GR64_AD
      87,	// sub_8bit_hi -> GR64_AD
      0,	// sub_8bit_hi_phony
      87,	// sub_16bit -> GR64_AD
      0,	// sub_16bit_hi
      87,	// sub_32bit -> GR64_AD
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_and_LOW32_ADDR_ACCESS_RBP
      98,	// sub_8bit -> GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_DIBP
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      88,	// sub_16bit -> GR64_and_LOW32_ADDR_ACCESS_RBP
      0,	// sub_16bit_hi
      88,	// sub_32bit -> GR64_and_LOW32_ADDR_ACCESS_RBP
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_with_sub_32bit_in_GR32_BPSP
      89,	// sub_8bit -> GR64_with_sub_32bit_in_GR32_BPSP
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      89,	// sub_16bit -> GR64_with_sub_32bit_in_GR32_BPSP
      0,	// sub_16bit_hi
      89,	// sub_32bit -> GR64_with_sub_32bit_in_GR32_BPSP
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_with_sub_32bit_in_GR32_BSI
      90,	// sub_8bit -> GR64_with_sub_32bit_in_GR32_BSI
      96,	// sub_8bit_hi -> GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_BSI
      0,	// sub_8bit_hi_phony
      90,	// sub_16bit -> GR64_with_sub_32bit_in_GR32_BSI
      0,	// sub_16bit_hi
      90,	// sub_32bit -> GR64_with_sub_32bit_in_GR32_BSI
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_with_sub_32bit_in_GR32_CB
      91,	// sub_8bit -> GR64_with_sub_32bit_in_GR32_CB
      91,	// sub_8bit_hi -> GR64_with_sub_32bit_in_GR32_CB
      0,	// sub_8bit_hi_phony
      91,	// sub_16bit -> GR64_with_sub_32bit_in_GR32_CB
      0,	// sub_16bit_hi
      91,	// sub_32bit -> GR64_with_sub_32bit_in_GR32_CB
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_with_sub_32bit_in_GR32_DC
      92,	// sub_8bit -> GR64_with_sub_32bit_in_GR32_DC
      92,	// sub_8bit_hi -> GR64_with_sub_32bit_in_GR32_DC
      0,	// sub_8bit_hi_phony
      92,	// sub_16bit -> GR64_with_sub_32bit_in_GR32_DC
      0,	// sub_16bit_hi
      92,	// sub_32bit -> GR64_with_sub_32bit_in_GR32_DC
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_with_sub_32bit_in_GR32_DIBP
      93,	// sub_8bit -> GR64_with_sub_32bit_in_GR32_DIBP
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      93,	// sub_16bit -> GR64_with_sub_32bit_in_GR32_DIBP
      0,	// sub_16bit_hi
      93,	// sub_32bit -> GR64_with_sub_32bit_in_GR32_DIBP
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_with_sub_32bit_in_GR32_SIDI
      94,	// sub_8bit -> GR64_with_sub_32bit_in_GR32_SIDI
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      94,	// sub_16bit -> GR64_with_sub_32bit_in_GR32_SIDI
      0,	// sub_16bit_hi
      94,	// sub_32bit -> GR64_with_sub_32bit_in_GR32_SIDI
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_and_LOW32_ADDR_ACCESS
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      95,	// sub_16bit -> GR64_and_LOW32_ADDR_ACCESS
      0,	// sub_16bit_hi
      95,	// sub_32bit -> GR64_and_LOW32_ADDR_ACCESS
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_BSI
      96,	// sub_8bit -> GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_BSI
      96,	// sub_8bit_hi -> GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_BSI
      0,	// sub_8bit_hi_phony
      96,	// sub_16bit -> GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_BSI
      0,	// sub_16bit_hi
      96,	// sub_32bit -> GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_BSI
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_with_sub_32bit_in_GR32_AD_and_GR32_DC
      97,	// sub_8bit -> GR64_with_sub_32bit_in_GR32_AD_and_GR32_DC
      97,	// sub_8bit_hi -> GR64_with_sub_32bit_in_GR32_AD_and_GR32_DC
      0,	// sub_8bit_hi_phony
      97,	// sub_16bit -> GR64_with_sub_32bit_in_GR32_AD_and_GR32_DC
      0,	// sub_16bit_hi
      97,	// sub_32bit -> GR64_with_sub_32bit_in_GR32_AD_and_GR32_DC
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_DIBP
      98,	// sub_8bit -> GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_DIBP
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      98,	// sub_16bit -> GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_DIBP
      0,	// sub_16bit_hi
      98,	// sub_32bit -> GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_DIBP
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_TC
      99,	// sub_8bit -> GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_TC
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      99,	// sub_16bit -> GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_TC
      0,	// sub_16bit_hi
      99,	// sub_32bit -> GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_TC
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_with_sub_32bit_in_GR32_BSI_and_GR32_SIDI
      100,	// sub_8bit -> GR64_with_sub_32bit_in_GR32_BSI_and_GR32_SIDI
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      100,	// sub_16bit -> GR64_with_sub_32bit_in_GR32_BSI_and_GR32_SIDI
      0,	// sub_16bit_hi
      100,	// sub_32bit -> GR64_with_sub_32bit_in_GR32_BSI_and_GR32_SIDI
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_with_sub_32bit_in_GR32_CB_and_GR32_DC
      101,	// sub_8bit -> GR64_with_sub_32bit_in_GR32_CB_and_GR32_DC
      101,	// sub_8bit_hi -> GR64_with_sub_32bit_in_GR32_CB_and_GR32_DC
      0,	// sub_8bit_hi_phony
      101,	// sub_16bit -> GR64_with_sub_32bit_in_GR32_CB_and_GR32_DC
      0,	// sub_16bit_hi
      101,	// sub_32bit -> GR64_with_sub_32bit_in_GR32_CB_and_GR32_DC
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// GR64_with_sub_32bit_in_GR32_DIBP_and_GR32_SIDI
      102,	// sub_8bit -> GR64_with_sub_32bit_in_GR32_DIBP_and_GR32_SIDI
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      102,	// sub_16bit -> GR64_with_sub_32bit_in_GR32_DIBP_and_GR32_SIDI
      0,	// sub_16bit_hi
      102,	// sub_32bit -> GR64_with_sub_32bit_in_GR32_DIBP_and_GR32_SIDI
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// RST
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// RFP80
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// RFP80_7
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// VR128X
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// VR128
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// VR128H
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// VR128L
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// BNDR
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      0,	// sub_xmm
      0,	// sub_ymm
    },
    {	// VR256X
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      111,	// sub_xmm -> VR256X
      0,	// sub_ymm
    },
    {	// VR256
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      112,	// sub_xmm -> VR256
      0,	// sub_ymm
    },
    {	// VR256H
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      113,	// sub_xmm -> VR256H
      0,	// sub_ymm
    },
    {	// VR256L
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      114,	// sub_xmm -> VR256L
      0,	// sub_ymm
    },
    {	// VR512
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      115,	// sub_xmm -> VR512
      115,	// sub_ymm -> VR512
    },
    {	// VR512_with_sub_xmm_in_FR32
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      116,	// sub_xmm -> VR512_with_sub_xmm_in_FR32
      116,	// sub_ymm -> VR512_with_sub_xmm_in_FR32
    },
    {	// VR512_with_sub_xmm_in_VR128H
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      117,	// sub_xmm -> VR512_with_sub_xmm_in_VR128H
      117,	// sub_ymm -> VR512_with_sub_xmm_in_VR128H
    },
    {	// VR512_with_sub_xmm_in_VR128L
      0,	// sub_8bit
      0,	// sub_8bit_hi
      0,	// sub_8bit_hi_phony
      0,	// sub_16bit
      0,	// sub_16bit_hi
      0,	// sub_32bit
      118,	// sub_xmm -> VR512_with_sub_xmm_in_VR128L
      118,	// sub_ymm -> VR512_with_sub_xmm_in_VR128L
    },
  };
  assert(RC && "Missing regclass");
  if (!Idx) return RC;
  --Idx;
  assert(Idx < 8 && "Bad subreg");
  unsigned TV = Table[RC->getID()][Idx];
  return TV ? getRegClass(TV - 1) : nullptr;
}

/// Get the weight in units of pressure for this register class.
const RegClassWeight &X86GenRegisterInfo::
getRegClassWeight(const TargetRegisterClass *RC) const {
  static const RegClassWeight RCWeightTable[] = {
    {1, 20},  	// GR8
    {0, 0},  	// GRH8
    {1, 8},  	// GR8_NOREX
    {1, 4},  	// GR8_ABCD_H
    {1, 4},  	// GR8_ABCD_L
    {0, 0},  	// GRH16
    {2, 32},  	// GR16
    {2, 16},  	// GR16_NOREX
    {1, 8},  	// VK1
    {1, 8},  	// VK16
    {1, 8},  	// VK2
    {1, 8},  	// VK4
    {1, 8},  	// VK8
    {1, 7},  	// VK16WM
    {1, 7},  	// VK1WM
    {1, 7},  	// VK2WM
    {1, 7},  	// VK4WM
    {1, 7},  	// VK8WM
    {1, 6},  	// SEGMENT_REG
    {2, 8},  	// GR16_ABCD
    {0, 0},  	// FPCCR
    {1, 32},  	// FR32X
    {2, 34},  	// LOW32_ADDR_ACCESS_RBP
    {2, 34},  	// LOW32_ADDR_ACCESS
    {2, 32},  	// LOW32_ADDR_ACCESS_RBP_with_sub_8bit
    {1, 16},  	// DEBUG_REG
    {1, 16},  	// FR32
    {2, 32},  	// GR32
    {2, 30},  	// GR32_NOSP
    {2, 16},  	// LOW32_ADDR_ACCESS_RBP_with_sub_16bit_in_GR16_NOREX
    {2, 16},  	// GR32_NOREX
    {1, 8},  	// VK32
    {2, 14},  	// GR32_NOREX_NOSP
    {1, 7},  	// RFP32
    {1, 7},  	// VK32WM
    {2, 8},  	// GR32_ABCD
    {2, 8},  	// GR32_TC
    {2, 6},  	// GR32_ABCD_and_GR32_TC
    {2, 4},  	// GR32_AD
    {2, 4},  	// GR32_BPSP
    {2, 4},  	// GR32_BSI
    {2, 4},  	// GR32_CB
    {2, 4},  	// GR32_DC
    {2, 4},  	// GR32_DIBP
    {2, 4},  	// GR32_SIDI
    {2, 4},  	// LOW32_ADDR_ACCESS_RBP_with_sub_32bit
    {0, 0},  	// CCR
    {0, 0},  	// DFCCR
    {2, 2},  	// GR32_ABCD_and_GR32_BSI
    {2, 2},  	// GR32_AD_and_GR32_DC
    {2, 2},  	// GR32_BPSP_and_GR32_DIBP
    {2, 2},  	// GR32_BPSP_and_GR32_TC
    {2, 2},  	// GR32_BSI_and_GR32_SIDI
    {2, 2},  	// GR32_CB_and_GR32_DC
    {2, 2},  	// GR32_DIBP_and_GR32_SIDI
    {2, 2},  	// LOW32_ADDR_ACCESS_RBP_with_sub_8bit_with_sub_32bit
    {2, 2},  	// LOW32_ADDR_ACCESS_with_sub_32bit
    {1, 7},  	// RFP64
    {1, 32},  	// FR64X
    {2, 34},  	// GR64
    {1, 16},  	// CONTROL_REG
    {1, 16},  	// FR64
    {2, 32},  	// GR64_with_sub_8bit
    {2, 30},  	// GR64_NOSP
    {2, 20},  	// GR64_TC
    {2, 18},  	// GR64_NOREX
    {2, 18},  	// GR64_TCW64
    {2, 18},  	// GR64_TC_with_sub_8bit
    {2, 16},  	// GR64_NOSP_and_GR64_TC
    {2, 16},  	// GR64_TCW64_with_sub_8bit
    {2, 16},  	// GR64_TC_and_GR64_TCW64
    {2, 16},  	// GR64_with_sub_16bit_in_GR16_NOREX
    {1, 8},  	// VK64
    {1, 8},  	// VR64
    {2, 14},  	// GR64_NOREX_NOSP
    {2, 14},  	// GR64_NOREX_and_GR64_TC
    {2, 14},  	// GR64_NOSP_and_GR64_TCW64
    {2, 14},  	// GR64_TCW64_and_GR64_TC_with_sub_8bit
    {1, 7},  	// VK64WM
    {2, 12},  	// GR64_TC_and_GR64_NOSP_and_GR64_TCW64
    {2, 12},  	// GR64_TC_and_GR64_with_sub_16bit_in_GR16_NOREX
    {2, 10},  	// GR64_NOREX_NOSP_and_GR64_TC
    {2, 10},  	// GR64_NOREX_and_GR64_TCW64
    {2, 8},  	// GR64_ABCD
    {2, 8},  	// GR64_with_sub_32bit_in_GR32_TC
    {2, 6},  	// GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_TC
    {2, 4},  	// GR64_AD
    {2, 4},  	// GR64_and_LOW32_ADDR_ACCESS_RBP
    {2, 4},  	// GR64_with_sub_32bit_in_GR32_BPSP
    {2, 4},  	// GR64_with_sub_32bit_in_GR32_BSI
    {2, 4},  	// GR64_with_sub_32bit_in_GR32_CB
    {2, 4},  	// GR64_with_sub_32bit_in_GR32_DC
    {2, 4},  	// GR64_with_sub_32bit_in_GR32_DIBP
    {2, 4},  	// GR64_with_sub_32bit_in_GR32_SIDI
    {2, 2},  	// GR64_and_LOW32_ADDR_ACCESS
    {2, 2},  	// GR64_with_sub_32bit_in_GR32_ABCD_and_GR32_BSI
    {2, 2},  	// GR64_with_sub_32bit_in_GR32_AD_and_GR32_DC
    {2, 2},  	// GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_DIBP
    {2, 2},  	// GR64_with_sub_32bit_in_GR32_BPSP_and_GR32_TC
    {2, 2},  	// GR64_with_sub_32bit_in_GR32_BSI_and_GR32_SIDI
    {2, 2},  	// GR64_with_sub_32bit_in_GR32_CB_and_GR32_DC
    {2, 2},  	// GR64_with_sub_32bit_in_GR32_DIBP_and_GR32_SIDI
    {0, 0},  	// RST
    {1, 7},  	// RFP80
    {0, 0},  	// RFP80_7
    {1, 32},  	// VR128X
    {1, 16},  	// VR128
    {1, 8},  	// VR128H
    {1, 8},  	// VR128L
    {1, 4},  	// BNDR
    {1, 32},  	// VR256X
    {1, 16},  	// VR256
    {1, 8},  	// VR256H
    {1, 8},  	// VR256L
    {1, 32},  	// VR512
    {1, 16},  	// VR512_with_sub_xmm_in_FR32
    {1, 8},  	// VR512_with_sub_xmm_in_VR128H
    {1, 8},  	// VR512_with_sub_xmm_in_VR128L
  };
  return RCWeightTable[RC->getID()];
}

/// Get the weight in units of pressure for this register unit.
unsigned X86GenRegisterInfo::
getRegUnitWeight(unsigned RegUnit) const {
  assert(RegUnit < 163 && "invalid register unit");
  // All register units have unit weight.
  return 1;
}


// Get the number of dimensions of register pressure.
unsigned X86GenRegisterInfo::getNumRegPressureSets() const {
  return 35;
}

// Get the name of this register unit pressure set.
const char *X86GenRegisterInfo::
getRegPressureSetName(unsigned Idx) const {
  static const char *const PressureNameTable[] = {
    "BNDR",
    "SEGMENT_REG",
    "GR32_BPSP",
    "LOW32_ADDR_ACCESS_with_sub_32bit",
    "GR32_BSI",
    "GR32_SIDI",
    "GR32_DIBP+GR32_SIDI",
    "GR32_DIBP+LOW32_ADDR_ACCESS_with_sub_32bit",
    "RFP32",
    "GR8_ABCD_H+GR32_BSI",
    "GR8_ABCD_L+GR32_BSI",
    "VK1",
    "VR64",
    "VR128H",
    "VR128L",
    "GR8_NOREX",
    "GR32_TC",
    "GR32_BPSP+GR32_TC",
    "DEBUG_REG",
    "FR32",
    "CONTROL_REG",
    "GR64_NOREX",
    "GR64_TCW64",
    "GR32_BPSP+GR64_TCW64",
    "GR8",
    "GR8+GR32_DIBP",
    "GR8+GR32_BSI",
    "GR64_TC+GR64_TCW64",
    "GR8+LOW32_ADDR_ACCESS_with_sub_32bit",
    "GR8+GR64_NOREX",
    "GR64_TC",
    "GR8+GR64_TCW64",
    "GR8+GR64_TC",
    "FR32X",
    "GR16",
  };
  return PressureNameTable[Idx];
}

// Get the register unit pressure limit for this dimension.
// This limit must be adjusted dynamically for reserved registers.
unsigned X86GenRegisterInfo::
getRegPressureSetLimit(const MachineFunction &MF, unsigned Idx) const {
  static const uint8_t PressureLimitTable[] = {
    4,  	// 0: BNDR
    6,  	// 1: SEGMENT_REG
    6,  	// 2: GR32_BPSP
    6,  	// 3: LOW32_ADDR_ACCESS_with_sub_32bit
    6,  	// 4: GR32_BSI
    6,  	// 5: GR32_SIDI
    6,  	// 6: GR32_DIBP+GR32_SIDI
    6,  	// 7: GR32_DIBP+LOW32_ADDR_ACCESS_with_sub_32bit
    7,  	// 8: RFP32
    7,  	// 9: GR8_ABCD_H+GR32_BSI
    7,  	// 10: GR8_ABCD_L+GR32_BSI
    8,  	// 11: VK1
    8,  	// 12: VR64
    8,  	// 13: VR128H
    8,  	// 14: VR128L
    10,  	// 15: GR8_NOREX
    12,  	// 16: GR32_TC
    12,  	// 17: GR32_BPSP+GR32_TC
    16,  	// 18: DEBUG_REG
    16,  	// 19: FR32
    16,  	// 20: CONTROL_REG
    18,  	// 21: GR64_NOREX
    20,  	// 22: GR64_TCW64
    20,  	// 23: GR32_BPSP+GR64_TCW64
    22,  	// 24: GR8
    22,  	// 25: GR8+GR32_DIBP
    22,  	// 26: GR8+GR32_BSI
    22,  	// 27: GR64_TC+GR64_TCW64
    23,  	// 28: GR8+LOW32_ADDR_ACCESS_with_sub_32bit
    26,  	// 29: GR8+GR64_NOREX
    26,  	// 30: GR64_TC
    27,  	// 31: GR8+GR64_TCW64
    28,  	// 32: GR8+GR64_TC
    32,  	// 33: FR32X
    34,  	// 34: GR16
  };
  return PressureLimitTable[Idx];
}

/// Table of pressure sets per register class or unit.
static const int RCSetsTable[] = {
  /* 0 */ 0, -1,
  /* 2 */ 1, -1,
  /* 4 */ 8, -1,
  /* 6 */ 11, -1,
  /* 8 */ 12, -1,
  /* 10 */ 18, -1,
  /* 12 */ 20, -1,
  /* 14 */ 13, 19, 33, -1,
  /* 18 */ 14, 19, 33, -1,
  /* 22 */ 21, 29, 30, 34, -1,
  /* 27 */ 2, 3, 17, 21, 23, 24, 29, 30, 34, -1,
  /* 37 */ 2, 6, 7, 21, 25, 29, 30, 34, -1,
  /* 46 */ 3, 7, 17, 21, 23, 28, 29, 30, 34, -1,
  /* 56 */ 2, 3, 6, 7, 17, 21, 23, 24, 25, 28, 29, 30, 34, -1,
  /* 70 */ 22, 23, 27, 30, 31, 34, -1,
  /* 77 */ 27, 30, 32, 34, -1,
  /* 82 */ 4, 5, 9, 10, 15, 21, 26, 29, 30, 32, 34, -1,
  /* 94 */ 21, 27, 29, 30, 32, 34, -1,
  /* 101 */ 5, 6, 21, 26, 27, 29, 30, 32, 34, -1,
  /* 111 */ 4, 5, 6, 9, 10, 15, 21, 26, 27, 29, 30, 32, 34, -1,
  /* 125 */ 2, 5, 6, 7, 21, 25, 26, 27, 29, 30, 32, 34, -1,
  /* 138 */ 24, 25, 26, 28, 29, 31, 32, 34, -1,
  /* 147 */ 22, 23, 27, 30, 31, 32, 34, -1,
  /* 155 */ 16, 17, 21, 22, 23, 27, 29, 30, 31, 32, 34, -1,
  /* 167 */ 2, 3, 16, 17, 21, 22, 23, 24, 27, 29, 30, 31, 32, 34, -1,
  /* 182 */ 4, 15, 16, 21, 22, 24, 25, 26, 28, 29, 30, 31, 32, 34, -1,
  /* 197 */ 9, 15, 16, 21, 22, 24, 25, 26, 28, 29, 30, 31, 32, 34, -1,
  /* 212 */ 4, 5, 9, 10, 15, 16, 21, 22, 24, 25, 26, 28, 29, 30, 31, 32, 34, -1,
  /* 230 */ 2, 3, 6, 7, 17, 21, 23, 24, 25, 26, 28, 29, 30, 31, 32, 34, -1,
  /* 247 */ 3, 7, 16, 17, 21, 22, 23, 27, 28, 29, 30, 31, 32, 34, -1,
  /* 262 */ 2, 5, 6, 7, 21, 24, 25, 26, 27, 28, 29, 30, 31, 32, 34, -1,
  /* 278 */ 4, 5, 6, 9, 10, 15, 21, 24, 25, 26, 27, 28, 29, 30, 31, 32, 34, -1,
  /* 296 */ 2, 3, 16, 17, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 34, -1,
  /* 314 */ 4, 15, 16, 17, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 34, -1,
  /* 332 */ 4, 9, 15, 16, 17, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 34, -1,
  /* 351 */ 4, 10, 15, 16, 17, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 34, -1,
};

/// Get the dimensions of register pressure impacted by this register class.
/// Returns a -1 terminated array of pressure set IDs
const int* X86GenRegisterInfo::
getRegClassPressureSets(const TargetRegisterClass *RC) const {
  static const uint16_t RCSetStartTable[] = {
    138,1,183,197,215,1,25,22,6,6,6,6,6,6,6,6,6,6,2,183,1,16,25,25,25,10,15,25,25,22,22,6,22,4,6,183,169,315,315,27,82,182,315,37,101,46,1,1,212,315,56,167,111,314,125,56,247,4,16,25,12,15,25,25,77,22,70,77,77,70,147,22,6,8,22,94,70,147,6,147,94,103,155,183,169,315,315,46,27,82,182,315,37,101,247,212,315,56,167,111,314,125,1,4,1,16,15,14,18,0,16,15,14,18,16,15,14,18,};
  return &RCSetsTable[RCSetStartTable[RC->getID()]];
}

/// Get the dimensions of register pressure impacted by this register unit.
/// Returns a -1 terminated array of pressure set IDs
const int* X86GenRegisterInfo::
getRegUnitPressureSets(unsigned RegUnit) const {
  assert(RegUnit < 163 && "invalid register unit");
  static const uint16_t RUSetStartTable[] = {
    333,352,212,212,230,1,332,351,2,1,333,262,1,352,2,1,1,1,1,1,1,1,247,1,1,2,278,1,1,296,1,1,1,1,2,2,1,2,1,0,0,0,0,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,4,4,4,4,4,4,4,1,6,6,6,6,6,6,6,6,8,8,8,8,8,8,8,8,301,1,1,301,1,1,301,1,1,301,1,1,138,1,1,138,1,1,138,1,1,138,1,1,1,1,1,1,1,1,1,1,18,18,18,18,18,18,18,18,14,14,14,14,14,14,14,14,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,};
  return &RCSetsTable[RUSetStartTable[RegUnit]];
}

extern const MCRegisterDesc X86RegDesc[];
extern const MCPhysReg X86RegDiffLists[];
extern const LaneBitmask X86LaneMaskLists[];
extern const char X86RegStrings[];
extern const char X86RegClassStrings[];
extern const MCPhysReg X86RegUnitRoots[][2];
extern const uint16_t X86SubRegIdxLists[];
extern const MCRegisterInfo::SubRegCoveredBits X86SubRegIdxRanges[];
extern const uint16_t X86RegEncodingTable[];
// X86 Dwarf<->LLVM register mappings.
extern const MCRegisterInfo::DwarfLLVMRegPair X86DwarfFlavour0Dwarf2L[];
extern const unsigned X86DwarfFlavour0Dwarf2LSize;

extern const MCRegisterInfo::DwarfLLVMRegPair X86DwarfFlavour1Dwarf2L[];
extern const unsigned X86DwarfFlavour1Dwarf2LSize;

extern const MCRegisterInfo::DwarfLLVMRegPair X86DwarfFlavour2Dwarf2L[];
extern const unsigned X86DwarfFlavour2Dwarf2LSize;

extern const MCRegisterInfo::DwarfLLVMRegPair X86EHFlavour0Dwarf2L[];
extern const unsigned X86EHFlavour0Dwarf2LSize;

extern const MCRegisterInfo::DwarfLLVMRegPair X86EHFlavour1Dwarf2L[];
extern const unsigned X86EHFlavour1Dwarf2LSize;

extern const MCRegisterInfo::DwarfLLVMRegPair X86EHFlavour2Dwarf2L[];
extern const unsigned X86EHFlavour2Dwarf2LSize;

extern const MCRegisterInfo::DwarfLLVMRegPair X86DwarfFlavour0L2Dwarf[];
extern const unsigned X86DwarfFlavour0L2DwarfSize;

extern const MCRegisterInfo::DwarfLLVMRegPair X86DwarfFlavour1L2Dwarf[];
extern const unsigned X86DwarfFlavour1L2DwarfSize;

extern const MCRegisterInfo::DwarfLLVMRegPair X86DwarfFlavour2L2Dwarf[];
extern const unsigned X86DwarfFlavour2L2DwarfSize;

extern const MCRegisterInfo::DwarfLLVMRegPair X86EHFlavour0L2Dwarf[];
extern const unsigned X86EHFlavour0L2DwarfSize;

extern const MCRegisterInfo::DwarfLLVMRegPair X86EHFlavour1L2Dwarf[];
extern const unsigned X86EHFlavour1L2DwarfSize;

extern const MCRegisterInfo::DwarfLLVMRegPair X86EHFlavour2L2Dwarf[];
extern const unsigned X86EHFlavour2L2DwarfSize;

X86GenRegisterInfo::
X86GenRegisterInfo(unsigned RA, unsigned DwarfFlavour, unsigned EHFlavour,
      unsigned PC, unsigned HwMode)
  : TargetRegisterInfo(X86RegInfoDesc, RegisterClasses, RegisterClasses+118,
             SubRegIndexNameTable, SubRegIndexLaneMaskTable,
             LaneBitmask(0xFFFFFFE0), RegClassInfos, HwMode) {
  InitMCRegisterInfo(X86RegDesc, 278, RA, PC,
                     X86MCRegisterClasses, 118,
                     X86RegUnitRoots,
                     163,
                     X86RegDiffLists,
                     X86LaneMaskLists,
                     X86RegStrings,
                     X86RegClassStrings,
                     X86SubRegIdxLists,
                     9,
                     X86SubRegIdxRanges,
                     X86RegEncodingTable);

  switch (DwarfFlavour) {
  default:
    llvm_unreachable("Unknown DWARF flavour");
  case 0:
    mapDwarfRegsToLLVMRegs(X86DwarfFlavour0Dwarf2L, X86DwarfFlavour0Dwarf2LSize, false);
    break;
  case 1:
    mapDwarfRegsToLLVMRegs(X86DwarfFlavour1Dwarf2L, X86DwarfFlavour1Dwarf2LSize, false);
    break;
  case 2:
    mapDwarfRegsToLLVMRegs(X86DwarfFlavour2Dwarf2L, X86DwarfFlavour2Dwarf2LSize, false);
    break;
  }
  switch (EHFlavour) {
  default:
    llvm_unreachable("Unknown DWARF flavour");
  case 0:
    mapDwarfRegsToLLVMRegs(X86EHFlavour0Dwarf2L, X86EHFlavour0Dwarf2LSize, true);
    break;
  case 1:
    mapDwarfRegsToLLVMRegs(X86EHFlavour1Dwarf2L, X86EHFlavour1Dwarf2LSize, true);
    break;
  case 2:
    mapDwarfRegsToLLVMRegs(X86EHFlavour2Dwarf2L, X86EHFlavour2Dwarf2LSize, true);
    break;
  }
  switch (DwarfFlavour) {
  default:
    llvm_unreachable("Unknown DWARF flavour");
  case 0:
    mapLLVMRegsToDwarfRegs(X86DwarfFlavour0L2Dwarf, X86DwarfFlavour0L2DwarfSize, false);
    break;
  case 1:
    mapLLVMRegsToDwarfRegs(X86DwarfFlavour1L2Dwarf, X86DwarfFlavour1L2DwarfSize, false);
    break;
  case 2:
    mapLLVMRegsToDwarfRegs(X86DwarfFlavour2L2Dwarf, X86DwarfFlavour2L2DwarfSize, false);
    break;
  }
  switch (EHFlavour) {
  default:
    llvm_unreachable("Unknown DWARF flavour");
  case 0:
    mapLLVMRegsToDwarfRegs(X86EHFlavour0L2Dwarf, X86EHFlavour0L2DwarfSize, true);
    break;
  case 1:
    mapLLVMRegsToDwarfRegs(X86EHFlavour1L2Dwarf, X86EHFlavour1L2DwarfSize, true);
    break;
  case 2:
    mapLLVMRegsToDwarfRegs(X86EHFlavour2L2Dwarf, X86EHFlavour2L2DwarfSize, true);
    break;
  }
}

static const MCPhysReg CSR_32_SaveList[] = { X86::ESI, X86::EDI, X86::EBX, X86::EBP, 0 };
static const uint32_t CSR_32_RegMask[] = { 0x058703f0, 0x1c002581, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, };
static const MCPhysReg CSR_32EHRet_SaveList[] = { X86::EAX, X86::EDX, X86::ESI, X86::EDI, X86::EBX, X86::EBP, 0 };
static const uint32_t CSR_32EHRet_RegMask[] = { 0x0def83fe, 0x1c002dc1, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, };
static const MCPhysReg CSR_32_AllRegs_SaveList[] = { X86::EAX, X86::EBX, X86::ECX, X86::EDX, X86::EBP, X86::ESI, X86::EDI, 0 };
static const uint32_t CSR_32_AllRegs_RegMask[] = { 0x0fefaffe, 0x1c002fc1, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, };
static const MCPhysReg CSR_32_AllRegs_AVX_SaveList[] = { X86::EAX, X86::EBX, X86::ECX, X86::EDX, X86::EBP, X86::ESI, X86::EDI, X86::YMM0, X86::YMM1, X86::YMM2, X86::YMM3, X86::YMM4, X86::YMM5, X86::YMM6, X86::YMM7, 0 };
static const uint32_t CSR_32_AllRegs_AVX_RegMask[] = { 0x0fefaffe, 0x1c002fc1, 0x00000000, 0x00000000, 0x003fc000, 0x003fc000, 0x00000000, 0x00000000, 0x00000000, };
static const MCPhysReg CSR_32_AllRegs_AVX512_SaveList[] = { X86::EAX, X86::EBX, X86::ECX, X86::EDX, X86::EBP, X86::ESI, X86::EDI, X86::ZMM0, X86::ZMM1, X86::ZMM2, X86::ZMM3, X86::ZMM4, X86::ZMM5, X86::ZMM6, X86::ZMM7, X86::K0, X86::K1, X86::K2, X86::K3, X86::K4, X86::K5, X86::K6, X86::K7, 0 };
static const uint32_t CSR_32_AllRegs_AVX512_RegMask[] = { 0x0fefaffe, 0x1c002fc1, 0x00000000, 0x003fc000, 0x003fc000, 0x003fc000, 0x003fc000, 0x00000000, 0x00000000, };
static const MCPhysReg CSR_32_AllRegs_SSE_SaveList[] = { X86::EAX, X86::EBX, X86::ECX, X86::EDX, X86::EBP, X86::ESI, X86::EDI, X86::XMM0, X86::XMM1, X86::XMM2, X86::XMM3, X86::XMM4, X86::XMM5, X86::XMM6, X86::XMM7, 0 };
static const uint32_t CSR_32_AllRegs_SSE_RegMask[] = { 0x0fefaffe, 0x1c002fc1, 0x00000000, 0x00000000, 0x003fc000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, };
static const MCPhysReg CSR_32_RegCall_SaveList[] = { X86::ESI, X86::EDI, X86::EBX, X86::EBP, X86::ESP, X86::XMM4, X86::XMM5, X86::XMM6, X86::XMM7, 0 };
static const uint32_t CSR_32_RegCall_RegMask[] = { 0x058703f0, 0xfc006583, 0x00000000, 0x00000000, 0x003c0000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, };
static const MCPhysReg CSR_32_RegCall_NoSSE_SaveList[] = { X86::ESI, X86::EDI, X86::EBX, X86::EBP, X86::ESP, 0 };
static const uint32_t CSR_32_RegCall_NoSSE_RegMask[] = { 0x058703f0, 0xfc006583, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, };
static const MCPhysReg CSR_64_SaveList[] = { X86::RBX, X86::R12, X86::R13, X86::R14, X86::R15, X86::RBP, 0 };
static const uint32_t CSR_64_RegMask[] = { 0x018003f0, 0x00060180, 0x00000000, 0x00000000, 0x0000003c, 0x00000000, 0x00000000, 0x3c3c0000, 0x003c3c3c, };
static const MCPhysReg CSR_64EHRet_SaveList[] = { X86::RAX, X86::RDX, X86::RBX, X86::R12, X86::R13, X86::R14, X86::R15, X86::RBP, 0 };
static const uint32_t CSR_64EHRet_RegMask[] = { 0x09e883fe, 0x002709c0, 0x00000000, 0x00000000, 0x0000003c, 0x00000000, 0x00000000, 0x3c3c0000, 0x003c3c3c, };
static const MCPhysReg CSR_64_AllRegs_SaveList[] = { X86::RBX, X86::RCX, X86::RDX, X86::RSI, X86::RDI, X86::R8, X86::R9, X86::R10, X86::R11, X86::R12, X86::R13, X86::R14, X86::R15, X86::RBP, X86::XMM0, X86::XMM1, X86::XMM2, X86::XMM3, X86::XMM4, X86::XMM5, X86::XMM6, X86::XMM7, X86::XMM8, X86::XMM9, X86::XMM10, X86::XMM11, X86::XMM12, X86::XMM13, X86::XMM14, X86::XMM15, X86::RAX, 0 };
static const uint32_t CSR_64_AllRegs_RegMask[] = { 0x0fefaffe, 0x1d3f2fc1, 0x00000000, 0xc0000000, 0x3fffc03f, 0x00000000, 0x00000000, 0xffffc000, 0x003fffff, };
static const MCPhysReg CSR_64_AllRegs_AVX_SaveList[] = { X86::RBX, X86::RCX, X86::RDX, X86::RSI, X86::RDI, X86::R8, X86::R9, X86::R10, X86::R11, X86::R12, X86::R13, X86::R14, X86::R15, X86::RBP, X86::RAX, X86::YMM0, X86::YMM1, X86::YMM2, X86::YMM3, X86::YMM4, X86::YMM5, X86::YMM6, X86::YMM7, X86::YMM8, X86::YMM9, X86::YMM10, X86::YMM11, X86::YMM12, X86::YMM13, X86::YMM14, X86::YMM15, 0 };
static const uint32_t CSR_64_AllRegs_AVX_RegMask[] = { 0x0fefaffe, 0x1d3f2fc1, 0x00000000, 0xc0000000, 0x3fffc03f, 0x3fffc000, 0x00000000, 0xffffc000, 0x003fffff, };
static const MCPhysReg CSR_64_AllRegs_AVX512_SaveList[] = { X86::RBX, X86::RCX, X86::RDX, X86::RSI, X86::RDI, X86::R8, X86::R9, X86::R10, X86::R11, X86::R12, X86::R13, X86::R14, X86::R15, X86::RBP, X86::RAX, X86::ZMM0, X86::ZMM1, X86::ZMM2, X86::ZMM3, X86::ZMM4, X86::ZMM5, X86::ZMM6, X86::ZMM7, X86::ZMM8, X86::ZMM9, X86::ZMM10, X86::ZMM11, X86::ZMM12, X86::ZMM13, X86::ZMM14, X86::ZMM15, X86::ZMM16, X86::ZMM17, X86::ZMM18, X86::ZMM19, X86::ZMM20, X86::ZMM21, X86::ZMM22, X86::ZMM23, X86::ZMM24, X86::ZMM25, X86::ZMM26, X86::ZMM27, X86::ZMM28, X86::ZMM29, X86::ZMM30, X86::ZMM31, X86::K0, X86::K1, X86::K2, X86::K3, X86::K4, X86::K5, X86::K6, X86::K7, 0 };
static const uint32_t CSR_64_AllRegs_AVX512_RegMask[] = { 0x0fefaffe, 0x1d3f2fc1, 0x00000000, 0xc03fc000, 0xffffc03f, 0xffffffff, 0xffffffff, 0xffffffff, 0x003fffff, };
static const MCPhysReg CSR_64_AllRegs_NoSSE_SaveList[] = { X86::RAX, X86::RBX, X86::RCX, X86::RDX, X86::RSI, X86::RDI, X86::R8, X86::R9, X86::R10, X86::R11, X86::R12, X86::R13, X86::R14, X86::R15, X86::RBP, 0 };
static const uint32_t CSR_64_AllRegs_NoSSE_RegMask[] = { 0x0fefaffe, 0x1d3f2fc1, 0x00000000, 0xc0000000, 0x0000003f, 0x00000000, 0x00000000, 0xffffc000, 0x003fffff, };
static const MCPhysReg CSR_64_CXX_TLS_Darwin_PE_SaveList[] = { X86::RBP, 0 };
static const uint32_t CSR_64_CXX_TLS_Darwin_PE_RegMask[] = { 0x008001c0, 0x00020080, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, };
static const MCPhysReg CSR_64_CXX_TLS_Darwin_ViaCopy_SaveList[] = { X86::RBX, X86::R12, X86::R13, X86::R14, X86::R15, X86::RCX, X86::RDX, X86::RSI, X86::R8, X86::R9, X86::R10, X86::R11, 0 };
static const uint32_t CSR_64_CXX_TLS_Darwin_ViaCopy_RegMask[] = { 0x0b28ae30, 0x1d2c2b01, 0x00000000, 0xc0000000, 0x0000003f, 0x00000000, 0x00000000, 0xffffc000, 0x003fffff, };
static const MCPhysReg CSR_64_HHVM_SaveList[] = { X86::R12, 0 };
static const uint32_t CSR_64_HHVM_RegMask[] = { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000004, 0x00000000, 0x00000000, 0x04040000, 0x00040404, };
static const MCPhysReg CSR_64_Intel_OCL_BI_SaveList[] = { X86::RBX, X86::R12, X86::R13, X86::R14, X86::R15, X86::RBP, X86::XMM8, X86::XMM9, X86::XMM10, X86::XMM11, X86::XMM12, X86::XMM13, X86::XMM14, X86::XMM15, 0 };
static const uint32_t CSR_64_Intel_OCL_BI_RegMask[] = { 0x018003f0, 0x00060180, 0x00000000, 0x00000000, 0x3fc0003c, 0x00000000, 0x00000000, 0x3c3c0000, 0x003c3c3c, };
static const MCPhysReg CSR_64_Intel_OCL_BI_AVX_SaveList[] = { X86::RBX, X86::R12, X86::R13, X86::R14, X86::R15, X86::RBP, X86::YMM8, X86::YMM9, X86::YMM10, X86::YMM11, X86::YMM12, X86::YMM13, X86::YMM14, X86::YMM15, 0 };
static const uint32_t CSR_64_Intel_OCL_BI_AVX_RegMask[] = { 0x018003f0, 0x00060180, 0x00000000, 0x00000000, 0x3fc0003c, 0x3fc00000, 0x00000000, 0x3c3c0000, 0x003c3c3c, };
static const MCPhysReg CSR_64_Intel_OCL_BI_AVX512_SaveList[] = { X86::RBX, X86::RDI, X86::RSI, X86::R14, X86::R15, X86::ZMM16, X86::ZMM17, X86::ZMM18, X86::ZMM19, X86::ZMM20, X86::ZMM21, X86::ZMM22, X86::ZMM23, X86::ZMM24, X86::ZMM25, X86::ZMM26, X86::ZMM27, X86::ZMM28, X86::ZMM29, X86::ZMM30, X86::ZMM31, X86::K4, X86::K5, X86::K6, X86::K7, 0 };
static const uint32_t CSR_64_Intel_OCL_BI_AVX512_RegMask[] = { 0x05070230, 0x1d142501, 0x00000000, 0x003c0000, 0xc0000030, 0xc0003fff, 0xc0003fff, 0x30303fff, 0x00303030, };
static const MCPhysReg CSR_64_MostRegs_SaveList[] = { X86::RBX, X86::RCX, X86::RDX, X86::RSI, X86::RDI, X86::R8, X86::R9, X86::R10, X86::R11, X86::R12, X86::R13, X86::R14, X86::R15, X86::RBP, X86::XMM0, X86::XMM1, X86::XMM2, X86::XMM3, X86::XMM4, X86::XMM5, X86::XMM6, X86::XMM7, X86::XMM8, X86::XMM9, X86::XMM10, X86::XMM11, X86::XMM12, X86::XMM13, X86::XMM14, X86::XMM15, 0 };
static const uint32_t CSR_64_MostRegs_RegMask[] = { 0x0fafaff0, 0x1d3e2f81, 0x00000000, 0xc0000000, 0x3fffc03f, 0x00000000, 0x00000000, 0xffffc000, 0x003fffff, };
static const MCPhysReg CSR_64_RT_AllRegs_SaveList[] = { X86::RBX, X86::R12, X86::R13, X86::R14, X86::R15, X86::RBP, X86::RAX, X86::RCX, X86::RDX, X86::RSI, X86::RDI, X86::R8, X86::R9, X86::R10, X86::RSP, X86::XMM0, X86::XMM1, X86::XMM2, X86::XMM3, X86::XMM4, X86::XMM5, X86::XMM6, X86::XMM7, X86::XMM8, X86::XMM9, X86::XMM10, X86::XMM11, X86::XMM12, X86::XMM13, X86::XMM14, X86::XMM15, 0 };
static const uint32_t CSR_64_RT_AllRegs_RegMask[] = { 0x0fefaffe, 0xff3f6fc3, 0x00000000, 0xc0000000, 0x3fffc03d, 0x00000000, 0x00000000, 0xfdfdc000, 0x003dfdfd, };
static const MCPhysReg CSR_64_RT_AllRegs_AVX_SaveList[] = { X86::RBX, X86::R12, X86::R13, X86::R14, X86::R15, X86::RBP, X86::RAX, X86::RCX, X86::RDX, X86::RSI, X86::RDI, X86::R8, X86::R9, X86::R10, X86::RSP, X86::YMM0, X86::YMM1, X86::YMM2, X86::YMM3, X86::YMM4, X86::YMM5, X86::YMM6, X86::YMM7, X86::YMM8, X86::YMM9, X86::YMM10, X86::YMM11, X86::YMM12, X86::YMM13, X86::YMM14, X86::YMM15, 0 };
static const uint32_t CSR_64_RT_AllRegs_AVX_RegMask[] = { 0x0fefaffe, 0xff3f6fc3, 0x00000000, 0xc0000000, 0x3fffc03d, 0x3fffc000, 0x00000000, 0xfdfdc000, 0x003dfdfd, };
static const MCPhysReg CSR_64_RT_MostRegs_SaveList[] = { X86::RBX, X86::R12, X86::R13, X86::R14, X86::R15, X86::RBP, X86::RAX, X86::RCX, X86::RDX, X86::RSI, X86::RDI, X86::R8, X86::R9, X86::R10, X86::RSP, 0 };
static const uint32_t CSR_64_RT_MostRegs_RegMask[] = { 0x0fefaffe, 0xff3f6fc3, 0x00000000, 0xc0000000, 0x0000003d, 0x00000000, 0x00000000, 0xfdfdc000, 0x003dfdfd, };
static const MCPhysReg CSR_64_SwiftError_SaveList[] = { X86::RBX, X86::R13, X86::R14, X86::R15, X86::RBP, 0 };
static const uint32_t CSR_64_SwiftError_RegMask[] = { 0x018003f0, 0x00060180, 0x00000000, 0x00000000, 0x00000038, 0x00000000, 0x00000000, 0x38380000, 0x00383838, };
static const MCPhysReg CSR_64_TLS_Darwin_SaveList[] = { X86::RBX, X86::R12, X86::R13, X86::R14, X86::R15, X86::RBP, X86::RCX, X86::RDX, X86::RSI, X86::R8, X86::R9, X86::R10, X86::R11, 0 };
static const uint32_t CSR_64_TLS_Darwin_RegMask[] = { 0x0ba8aff0, 0x1d2e2b81, 0x00000000, 0xc0000000, 0x0000003f, 0x00000000, 0x00000000, 0xffffc000, 0x003fffff, };
static const MCPhysReg CSR_NoRegs_SaveList[] = { 0 };
static const uint32_t CSR_NoRegs_RegMask[] = { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, };
static const MCPhysReg CSR_SysV64_RegCall_SaveList[] = { X86::RBX, X86::RBP, X86::RSP, X86::R12, X86::R13, X86::R14, X86::R15, X86::XMM8, X86::XMM9, X86::XMM10, X86::XMM11, X86::XMM12, X86::XMM13, X86::XMM14, X86::XMM15, 0 };
static const uint32_t CSR_SysV64_RegCall_RegMask[] = { 0x018003f0, 0xe2064182, 0x00000000, 0x00000000, 0x3fc0003c, 0x00000000, 0x00000000, 0x3c3c0000, 0x003c3c3c, };
static const MCPhysReg CSR_SysV64_RegCall_NoSSE_SaveList[] = { X86::RBX, X86::RBP, X86::RSP, X86::R12, X86::R13, X86::R14, X86::R15, 0 };
static const uint32_t CSR_SysV64_RegCall_NoSSE_RegMask[] = { 0x018003f0, 0xe2064182, 0x00000000, 0x00000000, 0x0000003c, 0x00000000, 0x00000000, 0x3c3c0000, 0x003c3c3c, };
static const MCPhysReg CSR_Win64_SaveList[] = { X86::RBX, X86::RBP, X86::RDI, X86::RSI, X86::R12, X86::R13, X86::R14, X86::R15, X86::XMM6, X86::XMM7, X86::XMM8, X86::XMM9, X86::XMM10, X86::XMM11, X86::XMM12, X86::XMM13, X86::XMM14, X86::XMM15, 0 };
static const uint32_t CSR_Win64_RegMask[] = { 0x058703f0, 0x1d162581, 0x00000000, 0x00000000, 0x3ff0003c, 0x00000000, 0x00000000, 0x3c3c0000, 0x003c3c3c, };
static const MCPhysReg CSR_Win64_Intel_OCL_BI_AVX_SaveList[] = { X86::RBX, X86::RBP, X86::RDI, X86::RSI, X86::R12, X86::R13, X86::R14, X86::R15, X86::YMM6, X86::YMM7, X86::YMM8, X86::YMM9, X86::YMM10, X86::YMM11, X86::YMM12, X86::YMM13, X86::YMM14, X86::YMM15, 0 };
static const uint32_t CSR_Win64_Intel_OCL_BI_AVX_RegMask[] = { 0x058703f0, 0x1d162581, 0x00000000, 0x00000000, 0x3ff0003c, 0x3ff00000, 0x00000000, 0x3c3c0000, 0x003c3c3c, };
static const MCPhysReg CSR_Win64_Intel_OCL_BI_AVX512_SaveList[] = { X86::RBX, X86::RBP, X86::RDI, X86::RSI, X86::R12, X86::R13, X86::R14, X86::R15, X86::ZMM6, X86::ZMM7, X86::ZMM8, X86::ZMM9, X86::ZMM10, X86::ZMM11, X86::ZMM12, X86::ZMM13, X86::ZMM14, X86::ZMM15, X86::ZMM16, X86::ZMM17, X86::ZMM18, X86::ZMM19, X86::ZMM20, X86::ZMM21, X86::K4, X86::K5, X86::K6, X86::K7, 0 };
static const uint32_t CSR_Win64_Intel_OCL_BI_AVX512_RegMask[] = { 0x058703f0, 0x1d162581, 0x00000000, 0x003c0000, 0xfff0003c, 0xfff0000f, 0xfff0000f, 0x3c3c000f, 0x003c3c3c, };
static const MCPhysReg CSR_Win64_NoSSE_SaveList[] = { X86::RBX, X86::RBP, X86::RDI, X86::RSI, X86::R12, X86::R13, X86::R14, X86::R15, 0 };
static const uint32_t CSR_Win64_NoSSE_RegMask[] = { 0x058703f0, 0x1d162581, 0x00000000, 0x00000000, 0x0000003c, 0x00000000, 0x00000000, 0x3c3c0000, 0x003c3c3c, };
static const MCPhysReg CSR_Win64_RegCall_SaveList[] = { X86::RBX, X86::RBP, X86::RSP, X86::R10, X86::R11, X86::R12, X86::R13, X86::R14, X86::R15, X86::XMM8, X86::XMM9, X86::XMM10, X86::XMM11, X86::XMM12, X86::XMM13, X86::XMM14, X86::XMM15, 0 };
static const uint32_t CSR_Win64_RegCall_RegMask[] = { 0x018003f0, 0xe2064182, 0x00000000, 0x00000000, 0x3fc0003f, 0x00000000, 0x00000000, 0x3f3f0000, 0x003f3f3f, };
static const MCPhysReg CSR_Win64_RegCall_NoSSE_SaveList[] = { X86::RBX, X86::RBP, X86::RSP, X86::R10, X86::R11, X86::R12, X86::R13, X86::R14, X86::R15, 0 };
static const uint32_t CSR_Win64_RegCall_NoSSE_RegMask[] = { 0x018003f0, 0xe2064182, 0x00000000, 0x00000000, 0x0000003f, 0x00000000, 0x00000000, 0x3f3f0000, 0x003f3f3f, };
static const MCPhysReg CSR_Win64_SwiftError_SaveList[] = { X86::RBX, X86::RBP, X86::RDI, X86::RSI, X86::R13, X86::R14, X86::R15, X86::XMM6, X86::XMM7, X86::XMM8, X86::XMM9, X86::XMM10, X86::XMM11, X86::XMM12, X86::XMM13, X86::XMM14, X86::XMM15, 0 };
static const uint32_t CSR_Win64_SwiftError_RegMask[] = { 0x058703f0, 0x1d162581, 0x00000000, 0x00000000, 0x3ff00038, 0x00000000, 0x00000000, 0x38380000, 0x00383838, };


ArrayRef<const uint32_t *> X86GenRegisterInfo::getRegMasks() const {
  static const uint32_t *const Masks[] = {
    CSR_32_RegMask,
    CSR_32EHRet_RegMask,
    CSR_32_AllRegs_RegMask,
    CSR_32_AllRegs_AVX_RegMask,
    CSR_32_AllRegs_AVX512_RegMask,
    CSR_32_AllRegs_SSE_RegMask,
    CSR_32_RegCall_RegMask,
    CSR_32_RegCall_NoSSE_RegMask,
    CSR_64_RegMask,
    CSR_64EHRet_RegMask,
    CSR_64_AllRegs_RegMask,
    CSR_64_AllRegs_AVX_RegMask,
    CSR_64_AllRegs_AVX512_RegMask,
    CSR_64_AllRegs_NoSSE_RegMask,
    CSR_64_CXX_TLS_Darwin_PE_RegMask,
    CSR_64_CXX_TLS_Darwin_ViaCopy_RegMask,
    CSR_64_HHVM_RegMask,
    CSR_64_Intel_OCL_BI_RegMask,
    CSR_64_Intel_OCL_BI_AVX_RegMask,
    CSR_64_Intel_OCL_BI_AVX512_RegMask,
    CSR_64_MostRegs_RegMask,
    CSR_64_RT_AllRegs_RegMask,
    CSR_64_RT_AllRegs_AVX_RegMask,
    CSR_64_RT_MostRegs_RegMask,
    CSR_64_SwiftError_RegMask,
    CSR_64_TLS_Darwin_RegMask,
    CSR_NoRegs_RegMask,
    CSR_SysV64_RegCall_RegMask,
    CSR_SysV64_RegCall_NoSSE_RegMask,
    CSR_Win64_RegMask,
    CSR_Win64_Intel_OCL_BI_AVX_RegMask,
    CSR_Win64_Intel_OCL_BI_AVX512_RegMask,
    CSR_Win64_NoSSE_RegMask,
    CSR_Win64_RegCall_RegMask,
    CSR_Win64_RegCall_NoSSE_RegMask,
    CSR_Win64_SwiftError_RegMask,
  };
  return makeArrayRef(Masks);
}

ArrayRef<const char *> X86GenRegisterInfo::getRegMaskNames() const {
  static const char *const Names[] = {
    "CSR_32",
    "CSR_32EHRet",
    "CSR_32_AllRegs",
    "CSR_32_AllRegs_AVX",
    "CSR_32_AllRegs_AVX512",
    "CSR_32_AllRegs_SSE",
    "CSR_32_RegCall",
    "CSR_32_RegCall_NoSSE",
    "CSR_64",
    "CSR_64EHRet",
    "CSR_64_AllRegs",
    "CSR_64_AllRegs_AVX",
    "CSR_64_AllRegs_AVX512",
    "CSR_64_AllRegs_NoSSE",
    "CSR_64_CXX_TLS_Darwin_PE",
    "CSR_64_CXX_TLS_Darwin_ViaCopy",
    "CSR_64_HHVM",
    "CSR_64_Intel_OCL_BI",
    "CSR_64_Intel_OCL_BI_AVX",
    "CSR_64_Intel_OCL_BI_AVX512",
    "CSR_64_MostRegs",
    "CSR_64_RT_AllRegs",
    "CSR_64_RT_AllRegs_AVX",
    "CSR_64_RT_MostRegs",
    "CSR_64_SwiftError",
    "CSR_64_TLS_Darwin",
    "CSR_NoRegs",
    "CSR_SysV64_RegCall",
    "CSR_SysV64_RegCall_NoSSE",
    "CSR_Win64",
    "CSR_Win64_Intel_OCL_BI_AVX",
    "CSR_Win64_Intel_OCL_BI_AVX512",
    "CSR_Win64_NoSSE",
    "CSR_Win64_RegCall",
    "CSR_Win64_RegCall_NoSSE",
    "CSR_Win64_SwiftError",
  };
  return makeArrayRef(Names);
}

const X86FrameLowering *
X86GenRegisterInfo::getFrameLowering(const MachineFunction &MF) {
  return static_cast<const X86FrameLowering *>(
      MF.getSubtarget().getFrameLowering());
}

} // end namespace llvm

#endif // GET_REGINFO_TARGET_DESC


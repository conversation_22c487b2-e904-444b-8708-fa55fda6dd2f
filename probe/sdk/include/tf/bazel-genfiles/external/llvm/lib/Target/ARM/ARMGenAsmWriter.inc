/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Assembly Writer Source Fragment                                            *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/// printInstruction - This method is automatically generated by tablegen
/// from the instruction set description.
void ARMInstPrinter::printInstruction(const MCInst *MI, const MCSubtargetInfo &STI, raw_ostream &O) {
  static const char AsmStrs[] = {
  /* 0 */ 's', 'h', 'a', '1', 's', 'u', '0', '.', '3', '2', 9, 0,
  /* 12 */ 's', 'h', 'a', '2', '5', '6', 's', 'u', '0', '.', '3', '2', 9, 0,
  /* 26 */ 's', 'h', 'a', '1', 's', 'u', '1', '.', '3', '2', 9, 0,
  /* 38 */ 's', 'h', 'a', '2', '5', '6', 's', 'u', '1', '.', '3', '2', 9, 0,
  /* 52 */ 's', 'h', 'a', '2', '5', '6', 'h', '2', '.', '3', '2', 9, 0,
  /* 65 */ 's', 'h', 'a', '1', 'c', '.', '3', '2', 9, 0,
  /* 75 */ 's', 'h', 'a', '1', 'h', '.', '3', '2', 9, 0,
  /* 85 */ 's', 'h', 'a', '2', '5', '6', 'h', '.', '3', '2', 9, 0,
  /* 97 */ 's', 'h', 'a', '1', 'm', '.', '3', '2', 9, 0,
  /* 107 */ 's', 'h', 'a', '1', 'p', '.', '3', '2', 9, 0,
  /* 117 */ 'v', 'c', 'v', 't', 'a', '.', 's', '3', '2', '.', 'f', '3', '2', 9, 0,
  /* 132 */ 'v', 'c', 'v', 't', 'm', '.', 's', '3', '2', '.', 'f', '3', '2', 9, 0,
  /* 147 */ 'v', 'c', 'v', 't', 'n', '.', 's', '3', '2', '.', 'f', '3', '2', 9, 0,
  /* 162 */ 'v', 'c', 'v', 't', 'p', '.', 's', '3', '2', '.', 'f', '3', '2', 9, 0,
  /* 177 */ 'v', 'c', 'v', 't', 'a', '.', 'u', '3', '2', '.', 'f', '3', '2', 9, 0,
  /* 192 */ 'v', 'c', 'v', 't', 'm', '.', 'u', '3', '2', '.', 'f', '3', '2', 9, 0,
  /* 207 */ 'v', 'c', 'v', 't', 'n', '.', 'u', '3', '2', '.', 'f', '3', '2', 9, 0,
  /* 222 */ 'v', 'c', 'v', 't', 'p', '.', 'u', '3', '2', '.', 'f', '3', '2', 9, 0,
  /* 237 */ 'v', 'c', 'm', 'l', 'a', '.', 'f', '3', '2', 9, 0,
  /* 248 */ 'v', 'r', 'i', 'n', 't', 'a', '.', 'f', '3', '2', 9, 0,
  /* 260 */ 'v', 'c', 'a', 'd', 'd', '.', 'f', '3', '2', 9, 0,
  /* 271 */ 'v', 's', 'e', 'l', 'g', 'e', '.', 'f', '3', '2', 9, 0,
  /* 283 */ 'v', 'm', 'i', 'n', 'n', 'm', '.', 'f', '3', '2', 9, 0,
  /* 295 */ 'v', 'm', 'a', 'x', 'n', 'm', '.', 'f', '3', '2', 9, 0,
  /* 307 */ 'v', 'r', 'i', 'n', 't', 'm', '.', 'f', '3', '2', 9, 0,
  /* 319 */ 'v', 'r', 'i', 'n', 't', 'n', '.', 'f', '3', '2', 9, 0,
  /* 331 */ 'v', 'r', 'i', 'n', 't', 'p', '.', 'f', '3', '2', 9, 0,
  /* 343 */ 'v', 's', 'e', 'l', 'e', 'q', '.', 'f', '3', '2', 9, 0,
  /* 355 */ 'v', 's', 'e', 'l', 'v', 's', '.', 'f', '3', '2', 9, 0,
  /* 367 */ 'v', 's', 'e', 'l', 'g', 't', '.', 'f', '3', '2', 9, 0,
  /* 379 */ 'v', 'r', 'i', 'n', 't', 'x', '.', 'f', '3', '2', 9, 0,
  /* 391 */ 'v', 'r', 'i', 'n', 't', 'z', '.', 'f', '3', '2', 9, 0,
  /* 403 */ 'l', 'd', 'c', '2', 9, 0,
  /* 409 */ 'm', 'r', 'c', '2', 9, 0,
  /* 415 */ 'm', 'r', 'r', 'c', '2', 9, 0,
  /* 422 */ 's', 't', 'c', '2', 9, 0,
  /* 428 */ 'c', 'd', 'p', '2', 9, 0,
  /* 434 */ 'm', 'c', 'r', '2', 9, 0,
  /* 440 */ 'm', 'c', 'r', 'r', '2', 9, 0,
  /* 447 */ 'v', 'c', 'v', 't', 'a', '.', 's', '3', '2', '.', 'f', '6', '4', 9, 0,
  /* 462 */ 'v', 'c', 'v', 't', 'm', '.', 's', '3', '2', '.', 'f', '6', '4', 9, 0,
  /* 477 */ 'v', 'c', 'v', 't', 'n', '.', 's', '3', '2', '.', 'f', '6', '4', 9, 0,
  /* 492 */ 'v', 'c', 'v', 't', 'p', '.', 's', '3', '2', '.', 'f', '6', '4', 9, 0,
  /* 507 */ 'v', 'c', 'v', 't', 'a', '.', 'u', '3', '2', '.', 'f', '6', '4', 9, 0,
  /* 522 */ 'v', 'c', 'v', 't', 'm', '.', 'u', '3', '2', '.', 'f', '6', '4', 9, 0,
  /* 537 */ 'v', 'c', 'v', 't', 'n', '.', 'u', '3', '2', '.', 'f', '6', '4', 9, 0,
  /* 552 */ 'v', 'c', 'v', 't', 'p', '.', 'u', '3', '2', '.', 'f', '6', '4', 9, 0,
  /* 567 */ 'v', 'r', 'i', 'n', 't', 'a', '.', 'f', '6', '4', 9, 0,
  /* 579 */ 'v', 's', 'e', 'l', 'g', 'e', '.', 'f', '6', '4', 9, 0,
  /* 591 */ 'v', 'm', 'i', 'n', 'n', 'm', '.', 'f', '6', '4', 9, 0,
  /* 603 */ 'v', 'm', 'a', 'x', 'n', 'm', '.', 'f', '6', '4', 9, 0,
  /* 615 */ 'v', 'r', 'i', 'n', 't', 'm', '.', 'f', '6', '4', 9, 0,
  /* 627 */ 'v', 'r', 'i', 'n', 't', 'n', '.', 'f', '6', '4', 9, 0,
  /* 639 */ 'v', 'r', 'i', 'n', 't', 'p', '.', 'f', '6', '4', 9, 0,
  /* 651 */ 'v', 's', 'e', 'l', 'e', 'q', '.', 'f', '6', '4', 9, 0,
  /* 663 */ 'v', 's', 'e', 'l', 'v', 's', '.', 'f', '6', '4', 9, 0,
  /* 675 */ 'v', 's', 'e', 'l', 'g', 't', '.', 'f', '6', '4', 9, 0,
  /* 687 */ 'v', 'm', 'u', 'l', 'l', '.', 'p', '6', '4', 9, 0,
  /* 698 */ 'v', 'c', 'v', 't', 'a', '.', 's', '3', '2', '.', 'f', '1', '6', 9, 0,
  /* 713 */ 'v', 'c', 'v', 't', 'm', '.', 's', '3', '2', '.', 'f', '1', '6', 9, 0,
  /* 728 */ 'v', 'c', 'v', 't', 'n', '.', 's', '3', '2', '.', 'f', '1', '6', 9, 0,
  /* 743 */ 'v', 'c', 'v', 't', 'p', '.', 's', '3', '2', '.', 'f', '1', '6', 9, 0,
  /* 758 */ 'v', 'c', 'v', 't', 'a', '.', 'u', '3', '2', '.', 'f', '1', '6', 9, 0,
  /* 773 */ 'v', 'c', 'v', 't', 'm', '.', 'u', '3', '2', '.', 'f', '1', '6', 9, 0,
  /* 788 */ 'v', 'c', 'v', 't', 'n', '.', 'u', '3', '2', '.', 'f', '1', '6', 9, 0,
  /* 803 */ 'v', 'c', 'v', 't', 'p', '.', 'u', '3', '2', '.', 'f', '1', '6', 9, 0,
  /* 818 */ 'v', 'c', 'v', 't', 'a', '.', 's', '1', '6', '.', 'f', '1', '6', 9, 0,
  /* 833 */ 'v', 'c', 'v', 't', 'm', '.', 's', '1', '6', '.', 'f', '1', '6', 9, 0,
  /* 848 */ 'v', 'c', 'v', 't', 'n', '.', 's', '1', '6', '.', 'f', '1', '6', 9, 0,
  /* 863 */ 'v', 'c', 'v', 't', 'p', '.', 's', '1', '6', '.', 'f', '1', '6', 9, 0,
  /* 878 */ 'v', 'c', 'v', 't', 'a', '.', 'u', '1', '6', '.', 'f', '1', '6', 9, 0,
  /* 893 */ 'v', 'c', 'v', 't', 'm', '.', 'u', '1', '6', '.', 'f', '1', '6', 9, 0,
  /* 908 */ 'v', 'c', 'v', 't', 'n', '.', 'u', '1', '6', '.', 'f', '1', '6', 9, 0,
  /* 923 */ 'v', 'c', 'v', 't', 'p', '.', 'u', '1', '6', '.', 'f', '1', '6', 9, 0,
  /* 938 */ 'v', 'c', 'm', 'l', 'a', '.', 'f', '1', '6', 9, 0,
  /* 949 */ 'v', 'r', 'i', 'n', 't', 'a', '.', 'f', '1', '6', 9, 0,
  /* 961 */ 'v', 'c', 'a', 'd', 'd', '.', 'f', '1', '6', 9, 0,
  /* 972 */ 'v', 's', 'e', 'l', 'g', 'e', '.', 'f', '1', '6', 9, 0,
  /* 984 */ 'v', 'f', 'm', 'a', 'l', '.', 'f', '1', '6', 9, 0,
  /* 995 */ 'v', 'f', 'm', 's', 'l', '.', 'f', '1', '6', 9, 0,
  /* 1006 */ 'v', 'm', 'i', 'n', 'n', 'm', '.', 'f', '1', '6', 9, 0,
  /* 1018 */ 'v', 'm', 'a', 'x', 'n', 'm', '.', 'f', '1', '6', 9, 0,
  /* 1030 */ 'v', 'r', 'i', 'n', 't', 'm', '.', 'f', '1', '6', 9, 0,
  /* 1042 */ 'v', 'r', 'i', 'n', 't', 'n', '.', 'f', '1', '6', 9, 0,
  /* 1054 */ 'v', 'r', 'i', 'n', 't', 'p', '.', 'f', '1', '6', 9, 0,
  /* 1066 */ 'v', 's', 'e', 'l', 'e', 'q', '.', 'f', '1', '6', 9, 0,
  /* 1078 */ 'v', 'i', 'n', 's', '.', 'f', '1', '6', 9, 0,
  /* 1088 */ 'v', 's', 'e', 'l', 'v', 's', '.', 'f', '1', '6', 9, 0,
  /* 1100 */ 'v', 's', 'e', 'l', 'g', 't', '.', 'f', '1', '6', 9, 0,
  /* 1112 */ 'v', 'r', 'i', 'n', 't', 'x', '.', 'f', '1', '6', 9, 0,
  /* 1124 */ 'v', 'm', 'o', 'v', 'x', '.', 'f', '1', '6', 9, 0,
  /* 1135 */ 'v', 'r', 'i', 'n', 't', 'z', '.', 'f', '1', '6', 9, 0,
  /* 1147 */ 'a', 'e', 's', 'i', 'm', 'c', '.', '8', 9, 0,
  /* 1157 */ 'a', 'e', 's', 'm', 'c', '.', '8', 9, 0,
  /* 1166 */ 'a', 'e', 's', 'd', '.', '8', 9, 0,
  /* 1174 */ 'a', 'e', 's', 'e', '.', '8', 9, 0,
  /* 1182 */ 'v', 's', 'd', 'o', 't', '.', 's', '8', 9, 0,
  /* 1192 */ 'v', 'u', 'd', 'o', 't', '.', 'u', '8', 9, 0,
  /* 1202 */ 'r', 'f', 'e', 'd', 'a', 9, 0,
  /* 1209 */ 'r', 'f', 'e', 'i', 'a', 9, 0,
  /* 1216 */ 'c', 'r', 'c', '3', '2', 'b', 9, 0,
  /* 1224 */ 'c', 'r', 'c', '3', '2', 'c', 'b', 9, 0,
  /* 1233 */ 'r', 'f', 'e', 'd', 'b', 9, 0,
  /* 1240 */ 'r', 'f', 'e', 'i', 'b', 9, 0,
  /* 1247 */ 'd', 'm', 'b', 9, 0,
  /* 1252 */ 'd', 's', 'b', 9, 0,
  /* 1257 */ 'i', 's', 'b', 9, 0,
  /* 1262 */ 't', 's', 'b', 9, 0,
  /* 1267 */ 'h', 'v', 'c', 9, 0,
  /* 1272 */ 'p', 'l', 'd', 9, 0,
  /* 1277 */ 's', 'e', 't', 'e', 'n', 'd', 9, 0,
  /* 1285 */ 'u', 'd', 'f', 9, 0,
  /* 1290 */ 'c', 'r', 'c', '3', '2', 'h', 9, 0,
  /* 1298 */ 'c', 'r', 'c', '3', '2', 'c', 'h', 9, 0,
  /* 1307 */ 'p', 'l', 'i', 9, 0,
  /* 1312 */ 'l', 'd', 'c', '2', 'l', 9, 0,
  /* 1319 */ 's', 't', 'c', '2', 'l', 9, 0,
  /* 1326 */ 'b', 'l', 9, 0,
  /* 1330 */ 's', 'e', 't', 'p', 'a', 'n', 9, 0,
  /* 1338 */ 'c', 'p', 's', 9, 0,
  /* 1343 */ 'm', 'o', 'v', 's', 9, 0,
  /* 1349 */ 'h', 'l', 't', 9, 0,
  /* 1354 */ 'b', 'k', 'p', 't', 9, 0,
  /* 1360 */ 'h', 'v', 'c', '.', 'w', 9, 0,
  /* 1367 */ 'u', 'd', 'f', '.', 'w', 9, 0,
  /* 1374 */ 'c', 'r', 'c', '3', '2', 'w', 9, 0,
  /* 1382 */ 'c', 'r', 'c', '3', '2', 'c', 'w', 9, 0,
  /* 1391 */ 'p', 'l', 'd', 'w', 9, 0,
  /* 1397 */ 'b', 'x', 9, 0,
  /* 1401 */ 'b', 'l', 'x', 9, 0,
  /* 1406 */ 'c', 'b', 'z', 9, 0,
  /* 1411 */ 'c', 'b', 'n', 'z', 9, 0,
  /* 1417 */ 's', 'r', 's', 'd', 'a', 9, 's', 'p', '!', ',', 32, 0,
  /* 1429 */ 's', 'r', 's', 'i', 'a', 9, 's', 'p', '!', ',', 32, 0,
  /* 1441 */ 's', 'r', 's', 'd', 'b', 9, 's', 'p', '!', ',', 32, 0,
  /* 1453 */ 's', 'r', 's', 'i', 'b', 9, 's', 'p', '!', ',', 32, 0,
  /* 1465 */ 's', 'r', 's', 'd', 'a', 9, 's', 'p', ',', 32, 0,
  /* 1476 */ 's', 'r', 's', 'i', 'a', 9, 's', 'p', ',', 32, 0,
  /* 1487 */ 's', 'r', 's', 'd', 'b', 9, 's', 'p', ',', 32, 0,
  /* 1498 */ 's', 'r', 's', 'i', 'b', 9, 's', 'p', ',', 32, 0,
  /* 1509 */ '#', 32, 'X', 'R', 'a', 'y', 32, 'F', 'u', 'n', 'c', 't', 'i', 'o', 'n', 32, 'P', 'a', 't', 'c', 'h', 'a', 'b', 'l', 'e', 32, 'R', 'E', 'T', '.', 0,
  /* 1540 */ '#', 32, 'X', 'R', 'a', 'y', 32, 'T', 'y', 'p', 'e', 'd', 32, 'E', 'v', 'e', 'n', 't', 32, 'L', 'o', 'g', '.', 0,
  /* 1564 */ '#', 32, 'X', 'R', 'a', 'y', 32, 'C', 'u', 's', 't', 'o', 'm', 32, 'E', 'v', 'e', 'n', 't', 32, 'L', 'o', 'g', '.', 0,
  /* 1589 */ '#', 32, 'X', 'R', 'a', 'y', 32, 'F', 'u', 'n', 'c', 't', 'i', 'o', 'n', 32, 'E', 'n', 't', 'e', 'r', '.', 0,
  /* 1612 */ '#', 32, 'X', 'R', 'a', 'y', 32, 'T', 'a', 'i', 'l', 32, 'C', 'a', 'l', 'l', 32, 'E', 'x', 'i', 't', '.', 0,
  /* 1635 */ '#', 32, 'X', 'R', 'a', 'y', 32, 'F', 'u', 'n', 'c', 't', 'i', 'o', 'n', 32, 'E', 'x', 'i', 't', '.', 0,
  /* 1657 */ '_', '_', 'b', 'r', 'k', 'd', 'i', 'v', '0', 0,
  /* 1667 */ 'v', 'l', 'd', '1', 0,
  /* 1672 */ 'd', 'c', 'p', 's', '1', 0,
  /* 1678 */ 'v', 's', 't', '1', 0,
  /* 1683 */ 'v', 'r', 'e', 'v', '3', '2', 0,
  /* 1690 */ 'l', 'd', 'c', '2', 0,
  /* 1695 */ 'm', 'r', 'c', '2', 0,
  /* 1700 */ 'm', 'r', 'r', 'c', '2', 0,
  /* 1706 */ 's', 't', 'c', '2', 0,
  /* 1711 */ 'v', 'l', 'd', '2', 0,
  /* 1716 */ 'c', 'd', 'p', '2', 0,
  /* 1721 */ 'm', 'c', 'r', '2', 0,
  /* 1726 */ 'm', 'c', 'r', 'r', '2', 0,
  /* 1732 */ 'd', 'c', 'p', 's', '2', 0,
  /* 1738 */ 'v', 's', 't', '2', 0,
  /* 1743 */ 'v', 'l', 'd', '3', 0,
  /* 1748 */ 'd', 'c', 'p', 's', '3', 0,
  /* 1754 */ 'v', 's', 't', '3', 0,
  /* 1759 */ 'v', 'r', 'e', 'v', '6', '4', 0,
  /* 1766 */ 'v', 'l', 'd', '4', 0,
  /* 1771 */ 'v', 's', 't', '4', 0,
  /* 1776 */ 's', 'x', 't', 'a', 'b', '1', '6', 0,
  /* 1784 */ 'u', 'x', 't', 'a', 'b', '1', '6', 0,
  /* 1792 */ 's', 'x', 't', 'b', '1', '6', 0,
  /* 1799 */ 'u', 'x', 't', 'b', '1', '6', 0,
  /* 1806 */ 's', 'h', 's', 'u', 'b', '1', '6', 0,
  /* 1814 */ 'u', 'h', 's', 'u', 'b', '1', '6', 0,
  /* 1822 */ 'u', 'q', 's', 'u', 'b', '1', '6', 0,
  /* 1830 */ 's', 's', 'u', 'b', '1', '6', 0,
  /* 1837 */ 'u', 's', 'u', 'b', '1', '6', 0,
  /* 1844 */ 's', 'h', 'a', 'd', 'd', '1', '6', 0,
  /* 1852 */ 'u', 'h', 'a', 'd', 'd', '1', '6', 0,
  /* 1860 */ 'u', 'q', 'a', 'd', 'd', '1', '6', 0,
  /* 1868 */ 's', 'a', 'd', 'd', '1', '6', 0,
  /* 1875 */ 'u', 'a', 'd', 'd', '1', '6', 0,
  /* 1882 */ 's', 's', 'a', 't', '1', '6', 0,
  /* 1889 */ 'u', 's', 'a', 't', '1', '6', 0,
  /* 1896 */ 'v', 'r', 'e', 'v', '1', '6', 0,
  /* 1903 */ 'u', 's', 'a', 'd', 'a', '8', 0,
  /* 1910 */ 's', 'h', 's', 'u', 'b', '8', 0,
  /* 1917 */ 'u', 'h', 's', 'u', 'b', '8', 0,
  /* 1924 */ 'u', 'q', 's', 'u', 'b', '8', 0,
  /* 1931 */ 's', 's', 'u', 'b', '8', 0,
  /* 1937 */ 'u', 's', 'u', 'b', '8', 0,
  /* 1943 */ 'u', 's', 'a', 'd', '8', 0,
  /* 1949 */ 's', 'h', 'a', 'd', 'd', '8', 0,
  /* 1956 */ 'u', 'h', 'a', 'd', 'd', '8', 0,
  /* 1963 */ 'u', 'q', 'a', 'd', 'd', '8', 0,
  /* 1970 */ 's', 'a', 'd', 'd', '8', 0,
  /* 1976 */ 'u', 'a', 'd', 'd', '8', 0,
  /* 1982 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'E', 'N', 'D', 0,
  /* 1995 */ 'B', 'U', 'N', 'D', 'L', 'E', 0,
  /* 2002 */ 'D', 'B', 'G', '_', 'V', 'A', 'L', 'U', 'E', 0,
  /* 2012 */ 'D', 'B', 'G', '_', 'L', 'A', 'B', 'E', 'L', 0,
  /* 2022 */ '@', 32, 'C', 'O', 'M', 'P', 'I', 'L', 'E', 'R', 32, 'B', 'A', 'R', 'R', 'I', 'E', 'R', 0,
  /* 2041 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'S', 'T', 'A', 'R', 'T', 0,
  /* 2056 */ 'v', 'a', 'b', 'a', 0,
  /* 2061 */ 'l', 'd', 'a', 0,
  /* 2065 */ 'l', 'd', 'm', 'd', 'a', 0,
  /* 2071 */ 's', 't', 'm', 'd', 'a', 0,
  /* 2077 */ 'r', 'f', 'e', 'i', 'a', 0,
  /* 2083 */ 'v', 'l', 'd', 'm', 'i', 'a', 0,
  /* 2090 */ 'v', 's', 't', 'm', 'i', 'a', 0,
  /* 2097 */ 's', 'r', 's', 'i', 'a', 0,
  /* 2103 */ 's', 'm', 'm', 'l', 'a', 0,
  /* 2109 */ 'v', 'n', 'm', 'l', 'a', 0,
  /* 2115 */ 'v', 'm', 'l', 'a', 0,
  /* 2120 */ 'v', 'f', 'm', 'a', 0,
  /* 2125 */ 'v', 'f', 'n', 'm', 'a', 0,
  /* 2131 */ 'v', 'r', 's', 'r', 'a', 0,
  /* 2137 */ 'v', 's', 'r', 'a', 0,
  /* 2142 */ 't', 't', 'a', 0,
  /* 2146 */ 'l', 'd', 'a', 'b', 0,
  /* 2151 */ 's', 'x', 't', 'a', 'b', 0,
  /* 2157 */ 'u', 'x', 't', 'a', 'b', 0,
  /* 2163 */ 's', 'm', 'l', 'a', 'b', 'b', 0,
  /* 2170 */ 's', 'm', 'l', 'a', 'l', 'b', 'b', 0,
  /* 2178 */ 's', 'm', 'u', 'l', 'b', 'b', 0,
  /* 2185 */ 't', 'b', 'b', 0,
  /* 2189 */ 'r', 'f', 'e', 'd', 'b', 0,
  /* 2195 */ 'v', 'l', 'd', 'm', 'd', 'b', 0,
  /* 2202 */ 'v', 's', 't', 'm', 'd', 'b', 0,
  /* 2209 */ 's', 'r', 's', 'd', 'b', 0,
  /* 2215 */ 'l', 'd', 'm', 'i', 'b', 0,
  /* 2221 */ 's', 't', 'm', 'i', 'b', 0,
  /* 2227 */ 's', 't', 'l', 'b', 0,
  /* 2232 */ 'd', 'm', 'b', 0,
  /* 2236 */ 's', 'w', 'p', 'b', 0,
  /* 2241 */ 'l', 'd', 'r', 'b', 0,
  /* 2246 */ 's', 't', 'r', 'b', 0,
  /* 2251 */ 'd', 's', 'b', 0,
  /* 2255 */ 'i', 's', 'b', 0,
  /* 2259 */ 'l', 'd', 'r', 's', 'b', 0,
  /* 2265 */ 't', 's', 'b', 0,
  /* 2269 */ 's', 'm', 'l', 'a', 't', 'b', 0,
  /* 2276 */ 'p', 'k', 'h', 't', 'b', 0,
  /* 2282 */ 's', 'm', 'l', 'a', 'l', 't', 'b', 0,
  /* 2290 */ 's', 'm', 'u', 'l', 't', 'b', 0,
  /* 2297 */ 'v', 'c', 'v', 't', 'b', 0,
  /* 2303 */ 's', 'x', 't', 'b', 0,
  /* 2308 */ 'u', 'x', 't', 'b', 0,
  /* 2313 */ 'q', 'd', 's', 'u', 'b', 0,
  /* 2319 */ 'v', 'h', 's', 'u', 'b', 0,
  /* 2325 */ 'v', 'q', 's', 'u', 'b', 0,
  /* 2331 */ 'v', 's', 'u', 'b', 0,
  /* 2336 */ 's', 'm', 'l', 'a', 'w', 'b', 0,
  /* 2343 */ 's', 'm', 'u', 'l', 'w', 'b', 0,
  /* 2350 */ 'l', 'd', 'a', 'e', 'x', 'b', 0,
  /* 2357 */ 's', 't', 'l', 'e', 'x', 'b', 0,
  /* 2364 */ 'l', 'd', 'r', 'e', 'x', 'b', 0,
  /* 2371 */ 's', 't', 'r', 'e', 'x', 'b', 0,
  /* 2378 */ 's', 'b', 'c', 0,
  /* 2382 */ 'a', 'd', 'c', 0,
  /* 2386 */ 'l', 'd', 'c', 0,
  /* 2390 */ 'b', 'f', 'c', 0,
  /* 2394 */ 'v', 'b', 'i', 'c', 0,
  /* 2399 */ 's', 'm', 'c', 0,
  /* 2403 */ 'm', 'r', 'c', 0,
  /* 2407 */ 'm', 'r', 'r', 'c', 0,
  /* 2412 */ 'r', 's', 'c', 0,
  /* 2416 */ 's', 't', 'c', 0,
  /* 2420 */ 's', 'v', 'c', 0,
  /* 2424 */ 's', 'm', 'l', 'a', 'd', 0,
  /* 2430 */ 's', 'm', 'u', 'a', 'd', 0,
  /* 2436 */ 'v', 'a', 'b', 'd', 0,
  /* 2441 */ 'q', 'd', 'a', 'd', 'd', 0,
  /* 2447 */ 'v', 'r', 'h', 'a', 'd', 'd', 0,
  /* 2454 */ 'v', 'h', 'a', 'd', 'd', 0,
  /* 2460 */ 'v', 'p', 'a', 'd', 'd', 0,
  /* 2466 */ 'v', 'q', 'a', 'd', 'd', 0,
  /* 2472 */ 'v', 'a', 'd', 'd', 0,
  /* 2477 */ 's', 'm', 'l', 'a', 'l', 'd', 0,
  /* 2484 */ 'p', 'l', 'd', 0,
  /* 2488 */ 's', 'm', 'l', 's', 'l', 'd', 0,
  /* 2495 */ 'v', 'a', 'n', 'd', 0,
  /* 2500 */ 'l', 'd', 'r', 'd', 0,
  /* 2505 */ 's', 't', 'r', 'd', 0,
  /* 2510 */ 's', 'm', 'l', 's', 'd', 0,
  /* 2516 */ 's', 'm', 'u', 's', 'd', 0,
  /* 2522 */ 'l', 'd', 'a', 'e', 'x', 'd', 0,
  /* 2529 */ 's', 't', 'l', 'e', 'x', 'd', 0,
  /* 2536 */ 'l', 'd', 'r', 'e', 'x', 'd', 0,
  /* 2543 */ 's', 't', 'r', 'e', 'x', 'd', 0,
  /* 2550 */ 'v', 'a', 'c', 'g', 'e', 0,
  /* 2556 */ 'v', 'c', 'g', 'e', 0,
  /* 2561 */ 'v', 'c', 'l', 'e', 0,
  /* 2566 */ 'v', 'r', 'e', 'c', 'p', 'e', 0,
  /* 2573 */ 'v', 'c', 'm', 'p', 'e', 0,
  /* 2579 */ 'v', 'r', 's', 'q', 'r', 't', 'e', 0,
  /* 2587 */ 'v', 'b', 'i', 'f', 0,
  /* 2592 */ 'd', 'b', 'g', 0,
  /* 2596 */ 'v', 'q', 'n', 'e', 'g', 0,
  /* 2602 */ 'v', 'n', 'e', 'g', 0,
  /* 2607 */ 's', 'g', 0,
  /* 2610 */ 'l', 'd', 'a', 'h', 0,
  /* 2615 */ 'v', 'q', 'r', 'd', 'm', 'l', 'a', 'h', 0,
  /* 2624 */ 's', 'x', 't', 'a', 'h', 0,
  /* 2630 */ 'u', 'x', 't', 'a', 'h', 0,
  /* 2636 */ 't', 'b', 'h', 0,
  /* 2640 */ 's', 't', 'l', 'h', 0,
  /* 2645 */ 'v', 'q', 'd', 'm', 'u', 'l', 'h', 0,
  /* 2653 */ 'v', 'q', 'r', 'd', 'm', 'u', 'l', 'h', 0,
  /* 2662 */ 'l', 'd', 'r', 'h', 0,
  /* 2667 */ 's', 't', 'r', 'h', 0,
  /* 2672 */ 'v', 'q', 'r', 'd', 'm', 'l', 's', 'h', 0,
  /* 2681 */ 'l', 'd', 'r', 's', 'h', 0,
  /* 2687 */ 'p', 'u', 's', 'h', 0,
  /* 2692 */ 'r', 'e', 'v', 's', 'h', 0,
  /* 2698 */ 's', 'x', 't', 'h', 0,
  /* 2703 */ 'u', 'x', 't', 'h', 0,
  /* 2708 */ 'l', 'd', 'a', 'e', 'x', 'h', 0,
  /* 2715 */ 's', 't', 'l', 'e', 'x', 'h', 0,
  /* 2722 */ 'l', 'd', 'r', 'e', 'x', 'h', 0,
  /* 2729 */ 's', 't', 'r', 'e', 'x', 'h', 0,
  /* 2736 */ 'b', 'f', 'i', 0,
  /* 2740 */ 'p', 'l', 'i', 0,
  /* 2744 */ 'v', 's', 'l', 'i', 0,
  /* 2749 */ 'v', 's', 'r', 'i', 0,
  /* 2754 */ 'b', 'x', 'j', 0,
  /* 2758 */ 'l', 'd', 'c', '2', 'l', 0,
  /* 2764 */ 's', 't', 'c', '2', 'l', 0,
  /* 2770 */ 'u', 'm', 'a', 'a', 'l', 0,
  /* 2776 */ 'v', 'a', 'b', 'a', 'l', 0,
  /* 2782 */ 'v', 'p', 'a', 'd', 'a', 'l', 0,
  /* 2789 */ 'v', 'q', 'd', 'm', 'l', 'a', 'l', 0,
  /* 2797 */ 's', 'm', 'l', 'a', 'l', 0,
  /* 2803 */ 'u', 'm', 'l', 'a', 'l', 0,
  /* 2809 */ 'v', 'm', 'l', 'a', 'l', 0,
  /* 2815 */ 'v', 't', 'b', 'l', 0,
  /* 2820 */ 'v', 's', 'u', 'b', 'l', 0,
  /* 2826 */ 'l', 'd', 'c', 'l', 0,
  /* 2831 */ 's', 't', 'c', 'l', 0,
  /* 2836 */ 'v', 'a', 'b', 'd', 'l', 0,
  /* 2842 */ 'v', 'p', 'a', 'd', 'd', 'l', 0,
  /* 2849 */ 'v', 'a', 'd', 'd', 'l', 0,
  /* 2855 */ 's', 'e', 'l', 0,
  /* 2859 */ 'v', 'q', 's', 'h', 'l', 0,
  /* 2865 */ 'v', 'q', 'r', 's', 'h', 'l', 0,
  /* 2872 */ 'v', 'r', 's', 'h', 'l', 0,
  /* 2878 */ 'v', 's', 'h', 'l', 0,
  /* 2883 */ '#', 32, 'F', 'E', 'n', 't', 'r', 'y', 32, 'c', 'a', 'l', 'l', 0,
  /* 2897 */ 'v', 's', 'h', 'l', 'l', 0,
  /* 2903 */ 'v', 'q', 'd', 'm', 'u', 'l', 'l', 0,
  /* 2911 */ 's', 'm', 'u', 'l', 'l', 0,
  /* 2917 */ 'u', 'm', 'u', 'l', 'l', 0,
  /* 2923 */ 'v', 'm', 'u', 'l', 'l', 0,
  /* 2929 */ 'v', 'b', 's', 'l', 0,
  /* 2934 */ 'v', 'q', 'd', 'm', 'l', 's', 'l', 0,
  /* 2942 */ 'v', 'm', 'l', 's', 'l', 0,
  /* 2948 */ 's', 't', 'l', 0,
  /* 2952 */ 's', 'm', 'm', 'u', 'l', 0,
  /* 2958 */ 'v', 'n', 'm', 'u', 'l', 0,
  /* 2964 */ 'v', 'm', 'u', 'l', 0,
  /* 2969 */ 'v', 'm', 'o', 'v', 'l', 0,
  /* 2975 */ 'v', 'l', 'l', 'd', 'm', 0,
  /* 2981 */ 'v', 'l', 's', 't', 'm', 0,
  /* 2987 */ 'v', 'r', 's', 'u', 'b', 'h', 'n', 0,
  /* 2995 */ 'v', 's', 'u', 'b', 'h', 'n', 0,
  /* 3002 */ 'v', 'r', 'a', 'd', 'd', 'h', 'n', 0,
  /* 3010 */ 'v', 'a', 'd', 'd', 'h', 'n', 0,
  /* 3017 */ 'v', 'p', 'm', 'i', 'n', 0,
  /* 3023 */ 'v', 'm', 'i', 'n', 0,
  /* 3028 */ 'c', 'm', 'n', 0,
  /* 3032 */ 'v', 'q', 's', 'h', 'r', 'n', 0,
  /* 3039 */ 'v', 'q', 'r', 's', 'h', 'r', 'n', 0,
  /* 3047 */ 'v', 'r', 's', 'h', 'r', 'n', 0,
  /* 3054 */ 'v', 's', 'h', 'r', 'n', 0,
  /* 3060 */ 'v', 'o', 'r', 'n', 0,
  /* 3065 */ 'v', 't', 'r', 'n', 0,
  /* 3070 */ 'v', 'q', 's', 'h', 'r', 'u', 'n', 0,
  /* 3078 */ 'v', 'q', 'r', 's', 'h', 'r', 'u', 'n', 0,
  /* 3087 */ 'v', 'q', 'm', 'o', 'v', 'u', 'n', 0,
  /* 3095 */ 'v', 'm', 'v', 'n', 0,
  /* 3100 */ 'v', 'q', 'm', 'o', 'v', 'n', 0,
  /* 3107 */ 'v', 'm', 'o', 'v', 'n', 0,
  /* 3113 */ 't', 'r', 'a', 'p', 0,
  /* 3118 */ 'c', 'd', 'p', 0,
  /* 3122 */ 'v', 'z', 'i', 'p', 0,
  /* 3127 */ 'v', 'c', 'm', 'p', 0,
  /* 3132 */ 'p', 'o', 'p', 0,
  /* 3136 */ 'v', 'd', 'u', 'p', 0,
  /* 3141 */ 'v', 's', 'w', 'p', 0,
  /* 3146 */ 'v', 'u', 'z', 'p', 0,
  /* 3151 */ 'v', 'c', 'e', 'q', 0,
  /* 3156 */ 't', 'e', 'q', 0,
  /* 3160 */ 's', 'm', 'm', 'l', 'a', 'r', 0,
  /* 3167 */ 'm', 'c', 'r', 0,
  /* 3171 */ 'a', 'd', 'r', 0,
  /* 3175 */ 'v', 'l', 'd', 'r', 0,
  /* 3180 */ 'v', 'r', 's', 'h', 'r', 0,
  /* 3186 */ 'v', 's', 'h', 'r', 0,
  /* 3191 */ 's', 'm', 'm', 'u', 'l', 'r', 0,
  /* 3198 */ 'v', 'e', 'o', 'r', 0,
  /* 3203 */ 'r', 'o', 'r', 0,
  /* 3207 */ 'm', 'c', 'r', 'r', 0,
  /* 3212 */ 'v', 'o', 'r', 'r', 0,
  /* 3217 */ 'a', 's', 'r', 0,
  /* 3221 */ 's', 'm', 'm', 'l', 's', 'r', 0,
  /* 3228 */ 'v', 'm', 's', 'r', 0,
  /* 3233 */ 'v', 'r', 'i', 'n', 't', 'r', 0,
  /* 3240 */ 'v', 's', 't', 'r', 0,
  /* 3245 */ 'v', 'c', 'v', 't', 'r', 0,
  /* 3251 */ 'v', 'q', 'a', 'b', 's', 0,
  /* 3257 */ 'v', 'a', 'b', 's', 0,
  /* 3262 */ 's', 'u', 'b', 's', 0,
  /* 3267 */ 'v', 'c', 'l', 's', 0,
  /* 3272 */ 's', 'm', 'm', 'l', 's', 0,
  /* 3278 */ 'v', 'n', 'm', 'l', 's', 0,
  /* 3284 */ 'v', 'm', 'l', 's', 0,
  /* 3289 */ 'v', 'f', 'm', 's', 0,
  /* 3294 */ 'v', 'f', 'n', 'm', 's', 0,
  /* 3300 */ 'b', 'x', 'n', 's', 0,
  /* 3305 */ 'b', 'l', 'x', 'n', 's', 0,
  /* 3311 */ 'v', 'r', 'e', 'c', 'p', 's', 0,
  /* 3318 */ 'v', 'm', 'r', 's', 0,
  /* 3323 */ 'a', 's', 'r', 's', 0,
  /* 3328 */ 'l', 's', 'r', 's', 0,
  /* 3333 */ 'v', 'r', 's', 'q', 'r', 't', 's', 0,
  /* 3341 */ 'm', 'o', 'v', 's', 0,
  /* 3346 */ 's', 's', 'a', 't', 0,
  /* 3351 */ 'u', 's', 'a', 't', 0,
  /* 3356 */ 't', 't', 'a', 't', 0,
  /* 3361 */ 's', 'm', 'l', 'a', 'b', 't', 0,
  /* 3368 */ 'p', 'k', 'h', 'b', 't', 0,
  /* 3374 */ 's', 'm', 'l', 'a', 'l', 'b', 't', 0,
  /* 3382 */ 's', 'm', 'u', 'l', 'b', 't', 0,
  /* 3389 */ 'l', 'd', 'r', 'b', 't', 0,
  /* 3395 */ 's', 't', 'r', 'b', 't', 0,
  /* 3401 */ 'l', 'd', 'r', 's', 'b', 't', 0,
  /* 3408 */ 'e', 'r', 'e', 't', 0,
  /* 3413 */ 'v', 'a', 'c', 'g', 't', 0,
  /* 3419 */ 'v', 'c', 'g', 't', 0,
  /* 3424 */ 'l', 'd', 'r', 'h', 't', 0,
  /* 3430 */ 's', 't', 'r', 'h', 't', 0,
  /* 3436 */ 'l', 'd', 'r', 's', 'h', 't', 0,
  /* 3443 */ 'r', 'b', 'i', 't', 0,
  /* 3448 */ 'v', 'b', 'i', 't', 0,
  /* 3453 */ 'v', 'c', 'l', 't', 0,
  /* 3458 */ 'v', 'c', 'n', 't', 0,
  /* 3463 */ 'h', 'i', 'n', 't', 0,
  /* 3468 */ 'l', 'd', 'r', 't', 0,
  /* 3473 */ 'v', 's', 'q', 'r', 't', 0,
  /* 3479 */ 's', 't', 'r', 't', 0,
  /* 3484 */ 'v', 't', 's', 't', 0,
  /* 3489 */ 's', 'm', 'l', 'a', 't', 't', 0,
  /* 3496 */ 's', 'm', 'l', 'a', 'l', 't', 't', 0,
  /* 3504 */ 's', 'm', 'u', 'l', 't', 't', 0,
  /* 3511 */ 't', 't', 't', 0,
  /* 3515 */ 'v', 'c', 'v', 't', 't', 0,
  /* 3521 */ 'v', 'j', 'c', 'v', 't', 0,
  /* 3527 */ 'v', 'c', 'v', 't', 0,
  /* 3532 */ 'm', 'o', 'v', 't', 0,
  /* 3537 */ 's', 'm', 'l', 'a', 'w', 't', 0,
  /* 3544 */ 's', 'm', 'u', 'l', 'w', 't', 0,
  /* 3551 */ 'v', 'e', 'x', 't', 0,
  /* 3556 */ 'v', 'q', 's', 'h', 'l', 'u', 0,
  /* 3563 */ 'r', 'e', 'v', 0,
  /* 3567 */ 's', 'd', 'i', 'v', 0,
  /* 3572 */ 'u', 'd', 'i', 'v', 0,
  /* 3577 */ 'v', 'd', 'i', 'v', 0,
  /* 3582 */ 'v', 'm', 'o', 'v', 0,
  /* 3587 */ 'v', 's', 'u', 'b', 'w', 0,
  /* 3593 */ 'v', 'a', 'd', 'd', 'w', 0,
  /* 3599 */ 'p', 'l', 'd', 'w', 0,
  /* 3604 */ 'm', 'o', 'v', 'w', 0,
  /* 3609 */ 'f', 'l', 'd', 'm', 'i', 'a', 'x', 0,
  /* 3617 */ 'f', 's', 't', 'm', 'i', 'a', 'x', 0,
  /* 3625 */ 'v', 'p', 'm', 'a', 'x', 0,
  /* 3631 */ 'v', 'm', 'a', 'x', 0,
  /* 3636 */ 's', 'h', 's', 'a', 'x', 0,
  /* 3642 */ 'u', 'h', 's', 'a', 'x', 0,
  /* 3648 */ 'u', 'q', 's', 'a', 'x', 0,
  /* 3654 */ 's', 's', 'a', 'x', 0,
  /* 3659 */ 'u', 's', 'a', 'x', 0,
  /* 3664 */ 'f', 'l', 'd', 'm', 'd', 'b', 'x', 0,
  /* 3672 */ 'f', 's', 't', 'm', 'd', 'b', 'x', 0,
  /* 3680 */ 'v', 't', 'b', 'x', 0,
  /* 3685 */ 's', 'm', 'l', 'a', 'd', 'x', 0,
  /* 3692 */ 's', 'm', 'u', 'a', 'd', 'x', 0,
  /* 3699 */ 's', 'm', 'l', 'a', 'l', 'd', 'x', 0,
  /* 3707 */ 's', 'm', 'l', 's', 'l', 'd', 'x', 0,
  /* 3715 */ 's', 'm', 'l', 's', 'd', 'x', 0,
  /* 3722 */ 's', 'm', 'u', 's', 'd', 'x', 0,
  /* 3729 */ 'l', 'd', 'a', 'e', 'x', 0,
  /* 3735 */ 's', 't', 'l', 'e', 'x', 0,
  /* 3741 */ 'l', 'd', 'r', 'e', 'x', 0,
  /* 3747 */ 'c', 'l', 'r', 'e', 'x', 0,
  /* 3753 */ 's', 't', 'r', 'e', 'x', 0,
  /* 3759 */ 's', 'b', 'f', 'x', 0,
  /* 3764 */ 'u', 'b', 'f', 'x', 0,
  /* 3769 */ 'b', 'l', 'x', 0,
  /* 3773 */ 'r', 'r', 'x', 0,
  /* 3777 */ 's', 'h', 'a', 's', 'x', 0,
  /* 3783 */ 'u', 'h', 'a', 's', 'x', 0,
  /* 3789 */ 'u', 'q', 'a', 's', 'x', 0,
  /* 3795 */ 's', 'a', 's', 'x', 0,
  /* 3800 */ 'u', 'a', 's', 'x', 0,
  /* 3805 */ 'v', 'r', 'i', 'n', 't', 'x', 0,
  /* 3812 */ 'v', 'c', 'l', 'z', 0,
  /* 3817 */ 'v', 'r', 'i', 'n', 't', 'z', 0,
  };

  static const uint32_t OpInfo0[] = {
    0U,	// PHI
    0U,	// INLINEASM
    0U,	// INLINEASM_BR
    0U,	// CFI_INSTRUCTION
    0U,	// EH_LABEL
    0U,	// GC_LABEL
    0U,	// ANNOTATION_LABEL
    0U,	// KILL
    0U,	// EXTRACT_SUBREG
    0U,	// INSERT_SUBREG
    0U,	// IMPLICIT_DEF
    0U,	// SUBREG_TO_REG
    0U,	// COPY_TO_REGCLASS
    2003U,	// DBG_VALUE
    2013U,	// DBG_LABEL
    0U,	// REG_SEQUENCE
    0U,	// COPY
    1996U,	// BUNDLE
    2042U,	// LIFETIME_START
    1983U,	// LIFETIME_END
    0U,	// STACKMAP
    2884U,	// FENTRY_CALL
    0U,	// PATCHPOINT
    0U,	// LOAD_STACK_GUARD
    0U,	// STATEPOINT
    0U,	// LOCAL_ESCAPE
    0U,	// FAULTING_OP
    0U,	// PATCHABLE_OP
    1590U,	// PATCHABLE_FUNCTION_ENTER
    1510U,	// PATCHABLE_RET
    1636U,	// PATCHABLE_FUNCTION_EXIT
    1613U,	// PATCHABLE_TAIL_CALL
    1565U,	// PATCHABLE_EVENT_CALL
    1541U,	// PATCHABLE_TYPED_EVENT_CALL
    0U,	// ICALL_BRANCH_FUNNEL
    0U,	// G_ADD
    0U,	// G_SUB
    0U,	// G_MUL
    0U,	// G_SDIV
    0U,	// G_UDIV
    0U,	// G_SREM
    0U,	// G_UREM
    0U,	// G_AND
    0U,	// G_OR
    0U,	// G_XOR
    0U,	// G_IMPLICIT_DEF
    0U,	// G_PHI
    0U,	// G_FRAME_INDEX
    0U,	// G_GLOBAL_VALUE
    0U,	// G_EXTRACT
    0U,	// G_UNMERGE_VALUES
    0U,	// G_INSERT
    0U,	// G_MERGE_VALUES
    0U,	// G_BUILD_VECTOR
    0U,	// G_BUILD_VECTOR_TRUNC
    0U,	// G_CONCAT_VECTORS
    0U,	// G_PTRTOINT
    0U,	// G_INTTOPTR
    0U,	// G_BITCAST
    0U,	// G_INTRINSIC_TRUNC
    0U,	// G_INTRINSIC_ROUND
    0U,	// G_LOAD
    0U,	// G_SEXTLOAD
    0U,	// G_ZEXTLOAD
    0U,	// G_STORE
    0U,	// G_ATOMIC_CMPXCHG_WITH_SUCCESS
    0U,	// G_ATOMIC_CMPXCHG
    0U,	// G_ATOMICRMW_XCHG
    0U,	// G_ATOMICRMW_ADD
    0U,	// G_ATOMICRMW_SUB
    0U,	// G_ATOMICRMW_AND
    0U,	// G_ATOMICRMW_NAND
    0U,	// G_ATOMICRMW_OR
    0U,	// G_ATOMICRMW_XOR
    0U,	// G_ATOMICRMW_MAX
    0U,	// G_ATOMICRMW_MIN
    0U,	// G_ATOMICRMW_UMAX
    0U,	// G_ATOMICRMW_UMIN
    0U,	// G_BRCOND
    0U,	// G_BRINDIRECT
    0U,	// G_INTRINSIC
    0U,	// G_INTRINSIC_W_SIDE_EFFECTS
    0U,	// G_ANYEXT
    0U,	// G_TRUNC
    0U,	// G_CONSTANT
    0U,	// G_FCONSTANT
    0U,	// G_VASTART
    0U,	// G_VAARG
    0U,	// G_SEXT
    0U,	// G_ZEXT
    0U,	// G_SHL
    0U,	// G_LSHR
    0U,	// G_ASHR
    0U,	// G_ICMP
    0U,	// G_FCMP
    0U,	// G_SELECT
    0U,	// G_UADDO
    0U,	// G_UADDE
    0U,	// G_USUBO
    0U,	// G_USUBE
    0U,	// G_SADDO
    0U,	// G_SADDE
    0U,	// G_SSUBO
    0U,	// G_SSUBE
    0U,	// G_UMULO
    0U,	// G_SMULO
    0U,	// G_UMULH
    0U,	// G_SMULH
    0U,	// G_FADD
    0U,	// G_FSUB
    0U,	// G_FMUL
    0U,	// G_FMA
    0U,	// G_FDIV
    0U,	// G_FREM
    0U,	// G_FPOW
    0U,	// G_FEXP
    0U,	// G_FEXP2
    0U,	// G_FLOG
    0U,	// G_FLOG2
    0U,	// G_FLOG10
    0U,	// G_FNEG
    0U,	// G_FPEXT
    0U,	// G_FPTRUNC
    0U,	// G_FPTOSI
    0U,	// G_FPTOUI
    0U,	// G_SITOFP
    0U,	// G_UITOFP
    0U,	// G_FABS
    0U,	// G_FCANONICALIZE
    0U,	// G_GEP
    0U,	// G_PTR_MASK
    0U,	// G_BR
    0U,	// G_INSERT_VECTOR_ELT
    0U,	// G_EXTRACT_VECTOR_ELT
    0U,	// G_SHUFFLE_VECTOR
    0U,	// G_CTTZ
    0U,	// G_CTTZ_ZERO_UNDEF
    0U,	// G_CTLZ
    0U,	// G_CTLZ_ZERO_UNDEF
    0U,	// G_CTPOP
    0U,	// G_BSWAP
    0U,	// G_FCEIL
    0U,	// G_FCOS
    0U,	// G_FSIN
    0U,	// G_FSQRT
    0U,	// G_FFLOOR
    0U,	// G_ADDRSPACE_CAST
    0U,	// G_BLOCK_ADDR
    0U,	// ABS
    0U,	// ADDSri
    0U,	// ADDSrr
    0U,	// ADDSrsi
    0U,	// ADDSrsr
    0U,	// ADJCALLSTACKDOWN
    0U,	// ADJCALLSTACKUP
    7314U,	// ASRi
    7314U,	// ASRr
    0U,	// B
    0U,	// BCCZi64
    0U,	// BCCi64
    0U,	// BMOVPCB_CALL
    0U,	// BMOVPCRX_CALL
    0U,	// BR_JTadd
    0U,	// BR_JTm_i12
    0U,	// BR_JTm_rs
    0U,	// BR_JTr
    0U,	// BX_CALL
    0U,	// CMP_SWAP_16
    0U,	// CMP_SWAP_32
    0U,	// CMP_SWAP_64
    0U,	// CMP_SWAP_8
    0U,	// CONSTPOOL_ENTRY
    0U,	// COPY_STRUCT_BYVAL_I32
    2023U,	// CompilerBarrier
    16788854U,	// ITasm
    0U,	// Int_eh_sjlj_dispatchsetup
    0U,	// Int_eh_sjlj_longjmp
    0U,	// Int_eh_sjlj_setjmp
    0U,	// Int_eh_sjlj_setjmp_nofp
    0U,	// Int_eh_sjlj_setup_dispatch
    0U,	// JUMPTABLE_ADDRS
    0U,	// JUMPTABLE_INSTS
    0U,	// JUMPTABLE_TBB
    0U,	// JUMPTABLE_TBH
    0U,	// LDMIA_RET
    15678U,	// LDRBT_POST
    15465U,	// LDRConstPool
    0U,	// LDRLIT_ga_abs
    0U,	// LDRLIT_ga_pcrel
    0U,	// LDRLIT_ga_pcrel_ldr
    15757U,	// LDRT_POST
    0U,	// LEApcrel
    0U,	// LEApcrelJT
    7035U,	// LSLi
    7035U,	// LSLr
    7321U,	// LSRi
    7321U,	// LSRr
    0U,	// MEMCPY
    0U,	// MLAv5
    0U,	// MOVCCi
    0U,	// MOVCCi16
    0U,	// MOVCCi32imm
    0U,	// MOVCCr
    0U,	// MOVCCsi
    0U,	// MOVCCsr
    0U,	// MOVPCRX
    0U,	// MOVTi16_ga_pcrel
    0U,	// MOV_ga_pcrel
    0U,	// MOV_ga_pcrel_ldr
    0U,	// MOVi16_ga_pcrel
    0U,	// MOVi32imm
    0U,	// MOVsra_flag
    0U,	// MOVsrl_flag
    0U,	// MULv5
    0U,	// MVNCCi
    0U,	// PICADD
    0U,	// PICLDR
    0U,	// PICLDRB
    0U,	// PICLDRH
    0U,	// PICLDRSB
    0U,	// PICLDRSH
    0U,	// PICSTR
    0U,	// PICSTRB
    0U,	// PICSTRH
    7300U,	// RORi
    7300U,	// RORr
    0U,	// RRX
    20158U,	// RRXi
    0U,	// RSBSri
    0U,	// RSBSrsi
    0U,	// RSBSrsr
    0U,	// SMLALv5
    0U,	// SMULLv5
    0U,	// SPACE
    15684U,	// STRBT_POST
    0U,	// STRBi_preidx
    0U,	// STRBr_preidx
    0U,	// STRH_preidx
    15768U,	// STRT_POST
    0U,	// STRi_preidx
    0U,	// STRr_preidx
    0U,	// SUBS_PC_LR
    0U,	// SUBSri
    0U,	// SUBSrr
    0U,	// SUBSrsi
    0U,	// SUBSrsr
    0U,	// TAILJMPd
    0U,	// TAILJMPr
    0U,	// TAILJMPr4
    0U,	// TCRETURNdi
    0U,	// TCRETURNri
    0U,	// TPsoft
    0U,	// UMLALv5
    0U,	// UMULLv5
    153220U,	// VLD1LNdAsm_16
    284292U,	// VLD1LNdAsm_32
    415364U,	// VLD1LNdAsm_8
    153220U,	// VLD1LNdWB_fixed_Asm_16
    284292U,	// VLD1LNdWB_fixed_Asm_32
    415364U,	// VLD1LNdWB_fixed_Asm_8
    157316U,	// VLD1LNdWB_register_Asm_16
    288388U,	// VLD1LNdWB_register_Asm_32
    419460U,	// VLD1LNdWB_register_Asm_8
    153264U,	// VLD2LNdAsm_16
    284336U,	// VLD2LNdAsm_32
    415408U,	// VLD2LNdAsm_8
    153264U,	// VLD2LNdWB_fixed_Asm_16
    284336U,	// VLD2LNdWB_fixed_Asm_32
    415408U,	// VLD2LNdWB_fixed_Asm_8
    157360U,	// VLD2LNdWB_register_Asm_16
    288432U,	// VLD2LNdWB_register_Asm_32
    419504U,	// VLD2LNdWB_register_Asm_8
    153264U,	// VLD2LNqAsm_16
    284336U,	// VLD2LNqAsm_32
    153264U,	// VLD2LNqWB_fixed_Asm_16
    284336U,	// VLD2LNqWB_fixed_Asm_32
    157360U,	// VLD2LNqWB_register_Asm_16
    288432U,	// VLD2LNqWB_register_Asm_32
    1107457744U,	// VLD3DUPdAsm_16
    1107588816U,	// VLD3DUPdAsm_32
    1107719888U,	// VLD3DUPdAsm_8
    2181199568U,	// VLD3DUPdWB_fixed_Asm_16
    2181330640U,	// VLD3DUPdWB_fixed_Asm_32
    2181461712U,	// VLD3DUPdWB_fixed_Asm_8
    33707728U,	// VLD3DUPdWB_register_Asm_16
    33838800U,	// VLD3DUPdWB_register_Asm_32
    33969872U,	// VLD3DUPdWB_register_Asm_8
    1124234960U,	// VLD3DUPqAsm_16
    1124366032U,	// VLD3DUPqAsm_32
    1124497104U,	// VLD3DUPqAsm_8
    2197976784U,	// VLD3DUPqWB_fixed_Asm_16
    2198107856U,	// VLD3DUPqWB_fixed_Asm_32
    2198238928U,	// VLD3DUPqWB_fixed_Asm_8
    50484944U,	// VLD3DUPqWB_register_Asm_16
    50616016U,	// VLD3DUPqWB_register_Asm_32
    50747088U,	// VLD3DUPqWB_register_Asm_8
    153296U,	// VLD3LNdAsm_16
    284368U,	// VLD3LNdAsm_32
    415440U,	// VLD3LNdAsm_8
    153296U,	// VLD3LNdWB_fixed_Asm_16
    284368U,	// VLD3LNdWB_fixed_Asm_32
    415440U,	// VLD3LNdWB_fixed_Asm_8
    157392U,	// VLD3LNdWB_register_Asm_16
    288464U,	// VLD3LNdWB_register_Asm_32
    419536U,	// VLD3LNdWB_register_Asm_8
    153296U,	// VLD3LNqAsm_16
    284368U,	// VLD3LNqAsm_32
    153296U,	// VLD3LNqWB_fixed_Asm_16
    284368U,	// VLD3LNqWB_fixed_Asm_32
    157392U,	// VLD3LNqWB_register_Asm_16
    288464U,	// VLD3LNqWB_register_Asm_32
    3288495824U,	// VLD3dAsm_16
    3288626896U,	// VLD3dAsm_32
    3288757968U,	// VLD3dAsm_8
    3288495824U,	// VLD3dWB_fixed_Asm_16
    3288626896U,	// VLD3dWB_fixed_Asm_32
    3288757968U,	// VLD3dWB_fixed_Asm_8
    3288487632U,	// VLD3dWB_register_Asm_16
    3288618704U,	// VLD3dWB_register_Asm_32
    3288749776U,	// VLD3dWB_register_Asm_8
    1157789392U,	// VLD3qAsm_16
    1157920464U,	// VLD3qAsm_32
    1158051536U,	// VLD3qAsm_8
    2231531216U,	// VLD3qWB_fixed_Asm_16
    2231662288U,	// VLD3qWB_fixed_Asm_32
    2231793360U,	// VLD3qWB_fixed_Asm_8
    84039376U,	// VLD3qWB_register_Asm_16
    84170448U,	// VLD3qWB_register_Asm_32
    84301520U,	// VLD3qWB_register_Asm_8
    1174566631U,	// VLD4DUPdAsm_16
    1174697703U,	// VLD4DUPdAsm_32
    1174828775U,	// VLD4DUPdAsm_8
    2248308455U,	// VLD4DUPdWB_fixed_Asm_16
    2248439527U,	// VLD4DUPdWB_fixed_Asm_32
    2248570599U,	// VLD4DUPdWB_fixed_Asm_8
    100816615U,	// VLD4DUPdWB_register_Asm_16
    100947687U,	// VLD4DUPdWB_register_Asm_32
    101078759U,	// VLD4DUPdWB_register_Asm_8
    1191343847U,	// VLD4DUPqAsm_16
    1191474919U,	// VLD4DUPqAsm_32
    1191605991U,	// VLD4DUPqAsm_8
    2265085671U,	// VLD4DUPqWB_fixed_Asm_16
    2265216743U,	// VLD4DUPqWB_fixed_Asm_32
    2265347815U,	// VLD4DUPqWB_fixed_Asm_8
    117593831U,	// VLD4DUPqWB_register_Asm_16
    117724903U,	// VLD4DUPqWB_register_Asm_32
    117855975U,	// VLD4DUPqWB_register_Asm_8
    153319U,	// VLD4LNdAsm_16
    284391U,	// VLD4LNdAsm_32
    415463U,	// VLD4LNdAsm_8
    153319U,	// VLD4LNdWB_fixed_Asm_16
    284391U,	// VLD4LNdWB_fixed_Asm_32
    415463U,	// VLD4LNdWB_fixed_Asm_8
    157415U,	// VLD4LNdWB_register_Asm_16
    288487U,	// VLD4LNdWB_register_Asm_32
    419559U,	// VLD4LNdWB_register_Asm_8
    153319U,	// VLD4LNqAsm_16
    284391U,	// VLD4LNqAsm_32
    153319U,	// VLD4LNqWB_fixed_Asm_16
    284391U,	// VLD4LNqWB_fixed_Asm_32
    157415U,	// VLD4LNqWB_register_Asm_16
    288487U,	// VLD4LNqWB_register_Asm_32
    3355604711U,	// VLD4dAsm_16
    3355735783U,	// VLD4dAsm_32
    3355866855U,	// VLD4dAsm_8
    3355604711U,	// VLD4dWB_fixed_Asm_16
    3355735783U,	// VLD4dWB_fixed_Asm_32
    3355866855U,	// VLD4dWB_fixed_Asm_8
    3355596519U,	// VLD4dWB_register_Asm_16
    3355727591U,	// VLD4dWB_register_Asm_32
    3355858663U,	// VLD4dWB_register_Asm_8
    1224898279U,	// VLD4qAsm_16
    1225029351U,	// VLD4qAsm_32
    1225160423U,	// VLD4qAsm_8
    2298640103U,	// VLD4qWB_fixed_Asm_16
    2298771175U,	// VLD4qWB_fixed_Asm_32
    2298902247U,	// VLD4qWB_fixed_Asm_8
    151148263U,	// VLD4qWB_register_Asm_16
    151279335U,	// VLD4qWB_register_Asm_32
    151410407U,	// VLD4qWB_register_Asm_8
    0U,	// VMOVD0
    0U,	// VMOVDcc
    0U,	// VMOVQ0
    0U,	// VMOVScc
    153231U,	// VST1LNdAsm_16
    284303U,	// VST1LNdAsm_32
    415375U,	// VST1LNdAsm_8
    153231U,	// VST1LNdWB_fixed_Asm_16
    284303U,	// VST1LNdWB_fixed_Asm_32
    415375U,	// VST1LNdWB_fixed_Asm_8
    157327U,	// VST1LNdWB_register_Asm_16
    288399U,	// VST1LNdWB_register_Asm_32
    419471U,	// VST1LNdWB_register_Asm_8
    153291U,	// VST2LNdAsm_16
    284363U,	// VST2LNdAsm_32
    415435U,	// VST2LNdAsm_8
    153291U,	// VST2LNdWB_fixed_Asm_16
    284363U,	// VST2LNdWB_fixed_Asm_32
    415435U,	// VST2LNdWB_fixed_Asm_8
    157387U,	// VST2LNdWB_register_Asm_16
    288459U,	// VST2LNdWB_register_Asm_32
    419531U,	// VST2LNdWB_register_Asm_8
    153291U,	// VST2LNqAsm_16
    284363U,	// VST2LNqAsm_32
    153291U,	// VST2LNqWB_fixed_Asm_16
    284363U,	// VST2LNqWB_fixed_Asm_32
    157387U,	// VST2LNqWB_register_Asm_16
    288459U,	// VST2LNqWB_register_Asm_32
    153307U,	// VST3LNdAsm_16
    284379U,	// VST3LNdAsm_32
    415451U,	// VST3LNdAsm_8
    153307U,	// VST3LNdWB_fixed_Asm_16
    284379U,	// VST3LNdWB_fixed_Asm_32
    415451U,	// VST3LNdWB_fixed_Asm_8
    157403U,	// VST3LNdWB_register_Asm_16
    288475U,	// VST3LNdWB_register_Asm_32
    419547U,	// VST3LNdWB_register_Asm_8
    153307U,	// VST3LNqAsm_16
    284379U,	// VST3LNqAsm_32
    153307U,	// VST3LNqWB_fixed_Asm_16
    284379U,	// VST3LNqWB_fixed_Asm_32
    157403U,	// VST3LNqWB_register_Asm_16
    288475U,	// VST3LNqWB_register_Asm_32
    3288495835U,	// VST3dAsm_16
    3288626907U,	// VST3dAsm_32
    3288757979U,	// VST3dAsm_8
    3288495835U,	// VST3dWB_fixed_Asm_16
    3288626907U,	// VST3dWB_fixed_Asm_32
    3288757979U,	// VST3dWB_fixed_Asm_8
    3288487643U,	// VST3dWB_register_Asm_16
    3288618715U,	// VST3dWB_register_Asm_32
    3288749787U,	// VST3dWB_register_Asm_8
    1157789403U,	// VST3qAsm_16
    1157920475U,	// VST3qAsm_32
    1158051547U,	// VST3qAsm_8
    2231531227U,	// VST3qWB_fixed_Asm_16
    2231662299U,	// VST3qWB_fixed_Asm_32
    2231793371U,	// VST3qWB_fixed_Asm_8
    84039387U,	// VST3qWB_register_Asm_16
    84170459U,	// VST3qWB_register_Asm_32
    84301531U,	// VST3qWB_register_Asm_8
    153324U,	// VST4LNdAsm_16
    284396U,	// VST4LNdAsm_32
    415468U,	// VST4LNdAsm_8
    153324U,	// VST4LNdWB_fixed_Asm_16
    284396U,	// VST4LNdWB_fixed_Asm_32
    415468U,	// VST4LNdWB_fixed_Asm_8
    157420U,	// VST4LNdWB_register_Asm_16
    288492U,	// VST4LNdWB_register_Asm_32
    419564U,	// VST4LNdWB_register_Asm_8
    153324U,	// VST4LNqAsm_16
    284396U,	// VST4LNqAsm_32
    153324U,	// VST4LNqWB_fixed_Asm_16
    284396U,	// VST4LNqWB_fixed_Asm_32
    157420U,	// VST4LNqWB_register_Asm_16
    288492U,	// VST4LNqWB_register_Asm_32
    3355604716U,	// VST4dAsm_16
    3355735788U,	// VST4dAsm_32
    3355866860U,	// VST4dAsm_8
    3355604716U,	// VST4dWB_fixed_Asm_16
    3355735788U,	// VST4dWB_fixed_Asm_32
    3355866860U,	// VST4dWB_fixed_Asm_8
    3355596524U,	// VST4dWB_register_Asm_16
    3355727596U,	// VST4dWB_register_Asm_32
    3355858668U,	// VST4dWB_register_Asm_8
    1224898284U,	// VST4qAsm_16
    1225029356U,	// VST4qAsm_32
    1225160428U,	// VST4qAsm_8
    2298640108U,	// VST4qWB_fixed_Asm_16
    2298771180U,	// VST4qWB_fixed_Asm_32
    2298902252U,	// VST4qWB_fixed_Asm_8
    151148268U,	// VST4qWB_register_Asm_16
    151279340U,	// VST4qWB_register_Asm_32
    151410412U,	// VST4qWB_register_Asm_8
    0U,	// WIN__CHKSTK
    0U,	// WIN__DBZCHK
    0U,	// t2ABS
    0U,	// t2ADDSri
    0U,	// t2ADDSrr
    0U,	// t2ADDSrs
    0U,	// t2BR_JT
    0U,	// t2LDMIA_RET
    14530U,	// t2LDRBpcrel
    15465U,	// t2LDRConstPool
    14951U,	// t2LDRHpcrel
    14548U,	// t2LDRSBpcrel
    14970U,	// t2LDRSHpcrel
    0U,	// t2LDRpci_pic
    15465U,	// t2LDRpcrel
    0U,	// t2LEApcrel
    0U,	// t2LEApcrelJT
    0U,	// t2MOVCCasr
    0U,	// t2MOVCCi
    0U,	// t2MOVCCi16
    0U,	// t2MOVCCi32imm
    0U,	// t2MOVCClsl
    0U,	// t2MOVCClsr
    0U,	// t2MOVCCr
    0U,	// t2MOVCCror
    32014U,	// t2MOVSsi
    23822U,	// t2MOVSsr
    0U,	// t2MOVTi16_ga_pcrel
    0U,	// t2MOV_ga_pcrel
    0U,	// t2MOVi16_ga_pcrel
    0U,	// t2MOVi32imm
    32256U,	// t2MOVsi
    24064U,	// t2MOVsr
    0U,	// t2MVNCCi
    0U,	// t2RSBSri
    0U,	// t2RSBSrs
    0U,	// t2STRB_preidx
    0U,	// t2STRH_preidx
    0U,	// t2STR_preidx
    0U,	// t2SUBSri
    0U,	// t2SUBSrr
    0U,	// t2SUBSrs
    0U,	// t2TBB_JT
    0U,	// t2TBH_JT
    0U,	// tADCS
    0U,	// tADDSi3
    0U,	// tADDSi8
    0U,	// tADDSrr
    0U,	// tADDframe
    0U,	// tADJCALLSTACKDOWN
    0U,	// tADJCALLSTACKUP
    0U,	// tBRIND
    0U,	// tBR_JTr
    0U,	// tBX_CALL
    0U,	// tBX_RET
    0U,	// tBX_RET_vararg
    0U,	// tBfar
    0U,	// tLDMIA_UPD
    15465U,	// tLDRConstPool
    0U,	// tLDRLIT_ga_abs
    0U,	// tLDRLIT_ga_pcrel
    0U,	// tLDR_postidx
    0U,	// tLDRpci_pic
    0U,	// tLEApcrel
    0U,	// tLEApcrelJT
    0U,	// tMOVCCr_pseudo
    0U,	// tPOP_RET
    0U,	// tRSBS
    0U,	// tSBCS
    0U,	// tSUBSi3
    0U,	// tSUBSi8
    0U,	// tSUBSrr
    0U,	// tTAILJMPd
    0U,	// tTAILJMPdND
    0U,	// tTAILJMPr
    0U,	// tTBB_JT
    0U,	// tTBH_JT
    0U,	// tTPsoft
    530767U,	// ADCri
    530767U,	// ADCrr
    559439U,	// ADCrsi
    39247U,	// ADCrsr
    530828U,	// ADDri
    530828U,	// ADDrr
    559500U,	// ADDrsi
    39308U,	// ADDrsr
    539748U,	// ADR
    1242211471U,	// AESD
    1242211479U,	// AESE
    1258988668U,	// AESIMC
    1258988678U,	// AESMC
    530881U,	// ANDri
    530881U,	// ANDrr
    559553U,	// ANDrsi
    39361U,	// ANDrsr
    555351U,	// BFC
    547505U,	// BFI
    530780U,	// BICri
    530780U,	// BICrr
    559452U,	// BICrsi
    39260U,	// BICrsr
    828747U,	// BKPT
    828719U,	// BL
    828794U,	// BLX
    1074314938U,	// BLX_pred
    828794U,	// BLXi
    1074313986U,	// BL_pred
    828790U,	// BX
    1074313923U,	// BXJ
    970326U,	// BX_RET
    1074314838U,	// BX_pred
    1074313318U,	// Bcc
    201907247U,	// CDP
    219210157U,	// CDP2
    3748U,	// CLREX
    540390U,	// CLZ
    539605U,	// CMNri
    539605U,	// CMNzrr
    555989U,	// CMNzrsi
    547797U,	// CMNzrsr
    539705U,	// CMPri
    539705U,	// CMPrr
    556089U,	// CMPrsi
    547897U,	// CMPrsr
    828731U,	// CPS1p
    1309211891U,	// CPS2p
    235470067U,	// CPS3p
    185246913U,	// CRC32B
    185246921U,	// CRC32CB
    185246995U,	// CRC32CH
    185247079U,	// CRC32CW
    185246987U,	// CRC32H
    185247071U,	// CRC32W
    1074313761U,	// DBG
    66784U,	// DMB
    66789U,	// DSB
    531584U,	// EORri
    531584U,	// EORrr
    560256U,	// EORrsi
    40064U,	// EORrsr
    838993U,	// ERET
    1326595583U,	// FCONSTD
    1326726655U,	// FCONSTH
    1326857727U,	// FCONSTS
    2332573265U,	// FLDMXDB_UPD
    572954U,	// FLDMXIA
    2332573210U,	// FLDMXIA_UPD
    1625335U,	// FMSTAT
    2332573273U,	// FSTMXDB_UPD
    572962U,	// FSTMXIA
    2332573218U,	// FSTMXIA_UPD
    1074314632U,	// HINT
    828742U,	// HLT
    828660U,	// HVC
    70890U,	// ISB
    538638U,	// LDA
    538723U,	// LDAB
    540306U,	// LDAEX
    538927U,	// LDAEXB
    268974555U,	// LDAEXD
    539285U,	// LDAEXH
    539187U,	// LDAH
    286975265U,	// LDC2L_OFFSET
    3524977953U,	// LDC2L_OPTION
    303752481U,	// LDC2L_POST
    320529697U,	// LDC2L_PRE
    286974356U,	// LDC2_OFFSET
    3524977044U,	// LDC2_OPTION
    303751572U,	// LDC2_POST
    320528788U,	// LDC2_PRE
    1275616011U,	// LDCL_OFFSET
    1275616011U,	// LDCL_OPTION
    1275616011U,	// LDCL_POST
    1275616011U,	// LDCL_PRE
    1275615571U,	// LDC_OFFSET
    1275615571U,	// LDC_OPTION
    1275615571U,	// LDC_POST
    1275615571U,	// LDC_PRE
    571410U,	// LDMDA
    2332571666U,	// LDMDA_UPD
    571541U,	// LDMDB
    2332571797U,	// LDMDB_UPD
    572322U,	// LDMIA
    2332572578U,	// LDMIA_UPD
    571560U,	// LDMIB
    2332571816U,	// LDMIB_UPD
    552254U,	// LDRBT_POST_IMM
    552254U,	// LDRBT_POST_REG
    551106U,	// LDRB_POST_IMM
    551106U,	// LDRB_POST_REG
    547010U,	// LDRB_PRE_IMM
    551106U,	// LDRB_PRE_REG
    555202U,	// LDRBi12
    547010U,	// LDRBrs
    551365U,	// LDRD
    580037U,	// LDRD_POST
    580037U,	// LDRD_PRE
    540318U,	// LDREX
    538941U,	// LDREXB
    268974569U,	// LDREXD
    539299U,	// LDREXH
    547431U,	// LDRH
    548193U,	// LDRHTi
    552289U,	// LDRHTr
    551527U,	// LDRH_POST
    551527U,	// LDRH_PRE
    547028U,	// LDRSB
    548170U,	// LDRSBTi
    552266U,	// LDRSBTr
    551124U,	// LDRSB_POST
    551124U,	// LDRSB_PRE
    547450U,	// LDRSH
    548205U,	// LDRSHTi
    552301U,	// LDRSHTr
    551546U,	// LDRSH_POST
    551546U,	// LDRSH_PRE
    552333U,	// LDRT_POST_IMM
    552333U,	// LDRT_POST_REG
    552041U,	// LDR_POST_IMM
    552041U,	// LDR_POST_REG
    547945U,	// LDR_PRE_IMM
    552041U,	// LDR_PRE_REG
    556137U,	// LDRcp
    556137U,	// LDRi12
    547945U,	// LDRrs
    201907296U,	// MCR
    168878515U,	// MCR2
    201878664U,	// MCRR
    168878521U,	// MCRR2
    559162U,	// MLA
    548043U,	// MLS
    1887744U,	// MOVPCLR
    556493U,	// MOVTi16
    544256U,	// MOVi
    540181U,	// MOVi16
    544256U,	// MOVr
    544256U,	// MOVr_TC
    531968U,	// MOVsi
    560640U,	// MOVsr
    336124260U,	// MRC
    74138U,	// MRC2
    352872808U,	// MRRC
    78240U,	// MRRC2
    2148056312U,	// MRS
    539896U,	// MRSbanked
    3221798136U,	// MRSsys
    369638558U,	// MSR
    386415774U,	// MSRbanked
    369638558U,	// MSRi
    531339U,	// MUL
    543769U,	// MVNi
    543769U,	// MVNr
    531481U,	// MVNsi
    560153U,	// MVNsr
    531598U,	// ORRri
    531598U,	// ORRrr
    560270U,	// ORRrsi
    40078U,	// ORRrsr
    548137U,	// PKHBT
    547045U,	// PKHTB
    83312U,	// PLDWi12
    87408U,	// PLDWrs
    83193U,	// PLDi12
    87289U,	// PLDrs
    83228U,	// PLIi12
    87324U,	// PLIrs
    555428U,	// QADD
    554822U,	// QADD16
    554925U,	// QADD8
    556751U,	// QASX
    555402U,	// QDADD
    555274U,	// QDSUB
    556610U,	// QSAX
    555287U,	// QSUB
    554784U,	// QSUB16
    554886U,	// QSUB8
    540020U,	// RBIT
    540140U,	// REV
    538474U,	// REV16
    539269U,	// REVSH
    828595U,	// RFEDA
    2008243U,	// RFEDA_UPD
    828626U,	// RFEDB
    2008274U,	// RFEDB_UPD
    828602U,	// RFEIA
    2008250U,	// RFEIA_UPD
    828633U,	// RFEIB
    2008281U,	// RFEIB_UPD
    530646U,	// RSBri
    530646U,	// RSBrr
    559318U,	// RSBrsi
    39126U,	// RSBrsr
    530797U,	// RSCri
    530797U,	// RSCrr
    559469U,	// RSCrsi
    39277U,	// RSCrsr
    554829U,	// SADD16
    554931U,	// SADD8
    556756U,	// SASX
    2253U,	// SB
    530763U,	// SBCri
    530763U,	// SBCrr
    559435U,	// SBCrsi
    39243U,	// SBCrsr
    548528U,	// SBFX
    556528U,	// SDIV
    555816U,	// SEL
    91390U,	// SETEND
    828723U,	// SETPAN
    168468546U,	// SHA1C
    1258987596U,	// SHA1H
    168468578U,	// SHA1M
    168468588U,	// SHA1P
    168468481U,	// SHA1SU0
    1242210331U,	// SHA1SU1
    168468566U,	// SHA256H
    168468533U,	// SHA256H2
    1242210317U,	// SHA256SU0
    168468519U,	// SHA256SU1
    554805U,	// SHADD16
    554910U,	// SHADD8
    556738U,	// SHASX
    556597U,	// SHSAX
    554767U,	// SHSUB16
    554871U,	// SHSUB8
    1074313568U,	// SMC
    546932U,	// SMLABB
    548130U,	// SMLABT
    547193U,	// SMLAD
    548454U,	// SMLADX
    97006U,	// SMLAL
    579707U,	// SMLALBB
    580911U,	// SMLALBT
    580014U,	// SMLALD
    581236U,	// SMLALDX
    579819U,	// SMLALTB
    581033U,	// SMLALTT
    547038U,	// SMLATB
    548258U,	// SMLATT
    547105U,	// SMLAWB
    548306U,	// SMLAWT
    547279U,	// SMLSD
    548484U,	// SMLSDX
    580025U,	// SMLSLD
    581244U,	// SMLSLDX
    546872U,	// SMMLA
    547929U,	// SMMLAR
    548041U,	// SMMLS
    547990U,	// SMMLSR
    555913U,	// SMMUL
    556152U,	// SMMULR
    555391U,	// SMUAD
    556653U,	// SMUADX
    555139U,	// SMULBB
    556343U,	// SMULBT
    559968U,	// SMULL
    555251U,	// SMULTB
    556465U,	// SMULTT
    555304U,	// SMULWB
    556505U,	// SMULWT
    555477U,	// SMUSD
    556683U,	// SMUSDX
    828858U,	// SRSDA
    828810U,	// SRSDA_UPD
    828880U,	// SRSDB
    828834U,	// SRSDB_UPD
    828869U,	// SRSIA
    828822U,	// SRSIA_UPD
    828891U,	// SRSIB
    828846U,	// SRSIB_UPD
    548115U,	// SSAT
    554843U,	// SSAT16
    556615U,	// SSAX
    554791U,	// SSUB16
    554892U,	// SSUB8
    286975272U,	// STC2L_OFFSET
    3524977960U,	// STC2L_OPTION
    303752488U,	// STC2L_POST
    320529704U,	// STC2L_PRE
    286974375U,	// STC2_OFFSET
    3524977063U,	// STC2_OPTION
    303751591U,	// STC2_POST
    320528807U,	// STC2_PRE
    1275616016U,	// STCL_OFFSET
    1275616016U,	// STCL_OPTION
    1275616016U,	// STCL_POST
    1275616016U,	// STCL_PRE
    1275615601U,	// STC_OFFSET
    1275615601U,	// STC_OPTION
    1275615601U,	// STC_POST
    1275615601U,	// STC_PRE
    539525U,	// STL
    538804U,	// STLB
    556696U,	// STLEX
    555318U,	// STLEXB
    555490U,	// STLEXD
    555676U,	// STLEXH
    539217U,	// STLH
    571416U,	// STMDA
    2332571672U,	// STMDA_UPD
    571548U,	// STMDB
    2332571804U,	// STMDB_UPD
    572328U,	// STMIA
    2332572584U,	// STMIA_UPD
    571566U,	// STMIB
    2332571822U,	// STMIB_UPD
    185101636U,	// STRBT_POST_IMM
    185101636U,	// STRBT_POST_REG
    185100487U,	// STRB_POST_IMM
    185100487U,	// STRB_POST_REG
    185096391U,	// STRB_PRE_IMM
    185100487U,	// STRB_PRE_REG
    555207U,	// STRBi12
    547015U,	// STRBrs
    551370U,	// STRD
    185129418U,	// STRD_POST
    185129418U,	// STRD_PRE
    556714U,	// STREX
    555332U,	// STREXB
    555504U,	// STREXD
    555690U,	// STREXH
    547436U,	// STRH
    185097575U,	// STRHTi
    185101671U,	// STRHTr
    185100908U,	// STRH_POST
    185100908U,	// STRH_PRE
    185101720U,	// STRT_POST_IMM
    185101720U,	// STRT_POST_REG
    185101482U,	// STR_POST_IMM
    185101482U,	// STR_POST_REG
    185097386U,	// STR_PRE_IMM
    185101482U,	// STR_PRE_REG
    556202U,	// STRi12
    548010U,	// STRrs
    530700U,	// SUBri
    530700U,	// SUBrr
    559372U,	// SUBrsi
    39180U,	// SUBrsr
    1074313589U,	// SVC
    556103U,	// SWP
    555197U,	// SWPB
    546920U,	// SXTAB
    546545U,	// SXTAB16
    547393U,	// SXTAH
    555264U,	// SXTB
    554753U,	// SXTB16
    555659U,	// SXTH
    539733U,	// TEQri
    539733U,	// TEQrr
    556117U,	// TEQrsi
    547925U,	// TEQrsr
    3114U,	// TRAP
    3114U,	// TRAPNaCl
    99567U,	// TSB
    540062U,	// TSTri
    540062U,	// TSTrr
    556446U,	// TSTrsi
    548254U,	// TSTrsr
    554836U,	// UADD16
    554937U,	// UADD8
    556761U,	// UASX
    548533U,	// UBFX
    828678U,	// UDF
    556533U,	// UDIV
    554813U,	// UHADD16
    554917U,	// UHADD8
    556744U,	// UHASX
    556603U,	// UHSAX
    554775U,	// UHSUB16
    554878U,	// UHSUB8
    580307U,	// UMAAL
    97012U,	// UMLAL
    559974U,	// UMULL
    554821U,	// UQADD16
    554924U,	// UQADD8
    556750U,	// UQASX
    556609U,	// UQSAX
    554783U,	// UQSUB16
    554885U,	// UQSUB8
    554904U,	// USAD8
    546672U,	// USADA8
    548120U,	// USAT
    554850U,	// USAT16
    556620U,	// USAX
    554798U,	// USUB16
    554898U,	// USUB8
    546926U,	// UXTAB
    546553U,	// UXTAB16
    547399U,	// UXTAH
    555269U,	// UXTB
    554760U,	// UXTB16
    555664U,	// UXTH
    169892569U,	// VABALsv2i64
    170023641U,	// VABALsv4i32
    170154713U,	// VABALsv8i16
    170285785U,	// VABALuv2i64
    170416857U,	// VABALuv4i32
    170547929U,	// VABALuv8i16
    170153993U,	// VABAsv16i8
    169891849U,	// VABAsv2i32
    170022921U,	// VABAsv4i16
    169891849U,	// VABAsv4i32
    170022921U,	// VABAsv8i16
    170153993U,	// VABAsv8i8
    170547209U,	// VABAuv16i8
    170285065U,	// VABAuv2i32
    170416137U,	// VABAuv4i16
    170285065U,	// VABAuv4i32
    170416137U,	// VABAuv8i16
    170547209U,	// VABAuv8i8
    186678037U,	// VABDLsv2i64
    186809109U,	// VABDLsv4i32
    186940181U,	// VABDLsv8i16
    187071253U,	// VABDLuv2i64
    187202325U,	// VABDLuv4i32
    187333397U,	// VABDLuv8i16
    253131141U,	// VABDfd
    253131141U,	// VABDfq
    253000069U,	// VABDhd
    253000069U,	// VABDhq
    186939781U,	// VABDsv16i8
    186677637U,	// VABDsv2i32
    186808709U,	// VABDsv4i16
    186677637U,	// VABDsv4i32
    186808709U,	// VABDsv8i16
    186939781U,	// VABDsv8i8
    187332997U,	// VABDuv16i8
    187070853U,	// VABDuv2i32
    187201925U,	// VABDuv4i16
    187070853U,	// VABDuv4i32
    187201925U,	// VABDuv8i16
    187332997U,	// VABDuv8i8
    252853434U,	// VABSD
    252984506U,	// VABSH
    253115578U,	// VABSS
    253115578U,	// VABSfd
    253115578U,	// VABSfq
    252984506U,	// VABShd
    252984506U,	// VABShq
    1260666042U,	// VABSv16i8
    1260403898U,	// VABSv2i32
    1260534970U,	// VABSv4i16
    1260403898U,	// VABSv4i32
    1260534970U,	// VABSv8i16
    1260666042U,	// VABSv8i8
    253131255U,	// VACGEfd
    253131255U,	// VACGEfq
    253000183U,	// VACGEhd
    253000183U,	// VACGEhq
    253132118U,	// VACGTfd
    253132118U,	// VACGTfq
    253001046U,	// VACGThd
    253001046U,	// VACGThq
    252869033U,	// VADDD
    253000105U,	// VADDH
    187464643U,	// VADDHNv2i32
    187595715U,	// VADDHNv4i16
    187726787U,	// VADDHNv8i8
    186678050U,	// VADDLsv2i64
    186809122U,	// VADDLsv4i32
    186940194U,	// VADDLsv8i16
    187071266U,	// VADDLuv2i64
    187202338U,	// VADDLuv4i32
    187333410U,	// VADDLuv8i16
    253131177U,	// VADDS
    186678794U,	// VADDWsv2i64
    186809866U,	// VADDWsv4i32
    186940938U,	// VADDWsv8i16
    187072010U,	// VADDWuv2i64
    187203082U,	// VADDWuv4i32
    187334154U,	// VADDWuv8i16
    253131177U,	// VADDfd
    253131177U,	// VADDfq
    253000105U,	// VADDhd
    253000105U,	// VADDhq
    187857321U,	// VADDv16i8
    187464105U,	// VADDv1i64
    187595177U,	// VADDv2i32
    187464105U,	// VADDv2i64
    187726249U,	// VADDv4i16
    187595177U,	// VADDv4i32
    187726249U,	// VADDv8i16
    187857321U,	// VADDv8i8
    555456U,	// VANDd
    555456U,	// VANDq
    555355U,	// VBICd
    405698907U,	// VBICiv2i32
    405829979U,	// VBICiv4i16
    405698907U,	// VBICiv4i32
    405829979U,	// VBICiv8i16
    555355U,	// VBICq
    547356U,	// VBIFd
    547356U,	// VBIFq
    548217U,	// VBITd
    548217U,	// VBITq
    547698U,	// VBSLd
    547698U,	// VBSLq
    185245957U,	// VCADDv2f32
    185246658U,	// VCADDv4f16
    185245957U,	// VCADDv4f32
    185246658U,	// VCADDv8f16
    253131856U,	// VCEQfd
    253131856U,	// VCEQfq
    253000784U,	// VCEQhd
    253000784U,	// VCEQhq
    187858000U,	// VCEQv16i8
    187595856U,	// VCEQv2i32
    187726928U,	// VCEQv4i16
    187595856U,	// VCEQv4i32
    187726928U,	// VCEQv8i16
    187858000U,	// VCEQv8i8
    1261583440U,	// VCEQzv16i8
    253115472U,	// VCEQzv2f32
    1261321296U,	// VCEQzv2i32
    252984400U,	// VCEQzv4f16
    253115472U,	// VCEQzv4f32
    1261452368U,	// VCEQzv4i16
    1261321296U,	// VCEQzv4i32
    252984400U,	// VCEQzv8f16
    1261452368U,	// VCEQzv8i16
    1261583440U,	// VCEQzv8i8
    253131261U,	// VCGEfd
    253131261U,	// VCGEfq
    253000189U,	// VCGEhd
    253000189U,	// VCGEhq
    186939901U,	// VCGEsv16i8
    186677757U,	// VCGEsv2i32
    186808829U,	// VCGEsv4i16
    186677757U,	// VCGEsv4i32
    186808829U,	// VCGEsv8i16
    186939901U,	// VCGEsv8i8
    187333117U,	// VCGEuv16i8
    187070973U,	// VCGEuv2i32
    187202045U,	// VCGEuv4i16
    187070973U,	// VCGEuv4i32
    187202045U,	// VCGEuv8i16
    187333117U,	// VCGEuv8i8
    1260665341U,	// VCGEzv16i8
    253114877U,	// VCGEzv2f32
    1260403197U,	// VCGEzv2i32
    252983805U,	// VCGEzv4f16
    253114877U,	// VCGEzv4f32
    1260534269U,	// VCGEzv4i16
    1260403197U,	// VCGEzv4i32
    252983805U,	// VCGEzv8f16
    1260534269U,	// VCGEzv8i16
    1260665341U,	// VCGEzv8i8
    253132124U,	// VCGTfd
    253132124U,	// VCGTfq
    253001052U,	// VCGThd
    253001052U,	// VCGThq
    186940764U,	// VCGTsv16i8
    186678620U,	// VCGTsv2i32
    186809692U,	// VCGTsv4i16
    186678620U,	// VCGTsv4i32
    186809692U,	// VCGTsv8i16
    186940764U,	// VCGTsv8i8
    187333980U,	// VCGTuv16i8
    187071836U,	// VCGTuv2i32
    187202908U,	// VCGTuv4i16
    187071836U,	// VCGTuv4i32
    187202908U,	// VCGTuv8i16
    187333980U,	// VCGTuv8i8
    1260666204U,	// VCGTzv16i8
    253115740U,	// VCGTzv2f32
    1260404060U,	// VCGTzv2i32
    252984668U,	// VCGTzv4f16
    253115740U,	// VCGTzv4f32
    1260535132U,	// VCGTzv4i16
    1260404060U,	// VCGTzv4i32
    252984668U,	// VCGTzv8f16
    1260535132U,	// VCGTzv8i16
    1260666204U,	// VCGTzv8i8
    1260665346U,	// VCLEzv16i8
    253114882U,	// VCLEzv2f32
    1260403202U,	// VCLEzv2i32
    252983810U,	// VCLEzv4f16
    253114882U,	// VCLEzv4f32
    1260534274U,	// VCLEzv4i16
    1260403202U,	// VCLEzv4i32
    252983810U,	// VCLEzv8f16
    1260534274U,	// VCLEzv8i16
    1260665346U,	// VCLEzv8i8
    1260666052U,	// VCLSv16i8
    1260403908U,	// VCLSv2i32
    1260534980U,	// VCLSv4i16
    1260403908U,	// VCLSv4i32
    1260534980U,	// VCLSv8i16
    1260666052U,	// VCLSv8i8
    1260666238U,	// VCLTzv16i8
    253115774U,	// VCLTzv2f32
    1260404094U,	// VCLTzv2i32
    252984702U,	// VCLTzv4f16
    253115774U,	// VCLTzv4f32
    1260535166U,	// VCLTzv4i16
    1260404094U,	// VCLTzv4i32
    252984702U,	// VCLTzv8f16
    1260535166U,	// VCLTzv8i16
    1260666238U,	// VCLTzv8i8
    1261584101U,	// VCLZv16i8
    1261321957U,	// VCLZv2i32
    1261453029U,	// VCLZv4i16
    1261321957U,	// VCLZv4i32
    1261453029U,	// VCLZv8i16
    1261584101U,	// VCLZv8i8
    168468718U,	// VCMLAv2f32
    168468718U,	// VCMLAv2f32_indexed
    168469419U,	// VCMLAv4f16
    168469419U,	// VCMLAv4f16_indexed
    168468718U,	// VCMLAv4f32
    168468718U,	// VCMLAv4f32_indexed
    168469419U,	// VCMLAv8f16
    168469419U,	// VCMLAv8f16_indexed
    252853304U,	// VCMPD
    252852750U,	// VCMPED
    252983822U,	// VCMPEH
    253114894U,	// VCMPES
    420657678U,	// VCMPEZD
    420788750U,	// VCMPEZH
    420919822U,	// VCMPEZS
    252984376U,	// VCMPH
    253115448U,	// VCMPS
    420658232U,	// VCMPZD
    420789304U,	// VCMPZH
    420920376U,	// VCMPZS
    408963U,	// VCNTd
    408963U,	// VCNTq
    1258987638U,	// VCVTANSDf
    1258988339U,	// VCVTANSDh
    1258987638U,	// VCVTANSQf
    1258988339U,	// VCVTANSQh
    1258987698U,	// VCVTANUDf
    1258988399U,	// VCVTANUDh
    1258987698U,	// VCVTANUQf
    1258988399U,	// VCVTANUQh
    1258987968U,	// VCVTASD
    1258988219U,	// VCVTASH
    1258987638U,	// VCVTASS
    1258988028U,	// VCVTAUD
    1258988279U,	// VCVTAUH
    1258987698U,	// VCVTAUS
    3422458U,	// VCVTBDH
    3553530U,	// VCVTBHD
    3684602U,	// VCVTBHS
    3815674U,	// VCVTBSH
    3947976U,	// VCVTDS
    1258987653U,	// VCVTMNSDf
    1258988354U,	// VCVTMNSDh
    1258987653U,	// VCVTMNSQf
    1258988354U,	// VCVTMNSQh
    1258987713U,	// VCVTMNUDf
    1258988414U,	// VCVTMNUDh
    1258987713U,	// VCVTMNUQf
    1258988414U,	// VCVTMNUQh
    1258987983U,	// VCVTMSD
    1258988234U,	// VCVTMSH
    1258987653U,	// VCVTMSS
    1258988043U,	// VCVTMUD
    1258988294U,	// VCVTMUH
    1258987713U,	// VCVTMUS
    1258987668U,	// VCVTNNSDf
    1258988369U,	// VCVTNNSDh
    1258987668U,	// VCVTNNSQf
    1258988369U,	// VCVTNNSQh
    1258987728U,	// VCVTNNUDf
    1258988429U,	// VCVTNNUDh
    1258987728U,	// VCVTNNUQf
    1258988429U,	// VCVTNNUQh
    1258987998U,	// VCVTNSD
    1258988249U,	// VCVTNSH
    1258987668U,	// VCVTNSS
    1258988058U,	// VCVTNUD
    1258988309U,	// VCVTNUH
    1258987728U,	// VCVTNUS
    1258987683U,	// VCVTPNSDf
    1258988384U,	// VCVTPNSDh
    1258987683U,	// VCVTPNSQf
    1258988384U,	// VCVTPNSQh
    1258987743U,	// VCVTPNUDf
    1258988444U,	// VCVTPNUDh
    1258987743U,	// VCVTPNUQf
    1258988444U,	// VCVTPNUQh
    1258988013U,	// VCVTPSD
    1258988264U,	// VCVTPSH
    1258987683U,	// VCVTPSS
    1258988073U,	// VCVTPUD
    1258988324U,	// VCVTPUH
    1258987743U,	// VCVTPUS
    4079048U,	// VCVTSD
    3423676U,	// VCVTTDH
    3554748U,	// VCVTTHD
    3685820U,	// VCVTTHS
    3816892U,	// VCVTTSH
    3816904U,	// VCVTf2h
    440417736U,	// VCVTf2sd
    440417736U,	// VCVTf2sq
    440548808U,	// VCVTf2ud
    440548808U,	// VCVTf2uq
    2403368392U,	// VCVTf2xsd
    2403368392U,	// VCVTf2xsq
    2403499464U,	// VCVTf2xud
    2403499464U,	// VCVTf2xuq
    3685832U,	// VCVTh2f
    440679880U,	// VCVTh2sd
    440679880U,	// VCVTh2sq
    440810952U,	// VCVTh2ud
    440810952U,	// VCVTh2uq
    2403630536U,	// VCVTh2xsd
    2403630536U,	// VCVTh2xsq
    2403761608U,	// VCVTh2xud
    2403761608U,	// VCVTh2xuq
    440942024U,	// VCVTs2fd
    440942024U,	// VCVTs2fq
    441073096U,	// VCVTs2hd
    441073096U,	// VCVTs2hq
    441204168U,	// VCVTu2fd
    441204168U,	// VCVTu2fq
    441335240U,	// VCVTu2hd
    441335240U,	// VCVTu2hq
    2403892680U,	// VCVTxs2fd
    2403892680U,	// VCVTxs2fq
    2404023752U,	// VCVTxs2hd
    2404023752U,	// VCVTxs2hq
    2404154824U,	// VCVTxu2fd
    2404154824U,	// VCVTxu2fq
    2404285896U,	// VCVTxu2hd
    2404285896U,	// VCVTxu2hq
    252870138U,	// VDIVD
    253001210U,	// VDIVH
    253132282U,	// VDIVS
    146497U,	// VDUP16d
    146497U,	// VDUP16q
    277569U,	// VDUP32d
    277569U,	// VDUP32q
    408641U,	// VDUP8d
    408641U,	// VDUP8q
    162881U,	// VDUPLN16d
    162881U,	// VDUPLN16q
    293953U,	// VDUPLN32d
    293953U,	// VDUPLN32q
    425025U,	// VDUPLN8d
    425025U,	// VDUPLN8q
    556159U,	// VEORd
    556159U,	// VEORq
    155104U,	// VEXTd16
    286176U,	// VEXTd32
    417248U,	// VEXTd8
    155104U,	// VEXTq16
    286176U,	// VEXTq32
    5266912U,	// VEXTq64
    417248U,	// VEXTq8
    2400344137U,	// VFMAD
    2400475209U,	// VFMAH
    185246681U,	// VFMALD
    185246681U,	// VFMALDI
    185246681U,	// VFMALQ
    185246681U,	// VFMALQI
    2400606281U,	// VFMAS
    2400606281U,	// VFMAfd
    2400606281U,	// VFMAfq
    2400475209U,	// VFMAhd
    2400475209U,	// VFMAhq
    2400345306U,	// VFMSD
    2400476378U,	// VFMSH
    185246692U,	// VFMSLD
    185246692U,	// VFMSLDI
    185246692U,	// VFMSLQ
    185246692U,	// VFMSLQI
    2400607450U,	// VFMSS
    2400607450U,	// VFMSfd
    2400607450U,	// VFMSfq
    2400476378U,	// VFMShd
    2400476378U,	// VFMShq
    2400344142U,	// VFNMAD
    2400475214U,	// VFNMAH
    2400606286U,	// VFNMAS
    2400345311U,	// VFNMSD
    2400476383U,	// VFNMSH
    2400607455U,	// VFNMSS
    294399U,	// VGETLNi32
    3408035327U,	// VGETLNs16
    3408166399U,	// VGETLNs8
    3408428543U,	// VGETLNu16
    3408559615U,	// VGETLNu8
    186939799U,	// VHADDsv16i8
    186677655U,	// VHADDsv2i32
    186808727U,	// VHADDsv4i16
    186677655U,	// VHADDsv4i32
    186808727U,	// VHADDsv8i16
    186939799U,	// VHADDsv8i8
    187333015U,	// VHADDuv16i8
    187070871U,	// VHADDuv2i32
    187201943U,	// VHADDuv4i16
    187070871U,	// VHADDuv4i32
    187201943U,	// VHADDuv8i16
    187333015U,	// VHADDuv8i8
    186939664U,	// VHSUBsv16i8
    186677520U,	// VHSUBsv2i32
    186808592U,	// VHSUBsv4i16
    186677520U,	// VHSUBsv4i32
    186808592U,	// VHSUBsv8i16
    186939664U,	// VHSUBsv8i8
    187332880U,	// VHSUBuv16i8
    187070736U,	// VHSUBuv2i32
    187201808U,	// VHSUBuv4i16
    187070736U,	// VHSUBuv4i32
    187201808U,	// VHSUBuv8i16
    187332880U,	// VHSUBuv8i8
    1258988599U,	// VINSH
    441597378U,	// VJCVT
    3674371716U,	// VLD1DUPd16
    453138052U,	// VLD1DUPd16wb_fixed
    453142148U,	// VLD1DUPd16wb_register
    3674502788U,	// VLD1DUPd32
    453269124U,	// VLD1DUPd32wb_fixed
    453273220U,	// VLD1DUPd32wb_register
    3674633860U,	// VLD1DUPd8
    453400196U,	// VLD1DUPd8wb_fixed
    453404292U,	// VLD1DUPd8wb_register
    3691148932U,	// VLD1DUPq16
    469915268U,	// VLD1DUPq16wb_fixed
    469919364U,	// VLD1DUPq16wb_register
    3691280004U,	// VLD1DUPq32
    470046340U,	// VLD1DUPq32wb_fixed
    470050436U,	// VLD1DUPq32wb_register
    3691411076U,	// VLD1DUPq8
    470177412U,	// VLD1DUPq8wb_fixed
    470181508U,	// VLD1DUPq8wb_register
    1079273092U,	// VLD1LNd16
    1079350916U,	// VLD1LNd16_UPD
    1079404164U,	// VLD1LNd32
    1079481988U,	// VLD1LNd32_UPD
    1079535236U,	// VLD1LNd8
    1079613060U,	// VLD1LNd8_UPD
    0U,	// VLD1LNq16Pseudo
    0U,	// VLD1LNq16Pseudo_UPD
    0U,	// VLD1LNq32Pseudo
    0U,	// VLD1LNq32Pseudo_UPD
    0U,	// VLD1LNq8Pseudo
    0U,	// VLD1LNq8Pseudo_UPD
    3707926148U,	// VLD1d16
    3355604612U,	// VLD1d16Q
    0U,	// VLD1d16QPseudo
    134370948U,	// VLD1d16Qwb_fixed
    134375044U,	// VLD1d16Qwb_register
    3288495748U,	// VLD1d16T
    0U,	// VLD1d16TPseudo
    67262084U,	// VLD1d16Twb_fixed
    67266180U,	// VLD1d16Twb_register
    486692484U,	// VLD1d16wb_fixed
    486696580U,	// VLD1d16wb_register
    3708057220U,	// VLD1d32
    3355735684U,	// VLD1d32Q
    0U,	// VLD1d32QPseudo
    134502020U,	// VLD1d32Qwb_fixed
    134506116U,	// VLD1d32Qwb_register
    3288626820U,	// VLD1d32T
    0U,	// VLD1d32TPseudo
    67393156U,	// VLD1d32Twb_fixed
    67397252U,	// VLD1d32Twb_register
    486823556U,	// VLD1d32wb_fixed
    486827652U,	// VLD1d32wb_register
    3713037956U,	// VLD1d64
    3360716420U,	// VLD1d64Q
    0U,	// VLD1d64QPseudo
    0U,	// VLD1d64QPseudoWB_fixed
    0U,	// VLD1d64QPseudoWB_register
    139482756U,	// VLD1d64Qwb_fixed
    139486852U,	// VLD1d64Qwb_register
    3293607556U,	// VLD1d64T
    0U,	// VLD1d64TPseudo
    0U,	// VLD1d64TPseudoWB_fixed
    0U,	// VLD1d64TPseudoWB_register
    72373892U,	// VLD1d64Twb_fixed
    72377988U,	// VLD1d64Twb_register
    491804292U,	// VLD1d64wb_fixed
    491808388U,	// VLD1d64wb_register
    3708188292U,	// VLD1d8
    3355866756U,	// VLD1d8Q
    0U,	// VLD1d8QPseudo
    134633092U,	// VLD1d8Qwb_fixed
    134637188U,	// VLD1d8Qwb_register
    3288757892U,	// VLD1d8T
    0U,	// VLD1d8TPseudo
    67524228U,	// VLD1d8Twb_fixed
    67528324U,	// VLD1d8Twb_register
    486954628U,	// VLD1d8wb_fixed
    486958724U,	// VLD1d8wb_register
    3724703364U,	// VLD1q16
    0U,	// VLD1q16HighQPseudo
    0U,	// VLD1q16HighTPseudo
    0U,	// VLD1q16LowQPseudo_UPD
    0U,	// VLD1q16LowTPseudo_UPD
    503469700U,	// VLD1q16wb_fixed
    503473796U,	// VLD1q16wb_register
    3724834436U,	// VLD1q32
    0U,	// VLD1q32HighQPseudo
    0U,	// VLD1q32HighTPseudo
    0U,	// VLD1q32LowQPseudo_UPD
    0U,	// VLD1q32LowTPseudo_UPD
    503600772U,	// VLD1q32wb_fixed
    503604868U,	// VLD1q32wb_register
    3729815172U,	// VLD1q64
    0U,	// VLD1q64HighQPseudo
    0U,	// VLD1q64HighTPseudo
    0U,	// VLD1q64LowQPseudo_UPD
    0U,	// VLD1q64LowTPseudo_UPD
    508581508U,	// VLD1q64wb_fixed
    508585604U,	// VLD1q64wb_register
    3724965508U,	// VLD1q8
    0U,	// VLD1q8HighQPseudo
    0U,	// VLD1q8HighTPseudo
    0U,	// VLD1q8LowQPseudo_UPD
    0U,	// VLD1q8LowTPseudo_UPD
    503731844U,	// VLD1q8wb_fixed
    503735940U,	// VLD1q8wb_register
    3691148976U,	// VLD2DUPd16
    469915312U,	// VLD2DUPd16wb_fixed
    469919408U,	// VLD2DUPd16wb_register
    3741480624U,	// VLD2DUPd16x2
    520246960U,	// VLD2DUPd16x2wb_fixed
    520251056U,	// VLD2DUPd16x2wb_register
    3691280048U,	// VLD2DUPd32
    470046384U,	// VLD2DUPd32wb_fixed
    470050480U,	// VLD2DUPd32wb_register
    3741611696U,	// VLD2DUPd32x2
    520378032U,	// VLD2DUPd32x2wb_fixed
    520382128U,	// VLD2DUPd32x2wb_register
    3691411120U,	// VLD2DUPd8
    470177456U,	// VLD2DUPd8wb_fixed
    470181552U,	// VLD2DUPd8wb_register
    3741742768U,	// VLD2DUPd8x2
    520509104U,	// VLD2DUPd8x2wb_fixed
    520513200U,	// VLD2DUPd8x2wb_register
    0U,	// VLD2DUPq16EvenPseudo
    0U,	// VLD2DUPq16OddPseudo
    0U,	// VLD2DUPq32EvenPseudo
    0U,	// VLD2DUPq32OddPseudo
    0U,	// VLD2DUPq8EvenPseudo
    0U,	// VLD2DUPq8OddPseudo
    1079350960U,	// VLD2LNd16
    0U,	// VLD2LNd16Pseudo
    0U,	// VLD2LNd16Pseudo_UPD
    1079355056U,	// VLD2LNd16_UPD
    1079482032U,	// VLD2LNd32
    0U,	// VLD2LNd32Pseudo
    0U,	// VLD2LNd32Pseudo_UPD
    1079486128U,	// VLD2LNd32_UPD
    1079613104U,	// VLD2LNd8
    0U,	// VLD2LNd8Pseudo
    0U,	// VLD2LNd8Pseudo_UPD
    1079617200U,	// VLD2LNd8_UPD
    1079350960U,	// VLD2LNq16
    0U,	// VLD2LNq16Pseudo
    0U,	// VLD2LNq16Pseudo_UPD
    1079355056U,	// VLD2LNq16_UPD
    1079482032U,	// VLD2LNq32
    0U,	// VLD2LNq32Pseudo
    0U,	// VLD2LNq32Pseudo_UPD
    1079486128U,	// VLD2LNq32_UPD
    3758257840U,	// VLD2b16
    537024176U,	// VLD2b16wb_fixed
    537028272U,	// VLD2b16wb_register
    3758388912U,	// VLD2b32
    537155248U,	// VLD2b32wb_fixed
    537159344U,	// VLD2b32wb_register
    3758519984U,	// VLD2b8
    537286320U,	// VLD2b8wb_fixed
    537290416U,	// VLD2b8wb_register
    3724703408U,	// VLD2d16
    503469744U,	// VLD2d16wb_fixed
    503473840U,	// VLD2d16wb_register
    3724834480U,	// VLD2d32
    503600816U,	// VLD2d32wb_fixed
    503604912U,	// VLD2d32wb_register
    3724965552U,	// VLD2d8
    503731888U,	// VLD2d8wb_fixed
    503735984U,	// VLD2d8wb_register
    3355604656U,	// VLD2q16
    0U,	// VLD2q16Pseudo
    0U,	// VLD2q16PseudoWB_fixed
    0U,	// VLD2q16PseudoWB_register
    134370992U,	// VLD2q16wb_fixed
    134375088U,	// VLD2q16wb_register
    3355735728U,	// VLD2q32
    0U,	// VLD2q32Pseudo
    0U,	// VLD2q32PseudoWB_fixed
    0U,	// VLD2q32PseudoWB_register
    134502064U,	// VLD2q32wb_fixed
    134506160U,	// VLD2q32wb_register
    3355866800U,	// VLD2q8
    0U,	// VLD2q8Pseudo
    0U,	// VLD2q8PseudoWB_fixed
    0U,	// VLD2q8PseudoWB_register
    134633136U,	// VLD2q8wb_fixed
    134637232U,	// VLD2q8wb_register
    2153014992U,	// VLD3DUPd16
    0U,	// VLD3DUPd16Pseudo
    0U,	// VLD3DUPd16Pseudo_UPD
    2153092816U,	// VLD3DUPd16_UPD
    2153146064U,	// VLD3DUPd32
    0U,	// VLD3DUPd32Pseudo
    0U,	// VLD3DUPd32Pseudo_UPD
    2153223888U,	// VLD3DUPd32_UPD
    2153277136U,	// VLD3DUPd8
    0U,	// VLD3DUPd8Pseudo
    0U,	// VLD3DUPd8Pseudo_UPD
    2153354960U,	// VLD3DUPd8_UPD
    2153014992U,	// VLD3DUPq16
    0U,	// VLD3DUPq16EvenPseudo
    0U,	// VLD3DUPq16OddPseudo
    2153092816U,	// VLD3DUPq16_UPD
    2153146064U,	// VLD3DUPq32
    0U,	// VLD3DUPq32EvenPseudo
    0U,	// VLD3DUPq32OddPseudo
    2153223888U,	// VLD3DUPq32_UPD
    2153277136U,	// VLD3DUPq8
    0U,	// VLD3DUPq8EvenPseudo
    0U,	// VLD3DUPq8OddPseudo
    2153354960U,	// VLD3DUPq8_UPD
    1079355088U,	// VLD3LNd16
    0U,	// VLD3LNd16Pseudo
    0U,	// VLD3LNd16Pseudo_UPD
    1079359184U,	// VLD3LNd16_UPD
    1079486160U,	// VLD3LNd32
    0U,	// VLD3LNd32Pseudo
    0U,	// VLD3LNd32Pseudo_UPD
    1079490256U,	// VLD3LNd32_UPD
    1079617232U,	// VLD3LNd8
    0U,	// VLD3LNd8Pseudo
    0U,	// VLD3LNd8Pseudo_UPD
    1079621328U,	// VLD3LNd8_UPD
    1079355088U,	// VLD3LNq16
    0U,	// VLD3LNq16Pseudo
    0U,	// VLD3LNq16Pseudo_UPD
    1079359184U,	// VLD3LNq16_UPD
    1079486160U,	// VLD3LNq32
    0U,	// VLD3LNq32Pseudo
    0U,	// VLD3LNq32Pseudo_UPD
    1079490256U,	// VLD3LNq32_UPD
    5531344U,	// VLD3d16
    0U,	// VLD3d16Pseudo
    0U,	// VLD3d16Pseudo_UPD
    5609168U,	// VLD3d16_UPD
    5662416U,	// VLD3d32
    0U,	// VLD3d32Pseudo
    0U,	// VLD3d32Pseudo_UPD
    5740240U,	// VLD3d32_UPD
    5793488U,	// VLD3d8
    0U,	// VLD3d8Pseudo
    0U,	// VLD3d8Pseudo_UPD
    5871312U,	// VLD3d8_UPD
    5531344U,	// VLD3q16
    0U,	// VLD3q16Pseudo_UPD
    5609168U,	// VLD3q16_UPD
    0U,	// VLD3q16oddPseudo
    0U,	// VLD3q16oddPseudo_UPD
    5662416U,	// VLD3q32
    0U,	// VLD3q32Pseudo_UPD
    5740240U,	// VLD3q32_UPD
    0U,	// VLD3q32oddPseudo
    0U,	// VLD3q32oddPseudo_UPD
    5793488U,	// VLD3q8
    0U,	// VLD3q8Pseudo_UPD
    5871312U,	// VLD3q8_UPD
    0U,	// VLD3q8oddPseudo
    0U,	// VLD3q8oddPseudo_UPD
    2153043687U,	// VLD4DUPd16
    0U,	// VLD4DUPd16Pseudo
    0U,	// VLD4DUPd16Pseudo_UPD
    2153105127U,	// VLD4DUPd16_UPD
    2153174759U,	// VLD4DUPd32
    0U,	// VLD4DUPd32Pseudo
    0U,	// VLD4DUPd32Pseudo_UPD
    2153236199U,	// VLD4DUPd32_UPD
    2153305831U,	// VLD4DUPd8
    0U,	// VLD4DUPd8Pseudo
    0U,	// VLD4DUPd8Pseudo_UPD
    2153367271U,	// VLD4DUPd8_UPD
    2153043687U,	// VLD4DUPq16
    0U,	// VLD4DUPq16EvenPseudo
    0U,	// VLD4DUPq16OddPseudo
    2153105127U,	// VLD4DUPq16_UPD
    2153174759U,	// VLD4DUPq32
    0U,	// VLD4DUPq32EvenPseudo
    0U,	// VLD4DUPq32OddPseudo
    2153236199U,	// VLD4DUPq32_UPD
    2153305831U,	// VLD4DUPq8
    0U,	// VLD4DUPq8EvenPseudo
    0U,	// VLD4DUPq8OddPseudo
    2153367271U,	// VLD4DUPq8_UPD
    1079359207U,	// VLD4LNd16
    0U,	// VLD4LNd16Pseudo
    0U,	// VLD4LNd16Pseudo_UPD
    1079367399U,	// VLD4LNd16_UPD
    1079490279U,	// VLD4LNd32
    0U,	// VLD4LNd32Pseudo
    0U,	// VLD4LNd32Pseudo_UPD
    1079498471U,	// VLD4LNd32_UPD
    1079621351U,	// VLD4LNd8
    0U,	// VLD4LNd8Pseudo
    0U,	// VLD4LNd8Pseudo_UPD
    1079629543U,	// VLD4LNd8_UPD
    1079359207U,	// VLD4LNq16
    0U,	// VLD4LNq16Pseudo
    0U,	// VLD4LNq16Pseudo_UPD
    1079367399U,	// VLD4LNq16_UPD
    1079490279U,	// VLD4LNq32
    0U,	// VLD4LNq32Pseudo
    0U,	// VLD4LNq32Pseudo_UPD
    1079498471U,	// VLD4LNq32_UPD
    5560039U,	// VLD4d16
    0U,	// VLD4d16Pseudo
    0U,	// VLD4d16Pseudo_UPD
    5621479U,	// VLD4d16_UPD
    5691111U,	// VLD4d32
    0U,	// VLD4d32Pseudo
    0U,	// VLD4d32Pseudo_UPD
    5752551U,	// VLD4d32_UPD
    5822183U,	// VLD4d8
    0U,	// VLD4d8Pseudo
    0U,	// VLD4d8Pseudo_UPD
    5883623U,	// VLD4d8_UPD
    5560039U,	// VLD4q16
    0U,	// VLD4q16Pseudo_UPD
    5621479U,	// VLD4q16_UPD
    0U,	// VLD4q16oddPseudo
    0U,	// VLD4q16oddPseudo_UPD
    5691111U,	// VLD4q32
    0U,	// VLD4q32Pseudo_UPD
    5752551U,	// VLD4q32_UPD
    0U,	// VLD4q32oddPseudo
    0U,	// VLD4q32oddPseudo_UPD
    5822183U,	// VLD4q8
    0U,	// VLD4q8Pseudo_UPD
    5883623U,	// VLD4q8_UPD
    0U,	// VLD4q8oddPseudo
    0U,	// VLD4q8oddPseudo_UPD
    2332571796U,	// VLDMDDB_UPD
    571428U,	// VLDMDIA
    2332571684U,	// VLDMDIA_UPD
    0U,	// VLDMQIA
    2332571796U,	// VLDMSDB_UPD
    571428U,	// VLDMSIA
    2332571684U,	// VLDMSIA_UPD
    556136U,	// VLDRD
    162920U,	// VLDRH
    556136U,	// VLDRS
    1074314144U,	// VLLDM
    1074314150U,	// VLSTM
    185246300U,	// VMAXNMD
    185246715U,	// VMAXNMH
    185245992U,	// VMAXNMNDf
    185246715U,	// VMAXNMNDh
    185245992U,	// VMAXNMNQf
    185246715U,	// VMAXNMNQh
    185245992U,	// VMAXNMS
    253132336U,	// VMAXfd
    253132336U,	// VMAXfq
    253001264U,	// VMAXhd
    253001264U,	// VMAXhq
    186940976U,	// VMAXsv16i8
    186678832U,	// VMAXsv2i32
    186809904U,	// VMAXsv4i16
    186678832U,	// VMAXsv4i32
    186809904U,	// VMAXsv8i16
    186940976U,	// VMAXsv8i8
    187334192U,	// VMAXuv16i8
    187072048U,	// VMAXuv2i32
    187203120U,	// VMAXuv4i16
    187072048U,	// VMAXuv4i32
    187203120U,	// VMAXuv8i16
    187334192U,	// VMAXuv8i8
    185246288U,	// VMINNMD
    185246703U,	// VMINNMH
    185245980U,	// VMINNMNDf
    185246703U,	// VMINNMNDh
    185245980U,	// VMINNMNQf
    185246703U,	// VMINNMNQh
    185245980U,	// VMINNMS
    253131728U,	// VMINfd
    253131728U,	// VMINfq
    253000656U,	// VMINhd
    253000656U,	// VMINhq
    186940368U,	// VMINsv16i8
    186678224U,	// VMINsv2i32
    186809296U,	// VMINsv4i16
    186678224U,	// VMINsv4i32
    186809296U,	// VMINsv8i16
    186940368U,	// VMINsv8i8
    187333584U,	// VMINuv16i8
    187071440U,	// VMINuv2i32
    187202512U,	// VMINuv4i16
    187071440U,	// VMINuv4i32
    187202512U,	// VMINuv8i16
    187333584U,	// VMINuv8i8
    2400344132U,	// VMLAD
    2400475204U,	// VMLAH
    169896698U,	// VMLALslsv2i32
    170027770U,	// VMLALslsv4i16
    170289914U,	// VMLALsluv2i32
    170420986U,	// VMLALsluv4i16
    169892602U,	// VMLALsv2i64
    170023674U,	// VMLALsv4i32
    170154746U,	// VMLALsv8i16
    170285818U,	// VMLALuv2i64
    170416890U,	// VMLALuv4i32
    170547962U,	// VMLALuv8i16
    2400606276U,	// VMLAS
    2400606276U,	// VMLAfd
    2400606276U,	// VMLAfq
    2400475204U,	// VMLAhd
    2400475204U,	// VMLAhq
    2400610372U,	// VMLAslfd
    2400610372U,	// VMLAslfq
    2400479300U,	// VMLAslhd
    2400479300U,	// VMLAslhq
    170813508U,	// VMLAslv2i32
    170944580U,	// VMLAslv4i16
    170813508U,	// VMLAslv4i32
    170944580U,	// VMLAslv8i16
    171071556U,	// VMLAv16i8
    170809412U,	// VMLAv2i32
    170940484U,	// VMLAv4i16
    170809412U,	// VMLAv4i32
    170940484U,	// VMLAv8i16
    171071556U,	// VMLAv8i8
    2400345301U,	// VMLSD
    2400476373U,	// VMLSH
    169896831U,	// VMLSLslsv2i32
    170027903U,	// VMLSLslsv4i16
    170290047U,	// VMLSLsluv2i32
    170421119U,	// VMLSLsluv4i16
    169892735U,	// VMLSLsv2i64
    170023807U,	// VMLSLsv4i32
    170154879U,	// VMLSLsv8i16
    170285951U,	// VMLSLuv2i64
    170417023U,	// VMLSLuv4i32
    170548095U,	// VMLSLuv8i16
    2400607445U,	// VMLSS
    2400607445U,	// VMLSfd
    2400607445U,	// VMLSfq
    2400476373U,	// VMLShd
    2400476373U,	// VMLShq
    2400611541U,	// VMLSslfd
    2400611541U,	// VMLSslfq
    2400480469U,	// VMLSslhd
    2400480469U,	// VMLSslhq
    170814677U,	// VMLSslv2i32
    170945749U,	// VMLSslv4i16
    170814677U,	// VMLSslv4i32
    170945749U,	// VMLSslv8i16
    171072725U,	// VMLSv16i8
    170810581U,	// VMLSv2i32
    170941653U,	// VMLSv4i16
    170810581U,	// VMLSv4i32
    170941653U,	// VMLSv8i16
    171072725U,	// VMLSv8i8
    252853759U,	// VMOVD
    556543U,	// VMOVDRR
    1258988645U,	// VMOVH
    252984831U,	// VMOVHR
    1260403610U,	// VMOVLsv2i64
    1260534682U,	// VMOVLsv4i32
    1260665754U,	// VMOVLsv8i16
    1260796826U,	// VMOVLuv2i64
    1260927898U,	// VMOVLuv4i32
    1261058970U,	// VMOVLuv8i16
    1261190180U,	// VMOVNv2i32
    1261321252U,	// VMOVNv4i16
    1261452324U,	// VMOVNv8i8
    252984831U,	// VMOVRH
    556543U,	// VMOVRRD
    548351U,	// VMOVRRS
    540159U,	// VMOVRS
    253115903U,	// VMOVS
    540159U,	// VMOVSR
    548351U,	// VMOVSRR
    405945855U,	// VMOVv16i8
    405552639U,	// VMOVv1i64
    1326857727U,	// VMOVv2f32
    405683711U,	// VMOVv2i32
    405552639U,	// VMOVv2i64
    1326857727U,	// VMOVv4f32
    405814783U,	// VMOVv4i16
    405683711U,	// VMOVv4i32
    405814783U,	// VMOVv8i16
    405945855U,	// VMOVv8i8
    3221798135U,	// VMRS
    572663U,	// VMRS_FPEXC
    1074314487U,	// VMRS_FPINST
    2148056311U,	// VMRS_FPINST2
    3221798135U,	// VMRS_FPSID
    572663U,	// VMRS_MVFR0
    1074314487U,	// VMRS_MVFR1
    2148056311U,	// VMRS_MVFR2
    5946525U,	// VMSR
    6077597U,	// VMSR_FPEXC
    6208669U,	// VMSR_FPINST
    6339741U,	// VMSR_FPINST2
    6470813U,	// VMSR_FPSID
    252869525U,	// VMULD
    253000597U,	// VMULH
    185246384U,	// VMULLp64
    6585196U,	// VMULLp8
    186669932U,	// VMULLslsv2i32
    186801004U,	// VMULLslsv4i16
    187063148U,	// VMULLsluv2i32
    187194220U,	// VMULLsluv4i16
    186678124U,	// VMULLsv2i64
    186809196U,	// VMULLsv4i32
    186940268U,	// VMULLsv8i16
    187071340U,	// VMULLuv2i64
    187202412U,	// VMULLuv4i32
    187333484U,	// VMULLuv8i16
    253131669U,	// VMULS
    253131669U,	// VMULfd
    253131669U,	// VMULfq
    253000597U,	// VMULhd
    253000597U,	// VMULhq
    6585237U,	// VMULpd
    6585237U,	// VMULpq
    253123477U,	// VMULslfd
    253123477U,	// VMULslfq
    252992405U,	// VMULslhd
    252992405U,	// VMULslhq
    187587477U,	// VMULslv2i32
    187718549U,	// VMULslv4i16
    187587477U,	// VMULslv4i32
    187718549U,	// VMULslv8i16
    187857813U,	// VMULv16i8
    187595669U,	// VMULv2i32
    187726741U,	// VMULv4i16
    187595669U,	// VMULv4i32
    187726741U,	// VMULv8i16
    187857813U,	// VMULv8i8
    539672U,	// VMVNd
    539672U,	// VMVNq
    405683224U,	// VMVNv2i32
    405814296U,	// VMVNv4i16
    405683224U,	// VMVNv4i32
    405814296U,	// VMVNv8i16
    252852779U,	// VNEGD
    252983851U,	// VNEGH
    253114923U,	// VNEGS
    253114923U,	// VNEGf32q
    253114923U,	// VNEGfd
    252983851U,	// VNEGhd
    252983851U,	// VNEGhq
    1260534315U,	// VNEGs16d
    1260534315U,	// VNEGs16q
    1260403243U,	// VNEGs32d
    1260403243U,	// VNEGs32q
    1260665387U,	// VNEGs8d
    1260665387U,	// VNEGs8q
    2400344126U,	// VNMLAD
    2400475198U,	// VNMLAH
    2400606270U,	// VNMLAS
    2400345295U,	// VNMLSD
    2400476367U,	// VNMLSH
    2400607439U,	// VNMLSS
    252869519U,	// VNMULD
    253000591U,	// VNMULH
    253131663U,	// VNMULS
    556021U,	// VORNd
    556021U,	// VORNq
    556173U,	// VORRd
    405699725U,	// VORRiv2i32
    405830797U,	// VORRiv4i16
    405699725U,	// VORRiv4i32
    405830797U,	// VORRiv8i16
    556173U,	// VORRq
    1243904735U,	// VPADALsv16i8
    1243642591U,	// VPADALsv2i32
    1243773663U,	// VPADALsv4i16
    1243642591U,	// VPADALsv4i32
    1243773663U,	// VPADALsv8i16
    1243904735U,	// VPADALsv8i8
    1244297951U,	// VPADALuv16i8
    1244035807U,	// VPADALuv2i32
    1244166879U,	// VPADALuv4i16
    1244035807U,	// VPADALuv4i32
    1244166879U,	// VPADALuv8i16
    1244297951U,	// VPADALuv8i8
    1260665627U,	// VPADDLsv16i8
    1260403483U,	// VPADDLsv2i32
    1260534555U,	// VPADDLsv4i16
    1260403483U,	// VPADDLsv4i32
    1260534555U,	// VPADDLsv8i16
    1260665627U,	// VPADDLsv8i8
    1261058843U,	// VPADDLuv16i8
    1260796699U,	// VPADDLuv2i32
    1260927771U,	// VPADDLuv4i16
    1260796699U,	// VPADDLuv4i32
    1260927771U,	// VPADDLuv8i16
    1261058843U,	// VPADDLuv8i8
    253131165U,	// VPADDf
    253000093U,	// VPADDh
    187726237U,	// VPADDi16
    187595165U,	// VPADDi32
    187857309U,	// VPADDi8
    253132330U,	// VPMAXf
    253001258U,	// VPMAXh
    186809898U,	// VPMAXs16
    186678826U,	// VPMAXs32
    186940970U,	// VPMAXs8
    187203114U,	// VPMAXu16
    187072042U,	// VPMAXu32
    187334186U,	// VPMAXu8
    253131722U,	// VPMINf
    253000650U,	// VPMINh
    186809290U,	// VPMINs16
    186678218U,	// VPMINs32
    186940362U,	// VPMINs8
    187202506U,	// VPMINu16
    187071434U,	// VPMINu32
    187333578U,	// VPMINu8
    1260666036U,	// VQABSv16i8
    1260403892U,	// VQABSv2i32
    1260534964U,	// VQABSv4i16
    1260403892U,	// VQABSv4i32
    1260534964U,	// VQABSv8i16
    1260666036U,	// VQABSv8i8
    186939811U,	// VQADDsv16i8
    191265187U,	// VQADDsv1i64
    186677667U,	// VQADDsv2i32
    191265187U,	// VQADDsv2i64
    186808739U,	// VQADDsv4i16
    186677667U,	// VQADDsv4i32
    186808739U,	// VQADDsv8i16
    186939811U,	// VQADDsv8i8
    187333027U,	// VQADDuv16i8
    191396259U,	// VQADDuv1i64
    187070883U,	// VQADDuv2i32
    191396259U,	// VQADDuv2i64
    187201955U,	// VQADDuv4i16
    187070883U,	// VQADDuv4i32
    187201955U,	// VQADDuv8i16
    187333027U,	// VQADDuv8i8
    169896678U,	// VQDMLALslv2i32
    170027750U,	// VQDMLALslv4i16
    169892582U,	// VQDMLALv2i64
    170023654U,	// VQDMLALv4i32
    169896823U,	// VQDMLSLslv2i32
    170027895U,	// VQDMLSLslv4i16
    169892727U,	// VQDMLSLv2i64
    170023799U,	// VQDMLSLv4i32
    186669654U,	// VQDMULHslv2i32
    186800726U,	// VQDMULHslv4i16
    186669654U,	// VQDMULHslv4i32
    186800726U,	// VQDMULHslv8i16
    186677846U,	// VQDMULHv2i32
    186808918U,	// VQDMULHv4i16
    186677846U,	// VQDMULHv4i32
    186808918U,	// VQDMULHv8i16
    186669912U,	// VQDMULLslv2i32
    186800984U,	// VQDMULLslv4i16
    186678104U,	// VQDMULLv2i64
    186809176U,	// VQDMULLv4i32
    1264991248U,	// VQMOVNsuv2i32
    1260403728U,	// VQMOVNsuv4i16
    1260534800U,	// VQMOVNsuv8i8
    1264991261U,	// VQMOVNsv2i32
    1260403741U,	// VQMOVNsv4i16
    1260534813U,	// VQMOVNsv8i8
    1265122333U,	// VQMOVNuv2i32
    1260796957U,	// VQMOVNuv4i16
    1260928029U,	// VQMOVNuv8i8
    1260665381U,	// VQNEGv16i8
    1260403237U,	// VQNEGv2i32
    1260534309U,	// VQNEGv4i16
    1260403237U,	// VQNEGv4i32
    1260534309U,	// VQNEGv8i16
    1260665381U,	// VQNEGv8i8
    169896504U,	// VQRDMLAHslv2i32
    170027576U,	// VQRDMLAHslv4i16
    169896504U,	// VQRDMLAHslv4i32
    170027576U,	// VQRDMLAHslv8i16
    169892408U,	// VQRDMLAHv2i32
    170023480U,	// VQRDMLAHv4i16
    169892408U,	// VQRDMLAHv4i32
    170023480U,	// VQRDMLAHv8i16
    169896561U,	// VQRDMLSHslv2i32
    170027633U,	// VQRDMLSHslv4i16
    169896561U,	// VQRDMLSHslv4i32
    170027633U,	// VQRDMLSHslv8i16
    169892465U,	// VQRDMLSHv2i32
    170023537U,	// VQRDMLSHv4i16
    169892465U,	// VQRDMLSHv4i32
    170023537U,	// VQRDMLSHv8i16
    186669662U,	// VQRDMULHslv2i32
    186800734U,	// VQRDMULHslv4i16
    186669662U,	// VQRDMULHslv4i32
    186800734U,	// VQRDMULHslv8i16
    186677854U,	// VQRDMULHv2i32
    186808926U,	// VQRDMULHv4i16
    186677854U,	// VQRDMULHv4i32
    186808926U,	// VQRDMULHv8i16
    186940210U,	// VQRSHLsv16i8
    191265586U,	// VQRSHLsv1i64
    186678066U,	// VQRSHLsv2i32
    191265586U,	// VQRSHLsv2i64
    186809138U,	// VQRSHLsv4i16
    186678066U,	// VQRSHLsv4i32
    186809138U,	// VQRSHLsv8i16
    186940210U,	// VQRSHLsv8i8
    187333426U,	// VQRSHLuv16i8
    191396658U,	// VQRSHLuv1i64
    187071282U,	// VQRSHLuv2i32
    191396658U,	// VQRSHLuv2i64
    187202354U,	// VQRSHLuv4i16
    187071282U,	// VQRSHLuv4i32
    187202354U,	// VQRSHLuv8i16
    187333426U,	// VQRSHLuv8i8
    191265760U,	// VQRSHRNsv2i32
    186678240U,	// VQRSHRNsv4i16
    186809312U,	// VQRSHRNsv8i8
    191396832U,	// VQRSHRNuv2i32
    187071456U,	// VQRSHRNuv4i16
    187202528U,	// VQRSHRNuv8i8
    191265799U,	// VQRSHRUNv2i32
    186678279U,	// VQRSHRUNv4i16
    186809351U,	// VQRSHRUNv8i8
    186940204U,	// VQSHLsiv16i8
    191265580U,	// VQSHLsiv1i64
    186678060U,	// VQSHLsiv2i32
    191265580U,	// VQSHLsiv2i64
    186809132U,	// VQSHLsiv4i16
    186678060U,	// VQSHLsiv4i32
    186809132U,	// VQSHLsiv8i16
    186940204U,	// VQSHLsiv8i8
    186940901U,	// VQSHLsuv16i8
    191266277U,	// VQSHLsuv1i64
    186678757U,	// VQSHLsuv2i32
    191266277U,	// VQSHLsuv2i64
    186809829U,	// VQSHLsuv4i16
    186678757U,	// VQSHLsuv4i32
    186809829U,	// VQSHLsuv8i16
    186940901U,	// VQSHLsuv8i8
    186940204U,	// VQSHLsv16i8
    191265580U,	// VQSHLsv1i64
    186678060U,	// VQSHLsv2i32
    191265580U,	// VQSHLsv2i64
    186809132U,	// VQSHLsv4i16
    186678060U,	// VQSHLsv4i32
    186809132U,	// VQSHLsv8i16
    186940204U,	// VQSHLsv8i8
    187333420U,	// VQSHLuiv16i8
    191396652U,	// VQSHLuiv1i64
    187071276U,	// VQSHLuiv2i32
    191396652U,	// VQSHLuiv2i64
    187202348U,	// VQSHLuiv4i16
    187071276U,	// VQSHLuiv4i32
    187202348U,	// VQSHLuiv8i16
    187333420U,	// VQSHLuiv8i8
    187333420U,	// VQSHLuv16i8
    191396652U,	// VQSHLuv1i64
    187071276U,	// VQSHLuv2i32
    191396652U,	// VQSHLuv2i64
    187202348U,	// VQSHLuv4i16
    187071276U,	// VQSHLuv4i32
    187202348U,	// VQSHLuv8i16
    187333420U,	// VQSHLuv8i8
    191265753U,	// VQSHRNsv2i32
    186678233U,	// VQSHRNsv4i16
    186809305U,	// VQSHRNsv8i8
    191396825U,	// VQSHRNuv2i32
    187071449U,	// VQSHRNuv4i16
    187202521U,	// VQSHRNuv8i8
    191265791U,	// VQSHRUNv2i32
    186678271U,	// VQSHRUNv4i16
    186809343U,	// VQSHRUNv8i8
    186939670U,	// VQSUBsv16i8
    191265046U,	// VQSUBsv1i64
    186677526U,	// VQSUBsv2i32
    191265046U,	// VQSUBsv2i64
    186808598U,	// VQSUBsv4i16
    186677526U,	// VQSUBsv4i32
    186808598U,	// VQSUBsv8i16
    186939670U,	// VQSUBsv8i8
    187332886U,	// VQSUBuv16i8
    191396118U,	// VQSUBuv1i64
    187070742U,	// VQSUBuv2i32
    191396118U,	// VQSUBuv2i64
    187201814U,	// VQSUBuv4i16
    187070742U,	// VQSUBuv4i32
    187201814U,	// VQSUBuv8i16
    187332886U,	// VQSUBuv8i8
    187464635U,	// VRADDHNv2i32
    187595707U,	// VRADDHNv4i16
    187726779U,	// VRADDHNv8i8
    1260796423U,	// VRECPEd
    253114887U,	// VRECPEfd
    253114887U,	// VRECPEfq
    252983815U,	// VRECPEhd
    252983815U,	// VRECPEhq
    1260796423U,	// VRECPEq
    253132016U,	// VRECPSfd
    253132016U,	// VRECPSfq
    253000944U,	// VRECPShd
    253000944U,	// VRECPShq
    407401U,	// VREV16d8
    407401U,	// VREV16q8
    145044U,	// VREV32d16
    407188U,	// VREV32d8
    145044U,	// VREV32q16
    407188U,	// VREV32q8
    145120U,	// VREV64d16
    276192U,	// VREV64d32
    407264U,	// VREV64d8
    145120U,	// VREV64q16
    276192U,	// VREV64q32
    407264U,	// VREV64q8
    186939792U,	// VRHADDsv16i8
    186677648U,	// VRHADDsv2i32
    186808720U,	// VRHADDsv4i16
    186677648U,	// VRHADDsv4i32
    186808720U,	// VRHADDsv8i16
    186939792U,	// VRHADDsv8i8
    187333008U,	// VRHADDuv16i8
    187070864U,	// VRHADDuv2i32
    187201936U,	// VRHADDuv4i16
    187070864U,	// VRHADDuv4i32
    187201936U,	// VRHADDuv8i16
    187333008U,	// VRHADDuv8i8
    1258988088U,	// VRINTAD
    1258988470U,	// VRINTAH
    1258987769U,	// VRINTANDf
    1258988470U,	// VRINTANDh
    1258987769U,	// VRINTANQf
    1258988470U,	// VRINTANQh
    1258987769U,	// VRINTAS
    1258988136U,	// VRINTMD
    1258988551U,	// VRINTMH
    1258987828U,	// VRINTMNDf
    1258988551U,	// VRINTMNDh
    1258987828U,	// VRINTMNQf
    1258988551U,	// VRINTMNQh
    1258987828U,	// VRINTMS
    1258988148U,	// VRINTND
    1258988563U,	// VRINTNH
    1258987840U,	// VRINTNNDf
    1258988563U,	// VRINTNNDh
    1258987840U,	// VRINTNNQf
    1258988563U,	// VRINTNNQh
    1258987840U,	// VRINTNS
    1258988160U,	// VRINTPD
    1258988575U,	// VRINTPH
    1258987852U,	// VRINTPNDf
    1258988575U,	// VRINTPNDh
    1258987852U,	// VRINTPNQf
    1258988575U,	// VRINTPNQh
    1258987852U,	// VRINTPS
    252853410U,	// VRINTRD
    252984482U,	// VRINTRH
    253115554U,	// VRINTRS
    252853982U,	// VRINTXD
    252985054U,	// VRINTXH
    1258987900U,	// VRINTXNDf
    1258988633U,	// VRINTXNDh
    1258987900U,	// VRINTXNQf
    1258988633U,	// VRINTXNQh
    253116126U,	// VRINTXS
    252853994U,	// VRINTZD
    252985066U,	// VRINTZH
    1258987912U,	// VRINTZNDf
    1258988656U,	// VRINTZNDh
    1258987912U,	// VRINTZNQf
    1258988656U,	// VRINTZNQh
    253116138U,	// VRINTZS
    186940217U,	// VRSHLsv16i8
    191265593U,	// VRSHLsv1i64
    186678073U,	// VRSHLsv2i32
    191265593U,	// VRSHLsv2i64
    186809145U,	// VRSHLsv4i16
    186678073U,	// VRSHLsv4i32
    186809145U,	// VRSHLsv8i16
    186940217U,	// VRSHLsv8i8
    187333433U,	// VRSHLuv16i8
    191396665U,	// VRSHLuv1i64
    187071289U,	// VRSHLuv2i32
    191396665U,	// VRSHLuv2i64
    187202361U,	// VRSHLuv4i16
    187071289U,	// VRSHLuv4i32
    187202361U,	// VRSHLuv8i16
    187333433U,	// VRSHLuv8i8
    187464680U,	// VRSHRNv2i32
    187595752U,	// VRSHRNv4i16
    187726824U,	// VRSHRNv8i8
    186940525U,	// VRSHRsv16i8
    191265901U,	// VRSHRsv1i64
    186678381U,	// VRSHRsv2i32
    191265901U,	// VRSHRsv2i64
    186809453U,	// VRSHRsv4i16
    186678381U,	// VRSHRsv4i32
    186809453U,	// VRSHRsv8i16
    186940525U,	// VRSHRsv8i8
    187333741U,	// VRSHRuv16i8
    191396973U,	// VRSHRuv1i64
    187071597U,	// VRSHRuv2i32
    191396973U,	// VRSHRuv2i64
    187202669U,	// VRSHRuv4i16
    187071597U,	// VRSHRuv4i32
    187202669U,	// VRSHRuv8i16
    187333741U,	// VRSHRuv8i8
    1260796436U,	// VRSQRTEd
    253114900U,	// VRSQRTEfd
    253114900U,	// VRSQRTEfq
    252983828U,	// VRSQRTEhd
    252983828U,	// VRSQRTEhq
    1260796436U,	// VRSQRTEq
    253132038U,	// VRSQRTSfd
    253132038U,	// VRSQRTSfq
    253000966U,	// VRSQRTShd
    253000966U,	// VRSQRTShq
    170154068U,	// VRSRAsv16i8
    174479444U,	// VRSRAsv1i64
    169891924U,	// VRSRAsv2i32
    174479444U,	// VRSRAsv2i64
    170022996U,	// VRSRAsv4i16
    169891924U,	// VRSRAsv4i32
    170022996U,	// VRSRAsv8i16
    170154068U,	// VRSRAsv8i8
    170547284U,	// VRSRAuv16i8
    174610516U,	// VRSRAuv1i64
    170285140U,	// VRSRAuv2i32
    174610516U,	// VRSRAuv2i64
    170416212U,	// VRSRAuv4i16
    170285140U,	// VRSRAuv4i32
    170416212U,	// VRSRAuv8i16
    170547284U,	// VRSRAuv8i8
    187464620U,	// VRSUBHNv2i32
    187595692U,	// VRSUBHNv4i16
    187726764U,	// VRSUBHNv8i8
    910495U,	// VSDOTD
    7070879U,	// VSDOTDI
    910495U,	// VSDOTQ
    7070879U,	// VSDOTQI
    185246348U,	// VSELEQD
    185246763U,	// VSELEQH
    185246040U,	// VSELEQS
    185246276U,	// VSELGED
    185246669U,	// VSELGEH
    185245968U,	// VSELGES
    185246372U,	// VSELGTD
    185246797U,	// VSELGTH
    185246064U,	// VSELGTS
    185246360U,	// VSELVSD
    185246785U,	// VSELVSH
    185246052U,	// VSELVSS
    3221380607U,	// VSETLNi16
    3221511679U,	// VSETLNi32
    3221642751U,	// VSETLNi8
    187726674U,	// VSHLLi16
    187595602U,	// VSHLLi32
    187857746U,	// VSHLLi8
    186678098U,	// VSHLLsv2i64
    186809170U,	// VSHLLsv4i32
    186940242U,	// VSHLLsv8i16
    187071314U,	// VSHLLuv2i64
    187202386U,	// VSHLLuv4i32
    187333458U,	// VSHLLuv8i16
    187857727U,	// VSHLiv16i8
    187464511U,	// VSHLiv1i64
    187595583U,	// VSHLiv2i32
    187464511U,	// VSHLiv2i64
    187726655U,	// VSHLiv4i16
    187595583U,	// VSHLiv4i32
    187726655U,	// VSHLiv8i16
    187857727U,	// VSHLiv8i8
    186940223U,	// VSHLsv16i8
    191265599U,	// VSHLsv1i64
    186678079U,	// VSHLsv2i32
    191265599U,	// VSHLsv2i64
    186809151U,	// VSHLsv4i16
    186678079U,	// VSHLsv4i32
    186809151U,	// VSHLsv8i16
    186940223U,	// VSHLsv8i8
    187333439U,	// VSHLuv16i8
    191396671U,	// VSHLuv1i64
    187071295U,	// VSHLuv2i32
    191396671U,	// VSHLuv2i64
    187202367U,	// VSHLuv4i16
    187071295U,	// VSHLuv4i32
    187202367U,	// VSHLuv8i16
    187333439U,	// VSHLuv8i8
    187464687U,	// VSHRNv2i32
    187595759U,	// VSHRNv4i16
    187726831U,	// VSHRNv8i8
    186940531U,	// VSHRsv16i8
    191265907U,	// VSHRsv1i64
    186678387U,	// VSHRsv2i32
    191265907U,	// VSHRsv2i64
    186809459U,	// VSHRsv4i16
    186678387U,	// VSHRsv4i32
    186809459U,	// VSHRsv8i16
    186940531U,	// VSHRsv8i8
    187333747U,	// VSHRuv16i8
    191396979U,	// VSHRuv1i64
    187071603U,	// VSHRuv2i32
    191396979U,	// VSHRuv2i64
    187202675U,	// VSHRuv4i16
    187071603U,	// VSHRuv4i32
    187202675U,	// VSHRuv8i16
    187333747U,	// VSHRuv8i8
    7110088U,	// VSHTOD
    256540104U,	// VSHTOH
    7241160U,	// VSHTOS
    443563464U,	// VSITOD
    443694536U,	// VSITOH
    440942024U,	// VSITOS
    416441U,	// VSLIv16i8
    5266105U,	// VSLIv1i64
    285369U,	// VSLIv2i32
    5266105U,	// VSLIv2i64
    154297U,	// VSLIv4i16
    285369U,	// VSLIv4i32
    154297U,	// VSLIv8i16
    416441U,	// VSLIv8i8
    1332772296U,	// VSLTOD
    1332903368U,	// VSLTOH
    1330150856U,	// VSLTOS
    252853650U,	// VSQRTD
    252984722U,	// VSQRTH
    253115794U,	// VSQRTS
    170154074U,	// VSRAsv16i8
    174479450U,	// VSRAsv1i64
    169891930U,	// VSRAsv2i32
    174479450U,	// VSRAsv2i64
    170023002U,	// VSRAsv4i16
    169891930U,	// VSRAsv4i32
    170023002U,	// VSRAsv8i16
    170154074U,	// VSRAsv8i8
    170547290U,	// VSRAuv16i8
    174610522U,	// VSRAuv1i64
    170285146U,	// VSRAuv2i32
    174610522U,	// VSRAuv2i64
    170416218U,	// VSRAuv4i16
    170285146U,	// VSRAuv4i32
    170416218U,	// VSRAuv8i16
    170547290U,	// VSRAuv8i8
    416446U,	// VSRIv16i8
    5266110U,	// VSRIv1i64
    285374U,	// VSRIv2i32
    5266110U,	// VSRIv2i64
    154302U,	// VSRIv4i16
    285374U,	// VSRIv4i32
    154302U,	// VSRIv8i16
    416446U,	// VSRIv8i8
    1247041167U,	// VST1LNd16
    1632949903U,	// VST1LNd16_UPD
    1247172239U,	// VST1LNd32
    1633080975U,	// VST1LNd32_UPD
    1247303311U,	// VST1LNd8
    1633212047U,	// VST1LNd8_UPD
    0U,	// VST1LNq16Pseudo
    0U,	// VST1LNq16Pseudo_UPD
    0U,	// VST1LNq32Pseudo
    0U,	// VST1LNq32Pseudo_UPD
    0U,	// VST1LNq8Pseudo
    0U,	// VST1LNq8Pseudo_UPD
    570586767U,	// VST1d16
    587363983U,	// VST1d16Q
    0U,	// VST1d16QPseudo
    604133007U,	// VST1d16Qwb_fixed
    620914319U,	// VST1d16Qwb_register
    637695631U,	// VST1d16T
    0U,	// VST1d16TPseudo
    654464655U,	// VST1d16Twb_fixed
    671245967U,	// VST1d16Twb_register
    688019087U,	// VST1d16wb_fixed
    704800399U,	// VST1d16wb_register
    570717839U,	// VST1d32
    587495055U,	// VST1d32Q
    0U,	// VST1d32QPseudo
    604264079U,	// VST1d32Qwb_fixed
    621045391U,	// VST1d32Qwb_register
    637826703U,	// VST1d32T
    0U,	// VST1d32TPseudo
    654595727U,	// VST1d32Twb_fixed
    671377039U,	// VST1d32Twb_register
    688150159U,	// VST1d32wb_fixed
    704931471U,	// VST1d32wb_register
    575698575U,	// VST1d64
    592475791U,	// VST1d64Q
    0U,	// VST1d64QPseudo
    0U,	// VST1d64QPseudoWB_fixed
    0U,	// VST1d64QPseudoWB_register
    609244815U,	// VST1d64Qwb_fixed
    626026127U,	// VST1d64Qwb_register
    642807439U,	// VST1d64T
    0U,	// VST1d64TPseudo
    0U,	// VST1d64TPseudoWB_fixed
    0U,	// VST1d64TPseudoWB_register
    659576463U,	// VST1d64Twb_fixed
    676357775U,	// VST1d64Twb_register
    693130895U,	// VST1d64wb_fixed
    709912207U,	// VST1d64wb_register
    570848911U,	// VST1d8
    587626127U,	// VST1d8Q
    0U,	// VST1d8QPseudo
    604395151U,	// VST1d8Qwb_fixed
    621176463U,	// VST1d8Qwb_register
    637957775U,	// VST1d8T
    0U,	// VST1d8TPseudo
    654726799U,	// VST1d8Twb_fixed
    671508111U,	// VST1d8Twb_register
    688281231U,	// VST1d8wb_fixed
    705062543U,	// VST1d8wb_register
    721581711U,	// VST1q16
    0U,	// VST1q16HighQPseudo
    0U,	// VST1q16HighTPseudo
    0U,	// VST1q16LowQPseudo_UPD
    0U,	// VST1q16LowTPseudo_UPD
    738350735U,	// VST1q16wb_fixed
    755132047U,	// VST1q16wb_register
    721712783U,	// VST1q32
    0U,	// VST1q32HighQPseudo
    0U,	// VST1q32HighTPseudo
    0U,	// VST1q32LowQPseudo_UPD
    0U,	// VST1q32LowTPseudo_UPD
    738481807U,	// VST1q32wb_fixed
    755263119U,	// VST1q32wb_register
    726693519U,	// VST1q64
    0U,	// VST1q64HighQPseudo
    0U,	// VST1q64HighTPseudo
    0U,	// VST1q64LowQPseudo_UPD
    0U,	// VST1q64LowTPseudo_UPD
    743462543U,	// VST1q64wb_fixed
    760243855U,	// VST1q64wb_register
    721843855U,	// VST1q8
    0U,	// VST1q8HighQPseudo
    0U,	// VST1q8HighTPseudo
    0U,	// VST1q8LowQPseudo_UPD
    0U,	// VST1q8LowTPseudo_UPD
    738612879U,	// VST1q8wb_fixed
    755394191U,	// VST1q8wb_register
    1247045323U,	// VST2LNd16
    0U,	// VST2LNd16Pseudo
    0U,	// VST2LNd16Pseudo_UPD
    1632999115U,	// VST2LNd16_UPD
    1247176395U,	// VST2LNd32
    0U,	// VST2LNd32Pseudo
    0U,	// VST2LNd32Pseudo_UPD
    1633130187U,	// VST2LNd32_UPD
    1247307467U,	// VST2LNd8
    0U,	// VST2LNd8Pseudo
    0U,	// VST2LNd8Pseudo_UPD
    1633261259U,	// VST2LNd8_UPD
    1247045323U,	// VST2LNq16
    0U,	// VST2LNq16Pseudo
    0U,	// VST2LNq16Pseudo_UPD
    1632999115U,	// VST2LNq16_UPD
    1247176395U,	// VST2LNq32
    0U,	// VST2LNq32Pseudo
    0U,	// VST2LNq32Pseudo_UPD
    1633130187U,	// VST2LNq32_UPD
    771913419U,	// VST2b16
    788682443U,	// VST2b16wb_fixed
    805463755U,	// VST2b16wb_register
    772044491U,	// VST2b32
    788813515U,	// VST2b32wb_fixed
    805594827U,	// VST2b32wb_register
    772175563U,	// VST2b8
    788944587U,	// VST2b8wb_fixed
    805725899U,	// VST2b8wb_register
    721581771U,	// VST2d16
    738350795U,	// VST2d16wb_fixed
    755132107U,	// VST2d16wb_register
    721712843U,	// VST2d32
    738481867U,	// VST2d32wb_fixed
    755263179U,	// VST2d32wb_register
    721843915U,	// VST2d8
    738612939U,	// VST2d8wb_fixed
    755394251U,	// VST2d8wb_register
    587364043U,	// VST2q16
    0U,	// VST2q16Pseudo
    0U,	// VST2q16PseudoWB_fixed
    0U,	// VST2q16PseudoWB_register
    604133067U,	// VST2q16wb_fixed
    620914379U,	// VST2q16wb_register
    587495115U,	// VST2q32
    0U,	// VST2q32Pseudo
    0U,	// VST2q32PseudoWB_fixed
    0U,	// VST2q32PseudoWB_register
    604264139U,	// VST2q32wb_fixed
    621045451U,	// VST2q32wb_register
    587626187U,	// VST2q8
    0U,	// VST2q8Pseudo
    0U,	// VST2q8PseudoWB_fixed
    0U,	// VST2q8PseudoWB_register
    604395211U,	// VST2q8wb_fixed
    621176523U,	// VST2q8wb_register
    1247074011U,	// VST3LNd16
    0U,	// VST3LNd16Pseudo
    0U,	// VST3LNd16Pseudo_UPD
    1633011419U,	// VST3LNd16_UPD
    1247205083U,	// VST3LNd32
    0U,	// VST3LNd32Pseudo
    0U,	// VST3LNd32Pseudo_UPD
    1633142491U,	// VST3LNd32_UPD
    1247336155U,	// VST3LNd8
    0U,	// VST3LNd8Pseudo
    0U,	// VST3LNd8Pseudo_UPD
    1633273563U,	// VST3LNd8_UPD
    1247074011U,	// VST3LNq16
    0U,	// VST3LNq16Pseudo
    0U,	// VST3LNq16Pseudo_UPD
    1633011419U,	// VST3LNq16_UPD
    1247205083U,	// VST3LNq32
    0U,	// VST3LNq32Pseudo
    0U,	// VST3LNq32Pseudo_UPD
    1633142491U,	// VST3LNq32_UPD
    173303515U,	// VST3d16
    0U,	// VST3d16Pseudo
    0U,	// VST3d16Pseudo_UPD
    559257307U,	// VST3d16_UPD
    173434587U,	// VST3d32
    0U,	// VST3d32Pseudo
    0U,	// VST3d32Pseudo_UPD
    559388379U,	// VST3d32_UPD
    173565659U,	// VST3d8
    0U,	// VST3d8Pseudo
    0U,	// VST3d8Pseudo_UPD
    559519451U,	// VST3d8_UPD
    173303515U,	// VST3q16
    0U,	// VST3q16Pseudo_UPD
    559257307U,	// VST3q16_UPD
    0U,	// VST3q16oddPseudo
    0U,	// VST3q16oddPseudo_UPD
    173434587U,	// VST3q32
    0U,	// VST3q32Pseudo_UPD
    559388379U,	// VST3q32_UPD
    0U,	// VST3q32oddPseudo
    0U,	// VST3q32oddPseudo_UPD
    173565659U,	// VST3q8
    0U,	// VST3q8Pseudo_UPD
    559519451U,	// VST3q8_UPD
    0U,	// VST3q8oddPseudo
    0U,	// VST3q8oddPseudo_UPD
    1247123180U,	// VST4LNd16
    0U,	// VST4LNd16Pseudo
    0U,	// VST4LNd16Pseudo_UPD
    1633003244U,	// VST4LNd16_UPD
    1247254252U,	// VST4LNd32
    0U,	// VST4LNd32Pseudo
    0U,	// VST4LNd32Pseudo_UPD
    1633134316U,	// VST4LNd32_UPD
    1247385324U,	// VST4LNd8
    0U,	// VST4LNd8Pseudo
    0U,	// VST4LNd8Pseudo_UPD
    1633265388U,	// VST4LNd8_UPD
    1247123180U,	// VST4LNq16
    0U,	// VST4LNq16Pseudo
    0U,	// VST4LNq16Pseudo_UPD
    1633003244U,	// VST4LNq16_UPD
    1247254252U,	// VST4LNq32
    0U,	// VST4LNq32Pseudo
    0U,	// VST4LNq32Pseudo_UPD
    1633134316U,	// VST4LNq32_UPD
    173332204U,	// VST4d16
    0U,	// VST4d16Pseudo
    0U,	// VST4d16Pseudo_UPD
    559269612U,	// VST4d16_UPD
    173463276U,	// VST4d32
    0U,	// VST4d32Pseudo
    0U,	// VST4d32Pseudo_UPD
    559400684U,	// VST4d32_UPD
    173594348U,	// VST4d8
    0U,	// VST4d8Pseudo
    0U,	// VST4d8Pseudo_UPD
    559531756U,	// VST4d8_UPD
    173332204U,	// VST4q16
    0U,	// VST4q16Pseudo_UPD
    559269612U,	// VST4q16_UPD
    0U,	// VST4q16oddPseudo
    0U,	// VST4q16oddPseudo_UPD
    173463276U,	// VST4q32
    0U,	// VST4q32Pseudo_UPD
    559400684U,	// VST4q32_UPD
    0U,	// VST4q32oddPseudo
    0U,	// VST4q32oddPseudo_UPD
    173594348U,	// VST4q8
    0U,	// VST4q8Pseudo_UPD
    559531756U,	// VST4q8_UPD
    0U,	// VST4q8oddPseudo
    0U,	// VST4q8oddPseudo_UPD
    2332571803U,	// VSTMDDB_UPD
    571435U,	// VSTMDIA
    2332571691U,	// VSTMDIA_UPD
    0U,	// VSTMQIA
    2332571803U,	// VSTMSDB_UPD
    571435U,	// VSTMSIA
    2332571691U,	// VSTMSIA_UPD
    556201U,	// VSTRD
    162985U,	// VSTRH
    556201U,	// VSTRS
    252868892U,	// VSUBD
    252999964U,	// VSUBH
    187464628U,	// VSUBHNv2i32
    187595700U,	// VSUBHNv4i16
    187726772U,	// VSUBHNv8i8
    186678021U,	// VSUBLsv2i64
    186809093U,	// VSUBLsv4i32
    186940165U,	// VSUBLsv8i16
    187071237U,	// VSUBLuv2i64
    187202309U,	// VSUBLuv4i32
    187333381U,	// VSUBLuv8i16
    253131036U,	// VSUBS
    186678788U,	// VSUBWsv2i64
    186809860U,	// VSUBWsv4i32
    186940932U,	// VSUBWsv8i16
    187072004U,	// VSUBWuv2i64
    187203076U,	// VSUBWuv4i32
    187334148U,	// VSUBWuv8i16
    253131036U,	// VSUBfd
    253131036U,	// VSUBfq
    252999964U,	// VSUBhd
    252999964U,	// VSUBhq
    187857180U,	// VSUBv16i8
    187463964U,	// VSUBv1i64
    187595036U,	// VSUBv2i32
    187463964U,	// VSUBv2i64
    187726108U,	// VSUBv4i16
    187595036U,	// VSUBv4i32
    187726108U,	// VSUBv8i16
    187857180U,	// VSUBv8i8
    547910U,	// VSWPd
    547910U,	// VSWPq
    424704U,	// VTBL1
    424704U,	// VTBL2
    424704U,	// VTBL3
    0U,	// VTBL3Pseudo
    424704U,	// VTBL4
    0U,	// VTBL4Pseudo
    417377U,	// VTBX1
    417377U,	// VTBX2
    417377U,	// VTBX3
    0U,	// VTBX3Pseudo
    417377U,	// VTBX4
    0U,	// VTBX4Pseudo
    7634376U,	// VTOSHD
    256146888U,	// VTOSHH
    7765448U,	// VTOSHS
    441597102U,	// VTOSIRD
    444087470U,	// VTOSIRH
    440417454U,	// VTOSIRS
    441597384U,	// VTOSIZD
    444087752U,	// VTOSIZH
    440417736U,	// VTOSIZS
    1330806216U,	// VTOSLD
    1333296584U,	// VTOSLH
    1329626568U,	// VTOSLS
    8027592U,	// VTOUHD
    256277960U,	// VTOUHH
    8158664U,	// VTOUHS
    444480686U,	// VTOUIRD
    444611758U,	// VTOUIRH
    440548526U,	// VTOUIRS
    444480968U,	// VTOUIZD
    444612040U,	// VTOUIZH
    440548808U,	// VTOUIZS
    1333689800U,	// VTOULD
    1333820872U,	// VTOULH
    1329757640U,	// VTOULS
    154618U,	// VTRNd16
    285690U,	// VTRNd32
    416762U,	// VTRNd8
    154618U,	// VTRNq16
    285690U,	// VTRNq32
    416762U,	// VTRNq8
    425373U,	// VTSTv16i8
    294301U,	// VTSTv2i32
    163229U,	// VTSTv4i16
    294301U,	// VTSTv4i32
    163229U,	// VTSTv8i16
    425373U,	// VTSTv8i8
    910505U,	// VUDOTD
    7070889U,	// VUDOTDI
    910505U,	// VUDOTQ
    7070889U,	// VUDOTQI
    8551880U,	// VUHTOD
    256802248U,	// VUHTOH
    8682952U,	// VUHTOS
    445005256U,	// VUITOD
    445136328U,	// VUITOH
    441204168U,	// VUITOS
    1334214088U,	// VULTOD
    1334345160U,	// VULTOH
    1330413000U,	// VULTOS
    154699U,	// VUZPd16
    416843U,	// VUZPd8
    154699U,	// VUZPq16
    285771U,	// VUZPq32
    416843U,	// VUZPq8
    154675U,	// VZIPd16
    416819U,	// VZIPd8
    154675U,	// VZIPq16
    285747U,	// VZIPq32
    416819U,	// VZIPq8
    571410U,	// sysLDMDA
    2332571666U,	// sysLDMDA_UPD
    571541U,	// sysLDMDB
    2332571797U,	// sysLDMDB_UPD
    572322U,	// sysLDMIA
    2332572578U,	// sysLDMIA_UPD
    571560U,	// sysLDMIB
    2332571816U,	// sysLDMIB_UPD
    571416U,	// sysSTMDA
    2332571672U,	// sysSTMDA_UPD
    571548U,	// sysSTMDB
    2332571804U,	// sysSTMDB_UPD
    572328U,	// sysSTMIA
    2332572584U,	// sysSTMIA_UPD
    571566U,	// sysSTMIB
    2332571822U,	// sysSTMIB_UPD
    530767U,	// t2ADCri
    9050447U,	// t2ADCrr
    9079119U,	// t2ADCrs
    9050508U,	// t2ADDri
    556555U,	// t2ADDri12
    9050508U,	// t2ADDrr
    9079180U,	// t2ADDrs
    9059428U,	// t2ADR
    530881U,	// t2ANDri
    9050561U,	// t2ANDrr
    9079233U,	// t2ANDrs
    9051282U,	// t2ASRri
    9051282U,	// t2ASRrr
    1082832998U,	// t2B
    555351U,	// t2BFC
    547505U,	// t2BFI
    530780U,	// t2BICri
    9050460U,	// t2BICrr
    9079132U,	// t2BICrs
    1074313923U,	// t2BXJ
    1082832998U,	// t2Bcc
    201907247U,	// t2CDP
    201905845U,	// t2CDP2
    839332U,	// t2CLREX
    540390U,	// t2CLZ
    9059285U,	// t2CMNri
    9059285U,	// t2CMNzrr
    9075669U,	// t2CMNzrs
    9059385U,	// t2CMPri
    9059385U,	// t2CMPrr
    9075769U,	// t2CMPrs
    828731U,	// t2CPS1p
    1317731571U,	// t2CPS2p
    235470067U,	// t2CPS3p
    185246913U,	// t2CRC32B
    185246921U,	// t2CRC32CB
    185246995U,	// t2CRC32CH
    185247079U,	// t2CRC32CW
    185246987U,	// t2CRC32H
    185247071U,	// t2CRC32W
    1074313761U,	// t2DBG
    837257U,	// t2DCPS1
    837317U,	// t2DCPS2
    837333U,	// t2DCPS3
    822655161U,	// t2DMB
    822655180U,	// t2DSB
    531584U,	// t2EORri
    9051264U,	// t2EORrr
    9079936U,	// t2EORrs
    1082834312U,	// t2HINT
    828753U,	// t2HVC
    839432400U,	// t2ISB
    17313142U,	// t2IT
    0U,	// t2Int_eh_sjlj_setjmp
    0U,	// t2Int_eh_sjlj_setjmp_nofp
    538638U,	// t2LDA
    538723U,	// t2LDAB
    540306U,	// t2LDAEX
    538927U,	// t2LDAEXB
    555483U,	// t2LDAEXD
    539285U,	// t2LDAEXH
    539187U,	// t2LDAH
    1275615943U,	// t2LDC2L_OFFSET
    1275615943U,	// t2LDC2L_OPTION
    1275615943U,	// t2LDC2L_POST
    1275615943U,	// t2LDC2L_PRE
    1275614875U,	// t2LDC2_OFFSET
    1275614875U,	// t2LDC2_OPTION
    1275614875U,	// t2LDC2_POST
    1275614875U,	// t2LDC2_PRE
    1275616011U,	// t2LDCL_OFFSET
    1275616011U,	// t2LDCL_OPTION
    1275616011U,	// t2LDCL_POST
    1275616011U,	// t2LDCL_PRE
    1275615571U,	// t2LDC_OFFSET
    1275615571U,	// t2LDC_OPTION
    1275615571U,	// t2LDC_POST
    1275615571U,	// t2LDC_PRE
    571541U,	// t2LDMDB
    2332571797U,	// t2LDMDB_UPD
    9092002U,	// t2LDMIA
    2341092258U,	// t2LDMIA_UPD
    556350U,	// t2LDRBT
    547010U,	// t2LDRB_POST
    547010U,	// t2LDRB_PRE
    9074882U,	// t2LDRBi12
    555202U,	// t2LDRBi8
    9058498U,	// t2LDRBpci
    9066690U,	// t2LDRBs
    551365U,	// t2LDRD_POST
    551365U,	// t2LDRD_PRE
    547269U,	// t2LDRDi8
    556702U,	// t2LDREX
    538941U,	// t2LDREXB
    555497U,	// t2LDREXD
    539299U,	// t2LDREXH
    556385U,	// t2LDRHT
    547431U,	// t2LDRH_POST
    547431U,	// t2LDRH_PRE
    9075303U,	// t2LDRHi12
    555623U,	// t2LDRHi8
    9058919U,	// t2LDRHpci
    9067111U,	// t2LDRHs
    556362U,	// t2LDRSBT
    547028U,	// t2LDRSB_POST
    547028U,	// t2LDRSB_PRE
    9074900U,	// t2LDRSBi12
    555220U,	// t2LDRSBi8
    9058516U,	// t2LDRSBpci
    9066708U,	// t2LDRSBs
    556397U,	// t2LDRSHT
    547450U,	// t2LDRSH_POST
    547450U,	// t2LDRSH_PRE
    9075322U,	// t2LDRSHi12
    555642U,	// t2LDRSHi8
    9058938U,	// t2LDRSHpci
    9067130U,	// t2LDRSHs
    556429U,	// t2LDRT
    547945U,	// t2LDR_POST
    547945U,	// t2LDR_PRE
    9075817U,	// t2LDRi12
    556137U,	// t2LDRi8
    9059433U,	// t2LDRpci
    9067625U,	// t2LDRs
    9051003U,	// t2LSLri
    9051003U,	// t2LSLrr
    9051289U,	// t2LSRri
    9051289U,	// t2LSRrr
    201907296U,	// t2MCR
    201905850U,	// t2MCR2
    201878664U,	// t2MCRR
    201877183U,	// t2MCRR2
    546874U,	// t2MLA
    548043U,	// t2MLS
    556493U,	// t2MOVTi16
    9063936U,	// t2MOVi
    540181U,	// t2MOVi16
    9063936U,	// t2MOVr
    9059580U,	// t2MOVsra_flag
    9059585U,	// t2MOVsrl_flag
    336124260U,	// t2MRC
    336123552U,	// t2MRC2
    352872808U,	// t2MRRC
    352872101U,	// t2MRRC2
    2148056312U,	// t2MRS_AR
    539896U,	// t2MRS_M
    539896U,	// t2MRSbanked
    3221798136U,	// t2MRSsys_AR
    369638558U,	// t2MSR_AR
    369638558U,	// t2MSR_M
    386415774U,	// t2MSRbanked
    555915U,	// t2MUL
    543769U,	// t2MVNi
    9063449U,	// t2MVNr
    9051161U,	// t2MVNs
    531446U,	// t2ORNri
    531446U,	// t2ORNrr
    560118U,	// t2ORNrs
    531598U,	// t2ORRri
    9051278U,	// t2ORRrr
    9079950U,	// t2ORRrs
    548137U,	// t2PKHBT
    547045U,	// t2PKHTB
    856178192U,	// t2PLDWi12
    872955408U,	// t2PLDWi8
    889749008U,	// t2PLDWs
    856177077U,	// t2PLDi12
    872954293U,	// t2PLDi8
    906541493U,	// t2PLDpci
    889747893U,	// t2PLDs
    856177333U,	// t2PLIi12
    872954549U,	// t2PLIi8
    906541749U,	// t2PLIpci
    889748149U,	// t2PLIs
    555428U,	// t2QADD
    554822U,	// t2QADD16
    554925U,	// t2QADD8
    556751U,	// t2QASX
    555402U,	// t2QDADD
    555274U,	// t2QDSUB
    556610U,	// t2QSAX
    555287U,	// t2QSUB
    554784U,	// t2QSUB16
    554886U,	// t2QSUB8
    540020U,	// t2RBIT
    9059820U,	// t2REV
    9058154U,	// t2REV16
    9058949U,	// t2REVSH
    1074313358U,	// t2RFEDB
    2148055182U,	// t2RFEDBW
    *********6U,	// t2RFEIA
    2148055070U,	// t2RFEIAW
    9051268U,	// t2RORri
    9051268U,	// t2RORrr
    544446U,	// t2RRX
    9050326U,	// t2RSBri
    530646U,	// t2RSBrr
    559318U,	// t2RSBrs
    554829U,	// t2SADD16
    554931U,	// t2SADD8
    556756U,	// t2SASX
    2253U,	// t2SB
    530763U,	// t2SBCri
    9050443U,	// t2SBCrr
    9079115U,	// t2SBCrs
    548528U,	// t2SBFX
    556528U,	// t2SDIV
    555816U,	// t2SEL
    828723U,	// t2SETPAN
    838192U,	// t2SG
    554805U,	// t2SHADD16
    554910U,	// t2SHADD8
    556738U,	// t2SHASX
    556597U,	// t2SHSAX
    554767U,	// t2SHSUB16
    554871U,	// t2SHSUB8
    1074313568U,	// t2SMC
    546932U,	// t2SMLABB
    548130U,	// t2SMLABT
    547193U,	// t2SMLAD
    548454U,	// t2SMLADX
    580334U,	// t2SMLAL
    579707U,	// t2SMLALBB
    580911U,	// t2SMLALBT
    580014U,	// t2SMLALD
    581236U,	// t2SMLALDX
    579819U,	// t2SMLALTB
    581033U,	// t2SMLALTT
    547038U,	// t2SMLATB
    548258U,	// t2SMLATT
    547105U,	// t2SMLAWB
    548306U,	// t2SMLAWT
    547279U,	// t2SMLSD
    548484U,	// t2SMLSDX
    580025U,	// t2SMLSLD
    581244U,	// t2SMLSLDX
    546872U,	// t2SMMLA
    547929U,	// t2SMMLAR
    548041U,	// t2SMMLS
    547990U,	// t2SMMLSR
    555913U,	// t2SMMUL
    556152U,	// t2SMMULR
    555391U,	// t2SMUAD
    556653U,	// t2SMUADX
    555139U,	// t2SMULBB
    556343U,	// t2SMULBT
    547680U,	// t2SMULL
    555251U,	// t2SMULTB
    556465U,	// t2SMULTT
    555304U,	// t2SMULWB
    556505U,	// t2SMULWT
    555477U,	// t2SMUSD
    556683U,	// t2SMUSDX
    9222306U,	// t2SRSDB
    9353378U,	// t2SRSDB_UPD
    9222194U,	// t2SRSIA
    9353266U,	// t2SRSIA_UPD
    548115U,	// t2SSAT
    554843U,	// t2SSAT16
    556615U,	// t2SSAX
    554791U,	// t2SSUB16
    554892U,	// t2SSUB8
    1275615949U,	// t2STC2L_OFFSET
    1275615949U,	// t2STC2L_OPTION
    1275615949U,	// t2STC2L_POST
    1275615949U,	// t2STC2L_PRE
    1275614891U,	// t2STC2_OFFSET
    1275614891U,	// t2STC2_OPTION
    1275614891U,	// t2STC2_POST
    1275614891U,	// t2STC2_PRE
    1275616016U,	// t2STCL_OFFSET
    1275616016U,	// t2STCL_OPTION
    1275616016U,	// t2STCL_POST
    1275616016U,	// t2STCL_PRE
    1275615601U,	// t2STC_OFFSET
    1275615601U,	// t2STC_OPTION
    1275615601U,	// t2STC_POST
    1275615601U,	// t2STC_PRE
    539525U,	// t2STL
    538804U,	// t2STLB
    556696U,	// t2STLEX
    555318U,	// t2STLEXB
    547298U,	// t2STLEXD
    555676U,	// t2STLEXH
    539217U,	// t2STLH
    571548U,	// t2STMDB
    2332571804U,	// t2STMDB_UPD
    9092008U,	// t2STMIA
    2341092264U,	// t2STMIA_UPD
    556356U,	// t2STRBT
    185096391U,	// t2STRB_POST
    185096391U,	// t2STRB_PRE
    9074887U,	// t2STRBi12
    555207U,	// t2STRBi8
    9066695U,	// t2STRBs
    185100746U,	// t2STRD_POST
    185100746U,	// t2STRD_PRE
    547274U,	// t2STRDi8
    548522U,	// t2STREX
    555332U,	// t2STREXB
    547312U,	// t2STREXD
    555690U,	// t2STREXH
    556391U,	// t2STRHT
    185096812U,	// t2STRH_POST
    185096812U,	// t2STRH_PRE
    9075308U,	// t2STRHi12
    555628U,	// t2STRHi8
    9067116U,	// t2STRHs
    556440U,	// t2STRT
    185097386U,	// t2STR_POST
    185097386U,	// t2STR_PRE
    9075882U,	// t2STRi12
    556202U,	// t2STRi8
    9067690U,	// t2STRs
    9485503U,	// t2SUBS_PC_LR
    9050380U,	// t2SUBri
    556549U,	// t2SUBri12
    9050380U,	// t2SUBrr
    9079052U,	// t2SUBrs
    546920U,	// t2SXTAB
    546545U,	// t2SXTAB16
    547393U,	// t2SXTAH
    9074944U,	// t2SXTB
    554753U,	// t2SXTB16
    9075339U,	// t2SXTH
    923285642U,	// t2TBB
    940063309U,	// t2TBH
    9059413U,	// t2TEQri
    9059413U,	// t2TEQrr
    9075797U,	// t2TEQrs
    956872922U,	// t2TSB
    9059742U,	// t2TSTri
    9059742U,	// t2TSTrr
    9076126U,	// t2TSTrs
    540070U,	// t2TT
    538719U,	// t2TTA
    539933U,	// t2TTAT
    540088U,	// t2TTT
    554836U,	// t2UADD16
    554937U,	// t2UADD8
    556761U,	// t2UASX
    548533U,	// t2UBFX
    828760U,	// t2UDF
    556533U,	// t2UDIV
    554813U,	// t2UHADD16
    554917U,	// t2UHADD8
    556744U,	// t2UHASX
    556603U,	// t2UHSAX
    554775U,	// t2UHSUB16
    554878U,	// t2UHSUB8
    580307U,	// t2UMAAL
    580340U,	// t2UMLAL
    547686U,	// t2UMULL
    554821U,	// t2UQADD16
    554924U,	// t2UQADD8
    556750U,	// t2UQASX
    556609U,	// t2UQSAX
    554783U,	// t2UQSUB16
    554885U,	// t2UQSUB8
    554904U,	// t2USAD8
    546672U,	// t2USADA8
    548120U,	// t2USAT
    554850U,	// t2USAT16
    556620U,	// t2USAX
    554798U,	// t2USUB16
    554898U,	// t2USUB8
    546926U,	// t2UXTAB
    546553U,	// t2UXTAB16
    547399U,	// t2UXTAH
    9074949U,	// t2UXTB
    554760U,	// t2UXTB16
    9075344U,	// t2UXTH
    982776143U,	// tADC
    555404U,	// tADDhirr
    177469836U,	// tADDi3
    982776204U,	// tADDi8
    555404U,	// tADDrSP
    555404U,	// tADDrSPi
    177469836U,	// tADDrr
    555404U,	// tADDspi
    555404U,	// tADDspr
    539748U,	// tADR
    982776257U,	// tAND
    177470610U,	// tASRri
    982776978U,	// tASRrr
    1074313318U,	// tB
    982776156U,	// tBIC
    828747U,	// tBKPT
    1242090242U,	// tBL
    1242090730U,	// tBLXNSr
    1242091194U,	// tBLXi
    1242091194U,	// tBLXr
    1074314838U,	// tBX
    1074314469U,	// tBXNS
    1074313318U,	// tBcc
    1258988932U,	// tCBNZ
    1258988927U,	// tCBZ
    539605U,	// tCMNz
    539705U,	// tCMPhir
    539705U,	// tCMPi8
    539705U,	// tCMPr
    1308687603U,	// tCPS
    982776960U,	// tEOR
    1074314632U,	// tHINT
    828742U,	// tHLT
    0U,	// tInt_WIN_eh_sjlj_longjmp
    0U,	// tInt_eh_sjlj_longjmp
    0U,	// tInt_eh_sjlj_setjmp
    572322U,	// tLDMIA
    555202U,	// tLDRBi
    555202U,	// tLDRBr
    555623U,	// tLDRHi
    555623U,	// tLDRHr
    555220U,	// tLDRSB
    555642U,	// tLDRSH
    556137U,	// tLDRi
    539753U,	// tLDRpci
    556137U,	// tLDRr
    556137U,	// tLDRspi
    177470331U,	// tLSLri
    982776699U,	// tLSLrr
    177470617U,	// tLSRri
    982776985U,	// tLSRrr
    1258988864U,	// tMOVSr
    446037504U,	// tMOVi8
    540160U,	// tMOVr
    177470347U,	// tMUL
    446037017U,	// tMVN
    982776974U,	// tORR
    0U,	// tPICADD
    990432317U,	// tPOP
    990431872U,	// tPUSH
    540140U,	// tREV
    538474U,	// tREV16
    539269U,	// tREVSH
    982776964U,	// tROR
    429258966U,	// tRSB
    982776139U,	// tSBC
    91390U,	// tSETEND
    2332572584U,	// tSTMIA_UPD
    555207U,	// tSTRBi
    555207U,	// tSTRBr
    555628U,	// tSTRHi
    555628U,	// tSTRHr
    556202U,	// tSTRi
    556202U,	// tSTRr
    556202U,	// tSTRspi
    177469708U,	// tSUBi3
    982776076U,	// tSUBi8
    177469708U,	// tSUBrr
    555276U,	// tSUBspi
    1074313589U,	// tSVC
    538880U,	// tSXTB
    539275U,	// tSXTH
    3114U,	// tTRAP
    540062U,	// tTST
    828678U,	// tUDF
    538885U,	// tUXTB
    539280U,	// tUXTH
    1658U,	// t__brkdiv0
  };

  static const uint32_t OpInfo1[] = {
    0U,	// PHI
    0U,	// INLINEASM
    0U,	// INLINEASM_BR
    0U,	// CFI_INSTRUCTION
    0U,	// EH_LABEL
    0U,	// GC_LABEL
    0U,	// ANNOTATION_LABEL
    0U,	// KILL
    0U,	// EXTRACT_SUBREG
    0U,	// INSERT_SUBREG
    0U,	// IMPLICIT_DEF
    0U,	// SUBREG_TO_REG
    0U,	// COPY_TO_REGCLASS
    0U,	// DBG_VALUE
    0U,	// DBG_LABEL
    0U,	// REG_SEQUENCE
    0U,	// COPY
    0U,	// BUNDLE
    0U,	// LIFETIME_START
    0U,	// LIFETIME_END
    0U,	// STACKMAP
    0U,	// FENTRY_CALL
    0U,	// PATCHPOINT
    0U,	// LOAD_STACK_GUARD
    0U,	// STATEPOINT
    0U,	// LOCAL_ESCAPE
    0U,	// FAULTING_OP
    0U,	// PATCHABLE_OP
    0U,	// PATCHABLE_FUNCTION_ENTER
    0U,	// PATCHABLE_RET
    0U,	// PATCHABLE_FUNCTION_EXIT
    0U,	// PATCHABLE_TAIL_CALL
    0U,	// PATCHABLE_EVENT_CALL
    0U,	// PATCHABLE_TYPED_EVENT_CALL
    0U,	// ICALL_BRANCH_FUNNEL
    0U,	// G_ADD
    0U,	// G_SUB
    0U,	// G_MUL
    0U,	// G_SDIV
    0U,	// G_UDIV
    0U,	// G_SREM
    0U,	// G_UREM
    0U,	// G_AND
    0U,	// G_OR
    0U,	// G_XOR
    0U,	// G_IMPLICIT_DEF
    0U,	// G_PHI
    0U,	// G_FRAME_INDEX
    0U,	// G_GLOBAL_VALUE
    0U,	// G_EXTRACT
    0U,	// G_UNMERGE_VALUES
    0U,	// G_INSERT
    0U,	// G_MERGE_VALUES
    0U,	// G_BUILD_VECTOR
    0U,	// G_BUILD_VECTOR_TRUNC
    0U,	// G_CONCAT_VECTORS
    0U,	// G_PTRTOINT
    0U,	// G_INTTOPTR
    0U,	// G_BITCAST
    0U,	// G_INTRINSIC_TRUNC
    0U,	// G_INTRINSIC_ROUND
    0U,	// G_LOAD
    0U,	// G_SEXTLOAD
    0U,	// G_ZEXTLOAD
    0U,	// G_STORE
    0U,	// G_ATOMIC_CMPXCHG_WITH_SUCCESS
    0U,	// G_ATOMIC_CMPXCHG
    0U,	// G_ATOMICRMW_XCHG
    0U,	// G_ATOMICRMW_ADD
    0U,	// G_ATOMICRMW_SUB
    0U,	// G_ATOMICRMW_AND
    0U,	// G_ATOMICRMW_NAND
    0U,	// G_ATOMICRMW_OR
    0U,	// G_ATOMICRMW_XOR
    0U,	// G_ATOMICRMW_MAX
    0U,	// G_ATOMICRMW_MIN
    0U,	// G_ATOMICRMW_UMAX
    0U,	// G_ATOMICRMW_UMIN
    0U,	// G_BRCOND
    0U,	// G_BRINDIRECT
    0U,	// G_INTRINSIC
    0U,	// G_INTRINSIC_W_SIDE_EFFECTS
    0U,	// G_ANYEXT
    0U,	// G_TRUNC
    0U,	// G_CONSTANT
    0U,	// G_FCONSTANT
    0U,	// G_VASTART
    0U,	// G_VAARG
    0U,	// G_SEXT
    0U,	// G_ZEXT
    0U,	// G_SHL
    0U,	// G_LSHR
    0U,	// G_ASHR
    0U,	// G_ICMP
    0U,	// G_FCMP
    0U,	// G_SELECT
    0U,	// G_UADDO
    0U,	// G_UADDE
    0U,	// G_USUBO
    0U,	// G_USUBE
    0U,	// G_SADDO
    0U,	// G_SADDE
    0U,	// G_SSUBO
    0U,	// G_SSUBE
    0U,	// G_UMULO
    0U,	// G_SMULO
    0U,	// G_UMULH
    0U,	// G_SMULH
    0U,	// G_FADD
    0U,	// G_FSUB
    0U,	// G_FMUL
    0U,	// G_FMA
    0U,	// G_FDIV
    0U,	// G_FREM
    0U,	// G_FPOW
    0U,	// G_FEXP
    0U,	// G_FEXP2
    0U,	// G_FLOG
    0U,	// G_FLOG2
    0U,	// G_FLOG10
    0U,	// G_FNEG
    0U,	// G_FPEXT
    0U,	// G_FPTRUNC
    0U,	// G_FPTOSI
    0U,	// G_FPTOUI
    0U,	// G_SITOFP
    0U,	// G_UITOFP
    0U,	// G_FABS
    0U,	// G_FCANONICALIZE
    0U,	// G_GEP
    0U,	// G_PTR_MASK
    0U,	// G_BR
    0U,	// G_INSERT_VECTOR_ELT
    0U,	// G_EXTRACT_VECTOR_ELT
    0U,	// G_SHUFFLE_VECTOR
    0U,	// G_CTTZ
    0U,	// G_CTTZ_ZERO_UNDEF
    0U,	// G_CTLZ
    0U,	// G_CTLZ_ZERO_UNDEF
    0U,	// G_CTPOP
    0U,	// G_BSWAP
    0U,	// G_FCEIL
    0U,	// G_FCOS
    0U,	// G_FSIN
    0U,	// G_FSQRT
    0U,	// G_FFLOOR
    0U,	// G_ADDRSPACE_CAST
    0U,	// G_BLOCK_ADDR
    0U,	// ABS
    0U,	// ADDSri
    0U,	// ADDSrr
    0U,	// ADDSrsi
    0U,	// ADDSrsr
    0U,	// ADJCALLSTACKDOWN
    0U,	// ADJCALLSTACKUP
    0U,	// ASRi
    0U,	// ASRr
    0U,	// B
    0U,	// BCCZi64
    0U,	// BCCi64
    0U,	// BMOVPCB_CALL
    0U,	// BMOVPCRX_CALL
    0U,	// BR_JTadd
    0U,	// BR_JTm_i12
    0U,	// BR_JTm_rs
    0U,	// BR_JTr
    0U,	// BX_CALL
    0U,	// CMP_SWAP_16
    0U,	// CMP_SWAP_32
    0U,	// CMP_SWAP_64
    0U,	// CMP_SWAP_8
    0U,	// CONSTPOOL_ENTRY
    0U,	// COPY_STRUCT_BYVAL_I32
    0U,	// CompilerBarrier
    0U,	// ITasm
    0U,	// Int_eh_sjlj_dispatchsetup
    0U,	// Int_eh_sjlj_longjmp
    0U,	// Int_eh_sjlj_setjmp
    0U,	// Int_eh_sjlj_setjmp_nofp
    0U,	// Int_eh_sjlj_setup_dispatch
    0U,	// JUMPTABLE_ADDRS
    0U,	// JUMPTABLE_INSTS
    0U,	// JUMPTABLE_TBB
    0U,	// JUMPTABLE_TBH
    0U,	// LDMIA_RET
    8U,	// LDRBT_POST
    1024U,	// LDRConstPool
    0U,	// LDRLIT_ga_abs
    0U,	// LDRLIT_ga_pcrel
    0U,	// LDRLIT_ga_pcrel_ldr
    8U,	// LDRT_POST
    0U,	// LEApcrel
    0U,	// LEApcrelJT
    0U,	// LSLi
    0U,	// LSLr
    0U,	// LSRi
    0U,	// LSRr
    0U,	// MEMCPY
    0U,	// MLAv5
    0U,	// MOVCCi
    0U,	// MOVCCi16
    0U,	// MOVCCi32imm
    0U,	// MOVCCr
    0U,	// MOVCCsi
    0U,	// MOVCCsr
    0U,	// MOVPCRX
    0U,	// MOVTi16_ga_pcrel
    0U,	// MOV_ga_pcrel
    0U,	// MOV_ga_pcrel_ldr
    0U,	// MOVi16_ga_pcrel
    0U,	// MOVi32imm
    0U,	// MOVsra_flag
    0U,	// MOVsrl_flag
    0U,	// MULv5
    0U,	// MVNCCi
    0U,	// PICADD
    0U,	// PICLDR
    0U,	// PICLDRB
    0U,	// PICLDRH
    0U,	// PICLDRSB
    0U,	// PICLDRSH
    0U,	// PICSTR
    0U,	// PICSTRB
    0U,	// PICSTRH
    0U,	// RORi
    0U,	// RORr
    0U,	// RRX
    1024U,	// RRXi
    0U,	// RSBSri
    0U,	// RSBSrsi
    0U,	// RSBSrsr
    0U,	// SMLALv5
    0U,	// SMULLv5
    0U,	// SPACE
    8U,	// STRBT_POST
    0U,	// STRBi_preidx
    0U,	// STRBr_preidx
    0U,	// STRH_preidx
    8U,	// STRT_POST
    0U,	// STRi_preidx
    0U,	// STRr_preidx
    0U,	// SUBS_PC_LR
    0U,	// SUBSri
    0U,	// SUBSrr
    0U,	// SUBSrsi
    0U,	// SUBSrsr
    0U,	// TAILJMPd
    0U,	// TAILJMPr
    0U,	// TAILJMPr4
    0U,	// TCRETURNdi
    0U,	// TCRETURNri
    0U,	// TPsoft
    0U,	// UMLALv5
    0U,	// UMULLv5
    1040U,	// VLD1LNdAsm_16
    1040U,	// VLD1LNdAsm_32
    1040U,	// VLD1LNdAsm_8
    2064U,	// VLD1LNdWB_fixed_Asm_16
    2064U,	// VLD1LNdWB_fixed_Asm_32
    2064U,	// VLD1LNdWB_fixed_Asm_8
    32784U,	// VLD1LNdWB_register_Asm_16
    32784U,	// VLD1LNdWB_register_Asm_32
    32784U,	// VLD1LNdWB_register_Asm_8
    1040U,	// VLD2LNdAsm_16
    1040U,	// VLD2LNdAsm_32
    1040U,	// VLD2LNdAsm_8
    2064U,	// VLD2LNdWB_fixed_Asm_16
    2064U,	// VLD2LNdWB_fixed_Asm_32
    2064U,	// VLD2LNdWB_fixed_Asm_8
    32784U,	// VLD2LNdWB_register_Asm_16
    32784U,	// VLD2LNdWB_register_Asm_32
    32784U,	// VLD2LNdWB_register_Asm_8
    1040U,	// VLD2LNqAsm_16
    1040U,	// VLD2LNqAsm_32
    2064U,	// VLD2LNqWB_fixed_Asm_16
    2064U,	// VLD2LNqWB_fixed_Asm_32
    32784U,	// VLD2LNqWB_register_Asm_16
    32784U,	// VLD2LNqWB_register_Asm_32
    0U,	// VLD3DUPdAsm_16
    0U,	// VLD3DUPdAsm_32
    0U,	// VLD3DUPdAsm_8
    0U,	// VLD3DUPdWB_fixed_Asm_16
    0U,	// VLD3DUPdWB_fixed_Asm_32
    0U,	// VLD3DUPdWB_fixed_Asm_8
    1048U,	// VLD3DUPdWB_register_Asm_16
    1048U,	// VLD3DUPdWB_register_Asm_32
    1048U,	// VLD3DUPdWB_register_Asm_8
    0U,	// VLD3DUPqAsm_16
    0U,	// VLD3DUPqAsm_32
    0U,	// VLD3DUPqAsm_8
    0U,	// VLD3DUPqWB_fixed_Asm_16
    0U,	// VLD3DUPqWB_fixed_Asm_32
    0U,	// VLD3DUPqWB_fixed_Asm_8
    1048U,	// VLD3DUPqWB_register_Asm_16
    1048U,	// VLD3DUPqWB_register_Asm_32
    1048U,	// VLD3DUPqWB_register_Asm_8
    1040U,	// VLD3LNdAsm_16
    1040U,	// VLD3LNdAsm_32
    1040U,	// VLD3LNdAsm_8
    2064U,	// VLD3LNdWB_fixed_Asm_16
    2064U,	// VLD3LNdWB_fixed_Asm_32
    2064U,	// VLD3LNdWB_fixed_Asm_8
    32784U,	// VLD3LNdWB_register_Asm_16
    32784U,	// VLD3LNdWB_register_Asm_32
    32784U,	// VLD3LNdWB_register_Asm_8
    1040U,	// VLD3LNqAsm_16
    1040U,	// VLD3LNqAsm_32
    2064U,	// VLD3LNqWB_fixed_Asm_16
    2064U,	// VLD3LNqWB_fixed_Asm_32
    32784U,	// VLD3LNqWB_register_Asm_16
    32784U,	// VLD3LNqWB_register_Asm_32
    32U,	// VLD3dAsm_16
    32U,	// VLD3dAsm_32
    32U,	// VLD3dAsm_8
    40U,	// VLD3dWB_fixed_Asm_16
    40U,	// VLD3dWB_fixed_Asm_32
    40U,	// VLD3dWB_fixed_Asm_8
    68656U,	// VLD3dWB_register_Asm_16
    68656U,	// VLD3dWB_register_Asm_32
    68656U,	// VLD3dWB_register_Asm_8
    0U,	// VLD3qAsm_16
    0U,	// VLD3qAsm_32
    0U,	// VLD3qAsm_8
    0U,	// VLD3qWB_fixed_Asm_16
    0U,	// VLD3qWB_fixed_Asm_32
    0U,	// VLD3qWB_fixed_Asm_8
    1048U,	// VLD3qWB_register_Asm_16
    1048U,	// VLD3qWB_register_Asm_32
    1048U,	// VLD3qWB_register_Asm_8
    0U,	// VLD4DUPdAsm_16
    0U,	// VLD4DUPdAsm_32
    0U,	// VLD4DUPdAsm_8
    0U,	// VLD4DUPdWB_fixed_Asm_16
    0U,	// VLD4DUPdWB_fixed_Asm_32
    0U,	// VLD4DUPdWB_fixed_Asm_8
    1048U,	// VLD4DUPdWB_register_Asm_16
    1048U,	// VLD4DUPdWB_register_Asm_32
    1048U,	// VLD4DUPdWB_register_Asm_8
    0U,	// VLD4DUPqAsm_16
    0U,	// VLD4DUPqAsm_32
    0U,	// VLD4DUPqAsm_8
    0U,	// VLD4DUPqWB_fixed_Asm_16
    0U,	// VLD4DUPqWB_fixed_Asm_32
    0U,	// VLD4DUPqWB_fixed_Asm_8
    1048U,	// VLD4DUPqWB_register_Asm_16
    1048U,	// VLD4DUPqWB_register_Asm_32
    1048U,	// VLD4DUPqWB_register_Asm_8
    1040U,	// VLD4LNdAsm_16
    1040U,	// VLD4LNdAsm_32
    1040U,	// VLD4LNdAsm_8
    2064U,	// VLD4LNdWB_fixed_Asm_16
    2064U,	// VLD4LNdWB_fixed_Asm_32
    2064U,	// VLD4LNdWB_fixed_Asm_8
    32784U,	// VLD4LNdWB_register_Asm_16
    32784U,	// VLD4LNdWB_register_Asm_32
    32784U,	// VLD4LNdWB_register_Asm_8
    1040U,	// VLD4LNqAsm_16
    1040U,	// VLD4LNqAsm_32
    2064U,	// VLD4LNqWB_fixed_Asm_16
    2064U,	// VLD4LNqWB_fixed_Asm_32
    32784U,	// VLD4LNqWB_register_Asm_16
    32784U,	// VLD4LNqWB_register_Asm_32
    32U,	// VLD4dAsm_16
    32U,	// VLD4dAsm_32
    32U,	// VLD4dAsm_8
    40U,	// VLD4dWB_fixed_Asm_16
    40U,	// VLD4dWB_fixed_Asm_32
    40U,	// VLD4dWB_fixed_Asm_8
    68656U,	// VLD4dWB_register_Asm_16
    68656U,	// VLD4dWB_register_Asm_32
    68656U,	// VLD4dWB_register_Asm_8
    0U,	// VLD4qAsm_16
    0U,	// VLD4qAsm_32
    0U,	// VLD4qAsm_8
    0U,	// VLD4qWB_fixed_Asm_16
    0U,	// VLD4qWB_fixed_Asm_32
    0U,	// VLD4qWB_fixed_Asm_8
    1048U,	// VLD4qWB_register_Asm_16
    1048U,	// VLD4qWB_register_Asm_32
    1048U,	// VLD4qWB_register_Asm_8
    0U,	// VMOVD0
    0U,	// VMOVDcc
    0U,	// VMOVQ0
    0U,	// VMOVScc
    1040U,	// VST1LNdAsm_16
    1040U,	// VST1LNdAsm_32
    1040U,	// VST1LNdAsm_8
    2064U,	// VST1LNdWB_fixed_Asm_16
    2064U,	// VST1LNdWB_fixed_Asm_32
    2064U,	// VST1LNdWB_fixed_Asm_8
    32784U,	// VST1LNdWB_register_Asm_16
    32784U,	// VST1LNdWB_register_Asm_32
    32784U,	// VST1LNdWB_register_Asm_8
    1040U,	// VST2LNdAsm_16
    1040U,	// VST2LNdAsm_32
    1040U,	// VST2LNdAsm_8
    2064U,	// VST2LNdWB_fixed_Asm_16
    2064U,	// VST2LNdWB_fixed_Asm_32
    2064U,	// VST2LNdWB_fixed_Asm_8
    32784U,	// VST2LNdWB_register_Asm_16
    32784U,	// VST2LNdWB_register_Asm_32
    32784U,	// VST2LNdWB_register_Asm_8
    1040U,	// VST2LNqAsm_16
    1040U,	// VST2LNqAsm_32
    2064U,	// VST2LNqWB_fixed_Asm_16
    2064U,	// VST2LNqWB_fixed_Asm_32
    32784U,	// VST2LNqWB_register_Asm_16
    32784U,	// VST2LNqWB_register_Asm_32
    1040U,	// VST3LNdAsm_16
    1040U,	// VST3LNdAsm_32
    1040U,	// VST3LNdAsm_8
    2064U,	// VST3LNdWB_fixed_Asm_16
    2064U,	// VST3LNdWB_fixed_Asm_32
    2064U,	// VST3LNdWB_fixed_Asm_8
    32784U,	// VST3LNdWB_register_Asm_16
    32784U,	// VST3LNdWB_register_Asm_32
    32784U,	// VST3LNdWB_register_Asm_8
    1040U,	// VST3LNqAsm_16
    1040U,	// VST3LNqAsm_32
    2064U,	// VST3LNqWB_fixed_Asm_16
    2064U,	// VST3LNqWB_fixed_Asm_32
    32784U,	// VST3LNqWB_register_Asm_16
    32784U,	// VST3LNqWB_register_Asm_32
    32U,	// VST3dAsm_16
    32U,	// VST3dAsm_32
    32U,	// VST3dAsm_8
    40U,	// VST3dWB_fixed_Asm_16
    40U,	// VST3dWB_fixed_Asm_32
    40U,	// VST3dWB_fixed_Asm_8
    68656U,	// VST3dWB_register_Asm_16
    68656U,	// VST3dWB_register_Asm_32
    68656U,	// VST3dWB_register_Asm_8
    0U,	// VST3qAsm_16
    0U,	// VST3qAsm_32
    0U,	// VST3qAsm_8
    0U,	// VST3qWB_fixed_Asm_16
    0U,	// VST3qWB_fixed_Asm_32
    0U,	// VST3qWB_fixed_Asm_8
    1048U,	// VST3qWB_register_Asm_16
    1048U,	// VST3qWB_register_Asm_32
    1048U,	// VST3qWB_register_Asm_8
    1040U,	// VST4LNdAsm_16
    1040U,	// VST4LNdAsm_32
    1040U,	// VST4LNdAsm_8
    2064U,	// VST4LNdWB_fixed_Asm_16
    2064U,	// VST4LNdWB_fixed_Asm_32
    2064U,	// VST4LNdWB_fixed_Asm_8
    32784U,	// VST4LNdWB_register_Asm_16
    32784U,	// VST4LNdWB_register_Asm_32
    32784U,	// VST4LNdWB_register_Asm_8
    1040U,	// VST4LNqAsm_16
    1040U,	// VST4LNqAsm_32
    2064U,	// VST4LNqWB_fixed_Asm_16
    2064U,	// VST4LNqWB_fixed_Asm_32
    32784U,	// VST4LNqWB_register_Asm_16
    32784U,	// VST4LNqWB_register_Asm_32
    32U,	// VST4dAsm_16
    32U,	// VST4dAsm_32
    32U,	// VST4dAsm_8
    40U,	// VST4dWB_fixed_Asm_16
    40U,	// VST4dWB_fixed_Asm_32
    40U,	// VST4dWB_fixed_Asm_8
    68656U,	// VST4dWB_register_Asm_16
    68656U,	// VST4dWB_register_Asm_32
    68656U,	// VST4dWB_register_Asm_8
    0U,	// VST4qAsm_16
    0U,	// VST4qAsm_32
    0U,	// VST4qAsm_8
    0U,	// VST4qWB_fixed_Asm_16
    0U,	// VST4qWB_fixed_Asm_32
    0U,	// VST4qWB_fixed_Asm_8
    1048U,	// VST4qWB_register_Asm_16
    1048U,	// VST4qWB_register_Asm_32
    1048U,	// VST4qWB_register_Asm_8
    0U,	// WIN__CHKSTK
    0U,	// WIN__DBZCHK
    0U,	// t2ABS
    0U,	// t2ADDSri
    0U,	// t2ADDSrr
    0U,	// t2ADDSrs
    0U,	// t2BR_JT
    0U,	// t2LDMIA_RET
    1024U,	// t2LDRBpcrel
    1024U,	// t2LDRConstPool
    1024U,	// t2LDRHpcrel
    1024U,	// t2LDRSBpcrel
    1024U,	// t2LDRSHpcrel
    0U,	// t2LDRpci_pic
    1024U,	// t2LDRpcrel
    0U,	// t2LEApcrel
    0U,	// t2LEApcrelJT
    0U,	// t2MOVCCasr
    0U,	// t2MOVCCi
    0U,	// t2MOVCCi16
    0U,	// t2MOVCCi32imm
    0U,	// t2MOVCClsl
    0U,	// t2MOVCClsr
    0U,	// t2MOVCCr
    0U,	// t2MOVCCror
    56U,	// t2MOVSsi
    64U,	// t2MOVSsr
    0U,	// t2MOVTi16_ga_pcrel
    0U,	// t2MOV_ga_pcrel
    0U,	// t2MOVi16_ga_pcrel
    0U,	// t2MOVi32imm
    56U,	// t2MOVsi
    64U,	// t2MOVsr
    0U,	// t2MVNCCi
    0U,	// t2RSBSri
    0U,	// t2RSBSrs
    0U,	// t2STRB_preidx
    0U,	// t2STRH_preidx
    0U,	// t2STR_preidx
    0U,	// t2SUBSri
    0U,	// t2SUBSrr
    0U,	// t2SUBSrs
    0U,	// t2TBB_JT
    0U,	// t2TBH_JT
    0U,	// tADCS
    0U,	// tADDSi3
    0U,	// tADDSi8
    0U,	// tADDSrr
    0U,	// tADDframe
    0U,	// tADJCALLSTACKDOWN
    0U,	// tADJCALLSTACKUP
    0U,	// tBRIND
    0U,	// tBR_JTr
    0U,	// tBX_CALL
    0U,	// tBX_RET
    0U,	// tBX_RET_vararg
    0U,	// tBfar
    0U,	// tLDMIA_UPD
    1024U,	// tLDRConstPool
    0U,	// tLDRLIT_ga_abs
    0U,	// tLDRLIT_ga_pcrel
    0U,	// tLDR_postidx
    0U,	// tLDRpci_pic
    0U,	// tLEApcrel
    0U,	// tLEApcrelJT
    0U,	// tMOVCCr_pseudo
    0U,	// tPOP_RET
    0U,	// tRSBS
    0U,	// tSBCS
    0U,	// tSUBSi3
    0U,	// tSUBSi8
    0U,	// tSUBSrr
    0U,	// tTAILJMPd
    0U,	// tTAILJMPdND
    0U,	// tTAILJMPr
    0U,	// tTBB_JT
    0U,	// tTBH_JT
    0U,	// tTPsoft
    98304U,	// ADCri
    0U,	// ADCrr
    131072U,	// ADCrsi
    0U,	// ADCrsr
    98304U,	// ADDri
    0U,	// ADDrr
    131072U,	// ADDrsi
    0U,	// ADDrsr
    72U,	// ADR
    0U,	// AESD
    0U,	// AESE
    0U,	// AESIMC
    0U,	// AESMC
    98304U,	// ANDri
    0U,	// ANDrr
    131072U,	// ANDrsi
    0U,	// ANDrsr
    80U,	// BFC
    163928U,	// BFI
    98304U,	// BICri
    0U,	// BICrr
    131072U,	// BICrsi
    0U,	// BICrsr
    0U,	// BKPT
    0U,	// BL
    0U,	// BLX
    0U,	// BLX_pred
    0U,	// BLXi
    0U,	// BL_pred
    0U,	// BX
    0U,	// BXJ
    0U,	// BX_RET
    0U,	// BX_pred
    0U,	// Bcc
    4145U,	// CDP
    0U,	// CDP2
    0U,	// CLREX
    1024U,	// CLZ
    96U,	// CMNri
    1024U,	// CMNzrr
    104U,	// CMNzrsi
    64U,	// CMNzrsr
    96U,	// CMPri
    1024U,	// CMPrr
    104U,	// CMPrsi
    64U,	// CMPrsr
    0U,	// CPS1p
    0U,	// CPS2p
    1112U,	// CPS3p
    1112U,	// CRC32B
    1112U,	// CRC32CB
    1112U,	// CRC32CH
    1112U,	// CRC32CW
    1112U,	// CRC32H
    1112U,	// CRC32W
    0U,	// DBG
    0U,	// DMB
    0U,	// DSB
    98304U,	// EORri
    0U,	// EORrr
    131072U,	// EORrsi
    0U,	// EORrsr
    0U,	// ERET
    1U,	// FCONSTD
    1U,	// FCONSTH
    1U,	// FCONSTS
    33U,	// FLDMXDB_UPD
    1136U,	// FLDMXIA
    33U,	// FLDMXIA_UPD
    0U,	// FMSTAT
    33U,	// FSTMXDB_UPD
    1136U,	// FSTMXIA
    33U,	// FSTMXIA_UPD
    0U,	// HINT
    0U,	// HLT
    0U,	// HVC
    0U,	// ISB
    8U,	// LDA
    8U,	// LDAB
    8U,	// LDAEX
    8U,	// LDAEXB
    0U,	// LDAEXD
    8U,	// LDAEXH
    8U,	// LDAH
    0U,	// LDC2L_OFFSET
    1U,	// LDC2L_OPTION
    2U,	// LDC2L_POST
    0U,	// LDC2L_PRE
    0U,	// LDC2_OFFSET
    1U,	// LDC2_OPTION
    2U,	// LDC2_POST
    0U,	// LDC2_PRE
    122U,	// LDCL_OFFSET
    196738U,	// LDCL_OPTION
    229506U,	// LDCL_POST
    138U,	// LDCL_PRE
    122U,	// LDC_OFFSET
    196738U,	// LDC_OPTION
    229506U,	// LDC_POST
    138U,	// LDC_PRE
    1136U,	// LDMDA
    33U,	// LDMDA_UPD
    1136U,	// LDMDB
    33U,	// LDMDB_UPD
    1136U,	// LDMIA
    33U,	// LDMIA_UPD
    1136U,	// LDMIB
    33U,	// LDMIB_UPD
    262272U,	// LDRBT_POST_IMM
    262272U,	// LDRBT_POST_REG
    262272U,	// LDRB_POST_IMM
    262272U,	// LDRB_POST_REG
    144U,	// LDRB_PRE_IMM
    152U,	// LDRB_PRE_REG
    160U,	// LDRBi12
    168U,	// LDRBrs
    294912U,	// LDRD
    2424832U,	// LDRD_POST
    360448U,	// LDRD_PRE
    8U,	// LDREX
    8U,	// LDREXB
    0U,	// LDREXD
    8U,	// LDREXH
    176U,	// LDRH
    393344U,	// LDRHTi
    426112U,	// LDRHTr
    458880U,	// LDRH_POST
    184U,	// LDRH_PRE
    176U,	// LDRSB
    393344U,	// LDRSBTi
    426112U,	// LDRSBTr
    458880U,	// LDRSB_POST
    184U,	// LDRSB_PRE
    176U,	// LDRSH
    393344U,	// LDRSHTi
    426112U,	// LDRSHTr
    458880U,	// LDRSH_POST
    184U,	// LDRSH_PRE
    262272U,	// LDRT_POST_IMM
    262272U,	// LDRT_POST_REG
    262272U,	// LDR_POST_IMM
    262272U,	// LDR_POST_REG
    144U,	// LDR_PRE_IMM
    152U,	// LDR_PRE_REG
    160U,	// LDRcp
    160U,	// LDRi12
    168U,	// LDRrs
    4690993U,	// MCR
    192U,	// MCR2
    6788145U,	// MCRR
    524312U,	// MCRR2
    35651584U,	// MLA
    35651584U,	// MLS
    0U,	// MOVPCLR
    1112U,	// MOVTi16
    96U,	// MOVi
    1024U,	// MOVi16
    1024U,	// MOVr
    1024U,	// MOVr_TC
    104U,	// MOVsi
    64U,	// MOVsr
    0U,	// MRC
    0U,	// MRC2
    0U,	// MRRC
    0U,	// MRRC2
    2U,	// MRS
    200U,	// MRSbanked
    2U,	// MRSsys
    33U,	// MSR
    0U,	// MSRbanked
    3U,	// MSRi
    0U,	// MUL
    96U,	// MVNi
    1024U,	// MVNr
    104U,	// MVNsi
    64U,	// MVNsr
    98304U,	// ORRri
    0U,	// ORRrr
    131072U,	// ORRrsi
    0U,	// ORRrsr
    8388608U,	// PKHBT
    10485760U,	// PKHTB
    0U,	// PLDWi12
    0U,	// PLDWrs
    0U,	// PLDi12
    0U,	// PLDrs
    0U,	// PLIi12
    0U,	// PLIrs
    0U,	// QADD
    0U,	// QADD16
    0U,	// QADD8
    0U,	// QASX
    0U,	// QDADD
    0U,	// QDSUB
    0U,	// QSAX
    0U,	// QSUB
    0U,	// QSUB16
    0U,	// QSUB8
    1024U,	// RBIT
    1024U,	// REV
    1024U,	// REV16
    1024U,	// REVSH
    0U,	// RFEDA
    0U,	// RFEDA_UPD
    0U,	// RFEDB
    0U,	// RFEDB_UPD
    0U,	// RFEIA
    0U,	// RFEIA_UPD
    0U,	// RFEIB
    0U,	// RFEIB_UPD
    98304U,	// RSBri
    0U,	// RSBrr
    131072U,	// RSBrsi
    0U,	// RSBrsr
    98304U,	// RSCri
    0U,	// RSCrr
    131072U,	// RSCrsi
    0U,	// RSCrsr
    0U,	// SADD16
    0U,	// SADD8
    0U,	// SASX
    0U,	// SB
    98304U,	// SBCri
    0U,	// SBCrr
    131072U,	// SBCrsi
    0U,	// SBCrsr
    69206016U,	// SBFX
    0U,	// SDIV
    0U,	// SEL
    0U,	// SETEND
    0U,	// SETPAN
    1048U,	// SHA1C
    0U,	// SHA1H
    1048U,	// SHA1M
    1048U,	// SHA1P
    1048U,	// SHA1SU0
    0U,	// SHA1SU1
    1048U,	// SHA256H
    1048U,	// SHA256H2
    0U,	// SHA256SU0
    1048U,	// SHA256SU1
    0U,	// SHADD16
    0U,	// SHADD8
    0U,	// SHASX
    0U,	// SHSAX
    0U,	// SHSUB16
    0U,	// SHSUB8
    0U,	// SMC
    35651584U,	// SMLABB
    35651584U,	// SMLABT
    35651584U,	// SMLAD
    35651584U,	// SMLADX
    0U,	// SMLAL
    35651584U,	// SMLALBB
    35651584U,	// SMLALBT
    35651584U,	// SMLALD
    35651584U,	// SMLALDX
    35651584U,	// SMLALTB
    35651584U,	// SMLALTT
    35651584U,	// SMLATB
    35651584U,	// SMLATT
    35651584U,	// SMLAWB
    35651584U,	// SMLAWT
    35651584U,	// SMLSD
    35651584U,	// SMLSDX
    35651584U,	// SMLSLD
    35651584U,	// SMLSLDX
    35651584U,	// SMMLA
    35651584U,	// SMMLAR
    35651584U,	// SMMLS
    35651584U,	// SMMLSR
    0U,	// SMMUL
    0U,	// SMMULR
    0U,	// SMUAD
    0U,	// SMUADX
    0U,	// SMULBB
    0U,	// SMULBT
    35651584U,	// SMULL
    0U,	// SMULTB
    0U,	// SMULTT
    0U,	// SMULWB
    0U,	// SMULWT
    0U,	// SMUSD
    0U,	// SMUSDX
    0U,	// SRSDA
    0U,	// SRSDA_UPD
    0U,	// SRSDB
    0U,	// SRSDB_UPD
    0U,	// SRSIA
    0U,	// SRSIA_UPD
    0U,	// SRSIB
    0U,	// SRSIB_UPD
    6352U,	// SSAT
    1232U,	// SSAT16
    0U,	// SSAX
    0U,	// SSUB16
    0U,	// SSUB8
    0U,	// STC2L_OFFSET
    1U,	// STC2L_OPTION
    2U,	// STC2L_POST
    0U,	// STC2L_PRE
    0U,	// STC2_OFFSET
    1U,	// STC2_OPTION
    2U,	// STC2_POST
    0U,	// STC2_PRE
    122U,	// STCL_OFFSET
    196738U,	// STCL_OPTION
    229506U,	// STCL_POST
    138U,	// STCL_PRE
    122U,	// STC_OFFSET
    196738U,	// STC_OPTION
    229506U,	// STC_POST
    138U,	// STC_PRE
    8U,	// STL
    8U,	// STLB
    557056U,	// STLEX
    557056U,	// STLEXB
    216U,	// STLEXD
    557056U,	// STLEXH
    8U,	// STLH
    1136U,	// STMDA
    33U,	// STMDA_UPD
    1136U,	// STMDB
    33U,	// STMDB_UPD
    1136U,	// STMIA
    33U,	// STMIA_UPD
    1136U,	// STMIB
    33U,	// STMIB_UPD
    262272U,	// STRBT_POST_IMM
    262272U,	// STRBT_POST_REG
    262272U,	// STRB_POST_IMM
    262272U,	// STRB_POST_REG
    144U,	// STRB_PRE_IMM
    152U,	// STRB_PRE_REG
    160U,	// STRBi12
    168U,	// STRBrs
    294912U,	// STRD
    2424920U,	// STRD_POST
    360536U,	// STRD_PRE
    557056U,	// STREX
    557056U,	// STREXB
    216U,	// STREXD
    557056U,	// STREXH
    176U,	// STRH
    393344U,	// STRHTi
    426112U,	// STRHTr
    458880U,	// STRH_POST
    184U,	// STRH_PRE
    262272U,	// STRT_POST_IMM
    262272U,	// STRT_POST_REG
    262272U,	// STR_POST_IMM
    262272U,	// STR_POST_REG
    144U,	// STR_PRE_IMM
    152U,	// STR_PRE_REG
    160U,	// STRi12
    168U,	// STRrs
    98304U,	// SUBri
    0U,	// SUBrr
    131072U,	// SUBrsi
    0U,	// SUBrsr
    0U,	// SVC
    557056U,	// SWP
    557056U,	// SWPB
    12582912U,	// SXTAB
    12582912U,	// SXTAB16
    12582912U,	// SXTAH
    7168U,	// SXTB
    7168U,	// SXTB16
    7168U,	// SXTH
    96U,	// TEQri
    1024U,	// TEQrr
    104U,	// TEQrsi
    64U,	// TEQrsr
    0U,	// TRAP
    0U,	// TRAPNaCl
    0U,	// TSB
    96U,	// TSTri
    1024U,	// TSTrr
    104U,	// TSTrsi
    64U,	// TSTrsr
    0U,	// UADD16
    0U,	// UADD8
    0U,	// UASX
    69206016U,	// UBFX
    0U,	// UDF
    0U,	// UDIV
    0U,	// UHADD16
    0U,	// UHADD8
    0U,	// UHASX
    0U,	// UHSAX
    0U,	// UHSUB16
    0U,	// UHSUB8
    35651584U,	// UMAAL
    0U,	// UMLAL
    35651584U,	// UMULL
    0U,	// UQADD16
    0U,	// UQADD8
    0U,	// UQASX
    0U,	// UQSAX
    0U,	// UQSUB16
    0U,	// UQSUB8
    0U,	// USAD8
    35651584U,	// USADA8
    14680064U,	// USAT
    0U,	// USAT16
    0U,	// USAX
    0U,	// USUB16
    0U,	// USUB8
    12582912U,	// UXTAB
    12582912U,	// UXTAB16
    12582912U,	// UXTAH
    7168U,	// UXTB
    7168U,	// UXTB16
    7168U,	// UXTH
    1048U,	// VABALsv2i64
    1048U,	// VABALsv4i32
    1048U,	// VABALsv8i16
    1048U,	// VABALuv2i64
    1048U,	// VABALuv4i32
    1048U,	// VABALuv8i16
    1048U,	// VABAsv16i8
    1048U,	// VABAsv2i32
    1048U,	// VABAsv4i16
    1048U,	// VABAsv4i32
    1048U,	// VABAsv8i16
    1048U,	// VABAsv8i8
    1048U,	// VABAuv16i8
    1048U,	// VABAuv2i32
    1048U,	// VABAuv4i16
    1048U,	// VABAuv4i32
    1048U,	// VABAuv8i16
    1048U,	// VABAuv8i8
    1112U,	// VABDLsv2i64
    1112U,	// VABDLsv4i32
    1112U,	// VABDLsv8i16
    1112U,	// VABDLuv2i64
    1112U,	// VABDLuv4i32
    1112U,	// VABDLuv8i16
    70705U,	// VABDfd
    70705U,	// VABDfq
    70705U,	// VABDhd
    70705U,	// VABDhq
    1112U,	// VABDsv16i8
    1112U,	// VABDsv2i32
    1112U,	// VABDsv4i16
    1112U,	// VABDsv4i32
    1112U,	// VABDsv8i16
    1112U,	// VABDsv8i8
    1112U,	// VABDuv16i8
    1112U,	// VABDuv2i32
    1112U,	// VABDuv4i16
    1112U,	// VABDuv4i32
    1112U,	// VABDuv8i16
    1112U,	// VABDuv8i8
    33U,	// VABSD
    33U,	// VABSH
    33U,	// VABSS
    33U,	// VABSfd
    33U,	// VABSfq
    33U,	// VABShd
    33U,	// VABShq
    0U,	// VABSv16i8
    0U,	// VABSv2i32
    0U,	// VABSv4i16
    0U,	// VABSv4i32
    0U,	// VABSv8i16
    0U,	// VABSv8i8
    70705U,	// VACGEfd
    70705U,	// VACGEfq
    70705U,	// VACGEhd
    70705U,	// VACGEhq
    70705U,	// VACGTfd
    70705U,	// VACGTfq
    70705U,	// VACGThd
    70705U,	// VACGThq
    70705U,	// VADDD
    70705U,	// VADDH
    1112U,	// VADDHNv2i32
    1112U,	// VADDHNv4i16
    1112U,	// VADDHNv8i8
    1112U,	// VADDLsv2i64
    1112U,	// VADDLsv4i32
    1112U,	// VADDLsv8i16
    1112U,	// VADDLuv2i64
    1112U,	// VADDLuv4i32
    1112U,	// VADDLuv8i16
    70705U,	// VADDS
    1112U,	// VADDWsv2i64
    1112U,	// VADDWsv4i32
    1112U,	// VADDWsv8i16
    1112U,	// VADDWuv2i64
    1112U,	// VADDWuv4i32
    1112U,	// VADDWuv8i16
    70705U,	// VADDfd
    70705U,	// VADDfq
    70705U,	// VADDhd
    70705U,	// VADDhq
    1112U,	// VADDv16i8
    1112U,	// VADDv1i64
    1112U,	// VADDv2i32
    1112U,	// VADDv2i64
    1112U,	// VADDv4i16
    1112U,	// VADDv4i32
    1112U,	// VADDv8i16
    1112U,	// VADDv8i8
    0U,	// VANDd
    0U,	// VANDq
    0U,	// VBICd
    0U,	// VBICiv2i32
    0U,	// VBICiv4i16
    0U,	// VBICiv4i32
    0U,	// VBICiv8i16
    0U,	// VBICq
    589912U,	// VBIFd
    589912U,	// VBIFq
    589912U,	// VBITd
    589912U,	// VBITq
    589912U,	// VBSLd
    589912U,	// VBSLq
    622680U,	// VCADDv2f32
    622680U,	// VCADDv4f16
    622680U,	// VCADDv4f32
    622680U,	// VCADDv8f16
    70705U,	// VCEQfd
    70705U,	// VCEQfq
    70705U,	// VCEQhd
    70705U,	// VCEQhq
    1112U,	// VCEQv16i8
    1112U,	// VCEQv2i32
    1112U,	// VCEQv4i16
    1112U,	// VCEQv4i32
    1112U,	// VCEQv8i16
    1112U,	// VCEQv8i8
    3U,	// VCEQzv16i8
    225U,	// VCEQzv2f32
    3U,	// VCEQzv2i32
    225U,	// VCEQzv4f16
    225U,	// VCEQzv4f32
    3U,	// VCEQzv4i16
    3U,	// VCEQzv4i32
    225U,	// VCEQzv8f16
    3U,	// VCEQzv8i16
    3U,	// VCEQzv8i8
    70705U,	// VCGEfd
    70705U,	// VCGEfq
    70705U,	// VCGEhd
    70705U,	// VCGEhq
    1112U,	// VCGEsv16i8
    1112U,	// VCGEsv2i32
    1112U,	// VCGEsv4i16
    1112U,	// VCGEsv4i32
    1112U,	// VCGEsv8i16
    1112U,	// VCGEsv8i8
    1112U,	// VCGEuv16i8
    1112U,	// VCGEuv2i32
    1112U,	// VCGEuv4i16
    1112U,	// VCGEuv4i32
    1112U,	// VCGEuv8i16
    1112U,	// VCGEuv8i8
    3U,	// VCGEzv16i8
    225U,	// VCGEzv2f32
    3U,	// VCGEzv2i32
    225U,	// VCGEzv4f16
    225U,	// VCGEzv4f32
    3U,	// VCGEzv4i16
    3U,	// VCGEzv4i32
    225U,	// VCGEzv8f16
    3U,	// VCGEzv8i16
    3U,	// VCGEzv8i8
    70705U,	// VCGTfd
    70705U,	// VCGTfq
    70705U,	// VCGThd
    70705U,	// VCGThq
    1112U,	// VCGTsv16i8
    1112U,	// VCGTsv2i32
    1112U,	// VCGTsv4i16
    1112U,	// VCGTsv4i32
    1112U,	// VCGTsv8i16
    1112U,	// VCGTsv8i8
    1112U,	// VCGTuv16i8
    1112U,	// VCGTuv2i32
    1112U,	// VCGTuv4i16
    1112U,	// VCGTuv4i32
    1112U,	// VCGTuv8i16
    1112U,	// VCGTuv8i8
    3U,	// VCGTzv16i8
    225U,	// VCGTzv2f32
    3U,	// VCGTzv2i32
    225U,	// VCGTzv4f16
    225U,	// VCGTzv4f32
    3U,	// VCGTzv4i16
    3U,	// VCGTzv4i32
    225U,	// VCGTzv8f16
    3U,	// VCGTzv8i16
    3U,	// VCGTzv8i8
    3U,	// VCLEzv16i8
    225U,	// VCLEzv2f32
    3U,	// VCLEzv2i32
    225U,	// VCLEzv4f16
    225U,	// VCLEzv4f32
    3U,	// VCLEzv4i16
    3U,	// VCLEzv4i32
    225U,	// VCLEzv8f16
    3U,	// VCLEzv8i16
    3U,	// VCLEzv8i8
    0U,	// VCLSv16i8
    0U,	// VCLSv2i32
    0U,	// VCLSv4i16
    0U,	// VCLSv4i32
    0U,	// VCLSv8i16
    0U,	// VCLSv8i8
    3U,	// VCLTzv16i8
    225U,	// VCLTzv2f32
    3U,	// VCLTzv2i32
    225U,	// VCLTzv4f16
    225U,	// VCLTzv4f32
    3U,	// VCLTzv4i16
    3U,	// VCLTzv4i32
    225U,	// VCLTzv8f16
    3U,	// VCLTzv8i16
    3U,	// VCLTzv8i8
    0U,	// VCLZv16i8
    0U,	// VCLZv2i32
    0U,	// VCLZv4i16
    0U,	// VCLZv4i32
    0U,	// VCLZv8i16
    0U,	// VCLZv8i8
    655384U,	// VCMLAv2f32
    17276952U,	// VCMLAv2f32_indexed
    655384U,	// VCMLAv4f16
    17276952U,	// VCMLAv4f16_indexed
    655384U,	// VCMLAv4f32
    17276952U,	// VCMLAv4f32_indexed
    655384U,	// VCMLAv8f16
    17276952U,	// VCMLAv8f16_indexed
    33U,	// VCMPD
    33U,	// VCMPED
    33U,	// VCMPEH
    33U,	// VCMPES
    0U,	// VCMPEZD
    0U,	// VCMPEZH
    0U,	// VCMPEZS
    33U,	// VCMPH
    33U,	// VCMPS
    0U,	// VCMPZD
    0U,	// VCMPZH
    0U,	// VCMPZS
    1024U,	// VCNTd
    1024U,	// VCNTq
    0U,	// VCVTANSDf
    0U,	// VCVTANSDh
    0U,	// VCVTANSQf
    0U,	// VCVTANSQh
    0U,	// VCVTANUDf
    0U,	// VCVTANUDh
    0U,	// VCVTANUQf
    0U,	// VCVTANUQh
    0U,	// VCVTASD
    0U,	// VCVTASH
    0U,	// VCVTASS
    0U,	// VCVTAUD
    0U,	// VCVTAUH
    0U,	// VCVTAUS
    0U,	// VCVTBDH
    0U,	// VCVTBHD
    0U,	// VCVTBHS
    0U,	// VCVTBSH
    0U,	// VCVTDS
    0U,	// VCVTMNSDf
    0U,	// VCVTMNSDh
    0U,	// VCVTMNSQf
    0U,	// VCVTMNSQh
    0U,	// VCVTMNUDf
    0U,	// VCVTMNUDh
    0U,	// VCVTMNUQf
    0U,	// VCVTMNUQh
    0U,	// VCVTMSD
    0U,	// VCVTMSH
    0U,	// VCVTMSS
    0U,	// VCVTMUD
    0U,	// VCVTMUH
    0U,	// VCVTMUS
    0U,	// VCVTNNSDf
    0U,	// VCVTNNSDh
    0U,	// VCVTNNSQf
    0U,	// VCVTNNSQh
    0U,	// VCVTNNUDf
    0U,	// VCVTNNUDh
    0U,	// VCVTNNUQf
    0U,	// VCVTNNUQh
    0U,	// VCVTNSD
    0U,	// VCVTNSH
    0U,	// VCVTNSS
    0U,	// VCVTNUD
    0U,	// VCVTNUH
    0U,	// VCVTNUS
    0U,	// VCVTPNSDf
    0U,	// VCVTPNSDh
    0U,	// VCVTPNSQf
    0U,	// VCVTPNSQh
    0U,	// VCVTPNUDf
    0U,	// VCVTPNUDh
    0U,	// VCVTPNUQf
    0U,	// VCVTPNUQh
    0U,	// VCVTPSD
    0U,	// VCVTPSH
    0U,	// VCVTPSS
    0U,	// VCVTPUD
    0U,	// VCVTPUH
    0U,	// VCVTPUS
    0U,	// VCVTSD
    0U,	// VCVTTDH
    0U,	// VCVTTHD
    0U,	// VCVTTHS
    0U,	// VCVTTSH
    0U,	// VCVTf2h
    0U,	// VCVTf2sd
    0U,	// VCVTf2sq
    0U,	// VCVTf2ud
    0U,	// VCVTf2uq
    35U,	// VCVTf2xsd
    35U,	// VCVTf2xsq
    35U,	// VCVTf2xud
    35U,	// VCVTf2xuq
    0U,	// VCVTh2f
    0U,	// VCVTh2sd
    0U,	// VCVTh2sq
    0U,	// VCVTh2ud
    0U,	// VCVTh2uq
    35U,	// VCVTh2xsd
    35U,	// VCVTh2xsq
    35U,	// VCVTh2xud
    35U,	// VCVTh2xuq
    0U,	// VCVTs2fd
    0U,	// VCVTs2fq
    0U,	// VCVTs2hd
    0U,	// VCVTs2hq
    0U,	// VCVTu2fd
    0U,	// VCVTu2fq
    0U,	// VCVTu2hd
    0U,	// VCVTu2hq
    35U,	// VCVTxs2fd
    35U,	// VCVTxs2fq
    35U,	// VCVTxs2hd
    35U,	// VCVTxs2hq
    35U,	// VCVTxu2fd
    35U,	// VCVTxu2fq
    35U,	// VCVTxu2hd
    35U,	// VCVTxu2hq
    70705U,	// VDIVD
    70705U,	// VDIVH
    70705U,	// VDIVS
    1024U,	// VDUP16d
    1024U,	// VDUP16q
    1024U,	// VDUP32d
    1024U,	// VDUP32q
    1024U,	// VDUP8d
    1024U,	// VDUP8q
    9216U,	// VDUPLN16d
    9216U,	// VDUPLN16q
    9216U,	// VDUPLN32d
    9216U,	// VDUPLN32q
    9216U,	// VDUPLN8d
    9216U,	// VDUPLN8q
    0U,	// VEORd
    0U,	// VEORq
    35651584U,	// VEXTd16
    35651584U,	// VEXTd32
    35651584U,	// VEXTd8
    35651584U,	// VEXTq16
    35651584U,	// VEXTq32
    35651584U,	// VEXTq64
    35651584U,	// VEXTq8
    68659U,	// VFMAD
    68659U,	// VFMAH
    1112U,	// VFMALD
    10328U,	// VFMALDI
    1112U,	// VFMALQ
    10328U,	// VFMALQI
    68659U,	// VFMAS
    68659U,	// VFMAfd
    68659U,	// VFMAfq
    68659U,	// VFMAhd
    68659U,	// VFMAhq
    68659U,	// VFMSD
    68659U,	// VFMSH
    1112U,	// VFMSLD
    10328U,	// VFMSLDI
    1112U,	// VFMSLQ
    10328U,	// VFMSLQI
    68659U,	// VFMSS
    68659U,	// VFMSfd
    68659U,	// VFMSfq
    68659U,	// VFMShd
    68659U,	// VFMShq
    68659U,	// VFNMAD
    68659U,	// VFNMAH
    68659U,	// VFNMAS
    68659U,	// VFNMSD
    68659U,	// VFNMSH
    68659U,	// VFNMSS
    9216U,	// VGETLNi32
    3U,	// VGETLNs16
    3U,	// VGETLNs8
    3U,	// VGETLNu16
    3U,	// VGETLNu8
    1112U,	// VHADDsv16i8
    1112U,	// VHADDsv2i32
    1112U,	// VHADDsv4i16
    1112U,	// VHADDsv4i32
    1112U,	// VHADDsv8i16
    1112U,	// VHADDsv8i8
    1112U,	// VHADDuv16i8
    1112U,	// VHADDuv2i32
    1112U,	// VHADDuv4i16
    1112U,	// VHADDuv4i32
    1112U,	// VHADDuv8i16
    1112U,	// VHADDuv8i8
    1112U,	// VHSUBsv16i8
    1112U,	// VHSUBsv2i32
    1112U,	// VHSUBsv4i16
    1112U,	// VHSUBsv4i32
    1112U,	// VHSUBsv8i16
    1112U,	// VHSUBsv8i8
    1112U,	// VHSUBuv16i8
    1112U,	// VHSUBuv2i32
    1112U,	// VHSUBuv4i16
    1112U,	// VHSUBuv4i32
    1112U,	// VHSUBuv8i16
    1112U,	// VHSUBuv8i8
    0U,	// VINSH
    0U,	// VJCVT
    32U,	// VLD1DUPd16
    44U,	// VLD1DUPd16wb_fixed
    11316U,	// VLD1DUPd16wb_register
    32U,	// VLD1DUPd32
    44U,	// VLD1DUPd32wb_fixed
    11316U,	// VLD1DUPd32wb_register
    32U,	// VLD1DUPd8
    44U,	// VLD1DUPd8wb_fixed
    11316U,	// VLD1DUPd8wb_register
    32U,	// VLD1DUPq16
    44U,	// VLD1DUPq16wb_fixed
    11316U,	// VLD1DUPq16wb_register
    32U,	// VLD1DUPq32
    44U,	// VLD1DUPq32wb_fixed
    11316U,	// VLD1DUPq32wb_register
    32U,	// VLD1DUPq8
    44U,	// VLD1DUPq8wb_fixed
    11316U,	// VLD1DUPq8wb_register
    700652U,	// VLD1LNd16
    733428U,	// VLD1LNd16_UPD
    700652U,	// VLD1LNd32
    733428U,	// VLD1LNd32_UPD
    700652U,	// VLD1LNd8
    733428U,	// VLD1LNd8_UPD
    0U,	// VLD1LNq16Pseudo
    0U,	// VLD1LNq16Pseudo_UPD
    0U,	// VLD1LNq32Pseudo
    0U,	// VLD1LNq32Pseudo_UPD
    0U,	// VLD1LNq8Pseudo
    0U,	// VLD1LNq8Pseudo_UPD
    32U,	// VLD1d16
    32U,	// VLD1d16Q
    0U,	// VLD1d16QPseudo
    44U,	// VLD1d16Qwb_fixed
    11316U,	// VLD1d16Qwb_register
    32U,	// VLD1d16T
    0U,	// VLD1d16TPseudo
    44U,	// VLD1d16Twb_fixed
    11316U,	// VLD1d16Twb_register
    44U,	// VLD1d16wb_fixed
    11316U,	// VLD1d16wb_register
    32U,	// VLD1d32
    32U,	// VLD1d32Q
    0U,	// VLD1d32QPseudo
    44U,	// VLD1d32Qwb_fixed
    11316U,	// VLD1d32Qwb_register
    32U,	// VLD1d32T
    0U,	// VLD1d32TPseudo
    44U,	// VLD1d32Twb_fixed
    11316U,	// VLD1d32Twb_register
    44U,	// VLD1d32wb_fixed
    11316U,	// VLD1d32wb_register
    32U,	// VLD1d64
    32U,	// VLD1d64Q
    0U,	// VLD1d64QPseudo
    0U,	// VLD1d64QPseudoWB_fixed
    0U,	// VLD1d64QPseudoWB_register
    44U,	// VLD1d64Qwb_fixed
    11316U,	// VLD1d64Qwb_register
    32U,	// VLD1d64T
    0U,	// VLD1d64TPseudo
    0U,	// VLD1d64TPseudoWB_fixed
    0U,	// VLD1d64TPseudoWB_register
    44U,	// VLD1d64Twb_fixed
    11316U,	// VLD1d64Twb_register
    44U,	// VLD1d64wb_fixed
    11316U,	// VLD1d64wb_register
    32U,	// VLD1d8
    32U,	// VLD1d8Q
    0U,	// VLD1d8QPseudo
    44U,	// VLD1d8Qwb_fixed
    11316U,	// VLD1d8Qwb_register
    32U,	// VLD1d8T
    0U,	// VLD1d8TPseudo
    44U,	// VLD1d8Twb_fixed
    11316U,	// VLD1d8Twb_register
    44U,	// VLD1d8wb_fixed
    11316U,	// VLD1d8wb_register
    32U,	// VLD1q16
    0U,	// VLD1q16HighQPseudo
    0U,	// VLD1q16HighTPseudo
    0U,	// VLD1q16LowQPseudo_UPD
    0U,	// VLD1q16LowTPseudo_UPD
    44U,	// VLD1q16wb_fixed
    11316U,	// VLD1q16wb_register
    32U,	// VLD1q32
    0U,	// VLD1q32HighQPseudo
    0U,	// VLD1q32HighTPseudo
    0U,	// VLD1q32LowQPseudo_UPD
    0U,	// VLD1q32LowTPseudo_UPD
    44U,	// VLD1q32wb_fixed
    11316U,	// VLD1q32wb_register
    32U,	// VLD1q64
    0U,	// VLD1q64HighQPseudo
    0U,	// VLD1q64HighTPseudo
    0U,	// VLD1q64LowQPseudo_UPD
    0U,	// VLD1q64LowTPseudo_UPD
    44U,	// VLD1q64wb_fixed
    11316U,	// VLD1q64wb_register
    32U,	// VLD1q8
    0U,	// VLD1q8HighQPseudo
    0U,	// VLD1q8HighTPseudo
    0U,	// VLD1q8LowQPseudo_UPD
    0U,	// VLD1q8LowTPseudo_UPD
    44U,	// VLD1q8wb_fixed
    11316U,	// VLD1q8wb_register
    32U,	// VLD2DUPd16
    44U,	// VLD2DUPd16wb_fixed
    11316U,	// VLD2DUPd16wb_register
    32U,	// VLD2DUPd16x2
    44U,	// VLD2DUPd16x2wb_fixed
    11316U,	// VLD2DUPd16x2wb_register
    32U,	// VLD2DUPd32
    44U,	// VLD2DUPd32wb_fixed
    11316U,	// VLD2DUPd32wb_register
    32U,	// VLD2DUPd32x2
    44U,	// VLD2DUPd32x2wb_fixed
    11316U,	// VLD2DUPd32x2wb_register
    32U,	// VLD2DUPd8
    44U,	// VLD2DUPd8wb_fixed
    11316U,	// VLD2DUPd8wb_register
    32U,	// VLD2DUPd8x2
    44U,	// VLD2DUPd8x2wb_fixed
    11316U,	// VLD2DUPd8x2wb_register
    0U,	// VLD2DUPq16EvenPseudo
    0U,	// VLD2DUPq16OddPseudo
    0U,	// VLD2DUPq32EvenPseudo
    0U,	// VLD2DUPq32OddPseudo
    0U,	// VLD2DUPq8EvenPseudo
    0U,	// VLD2DUPq8OddPseudo
    767220U,	// VLD2LNd16
    0U,	// VLD2LNd16Pseudo
    0U,	// VLD2LNd16Pseudo_UPD
    801020U,	// VLD2LNd16_UPD
    767220U,	// VLD2LNd32
    0U,	// VLD2LNd32Pseudo
    0U,	// VLD2LNd32Pseudo_UPD
    801020U,	// VLD2LNd32_UPD
    767220U,	// VLD2LNd8
    0U,	// VLD2LNd8Pseudo
    0U,	// VLD2LNd8Pseudo_UPD
    801020U,	// VLD2LNd8_UPD
    767220U,	// VLD2LNq16
    0U,	// VLD2LNq16Pseudo
    0U,	// VLD2LNq16Pseudo_UPD
    801020U,	// VLD2LNq16_UPD
    767220U,	// VLD2LNq32
    0U,	// VLD2LNq32Pseudo
    0U,	// VLD2LNq32Pseudo_UPD
    801020U,	// VLD2LNq32_UPD
    32U,	// VLD2b16
    44U,	// VLD2b16wb_fixed
    11316U,	// VLD2b16wb_register
    32U,	// VLD2b32
    44U,	// VLD2b32wb_fixed
    11316U,	// VLD2b32wb_register
    32U,	// VLD2b8
    44U,	// VLD2b8wb_fixed
    11316U,	// VLD2b8wb_register
    32U,	// VLD2d16
    44U,	// VLD2d16wb_fixed
    11316U,	// VLD2d16wb_register
    32U,	// VLD2d32
    44U,	// VLD2d32wb_fixed
    11316U,	// VLD2d32wb_register
    32U,	// VLD2d8
    44U,	// VLD2d8wb_fixed
    11316U,	// VLD2d8wb_register
    32U,	// VLD2q16
    0U,	// VLD2q16Pseudo
    0U,	// VLD2q16PseudoWB_fixed
    0U,	// VLD2q16PseudoWB_register
    44U,	// VLD2q16wb_fixed
    11316U,	// VLD2q16wb_register
    32U,	// VLD2q32
    0U,	// VLD2q32Pseudo
    0U,	// VLD2q32PseudoWB_fixed
    0U,	// VLD2q32PseudoWB_register
    44U,	// VLD2q32wb_fixed
    11316U,	// VLD2q32wb_register
    32U,	// VLD2q8
    0U,	// VLD2q8Pseudo
    0U,	// VLD2q8PseudoWB_fixed
    0U,	// VLD2q8PseudoWB_register
    44U,	// VLD2q8wb_fixed
    11316U,	// VLD2q8wb_register
    15620U,	// VLD3DUPd16
    0U,	// VLD3DUPd16Pseudo
    0U,	// VLD3DUPd16Pseudo_UPD
    835844U,	// VLD3DUPd16_UPD
    15620U,	// VLD3DUPd32
    0U,	// VLD3DUPd32Pseudo
    0U,	// VLD3DUPd32Pseudo_UPD
    835844U,	// VLD3DUPd32_UPD
    15620U,	// VLD3DUPd8
    0U,	// VLD3DUPd8Pseudo
    0U,	// VLD3DUPd8Pseudo_UPD
    835844U,	// VLD3DUPd8_UPD
    15620U,	// VLD3DUPq16
    0U,	// VLD3DUPq16EvenPseudo
    0U,	// VLD3DUPq16OddPseudo
    835844U,	// VLD3DUPq16_UPD
    15620U,	// VLD3DUPq32
    0U,	// VLD3DUPq32EvenPseudo
    0U,	// VLD3DUPq32OddPseudo
    835844U,	// VLD3DUPq32_UPD
    15620U,	// VLD3DUPq8
    0U,	// VLD3DUPq8EvenPseudo
    0U,	// VLD3DUPq8OddPseudo
    835844U,	// VLD3DUPq8_UPD
    866556U,	// VLD3LNd16
    0U,	// VLD3LNd16Pseudo
    0U,	// VLD3LNd16Pseudo_UPD
    897292U,	// VLD3LNd16_UPD
    866556U,	// VLD3LNd32
    0U,	// VLD3LNd32Pseudo
    0U,	// VLD3LNd32Pseudo_UPD
    897292U,	// VLD3LNd32_UPD
    866556U,	// VLD3LNd8
    0U,	// VLD3LNd8Pseudo
    0U,	// VLD3LNd8Pseudo_UPD
    897292U,	// VLD3LNd8_UPD
    866556U,	// VLD3LNq16
    0U,	// VLD3LNq16Pseudo
    0U,	// VLD3LNq16Pseudo_UPD
    897292U,	// VLD3LNq16_UPD
    866556U,	// VLD3LNq32
    0U,	// VLD3LNq32Pseudo
    0U,	// VLD3LNq32Pseudo_UPD
    897292U,	// VLD3LNq32_UPD
    119537664U,	// VLD3d16
    0U,	// VLD3d16Pseudo
    0U,	// VLD3d16Pseudo_UPD
    153092096U,	// VLD3d16_UPD
    119537664U,	// VLD3d32
    0U,	// VLD3d32Pseudo
    0U,	// VLD3d32Pseudo_UPD
    153092096U,	// VLD3d32_UPD
    119537664U,	// VLD3d8
    0U,	// VLD3d8Pseudo
    0U,	// VLD3d8Pseudo_UPD
    153092096U,	// VLD3d8_UPD
    119537664U,	// VLD3q16
    0U,	// VLD3q16Pseudo_UPD
    153092096U,	// VLD3q16_UPD
    0U,	// VLD3q16oddPseudo
    0U,	// VLD3q16oddPseudo_UPD
    119537664U,	// VLD3q32
    0U,	// VLD3q32Pseudo_UPD
    153092096U,	// VLD3q32_UPD
    0U,	// VLD3q32oddPseudo
    0U,	// VLD3q32oddPseudo_UPD
    119537664U,	// VLD3q8
    0U,	// VLD3q8Pseudo_UPD
    153092096U,	// VLD3q8_UPD
    0U,	// VLD3q8oddPseudo
    0U,	// VLD3q8oddPseudo_UPD
    82196U,	// VLD4DUPd16
    0U,	// VLD4DUPd16Pseudo
    0U,	// VLD4DUPd16Pseudo_UPD
    17684U,	// VLD4DUPd16_UPD
    82196U,	// VLD4DUPd32
    0U,	// VLD4DUPd32Pseudo
    0U,	// VLD4DUPd32Pseudo_UPD
    17684U,	// VLD4DUPd32_UPD
    82196U,	// VLD4DUPd8
    0U,	// VLD4DUPd8Pseudo
    0U,	// VLD4DUPd8Pseudo_UPD
    17684U,	// VLD4DUPd8_UPD
    82196U,	// VLD4DUPq16
    0U,	// VLD4DUPq16EvenPseudo
    0U,	// VLD4DUPq16OddPseudo
    17684U,	// VLD4DUPq16_UPD
    82196U,	// VLD4DUPq32
    0U,	// VLD4DUPq32EvenPseudo
    0U,	// VLD4DUPq32OddPseudo
    17684U,	// VLD4DUPq32_UPD
    82196U,	// VLD4DUPq8
    0U,	// VLD4DUPq8EvenPseudo
    0U,	// VLD4DUPq8OddPseudo
    17684U,	// VLD4DUPq8_UPD
    189347084U,	// VLD4LNd16
    0U,	// VLD4LNd16Pseudo
    0U,	// VLD4LNd16Pseudo_UPD
    284U,	// VLD4LNd16_UPD
    189347084U,	// VLD4LNd32
    0U,	// VLD4LNd32Pseudo
    0U,	// VLD4LNd32Pseudo_UPD
    284U,	// VLD4LNd32_UPD
    189347084U,	// VLD4LNd8
    0U,	// VLD4LNd8Pseudo
    0U,	// VLD4LNd8Pseudo_UPD
    284U,	// VLD4LNd8_UPD
    189347084U,	// VLD4LNq16
    0U,	// VLD4LNq16Pseudo
    0U,	// VLD4LNq16Pseudo_UPD
    284U,	// VLD4LNq16_UPD
    189347084U,	// VLD4LNq32
    0U,	// VLD4LNq32Pseudo
    0U,	// VLD4LNq32Pseudo_UPD
    284U,	// VLD4LNq32_UPD
    572522496U,	// VLD4d16
    0U,	// VLD4d16Pseudo
    0U,	// VLD4d16Pseudo_UPD
    1646264320U,	// VLD4d16_UPD
    572522496U,	// VLD4d32
    0U,	// VLD4d32Pseudo
    0U,	// VLD4d32Pseudo_UPD
    1646264320U,	// VLD4d32_UPD
    572522496U,	// VLD4d8
    0U,	// VLD4d8Pseudo
    0U,	// VLD4d8Pseudo_UPD
    1646264320U,	// VLD4d8_UPD
    572522496U,	// VLD4q16
    0U,	// VLD4q16Pseudo_UPD
    1646264320U,	// VLD4q16_UPD
    0U,	// VLD4q16oddPseudo
    0U,	// VLD4q16oddPseudo_UPD
    572522496U,	// VLD4q32
    0U,	// VLD4q32Pseudo_UPD
    1646264320U,	// VLD4q32_UPD
    0U,	// VLD4q32oddPseudo
    0U,	// VLD4q32oddPseudo_UPD
    572522496U,	// VLD4q8
    0U,	// VLD4q8Pseudo_UPD
    1646264320U,	// VLD4q8_UPD
    0U,	// VLD4q8oddPseudo
    0U,	// VLD4q8oddPseudo_UPD
    33U,	// VLDMDDB_UPD
    1136U,	// VLDMDIA
    33U,	// VLDMDIA_UPD
    0U,	// VLDMQIA
    33U,	// VLDMSDB_UPD
    1136U,	// VLDMSIA
    33U,	// VLDMSIA_UPD
    288U,	// VLDRD
    296U,	// VLDRH
    288U,	// VLDRS
    0U,	// VLLDM
    0U,	// VLSTM
    1112U,	// VMAXNMD
    1112U,	// VMAXNMH
    1112U,	// VMAXNMNDf
    1112U,	// VMAXNMNDh
    1112U,	// VMAXNMNQf
    1112U,	// VMAXNMNQh
    1112U,	// VMAXNMS
    70705U,	// VMAXfd
    70705U,	// VMAXfq
    70705U,	// VMAXhd
    70705U,	// VMAXhq
    1112U,	// VMAXsv16i8
    1112U,	// VMAXsv2i32
    1112U,	// VMAXsv4i16
    1112U,	// VMAXsv4i32
    1112U,	// VMAXsv8i16
    1112U,	// VMAXsv8i8
    1112U,	// VMAXuv16i8
    1112U,	// VMAXuv2i32
    1112U,	// VMAXuv4i16
    1112U,	// VMAXuv4i32
    1112U,	// VMAXuv8i16
    1112U,	// VMAXuv8i8
    1112U,	// VMINNMD
    1112U,	// VMINNMH
    1112U,	// VMINNMNDf
    1112U,	// VMINNMNDh
    1112U,	// VMINNMNQf
    1112U,	// VMINNMNQh
    1112U,	// VMINNMS
    70705U,	// VMINfd
    70705U,	// VMINfq
    70705U,	// VMINhd
    70705U,	// VMINhq
    1112U,	// VMINsv16i8
    1112U,	// VMINsv2i32
    1112U,	// VMINsv4i16
    1112U,	// VMINsv4i32
    1112U,	// VMINsv8i16
    1112U,	// VMINsv8i8
    1112U,	// VMINuv16i8
    1112U,	// VMINuv2i32
    1112U,	// VMINuv4i16
    1112U,	// VMINuv4i32
    1112U,	// VMINuv8i16
    1112U,	// VMINuv8i8
    68659U,	// VMLAD
    68659U,	// VMLAH
    73752U,	// VMLALslsv2i32
    73752U,	// VMLALslsv4i16
    73752U,	// VMLALsluv2i32
    73752U,	// VMLALsluv4i16
    1048U,	// VMLALsv2i64
    1048U,	// VMLALsv4i32
    1048U,	// VMLALsv8i16
    1048U,	// VMLALuv2i64
    1048U,	// VMLALuv4i32
    1048U,	// VMLALuv8i16
    68659U,	// VMLAS
    68659U,	// VMLAfd
    68659U,	// VMLAfq
    68659U,	// VMLAhd
    68659U,	// VMLAhq
    920627U,	// VMLAslfd
    920627U,	// VMLAslfq
    920627U,	// VMLAslhd
    920627U,	// VMLAslhq
    73752U,	// VMLAslv2i32
    73752U,	// VMLAslv4i16
    73752U,	// VMLAslv4i32
    73752U,	// VMLAslv8i16
    1048U,	// VMLAv16i8
    1048U,	// VMLAv2i32
    1048U,	// VMLAv4i16
    1048U,	// VMLAv4i32
    1048U,	// VMLAv8i16
    1048U,	// VMLAv8i8
    68659U,	// VMLSD
    68659U,	// VMLSH
    73752U,	// VMLSLslsv2i32
    73752U,	// VMLSLslsv4i16
    73752U,	// VMLSLsluv2i32
    73752U,	// VMLSLsluv4i16
    1048U,	// VMLSLsv2i64
    1048U,	// VMLSLsv4i32
    1048U,	// VMLSLsv8i16
    1048U,	// VMLSLuv2i64
    1048U,	// VMLSLuv4i32
    1048U,	// VMLSLuv8i16
    68659U,	// VMLSS
    68659U,	// VMLSfd
    68659U,	// VMLSfq
    68659U,	// VMLShd
    68659U,	// VMLShq
    920627U,	// VMLSslfd
    920627U,	// VMLSslfq
    920627U,	// VMLSslhd
    920627U,	// VMLSslhq
    73752U,	// VMLSslv2i32
    73752U,	// VMLSslv4i16
    73752U,	// VMLSslv4i32
    73752U,	// VMLSslv8i16
    1048U,	// VMLSv16i8
    1048U,	// VMLSv2i32
    1048U,	// VMLSv4i16
    1048U,	// VMLSv4i32
    1048U,	// VMLSv8i16
    1048U,	// VMLSv8i8
    33U,	// VMOVD
    0U,	// VMOVDRR
    0U,	// VMOVH
    33U,	// VMOVHR
    0U,	// VMOVLsv2i64
    0U,	// VMOVLsv4i32
    0U,	// VMOVLsv8i16
    0U,	// VMOVLuv2i64
    0U,	// VMOVLuv4i32
    0U,	// VMOVLuv8i16
    0U,	// VMOVNv2i32
    0U,	// VMOVNv4i16
    0U,	// VMOVNv8i8
    33U,	// VMOVRH
    0U,	// VMOVRRD
    35651584U,	// VMOVRRS
    1024U,	// VMOVRS
    33U,	// VMOVS
    1024U,	// VMOVSR
    35651584U,	// VMOVSRR
    0U,	// VMOVv16i8
    0U,	// VMOVv1i64
    1U,	// VMOVv2f32
    0U,	// VMOVv2i32
    0U,	// VMOVv2i64
    1U,	// VMOVv4f32
    0U,	// VMOVv4i16
    0U,	// VMOVv4i32
    0U,	// VMOVv8i16
    0U,	// VMOVv8i8
    4U,	// VMRS
    5U,	// VMRS_FPEXC
    5U,	// VMRS_FPINST
    5U,	// VMRS_FPINST2
    5U,	// VMRS_FPSID
    6U,	// VMRS_MVFR0
    6U,	// VMRS_MVFR1
    6U,	// VMRS_MVFR2
    0U,	// VMSR
    0U,	// VMSR_FPEXC
    0U,	// VMSR_FPINST
    0U,	// VMSR_FPINST2
    0U,	// VMSR_FPSID
    70705U,	// VMULD
    70705U,	// VMULH
    1112U,	// VMULLp64
    0U,	// VMULLp8
    10328U,	// VMULLslsv2i32
    10328U,	// VMULLslsv4i16
    10328U,	// VMULLsluv2i32
    10328U,	// VMULLsluv4i16
    1112U,	// VMULLsv2i64
    1112U,	// VMULLsv4i32
    1112U,	// VMULLsv8i16
    1112U,	// VMULLuv2i64
    1112U,	// VMULLuv4i32
    1112U,	// VMULLuv8i16
    70705U,	// VMULS
    70705U,	// VMULfd
    70705U,	// VMULfq
    70705U,	// VMULhd
    70705U,	// VMULhq
    0U,	// VMULpd
    0U,	// VMULpq
    955441U,	// VMULslfd
    955441U,	// VMULslfq
    955441U,	// VMULslhd
    955441U,	// VMULslhq
    10328U,	// VMULslv2i32
    10328U,	// VMULslv4i16
    10328U,	// VMULslv4i32
    10328U,	// VMULslv8i16
    1112U,	// VMULv16i8
    1112U,	// VMULv2i32
    1112U,	// VMULv4i16
    1112U,	// VMULv4i32
    1112U,	// VMULv8i16
    1112U,	// VMULv8i8
    1024U,	// VMVNd
    1024U,	// VMVNq
    0U,	// VMVNv2i32
    0U,	// VMVNv4i16
    0U,	// VMVNv4i32
    0U,	// VMVNv8i16
    33U,	// VNEGD
    33U,	// VNEGH
    33U,	// VNEGS
    33U,	// VNEGf32q
    33U,	// VNEGfd
    33U,	// VNEGhd
    33U,	// VNEGhq
    0U,	// VNEGs16d
    0U,	// VNEGs16q
    0U,	// VNEGs32d
    0U,	// VNEGs32q
    0U,	// VNEGs8d
    0U,	// VNEGs8q
    68659U,	// VNMLAD
    68659U,	// VNMLAH
    68659U,	// VNMLAS
    68659U,	// VNMLSD
    68659U,	// VNMLSH
    68659U,	// VNMLSS
    70705U,	// VNMULD
    70705U,	// VNMULH
    70705U,	// VNMULS
    0U,	// VORNd
    0U,	// VORNq
    0U,	// VORRd
    0U,	// VORRiv2i32
    0U,	// VORRiv4i16
    0U,	// VORRiv4i32
    0U,	// VORRiv8i16
    0U,	// VORRq
    0U,	// VPADALsv16i8
    0U,	// VPADALsv2i32
    0U,	// VPADALsv4i16
    0U,	// VPADALsv4i32
    0U,	// VPADALsv8i16
    0U,	// VPADALsv8i8
    0U,	// VPADALuv16i8
    0U,	// VPADALuv2i32
    0U,	// VPADALuv4i16
    0U,	// VPADALuv4i32
    0U,	// VPADALuv8i16
    0U,	// VPADALuv8i8
    0U,	// VPADDLsv16i8
    0U,	// VPADDLsv2i32
    0U,	// VPADDLsv4i16
    0U,	// VPADDLsv4i32
    0U,	// VPADDLsv8i16
    0U,	// VPADDLsv8i8
    0U,	// VPADDLuv16i8
    0U,	// VPADDLuv2i32
    0U,	// VPADDLuv4i16
    0U,	// VPADDLuv4i32
    0U,	// VPADDLuv8i16
    0U,	// VPADDLuv8i8
    70705U,	// VPADDf
    70705U,	// VPADDh
    1112U,	// VPADDi16
    1112U,	// VPADDi32
    1112U,	// VPADDi8
    70705U,	// VPMAXf
    70705U,	// VPMAXh
    1112U,	// VPMAXs16
    1112U,	// VPMAXs32
    1112U,	// VPMAXs8
    1112U,	// VPMAXu16
    1112U,	// VPMAXu32
    1112U,	// VPMAXu8
    70705U,	// VPMINf
    70705U,	// VPMINh
    1112U,	// VPMINs16
    1112U,	// VPMINs32
    1112U,	// VPMINs8
    1112U,	// VPMINu16
    1112U,	// VPMINu32
    1112U,	// VPMINu8
    0U,	// VQABSv16i8
    0U,	// VQABSv2i32
    0U,	// VQABSv4i16
    0U,	// VQABSv4i32
    0U,	// VQABSv8i16
    0U,	// VQABSv8i8
    1112U,	// VQADDsv16i8
    1112U,	// VQADDsv1i64
    1112U,	// VQADDsv2i32
    1112U,	// VQADDsv2i64
    1112U,	// VQADDsv4i16
    1112U,	// VQADDsv4i32
    1112U,	// VQADDsv8i16
    1112U,	// VQADDsv8i8
    1112U,	// VQADDuv16i8
    1112U,	// VQADDuv1i64
    1112U,	// VQADDuv2i32
    1112U,	// VQADDuv2i64
    1112U,	// VQADDuv4i16
    1112U,	// VQADDuv4i32
    1112U,	// VQADDuv8i16
    1112U,	// VQADDuv8i8
    73752U,	// VQDMLALslv2i32
    73752U,	// VQDMLALslv4i16
    1048U,	// VQDMLALv2i64
    1048U,	// VQDMLALv4i32
    73752U,	// VQDMLSLslv2i32
    73752U,	// VQDMLSLslv4i16
    1048U,	// VQDMLSLv2i64
    1048U,	// VQDMLSLv4i32
    10328U,	// VQDMULHslv2i32
    10328U,	// VQDMULHslv4i16
    10328U,	// VQDMULHslv4i32
    10328U,	// VQDMULHslv8i16
    1112U,	// VQDMULHv2i32
    1112U,	// VQDMULHv4i16
    1112U,	// VQDMULHv4i32
    1112U,	// VQDMULHv8i16
    10328U,	// VQDMULLslv2i32
    10328U,	// VQDMULLslv4i16
    1112U,	// VQDMULLv2i64
    1112U,	// VQDMULLv4i32
    0U,	// VQMOVNsuv2i32
    0U,	// VQMOVNsuv4i16
    0U,	// VQMOVNsuv8i8
    0U,	// VQMOVNsv2i32
    0U,	// VQMOVNsv4i16
    0U,	// VQMOVNsv8i8
    0U,	// VQMOVNuv2i32
    0U,	// VQMOVNuv4i16
    0U,	// VQMOVNuv8i8
    0U,	// VQNEGv16i8
    0U,	// VQNEGv2i32
    0U,	// VQNEGv4i16
    0U,	// VQNEGv4i32
    0U,	// VQNEGv8i16
    0U,	// VQNEGv8i8
    73752U,	// VQRDMLAHslv2i32
    73752U,	// VQRDMLAHslv4i16
    73752U,	// VQRDMLAHslv4i32
    73752U,	// VQRDMLAHslv8i16
    1048U,	// VQRDMLAHv2i32
    1048U,	// VQRDMLAHv4i16
    1048U,	// VQRDMLAHv4i32
    1048U,	// VQRDMLAHv8i16
    73752U,	// VQRDMLSHslv2i32
    73752U,	// VQRDMLSHslv4i16
    73752U,	// VQRDMLSHslv4i32
    73752U,	// VQRDMLSHslv8i16
    1048U,	// VQRDMLSHv2i32
    1048U,	// VQRDMLSHv4i16
    1048U,	// VQRDMLSHv4i32
    1048U,	// VQRDMLSHv8i16
    10328U,	// VQRDMULHslv2i32
    10328U,	// VQRDMULHslv4i16
    10328U,	// VQRDMULHslv4i32
    10328U,	// VQRDMULHslv8i16
    1112U,	// VQRDMULHv2i32
    1112U,	// VQRDMULHv4i16
    1112U,	// VQRDMULHv4i32
    1112U,	// VQRDMULHv8i16
    1112U,	// VQRSHLsv16i8
    1112U,	// VQRSHLsv1i64
    1112U,	// VQRSHLsv2i32
    1112U,	// VQRSHLsv2i64
    1112U,	// VQRSHLsv4i16
    1112U,	// VQRSHLsv4i32
    1112U,	// VQRSHLsv8i16
    1112U,	// VQRSHLsv8i8
    1112U,	// VQRSHLuv16i8
    1112U,	// VQRSHLuv1i64
    1112U,	// VQRSHLuv2i32
    1112U,	// VQRSHLuv2i64
    1112U,	// VQRSHLuv4i16
    1112U,	// VQRSHLuv4i32
    1112U,	// VQRSHLuv8i16
    1112U,	// VQRSHLuv8i8
    1112U,	// VQRSHRNsv2i32
    1112U,	// VQRSHRNsv4i16
    1112U,	// VQRSHRNsv8i8
    1112U,	// VQRSHRNuv2i32
    1112U,	// VQRSHRNuv4i16
    1112U,	// VQRSHRNuv8i8
    1112U,	// VQRSHRUNv2i32
    1112U,	// VQRSHRUNv4i16
    1112U,	// VQRSHRUNv8i8
    1112U,	// VQSHLsiv16i8
    1112U,	// VQSHLsiv1i64
    1112U,	// VQSHLsiv2i32
    1112U,	// VQSHLsiv2i64
    1112U,	// VQSHLsiv4i16
    1112U,	// VQSHLsiv4i32
    1112U,	// VQSHLsiv8i16
    1112U,	// VQSHLsiv8i8
    1112U,	// VQSHLsuv16i8
    1112U,	// VQSHLsuv1i64
    1112U,	// VQSHLsuv2i32
    1112U,	// VQSHLsuv2i64
    1112U,	// VQSHLsuv4i16
    1112U,	// VQSHLsuv4i32
    1112U,	// VQSHLsuv8i16
    1112U,	// VQSHLsuv8i8
    1112U,	// VQSHLsv16i8
    1112U,	// VQSHLsv1i64
    1112U,	// VQSHLsv2i32
    1112U,	// VQSHLsv2i64
    1112U,	// VQSHLsv4i16
    1112U,	// VQSHLsv4i32
    1112U,	// VQSHLsv8i16
    1112U,	// VQSHLsv8i8
    1112U,	// VQSHLuiv16i8
    1112U,	// VQSHLuiv1i64
    1112U,	// VQSHLuiv2i32
    1112U,	// VQSHLuiv2i64
    1112U,	// VQSHLuiv4i16
    1112U,	// VQSHLuiv4i32
    1112U,	// VQSHLuiv8i16
    1112U,	// VQSHLuiv8i8
    1112U,	// VQSHLuv16i8
    1112U,	// VQSHLuv1i64
    1112U,	// VQSHLuv2i32
    1112U,	// VQSHLuv2i64
    1112U,	// VQSHLuv4i16
    1112U,	// VQSHLuv4i32
    1112U,	// VQSHLuv8i16
    1112U,	// VQSHLuv8i8
    1112U,	// VQSHRNsv2i32
    1112U,	// VQSHRNsv4i16
    1112U,	// VQSHRNsv8i8
    1112U,	// VQSHRNuv2i32
    1112U,	// VQSHRNuv4i16
    1112U,	// VQSHRNuv8i8
    1112U,	// VQSHRUNv2i32
    1112U,	// VQSHRUNv4i16
    1112U,	// VQSHRUNv8i8
    1112U,	// VQSUBsv16i8
    1112U,	// VQSUBsv1i64
    1112U,	// VQSUBsv2i32
    1112U,	// VQSUBsv2i64
    1112U,	// VQSUBsv4i16
    1112U,	// VQSUBsv4i32
    1112U,	// VQSUBsv8i16
    1112U,	// VQSUBsv8i8
    1112U,	// VQSUBuv16i8
    1112U,	// VQSUBuv1i64
    1112U,	// VQSUBuv2i32
    1112U,	// VQSUBuv2i64
    1112U,	// VQSUBuv4i16
    1112U,	// VQSUBuv4i32
    1112U,	// VQSUBuv8i16
    1112U,	// VQSUBuv8i8
    1112U,	// VRADDHNv2i32
    1112U,	// VRADDHNv4i16
    1112U,	// VRADDHNv8i8
    0U,	// VRECPEd
    33U,	// VRECPEfd
    33U,	// VRECPEfq
    33U,	// VRECPEhd
    33U,	// VRECPEhq
    0U,	// VRECPEq
    70705U,	// VRECPSfd
    70705U,	// VRECPSfq
    70705U,	// VRECPShd
    70705U,	// VRECPShq
    1024U,	// VREV16d8
    1024U,	// VREV16q8
    1024U,	// VREV32d16
    1024U,	// VREV32d8
    1024U,	// VREV32q16
    1024U,	// VREV32q8
    1024U,	// VREV64d16
    1024U,	// VREV64d32
    1024U,	// VREV64d8
    1024U,	// VREV64q16
    1024U,	// VREV64q32
    1024U,	// VREV64q8
    1112U,	// VRHADDsv16i8
    1112U,	// VRHADDsv2i32
    1112U,	// VRHADDsv4i16
    1112U,	// VRHADDsv4i32
    1112U,	// VRHADDsv8i16
    1112U,	// VRHADDsv8i8
    1112U,	// VRHADDuv16i8
    1112U,	// VRHADDuv2i32
    1112U,	// VRHADDuv4i16
    1112U,	// VRHADDuv4i32
    1112U,	// VRHADDuv8i16
    1112U,	// VRHADDuv8i8
    0U,	// VRINTAD
    0U,	// VRINTAH
    0U,	// VRINTANDf
    0U,	// VRINTANDh
    0U,	// VRINTANQf
    0U,	// VRINTANQh
    0U,	// VRINTAS
    0U,	// VRINTMD
    0U,	// VRINTMH
    0U,	// VRINTMNDf
    0U,	// VRINTMNDh
    0U,	// VRINTMNQf
    0U,	// VRINTMNQh
    0U,	// VRINTMS
    0U,	// VRINTND
    0U,	// VRINTNH
    0U,	// VRINTNNDf
    0U,	// VRINTNNDh
    0U,	// VRINTNNQf
    0U,	// VRINTNNQh
    0U,	// VRINTNS
    0U,	// VRINTPD
    0U,	// VRINTPH
    0U,	// VRINTPNDf
    0U,	// VRINTPNDh
    0U,	// VRINTPNQf
    0U,	// VRINTPNQh
    0U,	// VRINTPS
    33U,	// VRINTRD
    33U,	// VRINTRH
    33U,	// VRINTRS
    33U,	// VRINTXD
    33U,	// VRINTXH
    0U,	// VRINTXNDf
    0U,	// VRINTXNDh
    0U,	// VRINTXNQf
    0U,	// VRINTXNQh
    33U,	// VRINTXS
    33U,	// VRINTZD
    33U,	// VRINTZH
    0U,	// VRINTZNDf
    0U,	// VRINTZNDh
    0U,	// VRINTZNQf
    0U,	// VRINTZNQh
    33U,	// VRINTZS
    1112U,	// VRSHLsv16i8
    1112U,	// VRSHLsv1i64
    1112U,	// VRSHLsv2i32
    1112U,	// VRSHLsv2i64
    1112U,	// VRSHLsv4i16
    1112U,	// VRSHLsv4i32
    1112U,	// VRSHLsv8i16
    1112U,	// VRSHLsv8i8
    1112U,	// VRSHLuv16i8
    1112U,	// VRSHLuv1i64
    1112U,	// VRSHLuv2i32
    1112U,	// VRSHLuv2i64
    1112U,	// VRSHLuv4i16
    1112U,	// VRSHLuv4i32
    1112U,	// VRSHLuv8i16
    1112U,	// VRSHLuv8i8
    1112U,	// VRSHRNv2i32
    1112U,	// VRSHRNv4i16
    1112U,	// VRSHRNv8i8
    1112U,	// VRSHRsv16i8
    1112U,	// VRSHRsv1i64
    1112U,	// VRSHRsv2i32
    1112U,	// VRSHRsv2i64
    1112U,	// VRSHRsv4i16
    1112U,	// VRSHRsv4i32
    1112U,	// VRSHRsv8i16
    1112U,	// VRSHRsv8i8
    1112U,	// VRSHRuv16i8
    1112U,	// VRSHRuv1i64
    1112U,	// VRSHRuv2i32
    1112U,	// VRSHRuv2i64
    1112U,	// VRSHRuv4i16
    1112U,	// VRSHRuv4i32
    1112U,	// VRSHRuv8i16
    1112U,	// VRSHRuv8i8
    0U,	// VRSQRTEd
    33U,	// VRSQRTEfd
    33U,	// VRSQRTEfq
    33U,	// VRSQRTEhd
    33U,	// VRSQRTEhq
    0U,	// VRSQRTEq
    70705U,	// VRSQRTSfd
    70705U,	// VRSQRTSfq
    70705U,	// VRSQRTShd
    70705U,	// VRSQRTShq
    1048U,	// VRSRAsv16i8
    1048U,	// VRSRAsv1i64
    1048U,	// VRSRAsv2i32
    1048U,	// VRSRAsv2i64
    1048U,	// VRSRAsv4i16
    1048U,	// VRSRAsv4i32
    1048U,	// VRSRAsv8i16
    1048U,	// VRSRAsv8i8
    1048U,	// VRSRAuv16i8
    1048U,	// VRSRAuv1i64
    1048U,	// VRSRAuv2i32
    1048U,	// VRSRAuv2i64
    1048U,	// VRSRAuv4i16
    1048U,	// VRSRAuv4i32
    1048U,	// VRSRAuv8i16
    1048U,	// VRSRAuv8i8
    1112U,	// VRSUBHNv2i32
    1112U,	// VRSUBHNv4i16
    1112U,	// VRSUBHNv8i8
    0U,	// VSDOTD
    0U,	// VSDOTDI
    0U,	// VSDOTQ
    0U,	// VSDOTQI
    1112U,	// VSELEQD
    1112U,	// VSELEQH
    1112U,	// VSELEQS
    1112U,	// VSELGED
    1112U,	// VSELGEH
    1112U,	// VSELGES
    1112U,	// VSELGTD
    1112U,	// VSELGTH
    1112U,	// VSELGTS
    1112U,	// VSELVSD
    1112U,	// VSELVSH
    1112U,	// VSELVSS
    6U,	// VSETLNi16
    6U,	// VSETLNi32
    6U,	// VSETLNi8
    1112U,	// VSHLLi16
    1112U,	// VSHLLi32
    1112U,	// VSHLLi8
    1112U,	// VSHLLsv2i64
    1112U,	// VSHLLsv4i32
    1112U,	// VSHLLsv8i16
    1112U,	// VSHLLuv2i64
    1112U,	// VSHLLuv4i32
    1112U,	// VSHLLuv8i16
    1112U,	// VSHLiv16i8
    1112U,	// VSHLiv1i64
    1112U,	// VSHLiv2i32
    1112U,	// VSHLiv2i64
    1112U,	// VSHLiv4i16
    1112U,	// VSHLiv4i32
    1112U,	// VSHLiv8i16
    1112U,	// VSHLiv8i8
    1112U,	// VSHLsv16i8
    1112U,	// VSHLsv1i64
    1112U,	// VSHLsv2i32
    1112U,	// VSHLsv2i64
    1112U,	// VSHLsv4i16
    1112U,	// VSHLsv4i32
    1112U,	// VSHLsv8i16
    1112U,	// VSHLsv8i8
    1112U,	// VSHLuv16i8
    1112U,	// VSHLuv1i64
    1112U,	// VSHLuv2i32
    1112U,	// VSHLuv2i64
    1112U,	// VSHLuv4i16
    1112U,	// VSHLuv4i32
    1112U,	// VSHLuv8i16
    1112U,	// VSHLuv8i8
    1112U,	// VSHRNv2i32
    1112U,	// VSHRNv4i16
    1112U,	// VSHRNv8i8
    1112U,	// VSHRsv16i8
    1112U,	// VSHRsv1i64
    1112U,	// VSHRsv2i32
    1112U,	// VSHRsv2i64
    1112U,	// VSHRsv4i16
    1112U,	// VSHRsv4i32
    1112U,	// VSHRsv8i16
    1112U,	// VSHRsv8i8
    1112U,	// VSHRuv16i8
    1112U,	// VSHRuv1i64
    1112U,	// VSHRuv2i32
    1112U,	// VSHRuv2i64
    1112U,	// VSHRuv4i16
    1112U,	// VSHRuv4i32
    1112U,	// VSHRuv8i16
    1112U,	// VSHRuv8i8
    0U,	// VSHTOD
    7U,	// VSHTOH
    0U,	// VSHTOS
    0U,	// VSITOD
    0U,	// VSITOH
    0U,	// VSITOS
    589912U,	// VSLIv16i8
    589912U,	// VSLIv1i64
    589912U,	// VSLIv2i32
    589912U,	// VSLIv2i64
    589912U,	// VSLIv4i16
    589912U,	// VSLIv4i32
    589912U,	// VSLIv8i16
    589912U,	// VSLIv8i8
    7U,	// VSLTOD
    7U,	// VSLTOH
    7U,	// VSLTOS
    33U,	// VSQRTD
    33U,	// VSQRTH
    33U,	// VSQRTS
    1048U,	// VSRAsv16i8
    1048U,	// VSRAsv1i64
    1048U,	// VSRAsv2i32
    1048U,	// VSRAsv2i64
    1048U,	// VSRAsv4i16
    1048U,	// VSRAsv4i32
    1048U,	// VSRAsv8i16
    1048U,	// VSRAsv8i8
    1048U,	// VSRAuv16i8
    1048U,	// VSRAuv1i64
    1048U,	// VSRAuv2i32
    1048U,	// VSRAuv2i64
    1048U,	// VSRAuv4i16
    1048U,	// VSRAuv4i32
    1048U,	// VSRAuv8i16
    1048U,	// VSRAuv8i8
    589912U,	// VSRIv16i8
    589912U,	// VSRIv1i64
    589912U,	// VSRIv2i32
    589912U,	// VSRIv2i64
    589912U,	// VSRIv4i16
    589912U,	// VSRIv4i32
    589912U,	// VSRIv8i16
    589912U,	// VSRIv8i8
    308U,	// VST1LNd16
    23769404U,	// VST1LNd16_UPD
    308U,	// VST1LNd32
    23769404U,	// VST1LNd32_UPD
    308U,	// VST1LNd8
    23769404U,	// VST1LNd8_UPD
    0U,	// VST1LNq16Pseudo
    0U,	// VST1LNq16Pseudo_UPD
    0U,	// VST1LNq32Pseudo
    0U,	// VST1LNq32Pseudo_UPD
    0U,	// VST1LNq8Pseudo
    0U,	// VST1LNq8Pseudo_UPD
    0U,	// VST1d16
    0U,	// VST1d16Q
    0U,	// VST1d16QPseudo
    0U,	// VST1d16Qwb_fixed
    0U,	// VST1d16Qwb_register
    0U,	// VST1d16T
    0U,	// VST1d16TPseudo
    0U,	// VST1d16Twb_fixed
    0U,	// VST1d16Twb_register
    0U,	// VST1d16wb_fixed
    0U,	// VST1d16wb_register
    0U,	// VST1d32
    0U,	// VST1d32Q
    0U,	// VST1d32QPseudo
    0U,	// VST1d32Qwb_fixed
    0U,	// VST1d32Qwb_register
    0U,	// VST1d32T
    0U,	// VST1d32TPseudo
    0U,	// VST1d32Twb_fixed
    0U,	// VST1d32Twb_register
    0U,	// VST1d32wb_fixed
    0U,	// VST1d32wb_register
    0U,	// VST1d64
    0U,	// VST1d64Q
    0U,	// VST1d64QPseudo
    0U,	// VST1d64QPseudoWB_fixed
    0U,	// VST1d64QPseudoWB_register
    0U,	// VST1d64Qwb_fixed
    0U,	// VST1d64Qwb_register
    0U,	// VST1d64T
    0U,	// VST1d64TPseudo
    0U,	// VST1d64TPseudoWB_fixed
    0U,	// VST1d64TPseudoWB_register
    0U,	// VST1d64Twb_fixed
    0U,	// VST1d64Twb_register
    0U,	// VST1d64wb_fixed
    0U,	// VST1d64wb_register
    0U,	// VST1d8
    0U,	// VST1d8Q
    0U,	// VST1d8QPseudo
    0U,	// VST1d8Qwb_fixed
    0U,	// VST1d8Qwb_register
    0U,	// VST1d8T
    0U,	// VST1d8TPseudo
    0U,	// VST1d8Twb_fixed
    0U,	// VST1d8Twb_register
    0U,	// VST1d8wb_fixed
    0U,	// VST1d8wb_register
    0U,	// VST1q16
    0U,	// VST1q16HighQPseudo
    0U,	// VST1q16HighTPseudo
    0U,	// VST1q16LowQPseudo_UPD
    0U,	// VST1q16LowTPseudo_UPD
    0U,	// VST1q16wb_fixed
    0U,	// VST1q16wb_register
    0U,	// VST1q32
    0U,	// VST1q32HighQPseudo
    0U,	// VST1q32HighTPseudo
    0U,	// VST1q32LowQPseudo_UPD
    0U,	// VST1q32LowTPseudo_UPD
    0U,	// VST1q32wb_fixed
    0U,	// VST1q32wb_register
    0U,	// VST1q64
    0U,	// VST1q64HighQPseudo
    0U,	// VST1q64HighTPseudo
    0U,	// VST1q64LowQPseudo_UPD
    0U,	// VST1q64LowTPseudo_UPD
    0U,	// VST1q64wb_fixed
    0U,	// VST1q64wb_register
    0U,	// VST1q8
    0U,	// VST1q8HighQPseudo
    0U,	// VST1q8HighTPseudo
    0U,	// VST1q8LowQPseudo_UPD
    0U,	// VST1q8LowTPseudo_UPD
    0U,	// VST1q8wb_fixed
    0U,	// VST1q8wb_register
    222901484U,	// VST2LNd16
    0U,	// VST2LNd16Pseudo
    0U,	// VST2LNd16Pseudo_UPD
    996596U,	// VST2LNd16_UPD
    222901484U,	// VST2LNd32
    0U,	// VST2LNd32Pseudo
    0U,	// VST2LNd32Pseudo_UPD
    996596U,	// VST2LNd32_UPD
    222901484U,	// VST2LNd8
    0U,	// VST2LNd8Pseudo
    0U,	// VST2LNd8Pseudo_UPD
    996596U,	// VST2LNd8_UPD
    222901484U,	// VST2LNq16
    0U,	// VST2LNq16Pseudo
    0U,	// VST2LNq16Pseudo_UPD
    996596U,	// VST2LNq16_UPD
    222901484U,	// VST2LNq32
    0U,	// VST2LNq32Pseudo
    0U,	// VST2LNq32Pseudo_UPD
    996596U,	// VST2LNq32_UPD
    0U,	// VST2b16
    0U,	// VST2b16wb_fixed
    0U,	// VST2b16wb_register
    0U,	// VST2b32
    0U,	// VST2b32wb_fixed
    0U,	// VST2b32wb_register
    0U,	// VST2b8
    0U,	// VST2b8wb_fixed
    0U,	// VST2b8wb_register
    0U,	// VST2d16
    0U,	// VST2d16wb_fixed
    0U,	// VST2d16wb_register
    0U,	// VST2d32
    0U,	// VST2d32wb_fixed
    0U,	// VST2d32wb_register
    0U,	// VST2d8
    0U,	// VST2d8wb_fixed
    0U,	// VST2d8wb_register
    0U,	// VST2q16
    0U,	// VST2q16Pseudo
    0U,	// VST2q16PseudoWB_fixed
    0U,	// VST2q16PseudoWB_register
    0U,	// VST2q16wb_fixed
    0U,	// VST2q16wb_register
    0U,	// VST2q32
    0U,	// VST2q32Pseudo
    0U,	// VST2q32PseudoWB_fixed
    0U,	// VST2q32PseudoWB_register
    0U,	// VST2q32wb_fixed
    0U,	// VST2q32wb_register
    0U,	// VST2q8
    0U,	// VST2q8Pseudo
    0U,	// VST2q8PseudoWB_fixed
    0U,	// VST2q8PseudoWB_register
    0U,	// VST2q8wb_fixed
    0U,	// VST2q8wb_register
    256455996U,	// VST3LNd16
    0U,	// VST3LNd16Pseudo
    0U,	// VST3LNd16Pseudo_UPD
    324U,	// VST3LNd16_UPD
    256455996U,	// VST3LNd32
    0U,	// VST3LNd32Pseudo
    0U,	// VST3LNd32Pseudo_UPD
    324U,	// VST3LNd32_UPD
    256455996U,	// VST3LNd8
    0U,	// VST3LNd8Pseudo
    0U,	// VST3LNd8Pseudo_UPD
    324U,	// VST3LNd8_UPD
    256455996U,	// VST3LNq16
    0U,	// VST3LNq16Pseudo
    0U,	// VST3LNq16Pseudo_UPD
    324U,	// VST3LNq16_UPD
    256455996U,	// VST3LNq32
    0U,	// VST3LNq32Pseudo
    0U,	// VST3LNq32Pseudo_UPD
    324U,	// VST3LNq32_UPD
    287342616U,	// VST3d16
    0U,	// VST3d16Pseudo
    0U,	// VST3d16Pseudo_UPD
    18760U,	// VST3d16_UPD
    287342616U,	// VST3d32
    0U,	// VST3d32Pseudo
    0U,	// VST3d32Pseudo_UPD
    18760U,	// VST3d32_UPD
    287342616U,	// VST3d8
    0U,	// VST3d8Pseudo
    0U,	// VST3d8Pseudo_UPD
    18760U,	// VST3d8_UPD
    287342616U,	// VST3q16
    0U,	// VST3q16Pseudo_UPD
    18760U,	// VST3q16_UPD
    0U,	// VST3q16oddPseudo
    0U,	// VST3q16oddPseudo_UPD
    287342616U,	// VST3q32
    0U,	// VST3q32Pseudo_UPD
    18760U,	// VST3q32_UPD
    0U,	// VST3q32oddPseudo
    0U,	// VST3q32oddPseudo_UPD
    287342616U,	// VST3q8
    0U,	// VST3q8Pseudo_UPD
    18760U,	// VST3q8_UPD
    0U,	// VST3q8oddPseudo
    0U,	// VST3q8oddPseudo_UPD
    323564788U,	// VST4LNd16
    0U,	// VST4LNd16Pseudo
    0U,	// VST4LNd16Pseudo_UPD
    19708U,	// VST4LNd16_UPD
    323564788U,	// VST4LNd32
    0U,	// VST4LNd32Pseudo
    0U,	// VST4LNd32Pseudo_UPD
    19708U,	// VST4LNd32_UPD
    323564788U,	// VST4LNd8
    0U,	// VST4LNd8Pseudo
    0U,	// VST4LNd8Pseudo_UPD
    19708U,	// VST4LNd8_UPD
    323564788U,	// VST4LNq16
    0U,	// VST4LNq16Pseudo
    0U,	// VST4LNq16Pseudo_UPD
    19708U,	// VST4LNq16_UPD
    323564788U,	// VST4LNq32
    0U,	// VST4LNq32Pseudo
    0U,	// VST4LNq32Pseudo_UPD
    19708U,	// VST4LNq32_UPD
    337674264U,	// VST4d16
    0U,	// VST4d16Pseudo
    0U,	// VST4d16Pseudo_UPD
    1016136U,	// VST4d16_UPD
    337674264U,	// VST4d32
    0U,	// VST4d32Pseudo
    0U,	// VST4d32Pseudo_UPD
    1016136U,	// VST4d32_UPD
    337674264U,	// VST4d8
    0U,	// VST4d8Pseudo
    0U,	// VST4d8Pseudo_UPD
    1016136U,	// VST4d8_UPD
    337674264U,	// VST4q16
    0U,	// VST4q16Pseudo_UPD
    1016136U,	// VST4q16_UPD
    0U,	// VST4q16oddPseudo
    0U,	// VST4q16oddPseudo_UPD
    337674264U,	// VST4q32
    0U,	// VST4q32Pseudo_UPD
    1016136U,	// VST4q32_UPD
    0U,	// VST4q32oddPseudo
    0U,	// VST4q32oddPseudo_UPD
    337674264U,	// VST4q8
    0U,	// VST4q8Pseudo_UPD
    1016136U,	// VST4q8_UPD
    0U,	// VST4q8oddPseudo
    0U,	// VST4q8oddPseudo_UPD
    33U,	// VSTMDDB_UPD
    1136U,	// VSTMDIA
    33U,	// VSTMDIA_UPD
    0U,	// VSTMQIA
    33U,	// VSTMSDB_UPD
    1136U,	// VSTMSIA
    33U,	// VSTMSIA_UPD
    288U,	// VSTRD
    296U,	// VSTRH
    288U,	// VSTRS
    70705U,	// VSUBD
    70705U,	// VSUBH
    1112U,	// VSUBHNv2i32
    1112U,	// VSUBHNv4i16
    1112U,	// VSUBHNv8i8
    1112U,	// VSUBLsv2i64
    1112U,	// VSUBLsv4i32
    1112U,	// VSUBLsv8i16
    1112U,	// VSUBLuv2i64
    1112U,	// VSUBLuv4i32
    1112U,	// VSUBLuv8i16
    70705U,	// VSUBS
    1112U,	// VSUBWsv2i64
    1112U,	// VSUBWsv4i32
    1112U,	// VSUBWsv8i16
    1112U,	// VSUBWuv2i64
    1112U,	// VSUBWuv4i32
    1112U,	// VSUBWuv8i16
    70705U,	// VSUBfd
    70705U,	// VSUBfq
    70705U,	// VSUBhd
    70705U,	// VSUBhq
    1112U,	// VSUBv16i8
    1112U,	// VSUBv1i64
    1112U,	// VSUBv2i32
    1112U,	// VSUBv2i64
    1112U,	// VSUBv4i16
    1112U,	// VSUBv4i32
    1112U,	// VSUBv8i16
    1112U,	// VSUBv8i8
    1024U,	// VSWPd
    1024U,	// VSWPq
    336U,	// VTBL1
    344U,	// VTBL2
    352U,	// VTBL3
    0U,	// VTBL3Pseudo
    360U,	// VTBL4
    0U,	// VTBL4Pseudo
    368U,	// VTBX1
    376U,	// VTBX2
    384U,	// VTBX3
    0U,	// VTBX3Pseudo
    392U,	// VTBX4
    0U,	// VTBX4Pseudo
    0U,	// VTOSHD
    7U,	// VTOSHH
    0U,	// VTOSHS
    0U,	// VTOSIRD
    0U,	// VTOSIRH
    0U,	// VTOSIRS
    0U,	// VTOSIZD
    0U,	// VTOSIZH
    0U,	// VTOSIZS
    7U,	// VTOSLD
    7U,	// VTOSLH
    7U,	// VTOSLS
    0U,	// VTOUHD
    7U,	// VTOUHH
    0U,	// VTOUHS
    0U,	// VTOUIRD
    0U,	// VTOUIRH
    0U,	// VTOUIRS
    0U,	// VTOUIZD
    0U,	// VTOUIZH
    0U,	// VTOUIZS
    7U,	// VTOULD
    7U,	// VTOULH
    7U,	// VTOULS
    1024U,	// VTRNd16
    1024U,	// VTRNd32
    1024U,	// VTRNd8
    1024U,	// VTRNq16
    1024U,	// VTRNq32
    1024U,	// VTRNq8
    0U,	// VTSTv16i8
    0U,	// VTSTv2i32
    0U,	// VTSTv4i16
    0U,	// VTSTv4i32
    0U,	// VTSTv8i16
    0U,	// VTSTv8i8
    0U,	// VUDOTD
    0U,	// VUDOTDI
    0U,	// VUDOTQ
    0U,	// VUDOTQI
    0U,	// VUHTOD
    7U,	// VUHTOH
    0U,	// VUHTOS
    0U,	// VUITOD
    0U,	// VUITOH
    0U,	// VUITOS
    7U,	// VULTOD
    7U,	// VULTOH
    7U,	// VULTOS
    1024U,	// VUZPd16
    1024U,	// VUZPd8
    1024U,	// VUZPq16
    1024U,	// VUZPq32
    1024U,	// VUZPq8
    1024U,	// VZIPd16
    1024U,	// VZIPd8
    1024U,	// VZIPq16
    1024U,	// VZIPq32
    1024U,	// VZIPq8
    20592U,	// sysLDMDA
    401U,	// sysLDMDA_UPD
    20592U,	// sysLDMDB
    401U,	// sysLDMDB_UPD
    20592U,	// sysLDMIA
    401U,	// sysLDMIA_UPD
    20592U,	// sysLDMIB
    401U,	// sysLDMIB_UPD
    20592U,	// sysSTMDA
    401U,	// sysSTMDA_UPD
    20592U,	// sysSTMDB
    401U,	// sysSTMDB_UPD
    20592U,	// sysSTMIA
    401U,	// sysSTMIA_UPD
    20592U,	// sysSTMIB
    401U,	// sysSTMIB_UPD
    0U,	// t2ADCri
    0U,	// t2ADCrr
    1048576U,	// t2ADCrs
    0U,	// t2ADDri
    0U,	// t2ADDri12
    0U,	// t2ADDrr
    1048576U,	// t2ADDrs
    72U,	// t2ADR
    0U,	// t2ANDri
    0U,	// t2ANDrr
    1048576U,	// t2ANDrs
    1081344U,	// t2ASRri
    0U,	// t2ASRrr
    0U,	// t2B
    80U,	// t2BFC
    163928U,	// t2BFI
    0U,	// t2BICri
    0U,	// t2BICrr
    1048576U,	// t2BICrs
    0U,	// t2BXJ
    0U,	// t2Bcc
    4145U,	// t2CDP
    4145U,	// t2CDP2
    0U,	// t2CLREX
    1024U,	// t2CLZ
    1024U,	// t2CMNri
    1024U,	// t2CMNzrr
    56U,	// t2CMNzrs
    1024U,	// t2CMPri
    1024U,	// t2CMPrr
    56U,	// t2CMPrs
    0U,	// t2CPS1p
    0U,	// t2CPS2p
    1112U,	// t2CPS3p
    1112U,	// t2CRC32B
    1112U,	// t2CRC32CB
    1112U,	// t2CRC32CH
    1112U,	// t2CRC32CW
    1112U,	// t2CRC32H
    1112U,	// t2CRC32W
    0U,	// t2DBG
    0U,	// t2DCPS1
    0U,	// t2DCPS2
    0U,	// t2DCPS3
    0U,	// t2DMB
    0U,	// t2DSB
    0U,	// t2EORri
    0U,	// t2EORrr
    1048576U,	// t2EORrs
    0U,	// t2HINT
    0U,	// t2HVC
    0U,	// t2ISB
    0U,	// t2IT
    0U,	// t2Int_eh_sjlj_setjmp
    0U,	// t2Int_eh_sjlj_setjmp_nofp
    8U,	// t2LDA
    8U,	// t2LDAB
    8U,	// t2LDAEX
    8U,	// t2LDAEXB
    557056U,	// t2LDAEXD
    8U,	// t2LDAEXH
    8U,	// t2LDAH
    122U,	// t2LDC2L_OFFSET
    196738U,	// t2LDC2L_OPTION
    229506U,	// t2LDC2L_POST
    138U,	// t2LDC2L_PRE
    122U,	// t2LDC2_OFFSET
    196738U,	// t2LDC2_OPTION
    229506U,	// t2LDC2_POST
    138U,	// t2LDC2_PRE
    122U,	// t2LDCL_OFFSET
    196738U,	// t2LDCL_OPTION
    229506U,	// t2LDCL_POST
    138U,	// t2LDCL_PRE
    122U,	// t2LDC_OFFSET
    196738U,	// t2LDC_OPTION
    229506U,	// t2LDC_POST
    138U,	// t2LDC_PRE
    1136U,	// t2LDMDB
    33U,	// t2LDMDB_UPD
    1136U,	// t2LDMIA
    33U,	// t2LDMIA_UPD
    408U,	// t2LDRBT
    21632U,	// t2LDRB_POST
    416U,	// t2LDRB_PRE
    160U,	// t2LDRBi12
    408U,	// t2LDRBi8
    424U,	// t2LDRBpci
    432U,	// t2LDRBs
    25493504U,	// t2LDRD_POST
    1114112U,	// t2LDRD_PRE
    1146880U,	// t2LDRDi8
    440U,	// t2LDREX
    8U,	// t2LDREXB
    557056U,	// t2LDREXD
    8U,	// t2LDREXH
    408U,	// t2LDRHT
    21632U,	// t2LDRH_POST
    416U,	// t2LDRH_PRE
    160U,	// t2LDRHi12
    408U,	// t2LDRHi8
    424U,	// t2LDRHpci
    432U,	// t2LDRHs
    408U,	// t2LDRSBT
    21632U,	// t2LDRSB_POST
    416U,	// t2LDRSB_PRE
    160U,	// t2LDRSBi12
    408U,	// t2LDRSBi8
    424U,	// t2LDRSBpci
    432U,	// t2LDRSBs
    408U,	// t2LDRSHT
    21632U,	// t2LDRSH_POST
    416U,	// t2LDRSH_PRE
    160U,	// t2LDRSHi12
    408U,	// t2LDRSHi8
    424U,	// t2LDRSHpci
    432U,	// t2LDRSHs
    408U,	// t2LDRT
    21632U,	// t2LDR_POST
    416U,	// t2LDR_PRE
    160U,	// t2LDRi12
    408U,	// t2LDRi8
    424U,	// t2LDRpci
    432U,	// t2LDRs
    0U,	// t2LSLri
    0U,	// t2LSLrr
    1081344U,	// t2LSRri
    0U,	// t2LSRrr
    4690993U,	// t2MCR
    4690993U,	// t2MCR2
    6788145U,	// t2MCRR
    6788145U,	// t2MCRR2
    35651584U,	// t2MLA
    35651584U,	// t2MLS
    1112U,	// t2MOVTi16
    1024U,	// t2MOVi
    1024U,	// t2MOVi16
    1024U,	// t2MOVr
    22528U,	// t2MOVsra_flag
    22528U,	// t2MOVsrl_flag
    0U,	// t2MRC
    0U,	// t2MRC2
    0U,	// t2MRRC
    0U,	// t2MRRC2
    2U,	// t2MRS_AR
    448U,	// t2MRS_M
    200U,	// t2MRSbanked
    2U,	// t2MRSsys_AR
    33U,	// t2MSR_AR
    33U,	// t2MSR_M
    0U,	// t2MSRbanked
    0U,	// t2MUL
    1024U,	// t2MVNi
    1024U,	// t2MVNr
    56U,	// t2MVNs
    0U,	// t2ORNri
    0U,	// t2ORNrr
    1048576U,	// t2ORNrs
    0U,	// t2ORRri
    0U,	// t2ORRrr
    1048576U,	// t2ORRrs
    8388608U,	// t2PKHBT
    10485760U,	// t2PKHTB
    0U,	// t2PLDWi12
    0U,	// t2PLDWi8
    0U,	// t2PLDWs
    0U,	// t2PLDi12
    0U,	// t2PLDi8
    0U,	// t2PLDpci
    0U,	// t2PLDs
    0U,	// t2PLIi12
    0U,	// t2PLIi8
    0U,	// t2PLIpci
    0U,	// t2PLIs
    0U,	// t2QADD
    0U,	// t2QADD16
    0U,	// t2QADD8
    0U,	// t2QASX
    0U,	// t2QDADD
    0U,	// t2QDSUB
    0U,	// t2QSAX
    0U,	// t2QSUB
    0U,	// t2QSUB16
    0U,	// t2QSUB8
    1024U,	// t2RBIT
    1024U,	// t2REV
    1024U,	// t2REV16
    1024U,	// t2REVSH
    0U,	// t2RFEDB
    0U,	// t2RFEDBW
    0U,	// t2RFEIA
    0U,	// t2RFEIAW
    0U,	// t2RORri
    0U,	// t2RORrr
    1024U,	// t2RRX
    0U,	// t2RSBri
    0U,	// t2RSBrr
    1048576U,	// t2RSBrs
    0U,	// t2SADD16
    0U,	// t2SADD8
    0U,	// t2SASX
    0U,	// t2SB
    0U,	// t2SBCri
    0U,	// t2SBCrr
    1048576U,	// t2SBCrs
    69206016U,	// t2SBFX
    0U,	// t2SDIV
    0U,	// t2SEL
    0U,	// t2SETPAN
    0U,	// t2SG
    0U,	// t2SHADD16
    0U,	// t2SHADD8
    0U,	// t2SHASX
    0U,	// t2SHSAX
    0U,	// t2SHSUB16
    0U,	// t2SHSUB8
    0U,	// t2SMC
    35651584U,	// t2SMLABB
    35651584U,	// t2SMLABT
    35651584U,	// t2SMLAD
    35651584U,	// t2SMLADX
    35651584U,	// t2SMLAL
    35651584U,	// t2SMLALBB
    35651584U,	// t2SMLALBT
    35651584U,	// t2SMLALD
    35651584U,	// t2SMLALDX
    35651584U,	// t2SMLALTB
    35651584U,	// t2SMLALTT
    35651584U,	// t2SMLATB
    35651584U,	// t2SMLATT
    35651584U,	// t2SMLAWB
    35651584U,	// t2SMLAWT
    35651584U,	// t2SMLSD
    35651584U,	// t2SMLSDX
    35651584U,	// t2SMLSLD
    35651584U,	// t2SMLSLDX
    35651584U,	// t2SMMLA
    35651584U,	// t2SMMLAR
    35651584U,	// t2SMMLS
    35651584U,	// t2SMMLSR
    0U,	// t2SMMUL
    0U,	// t2SMMULR
    0U,	// t2SMUAD
    0U,	// t2SMUADX
    0U,	// t2SMULBB
    0U,	// t2SMULBT
    35651584U,	// t2SMULL
    0U,	// t2SMULTB
    0U,	// t2SMULTT
    0U,	// t2SMULWB
    0U,	// t2SMULWT
    0U,	// t2SMUSD
    0U,	// t2SMUSDX
    0U,	// t2SRSDB
    0U,	// t2SRSDB_UPD
    0U,	// t2SRSIA
    0U,	// t2SRSIA_UPD
    6352U,	// t2SSAT
    1232U,	// t2SSAT16
    0U,	// t2SSAX
    0U,	// t2SSUB16
    0U,	// t2SSUB8
    122U,	// t2STC2L_OFFSET
    196738U,	// t2STC2L_OPTION
    229506U,	// t2STC2L_POST
    138U,	// t2STC2L_PRE
    122U,	// t2STC2_OFFSET
    196738U,	// t2STC2_OPTION
    229506U,	// t2STC2_POST
    138U,	// t2STC2_PRE
    122U,	// t2STCL_OFFSET
    196738U,	// t2STCL_OPTION
    229506U,	// t2STCL_POST
    138U,	// t2STCL_PRE
    122U,	// t2STC_OFFSET
    196738U,	// t2STC_OPTION
    229506U,	// t2STC_POST
    138U,	// t2STC_PRE
    8U,	// t2STL
    8U,	// t2STLB
    557056U,	// t2STLEX
    557056U,	// t2STLEXB
    371195904U,	// t2STLEXD
    557056U,	// t2STLEXH
    8U,	// t2STLH
    1136U,	// t2STMDB
    33U,	// t2STMDB_UPD
    1136U,	// t2STMIA
    33U,	// t2STMIA_UPD
    408U,	// t2STRBT
    21632U,	// t2STRB_POST
    416U,	// t2STRB_PRE
    160U,	// t2STRBi12
    408U,	// t2STRBi8
    432U,	// t2STRBs
    25493592U,	// t2STRD_POST
    1114200U,	// t2STRD_PRE
    1146880U,	// t2STRDi8
    1179648U,	// t2STREX
    557056U,	// t2STREXB
    371195904U,	// t2STREXD
    557056U,	// t2STREXH
    408U,	// t2STRHT
    21632U,	// t2STRH_POST
    416U,	// t2STRH_PRE
    160U,	// t2STRHi12
    408U,	// t2STRHi8
    432U,	// t2STRHs
    408U,	// t2STRT
    21632U,	// t2STR_POST
    416U,	// t2STR_PRE
    160U,	// t2STRi12
    408U,	// t2STRi8
    432U,	// t2STRs
    0U,	// t2SUBS_PC_LR
    0U,	// t2SUBri
    0U,	// t2SUBri12
    0U,	// t2SUBrr
    1048576U,	// t2SUBrs
    12582912U,	// t2SXTAB
    12582912U,	// t2SXTAB16
    12582912U,	// t2SXTAH
    7168U,	// t2SXTB
    7168U,	// t2SXTB16
    7168U,	// t2SXTH
    0U,	// t2TBB
    0U,	// t2TBH
    1024U,	// t2TEQri
    1024U,	// t2TEQrr
    56U,	// t2TEQrs
    0U,	// t2TSB
    1024U,	// t2TSTri
    1024U,	// t2TSTrr
    56U,	// t2TSTrs
    1024U,	// t2TT
    1024U,	// t2TTA
    1024U,	// t2TTAT
    1024U,	// t2TTT
    0U,	// t2UADD16
    0U,	// t2UADD8
    0U,	// t2UASX
    69206016U,	// t2UBFX
    0U,	// t2UDF
    0U,	// t2UDIV
    0U,	// t2UHADD16
    0U,	// t2UHADD8
    0U,	// t2UHASX
    0U,	// t2UHSAX
    0U,	// t2UHSUB16
    0U,	// t2UHSUB8
    35651584U,	// t2UMAAL
    35651584U,	// t2UMLAL
    35651584U,	// t2UMULL
    0U,	// t2UQADD16
    0U,	// t2UQADD8
    0U,	// t2UQASX
    0U,	// t2UQSAX
    0U,	// t2UQSUB16
    0U,	// t2UQSUB8
    0U,	// t2USAD8
    35651584U,	// t2USADA8
    14680064U,	// t2USAT
    0U,	// t2USAT16
    0U,	// t2USAX
    0U,	// t2USUB16
    0U,	// t2USUB8
    12582912U,	// t2UXTAB
    12582912U,	// t2UXTAB16
    12582912U,	// t2UXTAH
    7168U,	// t2UXTB
    7168U,	// t2UXTB16
    7168U,	// t2UXTH
    0U,	// tADC
    1112U,	// tADDhirr
    1048U,	// tADDi3
    0U,	// tADDi8
    0U,	// tADDrSP
    1212416U,	// tADDrSPi
    1048U,	// tADDrr
    456U,	// tADDspi
    1112U,	// tADDspr
    464U,	// tADR
    0U,	// tAND
    472U,	// tASRri
    0U,	// tASRrr
    0U,	// tB
    0U,	// tBIC
    0U,	// tBKPT
    0U,	// tBL
    0U,	// tBLXNSr
    0U,	// tBLXi
    0U,	// tBLXr
    0U,	// tBX
    0U,	// tBXNS
    0U,	// tBcc
    0U,	// tCBNZ
    0U,	// tCBZ
    1024U,	// tCMNz
    1024U,	// tCMPhir
    1024U,	// tCMPi8
    1024U,	// tCMPr
    0U,	// tCPS
    0U,	// tEOR
    0U,	// tHINT
    0U,	// tHLT
    0U,	// tInt_WIN_eh_sjlj_longjmp
    0U,	// tInt_eh_sjlj_longjmp
    0U,	// tInt_eh_sjlj_setjmp
    1136U,	// tLDMIA
    480U,	// tLDRBi
    488U,	// tLDRBr
    496U,	// tLDRHi
    488U,	// tLDRHr
    488U,	// tLDRSB
    488U,	// tLDRSH
    504U,	// tLDRi
    424U,	// tLDRpci
    488U,	// tLDRr
    512U,	// tLDRspi
    1048U,	// tLSLri
    0U,	// tLSLrr
    472U,	// tLSRri
    0U,	// tLSRrr
    0U,	// tMOVSr
    0U,	// tMOVi8
    1024U,	// tMOVr
    1048U,	// tMUL
    0U,	// tMVN
    0U,	// tORR
    0U,	// tPICADD
    0U,	// tPOP
    0U,	// tPUSH
    1024U,	// tREV
    1024U,	// tREV16
    1024U,	// tREVSH
    0U,	// tROR
    0U,	// tRSB
    0U,	// tSBC
    0U,	// tSETEND
    33U,	// tSTMIA_UPD
    480U,	// tSTRBi
    488U,	// tSTRBr
    496U,	// tSTRHi
    488U,	// tSTRHr
    504U,	// tSTRi
    488U,	// tSTRr
    512U,	// tSTRspi
    1048U,	// tSUBi3
    0U,	// tSUBi8
    1048U,	// tSUBrr
    456U,	// tSUBspi
    0U,	// tSVC
    1024U,	// tSXTB
    1024U,	// tSXTH
    0U,	// tTRAP
    1024U,	// tTST
    0U,	// tUDF
    1024U,	// tUXTB
    1024U,	// tUXTH
    0U,	// t__brkdiv0
  };

  O << "\t";

  // Emit the opcode for the instruction.
  uint64_t Bits = 0;
  Bits |= (uint64_t)OpInfo0[MI->getOpcode()] << 0;
  Bits |= (uint64_t)OpInfo1[MI->getOpcode()] << 32;
  assert(Bits != 0 && "Cannot print this instruction.");
  O << AsmStrs+(Bits & 4095)-1;


  // Fragment 0 encoded into 5 bits for 32 unique commands.
  switch ((Bits >> 12) & 31) {
  default: llvm_unreachable("Invalid command number.");
  case 0:
    // DBG_VALUE, DBG_LABEL, BUNDLE, LIFETIME_START, LIFETIME_END, FENTRY_CAL...
    return;
    break;
  case 1:
    // ASRi, ASRr, LSLi, LSLr, LSRi, LSRr, RORi, RORr, ADCri, ADCrr, ADDri, A...
    printSBitModifierOperand(MI, 5, STI, O);
    printPredicateOperand(MI, 3, STI, O);
    break;
  case 2:
    // ITasm, t2IT
    printThumbITMask(MI, 1, STI, O);
    break;
  case 3:
    // LDRBT_POST, LDRConstPool, LDRT_POST, STRBT_POST, STRT_POST, t2LDRBpcre...
    printPredicateOperand(MI, 2, STI, O);
    break;
  case 4:
    // RRXi, MOVi, MOVr, MOVr_TC, MVNi, MVNr, t2MOVi, t2MOVr, t2MVNi, t2MVNr,...
    printSBitModifierOperand(MI, 4, STI, O);
    printPredicateOperand(MI, 2, STI, O);
    break;
  case 5:
    // VLD1LNdAsm_16, VLD1LNdAsm_32, VLD1LNdAsm_8, VLD1LNdWB_fixed_Asm_16, VL...
    printPredicateOperand(MI, 4, STI, O);
    break;
  case 6:
    // VLD1LNdWB_register_Asm_16, VLD1LNdWB_register_Asm_32, VLD1LNdWB_regist...
    printPredicateOperand(MI, 5, STI, O);
    break;
  case 7:
    // VLD3DUPdAsm_16, VLD3DUPdAsm_32, VLD3DUPdAsm_8, VLD3DUPdWB_fixed_Asm_16...
    printPredicateOperand(MI, 3, STI, O);
    break;
  case 8:
    // ADCrsi, ADDrsi, ANDrsi, BICrsi, EORrsi, MLA, MOVsr, MVNsr, ORRrsi, RSB...
    printSBitModifierOperand(MI, 6, STI, O);
    printPredicateOperand(MI, 4, STI, O);
    break;
  case 9:
    // ADCrsr, ADDrsr, ANDrsr, BICrsr, EORrsr, ORRrsr, RSBrsr, RSCrsr, SBCrsr...
    printSBitModifierOperand(MI, 7, STI, O);
    printPredicateOperand(MI, 5, STI, O);
    O << "\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    O << ", ";
    printSORegRegOperand(MI, 2, STI, O);
    return;
    break;
  case 10:
    // AESD, AESE, AESIMC, AESMC, BKPT, BL, BLX, BLXi, BX, CPS1p, CRC32B, CRC...
    printOperand(MI, 0, STI, O);
    break;
  case 11:
    // BLX_pred, BL_pred, BXJ, BX_pred, Bcc, DBG, FLDMXIA, FSTMXIA, HINT, LDM...
    printPredicateOperand(MI, 1, STI, O);
    break;
  case 12:
    // BX_RET, ERET, FMSTAT, MOVPCLR, t2CLREX, t2DCPS1, t2DCPS2, t2DCPS3, t2S...
    printPredicateOperand(MI, 0, STI, O);
    break;
  case 13:
    // CDP, LDRD_POST, LDRD_PRE, MCR, MRC, SMLALBB, SMLALBT, SMLALD, SMLALDX,...
    printPredicateOperand(MI, 6, STI, O);
    break;
  case 14:
    // CDP2, LDC2L_OFFSET, LDC2L_OPTION, LDC2L_POST, LDC2L_PRE, LDC2_OFFSET, ...
    printPImmediate(MI, 0, STI, O);
    O << ", ";
    break;
  case 15:
    // CPS2p, CPS3p, t2CPS2p, t2CPS3p, tCPS
    printCPSIMod(MI, 0, STI, O);
    break;
  case 16:
    // DMB, DSB
    printMemBOption(MI, 0, STI, O);
    return;
    break;
  case 17:
    // ISB
    printInstSyncBOption(MI, 0, STI, O);
    return;
    break;
  case 18:
    // MRC2
    printPImmediate(MI, 1, STI, O);
    O << ", ";
    printOperand(MI, 2, STI, O);
    O << ", ";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printCImmediate(MI, 3, STI, O);
    O << ", ";
    printCImmediate(MI, 4, STI, O);
    O << ", ";
    printOperand(MI, 5, STI, O);
    return;
    break;
  case 19:
    // MRRC2
    printPImmediate(MI, 2, STI, O);
    O << ", ";
    printOperand(MI, 3, STI, O);
    O << ", ";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    O << ", ";
    printCImmediate(MI, 4, STI, O);
    return;
    break;
  case 20:
    // PLDWi12, PLDi12, PLIi12
    printAddrModeImm12Operand<false>(MI, 0, STI, O);
    return;
    break;
  case 21:
    // PLDWrs, PLDrs, PLIrs
    printAddrMode2Operand(MI, 0, STI, O);
    return;
    break;
  case 22:
    // SETEND, tSETEND
    printSetendOperand(MI, 0, STI, O);
    return;
    break;
  case 23:
    // SMLAL, UMLAL
    printSBitModifierOperand(MI, 8, STI, O);
    printPredicateOperand(MI, 6, STI, O);
    O << "\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    O << ", ";
    printOperand(MI, 2, STI, O);
    O << ", ";
    printOperand(MI, 3, STI, O);
    return;
    break;
  case 24:
    // TSB
    printTraceSyncBOption(MI, 0, STI, O);
    return;
    break;
  case 25:
    // VLD1LNd16_UPD, VLD1LNd32_UPD, VLD1LNd8_UPD, VLD2LNd16, VLD2LNd32, VLD2...
    printPredicateOperand(MI, 7, STI, O);
    break;
  case 26:
    // VLD2LNd16_UPD, VLD2LNd32_UPD, VLD2LNd8_UPD, VLD2LNq16_UPD, VLD2LNq32_U...
    printPredicateOperand(MI, 9, STI, O);
    break;
  case 27:
    // VLD3LNd16_UPD, VLD3LNd32_UPD, VLD3LNd8_UPD, VLD3LNq16_UPD, VLD3LNq32_U...
    printPredicateOperand(MI, 11, STI, O);
    break;
  case 28:
    // VLD4DUPd16_UPD, VLD4DUPd32_UPD, VLD4DUPd8_UPD, VLD4DUPq16_UPD, VLD4DUP...
    printPredicateOperand(MI, 8, STI, O);
    break;
  case 29:
    // VLD4LNd16_UPD, VLD4LNd32_UPD, VLD4LNd8_UPD, VLD4LNq16_UPD, VLD4LNq32_U...
    printPredicateOperand(MI, 13, STI, O);
    break;
  case 30:
    // VSDOTD, VSDOTDI, VSDOTQ, VSDOTQI, VUDOTD, VUDOTDI, VUDOTQ, VUDOTQI
    printOperand(MI, 1, STI, O);
    O << ", ";
    printOperand(MI, 2, STI, O);
    O << ", ";
    printOperand(MI, 3, STI, O);
    break;
  case 31:
    // tADC, tADDi3, tADDi8, tADDrr, tAND, tASRri, tASRrr, tBIC, tEOR, tLSLri...
    printSBitModifierOperand(MI, 1, STI, O);
    break;
  }


  // Fragment 1 encoded into 7 bits for 75 unique commands.
  switch ((Bits >> 17) & 127) {
  default: llvm_unreachable("Invalid command number.");
  case 0:
    // ASRi, ASRr, ITasm, LDRBT_POST, LDRConstPool, LDRT_POST, LSLi, LSLr, LS...
    O << ' ';
    break;
  case 1:
    // VLD1LNdAsm_16, VLD1LNdWB_fixed_Asm_16, VLD1LNdWB_register_Asm_16, VLD2...
    O << ".16\t";
    break;
  case 2:
    // VLD1LNdAsm_32, VLD1LNdWB_fixed_Asm_32, VLD1LNdWB_register_Asm_32, VLD2...
    O << ".32\t";
    break;
  case 3:
    // VLD1LNdAsm_8, VLD1LNdWB_fixed_Asm_8, VLD1LNdWB_register_Asm_8, VLD2LNd...
    O << ".8\t";
    break;
  case 4:
    // ADCri, ADCrr, ADCrsi, ADDri, ADDrr, ADDrsi, ADR, ANDri, ANDrr, ANDrsi,...
    O << "\t";
    break;
  case 5:
    // AESD, AESE, AESIMC, AESMC, CRC32B, CRC32CB, CRC32CH, CRC32CW, CRC32H, ...
    O << ", ";
    break;
  case 6:
    // BKPT, BL, BLX, BLXi, BX, CPS1p, ERET, HLT, HVC, RFEDA, RFEDB, RFEIA, R...
    return;
    break;
  case 7:
    // BX_RET
    O << "\tlr";
    return;
    break;
  case 8:
    // CDP2, MCR2, MCRR2
    printOperand(MI, 1, STI, O);
    O << ", ";
    break;
  case 9:
    // FCONSTD, VABSD, VADDD, VCMPD, VCMPED, VCMPEZD, VCMPZD, VDIVD, VFMAD, V...
    O << ".f64\t";
    printOperand(MI, 0, STI, O);
    break;
  case 10:
    // FCONSTH, VABDhd, VABDhq, VABSH, VABShd, VABShq, VACGEhd, VACGEhq, VACG...
    O << ".f16\t";
    printOperand(MI, 0, STI, O);
    break;
  case 11:
    // FCONSTS, VABDfd, VABDfq, VABSS, VABSfd, VABSfq, VACGEfd, VACGEfq, VACG...
    O << ".f32\t";
    printOperand(MI, 0, STI, O);
    break;
  case 12:
    // FMSTAT
    O << "\tAPSR_nzcv, fpscr";
    return;
    break;
  case 13:
    // LDC2L_OFFSET, LDC2L_OPTION, LDC2L_POST, LDC2L_PRE, LDC2_OFFSET, LDC2_O...
    printCImmediate(MI, 1, STI, O);
    O << ", ";
    break;
  case 14:
    // MOVPCLR
    O << "\tpc, lr";
    return;
    break;
  case 15:
    // RFEDA_UPD, RFEDB_UPD, RFEIA_UPD, RFEIB_UPD
    O << '!';
    return;
    break;
  case 16:
    // VABALsv2i64, VABAsv2i32, VABAsv4i32, VABDLsv2i64, VABDsv2i32, VABDsv4i...
    O << ".s32\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    break;
  case 17:
    // VABALsv4i32, VABAsv4i16, VABAsv8i16, VABDLsv4i32, VABDsv4i16, VABDsv8i...
    O << ".s16\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    break;
  case 18:
    // VABALsv8i16, VABAsv16i8, VABAsv8i8, VABDLsv8i16, VABDsv16i8, VABDsv8i8...
    O << ".s8\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    break;
  case 19:
    // VABALuv2i64, VABAuv2i32, VABAuv4i32, VABDLuv2i64, VABDuv2i32, VABDuv4i...
    O << ".u32\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    break;
  case 20:
    // VABALuv4i32, VABAuv4i16, VABAuv8i16, VABDLuv4i32, VABDuv4i16, VABDuv8i...
    O << ".u16\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    break;
  case 21:
    // VABALuv8i16, VABAuv16i8, VABAuv8i8, VABDLuv8i16, VABDuv16i8, VABDuv8i8...
    O << ".u8\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    break;
  case 22:
    // VADDHNv2i32, VADDv1i64, VADDv2i64, VMOVNv2i32, VMOVv1i64, VMOVv2i64, V...
    O << ".i64\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    break;
  case 23:
    // VADDHNv4i16, VADDv2i32, VADDv4i32, VBICiv2i32, VBICiv4i32, VCEQv2i32, ...
    O << ".i32\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    break;
  case 24:
    // VADDHNv8i8, VADDv4i16, VADDv8i16, VBICiv4i16, VBICiv8i16, VCEQv4i16, V...
    O << ".i16\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    break;
  case 25:
    // VADDv16i8, VADDv8i8, VCEQv16i8, VCEQv8i8, VCEQzv16i8, VCEQzv8i8, VCLZv...
    O << ".i8\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    break;
  case 26:
    // VCVTBDH, VCVTTDH
    O << ".f16.f64\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    return;
    break;
  case 27:
    // VCVTBHD, VCVTTHD
    O << ".f64.f16\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    return;
    break;
  case 28:
    // VCVTBHS, VCVTTHS, VCVTh2f
    O << ".f32.f16\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    return;
    break;
  case 29:
    // VCVTBSH, VCVTTSH, VCVTf2h
    O << ".f16.f32\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    return;
    break;
  case 30:
    // VCVTDS
    O << ".f64.f32\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    return;
    break;
  case 31:
    // VCVTSD
    O << ".f32.f64\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    return;
    break;
  case 32:
    // VCVTf2sd, VCVTf2sq, VCVTf2xsd, VCVTf2xsq, VTOSIRS, VTOSIZS, VTOSLS
    O << ".s32.f32\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    break;
  case 33:
    // VCVTf2ud, VCVTf2uq, VCVTf2xud, VCVTf2xuq, VTOUIRS, VTOUIZS, VTOULS
    O << ".u32.f32\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    break;
  case 34:
    // VCVTh2sd, VCVTh2sq, VCVTh2xsd, VCVTh2xsq, VTOSHH
    O << ".s16.f16\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    break;
  case 35:
    // VCVTh2ud, VCVTh2uq, VCVTh2xud, VCVTh2xuq, VTOUHH
    O << ".u16.f16\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    break;
  case 36:
    // VCVTs2fd, VCVTs2fq, VCVTxs2fd, VCVTxs2fq, VSITOS, VSLTOS
    O << ".f32.s32\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    break;
  case 37:
    // VCVTs2hd, VCVTs2hq, VCVTxs2hd, VCVTxs2hq, VSHTOH
    O << ".f16.s16\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    break;
  case 38:
    // VCVTu2fd, VCVTu2fq, VCVTxu2fd, VCVTxu2fq, VUITOS, VULTOS
    O << ".f32.u32\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    break;
  case 39:
    // VCVTu2hd, VCVTu2hq, VCVTxu2hd, VCVTxu2hq, VUHTOH
    O << ".f16.u16\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    break;
  case 40:
    // VEXTq64, VLD1d64, VLD1d64Q, VLD1d64Qwb_fixed, VLD1d64Qwb_register, VLD...
    O << ".64\t";
    break;
  case 41:
    // VJCVT, VTOSIRD, VTOSIZD, VTOSLD
    O << ".s32.f64\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    break;
  case 42:
    // VLD1LNd16, VLD1LNd16_UPD, VLD2LNd16, VLD2LNd16_UPD, VLD2LNq16, VLD2LNq...
    O << ".16\t{";
    break;
  case 43:
    // VLD1LNd32, VLD1LNd32_UPD, VLD2LNd32, VLD2LNd32_UPD, VLD2LNq32, VLD2LNq...
    O << ".32\t{";
    break;
  case 44:
    // VLD1LNd8, VLD1LNd8_UPD, VLD2LNd8, VLD2LNd8_UPD, VLD3DUPd8, VLD3DUPd8_U...
    O << ".8\t{";
    break;
  case 45:
    // VMSR
    O << "\tfpscr, ";
    printOperand(MI, 0, STI, O);
    return;
    break;
  case 46:
    // VMSR_FPEXC
    O << "\tfpexc, ";
    printOperand(MI, 0, STI, O);
    return;
    break;
  case 47:
    // VMSR_FPINST
    O << "\tfpinst, ";
    printOperand(MI, 0, STI, O);
    return;
    break;
  case 48:
    // VMSR_FPINST2
    O << "\tfpinst2, ";
    printOperand(MI, 0, STI, O);
    return;
    break;
  case 49:
    // VMSR_FPSID
    O << "\tfpsid, ";
    printOperand(MI, 0, STI, O);
    return;
    break;
  case 50:
    // VMULLp8, VMULpd, VMULpq
    O << ".p8\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    O << ", ";
    printOperand(MI, 2, STI, O);
    return;
    break;
  case 51:
    // VQADDsv1i64, VQADDsv2i64, VQMOVNsuv2i32, VQMOVNsv2i32, VQRSHLsv1i64, V...
    O << ".s64\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    break;
  case 52:
    // VQADDuv1i64, VQADDuv2i64, VQMOVNuv2i32, VQRSHLuv1i64, VQRSHLuv2i64, VQ...
    O << ".u64\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    break;
  case 53:
    // VSDOTDI, VSDOTQI, VUDOTDI, VUDOTQI
    printVectorIndex(MI, 4, STI, O);
    return;
    break;
  case 54:
    // VSHTOD
    O << ".f64.s16\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    O << ", ";
    printFBits16(MI, 2, STI, O);
    return;
    break;
  case 55:
    // VSHTOS
    O << ".f32.s16\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    O << ", ";
    printFBits16(MI, 2, STI, O);
    return;
    break;
  case 56:
    // VSITOD, VSLTOD
    O << ".f64.s32\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    break;
  case 57:
    // VSITOH, VSLTOH
    O << ".f16.s32\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    break;
  case 58:
    // VTOSHD
    O << ".s16.f64\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    O << ", ";
    printFBits16(MI, 2, STI, O);
    return;
    break;
  case 59:
    // VTOSHS
    O << ".s16.f32\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    O << ", ";
    printFBits16(MI, 2, STI, O);
    return;
    break;
  case 60:
    // VTOSIRH, VTOSIZH, VTOSLH
    O << ".s32.f16\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    break;
  case 61:
    // VTOUHD
    O << ".u16.f64\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    O << ", ";
    printFBits16(MI, 2, STI, O);
    return;
    break;
  case 62:
    // VTOUHS
    O << ".u16.f32\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    O << ", ";
    printFBits16(MI, 2, STI, O);
    return;
    break;
  case 63:
    // VTOUIRD, VTOUIZD, VTOULD
    O << ".u32.f64\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    break;
  case 64:
    // VTOUIRH, VTOUIZH, VTOULH
    O << ".u32.f16\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    break;
  case 65:
    // VUHTOD
    O << ".f64.u16\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    O << ", ";
    printFBits16(MI, 2, STI, O);
    return;
    break;
  case 66:
    // VUHTOS
    O << ".f32.u16\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    O << ", ";
    printFBits16(MI, 2, STI, O);
    return;
    break;
  case 67:
    // VUITOD, VULTOD
    O << ".f64.u32\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    break;
  case 68:
    // VUITOH, VULTOH
    O << ".f16.u32\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    break;
  case 69:
    // t2ADCrr, t2ADCrs, t2ADDri, t2ADDrr, t2ADDrs, t2ADR, t2ANDrr, t2ANDrs, ...
    O << ".w\t";
    break;
  case 70:
    // t2SRSDB, t2SRSIA
    O << "\tsp, ";
    printOperand(MI, 0, STI, O);
    return;
    break;
  case 71:
    // t2SRSDB_UPD, t2SRSIA_UPD
    O << "\tsp!, ";
    printOperand(MI, 0, STI, O);
    return;
    break;
  case 72:
    // t2SUBS_PC_LR
    O << "\tpc, lr, ";
    printOperand(MI, 0, STI, O);
    return;
    break;
  case 73:
    // tADC, tADDi3, tADDi8, tADDrr, tAND, tASRri, tASRrr, tBIC, tEOR, tLSLri...
    printPredicateOperand(MI, 4, STI, O);
    O << "\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    break;
  case 74:
    // tMOVi8, tMVN, tRSB
    printPredicateOperand(MI, 3, STI, O);
    O << "\t";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 2, STI, O);
    break;
  }


  // Fragment 2 encoded into 6 bits for 60 unique commands.
  switch ((Bits >> 24) & 63) {
  default: llvm_unreachable("Invalid command number.");
  case 0:
    // ASRi, ASRr, LDRBT_POST, LDRConstPool, LDRT_POST, LSLi, LSLr, LSRi, LSR...
    printOperand(MI, 0, STI, O);
    break;
  case 1:
    // ITasm, t2IT
    printMandatoryPredicateOperand(MI, 0, STI, O);
    return;
    break;
  case 2:
    // VLD3DUPdAsm_16, VLD3DUPdAsm_32, VLD3DUPdAsm_8, VLD3DUPdWB_fixed_Asm_16...
    printVectorListThreeAllLanes(MI, 0, STI, O);
    O << ", ";
    printAddrMode6Operand(MI, 1, STI, O);
    break;
  case 3:
    // VLD3DUPqAsm_16, VLD3DUPqAsm_32, VLD3DUPqAsm_8, VLD3DUPqWB_fixed_Asm_16...
    printVectorListThreeSpacedAllLanes(MI, 0, STI, O);
    O << ", ";
    printAddrMode6Operand(MI, 1, STI, O);
    break;
  case 4:
    // VLD3dAsm_16, VLD3dAsm_32, VLD3dAsm_8, VLD3dWB_fixed_Asm_16, VLD3dWB_fi...
    printVectorListThree(MI, 0, STI, O);
    O << ", ";
    break;
  case 5:
    // VLD3qAsm_16, VLD3qAsm_32, VLD3qAsm_8, VLD3qWB_fixed_Asm_16, VLD3qWB_fi...
    printVectorListThreeSpaced(MI, 0, STI, O);
    O << ", ";
    printAddrMode6Operand(MI, 1, STI, O);
    break;
  case 6:
    // VLD4DUPdAsm_16, VLD4DUPdAsm_32, VLD4DUPdAsm_8, VLD4DUPdWB_fixed_Asm_16...
    printVectorListFourAllLanes(MI, 0, STI, O);
    O << ", ";
    printAddrMode6Operand(MI, 1, STI, O);
    break;
  case 7:
    // VLD4DUPqAsm_16, VLD4DUPqAsm_32, VLD4DUPqAsm_8, VLD4DUPqWB_fixed_Asm_16...
    printVectorListFourSpacedAllLanes(MI, 0, STI, O);
    O << ", ";
    printAddrMode6Operand(MI, 1, STI, O);
    break;
  case 8:
    // VLD4dAsm_16, VLD4dAsm_32, VLD4dAsm_8, VLD4dWB_fixed_Asm_16, VLD4dWB_fi...
    printVectorListFour(MI, 0, STI, O);
    O << ", ";
    break;
  case 9:
    // VLD4qAsm_16, VLD4qAsm_32, VLD4qAsm_8, VLD4qWB_fixed_Asm_16, VLD4qWB_fi...
    printVectorListFourSpaced(MI, 0, STI, O);
    O << ", ";
    printAddrMode6Operand(MI, 1, STI, O);
    break;
  case 10:
    // AESD, AESE, MCR2, MCRR2, SHA1C, SHA1M, SHA1P, SHA1SU0, SHA1SU1, SHA256...
    printOperand(MI, 2, STI, O);
    break;
  case 11:
    // AESIMC, AESMC, CRC32B, CRC32CB, CRC32CH, CRC32CW, CRC32H, CRC32W, FLDM...
    printOperand(MI, 1, STI, O);
    break;
  case 12:
    // CDP, LDCL_OFFSET, LDCL_OPTION, LDCL_POST, LDCL_PRE, LDC_OFFSET, LDC_OP...
    printPImmediate(MI, 0, STI, O);
    O << ", ";
    break;
  case 13:
    // CDP2
    printCImmediate(MI, 2, STI, O);
    O << ", ";
    printCImmediate(MI, 3, STI, O);
    O << ", ";
    printCImmediate(MI, 4, STI, O);
    O << ", ";
    printOperand(MI, 5, STI, O);
    return;
    break;
  case 14:
    // CPS2p, CPS3p, t2CPS2p, t2CPS3p, tCPS
    printCPSIFlag(MI, 1, STI, O);
    break;
  case 15:
    // FCONSTD, FCONSTH, FCONSTS, VABDfd, VABDfq, VABDhd, VABDhq, VABSD, VABS...
    O << ", ";
    break;
  case 16:
    // LDAEXD, LDREXD
    printGPRPairOperand(MI, 0, STI, O);
    O << ", ";
    printAddrMode7Operand(MI, 1, STI, O);
    return;
    break;
  case 17:
    // LDC2L_OFFSET, LDC2_OFFSET, STC2L_OFFSET, STC2_OFFSET
    printAddrMode5Operand<false>(MI, 2, STI, O);
    return;
    break;
  case 18:
    // LDC2L_OPTION, LDC2L_POST, LDC2_OPTION, LDC2_POST, STC2L_OPTION, STC2L_...
    printAddrMode7Operand(MI, 2, STI, O);
    O << ", ";
    break;
  case 19:
    // LDC2L_PRE, LDC2_PRE, STC2L_PRE, STC2_PRE
    printAddrMode5Operand<true>(MI, 2, STI, O);
    O << '!';
    return;
    break;
  case 20:
    // MRC, t2MRC, t2MRC2
    printPImmediate(MI, 1, STI, O);
    O << ", ";
    printOperand(MI, 2, STI, O);
    O << ", ";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printCImmediate(MI, 3, STI, O);
    O << ", ";
    printCImmediate(MI, 4, STI, O);
    O << ", ";
    printOperand(MI, 5, STI, O);
    return;
    break;
  case 21:
    // MRRC, t2MRRC, t2MRRC2
    printPImmediate(MI, 2, STI, O);
    O << ", ";
    printOperand(MI, 3, STI, O);
    O << ", ";
    printOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    O << ", ";
    printCImmediate(MI, 4, STI, O);
    return;
    break;
  case 22:
    // MSR, MSRi, t2MSR_AR, t2MSR_M
    printMSRMaskOperand(MI, 0, STI, O);
    O << ", ";
    break;
  case 23:
    // MSRbanked, t2MSRbanked
    printBankedRegOperand(MI, 0, STI, O);
    O << ", ";
    printOperand(MI, 1, STI, O);
    return;
    break;
  case 24:
    // VBICiv2i32, VBICiv4i16, VBICiv4i32, VBICiv8i16, VMOVv16i8, VMOVv1i64, ...
    printNEONModImmOperand(MI, 1, STI, O);
    return;
    break;
  case 25:
    // VCMPEZD, VCMPEZH, VCMPEZS, VCMPZD, VCMPZH, VCMPZS, tRSB
    O << ", #0";
    return;
    break;
  case 26:
    // VCVTf2sd, VCVTf2sq, VCVTf2ud, VCVTf2uq, VCVTh2sd, VCVTh2sq, VCVTh2ud, ...
    return;
    break;
  case 27:
    // VLD1DUPd16, VLD1DUPd16wb_fixed, VLD1DUPd16wb_register, VLD1DUPd32, VLD...
    printVectorListOneAllLanes(MI, 0, STI, O);
    O << ", ";
    break;
  case 28:
    // VLD1DUPq16, VLD1DUPq16wb_fixed, VLD1DUPq16wb_register, VLD1DUPq32, VLD...
    printVectorListTwoAllLanes(MI, 0, STI, O);
    O << ", ";
    break;
  case 29:
    // VLD1d16, VLD1d16wb_fixed, VLD1d16wb_register, VLD1d32, VLD1d32wb_fixed...
    printVectorListOne(MI, 0, STI, O);
    O << ", ";
    break;
  case 30:
    // VLD1q16, VLD1q16wb_fixed, VLD1q16wb_register, VLD1q32, VLD1q32wb_fixed...
    printVectorListTwo(MI, 0, STI, O);
    O << ", ";
    break;
  case 31:
    // VLD2DUPd16x2, VLD2DUPd16x2wb_fixed, VLD2DUPd16x2wb_register, VLD2DUPd3...
    printVectorListTwoSpacedAllLanes(MI, 0, STI, O);
    O << ", ";
    break;
  case 32:
    // VLD2b16, VLD2b16wb_fixed, VLD2b16wb_register, VLD2b32, VLD2b32wb_fixed...
    printVectorListTwoSpaced(MI, 0, STI, O);
    O << ", ";
    break;
  case 33:
    // VST1LNd16_UPD, VST1LNd32_UPD, VST1LNd8_UPD, VST2LNd16_UPD, VST2LNd32_U...
    printOperand(MI, 4, STI, O);
    break;
  case 34:
    // VST1d16, VST1d32, VST1d64, VST1d8
    printVectorListOne(MI, 2, STI, O);
    O << ", ";
    printAddrMode6Operand(MI, 0, STI, O);
    return;
    break;
  case 35:
    // VST1d16Q, VST1d32Q, VST1d64Q, VST1d8Q, VST2q16, VST2q32, VST2q8
    printVectorListFour(MI, 2, STI, O);
    O << ", ";
    printAddrMode6Operand(MI, 0, STI, O);
    return;
    break;
  case 36:
    // VST1d16Qwb_fixed, VST1d32Qwb_fixed, VST1d64Qwb_fixed, VST1d8Qwb_fixed,...
    printVectorListFour(MI, 3, STI, O);
    O << ", ";
    printAddrMode6Operand(MI, 1, STI, O);
    O << '!';
    return;
    break;
  case 37:
    // VST1d16Qwb_register, VST1d32Qwb_register, VST1d64Qwb_register, VST1d8Q...
    printVectorListFour(MI, 4, STI, O);
    O << ", ";
    printAddrMode6Operand(MI, 1, STI, O);
    O << ", ";
    printOperand(MI, 3, STI, O);
    return;
    break;
  case 38:
    // VST1d16T, VST1d32T, VST1d64T, VST1d8T
    printVectorListThree(MI, 2, STI, O);
    O << ", ";
    printAddrMode6Operand(MI, 0, STI, O);
    return;
    break;
  case 39:
    // VST1d16Twb_fixed, VST1d32Twb_fixed, VST1d64Twb_fixed, VST1d8Twb_fixed
    printVectorListThree(MI, 3, STI, O);
    O << ", ";
    printAddrMode6Operand(MI, 1, STI, O);
    O << '!';
    return;
    break;
  case 40:
    // VST1d16Twb_register, VST1d32Twb_register, VST1d64Twb_register, VST1d8T...
    printVectorListThree(MI, 4, STI, O);
    O << ", ";
    printAddrMode6Operand(MI, 1, STI, O);
    O << ", ";
    printOperand(MI, 3, STI, O);
    return;
    break;
  case 41:
    // VST1d16wb_fixed, VST1d32wb_fixed, VST1d64wb_fixed, VST1d8wb_fixed
    printVectorListOne(MI, 3, STI, O);
    O << ", ";
    printAddrMode6Operand(MI, 1, STI, O);
    O << '!';
    return;
    break;
  case 42:
    // VST1d16wb_register, VST1d32wb_register, VST1d64wb_register, VST1d8wb_r...
    printVectorListOne(MI, 4, STI, O);
    O << ", ";
    printAddrMode6Operand(MI, 1, STI, O);
    O << ", ";
    printOperand(MI, 3, STI, O);
    return;
    break;
  case 43:
    // VST1q16, VST1q32, VST1q64, VST1q8, VST2d16, VST2d32, VST2d8
    printVectorListTwo(MI, 2, STI, O);
    O << ", ";
    printAddrMode6Operand(MI, 0, STI, O);
    return;
    break;
  case 44:
    // VST1q16wb_fixed, VST1q32wb_fixed, VST1q64wb_fixed, VST1q8wb_fixed, VST...
    printVectorListTwo(MI, 3, STI, O);
    O << ", ";
    printAddrMode6Operand(MI, 1, STI, O);
    O << '!';
    return;
    break;
  case 45:
    // VST1q16wb_register, VST1q32wb_register, VST1q64wb_register, VST1q8wb_r...
    printVectorListTwo(MI, 4, STI, O);
    O << ", ";
    printAddrMode6Operand(MI, 1, STI, O);
    O << ", ";
    printOperand(MI, 3, STI, O);
    return;
    break;
  case 46:
    // VST2b16, VST2b32, VST2b8
    printVectorListTwoSpaced(MI, 2, STI, O);
    O << ", ";
    printAddrMode6Operand(MI, 0, STI, O);
    return;
    break;
  case 47:
    // VST2b16wb_fixed, VST2b32wb_fixed, VST2b8wb_fixed
    printVectorListTwoSpaced(MI, 3, STI, O);
    O << ", ";
    printAddrMode6Operand(MI, 1, STI, O);
    O << '!';
    return;
    break;
  case 48:
    // VST2b16wb_register, VST2b32wb_register, VST2b8wb_register
    printVectorListTwoSpaced(MI, 4, STI, O);
    O << ", ";
    printAddrMode6Operand(MI, 1, STI, O);
    O << ", ";
    printOperand(MI, 3, STI, O);
    return;
    break;
  case 49:
    // t2DMB, t2DSB
    printMemBOption(MI, 0, STI, O);
    return;
    break;
  case 50:
    // t2ISB
    printInstSyncBOption(MI, 0, STI, O);
    return;
    break;
  case 51:
    // t2PLDWi12, t2PLDi12, t2PLIi12
    printAddrModeImm12Operand<false>(MI, 0, STI, O);
    return;
    break;
  case 52:
    // t2PLDWi8, t2PLDi8, t2PLIi8
    printT2AddrModeImm8Operand<false>(MI, 0, STI, O);
    return;
    break;
  case 53:
    // t2PLDWs, t2PLDs, t2PLIs
    printT2AddrModeSoRegOperand(MI, 0, STI, O);
    return;
    break;
  case 54:
    // t2PLDpci, t2PLIpci
    printThumbLdrLabelOperand(MI, 0, STI, O);
    return;
    break;
  case 55:
    // t2TBB
    printAddrModeTBB(MI, 0, STI, O);
    return;
    break;
  case 56:
    // t2TBH
    printAddrModeTBH(MI, 0, STI, O);
    return;
    break;
  case 57:
    // t2TSB
    printTraceSyncBOption(MI, 0, STI, O);
    return;
    break;
  case 58:
    // tADC, tADDi8, tAND, tASRrr, tBIC, tEOR, tLSLrr, tLSRrr, tORR, tROR, tS...
    printOperand(MI, 3, STI, O);
    return;
    break;
  case 59:
    // tPOP, tPUSH
    printRegisterList(MI, 2, STI, O);
    return;
    break;
  }


  // Fragment 3 encoded into 5 bits for 30 unique commands.
  switch ((Bits >> 30) & 31) {
  default: llvm_unreachable("Invalid command number.");
  case 0:
    // ASRi, ASRr, LDRBT_POST, LDRConstPool, LDRT_POST, LSLi, LSLr, LSRi, LSR...
    O << ", ";
    break;
  case 1:
    // VLD3DUPdAsm_16, VLD3DUPdAsm_32, VLD3DUPdAsm_8, VLD3DUPqAsm_16, VLD3DUP...
    return;
    break;
  case 2:
    // VLD3DUPdWB_fixed_Asm_16, VLD3DUPdWB_fixed_Asm_32, VLD3DUPdWB_fixed_Asm...
    O << '!';
    return;
    break;
  case 3:
    // VLD3dAsm_16, VLD3dAsm_32, VLD3dAsm_8, VLD3dWB_fixed_Asm_16, VLD3dWB_fi...
    printAddrMode6Operand(MI, 1, STI, O);
    break;
  case 4:
    // CDP, MCR, MCRR, MSR, VABDfd, VABDfq, VABDhd, VABDhq, VABSD, VABSH, VAB...
    printOperand(MI, 1, STI, O);
    break;
  case 5:
    // FCONSTD, FCONSTH, FCONSTS, VMOVv2f32, VMOVv4f32
    printFPImmOperand(MI, 1, STI, O);
    return;
    break;
  case 6:
    // FLDMXDB_UPD, FLDMXIA_UPD, FSTMXDB_UPD, FSTMXIA_UPD, LDMDA_UPD, LDMDB_U...
    O << "!, ";
    printRegisterList(MI, 4, STI, O);
    break;
  case 7:
    // LDC2L_OPTION, LDC2_OPTION, STC2L_OPTION, STC2_OPTION
    printCoprocOptionImm(MI, 3, STI, O);
    return;
    break;
  case 8:
    // LDC2L_POST, LDC2_POST, STC2L_POST, STC2_POST
    printPostIdxImm8s4Operand(MI, 3, STI, O);
    return;
    break;
  case 9:
    // LDCL_OFFSET, LDCL_OPTION, LDCL_POST, LDCL_PRE, LDC_OFFSET, LDC_OPTION,...
    printCImmediate(MI, 1, STI, O);
    O << ", ";
    break;
  case 10:
    // MRS, t2MRS_AR
    O << ", apsr";
    return;
    break;
  case 11:
    // MRSsys, t2MRSsys_AR
    O << ", spsr";
    return;
    break;
  case 12:
    // MSRi
    printModImmOperand(MI, 1, STI, O);
    return;
    break;
  case 13:
    // VCEQzv16i8, VCEQzv2i32, VCEQzv4i16, VCEQzv4i32, VCEQzv8i16, VCEQzv8i8,...
    O << ", #0";
    return;
    break;
  case 14:
    // VCVTf2xsd, VCVTf2xsq, VCVTf2xud, VCVTf2xuq, VCVTh2xsd, VCVTh2xsq, VCVT...
    printOperand(MI, 2, STI, O);
    break;
  case 15:
    // VGETLNs16, VGETLNs8, VGETLNu16, VGETLNu8
    printVectorIndex(MI, 2, STI, O);
    return;
    break;
  case 16:
    // VLD1DUPd16wb_fixed, VLD1DUPd16wb_register, VLD1DUPd32wb_fixed, VLD1DUP...
    printAddrMode6Operand(MI, 2, STI, O);
    break;
  case 17:
    // VLD1LNd16, VLD1LNd16_UPD, VLD1LNd32, VLD1LNd32_UPD, VLD1LNd8, VLD1LNd8...
    O << '[';
    break;
  case 18:
    // VLD3DUPd16, VLD3DUPd16_UPD, VLD3DUPd32, VLD3DUPd32_UPD, VLD3DUPd8, VLD...
    O << "[], ";
    printOperand(MI, 1, STI, O);
    O << "[], ";
    printOperand(MI, 2, STI, O);
    break;
  case 19:
    // VMRS
    O << ", fpscr";
    return;
    break;
  case 20:
    // VMRS_FPEXC
    O << ", fpexc";
    return;
    break;
  case 21:
    // VMRS_FPINST
    O << ", fpinst";
    return;
    break;
  case 22:
    // VMRS_FPINST2
    O << ", fpinst2";
    return;
    break;
  case 23:
    // VMRS_FPSID
    O << ", fpsid";
    return;
    break;
  case 24:
    // VMRS_MVFR0
    O << ", mvfr0";
    return;
    break;
  case 25:
    // VMRS_MVFR1
    O << ", mvfr1";
    return;
    break;
  case 26:
    // VMRS_MVFR2
    O << ", mvfr2";
    return;
    break;
  case 27:
    // VSETLNi16, VSETLNi32, VSETLNi8
    printVectorIndex(MI, 3, STI, O);
    O << ", ";
    printOperand(MI, 2, STI, O);
    return;
    break;
  case 28:
    // VSHTOH, VTOSHH, VTOUHH, VUHTOH
    printFBits16(MI, 2, STI, O);
    return;
    break;
  case 29:
    // VSLTOD, VSLTOH, VSLTOS, VTOSLD, VTOSLH, VTOSLS, VTOULD, VTOULH, VTOULS...
    printFBits32(MI, 2, STI, O);
    return;
    break;
  }


  // Fragment 4 encoded into 7 bits for 65 unique commands.
  switch ((Bits >> 35) & 127) {
  default: llvm_unreachable("Invalid command number.");
  case 0:
    // ASRi, ASRr, LDRConstPool, LSLi, LSLr, LSRi, LSRr, RORi, RORr, RRXi, t2...
    printOperand(MI, 1, STI, O);
    break;
  case 1:
    // LDRBT_POST, LDRT_POST, STRBT_POST, STRT_POST, LDA, LDAB, LDAEX, LDAEXB...
    printAddrMode7Operand(MI, 1, STI, O);
    return;
    break;
  case 2:
    // VLD1LNdAsm_16, VLD1LNdAsm_32, VLD1LNdAsm_8, VLD1LNdWB_fixed_Asm_16, VL...
    printAddrMode6Operand(MI, 2, STI, O);
    break;
  case 3:
    // VLD3DUPdWB_register_Asm_16, VLD3DUPdWB_register_Asm_32, VLD3DUPdWB_reg...
    printOperand(MI, 3, STI, O);
    break;
  case 4:
    // VLD3dAsm_16, VLD3dAsm_32, VLD3dAsm_8, VLD4dAsm_16, VLD4dAsm_32, VLD4dA...
    return;
    break;
  case 5:
    // VLD3dWB_fixed_Asm_16, VLD3dWB_fixed_Asm_32, VLD3dWB_fixed_Asm_8, VLD4d...
    O << '!';
    return;
    break;
  case 6:
    // VLD3dWB_register_Asm_16, VLD3dWB_register_Asm_32, VLD3dWB_register_Asm...
    O << ", ";
    break;
  case 7:
    // t2MOVSsi, t2MOVsi, t2CMNzrs, t2CMPrs, t2MVNs, t2TEQrs, t2TSTrs
    printT2SOOperand(MI, 1, STI, O);
    return;
    break;
  case 8:
    // t2MOVSsr, t2MOVsr, CMNzrsr, CMPrsr, MOVsr, MVNsr, TEQrsr, TSTrsr
    printSORegRegOperand(MI, 1, STI, O);
    return;
    break;
  case 9:
    // ADR, t2ADR
    printAdrLabelOperand<0>(MI, 1, STI, O);
    return;
    break;
  case 10:
    // BFC, t2BFC
    printBitfieldInvMaskImmOperand(MI, 2, STI, O);
    return;
    break;
  case 11:
    // BFI, CPS3p, CRC32B, CRC32CB, CRC32CH, CRC32CW, CRC32H, CRC32W, MOVTi16...
    printOperand(MI, 2, STI, O);
    break;
  case 12:
    // CMNri, CMPri, MOVi, MVNi, TEQri, TSTri
    printModImmOperand(MI, 1, STI, O);
    return;
    break;
  case 13:
    // CMNzrsi, CMPrsi, MOVsi, MVNsi, TEQrsi, TSTrsi
    printSORegImmOperand(MI, 1, STI, O);
    return;
    break;
  case 14:
    // FLDMXIA, FSTMXIA, LDMDA, LDMDB, LDMIA, LDMIB, STMDA, STMDB, STMIA, STM...
    printRegisterList(MI, 3, STI, O);
    break;
  case 15:
    // LDCL_OFFSET, LDC_OFFSET, STCL_OFFSET, STC_OFFSET, t2LDC2L_OFFSET, t2LD...
    printAddrMode5Operand<false>(MI, 2, STI, O);
    return;
    break;
  case 16:
    // LDCL_OPTION, LDCL_POST, LDC_OPTION, LDC_POST, LDRBT_POST_IMM, LDRBT_PO...
    printAddrMode7Operand(MI, 2, STI, O);
    break;
  case 17:
    // LDCL_PRE, LDC_PRE, STCL_PRE, STC_PRE, t2LDC2L_PRE, t2LDC2_PRE, t2LDCL_...
    printAddrMode5Operand<true>(MI, 2, STI, O);
    O << '!';
    return;
    break;
  case 18:
    // LDRB_PRE_IMM, LDR_PRE_IMM, STRB_PRE_IMM, STR_PRE_IMM
    printAddrModeImm12Operand<true>(MI, 2, STI, O);
    O << '!';
    return;
    break;
  case 19:
    // LDRB_PRE_REG, LDR_PRE_REG, STRB_PRE_REG, STR_PRE_REG
    printAddrMode2Operand(MI, 2, STI, O);
    O << '!';
    return;
    break;
  case 20:
    // LDRBi12, LDRcp, LDRi12, STRBi12, STRi12, t2LDRBi12, t2LDRHi12, t2LDRSB...
    printAddrModeImm12Operand<false>(MI, 1, STI, O);
    return;
    break;
  case 21:
    // LDRBrs, LDRrs, STRBrs, STRrs
    printAddrMode2Operand(MI, 1, STI, O);
    return;
    break;
  case 22:
    // LDRH, LDRSB, LDRSH, STRH
    printAddrMode3Operand<false>(MI, 1, STI, O);
    return;
    break;
  case 23:
    // LDRH_PRE, LDRSB_PRE, LDRSH_PRE, STRH_PRE
    printAddrMode3Operand<true>(MI, 2, STI, O);
    O << '!';
    return;
    break;
  case 24:
    // MCR2
    printCImmediate(MI, 3, STI, O);
    O << ", ";
    printCImmediate(MI, 4, STI, O);
    O << ", ";
    printOperand(MI, 5, STI, O);
    return;
    break;
  case 25:
    // MRSbanked, t2MRSbanked
    printBankedRegOperand(MI, 1, STI, O);
    return;
    break;
  case 26:
    // SSAT, SSAT16, t2SSAT, t2SSAT16
    printImmPlusOneOperand(MI, 1, STI, O);
    O << ", ";
    printOperand(MI, 2, STI, O);
    break;
  case 27:
    // STLEXD, STREXD
    printGPRPairOperand(MI, 1, STI, O);
    O << ", ";
    printAddrMode7Operand(MI, 2, STI, O);
    return;
    break;
  case 28:
    // VCEQzv2f32, VCEQzv4f16, VCEQzv4f32, VCEQzv8f16, VCGEzv2f32, VCGEzv4f16...
    O << ", #0";
    return;
    break;
  case 29:
    // VLD1LNd16, VLD1LNd32, VLD1LNd8, VST2LNd16, VST2LNd32, VST2LNd8, VST2LN...
    printNoHashImmediate(MI, 4, STI, O);
    break;
  case 30:
    // VLD1LNd16_UPD, VLD1LNd32_UPD, VLD1LNd8_UPD, VLD2LNd16, VLD2LNd32, VLD2...
    printNoHashImmediate(MI, 6, STI, O);
    break;
  case 31:
    // VLD2LNd16_UPD, VLD2LNd32_UPD, VLD2LNd8_UPD, VLD2LNq16_UPD, VLD2LNq32_U...
    printNoHashImmediate(MI, 8, STI, O);
    O << "], ";
    break;
  case 32:
    // VLD3DUPd16, VLD3DUPd16_UPD, VLD3DUPd32, VLD3DUPd32_UPD, VLD3DUPd8, VLD...
    O << "[]}, ";
    break;
  case 33:
    // VLD3LNd16_UPD, VLD3LNd32_UPD, VLD3LNd8_UPD, VLD3LNq16_UPD, VLD3LNq32_U...
    printNoHashImmediate(MI, 10, STI, O);
    O << "], ";
    printOperand(MI, 1, STI, O);
    O << '[';
    printNoHashImmediate(MI, 10, STI, O);
    O << "], ";
    printOperand(MI, 2, STI, O);
    O << '[';
    printNoHashImmediate(MI, 10, STI, O);
    break;
  case 34:
    // VLD4DUPd16, VLD4DUPd16_UPD, VLD4DUPd32, VLD4DUPd32_UPD, VLD4DUPd8, VLD...
    O << "[], ";
    printOperand(MI, 3, STI, O);
    O << "[]}, ";
    break;
  case 35:
    // VLD4LNd16_UPD, VLD4LNd32_UPD, VLD4LNd8_UPD, VLD4LNq16_UPD, VLD4LNq32_U...
    printNoHashImmediate(MI, 12, STI, O);
    O << "], ";
    printOperand(MI, 1, STI, O);
    O << '[';
    printNoHashImmediate(MI, 12, STI, O);
    O << "], ";
    printOperand(MI, 2, STI, O);
    O << '[';
    printNoHashImmediate(MI, 12, STI, O);
    O << "], ";
    printOperand(MI, 3, STI, O);
    O << '[';
    printNoHashImmediate(MI, 12, STI, O);
    O << "]}, ";
    printAddrMode6Operand(MI, 5, STI, O);
    printAddrMode6OffsetOperand(MI, 7, STI, O);
    return;
    break;
  case 36:
    // VLDRD, VLDRS, VSTRD, VSTRS
    printAddrMode5Operand<false>(MI, 1, STI, O);
    return;
    break;
  case 37:
    // VLDRH, VSTRH
    printAddrMode5FP16Operand<false>(MI, 1, STI, O);
    return;
    break;
  case 38:
    // VST1LNd16, VST1LNd32, VST1LNd8
    printNoHashImmediate(MI, 3, STI, O);
    O << "]}, ";
    printAddrMode6Operand(MI, 0, STI, O);
    return;
    break;
  case 39:
    // VST1LNd16_UPD, VST1LNd32_UPD, VST1LNd8_UPD, VST3LNd16, VST3LNd32, VST3...
    printNoHashImmediate(MI, 5, STI, O);
    break;
  case 40:
    // VST3LNd16_UPD, VST3LNd32_UPD, VST3LNd8_UPD, VST3LNq16_UPD, VST3LNq32_U...
    printNoHashImmediate(MI, 7, STI, O);
    O << "], ";
    printOperand(MI, 5, STI, O);
    O << '[';
    printNoHashImmediate(MI, 7, STI, O);
    O << "], ";
    printOperand(MI, 6, STI, O);
    O << '[';
    printNoHashImmediate(MI, 7, STI, O);
    O << "]}, ";
    printAddrMode6Operand(MI, 1, STI, O);
    printAddrMode6OffsetOperand(MI, 3, STI, O);
    return;
    break;
  case 41:
    // VST3d16_UPD, VST3d32_UPD, VST3d8_UPD, VST3q16_UPD, VST3q32_UPD, VST3q8...
    printOperand(MI, 5, STI, O);
    O << ", ";
    printOperand(MI, 6, STI, O);
    break;
  case 42:
    // VTBL1
    printVectorListOne(MI, 1, STI, O);
    O << ", ";
    printOperand(MI, 2, STI, O);
    return;
    break;
  case 43:
    // VTBL2
    printVectorListTwo(MI, 1, STI, O);
    O << ", ";
    printOperand(MI, 2, STI, O);
    return;
    break;
  case 44:
    // VTBL3
    printVectorListThree(MI, 1, STI, O);
    O << ", ";
    printOperand(MI, 2, STI, O);
    return;
    break;
  case 45:
    // VTBL4
    printVectorListFour(MI, 1, STI, O);
    O << ", ";
    printOperand(MI, 2, STI, O);
    return;
    break;
  case 46:
    // VTBX1
    printVectorListOne(MI, 2, STI, O);
    O << ", ";
    printOperand(MI, 3, STI, O);
    return;
    break;
  case 47:
    // VTBX2
    printVectorListTwo(MI, 2, STI, O);
    O << ", ";
    printOperand(MI, 3, STI, O);
    return;
    break;
  case 48:
    // VTBX3
    printVectorListThree(MI, 2, STI, O);
    O << ", ";
    printOperand(MI, 3, STI, O);
    return;
    break;
  case 49:
    // VTBX4
    printVectorListFour(MI, 2, STI, O);
    O << ", ";
    printOperand(MI, 3, STI, O);
    return;
    break;
  case 50:
    // sysLDMDA_UPD, sysLDMDB_UPD, sysLDMIA_UPD, sysLDMIB_UPD, sysSTMDA_UPD, ...
    O << " ^";
    return;
    break;
  case 51:
    // t2LDRBT, t2LDRBi8, t2LDRHT, t2LDRHi8, t2LDRSBT, t2LDRSBi8, t2LDRSHT, t...
    printT2AddrModeImm8Operand<false>(MI, 1, STI, O);
    return;
    break;
  case 52:
    // t2LDRB_PRE, t2LDRH_PRE, t2LDRSB_PRE, t2LDRSH_PRE, t2LDR_PRE, t2STRB_PR...
    printT2AddrModeImm8Operand<true>(MI, 2, STI, O);
    O << '!';
    return;
    break;
  case 53:
    // t2LDRBpci, t2LDRHpci, t2LDRSBpci, t2LDRSHpci, t2LDRpci, tLDRpci
    printThumbLdrLabelOperand(MI, 1, STI, O);
    return;
    break;
  case 54:
    // t2LDRBs, t2LDRHs, t2LDRSBs, t2LDRSHs, t2LDRs, t2STRBs, t2STRHs, t2STRs
    printT2AddrModeSoRegOperand(MI, 1, STI, O);
    return;
    break;
  case 55:
    // t2LDREX
    printT2AddrModeImm0_1020s4Operand(MI, 1, STI, O);
    return;
    break;
  case 56:
    // t2MRS_M
    printMSRMaskOperand(MI, 1, STI, O);
    return;
    break;
  case 57:
    // tADDspi, tSUBspi
    printThumbS4ImmOperand(MI, 2, STI, O);
    return;
    break;
  case 58:
    // tADR
    printAdrLabelOperand<2>(MI, 1, STI, O);
    return;
    break;
  case 59:
    // tASRri, tLSRri
    printThumbSRImm(MI, 3, STI, O);
    return;
    break;
  case 60:
    // tLDRBi, tSTRBi
    printThumbAddrModeImm5S1Operand(MI, 1, STI, O);
    return;
    break;
  case 61:
    // tLDRBr, tLDRHr, tLDRSB, tLDRSH, tLDRr, tSTRBr, tSTRHr, tSTRr
    printThumbAddrModeRROperand(MI, 1, STI, O);
    return;
    break;
  case 62:
    // tLDRHi, tSTRHi
    printThumbAddrModeImm5S2Operand(MI, 1, STI, O);
    return;
    break;
  case 63:
    // tLDRi, tSTRi
    printThumbAddrModeImm5S4Operand(MI, 1, STI, O);
    return;
    break;
  case 64:
    // tLDRspi, tSTRspi
    printThumbAddrModeSPOperand(MI, 1, STI, O);
    return;
    break;
  }


  // Fragment 5 encoded into 5 bits for 23 unique commands.
  switch ((Bits >> 42) & 31) {
  default: llvm_unreachable("Invalid command number.");
  case 0:
    // ASRi, ASRr, LSLi, LSLr, LSRi, LSRr, RORi, RORr, VLD1LNdWB_register_Asm...
    O << ", ";
    break;
  case 1:
    // LDRConstPool, RRXi, VLD1LNdAsm_16, VLD1LNdAsm_32, VLD1LNdAsm_8, VLD2LN...
    return;
    break;
  case 2:
    // VLD1LNdWB_fixed_Asm_16, VLD1LNdWB_fixed_Asm_32, VLD1LNdWB_fixed_Asm_8,...
    O << '!';
    return;
    break;
  case 3:
    // VLD3dWB_register_Asm_16, VLD3dWB_register_Asm_32, VLD3dWB_register_Asm...
    printOperand(MI, 3, STI, O);
    break;
  case 4:
    // CDP, t2CDP, t2CDP2
    printCImmediate(MI, 2, STI, O);
    O << ", ";
    printCImmediate(MI, 3, STI, O);
    O << ", ";
    printCImmediate(MI, 4, STI, O);
    O << ", ";
    printOperand(MI, 5, STI, O);
    return;
    break;
  case 5:
    // MCR, MCRR, VABDfd, VABDfq, VABDhd, VABDhq, VACGEfd, VACGEfq, VACGEhd, ...
    printOperand(MI, 2, STI, O);
    break;
  case 6:
    // SSAT, t2SSAT
    printShiftImmOperand(MI, 3, STI, O);
    return;
    break;
  case 7:
    // SXTB, SXTB16, SXTH, UXTB, UXTB16, UXTH, t2SXTB, t2SXTB16, t2SXTH, t2UX...
    printRotImmOperand(MI, 2, STI, O);
    return;
    break;
  case 8:
    // VCMLAv2f32_indexed, VCMLAv4f16_indexed, VCMLAv4f32_indexed, VCMLAv8f16...
    printVectorIndex(MI, 4, STI, O);
    break;
  case 9:
    // VDUPLN16d, VDUPLN16q, VDUPLN32d, VDUPLN32q, VDUPLN8d, VDUPLN8q, VGETLN...
    printVectorIndex(MI, 2, STI, O);
    return;
    break;
  case 10:
    // VFMALDI, VFMALQI, VFMSLDI, VFMSLQI, VMULLslsv2i32, VMULLslsv4i16, VMUL...
    printVectorIndex(MI, 3, STI, O);
    return;
    break;
  case 11:
    // VLD1DUPd16wb_register, VLD1DUPd32wb_register, VLD1DUPd8wb_register, VL...
    printOperand(MI, 4, STI, O);
    return;
    break;
  case 12:
    // VLD1LNd16, VLD1LNd16_UPD, VLD1LNd32, VLD1LNd32_UPD, VLD1LNd8, VLD1LNd8...
    O << "]}, ";
    break;
  case 13:
    // VLD2LNd16, VLD2LNd32, VLD2LNd8, VLD2LNq16, VLD2LNq32, VLD4LNd16, VLD4L...
    O << "], ";
    break;
  case 14:
    // VLD2LNd16_UPD, VLD2LNd32_UPD, VLD2LNd8_UPD, VLD2LNq16_UPD, VLD2LNq32_U...
    printOperand(MI, 1, STI, O);
    O << '[';
    printNoHashImmediate(MI, 8, STI, O);
    break;
  case 15:
    // VLD3DUPd16, VLD3DUPd32, VLD3DUPd8, VLD3DUPq16, VLD3DUPq32, VLD3DUPq8
    printAddrMode6Operand(MI, 3, STI, O);
    return;
    break;
  case 16:
    // VLD3DUPd16_UPD, VLD3DUPd32_UPD, VLD3DUPd8_UPD, VLD3DUPq16_UPD, VLD3DUP...
    printAddrMode6Operand(MI, 4, STI, O);
    break;
  case 17:
    // VLD4DUPd16_UPD, VLD4DUPd32_UPD, VLD4DUPd8_UPD, VLD4DUPq16_UPD, VLD4DUP...
    printAddrMode6Operand(MI, 5, STI, O);
    printAddrMode6OffsetOperand(MI, 7, STI, O);
    return;
    break;
  case 18:
    // VST3d16_UPD, VST3d32_UPD, VST3d8_UPD, VST3q16_UPD, VST3q32_UPD, VST3q8...
    O << "}, ";
    printAddrMode6Operand(MI, 1, STI, O);
    printAddrMode6OffsetOperand(MI, 3, STI, O);
    return;
    break;
  case 19:
    // VST4LNd16_UPD, VST4LNd32_UPD, VST4LNd8_UPD, VST4LNq16_UPD, VST4LNq32_U...
    printOperand(MI, 5, STI, O);
    O << '[';
    printNoHashImmediate(MI, 8, STI, O);
    O << "], ";
    printOperand(MI, 6, STI, O);
    O << '[';
    printNoHashImmediate(MI, 8, STI, O);
    O << "], ";
    printOperand(MI, 7, STI, O);
    O << '[';
    printNoHashImmediate(MI, 8, STI, O);
    O << "]}, ";
    printAddrMode6Operand(MI, 1, STI, O);
    printAddrMode6OffsetOperand(MI, 3, STI, O);
    return;
    break;
  case 20:
    // sysLDMDA, sysLDMDB, sysLDMIA, sysLDMIB, sysSTMDA, sysSTMDB, sysSTMIA, ...
    O << " ^";
    return;
    break;
  case 21:
    // t2LDRB_POST, t2LDRH_POST, t2LDRSB_POST, t2LDRSH_POST, t2LDR_POST, t2ST...
    printT2AddrModeImm8OffsetOperand(MI, 3, STI, O);
    return;
    break;
  case 22:
    // t2MOVsra_flag, t2MOVsrl_flag
    O << ", #1";
    return;
    break;
  }


  // Fragment 6 encoded into 6 bits for 38 unique commands.
  switch ((Bits >> 47) & 63) {
  default: llvm_unreachable("Invalid command number.");
  case 0:
    // ASRi, ASRr, LSLi, LSLr, LSRi, LSRr, RORi, RORr, ADCrr, ADDrr, ANDrr, B...
    printOperand(MI, 2, STI, O);
    break;
  case 1:
    // VLD1LNdWB_register_Asm_16, VLD1LNdWB_register_Asm_32, VLD1LNdWB_regist...
    printOperand(MI, 4, STI, O);
    break;
  case 2:
    // VLD3dWB_register_Asm_16, VLD3dWB_register_Asm_32, VLD3dWB_register_Asm...
    return;
    break;
  case 3:
    // ADCri, ADDri, ANDri, BICri, EORri, ORRri, RSBri, RSCri, SBCri, SUBri
    printModImmOperand(MI, 2, STI, O);
    return;
    break;
  case 4:
    // ADCrsi, ADDrsi, ANDrsi, BICrsi, EORrsi, ORRrsi, RSBrsi, RSCrsi, SBCrsi...
    printSORegImmOperand(MI, 2, STI, O);
    return;
    break;
  case 5:
    // BFI, t2BFI
    printBitfieldInvMaskImmOperand(MI, 3, STI, O);
    return;
    break;
  case 6:
    // LDCL_OPTION, LDC_OPTION, STCL_OPTION, STC_OPTION, t2LDC2L_OPTION, t2LD...
    printCoprocOptionImm(MI, 3, STI, O);
    return;
    break;
  case 7:
    // LDCL_POST, LDC_POST, STCL_POST, STC_POST, t2LDC2L_POST, t2LDC2_POST, t...
    printPostIdxImm8s4Operand(MI, 3, STI, O);
    return;
    break;
  case 8:
    // LDRBT_POST_IMM, LDRBT_POST_REG, LDRB_POST_IMM, LDRB_POST_REG, LDRT_POS...
    printAddrMode2OffsetOperand(MI, 3, STI, O);
    return;
    break;
  case 9:
    // LDRD, STRD
    printAddrMode3Operand<false>(MI, 2, STI, O);
    return;
    break;
  case 10:
    // LDRD_POST, STRD_POST, t2LDRD_POST, t2STRD_POST
    printAddrMode7Operand(MI, 3, STI, O);
    break;
  case 11:
    // LDRD_PRE, STRD_PRE
    printAddrMode3Operand<true>(MI, 3, STI, O);
    O << '!';
    return;
    break;
  case 12:
    // LDRHTi, LDRSBTi, LDRSHTi, STRHTi
    printPostIdxImm8Operand(MI, 3, STI, O);
    return;
    break;
  case 13:
    // LDRHTr, LDRSBTr, LDRSHTr, STRHTr
    printPostIdxRegOperand(MI, 3, STI, O);
    return;
    break;
  case 14:
    // LDRH_POST, LDRSB_POST, LDRSH_POST, STRH_POST
    printAddrMode3OffsetOperand(MI, 3, STI, O);
    return;
    break;
  case 15:
    // MCR, MCRR, VCMLAv2f32_indexed, VCMLAv4f16_indexed, VCMLAv4f32_indexed,...
    O << ", ";
    break;
  case 16:
    // MCRR2
    printCImmediate(MI, 4, STI, O);
    return;
    break;
  case 17:
    // STLEX, STLEXB, STLEXH, STREX, STREXB, STREXH, SWP, SWPB, t2LDAEXD, t2L...
    printAddrMode7Operand(MI, 2, STI, O);
    return;
    break;
  case 18:
    // VBIFd, VBIFq, VBITd, VBITq, VBSLd, VBSLq, VLD4LNd16, VLD4LNd32, VLD4LN...
    printOperand(MI, 3, STI, O);
    break;
  case 19:
    // VCADDv2f32, VCADDv4f16, VCADDv4f32, VCADDv8f16
    printComplexRotationOp<180, 90>(MI, 3, STI, O);
    return;
    break;
  case 20:
    // VCMLAv2f32, VCMLAv4f16, VCMLAv4f32, VCMLAv8f16
    printComplexRotationOp<90, 0>(MI, 4, STI, O);
    return;
    break;
  case 21:
    // VLD1LNd16, VLD1LNd32, VLD1LNd8, VST1LNd16_UPD, VST1LNd32_UPD, VST1LNd8...
    printAddrMode6Operand(MI, 1, STI, O);
    break;
  case 22:
    // VLD1LNd16_UPD, VLD1LNd32_UPD, VLD1LNd8_UPD
    printAddrMode6Operand(MI, 2, STI, O);
    printAddrMode6OffsetOperand(MI, 4, STI, O);
    return;
    break;
  case 23:
    // VLD2LNd16, VLD2LNd32, VLD2LNd8, VLD2LNq16, VLD2LNq32
    printOperand(MI, 1, STI, O);
    O << '[';
    printNoHashImmediate(MI, 6, STI, O);
    O << "]}, ";
    printAddrMode6Operand(MI, 2, STI, O);
    return;
    break;
  case 24:
    // VLD2LNd16_UPD, VLD2LNd32_UPD, VLD2LNd8_UPD, VLD2LNq16_UPD, VLD2LNq32_U...
    O << "]}, ";
    printAddrMode6Operand(MI, 3, STI, O);
    printAddrMode6OffsetOperand(MI, 5, STI, O);
    return;
    break;
  case 25:
    // VLD3DUPd16_UPD, VLD3DUPd32_UPD, VLD3DUPd8_UPD, VLD3DUPq16_UPD, VLD3DUP...
    printAddrMode6OffsetOperand(MI, 6, STI, O);
    return;
    break;
  case 26:
    // VLD3LNd16, VLD3LNd32, VLD3LNd8, VLD3LNq16, VLD3LNq32
    O << "], ";
    printOperand(MI, 2, STI, O);
    O << '[';
    printNoHashImmediate(MI, 8, STI, O);
    O << "]}, ";
    printAddrMode6Operand(MI, 3, STI, O);
    return;
    break;
  case 27:
    // VLD3LNd16_UPD, VLD3LNd32_UPD, VLD3LNd8_UPD, VLD3LNq16_UPD, VLD3LNq32_U...
    printAddrMode6Operand(MI, 4, STI, O);
    printAddrMode6OffsetOperand(MI, 6, STI, O);
    return;
    break;
  case 28:
    // VMLAslfd, VMLAslfq, VMLAslhd, VMLAslhq, VMLSslfd, VMLSslfq, VMLSslhd, ...
    printVectorIndex(MI, 4, STI, O);
    return;
    break;
  case 29:
    // VMULslfd, VMULslfq, VMULslhd, VMULslhq
    printVectorIndex(MI, 3, STI, O);
    return;
    break;
  case 30:
    // VST2LNd16_UPD, VST2LNd32_UPD, VST2LNd8_UPD, VST2LNq16_UPD, VST2LNq32_U...
    printOperand(MI, 5, STI, O);
    O << '[';
    printNoHashImmediate(MI, 6, STI, O);
    O << "]}, ";
    printAddrMode6Operand(MI, 1, STI, O);
    printAddrMode6OffsetOperand(MI, 3, STI, O);
    return;
    break;
  case 31:
    // VST4d16_UPD, VST4d32_UPD, VST4d8_UPD, VST4q16_UPD, VST4q32_UPD, VST4q8...
    printOperand(MI, 7, STI, O);
    O << "}, ";
    printAddrMode6Operand(MI, 1, STI, O);
    printAddrMode6OffsetOperand(MI, 3, STI, O);
    return;
    break;
  case 32:
    // t2ADCrs, t2ADDrs, t2ANDrs, t2BICrs, t2EORrs, t2ORNrs, t2ORRrs, t2RSBrs...
    printT2SOOperand(MI, 2, STI, O);
    return;
    break;
  case 33:
    // t2ASRri, t2LSRri
    printThumbSRImm(MI, 2, STI, O);
    return;
    break;
  case 34:
    // t2LDRD_PRE, t2STRD_PRE
    printT2AddrModeImm8s4Operand<true>(MI, 3, STI, O);
    O << '!';
    return;
    break;
  case 35:
    // t2LDRDi8, t2STRDi8
    printT2AddrModeImm8s4Operand<false>(MI, 2, STI, O);
    return;
    break;
  case 36:
    // t2STREX
    printT2AddrModeImm0_1020s4Operand(MI, 2, STI, O);
    return;
    break;
  case 37:
    // tADDrSPi
    printThumbS4ImmOperand(MI, 2, STI, O);
    return;
    break;
  }


  // Fragment 7 encoded into 4 bits for 13 unique commands.
  switch ((Bits >> 53) & 15) {
  default: llvm_unreachable("Invalid command number.");
  case 0:
    // ASRi, ASRr, LSLi, LSLr, LSRi, LSRr, RORi, RORr, VLD1LNdWB_register_Asm...
    return;
    break;
  case 1:
    // LDRD_POST, MLA, MLS, SBFX, SMLABB, SMLABT, SMLAD, SMLADX, SMLALBB, SML...
    O << ", ";
    break;
  case 2:
    // MCR, t2MCR, t2MCR2
    printCImmediate(MI, 3, STI, O);
    O << ", ";
    printCImmediate(MI, 4, STI, O);
    O << ", ";
    printOperand(MI, 5, STI, O);
    return;
    break;
  case 3:
    // MCRR, t2MCRR, t2MCRR2
    printOperand(MI, 3, STI, O);
    O << ", ";
    printCImmediate(MI, 4, STI, O);
    return;
    break;
  case 4:
    // PKHBT, t2PKHBT
    printPKHLSLShiftImm(MI, 3, STI, O);
    return;
    break;
  case 5:
    // PKHTB, t2PKHTB
    printPKHASRShiftImm(MI, 3, STI, O);
    return;
    break;
  case 6:
    // SXTAB, SXTAB16, SXTAH, UXTAB, UXTAB16, UXTAH, t2SXTAB, t2SXTAB16, t2SX...
    printRotImmOperand(MI, 3, STI, O);
    return;
    break;
  case 7:
    // USAT, t2USAT
    printShiftImmOperand(MI, 3, STI, O);
    return;
    break;
  case 8:
    // VCMLAv2f32_indexed, VCMLAv4f16_indexed, VCMLAv4f32_indexed, VCMLAv8f16...
    printComplexRotationOp<90, 0>(MI, 5, STI, O);
    return;
    break;
  case 9:
    // VLD3d16, VLD3d16_UPD, VLD3d32, VLD3d32_UPD, VLD3d8, VLD3d8_UPD, VLD3q1...
    O << "}, ";
    break;
  case 10:
    // VLD4LNd16, VLD4LNd32, VLD4LNd8, VLD4LNq16, VLD4LNq32, VST2LNd16, VST2L...
    O << '[';
    break;
  case 11:
    // VST1LNd16_UPD, VST1LNd32_UPD, VST1LNd8_UPD
    printAddrMode6OffsetOperand(MI, 3, STI, O);
    return;
    break;
  case 12:
    // t2LDRD_POST, t2STRD_POST
    printT2AddrModeImm8s4OffsetOperand(MI, 4, STI, O);
    return;
    break;
  }


  // Fragment 8 encoded into 4 bits for 12 unique commands.
  switch ((Bits >> 57) & 15) {
  default: llvm_unreachable("Invalid command number.");
  case 0:
    // LDRD_POST, STRD_POST
    printAddrMode3OffsetOperand(MI, 4, STI, O);
    return;
    break;
  case 1:
    // MLA, MLS, SMLABB, SMLABT, SMLAD, SMLADX, SMLALBB, SMLALBT, SMLALD, SML...
    printOperand(MI, 3, STI, O);
    break;
  case 2:
    // SBFX, UBFX, t2SBFX, t2UBFX
    printImmPlusOneOperand(MI, 3, STI, O);
    return;
    break;
  case 3:
    // VLD3d16, VLD3d32, VLD3d8, VLD3q16, VLD3q32, VLD3q8
    printAddrMode6Operand(MI, 3, STI, O);
    return;
    break;
  case 4:
    // VLD3d16_UPD, VLD3d32_UPD, VLD3d8_UPD, VLD3q16_UPD, VLD3q32_UPD, VLD3q8...
    printAddrMode6Operand(MI, 4, STI, O);
    printAddrMode6OffsetOperand(MI, 6, STI, O);
    return;
    break;
  case 5:
    // VLD4LNd16, VLD4LNd32, VLD4LNd8, VLD4LNq16, VLD4LNq32
    printNoHashImmediate(MI, 10, STI, O);
    O << "]}, ";
    printAddrMode6Operand(MI, 4, STI, O);
    return;
    break;
  case 6:
    // VST2LNd16, VST2LNd32, VST2LNd8, VST2LNq16, VST2LNq32
    printNoHashImmediate(MI, 4, STI, O);
    O << "]}, ";
    printAddrMode6Operand(MI, 0, STI, O);
    return;
    break;
  case 7:
    // VST3LNd16, VST3LNd32, VST3LNd8, VST3LNq16, VST3LNq32
    printNoHashImmediate(MI, 5, STI, O);
    O << "], ";
    printOperand(MI, 4, STI, O);
    O << '[';
    printNoHashImmediate(MI, 5, STI, O);
    O << "]}, ";
    printAddrMode6Operand(MI, 0, STI, O);
    return;
    break;
  case 8:
    // VST3d16, VST3d32, VST3d8, VST3q16, VST3q32, VST3q8
    printAddrMode6Operand(MI, 0, STI, O);
    return;
    break;
  case 9:
    // VST4LNd16, VST4LNd32, VST4LNd8, VST4LNq16, VST4LNq32
    printNoHashImmediate(MI, 6, STI, O);
    O << "], ";
    printOperand(MI, 4, STI, O);
    O << '[';
    printNoHashImmediate(MI, 6, STI, O);
    O << "], ";
    printOperand(MI, 5, STI, O);
    O << '[';
    printNoHashImmediate(MI, 6, STI, O);
    O << "]}, ";
    printAddrMode6Operand(MI, 0, STI, O);
    return;
    break;
  case 10:
    // VST4d16, VST4d32, VST4d8, VST4q16, VST4q32, VST4q8
    printOperand(MI, 5, STI, O);
    O << "}, ";
    printAddrMode6Operand(MI, 0, STI, O);
    return;
    break;
  case 11:
    // t2STLEXD, t2STREXD
    printAddrMode7Operand(MI, 3, STI, O);
    return;
    break;
  }


  // Fragment 9 encoded into 1 bits for 2 unique commands.
  if ((Bits >> 61) & 1) {
    // VLD4d16, VLD4d16_UPD, VLD4d32, VLD4d32_UPD, VLD4d8, VLD4d8_UPD, VLD4q1...
    O << "}, ";
  } else {
    // MLA, MLS, SMLABB, SMLABT, SMLAD, SMLADX, SMLALBB, SMLALBT, SMLALD, SML...
    return;
  }


  // Fragment 10 encoded into 1 bits for 2 unique commands.
  if ((Bits >> 62) & 1) {
    // VLD4d16_UPD, VLD4d32_UPD, VLD4d8_UPD, VLD4q16_UPD, VLD4q32_UPD, VLD4q8...
    printAddrMode6Operand(MI, 5, STI, O);
    printAddrMode6OffsetOperand(MI, 7, STI, O);
    return;
  } else {
    // VLD4d16, VLD4d32, VLD4d8, VLD4q16, VLD4q32, VLD4q8
    printAddrMode6Operand(MI, 4, STI, O);
    return;
  }

}


/// getRegisterName - This method is automatically generated by tblgen
/// from the register set description.  This returns the assembler name
/// for the specified register.
const char *ARMInstPrinter::
getRegisterName(unsigned RegNo, unsigned AltIdx) {
  assert(RegNo && RegNo < 289 && "Invalid register number!");

  static const char AsmStrsNoRegAltName[] = {
  /* 0 */ 'D', '4', '_', 'D', '6', '_', 'D', '8', '_', 'D', '1', '0', 0,
  /* 13 */ 'D', '7', '_', 'D', '8', '_', 'D', '9', '_', 'D', '1', '0', 0,
  /* 26 */ 'Q', '7', '_', 'Q', '8', '_', 'Q', '9', '_', 'Q', '1', '0', 0,
  /* 39 */ 'd', '1', '0', 0,
  /* 43 */ 'q', '1', '0', 0,
  /* 47 */ 'r', '1', '0', 0,
  /* 51 */ 's', '1', '0', 0,
  /* 55 */ 'D', '1', '4', '_', 'D', '1', '6', '_', 'D', '1', '8', '_', 'D', '2', '0', 0,
  /* 71 */ 'D', '1', '7', '_', 'D', '1', '8', '_', 'D', '1', '9', '_', 'D', '2', '0', 0,
  /* 87 */ 'd', '2', '0', 0,
  /* 91 */ 's', '2', '0', 0,
  /* 95 */ 'D', '2', '4', '_', 'D', '2', '6', '_', 'D', '2', '8', '_', 'D', '3', '0', 0,
  /* 111 */ 'D', '2', '7', '_', 'D', '2', '8', '_', 'D', '2', '9', '_', 'D', '3', '0', 0,
  /* 127 */ 'd', '3', '0', 0,
  /* 131 */ 's', '3', '0', 0,
  /* 135 */ 'd', '0', 0,
  /* 138 */ 'q', '0', 0,
  /* 141 */ 'm', 'v', 'f', 'r', '0', 0,
  /* 147 */ 's', '0', 0,
  /* 150 */ 'D', '9', '_', 'D', '1', '0', '_', 'D', '1', '1', 0,
  /* 161 */ 'D', '5', '_', 'D', '7', '_', 'D', '9', '_', 'D', '1', '1', 0,
  /* 174 */ 'Q', '8', '_', 'Q', '9', '_', 'Q', '1', '0', '_', 'Q', '1', '1', 0,
  /* 188 */ 'R', '1', '0', '_', 'R', '1', '1', 0,
  /* 196 */ 'd', '1', '1', 0,
  /* 200 */ 'q', '1', '1', 0,
  /* 204 */ 'r', '1', '1', 0,
  /* 208 */ 's', '1', '1', 0,
  /* 212 */ 'D', '1', '9', '_', 'D', '2', '0', '_', 'D', '2', '1', 0,
  /* 224 */ 'D', '1', '5', '_', 'D', '1', '7', '_', 'D', '1', '9', '_', 'D', '2', '1', 0,
  /* 240 */ 'd', '2', '1', 0,
  /* 244 */ 's', '2', '1', 0,
  /* 248 */ 'D', '2', '9', '_', 'D', '3', '0', '_', 'D', '3', '1', 0,
  /* 260 */ 'D', '2', '5', '_', 'D', '2', '7', '_', 'D', '2', '9', '_', 'D', '3', '1', 0,
  /* 276 */ 'd', '3', '1', 0,
  /* 280 */ 's', '3', '1', 0,
  /* 284 */ 'Q', '0', '_', 'Q', '1', 0,
  /* 290 */ 'R', '0', '_', 'R', '1', 0,
  /* 296 */ 'd', '1', 0,
  /* 299 */ 'q', '1', 0,
  /* 302 */ 'm', 'v', 'f', 'r', '1', 0,
  /* 308 */ 's', '1', 0,
  /* 311 */ 'D', '6', '_', 'D', '8', '_', 'D', '1', '0', '_', 'D', '1', '2', 0,
  /* 325 */ 'D', '9', '_', 'D', '1', '0', '_', 'D', '1', '1', '_', 'D', '1', '2', 0,
  /* 340 */ 'Q', '9', '_', 'Q', '1', '0', '_', 'Q', '1', '1', '_', 'Q', '1', '2', 0,
  /* 355 */ 'd', '1', '2', 0,
  /* 359 */ 'q', '1', '2', 0,
  /* 363 */ 'r', '1', '2', 0,
  /* 367 */ 's', '1', '2', 0,
  /* 371 */ 'D', '1', '6', '_', 'D', '1', '8', '_', 'D', '2', '0', '_', 'D', '2', '2', 0,
  /* 387 */ 'D', '1', '9', '_', 'D', '2', '0', '_', 'D', '2', '1', '_', 'D', '2', '2', 0,
  /* 403 */ 'd', '2', '2', 0,
  /* 407 */ 's', '2', '2', 0,
  /* 411 */ 'D', '0', '_', 'D', '2', 0,
  /* 417 */ 'D', '0', '_', 'D', '1', '_', 'D', '2', 0,
  /* 426 */ 'Q', '1', '_', 'Q', '2', 0,
  /* 432 */ 'd', '2', 0,
  /* 435 */ 'q', '2', 0,
  /* 438 */ 'm', 'v', 'f', 'r', '2', 0,
  /* 444 */ 's', '2', 0,
  /* 447 */ 'f', 'p', 'i', 'n', 's', 't', '2', 0,
  /* 455 */ 'D', '7', '_', 'D', '9', '_', 'D', '1', '1', '_', 'D', '1', '3', 0,
  /* 469 */ 'D', '1', '1', '_', 'D', '1', '2', '_', 'D', '1', '3', 0,
  /* 481 */ 'Q', '1', '0', '_', 'Q', '1', '1', '_', 'Q', '1', '2', '_', 'Q', '1', '3', 0,
  /* 497 */ 'd', '1', '3', 0,
  /* 501 */ 'q', '1', '3', 0,
  /* 505 */ 's', '1', '3', 0,
  /* 509 */ 'D', '1', '7', '_', 'D', '1', '9', '_', 'D', '2', '1', '_', 'D', '2', '3', 0,
  /* 525 */ 'D', '2', '1', '_', 'D', '2', '2', '_', 'D', '2', '3', 0,
  /* 537 */ 'd', '2', '3', 0,
  /* 541 */ 's', '2', '3', 0,
  /* 545 */ 'D', '1', '_', 'D', '3', 0,
  /* 551 */ 'D', '1', '_', 'D', '2', '_', 'D', '3', 0,
  /* 560 */ 'Q', '0', '_', 'Q', '1', '_', 'Q', '2', '_', 'Q', '3', 0,
  /* 572 */ 'R', '2', '_', 'R', '3', 0,
  /* 578 */ 'd', '3', 0,
  /* 581 */ 'q', '3', 0,
  /* 584 */ 'r', '3', 0,
  /* 587 */ 's', '3', 0,
  /* 590 */ 'D', '8', '_', 'D', '1', '0', '_', 'D', '1', '2', '_', 'D', '1', '4', 0,
  /* 605 */ 'D', '1', '1', '_', 'D', '1', '2', '_', 'D', '1', '3', '_', 'D', '1', '4', 0,
  /* 621 */ 'Q', '1', '1', '_', 'Q', '1', '2', '_', 'Q', '1', '3', '_', 'Q', '1', '4', 0,
  /* 637 */ 'd', '1', '4', 0,
  /* 641 */ 'q', '1', '4', 0,
  /* 645 */ 's', '1', '4', 0,
  /* 649 */ 'D', '1', '8', '_', 'D', '2', '0', '_', 'D', '2', '2', '_', 'D', '2', '4', 0,
  /* 665 */ 'D', '2', '1', '_', 'D', '2', '2', '_', 'D', '2', '3', '_', 'D', '2', '4', 0,
  /* 681 */ 'd', '2', '4', 0,
  /* 685 */ 's', '2', '4', 0,
  /* 689 */ 'D', '0', '_', 'D', '2', '_', 'D', '4', 0,
  /* 698 */ 'D', '1', '_', 'D', '2', '_', 'D', '3', '_', 'D', '4', 0,
  /* 710 */ 'Q', '1', '_', 'Q', '2', '_', 'Q', '3', '_', 'Q', '4', 0,
  /* 722 */ 'd', '4', 0,
  /* 725 */ 'q', '4', 0,
  /* 728 */ 'r', '4', 0,
  /* 731 */ 's', '4', 0,
  /* 734 */ 'D', '9', '_', 'D', '1', '1', '_', 'D', '1', '3', '_', 'D', '1', '5', 0,
  /* 749 */ 'D', '1', '3', '_', 'D', '1', '4', '_', 'D', '1', '5', 0,
  /* 761 */ 'Q', '1', '2', '_', 'Q', '1', '3', '_', 'Q', '1', '4', '_', 'Q', '1', '5', 0,
  /* 777 */ 'd', '1', '5', 0,
  /* 781 */ 'q', '1', '5', 0,
  /* 785 */ 's', '1', '5', 0,
  /* 789 */ 'D', '1', '9', '_', 'D', '2', '1', '_', 'D', '2', '3', '_', 'D', '2', '5', 0,
  /* 805 */ 'D', '2', '3', '_', 'D', '2', '4', '_', 'D', '2', '5', 0,
  /* 817 */ 'd', '2', '5', 0,
  /* 821 */ 's', '2', '5', 0,
  /* 825 */ 'D', '1', '_', 'D', '3', '_', 'D', '5', 0,
  /* 834 */ 'D', '3', '_', 'D', '4', '_', 'D', '5', 0,
  /* 843 */ 'Q', '2', '_', 'Q', '3', '_', 'Q', '4', '_', 'Q', '5', 0,
  /* 855 */ 'R', '4', '_', 'R', '5', 0,
  /* 861 */ 'd', '5', 0,
  /* 864 */ 'q', '5', 0,
  /* 867 */ 'r', '5', 0,
  /* 870 */ 's', '5', 0,
  /* 873 */ 'D', '1', '0', '_', 'D', '1', '2', '_', 'D', '1', '4', '_', 'D', '1', '6', 0,
  /* 889 */ 'D', '1', '3', '_', 'D', '1', '4', '_', 'D', '1', '5', '_', 'D', '1', '6', 0,
  /* 905 */ 'd', '1', '6', 0,
  /* 909 */ 's', '1', '6', 0,
  /* 913 */ 'D', '2', '0', '_', 'D', '2', '2', '_', 'D', '2', '4', '_', 'D', '2', '6', 0,
  /* 929 */ 'D', '2', '3', '_', 'D', '2', '4', '_', 'D', '2', '5', '_', 'D', '2', '6', 0,
  /* 945 */ 'd', '2', '6', 0,
  /* 949 */ 's', '2', '6', 0,
  /* 953 */ 'D', '0', '_', 'D', '2', '_', 'D', '4', '_', 'D', '6', 0,
  /* 965 */ 'D', '3', '_', 'D', '4', '_', 'D', '5', '_', 'D', '6', 0,
  /* 977 */ 'Q', '3', '_', 'Q', '4', '_', 'Q', '5', '_', 'Q', '6', 0,
  /* 989 */ 'd', '6', 0,
  /* 992 */ 'q', '6', 0,
  /* 995 */ 'r', '6', 0,
  /* 998 */ 's', '6', 0,
  /* 1001 */ 'D', '1', '1', '_', 'D', '1', '3', '_', 'D', '1', '5', '_', 'D', '1', '7', 0,
  /* 1017 */ 'D', '1', '5', '_', 'D', '1', '6', '_', 'D', '1', '7', 0,
  /* 1029 */ 'd', '1', '7', 0,
  /* 1033 */ 's', '1', '7', 0,
  /* 1037 */ 'D', '2', '1', '_', 'D', '2', '3', '_', 'D', '2', '5', '_', 'D', '2', '7', 0,
  /* 1053 */ 'D', '2', '5', '_', 'D', '2', '6', '_', 'D', '2', '7', 0,
  /* 1065 */ 'd', '2', '7', 0,
  /* 1069 */ 's', '2', '7', 0,
  /* 1073 */ 'D', '1', '_', 'D', '3', '_', 'D', '5', '_', 'D', '7', 0,
  /* 1085 */ 'D', '5', '_', 'D', '6', '_', 'D', '7', 0,
  /* 1094 */ 'Q', '4', '_', 'Q', '5', '_', 'Q', '6', '_', 'Q', '7', 0,
  /* 1106 */ 'R', '6', '_', 'R', '7', 0,
  /* 1112 */ 'd', '7', 0,
  /* 1115 */ 'q', '7', 0,
  /* 1118 */ 'r', '7', 0,
  /* 1121 */ 's', '7', 0,
  /* 1124 */ 'D', '1', '2', '_', 'D', '1', '4', '_', 'D', '1', '6', '_', 'D', '1', '8', 0,
  /* 1140 */ 'D', '1', '5', '_', 'D', '1', '6', '_', 'D', '1', '7', '_', 'D', '1', '8', 0,
  /* 1156 */ 'd', '1', '8', 0,
  /* 1160 */ 's', '1', '8', 0,
  /* 1164 */ 'D', '2', '2', '_', 'D', '2', '4', '_', 'D', '2', '6', '_', 'D', '2', '8', 0,
  /* 1180 */ 'D', '2', '5', '_', 'D', '2', '6', '_', 'D', '2', '7', '_', 'D', '2', '8', 0,
  /* 1196 */ 'd', '2', '8', 0,
  /* 1200 */ 's', '2', '8', 0,
  /* 1204 */ 'D', '2', '_', 'D', '4', '_', 'D', '6', '_', 'D', '8', 0,
  /* 1216 */ 'D', '5', '_', 'D', '6', '_', 'D', '7', '_', 'D', '8', 0,
  /* 1228 */ 'Q', '5', '_', 'Q', '6', '_', 'Q', '7', '_', 'Q', '8', 0,
  /* 1240 */ 'd', '8', 0,
  /* 1243 */ 'q', '8', 0,
  /* 1246 */ 'r', '8', 0,
  /* 1249 */ 's', '8', 0,
  /* 1252 */ 'D', '1', '3', '_', 'D', '1', '5', '_', 'D', '1', '7', '_', 'D', '1', '9', 0,
  /* 1268 */ 'D', '1', '7', '_', 'D', '1', '8', '_', 'D', '1', '9', 0,
  /* 1280 */ 'd', '1', '9', 0,
  /* 1284 */ 's', '1', '9', 0,
  /* 1288 */ 'D', '2', '3', '_', 'D', '2', '5', '_', 'D', '2', '7', '_', 'D', '2', '9', 0,
  /* 1304 */ 'D', '2', '7', '_', 'D', '2', '8', '_', 'D', '2', '9', 0,
  /* 1316 */ 'd', '2', '9', 0,
  /* 1320 */ 's', '2', '9', 0,
  /* 1324 */ 'D', '3', '_', 'D', '5', '_', 'D', '7', '_', 'D', '9', 0,
  /* 1336 */ 'D', '7', '_', 'D', '8', '_', 'D', '9', 0,
  /* 1345 */ 'Q', '6', '_', 'Q', '7', '_', 'Q', '8', '_', 'Q', '9', 0,
  /* 1357 */ 'R', '8', '_', 'R', '9', 0,
  /* 1363 */ 'd', '9', 0,
  /* 1366 */ 'q', '9', 0,
  /* 1369 */ 'r', '9', 0,
  /* 1372 */ 's', '9', 0,
  /* 1375 */ 'R', '1', '2', '_', 'S', 'P', 0,
  /* 1382 */ 'p', 'c', 0,
  /* 1385 */ 'f', 'p', 'e', 'x', 'c', 0,
  /* 1391 */ 'f', 'p', 's', 'i', 'd', 0,
  /* 1397 */ 'i', 't', 's', 't', 'a', 't', 'e', 0,
  /* 1405 */ 's', 'p', 0,
  /* 1408 */ 'f', 'p', 's', 'c', 'r', 0,
  /* 1414 */ 'l', 'r', 0,
  /* 1417 */ 'a', 'p', 's', 'r', 0,
  /* 1422 */ 'c', 'p', 's', 'r', 0,
  /* 1427 */ 's', 'p', 's', 'r', 0,
  /* 1432 */ 'f', 'p', 'i', 'n', 's', 't', 0,
  /* 1439 */ 'f', 'p', 's', 'c', 'r', '_', 'n', 'z', 'c', 'v', 0,
  /* 1450 */ 'a', 'p', 's', 'r', '_', 'n', 'z', 'c', 'v', 0,
  };

  static const uint16_t RegAsmOffsetNoRegAltName[] = {
    1417, 1450, 1422, 1385, 1432, 1408, 1439, 1391, 1397, 1414, 1382, 1405, 1427, 135, 
    296, 432, 578, 722, 861, 989, 1112, 1240, 1363, 39, 196, 355, 497, 637, 
    777, 905, 1029, 1156, 1280, 87, 240, 403, 537, 681, 817, 945, 1065, 1196, 
    1316, 127, 276, 447, 141, 302, 438, 138, 299, 435, 581, 725, 864, 992, 
    1115, 1243, 1366, 43, 200, 359, 501, 641, 781, 144, 305, 441, 584, 728, 
    867, 995, 1118, 1246, 1369, 47, 204, 363, 147, 308, 444, 587, 731, 870, 
    998, 1121, 1249, 1372, 51, 208, 367, 505, 645, 785, 909, 1033, 1160, 1284, 
    91, 244, 407, 541, 685, 821, 949, 1069, 1200, 1320, 131, 280, 411, 545, 
    692, 828, 959, 1079, 1210, 1330, 6, 167, 317, 461, 597, 741, 881, 1009, 
    1132, 1260, 63, 232, 379, 517, 657, 797, 921, 1045, 1172, 1296, 103, 268, 
    284, 426, 566, 716, 849, 983, 1100, 1234, 1351, 32, 180, 347, 489, 629, 
    769, 560, 710, 843, 977, 1094, 1228, 1345, 26, 174, 340, 481, 621, 761, 
    1375, 290, 572, 855, 1106, 1357, 188, 417, 551, 701, 834, 968, 1085, 1219, 
    1336, 16, 150, 328, 469, 609, 749, 893, 1017, 1144, 1268, 75, 212, 391, 
    525, 669, 805, 933, 1053, 1184, 1304, 115, 248, 689, 825, 956, 1076, 1207, 
    1327, 3, 164, 314, 458, 593, 737, 877, 1005, 1128, 1256, 59, 228, 375, 
    513, 653, 793, 917, 1041, 1168, 1292, 99, 264, 953, 1073, 1204, 1324, 0, 
    161, 311, 455, 590, 734, 873, 1001, 1124, 1252, 55, 224, 371, 509, 649, 
    789, 913, 1037, 1164, 1288, 95, 260, 420, 704, 971, 1222, 19, 332, 613, 
    897, 1148, 79, 395, 673, 937, 1188, 119, 698, 965, 1216, 13, 325, 605, 
    889, 1140, 71, 387, 665, 929, 1180, 111, 
  };

  static const char AsmStrsRegNamesRaw[] = {
  /* 0 */ 'r', '1', '3', 0,
  /* 4 */ 'r', '1', '4', 0,
  /* 8 */ 'r', '1', '5', 0,
  };

  static const uint8_t RegAsmOffsetRegNamesRaw[] = {
    3, 3, 3, 3, 3, 3, 3, 3, 3, 4, 8, 0, 3, 3, 
    3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
    3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
    3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
    3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
    3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
    3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
    3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
    3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
    3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
    3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
    3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
    3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
    3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
    3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
    3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
    3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
    3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
    3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
    3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
    3, 3, 3, 3, 3, 3, 3, 3, 
  };

  switch(AltIdx) {
  default: llvm_unreachable("Invalid register alt name index!");
  case ARM::NoRegAltName:
    assert(*(AsmStrsNoRegAltName+RegAsmOffsetNoRegAltName[RegNo-1]) &&
           "Invalid alt name index for register!");
    return AsmStrsNoRegAltName+RegAsmOffsetNoRegAltName[RegNo-1];
  case ARM::RegNamesRaw:
    if (!*(AsmStrsRegNamesRaw+RegAsmOffsetRegNamesRaw[RegNo-1]))
      return getRegisterName(RegNo, ARM::NoRegAltName);
    return AsmStrsRegNamesRaw+RegAsmOffsetRegNamesRaw[RegNo-1];
  }
}

#ifdef PRINT_ALIAS_INSTR
#undef PRINT_ALIAS_INSTR

bool ARMInstPrinter::printAliasInstr(const MCInst *MI, const MCSubtargetInfo &STI, raw_ostream &OS) {
  const char *AsmString;
  switch (MI->getOpcode()) {
  default: return false;
  case ARM::DSB:
    if (MI->getNumOperands() == 1 &&
        MI->getOperand(0).isImm() &&
        MI->getOperand(0).getImm() == 0 &&
        !STI.getFeatureBits()[ARM::ModeThumb] &&
        STI.getFeatureBits()[ARM::FeatureDB]) {
      // (DSB 0)
      AsmString = "ssbb";
      break;
    }
    if (MI->getNumOperands() == 1 &&
        MI->getOperand(0).isImm() &&
        MI->getOperand(0).getImm() == 4 &&
        !STI.getFeatureBits()[ARM::ModeThumb] &&
        STI.getFeatureBits()[ARM::FeatureDB]) {
      // (DSB 4)
      AsmString = "pssbb";
      break;
    }
    if (MI->getNumOperands() == 1 &&
        MI->getOperand(0).isImm() &&
        MI->getOperand(0).getImm() == 12 &&
        !STI.getFeatureBits()[ARM::ModeThumb] &&
        STI.getFeatureBits()[ARM::FeatureDFB]) {
      // (DSB 12)
      AsmString = "dfb";
      break;
    }
    return false;
  case ARM::HINT:
    if (MI->getNumOperands() == 3 &&
        MI->getOperand(0).isImm() &&
        MI->getOperand(0).getImm() == 0 &&
        !STI.getFeatureBits()[ARM::ModeThumb] &&
        STI.getFeatureBits()[ARM::HasV6KOps]) {
      // (HINT 0, pred:$p)
      AsmString = "nop$\xFF\x02\x01";
      break;
    }
    if (MI->getNumOperands() == 3 &&
        MI->getOperand(0).isImm() &&
        MI->getOperand(0).getImm() == 1 &&
        !STI.getFeatureBits()[ARM::ModeThumb] &&
        STI.getFeatureBits()[ARM::HasV6KOps]) {
      // (HINT 1, pred:$p)
      AsmString = "yield$\xFF\x02\x01";
      break;
    }
    if (MI->getNumOperands() == 3 &&
        MI->getOperand(0).isImm() &&
        MI->getOperand(0).getImm() == 2 &&
        !STI.getFeatureBits()[ARM::ModeThumb] &&
        STI.getFeatureBits()[ARM::HasV6KOps]) {
      // (HINT 2, pred:$p)
      AsmString = "wfe$\xFF\x02\x01";
      break;
    }
    if (MI->getNumOperands() == 3 &&
        MI->getOperand(0).isImm() &&
        MI->getOperand(0).getImm() == 3 &&
        !STI.getFeatureBits()[ARM::ModeThumb] &&
        STI.getFeatureBits()[ARM::HasV6KOps]) {
      // (HINT 3, pred:$p)
      AsmString = "wfi$\xFF\x02\x01";
      break;
    }
    if (MI->getNumOperands() == 3 &&
        MI->getOperand(0).isImm() &&
        MI->getOperand(0).getImm() == 4 &&
        !STI.getFeatureBits()[ARM::ModeThumb] &&
        STI.getFeatureBits()[ARM::HasV6KOps]) {
      // (HINT 4, pred:$p)
      AsmString = "sev$\xFF\x02\x01";
      break;
    }
    if (MI->getNumOperands() == 3 &&
        MI->getOperand(0).isImm() &&
        MI->getOperand(0).getImm() == 5 &&
        !STI.getFeatureBits()[ARM::ModeThumb] &&
        STI.getFeatureBits()[ARM::HasV8Ops]) {
      // (HINT 5, pred:$p)
      AsmString = "sevl$\xFF\x02\x01";
      break;
    }
    if (MI->getNumOperands() == 3 &&
        MI->getOperand(0).isImm() &&
        MI->getOperand(0).getImm() == 16 &&
        !STI.getFeatureBits()[ARM::ModeThumb] &&
        STI.getFeatureBits()[ARM::FeatureRAS]) {
      // (HINT 16, pred:$p)
      AsmString = "esb$\xFF\x02\x01";
      break;
    }
    if (MI->getNumOperands() == 3 &&
        MI->getOperand(0).isImm() &&
        MI->getOperand(0).getImm() == 20 &&
        !STI.getFeatureBits()[ARM::ModeThumb] &&
        STI.getFeatureBits()[ARM::HasV6KOps]) {
      // (HINT 20, pred:$p)
      AsmString = "csdb$\xFF\x02\x01";
      break;
    }
    return false;
  case ARM::t2DSB:
    if (MI->getNumOperands() == 3 &&
        MI->getOperand(0).isImm() &&
        MI->getOperand(0).getImm() == 0 &&
        MI->getOperand(1).isImm() &&
        MI->getOperand(1).getImm() == 14 &&
        MI->getOperand(2).isImm() &&
        MI->getOperand(2).getImm() == 0 &&
        STI.getFeatureBits()[ARM::FeatureDB] &&
        STI.getFeatureBits()[ARM::ModeThumb] &&
        STI.getFeatureBits()[ARM::FeatureThumb2]) {
      // (t2DSB 0, 14, 0)
      AsmString = "ssbb";
      break;
    }
    if (MI->getNumOperands() == 3 &&
        MI->getOperand(0).isImm() &&
        MI->getOperand(0).getImm() == 4 &&
        MI->getOperand(1).isImm() &&
        MI->getOperand(1).getImm() == 14 &&
        MI->getOperand(2).isImm() &&
        MI->getOperand(2).getImm() == 0 &&
        STI.getFeatureBits()[ARM::FeatureDB] &&
        STI.getFeatureBits()[ARM::ModeThumb] &&
        STI.getFeatureBits()[ARM::FeatureThumb2]) {
      // (t2DSB 4, 14, 0)
      AsmString = "pssbb";
      break;
    }
    if (MI->getNumOperands() == 3 &&
        MI->getOperand(0).isImm() &&
        MI->getOperand(0).getImm() == 12 &&
        STI.getFeatureBits()[ARM::FeatureDFB]) {
      // (t2DSB 12, pred:$p)
      AsmString = "dfb$\xFF\x02\x01";
      break;
    }
    return false;
  case ARM::t2HINT:
    if (MI->getNumOperands() == 3 &&
        MI->getOperand(0).isImm() &&
        MI->getOperand(0).getImm() == 0 &&
        STI.getFeatureBits()[ARM::ModeThumb] &&
        STI.getFeatureBits()[ARM::FeatureThumb2]) {
      // (t2HINT 0, pred:$p)
      AsmString = "nop$\xFF\x02\x01.w";
      break;
    }
    if (MI->getNumOperands() == 3 &&
        MI->getOperand(0).isImm() &&
        MI->getOperand(0).getImm() == 1 &&
        STI.getFeatureBits()[ARM::ModeThumb] &&
        STI.getFeatureBits()[ARM::FeatureThumb2]) {
      // (t2HINT 1, pred:$p)
      AsmString = "yield$\xFF\x02\x01.w";
      break;
    }
    if (MI->getNumOperands() == 3 &&
        MI->getOperand(0).isImm() &&
        MI->getOperand(0).getImm() == 2 &&
        STI.getFeatureBits()[ARM::ModeThumb] &&
        STI.getFeatureBits()[ARM::FeatureThumb2]) {
      // (t2HINT 2, pred:$p)
      AsmString = "wfe$\xFF\x02\x01.w";
      break;
    }
    if (MI->getNumOperands() == 3 &&
        MI->getOperand(0).isImm() &&
        MI->getOperand(0).getImm() == 3 &&
        STI.getFeatureBits()[ARM::ModeThumb] &&
        STI.getFeatureBits()[ARM::FeatureThumb2]) {
      // (t2HINT 3, pred:$p)
      AsmString = "wfi$\xFF\x02\x01.w";
      break;
    }
    if (MI->getNumOperands() == 3 &&
        MI->getOperand(0).isImm() &&
        MI->getOperand(0).getImm() == 4 &&
        STI.getFeatureBits()[ARM::ModeThumb] &&
        STI.getFeatureBits()[ARM::FeatureThumb2]) {
      // (t2HINT 4, pred:$p)
      AsmString = "sev$\xFF\x02\x01.w";
      break;
    }
    if (MI->getNumOperands() == 3 &&
        MI->getOperand(0).isImm() &&
        MI->getOperand(0).getImm() == 5 &&
        STI.getFeatureBits()[ARM::ModeThumb] &&
        STI.getFeatureBits()[ARM::FeatureThumb2] &&
        STI.getFeatureBits()[ARM::HasV8Ops]) {
      // (t2HINT 5, pred:$p)
      AsmString = "sevl$\xFF\x02\x01.w";
      break;
    }
    if (MI->getNumOperands() == 3 &&
        MI->getOperand(0).isImm() &&
        MI->getOperand(0).getImm() == 16 &&
        STI.getFeatureBits()[ARM::ModeThumb] &&
        STI.getFeatureBits()[ARM::FeatureThumb2] &&
        STI.getFeatureBits()[ARM::FeatureRAS]) {
      // (t2HINT 16, pred:$p)
      AsmString = "esb$\xFF\x02\x01.w";
      break;
    }
    if (MI->getNumOperands() == 3 &&
        MI->getOperand(0).isImm() &&
        MI->getOperand(0).getImm() == 20 &&
        STI.getFeatureBits()[ARM::ModeThumb] &&
        STI.getFeatureBits()[ARM::FeatureThumb2]) {
      // (t2HINT 20, pred:$p)
      AsmString = "csdb$\xFF\x02\x01";
      break;
    }
    return false;
  case ARM::t2SUBS_PC_LR:
    if (MI->getNumOperands() == 3 &&
        MI->getOperand(0).isImm() &&
        MI->getOperand(0).getImm() == 0 &&
        STI.getFeatureBits()[ARM::ModeThumb] &&
        STI.getFeatureBits()[ARM::FeatureThumb2] &&
        STI.getFeatureBits()[ARM::FeatureVirtualization]) {
      // (t2SUBS_PC_LR 0, pred:$p)
      AsmString = "eret$\xFF\x02\x01";
      break;
    }
    return false;
  case ARM::tHINT:
    if (MI->getNumOperands() == 3 &&
        MI->getOperand(0).isImm() &&
        MI->getOperand(0).getImm() == 0 &&
        STI.getFeatureBits()[ARM::ModeThumb] &&
        STI.getFeatureBits()[ARM::HasV6MOps]) {
      // (tHINT 0, pred:$p)
      AsmString = "nop$\xFF\x02\x01";
      break;
    }
    if (MI->getNumOperands() == 3 &&
        MI->getOperand(0).isImm() &&
        MI->getOperand(0).getImm() == 1 &&
        STI.getFeatureBits()[ARM::ModeThumb] &&
        STI.getFeatureBits()[ARM::HasV6MOps]) {
      // (tHINT 1, pred:$p)
      AsmString = "yield$\xFF\x02\x01";
      break;
    }
    if (MI->getNumOperands() == 3 &&
        MI->getOperand(0).isImm() &&
        MI->getOperand(0).getImm() == 2 &&
        STI.getFeatureBits()[ARM::ModeThumb] &&
        STI.getFeatureBits()[ARM::HasV6MOps]) {
      // (tHINT 2, pred:$p)
      AsmString = "wfe$\xFF\x02\x01";
      break;
    }
    if (MI->getNumOperands() == 3 &&
        MI->getOperand(0).isImm() &&
        MI->getOperand(0).getImm() == 3 &&
        STI.getFeatureBits()[ARM::ModeThumb] &&
        STI.getFeatureBits()[ARM::HasV6MOps]) {
      // (tHINT 3, pred:$p)
      AsmString = "wfi$\xFF\x02\x01";
      break;
    }
    if (MI->getNumOperands() == 3 &&
        MI->getOperand(0).isImm() &&
        MI->getOperand(0).getImm() == 4 &&
        STI.getFeatureBits()[ARM::ModeThumb] &&
        STI.getFeatureBits()[ARM::HasV6MOps]) {
      // (tHINT 4, pred:$p)
      AsmString = "sev$\xFF\x02\x01";
      break;
    }
    if (MI->getNumOperands() == 3 &&
        MI->getOperand(0).isImm() &&
        MI->getOperand(0).getImm() == 5 &&
        STI.getFeatureBits()[ARM::ModeThumb] &&
        STI.getFeatureBits()[ARM::FeatureThumb2] &&
        STI.getFeatureBits()[ARM::HasV8Ops]) {
      // (tHINT 5, pred:$p)
      AsmString = "sevl$\xFF\x02\x01";
      break;
    }
    return false;
  }

  unsigned I = 0;
  while (AsmString[I] != ' ' && AsmString[I] != '\t' &&
         AsmString[I] != '$' && AsmString[I] != '\0')
    ++I;
  OS << '\t' << StringRef(AsmString, I);
  if (AsmString[I] != '\0') {
    if (AsmString[I] == ' ' || AsmString[I] == '\t') {
      OS << '\t';
      ++I;
    }
    do {
      if (AsmString[I] == '$') {
        ++I;
        if (AsmString[I] == (char)0xff) {
          ++I;
          int OpIdx = AsmString[I++] - 1;
          int PrintMethodIdx = AsmString[I++] - 1;
          printCustomAliasOperand(MI, OpIdx, PrintMethodIdx, STI, OS);
        } else
          printOperand(MI, unsigned(AsmString[I++]) - 1, STI, OS);
      } else {
        OS << AsmString[I++];
      }
    } while (AsmString[I] != '\0');
  }

  return true;
}

void ARMInstPrinter::printCustomAliasOperand(
         const MCInst *MI, unsigned OpIdx,
         unsigned PrintMethodIdx,
         const MCSubtargetInfo &STI,
         raw_ostream &OS) {
  switch (PrintMethodIdx) {
  default:
    llvm_unreachable("Unknown PrintMethod kind");
    break;
  case 0:
    printPredicateOperand(MI, OpIdx, STI, OS);
    break;
  }
}

#endif // PRINT_ALIAS_INSTR

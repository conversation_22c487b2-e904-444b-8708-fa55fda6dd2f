/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* X86 EVEX2VEX tables                                                        *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

// X86 EVEX encoded instructions that have a VEX 128 encoding
// (table format: <EVEX opcode, VEX-128 opcode>).
static const X86EvexToVexCompressTableEntry X86EvexToVex128CompressTable[] = {
  // EVEX scalar with corresponding VEX.
  { X86::VADDPDZ128rm, X86::VADDPDrm },
  { X86::VADDPDZ128rr, X86::VADDPDrr },
  { X86::VADDPSZ128rm, X86::VADDPSrm },
  { X86::VADDPSZ128rr, X86::VADDPSrr },
  { X86::VADDSDZrm, X86::VADDSDrm },
  { X86::VADDSDZrm_Int, X86::VADDSDrm_Int },
  { X86::VADDSDZrr, X86::VADDSDrr },
  { X86::VADDSDZrr_Int, X86::VADDSDrr_Int },
  { X86::VADDSSZrm, X86::VADDSSrm },
  { X86::VADDSSZrm_Int, X86::VADDSSrm_Int },
  { X86::VADDSSZrr, X86::VADDSSrr },
  { X86::VADDSSZrr_Int, X86::VADDSSrr_Int },
  { X86::VAESDECLASTZ128rm, X86::VAESDECLASTrm },
  { X86::VAESDECLASTZ128rr, X86::VAESDECLASTrr },
  { X86::VAESDECZ128rm, X86::VAESDECrm },
  { X86::VAESDECZ128rr, X86::VAESDECrr },
  { X86::VAESENCLASTZ128rm, X86::VAESENCLASTrm },
  { X86::VAESENCLASTZ128rr, X86::VAESENCLASTrr },
  { X86::VAESENCZ128rm, X86::VAESENCrm },
  { X86::VAESENCZ128rr, X86::VAESENCrr },
  { X86::VALIGNDZ128rmi, X86::VPALIGNRrmi },
  { X86::VALIGNDZ128rri, X86::VPALIGNRrri },
  { X86::VALIGNQZ128rmi, X86::VPALIGNRrmi },
  { X86::VALIGNQZ128rri, X86::VPALIGNRrri },
  { X86::VANDNPDZ128rm, X86::VANDNPDrm },
  { X86::VANDNPDZ128rr, X86::VANDNPDrr },
  { X86::VANDNPSZ128rm, X86::VANDNPSrm },
  { X86::VANDNPSZ128rr, X86::VANDNPSrr },
  { X86::VANDPDZ128rm, X86::VANDPDrm },
  { X86::VANDPDZ128rr, X86::VANDPDrr },
  { X86::VANDPSZ128rm, X86::VANDPSrm },
  { X86::VANDPSZ128rr, X86::VANDPSrr },
  { X86::VBROADCASTI32X2Z128m, X86::VPBROADCASTQrm },
  { X86::VBROADCASTI32X2Z128r, X86::VPBROADCASTQrr },
  { X86::VBROADCASTSSZ128m, X86::VBROADCASTSSrm },
  { X86::VBROADCASTSSZ128r, X86::VBROADCASTSSrr },
  { X86::VCOMISDZrm, X86::VCOMISDrm },
  { X86::VCOMISDZrm_Int, X86::VCOMISDrm_Int },
  { X86::VCOMISDZrr, X86::VCOMISDrr },
  { X86::VCOMISDZrr_Int, X86::VCOMISDrr_Int },
  { X86::VCOMISSZrm, X86::VCOMISSrm },
  { X86::VCOMISSZrm_Int, X86::VCOMISSrm_Int },
  { X86::VCOMISSZrr, X86::VCOMISSrr },
  { X86::VCOMISSZrr_Int, X86::VCOMISSrr_Int },
  { X86::VCVTDQ2PDZ128rm, X86::VCVTDQ2PDrm },
  { X86::VCVTDQ2PDZ128rr, X86::VCVTDQ2PDrr },
  { X86::VCVTDQ2PSZ128rm, X86::VCVTDQ2PSrm },
  { X86::VCVTDQ2PSZ128rr, X86::VCVTDQ2PSrr },
  { X86::VCVTPD2DQZ128rm, X86::VCVTPD2DQrm },
  { X86::VCVTPD2DQZ128rr, X86::VCVTPD2DQrr },
  { X86::VCVTPD2PSZ128rm, X86::VCVTPD2PSrm },
  { X86::VCVTPD2PSZ128rr, X86::VCVTPD2PSrr },
  { X86::VCVTPH2PSZ128rm, X86::VCVTPH2PSrm },
  { X86::VCVTPH2PSZ128rr, X86::VCVTPH2PSrr },
  { X86::VCVTPS2DQZ128rm, X86::VCVTPS2DQrm },
  { X86::VCVTPS2DQZ128rr, X86::VCVTPS2DQrr },
  { X86::VCVTPS2PDZ128rm, X86::VCVTPS2PDrm },
  { X86::VCVTPS2PDZ128rr, X86::VCVTPS2PDrr },
  { X86::VCVTPS2PHZ128mr, X86::VCVTPS2PHmr },
  { X86::VCVTPS2PHZ128rr, X86::VCVTPS2PHrr },
  { X86::VCVTSD2SI64Zrm_Int, X86::VCVTSD2SI64rm_Int },
  { X86::VCVTSD2SI64Zrr_Int, X86::VCVTSD2SI64rr_Int },
  { X86::VCVTSD2SIZrm_Int, X86::VCVTSD2SIrm_Int },
  { X86::VCVTSD2SIZrr_Int, X86::VCVTSD2SIrr_Int },
  { X86::VCVTSD2SSZrm, X86::VCVTSD2SSrm },
  { X86::VCVTSD2SSZrm_Int, X86::VCVTSD2SSrm_Int },
  { X86::VCVTSD2SSZrr, X86::VCVTSD2SSrr },
  { X86::VCVTSD2SSZrr_Int, X86::VCVTSD2SSrr_Int },
  { X86::VCVTSI2SDZrm, X86::VCVTSI2SDrm },
  { X86::VCVTSI2SDZrm_Int, X86::VCVTSI2SDrm_Int },
  { X86::VCVTSI2SDZrr, X86::VCVTSI2SDrr },
  { X86::VCVTSI2SDZrr_Int, X86::VCVTSI2SDrr_Int },
  { X86::VCVTSI2SSZrm, X86::VCVTSI2SSrm },
  { X86::VCVTSI2SSZrm_Int, X86::VCVTSI2SSrm_Int },
  { X86::VCVTSI2SSZrr, X86::VCVTSI2SSrr },
  { X86::VCVTSI2SSZrr_Int, X86::VCVTSI2SSrr_Int },
  { X86::VCVTSI642SDZrm, X86::VCVTSI642SDrm },
  { X86::VCVTSI642SDZrm_Int, X86::VCVTSI642SDrm_Int },
  { X86::VCVTSI642SDZrr, X86::VCVTSI642SDrr },
  { X86::VCVTSI642SDZrr_Int, X86::VCVTSI642SDrr_Int },
  { X86::VCVTSI642SSZrm, X86::VCVTSI642SSrm },
  { X86::VCVTSI642SSZrm_Int, X86::VCVTSI642SSrm_Int },
  { X86::VCVTSI642SSZrr, X86::VCVTSI642SSrr },
  { X86::VCVTSI642SSZrr_Int, X86::VCVTSI642SSrr_Int },
  { X86::VCVTSS2SDZrm, X86::VCVTSS2SDrm },
  { X86::VCVTSS2SDZrm_Int, X86::VCVTSS2SDrm_Int },
  { X86::VCVTSS2SDZrr, X86::VCVTSS2SDrr },
  { X86::VCVTSS2SDZrr_Int, X86::VCVTSS2SDrr_Int },
  { X86::VCVTSS2SI64Zrm_Int, X86::VCVTSS2SI64rm_Int },
  { X86::VCVTSS2SI64Zrr_Int, X86::VCVTSS2SI64rr_Int },
  { X86::VCVTSS2SIZrm_Int, X86::VCVTSS2SIrm_Int },
  { X86::VCVTSS2SIZrr_Int, X86::VCVTSS2SIrr_Int },
  { X86::VCVTTPD2DQZ128rm, X86::VCVTTPD2DQrm },
  { X86::VCVTTPD2DQZ128rr, X86::VCVTTPD2DQrr },
  { X86::VCVTTPS2DQZ128rm, X86::VCVTTPS2DQrm },
  { X86::VCVTTPS2DQZ128rr, X86::VCVTTPS2DQrr },
  { X86::VCVTTSD2SI64Zrm, X86::VCVTTSD2SI64rm },
  { X86::VCVTTSD2SI64Zrm_Int, X86::VCVTTSD2SI64rm_Int },
  { X86::VCVTTSD2SI64Zrr, X86::VCVTTSD2SI64rr },
  { X86::VCVTTSD2SI64Zrr_Int, X86::VCVTTSD2SI64rr_Int },
  { X86::VCVTTSD2SIZrm, X86::VCVTTSD2SIrm },
  { X86::VCVTTSD2SIZrm_Int, X86::VCVTTSD2SIrm_Int },
  { X86::VCVTTSD2SIZrr, X86::VCVTTSD2SIrr },
  { X86::VCVTTSD2SIZrr_Int, X86::VCVTTSD2SIrr_Int },
  { X86::VCVTTSS2SI64Zrm, X86::VCVTTSS2SI64rm },
  { X86::VCVTTSS2SI64Zrm_Int, X86::VCVTTSS2SI64rm_Int },
  { X86::VCVTTSS2SI64Zrr, X86::VCVTTSS2SI64rr },
  { X86::VCVTTSS2SI64Zrr_Int, X86::VCVTTSS2SI64rr_Int },
  { X86::VCVTTSS2SIZrm, X86::VCVTTSS2SIrm },
  { X86::VCVTTSS2SIZrm_Int, X86::VCVTTSS2SIrm_Int },
  { X86::VCVTTSS2SIZrr, X86::VCVTTSS2SIrr },
  { X86::VCVTTSS2SIZrr_Int, X86::VCVTTSS2SIrr_Int },
  { X86::VDIVPDZ128rm, X86::VDIVPDrm },
  { X86::VDIVPDZ128rr, X86::VDIVPDrr },
  { X86::VDIVPSZ128rm, X86::VDIVPSrm },
  { X86::VDIVPSZ128rr, X86::VDIVPSrr },
  { X86::VDIVSDZrm, X86::VDIVSDrm },
  { X86::VDIVSDZrm_Int, X86::VDIVSDrm_Int },
  { X86::VDIVSDZrr, X86::VDIVSDrr },
  { X86::VDIVSDZrr_Int, X86::VDIVSDrr_Int },
  { X86::VDIVSSZrm, X86::VDIVSSrm },
  { X86::VDIVSSZrm_Int, X86::VDIVSSrm_Int },
  { X86::VDIVSSZrr, X86::VDIVSSrr },
  { X86::VDIVSSZrr_Int, X86::VDIVSSrr_Int },
  { X86::VEXTRACTPSZmr, X86::VEXTRACTPSmr },
  { X86::VEXTRACTPSZrr, X86::VEXTRACTPSrr },
  { X86::VFMADD132PDZ128m, X86::VFMADD132PDm },
  { X86::VFMADD132PDZ128r, X86::VFMADD132PDr },
  { X86::VFMADD132PSZ128m, X86::VFMADD132PSm },
  { X86::VFMADD132PSZ128r, X86::VFMADD132PSr },
  { X86::VFMADD132SDZm, X86::VFMADD132SDm },
  { X86::VFMADD132SDZm_Int, X86::VFMADD132SDm_Int },
  { X86::VFMADD132SDZr, X86::VFMADD132SDr },
  { X86::VFMADD132SDZr_Int, X86::VFMADD132SDr_Int },
  { X86::VFMADD132SSZm, X86::VFMADD132SSm },
  { X86::VFMADD132SSZm_Int, X86::VFMADD132SSm_Int },
  { X86::VFMADD132SSZr, X86::VFMADD132SSr },
  { X86::VFMADD132SSZr_Int, X86::VFMADD132SSr_Int },
  { X86::VFMADD213PDZ128m, X86::VFMADD213PDm },
  { X86::VFMADD213PDZ128r, X86::VFMADD213PDr },
  { X86::VFMADD213PSZ128m, X86::VFMADD213PSm },
  { X86::VFMADD213PSZ128r, X86::VFMADD213PSr },
  { X86::VFMADD213SDZm, X86::VFMADD213SDm },
  { X86::VFMADD213SDZm_Int, X86::VFMADD213SDm_Int },
  { X86::VFMADD213SDZr, X86::VFMADD213SDr },
  { X86::VFMADD213SDZr_Int, X86::VFMADD213SDr_Int },
  { X86::VFMADD213SSZm, X86::VFMADD213SSm },
  { X86::VFMADD213SSZm_Int, X86::VFMADD213SSm_Int },
  { X86::VFMADD213SSZr, X86::VFMADD213SSr },
  { X86::VFMADD213SSZr_Int, X86::VFMADD213SSr_Int },
  { X86::VFMADD231PDZ128m, X86::VFMADD231PDm },
  { X86::VFMADD231PDZ128r, X86::VFMADD231PDr },
  { X86::VFMADD231PSZ128m, X86::VFMADD231PSm },
  { X86::VFMADD231PSZ128r, X86::VFMADD231PSr },
  { X86::VFMADD231SDZm, X86::VFMADD231SDm },
  { X86::VFMADD231SDZm_Int, X86::VFMADD231SDm_Int },
  { X86::VFMADD231SDZr, X86::VFMADD231SDr },
  { X86::VFMADD231SDZr_Int, X86::VFMADD231SDr_Int },
  { X86::VFMADD231SSZm, X86::VFMADD231SSm },
  { X86::VFMADD231SSZm_Int, X86::VFMADD231SSm_Int },
  { X86::VFMADD231SSZr, X86::VFMADD231SSr },
  { X86::VFMADD231SSZr_Int, X86::VFMADD231SSr_Int },
  { X86::VFMADDSUB132PDZ128m, X86::VFMADDSUB132PDm },
  { X86::VFMADDSUB132PDZ128r, X86::VFMADDSUB132PDr },
  { X86::VFMADDSUB132PSZ128m, X86::VFMADDSUB132PSm },
  { X86::VFMADDSUB132PSZ128r, X86::VFMADDSUB132PSr },
  { X86::VFMADDSUB213PDZ128m, X86::VFMADDSUB213PDm },
  { X86::VFMADDSUB213PDZ128r, X86::VFMADDSUB213PDr },
  { X86::VFMADDSUB213PSZ128m, X86::VFMADDSUB213PSm },
  { X86::VFMADDSUB213PSZ128r, X86::VFMADDSUB213PSr },
  { X86::VFMADDSUB231PDZ128m, X86::VFMADDSUB231PDm },
  { X86::VFMADDSUB231PDZ128r, X86::VFMADDSUB231PDr },
  { X86::VFMADDSUB231PSZ128m, X86::VFMADDSUB231PSm },
  { X86::VFMADDSUB231PSZ128r, X86::VFMADDSUB231PSr },
  { X86::VFMSUB132PDZ128m, X86::VFMSUB132PDm },
  { X86::VFMSUB132PDZ128r, X86::VFMSUB132PDr },
  { X86::VFMSUB132PSZ128m, X86::VFMSUB132PSm },
  { X86::VFMSUB132PSZ128r, X86::VFMSUB132PSr },
  { X86::VFMSUB132SDZm, X86::VFMSUB132SDm },
  { X86::VFMSUB132SDZm_Int, X86::VFMSUB132SDm_Int },
  { X86::VFMSUB132SDZr, X86::VFMSUB132SDr },
  { X86::VFMSUB132SDZr_Int, X86::VFMSUB132SDr_Int },
  { X86::VFMSUB132SSZm, X86::VFMSUB132SSm },
  { X86::VFMSUB132SSZm_Int, X86::VFMSUB132SSm_Int },
  { X86::VFMSUB132SSZr, X86::VFMSUB132SSr },
  { X86::VFMSUB132SSZr_Int, X86::VFMSUB132SSr_Int },
  { X86::VFMSUB213PDZ128m, X86::VFMSUB213PDm },
  { X86::VFMSUB213PDZ128r, X86::VFMSUB213PDr },
  { X86::VFMSUB213PSZ128m, X86::VFMSUB213PSm },
  { X86::VFMSUB213PSZ128r, X86::VFMSUB213PSr },
  { X86::VFMSUB213SDZm, X86::VFMSUB213SDm },
  { X86::VFMSUB213SDZm_Int, X86::VFMSUB213SDm_Int },
  { X86::VFMSUB213SDZr, X86::VFMSUB213SDr },
  { X86::VFMSUB213SDZr_Int, X86::VFMSUB213SDr_Int },
  { X86::VFMSUB213SSZm, X86::VFMSUB213SSm },
  { X86::VFMSUB213SSZm_Int, X86::VFMSUB213SSm_Int },
  { X86::VFMSUB213SSZr, X86::VFMSUB213SSr },
  { X86::VFMSUB213SSZr_Int, X86::VFMSUB213SSr_Int },
  { X86::VFMSUB231PDZ128m, X86::VFMSUB231PDm },
  { X86::VFMSUB231PDZ128r, X86::VFMSUB231PDr },
  { X86::VFMSUB231PSZ128m, X86::VFMSUB231PSm },
  { X86::VFMSUB231PSZ128r, X86::VFMSUB231PSr },
  { X86::VFMSUB231SDZm, X86::VFMSUB231SDm },
  { X86::VFMSUB231SDZm_Int, X86::VFMSUB231SDm_Int },
  { X86::VFMSUB231SDZr, X86::VFMSUB231SDr },
  { X86::VFMSUB231SDZr_Int, X86::VFMSUB231SDr_Int },
  { X86::VFMSUB231SSZm, X86::VFMSUB231SSm },
  { X86::VFMSUB231SSZm_Int, X86::VFMSUB231SSm_Int },
  { X86::VFMSUB231SSZr, X86::VFMSUB231SSr },
  { X86::VFMSUB231SSZr_Int, X86::VFMSUB231SSr_Int },
  { X86::VFMSUBADD132PDZ128m, X86::VFMSUBADD132PDm },
  { X86::VFMSUBADD132PDZ128r, X86::VFMSUBADD132PDr },
  { X86::VFMSUBADD132PSZ128m, X86::VFMSUBADD132PSm },
  { X86::VFMSUBADD132PSZ128r, X86::VFMSUBADD132PSr },
  { X86::VFMSUBADD213PDZ128m, X86::VFMSUBADD213PDm },
  { X86::VFMSUBADD213PDZ128r, X86::VFMSUBADD213PDr },
  { X86::VFMSUBADD213PSZ128m, X86::VFMSUBADD213PSm },
  { X86::VFMSUBADD213PSZ128r, X86::VFMSUBADD213PSr },
  { X86::VFMSUBADD231PDZ128m, X86::VFMSUBADD231PDm },
  { X86::VFMSUBADD231PDZ128r, X86::VFMSUBADD231PDr },
  { X86::VFMSUBADD231PSZ128m, X86::VFMSUBADD231PSm },
  { X86::VFMSUBADD231PSZ128r, X86::VFMSUBADD231PSr },
  { X86::VFNMADD132PDZ128m, X86::VFNMADD132PDm },
  { X86::VFNMADD132PDZ128r, X86::VFNMADD132PDr },
  { X86::VFNMADD132PSZ128m, X86::VFNMADD132PSm },
  { X86::VFNMADD132PSZ128r, X86::VFNMADD132PSr },
  { X86::VFNMADD132SDZm, X86::VFNMADD132SDm },
  { X86::VFNMADD132SDZm_Int, X86::VFNMADD132SDm_Int },
  { X86::VFNMADD132SDZr, X86::VFNMADD132SDr },
  { X86::VFNMADD132SDZr_Int, X86::VFNMADD132SDr_Int },
  { X86::VFNMADD132SSZm, X86::VFNMADD132SSm },
  { X86::VFNMADD132SSZm_Int, X86::VFNMADD132SSm_Int },
  { X86::VFNMADD132SSZr, X86::VFNMADD132SSr },
  { X86::VFNMADD132SSZr_Int, X86::VFNMADD132SSr_Int },
  { X86::VFNMADD213PDZ128m, X86::VFNMADD213PDm },
  { X86::VFNMADD213PDZ128r, X86::VFNMADD213PDr },
  { X86::VFNMADD213PSZ128m, X86::VFNMADD213PSm },
  { X86::VFNMADD213PSZ128r, X86::VFNMADD213PSr },
  { X86::VFNMADD213SDZm, X86::VFNMADD213SDm },
  { X86::VFNMADD213SDZm_Int, X86::VFNMADD213SDm_Int },
  { X86::VFNMADD213SDZr, X86::VFNMADD213SDr },
  { X86::VFNMADD213SDZr_Int, X86::VFNMADD213SDr_Int },
  { X86::VFNMADD213SSZm, X86::VFNMADD213SSm },
  { X86::VFNMADD213SSZm_Int, X86::VFNMADD213SSm_Int },
  { X86::VFNMADD213SSZr, X86::VFNMADD213SSr },
  { X86::VFNMADD213SSZr_Int, X86::VFNMADD213SSr_Int },
  { X86::VFNMADD231PDZ128m, X86::VFNMADD231PDm },
  { X86::VFNMADD231PDZ128r, X86::VFNMADD231PDr },
  { X86::VFNMADD231PSZ128m, X86::VFNMADD231PSm },
  { X86::VFNMADD231PSZ128r, X86::VFNMADD231PSr },
  { X86::VFNMADD231SDZm, X86::VFNMADD231SDm },
  { X86::VFNMADD231SDZm_Int, X86::VFNMADD231SDm_Int },
  { X86::VFNMADD231SDZr, X86::VFNMADD231SDr },
  { X86::VFNMADD231SDZr_Int, X86::VFNMADD231SDr_Int },
  { X86::VFNMADD231SSZm, X86::VFNMADD231SSm },
  { X86::VFNMADD231SSZm_Int, X86::VFNMADD231SSm_Int },
  { X86::VFNMADD231SSZr, X86::VFNMADD231SSr },
  { X86::VFNMADD231SSZr_Int, X86::VFNMADD231SSr_Int },
  { X86::VFNMSUB132PDZ128m, X86::VFNMSUB132PDm },
  { X86::VFNMSUB132PDZ128r, X86::VFNMSUB132PDr },
  { X86::VFNMSUB132PSZ128m, X86::VFNMSUB132PSm },
  { X86::VFNMSUB132PSZ128r, X86::VFNMSUB132PSr },
  { X86::VFNMSUB132SDZm, X86::VFNMSUB132SDm },
  { X86::VFNMSUB132SDZm_Int, X86::VFNMSUB132SDm_Int },
  { X86::VFNMSUB132SDZr, X86::VFNMSUB132SDr },
  { X86::VFNMSUB132SDZr_Int, X86::VFNMSUB132SDr_Int },
  { X86::VFNMSUB132SSZm, X86::VFNMSUB132SSm },
  { X86::VFNMSUB132SSZm_Int, X86::VFNMSUB132SSm_Int },
  { X86::VFNMSUB132SSZr, X86::VFNMSUB132SSr },
  { X86::VFNMSUB132SSZr_Int, X86::VFNMSUB132SSr_Int },
  { X86::VFNMSUB213PDZ128m, X86::VFNMSUB213PDm },
  { X86::VFNMSUB213PDZ128r, X86::VFNMSUB213PDr },
  { X86::VFNMSUB213PSZ128m, X86::VFNMSUB213PSm },
  { X86::VFNMSUB213PSZ128r, X86::VFNMSUB213PSr },
  { X86::VFNMSUB213SDZm, X86::VFNMSUB213SDm },
  { X86::VFNMSUB213SDZm_Int, X86::VFNMSUB213SDm_Int },
  { X86::VFNMSUB213SDZr, X86::VFNMSUB213SDr },
  { X86::VFNMSUB213SDZr_Int, X86::VFNMSUB213SDr_Int },
  { X86::VFNMSUB213SSZm, X86::VFNMSUB213SSm },
  { X86::VFNMSUB213SSZm_Int, X86::VFNMSUB213SSm_Int },
  { X86::VFNMSUB213SSZr, X86::VFNMSUB213SSr },
  { X86::VFNMSUB213SSZr_Int, X86::VFNMSUB213SSr_Int },
  { X86::VFNMSUB231PDZ128m, X86::VFNMSUB231PDm },
  { X86::VFNMSUB231PDZ128r, X86::VFNMSUB231PDr },
  { X86::VFNMSUB231PSZ128m, X86::VFNMSUB231PSm },
  { X86::VFNMSUB231PSZ128r, X86::VFNMSUB231PSr },
  { X86::VFNMSUB231SDZm, X86::VFNMSUB231SDm },
  { X86::VFNMSUB231SDZm_Int, X86::VFNMSUB231SDm_Int },
  { X86::VFNMSUB231SDZr, X86::VFNMSUB231SDr },
  { X86::VFNMSUB231SDZr_Int, X86::VFNMSUB231SDr_Int },
  { X86::VFNMSUB231SSZm, X86::VFNMSUB231SSm },
  { X86::VFNMSUB231SSZm_Int, X86::VFNMSUB231SSm_Int },
  { X86::VFNMSUB231SSZr, X86::VFNMSUB231SSr },
  { X86::VFNMSUB231SSZr_Int, X86::VFNMSUB231SSr_Int },
  { X86::VGF2P8AFFINEINVQBZ128rmi, X86::VGF2P8AFFINEINVQBrmi },
  { X86::VGF2P8AFFINEINVQBZ128rri, X86::VGF2P8AFFINEINVQBrri },
  { X86::VGF2P8AFFINEQBZ128rmi, X86::VGF2P8AFFINEQBrmi },
  { X86::VGF2P8AFFINEQBZ128rri, X86::VGF2P8AFFINEQBrri },
  { X86::VGF2P8MULBZ128rm, X86::VGF2P8MULBrm },
  { X86::VGF2P8MULBZ128rr, X86::VGF2P8MULBrr },
  { X86::VINSERTPSZrm, X86::VINSERTPSrm },
  { X86::VINSERTPSZrr, X86::VINSERTPSrr },
  { X86::VMAXCPDZ128rm, X86::VMAXCPDrm },
  { X86::VMAXCPDZ128rr, X86::VMAXCPDrr },
  { X86::VMAXCPSZ128rm, X86::VMAXCPSrm },
  { X86::VMAXCPSZ128rr, X86::VMAXCPSrr },
  { X86::VMAXCSDZrm, X86::VMAXCSDrm },
  { X86::VMAXCSDZrr, X86::VMAXCSDrr },
  { X86::VMAXCSSZrm, X86::VMAXCSSrm },
  { X86::VMAXCSSZrr, X86::VMAXCSSrr },
  { X86::VMAXPDZ128rm, X86::VMAXCPDrm },
  { X86::VMAXPDZ128rr, X86::VMAXCPDrr },
  { X86::VMAXPSZ128rm, X86::VMAXCPSrm },
  { X86::VMAXPSZ128rr, X86::VMAXCPSrr },
  { X86::VMAXSDZrm, X86::VMAXCSDrm },
  { X86::VMAXSDZrm_Int, X86::VMAXSDrm_Int },
  { X86::VMAXSDZrr, X86::VMAXCSDrr },
  { X86::VMAXSDZrr_Int, X86::VMAXSDrr_Int },
  { X86::VMAXSSZrm, X86::VMAXCSSrm },
  { X86::VMAXSSZrm_Int, X86::VMAXSSrm_Int },
  { X86::VMAXSSZrr, X86::VMAXCSSrr },
  { X86::VMAXSSZrr_Int, X86::VMAXSSrr_Int },
  { X86::VMINCPDZ128rm, X86::VMINCPDrm },
  { X86::VMINCPDZ128rr, X86::VMINCPDrr },
  { X86::VMINCPSZ128rm, X86::VMINCPSrm },
  { X86::VMINCPSZ128rr, X86::VMINCPSrr },
  { X86::VMINCSDZrm, X86::VMINCSDrm },
  { X86::VMINCSDZrr, X86::VMINCSDrr },
  { X86::VMINCSSZrm, X86::VMINCSSrm },
  { X86::VMINCSSZrr, X86::VMINCSSrr },
  { X86::VMINPDZ128rm, X86::VMINCPDrm },
  { X86::VMINPDZ128rr, X86::VMINCPDrr },
  { X86::VMINPSZ128rm, X86::VMINCPSrm },
  { X86::VMINPSZ128rr, X86::VMINCPSrr },
  { X86::VMINSDZrm, X86::VMINCSDrm },
  { X86::VMINSDZrm_Int, X86::VMINSDrm_Int },
  { X86::VMINSDZrr, X86::VMINCSDrr },
  { X86::VMINSDZrr_Int, X86::VMINSDrr_Int },
  { X86::VMINSSZrm, X86::VMINCSSrm },
  { X86::VMINSSZrm_Int, X86::VMINSSrm_Int },
  { X86::VMINSSZrr, X86::VMINCSSrr },
  { X86::VMINSSZrr_Int, X86::VMINSSrr_Int },
  { X86::VMOV64toPQIZrm, X86::VMOV64toPQIrm },
  { X86::VMOV64toPQIZrr, X86::VMOV64toPQIrr },
  { X86::VMOV64toSDZrr, X86::VMOV64toSDrr },
  { X86::VMOVAPDZ128mr, X86::VMOVAPDmr },
  { X86::VMOVAPDZ128rm, X86::VMOVAPDrm },
  { X86::VMOVAPDZ128rr, X86::VMOVAPDrr },
  { X86::VMOVAPDZ128rr_REV, X86::VMOVAPDrr_REV },
  { X86::VMOVAPSZ128mr, X86::VMOVAPSmr },
  { X86::VMOVAPSZ128rm, X86::VMOVAPSrm },
  { X86::VMOVAPSZ128rr, X86::VMOVAPSrr },
  { X86::VMOVAPSZ128rr_REV, X86::VMOVAPSrr_REV },
  { X86::VMOVDDUPZ128rm, X86::VMOVDDUPrm },
  { X86::VMOVDDUPZ128rr, X86::VMOVDDUPrr },
  { X86::VMOVDI2PDIZrm, X86::VMOVDI2PDIrm },
  { X86::VMOVDI2PDIZrr, X86::VMOVDI2PDIrr },
  { X86::VMOVDI2SSZrm, X86::VMOVDI2SSrm },
  { X86::VMOVDI2SSZrr, X86::VMOVDI2SSrr },
  { X86::VMOVDQA32Z128mr, X86::VMOVDQAmr },
  { X86::VMOVDQA32Z128rm, X86::VMOVDQArm },
  { X86::VMOVDQA32Z128rr, X86::VMOVDQArr },
  { X86::VMOVDQA32Z128rr_REV, X86::VMOVDQArr_REV },
  { X86::VMOVDQA64Z128mr, X86::VMOVDQAmr },
  { X86::VMOVDQA64Z128rm, X86::VMOVDQArm },
  { X86::VMOVDQA64Z128rr, X86::VMOVDQArr },
  { X86::VMOVDQA64Z128rr_REV, X86::VMOVDQArr_REV },
  { X86::VMOVDQU16Z128mr, X86::VMOVDQUmr },
  { X86::VMOVDQU16Z128rm, X86::VMOVDQUrm },
  { X86::VMOVDQU16Z128rr, X86::VMOVDQUrr },
  { X86::VMOVDQU16Z128rr_REV, X86::VMOVDQUrr_REV },
  { X86::VMOVDQU32Z128mr, X86::VMOVDQUmr },
  { X86::VMOVDQU32Z128rm, X86::VMOVDQUrm },
  { X86::VMOVDQU32Z128rr, X86::VMOVDQUrr },
  { X86::VMOVDQU32Z128rr_REV, X86::VMOVDQUrr_REV },
  { X86::VMOVDQU64Z128mr, X86::VMOVDQUmr },
  { X86::VMOVDQU64Z128rm, X86::VMOVDQUrm },
  { X86::VMOVDQU64Z128rr, X86::VMOVDQUrr },
  { X86::VMOVDQU64Z128rr_REV, X86::VMOVDQUrr_REV },
  { X86::VMOVDQU8Z128mr, X86::VMOVDQUmr },
  { X86::VMOVDQU8Z128rm, X86::VMOVDQUrm },
  { X86::VMOVDQU8Z128rr, X86::VMOVDQUrr },
  { X86::VMOVDQU8Z128rr_REV, X86::VMOVDQUrr_REV },
  { X86::VMOVHLPSZrr, X86::VMOVHLPSrr },
  { X86::VMOVHPDZ128mr, X86::VMOVHPDmr },
  { X86::VMOVHPDZ128rm, X86::VMOVHPDrm },
  { X86::VMOVHPSZ128mr, X86::VMOVHPSmr },
  { X86::VMOVHPSZ128rm, X86::VMOVHPSrm },
  { X86::VMOVLHPSZrr, X86::VMOVLHPSrr },
  { X86::VMOVLPDZ128mr, X86::VMOVLPDmr },
  { X86::VMOVLPDZ128rm, X86::VMOVLPDrm },
  { X86::VMOVLPSZ128mr, X86::VMOVLPSmr },
  { X86::VMOVLPSZ128rm, X86::VMOVLPSrm },
  { X86::VMOVNTDQAZ128rm, X86::VMOVNTDQArm },
  { X86::VMOVNTDQZ128mr, X86::VMOVNTDQmr },
  { X86::VMOVNTPDZ128mr, X86::VMOVNTPDmr },
  { X86::VMOVNTPSZ128mr, X86::VMOVNTPSmr },
  { X86::VMOVPDI2DIZmr, X86::VMOVPDI2DImr },
  { X86::VMOVPDI2DIZrr, X86::VMOVPDI2DIrr },
  { X86::VMOVPQI2QIZmr, X86::VMOVPQI2QImr },
  { X86::VMOVPQI2QIZrr, X86::VMOVPQI2QIrr },
  { X86::VMOVPQIto64Zmr, X86::VMOVPQIto64mr },
  { X86::VMOVPQIto64Zrr, X86::VMOVPQIto64rr },
  { X86::VMOVQI2PQIZrm, X86::VMOVQI2PQIrm },
  { X86::VMOVSDZmr, X86::VMOVSDmr },
  { X86::VMOVSDZrm, X86::VMOVSDrm },
  { X86::VMOVSDZrr, X86::VMOVSDrr },
  { X86::VMOVSDZrr_REV, X86::VMOVSDrr_REV },
  { X86::VMOVSDto64Zmr, X86::VMOVSDto64mr },
  { X86::VMOVSDto64Zrr, X86::VMOVSDto64rr },
  { X86::VMOVSHDUPZ128rm, X86::VMOVSHDUPrm },
  { X86::VMOVSHDUPZ128rr, X86::VMOVSHDUPrr },
  { X86::VMOVSLDUPZ128rm, X86::VMOVSLDUPrm },
  { X86::VMOVSLDUPZ128rr, X86::VMOVSLDUPrr },
  { X86::VMOVSS2DIZmr, X86::VMOVSS2DImr },
  { X86::VMOVSS2DIZrr, X86::VMOVSS2DIrr },
  { X86::VMOVSSZmr, X86::VMOVSSmr },
  { X86::VMOVSSZrm, X86::VMOVSSrm },
  { X86::VMOVSSZrr, X86::VMOVSSrr },
  { X86::VMOVSSZrr_REV, X86::VMOVSSrr_REV },
  { X86::VMOVUPDZ128mr, X86::VMOVUPDmr },
  { X86::VMOVUPDZ128rm, X86::VMOVUPDrm },
  { X86::VMOVUPDZ128rr, X86::VMOVUPDrr },
  { X86::VMOVUPDZ128rr_REV, X86::VMOVUPDrr_REV },
  { X86::VMOVUPSZ128mr, X86::VMOVUPSmr },
  { X86::VMOVUPSZ128rm, X86::VMOVUPSrm },
  { X86::VMOVUPSZ128rr, X86::VMOVUPSrr },
  { X86::VMOVUPSZ128rr_REV, X86::VMOVUPSrr_REV },
  { X86::VMOVZPQILo2PQIZrr, X86::VMOVZPQILo2PQIrr },
  { X86::VMULPDZ128rm, X86::VMULPDrm },
  { X86::VMULPDZ128rr, X86::VMULPDrr },
  { X86::VMULPSZ128rm, X86::VMULPSrm },
  { X86::VMULPSZ128rr, X86::VMULPSrr },
  { X86::VMULSDZrm, X86::VMULSDrm },
  { X86::VMULSDZrm_Int, X86::VMULSDrm_Int },
  { X86::VMULSDZrr, X86::VMULSDrr },
  { X86::VMULSDZrr_Int, X86::VMULSDrr_Int },
  { X86::VMULSSZrm, X86::VMULSSrm },
  { X86::VMULSSZrm_Int, X86::VMULSSrm_Int },
  { X86::VMULSSZrr, X86::VMULSSrr },
  { X86::VMULSSZrr_Int, X86::VMULSSrr_Int },
  { X86::VORPDZ128rm, X86::VORPDrm },
  { X86::VORPDZ128rr, X86::VORPDrr },
  { X86::VORPSZ128rm, X86::VORPSrm },
  { X86::VORPSZ128rr, X86::VORPSrr },
  { X86::VPABSBZ128rm, X86::VPABSBrm },
  { X86::VPABSBZ128rr, X86::VPABSBrr },
  { X86::VPABSDZ128rm, X86::VPABSDrm },
  { X86::VPABSDZ128rr, X86::VPABSDrr },
  { X86::VPABSWZ128rm, X86::VPABSWrm },
  { X86::VPABSWZ128rr, X86::VPABSWrr },
  { X86::VPACKSSDWZ128rm, X86::VPACKSSDWrm },
  { X86::VPACKSSDWZ128rr, X86::VPACKSSDWrr },
  { X86::VPACKSSWBZ128rm, X86::VPACKSSWBrm },
  { X86::VPACKSSWBZ128rr, X86::VPACKSSWBrr },
  { X86::VPACKUSDWZ128rm, X86::VPACKUSDWrm },
  { X86::VPACKUSDWZ128rr, X86::VPACKUSDWrr },
  { X86::VPACKUSWBZ128rm, X86::VPACKUSWBrm },
  { X86::VPACKUSWBZ128rr, X86::VPACKUSWBrr },
  { X86::VPADDBZ128rm, X86::VPADDBrm },
  { X86::VPADDBZ128rr, X86::VPADDBrr },
  { X86::VPADDDZ128rm, X86::VPADDDrm },
  { X86::VPADDDZ128rr, X86::VPADDDrr },
  { X86::VPADDQZ128rm, X86::VPADDQrm },
  { X86::VPADDQZ128rr, X86::VPADDQrr },
  { X86::VPADDSBZ128rm, X86::VPADDSBrm },
  { X86::VPADDSBZ128rr, X86::VPADDSBrr },
  { X86::VPADDSWZ128rm, X86::VPADDSWrm },
  { X86::VPADDSWZ128rr, X86::VPADDSWrr },
  { X86::VPADDUSBZ128rm, X86::VPADDUSBrm },
  { X86::VPADDUSBZ128rr, X86::VPADDUSBrr },
  { X86::VPADDUSWZ128rm, X86::VPADDUSWrm },
  { X86::VPADDUSWZ128rr, X86::VPADDUSWrr },
  { X86::VPADDWZ128rm, X86::VPADDWrm },
  { X86::VPADDWZ128rr, X86::VPADDWrr },
  { X86::VPALIGNRZ128rmi, X86::VPALIGNRrmi },
  { X86::VPALIGNRZ128rri, X86::VPALIGNRrri },
  { X86::VPANDDZ128rm, X86::VPANDrm },
  { X86::VPANDDZ128rr, X86::VPANDrr },
  { X86::VPANDNDZ128rm, X86::VPANDNrm },
  { X86::VPANDNDZ128rr, X86::VPANDNrr },
  { X86::VPANDNQZ128rm, X86::VPANDNrm },
  { X86::VPANDNQZ128rr, X86::VPANDNrr },
  { X86::VPANDQZ128rm, X86::VPANDrm },
  { X86::VPANDQZ128rr, X86::VPANDrr },
  { X86::VPAVGBZ128rm, X86::VPAVGBrm },
  { X86::VPAVGBZ128rr, X86::VPAVGBrr },
  { X86::VPAVGWZ128rm, X86::VPAVGWrm },
  { X86::VPAVGWZ128rr, X86::VPAVGWrr },
  { X86::VPBROADCASTBZ128m, X86::VPBROADCASTBrm },
  { X86::VPBROADCASTBZ128r, X86::VPBROADCASTBrr },
  { X86::VPBROADCASTDZ128m, X86::VPBROADCASTDrm },
  { X86::VPBROADCASTDZ128r, X86::VPBROADCASTDrr },
  { X86::VPBROADCASTQZ128m, X86::VPBROADCASTQrm },
  { X86::VPBROADCASTQZ128r, X86::VPBROADCASTQrr },
  { X86::VPBROADCASTWZ128m, X86::VPBROADCASTWrm },
  { X86::VPBROADCASTWZ128r, X86::VPBROADCASTWrr },
  { X86::VPCLMULQDQZ128rm, X86::VPCLMULQDQrm },
  { X86::VPCLMULQDQZ128rr, X86::VPCLMULQDQrr },
  { X86::VPERMILPDZ128mi, X86::VPERMILPDmi },
  { X86::VPERMILPDZ128ri, X86::VPERMILPDri },
  { X86::VPERMILPDZ128rm, X86::VPERMILPDrm },
  { X86::VPERMILPDZ128rr, X86::VPERMILPDrr },
  { X86::VPERMILPSZ128mi, X86::VPERMILPSmi },
  { X86::VPERMILPSZ128ri, X86::VPERMILPSri },
  { X86::VPERMILPSZ128rm, X86::VPERMILPSrm },
  { X86::VPERMILPSZ128rr, X86::VPERMILPSrr },
  { X86::VPEXTRBZmr, X86::VPEXTRBmr },
  { X86::VPEXTRBZrr, X86::VPEXTRBrr },
  { X86::VPEXTRDZmr, X86::VPEXTRDmr },
  { X86::VPEXTRDZrr, X86::VPEXTRDrr },
  { X86::VPEXTRQZmr, X86::VPEXTRQmr },
  { X86::VPEXTRQZrr, X86::VPEXTRQrr },
  { X86::VPEXTRWZmr, X86::VPEXTRWmr },
  { X86::VPEXTRWZrr, X86::VPEXTRWrr },
  { X86::VPEXTRWZrr_REV, X86::VPEXTRWrr_REV },
  { X86::VPINSRBZrm, X86::VPINSRBrm },
  { X86::VPINSRBZrr, X86::VPINSRBrr },
  { X86::VPINSRDZrm, X86::VPINSRDrm },
  { X86::VPINSRDZrr, X86::VPINSRDrr },
  { X86::VPINSRQZrm, X86::VPINSRQrm },
  { X86::VPINSRQZrr, X86::VPINSRQrr },
  { X86::VPINSRWZrm, X86::VPINSRWrm },
  { X86::VPINSRWZrr, X86::VPINSRWrr },
  { X86::VPMADDUBSWZ128rm, X86::VPMADDUBSWrm },
  { X86::VPMADDUBSWZ128rr, X86::VPMADDUBSWrr },
  { X86::VPMADDWDZ128rm, X86::VPMADDWDrm },
  { X86::VPMADDWDZ128rr, X86::VPMADDWDrr },
  { X86::VPMAXSBZ128rm, X86::VPMAXSBrm },
  { X86::VPMAXSBZ128rr, X86::VPMAXSBrr },
  { X86::VPMAXSDZ128rm, X86::VPMAXSDrm },
  { X86::VPMAXSDZ128rr, X86::VPMAXSDrr },
  { X86::VPMAXSWZ128rm, X86::VPMAXSWrm },
  { X86::VPMAXSWZ128rr, X86::VPMAXSWrr },
  { X86::VPMAXUBZ128rm, X86::VPMAXUBrm },
  { X86::VPMAXUBZ128rr, X86::VPMAXUBrr },
  { X86::VPMAXUDZ128rm, X86::VPMAXUDrm },
  { X86::VPMAXUDZ128rr, X86::VPMAXUDrr },
  { X86::VPMAXUWZ128rm, X86::VPMAXUWrm },
  { X86::VPMAXUWZ128rr, X86::VPMAXUWrr },
  { X86::VPMINSBZ128rm, X86::VPMINSBrm },
  { X86::VPMINSBZ128rr, X86::VPMINSBrr },
  { X86::VPMINSDZ128rm, X86::VPMINSDrm },
  { X86::VPMINSDZ128rr, X86::VPMINSDrr },
  { X86::VPMINSWZ128rm, X86::VPMINSWrm },
  { X86::VPMINSWZ128rr, X86::VPMINSWrr },
  { X86::VPMINUBZ128rm, X86::VPMINUBrm },
  { X86::VPMINUBZ128rr, X86::VPMINUBrr },
  { X86::VPMINUDZ128rm, X86::VPMINUDrm },
  { X86::VPMINUDZ128rr, X86::VPMINUDrr },
  { X86::VPMINUWZ128rm, X86::VPMINUWrm },
  { X86::VPMINUWZ128rr, X86::VPMINUWrr },
  { X86::VPMOVSXBDZ128rm, X86::VPMOVSXBDrm },
  { X86::VPMOVSXBDZ128rr, X86::VPMOVSXBDrr },
  { X86::VPMOVSXBQZ128rm, X86::VPMOVSXBQrm },
  { X86::VPMOVSXBQZ128rr, X86::VPMOVSXBQrr },
  { X86::VPMOVSXBWZ128rm, X86::VPMOVSXBWrm },
  { X86::VPMOVSXBWZ128rr, X86::VPMOVSXBWrr },
  { X86::VPMOVSXDQZ128rm, X86::VPMOVSXDQrm },
  { X86::VPMOVSXDQZ128rr, X86::VPMOVSXDQrr },
  { X86::VPMOVSXWDZ128rm, X86::VPMOVSXWDrm },
  { X86::VPMOVSXWDZ128rr, X86::VPMOVSXWDrr },
  { X86::VPMOVSXWQZ128rm, X86::VPMOVSXWQrm },
  { X86::VPMOVSXWQZ128rr, X86::VPMOVSXWQrr },
  { X86::VPMOVZXBDZ128rm, X86::VPMOVZXBDrm },
  { X86::VPMOVZXBDZ128rr, X86::VPMOVZXBDrr },
  { X86::VPMOVZXBQZ128rm, X86::VPMOVZXBQrm },
  { X86::VPMOVZXBQZ128rr, X86::VPMOVZXBQrr },
  { X86::VPMOVZXBWZ128rm, X86::VPMOVZXBWrm },
  { X86::VPMOVZXBWZ128rr, X86::VPMOVZXBWrr },
  { X86::VPMOVZXDQZ128rm, X86::VPMOVZXDQrm },
  { X86::VPMOVZXDQZ128rr, X86::VPMOVZXDQrr },
  { X86::VPMOVZXWDZ128rm, X86::VPMOVZXWDrm },
  { X86::VPMOVZXWDZ128rr, X86::VPMOVZXWDrr },
  { X86::VPMOVZXWQZ128rm, X86::VPMOVZXWQrm },
  { X86::VPMOVZXWQZ128rr, X86::VPMOVZXWQrr },
  { X86::VPMULDQZ128rm, X86::VPMULDQrm },
  { X86::VPMULDQZ128rr, X86::VPMULDQrr },
  { X86::VPMULHRSWZ128rm, X86::VPMULHRSWrm },
  { X86::VPMULHRSWZ128rr, X86::VPMULHRSWrr },
  { X86::VPMULHUWZ128rm, X86::VPMULHUWrm },
  { X86::VPMULHUWZ128rr, X86::VPMULHUWrr },
  { X86::VPMULHWZ128rm, X86::VPMULHWrm },
  { X86::VPMULHWZ128rr, X86::VPMULHWrr },
  { X86::VPMULLDZ128rm, X86::VPMULLDrm },
  { X86::VPMULLDZ128rr, X86::VPMULLDrr },
  { X86::VPMULLWZ128rm, X86::VPMULLWrm },
  { X86::VPMULLWZ128rr, X86::VPMULLWrr },
  { X86::VPMULUDQZ128rm, X86::VPMULUDQrm },
  { X86::VPMULUDQZ128rr, X86::VPMULUDQrr },
  { X86::VPORDZ128rm, X86::VPORrm },
  { X86::VPORDZ128rr, X86::VPORrr },
  { X86::VPORQZ128rm, X86::VPORrm },
  { X86::VPORQZ128rr, X86::VPORrr },
  { X86::VPSADBWZ128rm, X86::VPSADBWrm },
  { X86::VPSADBWZ128rr, X86::VPSADBWrr },
  { X86::VPSHUFBZ128rm, X86::VPSHUFBrm },
  { X86::VPSHUFBZ128rr, X86::VPSHUFBrr },
  { X86::VPSHUFDZ128mi, X86::VPSHUFDmi },
  { X86::VPSHUFDZ128ri, X86::VPSHUFDri },
  { X86::VPSHUFHWZ128mi, X86::VPSHUFHWmi },
  { X86::VPSHUFHWZ128ri, X86::VPSHUFHWri },
  { X86::VPSHUFLWZ128mi, X86::VPSHUFLWmi },
  { X86::VPSHUFLWZ128ri, X86::VPSHUFLWri },
  { X86::VPSLLDQZ128rr, X86::VPSLLDQri },
  { X86::VPSLLDZ128ri, X86::VPSLLDri },
  { X86::VPSLLDZ128rm, X86::VPSLLDrm },
  { X86::VPSLLDZ128rr, X86::VPSLLDrr },
  { X86::VPSLLQZ128ri, X86::VPSLLQri },
  { X86::VPSLLQZ128rm, X86::VPSLLQrm },
  { X86::VPSLLQZ128rr, X86::VPSLLQrr },
  { X86::VPSLLVDZ128rm, X86::VPSLLVDrm },
  { X86::VPSLLVDZ128rr, X86::VPSLLVDrr },
  { X86::VPSLLVQZ128rm, X86::VPSLLVQrm },
  { X86::VPSLLVQZ128rr, X86::VPSLLVQrr },
  { X86::VPSLLWZ128ri, X86::VPSLLWri },
  { X86::VPSLLWZ128rm, X86::VPSLLWrm },
  { X86::VPSLLWZ128rr, X86::VPSLLWrr },
  { X86::VPSRADZ128ri, X86::VPSRADri },
  { X86::VPSRADZ128rm, X86::VPSRADrm },
  { X86::VPSRADZ128rr, X86::VPSRADrr },
  { X86::VPSRAVDZ128rm, X86::VPSRAVDrm },
  { X86::VPSRAVDZ128rr, X86::VPSRAVDrr },
  { X86::VPSRAWZ128ri, X86::VPSRAWri },
  { X86::VPSRAWZ128rm, X86::VPSRAWrm },
  { X86::VPSRAWZ128rr, X86::VPSRAWrr },
  { X86::VPSRLDQZ128rr, X86::VPSRLDQri },
  { X86::VPSRLDZ128ri, X86::VPSRLDri },
  { X86::VPSRLDZ128rm, X86::VPSRLDrm },
  { X86::VPSRLDZ128rr, X86::VPSRLDrr },
  { X86::VPSRLQZ128ri, X86::VPSRLQri },
  { X86::VPSRLQZ128rm, X86::VPSRLQrm },
  { X86::VPSRLQZ128rr, X86::VPSRLQrr },
  { X86::VPSRLVDZ128rm, X86::VPSRLVDrm },
  { X86::VPSRLVDZ128rr, X86::VPSRLVDrr },
  { X86::VPSRLVQZ128rm, X86::VPSRLVQrm },
  { X86::VPSRLVQZ128rr, X86::VPSRLVQrr },
  { X86::VPSRLWZ128ri, X86::VPSRLWri },
  { X86::VPSRLWZ128rm, X86::VPSRLWrm },
  { X86::VPSRLWZ128rr, X86::VPSRLWrr },
  { X86::VPSUBBZ128rm, X86::VPSUBBrm },
  { X86::VPSUBBZ128rr, X86::VPSUBBrr },
  { X86::VPSUBDZ128rm, X86::VPSUBDrm },
  { X86::VPSUBDZ128rr, X86::VPSUBDrr },
  { X86::VPSUBQZ128rm, X86::VPSUBQrm },
  { X86::VPSUBQZ128rr, X86::VPSUBQrr },
  { X86::VPSUBSBZ128rm, X86::VPSUBSBrm },
  { X86::VPSUBSBZ128rr, X86::VPSUBSBrr },
  { X86::VPSUBSWZ128rm, X86::VPSUBSWrm },
  { X86::VPSUBSWZ128rr, X86::VPSUBSWrr },
  { X86::VPSUBUSBZ128rm, X86::VPSUBUSBrm },
  { X86::VPSUBUSBZ128rr, X86::VPSUBUSBrr },
  { X86::VPSUBUSWZ128rm, X86::VPSUBUSWrm },
  { X86::VPSUBUSWZ128rr, X86::VPSUBUSWrr },
  { X86::VPSUBWZ128rm, X86::VPSUBWrm },
  { X86::VPSUBWZ128rr, X86::VPSUBWrr },
  { X86::VPUNPCKHBWZ128rm, X86::VPUNPCKHBWrm },
  { X86::VPUNPCKHBWZ128rr, X86::VPUNPCKHBWrr },
  { X86::VPUNPCKHDQZ128rm, X86::VPUNPCKHDQrm },
  { X86::VPUNPCKHDQZ128rr, X86::VPUNPCKHDQrr },
  { X86::VPUNPCKHQDQZ128rm, X86::VPUNPCKHQDQrm },
  { X86::VPUNPCKHQDQZ128rr, X86::VPUNPCKHQDQrr },
  { X86::VPUNPCKHWDZ128rm, X86::VPUNPCKHWDrm },
  { X86::VPUNPCKHWDZ128rr, X86::VPUNPCKHWDrr },
  { X86::VPUNPCKLBWZ128rm, X86::VPUNPCKLBWrm },
  { X86::VPUNPCKLBWZ128rr, X86::VPUNPCKLBWrr },
  { X86::VPUNPCKLDQZ128rm, X86::VPUNPCKLDQrm },
  { X86::VPUNPCKLDQZ128rr, X86::VPUNPCKLDQrr },
  { X86::VPUNPCKLQDQZ128rm, X86::VPUNPCKLQDQrm },
  { X86::VPUNPCKLQDQZ128rr, X86::VPUNPCKLQDQrr },
  { X86::VPUNPCKLWDZ128rm, X86::VPUNPCKLWDrm },
  { X86::VPUNPCKLWDZ128rr, X86::VPUNPCKLWDrr },
  { X86::VPXORDZ128rm, X86::VPXORrm },
  { X86::VPXORDZ128rr, X86::VPXORrr },
  { X86::VPXORQZ128rm, X86::VPXORrm },
  { X86::VPXORQZ128rr, X86::VPXORrr },
  { X86::VRNDSCALEPDZ128rmi, X86::VROUNDPDm },
  { X86::VRNDSCALEPDZ128rri, X86::VROUNDPDr },
  { X86::VRNDSCALEPSZ128rmi, X86::VROUNDPSm },
  { X86::VRNDSCALEPSZ128rri, X86::VROUNDPSr },
  { X86::VRNDSCALESDZm, X86::VROUNDSDm },
  { X86::VRNDSCALESDZm_Int, X86::VROUNDSDm_Int },
  { X86::VRNDSCALESDZr, X86::VROUNDSDr },
  { X86::VRNDSCALESDZr_Int, X86::VROUNDSDr_Int },
  { X86::VRNDSCALESSZm, X86::VROUNDSSm },
  { X86::VRNDSCALESSZm_Int, X86::VROUNDSSm_Int },
  { X86::VRNDSCALESSZr, X86::VROUNDSSr },
  { X86::VRNDSCALESSZr_Int, X86::VROUNDSSr_Int },
  { X86::VSHUFPDZ128rmi, X86::VSHUFPDrmi },
  { X86::VSHUFPDZ128rri, X86::VSHUFPDrri },
  { X86::VSHUFPSZ128rmi, X86::VSHUFPSrmi },
  { X86::VSHUFPSZ128rri, X86::VSHUFPSrri },
  { X86::VSQRTPDZ128m, X86::VSQRTPDm },
  { X86::VSQRTPDZ128r, X86::VSQRTPDr },
  { X86::VSQRTPSZ128m, X86::VSQRTPSm },
  { X86::VSQRTPSZ128r, X86::VSQRTPSr },
  { X86::VSQRTSDZm, X86::VSQRTSDm },
  { X86::VSQRTSDZm_Int, X86::VSQRTSDm_Int },
  { X86::VSQRTSDZr, X86::VSQRTSDr },
  { X86::VSQRTSDZr_Int, X86::VSQRTSDr_Int },
  { X86::VSQRTSSZm, X86::VSQRTSSm },
  { X86::VSQRTSSZm_Int, X86::VSQRTSSm_Int },
  { X86::VSQRTSSZr, X86::VSQRTSSr },
  { X86::VSQRTSSZr_Int, X86::VSQRTSSr_Int },
  { X86::VSUBPDZ128rm, X86::VSUBPDrm },
  { X86::VSUBPDZ128rr, X86::VSUBPDrr },
  { X86::VSUBPSZ128rm, X86::VSUBPSrm },
  { X86::VSUBPSZ128rr, X86::VSUBPSrr },
  { X86::VSUBSDZrm, X86::VSUBSDrm },
  { X86::VSUBSDZrm_Int, X86::VSUBSDrm_Int },
  { X86::VSUBSDZrr, X86::VSUBSDrr },
  { X86::VSUBSDZrr_Int, X86::VSUBSDrr_Int },
  { X86::VSUBSSZrm, X86::VSUBSSrm },
  { X86::VSUBSSZrm_Int, X86::VSUBSSrm_Int },
  { X86::VSUBSSZrr, X86::VSUBSSrr },
  { X86::VSUBSSZrr_Int, X86::VSUBSSrr_Int },
  { X86::VUCOMISDZrm, X86::VUCOMISDrm },
  { X86::VUCOMISDZrm_Int, X86::VUCOMISDrm_Int },
  { X86::VUCOMISDZrr, X86::VUCOMISDrr },
  { X86::VUCOMISDZrr_Int, X86::VUCOMISDrr_Int },
  { X86::VUCOMISSZrm, X86::VUCOMISSrm },
  { X86::VUCOMISSZrm_Int, X86::VUCOMISSrm_Int },
  { X86::VUCOMISSZrr, X86::VUCOMISSrr },
  { X86::VUCOMISSZrr_Int, X86::VUCOMISSrr_Int },
  { X86::VUNPCKHPDZ128rm, X86::VUNPCKHPDrm },
  { X86::VUNPCKHPDZ128rr, X86::VUNPCKHPDrr },
  { X86::VUNPCKHPSZ128rm, X86::VUNPCKHPSrm },
  { X86::VUNPCKHPSZ128rr, X86::VUNPCKHPSrr },
  { X86::VUNPCKLPDZ128rm, X86::VUNPCKLPDrm },
  { X86::VUNPCKLPDZ128rr, X86::VUNPCKLPDrr },
  { X86::VUNPCKLPSZ128rm, X86::VUNPCKLPSrm },
  { X86::VUNPCKLPSZ128rr, X86::VUNPCKLPSrr },
  { X86::VXORPDZ128rm, X86::VXORPDrm },
  { X86::VXORPDZ128rr, X86::VXORPDrr },
  { X86::VXORPSZ128rm, X86::VXORPSrm },
  { X86::VXORPSZ128rr, X86::VXORPSrr },
};

// X86 EVEX encoded instructions that have a VEX 256 encoding
// (table format: <EVEX opcode, VEX-256 opcode>).
static const X86EvexToVexCompressTableEntry X86EvexToVex256CompressTable[] = {
  // EVEX scalar with corresponding VEX.
  { X86::VADDPDZ256rm, X86::VADDPDYrm },
  { X86::VADDPDZ256rr, X86::VADDPDYrr },
  { X86::VADDPSZ256rm, X86::VADDPSYrm },
  { X86::VADDPSZ256rr, X86::VADDPSYrr },
  { X86::VAESDECLASTZ256rm, X86::VAESDECLASTYrm },
  { X86::VAESDECLASTZ256rr, X86::VAESDECLASTYrr },
  { X86::VAESDECZ256rm, X86::VAESDECYrm },
  { X86::VAESDECZ256rr, X86::VAESDECYrr },
  { X86::VAESENCLASTZ256rm, X86::VAESENCLASTYrm },
  { X86::VAESENCLASTZ256rr, X86::VAESENCLASTYrr },
  { X86::VAESENCZ256rm, X86::VAESENCYrm },
  { X86::VAESENCZ256rr, X86::VAESENCYrr },
  { X86::VANDNPDZ256rm, X86::VANDNPDYrm },
  { X86::VANDNPDZ256rr, X86::VANDNPDYrr },
  { X86::VANDNPSZ256rm, X86::VANDNPSYrm },
  { X86::VANDNPSZ256rr, X86::VANDNPSYrr },
  { X86::VANDPDZ256rm, X86::VANDPDYrm },
  { X86::VANDPDZ256rr, X86::VANDPDYrr },
  { X86::VANDPSZ256rm, X86::VANDPSYrm },
  { X86::VANDPSZ256rr, X86::VANDPSYrr },
  { X86::VBROADCASTF32X2Z256m, X86::VBROADCASTSDYrm },
  { X86::VBROADCASTF32X2Z256r, X86::VBROADCASTSDYrr },
  { X86::VBROADCASTF32X4Z256rm, X86::VBROADCASTF128 },
  { X86::VBROADCASTF64X2Z128rm, X86::VBROADCASTF128 },
  { X86::VBROADCASTI32X2Z256m, X86::VPBROADCASTQYrm },
  { X86::VBROADCASTI32X2Z256r, X86::VPBROADCASTQYrr },
  { X86::VBROADCASTI32X4Z256rm, X86::VBROADCASTI128 },
  { X86::VBROADCASTI64X2Z128rm, X86::VBROADCASTI128 },
  { X86::VBROADCASTSDZ256m, X86::VBROADCASTSDYrm },
  { X86::VBROADCASTSDZ256r, X86::VBROADCASTSDYrr },
  { X86::VBROADCASTSSZ256m, X86::VBROADCASTSSYrm },
  { X86::VBROADCASTSSZ256r, X86::VBROADCASTSSYrr },
  { X86::VCVTDQ2PDZ256rm, X86::VCVTDQ2PDYrm },
  { X86::VCVTDQ2PDZ256rr, X86::VCVTDQ2PDYrr },
  { X86::VCVTDQ2PSZ256rm, X86::VCVTDQ2PSYrm },
  { X86::VCVTDQ2PSZ256rr, X86::VCVTDQ2PSYrr },
  { X86::VCVTPD2DQZ256rm, X86::VCVTPD2DQYrm },
  { X86::VCVTPD2DQZ256rr, X86::VCVTPD2DQYrr },
  { X86::VCVTPD2PSZ256rm, X86::VCVTPD2PSYrm },
  { X86::VCVTPD2PSZ256rr, X86::VCVTPD2PSYrr },
  { X86::VCVTPH2PSZ256rm, X86::VCVTPH2PSYrm },
  { X86::VCVTPH2PSZ256rr, X86::VCVTPH2PSYrr },
  { X86::VCVTPS2DQZ256rm, X86::VCVTPS2DQYrm },
  { X86::VCVTPS2DQZ256rr, X86::VCVTPS2DQYrr },
  { X86::VCVTPS2PDZ256rm, X86::VCVTPS2PDYrm },
  { X86::VCVTPS2PDZ256rr, X86::VCVTPS2PDYrr },
  { X86::VCVTPS2PHZ256mr, X86::VCVTPS2PHYmr },
  { X86::VCVTPS2PHZ256rr, X86::VCVTPS2PHYrr },
  { X86::VCVTTPD2DQZ256rm, X86::VCVTTPD2DQYrm },
  { X86::VCVTTPD2DQZ256rr, X86::VCVTTPD2DQYrr },
  { X86::VCVTTPS2DQZ256rm, X86::VCVTTPS2DQYrm },
  { X86::VCVTTPS2DQZ256rr, X86::VCVTTPS2DQYrr },
  { X86::VDIVPDZ256rm, X86::VDIVPDYrm },
  { X86::VDIVPDZ256rr, X86::VDIVPDYrr },
  { X86::VDIVPSZ256rm, X86::VDIVPSYrm },
  { X86::VDIVPSZ256rr, X86::VDIVPSYrr },
  { X86::VEXTRACTF32x4Z256mr, X86::VEXTRACTF128mr },
  { X86::VEXTRACTF32x4Z256rr, X86::VEXTRACTF128rr },
  { X86::VEXTRACTF64x2Z256mr, X86::VEXTRACTF128mr },
  { X86::VEXTRACTF64x2Z256rr, X86::VEXTRACTF128rr },
  { X86::VEXTRACTI32x4Z256mr, X86::VEXTRACTI128mr },
  { X86::VEXTRACTI32x4Z256rr, X86::VEXTRACTI128rr },
  { X86::VEXTRACTI64x2Z256mr, X86::VEXTRACTI128mr },
  { X86::VEXTRACTI64x2Z256rr, X86::VEXTRACTI128rr },
  { X86::VFMADD132PDZ256m, X86::VFMADD132PDYm },
  { X86::VFMADD132PDZ256r, X86::VFMADD132PDYr },
  { X86::VFMADD132PSZ256m, X86::VFMADD132PSYm },
  { X86::VFMADD132PSZ256r, X86::VFMADD132PSYr },
  { X86::VFMADD213PDZ256m, X86::VFMADD213PDYm },
  { X86::VFMADD213PDZ256r, X86::VFMADD213PDYr },
  { X86::VFMADD213PSZ256m, X86::VFMADD213PSYm },
  { X86::VFMADD213PSZ256r, X86::VFMADD213PSYr },
  { X86::VFMADD231PDZ256m, X86::VFMADD231PDYm },
  { X86::VFMADD231PDZ256r, X86::VFMADD231PDYr },
  { X86::VFMADD231PSZ256m, X86::VFMADD231PSYm },
  { X86::VFMADD231PSZ256r, X86::VFMADD231PSYr },
  { X86::VFMADDSUB132PDZ256m, X86::VFMADDSUB132PDYm },
  { X86::VFMADDSUB132PDZ256r, X86::VFMADDSUB132PDYr },
  { X86::VFMADDSUB132PSZ256m, X86::VFMADDSUB132PSYm },
  { X86::VFMADDSUB132PSZ256r, X86::VFMADDSUB132PSYr },
  { X86::VFMADDSUB213PDZ256m, X86::VFMADDSUB213PDYm },
  { X86::VFMADDSUB213PDZ256r, X86::VFMADDSUB213PDYr },
  { X86::VFMADDSUB213PSZ256m, X86::VFMADDSUB213PSYm },
  { X86::VFMADDSUB213PSZ256r, X86::VFMADDSUB213PSYr },
  { X86::VFMADDSUB231PDZ256m, X86::VFMADDSUB231PDYm },
  { X86::VFMADDSUB231PDZ256r, X86::VFMADDSUB231PDYr },
  { X86::VFMADDSUB231PSZ256m, X86::VFMADDSUB231PSYm },
  { X86::VFMADDSUB231PSZ256r, X86::VFMADDSUB231PSYr },
  { X86::VFMSUB132PDZ256m, X86::VFMSUB132PDYm },
  { X86::VFMSUB132PDZ256r, X86::VFMSUB132PDYr },
  { X86::VFMSUB132PSZ256m, X86::VFMSUB132PSYm },
  { X86::VFMSUB132PSZ256r, X86::VFMSUB132PSYr },
  { X86::VFMSUB213PDZ256m, X86::VFMSUB213PDYm },
  { X86::VFMSUB213PDZ256r, X86::VFMSUB213PDYr },
  { X86::VFMSUB213PSZ256m, X86::VFMSUB213PSYm },
  { X86::VFMSUB213PSZ256r, X86::VFMSUB213PSYr },
  { X86::VFMSUB231PDZ256m, X86::VFMSUB231PDYm },
  { X86::VFMSUB231PDZ256r, X86::VFMSUB231PDYr },
  { X86::VFMSUB231PSZ256m, X86::VFMSUB231PSYm },
  { X86::VFMSUB231PSZ256r, X86::VFMSUB231PSYr },
  { X86::VFMSUBADD132PDZ256m, X86::VFMSUBADD132PDYm },
  { X86::VFMSUBADD132PDZ256r, X86::VFMSUBADD132PDYr },
  { X86::VFMSUBADD132PSZ256m, X86::VFMSUBADD132PSYm },
  { X86::VFMSUBADD132PSZ256r, X86::VFMSUBADD132PSYr },
  { X86::VFMSUBADD213PDZ256m, X86::VFMSUBADD213PDYm },
  { X86::VFMSUBADD213PDZ256r, X86::VFMSUBADD213PDYr },
  { X86::VFMSUBADD213PSZ256m, X86::VFMSUBADD213PSYm },
  { X86::VFMSUBADD213PSZ256r, X86::VFMSUBADD213PSYr },
  { X86::VFMSUBADD231PDZ256m, X86::VFMSUBADD231PDYm },
  { X86::VFMSUBADD231PDZ256r, X86::VFMSUBADD231PDYr },
  { X86::VFMSUBADD231PSZ256m, X86::VFMSUBADD231PSYm },
  { X86::VFMSUBADD231PSZ256r, X86::VFMSUBADD231PSYr },
  { X86::VFNMADD132PDZ256m, X86::VFNMADD132PDYm },
  { X86::VFNMADD132PDZ256r, X86::VFNMADD132PDYr },
  { X86::VFNMADD132PSZ256m, X86::VFNMADD132PSYm },
  { X86::VFNMADD132PSZ256r, X86::VFNMADD132PSYr },
  { X86::VFNMADD213PDZ256m, X86::VFNMADD213PDYm },
  { X86::VFNMADD213PDZ256r, X86::VFNMADD213PDYr },
  { X86::VFNMADD213PSZ256m, X86::VFNMADD213PSYm },
  { X86::VFNMADD213PSZ256r, X86::VFNMADD213PSYr },
  { X86::VFNMADD231PDZ256m, X86::VFNMADD231PDYm },
  { X86::VFNMADD231PDZ256r, X86::VFNMADD231PDYr },
  { X86::VFNMADD231PSZ256m, X86::VFNMADD231PSYm },
  { X86::VFNMADD231PSZ256r, X86::VFNMADD231PSYr },
  { X86::VFNMSUB132PDZ256m, X86::VFNMSUB132PDYm },
  { X86::VFNMSUB132PDZ256r, X86::VFNMSUB132PDYr },
  { X86::VFNMSUB132PSZ256m, X86::VFNMSUB132PSYm },
  { X86::VFNMSUB132PSZ256r, X86::VFNMSUB132PSYr },
  { X86::VFNMSUB213PDZ256m, X86::VFNMSUB213PDYm },
  { X86::VFNMSUB213PDZ256r, X86::VFNMSUB213PDYr },
  { X86::VFNMSUB213PSZ256m, X86::VFNMSUB213PSYm },
  { X86::VFNMSUB213PSZ256r, X86::VFNMSUB213PSYr },
  { X86::VFNMSUB231PDZ256m, X86::VFNMSUB231PDYm },
  { X86::VFNMSUB231PDZ256r, X86::VFNMSUB231PDYr },
  { X86::VFNMSUB231PSZ256m, X86::VFNMSUB231PSYm },
  { X86::VFNMSUB231PSZ256r, X86::VFNMSUB231PSYr },
  { X86::VGF2P8AFFINEINVQBZ256rmi, X86::VGF2P8AFFINEINVQBYrmi },
  { X86::VGF2P8AFFINEINVQBZ256rri, X86::VGF2P8AFFINEINVQBYrri },
  { X86::VGF2P8AFFINEQBZ256rmi, X86::VGF2P8AFFINEQBYrmi },
  { X86::VGF2P8AFFINEQBZ256rri, X86::VGF2P8AFFINEQBYrri },
  { X86::VGF2P8MULBZ256rm, X86::VGF2P8MULBYrm },
  { X86::VGF2P8MULBZ256rr, X86::VGF2P8MULBYrr },
  { X86::VINSERTF32x4Z256rm, X86::VINSERTF128rm },
  { X86::VINSERTF32x4Z256rr, X86::VINSERTF128rr },
  { X86::VINSERTF64x2Z256rm, X86::VINSERTF128rm },
  { X86::VINSERTF64x2Z256rr, X86::VINSERTF128rr },
  { X86::VINSERTI32x4Z256rm, X86::VINSERTI128rm },
  { X86::VINSERTI32x4Z256rr, X86::VINSERTI128rr },
  { X86::VINSERTI64x2Z256rm, X86::VINSERTI128rm },
  { X86::VINSERTI64x2Z256rr, X86::VINSERTI128rr },
  { X86::VMAXCPDZ256rm, X86::VMAXCPDYrm },
  { X86::VMAXCPDZ256rr, X86::VMAXCPDYrr },
  { X86::VMAXCPSZ256rm, X86::VMAXCPSYrm },
  { X86::VMAXCPSZ256rr, X86::VMAXCPSYrr },
  { X86::VMAXPDZ256rm, X86::VMAXCPDYrm },
  { X86::VMAXPDZ256rr, X86::VMAXCPDYrr },
  { X86::VMAXPSZ256rm, X86::VMAXCPSYrm },
  { X86::VMAXPSZ256rr, X86::VMAXCPSYrr },
  { X86::VMINCPDZ256rm, X86::VMINCPDYrm },
  { X86::VMINCPDZ256rr, X86::VMINCPDYrr },
  { X86::VMINCPSZ256rm, X86::VMINCPSYrm },
  { X86::VMINCPSZ256rr, X86::VMINCPSYrr },
  { X86::VMINPDZ256rm, X86::VMINCPDYrm },
  { X86::VMINPDZ256rr, X86::VMINCPDYrr },
  { X86::VMINPSZ256rm, X86::VMINCPSYrm },
  { X86::VMINPSZ256rr, X86::VMINCPSYrr },
  { X86::VMOVAPDZ256mr, X86::VMOVAPDYmr },
  { X86::VMOVAPDZ256rm, X86::VMOVAPDYrm },
  { X86::VMOVAPDZ256rr, X86::VMOVAPDYrr },
  { X86::VMOVAPDZ256rr_REV, X86::VMOVAPDYrr_REV },
  { X86::VMOVAPSZ256mr, X86::VMOVAPSYmr },
  { X86::VMOVAPSZ256rm, X86::VMOVAPSYrm },
  { X86::VMOVAPSZ256rr, X86::VMOVAPSYrr },
  { X86::VMOVAPSZ256rr_REV, X86::VMOVAPSYrr_REV },
  { X86::VMOVDDUPZ256rm, X86::VMOVDDUPYrm },
  { X86::VMOVDDUPZ256rr, X86::VMOVDDUPYrr },
  { X86::VMOVDQA32Z256mr, X86::VMOVDQAYmr },
  { X86::VMOVDQA32Z256rm, X86::VMOVDQAYrm },
  { X86::VMOVDQA32Z256rr, X86::VMOVDQAYrr },
  { X86::VMOVDQA32Z256rr_REV, X86::VMOVDQAYrr_REV },
  { X86::VMOVDQA64Z256mr, X86::VMOVDQAYmr },
  { X86::VMOVDQA64Z256rm, X86::VMOVDQAYrm },
  { X86::VMOVDQA64Z256rr, X86::VMOVDQAYrr },
  { X86::VMOVDQA64Z256rr_REV, X86::VMOVDQAYrr_REV },
  { X86::VMOVDQU16Z256mr, X86::VMOVDQUYmr },
  { X86::VMOVDQU16Z256rm, X86::VMOVDQUYrm },
  { X86::VMOVDQU16Z256rr, X86::VMOVDQUYrr },
  { X86::VMOVDQU16Z256rr_REV, X86::VMOVDQUYrr_REV },
  { X86::VMOVDQU32Z256mr, X86::VMOVDQUYmr },
  { X86::VMOVDQU32Z256rm, X86::VMOVDQUYrm },
  { X86::VMOVDQU32Z256rr, X86::VMOVDQUYrr },
  { X86::VMOVDQU32Z256rr_REV, X86::VMOVDQUYrr_REV },
  { X86::VMOVDQU64Z256mr, X86::VMOVDQUYmr },
  { X86::VMOVDQU64Z256rm, X86::VMOVDQUYrm },
  { X86::VMOVDQU64Z256rr, X86::VMOVDQUYrr },
  { X86::VMOVDQU64Z256rr_REV, X86::VMOVDQUYrr_REV },
  { X86::VMOVDQU8Z256mr, X86::VMOVDQUYmr },
  { X86::VMOVDQU8Z256rm, X86::VMOVDQUYrm },
  { X86::VMOVDQU8Z256rr, X86::VMOVDQUYrr },
  { X86::VMOVDQU8Z256rr_REV, X86::VMOVDQUYrr_REV },
  { X86::VMOVNTDQAZ256rm, X86::VMOVNTDQAYrm },
  { X86::VMOVNTDQZ256mr, X86::VMOVNTDQYmr },
  { X86::VMOVNTPDZ256mr, X86::VMOVNTPDYmr },
  { X86::VMOVNTPSZ256mr, X86::VMOVNTPSYmr },
  { X86::VMOVSHDUPZ256rm, X86::VMOVSHDUPYrm },
  { X86::VMOVSHDUPZ256rr, X86::VMOVSHDUPYrr },
  { X86::VMOVSLDUPZ256rm, X86::VMOVSLDUPYrm },
  { X86::VMOVSLDUPZ256rr, X86::VMOVSLDUPYrr },
  { X86::VMOVUPDZ256mr, X86::VMOVUPDYmr },
  { X86::VMOVUPDZ256rm, X86::VMOVUPDYrm },
  { X86::VMOVUPDZ256rr, X86::VMOVUPDYrr },
  { X86::VMOVUPDZ256rr_REV, X86::VMOVUPDYrr_REV },
  { X86::VMOVUPSZ256mr, X86::VMOVUPSYmr },
  { X86::VMOVUPSZ256rm, X86::VMOVUPSYrm },
  { X86::VMOVUPSZ256rr, X86::VMOVUPSYrr },
  { X86::VMOVUPSZ256rr_REV, X86::VMOVUPSYrr_REV },
  { X86::VMULPDZ256rm, X86::VMULPDYrm },
  { X86::VMULPDZ256rr, X86::VMULPDYrr },
  { X86::VMULPSZ256rm, X86::VMULPSYrm },
  { X86::VMULPSZ256rr, X86::VMULPSYrr },
  { X86::VORPDZ256rm, X86::VORPDYrm },
  { X86::VORPDZ256rr, X86::VORPDYrr },
  { X86::VORPSZ256rm, X86::VORPSYrm },
  { X86::VORPSZ256rr, X86::VORPSYrr },
  { X86::VPABSBZ256rm, X86::VPABSBYrm },
  { X86::VPABSBZ256rr, X86::VPABSBYrr },
  { X86::VPABSDZ256rm, X86::VPABSDYrm },
  { X86::VPABSDZ256rr, X86::VPABSDYrr },
  { X86::VPABSWZ256rm, X86::VPABSWYrm },
  { X86::VPABSWZ256rr, X86::VPABSWYrr },
  { X86::VPACKSSDWZ256rm, X86::VPACKSSDWYrm },
  { X86::VPACKSSDWZ256rr, X86::VPACKSSDWYrr },
  { X86::VPACKSSWBZ256rm, X86::VPACKSSWBYrm },
  { X86::VPACKSSWBZ256rr, X86::VPACKSSWBYrr },
  { X86::VPACKUSDWZ256rm, X86::VPACKUSDWYrm },
  { X86::VPACKUSDWZ256rr, X86::VPACKUSDWYrr },
  { X86::VPACKUSWBZ256rm, X86::VPACKUSWBYrm },
  { X86::VPACKUSWBZ256rr, X86::VPACKUSWBYrr },
  { X86::VPADDBZ256rm, X86::VPADDBYrm },
  { X86::VPADDBZ256rr, X86::VPADDBYrr },
  { X86::VPADDDZ256rm, X86::VPADDDYrm },
  { X86::VPADDDZ256rr, X86::VPADDDYrr },
  { X86::VPADDQZ256rm, X86::VPADDQYrm },
  { X86::VPADDQZ256rr, X86::VPADDQYrr },
  { X86::VPADDSBZ256rm, X86::VPADDSBYrm },
  { X86::VPADDSBZ256rr, X86::VPADDSBYrr },
  { X86::VPADDSWZ256rm, X86::VPADDSWYrm },
  { X86::VPADDSWZ256rr, X86::VPADDSWYrr },
  { X86::VPADDUSBZ256rm, X86::VPADDUSBYrm },
  { X86::VPADDUSBZ256rr, X86::VPADDUSBYrr },
  { X86::VPADDUSWZ256rm, X86::VPADDUSWYrm },
  { X86::VPADDUSWZ256rr, X86::VPADDUSWYrr },
  { X86::VPADDWZ256rm, X86::VPADDWYrm },
  { X86::VPADDWZ256rr, X86::VPADDWYrr },
  { X86::VPALIGNRZ256rmi, X86::VPALIGNRYrmi },
  { X86::VPALIGNRZ256rri, X86::VPALIGNRYrri },
  { X86::VPANDDZ256rm, X86::VPANDYrm },
  { X86::VPANDDZ256rr, X86::VPANDYrr },
  { X86::VPANDNDZ256rm, X86::VPANDNYrm },
  { X86::VPANDNDZ256rr, X86::VPANDNYrr },
  { X86::VPANDNQZ256rm, X86::VPANDNYrm },
  { X86::VPANDNQZ256rr, X86::VPANDNYrr },
  { X86::VPANDQZ256rm, X86::VPANDYrm },
  { X86::VPANDQZ256rr, X86::VPANDYrr },
  { X86::VPAVGBZ256rm, X86::VPAVGBYrm },
  { X86::VPAVGBZ256rr, X86::VPAVGBYrr },
  { X86::VPAVGWZ256rm, X86::VPAVGWYrm },
  { X86::VPAVGWZ256rr, X86::VPAVGWYrr },
  { X86::VPBROADCASTBZ256m, X86::VPBROADCASTBYrm },
  { X86::VPBROADCASTBZ256r, X86::VPBROADCASTBYrr },
  { X86::VPBROADCASTDZ256m, X86::VPBROADCASTDYrm },
  { X86::VPBROADCASTDZ256r, X86::VPBROADCASTDYrr },
  { X86::VPBROADCASTQZ256m, X86::VPBROADCASTQYrm },
  { X86::VPBROADCASTQZ256r, X86::VPBROADCASTQYrr },
  { X86::VPBROADCASTWZ256m, X86::VPBROADCASTWYrm },
  { X86::VPBROADCASTWZ256r, X86::VPBROADCASTWYrr },
  { X86::VPCLMULQDQZ256rm, X86::VPCLMULQDQYrm },
  { X86::VPCLMULQDQZ256rr, X86::VPCLMULQDQYrr },
  { X86::VPERMDZ256rm, X86::VPERMDYrm },
  { X86::VPERMDZ256rr, X86::VPERMDYrr },
  { X86::VPERMILPDZ256mi, X86::VPERMILPDYmi },
  { X86::VPERMILPDZ256ri, X86::VPERMILPDYri },
  { X86::VPERMILPDZ256rm, X86::VPERMILPDYrm },
  { X86::VPERMILPDZ256rr, X86::VPERMILPDYrr },
  { X86::VPERMILPSZ256mi, X86::VPERMILPSYmi },
  { X86::VPERMILPSZ256ri, X86::VPERMILPSYri },
  { X86::VPERMILPSZ256rm, X86::VPERMILPSYrm },
  { X86::VPERMILPSZ256rr, X86::VPERMILPSYrr },
  { X86::VPERMPDZ256mi, X86::VPERMPDYmi },
  { X86::VPERMPDZ256ri, X86::VPERMPDYri },
  { X86::VPERMPSZ256rm, X86::VPERMPSYrm },
  { X86::VPERMPSZ256rr, X86::VPERMPSYrr },
  { X86::VPERMQZ256mi, X86::VPERMQYmi },
  { X86::VPERMQZ256ri, X86::VPERMQYri },
  { X86::VPMADDUBSWZ256rm, X86::VPMADDUBSWYrm },
  { X86::VPMADDUBSWZ256rr, X86::VPMADDUBSWYrr },
  { X86::VPMADDWDZ256rm, X86::VPMADDWDYrm },
  { X86::VPMADDWDZ256rr, X86::VPMADDWDYrr },
  { X86::VPMAXSBZ256rm, X86::VPMAXSBYrm },
  { X86::VPMAXSBZ256rr, X86::VPMAXSBYrr },
  { X86::VPMAXSDZ256rm, X86::VPMAXSDYrm },
  { X86::VPMAXSDZ256rr, X86::VPMAXSDYrr },
  { X86::VPMAXSWZ256rm, X86::VPMAXSWYrm },
  { X86::VPMAXSWZ256rr, X86::VPMAXSWYrr },
  { X86::VPMAXUBZ256rm, X86::VPMAXUBYrm },
  { X86::VPMAXUBZ256rr, X86::VPMAXUBYrr },
  { X86::VPMAXUDZ256rm, X86::VPMAXUDYrm },
  { X86::VPMAXUDZ256rr, X86::VPMAXUDYrr },
  { X86::VPMAXUWZ256rm, X86::VPMAXUWYrm },
  { X86::VPMAXUWZ256rr, X86::VPMAXUWYrr },
  { X86::VPMINSBZ256rm, X86::VPMINSBYrm },
  { X86::VPMINSBZ256rr, X86::VPMINSBYrr },
  { X86::VPMINSDZ256rm, X86::VPMINSDYrm },
  { X86::VPMINSDZ256rr, X86::VPMINSDYrr },
  { X86::VPMINSWZ256rm, X86::VPMINSWYrm },
  { X86::VPMINSWZ256rr, X86::VPMINSWYrr },
  { X86::VPMINUBZ256rm, X86::VPMINUBYrm },
  { X86::VPMINUBZ256rr, X86::VPMINUBYrr },
  { X86::VPMINUDZ256rm, X86::VPMINUDYrm },
  { X86::VPMINUDZ256rr, X86::VPMINUDYrr },
  { X86::VPMINUWZ256rm, X86::VPMINUWYrm },
  { X86::VPMINUWZ256rr, X86::VPMINUWYrr },
  { X86::VPMOVSXBDZ256rm, X86::VPMOVSXBDYrm },
  { X86::VPMOVSXBDZ256rr, X86::VPMOVSXBDYrr },
  { X86::VPMOVSXBQZ256rm, X86::VPMOVSXBQYrm },
  { X86::VPMOVSXBQZ256rr, X86::VPMOVSXBQYrr },
  { X86::VPMOVSXBWZ256rm, X86::VPMOVSXBWYrm },
  { X86::VPMOVSXBWZ256rr, X86::VPMOVSXBWYrr },
  { X86::VPMOVSXDQZ256rm, X86::VPMOVSXDQYrm },
  { X86::VPMOVSXDQZ256rr, X86::VPMOVSXDQYrr },
  { X86::VPMOVSXWDZ256rm, X86::VPMOVSXWDYrm },
  { X86::VPMOVSXWDZ256rr, X86::VPMOVSXWDYrr },
  { X86::VPMOVSXWQZ256rm, X86::VPMOVSXWQYrm },
  { X86::VPMOVSXWQZ256rr, X86::VPMOVSXWQYrr },
  { X86::VPMOVZXBDZ256rm, X86::VPMOVZXBDYrm },
  { X86::VPMOVZXBDZ256rr, X86::VPMOVZXBDYrr },
  { X86::VPMOVZXBQZ256rm, X86::VPMOVZXBQYrm },
  { X86::VPMOVZXBQZ256rr, X86::VPMOVZXBQYrr },
  { X86::VPMOVZXBWZ256rm, X86::VPMOVZXBWYrm },
  { X86::VPMOVZXBWZ256rr, X86::VPMOVZXBWYrr },
  { X86::VPMOVZXDQZ256rm, X86::VPMOVZXDQYrm },
  { X86::VPMOVZXDQZ256rr, X86::VPMOVZXDQYrr },
  { X86::VPMOVZXWDZ256rm, X86::VPMOVZXWDYrm },
  { X86::VPMOVZXWDZ256rr, X86::VPMOVZXWDYrr },
  { X86::VPMOVZXWQZ256rm, X86::VPMOVZXWQYrm },
  { X86::VPMOVZXWQZ256rr, X86::VPMOVZXWQYrr },
  { X86::VPMULDQZ256rm, X86::VPMULDQYrm },
  { X86::VPMULDQZ256rr, X86::VPMULDQYrr },
  { X86::VPMULHRSWZ256rm, X86::VPMULHRSWYrm },
  { X86::VPMULHRSWZ256rr, X86::VPMULHRSWYrr },
  { X86::VPMULHUWZ256rm, X86::VPMULHUWYrm },
  { X86::VPMULHUWZ256rr, X86::VPMULHUWYrr },
  { X86::VPMULHWZ256rm, X86::VPMULHWYrm },
  { X86::VPMULHWZ256rr, X86::VPMULHWYrr },
  { X86::VPMULLDZ256rm, X86::VPMULLDYrm },
  { X86::VPMULLDZ256rr, X86::VPMULLDYrr },
  { X86::VPMULLWZ256rm, X86::VPMULLWYrm },
  { X86::VPMULLWZ256rr, X86::VPMULLWYrr },
  { X86::VPMULUDQZ256rm, X86::VPMULUDQYrm },
  { X86::VPMULUDQZ256rr, X86::VPMULUDQYrr },
  { X86::VPORDZ256rm, X86::VPORYrm },
  { X86::VPORDZ256rr, X86::VPORYrr },
  { X86::VPORQZ256rm, X86::VPORYrm },
  { X86::VPORQZ256rr, X86::VPORYrr },
  { X86::VPSADBWZ256rm, X86::VPSADBWYrm },
  { X86::VPSADBWZ256rr, X86::VPSADBWYrr },
  { X86::VPSHUFBZ256rm, X86::VPSHUFBYrm },
  { X86::VPSHUFBZ256rr, X86::VPSHUFBYrr },
  { X86::VPSHUFDZ256mi, X86::VPSHUFDYmi },
  { X86::VPSHUFDZ256ri, X86::VPSHUFDYri },
  { X86::VPSHUFHWZ256mi, X86::VPSHUFHWYmi },
  { X86::VPSHUFHWZ256ri, X86::VPSHUFHWYri },
  { X86::VPSHUFLWZ256mi, X86::VPSHUFLWYmi },
  { X86::VPSHUFLWZ256ri, X86::VPSHUFLWYri },
  { X86::VPSLLDQZ256rr, X86::VPSLLDQYri },
  { X86::VPSLLDZ256ri, X86::VPSLLDYri },
  { X86::VPSLLDZ256rm, X86::VPSLLDYrm },
  { X86::VPSLLDZ256rr, X86::VPSLLDYrr },
  { X86::VPSLLQZ256ri, X86::VPSLLQYri },
  { X86::VPSLLQZ256rm, X86::VPSLLQYrm },
  { X86::VPSLLQZ256rr, X86::VPSLLQYrr },
  { X86::VPSLLVDZ256rm, X86::VPSLLVDYrm },
  { X86::VPSLLVDZ256rr, X86::VPSLLVDYrr },
  { X86::VPSLLVQZ256rm, X86::VPSLLVQYrm },
  { X86::VPSLLVQZ256rr, X86::VPSLLVQYrr },
  { X86::VPSLLWZ256ri, X86::VPSLLWYri },
  { X86::VPSLLWZ256rm, X86::VPSLLWYrm },
  { X86::VPSLLWZ256rr, X86::VPSLLWYrr },
  { X86::VPSRADZ256ri, X86::VPSRADYri },
  { X86::VPSRADZ256rm, X86::VPSRADYrm },
  { X86::VPSRADZ256rr, X86::VPSRADYrr },
  { X86::VPSRAVDZ256rm, X86::VPSRAVDYrm },
  { X86::VPSRAVDZ256rr, X86::VPSRAVDYrr },
  { X86::VPSRAWZ256ri, X86::VPSRAWYri },
  { X86::VPSRAWZ256rm, X86::VPSRAWYrm },
  { X86::VPSRAWZ256rr, X86::VPSRAWYrr },
  { X86::VPSRLDQZ256rr, X86::VPSRLDQYri },
  { X86::VPSRLDZ256ri, X86::VPSRLDYri },
  { X86::VPSRLDZ256rm, X86::VPSRLDYrm },
  { X86::VPSRLDZ256rr, X86::VPSRLDYrr },
  { X86::VPSRLQZ256ri, X86::VPSRLQYri },
  { X86::VPSRLQZ256rm, X86::VPSRLQYrm },
  { X86::VPSRLQZ256rr, X86::VPSRLQYrr },
  { X86::VPSRLVDZ256rm, X86::VPSRLVDYrm },
  { X86::VPSRLVDZ256rr, X86::VPSRLVDYrr },
  { X86::VPSRLVQZ256rm, X86::VPSRLVQYrm },
  { X86::VPSRLVQZ256rr, X86::VPSRLVQYrr },
  { X86::VPSRLWZ256ri, X86::VPSRLWYri },
  { X86::VPSRLWZ256rm, X86::VPSRLWYrm },
  { X86::VPSRLWZ256rr, X86::VPSRLWYrr },
  { X86::VPSUBBZ256rm, X86::VPSUBBYrm },
  { X86::VPSUBBZ256rr, X86::VPSUBBYrr },
  { X86::VPSUBDZ256rm, X86::VPSUBDYrm },
  { X86::VPSUBDZ256rr, X86::VPSUBDYrr },
  { X86::VPSUBQZ256rm, X86::VPSUBQYrm },
  { X86::VPSUBQZ256rr, X86::VPSUBQYrr },
  { X86::VPSUBSBZ256rm, X86::VPSUBSBYrm },
  { X86::VPSUBSBZ256rr, X86::VPSUBSBYrr },
  { X86::VPSUBSWZ256rm, X86::VPSUBSWYrm },
  { X86::VPSUBSWZ256rr, X86::VPSUBSWYrr },
  { X86::VPSUBUSBZ256rm, X86::VPSUBUSBYrm },
  { X86::VPSUBUSBZ256rr, X86::VPSUBUSBYrr },
  { X86::VPSUBUSWZ256rm, X86::VPSUBUSWYrm },
  { X86::VPSUBUSWZ256rr, X86::VPSUBUSWYrr },
  { X86::VPSUBWZ256rm, X86::VPSUBWYrm },
  { X86::VPSUBWZ256rr, X86::VPSUBWYrr },
  { X86::VPUNPCKHBWZ256rm, X86::VPUNPCKHBWYrm },
  { X86::VPUNPCKHBWZ256rr, X86::VPUNPCKHBWYrr },
  { X86::VPUNPCKHDQZ256rm, X86::VPUNPCKHDQYrm },
  { X86::VPUNPCKHDQZ256rr, X86::VPUNPCKHDQYrr },
  { X86::VPUNPCKHQDQZ256rm, X86::VPUNPCKHQDQYrm },
  { X86::VPUNPCKHQDQZ256rr, X86::VPUNPCKHQDQYrr },
  { X86::VPUNPCKHWDZ256rm, X86::VPUNPCKHWDYrm },
  { X86::VPUNPCKHWDZ256rr, X86::VPUNPCKHWDYrr },
  { X86::VPUNPCKLBWZ256rm, X86::VPUNPCKLBWYrm },
  { X86::VPUNPCKLBWZ256rr, X86::VPUNPCKLBWYrr },
  { X86::VPUNPCKLDQZ256rm, X86::VPUNPCKLDQYrm },
  { X86::VPUNPCKLDQZ256rr, X86::VPUNPCKLDQYrr },
  { X86::VPUNPCKLQDQZ256rm, X86::VPUNPCKLQDQYrm },
  { X86::VPUNPCKLQDQZ256rr, X86::VPUNPCKLQDQYrr },
  { X86::VPUNPCKLWDZ256rm, X86::VPUNPCKLWDYrm },
  { X86::VPUNPCKLWDZ256rr, X86::VPUNPCKLWDYrr },
  { X86::VPXORDZ256rm, X86::VPXORYrm },
  { X86::VPXORDZ256rr, X86::VPXORYrr },
  { X86::VPXORQZ256rm, X86::VPXORYrm },
  { X86::VPXORQZ256rr, X86::VPXORYrr },
  { X86::VRNDSCALEPDZ256rmi, X86::VROUNDPDYm },
  { X86::VRNDSCALEPDZ256rri, X86::VROUNDPDYr },
  { X86::VRNDSCALEPSZ256rmi, X86::VROUNDPSYm },
  { X86::VRNDSCALEPSZ256rri, X86::VROUNDPSYr },
  { X86::VSHUFF32X4Z256rmi, X86::VPERM2F128rm },
  { X86::VSHUFF32X4Z256rri, X86::VPERM2F128rr },
  { X86::VSHUFF64X2Z256rmi, X86::VPERM2F128rm },
  { X86::VSHUFF64X2Z256rri, X86::VPERM2F128rr },
  { X86::VSHUFI32X4Z256rmi, X86::VPERM2I128rm },
  { X86::VSHUFI32X4Z256rri, X86::VPERM2I128rr },
  { X86::VSHUFI64X2Z256rmi, X86::VPERM2I128rm },
  { X86::VSHUFI64X2Z256rri, X86::VPERM2I128rr },
  { X86::VSHUFPDZ256rmi, X86::VSHUFPDYrmi },
  { X86::VSHUFPDZ256rri, X86::VSHUFPDYrri },
  { X86::VSHUFPSZ256rmi, X86::VSHUFPSYrmi },
  { X86::VSHUFPSZ256rri, X86::VSHUFPSYrri },
  { X86::VSQRTPDZ256m, X86::VSQRTPDYm },
  { X86::VSQRTPDZ256r, X86::VSQRTPDYr },
  { X86::VSQRTPSZ256m, X86::VSQRTPSYm },
  { X86::VSQRTPSZ256r, X86::VSQRTPSYr },
  { X86::VSUBPDZ256rm, X86::VSUBPDYrm },
  { X86::VSUBPDZ256rr, X86::VSUBPDYrr },
  { X86::VSUBPSZ256rm, X86::VSUBPSYrm },
  { X86::VSUBPSZ256rr, X86::VSUBPSYrr },
  { X86::VUNPCKHPDZ256rm, X86::VUNPCKHPDYrm },
  { X86::VUNPCKHPDZ256rr, X86::VUNPCKHPDYrr },
  { X86::VUNPCKHPSZ256rm, X86::VUNPCKHPSYrm },
  { X86::VUNPCKHPSZ256rr, X86::VUNPCKHPSYrr },
  { X86::VUNPCKLPDZ256rm, X86::VUNPCKLPDYrm },
  { X86::VUNPCKLPDZ256rr, X86::VUNPCKLPDYrr },
  { X86::VUNPCKLPSZ256rm, X86::VUNPCKLPSYrm },
  { X86::VUNPCKLPSZ256rr, X86::VUNPCKLPSYrr },
  { X86::VXORPDZ256rm, X86::VXORPDYrm },
  { X86::VXORPDZ256rr, X86::VXORPDYrr },
  { X86::VXORPSZ256rm, X86::VXORPSYrm },
  { X86::VXORPSZ256rr, X86::VXORPSYrr },
};


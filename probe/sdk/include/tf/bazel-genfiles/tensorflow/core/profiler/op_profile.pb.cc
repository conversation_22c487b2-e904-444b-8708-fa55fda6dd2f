// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/profiler/op_profile.proto

#include "tensorflow/core/profiler/op_profile.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_Metrics;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_Node_InstructionCategory;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_Node_XLAInstruction_LayoutAnalysis_Dimension;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_Node_XLAInstruction;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_Node_XLAInstruction_LayoutAnalysis;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto ::google::protobuf::internal::SCCInfo<3> scc_info_Node;
}  // namespace protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto
namespace tensorflow {
namespace profiler {
namespace op_profile {
class ProfileDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Profile>
      _instance;
} _Profile_default_instance_;
class Node_InstructionCategoryDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Node_InstructionCategory>
      _instance;
} _Node_InstructionCategory_default_instance_;
class Node_XLAInstruction_LayoutAnalysis_DimensionDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Node_XLAInstruction_LayoutAnalysis_Dimension>
      _instance;
} _Node_XLAInstruction_LayoutAnalysis_Dimension_default_instance_;
class Node_XLAInstruction_LayoutAnalysisDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Node_XLAInstruction_LayoutAnalysis>
      _instance;
} _Node_XLAInstruction_LayoutAnalysis_default_instance_;
class Node_XLAInstructionDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Node_XLAInstruction>
      _instance;
} _Node_XLAInstruction_default_instance_;
class NodeDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Node>
      _instance;
  const ::tensorflow::profiler::op_profile::Node_InstructionCategory* category_;
  const ::tensorflow::profiler::op_profile::Node_XLAInstruction* xla_;
} _Node_default_instance_;
class MetricsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Metrics>
      _instance;
} _Metrics_default_instance_;
}  // namespace op_profile
}  // namespace profiler
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto {
static void InitDefaultsProfile() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::profiler::op_profile::_Profile_default_instance_;
    new (ptr) ::tensorflow::profiler::op_profile::Profile();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::profiler::op_profile::Profile::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_Profile =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsProfile}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::scc_info_Node.base,}};

static void InitDefaultsNode_InstructionCategory() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::profiler::op_profile::_Node_InstructionCategory_default_instance_;
    new (ptr) ::tensorflow::profiler::op_profile::Node_InstructionCategory();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::profiler::op_profile::Node_InstructionCategory::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_Node_InstructionCategory =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsNode_InstructionCategory}, {}};

static void InitDefaultsNode_XLAInstruction_LayoutAnalysis_Dimension() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::profiler::op_profile::_Node_XLAInstruction_LayoutAnalysis_Dimension_default_instance_;
    new (ptr) ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis_Dimension();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis_Dimension::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_Node_XLAInstruction_LayoutAnalysis_Dimension =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsNode_XLAInstruction_LayoutAnalysis_Dimension}, {}};

static void InitDefaultsNode_XLAInstruction_LayoutAnalysis() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::profiler::op_profile::_Node_XLAInstruction_LayoutAnalysis_default_instance_;
    new (ptr) ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_Node_XLAInstruction_LayoutAnalysis =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsNode_XLAInstruction_LayoutAnalysis}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::scc_info_Node_XLAInstruction_LayoutAnalysis_Dimension.base,}};

static void InitDefaultsNode_XLAInstruction() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::profiler::op_profile::_Node_XLAInstruction_default_instance_;
    new (ptr) ::tensorflow::profiler::op_profile::Node_XLAInstruction();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::profiler::op_profile::Node_XLAInstruction::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_Node_XLAInstruction =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsNode_XLAInstruction}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::scc_info_Node_XLAInstruction_LayoutAnalysis.base,}};

static void InitDefaultsNode() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::profiler::op_profile::_Node_default_instance_;
    new (ptr) ::tensorflow::profiler::op_profile::Node();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::profiler::op_profile::Node::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<3> scc_info_Node =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 3, InitDefaultsNode}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::scc_info_Metrics.base,
      &protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::scc_info_Node_InstructionCategory.base,
      &protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::scc_info_Node_XLAInstruction.base,}};

static void InitDefaultsMetrics() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::profiler::op_profile::_Metrics_default_instance_;
    new (ptr) ::tensorflow::profiler::op_profile::Metrics();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::profiler::op_profile::Metrics::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_Metrics =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsMetrics}, {}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_Profile.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Node_InstructionCategory.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Node_XLAInstruction_LayoutAnalysis_Dimension.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Node_XLAInstruction_LayoutAnalysis.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Node_XLAInstruction.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Node.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Metrics.base);
}

::google::protobuf::Metadata file_level_metadata[7];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Profile, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Profile, by_category_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Profile, by_program_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Node_InstructionCategory, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis_Dimension, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis_Dimension, size_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis_Dimension, alignment_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis_Dimension, semantics_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis, dimensions_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Node_XLAInstruction, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Node_XLAInstruction, op_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Node_XLAInstruction, expression_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Node_XLAInstruction, provenance_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Node_XLAInstruction, category_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Node_XLAInstruction, layout_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Node, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Node, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Node, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Node, metrics_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Node, children_),
  offsetof(::tensorflow::profiler::op_profile::NodeDefaultTypeInternal, category_),
  offsetof(::tensorflow::profiler::op_profile::NodeDefaultTypeInternal, xla_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Node, num_children_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Node, contents_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Metrics, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Metrics, time_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Metrics, flops_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Metrics, memory_bandwidth_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Metrics, raw_time_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Metrics, raw_flops_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::op_profile::Metrics, raw_bytes_accessed_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::profiler::op_profile::Profile)},
  { 7, -1, sizeof(::tensorflow::profiler::op_profile::Node_InstructionCategory)},
  { 12, -1, sizeof(::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis_Dimension)},
  { 20, -1, sizeof(::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis)},
  { 26, -1, sizeof(::tensorflow::profiler::op_profile::Node_XLAInstruction)},
  { 36, -1, sizeof(::tensorflow::profiler::op_profile::Node)},
  { 48, -1, sizeof(::tensorflow::profiler::op_profile::Metrics)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::profiler::op_profile::_Profile_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::profiler::op_profile::_Node_InstructionCategory_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::profiler::op_profile::_Node_XLAInstruction_LayoutAnalysis_Dimension_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::profiler::op_profile::_Node_XLAInstruction_LayoutAnalysis_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::profiler::op_profile::_Node_XLAInstruction_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::profiler::op_profile::_Node_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::profiler::op_profile::_Metrics_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/profiler/op_profile.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 7);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n)tensorflow/core/profiler/op_profile.pr"
      "oto\022\036tensorflow.profiler.op_profile\"\255\001\n\007"
      "Profile\0229\n\013by_category\030\001 \001(\0132$.tensorflo"
      "w.profiler.op_profile.Node\0228\n\nby_program"
      "\030\004 \001(\0132$.tensorflow.profiler.op_profile."
      "NodeJ\004\010\002\020\003J\004\010\003\020\004R\024by_program_structureR\013"
      "per_program\"\264\005\n\004Node\022\014\n\004name\030\001 \001(\t\0228\n\007me"
      "trics\030\002 \001(\0132\'.tensorflow.profiler.op_pro"
      "file.Metrics\0226\n\010children\030\003 \003(\0132$.tensorf"
      "low.profiler.op_profile.Node\022L\n\010category"
      "\030\004 \001(\01328.tensorflow.profiler.op_profile."
      "Node.InstructionCategoryH\000\022B\n\003xla\030\005 \001(\0132"
      "3.tensorflow.profiler.op_profile.Node.XL"
      "AInstructionH\000\022\024\n\014num_children\030\006 \001(\005\032\025\n\023"
      "InstructionCategory\032\340\002\n\016XLAInstruction\022\n"
      "\n\002op\030\001 \001(\t\022\022\n\nexpression\030\002 \001(\t\022\022\n\nproven"
      "ance\030\003 \001(\t\022\020\n\010category\030\004 \001(\t\022R\n\006layout\030\005"
      " \001(\0132B.tensorflow.profiler.op_profile.No"
      "de.XLAInstruction.LayoutAnalysis\032\263\001\n\016Lay"
      "outAnalysis\022`\n\ndimensions\030\001 \003(\0132L.tensor"
      "flow.profiler.op_profile.Node.XLAInstruc"
      "tion.LayoutAnalysis.Dimension\032\?\n\tDimensi"
      "on\022\014\n\004size\030\001 \001(\005\022\021\n\talignment\030\002 \001(\005\022\021\n\ts"
      "emantics\030\003 \001(\tB\n\n\010contents\"\201\001\n\007Metrics\022\014"
      "\n\004time\030\001 \001(\001\022\r\n\005flops\030\002 \001(\001\022\030\n\020memory_ba"
      "ndwidth\030\003 \001(\001\022\020\n\010raw_time\030\013 \001(\001\022\021\n\traw_f"
      "lops\030\014 \001(\001\022\032\n\022raw_bytes_accessed\030\r \001(\001b\006"
      "proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 1086);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/profiler/op_profile.proto", &protobuf_RegisterTypes);
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto
namespace tensorflow {
namespace profiler {
namespace op_profile {

// ===================================================================

void Profile::InitAsDefaultInstance() {
  ::tensorflow::profiler::op_profile::_Profile_default_instance_._instance.get_mutable()->by_category_ = const_cast< ::tensorflow::profiler::op_profile::Node*>(
      ::tensorflow::profiler::op_profile::Node::internal_default_instance());
  ::tensorflow::profiler::op_profile::_Profile_default_instance_._instance.get_mutable()->by_program_ = const_cast< ::tensorflow::profiler::op_profile::Node*>(
      ::tensorflow::profiler::op_profile::Node::internal_default_instance());
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Profile::kByCategoryFieldNumber;
const int Profile::kByProgramFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Profile::Profile()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::scc_info_Profile.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.profiler.op_profile.Profile)
}
Profile::Profile(const Profile& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_by_category()) {
    by_category_ = new ::tensorflow::profiler::op_profile::Node(*from.by_category_);
  } else {
    by_category_ = NULL;
  }
  if (from.has_by_program()) {
    by_program_ = new ::tensorflow::profiler::op_profile::Node(*from.by_program_);
  } else {
    by_program_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.profiler.op_profile.Profile)
}

void Profile::SharedCtor() {
  ::memset(&by_category_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&by_program_) -
      reinterpret_cast<char*>(&by_category_)) + sizeof(by_program_));
}

Profile::~Profile() {
  // @@protoc_insertion_point(destructor:tensorflow.profiler.op_profile.Profile)
  SharedDtor();
}

void Profile::SharedDtor() {
  if (this != internal_default_instance()) delete by_category_;
  if (this != internal_default_instance()) delete by_program_;
}

void Profile::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Profile::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Profile& Profile::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::scc_info_Profile.base);
  return *internal_default_instance();
}


void Profile::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.profiler.op_profile.Profile)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && by_category_ != NULL) {
    delete by_category_;
  }
  by_category_ = NULL;
  if (GetArenaNoVirtual() == NULL && by_program_ != NULL) {
    delete by_program_;
  }
  by_program_ = NULL;
  _internal_metadata_.Clear();
}

bool Profile::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.profiler.op_profile.Profile)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.profiler.op_profile.Node by_category = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_by_category()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.profiler.op_profile.Node by_program = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_by_program()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.profiler.op_profile.Profile)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.profiler.op_profile.Profile)
  return false;
#undef DO_
}

void Profile::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.profiler.op_profile.Profile)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.profiler.op_profile.Node by_category = 1;
  if (this->has_by_category()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->_internal_by_category(), output);
  }

  // .tensorflow.profiler.op_profile.Node by_program = 4;
  if (this->has_by_program()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->_internal_by_program(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.profiler.op_profile.Profile)
}

::google::protobuf::uint8* Profile::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.profiler.op_profile.Profile)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.profiler.op_profile.Node by_category = 1;
  if (this->has_by_category()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->_internal_by_category(), deterministic, target);
  }

  // .tensorflow.profiler.op_profile.Node by_program = 4;
  if (this->has_by_program()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->_internal_by_program(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.profiler.op_profile.Profile)
  return target;
}

size_t Profile::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.profiler.op_profile.Profile)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .tensorflow.profiler.op_profile.Node by_category = 1;
  if (this->has_by_category()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *by_category_);
  }

  // .tensorflow.profiler.op_profile.Node by_program = 4;
  if (this->has_by_program()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *by_program_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Profile::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.profiler.op_profile.Profile)
  GOOGLE_DCHECK_NE(&from, this);
  const Profile* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Profile>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.profiler.op_profile.Profile)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.profiler.op_profile.Profile)
    MergeFrom(*source);
  }
}

void Profile::MergeFrom(const Profile& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.profiler.op_profile.Profile)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_by_category()) {
    mutable_by_category()->::tensorflow::profiler::op_profile::Node::MergeFrom(from.by_category());
  }
  if (from.has_by_program()) {
    mutable_by_program()->::tensorflow::profiler::op_profile::Node::MergeFrom(from.by_program());
  }
}

void Profile::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.profiler.op_profile.Profile)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Profile::CopyFrom(const Profile& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.profiler.op_profile.Profile)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Profile::IsInitialized() const {
  return true;
}

void Profile::Swap(Profile* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Profile::InternalSwap(Profile* other) {
  using std::swap;
  swap(by_category_, other->by_category_);
  swap(by_program_, other->by_program_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Profile::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Node_InstructionCategory::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Node_InstructionCategory::Node_InstructionCategory()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::scc_info_Node_InstructionCategory.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.profiler.op_profile.Node.InstructionCategory)
}
Node_InstructionCategory::Node_InstructionCategory(const Node_InstructionCategory& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.profiler.op_profile.Node.InstructionCategory)
}

void Node_InstructionCategory::SharedCtor() {
}

Node_InstructionCategory::~Node_InstructionCategory() {
  // @@protoc_insertion_point(destructor:tensorflow.profiler.op_profile.Node.InstructionCategory)
  SharedDtor();
}

void Node_InstructionCategory::SharedDtor() {
}

void Node_InstructionCategory::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Node_InstructionCategory::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Node_InstructionCategory& Node_InstructionCategory::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::scc_info_Node_InstructionCategory.base);
  return *internal_default_instance();
}


void Node_InstructionCategory::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.profiler.op_profile.Node.InstructionCategory)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_metadata_.Clear();
}

bool Node_InstructionCategory::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.profiler.op_profile.Node.InstructionCategory)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, _internal_metadata_.mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.profiler.op_profile.Node.InstructionCategory)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.profiler.op_profile.Node.InstructionCategory)
  return false;
#undef DO_
}

void Node_InstructionCategory::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.profiler.op_profile.Node.InstructionCategory)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.profiler.op_profile.Node.InstructionCategory)
}

::google::protobuf::uint8* Node_InstructionCategory::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.profiler.op_profile.Node.InstructionCategory)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.profiler.op_profile.Node.InstructionCategory)
  return target;
}

size_t Node_InstructionCategory::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.profiler.op_profile.Node.InstructionCategory)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Node_InstructionCategory::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.profiler.op_profile.Node.InstructionCategory)
  GOOGLE_DCHECK_NE(&from, this);
  const Node_InstructionCategory* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Node_InstructionCategory>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.profiler.op_profile.Node.InstructionCategory)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.profiler.op_profile.Node.InstructionCategory)
    MergeFrom(*source);
  }
}

void Node_InstructionCategory::MergeFrom(const Node_InstructionCategory& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.profiler.op_profile.Node.InstructionCategory)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

}

void Node_InstructionCategory::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.profiler.op_profile.Node.InstructionCategory)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Node_InstructionCategory::CopyFrom(const Node_InstructionCategory& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.profiler.op_profile.Node.InstructionCategory)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Node_InstructionCategory::IsInitialized() const {
  return true;
}

void Node_InstructionCategory::Swap(Node_InstructionCategory* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Node_InstructionCategory::InternalSwap(Node_InstructionCategory* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Node_InstructionCategory::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Node_XLAInstruction_LayoutAnalysis_Dimension::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Node_XLAInstruction_LayoutAnalysis_Dimension::kSizeFieldNumber;
const int Node_XLAInstruction_LayoutAnalysis_Dimension::kAlignmentFieldNumber;
const int Node_XLAInstruction_LayoutAnalysis_Dimension::kSemanticsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Node_XLAInstruction_LayoutAnalysis_Dimension::Node_XLAInstruction_LayoutAnalysis_Dimension()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::scc_info_Node_XLAInstruction_LayoutAnalysis_Dimension.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension)
}
Node_XLAInstruction_LayoutAnalysis_Dimension::Node_XLAInstruction_LayoutAnalysis_Dimension(const Node_XLAInstruction_LayoutAnalysis_Dimension& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  semantics_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.semantics().size() > 0) {
    semantics_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.semantics_);
  }
  ::memcpy(&size_, &from.size_,
    static_cast<size_t>(reinterpret_cast<char*>(&alignment_) -
    reinterpret_cast<char*>(&size_)) + sizeof(alignment_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension)
}

void Node_XLAInstruction_LayoutAnalysis_Dimension::SharedCtor() {
  semantics_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&size_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&alignment_) -
      reinterpret_cast<char*>(&size_)) + sizeof(alignment_));
}

Node_XLAInstruction_LayoutAnalysis_Dimension::~Node_XLAInstruction_LayoutAnalysis_Dimension() {
  // @@protoc_insertion_point(destructor:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension)
  SharedDtor();
}

void Node_XLAInstruction_LayoutAnalysis_Dimension::SharedDtor() {
  semantics_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void Node_XLAInstruction_LayoutAnalysis_Dimension::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Node_XLAInstruction_LayoutAnalysis_Dimension::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Node_XLAInstruction_LayoutAnalysis_Dimension& Node_XLAInstruction_LayoutAnalysis_Dimension::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::scc_info_Node_XLAInstruction_LayoutAnalysis_Dimension.base);
  return *internal_default_instance();
}


void Node_XLAInstruction_LayoutAnalysis_Dimension::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  semantics_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&size_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&alignment_) -
      reinterpret_cast<char*>(&size_)) + sizeof(alignment_));
  _internal_metadata_.Clear();
}

bool Node_XLAInstruction_LayoutAnalysis_Dimension::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 size = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 alignment = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &alignment_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string semantics = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_semantics()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->semantics().data(), static_cast<int>(this->semantics().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension.semantics"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension)
  return false;
#undef DO_
}

void Node_XLAInstruction_LayoutAnalysis_Dimension::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 size = 1;
  if (this->size() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->size(), output);
  }

  // int32 alignment = 2;
  if (this->alignment() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->alignment(), output);
  }

  // string semantics = 3;
  if (this->semantics().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->semantics().data(), static_cast<int>(this->semantics().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension.semantics");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->semantics(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension)
}

::google::protobuf::uint8* Node_XLAInstruction_LayoutAnalysis_Dimension::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 size = 1;
  if (this->size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->size(), target);
  }

  // int32 alignment = 2;
  if (this->alignment() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->alignment(), target);
  }

  // string semantics = 3;
  if (this->semantics().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->semantics().data(), static_cast<int>(this->semantics().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension.semantics");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->semantics(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension)
  return target;
}

size_t Node_XLAInstruction_LayoutAnalysis_Dimension::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string semantics = 3;
  if (this->semantics().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->semantics());
  }

  // int32 size = 1;
  if (this->size() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->size());
  }

  // int32 alignment = 2;
  if (this->alignment() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->alignment());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Node_XLAInstruction_LayoutAnalysis_Dimension::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension)
  GOOGLE_DCHECK_NE(&from, this);
  const Node_XLAInstruction_LayoutAnalysis_Dimension* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Node_XLAInstruction_LayoutAnalysis_Dimension>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension)
    MergeFrom(*source);
  }
}

void Node_XLAInstruction_LayoutAnalysis_Dimension::MergeFrom(const Node_XLAInstruction_LayoutAnalysis_Dimension& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.semantics().size() > 0) {

    semantics_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.semantics_);
  }
  if (from.size() != 0) {
    set_size(from.size());
  }
  if (from.alignment() != 0) {
    set_alignment(from.alignment());
  }
}

void Node_XLAInstruction_LayoutAnalysis_Dimension::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Node_XLAInstruction_LayoutAnalysis_Dimension::CopyFrom(const Node_XLAInstruction_LayoutAnalysis_Dimension& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Node_XLAInstruction_LayoutAnalysis_Dimension::IsInitialized() const {
  return true;
}

void Node_XLAInstruction_LayoutAnalysis_Dimension::Swap(Node_XLAInstruction_LayoutAnalysis_Dimension* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Node_XLAInstruction_LayoutAnalysis_Dimension::InternalSwap(Node_XLAInstruction_LayoutAnalysis_Dimension* other) {
  using std::swap;
  semantics_.Swap(&other->semantics_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(size_, other->size_);
  swap(alignment_, other->alignment_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Node_XLAInstruction_LayoutAnalysis_Dimension::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Node_XLAInstruction_LayoutAnalysis::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Node_XLAInstruction_LayoutAnalysis::kDimensionsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Node_XLAInstruction_LayoutAnalysis::Node_XLAInstruction_LayoutAnalysis()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::scc_info_Node_XLAInstruction_LayoutAnalysis.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis)
}
Node_XLAInstruction_LayoutAnalysis::Node_XLAInstruction_LayoutAnalysis(const Node_XLAInstruction_LayoutAnalysis& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      dimensions_(from.dimensions_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis)
}

void Node_XLAInstruction_LayoutAnalysis::SharedCtor() {
}

Node_XLAInstruction_LayoutAnalysis::~Node_XLAInstruction_LayoutAnalysis() {
  // @@protoc_insertion_point(destructor:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis)
  SharedDtor();
}

void Node_XLAInstruction_LayoutAnalysis::SharedDtor() {
}

void Node_XLAInstruction_LayoutAnalysis::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Node_XLAInstruction_LayoutAnalysis::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Node_XLAInstruction_LayoutAnalysis& Node_XLAInstruction_LayoutAnalysis::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::scc_info_Node_XLAInstruction_LayoutAnalysis.base);
  return *internal_default_instance();
}


void Node_XLAInstruction_LayoutAnalysis::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  dimensions_.Clear();
  _internal_metadata_.Clear();
}

bool Node_XLAInstruction_LayoutAnalysis::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension dimensions = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_dimensions()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis)
  return false;
#undef DO_
}

void Node_XLAInstruction_LayoutAnalysis::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension dimensions = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->dimensions_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->dimensions(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis)
}

::google::protobuf::uint8* Node_XLAInstruction_LayoutAnalysis::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension dimensions = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->dimensions_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->dimensions(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis)
  return target;
}

size_t Node_XLAInstruction_LayoutAnalysis::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension dimensions = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->dimensions_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->dimensions(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Node_XLAInstruction_LayoutAnalysis::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis)
  GOOGLE_DCHECK_NE(&from, this);
  const Node_XLAInstruction_LayoutAnalysis* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Node_XLAInstruction_LayoutAnalysis>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis)
    MergeFrom(*source);
  }
}

void Node_XLAInstruction_LayoutAnalysis::MergeFrom(const Node_XLAInstruction_LayoutAnalysis& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  dimensions_.MergeFrom(from.dimensions_);
}

void Node_XLAInstruction_LayoutAnalysis::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Node_XLAInstruction_LayoutAnalysis::CopyFrom(const Node_XLAInstruction_LayoutAnalysis& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Node_XLAInstruction_LayoutAnalysis::IsInitialized() const {
  return true;
}

void Node_XLAInstruction_LayoutAnalysis::Swap(Node_XLAInstruction_LayoutAnalysis* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Node_XLAInstruction_LayoutAnalysis::InternalSwap(Node_XLAInstruction_LayoutAnalysis* other) {
  using std::swap;
  CastToBase(&dimensions_)->InternalSwap(CastToBase(&other->dimensions_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Node_XLAInstruction_LayoutAnalysis::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Node_XLAInstruction::InitAsDefaultInstance() {
  ::tensorflow::profiler::op_profile::_Node_XLAInstruction_default_instance_._instance.get_mutable()->layout_ = const_cast< ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis*>(
      ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis::internal_default_instance());
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Node_XLAInstruction::kOpFieldNumber;
const int Node_XLAInstruction::kExpressionFieldNumber;
const int Node_XLAInstruction::kProvenanceFieldNumber;
const int Node_XLAInstruction::kCategoryFieldNumber;
const int Node_XLAInstruction::kLayoutFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Node_XLAInstruction::Node_XLAInstruction()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::scc_info_Node_XLAInstruction.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.profiler.op_profile.Node.XLAInstruction)
}
Node_XLAInstruction::Node_XLAInstruction(const Node_XLAInstruction& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  op_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.op().size() > 0) {
    op_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.op_);
  }
  expression_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.expression().size() > 0) {
    expression_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.expression_);
  }
  provenance_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.provenance().size() > 0) {
    provenance_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.provenance_);
  }
  category_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.category().size() > 0) {
    category_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.category_);
  }
  if (from.has_layout()) {
    layout_ = new ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis(*from.layout_);
  } else {
    layout_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.profiler.op_profile.Node.XLAInstruction)
}

void Node_XLAInstruction::SharedCtor() {
  op_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  expression_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  provenance_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  category_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  layout_ = NULL;
}

Node_XLAInstruction::~Node_XLAInstruction() {
  // @@protoc_insertion_point(destructor:tensorflow.profiler.op_profile.Node.XLAInstruction)
  SharedDtor();
}

void Node_XLAInstruction::SharedDtor() {
  op_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  expression_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  provenance_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  category_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete layout_;
}

void Node_XLAInstruction::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Node_XLAInstruction::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Node_XLAInstruction& Node_XLAInstruction::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::scc_info_Node_XLAInstruction.base);
  return *internal_default_instance();
}


void Node_XLAInstruction::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.profiler.op_profile.Node.XLAInstruction)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  op_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  expression_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  provenance_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  category_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && layout_ != NULL) {
    delete layout_;
  }
  layout_ = NULL;
  _internal_metadata_.Clear();
}

bool Node_XLAInstruction::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.profiler.op_profile.Node.XLAInstruction)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string op = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_op()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->op().data(), static_cast<int>(this->op().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.profiler.op_profile.Node.XLAInstruction.op"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string expression = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_expression()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->expression().data(), static_cast<int>(this->expression().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.profiler.op_profile.Node.XLAInstruction.expression"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string provenance = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_provenance()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->provenance().data(), static_cast<int>(this->provenance().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.profiler.op_profile.Node.XLAInstruction.provenance"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string category = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_category()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->category().data(), static_cast<int>(this->category().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.profiler.op_profile.Node.XLAInstruction.category"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis layout = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_layout()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.profiler.op_profile.Node.XLAInstruction)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.profiler.op_profile.Node.XLAInstruction)
  return false;
#undef DO_
}

void Node_XLAInstruction::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.profiler.op_profile.Node.XLAInstruction)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string op = 1;
  if (this->op().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->op().data(), static_cast<int>(this->op().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.profiler.op_profile.Node.XLAInstruction.op");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->op(), output);
  }

  // string expression = 2;
  if (this->expression().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->expression().data(), static_cast<int>(this->expression().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.profiler.op_profile.Node.XLAInstruction.expression");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->expression(), output);
  }

  // string provenance = 3;
  if (this->provenance().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->provenance().data(), static_cast<int>(this->provenance().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.profiler.op_profile.Node.XLAInstruction.provenance");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->provenance(), output);
  }

  // string category = 4;
  if (this->category().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->category().data(), static_cast<int>(this->category().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.profiler.op_profile.Node.XLAInstruction.category");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->category(), output);
  }

  // .tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis layout = 5;
  if (this->has_layout()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, this->_internal_layout(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.profiler.op_profile.Node.XLAInstruction)
}

::google::protobuf::uint8* Node_XLAInstruction::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.profiler.op_profile.Node.XLAInstruction)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string op = 1;
  if (this->op().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->op().data(), static_cast<int>(this->op().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.profiler.op_profile.Node.XLAInstruction.op");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->op(), target);
  }

  // string expression = 2;
  if (this->expression().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->expression().data(), static_cast<int>(this->expression().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.profiler.op_profile.Node.XLAInstruction.expression");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->expression(), target);
  }

  // string provenance = 3;
  if (this->provenance().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->provenance().data(), static_cast<int>(this->provenance().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.profiler.op_profile.Node.XLAInstruction.provenance");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->provenance(), target);
  }

  // string category = 4;
  if (this->category().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->category().data(), static_cast<int>(this->category().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.profiler.op_profile.Node.XLAInstruction.category");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->category(), target);
  }

  // .tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis layout = 5;
  if (this->has_layout()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        5, this->_internal_layout(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.profiler.op_profile.Node.XLAInstruction)
  return target;
}

size_t Node_XLAInstruction::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.profiler.op_profile.Node.XLAInstruction)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string op = 1;
  if (this->op().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->op());
  }

  // string expression = 2;
  if (this->expression().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->expression());
  }

  // string provenance = 3;
  if (this->provenance().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->provenance());
  }

  // string category = 4;
  if (this->category().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->category());
  }

  // .tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis layout = 5;
  if (this->has_layout()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *layout_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Node_XLAInstruction::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.profiler.op_profile.Node.XLAInstruction)
  GOOGLE_DCHECK_NE(&from, this);
  const Node_XLAInstruction* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Node_XLAInstruction>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.profiler.op_profile.Node.XLAInstruction)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.profiler.op_profile.Node.XLAInstruction)
    MergeFrom(*source);
  }
}

void Node_XLAInstruction::MergeFrom(const Node_XLAInstruction& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.profiler.op_profile.Node.XLAInstruction)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.op().size() > 0) {

    op_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.op_);
  }
  if (from.expression().size() > 0) {

    expression_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.expression_);
  }
  if (from.provenance().size() > 0) {

    provenance_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.provenance_);
  }
  if (from.category().size() > 0) {

    category_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.category_);
  }
  if (from.has_layout()) {
    mutable_layout()->::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis::MergeFrom(from.layout());
  }
}

void Node_XLAInstruction::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.profiler.op_profile.Node.XLAInstruction)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Node_XLAInstruction::CopyFrom(const Node_XLAInstruction& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.profiler.op_profile.Node.XLAInstruction)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Node_XLAInstruction::IsInitialized() const {
  return true;
}

void Node_XLAInstruction::Swap(Node_XLAInstruction* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Node_XLAInstruction::InternalSwap(Node_XLAInstruction* other) {
  using std::swap;
  op_.Swap(&other->op_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  expression_.Swap(&other->expression_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  provenance_.Swap(&other->provenance_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  category_.Swap(&other->category_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(layout_, other->layout_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Node_XLAInstruction::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Node::InitAsDefaultInstance() {
  ::tensorflow::profiler::op_profile::_Node_default_instance_._instance.get_mutable()->metrics_ = const_cast< ::tensorflow::profiler::op_profile::Metrics*>(
      ::tensorflow::profiler::op_profile::Metrics::internal_default_instance());
  ::tensorflow::profiler::op_profile::_Node_default_instance_.category_ = const_cast< ::tensorflow::profiler::op_profile::Node_InstructionCategory*>(
      ::tensorflow::profiler::op_profile::Node_InstructionCategory::internal_default_instance());
  ::tensorflow::profiler::op_profile::_Node_default_instance_.xla_ = const_cast< ::tensorflow::profiler::op_profile::Node_XLAInstruction*>(
      ::tensorflow::profiler::op_profile::Node_XLAInstruction::internal_default_instance());
}
void Node::set_allocated_category(::tensorflow::profiler::op_profile::Node_InstructionCategory* category) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_contents();
  if (category) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      category = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, category, submessage_arena);
    }
    set_has_category();
    contents_.category_ = category;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.op_profile.Node.category)
}
void Node::set_allocated_xla(::tensorflow::profiler::op_profile::Node_XLAInstruction* xla) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_contents();
  if (xla) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      xla = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, xla, submessage_arena);
    }
    set_has_xla();
    contents_.xla_ = xla;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.op_profile.Node.xla)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Node::kNameFieldNumber;
const int Node::kMetricsFieldNumber;
const int Node::kChildrenFieldNumber;
const int Node::kCategoryFieldNumber;
const int Node::kXlaFieldNumber;
const int Node::kNumChildrenFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Node::Node()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::scc_info_Node.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.profiler.op_profile.Node)
}
Node::Node(const Node& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      children_(from.children_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  if (from.has_metrics()) {
    metrics_ = new ::tensorflow::profiler::op_profile::Metrics(*from.metrics_);
  } else {
    metrics_ = NULL;
  }
  num_children_ = from.num_children_;
  clear_has_contents();
  switch (from.contents_case()) {
    case kCategory: {
      mutable_category()->::tensorflow::profiler::op_profile::Node_InstructionCategory::MergeFrom(from.category());
      break;
    }
    case kXla: {
      mutable_xla()->::tensorflow::profiler::op_profile::Node_XLAInstruction::MergeFrom(from.xla());
      break;
    }
    case CONTENTS_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.profiler.op_profile.Node)
}

void Node::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&metrics_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&num_children_) -
      reinterpret_cast<char*>(&metrics_)) + sizeof(num_children_));
  clear_has_contents();
}

Node::~Node() {
  // @@protoc_insertion_point(destructor:tensorflow.profiler.op_profile.Node)
  SharedDtor();
}

void Node::SharedDtor() {
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete metrics_;
  if (has_contents()) {
    clear_contents();
  }
}

void Node::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Node::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Node& Node::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::scc_info_Node.base);
  return *internal_default_instance();
}


void Node::clear_contents() {
// @@protoc_insertion_point(one_of_clear_start:tensorflow.profiler.op_profile.Node)
  switch (contents_case()) {
    case kCategory: {
      delete contents_.category_;
      break;
    }
    case kXla: {
      delete contents_.xla_;
      break;
    }
    case CONTENTS_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = CONTENTS_NOT_SET;
}


void Node::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.profiler.op_profile.Node)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  children_.Clear();
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && metrics_ != NULL) {
    delete metrics_;
  }
  metrics_ = NULL;
  num_children_ = 0;
  clear_contents();
  _internal_metadata_.Clear();
}

bool Node::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.profiler.op_profile.Node)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.profiler.op_profile.Node.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.profiler.op_profile.Metrics metrics = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_metrics()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.profiler.op_profile.Node children = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_children()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.profiler.op_profile.Node.InstructionCategory category = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_category()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.profiler.op_profile.Node.XLAInstruction xla = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_xla()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 num_children = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(48u /* 48 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &num_children_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.profiler.op_profile.Node)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.profiler.op_profile.Node)
  return false;
#undef DO_
}

void Node::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.profiler.op_profile.Node)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.profiler.op_profile.Node.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // .tensorflow.profiler.op_profile.Metrics metrics = 2;
  if (this->has_metrics()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_metrics(), output);
  }

  // repeated .tensorflow.profiler.op_profile.Node children = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->children_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3,
      this->children(static_cast<int>(i)),
      output);
  }

  // .tensorflow.profiler.op_profile.Node.InstructionCategory category = 4;
  if (has_category()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->_internal_category(), output);
  }

  // .tensorflow.profiler.op_profile.Node.XLAInstruction xla = 5;
  if (has_xla()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, this->_internal_xla(), output);
  }

  // int32 num_children = 6;
  if (this->num_children() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(6, this->num_children(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.profiler.op_profile.Node)
}

::google::protobuf::uint8* Node::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.profiler.op_profile.Node)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.profiler.op_profile.Node.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // .tensorflow.profiler.op_profile.Metrics metrics = 2;
  if (this->has_metrics()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_metrics(), deterministic, target);
  }

  // repeated .tensorflow.profiler.op_profile.Node children = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->children_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->children(static_cast<int>(i)), deterministic, target);
  }

  // .tensorflow.profiler.op_profile.Node.InstructionCategory category = 4;
  if (has_category()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->_internal_category(), deterministic, target);
  }

  // .tensorflow.profiler.op_profile.Node.XLAInstruction xla = 5;
  if (has_xla()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        5, this->_internal_xla(), deterministic, target);
  }

  // int32 num_children = 6;
  if (this->num_children() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(6, this->num_children(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.profiler.op_profile.Node)
  return target;
}

size_t Node::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.profiler.op_profile.Node)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.profiler.op_profile.Node children = 3;
  {
    unsigned int count = static_cast<unsigned int>(this->children_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->children(static_cast<int>(i)));
    }
  }

  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // .tensorflow.profiler.op_profile.Metrics metrics = 2;
  if (this->has_metrics()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *metrics_);
  }

  // int32 num_children = 6;
  if (this->num_children() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->num_children());
  }

  switch (contents_case()) {
    // .tensorflow.profiler.op_profile.Node.InstructionCategory category = 4;
    case kCategory: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *contents_.category_);
      break;
    }
    // .tensorflow.profiler.op_profile.Node.XLAInstruction xla = 5;
    case kXla: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *contents_.xla_);
      break;
    }
    case CONTENTS_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Node::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.profiler.op_profile.Node)
  GOOGLE_DCHECK_NE(&from, this);
  const Node* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Node>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.profiler.op_profile.Node)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.profiler.op_profile.Node)
    MergeFrom(*source);
  }
}

void Node::MergeFrom(const Node& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.profiler.op_profile.Node)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  children_.MergeFrom(from.children_);
  if (from.name().size() > 0) {

    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  if (from.has_metrics()) {
    mutable_metrics()->::tensorflow::profiler::op_profile::Metrics::MergeFrom(from.metrics());
  }
  if (from.num_children() != 0) {
    set_num_children(from.num_children());
  }
  switch (from.contents_case()) {
    case kCategory: {
      mutable_category()->::tensorflow::profiler::op_profile::Node_InstructionCategory::MergeFrom(from.category());
      break;
    }
    case kXla: {
      mutable_xla()->::tensorflow::profiler::op_profile::Node_XLAInstruction::MergeFrom(from.xla());
      break;
    }
    case CONTENTS_NOT_SET: {
      break;
    }
  }
}

void Node::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.profiler.op_profile.Node)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Node::CopyFrom(const Node& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.profiler.op_profile.Node)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Node::IsInitialized() const {
  return true;
}

void Node::Swap(Node* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Node::InternalSwap(Node* other) {
  using std::swap;
  CastToBase(&children_)->InternalSwap(CastToBase(&other->children_));
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(metrics_, other->metrics_);
  swap(num_children_, other->num_children_);
  swap(contents_, other->contents_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Node::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Metrics::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Metrics::kTimeFieldNumber;
const int Metrics::kFlopsFieldNumber;
const int Metrics::kMemoryBandwidthFieldNumber;
const int Metrics::kRawTimeFieldNumber;
const int Metrics::kRawFlopsFieldNumber;
const int Metrics::kRawBytesAccessedFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Metrics::Metrics()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::scc_info_Metrics.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.profiler.op_profile.Metrics)
}
Metrics::Metrics(const Metrics& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&time_, &from.time_,
    static_cast<size_t>(reinterpret_cast<char*>(&raw_bytes_accessed_) -
    reinterpret_cast<char*>(&time_)) + sizeof(raw_bytes_accessed_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.profiler.op_profile.Metrics)
}

void Metrics::SharedCtor() {
  ::memset(&time_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&raw_bytes_accessed_) -
      reinterpret_cast<char*>(&time_)) + sizeof(raw_bytes_accessed_));
}

Metrics::~Metrics() {
  // @@protoc_insertion_point(destructor:tensorflow.profiler.op_profile.Metrics)
  SharedDtor();
}

void Metrics::SharedDtor() {
}

void Metrics::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Metrics::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Metrics& Metrics::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::scc_info_Metrics.base);
  return *internal_default_instance();
}


void Metrics::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.profiler.op_profile.Metrics)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&time_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&raw_bytes_accessed_) -
      reinterpret_cast<char*>(&time_)) + sizeof(raw_bytes_accessed_));
  _internal_metadata_.Clear();
}

bool Metrics::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.profiler.op_profile.Metrics)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // double time = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(9u /* 9 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &time_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double flops = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(17u /* 17 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &flops_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double memory_bandwidth = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(25u /* 25 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &memory_bandwidth_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double raw_time = 11;
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(89u /* 89 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &raw_time_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double raw_flops = 12;
      case 12: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(97u /* 97 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &raw_flops_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double raw_bytes_accessed = 13;
      case 13: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(105u /* 105 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &raw_bytes_accessed_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.profiler.op_profile.Metrics)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.profiler.op_profile.Metrics)
  return false;
#undef DO_
}

void Metrics::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.profiler.op_profile.Metrics)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double time = 1;
  if (this->time() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(1, this->time(), output);
  }

  // double flops = 2;
  if (this->flops() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(2, this->flops(), output);
  }

  // double memory_bandwidth = 3;
  if (this->memory_bandwidth() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(3, this->memory_bandwidth(), output);
  }

  // double raw_time = 11;
  if (this->raw_time() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(11, this->raw_time(), output);
  }

  // double raw_flops = 12;
  if (this->raw_flops() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(12, this->raw_flops(), output);
  }

  // double raw_bytes_accessed = 13;
  if (this->raw_bytes_accessed() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(13, this->raw_bytes_accessed(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.profiler.op_profile.Metrics)
}

::google::protobuf::uint8* Metrics::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.profiler.op_profile.Metrics)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double time = 1;
  if (this->time() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(1, this->time(), target);
  }

  // double flops = 2;
  if (this->flops() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(2, this->flops(), target);
  }

  // double memory_bandwidth = 3;
  if (this->memory_bandwidth() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(3, this->memory_bandwidth(), target);
  }

  // double raw_time = 11;
  if (this->raw_time() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(11, this->raw_time(), target);
  }

  // double raw_flops = 12;
  if (this->raw_flops() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(12, this->raw_flops(), target);
  }

  // double raw_bytes_accessed = 13;
  if (this->raw_bytes_accessed() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(13, this->raw_bytes_accessed(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.profiler.op_profile.Metrics)
  return target;
}

size_t Metrics::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.profiler.op_profile.Metrics)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // double time = 1;
  if (this->time() != 0) {
    total_size += 1 + 8;
  }

  // double flops = 2;
  if (this->flops() != 0) {
    total_size += 1 + 8;
  }

  // double memory_bandwidth = 3;
  if (this->memory_bandwidth() != 0) {
    total_size += 1 + 8;
  }

  // double raw_time = 11;
  if (this->raw_time() != 0) {
    total_size += 1 + 8;
  }

  // double raw_flops = 12;
  if (this->raw_flops() != 0) {
    total_size += 1 + 8;
  }

  // double raw_bytes_accessed = 13;
  if (this->raw_bytes_accessed() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Metrics::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.profiler.op_profile.Metrics)
  GOOGLE_DCHECK_NE(&from, this);
  const Metrics* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Metrics>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.profiler.op_profile.Metrics)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.profiler.op_profile.Metrics)
    MergeFrom(*source);
  }
}

void Metrics::MergeFrom(const Metrics& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.profiler.op_profile.Metrics)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.time() != 0) {
    set_time(from.time());
  }
  if (from.flops() != 0) {
    set_flops(from.flops());
  }
  if (from.memory_bandwidth() != 0) {
    set_memory_bandwidth(from.memory_bandwidth());
  }
  if (from.raw_time() != 0) {
    set_raw_time(from.raw_time());
  }
  if (from.raw_flops() != 0) {
    set_raw_flops(from.raw_flops());
  }
  if (from.raw_bytes_accessed() != 0) {
    set_raw_bytes_accessed(from.raw_bytes_accessed());
  }
}

void Metrics::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.profiler.op_profile.Metrics)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Metrics::CopyFrom(const Metrics& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.profiler.op_profile.Metrics)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Metrics::IsInitialized() const {
  return true;
}

void Metrics::Swap(Metrics* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Metrics::InternalSwap(Metrics* other) {
  using std::swap;
  swap(time_, other->time_);
  swap(flops_, other->flops_);
  swap(memory_bandwidth_, other->memory_bandwidth_);
  swap(raw_time_, other->raw_time_);
  swap(raw_flops_, other->raw_flops_);
  swap(raw_bytes_accessed_, other->raw_bytes_accessed_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Metrics::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace op_profile
}  // namespace profiler
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::profiler::op_profile::Profile* Arena::CreateMaybeMessage< ::tensorflow::profiler::op_profile::Profile >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::profiler::op_profile::Profile >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::profiler::op_profile::Node_InstructionCategory* Arena::CreateMaybeMessage< ::tensorflow::profiler::op_profile::Node_InstructionCategory >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::profiler::op_profile::Node_InstructionCategory >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis_Dimension* Arena::CreateMaybeMessage< ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis_Dimension >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis_Dimension >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis* Arena::CreateMaybeMessage< ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::profiler::op_profile::Node_XLAInstruction* Arena::CreateMaybeMessage< ::tensorflow::profiler::op_profile::Node_XLAInstruction >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::profiler::op_profile::Node_XLAInstruction >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::profiler::op_profile::Node* Arena::CreateMaybeMessage< ::tensorflow::profiler::op_profile::Node >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::profiler::op_profile::Node >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::profiler::op_profile::Metrics* Arena::CreateMaybeMessage< ::tensorflow::profiler::op_profile::Metrics >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::profiler::op_profile::Metrics >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

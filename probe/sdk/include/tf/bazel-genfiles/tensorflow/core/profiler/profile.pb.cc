// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/profiler/profile.proto

#include "tensorflow/core/profiler/profile.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_Function;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_Label;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_Line;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_Mapping;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_ValueType;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_Location;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_Sample;
}  // namespace protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto
namespace tensorflow {
namespace tfprof {
namespace pprof {
class ProfileDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Profile>
      _instance;
} _Profile_default_instance_;
class ValueTypeDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ValueType>
      _instance;
} _ValueType_default_instance_;
class SampleDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Sample>
      _instance;
} _Sample_default_instance_;
class LabelDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Label>
      _instance;
} _Label_default_instance_;
class MappingDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Mapping>
      _instance;
} _Mapping_default_instance_;
class LocationDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Location>
      _instance;
} _Location_default_instance_;
class LineDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Line>
      _instance;
} _Line_default_instance_;
class FunctionDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Function>
      _instance;
} _Function_default_instance_;
}  // namespace pprof
}  // namespace tfprof
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto {
static void InitDefaultsProfile() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::pprof::_Profile_default_instance_;
    new (ptr) ::tensorflow::tfprof::pprof::Profile();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tfprof::pprof::Profile::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<5> scc_info_Profile =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 5, InitDefaultsProfile}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::scc_info_ValueType.base,
      &protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::scc_info_Sample.base,
      &protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::scc_info_Mapping.base,
      &protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::scc_info_Location.base,
      &protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::scc_info_Function.base,}};

static void InitDefaultsValueType() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::pprof::_ValueType_default_instance_;
    new (ptr) ::tensorflow::tfprof::pprof::ValueType();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tfprof::pprof::ValueType::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_ValueType =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsValueType}, {}};

static void InitDefaultsSample() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::pprof::_Sample_default_instance_;
    new (ptr) ::tensorflow::tfprof::pprof::Sample();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tfprof::pprof::Sample::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_Sample =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsSample}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::scc_info_Label.base,}};

static void InitDefaultsLabel() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::pprof::_Label_default_instance_;
    new (ptr) ::tensorflow::tfprof::pprof::Label();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tfprof::pprof::Label::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_Label =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsLabel}, {}};

static void InitDefaultsMapping() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::pprof::_Mapping_default_instance_;
    new (ptr) ::tensorflow::tfprof::pprof::Mapping();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tfprof::pprof::Mapping::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_Mapping =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsMapping}, {}};

static void InitDefaultsLocation() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::pprof::_Location_default_instance_;
    new (ptr) ::tensorflow::tfprof::pprof::Location();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tfprof::pprof::Location::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_Location =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsLocation}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::scc_info_Line.base,}};

static void InitDefaultsLine() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::pprof::_Line_default_instance_;
    new (ptr) ::tensorflow::tfprof::pprof::Line();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tfprof::pprof::Line::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_Line =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsLine}, {}};

static void InitDefaultsFunction() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::pprof::_Function_default_instance_;
    new (ptr) ::tensorflow::tfprof::pprof::Function();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tfprof::pprof::Function::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_Function =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsFunction}, {}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_Profile.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ValueType.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Sample.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Label.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Mapping.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Location.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Line.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Function.base);
}

::google::protobuf::Metadata file_level_metadata[8];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Profile, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Profile, sample_type_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Profile, sample_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Profile, mapping_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Profile, location_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Profile, function_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Profile, string_table_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Profile, drop_frames_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Profile, keep_frames_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Profile, time_nanos_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Profile, duration_nanos_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Profile, period_type_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Profile, period_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Profile, comment_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Profile, default_sample_type_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::ValueType, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::ValueType, type_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::ValueType, unit_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Sample, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Sample, location_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Sample, value_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Sample, label_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Label, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Label, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Label, str_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Label, num_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Mapping, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Mapping, id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Mapping, memory_start_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Mapping, memory_limit_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Mapping, file_offset_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Mapping, filename_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Mapping, build_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Mapping, has_functions_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Mapping, has_filenames_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Mapping, has_line_numbers_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Mapping, has_inline_frames_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Location, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Location, id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Location, mapping_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Location, address_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Location, line_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Line, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Line, function_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Line, line_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Function, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Function, id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Function, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Function, system_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Function, filename_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::pprof::Function, start_line_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::tfprof::pprof::Profile)},
  { 19, -1, sizeof(::tensorflow::tfprof::pprof::ValueType)},
  { 26, -1, sizeof(::tensorflow::tfprof::pprof::Sample)},
  { 34, -1, sizeof(::tensorflow::tfprof::pprof::Label)},
  { 42, -1, sizeof(::tensorflow::tfprof::pprof::Mapping)},
  { 57, -1, sizeof(::tensorflow::tfprof::pprof::Location)},
  { 66, -1, sizeof(::tensorflow::tfprof::pprof::Line)},
  { 73, -1, sizeof(::tensorflow::tfprof::pprof::Function)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::pprof::_Profile_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::pprof::_ValueType_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::pprof::_Sample_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::pprof::_Label_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::pprof::_Mapping_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::pprof::_Location_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::pprof::_Line_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::pprof::_Function_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/profiler/profile.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 8);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n&tensorflow/core/profiler/profile.proto"
      "\022\027tensorflow.tfprof.pprof\"\363\003\n\007Profile\0227\n"
      "\013sample_type\030\001 \003(\0132\".tensorflow.tfprof.p"
      "prof.ValueType\022/\n\006sample\030\002 \003(\0132\037.tensorf"
      "low.tfprof.pprof.Sample\0221\n\007mapping\030\003 \003(\013"
      "2 .tensorflow.tfprof.pprof.Mapping\0223\n\010lo"
      "cation\030\004 \003(\0132!.tensorflow.tfprof.pprof.L"
      "ocation\0223\n\010function\030\005 \003(\0132!.tensorflow.t"
      "fprof.pprof.Function\022\024\n\014string_table\030\006 \003"
      "(\t\022\023\n\013drop_frames\030\007 \001(\003\022\023\n\013keep_frames\030\010"
      " \001(\003\022\022\n\ntime_nanos\030\t \001(\003\022\026\n\016duration_nan"
      "os\030\n \001(\003\0227\n\013period_type\030\013 \001(\0132\".tensorfl"
      "ow.tfprof.pprof.ValueType\022\016\n\006period\030\014 \001("
      "\003\022\017\n\007comment\030\r \003(\003\022\033\n\023default_sample_typ"
      "e\030\016 \001(\003\"\'\n\tValueType\022\014\n\004type\030\001 \001(\003\022\014\n\004un"
      "it\030\002 \001(\003\"[\n\006Sample\022\023\n\013location_id\030\001 \003(\004\022"
      "\r\n\005value\030\002 \003(\003\022-\n\005label\030\003 \003(\0132\036.tensorfl"
      "ow.tfprof.pprof.Label\".\n\005Label\022\013\n\003key\030\001 "
      "\001(\003\022\013\n\003str\030\002 \001(\003\022\013\n\003num\030\003 \001(\003\"\335\001\n\007Mappin"
      "g\022\n\n\002id\030\001 \001(\004\022\024\n\014memory_start\030\002 \001(\004\022\024\n\014m"
      "emory_limit\030\003 \001(\004\022\023\n\013file_offset\030\004 \001(\004\022\020"
      "\n\010filename\030\005 \001(\003\022\020\n\010build_id\030\006 \001(\003\022\025\n\rha"
      "s_functions\030\007 \001(\010\022\025\n\rhas_filenames\030\010 \001(\010"
      "\022\030\n\020has_line_numbers\030\t \001(\010\022\031\n\021has_inline"
      "_frames\030\n \001(\010\"h\n\010Location\022\n\n\002id\030\001 \001(\004\022\022\n"
      "\nmapping_id\030\002 \001(\004\022\017\n\007address\030\003 \001(\004\022+\n\004li"
      "ne\030\004 \003(\0132\035.tensorflow.tfprof.pprof.Line\""
      ")\n\004Line\022\023\n\013function_id\030\001 \001(\004\022\014\n\004line\030\002 \001"
      "(\003\"_\n\010Function\022\n\n\002id\030\001 \001(\004\022\014\n\004name\030\002 \001(\003"
      "\022\023\n\013system_name\030\003 \001(\003\022\020\n\010filename\030\004 \001(\003\022"
      "\022\n\nstart_line\030\005 \001(\003b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 1227);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/profiler/profile.proto", &protobuf_RegisterTypes);
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto
namespace tensorflow {
namespace tfprof {
namespace pprof {

// ===================================================================

void Profile::InitAsDefaultInstance() {
  ::tensorflow::tfprof::pprof::_Profile_default_instance_._instance.get_mutable()->period_type_ = const_cast< ::tensorflow::tfprof::pprof::ValueType*>(
      ::tensorflow::tfprof::pprof::ValueType::internal_default_instance());
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Profile::kSampleTypeFieldNumber;
const int Profile::kSampleFieldNumber;
const int Profile::kMappingFieldNumber;
const int Profile::kLocationFieldNumber;
const int Profile::kFunctionFieldNumber;
const int Profile::kStringTableFieldNumber;
const int Profile::kDropFramesFieldNumber;
const int Profile::kKeepFramesFieldNumber;
const int Profile::kTimeNanosFieldNumber;
const int Profile::kDurationNanosFieldNumber;
const int Profile::kPeriodTypeFieldNumber;
const int Profile::kPeriodFieldNumber;
const int Profile::kCommentFieldNumber;
const int Profile::kDefaultSampleTypeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Profile::Profile()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::scc_info_Profile.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tfprof.pprof.Profile)
}
Profile::Profile(const Profile& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      sample_type_(from.sample_type_),
      sample_(from.sample_),
      mapping_(from.mapping_),
      location_(from.location_),
      function_(from.function_),
      string_table_(from.string_table_),
      comment_(from.comment_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_period_type()) {
    period_type_ = new ::tensorflow::tfprof::pprof::ValueType(*from.period_type_);
  } else {
    period_type_ = NULL;
  }
  ::memcpy(&drop_frames_, &from.drop_frames_,
    static_cast<size_t>(reinterpret_cast<char*>(&default_sample_type_) -
    reinterpret_cast<char*>(&drop_frames_)) + sizeof(default_sample_type_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.tfprof.pprof.Profile)
}

void Profile::SharedCtor() {
  ::memset(&period_type_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&default_sample_type_) -
      reinterpret_cast<char*>(&period_type_)) + sizeof(default_sample_type_));
}

Profile::~Profile() {
  // @@protoc_insertion_point(destructor:tensorflow.tfprof.pprof.Profile)
  SharedDtor();
}

void Profile::SharedDtor() {
  if (this != internal_default_instance()) delete period_type_;
}

void Profile::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Profile::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Profile& Profile::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::scc_info_Profile.base);
  return *internal_default_instance();
}


void Profile::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tfprof.pprof.Profile)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  sample_type_.Clear();
  sample_.Clear();
  mapping_.Clear();
  location_.Clear();
  function_.Clear();
  string_table_.Clear();
  comment_.Clear();
  if (GetArenaNoVirtual() == NULL && period_type_ != NULL) {
    delete period_type_;
  }
  period_type_ = NULL;
  ::memset(&drop_frames_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&default_sample_type_) -
      reinterpret_cast<char*>(&drop_frames_)) + sizeof(default_sample_type_));
  _internal_metadata_.Clear();
}

bool Profile::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tfprof.pprof.Profile)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.tfprof.pprof.ValueType sample_type = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_sample_type()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.tfprof.pprof.Sample sample = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_sample()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.tfprof.pprof.Mapping mapping = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_mapping()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.tfprof.pprof.Location location = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_location()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.tfprof.pprof.Function function = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_function()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string string_table = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_string_table()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->string_table(this->string_table_size() - 1).data(),
            static_cast<int>(this->string_table(this->string_table_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.pprof.Profile.string_table"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 drop_frames = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(56u /* 56 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &drop_frames_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 keep_frames = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(64u /* 64 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &keep_frames_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 time_nanos = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(72u /* 72 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &time_nanos_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 duration_nanos = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(80u /* 80 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &duration_nanos_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.tfprof.pprof.ValueType period_type = 11;
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(90u /* 90 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_period_type()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 period = 12;
      case 12: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(96u /* 96 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &period_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated int64 comment = 13;
      case 13: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(106u /* 106 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_comment())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(104u /* 104 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 1, 106u, input, this->mutable_comment())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 default_sample_type = 14;
      case 14: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(112u /* 112 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &default_sample_type_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tfprof.pprof.Profile)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tfprof.pprof.Profile)
  return false;
#undef DO_
}

void Profile::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tfprof.pprof.Profile)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.tfprof.pprof.ValueType sample_type = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->sample_type_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->sample_type(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.tfprof.pprof.Sample sample = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->sample_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2,
      this->sample(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.tfprof.pprof.Mapping mapping = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->mapping_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3,
      this->mapping(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.tfprof.pprof.Location location = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->location_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4,
      this->location(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.tfprof.pprof.Function function = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->function_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5,
      this->function(static_cast<int>(i)),
      output);
  }

  // repeated string string_table = 6;
  for (int i = 0, n = this->string_table_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->string_table(i).data(), static_cast<int>(this->string_table(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.pprof.Profile.string_table");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      6, this->string_table(i), output);
  }

  // int64 drop_frames = 7;
  if (this->drop_frames() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(7, this->drop_frames(), output);
  }

  // int64 keep_frames = 8;
  if (this->keep_frames() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->keep_frames(), output);
  }

  // int64 time_nanos = 9;
  if (this->time_nanos() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->time_nanos(), output);
  }

  // int64 duration_nanos = 10;
  if (this->duration_nanos() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->duration_nanos(), output);
  }

  // .tensorflow.tfprof.pprof.ValueType period_type = 11;
  if (this->has_period_type()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      11, this->_internal_period_type(), output);
  }

  // int64 period = 12;
  if (this->period() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->period(), output);
  }

  // repeated int64 comment = 13;
  if (this->comment_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(13, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _comment_cached_byte_size_));
  }
  for (int i = 0, n = this->comment_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->comment(i), output);
  }

  // int64 default_sample_type = 14;
  if (this->default_sample_type() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->default_sample_type(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tfprof.pprof.Profile)
}

::google::protobuf::uint8* Profile::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tfprof.pprof.Profile)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.tfprof.pprof.ValueType sample_type = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->sample_type_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->sample_type(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.tfprof.pprof.Sample sample = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->sample_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->sample(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.tfprof.pprof.Mapping mapping = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->mapping_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->mapping(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.tfprof.pprof.Location location = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->location_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->location(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.tfprof.pprof.Function function = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->function_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        5, this->function(static_cast<int>(i)), deterministic, target);
  }

  // repeated string string_table = 6;
  for (int i = 0, n = this->string_table_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->string_table(i).data(), static_cast<int>(this->string_table(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.pprof.Profile.string_table");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(6, this->string_table(i), target);
  }

  // int64 drop_frames = 7;
  if (this->drop_frames() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(7, this->drop_frames(), target);
  }

  // int64 keep_frames = 8;
  if (this->keep_frames() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->keep_frames(), target);
  }

  // int64 time_nanos = 9;
  if (this->time_nanos() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->time_nanos(), target);
  }

  // int64 duration_nanos = 10;
  if (this->duration_nanos() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->duration_nanos(), target);
  }

  // .tensorflow.tfprof.pprof.ValueType period_type = 11;
  if (this->has_period_type()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        11, this->_internal_period_type(), deterministic, target);
  }

  // int64 period = 12;
  if (this->period() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->period(), target);
  }

  // repeated int64 comment = 13;
  if (this->comment_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      13,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _comment_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->comment_, target);
  }

  // int64 default_sample_type = 14;
  if (this->default_sample_type() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->default_sample_type(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tfprof.pprof.Profile)
  return target;
}

size_t Profile::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tfprof.pprof.Profile)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.tfprof.pprof.ValueType sample_type = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->sample_type_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->sample_type(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.tfprof.pprof.Sample sample = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->sample_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->sample(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.tfprof.pprof.Mapping mapping = 3;
  {
    unsigned int count = static_cast<unsigned int>(this->mapping_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->mapping(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.tfprof.pprof.Location location = 4;
  {
    unsigned int count = static_cast<unsigned int>(this->location_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->location(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.tfprof.pprof.Function function = 5;
  {
    unsigned int count = static_cast<unsigned int>(this->function_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->function(static_cast<int>(i)));
    }
  }

  // repeated string string_table = 6;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->string_table_size());
  for (int i = 0, n = this->string_table_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->string_table(i));
  }

  // repeated int64 comment = 13;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int64Size(this->comment_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _comment_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // .tensorflow.tfprof.pprof.ValueType period_type = 11;
  if (this->has_period_type()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *period_type_);
  }

  // int64 drop_frames = 7;
  if (this->drop_frames() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->drop_frames());
  }

  // int64 keep_frames = 8;
  if (this->keep_frames() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->keep_frames());
  }

  // int64 time_nanos = 9;
  if (this->time_nanos() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->time_nanos());
  }

  // int64 duration_nanos = 10;
  if (this->duration_nanos() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->duration_nanos());
  }

  // int64 period = 12;
  if (this->period() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->period());
  }

  // int64 default_sample_type = 14;
  if (this->default_sample_type() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->default_sample_type());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Profile::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tfprof.pprof.Profile)
  GOOGLE_DCHECK_NE(&from, this);
  const Profile* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Profile>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tfprof.pprof.Profile)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tfprof.pprof.Profile)
    MergeFrom(*source);
  }
}

void Profile::MergeFrom(const Profile& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tfprof.pprof.Profile)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  sample_type_.MergeFrom(from.sample_type_);
  sample_.MergeFrom(from.sample_);
  mapping_.MergeFrom(from.mapping_);
  location_.MergeFrom(from.location_);
  function_.MergeFrom(from.function_);
  string_table_.MergeFrom(from.string_table_);
  comment_.MergeFrom(from.comment_);
  if (from.has_period_type()) {
    mutable_period_type()->::tensorflow::tfprof::pprof::ValueType::MergeFrom(from.period_type());
  }
  if (from.drop_frames() != 0) {
    set_drop_frames(from.drop_frames());
  }
  if (from.keep_frames() != 0) {
    set_keep_frames(from.keep_frames());
  }
  if (from.time_nanos() != 0) {
    set_time_nanos(from.time_nanos());
  }
  if (from.duration_nanos() != 0) {
    set_duration_nanos(from.duration_nanos());
  }
  if (from.period() != 0) {
    set_period(from.period());
  }
  if (from.default_sample_type() != 0) {
    set_default_sample_type(from.default_sample_type());
  }
}

void Profile::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tfprof.pprof.Profile)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Profile::CopyFrom(const Profile& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tfprof.pprof.Profile)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Profile::IsInitialized() const {
  return true;
}

void Profile::Swap(Profile* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Profile::InternalSwap(Profile* other) {
  using std::swap;
  CastToBase(&sample_type_)->InternalSwap(CastToBase(&other->sample_type_));
  CastToBase(&sample_)->InternalSwap(CastToBase(&other->sample_));
  CastToBase(&mapping_)->InternalSwap(CastToBase(&other->mapping_));
  CastToBase(&location_)->InternalSwap(CastToBase(&other->location_));
  CastToBase(&function_)->InternalSwap(CastToBase(&other->function_));
  string_table_.InternalSwap(CastToBase(&other->string_table_));
  comment_.InternalSwap(&other->comment_);
  swap(period_type_, other->period_type_);
  swap(drop_frames_, other->drop_frames_);
  swap(keep_frames_, other->keep_frames_);
  swap(time_nanos_, other->time_nanos_);
  swap(duration_nanos_, other->duration_nanos_);
  swap(period_, other->period_);
  swap(default_sample_type_, other->default_sample_type_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Profile::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ValueType::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ValueType::kTypeFieldNumber;
const int ValueType::kUnitFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ValueType::ValueType()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::scc_info_ValueType.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tfprof.pprof.ValueType)
}
ValueType::ValueType(const ValueType& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&type_, &from.type_,
    static_cast<size_t>(reinterpret_cast<char*>(&unit_) -
    reinterpret_cast<char*>(&type_)) + sizeof(unit_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.tfprof.pprof.ValueType)
}

void ValueType::SharedCtor() {
  ::memset(&type_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&unit_) -
      reinterpret_cast<char*>(&type_)) + sizeof(unit_));
}

ValueType::~ValueType() {
  // @@protoc_insertion_point(destructor:tensorflow.tfprof.pprof.ValueType)
  SharedDtor();
}

void ValueType::SharedDtor() {
}

void ValueType::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ValueType::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ValueType& ValueType::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::scc_info_ValueType.base);
  return *internal_default_instance();
}


void ValueType::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tfprof.pprof.ValueType)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&type_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&unit_) -
      reinterpret_cast<char*>(&type_)) + sizeof(unit_));
  _internal_metadata_.Clear();
}

bool ValueType::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tfprof.pprof.ValueType)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 type = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &type_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 unit = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &unit_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tfprof.pprof.ValueType)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tfprof.pprof.ValueType)
  return false;
#undef DO_
}

void ValueType::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tfprof.pprof.ValueType)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 type = 1;
  if (this->type() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->type(), output);
  }

  // int64 unit = 2;
  if (this->unit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->unit(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tfprof.pprof.ValueType)
}

::google::protobuf::uint8* ValueType::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tfprof.pprof.ValueType)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 type = 1;
  if (this->type() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->type(), target);
  }

  // int64 unit = 2;
  if (this->unit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->unit(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tfprof.pprof.ValueType)
  return target;
}

size_t ValueType::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tfprof.pprof.ValueType)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int64 type = 1;
  if (this->type() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->type());
  }

  // int64 unit = 2;
  if (this->unit() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->unit());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ValueType::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tfprof.pprof.ValueType)
  GOOGLE_DCHECK_NE(&from, this);
  const ValueType* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ValueType>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tfprof.pprof.ValueType)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tfprof.pprof.ValueType)
    MergeFrom(*source);
  }
}

void ValueType::MergeFrom(const ValueType& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tfprof.pprof.ValueType)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.type() != 0) {
    set_type(from.type());
  }
  if (from.unit() != 0) {
    set_unit(from.unit());
  }
}

void ValueType::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tfprof.pprof.ValueType)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ValueType::CopyFrom(const ValueType& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tfprof.pprof.ValueType)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ValueType::IsInitialized() const {
  return true;
}

void ValueType::Swap(ValueType* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ValueType::InternalSwap(ValueType* other) {
  using std::swap;
  swap(type_, other->type_);
  swap(unit_, other->unit_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ValueType::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Sample::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Sample::kLocationIdFieldNumber;
const int Sample::kValueFieldNumber;
const int Sample::kLabelFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Sample::Sample()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::scc_info_Sample.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tfprof.pprof.Sample)
}
Sample::Sample(const Sample& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      location_id_(from.location_id_),
      value_(from.value_),
      label_(from.label_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.tfprof.pprof.Sample)
}

void Sample::SharedCtor() {
}

Sample::~Sample() {
  // @@protoc_insertion_point(destructor:tensorflow.tfprof.pprof.Sample)
  SharedDtor();
}

void Sample::SharedDtor() {
}

void Sample::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Sample::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Sample& Sample::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::scc_info_Sample.base);
  return *internal_default_instance();
}


void Sample::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tfprof.pprof.Sample)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  location_id_.Clear();
  value_.Clear();
  label_.Clear();
  _internal_metadata_.Clear();
}

bool Sample::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tfprof.pprof.Sample)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated uint64 location_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, this->mutable_location_id())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 1, 10u, input, this->mutable_location_id())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated int64 value = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_value())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 1, 18u, input, this->mutable_value())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.tfprof.pprof.Label label = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_label()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tfprof.pprof.Sample)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tfprof.pprof.Sample)
  return false;
#undef DO_
}

void Sample::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tfprof.pprof.Sample)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated uint64 location_id = 1;
  if (this->location_id_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(1, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _location_id_cached_byte_size_));
  }
  for (int i = 0, n = this->location_id_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64NoTag(
      this->location_id(i), output);
  }

  // repeated int64 value = 2;
  if (this->value_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(2, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _value_cached_byte_size_));
  }
  for (int i = 0, n = this->value_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->value(i), output);
  }

  // repeated .tensorflow.tfprof.pprof.Label label = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->label_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3,
      this->label(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tfprof.pprof.Sample)
}

::google::protobuf::uint8* Sample::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tfprof.pprof.Sample)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated uint64 location_id = 1;
  if (this->location_id_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      1,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _location_id_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteUInt64NoTagToArray(this->location_id_, target);
  }

  // repeated int64 value = 2;
  if (this->value_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      2,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _value_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->value_, target);
  }

  // repeated .tensorflow.tfprof.pprof.Label label = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->label_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->label(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tfprof.pprof.Sample)
  return target;
}

size_t Sample::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tfprof.pprof.Sample)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated uint64 location_id = 1;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      UInt64Size(this->location_id_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _location_id_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 value = 2;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int64Size(this->value_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _value_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated .tensorflow.tfprof.pprof.Label label = 3;
  {
    unsigned int count = static_cast<unsigned int>(this->label_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->label(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Sample::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tfprof.pprof.Sample)
  GOOGLE_DCHECK_NE(&from, this);
  const Sample* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Sample>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tfprof.pprof.Sample)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tfprof.pprof.Sample)
    MergeFrom(*source);
  }
}

void Sample::MergeFrom(const Sample& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tfprof.pprof.Sample)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  location_id_.MergeFrom(from.location_id_);
  value_.MergeFrom(from.value_);
  label_.MergeFrom(from.label_);
}

void Sample::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tfprof.pprof.Sample)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Sample::CopyFrom(const Sample& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tfprof.pprof.Sample)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Sample::IsInitialized() const {
  return true;
}

void Sample::Swap(Sample* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Sample::InternalSwap(Sample* other) {
  using std::swap;
  location_id_.InternalSwap(&other->location_id_);
  value_.InternalSwap(&other->value_);
  CastToBase(&label_)->InternalSwap(CastToBase(&other->label_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Sample::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Label::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Label::kKeyFieldNumber;
const int Label::kStrFieldNumber;
const int Label::kNumFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Label::Label()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::scc_info_Label.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tfprof.pprof.Label)
}
Label::Label(const Label& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&key_, &from.key_,
    static_cast<size_t>(reinterpret_cast<char*>(&num_) -
    reinterpret_cast<char*>(&key_)) + sizeof(num_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.tfprof.pprof.Label)
}

void Label::SharedCtor() {
  ::memset(&key_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&num_) -
      reinterpret_cast<char*>(&key_)) + sizeof(num_));
}

Label::~Label() {
  // @@protoc_insertion_point(destructor:tensorflow.tfprof.pprof.Label)
  SharedDtor();
}

void Label::SharedDtor() {
}

void Label::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Label::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Label& Label::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::scc_info_Label.base);
  return *internal_default_instance();
}


void Label::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tfprof.pprof.Label)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&key_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&num_) -
      reinterpret_cast<char*>(&key_)) + sizeof(num_));
  _internal_metadata_.Clear();
}

bool Label::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tfprof.pprof.Label)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 key = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &key_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 str = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &str_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 num = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &num_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tfprof.pprof.Label)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tfprof.pprof.Label)
  return false;
#undef DO_
}

void Label::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tfprof.pprof.Label)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 key = 1;
  if (this->key() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->key(), output);
  }

  // int64 str = 2;
  if (this->str() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->str(), output);
  }

  // int64 num = 3;
  if (this->num() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->num(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tfprof.pprof.Label)
}

::google::protobuf::uint8* Label::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tfprof.pprof.Label)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 key = 1;
  if (this->key() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->key(), target);
  }

  // int64 str = 2;
  if (this->str() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->str(), target);
  }

  // int64 num = 3;
  if (this->num() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->num(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tfprof.pprof.Label)
  return target;
}

size_t Label::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tfprof.pprof.Label)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int64 key = 1;
  if (this->key() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->key());
  }

  // int64 str = 2;
  if (this->str() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->str());
  }

  // int64 num = 3;
  if (this->num() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->num());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Label::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tfprof.pprof.Label)
  GOOGLE_DCHECK_NE(&from, this);
  const Label* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Label>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tfprof.pprof.Label)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tfprof.pprof.Label)
    MergeFrom(*source);
  }
}

void Label::MergeFrom(const Label& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tfprof.pprof.Label)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.key() != 0) {
    set_key(from.key());
  }
  if (from.str() != 0) {
    set_str(from.str());
  }
  if (from.num() != 0) {
    set_num(from.num());
  }
}

void Label::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tfprof.pprof.Label)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Label::CopyFrom(const Label& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tfprof.pprof.Label)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Label::IsInitialized() const {
  return true;
}

void Label::Swap(Label* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Label::InternalSwap(Label* other) {
  using std::swap;
  swap(key_, other->key_);
  swap(str_, other->str_);
  swap(num_, other->num_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Label::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Mapping::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Mapping::kIdFieldNumber;
const int Mapping::kMemoryStartFieldNumber;
const int Mapping::kMemoryLimitFieldNumber;
const int Mapping::kFileOffsetFieldNumber;
const int Mapping::kFilenameFieldNumber;
const int Mapping::kBuildIdFieldNumber;
const int Mapping::kHasFunctionsFieldNumber;
const int Mapping::kHasFilenamesFieldNumber;
const int Mapping::kHasLineNumbersFieldNumber;
const int Mapping::kHasInlineFramesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Mapping::Mapping()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::scc_info_Mapping.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tfprof.pprof.Mapping)
}
Mapping::Mapping(const Mapping& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&id_, &from.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&has_inline_frames_) -
    reinterpret_cast<char*>(&id_)) + sizeof(has_inline_frames_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.tfprof.pprof.Mapping)
}

void Mapping::SharedCtor() {
  ::memset(&id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&has_inline_frames_) -
      reinterpret_cast<char*>(&id_)) + sizeof(has_inline_frames_));
}

Mapping::~Mapping() {
  // @@protoc_insertion_point(destructor:tensorflow.tfprof.pprof.Mapping)
  SharedDtor();
}

void Mapping::SharedDtor() {
}

void Mapping::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Mapping::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Mapping& Mapping::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::scc_info_Mapping.base);
  return *internal_default_instance();
}


void Mapping::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tfprof.pprof.Mapping)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&has_inline_frames_) -
      reinterpret_cast<char*>(&id_)) + sizeof(has_inline_frames_));
  _internal_metadata_.Clear();
}

bool Mapping::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tfprof.pprof.Mapping)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint64 id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint64 memory_start = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &memory_start_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint64 memory_limit = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &memory_limit_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint64 file_offset = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &file_offset_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 filename = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &filename_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 build_id = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(48u /* 48 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &build_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool has_functions = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(56u /* 56 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &has_functions_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool has_filenames = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(64u /* 64 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &has_filenames_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool has_line_numbers = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(72u /* 72 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &has_line_numbers_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool has_inline_frames = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(80u /* 80 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &has_inline_frames_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tfprof.pprof.Mapping)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tfprof.pprof.Mapping)
  return false;
#undef DO_
}

void Mapping::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tfprof.pprof.Mapping)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 id = 1;
  if (this->id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(1, this->id(), output);
  }

  // uint64 memory_start = 2;
  if (this->memory_start() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->memory_start(), output);
  }

  // uint64 memory_limit = 3;
  if (this->memory_limit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(3, this->memory_limit(), output);
  }

  // uint64 file_offset = 4;
  if (this->file_offset() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(4, this->file_offset(), output);
  }

  // int64 filename = 5;
  if (this->filename() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(5, this->filename(), output);
  }

  // int64 build_id = 6;
  if (this->build_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(6, this->build_id(), output);
  }

  // bool has_functions = 7;
  if (this->has_functions() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(7, this->has_functions(), output);
  }

  // bool has_filenames = 8;
  if (this->has_filenames() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(8, this->has_filenames(), output);
  }

  // bool has_line_numbers = 9;
  if (this->has_line_numbers() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(9, this->has_line_numbers(), output);
  }

  // bool has_inline_frames = 10;
  if (this->has_inline_frames() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(10, this->has_inline_frames(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tfprof.pprof.Mapping)
}

::google::protobuf::uint8* Mapping::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tfprof.pprof.Mapping)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 id = 1;
  if (this->id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(1, this->id(), target);
  }

  // uint64 memory_start = 2;
  if (this->memory_start() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->memory_start(), target);
  }

  // uint64 memory_limit = 3;
  if (this->memory_limit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(3, this->memory_limit(), target);
  }

  // uint64 file_offset = 4;
  if (this->file_offset() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(4, this->file_offset(), target);
  }

  // int64 filename = 5;
  if (this->filename() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(5, this->filename(), target);
  }

  // int64 build_id = 6;
  if (this->build_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(6, this->build_id(), target);
  }

  // bool has_functions = 7;
  if (this->has_functions() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(7, this->has_functions(), target);
  }

  // bool has_filenames = 8;
  if (this->has_filenames() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(8, this->has_filenames(), target);
  }

  // bool has_line_numbers = 9;
  if (this->has_line_numbers() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(9, this->has_line_numbers(), target);
  }

  // bool has_inline_frames = 10;
  if (this->has_inline_frames() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(10, this->has_inline_frames(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tfprof.pprof.Mapping)
  return target;
}

size_t Mapping::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tfprof.pprof.Mapping)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // uint64 id = 1;
  if (this->id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->id());
  }

  // uint64 memory_start = 2;
  if (this->memory_start() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->memory_start());
  }

  // uint64 memory_limit = 3;
  if (this->memory_limit() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->memory_limit());
  }

  // uint64 file_offset = 4;
  if (this->file_offset() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->file_offset());
  }

  // int64 filename = 5;
  if (this->filename() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->filename());
  }

  // int64 build_id = 6;
  if (this->build_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->build_id());
  }

  // bool has_functions = 7;
  if (this->has_functions() != 0) {
    total_size += 1 + 1;
  }

  // bool has_filenames = 8;
  if (this->has_filenames() != 0) {
    total_size += 1 + 1;
  }

  // bool has_line_numbers = 9;
  if (this->has_line_numbers() != 0) {
    total_size += 1 + 1;
  }

  // bool has_inline_frames = 10;
  if (this->has_inline_frames() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Mapping::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tfprof.pprof.Mapping)
  GOOGLE_DCHECK_NE(&from, this);
  const Mapping* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Mapping>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tfprof.pprof.Mapping)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tfprof.pprof.Mapping)
    MergeFrom(*source);
  }
}

void Mapping::MergeFrom(const Mapping& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tfprof.pprof.Mapping)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.id() != 0) {
    set_id(from.id());
  }
  if (from.memory_start() != 0) {
    set_memory_start(from.memory_start());
  }
  if (from.memory_limit() != 0) {
    set_memory_limit(from.memory_limit());
  }
  if (from.file_offset() != 0) {
    set_file_offset(from.file_offset());
  }
  if (from.filename() != 0) {
    set_filename(from.filename());
  }
  if (from.build_id() != 0) {
    set_build_id(from.build_id());
  }
  if (from.has_functions() != 0) {
    set_has_functions(from.has_functions());
  }
  if (from.has_filenames() != 0) {
    set_has_filenames(from.has_filenames());
  }
  if (from.has_line_numbers() != 0) {
    set_has_line_numbers(from.has_line_numbers());
  }
  if (from.has_inline_frames() != 0) {
    set_has_inline_frames(from.has_inline_frames());
  }
}

void Mapping::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tfprof.pprof.Mapping)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Mapping::CopyFrom(const Mapping& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tfprof.pprof.Mapping)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Mapping::IsInitialized() const {
  return true;
}

void Mapping::Swap(Mapping* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Mapping::InternalSwap(Mapping* other) {
  using std::swap;
  swap(id_, other->id_);
  swap(memory_start_, other->memory_start_);
  swap(memory_limit_, other->memory_limit_);
  swap(file_offset_, other->file_offset_);
  swap(filename_, other->filename_);
  swap(build_id_, other->build_id_);
  swap(has_functions_, other->has_functions_);
  swap(has_filenames_, other->has_filenames_);
  swap(has_line_numbers_, other->has_line_numbers_);
  swap(has_inline_frames_, other->has_inline_frames_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Mapping::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Location::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Location::kIdFieldNumber;
const int Location::kMappingIdFieldNumber;
const int Location::kAddressFieldNumber;
const int Location::kLineFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Location::Location()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::scc_info_Location.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tfprof.pprof.Location)
}
Location::Location(const Location& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      line_(from.line_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&id_, &from.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&address_) -
    reinterpret_cast<char*>(&id_)) + sizeof(address_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.tfprof.pprof.Location)
}

void Location::SharedCtor() {
  ::memset(&id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&address_) -
      reinterpret_cast<char*>(&id_)) + sizeof(address_));
}

Location::~Location() {
  // @@protoc_insertion_point(destructor:tensorflow.tfprof.pprof.Location)
  SharedDtor();
}

void Location::SharedDtor() {
}

void Location::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Location::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Location& Location::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::scc_info_Location.base);
  return *internal_default_instance();
}


void Location::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tfprof.pprof.Location)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  line_.Clear();
  ::memset(&id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&address_) -
      reinterpret_cast<char*>(&id_)) + sizeof(address_));
  _internal_metadata_.Clear();
}

bool Location::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tfprof.pprof.Location)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint64 id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint64 mapping_id = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &mapping_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint64 address = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &address_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.tfprof.pprof.Line line = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_line()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tfprof.pprof.Location)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tfprof.pprof.Location)
  return false;
#undef DO_
}

void Location::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tfprof.pprof.Location)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 id = 1;
  if (this->id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(1, this->id(), output);
  }

  // uint64 mapping_id = 2;
  if (this->mapping_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->mapping_id(), output);
  }

  // uint64 address = 3;
  if (this->address() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(3, this->address(), output);
  }

  // repeated .tensorflow.tfprof.pprof.Line line = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->line_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4,
      this->line(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tfprof.pprof.Location)
}

::google::protobuf::uint8* Location::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tfprof.pprof.Location)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 id = 1;
  if (this->id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(1, this->id(), target);
  }

  // uint64 mapping_id = 2;
  if (this->mapping_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->mapping_id(), target);
  }

  // uint64 address = 3;
  if (this->address() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(3, this->address(), target);
  }

  // repeated .tensorflow.tfprof.pprof.Line line = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->line_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->line(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tfprof.pprof.Location)
  return target;
}

size_t Location::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tfprof.pprof.Location)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.tfprof.pprof.Line line = 4;
  {
    unsigned int count = static_cast<unsigned int>(this->line_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->line(static_cast<int>(i)));
    }
  }

  // uint64 id = 1;
  if (this->id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->id());
  }

  // uint64 mapping_id = 2;
  if (this->mapping_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->mapping_id());
  }

  // uint64 address = 3;
  if (this->address() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->address());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Location::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tfprof.pprof.Location)
  GOOGLE_DCHECK_NE(&from, this);
  const Location* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Location>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tfprof.pprof.Location)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tfprof.pprof.Location)
    MergeFrom(*source);
  }
}

void Location::MergeFrom(const Location& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tfprof.pprof.Location)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  line_.MergeFrom(from.line_);
  if (from.id() != 0) {
    set_id(from.id());
  }
  if (from.mapping_id() != 0) {
    set_mapping_id(from.mapping_id());
  }
  if (from.address() != 0) {
    set_address(from.address());
  }
}

void Location::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tfprof.pprof.Location)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Location::CopyFrom(const Location& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tfprof.pprof.Location)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Location::IsInitialized() const {
  return true;
}

void Location::Swap(Location* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Location::InternalSwap(Location* other) {
  using std::swap;
  CastToBase(&line_)->InternalSwap(CastToBase(&other->line_));
  swap(id_, other->id_);
  swap(mapping_id_, other->mapping_id_);
  swap(address_, other->address_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Location::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Line::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Line::kFunctionIdFieldNumber;
const int Line::kLineFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Line::Line()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::scc_info_Line.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tfprof.pprof.Line)
}
Line::Line(const Line& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&function_id_, &from.function_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&line_) -
    reinterpret_cast<char*>(&function_id_)) + sizeof(line_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.tfprof.pprof.Line)
}

void Line::SharedCtor() {
  ::memset(&function_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&line_) -
      reinterpret_cast<char*>(&function_id_)) + sizeof(line_));
}

Line::~Line() {
  // @@protoc_insertion_point(destructor:tensorflow.tfprof.pprof.Line)
  SharedDtor();
}

void Line::SharedDtor() {
}

void Line::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Line::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Line& Line::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::scc_info_Line.base);
  return *internal_default_instance();
}


void Line::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tfprof.pprof.Line)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&function_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&line_) -
      reinterpret_cast<char*>(&function_id_)) + sizeof(line_));
  _internal_metadata_.Clear();
}

bool Line::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tfprof.pprof.Line)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint64 function_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &function_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 line = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &line_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tfprof.pprof.Line)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tfprof.pprof.Line)
  return false;
#undef DO_
}

void Line::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tfprof.pprof.Line)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 function_id = 1;
  if (this->function_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(1, this->function_id(), output);
  }

  // int64 line = 2;
  if (this->line() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->line(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tfprof.pprof.Line)
}

::google::protobuf::uint8* Line::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tfprof.pprof.Line)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 function_id = 1;
  if (this->function_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(1, this->function_id(), target);
  }

  // int64 line = 2;
  if (this->line() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->line(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tfprof.pprof.Line)
  return target;
}

size_t Line::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tfprof.pprof.Line)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // uint64 function_id = 1;
  if (this->function_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->function_id());
  }

  // int64 line = 2;
  if (this->line() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->line());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Line::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tfprof.pprof.Line)
  GOOGLE_DCHECK_NE(&from, this);
  const Line* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Line>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tfprof.pprof.Line)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tfprof.pprof.Line)
    MergeFrom(*source);
  }
}

void Line::MergeFrom(const Line& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tfprof.pprof.Line)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.function_id() != 0) {
    set_function_id(from.function_id());
  }
  if (from.line() != 0) {
    set_line(from.line());
  }
}

void Line::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tfprof.pprof.Line)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Line::CopyFrom(const Line& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tfprof.pprof.Line)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Line::IsInitialized() const {
  return true;
}

void Line::Swap(Line* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Line::InternalSwap(Line* other) {
  using std::swap;
  swap(function_id_, other->function_id_);
  swap(line_, other->line_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Line::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Function::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Function::kIdFieldNumber;
const int Function::kNameFieldNumber;
const int Function::kSystemNameFieldNumber;
const int Function::kFilenameFieldNumber;
const int Function::kStartLineFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Function::Function()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::scc_info_Function.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tfprof.pprof.Function)
}
Function::Function(const Function& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&id_, &from.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&start_line_) -
    reinterpret_cast<char*>(&id_)) + sizeof(start_line_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.tfprof.pprof.Function)
}

void Function::SharedCtor() {
  ::memset(&id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&start_line_) -
      reinterpret_cast<char*>(&id_)) + sizeof(start_line_));
}

Function::~Function() {
  // @@protoc_insertion_point(destructor:tensorflow.tfprof.pprof.Function)
  SharedDtor();
}

void Function::SharedDtor() {
}

void Function::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Function::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Function& Function::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::scc_info_Function.base);
  return *internal_default_instance();
}


void Function::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tfprof.pprof.Function)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&start_line_) -
      reinterpret_cast<char*>(&id_)) + sizeof(start_line_));
  _internal_metadata_.Clear();
}

bool Function::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tfprof.pprof.Function)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint64 id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 name = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &name_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 system_name = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &system_name_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 filename = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &filename_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 start_line = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &start_line_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tfprof.pprof.Function)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tfprof.pprof.Function)
  return false;
#undef DO_
}

void Function::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tfprof.pprof.Function)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 id = 1;
  if (this->id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(1, this->id(), output);
  }

  // int64 name = 2;
  if (this->name() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->name(), output);
  }

  // int64 system_name = 3;
  if (this->system_name() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->system_name(), output);
  }

  // int64 filename = 4;
  if (this->filename() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->filename(), output);
  }

  // int64 start_line = 5;
  if (this->start_line() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(5, this->start_line(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tfprof.pprof.Function)
}

::google::protobuf::uint8* Function::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tfprof.pprof.Function)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 id = 1;
  if (this->id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(1, this->id(), target);
  }

  // int64 name = 2;
  if (this->name() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->name(), target);
  }

  // int64 system_name = 3;
  if (this->system_name() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->system_name(), target);
  }

  // int64 filename = 4;
  if (this->filename() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->filename(), target);
  }

  // int64 start_line = 5;
  if (this->start_line() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(5, this->start_line(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tfprof.pprof.Function)
  return target;
}

size_t Function::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tfprof.pprof.Function)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // uint64 id = 1;
  if (this->id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->id());
  }

  // int64 name = 2;
  if (this->name() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->name());
  }

  // int64 system_name = 3;
  if (this->system_name() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->system_name());
  }

  // int64 filename = 4;
  if (this->filename() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->filename());
  }

  // int64 start_line = 5;
  if (this->start_line() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->start_line());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Function::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tfprof.pprof.Function)
  GOOGLE_DCHECK_NE(&from, this);
  const Function* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Function>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tfprof.pprof.Function)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tfprof.pprof.Function)
    MergeFrom(*source);
  }
}

void Function::MergeFrom(const Function& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tfprof.pprof.Function)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.id() != 0) {
    set_id(from.id());
  }
  if (from.name() != 0) {
    set_name(from.name());
  }
  if (from.system_name() != 0) {
    set_system_name(from.system_name());
  }
  if (from.filename() != 0) {
    set_filename(from.filename());
  }
  if (from.start_line() != 0) {
    set_start_line(from.start_line());
  }
}

void Function::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tfprof.pprof.Function)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Function::CopyFrom(const Function& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tfprof.pprof.Function)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Function::IsInitialized() const {
  return true;
}

void Function::Swap(Function* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Function::InternalSwap(Function* other) {
  using std::swap;
  swap(id_, other->id_);
  swap(name_, other->name_);
  swap(system_name_, other->system_name_);
  swap(filename_, other->filename_);
  swap(start_line_, other->start_line_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Function::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2fprofile_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace pprof
}  // namespace tfprof
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::pprof::Profile* Arena::CreateMaybeMessage< ::tensorflow::tfprof::pprof::Profile >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::pprof::Profile >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::pprof::ValueType* Arena::CreateMaybeMessage< ::tensorflow::tfprof::pprof::ValueType >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::pprof::ValueType >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::pprof::Sample* Arena::CreateMaybeMessage< ::tensorflow::tfprof::pprof::Sample >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::pprof::Sample >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::pprof::Label* Arena::CreateMaybeMessage< ::tensorflow::tfprof::pprof::Label >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::pprof::Label >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::pprof::Mapping* Arena::CreateMaybeMessage< ::tensorflow::tfprof::pprof::Mapping >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::pprof::Mapping >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::pprof::Location* Arena::CreateMaybeMessage< ::tensorflow::tfprof::pprof::Location >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::pprof::Location >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::pprof::Line* Arena::CreateMaybeMessage< ::tensorflow::tfprof::pprof::Line >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::pprof::Line >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::pprof::Function* Arena::CreateMaybeMessage< ::tensorflow::tfprof::pprof::Function >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::pprof::Function >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/profiler/tfprof_options.proto

#include "tensorflow/core/profiler/tfprof_options.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_AdvisorOptionsProto_CheckerOption;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_AdvisorOptionsProto_CheckersEntry_DoNotUse;
}  // namespace protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto
namespace tensorflow {
namespace tfprof {
class OptionsProtoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<OptionsProto>
      _instance;
} _OptionsProto_default_instance_;
class AdvisorOptionsProto_CheckersEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<AdvisorOptionsProto_CheckersEntry_DoNotUse>
      _instance;
} _AdvisorOptionsProto_CheckersEntry_DoNotUse_default_instance_;
class AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse>
      _instance;
} _AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse_default_instance_;
class AdvisorOptionsProto_CheckerOptionDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<AdvisorOptionsProto_CheckerOption>
      _instance;
} _AdvisorOptionsProto_CheckerOption_default_instance_;
class AdvisorOptionsProtoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<AdvisorOptionsProto>
      _instance;
} _AdvisorOptionsProto_default_instance_;
}  // namespace tfprof
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto {
static void InitDefaultsOptionsProto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_OptionsProto_default_instance_;
    new (ptr) ::tensorflow::tfprof::OptionsProto();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tfprof::OptionsProto::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_OptionsProto =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsOptionsProto}, {}};

static void InitDefaultsAdvisorOptionsProto_CheckersEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_AdvisorOptionsProto_CheckersEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::tfprof::AdvisorOptionsProto_CheckersEntry_DoNotUse();
  }
  ::tensorflow::tfprof::AdvisorOptionsProto_CheckersEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_AdvisorOptionsProto_CheckersEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsAdvisorOptionsProto_CheckersEntry_DoNotUse}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto::scc_info_AdvisorOptionsProto_CheckerOption.base,}};

static void InitDefaultsAdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse();
  }
  ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsAdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse}, {}};

static void InitDefaultsAdvisorOptionsProto_CheckerOption() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_AdvisorOptionsProto_CheckerOption_default_instance_;
    new (ptr) ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_AdvisorOptionsProto_CheckerOption =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsAdvisorOptionsProto_CheckerOption}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto::scc_info_AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse.base,}};

static void InitDefaultsAdvisorOptionsProto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_AdvisorOptionsProto_default_instance_;
    new (ptr) ::tensorflow::tfprof::AdvisorOptionsProto();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tfprof::AdvisorOptionsProto::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_AdvisorOptionsProto =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsAdvisorOptionsProto}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto::scc_info_AdvisorOptionsProto_CheckersEntry_DoNotUse.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_OptionsProto.base);
  ::google::protobuf::internal::InitSCC(&scc_info_AdvisorOptionsProto_CheckersEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_AdvisorOptionsProto_CheckerOption.base);
  ::google::protobuf::internal::InitSCC(&scc_info_AdvisorOptionsProto.base);
}

::google::protobuf::Metadata file_level_metadata[5];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OptionsProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OptionsProto, max_depth_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OptionsProto, min_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OptionsProto, min_peak_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OptionsProto, min_residual_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OptionsProto, min_output_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OptionsProto, min_micros_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OptionsProto, min_accelerator_micros_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OptionsProto, min_cpu_micros_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OptionsProto, min_params_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OptionsProto, min_float_ops_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OptionsProto, min_occurrence_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OptionsProto, step_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OptionsProto, order_by_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OptionsProto, account_type_regexes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OptionsProto, start_name_regexes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OptionsProto, trim_name_regexes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OptionsProto, show_name_regexes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OptionsProto, hide_name_regexes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OptionsProto, account_displayed_op_only_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OptionsProto, select_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OptionsProto, output_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OptionsProto, dump_to_file_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::AdvisorOptionsProto_CheckersEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::AdvisorOptionsProto_CheckersEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::AdvisorOptionsProto_CheckersEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::AdvisorOptionsProto_CheckersEntry_DoNotUse, value_),
  0,
  1,
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption, options_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::AdvisorOptionsProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::AdvisorOptionsProto, checkers_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::tfprof::OptionsProto)},
  { 27, 34, sizeof(::tensorflow::tfprof::AdvisorOptionsProto_CheckersEntry_DoNotUse)},
  { 36, 43, sizeof(::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse)},
  { 45, -1, sizeof(::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption)},
  { 51, -1, sizeof(::tensorflow::tfprof::AdvisorOptionsProto)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_OptionsProto_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_AdvisorOptionsProto_CheckersEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_AdvisorOptionsProto_CheckerOption_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_AdvisorOptionsProto_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/profiler/tfprof_options.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 5);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n-tensorflow/core/profiler/tfprof_option"
      "s.proto\022\021tensorflow.tfprof\"\225\004\n\014OptionsPr"
      "oto\022\021\n\tmax_depth\030\001 \001(\003\022\021\n\tmin_bytes\030\002 \001("
      "\003\022\026\n\016min_peak_bytes\030\023 \001(\003\022\032\n\022min_residua"
      "l_bytes\030\024 \001(\003\022\030\n\020min_output_bytes\030\025 \001(\003\022"
      "\022\n\nmin_micros\030\003 \001(\003\022\036\n\026min_accelerator_m"
      "icros\030\026 \001(\003\022\026\n\016min_cpu_micros\030\027 \001(\003\022\022\n\nm"
      "in_params\030\004 \001(\003\022\025\n\rmin_float_ops\030\005 \001(\003\022\026"
      "\n\016min_occurrence\030\021 \001(\003\022\014\n\004step\030\022 \001(\003\022\020\n\010"
      "order_by\030\007 \001(\t\022\034\n\024account_type_regexes\030\010"
      " \003(\t\022\032\n\022start_name_regexes\030\t \003(\t\022\031\n\021trim"
      "_name_regexes\030\n \003(\t\022\031\n\021show_name_regexes"
      "\030\013 \003(\t\022\031\n\021hide_name_regexes\030\014 \003(\t\022!\n\031acc"
      "ount_displayed_op_only\030\r \001(\010\022\016\n\006select\030\016"
      " \003(\t\022\016\n\006output\030\017 \001(\t\022\024\n\014dump_to_file\030\020 \001"
      "(\t\"\332\002\n\023AdvisorOptionsProto\022F\n\010checkers\030\001"
      " \003(\01324.tensorflow.tfprof.AdvisorOptionsP"
      "roto.CheckersEntry\032e\n\rCheckersEntry\022\013\n\003k"
      "ey\030\001 \001(\t\022C\n\005value\030\002 \001(\01324.tensorflow.tfp"
      "rof.AdvisorOptionsProto.CheckerOption:\0028"
      "\001\032\223\001\n\rCheckerOption\022R\n\007options\030\001 \003(\0132A.t"
      "ensorflow.tfprof.AdvisorOptionsProto.Che"
      "ckerOption.OptionsEntry\032.\n\014OptionsEntry\022"
      "\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 959);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/profiler/tfprof_options.proto", &protobuf_RegisterTypes);
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto
namespace tensorflow {
namespace tfprof {

// ===================================================================

void OptionsProto::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int OptionsProto::kMaxDepthFieldNumber;
const int OptionsProto::kMinBytesFieldNumber;
const int OptionsProto::kMinPeakBytesFieldNumber;
const int OptionsProto::kMinResidualBytesFieldNumber;
const int OptionsProto::kMinOutputBytesFieldNumber;
const int OptionsProto::kMinMicrosFieldNumber;
const int OptionsProto::kMinAcceleratorMicrosFieldNumber;
const int OptionsProto::kMinCpuMicrosFieldNumber;
const int OptionsProto::kMinParamsFieldNumber;
const int OptionsProto::kMinFloatOpsFieldNumber;
const int OptionsProto::kMinOccurrenceFieldNumber;
const int OptionsProto::kStepFieldNumber;
const int OptionsProto::kOrderByFieldNumber;
const int OptionsProto::kAccountTypeRegexesFieldNumber;
const int OptionsProto::kStartNameRegexesFieldNumber;
const int OptionsProto::kTrimNameRegexesFieldNumber;
const int OptionsProto::kShowNameRegexesFieldNumber;
const int OptionsProto::kHideNameRegexesFieldNumber;
const int OptionsProto::kAccountDisplayedOpOnlyFieldNumber;
const int OptionsProto::kSelectFieldNumber;
const int OptionsProto::kOutputFieldNumber;
const int OptionsProto::kDumpToFileFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

OptionsProto::OptionsProto()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto::scc_info_OptionsProto.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tfprof.OptionsProto)
}
OptionsProto::OptionsProto(const OptionsProto& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      account_type_regexes_(from.account_type_regexes_),
      start_name_regexes_(from.start_name_regexes_),
      trim_name_regexes_(from.trim_name_regexes_),
      show_name_regexes_(from.show_name_regexes_),
      hide_name_regexes_(from.hide_name_regexes_),
      select_(from.select_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  order_by_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.order_by().size() > 0) {
    order_by_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.order_by_);
  }
  output_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.output().size() > 0) {
    output_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.output_);
  }
  dump_to_file_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.dump_to_file().size() > 0) {
    dump_to_file_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.dump_to_file_);
  }
  ::memcpy(&max_depth_, &from.max_depth_,
    static_cast<size_t>(reinterpret_cast<char*>(&account_displayed_op_only_) -
    reinterpret_cast<char*>(&max_depth_)) + sizeof(account_displayed_op_only_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.tfprof.OptionsProto)
}

void OptionsProto::SharedCtor() {
  order_by_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  output_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dump_to_file_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&max_depth_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&account_displayed_op_only_) -
      reinterpret_cast<char*>(&max_depth_)) + sizeof(account_displayed_op_only_));
}

OptionsProto::~OptionsProto() {
  // @@protoc_insertion_point(destructor:tensorflow.tfprof.OptionsProto)
  SharedDtor();
}

void OptionsProto::SharedDtor() {
  order_by_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  output_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dump_to_file_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void OptionsProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* OptionsProto::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const OptionsProto& OptionsProto::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto::scc_info_OptionsProto.base);
  return *internal_default_instance();
}


void OptionsProto::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tfprof.OptionsProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  account_type_regexes_.Clear();
  start_name_regexes_.Clear();
  trim_name_regexes_.Clear();
  show_name_regexes_.Clear();
  hide_name_regexes_.Clear();
  select_.Clear();
  order_by_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  output_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dump_to_file_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&max_depth_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&account_displayed_op_only_) -
      reinterpret_cast<char*>(&max_depth_)) + sizeof(account_displayed_op_only_));
  _internal_metadata_.Clear();
}

bool OptionsProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tfprof.OptionsProto)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(16383u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 max_depth = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &max_depth_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 min_bytes = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &min_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 min_micros = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &min_micros_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 min_params = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &min_params_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 min_float_ops = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &min_float_ops_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string order_by = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_order_by()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->order_by().data(), static_cast<int>(this->order_by().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.OptionsProto.order_by"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string account_type_regexes = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(66u /* 66 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_account_type_regexes()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->account_type_regexes(this->account_type_regexes_size() - 1).data(),
            static_cast<int>(this->account_type_regexes(this->account_type_regexes_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.OptionsProto.account_type_regexes"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string start_name_regexes = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(74u /* 74 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_start_name_regexes()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->start_name_regexes(this->start_name_regexes_size() - 1).data(),
            static_cast<int>(this->start_name_regexes(this->start_name_regexes_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.OptionsProto.start_name_regexes"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string trim_name_regexes = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(82u /* 82 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_trim_name_regexes()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->trim_name_regexes(this->trim_name_regexes_size() - 1).data(),
            static_cast<int>(this->trim_name_regexes(this->trim_name_regexes_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.OptionsProto.trim_name_regexes"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string show_name_regexes = 11;
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(90u /* 90 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_show_name_regexes()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->show_name_regexes(this->show_name_regexes_size() - 1).data(),
            static_cast<int>(this->show_name_regexes(this->show_name_regexes_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.OptionsProto.show_name_regexes"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string hide_name_regexes = 12;
      case 12: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(98u /* 98 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_hide_name_regexes()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->hide_name_regexes(this->hide_name_regexes_size() - 1).data(),
            static_cast<int>(this->hide_name_regexes(this->hide_name_regexes_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.OptionsProto.hide_name_regexes"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool account_displayed_op_only = 13;
      case 13: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(104u /* 104 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &account_displayed_op_only_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string select = 14;
      case 14: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(114u /* 114 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_select()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->select(this->select_size() - 1).data(),
            static_cast<int>(this->select(this->select_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.OptionsProto.select"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string output = 15;
      case 15: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(122u /* 122 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_output()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->output().data(), static_cast<int>(this->output().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.OptionsProto.output"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string dump_to_file = 16;
      case 16: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(130u /* 130 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_dump_to_file()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->dump_to_file().data(), static_cast<int>(this->dump_to_file().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.OptionsProto.dump_to_file"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 min_occurrence = 17;
      case 17: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(136u /* 136 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &min_occurrence_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 step = 18;
      case 18: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(144u /* 144 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &step_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 min_peak_bytes = 19;
      case 19: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(152u /* 152 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &min_peak_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 min_residual_bytes = 20;
      case 20: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(160u /* 160 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &min_residual_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 min_output_bytes = 21;
      case 21: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(168u /* 168 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &min_output_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 min_accelerator_micros = 22;
      case 22: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(176u /* 176 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &min_accelerator_micros_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 min_cpu_micros = 23;
      case 23: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(184u /* 184 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &min_cpu_micros_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tfprof.OptionsProto)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tfprof.OptionsProto)
  return false;
#undef DO_
}

void OptionsProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tfprof.OptionsProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 max_depth = 1;
  if (this->max_depth() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->max_depth(), output);
  }

  // int64 min_bytes = 2;
  if (this->min_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->min_bytes(), output);
  }

  // int64 min_micros = 3;
  if (this->min_micros() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->min_micros(), output);
  }

  // int64 min_params = 4;
  if (this->min_params() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->min_params(), output);
  }

  // int64 min_float_ops = 5;
  if (this->min_float_ops() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(5, this->min_float_ops(), output);
  }

  // string order_by = 7;
  if (this->order_by().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->order_by().data(), static_cast<int>(this->order_by().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.OptionsProto.order_by");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->order_by(), output);
  }

  // repeated string account_type_regexes = 8;
  for (int i = 0, n = this->account_type_regexes_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->account_type_regexes(i).data(), static_cast<int>(this->account_type_regexes(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.OptionsProto.account_type_regexes");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      8, this->account_type_regexes(i), output);
  }

  // repeated string start_name_regexes = 9;
  for (int i = 0, n = this->start_name_regexes_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->start_name_regexes(i).data(), static_cast<int>(this->start_name_regexes(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.OptionsProto.start_name_regexes");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      9, this->start_name_regexes(i), output);
  }

  // repeated string trim_name_regexes = 10;
  for (int i = 0, n = this->trim_name_regexes_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->trim_name_regexes(i).data(), static_cast<int>(this->trim_name_regexes(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.OptionsProto.trim_name_regexes");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      10, this->trim_name_regexes(i), output);
  }

  // repeated string show_name_regexes = 11;
  for (int i = 0, n = this->show_name_regexes_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->show_name_regexes(i).data(), static_cast<int>(this->show_name_regexes(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.OptionsProto.show_name_regexes");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      11, this->show_name_regexes(i), output);
  }

  // repeated string hide_name_regexes = 12;
  for (int i = 0, n = this->hide_name_regexes_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->hide_name_regexes(i).data(), static_cast<int>(this->hide_name_regexes(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.OptionsProto.hide_name_regexes");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      12, this->hide_name_regexes(i), output);
  }

  // bool account_displayed_op_only = 13;
  if (this->account_displayed_op_only() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(13, this->account_displayed_op_only(), output);
  }

  // repeated string select = 14;
  for (int i = 0, n = this->select_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->select(i).data(), static_cast<int>(this->select(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.OptionsProto.select");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      14, this->select(i), output);
  }

  // string output = 15;
  if (this->output().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->output().data(), static_cast<int>(this->output().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.OptionsProto.output");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      15, this->output(), output);
  }

  // string dump_to_file = 16;
  if (this->dump_to_file().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->dump_to_file().data(), static_cast<int>(this->dump_to_file().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.OptionsProto.dump_to_file");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      16, this->dump_to_file(), output);
  }

  // int64 min_occurrence = 17;
  if (this->min_occurrence() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(17, this->min_occurrence(), output);
  }

  // int64 step = 18;
  if (this->step() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(18, this->step(), output);
  }

  // int64 min_peak_bytes = 19;
  if (this->min_peak_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(19, this->min_peak_bytes(), output);
  }

  // int64 min_residual_bytes = 20;
  if (this->min_residual_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(20, this->min_residual_bytes(), output);
  }

  // int64 min_output_bytes = 21;
  if (this->min_output_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(21, this->min_output_bytes(), output);
  }

  // int64 min_accelerator_micros = 22;
  if (this->min_accelerator_micros() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(22, this->min_accelerator_micros(), output);
  }

  // int64 min_cpu_micros = 23;
  if (this->min_cpu_micros() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(23, this->min_cpu_micros(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tfprof.OptionsProto)
}

::google::protobuf::uint8* OptionsProto::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tfprof.OptionsProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 max_depth = 1;
  if (this->max_depth() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->max_depth(), target);
  }

  // int64 min_bytes = 2;
  if (this->min_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->min_bytes(), target);
  }

  // int64 min_micros = 3;
  if (this->min_micros() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->min_micros(), target);
  }

  // int64 min_params = 4;
  if (this->min_params() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->min_params(), target);
  }

  // int64 min_float_ops = 5;
  if (this->min_float_ops() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(5, this->min_float_ops(), target);
  }

  // string order_by = 7;
  if (this->order_by().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->order_by().data(), static_cast<int>(this->order_by().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.OptionsProto.order_by");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->order_by(), target);
  }

  // repeated string account_type_regexes = 8;
  for (int i = 0, n = this->account_type_regexes_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->account_type_regexes(i).data(), static_cast<int>(this->account_type_regexes(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.OptionsProto.account_type_regexes");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(8, this->account_type_regexes(i), target);
  }

  // repeated string start_name_regexes = 9;
  for (int i = 0, n = this->start_name_regexes_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->start_name_regexes(i).data(), static_cast<int>(this->start_name_regexes(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.OptionsProto.start_name_regexes");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(9, this->start_name_regexes(i), target);
  }

  // repeated string trim_name_regexes = 10;
  for (int i = 0, n = this->trim_name_regexes_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->trim_name_regexes(i).data(), static_cast<int>(this->trim_name_regexes(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.OptionsProto.trim_name_regexes");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(10, this->trim_name_regexes(i), target);
  }

  // repeated string show_name_regexes = 11;
  for (int i = 0, n = this->show_name_regexes_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->show_name_regexes(i).data(), static_cast<int>(this->show_name_regexes(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.OptionsProto.show_name_regexes");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(11, this->show_name_regexes(i), target);
  }

  // repeated string hide_name_regexes = 12;
  for (int i = 0, n = this->hide_name_regexes_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->hide_name_regexes(i).data(), static_cast<int>(this->hide_name_regexes(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.OptionsProto.hide_name_regexes");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(12, this->hide_name_regexes(i), target);
  }

  // bool account_displayed_op_only = 13;
  if (this->account_displayed_op_only() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(13, this->account_displayed_op_only(), target);
  }

  // repeated string select = 14;
  for (int i = 0, n = this->select_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->select(i).data(), static_cast<int>(this->select(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.OptionsProto.select");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(14, this->select(i), target);
  }

  // string output = 15;
  if (this->output().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->output().data(), static_cast<int>(this->output().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.OptionsProto.output");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        15, this->output(), target);
  }

  // string dump_to_file = 16;
  if (this->dump_to_file().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->dump_to_file().data(), static_cast<int>(this->dump_to_file().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.OptionsProto.dump_to_file");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        16, this->dump_to_file(), target);
  }

  // int64 min_occurrence = 17;
  if (this->min_occurrence() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(17, this->min_occurrence(), target);
  }

  // int64 step = 18;
  if (this->step() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(18, this->step(), target);
  }

  // int64 min_peak_bytes = 19;
  if (this->min_peak_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(19, this->min_peak_bytes(), target);
  }

  // int64 min_residual_bytes = 20;
  if (this->min_residual_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(20, this->min_residual_bytes(), target);
  }

  // int64 min_output_bytes = 21;
  if (this->min_output_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(21, this->min_output_bytes(), target);
  }

  // int64 min_accelerator_micros = 22;
  if (this->min_accelerator_micros() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(22, this->min_accelerator_micros(), target);
  }

  // int64 min_cpu_micros = 23;
  if (this->min_cpu_micros() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(23, this->min_cpu_micros(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tfprof.OptionsProto)
  return target;
}

size_t OptionsProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tfprof.OptionsProto)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated string account_type_regexes = 8;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->account_type_regexes_size());
  for (int i = 0, n = this->account_type_regexes_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->account_type_regexes(i));
  }

  // repeated string start_name_regexes = 9;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->start_name_regexes_size());
  for (int i = 0, n = this->start_name_regexes_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->start_name_regexes(i));
  }

  // repeated string trim_name_regexes = 10;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->trim_name_regexes_size());
  for (int i = 0, n = this->trim_name_regexes_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->trim_name_regexes(i));
  }

  // repeated string show_name_regexes = 11;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->show_name_regexes_size());
  for (int i = 0, n = this->show_name_regexes_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->show_name_regexes(i));
  }

  // repeated string hide_name_regexes = 12;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->hide_name_regexes_size());
  for (int i = 0, n = this->hide_name_regexes_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->hide_name_regexes(i));
  }

  // repeated string select = 14;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->select_size());
  for (int i = 0, n = this->select_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->select(i));
  }

  // string order_by = 7;
  if (this->order_by().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->order_by());
  }

  // string output = 15;
  if (this->output().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->output());
  }

  // string dump_to_file = 16;
  if (this->dump_to_file().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->dump_to_file());
  }

  // int64 max_depth = 1;
  if (this->max_depth() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->max_depth());
  }

  // int64 min_bytes = 2;
  if (this->min_bytes() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->min_bytes());
  }

  // int64 min_micros = 3;
  if (this->min_micros() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->min_micros());
  }

  // int64 min_params = 4;
  if (this->min_params() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->min_params());
  }

  // int64 min_float_ops = 5;
  if (this->min_float_ops() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->min_float_ops());
  }

  // int64 min_occurrence = 17;
  if (this->min_occurrence() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->min_occurrence());
  }

  // int64 step = 18;
  if (this->step() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->step());
  }

  // int64 min_peak_bytes = 19;
  if (this->min_peak_bytes() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->min_peak_bytes());
  }

  // int64 min_residual_bytes = 20;
  if (this->min_residual_bytes() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->min_residual_bytes());
  }

  // int64 min_output_bytes = 21;
  if (this->min_output_bytes() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->min_output_bytes());
  }

  // int64 min_accelerator_micros = 22;
  if (this->min_accelerator_micros() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->min_accelerator_micros());
  }

  // int64 min_cpu_micros = 23;
  if (this->min_cpu_micros() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->min_cpu_micros());
  }

  // bool account_displayed_op_only = 13;
  if (this->account_displayed_op_only() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void OptionsProto::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tfprof.OptionsProto)
  GOOGLE_DCHECK_NE(&from, this);
  const OptionsProto* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const OptionsProto>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tfprof.OptionsProto)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tfprof.OptionsProto)
    MergeFrom(*source);
  }
}

void OptionsProto::MergeFrom(const OptionsProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tfprof.OptionsProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  account_type_regexes_.MergeFrom(from.account_type_regexes_);
  start_name_regexes_.MergeFrom(from.start_name_regexes_);
  trim_name_regexes_.MergeFrom(from.trim_name_regexes_);
  show_name_regexes_.MergeFrom(from.show_name_regexes_);
  hide_name_regexes_.MergeFrom(from.hide_name_regexes_);
  select_.MergeFrom(from.select_);
  if (from.order_by().size() > 0) {

    order_by_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.order_by_);
  }
  if (from.output().size() > 0) {

    output_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.output_);
  }
  if (from.dump_to_file().size() > 0) {

    dump_to_file_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.dump_to_file_);
  }
  if (from.max_depth() != 0) {
    set_max_depth(from.max_depth());
  }
  if (from.min_bytes() != 0) {
    set_min_bytes(from.min_bytes());
  }
  if (from.min_micros() != 0) {
    set_min_micros(from.min_micros());
  }
  if (from.min_params() != 0) {
    set_min_params(from.min_params());
  }
  if (from.min_float_ops() != 0) {
    set_min_float_ops(from.min_float_ops());
  }
  if (from.min_occurrence() != 0) {
    set_min_occurrence(from.min_occurrence());
  }
  if (from.step() != 0) {
    set_step(from.step());
  }
  if (from.min_peak_bytes() != 0) {
    set_min_peak_bytes(from.min_peak_bytes());
  }
  if (from.min_residual_bytes() != 0) {
    set_min_residual_bytes(from.min_residual_bytes());
  }
  if (from.min_output_bytes() != 0) {
    set_min_output_bytes(from.min_output_bytes());
  }
  if (from.min_accelerator_micros() != 0) {
    set_min_accelerator_micros(from.min_accelerator_micros());
  }
  if (from.min_cpu_micros() != 0) {
    set_min_cpu_micros(from.min_cpu_micros());
  }
  if (from.account_displayed_op_only() != 0) {
    set_account_displayed_op_only(from.account_displayed_op_only());
  }
}

void OptionsProto::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tfprof.OptionsProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OptionsProto::CopyFrom(const OptionsProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tfprof.OptionsProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OptionsProto::IsInitialized() const {
  return true;
}

void OptionsProto::Swap(OptionsProto* other) {
  if (other == this) return;
  InternalSwap(other);
}
void OptionsProto::InternalSwap(OptionsProto* other) {
  using std::swap;
  account_type_regexes_.InternalSwap(CastToBase(&other->account_type_regexes_));
  start_name_regexes_.InternalSwap(CastToBase(&other->start_name_regexes_));
  trim_name_regexes_.InternalSwap(CastToBase(&other->trim_name_regexes_));
  show_name_regexes_.InternalSwap(CastToBase(&other->show_name_regexes_));
  hide_name_regexes_.InternalSwap(CastToBase(&other->hide_name_regexes_));
  select_.InternalSwap(CastToBase(&other->select_));
  order_by_.Swap(&other->order_by_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  output_.Swap(&other->output_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  dump_to_file_.Swap(&other->dump_to_file_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(max_depth_, other->max_depth_);
  swap(min_bytes_, other->min_bytes_);
  swap(min_micros_, other->min_micros_);
  swap(min_params_, other->min_params_);
  swap(min_float_ops_, other->min_float_ops_);
  swap(min_occurrence_, other->min_occurrence_);
  swap(step_, other->step_);
  swap(min_peak_bytes_, other->min_peak_bytes_);
  swap(min_residual_bytes_, other->min_residual_bytes_);
  swap(min_output_bytes_, other->min_output_bytes_);
  swap(min_accelerator_micros_, other->min_accelerator_micros_);
  swap(min_cpu_micros_, other->min_cpu_micros_);
  swap(account_displayed_op_only_, other->account_displayed_op_only_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata OptionsProto::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

AdvisorOptionsProto_CheckersEntry_DoNotUse::AdvisorOptionsProto_CheckersEntry_DoNotUse() {}
AdvisorOptionsProto_CheckersEntry_DoNotUse::AdvisorOptionsProto_CheckersEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void AdvisorOptionsProto_CheckersEntry_DoNotUse::MergeFrom(const AdvisorOptionsProto_CheckersEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata AdvisorOptionsProto_CheckersEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto::file_level_metadata[1];
}
void AdvisorOptionsProto_CheckersEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse::AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse() {}
AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse::AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse::MergeFrom(const AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto::file_level_metadata[2];
}
void AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void AdvisorOptionsProto_CheckerOption::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AdvisorOptionsProto_CheckerOption::kOptionsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AdvisorOptionsProto_CheckerOption::AdvisorOptionsProto_CheckerOption()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto::scc_info_AdvisorOptionsProto_CheckerOption.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption)
}
AdvisorOptionsProto_CheckerOption::AdvisorOptionsProto_CheckerOption(const AdvisorOptionsProto_CheckerOption& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  options_.MergeFrom(from.options_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption)
}

void AdvisorOptionsProto_CheckerOption::SharedCtor() {
}

AdvisorOptionsProto_CheckerOption::~AdvisorOptionsProto_CheckerOption() {
  // @@protoc_insertion_point(destructor:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption)
  SharedDtor();
}

void AdvisorOptionsProto_CheckerOption::SharedDtor() {
}

void AdvisorOptionsProto_CheckerOption::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* AdvisorOptionsProto_CheckerOption::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const AdvisorOptionsProto_CheckerOption& AdvisorOptionsProto_CheckerOption::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto::scc_info_AdvisorOptionsProto_CheckerOption.base);
  return *internal_default_instance();
}


void AdvisorOptionsProto_CheckerOption::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  options_.Clear();
  _internal_metadata_.Clear();
}

bool AdvisorOptionsProto_CheckerOption::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<string, string> options = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse,
              ::std::string, ::std::string,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              0 >,
            ::google::protobuf::Map< ::std::string, ::std::string > > parser(&options_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.AdvisorOptionsProto.CheckerOption.OptionsEntry.key"));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.value().data(), static_cast<int>(parser.value().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.AdvisorOptionsProto.CheckerOption.OptionsEntry.value"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption)
  return false;
#undef DO_
}

void AdvisorOptionsProto_CheckerOption::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, string> options = 1;
  if (!this->options().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.tfprof.AdvisorOptionsProto.CheckerOption.OptionsEntry.key");
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.tfprof.AdvisorOptionsProto.CheckerOption.OptionsEntry.value");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->options().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->options().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->options().begin();
          it != this->options().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(options_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->options().begin();
          it != this->options().end(); ++it) {
        entry.reset(options_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption)
}

::google::protobuf::uint8* AdvisorOptionsProto_CheckerOption::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, string> options = 1;
  if (!this->options().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.tfprof.AdvisorOptionsProto.CheckerOption.OptionsEntry.key");
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.tfprof.AdvisorOptionsProto.CheckerOption.OptionsEntry.value");
      }
    };

    if (deterministic &&
        this->options().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->options().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->options().begin();
          it != this->options().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(options_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->options().begin();
          it != this->options().end(); ++it) {
        entry.reset(options_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption)
  return target;
}

size_t AdvisorOptionsProto_CheckerOption::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // map<string, string> options = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->options_size());
  {
    ::std::unique_ptr<AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
        it = this->options().begin();
        it != this->options().end(); ++it) {
      entry.reset(options_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AdvisorOptionsProto_CheckerOption::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption)
  GOOGLE_DCHECK_NE(&from, this);
  const AdvisorOptionsProto_CheckerOption* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AdvisorOptionsProto_CheckerOption>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption)
    MergeFrom(*source);
  }
}

void AdvisorOptionsProto_CheckerOption::MergeFrom(const AdvisorOptionsProto_CheckerOption& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  options_.MergeFrom(from.options_);
}

void AdvisorOptionsProto_CheckerOption::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AdvisorOptionsProto_CheckerOption::CopyFrom(const AdvisorOptionsProto_CheckerOption& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AdvisorOptionsProto_CheckerOption::IsInitialized() const {
  return true;
}

void AdvisorOptionsProto_CheckerOption::Swap(AdvisorOptionsProto_CheckerOption* other) {
  if (other == this) return;
  InternalSwap(other);
}
void AdvisorOptionsProto_CheckerOption::InternalSwap(AdvisorOptionsProto_CheckerOption* other) {
  using std::swap;
  options_.Swap(&other->options_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata AdvisorOptionsProto_CheckerOption::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void AdvisorOptionsProto::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AdvisorOptionsProto::kCheckersFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AdvisorOptionsProto::AdvisorOptionsProto()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto::scc_info_AdvisorOptionsProto.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tfprof.AdvisorOptionsProto)
}
AdvisorOptionsProto::AdvisorOptionsProto(const AdvisorOptionsProto& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  checkers_.MergeFrom(from.checkers_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.tfprof.AdvisorOptionsProto)
}

void AdvisorOptionsProto::SharedCtor() {
}

AdvisorOptionsProto::~AdvisorOptionsProto() {
  // @@protoc_insertion_point(destructor:tensorflow.tfprof.AdvisorOptionsProto)
  SharedDtor();
}

void AdvisorOptionsProto::SharedDtor() {
}

void AdvisorOptionsProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* AdvisorOptionsProto::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const AdvisorOptionsProto& AdvisorOptionsProto::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto::scc_info_AdvisorOptionsProto.base);
  return *internal_default_instance();
}


void AdvisorOptionsProto::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tfprof.AdvisorOptionsProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  checkers_.Clear();
  _internal_metadata_.Clear();
}

bool AdvisorOptionsProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tfprof.AdvisorOptionsProto)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<string, .tensorflow.tfprof.AdvisorOptionsProto.CheckerOption> checkers = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          AdvisorOptionsProto_CheckersEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              AdvisorOptionsProto_CheckersEntry_DoNotUse,
              ::std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption > > parser(&checkers_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.AdvisorOptionsProto.CheckersEntry.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tfprof.AdvisorOptionsProto)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tfprof.AdvisorOptionsProto)
  return false;
#undef DO_
}

void AdvisorOptionsProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tfprof.AdvisorOptionsProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, .tensorflow.tfprof.AdvisorOptionsProto.CheckerOption> checkers = 1;
  if (!this->checkers().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.tfprof.AdvisorOptionsProto.CheckersEntry.key");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->checkers().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->checkers().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption >::const_iterator
          it = this->checkers().begin();
          it != this->checkers().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<AdvisorOptionsProto_CheckersEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(checkers_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<AdvisorOptionsProto_CheckersEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption >::const_iterator
          it = this->checkers().begin();
          it != this->checkers().end(); ++it) {
        entry.reset(checkers_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tfprof.AdvisorOptionsProto)
}

::google::protobuf::uint8* AdvisorOptionsProto::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tfprof.AdvisorOptionsProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, .tensorflow.tfprof.AdvisorOptionsProto.CheckerOption> checkers = 1;
  if (!this->checkers().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.tfprof.AdvisorOptionsProto.CheckersEntry.key");
      }
    };

    if (deterministic &&
        this->checkers().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->checkers().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption >::const_iterator
          it = this->checkers().begin();
          it != this->checkers().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<AdvisorOptionsProto_CheckersEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(checkers_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<AdvisorOptionsProto_CheckersEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption >::const_iterator
          it = this->checkers().begin();
          it != this->checkers().end(); ++it) {
        entry.reset(checkers_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tfprof.AdvisorOptionsProto)
  return target;
}

size_t AdvisorOptionsProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tfprof.AdvisorOptionsProto)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // map<string, .tensorflow.tfprof.AdvisorOptionsProto.CheckerOption> checkers = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->checkers_size());
  {
    ::std::unique_ptr<AdvisorOptionsProto_CheckersEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption >::const_iterator
        it = this->checkers().begin();
        it != this->checkers().end(); ++it) {
      entry.reset(checkers_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AdvisorOptionsProto::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tfprof.AdvisorOptionsProto)
  GOOGLE_DCHECK_NE(&from, this);
  const AdvisorOptionsProto* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AdvisorOptionsProto>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tfprof.AdvisorOptionsProto)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tfprof.AdvisorOptionsProto)
    MergeFrom(*source);
  }
}

void AdvisorOptionsProto::MergeFrom(const AdvisorOptionsProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tfprof.AdvisorOptionsProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  checkers_.MergeFrom(from.checkers_);
}

void AdvisorOptionsProto::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tfprof.AdvisorOptionsProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AdvisorOptionsProto::CopyFrom(const AdvisorOptionsProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tfprof.AdvisorOptionsProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AdvisorOptionsProto::IsInitialized() const {
  return true;
}

void AdvisorOptionsProto::Swap(AdvisorOptionsProto* other) {
  if (other == this) return;
  InternalSwap(other);
}
void AdvisorOptionsProto::InternalSwap(AdvisorOptionsProto* other) {
  using std::swap;
  checkers_.Swap(&other->checkers_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata AdvisorOptionsProto::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tfprof
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::OptionsProto* Arena::CreateMaybeMessage< ::tensorflow::tfprof::OptionsProto >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::OptionsProto >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::AdvisorOptionsProto_CheckersEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::tfprof::AdvisorOptionsProto_CheckersEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::AdvisorOptionsProto_CheckersEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption* Arena::CreateMaybeMessage< ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::AdvisorOptionsProto* Arena::CreateMaybeMessage< ::tensorflow::tfprof::AdvisorOptionsProto >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::AdvisorOptionsProto >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/profiler/tfprof_log.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/attr_value.pb.h"
#include "tensorflow/core/framework/step_stats.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto 

namespace protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[24];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto
namespace tensorflow {
namespace tfprof {
class CodeDef;
class CodeDefDefaultTypeInternal;
extern CodeDefDefaultTypeInternal _CodeDef_default_instance_;
class CodeDef_Trace;
class CodeDef_TraceDefaultTypeInternal;
extern CodeDef_TraceDefaultTypeInternal _CodeDef_Trace_default_instance_;
class ExecMemory;
class ExecMemoryDefaultTypeInternal;
extern ExecMemoryDefaultTypeInternal _ExecMemory_default_instance_;
class ExecMemory_OutputMemoryEntry_DoNotUse;
class ExecMemory_OutputMemoryEntry_DoNotUseDefaultTypeInternal;
extern ExecMemory_OutputMemoryEntry_DoNotUseDefaultTypeInternal _ExecMemory_OutputMemoryEntry_DoNotUse_default_instance_;
class ExecProfile;
class ExecProfileDefaultTypeInternal;
extern ExecProfileDefaultTypeInternal _ExecProfile_default_instance_;
class ExecProfile_AcceleratorExecsEntry_DoNotUse;
class ExecProfile_AcceleratorExecsEntry_DoNotUseDefaultTypeInternal;
extern ExecProfile_AcceleratorExecsEntry_DoNotUseDefaultTypeInternal _ExecProfile_AcceleratorExecsEntry_DoNotUse_default_instance_;
class ExecProfile_CpuExecsEntry_DoNotUse;
class ExecProfile_CpuExecsEntry_DoNotUseDefaultTypeInternal;
extern ExecProfile_CpuExecsEntry_DoNotUseDefaultTypeInternal _ExecProfile_CpuExecsEntry_DoNotUse_default_instance_;
class ExecTime;
class ExecTimeDefaultTypeInternal;
extern ExecTimeDefaultTypeInternal _ExecTime_default_instance_;
class Memory;
class MemoryDefaultTypeInternal;
extern MemoryDefaultTypeInternal _Memory_default_instance_;
class OpLogEntry;
class OpLogEntryDefaultTypeInternal;
extern OpLogEntryDefaultTypeInternal _OpLogEntry_default_instance_;
class OpLogProto;
class OpLogProtoDefaultTypeInternal;
extern OpLogProtoDefaultTypeInternal _OpLogProto_default_instance_;
class OpLogProto_IdToStringEntry_DoNotUse;
class OpLogProto_IdToStringEntry_DoNotUseDefaultTypeInternal;
extern OpLogProto_IdToStringEntry_DoNotUseDefaultTypeInternal _OpLogProto_IdToStringEntry_DoNotUse_default_instance_;
class ProfileNode;
class ProfileNodeDefaultTypeInternal;
extern ProfileNodeDefaultTypeInternal _ProfileNode_default_instance_;
class ProfileNode_AttrsEntry_DoNotUse;
class ProfileNode_AttrsEntry_DoNotUseDefaultTypeInternal;
extern ProfileNode_AttrsEntry_DoNotUseDefaultTypeInternal _ProfileNode_AttrsEntry_DoNotUse_default_instance_;
class ProfileNode_ExecsEntry_DoNotUse;
class ProfileNode_ExecsEntry_DoNotUseDefaultTypeInternal;
extern ProfileNode_ExecsEntry_DoNotUseDefaultTypeInternal _ProfileNode_ExecsEntry_DoNotUse_default_instance_;
class ProfileNode_InputShapesEntry_DoNotUse;
class ProfileNode_InputShapesEntry_DoNotUseDefaultTypeInternal;
extern ProfileNode_InputShapesEntry_DoNotUseDefaultTypeInternal _ProfileNode_InputShapesEntry_DoNotUse_default_instance_;
class ProfileNode_InputsEntry_DoNotUse;
class ProfileNode_InputsEntry_DoNotUseDefaultTypeInternal;
extern ProfileNode_InputsEntry_DoNotUseDefaultTypeInternal _ProfileNode_InputsEntry_DoNotUse_default_instance_;
class ProfileNode_OutputShapesEntry_DoNotUse;
class ProfileNode_OutputShapesEntry_DoNotUseDefaultTypeInternal;
extern ProfileNode_OutputShapesEntry_DoNotUseDefaultTypeInternal _ProfileNode_OutputShapesEntry_DoNotUse_default_instance_;
class ProfileNode_OutputsEntry_DoNotUse;
class ProfileNode_OutputsEntry_DoNotUseDefaultTypeInternal;
extern ProfileNode_OutputsEntry_DoNotUseDefaultTypeInternal _ProfileNode_OutputsEntry_DoNotUse_default_instance_;
class ProfileNode_SrcOutputIndexEntry_DoNotUse;
class ProfileNode_SrcOutputIndexEntry_DoNotUseDefaultTypeInternal;
extern ProfileNode_SrcOutputIndexEntry_DoNotUseDefaultTypeInternal _ProfileNode_SrcOutputIndexEntry_DoNotUse_default_instance_;
class ProfileProto;
class ProfileProtoDefaultTypeInternal;
extern ProfileProtoDefaultTypeInternal _ProfileProto_default_instance_;
class ProfileProto_IdToStringEntry_DoNotUse;
class ProfileProto_IdToStringEntry_DoNotUseDefaultTypeInternal;
extern ProfileProto_IdToStringEntry_DoNotUseDefaultTypeInternal _ProfileProto_IdToStringEntry_DoNotUse_default_instance_;
class ProfileProto_NodesEntry_DoNotUse;
class ProfileProto_NodesEntry_DoNotUseDefaultTypeInternal;
extern ProfileProto_NodesEntry_DoNotUseDefaultTypeInternal _ProfileProto_NodesEntry_DoNotUse_default_instance_;
class Tuple;
class TupleDefaultTypeInternal;
extern TupleDefaultTypeInternal _Tuple_default_instance_;
}  // namespace tfprof
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::tfprof::CodeDef* Arena::CreateMaybeMessage<::tensorflow::tfprof::CodeDef>(Arena*);
template<> ::tensorflow::tfprof::CodeDef_Trace* Arena::CreateMaybeMessage<::tensorflow::tfprof::CodeDef_Trace>(Arena*);
template<> ::tensorflow::tfprof::ExecMemory* Arena::CreateMaybeMessage<::tensorflow::tfprof::ExecMemory>(Arena*);
template<> ::tensorflow::tfprof::ExecMemory_OutputMemoryEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ExecMemory_OutputMemoryEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ExecProfile* Arena::CreateMaybeMessage<::tensorflow::tfprof::ExecProfile>(Arena*);
template<> ::tensorflow::tfprof::ExecProfile_AcceleratorExecsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ExecProfile_AcceleratorExecsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ExecProfile_CpuExecsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ExecProfile_CpuExecsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ExecTime* Arena::CreateMaybeMessage<::tensorflow::tfprof::ExecTime>(Arena*);
template<> ::tensorflow::tfprof::Memory* Arena::CreateMaybeMessage<::tensorflow::tfprof::Memory>(Arena*);
template<> ::tensorflow::tfprof::OpLogEntry* Arena::CreateMaybeMessage<::tensorflow::tfprof::OpLogEntry>(Arena*);
template<> ::tensorflow::tfprof::OpLogProto* Arena::CreateMaybeMessage<::tensorflow::tfprof::OpLogProto>(Arena*);
template<> ::tensorflow::tfprof::OpLogProto_IdToStringEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::OpLogProto_IdToStringEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ProfileNode* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileNode>(Arena*);
template<> ::tensorflow::tfprof::ProfileNode_AttrsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileNode_AttrsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ProfileNode_ExecsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileNode_ExecsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ProfileNode_InputShapesEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileNode_InputShapesEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ProfileNode_InputsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileNode_InputsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ProfileNode_OutputShapesEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileNode_OutputShapesEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ProfileNode_OutputsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileNode_OutputsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ProfileNode_SrcOutputIndexEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileNode_SrcOutputIndexEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ProfileProto* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileProto>(Arena*);
template<> ::tensorflow::tfprof::ProfileProto_IdToStringEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileProto_IdToStringEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ProfileProto_NodesEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileProto_NodesEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::Tuple* Arena::CreateMaybeMessage<::tensorflow::tfprof::Tuple>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {
namespace tfprof {

// ===================================================================

class CodeDef_Trace : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.CodeDef.Trace) */ {
 public:
  CodeDef_Trace();
  virtual ~CodeDef_Trace();

  CodeDef_Trace(const CodeDef_Trace& from);

  inline CodeDef_Trace& operator=(const CodeDef_Trace& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CodeDef_Trace(CodeDef_Trace&& from) noexcept
    : CodeDef_Trace() {
    *this = ::std::move(from);
  }

  inline CodeDef_Trace& operator=(CodeDef_Trace&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const CodeDef_Trace& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CodeDef_Trace* internal_default_instance() {
    return reinterpret_cast<const CodeDef_Trace*>(
               &_CodeDef_Trace_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void Swap(CodeDef_Trace* other);
  friend void swap(CodeDef_Trace& a, CodeDef_Trace& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CodeDef_Trace* New() const final {
    return CreateMaybeMessage<CodeDef_Trace>(NULL);
  }

  CodeDef_Trace* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CodeDef_Trace>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CodeDef_Trace& from);
  void MergeFrom(const CodeDef_Trace& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CodeDef_Trace* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string file = 1 [deprecated = true];
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void clear_file();
  GOOGLE_PROTOBUF_DEPRECATED_ATTR static const int kFileFieldNumber = 1;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR const ::std::string& file() const;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void set_file(const ::std::string& value);
  #if LANG_CXX11
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void set_file(::std::string&& value);
  #endif
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void set_file(const char* value);
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void set_file(const char* value, size_t size);
  GOOGLE_PROTOBUF_DEPRECATED_ATTR ::std::string* mutable_file();
  GOOGLE_PROTOBUF_DEPRECATED_ATTR ::std::string* release_file();
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void set_allocated_file(::std::string* file);

  // string function = 3 [deprecated = true];
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void clear_function();
  GOOGLE_PROTOBUF_DEPRECATED_ATTR static const int kFunctionFieldNumber = 3;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR const ::std::string& function() const;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void set_function(const ::std::string& value);
  #if LANG_CXX11
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void set_function(::std::string&& value);
  #endif
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void set_function(const char* value);
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void set_function(const char* value, size_t size);
  GOOGLE_PROTOBUF_DEPRECATED_ATTR ::std::string* mutable_function();
  GOOGLE_PROTOBUF_DEPRECATED_ATTR ::std::string* release_function();
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void set_allocated_function(::std::string* function);

  // string line = 4 [deprecated = true];
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void clear_line();
  GOOGLE_PROTOBUF_DEPRECATED_ATTR static const int kLineFieldNumber = 4;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR const ::std::string& line() const;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void set_line(const ::std::string& value);
  #if LANG_CXX11
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void set_line(::std::string&& value);
  #endif
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void set_line(const char* value);
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void set_line(const char* value, size_t size);
  GOOGLE_PROTOBUF_DEPRECATED_ATTR ::std::string* mutable_line();
  GOOGLE_PROTOBUF_DEPRECATED_ATTR ::std::string* release_line();
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void set_allocated_line(::std::string* line);

  // int32 lineno = 2;
  void clear_lineno();
  static const int kLinenoFieldNumber = 2;
  ::google::protobuf::int32 lineno() const;
  void set_lineno(::google::protobuf::int32 value);

  // int32 func_start_line = 5;
  void clear_func_start_line();
  static const int kFuncStartLineFieldNumber = 5;
  ::google::protobuf::int32 func_start_line() const;
  void set_func_start_line(::google::protobuf::int32 value);

  // int64 file_id = 6;
  void clear_file_id();
  static const int kFileIdFieldNumber = 6;
  ::google::protobuf::int64 file_id() const;
  void set_file_id(::google::protobuf::int64 value);

  // int64 function_id = 7;
  void clear_function_id();
  static const int kFunctionIdFieldNumber = 7;
  ::google::protobuf::int64 function_id() const;
  void set_function_id(::google::protobuf::int64 value);

  // int64 line_id = 8;
  void clear_line_id();
  static const int kLineIdFieldNumber = 8;
  ::google::protobuf::int64 line_id() const;
  void set_line_id(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.CodeDef.Trace)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr file_;
  ::google::protobuf::internal::ArenaStringPtr function_;
  ::google::protobuf::internal::ArenaStringPtr line_;
  ::google::protobuf::int32 lineno_;
  ::google::protobuf::int32 func_start_line_;
  ::google::protobuf::int64 file_id_;
  ::google::protobuf::int64 function_id_;
  ::google::protobuf::int64 line_id_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CodeDef : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.CodeDef) */ {
 public:
  CodeDef();
  virtual ~CodeDef();

  CodeDef(const CodeDef& from);

  inline CodeDef& operator=(const CodeDef& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CodeDef(CodeDef&& from) noexcept
    : CodeDef() {
    *this = ::std::move(from);
  }

  inline CodeDef& operator=(CodeDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const CodeDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CodeDef* internal_default_instance() {
    return reinterpret_cast<const CodeDef*>(
               &_CodeDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void Swap(CodeDef* other);
  friend void swap(CodeDef& a, CodeDef& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CodeDef* New() const final {
    return CreateMaybeMessage<CodeDef>(NULL);
  }

  CodeDef* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CodeDef>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CodeDef& from);
  void MergeFrom(const CodeDef& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CodeDef* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef CodeDef_Trace Trace;

  // accessors -------------------------------------------------------

  // repeated .tensorflow.tfprof.CodeDef.Trace traces = 1;
  int traces_size() const;
  void clear_traces();
  static const int kTracesFieldNumber = 1;
  ::tensorflow::tfprof::CodeDef_Trace* mutable_traces(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::CodeDef_Trace >*
      mutable_traces();
  const ::tensorflow::tfprof::CodeDef_Trace& traces(int index) const;
  ::tensorflow::tfprof::CodeDef_Trace* add_traces();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::CodeDef_Trace >&
      traces() const;

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.CodeDef)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::CodeDef_Trace > traces_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class OpLogEntry : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.OpLogEntry) */ {
 public:
  OpLogEntry();
  virtual ~OpLogEntry();

  OpLogEntry(const OpLogEntry& from);

  inline OpLogEntry& operator=(const OpLogEntry& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  OpLogEntry(OpLogEntry&& from) noexcept
    : OpLogEntry() {
    *this = ::std::move(from);
  }

  inline OpLogEntry& operator=(OpLogEntry&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const OpLogEntry& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OpLogEntry* internal_default_instance() {
    return reinterpret_cast<const OpLogEntry*>(
               &_OpLogEntry_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void Swap(OpLogEntry* other);
  friend void swap(OpLogEntry& a, OpLogEntry& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline OpLogEntry* New() const final {
    return CreateMaybeMessage<OpLogEntry>(NULL);
  }

  OpLogEntry* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<OpLogEntry>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const OpLogEntry& from);
  void MergeFrom(const OpLogEntry& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpLogEntry* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string types = 3;
  int types_size() const;
  void clear_types();
  static const int kTypesFieldNumber = 3;
  const ::std::string& types(int index) const;
  ::std::string* mutable_types(int index);
  void set_types(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_types(int index, ::std::string&& value);
  #endif
  void set_types(int index, const char* value);
  void set_types(int index, const char* value, size_t size);
  ::std::string* add_types();
  void add_types(const ::std::string& value);
  #if LANG_CXX11
  void add_types(::std::string&& value);
  #endif
  void add_types(const char* value);
  void add_types(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& types() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_types();

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // .tensorflow.tfprof.CodeDef code_def = 4;
  bool has_code_def() const;
  void clear_code_def();
  static const int kCodeDefFieldNumber = 4;
  private:
  const ::tensorflow::tfprof::CodeDef& _internal_code_def() const;
  public:
  const ::tensorflow::tfprof::CodeDef& code_def() const;
  ::tensorflow::tfprof::CodeDef* release_code_def();
  ::tensorflow::tfprof::CodeDef* mutable_code_def();
  void set_allocated_code_def(::tensorflow::tfprof::CodeDef* code_def);

  // int64 float_ops = 2;
  void clear_float_ops();
  static const int kFloatOpsFieldNumber = 2;
  ::google::protobuf::int64 float_ops() const;
  void set_float_ops(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.OpLogEntry)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::std::string> types_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::tensorflow::tfprof::CodeDef* code_def_;
  ::google::protobuf::int64 float_ops_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class OpLogProto_IdToStringEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<OpLogProto_IdToStringEntry_DoNotUse, 
    ::google::protobuf::int64, ::std::string,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<OpLogProto_IdToStringEntry_DoNotUse, 
    ::google::protobuf::int64, ::std::string,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    0 > SuperType;
  OpLogProto_IdToStringEntry_DoNotUse();
  OpLogProto_IdToStringEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const OpLogProto_IdToStringEntry_DoNotUse& other);
  static const OpLogProto_IdToStringEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const OpLogProto_IdToStringEntry_DoNotUse*>(&_OpLogProto_IdToStringEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class OpLogProto : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.OpLogProto) */ {
 public:
  OpLogProto();
  virtual ~OpLogProto();

  OpLogProto(const OpLogProto& from);

  inline OpLogProto& operator=(const OpLogProto& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  OpLogProto(OpLogProto&& from) noexcept
    : OpLogProto() {
    *this = ::std::move(from);
  }

  inline OpLogProto& operator=(OpLogProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const OpLogProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OpLogProto* internal_default_instance() {
    return reinterpret_cast<const OpLogProto*>(
               &_OpLogProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void Swap(OpLogProto* other);
  friend void swap(OpLogProto& a, OpLogProto& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline OpLogProto* New() const final {
    return CreateMaybeMessage<OpLogProto>(NULL);
  }

  OpLogProto* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<OpLogProto>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const OpLogProto& from);
  void MergeFrom(const OpLogProto& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpLogProto* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // repeated .tensorflow.tfprof.OpLogEntry log_entries = 1;
  int log_entries_size() const;
  void clear_log_entries();
  static const int kLogEntriesFieldNumber = 1;
  ::tensorflow::tfprof::OpLogEntry* mutable_log_entries(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::OpLogEntry >*
      mutable_log_entries();
  const ::tensorflow::tfprof::OpLogEntry& log_entries(int index) const;
  ::tensorflow::tfprof::OpLogEntry* add_log_entries();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::OpLogEntry >&
      log_entries() const;

  // map<int64, string> id_to_string = 2;
  int id_to_string_size() const;
  void clear_id_to_string();
  static const int kIdToStringFieldNumber = 2;
  const ::google::protobuf::Map< ::google::protobuf::int64, ::std::string >&
      id_to_string() const;
  ::google::protobuf::Map< ::google::protobuf::int64, ::std::string >*
      mutable_id_to_string();

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.OpLogProto)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::OpLogEntry > log_entries_;
  ::google::protobuf::internal::MapField<
      OpLogProto_IdToStringEntry_DoNotUse,
      ::google::protobuf::int64, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 > id_to_string_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ProfileProto_NodesEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<ProfileProto_NodesEntry_DoNotUse, 
    ::google::protobuf::int64, ::tensorflow::tfprof::ProfileNode,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<ProfileProto_NodesEntry_DoNotUse, 
    ::google::protobuf::int64, ::tensorflow::tfprof::ProfileNode,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  ProfileProto_NodesEntry_DoNotUse();
  ProfileProto_NodesEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const ProfileProto_NodesEntry_DoNotUse& other);
  static const ProfileProto_NodesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileProto_NodesEntry_DoNotUse*>(&_ProfileProto_NodesEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class ProfileProto_IdToStringEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<ProfileProto_IdToStringEntry_DoNotUse, 
    ::google::protobuf::int64, ::std::string,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<ProfileProto_IdToStringEntry_DoNotUse, 
    ::google::protobuf::int64, ::std::string,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    0 > SuperType;
  ProfileProto_IdToStringEntry_DoNotUse();
  ProfileProto_IdToStringEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const ProfileProto_IdToStringEntry_DoNotUse& other);
  static const ProfileProto_IdToStringEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileProto_IdToStringEntry_DoNotUse*>(&_ProfileProto_IdToStringEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class ProfileProto : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.ProfileProto) */ {
 public:
  ProfileProto();
  virtual ~ProfileProto();

  ProfileProto(const ProfileProto& from);

  inline ProfileProto& operator=(const ProfileProto& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ProfileProto(ProfileProto&& from) noexcept
    : ProfileProto() {
    *this = ::std::move(from);
  }

  inline ProfileProto& operator=(ProfileProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ProfileProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ProfileProto* internal_default_instance() {
    return reinterpret_cast<const ProfileProto*>(
               &_ProfileProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  void Swap(ProfileProto* other);
  friend void swap(ProfileProto& a, ProfileProto& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ProfileProto* New() const final {
    return CreateMaybeMessage<ProfileProto>(NULL);
  }

  ProfileProto* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ProfileProto>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ProfileProto& from);
  void MergeFrom(const ProfileProto& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProfileProto* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<int64, .tensorflow.tfprof.ProfileNode> nodes = 1;
  int nodes_size() const;
  void clear_nodes();
  static const int kNodesFieldNumber = 1;
  const ::google::protobuf::Map< ::google::protobuf::int64, ::tensorflow::tfprof::ProfileNode >&
      nodes() const;
  ::google::protobuf::Map< ::google::protobuf::int64, ::tensorflow::tfprof::ProfileNode >*
      mutable_nodes();

  // repeated int64 steps = 3;
  int steps_size() const;
  void clear_steps();
  static const int kStepsFieldNumber = 3;
  ::google::protobuf::int64 steps(int index) const;
  void set_steps(int index, ::google::protobuf::int64 value);
  void add_steps(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      steps() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_steps();

  // map<int64, string> id_to_string = 4;
  int id_to_string_size() const;
  void clear_id_to_string();
  static const int kIdToStringFieldNumber = 4;
  const ::google::protobuf::Map< ::google::protobuf::int64, ::std::string >&
      id_to_string() const;
  ::google::protobuf::Map< ::google::protobuf::int64, ::std::string >*
      mutable_id_to_string();

  // bool has_trace = 2;
  void clear_has_trace();
  static const int kHasTraceFieldNumber = 2;
  bool has_trace() const;
  void set_has_trace(bool value);

  // bool miss_accelerator_stream = 5;
  void clear_miss_accelerator_stream();
  static const int kMissAcceleratorStreamFieldNumber = 5;
  bool miss_accelerator_stream() const;
  void set_miss_accelerator_stream(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.ProfileProto)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::MapField<
      ProfileProto_NodesEntry_DoNotUse,
      ::google::protobuf::int64, ::tensorflow::tfprof::ProfileNode,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > nodes_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > steps_;
  mutable int _steps_cached_byte_size_;
  ::google::protobuf::internal::MapField<
      ProfileProto_IdToStringEntry_DoNotUse,
      ::google::protobuf::int64, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 > id_to_string_;
  bool has_trace_;
  bool miss_accelerator_stream_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ProfileNode_InputsEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<ProfileNode_InputsEntry_DoNotUse, 
    ::google::protobuf::int32, ::google::protobuf::int64,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<ProfileNode_InputsEntry_DoNotUse, 
    ::google::protobuf::int32, ::google::protobuf::int64,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
    0 > SuperType;
  ProfileNode_InputsEntry_DoNotUse();
  ProfileNode_InputsEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const ProfileNode_InputsEntry_DoNotUse& other);
  static const ProfileNode_InputsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileNode_InputsEntry_DoNotUse*>(&_ProfileNode_InputsEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class ProfileNode_InputShapesEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<ProfileNode_InputShapesEntry_DoNotUse, 
    ::google::protobuf::int32, ::tensorflow::tfprof::Tuple,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<ProfileNode_InputShapesEntry_DoNotUse, 
    ::google::protobuf::int32, ::tensorflow::tfprof::Tuple,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  ProfileNode_InputShapesEntry_DoNotUse();
  ProfileNode_InputShapesEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const ProfileNode_InputShapesEntry_DoNotUse& other);
  static const ProfileNode_InputShapesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileNode_InputShapesEntry_DoNotUse*>(&_ProfileNode_InputShapesEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class ProfileNode_OutputsEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<ProfileNode_OutputsEntry_DoNotUse, 
    ::google::protobuf::int32, ::google::protobuf::int64,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<ProfileNode_OutputsEntry_DoNotUse, 
    ::google::protobuf::int32, ::google::protobuf::int64,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
    0 > SuperType;
  ProfileNode_OutputsEntry_DoNotUse();
  ProfileNode_OutputsEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const ProfileNode_OutputsEntry_DoNotUse& other);
  static const ProfileNode_OutputsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileNode_OutputsEntry_DoNotUse*>(&_ProfileNode_OutputsEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class ProfileNode_OutputShapesEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<ProfileNode_OutputShapesEntry_DoNotUse, 
    ::google::protobuf::int32, ::tensorflow::tfprof::Tuple,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<ProfileNode_OutputShapesEntry_DoNotUse, 
    ::google::protobuf::int32, ::tensorflow::tfprof::Tuple,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  ProfileNode_OutputShapesEntry_DoNotUse();
  ProfileNode_OutputShapesEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const ProfileNode_OutputShapesEntry_DoNotUse& other);
  static const ProfileNode_OutputShapesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileNode_OutputShapesEntry_DoNotUse*>(&_ProfileNode_OutputShapesEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class ProfileNode_SrcOutputIndexEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<ProfileNode_SrcOutputIndexEntry_DoNotUse, 
    ::google::protobuf::int64, ::google::protobuf::int32,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<ProfileNode_SrcOutputIndexEntry_DoNotUse, 
    ::google::protobuf::int64, ::google::protobuf::int32,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
    0 > SuperType;
  ProfileNode_SrcOutputIndexEntry_DoNotUse();
  ProfileNode_SrcOutputIndexEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const ProfileNode_SrcOutputIndexEntry_DoNotUse& other);
  static const ProfileNode_SrcOutputIndexEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileNode_SrcOutputIndexEntry_DoNotUse*>(&_ProfileNode_SrcOutputIndexEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class ProfileNode_AttrsEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<ProfileNode_AttrsEntry_DoNotUse, 
    ::std::string, ::tensorflow::AttrValue,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<ProfileNode_AttrsEntry_DoNotUse, 
    ::std::string, ::tensorflow::AttrValue,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  ProfileNode_AttrsEntry_DoNotUse();
  ProfileNode_AttrsEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const ProfileNode_AttrsEntry_DoNotUse& other);
  static const ProfileNode_AttrsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileNode_AttrsEntry_DoNotUse*>(&_ProfileNode_AttrsEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class ProfileNode_ExecsEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<ProfileNode_ExecsEntry_DoNotUse, 
    ::google::protobuf::int64, ::tensorflow::tfprof::ExecProfile,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<ProfileNode_ExecsEntry_DoNotUse, 
    ::google::protobuf::int64, ::tensorflow::tfprof::ExecProfile,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  ProfileNode_ExecsEntry_DoNotUse();
  ProfileNode_ExecsEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const ProfileNode_ExecsEntry_DoNotUse& other);
  static const ProfileNode_ExecsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileNode_ExecsEntry_DoNotUse*>(&_ProfileNode_ExecsEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class ProfileNode : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.ProfileNode) */ {
 public:
  ProfileNode();
  virtual ~ProfileNode();

  ProfileNode(const ProfileNode& from);

  inline ProfileNode& operator=(const ProfileNode& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ProfileNode(ProfileNode&& from) noexcept
    : ProfileNode() {
    *this = ::std::move(from);
  }

  inline ProfileNode& operator=(ProfileNode&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ProfileNode& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ProfileNode* internal_default_instance() {
    return reinterpret_cast<const ProfileNode*>(
               &_ProfileNode_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  void Swap(ProfileNode* other);
  friend void swap(ProfileNode& a, ProfileNode& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ProfileNode* New() const final {
    return CreateMaybeMessage<ProfileNode>(NULL);
  }

  ProfileNode* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ProfileNode>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ProfileNode& from);
  void MergeFrom(const ProfileNode& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProfileNode* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<int32, int64> inputs = 2;
  int inputs_size() const;
  void clear_inputs();
  static const int kInputsFieldNumber = 2;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int64 >&
      inputs() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int64 >*
      mutable_inputs();

  // map<int32, int64> outputs = 3;
  int outputs_size() const;
  void clear_outputs();
  static const int kOutputsFieldNumber = 3;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int64 >&
      outputs() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int64 >*
      mutable_outputs();

  // repeated int64 shape = 4;
  int shape_size() const;
  void clear_shape();
  static const int kShapeFieldNumber = 4;
  ::google::protobuf::int64 shape(int index) const;
  void set_shape(int index, ::google::protobuf::int64 value);
  void add_shape(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      shape() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_shape();

  // repeated string op_types = 5;
  int op_types_size() const;
  void clear_op_types();
  static const int kOpTypesFieldNumber = 5;
  const ::std::string& op_types(int index) const;
  ::std::string* mutable_op_types(int index);
  void set_op_types(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_op_types(int index, ::std::string&& value);
  #endif
  void set_op_types(int index, const char* value);
  void set_op_types(int index, const char* value, size_t size);
  ::std::string* add_op_types();
  void add_op_types(const ::std::string& value);
  #if LANG_CXX11
  void add_op_types(::std::string&& value);
  #endif
  void add_op_types(const char* value);
  void add_op_types(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& op_types() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_op_types();

  // map<string, .tensorflow.AttrValue> attrs = 11;
  int attrs_size() const;
  void clear_attrs();
  static const int kAttrsFieldNumber = 11;
  const ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >&
      attrs() const;
  ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >*
      mutable_attrs();

  // map<int64, .tensorflow.tfprof.ExecProfile> execs = 12;
  int execs_size() const;
  void clear_execs();
  static const int kExecsFieldNumber = 12;
  const ::google::protobuf::Map< ::google::protobuf::int64, ::tensorflow::tfprof::ExecProfile >&
      execs() const;
  ::google::protobuf::Map< ::google::protobuf::int64, ::tensorflow::tfprof::ExecProfile >*
      mutable_execs();

  // map<int64, int32> src_output_index = 14;
  int src_output_index_size() const;
  void clear_src_output_index();
  static const int kSrcOutputIndexFieldNumber = 14;
  const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >&
      src_output_index() const;
  ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >*
      mutable_src_output_index();

  // map<int32, .tensorflow.tfprof.Tuple> output_shapes = 15;
  int output_shapes_size() const;
  void clear_output_shapes();
  static const int kOutputShapesFieldNumber = 15;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Tuple >&
      output_shapes() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Tuple >*
      mutable_output_shapes();

  // map<int32, .tensorflow.tfprof.Tuple> input_shapes = 16;
  int input_shapes_size() const;
  void clear_input_shapes();
  static const int kInputShapesFieldNumber = 16;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Tuple >&
      input_shapes() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Tuple >*
      mutable_input_shapes();

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // string canonical_device = 6;
  void clear_canonical_device();
  static const int kCanonicalDeviceFieldNumber = 6;
  const ::std::string& canonical_device() const;
  void set_canonical_device(const ::std::string& value);
  #if LANG_CXX11
  void set_canonical_device(::std::string&& value);
  #endif
  void set_canonical_device(const char* value);
  void set_canonical_device(const char* value, size_t size);
  ::std::string* mutable_canonical_device();
  ::std::string* release_canonical_device();
  void set_allocated_canonical_device(::std::string* canonical_device);

  // string host_device = 7;
  void clear_host_device();
  static const int kHostDeviceFieldNumber = 7;
  const ::std::string& host_device() const;
  void set_host_device(const ::std::string& value);
  #if LANG_CXX11
  void set_host_device(::std::string&& value);
  #endif
  void set_host_device(const char* value);
  void set_host_device(const char* value, size_t size);
  ::std::string* mutable_host_device();
  ::std::string* release_host_device();
  void set_allocated_host_device(::std::string* host_device);

  // string op = 9;
  void clear_op();
  static const int kOpFieldNumber = 9;
  const ::std::string& op() const;
  void set_op(const ::std::string& value);
  #if LANG_CXX11
  void set_op(::std::string&& value);
  #endif
  void set_op(const char* value);
  void set_op(const char* value, size_t size);
  ::std::string* mutable_op();
  ::std::string* release_op();
  void set_allocated_op(::std::string* op);

  // .tensorflow.tfprof.CodeDef trace = 10;
  bool has_trace() const;
  void clear_trace();
  static const int kTraceFieldNumber = 10;
  private:
  const ::tensorflow::tfprof::CodeDef& _internal_trace() const;
  public:
  const ::tensorflow::tfprof::CodeDef& trace() const;
  ::tensorflow::tfprof::CodeDef* release_trace();
  ::tensorflow::tfprof::CodeDef* mutable_trace();
  void set_allocated_trace(::tensorflow::tfprof::CodeDef* trace);

  // int64 float_ops = 8;
  void clear_float_ops();
  static const int kFloatOpsFieldNumber = 8;
  ::google::protobuf::int64 float_ops() const;
  void set_float_ops(::google::protobuf::int64 value);

  // int64 id = 13;
  void clear_id();
  static const int kIdFieldNumber = 13;
  ::google::protobuf::int64 id() const;
  void set_id(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.ProfileNode)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::MapField<
      ProfileNode_InputsEntry_DoNotUse,
      ::google::protobuf::int32, ::google::protobuf::int64,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      0 > inputs_;
  ::google::protobuf::internal::MapField<
      ProfileNode_OutputsEntry_DoNotUse,
      ::google::protobuf::int32, ::google::protobuf::int64,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      0 > outputs_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > shape_;
  mutable int _shape_cached_byte_size_;
  ::google::protobuf::RepeatedPtrField< ::std::string> op_types_;
  ::google::protobuf::internal::MapField<
      ProfileNode_AttrsEntry_DoNotUse,
      ::std::string, ::tensorflow::AttrValue,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > attrs_;
  ::google::protobuf::internal::MapField<
      ProfileNode_ExecsEntry_DoNotUse,
      ::google::protobuf::int64, ::tensorflow::tfprof::ExecProfile,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > execs_;
  ::google::protobuf::internal::MapField<
      ProfileNode_SrcOutputIndexEntry_DoNotUse,
      ::google::protobuf::int64, ::google::protobuf::int32,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      0 > src_output_index_;
  ::google::protobuf::internal::MapField<
      ProfileNode_OutputShapesEntry_DoNotUse,
      ::google::protobuf::int32, ::tensorflow::tfprof::Tuple,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > output_shapes_;
  ::google::protobuf::internal::MapField<
      ProfileNode_InputShapesEntry_DoNotUse,
      ::google::protobuf::int32, ::tensorflow::tfprof::Tuple,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > input_shapes_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::internal::ArenaStringPtr canonical_device_;
  ::google::protobuf::internal::ArenaStringPtr host_device_;
  ::google::protobuf::internal::ArenaStringPtr op_;
  ::tensorflow::tfprof::CodeDef* trace_;
  ::google::protobuf::int64 float_ops_;
  ::google::protobuf::int64 id_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ExecProfile_AcceleratorExecsEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<ExecProfile_AcceleratorExecsEntry_DoNotUse, 
    ::std::string, ::tensorflow::tfprof::ExecTime,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<ExecProfile_AcceleratorExecsEntry_DoNotUse, 
    ::std::string, ::tensorflow::tfprof::ExecTime,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  ExecProfile_AcceleratorExecsEntry_DoNotUse();
  ExecProfile_AcceleratorExecsEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const ExecProfile_AcceleratorExecsEntry_DoNotUse& other);
  static const ExecProfile_AcceleratorExecsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ExecProfile_AcceleratorExecsEntry_DoNotUse*>(&_ExecProfile_AcceleratorExecsEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class ExecProfile_CpuExecsEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<ExecProfile_CpuExecsEntry_DoNotUse, 
    ::std::string, ::tensorflow::tfprof::ExecTime,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<ExecProfile_CpuExecsEntry_DoNotUse, 
    ::std::string, ::tensorflow::tfprof::ExecTime,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  ExecProfile_CpuExecsEntry_DoNotUse();
  ExecProfile_CpuExecsEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const ExecProfile_CpuExecsEntry_DoNotUse& other);
  static const ExecProfile_CpuExecsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ExecProfile_CpuExecsEntry_DoNotUse*>(&_ExecProfile_CpuExecsEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class ExecProfile : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.ExecProfile) */ {
 public:
  ExecProfile();
  virtual ~ExecProfile();

  ExecProfile(const ExecProfile& from);

  inline ExecProfile& operator=(const ExecProfile& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ExecProfile(ExecProfile&& from) noexcept
    : ExecProfile() {
    *this = ::std::move(from);
  }

  inline ExecProfile& operator=(ExecProfile&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ExecProfile& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ExecProfile* internal_default_instance() {
    return reinterpret_cast<const ExecProfile*>(
               &_ExecProfile_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  void Swap(ExecProfile* other);
  friend void swap(ExecProfile& a, ExecProfile& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ExecProfile* New() const final {
    return CreateMaybeMessage<ExecProfile>(NULL);
  }

  ExecProfile* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ExecProfile>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ExecProfile& from);
  void MergeFrom(const ExecProfile& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExecProfile* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<string, .tensorflow.tfprof.ExecTime> accelerator_execs = 4;
  int accelerator_execs_size() const;
  void clear_accelerator_execs();
  static const int kAcceleratorExecsFieldNumber = 4;
  const ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::ExecTime >&
      accelerator_execs() const;
  ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::ExecTime >*
      mutable_accelerator_execs();

  // map<string, .tensorflow.tfprof.ExecTime> cpu_execs = 5;
  int cpu_execs_size() const;
  void clear_cpu_execs();
  static const int kCpuExecsFieldNumber = 5;
  const ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::ExecTime >&
      cpu_execs() const;
  ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::ExecTime >*
      mutable_cpu_execs();

  // repeated string devices = 6;
  int devices_size() const;
  void clear_devices();
  static const int kDevicesFieldNumber = 6;
  const ::std::string& devices(int index) const;
  ::std::string* mutable_devices(int index);
  void set_devices(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_devices(int index, ::std::string&& value);
  #endif
  void set_devices(int index, const char* value);
  void set_devices(int index, const char* value, size_t size);
  ::std::string* add_devices();
  void add_devices(const ::std::string& value);
  #if LANG_CXX11
  void add_devices(::std::string&& value);
  #endif
  void add_devices(const char* value);
  void add_devices(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& devices() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_devices();

  // repeated .tensorflow.tfprof.ExecMemory memory_execs = 7;
  int memory_execs_size() const;
  void clear_memory_execs();
  static const int kMemoryExecsFieldNumber = 7;
  ::tensorflow::tfprof::ExecMemory* mutable_memory_execs(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::ExecMemory >*
      mutable_memory_execs();
  const ::tensorflow::tfprof::ExecMemory& memory_execs(int index) const;
  ::tensorflow::tfprof::ExecMemory* add_memory_execs();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::ExecMemory >&
      memory_execs() const;

  // repeated .tensorflow.AllocationRecord allocations = 11;
  int allocations_size() const;
  void clear_allocations();
  static const int kAllocationsFieldNumber = 11;
  ::tensorflow::AllocationRecord* mutable_allocations(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::AllocationRecord >*
      mutable_allocations();
  const ::tensorflow::AllocationRecord& allocations(int index) const;
  ::tensorflow::AllocationRecord* add_allocations();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::AllocationRecord >&
      allocations() const;

  // int64 run_count = 1;
  void clear_run_count();
  static const int kRunCountFieldNumber = 1;
  ::google::protobuf::int64 run_count() const;
  void set_run_count(::google::protobuf::int64 value);

  // int64 all_start_micros = 2;
  void clear_all_start_micros();
  static const int kAllStartMicrosFieldNumber = 2;
  ::google::protobuf::int64 all_start_micros() const;
  void set_all_start_micros(::google::protobuf::int64 value);

  // int64 latest_end_micros = 3;
  void clear_latest_end_micros();
  static const int kLatestEndMicrosFieldNumber = 3;
  ::google::protobuf::int64 latest_end_micros() const;
  void set_latest_end_micros(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.ExecProfile)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::MapField<
      ExecProfile_AcceleratorExecsEntry_DoNotUse,
      ::std::string, ::tensorflow::tfprof::ExecTime,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > accelerator_execs_;
  ::google::protobuf::internal::MapField<
      ExecProfile_CpuExecsEntry_DoNotUse,
      ::std::string, ::tensorflow::tfprof::ExecTime,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > cpu_execs_;
  ::google::protobuf::RepeatedPtrField< ::std::string> devices_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::ExecMemory > memory_execs_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::AllocationRecord > allocations_;
  ::google::protobuf::int64 run_count_;
  ::google::protobuf::int64 all_start_micros_;
  ::google::protobuf::int64 latest_end_micros_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ExecTime : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.ExecTime) */ {
 public:
  ExecTime();
  virtual ~ExecTime();

  ExecTime(const ExecTime& from);

  inline ExecTime& operator=(const ExecTime& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ExecTime(ExecTime&& from) noexcept
    : ExecTime() {
    *this = ::std::move(from);
  }

  inline ExecTime& operator=(ExecTime&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ExecTime& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ExecTime* internal_default_instance() {
    return reinterpret_cast<const ExecTime*>(
               &_ExecTime_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  void Swap(ExecTime* other);
  friend void swap(ExecTime& a, ExecTime& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ExecTime* New() const final {
    return CreateMaybeMessage<ExecTime>(NULL);
  }

  ExecTime* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ExecTime>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ExecTime& from);
  void MergeFrom(const ExecTime& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExecTime* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.tfprof.Tuple times = 1;
  int times_size() const;
  void clear_times();
  static const int kTimesFieldNumber = 1;
  ::tensorflow::tfprof::Tuple* mutable_times(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::Tuple >*
      mutable_times();
  const ::tensorflow::tfprof::Tuple& times(int index) const;
  ::tensorflow::tfprof::Tuple* add_times();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::Tuple >&
      times() const;

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.ExecTime)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::Tuple > times_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ExecMemory_OutputMemoryEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<ExecMemory_OutputMemoryEntry_DoNotUse, 
    ::google::protobuf::int32, ::tensorflow::tfprof::Memory,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<ExecMemory_OutputMemoryEntry_DoNotUse, 
    ::google::protobuf::int32, ::tensorflow::tfprof::Memory,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  ExecMemory_OutputMemoryEntry_DoNotUse();
  ExecMemory_OutputMemoryEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const ExecMemory_OutputMemoryEntry_DoNotUse& other);
  static const ExecMemory_OutputMemoryEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ExecMemory_OutputMemoryEntry_DoNotUse*>(&_ExecMemory_OutputMemoryEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class ExecMemory : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.ExecMemory) */ {
 public:
  ExecMemory();
  virtual ~ExecMemory();

  ExecMemory(const ExecMemory& from);

  inline ExecMemory& operator=(const ExecMemory& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ExecMemory(ExecMemory&& from) noexcept
    : ExecMemory() {
    *this = ::std::move(from);
  }

  inline ExecMemory& operator=(ExecMemory&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ExecMemory& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ExecMemory* internal_default_instance() {
    return reinterpret_cast<const ExecMemory*>(
               &_ExecMemory_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  void Swap(ExecMemory* other);
  friend void swap(ExecMemory& a, ExecMemory& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ExecMemory* New() const final {
    return CreateMaybeMessage<ExecMemory>(NULL);
  }

  ExecMemory* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ExecMemory>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ExecMemory& from);
  void MergeFrom(const ExecMemory& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExecMemory* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<int32, .tensorflow.tfprof.Memory> output_memory = 11;
  int output_memory_size() const;
  void clear_output_memory();
  static const int kOutputMemoryFieldNumber = 11;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Memory >&
      output_memory() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Memory >*
      mutable_output_memory();

  // int64 memory_micros = 1;
  void clear_memory_micros();
  static const int kMemoryMicrosFieldNumber = 1;
  ::google::protobuf::int64 memory_micros() const;
  void set_memory_micros(::google::protobuf::int64 value);

  // int64 host_temp_bytes = 2;
  void clear_host_temp_bytes();
  static const int kHostTempBytesFieldNumber = 2;
  ::google::protobuf::int64 host_temp_bytes() const;
  void set_host_temp_bytes(::google::protobuf::int64 value);

  // int64 host_persistent_bytes = 3;
  void clear_host_persistent_bytes();
  static const int kHostPersistentBytesFieldNumber = 3;
  ::google::protobuf::int64 host_persistent_bytes() const;
  void set_host_persistent_bytes(::google::protobuf::int64 value);

  // int64 accelerator_temp_bytes = 4;
  void clear_accelerator_temp_bytes();
  static const int kAcceleratorTempBytesFieldNumber = 4;
  ::google::protobuf::int64 accelerator_temp_bytes() const;
  void set_accelerator_temp_bytes(::google::protobuf::int64 value);

  // int64 accelerator_persistent_bytes = 5;
  void clear_accelerator_persistent_bytes();
  static const int kAcceleratorPersistentBytesFieldNumber = 5;
  ::google::protobuf::int64 accelerator_persistent_bytes() const;
  void set_accelerator_persistent_bytes(::google::protobuf::int64 value);

  // int64 requested_bytes = 6;
  void clear_requested_bytes();
  static const int kRequestedBytesFieldNumber = 6;
  ::google::protobuf::int64 requested_bytes() const;
  void set_requested_bytes(::google::protobuf::int64 value);

  // int64 peak_bytes = 7;
  void clear_peak_bytes();
  static const int kPeakBytesFieldNumber = 7;
  ::google::protobuf::int64 peak_bytes() const;
  void set_peak_bytes(::google::protobuf::int64 value);

  // int64 residual_bytes = 8;
  void clear_residual_bytes();
  static const int kResidualBytesFieldNumber = 8;
  ::google::protobuf::int64 residual_bytes() const;
  void set_residual_bytes(::google::protobuf::int64 value);

  // int64 output_bytes = 9;
  void clear_output_bytes();
  static const int kOutputBytesFieldNumber = 9;
  ::google::protobuf::int64 output_bytes() const;
  void set_output_bytes(::google::protobuf::int64 value);

  // int64 allocator_bytes_in_use = 10;
  void clear_allocator_bytes_in_use();
  static const int kAllocatorBytesInUseFieldNumber = 10;
  ::google::protobuf::int64 allocator_bytes_in_use() const;
  void set_allocator_bytes_in_use(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.ExecMemory)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::MapField<
      ExecMemory_OutputMemoryEntry_DoNotUse,
      ::google::protobuf::int32, ::tensorflow::tfprof::Memory,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > output_memory_;
  ::google::protobuf::int64 memory_micros_;
  ::google::protobuf::int64 host_temp_bytes_;
  ::google::protobuf::int64 host_persistent_bytes_;
  ::google::protobuf::int64 accelerator_temp_bytes_;
  ::google::protobuf::int64 accelerator_persistent_bytes_;
  ::google::protobuf::int64 requested_bytes_;
  ::google::protobuf::int64 peak_bytes_;
  ::google::protobuf::int64 residual_bytes_;
  ::google::protobuf::int64 output_bytes_;
  ::google::protobuf::int64 allocator_bytes_in_use_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Tuple : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.Tuple) */ {
 public:
  Tuple();
  virtual ~Tuple();

  Tuple(const Tuple& from);

  inline Tuple& operator=(const Tuple& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Tuple(Tuple&& from) noexcept
    : Tuple() {
    *this = ::std::move(from);
  }

  inline Tuple& operator=(Tuple&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Tuple& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Tuple* internal_default_instance() {
    return reinterpret_cast<const Tuple*>(
               &_Tuple_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    22;

  void Swap(Tuple* other);
  friend void swap(Tuple& a, Tuple& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Tuple* New() const final {
    return CreateMaybeMessage<Tuple>(NULL);
  }

  Tuple* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Tuple>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Tuple& from);
  void MergeFrom(const Tuple& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Tuple* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int64 int64_values = 1;
  int int64_values_size() const;
  void clear_int64_values();
  static const int kInt64ValuesFieldNumber = 1;
  ::google::protobuf::int64 int64_values(int index) const;
  void set_int64_values(int index, ::google::protobuf::int64 value);
  void add_int64_values(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      int64_values() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_int64_values();

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.Tuple)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > int64_values_;
  mutable int _int64_values_cached_byte_size_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Memory : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.Memory) */ {
 public:
  Memory();
  virtual ~Memory();

  Memory(const Memory& from);

  inline Memory& operator=(const Memory& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Memory(Memory&& from) noexcept
    : Memory() {
    *this = ::std::move(from);
  }

  inline Memory& operator=(Memory&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Memory& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Memory* internal_default_instance() {
    return reinterpret_cast<const Memory*>(
               &_Memory_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    23;

  void Swap(Memory* other);
  friend void swap(Memory& a, Memory& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Memory* New() const final {
    return CreateMaybeMessage<Memory>(NULL);
  }

  Memory* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Memory>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Memory& from);
  void MergeFrom(const Memory& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Memory* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 bytes = 1;
  void clear_bytes();
  static const int kBytesFieldNumber = 1;
  ::google::protobuf::int64 bytes() const;
  void set_bytes(::google::protobuf::int64 value);

  // uint64 ptr = 2;
  void clear_ptr();
  static const int kPtrFieldNumber = 2;
  ::google::protobuf::uint64 ptr() const;
  void set_ptr(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.Memory)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int64 bytes_;
  ::google::protobuf::uint64 ptr_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CodeDef_Trace

// string file = 1 [deprecated = true];
inline void CodeDef_Trace::clear_file() {
  file_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& CodeDef_Trace::file() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.CodeDef.Trace.file)
  return file_.GetNoArena();
}
inline void CodeDef_Trace::set_file(const ::std::string& value) {
  
  file_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.CodeDef.Trace.file)
}
#if LANG_CXX11
inline void CodeDef_Trace::set_file(::std::string&& value) {
  
  file_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tfprof.CodeDef.Trace.file)
}
#endif
inline void CodeDef_Trace::set_file(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  file_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.CodeDef.Trace.file)
}
inline void CodeDef_Trace::set_file(const char* value, size_t size) {
  
  file_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.CodeDef.Trace.file)
}
inline ::std::string* CodeDef_Trace::mutable_file() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.CodeDef.Trace.file)
  return file_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* CodeDef_Trace::release_file() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.CodeDef.Trace.file)
  
  return file_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void CodeDef_Trace::set_allocated_file(::std::string* file) {
  if (file != NULL) {
    
  } else {
    
  }
  file_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), file);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.CodeDef.Trace.file)
}

// int64 file_id = 6;
inline void CodeDef_Trace::clear_file_id() {
  file_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CodeDef_Trace::file_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.CodeDef.Trace.file_id)
  return file_id_;
}
inline void CodeDef_Trace::set_file_id(::google::protobuf::int64 value) {
  
  file_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.CodeDef.Trace.file_id)
}

// int32 lineno = 2;
inline void CodeDef_Trace::clear_lineno() {
  lineno_ = 0;
}
inline ::google::protobuf::int32 CodeDef_Trace::lineno() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.CodeDef.Trace.lineno)
  return lineno_;
}
inline void CodeDef_Trace::set_lineno(::google::protobuf::int32 value) {
  
  lineno_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.CodeDef.Trace.lineno)
}

// string function = 3 [deprecated = true];
inline void CodeDef_Trace::clear_function() {
  function_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& CodeDef_Trace::function() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.CodeDef.Trace.function)
  return function_.GetNoArena();
}
inline void CodeDef_Trace::set_function(const ::std::string& value) {
  
  function_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.CodeDef.Trace.function)
}
#if LANG_CXX11
inline void CodeDef_Trace::set_function(::std::string&& value) {
  
  function_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tfprof.CodeDef.Trace.function)
}
#endif
inline void CodeDef_Trace::set_function(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  function_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.CodeDef.Trace.function)
}
inline void CodeDef_Trace::set_function(const char* value, size_t size) {
  
  function_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.CodeDef.Trace.function)
}
inline ::std::string* CodeDef_Trace::mutable_function() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.CodeDef.Trace.function)
  return function_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* CodeDef_Trace::release_function() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.CodeDef.Trace.function)
  
  return function_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void CodeDef_Trace::set_allocated_function(::std::string* function) {
  if (function != NULL) {
    
  } else {
    
  }
  function_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), function);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.CodeDef.Trace.function)
}

// int64 function_id = 7;
inline void CodeDef_Trace::clear_function_id() {
  function_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CodeDef_Trace::function_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.CodeDef.Trace.function_id)
  return function_id_;
}
inline void CodeDef_Trace::set_function_id(::google::protobuf::int64 value) {
  
  function_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.CodeDef.Trace.function_id)
}

// string line = 4 [deprecated = true];
inline void CodeDef_Trace::clear_line() {
  line_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& CodeDef_Trace::line() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.CodeDef.Trace.line)
  return line_.GetNoArena();
}
inline void CodeDef_Trace::set_line(const ::std::string& value) {
  
  line_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.CodeDef.Trace.line)
}
#if LANG_CXX11
inline void CodeDef_Trace::set_line(::std::string&& value) {
  
  line_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tfprof.CodeDef.Trace.line)
}
#endif
inline void CodeDef_Trace::set_line(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  line_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.CodeDef.Trace.line)
}
inline void CodeDef_Trace::set_line(const char* value, size_t size) {
  
  line_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.CodeDef.Trace.line)
}
inline ::std::string* CodeDef_Trace::mutable_line() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.CodeDef.Trace.line)
  return line_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* CodeDef_Trace::release_line() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.CodeDef.Trace.line)
  
  return line_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void CodeDef_Trace::set_allocated_line(::std::string* line) {
  if (line != NULL) {
    
  } else {
    
  }
  line_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), line);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.CodeDef.Trace.line)
}

// int64 line_id = 8;
inline void CodeDef_Trace::clear_line_id() {
  line_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CodeDef_Trace::line_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.CodeDef.Trace.line_id)
  return line_id_;
}
inline void CodeDef_Trace::set_line_id(::google::protobuf::int64 value) {
  
  line_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.CodeDef.Trace.line_id)
}

// int32 func_start_line = 5;
inline void CodeDef_Trace::clear_func_start_line() {
  func_start_line_ = 0;
}
inline ::google::protobuf::int32 CodeDef_Trace::func_start_line() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.CodeDef.Trace.func_start_line)
  return func_start_line_;
}
inline void CodeDef_Trace::set_func_start_line(::google::protobuf::int32 value) {
  
  func_start_line_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.CodeDef.Trace.func_start_line)
}

// -------------------------------------------------------------------

// CodeDef

// repeated .tensorflow.tfprof.CodeDef.Trace traces = 1;
inline int CodeDef::traces_size() const {
  return traces_.size();
}
inline void CodeDef::clear_traces() {
  traces_.Clear();
}
inline ::tensorflow::tfprof::CodeDef_Trace* CodeDef::mutable_traces(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.CodeDef.traces)
  return traces_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::CodeDef_Trace >*
CodeDef::mutable_traces() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.CodeDef.traces)
  return &traces_;
}
inline const ::tensorflow::tfprof::CodeDef_Trace& CodeDef::traces(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.CodeDef.traces)
  return traces_.Get(index);
}
inline ::tensorflow::tfprof::CodeDef_Trace* CodeDef::add_traces() {
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.CodeDef.traces)
  return traces_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::CodeDef_Trace >&
CodeDef::traces() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.CodeDef.traces)
  return traces_;
}

// -------------------------------------------------------------------

// OpLogEntry

// string name = 1;
inline void OpLogEntry::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OpLogEntry::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OpLogEntry.name)
  return name_.GetNoArena();
}
inline void OpLogEntry::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OpLogEntry.name)
}
#if LANG_CXX11
inline void OpLogEntry::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tfprof.OpLogEntry.name)
}
#endif
inline void OpLogEntry::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.OpLogEntry.name)
}
inline void OpLogEntry::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.OpLogEntry.name)
}
inline ::std::string* OpLogEntry::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OpLogEntry.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OpLogEntry::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.OpLogEntry.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OpLogEntry::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.OpLogEntry.name)
}

// int64 float_ops = 2;
inline void OpLogEntry::clear_float_ops() {
  float_ops_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OpLogEntry::float_ops() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OpLogEntry.float_ops)
  return float_ops_;
}
inline void OpLogEntry::set_float_ops(::google::protobuf::int64 value) {
  
  float_ops_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OpLogEntry.float_ops)
}

// repeated string types = 3;
inline int OpLogEntry::types_size() const {
  return types_.size();
}
inline void OpLogEntry::clear_types() {
  types_.Clear();
}
inline const ::std::string& OpLogEntry::types(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OpLogEntry.types)
  return types_.Get(index);
}
inline ::std::string* OpLogEntry::mutable_types(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OpLogEntry.types)
  return types_.Mutable(index);
}
inline void OpLogEntry::set_types(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OpLogEntry.types)
  types_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void OpLogEntry::set_types(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OpLogEntry.types)
  types_.Mutable(index)->assign(std::move(value));
}
#endif
inline void OpLogEntry::set_types(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  types_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.OpLogEntry.types)
}
inline void OpLogEntry::set_types(int index, const char* value, size_t size) {
  types_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.OpLogEntry.types)
}
inline ::std::string* OpLogEntry::add_types() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.OpLogEntry.types)
  return types_.Add();
}
inline void OpLogEntry::add_types(const ::std::string& value) {
  types_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OpLogEntry.types)
}
#if LANG_CXX11
inline void OpLogEntry::add_types(::std::string&& value) {
  types_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OpLogEntry.types)
}
#endif
inline void OpLogEntry::add_types(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  types_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.OpLogEntry.types)
}
inline void OpLogEntry::add_types(const char* value, size_t size) {
  types_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.OpLogEntry.types)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
OpLogEntry::types() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.OpLogEntry.types)
  return types_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
OpLogEntry::mutable_types() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.OpLogEntry.types)
  return &types_;
}

// .tensorflow.tfprof.CodeDef code_def = 4;
inline bool OpLogEntry::has_code_def() const {
  return this != internal_default_instance() && code_def_ != NULL;
}
inline void OpLogEntry::clear_code_def() {
  if (GetArenaNoVirtual() == NULL && code_def_ != NULL) {
    delete code_def_;
  }
  code_def_ = NULL;
}
inline const ::tensorflow::tfprof::CodeDef& OpLogEntry::_internal_code_def() const {
  return *code_def_;
}
inline const ::tensorflow::tfprof::CodeDef& OpLogEntry::code_def() const {
  const ::tensorflow::tfprof::CodeDef* p = code_def_;
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OpLogEntry.code_def)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::tfprof::CodeDef*>(
      &::tensorflow::tfprof::_CodeDef_default_instance_);
}
inline ::tensorflow::tfprof::CodeDef* OpLogEntry::release_code_def() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.OpLogEntry.code_def)
  
  ::tensorflow::tfprof::CodeDef* temp = code_def_;
  code_def_ = NULL;
  return temp;
}
inline ::tensorflow::tfprof::CodeDef* OpLogEntry::mutable_code_def() {
  
  if (code_def_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::tfprof::CodeDef>(GetArenaNoVirtual());
    code_def_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OpLogEntry.code_def)
  return code_def_;
}
inline void OpLogEntry::set_allocated_code_def(::tensorflow::tfprof::CodeDef* code_def) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete code_def_;
  }
  if (code_def) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      code_def = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, code_def, submessage_arena);
    }
    
  } else {
    
  }
  code_def_ = code_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.OpLogEntry.code_def)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// OpLogProto

// repeated .tensorflow.tfprof.OpLogEntry log_entries = 1;
inline int OpLogProto::log_entries_size() const {
  return log_entries_.size();
}
inline void OpLogProto::clear_log_entries() {
  log_entries_.Clear();
}
inline ::tensorflow::tfprof::OpLogEntry* OpLogProto::mutable_log_entries(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OpLogProto.log_entries)
  return log_entries_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::OpLogEntry >*
OpLogProto::mutable_log_entries() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.OpLogProto.log_entries)
  return &log_entries_;
}
inline const ::tensorflow::tfprof::OpLogEntry& OpLogProto::log_entries(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OpLogProto.log_entries)
  return log_entries_.Get(index);
}
inline ::tensorflow::tfprof::OpLogEntry* OpLogProto::add_log_entries() {
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OpLogProto.log_entries)
  return log_entries_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::OpLogEntry >&
OpLogProto::log_entries() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.OpLogProto.log_entries)
  return log_entries_;
}

// map<int64, string> id_to_string = 2;
inline int OpLogProto::id_to_string_size() const {
  return id_to_string_.size();
}
inline void OpLogProto::clear_id_to_string() {
  id_to_string_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int64, ::std::string >&
OpLogProto::id_to_string() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.OpLogProto.id_to_string)
  return id_to_string_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int64, ::std::string >*
OpLogProto::mutable_id_to_string() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.OpLogProto.id_to_string)
  return id_to_string_.MutableMap();
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// ProfileProto

// map<int64, .tensorflow.tfprof.ProfileNode> nodes = 1;
inline int ProfileProto::nodes_size() const {
  return nodes_.size();
}
inline void ProfileProto::clear_nodes() {
  nodes_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int64, ::tensorflow::tfprof::ProfileNode >&
ProfileProto::nodes() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ProfileProto.nodes)
  return nodes_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int64, ::tensorflow::tfprof::ProfileNode >*
ProfileProto::mutable_nodes() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ProfileProto.nodes)
  return nodes_.MutableMap();
}

// bool has_trace = 2;
inline void ProfileProto::clear_has_trace() {
  has_trace_ = false;
}
inline bool ProfileProto::has_trace() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileProto.has_trace)
  return has_trace_;
}
inline void ProfileProto::set_has_trace(bool value) {
  
  has_trace_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileProto.has_trace)
}

// bool miss_accelerator_stream = 5;
inline void ProfileProto::clear_miss_accelerator_stream() {
  miss_accelerator_stream_ = false;
}
inline bool ProfileProto::miss_accelerator_stream() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileProto.miss_accelerator_stream)
  return miss_accelerator_stream_;
}
inline void ProfileProto::set_miss_accelerator_stream(bool value) {
  
  miss_accelerator_stream_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileProto.miss_accelerator_stream)
}

// repeated int64 steps = 3;
inline int ProfileProto::steps_size() const {
  return steps_.size();
}
inline void ProfileProto::clear_steps() {
  steps_.Clear();
}
inline ::google::protobuf::int64 ProfileProto::steps(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileProto.steps)
  return steps_.Get(index);
}
inline void ProfileProto::set_steps(int index, ::google::protobuf::int64 value) {
  steps_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileProto.steps)
}
inline void ProfileProto::add_steps(::google::protobuf::int64 value) {
  steps_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.ProfileProto.steps)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
ProfileProto::steps() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.ProfileProto.steps)
  return steps_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
ProfileProto::mutable_steps() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.ProfileProto.steps)
  return &steps_;
}

// map<int64, string> id_to_string = 4;
inline int ProfileProto::id_to_string_size() const {
  return id_to_string_.size();
}
inline void ProfileProto::clear_id_to_string() {
  id_to_string_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int64, ::std::string >&
ProfileProto::id_to_string() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ProfileProto.id_to_string)
  return id_to_string_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int64, ::std::string >*
ProfileProto::mutable_id_to_string() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ProfileProto.id_to_string)
  return id_to_string_.MutableMap();
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// ProfileNode

// string name = 1;
inline void ProfileNode::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ProfileNode::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileNode.name)
  return name_.GetNoArena();
}
inline void ProfileNode::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileNode.name)
}
#if LANG_CXX11
inline void ProfileNode::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tfprof.ProfileNode.name)
}
#endif
inline void ProfileNode::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.ProfileNode.name)
}
inline void ProfileNode::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.ProfileNode.name)
}
inline ::std::string* ProfileNode::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ProfileNode.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ProfileNode::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.ProfileNode.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ProfileNode::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.ProfileNode.name)
}

// string op = 9;
inline void ProfileNode::clear_op() {
  op_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ProfileNode::op() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileNode.op)
  return op_.GetNoArena();
}
inline void ProfileNode::set_op(const ::std::string& value) {
  
  op_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileNode.op)
}
#if LANG_CXX11
inline void ProfileNode::set_op(::std::string&& value) {
  
  op_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tfprof.ProfileNode.op)
}
#endif
inline void ProfileNode::set_op(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  op_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.ProfileNode.op)
}
inline void ProfileNode::set_op(const char* value, size_t size) {
  
  op_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.ProfileNode.op)
}
inline ::std::string* ProfileNode::mutable_op() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ProfileNode.op)
  return op_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ProfileNode::release_op() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.ProfileNode.op)
  
  return op_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ProfileNode::set_allocated_op(::std::string* op) {
  if (op != NULL) {
    
  } else {
    
  }
  op_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), op);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.ProfileNode.op)
}

// int64 id = 13;
inline void ProfileNode::clear_id() {
  id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ProfileNode::id() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileNode.id)
  return id_;
}
inline void ProfileNode::set_id(::google::protobuf::int64 value) {
  
  id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileNode.id)
}

// map<int32, int64> inputs = 2;
inline int ProfileNode::inputs_size() const {
  return inputs_.size();
}
inline void ProfileNode::clear_inputs() {
  inputs_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int64 >&
ProfileNode::inputs() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ProfileNode.inputs)
  return inputs_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int64 >*
ProfileNode::mutable_inputs() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ProfileNode.inputs)
  return inputs_.MutableMap();
}

// map<int32, .tensorflow.tfprof.Tuple> input_shapes = 16;
inline int ProfileNode::input_shapes_size() const {
  return input_shapes_.size();
}
inline void ProfileNode::clear_input_shapes() {
  input_shapes_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Tuple >&
ProfileNode::input_shapes() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ProfileNode.input_shapes)
  return input_shapes_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Tuple >*
ProfileNode::mutable_input_shapes() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ProfileNode.input_shapes)
  return input_shapes_.MutableMap();
}

// map<int32, int64> outputs = 3;
inline int ProfileNode::outputs_size() const {
  return outputs_.size();
}
inline void ProfileNode::clear_outputs() {
  outputs_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int64 >&
ProfileNode::outputs() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ProfileNode.outputs)
  return outputs_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int64 >*
ProfileNode::mutable_outputs() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ProfileNode.outputs)
  return outputs_.MutableMap();
}

// map<int32, .tensorflow.tfprof.Tuple> output_shapes = 15;
inline int ProfileNode::output_shapes_size() const {
  return output_shapes_.size();
}
inline void ProfileNode::clear_output_shapes() {
  output_shapes_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Tuple >&
ProfileNode::output_shapes() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ProfileNode.output_shapes)
  return output_shapes_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Tuple >*
ProfileNode::mutable_output_shapes() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ProfileNode.output_shapes)
  return output_shapes_.MutableMap();
}

// map<int64, int32> src_output_index = 14;
inline int ProfileNode::src_output_index_size() const {
  return src_output_index_.size();
}
inline void ProfileNode::clear_src_output_index() {
  src_output_index_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >&
ProfileNode::src_output_index() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ProfileNode.src_output_index)
  return src_output_index_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >*
ProfileNode::mutable_src_output_index() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ProfileNode.src_output_index)
  return src_output_index_.MutableMap();
}

// repeated int64 shape = 4;
inline int ProfileNode::shape_size() const {
  return shape_.size();
}
inline void ProfileNode::clear_shape() {
  shape_.Clear();
}
inline ::google::protobuf::int64 ProfileNode::shape(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileNode.shape)
  return shape_.Get(index);
}
inline void ProfileNode::set_shape(int index, ::google::protobuf::int64 value) {
  shape_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileNode.shape)
}
inline void ProfileNode::add_shape(::google::protobuf::int64 value) {
  shape_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.ProfileNode.shape)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
ProfileNode::shape() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.ProfileNode.shape)
  return shape_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
ProfileNode::mutable_shape() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.ProfileNode.shape)
  return &shape_;
}

// repeated string op_types = 5;
inline int ProfileNode::op_types_size() const {
  return op_types_.size();
}
inline void ProfileNode::clear_op_types() {
  op_types_.Clear();
}
inline const ::std::string& ProfileNode::op_types(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileNode.op_types)
  return op_types_.Get(index);
}
inline ::std::string* ProfileNode::mutable_op_types(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ProfileNode.op_types)
  return op_types_.Mutable(index);
}
inline void ProfileNode::set_op_types(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileNode.op_types)
  op_types_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void ProfileNode::set_op_types(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileNode.op_types)
  op_types_.Mutable(index)->assign(std::move(value));
}
#endif
inline void ProfileNode::set_op_types(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  op_types_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.ProfileNode.op_types)
}
inline void ProfileNode::set_op_types(int index, const char* value, size_t size) {
  op_types_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.ProfileNode.op_types)
}
inline ::std::string* ProfileNode::add_op_types() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.ProfileNode.op_types)
  return op_types_.Add();
}
inline void ProfileNode::add_op_types(const ::std::string& value) {
  op_types_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.ProfileNode.op_types)
}
#if LANG_CXX11
inline void ProfileNode::add_op_types(::std::string&& value) {
  op_types_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.ProfileNode.op_types)
}
#endif
inline void ProfileNode::add_op_types(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  op_types_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.ProfileNode.op_types)
}
inline void ProfileNode::add_op_types(const char* value, size_t size) {
  op_types_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.ProfileNode.op_types)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
ProfileNode::op_types() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.ProfileNode.op_types)
  return op_types_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
ProfileNode::mutable_op_types() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.ProfileNode.op_types)
  return &op_types_;
}

// string canonical_device = 6;
inline void ProfileNode::clear_canonical_device() {
  canonical_device_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ProfileNode::canonical_device() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileNode.canonical_device)
  return canonical_device_.GetNoArena();
}
inline void ProfileNode::set_canonical_device(const ::std::string& value) {
  
  canonical_device_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileNode.canonical_device)
}
#if LANG_CXX11
inline void ProfileNode::set_canonical_device(::std::string&& value) {
  
  canonical_device_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tfprof.ProfileNode.canonical_device)
}
#endif
inline void ProfileNode::set_canonical_device(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  canonical_device_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.ProfileNode.canonical_device)
}
inline void ProfileNode::set_canonical_device(const char* value, size_t size) {
  
  canonical_device_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.ProfileNode.canonical_device)
}
inline ::std::string* ProfileNode::mutable_canonical_device() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ProfileNode.canonical_device)
  return canonical_device_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ProfileNode::release_canonical_device() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.ProfileNode.canonical_device)
  
  return canonical_device_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ProfileNode::set_allocated_canonical_device(::std::string* canonical_device) {
  if (canonical_device != NULL) {
    
  } else {
    
  }
  canonical_device_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), canonical_device);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.ProfileNode.canonical_device)
}

// string host_device = 7;
inline void ProfileNode::clear_host_device() {
  host_device_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ProfileNode::host_device() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileNode.host_device)
  return host_device_.GetNoArena();
}
inline void ProfileNode::set_host_device(const ::std::string& value) {
  
  host_device_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileNode.host_device)
}
#if LANG_CXX11
inline void ProfileNode::set_host_device(::std::string&& value) {
  
  host_device_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tfprof.ProfileNode.host_device)
}
#endif
inline void ProfileNode::set_host_device(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  host_device_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.ProfileNode.host_device)
}
inline void ProfileNode::set_host_device(const char* value, size_t size) {
  
  host_device_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.ProfileNode.host_device)
}
inline ::std::string* ProfileNode::mutable_host_device() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ProfileNode.host_device)
  return host_device_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ProfileNode::release_host_device() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.ProfileNode.host_device)
  
  return host_device_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ProfileNode::set_allocated_host_device(::std::string* host_device) {
  if (host_device != NULL) {
    
  } else {
    
  }
  host_device_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), host_device);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.ProfileNode.host_device)
}

// int64 float_ops = 8;
inline void ProfileNode::clear_float_ops() {
  float_ops_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ProfileNode::float_ops() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileNode.float_ops)
  return float_ops_;
}
inline void ProfileNode::set_float_ops(::google::protobuf::int64 value) {
  
  float_ops_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileNode.float_ops)
}

// .tensorflow.tfprof.CodeDef trace = 10;
inline bool ProfileNode::has_trace() const {
  return this != internal_default_instance() && trace_ != NULL;
}
inline void ProfileNode::clear_trace() {
  if (GetArenaNoVirtual() == NULL && trace_ != NULL) {
    delete trace_;
  }
  trace_ = NULL;
}
inline const ::tensorflow::tfprof::CodeDef& ProfileNode::_internal_trace() const {
  return *trace_;
}
inline const ::tensorflow::tfprof::CodeDef& ProfileNode::trace() const {
  const ::tensorflow::tfprof::CodeDef* p = trace_;
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileNode.trace)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::tfprof::CodeDef*>(
      &::tensorflow::tfprof::_CodeDef_default_instance_);
}
inline ::tensorflow::tfprof::CodeDef* ProfileNode::release_trace() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.ProfileNode.trace)
  
  ::tensorflow::tfprof::CodeDef* temp = trace_;
  trace_ = NULL;
  return temp;
}
inline ::tensorflow::tfprof::CodeDef* ProfileNode::mutable_trace() {
  
  if (trace_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::tfprof::CodeDef>(GetArenaNoVirtual());
    trace_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ProfileNode.trace)
  return trace_;
}
inline void ProfileNode::set_allocated_trace(::tensorflow::tfprof::CodeDef* trace) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete trace_;
  }
  if (trace) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      trace = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, trace, submessage_arena);
    }
    
  } else {
    
  }
  trace_ = trace;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.ProfileNode.trace)
}

// map<string, .tensorflow.AttrValue> attrs = 11;
inline int ProfileNode::attrs_size() const {
  return attrs_.size();
}
inline const ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >&
ProfileNode::attrs() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ProfileNode.attrs)
  return attrs_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >*
ProfileNode::mutable_attrs() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ProfileNode.attrs)
  return attrs_.MutableMap();
}

// map<int64, .tensorflow.tfprof.ExecProfile> execs = 12;
inline int ProfileNode::execs_size() const {
  return execs_.size();
}
inline void ProfileNode::clear_execs() {
  execs_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int64, ::tensorflow::tfprof::ExecProfile >&
ProfileNode::execs() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ProfileNode.execs)
  return execs_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int64, ::tensorflow::tfprof::ExecProfile >*
ProfileNode::mutable_execs() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ProfileNode.execs)
  return execs_.MutableMap();
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// ExecProfile

// int64 run_count = 1;
inline void ExecProfile::clear_run_count() {
  run_count_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ExecProfile::run_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecProfile.run_count)
  return run_count_;
}
inline void ExecProfile::set_run_count(::google::protobuf::int64 value) {
  
  run_count_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecProfile.run_count)
}

// int64 all_start_micros = 2;
inline void ExecProfile::clear_all_start_micros() {
  all_start_micros_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ExecProfile::all_start_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecProfile.all_start_micros)
  return all_start_micros_;
}
inline void ExecProfile::set_all_start_micros(::google::protobuf::int64 value) {
  
  all_start_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecProfile.all_start_micros)
}

// int64 latest_end_micros = 3;
inline void ExecProfile::clear_latest_end_micros() {
  latest_end_micros_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ExecProfile::latest_end_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecProfile.latest_end_micros)
  return latest_end_micros_;
}
inline void ExecProfile::set_latest_end_micros(::google::protobuf::int64 value) {
  
  latest_end_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecProfile.latest_end_micros)
}

// map<string, .tensorflow.tfprof.ExecTime> accelerator_execs = 4;
inline int ExecProfile::accelerator_execs_size() const {
  return accelerator_execs_.size();
}
inline void ExecProfile::clear_accelerator_execs() {
  accelerator_execs_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::ExecTime >&
ExecProfile::accelerator_execs() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ExecProfile.accelerator_execs)
  return accelerator_execs_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::ExecTime >*
ExecProfile::mutable_accelerator_execs() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ExecProfile.accelerator_execs)
  return accelerator_execs_.MutableMap();
}

// map<string, .tensorflow.tfprof.ExecTime> cpu_execs = 5;
inline int ExecProfile::cpu_execs_size() const {
  return cpu_execs_.size();
}
inline void ExecProfile::clear_cpu_execs() {
  cpu_execs_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::ExecTime >&
ExecProfile::cpu_execs() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ExecProfile.cpu_execs)
  return cpu_execs_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::ExecTime >*
ExecProfile::mutable_cpu_execs() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ExecProfile.cpu_execs)
  return cpu_execs_.MutableMap();
}

// repeated .tensorflow.tfprof.ExecMemory memory_execs = 7;
inline int ExecProfile::memory_execs_size() const {
  return memory_execs_.size();
}
inline void ExecProfile::clear_memory_execs() {
  memory_execs_.Clear();
}
inline ::tensorflow::tfprof::ExecMemory* ExecProfile::mutable_memory_execs(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ExecProfile.memory_execs)
  return memory_execs_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::ExecMemory >*
ExecProfile::mutable_memory_execs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.ExecProfile.memory_execs)
  return &memory_execs_;
}
inline const ::tensorflow::tfprof::ExecMemory& ExecProfile::memory_execs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecProfile.memory_execs)
  return memory_execs_.Get(index);
}
inline ::tensorflow::tfprof::ExecMemory* ExecProfile::add_memory_execs() {
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.ExecProfile.memory_execs)
  return memory_execs_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::ExecMemory >&
ExecProfile::memory_execs() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.ExecProfile.memory_execs)
  return memory_execs_;
}

// repeated .tensorflow.AllocationRecord allocations = 11;
inline int ExecProfile::allocations_size() const {
  return allocations_.size();
}
inline ::tensorflow::AllocationRecord* ExecProfile::mutable_allocations(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ExecProfile.allocations)
  return allocations_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::AllocationRecord >*
ExecProfile::mutable_allocations() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.ExecProfile.allocations)
  return &allocations_;
}
inline const ::tensorflow::AllocationRecord& ExecProfile::allocations(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecProfile.allocations)
  return allocations_.Get(index);
}
inline ::tensorflow::AllocationRecord* ExecProfile::add_allocations() {
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.ExecProfile.allocations)
  return allocations_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::AllocationRecord >&
ExecProfile::allocations() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.ExecProfile.allocations)
  return allocations_;
}

// repeated string devices = 6;
inline int ExecProfile::devices_size() const {
  return devices_.size();
}
inline void ExecProfile::clear_devices() {
  devices_.Clear();
}
inline const ::std::string& ExecProfile::devices(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecProfile.devices)
  return devices_.Get(index);
}
inline ::std::string* ExecProfile::mutable_devices(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ExecProfile.devices)
  return devices_.Mutable(index);
}
inline void ExecProfile::set_devices(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecProfile.devices)
  devices_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void ExecProfile::set_devices(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecProfile.devices)
  devices_.Mutable(index)->assign(std::move(value));
}
#endif
inline void ExecProfile::set_devices(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  devices_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.ExecProfile.devices)
}
inline void ExecProfile::set_devices(int index, const char* value, size_t size) {
  devices_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.ExecProfile.devices)
}
inline ::std::string* ExecProfile::add_devices() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.ExecProfile.devices)
  return devices_.Add();
}
inline void ExecProfile::add_devices(const ::std::string& value) {
  devices_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.ExecProfile.devices)
}
#if LANG_CXX11
inline void ExecProfile::add_devices(::std::string&& value) {
  devices_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.ExecProfile.devices)
}
#endif
inline void ExecProfile::add_devices(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  devices_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.ExecProfile.devices)
}
inline void ExecProfile::add_devices(const char* value, size_t size) {
  devices_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.ExecProfile.devices)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
ExecProfile::devices() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.ExecProfile.devices)
  return devices_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
ExecProfile::mutable_devices() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.ExecProfile.devices)
  return &devices_;
}

// -------------------------------------------------------------------

// ExecTime

// repeated .tensorflow.tfprof.Tuple times = 1;
inline int ExecTime::times_size() const {
  return times_.size();
}
inline void ExecTime::clear_times() {
  times_.Clear();
}
inline ::tensorflow::tfprof::Tuple* ExecTime::mutable_times(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ExecTime.times)
  return times_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::Tuple >*
ExecTime::mutable_times() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.ExecTime.times)
  return &times_;
}
inline const ::tensorflow::tfprof::Tuple& ExecTime::times(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecTime.times)
  return times_.Get(index);
}
inline ::tensorflow::tfprof::Tuple* ExecTime::add_times() {
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.ExecTime.times)
  return times_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::Tuple >&
ExecTime::times() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.ExecTime.times)
  return times_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// ExecMemory

// int64 memory_micros = 1;
inline void ExecMemory::clear_memory_micros() {
  memory_micros_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ExecMemory::memory_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.memory_micros)
  return memory_micros_;
}
inline void ExecMemory::set_memory_micros(::google::protobuf::int64 value) {
  
  memory_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.memory_micros)
}

// int64 host_temp_bytes = 2;
inline void ExecMemory::clear_host_temp_bytes() {
  host_temp_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ExecMemory::host_temp_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.host_temp_bytes)
  return host_temp_bytes_;
}
inline void ExecMemory::set_host_temp_bytes(::google::protobuf::int64 value) {
  
  host_temp_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.host_temp_bytes)
}

// int64 host_persistent_bytes = 3;
inline void ExecMemory::clear_host_persistent_bytes() {
  host_persistent_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ExecMemory::host_persistent_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.host_persistent_bytes)
  return host_persistent_bytes_;
}
inline void ExecMemory::set_host_persistent_bytes(::google::protobuf::int64 value) {
  
  host_persistent_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.host_persistent_bytes)
}

// int64 accelerator_temp_bytes = 4;
inline void ExecMemory::clear_accelerator_temp_bytes() {
  accelerator_temp_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ExecMemory::accelerator_temp_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.accelerator_temp_bytes)
  return accelerator_temp_bytes_;
}
inline void ExecMemory::set_accelerator_temp_bytes(::google::protobuf::int64 value) {
  
  accelerator_temp_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.accelerator_temp_bytes)
}

// int64 accelerator_persistent_bytes = 5;
inline void ExecMemory::clear_accelerator_persistent_bytes() {
  accelerator_persistent_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ExecMemory::accelerator_persistent_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.accelerator_persistent_bytes)
  return accelerator_persistent_bytes_;
}
inline void ExecMemory::set_accelerator_persistent_bytes(::google::protobuf::int64 value) {
  
  accelerator_persistent_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.accelerator_persistent_bytes)
}

// int64 requested_bytes = 6;
inline void ExecMemory::clear_requested_bytes() {
  requested_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ExecMemory::requested_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.requested_bytes)
  return requested_bytes_;
}
inline void ExecMemory::set_requested_bytes(::google::protobuf::int64 value) {
  
  requested_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.requested_bytes)
}

// int64 peak_bytes = 7;
inline void ExecMemory::clear_peak_bytes() {
  peak_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ExecMemory::peak_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.peak_bytes)
  return peak_bytes_;
}
inline void ExecMemory::set_peak_bytes(::google::protobuf::int64 value) {
  
  peak_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.peak_bytes)
}

// int64 residual_bytes = 8;
inline void ExecMemory::clear_residual_bytes() {
  residual_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ExecMemory::residual_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.residual_bytes)
  return residual_bytes_;
}
inline void ExecMemory::set_residual_bytes(::google::protobuf::int64 value) {
  
  residual_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.residual_bytes)
}

// int64 output_bytes = 9;
inline void ExecMemory::clear_output_bytes() {
  output_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ExecMemory::output_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.output_bytes)
  return output_bytes_;
}
inline void ExecMemory::set_output_bytes(::google::protobuf::int64 value) {
  
  output_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.output_bytes)
}

// int64 allocator_bytes_in_use = 10;
inline void ExecMemory::clear_allocator_bytes_in_use() {
  allocator_bytes_in_use_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ExecMemory::allocator_bytes_in_use() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.allocator_bytes_in_use)
  return allocator_bytes_in_use_;
}
inline void ExecMemory::set_allocator_bytes_in_use(::google::protobuf::int64 value) {
  
  allocator_bytes_in_use_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.allocator_bytes_in_use)
}

// map<int32, .tensorflow.tfprof.Memory> output_memory = 11;
inline int ExecMemory::output_memory_size() const {
  return output_memory_.size();
}
inline void ExecMemory::clear_output_memory() {
  output_memory_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Memory >&
ExecMemory::output_memory() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ExecMemory.output_memory)
  return output_memory_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Memory >*
ExecMemory::mutable_output_memory() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ExecMemory.output_memory)
  return output_memory_.MutableMap();
}

// -------------------------------------------------------------------

// Tuple

// repeated int64 int64_values = 1;
inline int Tuple::int64_values_size() const {
  return int64_values_.size();
}
inline void Tuple::clear_int64_values() {
  int64_values_.Clear();
}
inline ::google::protobuf::int64 Tuple::int64_values(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.Tuple.int64_values)
  return int64_values_.Get(index);
}
inline void Tuple::set_int64_values(int index, ::google::protobuf::int64 value) {
  int64_values_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.Tuple.int64_values)
}
inline void Tuple::add_int64_values(::google::protobuf::int64 value) {
  int64_values_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.Tuple.int64_values)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
Tuple::int64_values() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.Tuple.int64_values)
  return int64_values_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
Tuple::mutable_int64_values() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.Tuple.int64_values)
  return &int64_values_;
}

// -------------------------------------------------------------------

// Memory

// int64 bytes = 1;
inline void Memory::clear_bytes() {
  bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 Memory::bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.Memory.bytes)
  return bytes_;
}
inline void Memory::set_bytes(::google::protobuf::int64 value) {
  
  bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.Memory.bytes)
}

// uint64 ptr = 2;
inline void Memory::clear_ptr() {
  ptr_ = GOOGLE_ULONGLONG(0);
}
inline ::google::protobuf::uint64 Memory::ptr() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.Memory.ptr)
  return ptr_;
}
inline void Memory::set_ptr(::google::protobuf::uint64 value) {
  
  ptr_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.Memory.ptr)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tfprof
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto

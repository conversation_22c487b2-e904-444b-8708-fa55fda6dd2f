// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/profiler/trace_events.proto

#include "tensorflow/core/profiler/trace_events.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_Resource;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_TraceEvent_ArgsEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_Device;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_Device_ResourcesEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_TraceEvent;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_Trace_DevicesEntry_DoNotUse;
}  // namespace protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto
namespace tensorflow {
namespace profiler {
class Trace_DevicesEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Trace_DevicesEntry_DoNotUse>
      _instance;
} _Trace_DevicesEntry_DoNotUse_default_instance_;
class TraceDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Trace>
      _instance;
} _Trace_default_instance_;
class Device_ResourcesEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Device_ResourcesEntry_DoNotUse>
      _instance;
} _Device_ResourcesEntry_DoNotUse_default_instance_;
class DeviceDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Device>
      _instance;
} _Device_default_instance_;
class ResourceDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Resource>
      _instance;
} _Resource_default_instance_;
class TraceEvent_ArgsEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TraceEvent_ArgsEntry_DoNotUse>
      _instance;
} _TraceEvent_ArgsEntry_DoNotUse_default_instance_;
class TraceEventDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TraceEvent>
      _instance;
} _TraceEvent_default_instance_;
}  // namespace profiler
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto {
static void InitDefaultsTrace_DevicesEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::profiler::_Trace_DevicesEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::profiler::Trace_DevicesEntry_DoNotUse();
  }
  ::tensorflow::profiler::Trace_DevicesEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_Trace_DevicesEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsTrace_DevicesEntry_DoNotUse}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::scc_info_Device.base,}};

static void InitDefaultsTrace() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::profiler::_Trace_default_instance_;
    new (ptr) ::tensorflow::profiler::Trace();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::profiler::Trace::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_Trace =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsTrace}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::scc_info_Trace_DevicesEntry_DoNotUse.base,
      &protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::scc_info_TraceEvent.base,}};

static void InitDefaultsDevice_ResourcesEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::profiler::_Device_ResourcesEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::profiler::Device_ResourcesEntry_DoNotUse();
  }
  ::tensorflow::profiler::Device_ResourcesEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_Device_ResourcesEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsDevice_ResourcesEntry_DoNotUse}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::scc_info_Resource.base,}};

static void InitDefaultsDevice() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::profiler::_Device_default_instance_;
    new (ptr) ::tensorflow::profiler::Device();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::profiler::Device::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_Device =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsDevice}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::scc_info_Device_ResourcesEntry_DoNotUse.base,}};

static void InitDefaultsResource() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::profiler::_Resource_default_instance_;
    new (ptr) ::tensorflow::profiler::Resource();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::profiler::Resource::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_Resource =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsResource}, {}};

static void InitDefaultsTraceEvent_ArgsEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::profiler::_TraceEvent_ArgsEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::profiler::TraceEvent_ArgsEntry_DoNotUse();
  }
  ::tensorflow::profiler::TraceEvent_ArgsEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_TraceEvent_ArgsEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsTraceEvent_ArgsEntry_DoNotUse}, {}};

static void InitDefaultsTraceEvent() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::profiler::_TraceEvent_default_instance_;
    new (ptr) ::tensorflow::profiler::TraceEvent();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::profiler::TraceEvent::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_TraceEvent =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsTraceEvent}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::scc_info_TraceEvent_ArgsEntry_DoNotUse.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_Trace_DevicesEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Trace.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Device_ResourcesEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Device.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Resource.base);
  ::google::protobuf::internal::InitSCC(&scc_info_TraceEvent_ArgsEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_TraceEvent.base);
}

::google::protobuf::Metadata file_level_metadata[7];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::Trace_DevicesEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::Trace_DevicesEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::Trace_DevicesEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::Trace_DevicesEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::Trace, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::Trace, devices_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::Trace, trace_events_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::Device_ResourcesEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::Device_ResourcesEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::Device_ResourcesEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::Device_ResourcesEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::Device, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::Device, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::Device, device_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::Device, resources_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::Resource, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::Resource, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::Resource, resource_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::TraceEvent_ArgsEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::TraceEvent_ArgsEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::TraceEvent_ArgsEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::TraceEvent_ArgsEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::TraceEvent, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::TraceEvent, device_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::TraceEvent, resource_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::TraceEvent, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::TraceEvent, timestamp_ps_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::TraceEvent, duration_ps_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::profiler::TraceEvent, args_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, 7, sizeof(::tensorflow::profiler::Trace_DevicesEntry_DoNotUse)},
  { 9, -1, sizeof(::tensorflow::profiler::Trace)},
  { 16, 23, sizeof(::tensorflow::profiler::Device_ResourcesEntry_DoNotUse)},
  { 25, -1, sizeof(::tensorflow::profiler::Device)},
  { 33, -1, sizeof(::tensorflow::profiler::Resource)},
  { 40, 47, sizeof(::tensorflow::profiler::TraceEvent_ArgsEntry_DoNotUse)},
  { 49, -1, sizeof(::tensorflow::profiler::TraceEvent)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::profiler::_Trace_DevicesEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::profiler::_Trace_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::profiler::_Device_ResourcesEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::profiler::_Device_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::profiler::_Resource_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::profiler::_TraceEvent_ArgsEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::profiler::_TraceEvent_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/profiler/trace_events.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 7);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n+tensorflow/core/profiler/trace_events."
      "proto\022\023tensorflow.profiler\"\305\001\n\005Trace\0228\n\007"
      "devices\030\001 \003(\0132\'.tensorflow.profiler.Trac"
      "e.DevicesEntry\0225\n\014trace_events\030\004 \003(\0132\037.t"
      "ensorflow.profiler.TraceEvent\032K\n\014Devices"
      "Entry\022\013\n\003key\030\001 \001(\r\022*\n\005value\030\002 \001(\0132\033.tens"
      "orflow.profiler.Device:\0028\001\"\271\001\n\006Device\022\014\n"
      "\004name\030\001 \001(\t\022\021\n\tdevice_id\030\002 \001(\r\022=\n\tresour"
      "ces\030\003 \003(\0132*.tensorflow.profiler.Device.R"
      "esourcesEntry\032O\n\016ResourcesEntry\022\013\n\003key\030\001"
      " \001(\r\022,\n\005value\030\002 \001(\0132\035.tensorflow.profile"
      "r.Resource:\0028\001\"-\n\010Resource\022\014\n\004name\030\001 \001(\t"
      "\022\023\n\013resource_id\030\002 \001(\r\"\323\001\n\nTraceEvent\022\021\n\t"
      "device_id\030\001 \001(\r\022\023\n\013resource_id\030\002 \001(\r\022\014\n\004"
      "name\030\003 \001(\t\022\024\n\014timestamp_ps\030\t \001(\004\022\023\n\013dura"
      "tion_ps\030\n \001(\004\0227\n\004args\030\013 \003(\0132).tensorflow"
      ".profiler.TraceEvent.ArgsEntry\032+\n\tArgsEn"
      "try\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001b\006pro"
      "to3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 723);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/profiler/trace_events.proto", &protobuf_RegisterTypes);
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto
namespace tensorflow {
namespace profiler {

// ===================================================================

Trace_DevicesEntry_DoNotUse::Trace_DevicesEntry_DoNotUse() {}
Trace_DevicesEntry_DoNotUse::Trace_DevicesEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void Trace_DevicesEntry_DoNotUse::MergeFrom(const Trace_DevicesEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata Trace_DevicesEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::file_level_metadata[0];
}
void Trace_DevicesEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void Trace::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Trace::kDevicesFieldNumber;
const int Trace::kTraceEventsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Trace::Trace()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::scc_info_Trace.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.profiler.Trace)
}
Trace::Trace(const Trace& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      trace_events_(from.trace_events_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  devices_.MergeFrom(from.devices_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.profiler.Trace)
}

void Trace::SharedCtor() {
}

Trace::~Trace() {
  // @@protoc_insertion_point(destructor:tensorflow.profiler.Trace)
  SharedDtor();
}

void Trace::SharedDtor() {
}

void Trace::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Trace::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Trace& Trace::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::scc_info_Trace.base);
  return *internal_default_instance();
}


void Trace::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.profiler.Trace)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  devices_.Clear();
  trace_events_.Clear();
  _internal_metadata_.Clear();
}

bool Trace::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.profiler.Trace)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<uint32, .tensorflow.profiler.Device> devices = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          Trace_DevicesEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              Trace_DevicesEntry_DoNotUse,
              ::google::protobuf::uint32, ::tensorflow::profiler::Device,
              ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint32, ::tensorflow::profiler::Device > > parser(&devices_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.profiler.TraceEvent trace_events = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_trace_events()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.profiler.Trace)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.profiler.Trace)
  return false;
#undef DO_
}

void Trace::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.profiler.Trace)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // map<uint32, .tensorflow.profiler.Device> devices = 1;
  if (!this->devices().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::tensorflow::profiler::Device >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterministic() &&
        this->devices().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->devices().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::tensorflow::profiler::Device >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::tensorflow::profiler::Device >::const_iterator
          it = this->devices().begin();
          it != this->devices().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<Trace_DevicesEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(devices_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
      }
    } else {
      ::std::unique_ptr<Trace_DevicesEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::tensorflow::profiler::Device >::const_iterator
          it = this->devices().begin();
          it != this->devices().end(); ++it) {
        entry.reset(devices_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
      }
    }
  }

  // repeated .tensorflow.profiler.TraceEvent trace_events = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->trace_events_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4,
      this->trace_events(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.profiler.Trace)
}

::google::protobuf::uint8* Trace::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.profiler.Trace)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // map<uint32, .tensorflow.profiler.Device> devices = 1;
  if (!this->devices().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::tensorflow::profiler::Device >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->devices().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->devices().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::tensorflow::profiler::Device >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::tensorflow::profiler::Device >::const_iterator
          it = this->devices().begin();
          it != this->devices().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<Trace_DevicesEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(devices_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
      }
    } else {
      ::std::unique_ptr<Trace_DevicesEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::tensorflow::profiler::Device >::const_iterator
          it = this->devices().begin();
          it != this->devices().end(); ++it) {
        entry.reset(devices_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
      }
    }
  }

  // repeated .tensorflow.profiler.TraceEvent trace_events = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->trace_events_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->trace_events(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.profiler.Trace)
  return target;
}

size_t Trace::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.profiler.Trace)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // map<uint32, .tensorflow.profiler.Device> devices = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->devices_size());
  {
    ::std::unique_ptr<Trace_DevicesEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint32, ::tensorflow::profiler::Device >::const_iterator
        it = this->devices().begin();
        it != this->devices().end(); ++it) {
      entry.reset(devices_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // repeated .tensorflow.profiler.TraceEvent trace_events = 4;
  {
    unsigned int count = static_cast<unsigned int>(this->trace_events_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->trace_events(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Trace::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.profiler.Trace)
  GOOGLE_DCHECK_NE(&from, this);
  const Trace* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Trace>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.profiler.Trace)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.profiler.Trace)
    MergeFrom(*source);
  }
}

void Trace::MergeFrom(const Trace& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.profiler.Trace)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  devices_.MergeFrom(from.devices_);
  trace_events_.MergeFrom(from.trace_events_);
}

void Trace::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.profiler.Trace)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Trace::CopyFrom(const Trace& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.profiler.Trace)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Trace::IsInitialized() const {
  return true;
}

void Trace::Swap(Trace* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Trace::InternalSwap(Trace* other) {
  using std::swap;
  devices_.Swap(&other->devices_);
  CastToBase(&trace_events_)->InternalSwap(CastToBase(&other->trace_events_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Trace::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

Device_ResourcesEntry_DoNotUse::Device_ResourcesEntry_DoNotUse() {}
Device_ResourcesEntry_DoNotUse::Device_ResourcesEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void Device_ResourcesEntry_DoNotUse::MergeFrom(const Device_ResourcesEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata Device_ResourcesEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::file_level_metadata[2];
}
void Device_ResourcesEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void Device::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Device::kNameFieldNumber;
const int Device::kDeviceIdFieldNumber;
const int Device::kResourcesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Device::Device()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::scc_info_Device.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.profiler.Device)
}
Device::Device(const Device& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  resources_.MergeFrom(from.resources_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  device_id_ = from.device_id_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.profiler.Device)
}

void Device::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  device_id_ = 0u;
}

Device::~Device() {
  // @@protoc_insertion_point(destructor:tensorflow.profiler.Device)
  SharedDtor();
}

void Device::SharedDtor() {
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void Device::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Device::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Device& Device::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::scc_info_Device.base);
  return *internal_default_instance();
}


void Device::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.profiler.Device)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  resources_.Clear();
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  device_id_ = 0u;
  _internal_metadata_.Clear();
}

bool Device::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.profiler.Device)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.profiler.Device.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint32 device_id = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &device_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<uint32, .tensorflow.profiler.Resource> resources = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          Device_ResourcesEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              Device_ResourcesEntry_DoNotUse,
              ::google::protobuf::uint32, ::tensorflow::profiler::Resource,
              ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint32, ::tensorflow::profiler::Resource > > parser(&resources_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.profiler.Device)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.profiler.Device)
  return false;
#undef DO_
}

void Device::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.profiler.Device)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.profiler.Device.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // uint32 device_id = 2;
  if (this->device_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(2, this->device_id(), output);
  }

  // map<uint32, .tensorflow.profiler.Resource> resources = 3;
  if (!this->resources().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::tensorflow::profiler::Resource >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterministic() &&
        this->resources().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->resources().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::tensorflow::profiler::Resource >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::tensorflow::profiler::Resource >::const_iterator
          it = this->resources().begin();
          it != this->resources().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<Device_ResourcesEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(resources_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            3, *entry, output);
      }
    } else {
      ::std::unique_ptr<Device_ResourcesEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::tensorflow::profiler::Resource >::const_iterator
          it = this->resources().begin();
          it != this->resources().end(); ++it) {
        entry.reset(resources_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            3, *entry, output);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.profiler.Device)
}

::google::protobuf::uint8* Device::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.profiler.Device)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.profiler.Device.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // uint32 device_id = 2;
  if (this->device_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(2, this->device_id(), target);
  }

  // map<uint32, .tensorflow.profiler.Resource> resources = 3;
  if (!this->resources().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::tensorflow::profiler::Resource >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->resources().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->resources().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::tensorflow::profiler::Resource >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::tensorflow::profiler::Resource >::const_iterator
          it = this->resources().begin();
          it != this->resources().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<Device_ResourcesEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(resources_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       3, *entry, deterministic, target);
;
      }
    } else {
      ::std::unique_ptr<Device_ResourcesEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::tensorflow::profiler::Resource >::const_iterator
          it = this->resources().begin();
          it != this->resources().end(); ++it) {
        entry.reset(resources_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       3, *entry, deterministic, target);
;
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.profiler.Device)
  return target;
}

size_t Device::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.profiler.Device)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // map<uint32, .tensorflow.profiler.Resource> resources = 3;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->resources_size());
  {
    ::std::unique_ptr<Device_ResourcesEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint32, ::tensorflow::profiler::Resource >::const_iterator
        it = this->resources().begin();
        it != this->resources().end(); ++it) {
      entry.reset(resources_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // uint32 device_id = 2;
  if (this->device_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->device_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Device::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.profiler.Device)
  GOOGLE_DCHECK_NE(&from, this);
  const Device* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Device>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.profiler.Device)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.profiler.Device)
    MergeFrom(*source);
  }
}

void Device::MergeFrom(const Device& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.profiler.Device)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  resources_.MergeFrom(from.resources_);
  if (from.name().size() > 0) {

    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  if (from.device_id() != 0) {
    set_device_id(from.device_id());
  }
}

void Device::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.profiler.Device)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Device::CopyFrom(const Device& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.profiler.Device)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Device::IsInitialized() const {
  return true;
}

void Device::Swap(Device* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Device::InternalSwap(Device* other) {
  using std::swap;
  resources_.Swap(&other->resources_);
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(device_id_, other->device_id_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Device::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Resource::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Resource::kNameFieldNumber;
const int Resource::kResourceIdFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Resource::Resource()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::scc_info_Resource.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.profiler.Resource)
}
Resource::Resource(const Resource& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  resource_id_ = from.resource_id_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.profiler.Resource)
}

void Resource::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  resource_id_ = 0u;
}

Resource::~Resource() {
  // @@protoc_insertion_point(destructor:tensorflow.profiler.Resource)
  SharedDtor();
}

void Resource::SharedDtor() {
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void Resource::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Resource::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Resource& Resource::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::scc_info_Resource.base);
  return *internal_default_instance();
}


void Resource::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.profiler.Resource)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  resource_id_ = 0u;
  _internal_metadata_.Clear();
}

bool Resource::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.profiler.Resource)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.profiler.Resource.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint32 resource_id = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &resource_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.profiler.Resource)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.profiler.Resource)
  return false;
#undef DO_
}

void Resource::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.profiler.Resource)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.profiler.Resource.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // uint32 resource_id = 2;
  if (this->resource_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(2, this->resource_id(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.profiler.Resource)
}

::google::protobuf::uint8* Resource::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.profiler.Resource)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.profiler.Resource.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // uint32 resource_id = 2;
  if (this->resource_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(2, this->resource_id(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.profiler.Resource)
  return target;
}

size_t Resource::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.profiler.Resource)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // uint32 resource_id = 2;
  if (this->resource_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->resource_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Resource::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.profiler.Resource)
  GOOGLE_DCHECK_NE(&from, this);
  const Resource* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Resource>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.profiler.Resource)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.profiler.Resource)
    MergeFrom(*source);
  }
}

void Resource::MergeFrom(const Resource& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.profiler.Resource)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.name().size() > 0) {

    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  if (from.resource_id() != 0) {
    set_resource_id(from.resource_id());
  }
}

void Resource::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.profiler.Resource)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Resource::CopyFrom(const Resource& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.profiler.Resource)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Resource::IsInitialized() const {
  return true;
}

void Resource::Swap(Resource* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Resource::InternalSwap(Resource* other) {
  using std::swap;
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(resource_id_, other->resource_id_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Resource::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

TraceEvent_ArgsEntry_DoNotUse::TraceEvent_ArgsEntry_DoNotUse() {}
TraceEvent_ArgsEntry_DoNotUse::TraceEvent_ArgsEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void TraceEvent_ArgsEntry_DoNotUse::MergeFrom(const TraceEvent_ArgsEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata TraceEvent_ArgsEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::file_level_metadata[5];
}
void TraceEvent_ArgsEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void TraceEvent::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TraceEvent::kDeviceIdFieldNumber;
const int TraceEvent::kResourceIdFieldNumber;
const int TraceEvent::kNameFieldNumber;
const int TraceEvent::kTimestampPsFieldNumber;
const int TraceEvent::kDurationPsFieldNumber;
const int TraceEvent::kArgsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TraceEvent::TraceEvent()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::scc_info_TraceEvent.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.profiler.TraceEvent)
}
TraceEvent::TraceEvent(const TraceEvent& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  args_.MergeFrom(from.args_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  ::memcpy(&device_id_, &from.device_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&duration_ps_) -
    reinterpret_cast<char*>(&device_id_)) + sizeof(duration_ps_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.profiler.TraceEvent)
}

void TraceEvent::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&device_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&duration_ps_) -
      reinterpret_cast<char*>(&device_id_)) + sizeof(duration_ps_));
}

TraceEvent::~TraceEvent() {
  // @@protoc_insertion_point(destructor:tensorflow.profiler.TraceEvent)
  SharedDtor();
}

void TraceEvent::SharedDtor() {
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void TraceEvent::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* TraceEvent::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const TraceEvent& TraceEvent::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::scc_info_TraceEvent.base);
  return *internal_default_instance();
}


void TraceEvent::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.profiler.TraceEvent)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  args_.Clear();
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&device_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&duration_ps_) -
      reinterpret_cast<char*>(&device_id_)) + sizeof(duration_ps_));
  _internal_metadata_.Clear();
}

bool TraceEvent::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.profiler.TraceEvent)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint32 device_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &device_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint32 resource_id = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &resource_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string name = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.profiler.TraceEvent.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint64 timestamp_ps = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(72u /* 72 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &timestamp_ps_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint64 duration_ps = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(80u /* 80 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &duration_ps_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, string> args = 11;
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(90u /* 90 & 0xFF */)) {
          TraceEvent_ArgsEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              TraceEvent_ArgsEntry_DoNotUse,
              ::std::string, ::std::string,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              0 >,
            ::google::protobuf::Map< ::std::string, ::std::string > > parser(&args_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.profiler.TraceEvent.ArgsEntry.key"));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.value().data(), static_cast<int>(parser.value().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.profiler.TraceEvent.ArgsEntry.value"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.profiler.TraceEvent)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.profiler.TraceEvent)
  return false;
#undef DO_
}

void TraceEvent::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.profiler.TraceEvent)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 device_id = 1;
  if (this->device_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->device_id(), output);
  }

  // uint32 resource_id = 2;
  if (this->resource_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(2, this->resource_id(), output);
  }

  // string name = 3;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.profiler.TraceEvent.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->name(), output);
  }

  // uint64 timestamp_ps = 9;
  if (this->timestamp_ps() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(9, this->timestamp_ps(), output);
  }

  // uint64 duration_ps = 10;
  if (this->duration_ps() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(10, this->duration_ps(), output);
  }

  // map<string, string> args = 11;
  if (!this->args().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.profiler.TraceEvent.ArgsEntry.key");
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.profiler.TraceEvent.ArgsEntry.value");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->args().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->args().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->args().begin();
          it != this->args().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<TraceEvent_ArgsEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(args_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            11, *entry, output);
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<TraceEvent_ArgsEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->args().begin();
          it != this->args().end(); ++it) {
        entry.reset(args_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            11, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.profiler.TraceEvent)
}

::google::protobuf::uint8* TraceEvent::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.profiler.TraceEvent)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 device_id = 1;
  if (this->device_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->device_id(), target);
  }

  // uint32 resource_id = 2;
  if (this->resource_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(2, this->resource_id(), target);
  }

  // string name = 3;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.profiler.TraceEvent.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->name(), target);
  }

  // uint64 timestamp_ps = 9;
  if (this->timestamp_ps() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(9, this->timestamp_ps(), target);
  }

  // uint64 duration_ps = 10;
  if (this->duration_ps() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(10, this->duration_ps(), target);
  }

  // map<string, string> args = 11;
  if (!this->args().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.profiler.TraceEvent.ArgsEntry.key");
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.profiler.TraceEvent.ArgsEntry.value");
      }
    };

    if (deterministic &&
        this->args().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->args().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->args().begin();
          it != this->args().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<TraceEvent_ArgsEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(args_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       11, *entry, deterministic, target);
;
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<TraceEvent_ArgsEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->args().begin();
          it != this->args().end(); ++it) {
        entry.reset(args_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       11, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.profiler.TraceEvent)
  return target;
}

size_t TraceEvent::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.profiler.TraceEvent)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // map<string, string> args = 11;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->args_size());
  {
    ::std::unique_ptr<TraceEvent_ArgsEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
        it = this->args().begin();
        it != this->args().end(); ++it) {
      entry.reset(args_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // string name = 3;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // uint32 device_id = 1;
  if (this->device_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->device_id());
  }

  // uint32 resource_id = 2;
  if (this->resource_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->resource_id());
  }

  // uint64 timestamp_ps = 9;
  if (this->timestamp_ps() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->timestamp_ps());
  }

  // uint64 duration_ps = 10;
  if (this->duration_ps() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->duration_ps());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TraceEvent::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.profiler.TraceEvent)
  GOOGLE_DCHECK_NE(&from, this);
  const TraceEvent* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TraceEvent>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.profiler.TraceEvent)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.profiler.TraceEvent)
    MergeFrom(*source);
  }
}

void TraceEvent::MergeFrom(const TraceEvent& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.profiler.TraceEvent)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  args_.MergeFrom(from.args_);
  if (from.name().size() > 0) {

    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  if (from.device_id() != 0) {
    set_device_id(from.device_id());
  }
  if (from.resource_id() != 0) {
    set_resource_id(from.resource_id());
  }
  if (from.timestamp_ps() != 0) {
    set_timestamp_ps(from.timestamp_ps());
  }
  if (from.duration_ps() != 0) {
    set_duration_ps(from.duration_ps());
  }
}

void TraceEvent::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.profiler.TraceEvent)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TraceEvent::CopyFrom(const TraceEvent& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.profiler.TraceEvent)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TraceEvent::IsInitialized() const {
  return true;
}

void TraceEvent::Swap(TraceEvent* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TraceEvent::InternalSwap(TraceEvent* other) {
  using std::swap;
  args_.Swap(&other->args_);
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(device_id_, other->device_id_);
  swap(resource_id_, other->resource_id_);
  swap(timestamp_ps_, other->timestamp_ps_);
  swap(duration_ps_, other->duration_ps_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata TraceEvent::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace profiler
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::profiler::Trace_DevicesEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::profiler::Trace_DevicesEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::profiler::Trace_DevicesEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::profiler::Trace* Arena::CreateMaybeMessage< ::tensorflow::profiler::Trace >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::profiler::Trace >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::profiler::Device_ResourcesEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::profiler::Device_ResourcesEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::profiler::Device_ResourcesEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::profiler::Device* Arena::CreateMaybeMessage< ::tensorflow::profiler::Device >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::profiler::Device >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::profiler::Resource* Arena::CreateMaybeMessage< ::tensorflow::profiler::Resource >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::profiler::Resource >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::profiler::TraceEvent_ArgsEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::profiler::TraceEvent_ArgsEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::profiler::TraceEvent_ArgsEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::profiler::TraceEvent* Arena::CreateMaybeMessage< ::tensorflow::profiler::TraceEvent >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::profiler::TraceEvent >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

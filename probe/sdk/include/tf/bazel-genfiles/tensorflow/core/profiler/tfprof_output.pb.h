// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/profiler/tfprof_output.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto 

namespace protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[7];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto
namespace tensorflow {
namespace tfprof {
class AdviceProto;
class AdviceProtoDefaultTypeInternal;
extern AdviceProtoDefaultTypeInternal _AdviceProto_default_instance_;
class AdviceProto_Checker;
class AdviceProto_CheckerDefaultTypeInternal;
extern AdviceProto_CheckerDefaultTypeInternal _AdviceProto_Checker_default_instance_;
class AdviceProto_CheckersEntry_DoNotUse;
class AdviceProto_CheckersEntry_DoNotUseDefaultTypeInternal;
extern AdviceProto_CheckersEntry_DoNotUseDefaultTypeInternal _AdviceProto_CheckersEntry_DoNotUse_default_instance_;
class GraphNodeProto;
class GraphNodeProtoDefaultTypeInternal;
extern GraphNodeProtoDefaultTypeInternal _GraphNodeProto_default_instance_;
class GraphNodeProto_InputShapesEntry_DoNotUse;
class GraphNodeProto_InputShapesEntry_DoNotUseDefaultTypeInternal;
extern GraphNodeProto_InputShapesEntry_DoNotUseDefaultTypeInternal _GraphNodeProto_InputShapesEntry_DoNotUse_default_instance_;
class MultiGraphNodeProto;
class MultiGraphNodeProtoDefaultTypeInternal;
extern MultiGraphNodeProtoDefaultTypeInternal _MultiGraphNodeProto_default_instance_;
class TFProfTensorProto;
class TFProfTensorProtoDefaultTypeInternal;
extern TFProfTensorProtoDefaultTypeInternal _TFProfTensorProto_default_instance_;
}  // namespace tfprof
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::tfprof::AdviceProto* Arena::CreateMaybeMessage<::tensorflow::tfprof::AdviceProto>(Arena*);
template<> ::tensorflow::tfprof::AdviceProto_Checker* Arena::CreateMaybeMessage<::tensorflow::tfprof::AdviceProto_Checker>(Arena*);
template<> ::tensorflow::tfprof::AdviceProto_CheckersEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::AdviceProto_CheckersEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::GraphNodeProto* Arena::CreateMaybeMessage<::tensorflow::tfprof::GraphNodeProto>(Arena*);
template<> ::tensorflow::tfprof::GraphNodeProto_InputShapesEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::GraphNodeProto_InputShapesEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::MultiGraphNodeProto* Arena::CreateMaybeMessage<::tensorflow::tfprof::MultiGraphNodeProto>(Arena*);
template<> ::tensorflow::tfprof::TFProfTensorProto* Arena::CreateMaybeMessage<::tensorflow::tfprof::TFProfTensorProto>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {
namespace tfprof {

// ===================================================================

class TFProfTensorProto : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.TFProfTensorProto) */ {
 public:
  TFProfTensorProto();
  virtual ~TFProfTensorProto();

  TFProfTensorProto(const TFProfTensorProto& from);

  inline TFProfTensorProto& operator=(const TFProfTensorProto& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TFProfTensorProto(TFProfTensorProto&& from) noexcept
    : TFProfTensorProto() {
    *this = ::std::move(from);
  }

  inline TFProfTensorProto& operator=(TFProfTensorProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const TFProfTensorProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TFProfTensorProto* internal_default_instance() {
    return reinterpret_cast<const TFProfTensorProto*>(
               &_TFProfTensorProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void Swap(TFProfTensorProto* other);
  friend void swap(TFProfTensorProto& a, TFProfTensorProto& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TFProfTensorProto* New() const final {
    return CreateMaybeMessage<TFProfTensorProto>(NULL);
  }

  TFProfTensorProto* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TFProfTensorProto>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TFProfTensorProto& from);
  void MergeFrom(const TFProfTensorProto& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TFProfTensorProto* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated double value_double = 2;
  int value_double_size() const;
  void clear_value_double();
  static const int kValueDoubleFieldNumber = 2;
  double value_double(int index) const;
  void set_value_double(int index, double value);
  void add_value_double(double value);
  const ::google::protobuf::RepeatedField< double >&
      value_double() const;
  ::google::protobuf::RepeatedField< double >*
      mutable_value_double();

  // repeated int64 value_int64 = 3;
  int value_int64_size() const;
  void clear_value_int64();
  static const int kValueInt64FieldNumber = 3;
  ::google::protobuf::int64 value_int64(int index) const;
  void set_value_int64(int index, ::google::protobuf::int64 value);
  void add_value_int64(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      value_int64() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_value_int64();

  // repeated string value_str = 4;
  int value_str_size() const;
  void clear_value_str();
  static const int kValueStrFieldNumber = 4;
  const ::std::string& value_str(int index) const;
  ::std::string* mutable_value_str(int index);
  void set_value_str(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_value_str(int index, ::std::string&& value);
  #endif
  void set_value_str(int index, const char* value);
  void set_value_str(int index, const char* value, size_t size);
  ::std::string* add_value_str();
  void add_value_str(const ::std::string& value);
  #if LANG_CXX11
  void add_value_str(::std::string&& value);
  #endif
  void add_value_str(const char* value);
  void add_value_str(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& value_str() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_value_str();

  // .tensorflow.DataType dtype = 1;
  void clear_dtype();
  static const int kDtypeFieldNumber = 1;
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.TFProfTensorProto)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedField< double > value_double_;
  mutable int _value_double_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > value_int64_;
  mutable int _value_int64_cached_byte_size_;
  ::google::protobuf::RepeatedPtrField< ::std::string> value_str_;
  int dtype_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class GraphNodeProto_InputShapesEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<GraphNodeProto_InputShapesEntry_DoNotUse, 
    ::google::protobuf::int32, ::tensorflow::TensorShapeProto,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<GraphNodeProto_InputShapesEntry_DoNotUse, 
    ::google::protobuf::int32, ::tensorflow::TensorShapeProto,
    ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  GraphNodeProto_InputShapesEntry_DoNotUse();
  GraphNodeProto_InputShapesEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const GraphNodeProto_InputShapesEntry_DoNotUse& other);
  static const GraphNodeProto_InputShapesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const GraphNodeProto_InputShapesEntry_DoNotUse*>(&_GraphNodeProto_InputShapesEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class GraphNodeProto : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.GraphNodeProto) */ {
 public:
  GraphNodeProto();
  virtual ~GraphNodeProto();

  GraphNodeProto(const GraphNodeProto& from);

  inline GraphNodeProto& operator=(const GraphNodeProto& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  GraphNodeProto(GraphNodeProto&& from) noexcept
    : GraphNodeProto() {
    *this = ::std::move(from);
  }

  inline GraphNodeProto& operator=(GraphNodeProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const GraphNodeProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GraphNodeProto* internal_default_instance() {
    return reinterpret_cast<const GraphNodeProto*>(
               &_GraphNodeProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void Swap(GraphNodeProto* other);
  friend void swap(GraphNodeProto& a, GraphNodeProto& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline GraphNodeProto* New() const final {
    return CreateMaybeMessage<GraphNodeProto>(NULL);
  }

  GraphNodeProto* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<GraphNodeProto>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const GraphNodeProto& from);
  void MergeFrom(const GraphNodeProto& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphNodeProto* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // repeated string devices = 10;
  int devices_size() const;
  void clear_devices();
  static const int kDevicesFieldNumber = 10;
  const ::std::string& devices(int index) const;
  ::std::string* mutable_devices(int index);
  void set_devices(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_devices(int index, ::std::string&& value);
  #endif
  void set_devices(int index, const char* value);
  void set_devices(int index, const char* value, size_t size);
  ::std::string* add_devices();
  void add_devices(const ::std::string& value);
  #if LANG_CXX11
  void add_devices(::std::string&& value);
  #endif
  void add_devices(const char* value);
  void add_devices(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& devices() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_devices();

  // repeated .tensorflow.TensorShapeProto shapes = 11;
  int shapes_size() const;
  void clear_shapes();
  static const int kShapesFieldNumber = 11;
  ::tensorflow::TensorShapeProto* mutable_shapes(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorShapeProto >*
      mutable_shapes();
  const ::tensorflow::TensorShapeProto& shapes(int index) const;
  ::tensorflow::TensorShapeProto* add_shapes();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorShapeProto >&
      shapes() const;

  // repeated .tensorflow.tfprof.GraphNodeProto children = 12;
  int children_size() const;
  void clear_children();
  static const int kChildrenFieldNumber = 12;
  ::tensorflow::tfprof::GraphNodeProto* mutable_children(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto >*
      mutable_children();
  const ::tensorflow::tfprof::GraphNodeProto& children(int index) const;
  ::tensorflow::tfprof::GraphNodeProto* add_children();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto >&
      children() const;

  // map<int32, .tensorflow.TensorShapeProto> input_shapes = 16;
  int input_shapes_size() const;
  void clear_input_shapes();
  static const int kInputShapesFieldNumber = 16;
  const ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::TensorShapeProto >&
      input_shapes() const;
  ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::TensorShapeProto >*
      mutable_input_shapes();

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // .tensorflow.tfprof.TFProfTensorProto tensor_value = 15;
  bool has_tensor_value() const;
  void clear_tensor_value();
  static const int kTensorValueFieldNumber = 15;
  private:
  const ::tensorflow::tfprof::TFProfTensorProto& _internal_tensor_value() const;
  public:
  const ::tensorflow::tfprof::TFProfTensorProto& tensor_value() const;
  ::tensorflow::tfprof::TFProfTensorProto* release_tensor_value();
  ::tensorflow::tfprof::TFProfTensorProto* mutable_tensor_value();
  void set_allocated_tensor_value(::tensorflow::tfprof::TFProfTensorProto* tensor_value);

  // int64 exec_micros = 2;
  void clear_exec_micros();
  static const int kExecMicrosFieldNumber = 2;
  ::google::protobuf::int64 exec_micros() const;
  void set_exec_micros(::google::protobuf::int64 value);

  // int64 requested_bytes = 3;
  void clear_requested_bytes();
  static const int kRequestedBytesFieldNumber = 3;
  ::google::protobuf::int64 requested_bytes() const;
  void set_requested_bytes(::google::protobuf::int64 value);

  // int64 parameters = 4;
  void clear_parameters();
  static const int kParametersFieldNumber = 4;
  ::google::protobuf::int64 parameters() const;
  void set_parameters(::google::protobuf::int64 value);

  // int64 total_exec_micros = 6;
  void clear_total_exec_micros();
  static const int kTotalExecMicrosFieldNumber = 6;
  ::google::protobuf::int64 total_exec_micros() const;
  void set_total_exec_micros(::google::protobuf::int64 value);

  // int64 total_requested_bytes = 7;
  void clear_total_requested_bytes();
  static const int kTotalRequestedBytesFieldNumber = 7;
  ::google::protobuf::int64 total_requested_bytes() const;
  void set_total_requested_bytes(::google::protobuf::int64 value);

  // int64 total_parameters = 8;
  void clear_total_parameters();
  static const int kTotalParametersFieldNumber = 8;
  ::google::protobuf::int64 total_parameters() const;
  void set_total_parameters(::google::protobuf::int64 value);

  // int64 float_ops = 13;
  void clear_float_ops();
  static const int kFloatOpsFieldNumber = 13;
  ::google::protobuf::int64 float_ops() const;
  void set_float_ops(::google::protobuf::int64 value);

  // int64 total_float_ops = 14;
  void clear_total_float_ops();
  static const int kTotalFloatOpsFieldNumber = 14;
  ::google::protobuf::int64 total_float_ops() const;
  void set_total_float_ops(::google::protobuf::int64 value);

  // int64 accelerator_exec_micros = 17;
  void clear_accelerator_exec_micros();
  static const int kAcceleratorExecMicrosFieldNumber = 17;
  ::google::protobuf::int64 accelerator_exec_micros() const;
  void set_accelerator_exec_micros(::google::protobuf::int64 value);

  // int64 cpu_exec_micros = 18;
  void clear_cpu_exec_micros();
  static const int kCpuExecMicrosFieldNumber = 18;
  ::google::protobuf::int64 cpu_exec_micros() const;
  void set_cpu_exec_micros(::google::protobuf::int64 value);

  // int64 total_accelerator_exec_micros = 19;
  void clear_total_accelerator_exec_micros();
  static const int kTotalAcceleratorExecMicrosFieldNumber = 19;
  ::google::protobuf::int64 total_accelerator_exec_micros() const;
  void set_total_accelerator_exec_micros(::google::protobuf::int64 value);

  // int64 total_cpu_exec_micros = 20;
  void clear_total_cpu_exec_micros();
  static const int kTotalCpuExecMicrosFieldNumber = 20;
  ::google::protobuf::int64 total_cpu_exec_micros() const;
  void set_total_cpu_exec_micros(::google::protobuf::int64 value);

  // int64 run_count = 21;
  void clear_run_count();
  static const int kRunCountFieldNumber = 21;
  ::google::protobuf::int64 run_count() const;
  void set_run_count(::google::protobuf::int64 value);

  // int64 total_run_count = 22;
  void clear_total_run_count();
  static const int kTotalRunCountFieldNumber = 22;
  ::google::protobuf::int64 total_run_count() const;
  void set_total_run_count(::google::protobuf::int64 value);

  // int64 total_definition_count = 23;
  void clear_total_definition_count();
  static const int kTotalDefinitionCountFieldNumber = 23;
  ::google::protobuf::int64 total_definition_count() const;
  void set_total_definition_count(::google::protobuf::int64 value);

  // int64 peak_bytes = 24;
  void clear_peak_bytes();
  static const int kPeakBytesFieldNumber = 24;
  ::google::protobuf::int64 peak_bytes() const;
  void set_peak_bytes(::google::protobuf::int64 value);

  // int64 residual_bytes = 25;
  void clear_residual_bytes();
  static const int kResidualBytesFieldNumber = 25;
  ::google::protobuf::int64 residual_bytes() const;
  void set_residual_bytes(::google::protobuf::int64 value);

  // int64 output_bytes = 26;
  void clear_output_bytes();
  static const int kOutputBytesFieldNumber = 26;
  ::google::protobuf::int64 output_bytes() const;
  void set_output_bytes(::google::protobuf::int64 value);

  // int64 total_peak_bytes = 27;
  void clear_total_peak_bytes();
  static const int kTotalPeakBytesFieldNumber = 27;
  ::google::protobuf::int64 total_peak_bytes() const;
  void set_total_peak_bytes(::google::protobuf::int64 value);

  // int64 total_residual_bytes = 28;
  void clear_total_residual_bytes();
  static const int kTotalResidualBytesFieldNumber = 28;
  ::google::protobuf::int64 total_residual_bytes() const;
  void set_total_residual_bytes(::google::protobuf::int64 value);

  // int64 total_output_bytes = 29;
  void clear_total_output_bytes();
  static const int kTotalOutputBytesFieldNumber = 29;
  ::google::protobuf::int64 total_output_bytes() const;
  void set_total_output_bytes(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.GraphNodeProto)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::std::string> devices_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorShapeProto > shapes_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto > children_;
  ::google::protobuf::internal::MapField<
      GraphNodeProto_InputShapesEntry_DoNotUse,
      ::google::protobuf::int32, ::tensorflow::TensorShapeProto,
      ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > input_shapes_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::tensorflow::tfprof::TFProfTensorProto* tensor_value_;
  ::google::protobuf::int64 exec_micros_;
  ::google::protobuf::int64 requested_bytes_;
  ::google::protobuf::int64 parameters_;
  ::google::protobuf::int64 total_exec_micros_;
  ::google::protobuf::int64 total_requested_bytes_;
  ::google::protobuf::int64 total_parameters_;
  ::google::protobuf::int64 float_ops_;
  ::google::protobuf::int64 total_float_ops_;
  ::google::protobuf::int64 accelerator_exec_micros_;
  ::google::protobuf::int64 cpu_exec_micros_;
  ::google::protobuf::int64 total_accelerator_exec_micros_;
  ::google::protobuf::int64 total_cpu_exec_micros_;
  ::google::protobuf::int64 run_count_;
  ::google::protobuf::int64 total_run_count_;
  ::google::protobuf::int64 total_definition_count_;
  ::google::protobuf::int64 peak_bytes_;
  ::google::protobuf::int64 residual_bytes_;
  ::google::protobuf::int64 output_bytes_;
  ::google::protobuf::int64 total_peak_bytes_;
  ::google::protobuf::int64 total_residual_bytes_;
  ::google::protobuf::int64 total_output_bytes_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class MultiGraphNodeProto : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.MultiGraphNodeProto) */ {
 public:
  MultiGraphNodeProto();
  virtual ~MultiGraphNodeProto();

  MultiGraphNodeProto(const MultiGraphNodeProto& from);

  inline MultiGraphNodeProto& operator=(const MultiGraphNodeProto& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  MultiGraphNodeProto(MultiGraphNodeProto&& from) noexcept
    : MultiGraphNodeProto() {
    *this = ::std::move(from);
  }

  inline MultiGraphNodeProto& operator=(MultiGraphNodeProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const MultiGraphNodeProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MultiGraphNodeProto* internal_default_instance() {
    return reinterpret_cast<const MultiGraphNodeProto*>(
               &_MultiGraphNodeProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void Swap(MultiGraphNodeProto* other);
  friend void swap(MultiGraphNodeProto& a, MultiGraphNodeProto& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline MultiGraphNodeProto* New() const final {
    return CreateMaybeMessage<MultiGraphNodeProto>(NULL);
  }

  MultiGraphNodeProto* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<MultiGraphNodeProto>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const MultiGraphNodeProto& from);
  void MergeFrom(const MultiGraphNodeProto& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MultiGraphNodeProto* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.tfprof.GraphNodeProto graph_nodes = 10;
  int graph_nodes_size() const;
  void clear_graph_nodes();
  static const int kGraphNodesFieldNumber = 10;
  ::tensorflow::tfprof::GraphNodeProto* mutable_graph_nodes(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto >*
      mutable_graph_nodes();
  const ::tensorflow::tfprof::GraphNodeProto& graph_nodes(int index) const;
  ::tensorflow::tfprof::GraphNodeProto* add_graph_nodes();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto >&
      graph_nodes() const;

  // repeated .tensorflow.tfprof.MultiGraphNodeProto children = 11;
  int children_size() const;
  void clear_children();
  static const int kChildrenFieldNumber = 11;
  ::tensorflow::tfprof::MultiGraphNodeProto* mutable_children(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::MultiGraphNodeProto >*
      mutable_children();
  const ::tensorflow::tfprof::MultiGraphNodeProto& children(int index) const;
  ::tensorflow::tfprof::MultiGraphNodeProto* add_children();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::MultiGraphNodeProto >&
      children() const;

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // int64 exec_micros = 2;
  void clear_exec_micros();
  static const int kExecMicrosFieldNumber = 2;
  ::google::protobuf::int64 exec_micros() const;
  void set_exec_micros(::google::protobuf::int64 value);

  // int64 requested_bytes = 3;
  void clear_requested_bytes();
  static const int kRequestedBytesFieldNumber = 3;
  ::google::protobuf::int64 requested_bytes() const;
  void set_requested_bytes(::google::protobuf::int64 value);

  // int64 parameters = 4;
  void clear_parameters();
  static const int kParametersFieldNumber = 4;
  ::google::protobuf::int64 parameters() const;
  void set_parameters(::google::protobuf::int64 value);

  // int64 float_ops = 5;
  void clear_float_ops();
  static const int kFloatOpsFieldNumber = 5;
  ::google::protobuf::int64 float_ops() const;
  void set_float_ops(::google::protobuf::int64 value);

  // int64 total_exec_micros = 6;
  void clear_total_exec_micros();
  static const int kTotalExecMicrosFieldNumber = 6;
  ::google::protobuf::int64 total_exec_micros() const;
  void set_total_exec_micros(::google::protobuf::int64 value);

  // int64 total_requested_bytes = 7;
  void clear_total_requested_bytes();
  static const int kTotalRequestedBytesFieldNumber = 7;
  ::google::protobuf::int64 total_requested_bytes() const;
  void set_total_requested_bytes(::google::protobuf::int64 value);

  // int64 total_parameters = 8;
  void clear_total_parameters();
  static const int kTotalParametersFieldNumber = 8;
  ::google::protobuf::int64 total_parameters() const;
  void set_total_parameters(::google::protobuf::int64 value);

  // int64 total_float_ops = 9;
  void clear_total_float_ops();
  static const int kTotalFloatOpsFieldNumber = 9;
  ::google::protobuf::int64 total_float_ops() const;
  void set_total_float_ops(::google::protobuf::int64 value);

  // int64 accelerator_exec_micros = 12;
  void clear_accelerator_exec_micros();
  static const int kAcceleratorExecMicrosFieldNumber = 12;
  ::google::protobuf::int64 accelerator_exec_micros() const;
  void set_accelerator_exec_micros(::google::protobuf::int64 value);

  // int64 cpu_exec_micros = 13;
  void clear_cpu_exec_micros();
  static const int kCpuExecMicrosFieldNumber = 13;
  ::google::protobuf::int64 cpu_exec_micros() const;
  void set_cpu_exec_micros(::google::protobuf::int64 value);

  // int64 total_accelerator_exec_micros = 14;
  void clear_total_accelerator_exec_micros();
  static const int kTotalAcceleratorExecMicrosFieldNumber = 14;
  ::google::protobuf::int64 total_accelerator_exec_micros() const;
  void set_total_accelerator_exec_micros(::google::protobuf::int64 value);

  // int64 total_cpu_exec_micros = 15;
  void clear_total_cpu_exec_micros();
  static const int kTotalCpuExecMicrosFieldNumber = 15;
  ::google::protobuf::int64 total_cpu_exec_micros() const;
  void set_total_cpu_exec_micros(::google::protobuf::int64 value);

  // int64 peak_bytes = 16;
  void clear_peak_bytes();
  static const int kPeakBytesFieldNumber = 16;
  ::google::protobuf::int64 peak_bytes() const;
  void set_peak_bytes(::google::protobuf::int64 value);

  // int64 residual_bytes = 17;
  void clear_residual_bytes();
  static const int kResidualBytesFieldNumber = 17;
  ::google::protobuf::int64 residual_bytes() const;
  void set_residual_bytes(::google::protobuf::int64 value);

  // int64 output_bytes = 18;
  void clear_output_bytes();
  static const int kOutputBytesFieldNumber = 18;
  ::google::protobuf::int64 output_bytes() const;
  void set_output_bytes(::google::protobuf::int64 value);

  // int64 total_peak_bytes = 19;
  void clear_total_peak_bytes();
  static const int kTotalPeakBytesFieldNumber = 19;
  ::google::protobuf::int64 total_peak_bytes() const;
  void set_total_peak_bytes(::google::protobuf::int64 value);

  // int64 total_residual_bytes = 20;
  void clear_total_residual_bytes();
  static const int kTotalResidualBytesFieldNumber = 20;
  ::google::protobuf::int64 total_residual_bytes() const;
  void set_total_residual_bytes(::google::protobuf::int64 value);

  // int64 total_output_bytes = 21;
  void clear_total_output_bytes();
  static const int kTotalOutputBytesFieldNumber = 21;
  ::google::protobuf::int64 total_output_bytes() const;
  void set_total_output_bytes(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.MultiGraphNodeProto)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto > graph_nodes_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::MultiGraphNodeProto > children_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::int64 exec_micros_;
  ::google::protobuf::int64 requested_bytes_;
  ::google::protobuf::int64 parameters_;
  ::google::protobuf::int64 float_ops_;
  ::google::protobuf::int64 total_exec_micros_;
  ::google::protobuf::int64 total_requested_bytes_;
  ::google::protobuf::int64 total_parameters_;
  ::google::protobuf::int64 total_float_ops_;
  ::google::protobuf::int64 accelerator_exec_micros_;
  ::google::protobuf::int64 cpu_exec_micros_;
  ::google::protobuf::int64 total_accelerator_exec_micros_;
  ::google::protobuf::int64 total_cpu_exec_micros_;
  ::google::protobuf::int64 peak_bytes_;
  ::google::protobuf::int64 residual_bytes_;
  ::google::protobuf::int64 output_bytes_;
  ::google::protobuf::int64 total_peak_bytes_;
  ::google::protobuf::int64 total_residual_bytes_;
  ::google::protobuf::int64 total_output_bytes_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class AdviceProto_CheckersEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<AdviceProto_CheckersEntry_DoNotUse, 
    ::std::string, ::tensorflow::tfprof::AdviceProto_Checker,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<AdviceProto_CheckersEntry_DoNotUse, 
    ::std::string, ::tensorflow::tfprof::AdviceProto_Checker,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  AdviceProto_CheckersEntry_DoNotUse();
  AdviceProto_CheckersEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const AdviceProto_CheckersEntry_DoNotUse& other);
  static const AdviceProto_CheckersEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const AdviceProto_CheckersEntry_DoNotUse*>(&_AdviceProto_CheckersEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class AdviceProto_Checker : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.AdviceProto.Checker) */ {
 public:
  AdviceProto_Checker();
  virtual ~AdviceProto_Checker();

  AdviceProto_Checker(const AdviceProto_Checker& from);

  inline AdviceProto_Checker& operator=(const AdviceProto_Checker& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  AdviceProto_Checker(AdviceProto_Checker&& from) noexcept
    : AdviceProto_Checker() {
    *this = ::std::move(from);
  }

  inline AdviceProto_Checker& operator=(AdviceProto_Checker&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const AdviceProto_Checker& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AdviceProto_Checker* internal_default_instance() {
    return reinterpret_cast<const AdviceProto_Checker*>(
               &_AdviceProto_Checker_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  void Swap(AdviceProto_Checker* other);
  friend void swap(AdviceProto_Checker& a, AdviceProto_Checker& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline AdviceProto_Checker* New() const final {
    return CreateMaybeMessage<AdviceProto_Checker>(NULL);
  }

  AdviceProto_Checker* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<AdviceProto_Checker>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const AdviceProto_Checker& from);
  void MergeFrom(const AdviceProto_Checker& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AdviceProto_Checker* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string reports = 2;
  int reports_size() const;
  void clear_reports();
  static const int kReportsFieldNumber = 2;
  const ::std::string& reports(int index) const;
  ::std::string* mutable_reports(int index);
  void set_reports(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_reports(int index, ::std::string&& value);
  #endif
  void set_reports(int index, const char* value);
  void set_reports(int index, const char* value, size_t size);
  ::std::string* add_reports();
  void add_reports(const ::std::string& value);
  #if LANG_CXX11
  void add_reports(::std::string&& value);
  #endif
  void add_reports(const char* value);
  void add_reports(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& reports() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_reports();

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.AdviceProto.Checker)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::std::string> reports_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class AdviceProto : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.AdviceProto) */ {
 public:
  AdviceProto();
  virtual ~AdviceProto();

  AdviceProto(const AdviceProto& from);

  inline AdviceProto& operator=(const AdviceProto& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  AdviceProto(AdviceProto&& from) noexcept
    : AdviceProto() {
    *this = ::std::move(from);
  }

  inline AdviceProto& operator=(AdviceProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const AdviceProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AdviceProto* internal_default_instance() {
    return reinterpret_cast<const AdviceProto*>(
               &_AdviceProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  void Swap(AdviceProto* other);
  friend void swap(AdviceProto& a, AdviceProto& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline AdviceProto* New() const final {
    return CreateMaybeMessage<AdviceProto>(NULL);
  }

  AdviceProto* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<AdviceProto>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const AdviceProto& from);
  void MergeFrom(const AdviceProto& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AdviceProto* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef AdviceProto_Checker Checker;

  // accessors -------------------------------------------------------

  // map<string, .tensorflow.tfprof.AdviceProto.Checker> checkers = 1;
  int checkers_size() const;
  void clear_checkers();
  static const int kCheckersFieldNumber = 1;
  const ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::AdviceProto_Checker >&
      checkers() const;
  ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::AdviceProto_Checker >*
      mutable_checkers();

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.AdviceProto)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::MapField<
      AdviceProto_CheckersEntry_DoNotUse,
      ::std::string, ::tensorflow::tfprof::AdviceProto_Checker,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > checkers_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TFProfTensorProto

// .tensorflow.DataType dtype = 1;
inline void TFProfTensorProto::clear_dtype() {
  dtype_ = 0;
}
inline ::tensorflow::DataType TFProfTensorProto::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.TFProfTensorProto.dtype)
  return static_cast< ::tensorflow::DataType >(dtype_);
}
inline void TFProfTensorProto::set_dtype(::tensorflow::DataType value) {
  
  dtype_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.TFProfTensorProto.dtype)
}

// repeated double value_double = 2;
inline int TFProfTensorProto::value_double_size() const {
  return value_double_.size();
}
inline void TFProfTensorProto::clear_value_double() {
  value_double_.Clear();
}
inline double TFProfTensorProto::value_double(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.TFProfTensorProto.value_double)
  return value_double_.Get(index);
}
inline void TFProfTensorProto::set_value_double(int index, double value) {
  value_double_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.TFProfTensorProto.value_double)
}
inline void TFProfTensorProto::add_value_double(double value) {
  value_double_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.TFProfTensorProto.value_double)
}
inline const ::google::protobuf::RepeatedField< double >&
TFProfTensorProto::value_double() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.TFProfTensorProto.value_double)
  return value_double_;
}
inline ::google::protobuf::RepeatedField< double >*
TFProfTensorProto::mutable_value_double() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.TFProfTensorProto.value_double)
  return &value_double_;
}

// repeated int64 value_int64 = 3;
inline int TFProfTensorProto::value_int64_size() const {
  return value_int64_.size();
}
inline void TFProfTensorProto::clear_value_int64() {
  value_int64_.Clear();
}
inline ::google::protobuf::int64 TFProfTensorProto::value_int64(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.TFProfTensorProto.value_int64)
  return value_int64_.Get(index);
}
inline void TFProfTensorProto::set_value_int64(int index, ::google::protobuf::int64 value) {
  value_int64_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.TFProfTensorProto.value_int64)
}
inline void TFProfTensorProto::add_value_int64(::google::protobuf::int64 value) {
  value_int64_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.TFProfTensorProto.value_int64)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
TFProfTensorProto::value_int64() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.TFProfTensorProto.value_int64)
  return value_int64_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
TFProfTensorProto::mutable_value_int64() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.TFProfTensorProto.value_int64)
  return &value_int64_;
}

// repeated string value_str = 4;
inline int TFProfTensorProto::value_str_size() const {
  return value_str_.size();
}
inline void TFProfTensorProto::clear_value_str() {
  value_str_.Clear();
}
inline const ::std::string& TFProfTensorProto::value_str(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.TFProfTensorProto.value_str)
  return value_str_.Get(index);
}
inline ::std::string* TFProfTensorProto::mutable_value_str(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.TFProfTensorProto.value_str)
  return value_str_.Mutable(index);
}
inline void TFProfTensorProto::set_value_str(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.TFProfTensorProto.value_str)
  value_str_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void TFProfTensorProto::set_value_str(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.TFProfTensorProto.value_str)
  value_str_.Mutable(index)->assign(std::move(value));
}
#endif
inline void TFProfTensorProto::set_value_str(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  value_str_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.TFProfTensorProto.value_str)
}
inline void TFProfTensorProto::set_value_str(int index, const char* value, size_t size) {
  value_str_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.TFProfTensorProto.value_str)
}
inline ::std::string* TFProfTensorProto::add_value_str() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.TFProfTensorProto.value_str)
  return value_str_.Add();
}
inline void TFProfTensorProto::add_value_str(const ::std::string& value) {
  value_str_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.TFProfTensorProto.value_str)
}
#if LANG_CXX11
inline void TFProfTensorProto::add_value_str(::std::string&& value) {
  value_str_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.TFProfTensorProto.value_str)
}
#endif
inline void TFProfTensorProto::add_value_str(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  value_str_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.TFProfTensorProto.value_str)
}
inline void TFProfTensorProto::add_value_str(const char* value, size_t size) {
  value_str_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.TFProfTensorProto.value_str)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
TFProfTensorProto::value_str() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.TFProfTensorProto.value_str)
  return value_str_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
TFProfTensorProto::mutable_value_str() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.TFProfTensorProto.value_str)
  return &value_str_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// GraphNodeProto

// string name = 1;
inline void GraphNodeProto::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& GraphNodeProto::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.name)
  return name_.GetNoArena();
}
inline void GraphNodeProto::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.name)
}
#if LANG_CXX11
inline void GraphNodeProto::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tfprof.GraphNodeProto.name)
}
#endif
inline void GraphNodeProto::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.GraphNodeProto.name)
}
inline void GraphNodeProto::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.GraphNodeProto.name)
}
inline ::std::string* GraphNodeProto::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.GraphNodeProto.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* GraphNodeProto::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.GraphNodeProto.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void GraphNodeProto::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.GraphNodeProto.name)
}

// .tensorflow.tfprof.TFProfTensorProto tensor_value = 15;
inline bool GraphNodeProto::has_tensor_value() const {
  return this != internal_default_instance() && tensor_value_ != NULL;
}
inline void GraphNodeProto::clear_tensor_value() {
  if (GetArenaNoVirtual() == NULL && tensor_value_ != NULL) {
    delete tensor_value_;
  }
  tensor_value_ = NULL;
}
inline const ::tensorflow::tfprof::TFProfTensorProto& GraphNodeProto::_internal_tensor_value() const {
  return *tensor_value_;
}
inline const ::tensorflow::tfprof::TFProfTensorProto& GraphNodeProto::tensor_value() const {
  const ::tensorflow::tfprof::TFProfTensorProto* p = tensor_value_;
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.tensor_value)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::tfprof::TFProfTensorProto*>(
      &::tensorflow::tfprof::_TFProfTensorProto_default_instance_);
}
inline ::tensorflow::tfprof::TFProfTensorProto* GraphNodeProto::release_tensor_value() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.GraphNodeProto.tensor_value)
  
  ::tensorflow::tfprof::TFProfTensorProto* temp = tensor_value_;
  tensor_value_ = NULL;
  return temp;
}
inline ::tensorflow::tfprof::TFProfTensorProto* GraphNodeProto::mutable_tensor_value() {
  
  if (tensor_value_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::tfprof::TFProfTensorProto>(GetArenaNoVirtual());
    tensor_value_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.GraphNodeProto.tensor_value)
  return tensor_value_;
}
inline void GraphNodeProto::set_allocated_tensor_value(::tensorflow::tfprof::TFProfTensorProto* tensor_value) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete tensor_value_;
  }
  if (tensor_value) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      tensor_value = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, tensor_value, submessage_arena);
    }
    
  } else {
    
  }
  tensor_value_ = tensor_value;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.GraphNodeProto.tensor_value)
}

// int64 run_count = 21;
inline void GraphNodeProto::clear_run_count() {
  run_count_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GraphNodeProto::run_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.run_count)
  return run_count_;
}
inline void GraphNodeProto::set_run_count(::google::protobuf::int64 value) {
  
  run_count_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.run_count)
}

// int64 exec_micros = 2;
inline void GraphNodeProto::clear_exec_micros() {
  exec_micros_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GraphNodeProto::exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.exec_micros)
  return exec_micros_;
}
inline void GraphNodeProto::set_exec_micros(::google::protobuf::int64 value) {
  
  exec_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.exec_micros)
}

// int64 accelerator_exec_micros = 17;
inline void GraphNodeProto::clear_accelerator_exec_micros() {
  accelerator_exec_micros_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GraphNodeProto::accelerator_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.accelerator_exec_micros)
  return accelerator_exec_micros_;
}
inline void GraphNodeProto::set_accelerator_exec_micros(::google::protobuf::int64 value) {
  
  accelerator_exec_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.accelerator_exec_micros)
}

// int64 cpu_exec_micros = 18;
inline void GraphNodeProto::clear_cpu_exec_micros() {
  cpu_exec_micros_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GraphNodeProto::cpu_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.cpu_exec_micros)
  return cpu_exec_micros_;
}
inline void GraphNodeProto::set_cpu_exec_micros(::google::protobuf::int64 value) {
  
  cpu_exec_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.cpu_exec_micros)
}

// int64 requested_bytes = 3;
inline void GraphNodeProto::clear_requested_bytes() {
  requested_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GraphNodeProto::requested_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.requested_bytes)
  return requested_bytes_;
}
inline void GraphNodeProto::set_requested_bytes(::google::protobuf::int64 value) {
  
  requested_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.requested_bytes)
}

// int64 peak_bytes = 24;
inline void GraphNodeProto::clear_peak_bytes() {
  peak_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GraphNodeProto::peak_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.peak_bytes)
  return peak_bytes_;
}
inline void GraphNodeProto::set_peak_bytes(::google::protobuf::int64 value) {
  
  peak_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.peak_bytes)
}

// int64 residual_bytes = 25;
inline void GraphNodeProto::clear_residual_bytes() {
  residual_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GraphNodeProto::residual_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.residual_bytes)
  return residual_bytes_;
}
inline void GraphNodeProto::set_residual_bytes(::google::protobuf::int64 value) {
  
  residual_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.residual_bytes)
}

// int64 output_bytes = 26;
inline void GraphNodeProto::clear_output_bytes() {
  output_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GraphNodeProto::output_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.output_bytes)
  return output_bytes_;
}
inline void GraphNodeProto::set_output_bytes(::google::protobuf::int64 value) {
  
  output_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.output_bytes)
}

// int64 parameters = 4;
inline void GraphNodeProto::clear_parameters() {
  parameters_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GraphNodeProto::parameters() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.parameters)
  return parameters_;
}
inline void GraphNodeProto::set_parameters(::google::protobuf::int64 value) {
  
  parameters_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.parameters)
}

// int64 float_ops = 13;
inline void GraphNodeProto::clear_float_ops() {
  float_ops_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GraphNodeProto::float_ops() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.float_ops)
  return float_ops_;
}
inline void GraphNodeProto::set_float_ops(::google::protobuf::int64 value) {
  
  float_ops_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.float_ops)
}

// repeated string devices = 10;
inline int GraphNodeProto::devices_size() const {
  return devices_.size();
}
inline void GraphNodeProto::clear_devices() {
  devices_.Clear();
}
inline const ::std::string& GraphNodeProto::devices(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.devices)
  return devices_.Get(index);
}
inline ::std::string* GraphNodeProto::mutable_devices(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.GraphNodeProto.devices)
  return devices_.Mutable(index);
}
inline void GraphNodeProto::set_devices(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.devices)
  devices_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void GraphNodeProto::set_devices(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.devices)
  devices_.Mutable(index)->assign(std::move(value));
}
#endif
inline void GraphNodeProto::set_devices(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  devices_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.GraphNodeProto.devices)
}
inline void GraphNodeProto::set_devices(int index, const char* value, size_t size) {
  devices_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.GraphNodeProto.devices)
}
inline ::std::string* GraphNodeProto::add_devices() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.GraphNodeProto.devices)
  return devices_.Add();
}
inline void GraphNodeProto::add_devices(const ::std::string& value) {
  devices_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.GraphNodeProto.devices)
}
#if LANG_CXX11
inline void GraphNodeProto::add_devices(::std::string&& value) {
  devices_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.GraphNodeProto.devices)
}
#endif
inline void GraphNodeProto::add_devices(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  devices_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.GraphNodeProto.devices)
}
inline void GraphNodeProto::add_devices(const char* value, size_t size) {
  devices_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.GraphNodeProto.devices)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
GraphNodeProto::devices() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.GraphNodeProto.devices)
  return devices_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
GraphNodeProto::mutable_devices() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.GraphNodeProto.devices)
  return &devices_;
}

// int64 total_definition_count = 23;
inline void GraphNodeProto::clear_total_definition_count() {
  total_definition_count_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GraphNodeProto::total_definition_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_definition_count)
  return total_definition_count_;
}
inline void GraphNodeProto::set_total_definition_count(::google::protobuf::int64 value) {
  
  total_definition_count_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_definition_count)
}

// int64 total_run_count = 22;
inline void GraphNodeProto::clear_total_run_count() {
  total_run_count_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GraphNodeProto::total_run_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_run_count)
  return total_run_count_;
}
inline void GraphNodeProto::set_total_run_count(::google::protobuf::int64 value) {
  
  total_run_count_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_run_count)
}

// int64 total_exec_micros = 6;
inline void GraphNodeProto::clear_total_exec_micros() {
  total_exec_micros_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GraphNodeProto::total_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_exec_micros)
  return total_exec_micros_;
}
inline void GraphNodeProto::set_total_exec_micros(::google::protobuf::int64 value) {
  
  total_exec_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_exec_micros)
}

// int64 total_accelerator_exec_micros = 19;
inline void GraphNodeProto::clear_total_accelerator_exec_micros() {
  total_accelerator_exec_micros_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GraphNodeProto::total_accelerator_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_accelerator_exec_micros)
  return total_accelerator_exec_micros_;
}
inline void GraphNodeProto::set_total_accelerator_exec_micros(::google::protobuf::int64 value) {
  
  total_accelerator_exec_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_accelerator_exec_micros)
}

// int64 total_cpu_exec_micros = 20;
inline void GraphNodeProto::clear_total_cpu_exec_micros() {
  total_cpu_exec_micros_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GraphNodeProto::total_cpu_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_cpu_exec_micros)
  return total_cpu_exec_micros_;
}
inline void GraphNodeProto::set_total_cpu_exec_micros(::google::protobuf::int64 value) {
  
  total_cpu_exec_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_cpu_exec_micros)
}

// int64 total_requested_bytes = 7;
inline void GraphNodeProto::clear_total_requested_bytes() {
  total_requested_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GraphNodeProto::total_requested_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_requested_bytes)
  return total_requested_bytes_;
}
inline void GraphNodeProto::set_total_requested_bytes(::google::protobuf::int64 value) {
  
  total_requested_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_requested_bytes)
}

// int64 total_peak_bytes = 27;
inline void GraphNodeProto::clear_total_peak_bytes() {
  total_peak_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GraphNodeProto::total_peak_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_peak_bytes)
  return total_peak_bytes_;
}
inline void GraphNodeProto::set_total_peak_bytes(::google::protobuf::int64 value) {
  
  total_peak_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_peak_bytes)
}

// int64 total_residual_bytes = 28;
inline void GraphNodeProto::clear_total_residual_bytes() {
  total_residual_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GraphNodeProto::total_residual_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_residual_bytes)
  return total_residual_bytes_;
}
inline void GraphNodeProto::set_total_residual_bytes(::google::protobuf::int64 value) {
  
  total_residual_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_residual_bytes)
}

// int64 total_output_bytes = 29;
inline void GraphNodeProto::clear_total_output_bytes() {
  total_output_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GraphNodeProto::total_output_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_output_bytes)
  return total_output_bytes_;
}
inline void GraphNodeProto::set_total_output_bytes(::google::protobuf::int64 value) {
  
  total_output_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_output_bytes)
}

// int64 total_parameters = 8;
inline void GraphNodeProto::clear_total_parameters() {
  total_parameters_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GraphNodeProto::total_parameters() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_parameters)
  return total_parameters_;
}
inline void GraphNodeProto::set_total_parameters(::google::protobuf::int64 value) {
  
  total_parameters_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_parameters)
}

// int64 total_float_ops = 14;
inline void GraphNodeProto::clear_total_float_ops() {
  total_float_ops_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GraphNodeProto::total_float_ops() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_float_ops)
  return total_float_ops_;
}
inline void GraphNodeProto::set_total_float_ops(::google::protobuf::int64 value) {
  
  total_float_ops_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_float_ops)
}

// repeated .tensorflow.TensorShapeProto shapes = 11;
inline int GraphNodeProto::shapes_size() const {
  return shapes_.size();
}
inline ::tensorflow::TensorShapeProto* GraphNodeProto::mutable_shapes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.GraphNodeProto.shapes)
  return shapes_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorShapeProto >*
GraphNodeProto::mutable_shapes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.GraphNodeProto.shapes)
  return &shapes_;
}
inline const ::tensorflow::TensorShapeProto& GraphNodeProto::shapes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.shapes)
  return shapes_.Get(index);
}
inline ::tensorflow::TensorShapeProto* GraphNodeProto::add_shapes() {
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.GraphNodeProto.shapes)
  return shapes_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorShapeProto >&
GraphNodeProto::shapes() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.GraphNodeProto.shapes)
  return shapes_;
}

// map<int32, .tensorflow.TensorShapeProto> input_shapes = 16;
inline int GraphNodeProto::input_shapes_size() const {
  return input_shapes_.size();
}
inline const ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::TensorShapeProto >&
GraphNodeProto::input_shapes() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.GraphNodeProto.input_shapes)
  return input_shapes_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::TensorShapeProto >*
GraphNodeProto::mutable_input_shapes() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.GraphNodeProto.input_shapes)
  return input_shapes_.MutableMap();
}

// repeated .tensorflow.tfprof.GraphNodeProto children = 12;
inline int GraphNodeProto::children_size() const {
  return children_.size();
}
inline void GraphNodeProto::clear_children() {
  children_.Clear();
}
inline ::tensorflow::tfprof::GraphNodeProto* GraphNodeProto::mutable_children(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.GraphNodeProto.children)
  return children_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto >*
GraphNodeProto::mutable_children() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.GraphNodeProto.children)
  return &children_;
}
inline const ::tensorflow::tfprof::GraphNodeProto& GraphNodeProto::children(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.children)
  return children_.Get(index);
}
inline ::tensorflow::tfprof::GraphNodeProto* GraphNodeProto::add_children() {
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.GraphNodeProto.children)
  return children_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto >&
GraphNodeProto::children() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.GraphNodeProto.children)
  return children_;
}

// -------------------------------------------------------------------

// MultiGraphNodeProto

// string name = 1;
inline void MultiGraphNodeProto::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MultiGraphNodeProto::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.name)
  return name_.GetNoArena();
}
inline void MultiGraphNodeProto::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.name)
}
#if LANG_CXX11
inline void MultiGraphNodeProto::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tfprof.MultiGraphNodeProto.name)
}
#endif
inline void MultiGraphNodeProto::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.MultiGraphNodeProto.name)
}
inline void MultiGraphNodeProto::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.MultiGraphNodeProto.name)
}
inline ::std::string* MultiGraphNodeProto::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.MultiGraphNodeProto.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MultiGraphNodeProto::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.MultiGraphNodeProto.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MultiGraphNodeProto::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.MultiGraphNodeProto.name)
}

// int64 exec_micros = 2;
inline void MultiGraphNodeProto::clear_exec_micros() {
  exec_micros_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MultiGraphNodeProto::exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.exec_micros)
  return exec_micros_;
}
inline void MultiGraphNodeProto::set_exec_micros(::google::protobuf::int64 value) {
  
  exec_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.exec_micros)
}

// int64 accelerator_exec_micros = 12;
inline void MultiGraphNodeProto::clear_accelerator_exec_micros() {
  accelerator_exec_micros_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MultiGraphNodeProto::accelerator_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.accelerator_exec_micros)
  return accelerator_exec_micros_;
}
inline void MultiGraphNodeProto::set_accelerator_exec_micros(::google::protobuf::int64 value) {
  
  accelerator_exec_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.accelerator_exec_micros)
}

// int64 cpu_exec_micros = 13;
inline void MultiGraphNodeProto::clear_cpu_exec_micros() {
  cpu_exec_micros_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MultiGraphNodeProto::cpu_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.cpu_exec_micros)
  return cpu_exec_micros_;
}
inline void MultiGraphNodeProto::set_cpu_exec_micros(::google::protobuf::int64 value) {
  
  cpu_exec_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.cpu_exec_micros)
}

// int64 requested_bytes = 3;
inline void MultiGraphNodeProto::clear_requested_bytes() {
  requested_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MultiGraphNodeProto::requested_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.requested_bytes)
  return requested_bytes_;
}
inline void MultiGraphNodeProto::set_requested_bytes(::google::protobuf::int64 value) {
  
  requested_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.requested_bytes)
}

// int64 peak_bytes = 16;
inline void MultiGraphNodeProto::clear_peak_bytes() {
  peak_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MultiGraphNodeProto::peak_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.peak_bytes)
  return peak_bytes_;
}
inline void MultiGraphNodeProto::set_peak_bytes(::google::protobuf::int64 value) {
  
  peak_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.peak_bytes)
}

// int64 residual_bytes = 17;
inline void MultiGraphNodeProto::clear_residual_bytes() {
  residual_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MultiGraphNodeProto::residual_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.residual_bytes)
  return residual_bytes_;
}
inline void MultiGraphNodeProto::set_residual_bytes(::google::protobuf::int64 value) {
  
  residual_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.residual_bytes)
}

// int64 output_bytes = 18;
inline void MultiGraphNodeProto::clear_output_bytes() {
  output_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MultiGraphNodeProto::output_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.output_bytes)
  return output_bytes_;
}
inline void MultiGraphNodeProto::set_output_bytes(::google::protobuf::int64 value) {
  
  output_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.output_bytes)
}

// int64 parameters = 4;
inline void MultiGraphNodeProto::clear_parameters() {
  parameters_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MultiGraphNodeProto::parameters() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.parameters)
  return parameters_;
}
inline void MultiGraphNodeProto::set_parameters(::google::protobuf::int64 value) {
  
  parameters_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.parameters)
}

// int64 float_ops = 5;
inline void MultiGraphNodeProto::clear_float_ops() {
  float_ops_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MultiGraphNodeProto::float_ops() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.float_ops)
  return float_ops_;
}
inline void MultiGraphNodeProto::set_float_ops(::google::protobuf::int64 value) {
  
  float_ops_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.float_ops)
}

// int64 total_exec_micros = 6;
inline void MultiGraphNodeProto::clear_total_exec_micros() {
  total_exec_micros_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MultiGraphNodeProto::total_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.total_exec_micros)
  return total_exec_micros_;
}
inline void MultiGraphNodeProto::set_total_exec_micros(::google::protobuf::int64 value) {
  
  total_exec_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.total_exec_micros)
}

// int64 total_accelerator_exec_micros = 14;
inline void MultiGraphNodeProto::clear_total_accelerator_exec_micros() {
  total_accelerator_exec_micros_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MultiGraphNodeProto::total_accelerator_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.total_accelerator_exec_micros)
  return total_accelerator_exec_micros_;
}
inline void MultiGraphNodeProto::set_total_accelerator_exec_micros(::google::protobuf::int64 value) {
  
  total_accelerator_exec_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.total_accelerator_exec_micros)
}

// int64 total_cpu_exec_micros = 15;
inline void MultiGraphNodeProto::clear_total_cpu_exec_micros() {
  total_cpu_exec_micros_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MultiGraphNodeProto::total_cpu_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.total_cpu_exec_micros)
  return total_cpu_exec_micros_;
}
inline void MultiGraphNodeProto::set_total_cpu_exec_micros(::google::protobuf::int64 value) {
  
  total_cpu_exec_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.total_cpu_exec_micros)
}

// int64 total_requested_bytes = 7;
inline void MultiGraphNodeProto::clear_total_requested_bytes() {
  total_requested_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MultiGraphNodeProto::total_requested_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.total_requested_bytes)
  return total_requested_bytes_;
}
inline void MultiGraphNodeProto::set_total_requested_bytes(::google::protobuf::int64 value) {
  
  total_requested_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.total_requested_bytes)
}

// int64 total_peak_bytes = 19;
inline void MultiGraphNodeProto::clear_total_peak_bytes() {
  total_peak_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MultiGraphNodeProto::total_peak_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.total_peak_bytes)
  return total_peak_bytes_;
}
inline void MultiGraphNodeProto::set_total_peak_bytes(::google::protobuf::int64 value) {
  
  total_peak_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.total_peak_bytes)
}

// int64 total_residual_bytes = 20;
inline void MultiGraphNodeProto::clear_total_residual_bytes() {
  total_residual_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MultiGraphNodeProto::total_residual_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.total_residual_bytes)
  return total_residual_bytes_;
}
inline void MultiGraphNodeProto::set_total_residual_bytes(::google::protobuf::int64 value) {
  
  total_residual_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.total_residual_bytes)
}

// int64 total_output_bytes = 21;
inline void MultiGraphNodeProto::clear_total_output_bytes() {
  total_output_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MultiGraphNodeProto::total_output_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.total_output_bytes)
  return total_output_bytes_;
}
inline void MultiGraphNodeProto::set_total_output_bytes(::google::protobuf::int64 value) {
  
  total_output_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.total_output_bytes)
}

// int64 total_parameters = 8;
inline void MultiGraphNodeProto::clear_total_parameters() {
  total_parameters_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MultiGraphNodeProto::total_parameters() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.total_parameters)
  return total_parameters_;
}
inline void MultiGraphNodeProto::set_total_parameters(::google::protobuf::int64 value) {
  
  total_parameters_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.total_parameters)
}

// int64 total_float_ops = 9;
inline void MultiGraphNodeProto::clear_total_float_ops() {
  total_float_ops_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MultiGraphNodeProto::total_float_ops() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.total_float_ops)
  return total_float_ops_;
}
inline void MultiGraphNodeProto::set_total_float_ops(::google::protobuf::int64 value) {
  
  total_float_ops_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.total_float_ops)
}

// repeated .tensorflow.tfprof.GraphNodeProto graph_nodes = 10;
inline int MultiGraphNodeProto::graph_nodes_size() const {
  return graph_nodes_.size();
}
inline void MultiGraphNodeProto::clear_graph_nodes() {
  graph_nodes_.Clear();
}
inline ::tensorflow::tfprof::GraphNodeProto* MultiGraphNodeProto::mutable_graph_nodes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.MultiGraphNodeProto.graph_nodes)
  return graph_nodes_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto >*
MultiGraphNodeProto::mutable_graph_nodes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.MultiGraphNodeProto.graph_nodes)
  return &graph_nodes_;
}
inline const ::tensorflow::tfprof::GraphNodeProto& MultiGraphNodeProto::graph_nodes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.graph_nodes)
  return graph_nodes_.Get(index);
}
inline ::tensorflow::tfprof::GraphNodeProto* MultiGraphNodeProto::add_graph_nodes() {
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.MultiGraphNodeProto.graph_nodes)
  return graph_nodes_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto >&
MultiGraphNodeProto::graph_nodes() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.MultiGraphNodeProto.graph_nodes)
  return graph_nodes_;
}

// repeated .tensorflow.tfprof.MultiGraphNodeProto children = 11;
inline int MultiGraphNodeProto::children_size() const {
  return children_.size();
}
inline void MultiGraphNodeProto::clear_children() {
  children_.Clear();
}
inline ::tensorflow::tfprof::MultiGraphNodeProto* MultiGraphNodeProto::mutable_children(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.MultiGraphNodeProto.children)
  return children_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::MultiGraphNodeProto >*
MultiGraphNodeProto::mutable_children() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.MultiGraphNodeProto.children)
  return &children_;
}
inline const ::tensorflow::tfprof::MultiGraphNodeProto& MultiGraphNodeProto::children(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.children)
  return children_.Get(index);
}
inline ::tensorflow::tfprof::MultiGraphNodeProto* MultiGraphNodeProto::add_children() {
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.MultiGraphNodeProto.children)
  return children_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::tfprof::MultiGraphNodeProto >&
MultiGraphNodeProto::children() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.MultiGraphNodeProto.children)
  return children_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// AdviceProto_Checker

// repeated string reports = 2;
inline int AdviceProto_Checker::reports_size() const {
  return reports_.size();
}
inline void AdviceProto_Checker::clear_reports() {
  reports_.Clear();
}
inline const ::std::string& AdviceProto_Checker::reports(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.AdviceProto.Checker.reports)
  return reports_.Get(index);
}
inline ::std::string* AdviceProto_Checker::mutable_reports(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.AdviceProto.Checker.reports)
  return reports_.Mutable(index);
}
inline void AdviceProto_Checker::set_reports(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.AdviceProto.Checker.reports)
  reports_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void AdviceProto_Checker::set_reports(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.AdviceProto.Checker.reports)
  reports_.Mutable(index)->assign(std::move(value));
}
#endif
inline void AdviceProto_Checker::set_reports(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  reports_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.AdviceProto.Checker.reports)
}
inline void AdviceProto_Checker::set_reports(int index, const char* value, size_t size) {
  reports_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.AdviceProto.Checker.reports)
}
inline ::std::string* AdviceProto_Checker::add_reports() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.AdviceProto.Checker.reports)
  return reports_.Add();
}
inline void AdviceProto_Checker::add_reports(const ::std::string& value) {
  reports_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.AdviceProto.Checker.reports)
}
#if LANG_CXX11
inline void AdviceProto_Checker::add_reports(::std::string&& value) {
  reports_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.AdviceProto.Checker.reports)
}
#endif
inline void AdviceProto_Checker::add_reports(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  reports_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.AdviceProto.Checker.reports)
}
inline void AdviceProto_Checker::add_reports(const char* value, size_t size) {
  reports_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.AdviceProto.Checker.reports)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
AdviceProto_Checker::reports() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.AdviceProto.Checker.reports)
  return reports_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
AdviceProto_Checker::mutable_reports() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.AdviceProto.Checker.reports)
  return &reports_;
}

// -------------------------------------------------------------------

// AdviceProto

// map<string, .tensorflow.tfprof.AdviceProto.Checker> checkers = 1;
inline int AdviceProto::checkers_size() const {
  return checkers_.size();
}
inline void AdviceProto::clear_checkers() {
  checkers_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::AdviceProto_Checker >&
AdviceProto::checkers() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.AdviceProto.checkers)
  return checkers_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::AdviceProto_Checker >*
AdviceProto::mutable_checkers() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.AdviceProto.checkers)
  return checkers_.MutableMap();
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tfprof
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto

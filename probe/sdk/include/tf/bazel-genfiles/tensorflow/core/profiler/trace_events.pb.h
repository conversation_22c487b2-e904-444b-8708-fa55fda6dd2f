// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/profiler/trace_events.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto 

namespace protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[7];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto
namespace tensorflow {
namespace profiler {
class Device;
class DeviceDefaultTypeInternal;
extern DeviceDefaultTypeInternal _Device_default_instance_;
class Device_ResourcesEntry_DoNotUse;
class Device_ResourcesEntry_DoNotUseDefaultTypeInternal;
extern Device_ResourcesEntry_DoNotUseDefaultTypeInternal _Device_ResourcesEntry_DoNotUse_default_instance_;
class Resource;
class ResourceDefaultTypeInternal;
extern ResourceDefaultTypeInternal _Resource_default_instance_;
class Trace;
class TraceDefaultTypeInternal;
extern TraceDefaultTypeInternal _Trace_default_instance_;
class TraceEvent;
class TraceEventDefaultTypeInternal;
extern TraceEventDefaultTypeInternal _TraceEvent_default_instance_;
class TraceEvent_ArgsEntry_DoNotUse;
class TraceEvent_ArgsEntry_DoNotUseDefaultTypeInternal;
extern TraceEvent_ArgsEntry_DoNotUseDefaultTypeInternal _TraceEvent_ArgsEntry_DoNotUse_default_instance_;
class Trace_DevicesEntry_DoNotUse;
class Trace_DevicesEntry_DoNotUseDefaultTypeInternal;
extern Trace_DevicesEntry_DoNotUseDefaultTypeInternal _Trace_DevicesEntry_DoNotUse_default_instance_;
}  // namespace profiler
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::profiler::Device* Arena::CreateMaybeMessage<::tensorflow::profiler::Device>(Arena*);
template<> ::tensorflow::profiler::Device_ResourcesEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::profiler::Device_ResourcesEntry_DoNotUse>(Arena*);
template<> ::tensorflow::profiler::Resource* Arena::CreateMaybeMessage<::tensorflow::profiler::Resource>(Arena*);
template<> ::tensorflow::profiler::Trace* Arena::CreateMaybeMessage<::tensorflow::profiler::Trace>(Arena*);
template<> ::tensorflow::profiler::TraceEvent* Arena::CreateMaybeMessage<::tensorflow::profiler::TraceEvent>(Arena*);
template<> ::tensorflow::profiler::TraceEvent_ArgsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::profiler::TraceEvent_ArgsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::profiler::Trace_DevicesEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::profiler::Trace_DevicesEntry_DoNotUse>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {
namespace profiler {

// ===================================================================

class Trace_DevicesEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<Trace_DevicesEntry_DoNotUse, 
    ::google::protobuf::uint32, ::tensorflow::profiler::Device,
    ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<Trace_DevicesEntry_DoNotUse, 
    ::google::protobuf::uint32, ::tensorflow::profiler::Device,
    ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  Trace_DevicesEntry_DoNotUse();
  Trace_DevicesEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const Trace_DevicesEntry_DoNotUse& other);
  static const Trace_DevicesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const Trace_DevicesEntry_DoNotUse*>(&_Trace_DevicesEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class Trace : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.profiler.Trace) */ {
 public:
  Trace();
  virtual ~Trace();

  Trace(const Trace& from);

  inline Trace& operator=(const Trace& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Trace(Trace&& from) noexcept
    : Trace() {
    *this = ::std::move(from);
  }

  inline Trace& operator=(Trace&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Trace& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Trace* internal_default_instance() {
    return reinterpret_cast<const Trace*>(
               &_Trace_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void Swap(Trace* other);
  friend void swap(Trace& a, Trace& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Trace* New() const final {
    return CreateMaybeMessage<Trace>(NULL);
  }

  Trace* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Trace>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Trace& from);
  void MergeFrom(const Trace& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Trace* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<uint32, .tensorflow.profiler.Device> devices = 1;
  int devices_size() const;
  void clear_devices();
  static const int kDevicesFieldNumber = 1;
  const ::google::protobuf::Map< ::google::protobuf::uint32, ::tensorflow::profiler::Device >&
      devices() const;
  ::google::protobuf::Map< ::google::protobuf::uint32, ::tensorflow::profiler::Device >*
      mutable_devices();

  // repeated .tensorflow.profiler.TraceEvent trace_events = 4;
  int trace_events_size() const;
  void clear_trace_events();
  static const int kTraceEventsFieldNumber = 4;
  ::tensorflow::profiler::TraceEvent* mutable_trace_events(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::profiler::TraceEvent >*
      mutable_trace_events();
  const ::tensorflow::profiler::TraceEvent& trace_events(int index) const;
  ::tensorflow::profiler::TraceEvent* add_trace_events();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::profiler::TraceEvent >&
      trace_events() const;

  // @@protoc_insertion_point(class_scope:tensorflow.profiler.Trace)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::MapField<
      Trace_DevicesEntry_DoNotUse,
      ::google::protobuf::uint32, ::tensorflow::profiler::Device,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > devices_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::profiler::TraceEvent > trace_events_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Device_ResourcesEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<Device_ResourcesEntry_DoNotUse, 
    ::google::protobuf::uint32, ::tensorflow::profiler::Resource,
    ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<Device_ResourcesEntry_DoNotUse, 
    ::google::protobuf::uint32, ::tensorflow::profiler::Resource,
    ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  Device_ResourcesEntry_DoNotUse();
  Device_ResourcesEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const Device_ResourcesEntry_DoNotUse& other);
  static const Device_ResourcesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const Device_ResourcesEntry_DoNotUse*>(&_Device_ResourcesEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class Device : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.profiler.Device) */ {
 public:
  Device();
  virtual ~Device();

  Device(const Device& from);

  inline Device& operator=(const Device& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Device(Device&& from) noexcept
    : Device() {
    *this = ::std::move(from);
  }

  inline Device& operator=(Device&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Device& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Device* internal_default_instance() {
    return reinterpret_cast<const Device*>(
               &_Device_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void Swap(Device* other);
  friend void swap(Device& a, Device& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Device* New() const final {
    return CreateMaybeMessage<Device>(NULL);
  }

  Device* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Device>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Device& from);
  void MergeFrom(const Device& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Device* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<uint32, .tensorflow.profiler.Resource> resources = 3;
  int resources_size() const;
  void clear_resources();
  static const int kResourcesFieldNumber = 3;
  const ::google::protobuf::Map< ::google::protobuf::uint32, ::tensorflow::profiler::Resource >&
      resources() const;
  ::google::protobuf::Map< ::google::protobuf::uint32, ::tensorflow::profiler::Resource >*
      mutable_resources();

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // uint32 device_id = 2;
  void clear_device_id();
  static const int kDeviceIdFieldNumber = 2;
  ::google::protobuf::uint32 device_id() const;
  void set_device_id(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.profiler.Device)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::MapField<
      Device_ResourcesEntry_DoNotUse,
      ::google::protobuf::uint32, ::tensorflow::profiler::Resource,
      ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > resources_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::uint32 device_id_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Resource : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.profiler.Resource) */ {
 public:
  Resource();
  virtual ~Resource();

  Resource(const Resource& from);

  inline Resource& operator=(const Resource& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Resource(Resource&& from) noexcept
    : Resource() {
    *this = ::std::move(from);
  }

  inline Resource& operator=(Resource&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Resource& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Resource* internal_default_instance() {
    return reinterpret_cast<const Resource*>(
               &_Resource_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void Swap(Resource* other);
  friend void swap(Resource& a, Resource& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Resource* New() const final {
    return CreateMaybeMessage<Resource>(NULL);
  }

  Resource* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Resource>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Resource& from);
  void MergeFrom(const Resource& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Resource* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // uint32 resource_id = 2;
  void clear_resource_id();
  static const int kResourceIdFieldNumber = 2;
  ::google::protobuf::uint32 resource_id() const;
  void set_resource_id(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.profiler.Resource)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::uint32 resource_id_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class TraceEvent_ArgsEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<TraceEvent_ArgsEntry_DoNotUse, 
    ::std::string, ::std::string,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<TraceEvent_ArgsEntry_DoNotUse, 
    ::std::string, ::std::string,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    0 > SuperType;
  TraceEvent_ArgsEntry_DoNotUse();
  TraceEvent_ArgsEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const TraceEvent_ArgsEntry_DoNotUse& other);
  static const TraceEvent_ArgsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const TraceEvent_ArgsEntry_DoNotUse*>(&_TraceEvent_ArgsEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class TraceEvent : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.profiler.TraceEvent) */ {
 public:
  TraceEvent();
  virtual ~TraceEvent();

  TraceEvent(const TraceEvent& from);

  inline TraceEvent& operator=(const TraceEvent& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TraceEvent(TraceEvent&& from) noexcept
    : TraceEvent() {
    *this = ::std::move(from);
  }

  inline TraceEvent& operator=(TraceEvent&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const TraceEvent& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TraceEvent* internal_default_instance() {
    return reinterpret_cast<const TraceEvent*>(
               &_TraceEvent_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  void Swap(TraceEvent* other);
  friend void swap(TraceEvent& a, TraceEvent& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TraceEvent* New() const final {
    return CreateMaybeMessage<TraceEvent>(NULL);
  }

  TraceEvent* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TraceEvent>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TraceEvent& from);
  void MergeFrom(const TraceEvent& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TraceEvent* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<string, string> args = 11;
  int args_size() const;
  void clear_args();
  static const int kArgsFieldNumber = 11;
  const ::google::protobuf::Map< ::std::string, ::std::string >&
      args() const;
  ::google::protobuf::Map< ::std::string, ::std::string >*
      mutable_args();

  // string name = 3;
  void clear_name();
  static const int kNameFieldNumber = 3;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // uint32 device_id = 1;
  void clear_device_id();
  static const int kDeviceIdFieldNumber = 1;
  ::google::protobuf::uint32 device_id() const;
  void set_device_id(::google::protobuf::uint32 value);

  // uint32 resource_id = 2;
  void clear_resource_id();
  static const int kResourceIdFieldNumber = 2;
  ::google::protobuf::uint32 resource_id() const;
  void set_resource_id(::google::protobuf::uint32 value);

  // uint64 timestamp_ps = 9;
  void clear_timestamp_ps();
  static const int kTimestampPsFieldNumber = 9;
  ::google::protobuf::uint64 timestamp_ps() const;
  void set_timestamp_ps(::google::protobuf::uint64 value);

  // uint64 duration_ps = 10;
  void clear_duration_ps();
  static const int kDurationPsFieldNumber = 10;
  ::google::protobuf::uint64 duration_ps() const;
  void set_duration_ps(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.profiler.TraceEvent)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::MapField<
      TraceEvent_ArgsEntry_DoNotUse,
      ::std::string, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 > args_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::uint32 device_id_;
  ::google::protobuf::uint32 resource_id_;
  ::google::protobuf::uint64 timestamp_ps_;
  ::google::protobuf::uint64 duration_ps_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// -------------------------------------------------------------------

// Trace

// map<uint32, .tensorflow.profiler.Device> devices = 1;
inline int Trace::devices_size() const {
  return devices_.size();
}
inline void Trace::clear_devices() {
  devices_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::uint32, ::tensorflow::profiler::Device >&
Trace::devices() const {
  // @@protoc_insertion_point(field_map:tensorflow.profiler.Trace.devices)
  return devices_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::uint32, ::tensorflow::profiler::Device >*
Trace::mutable_devices() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.profiler.Trace.devices)
  return devices_.MutableMap();
}

// repeated .tensorflow.profiler.TraceEvent trace_events = 4;
inline int Trace::trace_events_size() const {
  return trace_events_.size();
}
inline void Trace::clear_trace_events() {
  trace_events_.Clear();
}
inline ::tensorflow::profiler::TraceEvent* Trace::mutable_trace_events(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.Trace.trace_events)
  return trace_events_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::profiler::TraceEvent >*
Trace::mutable_trace_events() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.profiler.Trace.trace_events)
  return &trace_events_;
}
inline const ::tensorflow::profiler::TraceEvent& Trace::trace_events(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.Trace.trace_events)
  return trace_events_.Get(index);
}
inline ::tensorflow::profiler::TraceEvent* Trace::add_trace_events() {
  // @@protoc_insertion_point(field_add:tensorflow.profiler.Trace.trace_events)
  return trace_events_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::profiler::TraceEvent >&
Trace::trace_events() const {
  // @@protoc_insertion_point(field_list:tensorflow.profiler.Trace.trace_events)
  return trace_events_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// Device

// string name = 1;
inline void Device::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Device::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.Device.name)
  return name_.GetNoArena();
}
inline void Device::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.Device.name)
}
#if LANG_CXX11
inline void Device::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.profiler.Device.name)
}
#endif
inline void Device::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.profiler.Device.name)
}
inline void Device::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.profiler.Device.name)
}
inline ::std::string* Device::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.Device.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Device::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.Device.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Device::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.Device.name)
}

// uint32 device_id = 2;
inline void Device::clear_device_id() {
  device_id_ = 0u;
}
inline ::google::protobuf::uint32 Device::device_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.Device.device_id)
  return device_id_;
}
inline void Device::set_device_id(::google::protobuf::uint32 value) {
  
  device_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.Device.device_id)
}

// map<uint32, .tensorflow.profiler.Resource> resources = 3;
inline int Device::resources_size() const {
  return resources_.size();
}
inline void Device::clear_resources() {
  resources_.Clear();
}
inline const ::google::protobuf::Map< ::google::protobuf::uint32, ::tensorflow::profiler::Resource >&
Device::resources() const {
  // @@protoc_insertion_point(field_map:tensorflow.profiler.Device.resources)
  return resources_.GetMap();
}
inline ::google::protobuf::Map< ::google::protobuf::uint32, ::tensorflow::profiler::Resource >*
Device::mutable_resources() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.profiler.Device.resources)
  return resources_.MutableMap();
}

// -------------------------------------------------------------------

// Resource

// string name = 1;
inline void Resource::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Resource::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.Resource.name)
  return name_.GetNoArena();
}
inline void Resource::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.Resource.name)
}
#if LANG_CXX11
inline void Resource::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.profiler.Resource.name)
}
#endif
inline void Resource::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.profiler.Resource.name)
}
inline void Resource::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.profiler.Resource.name)
}
inline ::std::string* Resource::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.Resource.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Resource::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.Resource.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Resource::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.Resource.name)
}

// uint32 resource_id = 2;
inline void Resource::clear_resource_id() {
  resource_id_ = 0u;
}
inline ::google::protobuf::uint32 Resource::resource_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.Resource.resource_id)
  return resource_id_;
}
inline void Resource::set_resource_id(::google::protobuf::uint32 value) {
  
  resource_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.Resource.resource_id)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// TraceEvent

// uint32 device_id = 1;
inline void TraceEvent::clear_device_id() {
  device_id_ = 0u;
}
inline ::google::protobuf::uint32 TraceEvent::device_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.TraceEvent.device_id)
  return device_id_;
}
inline void TraceEvent::set_device_id(::google::protobuf::uint32 value) {
  
  device_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.TraceEvent.device_id)
}

// uint32 resource_id = 2;
inline void TraceEvent::clear_resource_id() {
  resource_id_ = 0u;
}
inline ::google::protobuf::uint32 TraceEvent::resource_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.TraceEvent.resource_id)
  return resource_id_;
}
inline void TraceEvent::set_resource_id(::google::protobuf::uint32 value) {
  
  resource_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.TraceEvent.resource_id)
}

// string name = 3;
inline void TraceEvent::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& TraceEvent::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.TraceEvent.name)
  return name_.GetNoArena();
}
inline void TraceEvent::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.TraceEvent.name)
}
#if LANG_CXX11
inline void TraceEvent::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.profiler.TraceEvent.name)
}
#endif
inline void TraceEvent::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.profiler.TraceEvent.name)
}
inline void TraceEvent::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.profiler.TraceEvent.name)
}
inline ::std::string* TraceEvent::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.TraceEvent.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TraceEvent::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.TraceEvent.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TraceEvent::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.TraceEvent.name)
}

// uint64 timestamp_ps = 9;
inline void TraceEvent::clear_timestamp_ps() {
  timestamp_ps_ = GOOGLE_ULONGLONG(0);
}
inline ::google::protobuf::uint64 TraceEvent::timestamp_ps() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.TraceEvent.timestamp_ps)
  return timestamp_ps_;
}
inline void TraceEvent::set_timestamp_ps(::google::protobuf::uint64 value) {
  
  timestamp_ps_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.TraceEvent.timestamp_ps)
}

// uint64 duration_ps = 10;
inline void TraceEvent::clear_duration_ps() {
  duration_ps_ = GOOGLE_ULONGLONG(0);
}
inline ::google::protobuf::uint64 TraceEvent::duration_ps() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.TraceEvent.duration_ps)
  return duration_ps_;
}
inline void TraceEvent::set_duration_ps(::google::protobuf::uint64 value) {
  
  duration_ps_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.TraceEvent.duration_ps)
}

// map<string, string> args = 11;
inline int TraceEvent::args_size() const {
  return args_.size();
}
inline void TraceEvent::clear_args() {
  args_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::std::string >&
TraceEvent::args() const {
  // @@protoc_insertion_point(field_map:tensorflow.profiler.TraceEvent.args)
  return args_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::std::string >*
TraceEvent::mutable_args() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.profiler.TraceEvent.args)
  return args_.MutableMap();
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace profiler
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftrace_5fevents_2eproto

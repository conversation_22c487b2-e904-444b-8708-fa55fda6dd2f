// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/profiler/tfprof_output.proto

#include "tensorflow/core/profiler/tfprof_output.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_TensorShapeProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto
namespace protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_AdviceProto_Checker;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_TFProfTensorProto;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_AdviceProto_CheckersEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_GraphNodeProto_InputShapesEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_MultiGraphNodeProto;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto ::google::protobuf::internal::SCCInfo<3> scc_info_GraphNodeProto;
}  // namespace protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto
namespace tensorflow {
namespace tfprof {
class TFProfTensorProtoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TFProfTensorProto>
      _instance;
} _TFProfTensorProto_default_instance_;
class GraphNodeProto_InputShapesEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<GraphNodeProto_InputShapesEntry_DoNotUse>
      _instance;
} _GraphNodeProto_InputShapesEntry_DoNotUse_default_instance_;
class GraphNodeProtoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<GraphNodeProto>
      _instance;
} _GraphNodeProto_default_instance_;
class MultiGraphNodeProtoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<MultiGraphNodeProto>
      _instance;
} _MultiGraphNodeProto_default_instance_;
class AdviceProto_CheckersEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<AdviceProto_CheckersEntry_DoNotUse>
      _instance;
} _AdviceProto_CheckersEntry_DoNotUse_default_instance_;
class AdviceProto_CheckerDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<AdviceProto_Checker>
      _instance;
} _AdviceProto_Checker_default_instance_;
class AdviceProtoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<AdviceProto>
      _instance;
} _AdviceProto_default_instance_;
}  // namespace tfprof
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto {
static void InitDefaultsTFProfTensorProto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_TFProfTensorProto_default_instance_;
    new (ptr) ::tensorflow::tfprof::TFProfTensorProto();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tfprof::TFProfTensorProto::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_TFProfTensorProto =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsTFProfTensorProto}, {}};

static void InitDefaultsGraphNodeProto_InputShapesEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_GraphNodeProto_InputShapesEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::tfprof::GraphNodeProto_InputShapesEntry_DoNotUse();
  }
  ::tensorflow::tfprof::GraphNodeProto_InputShapesEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_GraphNodeProto_InputShapesEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsGraphNodeProto_InputShapesEntry_DoNotUse}, {
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::scc_info_TensorShapeProto.base,}};

static void InitDefaultsGraphNodeProto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_GraphNodeProto_default_instance_;
    new (ptr) ::tensorflow::tfprof::GraphNodeProto();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tfprof::GraphNodeProto::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<3> scc_info_GraphNodeProto =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 3, InitDefaultsGraphNodeProto}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::scc_info_TFProfTensorProto.base,
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::scc_info_TensorShapeProto.base,
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::scc_info_GraphNodeProto_InputShapesEntry_DoNotUse.base,}};

static void InitDefaultsMultiGraphNodeProto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_MultiGraphNodeProto_default_instance_;
    new (ptr) ::tensorflow::tfprof::MultiGraphNodeProto();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tfprof::MultiGraphNodeProto::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_MultiGraphNodeProto =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsMultiGraphNodeProto}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::scc_info_GraphNodeProto.base,}};

static void InitDefaultsAdviceProto_CheckersEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_AdviceProto_CheckersEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::tfprof::AdviceProto_CheckersEntry_DoNotUse();
  }
  ::tensorflow::tfprof::AdviceProto_CheckersEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_AdviceProto_CheckersEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsAdviceProto_CheckersEntry_DoNotUse}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::scc_info_AdviceProto_Checker.base,}};

static void InitDefaultsAdviceProto_Checker() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_AdviceProto_Checker_default_instance_;
    new (ptr) ::tensorflow::tfprof::AdviceProto_Checker();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tfprof::AdviceProto_Checker::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_AdviceProto_Checker =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsAdviceProto_Checker}, {}};

static void InitDefaultsAdviceProto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_AdviceProto_default_instance_;
    new (ptr) ::tensorflow::tfprof::AdviceProto();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tfprof::AdviceProto::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_AdviceProto =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsAdviceProto}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::scc_info_AdviceProto_CheckersEntry_DoNotUse.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_TFProfTensorProto.base);
  ::google::protobuf::internal::InitSCC(&scc_info_GraphNodeProto_InputShapesEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_GraphNodeProto.base);
  ::google::protobuf::internal::InitSCC(&scc_info_MultiGraphNodeProto.base);
  ::google::protobuf::internal::InitSCC(&scc_info_AdviceProto_CheckersEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_AdviceProto_Checker.base);
  ::google::protobuf::internal::InitSCC(&scc_info_AdviceProto.base);
}

::google::protobuf::Metadata file_level_metadata[7];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::TFProfTensorProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::TFProfTensorProto, dtype_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::TFProfTensorProto, value_double_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::TFProfTensorProto, value_int64_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::TFProfTensorProto, value_str_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto_InputShapesEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto_InputShapesEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto_InputShapesEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto_InputShapesEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto, tensor_value_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto, run_count_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto, exec_micros_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto, accelerator_exec_micros_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto, cpu_exec_micros_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto, requested_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto, peak_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto, residual_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto, output_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto, parameters_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto, float_ops_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto, devices_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto, total_definition_count_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto, total_run_count_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto, total_exec_micros_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto, total_accelerator_exec_micros_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto, total_cpu_exec_micros_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto, total_requested_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto, total_peak_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto, total_residual_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto, total_output_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto, total_parameters_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto, total_float_ops_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto, shapes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto, input_shapes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::GraphNodeProto, children_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::MultiGraphNodeProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::MultiGraphNodeProto, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::MultiGraphNodeProto, exec_micros_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::MultiGraphNodeProto, accelerator_exec_micros_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::MultiGraphNodeProto, cpu_exec_micros_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::MultiGraphNodeProto, requested_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::MultiGraphNodeProto, peak_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::MultiGraphNodeProto, residual_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::MultiGraphNodeProto, output_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::MultiGraphNodeProto, parameters_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::MultiGraphNodeProto, float_ops_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::MultiGraphNodeProto, total_exec_micros_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::MultiGraphNodeProto, total_accelerator_exec_micros_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::MultiGraphNodeProto, total_cpu_exec_micros_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::MultiGraphNodeProto, total_requested_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::MultiGraphNodeProto, total_peak_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::MultiGraphNodeProto, total_residual_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::MultiGraphNodeProto, total_output_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::MultiGraphNodeProto, total_parameters_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::MultiGraphNodeProto, total_float_ops_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::MultiGraphNodeProto, graph_nodes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::MultiGraphNodeProto, children_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::AdviceProto_CheckersEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::AdviceProto_CheckersEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::AdviceProto_CheckersEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::AdviceProto_CheckersEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::AdviceProto_Checker, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::AdviceProto_Checker, reports_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::AdviceProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::AdviceProto, checkers_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::tfprof::TFProfTensorProto)},
  { 9, 16, sizeof(::tensorflow::tfprof::GraphNodeProto_InputShapesEntry_DoNotUse)},
  { 18, -1, sizeof(::tensorflow::tfprof::GraphNodeProto)},
  { 50, -1, sizeof(::tensorflow::tfprof::MultiGraphNodeProto)},
  { 76, 83, sizeof(::tensorflow::tfprof::AdviceProto_CheckersEntry_DoNotUse)},
  { 85, -1, sizeof(::tensorflow::tfprof::AdviceProto_Checker)},
  { 91, -1, sizeof(::tensorflow::tfprof::AdviceProto)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_TFProfTensorProto_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_GraphNodeProto_InputShapesEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_GraphNodeProto_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_MultiGraphNodeProto_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_AdviceProto_CheckersEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_AdviceProto_Checker_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_AdviceProto_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/profiler/tfprof_output.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 7);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n,tensorflow/core/profiler/tfprof_output"
      ".proto\022\021tensorflow.tfprof\032,tensorflow/co"
      "re/framework/tensor_shape.proto\032%tensorf"
      "low/core/framework/types.proto\"v\n\021TFProf"
      "TensorProto\022#\n\005dtype\030\001 \001(\0162\024.tensorflow."
      "DataType\022\024\n\014value_double\030\002 \003(\001\022\023\n\013value_"
      "int64\030\003 \003(\003\022\021\n\tvalue_str\030\004 \003(\t\"\216\007\n\016Graph"
      "NodeProto\022\014\n\004name\030\001 \001(\t\022:\n\014tensor_value\030"
      "\017 \001(\0132$.tensorflow.tfprof.TFProfTensorPr"
      "oto\022\021\n\trun_count\030\025 \001(\003\022\023\n\013exec_micros\030\002 "
      "\001(\003\022\037\n\027accelerator_exec_micros\030\021 \001(\003\022\027\n\017"
      "cpu_exec_micros\030\022 \001(\003\022\027\n\017requested_bytes"
      "\030\003 \001(\003\022\022\n\npeak_bytes\030\030 \001(\003\022\026\n\016residual_b"
      "ytes\030\031 \001(\003\022\024\n\014output_bytes\030\032 \001(\003\022\022\n\npara"
      "meters\030\004 \001(\003\022\021\n\tfloat_ops\030\r \001(\003\022\017\n\007devic"
      "es\030\n \003(\t\022\036\n\026total_definition_count\030\027 \001(\003"
      "\022\027\n\017total_run_count\030\026 \001(\003\022\031\n\021total_exec_"
      "micros\030\006 \001(\003\022%\n\035total_accelerator_exec_m"
      "icros\030\023 \001(\003\022\035\n\025total_cpu_exec_micros\030\024 \001"
      "(\003\022\035\n\025total_requested_bytes\030\007 \001(\003\022\030\n\020tot"
      "al_peak_bytes\030\033 \001(\003\022\034\n\024total_residual_by"
      "tes\030\034 \001(\003\022\032\n\022total_output_bytes\030\035 \001(\003\022\030\n"
      "\020total_parameters\030\010 \001(\003\022\027\n\017total_float_o"
      "ps\030\016 \001(\003\022,\n\006shapes\030\013 \003(\0132\034.tensorflow.Te"
      "nsorShapeProto\022H\n\014input_shapes\030\020 \003(\01322.t"
      "ensorflow.tfprof.GraphNodeProto.InputSha"
      "pesEntry\0223\n\010children\030\014 \003(\0132!.tensorflow."
      "tfprof.GraphNodeProto\032P\n\020InputShapesEntr"
      "y\022\013\n\003key\030\001 \001(\005\022+\n\005value\030\002 \001(\0132\034.tensorfl"
      "ow.TensorShapeProto:\0028\001\"\355\004\n\023MultiGraphNo"
      "deProto\022\014\n\004name\030\001 \001(\t\022\023\n\013exec_micros\030\002 \001"
      "(\003\022\037\n\027accelerator_exec_micros\030\014 \001(\003\022\027\n\017c"
      "pu_exec_micros\030\r \001(\003\022\027\n\017requested_bytes\030"
      "\003 \001(\003\022\022\n\npeak_bytes\030\020 \001(\003\022\026\n\016residual_by"
      "tes\030\021 \001(\003\022\024\n\014output_bytes\030\022 \001(\003\022\022\n\nparam"
      "eters\030\004 \001(\003\022\021\n\tfloat_ops\030\005 \001(\003\022\031\n\021total_"
      "exec_micros\030\006 \001(\003\022%\n\035total_accelerator_e"
      "xec_micros\030\016 \001(\003\022\035\n\025total_cpu_exec_micro"
      "s\030\017 \001(\003\022\035\n\025total_requested_bytes\030\007 \001(\003\022\030"
      "\n\020total_peak_bytes\030\023 \001(\003\022\034\n\024total_residu"
      "al_bytes\030\024 \001(\003\022\032\n\022total_output_bytes\030\025 \001"
      "(\003\022\030\n\020total_parameters\030\010 \001(\003\022\027\n\017total_fl"
      "oat_ops\030\t \001(\003\0226\n\013graph_nodes\030\n \003(\0132!.ten"
      "sorflow.tfprof.GraphNodeProto\0228\n\010childre"
      "n\030\013 \003(\0132&.tensorflow.tfprof.MultiGraphNo"
      "deProto\"\302\001\n\013AdviceProto\022>\n\010checkers\030\001 \003("
      "\0132,.tensorflow.tfprof.AdviceProto.Checke"
      "rsEntry\032W\n\rCheckersEntry\022\013\n\003key\030\001 \001(\t\0225\n"
      "\005value\030\002 \001(\0132&.tensorflow.tfprof.AdviceP"
      "roto.Checker:\0028\001\032\032\n\007Checker\022\017\n\007reports\030\002"
      " \003(\tb\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 2012);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/profiler/tfprof_output.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftypes_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto
namespace tensorflow {
namespace tfprof {

// ===================================================================

void TFProfTensorProto::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TFProfTensorProto::kDtypeFieldNumber;
const int TFProfTensorProto::kValueDoubleFieldNumber;
const int TFProfTensorProto::kValueInt64FieldNumber;
const int TFProfTensorProto::kValueStrFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TFProfTensorProto::TFProfTensorProto()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::scc_info_TFProfTensorProto.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tfprof.TFProfTensorProto)
}
TFProfTensorProto::TFProfTensorProto(const TFProfTensorProto& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      value_double_(from.value_double_),
      value_int64_(from.value_int64_),
      value_str_(from.value_str_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  dtype_ = from.dtype_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.tfprof.TFProfTensorProto)
}

void TFProfTensorProto::SharedCtor() {
  dtype_ = 0;
}

TFProfTensorProto::~TFProfTensorProto() {
  // @@protoc_insertion_point(destructor:tensorflow.tfprof.TFProfTensorProto)
  SharedDtor();
}

void TFProfTensorProto::SharedDtor() {
}

void TFProfTensorProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* TFProfTensorProto::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const TFProfTensorProto& TFProfTensorProto::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::scc_info_TFProfTensorProto.base);
  return *internal_default_instance();
}


void TFProfTensorProto::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tfprof.TFProfTensorProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  value_double_.Clear();
  value_int64_.Clear();
  value_str_.Clear();
  dtype_ = 0;
  _internal_metadata_.Clear();
}

bool TFProfTensorProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tfprof.TFProfTensorProto)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.DataType dtype = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_dtype(static_cast< ::tensorflow::DataType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated double value_double = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, this->mutable_value_double())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(17u /* 17 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 1, 18u, input, this->mutable_value_double())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated int64 value_int64 = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_value_int64())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 1, 26u, input, this->mutable_value_int64())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string value_str = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_value_str()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->value_str(this->value_str_size() - 1).data(),
            static_cast<int>(this->value_str(this->value_str_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.TFProfTensorProto.value_str"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tfprof.TFProfTensorProto)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tfprof.TFProfTensorProto)
  return false;
#undef DO_
}

void TFProfTensorProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tfprof.TFProfTensorProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.DataType dtype = 1;
  if (this->dtype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->dtype(), output);
  }

  // repeated double value_double = 2;
  if (this->value_double_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(2, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _value_double_cached_byte_size_));
    ::google::protobuf::internal::WireFormatLite::WriteDoubleArray(
      this->value_double().data(), this->value_double_size(), output);
  }

  // repeated int64 value_int64 = 3;
  if (this->value_int64_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(3, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _value_int64_cached_byte_size_));
  }
  for (int i = 0, n = this->value_int64_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->value_int64(i), output);
  }

  // repeated string value_str = 4;
  for (int i = 0, n = this->value_str_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->value_str(i).data(), static_cast<int>(this->value_str(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.TFProfTensorProto.value_str");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      4, this->value_str(i), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tfprof.TFProfTensorProto)
}

::google::protobuf::uint8* TFProfTensorProto::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tfprof.TFProfTensorProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.DataType dtype = 1;
  if (this->dtype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->dtype(), target);
  }

  // repeated double value_double = 2;
  if (this->value_double_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      2,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _value_double_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteDoubleNoTagToArray(this->value_double_, target);
  }

  // repeated int64 value_int64 = 3;
  if (this->value_int64_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      3,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _value_int64_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->value_int64_, target);
  }

  // repeated string value_str = 4;
  for (int i = 0, n = this->value_str_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->value_str(i).data(), static_cast<int>(this->value_str(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.TFProfTensorProto.value_str");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(4, this->value_str(i), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tfprof.TFProfTensorProto)
  return target;
}

size_t TFProfTensorProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tfprof.TFProfTensorProto)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated double value_double = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->value_double_size());
    size_t data_size = 8UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _value_double_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 value_int64 = 3;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int64Size(this->value_int64_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _value_int64_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated string value_str = 4;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->value_str_size());
  for (int i = 0, n = this->value_str_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->value_str(i));
  }

  // .tensorflow.DataType dtype = 1;
  if (this->dtype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->dtype());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TFProfTensorProto::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tfprof.TFProfTensorProto)
  GOOGLE_DCHECK_NE(&from, this);
  const TFProfTensorProto* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TFProfTensorProto>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tfprof.TFProfTensorProto)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tfprof.TFProfTensorProto)
    MergeFrom(*source);
  }
}

void TFProfTensorProto::MergeFrom(const TFProfTensorProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tfprof.TFProfTensorProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  value_double_.MergeFrom(from.value_double_);
  value_int64_.MergeFrom(from.value_int64_);
  value_str_.MergeFrom(from.value_str_);
  if (from.dtype() != 0) {
    set_dtype(from.dtype());
  }
}

void TFProfTensorProto::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tfprof.TFProfTensorProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TFProfTensorProto::CopyFrom(const TFProfTensorProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tfprof.TFProfTensorProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TFProfTensorProto::IsInitialized() const {
  return true;
}

void TFProfTensorProto::Swap(TFProfTensorProto* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TFProfTensorProto::InternalSwap(TFProfTensorProto* other) {
  using std::swap;
  value_double_.InternalSwap(&other->value_double_);
  value_int64_.InternalSwap(&other->value_int64_);
  value_str_.InternalSwap(CastToBase(&other->value_str_));
  swap(dtype_, other->dtype_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata TFProfTensorProto::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

GraphNodeProto_InputShapesEntry_DoNotUse::GraphNodeProto_InputShapesEntry_DoNotUse() {}
GraphNodeProto_InputShapesEntry_DoNotUse::GraphNodeProto_InputShapesEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void GraphNodeProto_InputShapesEntry_DoNotUse::MergeFrom(const GraphNodeProto_InputShapesEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata GraphNodeProto_InputShapesEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::file_level_metadata[1];
}
void GraphNodeProto_InputShapesEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void GraphNodeProto::InitAsDefaultInstance() {
  ::tensorflow::tfprof::_GraphNodeProto_default_instance_._instance.get_mutable()->tensor_value_ = const_cast< ::tensorflow::tfprof::TFProfTensorProto*>(
      ::tensorflow::tfprof::TFProfTensorProto::internal_default_instance());
}
void GraphNodeProto::clear_shapes() {
  shapes_.Clear();
}
void GraphNodeProto::clear_input_shapes() {
  input_shapes_.Clear();
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GraphNodeProto::kNameFieldNumber;
const int GraphNodeProto::kTensorValueFieldNumber;
const int GraphNodeProto::kRunCountFieldNumber;
const int GraphNodeProto::kExecMicrosFieldNumber;
const int GraphNodeProto::kAcceleratorExecMicrosFieldNumber;
const int GraphNodeProto::kCpuExecMicrosFieldNumber;
const int GraphNodeProto::kRequestedBytesFieldNumber;
const int GraphNodeProto::kPeakBytesFieldNumber;
const int GraphNodeProto::kResidualBytesFieldNumber;
const int GraphNodeProto::kOutputBytesFieldNumber;
const int GraphNodeProto::kParametersFieldNumber;
const int GraphNodeProto::kFloatOpsFieldNumber;
const int GraphNodeProto::kDevicesFieldNumber;
const int GraphNodeProto::kTotalDefinitionCountFieldNumber;
const int GraphNodeProto::kTotalRunCountFieldNumber;
const int GraphNodeProto::kTotalExecMicrosFieldNumber;
const int GraphNodeProto::kTotalAcceleratorExecMicrosFieldNumber;
const int GraphNodeProto::kTotalCpuExecMicrosFieldNumber;
const int GraphNodeProto::kTotalRequestedBytesFieldNumber;
const int GraphNodeProto::kTotalPeakBytesFieldNumber;
const int GraphNodeProto::kTotalResidualBytesFieldNumber;
const int GraphNodeProto::kTotalOutputBytesFieldNumber;
const int GraphNodeProto::kTotalParametersFieldNumber;
const int GraphNodeProto::kTotalFloatOpsFieldNumber;
const int GraphNodeProto::kShapesFieldNumber;
const int GraphNodeProto::kInputShapesFieldNumber;
const int GraphNodeProto::kChildrenFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GraphNodeProto::GraphNodeProto()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::scc_info_GraphNodeProto.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tfprof.GraphNodeProto)
}
GraphNodeProto::GraphNodeProto(const GraphNodeProto& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      devices_(from.devices_),
      shapes_(from.shapes_),
      children_(from.children_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  input_shapes_.MergeFrom(from.input_shapes_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  if (from.has_tensor_value()) {
    tensor_value_ = new ::tensorflow::tfprof::TFProfTensorProto(*from.tensor_value_);
  } else {
    tensor_value_ = NULL;
  }
  ::memcpy(&exec_micros_, &from.exec_micros_,
    static_cast<size_t>(reinterpret_cast<char*>(&total_output_bytes_) -
    reinterpret_cast<char*>(&exec_micros_)) + sizeof(total_output_bytes_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.tfprof.GraphNodeProto)
}

void GraphNodeProto::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&tensor_value_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&total_output_bytes_) -
      reinterpret_cast<char*>(&tensor_value_)) + sizeof(total_output_bytes_));
}

GraphNodeProto::~GraphNodeProto() {
  // @@protoc_insertion_point(destructor:tensorflow.tfprof.GraphNodeProto)
  SharedDtor();
}

void GraphNodeProto::SharedDtor() {
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete tensor_value_;
}

void GraphNodeProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* GraphNodeProto::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const GraphNodeProto& GraphNodeProto::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::scc_info_GraphNodeProto.base);
  return *internal_default_instance();
}


void GraphNodeProto::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tfprof.GraphNodeProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  devices_.Clear();
  shapes_.Clear();
  children_.Clear();
  input_shapes_.Clear();
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && tensor_value_ != NULL) {
    delete tensor_value_;
  }
  tensor_value_ = NULL;
  ::memset(&exec_micros_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&total_output_bytes_) -
      reinterpret_cast<char*>(&exec_micros_)) + sizeof(total_output_bytes_));
  _internal_metadata_.Clear();
}

bool GraphNodeProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tfprof.GraphNodeProto)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(16383u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.GraphNodeProto.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 exec_micros = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &exec_micros_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 requested_bytes = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &requested_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 parameters = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &parameters_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 total_exec_micros = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(48u /* 48 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &total_exec_micros_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 total_requested_bytes = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(56u /* 56 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &total_requested_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 total_parameters = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(64u /* 64 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &total_parameters_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string devices = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(82u /* 82 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_devices()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->devices(this->devices_size() - 1).data(),
            static_cast<int>(this->devices(this->devices_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.GraphNodeProto.devices"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.TensorShapeProto shapes = 11;
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(90u /* 90 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_shapes()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.tfprof.GraphNodeProto children = 12;
      case 12: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(98u /* 98 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_children()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 float_ops = 13;
      case 13: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(104u /* 104 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &float_ops_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 total_float_ops = 14;
      case 14: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(112u /* 112 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &total_float_ops_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.tfprof.TFProfTensorProto tensor_value = 15;
      case 15: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(122u /* 122 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_tensor_value()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<int32, .tensorflow.TensorShapeProto> input_shapes = 16;
      case 16: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(130u /* 130 & 0xFF */)) {
          GraphNodeProto_InputShapesEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              GraphNodeProto_InputShapesEntry_DoNotUse,
              ::google::protobuf::int32, ::tensorflow::TensorShapeProto,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::TensorShapeProto > > parser(&input_shapes_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 accelerator_exec_micros = 17;
      case 17: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(136u /* 136 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &accelerator_exec_micros_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 cpu_exec_micros = 18;
      case 18: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(144u /* 144 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &cpu_exec_micros_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 total_accelerator_exec_micros = 19;
      case 19: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(152u /* 152 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &total_accelerator_exec_micros_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 total_cpu_exec_micros = 20;
      case 20: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(160u /* 160 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &total_cpu_exec_micros_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 run_count = 21;
      case 21: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(168u /* 168 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &run_count_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 total_run_count = 22;
      case 22: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(176u /* 176 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &total_run_count_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 total_definition_count = 23;
      case 23: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(184u /* 184 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &total_definition_count_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 peak_bytes = 24;
      case 24: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(192u /* 192 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &peak_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 residual_bytes = 25;
      case 25: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(200u /* 200 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &residual_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 output_bytes = 26;
      case 26: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(208u /* 208 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &output_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 total_peak_bytes = 27;
      case 27: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(216u /* 216 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &total_peak_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 total_residual_bytes = 28;
      case 28: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(224u /* 224 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &total_residual_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 total_output_bytes = 29;
      case 29: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(232u /* 232 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &total_output_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tfprof.GraphNodeProto)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tfprof.GraphNodeProto)
  return false;
#undef DO_
}

void GraphNodeProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tfprof.GraphNodeProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.GraphNodeProto.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // int64 exec_micros = 2;
  if (this->exec_micros() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->exec_micros(), output);
  }

  // int64 requested_bytes = 3;
  if (this->requested_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->requested_bytes(), output);
  }

  // int64 parameters = 4;
  if (this->parameters() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->parameters(), output);
  }

  // int64 total_exec_micros = 6;
  if (this->total_exec_micros() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(6, this->total_exec_micros(), output);
  }

  // int64 total_requested_bytes = 7;
  if (this->total_requested_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(7, this->total_requested_bytes(), output);
  }

  // int64 total_parameters = 8;
  if (this->total_parameters() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->total_parameters(), output);
  }

  // repeated string devices = 10;
  for (int i = 0, n = this->devices_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->devices(i).data(), static_cast<int>(this->devices(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.GraphNodeProto.devices");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      10, this->devices(i), output);
  }

  // repeated .tensorflow.TensorShapeProto shapes = 11;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->shapes_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      11,
      this->shapes(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.tfprof.GraphNodeProto children = 12;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->children_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      12,
      this->children(static_cast<int>(i)),
      output);
  }

  // int64 float_ops = 13;
  if (this->float_ops() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->float_ops(), output);
  }

  // int64 total_float_ops = 14;
  if (this->total_float_ops() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->total_float_ops(), output);
  }

  // .tensorflow.tfprof.TFProfTensorProto tensor_value = 15;
  if (this->has_tensor_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      15, this->_internal_tensor_value(), output);
  }

  // map<int32, .tensorflow.TensorShapeProto> input_shapes = 16;
  if (!this->input_shapes().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::TensorShapeProto >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterministic() &&
        this->input_shapes().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->input_shapes().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::TensorShapeProto >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::TensorShapeProto >::const_iterator
          it = this->input_shapes().begin();
          it != this->input_shapes().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<GraphNodeProto_InputShapesEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(input_shapes_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            16, *entry, output);
      }
    } else {
      ::std::unique_ptr<GraphNodeProto_InputShapesEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::TensorShapeProto >::const_iterator
          it = this->input_shapes().begin();
          it != this->input_shapes().end(); ++it) {
        entry.reset(input_shapes_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            16, *entry, output);
      }
    }
  }

  // int64 accelerator_exec_micros = 17;
  if (this->accelerator_exec_micros() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(17, this->accelerator_exec_micros(), output);
  }

  // int64 cpu_exec_micros = 18;
  if (this->cpu_exec_micros() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(18, this->cpu_exec_micros(), output);
  }

  // int64 total_accelerator_exec_micros = 19;
  if (this->total_accelerator_exec_micros() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(19, this->total_accelerator_exec_micros(), output);
  }

  // int64 total_cpu_exec_micros = 20;
  if (this->total_cpu_exec_micros() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(20, this->total_cpu_exec_micros(), output);
  }

  // int64 run_count = 21;
  if (this->run_count() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(21, this->run_count(), output);
  }

  // int64 total_run_count = 22;
  if (this->total_run_count() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(22, this->total_run_count(), output);
  }

  // int64 total_definition_count = 23;
  if (this->total_definition_count() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(23, this->total_definition_count(), output);
  }

  // int64 peak_bytes = 24;
  if (this->peak_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(24, this->peak_bytes(), output);
  }

  // int64 residual_bytes = 25;
  if (this->residual_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(25, this->residual_bytes(), output);
  }

  // int64 output_bytes = 26;
  if (this->output_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(26, this->output_bytes(), output);
  }

  // int64 total_peak_bytes = 27;
  if (this->total_peak_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(27, this->total_peak_bytes(), output);
  }

  // int64 total_residual_bytes = 28;
  if (this->total_residual_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(28, this->total_residual_bytes(), output);
  }

  // int64 total_output_bytes = 29;
  if (this->total_output_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(29, this->total_output_bytes(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tfprof.GraphNodeProto)
}

::google::protobuf::uint8* GraphNodeProto::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tfprof.GraphNodeProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.GraphNodeProto.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // int64 exec_micros = 2;
  if (this->exec_micros() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->exec_micros(), target);
  }

  // int64 requested_bytes = 3;
  if (this->requested_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->requested_bytes(), target);
  }

  // int64 parameters = 4;
  if (this->parameters() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->parameters(), target);
  }

  // int64 total_exec_micros = 6;
  if (this->total_exec_micros() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(6, this->total_exec_micros(), target);
  }

  // int64 total_requested_bytes = 7;
  if (this->total_requested_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(7, this->total_requested_bytes(), target);
  }

  // int64 total_parameters = 8;
  if (this->total_parameters() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->total_parameters(), target);
  }

  // repeated string devices = 10;
  for (int i = 0, n = this->devices_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->devices(i).data(), static_cast<int>(this->devices(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.GraphNodeProto.devices");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(10, this->devices(i), target);
  }

  // repeated .tensorflow.TensorShapeProto shapes = 11;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->shapes_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        11, this->shapes(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.tfprof.GraphNodeProto children = 12;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->children_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        12, this->children(static_cast<int>(i)), deterministic, target);
  }

  // int64 float_ops = 13;
  if (this->float_ops() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->float_ops(), target);
  }

  // int64 total_float_ops = 14;
  if (this->total_float_ops() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->total_float_ops(), target);
  }

  // .tensorflow.tfprof.TFProfTensorProto tensor_value = 15;
  if (this->has_tensor_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        15, this->_internal_tensor_value(), deterministic, target);
  }

  // map<int32, .tensorflow.TensorShapeProto> input_shapes = 16;
  if (!this->input_shapes().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::TensorShapeProto >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->input_shapes().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->input_shapes().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::TensorShapeProto >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::TensorShapeProto >::const_iterator
          it = this->input_shapes().begin();
          it != this->input_shapes().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<GraphNodeProto_InputShapesEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(input_shapes_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       16, *entry, deterministic, target);
;
      }
    } else {
      ::std::unique_ptr<GraphNodeProto_InputShapesEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::TensorShapeProto >::const_iterator
          it = this->input_shapes().begin();
          it != this->input_shapes().end(); ++it) {
        entry.reset(input_shapes_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       16, *entry, deterministic, target);
;
      }
    }
  }

  // int64 accelerator_exec_micros = 17;
  if (this->accelerator_exec_micros() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(17, this->accelerator_exec_micros(), target);
  }

  // int64 cpu_exec_micros = 18;
  if (this->cpu_exec_micros() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(18, this->cpu_exec_micros(), target);
  }

  // int64 total_accelerator_exec_micros = 19;
  if (this->total_accelerator_exec_micros() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(19, this->total_accelerator_exec_micros(), target);
  }

  // int64 total_cpu_exec_micros = 20;
  if (this->total_cpu_exec_micros() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(20, this->total_cpu_exec_micros(), target);
  }

  // int64 run_count = 21;
  if (this->run_count() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(21, this->run_count(), target);
  }

  // int64 total_run_count = 22;
  if (this->total_run_count() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(22, this->total_run_count(), target);
  }

  // int64 total_definition_count = 23;
  if (this->total_definition_count() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(23, this->total_definition_count(), target);
  }

  // int64 peak_bytes = 24;
  if (this->peak_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(24, this->peak_bytes(), target);
  }

  // int64 residual_bytes = 25;
  if (this->residual_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(25, this->residual_bytes(), target);
  }

  // int64 output_bytes = 26;
  if (this->output_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(26, this->output_bytes(), target);
  }

  // int64 total_peak_bytes = 27;
  if (this->total_peak_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(27, this->total_peak_bytes(), target);
  }

  // int64 total_residual_bytes = 28;
  if (this->total_residual_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(28, this->total_residual_bytes(), target);
  }

  // int64 total_output_bytes = 29;
  if (this->total_output_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(29, this->total_output_bytes(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tfprof.GraphNodeProto)
  return target;
}

size_t GraphNodeProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tfprof.GraphNodeProto)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated string devices = 10;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->devices_size());
  for (int i = 0, n = this->devices_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->devices(i));
  }

  // repeated .tensorflow.TensorShapeProto shapes = 11;
  {
    unsigned int count = static_cast<unsigned int>(this->shapes_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->shapes(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.tfprof.GraphNodeProto children = 12;
  {
    unsigned int count = static_cast<unsigned int>(this->children_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->children(static_cast<int>(i)));
    }
  }

  // map<int32, .tensorflow.TensorShapeProto> input_shapes = 16;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->input_shapes_size());
  {
    ::std::unique_ptr<GraphNodeProto_InputShapesEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::TensorShapeProto >::const_iterator
        it = this->input_shapes().begin();
        it != this->input_shapes().end(); ++it) {
      entry.reset(input_shapes_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // .tensorflow.tfprof.TFProfTensorProto tensor_value = 15;
  if (this->has_tensor_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *tensor_value_);
  }

  // int64 exec_micros = 2;
  if (this->exec_micros() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->exec_micros());
  }

  // int64 requested_bytes = 3;
  if (this->requested_bytes() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->requested_bytes());
  }

  // int64 parameters = 4;
  if (this->parameters() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->parameters());
  }

  // int64 total_exec_micros = 6;
  if (this->total_exec_micros() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->total_exec_micros());
  }

  // int64 total_requested_bytes = 7;
  if (this->total_requested_bytes() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->total_requested_bytes());
  }

  // int64 total_parameters = 8;
  if (this->total_parameters() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->total_parameters());
  }

  // int64 float_ops = 13;
  if (this->float_ops() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->float_ops());
  }

  // int64 total_float_ops = 14;
  if (this->total_float_ops() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->total_float_ops());
  }

  // int64 accelerator_exec_micros = 17;
  if (this->accelerator_exec_micros() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->accelerator_exec_micros());
  }

  // int64 cpu_exec_micros = 18;
  if (this->cpu_exec_micros() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->cpu_exec_micros());
  }

  // int64 total_accelerator_exec_micros = 19;
  if (this->total_accelerator_exec_micros() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->total_accelerator_exec_micros());
  }

  // int64 total_cpu_exec_micros = 20;
  if (this->total_cpu_exec_micros() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->total_cpu_exec_micros());
  }

  // int64 run_count = 21;
  if (this->run_count() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->run_count());
  }

  // int64 total_run_count = 22;
  if (this->total_run_count() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->total_run_count());
  }

  // int64 total_definition_count = 23;
  if (this->total_definition_count() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->total_definition_count());
  }

  // int64 peak_bytes = 24;
  if (this->peak_bytes() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->peak_bytes());
  }

  // int64 residual_bytes = 25;
  if (this->residual_bytes() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->residual_bytes());
  }

  // int64 output_bytes = 26;
  if (this->output_bytes() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->output_bytes());
  }

  // int64 total_peak_bytes = 27;
  if (this->total_peak_bytes() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->total_peak_bytes());
  }

  // int64 total_residual_bytes = 28;
  if (this->total_residual_bytes() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->total_residual_bytes());
  }

  // int64 total_output_bytes = 29;
  if (this->total_output_bytes() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->total_output_bytes());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void GraphNodeProto::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tfprof.GraphNodeProto)
  GOOGLE_DCHECK_NE(&from, this);
  const GraphNodeProto* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GraphNodeProto>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tfprof.GraphNodeProto)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tfprof.GraphNodeProto)
    MergeFrom(*source);
  }
}

void GraphNodeProto::MergeFrom(const GraphNodeProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tfprof.GraphNodeProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  devices_.MergeFrom(from.devices_);
  shapes_.MergeFrom(from.shapes_);
  children_.MergeFrom(from.children_);
  input_shapes_.MergeFrom(from.input_shapes_);
  if (from.name().size() > 0) {

    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  if (from.has_tensor_value()) {
    mutable_tensor_value()->::tensorflow::tfprof::TFProfTensorProto::MergeFrom(from.tensor_value());
  }
  if (from.exec_micros() != 0) {
    set_exec_micros(from.exec_micros());
  }
  if (from.requested_bytes() != 0) {
    set_requested_bytes(from.requested_bytes());
  }
  if (from.parameters() != 0) {
    set_parameters(from.parameters());
  }
  if (from.total_exec_micros() != 0) {
    set_total_exec_micros(from.total_exec_micros());
  }
  if (from.total_requested_bytes() != 0) {
    set_total_requested_bytes(from.total_requested_bytes());
  }
  if (from.total_parameters() != 0) {
    set_total_parameters(from.total_parameters());
  }
  if (from.float_ops() != 0) {
    set_float_ops(from.float_ops());
  }
  if (from.total_float_ops() != 0) {
    set_total_float_ops(from.total_float_ops());
  }
  if (from.accelerator_exec_micros() != 0) {
    set_accelerator_exec_micros(from.accelerator_exec_micros());
  }
  if (from.cpu_exec_micros() != 0) {
    set_cpu_exec_micros(from.cpu_exec_micros());
  }
  if (from.total_accelerator_exec_micros() != 0) {
    set_total_accelerator_exec_micros(from.total_accelerator_exec_micros());
  }
  if (from.total_cpu_exec_micros() != 0) {
    set_total_cpu_exec_micros(from.total_cpu_exec_micros());
  }
  if (from.run_count() != 0) {
    set_run_count(from.run_count());
  }
  if (from.total_run_count() != 0) {
    set_total_run_count(from.total_run_count());
  }
  if (from.total_definition_count() != 0) {
    set_total_definition_count(from.total_definition_count());
  }
  if (from.peak_bytes() != 0) {
    set_peak_bytes(from.peak_bytes());
  }
  if (from.residual_bytes() != 0) {
    set_residual_bytes(from.residual_bytes());
  }
  if (from.output_bytes() != 0) {
    set_output_bytes(from.output_bytes());
  }
  if (from.total_peak_bytes() != 0) {
    set_total_peak_bytes(from.total_peak_bytes());
  }
  if (from.total_residual_bytes() != 0) {
    set_total_residual_bytes(from.total_residual_bytes());
  }
  if (from.total_output_bytes() != 0) {
    set_total_output_bytes(from.total_output_bytes());
  }
}

void GraphNodeProto::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tfprof.GraphNodeProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GraphNodeProto::CopyFrom(const GraphNodeProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tfprof.GraphNodeProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GraphNodeProto::IsInitialized() const {
  return true;
}

void GraphNodeProto::Swap(GraphNodeProto* other) {
  if (other == this) return;
  InternalSwap(other);
}
void GraphNodeProto::InternalSwap(GraphNodeProto* other) {
  using std::swap;
  devices_.InternalSwap(CastToBase(&other->devices_));
  CastToBase(&shapes_)->InternalSwap(CastToBase(&other->shapes_));
  CastToBase(&children_)->InternalSwap(CastToBase(&other->children_));
  input_shapes_.Swap(&other->input_shapes_);
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(tensor_value_, other->tensor_value_);
  swap(exec_micros_, other->exec_micros_);
  swap(requested_bytes_, other->requested_bytes_);
  swap(parameters_, other->parameters_);
  swap(total_exec_micros_, other->total_exec_micros_);
  swap(total_requested_bytes_, other->total_requested_bytes_);
  swap(total_parameters_, other->total_parameters_);
  swap(float_ops_, other->float_ops_);
  swap(total_float_ops_, other->total_float_ops_);
  swap(accelerator_exec_micros_, other->accelerator_exec_micros_);
  swap(cpu_exec_micros_, other->cpu_exec_micros_);
  swap(total_accelerator_exec_micros_, other->total_accelerator_exec_micros_);
  swap(total_cpu_exec_micros_, other->total_cpu_exec_micros_);
  swap(run_count_, other->run_count_);
  swap(total_run_count_, other->total_run_count_);
  swap(total_definition_count_, other->total_definition_count_);
  swap(peak_bytes_, other->peak_bytes_);
  swap(residual_bytes_, other->residual_bytes_);
  swap(output_bytes_, other->output_bytes_);
  swap(total_peak_bytes_, other->total_peak_bytes_);
  swap(total_residual_bytes_, other->total_residual_bytes_);
  swap(total_output_bytes_, other->total_output_bytes_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata GraphNodeProto::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void MultiGraphNodeProto::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MultiGraphNodeProto::kNameFieldNumber;
const int MultiGraphNodeProto::kExecMicrosFieldNumber;
const int MultiGraphNodeProto::kAcceleratorExecMicrosFieldNumber;
const int MultiGraphNodeProto::kCpuExecMicrosFieldNumber;
const int MultiGraphNodeProto::kRequestedBytesFieldNumber;
const int MultiGraphNodeProto::kPeakBytesFieldNumber;
const int MultiGraphNodeProto::kResidualBytesFieldNumber;
const int MultiGraphNodeProto::kOutputBytesFieldNumber;
const int MultiGraphNodeProto::kParametersFieldNumber;
const int MultiGraphNodeProto::kFloatOpsFieldNumber;
const int MultiGraphNodeProto::kTotalExecMicrosFieldNumber;
const int MultiGraphNodeProto::kTotalAcceleratorExecMicrosFieldNumber;
const int MultiGraphNodeProto::kTotalCpuExecMicrosFieldNumber;
const int MultiGraphNodeProto::kTotalRequestedBytesFieldNumber;
const int MultiGraphNodeProto::kTotalPeakBytesFieldNumber;
const int MultiGraphNodeProto::kTotalResidualBytesFieldNumber;
const int MultiGraphNodeProto::kTotalOutputBytesFieldNumber;
const int MultiGraphNodeProto::kTotalParametersFieldNumber;
const int MultiGraphNodeProto::kTotalFloatOpsFieldNumber;
const int MultiGraphNodeProto::kGraphNodesFieldNumber;
const int MultiGraphNodeProto::kChildrenFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MultiGraphNodeProto::MultiGraphNodeProto()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::scc_info_MultiGraphNodeProto.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tfprof.MultiGraphNodeProto)
}
MultiGraphNodeProto::MultiGraphNodeProto(const MultiGraphNodeProto& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      graph_nodes_(from.graph_nodes_),
      children_(from.children_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  ::memcpy(&exec_micros_, &from.exec_micros_,
    static_cast<size_t>(reinterpret_cast<char*>(&total_output_bytes_) -
    reinterpret_cast<char*>(&exec_micros_)) + sizeof(total_output_bytes_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.tfprof.MultiGraphNodeProto)
}

void MultiGraphNodeProto::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&exec_micros_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&total_output_bytes_) -
      reinterpret_cast<char*>(&exec_micros_)) + sizeof(total_output_bytes_));
}

MultiGraphNodeProto::~MultiGraphNodeProto() {
  // @@protoc_insertion_point(destructor:tensorflow.tfprof.MultiGraphNodeProto)
  SharedDtor();
}

void MultiGraphNodeProto::SharedDtor() {
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MultiGraphNodeProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* MultiGraphNodeProto::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const MultiGraphNodeProto& MultiGraphNodeProto::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::scc_info_MultiGraphNodeProto.base);
  return *internal_default_instance();
}


void MultiGraphNodeProto::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tfprof.MultiGraphNodeProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  graph_nodes_.Clear();
  children_.Clear();
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&exec_micros_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&total_output_bytes_) -
      reinterpret_cast<char*>(&exec_micros_)) + sizeof(total_output_bytes_));
  _internal_metadata_.Clear();
}

bool MultiGraphNodeProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tfprof.MultiGraphNodeProto)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(16383u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.MultiGraphNodeProto.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 exec_micros = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &exec_micros_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 requested_bytes = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &requested_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 parameters = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &parameters_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 float_ops = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &float_ops_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 total_exec_micros = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(48u /* 48 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &total_exec_micros_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 total_requested_bytes = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(56u /* 56 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &total_requested_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 total_parameters = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(64u /* 64 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &total_parameters_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 total_float_ops = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(72u /* 72 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &total_float_ops_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.tfprof.GraphNodeProto graph_nodes = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(82u /* 82 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_graph_nodes()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.tfprof.MultiGraphNodeProto children = 11;
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(90u /* 90 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_children()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 accelerator_exec_micros = 12;
      case 12: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(96u /* 96 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &accelerator_exec_micros_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 cpu_exec_micros = 13;
      case 13: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(104u /* 104 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &cpu_exec_micros_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 total_accelerator_exec_micros = 14;
      case 14: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(112u /* 112 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &total_accelerator_exec_micros_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 total_cpu_exec_micros = 15;
      case 15: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(120u /* 120 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &total_cpu_exec_micros_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 peak_bytes = 16;
      case 16: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(128u /* 128 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &peak_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 residual_bytes = 17;
      case 17: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(136u /* 136 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &residual_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 output_bytes = 18;
      case 18: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(144u /* 144 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &output_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 total_peak_bytes = 19;
      case 19: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(152u /* 152 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &total_peak_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 total_residual_bytes = 20;
      case 20: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(160u /* 160 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &total_residual_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 total_output_bytes = 21;
      case 21: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(168u /* 168 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &total_output_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tfprof.MultiGraphNodeProto)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tfprof.MultiGraphNodeProto)
  return false;
#undef DO_
}

void MultiGraphNodeProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tfprof.MultiGraphNodeProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.MultiGraphNodeProto.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // int64 exec_micros = 2;
  if (this->exec_micros() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->exec_micros(), output);
  }

  // int64 requested_bytes = 3;
  if (this->requested_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->requested_bytes(), output);
  }

  // int64 parameters = 4;
  if (this->parameters() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->parameters(), output);
  }

  // int64 float_ops = 5;
  if (this->float_ops() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(5, this->float_ops(), output);
  }

  // int64 total_exec_micros = 6;
  if (this->total_exec_micros() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(6, this->total_exec_micros(), output);
  }

  // int64 total_requested_bytes = 7;
  if (this->total_requested_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(7, this->total_requested_bytes(), output);
  }

  // int64 total_parameters = 8;
  if (this->total_parameters() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->total_parameters(), output);
  }

  // int64 total_float_ops = 9;
  if (this->total_float_ops() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->total_float_ops(), output);
  }

  // repeated .tensorflow.tfprof.GraphNodeProto graph_nodes = 10;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->graph_nodes_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      10,
      this->graph_nodes(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.tfprof.MultiGraphNodeProto children = 11;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->children_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      11,
      this->children(static_cast<int>(i)),
      output);
  }

  // int64 accelerator_exec_micros = 12;
  if (this->accelerator_exec_micros() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->accelerator_exec_micros(), output);
  }

  // int64 cpu_exec_micros = 13;
  if (this->cpu_exec_micros() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->cpu_exec_micros(), output);
  }

  // int64 total_accelerator_exec_micros = 14;
  if (this->total_accelerator_exec_micros() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->total_accelerator_exec_micros(), output);
  }

  // int64 total_cpu_exec_micros = 15;
  if (this->total_cpu_exec_micros() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->total_cpu_exec_micros(), output);
  }

  // int64 peak_bytes = 16;
  if (this->peak_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->peak_bytes(), output);
  }

  // int64 residual_bytes = 17;
  if (this->residual_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(17, this->residual_bytes(), output);
  }

  // int64 output_bytes = 18;
  if (this->output_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(18, this->output_bytes(), output);
  }

  // int64 total_peak_bytes = 19;
  if (this->total_peak_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(19, this->total_peak_bytes(), output);
  }

  // int64 total_residual_bytes = 20;
  if (this->total_residual_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(20, this->total_residual_bytes(), output);
  }

  // int64 total_output_bytes = 21;
  if (this->total_output_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(21, this->total_output_bytes(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tfprof.MultiGraphNodeProto)
}

::google::protobuf::uint8* MultiGraphNodeProto::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tfprof.MultiGraphNodeProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.MultiGraphNodeProto.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // int64 exec_micros = 2;
  if (this->exec_micros() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->exec_micros(), target);
  }

  // int64 requested_bytes = 3;
  if (this->requested_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->requested_bytes(), target);
  }

  // int64 parameters = 4;
  if (this->parameters() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->parameters(), target);
  }

  // int64 float_ops = 5;
  if (this->float_ops() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(5, this->float_ops(), target);
  }

  // int64 total_exec_micros = 6;
  if (this->total_exec_micros() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(6, this->total_exec_micros(), target);
  }

  // int64 total_requested_bytes = 7;
  if (this->total_requested_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(7, this->total_requested_bytes(), target);
  }

  // int64 total_parameters = 8;
  if (this->total_parameters() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->total_parameters(), target);
  }

  // int64 total_float_ops = 9;
  if (this->total_float_ops() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->total_float_ops(), target);
  }

  // repeated .tensorflow.tfprof.GraphNodeProto graph_nodes = 10;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->graph_nodes_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        10, this->graph_nodes(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.tfprof.MultiGraphNodeProto children = 11;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->children_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        11, this->children(static_cast<int>(i)), deterministic, target);
  }

  // int64 accelerator_exec_micros = 12;
  if (this->accelerator_exec_micros() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->accelerator_exec_micros(), target);
  }

  // int64 cpu_exec_micros = 13;
  if (this->cpu_exec_micros() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->cpu_exec_micros(), target);
  }

  // int64 total_accelerator_exec_micros = 14;
  if (this->total_accelerator_exec_micros() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->total_accelerator_exec_micros(), target);
  }

  // int64 total_cpu_exec_micros = 15;
  if (this->total_cpu_exec_micros() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->total_cpu_exec_micros(), target);
  }

  // int64 peak_bytes = 16;
  if (this->peak_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->peak_bytes(), target);
  }

  // int64 residual_bytes = 17;
  if (this->residual_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(17, this->residual_bytes(), target);
  }

  // int64 output_bytes = 18;
  if (this->output_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(18, this->output_bytes(), target);
  }

  // int64 total_peak_bytes = 19;
  if (this->total_peak_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(19, this->total_peak_bytes(), target);
  }

  // int64 total_residual_bytes = 20;
  if (this->total_residual_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(20, this->total_residual_bytes(), target);
  }

  // int64 total_output_bytes = 21;
  if (this->total_output_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(21, this->total_output_bytes(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tfprof.MultiGraphNodeProto)
  return target;
}

size_t MultiGraphNodeProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tfprof.MultiGraphNodeProto)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.tfprof.GraphNodeProto graph_nodes = 10;
  {
    unsigned int count = static_cast<unsigned int>(this->graph_nodes_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->graph_nodes(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.tfprof.MultiGraphNodeProto children = 11;
  {
    unsigned int count = static_cast<unsigned int>(this->children_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->children(static_cast<int>(i)));
    }
  }

  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // int64 exec_micros = 2;
  if (this->exec_micros() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->exec_micros());
  }

  // int64 requested_bytes = 3;
  if (this->requested_bytes() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->requested_bytes());
  }

  // int64 parameters = 4;
  if (this->parameters() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->parameters());
  }

  // int64 float_ops = 5;
  if (this->float_ops() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->float_ops());
  }

  // int64 total_exec_micros = 6;
  if (this->total_exec_micros() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->total_exec_micros());
  }

  // int64 total_requested_bytes = 7;
  if (this->total_requested_bytes() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->total_requested_bytes());
  }

  // int64 total_parameters = 8;
  if (this->total_parameters() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->total_parameters());
  }

  // int64 total_float_ops = 9;
  if (this->total_float_ops() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->total_float_ops());
  }

  // int64 accelerator_exec_micros = 12;
  if (this->accelerator_exec_micros() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->accelerator_exec_micros());
  }

  // int64 cpu_exec_micros = 13;
  if (this->cpu_exec_micros() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->cpu_exec_micros());
  }

  // int64 total_accelerator_exec_micros = 14;
  if (this->total_accelerator_exec_micros() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->total_accelerator_exec_micros());
  }

  // int64 total_cpu_exec_micros = 15;
  if (this->total_cpu_exec_micros() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->total_cpu_exec_micros());
  }

  // int64 peak_bytes = 16;
  if (this->peak_bytes() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->peak_bytes());
  }

  // int64 residual_bytes = 17;
  if (this->residual_bytes() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->residual_bytes());
  }

  // int64 output_bytes = 18;
  if (this->output_bytes() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->output_bytes());
  }

  // int64 total_peak_bytes = 19;
  if (this->total_peak_bytes() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->total_peak_bytes());
  }

  // int64 total_residual_bytes = 20;
  if (this->total_residual_bytes() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->total_residual_bytes());
  }

  // int64 total_output_bytes = 21;
  if (this->total_output_bytes() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->total_output_bytes());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void MultiGraphNodeProto::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tfprof.MultiGraphNodeProto)
  GOOGLE_DCHECK_NE(&from, this);
  const MultiGraphNodeProto* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MultiGraphNodeProto>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tfprof.MultiGraphNodeProto)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tfprof.MultiGraphNodeProto)
    MergeFrom(*source);
  }
}

void MultiGraphNodeProto::MergeFrom(const MultiGraphNodeProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tfprof.MultiGraphNodeProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  graph_nodes_.MergeFrom(from.graph_nodes_);
  children_.MergeFrom(from.children_);
  if (from.name().size() > 0) {

    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  if (from.exec_micros() != 0) {
    set_exec_micros(from.exec_micros());
  }
  if (from.requested_bytes() != 0) {
    set_requested_bytes(from.requested_bytes());
  }
  if (from.parameters() != 0) {
    set_parameters(from.parameters());
  }
  if (from.float_ops() != 0) {
    set_float_ops(from.float_ops());
  }
  if (from.total_exec_micros() != 0) {
    set_total_exec_micros(from.total_exec_micros());
  }
  if (from.total_requested_bytes() != 0) {
    set_total_requested_bytes(from.total_requested_bytes());
  }
  if (from.total_parameters() != 0) {
    set_total_parameters(from.total_parameters());
  }
  if (from.total_float_ops() != 0) {
    set_total_float_ops(from.total_float_ops());
  }
  if (from.accelerator_exec_micros() != 0) {
    set_accelerator_exec_micros(from.accelerator_exec_micros());
  }
  if (from.cpu_exec_micros() != 0) {
    set_cpu_exec_micros(from.cpu_exec_micros());
  }
  if (from.total_accelerator_exec_micros() != 0) {
    set_total_accelerator_exec_micros(from.total_accelerator_exec_micros());
  }
  if (from.total_cpu_exec_micros() != 0) {
    set_total_cpu_exec_micros(from.total_cpu_exec_micros());
  }
  if (from.peak_bytes() != 0) {
    set_peak_bytes(from.peak_bytes());
  }
  if (from.residual_bytes() != 0) {
    set_residual_bytes(from.residual_bytes());
  }
  if (from.output_bytes() != 0) {
    set_output_bytes(from.output_bytes());
  }
  if (from.total_peak_bytes() != 0) {
    set_total_peak_bytes(from.total_peak_bytes());
  }
  if (from.total_residual_bytes() != 0) {
    set_total_residual_bytes(from.total_residual_bytes());
  }
  if (from.total_output_bytes() != 0) {
    set_total_output_bytes(from.total_output_bytes());
  }
}

void MultiGraphNodeProto::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tfprof.MultiGraphNodeProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MultiGraphNodeProto::CopyFrom(const MultiGraphNodeProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tfprof.MultiGraphNodeProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MultiGraphNodeProto::IsInitialized() const {
  return true;
}

void MultiGraphNodeProto::Swap(MultiGraphNodeProto* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MultiGraphNodeProto::InternalSwap(MultiGraphNodeProto* other) {
  using std::swap;
  CastToBase(&graph_nodes_)->InternalSwap(CastToBase(&other->graph_nodes_));
  CastToBase(&children_)->InternalSwap(CastToBase(&other->children_));
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(exec_micros_, other->exec_micros_);
  swap(requested_bytes_, other->requested_bytes_);
  swap(parameters_, other->parameters_);
  swap(float_ops_, other->float_ops_);
  swap(total_exec_micros_, other->total_exec_micros_);
  swap(total_requested_bytes_, other->total_requested_bytes_);
  swap(total_parameters_, other->total_parameters_);
  swap(total_float_ops_, other->total_float_ops_);
  swap(accelerator_exec_micros_, other->accelerator_exec_micros_);
  swap(cpu_exec_micros_, other->cpu_exec_micros_);
  swap(total_accelerator_exec_micros_, other->total_accelerator_exec_micros_);
  swap(total_cpu_exec_micros_, other->total_cpu_exec_micros_);
  swap(peak_bytes_, other->peak_bytes_);
  swap(residual_bytes_, other->residual_bytes_);
  swap(output_bytes_, other->output_bytes_);
  swap(total_peak_bytes_, other->total_peak_bytes_);
  swap(total_residual_bytes_, other->total_residual_bytes_);
  swap(total_output_bytes_, other->total_output_bytes_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata MultiGraphNodeProto::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

AdviceProto_CheckersEntry_DoNotUse::AdviceProto_CheckersEntry_DoNotUse() {}
AdviceProto_CheckersEntry_DoNotUse::AdviceProto_CheckersEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void AdviceProto_CheckersEntry_DoNotUse::MergeFrom(const AdviceProto_CheckersEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata AdviceProto_CheckersEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::file_level_metadata[4];
}
void AdviceProto_CheckersEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void AdviceProto_Checker::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AdviceProto_Checker::kReportsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AdviceProto_Checker::AdviceProto_Checker()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::scc_info_AdviceProto_Checker.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tfprof.AdviceProto.Checker)
}
AdviceProto_Checker::AdviceProto_Checker(const AdviceProto_Checker& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      reports_(from.reports_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.tfprof.AdviceProto.Checker)
}

void AdviceProto_Checker::SharedCtor() {
}

AdviceProto_Checker::~AdviceProto_Checker() {
  // @@protoc_insertion_point(destructor:tensorflow.tfprof.AdviceProto.Checker)
  SharedDtor();
}

void AdviceProto_Checker::SharedDtor() {
}

void AdviceProto_Checker::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* AdviceProto_Checker::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const AdviceProto_Checker& AdviceProto_Checker::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::scc_info_AdviceProto_Checker.base);
  return *internal_default_instance();
}


void AdviceProto_Checker::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tfprof.AdviceProto.Checker)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  reports_.Clear();
  _internal_metadata_.Clear();
}

bool AdviceProto_Checker::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tfprof.AdviceProto.Checker)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated string reports = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_reports()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->reports(this->reports_size() - 1).data(),
            static_cast<int>(this->reports(this->reports_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.AdviceProto.Checker.reports"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tfprof.AdviceProto.Checker)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tfprof.AdviceProto.Checker)
  return false;
#undef DO_
}

void AdviceProto_Checker::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tfprof.AdviceProto.Checker)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string reports = 2;
  for (int i = 0, n = this->reports_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->reports(i).data(), static_cast<int>(this->reports(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.AdviceProto.Checker.reports");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->reports(i), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tfprof.AdviceProto.Checker)
}

::google::protobuf::uint8* AdviceProto_Checker::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tfprof.AdviceProto.Checker)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string reports = 2;
  for (int i = 0, n = this->reports_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->reports(i).data(), static_cast<int>(this->reports(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.AdviceProto.Checker.reports");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(2, this->reports(i), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tfprof.AdviceProto.Checker)
  return target;
}

size_t AdviceProto_Checker::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tfprof.AdviceProto.Checker)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated string reports = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->reports_size());
  for (int i = 0, n = this->reports_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->reports(i));
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AdviceProto_Checker::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tfprof.AdviceProto.Checker)
  GOOGLE_DCHECK_NE(&from, this);
  const AdviceProto_Checker* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AdviceProto_Checker>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tfprof.AdviceProto.Checker)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tfprof.AdviceProto.Checker)
    MergeFrom(*source);
  }
}

void AdviceProto_Checker::MergeFrom(const AdviceProto_Checker& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tfprof.AdviceProto.Checker)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  reports_.MergeFrom(from.reports_);
}

void AdviceProto_Checker::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tfprof.AdviceProto.Checker)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AdviceProto_Checker::CopyFrom(const AdviceProto_Checker& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tfprof.AdviceProto.Checker)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AdviceProto_Checker::IsInitialized() const {
  return true;
}

void AdviceProto_Checker::Swap(AdviceProto_Checker* other) {
  if (other == this) return;
  InternalSwap(other);
}
void AdviceProto_Checker::InternalSwap(AdviceProto_Checker* other) {
  using std::swap;
  reports_.InternalSwap(CastToBase(&other->reports_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata AdviceProto_Checker::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void AdviceProto::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AdviceProto::kCheckersFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AdviceProto::AdviceProto()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::scc_info_AdviceProto.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tfprof.AdviceProto)
}
AdviceProto::AdviceProto(const AdviceProto& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  checkers_.MergeFrom(from.checkers_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.tfprof.AdviceProto)
}

void AdviceProto::SharedCtor() {
}

AdviceProto::~AdviceProto() {
  // @@protoc_insertion_point(destructor:tensorflow.tfprof.AdviceProto)
  SharedDtor();
}

void AdviceProto::SharedDtor() {
}

void AdviceProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* AdviceProto::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const AdviceProto& AdviceProto::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::scc_info_AdviceProto.base);
  return *internal_default_instance();
}


void AdviceProto::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tfprof.AdviceProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  checkers_.Clear();
  _internal_metadata_.Clear();
}

bool AdviceProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tfprof.AdviceProto)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<string, .tensorflow.tfprof.AdviceProto.Checker> checkers = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          AdviceProto_CheckersEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              AdviceProto_CheckersEntry_DoNotUse,
              ::std::string, ::tensorflow::tfprof::AdviceProto_Checker,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::AdviceProto_Checker > > parser(&checkers_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.AdviceProto.CheckersEntry.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tfprof.AdviceProto)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tfprof.AdviceProto)
  return false;
#undef DO_
}

void AdviceProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tfprof.AdviceProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, .tensorflow.tfprof.AdviceProto.Checker> checkers = 1;
  if (!this->checkers().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::AdviceProto_Checker >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.tfprof.AdviceProto.CheckersEntry.key");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->checkers().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->checkers().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::AdviceProto_Checker >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::AdviceProto_Checker >::const_iterator
          it = this->checkers().begin();
          it != this->checkers().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<AdviceProto_CheckersEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(checkers_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<AdviceProto_CheckersEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::AdviceProto_Checker >::const_iterator
          it = this->checkers().begin();
          it != this->checkers().end(); ++it) {
        entry.reset(checkers_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tfprof.AdviceProto)
}

::google::protobuf::uint8* AdviceProto::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tfprof.AdviceProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, .tensorflow.tfprof.AdviceProto.Checker> checkers = 1;
  if (!this->checkers().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::AdviceProto_Checker >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.tfprof.AdviceProto.CheckersEntry.key");
      }
    };

    if (deterministic &&
        this->checkers().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->checkers().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::AdviceProto_Checker >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::AdviceProto_Checker >::const_iterator
          it = this->checkers().begin();
          it != this->checkers().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<AdviceProto_CheckersEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(checkers_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<AdviceProto_CheckersEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::AdviceProto_Checker >::const_iterator
          it = this->checkers().begin();
          it != this->checkers().end(); ++it) {
        entry.reset(checkers_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tfprof.AdviceProto)
  return target;
}

size_t AdviceProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tfprof.AdviceProto)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // map<string, .tensorflow.tfprof.AdviceProto.Checker> checkers = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->checkers_size());
  {
    ::std::unique_ptr<AdviceProto_CheckersEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::AdviceProto_Checker >::const_iterator
        it = this->checkers().begin();
        it != this->checkers().end(); ++it) {
      entry.reset(checkers_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AdviceProto::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tfprof.AdviceProto)
  GOOGLE_DCHECK_NE(&from, this);
  const AdviceProto* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AdviceProto>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tfprof.AdviceProto)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tfprof.AdviceProto)
    MergeFrom(*source);
  }
}

void AdviceProto::MergeFrom(const AdviceProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tfprof.AdviceProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  checkers_.MergeFrom(from.checkers_);
}

void AdviceProto::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tfprof.AdviceProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AdviceProto::CopyFrom(const AdviceProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tfprof.AdviceProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AdviceProto::IsInitialized() const {
  return true;
}

void AdviceProto::Swap(AdviceProto* other) {
  if (other == this) return;
  InternalSwap(other);
}
void AdviceProto::InternalSwap(AdviceProto* other) {
  using std::swap;
  checkers_.Swap(&other->checkers_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata AdviceProto::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tfprof
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::TFProfTensorProto* Arena::CreateMaybeMessage< ::tensorflow::tfprof::TFProfTensorProto >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::TFProfTensorProto >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::GraphNodeProto_InputShapesEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::tfprof::GraphNodeProto_InputShapesEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::GraphNodeProto_InputShapesEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::GraphNodeProto* Arena::CreateMaybeMessage< ::tensorflow::tfprof::GraphNodeProto >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::GraphNodeProto >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::MultiGraphNodeProto* Arena::CreateMaybeMessage< ::tensorflow::tfprof::MultiGraphNodeProto >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::MultiGraphNodeProto >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::AdviceProto_CheckersEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::tfprof::AdviceProto_CheckersEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::AdviceProto_CheckersEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::AdviceProto_Checker* Arena::CreateMaybeMessage< ::tensorflow::tfprof::AdviceProto_Checker >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::AdviceProto_Checker >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::AdviceProto* Arena::CreateMaybeMessage< ::tensorflow::tfprof::AdviceProto >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::AdviceProto >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/profiler/op_profile.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto 

namespace protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[7];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto
namespace tensorflow {
namespace profiler {
namespace op_profile {
class Metrics;
class MetricsDefaultTypeInternal;
extern MetricsDefaultTypeInternal _Metrics_default_instance_;
class Node;
class NodeDefaultTypeInternal;
extern NodeDefaultTypeInternal _Node_default_instance_;
class Node_InstructionCategory;
class Node_InstructionCategoryDefaultTypeInternal;
extern Node_InstructionCategoryDefaultTypeInternal _Node_InstructionCategory_default_instance_;
class Node_XLAInstruction;
class Node_XLAInstructionDefaultTypeInternal;
extern Node_XLAInstructionDefaultTypeInternal _Node_XLAInstruction_default_instance_;
class Node_XLAInstruction_LayoutAnalysis;
class Node_XLAInstruction_LayoutAnalysisDefaultTypeInternal;
extern Node_XLAInstruction_LayoutAnalysisDefaultTypeInternal _Node_XLAInstruction_LayoutAnalysis_default_instance_;
class Node_XLAInstruction_LayoutAnalysis_Dimension;
class Node_XLAInstruction_LayoutAnalysis_DimensionDefaultTypeInternal;
extern Node_XLAInstruction_LayoutAnalysis_DimensionDefaultTypeInternal _Node_XLAInstruction_LayoutAnalysis_Dimension_default_instance_;
class Profile;
class ProfileDefaultTypeInternal;
extern ProfileDefaultTypeInternal _Profile_default_instance_;
}  // namespace op_profile
}  // namespace profiler
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::profiler::op_profile::Metrics* Arena::CreateMaybeMessage<::tensorflow::profiler::op_profile::Metrics>(Arena*);
template<> ::tensorflow::profiler::op_profile::Node* Arena::CreateMaybeMessage<::tensorflow::profiler::op_profile::Node>(Arena*);
template<> ::tensorflow::profiler::op_profile::Node_InstructionCategory* Arena::CreateMaybeMessage<::tensorflow::profiler::op_profile::Node_InstructionCategory>(Arena*);
template<> ::tensorflow::profiler::op_profile::Node_XLAInstruction* Arena::CreateMaybeMessage<::tensorflow::profiler::op_profile::Node_XLAInstruction>(Arena*);
template<> ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis* Arena::CreateMaybeMessage<::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis>(Arena*);
template<> ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis_Dimension* Arena::CreateMaybeMessage<::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis_Dimension>(Arena*);
template<> ::tensorflow::profiler::op_profile::Profile* Arena::CreateMaybeMessage<::tensorflow::profiler::op_profile::Profile>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {
namespace profiler {
namespace op_profile {

// ===================================================================

class Profile : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.profiler.op_profile.Profile) */ {
 public:
  Profile();
  virtual ~Profile();

  Profile(const Profile& from);

  inline Profile& operator=(const Profile& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Profile(Profile&& from) noexcept
    : Profile() {
    *this = ::std::move(from);
  }

  inline Profile& operator=(Profile&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Profile& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Profile* internal_default_instance() {
    return reinterpret_cast<const Profile*>(
               &_Profile_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void Swap(Profile* other);
  friend void swap(Profile& a, Profile& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Profile* New() const final {
    return CreateMaybeMessage<Profile>(NULL);
  }

  Profile* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Profile>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Profile& from);
  void MergeFrom(const Profile& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Profile* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .tensorflow.profiler.op_profile.Node by_category = 1;
  bool has_by_category() const;
  void clear_by_category();
  static const int kByCategoryFieldNumber = 1;
  private:
  const ::tensorflow::profiler::op_profile::Node& _internal_by_category() const;
  public:
  const ::tensorflow::profiler::op_profile::Node& by_category() const;
  ::tensorflow::profiler::op_profile::Node* release_by_category();
  ::tensorflow::profiler::op_profile::Node* mutable_by_category();
  void set_allocated_by_category(::tensorflow::profiler::op_profile::Node* by_category);

  // .tensorflow.profiler.op_profile.Node by_program = 4;
  bool has_by_program() const;
  void clear_by_program();
  static const int kByProgramFieldNumber = 4;
  private:
  const ::tensorflow::profiler::op_profile::Node& _internal_by_program() const;
  public:
  const ::tensorflow::profiler::op_profile::Node& by_program() const;
  ::tensorflow::profiler::op_profile::Node* release_by_program();
  ::tensorflow::profiler::op_profile::Node* mutable_by_program();
  void set_allocated_by_program(::tensorflow::profiler::op_profile::Node* by_program);

  // @@protoc_insertion_point(class_scope:tensorflow.profiler.op_profile.Profile)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::tensorflow::profiler::op_profile::Node* by_category_;
  ::tensorflow::profiler::op_profile::Node* by_program_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Node_InstructionCategory : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.profiler.op_profile.Node.InstructionCategory) */ {
 public:
  Node_InstructionCategory();
  virtual ~Node_InstructionCategory();

  Node_InstructionCategory(const Node_InstructionCategory& from);

  inline Node_InstructionCategory& operator=(const Node_InstructionCategory& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Node_InstructionCategory(Node_InstructionCategory&& from) noexcept
    : Node_InstructionCategory() {
    *this = ::std::move(from);
  }

  inline Node_InstructionCategory& operator=(Node_InstructionCategory&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Node_InstructionCategory& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Node_InstructionCategory* internal_default_instance() {
    return reinterpret_cast<const Node_InstructionCategory*>(
               &_Node_InstructionCategory_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void Swap(Node_InstructionCategory* other);
  friend void swap(Node_InstructionCategory& a, Node_InstructionCategory& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Node_InstructionCategory* New() const final {
    return CreateMaybeMessage<Node_InstructionCategory>(NULL);
  }

  Node_InstructionCategory* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Node_InstructionCategory>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Node_InstructionCategory& from);
  void MergeFrom(const Node_InstructionCategory& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Node_InstructionCategory* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.profiler.op_profile.Node.InstructionCategory)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Node_XLAInstruction_LayoutAnalysis_Dimension : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension) */ {
 public:
  Node_XLAInstruction_LayoutAnalysis_Dimension();
  virtual ~Node_XLAInstruction_LayoutAnalysis_Dimension();

  Node_XLAInstruction_LayoutAnalysis_Dimension(const Node_XLAInstruction_LayoutAnalysis_Dimension& from);

  inline Node_XLAInstruction_LayoutAnalysis_Dimension& operator=(const Node_XLAInstruction_LayoutAnalysis_Dimension& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Node_XLAInstruction_LayoutAnalysis_Dimension(Node_XLAInstruction_LayoutAnalysis_Dimension&& from) noexcept
    : Node_XLAInstruction_LayoutAnalysis_Dimension() {
    *this = ::std::move(from);
  }

  inline Node_XLAInstruction_LayoutAnalysis_Dimension& operator=(Node_XLAInstruction_LayoutAnalysis_Dimension&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Node_XLAInstruction_LayoutAnalysis_Dimension& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Node_XLAInstruction_LayoutAnalysis_Dimension* internal_default_instance() {
    return reinterpret_cast<const Node_XLAInstruction_LayoutAnalysis_Dimension*>(
               &_Node_XLAInstruction_LayoutAnalysis_Dimension_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void Swap(Node_XLAInstruction_LayoutAnalysis_Dimension* other);
  friend void swap(Node_XLAInstruction_LayoutAnalysis_Dimension& a, Node_XLAInstruction_LayoutAnalysis_Dimension& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Node_XLAInstruction_LayoutAnalysis_Dimension* New() const final {
    return CreateMaybeMessage<Node_XLAInstruction_LayoutAnalysis_Dimension>(NULL);
  }

  Node_XLAInstruction_LayoutAnalysis_Dimension* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Node_XLAInstruction_LayoutAnalysis_Dimension>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Node_XLAInstruction_LayoutAnalysis_Dimension& from);
  void MergeFrom(const Node_XLAInstruction_LayoutAnalysis_Dimension& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Node_XLAInstruction_LayoutAnalysis_Dimension* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string semantics = 3;
  void clear_semantics();
  static const int kSemanticsFieldNumber = 3;
  const ::std::string& semantics() const;
  void set_semantics(const ::std::string& value);
  #if LANG_CXX11
  void set_semantics(::std::string&& value);
  #endif
  void set_semantics(const char* value);
  void set_semantics(const char* value, size_t size);
  ::std::string* mutable_semantics();
  ::std::string* release_semantics();
  void set_allocated_semantics(::std::string* semantics);

  // int32 size = 1;
  void clear_size();
  static const int kSizeFieldNumber = 1;
  ::google::protobuf::int32 size() const;
  void set_size(::google::protobuf::int32 value);

  // int32 alignment = 2;
  void clear_alignment();
  static const int kAlignmentFieldNumber = 2;
  ::google::protobuf::int32 alignment() const;
  void set_alignment(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr semantics_;
  ::google::protobuf::int32 size_;
  ::google::protobuf::int32 alignment_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Node_XLAInstruction_LayoutAnalysis : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis) */ {
 public:
  Node_XLAInstruction_LayoutAnalysis();
  virtual ~Node_XLAInstruction_LayoutAnalysis();

  Node_XLAInstruction_LayoutAnalysis(const Node_XLAInstruction_LayoutAnalysis& from);

  inline Node_XLAInstruction_LayoutAnalysis& operator=(const Node_XLAInstruction_LayoutAnalysis& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Node_XLAInstruction_LayoutAnalysis(Node_XLAInstruction_LayoutAnalysis&& from) noexcept
    : Node_XLAInstruction_LayoutAnalysis() {
    *this = ::std::move(from);
  }

  inline Node_XLAInstruction_LayoutAnalysis& operator=(Node_XLAInstruction_LayoutAnalysis&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Node_XLAInstruction_LayoutAnalysis& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Node_XLAInstruction_LayoutAnalysis* internal_default_instance() {
    return reinterpret_cast<const Node_XLAInstruction_LayoutAnalysis*>(
               &_Node_XLAInstruction_LayoutAnalysis_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void Swap(Node_XLAInstruction_LayoutAnalysis* other);
  friend void swap(Node_XLAInstruction_LayoutAnalysis& a, Node_XLAInstruction_LayoutAnalysis& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Node_XLAInstruction_LayoutAnalysis* New() const final {
    return CreateMaybeMessage<Node_XLAInstruction_LayoutAnalysis>(NULL);
  }

  Node_XLAInstruction_LayoutAnalysis* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Node_XLAInstruction_LayoutAnalysis>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Node_XLAInstruction_LayoutAnalysis& from);
  void MergeFrom(const Node_XLAInstruction_LayoutAnalysis& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Node_XLAInstruction_LayoutAnalysis* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef Node_XLAInstruction_LayoutAnalysis_Dimension Dimension;

  // accessors -------------------------------------------------------

  // repeated .tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension dimensions = 1;
  int dimensions_size() const;
  void clear_dimensions();
  static const int kDimensionsFieldNumber = 1;
  ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis_Dimension* mutable_dimensions(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis_Dimension >*
      mutable_dimensions();
  const ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis_Dimension& dimensions(int index) const;
  ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis_Dimension* add_dimensions();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis_Dimension >&
      dimensions() const;

  // @@protoc_insertion_point(class_scope:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis_Dimension > dimensions_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Node_XLAInstruction : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.profiler.op_profile.Node.XLAInstruction) */ {
 public:
  Node_XLAInstruction();
  virtual ~Node_XLAInstruction();

  Node_XLAInstruction(const Node_XLAInstruction& from);

  inline Node_XLAInstruction& operator=(const Node_XLAInstruction& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Node_XLAInstruction(Node_XLAInstruction&& from) noexcept
    : Node_XLAInstruction() {
    *this = ::std::move(from);
  }

  inline Node_XLAInstruction& operator=(Node_XLAInstruction&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Node_XLAInstruction& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Node_XLAInstruction* internal_default_instance() {
    return reinterpret_cast<const Node_XLAInstruction*>(
               &_Node_XLAInstruction_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void Swap(Node_XLAInstruction* other);
  friend void swap(Node_XLAInstruction& a, Node_XLAInstruction& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Node_XLAInstruction* New() const final {
    return CreateMaybeMessage<Node_XLAInstruction>(NULL);
  }

  Node_XLAInstruction* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Node_XLAInstruction>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Node_XLAInstruction& from);
  void MergeFrom(const Node_XLAInstruction& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Node_XLAInstruction* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef Node_XLAInstruction_LayoutAnalysis LayoutAnalysis;

  // accessors -------------------------------------------------------

  // string op = 1;
  void clear_op();
  static const int kOpFieldNumber = 1;
  const ::std::string& op() const;
  void set_op(const ::std::string& value);
  #if LANG_CXX11
  void set_op(::std::string&& value);
  #endif
  void set_op(const char* value);
  void set_op(const char* value, size_t size);
  ::std::string* mutable_op();
  ::std::string* release_op();
  void set_allocated_op(::std::string* op);

  // string expression = 2;
  void clear_expression();
  static const int kExpressionFieldNumber = 2;
  const ::std::string& expression() const;
  void set_expression(const ::std::string& value);
  #if LANG_CXX11
  void set_expression(::std::string&& value);
  #endif
  void set_expression(const char* value);
  void set_expression(const char* value, size_t size);
  ::std::string* mutable_expression();
  ::std::string* release_expression();
  void set_allocated_expression(::std::string* expression);

  // string provenance = 3;
  void clear_provenance();
  static const int kProvenanceFieldNumber = 3;
  const ::std::string& provenance() const;
  void set_provenance(const ::std::string& value);
  #if LANG_CXX11
  void set_provenance(::std::string&& value);
  #endif
  void set_provenance(const char* value);
  void set_provenance(const char* value, size_t size);
  ::std::string* mutable_provenance();
  ::std::string* release_provenance();
  void set_allocated_provenance(::std::string* provenance);

  // string category = 4;
  void clear_category();
  static const int kCategoryFieldNumber = 4;
  const ::std::string& category() const;
  void set_category(const ::std::string& value);
  #if LANG_CXX11
  void set_category(::std::string&& value);
  #endif
  void set_category(const char* value);
  void set_category(const char* value, size_t size);
  ::std::string* mutable_category();
  ::std::string* release_category();
  void set_allocated_category(::std::string* category);

  // .tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis layout = 5;
  bool has_layout() const;
  void clear_layout();
  static const int kLayoutFieldNumber = 5;
  private:
  const ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis& _internal_layout() const;
  public:
  const ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis& layout() const;
  ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis* release_layout();
  ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis* mutable_layout();
  void set_allocated_layout(::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis* layout);

  // @@protoc_insertion_point(class_scope:tensorflow.profiler.op_profile.Node.XLAInstruction)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr op_;
  ::google::protobuf::internal::ArenaStringPtr expression_;
  ::google::protobuf::internal::ArenaStringPtr provenance_;
  ::google::protobuf::internal::ArenaStringPtr category_;
  ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis* layout_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Node : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.profiler.op_profile.Node) */ {
 public:
  Node();
  virtual ~Node();

  Node(const Node& from);

  inline Node& operator=(const Node& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Node(Node&& from) noexcept
    : Node() {
    *this = ::std::move(from);
  }

  inline Node& operator=(Node&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Node& default_instance();

  enum ContentsCase {
    kCategory = 4,
    kXla = 5,
    CONTENTS_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Node* internal_default_instance() {
    return reinterpret_cast<const Node*>(
               &_Node_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  void Swap(Node* other);
  friend void swap(Node& a, Node& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Node* New() const final {
    return CreateMaybeMessage<Node>(NULL);
  }

  Node* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Node>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Node& from);
  void MergeFrom(const Node& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Node* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef Node_InstructionCategory InstructionCategory;
  typedef Node_XLAInstruction XLAInstruction;

  // accessors -------------------------------------------------------

  // repeated .tensorflow.profiler.op_profile.Node children = 3;
  int children_size() const;
  void clear_children();
  static const int kChildrenFieldNumber = 3;
  ::tensorflow::profiler::op_profile::Node* mutable_children(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::profiler::op_profile::Node >*
      mutable_children();
  const ::tensorflow::profiler::op_profile::Node& children(int index) const;
  ::tensorflow::profiler::op_profile::Node* add_children();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::profiler::op_profile::Node >&
      children() const;

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // .tensorflow.profiler.op_profile.Metrics metrics = 2;
  bool has_metrics() const;
  void clear_metrics();
  static const int kMetricsFieldNumber = 2;
  private:
  const ::tensorflow::profiler::op_profile::Metrics& _internal_metrics() const;
  public:
  const ::tensorflow::profiler::op_profile::Metrics& metrics() const;
  ::tensorflow::profiler::op_profile::Metrics* release_metrics();
  ::tensorflow::profiler::op_profile::Metrics* mutable_metrics();
  void set_allocated_metrics(::tensorflow::profiler::op_profile::Metrics* metrics);

  // int32 num_children = 6;
  void clear_num_children();
  static const int kNumChildrenFieldNumber = 6;
  ::google::protobuf::int32 num_children() const;
  void set_num_children(::google::protobuf::int32 value);

  // .tensorflow.profiler.op_profile.Node.InstructionCategory category = 4;
  bool has_category() const;
  void clear_category();
  static const int kCategoryFieldNumber = 4;
  private:
  const ::tensorflow::profiler::op_profile::Node_InstructionCategory& _internal_category() const;
  public:
  const ::tensorflow::profiler::op_profile::Node_InstructionCategory& category() const;
  ::tensorflow::profiler::op_profile::Node_InstructionCategory* release_category();
  ::tensorflow::profiler::op_profile::Node_InstructionCategory* mutable_category();
  void set_allocated_category(::tensorflow::profiler::op_profile::Node_InstructionCategory* category);

  // .tensorflow.profiler.op_profile.Node.XLAInstruction xla = 5;
  bool has_xla() const;
  void clear_xla();
  static const int kXlaFieldNumber = 5;
  private:
  const ::tensorflow::profiler::op_profile::Node_XLAInstruction& _internal_xla() const;
  public:
  const ::tensorflow::profiler::op_profile::Node_XLAInstruction& xla() const;
  ::tensorflow::profiler::op_profile::Node_XLAInstruction* release_xla();
  ::tensorflow::profiler::op_profile::Node_XLAInstruction* mutable_xla();
  void set_allocated_xla(::tensorflow::profiler::op_profile::Node_XLAInstruction* xla);

  void clear_contents();
  ContentsCase contents_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.profiler.op_profile.Node)
 private:
  void set_has_category();
  void set_has_xla();

  inline bool has_contents() const;
  inline void clear_has_contents();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::profiler::op_profile::Node > children_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::tensorflow::profiler::op_profile::Metrics* metrics_;
  ::google::protobuf::int32 num_children_;
  union ContentsUnion {
    ContentsUnion() {}
    ::tensorflow::profiler::op_profile::Node_InstructionCategory* category_;
    ::tensorflow::profiler::op_profile::Node_XLAInstruction* xla_;
  } contents_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Metrics : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.profiler.op_profile.Metrics) */ {
 public:
  Metrics();
  virtual ~Metrics();

  Metrics(const Metrics& from);

  inline Metrics& operator=(const Metrics& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Metrics(Metrics&& from) noexcept
    : Metrics() {
    *this = ::std::move(from);
  }

  inline Metrics& operator=(Metrics&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Metrics& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Metrics* internal_default_instance() {
    return reinterpret_cast<const Metrics*>(
               &_Metrics_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  void Swap(Metrics* other);
  friend void swap(Metrics& a, Metrics& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Metrics* New() const final {
    return CreateMaybeMessage<Metrics>(NULL);
  }

  Metrics* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Metrics>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Metrics& from);
  void MergeFrom(const Metrics& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Metrics* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // double time = 1;
  void clear_time();
  static const int kTimeFieldNumber = 1;
  double time() const;
  void set_time(double value);

  // double flops = 2;
  void clear_flops();
  static const int kFlopsFieldNumber = 2;
  double flops() const;
  void set_flops(double value);

  // double memory_bandwidth = 3;
  void clear_memory_bandwidth();
  static const int kMemoryBandwidthFieldNumber = 3;
  double memory_bandwidth() const;
  void set_memory_bandwidth(double value);

  // double raw_time = 11;
  void clear_raw_time();
  static const int kRawTimeFieldNumber = 11;
  double raw_time() const;
  void set_raw_time(double value);

  // double raw_flops = 12;
  void clear_raw_flops();
  static const int kRawFlopsFieldNumber = 12;
  double raw_flops() const;
  void set_raw_flops(double value);

  // double raw_bytes_accessed = 13;
  void clear_raw_bytes_accessed();
  static const int kRawBytesAccessedFieldNumber = 13;
  double raw_bytes_accessed() const;
  void set_raw_bytes_accessed(double value);

  // @@protoc_insertion_point(class_scope:tensorflow.profiler.op_profile.Metrics)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  double time_;
  double flops_;
  double memory_bandwidth_;
  double raw_time_;
  double raw_flops_;
  double raw_bytes_accessed_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Profile

// .tensorflow.profiler.op_profile.Node by_category = 1;
inline bool Profile::has_by_category() const {
  return this != internal_default_instance() && by_category_ != NULL;
}
inline void Profile::clear_by_category() {
  if (GetArenaNoVirtual() == NULL && by_category_ != NULL) {
    delete by_category_;
  }
  by_category_ = NULL;
}
inline const ::tensorflow::profiler::op_profile::Node& Profile::_internal_by_category() const {
  return *by_category_;
}
inline const ::tensorflow::profiler::op_profile::Node& Profile::by_category() const {
  const ::tensorflow::profiler::op_profile::Node* p = by_category_;
  // @@protoc_insertion_point(field_get:tensorflow.profiler.op_profile.Profile.by_category)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::profiler::op_profile::Node*>(
      &::tensorflow::profiler::op_profile::_Node_default_instance_);
}
inline ::tensorflow::profiler::op_profile::Node* Profile::release_by_category() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.op_profile.Profile.by_category)
  
  ::tensorflow::profiler::op_profile::Node* temp = by_category_;
  by_category_ = NULL;
  return temp;
}
inline ::tensorflow::profiler::op_profile::Node* Profile::mutable_by_category() {
  
  if (by_category_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::profiler::op_profile::Node>(GetArenaNoVirtual());
    by_category_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.op_profile.Profile.by_category)
  return by_category_;
}
inline void Profile::set_allocated_by_category(::tensorflow::profiler::op_profile::Node* by_category) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete by_category_;
  }
  if (by_category) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      by_category = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, by_category, submessage_arena);
    }
    
  } else {
    
  }
  by_category_ = by_category;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.op_profile.Profile.by_category)
}

// .tensorflow.profiler.op_profile.Node by_program = 4;
inline bool Profile::has_by_program() const {
  return this != internal_default_instance() && by_program_ != NULL;
}
inline void Profile::clear_by_program() {
  if (GetArenaNoVirtual() == NULL && by_program_ != NULL) {
    delete by_program_;
  }
  by_program_ = NULL;
}
inline const ::tensorflow::profiler::op_profile::Node& Profile::_internal_by_program() const {
  return *by_program_;
}
inline const ::tensorflow::profiler::op_profile::Node& Profile::by_program() const {
  const ::tensorflow::profiler::op_profile::Node* p = by_program_;
  // @@protoc_insertion_point(field_get:tensorflow.profiler.op_profile.Profile.by_program)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::profiler::op_profile::Node*>(
      &::tensorflow::profiler::op_profile::_Node_default_instance_);
}
inline ::tensorflow::profiler::op_profile::Node* Profile::release_by_program() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.op_profile.Profile.by_program)
  
  ::tensorflow::profiler::op_profile::Node* temp = by_program_;
  by_program_ = NULL;
  return temp;
}
inline ::tensorflow::profiler::op_profile::Node* Profile::mutable_by_program() {
  
  if (by_program_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::profiler::op_profile::Node>(GetArenaNoVirtual());
    by_program_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.op_profile.Profile.by_program)
  return by_program_;
}
inline void Profile::set_allocated_by_program(::tensorflow::profiler::op_profile::Node* by_program) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete by_program_;
  }
  if (by_program) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      by_program = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, by_program, submessage_arena);
    }
    
  } else {
    
  }
  by_program_ = by_program;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.op_profile.Profile.by_program)
}

// -------------------------------------------------------------------

// Node_InstructionCategory

// -------------------------------------------------------------------

// Node_XLAInstruction_LayoutAnalysis_Dimension

// int32 size = 1;
inline void Node_XLAInstruction_LayoutAnalysis_Dimension::clear_size() {
  size_ = 0;
}
inline ::google::protobuf::int32 Node_XLAInstruction_LayoutAnalysis_Dimension::size() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension.size)
  return size_;
}
inline void Node_XLAInstruction_LayoutAnalysis_Dimension::set_size(::google::protobuf::int32 value) {
  
  size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension.size)
}

// int32 alignment = 2;
inline void Node_XLAInstruction_LayoutAnalysis_Dimension::clear_alignment() {
  alignment_ = 0;
}
inline ::google::protobuf::int32 Node_XLAInstruction_LayoutAnalysis_Dimension::alignment() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension.alignment)
  return alignment_;
}
inline void Node_XLAInstruction_LayoutAnalysis_Dimension::set_alignment(::google::protobuf::int32 value) {
  
  alignment_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension.alignment)
}

// string semantics = 3;
inline void Node_XLAInstruction_LayoutAnalysis_Dimension::clear_semantics() {
  semantics_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Node_XLAInstruction_LayoutAnalysis_Dimension::semantics() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension.semantics)
  return semantics_.GetNoArena();
}
inline void Node_XLAInstruction_LayoutAnalysis_Dimension::set_semantics(const ::std::string& value) {
  
  semantics_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension.semantics)
}
#if LANG_CXX11
inline void Node_XLAInstruction_LayoutAnalysis_Dimension::set_semantics(::std::string&& value) {
  
  semantics_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension.semantics)
}
#endif
inline void Node_XLAInstruction_LayoutAnalysis_Dimension::set_semantics(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  semantics_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension.semantics)
}
inline void Node_XLAInstruction_LayoutAnalysis_Dimension::set_semantics(const char* value, size_t size) {
  
  semantics_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension.semantics)
}
inline ::std::string* Node_XLAInstruction_LayoutAnalysis_Dimension::mutable_semantics() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension.semantics)
  return semantics_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Node_XLAInstruction_LayoutAnalysis_Dimension::release_semantics() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension.semantics)
  
  return semantics_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Node_XLAInstruction_LayoutAnalysis_Dimension::set_allocated_semantics(::std::string* semantics) {
  if (semantics != NULL) {
    
  } else {
    
  }
  semantics_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), semantics);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension.semantics)
}

// -------------------------------------------------------------------

// Node_XLAInstruction_LayoutAnalysis

// repeated .tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.Dimension dimensions = 1;
inline int Node_XLAInstruction_LayoutAnalysis::dimensions_size() const {
  return dimensions_.size();
}
inline void Node_XLAInstruction_LayoutAnalysis::clear_dimensions() {
  dimensions_.Clear();
}
inline ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis_Dimension* Node_XLAInstruction_LayoutAnalysis::mutable_dimensions(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.dimensions)
  return dimensions_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis_Dimension >*
Node_XLAInstruction_LayoutAnalysis::mutable_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.dimensions)
  return &dimensions_;
}
inline const ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis_Dimension& Node_XLAInstruction_LayoutAnalysis::dimensions(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.dimensions)
  return dimensions_.Get(index);
}
inline ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis_Dimension* Node_XLAInstruction_LayoutAnalysis::add_dimensions() {
  // @@protoc_insertion_point(field_add:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.dimensions)
  return dimensions_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis_Dimension >&
Node_XLAInstruction_LayoutAnalysis::dimensions() const {
  // @@protoc_insertion_point(field_list:tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis.dimensions)
  return dimensions_;
}

// -------------------------------------------------------------------

// Node_XLAInstruction

// string op = 1;
inline void Node_XLAInstruction::clear_op() {
  op_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Node_XLAInstruction::op() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.op_profile.Node.XLAInstruction.op)
  return op_.GetNoArena();
}
inline void Node_XLAInstruction::set_op(const ::std::string& value) {
  
  op_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.op_profile.Node.XLAInstruction.op)
}
#if LANG_CXX11
inline void Node_XLAInstruction::set_op(::std::string&& value) {
  
  op_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.profiler.op_profile.Node.XLAInstruction.op)
}
#endif
inline void Node_XLAInstruction::set_op(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  op_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.profiler.op_profile.Node.XLAInstruction.op)
}
inline void Node_XLAInstruction::set_op(const char* value, size_t size) {
  
  op_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.profiler.op_profile.Node.XLAInstruction.op)
}
inline ::std::string* Node_XLAInstruction::mutable_op() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.op_profile.Node.XLAInstruction.op)
  return op_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Node_XLAInstruction::release_op() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.op_profile.Node.XLAInstruction.op)
  
  return op_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Node_XLAInstruction::set_allocated_op(::std::string* op) {
  if (op != NULL) {
    
  } else {
    
  }
  op_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), op);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.op_profile.Node.XLAInstruction.op)
}

// string expression = 2;
inline void Node_XLAInstruction::clear_expression() {
  expression_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Node_XLAInstruction::expression() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.op_profile.Node.XLAInstruction.expression)
  return expression_.GetNoArena();
}
inline void Node_XLAInstruction::set_expression(const ::std::string& value) {
  
  expression_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.op_profile.Node.XLAInstruction.expression)
}
#if LANG_CXX11
inline void Node_XLAInstruction::set_expression(::std::string&& value) {
  
  expression_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.profiler.op_profile.Node.XLAInstruction.expression)
}
#endif
inline void Node_XLAInstruction::set_expression(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  expression_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.profiler.op_profile.Node.XLAInstruction.expression)
}
inline void Node_XLAInstruction::set_expression(const char* value, size_t size) {
  
  expression_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.profiler.op_profile.Node.XLAInstruction.expression)
}
inline ::std::string* Node_XLAInstruction::mutable_expression() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.op_profile.Node.XLAInstruction.expression)
  return expression_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Node_XLAInstruction::release_expression() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.op_profile.Node.XLAInstruction.expression)
  
  return expression_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Node_XLAInstruction::set_allocated_expression(::std::string* expression) {
  if (expression != NULL) {
    
  } else {
    
  }
  expression_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), expression);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.op_profile.Node.XLAInstruction.expression)
}

// string provenance = 3;
inline void Node_XLAInstruction::clear_provenance() {
  provenance_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Node_XLAInstruction::provenance() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.op_profile.Node.XLAInstruction.provenance)
  return provenance_.GetNoArena();
}
inline void Node_XLAInstruction::set_provenance(const ::std::string& value) {
  
  provenance_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.op_profile.Node.XLAInstruction.provenance)
}
#if LANG_CXX11
inline void Node_XLAInstruction::set_provenance(::std::string&& value) {
  
  provenance_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.profiler.op_profile.Node.XLAInstruction.provenance)
}
#endif
inline void Node_XLAInstruction::set_provenance(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  provenance_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.profiler.op_profile.Node.XLAInstruction.provenance)
}
inline void Node_XLAInstruction::set_provenance(const char* value, size_t size) {
  
  provenance_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.profiler.op_profile.Node.XLAInstruction.provenance)
}
inline ::std::string* Node_XLAInstruction::mutable_provenance() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.op_profile.Node.XLAInstruction.provenance)
  return provenance_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Node_XLAInstruction::release_provenance() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.op_profile.Node.XLAInstruction.provenance)
  
  return provenance_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Node_XLAInstruction::set_allocated_provenance(::std::string* provenance) {
  if (provenance != NULL) {
    
  } else {
    
  }
  provenance_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), provenance);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.op_profile.Node.XLAInstruction.provenance)
}

// string category = 4;
inline void Node_XLAInstruction::clear_category() {
  category_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Node_XLAInstruction::category() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.op_profile.Node.XLAInstruction.category)
  return category_.GetNoArena();
}
inline void Node_XLAInstruction::set_category(const ::std::string& value) {
  
  category_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.op_profile.Node.XLAInstruction.category)
}
#if LANG_CXX11
inline void Node_XLAInstruction::set_category(::std::string&& value) {
  
  category_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.profiler.op_profile.Node.XLAInstruction.category)
}
#endif
inline void Node_XLAInstruction::set_category(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  category_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.profiler.op_profile.Node.XLAInstruction.category)
}
inline void Node_XLAInstruction::set_category(const char* value, size_t size) {
  
  category_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.profiler.op_profile.Node.XLAInstruction.category)
}
inline ::std::string* Node_XLAInstruction::mutable_category() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.op_profile.Node.XLAInstruction.category)
  return category_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Node_XLAInstruction::release_category() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.op_profile.Node.XLAInstruction.category)
  
  return category_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Node_XLAInstruction::set_allocated_category(::std::string* category) {
  if (category != NULL) {
    
  } else {
    
  }
  category_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), category);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.op_profile.Node.XLAInstruction.category)
}

// .tensorflow.profiler.op_profile.Node.XLAInstruction.LayoutAnalysis layout = 5;
inline bool Node_XLAInstruction::has_layout() const {
  return this != internal_default_instance() && layout_ != NULL;
}
inline void Node_XLAInstruction::clear_layout() {
  if (GetArenaNoVirtual() == NULL && layout_ != NULL) {
    delete layout_;
  }
  layout_ = NULL;
}
inline const ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis& Node_XLAInstruction::_internal_layout() const {
  return *layout_;
}
inline const ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis& Node_XLAInstruction::layout() const {
  const ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis* p = layout_;
  // @@protoc_insertion_point(field_get:tensorflow.profiler.op_profile.Node.XLAInstruction.layout)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis*>(
      &::tensorflow::profiler::op_profile::_Node_XLAInstruction_LayoutAnalysis_default_instance_);
}
inline ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis* Node_XLAInstruction::release_layout() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.op_profile.Node.XLAInstruction.layout)
  
  ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis* temp = layout_;
  layout_ = NULL;
  return temp;
}
inline ::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis* Node_XLAInstruction::mutable_layout() {
  
  if (layout_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis>(GetArenaNoVirtual());
    layout_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.op_profile.Node.XLAInstruction.layout)
  return layout_;
}
inline void Node_XLAInstruction::set_allocated_layout(::tensorflow::profiler::op_profile::Node_XLAInstruction_LayoutAnalysis* layout) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete layout_;
  }
  if (layout) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      layout = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, layout, submessage_arena);
    }
    
  } else {
    
  }
  layout_ = layout;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.op_profile.Node.XLAInstruction.layout)
}

// -------------------------------------------------------------------

// Node

// string name = 1;
inline void Node::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Node::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.op_profile.Node.name)
  return name_.GetNoArena();
}
inline void Node::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.op_profile.Node.name)
}
#if LANG_CXX11
inline void Node::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.profiler.op_profile.Node.name)
}
#endif
inline void Node::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.profiler.op_profile.Node.name)
}
inline void Node::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.profiler.op_profile.Node.name)
}
inline ::std::string* Node::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.op_profile.Node.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Node::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.op_profile.Node.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Node::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.op_profile.Node.name)
}

// .tensorflow.profiler.op_profile.Metrics metrics = 2;
inline bool Node::has_metrics() const {
  return this != internal_default_instance() && metrics_ != NULL;
}
inline void Node::clear_metrics() {
  if (GetArenaNoVirtual() == NULL && metrics_ != NULL) {
    delete metrics_;
  }
  metrics_ = NULL;
}
inline const ::tensorflow::profiler::op_profile::Metrics& Node::_internal_metrics() const {
  return *metrics_;
}
inline const ::tensorflow::profiler::op_profile::Metrics& Node::metrics() const {
  const ::tensorflow::profiler::op_profile::Metrics* p = metrics_;
  // @@protoc_insertion_point(field_get:tensorflow.profiler.op_profile.Node.metrics)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::profiler::op_profile::Metrics*>(
      &::tensorflow::profiler::op_profile::_Metrics_default_instance_);
}
inline ::tensorflow::profiler::op_profile::Metrics* Node::release_metrics() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.op_profile.Node.metrics)
  
  ::tensorflow::profiler::op_profile::Metrics* temp = metrics_;
  metrics_ = NULL;
  return temp;
}
inline ::tensorflow::profiler::op_profile::Metrics* Node::mutable_metrics() {
  
  if (metrics_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::profiler::op_profile::Metrics>(GetArenaNoVirtual());
    metrics_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.op_profile.Node.metrics)
  return metrics_;
}
inline void Node::set_allocated_metrics(::tensorflow::profiler::op_profile::Metrics* metrics) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete metrics_;
  }
  if (metrics) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      metrics = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, metrics, submessage_arena);
    }
    
  } else {
    
  }
  metrics_ = metrics;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.op_profile.Node.metrics)
}

// repeated .tensorflow.profiler.op_profile.Node children = 3;
inline int Node::children_size() const {
  return children_.size();
}
inline void Node::clear_children() {
  children_.Clear();
}
inline ::tensorflow::profiler::op_profile::Node* Node::mutable_children(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.op_profile.Node.children)
  return children_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::profiler::op_profile::Node >*
Node::mutable_children() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.profiler.op_profile.Node.children)
  return &children_;
}
inline const ::tensorflow::profiler::op_profile::Node& Node::children(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.op_profile.Node.children)
  return children_.Get(index);
}
inline ::tensorflow::profiler::op_profile::Node* Node::add_children() {
  // @@protoc_insertion_point(field_add:tensorflow.profiler.op_profile.Node.children)
  return children_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::profiler::op_profile::Node >&
Node::children() const {
  // @@protoc_insertion_point(field_list:tensorflow.profiler.op_profile.Node.children)
  return children_;
}

// .tensorflow.profiler.op_profile.Node.InstructionCategory category = 4;
inline bool Node::has_category() const {
  return contents_case() == kCategory;
}
inline void Node::set_has_category() {
  _oneof_case_[0] = kCategory;
}
inline void Node::clear_category() {
  if (has_category()) {
    delete contents_.category_;
    clear_has_contents();
  }
}
inline const ::tensorflow::profiler::op_profile::Node_InstructionCategory& Node::_internal_category() const {
  return *contents_.category_;
}
inline ::tensorflow::profiler::op_profile::Node_InstructionCategory* Node::release_category() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.op_profile.Node.category)
  if (has_category()) {
    clear_has_contents();
      ::tensorflow::profiler::op_profile::Node_InstructionCategory* temp = contents_.category_;
    contents_.category_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::profiler::op_profile::Node_InstructionCategory& Node::category() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.op_profile.Node.category)
  return has_category()
      ? *contents_.category_
      : *reinterpret_cast< ::tensorflow::profiler::op_profile::Node_InstructionCategory*>(&::tensorflow::profiler::op_profile::_Node_InstructionCategory_default_instance_);
}
inline ::tensorflow::profiler::op_profile::Node_InstructionCategory* Node::mutable_category() {
  if (!has_category()) {
    clear_contents();
    set_has_category();
    contents_.category_ = CreateMaybeMessage< ::tensorflow::profiler::op_profile::Node_InstructionCategory >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.op_profile.Node.category)
  return contents_.category_;
}

// .tensorflow.profiler.op_profile.Node.XLAInstruction xla = 5;
inline bool Node::has_xla() const {
  return contents_case() == kXla;
}
inline void Node::set_has_xla() {
  _oneof_case_[0] = kXla;
}
inline void Node::clear_xla() {
  if (has_xla()) {
    delete contents_.xla_;
    clear_has_contents();
  }
}
inline const ::tensorflow::profiler::op_profile::Node_XLAInstruction& Node::_internal_xla() const {
  return *contents_.xla_;
}
inline ::tensorflow::profiler::op_profile::Node_XLAInstruction* Node::release_xla() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.op_profile.Node.xla)
  if (has_xla()) {
    clear_has_contents();
      ::tensorflow::profiler::op_profile::Node_XLAInstruction* temp = contents_.xla_;
    contents_.xla_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::profiler::op_profile::Node_XLAInstruction& Node::xla() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.op_profile.Node.xla)
  return has_xla()
      ? *contents_.xla_
      : *reinterpret_cast< ::tensorflow::profiler::op_profile::Node_XLAInstruction*>(&::tensorflow::profiler::op_profile::_Node_XLAInstruction_default_instance_);
}
inline ::tensorflow::profiler::op_profile::Node_XLAInstruction* Node::mutable_xla() {
  if (!has_xla()) {
    clear_contents();
    set_has_xla();
    contents_.xla_ = CreateMaybeMessage< ::tensorflow::profiler::op_profile::Node_XLAInstruction >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.op_profile.Node.xla)
  return contents_.xla_;
}

// int32 num_children = 6;
inline void Node::clear_num_children() {
  num_children_ = 0;
}
inline ::google::protobuf::int32 Node::num_children() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.op_profile.Node.num_children)
  return num_children_;
}
inline void Node::set_num_children(::google::protobuf::int32 value) {
  
  num_children_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.op_profile.Node.num_children)
}

inline bool Node::has_contents() const {
  return contents_case() != CONTENTS_NOT_SET;
}
inline void Node::clear_has_contents() {
  _oneof_case_[0] = CONTENTS_NOT_SET;
}
inline Node::ContentsCase Node::contents_case() const {
  return Node::ContentsCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// Metrics

// double time = 1;
inline void Metrics::clear_time() {
  time_ = 0;
}
inline double Metrics::time() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.op_profile.Metrics.time)
  return time_;
}
inline void Metrics::set_time(double value) {
  
  time_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.op_profile.Metrics.time)
}

// double flops = 2;
inline void Metrics::clear_flops() {
  flops_ = 0;
}
inline double Metrics::flops() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.op_profile.Metrics.flops)
  return flops_;
}
inline void Metrics::set_flops(double value) {
  
  flops_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.op_profile.Metrics.flops)
}

// double memory_bandwidth = 3;
inline void Metrics::clear_memory_bandwidth() {
  memory_bandwidth_ = 0;
}
inline double Metrics::memory_bandwidth() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.op_profile.Metrics.memory_bandwidth)
  return memory_bandwidth_;
}
inline void Metrics::set_memory_bandwidth(double value) {
  
  memory_bandwidth_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.op_profile.Metrics.memory_bandwidth)
}

// double raw_time = 11;
inline void Metrics::clear_raw_time() {
  raw_time_ = 0;
}
inline double Metrics::raw_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.op_profile.Metrics.raw_time)
  return raw_time_;
}
inline void Metrics::set_raw_time(double value) {
  
  raw_time_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.op_profile.Metrics.raw_time)
}

// double raw_flops = 12;
inline void Metrics::clear_raw_flops() {
  raw_flops_ = 0;
}
inline double Metrics::raw_flops() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.op_profile.Metrics.raw_flops)
  return raw_flops_;
}
inline void Metrics::set_raw_flops(double value) {
  
  raw_flops_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.op_profile.Metrics.raw_flops)
}

// double raw_bytes_accessed = 13;
inline void Metrics::clear_raw_bytes_accessed() {
  raw_bytes_accessed_ = 0;
}
inline double Metrics::raw_bytes_accessed() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.op_profile.Metrics.raw_bytes_accessed)
  return raw_bytes_accessed_;
}
inline void Metrics::set_raw_bytes_accessed(double value) {
  
  raw_bytes_accessed_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.profiler.op_profile.Metrics.raw_bytes_accessed)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace op_profile
}  // namespace profiler
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2fop_5fprofile_2eproto

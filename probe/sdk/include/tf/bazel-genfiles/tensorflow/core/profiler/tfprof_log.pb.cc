// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/profiler/tfprof_log.proto

#include "tensorflow/core/profiler/tfprof_log.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_AttrValue;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_AllocationRecord;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto
namespace protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_CodeDef_Trace;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_Memory;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_OpLogProto_IdToStringEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_ProfileNode_InputsEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_ProfileNode_OutputsEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_ProfileNode_SrcOutputIndexEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_ProfileProto_IdToStringEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_Tuple;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_CodeDef;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_ExecMemory;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_ExecMemory_OutputMemoryEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_ExecProfile_AcceleratorExecsEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_ExecProfile_CpuExecsEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_ExecTime;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_OpLogEntry;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_ProfileNode_AttrsEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_ProfileNode_ExecsEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_ProfileNode_InputShapesEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_ProfileNode_OutputShapesEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_ProfileProto_NodesEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto ::google::protobuf::internal::SCCInfo<4> scc_info_ExecProfile;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto ::google::protobuf::internal::SCCInfo<8> scc_info_ProfileNode;
}  // namespace protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto
namespace tensorflow {
namespace tfprof {
class CodeDef_TraceDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CodeDef_Trace>
      _instance;
} _CodeDef_Trace_default_instance_;
class CodeDefDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CodeDef>
      _instance;
} _CodeDef_default_instance_;
class OpLogEntryDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<OpLogEntry>
      _instance;
} _OpLogEntry_default_instance_;
class OpLogProto_IdToStringEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<OpLogProto_IdToStringEntry_DoNotUse>
      _instance;
} _OpLogProto_IdToStringEntry_DoNotUse_default_instance_;
class OpLogProtoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<OpLogProto>
      _instance;
} _OpLogProto_default_instance_;
class ProfileProto_NodesEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ProfileProto_NodesEntry_DoNotUse>
      _instance;
} _ProfileProto_NodesEntry_DoNotUse_default_instance_;
class ProfileProto_IdToStringEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ProfileProto_IdToStringEntry_DoNotUse>
      _instance;
} _ProfileProto_IdToStringEntry_DoNotUse_default_instance_;
class ProfileProtoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ProfileProto>
      _instance;
} _ProfileProto_default_instance_;
class ProfileNode_InputsEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ProfileNode_InputsEntry_DoNotUse>
      _instance;
} _ProfileNode_InputsEntry_DoNotUse_default_instance_;
class ProfileNode_InputShapesEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ProfileNode_InputShapesEntry_DoNotUse>
      _instance;
} _ProfileNode_InputShapesEntry_DoNotUse_default_instance_;
class ProfileNode_OutputsEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ProfileNode_OutputsEntry_DoNotUse>
      _instance;
} _ProfileNode_OutputsEntry_DoNotUse_default_instance_;
class ProfileNode_OutputShapesEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ProfileNode_OutputShapesEntry_DoNotUse>
      _instance;
} _ProfileNode_OutputShapesEntry_DoNotUse_default_instance_;
class ProfileNode_SrcOutputIndexEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ProfileNode_SrcOutputIndexEntry_DoNotUse>
      _instance;
} _ProfileNode_SrcOutputIndexEntry_DoNotUse_default_instance_;
class ProfileNode_AttrsEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ProfileNode_AttrsEntry_DoNotUse>
      _instance;
} _ProfileNode_AttrsEntry_DoNotUse_default_instance_;
class ProfileNode_ExecsEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ProfileNode_ExecsEntry_DoNotUse>
      _instance;
} _ProfileNode_ExecsEntry_DoNotUse_default_instance_;
class ProfileNodeDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ProfileNode>
      _instance;
} _ProfileNode_default_instance_;
class ExecProfile_AcceleratorExecsEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ExecProfile_AcceleratorExecsEntry_DoNotUse>
      _instance;
} _ExecProfile_AcceleratorExecsEntry_DoNotUse_default_instance_;
class ExecProfile_CpuExecsEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ExecProfile_CpuExecsEntry_DoNotUse>
      _instance;
} _ExecProfile_CpuExecsEntry_DoNotUse_default_instance_;
class ExecProfileDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ExecProfile>
      _instance;
} _ExecProfile_default_instance_;
class ExecTimeDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ExecTime>
      _instance;
} _ExecTime_default_instance_;
class ExecMemory_OutputMemoryEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ExecMemory_OutputMemoryEntry_DoNotUse>
      _instance;
} _ExecMemory_OutputMemoryEntry_DoNotUse_default_instance_;
class ExecMemoryDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ExecMemory>
      _instance;
} _ExecMemory_default_instance_;
class TupleDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Tuple>
      _instance;
} _Tuple_default_instance_;
class MemoryDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Memory>
      _instance;
} _Memory_default_instance_;
}  // namespace tfprof
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto {
static void InitDefaultsCodeDef_Trace() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_CodeDef_Trace_default_instance_;
    new (ptr) ::tensorflow::tfprof::CodeDef_Trace();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tfprof::CodeDef_Trace::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_CodeDef_Trace =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsCodeDef_Trace}, {}};

static void InitDefaultsCodeDef() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_CodeDef_default_instance_;
    new (ptr) ::tensorflow::tfprof::CodeDef();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tfprof::CodeDef::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_CodeDef =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsCodeDef}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_CodeDef_Trace.base,}};

static void InitDefaultsOpLogEntry() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_OpLogEntry_default_instance_;
    new (ptr) ::tensorflow::tfprof::OpLogEntry();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tfprof::OpLogEntry::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_OpLogEntry =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsOpLogEntry}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_CodeDef.base,}};

static void InitDefaultsOpLogProto_IdToStringEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_OpLogProto_IdToStringEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::tfprof::OpLogProto_IdToStringEntry_DoNotUse();
  }
  ::tensorflow::tfprof::OpLogProto_IdToStringEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_OpLogProto_IdToStringEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsOpLogProto_IdToStringEntry_DoNotUse}, {}};

static void InitDefaultsOpLogProto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_OpLogProto_default_instance_;
    new (ptr) ::tensorflow::tfprof::OpLogProto();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tfprof::OpLogProto::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_OpLogProto =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsOpLogProto}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_OpLogEntry.base,
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_OpLogProto_IdToStringEntry_DoNotUse.base,}};

static void InitDefaultsProfileProto_NodesEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_ProfileProto_NodesEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::tfprof::ProfileProto_NodesEntry_DoNotUse();
  }
  ::tensorflow::tfprof::ProfileProto_NodesEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_ProfileProto_NodesEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsProfileProto_NodesEntry_DoNotUse}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_ProfileNode.base,}};

static void InitDefaultsProfileProto_IdToStringEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_ProfileProto_IdToStringEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::tfprof::ProfileProto_IdToStringEntry_DoNotUse();
  }
  ::tensorflow::tfprof::ProfileProto_IdToStringEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_ProfileProto_IdToStringEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsProfileProto_IdToStringEntry_DoNotUse}, {}};

static void InitDefaultsProfileProto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_ProfileProto_default_instance_;
    new (ptr) ::tensorflow::tfprof::ProfileProto();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tfprof::ProfileProto::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_ProfileProto =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsProfileProto}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_ProfileProto_NodesEntry_DoNotUse.base,
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_ProfileProto_IdToStringEntry_DoNotUse.base,}};

static void InitDefaultsProfileNode_InputsEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_ProfileNode_InputsEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::tfprof::ProfileNode_InputsEntry_DoNotUse();
  }
  ::tensorflow::tfprof::ProfileNode_InputsEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_ProfileNode_InputsEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsProfileNode_InputsEntry_DoNotUse}, {}};

static void InitDefaultsProfileNode_InputShapesEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_ProfileNode_InputShapesEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::tfprof::ProfileNode_InputShapesEntry_DoNotUse();
  }
  ::tensorflow::tfprof::ProfileNode_InputShapesEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_ProfileNode_InputShapesEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsProfileNode_InputShapesEntry_DoNotUse}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_Tuple.base,}};

static void InitDefaultsProfileNode_OutputsEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_ProfileNode_OutputsEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::tfprof::ProfileNode_OutputsEntry_DoNotUse();
  }
  ::tensorflow::tfprof::ProfileNode_OutputsEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_ProfileNode_OutputsEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsProfileNode_OutputsEntry_DoNotUse}, {}};

static void InitDefaultsProfileNode_OutputShapesEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_ProfileNode_OutputShapesEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::tfprof::ProfileNode_OutputShapesEntry_DoNotUse();
  }
  ::tensorflow::tfprof::ProfileNode_OutputShapesEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_ProfileNode_OutputShapesEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsProfileNode_OutputShapesEntry_DoNotUse}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_Tuple.base,}};

static void InitDefaultsProfileNode_SrcOutputIndexEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_ProfileNode_SrcOutputIndexEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::tfprof::ProfileNode_SrcOutputIndexEntry_DoNotUse();
  }
  ::tensorflow::tfprof::ProfileNode_SrcOutputIndexEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_ProfileNode_SrcOutputIndexEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsProfileNode_SrcOutputIndexEntry_DoNotUse}, {}};

static void InitDefaultsProfileNode_AttrsEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_ProfileNode_AttrsEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::tfprof::ProfileNode_AttrsEntry_DoNotUse();
  }
  ::tensorflow::tfprof::ProfileNode_AttrsEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_ProfileNode_AttrsEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsProfileNode_AttrsEntry_DoNotUse}, {
      &protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::scc_info_AttrValue.base,}};

static void InitDefaultsProfileNode_ExecsEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_ProfileNode_ExecsEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::tfprof::ProfileNode_ExecsEntry_DoNotUse();
  }
  ::tensorflow::tfprof::ProfileNode_ExecsEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_ProfileNode_ExecsEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsProfileNode_ExecsEntry_DoNotUse}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_ExecProfile.base,}};

static void InitDefaultsProfileNode() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_ProfileNode_default_instance_;
    new (ptr) ::tensorflow::tfprof::ProfileNode();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tfprof::ProfileNode::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<8> scc_info_ProfileNode =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 8, InitDefaultsProfileNode}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_ProfileNode_InputsEntry_DoNotUse.base,
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_ProfileNode_InputShapesEntry_DoNotUse.base,
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_ProfileNode_OutputsEntry_DoNotUse.base,
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_ProfileNode_OutputShapesEntry_DoNotUse.base,
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_ProfileNode_SrcOutputIndexEntry_DoNotUse.base,
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_CodeDef.base,
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_ProfileNode_AttrsEntry_DoNotUse.base,
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_ProfileNode_ExecsEntry_DoNotUse.base,}};

static void InitDefaultsExecProfile_AcceleratorExecsEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_ExecProfile_AcceleratorExecsEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::tfprof::ExecProfile_AcceleratorExecsEntry_DoNotUse();
  }
  ::tensorflow::tfprof::ExecProfile_AcceleratorExecsEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_ExecProfile_AcceleratorExecsEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsExecProfile_AcceleratorExecsEntry_DoNotUse}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_ExecTime.base,}};

static void InitDefaultsExecProfile_CpuExecsEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_ExecProfile_CpuExecsEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::tfprof::ExecProfile_CpuExecsEntry_DoNotUse();
  }
  ::tensorflow::tfprof::ExecProfile_CpuExecsEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_ExecProfile_CpuExecsEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsExecProfile_CpuExecsEntry_DoNotUse}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_ExecTime.base,}};

static void InitDefaultsExecProfile() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_ExecProfile_default_instance_;
    new (ptr) ::tensorflow::tfprof::ExecProfile();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tfprof::ExecProfile::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<4> scc_info_ExecProfile =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 4, InitDefaultsExecProfile}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_ExecProfile_AcceleratorExecsEntry_DoNotUse.base,
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_ExecProfile_CpuExecsEntry_DoNotUse.base,
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_ExecMemory.base,
      &protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_AllocationRecord.base,}};

static void InitDefaultsExecTime() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_ExecTime_default_instance_;
    new (ptr) ::tensorflow::tfprof::ExecTime();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tfprof::ExecTime::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_ExecTime =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsExecTime}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_Tuple.base,}};

static void InitDefaultsExecMemory_OutputMemoryEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_ExecMemory_OutputMemoryEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::tfprof::ExecMemory_OutputMemoryEntry_DoNotUse();
  }
  ::tensorflow::tfprof::ExecMemory_OutputMemoryEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_ExecMemory_OutputMemoryEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsExecMemory_OutputMemoryEntry_DoNotUse}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_Memory.base,}};

static void InitDefaultsExecMemory() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_ExecMemory_default_instance_;
    new (ptr) ::tensorflow::tfprof::ExecMemory();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tfprof::ExecMemory::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_ExecMemory =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsExecMemory}, {
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_ExecMemory_OutputMemoryEntry_DoNotUse.base,}};

static void InitDefaultsTuple() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_Tuple_default_instance_;
    new (ptr) ::tensorflow::tfprof::Tuple();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tfprof::Tuple::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_Tuple =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsTuple}, {}};

static void InitDefaultsMemory() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tfprof::_Memory_default_instance_;
    new (ptr) ::tensorflow::tfprof::Memory();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tfprof::Memory::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_Memory =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsMemory}, {}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_CodeDef_Trace.base);
  ::google::protobuf::internal::InitSCC(&scc_info_CodeDef.base);
  ::google::protobuf::internal::InitSCC(&scc_info_OpLogEntry.base);
  ::google::protobuf::internal::InitSCC(&scc_info_OpLogProto_IdToStringEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_OpLogProto.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ProfileProto_NodesEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ProfileProto_IdToStringEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ProfileProto.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ProfileNode_InputsEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ProfileNode_InputShapesEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ProfileNode_OutputsEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ProfileNode_OutputShapesEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ProfileNode_SrcOutputIndexEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ProfileNode_AttrsEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ProfileNode_ExecsEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ProfileNode.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ExecProfile_AcceleratorExecsEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ExecProfile_CpuExecsEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ExecProfile.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ExecTime.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ExecMemory_OutputMemoryEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ExecMemory.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Tuple.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Memory.base);
}

::google::protobuf::Metadata file_level_metadata[24];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::CodeDef_Trace, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::CodeDef_Trace, file_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::CodeDef_Trace, file_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::CodeDef_Trace, lineno_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::CodeDef_Trace, function_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::CodeDef_Trace, function_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::CodeDef_Trace, line_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::CodeDef_Trace, line_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::CodeDef_Trace, func_start_line_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::CodeDef, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::CodeDef, traces_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OpLogEntry, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OpLogEntry, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OpLogEntry, float_ops_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OpLogEntry, types_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OpLogEntry, code_def_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OpLogProto_IdToStringEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OpLogProto_IdToStringEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OpLogProto_IdToStringEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OpLogProto_IdToStringEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OpLogProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OpLogProto, log_entries_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::OpLogProto, id_to_string_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileProto_NodesEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileProto_NodesEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileProto_NodesEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileProto_NodesEntry_DoNotUse, value_),
  0,
  1,
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileProto_IdToStringEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileProto_IdToStringEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileProto_IdToStringEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileProto_IdToStringEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileProto, nodes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileProto, has_trace_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileProto, miss_accelerator_stream_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileProto, steps_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileProto, id_to_string_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode_InputsEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode_InputsEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode_InputsEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode_InputsEntry_DoNotUse, value_),
  0,
  1,
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode_InputShapesEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode_InputShapesEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode_InputShapesEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode_InputShapesEntry_DoNotUse, value_),
  0,
  1,
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode_OutputsEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode_OutputsEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode_OutputsEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode_OutputsEntry_DoNotUse, value_),
  0,
  1,
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode_OutputShapesEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode_OutputShapesEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode_OutputShapesEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode_OutputShapesEntry_DoNotUse, value_),
  0,
  1,
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode_SrcOutputIndexEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode_SrcOutputIndexEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode_SrcOutputIndexEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode_SrcOutputIndexEntry_DoNotUse, value_),
  0,
  1,
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode_AttrsEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode_AttrsEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode_AttrsEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode_AttrsEntry_DoNotUse, value_),
  0,
  1,
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode_ExecsEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode_ExecsEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode_ExecsEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode_ExecsEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode, op_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode, id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode, inputs_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode, input_shapes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode, outputs_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode, output_shapes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode, src_output_index_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode, shape_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode, op_types_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode, canonical_device_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode, host_device_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode, float_ops_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode, trace_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode, attrs_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ProfileNode, execs_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecProfile_AcceleratorExecsEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecProfile_AcceleratorExecsEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecProfile_AcceleratorExecsEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecProfile_AcceleratorExecsEntry_DoNotUse, value_),
  0,
  1,
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecProfile_CpuExecsEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecProfile_CpuExecsEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecProfile_CpuExecsEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecProfile_CpuExecsEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecProfile, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecProfile, run_count_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecProfile, all_start_micros_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecProfile, latest_end_micros_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecProfile, accelerator_execs_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecProfile, cpu_execs_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecProfile, memory_execs_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecProfile, allocations_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecProfile, devices_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecTime, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecTime, times_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecMemory_OutputMemoryEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecMemory_OutputMemoryEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecMemory_OutputMemoryEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecMemory_OutputMemoryEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecMemory, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecMemory, memory_micros_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecMemory, host_temp_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecMemory, host_persistent_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecMemory, accelerator_temp_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecMemory, accelerator_persistent_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecMemory, requested_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecMemory, peak_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecMemory, residual_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecMemory, output_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecMemory, allocator_bytes_in_use_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::ExecMemory, output_memory_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::Tuple, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::Tuple, int64_values_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::Memory, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::Memory, bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tfprof::Memory, ptr_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::tfprof::CodeDef_Trace)},
  { 13, -1, sizeof(::tensorflow::tfprof::CodeDef)},
  { 19, -1, sizeof(::tensorflow::tfprof::OpLogEntry)},
  { 28, 35, sizeof(::tensorflow::tfprof::OpLogProto_IdToStringEntry_DoNotUse)},
  { 37, -1, sizeof(::tensorflow::tfprof::OpLogProto)},
  { 44, 51, sizeof(::tensorflow::tfprof::ProfileProto_NodesEntry_DoNotUse)},
  { 53, 60, sizeof(::tensorflow::tfprof::ProfileProto_IdToStringEntry_DoNotUse)},
  { 62, -1, sizeof(::tensorflow::tfprof::ProfileProto)},
  { 72, 79, sizeof(::tensorflow::tfprof::ProfileNode_InputsEntry_DoNotUse)},
  { 81, 88, sizeof(::tensorflow::tfprof::ProfileNode_InputShapesEntry_DoNotUse)},
  { 90, 97, sizeof(::tensorflow::tfprof::ProfileNode_OutputsEntry_DoNotUse)},
  { 99, 106, sizeof(::tensorflow::tfprof::ProfileNode_OutputShapesEntry_DoNotUse)},
  { 108, 115, sizeof(::tensorflow::tfprof::ProfileNode_SrcOutputIndexEntry_DoNotUse)},
  { 117, 124, sizeof(::tensorflow::tfprof::ProfileNode_AttrsEntry_DoNotUse)},
  { 126, 133, sizeof(::tensorflow::tfprof::ProfileNode_ExecsEntry_DoNotUse)},
  { 135, -1, sizeof(::tensorflow::tfprof::ProfileNode)},
  { 156, 163, sizeof(::tensorflow::tfprof::ExecProfile_AcceleratorExecsEntry_DoNotUse)},
  { 165, 172, sizeof(::tensorflow::tfprof::ExecProfile_CpuExecsEntry_DoNotUse)},
  { 174, -1, sizeof(::tensorflow::tfprof::ExecProfile)},
  { 187, -1, sizeof(::tensorflow::tfprof::ExecTime)},
  { 193, 200, sizeof(::tensorflow::tfprof::ExecMemory_OutputMemoryEntry_DoNotUse)},
  { 202, -1, sizeof(::tensorflow::tfprof::ExecMemory)},
  { 218, -1, sizeof(::tensorflow::tfprof::Tuple)},
  { 224, -1, sizeof(::tensorflow::tfprof::Memory)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_CodeDef_Trace_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_CodeDef_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_OpLogEntry_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_OpLogProto_IdToStringEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_OpLogProto_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_ProfileProto_NodesEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_ProfileProto_IdToStringEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_ProfileProto_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_ProfileNode_InputsEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_ProfileNode_InputShapesEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_ProfileNode_OutputsEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_ProfileNode_OutputShapesEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_ProfileNode_SrcOutputIndexEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_ProfileNode_AttrsEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_ProfileNode_ExecsEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_ProfileNode_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_ExecProfile_AcceleratorExecsEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_ExecProfile_CpuExecsEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_ExecProfile_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_ExecTime_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_ExecMemory_OutputMemoryEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_ExecMemory_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_Tuple_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tfprof::_Memory_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/profiler/tfprof_log.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 24);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n)tensorflow/core/profiler/tfprof_log.pr"
      "oto\022\021tensorflow.tfprof\032*tensorflow/core/"
      "framework/attr_value.proto\032*tensorflow/c"
      "ore/framework/step_stats.proto\"\337\001\n\007CodeD"
      "ef\0220\n\006traces\030\001 \003(\0132 .tensorflow.tfprof.C"
      "odeDef.Trace\032\241\001\n\005Trace\022\020\n\004file\030\001 \001(\tB\002\030\001"
      "\022\017\n\007file_id\030\006 \001(\003\022\016\n\006lineno\030\002 \001(\005\022\024\n\010fun"
      "ction\030\003 \001(\tB\002\030\001\022\023\n\013function_id\030\007 \001(\003\022\020\n\004"
      "line\030\004 \001(\tB\002\030\001\022\017\n\007line_id\030\010 \001(\003\022\027\n\017func_"
      "start_line\030\005 \001(\005\"j\n\nOpLogEntry\022\014\n\004name\030\001"
      " \001(\t\022\021\n\tfloat_ops\030\002 \001(\003\022\r\n\005types\030\003 \003(\t\022,"
      "\n\010code_def\030\004 \001(\0132\032.tensorflow.tfprof.Cod"
      "eDef\"\270\001\n\nOpLogProto\0222\n\013log_entries\030\001 \003(\013"
      "2\035.tensorflow.tfprof.OpLogEntry\022C\n\014id_to"
      "_string\030\002 \003(\0132-.tensorflow.tfprof.OpLogP"
      "roto.IdToStringEntry\0321\n\017IdToStringEntry\022"
      "\013\n\003key\030\001 \001(\003\022\r\n\005value\030\002 \001(\t:\0028\001\"\324\002\n\014Prof"
      "ileProto\0229\n\005nodes\030\001 \003(\0132*.tensorflow.tfp"
      "rof.ProfileProto.NodesEntry\022\021\n\thas_trace"
      "\030\002 \001(\010\022\037\n\027miss_accelerator_stream\030\005 \001(\010\022"
      "\r\n\005steps\030\003 \003(\003\022E\n\014id_to_string\030\004 \003(\0132/.t"
      "ensorflow.tfprof.ProfileProto.IdToString"
      "Entry\032L\n\nNodesEntry\022\013\n\003key\030\001 \001(\003\022-\n\005valu"
      "e\030\002 \001(\0132\036.tensorflow.tfprof.ProfileNode:"
      "\0028\001\0321\n\017IdToStringEntry\022\013\n\003key\030\001 \001(\003\022\r\n\005v"
      "alue\030\002 \001(\t:\0028\001\"\323\010\n\013ProfileNode\022\014\n\004name\030\001"
      " \001(\t\022\n\n\002op\030\t \001(\t\022\n\n\002id\030\r \001(\003\022:\n\006inputs\030\002"
      " \003(\0132*.tensorflow.tfprof.ProfileNode.Inp"
      "utsEntry\022E\n\014input_shapes\030\020 \003(\0132/.tensorf"
      "low.tfprof.ProfileNode.InputShapesEntry\022"
      "<\n\007outputs\030\003 \003(\0132+.tensorflow.tfprof.Pro"
      "fileNode.OutputsEntry\022G\n\routput_shapes\030\017"
      " \003(\01320.tensorflow.tfprof.ProfileNode.Out"
      "putShapesEntry\022L\n\020src_output_index\030\016 \003(\013"
      "22.tensorflow.tfprof.ProfileNode.SrcOutp"
      "utIndexEntry\022\r\n\005shape\030\004 \003(\003\022\020\n\010op_types\030"
      "\005 \003(\t\022\030\n\020canonical_device\030\006 \001(\t\022\023\n\013host_"
      "device\030\007 \001(\t\022\021\n\tfloat_ops\030\010 \001(\003\022)\n\005trace"
      "\030\n \001(\0132\032.tensorflow.tfprof.CodeDef\0228\n\005at"
      "trs\030\013 \003(\0132).tensorflow.tfprof.ProfileNod"
      "e.AttrsEntry\0228\n\005execs\030\014 \003(\0132).tensorflow"
      ".tfprof.ProfileNode.ExecsEntry\032-\n\013Inputs"
      "Entry\022\013\n\003key\030\001 \001(\005\022\r\n\005value\030\002 \001(\003:\0028\001\032L\n"
      "\020InputShapesEntry\022\013\n\003key\030\001 \001(\005\022\'\n\005value\030"
      "\002 \001(\0132\030.tensorflow.tfprof.Tuple:\0028\001\032.\n\014O"
      "utputsEntry\022\013\n\003key\030\001 \001(\005\022\r\n\005value\030\002 \001(\003:"
      "\0028\001\032M\n\021OutputShapesEntry\022\013\n\003key\030\001 \001(\005\022\'\n"
      "\005value\030\002 \001(\0132\030.tensorflow.tfprof.Tuple:\002"
      "8\001\0325\n\023SrcOutputIndexEntry\022\013\n\003key\030\001 \001(\003\022\r"
      "\n\005value\030\002 \001(\005:\0028\001\032C\n\nAttrsEntry\022\013\n\003key\030\001"
      " \001(\t\022$\n\005value\030\002 \001(\0132\025.tensorflow.AttrVal"
      "ue:\0028\001\032L\n\nExecsEntry\022\013\n\003key\030\001 \001(\003\022-\n\005val"
      "ue\030\002 \001(\0132\036.tensorflow.tfprof.ExecProfile"
      ":\0028\001\"\204\004\n\013ExecProfile\022\021\n\trun_count\030\001 \001(\003\022"
      "\030\n\020all_start_micros\030\002 \001(\003\022\031\n\021latest_end_"
      "micros\030\003 \001(\003\022O\n\021accelerator_execs\030\004 \003(\0132"
      "4.tensorflow.tfprof.ExecProfile.Accelera"
      "torExecsEntry\022\?\n\tcpu_execs\030\005 \003(\0132,.tenso"
      "rflow.tfprof.ExecProfile.CpuExecsEntry\0223"
      "\n\014memory_execs\030\007 \003(\0132\035.tensorflow.tfprof"
      ".ExecMemory\0221\n\013allocations\030\013 \003(\0132\034.tenso"
      "rflow.AllocationRecord\022\017\n\007devices\030\006 \003(\t\032"
      "T\n\025AcceleratorExecsEntry\022\013\n\003key\030\001 \001(\t\022*\n"
      "\005value\030\002 \001(\0132\033.tensorflow.tfprof.ExecTim"
      "e:\0028\001\032L\n\rCpuExecsEntry\022\013\n\003key\030\001 \001(\t\022*\n\005v"
      "alue\030\002 \001(\0132\033.tensorflow.tfprof.ExecTime:"
      "\0028\001\"3\n\010ExecTime\022\'\n\005times\030\001 \003(\0132\030.tensorf"
      "low.tfprof.Tuple\"\264\003\n\nExecMemory\022\025\n\rmemor"
      "y_micros\030\001 \001(\003\022\027\n\017host_temp_bytes\030\002 \001(\003\022"
      "\035\n\025host_persistent_bytes\030\003 \001(\003\022\036\n\026accele"
      "rator_temp_bytes\030\004 \001(\003\022$\n\034accelerator_pe"
      "rsistent_bytes\030\005 \001(\003\022\027\n\017requested_bytes\030"
      "\006 \001(\003\022\022\n\npeak_bytes\030\007 \001(\003\022\026\n\016residual_by"
      "tes\030\010 \001(\003\022\024\n\014output_bytes\030\t \001(\003\022\036\n\026alloc"
      "ator_bytes_in_use\030\n \001(\003\022F\n\routput_memory"
      "\030\013 \003(\0132/.tensorflow.tfprof.ExecMemory.Ou"
      "tputMemoryEntry\032N\n\021OutputMemoryEntry\022\013\n\003"
      "key\030\001 \001(\005\022(\n\005value\030\002 \001(\0132\031.tensorflow.tf"
      "prof.Memory:\0028\001\"\035\n\005Tuple\022\024\n\014int64_values"
      "\030\001 \003(\003\"$\n\006Memory\022\r\n\005bytes\030\001 \001(\003\022\013\n\003ptr\030\002"
      " \001(\004b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 3212);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/profiler/tfprof_log.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto
namespace tensorflow {
namespace tfprof {

// ===================================================================

void CodeDef_Trace::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CodeDef_Trace::kFileFieldNumber;
const int CodeDef_Trace::kFileIdFieldNumber;
const int CodeDef_Trace::kLinenoFieldNumber;
const int CodeDef_Trace::kFunctionFieldNumber;
const int CodeDef_Trace::kFunctionIdFieldNumber;
const int CodeDef_Trace::kLineFieldNumber;
const int CodeDef_Trace::kLineIdFieldNumber;
const int CodeDef_Trace::kFuncStartLineFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CodeDef_Trace::CodeDef_Trace()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_CodeDef_Trace.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tfprof.CodeDef.Trace)
}
CodeDef_Trace::CodeDef_Trace(const CodeDef_Trace& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  file_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.file().size() > 0) {
    file_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.file_);
  }
  function_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.function().size() > 0) {
    function_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.function_);
  }
  line_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.line().size() > 0) {
    line_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.line_);
  }
  ::memcpy(&lineno_, &from.lineno_,
    static_cast<size_t>(reinterpret_cast<char*>(&line_id_) -
    reinterpret_cast<char*>(&lineno_)) + sizeof(line_id_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.tfprof.CodeDef.Trace)
}

void CodeDef_Trace::SharedCtor() {
  file_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  function_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  line_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&lineno_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&line_id_) -
      reinterpret_cast<char*>(&lineno_)) + sizeof(line_id_));
}

CodeDef_Trace::~CodeDef_Trace() {
  // @@protoc_insertion_point(destructor:tensorflow.tfprof.CodeDef.Trace)
  SharedDtor();
}

void CodeDef_Trace::SharedDtor() {
  file_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  function_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  line_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void CodeDef_Trace::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* CodeDef_Trace::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CodeDef_Trace& CodeDef_Trace::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_CodeDef_Trace.base);
  return *internal_default_instance();
}


void CodeDef_Trace::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tfprof.CodeDef.Trace)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  file_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  function_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  line_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&lineno_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&line_id_) -
      reinterpret_cast<char*>(&lineno_)) + sizeof(line_id_));
  _internal_metadata_.Clear();
}

bool CodeDef_Trace::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tfprof.CodeDef.Trace)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string file = 1 [deprecated = true];
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_file()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->file().data(), static_cast<int>(this->file().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.CodeDef.Trace.file"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 lineno = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &lineno_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string function = 3 [deprecated = true];
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_function()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->function().data(), static_cast<int>(this->function().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.CodeDef.Trace.function"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string line = 4 [deprecated = true];
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_line()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->line().data(), static_cast<int>(this->line().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.CodeDef.Trace.line"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 func_start_line = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &func_start_line_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 file_id = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(48u /* 48 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &file_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 function_id = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(56u /* 56 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &function_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 line_id = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(64u /* 64 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &line_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tfprof.CodeDef.Trace)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tfprof.CodeDef.Trace)
  return false;
#undef DO_
}

void CodeDef_Trace::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tfprof.CodeDef.Trace)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string file = 1 [deprecated = true];
  if (this->file().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->file().data(), static_cast<int>(this->file().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.CodeDef.Trace.file");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->file(), output);
  }

  // int32 lineno = 2;
  if (this->lineno() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->lineno(), output);
  }

  // string function = 3 [deprecated = true];
  if (this->function().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->function().data(), static_cast<int>(this->function().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.CodeDef.Trace.function");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->function(), output);
  }

  // string line = 4 [deprecated = true];
  if (this->line().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->line().data(), static_cast<int>(this->line().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.CodeDef.Trace.line");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->line(), output);
  }

  // int32 func_start_line = 5;
  if (this->func_start_line() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(5, this->func_start_line(), output);
  }

  // int64 file_id = 6;
  if (this->file_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(6, this->file_id(), output);
  }

  // int64 function_id = 7;
  if (this->function_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(7, this->function_id(), output);
  }

  // int64 line_id = 8;
  if (this->line_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->line_id(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tfprof.CodeDef.Trace)
}

::google::protobuf::uint8* CodeDef_Trace::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tfprof.CodeDef.Trace)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string file = 1 [deprecated = true];
  if (this->file().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->file().data(), static_cast<int>(this->file().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.CodeDef.Trace.file");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->file(), target);
  }

  // int32 lineno = 2;
  if (this->lineno() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->lineno(), target);
  }

  // string function = 3 [deprecated = true];
  if (this->function().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->function().data(), static_cast<int>(this->function().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.CodeDef.Trace.function");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->function(), target);
  }

  // string line = 4 [deprecated = true];
  if (this->line().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->line().data(), static_cast<int>(this->line().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.CodeDef.Trace.line");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->line(), target);
  }

  // int32 func_start_line = 5;
  if (this->func_start_line() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(5, this->func_start_line(), target);
  }

  // int64 file_id = 6;
  if (this->file_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(6, this->file_id(), target);
  }

  // int64 function_id = 7;
  if (this->function_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(7, this->function_id(), target);
  }

  // int64 line_id = 8;
  if (this->line_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->line_id(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tfprof.CodeDef.Trace)
  return target;
}

size_t CodeDef_Trace::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tfprof.CodeDef.Trace)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string file = 1 [deprecated = true];
  if (this->file().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->file());
  }

  // string function = 3 [deprecated = true];
  if (this->function().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->function());
  }

  // string line = 4 [deprecated = true];
  if (this->line().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->line());
  }

  // int32 lineno = 2;
  if (this->lineno() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->lineno());
  }

  // int32 func_start_line = 5;
  if (this->func_start_line() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->func_start_line());
  }

  // int64 file_id = 6;
  if (this->file_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->file_id());
  }

  // int64 function_id = 7;
  if (this->function_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->function_id());
  }

  // int64 line_id = 8;
  if (this->line_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->line_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CodeDef_Trace::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tfprof.CodeDef.Trace)
  GOOGLE_DCHECK_NE(&from, this);
  const CodeDef_Trace* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CodeDef_Trace>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tfprof.CodeDef.Trace)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tfprof.CodeDef.Trace)
    MergeFrom(*source);
  }
}

void CodeDef_Trace::MergeFrom(const CodeDef_Trace& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tfprof.CodeDef.Trace)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.file().size() > 0) {

    file_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.file_);
  }
  if (from.function().size() > 0) {

    function_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.function_);
  }
  if (from.line().size() > 0) {

    line_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.line_);
  }
  if (from.lineno() != 0) {
    set_lineno(from.lineno());
  }
  if (from.func_start_line() != 0) {
    set_func_start_line(from.func_start_line());
  }
  if (from.file_id() != 0) {
    set_file_id(from.file_id());
  }
  if (from.function_id() != 0) {
    set_function_id(from.function_id());
  }
  if (from.line_id() != 0) {
    set_line_id(from.line_id());
  }
}

void CodeDef_Trace::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tfprof.CodeDef.Trace)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CodeDef_Trace::CopyFrom(const CodeDef_Trace& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tfprof.CodeDef.Trace)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CodeDef_Trace::IsInitialized() const {
  return true;
}

void CodeDef_Trace::Swap(CodeDef_Trace* other) {
  if (other == this) return;
  InternalSwap(other);
}
void CodeDef_Trace::InternalSwap(CodeDef_Trace* other) {
  using std::swap;
  file_.Swap(&other->file_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  function_.Swap(&other->function_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  line_.Swap(&other->line_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(lineno_, other->lineno_);
  swap(func_start_line_, other->func_start_line_);
  swap(file_id_, other->file_id_);
  swap(function_id_, other->function_id_);
  swap(line_id_, other->line_id_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata CodeDef_Trace::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void CodeDef::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CodeDef::kTracesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CodeDef::CodeDef()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_CodeDef.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tfprof.CodeDef)
}
CodeDef::CodeDef(const CodeDef& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      traces_(from.traces_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.tfprof.CodeDef)
}

void CodeDef::SharedCtor() {
}

CodeDef::~CodeDef() {
  // @@protoc_insertion_point(destructor:tensorflow.tfprof.CodeDef)
  SharedDtor();
}

void CodeDef::SharedDtor() {
}

void CodeDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* CodeDef::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CodeDef& CodeDef::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_CodeDef.base);
  return *internal_default_instance();
}


void CodeDef::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tfprof.CodeDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  traces_.Clear();
  _internal_metadata_.Clear();
}

bool CodeDef::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tfprof.CodeDef)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.tfprof.CodeDef.Trace traces = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_traces()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tfprof.CodeDef)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tfprof.CodeDef)
  return false;
#undef DO_
}

void CodeDef::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tfprof.CodeDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.tfprof.CodeDef.Trace traces = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->traces_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->traces(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tfprof.CodeDef)
}

::google::protobuf::uint8* CodeDef::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tfprof.CodeDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.tfprof.CodeDef.Trace traces = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->traces_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->traces(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tfprof.CodeDef)
  return target;
}

size_t CodeDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tfprof.CodeDef)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.tfprof.CodeDef.Trace traces = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->traces_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->traces(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CodeDef::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tfprof.CodeDef)
  GOOGLE_DCHECK_NE(&from, this);
  const CodeDef* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CodeDef>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tfprof.CodeDef)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tfprof.CodeDef)
    MergeFrom(*source);
  }
}

void CodeDef::MergeFrom(const CodeDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tfprof.CodeDef)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  traces_.MergeFrom(from.traces_);
}

void CodeDef::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tfprof.CodeDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CodeDef::CopyFrom(const CodeDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tfprof.CodeDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CodeDef::IsInitialized() const {
  return true;
}

void CodeDef::Swap(CodeDef* other) {
  if (other == this) return;
  InternalSwap(other);
}
void CodeDef::InternalSwap(CodeDef* other) {
  using std::swap;
  CastToBase(&traces_)->InternalSwap(CastToBase(&other->traces_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata CodeDef::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void OpLogEntry::InitAsDefaultInstance() {
  ::tensorflow::tfprof::_OpLogEntry_default_instance_._instance.get_mutable()->code_def_ = const_cast< ::tensorflow::tfprof::CodeDef*>(
      ::tensorflow::tfprof::CodeDef::internal_default_instance());
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int OpLogEntry::kNameFieldNumber;
const int OpLogEntry::kFloatOpsFieldNumber;
const int OpLogEntry::kTypesFieldNumber;
const int OpLogEntry::kCodeDefFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

OpLogEntry::OpLogEntry()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_OpLogEntry.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tfprof.OpLogEntry)
}
OpLogEntry::OpLogEntry(const OpLogEntry& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      types_(from.types_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  if (from.has_code_def()) {
    code_def_ = new ::tensorflow::tfprof::CodeDef(*from.code_def_);
  } else {
    code_def_ = NULL;
  }
  float_ops_ = from.float_ops_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.tfprof.OpLogEntry)
}

void OpLogEntry::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&code_def_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&float_ops_) -
      reinterpret_cast<char*>(&code_def_)) + sizeof(float_ops_));
}

OpLogEntry::~OpLogEntry() {
  // @@protoc_insertion_point(destructor:tensorflow.tfprof.OpLogEntry)
  SharedDtor();
}

void OpLogEntry::SharedDtor() {
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete code_def_;
}

void OpLogEntry::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* OpLogEntry::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const OpLogEntry& OpLogEntry::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_OpLogEntry.base);
  return *internal_default_instance();
}


void OpLogEntry::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tfprof.OpLogEntry)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  types_.Clear();
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && code_def_ != NULL) {
    delete code_def_;
  }
  code_def_ = NULL;
  float_ops_ = GOOGLE_LONGLONG(0);
  _internal_metadata_.Clear();
}

bool OpLogEntry::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tfprof.OpLogEntry)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.OpLogEntry.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 float_ops = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &float_ops_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string types = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_types()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->types(this->types_size() - 1).data(),
            static_cast<int>(this->types(this->types_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.OpLogEntry.types"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.tfprof.CodeDef code_def = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_code_def()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tfprof.OpLogEntry)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tfprof.OpLogEntry)
  return false;
#undef DO_
}

void OpLogEntry::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tfprof.OpLogEntry)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.OpLogEntry.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // int64 float_ops = 2;
  if (this->float_ops() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->float_ops(), output);
  }

  // repeated string types = 3;
  for (int i = 0, n = this->types_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->types(i).data(), static_cast<int>(this->types(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.OpLogEntry.types");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      3, this->types(i), output);
  }

  // .tensorflow.tfprof.CodeDef code_def = 4;
  if (this->has_code_def()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->_internal_code_def(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tfprof.OpLogEntry)
}

::google::protobuf::uint8* OpLogEntry::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tfprof.OpLogEntry)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.OpLogEntry.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // int64 float_ops = 2;
  if (this->float_ops() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->float_ops(), target);
  }

  // repeated string types = 3;
  for (int i = 0, n = this->types_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->types(i).data(), static_cast<int>(this->types(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.OpLogEntry.types");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(3, this->types(i), target);
  }

  // .tensorflow.tfprof.CodeDef code_def = 4;
  if (this->has_code_def()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->_internal_code_def(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tfprof.OpLogEntry)
  return target;
}

size_t OpLogEntry::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tfprof.OpLogEntry)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated string types = 3;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->types_size());
  for (int i = 0, n = this->types_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->types(i));
  }

  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // .tensorflow.tfprof.CodeDef code_def = 4;
  if (this->has_code_def()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *code_def_);
  }

  // int64 float_ops = 2;
  if (this->float_ops() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->float_ops());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void OpLogEntry::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tfprof.OpLogEntry)
  GOOGLE_DCHECK_NE(&from, this);
  const OpLogEntry* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const OpLogEntry>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tfprof.OpLogEntry)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tfprof.OpLogEntry)
    MergeFrom(*source);
  }
}

void OpLogEntry::MergeFrom(const OpLogEntry& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tfprof.OpLogEntry)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  types_.MergeFrom(from.types_);
  if (from.name().size() > 0) {

    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  if (from.has_code_def()) {
    mutable_code_def()->::tensorflow::tfprof::CodeDef::MergeFrom(from.code_def());
  }
  if (from.float_ops() != 0) {
    set_float_ops(from.float_ops());
  }
}

void OpLogEntry::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tfprof.OpLogEntry)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OpLogEntry::CopyFrom(const OpLogEntry& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tfprof.OpLogEntry)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OpLogEntry::IsInitialized() const {
  return true;
}

void OpLogEntry::Swap(OpLogEntry* other) {
  if (other == this) return;
  InternalSwap(other);
}
void OpLogEntry::InternalSwap(OpLogEntry* other) {
  using std::swap;
  types_.InternalSwap(CastToBase(&other->types_));
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(code_def_, other->code_def_);
  swap(float_ops_, other->float_ops_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata OpLogEntry::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

OpLogProto_IdToStringEntry_DoNotUse::OpLogProto_IdToStringEntry_DoNotUse() {}
OpLogProto_IdToStringEntry_DoNotUse::OpLogProto_IdToStringEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void OpLogProto_IdToStringEntry_DoNotUse::MergeFrom(const OpLogProto_IdToStringEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata OpLogProto_IdToStringEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[3];
}
void OpLogProto_IdToStringEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void OpLogProto::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int OpLogProto::kLogEntriesFieldNumber;
const int OpLogProto::kIdToStringFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

OpLogProto::OpLogProto()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_OpLogProto.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tfprof.OpLogProto)
}
OpLogProto::OpLogProto(const OpLogProto& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      log_entries_(from.log_entries_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  id_to_string_.MergeFrom(from.id_to_string_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.tfprof.OpLogProto)
}

void OpLogProto::SharedCtor() {
}

OpLogProto::~OpLogProto() {
  // @@protoc_insertion_point(destructor:tensorflow.tfprof.OpLogProto)
  SharedDtor();
}

void OpLogProto::SharedDtor() {
}

void OpLogProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* OpLogProto::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const OpLogProto& OpLogProto::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_OpLogProto.base);
  return *internal_default_instance();
}


void OpLogProto::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tfprof.OpLogProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  log_entries_.Clear();
  id_to_string_.Clear();
  _internal_metadata_.Clear();
}

bool OpLogProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tfprof.OpLogProto)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.tfprof.OpLogEntry log_entries = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_log_entries()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<int64, string> id_to_string = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          OpLogProto_IdToStringEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              OpLogProto_IdToStringEntry_DoNotUse,
              ::google::protobuf::int64, ::std::string,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int64, ::std::string > > parser(&id_to_string_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.value().data(), static_cast<int>(parser.value().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.OpLogProto.IdToStringEntry.value"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tfprof.OpLogProto)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tfprof.OpLogProto)
  return false;
#undef DO_
}

void OpLogProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tfprof.OpLogProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.tfprof.OpLogEntry log_entries = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->log_entries_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->log_entries(static_cast<int>(i)),
      output);
  }

  // map<int64, string> id_to_string = 2;
  if (!this->id_to_string().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::std::string >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.tfprof.OpLogProto.IdToStringEntry.value");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->id_to_string().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->id_to_string().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::std::string >::const_iterator
          it = this->id_to_string().begin();
          it != this->id_to_string().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<OpLogProto_IdToStringEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(id_to_string_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)].second);
      }
    } else {
      ::std::unique_ptr<OpLogProto_IdToStringEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::std::string >::const_iterator
          it = this->id_to_string().begin();
          it != this->id_to_string().end(); ++it) {
        entry.reset(id_to_string_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tfprof.OpLogProto)
}

::google::protobuf::uint8* OpLogProto::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tfprof.OpLogProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.tfprof.OpLogEntry log_entries = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->log_entries_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->log_entries(static_cast<int>(i)), deterministic, target);
  }

  // map<int64, string> id_to_string = 2;
  if (!this->id_to_string().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::std::string >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.tfprof.OpLogProto.IdToStringEntry.value");
      }
    };

    if (deterministic &&
        this->id_to_string().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->id_to_string().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::std::string >::const_iterator
          it = this->id_to_string().begin();
          it != this->id_to_string().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<OpLogProto_IdToStringEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(id_to_string_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)].second);
      }
    } else {
      ::std::unique_ptr<OpLogProto_IdToStringEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::std::string >::const_iterator
          it = this->id_to_string().begin();
          it != this->id_to_string().end(); ++it) {
        entry.reset(id_to_string_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tfprof.OpLogProto)
  return target;
}

size_t OpLogProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tfprof.OpLogProto)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.tfprof.OpLogEntry log_entries = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->log_entries_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->log_entries(static_cast<int>(i)));
    }
  }

  // map<int64, string> id_to_string = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->id_to_string_size());
  {
    ::std::unique_ptr<OpLogProto_IdToStringEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::google::protobuf::int64, ::std::string >::const_iterator
        it = this->id_to_string().begin();
        it != this->id_to_string().end(); ++it) {
      entry.reset(id_to_string_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void OpLogProto::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tfprof.OpLogProto)
  GOOGLE_DCHECK_NE(&from, this);
  const OpLogProto* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const OpLogProto>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tfprof.OpLogProto)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tfprof.OpLogProto)
    MergeFrom(*source);
  }
}

void OpLogProto::MergeFrom(const OpLogProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tfprof.OpLogProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  log_entries_.MergeFrom(from.log_entries_);
  id_to_string_.MergeFrom(from.id_to_string_);
}

void OpLogProto::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tfprof.OpLogProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OpLogProto::CopyFrom(const OpLogProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tfprof.OpLogProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OpLogProto::IsInitialized() const {
  return true;
}

void OpLogProto::Swap(OpLogProto* other) {
  if (other == this) return;
  InternalSwap(other);
}
void OpLogProto::InternalSwap(OpLogProto* other) {
  using std::swap;
  CastToBase(&log_entries_)->InternalSwap(CastToBase(&other->log_entries_));
  id_to_string_.Swap(&other->id_to_string_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata OpLogProto::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

ProfileProto_NodesEntry_DoNotUse::ProfileProto_NodesEntry_DoNotUse() {}
ProfileProto_NodesEntry_DoNotUse::ProfileProto_NodesEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void ProfileProto_NodesEntry_DoNotUse::MergeFrom(const ProfileProto_NodesEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata ProfileProto_NodesEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[5];
}
void ProfileProto_NodesEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

ProfileProto_IdToStringEntry_DoNotUse::ProfileProto_IdToStringEntry_DoNotUse() {}
ProfileProto_IdToStringEntry_DoNotUse::ProfileProto_IdToStringEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void ProfileProto_IdToStringEntry_DoNotUse::MergeFrom(const ProfileProto_IdToStringEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata ProfileProto_IdToStringEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[6];
}
void ProfileProto_IdToStringEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void ProfileProto::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ProfileProto::kNodesFieldNumber;
const int ProfileProto::kHasTraceFieldNumber;
const int ProfileProto::kMissAcceleratorStreamFieldNumber;
const int ProfileProto::kStepsFieldNumber;
const int ProfileProto::kIdToStringFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ProfileProto::ProfileProto()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_ProfileProto.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tfprof.ProfileProto)
}
ProfileProto::ProfileProto(const ProfileProto& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      steps_(from.steps_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  nodes_.MergeFrom(from.nodes_);
  id_to_string_.MergeFrom(from.id_to_string_);
  ::memcpy(&has_trace_, &from.has_trace_,
    static_cast<size_t>(reinterpret_cast<char*>(&miss_accelerator_stream_) -
    reinterpret_cast<char*>(&has_trace_)) + sizeof(miss_accelerator_stream_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.tfprof.ProfileProto)
}

void ProfileProto::SharedCtor() {
  ::memset(&has_trace_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&miss_accelerator_stream_) -
      reinterpret_cast<char*>(&has_trace_)) + sizeof(miss_accelerator_stream_));
}

ProfileProto::~ProfileProto() {
  // @@protoc_insertion_point(destructor:tensorflow.tfprof.ProfileProto)
  SharedDtor();
}

void ProfileProto::SharedDtor() {
}

void ProfileProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ProfileProto::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ProfileProto& ProfileProto::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_ProfileProto.base);
  return *internal_default_instance();
}


void ProfileProto::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tfprof.ProfileProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  nodes_.Clear();
  steps_.Clear();
  id_to_string_.Clear();
  ::memset(&has_trace_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&miss_accelerator_stream_) -
      reinterpret_cast<char*>(&has_trace_)) + sizeof(miss_accelerator_stream_));
  _internal_metadata_.Clear();
}

bool ProfileProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tfprof.ProfileProto)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<int64, .tensorflow.tfprof.ProfileNode> nodes = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          ProfileProto_NodesEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              ProfileProto_NodesEntry_DoNotUse,
              ::google::protobuf::int64, ::tensorflow::tfprof::ProfileNode,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int64, ::tensorflow::tfprof::ProfileNode > > parser(&nodes_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool has_trace = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &has_trace_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated int64 steps = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_steps())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 1, 26u, input, this->mutable_steps())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<int64, string> id_to_string = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          ProfileProto_IdToStringEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              ProfileProto_IdToStringEntry_DoNotUse,
              ::google::protobuf::int64, ::std::string,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int64, ::std::string > > parser(&id_to_string_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.value().data(), static_cast<int>(parser.value().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.ProfileProto.IdToStringEntry.value"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool miss_accelerator_stream = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &miss_accelerator_stream_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tfprof.ProfileProto)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tfprof.ProfileProto)
  return false;
#undef DO_
}

void ProfileProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tfprof.ProfileProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // map<int64, .tensorflow.tfprof.ProfileNode> nodes = 1;
  if (!this->nodes().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::tensorflow::tfprof::ProfileNode >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterministic() &&
        this->nodes().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->nodes().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::tensorflow::tfprof::ProfileNode >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::tensorflow::tfprof::ProfileNode >::const_iterator
          it = this->nodes().begin();
          it != this->nodes().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ProfileProto_NodesEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(nodes_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
      }
    } else {
      ::std::unique_ptr<ProfileProto_NodesEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::tensorflow::tfprof::ProfileNode >::const_iterator
          it = this->nodes().begin();
          it != this->nodes().end(); ++it) {
        entry.reset(nodes_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
      }
    }
  }

  // bool has_trace = 2;
  if (this->has_trace() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(2, this->has_trace(), output);
  }

  // repeated int64 steps = 3;
  if (this->steps_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(3, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _steps_cached_byte_size_));
  }
  for (int i = 0, n = this->steps_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->steps(i), output);
  }

  // map<int64, string> id_to_string = 4;
  if (!this->id_to_string().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::std::string >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.tfprof.ProfileProto.IdToStringEntry.value");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->id_to_string().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->id_to_string().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::std::string >::const_iterator
          it = this->id_to_string().begin();
          it != this->id_to_string().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ProfileProto_IdToStringEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(id_to_string_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            4, *entry, output);
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)].second);
      }
    } else {
      ::std::unique_ptr<ProfileProto_IdToStringEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::std::string >::const_iterator
          it = this->id_to_string().begin();
          it != this->id_to_string().end(); ++it) {
        entry.reset(id_to_string_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            4, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  // bool miss_accelerator_stream = 5;
  if (this->miss_accelerator_stream() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(5, this->miss_accelerator_stream(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tfprof.ProfileProto)
}

::google::protobuf::uint8* ProfileProto::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tfprof.ProfileProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // map<int64, .tensorflow.tfprof.ProfileNode> nodes = 1;
  if (!this->nodes().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::tensorflow::tfprof::ProfileNode >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->nodes().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->nodes().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::tensorflow::tfprof::ProfileNode >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::tensorflow::tfprof::ProfileNode >::const_iterator
          it = this->nodes().begin();
          it != this->nodes().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ProfileProto_NodesEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(nodes_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
      }
    } else {
      ::std::unique_ptr<ProfileProto_NodesEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::tensorflow::tfprof::ProfileNode >::const_iterator
          it = this->nodes().begin();
          it != this->nodes().end(); ++it) {
        entry.reset(nodes_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
      }
    }
  }

  // bool has_trace = 2;
  if (this->has_trace() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(2, this->has_trace(), target);
  }

  // repeated int64 steps = 3;
  if (this->steps_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      3,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _steps_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->steps_, target);
  }

  // map<int64, string> id_to_string = 4;
  if (!this->id_to_string().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::std::string >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.tfprof.ProfileProto.IdToStringEntry.value");
      }
    };

    if (deterministic &&
        this->id_to_string().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->id_to_string().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::std::string >::const_iterator
          it = this->id_to_string().begin();
          it != this->id_to_string().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ProfileProto_IdToStringEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(id_to_string_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       4, *entry, deterministic, target);
;
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)].second);
      }
    } else {
      ::std::unique_ptr<ProfileProto_IdToStringEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::std::string >::const_iterator
          it = this->id_to_string().begin();
          it != this->id_to_string().end(); ++it) {
        entry.reset(id_to_string_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       4, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  // bool miss_accelerator_stream = 5;
  if (this->miss_accelerator_stream() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(5, this->miss_accelerator_stream(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tfprof.ProfileProto)
  return target;
}

size_t ProfileProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tfprof.ProfileProto)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // map<int64, .tensorflow.tfprof.ProfileNode> nodes = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->nodes_size());
  {
    ::std::unique_ptr<ProfileProto_NodesEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::google::protobuf::int64, ::tensorflow::tfprof::ProfileNode >::const_iterator
        it = this->nodes().begin();
        it != this->nodes().end(); ++it) {
      entry.reset(nodes_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // repeated int64 steps = 3;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int64Size(this->steps_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _steps_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // map<int64, string> id_to_string = 4;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->id_to_string_size());
  {
    ::std::unique_ptr<ProfileProto_IdToStringEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::google::protobuf::int64, ::std::string >::const_iterator
        it = this->id_to_string().begin();
        it != this->id_to_string().end(); ++it) {
      entry.reset(id_to_string_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // bool has_trace = 2;
  if (this->has_trace() != 0) {
    total_size += 1 + 1;
  }

  // bool miss_accelerator_stream = 5;
  if (this->miss_accelerator_stream() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ProfileProto::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tfprof.ProfileProto)
  GOOGLE_DCHECK_NE(&from, this);
  const ProfileProto* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ProfileProto>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tfprof.ProfileProto)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tfprof.ProfileProto)
    MergeFrom(*source);
  }
}

void ProfileProto::MergeFrom(const ProfileProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tfprof.ProfileProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  nodes_.MergeFrom(from.nodes_);
  steps_.MergeFrom(from.steps_);
  id_to_string_.MergeFrom(from.id_to_string_);
  if (from.has_trace() != 0) {
    set_has_trace(from.has_trace());
  }
  if (from.miss_accelerator_stream() != 0) {
    set_miss_accelerator_stream(from.miss_accelerator_stream());
  }
}

void ProfileProto::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tfprof.ProfileProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ProfileProto::CopyFrom(const ProfileProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tfprof.ProfileProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ProfileProto::IsInitialized() const {
  return true;
}

void ProfileProto::Swap(ProfileProto* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ProfileProto::InternalSwap(ProfileProto* other) {
  using std::swap;
  nodes_.Swap(&other->nodes_);
  steps_.InternalSwap(&other->steps_);
  id_to_string_.Swap(&other->id_to_string_);
  swap(has_trace_, other->has_trace_);
  swap(miss_accelerator_stream_, other->miss_accelerator_stream_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ProfileProto::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

ProfileNode_InputsEntry_DoNotUse::ProfileNode_InputsEntry_DoNotUse() {}
ProfileNode_InputsEntry_DoNotUse::ProfileNode_InputsEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void ProfileNode_InputsEntry_DoNotUse::MergeFrom(const ProfileNode_InputsEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata ProfileNode_InputsEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[8];
}
void ProfileNode_InputsEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

ProfileNode_InputShapesEntry_DoNotUse::ProfileNode_InputShapesEntry_DoNotUse() {}
ProfileNode_InputShapesEntry_DoNotUse::ProfileNode_InputShapesEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void ProfileNode_InputShapesEntry_DoNotUse::MergeFrom(const ProfileNode_InputShapesEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata ProfileNode_InputShapesEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[9];
}
void ProfileNode_InputShapesEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

ProfileNode_OutputsEntry_DoNotUse::ProfileNode_OutputsEntry_DoNotUse() {}
ProfileNode_OutputsEntry_DoNotUse::ProfileNode_OutputsEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void ProfileNode_OutputsEntry_DoNotUse::MergeFrom(const ProfileNode_OutputsEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata ProfileNode_OutputsEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[10];
}
void ProfileNode_OutputsEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

ProfileNode_OutputShapesEntry_DoNotUse::ProfileNode_OutputShapesEntry_DoNotUse() {}
ProfileNode_OutputShapesEntry_DoNotUse::ProfileNode_OutputShapesEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void ProfileNode_OutputShapesEntry_DoNotUse::MergeFrom(const ProfileNode_OutputShapesEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata ProfileNode_OutputShapesEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[11];
}
void ProfileNode_OutputShapesEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

ProfileNode_SrcOutputIndexEntry_DoNotUse::ProfileNode_SrcOutputIndexEntry_DoNotUse() {}
ProfileNode_SrcOutputIndexEntry_DoNotUse::ProfileNode_SrcOutputIndexEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void ProfileNode_SrcOutputIndexEntry_DoNotUse::MergeFrom(const ProfileNode_SrcOutputIndexEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata ProfileNode_SrcOutputIndexEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[12];
}
void ProfileNode_SrcOutputIndexEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

ProfileNode_AttrsEntry_DoNotUse::ProfileNode_AttrsEntry_DoNotUse() {}
ProfileNode_AttrsEntry_DoNotUse::ProfileNode_AttrsEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void ProfileNode_AttrsEntry_DoNotUse::MergeFrom(const ProfileNode_AttrsEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata ProfileNode_AttrsEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[13];
}
void ProfileNode_AttrsEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

ProfileNode_ExecsEntry_DoNotUse::ProfileNode_ExecsEntry_DoNotUse() {}
ProfileNode_ExecsEntry_DoNotUse::ProfileNode_ExecsEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void ProfileNode_ExecsEntry_DoNotUse::MergeFrom(const ProfileNode_ExecsEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata ProfileNode_ExecsEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[14];
}
void ProfileNode_ExecsEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void ProfileNode::InitAsDefaultInstance() {
  ::tensorflow::tfprof::_ProfileNode_default_instance_._instance.get_mutable()->trace_ = const_cast< ::tensorflow::tfprof::CodeDef*>(
      ::tensorflow::tfprof::CodeDef::internal_default_instance());
}
void ProfileNode::clear_attrs() {
  attrs_.Clear();
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ProfileNode::kNameFieldNumber;
const int ProfileNode::kOpFieldNumber;
const int ProfileNode::kIdFieldNumber;
const int ProfileNode::kInputsFieldNumber;
const int ProfileNode::kInputShapesFieldNumber;
const int ProfileNode::kOutputsFieldNumber;
const int ProfileNode::kOutputShapesFieldNumber;
const int ProfileNode::kSrcOutputIndexFieldNumber;
const int ProfileNode::kShapeFieldNumber;
const int ProfileNode::kOpTypesFieldNumber;
const int ProfileNode::kCanonicalDeviceFieldNumber;
const int ProfileNode::kHostDeviceFieldNumber;
const int ProfileNode::kFloatOpsFieldNumber;
const int ProfileNode::kTraceFieldNumber;
const int ProfileNode::kAttrsFieldNumber;
const int ProfileNode::kExecsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ProfileNode::ProfileNode()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_ProfileNode.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tfprof.ProfileNode)
}
ProfileNode::ProfileNode(const ProfileNode& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      shape_(from.shape_),
      op_types_(from.op_types_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  inputs_.MergeFrom(from.inputs_);
  outputs_.MergeFrom(from.outputs_);
  attrs_.MergeFrom(from.attrs_);
  execs_.MergeFrom(from.execs_);
  src_output_index_.MergeFrom(from.src_output_index_);
  output_shapes_.MergeFrom(from.output_shapes_);
  input_shapes_.MergeFrom(from.input_shapes_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  canonical_device_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.canonical_device().size() > 0) {
    canonical_device_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.canonical_device_);
  }
  host_device_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.host_device().size() > 0) {
    host_device_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.host_device_);
  }
  op_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.op().size() > 0) {
    op_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.op_);
  }
  if (from.has_trace()) {
    trace_ = new ::tensorflow::tfprof::CodeDef(*from.trace_);
  } else {
    trace_ = NULL;
  }
  ::memcpy(&float_ops_, &from.float_ops_,
    static_cast<size_t>(reinterpret_cast<char*>(&id_) -
    reinterpret_cast<char*>(&float_ops_)) + sizeof(id_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.tfprof.ProfileNode)
}

void ProfileNode::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  canonical_device_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  host_device_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  op_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&trace_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&id_) -
      reinterpret_cast<char*>(&trace_)) + sizeof(id_));
}

ProfileNode::~ProfileNode() {
  // @@protoc_insertion_point(destructor:tensorflow.tfprof.ProfileNode)
  SharedDtor();
}

void ProfileNode::SharedDtor() {
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  canonical_device_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  host_device_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  op_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete trace_;
}

void ProfileNode::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ProfileNode::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ProfileNode& ProfileNode::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_ProfileNode.base);
  return *internal_default_instance();
}


void ProfileNode::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tfprof.ProfileNode)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  inputs_.Clear();
  outputs_.Clear();
  shape_.Clear();
  op_types_.Clear();
  attrs_.Clear();
  execs_.Clear();
  src_output_index_.Clear();
  output_shapes_.Clear();
  input_shapes_.Clear();
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  canonical_device_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  host_device_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  op_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && trace_ != NULL) {
    delete trace_;
  }
  trace_ = NULL;
  ::memset(&float_ops_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&id_) -
      reinterpret_cast<char*>(&float_ops_)) + sizeof(id_));
  _internal_metadata_.Clear();
}

bool ProfileNode::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tfprof.ProfileNode)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(16383u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.ProfileNode.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<int32, int64> inputs = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          ProfileNode_InputsEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              ProfileNode_InputsEntry_DoNotUse,
              ::google::protobuf::int32, ::google::protobuf::int64,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int64 > > parser(&inputs_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<int32, int64> outputs = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          ProfileNode_OutputsEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              ProfileNode_OutputsEntry_DoNotUse,
              ::google::protobuf::int32, ::google::protobuf::int64,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int64 > > parser(&outputs_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated int64 shape = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_shape())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 1, 34u, input, this->mutable_shape())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string op_types = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_op_types()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->op_types(this->op_types_size() - 1).data(),
            static_cast<int>(this->op_types(this->op_types_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.ProfileNode.op_types"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string canonical_device = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_canonical_device()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->canonical_device().data(), static_cast<int>(this->canonical_device().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.ProfileNode.canonical_device"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string host_device = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_host_device()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->host_device().data(), static_cast<int>(this->host_device().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.ProfileNode.host_device"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 float_ops = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(64u /* 64 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &float_ops_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string op = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(74u /* 74 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_op()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->op().data(), static_cast<int>(this->op().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.ProfileNode.op"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.tfprof.CodeDef trace = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(82u /* 82 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_trace()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, .tensorflow.AttrValue> attrs = 11;
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(90u /* 90 & 0xFF */)) {
          ProfileNode_AttrsEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              ProfileNode_AttrsEntry_DoNotUse,
              ::std::string, ::tensorflow::AttrValue,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue > > parser(&attrs_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.ProfileNode.AttrsEntry.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<int64, .tensorflow.tfprof.ExecProfile> execs = 12;
      case 12: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(98u /* 98 & 0xFF */)) {
          ProfileNode_ExecsEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              ProfileNode_ExecsEntry_DoNotUse,
              ::google::protobuf::int64, ::tensorflow::tfprof::ExecProfile,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int64, ::tensorflow::tfprof::ExecProfile > > parser(&execs_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 id = 13;
      case 13: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(104u /* 104 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<int64, int32> src_output_index = 14;
      case 14: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(114u /* 114 & 0xFF */)) {
          ProfileNode_SrcOutputIndexEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              ProfileNode_SrcOutputIndexEntry_DoNotUse,
              ::google::protobuf::int64, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT64,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 > > parser(&src_output_index_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<int32, .tensorflow.tfprof.Tuple> output_shapes = 15;
      case 15: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(122u /* 122 & 0xFF */)) {
          ProfileNode_OutputShapesEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              ProfileNode_OutputShapesEntry_DoNotUse,
              ::google::protobuf::int32, ::tensorflow::tfprof::Tuple,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Tuple > > parser(&output_shapes_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<int32, .tensorflow.tfprof.Tuple> input_shapes = 16;
      case 16: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(130u /* 130 & 0xFF */)) {
          ProfileNode_InputShapesEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              ProfileNode_InputShapesEntry_DoNotUse,
              ::google::protobuf::int32, ::tensorflow::tfprof::Tuple,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Tuple > > parser(&input_shapes_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tfprof.ProfileNode)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tfprof.ProfileNode)
  return false;
#undef DO_
}

void ProfileNode::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tfprof.ProfileNode)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.ProfileNode.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // map<int32, int64> inputs = 2;
  if (!this->inputs().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterministic() &&
        this->inputs().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->inputs().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int64 >::const_iterator
          it = this->inputs().begin();
          it != this->inputs().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ProfileNode_InputsEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(inputs_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
      }
    } else {
      ::std::unique_ptr<ProfileNode_InputsEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int64 >::const_iterator
          it = this->inputs().begin();
          it != this->inputs().end(); ++it) {
        entry.reset(inputs_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
      }
    }
  }

  // map<int32, int64> outputs = 3;
  if (!this->outputs().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterministic() &&
        this->outputs().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->outputs().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int64 >::const_iterator
          it = this->outputs().begin();
          it != this->outputs().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ProfileNode_OutputsEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(outputs_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            3, *entry, output);
      }
    } else {
      ::std::unique_ptr<ProfileNode_OutputsEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int64 >::const_iterator
          it = this->outputs().begin();
          it != this->outputs().end(); ++it) {
        entry.reset(outputs_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            3, *entry, output);
      }
    }
  }

  // repeated int64 shape = 4;
  if (this->shape_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(4, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _shape_cached_byte_size_));
  }
  for (int i = 0, n = this->shape_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->shape(i), output);
  }

  // repeated string op_types = 5;
  for (int i = 0, n = this->op_types_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->op_types(i).data(), static_cast<int>(this->op_types(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.ProfileNode.op_types");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      5, this->op_types(i), output);
  }

  // string canonical_device = 6;
  if (this->canonical_device().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->canonical_device().data(), static_cast<int>(this->canonical_device().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.ProfileNode.canonical_device");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      6, this->canonical_device(), output);
  }

  // string host_device = 7;
  if (this->host_device().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->host_device().data(), static_cast<int>(this->host_device().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.ProfileNode.host_device");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->host_device(), output);
  }

  // int64 float_ops = 8;
  if (this->float_ops() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->float_ops(), output);
  }

  // string op = 9;
  if (this->op().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->op().data(), static_cast<int>(this->op().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.ProfileNode.op");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      9, this->op(), output);
  }

  // .tensorflow.tfprof.CodeDef trace = 10;
  if (this->has_trace()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      10, this->_internal_trace(), output);
  }

  // map<string, .tensorflow.AttrValue> attrs = 11;
  if (!this->attrs().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.tfprof.ProfileNode.AttrsEntry.key");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->attrs().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->attrs().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
          it = this->attrs().begin();
          it != this->attrs().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ProfileNode_AttrsEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(attrs_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            11, *entry, output);
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<ProfileNode_AttrsEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
          it = this->attrs().begin();
          it != this->attrs().end(); ++it) {
        entry.reset(attrs_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            11, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<int64, .tensorflow.tfprof.ExecProfile> execs = 12;
  if (!this->execs().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::tensorflow::tfprof::ExecProfile >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterministic() &&
        this->execs().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->execs().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::tensorflow::tfprof::ExecProfile >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::tensorflow::tfprof::ExecProfile >::const_iterator
          it = this->execs().begin();
          it != this->execs().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ProfileNode_ExecsEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(execs_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            12, *entry, output);
      }
    } else {
      ::std::unique_ptr<ProfileNode_ExecsEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::tensorflow::tfprof::ExecProfile >::const_iterator
          it = this->execs().begin();
          it != this->execs().end(); ++it) {
        entry.reset(execs_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            12, *entry, output);
      }
    }
  }

  // int64 id = 13;
  if (this->id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->id(), output);
  }

  // map<int64, int32> src_output_index = 14;
  if (!this->src_output_index().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterministic() &&
        this->src_output_index().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->src_output_index().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >::const_iterator
          it = this->src_output_index().begin();
          it != this->src_output_index().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ProfileNode_SrcOutputIndexEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(src_output_index_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            14, *entry, output);
      }
    } else {
      ::std::unique_ptr<ProfileNode_SrcOutputIndexEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >::const_iterator
          it = this->src_output_index().begin();
          it != this->src_output_index().end(); ++it) {
        entry.reset(src_output_index_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            14, *entry, output);
      }
    }
  }

  // map<int32, .tensorflow.tfprof.Tuple> output_shapes = 15;
  if (!this->output_shapes().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Tuple >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterministic() &&
        this->output_shapes().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->output_shapes().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Tuple >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Tuple >::const_iterator
          it = this->output_shapes().begin();
          it != this->output_shapes().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ProfileNode_OutputShapesEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(output_shapes_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            15, *entry, output);
      }
    } else {
      ::std::unique_ptr<ProfileNode_OutputShapesEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Tuple >::const_iterator
          it = this->output_shapes().begin();
          it != this->output_shapes().end(); ++it) {
        entry.reset(output_shapes_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            15, *entry, output);
      }
    }
  }

  // map<int32, .tensorflow.tfprof.Tuple> input_shapes = 16;
  if (!this->input_shapes().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Tuple >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterministic() &&
        this->input_shapes().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->input_shapes().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Tuple >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Tuple >::const_iterator
          it = this->input_shapes().begin();
          it != this->input_shapes().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ProfileNode_InputShapesEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(input_shapes_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            16, *entry, output);
      }
    } else {
      ::std::unique_ptr<ProfileNode_InputShapesEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Tuple >::const_iterator
          it = this->input_shapes().begin();
          it != this->input_shapes().end(); ++it) {
        entry.reset(input_shapes_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            16, *entry, output);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tfprof.ProfileNode)
}

::google::protobuf::uint8* ProfileNode::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tfprof.ProfileNode)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.ProfileNode.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // map<int32, int64> inputs = 2;
  if (!this->inputs().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->inputs().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->inputs().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int64 >::const_iterator
          it = this->inputs().begin();
          it != this->inputs().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ProfileNode_InputsEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(inputs_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
      }
    } else {
      ::std::unique_ptr<ProfileNode_InputsEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int64 >::const_iterator
          it = this->inputs().begin();
          it != this->inputs().end(); ++it) {
        entry.reset(inputs_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
      }
    }
  }

  // map<int32, int64> outputs = 3;
  if (!this->outputs().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int64 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->outputs().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->outputs().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int64 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int64 >::const_iterator
          it = this->outputs().begin();
          it != this->outputs().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ProfileNode_OutputsEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(outputs_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       3, *entry, deterministic, target);
;
      }
    } else {
      ::std::unique_ptr<ProfileNode_OutputsEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int64 >::const_iterator
          it = this->outputs().begin();
          it != this->outputs().end(); ++it) {
        entry.reset(outputs_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       3, *entry, deterministic, target);
;
      }
    }
  }

  // repeated int64 shape = 4;
  if (this->shape_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      4,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _shape_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->shape_, target);
  }

  // repeated string op_types = 5;
  for (int i = 0, n = this->op_types_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->op_types(i).data(), static_cast<int>(this->op_types(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.ProfileNode.op_types");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(5, this->op_types(i), target);
  }

  // string canonical_device = 6;
  if (this->canonical_device().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->canonical_device().data(), static_cast<int>(this->canonical_device().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.ProfileNode.canonical_device");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        6, this->canonical_device(), target);
  }

  // string host_device = 7;
  if (this->host_device().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->host_device().data(), static_cast<int>(this->host_device().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.ProfileNode.host_device");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->host_device(), target);
  }

  // int64 float_ops = 8;
  if (this->float_ops() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->float_ops(), target);
  }

  // string op = 9;
  if (this->op().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->op().data(), static_cast<int>(this->op().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.ProfileNode.op");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        9, this->op(), target);
  }

  // .tensorflow.tfprof.CodeDef trace = 10;
  if (this->has_trace()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        10, this->_internal_trace(), deterministic, target);
  }

  // map<string, .tensorflow.AttrValue> attrs = 11;
  if (!this->attrs().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.tfprof.ProfileNode.AttrsEntry.key");
      }
    };

    if (deterministic &&
        this->attrs().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->attrs().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
          it = this->attrs().begin();
          it != this->attrs().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ProfileNode_AttrsEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(attrs_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       11, *entry, deterministic, target);
;
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<ProfileNode_AttrsEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
          it = this->attrs().begin();
          it != this->attrs().end(); ++it) {
        entry.reset(attrs_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       11, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<int64, .tensorflow.tfprof.ExecProfile> execs = 12;
  if (!this->execs().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::tensorflow::tfprof::ExecProfile >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->execs().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->execs().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::tensorflow::tfprof::ExecProfile >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::tensorflow::tfprof::ExecProfile >::const_iterator
          it = this->execs().begin();
          it != this->execs().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ProfileNode_ExecsEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(execs_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       12, *entry, deterministic, target);
;
      }
    } else {
      ::std::unique_ptr<ProfileNode_ExecsEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::tensorflow::tfprof::ExecProfile >::const_iterator
          it = this->execs().begin();
          it != this->execs().end(); ++it) {
        entry.reset(execs_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       12, *entry, deterministic, target);
;
      }
    }
  }

  // int64 id = 13;
  if (this->id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->id(), target);
  }

  // map<int64, int32> src_output_index = 14;
  if (!this->src_output_index().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int64, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->src_output_index().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->src_output_index().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >::const_iterator
          it = this->src_output_index().begin();
          it != this->src_output_index().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ProfileNode_SrcOutputIndexEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(src_output_index_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       14, *entry, deterministic, target);
;
      }
    } else {
      ::std::unique_ptr<ProfileNode_SrcOutputIndexEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >::const_iterator
          it = this->src_output_index().begin();
          it != this->src_output_index().end(); ++it) {
        entry.reset(src_output_index_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       14, *entry, deterministic, target);
;
      }
    }
  }

  // map<int32, .tensorflow.tfprof.Tuple> output_shapes = 15;
  if (!this->output_shapes().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Tuple >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->output_shapes().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->output_shapes().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Tuple >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Tuple >::const_iterator
          it = this->output_shapes().begin();
          it != this->output_shapes().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ProfileNode_OutputShapesEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(output_shapes_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       15, *entry, deterministic, target);
;
      }
    } else {
      ::std::unique_ptr<ProfileNode_OutputShapesEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Tuple >::const_iterator
          it = this->output_shapes().begin();
          it != this->output_shapes().end(); ++it) {
        entry.reset(output_shapes_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       15, *entry, deterministic, target);
;
      }
    }
  }

  // map<int32, .tensorflow.tfprof.Tuple> input_shapes = 16;
  if (!this->input_shapes().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Tuple >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->input_shapes().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->input_shapes().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Tuple >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Tuple >::const_iterator
          it = this->input_shapes().begin();
          it != this->input_shapes().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ProfileNode_InputShapesEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(input_shapes_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       16, *entry, deterministic, target);
;
      }
    } else {
      ::std::unique_ptr<ProfileNode_InputShapesEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Tuple >::const_iterator
          it = this->input_shapes().begin();
          it != this->input_shapes().end(); ++it) {
        entry.reset(input_shapes_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       16, *entry, deterministic, target);
;
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tfprof.ProfileNode)
  return target;
}

size_t ProfileNode::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tfprof.ProfileNode)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // map<int32, int64> inputs = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->inputs_size());
  {
    ::std::unique_ptr<ProfileNode_InputsEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int64 >::const_iterator
        it = this->inputs().begin();
        it != this->inputs().end(); ++it) {
      entry.reset(inputs_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int32, int64> outputs = 3;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->outputs_size());
  {
    ::std::unique_ptr<ProfileNode_OutputsEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::google::protobuf::int64 >::const_iterator
        it = this->outputs().begin();
        it != this->outputs().end(); ++it) {
      entry.reset(outputs_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // repeated int64 shape = 4;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int64Size(this->shape_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _shape_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated string op_types = 5;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->op_types_size());
  for (int i = 0, n = this->op_types_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->op_types(i));
  }

  // map<string, .tensorflow.AttrValue> attrs = 11;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->attrs_size());
  {
    ::std::unique_ptr<ProfileNode_AttrsEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
        it = this->attrs().begin();
        it != this->attrs().end(); ++it) {
      entry.reset(attrs_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int64, .tensorflow.tfprof.ExecProfile> execs = 12;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->execs_size());
  {
    ::std::unique_ptr<ProfileNode_ExecsEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::google::protobuf::int64, ::tensorflow::tfprof::ExecProfile >::const_iterator
        it = this->execs().begin();
        it != this->execs().end(); ++it) {
      entry.reset(execs_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int64, int32> src_output_index = 14;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->src_output_index_size());
  {
    ::std::unique_ptr<ProfileNode_SrcOutputIndexEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::google::protobuf::int64, ::google::protobuf::int32 >::const_iterator
        it = this->src_output_index().begin();
        it != this->src_output_index().end(); ++it) {
      entry.reset(src_output_index_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int32, .tensorflow.tfprof.Tuple> output_shapes = 15;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->output_shapes_size());
  {
    ::std::unique_ptr<ProfileNode_OutputShapesEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Tuple >::const_iterator
        it = this->output_shapes().begin();
        it != this->output_shapes().end(); ++it) {
      entry.reset(output_shapes_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<int32, .tensorflow.tfprof.Tuple> input_shapes = 16;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->input_shapes_size());
  {
    ::std::unique_ptr<ProfileNode_InputShapesEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Tuple >::const_iterator
        it = this->input_shapes().begin();
        it != this->input_shapes().end(); ++it) {
      entry.reset(input_shapes_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // string canonical_device = 6;
  if (this->canonical_device().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->canonical_device());
  }

  // string host_device = 7;
  if (this->host_device().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->host_device());
  }

  // string op = 9;
  if (this->op().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->op());
  }

  // .tensorflow.tfprof.CodeDef trace = 10;
  if (this->has_trace()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *trace_);
  }

  // int64 float_ops = 8;
  if (this->float_ops() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->float_ops());
  }

  // int64 id = 13;
  if (this->id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ProfileNode::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tfprof.ProfileNode)
  GOOGLE_DCHECK_NE(&from, this);
  const ProfileNode* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ProfileNode>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tfprof.ProfileNode)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tfprof.ProfileNode)
    MergeFrom(*source);
  }
}

void ProfileNode::MergeFrom(const ProfileNode& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tfprof.ProfileNode)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  inputs_.MergeFrom(from.inputs_);
  outputs_.MergeFrom(from.outputs_);
  shape_.MergeFrom(from.shape_);
  op_types_.MergeFrom(from.op_types_);
  attrs_.MergeFrom(from.attrs_);
  execs_.MergeFrom(from.execs_);
  src_output_index_.MergeFrom(from.src_output_index_);
  output_shapes_.MergeFrom(from.output_shapes_);
  input_shapes_.MergeFrom(from.input_shapes_);
  if (from.name().size() > 0) {

    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  if (from.canonical_device().size() > 0) {

    canonical_device_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.canonical_device_);
  }
  if (from.host_device().size() > 0) {

    host_device_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.host_device_);
  }
  if (from.op().size() > 0) {

    op_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.op_);
  }
  if (from.has_trace()) {
    mutable_trace()->::tensorflow::tfprof::CodeDef::MergeFrom(from.trace());
  }
  if (from.float_ops() != 0) {
    set_float_ops(from.float_ops());
  }
  if (from.id() != 0) {
    set_id(from.id());
  }
}

void ProfileNode::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tfprof.ProfileNode)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ProfileNode::CopyFrom(const ProfileNode& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tfprof.ProfileNode)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ProfileNode::IsInitialized() const {
  return true;
}

void ProfileNode::Swap(ProfileNode* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ProfileNode::InternalSwap(ProfileNode* other) {
  using std::swap;
  inputs_.Swap(&other->inputs_);
  outputs_.Swap(&other->outputs_);
  shape_.InternalSwap(&other->shape_);
  op_types_.InternalSwap(CastToBase(&other->op_types_));
  attrs_.Swap(&other->attrs_);
  execs_.Swap(&other->execs_);
  src_output_index_.Swap(&other->src_output_index_);
  output_shapes_.Swap(&other->output_shapes_);
  input_shapes_.Swap(&other->input_shapes_);
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  canonical_device_.Swap(&other->canonical_device_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  host_device_.Swap(&other->host_device_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  op_.Swap(&other->op_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(trace_, other->trace_);
  swap(float_ops_, other->float_ops_);
  swap(id_, other->id_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ProfileNode::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

ExecProfile_AcceleratorExecsEntry_DoNotUse::ExecProfile_AcceleratorExecsEntry_DoNotUse() {}
ExecProfile_AcceleratorExecsEntry_DoNotUse::ExecProfile_AcceleratorExecsEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void ExecProfile_AcceleratorExecsEntry_DoNotUse::MergeFrom(const ExecProfile_AcceleratorExecsEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata ExecProfile_AcceleratorExecsEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[16];
}
void ExecProfile_AcceleratorExecsEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

ExecProfile_CpuExecsEntry_DoNotUse::ExecProfile_CpuExecsEntry_DoNotUse() {}
ExecProfile_CpuExecsEntry_DoNotUse::ExecProfile_CpuExecsEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void ExecProfile_CpuExecsEntry_DoNotUse::MergeFrom(const ExecProfile_CpuExecsEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata ExecProfile_CpuExecsEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[17];
}
void ExecProfile_CpuExecsEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void ExecProfile::InitAsDefaultInstance() {
}
void ExecProfile::clear_allocations() {
  allocations_.Clear();
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ExecProfile::kRunCountFieldNumber;
const int ExecProfile::kAllStartMicrosFieldNumber;
const int ExecProfile::kLatestEndMicrosFieldNumber;
const int ExecProfile::kAcceleratorExecsFieldNumber;
const int ExecProfile::kCpuExecsFieldNumber;
const int ExecProfile::kMemoryExecsFieldNumber;
const int ExecProfile::kAllocationsFieldNumber;
const int ExecProfile::kDevicesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ExecProfile::ExecProfile()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_ExecProfile.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tfprof.ExecProfile)
}
ExecProfile::ExecProfile(const ExecProfile& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      devices_(from.devices_),
      memory_execs_(from.memory_execs_),
      allocations_(from.allocations_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  accelerator_execs_.MergeFrom(from.accelerator_execs_);
  cpu_execs_.MergeFrom(from.cpu_execs_);
  ::memcpy(&run_count_, &from.run_count_,
    static_cast<size_t>(reinterpret_cast<char*>(&latest_end_micros_) -
    reinterpret_cast<char*>(&run_count_)) + sizeof(latest_end_micros_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.tfprof.ExecProfile)
}

void ExecProfile::SharedCtor() {
  ::memset(&run_count_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&latest_end_micros_) -
      reinterpret_cast<char*>(&run_count_)) + sizeof(latest_end_micros_));
}

ExecProfile::~ExecProfile() {
  // @@protoc_insertion_point(destructor:tensorflow.tfprof.ExecProfile)
  SharedDtor();
}

void ExecProfile::SharedDtor() {
}

void ExecProfile::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ExecProfile::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ExecProfile& ExecProfile::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_ExecProfile.base);
  return *internal_default_instance();
}


void ExecProfile::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tfprof.ExecProfile)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  accelerator_execs_.Clear();
  cpu_execs_.Clear();
  devices_.Clear();
  memory_execs_.Clear();
  allocations_.Clear();
  ::memset(&run_count_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&latest_end_micros_) -
      reinterpret_cast<char*>(&run_count_)) + sizeof(latest_end_micros_));
  _internal_metadata_.Clear();
}

bool ExecProfile::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tfprof.ExecProfile)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 run_count = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &run_count_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 all_start_micros = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &all_start_micros_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 latest_end_micros = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &latest_end_micros_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, .tensorflow.tfprof.ExecTime> accelerator_execs = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          ExecProfile_AcceleratorExecsEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              ExecProfile_AcceleratorExecsEntry_DoNotUse,
              ::std::string, ::tensorflow::tfprof::ExecTime,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::ExecTime > > parser(&accelerator_execs_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.ExecProfile.AcceleratorExecsEntry.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, .tensorflow.tfprof.ExecTime> cpu_execs = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          ExecProfile_CpuExecsEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              ExecProfile_CpuExecsEntry_DoNotUse,
              ::std::string, ::tensorflow::tfprof::ExecTime,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::ExecTime > > parser(&cpu_execs_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.ExecProfile.CpuExecsEntry.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string devices = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_devices()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->devices(this->devices_size() - 1).data(),
            static_cast<int>(this->devices(this->devices_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tfprof.ExecProfile.devices"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.tfprof.ExecMemory memory_execs = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_memory_execs()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.AllocationRecord allocations = 11;
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(90u /* 90 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_allocations()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tfprof.ExecProfile)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tfprof.ExecProfile)
  return false;
#undef DO_
}

void ExecProfile::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tfprof.ExecProfile)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 run_count = 1;
  if (this->run_count() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->run_count(), output);
  }

  // int64 all_start_micros = 2;
  if (this->all_start_micros() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->all_start_micros(), output);
  }

  // int64 latest_end_micros = 3;
  if (this->latest_end_micros() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->latest_end_micros(), output);
  }

  // map<string, .tensorflow.tfprof.ExecTime> accelerator_execs = 4;
  if (!this->accelerator_execs().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::ExecTime >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.tfprof.ExecProfile.AcceleratorExecsEntry.key");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->accelerator_execs().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->accelerator_execs().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::ExecTime >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::ExecTime >::const_iterator
          it = this->accelerator_execs().begin();
          it != this->accelerator_execs().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ExecProfile_AcceleratorExecsEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(accelerator_execs_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            4, *entry, output);
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<ExecProfile_AcceleratorExecsEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::ExecTime >::const_iterator
          it = this->accelerator_execs().begin();
          it != this->accelerator_execs().end(); ++it) {
        entry.reset(accelerator_execs_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            4, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<string, .tensorflow.tfprof.ExecTime> cpu_execs = 5;
  if (!this->cpu_execs().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::ExecTime >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.tfprof.ExecProfile.CpuExecsEntry.key");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->cpu_execs().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->cpu_execs().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::ExecTime >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::ExecTime >::const_iterator
          it = this->cpu_execs().begin();
          it != this->cpu_execs().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ExecProfile_CpuExecsEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(cpu_execs_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            5, *entry, output);
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<ExecProfile_CpuExecsEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::ExecTime >::const_iterator
          it = this->cpu_execs().begin();
          it != this->cpu_execs().end(); ++it) {
        entry.reset(cpu_execs_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            5, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  // repeated string devices = 6;
  for (int i = 0, n = this->devices_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->devices(i).data(), static_cast<int>(this->devices(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.ExecProfile.devices");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      6, this->devices(i), output);
  }

  // repeated .tensorflow.tfprof.ExecMemory memory_execs = 7;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->memory_execs_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7,
      this->memory_execs(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.AllocationRecord allocations = 11;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->allocations_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      11,
      this->allocations(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tfprof.ExecProfile)
}

::google::protobuf::uint8* ExecProfile::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tfprof.ExecProfile)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 run_count = 1;
  if (this->run_count() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->run_count(), target);
  }

  // int64 all_start_micros = 2;
  if (this->all_start_micros() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->all_start_micros(), target);
  }

  // int64 latest_end_micros = 3;
  if (this->latest_end_micros() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->latest_end_micros(), target);
  }

  // map<string, .tensorflow.tfprof.ExecTime> accelerator_execs = 4;
  if (!this->accelerator_execs().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::ExecTime >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.tfprof.ExecProfile.AcceleratorExecsEntry.key");
      }
    };

    if (deterministic &&
        this->accelerator_execs().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->accelerator_execs().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::ExecTime >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::ExecTime >::const_iterator
          it = this->accelerator_execs().begin();
          it != this->accelerator_execs().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ExecProfile_AcceleratorExecsEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(accelerator_execs_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       4, *entry, deterministic, target);
;
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<ExecProfile_AcceleratorExecsEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::ExecTime >::const_iterator
          it = this->accelerator_execs().begin();
          it != this->accelerator_execs().end(); ++it) {
        entry.reset(accelerator_execs_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       4, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<string, .tensorflow.tfprof.ExecTime> cpu_execs = 5;
  if (!this->cpu_execs().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::ExecTime >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.tfprof.ExecProfile.CpuExecsEntry.key");
      }
    };

    if (deterministic &&
        this->cpu_execs().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->cpu_execs().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::ExecTime >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::ExecTime >::const_iterator
          it = this->cpu_execs().begin();
          it != this->cpu_execs().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ExecProfile_CpuExecsEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(cpu_execs_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       5, *entry, deterministic, target);
;
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<ExecProfile_CpuExecsEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::ExecTime >::const_iterator
          it = this->cpu_execs().begin();
          it != this->cpu_execs().end(); ++it) {
        entry.reset(cpu_execs_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       5, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  // repeated string devices = 6;
  for (int i = 0, n = this->devices_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->devices(i).data(), static_cast<int>(this->devices(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tfprof.ExecProfile.devices");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(6, this->devices(i), target);
  }

  // repeated .tensorflow.tfprof.ExecMemory memory_execs = 7;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->memory_execs_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        7, this->memory_execs(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.AllocationRecord allocations = 11;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->allocations_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        11, this->allocations(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tfprof.ExecProfile)
  return target;
}

size_t ExecProfile::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tfprof.ExecProfile)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // map<string, .tensorflow.tfprof.ExecTime> accelerator_execs = 4;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->accelerator_execs_size());
  {
    ::std::unique_ptr<ExecProfile_AcceleratorExecsEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::ExecTime >::const_iterator
        it = this->accelerator_execs().begin();
        it != this->accelerator_execs().end(); ++it) {
      entry.reset(accelerator_execs_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<string, .tensorflow.tfprof.ExecTime> cpu_execs = 5;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->cpu_execs_size());
  {
    ::std::unique_ptr<ExecProfile_CpuExecsEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::ExecTime >::const_iterator
        it = this->cpu_execs().begin();
        it != this->cpu_execs().end(); ++it) {
      entry.reset(cpu_execs_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // repeated string devices = 6;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->devices_size());
  for (int i = 0, n = this->devices_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->devices(i));
  }

  // repeated .tensorflow.tfprof.ExecMemory memory_execs = 7;
  {
    unsigned int count = static_cast<unsigned int>(this->memory_execs_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->memory_execs(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.AllocationRecord allocations = 11;
  {
    unsigned int count = static_cast<unsigned int>(this->allocations_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->allocations(static_cast<int>(i)));
    }
  }

  // int64 run_count = 1;
  if (this->run_count() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->run_count());
  }

  // int64 all_start_micros = 2;
  if (this->all_start_micros() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->all_start_micros());
  }

  // int64 latest_end_micros = 3;
  if (this->latest_end_micros() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->latest_end_micros());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ExecProfile::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tfprof.ExecProfile)
  GOOGLE_DCHECK_NE(&from, this);
  const ExecProfile* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ExecProfile>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tfprof.ExecProfile)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tfprof.ExecProfile)
    MergeFrom(*source);
  }
}

void ExecProfile::MergeFrom(const ExecProfile& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tfprof.ExecProfile)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  accelerator_execs_.MergeFrom(from.accelerator_execs_);
  cpu_execs_.MergeFrom(from.cpu_execs_);
  devices_.MergeFrom(from.devices_);
  memory_execs_.MergeFrom(from.memory_execs_);
  allocations_.MergeFrom(from.allocations_);
  if (from.run_count() != 0) {
    set_run_count(from.run_count());
  }
  if (from.all_start_micros() != 0) {
    set_all_start_micros(from.all_start_micros());
  }
  if (from.latest_end_micros() != 0) {
    set_latest_end_micros(from.latest_end_micros());
  }
}

void ExecProfile::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tfprof.ExecProfile)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ExecProfile::CopyFrom(const ExecProfile& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tfprof.ExecProfile)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ExecProfile::IsInitialized() const {
  return true;
}

void ExecProfile::Swap(ExecProfile* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ExecProfile::InternalSwap(ExecProfile* other) {
  using std::swap;
  accelerator_execs_.Swap(&other->accelerator_execs_);
  cpu_execs_.Swap(&other->cpu_execs_);
  devices_.InternalSwap(CastToBase(&other->devices_));
  CastToBase(&memory_execs_)->InternalSwap(CastToBase(&other->memory_execs_));
  CastToBase(&allocations_)->InternalSwap(CastToBase(&other->allocations_));
  swap(run_count_, other->run_count_);
  swap(all_start_micros_, other->all_start_micros_);
  swap(latest_end_micros_, other->latest_end_micros_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ExecProfile::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ExecTime::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ExecTime::kTimesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ExecTime::ExecTime()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_ExecTime.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tfprof.ExecTime)
}
ExecTime::ExecTime(const ExecTime& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      times_(from.times_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.tfprof.ExecTime)
}

void ExecTime::SharedCtor() {
}

ExecTime::~ExecTime() {
  // @@protoc_insertion_point(destructor:tensorflow.tfprof.ExecTime)
  SharedDtor();
}

void ExecTime::SharedDtor() {
}

void ExecTime::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ExecTime::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ExecTime& ExecTime::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_ExecTime.base);
  return *internal_default_instance();
}


void ExecTime::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tfprof.ExecTime)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  times_.Clear();
  _internal_metadata_.Clear();
}

bool ExecTime::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tfprof.ExecTime)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.tfprof.Tuple times = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_times()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tfprof.ExecTime)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tfprof.ExecTime)
  return false;
#undef DO_
}

void ExecTime::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tfprof.ExecTime)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.tfprof.Tuple times = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->times_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->times(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tfprof.ExecTime)
}

::google::protobuf::uint8* ExecTime::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tfprof.ExecTime)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.tfprof.Tuple times = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->times_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->times(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tfprof.ExecTime)
  return target;
}

size_t ExecTime::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tfprof.ExecTime)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.tfprof.Tuple times = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->times_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->times(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ExecTime::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tfprof.ExecTime)
  GOOGLE_DCHECK_NE(&from, this);
  const ExecTime* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ExecTime>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tfprof.ExecTime)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tfprof.ExecTime)
    MergeFrom(*source);
  }
}

void ExecTime::MergeFrom(const ExecTime& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tfprof.ExecTime)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  times_.MergeFrom(from.times_);
}

void ExecTime::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tfprof.ExecTime)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ExecTime::CopyFrom(const ExecTime& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tfprof.ExecTime)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ExecTime::IsInitialized() const {
  return true;
}

void ExecTime::Swap(ExecTime* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ExecTime::InternalSwap(ExecTime* other) {
  using std::swap;
  CastToBase(&times_)->InternalSwap(CastToBase(&other->times_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ExecTime::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

ExecMemory_OutputMemoryEntry_DoNotUse::ExecMemory_OutputMemoryEntry_DoNotUse() {}
ExecMemory_OutputMemoryEntry_DoNotUse::ExecMemory_OutputMemoryEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void ExecMemory_OutputMemoryEntry_DoNotUse::MergeFrom(const ExecMemory_OutputMemoryEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata ExecMemory_OutputMemoryEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[20];
}
void ExecMemory_OutputMemoryEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void ExecMemory::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ExecMemory::kMemoryMicrosFieldNumber;
const int ExecMemory::kHostTempBytesFieldNumber;
const int ExecMemory::kHostPersistentBytesFieldNumber;
const int ExecMemory::kAcceleratorTempBytesFieldNumber;
const int ExecMemory::kAcceleratorPersistentBytesFieldNumber;
const int ExecMemory::kRequestedBytesFieldNumber;
const int ExecMemory::kPeakBytesFieldNumber;
const int ExecMemory::kResidualBytesFieldNumber;
const int ExecMemory::kOutputBytesFieldNumber;
const int ExecMemory::kAllocatorBytesInUseFieldNumber;
const int ExecMemory::kOutputMemoryFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ExecMemory::ExecMemory()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_ExecMemory.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tfprof.ExecMemory)
}
ExecMemory::ExecMemory(const ExecMemory& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  output_memory_.MergeFrom(from.output_memory_);
  ::memcpy(&memory_micros_, &from.memory_micros_,
    static_cast<size_t>(reinterpret_cast<char*>(&allocator_bytes_in_use_) -
    reinterpret_cast<char*>(&memory_micros_)) + sizeof(allocator_bytes_in_use_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.tfprof.ExecMemory)
}

void ExecMemory::SharedCtor() {
  ::memset(&memory_micros_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&allocator_bytes_in_use_) -
      reinterpret_cast<char*>(&memory_micros_)) + sizeof(allocator_bytes_in_use_));
}

ExecMemory::~ExecMemory() {
  // @@protoc_insertion_point(destructor:tensorflow.tfprof.ExecMemory)
  SharedDtor();
}

void ExecMemory::SharedDtor() {
}

void ExecMemory::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ExecMemory::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ExecMemory& ExecMemory::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_ExecMemory.base);
  return *internal_default_instance();
}


void ExecMemory::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tfprof.ExecMemory)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  output_memory_.Clear();
  ::memset(&memory_micros_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&allocator_bytes_in_use_) -
      reinterpret_cast<char*>(&memory_micros_)) + sizeof(allocator_bytes_in_use_));
  _internal_metadata_.Clear();
}

bool ExecMemory::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tfprof.ExecMemory)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 memory_micros = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &memory_micros_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 host_temp_bytes = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &host_temp_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 host_persistent_bytes = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &host_persistent_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 accelerator_temp_bytes = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &accelerator_temp_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 accelerator_persistent_bytes = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &accelerator_persistent_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 requested_bytes = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(48u /* 48 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &requested_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 peak_bytes = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(56u /* 56 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &peak_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 residual_bytes = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(64u /* 64 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &residual_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 output_bytes = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(72u /* 72 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &output_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 allocator_bytes_in_use = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(80u /* 80 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &allocator_bytes_in_use_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<int32, .tensorflow.tfprof.Memory> output_memory = 11;
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(90u /* 90 & 0xFF */)) {
          ExecMemory_OutputMemoryEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              ExecMemory_OutputMemoryEntry_DoNotUse,
              ::google::protobuf::int32, ::tensorflow::tfprof::Memory,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Memory > > parser(&output_memory_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tfprof.ExecMemory)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tfprof.ExecMemory)
  return false;
#undef DO_
}

void ExecMemory::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tfprof.ExecMemory)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 memory_micros = 1;
  if (this->memory_micros() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->memory_micros(), output);
  }

  // int64 host_temp_bytes = 2;
  if (this->host_temp_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->host_temp_bytes(), output);
  }

  // int64 host_persistent_bytes = 3;
  if (this->host_persistent_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->host_persistent_bytes(), output);
  }

  // int64 accelerator_temp_bytes = 4;
  if (this->accelerator_temp_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->accelerator_temp_bytes(), output);
  }

  // int64 accelerator_persistent_bytes = 5;
  if (this->accelerator_persistent_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(5, this->accelerator_persistent_bytes(), output);
  }

  // int64 requested_bytes = 6;
  if (this->requested_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(6, this->requested_bytes(), output);
  }

  // int64 peak_bytes = 7;
  if (this->peak_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(7, this->peak_bytes(), output);
  }

  // int64 residual_bytes = 8;
  if (this->residual_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->residual_bytes(), output);
  }

  // int64 output_bytes = 9;
  if (this->output_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->output_bytes(), output);
  }

  // int64 allocator_bytes_in_use = 10;
  if (this->allocator_bytes_in_use() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->allocator_bytes_in_use(), output);
  }

  // map<int32, .tensorflow.tfprof.Memory> output_memory = 11;
  if (!this->output_memory().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Memory >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterministic() &&
        this->output_memory().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->output_memory().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Memory >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Memory >::const_iterator
          it = this->output_memory().begin();
          it != this->output_memory().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ExecMemory_OutputMemoryEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(output_memory_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            11, *entry, output);
      }
    } else {
      ::std::unique_ptr<ExecMemory_OutputMemoryEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Memory >::const_iterator
          it = this->output_memory().begin();
          it != this->output_memory().end(); ++it) {
        entry.reset(output_memory_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            11, *entry, output);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tfprof.ExecMemory)
}

::google::protobuf::uint8* ExecMemory::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tfprof.ExecMemory)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 memory_micros = 1;
  if (this->memory_micros() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->memory_micros(), target);
  }

  // int64 host_temp_bytes = 2;
  if (this->host_temp_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->host_temp_bytes(), target);
  }

  // int64 host_persistent_bytes = 3;
  if (this->host_persistent_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->host_persistent_bytes(), target);
  }

  // int64 accelerator_temp_bytes = 4;
  if (this->accelerator_temp_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->accelerator_temp_bytes(), target);
  }

  // int64 accelerator_persistent_bytes = 5;
  if (this->accelerator_persistent_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(5, this->accelerator_persistent_bytes(), target);
  }

  // int64 requested_bytes = 6;
  if (this->requested_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(6, this->requested_bytes(), target);
  }

  // int64 peak_bytes = 7;
  if (this->peak_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(7, this->peak_bytes(), target);
  }

  // int64 residual_bytes = 8;
  if (this->residual_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->residual_bytes(), target);
  }

  // int64 output_bytes = 9;
  if (this->output_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->output_bytes(), target);
  }

  // int64 allocator_bytes_in_use = 10;
  if (this->allocator_bytes_in_use() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->allocator_bytes_in_use(), target);
  }

  // map<int32, .tensorflow.tfprof.Memory> output_memory = 11;
  if (!this->output_memory().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Memory >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::int32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->output_memory().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->output_memory().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Memory >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Memory >::const_iterator
          it = this->output_memory().begin();
          it != this->output_memory().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ExecMemory_OutputMemoryEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(output_memory_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       11, *entry, deterministic, target);
;
      }
    } else {
      ::std::unique_ptr<ExecMemory_OutputMemoryEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Memory >::const_iterator
          it = this->output_memory().begin();
          it != this->output_memory().end(); ++it) {
        entry.reset(output_memory_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       11, *entry, deterministic, target);
;
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tfprof.ExecMemory)
  return target;
}

size_t ExecMemory::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tfprof.ExecMemory)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // map<int32, .tensorflow.tfprof.Memory> output_memory = 11;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->output_memory_size());
  {
    ::std::unique_ptr<ExecMemory_OutputMemoryEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::google::protobuf::int32, ::tensorflow::tfprof::Memory >::const_iterator
        it = this->output_memory().begin();
        it != this->output_memory().end(); ++it) {
      entry.reset(output_memory_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // int64 memory_micros = 1;
  if (this->memory_micros() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->memory_micros());
  }

  // int64 host_temp_bytes = 2;
  if (this->host_temp_bytes() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->host_temp_bytes());
  }

  // int64 host_persistent_bytes = 3;
  if (this->host_persistent_bytes() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->host_persistent_bytes());
  }

  // int64 accelerator_temp_bytes = 4;
  if (this->accelerator_temp_bytes() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->accelerator_temp_bytes());
  }

  // int64 accelerator_persistent_bytes = 5;
  if (this->accelerator_persistent_bytes() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->accelerator_persistent_bytes());
  }

  // int64 requested_bytes = 6;
  if (this->requested_bytes() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->requested_bytes());
  }

  // int64 peak_bytes = 7;
  if (this->peak_bytes() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->peak_bytes());
  }

  // int64 residual_bytes = 8;
  if (this->residual_bytes() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->residual_bytes());
  }

  // int64 output_bytes = 9;
  if (this->output_bytes() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->output_bytes());
  }

  // int64 allocator_bytes_in_use = 10;
  if (this->allocator_bytes_in_use() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->allocator_bytes_in_use());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ExecMemory::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tfprof.ExecMemory)
  GOOGLE_DCHECK_NE(&from, this);
  const ExecMemory* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ExecMemory>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tfprof.ExecMemory)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tfprof.ExecMemory)
    MergeFrom(*source);
  }
}

void ExecMemory::MergeFrom(const ExecMemory& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tfprof.ExecMemory)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  output_memory_.MergeFrom(from.output_memory_);
  if (from.memory_micros() != 0) {
    set_memory_micros(from.memory_micros());
  }
  if (from.host_temp_bytes() != 0) {
    set_host_temp_bytes(from.host_temp_bytes());
  }
  if (from.host_persistent_bytes() != 0) {
    set_host_persistent_bytes(from.host_persistent_bytes());
  }
  if (from.accelerator_temp_bytes() != 0) {
    set_accelerator_temp_bytes(from.accelerator_temp_bytes());
  }
  if (from.accelerator_persistent_bytes() != 0) {
    set_accelerator_persistent_bytes(from.accelerator_persistent_bytes());
  }
  if (from.requested_bytes() != 0) {
    set_requested_bytes(from.requested_bytes());
  }
  if (from.peak_bytes() != 0) {
    set_peak_bytes(from.peak_bytes());
  }
  if (from.residual_bytes() != 0) {
    set_residual_bytes(from.residual_bytes());
  }
  if (from.output_bytes() != 0) {
    set_output_bytes(from.output_bytes());
  }
  if (from.allocator_bytes_in_use() != 0) {
    set_allocator_bytes_in_use(from.allocator_bytes_in_use());
  }
}

void ExecMemory::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tfprof.ExecMemory)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ExecMemory::CopyFrom(const ExecMemory& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tfprof.ExecMemory)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ExecMemory::IsInitialized() const {
  return true;
}

void ExecMemory::Swap(ExecMemory* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ExecMemory::InternalSwap(ExecMemory* other) {
  using std::swap;
  output_memory_.Swap(&other->output_memory_);
  swap(memory_micros_, other->memory_micros_);
  swap(host_temp_bytes_, other->host_temp_bytes_);
  swap(host_persistent_bytes_, other->host_persistent_bytes_);
  swap(accelerator_temp_bytes_, other->accelerator_temp_bytes_);
  swap(accelerator_persistent_bytes_, other->accelerator_persistent_bytes_);
  swap(requested_bytes_, other->requested_bytes_);
  swap(peak_bytes_, other->peak_bytes_);
  swap(residual_bytes_, other->residual_bytes_);
  swap(output_bytes_, other->output_bytes_);
  swap(allocator_bytes_in_use_, other->allocator_bytes_in_use_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ExecMemory::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Tuple::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Tuple::kInt64ValuesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Tuple::Tuple()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_Tuple.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tfprof.Tuple)
}
Tuple::Tuple(const Tuple& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      int64_values_(from.int64_values_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.tfprof.Tuple)
}

void Tuple::SharedCtor() {
}

Tuple::~Tuple() {
  // @@protoc_insertion_point(destructor:tensorflow.tfprof.Tuple)
  SharedDtor();
}

void Tuple::SharedDtor() {
}

void Tuple::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Tuple::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Tuple& Tuple::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_Tuple.base);
  return *internal_default_instance();
}


void Tuple::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tfprof.Tuple)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  int64_values_.Clear();
  _internal_metadata_.Clear();
}

bool Tuple::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tfprof.Tuple)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated int64 int64_values = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_int64_values())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 1, 10u, input, this->mutable_int64_values())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tfprof.Tuple)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tfprof.Tuple)
  return false;
#undef DO_
}

void Tuple::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tfprof.Tuple)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated int64 int64_values = 1;
  if (this->int64_values_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(1, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _int64_values_cached_byte_size_));
  }
  for (int i = 0, n = this->int64_values_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->int64_values(i), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tfprof.Tuple)
}

::google::protobuf::uint8* Tuple::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tfprof.Tuple)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated int64 int64_values = 1;
  if (this->int64_values_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      1,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _int64_values_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->int64_values_, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tfprof.Tuple)
  return target;
}

size_t Tuple::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tfprof.Tuple)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated int64 int64_values = 1;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int64Size(this->int64_values_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _int64_values_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Tuple::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tfprof.Tuple)
  GOOGLE_DCHECK_NE(&from, this);
  const Tuple* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Tuple>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tfprof.Tuple)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tfprof.Tuple)
    MergeFrom(*source);
  }
}

void Tuple::MergeFrom(const Tuple& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tfprof.Tuple)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  int64_values_.MergeFrom(from.int64_values_);
}

void Tuple::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tfprof.Tuple)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Tuple::CopyFrom(const Tuple& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tfprof.Tuple)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Tuple::IsInitialized() const {
  return true;
}

void Tuple::Swap(Tuple* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Tuple::InternalSwap(Tuple* other) {
  using std::swap;
  int64_values_.InternalSwap(&other->int64_values_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Tuple::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Memory::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Memory::kBytesFieldNumber;
const int Memory::kPtrFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Memory::Memory()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_Memory.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tfprof.Memory)
}
Memory::Memory(const Memory& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&bytes_, &from.bytes_,
    static_cast<size_t>(reinterpret_cast<char*>(&ptr_) -
    reinterpret_cast<char*>(&bytes_)) + sizeof(ptr_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.tfprof.Memory)
}

void Memory::SharedCtor() {
  ::memset(&bytes_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&ptr_) -
      reinterpret_cast<char*>(&bytes_)) + sizeof(ptr_));
}

Memory::~Memory() {
  // @@protoc_insertion_point(destructor:tensorflow.tfprof.Memory)
  SharedDtor();
}

void Memory::SharedDtor() {
}

void Memory::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Memory::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Memory& Memory::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::scc_info_Memory.base);
  return *internal_default_instance();
}


void Memory::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tfprof.Memory)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&bytes_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&ptr_) -
      reinterpret_cast<char*>(&bytes_)) + sizeof(ptr_));
  _internal_metadata_.Clear();
}

bool Memory::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tfprof.Memory)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 bytes = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint64 ptr = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &ptr_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tfprof.Memory)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tfprof.Memory)
  return false;
#undef DO_
}

void Memory::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tfprof.Memory)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 bytes = 1;
  if (this->bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->bytes(), output);
  }

  // uint64 ptr = 2;
  if (this->ptr() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->ptr(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tfprof.Memory)
}

::google::protobuf::uint8* Memory::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tfprof.Memory)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 bytes = 1;
  if (this->bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->bytes(), target);
  }

  // uint64 ptr = 2;
  if (this->ptr() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->ptr(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tfprof.Memory)
  return target;
}

size_t Memory::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tfprof.Memory)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int64 bytes = 1;
  if (this->bytes() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->bytes());
  }

  // uint64 ptr = 2;
  if (this->ptr() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->ptr());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Memory::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tfprof.Memory)
  GOOGLE_DCHECK_NE(&from, this);
  const Memory* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Memory>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tfprof.Memory)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tfprof.Memory)
    MergeFrom(*source);
  }
}

void Memory::MergeFrom(const Memory& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tfprof.Memory)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.bytes() != 0) {
    set_bytes(from.bytes());
  }
  if (from.ptr() != 0) {
    set_ptr(from.ptr());
  }
}

void Memory::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tfprof.Memory)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Memory::CopyFrom(const Memory& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tfprof.Memory)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Memory::IsInitialized() const {
  return true;
}

void Memory::Swap(Memory* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Memory::InternalSwap(Memory* other) {
  using std::swap;
  swap(bytes_, other->bytes_);
  swap(ptr_, other->ptr_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Memory::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tfprof
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::CodeDef_Trace* Arena::CreateMaybeMessage< ::tensorflow::tfprof::CodeDef_Trace >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::CodeDef_Trace >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::CodeDef* Arena::CreateMaybeMessage< ::tensorflow::tfprof::CodeDef >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::CodeDef >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::OpLogEntry* Arena::CreateMaybeMessage< ::tensorflow::tfprof::OpLogEntry >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::OpLogEntry >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::OpLogProto_IdToStringEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::tfprof::OpLogProto_IdToStringEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::OpLogProto_IdToStringEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::OpLogProto* Arena::CreateMaybeMessage< ::tensorflow::tfprof::OpLogProto >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::OpLogProto >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::ProfileProto_NodesEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::tfprof::ProfileProto_NodesEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::ProfileProto_NodesEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::ProfileProto_IdToStringEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::tfprof::ProfileProto_IdToStringEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::ProfileProto_IdToStringEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::ProfileProto* Arena::CreateMaybeMessage< ::tensorflow::tfprof::ProfileProto >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::ProfileProto >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::ProfileNode_InputsEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::tfprof::ProfileNode_InputsEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::ProfileNode_InputsEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::ProfileNode_InputShapesEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::tfprof::ProfileNode_InputShapesEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::ProfileNode_InputShapesEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::ProfileNode_OutputsEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::tfprof::ProfileNode_OutputsEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::ProfileNode_OutputsEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::ProfileNode_OutputShapesEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::tfprof::ProfileNode_OutputShapesEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::ProfileNode_OutputShapesEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::ProfileNode_SrcOutputIndexEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::tfprof::ProfileNode_SrcOutputIndexEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::ProfileNode_SrcOutputIndexEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::ProfileNode_AttrsEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::tfprof::ProfileNode_AttrsEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::ProfileNode_AttrsEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::ProfileNode_ExecsEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::tfprof::ProfileNode_ExecsEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::ProfileNode_ExecsEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::ProfileNode* Arena::CreateMaybeMessage< ::tensorflow::tfprof::ProfileNode >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::ProfileNode >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::ExecProfile_AcceleratorExecsEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::tfprof::ExecProfile_AcceleratorExecsEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::ExecProfile_AcceleratorExecsEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::ExecProfile_CpuExecsEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::tfprof::ExecProfile_CpuExecsEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::ExecProfile_CpuExecsEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::ExecProfile* Arena::CreateMaybeMessage< ::tensorflow::tfprof::ExecProfile >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::ExecProfile >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::ExecTime* Arena::CreateMaybeMessage< ::tensorflow::tfprof::ExecTime >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::ExecTime >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::ExecMemory_OutputMemoryEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::tfprof::ExecMemory_OutputMemoryEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::ExecMemory_OutputMemoryEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::ExecMemory* Arena::CreateMaybeMessage< ::tensorflow::tfprof::ExecMemory >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::ExecMemory >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::Tuple* Arena::CreateMaybeMessage< ::tensorflow::tfprof::Tuple >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::Tuple >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tfprof::Memory* Arena::CreateMaybeMessage< ::tensorflow::tfprof::Memory >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tfprof::Memory >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

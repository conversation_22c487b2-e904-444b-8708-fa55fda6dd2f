// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/profiler/tfprof_options.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto 

namespace protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[5];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto
namespace tensorflow {
namespace tfprof {
class AdvisorOptionsProto;
class AdvisorOptionsProtoDefaultTypeInternal;
extern AdvisorOptionsProtoDefaultTypeInternal _AdvisorOptionsProto_default_instance_;
class AdvisorOptionsProto_CheckerOption;
class AdvisorOptionsProto_CheckerOptionDefaultTypeInternal;
extern AdvisorOptionsProto_CheckerOptionDefaultTypeInternal _AdvisorOptionsProto_CheckerOption_default_instance_;
class AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse;
class AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUseDefaultTypeInternal;
extern AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUseDefaultTypeInternal _AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse_default_instance_;
class AdvisorOptionsProto_CheckersEntry_DoNotUse;
class AdvisorOptionsProto_CheckersEntry_DoNotUseDefaultTypeInternal;
extern AdvisorOptionsProto_CheckersEntry_DoNotUseDefaultTypeInternal _AdvisorOptionsProto_CheckersEntry_DoNotUse_default_instance_;
class OptionsProto;
class OptionsProtoDefaultTypeInternal;
extern OptionsProtoDefaultTypeInternal _OptionsProto_default_instance_;
}  // namespace tfprof
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::tfprof::AdvisorOptionsProto* Arena::CreateMaybeMessage<::tensorflow::tfprof::AdvisorOptionsProto>(Arena*);
template<> ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption* Arena::CreateMaybeMessage<::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption>(Arena*);
template<> ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::AdvisorOptionsProto_CheckersEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::AdvisorOptionsProto_CheckersEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::OptionsProto* Arena::CreateMaybeMessage<::tensorflow::tfprof::OptionsProto>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {
namespace tfprof {

// ===================================================================

class OptionsProto : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.OptionsProto) */ {
 public:
  OptionsProto();
  virtual ~OptionsProto();

  OptionsProto(const OptionsProto& from);

  inline OptionsProto& operator=(const OptionsProto& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  OptionsProto(OptionsProto&& from) noexcept
    : OptionsProto() {
    *this = ::std::move(from);
  }

  inline OptionsProto& operator=(OptionsProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const OptionsProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OptionsProto* internal_default_instance() {
    return reinterpret_cast<const OptionsProto*>(
               &_OptionsProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void Swap(OptionsProto* other);
  friend void swap(OptionsProto& a, OptionsProto& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline OptionsProto* New() const final {
    return CreateMaybeMessage<OptionsProto>(NULL);
  }

  OptionsProto* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<OptionsProto>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const OptionsProto& from);
  void MergeFrom(const OptionsProto& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OptionsProto* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string account_type_regexes = 8;
  int account_type_regexes_size() const;
  void clear_account_type_regexes();
  static const int kAccountTypeRegexesFieldNumber = 8;
  const ::std::string& account_type_regexes(int index) const;
  ::std::string* mutable_account_type_regexes(int index);
  void set_account_type_regexes(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_account_type_regexes(int index, ::std::string&& value);
  #endif
  void set_account_type_regexes(int index, const char* value);
  void set_account_type_regexes(int index, const char* value, size_t size);
  ::std::string* add_account_type_regexes();
  void add_account_type_regexes(const ::std::string& value);
  #if LANG_CXX11
  void add_account_type_regexes(::std::string&& value);
  #endif
  void add_account_type_regexes(const char* value);
  void add_account_type_regexes(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& account_type_regexes() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_account_type_regexes();

  // repeated string start_name_regexes = 9;
  int start_name_regexes_size() const;
  void clear_start_name_regexes();
  static const int kStartNameRegexesFieldNumber = 9;
  const ::std::string& start_name_regexes(int index) const;
  ::std::string* mutable_start_name_regexes(int index);
  void set_start_name_regexes(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_start_name_regexes(int index, ::std::string&& value);
  #endif
  void set_start_name_regexes(int index, const char* value);
  void set_start_name_regexes(int index, const char* value, size_t size);
  ::std::string* add_start_name_regexes();
  void add_start_name_regexes(const ::std::string& value);
  #if LANG_CXX11
  void add_start_name_regexes(::std::string&& value);
  #endif
  void add_start_name_regexes(const char* value);
  void add_start_name_regexes(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& start_name_regexes() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_start_name_regexes();

  // repeated string trim_name_regexes = 10;
  int trim_name_regexes_size() const;
  void clear_trim_name_regexes();
  static const int kTrimNameRegexesFieldNumber = 10;
  const ::std::string& trim_name_regexes(int index) const;
  ::std::string* mutable_trim_name_regexes(int index);
  void set_trim_name_regexes(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_trim_name_regexes(int index, ::std::string&& value);
  #endif
  void set_trim_name_regexes(int index, const char* value);
  void set_trim_name_regexes(int index, const char* value, size_t size);
  ::std::string* add_trim_name_regexes();
  void add_trim_name_regexes(const ::std::string& value);
  #if LANG_CXX11
  void add_trim_name_regexes(::std::string&& value);
  #endif
  void add_trim_name_regexes(const char* value);
  void add_trim_name_regexes(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& trim_name_regexes() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_trim_name_regexes();

  // repeated string show_name_regexes = 11;
  int show_name_regexes_size() const;
  void clear_show_name_regexes();
  static const int kShowNameRegexesFieldNumber = 11;
  const ::std::string& show_name_regexes(int index) const;
  ::std::string* mutable_show_name_regexes(int index);
  void set_show_name_regexes(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_show_name_regexes(int index, ::std::string&& value);
  #endif
  void set_show_name_regexes(int index, const char* value);
  void set_show_name_regexes(int index, const char* value, size_t size);
  ::std::string* add_show_name_regexes();
  void add_show_name_regexes(const ::std::string& value);
  #if LANG_CXX11
  void add_show_name_regexes(::std::string&& value);
  #endif
  void add_show_name_regexes(const char* value);
  void add_show_name_regexes(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& show_name_regexes() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_show_name_regexes();

  // repeated string hide_name_regexes = 12;
  int hide_name_regexes_size() const;
  void clear_hide_name_regexes();
  static const int kHideNameRegexesFieldNumber = 12;
  const ::std::string& hide_name_regexes(int index) const;
  ::std::string* mutable_hide_name_regexes(int index);
  void set_hide_name_regexes(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_hide_name_regexes(int index, ::std::string&& value);
  #endif
  void set_hide_name_regexes(int index, const char* value);
  void set_hide_name_regexes(int index, const char* value, size_t size);
  ::std::string* add_hide_name_regexes();
  void add_hide_name_regexes(const ::std::string& value);
  #if LANG_CXX11
  void add_hide_name_regexes(::std::string&& value);
  #endif
  void add_hide_name_regexes(const char* value);
  void add_hide_name_regexes(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& hide_name_regexes() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_hide_name_regexes();

  // repeated string select = 14;
  int select_size() const;
  void clear_select();
  static const int kSelectFieldNumber = 14;
  const ::std::string& select(int index) const;
  ::std::string* mutable_select(int index);
  void set_select(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_select(int index, ::std::string&& value);
  #endif
  void set_select(int index, const char* value);
  void set_select(int index, const char* value, size_t size);
  ::std::string* add_select();
  void add_select(const ::std::string& value);
  #if LANG_CXX11
  void add_select(::std::string&& value);
  #endif
  void add_select(const char* value);
  void add_select(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& select() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_select();

  // string order_by = 7;
  void clear_order_by();
  static const int kOrderByFieldNumber = 7;
  const ::std::string& order_by() const;
  void set_order_by(const ::std::string& value);
  #if LANG_CXX11
  void set_order_by(::std::string&& value);
  #endif
  void set_order_by(const char* value);
  void set_order_by(const char* value, size_t size);
  ::std::string* mutable_order_by();
  ::std::string* release_order_by();
  void set_allocated_order_by(::std::string* order_by);

  // string output = 15;
  void clear_output();
  static const int kOutputFieldNumber = 15;
  const ::std::string& output() const;
  void set_output(const ::std::string& value);
  #if LANG_CXX11
  void set_output(::std::string&& value);
  #endif
  void set_output(const char* value);
  void set_output(const char* value, size_t size);
  ::std::string* mutable_output();
  ::std::string* release_output();
  void set_allocated_output(::std::string* output);

  // string dump_to_file = 16;
  void clear_dump_to_file();
  static const int kDumpToFileFieldNumber = 16;
  const ::std::string& dump_to_file() const;
  void set_dump_to_file(const ::std::string& value);
  #if LANG_CXX11
  void set_dump_to_file(::std::string&& value);
  #endif
  void set_dump_to_file(const char* value);
  void set_dump_to_file(const char* value, size_t size);
  ::std::string* mutable_dump_to_file();
  ::std::string* release_dump_to_file();
  void set_allocated_dump_to_file(::std::string* dump_to_file);

  // int64 max_depth = 1;
  void clear_max_depth();
  static const int kMaxDepthFieldNumber = 1;
  ::google::protobuf::int64 max_depth() const;
  void set_max_depth(::google::protobuf::int64 value);

  // int64 min_bytes = 2;
  void clear_min_bytes();
  static const int kMinBytesFieldNumber = 2;
  ::google::protobuf::int64 min_bytes() const;
  void set_min_bytes(::google::protobuf::int64 value);

  // int64 min_micros = 3;
  void clear_min_micros();
  static const int kMinMicrosFieldNumber = 3;
  ::google::protobuf::int64 min_micros() const;
  void set_min_micros(::google::protobuf::int64 value);

  // int64 min_params = 4;
  void clear_min_params();
  static const int kMinParamsFieldNumber = 4;
  ::google::protobuf::int64 min_params() const;
  void set_min_params(::google::protobuf::int64 value);

  // int64 min_float_ops = 5;
  void clear_min_float_ops();
  static const int kMinFloatOpsFieldNumber = 5;
  ::google::protobuf::int64 min_float_ops() const;
  void set_min_float_ops(::google::protobuf::int64 value);

  // int64 min_occurrence = 17;
  void clear_min_occurrence();
  static const int kMinOccurrenceFieldNumber = 17;
  ::google::protobuf::int64 min_occurrence() const;
  void set_min_occurrence(::google::protobuf::int64 value);

  // int64 step = 18;
  void clear_step();
  static const int kStepFieldNumber = 18;
  ::google::protobuf::int64 step() const;
  void set_step(::google::protobuf::int64 value);

  // int64 min_peak_bytes = 19;
  void clear_min_peak_bytes();
  static const int kMinPeakBytesFieldNumber = 19;
  ::google::protobuf::int64 min_peak_bytes() const;
  void set_min_peak_bytes(::google::protobuf::int64 value);

  // int64 min_residual_bytes = 20;
  void clear_min_residual_bytes();
  static const int kMinResidualBytesFieldNumber = 20;
  ::google::protobuf::int64 min_residual_bytes() const;
  void set_min_residual_bytes(::google::protobuf::int64 value);

  // int64 min_output_bytes = 21;
  void clear_min_output_bytes();
  static const int kMinOutputBytesFieldNumber = 21;
  ::google::protobuf::int64 min_output_bytes() const;
  void set_min_output_bytes(::google::protobuf::int64 value);

  // int64 min_accelerator_micros = 22;
  void clear_min_accelerator_micros();
  static const int kMinAcceleratorMicrosFieldNumber = 22;
  ::google::protobuf::int64 min_accelerator_micros() const;
  void set_min_accelerator_micros(::google::protobuf::int64 value);

  // int64 min_cpu_micros = 23;
  void clear_min_cpu_micros();
  static const int kMinCpuMicrosFieldNumber = 23;
  ::google::protobuf::int64 min_cpu_micros() const;
  void set_min_cpu_micros(::google::protobuf::int64 value);

  // bool account_displayed_op_only = 13;
  void clear_account_displayed_op_only();
  static const int kAccountDisplayedOpOnlyFieldNumber = 13;
  bool account_displayed_op_only() const;
  void set_account_displayed_op_only(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.OptionsProto)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::std::string> account_type_regexes_;
  ::google::protobuf::RepeatedPtrField< ::std::string> start_name_regexes_;
  ::google::protobuf::RepeatedPtrField< ::std::string> trim_name_regexes_;
  ::google::protobuf::RepeatedPtrField< ::std::string> show_name_regexes_;
  ::google::protobuf::RepeatedPtrField< ::std::string> hide_name_regexes_;
  ::google::protobuf::RepeatedPtrField< ::std::string> select_;
  ::google::protobuf::internal::ArenaStringPtr order_by_;
  ::google::protobuf::internal::ArenaStringPtr output_;
  ::google::protobuf::internal::ArenaStringPtr dump_to_file_;
  ::google::protobuf::int64 max_depth_;
  ::google::protobuf::int64 min_bytes_;
  ::google::protobuf::int64 min_micros_;
  ::google::protobuf::int64 min_params_;
  ::google::protobuf::int64 min_float_ops_;
  ::google::protobuf::int64 min_occurrence_;
  ::google::protobuf::int64 step_;
  ::google::protobuf::int64 min_peak_bytes_;
  ::google::protobuf::int64 min_residual_bytes_;
  ::google::protobuf::int64 min_output_bytes_;
  ::google::protobuf::int64 min_accelerator_micros_;
  ::google::protobuf::int64 min_cpu_micros_;
  bool account_displayed_op_only_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class AdvisorOptionsProto_CheckersEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<AdvisorOptionsProto_CheckersEntry_DoNotUse, 
    ::std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<AdvisorOptionsProto_CheckersEntry_DoNotUse, 
    ::std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  AdvisorOptionsProto_CheckersEntry_DoNotUse();
  AdvisorOptionsProto_CheckersEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const AdvisorOptionsProto_CheckersEntry_DoNotUse& other);
  static const AdvisorOptionsProto_CheckersEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const AdvisorOptionsProto_CheckersEntry_DoNotUse*>(&_AdvisorOptionsProto_CheckersEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse, 
    ::std::string, ::std::string,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse, 
    ::std::string, ::std::string,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    0 > SuperType;
  AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse();
  AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse& other);
  static const AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse*>(&_AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class AdvisorOptionsProto_CheckerOption : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption) */ {
 public:
  AdvisorOptionsProto_CheckerOption();
  virtual ~AdvisorOptionsProto_CheckerOption();

  AdvisorOptionsProto_CheckerOption(const AdvisorOptionsProto_CheckerOption& from);

  inline AdvisorOptionsProto_CheckerOption& operator=(const AdvisorOptionsProto_CheckerOption& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  AdvisorOptionsProto_CheckerOption(AdvisorOptionsProto_CheckerOption&& from) noexcept
    : AdvisorOptionsProto_CheckerOption() {
    *this = ::std::move(from);
  }

  inline AdvisorOptionsProto_CheckerOption& operator=(AdvisorOptionsProto_CheckerOption&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const AdvisorOptionsProto_CheckerOption& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AdvisorOptionsProto_CheckerOption* internal_default_instance() {
    return reinterpret_cast<const AdvisorOptionsProto_CheckerOption*>(
               &_AdvisorOptionsProto_CheckerOption_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void Swap(AdvisorOptionsProto_CheckerOption* other);
  friend void swap(AdvisorOptionsProto_CheckerOption& a, AdvisorOptionsProto_CheckerOption& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline AdvisorOptionsProto_CheckerOption* New() const final {
    return CreateMaybeMessage<AdvisorOptionsProto_CheckerOption>(NULL);
  }

  AdvisorOptionsProto_CheckerOption* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<AdvisorOptionsProto_CheckerOption>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const AdvisorOptionsProto_CheckerOption& from);
  void MergeFrom(const AdvisorOptionsProto_CheckerOption& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AdvisorOptionsProto_CheckerOption* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<string, string> options = 1;
  int options_size() const;
  void clear_options();
  static const int kOptionsFieldNumber = 1;
  const ::google::protobuf::Map< ::std::string, ::std::string >&
      options() const;
  ::google::protobuf::Map< ::std::string, ::std::string >*
      mutable_options();

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::MapField<
      AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse,
      ::std::string, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 > options_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class AdvisorOptionsProto : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.AdvisorOptionsProto) */ {
 public:
  AdvisorOptionsProto();
  virtual ~AdvisorOptionsProto();

  AdvisorOptionsProto(const AdvisorOptionsProto& from);

  inline AdvisorOptionsProto& operator=(const AdvisorOptionsProto& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  AdvisorOptionsProto(AdvisorOptionsProto&& from) noexcept
    : AdvisorOptionsProto() {
    *this = ::std::move(from);
  }

  inline AdvisorOptionsProto& operator=(AdvisorOptionsProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const AdvisorOptionsProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AdvisorOptionsProto* internal_default_instance() {
    return reinterpret_cast<const AdvisorOptionsProto*>(
               &_AdvisorOptionsProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void Swap(AdvisorOptionsProto* other);
  friend void swap(AdvisorOptionsProto& a, AdvisorOptionsProto& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline AdvisorOptionsProto* New() const final {
    return CreateMaybeMessage<AdvisorOptionsProto>(NULL);
  }

  AdvisorOptionsProto* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<AdvisorOptionsProto>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const AdvisorOptionsProto& from);
  void MergeFrom(const AdvisorOptionsProto& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AdvisorOptionsProto* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef AdvisorOptionsProto_CheckerOption CheckerOption;

  // accessors -------------------------------------------------------

  // map<string, .tensorflow.tfprof.AdvisorOptionsProto.CheckerOption> checkers = 1;
  int checkers_size() const;
  void clear_checkers();
  static const int kCheckersFieldNumber = 1;
  const ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption >&
      checkers() const;
  ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption >*
      mutable_checkers();

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.AdvisorOptionsProto)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::MapField<
      AdvisorOptionsProto_CheckersEntry_DoNotUse,
      ::std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > checkers_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// OptionsProto

// int64 max_depth = 1;
inline void OptionsProto::clear_max_depth() {
  max_depth_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OptionsProto::max_depth() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.max_depth)
  return max_depth_;
}
inline void OptionsProto::set_max_depth(::google::protobuf::int64 value) {
  
  max_depth_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.max_depth)
}

// int64 min_bytes = 2;
inline void OptionsProto::clear_min_bytes() {
  min_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OptionsProto::min_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_bytes)
  return min_bytes_;
}
inline void OptionsProto::set_min_bytes(::google::protobuf::int64 value) {
  
  min_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_bytes)
}

// int64 min_peak_bytes = 19;
inline void OptionsProto::clear_min_peak_bytes() {
  min_peak_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OptionsProto::min_peak_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_peak_bytes)
  return min_peak_bytes_;
}
inline void OptionsProto::set_min_peak_bytes(::google::protobuf::int64 value) {
  
  min_peak_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_peak_bytes)
}

// int64 min_residual_bytes = 20;
inline void OptionsProto::clear_min_residual_bytes() {
  min_residual_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OptionsProto::min_residual_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_residual_bytes)
  return min_residual_bytes_;
}
inline void OptionsProto::set_min_residual_bytes(::google::protobuf::int64 value) {
  
  min_residual_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_residual_bytes)
}

// int64 min_output_bytes = 21;
inline void OptionsProto::clear_min_output_bytes() {
  min_output_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OptionsProto::min_output_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_output_bytes)
  return min_output_bytes_;
}
inline void OptionsProto::set_min_output_bytes(::google::protobuf::int64 value) {
  
  min_output_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_output_bytes)
}

// int64 min_micros = 3;
inline void OptionsProto::clear_min_micros() {
  min_micros_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OptionsProto::min_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_micros)
  return min_micros_;
}
inline void OptionsProto::set_min_micros(::google::protobuf::int64 value) {
  
  min_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_micros)
}

// int64 min_accelerator_micros = 22;
inline void OptionsProto::clear_min_accelerator_micros() {
  min_accelerator_micros_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OptionsProto::min_accelerator_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_accelerator_micros)
  return min_accelerator_micros_;
}
inline void OptionsProto::set_min_accelerator_micros(::google::protobuf::int64 value) {
  
  min_accelerator_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_accelerator_micros)
}

// int64 min_cpu_micros = 23;
inline void OptionsProto::clear_min_cpu_micros() {
  min_cpu_micros_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OptionsProto::min_cpu_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_cpu_micros)
  return min_cpu_micros_;
}
inline void OptionsProto::set_min_cpu_micros(::google::protobuf::int64 value) {
  
  min_cpu_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_cpu_micros)
}

// int64 min_params = 4;
inline void OptionsProto::clear_min_params() {
  min_params_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OptionsProto::min_params() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_params)
  return min_params_;
}
inline void OptionsProto::set_min_params(::google::protobuf::int64 value) {
  
  min_params_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_params)
}

// int64 min_float_ops = 5;
inline void OptionsProto::clear_min_float_ops() {
  min_float_ops_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OptionsProto::min_float_ops() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_float_ops)
  return min_float_ops_;
}
inline void OptionsProto::set_min_float_ops(::google::protobuf::int64 value) {
  
  min_float_ops_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_float_ops)
}

// int64 min_occurrence = 17;
inline void OptionsProto::clear_min_occurrence() {
  min_occurrence_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OptionsProto::min_occurrence() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_occurrence)
  return min_occurrence_;
}
inline void OptionsProto::set_min_occurrence(::google::protobuf::int64 value) {
  
  min_occurrence_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_occurrence)
}

// int64 step = 18;
inline void OptionsProto::clear_step() {
  step_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OptionsProto::step() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.step)
  return step_;
}
inline void OptionsProto::set_step(::google::protobuf::int64 value) {
  
  step_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.step)
}

// string order_by = 7;
inline void OptionsProto::clear_order_by() {
  order_by_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionsProto::order_by() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.order_by)
  return order_by_.GetNoArena();
}
inline void OptionsProto::set_order_by(const ::std::string& value) {
  
  order_by_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.order_by)
}
#if LANG_CXX11
inline void OptionsProto::set_order_by(::std::string&& value) {
  
  order_by_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tfprof.OptionsProto.order_by)
}
#endif
inline void OptionsProto::set_order_by(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  order_by_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.OptionsProto.order_by)
}
inline void OptionsProto::set_order_by(const char* value, size_t size) {
  
  order_by_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.OptionsProto.order_by)
}
inline ::std::string* OptionsProto::mutable_order_by() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OptionsProto.order_by)
  return order_by_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionsProto::release_order_by() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.OptionsProto.order_by)
  
  return order_by_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionsProto::set_allocated_order_by(::std::string* order_by) {
  if (order_by != NULL) {
    
  } else {
    
  }
  order_by_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), order_by);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.OptionsProto.order_by)
}

// repeated string account_type_regexes = 8;
inline int OptionsProto::account_type_regexes_size() const {
  return account_type_regexes_.size();
}
inline void OptionsProto::clear_account_type_regexes() {
  account_type_regexes_.Clear();
}
inline const ::std::string& OptionsProto::account_type_regexes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.account_type_regexes)
  return account_type_regexes_.Get(index);
}
inline ::std::string* OptionsProto::mutable_account_type_regexes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OptionsProto.account_type_regexes)
  return account_type_regexes_.Mutable(index);
}
inline void OptionsProto::set_account_type_regexes(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.account_type_regexes)
  account_type_regexes_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void OptionsProto::set_account_type_regexes(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.account_type_regexes)
  account_type_regexes_.Mutable(index)->assign(std::move(value));
}
#endif
inline void OptionsProto::set_account_type_regexes(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  account_type_regexes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.OptionsProto.account_type_regexes)
}
inline void OptionsProto::set_account_type_regexes(int index, const char* value, size_t size) {
  account_type_regexes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.OptionsProto.account_type_regexes)
}
inline ::std::string* OptionsProto::add_account_type_regexes() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.OptionsProto.account_type_regexes)
  return account_type_regexes_.Add();
}
inline void OptionsProto::add_account_type_regexes(const ::std::string& value) {
  account_type_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.account_type_regexes)
}
#if LANG_CXX11
inline void OptionsProto::add_account_type_regexes(::std::string&& value) {
  account_type_regexes_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.account_type_regexes)
}
#endif
inline void OptionsProto::add_account_type_regexes(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  account_type_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.OptionsProto.account_type_regexes)
}
inline void OptionsProto::add_account_type_regexes(const char* value, size_t size) {
  account_type_regexes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.OptionsProto.account_type_regexes)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
OptionsProto::account_type_regexes() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.OptionsProto.account_type_regexes)
  return account_type_regexes_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
OptionsProto::mutable_account_type_regexes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.OptionsProto.account_type_regexes)
  return &account_type_regexes_;
}

// repeated string start_name_regexes = 9;
inline int OptionsProto::start_name_regexes_size() const {
  return start_name_regexes_.size();
}
inline void OptionsProto::clear_start_name_regexes() {
  start_name_regexes_.Clear();
}
inline const ::std::string& OptionsProto::start_name_regexes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.start_name_regexes)
  return start_name_regexes_.Get(index);
}
inline ::std::string* OptionsProto::mutable_start_name_regexes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OptionsProto.start_name_regexes)
  return start_name_regexes_.Mutable(index);
}
inline void OptionsProto::set_start_name_regexes(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.start_name_regexes)
  start_name_regexes_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void OptionsProto::set_start_name_regexes(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.start_name_regexes)
  start_name_regexes_.Mutable(index)->assign(std::move(value));
}
#endif
inline void OptionsProto::set_start_name_regexes(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  start_name_regexes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.OptionsProto.start_name_regexes)
}
inline void OptionsProto::set_start_name_regexes(int index, const char* value, size_t size) {
  start_name_regexes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.OptionsProto.start_name_regexes)
}
inline ::std::string* OptionsProto::add_start_name_regexes() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.OptionsProto.start_name_regexes)
  return start_name_regexes_.Add();
}
inline void OptionsProto::add_start_name_regexes(const ::std::string& value) {
  start_name_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.start_name_regexes)
}
#if LANG_CXX11
inline void OptionsProto::add_start_name_regexes(::std::string&& value) {
  start_name_regexes_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.start_name_regexes)
}
#endif
inline void OptionsProto::add_start_name_regexes(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  start_name_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.OptionsProto.start_name_regexes)
}
inline void OptionsProto::add_start_name_regexes(const char* value, size_t size) {
  start_name_regexes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.OptionsProto.start_name_regexes)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
OptionsProto::start_name_regexes() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.OptionsProto.start_name_regexes)
  return start_name_regexes_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
OptionsProto::mutable_start_name_regexes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.OptionsProto.start_name_regexes)
  return &start_name_regexes_;
}

// repeated string trim_name_regexes = 10;
inline int OptionsProto::trim_name_regexes_size() const {
  return trim_name_regexes_.size();
}
inline void OptionsProto::clear_trim_name_regexes() {
  trim_name_regexes_.Clear();
}
inline const ::std::string& OptionsProto::trim_name_regexes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.trim_name_regexes)
  return trim_name_regexes_.Get(index);
}
inline ::std::string* OptionsProto::mutable_trim_name_regexes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OptionsProto.trim_name_regexes)
  return trim_name_regexes_.Mutable(index);
}
inline void OptionsProto::set_trim_name_regexes(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.trim_name_regexes)
  trim_name_regexes_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void OptionsProto::set_trim_name_regexes(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.trim_name_regexes)
  trim_name_regexes_.Mutable(index)->assign(std::move(value));
}
#endif
inline void OptionsProto::set_trim_name_regexes(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  trim_name_regexes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.OptionsProto.trim_name_regexes)
}
inline void OptionsProto::set_trim_name_regexes(int index, const char* value, size_t size) {
  trim_name_regexes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.OptionsProto.trim_name_regexes)
}
inline ::std::string* OptionsProto::add_trim_name_regexes() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.OptionsProto.trim_name_regexes)
  return trim_name_regexes_.Add();
}
inline void OptionsProto::add_trim_name_regexes(const ::std::string& value) {
  trim_name_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.trim_name_regexes)
}
#if LANG_CXX11
inline void OptionsProto::add_trim_name_regexes(::std::string&& value) {
  trim_name_regexes_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.trim_name_regexes)
}
#endif
inline void OptionsProto::add_trim_name_regexes(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  trim_name_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.OptionsProto.trim_name_regexes)
}
inline void OptionsProto::add_trim_name_regexes(const char* value, size_t size) {
  trim_name_regexes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.OptionsProto.trim_name_regexes)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
OptionsProto::trim_name_regexes() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.OptionsProto.trim_name_regexes)
  return trim_name_regexes_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
OptionsProto::mutable_trim_name_regexes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.OptionsProto.trim_name_regexes)
  return &trim_name_regexes_;
}

// repeated string show_name_regexes = 11;
inline int OptionsProto::show_name_regexes_size() const {
  return show_name_regexes_.size();
}
inline void OptionsProto::clear_show_name_regexes() {
  show_name_regexes_.Clear();
}
inline const ::std::string& OptionsProto::show_name_regexes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.show_name_regexes)
  return show_name_regexes_.Get(index);
}
inline ::std::string* OptionsProto::mutable_show_name_regexes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OptionsProto.show_name_regexes)
  return show_name_regexes_.Mutable(index);
}
inline void OptionsProto::set_show_name_regexes(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.show_name_regexes)
  show_name_regexes_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void OptionsProto::set_show_name_regexes(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.show_name_regexes)
  show_name_regexes_.Mutable(index)->assign(std::move(value));
}
#endif
inline void OptionsProto::set_show_name_regexes(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  show_name_regexes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.OptionsProto.show_name_regexes)
}
inline void OptionsProto::set_show_name_regexes(int index, const char* value, size_t size) {
  show_name_regexes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.OptionsProto.show_name_regexes)
}
inline ::std::string* OptionsProto::add_show_name_regexes() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.OptionsProto.show_name_regexes)
  return show_name_regexes_.Add();
}
inline void OptionsProto::add_show_name_regexes(const ::std::string& value) {
  show_name_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.show_name_regexes)
}
#if LANG_CXX11
inline void OptionsProto::add_show_name_regexes(::std::string&& value) {
  show_name_regexes_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.show_name_regexes)
}
#endif
inline void OptionsProto::add_show_name_regexes(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  show_name_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.OptionsProto.show_name_regexes)
}
inline void OptionsProto::add_show_name_regexes(const char* value, size_t size) {
  show_name_regexes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.OptionsProto.show_name_regexes)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
OptionsProto::show_name_regexes() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.OptionsProto.show_name_regexes)
  return show_name_regexes_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
OptionsProto::mutable_show_name_regexes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.OptionsProto.show_name_regexes)
  return &show_name_regexes_;
}

// repeated string hide_name_regexes = 12;
inline int OptionsProto::hide_name_regexes_size() const {
  return hide_name_regexes_.size();
}
inline void OptionsProto::clear_hide_name_regexes() {
  hide_name_regexes_.Clear();
}
inline const ::std::string& OptionsProto::hide_name_regexes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.hide_name_regexes)
  return hide_name_regexes_.Get(index);
}
inline ::std::string* OptionsProto::mutable_hide_name_regexes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OptionsProto.hide_name_regexes)
  return hide_name_regexes_.Mutable(index);
}
inline void OptionsProto::set_hide_name_regexes(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.hide_name_regexes)
  hide_name_regexes_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void OptionsProto::set_hide_name_regexes(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.hide_name_regexes)
  hide_name_regexes_.Mutable(index)->assign(std::move(value));
}
#endif
inline void OptionsProto::set_hide_name_regexes(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  hide_name_regexes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.OptionsProto.hide_name_regexes)
}
inline void OptionsProto::set_hide_name_regexes(int index, const char* value, size_t size) {
  hide_name_regexes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.OptionsProto.hide_name_regexes)
}
inline ::std::string* OptionsProto::add_hide_name_regexes() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.OptionsProto.hide_name_regexes)
  return hide_name_regexes_.Add();
}
inline void OptionsProto::add_hide_name_regexes(const ::std::string& value) {
  hide_name_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.hide_name_regexes)
}
#if LANG_CXX11
inline void OptionsProto::add_hide_name_regexes(::std::string&& value) {
  hide_name_regexes_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.hide_name_regexes)
}
#endif
inline void OptionsProto::add_hide_name_regexes(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  hide_name_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.OptionsProto.hide_name_regexes)
}
inline void OptionsProto::add_hide_name_regexes(const char* value, size_t size) {
  hide_name_regexes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.OptionsProto.hide_name_regexes)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
OptionsProto::hide_name_regexes() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.OptionsProto.hide_name_regexes)
  return hide_name_regexes_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
OptionsProto::mutable_hide_name_regexes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.OptionsProto.hide_name_regexes)
  return &hide_name_regexes_;
}

// bool account_displayed_op_only = 13;
inline void OptionsProto::clear_account_displayed_op_only() {
  account_displayed_op_only_ = false;
}
inline bool OptionsProto::account_displayed_op_only() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.account_displayed_op_only)
  return account_displayed_op_only_;
}
inline void OptionsProto::set_account_displayed_op_only(bool value) {
  
  account_displayed_op_only_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.account_displayed_op_only)
}

// repeated string select = 14;
inline int OptionsProto::select_size() const {
  return select_.size();
}
inline void OptionsProto::clear_select() {
  select_.Clear();
}
inline const ::std::string& OptionsProto::select(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.select)
  return select_.Get(index);
}
inline ::std::string* OptionsProto::mutable_select(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OptionsProto.select)
  return select_.Mutable(index);
}
inline void OptionsProto::set_select(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.select)
  select_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void OptionsProto::set_select(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.select)
  select_.Mutable(index)->assign(std::move(value));
}
#endif
inline void OptionsProto::set_select(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  select_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.OptionsProto.select)
}
inline void OptionsProto::set_select(int index, const char* value, size_t size) {
  select_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.OptionsProto.select)
}
inline ::std::string* OptionsProto::add_select() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.OptionsProto.select)
  return select_.Add();
}
inline void OptionsProto::add_select(const ::std::string& value) {
  select_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.select)
}
#if LANG_CXX11
inline void OptionsProto::add_select(::std::string&& value) {
  select_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.select)
}
#endif
inline void OptionsProto::add_select(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  select_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.OptionsProto.select)
}
inline void OptionsProto::add_select(const char* value, size_t size) {
  select_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.OptionsProto.select)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
OptionsProto::select() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.OptionsProto.select)
  return select_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
OptionsProto::mutable_select() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.OptionsProto.select)
  return &select_;
}

// string output = 15;
inline void OptionsProto::clear_output() {
  output_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionsProto::output() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.output)
  return output_.GetNoArena();
}
inline void OptionsProto::set_output(const ::std::string& value) {
  
  output_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.output)
}
#if LANG_CXX11
inline void OptionsProto::set_output(::std::string&& value) {
  
  output_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tfprof.OptionsProto.output)
}
#endif
inline void OptionsProto::set_output(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  output_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.OptionsProto.output)
}
inline void OptionsProto::set_output(const char* value, size_t size) {
  
  output_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.OptionsProto.output)
}
inline ::std::string* OptionsProto::mutable_output() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OptionsProto.output)
  return output_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionsProto::release_output() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.OptionsProto.output)
  
  return output_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionsProto::set_allocated_output(::std::string* output) {
  if (output != NULL) {
    
  } else {
    
  }
  output_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), output);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.OptionsProto.output)
}

// string dump_to_file = 16;
inline void OptionsProto::clear_dump_to_file() {
  dump_to_file_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionsProto::dump_to_file() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.dump_to_file)
  return dump_to_file_.GetNoArena();
}
inline void OptionsProto::set_dump_to_file(const ::std::string& value) {
  
  dump_to_file_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.dump_to_file)
}
#if LANG_CXX11
inline void OptionsProto::set_dump_to_file(::std::string&& value) {
  
  dump_to_file_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tfprof.OptionsProto.dump_to_file)
}
#endif
inline void OptionsProto::set_dump_to_file(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  dump_to_file_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.OptionsProto.dump_to_file)
}
inline void OptionsProto::set_dump_to_file(const char* value, size_t size) {
  
  dump_to_file_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.OptionsProto.dump_to_file)
}
inline ::std::string* OptionsProto::mutable_dump_to_file() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OptionsProto.dump_to_file)
  return dump_to_file_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionsProto::release_dump_to_file() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.OptionsProto.dump_to_file)
  
  return dump_to_file_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionsProto::set_allocated_dump_to_file(::std::string* dump_to_file) {
  if (dump_to_file != NULL) {
    
  } else {
    
  }
  dump_to_file_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), dump_to_file);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.OptionsProto.dump_to_file)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// AdvisorOptionsProto_CheckerOption

// map<string, string> options = 1;
inline int AdvisorOptionsProto_CheckerOption::options_size() const {
  return options_.size();
}
inline void AdvisorOptionsProto_CheckerOption::clear_options() {
  options_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::std::string >&
AdvisorOptionsProto_CheckerOption::options() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption.options)
  return options_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::std::string >*
AdvisorOptionsProto_CheckerOption::mutable_options() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption.options)
  return options_.MutableMap();
}

// -------------------------------------------------------------------

// AdvisorOptionsProto

// map<string, .tensorflow.tfprof.AdvisorOptionsProto.CheckerOption> checkers = 1;
inline int AdvisorOptionsProto::checkers_size() const {
  return checkers_.size();
}
inline void AdvisorOptionsProto::clear_checkers() {
  checkers_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption >&
AdvisorOptionsProto::checkers() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.AdvisorOptionsProto.checkers)
  return checkers_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption >*
AdvisorOptionsProto::mutable_checkers() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.AdvisorOptionsProto.checkers)
  return checkers_.MutableMap();
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tfprof
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto

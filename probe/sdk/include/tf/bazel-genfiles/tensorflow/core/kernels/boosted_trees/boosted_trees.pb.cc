// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/kernels/boosted_trees/boosted_trees.proto

#include "tensorflow/core/kernels/boosted_trees/boosted_trees.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_BucketizedSplit;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_CategoricalSplit;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_DenseSplit;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_GrowingMetadata;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_SparseVector;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_TreeMetadata_PostPruneNodeUpdate;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_Vector;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_NodeMetadata;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_Tree;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_TreeMetadata;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_Leaf;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto ::google::protobuf::internal::SCCInfo<5> scc_info_Node;
}  // namespace protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto
namespace tensorflow {
namespace boosted_trees {
class NodeDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Node>
      _instance;
  const ::tensorflow::boosted_trees::Leaf* leaf_;
  const ::tensorflow::boosted_trees::BucketizedSplit* bucketized_split_;
  const ::tensorflow::boosted_trees::CategoricalSplit* categorical_split_;
  const ::tensorflow::boosted_trees::DenseSplit* dense_split_;
} _Node_default_instance_;
class NodeMetadataDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<NodeMetadata>
      _instance;
} _NodeMetadata_default_instance_;
class LeafDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Leaf>
      _instance;
  const ::tensorflow::boosted_trees::Vector* vector_;
  const ::tensorflow::boosted_trees::SparseVector* sparse_vector_;
} _Leaf_default_instance_;
class VectorDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Vector>
      _instance;
} _Vector_default_instance_;
class SparseVectorDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SparseVector>
      _instance;
} _SparseVector_default_instance_;
class BucketizedSplitDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<BucketizedSplit>
      _instance;
} _BucketizedSplit_default_instance_;
class CategoricalSplitDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CategoricalSplit>
      _instance;
} _CategoricalSplit_default_instance_;
class DenseSplitDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<DenseSplit>
      _instance;
} _DenseSplit_default_instance_;
class TreeDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Tree>
      _instance;
} _Tree_default_instance_;
class TreeMetadata_PostPruneNodeUpdateDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TreeMetadata_PostPruneNodeUpdate>
      _instance;
} _TreeMetadata_PostPruneNodeUpdate_default_instance_;
class TreeMetadataDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TreeMetadata>
      _instance;
} _TreeMetadata_default_instance_;
class GrowingMetadataDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<GrowingMetadata>
      _instance;
} _GrowingMetadata_default_instance_;
class TreeEnsembleDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TreeEnsemble>
      _instance;
} _TreeEnsemble_default_instance_;
class DebugOutputDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<DebugOutput>
      _instance;
} _DebugOutput_default_instance_;
}  // namespace boosted_trees
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto {
static void InitDefaultsNode() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::boosted_trees::_Node_default_instance_;
    new (ptr) ::tensorflow::boosted_trees::Node();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::boosted_trees::Node::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<5> scc_info_Node =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 5, InitDefaultsNode}, {
      &protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_Leaf.base,
      &protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_BucketizedSplit.base,
      &protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_CategoricalSplit.base,
      &protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_DenseSplit.base,
      &protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_NodeMetadata.base,}};

static void InitDefaultsNodeMetadata() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::boosted_trees::_NodeMetadata_default_instance_;
    new (ptr) ::tensorflow::boosted_trees::NodeMetadata();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::boosted_trees::NodeMetadata::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_NodeMetadata =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsNodeMetadata}, {
      &protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_Leaf.base,}};

static void InitDefaultsLeaf() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::boosted_trees::_Leaf_default_instance_;
    new (ptr) ::tensorflow::boosted_trees::Leaf();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::boosted_trees::Leaf::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_Leaf =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsLeaf}, {
      &protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_Vector.base,
      &protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_SparseVector.base,}};

static void InitDefaultsVector() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::boosted_trees::_Vector_default_instance_;
    new (ptr) ::tensorflow::boosted_trees::Vector();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::boosted_trees::Vector::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_Vector =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsVector}, {}};

static void InitDefaultsSparseVector() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::boosted_trees::_SparseVector_default_instance_;
    new (ptr) ::tensorflow::boosted_trees::SparseVector();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::boosted_trees::SparseVector::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_SparseVector =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsSparseVector}, {}};

static void InitDefaultsBucketizedSplit() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::boosted_trees::_BucketizedSplit_default_instance_;
    new (ptr) ::tensorflow::boosted_trees::BucketizedSplit();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::boosted_trees::BucketizedSplit::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_BucketizedSplit =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsBucketizedSplit}, {}};

static void InitDefaultsCategoricalSplit() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::boosted_trees::_CategoricalSplit_default_instance_;
    new (ptr) ::tensorflow::boosted_trees::CategoricalSplit();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::boosted_trees::CategoricalSplit::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_CategoricalSplit =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsCategoricalSplit}, {}};

static void InitDefaultsDenseSplit() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::boosted_trees::_DenseSplit_default_instance_;
    new (ptr) ::tensorflow::boosted_trees::DenseSplit();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::boosted_trees::DenseSplit::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_DenseSplit =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsDenseSplit}, {}};

static void InitDefaultsTree() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::boosted_trees::_Tree_default_instance_;
    new (ptr) ::tensorflow::boosted_trees::Tree();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::boosted_trees::Tree::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_Tree =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsTree}, {
      &protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_Node.base,}};

static void InitDefaultsTreeMetadata_PostPruneNodeUpdate() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::boosted_trees::_TreeMetadata_PostPruneNodeUpdate_default_instance_;
    new (ptr) ::tensorflow::boosted_trees::TreeMetadata_PostPruneNodeUpdate();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::boosted_trees::TreeMetadata_PostPruneNodeUpdate::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_TreeMetadata_PostPruneNodeUpdate =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsTreeMetadata_PostPruneNodeUpdate}, {}};

static void InitDefaultsTreeMetadata() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::boosted_trees::_TreeMetadata_default_instance_;
    new (ptr) ::tensorflow::boosted_trees::TreeMetadata();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::boosted_trees::TreeMetadata::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_TreeMetadata =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsTreeMetadata}, {
      &protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_TreeMetadata_PostPruneNodeUpdate.base,}};

static void InitDefaultsGrowingMetadata() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::boosted_trees::_GrowingMetadata_default_instance_;
    new (ptr) ::tensorflow::boosted_trees::GrowingMetadata();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::boosted_trees::GrowingMetadata::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_GrowingMetadata =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsGrowingMetadata}, {}};

static void InitDefaultsTreeEnsemble() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::boosted_trees::_TreeEnsemble_default_instance_;
    new (ptr) ::tensorflow::boosted_trees::TreeEnsemble();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::boosted_trees::TreeEnsemble::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<3> scc_info_TreeEnsemble =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 3, InitDefaultsTreeEnsemble}, {
      &protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_Tree.base,
      &protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_TreeMetadata.base,
      &protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_GrowingMetadata.base,}};

static void InitDefaultsDebugOutput() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::boosted_trees::_DebugOutput_default_instance_;
    new (ptr) ::tensorflow::boosted_trees::DebugOutput();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::boosted_trees::DebugOutput::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_DebugOutput =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsDebugOutput}, {}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_Node.base);
  ::google::protobuf::internal::InitSCC(&scc_info_NodeMetadata.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Leaf.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Vector.base);
  ::google::protobuf::internal::InitSCC(&scc_info_SparseVector.base);
  ::google::protobuf::internal::InitSCC(&scc_info_BucketizedSplit.base);
  ::google::protobuf::internal::InitSCC(&scc_info_CategoricalSplit.base);
  ::google::protobuf::internal::InitSCC(&scc_info_DenseSplit.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Tree.base);
  ::google::protobuf::internal::InitSCC(&scc_info_TreeMetadata_PostPruneNodeUpdate.base);
  ::google::protobuf::internal::InitSCC(&scc_info_TreeMetadata.base);
  ::google::protobuf::internal::InitSCC(&scc_info_GrowingMetadata.base);
  ::google::protobuf::internal::InitSCC(&scc_info_TreeEnsemble.base);
  ::google::protobuf::internal::InitSCC(&scc_info_DebugOutput.base);
}

::google::protobuf::Metadata file_level_metadata[14];
const ::google::protobuf::EnumDescriptor* file_level_enum_descriptors[1];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::Node, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::Node, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  offsetof(::tensorflow::boosted_trees::NodeDefaultTypeInternal, leaf_),
  offsetof(::tensorflow::boosted_trees::NodeDefaultTypeInternal, bucketized_split_),
  offsetof(::tensorflow::boosted_trees::NodeDefaultTypeInternal, categorical_split_),
  offsetof(::tensorflow::boosted_trees::NodeDefaultTypeInternal, dense_split_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::Node, metadata_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::Node, node_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::NodeMetadata, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::NodeMetadata, gain_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::NodeMetadata, original_leaf_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::Leaf, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::Leaf, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  offsetof(::tensorflow::boosted_trees::LeafDefaultTypeInternal, vector_),
  offsetof(::tensorflow::boosted_trees::LeafDefaultTypeInternal, sparse_vector_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::Leaf, scalar_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::Leaf, leaf_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::Vector, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::Vector, value_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::SparseVector, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::SparseVector, index_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::SparseVector, value_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::BucketizedSplit, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::BucketizedSplit, feature_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::BucketizedSplit, threshold_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::BucketizedSplit, dimension_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::BucketizedSplit, default_direction_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::BucketizedSplit, left_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::BucketizedSplit, right_id_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::CategoricalSplit, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::CategoricalSplit, feature_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::CategoricalSplit, value_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::CategoricalSplit, left_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::CategoricalSplit, right_id_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::DenseSplit, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::DenseSplit, feature_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::DenseSplit, threshold_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::DenseSplit, left_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::DenseSplit, right_id_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::Tree, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::Tree, nodes_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::TreeMetadata_PostPruneNodeUpdate, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::TreeMetadata_PostPruneNodeUpdate, new_node_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::TreeMetadata_PostPruneNodeUpdate, logit_change_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::TreeMetadata, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::TreeMetadata, num_layers_grown_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::TreeMetadata, is_finalized_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::TreeMetadata, post_pruned_nodes_meta_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::GrowingMetadata, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::GrowingMetadata, num_trees_attempted_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::GrowingMetadata, num_layers_attempted_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::GrowingMetadata, last_layer_node_start_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::GrowingMetadata, last_layer_node_end_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::TreeEnsemble, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::TreeEnsemble, trees_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::TreeEnsemble, tree_weights_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::TreeEnsemble, tree_metadata_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::TreeEnsemble, growing_metadata_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::DebugOutput, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::DebugOutput, feature_ids_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::boosted_trees::DebugOutput, logits_path_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::boosted_trees::Node)},
  { 11, -1, sizeof(::tensorflow::boosted_trees::NodeMetadata)},
  { 18, -1, sizeof(::tensorflow::boosted_trees::Leaf)},
  { 27, -1, sizeof(::tensorflow::boosted_trees::Vector)},
  { 33, -1, sizeof(::tensorflow::boosted_trees::SparseVector)},
  { 40, -1, sizeof(::tensorflow::boosted_trees::BucketizedSplit)},
  { 51, -1, sizeof(::tensorflow::boosted_trees::CategoricalSplit)},
  { 60, -1, sizeof(::tensorflow::boosted_trees::DenseSplit)},
  { 69, -1, sizeof(::tensorflow::boosted_trees::Tree)},
  { 75, -1, sizeof(::tensorflow::boosted_trees::TreeMetadata_PostPruneNodeUpdate)},
  { 82, -1, sizeof(::tensorflow::boosted_trees::TreeMetadata)},
  { 90, -1, sizeof(::tensorflow::boosted_trees::GrowingMetadata)},
  { 99, -1, sizeof(::tensorflow::boosted_trees::TreeEnsemble)},
  { 108, -1, sizeof(::tensorflow::boosted_trees::DebugOutput)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::boosted_trees::_Node_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::boosted_trees::_NodeMetadata_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::boosted_trees::_Leaf_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::boosted_trees::_Vector_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::boosted_trees::_SparseVector_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::boosted_trees::_BucketizedSplit_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::boosted_trees::_CategoricalSplit_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::boosted_trees::_DenseSplit_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::boosted_trees::_Tree_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::boosted_trees::_TreeMetadata_PostPruneNodeUpdate_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::boosted_trees::_TreeMetadata_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::boosted_trees::_GrowingMetadata_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::boosted_trees::_TreeEnsemble_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::boosted_trees::_DebugOutput_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/kernels/boosted_trees/boosted_trees.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, file_level_enum_descriptors, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 14);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n9tensorflow/core/kernels/boosted_trees/"
      "boosted_trees.proto\022\030tensorflow.boosted_"
      "trees\"\306\002\n\004Node\022.\n\004leaf\030\001 \001(\0132\036.tensorflo"
      "w.boosted_trees.LeafH\000\022E\n\020bucketized_spl"
      "it\030\002 \001(\0132).tensorflow.boosted_trees.Buck"
      "etizedSplitH\000\022G\n\021categorical_split\030\003 \001(\013"
      "2*.tensorflow.boosted_trees.CategoricalS"
      "plitH\000\022;\n\013dense_split\030\004 \001(\0132$.tensorflow"
      ".boosted_trees.DenseSplitH\000\0229\n\010metadata\030"
      "\211\006 \001(\0132&.tensorflow.boosted_trees.NodeMe"
      "tadataB\006\n\004node\"S\n\014NodeMetadata\022\014\n\004gain\030\001"
      " \001(\002\0225\n\roriginal_leaf\030\002 \001(\0132\036.tensorflow"
      ".boosted_trees.Leaf\"\223\001\n\004Leaf\0222\n\006vector\030\001"
      " \001(\0132 .tensorflow.boosted_trees.VectorH\000"
      "\022\?\n\rsparse_vector\030\002 \001(\0132&.tensorflow.boo"
      "sted_trees.SparseVectorH\000\022\016\n\006scalar\030\003 \001("
      "\002B\006\n\004leaf\"\027\n\006Vector\022\r\n\005value\030\001 \003(\002\",\n\014Sp"
      "arseVector\022\r\n\005index\030\001 \003(\005\022\r\n\005value\030\002 \003(\002"
      "\"\201\002\n\017BucketizedSplit\022\022\n\nfeature_id\030\001 \001(\005"
      "\022\021\n\tthreshold\030\002 \001(\005\022\024\n\014dimension_id\030\005 \001("
      "\005\022U\n\021default_direction\030\006 \001(\0162:.tensorflo"
      "w.boosted_trees.BucketizedSplit.DefaultD"
      "irection\022\017\n\007left_id\030\003 \001(\005\022\020\n\010right_id\030\004 "
      "\001(\005\"7\n\020DefaultDirection\022\020\n\014DEFAULT_LEFT\020"
      "\000\022\021\n\rDEFAULT_RIGHT\020\001\"X\n\020CategoricalSplit"
      "\022\022\n\nfeature_id\030\001 \001(\005\022\r\n\005value\030\002 \001(\005\022\017\n\007l"
      "eft_id\030\003 \001(\005\022\020\n\010right_id\030\004 \001(\005\"V\n\nDenseS"
      "plit\022\022\n\nfeature_id\030\001 \001(\005\022\021\n\tthreshold\030\002 "
      "\001(\002\022\017\n\007left_id\030\003 \001(\005\022\020\n\010right_id\030\004 \001(\005\"5"
      "\n\004Tree\022-\n\005nodes\030\001 \003(\0132\036.tensorflow.boost"
      "ed_trees.Node\"\334\001\n\014TreeMetadata\022\030\n\020num_la"
      "yers_grown\030\002 \001(\005\022\024\n\014is_finalized\030\003 \001(\010\022Z"
      "\n\026post_pruned_nodes_meta\030\004 \003(\0132:.tensorf"
      "low.boosted_trees.TreeMetadata.PostPrune"
      "NodeUpdate\032@\n\023PostPruneNodeUpdate\022\023\n\013new"
      "_node_id\030\001 \001(\005\022\024\n\014logit_change\030\002 \001(\002\"\210\001\n"
      "\017GrowingMetadata\022\033\n\023num_trees_attempted\030"
      "\001 \001(\003\022\034\n\024num_layers_attempted\030\002 \001(\003\022\035\n\025l"
      "ast_layer_node_start\030\003 \001(\005\022\033\n\023last_layer"
      "_node_end\030\004 \001(\005\"\327\001\n\014TreeEnsemble\022-\n\005tree"
      "s\030\001 \003(\0132\036.tensorflow.boosted_trees.Tree\022"
      "\024\n\014tree_weights\030\002 \003(\002\022=\n\rtree_metadata\030\003"
      " \003(\0132&.tensorflow.boosted_trees.TreeMeta"
      "data\022C\n\020growing_metadata\030\004 \001(\0132).tensorf"
      "low.boosted_trees.GrowingMetadata\"7\n\013Deb"
      "ugOutput\022\023\n\013feature_ids\030\001 \003(\005\022\023\n\013logits_"
      "path\030\002 \003(\002B3\n\030org.tensorflow.frameworkB\022"
      "BoostedTreesProtosP\001\370\001\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 1911);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/kernels/boosted_trees/boosted_trees.proto", &protobuf_RegisterTypes);
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto
namespace tensorflow {
namespace boosted_trees {
const ::google::protobuf::EnumDescriptor* BucketizedSplit_DefaultDirection_descriptor() {
  protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::file_level_enum_descriptors[0];
}
bool BucketizedSplit_DefaultDirection_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const BucketizedSplit_DefaultDirection BucketizedSplit::DEFAULT_LEFT;
const BucketizedSplit_DefaultDirection BucketizedSplit::DEFAULT_RIGHT;
const BucketizedSplit_DefaultDirection BucketizedSplit::DefaultDirection_MIN;
const BucketizedSplit_DefaultDirection BucketizedSplit::DefaultDirection_MAX;
const int BucketizedSplit::DefaultDirection_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

// ===================================================================

void Node::InitAsDefaultInstance() {
  ::tensorflow::boosted_trees::_Node_default_instance_.leaf_ = const_cast< ::tensorflow::boosted_trees::Leaf*>(
      ::tensorflow::boosted_trees::Leaf::internal_default_instance());
  ::tensorflow::boosted_trees::_Node_default_instance_.bucketized_split_ = const_cast< ::tensorflow::boosted_trees::BucketizedSplit*>(
      ::tensorflow::boosted_trees::BucketizedSplit::internal_default_instance());
  ::tensorflow::boosted_trees::_Node_default_instance_.categorical_split_ = const_cast< ::tensorflow::boosted_trees::CategoricalSplit*>(
      ::tensorflow::boosted_trees::CategoricalSplit::internal_default_instance());
  ::tensorflow::boosted_trees::_Node_default_instance_.dense_split_ = const_cast< ::tensorflow::boosted_trees::DenseSplit*>(
      ::tensorflow::boosted_trees::DenseSplit::internal_default_instance());
  ::tensorflow::boosted_trees::_Node_default_instance_._instance.get_mutable()->metadata_ = const_cast< ::tensorflow::boosted_trees::NodeMetadata*>(
      ::tensorflow::boosted_trees::NodeMetadata::internal_default_instance());
}
void Node::set_allocated_leaf(::tensorflow::boosted_trees::Leaf* leaf) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_node();
  if (leaf) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(leaf);
    if (message_arena != submessage_arena) {
      leaf = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, leaf, submessage_arena);
    }
    set_has_leaf();
    node_.leaf_ = leaf;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.boosted_trees.Node.leaf)
}
void Node::set_allocated_bucketized_split(::tensorflow::boosted_trees::BucketizedSplit* bucketized_split) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_node();
  if (bucketized_split) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(bucketized_split);
    if (message_arena != submessage_arena) {
      bucketized_split = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, bucketized_split, submessage_arena);
    }
    set_has_bucketized_split();
    node_.bucketized_split_ = bucketized_split;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.boosted_trees.Node.bucketized_split)
}
void Node::set_allocated_categorical_split(::tensorflow::boosted_trees::CategoricalSplit* categorical_split) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_node();
  if (categorical_split) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(categorical_split);
    if (message_arena != submessage_arena) {
      categorical_split = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, categorical_split, submessage_arena);
    }
    set_has_categorical_split();
    node_.categorical_split_ = categorical_split;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.boosted_trees.Node.categorical_split)
}
void Node::set_allocated_dense_split(::tensorflow::boosted_trees::DenseSplit* dense_split) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_node();
  if (dense_split) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(dense_split);
    if (message_arena != submessage_arena) {
      dense_split = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, dense_split, submessage_arena);
    }
    set_has_dense_split();
    node_.dense_split_ = dense_split;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.boosted_trees.Node.dense_split)
}
void Node::unsafe_arena_set_allocated_metadata(
    ::tensorflow::boosted_trees::NodeMetadata* metadata) {
  if (GetArenaNoVirtual() == NULL) {
    delete metadata_;
  }
  metadata_ = metadata;
  if (metadata) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.boosted_trees.Node.metadata)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Node::kLeafFieldNumber;
const int Node::kBucketizedSplitFieldNumber;
const int Node::kCategoricalSplitFieldNumber;
const int Node::kDenseSplitFieldNumber;
const int Node::kMetadataFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Node::Node()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_Node.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.boosted_trees.Node)
}
Node::Node(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_Node.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.boosted_trees.Node)
}
Node::Node(const Node& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_metadata()) {
    metadata_ = new ::tensorflow::boosted_trees::NodeMetadata(*from.metadata_);
  } else {
    metadata_ = NULL;
  }
  clear_has_node();
  switch (from.node_case()) {
    case kLeaf: {
      mutable_leaf()->::tensorflow::boosted_trees::Leaf::MergeFrom(from.leaf());
      break;
    }
    case kBucketizedSplit: {
      mutable_bucketized_split()->::tensorflow::boosted_trees::BucketizedSplit::MergeFrom(from.bucketized_split());
      break;
    }
    case kCategoricalSplit: {
      mutable_categorical_split()->::tensorflow::boosted_trees::CategoricalSplit::MergeFrom(from.categorical_split());
      break;
    }
    case kDenseSplit: {
      mutable_dense_split()->::tensorflow::boosted_trees::DenseSplit::MergeFrom(from.dense_split());
      break;
    }
    case NODE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.boosted_trees.Node)
}

void Node::SharedCtor() {
  metadata_ = NULL;
  clear_has_node();
}

Node::~Node() {
  // @@protoc_insertion_point(destructor:tensorflow.boosted_trees.Node)
  SharedDtor();
}

void Node::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (this != internal_default_instance()) delete metadata_;
  if (has_node()) {
    clear_node();
  }
}

void Node::ArenaDtor(void* object) {
  Node* _this = reinterpret_cast< Node* >(object);
  (void)_this;
}
void Node::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void Node::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Node::descriptor() {
  ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Node& Node::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_Node.base);
  return *internal_default_instance();
}


void Node::clear_node() {
// @@protoc_insertion_point(one_of_clear_start:tensorflow.boosted_trees.Node)
  switch (node_case()) {
    case kLeaf: {
      if (GetArenaNoVirtual() == NULL) {
        delete node_.leaf_;
      }
      break;
    }
    case kBucketizedSplit: {
      if (GetArenaNoVirtual() == NULL) {
        delete node_.bucketized_split_;
      }
      break;
    }
    case kCategoricalSplit: {
      if (GetArenaNoVirtual() == NULL) {
        delete node_.categorical_split_;
      }
      break;
    }
    case kDenseSplit: {
      if (GetArenaNoVirtual() == NULL) {
        delete node_.dense_split_;
      }
      break;
    }
    case NODE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = NODE_NOT_SET;
}


void Node::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.boosted_trees.Node)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && metadata_ != NULL) {
    delete metadata_;
  }
  metadata_ = NULL;
  clear_node();
  _internal_metadata_.Clear();
}

bool Node::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.boosted_trees.Node)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(16383u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.boosted_trees.Leaf leaf = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_leaf()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.boosted_trees.BucketizedSplit bucketized_split = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_bucketized_split()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.boosted_trees.CategoricalSplit categorical_split = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_categorical_split()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.boosted_trees.DenseSplit dense_split = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_dense_split()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.boosted_trees.NodeMetadata metadata = 777;
      case 777: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(74u /* 6218 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_metadata()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.boosted_trees.Node)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.boosted_trees.Node)
  return false;
#undef DO_
}

void Node::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.boosted_trees.Node)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.boosted_trees.Leaf leaf = 1;
  if (has_leaf()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->_internal_leaf(), output);
  }

  // .tensorflow.boosted_trees.BucketizedSplit bucketized_split = 2;
  if (has_bucketized_split()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_bucketized_split(), output);
  }

  // .tensorflow.boosted_trees.CategoricalSplit categorical_split = 3;
  if (has_categorical_split()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->_internal_categorical_split(), output);
  }

  // .tensorflow.boosted_trees.DenseSplit dense_split = 4;
  if (has_dense_split()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->_internal_dense_split(), output);
  }

  // .tensorflow.boosted_trees.NodeMetadata metadata = 777;
  if (this->has_metadata()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      777, this->_internal_metadata(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.boosted_trees.Node)
}

::google::protobuf::uint8* Node::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.boosted_trees.Node)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.boosted_trees.Leaf leaf = 1;
  if (has_leaf()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->_internal_leaf(), deterministic, target);
  }

  // .tensorflow.boosted_trees.BucketizedSplit bucketized_split = 2;
  if (has_bucketized_split()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_bucketized_split(), deterministic, target);
  }

  // .tensorflow.boosted_trees.CategoricalSplit categorical_split = 3;
  if (has_categorical_split()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->_internal_categorical_split(), deterministic, target);
  }

  // .tensorflow.boosted_trees.DenseSplit dense_split = 4;
  if (has_dense_split()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->_internal_dense_split(), deterministic, target);
  }

  // .tensorflow.boosted_trees.NodeMetadata metadata = 777;
  if (this->has_metadata()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        777, this->_internal_metadata(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.boosted_trees.Node)
  return target;
}

size_t Node::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.boosted_trees.Node)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .tensorflow.boosted_trees.NodeMetadata metadata = 777;
  if (this->has_metadata()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *metadata_);
  }

  switch (node_case()) {
    // .tensorflow.boosted_trees.Leaf leaf = 1;
    case kLeaf: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *node_.leaf_);
      break;
    }
    // .tensorflow.boosted_trees.BucketizedSplit bucketized_split = 2;
    case kBucketizedSplit: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *node_.bucketized_split_);
      break;
    }
    // .tensorflow.boosted_trees.CategoricalSplit categorical_split = 3;
    case kCategoricalSplit: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *node_.categorical_split_);
      break;
    }
    // .tensorflow.boosted_trees.DenseSplit dense_split = 4;
    case kDenseSplit: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *node_.dense_split_);
      break;
    }
    case NODE_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Node::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.boosted_trees.Node)
  GOOGLE_DCHECK_NE(&from, this);
  const Node* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Node>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.boosted_trees.Node)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.boosted_trees.Node)
    MergeFrom(*source);
  }
}

void Node::MergeFrom(const Node& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.boosted_trees.Node)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_metadata()) {
    mutable_metadata()->::tensorflow::boosted_trees::NodeMetadata::MergeFrom(from.metadata());
  }
  switch (from.node_case()) {
    case kLeaf: {
      mutable_leaf()->::tensorflow::boosted_trees::Leaf::MergeFrom(from.leaf());
      break;
    }
    case kBucketizedSplit: {
      mutable_bucketized_split()->::tensorflow::boosted_trees::BucketizedSplit::MergeFrom(from.bucketized_split());
      break;
    }
    case kCategoricalSplit: {
      mutable_categorical_split()->::tensorflow::boosted_trees::CategoricalSplit::MergeFrom(from.categorical_split());
      break;
    }
    case kDenseSplit: {
      mutable_dense_split()->::tensorflow::boosted_trees::DenseSplit::MergeFrom(from.dense_split());
      break;
    }
    case NODE_NOT_SET: {
      break;
    }
  }
}

void Node::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.boosted_trees.Node)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Node::CopyFrom(const Node& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.boosted_trees.Node)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Node::IsInitialized() const {
  return true;
}

void Node::Swap(Node* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    Node* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void Node::UnsafeArenaSwap(Node* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void Node::InternalSwap(Node* other) {
  using std::swap;
  swap(metadata_, other->metadata_);
  swap(node_, other->node_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Node::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void NodeMetadata::InitAsDefaultInstance() {
  ::tensorflow::boosted_trees::_NodeMetadata_default_instance_._instance.get_mutable()->original_leaf_ = const_cast< ::tensorflow::boosted_trees::Leaf*>(
      ::tensorflow::boosted_trees::Leaf::internal_default_instance());
}
void NodeMetadata::unsafe_arena_set_allocated_original_leaf(
    ::tensorflow::boosted_trees::Leaf* original_leaf) {
  if (GetArenaNoVirtual() == NULL) {
    delete original_leaf_;
  }
  original_leaf_ = original_leaf;
  if (original_leaf) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.boosted_trees.NodeMetadata.original_leaf)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int NodeMetadata::kGainFieldNumber;
const int NodeMetadata::kOriginalLeafFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

NodeMetadata::NodeMetadata()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_NodeMetadata.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.boosted_trees.NodeMetadata)
}
NodeMetadata::NodeMetadata(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_NodeMetadata.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.boosted_trees.NodeMetadata)
}
NodeMetadata::NodeMetadata(const NodeMetadata& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_original_leaf()) {
    original_leaf_ = new ::tensorflow::boosted_trees::Leaf(*from.original_leaf_);
  } else {
    original_leaf_ = NULL;
  }
  gain_ = from.gain_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.boosted_trees.NodeMetadata)
}

void NodeMetadata::SharedCtor() {
  ::memset(&original_leaf_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&gain_) -
      reinterpret_cast<char*>(&original_leaf_)) + sizeof(gain_));
}

NodeMetadata::~NodeMetadata() {
  // @@protoc_insertion_point(destructor:tensorflow.boosted_trees.NodeMetadata)
  SharedDtor();
}

void NodeMetadata::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (this != internal_default_instance()) delete original_leaf_;
}

void NodeMetadata::ArenaDtor(void* object) {
  NodeMetadata* _this = reinterpret_cast< NodeMetadata* >(object);
  (void)_this;
}
void NodeMetadata::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void NodeMetadata::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* NodeMetadata::descriptor() {
  ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const NodeMetadata& NodeMetadata::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_NodeMetadata.base);
  return *internal_default_instance();
}


void NodeMetadata::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.boosted_trees.NodeMetadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && original_leaf_ != NULL) {
    delete original_leaf_;
  }
  original_leaf_ = NULL;
  gain_ = 0;
  _internal_metadata_.Clear();
}

bool NodeMetadata::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.boosted_trees.NodeMetadata)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // float gain = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(13u /* 13 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &gain_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.boosted_trees.Leaf original_leaf = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_original_leaf()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.boosted_trees.NodeMetadata)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.boosted_trees.NodeMetadata)
  return false;
#undef DO_
}

void NodeMetadata::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.boosted_trees.NodeMetadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float gain = 1;
  if (this->gain() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(1, this->gain(), output);
  }

  // .tensorflow.boosted_trees.Leaf original_leaf = 2;
  if (this->has_original_leaf()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_original_leaf(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.boosted_trees.NodeMetadata)
}

::google::protobuf::uint8* NodeMetadata::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.boosted_trees.NodeMetadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float gain = 1;
  if (this->gain() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(1, this->gain(), target);
  }

  // .tensorflow.boosted_trees.Leaf original_leaf = 2;
  if (this->has_original_leaf()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_original_leaf(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.boosted_trees.NodeMetadata)
  return target;
}

size_t NodeMetadata::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.boosted_trees.NodeMetadata)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .tensorflow.boosted_trees.Leaf original_leaf = 2;
  if (this->has_original_leaf()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *original_leaf_);
  }

  // float gain = 1;
  if (this->gain() != 0) {
    total_size += 1 + 4;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void NodeMetadata::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.boosted_trees.NodeMetadata)
  GOOGLE_DCHECK_NE(&from, this);
  const NodeMetadata* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const NodeMetadata>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.boosted_trees.NodeMetadata)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.boosted_trees.NodeMetadata)
    MergeFrom(*source);
  }
}

void NodeMetadata::MergeFrom(const NodeMetadata& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.boosted_trees.NodeMetadata)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_original_leaf()) {
    mutable_original_leaf()->::tensorflow::boosted_trees::Leaf::MergeFrom(from.original_leaf());
  }
  if (from.gain() != 0) {
    set_gain(from.gain());
  }
}

void NodeMetadata::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.boosted_trees.NodeMetadata)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void NodeMetadata::CopyFrom(const NodeMetadata& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.boosted_trees.NodeMetadata)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool NodeMetadata::IsInitialized() const {
  return true;
}

void NodeMetadata::Swap(NodeMetadata* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    NodeMetadata* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void NodeMetadata::UnsafeArenaSwap(NodeMetadata* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void NodeMetadata::InternalSwap(NodeMetadata* other) {
  using std::swap;
  swap(original_leaf_, other->original_leaf_);
  swap(gain_, other->gain_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata NodeMetadata::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Leaf::InitAsDefaultInstance() {
  ::tensorflow::boosted_trees::_Leaf_default_instance_.vector_ = const_cast< ::tensorflow::boosted_trees::Vector*>(
      ::tensorflow::boosted_trees::Vector::internal_default_instance());
  ::tensorflow::boosted_trees::_Leaf_default_instance_.sparse_vector_ = const_cast< ::tensorflow::boosted_trees::SparseVector*>(
      ::tensorflow::boosted_trees::SparseVector::internal_default_instance());
}
void Leaf::set_allocated_vector(::tensorflow::boosted_trees::Vector* vector) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_leaf();
  if (vector) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(vector);
    if (message_arena != submessage_arena) {
      vector = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, vector, submessage_arena);
    }
    set_has_vector();
    leaf_.vector_ = vector;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.boosted_trees.Leaf.vector)
}
void Leaf::set_allocated_sparse_vector(::tensorflow::boosted_trees::SparseVector* sparse_vector) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_leaf();
  if (sparse_vector) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(sparse_vector);
    if (message_arena != submessage_arena) {
      sparse_vector = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, sparse_vector, submessage_arena);
    }
    set_has_sparse_vector();
    leaf_.sparse_vector_ = sparse_vector;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.boosted_trees.Leaf.sparse_vector)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Leaf::kVectorFieldNumber;
const int Leaf::kSparseVectorFieldNumber;
const int Leaf::kScalarFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Leaf::Leaf()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_Leaf.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.boosted_trees.Leaf)
}
Leaf::Leaf(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_Leaf.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.boosted_trees.Leaf)
}
Leaf::Leaf(const Leaf& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  scalar_ = from.scalar_;
  clear_has_leaf();
  switch (from.leaf_case()) {
    case kVector: {
      mutable_vector()->::tensorflow::boosted_trees::Vector::MergeFrom(from.vector());
      break;
    }
    case kSparseVector: {
      mutable_sparse_vector()->::tensorflow::boosted_trees::SparseVector::MergeFrom(from.sparse_vector());
      break;
    }
    case LEAF_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.boosted_trees.Leaf)
}

void Leaf::SharedCtor() {
  scalar_ = 0;
  clear_has_leaf();
}

Leaf::~Leaf() {
  // @@protoc_insertion_point(destructor:tensorflow.boosted_trees.Leaf)
  SharedDtor();
}

void Leaf::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (has_leaf()) {
    clear_leaf();
  }
}

void Leaf::ArenaDtor(void* object) {
  Leaf* _this = reinterpret_cast< Leaf* >(object);
  (void)_this;
}
void Leaf::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void Leaf::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Leaf::descriptor() {
  ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Leaf& Leaf::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_Leaf.base);
  return *internal_default_instance();
}


void Leaf::clear_leaf() {
// @@protoc_insertion_point(one_of_clear_start:tensorflow.boosted_trees.Leaf)
  switch (leaf_case()) {
    case kVector: {
      if (GetArenaNoVirtual() == NULL) {
        delete leaf_.vector_;
      }
      break;
    }
    case kSparseVector: {
      if (GetArenaNoVirtual() == NULL) {
        delete leaf_.sparse_vector_;
      }
      break;
    }
    case LEAF_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = LEAF_NOT_SET;
}


void Leaf::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.boosted_trees.Leaf)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  scalar_ = 0;
  clear_leaf();
  _internal_metadata_.Clear();
}

bool Leaf::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.boosted_trees.Leaf)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.boosted_trees.Vector vector = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_vector()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.boosted_trees.SparseVector sparse_vector = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_sparse_vector()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float scalar = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(29u /* 29 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &scalar_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.boosted_trees.Leaf)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.boosted_trees.Leaf)
  return false;
#undef DO_
}

void Leaf::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.boosted_trees.Leaf)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.boosted_trees.Vector vector = 1;
  if (has_vector()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->_internal_vector(), output);
  }

  // .tensorflow.boosted_trees.SparseVector sparse_vector = 2;
  if (has_sparse_vector()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_sparse_vector(), output);
  }

  // float scalar = 3;
  if (this->scalar() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(3, this->scalar(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.boosted_trees.Leaf)
}

::google::protobuf::uint8* Leaf::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.boosted_trees.Leaf)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.boosted_trees.Vector vector = 1;
  if (has_vector()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->_internal_vector(), deterministic, target);
  }

  // .tensorflow.boosted_trees.SparseVector sparse_vector = 2;
  if (has_sparse_vector()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_sparse_vector(), deterministic, target);
  }

  // float scalar = 3;
  if (this->scalar() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(3, this->scalar(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.boosted_trees.Leaf)
  return target;
}

size_t Leaf::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.boosted_trees.Leaf)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // float scalar = 3;
  if (this->scalar() != 0) {
    total_size += 1 + 4;
  }

  switch (leaf_case()) {
    // .tensorflow.boosted_trees.Vector vector = 1;
    case kVector: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *leaf_.vector_);
      break;
    }
    // .tensorflow.boosted_trees.SparseVector sparse_vector = 2;
    case kSparseVector: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *leaf_.sparse_vector_);
      break;
    }
    case LEAF_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Leaf::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.boosted_trees.Leaf)
  GOOGLE_DCHECK_NE(&from, this);
  const Leaf* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Leaf>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.boosted_trees.Leaf)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.boosted_trees.Leaf)
    MergeFrom(*source);
  }
}

void Leaf::MergeFrom(const Leaf& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.boosted_trees.Leaf)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.scalar() != 0) {
    set_scalar(from.scalar());
  }
  switch (from.leaf_case()) {
    case kVector: {
      mutable_vector()->::tensorflow::boosted_trees::Vector::MergeFrom(from.vector());
      break;
    }
    case kSparseVector: {
      mutable_sparse_vector()->::tensorflow::boosted_trees::SparseVector::MergeFrom(from.sparse_vector());
      break;
    }
    case LEAF_NOT_SET: {
      break;
    }
  }
}

void Leaf::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.boosted_trees.Leaf)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Leaf::CopyFrom(const Leaf& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.boosted_trees.Leaf)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Leaf::IsInitialized() const {
  return true;
}

void Leaf::Swap(Leaf* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    Leaf* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void Leaf::UnsafeArenaSwap(Leaf* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void Leaf::InternalSwap(Leaf* other) {
  using std::swap;
  swap(scalar_, other->scalar_);
  swap(leaf_, other->leaf_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Leaf::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Vector::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Vector::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Vector::Vector()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_Vector.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.boosted_trees.Vector)
}
Vector::Vector(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  value_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_Vector.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.boosted_trees.Vector)
}
Vector::Vector(const Vector& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      value_(from.value_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.boosted_trees.Vector)
}

void Vector::SharedCtor() {
}

Vector::~Vector() {
  // @@protoc_insertion_point(destructor:tensorflow.boosted_trees.Vector)
  SharedDtor();
}

void Vector::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void Vector::ArenaDtor(void* object) {
  Vector* _this = reinterpret_cast< Vector* >(object);
  (void)_this;
}
void Vector::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void Vector::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Vector::descriptor() {
  ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Vector& Vector::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_Vector.base);
  return *internal_default_instance();
}


void Vector::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.boosted_trees.Vector)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  value_.Clear();
  _internal_metadata_.Clear();
}

bool Vector::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.boosted_trees.Vector)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated float value = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, this->mutable_value())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(13u /* 13 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 1, 10u, input, this->mutable_value())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.boosted_trees.Vector)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.boosted_trees.Vector)
  return false;
#undef DO_
}

void Vector::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.boosted_trees.Vector)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated float value = 1;
  if (this->value_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(1, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _value_cached_byte_size_));
    ::google::protobuf::internal::WireFormatLite::WriteFloatArray(
      this->value().data(), this->value_size(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.boosted_trees.Vector)
}

::google::protobuf::uint8* Vector::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.boosted_trees.Vector)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated float value = 1;
  if (this->value_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      1,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _value_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteFloatNoTagToArray(this->value_, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.boosted_trees.Vector)
  return target;
}

size_t Vector::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.boosted_trees.Vector)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated float value = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->value_size());
    size_t data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _value_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Vector::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.boosted_trees.Vector)
  GOOGLE_DCHECK_NE(&from, this);
  const Vector* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Vector>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.boosted_trees.Vector)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.boosted_trees.Vector)
    MergeFrom(*source);
  }
}

void Vector::MergeFrom(const Vector& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.boosted_trees.Vector)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  value_.MergeFrom(from.value_);
}

void Vector::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.boosted_trees.Vector)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Vector::CopyFrom(const Vector& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.boosted_trees.Vector)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Vector::IsInitialized() const {
  return true;
}

void Vector::Swap(Vector* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    Vector* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void Vector::UnsafeArenaSwap(Vector* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void Vector::InternalSwap(Vector* other) {
  using std::swap;
  value_.InternalSwap(&other->value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Vector::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void SparseVector::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SparseVector::kIndexFieldNumber;
const int SparseVector::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SparseVector::SparseVector()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_SparseVector.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.boosted_trees.SparseVector)
}
SparseVector::SparseVector(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  index_(arena),
  value_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_SparseVector.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.boosted_trees.SparseVector)
}
SparseVector::SparseVector(const SparseVector& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      index_(from.index_),
      value_(from.value_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.boosted_trees.SparseVector)
}

void SparseVector::SharedCtor() {
}

SparseVector::~SparseVector() {
  // @@protoc_insertion_point(destructor:tensorflow.boosted_trees.SparseVector)
  SharedDtor();
}

void SparseVector::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void SparseVector::ArenaDtor(void* object) {
  SparseVector* _this = reinterpret_cast< SparseVector* >(object);
  (void)_this;
}
void SparseVector::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void SparseVector::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* SparseVector::descriptor() {
  ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const SparseVector& SparseVector::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_SparseVector.base);
  return *internal_default_instance();
}


void SparseVector::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.boosted_trees.SparseVector)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  index_.Clear();
  value_.Clear();
  _internal_metadata_.Clear();
}

bool SparseVector::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.boosted_trees.SparseVector)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated int32 index = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_index())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 1, 10u, input, this->mutable_index())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated float value = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, this->mutable_value())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(21u /* 21 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 1, 18u, input, this->mutable_value())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.boosted_trees.SparseVector)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.boosted_trees.SparseVector)
  return false;
#undef DO_
}

void SparseVector::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.boosted_trees.SparseVector)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated int32 index = 1;
  if (this->index_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(1, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _index_cached_byte_size_));
  }
  for (int i = 0, n = this->index_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32NoTag(
      this->index(i), output);
  }

  // repeated float value = 2;
  if (this->value_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(2, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _value_cached_byte_size_));
    ::google::protobuf::internal::WireFormatLite::WriteFloatArray(
      this->value().data(), this->value_size(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.boosted_trees.SparseVector)
}

::google::protobuf::uint8* SparseVector::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.boosted_trees.SparseVector)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated int32 index = 1;
  if (this->index_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      1,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _index_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt32NoTagToArray(this->index_, target);
  }

  // repeated float value = 2;
  if (this->value_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      2,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _value_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteFloatNoTagToArray(this->value_, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.boosted_trees.SparseVector)
  return target;
}

size_t SparseVector::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.boosted_trees.SparseVector)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated int32 index = 1;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int32Size(this->index_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _index_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated float value = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->value_size());
    size_t data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _value_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SparseVector::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.boosted_trees.SparseVector)
  GOOGLE_DCHECK_NE(&from, this);
  const SparseVector* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SparseVector>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.boosted_trees.SparseVector)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.boosted_trees.SparseVector)
    MergeFrom(*source);
  }
}

void SparseVector::MergeFrom(const SparseVector& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.boosted_trees.SparseVector)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  index_.MergeFrom(from.index_);
  value_.MergeFrom(from.value_);
}

void SparseVector::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.boosted_trees.SparseVector)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SparseVector::CopyFrom(const SparseVector& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.boosted_trees.SparseVector)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SparseVector::IsInitialized() const {
  return true;
}

void SparseVector::Swap(SparseVector* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    SparseVector* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void SparseVector::UnsafeArenaSwap(SparseVector* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void SparseVector::InternalSwap(SparseVector* other) {
  using std::swap;
  index_.InternalSwap(&other->index_);
  value_.InternalSwap(&other->value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata SparseVector::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void BucketizedSplit::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int BucketizedSplit::kFeatureIdFieldNumber;
const int BucketizedSplit::kThresholdFieldNumber;
const int BucketizedSplit::kDimensionIdFieldNumber;
const int BucketizedSplit::kDefaultDirectionFieldNumber;
const int BucketizedSplit::kLeftIdFieldNumber;
const int BucketizedSplit::kRightIdFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

BucketizedSplit::BucketizedSplit()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_BucketizedSplit.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.boosted_trees.BucketizedSplit)
}
BucketizedSplit::BucketizedSplit(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_BucketizedSplit.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.boosted_trees.BucketizedSplit)
}
BucketizedSplit::BucketizedSplit(const BucketizedSplit& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&feature_id_, &from.feature_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&default_direction_) -
    reinterpret_cast<char*>(&feature_id_)) + sizeof(default_direction_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.boosted_trees.BucketizedSplit)
}

void BucketizedSplit::SharedCtor() {
  ::memset(&feature_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&default_direction_) -
      reinterpret_cast<char*>(&feature_id_)) + sizeof(default_direction_));
}

BucketizedSplit::~BucketizedSplit() {
  // @@protoc_insertion_point(destructor:tensorflow.boosted_trees.BucketizedSplit)
  SharedDtor();
}

void BucketizedSplit::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void BucketizedSplit::ArenaDtor(void* object) {
  BucketizedSplit* _this = reinterpret_cast< BucketizedSplit* >(object);
  (void)_this;
}
void BucketizedSplit::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void BucketizedSplit::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* BucketizedSplit::descriptor() {
  ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const BucketizedSplit& BucketizedSplit::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_BucketizedSplit.base);
  return *internal_default_instance();
}


void BucketizedSplit::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.boosted_trees.BucketizedSplit)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&feature_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&default_direction_) -
      reinterpret_cast<char*>(&feature_id_)) + sizeof(default_direction_));
  _internal_metadata_.Clear();
}

bool BucketizedSplit::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.boosted_trees.BucketizedSplit)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 feature_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &feature_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 threshold = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &threshold_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 left_id = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &left_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 right_id = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &right_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 dimension_id = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &dimension_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.boosted_trees.BucketizedSplit.DefaultDirection default_direction = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(48u /* 48 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_default_direction(static_cast< ::tensorflow::boosted_trees::BucketizedSplit_DefaultDirection >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.boosted_trees.BucketizedSplit)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.boosted_trees.BucketizedSplit)
  return false;
#undef DO_
}

void BucketizedSplit::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.boosted_trees.BucketizedSplit)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 feature_id = 1;
  if (this->feature_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->feature_id(), output);
  }

  // int32 threshold = 2;
  if (this->threshold() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->threshold(), output);
  }

  // int32 left_id = 3;
  if (this->left_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->left_id(), output);
  }

  // int32 right_id = 4;
  if (this->right_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->right_id(), output);
  }

  // int32 dimension_id = 5;
  if (this->dimension_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(5, this->dimension_id(), output);
  }

  // .tensorflow.boosted_trees.BucketizedSplit.DefaultDirection default_direction = 6;
  if (this->default_direction() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->default_direction(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.boosted_trees.BucketizedSplit)
}

::google::protobuf::uint8* BucketizedSplit::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.boosted_trees.BucketizedSplit)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 feature_id = 1;
  if (this->feature_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->feature_id(), target);
  }

  // int32 threshold = 2;
  if (this->threshold() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->threshold(), target);
  }

  // int32 left_id = 3;
  if (this->left_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->left_id(), target);
  }

  // int32 right_id = 4;
  if (this->right_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->right_id(), target);
  }

  // int32 dimension_id = 5;
  if (this->dimension_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(5, this->dimension_id(), target);
  }

  // .tensorflow.boosted_trees.BucketizedSplit.DefaultDirection default_direction = 6;
  if (this->default_direction() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->default_direction(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.boosted_trees.BucketizedSplit)
  return target;
}

size_t BucketizedSplit::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.boosted_trees.BucketizedSplit)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int32 feature_id = 1;
  if (this->feature_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->feature_id());
  }

  // int32 threshold = 2;
  if (this->threshold() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->threshold());
  }

  // int32 left_id = 3;
  if (this->left_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->left_id());
  }

  // int32 right_id = 4;
  if (this->right_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->right_id());
  }

  // int32 dimension_id = 5;
  if (this->dimension_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->dimension_id());
  }

  // .tensorflow.boosted_trees.BucketizedSplit.DefaultDirection default_direction = 6;
  if (this->default_direction() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->default_direction());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void BucketizedSplit::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.boosted_trees.BucketizedSplit)
  GOOGLE_DCHECK_NE(&from, this);
  const BucketizedSplit* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const BucketizedSplit>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.boosted_trees.BucketizedSplit)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.boosted_trees.BucketizedSplit)
    MergeFrom(*source);
  }
}

void BucketizedSplit::MergeFrom(const BucketizedSplit& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.boosted_trees.BucketizedSplit)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.feature_id() != 0) {
    set_feature_id(from.feature_id());
  }
  if (from.threshold() != 0) {
    set_threshold(from.threshold());
  }
  if (from.left_id() != 0) {
    set_left_id(from.left_id());
  }
  if (from.right_id() != 0) {
    set_right_id(from.right_id());
  }
  if (from.dimension_id() != 0) {
    set_dimension_id(from.dimension_id());
  }
  if (from.default_direction() != 0) {
    set_default_direction(from.default_direction());
  }
}

void BucketizedSplit::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.boosted_trees.BucketizedSplit)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void BucketizedSplit::CopyFrom(const BucketizedSplit& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.boosted_trees.BucketizedSplit)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BucketizedSplit::IsInitialized() const {
  return true;
}

void BucketizedSplit::Swap(BucketizedSplit* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    BucketizedSplit* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void BucketizedSplit::UnsafeArenaSwap(BucketizedSplit* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void BucketizedSplit::InternalSwap(BucketizedSplit* other) {
  using std::swap;
  swap(feature_id_, other->feature_id_);
  swap(threshold_, other->threshold_);
  swap(left_id_, other->left_id_);
  swap(right_id_, other->right_id_);
  swap(dimension_id_, other->dimension_id_);
  swap(default_direction_, other->default_direction_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata BucketizedSplit::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void CategoricalSplit::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CategoricalSplit::kFeatureIdFieldNumber;
const int CategoricalSplit::kValueFieldNumber;
const int CategoricalSplit::kLeftIdFieldNumber;
const int CategoricalSplit::kRightIdFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CategoricalSplit::CategoricalSplit()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_CategoricalSplit.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.boosted_trees.CategoricalSplit)
}
CategoricalSplit::CategoricalSplit(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_CategoricalSplit.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.boosted_trees.CategoricalSplit)
}
CategoricalSplit::CategoricalSplit(const CategoricalSplit& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&feature_id_, &from.feature_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&right_id_) -
    reinterpret_cast<char*>(&feature_id_)) + sizeof(right_id_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.boosted_trees.CategoricalSplit)
}

void CategoricalSplit::SharedCtor() {
  ::memset(&feature_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&right_id_) -
      reinterpret_cast<char*>(&feature_id_)) + sizeof(right_id_));
}

CategoricalSplit::~CategoricalSplit() {
  // @@protoc_insertion_point(destructor:tensorflow.boosted_trees.CategoricalSplit)
  SharedDtor();
}

void CategoricalSplit::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void CategoricalSplit::ArenaDtor(void* object) {
  CategoricalSplit* _this = reinterpret_cast< CategoricalSplit* >(object);
  (void)_this;
}
void CategoricalSplit::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void CategoricalSplit::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* CategoricalSplit::descriptor() {
  ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CategoricalSplit& CategoricalSplit::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_CategoricalSplit.base);
  return *internal_default_instance();
}


void CategoricalSplit::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.boosted_trees.CategoricalSplit)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&feature_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&right_id_) -
      reinterpret_cast<char*>(&feature_id_)) + sizeof(right_id_));
  _internal_metadata_.Clear();
}

bool CategoricalSplit::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.boosted_trees.CategoricalSplit)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 feature_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &feature_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 value = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &value_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 left_id = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &left_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 right_id = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &right_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.boosted_trees.CategoricalSplit)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.boosted_trees.CategoricalSplit)
  return false;
#undef DO_
}

void CategoricalSplit::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.boosted_trees.CategoricalSplit)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 feature_id = 1;
  if (this->feature_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->feature_id(), output);
  }

  // int32 value = 2;
  if (this->value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->value(), output);
  }

  // int32 left_id = 3;
  if (this->left_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->left_id(), output);
  }

  // int32 right_id = 4;
  if (this->right_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->right_id(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.boosted_trees.CategoricalSplit)
}

::google::protobuf::uint8* CategoricalSplit::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.boosted_trees.CategoricalSplit)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 feature_id = 1;
  if (this->feature_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->feature_id(), target);
  }

  // int32 value = 2;
  if (this->value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->value(), target);
  }

  // int32 left_id = 3;
  if (this->left_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->left_id(), target);
  }

  // int32 right_id = 4;
  if (this->right_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->right_id(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.boosted_trees.CategoricalSplit)
  return target;
}

size_t CategoricalSplit::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.boosted_trees.CategoricalSplit)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int32 feature_id = 1;
  if (this->feature_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->feature_id());
  }

  // int32 value = 2;
  if (this->value() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->value());
  }

  // int32 left_id = 3;
  if (this->left_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->left_id());
  }

  // int32 right_id = 4;
  if (this->right_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->right_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CategoricalSplit::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.boosted_trees.CategoricalSplit)
  GOOGLE_DCHECK_NE(&from, this);
  const CategoricalSplit* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CategoricalSplit>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.boosted_trees.CategoricalSplit)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.boosted_trees.CategoricalSplit)
    MergeFrom(*source);
  }
}

void CategoricalSplit::MergeFrom(const CategoricalSplit& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.boosted_trees.CategoricalSplit)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.feature_id() != 0) {
    set_feature_id(from.feature_id());
  }
  if (from.value() != 0) {
    set_value(from.value());
  }
  if (from.left_id() != 0) {
    set_left_id(from.left_id());
  }
  if (from.right_id() != 0) {
    set_right_id(from.right_id());
  }
}

void CategoricalSplit::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.boosted_trees.CategoricalSplit)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CategoricalSplit::CopyFrom(const CategoricalSplit& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.boosted_trees.CategoricalSplit)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CategoricalSplit::IsInitialized() const {
  return true;
}

void CategoricalSplit::Swap(CategoricalSplit* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    CategoricalSplit* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void CategoricalSplit::UnsafeArenaSwap(CategoricalSplit* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void CategoricalSplit::InternalSwap(CategoricalSplit* other) {
  using std::swap;
  swap(feature_id_, other->feature_id_);
  swap(value_, other->value_);
  swap(left_id_, other->left_id_);
  swap(right_id_, other->right_id_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata CategoricalSplit::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void DenseSplit::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int DenseSplit::kFeatureIdFieldNumber;
const int DenseSplit::kThresholdFieldNumber;
const int DenseSplit::kLeftIdFieldNumber;
const int DenseSplit::kRightIdFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DenseSplit::DenseSplit()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_DenseSplit.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.boosted_trees.DenseSplit)
}
DenseSplit::DenseSplit(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_DenseSplit.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.boosted_trees.DenseSplit)
}
DenseSplit::DenseSplit(const DenseSplit& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&feature_id_, &from.feature_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&right_id_) -
    reinterpret_cast<char*>(&feature_id_)) + sizeof(right_id_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.boosted_trees.DenseSplit)
}

void DenseSplit::SharedCtor() {
  ::memset(&feature_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&right_id_) -
      reinterpret_cast<char*>(&feature_id_)) + sizeof(right_id_));
}

DenseSplit::~DenseSplit() {
  // @@protoc_insertion_point(destructor:tensorflow.boosted_trees.DenseSplit)
  SharedDtor();
}

void DenseSplit::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void DenseSplit::ArenaDtor(void* object) {
  DenseSplit* _this = reinterpret_cast< DenseSplit* >(object);
  (void)_this;
}
void DenseSplit::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void DenseSplit::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* DenseSplit::descriptor() {
  ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const DenseSplit& DenseSplit::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_DenseSplit.base);
  return *internal_default_instance();
}


void DenseSplit::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.boosted_trees.DenseSplit)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&feature_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&right_id_) -
      reinterpret_cast<char*>(&feature_id_)) + sizeof(right_id_));
  _internal_metadata_.Clear();
}

bool DenseSplit::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.boosted_trees.DenseSplit)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 feature_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &feature_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float threshold = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(21u /* 21 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &threshold_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 left_id = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &left_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 right_id = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &right_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.boosted_trees.DenseSplit)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.boosted_trees.DenseSplit)
  return false;
#undef DO_
}

void DenseSplit::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.boosted_trees.DenseSplit)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 feature_id = 1;
  if (this->feature_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->feature_id(), output);
  }

  // float threshold = 2;
  if (this->threshold() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(2, this->threshold(), output);
  }

  // int32 left_id = 3;
  if (this->left_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->left_id(), output);
  }

  // int32 right_id = 4;
  if (this->right_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->right_id(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.boosted_trees.DenseSplit)
}

::google::protobuf::uint8* DenseSplit::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.boosted_trees.DenseSplit)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 feature_id = 1;
  if (this->feature_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->feature_id(), target);
  }

  // float threshold = 2;
  if (this->threshold() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(2, this->threshold(), target);
  }

  // int32 left_id = 3;
  if (this->left_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->left_id(), target);
  }

  // int32 right_id = 4;
  if (this->right_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->right_id(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.boosted_trees.DenseSplit)
  return target;
}

size_t DenseSplit::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.boosted_trees.DenseSplit)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int32 feature_id = 1;
  if (this->feature_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->feature_id());
  }

  // float threshold = 2;
  if (this->threshold() != 0) {
    total_size += 1 + 4;
  }

  // int32 left_id = 3;
  if (this->left_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->left_id());
  }

  // int32 right_id = 4;
  if (this->right_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->right_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DenseSplit::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.boosted_trees.DenseSplit)
  GOOGLE_DCHECK_NE(&from, this);
  const DenseSplit* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const DenseSplit>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.boosted_trees.DenseSplit)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.boosted_trees.DenseSplit)
    MergeFrom(*source);
  }
}

void DenseSplit::MergeFrom(const DenseSplit& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.boosted_trees.DenseSplit)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.feature_id() != 0) {
    set_feature_id(from.feature_id());
  }
  if (from.threshold() != 0) {
    set_threshold(from.threshold());
  }
  if (from.left_id() != 0) {
    set_left_id(from.left_id());
  }
  if (from.right_id() != 0) {
    set_right_id(from.right_id());
  }
}

void DenseSplit::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.boosted_trees.DenseSplit)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DenseSplit::CopyFrom(const DenseSplit& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.boosted_trees.DenseSplit)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DenseSplit::IsInitialized() const {
  return true;
}

void DenseSplit::Swap(DenseSplit* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    DenseSplit* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void DenseSplit::UnsafeArenaSwap(DenseSplit* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void DenseSplit::InternalSwap(DenseSplit* other) {
  using std::swap;
  swap(feature_id_, other->feature_id_);
  swap(threshold_, other->threshold_);
  swap(left_id_, other->left_id_);
  swap(right_id_, other->right_id_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata DenseSplit::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Tree::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Tree::kNodesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Tree::Tree()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_Tree.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.boosted_trees.Tree)
}
Tree::Tree(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  nodes_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_Tree.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.boosted_trees.Tree)
}
Tree::Tree(const Tree& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      nodes_(from.nodes_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.boosted_trees.Tree)
}

void Tree::SharedCtor() {
}

Tree::~Tree() {
  // @@protoc_insertion_point(destructor:tensorflow.boosted_trees.Tree)
  SharedDtor();
}

void Tree::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void Tree::ArenaDtor(void* object) {
  Tree* _this = reinterpret_cast< Tree* >(object);
  (void)_this;
}
void Tree::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void Tree::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Tree::descriptor() {
  ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Tree& Tree::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_Tree.base);
  return *internal_default_instance();
}


void Tree::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.boosted_trees.Tree)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  nodes_.Clear();
  _internal_metadata_.Clear();
}

bool Tree::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.boosted_trees.Tree)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.boosted_trees.Node nodes = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_nodes()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.boosted_trees.Tree)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.boosted_trees.Tree)
  return false;
#undef DO_
}

void Tree::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.boosted_trees.Tree)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.boosted_trees.Node nodes = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->nodes_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->nodes(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.boosted_trees.Tree)
}

::google::protobuf::uint8* Tree::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.boosted_trees.Tree)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.boosted_trees.Node nodes = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->nodes_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->nodes(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.boosted_trees.Tree)
  return target;
}

size_t Tree::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.boosted_trees.Tree)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.boosted_trees.Node nodes = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->nodes_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->nodes(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Tree::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.boosted_trees.Tree)
  GOOGLE_DCHECK_NE(&from, this);
  const Tree* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Tree>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.boosted_trees.Tree)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.boosted_trees.Tree)
    MergeFrom(*source);
  }
}

void Tree::MergeFrom(const Tree& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.boosted_trees.Tree)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  nodes_.MergeFrom(from.nodes_);
}

void Tree::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.boosted_trees.Tree)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Tree::CopyFrom(const Tree& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.boosted_trees.Tree)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Tree::IsInitialized() const {
  return true;
}

void Tree::Swap(Tree* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    Tree* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void Tree::UnsafeArenaSwap(Tree* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void Tree::InternalSwap(Tree* other) {
  using std::swap;
  CastToBase(&nodes_)->InternalSwap(CastToBase(&other->nodes_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Tree::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void TreeMetadata_PostPruneNodeUpdate::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TreeMetadata_PostPruneNodeUpdate::kNewNodeIdFieldNumber;
const int TreeMetadata_PostPruneNodeUpdate::kLogitChangeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TreeMetadata_PostPruneNodeUpdate::TreeMetadata_PostPruneNodeUpdate()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_TreeMetadata_PostPruneNodeUpdate.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate)
}
TreeMetadata_PostPruneNodeUpdate::TreeMetadata_PostPruneNodeUpdate(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_TreeMetadata_PostPruneNodeUpdate.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate)
}
TreeMetadata_PostPruneNodeUpdate::TreeMetadata_PostPruneNodeUpdate(const TreeMetadata_PostPruneNodeUpdate& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&new_node_id_, &from.new_node_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&logit_change_) -
    reinterpret_cast<char*>(&new_node_id_)) + sizeof(logit_change_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate)
}

void TreeMetadata_PostPruneNodeUpdate::SharedCtor() {
  ::memset(&new_node_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&logit_change_) -
      reinterpret_cast<char*>(&new_node_id_)) + sizeof(logit_change_));
}

TreeMetadata_PostPruneNodeUpdate::~TreeMetadata_PostPruneNodeUpdate() {
  // @@protoc_insertion_point(destructor:tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate)
  SharedDtor();
}

void TreeMetadata_PostPruneNodeUpdate::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void TreeMetadata_PostPruneNodeUpdate::ArenaDtor(void* object) {
  TreeMetadata_PostPruneNodeUpdate* _this = reinterpret_cast< TreeMetadata_PostPruneNodeUpdate* >(object);
  (void)_this;
}
void TreeMetadata_PostPruneNodeUpdate::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TreeMetadata_PostPruneNodeUpdate::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* TreeMetadata_PostPruneNodeUpdate::descriptor() {
  ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const TreeMetadata_PostPruneNodeUpdate& TreeMetadata_PostPruneNodeUpdate::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_TreeMetadata_PostPruneNodeUpdate.base);
  return *internal_default_instance();
}


void TreeMetadata_PostPruneNodeUpdate::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&new_node_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&logit_change_) -
      reinterpret_cast<char*>(&new_node_id_)) + sizeof(logit_change_));
  _internal_metadata_.Clear();
}

bool TreeMetadata_PostPruneNodeUpdate::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 new_node_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &new_node_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float logit_change = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(21u /* 21 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &logit_change_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate)
  return false;
#undef DO_
}

void TreeMetadata_PostPruneNodeUpdate::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 new_node_id = 1;
  if (this->new_node_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->new_node_id(), output);
  }

  // float logit_change = 2;
  if (this->logit_change() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(2, this->logit_change(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate)
}

::google::protobuf::uint8* TreeMetadata_PostPruneNodeUpdate::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 new_node_id = 1;
  if (this->new_node_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->new_node_id(), target);
  }

  // float logit_change = 2;
  if (this->logit_change() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(2, this->logit_change(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate)
  return target;
}

size_t TreeMetadata_PostPruneNodeUpdate::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int32 new_node_id = 1;
  if (this->new_node_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->new_node_id());
  }

  // float logit_change = 2;
  if (this->logit_change() != 0) {
    total_size += 1 + 4;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TreeMetadata_PostPruneNodeUpdate::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate)
  GOOGLE_DCHECK_NE(&from, this);
  const TreeMetadata_PostPruneNodeUpdate* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TreeMetadata_PostPruneNodeUpdate>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate)
    MergeFrom(*source);
  }
}

void TreeMetadata_PostPruneNodeUpdate::MergeFrom(const TreeMetadata_PostPruneNodeUpdate& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.new_node_id() != 0) {
    set_new_node_id(from.new_node_id());
  }
  if (from.logit_change() != 0) {
    set_logit_change(from.logit_change());
  }
}

void TreeMetadata_PostPruneNodeUpdate::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TreeMetadata_PostPruneNodeUpdate::CopyFrom(const TreeMetadata_PostPruneNodeUpdate& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TreeMetadata_PostPruneNodeUpdate::IsInitialized() const {
  return true;
}

void TreeMetadata_PostPruneNodeUpdate::Swap(TreeMetadata_PostPruneNodeUpdate* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TreeMetadata_PostPruneNodeUpdate* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void TreeMetadata_PostPruneNodeUpdate::UnsafeArenaSwap(TreeMetadata_PostPruneNodeUpdate* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TreeMetadata_PostPruneNodeUpdate::InternalSwap(TreeMetadata_PostPruneNodeUpdate* other) {
  using std::swap;
  swap(new_node_id_, other->new_node_id_);
  swap(logit_change_, other->logit_change_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata TreeMetadata_PostPruneNodeUpdate::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void TreeMetadata::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TreeMetadata::kNumLayersGrownFieldNumber;
const int TreeMetadata::kIsFinalizedFieldNumber;
const int TreeMetadata::kPostPrunedNodesMetaFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TreeMetadata::TreeMetadata()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_TreeMetadata.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.boosted_trees.TreeMetadata)
}
TreeMetadata::TreeMetadata(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  post_pruned_nodes_meta_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_TreeMetadata.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.boosted_trees.TreeMetadata)
}
TreeMetadata::TreeMetadata(const TreeMetadata& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      post_pruned_nodes_meta_(from.post_pruned_nodes_meta_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&num_layers_grown_, &from.num_layers_grown_,
    static_cast<size_t>(reinterpret_cast<char*>(&is_finalized_) -
    reinterpret_cast<char*>(&num_layers_grown_)) + sizeof(is_finalized_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.boosted_trees.TreeMetadata)
}

void TreeMetadata::SharedCtor() {
  ::memset(&num_layers_grown_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&is_finalized_) -
      reinterpret_cast<char*>(&num_layers_grown_)) + sizeof(is_finalized_));
}

TreeMetadata::~TreeMetadata() {
  // @@protoc_insertion_point(destructor:tensorflow.boosted_trees.TreeMetadata)
  SharedDtor();
}

void TreeMetadata::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void TreeMetadata::ArenaDtor(void* object) {
  TreeMetadata* _this = reinterpret_cast< TreeMetadata* >(object);
  (void)_this;
}
void TreeMetadata::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TreeMetadata::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* TreeMetadata::descriptor() {
  ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const TreeMetadata& TreeMetadata::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_TreeMetadata.base);
  return *internal_default_instance();
}


void TreeMetadata::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.boosted_trees.TreeMetadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  post_pruned_nodes_meta_.Clear();
  ::memset(&num_layers_grown_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&is_finalized_) -
      reinterpret_cast<char*>(&num_layers_grown_)) + sizeof(is_finalized_));
  _internal_metadata_.Clear();
}

bool TreeMetadata::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.boosted_trees.TreeMetadata)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 num_layers_grown = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &num_layers_grown_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool is_finalized = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &is_finalized_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate post_pruned_nodes_meta = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_post_pruned_nodes_meta()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.boosted_trees.TreeMetadata)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.boosted_trees.TreeMetadata)
  return false;
#undef DO_
}

void TreeMetadata::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.boosted_trees.TreeMetadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 num_layers_grown = 2;
  if (this->num_layers_grown() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->num_layers_grown(), output);
  }

  // bool is_finalized = 3;
  if (this->is_finalized() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(3, this->is_finalized(), output);
  }

  // repeated .tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate post_pruned_nodes_meta = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->post_pruned_nodes_meta_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4,
      this->post_pruned_nodes_meta(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.boosted_trees.TreeMetadata)
}

::google::protobuf::uint8* TreeMetadata::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.boosted_trees.TreeMetadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 num_layers_grown = 2;
  if (this->num_layers_grown() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->num_layers_grown(), target);
  }

  // bool is_finalized = 3;
  if (this->is_finalized() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(3, this->is_finalized(), target);
  }

  // repeated .tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate post_pruned_nodes_meta = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->post_pruned_nodes_meta_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->post_pruned_nodes_meta(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.boosted_trees.TreeMetadata)
  return target;
}

size_t TreeMetadata::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.boosted_trees.TreeMetadata)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate post_pruned_nodes_meta = 4;
  {
    unsigned int count = static_cast<unsigned int>(this->post_pruned_nodes_meta_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->post_pruned_nodes_meta(static_cast<int>(i)));
    }
  }

  // int32 num_layers_grown = 2;
  if (this->num_layers_grown() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->num_layers_grown());
  }

  // bool is_finalized = 3;
  if (this->is_finalized() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TreeMetadata::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.boosted_trees.TreeMetadata)
  GOOGLE_DCHECK_NE(&from, this);
  const TreeMetadata* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TreeMetadata>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.boosted_trees.TreeMetadata)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.boosted_trees.TreeMetadata)
    MergeFrom(*source);
  }
}

void TreeMetadata::MergeFrom(const TreeMetadata& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.boosted_trees.TreeMetadata)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  post_pruned_nodes_meta_.MergeFrom(from.post_pruned_nodes_meta_);
  if (from.num_layers_grown() != 0) {
    set_num_layers_grown(from.num_layers_grown());
  }
  if (from.is_finalized() != 0) {
    set_is_finalized(from.is_finalized());
  }
}

void TreeMetadata::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.boosted_trees.TreeMetadata)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TreeMetadata::CopyFrom(const TreeMetadata& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.boosted_trees.TreeMetadata)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TreeMetadata::IsInitialized() const {
  return true;
}

void TreeMetadata::Swap(TreeMetadata* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TreeMetadata* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void TreeMetadata::UnsafeArenaSwap(TreeMetadata* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TreeMetadata::InternalSwap(TreeMetadata* other) {
  using std::swap;
  CastToBase(&post_pruned_nodes_meta_)->InternalSwap(CastToBase(&other->post_pruned_nodes_meta_));
  swap(num_layers_grown_, other->num_layers_grown_);
  swap(is_finalized_, other->is_finalized_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata TreeMetadata::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void GrowingMetadata::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GrowingMetadata::kNumTreesAttemptedFieldNumber;
const int GrowingMetadata::kNumLayersAttemptedFieldNumber;
const int GrowingMetadata::kLastLayerNodeStartFieldNumber;
const int GrowingMetadata::kLastLayerNodeEndFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GrowingMetadata::GrowingMetadata()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_GrowingMetadata.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.boosted_trees.GrowingMetadata)
}
GrowingMetadata::GrowingMetadata(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_GrowingMetadata.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.boosted_trees.GrowingMetadata)
}
GrowingMetadata::GrowingMetadata(const GrowingMetadata& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&num_trees_attempted_, &from.num_trees_attempted_,
    static_cast<size_t>(reinterpret_cast<char*>(&last_layer_node_end_) -
    reinterpret_cast<char*>(&num_trees_attempted_)) + sizeof(last_layer_node_end_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.boosted_trees.GrowingMetadata)
}

void GrowingMetadata::SharedCtor() {
  ::memset(&num_trees_attempted_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&last_layer_node_end_) -
      reinterpret_cast<char*>(&num_trees_attempted_)) + sizeof(last_layer_node_end_));
}

GrowingMetadata::~GrowingMetadata() {
  // @@protoc_insertion_point(destructor:tensorflow.boosted_trees.GrowingMetadata)
  SharedDtor();
}

void GrowingMetadata::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void GrowingMetadata::ArenaDtor(void* object) {
  GrowingMetadata* _this = reinterpret_cast< GrowingMetadata* >(object);
  (void)_this;
}
void GrowingMetadata::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void GrowingMetadata::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* GrowingMetadata::descriptor() {
  ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const GrowingMetadata& GrowingMetadata::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_GrowingMetadata.base);
  return *internal_default_instance();
}


void GrowingMetadata::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.boosted_trees.GrowingMetadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&num_trees_attempted_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&last_layer_node_end_) -
      reinterpret_cast<char*>(&num_trees_attempted_)) + sizeof(last_layer_node_end_));
  _internal_metadata_.Clear();
}

bool GrowingMetadata::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.boosted_trees.GrowingMetadata)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 num_trees_attempted = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &num_trees_attempted_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 num_layers_attempted = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &num_layers_attempted_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 last_layer_node_start = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &last_layer_node_start_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 last_layer_node_end = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &last_layer_node_end_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.boosted_trees.GrowingMetadata)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.boosted_trees.GrowingMetadata)
  return false;
#undef DO_
}

void GrowingMetadata::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.boosted_trees.GrowingMetadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 num_trees_attempted = 1;
  if (this->num_trees_attempted() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->num_trees_attempted(), output);
  }

  // int64 num_layers_attempted = 2;
  if (this->num_layers_attempted() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->num_layers_attempted(), output);
  }

  // int32 last_layer_node_start = 3;
  if (this->last_layer_node_start() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->last_layer_node_start(), output);
  }

  // int32 last_layer_node_end = 4;
  if (this->last_layer_node_end() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->last_layer_node_end(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.boosted_trees.GrowingMetadata)
}

::google::protobuf::uint8* GrowingMetadata::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.boosted_trees.GrowingMetadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 num_trees_attempted = 1;
  if (this->num_trees_attempted() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->num_trees_attempted(), target);
  }

  // int64 num_layers_attempted = 2;
  if (this->num_layers_attempted() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->num_layers_attempted(), target);
  }

  // int32 last_layer_node_start = 3;
  if (this->last_layer_node_start() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->last_layer_node_start(), target);
  }

  // int32 last_layer_node_end = 4;
  if (this->last_layer_node_end() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->last_layer_node_end(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.boosted_trees.GrowingMetadata)
  return target;
}

size_t GrowingMetadata::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.boosted_trees.GrowingMetadata)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int64 num_trees_attempted = 1;
  if (this->num_trees_attempted() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->num_trees_attempted());
  }

  // int64 num_layers_attempted = 2;
  if (this->num_layers_attempted() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->num_layers_attempted());
  }

  // int32 last_layer_node_start = 3;
  if (this->last_layer_node_start() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->last_layer_node_start());
  }

  // int32 last_layer_node_end = 4;
  if (this->last_layer_node_end() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->last_layer_node_end());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void GrowingMetadata::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.boosted_trees.GrowingMetadata)
  GOOGLE_DCHECK_NE(&from, this);
  const GrowingMetadata* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GrowingMetadata>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.boosted_trees.GrowingMetadata)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.boosted_trees.GrowingMetadata)
    MergeFrom(*source);
  }
}

void GrowingMetadata::MergeFrom(const GrowingMetadata& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.boosted_trees.GrowingMetadata)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.num_trees_attempted() != 0) {
    set_num_trees_attempted(from.num_trees_attempted());
  }
  if (from.num_layers_attempted() != 0) {
    set_num_layers_attempted(from.num_layers_attempted());
  }
  if (from.last_layer_node_start() != 0) {
    set_last_layer_node_start(from.last_layer_node_start());
  }
  if (from.last_layer_node_end() != 0) {
    set_last_layer_node_end(from.last_layer_node_end());
  }
}

void GrowingMetadata::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.boosted_trees.GrowingMetadata)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GrowingMetadata::CopyFrom(const GrowingMetadata& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.boosted_trees.GrowingMetadata)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GrowingMetadata::IsInitialized() const {
  return true;
}

void GrowingMetadata::Swap(GrowingMetadata* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    GrowingMetadata* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void GrowingMetadata::UnsafeArenaSwap(GrowingMetadata* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void GrowingMetadata::InternalSwap(GrowingMetadata* other) {
  using std::swap;
  swap(num_trees_attempted_, other->num_trees_attempted_);
  swap(num_layers_attempted_, other->num_layers_attempted_);
  swap(last_layer_node_start_, other->last_layer_node_start_);
  swap(last_layer_node_end_, other->last_layer_node_end_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata GrowingMetadata::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void TreeEnsemble::InitAsDefaultInstance() {
  ::tensorflow::boosted_trees::_TreeEnsemble_default_instance_._instance.get_mutable()->growing_metadata_ = const_cast< ::tensorflow::boosted_trees::GrowingMetadata*>(
      ::tensorflow::boosted_trees::GrowingMetadata::internal_default_instance());
}
void TreeEnsemble::unsafe_arena_set_allocated_growing_metadata(
    ::tensorflow::boosted_trees::GrowingMetadata* growing_metadata) {
  if (GetArenaNoVirtual() == NULL) {
    delete growing_metadata_;
  }
  growing_metadata_ = growing_metadata;
  if (growing_metadata) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.boosted_trees.TreeEnsemble.growing_metadata)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TreeEnsemble::kTreesFieldNumber;
const int TreeEnsemble::kTreeWeightsFieldNumber;
const int TreeEnsemble::kTreeMetadataFieldNumber;
const int TreeEnsemble::kGrowingMetadataFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TreeEnsemble::TreeEnsemble()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_TreeEnsemble.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.boosted_trees.TreeEnsemble)
}
TreeEnsemble::TreeEnsemble(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  trees_(arena),
  tree_weights_(arena),
  tree_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_TreeEnsemble.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.boosted_trees.TreeEnsemble)
}
TreeEnsemble::TreeEnsemble(const TreeEnsemble& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      trees_(from.trees_),
      tree_weights_(from.tree_weights_),
      tree_metadata_(from.tree_metadata_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_growing_metadata()) {
    growing_metadata_ = new ::tensorflow::boosted_trees::GrowingMetadata(*from.growing_metadata_);
  } else {
    growing_metadata_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.boosted_trees.TreeEnsemble)
}

void TreeEnsemble::SharedCtor() {
  growing_metadata_ = NULL;
}

TreeEnsemble::~TreeEnsemble() {
  // @@protoc_insertion_point(destructor:tensorflow.boosted_trees.TreeEnsemble)
  SharedDtor();
}

void TreeEnsemble::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (this != internal_default_instance()) delete growing_metadata_;
}

void TreeEnsemble::ArenaDtor(void* object) {
  TreeEnsemble* _this = reinterpret_cast< TreeEnsemble* >(object);
  (void)_this;
}
void TreeEnsemble::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TreeEnsemble::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* TreeEnsemble::descriptor() {
  ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const TreeEnsemble& TreeEnsemble::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_TreeEnsemble.base);
  return *internal_default_instance();
}


void TreeEnsemble::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.boosted_trees.TreeEnsemble)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  trees_.Clear();
  tree_weights_.Clear();
  tree_metadata_.Clear();
  if (GetArenaNoVirtual() == NULL && growing_metadata_ != NULL) {
    delete growing_metadata_;
  }
  growing_metadata_ = NULL;
  _internal_metadata_.Clear();
}

bool TreeEnsemble::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.boosted_trees.TreeEnsemble)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.boosted_trees.Tree trees = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_trees()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated float tree_weights = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, this->mutable_tree_weights())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(21u /* 21 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 1, 18u, input, this->mutable_tree_weights())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.boosted_trees.TreeMetadata tree_metadata = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_tree_metadata()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.boosted_trees.GrowingMetadata growing_metadata = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_growing_metadata()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.boosted_trees.TreeEnsemble)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.boosted_trees.TreeEnsemble)
  return false;
#undef DO_
}

void TreeEnsemble::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.boosted_trees.TreeEnsemble)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.boosted_trees.Tree trees = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->trees_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->trees(static_cast<int>(i)),
      output);
  }

  // repeated float tree_weights = 2;
  if (this->tree_weights_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(2, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _tree_weights_cached_byte_size_));
    ::google::protobuf::internal::WireFormatLite::WriteFloatArray(
      this->tree_weights().data(), this->tree_weights_size(), output);
  }

  // repeated .tensorflow.boosted_trees.TreeMetadata tree_metadata = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->tree_metadata_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3,
      this->tree_metadata(static_cast<int>(i)),
      output);
  }

  // .tensorflow.boosted_trees.GrowingMetadata growing_metadata = 4;
  if (this->has_growing_metadata()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->_internal_growing_metadata(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.boosted_trees.TreeEnsemble)
}

::google::protobuf::uint8* TreeEnsemble::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.boosted_trees.TreeEnsemble)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.boosted_trees.Tree trees = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->trees_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->trees(static_cast<int>(i)), deterministic, target);
  }

  // repeated float tree_weights = 2;
  if (this->tree_weights_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      2,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _tree_weights_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteFloatNoTagToArray(this->tree_weights_, target);
  }

  // repeated .tensorflow.boosted_trees.TreeMetadata tree_metadata = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->tree_metadata_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->tree_metadata(static_cast<int>(i)), deterministic, target);
  }

  // .tensorflow.boosted_trees.GrowingMetadata growing_metadata = 4;
  if (this->has_growing_metadata()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->_internal_growing_metadata(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.boosted_trees.TreeEnsemble)
  return target;
}

size_t TreeEnsemble::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.boosted_trees.TreeEnsemble)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.boosted_trees.Tree trees = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->trees_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->trees(static_cast<int>(i)));
    }
  }

  // repeated float tree_weights = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->tree_weights_size());
    size_t data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _tree_weights_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated .tensorflow.boosted_trees.TreeMetadata tree_metadata = 3;
  {
    unsigned int count = static_cast<unsigned int>(this->tree_metadata_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->tree_metadata(static_cast<int>(i)));
    }
  }

  // .tensorflow.boosted_trees.GrowingMetadata growing_metadata = 4;
  if (this->has_growing_metadata()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *growing_metadata_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TreeEnsemble::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.boosted_trees.TreeEnsemble)
  GOOGLE_DCHECK_NE(&from, this);
  const TreeEnsemble* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TreeEnsemble>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.boosted_trees.TreeEnsemble)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.boosted_trees.TreeEnsemble)
    MergeFrom(*source);
  }
}

void TreeEnsemble::MergeFrom(const TreeEnsemble& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.boosted_trees.TreeEnsemble)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  trees_.MergeFrom(from.trees_);
  tree_weights_.MergeFrom(from.tree_weights_);
  tree_metadata_.MergeFrom(from.tree_metadata_);
  if (from.has_growing_metadata()) {
    mutable_growing_metadata()->::tensorflow::boosted_trees::GrowingMetadata::MergeFrom(from.growing_metadata());
  }
}

void TreeEnsemble::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.boosted_trees.TreeEnsemble)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TreeEnsemble::CopyFrom(const TreeEnsemble& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.boosted_trees.TreeEnsemble)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TreeEnsemble::IsInitialized() const {
  return true;
}

void TreeEnsemble::Swap(TreeEnsemble* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TreeEnsemble* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void TreeEnsemble::UnsafeArenaSwap(TreeEnsemble* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TreeEnsemble::InternalSwap(TreeEnsemble* other) {
  using std::swap;
  CastToBase(&trees_)->InternalSwap(CastToBase(&other->trees_));
  tree_weights_.InternalSwap(&other->tree_weights_);
  CastToBase(&tree_metadata_)->InternalSwap(CastToBase(&other->tree_metadata_));
  swap(growing_metadata_, other->growing_metadata_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata TreeEnsemble::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void DebugOutput::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int DebugOutput::kFeatureIdsFieldNumber;
const int DebugOutput::kLogitsPathFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DebugOutput::DebugOutput()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_DebugOutput.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.boosted_trees.DebugOutput)
}
DebugOutput::DebugOutput(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  feature_ids_(arena),
  logits_path_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_DebugOutput.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.boosted_trees.DebugOutput)
}
DebugOutput::DebugOutput(const DebugOutput& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      feature_ids_(from.feature_ids_),
      logits_path_(from.logits_path_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.boosted_trees.DebugOutput)
}

void DebugOutput::SharedCtor() {
}

DebugOutput::~DebugOutput() {
  // @@protoc_insertion_point(destructor:tensorflow.boosted_trees.DebugOutput)
  SharedDtor();
}

void DebugOutput::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void DebugOutput::ArenaDtor(void* object) {
  DebugOutput* _this = reinterpret_cast< DebugOutput* >(object);
  (void)_this;
}
void DebugOutput::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void DebugOutput::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* DebugOutput::descriptor() {
  ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const DebugOutput& DebugOutput::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::scc_info_DebugOutput.base);
  return *internal_default_instance();
}


void DebugOutput::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.boosted_trees.DebugOutput)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  feature_ids_.Clear();
  logits_path_.Clear();
  _internal_metadata_.Clear();
}

bool DebugOutput::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.boosted_trees.DebugOutput)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated int32 feature_ids = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_feature_ids())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 1, 10u, input, this->mutable_feature_ids())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated float logits_path = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, this->mutable_logits_path())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(21u /* 21 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 1, 18u, input, this->mutable_logits_path())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.boosted_trees.DebugOutput)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.boosted_trees.DebugOutput)
  return false;
#undef DO_
}

void DebugOutput::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.boosted_trees.DebugOutput)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated int32 feature_ids = 1;
  if (this->feature_ids_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(1, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _feature_ids_cached_byte_size_));
  }
  for (int i = 0, n = this->feature_ids_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32NoTag(
      this->feature_ids(i), output);
  }

  // repeated float logits_path = 2;
  if (this->logits_path_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(2, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _logits_path_cached_byte_size_));
    ::google::protobuf::internal::WireFormatLite::WriteFloatArray(
      this->logits_path().data(), this->logits_path_size(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.boosted_trees.DebugOutput)
}

::google::protobuf::uint8* DebugOutput::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.boosted_trees.DebugOutput)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated int32 feature_ids = 1;
  if (this->feature_ids_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      1,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _feature_ids_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt32NoTagToArray(this->feature_ids_, target);
  }

  // repeated float logits_path = 2;
  if (this->logits_path_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      2,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _logits_path_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteFloatNoTagToArray(this->logits_path_, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.boosted_trees.DebugOutput)
  return target;
}

size_t DebugOutput::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.boosted_trees.DebugOutput)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated int32 feature_ids = 1;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int32Size(this->feature_ids_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _feature_ids_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated float logits_path = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->logits_path_size());
    size_t data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _logits_path_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DebugOutput::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.boosted_trees.DebugOutput)
  GOOGLE_DCHECK_NE(&from, this);
  const DebugOutput* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const DebugOutput>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.boosted_trees.DebugOutput)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.boosted_trees.DebugOutput)
    MergeFrom(*source);
  }
}

void DebugOutput::MergeFrom(const DebugOutput& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.boosted_trees.DebugOutput)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  feature_ids_.MergeFrom(from.feature_ids_);
  logits_path_.MergeFrom(from.logits_path_);
}

void DebugOutput::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.boosted_trees.DebugOutput)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DebugOutput::CopyFrom(const DebugOutput& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.boosted_trees.DebugOutput)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DebugOutput::IsInitialized() const {
  return true;
}

void DebugOutput::Swap(DebugOutput* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    DebugOutput* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void DebugOutput::UnsafeArenaSwap(DebugOutput* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void DebugOutput::InternalSwap(DebugOutput* other) {
  using std::swap;
  feature_ids_.InternalSwap(&other->feature_ids_);
  logits_path_.InternalSwap(&other->logits_path_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata DebugOutput::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace boosted_trees
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::boosted_trees::Node* Arena::CreateMaybeMessage< ::tensorflow::boosted_trees::Node >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::boosted_trees::Node >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::boosted_trees::NodeMetadata* Arena::CreateMaybeMessage< ::tensorflow::boosted_trees::NodeMetadata >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::boosted_trees::NodeMetadata >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::boosted_trees::Leaf* Arena::CreateMaybeMessage< ::tensorflow::boosted_trees::Leaf >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::boosted_trees::Leaf >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::boosted_trees::Vector* Arena::CreateMaybeMessage< ::tensorflow::boosted_trees::Vector >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::boosted_trees::Vector >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::boosted_trees::SparseVector* Arena::CreateMaybeMessage< ::tensorflow::boosted_trees::SparseVector >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::boosted_trees::SparseVector >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::boosted_trees::BucketizedSplit* Arena::CreateMaybeMessage< ::tensorflow::boosted_trees::BucketizedSplit >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::boosted_trees::BucketizedSplit >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::boosted_trees::CategoricalSplit* Arena::CreateMaybeMessage< ::tensorflow::boosted_trees::CategoricalSplit >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::boosted_trees::CategoricalSplit >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::boosted_trees::DenseSplit* Arena::CreateMaybeMessage< ::tensorflow::boosted_trees::DenseSplit >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::boosted_trees::DenseSplit >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::boosted_trees::Tree* Arena::CreateMaybeMessage< ::tensorflow::boosted_trees::Tree >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::boosted_trees::Tree >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::boosted_trees::TreeMetadata_PostPruneNodeUpdate* Arena::CreateMaybeMessage< ::tensorflow::boosted_trees::TreeMetadata_PostPruneNodeUpdate >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::boosted_trees::TreeMetadata_PostPruneNodeUpdate >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::boosted_trees::TreeMetadata* Arena::CreateMaybeMessage< ::tensorflow::boosted_trees::TreeMetadata >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::boosted_trees::TreeMetadata >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::boosted_trees::GrowingMetadata* Arena::CreateMaybeMessage< ::tensorflow::boosted_trees::GrowingMetadata >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::boosted_trees::GrowingMetadata >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::boosted_trees::TreeEnsemble* Arena::CreateMaybeMessage< ::tensorflow::boosted_trees::TreeEnsemble >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::boosted_trees::TreeEnsemble >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::boosted_trees::DebugOutput* Arena::CreateMaybeMessage< ::tensorflow::boosted_trees::DebugOutput >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::boosted_trees::DebugOutput >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

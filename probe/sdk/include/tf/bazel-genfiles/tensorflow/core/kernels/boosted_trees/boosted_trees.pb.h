// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/kernels/boosted_trees/boosted_trees.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto 

namespace protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[14];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto
namespace tensorflow {
namespace boosted_trees {
class BucketizedSplit;
class BucketizedSplitDefaultTypeInternal;
extern BucketizedSplitDefaultTypeInternal _BucketizedSplit_default_instance_;
class CategoricalSplit;
class CategoricalSplitDefaultTypeInternal;
extern CategoricalSplitDefaultTypeInternal _CategoricalSplit_default_instance_;
class DebugOutput;
class DebugOutputDefaultTypeInternal;
extern DebugOutputDefaultTypeInternal _DebugOutput_default_instance_;
class DenseSplit;
class DenseSplitDefaultTypeInternal;
extern DenseSplitDefaultTypeInternal _DenseSplit_default_instance_;
class GrowingMetadata;
class GrowingMetadataDefaultTypeInternal;
extern GrowingMetadataDefaultTypeInternal _GrowingMetadata_default_instance_;
class Leaf;
class LeafDefaultTypeInternal;
extern LeafDefaultTypeInternal _Leaf_default_instance_;
class Node;
class NodeDefaultTypeInternal;
extern NodeDefaultTypeInternal _Node_default_instance_;
class NodeMetadata;
class NodeMetadataDefaultTypeInternal;
extern NodeMetadataDefaultTypeInternal _NodeMetadata_default_instance_;
class SparseVector;
class SparseVectorDefaultTypeInternal;
extern SparseVectorDefaultTypeInternal _SparseVector_default_instance_;
class Tree;
class TreeDefaultTypeInternal;
extern TreeDefaultTypeInternal _Tree_default_instance_;
class TreeEnsemble;
class TreeEnsembleDefaultTypeInternal;
extern TreeEnsembleDefaultTypeInternal _TreeEnsemble_default_instance_;
class TreeMetadata;
class TreeMetadataDefaultTypeInternal;
extern TreeMetadataDefaultTypeInternal _TreeMetadata_default_instance_;
class TreeMetadata_PostPruneNodeUpdate;
class TreeMetadata_PostPruneNodeUpdateDefaultTypeInternal;
extern TreeMetadata_PostPruneNodeUpdateDefaultTypeInternal _TreeMetadata_PostPruneNodeUpdate_default_instance_;
class Vector;
class VectorDefaultTypeInternal;
extern VectorDefaultTypeInternal _Vector_default_instance_;
}  // namespace boosted_trees
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::boosted_trees::BucketizedSplit* Arena::CreateMaybeMessage<::tensorflow::boosted_trees::BucketizedSplit>(Arena*);
template<> ::tensorflow::boosted_trees::CategoricalSplit* Arena::CreateMaybeMessage<::tensorflow::boosted_trees::CategoricalSplit>(Arena*);
template<> ::tensorflow::boosted_trees::DebugOutput* Arena::CreateMaybeMessage<::tensorflow::boosted_trees::DebugOutput>(Arena*);
template<> ::tensorflow::boosted_trees::DenseSplit* Arena::CreateMaybeMessage<::tensorflow::boosted_trees::DenseSplit>(Arena*);
template<> ::tensorflow::boosted_trees::GrowingMetadata* Arena::CreateMaybeMessage<::tensorflow::boosted_trees::GrowingMetadata>(Arena*);
template<> ::tensorflow::boosted_trees::Leaf* Arena::CreateMaybeMessage<::tensorflow::boosted_trees::Leaf>(Arena*);
template<> ::tensorflow::boosted_trees::Node* Arena::CreateMaybeMessage<::tensorflow::boosted_trees::Node>(Arena*);
template<> ::tensorflow::boosted_trees::NodeMetadata* Arena::CreateMaybeMessage<::tensorflow::boosted_trees::NodeMetadata>(Arena*);
template<> ::tensorflow::boosted_trees::SparseVector* Arena::CreateMaybeMessage<::tensorflow::boosted_trees::SparseVector>(Arena*);
template<> ::tensorflow::boosted_trees::Tree* Arena::CreateMaybeMessage<::tensorflow::boosted_trees::Tree>(Arena*);
template<> ::tensorflow::boosted_trees::TreeEnsemble* Arena::CreateMaybeMessage<::tensorflow::boosted_trees::TreeEnsemble>(Arena*);
template<> ::tensorflow::boosted_trees::TreeMetadata* Arena::CreateMaybeMessage<::tensorflow::boosted_trees::TreeMetadata>(Arena*);
template<> ::tensorflow::boosted_trees::TreeMetadata_PostPruneNodeUpdate* Arena::CreateMaybeMessage<::tensorflow::boosted_trees::TreeMetadata_PostPruneNodeUpdate>(Arena*);
template<> ::tensorflow::boosted_trees::Vector* Arena::CreateMaybeMessage<::tensorflow::boosted_trees::Vector>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {
namespace boosted_trees {

enum BucketizedSplit_DefaultDirection {
  BucketizedSplit_DefaultDirection_DEFAULT_LEFT = 0,
  BucketizedSplit_DefaultDirection_DEFAULT_RIGHT = 1,
  BucketizedSplit_DefaultDirection_BucketizedSplit_DefaultDirection_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  BucketizedSplit_DefaultDirection_BucketizedSplit_DefaultDirection_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool BucketizedSplit_DefaultDirection_IsValid(int value);
const BucketizedSplit_DefaultDirection BucketizedSplit_DefaultDirection_DefaultDirection_MIN = BucketizedSplit_DefaultDirection_DEFAULT_LEFT;
const BucketizedSplit_DefaultDirection BucketizedSplit_DefaultDirection_DefaultDirection_MAX = BucketizedSplit_DefaultDirection_DEFAULT_RIGHT;
const int BucketizedSplit_DefaultDirection_DefaultDirection_ARRAYSIZE = BucketizedSplit_DefaultDirection_DefaultDirection_MAX + 1;

const ::google::protobuf::EnumDescriptor* BucketizedSplit_DefaultDirection_descriptor();
inline const ::std::string& BucketizedSplit_DefaultDirection_Name(BucketizedSplit_DefaultDirection value) {
  return ::google::protobuf::internal::NameOfEnum(
    BucketizedSplit_DefaultDirection_descriptor(), value);
}
inline bool BucketizedSplit_DefaultDirection_Parse(
    const ::std::string& name, BucketizedSplit_DefaultDirection* value) {
  return ::google::protobuf::internal::ParseNamedEnum<BucketizedSplit_DefaultDirection>(
    BucketizedSplit_DefaultDirection_descriptor(), name, value);
}
// ===================================================================

class Node : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.boosted_trees.Node) */ {
 public:
  Node();
  virtual ~Node();

  Node(const Node& from);

  inline Node& operator=(const Node& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Node(Node&& from) noexcept
    : Node() {
    *this = ::std::move(from);
  }

  inline Node& operator=(Node&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const Node& default_instance();

  enum NodeCase {
    kLeaf = 1,
    kBucketizedSplit = 2,
    kCategoricalSplit = 3,
    kDenseSplit = 4,
    NODE_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Node* internal_default_instance() {
    return reinterpret_cast<const Node*>(
               &_Node_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(Node* other);
  void Swap(Node* other);
  friend void swap(Node& a, Node& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Node* New() const final {
    return CreateMaybeMessage<Node>(NULL);
  }

  Node* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Node>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Node& from);
  void MergeFrom(const Node& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Node* other);
  protected:
  explicit Node(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .tensorflow.boosted_trees.NodeMetadata metadata = 777;
  bool has_metadata() const;
  void clear_metadata();
  static const int kMetadataFieldNumber = 777;
  private:
  const ::tensorflow::boosted_trees::NodeMetadata& _internal_metadata() const;
  public:
  const ::tensorflow::boosted_trees::NodeMetadata& metadata() const;
  ::tensorflow::boosted_trees::NodeMetadata* release_metadata();
  ::tensorflow::boosted_trees::NodeMetadata* mutable_metadata();
  void set_allocated_metadata(::tensorflow::boosted_trees::NodeMetadata* metadata);
  void unsafe_arena_set_allocated_metadata(
      ::tensorflow::boosted_trees::NodeMetadata* metadata);
  ::tensorflow::boosted_trees::NodeMetadata* unsafe_arena_release_metadata();

  // .tensorflow.boosted_trees.Leaf leaf = 1;
  bool has_leaf() const;
  void clear_leaf();
  static const int kLeafFieldNumber = 1;
  private:
  const ::tensorflow::boosted_trees::Leaf& _internal_leaf() const;
  public:
  const ::tensorflow::boosted_trees::Leaf& leaf() const;
  ::tensorflow::boosted_trees::Leaf* release_leaf();
  ::tensorflow::boosted_trees::Leaf* mutable_leaf();
  void set_allocated_leaf(::tensorflow::boosted_trees::Leaf* leaf);
  void unsafe_arena_set_allocated_leaf(
      ::tensorflow::boosted_trees::Leaf* leaf);
  ::tensorflow::boosted_trees::Leaf* unsafe_arena_release_leaf();

  // .tensorflow.boosted_trees.BucketizedSplit bucketized_split = 2;
  bool has_bucketized_split() const;
  void clear_bucketized_split();
  static const int kBucketizedSplitFieldNumber = 2;
  private:
  const ::tensorflow::boosted_trees::BucketizedSplit& _internal_bucketized_split() const;
  public:
  const ::tensorflow::boosted_trees::BucketizedSplit& bucketized_split() const;
  ::tensorflow::boosted_trees::BucketizedSplit* release_bucketized_split();
  ::tensorflow::boosted_trees::BucketizedSplit* mutable_bucketized_split();
  void set_allocated_bucketized_split(::tensorflow::boosted_trees::BucketizedSplit* bucketized_split);
  void unsafe_arena_set_allocated_bucketized_split(
      ::tensorflow::boosted_trees::BucketizedSplit* bucketized_split);
  ::tensorflow::boosted_trees::BucketizedSplit* unsafe_arena_release_bucketized_split();

  // .tensorflow.boosted_trees.CategoricalSplit categorical_split = 3;
  bool has_categorical_split() const;
  void clear_categorical_split();
  static const int kCategoricalSplitFieldNumber = 3;
  private:
  const ::tensorflow::boosted_trees::CategoricalSplit& _internal_categorical_split() const;
  public:
  const ::tensorflow::boosted_trees::CategoricalSplit& categorical_split() const;
  ::tensorflow::boosted_trees::CategoricalSplit* release_categorical_split();
  ::tensorflow::boosted_trees::CategoricalSplit* mutable_categorical_split();
  void set_allocated_categorical_split(::tensorflow::boosted_trees::CategoricalSplit* categorical_split);
  void unsafe_arena_set_allocated_categorical_split(
      ::tensorflow::boosted_trees::CategoricalSplit* categorical_split);
  ::tensorflow::boosted_trees::CategoricalSplit* unsafe_arena_release_categorical_split();

  // .tensorflow.boosted_trees.DenseSplit dense_split = 4;
  bool has_dense_split() const;
  void clear_dense_split();
  static const int kDenseSplitFieldNumber = 4;
  private:
  const ::tensorflow::boosted_trees::DenseSplit& _internal_dense_split() const;
  public:
  const ::tensorflow::boosted_trees::DenseSplit& dense_split() const;
  ::tensorflow::boosted_trees::DenseSplit* release_dense_split();
  ::tensorflow::boosted_trees::DenseSplit* mutable_dense_split();
  void set_allocated_dense_split(::tensorflow::boosted_trees::DenseSplit* dense_split);
  void unsafe_arena_set_allocated_dense_split(
      ::tensorflow::boosted_trees::DenseSplit* dense_split);
  ::tensorflow::boosted_trees::DenseSplit* unsafe_arena_release_dense_split();

  void clear_node();
  NodeCase node_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.boosted_trees.Node)
 private:
  void set_has_leaf();
  void set_has_bucketized_split();
  void set_has_categorical_split();
  void set_has_dense_split();

  inline bool has_node() const;
  inline void clear_has_node();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::tensorflow::boosted_trees::NodeMetadata* metadata_;
  union NodeUnion {
    NodeUnion() {}
    ::tensorflow::boosted_trees::Leaf* leaf_;
    ::tensorflow::boosted_trees::BucketizedSplit* bucketized_split_;
    ::tensorflow::boosted_trees::CategoricalSplit* categorical_split_;
    ::tensorflow::boosted_trees::DenseSplit* dense_split_;
  } node_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class NodeMetadata : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.boosted_trees.NodeMetadata) */ {
 public:
  NodeMetadata();
  virtual ~NodeMetadata();

  NodeMetadata(const NodeMetadata& from);

  inline NodeMetadata& operator=(const NodeMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  NodeMetadata(NodeMetadata&& from) noexcept
    : NodeMetadata() {
    *this = ::std::move(from);
  }

  inline NodeMetadata& operator=(NodeMetadata&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const NodeMetadata& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const NodeMetadata* internal_default_instance() {
    return reinterpret_cast<const NodeMetadata*>(
               &_NodeMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void UnsafeArenaSwap(NodeMetadata* other);
  void Swap(NodeMetadata* other);
  friend void swap(NodeMetadata& a, NodeMetadata& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline NodeMetadata* New() const final {
    return CreateMaybeMessage<NodeMetadata>(NULL);
  }

  NodeMetadata* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<NodeMetadata>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const NodeMetadata& from);
  void MergeFrom(const NodeMetadata& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NodeMetadata* other);
  protected:
  explicit NodeMetadata(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .tensorflow.boosted_trees.Leaf original_leaf = 2;
  bool has_original_leaf() const;
  void clear_original_leaf();
  static const int kOriginalLeafFieldNumber = 2;
  private:
  const ::tensorflow::boosted_trees::Leaf& _internal_original_leaf() const;
  public:
  const ::tensorflow::boosted_trees::Leaf& original_leaf() const;
  ::tensorflow::boosted_trees::Leaf* release_original_leaf();
  ::tensorflow::boosted_trees::Leaf* mutable_original_leaf();
  void set_allocated_original_leaf(::tensorflow::boosted_trees::Leaf* original_leaf);
  void unsafe_arena_set_allocated_original_leaf(
      ::tensorflow::boosted_trees::Leaf* original_leaf);
  ::tensorflow::boosted_trees::Leaf* unsafe_arena_release_original_leaf();

  // float gain = 1;
  void clear_gain();
  static const int kGainFieldNumber = 1;
  float gain() const;
  void set_gain(float value);

  // @@protoc_insertion_point(class_scope:tensorflow.boosted_trees.NodeMetadata)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::tensorflow::boosted_trees::Leaf* original_leaf_;
  float gain_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Leaf : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.boosted_trees.Leaf) */ {
 public:
  Leaf();
  virtual ~Leaf();

  Leaf(const Leaf& from);

  inline Leaf& operator=(const Leaf& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Leaf(Leaf&& from) noexcept
    : Leaf() {
    *this = ::std::move(from);
  }

  inline Leaf& operator=(Leaf&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const Leaf& default_instance();

  enum LeafCase {
    kVector = 1,
    kSparseVector = 2,
    LEAF_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Leaf* internal_default_instance() {
    return reinterpret_cast<const Leaf*>(
               &_Leaf_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void UnsafeArenaSwap(Leaf* other);
  void Swap(Leaf* other);
  friend void swap(Leaf& a, Leaf& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Leaf* New() const final {
    return CreateMaybeMessage<Leaf>(NULL);
  }

  Leaf* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Leaf>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Leaf& from);
  void MergeFrom(const Leaf& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Leaf* other);
  protected:
  explicit Leaf(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // float scalar = 3;
  void clear_scalar();
  static const int kScalarFieldNumber = 3;
  float scalar() const;
  void set_scalar(float value);

  // .tensorflow.boosted_trees.Vector vector = 1;
  bool has_vector() const;
  void clear_vector();
  static const int kVectorFieldNumber = 1;
  private:
  const ::tensorflow::boosted_trees::Vector& _internal_vector() const;
  public:
  const ::tensorflow::boosted_trees::Vector& vector() const;
  ::tensorflow::boosted_trees::Vector* release_vector();
  ::tensorflow::boosted_trees::Vector* mutable_vector();
  void set_allocated_vector(::tensorflow::boosted_trees::Vector* vector);
  void unsafe_arena_set_allocated_vector(
      ::tensorflow::boosted_trees::Vector* vector);
  ::tensorflow::boosted_trees::Vector* unsafe_arena_release_vector();

  // .tensorflow.boosted_trees.SparseVector sparse_vector = 2;
  bool has_sparse_vector() const;
  void clear_sparse_vector();
  static const int kSparseVectorFieldNumber = 2;
  private:
  const ::tensorflow::boosted_trees::SparseVector& _internal_sparse_vector() const;
  public:
  const ::tensorflow::boosted_trees::SparseVector& sparse_vector() const;
  ::tensorflow::boosted_trees::SparseVector* release_sparse_vector();
  ::tensorflow::boosted_trees::SparseVector* mutable_sparse_vector();
  void set_allocated_sparse_vector(::tensorflow::boosted_trees::SparseVector* sparse_vector);
  void unsafe_arena_set_allocated_sparse_vector(
      ::tensorflow::boosted_trees::SparseVector* sparse_vector);
  ::tensorflow::boosted_trees::SparseVector* unsafe_arena_release_sparse_vector();

  void clear_leaf();
  LeafCase leaf_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.boosted_trees.Leaf)
 private:
  void set_has_vector();
  void set_has_sparse_vector();

  inline bool has_leaf() const;
  inline void clear_has_leaf();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float scalar_;
  union LeafUnion {
    LeafUnion() {}
    ::tensorflow::boosted_trees::Vector* vector_;
    ::tensorflow::boosted_trees::SparseVector* sparse_vector_;
  } leaf_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Vector : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.boosted_trees.Vector) */ {
 public:
  Vector();
  virtual ~Vector();

  Vector(const Vector& from);

  inline Vector& operator=(const Vector& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Vector(Vector&& from) noexcept
    : Vector() {
    *this = ::std::move(from);
  }

  inline Vector& operator=(Vector&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const Vector& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Vector* internal_default_instance() {
    return reinterpret_cast<const Vector*>(
               &_Vector_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void UnsafeArenaSwap(Vector* other);
  void Swap(Vector* other);
  friend void swap(Vector& a, Vector& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Vector* New() const final {
    return CreateMaybeMessage<Vector>(NULL);
  }

  Vector* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Vector>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Vector& from);
  void MergeFrom(const Vector& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Vector* other);
  protected:
  explicit Vector(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated float value = 1;
  int value_size() const;
  void clear_value();
  static const int kValueFieldNumber = 1;
  float value(int index) const;
  void set_value(int index, float value);
  void add_value(float value);
  const ::google::protobuf::RepeatedField< float >&
      value() const;
  ::google::protobuf::RepeatedField< float >*
      mutable_value();

  // @@protoc_insertion_point(class_scope:tensorflow.boosted_trees.Vector)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< float > value_;
  mutable int _value_cached_byte_size_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class SparseVector : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.boosted_trees.SparseVector) */ {
 public:
  SparseVector();
  virtual ~SparseVector();

  SparseVector(const SparseVector& from);

  inline SparseVector& operator=(const SparseVector& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SparseVector(SparseVector&& from) noexcept
    : SparseVector() {
    *this = ::std::move(from);
  }

  inline SparseVector& operator=(SparseVector&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const SparseVector& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SparseVector* internal_default_instance() {
    return reinterpret_cast<const SparseVector*>(
               &_SparseVector_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void UnsafeArenaSwap(SparseVector* other);
  void Swap(SparseVector* other);
  friend void swap(SparseVector& a, SparseVector& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SparseVector* New() const final {
    return CreateMaybeMessage<SparseVector>(NULL);
  }

  SparseVector* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<SparseVector>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const SparseVector& from);
  void MergeFrom(const SparseVector& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SparseVector* other);
  protected:
  explicit SparseVector(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int32 index = 1;
  int index_size() const;
  void clear_index();
  static const int kIndexFieldNumber = 1;
  ::google::protobuf::int32 index(int index) const;
  void set_index(int index, ::google::protobuf::int32 value);
  void add_index(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      index() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_index();

  // repeated float value = 2;
  int value_size() const;
  void clear_value();
  static const int kValueFieldNumber = 2;
  float value(int index) const;
  void set_value(int index, float value);
  void add_value(float value);
  const ::google::protobuf::RepeatedField< float >&
      value() const;
  ::google::protobuf::RepeatedField< float >*
      mutable_value();

  // @@protoc_insertion_point(class_scope:tensorflow.boosted_trees.SparseVector)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > index_;
  mutable int _index_cached_byte_size_;
  ::google::protobuf::RepeatedField< float > value_;
  mutable int _value_cached_byte_size_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class BucketizedSplit : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.boosted_trees.BucketizedSplit) */ {
 public:
  BucketizedSplit();
  virtual ~BucketizedSplit();

  BucketizedSplit(const BucketizedSplit& from);

  inline BucketizedSplit& operator=(const BucketizedSplit& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  BucketizedSplit(BucketizedSplit&& from) noexcept
    : BucketizedSplit() {
    *this = ::std::move(from);
  }

  inline BucketizedSplit& operator=(BucketizedSplit&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const BucketizedSplit& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const BucketizedSplit* internal_default_instance() {
    return reinterpret_cast<const BucketizedSplit*>(
               &_BucketizedSplit_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  void UnsafeArenaSwap(BucketizedSplit* other);
  void Swap(BucketizedSplit* other);
  friend void swap(BucketizedSplit& a, BucketizedSplit& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline BucketizedSplit* New() const final {
    return CreateMaybeMessage<BucketizedSplit>(NULL);
  }

  BucketizedSplit* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<BucketizedSplit>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const BucketizedSplit& from);
  void MergeFrom(const BucketizedSplit& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BucketizedSplit* other);
  protected:
  explicit BucketizedSplit(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef BucketizedSplit_DefaultDirection DefaultDirection;
  static const DefaultDirection DEFAULT_LEFT =
    BucketizedSplit_DefaultDirection_DEFAULT_LEFT;
  static const DefaultDirection DEFAULT_RIGHT =
    BucketizedSplit_DefaultDirection_DEFAULT_RIGHT;
  static inline bool DefaultDirection_IsValid(int value) {
    return BucketizedSplit_DefaultDirection_IsValid(value);
  }
  static const DefaultDirection DefaultDirection_MIN =
    BucketizedSplit_DefaultDirection_DefaultDirection_MIN;
  static const DefaultDirection DefaultDirection_MAX =
    BucketizedSplit_DefaultDirection_DefaultDirection_MAX;
  static const int DefaultDirection_ARRAYSIZE =
    BucketizedSplit_DefaultDirection_DefaultDirection_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  DefaultDirection_descriptor() {
    return BucketizedSplit_DefaultDirection_descriptor();
  }
  static inline const ::std::string& DefaultDirection_Name(DefaultDirection value) {
    return BucketizedSplit_DefaultDirection_Name(value);
  }
  static inline bool DefaultDirection_Parse(const ::std::string& name,
      DefaultDirection* value) {
    return BucketizedSplit_DefaultDirection_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // int32 feature_id = 1;
  void clear_feature_id();
  static const int kFeatureIdFieldNumber = 1;
  ::google::protobuf::int32 feature_id() const;
  void set_feature_id(::google::protobuf::int32 value);

  // int32 threshold = 2;
  void clear_threshold();
  static const int kThresholdFieldNumber = 2;
  ::google::protobuf::int32 threshold() const;
  void set_threshold(::google::protobuf::int32 value);

  // int32 left_id = 3;
  void clear_left_id();
  static const int kLeftIdFieldNumber = 3;
  ::google::protobuf::int32 left_id() const;
  void set_left_id(::google::protobuf::int32 value);

  // int32 right_id = 4;
  void clear_right_id();
  static const int kRightIdFieldNumber = 4;
  ::google::protobuf::int32 right_id() const;
  void set_right_id(::google::protobuf::int32 value);

  // int32 dimension_id = 5;
  void clear_dimension_id();
  static const int kDimensionIdFieldNumber = 5;
  ::google::protobuf::int32 dimension_id() const;
  void set_dimension_id(::google::protobuf::int32 value);

  // .tensorflow.boosted_trees.BucketizedSplit.DefaultDirection default_direction = 6;
  void clear_default_direction();
  static const int kDefaultDirectionFieldNumber = 6;
  ::tensorflow::boosted_trees::BucketizedSplit_DefaultDirection default_direction() const;
  void set_default_direction(::tensorflow::boosted_trees::BucketizedSplit_DefaultDirection value);

  // @@protoc_insertion_point(class_scope:tensorflow.boosted_trees.BucketizedSplit)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::int32 feature_id_;
  ::google::protobuf::int32 threshold_;
  ::google::protobuf::int32 left_id_;
  ::google::protobuf::int32 right_id_;
  ::google::protobuf::int32 dimension_id_;
  int default_direction_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CategoricalSplit : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.boosted_trees.CategoricalSplit) */ {
 public:
  CategoricalSplit();
  virtual ~CategoricalSplit();

  CategoricalSplit(const CategoricalSplit& from);

  inline CategoricalSplit& operator=(const CategoricalSplit& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CategoricalSplit(CategoricalSplit&& from) noexcept
    : CategoricalSplit() {
    *this = ::std::move(from);
  }

  inline CategoricalSplit& operator=(CategoricalSplit&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const CategoricalSplit& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CategoricalSplit* internal_default_instance() {
    return reinterpret_cast<const CategoricalSplit*>(
               &_CategoricalSplit_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  void UnsafeArenaSwap(CategoricalSplit* other);
  void Swap(CategoricalSplit* other);
  friend void swap(CategoricalSplit& a, CategoricalSplit& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CategoricalSplit* New() const final {
    return CreateMaybeMessage<CategoricalSplit>(NULL);
  }

  CategoricalSplit* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CategoricalSplit>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CategoricalSplit& from);
  void MergeFrom(const CategoricalSplit& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CategoricalSplit* other);
  protected:
  explicit CategoricalSplit(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int32 feature_id = 1;
  void clear_feature_id();
  static const int kFeatureIdFieldNumber = 1;
  ::google::protobuf::int32 feature_id() const;
  void set_feature_id(::google::protobuf::int32 value);

  // int32 value = 2;
  void clear_value();
  static const int kValueFieldNumber = 2;
  ::google::protobuf::int32 value() const;
  void set_value(::google::protobuf::int32 value);

  // int32 left_id = 3;
  void clear_left_id();
  static const int kLeftIdFieldNumber = 3;
  ::google::protobuf::int32 left_id() const;
  void set_left_id(::google::protobuf::int32 value);

  // int32 right_id = 4;
  void clear_right_id();
  static const int kRightIdFieldNumber = 4;
  ::google::protobuf::int32 right_id() const;
  void set_right_id(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.boosted_trees.CategoricalSplit)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::int32 feature_id_;
  ::google::protobuf::int32 value_;
  ::google::protobuf::int32 left_id_;
  ::google::protobuf::int32 right_id_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class DenseSplit : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.boosted_trees.DenseSplit) */ {
 public:
  DenseSplit();
  virtual ~DenseSplit();

  DenseSplit(const DenseSplit& from);

  inline DenseSplit& operator=(const DenseSplit& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  DenseSplit(DenseSplit&& from) noexcept
    : DenseSplit() {
    *this = ::std::move(from);
  }

  inline DenseSplit& operator=(DenseSplit&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const DenseSplit& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DenseSplit* internal_default_instance() {
    return reinterpret_cast<const DenseSplit*>(
               &_DenseSplit_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  void UnsafeArenaSwap(DenseSplit* other);
  void Swap(DenseSplit* other);
  friend void swap(DenseSplit& a, DenseSplit& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline DenseSplit* New() const final {
    return CreateMaybeMessage<DenseSplit>(NULL);
  }

  DenseSplit* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<DenseSplit>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const DenseSplit& from);
  void MergeFrom(const DenseSplit& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DenseSplit* other);
  protected:
  explicit DenseSplit(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int32 feature_id = 1;
  void clear_feature_id();
  static const int kFeatureIdFieldNumber = 1;
  ::google::protobuf::int32 feature_id() const;
  void set_feature_id(::google::protobuf::int32 value);

  // float threshold = 2;
  void clear_threshold();
  static const int kThresholdFieldNumber = 2;
  float threshold() const;
  void set_threshold(float value);

  // int32 left_id = 3;
  void clear_left_id();
  static const int kLeftIdFieldNumber = 3;
  ::google::protobuf::int32 left_id() const;
  void set_left_id(::google::protobuf::int32 value);

  // int32 right_id = 4;
  void clear_right_id();
  static const int kRightIdFieldNumber = 4;
  ::google::protobuf::int32 right_id() const;
  void set_right_id(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.boosted_trees.DenseSplit)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::int32 feature_id_;
  float threshold_;
  ::google::protobuf::int32 left_id_;
  ::google::protobuf::int32 right_id_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Tree : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.boosted_trees.Tree) */ {
 public:
  Tree();
  virtual ~Tree();

  Tree(const Tree& from);

  inline Tree& operator=(const Tree& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Tree(Tree&& from) noexcept
    : Tree() {
    *this = ::std::move(from);
  }

  inline Tree& operator=(Tree&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const Tree& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Tree* internal_default_instance() {
    return reinterpret_cast<const Tree*>(
               &_Tree_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  void UnsafeArenaSwap(Tree* other);
  void Swap(Tree* other);
  friend void swap(Tree& a, Tree& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Tree* New() const final {
    return CreateMaybeMessage<Tree>(NULL);
  }

  Tree* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Tree>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Tree& from);
  void MergeFrom(const Tree& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Tree* other);
  protected:
  explicit Tree(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.boosted_trees.Node nodes = 1;
  int nodes_size() const;
  void clear_nodes();
  static const int kNodesFieldNumber = 1;
  ::tensorflow::boosted_trees::Node* mutable_nodes(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::boosted_trees::Node >*
      mutable_nodes();
  const ::tensorflow::boosted_trees::Node& nodes(int index) const;
  ::tensorflow::boosted_trees::Node* add_nodes();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::boosted_trees::Node >&
      nodes() const;

  // @@protoc_insertion_point(class_scope:tensorflow.boosted_trees.Tree)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::boosted_trees::Node > nodes_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class TreeMetadata_PostPruneNodeUpdate : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate) */ {
 public:
  TreeMetadata_PostPruneNodeUpdate();
  virtual ~TreeMetadata_PostPruneNodeUpdate();

  TreeMetadata_PostPruneNodeUpdate(const TreeMetadata_PostPruneNodeUpdate& from);

  inline TreeMetadata_PostPruneNodeUpdate& operator=(const TreeMetadata_PostPruneNodeUpdate& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TreeMetadata_PostPruneNodeUpdate(TreeMetadata_PostPruneNodeUpdate&& from) noexcept
    : TreeMetadata_PostPruneNodeUpdate() {
    *this = ::std::move(from);
  }

  inline TreeMetadata_PostPruneNodeUpdate& operator=(TreeMetadata_PostPruneNodeUpdate&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TreeMetadata_PostPruneNodeUpdate& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TreeMetadata_PostPruneNodeUpdate* internal_default_instance() {
    return reinterpret_cast<const TreeMetadata_PostPruneNodeUpdate*>(
               &_TreeMetadata_PostPruneNodeUpdate_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  void UnsafeArenaSwap(TreeMetadata_PostPruneNodeUpdate* other);
  void Swap(TreeMetadata_PostPruneNodeUpdate* other);
  friend void swap(TreeMetadata_PostPruneNodeUpdate& a, TreeMetadata_PostPruneNodeUpdate& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TreeMetadata_PostPruneNodeUpdate* New() const final {
    return CreateMaybeMessage<TreeMetadata_PostPruneNodeUpdate>(NULL);
  }

  TreeMetadata_PostPruneNodeUpdate* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TreeMetadata_PostPruneNodeUpdate>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TreeMetadata_PostPruneNodeUpdate& from);
  void MergeFrom(const TreeMetadata_PostPruneNodeUpdate& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TreeMetadata_PostPruneNodeUpdate* other);
  protected:
  explicit TreeMetadata_PostPruneNodeUpdate(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int32 new_node_id = 1;
  void clear_new_node_id();
  static const int kNewNodeIdFieldNumber = 1;
  ::google::protobuf::int32 new_node_id() const;
  void set_new_node_id(::google::protobuf::int32 value);

  // float logit_change = 2;
  void clear_logit_change();
  static const int kLogitChangeFieldNumber = 2;
  float logit_change() const;
  void set_logit_change(float value);

  // @@protoc_insertion_point(class_scope:tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::int32 new_node_id_;
  float logit_change_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class TreeMetadata : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.boosted_trees.TreeMetadata) */ {
 public:
  TreeMetadata();
  virtual ~TreeMetadata();

  TreeMetadata(const TreeMetadata& from);

  inline TreeMetadata& operator=(const TreeMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TreeMetadata(TreeMetadata&& from) noexcept
    : TreeMetadata() {
    *this = ::std::move(from);
  }

  inline TreeMetadata& operator=(TreeMetadata&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TreeMetadata& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TreeMetadata* internal_default_instance() {
    return reinterpret_cast<const TreeMetadata*>(
               &_TreeMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  void UnsafeArenaSwap(TreeMetadata* other);
  void Swap(TreeMetadata* other);
  friend void swap(TreeMetadata& a, TreeMetadata& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TreeMetadata* New() const final {
    return CreateMaybeMessage<TreeMetadata>(NULL);
  }

  TreeMetadata* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TreeMetadata>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TreeMetadata& from);
  void MergeFrom(const TreeMetadata& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TreeMetadata* other);
  protected:
  explicit TreeMetadata(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef TreeMetadata_PostPruneNodeUpdate PostPruneNodeUpdate;

  // accessors -------------------------------------------------------

  // repeated .tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate post_pruned_nodes_meta = 4;
  int post_pruned_nodes_meta_size() const;
  void clear_post_pruned_nodes_meta();
  static const int kPostPrunedNodesMetaFieldNumber = 4;
  ::tensorflow::boosted_trees::TreeMetadata_PostPruneNodeUpdate* mutable_post_pruned_nodes_meta(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::boosted_trees::TreeMetadata_PostPruneNodeUpdate >*
      mutable_post_pruned_nodes_meta();
  const ::tensorflow::boosted_trees::TreeMetadata_PostPruneNodeUpdate& post_pruned_nodes_meta(int index) const;
  ::tensorflow::boosted_trees::TreeMetadata_PostPruneNodeUpdate* add_post_pruned_nodes_meta();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::boosted_trees::TreeMetadata_PostPruneNodeUpdate >&
      post_pruned_nodes_meta() const;

  // int32 num_layers_grown = 2;
  void clear_num_layers_grown();
  static const int kNumLayersGrownFieldNumber = 2;
  ::google::protobuf::int32 num_layers_grown() const;
  void set_num_layers_grown(::google::protobuf::int32 value);

  // bool is_finalized = 3;
  void clear_is_finalized();
  static const int kIsFinalizedFieldNumber = 3;
  bool is_finalized() const;
  void set_is_finalized(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.boosted_trees.TreeMetadata)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::boosted_trees::TreeMetadata_PostPruneNodeUpdate > post_pruned_nodes_meta_;
  ::google::protobuf::int32 num_layers_grown_;
  bool is_finalized_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class GrowingMetadata : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.boosted_trees.GrowingMetadata) */ {
 public:
  GrowingMetadata();
  virtual ~GrowingMetadata();

  GrowingMetadata(const GrowingMetadata& from);

  inline GrowingMetadata& operator=(const GrowingMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  GrowingMetadata(GrowingMetadata&& from) noexcept
    : GrowingMetadata() {
    *this = ::std::move(from);
  }

  inline GrowingMetadata& operator=(GrowingMetadata&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const GrowingMetadata& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GrowingMetadata* internal_default_instance() {
    return reinterpret_cast<const GrowingMetadata*>(
               &_GrowingMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  void UnsafeArenaSwap(GrowingMetadata* other);
  void Swap(GrowingMetadata* other);
  friend void swap(GrowingMetadata& a, GrowingMetadata& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline GrowingMetadata* New() const final {
    return CreateMaybeMessage<GrowingMetadata>(NULL);
  }

  GrowingMetadata* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<GrowingMetadata>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const GrowingMetadata& from);
  void MergeFrom(const GrowingMetadata& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GrowingMetadata* other);
  protected:
  explicit GrowingMetadata(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 num_trees_attempted = 1;
  void clear_num_trees_attempted();
  static const int kNumTreesAttemptedFieldNumber = 1;
  ::google::protobuf::int64 num_trees_attempted() const;
  void set_num_trees_attempted(::google::protobuf::int64 value);

  // int64 num_layers_attempted = 2;
  void clear_num_layers_attempted();
  static const int kNumLayersAttemptedFieldNumber = 2;
  ::google::protobuf::int64 num_layers_attempted() const;
  void set_num_layers_attempted(::google::protobuf::int64 value);

  // int32 last_layer_node_start = 3;
  void clear_last_layer_node_start();
  static const int kLastLayerNodeStartFieldNumber = 3;
  ::google::protobuf::int32 last_layer_node_start() const;
  void set_last_layer_node_start(::google::protobuf::int32 value);

  // int32 last_layer_node_end = 4;
  void clear_last_layer_node_end();
  static const int kLastLayerNodeEndFieldNumber = 4;
  ::google::protobuf::int32 last_layer_node_end() const;
  void set_last_layer_node_end(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.boosted_trees.GrowingMetadata)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::int64 num_trees_attempted_;
  ::google::protobuf::int64 num_layers_attempted_;
  ::google::protobuf::int32 last_layer_node_start_;
  ::google::protobuf::int32 last_layer_node_end_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class TreeEnsemble : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.boosted_trees.TreeEnsemble) */ {
 public:
  TreeEnsemble();
  virtual ~TreeEnsemble();

  TreeEnsemble(const TreeEnsemble& from);

  inline TreeEnsemble& operator=(const TreeEnsemble& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TreeEnsemble(TreeEnsemble&& from) noexcept
    : TreeEnsemble() {
    *this = ::std::move(from);
  }

  inline TreeEnsemble& operator=(TreeEnsemble&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TreeEnsemble& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TreeEnsemble* internal_default_instance() {
    return reinterpret_cast<const TreeEnsemble*>(
               &_TreeEnsemble_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  void UnsafeArenaSwap(TreeEnsemble* other);
  void Swap(TreeEnsemble* other);
  friend void swap(TreeEnsemble& a, TreeEnsemble& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TreeEnsemble* New() const final {
    return CreateMaybeMessage<TreeEnsemble>(NULL);
  }

  TreeEnsemble* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TreeEnsemble>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TreeEnsemble& from);
  void MergeFrom(const TreeEnsemble& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TreeEnsemble* other);
  protected:
  explicit TreeEnsemble(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.boosted_trees.Tree trees = 1;
  int trees_size() const;
  void clear_trees();
  static const int kTreesFieldNumber = 1;
  ::tensorflow::boosted_trees::Tree* mutable_trees(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::boosted_trees::Tree >*
      mutable_trees();
  const ::tensorflow::boosted_trees::Tree& trees(int index) const;
  ::tensorflow::boosted_trees::Tree* add_trees();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::boosted_trees::Tree >&
      trees() const;

  // repeated float tree_weights = 2;
  int tree_weights_size() const;
  void clear_tree_weights();
  static const int kTreeWeightsFieldNumber = 2;
  float tree_weights(int index) const;
  void set_tree_weights(int index, float value);
  void add_tree_weights(float value);
  const ::google::protobuf::RepeatedField< float >&
      tree_weights() const;
  ::google::protobuf::RepeatedField< float >*
      mutable_tree_weights();

  // repeated .tensorflow.boosted_trees.TreeMetadata tree_metadata = 3;
  int tree_metadata_size() const;
  void clear_tree_metadata();
  static const int kTreeMetadataFieldNumber = 3;
  ::tensorflow::boosted_trees::TreeMetadata* mutable_tree_metadata(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::boosted_trees::TreeMetadata >*
      mutable_tree_metadata();
  const ::tensorflow::boosted_trees::TreeMetadata& tree_metadata(int index) const;
  ::tensorflow::boosted_trees::TreeMetadata* add_tree_metadata();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::boosted_trees::TreeMetadata >&
      tree_metadata() const;

  // .tensorflow.boosted_trees.GrowingMetadata growing_metadata = 4;
  bool has_growing_metadata() const;
  void clear_growing_metadata();
  static const int kGrowingMetadataFieldNumber = 4;
  private:
  const ::tensorflow::boosted_trees::GrowingMetadata& _internal_growing_metadata() const;
  public:
  const ::tensorflow::boosted_trees::GrowingMetadata& growing_metadata() const;
  ::tensorflow::boosted_trees::GrowingMetadata* release_growing_metadata();
  ::tensorflow::boosted_trees::GrowingMetadata* mutable_growing_metadata();
  void set_allocated_growing_metadata(::tensorflow::boosted_trees::GrowingMetadata* growing_metadata);
  void unsafe_arena_set_allocated_growing_metadata(
      ::tensorflow::boosted_trees::GrowingMetadata* growing_metadata);
  ::tensorflow::boosted_trees::GrowingMetadata* unsafe_arena_release_growing_metadata();

  // @@protoc_insertion_point(class_scope:tensorflow.boosted_trees.TreeEnsemble)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::boosted_trees::Tree > trees_;
  ::google::protobuf::RepeatedField< float > tree_weights_;
  mutable int _tree_weights_cached_byte_size_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::boosted_trees::TreeMetadata > tree_metadata_;
  ::tensorflow::boosted_trees::GrowingMetadata* growing_metadata_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class DebugOutput : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.boosted_trees.DebugOutput) */ {
 public:
  DebugOutput();
  virtual ~DebugOutput();

  DebugOutput(const DebugOutput& from);

  inline DebugOutput& operator=(const DebugOutput& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  DebugOutput(DebugOutput&& from) noexcept
    : DebugOutput() {
    *this = ::std::move(from);
  }

  inline DebugOutput& operator=(DebugOutput&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const DebugOutput& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DebugOutput* internal_default_instance() {
    return reinterpret_cast<const DebugOutput*>(
               &_DebugOutput_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  void UnsafeArenaSwap(DebugOutput* other);
  void Swap(DebugOutput* other);
  friend void swap(DebugOutput& a, DebugOutput& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline DebugOutput* New() const final {
    return CreateMaybeMessage<DebugOutput>(NULL);
  }

  DebugOutput* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<DebugOutput>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const DebugOutput& from);
  void MergeFrom(const DebugOutput& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DebugOutput* other);
  protected:
  explicit DebugOutput(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int32 feature_ids = 1;
  int feature_ids_size() const;
  void clear_feature_ids();
  static const int kFeatureIdsFieldNumber = 1;
  ::google::protobuf::int32 feature_ids(int index) const;
  void set_feature_ids(int index, ::google::protobuf::int32 value);
  void add_feature_ids(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      feature_ids() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_feature_ids();

  // repeated float logits_path = 2;
  int logits_path_size() const;
  void clear_logits_path();
  static const int kLogitsPathFieldNumber = 2;
  float logits_path(int index) const;
  void set_logits_path(int index, float value);
  void add_logits_path(float value);
  const ::google::protobuf::RepeatedField< float >&
      logits_path() const;
  ::google::protobuf::RepeatedField< float >*
      mutable_logits_path();

  // @@protoc_insertion_point(class_scope:tensorflow.boosted_trees.DebugOutput)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > feature_ids_;
  mutable int _feature_ids_cached_byte_size_;
  ::google::protobuf::RepeatedField< float > logits_path_;
  mutable int _logits_path_cached_byte_size_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Node

// .tensorflow.boosted_trees.Leaf leaf = 1;
inline bool Node::has_leaf() const {
  return node_case() == kLeaf;
}
inline void Node::set_has_leaf() {
  _oneof_case_[0] = kLeaf;
}
inline void Node::clear_leaf() {
  if (has_leaf()) {
    if (GetArenaNoVirtual() == NULL) {
      delete node_.leaf_;
    }
    clear_has_node();
  }
}
inline const ::tensorflow::boosted_trees::Leaf& Node::_internal_leaf() const {
  return *node_.leaf_;
}
inline ::tensorflow::boosted_trees::Leaf* Node::release_leaf() {
  // @@protoc_insertion_point(field_release:tensorflow.boosted_trees.Node.leaf)
  if (has_leaf()) {
    clear_has_node();
      ::tensorflow::boosted_trees::Leaf* temp = node_.leaf_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    node_.leaf_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::boosted_trees::Leaf& Node::leaf() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.Node.leaf)
  return has_leaf()
      ? *node_.leaf_
      : *reinterpret_cast< ::tensorflow::boosted_trees::Leaf*>(&::tensorflow::boosted_trees::_Leaf_default_instance_);
}
inline ::tensorflow::boosted_trees::Leaf* Node::unsafe_arena_release_leaf() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.boosted_trees.Node.leaf)
  if (has_leaf()) {
    clear_has_node();
    ::tensorflow::boosted_trees::Leaf* temp = node_.leaf_;
    node_.leaf_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void Node::unsafe_arena_set_allocated_leaf(::tensorflow::boosted_trees::Leaf* leaf) {
  clear_node();
  if (leaf) {
    set_has_leaf();
    node_.leaf_ = leaf;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.boosted_trees.Node.leaf)
}
inline ::tensorflow::boosted_trees::Leaf* Node::mutable_leaf() {
  if (!has_leaf()) {
    clear_node();
    set_has_leaf();
    node_.leaf_ = CreateMaybeMessage< ::tensorflow::boosted_trees::Leaf >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.boosted_trees.Node.leaf)
  return node_.leaf_;
}

// .tensorflow.boosted_trees.BucketizedSplit bucketized_split = 2;
inline bool Node::has_bucketized_split() const {
  return node_case() == kBucketizedSplit;
}
inline void Node::set_has_bucketized_split() {
  _oneof_case_[0] = kBucketizedSplit;
}
inline void Node::clear_bucketized_split() {
  if (has_bucketized_split()) {
    if (GetArenaNoVirtual() == NULL) {
      delete node_.bucketized_split_;
    }
    clear_has_node();
  }
}
inline const ::tensorflow::boosted_trees::BucketizedSplit& Node::_internal_bucketized_split() const {
  return *node_.bucketized_split_;
}
inline ::tensorflow::boosted_trees::BucketizedSplit* Node::release_bucketized_split() {
  // @@protoc_insertion_point(field_release:tensorflow.boosted_trees.Node.bucketized_split)
  if (has_bucketized_split()) {
    clear_has_node();
      ::tensorflow::boosted_trees::BucketizedSplit* temp = node_.bucketized_split_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    node_.bucketized_split_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::boosted_trees::BucketizedSplit& Node::bucketized_split() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.Node.bucketized_split)
  return has_bucketized_split()
      ? *node_.bucketized_split_
      : *reinterpret_cast< ::tensorflow::boosted_trees::BucketizedSplit*>(&::tensorflow::boosted_trees::_BucketizedSplit_default_instance_);
}
inline ::tensorflow::boosted_trees::BucketizedSplit* Node::unsafe_arena_release_bucketized_split() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.boosted_trees.Node.bucketized_split)
  if (has_bucketized_split()) {
    clear_has_node();
    ::tensorflow::boosted_trees::BucketizedSplit* temp = node_.bucketized_split_;
    node_.bucketized_split_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void Node::unsafe_arena_set_allocated_bucketized_split(::tensorflow::boosted_trees::BucketizedSplit* bucketized_split) {
  clear_node();
  if (bucketized_split) {
    set_has_bucketized_split();
    node_.bucketized_split_ = bucketized_split;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.boosted_trees.Node.bucketized_split)
}
inline ::tensorflow::boosted_trees::BucketizedSplit* Node::mutable_bucketized_split() {
  if (!has_bucketized_split()) {
    clear_node();
    set_has_bucketized_split();
    node_.bucketized_split_ = CreateMaybeMessage< ::tensorflow::boosted_trees::BucketizedSplit >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.boosted_trees.Node.bucketized_split)
  return node_.bucketized_split_;
}

// .tensorflow.boosted_trees.CategoricalSplit categorical_split = 3;
inline bool Node::has_categorical_split() const {
  return node_case() == kCategoricalSplit;
}
inline void Node::set_has_categorical_split() {
  _oneof_case_[0] = kCategoricalSplit;
}
inline void Node::clear_categorical_split() {
  if (has_categorical_split()) {
    if (GetArenaNoVirtual() == NULL) {
      delete node_.categorical_split_;
    }
    clear_has_node();
  }
}
inline const ::tensorflow::boosted_trees::CategoricalSplit& Node::_internal_categorical_split() const {
  return *node_.categorical_split_;
}
inline ::tensorflow::boosted_trees::CategoricalSplit* Node::release_categorical_split() {
  // @@protoc_insertion_point(field_release:tensorflow.boosted_trees.Node.categorical_split)
  if (has_categorical_split()) {
    clear_has_node();
      ::tensorflow::boosted_trees::CategoricalSplit* temp = node_.categorical_split_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    node_.categorical_split_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::boosted_trees::CategoricalSplit& Node::categorical_split() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.Node.categorical_split)
  return has_categorical_split()
      ? *node_.categorical_split_
      : *reinterpret_cast< ::tensorflow::boosted_trees::CategoricalSplit*>(&::tensorflow::boosted_trees::_CategoricalSplit_default_instance_);
}
inline ::tensorflow::boosted_trees::CategoricalSplit* Node::unsafe_arena_release_categorical_split() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.boosted_trees.Node.categorical_split)
  if (has_categorical_split()) {
    clear_has_node();
    ::tensorflow::boosted_trees::CategoricalSplit* temp = node_.categorical_split_;
    node_.categorical_split_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void Node::unsafe_arena_set_allocated_categorical_split(::tensorflow::boosted_trees::CategoricalSplit* categorical_split) {
  clear_node();
  if (categorical_split) {
    set_has_categorical_split();
    node_.categorical_split_ = categorical_split;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.boosted_trees.Node.categorical_split)
}
inline ::tensorflow::boosted_trees::CategoricalSplit* Node::mutable_categorical_split() {
  if (!has_categorical_split()) {
    clear_node();
    set_has_categorical_split();
    node_.categorical_split_ = CreateMaybeMessage< ::tensorflow::boosted_trees::CategoricalSplit >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.boosted_trees.Node.categorical_split)
  return node_.categorical_split_;
}

// .tensorflow.boosted_trees.DenseSplit dense_split = 4;
inline bool Node::has_dense_split() const {
  return node_case() == kDenseSplit;
}
inline void Node::set_has_dense_split() {
  _oneof_case_[0] = kDenseSplit;
}
inline void Node::clear_dense_split() {
  if (has_dense_split()) {
    if (GetArenaNoVirtual() == NULL) {
      delete node_.dense_split_;
    }
    clear_has_node();
  }
}
inline const ::tensorflow::boosted_trees::DenseSplit& Node::_internal_dense_split() const {
  return *node_.dense_split_;
}
inline ::tensorflow::boosted_trees::DenseSplit* Node::release_dense_split() {
  // @@protoc_insertion_point(field_release:tensorflow.boosted_trees.Node.dense_split)
  if (has_dense_split()) {
    clear_has_node();
      ::tensorflow::boosted_trees::DenseSplit* temp = node_.dense_split_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    node_.dense_split_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::boosted_trees::DenseSplit& Node::dense_split() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.Node.dense_split)
  return has_dense_split()
      ? *node_.dense_split_
      : *reinterpret_cast< ::tensorflow::boosted_trees::DenseSplit*>(&::tensorflow::boosted_trees::_DenseSplit_default_instance_);
}
inline ::tensorflow::boosted_trees::DenseSplit* Node::unsafe_arena_release_dense_split() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.boosted_trees.Node.dense_split)
  if (has_dense_split()) {
    clear_has_node();
    ::tensorflow::boosted_trees::DenseSplit* temp = node_.dense_split_;
    node_.dense_split_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void Node::unsafe_arena_set_allocated_dense_split(::tensorflow::boosted_trees::DenseSplit* dense_split) {
  clear_node();
  if (dense_split) {
    set_has_dense_split();
    node_.dense_split_ = dense_split;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.boosted_trees.Node.dense_split)
}
inline ::tensorflow::boosted_trees::DenseSplit* Node::mutable_dense_split() {
  if (!has_dense_split()) {
    clear_node();
    set_has_dense_split();
    node_.dense_split_ = CreateMaybeMessage< ::tensorflow::boosted_trees::DenseSplit >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.boosted_trees.Node.dense_split)
  return node_.dense_split_;
}

// .tensorflow.boosted_trees.NodeMetadata metadata = 777;
inline bool Node::has_metadata() const {
  return this != internal_default_instance() && metadata_ != NULL;
}
inline void Node::clear_metadata() {
  if (GetArenaNoVirtual() == NULL && metadata_ != NULL) {
    delete metadata_;
  }
  metadata_ = NULL;
}
inline const ::tensorflow::boosted_trees::NodeMetadata& Node::_internal_metadata() const {
  return *metadata_;
}
inline const ::tensorflow::boosted_trees::NodeMetadata& Node::metadata() const {
  const ::tensorflow::boosted_trees::NodeMetadata* p = metadata_;
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.Node.metadata)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::boosted_trees::NodeMetadata*>(
      &::tensorflow::boosted_trees::_NodeMetadata_default_instance_);
}
inline ::tensorflow::boosted_trees::NodeMetadata* Node::release_metadata() {
  // @@protoc_insertion_point(field_release:tensorflow.boosted_trees.Node.metadata)
  
  ::tensorflow::boosted_trees::NodeMetadata* temp = metadata_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  metadata_ = NULL;
  return temp;
}
inline ::tensorflow::boosted_trees::NodeMetadata* Node::unsafe_arena_release_metadata() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.boosted_trees.Node.metadata)
  
  ::tensorflow::boosted_trees::NodeMetadata* temp = metadata_;
  metadata_ = NULL;
  return temp;
}
inline ::tensorflow::boosted_trees::NodeMetadata* Node::mutable_metadata() {
  
  if (metadata_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::boosted_trees::NodeMetadata>(GetArenaNoVirtual());
    metadata_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.boosted_trees.Node.metadata)
  return metadata_;
}
inline void Node::set_allocated_metadata(::tensorflow::boosted_trees::NodeMetadata* metadata) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete metadata_;
  }
  if (metadata) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(metadata);
    if (message_arena != submessage_arena) {
      metadata = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, metadata, submessage_arena);
    }
    
  } else {
    
  }
  metadata_ = metadata;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.boosted_trees.Node.metadata)
}

inline bool Node::has_node() const {
  return node_case() != NODE_NOT_SET;
}
inline void Node::clear_has_node() {
  _oneof_case_[0] = NODE_NOT_SET;
}
inline Node::NodeCase Node::node_case() const {
  return Node::NodeCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// NodeMetadata

// float gain = 1;
inline void NodeMetadata::clear_gain() {
  gain_ = 0;
}
inline float NodeMetadata::gain() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.NodeMetadata.gain)
  return gain_;
}
inline void NodeMetadata::set_gain(float value) {
  
  gain_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.NodeMetadata.gain)
}

// .tensorflow.boosted_trees.Leaf original_leaf = 2;
inline bool NodeMetadata::has_original_leaf() const {
  return this != internal_default_instance() && original_leaf_ != NULL;
}
inline void NodeMetadata::clear_original_leaf() {
  if (GetArenaNoVirtual() == NULL && original_leaf_ != NULL) {
    delete original_leaf_;
  }
  original_leaf_ = NULL;
}
inline const ::tensorflow::boosted_trees::Leaf& NodeMetadata::_internal_original_leaf() const {
  return *original_leaf_;
}
inline const ::tensorflow::boosted_trees::Leaf& NodeMetadata::original_leaf() const {
  const ::tensorflow::boosted_trees::Leaf* p = original_leaf_;
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.NodeMetadata.original_leaf)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::boosted_trees::Leaf*>(
      &::tensorflow::boosted_trees::_Leaf_default_instance_);
}
inline ::tensorflow::boosted_trees::Leaf* NodeMetadata::release_original_leaf() {
  // @@protoc_insertion_point(field_release:tensorflow.boosted_trees.NodeMetadata.original_leaf)
  
  ::tensorflow::boosted_trees::Leaf* temp = original_leaf_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  original_leaf_ = NULL;
  return temp;
}
inline ::tensorflow::boosted_trees::Leaf* NodeMetadata::unsafe_arena_release_original_leaf() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.boosted_trees.NodeMetadata.original_leaf)
  
  ::tensorflow::boosted_trees::Leaf* temp = original_leaf_;
  original_leaf_ = NULL;
  return temp;
}
inline ::tensorflow::boosted_trees::Leaf* NodeMetadata::mutable_original_leaf() {
  
  if (original_leaf_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::boosted_trees::Leaf>(GetArenaNoVirtual());
    original_leaf_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.boosted_trees.NodeMetadata.original_leaf)
  return original_leaf_;
}
inline void NodeMetadata::set_allocated_original_leaf(::tensorflow::boosted_trees::Leaf* original_leaf) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete original_leaf_;
  }
  if (original_leaf) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(original_leaf);
    if (message_arena != submessage_arena) {
      original_leaf = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, original_leaf, submessage_arena);
    }
    
  } else {
    
  }
  original_leaf_ = original_leaf;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.boosted_trees.NodeMetadata.original_leaf)
}

// -------------------------------------------------------------------

// Leaf

// .tensorflow.boosted_trees.Vector vector = 1;
inline bool Leaf::has_vector() const {
  return leaf_case() == kVector;
}
inline void Leaf::set_has_vector() {
  _oneof_case_[0] = kVector;
}
inline void Leaf::clear_vector() {
  if (has_vector()) {
    if (GetArenaNoVirtual() == NULL) {
      delete leaf_.vector_;
    }
    clear_has_leaf();
  }
}
inline const ::tensorflow::boosted_trees::Vector& Leaf::_internal_vector() const {
  return *leaf_.vector_;
}
inline ::tensorflow::boosted_trees::Vector* Leaf::release_vector() {
  // @@protoc_insertion_point(field_release:tensorflow.boosted_trees.Leaf.vector)
  if (has_vector()) {
    clear_has_leaf();
      ::tensorflow::boosted_trees::Vector* temp = leaf_.vector_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    leaf_.vector_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::boosted_trees::Vector& Leaf::vector() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.Leaf.vector)
  return has_vector()
      ? *leaf_.vector_
      : *reinterpret_cast< ::tensorflow::boosted_trees::Vector*>(&::tensorflow::boosted_trees::_Vector_default_instance_);
}
inline ::tensorflow::boosted_trees::Vector* Leaf::unsafe_arena_release_vector() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.boosted_trees.Leaf.vector)
  if (has_vector()) {
    clear_has_leaf();
    ::tensorflow::boosted_trees::Vector* temp = leaf_.vector_;
    leaf_.vector_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void Leaf::unsafe_arena_set_allocated_vector(::tensorflow::boosted_trees::Vector* vector) {
  clear_leaf();
  if (vector) {
    set_has_vector();
    leaf_.vector_ = vector;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.boosted_trees.Leaf.vector)
}
inline ::tensorflow::boosted_trees::Vector* Leaf::mutable_vector() {
  if (!has_vector()) {
    clear_leaf();
    set_has_vector();
    leaf_.vector_ = CreateMaybeMessage< ::tensorflow::boosted_trees::Vector >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.boosted_trees.Leaf.vector)
  return leaf_.vector_;
}

// .tensorflow.boosted_trees.SparseVector sparse_vector = 2;
inline bool Leaf::has_sparse_vector() const {
  return leaf_case() == kSparseVector;
}
inline void Leaf::set_has_sparse_vector() {
  _oneof_case_[0] = kSparseVector;
}
inline void Leaf::clear_sparse_vector() {
  if (has_sparse_vector()) {
    if (GetArenaNoVirtual() == NULL) {
      delete leaf_.sparse_vector_;
    }
    clear_has_leaf();
  }
}
inline const ::tensorflow::boosted_trees::SparseVector& Leaf::_internal_sparse_vector() const {
  return *leaf_.sparse_vector_;
}
inline ::tensorflow::boosted_trees::SparseVector* Leaf::release_sparse_vector() {
  // @@protoc_insertion_point(field_release:tensorflow.boosted_trees.Leaf.sparse_vector)
  if (has_sparse_vector()) {
    clear_has_leaf();
      ::tensorflow::boosted_trees::SparseVector* temp = leaf_.sparse_vector_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    leaf_.sparse_vector_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::boosted_trees::SparseVector& Leaf::sparse_vector() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.Leaf.sparse_vector)
  return has_sparse_vector()
      ? *leaf_.sparse_vector_
      : *reinterpret_cast< ::tensorflow::boosted_trees::SparseVector*>(&::tensorflow::boosted_trees::_SparseVector_default_instance_);
}
inline ::tensorflow::boosted_trees::SparseVector* Leaf::unsafe_arena_release_sparse_vector() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.boosted_trees.Leaf.sparse_vector)
  if (has_sparse_vector()) {
    clear_has_leaf();
    ::tensorflow::boosted_trees::SparseVector* temp = leaf_.sparse_vector_;
    leaf_.sparse_vector_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void Leaf::unsafe_arena_set_allocated_sparse_vector(::tensorflow::boosted_trees::SparseVector* sparse_vector) {
  clear_leaf();
  if (sparse_vector) {
    set_has_sparse_vector();
    leaf_.sparse_vector_ = sparse_vector;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.boosted_trees.Leaf.sparse_vector)
}
inline ::tensorflow::boosted_trees::SparseVector* Leaf::mutable_sparse_vector() {
  if (!has_sparse_vector()) {
    clear_leaf();
    set_has_sparse_vector();
    leaf_.sparse_vector_ = CreateMaybeMessage< ::tensorflow::boosted_trees::SparseVector >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.boosted_trees.Leaf.sparse_vector)
  return leaf_.sparse_vector_;
}

// float scalar = 3;
inline void Leaf::clear_scalar() {
  scalar_ = 0;
}
inline float Leaf::scalar() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.Leaf.scalar)
  return scalar_;
}
inline void Leaf::set_scalar(float value) {
  
  scalar_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.Leaf.scalar)
}

inline bool Leaf::has_leaf() const {
  return leaf_case() != LEAF_NOT_SET;
}
inline void Leaf::clear_has_leaf() {
  _oneof_case_[0] = LEAF_NOT_SET;
}
inline Leaf::LeafCase Leaf::leaf_case() const {
  return Leaf::LeafCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// Vector

// repeated float value = 1;
inline int Vector::value_size() const {
  return value_.size();
}
inline void Vector::clear_value() {
  value_.Clear();
}
inline float Vector::value(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.Vector.value)
  return value_.Get(index);
}
inline void Vector::set_value(int index, float value) {
  value_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.Vector.value)
}
inline void Vector::add_value(float value) {
  value_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.boosted_trees.Vector.value)
}
inline const ::google::protobuf::RepeatedField< float >&
Vector::value() const {
  // @@protoc_insertion_point(field_list:tensorflow.boosted_trees.Vector.value)
  return value_;
}
inline ::google::protobuf::RepeatedField< float >*
Vector::mutable_value() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.boosted_trees.Vector.value)
  return &value_;
}

// -------------------------------------------------------------------

// SparseVector

// repeated int32 index = 1;
inline int SparseVector::index_size() const {
  return index_.size();
}
inline void SparseVector::clear_index() {
  index_.Clear();
}
inline ::google::protobuf::int32 SparseVector::index(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.SparseVector.index)
  return index_.Get(index);
}
inline void SparseVector::set_index(int index, ::google::protobuf::int32 value) {
  index_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.SparseVector.index)
}
inline void SparseVector::add_index(::google::protobuf::int32 value) {
  index_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.boosted_trees.SparseVector.index)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
SparseVector::index() const {
  // @@protoc_insertion_point(field_list:tensorflow.boosted_trees.SparseVector.index)
  return index_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
SparseVector::mutable_index() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.boosted_trees.SparseVector.index)
  return &index_;
}

// repeated float value = 2;
inline int SparseVector::value_size() const {
  return value_.size();
}
inline void SparseVector::clear_value() {
  value_.Clear();
}
inline float SparseVector::value(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.SparseVector.value)
  return value_.Get(index);
}
inline void SparseVector::set_value(int index, float value) {
  value_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.SparseVector.value)
}
inline void SparseVector::add_value(float value) {
  value_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.boosted_trees.SparseVector.value)
}
inline const ::google::protobuf::RepeatedField< float >&
SparseVector::value() const {
  // @@protoc_insertion_point(field_list:tensorflow.boosted_trees.SparseVector.value)
  return value_;
}
inline ::google::protobuf::RepeatedField< float >*
SparseVector::mutable_value() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.boosted_trees.SparseVector.value)
  return &value_;
}

// -------------------------------------------------------------------

// BucketizedSplit

// int32 feature_id = 1;
inline void BucketizedSplit::clear_feature_id() {
  feature_id_ = 0;
}
inline ::google::protobuf::int32 BucketizedSplit::feature_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.BucketizedSplit.feature_id)
  return feature_id_;
}
inline void BucketizedSplit::set_feature_id(::google::protobuf::int32 value) {
  
  feature_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.BucketizedSplit.feature_id)
}

// int32 threshold = 2;
inline void BucketizedSplit::clear_threshold() {
  threshold_ = 0;
}
inline ::google::protobuf::int32 BucketizedSplit::threshold() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.BucketizedSplit.threshold)
  return threshold_;
}
inline void BucketizedSplit::set_threshold(::google::protobuf::int32 value) {
  
  threshold_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.BucketizedSplit.threshold)
}

// int32 dimension_id = 5;
inline void BucketizedSplit::clear_dimension_id() {
  dimension_id_ = 0;
}
inline ::google::protobuf::int32 BucketizedSplit::dimension_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.BucketizedSplit.dimension_id)
  return dimension_id_;
}
inline void BucketizedSplit::set_dimension_id(::google::protobuf::int32 value) {
  
  dimension_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.BucketizedSplit.dimension_id)
}

// .tensorflow.boosted_trees.BucketizedSplit.DefaultDirection default_direction = 6;
inline void BucketizedSplit::clear_default_direction() {
  default_direction_ = 0;
}
inline ::tensorflow::boosted_trees::BucketizedSplit_DefaultDirection BucketizedSplit::default_direction() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.BucketizedSplit.default_direction)
  return static_cast< ::tensorflow::boosted_trees::BucketizedSplit_DefaultDirection >(default_direction_);
}
inline void BucketizedSplit::set_default_direction(::tensorflow::boosted_trees::BucketizedSplit_DefaultDirection value) {
  
  default_direction_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.BucketizedSplit.default_direction)
}

// int32 left_id = 3;
inline void BucketizedSplit::clear_left_id() {
  left_id_ = 0;
}
inline ::google::protobuf::int32 BucketizedSplit::left_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.BucketizedSplit.left_id)
  return left_id_;
}
inline void BucketizedSplit::set_left_id(::google::protobuf::int32 value) {
  
  left_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.BucketizedSplit.left_id)
}

// int32 right_id = 4;
inline void BucketizedSplit::clear_right_id() {
  right_id_ = 0;
}
inline ::google::protobuf::int32 BucketizedSplit::right_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.BucketizedSplit.right_id)
  return right_id_;
}
inline void BucketizedSplit::set_right_id(::google::protobuf::int32 value) {
  
  right_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.BucketizedSplit.right_id)
}

// -------------------------------------------------------------------

// CategoricalSplit

// int32 feature_id = 1;
inline void CategoricalSplit::clear_feature_id() {
  feature_id_ = 0;
}
inline ::google::protobuf::int32 CategoricalSplit::feature_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.CategoricalSplit.feature_id)
  return feature_id_;
}
inline void CategoricalSplit::set_feature_id(::google::protobuf::int32 value) {
  
  feature_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.CategoricalSplit.feature_id)
}

// int32 value = 2;
inline void CategoricalSplit::clear_value() {
  value_ = 0;
}
inline ::google::protobuf::int32 CategoricalSplit::value() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.CategoricalSplit.value)
  return value_;
}
inline void CategoricalSplit::set_value(::google::protobuf::int32 value) {
  
  value_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.CategoricalSplit.value)
}

// int32 left_id = 3;
inline void CategoricalSplit::clear_left_id() {
  left_id_ = 0;
}
inline ::google::protobuf::int32 CategoricalSplit::left_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.CategoricalSplit.left_id)
  return left_id_;
}
inline void CategoricalSplit::set_left_id(::google::protobuf::int32 value) {
  
  left_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.CategoricalSplit.left_id)
}

// int32 right_id = 4;
inline void CategoricalSplit::clear_right_id() {
  right_id_ = 0;
}
inline ::google::protobuf::int32 CategoricalSplit::right_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.CategoricalSplit.right_id)
  return right_id_;
}
inline void CategoricalSplit::set_right_id(::google::protobuf::int32 value) {
  
  right_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.CategoricalSplit.right_id)
}

// -------------------------------------------------------------------

// DenseSplit

// int32 feature_id = 1;
inline void DenseSplit::clear_feature_id() {
  feature_id_ = 0;
}
inline ::google::protobuf::int32 DenseSplit::feature_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.DenseSplit.feature_id)
  return feature_id_;
}
inline void DenseSplit::set_feature_id(::google::protobuf::int32 value) {
  
  feature_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.DenseSplit.feature_id)
}

// float threshold = 2;
inline void DenseSplit::clear_threshold() {
  threshold_ = 0;
}
inline float DenseSplit::threshold() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.DenseSplit.threshold)
  return threshold_;
}
inline void DenseSplit::set_threshold(float value) {
  
  threshold_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.DenseSplit.threshold)
}

// int32 left_id = 3;
inline void DenseSplit::clear_left_id() {
  left_id_ = 0;
}
inline ::google::protobuf::int32 DenseSplit::left_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.DenseSplit.left_id)
  return left_id_;
}
inline void DenseSplit::set_left_id(::google::protobuf::int32 value) {
  
  left_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.DenseSplit.left_id)
}

// int32 right_id = 4;
inline void DenseSplit::clear_right_id() {
  right_id_ = 0;
}
inline ::google::protobuf::int32 DenseSplit::right_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.DenseSplit.right_id)
  return right_id_;
}
inline void DenseSplit::set_right_id(::google::protobuf::int32 value) {
  
  right_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.DenseSplit.right_id)
}

// -------------------------------------------------------------------

// Tree

// repeated .tensorflow.boosted_trees.Node nodes = 1;
inline int Tree::nodes_size() const {
  return nodes_.size();
}
inline void Tree::clear_nodes() {
  nodes_.Clear();
}
inline ::tensorflow::boosted_trees::Node* Tree::mutable_nodes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.boosted_trees.Tree.nodes)
  return nodes_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::boosted_trees::Node >*
Tree::mutable_nodes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.boosted_trees.Tree.nodes)
  return &nodes_;
}
inline const ::tensorflow::boosted_trees::Node& Tree::nodes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.Tree.nodes)
  return nodes_.Get(index);
}
inline ::tensorflow::boosted_trees::Node* Tree::add_nodes() {
  // @@protoc_insertion_point(field_add:tensorflow.boosted_trees.Tree.nodes)
  return nodes_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::boosted_trees::Node >&
Tree::nodes() const {
  // @@protoc_insertion_point(field_list:tensorflow.boosted_trees.Tree.nodes)
  return nodes_;
}

// -------------------------------------------------------------------

// TreeMetadata_PostPruneNodeUpdate

// int32 new_node_id = 1;
inline void TreeMetadata_PostPruneNodeUpdate::clear_new_node_id() {
  new_node_id_ = 0;
}
inline ::google::protobuf::int32 TreeMetadata_PostPruneNodeUpdate::new_node_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate.new_node_id)
  return new_node_id_;
}
inline void TreeMetadata_PostPruneNodeUpdate::set_new_node_id(::google::protobuf::int32 value) {
  
  new_node_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate.new_node_id)
}

// float logit_change = 2;
inline void TreeMetadata_PostPruneNodeUpdate::clear_logit_change() {
  logit_change_ = 0;
}
inline float TreeMetadata_PostPruneNodeUpdate::logit_change() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate.logit_change)
  return logit_change_;
}
inline void TreeMetadata_PostPruneNodeUpdate::set_logit_change(float value) {
  
  logit_change_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate.logit_change)
}

// -------------------------------------------------------------------

// TreeMetadata

// int32 num_layers_grown = 2;
inline void TreeMetadata::clear_num_layers_grown() {
  num_layers_grown_ = 0;
}
inline ::google::protobuf::int32 TreeMetadata::num_layers_grown() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.TreeMetadata.num_layers_grown)
  return num_layers_grown_;
}
inline void TreeMetadata::set_num_layers_grown(::google::protobuf::int32 value) {
  
  num_layers_grown_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.TreeMetadata.num_layers_grown)
}

// bool is_finalized = 3;
inline void TreeMetadata::clear_is_finalized() {
  is_finalized_ = false;
}
inline bool TreeMetadata::is_finalized() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.TreeMetadata.is_finalized)
  return is_finalized_;
}
inline void TreeMetadata::set_is_finalized(bool value) {
  
  is_finalized_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.TreeMetadata.is_finalized)
}

// repeated .tensorflow.boosted_trees.TreeMetadata.PostPruneNodeUpdate post_pruned_nodes_meta = 4;
inline int TreeMetadata::post_pruned_nodes_meta_size() const {
  return post_pruned_nodes_meta_.size();
}
inline void TreeMetadata::clear_post_pruned_nodes_meta() {
  post_pruned_nodes_meta_.Clear();
}
inline ::tensorflow::boosted_trees::TreeMetadata_PostPruneNodeUpdate* TreeMetadata::mutable_post_pruned_nodes_meta(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.boosted_trees.TreeMetadata.post_pruned_nodes_meta)
  return post_pruned_nodes_meta_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::boosted_trees::TreeMetadata_PostPruneNodeUpdate >*
TreeMetadata::mutable_post_pruned_nodes_meta() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.boosted_trees.TreeMetadata.post_pruned_nodes_meta)
  return &post_pruned_nodes_meta_;
}
inline const ::tensorflow::boosted_trees::TreeMetadata_PostPruneNodeUpdate& TreeMetadata::post_pruned_nodes_meta(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.TreeMetadata.post_pruned_nodes_meta)
  return post_pruned_nodes_meta_.Get(index);
}
inline ::tensorflow::boosted_trees::TreeMetadata_PostPruneNodeUpdate* TreeMetadata::add_post_pruned_nodes_meta() {
  // @@protoc_insertion_point(field_add:tensorflow.boosted_trees.TreeMetadata.post_pruned_nodes_meta)
  return post_pruned_nodes_meta_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::boosted_trees::TreeMetadata_PostPruneNodeUpdate >&
TreeMetadata::post_pruned_nodes_meta() const {
  // @@protoc_insertion_point(field_list:tensorflow.boosted_trees.TreeMetadata.post_pruned_nodes_meta)
  return post_pruned_nodes_meta_;
}

// -------------------------------------------------------------------

// GrowingMetadata

// int64 num_trees_attempted = 1;
inline void GrowingMetadata::clear_num_trees_attempted() {
  num_trees_attempted_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GrowingMetadata::num_trees_attempted() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.GrowingMetadata.num_trees_attempted)
  return num_trees_attempted_;
}
inline void GrowingMetadata::set_num_trees_attempted(::google::protobuf::int64 value) {
  
  num_trees_attempted_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.GrowingMetadata.num_trees_attempted)
}

// int64 num_layers_attempted = 2;
inline void GrowingMetadata::clear_num_layers_attempted() {
  num_layers_attempted_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GrowingMetadata::num_layers_attempted() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.GrowingMetadata.num_layers_attempted)
  return num_layers_attempted_;
}
inline void GrowingMetadata::set_num_layers_attempted(::google::protobuf::int64 value) {
  
  num_layers_attempted_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.GrowingMetadata.num_layers_attempted)
}

// int32 last_layer_node_start = 3;
inline void GrowingMetadata::clear_last_layer_node_start() {
  last_layer_node_start_ = 0;
}
inline ::google::protobuf::int32 GrowingMetadata::last_layer_node_start() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.GrowingMetadata.last_layer_node_start)
  return last_layer_node_start_;
}
inline void GrowingMetadata::set_last_layer_node_start(::google::protobuf::int32 value) {
  
  last_layer_node_start_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.GrowingMetadata.last_layer_node_start)
}

// int32 last_layer_node_end = 4;
inline void GrowingMetadata::clear_last_layer_node_end() {
  last_layer_node_end_ = 0;
}
inline ::google::protobuf::int32 GrowingMetadata::last_layer_node_end() const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.GrowingMetadata.last_layer_node_end)
  return last_layer_node_end_;
}
inline void GrowingMetadata::set_last_layer_node_end(::google::protobuf::int32 value) {
  
  last_layer_node_end_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.GrowingMetadata.last_layer_node_end)
}

// -------------------------------------------------------------------

// TreeEnsemble

// repeated .tensorflow.boosted_trees.Tree trees = 1;
inline int TreeEnsemble::trees_size() const {
  return trees_.size();
}
inline void TreeEnsemble::clear_trees() {
  trees_.Clear();
}
inline ::tensorflow::boosted_trees::Tree* TreeEnsemble::mutable_trees(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.boosted_trees.TreeEnsemble.trees)
  return trees_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::boosted_trees::Tree >*
TreeEnsemble::mutable_trees() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.boosted_trees.TreeEnsemble.trees)
  return &trees_;
}
inline const ::tensorflow::boosted_trees::Tree& TreeEnsemble::trees(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.TreeEnsemble.trees)
  return trees_.Get(index);
}
inline ::tensorflow::boosted_trees::Tree* TreeEnsemble::add_trees() {
  // @@protoc_insertion_point(field_add:tensorflow.boosted_trees.TreeEnsemble.trees)
  return trees_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::boosted_trees::Tree >&
TreeEnsemble::trees() const {
  // @@protoc_insertion_point(field_list:tensorflow.boosted_trees.TreeEnsemble.trees)
  return trees_;
}

// repeated float tree_weights = 2;
inline int TreeEnsemble::tree_weights_size() const {
  return tree_weights_.size();
}
inline void TreeEnsemble::clear_tree_weights() {
  tree_weights_.Clear();
}
inline float TreeEnsemble::tree_weights(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.TreeEnsemble.tree_weights)
  return tree_weights_.Get(index);
}
inline void TreeEnsemble::set_tree_weights(int index, float value) {
  tree_weights_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.TreeEnsemble.tree_weights)
}
inline void TreeEnsemble::add_tree_weights(float value) {
  tree_weights_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.boosted_trees.TreeEnsemble.tree_weights)
}
inline const ::google::protobuf::RepeatedField< float >&
TreeEnsemble::tree_weights() const {
  // @@protoc_insertion_point(field_list:tensorflow.boosted_trees.TreeEnsemble.tree_weights)
  return tree_weights_;
}
inline ::google::protobuf::RepeatedField< float >*
TreeEnsemble::mutable_tree_weights() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.boosted_trees.TreeEnsemble.tree_weights)
  return &tree_weights_;
}

// repeated .tensorflow.boosted_trees.TreeMetadata tree_metadata = 3;
inline int TreeEnsemble::tree_metadata_size() const {
  return tree_metadata_.size();
}
inline void TreeEnsemble::clear_tree_metadata() {
  tree_metadata_.Clear();
}
inline ::tensorflow::boosted_trees::TreeMetadata* TreeEnsemble::mutable_tree_metadata(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.boosted_trees.TreeEnsemble.tree_metadata)
  return tree_metadata_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::boosted_trees::TreeMetadata >*
TreeEnsemble::mutable_tree_metadata() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.boosted_trees.TreeEnsemble.tree_metadata)
  return &tree_metadata_;
}
inline const ::tensorflow::boosted_trees::TreeMetadata& TreeEnsemble::tree_metadata(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.TreeEnsemble.tree_metadata)
  return tree_metadata_.Get(index);
}
inline ::tensorflow::boosted_trees::TreeMetadata* TreeEnsemble::add_tree_metadata() {
  // @@protoc_insertion_point(field_add:tensorflow.boosted_trees.TreeEnsemble.tree_metadata)
  return tree_metadata_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::boosted_trees::TreeMetadata >&
TreeEnsemble::tree_metadata() const {
  // @@protoc_insertion_point(field_list:tensorflow.boosted_trees.TreeEnsemble.tree_metadata)
  return tree_metadata_;
}

// .tensorflow.boosted_trees.GrowingMetadata growing_metadata = 4;
inline bool TreeEnsemble::has_growing_metadata() const {
  return this != internal_default_instance() && growing_metadata_ != NULL;
}
inline void TreeEnsemble::clear_growing_metadata() {
  if (GetArenaNoVirtual() == NULL && growing_metadata_ != NULL) {
    delete growing_metadata_;
  }
  growing_metadata_ = NULL;
}
inline const ::tensorflow::boosted_trees::GrowingMetadata& TreeEnsemble::_internal_growing_metadata() const {
  return *growing_metadata_;
}
inline const ::tensorflow::boosted_trees::GrowingMetadata& TreeEnsemble::growing_metadata() const {
  const ::tensorflow::boosted_trees::GrowingMetadata* p = growing_metadata_;
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.TreeEnsemble.growing_metadata)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::boosted_trees::GrowingMetadata*>(
      &::tensorflow::boosted_trees::_GrowingMetadata_default_instance_);
}
inline ::tensorflow::boosted_trees::GrowingMetadata* TreeEnsemble::release_growing_metadata() {
  // @@protoc_insertion_point(field_release:tensorflow.boosted_trees.TreeEnsemble.growing_metadata)
  
  ::tensorflow::boosted_trees::GrowingMetadata* temp = growing_metadata_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  growing_metadata_ = NULL;
  return temp;
}
inline ::tensorflow::boosted_trees::GrowingMetadata* TreeEnsemble::unsafe_arena_release_growing_metadata() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.boosted_trees.TreeEnsemble.growing_metadata)
  
  ::tensorflow::boosted_trees::GrowingMetadata* temp = growing_metadata_;
  growing_metadata_ = NULL;
  return temp;
}
inline ::tensorflow::boosted_trees::GrowingMetadata* TreeEnsemble::mutable_growing_metadata() {
  
  if (growing_metadata_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::boosted_trees::GrowingMetadata>(GetArenaNoVirtual());
    growing_metadata_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.boosted_trees.TreeEnsemble.growing_metadata)
  return growing_metadata_;
}
inline void TreeEnsemble::set_allocated_growing_metadata(::tensorflow::boosted_trees::GrowingMetadata* growing_metadata) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete growing_metadata_;
  }
  if (growing_metadata) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(growing_metadata);
    if (message_arena != submessage_arena) {
      growing_metadata = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, growing_metadata, submessage_arena);
    }
    
  } else {
    
  }
  growing_metadata_ = growing_metadata;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.boosted_trees.TreeEnsemble.growing_metadata)
}

// -------------------------------------------------------------------

// DebugOutput

// repeated int32 feature_ids = 1;
inline int DebugOutput::feature_ids_size() const {
  return feature_ids_.size();
}
inline void DebugOutput::clear_feature_ids() {
  feature_ids_.Clear();
}
inline ::google::protobuf::int32 DebugOutput::feature_ids(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.DebugOutput.feature_ids)
  return feature_ids_.Get(index);
}
inline void DebugOutput::set_feature_ids(int index, ::google::protobuf::int32 value) {
  feature_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.DebugOutput.feature_ids)
}
inline void DebugOutput::add_feature_ids(::google::protobuf::int32 value) {
  feature_ids_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.boosted_trees.DebugOutput.feature_ids)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
DebugOutput::feature_ids() const {
  // @@protoc_insertion_point(field_list:tensorflow.boosted_trees.DebugOutput.feature_ids)
  return feature_ids_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
DebugOutput::mutable_feature_ids() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.boosted_trees.DebugOutput.feature_ids)
  return &feature_ids_;
}

// repeated float logits_path = 2;
inline int DebugOutput::logits_path_size() const {
  return logits_path_.size();
}
inline void DebugOutput::clear_logits_path() {
  logits_path_.Clear();
}
inline float DebugOutput::logits_path(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.boosted_trees.DebugOutput.logits_path)
  return logits_path_.Get(index);
}
inline void DebugOutput::set_logits_path(int index, float value) {
  logits_path_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.boosted_trees.DebugOutput.logits_path)
}
inline void DebugOutput::add_logits_path(float value) {
  logits_path_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.boosted_trees.DebugOutput.logits_path)
}
inline const ::google::protobuf::RepeatedField< float >&
DebugOutput::logits_path() const {
  // @@protoc_insertion_point(field_list:tensorflow.boosted_trees.DebugOutput.logits_path)
  return logits_path_;
}
inline ::google::protobuf::RepeatedField< float >*
DebugOutput::mutable_logits_path() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.boosted_trees.DebugOutput.logits_path)
  return &logits_path_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace boosted_trees
}  // namespace tensorflow

namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::tensorflow::boosted_trees::BucketizedSplit_DefaultDirection> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::boosted_trees::BucketizedSplit_DefaultDirection>() {
  return ::tensorflow::boosted_trees::BucketizedSplit_DefaultDirection_descriptor();
}

}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fkernels_2fboosted_5ftrees_2fboosted_5ftrees_2eproto

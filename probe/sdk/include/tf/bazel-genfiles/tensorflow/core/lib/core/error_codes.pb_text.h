// GENERATED FILE - DO NOT MODIFY
#ifndef tensorflow_core_lib_core_error_codes_proto_H_
#define tensorflow_core_lib_core_error_codes_proto_H_

#include "tensorflow/core/lib/core/error_codes.pb.h"
#include "tensorflow/core/platform/macros.h"
#include "tensorflow/core/platform/protobuf.h"
#include "tensorflow/core/platform/types.h"

namespace tensorflow {
namespace error {

// Enum text output for tensorflow.error.Code
const char* EnumName_Code(
    ::tensorflow::error::Code value);

}  // namespace error
}  // namespace tensorflow

#endif  // tensorflow_core_lib_core_error_codes_proto_H_

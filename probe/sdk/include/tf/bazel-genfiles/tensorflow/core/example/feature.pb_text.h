// GENERATED FILE - DO NOT MODIFY
#ifndef tensorflow_core_example_feature_proto_H_
#define tensorflow_core_example_feature_proto_H_

#include "tensorflow/core/example/feature.pb.h"
#include "tensorflow/core/platform/macros.h"
#include "tensorflow/core/platform/protobuf.h"
#include "tensorflow/core/platform/types.h"

namespace tensorflow {

// Message-text conversion for tensorflow.BytesList
string ProtoDebugString(
    const ::tensorflow::BytesList& msg);
string ProtoShortDebugString(
    const ::tensorflow::BytesList& msg);
bool ProtoParseFromString(
    const string& s,
    ::tensorflow::BytesList* msg)
        TF_MUST_USE_RESULT;

// Message-text conversion for tensorflow.FloatList
string ProtoDebugString(
    const ::tensorflow::FloatList& msg);
string ProtoShortDebugString(
    const ::tensorflow::FloatList& msg);
bool ProtoParseFromString(
    const string& s,
    ::tensorflow::FloatList* msg)
        TF_MUST_USE_RESULT;

// Message-text conversion for tensorflow.Int64List
string ProtoDebugString(
    const ::tensorflow::Int64List& msg);
string ProtoShortDebugString(
    const ::tensorflow::Int64List& msg);
bool ProtoParseFromString(
    const string& s,
    ::tensorflow::Int64List* msg)
        TF_MUST_USE_RESULT;

// Message-text conversion for tensorflow.Feature
string ProtoDebugString(
    const ::tensorflow::Feature& msg);
string ProtoShortDebugString(
    const ::tensorflow::Feature& msg);
bool ProtoParseFromString(
    const string& s,
    ::tensorflow::Feature* msg)
        TF_MUST_USE_RESULT;

// Message-text conversion for tensorflow.Features
string ProtoDebugString(
    const ::tensorflow::Features& msg);
string ProtoShortDebugString(
    const ::tensorflow::Features& msg);
bool ProtoParseFromString(
    const string& s,
    ::tensorflow::Features* msg)
        TF_MUST_USE_RESULT;

// Message-text conversion for tensorflow.FeatureList
string ProtoDebugString(
    const ::tensorflow::FeatureList& msg);
string ProtoShortDebugString(
    const ::tensorflow::FeatureList& msg);
bool ProtoParseFromString(
    const string& s,
    ::tensorflow::FeatureList* msg)
        TF_MUST_USE_RESULT;

// Message-text conversion for tensorflow.FeatureLists
string ProtoDebugString(
    const ::tensorflow::FeatureLists& msg);
string ProtoShortDebugString(
    const ::tensorflow::FeatureLists& msg);
bool ProtoParseFromString(
    const string& s,
    ::tensorflow::FeatureLists* msg)
        TF_MUST_USE_RESULT;

}  // namespace tensorflow

#endif  // tensorflow_core_example_feature_proto_H_

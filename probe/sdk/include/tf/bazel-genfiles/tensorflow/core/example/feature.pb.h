// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/example/feature.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fexample_2ffeature_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fexample_2ffeature_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto 

namespace protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[9];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto
namespace tensorflow {
class BytesList;
class BytesListDefaultTypeInternal;
extern BytesListDefaultTypeInternal _BytesList_default_instance_;
class Feature;
class FeatureDefaultTypeInternal;
extern FeatureDefaultTypeInternal _Feature_default_instance_;
class FeatureList;
class FeatureListDefaultTypeInternal;
extern FeatureListDefaultTypeInternal _FeatureList_default_instance_;
class FeatureLists;
class FeatureListsDefaultTypeInternal;
extern FeatureListsDefaultTypeInternal _FeatureLists_default_instance_;
class FeatureLists_FeatureListEntry_DoNotUse;
class FeatureLists_FeatureListEntry_DoNotUseDefaultTypeInternal;
extern FeatureLists_FeatureListEntry_DoNotUseDefaultTypeInternal _FeatureLists_FeatureListEntry_DoNotUse_default_instance_;
class Features;
class FeaturesDefaultTypeInternal;
extern FeaturesDefaultTypeInternal _Features_default_instance_;
class Features_FeatureEntry_DoNotUse;
class Features_FeatureEntry_DoNotUseDefaultTypeInternal;
extern Features_FeatureEntry_DoNotUseDefaultTypeInternal _Features_FeatureEntry_DoNotUse_default_instance_;
class FloatList;
class FloatListDefaultTypeInternal;
extern FloatListDefaultTypeInternal _FloatList_default_instance_;
class Int64List;
class Int64ListDefaultTypeInternal;
extern Int64ListDefaultTypeInternal _Int64List_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::BytesList* Arena::CreateMaybeMessage<::tensorflow::BytesList>(Arena*);
template<> ::tensorflow::Feature* Arena::CreateMaybeMessage<::tensorflow::Feature>(Arena*);
template<> ::tensorflow::FeatureList* Arena::CreateMaybeMessage<::tensorflow::FeatureList>(Arena*);
template<> ::tensorflow::FeatureLists* Arena::CreateMaybeMessage<::tensorflow::FeatureLists>(Arena*);
template<> ::tensorflow::FeatureLists_FeatureListEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::FeatureLists_FeatureListEntry_DoNotUse>(Arena*);
template<> ::tensorflow::Features* Arena::CreateMaybeMessage<::tensorflow::Features>(Arena*);
template<> ::tensorflow::Features_FeatureEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::Features_FeatureEntry_DoNotUse>(Arena*);
template<> ::tensorflow::FloatList* Arena::CreateMaybeMessage<::tensorflow::FloatList>(Arena*);
template<> ::tensorflow::Int64List* Arena::CreateMaybeMessage<::tensorflow::Int64List>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

// ===================================================================

class BytesList : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.BytesList) */ {
 public:
  BytesList();
  virtual ~BytesList();

  BytesList(const BytesList& from);

  inline BytesList& operator=(const BytesList& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  BytesList(BytesList&& from) noexcept
    : BytesList() {
    *this = ::std::move(from);
  }

  inline BytesList& operator=(BytesList&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const BytesList& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const BytesList* internal_default_instance() {
    return reinterpret_cast<const BytesList*>(
               &_BytesList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(BytesList* other);
  void Swap(BytesList* other);
  friend void swap(BytesList& a, BytesList& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline BytesList* New() const final {
    return CreateMaybeMessage<BytesList>(NULL);
  }

  BytesList* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<BytesList>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const BytesList& from);
  void MergeFrom(const BytesList& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BytesList* other);
  protected:
  explicit BytesList(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated bytes value = 1;
  int value_size() const;
  void clear_value();
  static const int kValueFieldNumber = 1;
  const ::std::string& value(int index) const;
  ::std::string* mutable_value(int index);
  void set_value(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_value(int index, ::std::string&& value);
  #endif
  void set_value(int index, const char* value);
  void set_value(int index, const void* value, size_t size);
  ::std::string* add_value();
  void add_value(const ::std::string& value);
  #if LANG_CXX11
  void add_value(::std::string&& value);
  #endif
  void add_value(const char* value);
  void add_value(const void* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& value() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_value();

  // @@protoc_insertion_point(class_scope:tensorflow.BytesList)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::std::string> value_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class FloatList : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.FloatList) */ {
 public:
  FloatList();
  virtual ~FloatList();

  FloatList(const FloatList& from);

  inline FloatList& operator=(const FloatList& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  FloatList(FloatList&& from) noexcept
    : FloatList() {
    *this = ::std::move(from);
  }

  inline FloatList& operator=(FloatList&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const FloatList& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const FloatList* internal_default_instance() {
    return reinterpret_cast<const FloatList*>(
               &_FloatList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void UnsafeArenaSwap(FloatList* other);
  void Swap(FloatList* other);
  friend void swap(FloatList& a, FloatList& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline FloatList* New() const final {
    return CreateMaybeMessage<FloatList>(NULL);
  }

  FloatList* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<FloatList>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const FloatList& from);
  void MergeFrom(const FloatList& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FloatList* other);
  protected:
  explicit FloatList(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated float value = 1 [packed = true];
  int value_size() const;
  void clear_value();
  static const int kValueFieldNumber = 1;
  float value(int index) const;
  void set_value(int index, float value);
  void add_value(float value);
  const ::google::protobuf::RepeatedField< float >&
      value() const;
  ::google::protobuf::RepeatedField< float >*
      mutable_value();

  // @@protoc_insertion_point(class_scope:tensorflow.FloatList)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< float > value_;
  mutable int _value_cached_byte_size_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Int64List : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.Int64List) */ {
 public:
  Int64List();
  virtual ~Int64List();

  Int64List(const Int64List& from);

  inline Int64List& operator=(const Int64List& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Int64List(Int64List&& from) noexcept
    : Int64List() {
    *this = ::std::move(from);
  }

  inline Int64List& operator=(Int64List&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const Int64List& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Int64List* internal_default_instance() {
    return reinterpret_cast<const Int64List*>(
               &_Int64List_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void UnsafeArenaSwap(Int64List* other);
  void Swap(Int64List* other);
  friend void swap(Int64List& a, Int64List& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Int64List* New() const final {
    return CreateMaybeMessage<Int64List>(NULL);
  }

  Int64List* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Int64List>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Int64List& from);
  void MergeFrom(const Int64List& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Int64List* other);
  protected:
  explicit Int64List(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int64 value = 1 [packed = true];
  int value_size() const;
  void clear_value();
  static const int kValueFieldNumber = 1;
  ::google::protobuf::int64 value(int index) const;
  void set_value(int index, ::google::protobuf::int64 value);
  void add_value(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      value() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_value();

  // @@protoc_insertion_point(class_scope:tensorflow.Int64List)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > value_;
  mutable int _value_cached_byte_size_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Feature : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.Feature) */ {
 public:
  Feature();
  virtual ~Feature();

  Feature(const Feature& from);

  inline Feature& operator=(const Feature& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Feature(Feature&& from) noexcept
    : Feature() {
    *this = ::std::move(from);
  }

  inline Feature& operator=(Feature&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const Feature& default_instance();

  enum KindCase {
    kBytesList = 1,
    kFloatList = 2,
    kInt64List = 3,
    KIND_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Feature* internal_default_instance() {
    return reinterpret_cast<const Feature*>(
               &_Feature_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void UnsafeArenaSwap(Feature* other);
  void Swap(Feature* other);
  friend void swap(Feature& a, Feature& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Feature* New() const final {
    return CreateMaybeMessage<Feature>(NULL);
  }

  Feature* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Feature>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Feature& from);
  void MergeFrom(const Feature& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Feature* other);
  protected:
  explicit Feature(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .tensorflow.BytesList bytes_list = 1;
  bool has_bytes_list() const;
  void clear_bytes_list();
  static const int kBytesListFieldNumber = 1;
  private:
  const ::tensorflow::BytesList& _internal_bytes_list() const;
  public:
  const ::tensorflow::BytesList& bytes_list() const;
  ::tensorflow::BytesList* release_bytes_list();
  ::tensorflow::BytesList* mutable_bytes_list();
  void set_allocated_bytes_list(::tensorflow::BytesList* bytes_list);
  void unsafe_arena_set_allocated_bytes_list(
      ::tensorflow::BytesList* bytes_list);
  ::tensorflow::BytesList* unsafe_arena_release_bytes_list();

  // .tensorflow.FloatList float_list = 2;
  bool has_float_list() const;
  void clear_float_list();
  static const int kFloatListFieldNumber = 2;
  private:
  const ::tensorflow::FloatList& _internal_float_list() const;
  public:
  const ::tensorflow::FloatList& float_list() const;
  ::tensorflow::FloatList* release_float_list();
  ::tensorflow::FloatList* mutable_float_list();
  void set_allocated_float_list(::tensorflow::FloatList* float_list);
  void unsafe_arena_set_allocated_float_list(
      ::tensorflow::FloatList* float_list);
  ::tensorflow::FloatList* unsafe_arena_release_float_list();

  // .tensorflow.Int64List int64_list = 3;
  bool has_int64_list() const;
  void clear_int64_list();
  static const int kInt64ListFieldNumber = 3;
  private:
  const ::tensorflow::Int64List& _internal_int64_list() const;
  public:
  const ::tensorflow::Int64List& int64_list() const;
  ::tensorflow::Int64List* release_int64_list();
  ::tensorflow::Int64List* mutable_int64_list();
  void set_allocated_int64_list(::tensorflow::Int64List* int64_list);
  void unsafe_arena_set_allocated_int64_list(
      ::tensorflow::Int64List* int64_list);
  ::tensorflow::Int64List* unsafe_arena_release_int64_list();

  void clear_kind();
  KindCase kind_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.Feature)
 private:
  void set_has_bytes_list();
  void set_has_float_list();
  void set_has_int64_list();

  inline bool has_kind() const;
  inline void clear_has_kind();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  union KindUnion {
    KindUnion() {}
    ::tensorflow::BytesList* bytes_list_;
    ::tensorflow::FloatList* float_list_;
    ::tensorflow::Int64List* int64_list_;
  } kind_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Features_FeatureEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<Features_FeatureEntry_DoNotUse, 
    ::std::string, ::tensorflow::Feature,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<Features_FeatureEntry_DoNotUse, 
    ::std::string, ::tensorflow::Feature,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  Features_FeatureEntry_DoNotUse();
  Features_FeatureEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const Features_FeatureEntry_DoNotUse& other);
  static const Features_FeatureEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const Features_FeatureEntry_DoNotUse*>(&_Features_FeatureEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class Features : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.Features) */ {
 public:
  Features();
  virtual ~Features();

  Features(const Features& from);

  inline Features& operator=(const Features& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Features(Features&& from) noexcept
    : Features() {
    *this = ::std::move(from);
  }

  inline Features& operator=(Features&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const Features& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Features* internal_default_instance() {
    return reinterpret_cast<const Features*>(
               &_Features_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  void UnsafeArenaSwap(Features* other);
  void Swap(Features* other);
  friend void swap(Features& a, Features& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Features* New() const final {
    return CreateMaybeMessage<Features>(NULL);
  }

  Features* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Features>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Features& from);
  void MergeFrom(const Features& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Features* other);
  protected:
  explicit Features(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<string, .tensorflow.Feature> feature = 1;
  int feature_size() const;
  void clear_feature();
  static const int kFeatureFieldNumber = 1;
  const ::google::protobuf::Map< ::std::string, ::tensorflow::Feature >&
      feature() const;
  ::google::protobuf::Map< ::std::string, ::tensorflow::Feature >*
      mutable_feature();

  // @@protoc_insertion_point(class_scope:tensorflow.Features)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::MapField<
      Features_FeatureEntry_DoNotUse,
      ::std::string, ::tensorflow::Feature,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > feature_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class FeatureList : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.FeatureList) */ {
 public:
  FeatureList();
  virtual ~FeatureList();

  FeatureList(const FeatureList& from);

  inline FeatureList& operator=(const FeatureList& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  FeatureList(FeatureList&& from) noexcept
    : FeatureList() {
    *this = ::std::move(from);
  }

  inline FeatureList& operator=(FeatureList&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const FeatureList& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const FeatureList* internal_default_instance() {
    return reinterpret_cast<const FeatureList*>(
               &_FeatureList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  void UnsafeArenaSwap(FeatureList* other);
  void Swap(FeatureList* other);
  friend void swap(FeatureList& a, FeatureList& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline FeatureList* New() const final {
    return CreateMaybeMessage<FeatureList>(NULL);
  }

  FeatureList* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<FeatureList>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const FeatureList& from);
  void MergeFrom(const FeatureList& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FeatureList* other);
  protected:
  explicit FeatureList(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.Feature feature = 1;
  int feature_size() const;
  void clear_feature();
  static const int kFeatureFieldNumber = 1;
  ::tensorflow::Feature* mutable_feature(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::Feature >*
      mutable_feature();
  const ::tensorflow::Feature& feature(int index) const;
  ::tensorflow::Feature* add_feature();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::Feature >&
      feature() const;

  // @@protoc_insertion_point(class_scope:tensorflow.FeatureList)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::Feature > feature_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class FeatureLists_FeatureListEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<FeatureLists_FeatureListEntry_DoNotUse, 
    ::std::string, ::tensorflow::FeatureList,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<FeatureLists_FeatureListEntry_DoNotUse, 
    ::std::string, ::tensorflow::FeatureList,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  FeatureLists_FeatureListEntry_DoNotUse();
  FeatureLists_FeatureListEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const FeatureLists_FeatureListEntry_DoNotUse& other);
  static const FeatureLists_FeatureListEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const FeatureLists_FeatureListEntry_DoNotUse*>(&_FeatureLists_FeatureListEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class FeatureLists : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.FeatureLists) */ {
 public:
  FeatureLists();
  virtual ~FeatureLists();

  FeatureLists(const FeatureLists& from);

  inline FeatureLists& operator=(const FeatureLists& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  FeatureLists(FeatureLists&& from) noexcept
    : FeatureLists() {
    *this = ::std::move(from);
  }

  inline FeatureLists& operator=(FeatureLists&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const FeatureLists& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const FeatureLists* internal_default_instance() {
    return reinterpret_cast<const FeatureLists*>(
               &_FeatureLists_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  void UnsafeArenaSwap(FeatureLists* other);
  void Swap(FeatureLists* other);
  friend void swap(FeatureLists& a, FeatureLists& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline FeatureLists* New() const final {
    return CreateMaybeMessage<FeatureLists>(NULL);
  }

  FeatureLists* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<FeatureLists>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const FeatureLists& from);
  void MergeFrom(const FeatureLists& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FeatureLists* other);
  protected:
  explicit FeatureLists(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<string, .tensorflow.FeatureList> feature_list = 1;
  int feature_list_size() const;
  void clear_feature_list();
  static const int kFeatureListFieldNumber = 1;
  const ::google::protobuf::Map< ::std::string, ::tensorflow::FeatureList >&
      feature_list() const;
  ::google::protobuf::Map< ::std::string, ::tensorflow::FeatureList >*
      mutable_feature_list();

  // @@protoc_insertion_point(class_scope:tensorflow.FeatureLists)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::MapField<
      FeatureLists_FeatureListEntry_DoNotUse,
      ::std::string, ::tensorflow::FeatureList,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > feature_list_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// BytesList

// repeated bytes value = 1;
inline int BytesList::value_size() const {
  return value_.size();
}
inline void BytesList::clear_value() {
  value_.Clear();
}
inline const ::std::string& BytesList::value(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.BytesList.value)
  return value_.Get(index);
}
inline ::std::string* BytesList::mutable_value(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.BytesList.value)
  return value_.Mutable(index);
}
inline void BytesList::set_value(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.BytesList.value)
  value_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void BytesList::set_value(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.BytesList.value)
  value_.Mutable(index)->assign(std::move(value));
}
#endif
inline void BytesList::set_value(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  value_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.BytesList.value)
}
inline void BytesList::set_value(int index, const void* value, size_t size) {
  value_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.BytesList.value)
}
inline ::std::string* BytesList::add_value() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.BytesList.value)
  return value_.Add();
}
inline void BytesList::add_value(const ::std::string& value) {
  value_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.BytesList.value)
}
#if LANG_CXX11
inline void BytesList::add_value(::std::string&& value) {
  value_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.BytesList.value)
}
#endif
inline void BytesList::add_value(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  value_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.BytesList.value)
}
inline void BytesList::add_value(const void* value, size_t size) {
  value_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.BytesList.value)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
BytesList::value() const {
  // @@protoc_insertion_point(field_list:tensorflow.BytesList.value)
  return value_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
BytesList::mutable_value() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.BytesList.value)
  return &value_;
}

// -------------------------------------------------------------------

// FloatList

// repeated float value = 1 [packed = true];
inline int FloatList::value_size() const {
  return value_.size();
}
inline void FloatList::clear_value() {
  value_.Clear();
}
inline float FloatList::value(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.FloatList.value)
  return value_.Get(index);
}
inline void FloatList::set_value(int index, float value) {
  value_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.FloatList.value)
}
inline void FloatList::add_value(float value) {
  value_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.FloatList.value)
}
inline const ::google::protobuf::RepeatedField< float >&
FloatList::value() const {
  // @@protoc_insertion_point(field_list:tensorflow.FloatList.value)
  return value_;
}
inline ::google::protobuf::RepeatedField< float >*
FloatList::mutable_value() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.FloatList.value)
  return &value_;
}

// -------------------------------------------------------------------

// Int64List

// repeated int64 value = 1 [packed = true];
inline int Int64List::value_size() const {
  return value_.size();
}
inline void Int64List::clear_value() {
  value_.Clear();
}
inline ::google::protobuf::int64 Int64List::value(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.Int64List.value)
  return value_.Get(index);
}
inline void Int64List::set_value(int index, ::google::protobuf::int64 value) {
  value_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.Int64List.value)
}
inline void Int64List::add_value(::google::protobuf::int64 value) {
  value_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.Int64List.value)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
Int64List::value() const {
  // @@protoc_insertion_point(field_list:tensorflow.Int64List.value)
  return value_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
Int64List::mutable_value() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.Int64List.value)
  return &value_;
}

// -------------------------------------------------------------------

// Feature

// .tensorflow.BytesList bytes_list = 1;
inline bool Feature::has_bytes_list() const {
  return kind_case() == kBytesList;
}
inline void Feature::set_has_bytes_list() {
  _oneof_case_[0] = kBytesList;
}
inline void Feature::clear_bytes_list() {
  if (has_bytes_list()) {
    if (GetArenaNoVirtual() == NULL) {
      delete kind_.bytes_list_;
    }
    clear_has_kind();
  }
}
inline const ::tensorflow::BytesList& Feature::_internal_bytes_list() const {
  return *kind_.bytes_list_;
}
inline ::tensorflow::BytesList* Feature::release_bytes_list() {
  // @@protoc_insertion_point(field_release:tensorflow.Feature.bytes_list)
  if (has_bytes_list()) {
    clear_has_kind();
      ::tensorflow::BytesList* temp = kind_.bytes_list_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    kind_.bytes_list_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::BytesList& Feature::bytes_list() const {
  // @@protoc_insertion_point(field_get:tensorflow.Feature.bytes_list)
  return has_bytes_list()
      ? *kind_.bytes_list_
      : *reinterpret_cast< ::tensorflow::BytesList*>(&::tensorflow::_BytesList_default_instance_);
}
inline ::tensorflow::BytesList* Feature::unsafe_arena_release_bytes_list() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Feature.bytes_list)
  if (has_bytes_list()) {
    clear_has_kind();
    ::tensorflow::BytesList* temp = kind_.bytes_list_;
    kind_.bytes_list_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void Feature::unsafe_arena_set_allocated_bytes_list(::tensorflow::BytesList* bytes_list) {
  clear_kind();
  if (bytes_list) {
    set_has_bytes_list();
    kind_.bytes_list_ = bytes_list;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Feature.bytes_list)
}
inline ::tensorflow::BytesList* Feature::mutable_bytes_list() {
  if (!has_bytes_list()) {
    clear_kind();
    set_has_bytes_list();
    kind_.bytes_list_ = CreateMaybeMessage< ::tensorflow::BytesList >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.Feature.bytes_list)
  return kind_.bytes_list_;
}

// .tensorflow.FloatList float_list = 2;
inline bool Feature::has_float_list() const {
  return kind_case() == kFloatList;
}
inline void Feature::set_has_float_list() {
  _oneof_case_[0] = kFloatList;
}
inline void Feature::clear_float_list() {
  if (has_float_list()) {
    if (GetArenaNoVirtual() == NULL) {
      delete kind_.float_list_;
    }
    clear_has_kind();
  }
}
inline const ::tensorflow::FloatList& Feature::_internal_float_list() const {
  return *kind_.float_list_;
}
inline ::tensorflow::FloatList* Feature::release_float_list() {
  // @@protoc_insertion_point(field_release:tensorflow.Feature.float_list)
  if (has_float_list()) {
    clear_has_kind();
      ::tensorflow::FloatList* temp = kind_.float_list_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    kind_.float_list_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::FloatList& Feature::float_list() const {
  // @@protoc_insertion_point(field_get:tensorflow.Feature.float_list)
  return has_float_list()
      ? *kind_.float_list_
      : *reinterpret_cast< ::tensorflow::FloatList*>(&::tensorflow::_FloatList_default_instance_);
}
inline ::tensorflow::FloatList* Feature::unsafe_arena_release_float_list() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Feature.float_list)
  if (has_float_list()) {
    clear_has_kind();
    ::tensorflow::FloatList* temp = kind_.float_list_;
    kind_.float_list_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void Feature::unsafe_arena_set_allocated_float_list(::tensorflow::FloatList* float_list) {
  clear_kind();
  if (float_list) {
    set_has_float_list();
    kind_.float_list_ = float_list;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Feature.float_list)
}
inline ::tensorflow::FloatList* Feature::mutable_float_list() {
  if (!has_float_list()) {
    clear_kind();
    set_has_float_list();
    kind_.float_list_ = CreateMaybeMessage< ::tensorflow::FloatList >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.Feature.float_list)
  return kind_.float_list_;
}

// .tensorflow.Int64List int64_list = 3;
inline bool Feature::has_int64_list() const {
  return kind_case() == kInt64List;
}
inline void Feature::set_has_int64_list() {
  _oneof_case_[0] = kInt64List;
}
inline void Feature::clear_int64_list() {
  if (has_int64_list()) {
    if (GetArenaNoVirtual() == NULL) {
      delete kind_.int64_list_;
    }
    clear_has_kind();
  }
}
inline const ::tensorflow::Int64List& Feature::_internal_int64_list() const {
  return *kind_.int64_list_;
}
inline ::tensorflow::Int64List* Feature::release_int64_list() {
  // @@protoc_insertion_point(field_release:tensorflow.Feature.int64_list)
  if (has_int64_list()) {
    clear_has_kind();
      ::tensorflow::Int64List* temp = kind_.int64_list_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    kind_.int64_list_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::Int64List& Feature::int64_list() const {
  // @@protoc_insertion_point(field_get:tensorflow.Feature.int64_list)
  return has_int64_list()
      ? *kind_.int64_list_
      : *reinterpret_cast< ::tensorflow::Int64List*>(&::tensorflow::_Int64List_default_instance_);
}
inline ::tensorflow::Int64List* Feature::unsafe_arena_release_int64_list() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Feature.int64_list)
  if (has_int64_list()) {
    clear_has_kind();
    ::tensorflow::Int64List* temp = kind_.int64_list_;
    kind_.int64_list_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void Feature::unsafe_arena_set_allocated_int64_list(::tensorflow::Int64List* int64_list) {
  clear_kind();
  if (int64_list) {
    set_has_int64_list();
    kind_.int64_list_ = int64_list;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Feature.int64_list)
}
inline ::tensorflow::Int64List* Feature::mutable_int64_list() {
  if (!has_int64_list()) {
    clear_kind();
    set_has_int64_list();
    kind_.int64_list_ = CreateMaybeMessage< ::tensorflow::Int64List >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.Feature.int64_list)
  return kind_.int64_list_;
}

inline bool Feature::has_kind() const {
  return kind_case() != KIND_NOT_SET;
}
inline void Feature::clear_has_kind() {
  _oneof_case_[0] = KIND_NOT_SET;
}
inline Feature::KindCase Feature::kind_case() const {
  return Feature::KindCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// Features

// map<string, .tensorflow.Feature> feature = 1;
inline int Features::feature_size() const {
  return feature_.size();
}
inline void Features::clear_feature() {
  feature_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::tensorflow::Feature >&
Features::feature() const {
  // @@protoc_insertion_point(field_map:tensorflow.Features.feature)
  return feature_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::tensorflow::Feature >*
Features::mutable_feature() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.Features.feature)
  return feature_.MutableMap();
}

// -------------------------------------------------------------------

// FeatureList

// repeated .tensorflow.Feature feature = 1;
inline int FeatureList::feature_size() const {
  return feature_.size();
}
inline void FeatureList::clear_feature() {
  feature_.Clear();
}
inline ::tensorflow::Feature* FeatureList::mutable_feature(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.FeatureList.feature)
  return feature_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::Feature >*
FeatureList::mutable_feature() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.FeatureList.feature)
  return &feature_;
}
inline const ::tensorflow::Feature& FeatureList::feature(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.FeatureList.feature)
  return feature_.Get(index);
}
inline ::tensorflow::Feature* FeatureList::add_feature() {
  // @@protoc_insertion_point(field_add:tensorflow.FeatureList.feature)
  return feature_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::Feature >&
FeatureList::feature() const {
  // @@protoc_insertion_point(field_list:tensorflow.FeatureList.feature)
  return feature_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// FeatureLists

// map<string, .tensorflow.FeatureList> feature_list = 1;
inline int FeatureLists::feature_list_size() const {
  return feature_list_.size();
}
inline void FeatureLists::clear_feature_list() {
  feature_list_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::tensorflow::FeatureList >&
FeatureLists::feature_list() const {
  // @@protoc_insertion_point(field_map:tensorflow.FeatureLists.feature_list)
  return feature_list_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::tensorflow::FeatureList >*
FeatureLists::mutable_feature_list() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.FeatureLists.feature_list)
  return feature_list_.MutableMap();
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fexample_2ffeature_2eproto

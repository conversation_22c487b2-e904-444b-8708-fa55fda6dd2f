// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/example/feature.proto

#include "tensorflow/core/example/feature.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_BytesList;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_FloatList;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_Int64List;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_FeatureList;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_FeatureLists_FeatureListEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_Features_FeatureEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto ::google::protobuf::internal::SCCInfo<3> scc_info_Feature;
}  // namespace protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto
namespace tensorflow {
class BytesListDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<BytesList>
      _instance;
} _BytesList_default_instance_;
class FloatListDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<FloatList>
      _instance;
} _FloatList_default_instance_;
class Int64ListDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Int64List>
      _instance;
} _Int64List_default_instance_;
class FeatureDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Feature>
      _instance;
  const ::tensorflow::BytesList* bytes_list_;
  const ::tensorflow::FloatList* float_list_;
  const ::tensorflow::Int64List* int64_list_;
} _Feature_default_instance_;
class Features_FeatureEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Features_FeatureEntry_DoNotUse>
      _instance;
} _Features_FeatureEntry_DoNotUse_default_instance_;
class FeaturesDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Features>
      _instance;
} _Features_default_instance_;
class FeatureListDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<FeatureList>
      _instance;
} _FeatureList_default_instance_;
class FeatureLists_FeatureListEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<FeatureLists_FeatureListEntry_DoNotUse>
      _instance;
} _FeatureLists_FeatureListEntry_DoNotUse_default_instance_;
class FeatureListsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<FeatureLists>
      _instance;
} _FeatureLists_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto {
static void InitDefaultsBytesList() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_BytesList_default_instance_;
    new (ptr) ::tensorflow::BytesList();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::BytesList::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_BytesList =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsBytesList}, {}};

static void InitDefaultsFloatList() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_FloatList_default_instance_;
    new (ptr) ::tensorflow::FloatList();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::FloatList::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_FloatList =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsFloatList}, {}};

static void InitDefaultsInt64List() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_Int64List_default_instance_;
    new (ptr) ::tensorflow::Int64List();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::Int64List::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_Int64List =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsInt64List}, {}};

static void InitDefaultsFeature() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_Feature_default_instance_;
    new (ptr) ::tensorflow::Feature();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::Feature::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<3> scc_info_Feature =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 3, InitDefaultsFeature}, {
      &protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::scc_info_BytesList.base,
      &protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::scc_info_FloatList.base,
      &protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::scc_info_Int64List.base,}};

static void InitDefaultsFeatures_FeatureEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_Features_FeatureEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::Features_FeatureEntry_DoNotUse();
  }
  ::tensorflow::Features_FeatureEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_Features_FeatureEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsFeatures_FeatureEntry_DoNotUse}, {
      &protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::scc_info_Feature.base,}};

static void InitDefaultsFeatures() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_Features_default_instance_;
    new (ptr) ::tensorflow::Features();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::Features::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_Features =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsFeatures}, {
      &protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::scc_info_Features_FeatureEntry_DoNotUse.base,}};

static void InitDefaultsFeatureList() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_FeatureList_default_instance_;
    new (ptr) ::tensorflow::FeatureList();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::FeatureList::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_FeatureList =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsFeatureList}, {
      &protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::scc_info_Feature.base,}};

static void InitDefaultsFeatureLists_FeatureListEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_FeatureLists_FeatureListEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::FeatureLists_FeatureListEntry_DoNotUse();
  }
  ::tensorflow::FeatureLists_FeatureListEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_FeatureLists_FeatureListEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsFeatureLists_FeatureListEntry_DoNotUse}, {
      &protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::scc_info_FeatureList.base,}};

static void InitDefaultsFeatureLists() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_FeatureLists_default_instance_;
    new (ptr) ::tensorflow::FeatureLists();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::FeatureLists::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_FeatureLists =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsFeatureLists}, {
      &protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::scc_info_FeatureLists_FeatureListEntry_DoNotUse.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_BytesList.base);
  ::google::protobuf::internal::InitSCC(&scc_info_FloatList.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Int64List.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Feature.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Features_FeatureEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Features.base);
  ::google::protobuf::internal::InitSCC(&scc_info_FeatureList.base);
  ::google::protobuf::internal::InitSCC(&scc_info_FeatureLists_FeatureListEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_FeatureLists.base);
}

::google::protobuf::Metadata file_level_metadata[9];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BytesList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BytesList, value_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FloatList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FloatList, value_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Int64List, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Int64List, value_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Feature, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Feature, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  offsetof(::tensorflow::FeatureDefaultTypeInternal, bytes_list_),
  offsetof(::tensorflow::FeatureDefaultTypeInternal, float_list_),
  offsetof(::tensorflow::FeatureDefaultTypeInternal, int64_list_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Feature, kind_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Features_FeatureEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Features_FeatureEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Features_FeatureEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Features_FeatureEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Features, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::Features, feature_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FeatureList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FeatureList, feature_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FeatureLists_FeatureListEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FeatureLists_FeatureListEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FeatureLists_FeatureListEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FeatureLists_FeatureListEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FeatureLists, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FeatureLists, feature_list_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::BytesList)},
  { 6, -1, sizeof(::tensorflow::FloatList)},
  { 12, -1, sizeof(::tensorflow::Int64List)},
  { 18, -1, sizeof(::tensorflow::Feature)},
  { 27, 34, sizeof(::tensorflow::Features_FeatureEntry_DoNotUse)},
  { 36, -1, sizeof(::tensorflow::Features)},
  { 42, -1, sizeof(::tensorflow::FeatureList)},
  { 48, 55, sizeof(::tensorflow::FeatureLists_FeatureListEntry_DoNotUse)},
  { 57, -1, sizeof(::tensorflow::FeatureLists)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_BytesList_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_FloatList_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_Int64List_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_Feature_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_Features_FeatureEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_Features_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_FeatureList_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_FeatureLists_FeatureListEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_FeatureLists_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/example/feature.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 9);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n%tensorflow/core/example/feature.proto\022"
      "\ntensorflow\"\032\n\tBytesList\022\r\n\005value\030\001 \003(\014\""
      "\036\n\tFloatList\022\021\n\005value\030\001 \003(\002B\002\020\001\"\036\n\tInt64"
      "List\022\021\n\005value\030\001 \003(\003B\002\020\001\"\230\001\n\007Feature\022+\n\nb"
      "ytes_list\030\001 \001(\0132\025.tensorflow.BytesListH\000"
      "\022+\n\nfloat_list\030\002 \001(\0132\025.tensorflow.FloatL"
      "istH\000\022+\n\nint64_list\030\003 \001(\0132\025.tensorflow.I"
      "nt64ListH\000B\006\n\004kind\"\203\001\n\010Features\0222\n\007featu"
      "re\030\001 \003(\0132!.tensorflow.Features.FeatureEn"
      "try\032C\n\014FeatureEntry\022\013\n\003key\030\001 \001(\t\022\"\n\005valu"
      "e\030\002 \001(\0132\023.tensorflow.Feature:\0028\001\"3\n\013Feat"
      "ureList\022$\n\007feature\030\001 \003(\0132\023.tensorflow.Fe"
      "ature\"\234\001\n\014FeatureLists\022\?\n\014feature_list\030\001"
      " \003(\0132).tensorflow.FeatureLists.FeatureLi"
      "stEntry\032K\n\020FeatureListEntry\022\013\n\003key\030\001 \001(\t"
      "\022&\n\005value\030\002 \001(\0132\027.tensorflow.FeatureList"
      ":\0028\001Bi\n\026org.tensorflow.exampleB\rFeatureP"
      "rotosP\001Z;github.com/tensorflow/tensorflo"
      "w/tensorflow/go/core/example\370\001\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 759);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/example/feature.proto", &protobuf_RegisterTypes);
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto
namespace tensorflow {

// ===================================================================

void BytesList::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int BytesList::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

BytesList::BytesList()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::scc_info_BytesList.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.BytesList)
}
BytesList::BytesList(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  value_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::scc_info_BytesList.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.BytesList)
}
BytesList::BytesList(const BytesList& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      value_(from.value_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.BytesList)
}

void BytesList::SharedCtor() {
}

BytesList::~BytesList() {
  // @@protoc_insertion_point(destructor:tensorflow.BytesList)
  SharedDtor();
}

void BytesList::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void BytesList::ArenaDtor(void* object) {
  BytesList* _this = reinterpret_cast< BytesList* >(object);
  (void)_this;
}
void BytesList::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void BytesList::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* BytesList::descriptor() {
  ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const BytesList& BytesList::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::scc_info_BytesList.base);
  return *internal_default_instance();
}


void BytesList::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.BytesList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  value_.Clear();
  _internal_metadata_.Clear();
}

bool BytesList::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.BytesList)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated bytes value = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->add_value()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.BytesList)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.BytesList)
  return false;
#undef DO_
}

void BytesList::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.BytesList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated bytes value = 1;
  for (int i = 0, n = this->value_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      1, this->value(i), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.BytesList)
}

::google::protobuf::uint8* BytesList::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.BytesList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated bytes value = 1;
  for (int i = 0, n = this->value_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteBytesToArray(1, this->value(i), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.BytesList)
  return target;
}

size_t BytesList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.BytesList)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated bytes value = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->value_size());
  for (int i = 0, n = this->value_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::BytesSize(
      this->value(i));
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void BytesList::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.BytesList)
  GOOGLE_DCHECK_NE(&from, this);
  const BytesList* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const BytesList>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.BytesList)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.BytesList)
    MergeFrom(*source);
  }
}

void BytesList::MergeFrom(const BytesList& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.BytesList)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  value_.MergeFrom(from.value_);
}

void BytesList::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.BytesList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void BytesList::CopyFrom(const BytesList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.BytesList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BytesList::IsInitialized() const {
  return true;
}

void BytesList::Swap(BytesList* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    BytesList* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void BytesList::UnsafeArenaSwap(BytesList* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void BytesList::InternalSwap(BytesList* other) {
  using std::swap;
  value_.InternalSwap(CastToBase(&other->value_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata BytesList::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void FloatList::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int FloatList::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

FloatList::FloatList()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::scc_info_FloatList.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.FloatList)
}
FloatList::FloatList(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  value_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::scc_info_FloatList.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.FloatList)
}
FloatList::FloatList(const FloatList& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      value_(from.value_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.FloatList)
}

void FloatList::SharedCtor() {
}

FloatList::~FloatList() {
  // @@protoc_insertion_point(destructor:tensorflow.FloatList)
  SharedDtor();
}

void FloatList::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void FloatList::ArenaDtor(void* object) {
  FloatList* _this = reinterpret_cast< FloatList* >(object);
  (void)_this;
}
void FloatList::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void FloatList::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* FloatList::descriptor() {
  ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const FloatList& FloatList::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::scc_info_FloatList.base);
  return *internal_default_instance();
}


void FloatList::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.FloatList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  value_.Clear();
  _internal_metadata_.Clear();
}

bool FloatList::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.FloatList)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated float value = 1 [packed = true];
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, this->mutable_value())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(13u /* 13 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 1, 10u, input, this->mutable_value())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.FloatList)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.FloatList)
  return false;
#undef DO_
}

void FloatList::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.FloatList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated float value = 1 [packed = true];
  if (this->value_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(1, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _value_cached_byte_size_));
    ::google::protobuf::internal::WireFormatLite::WriteFloatArray(
      this->value().data(), this->value_size(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.FloatList)
}

::google::protobuf::uint8* FloatList::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.FloatList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated float value = 1 [packed = true];
  if (this->value_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      1,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _value_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteFloatNoTagToArray(this->value_, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.FloatList)
  return target;
}

size_t FloatList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.FloatList)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated float value = 1 [packed = true];
  {
    unsigned int count = static_cast<unsigned int>(this->value_size());
    size_t data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _value_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void FloatList::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.FloatList)
  GOOGLE_DCHECK_NE(&from, this);
  const FloatList* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const FloatList>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.FloatList)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.FloatList)
    MergeFrom(*source);
  }
}

void FloatList::MergeFrom(const FloatList& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.FloatList)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  value_.MergeFrom(from.value_);
}

void FloatList::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.FloatList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FloatList::CopyFrom(const FloatList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.FloatList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FloatList::IsInitialized() const {
  return true;
}

void FloatList::Swap(FloatList* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    FloatList* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void FloatList::UnsafeArenaSwap(FloatList* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void FloatList::InternalSwap(FloatList* other) {
  using std::swap;
  value_.InternalSwap(&other->value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata FloatList::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Int64List::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Int64List::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Int64List::Int64List()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::scc_info_Int64List.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.Int64List)
}
Int64List::Int64List(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  value_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::scc_info_Int64List.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.Int64List)
}
Int64List::Int64List(const Int64List& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      value_(from.value_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.Int64List)
}

void Int64List::SharedCtor() {
}

Int64List::~Int64List() {
  // @@protoc_insertion_point(destructor:tensorflow.Int64List)
  SharedDtor();
}

void Int64List::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void Int64List::ArenaDtor(void* object) {
  Int64List* _this = reinterpret_cast< Int64List* >(object);
  (void)_this;
}
void Int64List::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void Int64List::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Int64List::descriptor() {
  ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Int64List& Int64List::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::scc_info_Int64List.base);
  return *internal_default_instance();
}


void Int64List::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.Int64List)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  value_.Clear();
  _internal_metadata_.Clear();
}

bool Int64List::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.Int64List)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated int64 value = 1 [packed = true];
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_value())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 1, 10u, input, this->mutable_value())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.Int64List)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.Int64List)
  return false;
#undef DO_
}

void Int64List::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.Int64List)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated int64 value = 1 [packed = true];
  if (this->value_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(1, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _value_cached_byte_size_));
  }
  for (int i = 0, n = this->value_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->value(i), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.Int64List)
}

::google::protobuf::uint8* Int64List::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.Int64List)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated int64 value = 1 [packed = true];
  if (this->value_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      1,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _value_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->value_, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.Int64List)
  return target;
}

size_t Int64List::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.Int64List)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated int64 value = 1 [packed = true];
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int64Size(this->value_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _value_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Int64List::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.Int64List)
  GOOGLE_DCHECK_NE(&from, this);
  const Int64List* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Int64List>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.Int64List)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.Int64List)
    MergeFrom(*source);
  }
}

void Int64List::MergeFrom(const Int64List& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.Int64List)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  value_.MergeFrom(from.value_);
}

void Int64List::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.Int64List)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Int64List::CopyFrom(const Int64List& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.Int64List)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Int64List::IsInitialized() const {
  return true;
}

void Int64List::Swap(Int64List* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    Int64List* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void Int64List::UnsafeArenaSwap(Int64List* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void Int64List::InternalSwap(Int64List* other) {
  using std::swap;
  value_.InternalSwap(&other->value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Int64List::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Feature::InitAsDefaultInstance() {
  ::tensorflow::_Feature_default_instance_.bytes_list_ = const_cast< ::tensorflow::BytesList*>(
      ::tensorflow::BytesList::internal_default_instance());
  ::tensorflow::_Feature_default_instance_.float_list_ = const_cast< ::tensorflow::FloatList*>(
      ::tensorflow::FloatList::internal_default_instance());
  ::tensorflow::_Feature_default_instance_.int64_list_ = const_cast< ::tensorflow::Int64List*>(
      ::tensorflow::Int64List::internal_default_instance());
}
void Feature::set_allocated_bytes_list(::tensorflow::BytesList* bytes_list) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_kind();
  if (bytes_list) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(bytes_list);
    if (message_arena != submessage_arena) {
      bytes_list = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, bytes_list, submessage_arena);
    }
    set_has_bytes_list();
    kind_.bytes_list_ = bytes_list;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Feature.bytes_list)
}
void Feature::set_allocated_float_list(::tensorflow::FloatList* float_list) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_kind();
  if (float_list) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(float_list);
    if (message_arena != submessage_arena) {
      float_list = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, float_list, submessage_arena);
    }
    set_has_float_list();
    kind_.float_list_ = float_list;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Feature.float_list)
}
void Feature::set_allocated_int64_list(::tensorflow::Int64List* int64_list) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_kind();
  if (int64_list) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(int64_list);
    if (message_arena != submessage_arena) {
      int64_list = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, int64_list, submessage_arena);
    }
    set_has_int64_list();
    kind_.int64_list_ = int64_list;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Feature.int64_list)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Feature::kBytesListFieldNumber;
const int Feature::kFloatListFieldNumber;
const int Feature::kInt64ListFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Feature::Feature()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::scc_info_Feature.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.Feature)
}
Feature::Feature(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::scc_info_Feature.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.Feature)
}
Feature::Feature(const Feature& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  clear_has_kind();
  switch (from.kind_case()) {
    case kBytesList: {
      mutable_bytes_list()->::tensorflow::BytesList::MergeFrom(from.bytes_list());
      break;
    }
    case kFloatList: {
      mutable_float_list()->::tensorflow::FloatList::MergeFrom(from.float_list());
      break;
    }
    case kInt64List: {
      mutable_int64_list()->::tensorflow::Int64List::MergeFrom(from.int64_list());
      break;
    }
    case KIND_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.Feature)
}

void Feature::SharedCtor() {
  clear_has_kind();
}

Feature::~Feature() {
  // @@protoc_insertion_point(destructor:tensorflow.Feature)
  SharedDtor();
}

void Feature::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (has_kind()) {
    clear_kind();
  }
}

void Feature::ArenaDtor(void* object) {
  Feature* _this = reinterpret_cast< Feature* >(object);
  (void)_this;
}
void Feature::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void Feature::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Feature::descriptor() {
  ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Feature& Feature::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::scc_info_Feature.base);
  return *internal_default_instance();
}


void Feature::clear_kind() {
// @@protoc_insertion_point(one_of_clear_start:tensorflow.Feature)
  switch (kind_case()) {
    case kBytesList: {
      if (GetArenaNoVirtual() == NULL) {
        delete kind_.bytes_list_;
      }
      break;
    }
    case kFloatList: {
      if (GetArenaNoVirtual() == NULL) {
        delete kind_.float_list_;
      }
      break;
    }
    case kInt64List: {
      if (GetArenaNoVirtual() == NULL) {
        delete kind_.int64_list_;
      }
      break;
    }
    case KIND_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = KIND_NOT_SET;
}


void Feature::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.Feature)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_kind();
  _internal_metadata_.Clear();
}

bool Feature::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.Feature)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.BytesList bytes_list = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_bytes_list()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.FloatList float_list = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_float_list()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.Int64List int64_list = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_int64_list()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.Feature)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.Feature)
  return false;
#undef DO_
}

void Feature::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.Feature)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.BytesList bytes_list = 1;
  if (has_bytes_list()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->_internal_bytes_list(), output);
  }

  // .tensorflow.FloatList float_list = 2;
  if (has_float_list()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_float_list(), output);
  }

  // .tensorflow.Int64List int64_list = 3;
  if (has_int64_list()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->_internal_int64_list(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.Feature)
}

::google::protobuf::uint8* Feature::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.Feature)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.BytesList bytes_list = 1;
  if (has_bytes_list()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->_internal_bytes_list(), deterministic, target);
  }

  // .tensorflow.FloatList float_list = 2;
  if (has_float_list()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_float_list(), deterministic, target);
  }

  // .tensorflow.Int64List int64_list = 3;
  if (has_int64_list()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->_internal_int64_list(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.Feature)
  return target;
}

size_t Feature::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.Feature)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  switch (kind_case()) {
    // .tensorflow.BytesList bytes_list = 1;
    case kBytesList: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *kind_.bytes_list_);
      break;
    }
    // .tensorflow.FloatList float_list = 2;
    case kFloatList: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *kind_.float_list_);
      break;
    }
    // .tensorflow.Int64List int64_list = 3;
    case kInt64List: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *kind_.int64_list_);
      break;
    }
    case KIND_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Feature::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.Feature)
  GOOGLE_DCHECK_NE(&from, this);
  const Feature* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Feature>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.Feature)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.Feature)
    MergeFrom(*source);
  }
}

void Feature::MergeFrom(const Feature& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.Feature)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.kind_case()) {
    case kBytesList: {
      mutable_bytes_list()->::tensorflow::BytesList::MergeFrom(from.bytes_list());
      break;
    }
    case kFloatList: {
      mutable_float_list()->::tensorflow::FloatList::MergeFrom(from.float_list());
      break;
    }
    case kInt64List: {
      mutable_int64_list()->::tensorflow::Int64List::MergeFrom(from.int64_list());
      break;
    }
    case KIND_NOT_SET: {
      break;
    }
  }
}

void Feature::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.Feature)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Feature::CopyFrom(const Feature& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.Feature)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Feature::IsInitialized() const {
  return true;
}

void Feature::Swap(Feature* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    Feature* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void Feature::UnsafeArenaSwap(Feature* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void Feature::InternalSwap(Feature* other) {
  using std::swap;
  swap(kind_, other->kind_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Feature::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

Features_FeatureEntry_DoNotUse::Features_FeatureEntry_DoNotUse() {}
Features_FeatureEntry_DoNotUse::Features_FeatureEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void Features_FeatureEntry_DoNotUse::MergeFrom(const Features_FeatureEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata Features_FeatureEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::file_level_metadata[4];
}
void Features_FeatureEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void Features::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Features::kFeatureFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Features::Features()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::scc_info_Features.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.Features)
}
Features::Features(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  feature_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::scc_info_Features.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.Features)
}
Features::Features(const Features& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  feature_.MergeFrom(from.feature_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.Features)
}

void Features::SharedCtor() {
}

Features::~Features() {
  // @@protoc_insertion_point(destructor:tensorflow.Features)
  SharedDtor();
}

void Features::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void Features::ArenaDtor(void* object) {
  Features* _this = reinterpret_cast< Features* >(object);
  (void)_this;
}
void Features::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void Features::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Features::descriptor() {
  ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Features& Features::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::scc_info_Features.base);
  return *internal_default_instance();
}


void Features::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.Features)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  feature_.Clear();
  _internal_metadata_.Clear();
}

bool Features::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.Features)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<string, .tensorflow.Feature> feature = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          Features_FeatureEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              Features_FeatureEntry_DoNotUse,
              ::std::string, ::tensorflow::Feature,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::tensorflow::Feature > > parser(&feature_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.Features.FeatureEntry.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.Features)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.Features)
  return false;
#undef DO_
}

void Features::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.Features)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, .tensorflow.Feature> feature = 1;
  if (!this->feature().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::Feature >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.Features.FeatureEntry.key");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->feature().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->feature().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::Feature >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::Feature >::const_iterator
          it = this->feature().begin();
          it != this->feature().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<Features_FeatureEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(feature_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<Features_FeatureEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::Feature >::const_iterator
          it = this->feature().begin();
          it != this->feature().end(); ++it) {
        entry.reset(feature_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.Features)
}

::google::protobuf::uint8* Features::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.Features)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, .tensorflow.Feature> feature = 1;
  if (!this->feature().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::Feature >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.Features.FeatureEntry.key");
      }
    };

    if (deterministic &&
        this->feature().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->feature().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::Feature >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::Feature >::const_iterator
          it = this->feature().begin();
          it != this->feature().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<Features_FeatureEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(feature_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<Features_FeatureEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::Feature >::const_iterator
          it = this->feature().begin();
          it != this->feature().end(); ++it) {
        entry.reset(feature_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.Features)
  return target;
}

size_t Features::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.Features)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // map<string, .tensorflow.Feature> feature = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->feature_size());
  {
    ::std::unique_ptr<Features_FeatureEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::tensorflow::Feature >::const_iterator
        it = this->feature().begin();
        it != this->feature().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(feature_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Features::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.Features)
  GOOGLE_DCHECK_NE(&from, this);
  const Features* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Features>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.Features)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.Features)
    MergeFrom(*source);
  }
}

void Features::MergeFrom(const Features& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.Features)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  feature_.MergeFrom(from.feature_);
}

void Features::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.Features)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Features::CopyFrom(const Features& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.Features)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Features::IsInitialized() const {
  return true;
}

void Features::Swap(Features* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    Features* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void Features::UnsafeArenaSwap(Features* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void Features::InternalSwap(Features* other) {
  using std::swap;
  feature_.Swap(&other->feature_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Features::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void FeatureList::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int FeatureList::kFeatureFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

FeatureList::FeatureList()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::scc_info_FeatureList.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.FeatureList)
}
FeatureList::FeatureList(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  feature_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::scc_info_FeatureList.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.FeatureList)
}
FeatureList::FeatureList(const FeatureList& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      feature_(from.feature_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.FeatureList)
}

void FeatureList::SharedCtor() {
}

FeatureList::~FeatureList() {
  // @@protoc_insertion_point(destructor:tensorflow.FeatureList)
  SharedDtor();
}

void FeatureList::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void FeatureList::ArenaDtor(void* object) {
  FeatureList* _this = reinterpret_cast< FeatureList* >(object);
  (void)_this;
}
void FeatureList::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void FeatureList::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* FeatureList::descriptor() {
  ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const FeatureList& FeatureList::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::scc_info_FeatureList.base);
  return *internal_default_instance();
}


void FeatureList::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.FeatureList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  feature_.Clear();
  _internal_metadata_.Clear();
}

bool FeatureList::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.FeatureList)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.Feature feature = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_feature()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.FeatureList)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.FeatureList)
  return false;
#undef DO_
}

void FeatureList::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.FeatureList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.Feature feature = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->feature_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->feature(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.FeatureList)
}

::google::protobuf::uint8* FeatureList::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.FeatureList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.Feature feature = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->feature_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->feature(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.FeatureList)
  return target;
}

size_t FeatureList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.FeatureList)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.Feature feature = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->feature_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->feature(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void FeatureList::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.FeatureList)
  GOOGLE_DCHECK_NE(&from, this);
  const FeatureList* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const FeatureList>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.FeatureList)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.FeatureList)
    MergeFrom(*source);
  }
}

void FeatureList::MergeFrom(const FeatureList& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.FeatureList)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  feature_.MergeFrom(from.feature_);
}

void FeatureList::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.FeatureList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FeatureList::CopyFrom(const FeatureList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.FeatureList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FeatureList::IsInitialized() const {
  return true;
}

void FeatureList::Swap(FeatureList* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    FeatureList* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void FeatureList::UnsafeArenaSwap(FeatureList* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void FeatureList::InternalSwap(FeatureList* other) {
  using std::swap;
  CastToBase(&feature_)->InternalSwap(CastToBase(&other->feature_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata FeatureList::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

FeatureLists_FeatureListEntry_DoNotUse::FeatureLists_FeatureListEntry_DoNotUse() {}
FeatureLists_FeatureListEntry_DoNotUse::FeatureLists_FeatureListEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void FeatureLists_FeatureListEntry_DoNotUse::MergeFrom(const FeatureLists_FeatureListEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata FeatureLists_FeatureListEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::file_level_metadata[7];
}
void FeatureLists_FeatureListEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void FeatureLists::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int FeatureLists::kFeatureListFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

FeatureLists::FeatureLists()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::scc_info_FeatureLists.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.FeatureLists)
}
FeatureLists::FeatureLists(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  feature_list_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::scc_info_FeatureLists.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.FeatureLists)
}
FeatureLists::FeatureLists(const FeatureLists& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  feature_list_.MergeFrom(from.feature_list_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.FeatureLists)
}

void FeatureLists::SharedCtor() {
}

FeatureLists::~FeatureLists() {
  // @@protoc_insertion_point(destructor:tensorflow.FeatureLists)
  SharedDtor();
}

void FeatureLists::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void FeatureLists::ArenaDtor(void* object) {
  FeatureLists* _this = reinterpret_cast< FeatureLists* >(object);
  (void)_this;
}
void FeatureLists::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void FeatureLists::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* FeatureLists::descriptor() {
  ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const FeatureLists& FeatureLists::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::scc_info_FeatureLists.base);
  return *internal_default_instance();
}


void FeatureLists::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.FeatureLists)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  feature_list_.Clear();
  _internal_metadata_.Clear();
}

bool FeatureLists::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.FeatureLists)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<string, .tensorflow.FeatureList> feature_list = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          FeatureLists_FeatureListEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              FeatureLists_FeatureListEntry_DoNotUse,
              ::std::string, ::tensorflow::FeatureList,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::tensorflow::FeatureList > > parser(&feature_list_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.FeatureLists.FeatureListEntry.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.FeatureLists)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.FeatureLists)
  return false;
#undef DO_
}

void FeatureLists::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.FeatureLists)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, .tensorflow.FeatureList> feature_list = 1;
  if (!this->feature_list().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::FeatureList >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.FeatureLists.FeatureListEntry.key");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->feature_list().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->feature_list().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::FeatureList >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::FeatureList >::const_iterator
          it = this->feature_list().begin();
          it != this->feature_list().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<FeatureLists_FeatureListEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(feature_list_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<FeatureLists_FeatureListEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::FeatureList >::const_iterator
          it = this->feature_list().begin();
          it != this->feature_list().end(); ++it) {
        entry.reset(feature_list_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.FeatureLists)
}

::google::protobuf::uint8* FeatureLists::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.FeatureLists)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, .tensorflow.FeatureList> feature_list = 1;
  if (!this->feature_list().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::FeatureList >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.FeatureLists.FeatureListEntry.key");
      }
    };

    if (deterministic &&
        this->feature_list().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->feature_list().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::FeatureList >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::FeatureList >::const_iterator
          it = this->feature_list().begin();
          it != this->feature_list().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<FeatureLists_FeatureListEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(feature_list_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<FeatureLists_FeatureListEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::FeatureList >::const_iterator
          it = this->feature_list().begin();
          it != this->feature_list().end(); ++it) {
        entry.reset(feature_list_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.FeatureLists)
  return target;
}

size_t FeatureLists::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.FeatureLists)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // map<string, .tensorflow.FeatureList> feature_list = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->feature_list_size());
  {
    ::std::unique_ptr<FeatureLists_FeatureListEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::tensorflow::FeatureList >::const_iterator
        it = this->feature_list().begin();
        it != this->feature_list().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(feature_list_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void FeatureLists::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.FeatureLists)
  GOOGLE_DCHECK_NE(&from, this);
  const FeatureLists* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const FeatureLists>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.FeatureLists)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.FeatureLists)
    MergeFrom(*source);
  }
}

void FeatureLists::MergeFrom(const FeatureLists& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.FeatureLists)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  feature_list_.MergeFrom(from.feature_list_);
}

void FeatureLists::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.FeatureLists)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FeatureLists::CopyFrom(const FeatureLists& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.FeatureLists)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FeatureLists::IsInitialized() const {
  return true;
}

void FeatureLists::Swap(FeatureLists* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    FeatureLists* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void FeatureLists::UnsafeArenaSwap(FeatureLists* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void FeatureLists::InternalSwap(FeatureLists* other) {
  using std::swap;
  feature_list_.Swap(&other->feature_list_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata FeatureLists::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fexample_2ffeature_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::BytesList* Arena::CreateMaybeMessage< ::tensorflow::BytesList >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::BytesList >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::FloatList* Arena::CreateMaybeMessage< ::tensorflow::FloatList >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::FloatList >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::Int64List* Arena::CreateMaybeMessage< ::tensorflow::Int64List >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::Int64List >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::Feature* Arena::CreateMaybeMessage< ::tensorflow::Feature >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::Feature >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::Features_FeatureEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::Features_FeatureEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::Features_FeatureEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::Features* Arena::CreateMaybeMessage< ::tensorflow::Features >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::Features >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::FeatureList* Arena::CreateMaybeMessage< ::tensorflow::FeatureList >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::FeatureList >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::FeatureLists_FeatureListEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::FeatureLists_FeatureListEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::FeatureLists_FeatureListEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::FeatureLists* Arena::CreateMaybeMessage< ::tensorflow::FeatureLists >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::FeatureLists >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

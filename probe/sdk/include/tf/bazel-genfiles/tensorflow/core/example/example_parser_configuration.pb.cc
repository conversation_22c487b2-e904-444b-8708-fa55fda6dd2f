// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/example/example_parser_configuration.proto

#include "tensorflow/core/example/example_parser_configuration.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_VarLenFeatureProto;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_ExampleParserConfiguration_FeatureMapEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_FeatureConfiguration;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_FixedLenFeatureProto;
}  // namespace protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_TensorProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_TensorShapeProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto
namespace tensorflow {
class VarLenFeatureProtoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<VarLenFeatureProto>
      _instance;
} _VarLenFeatureProto_default_instance_;
class FixedLenFeatureProtoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<FixedLenFeatureProto>
      _instance;
} _FixedLenFeatureProto_default_instance_;
class FeatureConfigurationDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<FeatureConfiguration>
      _instance;
  const ::tensorflow::FixedLenFeatureProto* fixed_len_feature_;
  const ::tensorflow::VarLenFeatureProto* var_len_feature_;
} _FeatureConfiguration_default_instance_;
class ExampleParserConfiguration_FeatureMapEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ExampleParserConfiguration_FeatureMapEntry_DoNotUse>
      _instance;
} _ExampleParserConfiguration_FeatureMapEntry_DoNotUse_default_instance_;
class ExampleParserConfigurationDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ExampleParserConfiguration>
      _instance;
} _ExampleParserConfiguration_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto {
static void InitDefaultsVarLenFeatureProto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_VarLenFeatureProto_default_instance_;
    new (ptr) ::tensorflow::VarLenFeatureProto();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::VarLenFeatureProto::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_VarLenFeatureProto =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsVarLenFeatureProto}, {}};

static void InitDefaultsFixedLenFeatureProto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_FixedLenFeatureProto_default_instance_;
    new (ptr) ::tensorflow::FixedLenFeatureProto();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::FixedLenFeatureProto::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_FixedLenFeatureProto =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsFixedLenFeatureProto}, {
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::scc_info_TensorShapeProto.base,
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::scc_info_TensorProto.base,}};

static void InitDefaultsFeatureConfiguration() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_FeatureConfiguration_default_instance_;
    new (ptr) ::tensorflow::FeatureConfiguration();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::FeatureConfiguration::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_FeatureConfiguration =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsFeatureConfiguration}, {
      &protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::scc_info_FixedLenFeatureProto.base,
      &protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::scc_info_VarLenFeatureProto.base,}};

static void InitDefaultsExampleParserConfiguration_FeatureMapEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_ExampleParserConfiguration_FeatureMapEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::ExampleParserConfiguration_FeatureMapEntry_DoNotUse();
  }
  ::tensorflow::ExampleParserConfiguration_FeatureMapEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_ExampleParserConfiguration_FeatureMapEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsExampleParserConfiguration_FeatureMapEntry_DoNotUse}, {
      &protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::scc_info_FeatureConfiguration.base,}};

static void InitDefaultsExampleParserConfiguration() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_ExampleParserConfiguration_default_instance_;
    new (ptr) ::tensorflow::ExampleParserConfiguration();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::ExampleParserConfiguration::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_ExampleParserConfiguration =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsExampleParserConfiguration}, {
      &protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::scc_info_ExampleParserConfiguration_FeatureMapEntry_DoNotUse.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_VarLenFeatureProto.base);
  ::google::protobuf::internal::InitSCC(&scc_info_FixedLenFeatureProto.base);
  ::google::protobuf::internal::InitSCC(&scc_info_FeatureConfiguration.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ExampleParserConfiguration_FeatureMapEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ExampleParserConfiguration.base);
}

::google::protobuf::Metadata file_level_metadata[5];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::VarLenFeatureProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::VarLenFeatureProto, dtype_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::VarLenFeatureProto, values_output_tensor_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::VarLenFeatureProto, indices_output_tensor_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::VarLenFeatureProto, shapes_output_tensor_name_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FixedLenFeatureProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FixedLenFeatureProto, dtype_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FixedLenFeatureProto, shape_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FixedLenFeatureProto, default_value_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FixedLenFeatureProto, values_output_tensor_name_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FeatureConfiguration, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FeatureConfiguration, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  offsetof(::tensorflow::FeatureConfigurationDefaultTypeInternal, fixed_len_feature_),
  offsetof(::tensorflow::FeatureConfigurationDefaultTypeInternal, var_len_feature_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FeatureConfiguration, config_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ExampleParserConfiguration_FeatureMapEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ExampleParserConfiguration_FeatureMapEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ExampleParserConfiguration_FeatureMapEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ExampleParserConfiguration_FeatureMapEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ExampleParserConfiguration, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ExampleParserConfiguration, feature_map_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::VarLenFeatureProto)},
  { 9, -1, sizeof(::tensorflow::FixedLenFeatureProto)},
  { 18, -1, sizeof(::tensorflow::FeatureConfiguration)},
  { 26, 33, sizeof(::tensorflow::ExampleParserConfiguration_FeatureMapEntry_DoNotUse)},
  { 35, -1, sizeof(::tensorflow::ExampleParserConfiguration)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_VarLenFeatureProto_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_FixedLenFeatureProto_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_FeatureConfiguration_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_ExampleParserConfiguration_FeatureMapEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_ExampleParserConfiguration_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/example/example_parser_configuration.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 5);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n:tensorflow/core/example/example_parser"
      "_configuration.proto\022\ntensorflow\032,tensor"
      "flow/core/framework/tensor_shape.proto\032&"
      "tensorflow/core/framework/tensor.proto\032%"
      "tensorflow/core/framework/types.proto\"\243\001"
      "\n\022VarLenFeatureProto\022#\n\005dtype\030\001 \001(\0162\024.te"
      "nsorflow.DataType\022!\n\031values_output_tenso"
      "r_name\030\002 \001(\t\022\"\n\032indices_output_tensor_na"
      "me\030\003 \001(\t\022!\n\031shapes_output_tensor_name\030\004 "
      "\001(\t\"\273\001\n\024FixedLenFeatureProto\022#\n\005dtype\030\001 "
      "\001(\0162\024.tensorflow.DataType\022+\n\005shape\030\002 \001(\013"
      "2\034.tensorflow.TensorShapeProto\022.\n\rdefaul"
      "t_value\030\003 \001(\0132\027.tensorflow.TensorProto\022!"
      "\n\031values_output_tensor_name\030\004 \001(\t\"\232\001\n\024Fe"
      "atureConfiguration\022=\n\021fixed_len_feature\030"
      "\001 \001(\0132 .tensorflow.FixedLenFeatureProtoH"
      "\000\0229\n\017var_len_feature\030\002 \001(\0132\036.tensorflow."
      "VarLenFeatureProtoH\000B\010\n\006config\"\276\001\n\032Examp"
      "leParserConfiguration\022K\n\013feature_map\030\001 \003"
      "(\01326.tensorflow.ExampleParserConfigurati"
      "on.FeatureMapEntry\032S\n\017FeatureMapEntry\022\013\n"
      "\003key\030\001 \001(\t\022/\n\005value\030\002 \001(\0132 .tensorflow.F"
      "eatureConfiguration:\0028\001B|\n\026org.tensorflo"
      "w.exampleB ExampleParserConfigurationPro"
      "tosP\001Z;github.com/tensorflow/tensorflow/"
      "tensorflow/go/core/example\370\001\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 1037);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/example/example_parser_configuration.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftypes_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto
namespace tensorflow {

// ===================================================================

void VarLenFeatureProto::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int VarLenFeatureProto::kDtypeFieldNumber;
const int VarLenFeatureProto::kValuesOutputTensorNameFieldNumber;
const int VarLenFeatureProto::kIndicesOutputTensorNameFieldNumber;
const int VarLenFeatureProto::kShapesOutputTensorNameFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

VarLenFeatureProto::VarLenFeatureProto()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::scc_info_VarLenFeatureProto.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.VarLenFeatureProto)
}
VarLenFeatureProto::VarLenFeatureProto(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::scc_info_VarLenFeatureProto.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.VarLenFeatureProto)
}
VarLenFeatureProto::VarLenFeatureProto(const VarLenFeatureProto& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  values_output_tensor_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.values_output_tensor_name().size() > 0) {
    values_output_tensor_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.values_output_tensor_name(),
      GetArenaNoVirtual());
  }
  indices_output_tensor_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.indices_output_tensor_name().size() > 0) {
    indices_output_tensor_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.indices_output_tensor_name(),
      GetArenaNoVirtual());
  }
  shapes_output_tensor_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.shapes_output_tensor_name().size() > 0) {
    shapes_output_tensor_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.shapes_output_tensor_name(),
      GetArenaNoVirtual());
  }
  dtype_ = from.dtype_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.VarLenFeatureProto)
}

void VarLenFeatureProto::SharedCtor() {
  values_output_tensor_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  indices_output_tensor_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  shapes_output_tensor_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dtype_ = 0;
}

VarLenFeatureProto::~VarLenFeatureProto() {
  // @@protoc_insertion_point(destructor:tensorflow.VarLenFeatureProto)
  SharedDtor();
}

void VarLenFeatureProto::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  values_output_tensor_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  indices_output_tensor_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  shapes_output_tensor_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void VarLenFeatureProto::ArenaDtor(void* object) {
  VarLenFeatureProto* _this = reinterpret_cast< VarLenFeatureProto* >(object);
  (void)_this;
}
void VarLenFeatureProto::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void VarLenFeatureProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* VarLenFeatureProto::descriptor() {
  ::protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const VarLenFeatureProto& VarLenFeatureProto::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::scc_info_VarLenFeatureProto.base);
  return *internal_default_instance();
}


void VarLenFeatureProto::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.VarLenFeatureProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  values_output_tensor_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  indices_output_tensor_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  shapes_output_tensor_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  dtype_ = 0;
  _internal_metadata_.Clear();
}

bool VarLenFeatureProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.VarLenFeatureProto)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.DataType dtype = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_dtype(static_cast< ::tensorflow::DataType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string values_output_tensor_name = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_values_output_tensor_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->values_output_tensor_name().data(), static_cast<int>(this->values_output_tensor_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.VarLenFeatureProto.values_output_tensor_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string indices_output_tensor_name = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_indices_output_tensor_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->indices_output_tensor_name().data(), static_cast<int>(this->indices_output_tensor_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.VarLenFeatureProto.indices_output_tensor_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string shapes_output_tensor_name = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_shapes_output_tensor_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->shapes_output_tensor_name().data(), static_cast<int>(this->shapes_output_tensor_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.VarLenFeatureProto.shapes_output_tensor_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.VarLenFeatureProto)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.VarLenFeatureProto)
  return false;
#undef DO_
}

void VarLenFeatureProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.VarLenFeatureProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.DataType dtype = 1;
  if (this->dtype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->dtype(), output);
  }

  // string values_output_tensor_name = 2;
  if (this->values_output_tensor_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->values_output_tensor_name().data(), static_cast<int>(this->values_output_tensor_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.VarLenFeatureProto.values_output_tensor_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->values_output_tensor_name(), output);
  }

  // string indices_output_tensor_name = 3;
  if (this->indices_output_tensor_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->indices_output_tensor_name().data(), static_cast<int>(this->indices_output_tensor_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.VarLenFeatureProto.indices_output_tensor_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->indices_output_tensor_name(), output);
  }

  // string shapes_output_tensor_name = 4;
  if (this->shapes_output_tensor_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->shapes_output_tensor_name().data(), static_cast<int>(this->shapes_output_tensor_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.VarLenFeatureProto.shapes_output_tensor_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->shapes_output_tensor_name(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.VarLenFeatureProto)
}

::google::protobuf::uint8* VarLenFeatureProto::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.VarLenFeatureProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.DataType dtype = 1;
  if (this->dtype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->dtype(), target);
  }

  // string values_output_tensor_name = 2;
  if (this->values_output_tensor_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->values_output_tensor_name().data(), static_cast<int>(this->values_output_tensor_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.VarLenFeatureProto.values_output_tensor_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->values_output_tensor_name(), target);
  }

  // string indices_output_tensor_name = 3;
  if (this->indices_output_tensor_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->indices_output_tensor_name().data(), static_cast<int>(this->indices_output_tensor_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.VarLenFeatureProto.indices_output_tensor_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->indices_output_tensor_name(), target);
  }

  // string shapes_output_tensor_name = 4;
  if (this->shapes_output_tensor_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->shapes_output_tensor_name().data(), static_cast<int>(this->shapes_output_tensor_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.VarLenFeatureProto.shapes_output_tensor_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->shapes_output_tensor_name(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.VarLenFeatureProto)
  return target;
}

size_t VarLenFeatureProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.VarLenFeatureProto)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string values_output_tensor_name = 2;
  if (this->values_output_tensor_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->values_output_tensor_name());
  }

  // string indices_output_tensor_name = 3;
  if (this->indices_output_tensor_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->indices_output_tensor_name());
  }

  // string shapes_output_tensor_name = 4;
  if (this->shapes_output_tensor_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->shapes_output_tensor_name());
  }

  // .tensorflow.DataType dtype = 1;
  if (this->dtype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->dtype());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void VarLenFeatureProto::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.VarLenFeatureProto)
  GOOGLE_DCHECK_NE(&from, this);
  const VarLenFeatureProto* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const VarLenFeatureProto>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.VarLenFeatureProto)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.VarLenFeatureProto)
    MergeFrom(*source);
  }
}

void VarLenFeatureProto::MergeFrom(const VarLenFeatureProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.VarLenFeatureProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.values_output_tensor_name().size() > 0) {
    set_values_output_tensor_name(from.values_output_tensor_name());
  }
  if (from.indices_output_tensor_name().size() > 0) {
    set_indices_output_tensor_name(from.indices_output_tensor_name());
  }
  if (from.shapes_output_tensor_name().size() > 0) {
    set_shapes_output_tensor_name(from.shapes_output_tensor_name());
  }
  if (from.dtype() != 0) {
    set_dtype(from.dtype());
  }
}

void VarLenFeatureProto::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.VarLenFeatureProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void VarLenFeatureProto::CopyFrom(const VarLenFeatureProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.VarLenFeatureProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VarLenFeatureProto::IsInitialized() const {
  return true;
}

void VarLenFeatureProto::Swap(VarLenFeatureProto* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    VarLenFeatureProto* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void VarLenFeatureProto::UnsafeArenaSwap(VarLenFeatureProto* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void VarLenFeatureProto::InternalSwap(VarLenFeatureProto* other) {
  using std::swap;
  values_output_tensor_name_.Swap(&other->values_output_tensor_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  indices_output_tensor_name_.Swap(&other->indices_output_tensor_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  shapes_output_tensor_name_.Swap(&other->shapes_output_tensor_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(dtype_, other->dtype_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata VarLenFeatureProto::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void FixedLenFeatureProto::InitAsDefaultInstance() {
  ::tensorflow::_FixedLenFeatureProto_default_instance_._instance.get_mutable()->shape_ = const_cast< ::tensorflow::TensorShapeProto*>(
      ::tensorflow::TensorShapeProto::internal_default_instance());
  ::tensorflow::_FixedLenFeatureProto_default_instance_._instance.get_mutable()->default_value_ = const_cast< ::tensorflow::TensorProto*>(
      ::tensorflow::TensorProto::internal_default_instance());
}
void FixedLenFeatureProto::unsafe_arena_set_allocated_shape(
    ::tensorflow::TensorShapeProto* shape) {
  if (GetArenaNoVirtual() == NULL) {
    delete shape_;
  }
  shape_ = shape;
  if (shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.FixedLenFeatureProto.shape)
}
void FixedLenFeatureProto::clear_shape() {
  if (GetArenaNoVirtual() == NULL && shape_ != NULL) {
    delete shape_;
  }
  shape_ = NULL;
}
void FixedLenFeatureProto::unsafe_arena_set_allocated_default_value(
    ::tensorflow::TensorProto* default_value) {
  if (GetArenaNoVirtual() == NULL) {
    delete default_value_;
  }
  default_value_ = default_value;
  if (default_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.FixedLenFeatureProto.default_value)
}
void FixedLenFeatureProto::clear_default_value() {
  if (GetArenaNoVirtual() == NULL && default_value_ != NULL) {
    delete default_value_;
  }
  default_value_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int FixedLenFeatureProto::kDtypeFieldNumber;
const int FixedLenFeatureProto::kShapeFieldNumber;
const int FixedLenFeatureProto::kDefaultValueFieldNumber;
const int FixedLenFeatureProto::kValuesOutputTensorNameFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

FixedLenFeatureProto::FixedLenFeatureProto()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::scc_info_FixedLenFeatureProto.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.FixedLenFeatureProto)
}
FixedLenFeatureProto::FixedLenFeatureProto(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::scc_info_FixedLenFeatureProto.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.FixedLenFeatureProto)
}
FixedLenFeatureProto::FixedLenFeatureProto(const FixedLenFeatureProto& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  values_output_tensor_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.values_output_tensor_name().size() > 0) {
    values_output_tensor_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.values_output_tensor_name(),
      GetArenaNoVirtual());
  }
  if (from.has_shape()) {
    shape_ = new ::tensorflow::TensorShapeProto(*from.shape_);
  } else {
    shape_ = NULL;
  }
  if (from.has_default_value()) {
    default_value_ = new ::tensorflow::TensorProto(*from.default_value_);
  } else {
    default_value_ = NULL;
  }
  dtype_ = from.dtype_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.FixedLenFeatureProto)
}

void FixedLenFeatureProto::SharedCtor() {
  values_output_tensor_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&shape_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&dtype_) -
      reinterpret_cast<char*>(&shape_)) + sizeof(dtype_));
}

FixedLenFeatureProto::~FixedLenFeatureProto() {
  // @@protoc_insertion_point(destructor:tensorflow.FixedLenFeatureProto)
  SharedDtor();
}

void FixedLenFeatureProto::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  values_output_tensor_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete shape_;
  if (this != internal_default_instance()) delete default_value_;
}

void FixedLenFeatureProto::ArenaDtor(void* object) {
  FixedLenFeatureProto* _this = reinterpret_cast< FixedLenFeatureProto* >(object);
  (void)_this;
}
void FixedLenFeatureProto::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void FixedLenFeatureProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* FixedLenFeatureProto::descriptor() {
  ::protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const FixedLenFeatureProto& FixedLenFeatureProto::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::scc_info_FixedLenFeatureProto.base);
  return *internal_default_instance();
}


void FixedLenFeatureProto::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.FixedLenFeatureProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  values_output_tensor_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && shape_ != NULL) {
    delete shape_;
  }
  shape_ = NULL;
  if (GetArenaNoVirtual() == NULL && default_value_ != NULL) {
    delete default_value_;
  }
  default_value_ = NULL;
  dtype_ = 0;
  _internal_metadata_.Clear();
}

bool FixedLenFeatureProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.FixedLenFeatureProto)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.DataType dtype = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_dtype(static_cast< ::tensorflow::DataType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorShapeProto shape = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_shape()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorProto default_value = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_default_value()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string values_output_tensor_name = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_values_output_tensor_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->values_output_tensor_name().data(), static_cast<int>(this->values_output_tensor_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.FixedLenFeatureProto.values_output_tensor_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.FixedLenFeatureProto)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.FixedLenFeatureProto)
  return false;
#undef DO_
}

void FixedLenFeatureProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.FixedLenFeatureProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.DataType dtype = 1;
  if (this->dtype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->dtype(), output);
  }

  // .tensorflow.TensorShapeProto shape = 2;
  if (this->has_shape()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_shape(), output);
  }

  // .tensorflow.TensorProto default_value = 3;
  if (this->has_default_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->_internal_default_value(), output);
  }

  // string values_output_tensor_name = 4;
  if (this->values_output_tensor_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->values_output_tensor_name().data(), static_cast<int>(this->values_output_tensor_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.FixedLenFeatureProto.values_output_tensor_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->values_output_tensor_name(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.FixedLenFeatureProto)
}

::google::protobuf::uint8* FixedLenFeatureProto::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.FixedLenFeatureProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.DataType dtype = 1;
  if (this->dtype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->dtype(), target);
  }

  // .tensorflow.TensorShapeProto shape = 2;
  if (this->has_shape()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_shape(), deterministic, target);
  }

  // .tensorflow.TensorProto default_value = 3;
  if (this->has_default_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->_internal_default_value(), deterministic, target);
  }

  // string values_output_tensor_name = 4;
  if (this->values_output_tensor_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->values_output_tensor_name().data(), static_cast<int>(this->values_output_tensor_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.FixedLenFeatureProto.values_output_tensor_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->values_output_tensor_name(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.FixedLenFeatureProto)
  return target;
}

size_t FixedLenFeatureProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.FixedLenFeatureProto)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string values_output_tensor_name = 4;
  if (this->values_output_tensor_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->values_output_tensor_name());
  }

  // .tensorflow.TensorShapeProto shape = 2;
  if (this->has_shape()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *shape_);
  }

  // .tensorflow.TensorProto default_value = 3;
  if (this->has_default_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *default_value_);
  }

  // .tensorflow.DataType dtype = 1;
  if (this->dtype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->dtype());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void FixedLenFeatureProto::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.FixedLenFeatureProto)
  GOOGLE_DCHECK_NE(&from, this);
  const FixedLenFeatureProto* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const FixedLenFeatureProto>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.FixedLenFeatureProto)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.FixedLenFeatureProto)
    MergeFrom(*source);
  }
}

void FixedLenFeatureProto::MergeFrom(const FixedLenFeatureProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.FixedLenFeatureProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.values_output_tensor_name().size() > 0) {
    set_values_output_tensor_name(from.values_output_tensor_name());
  }
  if (from.has_shape()) {
    mutable_shape()->::tensorflow::TensorShapeProto::MergeFrom(from.shape());
  }
  if (from.has_default_value()) {
    mutable_default_value()->::tensorflow::TensorProto::MergeFrom(from.default_value());
  }
  if (from.dtype() != 0) {
    set_dtype(from.dtype());
  }
}

void FixedLenFeatureProto::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.FixedLenFeatureProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FixedLenFeatureProto::CopyFrom(const FixedLenFeatureProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.FixedLenFeatureProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FixedLenFeatureProto::IsInitialized() const {
  return true;
}

void FixedLenFeatureProto::Swap(FixedLenFeatureProto* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    FixedLenFeatureProto* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void FixedLenFeatureProto::UnsafeArenaSwap(FixedLenFeatureProto* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void FixedLenFeatureProto::InternalSwap(FixedLenFeatureProto* other) {
  using std::swap;
  values_output_tensor_name_.Swap(&other->values_output_tensor_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(shape_, other->shape_);
  swap(default_value_, other->default_value_);
  swap(dtype_, other->dtype_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata FixedLenFeatureProto::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void FeatureConfiguration::InitAsDefaultInstance() {
  ::tensorflow::_FeatureConfiguration_default_instance_.fixed_len_feature_ = const_cast< ::tensorflow::FixedLenFeatureProto*>(
      ::tensorflow::FixedLenFeatureProto::internal_default_instance());
  ::tensorflow::_FeatureConfiguration_default_instance_.var_len_feature_ = const_cast< ::tensorflow::VarLenFeatureProto*>(
      ::tensorflow::VarLenFeatureProto::internal_default_instance());
}
void FeatureConfiguration::set_allocated_fixed_len_feature(::tensorflow::FixedLenFeatureProto* fixed_len_feature) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_config();
  if (fixed_len_feature) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(fixed_len_feature);
    if (message_arena != submessage_arena) {
      fixed_len_feature = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, fixed_len_feature, submessage_arena);
    }
    set_has_fixed_len_feature();
    config_.fixed_len_feature_ = fixed_len_feature;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.FeatureConfiguration.fixed_len_feature)
}
void FeatureConfiguration::set_allocated_var_len_feature(::tensorflow::VarLenFeatureProto* var_len_feature) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_config();
  if (var_len_feature) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(var_len_feature);
    if (message_arena != submessage_arena) {
      var_len_feature = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, var_len_feature, submessage_arena);
    }
    set_has_var_len_feature();
    config_.var_len_feature_ = var_len_feature;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.FeatureConfiguration.var_len_feature)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int FeatureConfiguration::kFixedLenFeatureFieldNumber;
const int FeatureConfiguration::kVarLenFeatureFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

FeatureConfiguration::FeatureConfiguration()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::scc_info_FeatureConfiguration.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.FeatureConfiguration)
}
FeatureConfiguration::FeatureConfiguration(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::scc_info_FeatureConfiguration.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.FeatureConfiguration)
}
FeatureConfiguration::FeatureConfiguration(const FeatureConfiguration& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  clear_has_config();
  switch (from.config_case()) {
    case kFixedLenFeature: {
      mutable_fixed_len_feature()->::tensorflow::FixedLenFeatureProto::MergeFrom(from.fixed_len_feature());
      break;
    }
    case kVarLenFeature: {
      mutable_var_len_feature()->::tensorflow::VarLenFeatureProto::MergeFrom(from.var_len_feature());
      break;
    }
    case CONFIG_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.FeatureConfiguration)
}

void FeatureConfiguration::SharedCtor() {
  clear_has_config();
}

FeatureConfiguration::~FeatureConfiguration() {
  // @@protoc_insertion_point(destructor:tensorflow.FeatureConfiguration)
  SharedDtor();
}

void FeatureConfiguration::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (has_config()) {
    clear_config();
  }
}

void FeatureConfiguration::ArenaDtor(void* object) {
  FeatureConfiguration* _this = reinterpret_cast< FeatureConfiguration* >(object);
  (void)_this;
}
void FeatureConfiguration::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void FeatureConfiguration::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* FeatureConfiguration::descriptor() {
  ::protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const FeatureConfiguration& FeatureConfiguration::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::scc_info_FeatureConfiguration.base);
  return *internal_default_instance();
}


void FeatureConfiguration::clear_config() {
// @@protoc_insertion_point(one_of_clear_start:tensorflow.FeatureConfiguration)
  switch (config_case()) {
    case kFixedLenFeature: {
      if (GetArenaNoVirtual() == NULL) {
        delete config_.fixed_len_feature_;
      }
      break;
    }
    case kVarLenFeature: {
      if (GetArenaNoVirtual() == NULL) {
        delete config_.var_len_feature_;
      }
      break;
    }
    case CONFIG_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = CONFIG_NOT_SET;
}


void FeatureConfiguration::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.FeatureConfiguration)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_config();
  _internal_metadata_.Clear();
}

bool FeatureConfiguration::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.FeatureConfiguration)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.FixedLenFeatureProto fixed_len_feature = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_fixed_len_feature()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.VarLenFeatureProto var_len_feature = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_var_len_feature()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.FeatureConfiguration)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.FeatureConfiguration)
  return false;
#undef DO_
}

void FeatureConfiguration::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.FeatureConfiguration)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.FixedLenFeatureProto fixed_len_feature = 1;
  if (has_fixed_len_feature()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->_internal_fixed_len_feature(), output);
  }

  // .tensorflow.VarLenFeatureProto var_len_feature = 2;
  if (has_var_len_feature()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_var_len_feature(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.FeatureConfiguration)
}

::google::protobuf::uint8* FeatureConfiguration::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.FeatureConfiguration)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.FixedLenFeatureProto fixed_len_feature = 1;
  if (has_fixed_len_feature()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->_internal_fixed_len_feature(), deterministic, target);
  }

  // .tensorflow.VarLenFeatureProto var_len_feature = 2;
  if (has_var_len_feature()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_var_len_feature(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.FeatureConfiguration)
  return target;
}

size_t FeatureConfiguration::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.FeatureConfiguration)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  switch (config_case()) {
    // .tensorflow.FixedLenFeatureProto fixed_len_feature = 1;
    case kFixedLenFeature: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *config_.fixed_len_feature_);
      break;
    }
    // .tensorflow.VarLenFeatureProto var_len_feature = 2;
    case kVarLenFeature: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *config_.var_len_feature_);
      break;
    }
    case CONFIG_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void FeatureConfiguration::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.FeatureConfiguration)
  GOOGLE_DCHECK_NE(&from, this);
  const FeatureConfiguration* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const FeatureConfiguration>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.FeatureConfiguration)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.FeatureConfiguration)
    MergeFrom(*source);
  }
}

void FeatureConfiguration::MergeFrom(const FeatureConfiguration& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.FeatureConfiguration)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.config_case()) {
    case kFixedLenFeature: {
      mutable_fixed_len_feature()->::tensorflow::FixedLenFeatureProto::MergeFrom(from.fixed_len_feature());
      break;
    }
    case kVarLenFeature: {
      mutable_var_len_feature()->::tensorflow::VarLenFeatureProto::MergeFrom(from.var_len_feature());
      break;
    }
    case CONFIG_NOT_SET: {
      break;
    }
  }
}

void FeatureConfiguration::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.FeatureConfiguration)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FeatureConfiguration::CopyFrom(const FeatureConfiguration& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.FeatureConfiguration)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FeatureConfiguration::IsInitialized() const {
  return true;
}

void FeatureConfiguration::Swap(FeatureConfiguration* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    FeatureConfiguration* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void FeatureConfiguration::UnsafeArenaSwap(FeatureConfiguration* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void FeatureConfiguration::InternalSwap(FeatureConfiguration* other) {
  using std::swap;
  swap(config_, other->config_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata FeatureConfiguration::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

ExampleParserConfiguration_FeatureMapEntry_DoNotUse::ExampleParserConfiguration_FeatureMapEntry_DoNotUse() {}
ExampleParserConfiguration_FeatureMapEntry_DoNotUse::ExampleParserConfiguration_FeatureMapEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void ExampleParserConfiguration_FeatureMapEntry_DoNotUse::MergeFrom(const ExampleParserConfiguration_FeatureMapEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata ExampleParserConfiguration_FeatureMapEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::file_level_metadata[3];
}
void ExampleParserConfiguration_FeatureMapEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void ExampleParserConfiguration::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ExampleParserConfiguration::kFeatureMapFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ExampleParserConfiguration::ExampleParserConfiguration()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::scc_info_ExampleParserConfiguration.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.ExampleParserConfiguration)
}
ExampleParserConfiguration::ExampleParserConfiguration(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  feature_map_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::scc_info_ExampleParserConfiguration.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.ExampleParserConfiguration)
}
ExampleParserConfiguration::ExampleParserConfiguration(const ExampleParserConfiguration& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  feature_map_.MergeFrom(from.feature_map_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.ExampleParserConfiguration)
}

void ExampleParserConfiguration::SharedCtor() {
}

ExampleParserConfiguration::~ExampleParserConfiguration() {
  // @@protoc_insertion_point(destructor:tensorflow.ExampleParserConfiguration)
  SharedDtor();
}

void ExampleParserConfiguration::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void ExampleParserConfiguration::ArenaDtor(void* object) {
  ExampleParserConfiguration* _this = reinterpret_cast< ExampleParserConfiguration* >(object);
  (void)_this;
}
void ExampleParserConfiguration::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void ExampleParserConfiguration::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ExampleParserConfiguration::descriptor() {
  ::protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ExampleParserConfiguration& ExampleParserConfiguration::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::scc_info_ExampleParserConfiguration.base);
  return *internal_default_instance();
}


void ExampleParserConfiguration::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.ExampleParserConfiguration)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  feature_map_.Clear();
  _internal_metadata_.Clear();
}

bool ExampleParserConfiguration::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.ExampleParserConfiguration)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<string, .tensorflow.FeatureConfiguration> feature_map = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          ExampleParserConfiguration_FeatureMapEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              ExampleParserConfiguration_FeatureMapEntry_DoNotUse,
              ::std::string, ::tensorflow::FeatureConfiguration,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::tensorflow::FeatureConfiguration > > parser(&feature_map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ExampleParserConfiguration.FeatureMapEntry.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.ExampleParserConfiguration)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.ExampleParserConfiguration)
  return false;
#undef DO_
}

void ExampleParserConfiguration::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.ExampleParserConfiguration)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, .tensorflow.FeatureConfiguration> feature_map = 1;
  if (!this->feature_map().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::FeatureConfiguration >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.ExampleParserConfiguration.FeatureMapEntry.key");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->feature_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->feature_map().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::FeatureConfiguration >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::FeatureConfiguration >::const_iterator
          it = this->feature_map().begin();
          it != this->feature_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ExampleParserConfiguration_FeatureMapEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(feature_map_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<ExampleParserConfiguration_FeatureMapEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::FeatureConfiguration >::const_iterator
          it = this->feature_map().begin();
          it != this->feature_map().end(); ++it) {
        entry.reset(feature_map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.ExampleParserConfiguration)
}

::google::protobuf::uint8* ExampleParserConfiguration::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.ExampleParserConfiguration)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, .tensorflow.FeatureConfiguration> feature_map = 1;
  if (!this->feature_map().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::FeatureConfiguration >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.ExampleParserConfiguration.FeatureMapEntry.key");
      }
    };

    if (deterministic &&
        this->feature_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->feature_map().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::FeatureConfiguration >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::FeatureConfiguration >::const_iterator
          it = this->feature_map().begin();
          it != this->feature_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ExampleParserConfiguration_FeatureMapEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(feature_map_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<ExampleParserConfiguration_FeatureMapEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::FeatureConfiguration >::const_iterator
          it = this->feature_map().begin();
          it != this->feature_map().end(); ++it) {
        entry.reset(feature_map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.ExampleParserConfiguration)
  return target;
}

size_t ExampleParserConfiguration::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.ExampleParserConfiguration)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // map<string, .tensorflow.FeatureConfiguration> feature_map = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->feature_map_size());
  {
    ::std::unique_ptr<ExampleParserConfiguration_FeatureMapEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::tensorflow::FeatureConfiguration >::const_iterator
        it = this->feature_map().begin();
        it != this->feature_map().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(feature_map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ExampleParserConfiguration::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.ExampleParserConfiguration)
  GOOGLE_DCHECK_NE(&from, this);
  const ExampleParserConfiguration* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ExampleParserConfiguration>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.ExampleParserConfiguration)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.ExampleParserConfiguration)
    MergeFrom(*source);
  }
}

void ExampleParserConfiguration::MergeFrom(const ExampleParserConfiguration& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.ExampleParserConfiguration)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  feature_map_.MergeFrom(from.feature_map_);
}

void ExampleParserConfiguration::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.ExampleParserConfiguration)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ExampleParserConfiguration::CopyFrom(const ExampleParserConfiguration& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.ExampleParserConfiguration)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ExampleParserConfiguration::IsInitialized() const {
  return true;
}

void ExampleParserConfiguration::Swap(ExampleParserConfiguration* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    ExampleParserConfiguration* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void ExampleParserConfiguration::UnsafeArenaSwap(ExampleParserConfiguration* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void ExampleParserConfiguration::InternalSwap(ExampleParserConfiguration* other) {
  using std::swap;
  feature_map_.Swap(&other->feature_map_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ExampleParserConfiguration::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fexample_2fexample_5fparser_5fconfiguration_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::VarLenFeatureProto* Arena::CreateMaybeMessage< ::tensorflow::VarLenFeatureProto >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::VarLenFeatureProto >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::FixedLenFeatureProto* Arena::CreateMaybeMessage< ::tensorflow::FixedLenFeatureProto >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::FixedLenFeatureProto >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::FeatureConfiguration* Arena::CreateMaybeMessage< ::tensorflow::FeatureConfiguration >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::FeatureConfiguration >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::ExampleParserConfiguration_FeatureMapEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::ExampleParserConfiguration_FeatureMapEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::ExampleParserConfiguration_FeatureMapEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::ExampleParserConfiguration* Arena::CreateMaybeMessage< ::tensorflow::ExampleParserConfiguration >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::ExampleParserConfiguration >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

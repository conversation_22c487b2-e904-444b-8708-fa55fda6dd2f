// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/conv_autotuning.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fconv_5fautotuning_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fconv_5fautotuning_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/node_def.pb.h"
#include "tensorflow/core/framework/tensor.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fconv_5fautotuning_2eproto 

namespace protobuf_tensorflow_2fcore_2fprotobuf_2fconv_5fautotuning_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[1];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fconv_5fautotuning_2eproto
namespace tensorflow {
class ConvNodeDef;
class ConvNodeDefDefaultTypeInternal;
extern ConvNodeDefDefaultTypeInternal _ConvNodeDef_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::ConvNodeDef* Arena::CreateMaybeMessage<::tensorflow::ConvNodeDef>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

// ===================================================================

class ConvNodeDef : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.ConvNodeDef) */ {
 public:
  ConvNodeDef();
  virtual ~ConvNodeDef();

  ConvNodeDef(const ConvNodeDef& from);

  inline ConvNodeDef& operator=(const ConvNodeDef& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ConvNodeDef(ConvNodeDef&& from) noexcept
    : ConvNodeDef() {
    *this = ::std::move(from);
  }

  inline ConvNodeDef& operator=(ConvNodeDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ConvNodeDef& default_instance();

  enum SideInputOneofCase {
    kSideInput = 6,
    SIDE_INPUT_ONEOF_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ConvNodeDef* internal_default_instance() {
    return reinterpret_cast<const ConvNodeDef*>(
               &_ConvNodeDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void Swap(ConvNodeDef* other);
  friend void swap(ConvNodeDef& a, ConvNodeDef& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ConvNodeDef* New() const final {
    return CreateMaybeMessage<ConvNodeDef>(NULL);
  }

  ConvNodeDef* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ConvNodeDef>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ConvNodeDef& from);
  void MergeFrom(const ConvNodeDef& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ConvNodeDef* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .tensorflow.NodeDef conv = 1;
  bool has_conv() const;
  void clear_conv();
  static const int kConvFieldNumber = 1;
  private:
  const ::tensorflow::NodeDef& _internal_conv() const;
  public:
  const ::tensorflow::NodeDef& conv() const;
  ::tensorflow::NodeDef* release_conv();
  ::tensorflow::NodeDef* mutable_conv();
  void set_allocated_conv(::tensorflow::NodeDef* conv);

  // .tensorflow.TensorProto input = 2;
  bool has_input() const;
  void clear_input();
  static const int kInputFieldNumber = 2;
  private:
  const ::tensorflow::TensorProto& _internal_input() const;
  public:
  const ::tensorflow::TensorProto& input() const;
  ::tensorflow::TensorProto* release_input();
  ::tensorflow::TensorProto* mutable_input();
  void set_allocated_input(::tensorflow::TensorProto* input);

  // .tensorflow.TensorProto filter = 3;
  bool has_filter() const;
  void clear_filter();
  static const int kFilterFieldNumber = 3;
  private:
  const ::tensorflow::TensorProto& _internal_filter() const;
  public:
  const ::tensorflow::TensorProto& filter() const;
  ::tensorflow::TensorProto* release_filter();
  ::tensorflow::TensorProto* mutable_filter();
  void set_allocated_filter(::tensorflow::TensorProto* filter);

  // .tensorflow.TensorProto output = 4;
  bool has_output() const;
  void clear_output();
  static const int kOutputFieldNumber = 4;
  private:
  const ::tensorflow::TensorProto& _internal_output() const;
  public:
  const ::tensorflow::TensorProto& output() const;
  ::tensorflow::TensorProto* release_output();
  ::tensorflow::TensorProto* mutable_output();
  void set_allocated_output(::tensorflow::TensorProto* output);

  // .tensorflow.TensorProto bias = 5;
  bool has_bias() const;
  void clear_bias();
  static const int kBiasFieldNumber = 5;
  private:
  const ::tensorflow::TensorProto& _internal_bias() const;
  public:
  const ::tensorflow::TensorProto& bias() const;
  ::tensorflow::TensorProto* release_bias();
  ::tensorflow::TensorProto* mutable_bias();
  void set_allocated_bias(::tensorflow::TensorProto* bias);

  // .tensorflow.TensorProto side_input = 6;
  bool has_side_input() const;
  void clear_side_input();
  static const int kSideInputFieldNumber = 6;
  private:
  const ::tensorflow::TensorProto& _internal_side_input() const;
  public:
  const ::tensorflow::TensorProto& side_input() const;
  ::tensorflow::TensorProto* release_side_input();
  ::tensorflow::TensorProto* mutable_side_input();
  void set_allocated_side_input(::tensorflow::TensorProto* side_input);

  void clear_side_input_oneof();
  SideInputOneofCase side_input_oneof_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.ConvNodeDef)
 private:
  void set_has_side_input();

  inline bool has_side_input_oneof() const;
  inline void clear_has_side_input_oneof();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::tensorflow::NodeDef* conv_;
  ::tensorflow::TensorProto* input_;
  ::tensorflow::TensorProto* filter_;
  ::tensorflow::TensorProto* output_;
  ::tensorflow::TensorProto* bias_;
  union SideInputOneofUnion {
    SideInputOneofUnion() {}
    ::tensorflow::TensorProto* side_input_;
  } side_input_oneof_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fconv_5fautotuning_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ConvNodeDef

// .tensorflow.NodeDef conv = 1;
inline bool ConvNodeDef::has_conv() const {
  return this != internal_default_instance() && conv_ != NULL;
}
inline const ::tensorflow::NodeDef& ConvNodeDef::_internal_conv() const {
  return *conv_;
}
inline const ::tensorflow::NodeDef& ConvNodeDef::conv() const {
  const ::tensorflow::NodeDef* p = conv_;
  // @@protoc_insertion_point(field_get:tensorflow.ConvNodeDef.conv)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::NodeDef*>(
      &::tensorflow::_NodeDef_default_instance_);
}
inline ::tensorflow::NodeDef* ConvNodeDef::release_conv() {
  // @@protoc_insertion_point(field_release:tensorflow.ConvNodeDef.conv)
  
  ::tensorflow::NodeDef* temp = conv_;
  conv_ = NULL;
  return temp;
}
inline ::tensorflow::NodeDef* ConvNodeDef::mutable_conv() {
  
  if (conv_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::NodeDef>(GetArenaNoVirtual());
    conv_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ConvNodeDef.conv)
  return conv_;
}
inline void ConvNodeDef::set_allocated_conv(::tensorflow::NodeDef* conv) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(conv_);
  }
  if (conv) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(conv)->GetArena();
    if (message_arena != submessage_arena) {
      conv = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, conv, submessage_arena);
    }
    
  } else {
    
  }
  conv_ = conv;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ConvNodeDef.conv)
}

// .tensorflow.TensorProto input = 2;
inline bool ConvNodeDef::has_input() const {
  return this != internal_default_instance() && input_ != NULL;
}
inline const ::tensorflow::TensorProto& ConvNodeDef::_internal_input() const {
  return *input_;
}
inline const ::tensorflow::TensorProto& ConvNodeDef::input() const {
  const ::tensorflow::TensorProto* p = input_;
  // @@protoc_insertion_point(field_get:tensorflow.ConvNodeDef.input)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::TensorProto*>(
      &::tensorflow::_TensorProto_default_instance_);
}
inline ::tensorflow::TensorProto* ConvNodeDef::release_input() {
  // @@protoc_insertion_point(field_release:tensorflow.ConvNodeDef.input)
  
  ::tensorflow::TensorProto* temp = input_;
  input_ = NULL;
  return temp;
}
inline ::tensorflow::TensorProto* ConvNodeDef::mutable_input() {
  
  if (input_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorProto>(GetArenaNoVirtual());
    input_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ConvNodeDef.input)
  return input_;
}
inline void ConvNodeDef::set_allocated_input(::tensorflow::TensorProto* input) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(input_);
  }
  if (input) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(input)->GetArena();
    if (message_arena != submessage_arena) {
      input = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, input, submessage_arena);
    }
    
  } else {
    
  }
  input_ = input;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ConvNodeDef.input)
}

// .tensorflow.TensorProto filter = 3;
inline bool ConvNodeDef::has_filter() const {
  return this != internal_default_instance() && filter_ != NULL;
}
inline const ::tensorflow::TensorProto& ConvNodeDef::_internal_filter() const {
  return *filter_;
}
inline const ::tensorflow::TensorProto& ConvNodeDef::filter() const {
  const ::tensorflow::TensorProto* p = filter_;
  // @@protoc_insertion_point(field_get:tensorflow.ConvNodeDef.filter)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::TensorProto*>(
      &::tensorflow::_TensorProto_default_instance_);
}
inline ::tensorflow::TensorProto* ConvNodeDef::release_filter() {
  // @@protoc_insertion_point(field_release:tensorflow.ConvNodeDef.filter)
  
  ::tensorflow::TensorProto* temp = filter_;
  filter_ = NULL;
  return temp;
}
inline ::tensorflow::TensorProto* ConvNodeDef::mutable_filter() {
  
  if (filter_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorProto>(GetArenaNoVirtual());
    filter_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ConvNodeDef.filter)
  return filter_;
}
inline void ConvNodeDef::set_allocated_filter(::tensorflow::TensorProto* filter) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(filter_);
  }
  if (filter) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(filter)->GetArena();
    if (message_arena != submessage_arena) {
      filter = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, filter, submessage_arena);
    }
    
  } else {
    
  }
  filter_ = filter;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ConvNodeDef.filter)
}

// .tensorflow.TensorProto output = 4;
inline bool ConvNodeDef::has_output() const {
  return this != internal_default_instance() && output_ != NULL;
}
inline const ::tensorflow::TensorProto& ConvNodeDef::_internal_output() const {
  return *output_;
}
inline const ::tensorflow::TensorProto& ConvNodeDef::output() const {
  const ::tensorflow::TensorProto* p = output_;
  // @@protoc_insertion_point(field_get:tensorflow.ConvNodeDef.output)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::TensorProto*>(
      &::tensorflow::_TensorProto_default_instance_);
}
inline ::tensorflow::TensorProto* ConvNodeDef::release_output() {
  // @@protoc_insertion_point(field_release:tensorflow.ConvNodeDef.output)
  
  ::tensorflow::TensorProto* temp = output_;
  output_ = NULL;
  return temp;
}
inline ::tensorflow::TensorProto* ConvNodeDef::mutable_output() {
  
  if (output_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorProto>(GetArenaNoVirtual());
    output_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ConvNodeDef.output)
  return output_;
}
inline void ConvNodeDef::set_allocated_output(::tensorflow::TensorProto* output) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(output_);
  }
  if (output) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(output)->GetArena();
    if (message_arena != submessage_arena) {
      output = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, output, submessage_arena);
    }
    
  } else {
    
  }
  output_ = output;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ConvNodeDef.output)
}

// .tensorflow.TensorProto bias = 5;
inline bool ConvNodeDef::has_bias() const {
  return this != internal_default_instance() && bias_ != NULL;
}
inline const ::tensorflow::TensorProto& ConvNodeDef::_internal_bias() const {
  return *bias_;
}
inline const ::tensorflow::TensorProto& ConvNodeDef::bias() const {
  const ::tensorflow::TensorProto* p = bias_;
  // @@protoc_insertion_point(field_get:tensorflow.ConvNodeDef.bias)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::TensorProto*>(
      &::tensorflow::_TensorProto_default_instance_);
}
inline ::tensorflow::TensorProto* ConvNodeDef::release_bias() {
  // @@protoc_insertion_point(field_release:tensorflow.ConvNodeDef.bias)
  
  ::tensorflow::TensorProto* temp = bias_;
  bias_ = NULL;
  return temp;
}
inline ::tensorflow::TensorProto* ConvNodeDef::mutable_bias() {
  
  if (bias_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorProto>(GetArenaNoVirtual());
    bias_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ConvNodeDef.bias)
  return bias_;
}
inline void ConvNodeDef::set_allocated_bias(::tensorflow::TensorProto* bias) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(bias_);
  }
  if (bias) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(bias)->GetArena();
    if (message_arena != submessage_arena) {
      bias = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, bias, submessage_arena);
    }
    
  } else {
    
  }
  bias_ = bias;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ConvNodeDef.bias)
}

// .tensorflow.TensorProto side_input = 6;
inline bool ConvNodeDef::has_side_input() const {
  return side_input_oneof_case() == kSideInput;
}
inline void ConvNodeDef::set_has_side_input() {
  _oneof_case_[0] = kSideInput;
}
inline const ::tensorflow::TensorProto& ConvNodeDef::_internal_side_input() const {
  return *side_input_oneof_.side_input_;
}
inline ::tensorflow::TensorProto* ConvNodeDef::release_side_input() {
  // @@protoc_insertion_point(field_release:tensorflow.ConvNodeDef.side_input)
  if (has_side_input()) {
    clear_has_side_input_oneof();
      ::tensorflow::TensorProto* temp = side_input_oneof_.side_input_;
    side_input_oneof_.side_input_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::TensorProto& ConvNodeDef::side_input() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConvNodeDef.side_input)
  return has_side_input()
      ? *side_input_oneof_.side_input_
      : *reinterpret_cast< ::tensorflow::TensorProto*>(&::tensorflow::_TensorProto_default_instance_);
}
inline ::tensorflow::TensorProto* ConvNodeDef::mutable_side_input() {
  if (!has_side_input()) {
    clear_side_input_oneof();
    set_has_side_input();
    side_input_oneof_.side_input_ = CreateMaybeMessage< ::tensorflow::TensorProto >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ConvNodeDef.side_input)
  return side_input_oneof_.side_input_;
}

inline bool ConvNodeDef::has_side_input_oneof() const {
  return side_input_oneof_case() != SIDE_INPUT_ONEOF_NOT_SET;
}
inline void ConvNodeDef::clear_has_side_input_oneof() {
  _oneof_case_[0] = SIDE_INPUT_ONEOF_NOT_SET;
}
inline ConvNodeDef::SideInputOneofCase ConvNodeDef::side_input_oneof_case() const {
  return ConvNodeDef::SideInputOneofCase(_oneof_case_[0]);
}
#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fconv_5fautotuning_2eproto

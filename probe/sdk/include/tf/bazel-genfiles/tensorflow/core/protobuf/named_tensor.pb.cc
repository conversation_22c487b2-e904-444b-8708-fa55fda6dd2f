// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/named_tensor.proto

#include "tensorflow/core/protobuf/named_tensor.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_TensorProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto
namespace tensorflow {
class NamedTensorProtoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<NamedTensorProto>
      _instance;
} _NamedTensorProto_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fprotobuf_2fnamed_5ftensor_2eproto {
static void InitDefaultsNamedTensorProto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_NamedTensorProto_default_instance_;
    new (ptr) ::tensorflow::NamedTensorProto();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::NamedTensorProto::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_NamedTensorProto =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsNamedTensorProto}, {
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::scc_info_TensorProto.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_NamedTensorProto.base);
}

::google::protobuf::Metadata file_level_metadata[1];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NamedTensorProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NamedTensorProto, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NamedTensorProto, tensor_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::NamedTensorProto)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_NamedTensorProto_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/protobuf/named_tensor.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 1);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n+tensorflow/core/protobuf/named_tensor."
      "proto\022\ntensorflow\032&tensorflow/core/frame"
      "work/tensor.proto\"I\n\020NamedTensorProto\022\014\n"
      "\004name\030\001 \001(\t\022\'\n\006tensor\030\002 \001(\0132\027.tensorflow"
      ".TensorProtoBp\n\030org.tensorflow.framework"
      "B\021NamedTensorProtosP\001Z<github.com/tensor"
      "flow/tensorflow/tensorflow/go/core/proto"
      "buf\370\001\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 294);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/protobuf/named_tensor.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fnamed_5ftensor_2eproto
namespace tensorflow {

// ===================================================================

void NamedTensorProto::InitAsDefaultInstance() {
  ::tensorflow::_NamedTensorProto_default_instance_._instance.get_mutable()->tensor_ = const_cast< ::tensorflow::TensorProto*>(
      ::tensorflow::TensorProto::internal_default_instance());
}
void NamedTensorProto::unsafe_arena_set_allocated_tensor(
    ::tensorflow::TensorProto* tensor) {
  if (GetArenaNoVirtual() == NULL) {
    delete tensor_;
  }
  tensor_ = tensor;
  if (tensor) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.NamedTensorProto.tensor)
}
void NamedTensorProto::clear_tensor() {
  if (GetArenaNoVirtual() == NULL && tensor_ != NULL) {
    delete tensor_;
  }
  tensor_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int NamedTensorProto::kNameFieldNumber;
const int NamedTensorProto::kTensorFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

NamedTensorProto::NamedTensorProto()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fnamed_5ftensor_2eproto::scc_info_NamedTensorProto.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.NamedTensorProto)
}
NamedTensorProto::NamedTensorProto(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fnamed_5ftensor_2eproto::scc_info_NamedTensorProto.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.NamedTensorProto)
}
NamedTensorProto::NamedTensorProto(const NamedTensorProto& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name(),
      GetArenaNoVirtual());
  }
  if (from.has_tensor()) {
    tensor_ = new ::tensorflow::TensorProto(*from.tensor_);
  } else {
    tensor_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.NamedTensorProto)
}

void NamedTensorProto::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tensor_ = NULL;
}

NamedTensorProto::~NamedTensorProto() {
  // @@protoc_insertion_point(destructor:tensorflow.NamedTensorProto)
  SharedDtor();
}

void NamedTensorProto::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete tensor_;
}

void NamedTensorProto::ArenaDtor(void* object) {
  NamedTensorProto* _this = reinterpret_cast< NamedTensorProto* >(object);
  (void)_this;
}
void NamedTensorProto::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void NamedTensorProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* NamedTensorProto::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fnamed_5ftensor_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fnamed_5ftensor_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const NamedTensorProto& NamedTensorProto::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fnamed_5ftensor_2eproto::scc_info_NamedTensorProto.base);
  return *internal_default_instance();
}


void NamedTensorProto::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.NamedTensorProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && tensor_ != NULL) {
    delete tensor_;
  }
  tensor_ = NULL;
  _internal_metadata_.Clear();
}

bool NamedTensorProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.NamedTensorProto)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.NamedTensorProto.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorProto tensor = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_tensor()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.NamedTensorProto)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.NamedTensorProto)
  return false;
#undef DO_
}

void NamedTensorProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.NamedTensorProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.NamedTensorProto.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // .tensorflow.TensorProto tensor = 2;
  if (this->has_tensor()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_tensor(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.NamedTensorProto)
}

::google::protobuf::uint8* NamedTensorProto::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.NamedTensorProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.NamedTensorProto.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // .tensorflow.TensorProto tensor = 2;
  if (this->has_tensor()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_tensor(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.NamedTensorProto)
  return target;
}

size_t NamedTensorProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.NamedTensorProto)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // .tensorflow.TensorProto tensor = 2;
  if (this->has_tensor()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *tensor_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void NamedTensorProto::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.NamedTensorProto)
  GOOGLE_DCHECK_NE(&from, this);
  const NamedTensorProto* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const NamedTensorProto>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.NamedTensorProto)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.NamedTensorProto)
    MergeFrom(*source);
  }
}

void NamedTensorProto::MergeFrom(const NamedTensorProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.NamedTensorProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.name().size() > 0) {
    set_name(from.name());
  }
  if (from.has_tensor()) {
    mutable_tensor()->::tensorflow::TensorProto::MergeFrom(from.tensor());
  }
}

void NamedTensorProto::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.NamedTensorProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void NamedTensorProto::CopyFrom(const NamedTensorProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.NamedTensorProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool NamedTensorProto::IsInitialized() const {
  return true;
}

void NamedTensorProto::Swap(NamedTensorProto* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    NamedTensorProto* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void NamedTensorProto::UnsafeArenaSwap(NamedTensorProto* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void NamedTensorProto::InternalSwap(NamedTensorProto* other) {
  using std::swap;
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(tensor_, other->tensor_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata NamedTensorProto::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fnamed_5ftensor_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fnamed_5ftensor_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::NamedTensorProto* Arena::CreateMaybeMessage< ::tensorflow::NamedTensorProto >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::NamedTensorProto >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

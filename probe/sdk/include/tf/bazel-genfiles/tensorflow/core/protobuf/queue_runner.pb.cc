// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/queue_runner.proto

#include "tensorflow/core/protobuf/queue_runner.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace tensorflow {
class QueueRunnerDefDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<QueueRunnerDef>
      _instance;
} _QueueRunnerDef_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fprotobuf_2fqueue_5frunner_2eproto {
static void InitDefaultsQueueRunnerDef() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_QueueRunnerDef_default_instance_;
    new (ptr) ::tensorflow::QueueRunnerDef();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::QueueRunnerDef::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_QueueRunnerDef =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsQueueRunnerDef}, {}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_QueueRunnerDef.base);
}

::google::protobuf::Metadata file_level_metadata[1];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::QueueRunnerDef, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::QueueRunnerDef, queue_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::QueueRunnerDef, enqueue_op_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::QueueRunnerDef, close_op_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::QueueRunnerDef, cancel_op_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::QueueRunnerDef, queue_closed_exception_types_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::QueueRunnerDef)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_QueueRunnerDef_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/protobuf/queue_runner.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 1);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n+tensorflow/core/protobuf/queue_runner."
      "proto\022\ntensorflow\032*tensorflow/core/lib/c"
      "ore/error_codes.proto\"\252\001\n\016QueueRunnerDef"
      "\022\022\n\nqueue_name\030\001 \001(\t\022\027\n\017enqueue_op_name\030"
      "\002 \003(\t\022\025\n\rclose_op_name\030\003 \001(\t\022\026\n\016cancel_o"
      "p_name\030\004 \001(\t\022<\n\034queue_closed_exception_t"
      "ypes\030\005 \003(\0162\026.tensorflow.error.CodeBp\n\030or"
      "g.tensorflow.frameworkB\021QueueRunnerProto"
      "sP\001Z<github.com/tensorflow/tensorflow/te"
      "nsorflow/go/core/protobuf\370\001\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 396);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/protobuf/queue_runner.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2flib_2fcore_2ferror_5fcodes_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fqueue_5frunner_2eproto
namespace tensorflow {

// ===================================================================

void QueueRunnerDef::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int QueueRunnerDef::kQueueNameFieldNumber;
const int QueueRunnerDef::kEnqueueOpNameFieldNumber;
const int QueueRunnerDef::kCloseOpNameFieldNumber;
const int QueueRunnerDef::kCancelOpNameFieldNumber;
const int QueueRunnerDef::kQueueClosedExceptionTypesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

QueueRunnerDef::QueueRunnerDef()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fqueue_5frunner_2eproto::scc_info_QueueRunnerDef.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.QueueRunnerDef)
}
QueueRunnerDef::QueueRunnerDef(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  enqueue_op_name_(arena),
  queue_closed_exception_types_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fqueue_5frunner_2eproto::scc_info_QueueRunnerDef.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.QueueRunnerDef)
}
QueueRunnerDef::QueueRunnerDef(const QueueRunnerDef& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      enqueue_op_name_(from.enqueue_op_name_),
      queue_closed_exception_types_(from.queue_closed_exception_types_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  queue_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.queue_name().size() > 0) {
    queue_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.queue_name(),
      GetArenaNoVirtual());
  }
  close_op_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.close_op_name().size() > 0) {
    close_op_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.close_op_name(),
      GetArenaNoVirtual());
  }
  cancel_op_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.cancel_op_name().size() > 0) {
    cancel_op_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.cancel_op_name(),
      GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.QueueRunnerDef)
}

void QueueRunnerDef::SharedCtor() {
  queue_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  close_op_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  cancel_op_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

QueueRunnerDef::~QueueRunnerDef() {
  // @@protoc_insertion_point(destructor:tensorflow.QueueRunnerDef)
  SharedDtor();
}

void QueueRunnerDef::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  queue_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  close_op_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  cancel_op_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void QueueRunnerDef::ArenaDtor(void* object) {
  QueueRunnerDef* _this = reinterpret_cast< QueueRunnerDef* >(object);
  (void)_this;
}
void QueueRunnerDef::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void QueueRunnerDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* QueueRunnerDef::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fqueue_5frunner_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fqueue_5frunner_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const QueueRunnerDef& QueueRunnerDef::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fqueue_5frunner_2eproto::scc_info_QueueRunnerDef.base);
  return *internal_default_instance();
}


void QueueRunnerDef::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.QueueRunnerDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  enqueue_op_name_.Clear();
  queue_closed_exception_types_.Clear();
  queue_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  close_op_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  cancel_op_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  _internal_metadata_.Clear();
}

bool QueueRunnerDef::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.QueueRunnerDef)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string queue_name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_queue_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->queue_name().data(), static_cast<int>(this->queue_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.QueueRunnerDef.queue_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string enqueue_op_name = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_enqueue_op_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->enqueue_op_name(this->enqueue_op_name_size() - 1).data(),
            static_cast<int>(this->enqueue_op_name(this->enqueue_op_name_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.QueueRunnerDef.enqueue_op_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string close_op_name = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_close_op_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->close_op_name().data(), static_cast<int>(this->close_op_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.QueueRunnerDef.close_op_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string cancel_op_name = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_cancel_op_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->cancel_op_name().data(), static_cast<int>(this->cancel_op_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.QueueRunnerDef.cancel_op_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.error.Code queue_closed_exception_types = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          ::google::protobuf::uint32 length;
          DO_(input->ReadVarint32(&length));
          ::google::protobuf::io::CodedInputStream::Limit limit = input->PushLimit(static_cast<int>(length));
          while (input->BytesUntilLimit() > 0) {
            int value;
            DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
            add_queue_closed_exception_types(static_cast< ::tensorflow::error::Code >(value));
          }
          input->PopLimit(limit);
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          add_queue_closed_exception_types(static_cast< ::tensorflow::error::Code >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.QueueRunnerDef)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.QueueRunnerDef)
  return false;
#undef DO_
}

void QueueRunnerDef::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.QueueRunnerDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string queue_name = 1;
  if (this->queue_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->queue_name().data(), static_cast<int>(this->queue_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.QueueRunnerDef.queue_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->queue_name(), output);
  }

  // repeated string enqueue_op_name = 2;
  for (int i = 0, n = this->enqueue_op_name_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->enqueue_op_name(i).data(), static_cast<int>(this->enqueue_op_name(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.QueueRunnerDef.enqueue_op_name");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->enqueue_op_name(i), output);
  }

  // string close_op_name = 3;
  if (this->close_op_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->close_op_name().data(), static_cast<int>(this->close_op_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.QueueRunnerDef.close_op_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->close_op_name(), output);
  }

  // string cancel_op_name = 4;
  if (this->cancel_op_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->cancel_op_name().data(), static_cast<int>(this->cancel_op_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.QueueRunnerDef.cancel_op_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->cancel_op_name(), output);
  }

  // repeated .tensorflow.error.Code queue_closed_exception_types = 5;
  if (this->queue_closed_exception_types_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(
      5,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      output);
    output->WriteVarint32(
        static_cast< ::google::protobuf::uint32>(_queue_closed_exception_types_cached_byte_size_));
  }
  for (int i = 0, n = this->queue_closed_exception_types_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteEnumNoTag(
      this->queue_closed_exception_types(i), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.QueueRunnerDef)
}

::google::protobuf::uint8* QueueRunnerDef::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.QueueRunnerDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string queue_name = 1;
  if (this->queue_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->queue_name().data(), static_cast<int>(this->queue_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.QueueRunnerDef.queue_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->queue_name(), target);
  }

  // repeated string enqueue_op_name = 2;
  for (int i = 0, n = this->enqueue_op_name_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->enqueue_op_name(i).data(), static_cast<int>(this->enqueue_op_name(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.QueueRunnerDef.enqueue_op_name");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(2, this->enqueue_op_name(i), target);
  }

  // string close_op_name = 3;
  if (this->close_op_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->close_op_name().data(), static_cast<int>(this->close_op_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.QueueRunnerDef.close_op_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->close_op_name(), target);
  }

  // string cancel_op_name = 4;
  if (this->cancel_op_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->cancel_op_name().data(), static_cast<int>(this->cancel_op_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.QueueRunnerDef.cancel_op_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->cancel_op_name(), target);
  }

  // repeated .tensorflow.error.Code queue_closed_exception_types = 5;
  if (this->queue_closed_exception_types_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      5,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(      static_cast< ::google::protobuf::uint32>(
            _queue_closed_exception_types_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumNoTagToArray(
      this->queue_closed_exception_types_, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.QueueRunnerDef)
  return target;
}

size_t QueueRunnerDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.QueueRunnerDef)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated string enqueue_op_name = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->enqueue_op_name_size());
  for (int i = 0, n = this->enqueue_op_name_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->enqueue_op_name(i));
  }

  // repeated .tensorflow.error.Code queue_closed_exception_types = 5;
  {
    size_t data_size = 0;
    unsigned int count = static_cast<unsigned int>(this->queue_closed_exception_types_size());for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::EnumSize(
        this->queue_closed_exception_types(static_cast<int>(i)));
    }
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _queue_closed_exception_types_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // string queue_name = 1;
  if (this->queue_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->queue_name());
  }

  // string close_op_name = 3;
  if (this->close_op_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->close_op_name());
  }

  // string cancel_op_name = 4;
  if (this->cancel_op_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->cancel_op_name());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void QueueRunnerDef::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.QueueRunnerDef)
  GOOGLE_DCHECK_NE(&from, this);
  const QueueRunnerDef* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const QueueRunnerDef>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.QueueRunnerDef)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.QueueRunnerDef)
    MergeFrom(*source);
  }
}

void QueueRunnerDef::MergeFrom(const QueueRunnerDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.QueueRunnerDef)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  enqueue_op_name_.MergeFrom(from.enqueue_op_name_);
  queue_closed_exception_types_.MergeFrom(from.queue_closed_exception_types_);
  if (from.queue_name().size() > 0) {
    set_queue_name(from.queue_name());
  }
  if (from.close_op_name().size() > 0) {
    set_close_op_name(from.close_op_name());
  }
  if (from.cancel_op_name().size() > 0) {
    set_cancel_op_name(from.cancel_op_name());
  }
}

void QueueRunnerDef::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.QueueRunnerDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void QueueRunnerDef::CopyFrom(const QueueRunnerDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.QueueRunnerDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool QueueRunnerDef::IsInitialized() const {
  return true;
}

void QueueRunnerDef::Swap(QueueRunnerDef* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    QueueRunnerDef* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void QueueRunnerDef::UnsafeArenaSwap(QueueRunnerDef* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void QueueRunnerDef::InternalSwap(QueueRunnerDef* other) {
  using std::swap;
  enqueue_op_name_.InternalSwap(CastToBase(&other->enqueue_op_name_));
  queue_closed_exception_types_.InternalSwap(&other->queue_closed_exception_types_);
  queue_name_.Swap(&other->queue_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  close_op_name_.Swap(&other->close_op_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  cancel_op_name_.Swap(&other->cancel_op_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata QueueRunnerDef::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fqueue_5frunner_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fqueue_5frunner_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::QueueRunnerDef* Arena::CreateMaybeMessage< ::tensorflow::QueueRunnerDef >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::QueueRunnerDef >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

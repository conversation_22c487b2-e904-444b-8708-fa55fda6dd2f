// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/autotuning.proto

#include "tensorflow/core/protobuf/autotuning.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_google_2fprotobuf_2fany_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_google_2fprotobuf_2fany_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_Any;
}  // namespace protobuf_google_2fprotobuf_2fany_2eproto
namespace protobuf_google_2fprotobuf_2fduration_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_google_2fprotobuf_2fduration_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_Duration;
}  // namespace protobuf_google_2fprotobuf_2fduration_2eproto
namespace protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_AutotuneResult_ConvKey;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_ComputeCapability;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_CudnnVersion;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_AutotuneResult_SuccessResult;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_AutotuneResult;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto
namespace tensorflow {
class CudnnVersionDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CudnnVersion>
      _instance;
} _CudnnVersion_default_instance_;
class ComputeCapabilityDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ComputeCapability>
      _instance;
} _ComputeCapability_default_instance_;
class AutotuneResult_SuccessResultDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<AutotuneResult_SuccessResult>
      _instance;
} _AutotuneResult_SuccessResult_default_instance_;
class AutotuneResult_ConvKeyDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<AutotuneResult_ConvKey>
      _instance;
} _AutotuneResult_ConvKey_default_instance_;
class AutotuneResultDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<AutotuneResult>
      _instance;
  const ::tensorflow::AutotuneResult_SuccessResult* success_;
  ::google::protobuf::internal::ArenaStringPtr error_string_;
  const ::tensorflow::AutotuneResult_ConvKey* conv_;
  const ::tensorflow::AutotuneResult_ConvKey* reference_conv_;
} _AutotuneResult_default_instance_;
class AutotuningLogDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<AutotuningLog>
      _instance;
} _AutotuningLog_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto {
static void InitDefaultsCudnnVersion() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_CudnnVersion_default_instance_;
    new (ptr) ::tensorflow::CudnnVersion();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::CudnnVersion::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_CudnnVersion =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsCudnnVersion}, {}};

static void InitDefaultsComputeCapability() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_ComputeCapability_default_instance_;
    new (ptr) ::tensorflow::ComputeCapability();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::ComputeCapability::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_ComputeCapability =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsComputeCapability}, {}};

static void InitDefaultsAutotuneResult_SuccessResult() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_AutotuneResult_SuccessResult_default_instance_;
    new (ptr) ::tensorflow::AutotuneResult_SuccessResult();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::AutotuneResult_SuccessResult::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_AutotuneResult_SuccessResult =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsAutotuneResult_SuccessResult}, {
      &protobuf_google_2fprotobuf_2fduration_2eproto::scc_info_Duration.base,}};

static void InitDefaultsAutotuneResult_ConvKey() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_AutotuneResult_ConvKey_default_instance_;
    new (ptr) ::tensorflow::AutotuneResult_ConvKey();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::AutotuneResult_ConvKey::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_AutotuneResult_ConvKey =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsAutotuneResult_ConvKey}, {}};

static void InitDefaultsAutotuneResult() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_AutotuneResult_default_instance_;
    new (ptr) ::tensorflow::AutotuneResult();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::AutotuneResult::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_AutotuneResult =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsAutotuneResult}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::scc_info_AutotuneResult_SuccessResult.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::scc_info_AutotuneResult_ConvKey.base,}};

static void InitDefaultsAutotuningLog() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_AutotuningLog_default_instance_;
    new (ptr) ::tensorflow::AutotuningLog();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::AutotuningLog::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<4> scc_info_AutotuningLog =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 4, InitDefaultsAutotuningLog}, {
      &protobuf_google_2fprotobuf_2fany_2eproto::scc_info_Any.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::scc_info_AutotuneResult.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::scc_info_CudnnVersion.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::scc_info_ComputeCapability.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_CudnnVersion.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ComputeCapability.base);
  ::google::protobuf::internal::InitSCC(&scc_info_AutotuneResult_SuccessResult.base);
  ::google::protobuf::internal::InitSCC(&scc_info_AutotuneResult_ConvKey.base);
  ::google::protobuf::internal::InitSCC(&scc_info_AutotuneResult.base);
  ::google::protobuf::internal::InitSCC(&scc_info_AutotuningLog.base);
}

::google::protobuf::Metadata file_level_metadata[6];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CudnnVersion, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CudnnVersion, major_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CudnnVersion, minor_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CudnnVersion, patch_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ComputeCapability, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ComputeCapability, major_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ComputeCapability, minor_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AutotuneResult_SuccessResult, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AutotuneResult_SuccessResult, scratch_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AutotuneResult_SuccessResult, run_time_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AutotuneResult_ConvKey, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AutotuneResult_ConvKey, algorithm_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AutotuneResult_ConvKey, tensor_ops_enabled_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AutotuneResult, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AutotuneResult, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  offsetof(::tensorflow::AutotuneResultDefaultTypeInternal, success_),
  offsetof(::tensorflow::AutotuneResultDefaultTypeInternal, error_string_),
  offsetof(::tensorflow::AutotuneResultDefaultTypeInternal, conv_),
  offsetof(::tensorflow::AutotuneResultDefaultTypeInternal, reference_conv_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AutotuneResult, result_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AutotuneResult, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AutotuneResult, checker_failure_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AutotuningLog, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AutotuningLog, instr_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AutotuningLog, results_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AutotuningLog, cudnn_version_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AutotuningLog, compute_capability_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::CudnnVersion)},
  { 8, -1, sizeof(::tensorflow::ComputeCapability)},
  { 15, -1, sizeof(::tensorflow::AutotuneResult_SuccessResult)},
  { 22, -1, sizeof(::tensorflow::AutotuneResult_ConvKey)},
  { 29, -1, sizeof(::tensorflow::AutotuneResult)},
  { 41, -1, sizeof(::tensorflow::AutotuningLog)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_CudnnVersion_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_ComputeCapability_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_AutotuneResult_SuccessResult_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_AutotuneResult_ConvKey_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_AutotuneResult_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_AutotuningLog_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/protobuf/autotuning.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 6);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n)tensorflow/core/protobuf/autotuning.pr"
      "oto\022\ntensorflow\032\031google/protobuf/any.pro"
      "to\032\036google/protobuf/duration.proto\";\n\014Cu"
      "dnnVersion\022\r\n\005major\030\001 \001(\005\022\r\n\005minor\030\002 \001(\005"
      "\022\r\n\005patch\030\003 \001(\005\"1\n\021ComputeCapability\022\r\n\005"
      "major\030\001 \001(\005\022\r\n\005minor\030\002 \001(\005\"\212\003\n\016AutotuneR"
      "esult\022;\n\007success\030\003 \001(\0132(.tensorflow.Auto"
      "tuneResult.SuccessResultH\000\022\026\n\014error_stri"
      "ng\030\004 \001(\tH\000\0222\n\004conv\030\005 \001(\0132\".tensorflow.Au"
      "totuneResult.ConvKeyH\001\022<\n\016reference_conv"
      "\030\006 \001(\0132\".tensorflow.AutotuneResult.ConvK"
      "eyH\002\032S\n\rSuccessResult\022\025\n\rscratch_bytes\030\001"
      " \001(\003\022+\n\010run_time\030\002 \001(\0132\031.google.protobuf"
      ".Duration\0328\n\007ConvKey\022\021\n\talgorithm\030\001 \001(\003\022"
      "\032\n\022tensor_ops_enabled\030\002 \001(\010B\010\n\006resultB\005\n"
      "\003keyB\021\n\017checker_failure\"\315\001\n\rAutotuningLo"
      "g\022#\n\005instr\030\001 \001(\0132\024.google.protobuf.Any\022+"
      "\n\007results\030\002 \003(\0132\032.tensorflow.AutotuneRes"
      "ult\022/\n\rcudnn_version\030\003 \001(\0132\030.tensorflow."
      "CudnnVersion\0229\n\022compute_capability\030\004 \001(\013"
      "2\035.tensorflow.ComputeCapabilityb\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 839);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/protobuf/autotuning.proto", &protobuf_RegisterTypes);
  ::protobuf_google_2fprotobuf_2fany_2eproto::AddDescriptors();
  ::protobuf_google_2fprotobuf_2fduration_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto
namespace tensorflow {

// ===================================================================

void CudnnVersion::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CudnnVersion::kMajorFieldNumber;
const int CudnnVersion::kMinorFieldNumber;
const int CudnnVersion::kPatchFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CudnnVersion::CudnnVersion()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::scc_info_CudnnVersion.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.CudnnVersion)
}
CudnnVersion::CudnnVersion(const CudnnVersion& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&major_, &from.major_,
    static_cast<size_t>(reinterpret_cast<char*>(&patch_) -
    reinterpret_cast<char*>(&major_)) + sizeof(patch_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.CudnnVersion)
}

void CudnnVersion::SharedCtor() {
  ::memset(&major_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&patch_) -
      reinterpret_cast<char*>(&major_)) + sizeof(patch_));
}

CudnnVersion::~CudnnVersion() {
  // @@protoc_insertion_point(destructor:tensorflow.CudnnVersion)
  SharedDtor();
}

void CudnnVersion::SharedDtor() {
}

void CudnnVersion::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* CudnnVersion::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CudnnVersion& CudnnVersion::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::scc_info_CudnnVersion.base);
  return *internal_default_instance();
}


void CudnnVersion::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.CudnnVersion)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&major_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&patch_) -
      reinterpret_cast<char*>(&major_)) + sizeof(patch_));
  _internal_metadata_.Clear();
}

bool CudnnVersion::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.CudnnVersion)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 major = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &major_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 minor = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &minor_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 patch = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &patch_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.CudnnVersion)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.CudnnVersion)
  return false;
#undef DO_
}

void CudnnVersion::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.CudnnVersion)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 major = 1;
  if (this->major() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->major(), output);
  }

  // int32 minor = 2;
  if (this->minor() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->minor(), output);
  }

  // int32 patch = 3;
  if (this->patch() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->patch(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.CudnnVersion)
}

::google::protobuf::uint8* CudnnVersion::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.CudnnVersion)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 major = 1;
  if (this->major() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->major(), target);
  }

  // int32 minor = 2;
  if (this->minor() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->minor(), target);
  }

  // int32 patch = 3;
  if (this->patch() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->patch(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.CudnnVersion)
  return target;
}

size_t CudnnVersion::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.CudnnVersion)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int32 major = 1;
  if (this->major() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->major());
  }

  // int32 minor = 2;
  if (this->minor() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->minor());
  }

  // int32 patch = 3;
  if (this->patch() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->patch());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CudnnVersion::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.CudnnVersion)
  GOOGLE_DCHECK_NE(&from, this);
  const CudnnVersion* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CudnnVersion>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.CudnnVersion)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.CudnnVersion)
    MergeFrom(*source);
  }
}

void CudnnVersion::MergeFrom(const CudnnVersion& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.CudnnVersion)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.major() != 0) {
    set_major(from.major());
  }
  if (from.minor() != 0) {
    set_minor(from.minor());
  }
  if (from.patch() != 0) {
    set_patch(from.patch());
  }
}

void CudnnVersion::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.CudnnVersion)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CudnnVersion::CopyFrom(const CudnnVersion& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.CudnnVersion)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CudnnVersion::IsInitialized() const {
  return true;
}

void CudnnVersion::Swap(CudnnVersion* other) {
  if (other == this) return;
  InternalSwap(other);
}
void CudnnVersion::InternalSwap(CudnnVersion* other) {
  using std::swap;
  swap(major_, other->major_);
  swap(minor_, other->minor_);
  swap(patch_, other->patch_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata CudnnVersion::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ComputeCapability::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ComputeCapability::kMajorFieldNumber;
const int ComputeCapability::kMinorFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ComputeCapability::ComputeCapability()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::scc_info_ComputeCapability.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.ComputeCapability)
}
ComputeCapability::ComputeCapability(const ComputeCapability& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&major_, &from.major_,
    static_cast<size_t>(reinterpret_cast<char*>(&minor_) -
    reinterpret_cast<char*>(&major_)) + sizeof(minor_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.ComputeCapability)
}

void ComputeCapability::SharedCtor() {
  ::memset(&major_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&minor_) -
      reinterpret_cast<char*>(&major_)) + sizeof(minor_));
}

ComputeCapability::~ComputeCapability() {
  // @@protoc_insertion_point(destructor:tensorflow.ComputeCapability)
  SharedDtor();
}

void ComputeCapability::SharedDtor() {
}

void ComputeCapability::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ComputeCapability::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ComputeCapability& ComputeCapability::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::scc_info_ComputeCapability.base);
  return *internal_default_instance();
}


void ComputeCapability::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.ComputeCapability)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&major_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&minor_) -
      reinterpret_cast<char*>(&major_)) + sizeof(minor_));
  _internal_metadata_.Clear();
}

bool ComputeCapability::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.ComputeCapability)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 major = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &major_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 minor = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &minor_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.ComputeCapability)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.ComputeCapability)
  return false;
#undef DO_
}

void ComputeCapability::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.ComputeCapability)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 major = 1;
  if (this->major() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->major(), output);
  }

  // int32 minor = 2;
  if (this->minor() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->minor(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.ComputeCapability)
}

::google::protobuf::uint8* ComputeCapability::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.ComputeCapability)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 major = 1;
  if (this->major() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->major(), target);
  }

  // int32 minor = 2;
  if (this->minor() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->minor(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.ComputeCapability)
  return target;
}

size_t ComputeCapability::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.ComputeCapability)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int32 major = 1;
  if (this->major() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->major());
  }

  // int32 minor = 2;
  if (this->minor() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->minor());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ComputeCapability::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.ComputeCapability)
  GOOGLE_DCHECK_NE(&from, this);
  const ComputeCapability* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ComputeCapability>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.ComputeCapability)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.ComputeCapability)
    MergeFrom(*source);
  }
}

void ComputeCapability::MergeFrom(const ComputeCapability& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.ComputeCapability)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.major() != 0) {
    set_major(from.major());
  }
  if (from.minor() != 0) {
    set_minor(from.minor());
  }
}

void ComputeCapability::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.ComputeCapability)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ComputeCapability::CopyFrom(const ComputeCapability& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.ComputeCapability)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ComputeCapability::IsInitialized() const {
  return true;
}

void ComputeCapability::Swap(ComputeCapability* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ComputeCapability::InternalSwap(ComputeCapability* other) {
  using std::swap;
  swap(major_, other->major_);
  swap(minor_, other->minor_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ComputeCapability::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void AutotuneResult_SuccessResult::InitAsDefaultInstance() {
  ::tensorflow::_AutotuneResult_SuccessResult_default_instance_._instance.get_mutable()->run_time_ = const_cast< ::google::protobuf::Duration*>(
      ::google::protobuf::Duration::internal_default_instance());
}
void AutotuneResult_SuccessResult::clear_run_time() {
  if (GetArenaNoVirtual() == NULL && run_time_ != NULL) {
    delete run_time_;
  }
  run_time_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AutotuneResult_SuccessResult::kScratchBytesFieldNumber;
const int AutotuneResult_SuccessResult::kRunTimeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AutotuneResult_SuccessResult::AutotuneResult_SuccessResult()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::scc_info_AutotuneResult_SuccessResult.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.AutotuneResult.SuccessResult)
}
AutotuneResult_SuccessResult::AutotuneResult_SuccessResult(const AutotuneResult_SuccessResult& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_run_time()) {
    run_time_ = new ::google::protobuf::Duration(*from.run_time_);
  } else {
    run_time_ = NULL;
  }
  scratch_bytes_ = from.scratch_bytes_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.AutotuneResult.SuccessResult)
}

void AutotuneResult_SuccessResult::SharedCtor() {
  ::memset(&run_time_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&scratch_bytes_) -
      reinterpret_cast<char*>(&run_time_)) + sizeof(scratch_bytes_));
}

AutotuneResult_SuccessResult::~AutotuneResult_SuccessResult() {
  // @@protoc_insertion_point(destructor:tensorflow.AutotuneResult.SuccessResult)
  SharedDtor();
}

void AutotuneResult_SuccessResult::SharedDtor() {
  if (this != internal_default_instance()) delete run_time_;
}

void AutotuneResult_SuccessResult::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* AutotuneResult_SuccessResult::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const AutotuneResult_SuccessResult& AutotuneResult_SuccessResult::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::scc_info_AutotuneResult_SuccessResult.base);
  return *internal_default_instance();
}


void AutotuneResult_SuccessResult::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.AutotuneResult.SuccessResult)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && run_time_ != NULL) {
    delete run_time_;
  }
  run_time_ = NULL;
  scratch_bytes_ = GOOGLE_LONGLONG(0);
  _internal_metadata_.Clear();
}

bool AutotuneResult_SuccessResult::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.AutotuneResult.SuccessResult)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 scratch_bytes = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &scratch_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .google.protobuf.Duration run_time = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_run_time()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.AutotuneResult.SuccessResult)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.AutotuneResult.SuccessResult)
  return false;
#undef DO_
}

void AutotuneResult_SuccessResult::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.AutotuneResult.SuccessResult)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 scratch_bytes = 1;
  if (this->scratch_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->scratch_bytes(), output);
  }

  // .google.protobuf.Duration run_time = 2;
  if (this->has_run_time()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_run_time(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.AutotuneResult.SuccessResult)
}

::google::protobuf::uint8* AutotuneResult_SuccessResult::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.AutotuneResult.SuccessResult)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 scratch_bytes = 1;
  if (this->scratch_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->scratch_bytes(), target);
  }

  // .google.protobuf.Duration run_time = 2;
  if (this->has_run_time()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_run_time(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.AutotuneResult.SuccessResult)
  return target;
}

size_t AutotuneResult_SuccessResult::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.AutotuneResult.SuccessResult)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .google.protobuf.Duration run_time = 2;
  if (this->has_run_time()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *run_time_);
  }

  // int64 scratch_bytes = 1;
  if (this->scratch_bytes() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->scratch_bytes());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AutotuneResult_SuccessResult::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.AutotuneResult.SuccessResult)
  GOOGLE_DCHECK_NE(&from, this);
  const AutotuneResult_SuccessResult* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AutotuneResult_SuccessResult>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.AutotuneResult.SuccessResult)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.AutotuneResult.SuccessResult)
    MergeFrom(*source);
  }
}

void AutotuneResult_SuccessResult::MergeFrom(const AutotuneResult_SuccessResult& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.AutotuneResult.SuccessResult)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_run_time()) {
    mutable_run_time()->::google::protobuf::Duration::MergeFrom(from.run_time());
  }
  if (from.scratch_bytes() != 0) {
    set_scratch_bytes(from.scratch_bytes());
  }
}

void AutotuneResult_SuccessResult::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.AutotuneResult.SuccessResult)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AutotuneResult_SuccessResult::CopyFrom(const AutotuneResult_SuccessResult& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.AutotuneResult.SuccessResult)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AutotuneResult_SuccessResult::IsInitialized() const {
  return true;
}

void AutotuneResult_SuccessResult::Swap(AutotuneResult_SuccessResult* other) {
  if (other == this) return;
  InternalSwap(other);
}
void AutotuneResult_SuccessResult::InternalSwap(AutotuneResult_SuccessResult* other) {
  using std::swap;
  swap(run_time_, other->run_time_);
  swap(scratch_bytes_, other->scratch_bytes_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata AutotuneResult_SuccessResult::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void AutotuneResult_ConvKey::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AutotuneResult_ConvKey::kAlgorithmFieldNumber;
const int AutotuneResult_ConvKey::kTensorOpsEnabledFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AutotuneResult_ConvKey::AutotuneResult_ConvKey()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::scc_info_AutotuneResult_ConvKey.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.AutotuneResult.ConvKey)
}
AutotuneResult_ConvKey::AutotuneResult_ConvKey(const AutotuneResult_ConvKey& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&algorithm_, &from.algorithm_,
    static_cast<size_t>(reinterpret_cast<char*>(&tensor_ops_enabled_) -
    reinterpret_cast<char*>(&algorithm_)) + sizeof(tensor_ops_enabled_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.AutotuneResult.ConvKey)
}

void AutotuneResult_ConvKey::SharedCtor() {
  ::memset(&algorithm_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&tensor_ops_enabled_) -
      reinterpret_cast<char*>(&algorithm_)) + sizeof(tensor_ops_enabled_));
}

AutotuneResult_ConvKey::~AutotuneResult_ConvKey() {
  // @@protoc_insertion_point(destructor:tensorflow.AutotuneResult.ConvKey)
  SharedDtor();
}

void AutotuneResult_ConvKey::SharedDtor() {
}

void AutotuneResult_ConvKey::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* AutotuneResult_ConvKey::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const AutotuneResult_ConvKey& AutotuneResult_ConvKey::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::scc_info_AutotuneResult_ConvKey.base);
  return *internal_default_instance();
}


void AutotuneResult_ConvKey::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.AutotuneResult.ConvKey)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&algorithm_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&tensor_ops_enabled_) -
      reinterpret_cast<char*>(&algorithm_)) + sizeof(tensor_ops_enabled_));
  _internal_metadata_.Clear();
}

bool AutotuneResult_ConvKey::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.AutotuneResult.ConvKey)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 algorithm = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &algorithm_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool tensor_ops_enabled = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &tensor_ops_enabled_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.AutotuneResult.ConvKey)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.AutotuneResult.ConvKey)
  return false;
#undef DO_
}

void AutotuneResult_ConvKey::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.AutotuneResult.ConvKey)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 algorithm = 1;
  if (this->algorithm() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->algorithm(), output);
  }

  // bool tensor_ops_enabled = 2;
  if (this->tensor_ops_enabled() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(2, this->tensor_ops_enabled(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.AutotuneResult.ConvKey)
}

::google::protobuf::uint8* AutotuneResult_ConvKey::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.AutotuneResult.ConvKey)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 algorithm = 1;
  if (this->algorithm() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->algorithm(), target);
  }

  // bool tensor_ops_enabled = 2;
  if (this->tensor_ops_enabled() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(2, this->tensor_ops_enabled(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.AutotuneResult.ConvKey)
  return target;
}

size_t AutotuneResult_ConvKey::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.AutotuneResult.ConvKey)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int64 algorithm = 1;
  if (this->algorithm() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->algorithm());
  }

  // bool tensor_ops_enabled = 2;
  if (this->tensor_ops_enabled() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AutotuneResult_ConvKey::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.AutotuneResult.ConvKey)
  GOOGLE_DCHECK_NE(&from, this);
  const AutotuneResult_ConvKey* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AutotuneResult_ConvKey>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.AutotuneResult.ConvKey)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.AutotuneResult.ConvKey)
    MergeFrom(*source);
  }
}

void AutotuneResult_ConvKey::MergeFrom(const AutotuneResult_ConvKey& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.AutotuneResult.ConvKey)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.algorithm() != 0) {
    set_algorithm(from.algorithm());
  }
  if (from.tensor_ops_enabled() != 0) {
    set_tensor_ops_enabled(from.tensor_ops_enabled());
  }
}

void AutotuneResult_ConvKey::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.AutotuneResult.ConvKey)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AutotuneResult_ConvKey::CopyFrom(const AutotuneResult_ConvKey& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.AutotuneResult.ConvKey)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AutotuneResult_ConvKey::IsInitialized() const {
  return true;
}

void AutotuneResult_ConvKey::Swap(AutotuneResult_ConvKey* other) {
  if (other == this) return;
  InternalSwap(other);
}
void AutotuneResult_ConvKey::InternalSwap(AutotuneResult_ConvKey* other) {
  using std::swap;
  swap(algorithm_, other->algorithm_);
  swap(tensor_ops_enabled_, other->tensor_ops_enabled_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata AutotuneResult_ConvKey::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void AutotuneResult::InitAsDefaultInstance() {
  ::tensorflow::_AutotuneResult_default_instance_.success_ = const_cast< ::tensorflow::AutotuneResult_SuccessResult*>(
      ::tensorflow::AutotuneResult_SuccessResult::internal_default_instance());
  ::tensorflow::_AutotuneResult_default_instance_.error_string_.UnsafeSetDefault(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::tensorflow::_AutotuneResult_default_instance_.conv_ = const_cast< ::tensorflow::AutotuneResult_ConvKey*>(
      ::tensorflow::AutotuneResult_ConvKey::internal_default_instance());
  ::tensorflow::_AutotuneResult_default_instance_.reference_conv_ = const_cast< ::tensorflow::AutotuneResult_ConvKey*>(
      ::tensorflow::AutotuneResult_ConvKey::internal_default_instance());
}
void AutotuneResult::set_allocated_success(::tensorflow::AutotuneResult_SuccessResult* success) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_result();
  if (success) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      success = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, success, submessage_arena);
    }
    set_has_success();
    result_.success_ = success;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AutotuneResult.success)
}
void AutotuneResult::set_allocated_conv(::tensorflow::AutotuneResult_ConvKey* conv) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_key();
  if (conv) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      conv = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, conv, submessage_arena);
    }
    set_has_conv();
    key_.conv_ = conv;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AutotuneResult.conv)
}
void AutotuneResult::set_allocated_reference_conv(::tensorflow::AutotuneResult_ConvKey* reference_conv) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_checker_failure();
  if (reference_conv) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      reference_conv = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, reference_conv, submessage_arena);
    }
    set_has_reference_conv();
    checker_failure_.reference_conv_ = reference_conv;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AutotuneResult.reference_conv)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AutotuneResult::kSuccessFieldNumber;
const int AutotuneResult::kErrorStringFieldNumber;
const int AutotuneResult::kConvFieldNumber;
const int AutotuneResult::kReferenceConvFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AutotuneResult::AutotuneResult()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::scc_info_AutotuneResult.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.AutotuneResult)
}
AutotuneResult::AutotuneResult(const AutotuneResult& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  clear_has_result();
  switch (from.result_case()) {
    case kSuccess: {
      mutable_success()->::tensorflow::AutotuneResult_SuccessResult::MergeFrom(from.success());
      break;
    }
    case kErrorString: {
      set_error_string(from.error_string());
      break;
    }
    case RESULT_NOT_SET: {
      break;
    }
  }
  clear_has_key();
  switch (from.key_case()) {
    case kConv: {
      mutable_conv()->::tensorflow::AutotuneResult_ConvKey::MergeFrom(from.conv());
      break;
    }
    case KEY_NOT_SET: {
      break;
    }
  }
  clear_has_checker_failure();
  switch (from.checker_failure_case()) {
    case kReferenceConv: {
      mutable_reference_conv()->::tensorflow::AutotuneResult_ConvKey::MergeFrom(from.reference_conv());
      break;
    }
    case CHECKER_FAILURE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.AutotuneResult)
}

void AutotuneResult::SharedCtor() {
  clear_has_result();
  clear_has_key();
  clear_has_checker_failure();
}

AutotuneResult::~AutotuneResult() {
  // @@protoc_insertion_point(destructor:tensorflow.AutotuneResult)
  SharedDtor();
}

void AutotuneResult::SharedDtor() {
  if (has_result()) {
    clear_result();
  }
  if (has_key()) {
    clear_key();
  }
  if (has_checker_failure()) {
    clear_checker_failure();
  }
}

void AutotuneResult::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* AutotuneResult::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const AutotuneResult& AutotuneResult::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::scc_info_AutotuneResult.base);
  return *internal_default_instance();
}


void AutotuneResult::clear_result() {
// @@protoc_insertion_point(one_of_clear_start:tensorflow.AutotuneResult)
  switch (result_case()) {
    case kSuccess: {
      delete result_.success_;
      break;
    }
    case kErrorString: {
      result_.error_string_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
      break;
    }
    case RESULT_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = RESULT_NOT_SET;
}

void AutotuneResult::clear_key() {
// @@protoc_insertion_point(one_of_clear_start:tensorflow.AutotuneResult)
  switch (key_case()) {
    case kConv: {
      delete key_.conv_;
      break;
    }
    case KEY_NOT_SET: {
      break;
    }
  }
  _oneof_case_[1] = KEY_NOT_SET;
}

void AutotuneResult::clear_checker_failure() {
// @@protoc_insertion_point(one_of_clear_start:tensorflow.AutotuneResult)
  switch (checker_failure_case()) {
    case kReferenceConv: {
      delete checker_failure_.reference_conv_;
      break;
    }
    case CHECKER_FAILURE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[2] = CHECKER_FAILURE_NOT_SET;
}


void AutotuneResult::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.AutotuneResult)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_result();
  clear_key();
  clear_checker_failure();
  _internal_metadata_.Clear();
}

bool AutotuneResult::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.AutotuneResult)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.AutotuneResult.SuccessResult success = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_success()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string error_string = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_error_string()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->error_string().data(), static_cast<int>(this->error_string().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.AutotuneResult.error_string"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.AutotuneResult.ConvKey conv = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_conv()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.AutotuneResult.ConvKey reference_conv = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_reference_conv()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.AutotuneResult)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.AutotuneResult)
  return false;
#undef DO_
}

void AutotuneResult::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.AutotuneResult)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.AutotuneResult.SuccessResult success = 3;
  if (has_success()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->_internal_success(), output);
  }

  // string error_string = 4;
  if (has_error_string()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->error_string().data(), static_cast<int>(this->error_string().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.AutotuneResult.error_string");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->error_string(), output);
  }

  // .tensorflow.AutotuneResult.ConvKey conv = 5;
  if (has_conv()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, this->_internal_conv(), output);
  }

  // .tensorflow.AutotuneResult.ConvKey reference_conv = 6;
  if (has_reference_conv()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, this->_internal_reference_conv(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.AutotuneResult)
}

::google::protobuf::uint8* AutotuneResult::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.AutotuneResult)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.AutotuneResult.SuccessResult success = 3;
  if (has_success()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->_internal_success(), deterministic, target);
  }

  // string error_string = 4;
  if (has_error_string()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->error_string().data(), static_cast<int>(this->error_string().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.AutotuneResult.error_string");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->error_string(), target);
  }

  // .tensorflow.AutotuneResult.ConvKey conv = 5;
  if (has_conv()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        5, this->_internal_conv(), deterministic, target);
  }

  // .tensorflow.AutotuneResult.ConvKey reference_conv = 6;
  if (has_reference_conv()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        6, this->_internal_reference_conv(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.AutotuneResult)
  return target;
}

size_t AutotuneResult::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.AutotuneResult)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  switch (result_case()) {
    // .tensorflow.AutotuneResult.SuccessResult success = 3;
    case kSuccess: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *result_.success_);
      break;
    }
    // string error_string = 4;
    case kErrorString: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->error_string());
      break;
    }
    case RESULT_NOT_SET: {
      break;
    }
  }
  switch (key_case()) {
    // .tensorflow.AutotuneResult.ConvKey conv = 5;
    case kConv: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *key_.conv_);
      break;
    }
    case KEY_NOT_SET: {
      break;
    }
  }
  switch (checker_failure_case()) {
    // .tensorflow.AutotuneResult.ConvKey reference_conv = 6;
    case kReferenceConv: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *checker_failure_.reference_conv_);
      break;
    }
    case CHECKER_FAILURE_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AutotuneResult::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.AutotuneResult)
  GOOGLE_DCHECK_NE(&from, this);
  const AutotuneResult* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AutotuneResult>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.AutotuneResult)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.AutotuneResult)
    MergeFrom(*source);
  }
}

void AutotuneResult::MergeFrom(const AutotuneResult& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.AutotuneResult)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.result_case()) {
    case kSuccess: {
      mutable_success()->::tensorflow::AutotuneResult_SuccessResult::MergeFrom(from.success());
      break;
    }
    case kErrorString: {
      set_error_string(from.error_string());
      break;
    }
    case RESULT_NOT_SET: {
      break;
    }
  }
  switch (from.key_case()) {
    case kConv: {
      mutable_conv()->::tensorflow::AutotuneResult_ConvKey::MergeFrom(from.conv());
      break;
    }
    case KEY_NOT_SET: {
      break;
    }
  }
  switch (from.checker_failure_case()) {
    case kReferenceConv: {
      mutable_reference_conv()->::tensorflow::AutotuneResult_ConvKey::MergeFrom(from.reference_conv());
      break;
    }
    case CHECKER_FAILURE_NOT_SET: {
      break;
    }
  }
}

void AutotuneResult::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.AutotuneResult)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AutotuneResult::CopyFrom(const AutotuneResult& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.AutotuneResult)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AutotuneResult::IsInitialized() const {
  return true;
}

void AutotuneResult::Swap(AutotuneResult* other) {
  if (other == this) return;
  InternalSwap(other);
}
void AutotuneResult::InternalSwap(AutotuneResult* other) {
  using std::swap;
  swap(result_, other->result_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  swap(key_, other->key_);
  swap(_oneof_case_[1], other->_oneof_case_[1]);
  swap(checker_failure_, other->checker_failure_);
  swap(_oneof_case_[2], other->_oneof_case_[2]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata AutotuneResult::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void AutotuningLog::InitAsDefaultInstance() {
  ::tensorflow::_AutotuningLog_default_instance_._instance.get_mutable()->instr_ = const_cast< ::google::protobuf::Any*>(
      ::google::protobuf::Any::internal_default_instance());
  ::tensorflow::_AutotuningLog_default_instance_._instance.get_mutable()->cudnn_version_ = const_cast< ::tensorflow::CudnnVersion*>(
      ::tensorflow::CudnnVersion::internal_default_instance());
  ::tensorflow::_AutotuningLog_default_instance_._instance.get_mutable()->compute_capability_ = const_cast< ::tensorflow::ComputeCapability*>(
      ::tensorflow::ComputeCapability::internal_default_instance());
}
void AutotuningLog::clear_instr() {
  if (GetArenaNoVirtual() == NULL && instr_ != NULL) {
    delete instr_;
  }
  instr_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AutotuningLog::kInstrFieldNumber;
const int AutotuningLog::kResultsFieldNumber;
const int AutotuningLog::kCudnnVersionFieldNumber;
const int AutotuningLog::kComputeCapabilityFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AutotuningLog::AutotuningLog()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::scc_info_AutotuningLog.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.AutotuningLog)
}
AutotuningLog::AutotuningLog(const AutotuningLog& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      results_(from.results_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_instr()) {
    instr_ = new ::google::protobuf::Any(*from.instr_);
  } else {
    instr_ = NULL;
  }
  if (from.has_cudnn_version()) {
    cudnn_version_ = new ::tensorflow::CudnnVersion(*from.cudnn_version_);
  } else {
    cudnn_version_ = NULL;
  }
  if (from.has_compute_capability()) {
    compute_capability_ = new ::tensorflow::ComputeCapability(*from.compute_capability_);
  } else {
    compute_capability_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.AutotuningLog)
}

void AutotuningLog::SharedCtor() {
  ::memset(&instr_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&compute_capability_) -
      reinterpret_cast<char*>(&instr_)) + sizeof(compute_capability_));
}

AutotuningLog::~AutotuningLog() {
  // @@protoc_insertion_point(destructor:tensorflow.AutotuningLog)
  SharedDtor();
}

void AutotuningLog::SharedDtor() {
  if (this != internal_default_instance()) delete instr_;
  if (this != internal_default_instance()) delete cudnn_version_;
  if (this != internal_default_instance()) delete compute_capability_;
}

void AutotuningLog::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* AutotuningLog::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const AutotuningLog& AutotuningLog::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::scc_info_AutotuningLog.base);
  return *internal_default_instance();
}


void AutotuningLog::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.AutotuningLog)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  results_.Clear();
  if (GetArenaNoVirtual() == NULL && instr_ != NULL) {
    delete instr_;
  }
  instr_ = NULL;
  if (GetArenaNoVirtual() == NULL && cudnn_version_ != NULL) {
    delete cudnn_version_;
  }
  cudnn_version_ = NULL;
  if (GetArenaNoVirtual() == NULL && compute_capability_ != NULL) {
    delete compute_capability_;
  }
  compute_capability_ = NULL;
  _internal_metadata_.Clear();
}

bool AutotuningLog::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.AutotuningLog)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .google.protobuf.Any instr = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_instr()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.AutotuneResult results = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_results()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.CudnnVersion cudnn_version = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_cudnn_version()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.ComputeCapability compute_capability = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_compute_capability()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.AutotuningLog)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.AutotuningLog)
  return false;
#undef DO_
}

void AutotuningLog::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.AutotuningLog)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .google.protobuf.Any instr = 1;
  if (this->has_instr()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->_internal_instr(), output);
  }

  // repeated .tensorflow.AutotuneResult results = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->results_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2,
      this->results(static_cast<int>(i)),
      output);
  }

  // .tensorflow.CudnnVersion cudnn_version = 3;
  if (this->has_cudnn_version()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->_internal_cudnn_version(), output);
  }

  // .tensorflow.ComputeCapability compute_capability = 4;
  if (this->has_compute_capability()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->_internal_compute_capability(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.AutotuningLog)
}

::google::protobuf::uint8* AutotuningLog::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.AutotuningLog)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .google.protobuf.Any instr = 1;
  if (this->has_instr()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->_internal_instr(), deterministic, target);
  }

  // repeated .tensorflow.AutotuneResult results = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->results_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->results(static_cast<int>(i)), deterministic, target);
  }

  // .tensorflow.CudnnVersion cudnn_version = 3;
  if (this->has_cudnn_version()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->_internal_cudnn_version(), deterministic, target);
  }

  // .tensorflow.ComputeCapability compute_capability = 4;
  if (this->has_compute_capability()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->_internal_compute_capability(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.AutotuningLog)
  return target;
}

size_t AutotuningLog::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.AutotuningLog)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.AutotuneResult results = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->results_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->results(static_cast<int>(i)));
    }
  }

  // .google.protobuf.Any instr = 1;
  if (this->has_instr()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *instr_);
  }

  // .tensorflow.CudnnVersion cudnn_version = 3;
  if (this->has_cudnn_version()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *cudnn_version_);
  }

  // .tensorflow.ComputeCapability compute_capability = 4;
  if (this->has_compute_capability()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *compute_capability_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AutotuningLog::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.AutotuningLog)
  GOOGLE_DCHECK_NE(&from, this);
  const AutotuningLog* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AutotuningLog>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.AutotuningLog)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.AutotuningLog)
    MergeFrom(*source);
  }
}

void AutotuningLog::MergeFrom(const AutotuningLog& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.AutotuningLog)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  results_.MergeFrom(from.results_);
  if (from.has_instr()) {
    mutable_instr()->::google::protobuf::Any::MergeFrom(from.instr());
  }
  if (from.has_cudnn_version()) {
    mutable_cudnn_version()->::tensorflow::CudnnVersion::MergeFrom(from.cudnn_version());
  }
  if (from.has_compute_capability()) {
    mutable_compute_capability()->::tensorflow::ComputeCapability::MergeFrom(from.compute_capability());
  }
}

void AutotuningLog::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.AutotuningLog)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AutotuningLog::CopyFrom(const AutotuningLog& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.AutotuningLog)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AutotuningLog::IsInitialized() const {
  return true;
}

void AutotuningLog::Swap(AutotuningLog* other) {
  if (other == this) return;
  InternalSwap(other);
}
void AutotuningLog::InternalSwap(AutotuningLog* other) {
  using std::swap;
  CastToBase(&results_)->InternalSwap(CastToBase(&other->results_));
  swap(instr_, other->instr_);
  swap(cudnn_version_, other->cudnn_version_);
  swap(compute_capability_, other->compute_capability_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata AutotuningLog::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::CudnnVersion* Arena::CreateMaybeMessage< ::tensorflow::CudnnVersion >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::CudnnVersion >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::ComputeCapability* Arena::CreateMaybeMessage< ::tensorflow::ComputeCapability >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::ComputeCapability >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::AutotuneResult_SuccessResult* Arena::CreateMaybeMessage< ::tensorflow::AutotuneResult_SuccessResult >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::AutotuneResult_SuccessResult >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::AutotuneResult_ConvKey* Arena::CreateMaybeMessage< ::tensorflow::AutotuneResult_ConvKey >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::AutotuneResult_ConvKey >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::AutotuneResult* Arena::CreateMaybeMessage< ::tensorflow::AutotuneResult >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::AutotuneResult >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::AutotuningLog* Arena::CreateMaybeMessage< ::tensorflow::AutotuningLog >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::AutotuningLog >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/tpu/tpu_embedding_output_layout.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto 

namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[6];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto
namespace tensorflow {
namespace tpu {
class TPUEmbeddingOutputLayout;
class TPUEmbeddingOutputLayoutDefaultTypeInternal;
extern TPUEmbeddingOutputLayoutDefaultTypeInternal _TPUEmbeddingOutputLayout_default_instance_;
class TPUEmbeddingOutputLayout_EmbeddingOutputTensor;
class TPUEmbeddingOutputLayout_EmbeddingOutputTensorDefaultTypeInternal;
extern TPUEmbeddingOutputLayout_EmbeddingOutputTensorDefaultTypeInternal _TPUEmbeddingOutputLayout_EmbeddingOutputTensor_default_instance_;
class TPUEmbeddingOutputLayout_FeatureDescriptor;
class TPUEmbeddingOutputLayout_FeatureDescriptorDefaultTypeInternal;
extern TPUEmbeddingOutputLayout_FeatureDescriptorDefaultTypeInternal _TPUEmbeddingOutputLayout_FeatureDescriptor_default_instance_;
class TPUEmbeddingOutputLayout_OutputLocation;
class TPUEmbeddingOutputLayout_OutputLocationDefaultTypeInternal;
extern TPUEmbeddingOutputLayout_OutputLocationDefaultTypeInternal _TPUEmbeddingOutputLayout_OutputLocation_default_instance_;
class TPUEmbeddingOutputLayout_TableDescriptor;
class TPUEmbeddingOutputLayout_TableDescriptorDefaultTypeInternal;
extern TPUEmbeddingOutputLayout_TableDescriptorDefaultTypeInternal _TPUEmbeddingOutputLayout_TableDescriptor_default_instance_;
class TPUEmbeddingOutputLayout_TwoDOutputTensor;
class TPUEmbeddingOutputLayout_TwoDOutputTensorDefaultTypeInternal;
extern TPUEmbeddingOutputLayout_TwoDOutputTensorDefaultTypeInternal _TPUEmbeddingOutputLayout_TwoDOutputTensor_default_instance_;
}  // namespace tpu
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::tpu::TPUEmbeddingOutputLayout* Arena::CreateMaybeMessage<::tensorflow::tpu::TPUEmbeddingOutputLayout>(Arena*);
template<> ::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor* Arena::CreateMaybeMessage<::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor>(Arena*);
template<> ::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor* Arena::CreateMaybeMessage<::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor>(Arena*);
template<> ::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation* Arena::CreateMaybeMessage<::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation>(Arena*);
template<> ::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor* Arena::CreateMaybeMessage<::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor>(Arena*);
template<> ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor* Arena::CreateMaybeMessage<::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {
namespace tpu {

// ===================================================================

class TPUEmbeddingOutputLayout_OutputLocation : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation) */ {
 public:
  TPUEmbeddingOutputLayout_OutputLocation();
  virtual ~TPUEmbeddingOutputLayout_OutputLocation();

  TPUEmbeddingOutputLayout_OutputLocation(const TPUEmbeddingOutputLayout_OutputLocation& from);

  inline TPUEmbeddingOutputLayout_OutputLocation& operator=(const TPUEmbeddingOutputLayout_OutputLocation& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TPUEmbeddingOutputLayout_OutputLocation(TPUEmbeddingOutputLayout_OutputLocation&& from) noexcept
    : TPUEmbeddingOutputLayout_OutputLocation() {
    *this = ::std::move(from);
  }

  inline TPUEmbeddingOutputLayout_OutputLocation& operator=(TPUEmbeddingOutputLayout_OutputLocation&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const TPUEmbeddingOutputLayout_OutputLocation& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TPUEmbeddingOutputLayout_OutputLocation* internal_default_instance() {
    return reinterpret_cast<const TPUEmbeddingOutputLayout_OutputLocation*>(
               &_TPUEmbeddingOutputLayout_OutputLocation_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void Swap(TPUEmbeddingOutputLayout_OutputLocation* other);
  friend void swap(TPUEmbeddingOutputLayout_OutputLocation& a, TPUEmbeddingOutputLayout_OutputLocation& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TPUEmbeddingOutputLayout_OutputLocation* New() const final {
    return CreateMaybeMessage<TPUEmbeddingOutputLayout_OutputLocation>(NULL);
  }

  TPUEmbeddingOutputLayout_OutputLocation* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TPUEmbeddingOutputLayout_OutputLocation>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TPUEmbeddingOutputLayout_OutputLocation& from);
  void MergeFrom(const TPUEmbeddingOutputLayout_OutputLocation& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TPUEmbeddingOutputLayout_OutputLocation* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int32 tensor_index = 1;
  void clear_tensor_index();
  static const int kTensorIndexFieldNumber = 1;
  ::google::protobuf::int32 tensor_index() const;
  void set_tensor_index(::google::protobuf::int32 value);

  // int32 dim0_offset = 2;
  void clear_dim0_offset();
  static const int kDim0OffsetFieldNumber = 2;
  ::google::protobuf::int32 dim0_offset() const;
  void set_dim0_offset(::google::protobuf::int32 value);

  // int32 dim1_offset = 3;
  void clear_dim1_offset();
  static const int kDim1OffsetFieldNumber = 3;
  ::google::protobuf::int32 dim1_offset() const;
  void set_dim1_offset(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int32 tensor_index_;
  ::google::protobuf::int32 dim0_offset_;
  ::google::protobuf::int32 dim1_offset_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class TPUEmbeddingOutputLayout_FeatureDescriptor : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor) */ {
 public:
  TPUEmbeddingOutputLayout_FeatureDescriptor();
  virtual ~TPUEmbeddingOutputLayout_FeatureDescriptor();

  TPUEmbeddingOutputLayout_FeatureDescriptor(const TPUEmbeddingOutputLayout_FeatureDescriptor& from);

  inline TPUEmbeddingOutputLayout_FeatureDescriptor& operator=(const TPUEmbeddingOutputLayout_FeatureDescriptor& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TPUEmbeddingOutputLayout_FeatureDescriptor(TPUEmbeddingOutputLayout_FeatureDescriptor&& from) noexcept
    : TPUEmbeddingOutputLayout_FeatureDescriptor() {
    *this = ::std::move(from);
  }

  inline TPUEmbeddingOutputLayout_FeatureDescriptor& operator=(TPUEmbeddingOutputLayout_FeatureDescriptor&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const TPUEmbeddingOutputLayout_FeatureDescriptor& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TPUEmbeddingOutputLayout_FeatureDescriptor* internal_default_instance() {
    return reinterpret_cast<const TPUEmbeddingOutputLayout_FeatureDescriptor*>(
               &_TPUEmbeddingOutputLayout_FeatureDescriptor_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void Swap(TPUEmbeddingOutputLayout_FeatureDescriptor* other);
  friend void swap(TPUEmbeddingOutputLayout_FeatureDescriptor& a, TPUEmbeddingOutputLayout_FeatureDescriptor& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TPUEmbeddingOutputLayout_FeatureDescriptor* New() const final {
    return CreateMaybeMessage<TPUEmbeddingOutputLayout_FeatureDescriptor>(NULL);
  }

  TPUEmbeddingOutputLayout_FeatureDescriptor* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TPUEmbeddingOutputLayout_FeatureDescriptor>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TPUEmbeddingOutputLayout_FeatureDescriptor& from);
  void MergeFrom(const TPUEmbeddingOutputLayout_FeatureDescriptor& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TPUEmbeddingOutputLayout_FeatureDescriptor* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation output_location = 1;
  int output_location_size() const;
  void clear_output_location();
  static const int kOutputLocationFieldNumber = 1;
  ::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation* mutable_output_location(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation >*
      mutable_output_location();
  const ::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation& output_location(int index) const;
  ::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation* add_output_location();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation >&
      output_location() const;

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation > output_location_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class TPUEmbeddingOutputLayout_TableDescriptor : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor) */ {
 public:
  TPUEmbeddingOutputLayout_TableDescriptor();
  virtual ~TPUEmbeddingOutputLayout_TableDescriptor();

  TPUEmbeddingOutputLayout_TableDescriptor(const TPUEmbeddingOutputLayout_TableDescriptor& from);

  inline TPUEmbeddingOutputLayout_TableDescriptor& operator=(const TPUEmbeddingOutputLayout_TableDescriptor& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TPUEmbeddingOutputLayout_TableDescriptor(TPUEmbeddingOutputLayout_TableDescriptor&& from) noexcept
    : TPUEmbeddingOutputLayout_TableDescriptor() {
    *this = ::std::move(from);
  }

  inline TPUEmbeddingOutputLayout_TableDescriptor& operator=(TPUEmbeddingOutputLayout_TableDescriptor&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const TPUEmbeddingOutputLayout_TableDescriptor& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TPUEmbeddingOutputLayout_TableDescriptor* internal_default_instance() {
    return reinterpret_cast<const TPUEmbeddingOutputLayout_TableDescriptor*>(
               &_TPUEmbeddingOutputLayout_TableDescriptor_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void Swap(TPUEmbeddingOutputLayout_TableDescriptor* other);
  friend void swap(TPUEmbeddingOutputLayout_TableDescriptor& a, TPUEmbeddingOutputLayout_TableDescriptor& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TPUEmbeddingOutputLayout_TableDescriptor* New() const final {
    return CreateMaybeMessage<TPUEmbeddingOutputLayout_TableDescriptor>(NULL);
  }

  TPUEmbeddingOutputLayout_TableDescriptor* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TPUEmbeddingOutputLayout_TableDescriptor>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TPUEmbeddingOutputLayout_TableDescriptor& from);
  void MergeFrom(const TPUEmbeddingOutputLayout_TableDescriptor& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TPUEmbeddingOutputLayout_TableDescriptor* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor feature = 1;
  int feature_size() const;
  void clear_feature();
  static const int kFeatureFieldNumber = 1;
  ::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor* mutable_feature(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor >*
      mutable_feature();
  const ::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor& feature(int index) const;
  ::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor* add_feature();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor >&
      feature() const;

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor > feature_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class TPUEmbeddingOutputLayout_TwoDOutputTensor : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor) */ {
 public:
  TPUEmbeddingOutputLayout_TwoDOutputTensor();
  virtual ~TPUEmbeddingOutputLayout_TwoDOutputTensor();

  TPUEmbeddingOutputLayout_TwoDOutputTensor(const TPUEmbeddingOutputLayout_TwoDOutputTensor& from);

  inline TPUEmbeddingOutputLayout_TwoDOutputTensor& operator=(const TPUEmbeddingOutputLayout_TwoDOutputTensor& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TPUEmbeddingOutputLayout_TwoDOutputTensor(TPUEmbeddingOutputLayout_TwoDOutputTensor&& from) noexcept
    : TPUEmbeddingOutputLayout_TwoDOutputTensor() {
    *this = ::std::move(from);
  }

  inline TPUEmbeddingOutputLayout_TwoDOutputTensor& operator=(TPUEmbeddingOutputLayout_TwoDOutputTensor&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const TPUEmbeddingOutputLayout_TwoDOutputTensor& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TPUEmbeddingOutputLayout_TwoDOutputTensor* internal_default_instance() {
    return reinterpret_cast<const TPUEmbeddingOutputLayout_TwoDOutputTensor*>(
               &_TPUEmbeddingOutputLayout_TwoDOutputTensor_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void Swap(TPUEmbeddingOutputLayout_TwoDOutputTensor* other);
  friend void swap(TPUEmbeddingOutputLayout_TwoDOutputTensor& a, TPUEmbeddingOutputLayout_TwoDOutputTensor& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TPUEmbeddingOutputLayout_TwoDOutputTensor* New() const final {
    return CreateMaybeMessage<TPUEmbeddingOutputLayout_TwoDOutputTensor>(NULL);
  }

  TPUEmbeddingOutputLayout_TwoDOutputTensor* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TPUEmbeddingOutputLayout_TwoDOutputTensor>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TPUEmbeddingOutputLayout_TwoDOutputTensor& from);
  void MergeFrom(const TPUEmbeddingOutputLayout_TwoDOutputTensor& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TPUEmbeddingOutputLayout_TwoDOutputTensor* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int32 dim1_size = 1;
  void clear_dim1_size();
  static const int kDim1SizeFieldNumber = 1;
  ::google::protobuf::int32 dim1_size() const;
  void set_dim1_size(::google::protobuf::int32 value);

  // int32 dim0_size_per_sample = 2;
  void clear_dim0_size_per_sample();
  static const int kDim0SizePerSampleFieldNumber = 2;
  ::google::protobuf::int32 dim0_size_per_sample() const;
  void set_dim0_size_per_sample(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int32 dim1_size_;
  ::google::protobuf::int32 dim0_size_per_sample_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class TPUEmbeddingOutputLayout_EmbeddingOutputTensor : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor) */ {
 public:
  TPUEmbeddingOutputLayout_EmbeddingOutputTensor();
  virtual ~TPUEmbeddingOutputLayout_EmbeddingOutputTensor();

  TPUEmbeddingOutputLayout_EmbeddingOutputTensor(const TPUEmbeddingOutputLayout_EmbeddingOutputTensor& from);

  inline TPUEmbeddingOutputLayout_EmbeddingOutputTensor& operator=(const TPUEmbeddingOutputLayout_EmbeddingOutputTensor& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TPUEmbeddingOutputLayout_EmbeddingOutputTensor(TPUEmbeddingOutputLayout_EmbeddingOutputTensor&& from) noexcept
    : TPUEmbeddingOutputLayout_EmbeddingOutputTensor() {
    *this = ::std::move(from);
  }

  inline TPUEmbeddingOutputLayout_EmbeddingOutputTensor& operator=(TPUEmbeddingOutputLayout_EmbeddingOutputTensor&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const TPUEmbeddingOutputLayout_EmbeddingOutputTensor& default_instance();

  enum OutputFormatCase {
    kTwoD = 4,
    OUTPUT_FORMAT_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TPUEmbeddingOutputLayout_EmbeddingOutputTensor* internal_default_instance() {
    return reinterpret_cast<const TPUEmbeddingOutputLayout_EmbeddingOutputTensor*>(
               &_TPUEmbeddingOutputLayout_EmbeddingOutputTensor_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void Swap(TPUEmbeddingOutputLayout_EmbeddingOutputTensor* other);
  friend void swap(TPUEmbeddingOutputLayout_EmbeddingOutputTensor& a, TPUEmbeddingOutputLayout_EmbeddingOutputTensor& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TPUEmbeddingOutputLayout_EmbeddingOutputTensor* New() const final {
    return CreateMaybeMessage<TPUEmbeddingOutputLayout_EmbeddingOutputTensor>(NULL);
  }

  TPUEmbeddingOutputLayout_EmbeddingOutputTensor* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TPUEmbeddingOutputLayout_EmbeddingOutputTensor>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TPUEmbeddingOutputLayout_EmbeddingOutputTensor& from);
  void MergeFrom(const TPUEmbeddingOutputLayout_EmbeddingOutputTensor& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TPUEmbeddingOutputLayout_EmbeddingOutputTensor* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor two_d = 4;
  bool has_two_d() const;
  void clear_two_d();
  static const int kTwoDFieldNumber = 4;
  private:
  const ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor& _internal_two_d() const;
  public:
  const ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor& two_d() const;
  ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor* release_two_d();
  ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor* mutable_two_d();
  void set_allocated_two_d(::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor* two_d);

  void clear_output_format();
  OutputFormatCase output_format_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor)
 private:
  void set_has_two_d();

  inline bool has_output_format() const;
  inline void clear_has_output_format();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  union OutputFormatUnion {
    OutputFormatUnion() {}
    ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor* two_d_;
  } output_format_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class TPUEmbeddingOutputLayout : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.TPUEmbeddingOutputLayout) */ {
 public:
  TPUEmbeddingOutputLayout();
  virtual ~TPUEmbeddingOutputLayout();

  TPUEmbeddingOutputLayout(const TPUEmbeddingOutputLayout& from);

  inline TPUEmbeddingOutputLayout& operator=(const TPUEmbeddingOutputLayout& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TPUEmbeddingOutputLayout(TPUEmbeddingOutputLayout&& from) noexcept
    : TPUEmbeddingOutputLayout() {
    *this = ::std::move(from);
  }

  inline TPUEmbeddingOutputLayout& operator=(TPUEmbeddingOutputLayout&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const TPUEmbeddingOutputLayout& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TPUEmbeddingOutputLayout* internal_default_instance() {
    return reinterpret_cast<const TPUEmbeddingOutputLayout*>(
               &_TPUEmbeddingOutputLayout_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  void Swap(TPUEmbeddingOutputLayout* other);
  friend void swap(TPUEmbeddingOutputLayout& a, TPUEmbeddingOutputLayout& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TPUEmbeddingOutputLayout* New() const final {
    return CreateMaybeMessage<TPUEmbeddingOutputLayout>(NULL);
  }

  TPUEmbeddingOutputLayout* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TPUEmbeddingOutputLayout>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TPUEmbeddingOutputLayout& from);
  void MergeFrom(const TPUEmbeddingOutputLayout& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TPUEmbeddingOutputLayout* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef TPUEmbeddingOutputLayout_OutputLocation OutputLocation;
  typedef TPUEmbeddingOutputLayout_FeatureDescriptor FeatureDescriptor;
  typedef TPUEmbeddingOutputLayout_TableDescriptor TableDescriptor;
  typedef TPUEmbeddingOutputLayout_TwoDOutputTensor TwoDOutputTensor;
  typedef TPUEmbeddingOutputLayout_EmbeddingOutputTensor EmbeddingOutputTensor;

  // accessors -------------------------------------------------------

  // repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor table = 1;
  int table_size() const;
  void clear_table();
  static const int kTableFieldNumber = 1;
  ::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor* mutable_table(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor >*
      mutable_table();
  const ::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor& table(int index) const;
  ::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor* add_table();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor >&
      table() const;

  // repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor output = 2;
  int output_size() const;
  void clear_output();
  static const int kOutputFieldNumber = 2;
  ::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor* mutable_output(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor >*
      mutable_output();
  const ::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor& output(int index) const;
  ::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor* add_output();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor >&
      output() const;

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.TPUEmbeddingOutputLayout)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor > table_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor > output_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TPUEmbeddingOutputLayout_OutputLocation

// int32 tensor_index = 1;
inline void TPUEmbeddingOutputLayout_OutputLocation::clear_tensor_index() {
  tensor_index_ = 0;
}
inline ::google::protobuf::int32 TPUEmbeddingOutputLayout_OutputLocation::tensor_index() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation.tensor_index)
  return tensor_index_;
}
inline void TPUEmbeddingOutputLayout_OutputLocation::set_tensor_index(::google::protobuf::int32 value) {
  
  tensor_index_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation.tensor_index)
}

// int32 dim0_offset = 2;
inline void TPUEmbeddingOutputLayout_OutputLocation::clear_dim0_offset() {
  dim0_offset_ = 0;
}
inline ::google::protobuf::int32 TPUEmbeddingOutputLayout_OutputLocation::dim0_offset() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation.dim0_offset)
  return dim0_offset_;
}
inline void TPUEmbeddingOutputLayout_OutputLocation::set_dim0_offset(::google::protobuf::int32 value) {
  
  dim0_offset_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation.dim0_offset)
}

// int32 dim1_offset = 3;
inline void TPUEmbeddingOutputLayout_OutputLocation::clear_dim1_offset() {
  dim1_offset_ = 0;
}
inline ::google::protobuf::int32 TPUEmbeddingOutputLayout_OutputLocation::dim1_offset() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation.dim1_offset)
  return dim1_offset_;
}
inline void TPUEmbeddingOutputLayout_OutputLocation::set_dim1_offset(::google::protobuf::int32 value) {
  
  dim1_offset_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation.dim1_offset)
}

// -------------------------------------------------------------------

// TPUEmbeddingOutputLayout_FeatureDescriptor

// repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation output_location = 1;
inline int TPUEmbeddingOutputLayout_FeatureDescriptor::output_location_size() const {
  return output_location_.size();
}
inline void TPUEmbeddingOutputLayout_FeatureDescriptor::clear_output_location() {
  output_location_.Clear();
}
inline ::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation* TPUEmbeddingOutputLayout_FeatureDescriptor::mutable_output_location(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor.output_location)
  return output_location_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation >*
TPUEmbeddingOutputLayout_FeatureDescriptor::mutable_output_location() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor.output_location)
  return &output_location_;
}
inline const ::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation& TPUEmbeddingOutputLayout_FeatureDescriptor::output_location(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor.output_location)
  return output_location_.Get(index);
}
inline ::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation* TPUEmbeddingOutputLayout_FeatureDescriptor::add_output_location() {
  // @@protoc_insertion_point(field_add:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor.output_location)
  return output_location_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation >&
TPUEmbeddingOutputLayout_FeatureDescriptor::output_location() const {
  // @@protoc_insertion_point(field_list:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor.output_location)
  return output_location_;
}

// -------------------------------------------------------------------

// TPUEmbeddingOutputLayout_TableDescriptor

// repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor feature = 1;
inline int TPUEmbeddingOutputLayout_TableDescriptor::feature_size() const {
  return feature_.size();
}
inline void TPUEmbeddingOutputLayout_TableDescriptor::clear_feature() {
  feature_.Clear();
}
inline ::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor* TPUEmbeddingOutputLayout_TableDescriptor::mutable_feature(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor.feature)
  return feature_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor >*
TPUEmbeddingOutputLayout_TableDescriptor::mutable_feature() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor.feature)
  return &feature_;
}
inline const ::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor& TPUEmbeddingOutputLayout_TableDescriptor::feature(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor.feature)
  return feature_.Get(index);
}
inline ::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor* TPUEmbeddingOutputLayout_TableDescriptor::add_feature() {
  // @@protoc_insertion_point(field_add:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor.feature)
  return feature_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor >&
TPUEmbeddingOutputLayout_TableDescriptor::feature() const {
  // @@protoc_insertion_point(field_list:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor.feature)
  return feature_;
}

// -------------------------------------------------------------------

// TPUEmbeddingOutputLayout_TwoDOutputTensor

// int32 dim0_size_per_sample = 2;
inline void TPUEmbeddingOutputLayout_TwoDOutputTensor::clear_dim0_size_per_sample() {
  dim0_size_per_sample_ = 0;
}
inline ::google::protobuf::int32 TPUEmbeddingOutputLayout_TwoDOutputTensor::dim0_size_per_sample() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor.dim0_size_per_sample)
  return dim0_size_per_sample_;
}
inline void TPUEmbeddingOutputLayout_TwoDOutputTensor::set_dim0_size_per_sample(::google::protobuf::int32 value) {
  
  dim0_size_per_sample_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor.dim0_size_per_sample)
}

// int32 dim1_size = 1;
inline void TPUEmbeddingOutputLayout_TwoDOutputTensor::clear_dim1_size() {
  dim1_size_ = 0;
}
inline ::google::protobuf::int32 TPUEmbeddingOutputLayout_TwoDOutputTensor::dim1_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor.dim1_size)
  return dim1_size_;
}
inline void TPUEmbeddingOutputLayout_TwoDOutputTensor::set_dim1_size(::google::protobuf::int32 value) {
  
  dim1_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor.dim1_size)
}

// -------------------------------------------------------------------

// TPUEmbeddingOutputLayout_EmbeddingOutputTensor

// .tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor two_d = 4;
inline bool TPUEmbeddingOutputLayout_EmbeddingOutputTensor::has_two_d() const {
  return output_format_case() == kTwoD;
}
inline void TPUEmbeddingOutputLayout_EmbeddingOutputTensor::set_has_two_d() {
  _oneof_case_[0] = kTwoD;
}
inline void TPUEmbeddingOutputLayout_EmbeddingOutputTensor::clear_two_d() {
  if (has_two_d()) {
    delete output_format_.two_d_;
    clear_has_output_format();
  }
}
inline const ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor& TPUEmbeddingOutputLayout_EmbeddingOutputTensor::_internal_two_d() const {
  return *output_format_.two_d_;
}
inline ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor* TPUEmbeddingOutputLayout_EmbeddingOutputTensor::release_two_d() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor.two_d)
  if (has_two_d()) {
    clear_has_output_format();
      ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor* temp = output_format_.two_d_;
    output_format_.two_d_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor& TPUEmbeddingOutputLayout_EmbeddingOutputTensor::two_d() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor.two_d)
  return has_two_d()
      ? *output_format_.two_d_
      : *reinterpret_cast< ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor*>(&::tensorflow::tpu::_TPUEmbeddingOutputLayout_TwoDOutputTensor_default_instance_);
}
inline ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor* TPUEmbeddingOutputLayout_EmbeddingOutputTensor::mutable_two_d() {
  if (!has_two_d()) {
    clear_output_format();
    set_has_two_d();
    output_format_.two_d_ = CreateMaybeMessage< ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor.two_d)
  return output_format_.two_d_;
}

inline bool TPUEmbeddingOutputLayout_EmbeddingOutputTensor::has_output_format() const {
  return output_format_case() != OUTPUT_FORMAT_NOT_SET;
}
inline void TPUEmbeddingOutputLayout_EmbeddingOutputTensor::clear_has_output_format() {
  _oneof_case_[0] = OUTPUT_FORMAT_NOT_SET;
}
inline TPUEmbeddingOutputLayout_EmbeddingOutputTensor::OutputFormatCase TPUEmbeddingOutputLayout_EmbeddingOutputTensor::output_format_case() const {
  return TPUEmbeddingOutputLayout_EmbeddingOutputTensor::OutputFormatCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// TPUEmbeddingOutputLayout

// repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor table = 1;
inline int TPUEmbeddingOutputLayout::table_size() const {
  return table_.size();
}
inline void TPUEmbeddingOutputLayout::clear_table() {
  table_.Clear();
}
inline ::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor* TPUEmbeddingOutputLayout::mutable_table(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUEmbeddingOutputLayout.table)
  return table_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor >*
TPUEmbeddingOutputLayout::mutable_table() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tpu.TPUEmbeddingOutputLayout.table)
  return &table_;
}
inline const ::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor& TPUEmbeddingOutputLayout::table(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingOutputLayout.table)
  return table_.Get(index);
}
inline ::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor* TPUEmbeddingOutputLayout::add_table() {
  // @@protoc_insertion_point(field_add:tensorflow.tpu.TPUEmbeddingOutputLayout.table)
  return table_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor >&
TPUEmbeddingOutputLayout::table() const {
  // @@protoc_insertion_point(field_list:tensorflow.tpu.TPUEmbeddingOutputLayout.table)
  return table_;
}

// repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor output = 2;
inline int TPUEmbeddingOutputLayout::output_size() const {
  return output_.size();
}
inline void TPUEmbeddingOutputLayout::clear_output() {
  output_.Clear();
}
inline ::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor* TPUEmbeddingOutputLayout::mutable_output(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUEmbeddingOutputLayout.output)
  return output_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor >*
TPUEmbeddingOutputLayout::mutable_output() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tpu.TPUEmbeddingOutputLayout.output)
  return &output_;
}
inline const ::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor& TPUEmbeddingOutputLayout::output(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingOutputLayout.output)
  return output_.Get(index);
}
inline ::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor* TPUEmbeddingOutputLayout::add_output() {
  // @@protoc_insertion_point(field_add:tensorflow.tpu.TPUEmbeddingOutputLayout.output)
  return output_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor >&
TPUEmbeddingOutputLayout::output() const {
  // @@protoc_insertion_point(field_list:tensorflow.tpu.TPUEmbeddingOutputLayout.output)
  return output_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tpu
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto

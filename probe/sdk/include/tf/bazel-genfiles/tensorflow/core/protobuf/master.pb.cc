// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/master.proto

#include "tensorflow/core/protobuf/master.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_DeviceAttributes;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2fgraph_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fgraph_2eproto ::google::protobuf::internal::SCCInfo<3> scc_info_GraphDef;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fgraph_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_TensorProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto
namespace protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_RunOptions;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto ::google::protobuf::internal::SCCInfo<4> scc_info_CallableOptions;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto ::google::protobuf::internal::SCCInfo<4> scc_info_RunMetadata;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto ::google::protobuf::internal::SCCInfo<7> scc_info_ConfigProto;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto
namespace protobuf_tensorflow_2fcore_2fprotobuf_2fnamed_5ftensor_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fnamed_5ftensor_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_NamedTensorProto;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fnamed_5ftensor_2eproto
namespace tensorflow {
class CreateSessionRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CreateSessionRequest>
      _instance;
} _CreateSessionRequest_default_instance_;
class CreateSessionResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CreateSessionResponse>
      _instance;
} _CreateSessionResponse_default_instance_;
class ExtendSessionRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ExtendSessionRequest>
      _instance;
} _ExtendSessionRequest_default_instance_;
class ExtendSessionResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ExtendSessionResponse>
      _instance;
} _ExtendSessionResponse_default_instance_;
class RunStepRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<RunStepRequest>
      _instance;
} _RunStepRequest_default_instance_;
class RunStepResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<RunStepResponse>
      _instance;
} _RunStepResponse_default_instance_;
class PartialRunSetupRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<PartialRunSetupRequest>
      _instance;
} _PartialRunSetupRequest_default_instance_;
class PartialRunSetupResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<PartialRunSetupResponse>
      _instance;
} _PartialRunSetupResponse_default_instance_;
class CloseSessionRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CloseSessionRequest>
      _instance;
} _CloseSessionRequest_default_instance_;
class CloseSessionResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CloseSessionResponse>
      _instance;
} _CloseSessionResponse_default_instance_;
class ResetRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ResetRequest>
      _instance;
} _ResetRequest_default_instance_;
class ResetResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ResetResponse>
      _instance;
} _ResetResponse_default_instance_;
class ListDevicesRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ListDevicesRequest>
      _instance;
} _ListDevicesRequest_default_instance_;
class ListDevicesResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ListDevicesResponse>
      _instance;
} _ListDevicesResponse_default_instance_;
class MakeCallableRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<MakeCallableRequest>
      _instance;
} _MakeCallableRequest_default_instance_;
class MakeCallableResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<MakeCallableResponse>
      _instance;
} _MakeCallableResponse_default_instance_;
class RunCallableRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<RunCallableRequest>
      _instance;
} _RunCallableRequest_default_instance_;
class RunCallableResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<RunCallableResponse>
      _instance;
} _RunCallableResponse_default_instance_;
class ReleaseCallableRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ReleaseCallableRequest>
      _instance;
} _ReleaseCallableRequest_default_instance_;
class ReleaseCallableResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ReleaseCallableResponse>
      _instance;
} _ReleaseCallableResponse_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto {
static void InitDefaultsCreateSessionRequest() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_CreateSessionRequest_default_instance_;
    new (ptr) ::tensorflow::CreateSessionRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::CreateSessionRequest::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_CreateSessionRequest =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsCreateSessionRequest}, {
      &protobuf_tensorflow_2fcore_2fframework_2fgraph_2eproto::scc_info_GraphDef.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_ConfigProto.base,}};

static void InitDefaultsCreateSessionResponse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_CreateSessionResponse_default_instance_;
    new (ptr) ::tensorflow::CreateSessionResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::CreateSessionResponse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_CreateSessionResponse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsCreateSessionResponse}, {}};

static void InitDefaultsExtendSessionRequest() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_ExtendSessionRequest_default_instance_;
    new (ptr) ::tensorflow::ExtendSessionRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::ExtendSessionRequest::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_ExtendSessionRequest =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsExtendSessionRequest}, {
      &protobuf_tensorflow_2fcore_2fframework_2fgraph_2eproto::scc_info_GraphDef.base,}};

static void InitDefaultsExtendSessionResponse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_ExtendSessionResponse_default_instance_;
    new (ptr) ::tensorflow::ExtendSessionResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::ExtendSessionResponse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_ExtendSessionResponse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsExtendSessionResponse}, {}};

static void InitDefaultsRunStepRequest() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_RunStepRequest_default_instance_;
    new (ptr) ::tensorflow::RunStepRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::RunStepRequest::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_RunStepRequest =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsRunStepRequest}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fnamed_5ftensor_2eproto::scc_info_NamedTensorProto.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_RunOptions.base,}};

static void InitDefaultsRunStepResponse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_RunStepResponse_default_instance_;
    new (ptr) ::tensorflow::RunStepResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::RunStepResponse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_RunStepResponse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsRunStepResponse}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fnamed_5ftensor_2eproto::scc_info_NamedTensorProto.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_RunMetadata.base,}};

static void InitDefaultsPartialRunSetupRequest() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_PartialRunSetupRequest_default_instance_;
    new (ptr) ::tensorflow::PartialRunSetupRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::PartialRunSetupRequest::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_PartialRunSetupRequest =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsPartialRunSetupRequest}, {}};

static void InitDefaultsPartialRunSetupResponse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_PartialRunSetupResponse_default_instance_;
    new (ptr) ::tensorflow::PartialRunSetupResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::PartialRunSetupResponse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_PartialRunSetupResponse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsPartialRunSetupResponse}, {}};

static void InitDefaultsCloseSessionRequest() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_CloseSessionRequest_default_instance_;
    new (ptr) ::tensorflow::CloseSessionRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::CloseSessionRequest::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_CloseSessionRequest =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsCloseSessionRequest}, {}};

static void InitDefaultsCloseSessionResponse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_CloseSessionResponse_default_instance_;
    new (ptr) ::tensorflow::CloseSessionResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::CloseSessionResponse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_CloseSessionResponse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsCloseSessionResponse}, {}};

static void InitDefaultsResetRequest() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_ResetRequest_default_instance_;
    new (ptr) ::tensorflow::ResetRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::ResetRequest::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_ResetRequest =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsResetRequest}, {}};

static void InitDefaultsResetResponse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_ResetResponse_default_instance_;
    new (ptr) ::tensorflow::ResetResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::ResetResponse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_ResetResponse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsResetResponse}, {}};

static void InitDefaultsListDevicesRequest() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_ListDevicesRequest_default_instance_;
    new (ptr) ::tensorflow::ListDevicesRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::ListDevicesRequest::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_ListDevicesRequest =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsListDevicesRequest}, {}};

static void InitDefaultsListDevicesResponse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_ListDevicesResponse_default_instance_;
    new (ptr) ::tensorflow::ListDevicesResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::ListDevicesResponse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_ListDevicesResponse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsListDevicesResponse}, {
      &protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::scc_info_DeviceAttributes.base,}};

static void InitDefaultsMakeCallableRequest() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_MakeCallableRequest_default_instance_;
    new (ptr) ::tensorflow::MakeCallableRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::MakeCallableRequest::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_MakeCallableRequest =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsMakeCallableRequest}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_CallableOptions.base,}};

static void InitDefaultsMakeCallableResponse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_MakeCallableResponse_default_instance_;
    new (ptr) ::tensorflow::MakeCallableResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::MakeCallableResponse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_MakeCallableResponse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsMakeCallableResponse}, {}};

static void InitDefaultsRunCallableRequest() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_RunCallableRequest_default_instance_;
    new (ptr) ::tensorflow::RunCallableRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::RunCallableRequest::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_RunCallableRequest =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsRunCallableRequest}, {
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::scc_info_TensorProto.base,}};

static void InitDefaultsRunCallableResponse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_RunCallableResponse_default_instance_;
    new (ptr) ::tensorflow::RunCallableResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::RunCallableResponse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_RunCallableResponse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsRunCallableResponse}, {
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::scc_info_TensorProto.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_RunMetadata.base,}};

static void InitDefaultsReleaseCallableRequest() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_ReleaseCallableRequest_default_instance_;
    new (ptr) ::tensorflow::ReleaseCallableRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::ReleaseCallableRequest::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_ReleaseCallableRequest =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsReleaseCallableRequest}, {}};

static void InitDefaultsReleaseCallableResponse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_ReleaseCallableResponse_default_instance_;
    new (ptr) ::tensorflow::ReleaseCallableResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::ReleaseCallableResponse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_ReleaseCallableResponse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsReleaseCallableResponse}, {}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_CreateSessionRequest.base);
  ::google::protobuf::internal::InitSCC(&scc_info_CreateSessionResponse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ExtendSessionRequest.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ExtendSessionResponse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_RunStepRequest.base);
  ::google::protobuf::internal::InitSCC(&scc_info_RunStepResponse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_PartialRunSetupRequest.base);
  ::google::protobuf::internal::InitSCC(&scc_info_PartialRunSetupResponse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_CloseSessionRequest.base);
  ::google::protobuf::internal::InitSCC(&scc_info_CloseSessionResponse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ResetRequest.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ResetResponse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ListDevicesRequest.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ListDevicesResponse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_MakeCallableRequest.base);
  ::google::protobuf::internal::InitSCC(&scc_info_MakeCallableResponse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_RunCallableRequest.base);
  ::google::protobuf::internal::InitSCC(&scc_info_RunCallableResponse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ReleaseCallableRequest.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ReleaseCallableResponse.base);
}

::google::protobuf::Metadata file_level_metadata[20];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CreateSessionRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CreateSessionRequest, graph_def_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CreateSessionRequest, config_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CreateSessionRequest, target_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CreateSessionResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CreateSessionResponse, session_handle_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CreateSessionResponse, graph_version_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ExtendSessionRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ExtendSessionRequest, session_handle_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ExtendSessionRequest, graph_def_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ExtendSessionRequest, current_graph_version_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ExtendSessionResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ExtendSessionResponse, new_graph_version_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunStepRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunStepRequest, session_handle_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunStepRequest, feed_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunStepRequest, fetch_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunStepRequest, target_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunStepRequest, options_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunStepRequest, partial_run_handle_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunStepRequest, store_errors_in_response_body_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunStepRequest, request_id_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunStepResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunStepResponse, tensor_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunStepResponse, metadata_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunStepResponse, status_code_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunStepResponse, status_error_message_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::PartialRunSetupRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::PartialRunSetupRequest, session_handle_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::PartialRunSetupRequest, feed_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::PartialRunSetupRequest, fetch_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::PartialRunSetupRequest, target_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::PartialRunSetupRequest, request_id_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::PartialRunSetupResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::PartialRunSetupResponse, partial_run_handle_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CloseSessionRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CloseSessionRequest, session_handle_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CloseSessionResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ResetRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ResetRequest, container_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ResetRequest, device_filters_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ResetResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ListDevicesRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ListDevicesRequest, session_handle_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ListDevicesResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ListDevicesResponse, local_device_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ListDevicesResponse, remote_device_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MakeCallableRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MakeCallableRequest, session_handle_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MakeCallableRequest, options_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MakeCallableRequest, request_id_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MakeCallableResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MakeCallableResponse, handle_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunCallableRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunCallableRequest, session_handle_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunCallableRequest, handle_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunCallableRequest, feed_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunCallableRequest, request_id_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunCallableResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunCallableResponse, fetch_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunCallableResponse, metadata_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ReleaseCallableRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ReleaseCallableRequest, session_handle_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ReleaseCallableRequest, handle_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ReleaseCallableResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::CreateSessionRequest)},
  { 8, -1, sizeof(::tensorflow::CreateSessionResponse)},
  { 15, -1, sizeof(::tensorflow::ExtendSessionRequest)},
  { 23, -1, sizeof(::tensorflow::ExtendSessionResponse)},
  { 29, -1, sizeof(::tensorflow::RunStepRequest)},
  { 42, -1, sizeof(::tensorflow::RunStepResponse)},
  { 51, -1, sizeof(::tensorflow::PartialRunSetupRequest)},
  { 61, -1, sizeof(::tensorflow::PartialRunSetupResponse)},
  { 67, -1, sizeof(::tensorflow::CloseSessionRequest)},
  { 73, -1, sizeof(::tensorflow::CloseSessionResponse)},
  { 78, -1, sizeof(::tensorflow::ResetRequest)},
  { 85, -1, sizeof(::tensorflow::ResetResponse)},
  { 90, -1, sizeof(::tensorflow::ListDevicesRequest)},
  { 96, -1, sizeof(::tensorflow::ListDevicesResponse)},
  { 103, -1, sizeof(::tensorflow::MakeCallableRequest)},
  { 111, -1, sizeof(::tensorflow::MakeCallableResponse)},
  { 117, -1, sizeof(::tensorflow::RunCallableRequest)},
  { 126, -1, sizeof(::tensorflow::RunCallableResponse)},
  { 133, -1, sizeof(::tensorflow::ReleaseCallableRequest)},
  { 140, -1, sizeof(::tensorflow::ReleaseCallableResponse)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_CreateSessionRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_CreateSessionResponse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_ExtendSessionRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_ExtendSessionResponse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_RunStepRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_RunStepResponse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_PartialRunSetupRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_PartialRunSetupResponse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_CloseSessionRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_CloseSessionResponse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_ResetRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_ResetResponse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_ListDevicesRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_ListDevicesResponse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_MakeCallableRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_MakeCallableResponse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_RunCallableRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_RunCallableResponse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_ReleaseCallableRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_ReleaseCallableResponse_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/protobuf/master.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 20);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n%tensorflow/core/protobuf/master.proto\022"
      "\ntensorflow\0321tensorflow/core/framework/d"
      "evice_attributes.proto\032%tensorflow/core/"
      "framework/graph.proto\032&tensorflow/core/f"
      "ramework/tensor.proto\032*tensorflow/core/l"
      "ib/core/error_codes.proto\032%tensorflow/co"
      "re/protobuf/config.proto\032+tensorflow/cor"
      "e/protobuf/named_tensor.proto\"x\n\024CreateS"
      "essionRequest\022\'\n\tgraph_def\030\001 \001(\0132\024.tenso"
      "rflow.GraphDef\022\'\n\006config\030\002 \001(\0132\027.tensorf"
      "low.ConfigProto\022\016\n\006target\030\003 \001(\t\"F\n\025Creat"
      "eSessionResponse\022\026\n\016session_handle\030\001 \001(\t"
      "\022\025\n\rgraph_version\030\002 \001(\003\"v\n\024ExtendSession"
      "Request\022\026\n\016session_handle\030\001 \001(\t\022\'\n\tgraph"
      "_def\030\002 \001(\0132\024.tensorflow.GraphDef\022\035\n\025curr"
      "ent_graph_version\030\003 \001(\003\"2\n\025ExtendSession"
      "Response\022\031\n\021new_graph_version\030\004 \001(\003\"\363\001\n\016"
      "RunStepRequest\022\026\n\016session_handle\030\001 \001(\t\022*"
      "\n\004feed\030\002 \003(\0132\034.tensorflow.NamedTensorPro"
      "to\022\r\n\005fetch\030\003 \003(\t\022\016\n\006target\030\004 \003(\t\022\'\n\007opt"
      "ions\030\005 \001(\0132\026.tensorflow.RunOptions\022\032\n\022pa"
      "rtial_run_handle\030\006 \001(\t\022%\n\035store_errors_i"
      "n_response_body\030\007 \001(\010\022\022\n\nrequest_id\030\010 \001("
      "\003\"\265\001\n\017RunStepResponse\022,\n\006tensor\030\001 \003(\0132\034."
      "tensorflow.NamedTensorProto\022)\n\010metadata\030"
      "\002 \001(\0132\027.tensorflow.RunMetadata\022+\n\013status"
      "_code\030\003 \001(\0162\026.tensorflow.error.Code\022\034\n\024s"
      "tatus_error_message\030\004 \001(\t\"q\n\026PartialRunS"
      "etupRequest\022\026\n\016session_handle\030\001 \001(\t\022\014\n\004f"
      "eed\030\002 \003(\t\022\r\n\005fetch\030\003 \003(\t\022\016\n\006target\030\004 \003(\t"
      "\022\022\n\nrequest_id\030\005 \001(\003\"5\n\027PartialRunSetupR"
      "esponse\022\032\n\022partial_run_handle\030\001 \001(\t\"-\n\023C"
      "loseSessionRequest\022\026\n\016session_handle\030\001 \001"
      "(\t\"\026\n\024CloseSessionResponse\"9\n\014ResetReque"
      "st\022\021\n\tcontainer\030\001 \003(\t\022\026\n\016device_filters\030"
      "\002 \003(\t\"\017\n\rResetResponse\",\n\022ListDevicesReq"
      "uest\022\026\n\016session_handle\030\001 \001(\t\"~\n\023ListDevi"
      "cesResponse\0222\n\014local_device\030\001 \003(\0132\034.tens"
      "orflow.DeviceAttributes\0223\n\rremote_device"
      "\030\002 \003(\0132\034.tensorflow.DeviceAttributes\"o\n\023"
      "MakeCallableRequest\022\026\n\016session_handle\030\001 "
      "\001(\t\022,\n\007options\030\002 \001(\0132\033.tensorflow.Callab"
      "leOptions\022\022\n\nrequest_id\030\003 \001(\003\"&\n\024MakeCal"
      "lableResponse\022\016\n\006handle\030\001 \001(\003\"w\n\022RunCall"
      "ableRequest\022\026\n\016session_handle\030\001 \001(\t\022\016\n\006h"
      "andle\030\002 \001(\003\022%\n\004feed\030\003 \003(\0132\027.tensorflow.T"
      "ensorProto\022\022\n\nrequest_id\030\004 \001(\003\"h\n\023RunCal"
      "lableResponse\022&\n\005fetch\030\001 \003(\0132\027.tensorflo"
      "w.TensorProto\022)\n\010metadata\030\002 \001(\0132\027.tensor"
      "flow.RunMetadata\"@\n\026ReleaseCallableReque"
      "st\022\026\n\016session_handle\030\001 \001(\t\022\016\n\006handle\030\002 \001"
      "(\003\"\031\n\027ReleaseCallableResponseB;\n\032org.ten"
      "sorflow.distruntimeB\030DistributedRuntimeP"
      "rotosP\001\370\001\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 2138);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/protobuf/master.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2fgraph_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2flib_2fcore_2ferror_5fcodes_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fnamed_5ftensor_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto
namespace tensorflow {

// ===================================================================

void CreateSessionRequest::InitAsDefaultInstance() {
  ::tensorflow::_CreateSessionRequest_default_instance_._instance.get_mutable()->graph_def_ = const_cast< ::tensorflow::GraphDef*>(
      ::tensorflow::GraphDef::internal_default_instance());
  ::tensorflow::_CreateSessionRequest_default_instance_._instance.get_mutable()->config_ = const_cast< ::tensorflow::ConfigProto*>(
      ::tensorflow::ConfigProto::internal_default_instance());
}
void CreateSessionRequest::unsafe_arena_set_allocated_graph_def(
    ::tensorflow::GraphDef* graph_def) {
  if (GetArenaNoVirtual() == NULL) {
    delete graph_def_;
  }
  graph_def_ = graph_def;
  if (graph_def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CreateSessionRequest.graph_def)
}
void CreateSessionRequest::clear_graph_def() {
  if (GetArenaNoVirtual() == NULL && graph_def_ != NULL) {
    delete graph_def_;
  }
  graph_def_ = NULL;
}
void CreateSessionRequest::unsafe_arena_set_allocated_config(
    ::tensorflow::ConfigProto* config) {
  if (GetArenaNoVirtual() == NULL) {
    delete config_;
  }
  config_ = config;
  if (config) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CreateSessionRequest.config)
}
void CreateSessionRequest::clear_config() {
  if (GetArenaNoVirtual() == NULL && config_ != NULL) {
    delete config_;
  }
  config_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CreateSessionRequest::kGraphDefFieldNumber;
const int CreateSessionRequest::kConfigFieldNumber;
const int CreateSessionRequest::kTargetFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CreateSessionRequest::CreateSessionRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_CreateSessionRequest.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.CreateSessionRequest)
}
CreateSessionRequest::CreateSessionRequest(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_CreateSessionRequest.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.CreateSessionRequest)
}
CreateSessionRequest::CreateSessionRequest(const CreateSessionRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  target_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.target().size() > 0) {
    target_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.target(),
      GetArenaNoVirtual());
  }
  if (from.has_graph_def()) {
    graph_def_ = new ::tensorflow::GraphDef(*from.graph_def_);
  } else {
    graph_def_ = NULL;
  }
  if (from.has_config()) {
    config_ = new ::tensorflow::ConfigProto(*from.config_);
  } else {
    config_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.CreateSessionRequest)
}

void CreateSessionRequest::SharedCtor() {
  target_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&graph_def_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&config_) -
      reinterpret_cast<char*>(&graph_def_)) + sizeof(config_));
}

CreateSessionRequest::~CreateSessionRequest() {
  // @@protoc_insertion_point(destructor:tensorflow.CreateSessionRequest)
  SharedDtor();
}

void CreateSessionRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  target_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete graph_def_;
  if (this != internal_default_instance()) delete config_;
}

void CreateSessionRequest::ArenaDtor(void* object) {
  CreateSessionRequest* _this = reinterpret_cast< CreateSessionRequest* >(object);
  (void)_this;
}
void CreateSessionRequest::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void CreateSessionRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* CreateSessionRequest::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CreateSessionRequest& CreateSessionRequest::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_CreateSessionRequest.base);
  return *internal_default_instance();
}


void CreateSessionRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.CreateSessionRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  target_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && graph_def_ != NULL) {
    delete graph_def_;
  }
  graph_def_ = NULL;
  if (GetArenaNoVirtual() == NULL && config_ != NULL) {
    delete config_;
  }
  config_ = NULL;
  _internal_metadata_.Clear();
}

bool CreateSessionRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.CreateSessionRequest)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.GraphDef graph_def = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_graph_def()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.ConfigProto config = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_config()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string target = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_target()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->target().data(), static_cast<int>(this->target().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.CreateSessionRequest.target"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.CreateSessionRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.CreateSessionRequest)
  return false;
#undef DO_
}

void CreateSessionRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.CreateSessionRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.GraphDef graph_def = 1;
  if (this->has_graph_def()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->_internal_graph_def(), output);
  }

  // .tensorflow.ConfigProto config = 2;
  if (this->has_config()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_config(), output);
  }

  // string target = 3;
  if (this->target().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->target().data(), static_cast<int>(this->target().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CreateSessionRequest.target");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->target(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.CreateSessionRequest)
}

::google::protobuf::uint8* CreateSessionRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.CreateSessionRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.GraphDef graph_def = 1;
  if (this->has_graph_def()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->_internal_graph_def(), deterministic, target);
  }

  // .tensorflow.ConfigProto config = 2;
  if (this->has_config()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_config(), deterministic, target);
  }

  // string target = 3;
  if (this->target().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->target().data(), static_cast<int>(this->target().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CreateSessionRequest.target");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->target(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.CreateSessionRequest)
  return target;
}

size_t CreateSessionRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.CreateSessionRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string target = 3;
  if (this->target().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->target());
  }

  // .tensorflow.GraphDef graph_def = 1;
  if (this->has_graph_def()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *graph_def_);
  }

  // .tensorflow.ConfigProto config = 2;
  if (this->has_config()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *config_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CreateSessionRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.CreateSessionRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const CreateSessionRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CreateSessionRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.CreateSessionRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.CreateSessionRequest)
    MergeFrom(*source);
  }
}

void CreateSessionRequest::MergeFrom(const CreateSessionRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.CreateSessionRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.target().size() > 0) {
    set_target(from.target());
  }
  if (from.has_graph_def()) {
    mutable_graph_def()->::tensorflow::GraphDef::MergeFrom(from.graph_def());
  }
  if (from.has_config()) {
    mutable_config()->::tensorflow::ConfigProto::MergeFrom(from.config());
  }
}

void CreateSessionRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.CreateSessionRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CreateSessionRequest::CopyFrom(const CreateSessionRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.CreateSessionRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CreateSessionRequest::IsInitialized() const {
  return true;
}

void CreateSessionRequest::Swap(CreateSessionRequest* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    CreateSessionRequest* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void CreateSessionRequest::UnsafeArenaSwap(CreateSessionRequest* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void CreateSessionRequest::InternalSwap(CreateSessionRequest* other) {
  using std::swap;
  target_.Swap(&other->target_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(graph_def_, other->graph_def_);
  swap(config_, other->config_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata CreateSessionRequest::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void CreateSessionResponse::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CreateSessionResponse::kSessionHandleFieldNumber;
const int CreateSessionResponse::kGraphVersionFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CreateSessionResponse::CreateSessionResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_CreateSessionResponse.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.CreateSessionResponse)
}
CreateSessionResponse::CreateSessionResponse(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_CreateSessionResponse.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.CreateSessionResponse)
}
CreateSessionResponse::CreateSessionResponse(const CreateSessionResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  session_handle_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.session_handle().size() > 0) {
    session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.session_handle(),
      GetArenaNoVirtual());
  }
  graph_version_ = from.graph_version_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.CreateSessionResponse)
}

void CreateSessionResponse::SharedCtor() {
  session_handle_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  graph_version_ = GOOGLE_LONGLONG(0);
}

CreateSessionResponse::~CreateSessionResponse() {
  // @@protoc_insertion_point(destructor:tensorflow.CreateSessionResponse)
  SharedDtor();
}

void CreateSessionResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  session_handle_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void CreateSessionResponse::ArenaDtor(void* object) {
  CreateSessionResponse* _this = reinterpret_cast< CreateSessionResponse* >(object);
  (void)_this;
}
void CreateSessionResponse::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void CreateSessionResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* CreateSessionResponse::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CreateSessionResponse& CreateSessionResponse::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_CreateSessionResponse.base);
  return *internal_default_instance();
}


void CreateSessionResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.CreateSessionResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  session_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  graph_version_ = GOOGLE_LONGLONG(0);
  _internal_metadata_.Clear();
}

bool CreateSessionResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.CreateSessionResponse)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string session_handle = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_session_handle()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->session_handle().data(), static_cast<int>(this->session_handle().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.CreateSessionResponse.session_handle"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 graph_version = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &graph_version_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.CreateSessionResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.CreateSessionResponse)
  return false;
#undef DO_
}

void CreateSessionResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.CreateSessionResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string session_handle = 1;
  if (this->session_handle().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->session_handle().data(), static_cast<int>(this->session_handle().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CreateSessionResponse.session_handle");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->session_handle(), output);
  }

  // int64 graph_version = 2;
  if (this->graph_version() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->graph_version(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.CreateSessionResponse)
}

::google::protobuf::uint8* CreateSessionResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.CreateSessionResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string session_handle = 1;
  if (this->session_handle().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->session_handle().data(), static_cast<int>(this->session_handle().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CreateSessionResponse.session_handle");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->session_handle(), target);
  }

  // int64 graph_version = 2;
  if (this->graph_version() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->graph_version(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.CreateSessionResponse)
  return target;
}

size_t CreateSessionResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.CreateSessionResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string session_handle = 1;
  if (this->session_handle().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->session_handle());
  }

  // int64 graph_version = 2;
  if (this->graph_version() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->graph_version());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CreateSessionResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.CreateSessionResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const CreateSessionResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CreateSessionResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.CreateSessionResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.CreateSessionResponse)
    MergeFrom(*source);
  }
}

void CreateSessionResponse::MergeFrom(const CreateSessionResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.CreateSessionResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.session_handle().size() > 0) {
    set_session_handle(from.session_handle());
  }
  if (from.graph_version() != 0) {
    set_graph_version(from.graph_version());
  }
}

void CreateSessionResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.CreateSessionResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CreateSessionResponse::CopyFrom(const CreateSessionResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.CreateSessionResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CreateSessionResponse::IsInitialized() const {
  return true;
}

void CreateSessionResponse::Swap(CreateSessionResponse* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    CreateSessionResponse* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void CreateSessionResponse::UnsafeArenaSwap(CreateSessionResponse* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void CreateSessionResponse::InternalSwap(CreateSessionResponse* other) {
  using std::swap;
  session_handle_.Swap(&other->session_handle_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(graph_version_, other->graph_version_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata CreateSessionResponse::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ExtendSessionRequest::InitAsDefaultInstance() {
  ::tensorflow::_ExtendSessionRequest_default_instance_._instance.get_mutable()->graph_def_ = const_cast< ::tensorflow::GraphDef*>(
      ::tensorflow::GraphDef::internal_default_instance());
}
void ExtendSessionRequest::unsafe_arena_set_allocated_graph_def(
    ::tensorflow::GraphDef* graph_def) {
  if (GetArenaNoVirtual() == NULL) {
    delete graph_def_;
  }
  graph_def_ = graph_def;
  if (graph_def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ExtendSessionRequest.graph_def)
}
void ExtendSessionRequest::clear_graph_def() {
  if (GetArenaNoVirtual() == NULL && graph_def_ != NULL) {
    delete graph_def_;
  }
  graph_def_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ExtendSessionRequest::kSessionHandleFieldNumber;
const int ExtendSessionRequest::kGraphDefFieldNumber;
const int ExtendSessionRequest::kCurrentGraphVersionFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ExtendSessionRequest::ExtendSessionRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_ExtendSessionRequest.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.ExtendSessionRequest)
}
ExtendSessionRequest::ExtendSessionRequest(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_ExtendSessionRequest.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.ExtendSessionRequest)
}
ExtendSessionRequest::ExtendSessionRequest(const ExtendSessionRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  session_handle_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.session_handle().size() > 0) {
    session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.session_handle(),
      GetArenaNoVirtual());
  }
  if (from.has_graph_def()) {
    graph_def_ = new ::tensorflow::GraphDef(*from.graph_def_);
  } else {
    graph_def_ = NULL;
  }
  current_graph_version_ = from.current_graph_version_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.ExtendSessionRequest)
}

void ExtendSessionRequest::SharedCtor() {
  session_handle_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&graph_def_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&current_graph_version_) -
      reinterpret_cast<char*>(&graph_def_)) + sizeof(current_graph_version_));
}

ExtendSessionRequest::~ExtendSessionRequest() {
  // @@protoc_insertion_point(destructor:tensorflow.ExtendSessionRequest)
  SharedDtor();
}

void ExtendSessionRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  session_handle_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete graph_def_;
}

void ExtendSessionRequest::ArenaDtor(void* object) {
  ExtendSessionRequest* _this = reinterpret_cast< ExtendSessionRequest* >(object);
  (void)_this;
}
void ExtendSessionRequest::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void ExtendSessionRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ExtendSessionRequest::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ExtendSessionRequest& ExtendSessionRequest::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_ExtendSessionRequest.base);
  return *internal_default_instance();
}


void ExtendSessionRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.ExtendSessionRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  session_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && graph_def_ != NULL) {
    delete graph_def_;
  }
  graph_def_ = NULL;
  current_graph_version_ = GOOGLE_LONGLONG(0);
  _internal_metadata_.Clear();
}

bool ExtendSessionRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.ExtendSessionRequest)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string session_handle = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_session_handle()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->session_handle().data(), static_cast<int>(this->session_handle().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ExtendSessionRequest.session_handle"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.GraphDef graph_def = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_graph_def()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 current_graph_version = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &current_graph_version_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.ExtendSessionRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.ExtendSessionRequest)
  return false;
#undef DO_
}

void ExtendSessionRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.ExtendSessionRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string session_handle = 1;
  if (this->session_handle().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->session_handle().data(), static_cast<int>(this->session_handle().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ExtendSessionRequest.session_handle");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->session_handle(), output);
  }

  // .tensorflow.GraphDef graph_def = 2;
  if (this->has_graph_def()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_graph_def(), output);
  }

  // int64 current_graph_version = 3;
  if (this->current_graph_version() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->current_graph_version(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.ExtendSessionRequest)
}

::google::protobuf::uint8* ExtendSessionRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.ExtendSessionRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string session_handle = 1;
  if (this->session_handle().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->session_handle().data(), static_cast<int>(this->session_handle().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ExtendSessionRequest.session_handle");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->session_handle(), target);
  }

  // .tensorflow.GraphDef graph_def = 2;
  if (this->has_graph_def()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_graph_def(), deterministic, target);
  }

  // int64 current_graph_version = 3;
  if (this->current_graph_version() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->current_graph_version(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.ExtendSessionRequest)
  return target;
}

size_t ExtendSessionRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.ExtendSessionRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string session_handle = 1;
  if (this->session_handle().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->session_handle());
  }

  // .tensorflow.GraphDef graph_def = 2;
  if (this->has_graph_def()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *graph_def_);
  }

  // int64 current_graph_version = 3;
  if (this->current_graph_version() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->current_graph_version());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ExtendSessionRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.ExtendSessionRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const ExtendSessionRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ExtendSessionRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.ExtendSessionRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.ExtendSessionRequest)
    MergeFrom(*source);
  }
}

void ExtendSessionRequest::MergeFrom(const ExtendSessionRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.ExtendSessionRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.session_handle().size() > 0) {
    set_session_handle(from.session_handle());
  }
  if (from.has_graph_def()) {
    mutable_graph_def()->::tensorflow::GraphDef::MergeFrom(from.graph_def());
  }
  if (from.current_graph_version() != 0) {
    set_current_graph_version(from.current_graph_version());
  }
}

void ExtendSessionRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.ExtendSessionRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ExtendSessionRequest::CopyFrom(const ExtendSessionRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.ExtendSessionRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ExtendSessionRequest::IsInitialized() const {
  return true;
}

void ExtendSessionRequest::Swap(ExtendSessionRequest* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    ExtendSessionRequest* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void ExtendSessionRequest::UnsafeArenaSwap(ExtendSessionRequest* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void ExtendSessionRequest::InternalSwap(ExtendSessionRequest* other) {
  using std::swap;
  session_handle_.Swap(&other->session_handle_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(graph_def_, other->graph_def_);
  swap(current_graph_version_, other->current_graph_version_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ExtendSessionRequest::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ExtendSessionResponse::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ExtendSessionResponse::kNewGraphVersionFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ExtendSessionResponse::ExtendSessionResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_ExtendSessionResponse.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.ExtendSessionResponse)
}
ExtendSessionResponse::ExtendSessionResponse(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_ExtendSessionResponse.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.ExtendSessionResponse)
}
ExtendSessionResponse::ExtendSessionResponse(const ExtendSessionResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  new_graph_version_ = from.new_graph_version_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.ExtendSessionResponse)
}

void ExtendSessionResponse::SharedCtor() {
  new_graph_version_ = GOOGLE_LONGLONG(0);
}

ExtendSessionResponse::~ExtendSessionResponse() {
  // @@protoc_insertion_point(destructor:tensorflow.ExtendSessionResponse)
  SharedDtor();
}

void ExtendSessionResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void ExtendSessionResponse::ArenaDtor(void* object) {
  ExtendSessionResponse* _this = reinterpret_cast< ExtendSessionResponse* >(object);
  (void)_this;
}
void ExtendSessionResponse::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void ExtendSessionResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ExtendSessionResponse::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ExtendSessionResponse& ExtendSessionResponse::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_ExtendSessionResponse.base);
  return *internal_default_instance();
}


void ExtendSessionResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.ExtendSessionResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  new_graph_version_ = GOOGLE_LONGLONG(0);
  _internal_metadata_.Clear();
}

bool ExtendSessionResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.ExtendSessionResponse)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 new_graph_version = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &new_graph_version_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.ExtendSessionResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.ExtendSessionResponse)
  return false;
#undef DO_
}

void ExtendSessionResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.ExtendSessionResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 new_graph_version = 4;
  if (this->new_graph_version() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->new_graph_version(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.ExtendSessionResponse)
}

::google::protobuf::uint8* ExtendSessionResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.ExtendSessionResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 new_graph_version = 4;
  if (this->new_graph_version() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->new_graph_version(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.ExtendSessionResponse)
  return target;
}

size_t ExtendSessionResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.ExtendSessionResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int64 new_graph_version = 4;
  if (this->new_graph_version() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->new_graph_version());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ExtendSessionResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.ExtendSessionResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const ExtendSessionResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ExtendSessionResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.ExtendSessionResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.ExtendSessionResponse)
    MergeFrom(*source);
  }
}

void ExtendSessionResponse::MergeFrom(const ExtendSessionResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.ExtendSessionResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.new_graph_version() != 0) {
    set_new_graph_version(from.new_graph_version());
  }
}

void ExtendSessionResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.ExtendSessionResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ExtendSessionResponse::CopyFrom(const ExtendSessionResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.ExtendSessionResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ExtendSessionResponse::IsInitialized() const {
  return true;
}

void ExtendSessionResponse::Swap(ExtendSessionResponse* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    ExtendSessionResponse* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void ExtendSessionResponse::UnsafeArenaSwap(ExtendSessionResponse* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void ExtendSessionResponse::InternalSwap(ExtendSessionResponse* other) {
  using std::swap;
  swap(new_graph_version_, other->new_graph_version_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ExtendSessionResponse::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void RunStepRequest::InitAsDefaultInstance() {
  ::tensorflow::_RunStepRequest_default_instance_._instance.get_mutable()->options_ = const_cast< ::tensorflow::RunOptions*>(
      ::tensorflow::RunOptions::internal_default_instance());
}
void RunStepRequest::clear_feed() {
  feed_.Clear();
}
void RunStepRequest::unsafe_arena_set_allocated_options(
    ::tensorflow::RunOptions* options) {
  if (GetArenaNoVirtual() == NULL) {
    delete options_;
  }
  options_ = options;
  if (options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunStepRequest.options)
}
void RunStepRequest::clear_options() {
  if (GetArenaNoVirtual() == NULL && options_ != NULL) {
    delete options_;
  }
  options_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RunStepRequest::kSessionHandleFieldNumber;
const int RunStepRequest::kFeedFieldNumber;
const int RunStepRequest::kFetchFieldNumber;
const int RunStepRequest::kTargetFieldNumber;
const int RunStepRequest::kOptionsFieldNumber;
const int RunStepRequest::kPartialRunHandleFieldNumber;
const int RunStepRequest::kStoreErrorsInResponseBodyFieldNumber;
const int RunStepRequest::kRequestIdFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RunStepRequest::RunStepRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_RunStepRequest.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.RunStepRequest)
}
RunStepRequest::RunStepRequest(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  feed_(arena),
  fetch_(arena),
  target_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_RunStepRequest.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.RunStepRequest)
}
RunStepRequest::RunStepRequest(const RunStepRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      feed_(from.feed_),
      fetch_(from.fetch_),
      target_(from.target_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  session_handle_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.session_handle().size() > 0) {
    session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.session_handle(),
      GetArenaNoVirtual());
  }
  partial_run_handle_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.partial_run_handle().size() > 0) {
    partial_run_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.partial_run_handle(),
      GetArenaNoVirtual());
  }
  if (from.has_options()) {
    options_ = new ::tensorflow::RunOptions(*from.options_);
  } else {
    options_ = NULL;
  }
  ::memcpy(&request_id_, &from.request_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&store_errors_in_response_body_) -
    reinterpret_cast<char*>(&request_id_)) + sizeof(store_errors_in_response_body_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.RunStepRequest)
}

void RunStepRequest::SharedCtor() {
  session_handle_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  partial_run_handle_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&options_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&store_errors_in_response_body_) -
      reinterpret_cast<char*>(&options_)) + sizeof(store_errors_in_response_body_));
}

RunStepRequest::~RunStepRequest() {
  // @@protoc_insertion_point(destructor:tensorflow.RunStepRequest)
  SharedDtor();
}

void RunStepRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  session_handle_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  partial_run_handle_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete options_;
}

void RunStepRequest::ArenaDtor(void* object) {
  RunStepRequest* _this = reinterpret_cast< RunStepRequest* >(object);
  (void)_this;
}
void RunStepRequest::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void RunStepRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* RunStepRequest::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const RunStepRequest& RunStepRequest::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_RunStepRequest.base);
  return *internal_default_instance();
}


void RunStepRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.RunStepRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  feed_.Clear();
  fetch_.Clear();
  target_.Clear();
  session_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  partial_run_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && options_ != NULL) {
    delete options_;
  }
  options_ = NULL;
  ::memset(&request_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&store_errors_in_response_body_) -
      reinterpret_cast<char*>(&request_id_)) + sizeof(store_errors_in_response_body_));
  _internal_metadata_.Clear();
}

bool RunStepRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.RunStepRequest)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string session_handle = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_session_handle()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->session_handle().data(), static_cast<int>(this->session_handle().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.RunStepRequest.session_handle"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.NamedTensorProto feed = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_feed()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string fetch = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_fetch()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->fetch(this->fetch_size() - 1).data(),
            static_cast<int>(this->fetch(this->fetch_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.RunStepRequest.fetch"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string target = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_target()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->target(this->target_size() - 1).data(),
            static_cast<int>(this->target(this->target_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.RunStepRequest.target"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.RunOptions options = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_options()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string partial_run_handle = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_partial_run_handle()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->partial_run_handle().data(), static_cast<int>(this->partial_run_handle().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.RunStepRequest.partial_run_handle"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool store_errors_in_response_body = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(56u /* 56 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &store_errors_in_response_body_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 request_id = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(64u /* 64 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &request_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.RunStepRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.RunStepRequest)
  return false;
#undef DO_
}

void RunStepRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.RunStepRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string session_handle = 1;
  if (this->session_handle().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->session_handle().data(), static_cast<int>(this->session_handle().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.RunStepRequest.session_handle");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->session_handle(), output);
  }

  // repeated .tensorflow.NamedTensorProto feed = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->feed_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2,
      this->feed(static_cast<int>(i)),
      output);
  }

  // repeated string fetch = 3;
  for (int i = 0, n = this->fetch_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fetch(i).data(), static_cast<int>(this->fetch(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.RunStepRequest.fetch");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      3, this->fetch(i), output);
  }

  // repeated string target = 4;
  for (int i = 0, n = this->target_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->target(i).data(), static_cast<int>(this->target(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.RunStepRequest.target");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      4, this->target(i), output);
  }

  // .tensorflow.RunOptions options = 5;
  if (this->has_options()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, this->_internal_options(), output);
  }

  // string partial_run_handle = 6;
  if (this->partial_run_handle().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->partial_run_handle().data(), static_cast<int>(this->partial_run_handle().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.RunStepRequest.partial_run_handle");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      6, this->partial_run_handle(), output);
  }

  // bool store_errors_in_response_body = 7;
  if (this->store_errors_in_response_body() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(7, this->store_errors_in_response_body(), output);
  }

  // int64 request_id = 8;
  if (this->request_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->request_id(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.RunStepRequest)
}

::google::protobuf::uint8* RunStepRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.RunStepRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string session_handle = 1;
  if (this->session_handle().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->session_handle().data(), static_cast<int>(this->session_handle().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.RunStepRequest.session_handle");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->session_handle(), target);
  }

  // repeated .tensorflow.NamedTensorProto feed = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->feed_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->feed(static_cast<int>(i)), deterministic, target);
  }

  // repeated string fetch = 3;
  for (int i = 0, n = this->fetch_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fetch(i).data(), static_cast<int>(this->fetch(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.RunStepRequest.fetch");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(3, this->fetch(i), target);
  }

  // repeated string target = 4;
  for (int i = 0, n = this->target_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->target(i).data(), static_cast<int>(this->target(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.RunStepRequest.target");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(4, this->target(i), target);
  }

  // .tensorflow.RunOptions options = 5;
  if (this->has_options()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        5, this->_internal_options(), deterministic, target);
  }

  // string partial_run_handle = 6;
  if (this->partial_run_handle().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->partial_run_handle().data(), static_cast<int>(this->partial_run_handle().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.RunStepRequest.partial_run_handle");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        6, this->partial_run_handle(), target);
  }

  // bool store_errors_in_response_body = 7;
  if (this->store_errors_in_response_body() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(7, this->store_errors_in_response_body(), target);
  }

  // int64 request_id = 8;
  if (this->request_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->request_id(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.RunStepRequest)
  return target;
}

size_t RunStepRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.RunStepRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.NamedTensorProto feed = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->feed_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->feed(static_cast<int>(i)));
    }
  }

  // repeated string fetch = 3;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->fetch_size());
  for (int i = 0, n = this->fetch_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->fetch(i));
  }

  // repeated string target = 4;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->target_size());
  for (int i = 0, n = this->target_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->target(i));
  }

  // string session_handle = 1;
  if (this->session_handle().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->session_handle());
  }

  // string partial_run_handle = 6;
  if (this->partial_run_handle().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->partial_run_handle());
  }

  // .tensorflow.RunOptions options = 5;
  if (this->has_options()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *options_);
  }

  // int64 request_id = 8;
  if (this->request_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->request_id());
  }

  // bool store_errors_in_response_body = 7;
  if (this->store_errors_in_response_body() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RunStepRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.RunStepRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const RunStepRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RunStepRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.RunStepRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.RunStepRequest)
    MergeFrom(*source);
  }
}

void RunStepRequest::MergeFrom(const RunStepRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.RunStepRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  feed_.MergeFrom(from.feed_);
  fetch_.MergeFrom(from.fetch_);
  target_.MergeFrom(from.target_);
  if (from.session_handle().size() > 0) {
    set_session_handle(from.session_handle());
  }
  if (from.partial_run_handle().size() > 0) {
    set_partial_run_handle(from.partial_run_handle());
  }
  if (from.has_options()) {
    mutable_options()->::tensorflow::RunOptions::MergeFrom(from.options());
  }
  if (from.request_id() != 0) {
    set_request_id(from.request_id());
  }
  if (from.store_errors_in_response_body() != 0) {
    set_store_errors_in_response_body(from.store_errors_in_response_body());
  }
}

void RunStepRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.RunStepRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RunStepRequest::CopyFrom(const RunStepRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.RunStepRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RunStepRequest::IsInitialized() const {
  return true;
}

void RunStepRequest::Swap(RunStepRequest* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    RunStepRequest* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void RunStepRequest::UnsafeArenaSwap(RunStepRequest* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void RunStepRequest::InternalSwap(RunStepRequest* other) {
  using std::swap;
  CastToBase(&feed_)->InternalSwap(CastToBase(&other->feed_));
  fetch_.InternalSwap(CastToBase(&other->fetch_));
  target_.InternalSwap(CastToBase(&other->target_));
  session_handle_.Swap(&other->session_handle_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  partial_run_handle_.Swap(&other->partial_run_handle_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(options_, other->options_);
  swap(request_id_, other->request_id_);
  swap(store_errors_in_response_body_, other->store_errors_in_response_body_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata RunStepRequest::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void RunStepResponse::InitAsDefaultInstance() {
  ::tensorflow::_RunStepResponse_default_instance_._instance.get_mutable()->metadata_ = const_cast< ::tensorflow::RunMetadata*>(
      ::tensorflow::RunMetadata::internal_default_instance());
}
void RunStepResponse::clear_tensor() {
  tensor_.Clear();
}
void RunStepResponse::unsafe_arena_set_allocated_metadata(
    ::tensorflow::RunMetadata* metadata) {
  if (GetArenaNoVirtual() == NULL) {
    delete metadata_;
  }
  metadata_ = metadata;
  if (metadata) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunStepResponse.metadata)
}
void RunStepResponse::clear_metadata() {
  if (GetArenaNoVirtual() == NULL && metadata_ != NULL) {
    delete metadata_;
  }
  metadata_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RunStepResponse::kTensorFieldNumber;
const int RunStepResponse::kMetadataFieldNumber;
const int RunStepResponse::kStatusCodeFieldNumber;
const int RunStepResponse::kStatusErrorMessageFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RunStepResponse::RunStepResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_RunStepResponse.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.RunStepResponse)
}
RunStepResponse::RunStepResponse(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  tensor_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_RunStepResponse.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.RunStepResponse)
}
RunStepResponse::RunStepResponse(const RunStepResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      tensor_(from.tensor_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  status_error_message_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.status_error_message().size() > 0) {
    status_error_message_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.status_error_message(),
      GetArenaNoVirtual());
  }
  if (from.has_metadata()) {
    metadata_ = new ::tensorflow::RunMetadata(*from.metadata_);
  } else {
    metadata_ = NULL;
  }
  status_code_ = from.status_code_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.RunStepResponse)
}

void RunStepResponse::SharedCtor() {
  status_error_message_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&metadata_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&status_code_) -
      reinterpret_cast<char*>(&metadata_)) + sizeof(status_code_));
}

RunStepResponse::~RunStepResponse() {
  // @@protoc_insertion_point(destructor:tensorflow.RunStepResponse)
  SharedDtor();
}

void RunStepResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  status_error_message_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete metadata_;
}

void RunStepResponse::ArenaDtor(void* object) {
  RunStepResponse* _this = reinterpret_cast< RunStepResponse* >(object);
  (void)_this;
}
void RunStepResponse::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void RunStepResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* RunStepResponse::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const RunStepResponse& RunStepResponse::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_RunStepResponse.base);
  return *internal_default_instance();
}


void RunStepResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.RunStepResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  tensor_.Clear();
  status_error_message_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && metadata_ != NULL) {
    delete metadata_;
  }
  metadata_ = NULL;
  status_code_ = 0;
  _internal_metadata_.Clear();
}

bool RunStepResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.RunStepResponse)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.NamedTensorProto tensor = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_tensor()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.RunMetadata metadata = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_metadata()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.error.Code status_code = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_status_code(static_cast< ::tensorflow::error::Code >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string status_error_message = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_status_error_message()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->status_error_message().data(), static_cast<int>(this->status_error_message().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.RunStepResponse.status_error_message"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.RunStepResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.RunStepResponse)
  return false;
#undef DO_
}

void RunStepResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.RunStepResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.NamedTensorProto tensor = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->tensor_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->tensor(static_cast<int>(i)),
      output);
  }

  // .tensorflow.RunMetadata metadata = 2;
  if (this->has_metadata()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_metadata(), output);
  }

  // .tensorflow.error.Code status_code = 3;
  if (this->status_code() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      3, this->status_code(), output);
  }

  // string status_error_message = 4;
  if (this->status_error_message().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->status_error_message().data(), static_cast<int>(this->status_error_message().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.RunStepResponse.status_error_message");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->status_error_message(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.RunStepResponse)
}

::google::protobuf::uint8* RunStepResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.RunStepResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.NamedTensorProto tensor = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->tensor_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->tensor(static_cast<int>(i)), deterministic, target);
  }

  // .tensorflow.RunMetadata metadata = 2;
  if (this->has_metadata()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_metadata(), deterministic, target);
  }

  // .tensorflow.error.Code status_code = 3;
  if (this->status_code() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      3, this->status_code(), target);
  }

  // string status_error_message = 4;
  if (this->status_error_message().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->status_error_message().data(), static_cast<int>(this->status_error_message().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.RunStepResponse.status_error_message");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->status_error_message(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.RunStepResponse)
  return target;
}

size_t RunStepResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.RunStepResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.NamedTensorProto tensor = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->tensor_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->tensor(static_cast<int>(i)));
    }
  }

  // string status_error_message = 4;
  if (this->status_error_message().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->status_error_message());
  }

  // .tensorflow.RunMetadata metadata = 2;
  if (this->has_metadata()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *metadata_);
  }

  // .tensorflow.error.Code status_code = 3;
  if (this->status_code() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->status_code());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RunStepResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.RunStepResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const RunStepResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RunStepResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.RunStepResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.RunStepResponse)
    MergeFrom(*source);
  }
}

void RunStepResponse::MergeFrom(const RunStepResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.RunStepResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  tensor_.MergeFrom(from.tensor_);
  if (from.status_error_message().size() > 0) {
    set_status_error_message(from.status_error_message());
  }
  if (from.has_metadata()) {
    mutable_metadata()->::tensorflow::RunMetadata::MergeFrom(from.metadata());
  }
  if (from.status_code() != 0) {
    set_status_code(from.status_code());
  }
}

void RunStepResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.RunStepResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RunStepResponse::CopyFrom(const RunStepResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.RunStepResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RunStepResponse::IsInitialized() const {
  return true;
}

void RunStepResponse::Swap(RunStepResponse* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    RunStepResponse* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void RunStepResponse::UnsafeArenaSwap(RunStepResponse* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void RunStepResponse::InternalSwap(RunStepResponse* other) {
  using std::swap;
  CastToBase(&tensor_)->InternalSwap(CastToBase(&other->tensor_));
  status_error_message_.Swap(&other->status_error_message_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(metadata_, other->metadata_);
  swap(status_code_, other->status_code_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata RunStepResponse::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void PartialRunSetupRequest::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int PartialRunSetupRequest::kSessionHandleFieldNumber;
const int PartialRunSetupRequest::kFeedFieldNumber;
const int PartialRunSetupRequest::kFetchFieldNumber;
const int PartialRunSetupRequest::kTargetFieldNumber;
const int PartialRunSetupRequest::kRequestIdFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

PartialRunSetupRequest::PartialRunSetupRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_PartialRunSetupRequest.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.PartialRunSetupRequest)
}
PartialRunSetupRequest::PartialRunSetupRequest(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  feed_(arena),
  fetch_(arena),
  target_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_PartialRunSetupRequest.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.PartialRunSetupRequest)
}
PartialRunSetupRequest::PartialRunSetupRequest(const PartialRunSetupRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      feed_(from.feed_),
      fetch_(from.fetch_),
      target_(from.target_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  session_handle_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.session_handle().size() > 0) {
    session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.session_handle(),
      GetArenaNoVirtual());
  }
  request_id_ = from.request_id_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.PartialRunSetupRequest)
}

void PartialRunSetupRequest::SharedCtor() {
  session_handle_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  request_id_ = GOOGLE_LONGLONG(0);
}

PartialRunSetupRequest::~PartialRunSetupRequest() {
  // @@protoc_insertion_point(destructor:tensorflow.PartialRunSetupRequest)
  SharedDtor();
}

void PartialRunSetupRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  session_handle_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void PartialRunSetupRequest::ArenaDtor(void* object) {
  PartialRunSetupRequest* _this = reinterpret_cast< PartialRunSetupRequest* >(object);
  (void)_this;
}
void PartialRunSetupRequest::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void PartialRunSetupRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* PartialRunSetupRequest::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const PartialRunSetupRequest& PartialRunSetupRequest::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_PartialRunSetupRequest.base);
  return *internal_default_instance();
}


void PartialRunSetupRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.PartialRunSetupRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  feed_.Clear();
  fetch_.Clear();
  target_.Clear();
  session_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  request_id_ = GOOGLE_LONGLONG(0);
  _internal_metadata_.Clear();
}

bool PartialRunSetupRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.PartialRunSetupRequest)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string session_handle = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_session_handle()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->session_handle().data(), static_cast<int>(this->session_handle().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.PartialRunSetupRequest.session_handle"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string feed = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_feed()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->feed(this->feed_size() - 1).data(),
            static_cast<int>(this->feed(this->feed_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.PartialRunSetupRequest.feed"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string fetch = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_fetch()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->fetch(this->fetch_size() - 1).data(),
            static_cast<int>(this->fetch(this->fetch_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.PartialRunSetupRequest.fetch"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string target = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_target()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->target(this->target_size() - 1).data(),
            static_cast<int>(this->target(this->target_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.PartialRunSetupRequest.target"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 request_id = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &request_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.PartialRunSetupRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.PartialRunSetupRequest)
  return false;
#undef DO_
}

void PartialRunSetupRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.PartialRunSetupRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string session_handle = 1;
  if (this->session_handle().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->session_handle().data(), static_cast<int>(this->session_handle().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.PartialRunSetupRequest.session_handle");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->session_handle(), output);
  }

  // repeated string feed = 2;
  for (int i = 0, n = this->feed_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->feed(i).data(), static_cast<int>(this->feed(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.PartialRunSetupRequest.feed");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->feed(i), output);
  }

  // repeated string fetch = 3;
  for (int i = 0, n = this->fetch_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fetch(i).data(), static_cast<int>(this->fetch(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.PartialRunSetupRequest.fetch");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      3, this->fetch(i), output);
  }

  // repeated string target = 4;
  for (int i = 0, n = this->target_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->target(i).data(), static_cast<int>(this->target(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.PartialRunSetupRequest.target");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      4, this->target(i), output);
  }

  // int64 request_id = 5;
  if (this->request_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(5, this->request_id(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.PartialRunSetupRequest)
}

::google::protobuf::uint8* PartialRunSetupRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.PartialRunSetupRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string session_handle = 1;
  if (this->session_handle().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->session_handle().data(), static_cast<int>(this->session_handle().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.PartialRunSetupRequest.session_handle");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->session_handle(), target);
  }

  // repeated string feed = 2;
  for (int i = 0, n = this->feed_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->feed(i).data(), static_cast<int>(this->feed(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.PartialRunSetupRequest.feed");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(2, this->feed(i), target);
  }

  // repeated string fetch = 3;
  for (int i = 0, n = this->fetch_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fetch(i).data(), static_cast<int>(this->fetch(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.PartialRunSetupRequest.fetch");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(3, this->fetch(i), target);
  }

  // repeated string target = 4;
  for (int i = 0, n = this->target_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->target(i).data(), static_cast<int>(this->target(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.PartialRunSetupRequest.target");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(4, this->target(i), target);
  }

  // int64 request_id = 5;
  if (this->request_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(5, this->request_id(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.PartialRunSetupRequest)
  return target;
}

size_t PartialRunSetupRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.PartialRunSetupRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated string feed = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->feed_size());
  for (int i = 0, n = this->feed_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->feed(i));
  }

  // repeated string fetch = 3;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->fetch_size());
  for (int i = 0, n = this->fetch_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->fetch(i));
  }

  // repeated string target = 4;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->target_size());
  for (int i = 0, n = this->target_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->target(i));
  }

  // string session_handle = 1;
  if (this->session_handle().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->session_handle());
  }

  // int64 request_id = 5;
  if (this->request_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->request_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void PartialRunSetupRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.PartialRunSetupRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const PartialRunSetupRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const PartialRunSetupRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.PartialRunSetupRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.PartialRunSetupRequest)
    MergeFrom(*source);
  }
}

void PartialRunSetupRequest::MergeFrom(const PartialRunSetupRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.PartialRunSetupRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  feed_.MergeFrom(from.feed_);
  fetch_.MergeFrom(from.fetch_);
  target_.MergeFrom(from.target_);
  if (from.session_handle().size() > 0) {
    set_session_handle(from.session_handle());
  }
  if (from.request_id() != 0) {
    set_request_id(from.request_id());
  }
}

void PartialRunSetupRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.PartialRunSetupRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PartialRunSetupRequest::CopyFrom(const PartialRunSetupRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.PartialRunSetupRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PartialRunSetupRequest::IsInitialized() const {
  return true;
}

void PartialRunSetupRequest::Swap(PartialRunSetupRequest* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    PartialRunSetupRequest* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void PartialRunSetupRequest::UnsafeArenaSwap(PartialRunSetupRequest* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void PartialRunSetupRequest::InternalSwap(PartialRunSetupRequest* other) {
  using std::swap;
  feed_.InternalSwap(CastToBase(&other->feed_));
  fetch_.InternalSwap(CastToBase(&other->fetch_));
  target_.InternalSwap(CastToBase(&other->target_));
  session_handle_.Swap(&other->session_handle_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(request_id_, other->request_id_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata PartialRunSetupRequest::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void PartialRunSetupResponse::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int PartialRunSetupResponse::kPartialRunHandleFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

PartialRunSetupResponse::PartialRunSetupResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_PartialRunSetupResponse.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.PartialRunSetupResponse)
}
PartialRunSetupResponse::PartialRunSetupResponse(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_PartialRunSetupResponse.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.PartialRunSetupResponse)
}
PartialRunSetupResponse::PartialRunSetupResponse(const PartialRunSetupResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  partial_run_handle_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.partial_run_handle().size() > 0) {
    partial_run_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.partial_run_handle(),
      GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.PartialRunSetupResponse)
}

void PartialRunSetupResponse::SharedCtor() {
  partial_run_handle_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

PartialRunSetupResponse::~PartialRunSetupResponse() {
  // @@protoc_insertion_point(destructor:tensorflow.PartialRunSetupResponse)
  SharedDtor();
}

void PartialRunSetupResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  partial_run_handle_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void PartialRunSetupResponse::ArenaDtor(void* object) {
  PartialRunSetupResponse* _this = reinterpret_cast< PartialRunSetupResponse* >(object);
  (void)_this;
}
void PartialRunSetupResponse::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void PartialRunSetupResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* PartialRunSetupResponse::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const PartialRunSetupResponse& PartialRunSetupResponse::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_PartialRunSetupResponse.base);
  return *internal_default_instance();
}


void PartialRunSetupResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.PartialRunSetupResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  partial_run_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  _internal_metadata_.Clear();
}

bool PartialRunSetupResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.PartialRunSetupResponse)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string partial_run_handle = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_partial_run_handle()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->partial_run_handle().data(), static_cast<int>(this->partial_run_handle().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.PartialRunSetupResponse.partial_run_handle"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.PartialRunSetupResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.PartialRunSetupResponse)
  return false;
#undef DO_
}

void PartialRunSetupResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.PartialRunSetupResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string partial_run_handle = 1;
  if (this->partial_run_handle().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->partial_run_handle().data(), static_cast<int>(this->partial_run_handle().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.PartialRunSetupResponse.partial_run_handle");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->partial_run_handle(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.PartialRunSetupResponse)
}

::google::protobuf::uint8* PartialRunSetupResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.PartialRunSetupResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string partial_run_handle = 1;
  if (this->partial_run_handle().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->partial_run_handle().data(), static_cast<int>(this->partial_run_handle().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.PartialRunSetupResponse.partial_run_handle");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->partial_run_handle(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.PartialRunSetupResponse)
  return target;
}

size_t PartialRunSetupResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.PartialRunSetupResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string partial_run_handle = 1;
  if (this->partial_run_handle().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->partial_run_handle());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void PartialRunSetupResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.PartialRunSetupResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const PartialRunSetupResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const PartialRunSetupResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.PartialRunSetupResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.PartialRunSetupResponse)
    MergeFrom(*source);
  }
}

void PartialRunSetupResponse::MergeFrom(const PartialRunSetupResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.PartialRunSetupResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.partial_run_handle().size() > 0) {
    set_partial_run_handle(from.partial_run_handle());
  }
}

void PartialRunSetupResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.PartialRunSetupResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PartialRunSetupResponse::CopyFrom(const PartialRunSetupResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.PartialRunSetupResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PartialRunSetupResponse::IsInitialized() const {
  return true;
}

void PartialRunSetupResponse::Swap(PartialRunSetupResponse* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    PartialRunSetupResponse* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void PartialRunSetupResponse::UnsafeArenaSwap(PartialRunSetupResponse* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void PartialRunSetupResponse::InternalSwap(PartialRunSetupResponse* other) {
  using std::swap;
  partial_run_handle_.Swap(&other->partial_run_handle_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata PartialRunSetupResponse::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void CloseSessionRequest::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CloseSessionRequest::kSessionHandleFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CloseSessionRequest::CloseSessionRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_CloseSessionRequest.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.CloseSessionRequest)
}
CloseSessionRequest::CloseSessionRequest(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_CloseSessionRequest.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.CloseSessionRequest)
}
CloseSessionRequest::CloseSessionRequest(const CloseSessionRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  session_handle_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.session_handle().size() > 0) {
    session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.session_handle(),
      GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.CloseSessionRequest)
}

void CloseSessionRequest::SharedCtor() {
  session_handle_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

CloseSessionRequest::~CloseSessionRequest() {
  // @@protoc_insertion_point(destructor:tensorflow.CloseSessionRequest)
  SharedDtor();
}

void CloseSessionRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  session_handle_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void CloseSessionRequest::ArenaDtor(void* object) {
  CloseSessionRequest* _this = reinterpret_cast< CloseSessionRequest* >(object);
  (void)_this;
}
void CloseSessionRequest::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void CloseSessionRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* CloseSessionRequest::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CloseSessionRequest& CloseSessionRequest::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_CloseSessionRequest.base);
  return *internal_default_instance();
}


void CloseSessionRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.CloseSessionRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  session_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  _internal_metadata_.Clear();
}

bool CloseSessionRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.CloseSessionRequest)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string session_handle = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_session_handle()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->session_handle().data(), static_cast<int>(this->session_handle().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.CloseSessionRequest.session_handle"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.CloseSessionRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.CloseSessionRequest)
  return false;
#undef DO_
}

void CloseSessionRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.CloseSessionRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string session_handle = 1;
  if (this->session_handle().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->session_handle().data(), static_cast<int>(this->session_handle().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CloseSessionRequest.session_handle");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->session_handle(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.CloseSessionRequest)
}

::google::protobuf::uint8* CloseSessionRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.CloseSessionRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string session_handle = 1;
  if (this->session_handle().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->session_handle().data(), static_cast<int>(this->session_handle().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CloseSessionRequest.session_handle");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->session_handle(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.CloseSessionRequest)
  return target;
}

size_t CloseSessionRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.CloseSessionRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string session_handle = 1;
  if (this->session_handle().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->session_handle());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CloseSessionRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.CloseSessionRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const CloseSessionRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CloseSessionRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.CloseSessionRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.CloseSessionRequest)
    MergeFrom(*source);
  }
}

void CloseSessionRequest::MergeFrom(const CloseSessionRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.CloseSessionRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.session_handle().size() > 0) {
    set_session_handle(from.session_handle());
  }
}

void CloseSessionRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.CloseSessionRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CloseSessionRequest::CopyFrom(const CloseSessionRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.CloseSessionRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CloseSessionRequest::IsInitialized() const {
  return true;
}

void CloseSessionRequest::Swap(CloseSessionRequest* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    CloseSessionRequest* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void CloseSessionRequest::UnsafeArenaSwap(CloseSessionRequest* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void CloseSessionRequest::InternalSwap(CloseSessionRequest* other) {
  using std::swap;
  session_handle_.Swap(&other->session_handle_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata CloseSessionRequest::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void CloseSessionResponse::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CloseSessionResponse::CloseSessionResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_CloseSessionResponse.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.CloseSessionResponse)
}
CloseSessionResponse::CloseSessionResponse(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_CloseSessionResponse.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.CloseSessionResponse)
}
CloseSessionResponse::CloseSessionResponse(const CloseSessionResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.CloseSessionResponse)
}

void CloseSessionResponse::SharedCtor() {
}

CloseSessionResponse::~CloseSessionResponse() {
  // @@protoc_insertion_point(destructor:tensorflow.CloseSessionResponse)
  SharedDtor();
}

void CloseSessionResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void CloseSessionResponse::ArenaDtor(void* object) {
  CloseSessionResponse* _this = reinterpret_cast< CloseSessionResponse* >(object);
  (void)_this;
}
void CloseSessionResponse::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void CloseSessionResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* CloseSessionResponse::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CloseSessionResponse& CloseSessionResponse::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_CloseSessionResponse.base);
  return *internal_default_instance();
}


void CloseSessionResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.CloseSessionResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_metadata_.Clear();
}

bool CloseSessionResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.CloseSessionResponse)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, _internal_metadata_.mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.CloseSessionResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.CloseSessionResponse)
  return false;
#undef DO_
}

void CloseSessionResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.CloseSessionResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.CloseSessionResponse)
}

::google::protobuf::uint8* CloseSessionResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.CloseSessionResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.CloseSessionResponse)
  return target;
}

size_t CloseSessionResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.CloseSessionResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CloseSessionResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.CloseSessionResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const CloseSessionResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CloseSessionResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.CloseSessionResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.CloseSessionResponse)
    MergeFrom(*source);
  }
}

void CloseSessionResponse::MergeFrom(const CloseSessionResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.CloseSessionResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

}

void CloseSessionResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.CloseSessionResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CloseSessionResponse::CopyFrom(const CloseSessionResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.CloseSessionResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CloseSessionResponse::IsInitialized() const {
  return true;
}

void CloseSessionResponse::Swap(CloseSessionResponse* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    CloseSessionResponse* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void CloseSessionResponse::UnsafeArenaSwap(CloseSessionResponse* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void CloseSessionResponse::InternalSwap(CloseSessionResponse* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata CloseSessionResponse::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ResetRequest::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ResetRequest::kContainerFieldNumber;
const int ResetRequest::kDeviceFiltersFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ResetRequest::ResetRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_ResetRequest.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.ResetRequest)
}
ResetRequest::ResetRequest(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  container_(arena),
  device_filters_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_ResetRequest.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.ResetRequest)
}
ResetRequest::ResetRequest(const ResetRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      container_(from.container_),
      device_filters_(from.device_filters_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.ResetRequest)
}

void ResetRequest::SharedCtor() {
}

ResetRequest::~ResetRequest() {
  // @@protoc_insertion_point(destructor:tensorflow.ResetRequest)
  SharedDtor();
}

void ResetRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void ResetRequest::ArenaDtor(void* object) {
  ResetRequest* _this = reinterpret_cast< ResetRequest* >(object);
  (void)_this;
}
void ResetRequest::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void ResetRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ResetRequest::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ResetRequest& ResetRequest::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_ResetRequest.base);
  return *internal_default_instance();
}


void ResetRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.ResetRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  container_.Clear();
  device_filters_.Clear();
  _internal_metadata_.Clear();
}

bool ResetRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.ResetRequest)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated string container = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_container()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->container(this->container_size() - 1).data(),
            static_cast<int>(this->container(this->container_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ResetRequest.container"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string device_filters = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_device_filters()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->device_filters(this->device_filters_size() - 1).data(),
            static_cast<int>(this->device_filters(this->device_filters_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ResetRequest.device_filters"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.ResetRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.ResetRequest)
  return false;
#undef DO_
}

void ResetRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.ResetRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string container = 1;
  for (int i = 0, n = this->container_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->container(i).data(), static_cast<int>(this->container(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ResetRequest.container");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->container(i), output);
  }

  // repeated string device_filters = 2;
  for (int i = 0, n = this->device_filters_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->device_filters(i).data(), static_cast<int>(this->device_filters(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ResetRequest.device_filters");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->device_filters(i), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.ResetRequest)
}

::google::protobuf::uint8* ResetRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.ResetRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string container = 1;
  for (int i = 0, n = this->container_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->container(i).data(), static_cast<int>(this->container(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ResetRequest.container");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(1, this->container(i), target);
  }

  // repeated string device_filters = 2;
  for (int i = 0, n = this->device_filters_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->device_filters(i).data(), static_cast<int>(this->device_filters(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ResetRequest.device_filters");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(2, this->device_filters(i), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.ResetRequest)
  return target;
}

size_t ResetRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.ResetRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated string container = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->container_size());
  for (int i = 0, n = this->container_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->container(i));
  }

  // repeated string device_filters = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->device_filters_size());
  for (int i = 0, n = this->device_filters_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->device_filters(i));
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ResetRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.ResetRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const ResetRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ResetRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.ResetRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.ResetRequest)
    MergeFrom(*source);
  }
}

void ResetRequest::MergeFrom(const ResetRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.ResetRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  container_.MergeFrom(from.container_);
  device_filters_.MergeFrom(from.device_filters_);
}

void ResetRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.ResetRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ResetRequest::CopyFrom(const ResetRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.ResetRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ResetRequest::IsInitialized() const {
  return true;
}

void ResetRequest::Swap(ResetRequest* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    ResetRequest* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void ResetRequest::UnsafeArenaSwap(ResetRequest* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void ResetRequest::InternalSwap(ResetRequest* other) {
  using std::swap;
  container_.InternalSwap(CastToBase(&other->container_));
  device_filters_.InternalSwap(CastToBase(&other->device_filters_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ResetRequest::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ResetResponse::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ResetResponse::ResetResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_ResetResponse.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.ResetResponse)
}
ResetResponse::ResetResponse(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_ResetResponse.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.ResetResponse)
}
ResetResponse::ResetResponse(const ResetResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.ResetResponse)
}

void ResetResponse::SharedCtor() {
}

ResetResponse::~ResetResponse() {
  // @@protoc_insertion_point(destructor:tensorflow.ResetResponse)
  SharedDtor();
}

void ResetResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void ResetResponse::ArenaDtor(void* object) {
  ResetResponse* _this = reinterpret_cast< ResetResponse* >(object);
  (void)_this;
}
void ResetResponse::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void ResetResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ResetResponse::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ResetResponse& ResetResponse::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_ResetResponse.base);
  return *internal_default_instance();
}


void ResetResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.ResetResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_metadata_.Clear();
}

bool ResetResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.ResetResponse)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, _internal_metadata_.mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.ResetResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.ResetResponse)
  return false;
#undef DO_
}

void ResetResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.ResetResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.ResetResponse)
}

::google::protobuf::uint8* ResetResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.ResetResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.ResetResponse)
  return target;
}

size_t ResetResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.ResetResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ResetResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.ResetResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const ResetResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ResetResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.ResetResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.ResetResponse)
    MergeFrom(*source);
  }
}

void ResetResponse::MergeFrom(const ResetResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.ResetResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

}

void ResetResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.ResetResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ResetResponse::CopyFrom(const ResetResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.ResetResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ResetResponse::IsInitialized() const {
  return true;
}

void ResetResponse::Swap(ResetResponse* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    ResetResponse* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void ResetResponse::UnsafeArenaSwap(ResetResponse* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void ResetResponse::InternalSwap(ResetResponse* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ResetResponse::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ListDevicesRequest::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ListDevicesRequest::kSessionHandleFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ListDevicesRequest::ListDevicesRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_ListDevicesRequest.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.ListDevicesRequest)
}
ListDevicesRequest::ListDevicesRequest(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_ListDevicesRequest.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.ListDevicesRequest)
}
ListDevicesRequest::ListDevicesRequest(const ListDevicesRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  session_handle_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.session_handle().size() > 0) {
    session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.session_handle(),
      GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.ListDevicesRequest)
}

void ListDevicesRequest::SharedCtor() {
  session_handle_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

ListDevicesRequest::~ListDevicesRequest() {
  // @@protoc_insertion_point(destructor:tensorflow.ListDevicesRequest)
  SharedDtor();
}

void ListDevicesRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  session_handle_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void ListDevicesRequest::ArenaDtor(void* object) {
  ListDevicesRequest* _this = reinterpret_cast< ListDevicesRequest* >(object);
  (void)_this;
}
void ListDevicesRequest::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void ListDevicesRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ListDevicesRequest::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ListDevicesRequest& ListDevicesRequest::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_ListDevicesRequest.base);
  return *internal_default_instance();
}


void ListDevicesRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.ListDevicesRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  session_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  _internal_metadata_.Clear();
}

bool ListDevicesRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.ListDevicesRequest)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string session_handle = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_session_handle()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->session_handle().data(), static_cast<int>(this->session_handle().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ListDevicesRequest.session_handle"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.ListDevicesRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.ListDevicesRequest)
  return false;
#undef DO_
}

void ListDevicesRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.ListDevicesRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string session_handle = 1;
  if (this->session_handle().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->session_handle().data(), static_cast<int>(this->session_handle().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ListDevicesRequest.session_handle");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->session_handle(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.ListDevicesRequest)
}

::google::protobuf::uint8* ListDevicesRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.ListDevicesRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string session_handle = 1;
  if (this->session_handle().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->session_handle().data(), static_cast<int>(this->session_handle().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ListDevicesRequest.session_handle");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->session_handle(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.ListDevicesRequest)
  return target;
}

size_t ListDevicesRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.ListDevicesRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string session_handle = 1;
  if (this->session_handle().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->session_handle());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ListDevicesRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.ListDevicesRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const ListDevicesRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ListDevicesRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.ListDevicesRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.ListDevicesRequest)
    MergeFrom(*source);
  }
}

void ListDevicesRequest::MergeFrom(const ListDevicesRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.ListDevicesRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.session_handle().size() > 0) {
    set_session_handle(from.session_handle());
  }
}

void ListDevicesRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.ListDevicesRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ListDevicesRequest::CopyFrom(const ListDevicesRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.ListDevicesRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ListDevicesRequest::IsInitialized() const {
  return true;
}

void ListDevicesRequest::Swap(ListDevicesRequest* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    ListDevicesRequest* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void ListDevicesRequest::UnsafeArenaSwap(ListDevicesRequest* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void ListDevicesRequest::InternalSwap(ListDevicesRequest* other) {
  using std::swap;
  session_handle_.Swap(&other->session_handle_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ListDevicesRequest::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ListDevicesResponse::InitAsDefaultInstance() {
}
void ListDevicesResponse::clear_local_device() {
  local_device_.Clear();
}
void ListDevicesResponse::clear_remote_device() {
  remote_device_.Clear();
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ListDevicesResponse::kLocalDeviceFieldNumber;
const int ListDevicesResponse::kRemoteDeviceFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ListDevicesResponse::ListDevicesResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_ListDevicesResponse.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.ListDevicesResponse)
}
ListDevicesResponse::ListDevicesResponse(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  local_device_(arena),
  remote_device_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_ListDevicesResponse.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.ListDevicesResponse)
}
ListDevicesResponse::ListDevicesResponse(const ListDevicesResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      local_device_(from.local_device_),
      remote_device_(from.remote_device_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.ListDevicesResponse)
}

void ListDevicesResponse::SharedCtor() {
}

ListDevicesResponse::~ListDevicesResponse() {
  // @@protoc_insertion_point(destructor:tensorflow.ListDevicesResponse)
  SharedDtor();
}

void ListDevicesResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void ListDevicesResponse::ArenaDtor(void* object) {
  ListDevicesResponse* _this = reinterpret_cast< ListDevicesResponse* >(object);
  (void)_this;
}
void ListDevicesResponse::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void ListDevicesResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ListDevicesResponse::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ListDevicesResponse& ListDevicesResponse::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_ListDevicesResponse.base);
  return *internal_default_instance();
}


void ListDevicesResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.ListDevicesResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  local_device_.Clear();
  remote_device_.Clear();
  _internal_metadata_.Clear();
}

bool ListDevicesResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.ListDevicesResponse)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.DeviceAttributes local_device = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_local_device()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.DeviceAttributes remote_device = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_remote_device()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.ListDevicesResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.ListDevicesResponse)
  return false;
#undef DO_
}

void ListDevicesResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.ListDevicesResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.DeviceAttributes local_device = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->local_device_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->local_device(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.DeviceAttributes remote_device = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->remote_device_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2,
      this->remote_device(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.ListDevicesResponse)
}

::google::protobuf::uint8* ListDevicesResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.ListDevicesResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.DeviceAttributes local_device = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->local_device_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->local_device(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.DeviceAttributes remote_device = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->remote_device_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->remote_device(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.ListDevicesResponse)
  return target;
}

size_t ListDevicesResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.ListDevicesResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.DeviceAttributes local_device = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->local_device_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->local_device(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.DeviceAttributes remote_device = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->remote_device_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->remote_device(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ListDevicesResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.ListDevicesResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const ListDevicesResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ListDevicesResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.ListDevicesResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.ListDevicesResponse)
    MergeFrom(*source);
  }
}

void ListDevicesResponse::MergeFrom(const ListDevicesResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.ListDevicesResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  local_device_.MergeFrom(from.local_device_);
  remote_device_.MergeFrom(from.remote_device_);
}

void ListDevicesResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.ListDevicesResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ListDevicesResponse::CopyFrom(const ListDevicesResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.ListDevicesResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ListDevicesResponse::IsInitialized() const {
  return true;
}

void ListDevicesResponse::Swap(ListDevicesResponse* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    ListDevicesResponse* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void ListDevicesResponse::UnsafeArenaSwap(ListDevicesResponse* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void ListDevicesResponse::InternalSwap(ListDevicesResponse* other) {
  using std::swap;
  CastToBase(&local_device_)->InternalSwap(CastToBase(&other->local_device_));
  CastToBase(&remote_device_)->InternalSwap(CastToBase(&other->remote_device_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ListDevicesResponse::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void MakeCallableRequest::InitAsDefaultInstance() {
  ::tensorflow::_MakeCallableRequest_default_instance_._instance.get_mutable()->options_ = const_cast< ::tensorflow::CallableOptions*>(
      ::tensorflow::CallableOptions::internal_default_instance());
}
void MakeCallableRequest::unsafe_arena_set_allocated_options(
    ::tensorflow::CallableOptions* options) {
  if (GetArenaNoVirtual() == NULL) {
    delete options_;
  }
  options_ = options;
  if (options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MakeCallableRequest.options)
}
void MakeCallableRequest::clear_options() {
  if (GetArenaNoVirtual() == NULL && options_ != NULL) {
    delete options_;
  }
  options_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MakeCallableRequest::kSessionHandleFieldNumber;
const int MakeCallableRequest::kOptionsFieldNumber;
const int MakeCallableRequest::kRequestIdFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MakeCallableRequest::MakeCallableRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_MakeCallableRequest.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.MakeCallableRequest)
}
MakeCallableRequest::MakeCallableRequest(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_MakeCallableRequest.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.MakeCallableRequest)
}
MakeCallableRequest::MakeCallableRequest(const MakeCallableRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  session_handle_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.session_handle().size() > 0) {
    session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.session_handle(),
      GetArenaNoVirtual());
  }
  if (from.has_options()) {
    options_ = new ::tensorflow::CallableOptions(*from.options_);
  } else {
    options_ = NULL;
  }
  request_id_ = from.request_id_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.MakeCallableRequest)
}

void MakeCallableRequest::SharedCtor() {
  session_handle_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&options_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&request_id_) -
      reinterpret_cast<char*>(&options_)) + sizeof(request_id_));
}

MakeCallableRequest::~MakeCallableRequest() {
  // @@protoc_insertion_point(destructor:tensorflow.MakeCallableRequest)
  SharedDtor();
}

void MakeCallableRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  session_handle_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete options_;
}

void MakeCallableRequest::ArenaDtor(void* object) {
  MakeCallableRequest* _this = reinterpret_cast< MakeCallableRequest* >(object);
  (void)_this;
}
void MakeCallableRequest::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void MakeCallableRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* MakeCallableRequest::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const MakeCallableRequest& MakeCallableRequest::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_MakeCallableRequest.base);
  return *internal_default_instance();
}


void MakeCallableRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.MakeCallableRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  session_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && options_ != NULL) {
    delete options_;
  }
  options_ = NULL;
  request_id_ = GOOGLE_LONGLONG(0);
  _internal_metadata_.Clear();
}

bool MakeCallableRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.MakeCallableRequest)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string session_handle = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_session_handle()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->session_handle().data(), static_cast<int>(this->session_handle().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.MakeCallableRequest.session_handle"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.CallableOptions options = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_options()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 request_id = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &request_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.MakeCallableRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.MakeCallableRequest)
  return false;
#undef DO_
}

void MakeCallableRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.MakeCallableRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string session_handle = 1;
  if (this->session_handle().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->session_handle().data(), static_cast<int>(this->session_handle().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MakeCallableRequest.session_handle");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->session_handle(), output);
  }

  // .tensorflow.CallableOptions options = 2;
  if (this->has_options()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_options(), output);
  }

  // int64 request_id = 3;
  if (this->request_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->request_id(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.MakeCallableRequest)
}

::google::protobuf::uint8* MakeCallableRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.MakeCallableRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string session_handle = 1;
  if (this->session_handle().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->session_handle().data(), static_cast<int>(this->session_handle().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MakeCallableRequest.session_handle");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->session_handle(), target);
  }

  // .tensorflow.CallableOptions options = 2;
  if (this->has_options()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_options(), deterministic, target);
  }

  // int64 request_id = 3;
  if (this->request_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->request_id(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.MakeCallableRequest)
  return target;
}

size_t MakeCallableRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.MakeCallableRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string session_handle = 1;
  if (this->session_handle().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->session_handle());
  }

  // .tensorflow.CallableOptions options = 2;
  if (this->has_options()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *options_);
  }

  // int64 request_id = 3;
  if (this->request_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->request_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void MakeCallableRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.MakeCallableRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const MakeCallableRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MakeCallableRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.MakeCallableRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.MakeCallableRequest)
    MergeFrom(*source);
  }
}

void MakeCallableRequest::MergeFrom(const MakeCallableRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.MakeCallableRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.session_handle().size() > 0) {
    set_session_handle(from.session_handle());
  }
  if (from.has_options()) {
    mutable_options()->::tensorflow::CallableOptions::MergeFrom(from.options());
  }
  if (from.request_id() != 0) {
    set_request_id(from.request_id());
  }
}

void MakeCallableRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.MakeCallableRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MakeCallableRequest::CopyFrom(const MakeCallableRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.MakeCallableRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MakeCallableRequest::IsInitialized() const {
  return true;
}

void MakeCallableRequest::Swap(MakeCallableRequest* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    MakeCallableRequest* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void MakeCallableRequest::UnsafeArenaSwap(MakeCallableRequest* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void MakeCallableRequest::InternalSwap(MakeCallableRequest* other) {
  using std::swap;
  session_handle_.Swap(&other->session_handle_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(options_, other->options_);
  swap(request_id_, other->request_id_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata MakeCallableRequest::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void MakeCallableResponse::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MakeCallableResponse::kHandleFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MakeCallableResponse::MakeCallableResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_MakeCallableResponse.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.MakeCallableResponse)
}
MakeCallableResponse::MakeCallableResponse(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_MakeCallableResponse.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.MakeCallableResponse)
}
MakeCallableResponse::MakeCallableResponse(const MakeCallableResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  handle_ = from.handle_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.MakeCallableResponse)
}

void MakeCallableResponse::SharedCtor() {
  handle_ = GOOGLE_LONGLONG(0);
}

MakeCallableResponse::~MakeCallableResponse() {
  // @@protoc_insertion_point(destructor:tensorflow.MakeCallableResponse)
  SharedDtor();
}

void MakeCallableResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void MakeCallableResponse::ArenaDtor(void* object) {
  MakeCallableResponse* _this = reinterpret_cast< MakeCallableResponse* >(object);
  (void)_this;
}
void MakeCallableResponse::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void MakeCallableResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* MakeCallableResponse::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const MakeCallableResponse& MakeCallableResponse::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_MakeCallableResponse.base);
  return *internal_default_instance();
}


void MakeCallableResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.MakeCallableResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  handle_ = GOOGLE_LONGLONG(0);
  _internal_metadata_.Clear();
}

bool MakeCallableResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.MakeCallableResponse)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 handle = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &handle_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.MakeCallableResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.MakeCallableResponse)
  return false;
#undef DO_
}

void MakeCallableResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.MakeCallableResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 handle = 1;
  if (this->handle() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->handle(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.MakeCallableResponse)
}

::google::protobuf::uint8* MakeCallableResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.MakeCallableResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 handle = 1;
  if (this->handle() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->handle(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.MakeCallableResponse)
  return target;
}

size_t MakeCallableResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.MakeCallableResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int64 handle = 1;
  if (this->handle() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->handle());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void MakeCallableResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.MakeCallableResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const MakeCallableResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MakeCallableResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.MakeCallableResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.MakeCallableResponse)
    MergeFrom(*source);
  }
}

void MakeCallableResponse::MergeFrom(const MakeCallableResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.MakeCallableResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.handle() != 0) {
    set_handle(from.handle());
  }
}

void MakeCallableResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.MakeCallableResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MakeCallableResponse::CopyFrom(const MakeCallableResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.MakeCallableResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MakeCallableResponse::IsInitialized() const {
  return true;
}

void MakeCallableResponse::Swap(MakeCallableResponse* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    MakeCallableResponse* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void MakeCallableResponse::UnsafeArenaSwap(MakeCallableResponse* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void MakeCallableResponse::InternalSwap(MakeCallableResponse* other) {
  using std::swap;
  swap(handle_, other->handle_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata MakeCallableResponse::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void RunCallableRequest::InitAsDefaultInstance() {
}
void RunCallableRequest::clear_feed() {
  feed_.Clear();
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RunCallableRequest::kSessionHandleFieldNumber;
const int RunCallableRequest::kHandleFieldNumber;
const int RunCallableRequest::kFeedFieldNumber;
const int RunCallableRequest::kRequestIdFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RunCallableRequest::RunCallableRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_RunCallableRequest.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.RunCallableRequest)
}
RunCallableRequest::RunCallableRequest(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  feed_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_RunCallableRequest.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.RunCallableRequest)
}
RunCallableRequest::RunCallableRequest(const RunCallableRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      feed_(from.feed_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  session_handle_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.session_handle().size() > 0) {
    session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.session_handle(),
      GetArenaNoVirtual());
  }
  ::memcpy(&handle_, &from.handle_,
    static_cast<size_t>(reinterpret_cast<char*>(&request_id_) -
    reinterpret_cast<char*>(&handle_)) + sizeof(request_id_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.RunCallableRequest)
}

void RunCallableRequest::SharedCtor() {
  session_handle_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&handle_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&request_id_) -
      reinterpret_cast<char*>(&handle_)) + sizeof(request_id_));
}

RunCallableRequest::~RunCallableRequest() {
  // @@protoc_insertion_point(destructor:tensorflow.RunCallableRequest)
  SharedDtor();
}

void RunCallableRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  session_handle_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void RunCallableRequest::ArenaDtor(void* object) {
  RunCallableRequest* _this = reinterpret_cast< RunCallableRequest* >(object);
  (void)_this;
}
void RunCallableRequest::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void RunCallableRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* RunCallableRequest::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const RunCallableRequest& RunCallableRequest::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_RunCallableRequest.base);
  return *internal_default_instance();
}


void RunCallableRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.RunCallableRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  feed_.Clear();
  session_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  ::memset(&handle_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&request_id_) -
      reinterpret_cast<char*>(&handle_)) + sizeof(request_id_));
  _internal_metadata_.Clear();
}

bool RunCallableRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.RunCallableRequest)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string session_handle = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_session_handle()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->session_handle().data(), static_cast<int>(this->session_handle().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.RunCallableRequest.session_handle"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 handle = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &handle_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.TensorProto feed = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_feed()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 request_id = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &request_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.RunCallableRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.RunCallableRequest)
  return false;
#undef DO_
}

void RunCallableRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.RunCallableRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string session_handle = 1;
  if (this->session_handle().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->session_handle().data(), static_cast<int>(this->session_handle().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.RunCallableRequest.session_handle");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->session_handle(), output);
  }

  // int64 handle = 2;
  if (this->handle() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->handle(), output);
  }

  // repeated .tensorflow.TensorProto feed = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->feed_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3,
      this->feed(static_cast<int>(i)),
      output);
  }

  // int64 request_id = 4;
  if (this->request_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->request_id(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.RunCallableRequest)
}

::google::protobuf::uint8* RunCallableRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.RunCallableRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string session_handle = 1;
  if (this->session_handle().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->session_handle().data(), static_cast<int>(this->session_handle().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.RunCallableRequest.session_handle");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->session_handle(), target);
  }

  // int64 handle = 2;
  if (this->handle() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->handle(), target);
  }

  // repeated .tensorflow.TensorProto feed = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->feed_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->feed(static_cast<int>(i)), deterministic, target);
  }

  // int64 request_id = 4;
  if (this->request_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->request_id(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.RunCallableRequest)
  return target;
}

size_t RunCallableRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.RunCallableRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.TensorProto feed = 3;
  {
    unsigned int count = static_cast<unsigned int>(this->feed_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->feed(static_cast<int>(i)));
    }
  }

  // string session_handle = 1;
  if (this->session_handle().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->session_handle());
  }

  // int64 handle = 2;
  if (this->handle() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->handle());
  }

  // int64 request_id = 4;
  if (this->request_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->request_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RunCallableRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.RunCallableRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const RunCallableRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RunCallableRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.RunCallableRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.RunCallableRequest)
    MergeFrom(*source);
  }
}

void RunCallableRequest::MergeFrom(const RunCallableRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.RunCallableRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  feed_.MergeFrom(from.feed_);
  if (from.session_handle().size() > 0) {
    set_session_handle(from.session_handle());
  }
  if (from.handle() != 0) {
    set_handle(from.handle());
  }
  if (from.request_id() != 0) {
    set_request_id(from.request_id());
  }
}

void RunCallableRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.RunCallableRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RunCallableRequest::CopyFrom(const RunCallableRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.RunCallableRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RunCallableRequest::IsInitialized() const {
  return true;
}

void RunCallableRequest::Swap(RunCallableRequest* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    RunCallableRequest* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void RunCallableRequest::UnsafeArenaSwap(RunCallableRequest* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void RunCallableRequest::InternalSwap(RunCallableRequest* other) {
  using std::swap;
  CastToBase(&feed_)->InternalSwap(CastToBase(&other->feed_));
  session_handle_.Swap(&other->session_handle_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(handle_, other->handle_);
  swap(request_id_, other->request_id_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata RunCallableRequest::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void RunCallableResponse::InitAsDefaultInstance() {
  ::tensorflow::_RunCallableResponse_default_instance_._instance.get_mutable()->metadata_ = const_cast< ::tensorflow::RunMetadata*>(
      ::tensorflow::RunMetadata::internal_default_instance());
}
void RunCallableResponse::clear_fetch() {
  fetch_.Clear();
}
void RunCallableResponse::unsafe_arena_set_allocated_metadata(
    ::tensorflow::RunMetadata* metadata) {
  if (GetArenaNoVirtual() == NULL) {
    delete metadata_;
  }
  metadata_ = metadata;
  if (metadata) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunCallableResponse.metadata)
}
void RunCallableResponse::clear_metadata() {
  if (GetArenaNoVirtual() == NULL && metadata_ != NULL) {
    delete metadata_;
  }
  metadata_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RunCallableResponse::kFetchFieldNumber;
const int RunCallableResponse::kMetadataFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RunCallableResponse::RunCallableResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_RunCallableResponse.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.RunCallableResponse)
}
RunCallableResponse::RunCallableResponse(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  fetch_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_RunCallableResponse.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.RunCallableResponse)
}
RunCallableResponse::RunCallableResponse(const RunCallableResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      fetch_(from.fetch_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_metadata()) {
    metadata_ = new ::tensorflow::RunMetadata(*from.metadata_);
  } else {
    metadata_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.RunCallableResponse)
}

void RunCallableResponse::SharedCtor() {
  metadata_ = NULL;
}

RunCallableResponse::~RunCallableResponse() {
  // @@protoc_insertion_point(destructor:tensorflow.RunCallableResponse)
  SharedDtor();
}

void RunCallableResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (this != internal_default_instance()) delete metadata_;
}

void RunCallableResponse::ArenaDtor(void* object) {
  RunCallableResponse* _this = reinterpret_cast< RunCallableResponse* >(object);
  (void)_this;
}
void RunCallableResponse::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void RunCallableResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* RunCallableResponse::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const RunCallableResponse& RunCallableResponse::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_RunCallableResponse.base);
  return *internal_default_instance();
}


void RunCallableResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.RunCallableResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  fetch_.Clear();
  if (GetArenaNoVirtual() == NULL && metadata_ != NULL) {
    delete metadata_;
  }
  metadata_ = NULL;
  _internal_metadata_.Clear();
}

bool RunCallableResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.RunCallableResponse)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.TensorProto fetch = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_fetch()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.RunMetadata metadata = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_metadata()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.RunCallableResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.RunCallableResponse)
  return false;
#undef DO_
}

void RunCallableResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.RunCallableResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.TensorProto fetch = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->fetch_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->fetch(static_cast<int>(i)),
      output);
  }

  // .tensorflow.RunMetadata metadata = 2;
  if (this->has_metadata()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_metadata(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.RunCallableResponse)
}

::google::protobuf::uint8* RunCallableResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.RunCallableResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.TensorProto fetch = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->fetch_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->fetch(static_cast<int>(i)), deterministic, target);
  }

  // .tensorflow.RunMetadata metadata = 2;
  if (this->has_metadata()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_metadata(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.RunCallableResponse)
  return target;
}

size_t RunCallableResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.RunCallableResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.TensorProto fetch = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->fetch_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->fetch(static_cast<int>(i)));
    }
  }

  // .tensorflow.RunMetadata metadata = 2;
  if (this->has_metadata()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *metadata_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RunCallableResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.RunCallableResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const RunCallableResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RunCallableResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.RunCallableResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.RunCallableResponse)
    MergeFrom(*source);
  }
}

void RunCallableResponse::MergeFrom(const RunCallableResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.RunCallableResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  fetch_.MergeFrom(from.fetch_);
  if (from.has_metadata()) {
    mutable_metadata()->::tensorflow::RunMetadata::MergeFrom(from.metadata());
  }
}

void RunCallableResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.RunCallableResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RunCallableResponse::CopyFrom(const RunCallableResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.RunCallableResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RunCallableResponse::IsInitialized() const {
  return true;
}

void RunCallableResponse::Swap(RunCallableResponse* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    RunCallableResponse* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void RunCallableResponse::UnsafeArenaSwap(RunCallableResponse* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void RunCallableResponse::InternalSwap(RunCallableResponse* other) {
  using std::swap;
  CastToBase(&fetch_)->InternalSwap(CastToBase(&other->fetch_));
  swap(metadata_, other->metadata_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata RunCallableResponse::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ReleaseCallableRequest::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ReleaseCallableRequest::kSessionHandleFieldNumber;
const int ReleaseCallableRequest::kHandleFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ReleaseCallableRequest::ReleaseCallableRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_ReleaseCallableRequest.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.ReleaseCallableRequest)
}
ReleaseCallableRequest::ReleaseCallableRequest(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_ReleaseCallableRequest.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.ReleaseCallableRequest)
}
ReleaseCallableRequest::ReleaseCallableRequest(const ReleaseCallableRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  session_handle_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.session_handle().size() > 0) {
    session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.session_handle(),
      GetArenaNoVirtual());
  }
  handle_ = from.handle_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.ReleaseCallableRequest)
}

void ReleaseCallableRequest::SharedCtor() {
  session_handle_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  handle_ = GOOGLE_LONGLONG(0);
}

ReleaseCallableRequest::~ReleaseCallableRequest() {
  // @@protoc_insertion_point(destructor:tensorflow.ReleaseCallableRequest)
  SharedDtor();
}

void ReleaseCallableRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  session_handle_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void ReleaseCallableRequest::ArenaDtor(void* object) {
  ReleaseCallableRequest* _this = reinterpret_cast< ReleaseCallableRequest* >(object);
  (void)_this;
}
void ReleaseCallableRequest::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void ReleaseCallableRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ReleaseCallableRequest::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ReleaseCallableRequest& ReleaseCallableRequest::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_ReleaseCallableRequest.base);
  return *internal_default_instance();
}


void ReleaseCallableRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.ReleaseCallableRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  session_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  handle_ = GOOGLE_LONGLONG(0);
  _internal_metadata_.Clear();
}

bool ReleaseCallableRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.ReleaseCallableRequest)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string session_handle = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_session_handle()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->session_handle().data(), static_cast<int>(this->session_handle().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ReleaseCallableRequest.session_handle"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 handle = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &handle_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.ReleaseCallableRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.ReleaseCallableRequest)
  return false;
#undef DO_
}

void ReleaseCallableRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.ReleaseCallableRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string session_handle = 1;
  if (this->session_handle().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->session_handle().data(), static_cast<int>(this->session_handle().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ReleaseCallableRequest.session_handle");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->session_handle(), output);
  }

  // int64 handle = 2;
  if (this->handle() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->handle(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.ReleaseCallableRequest)
}

::google::protobuf::uint8* ReleaseCallableRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.ReleaseCallableRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string session_handle = 1;
  if (this->session_handle().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->session_handle().data(), static_cast<int>(this->session_handle().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ReleaseCallableRequest.session_handle");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->session_handle(), target);
  }

  // int64 handle = 2;
  if (this->handle() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->handle(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.ReleaseCallableRequest)
  return target;
}

size_t ReleaseCallableRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.ReleaseCallableRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string session_handle = 1;
  if (this->session_handle().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->session_handle());
  }

  // int64 handle = 2;
  if (this->handle() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->handle());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ReleaseCallableRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.ReleaseCallableRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const ReleaseCallableRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ReleaseCallableRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.ReleaseCallableRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.ReleaseCallableRequest)
    MergeFrom(*source);
  }
}

void ReleaseCallableRequest::MergeFrom(const ReleaseCallableRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.ReleaseCallableRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.session_handle().size() > 0) {
    set_session_handle(from.session_handle());
  }
  if (from.handle() != 0) {
    set_handle(from.handle());
  }
}

void ReleaseCallableRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.ReleaseCallableRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ReleaseCallableRequest::CopyFrom(const ReleaseCallableRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.ReleaseCallableRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ReleaseCallableRequest::IsInitialized() const {
  return true;
}

void ReleaseCallableRequest::Swap(ReleaseCallableRequest* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    ReleaseCallableRequest* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void ReleaseCallableRequest::UnsafeArenaSwap(ReleaseCallableRequest* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void ReleaseCallableRequest::InternalSwap(ReleaseCallableRequest* other) {
  using std::swap;
  session_handle_.Swap(&other->session_handle_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(handle_, other->handle_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ReleaseCallableRequest::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ReleaseCallableResponse::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ReleaseCallableResponse::ReleaseCallableResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_ReleaseCallableResponse.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.ReleaseCallableResponse)
}
ReleaseCallableResponse::ReleaseCallableResponse(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_ReleaseCallableResponse.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.ReleaseCallableResponse)
}
ReleaseCallableResponse::ReleaseCallableResponse(const ReleaseCallableResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.ReleaseCallableResponse)
}

void ReleaseCallableResponse::SharedCtor() {
}

ReleaseCallableResponse::~ReleaseCallableResponse() {
  // @@protoc_insertion_point(destructor:tensorflow.ReleaseCallableResponse)
  SharedDtor();
}

void ReleaseCallableResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void ReleaseCallableResponse::ArenaDtor(void* object) {
  ReleaseCallableResponse* _this = reinterpret_cast< ReleaseCallableResponse* >(object);
  (void)_this;
}
void ReleaseCallableResponse::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void ReleaseCallableResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ReleaseCallableResponse::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ReleaseCallableResponse& ReleaseCallableResponse::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::scc_info_ReleaseCallableResponse.base);
  return *internal_default_instance();
}


void ReleaseCallableResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.ReleaseCallableResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_metadata_.Clear();
}

bool ReleaseCallableResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.ReleaseCallableResponse)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, _internal_metadata_.mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.ReleaseCallableResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.ReleaseCallableResponse)
  return false;
#undef DO_
}

void ReleaseCallableResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.ReleaseCallableResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.ReleaseCallableResponse)
}

::google::protobuf::uint8* ReleaseCallableResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.ReleaseCallableResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.ReleaseCallableResponse)
  return target;
}

size_t ReleaseCallableResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.ReleaseCallableResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ReleaseCallableResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.ReleaseCallableResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const ReleaseCallableResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ReleaseCallableResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.ReleaseCallableResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.ReleaseCallableResponse)
    MergeFrom(*source);
  }
}

void ReleaseCallableResponse::MergeFrom(const ReleaseCallableResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.ReleaseCallableResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

}

void ReleaseCallableResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.ReleaseCallableResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ReleaseCallableResponse::CopyFrom(const ReleaseCallableResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.ReleaseCallableResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ReleaseCallableResponse::IsInitialized() const {
  return true;
}

void ReleaseCallableResponse::Swap(ReleaseCallableResponse* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    ReleaseCallableResponse* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void ReleaseCallableResponse::UnsafeArenaSwap(ReleaseCallableResponse* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void ReleaseCallableResponse::InternalSwap(ReleaseCallableResponse* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ReleaseCallableResponse::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::CreateSessionRequest* Arena::CreateMaybeMessage< ::tensorflow::CreateSessionRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::CreateSessionRequest >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::CreateSessionResponse* Arena::CreateMaybeMessage< ::tensorflow::CreateSessionResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::CreateSessionResponse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::ExtendSessionRequest* Arena::CreateMaybeMessage< ::tensorflow::ExtendSessionRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::ExtendSessionRequest >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::ExtendSessionResponse* Arena::CreateMaybeMessage< ::tensorflow::ExtendSessionResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::ExtendSessionResponse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::RunStepRequest* Arena::CreateMaybeMessage< ::tensorflow::RunStepRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::RunStepRequest >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::RunStepResponse* Arena::CreateMaybeMessage< ::tensorflow::RunStepResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::RunStepResponse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::PartialRunSetupRequest* Arena::CreateMaybeMessage< ::tensorflow::PartialRunSetupRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::PartialRunSetupRequest >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::PartialRunSetupResponse* Arena::CreateMaybeMessage< ::tensorflow::PartialRunSetupResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::PartialRunSetupResponse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::CloseSessionRequest* Arena::CreateMaybeMessage< ::tensorflow::CloseSessionRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::CloseSessionRequest >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::CloseSessionResponse* Arena::CreateMaybeMessage< ::tensorflow::CloseSessionResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::CloseSessionResponse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::ResetRequest* Arena::CreateMaybeMessage< ::tensorflow::ResetRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::ResetRequest >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::ResetResponse* Arena::CreateMaybeMessage< ::tensorflow::ResetResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::ResetResponse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::ListDevicesRequest* Arena::CreateMaybeMessage< ::tensorflow::ListDevicesRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::ListDevicesRequest >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::ListDevicesResponse* Arena::CreateMaybeMessage< ::tensorflow::ListDevicesResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::ListDevicesResponse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::MakeCallableRequest* Arena::CreateMaybeMessage< ::tensorflow::MakeCallableRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::MakeCallableRequest >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::MakeCallableResponse* Arena::CreateMaybeMessage< ::tensorflow::MakeCallableResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::MakeCallableResponse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::RunCallableRequest* Arena::CreateMaybeMessage< ::tensorflow::RunCallableRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::RunCallableRequest >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::RunCallableResponse* Arena::CreateMaybeMessage< ::tensorflow::RunCallableResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::RunCallableResponse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::ReleaseCallableRequest* Arena::CreateMaybeMessage< ::tensorflow::ReleaseCallableRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::ReleaseCallableRequest >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::ReleaseCallableResponse* Arena::CreateMaybeMessage< ::tensorflow::ReleaseCallableResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::ReleaseCallableResponse >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

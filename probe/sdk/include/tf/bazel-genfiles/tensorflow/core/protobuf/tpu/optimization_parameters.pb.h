// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/tpu/optimization_parameters.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/wrappers.pb.h>
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto 

namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[19];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto
namespace tensorflow {
namespace tpu {
class AdadeltaParameters;
class AdadeltaParametersDefaultTypeInternal;
extern AdadeltaParametersDefaultTypeInternal _AdadeltaParameters_default_instance_;
class AdagradParameters;
class AdagradParametersDefaultTypeInternal;
extern AdagradParametersDefaultTypeInternal _AdagradParameters_default_instance_;
class AdamParameters;
class AdamParametersDefaultTypeInternal;
extern AdamParametersDefaultTypeInternal _AdamParameters_default_instance_;
class CenteredRmsPropParameters;
class CenteredRmsPropParametersDefaultTypeInternal;
extern CenteredRmsPropParametersDefaultTypeInternal _CenteredRmsPropParameters_default_instance_;
class ClippingLimits;
class ClippingLimitsDefaultTypeInternal;
extern ClippingLimitsDefaultTypeInternal _ClippingLimits_default_instance_;
class DynamicLearningRate;
class DynamicLearningRateDefaultTypeInternal;
extern DynamicLearningRateDefaultTypeInternal _DynamicLearningRate_default_instance_;
class FtrlParameters;
class FtrlParametersDefaultTypeInternal;
extern FtrlParametersDefaultTypeInternal _FtrlParameters_default_instance_;
class GradientAccumulationStatus;
class GradientAccumulationStatusDefaultTypeInternal;
extern GradientAccumulationStatusDefaultTypeInternal _GradientAccumulationStatus_default_instance_;
class HotIdOptimizerConfiguration;
class HotIdOptimizerConfigurationDefaultTypeInternal;
extern HotIdOptimizerConfigurationDefaultTypeInternal _HotIdOptimizerConfiguration_default_instance_;
class LearningRate;
class LearningRateDefaultTypeInternal;
extern LearningRateDefaultTypeInternal _LearningRate_default_instance_;
class MdlAdagradLightParameters;
class MdlAdagradLightParametersDefaultTypeInternal;
extern MdlAdagradLightParametersDefaultTypeInternal _MdlAdagradLightParameters_default_instance_;
class MomentumParameters;
class MomentumParametersDefaultTypeInternal;
extern MomentumParametersDefaultTypeInternal _MomentumParameters_default_instance_;
class OptimizationParameters;
class OptimizationParametersDefaultTypeInternal;
extern OptimizationParametersDefaultTypeInternal _OptimizationParameters_default_instance_;
class ProximalAdagradParameters;
class ProximalAdagradParametersDefaultTypeInternal;
extern ProximalAdagradParametersDefaultTypeInternal _ProximalAdagradParameters_default_instance_;
class RmsPropParameters;
class RmsPropParametersDefaultTypeInternal;
extern RmsPropParametersDefaultTypeInternal _RmsPropParameters_default_instance_;
class StateVariableSpecification;
class StateVariableSpecificationDefaultTypeInternal;
extern StateVariableSpecificationDefaultTypeInternal _StateVariableSpecification_default_instance_;
class StateVariableSpecification_FillWithConstant;
class StateVariableSpecification_FillWithConstantDefaultTypeInternal;
extern StateVariableSpecification_FillWithConstantDefaultTypeInternal _StateVariableSpecification_FillWithConstant_default_instance_;
class StateVariableSpecification_UserDefined;
class StateVariableSpecification_UserDefinedDefaultTypeInternal;
extern StateVariableSpecification_UserDefinedDefaultTypeInternal _StateVariableSpecification_UserDefined_default_instance_;
class StochasticGradientDescentParameters;
class StochasticGradientDescentParametersDefaultTypeInternal;
extern StochasticGradientDescentParametersDefaultTypeInternal _StochasticGradientDescentParameters_default_instance_;
}  // namespace tpu
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::tpu::AdadeltaParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::AdadeltaParameters>(Arena*);
template<> ::tensorflow::tpu::AdagradParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::AdagradParameters>(Arena*);
template<> ::tensorflow::tpu::AdamParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::AdamParameters>(Arena*);
template<> ::tensorflow::tpu::CenteredRmsPropParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::CenteredRmsPropParameters>(Arena*);
template<> ::tensorflow::tpu::ClippingLimits* Arena::CreateMaybeMessage<::tensorflow::tpu::ClippingLimits>(Arena*);
template<> ::tensorflow::tpu::DynamicLearningRate* Arena::CreateMaybeMessage<::tensorflow::tpu::DynamicLearningRate>(Arena*);
template<> ::tensorflow::tpu::FtrlParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::FtrlParameters>(Arena*);
template<> ::tensorflow::tpu::GradientAccumulationStatus* Arena::CreateMaybeMessage<::tensorflow::tpu::GradientAccumulationStatus>(Arena*);
template<> ::tensorflow::tpu::HotIdOptimizerConfiguration* Arena::CreateMaybeMessage<::tensorflow::tpu::HotIdOptimizerConfiguration>(Arena*);
template<> ::tensorflow::tpu::LearningRate* Arena::CreateMaybeMessage<::tensorflow::tpu::LearningRate>(Arena*);
template<> ::tensorflow::tpu::MdlAdagradLightParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::MdlAdagradLightParameters>(Arena*);
template<> ::tensorflow::tpu::MomentumParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::MomentumParameters>(Arena*);
template<> ::tensorflow::tpu::OptimizationParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::OptimizationParameters>(Arena*);
template<> ::tensorflow::tpu::ProximalAdagradParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::ProximalAdagradParameters>(Arena*);
template<> ::tensorflow::tpu::RmsPropParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::RmsPropParameters>(Arena*);
template<> ::tensorflow::tpu::StateVariableSpecification* Arena::CreateMaybeMessage<::tensorflow::tpu::StateVariableSpecification>(Arena*);
template<> ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* Arena::CreateMaybeMessage<::tensorflow::tpu::StateVariableSpecification_FillWithConstant>(Arena*);
template<> ::tensorflow::tpu::StateVariableSpecification_UserDefined* Arena::CreateMaybeMessage<::tensorflow::tpu::StateVariableSpecification_UserDefined>(Arena*);
template<> ::tensorflow::tpu::StochasticGradientDescentParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::StochasticGradientDescentParameters>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {
namespace tpu {

enum GradientAccumulationStatus_Status {
  GradientAccumulationStatus_Status_UNSPECIFIED = 0,
  GradientAccumulationStatus_Status_ENABLED = 1,
  GradientAccumulationStatus_Status_DISABLED = 2,
  GradientAccumulationStatus_Status_GradientAccumulationStatus_Status_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  GradientAccumulationStatus_Status_GradientAccumulationStatus_Status_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool GradientAccumulationStatus_Status_IsValid(int value);
const GradientAccumulationStatus_Status GradientAccumulationStatus_Status_Status_MIN = GradientAccumulationStatus_Status_UNSPECIFIED;
const GradientAccumulationStatus_Status GradientAccumulationStatus_Status_Status_MAX = GradientAccumulationStatus_Status_DISABLED;
const int GradientAccumulationStatus_Status_Status_ARRAYSIZE = GradientAccumulationStatus_Status_Status_MAX + 1;

const ::google::protobuf::EnumDescriptor* GradientAccumulationStatus_Status_descriptor();
inline const ::std::string& GradientAccumulationStatus_Status_Name(GradientAccumulationStatus_Status value) {
  return ::google::protobuf::internal::NameOfEnum(
    GradientAccumulationStatus_Status_descriptor(), value);
}
inline bool GradientAccumulationStatus_Status_Parse(
    const ::std::string& name, GradientAccumulationStatus_Status* value) {
  return ::google::protobuf::internal::ParseNamedEnum<GradientAccumulationStatus_Status>(
    GradientAccumulationStatus_Status_descriptor(), name, value);
}
enum HotIdOptimizerConfiguration_Status {
  HotIdOptimizerConfiguration_Status_UNSPECIFIED = 0,
  HotIdOptimizerConfiguration_Status_ENABLED = 1,
  HotIdOptimizerConfiguration_Status_DISABLED = 2,
  HotIdOptimizerConfiguration_Status_HotIdOptimizerConfiguration_Status_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  HotIdOptimizerConfiguration_Status_HotIdOptimizerConfiguration_Status_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool HotIdOptimizerConfiguration_Status_IsValid(int value);
const HotIdOptimizerConfiguration_Status HotIdOptimizerConfiguration_Status_Status_MIN = HotIdOptimizerConfiguration_Status_UNSPECIFIED;
const HotIdOptimizerConfiguration_Status HotIdOptimizerConfiguration_Status_Status_MAX = HotIdOptimizerConfiguration_Status_DISABLED;
const int HotIdOptimizerConfiguration_Status_Status_ARRAYSIZE = HotIdOptimizerConfiguration_Status_Status_MAX + 1;

const ::google::protobuf::EnumDescriptor* HotIdOptimizerConfiguration_Status_descriptor();
inline const ::std::string& HotIdOptimizerConfiguration_Status_Name(HotIdOptimizerConfiguration_Status value) {
  return ::google::protobuf::internal::NameOfEnum(
    HotIdOptimizerConfiguration_Status_descriptor(), value);
}
inline bool HotIdOptimizerConfiguration_Status_Parse(
    const ::std::string& name, HotIdOptimizerConfiguration_Status* value) {
  return ::google::protobuf::internal::ParseNamedEnum<HotIdOptimizerConfiguration_Status>(
    HotIdOptimizerConfiguration_Status_descriptor(), name, value);
}
// ===================================================================

class ClippingLimits : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.ClippingLimits) */ {
 public:
  ClippingLimits();
  virtual ~ClippingLimits();

  ClippingLimits(const ClippingLimits& from);

  inline ClippingLimits& operator=(const ClippingLimits& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ClippingLimits(ClippingLimits&& from) noexcept
    : ClippingLimits() {
    *this = ::std::move(from);
  }

  inline ClippingLimits& operator=(ClippingLimits&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ClippingLimits& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ClippingLimits* internal_default_instance() {
    return reinterpret_cast<const ClippingLimits*>(
               &_ClippingLimits_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void Swap(ClippingLimits* other);
  friend void swap(ClippingLimits& a, ClippingLimits& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ClippingLimits* New() const final {
    return CreateMaybeMessage<ClippingLimits>(NULL);
  }

  ClippingLimits* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ClippingLimits>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ClippingLimits& from);
  void MergeFrom(const ClippingLimits& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ClippingLimits* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .google.protobuf.FloatValue lower = 1;
  bool has_lower() const;
  void clear_lower();
  static const int kLowerFieldNumber = 1;
  private:
  const ::google::protobuf::FloatValue& _internal_lower() const;
  public:
  const ::google::protobuf::FloatValue& lower() const;
  ::google::protobuf::FloatValue* release_lower();
  ::google::protobuf::FloatValue* mutable_lower();
  void set_allocated_lower(::google::protobuf::FloatValue* lower);

  // .google.protobuf.FloatValue upper = 2;
  bool has_upper() const;
  void clear_upper();
  static const int kUpperFieldNumber = 2;
  private:
  const ::google::protobuf::FloatValue& _internal_upper() const;
  public:
  const ::google::protobuf::FloatValue& upper() const;
  ::google::protobuf::FloatValue* release_upper();
  ::google::protobuf::FloatValue* mutable_upper();
  void set_allocated_upper(::google::protobuf::FloatValue* upper);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.ClippingLimits)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::FloatValue* lower_;
  ::google::protobuf::FloatValue* upper_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class DynamicLearningRate : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.DynamicLearningRate) */ {
 public:
  DynamicLearningRate();
  virtual ~DynamicLearningRate();

  DynamicLearningRate(const DynamicLearningRate& from);

  inline DynamicLearningRate& operator=(const DynamicLearningRate& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  DynamicLearningRate(DynamicLearningRate&& from) noexcept
    : DynamicLearningRate() {
    *this = ::std::move(from);
  }

  inline DynamicLearningRate& operator=(DynamicLearningRate&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const DynamicLearningRate& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DynamicLearningRate* internal_default_instance() {
    return reinterpret_cast<const DynamicLearningRate*>(
               &_DynamicLearningRate_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void Swap(DynamicLearningRate* other);
  friend void swap(DynamicLearningRate& a, DynamicLearningRate& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline DynamicLearningRate* New() const final {
    return CreateMaybeMessage<DynamicLearningRate>(NULL);
  }

  DynamicLearningRate* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<DynamicLearningRate>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const DynamicLearningRate& from);
  void MergeFrom(const DynamicLearningRate& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DynamicLearningRate* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int32 tag = 1;
  void clear_tag();
  static const int kTagFieldNumber = 1;
  ::google::protobuf::int32 tag() const;
  void set_tag(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.DynamicLearningRate)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int32 tag_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class LearningRate : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.LearningRate) */ {
 public:
  LearningRate();
  virtual ~LearningRate();

  LearningRate(const LearningRate& from);

  inline LearningRate& operator=(const LearningRate& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  LearningRate(LearningRate&& from) noexcept
    : LearningRate() {
    *this = ::std::move(from);
  }

  inline LearningRate& operator=(LearningRate&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const LearningRate& default_instance();

  enum LearningRateCase {
    kConstant = 1,
    kDynamic = 2,
    LEARNING_RATE_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const LearningRate* internal_default_instance() {
    return reinterpret_cast<const LearningRate*>(
               &_LearningRate_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void Swap(LearningRate* other);
  friend void swap(LearningRate& a, LearningRate& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline LearningRate* New() const final {
    return CreateMaybeMessage<LearningRate>(NULL);
  }

  LearningRate* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<LearningRate>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const LearningRate& from);
  void MergeFrom(const LearningRate& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LearningRate* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // float constant = 1;
  private:
  bool has_constant() const;
  public:
  void clear_constant();
  static const int kConstantFieldNumber = 1;
  float constant() const;
  void set_constant(float value);

  // .tensorflow.tpu.DynamicLearningRate dynamic = 2;
  bool has_dynamic() const;
  void clear_dynamic();
  static const int kDynamicFieldNumber = 2;
  private:
  const ::tensorflow::tpu::DynamicLearningRate& _internal_dynamic() const;
  public:
  const ::tensorflow::tpu::DynamicLearningRate& dynamic() const;
  ::tensorflow::tpu::DynamicLearningRate* release_dynamic();
  ::tensorflow::tpu::DynamicLearningRate* mutable_dynamic();
  void set_allocated_dynamic(::tensorflow::tpu::DynamicLearningRate* dynamic);

  void clear_learning_rate();
  LearningRateCase learning_rate_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.tpu.LearningRate)
 private:
  void set_has_constant();
  void set_has_dynamic();

  inline bool has_learning_rate() const;
  inline void clear_has_learning_rate();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  union LearningRateUnion {
    LearningRateUnion() {}
    float constant_;
    ::tensorflow::tpu::DynamicLearningRate* dynamic_;
  } learning_rate_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class AdagradParameters : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.AdagradParameters) */ {
 public:
  AdagradParameters();
  virtual ~AdagradParameters();

  AdagradParameters(const AdagradParameters& from);

  inline AdagradParameters& operator=(const AdagradParameters& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  AdagradParameters(AdagradParameters&& from) noexcept
    : AdagradParameters() {
    *this = ::std::move(from);
  }

  inline AdagradParameters& operator=(AdagradParameters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const AdagradParameters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AdagradParameters* internal_default_instance() {
    return reinterpret_cast<const AdagradParameters*>(
               &_AdagradParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void Swap(AdagradParameters* other);
  friend void swap(AdagradParameters& a, AdagradParameters& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline AdagradParameters* New() const final {
    return CreateMaybeMessage<AdagradParameters>(NULL);
  }

  AdagradParameters* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<AdagradParameters>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const AdagradParameters& from);
  void MergeFrom(const AdagradParameters& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AdagradParameters* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // float initial_accumulator = 1;
  void clear_initial_accumulator();
  static const int kInitialAccumulatorFieldNumber = 1;
  float initial_accumulator() const;
  void set_initial_accumulator(float value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.AdagradParameters)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  float initial_accumulator_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class StochasticGradientDescentParameters : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.StochasticGradientDescentParameters) */ {
 public:
  StochasticGradientDescentParameters();
  virtual ~StochasticGradientDescentParameters();

  StochasticGradientDescentParameters(const StochasticGradientDescentParameters& from);

  inline StochasticGradientDescentParameters& operator=(const StochasticGradientDescentParameters& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  StochasticGradientDescentParameters(StochasticGradientDescentParameters&& from) noexcept
    : StochasticGradientDescentParameters() {
    *this = ::std::move(from);
  }

  inline StochasticGradientDescentParameters& operator=(StochasticGradientDescentParameters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const StochasticGradientDescentParameters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const StochasticGradientDescentParameters* internal_default_instance() {
    return reinterpret_cast<const StochasticGradientDescentParameters*>(
               &_StochasticGradientDescentParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void Swap(StochasticGradientDescentParameters* other);
  friend void swap(StochasticGradientDescentParameters& a, StochasticGradientDescentParameters& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline StochasticGradientDescentParameters* New() const final {
    return CreateMaybeMessage<StochasticGradientDescentParameters>(NULL);
  }

  StochasticGradientDescentParameters* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<StochasticGradientDescentParameters>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const StochasticGradientDescentParameters& from);
  void MergeFrom(const StochasticGradientDescentParameters& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StochasticGradientDescentParameters* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.StochasticGradientDescentParameters)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class FtrlParameters : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.FtrlParameters) */ {
 public:
  FtrlParameters();
  virtual ~FtrlParameters();

  FtrlParameters(const FtrlParameters& from);

  inline FtrlParameters& operator=(const FtrlParameters& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  FtrlParameters(FtrlParameters&& from) noexcept
    : FtrlParameters() {
    *this = ::std::move(from);
  }

  inline FtrlParameters& operator=(FtrlParameters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const FtrlParameters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const FtrlParameters* internal_default_instance() {
    return reinterpret_cast<const FtrlParameters*>(
               &_FtrlParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  void Swap(FtrlParameters* other);
  friend void swap(FtrlParameters& a, FtrlParameters& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline FtrlParameters* New() const final {
    return CreateMaybeMessage<FtrlParameters>(NULL);
  }

  FtrlParameters* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<FtrlParameters>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const FtrlParameters& from);
  void MergeFrom(const FtrlParameters& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FtrlParameters* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // float l1 = 1;
  void clear_l1();
  static const int kL1FieldNumber = 1;
  float l1() const;
  void set_l1(float value);

  // float l2 = 2;
  void clear_l2();
  static const int kL2FieldNumber = 2;
  float l2() const;
  void set_l2(float value);

  // float lr_power = 3;
  void clear_lr_power();
  static const int kLrPowerFieldNumber = 3;
  float lr_power() const;
  void set_lr_power(float value);

  // float initial_accum = 4;
  void clear_initial_accum();
  static const int kInitialAccumFieldNumber = 4;
  float initial_accum() const;
  void set_initial_accum(float value);

  // float initial_linear = 5;
  void clear_initial_linear();
  static const int kInitialLinearFieldNumber = 5;
  float initial_linear() const;
  void set_initial_linear(float value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.FtrlParameters)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  float l1_;
  float l2_;
  float lr_power_;
  float initial_accum_;
  float initial_linear_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class AdamParameters : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.AdamParameters) */ {
 public:
  AdamParameters();
  virtual ~AdamParameters();

  AdamParameters(const AdamParameters& from);

  inline AdamParameters& operator=(const AdamParameters& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  AdamParameters(AdamParameters&& from) noexcept
    : AdamParameters() {
    *this = ::std::move(from);
  }

  inline AdamParameters& operator=(AdamParameters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const AdamParameters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AdamParameters* internal_default_instance() {
    return reinterpret_cast<const AdamParameters*>(
               &_AdamParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  void Swap(AdamParameters* other);
  friend void swap(AdamParameters& a, AdamParameters& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline AdamParameters* New() const final {
    return CreateMaybeMessage<AdamParameters>(NULL);
  }

  AdamParameters* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<AdamParameters>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const AdamParameters& from);
  void MergeFrom(const AdamParameters& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AdamParameters* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // float beta1 = 3;
  void clear_beta1();
  static const int kBeta1FieldNumber = 3;
  float beta1() const;
  void set_beta1(float value);

  // float beta2 = 4;
  void clear_beta2();
  static const int kBeta2FieldNumber = 4;
  float beta2() const;
  void set_beta2(float value);

  // float epsilon = 5;
  void clear_epsilon();
  static const int kEpsilonFieldNumber = 5;
  float epsilon() const;
  void set_epsilon(float value);

  // float initial_m = 6;
  void clear_initial_m();
  static const int kInitialMFieldNumber = 6;
  float initial_m() const;
  void set_initial_m(float value);

  // float initial_v = 7;
  void clear_initial_v();
  static const int kInitialVFieldNumber = 7;
  float initial_v() const;
  void set_initial_v(float value);

  // bool use_non_lazy_adam = 8;
  void clear_use_non_lazy_adam();
  static const int kUseNonLazyAdamFieldNumber = 8;
  bool use_non_lazy_adam() const;
  void set_use_non_lazy_adam(bool value);

  // bool use_sum_inside_sqrt = 10;
  void clear_use_sum_inside_sqrt();
  static const int kUseSumInsideSqrtFieldNumber = 10;
  bool use_sum_inside_sqrt() const;
  void set_use_sum_inside_sqrt(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.AdamParameters)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  float beta1_;
  float beta2_;
  float epsilon_;
  float initial_m_;
  float initial_v_;
  bool use_non_lazy_adam_;
  bool use_sum_inside_sqrt_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class MomentumParameters : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.MomentumParameters) */ {
 public:
  MomentumParameters();
  virtual ~MomentumParameters();

  MomentumParameters(const MomentumParameters& from);

  inline MomentumParameters& operator=(const MomentumParameters& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  MomentumParameters(MomentumParameters&& from) noexcept
    : MomentumParameters() {
    *this = ::std::move(from);
  }

  inline MomentumParameters& operator=(MomentumParameters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const MomentumParameters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MomentumParameters* internal_default_instance() {
    return reinterpret_cast<const MomentumParameters*>(
               &_MomentumParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  void Swap(MomentumParameters* other);
  friend void swap(MomentumParameters& a, MomentumParameters& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline MomentumParameters* New() const final {
    return CreateMaybeMessage<MomentumParameters>(NULL);
  }

  MomentumParameters* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<MomentumParameters>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const MomentumParameters& from);
  void MergeFrom(const MomentumParameters& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MomentumParameters* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // float momentum = 1;
  void clear_momentum();
  static const int kMomentumFieldNumber = 1;
  float momentum() const;
  void set_momentum(float value);

  // bool use_nesterov = 2;
  void clear_use_nesterov();
  static const int kUseNesterovFieldNumber = 2;
  bool use_nesterov() const;
  void set_use_nesterov(bool value);

  // float initial_accum = 3;
  void clear_initial_accum();
  static const int kInitialAccumFieldNumber = 3;
  float initial_accum() const;
  void set_initial_accum(float value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.MomentumParameters)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  float momentum_;
  bool use_nesterov_;
  float initial_accum_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class RmsPropParameters : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.RmsPropParameters) */ {
 public:
  RmsPropParameters();
  virtual ~RmsPropParameters();

  RmsPropParameters(const RmsPropParameters& from);

  inline RmsPropParameters& operator=(const RmsPropParameters& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  RmsPropParameters(RmsPropParameters&& from) noexcept
    : RmsPropParameters() {
    *this = ::std::move(from);
  }

  inline RmsPropParameters& operator=(RmsPropParameters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const RmsPropParameters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RmsPropParameters* internal_default_instance() {
    return reinterpret_cast<const RmsPropParameters*>(
               &_RmsPropParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  void Swap(RmsPropParameters* other);
  friend void swap(RmsPropParameters& a, RmsPropParameters& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RmsPropParameters* New() const final {
    return CreateMaybeMessage<RmsPropParameters>(NULL);
  }

  RmsPropParameters* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<RmsPropParameters>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const RmsPropParameters& from);
  void MergeFrom(const RmsPropParameters& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RmsPropParameters* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // float rho = 1;
  void clear_rho();
  static const int kRhoFieldNumber = 1;
  float rho() const;
  void set_rho(float value);

  // float momentum = 2;
  void clear_momentum();
  static const int kMomentumFieldNumber = 2;
  float momentum() const;
  void set_momentum(float value);

  // float epsilon = 3;
  void clear_epsilon();
  static const int kEpsilonFieldNumber = 3;
  float epsilon() const;
  void set_epsilon(float value);

  // float initial_ms = 4;
  void clear_initial_ms();
  static const int kInitialMsFieldNumber = 4;
  float initial_ms() const;
  void set_initial_ms(float value);

  // float initial_mom = 5;
  void clear_initial_mom();
  static const int kInitialMomFieldNumber = 5;
  float initial_mom() const;
  void set_initial_mom(float value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.RmsPropParameters)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  float rho_;
  float momentum_;
  float epsilon_;
  float initial_ms_;
  float initial_mom_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CenteredRmsPropParameters : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.CenteredRmsPropParameters) */ {
 public:
  CenteredRmsPropParameters();
  virtual ~CenteredRmsPropParameters();

  CenteredRmsPropParameters(const CenteredRmsPropParameters& from);

  inline CenteredRmsPropParameters& operator=(const CenteredRmsPropParameters& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CenteredRmsPropParameters(CenteredRmsPropParameters&& from) noexcept
    : CenteredRmsPropParameters() {
    *this = ::std::move(from);
  }

  inline CenteredRmsPropParameters& operator=(CenteredRmsPropParameters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const CenteredRmsPropParameters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CenteredRmsPropParameters* internal_default_instance() {
    return reinterpret_cast<const CenteredRmsPropParameters*>(
               &_CenteredRmsPropParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  void Swap(CenteredRmsPropParameters* other);
  friend void swap(CenteredRmsPropParameters& a, CenteredRmsPropParameters& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CenteredRmsPropParameters* New() const final {
    return CreateMaybeMessage<CenteredRmsPropParameters>(NULL);
  }

  CenteredRmsPropParameters* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CenteredRmsPropParameters>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CenteredRmsPropParameters& from);
  void MergeFrom(const CenteredRmsPropParameters& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CenteredRmsPropParameters* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // float rho = 1;
  void clear_rho();
  static const int kRhoFieldNumber = 1;
  float rho() const;
  void set_rho(float value);

  // float momentum = 2;
  void clear_momentum();
  static const int kMomentumFieldNumber = 2;
  float momentum() const;
  void set_momentum(float value);

  // float epsilon = 3;
  void clear_epsilon();
  static const int kEpsilonFieldNumber = 3;
  float epsilon() const;
  void set_epsilon(float value);

  // float initial_ms = 4;
  void clear_initial_ms();
  static const int kInitialMsFieldNumber = 4;
  float initial_ms() const;
  void set_initial_ms(float value);

  // float initial_mom = 5;
  void clear_initial_mom();
  static const int kInitialMomFieldNumber = 5;
  float initial_mom() const;
  void set_initial_mom(float value);

  // float initial_mg = 6;
  void clear_initial_mg();
  static const int kInitialMgFieldNumber = 6;
  float initial_mg() const;
  void set_initial_mg(float value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.CenteredRmsPropParameters)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  float rho_;
  float momentum_;
  float epsilon_;
  float initial_ms_;
  float initial_mom_;
  float initial_mg_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class MdlAdagradLightParameters : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.MdlAdagradLightParameters) */ {
 public:
  MdlAdagradLightParameters();
  virtual ~MdlAdagradLightParameters();

  MdlAdagradLightParameters(const MdlAdagradLightParameters& from);

  inline MdlAdagradLightParameters& operator=(const MdlAdagradLightParameters& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  MdlAdagradLightParameters(MdlAdagradLightParameters&& from) noexcept
    : MdlAdagradLightParameters() {
    *this = ::std::move(from);
  }

  inline MdlAdagradLightParameters& operator=(MdlAdagradLightParameters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const MdlAdagradLightParameters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MdlAdagradLightParameters* internal_default_instance() {
    return reinterpret_cast<const MdlAdagradLightParameters*>(
               &_MdlAdagradLightParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  void Swap(MdlAdagradLightParameters* other);
  friend void swap(MdlAdagradLightParameters& a, MdlAdagradLightParameters& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline MdlAdagradLightParameters* New() const final {
    return CreateMaybeMessage<MdlAdagradLightParameters>(NULL);
  }

  MdlAdagradLightParameters* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<MdlAdagradLightParameters>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const MdlAdagradLightParameters& from);
  void MergeFrom(const MdlAdagradLightParameters& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MdlAdagradLightParameters* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // float l2 = 1;
  void clear_l2();
  static const int kL2FieldNumber = 1;
  float l2() const;
  void set_l2(float value);

  // float lr_power = 2;
  void clear_lr_power();
  static const int kLrPowerFieldNumber = 2;
  float lr_power() const;
  void set_lr_power(float value);

  // float min_servable_mdl_benefit = 3;
  void clear_min_servable_mdl_benefit();
  static const int kMinServableMdlBenefitFieldNumber = 3;
  float min_servable_mdl_benefit() const;
  void set_min_servable_mdl_benefit(float value);

  // float mdl_mix_in_margin = 4;
  void clear_mdl_mix_in_margin();
  static const int kMdlMixInMarginFieldNumber = 4;
  float mdl_mix_in_margin() const;
  void set_mdl_mix_in_margin(float value);

  // float mdl_benefit_rampup_coeff = 5;
  void clear_mdl_benefit_rampup_coeff();
  static const int kMdlBenefitRampupCoeffFieldNumber = 5;
  float mdl_benefit_rampup_coeff() const;
  void set_mdl_benefit_rampup_coeff(float value);

  // float mdl_min_weight = 6;
  void clear_mdl_min_weight();
  static const int kMdlMinWeightFieldNumber = 6;
  float mdl_min_weight() const;
  void set_mdl_min_weight(float value);

  // float benefit_revisit_scale = 7;
  void clear_benefit_revisit_scale();
  static const int kBenefitRevisitScaleFieldNumber = 7;
  float benefit_revisit_scale() const;
  void set_benefit_revisit_scale(float value);

  // float max_event_benefit = 8;
  void clear_max_event_benefit();
  static const int kMaxEventBenefitFieldNumber = 8;
  float max_event_benefit() const;
  void set_max_event_benefit(float value);

  // float max_total_benefit = 9;
  void clear_max_total_benefit();
  static const int kMaxTotalBenefitFieldNumber = 9;
  float max_total_benefit() const;
  void set_max_total_benefit(float value);

  // float mdl_hard_limit = 10;
  void clear_mdl_hard_limit();
  static const int kMdlHardLimitFieldNumber = 10;
  float mdl_hard_limit() const;
  void set_mdl_hard_limit(float value);

  // bool hard_limit_min_benefit = 11;
  void clear_hard_limit_min_benefit();
  static const int kHardLimitMinBenefitFieldNumber = 11;
  bool hard_limit_min_benefit() const;
  void set_hard_limit_min_benefit(bool value);

  // bool mdl_regularize = 12;
  void clear_mdl_regularize();
  static const int kMdlRegularizeFieldNumber = 12;
  bool mdl_regularize() const;
  void set_mdl_regularize(bool value);

  // float initial_accumulator = 13;
  void clear_initial_accumulator();
  static const int kInitialAccumulatorFieldNumber = 13;
  float initial_accumulator() const;
  void set_initial_accumulator(float value);

  // float initial_weight = 14;
  void clear_initial_weight();
  static const int kInitialWeightFieldNumber = 14;
  float initial_weight() const;
  void set_initial_weight(float value);

  // float initial_benefit = 15;
  void clear_initial_benefit();
  static const int kInitialBenefitFieldNumber = 15;
  float initial_benefit() const;
  void set_initial_benefit(float value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.MdlAdagradLightParameters)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  float l2_;
  float lr_power_;
  float min_servable_mdl_benefit_;
  float mdl_mix_in_margin_;
  float mdl_benefit_rampup_coeff_;
  float mdl_min_weight_;
  float benefit_revisit_scale_;
  float max_event_benefit_;
  float max_total_benefit_;
  float mdl_hard_limit_;
  bool hard_limit_min_benefit_;
  bool mdl_regularize_;
  float initial_accumulator_;
  float initial_weight_;
  float initial_benefit_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class AdadeltaParameters : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.AdadeltaParameters) */ {
 public:
  AdadeltaParameters();
  virtual ~AdadeltaParameters();

  AdadeltaParameters(const AdadeltaParameters& from);

  inline AdadeltaParameters& operator=(const AdadeltaParameters& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  AdadeltaParameters(AdadeltaParameters&& from) noexcept
    : AdadeltaParameters() {
    *this = ::std::move(from);
  }

  inline AdadeltaParameters& operator=(AdadeltaParameters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const AdadeltaParameters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AdadeltaParameters* internal_default_instance() {
    return reinterpret_cast<const AdadeltaParameters*>(
               &_AdadeltaParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  void Swap(AdadeltaParameters* other);
  friend void swap(AdadeltaParameters& a, AdadeltaParameters& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline AdadeltaParameters* New() const final {
    return CreateMaybeMessage<AdadeltaParameters>(NULL);
  }

  AdadeltaParameters* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<AdadeltaParameters>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const AdadeltaParameters& from);
  void MergeFrom(const AdadeltaParameters& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AdadeltaParameters* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // float rho = 1;
  void clear_rho();
  static const int kRhoFieldNumber = 1;
  float rho() const;
  void set_rho(float value);

  // float epsilon = 2;
  void clear_epsilon();
  static const int kEpsilonFieldNumber = 2;
  float epsilon() const;
  void set_epsilon(float value);

  // float initial_accumulator = 3;
  void clear_initial_accumulator();
  static const int kInitialAccumulatorFieldNumber = 3;
  float initial_accumulator() const;
  void set_initial_accumulator(float value);

  // float initial_update = 4;
  void clear_initial_update();
  static const int kInitialUpdateFieldNumber = 4;
  float initial_update() const;
  void set_initial_update(float value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.AdadeltaParameters)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  float rho_;
  float epsilon_;
  float initial_accumulator_;
  float initial_update_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ProximalAdagradParameters : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.ProximalAdagradParameters) */ {
 public:
  ProximalAdagradParameters();
  virtual ~ProximalAdagradParameters();

  ProximalAdagradParameters(const ProximalAdagradParameters& from);

  inline ProximalAdagradParameters& operator=(const ProximalAdagradParameters& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ProximalAdagradParameters(ProximalAdagradParameters&& from) noexcept
    : ProximalAdagradParameters() {
    *this = ::std::move(from);
  }

  inline ProximalAdagradParameters& operator=(ProximalAdagradParameters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ProximalAdagradParameters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ProximalAdagradParameters* internal_default_instance() {
    return reinterpret_cast<const ProximalAdagradParameters*>(
               &_ProximalAdagradParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  void Swap(ProximalAdagradParameters* other);
  friend void swap(ProximalAdagradParameters& a, ProximalAdagradParameters& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ProximalAdagradParameters* New() const final {
    return CreateMaybeMessage<ProximalAdagradParameters>(NULL);
  }

  ProximalAdagradParameters* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ProximalAdagradParameters>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ProximalAdagradParameters& from);
  void MergeFrom(const ProximalAdagradParameters& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProximalAdagradParameters* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // float l1 = 1;
  void clear_l1();
  static const int kL1FieldNumber = 1;
  float l1() const;
  void set_l1(float value);

  // float l2 = 2;
  void clear_l2();
  static const int kL2FieldNumber = 2;
  float l2() const;
  void set_l2(float value);

  // float initial_accumulator = 3;
  void clear_initial_accumulator();
  static const int kInitialAccumulatorFieldNumber = 3;
  float initial_accumulator() const;
  void set_initial_accumulator(float value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.ProximalAdagradParameters)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  float l1_;
  float l2_;
  float initial_accumulator_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class GradientAccumulationStatus : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.GradientAccumulationStatus) */ {
 public:
  GradientAccumulationStatus();
  virtual ~GradientAccumulationStatus();

  GradientAccumulationStatus(const GradientAccumulationStatus& from);

  inline GradientAccumulationStatus& operator=(const GradientAccumulationStatus& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  GradientAccumulationStatus(GradientAccumulationStatus&& from) noexcept
    : GradientAccumulationStatus() {
    *this = ::std::move(from);
  }

  inline GradientAccumulationStatus& operator=(GradientAccumulationStatus&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const GradientAccumulationStatus& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GradientAccumulationStatus* internal_default_instance() {
    return reinterpret_cast<const GradientAccumulationStatus*>(
               &_GradientAccumulationStatus_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  void Swap(GradientAccumulationStatus* other);
  friend void swap(GradientAccumulationStatus& a, GradientAccumulationStatus& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline GradientAccumulationStatus* New() const final {
    return CreateMaybeMessage<GradientAccumulationStatus>(NULL);
  }

  GradientAccumulationStatus* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<GradientAccumulationStatus>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const GradientAccumulationStatus& from);
  void MergeFrom(const GradientAccumulationStatus& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GradientAccumulationStatus* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef GradientAccumulationStatus_Status Status;
  static const Status UNSPECIFIED =
    GradientAccumulationStatus_Status_UNSPECIFIED;
  static const Status ENABLED =
    GradientAccumulationStatus_Status_ENABLED;
  static const Status DISABLED =
    GradientAccumulationStatus_Status_DISABLED;
  static inline bool Status_IsValid(int value) {
    return GradientAccumulationStatus_Status_IsValid(value);
  }
  static const Status Status_MIN =
    GradientAccumulationStatus_Status_Status_MIN;
  static const Status Status_MAX =
    GradientAccumulationStatus_Status_Status_MAX;
  static const int Status_ARRAYSIZE =
    GradientAccumulationStatus_Status_Status_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  Status_descriptor() {
    return GradientAccumulationStatus_Status_descriptor();
  }
  static inline const ::std::string& Status_Name(Status value) {
    return GradientAccumulationStatus_Status_Name(value);
  }
  static inline bool Status_Parse(const ::std::string& name,
      Status* value) {
    return GradientAccumulationStatus_Status_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.GradientAccumulationStatus)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class HotIdOptimizerConfiguration : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.HotIdOptimizerConfiguration) */ {
 public:
  HotIdOptimizerConfiguration();
  virtual ~HotIdOptimizerConfiguration();

  HotIdOptimizerConfiguration(const HotIdOptimizerConfiguration& from);

  inline HotIdOptimizerConfiguration& operator=(const HotIdOptimizerConfiguration& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  HotIdOptimizerConfiguration(HotIdOptimizerConfiguration&& from) noexcept
    : HotIdOptimizerConfiguration() {
    *this = ::std::move(from);
  }

  inline HotIdOptimizerConfiguration& operator=(HotIdOptimizerConfiguration&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const HotIdOptimizerConfiguration& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const HotIdOptimizerConfiguration* internal_default_instance() {
    return reinterpret_cast<const HotIdOptimizerConfiguration*>(
               &_HotIdOptimizerConfiguration_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  void Swap(HotIdOptimizerConfiguration* other);
  friend void swap(HotIdOptimizerConfiguration& a, HotIdOptimizerConfiguration& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline HotIdOptimizerConfiguration* New() const final {
    return CreateMaybeMessage<HotIdOptimizerConfiguration>(NULL);
  }

  HotIdOptimizerConfiguration* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<HotIdOptimizerConfiguration>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const HotIdOptimizerConfiguration& from);
  void MergeFrom(const HotIdOptimizerConfiguration& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HotIdOptimizerConfiguration* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef HotIdOptimizerConfiguration_Status Status;
  static const Status UNSPECIFIED =
    HotIdOptimizerConfiguration_Status_UNSPECIFIED;
  static const Status ENABLED =
    HotIdOptimizerConfiguration_Status_ENABLED;
  static const Status DISABLED =
    HotIdOptimizerConfiguration_Status_DISABLED;
  static inline bool Status_IsValid(int value) {
    return HotIdOptimizerConfiguration_Status_IsValid(value);
  }
  static const Status Status_MIN =
    HotIdOptimizerConfiguration_Status_Status_MIN;
  static const Status Status_MAX =
    HotIdOptimizerConfiguration_Status_Status_MAX;
  static const int Status_ARRAYSIZE =
    HotIdOptimizerConfiguration_Status_Status_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  Status_descriptor() {
    return HotIdOptimizerConfiguration_Status_descriptor();
  }
  static inline const ::std::string& Status_Name(Status value) {
    return HotIdOptimizerConfiguration_Status_Name(value);
  }
  static inline bool Status_Parse(const ::std::string& name,
      Status* value) {
    return HotIdOptimizerConfiguration_Status_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // .tensorflow.tpu.HotIdOptimizerConfiguration.Status status = 1;
  void clear_status();
  static const int kStatusFieldNumber = 1;
  ::tensorflow::tpu::HotIdOptimizerConfiguration_Status status() const;
  void set_status(::tensorflow::tpu::HotIdOptimizerConfiguration_Status value);

  // float frequency_threshold = 2;
  void clear_frequency_threshold();
  static const int kFrequencyThresholdFieldNumber = 2;
  float frequency_threshold() const;
  void set_frequency_threshold(float value);

  // int32 max_id_count = 3;
  void clear_max_id_count();
  static const int kMaxIdCountFieldNumber = 3;
  ::google::protobuf::int32 max_id_count() const;
  void set_max_id_count(::google::protobuf::int32 value);

  // int32 max_slot_count = 4;
  void clear_max_slot_count();
  static const int kMaxSlotCountFieldNumber = 4;
  ::google::protobuf::int32 max_slot_count() const;
  void set_max_slot_count(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.HotIdOptimizerConfiguration)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  int status_;
  float frequency_threshold_;
  ::google::protobuf::int32 max_id_count_;
  ::google::protobuf::int32 max_slot_count_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class OptimizationParameters : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.OptimizationParameters) */ {
 public:
  OptimizationParameters();
  virtual ~OptimizationParameters();

  OptimizationParameters(const OptimizationParameters& from);

  inline OptimizationParameters& operator=(const OptimizationParameters& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  OptimizationParameters(OptimizationParameters&& from) noexcept
    : OptimizationParameters() {
    *this = ::std::move(from);
  }

  inline OptimizationParameters& operator=(OptimizationParameters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const OptimizationParameters& default_instance();

  enum ParametersCase {
    kAdagrad = 3,
    kStochasticGradientDescent = 4,
    kFtrl = 5,
    kAdam = 6,
    kMomentum = 8,
    kRmsProp = 9,
    kCenteredRmsProp = 10,
    kMdlAdagradLight = 11,
    kAdadelta = 12,
    kProximalAdagrad = 14,
    PARAMETERS_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const OptimizationParameters* internal_default_instance() {
    return reinterpret_cast<const OptimizationParameters*>(
               &_OptimizationParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  void Swap(OptimizationParameters* other);
  friend void swap(OptimizationParameters& a, OptimizationParameters& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline OptimizationParameters* New() const final {
    return CreateMaybeMessage<OptimizationParameters>(NULL);
  }

  OptimizationParameters* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<OptimizationParameters>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const OptimizationParameters& from);
  void MergeFrom(const OptimizationParameters& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OptimizationParameters* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .tensorflow.tpu.ClippingLimits clipping_limits = 2;
  bool has_clipping_limits() const;
  void clear_clipping_limits();
  static const int kClippingLimitsFieldNumber = 2;
  private:
  const ::tensorflow::tpu::ClippingLimits& _internal_clipping_limits() const;
  public:
  const ::tensorflow::tpu::ClippingLimits& clipping_limits() const;
  ::tensorflow::tpu::ClippingLimits* release_clipping_limits();
  ::tensorflow::tpu::ClippingLimits* mutable_clipping_limits();
  void set_allocated_clipping_limits(::tensorflow::tpu::ClippingLimits* clipping_limits);

  // .tensorflow.tpu.ClippingLimits gradient_clipping_limits = 7;
  bool has_gradient_clipping_limits() const;
  void clear_gradient_clipping_limits();
  static const int kGradientClippingLimitsFieldNumber = 7;
  private:
  const ::tensorflow::tpu::ClippingLimits& _internal_gradient_clipping_limits() const;
  public:
  const ::tensorflow::tpu::ClippingLimits& gradient_clipping_limits() const;
  ::tensorflow::tpu::ClippingLimits* release_gradient_clipping_limits();
  ::tensorflow::tpu::ClippingLimits* mutable_gradient_clipping_limits();
  void set_allocated_gradient_clipping_limits(::tensorflow::tpu::ClippingLimits* gradient_clipping_limits);

  // .tensorflow.tpu.LearningRate learning_rate = 13;
  bool has_learning_rate() const;
  void clear_learning_rate();
  static const int kLearningRateFieldNumber = 13;
  private:
  const ::tensorflow::tpu::LearningRate& _internal_learning_rate() const;
  public:
  const ::tensorflow::tpu::LearningRate& learning_rate() const;
  ::tensorflow::tpu::LearningRate* release_learning_rate();
  ::tensorflow::tpu::LearningRate* mutable_learning_rate();
  void set_allocated_learning_rate(::tensorflow::tpu::LearningRate* learning_rate);

  // .tensorflow.tpu.HotIdOptimizerConfiguration hot_id_optimizer_configuration = 18;
  bool has_hot_id_optimizer_configuration() const;
  void clear_hot_id_optimizer_configuration();
  static const int kHotIdOptimizerConfigurationFieldNumber = 18;
  private:
  const ::tensorflow::tpu::HotIdOptimizerConfiguration& _internal_hot_id_optimizer_configuration() const;
  public:
  const ::tensorflow::tpu::HotIdOptimizerConfiguration& hot_id_optimizer_configuration() const;
  ::tensorflow::tpu::HotIdOptimizerConfiguration* release_hot_id_optimizer_configuration();
  ::tensorflow::tpu::HotIdOptimizerConfiguration* mutable_hot_id_optimizer_configuration();
  void set_allocated_hot_id_optimizer_configuration(::tensorflow::tpu::HotIdOptimizerConfiguration* hot_id_optimizer_configuration);

  // float weight_decay_factor = 16;
  void clear_weight_decay_factor();
  static const int kWeightDecayFactorFieldNumber = 16;
  float weight_decay_factor() const;
  void set_weight_decay_factor(float value);

  // .tensorflow.tpu.GradientAccumulationStatus.Status gradient_accumulation_status = 17;
  void clear_gradient_accumulation_status();
  static const int kGradientAccumulationStatusFieldNumber = 17;
  ::tensorflow::tpu::GradientAccumulationStatus_Status gradient_accumulation_status() const;
  void set_gradient_accumulation_status(::tensorflow::tpu::GradientAccumulationStatus_Status value);

  // .tensorflow.tpu.AdagradParameters adagrad = 3;
  bool has_adagrad() const;
  void clear_adagrad();
  static const int kAdagradFieldNumber = 3;
  private:
  const ::tensorflow::tpu::AdagradParameters& _internal_adagrad() const;
  public:
  const ::tensorflow::tpu::AdagradParameters& adagrad() const;
  ::tensorflow::tpu::AdagradParameters* release_adagrad();
  ::tensorflow::tpu::AdagradParameters* mutable_adagrad();
  void set_allocated_adagrad(::tensorflow::tpu::AdagradParameters* adagrad);

  // .tensorflow.tpu.StochasticGradientDescentParameters stochastic_gradient_descent = 4;
  bool has_stochastic_gradient_descent() const;
  void clear_stochastic_gradient_descent();
  static const int kStochasticGradientDescentFieldNumber = 4;
  private:
  const ::tensorflow::tpu::StochasticGradientDescentParameters& _internal_stochastic_gradient_descent() const;
  public:
  const ::tensorflow::tpu::StochasticGradientDescentParameters& stochastic_gradient_descent() const;
  ::tensorflow::tpu::StochasticGradientDescentParameters* release_stochastic_gradient_descent();
  ::tensorflow::tpu::StochasticGradientDescentParameters* mutable_stochastic_gradient_descent();
  void set_allocated_stochastic_gradient_descent(::tensorflow::tpu::StochasticGradientDescentParameters* stochastic_gradient_descent);

  // .tensorflow.tpu.FtrlParameters ftrl = 5;
  bool has_ftrl() const;
  void clear_ftrl();
  static const int kFtrlFieldNumber = 5;
  private:
  const ::tensorflow::tpu::FtrlParameters& _internal_ftrl() const;
  public:
  const ::tensorflow::tpu::FtrlParameters& ftrl() const;
  ::tensorflow::tpu::FtrlParameters* release_ftrl();
  ::tensorflow::tpu::FtrlParameters* mutable_ftrl();
  void set_allocated_ftrl(::tensorflow::tpu::FtrlParameters* ftrl);

  // .tensorflow.tpu.AdamParameters adam = 6;
  bool has_adam() const;
  void clear_adam();
  static const int kAdamFieldNumber = 6;
  private:
  const ::tensorflow::tpu::AdamParameters& _internal_adam() const;
  public:
  const ::tensorflow::tpu::AdamParameters& adam() const;
  ::tensorflow::tpu::AdamParameters* release_adam();
  ::tensorflow::tpu::AdamParameters* mutable_adam();
  void set_allocated_adam(::tensorflow::tpu::AdamParameters* adam);

  // .tensorflow.tpu.MomentumParameters momentum = 8;
  bool has_momentum() const;
  void clear_momentum();
  static const int kMomentumFieldNumber = 8;
  private:
  const ::tensorflow::tpu::MomentumParameters& _internal_momentum() const;
  public:
  const ::tensorflow::tpu::MomentumParameters& momentum() const;
  ::tensorflow::tpu::MomentumParameters* release_momentum();
  ::tensorflow::tpu::MomentumParameters* mutable_momentum();
  void set_allocated_momentum(::tensorflow::tpu::MomentumParameters* momentum);

  // .tensorflow.tpu.RmsPropParameters rms_prop = 9;
  bool has_rms_prop() const;
  void clear_rms_prop();
  static const int kRmsPropFieldNumber = 9;
  private:
  const ::tensorflow::tpu::RmsPropParameters& _internal_rms_prop() const;
  public:
  const ::tensorflow::tpu::RmsPropParameters& rms_prop() const;
  ::tensorflow::tpu::RmsPropParameters* release_rms_prop();
  ::tensorflow::tpu::RmsPropParameters* mutable_rms_prop();
  void set_allocated_rms_prop(::tensorflow::tpu::RmsPropParameters* rms_prop);

  // .tensorflow.tpu.CenteredRmsPropParameters centered_rms_prop = 10;
  bool has_centered_rms_prop() const;
  void clear_centered_rms_prop();
  static const int kCenteredRmsPropFieldNumber = 10;
  private:
  const ::tensorflow::tpu::CenteredRmsPropParameters& _internal_centered_rms_prop() const;
  public:
  const ::tensorflow::tpu::CenteredRmsPropParameters& centered_rms_prop() const;
  ::tensorflow::tpu::CenteredRmsPropParameters* release_centered_rms_prop();
  ::tensorflow::tpu::CenteredRmsPropParameters* mutable_centered_rms_prop();
  void set_allocated_centered_rms_prop(::tensorflow::tpu::CenteredRmsPropParameters* centered_rms_prop);

  // .tensorflow.tpu.MdlAdagradLightParameters mdl_adagrad_light = 11;
  bool has_mdl_adagrad_light() const;
  void clear_mdl_adagrad_light();
  static const int kMdlAdagradLightFieldNumber = 11;
  private:
  const ::tensorflow::tpu::MdlAdagradLightParameters& _internal_mdl_adagrad_light() const;
  public:
  const ::tensorflow::tpu::MdlAdagradLightParameters& mdl_adagrad_light() const;
  ::tensorflow::tpu::MdlAdagradLightParameters* release_mdl_adagrad_light();
  ::tensorflow::tpu::MdlAdagradLightParameters* mutable_mdl_adagrad_light();
  void set_allocated_mdl_adagrad_light(::tensorflow::tpu::MdlAdagradLightParameters* mdl_adagrad_light);

  // .tensorflow.tpu.AdadeltaParameters adadelta = 12;
  bool has_adadelta() const;
  void clear_adadelta();
  static const int kAdadeltaFieldNumber = 12;
  private:
  const ::tensorflow::tpu::AdadeltaParameters& _internal_adadelta() const;
  public:
  const ::tensorflow::tpu::AdadeltaParameters& adadelta() const;
  ::tensorflow::tpu::AdadeltaParameters* release_adadelta();
  ::tensorflow::tpu::AdadeltaParameters* mutable_adadelta();
  void set_allocated_adadelta(::tensorflow::tpu::AdadeltaParameters* adadelta);

  // .tensorflow.tpu.ProximalAdagradParameters proximal_adagrad = 14;
  bool has_proximal_adagrad() const;
  void clear_proximal_adagrad();
  static const int kProximalAdagradFieldNumber = 14;
  private:
  const ::tensorflow::tpu::ProximalAdagradParameters& _internal_proximal_adagrad() const;
  public:
  const ::tensorflow::tpu::ProximalAdagradParameters& proximal_adagrad() const;
  ::tensorflow::tpu::ProximalAdagradParameters* release_proximal_adagrad();
  ::tensorflow::tpu::ProximalAdagradParameters* mutable_proximal_adagrad();
  void set_allocated_proximal_adagrad(::tensorflow::tpu::ProximalAdagradParameters* proximal_adagrad);

  void clear_parameters();
  ParametersCase parameters_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.tpu.OptimizationParameters)
 private:
  void set_has_adagrad();
  void set_has_stochastic_gradient_descent();
  void set_has_ftrl();
  void set_has_adam();
  void set_has_momentum();
  void set_has_rms_prop();
  void set_has_centered_rms_prop();
  void set_has_mdl_adagrad_light();
  void set_has_adadelta();
  void set_has_proximal_adagrad();

  inline bool has_parameters() const;
  inline void clear_has_parameters();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::tensorflow::tpu::ClippingLimits* clipping_limits_;
  ::tensorflow::tpu::ClippingLimits* gradient_clipping_limits_;
  ::tensorflow::tpu::LearningRate* learning_rate_;
  ::tensorflow::tpu::HotIdOptimizerConfiguration* hot_id_optimizer_configuration_;
  float weight_decay_factor_;
  int gradient_accumulation_status_;
  union ParametersUnion {
    ParametersUnion() {}
    ::tensorflow::tpu::AdagradParameters* adagrad_;
    ::tensorflow::tpu::StochasticGradientDescentParameters* stochastic_gradient_descent_;
    ::tensorflow::tpu::FtrlParameters* ftrl_;
    ::tensorflow::tpu::AdamParameters* adam_;
    ::tensorflow::tpu::MomentumParameters* momentum_;
    ::tensorflow::tpu::RmsPropParameters* rms_prop_;
    ::tensorflow::tpu::CenteredRmsPropParameters* centered_rms_prop_;
    ::tensorflow::tpu::MdlAdagradLightParameters* mdl_adagrad_light_;
    ::tensorflow::tpu::AdadeltaParameters* adadelta_;
    ::tensorflow::tpu::ProximalAdagradParameters* proximal_adagrad_;
  } parameters_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class StateVariableSpecification_UserDefined : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.StateVariableSpecification.UserDefined) */ {
 public:
  StateVariableSpecification_UserDefined();
  virtual ~StateVariableSpecification_UserDefined();

  StateVariableSpecification_UserDefined(const StateVariableSpecification_UserDefined& from);

  inline StateVariableSpecification_UserDefined& operator=(const StateVariableSpecification_UserDefined& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  StateVariableSpecification_UserDefined(StateVariableSpecification_UserDefined&& from) noexcept
    : StateVariableSpecification_UserDefined() {
    *this = ::std::move(from);
  }

  inline StateVariableSpecification_UserDefined& operator=(StateVariableSpecification_UserDefined&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const StateVariableSpecification_UserDefined& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const StateVariableSpecification_UserDefined* internal_default_instance() {
    return reinterpret_cast<const StateVariableSpecification_UserDefined*>(
               &_StateVariableSpecification_UserDefined_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  void Swap(StateVariableSpecification_UserDefined* other);
  friend void swap(StateVariableSpecification_UserDefined& a, StateVariableSpecification_UserDefined& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline StateVariableSpecification_UserDefined* New() const final {
    return CreateMaybeMessage<StateVariableSpecification_UserDefined>(NULL);
  }

  StateVariableSpecification_UserDefined* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<StateVariableSpecification_UserDefined>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const StateVariableSpecification_UserDefined& from);
  void MergeFrom(const StateVariableSpecification_UserDefined& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StateVariableSpecification_UserDefined* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // double padding_initial_value = 1;
  void clear_padding_initial_value();
  static const int kPaddingInitialValueFieldNumber = 1;
  double padding_initial_value() const;
  void set_padding_initial_value(double value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.StateVariableSpecification.UserDefined)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  double padding_initial_value_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class StateVariableSpecification_FillWithConstant : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.StateVariableSpecification.FillWithConstant) */ {
 public:
  StateVariableSpecification_FillWithConstant();
  virtual ~StateVariableSpecification_FillWithConstant();

  StateVariableSpecification_FillWithConstant(const StateVariableSpecification_FillWithConstant& from);

  inline StateVariableSpecification_FillWithConstant& operator=(const StateVariableSpecification_FillWithConstant& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  StateVariableSpecification_FillWithConstant(StateVariableSpecification_FillWithConstant&& from) noexcept
    : StateVariableSpecification_FillWithConstant() {
    *this = ::std::move(from);
  }

  inline StateVariableSpecification_FillWithConstant& operator=(StateVariableSpecification_FillWithConstant&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const StateVariableSpecification_FillWithConstant& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const StateVariableSpecification_FillWithConstant* internal_default_instance() {
    return reinterpret_cast<const StateVariableSpecification_FillWithConstant*>(
               &_StateVariableSpecification_FillWithConstant_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  void Swap(StateVariableSpecification_FillWithConstant* other);
  friend void swap(StateVariableSpecification_FillWithConstant& a, StateVariableSpecification_FillWithConstant& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline StateVariableSpecification_FillWithConstant* New() const final {
    return CreateMaybeMessage<StateVariableSpecification_FillWithConstant>(NULL);
  }

  StateVariableSpecification_FillWithConstant* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<StateVariableSpecification_FillWithConstant>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const StateVariableSpecification_FillWithConstant& from);
  void MergeFrom(const StateVariableSpecification_FillWithConstant& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StateVariableSpecification_FillWithConstant* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // double initial_value = 1;
  void clear_initial_value();
  static const int kInitialValueFieldNumber = 1;
  double initial_value() const;
  void set_initial_value(double value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.StateVariableSpecification.FillWithConstant)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  double initial_value_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class StateVariableSpecification : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.StateVariableSpecification) */ {
 public:
  StateVariableSpecification();
  virtual ~StateVariableSpecification();

  StateVariableSpecification(const StateVariableSpecification& from);

  inline StateVariableSpecification& operator=(const StateVariableSpecification& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  StateVariableSpecification(StateVariableSpecification&& from) noexcept
    : StateVariableSpecification() {
    *this = ::std::move(from);
  }

  inline StateVariableSpecification& operator=(StateVariableSpecification&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const StateVariableSpecification& default_instance();

  enum UsageCase {
    kUserDefined = 2,
    kFillWithConstant = 3,
    USAGE_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const StateVariableSpecification* internal_default_instance() {
    return reinterpret_cast<const StateVariableSpecification*>(
               &_StateVariableSpecification_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  void Swap(StateVariableSpecification* other);
  friend void swap(StateVariableSpecification& a, StateVariableSpecification& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline StateVariableSpecification* New() const final {
    return CreateMaybeMessage<StateVariableSpecification>(NULL);
  }

  StateVariableSpecification* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<StateVariableSpecification>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const StateVariableSpecification& from);
  void MergeFrom(const StateVariableSpecification& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StateVariableSpecification* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef StateVariableSpecification_UserDefined UserDefined;
  typedef StateVariableSpecification_FillWithConstant FillWithConstant;

  // accessors -------------------------------------------------------

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // .tensorflow.tpu.StateVariableSpecification.UserDefined user_defined = 2;
  bool has_user_defined() const;
  void clear_user_defined();
  static const int kUserDefinedFieldNumber = 2;
  private:
  const ::tensorflow::tpu::StateVariableSpecification_UserDefined& _internal_user_defined() const;
  public:
  const ::tensorflow::tpu::StateVariableSpecification_UserDefined& user_defined() const;
  ::tensorflow::tpu::StateVariableSpecification_UserDefined* release_user_defined();
  ::tensorflow::tpu::StateVariableSpecification_UserDefined* mutable_user_defined();
  void set_allocated_user_defined(::tensorflow::tpu::StateVariableSpecification_UserDefined* user_defined);

  // .tensorflow.tpu.StateVariableSpecification.FillWithConstant fill_with_constant = 3;
  bool has_fill_with_constant() const;
  void clear_fill_with_constant();
  static const int kFillWithConstantFieldNumber = 3;
  private:
  const ::tensorflow::tpu::StateVariableSpecification_FillWithConstant& _internal_fill_with_constant() const;
  public:
  const ::tensorflow::tpu::StateVariableSpecification_FillWithConstant& fill_with_constant() const;
  ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* release_fill_with_constant();
  ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* mutable_fill_with_constant();
  void set_allocated_fill_with_constant(::tensorflow::tpu::StateVariableSpecification_FillWithConstant* fill_with_constant);

  void clear_usage();
  UsageCase usage_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.tpu.StateVariableSpecification)
 private:
  void set_has_user_defined();
  void set_has_fill_with_constant();

  inline bool has_usage() const;
  inline void clear_has_usage();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  union UsageUnion {
    UsageUnion() {}
    ::tensorflow::tpu::StateVariableSpecification_UserDefined* user_defined_;
    ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* fill_with_constant_;
  } usage_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ClippingLimits

// .google.protobuf.FloatValue lower = 1;
inline bool ClippingLimits::has_lower() const {
  return this != internal_default_instance() && lower_ != NULL;
}
inline const ::google::protobuf::FloatValue& ClippingLimits::_internal_lower() const {
  return *lower_;
}
inline const ::google::protobuf::FloatValue& ClippingLimits::lower() const {
  const ::google::protobuf::FloatValue* p = lower_;
  // @@protoc_insertion_point(field_get:tensorflow.tpu.ClippingLimits.lower)
  return p != NULL ? *p : *reinterpret_cast<const ::google::protobuf::FloatValue*>(
      &::google::protobuf::_FloatValue_default_instance_);
}
inline ::google::protobuf::FloatValue* ClippingLimits::release_lower() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.ClippingLimits.lower)
  
  ::google::protobuf::FloatValue* temp = lower_;
  lower_ = NULL;
  return temp;
}
inline ::google::protobuf::FloatValue* ClippingLimits::mutable_lower() {
  
  if (lower_ == NULL) {
    auto* p = CreateMaybeMessage<::google::protobuf::FloatValue>(GetArenaNoVirtual());
    lower_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.ClippingLimits.lower)
  return lower_;
}
inline void ClippingLimits::set_allocated_lower(::google::protobuf::FloatValue* lower) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(lower_);
  }
  if (lower) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(lower)->GetArena();
    if (message_arena != submessage_arena) {
      lower = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, lower, submessage_arena);
    }
    
  } else {
    
  }
  lower_ = lower;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.ClippingLimits.lower)
}

// .google.protobuf.FloatValue upper = 2;
inline bool ClippingLimits::has_upper() const {
  return this != internal_default_instance() && upper_ != NULL;
}
inline const ::google::protobuf::FloatValue& ClippingLimits::_internal_upper() const {
  return *upper_;
}
inline const ::google::protobuf::FloatValue& ClippingLimits::upper() const {
  const ::google::protobuf::FloatValue* p = upper_;
  // @@protoc_insertion_point(field_get:tensorflow.tpu.ClippingLimits.upper)
  return p != NULL ? *p : *reinterpret_cast<const ::google::protobuf::FloatValue*>(
      &::google::protobuf::_FloatValue_default_instance_);
}
inline ::google::protobuf::FloatValue* ClippingLimits::release_upper() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.ClippingLimits.upper)
  
  ::google::protobuf::FloatValue* temp = upper_;
  upper_ = NULL;
  return temp;
}
inline ::google::protobuf::FloatValue* ClippingLimits::mutable_upper() {
  
  if (upper_ == NULL) {
    auto* p = CreateMaybeMessage<::google::protobuf::FloatValue>(GetArenaNoVirtual());
    upper_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.ClippingLimits.upper)
  return upper_;
}
inline void ClippingLimits::set_allocated_upper(::google::protobuf::FloatValue* upper) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(upper_);
  }
  if (upper) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(upper)->GetArena();
    if (message_arena != submessage_arena) {
      upper = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, upper, submessage_arena);
    }
    
  } else {
    
  }
  upper_ = upper;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.ClippingLimits.upper)
}

// -------------------------------------------------------------------

// DynamicLearningRate

// int32 tag = 1;
inline void DynamicLearningRate::clear_tag() {
  tag_ = 0;
}
inline ::google::protobuf::int32 DynamicLearningRate::tag() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.DynamicLearningRate.tag)
  return tag_;
}
inline void DynamicLearningRate::set_tag(::google::protobuf::int32 value) {
  
  tag_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.DynamicLearningRate.tag)
}

// -------------------------------------------------------------------

// LearningRate

// float constant = 1;
inline bool LearningRate::has_constant() const {
  return learning_rate_case() == kConstant;
}
inline void LearningRate::set_has_constant() {
  _oneof_case_[0] = kConstant;
}
inline void LearningRate::clear_constant() {
  if (has_constant()) {
    learning_rate_.constant_ = 0;
    clear_has_learning_rate();
  }
}
inline float LearningRate::constant() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.LearningRate.constant)
  if (has_constant()) {
    return learning_rate_.constant_;
  }
  return 0;
}
inline void LearningRate::set_constant(float value) {
  if (!has_constant()) {
    clear_learning_rate();
    set_has_constant();
  }
  learning_rate_.constant_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.LearningRate.constant)
}

// .tensorflow.tpu.DynamicLearningRate dynamic = 2;
inline bool LearningRate::has_dynamic() const {
  return learning_rate_case() == kDynamic;
}
inline void LearningRate::set_has_dynamic() {
  _oneof_case_[0] = kDynamic;
}
inline void LearningRate::clear_dynamic() {
  if (has_dynamic()) {
    delete learning_rate_.dynamic_;
    clear_has_learning_rate();
  }
}
inline const ::tensorflow::tpu::DynamicLearningRate& LearningRate::_internal_dynamic() const {
  return *learning_rate_.dynamic_;
}
inline ::tensorflow::tpu::DynamicLearningRate* LearningRate::release_dynamic() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.LearningRate.dynamic)
  if (has_dynamic()) {
    clear_has_learning_rate();
      ::tensorflow::tpu::DynamicLearningRate* temp = learning_rate_.dynamic_;
    learning_rate_.dynamic_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::tpu::DynamicLearningRate& LearningRate::dynamic() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.LearningRate.dynamic)
  return has_dynamic()
      ? *learning_rate_.dynamic_
      : *reinterpret_cast< ::tensorflow::tpu::DynamicLearningRate*>(&::tensorflow::tpu::_DynamicLearningRate_default_instance_);
}
inline ::tensorflow::tpu::DynamicLearningRate* LearningRate::mutable_dynamic() {
  if (!has_dynamic()) {
    clear_learning_rate();
    set_has_dynamic();
    learning_rate_.dynamic_ = CreateMaybeMessage< ::tensorflow::tpu::DynamicLearningRate >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.LearningRate.dynamic)
  return learning_rate_.dynamic_;
}

inline bool LearningRate::has_learning_rate() const {
  return learning_rate_case() != LEARNING_RATE_NOT_SET;
}
inline void LearningRate::clear_has_learning_rate() {
  _oneof_case_[0] = LEARNING_RATE_NOT_SET;
}
inline LearningRate::LearningRateCase LearningRate::learning_rate_case() const {
  return LearningRate::LearningRateCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// AdagradParameters

// float initial_accumulator = 1;
inline void AdagradParameters::clear_initial_accumulator() {
  initial_accumulator_ = 0;
}
inline float AdagradParameters::initial_accumulator() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdagradParameters.initial_accumulator)
  return initial_accumulator_;
}
inline void AdagradParameters::set_initial_accumulator(float value) {
  
  initial_accumulator_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdagradParameters.initial_accumulator)
}

// -------------------------------------------------------------------

// StochasticGradientDescentParameters

// -------------------------------------------------------------------

// FtrlParameters

// float l1 = 1;
inline void FtrlParameters::clear_l1() {
  l1_ = 0;
}
inline float FtrlParameters::l1() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.FtrlParameters.l1)
  return l1_;
}
inline void FtrlParameters::set_l1(float value) {
  
  l1_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.FtrlParameters.l1)
}

// float l2 = 2;
inline void FtrlParameters::clear_l2() {
  l2_ = 0;
}
inline float FtrlParameters::l2() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.FtrlParameters.l2)
  return l2_;
}
inline void FtrlParameters::set_l2(float value) {
  
  l2_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.FtrlParameters.l2)
}

// float lr_power = 3;
inline void FtrlParameters::clear_lr_power() {
  lr_power_ = 0;
}
inline float FtrlParameters::lr_power() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.FtrlParameters.lr_power)
  return lr_power_;
}
inline void FtrlParameters::set_lr_power(float value) {
  
  lr_power_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.FtrlParameters.lr_power)
}

// float initial_accum = 4;
inline void FtrlParameters::clear_initial_accum() {
  initial_accum_ = 0;
}
inline float FtrlParameters::initial_accum() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.FtrlParameters.initial_accum)
  return initial_accum_;
}
inline void FtrlParameters::set_initial_accum(float value) {
  
  initial_accum_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.FtrlParameters.initial_accum)
}

// float initial_linear = 5;
inline void FtrlParameters::clear_initial_linear() {
  initial_linear_ = 0;
}
inline float FtrlParameters::initial_linear() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.FtrlParameters.initial_linear)
  return initial_linear_;
}
inline void FtrlParameters::set_initial_linear(float value) {
  
  initial_linear_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.FtrlParameters.initial_linear)
}

// -------------------------------------------------------------------

// AdamParameters

// float beta1 = 3;
inline void AdamParameters::clear_beta1() {
  beta1_ = 0;
}
inline float AdamParameters::beta1() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdamParameters.beta1)
  return beta1_;
}
inline void AdamParameters::set_beta1(float value) {
  
  beta1_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdamParameters.beta1)
}

// float beta2 = 4;
inline void AdamParameters::clear_beta2() {
  beta2_ = 0;
}
inline float AdamParameters::beta2() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdamParameters.beta2)
  return beta2_;
}
inline void AdamParameters::set_beta2(float value) {
  
  beta2_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdamParameters.beta2)
}

// float epsilon = 5;
inline void AdamParameters::clear_epsilon() {
  epsilon_ = 0;
}
inline float AdamParameters::epsilon() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdamParameters.epsilon)
  return epsilon_;
}
inline void AdamParameters::set_epsilon(float value) {
  
  epsilon_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdamParameters.epsilon)
}

// float initial_m = 6;
inline void AdamParameters::clear_initial_m() {
  initial_m_ = 0;
}
inline float AdamParameters::initial_m() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdamParameters.initial_m)
  return initial_m_;
}
inline void AdamParameters::set_initial_m(float value) {
  
  initial_m_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdamParameters.initial_m)
}

// float initial_v = 7;
inline void AdamParameters::clear_initial_v() {
  initial_v_ = 0;
}
inline float AdamParameters::initial_v() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdamParameters.initial_v)
  return initial_v_;
}
inline void AdamParameters::set_initial_v(float value) {
  
  initial_v_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdamParameters.initial_v)
}

// bool use_non_lazy_adam = 8;
inline void AdamParameters::clear_use_non_lazy_adam() {
  use_non_lazy_adam_ = false;
}
inline bool AdamParameters::use_non_lazy_adam() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdamParameters.use_non_lazy_adam)
  return use_non_lazy_adam_;
}
inline void AdamParameters::set_use_non_lazy_adam(bool value) {
  
  use_non_lazy_adam_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdamParameters.use_non_lazy_adam)
}

// bool use_sum_inside_sqrt = 10;
inline void AdamParameters::clear_use_sum_inside_sqrt() {
  use_sum_inside_sqrt_ = false;
}
inline bool AdamParameters::use_sum_inside_sqrt() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdamParameters.use_sum_inside_sqrt)
  return use_sum_inside_sqrt_;
}
inline void AdamParameters::set_use_sum_inside_sqrt(bool value) {
  
  use_sum_inside_sqrt_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdamParameters.use_sum_inside_sqrt)
}

// -------------------------------------------------------------------

// MomentumParameters

// float momentum = 1;
inline void MomentumParameters::clear_momentum() {
  momentum_ = 0;
}
inline float MomentumParameters::momentum() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MomentumParameters.momentum)
  return momentum_;
}
inline void MomentumParameters::set_momentum(float value) {
  
  momentum_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MomentumParameters.momentum)
}

// bool use_nesterov = 2;
inline void MomentumParameters::clear_use_nesterov() {
  use_nesterov_ = false;
}
inline bool MomentumParameters::use_nesterov() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MomentumParameters.use_nesterov)
  return use_nesterov_;
}
inline void MomentumParameters::set_use_nesterov(bool value) {
  
  use_nesterov_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MomentumParameters.use_nesterov)
}

// float initial_accum = 3;
inline void MomentumParameters::clear_initial_accum() {
  initial_accum_ = 0;
}
inline float MomentumParameters::initial_accum() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MomentumParameters.initial_accum)
  return initial_accum_;
}
inline void MomentumParameters::set_initial_accum(float value) {
  
  initial_accum_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MomentumParameters.initial_accum)
}

// -------------------------------------------------------------------

// RmsPropParameters

// float rho = 1;
inline void RmsPropParameters::clear_rho() {
  rho_ = 0;
}
inline float RmsPropParameters::rho() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.RmsPropParameters.rho)
  return rho_;
}
inline void RmsPropParameters::set_rho(float value) {
  
  rho_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.RmsPropParameters.rho)
}

// float momentum = 2;
inline void RmsPropParameters::clear_momentum() {
  momentum_ = 0;
}
inline float RmsPropParameters::momentum() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.RmsPropParameters.momentum)
  return momentum_;
}
inline void RmsPropParameters::set_momentum(float value) {
  
  momentum_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.RmsPropParameters.momentum)
}

// float epsilon = 3;
inline void RmsPropParameters::clear_epsilon() {
  epsilon_ = 0;
}
inline float RmsPropParameters::epsilon() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.RmsPropParameters.epsilon)
  return epsilon_;
}
inline void RmsPropParameters::set_epsilon(float value) {
  
  epsilon_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.RmsPropParameters.epsilon)
}

// float initial_ms = 4;
inline void RmsPropParameters::clear_initial_ms() {
  initial_ms_ = 0;
}
inline float RmsPropParameters::initial_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.RmsPropParameters.initial_ms)
  return initial_ms_;
}
inline void RmsPropParameters::set_initial_ms(float value) {
  
  initial_ms_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.RmsPropParameters.initial_ms)
}

// float initial_mom = 5;
inline void RmsPropParameters::clear_initial_mom() {
  initial_mom_ = 0;
}
inline float RmsPropParameters::initial_mom() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.RmsPropParameters.initial_mom)
  return initial_mom_;
}
inline void RmsPropParameters::set_initial_mom(float value) {
  
  initial_mom_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.RmsPropParameters.initial_mom)
}

// -------------------------------------------------------------------

// CenteredRmsPropParameters

// float rho = 1;
inline void CenteredRmsPropParameters::clear_rho() {
  rho_ = 0;
}
inline float CenteredRmsPropParameters::rho() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.CenteredRmsPropParameters.rho)
  return rho_;
}
inline void CenteredRmsPropParameters::set_rho(float value) {
  
  rho_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.CenteredRmsPropParameters.rho)
}

// float momentum = 2;
inline void CenteredRmsPropParameters::clear_momentum() {
  momentum_ = 0;
}
inline float CenteredRmsPropParameters::momentum() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.CenteredRmsPropParameters.momentum)
  return momentum_;
}
inline void CenteredRmsPropParameters::set_momentum(float value) {
  
  momentum_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.CenteredRmsPropParameters.momentum)
}

// float epsilon = 3;
inline void CenteredRmsPropParameters::clear_epsilon() {
  epsilon_ = 0;
}
inline float CenteredRmsPropParameters::epsilon() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.CenteredRmsPropParameters.epsilon)
  return epsilon_;
}
inline void CenteredRmsPropParameters::set_epsilon(float value) {
  
  epsilon_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.CenteredRmsPropParameters.epsilon)
}

// float initial_ms = 4;
inline void CenteredRmsPropParameters::clear_initial_ms() {
  initial_ms_ = 0;
}
inline float CenteredRmsPropParameters::initial_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.CenteredRmsPropParameters.initial_ms)
  return initial_ms_;
}
inline void CenteredRmsPropParameters::set_initial_ms(float value) {
  
  initial_ms_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.CenteredRmsPropParameters.initial_ms)
}

// float initial_mom = 5;
inline void CenteredRmsPropParameters::clear_initial_mom() {
  initial_mom_ = 0;
}
inline float CenteredRmsPropParameters::initial_mom() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.CenteredRmsPropParameters.initial_mom)
  return initial_mom_;
}
inline void CenteredRmsPropParameters::set_initial_mom(float value) {
  
  initial_mom_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.CenteredRmsPropParameters.initial_mom)
}

// float initial_mg = 6;
inline void CenteredRmsPropParameters::clear_initial_mg() {
  initial_mg_ = 0;
}
inline float CenteredRmsPropParameters::initial_mg() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.CenteredRmsPropParameters.initial_mg)
  return initial_mg_;
}
inline void CenteredRmsPropParameters::set_initial_mg(float value) {
  
  initial_mg_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.CenteredRmsPropParameters.initial_mg)
}

// -------------------------------------------------------------------

// MdlAdagradLightParameters

// float l2 = 1;
inline void MdlAdagradLightParameters::clear_l2() {
  l2_ = 0;
}
inline float MdlAdagradLightParameters::l2() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.l2)
  return l2_;
}
inline void MdlAdagradLightParameters::set_l2(float value) {
  
  l2_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.l2)
}

// float lr_power = 2;
inline void MdlAdagradLightParameters::clear_lr_power() {
  lr_power_ = 0;
}
inline float MdlAdagradLightParameters::lr_power() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.lr_power)
  return lr_power_;
}
inline void MdlAdagradLightParameters::set_lr_power(float value) {
  
  lr_power_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.lr_power)
}

// float min_servable_mdl_benefit = 3;
inline void MdlAdagradLightParameters::clear_min_servable_mdl_benefit() {
  min_servable_mdl_benefit_ = 0;
}
inline float MdlAdagradLightParameters::min_servable_mdl_benefit() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.min_servable_mdl_benefit)
  return min_servable_mdl_benefit_;
}
inline void MdlAdagradLightParameters::set_min_servable_mdl_benefit(float value) {
  
  min_servable_mdl_benefit_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.min_servable_mdl_benefit)
}

// float mdl_mix_in_margin = 4;
inline void MdlAdagradLightParameters::clear_mdl_mix_in_margin() {
  mdl_mix_in_margin_ = 0;
}
inline float MdlAdagradLightParameters::mdl_mix_in_margin() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.mdl_mix_in_margin)
  return mdl_mix_in_margin_;
}
inline void MdlAdagradLightParameters::set_mdl_mix_in_margin(float value) {
  
  mdl_mix_in_margin_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.mdl_mix_in_margin)
}

// float mdl_benefit_rampup_coeff = 5;
inline void MdlAdagradLightParameters::clear_mdl_benefit_rampup_coeff() {
  mdl_benefit_rampup_coeff_ = 0;
}
inline float MdlAdagradLightParameters::mdl_benefit_rampup_coeff() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.mdl_benefit_rampup_coeff)
  return mdl_benefit_rampup_coeff_;
}
inline void MdlAdagradLightParameters::set_mdl_benefit_rampup_coeff(float value) {
  
  mdl_benefit_rampup_coeff_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.mdl_benefit_rampup_coeff)
}

// float mdl_min_weight = 6;
inline void MdlAdagradLightParameters::clear_mdl_min_weight() {
  mdl_min_weight_ = 0;
}
inline float MdlAdagradLightParameters::mdl_min_weight() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.mdl_min_weight)
  return mdl_min_weight_;
}
inline void MdlAdagradLightParameters::set_mdl_min_weight(float value) {
  
  mdl_min_weight_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.mdl_min_weight)
}

// float benefit_revisit_scale = 7;
inline void MdlAdagradLightParameters::clear_benefit_revisit_scale() {
  benefit_revisit_scale_ = 0;
}
inline float MdlAdagradLightParameters::benefit_revisit_scale() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.benefit_revisit_scale)
  return benefit_revisit_scale_;
}
inline void MdlAdagradLightParameters::set_benefit_revisit_scale(float value) {
  
  benefit_revisit_scale_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.benefit_revisit_scale)
}

// float max_event_benefit = 8;
inline void MdlAdagradLightParameters::clear_max_event_benefit() {
  max_event_benefit_ = 0;
}
inline float MdlAdagradLightParameters::max_event_benefit() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.max_event_benefit)
  return max_event_benefit_;
}
inline void MdlAdagradLightParameters::set_max_event_benefit(float value) {
  
  max_event_benefit_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.max_event_benefit)
}

// float max_total_benefit = 9;
inline void MdlAdagradLightParameters::clear_max_total_benefit() {
  max_total_benefit_ = 0;
}
inline float MdlAdagradLightParameters::max_total_benefit() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.max_total_benefit)
  return max_total_benefit_;
}
inline void MdlAdagradLightParameters::set_max_total_benefit(float value) {
  
  max_total_benefit_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.max_total_benefit)
}

// float mdl_hard_limit = 10;
inline void MdlAdagradLightParameters::clear_mdl_hard_limit() {
  mdl_hard_limit_ = 0;
}
inline float MdlAdagradLightParameters::mdl_hard_limit() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.mdl_hard_limit)
  return mdl_hard_limit_;
}
inline void MdlAdagradLightParameters::set_mdl_hard_limit(float value) {
  
  mdl_hard_limit_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.mdl_hard_limit)
}

// bool hard_limit_min_benefit = 11;
inline void MdlAdagradLightParameters::clear_hard_limit_min_benefit() {
  hard_limit_min_benefit_ = false;
}
inline bool MdlAdagradLightParameters::hard_limit_min_benefit() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.hard_limit_min_benefit)
  return hard_limit_min_benefit_;
}
inline void MdlAdagradLightParameters::set_hard_limit_min_benefit(bool value) {
  
  hard_limit_min_benefit_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.hard_limit_min_benefit)
}

// bool mdl_regularize = 12;
inline void MdlAdagradLightParameters::clear_mdl_regularize() {
  mdl_regularize_ = false;
}
inline bool MdlAdagradLightParameters::mdl_regularize() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.mdl_regularize)
  return mdl_regularize_;
}
inline void MdlAdagradLightParameters::set_mdl_regularize(bool value) {
  
  mdl_regularize_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.mdl_regularize)
}

// float initial_accumulator = 13;
inline void MdlAdagradLightParameters::clear_initial_accumulator() {
  initial_accumulator_ = 0;
}
inline float MdlAdagradLightParameters::initial_accumulator() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.initial_accumulator)
  return initial_accumulator_;
}
inline void MdlAdagradLightParameters::set_initial_accumulator(float value) {
  
  initial_accumulator_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.initial_accumulator)
}

// float initial_weight = 14;
inline void MdlAdagradLightParameters::clear_initial_weight() {
  initial_weight_ = 0;
}
inline float MdlAdagradLightParameters::initial_weight() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.initial_weight)
  return initial_weight_;
}
inline void MdlAdagradLightParameters::set_initial_weight(float value) {
  
  initial_weight_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.initial_weight)
}

// float initial_benefit = 15;
inline void MdlAdagradLightParameters::clear_initial_benefit() {
  initial_benefit_ = 0;
}
inline float MdlAdagradLightParameters::initial_benefit() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.initial_benefit)
  return initial_benefit_;
}
inline void MdlAdagradLightParameters::set_initial_benefit(float value) {
  
  initial_benefit_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.initial_benefit)
}

// -------------------------------------------------------------------

// AdadeltaParameters

// float rho = 1;
inline void AdadeltaParameters::clear_rho() {
  rho_ = 0;
}
inline float AdadeltaParameters::rho() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdadeltaParameters.rho)
  return rho_;
}
inline void AdadeltaParameters::set_rho(float value) {
  
  rho_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdadeltaParameters.rho)
}

// float epsilon = 2;
inline void AdadeltaParameters::clear_epsilon() {
  epsilon_ = 0;
}
inline float AdadeltaParameters::epsilon() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdadeltaParameters.epsilon)
  return epsilon_;
}
inline void AdadeltaParameters::set_epsilon(float value) {
  
  epsilon_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdadeltaParameters.epsilon)
}

// float initial_accumulator = 3;
inline void AdadeltaParameters::clear_initial_accumulator() {
  initial_accumulator_ = 0;
}
inline float AdadeltaParameters::initial_accumulator() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdadeltaParameters.initial_accumulator)
  return initial_accumulator_;
}
inline void AdadeltaParameters::set_initial_accumulator(float value) {
  
  initial_accumulator_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdadeltaParameters.initial_accumulator)
}

// float initial_update = 4;
inline void AdadeltaParameters::clear_initial_update() {
  initial_update_ = 0;
}
inline float AdadeltaParameters::initial_update() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdadeltaParameters.initial_update)
  return initial_update_;
}
inline void AdadeltaParameters::set_initial_update(float value) {
  
  initial_update_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdadeltaParameters.initial_update)
}

// -------------------------------------------------------------------

// ProximalAdagradParameters

// float l1 = 1;
inline void ProximalAdagradParameters::clear_l1() {
  l1_ = 0;
}
inline float ProximalAdagradParameters::l1() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.ProximalAdagradParameters.l1)
  return l1_;
}
inline void ProximalAdagradParameters::set_l1(float value) {
  
  l1_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.ProximalAdagradParameters.l1)
}

// float l2 = 2;
inline void ProximalAdagradParameters::clear_l2() {
  l2_ = 0;
}
inline float ProximalAdagradParameters::l2() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.ProximalAdagradParameters.l2)
  return l2_;
}
inline void ProximalAdagradParameters::set_l2(float value) {
  
  l2_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.ProximalAdagradParameters.l2)
}

// float initial_accumulator = 3;
inline void ProximalAdagradParameters::clear_initial_accumulator() {
  initial_accumulator_ = 0;
}
inline float ProximalAdagradParameters::initial_accumulator() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.ProximalAdagradParameters.initial_accumulator)
  return initial_accumulator_;
}
inline void ProximalAdagradParameters::set_initial_accumulator(float value) {
  
  initial_accumulator_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.ProximalAdagradParameters.initial_accumulator)
}

// -------------------------------------------------------------------

// GradientAccumulationStatus

// -------------------------------------------------------------------

// HotIdOptimizerConfiguration

// .tensorflow.tpu.HotIdOptimizerConfiguration.Status status = 1;
inline void HotIdOptimizerConfiguration::clear_status() {
  status_ = 0;
}
inline ::tensorflow::tpu::HotIdOptimizerConfiguration_Status HotIdOptimizerConfiguration::status() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.HotIdOptimizerConfiguration.status)
  return static_cast< ::tensorflow::tpu::HotIdOptimizerConfiguration_Status >(status_);
}
inline void HotIdOptimizerConfiguration::set_status(::tensorflow::tpu::HotIdOptimizerConfiguration_Status value) {
  
  status_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.HotIdOptimizerConfiguration.status)
}

// float frequency_threshold = 2;
inline void HotIdOptimizerConfiguration::clear_frequency_threshold() {
  frequency_threshold_ = 0;
}
inline float HotIdOptimizerConfiguration::frequency_threshold() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.HotIdOptimizerConfiguration.frequency_threshold)
  return frequency_threshold_;
}
inline void HotIdOptimizerConfiguration::set_frequency_threshold(float value) {
  
  frequency_threshold_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.HotIdOptimizerConfiguration.frequency_threshold)
}

// int32 max_id_count = 3;
inline void HotIdOptimizerConfiguration::clear_max_id_count() {
  max_id_count_ = 0;
}
inline ::google::protobuf::int32 HotIdOptimizerConfiguration::max_id_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.HotIdOptimizerConfiguration.max_id_count)
  return max_id_count_;
}
inline void HotIdOptimizerConfiguration::set_max_id_count(::google::protobuf::int32 value) {
  
  max_id_count_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.HotIdOptimizerConfiguration.max_id_count)
}

// int32 max_slot_count = 4;
inline void HotIdOptimizerConfiguration::clear_max_slot_count() {
  max_slot_count_ = 0;
}
inline ::google::protobuf::int32 HotIdOptimizerConfiguration::max_slot_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.HotIdOptimizerConfiguration.max_slot_count)
  return max_slot_count_;
}
inline void HotIdOptimizerConfiguration::set_max_slot_count(::google::protobuf::int32 value) {
  
  max_slot_count_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.HotIdOptimizerConfiguration.max_slot_count)
}

// -------------------------------------------------------------------

// OptimizationParameters

// .tensorflow.tpu.LearningRate learning_rate = 13;
inline bool OptimizationParameters::has_learning_rate() const {
  return this != internal_default_instance() && learning_rate_ != NULL;
}
inline void OptimizationParameters::clear_learning_rate() {
  if (GetArenaNoVirtual() == NULL && learning_rate_ != NULL) {
    delete learning_rate_;
  }
  learning_rate_ = NULL;
}
inline const ::tensorflow::tpu::LearningRate& OptimizationParameters::_internal_learning_rate() const {
  return *learning_rate_;
}
inline const ::tensorflow::tpu::LearningRate& OptimizationParameters::learning_rate() const {
  const ::tensorflow::tpu::LearningRate* p = learning_rate_;
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.learning_rate)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::tpu::LearningRate*>(
      &::tensorflow::tpu::_LearningRate_default_instance_);
}
inline ::tensorflow::tpu::LearningRate* OptimizationParameters::release_learning_rate() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.learning_rate)
  
  ::tensorflow::tpu::LearningRate* temp = learning_rate_;
  learning_rate_ = NULL;
  return temp;
}
inline ::tensorflow::tpu::LearningRate* OptimizationParameters::mutable_learning_rate() {
  
  if (learning_rate_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::tpu::LearningRate>(GetArenaNoVirtual());
    learning_rate_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.learning_rate)
  return learning_rate_;
}
inline void OptimizationParameters::set_allocated_learning_rate(::tensorflow::tpu::LearningRate* learning_rate) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete learning_rate_;
  }
  if (learning_rate) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      learning_rate = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, learning_rate, submessage_arena);
    }
    
  } else {
    
  }
  learning_rate_ = learning_rate;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.OptimizationParameters.learning_rate)
}

// .tensorflow.tpu.ClippingLimits clipping_limits = 2;
inline bool OptimizationParameters::has_clipping_limits() const {
  return this != internal_default_instance() && clipping_limits_ != NULL;
}
inline void OptimizationParameters::clear_clipping_limits() {
  if (GetArenaNoVirtual() == NULL && clipping_limits_ != NULL) {
    delete clipping_limits_;
  }
  clipping_limits_ = NULL;
}
inline const ::tensorflow::tpu::ClippingLimits& OptimizationParameters::_internal_clipping_limits() const {
  return *clipping_limits_;
}
inline const ::tensorflow::tpu::ClippingLimits& OptimizationParameters::clipping_limits() const {
  const ::tensorflow::tpu::ClippingLimits* p = clipping_limits_;
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.clipping_limits)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::tpu::ClippingLimits*>(
      &::tensorflow::tpu::_ClippingLimits_default_instance_);
}
inline ::tensorflow::tpu::ClippingLimits* OptimizationParameters::release_clipping_limits() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.clipping_limits)
  
  ::tensorflow::tpu::ClippingLimits* temp = clipping_limits_;
  clipping_limits_ = NULL;
  return temp;
}
inline ::tensorflow::tpu::ClippingLimits* OptimizationParameters::mutable_clipping_limits() {
  
  if (clipping_limits_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::tpu::ClippingLimits>(GetArenaNoVirtual());
    clipping_limits_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.clipping_limits)
  return clipping_limits_;
}
inline void OptimizationParameters::set_allocated_clipping_limits(::tensorflow::tpu::ClippingLimits* clipping_limits) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete clipping_limits_;
  }
  if (clipping_limits) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      clipping_limits = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, clipping_limits, submessage_arena);
    }
    
  } else {
    
  }
  clipping_limits_ = clipping_limits;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.OptimizationParameters.clipping_limits)
}

// .tensorflow.tpu.ClippingLimits gradient_clipping_limits = 7;
inline bool OptimizationParameters::has_gradient_clipping_limits() const {
  return this != internal_default_instance() && gradient_clipping_limits_ != NULL;
}
inline void OptimizationParameters::clear_gradient_clipping_limits() {
  if (GetArenaNoVirtual() == NULL && gradient_clipping_limits_ != NULL) {
    delete gradient_clipping_limits_;
  }
  gradient_clipping_limits_ = NULL;
}
inline const ::tensorflow::tpu::ClippingLimits& OptimizationParameters::_internal_gradient_clipping_limits() const {
  return *gradient_clipping_limits_;
}
inline const ::tensorflow::tpu::ClippingLimits& OptimizationParameters::gradient_clipping_limits() const {
  const ::tensorflow::tpu::ClippingLimits* p = gradient_clipping_limits_;
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.gradient_clipping_limits)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::tpu::ClippingLimits*>(
      &::tensorflow::tpu::_ClippingLimits_default_instance_);
}
inline ::tensorflow::tpu::ClippingLimits* OptimizationParameters::release_gradient_clipping_limits() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.gradient_clipping_limits)
  
  ::tensorflow::tpu::ClippingLimits* temp = gradient_clipping_limits_;
  gradient_clipping_limits_ = NULL;
  return temp;
}
inline ::tensorflow::tpu::ClippingLimits* OptimizationParameters::mutable_gradient_clipping_limits() {
  
  if (gradient_clipping_limits_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::tpu::ClippingLimits>(GetArenaNoVirtual());
    gradient_clipping_limits_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.gradient_clipping_limits)
  return gradient_clipping_limits_;
}
inline void OptimizationParameters::set_allocated_gradient_clipping_limits(::tensorflow::tpu::ClippingLimits* gradient_clipping_limits) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete gradient_clipping_limits_;
  }
  if (gradient_clipping_limits) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      gradient_clipping_limits = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, gradient_clipping_limits, submessage_arena);
    }
    
  } else {
    
  }
  gradient_clipping_limits_ = gradient_clipping_limits;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.OptimizationParameters.gradient_clipping_limits)
}

// float weight_decay_factor = 16;
inline void OptimizationParameters::clear_weight_decay_factor() {
  weight_decay_factor_ = 0;
}
inline float OptimizationParameters::weight_decay_factor() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.weight_decay_factor)
  return weight_decay_factor_;
}
inline void OptimizationParameters::set_weight_decay_factor(float value) {
  
  weight_decay_factor_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.OptimizationParameters.weight_decay_factor)
}

// .tensorflow.tpu.GradientAccumulationStatus.Status gradient_accumulation_status = 17;
inline void OptimizationParameters::clear_gradient_accumulation_status() {
  gradient_accumulation_status_ = 0;
}
inline ::tensorflow::tpu::GradientAccumulationStatus_Status OptimizationParameters::gradient_accumulation_status() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.gradient_accumulation_status)
  return static_cast< ::tensorflow::tpu::GradientAccumulationStatus_Status >(gradient_accumulation_status_);
}
inline void OptimizationParameters::set_gradient_accumulation_status(::tensorflow::tpu::GradientAccumulationStatus_Status value) {
  
  gradient_accumulation_status_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.OptimizationParameters.gradient_accumulation_status)
}

// .tensorflow.tpu.HotIdOptimizerConfiguration hot_id_optimizer_configuration = 18;
inline bool OptimizationParameters::has_hot_id_optimizer_configuration() const {
  return this != internal_default_instance() && hot_id_optimizer_configuration_ != NULL;
}
inline void OptimizationParameters::clear_hot_id_optimizer_configuration() {
  if (GetArenaNoVirtual() == NULL && hot_id_optimizer_configuration_ != NULL) {
    delete hot_id_optimizer_configuration_;
  }
  hot_id_optimizer_configuration_ = NULL;
}
inline const ::tensorflow::tpu::HotIdOptimizerConfiguration& OptimizationParameters::_internal_hot_id_optimizer_configuration() const {
  return *hot_id_optimizer_configuration_;
}
inline const ::tensorflow::tpu::HotIdOptimizerConfiguration& OptimizationParameters::hot_id_optimizer_configuration() const {
  const ::tensorflow::tpu::HotIdOptimizerConfiguration* p = hot_id_optimizer_configuration_;
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.hot_id_optimizer_configuration)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::tpu::HotIdOptimizerConfiguration*>(
      &::tensorflow::tpu::_HotIdOptimizerConfiguration_default_instance_);
}
inline ::tensorflow::tpu::HotIdOptimizerConfiguration* OptimizationParameters::release_hot_id_optimizer_configuration() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.hot_id_optimizer_configuration)
  
  ::tensorflow::tpu::HotIdOptimizerConfiguration* temp = hot_id_optimizer_configuration_;
  hot_id_optimizer_configuration_ = NULL;
  return temp;
}
inline ::tensorflow::tpu::HotIdOptimizerConfiguration* OptimizationParameters::mutable_hot_id_optimizer_configuration() {
  
  if (hot_id_optimizer_configuration_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::tpu::HotIdOptimizerConfiguration>(GetArenaNoVirtual());
    hot_id_optimizer_configuration_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.hot_id_optimizer_configuration)
  return hot_id_optimizer_configuration_;
}
inline void OptimizationParameters::set_allocated_hot_id_optimizer_configuration(::tensorflow::tpu::HotIdOptimizerConfiguration* hot_id_optimizer_configuration) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete hot_id_optimizer_configuration_;
  }
  if (hot_id_optimizer_configuration) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      hot_id_optimizer_configuration = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, hot_id_optimizer_configuration, submessage_arena);
    }
    
  } else {
    
  }
  hot_id_optimizer_configuration_ = hot_id_optimizer_configuration;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.OptimizationParameters.hot_id_optimizer_configuration)
}

// .tensorflow.tpu.AdagradParameters adagrad = 3;
inline bool OptimizationParameters::has_adagrad() const {
  return parameters_case() == kAdagrad;
}
inline void OptimizationParameters::set_has_adagrad() {
  _oneof_case_[0] = kAdagrad;
}
inline void OptimizationParameters::clear_adagrad() {
  if (has_adagrad()) {
    delete parameters_.adagrad_;
    clear_has_parameters();
  }
}
inline const ::tensorflow::tpu::AdagradParameters& OptimizationParameters::_internal_adagrad() const {
  return *parameters_.adagrad_;
}
inline ::tensorflow::tpu::AdagradParameters* OptimizationParameters::release_adagrad() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.adagrad)
  if (has_adagrad()) {
    clear_has_parameters();
      ::tensorflow::tpu::AdagradParameters* temp = parameters_.adagrad_;
    parameters_.adagrad_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::tpu::AdagradParameters& OptimizationParameters::adagrad() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.adagrad)
  return has_adagrad()
      ? *parameters_.adagrad_
      : *reinterpret_cast< ::tensorflow::tpu::AdagradParameters*>(&::tensorflow::tpu::_AdagradParameters_default_instance_);
}
inline ::tensorflow::tpu::AdagradParameters* OptimizationParameters::mutable_adagrad() {
  if (!has_adagrad()) {
    clear_parameters();
    set_has_adagrad();
    parameters_.adagrad_ = CreateMaybeMessage< ::tensorflow::tpu::AdagradParameters >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.adagrad)
  return parameters_.adagrad_;
}

// .tensorflow.tpu.StochasticGradientDescentParameters stochastic_gradient_descent = 4;
inline bool OptimizationParameters::has_stochastic_gradient_descent() const {
  return parameters_case() == kStochasticGradientDescent;
}
inline void OptimizationParameters::set_has_stochastic_gradient_descent() {
  _oneof_case_[0] = kStochasticGradientDescent;
}
inline void OptimizationParameters::clear_stochastic_gradient_descent() {
  if (has_stochastic_gradient_descent()) {
    delete parameters_.stochastic_gradient_descent_;
    clear_has_parameters();
  }
}
inline const ::tensorflow::tpu::StochasticGradientDescentParameters& OptimizationParameters::_internal_stochastic_gradient_descent() const {
  return *parameters_.stochastic_gradient_descent_;
}
inline ::tensorflow::tpu::StochasticGradientDescentParameters* OptimizationParameters::release_stochastic_gradient_descent() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.stochastic_gradient_descent)
  if (has_stochastic_gradient_descent()) {
    clear_has_parameters();
      ::tensorflow::tpu::StochasticGradientDescentParameters* temp = parameters_.stochastic_gradient_descent_;
    parameters_.stochastic_gradient_descent_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::tpu::StochasticGradientDescentParameters& OptimizationParameters::stochastic_gradient_descent() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.stochastic_gradient_descent)
  return has_stochastic_gradient_descent()
      ? *parameters_.stochastic_gradient_descent_
      : *reinterpret_cast< ::tensorflow::tpu::StochasticGradientDescentParameters*>(&::tensorflow::tpu::_StochasticGradientDescentParameters_default_instance_);
}
inline ::tensorflow::tpu::StochasticGradientDescentParameters* OptimizationParameters::mutable_stochastic_gradient_descent() {
  if (!has_stochastic_gradient_descent()) {
    clear_parameters();
    set_has_stochastic_gradient_descent();
    parameters_.stochastic_gradient_descent_ = CreateMaybeMessage< ::tensorflow::tpu::StochasticGradientDescentParameters >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.stochastic_gradient_descent)
  return parameters_.stochastic_gradient_descent_;
}

// .tensorflow.tpu.FtrlParameters ftrl = 5;
inline bool OptimizationParameters::has_ftrl() const {
  return parameters_case() == kFtrl;
}
inline void OptimizationParameters::set_has_ftrl() {
  _oneof_case_[0] = kFtrl;
}
inline void OptimizationParameters::clear_ftrl() {
  if (has_ftrl()) {
    delete parameters_.ftrl_;
    clear_has_parameters();
  }
}
inline const ::tensorflow::tpu::FtrlParameters& OptimizationParameters::_internal_ftrl() const {
  return *parameters_.ftrl_;
}
inline ::tensorflow::tpu::FtrlParameters* OptimizationParameters::release_ftrl() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.ftrl)
  if (has_ftrl()) {
    clear_has_parameters();
      ::tensorflow::tpu::FtrlParameters* temp = parameters_.ftrl_;
    parameters_.ftrl_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::tpu::FtrlParameters& OptimizationParameters::ftrl() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.ftrl)
  return has_ftrl()
      ? *parameters_.ftrl_
      : *reinterpret_cast< ::tensorflow::tpu::FtrlParameters*>(&::tensorflow::tpu::_FtrlParameters_default_instance_);
}
inline ::tensorflow::tpu::FtrlParameters* OptimizationParameters::mutable_ftrl() {
  if (!has_ftrl()) {
    clear_parameters();
    set_has_ftrl();
    parameters_.ftrl_ = CreateMaybeMessage< ::tensorflow::tpu::FtrlParameters >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.ftrl)
  return parameters_.ftrl_;
}

// .tensorflow.tpu.AdamParameters adam = 6;
inline bool OptimizationParameters::has_adam() const {
  return parameters_case() == kAdam;
}
inline void OptimizationParameters::set_has_adam() {
  _oneof_case_[0] = kAdam;
}
inline void OptimizationParameters::clear_adam() {
  if (has_adam()) {
    delete parameters_.adam_;
    clear_has_parameters();
  }
}
inline const ::tensorflow::tpu::AdamParameters& OptimizationParameters::_internal_adam() const {
  return *parameters_.adam_;
}
inline ::tensorflow::tpu::AdamParameters* OptimizationParameters::release_adam() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.adam)
  if (has_adam()) {
    clear_has_parameters();
      ::tensorflow::tpu::AdamParameters* temp = parameters_.adam_;
    parameters_.adam_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::tpu::AdamParameters& OptimizationParameters::adam() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.adam)
  return has_adam()
      ? *parameters_.adam_
      : *reinterpret_cast< ::tensorflow::tpu::AdamParameters*>(&::tensorflow::tpu::_AdamParameters_default_instance_);
}
inline ::tensorflow::tpu::AdamParameters* OptimizationParameters::mutable_adam() {
  if (!has_adam()) {
    clear_parameters();
    set_has_adam();
    parameters_.adam_ = CreateMaybeMessage< ::tensorflow::tpu::AdamParameters >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.adam)
  return parameters_.adam_;
}

// .tensorflow.tpu.MomentumParameters momentum = 8;
inline bool OptimizationParameters::has_momentum() const {
  return parameters_case() == kMomentum;
}
inline void OptimizationParameters::set_has_momentum() {
  _oneof_case_[0] = kMomentum;
}
inline void OptimizationParameters::clear_momentum() {
  if (has_momentum()) {
    delete parameters_.momentum_;
    clear_has_parameters();
  }
}
inline const ::tensorflow::tpu::MomentumParameters& OptimizationParameters::_internal_momentum() const {
  return *parameters_.momentum_;
}
inline ::tensorflow::tpu::MomentumParameters* OptimizationParameters::release_momentum() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.momentum)
  if (has_momentum()) {
    clear_has_parameters();
      ::tensorflow::tpu::MomentumParameters* temp = parameters_.momentum_;
    parameters_.momentum_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::tpu::MomentumParameters& OptimizationParameters::momentum() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.momentum)
  return has_momentum()
      ? *parameters_.momentum_
      : *reinterpret_cast< ::tensorflow::tpu::MomentumParameters*>(&::tensorflow::tpu::_MomentumParameters_default_instance_);
}
inline ::tensorflow::tpu::MomentumParameters* OptimizationParameters::mutable_momentum() {
  if (!has_momentum()) {
    clear_parameters();
    set_has_momentum();
    parameters_.momentum_ = CreateMaybeMessage< ::tensorflow::tpu::MomentumParameters >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.momentum)
  return parameters_.momentum_;
}

// .tensorflow.tpu.RmsPropParameters rms_prop = 9;
inline bool OptimizationParameters::has_rms_prop() const {
  return parameters_case() == kRmsProp;
}
inline void OptimizationParameters::set_has_rms_prop() {
  _oneof_case_[0] = kRmsProp;
}
inline void OptimizationParameters::clear_rms_prop() {
  if (has_rms_prop()) {
    delete parameters_.rms_prop_;
    clear_has_parameters();
  }
}
inline const ::tensorflow::tpu::RmsPropParameters& OptimizationParameters::_internal_rms_prop() const {
  return *parameters_.rms_prop_;
}
inline ::tensorflow::tpu::RmsPropParameters* OptimizationParameters::release_rms_prop() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.rms_prop)
  if (has_rms_prop()) {
    clear_has_parameters();
      ::tensorflow::tpu::RmsPropParameters* temp = parameters_.rms_prop_;
    parameters_.rms_prop_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::tpu::RmsPropParameters& OptimizationParameters::rms_prop() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.rms_prop)
  return has_rms_prop()
      ? *parameters_.rms_prop_
      : *reinterpret_cast< ::tensorflow::tpu::RmsPropParameters*>(&::tensorflow::tpu::_RmsPropParameters_default_instance_);
}
inline ::tensorflow::tpu::RmsPropParameters* OptimizationParameters::mutable_rms_prop() {
  if (!has_rms_prop()) {
    clear_parameters();
    set_has_rms_prop();
    parameters_.rms_prop_ = CreateMaybeMessage< ::tensorflow::tpu::RmsPropParameters >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.rms_prop)
  return parameters_.rms_prop_;
}

// .tensorflow.tpu.CenteredRmsPropParameters centered_rms_prop = 10;
inline bool OptimizationParameters::has_centered_rms_prop() const {
  return parameters_case() == kCenteredRmsProp;
}
inline void OptimizationParameters::set_has_centered_rms_prop() {
  _oneof_case_[0] = kCenteredRmsProp;
}
inline void OptimizationParameters::clear_centered_rms_prop() {
  if (has_centered_rms_prop()) {
    delete parameters_.centered_rms_prop_;
    clear_has_parameters();
  }
}
inline const ::tensorflow::tpu::CenteredRmsPropParameters& OptimizationParameters::_internal_centered_rms_prop() const {
  return *parameters_.centered_rms_prop_;
}
inline ::tensorflow::tpu::CenteredRmsPropParameters* OptimizationParameters::release_centered_rms_prop() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.centered_rms_prop)
  if (has_centered_rms_prop()) {
    clear_has_parameters();
      ::tensorflow::tpu::CenteredRmsPropParameters* temp = parameters_.centered_rms_prop_;
    parameters_.centered_rms_prop_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::tpu::CenteredRmsPropParameters& OptimizationParameters::centered_rms_prop() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.centered_rms_prop)
  return has_centered_rms_prop()
      ? *parameters_.centered_rms_prop_
      : *reinterpret_cast< ::tensorflow::tpu::CenteredRmsPropParameters*>(&::tensorflow::tpu::_CenteredRmsPropParameters_default_instance_);
}
inline ::tensorflow::tpu::CenteredRmsPropParameters* OptimizationParameters::mutable_centered_rms_prop() {
  if (!has_centered_rms_prop()) {
    clear_parameters();
    set_has_centered_rms_prop();
    parameters_.centered_rms_prop_ = CreateMaybeMessage< ::tensorflow::tpu::CenteredRmsPropParameters >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.centered_rms_prop)
  return parameters_.centered_rms_prop_;
}

// .tensorflow.tpu.MdlAdagradLightParameters mdl_adagrad_light = 11;
inline bool OptimizationParameters::has_mdl_adagrad_light() const {
  return parameters_case() == kMdlAdagradLight;
}
inline void OptimizationParameters::set_has_mdl_adagrad_light() {
  _oneof_case_[0] = kMdlAdagradLight;
}
inline void OptimizationParameters::clear_mdl_adagrad_light() {
  if (has_mdl_adagrad_light()) {
    delete parameters_.mdl_adagrad_light_;
    clear_has_parameters();
  }
}
inline const ::tensorflow::tpu::MdlAdagradLightParameters& OptimizationParameters::_internal_mdl_adagrad_light() const {
  return *parameters_.mdl_adagrad_light_;
}
inline ::tensorflow::tpu::MdlAdagradLightParameters* OptimizationParameters::release_mdl_adagrad_light() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.mdl_adagrad_light)
  if (has_mdl_adagrad_light()) {
    clear_has_parameters();
      ::tensorflow::tpu::MdlAdagradLightParameters* temp = parameters_.mdl_adagrad_light_;
    parameters_.mdl_adagrad_light_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::tpu::MdlAdagradLightParameters& OptimizationParameters::mdl_adagrad_light() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.mdl_adagrad_light)
  return has_mdl_adagrad_light()
      ? *parameters_.mdl_adagrad_light_
      : *reinterpret_cast< ::tensorflow::tpu::MdlAdagradLightParameters*>(&::tensorflow::tpu::_MdlAdagradLightParameters_default_instance_);
}
inline ::tensorflow::tpu::MdlAdagradLightParameters* OptimizationParameters::mutable_mdl_adagrad_light() {
  if (!has_mdl_adagrad_light()) {
    clear_parameters();
    set_has_mdl_adagrad_light();
    parameters_.mdl_adagrad_light_ = CreateMaybeMessage< ::tensorflow::tpu::MdlAdagradLightParameters >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.mdl_adagrad_light)
  return parameters_.mdl_adagrad_light_;
}

// .tensorflow.tpu.AdadeltaParameters adadelta = 12;
inline bool OptimizationParameters::has_adadelta() const {
  return parameters_case() == kAdadelta;
}
inline void OptimizationParameters::set_has_adadelta() {
  _oneof_case_[0] = kAdadelta;
}
inline void OptimizationParameters::clear_adadelta() {
  if (has_adadelta()) {
    delete parameters_.adadelta_;
    clear_has_parameters();
  }
}
inline const ::tensorflow::tpu::AdadeltaParameters& OptimizationParameters::_internal_adadelta() const {
  return *parameters_.adadelta_;
}
inline ::tensorflow::tpu::AdadeltaParameters* OptimizationParameters::release_adadelta() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.adadelta)
  if (has_adadelta()) {
    clear_has_parameters();
      ::tensorflow::tpu::AdadeltaParameters* temp = parameters_.adadelta_;
    parameters_.adadelta_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::tpu::AdadeltaParameters& OptimizationParameters::adadelta() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.adadelta)
  return has_adadelta()
      ? *parameters_.adadelta_
      : *reinterpret_cast< ::tensorflow::tpu::AdadeltaParameters*>(&::tensorflow::tpu::_AdadeltaParameters_default_instance_);
}
inline ::tensorflow::tpu::AdadeltaParameters* OptimizationParameters::mutable_adadelta() {
  if (!has_adadelta()) {
    clear_parameters();
    set_has_adadelta();
    parameters_.adadelta_ = CreateMaybeMessage< ::tensorflow::tpu::AdadeltaParameters >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.adadelta)
  return parameters_.adadelta_;
}

// .tensorflow.tpu.ProximalAdagradParameters proximal_adagrad = 14;
inline bool OptimizationParameters::has_proximal_adagrad() const {
  return parameters_case() == kProximalAdagrad;
}
inline void OptimizationParameters::set_has_proximal_adagrad() {
  _oneof_case_[0] = kProximalAdagrad;
}
inline void OptimizationParameters::clear_proximal_adagrad() {
  if (has_proximal_adagrad()) {
    delete parameters_.proximal_adagrad_;
    clear_has_parameters();
  }
}
inline const ::tensorflow::tpu::ProximalAdagradParameters& OptimizationParameters::_internal_proximal_adagrad() const {
  return *parameters_.proximal_adagrad_;
}
inline ::tensorflow::tpu::ProximalAdagradParameters* OptimizationParameters::release_proximal_adagrad() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.proximal_adagrad)
  if (has_proximal_adagrad()) {
    clear_has_parameters();
      ::tensorflow::tpu::ProximalAdagradParameters* temp = parameters_.proximal_adagrad_;
    parameters_.proximal_adagrad_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::tpu::ProximalAdagradParameters& OptimizationParameters::proximal_adagrad() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.proximal_adagrad)
  return has_proximal_adagrad()
      ? *parameters_.proximal_adagrad_
      : *reinterpret_cast< ::tensorflow::tpu::ProximalAdagradParameters*>(&::tensorflow::tpu::_ProximalAdagradParameters_default_instance_);
}
inline ::tensorflow::tpu::ProximalAdagradParameters* OptimizationParameters::mutable_proximal_adagrad() {
  if (!has_proximal_adagrad()) {
    clear_parameters();
    set_has_proximal_adagrad();
    parameters_.proximal_adagrad_ = CreateMaybeMessage< ::tensorflow::tpu::ProximalAdagradParameters >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.proximal_adagrad)
  return parameters_.proximal_adagrad_;
}

inline bool OptimizationParameters::has_parameters() const {
  return parameters_case() != PARAMETERS_NOT_SET;
}
inline void OptimizationParameters::clear_has_parameters() {
  _oneof_case_[0] = PARAMETERS_NOT_SET;
}
inline OptimizationParameters::ParametersCase OptimizationParameters::parameters_case() const {
  return OptimizationParameters::ParametersCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// StateVariableSpecification_UserDefined

// double padding_initial_value = 1;
inline void StateVariableSpecification_UserDefined::clear_padding_initial_value() {
  padding_initial_value_ = 0;
}
inline double StateVariableSpecification_UserDefined::padding_initial_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.StateVariableSpecification.UserDefined.padding_initial_value)
  return padding_initial_value_;
}
inline void StateVariableSpecification_UserDefined::set_padding_initial_value(double value) {
  
  padding_initial_value_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.StateVariableSpecification.UserDefined.padding_initial_value)
}

// -------------------------------------------------------------------

// StateVariableSpecification_FillWithConstant

// double initial_value = 1;
inline void StateVariableSpecification_FillWithConstant::clear_initial_value() {
  initial_value_ = 0;
}
inline double StateVariableSpecification_FillWithConstant::initial_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.StateVariableSpecification.FillWithConstant.initial_value)
  return initial_value_;
}
inline void StateVariableSpecification_FillWithConstant::set_initial_value(double value) {
  
  initial_value_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.StateVariableSpecification.FillWithConstant.initial_value)
}

// -------------------------------------------------------------------

// StateVariableSpecification

// string name = 1;
inline void StateVariableSpecification::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& StateVariableSpecification::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.StateVariableSpecification.name)
  return name_.GetNoArena();
}
inline void StateVariableSpecification::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.StateVariableSpecification.name)
}
#if LANG_CXX11
inline void StateVariableSpecification::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tpu.StateVariableSpecification.name)
}
#endif
inline void StateVariableSpecification::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tpu.StateVariableSpecification.name)
}
inline void StateVariableSpecification::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tpu.StateVariableSpecification.name)
}
inline ::std::string* StateVariableSpecification::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.StateVariableSpecification.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* StateVariableSpecification::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.StateVariableSpecification.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void StateVariableSpecification::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.StateVariableSpecification.name)
}

// .tensorflow.tpu.StateVariableSpecification.UserDefined user_defined = 2;
inline bool StateVariableSpecification::has_user_defined() const {
  return usage_case() == kUserDefined;
}
inline void StateVariableSpecification::set_has_user_defined() {
  _oneof_case_[0] = kUserDefined;
}
inline void StateVariableSpecification::clear_user_defined() {
  if (has_user_defined()) {
    delete usage_.user_defined_;
    clear_has_usage();
  }
}
inline const ::tensorflow::tpu::StateVariableSpecification_UserDefined& StateVariableSpecification::_internal_user_defined() const {
  return *usage_.user_defined_;
}
inline ::tensorflow::tpu::StateVariableSpecification_UserDefined* StateVariableSpecification::release_user_defined() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.StateVariableSpecification.user_defined)
  if (has_user_defined()) {
    clear_has_usage();
      ::tensorflow::tpu::StateVariableSpecification_UserDefined* temp = usage_.user_defined_;
    usage_.user_defined_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::tpu::StateVariableSpecification_UserDefined& StateVariableSpecification::user_defined() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.StateVariableSpecification.user_defined)
  return has_user_defined()
      ? *usage_.user_defined_
      : *reinterpret_cast< ::tensorflow::tpu::StateVariableSpecification_UserDefined*>(&::tensorflow::tpu::_StateVariableSpecification_UserDefined_default_instance_);
}
inline ::tensorflow::tpu::StateVariableSpecification_UserDefined* StateVariableSpecification::mutable_user_defined() {
  if (!has_user_defined()) {
    clear_usage();
    set_has_user_defined();
    usage_.user_defined_ = CreateMaybeMessage< ::tensorflow::tpu::StateVariableSpecification_UserDefined >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.StateVariableSpecification.user_defined)
  return usage_.user_defined_;
}

// .tensorflow.tpu.StateVariableSpecification.FillWithConstant fill_with_constant = 3;
inline bool StateVariableSpecification::has_fill_with_constant() const {
  return usage_case() == kFillWithConstant;
}
inline void StateVariableSpecification::set_has_fill_with_constant() {
  _oneof_case_[0] = kFillWithConstant;
}
inline void StateVariableSpecification::clear_fill_with_constant() {
  if (has_fill_with_constant()) {
    delete usage_.fill_with_constant_;
    clear_has_usage();
  }
}
inline const ::tensorflow::tpu::StateVariableSpecification_FillWithConstant& StateVariableSpecification::_internal_fill_with_constant() const {
  return *usage_.fill_with_constant_;
}
inline ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* StateVariableSpecification::release_fill_with_constant() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.StateVariableSpecification.fill_with_constant)
  if (has_fill_with_constant()) {
    clear_has_usage();
      ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* temp = usage_.fill_with_constant_;
    usage_.fill_with_constant_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::tpu::StateVariableSpecification_FillWithConstant& StateVariableSpecification::fill_with_constant() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.StateVariableSpecification.fill_with_constant)
  return has_fill_with_constant()
      ? *usage_.fill_with_constant_
      : *reinterpret_cast< ::tensorflow::tpu::StateVariableSpecification_FillWithConstant*>(&::tensorflow::tpu::_StateVariableSpecification_FillWithConstant_default_instance_);
}
inline ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* StateVariableSpecification::mutable_fill_with_constant() {
  if (!has_fill_with_constant()) {
    clear_usage();
    set_has_fill_with_constant();
    usage_.fill_with_constant_ = CreateMaybeMessage< ::tensorflow::tpu::StateVariableSpecification_FillWithConstant >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.StateVariableSpecification.fill_with_constant)
  return usage_.fill_with_constant_;
}

inline bool StateVariableSpecification::has_usage() const {
  return usage_case() != USAGE_NOT_SET;
}
inline void StateVariableSpecification::clear_has_usage() {
  _oneof_case_[0] = USAGE_NOT_SET;
}
inline StateVariableSpecification::UsageCase StateVariableSpecification::usage_case() const {
  return StateVariableSpecification::UsageCase(_oneof_case_[0]);
}
#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tpu
}  // namespace tensorflow

namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::tensorflow::tpu::GradientAccumulationStatus_Status> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::tpu::GradientAccumulationStatus_Status>() {
  return ::tensorflow::tpu::GradientAccumulationStatus_Status_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::tpu::HotIdOptimizerConfiguration_Status> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::tpu::HotIdOptimizerConfiguration_Status>() {
  return ::tensorflow::tpu::HotIdOptimizerConfiguration_Status_descriptor();
}

}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/autotuning.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/any.pb.h>
#include <google/protobuf/duration.pb.h>
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto 

namespace protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[6];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto
namespace tensorflow {
class AutotuneResult;
class AutotuneResultDefaultTypeInternal;
extern AutotuneResultDefaultTypeInternal _AutotuneResult_default_instance_;
class AutotuneResult_ConvKey;
class AutotuneResult_ConvKeyDefaultTypeInternal;
extern AutotuneResult_ConvKeyDefaultTypeInternal _AutotuneResult_ConvKey_default_instance_;
class AutotuneResult_SuccessResult;
class AutotuneResult_SuccessResultDefaultTypeInternal;
extern AutotuneResult_SuccessResultDefaultTypeInternal _AutotuneResult_SuccessResult_default_instance_;
class AutotuningLog;
class AutotuningLogDefaultTypeInternal;
extern AutotuningLogDefaultTypeInternal _AutotuningLog_default_instance_;
class ComputeCapability;
class ComputeCapabilityDefaultTypeInternal;
extern ComputeCapabilityDefaultTypeInternal _ComputeCapability_default_instance_;
class CudnnVersion;
class CudnnVersionDefaultTypeInternal;
extern CudnnVersionDefaultTypeInternal _CudnnVersion_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::AutotuneResult* Arena::CreateMaybeMessage<::tensorflow::AutotuneResult>(Arena*);
template<> ::tensorflow::AutotuneResult_ConvKey* Arena::CreateMaybeMessage<::tensorflow::AutotuneResult_ConvKey>(Arena*);
template<> ::tensorflow::AutotuneResult_SuccessResult* Arena::CreateMaybeMessage<::tensorflow::AutotuneResult_SuccessResult>(Arena*);
template<> ::tensorflow::AutotuningLog* Arena::CreateMaybeMessage<::tensorflow::AutotuningLog>(Arena*);
template<> ::tensorflow::ComputeCapability* Arena::CreateMaybeMessage<::tensorflow::ComputeCapability>(Arena*);
template<> ::tensorflow::CudnnVersion* Arena::CreateMaybeMessage<::tensorflow::CudnnVersion>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

// ===================================================================

class CudnnVersion : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.CudnnVersion) */ {
 public:
  CudnnVersion();
  virtual ~CudnnVersion();

  CudnnVersion(const CudnnVersion& from);

  inline CudnnVersion& operator=(const CudnnVersion& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CudnnVersion(CudnnVersion&& from) noexcept
    : CudnnVersion() {
    *this = ::std::move(from);
  }

  inline CudnnVersion& operator=(CudnnVersion&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const CudnnVersion& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CudnnVersion* internal_default_instance() {
    return reinterpret_cast<const CudnnVersion*>(
               &_CudnnVersion_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void Swap(CudnnVersion* other);
  friend void swap(CudnnVersion& a, CudnnVersion& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CudnnVersion* New() const final {
    return CreateMaybeMessage<CudnnVersion>(NULL);
  }

  CudnnVersion* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CudnnVersion>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CudnnVersion& from);
  void MergeFrom(const CudnnVersion& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CudnnVersion* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int32 major = 1;
  void clear_major();
  static const int kMajorFieldNumber = 1;
  ::google::protobuf::int32 major() const;
  void set_major(::google::protobuf::int32 value);

  // int32 minor = 2;
  void clear_minor();
  static const int kMinorFieldNumber = 2;
  ::google::protobuf::int32 minor() const;
  void set_minor(::google::protobuf::int32 value);

  // int32 patch = 3;
  void clear_patch();
  static const int kPatchFieldNumber = 3;
  ::google::protobuf::int32 patch() const;
  void set_patch(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.CudnnVersion)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int32 major_;
  ::google::protobuf::int32 minor_;
  ::google::protobuf::int32 patch_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ComputeCapability : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.ComputeCapability) */ {
 public:
  ComputeCapability();
  virtual ~ComputeCapability();

  ComputeCapability(const ComputeCapability& from);

  inline ComputeCapability& operator=(const ComputeCapability& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ComputeCapability(ComputeCapability&& from) noexcept
    : ComputeCapability() {
    *this = ::std::move(from);
  }

  inline ComputeCapability& operator=(ComputeCapability&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ComputeCapability& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ComputeCapability* internal_default_instance() {
    return reinterpret_cast<const ComputeCapability*>(
               &_ComputeCapability_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void Swap(ComputeCapability* other);
  friend void swap(ComputeCapability& a, ComputeCapability& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ComputeCapability* New() const final {
    return CreateMaybeMessage<ComputeCapability>(NULL);
  }

  ComputeCapability* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ComputeCapability>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ComputeCapability& from);
  void MergeFrom(const ComputeCapability& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ComputeCapability* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int32 major = 1;
  void clear_major();
  static const int kMajorFieldNumber = 1;
  ::google::protobuf::int32 major() const;
  void set_major(::google::protobuf::int32 value);

  // int32 minor = 2;
  void clear_minor();
  static const int kMinorFieldNumber = 2;
  ::google::protobuf::int32 minor() const;
  void set_minor(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.ComputeCapability)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int32 major_;
  ::google::protobuf::int32 minor_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class AutotuneResult_SuccessResult : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.AutotuneResult.SuccessResult) */ {
 public:
  AutotuneResult_SuccessResult();
  virtual ~AutotuneResult_SuccessResult();

  AutotuneResult_SuccessResult(const AutotuneResult_SuccessResult& from);

  inline AutotuneResult_SuccessResult& operator=(const AutotuneResult_SuccessResult& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  AutotuneResult_SuccessResult(AutotuneResult_SuccessResult&& from) noexcept
    : AutotuneResult_SuccessResult() {
    *this = ::std::move(from);
  }

  inline AutotuneResult_SuccessResult& operator=(AutotuneResult_SuccessResult&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const AutotuneResult_SuccessResult& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AutotuneResult_SuccessResult* internal_default_instance() {
    return reinterpret_cast<const AutotuneResult_SuccessResult*>(
               &_AutotuneResult_SuccessResult_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void Swap(AutotuneResult_SuccessResult* other);
  friend void swap(AutotuneResult_SuccessResult& a, AutotuneResult_SuccessResult& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline AutotuneResult_SuccessResult* New() const final {
    return CreateMaybeMessage<AutotuneResult_SuccessResult>(NULL);
  }

  AutotuneResult_SuccessResult* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<AutotuneResult_SuccessResult>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const AutotuneResult_SuccessResult& from);
  void MergeFrom(const AutotuneResult_SuccessResult& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AutotuneResult_SuccessResult* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .google.protobuf.Duration run_time = 2;
  bool has_run_time() const;
  void clear_run_time();
  static const int kRunTimeFieldNumber = 2;
  private:
  const ::google::protobuf::Duration& _internal_run_time() const;
  public:
  const ::google::protobuf::Duration& run_time() const;
  ::google::protobuf::Duration* release_run_time();
  ::google::protobuf::Duration* mutable_run_time();
  void set_allocated_run_time(::google::protobuf::Duration* run_time);

  // int64 scratch_bytes = 1;
  void clear_scratch_bytes();
  static const int kScratchBytesFieldNumber = 1;
  ::google::protobuf::int64 scratch_bytes() const;
  void set_scratch_bytes(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.AutotuneResult.SuccessResult)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::Duration* run_time_;
  ::google::protobuf::int64 scratch_bytes_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class AutotuneResult_ConvKey : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.AutotuneResult.ConvKey) */ {
 public:
  AutotuneResult_ConvKey();
  virtual ~AutotuneResult_ConvKey();

  AutotuneResult_ConvKey(const AutotuneResult_ConvKey& from);

  inline AutotuneResult_ConvKey& operator=(const AutotuneResult_ConvKey& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  AutotuneResult_ConvKey(AutotuneResult_ConvKey&& from) noexcept
    : AutotuneResult_ConvKey() {
    *this = ::std::move(from);
  }

  inline AutotuneResult_ConvKey& operator=(AutotuneResult_ConvKey&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const AutotuneResult_ConvKey& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AutotuneResult_ConvKey* internal_default_instance() {
    return reinterpret_cast<const AutotuneResult_ConvKey*>(
               &_AutotuneResult_ConvKey_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void Swap(AutotuneResult_ConvKey* other);
  friend void swap(AutotuneResult_ConvKey& a, AutotuneResult_ConvKey& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline AutotuneResult_ConvKey* New() const final {
    return CreateMaybeMessage<AutotuneResult_ConvKey>(NULL);
  }

  AutotuneResult_ConvKey* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<AutotuneResult_ConvKey>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const AutotuneResult_ConvKey& from);
  void MergeFrom(const AutotuneResult_ConvKey& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AutotuneResult_ConvKey* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 algorithm = 1;
  void clear_algorithm();
  static const int kAlgorithmFieldNumber = 1;
  ::google::protobuf::int64 algorithm() const;
  void set_algorithm(::google::protobuf::int64 value);

  // bool tensor_ops_enabled = 2;
  void clear_tensor_ops_enabled();
  static const int kTensorOpsEnabledFieldNumber = 2;
  bool tensor_ops_enabled() const;
  void set_tensor_ops_enabled(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.AutotuneResult.ConvKey)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int64 algorithm_;
  bool tensor_ops_enabled_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class AutotuneResult : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.AutotuneResult) */ {
 public:
  AutotuneResult();
  virtual ~AutotuneResult();

  AutotuneResult(const AutotuneResult& from);

  inline AutotuneResult& operator=(const AutotuneResult& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  AutotuneResult(AutotuneResult&& from) noexcept
    : AutotuneResult() {
    *this = ::std::move(from);
  }

  inline AutotuneResult& operator=(AutotuneResult&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const AutotuneResult& default_instance();

  enum ResultCase {
    kSuccess = 3,
    kErrorString = 4,
    RESULT_NOT_SET = 0,
  };

  enum KeyCase {
    kConv = 5,
    KEY_NOT_SET = 0,
  };

  enum CheckerFailureCase {
    kReferenceConv = 6,
    CHECKER_FAILURE_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AutotuneResult* internal_default_instance() {
    return reinterpret_cast<const AutotuneResult*>(
               &_AutotuneResult_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void Swap(AutotuneResult* other);
  friend void swap(AutotuneResult& a, AutotuneResult& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline AutotuneResult* New() const final {
    return CreateMaybeMessage<AutotuneResult>(NULL);
  }

  AutotuneResult* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<AutotuneResult>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const AutotuneResult& from);
  void MergeFrom(const AutotuneResult& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AutotuneResult* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef AutotuneResult_SuccessResult SuccessResult;
  typedef AutotuneResult_ConvKey ConvKey;

  // accessors -------------------------------------------------------

  // .tensorflow.AutotuneResult.SuccessResult success = 3;
  bool has_success() const;
  void clear_success();
  static const int kSuccessFieldNumber = 3;
  private:
  const ::tensorflow::AutotuneResult_SuccessResult& _internal_success() const;
  public:
  const ::tensorflow::AutotuneResult_SuccessResult& success() const;
  ::tensorflow::AutotuneResult_SuccessResult* release_success();
  ::tensorflow::AutotuneResult_SuccessResult* mutable_success();
  void set_allocated_success(::tensorflow::AutotuneResult_SuccessResult* success);

  // string error_string = 4;
  private:
  bool has_error_string() const;
  public:
  void clear_error_string();
  static const int kErrorStringFieldNumber = 4;
  const ::std::string& error_string() const;
  void set_error_string(const ::std::string& value);
  #if LANG_CXX11
  void set_error_string(::std::string&& value);
  #endif
  void set_error_string(const char* value);
  void set_error_string(const char* value, size_t size);
  ::std::string* mutable_error_string();
  ::std::string* release_error_string();
  void set_allocated_error_string(::std::string* error_string);

  // .tensorflow.AutotuneResult.ConvKey conv = 5;
  bool has_conv() const;
  void clear_conv();
  static const int kConvFieldNumber = 5;
  private:
  const ::tensorflow::AutotuneResult_ConvKey& _internal_conv() const;
  public:
  const ::tensorflow::AutotuneResult_ConvKey& conv() const;
  ::tensorflow::AutotuneResult_ConvKey* release_conv();
  ::tensorflow::AutotuneResult_ConvKey* mutable_conv();
  void set_allocated_conv(::tensorflow::AutotuneResult_ConvKey* conv);

  // .tensorflow.AutotuneResult.ConvKey reference_conv = 6;
  bool has_reference_conv() const;
  void clear_reference_conv();
  static const int kReferenceConvFieldNumber = 6;
  private:
  const ::tensorflow::AutotuneResult_ConvKey& _internal_reference_conv() const;
  public:
  const ::tensorflow::AutotuneResult_ConvKey& reference_conv() const;
  ::tensorflow::AutotuneResult_ConvKey* release_reference_conv();
  ::tensorflow::AutotuneResult_ConvKey* mutable_reference_conv();
  void set_allocated_reference_conv(::tensorflow::AutotuneResult_ConvKey* reference_conv);

  void clear_result();
  ResultCase result_case() const;
  void clear_key();
  KeyCase key_case() const;
  void clear_checker_failure();
  CheckerFailureCase checker_failure_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.AutotuneResult)
 private:
  void set_has_success();
  void set_has_error_string();
  void set_has_conv();
  void set_has_reference_conv();

  inline bool has_result() const;
  inline void clear_has_result();

  inline bool has_key() const;
  inline void clear_has_key();

  inline bool has_checker_failure() const;
  inline void clear_has_checker_failure();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  union ResultUnion {
    ResultUnion() {}
    ::tensorflow::AutotuneResult_SuccessResult* success_;
    ::google::protobuf::internal::ArenaStringPtr error_string_;
  } result_;
  union KeyUnion {
    KeyUnion() {}
    ::tensorflow::AutotuneResult_ConvKey* conv_;
  } key_;
  union CheckerFailureUnion {
    CheckerFailureUnion() {}
    ::tensorflow::AutotuneResult_ConvKey* reference_conv_;
  } checker_failure_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[3];

  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class AutotuningLog : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.AutotuningLog) */ {
 public:
  AutotuningLog();
  virtual ~AutotuningLog();

  AutotuningLog(const AutotuningLog& from);

  inline AutotuningLog& operator=(const AutotuningLog& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  AutotuningLog(AutotuningLog&& from) noexcept
    : AutotuningLog() {
    *this = ::std::move(from);
  }

  inline AutotuningLog& operator=(AutotuningLog&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const AutotuningLog& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AutotuningLog* internal_default_instance() {
    return reinterpret_cast<const AutotuningLog*>(
               &_AutotuningLog_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  void Swap(AutotuningLog* other);
  friend void swap(AutotuningLog& a, AutotuningLog& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline AutotuningLog* New() const final {
    return CreateMaybeMessage<AutotuningLog>(NULL);
  }

  AutotuningLog* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<AutotuningLog>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const AutotuningLog& from);
  void MergeFrom(const AutotuningLog& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AutotuningLog* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.AutotuneResult results = 2;
  int results_size() const;
  void clear_results();
  static const int kResultsFieldNumber = 2;
  ::tensorflow::AutotuneResult* mutable_results(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::AutotuneResult >*
      mutable_results();
  const ::tensorflow::AutotuneResult& results(int index) const;
  ::tensorflow::AutotuneResult* add_results();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::AutotuneResult >&
      results() const;

  // .google.protobuf.Any instr = 1;
  bool has_instr() const;
  void clear_instr();
  static const int kInstrFieldNumber = 1;
  private:
  const ::google::protobuf::Any& _internal_instr() const;
  public:
  const ::google::protobuf::Any& instr() const;
  ::google::protobuf::Any* release_instr();
  ::google::protobuf::Any* mutable_instr();
  void set_allocated_instr(::google::protobuf::Any* instr);

  // .tensorflow.CudnnVersion cudnn_version = 3;
  bool has_cudnn_version() const;
  void clear_cudnn_version();
  static const int kCudnnVersionFieldNumber = 3;
  private:
  const ::tensorflow::CudnnVersion& _internal_cudnn_version() const;
  public:
  const ::tensorflow::CudnnVersion& cudnn_version() const;
  ::tensorflow::CudnnVersion* release_cudnn_version();
  ::tensorflow::CudnnVersion* mutable_cudnn_version();
  void set_allocated_cudnn_version(::tensorflow::CudnnVersion* cudnn_version);

  // .tensorflow.ComputeCapability compute_capability = 4;
  bool has_compute_capability() const;
  void clear_compute_capability();
  static const int kComputeCapabilityFieldNumber = 4;
  private:
  const ::tensorflow::ComputeCapability& _internal_compute_capability() const;
  public:
  const ::tensorflow::ComputeCapability& compute_capability() const;
  ::tensorflow::ComputeCapability* release_compute_capability();
  ::tensorflow::ComputeCapability* mutable_compute_capability();
  void set_allocated_compute_capability(::tensorflow::ComputeCapability* compute_capability);

  // @@protoc_insertion_point(class_scope:tensorflow.AutotuningLog)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::AutotuneResult > results_;
  ::google::protobuf::Any* instr_;
  ::tensorflow::CudnnVersion* cudnn_version_;
  ::tensorflow::ComputeCapability* compute_capability_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CudnnVersion

// int32 major = 1;
inline void CudnnVersion::clear_major() {
  major_ = 0;
}
inline ::google::protobuf::int32 CudnnVersion::major() const {
  // @@protoc_insertion_point(field_get:tensorflow.CudnnVersion.major)
  return major_;
}
inline void CudnnVersion::set_major(::google::protobuf::int32 value) {
  
  major_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CudnnVersion.major)
}

// int32 minor = 2;
inline void CudnnVersion::clear_minor() {
  minor_ = 0;
}
inline ::google::protobuf::int32 CudnnVersion::minor() const {
  // @@protoc_insertion_point(field_get:tensorflow.CudnnVersion.minor)
  return minor_;
}
inline void CudnnVersion::set_minor(::google::protobuf::int32 value) {
  
  minor_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CudnnVersion.minor)
}

// int32 patch = 3;
inline void CudnnVersion::clear_patch() {
  patch_ = 0;
}
inline ::google::protobuf::int32 CudnnVersion::patch() const {
  // @@protoc_insertion_point(field_get:tensorflow.CudnnVersion.patch)
  return patch_;
}
inline void CudnnVersion::set_patch(::google::protobuf::int32 value) {
  
  patch_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CudnnVersion.patch)
}

// -------------------------------------------------------------------

// ComputeCapability

// int32 major = 1;
inline void ComputeCapability::clear_major() {
  major_ = 0;
}
inline ::google::protobuf::int32 ComputeCapability::major() const {
  // @@protoc_insertion_point(field_get:tensorflow.ComputeCapability.major)
  return major_;
}
inline void ComputeCapability::set_major(::google::protobuf::int32 value) {
  
  major_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ComputeCapability.major)
}

// int32 minor = 2;
inline void ComputeCapability::clear_minor() {
  minor_ = 0;
}
inline ::google::protobuf::int32 ComputeCapability::minor() const {
  // @@protoc_insertion_point(field_get:tensorflow.ComputeCapability.minor)
  return minor_;
}
inline void ComputeCapability::set_minor(::google::protobuf::int32 value) {
  
  minor_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ComputeCapability.minor)
}

// -------------------------------------------------------------------

// AutotuneResult_SuccessResult

// int64 scratch_bytes = 1;
inline void AutotuneResult_SuccessResult::clear_scratch_bytes() {
  scratch_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 AutotuneResult_SuccessResult::scratch_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.AutotuneResult.SuccessResult.scratch_bytes)
  return scratch_bytes_;
}
inline void AutotuneResult_SuccessResult::set_scratch_bytes(::google::protobuf::int64 value) {
  
  scratch_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AutotuneResult.SuccessResult.scratch_bytes)
}

// .google.protobuf.Duration run_time = 2;
inline bool AutotuneResult_SuccessResult::has_run_time() const {
  return this != internal_default_instance() && run_time_ != NULL;
}
inline const ::google::protobuf::Duration& AutotuneResult_SuccessResult::_internal_run_time() const {
  return *run_time_;
}
inline const ::google::protobuf::Duration& AutotuneResult_SuccessResult::run_time() const {
  const ::google::protobuf::Duration* p = run_time_;
  // @@protoc_insertion_point(field_get:tensorflow.AutotuneResult.SuccessResult.run_time)
  return p != NULL ? *p : *reinterpret_cast<const ::google::protobuf::Duration*>(
      &::google::protobuf::_Duration_default_instance_);
}
inline ::google::protobuf::Duration* AutotuneResult_SuccessResult::release_run_time() {
  // @@protoc_insertion_point(field_release:tensorflow.AutotuneResult.SuccessResult.run_time)
  
  ::google::protobuf::Duration* temp = run_time_;
  run_time_ = NULL;
  return temp;
}
inline ::google::protobuf::Duration* AutotuneResult_SuccessResult::mutable_run_time() {
  
  if (run_time_ == NULL) {
    auto* p = CreateMaybeMessage<::google::protobuf::Duration>(GetArenaNoVirtual());
    run_time_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.AutotuneResult.SuccessResult.run_time)
  return run_time_;
}
inline void AutotuneResult_SuccessResult::set_allocated_run_time(::google::protobuf::Duration* run_time) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(run_time_);
  }
  if (run_time) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(run_time)->GetArena();
    if (message_arena != submessage_arena) {
      run_time = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, run_time, submessage_arena);
    }
    
  } else {
    
  }
  run_time_ = run_time;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AutotuneResult.SuccessResult.run_time)
}

// -------------------------------------------------------------------

// AutotuneResult_ConvKey

// int64 algorithm = 1;
inline void AutotuneResult_ConvKey::clear_algorithm() {
  algorithm_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 AutotuneResult_ConvKey::algorithm() const {
  // @@protoc_insertion_point(field_get:tensorflow.AutotuneResult.ConvKey.algorithm)
  return algorithm_;
}
inline void AutotuneResult_ConvKey::set_algorithm(::google::protobuf::int64 value) {
  
  algorithm_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AutotuneResult.ConvKey.algorithm)
}

// bool tensor_ops_enabled = 2;
inline void AutotuneResult_ConvKey::clear_tensor_ops_enabled() {
  tensor_ops_enabled_ = false;
}
inline bool AutotuneResult_ConvKey::tensor_ops_enabled() const {
  // @@protoc_insertion_point(field_get:tensorflow.AutotuneResult.ConvKey.tensor_ops_enabled)
  return tensor_ops_enabled_;
}
inline void AutotuneResult_ConvKey::set_tensor_ops_enabled(bool value) {
  
  tensor_ops_enabled_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AutotuneResult.ConvKey.tensor_ops_enabled)
}

// -------------------------------------------------------------------

// AutotuneResult

// .tensorflow.AutotuneResult.SuccessResult success = 3;
inline bool AutotuneResult::has_success() const {
  return result_case() == kSuccess;
}
inline void AutotuneResult::set_has_success() {
  _oneof_case_[0] = kSuccess;
}
inline void AutotuneResult::clear_success() {
  if (has_success()) {
    delete result_.success_;
    clear_has_result();
  }
}
inline const ::tensorflow::AutotuneResult_SuccessResult& AutotuneResult::_internal_success() const {
  return *result_.success_;
}
inline ::tensorflow::AutotuneResult_SuccessResult* AutotuneResult::release_success() {
  // @@protoc_insertion_point(field_release:tensorflow.AutotuneResult.success)
  if (has_success()) {
    clear_has_result();
      ::tensorflow::AutotuneResult_SuccessResult* temp = result_.success_;
    result_.success_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::AutotuneResult_SuccessResult& AutotuneResult::success() const {
  // @@protoc_insertion_point(field_get:tensorflow.AutotuneResult.success)
  return has_success()
      ? *result_.success_
      : *reinterpret_cast< ::tensorflow::AutotuneResult_SuccessResult*>(&::tensorflow::_AutotuneResult_SuccessResult_default_instance_);
}
inline ::tensorflow::AutotuneResult_SuccessResult* AutotuneResult::mutable_success() {
  if (!has_success()) {
    clear_result();
    set_has_success();
    result_.success_ = CreateMaybeMessage< ::tensorflow::AutotuneResult_SuccessResult >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.AutotuneResult.success)
  return result_.success_;
}

// string error_string = 4;
inline bool AutotuneResult::has_error_string() const {
  return result_case() == kErrorString;
}
inline void AutotuneResult::set_has_error_string() {
  _oneof_case_[0] = kErrorString;
}
inline void AutotuneResult::clear_error_string() {
  if (has_error_string()) {
    result_.error_string_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    clear_has_result();
  }
}
inline const ::std::string& AutotuneResult::error_string() const {
  // @@protoc_insertion_point(field_get:tensorflow.AutotuneResult.error_string)
  if (has_error_string()) {
    return result_.error_string_.GetNoArena();
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void AutotuneResult::set_error_string(const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.AutotuneResult.error_string)
  if (!has_error_string()) {
    clear_result();
    set_has_error_string();
    result_.error_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  result_.error_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.AutotuneResult.error_string)
}
#if LANG_CXX11
inline void AutotuneResult::set_error_string(::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.AutotuneResult.error_string)
  if (!has_error_string()) {
    clear_result();
    set_has_error_string();
    result_.error_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  result_.error_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.AutotuneResult.error_string)
}
#endif
inline void AutotuneResult::set_error_string(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  if (!has_error_string()) {
    clear_result();
    set_has_error_string();
    result_.error_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  result_.error_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.AutotuneResult.error_string)
}
inline void AutotuneResult::set_error_string(const char* value, size_t size) {
  if (!has_error_string()) {
    clear_result();
    set_has_error_string();
    result_.error_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  result_.error_string_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.AutotuneResult.error_string)
}
inline ::std::string* AutotuneResult::mutable_error_string() {
  if (!has_error_string()) {
    clear_result();
    set_has_error_string();
    result_.error_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.AutotuneResult.error_string)
  return result_.error_string_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* AutotuneResult::release_error_string() {
  // @@protoc_insertion_point(field_release:tensorflow.AutotuneResult.error_string)
  if (has_error_string()) {
    clear_has_result();
    return result_.error_string_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  } else {
    return NULL;
  }
}
inline void AutotuneResult::set_allocated_error_string(::std::string* error_string) {
  if (!has_error_string()) {
    result_.error_string_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_result();
  if (error_string != NULL) {
    set_has_error_string();
    result_.error_string_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), error_string);
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AutotuneResult.error_string)
}

// .tensorflow.AutotuneResult.ConvKey conv = 5;
inline bool AutotuneResult::has_conv() const {
  return key_case() == kConv;
}
inline void AutotuneResult::set_has_conv() {
  _oneof_case_[1] = kConv;
}
inline void AutotuneResult::clear_conv() {
  if (has_conv()) {
    delete key_.conv_;
    clear_has_key();
  }
}
inline const ::tensorflow::AutotuneResult_ConvKey& AutotuneResult::_internal_conv() const {
  return *key_.conv_;
}
inline ::tensorflow::AutotuneResult_ConvKey* AutotuneResult::release_conv() {
  // @@protoc_insertion_point(field_release:tensorflow.AutotuneResult.conv)
  if (has_conv()) {
    clear_has_key();
      ::tensorflow::AutotuneResult_ConvKey* temp = key_.conv_;
    key_.conv_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::AutotuneResult_ConvKey& AutotuneResult::conv() const {
  // @@protoc_insertion_point(field_get:tensorflow.AutotuneResult.conv)
  return has_conv()
      ? *key_.conv_
      : *reinterpret_cast< ::tensorflow::AutotuneResult_ConvKey*>(&::tensorflow::_AutotuneResult_ConvKey_default_instance_);
}
inline ::tensorflow::AutotuneResult_ConvKey* AutotuneResult::mutable_conv() {
  if (!has_conv()) {
    clear_key();
    set_has_conv();
    key_.conv_ = CreateMaybeMessage< ::tensorflow::AutotuneResult_ConvKey >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.AutotuneResult.conv)
  return key_.conv_;
}

// .tensorflow.AutotuneResult.ConvKey reference_conv = 6;
inline bool AutotuneResult::has_reference_conv() const {
  return checker_failure_case() == kReferenceConv;
}
inline void AutotuneResult::set_has_reference_conv() {
  _oneof_case_[2] = kReferenceConv;
}
inline void AutotuneResult::clear_reference_conv() {
  if (has_reference_conv()) {
    delete checker_failure_.reference_conv_;
    clear_has_checker_failure();
  }
}
inline const ::tensorflow::AutotuneResult_ConvKey& AutotuneResult::_internal_reference_conv() const {
  return *checker_failure_.reference_conv_;
}
inline ::tensorflow::AutotuneResult_ConvKey* AutotuneResult::release_reference_conv() {
  // @@protoc_insertion_point(field_release:tensorflow.AutotuneResult.reference_conv)
  if (has_reference_conv()) {
    clear_has_checker_failure();
      ::tensorflow::AutotuneResult_ConvKey* temp = checker_failure_.reference_conv_;
    checker_failure_.reference_conv_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::AutotuneResult_ConvKey& AutotuneResult::reference_conv() const {
  // @@protoc_insertion_point(field_get:tensorflow.AutotuneResult.reference_conv)
  return has_reference_conv()
      ? *checker_failure_.reference_conv_
      : *reinterpret_cast< ::tensorflow::AutotuneResult_ConvKey*>(&::tensorflow::_AutotuneResult_ConvKey_default_instance_);
}
inline ::tensorflow::AutotuneResult_ConvKey* AutotuneResult::mutable_reference_conv() {
  if (!has_reference_conv()) {
    clear_checker_failure();
    set_has_reference_conv();
    checker_failure_.reference_conv_ = CreateMaybeMessage< ::tensorflow::AutotuneResult_ConvKey >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.AutotuneResult.reference_conv)
  return checker_failure_.reference_conv_;
}

inline bool AutotuneResult::has_result() const {
  return result_case() != RESULT_NOT_SET;
}
inline void AutotuneResult::clear_has_result() {
  _oneof_case_[0] = RESULT_NOT_SET;
}
inline bool AutotuneResult::has_key() const {
  return key_case() != KEY_NOT_SET;
}
inline void AutotuneResult::clear_has_key() {
  _oneof_case_[1] = KEY_NOT_SET;
}
inline bool AutotuneResult::has_checker_failure() const {
  return checker_failure_case() != CHECKER_FAILURE_NOT_SET;
}
inline void AutotuneResult::clear_has_checker_failure() {
  _oneof_case_[2] = CHECKER_FAILURE_NOT_SET;
}
inline AutotuneResult::ResultCase AutotuneResult::result_case() const {
  return AutotuneResult::ResultCase(_oneof_case_[0]);
}
inline AutotuneResult::KeyCase AutotuneResult::key_case() const {
  return AutotuneResult::KeyCase(_oneof_case_[1]);
}
inline AutotuneResult::CheckerFailureCase AutotuneResult::checker_failure_case() const {
  return AutotuneResult::CheckerFailureCase(_oneof_case_[2]);
}
// -------------------------------------------------------------------

// AutotuningLog

// .google.protobuf.Any instr = 1;
inline bool AutotuningLog::has_instr() const {
  return this != internal_default_instance() && instr_ != NULL;
}
inline const ::google::protobuf::Any& AutotuningLog::_internal_instr() const {
  return *instr_;
}
inline const ::google::protobuf::Any& AutotuningLog::instr() const {
  const ::google::protobuf::Any* p = instr_;
  // @@protoc_insertion_point(field_get:tensorflow.AutotuningLog.instr)
  return p != NULL ? *p : *reinterpret_cast<const ::google::protobuf::Any*>(
      &::google::protobuf::_Any_default_instance_);
}
inline ::google::protobuf::Any* AutotuningLog::release_instr() {
  // @@protoc_insertion_point(field_release:tensorflow.AutotuningLog.instr)
  
  ::google::protobuf::Any* temp = instr_;
  instr_ = NULL;
  return temp;
}
inline ::google::protobuf::Any* AutotuningLog::mutable_instr() {
  
  if (instr_ == NULL) {
    auto* p = CreateMaybeMessage<::google::protobuf::Any>(GetArenaNoVirtual());
    instr_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.AutotuningLog.instr)
  return instr_;
}
inline void AutotuningLog::set_allocated_instr(::google::protobuf::Any* instr) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(instr_);
  }
  if (instr) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      instr = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, instr, submessage_arena);
    }
    
  } else {
    
  }
  instr_ = instr;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AutotuningLog.instr)
}

// repeated .tensorflow.AutotuneResult results = 2;
inline int AutotuningLog::results_size() const {
  return results_.size();
}
inline void AutotuningLog::clear_results() {
  results_.Clear();
}
inline ::tensorflow::AutotuneResult* AutotuningLog::mutable_results(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.AutotuningLog.results)
  return results_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::AutotuneResult >*
AutotuningLog::mutable_results() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.AutotuningLog.results)
  return &results_;
}
inline const ::tensorflow::AutotuneResult& AutotuningLog::results(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.AutotuningLog.results)
  return results_.Get(index);
}
inline ::tensorflow::AutotuneResult* AutotuningLog::add_results() {
  // @@protoc_insertion_point(field_add:tensorflow.AutotuningLog.results)
  return results_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::AutotuneResult >&
AutotuningLog::results() const {
  // @@protoc_insertion_point(field_list:tensorflow.AutotuningLog.results)
  return results_;
}

// .tensorflow.CudnnVersion cudnn_version = 3;
inline bool AutotuningLog::has_cudnn_version() const {
  return this != internal_default_instance() && cudnn_version_ != NULL;
}
inline void AutotuningLog::clear_cudnn_version() {
  if (GetArenaNoVirtual() == NULL && cudnn_version_ != NULL) {
    delete cudnn_version_;
  }
  cudnn_version_ = NULL;
}
inline const ::tensorflow::CudnnVersion& AutotuningLog::_internal_cudnn_version() const {
  return *cudnn_version_;
}
inline const ::tensorflow::CudnnVersion& AutotuningLog::cudnn_version() const {
  const ::tensorflow::CudnnVersion* p = cudnn_version_;
  // @@protoc_insertion_point(field_get:tensorflow.AutotuningLog.cudnn_version)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::CudnnVersion*>(
      &::tensorflow::_CudnnVersion_default_instance_);
}
inline ::tensorflow::CudnnVersion* AutotuningLog::release_cudnn_version() {
  // @@protoc_insertion_point(field_release:tensorflow.AutotuningLog.cudnn_version)
  
  ::tensorflow::CudnnVersion* temp = cudnn_version_;
  cudnn_version_ = NULL;
  return temp;
}
inline ::tensorflow::CudnnVersion* AutotuningLog::mutable_cudnn_version() {
  
  if (cudnn_version_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::CudnnVersion>(GetArenaNoVirtual());
    cudnn_version_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.AutotuningLog.cudnn_version)
  return cudnn_version_;
}
inline void AutotuningLog::set_allocated_cudnn_version(::tensorflow::CudnnVersion* cudnn_version) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete cudnn_version_;
  }
  if (cudnn_version) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      cudnn_version = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, cudnn_version, submessage_arena);
    }
    
  } else {
    
  }
  cudnn_version_ = cudnn_version;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AutotuningLog.cudnn_version)
}

// .tensorflow.ComputeCapability compute_capability = 4;
inline bool AutotuningLog::has_compute_capability() const {
  return this != internal_default_instance() && compute_capability_ != NULL;
}
inline void AutotuningLog::clear_compute_capability() {
  if (GetArenaNoVirtual() == NULL && compute_capability_ != NULL) {
    delete compute_capability_;
  }
  compute_capability_ = NULL;
}
inline const ::tensorflow::ComputeCapability& AutotuningLog::_internal_compute_capability() const {
  return *compute_capability_;
}
inline const ::tensorflow::ComputeCapability& AutotuningLog::compute_capability() const {
  const ::tensorflow::ComputeCapability* p = compute_capability_;
  // @@protoc_insertion_point(field_get:tensorflow.AutotuningLog.compute_capability)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::ComputeCapability*>(
      &::tensorflow::_ComputeCapability_default_instance_);
}
inline ::tensorflow::ComputeCapability* AutotuningLog::release_compute_capability() {
  // @@protoc_insertion_point(field_release:tensorflow.AutotuningLog.compute_capability)
  
  ::tensorflow::ComputeCapability* temp = compute_capability_;
  compute_capability_ = NULL;
  return temp;
}
inline ::tensorflow::ComputeCapability* AutotuningLog::mutable_compute_capability() {
  
  if (compute_capability_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::ComputeCapability>(GetArenaNoVirtual());
    compute_capability_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.AutotuningLog.compute_capability)
  return compute_capability_;
}
inline void AutotuningLog::set_allocated_compute_capability(::tensorflow::ComputeCapability* compute_capability) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete compute_capability_;
  }
  if (compute_capability) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      compute_capability = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, compute_capability, submessage_arena);
    }
    
  } else {
    
  }
  compute_capability_ = compute_capability;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AutotuningLog.compute_capability)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fautotuning_2eproto

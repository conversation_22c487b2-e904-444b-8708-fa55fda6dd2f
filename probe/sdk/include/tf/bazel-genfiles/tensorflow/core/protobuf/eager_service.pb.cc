// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/eager_service.proto

#include "tensorflow/core/protobuf/eager_service.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_AttrValue;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_DeviceAttributes;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto ::google::protobuf::internal::SCCInfo<5> scc_info_FunctionDef;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_TensorProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_TensorShapeProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2fversions_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fversions_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_VersionDef;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fversions_2eproto
namespace protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_RemoteTensorHandle;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_Operation_AttrsEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_QueueResponse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_Operation;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_QueueItem;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto
namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftensorflow_5fserver_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftensorflow_5fserver_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_ServerDef;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftensorflow_5fserver_2eproto
namespace tensorflow {
namespace eager {
class RemoteTensorHandleDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<RemoteTensorHandle>
      _instance;
} _RemoteTensorHandle_default_instance_;
class Operation_AttrsEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Operation_AttrsEntry_DoNotUse>
      _instance;
} _Operation_AttrsEntry_DoNotUse_default_instance_;
class OperationDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Operation>
      _instance;
} _Operation_default_instance_;
class QueueItemDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<QueueItem>
      _instance;
  const ::tensorflow::eager::RemoteTensorHandle* handle_to_decref_;
  const ::tensorflow::eager::Operation* operation_;
} _QueueItem_default_instance_;
class QueueResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<QueueResponse>
      _instance;
} _QueueResponse_default_instance_;
class CreateContextRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CreateContextRequest>
      _instance;
} _CreateContextRequest_default_instance_;
class CreateContextResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CreateContextResponse>
      _instance;
} _CreateContextResponse_default_instance_;
class EnqueueRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<EnqueueRequest>
      _instance;
} _EnqueueRequest_default_instance_;
class EnqueueResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<EnqueueResponse>
      _instance;
} _EnqueueResponse_default_instance_;
class WaitQueueDoneRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<WaitQueueDoneRequest>
      _instance;
} _WaitQueueDoneRequest_default_instance_;
class WaitQueueDoneResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<WaitQueueDoneResponse>
      _instance;
} _WaitQueueDoneResponse_default_instance_;
class KeepAliveRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<KeepAliveRequest>
      _instance;
} _KeepAliveRequest_default_instance_;
class KeepAliveResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<KeepAliveResponse>
      _instance;
} _KeepAliveResponse_default_instance_;
class CloseContextRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CloseContextRequest>
      _instance;
} _CloseContextRequest_default_instance_;
class CloseContextResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CloseContextResponse>
      _instance;
} _CloseContextResponse_default_instance_;
class RegisterFunctionRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<RegisterFunctionRequest>
      _instance;
} _RegisterFunctionRequest_default_instance_;
class RegisterFunctionResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<RegisterFunctionResponse>
      _instance;
} _RegisterFunctionResponse_default_instance_;
class SendTensorRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SendTensorRequest>
      _instance;
} _SendTensorRequest_default_instance_;
class SendTensorResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SendTensorResponse>
      _instance;
} _SendTensorResponse_default_instance_;
}  // namespace eager
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto {
static void InitDefaultsRemoteTensorHandle() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::eager::_RemoteTensorHandle_default_instance_;
    new (ptr) ::tensorflow::eager::RemoteTensorHandle();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::eager::RemoteTensorHandle::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_RemoteTensorHandle =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsRemoteTensorHandle}, {}};

static void InitDefaultsOperation_AttrsEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::eager::_Operation_AttrsEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::eager::Operation_AttrsEntry_DoNotUse();
  }
  ::tensorflow::eager::Operation_AttrsEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_Operation_AttrsEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsOperation_AttrsEntry_DoNotUse}, {
      &protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::scc_info_AttrValue.base,}};

static void InitDefaultsOperation() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::eager::_Operation_default_instance_;
    new (ptr) ::tensorflow::eager::Operation();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::eager::Operation::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_Operation =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsOperation}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_RemoteTensorHandle.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_Operation_AttrsEntry_DoNotUse.base,}};

static void InitDefaultsQueueItem() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::eager::_QueueItem_default_instance_;
    new (ptr) ::tensorflow::eager::QueueItem();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::eager::QueueItem::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_QueueItem =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsQueueItem}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_RemoteTensorHandle.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_Operation.base,}};

static void InitDefaultsQueueResponse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::eager::_QueueResponse_default_instance_;
    new (ptr) ::tensorflow::eager::QueueResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::eager::QueueResponse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_QueueResponse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsQueueResponse}, {
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::scc_info_TensorShapeProto.base,}};

static void InitDefaultsCreateContextRequest() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::eager::_CreateContextRequest_default_instance_;
    new (ptr) ::tensorflow::eager::CreateContextRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::eager::CreateContextRequest::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_CreateContextRequest =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsCreateContextRequest}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftensorflow_5fserver_2eproto::scc_info_ServerDef.base,
      &protobuf_tensorflow_2fcore_2fframework_2fversions_2eproto::scc_info_VersionDef.base,}};

static void InitDefaultsCreateContextResponse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::eager::_CreateContextResponse_default_instance_;
    new (ptr) ::tensorflow::eager::CreateContextResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::eager::CreateContextResponse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_CreateContextResponse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsCreateContextResponse}, {
      &protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::scc_info_DeviceAttributes.base,}};

static void InitDefaultsEnqueueRequest() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::eager::_EnqueueRequest_default_instance_;
    new (ptr) ::tensorflow::eager::EnqueueRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::eager::EnqueueRequest::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_EnqueueRequest =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsEnqueueRequest}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_QueueItem.base,}};

static void InitDefaultsEnqueueResponse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::eager::_EnqueueResponse_default_instance_;
    new (ptr) ::tensorflow::eager::EnqueueResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::eager::EnqueueResponse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_EnqueueResponse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsEnqueueResponse}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_QueueResponse.base,}};

static void InitDefaultsWaitQueueDoneRequest() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::eager::_WaitQueueDoneRequest_default_instance_;
    new (ptr) ::tensorflow::eager::WaitQueueDoneRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::eager::WaitQueueDoneRequest::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_WaitQueueDoneRequest =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsWaitQueueDoneRequest}, {}};

static void InitDefaultsWaitQueueDoneResponse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::eager::_WaitQueueDoneResponse_default_instance_;
    new (ptr) ::tensorflow::eager::WaitQueueDoneResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::eager::WaitQueueDoneResponse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_WaitQueueDoneResponse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsWaitQueueDoneResponse}, {}};

static void InitDefaultsKeepAliveRequest() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::eager::_KeepAliveRequest_default_instance_;
    new (ptr) ::tensorflow::eager::KeepAliveRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::eager::KeepAliveRequest::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_KeepAliveRequest =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsKeepAliveRequest}, {}};

static void InitDefaultsKeepAliveResponse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::eager::_KeepAliveResponse_default_instance_;
    new (ptr) ::tensorflow::eager::KeepAliveResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::eager::KeepAliveResponse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_KeepAliveResponse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsKeepAliveResponse}, {}};

static void InitDefaultsCloseContextRequest() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::eager::_CloseContextRequest_default_instance_;
    new (ptr) ::tensorflow::eager::CloseContextRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::eager::CloseContextRequest::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_CloseContextRequest =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsCloseContextRequest}, {}};

static void InitDefaultsCloseContextResponse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::eager::_CloseContextResponse_default_instance_;
    new (ptr) ::tensorflow::eager::CloseContextResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::eager::CloseContextResponse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_CloseContextResponse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsCloseContextResponse}, {}};

static void InitDefaultsRegisterFunctionRequest() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::eager::_RegisterFunctionRequest_default_instance_;
    new (ptr) ::tensorflow::eager::RegisterFunctionRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::eager::RegisterFunctionRequest::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_RegisterFunctionRequest =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsRegisterFunctionRequest}, {
      &protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::scc_info_FunctionDef.base,}};

static void InitDefaultsRegisterFunctionResponse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::eager::_RegisterFunctionResponse_default_instance_;
    new (ptr) ::tensorflow::eager::RegisterFunctionResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::eager::RegisterFunctionResponse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_RegisterFunctionResponse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsRegisterFunctionResponse}, {}};

static void InitDefaultsSendTensorRequest() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::eager::_SendTensorRequest_default_instance_;
    new (ptr) ::tensorflow::eager::SendTensorRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::eager::SendTensorRequest::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_SendTensorRequest =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsSendTensorRequest}, {
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::scc_info_TensorProto.base,}};

static void InitDefaultsSendTensorResponse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::eager::_SendTensorResponse_default_instance_;
    new (ptr) ::tensorflow::eager::SendTensorResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::eager::SendTensorResponse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_SendTensorResponse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsSendTensorResponse}, {}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_RemoteTensorHandle.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Operation_AttrsEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_Operation.base);
  ::google::protobuf::internal::InitSCC(&scc_info_QueueItem.base);
  ::google::protobuf::internal::InitSCC(&scc_info_QueueResponse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_CreateContextRequest.base);
  ::google::protobuf::internal::InitSCC(&scc_info_CreateContextResponse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_EnqueueRequest.base);
  ::google::protobuf::internal::InitSCC(&scc_info_EnqueueResponse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_WaitQueueDoneRequest.base);
  ::google::protobuf::internal::InitSCC(&scc_info_WaitQueueDoneResponse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_KeepAliveRequest.base);
  ::google::protobuf::internal::InitSCC(&scc_info_KeepAliveResponse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_CloseContextRequest.base);
  ::google::protobuf::internal::InitSCC(&scc_info_CloseContextResponse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_RegisterFunctionRequest.base);
  ::google::protobuf::internal::InitSCC(&scc_info_RegisterFunctionResponse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_SendTensorRequest.base);
  ::google::protobuf::internal::InitSCC(&scc_info_SendTensorResponse.base);
}

::google::protobuf::Metadata file_level_metadata[19];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::RemoteTensorHandle, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::RemoteTensorHandle, op_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::RemoteTensorHandle, output_num_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::Operation_AttrsEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::Operation_AttrsEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::Operation_AttrsEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::Operation_AttrsEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::Operation, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::Operation, id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::Operation, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::Operation, inputs_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::Operation, control_op_ids_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::Operation, attrs_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::Operation, device_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::QueueItem, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::QueueItem, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  offsetof(::tensorflow::eager::QueueItemDefaultTypeInternal, handle_to_decref_),
  offsetof(::tensorflow::eager::QueueItemDefaultTypeInternal, operation_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::QueueItem, item_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::QueueResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::QueueResponse, shape_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::CreateContextRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::CreateContextRequest, server_def_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::CreateContextRequest, async_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::CreateContextRequest, keep_alive_secs_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::CreateContextRequest, version_def_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::CreateContextRequest, rendezvous_id_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::CreateContextResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::CreateContextResponse, context_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::CreateContextResponse, device_attributes_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::EnqueueRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::EnqueueRequest, context_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::EnqueueRequest, queue_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::EnqueueResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::EnqueueResponse, queue_response_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::WaitQueueDoneRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::WaitQueueDoneRequest, context_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::WaitQueueDoneRequest, op_id_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::WaitQueueDoneResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::KeepAliveRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::KeepAliveRequest, context_id_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::KeepAliveResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::CloseContextRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::CloseContextRequest, context_id_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::CloseContextResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::RegisterFunctionRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::RegisterFunctionRequest, context_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::RegisterFunctionRequest, function_def_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::RegisterFunctionResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::SendTensorRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::SendTensorRequest, context_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::SendTensorRequest, op_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::SendTensorRequest, tensors_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::SendTensorRequest, device_name_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::eager::SendTensorResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::eager::RemoteTensorHandle)},
  { 7, 14, sizeof(::tensorflow::eager::Operation_AttrsEntry_DoNotUse)},
  { 16, -1, sizeof(::tensorflow::eager::Operation)},
  { 27, -1, sizeof(::tensorflow::eager::QueueItem)},
  { 35, -1, sizeof(::tensorflow::eager::QueueResponse)},
  { 41, -1, sizeof(::tensorflow::eager::CreateContextRequest)},
  { 51, -1, sizeof(::tensorflow::eager::CreateContextResponse)},
  { 58, -1, sizeof(::tensorflow::eager::EnqueueRequest)},
  { 65, -1, sizeof(::tensorflow::eager::EnqueueResponse)},
  { 71, -1, sizeof(::tensorflow::eager::WaitQueueDoneRequest)},
  { 78, -1, sizeof(::tensorflow::eager::WaitQueueDoneResponse)},
  { 83, -1, sizeof(::tensorflow::eager::KeepAliveRequest)},
  { 89, -1, sizeof(::tensorflow::eager::KeepAliveResponse)},
  { 94, -1, sizeof(::tensorflow::eager::CloseContextRequest)},
  { 100, -1, sizeof(::tensorflow::eager::CloseContextResponse)},
  { 105, -1, sizeof(::tensorflow::eager::RegisterFunctionRequest)},
  { 112, -1, sizeof(::tensorflow::eager::RegisterFunctionResponse)},
  { 117, -1, sizeof(::tensorflow::eager::SendTensorRequest)},
  { 126, -1, sizeof(::tensorflow::eager::SendTensorResponse)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::eager::_RemoteTensorHandle_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::eager::_Operation_AttrsEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::eager::_Operation_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::eager::_QueueItem_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::eager::_QueueResponse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::eager::_CreateContextRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::eager::_CreateContextResponse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::eager::_EnqueueRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::eager::_EnqueueResponse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::eager::_WaitQueueDoneRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::eager::_WaitQueueDoneResponse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::eager::_KeepAliveRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::eager::_KeepAliveResponse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::eager::_CloseContextRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::eager::_CloseContextResponse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::eager::_RegisterFunctionRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::eager::_RegisterFunctionResponse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::eager::_SendTensorRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::eager::_SendTensorResponse_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/protobuf/eager_service.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 19);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n,tensorflow/core/protobuf/eager_service"
      ".proto\022\020tensorflow.eager\032*tensorflow/cor"
      "e/framework/attr_value.proto\0321tensorflow"
      "/core/framework/device_attributes.proto\032"
      "(tensorflow/core/framework/function.prot"
      "o\032(tensorflow/core/framework/versions.pr"
      "oto\0320tensorflow/core/protobuf/tensorflow"
      "_server.proto\032,tensorflow/core/framework"
      "/tensor_shape.proto\032&tensorflow/core/fra"
      "mework/tensor.proto\"7\n\022RemoteTensorHandl"
      "e\022\r\n\005op_id\030\001 \001(\003\022\022\n\noutput_num\030\002 \001(\005\"\377\001\n"
      "\tOperation\022\n\n\002id\030\001 \001(\003\022\014\n\004name\030\002 \001(\t\0224\n\006"
      "inputs\030\003 \003(\0132$.tensorflow.eager.RemoteTe"
      "nsorHandle\022\026\n\016control_op_ids\030\004 \003(\003\0225\n\005at"
      "trs\030\005 \003(\0132&.tensorflow.eager.Operation.A"
      "ttrsEntry\022\016\n\006device\030\006 \001(\t\032C\n\nAttrsEntry\022"
      "\013\n\003key\030\001 \001(\t\022$\n\005value\030\002 \001(\0132\025.tensorflow"
      ".AttrValue:\0028\001\"\207\001\n\tQueueItem\022@\n\020handle_t"
      "o_decref\030\001 \001(\0132$.tensorflow.eager.Remote"
      "TensorHandleH\000\0220\n\toperation\030\002 \001(\0132\033.tens"
      "orflow.eager.OperationH\000B\006\n\004item\"<\n\rQueu"
      "eResponse\022+\n\005shape\030\001 \003(\0132\034.tensorflow.Te"
      "nsorShapeProto\"\255\001\n\024CreateContextRequest\022"
      ")\n\nserver_def\030\001 \001(\0132\025.tensorflow.ServerD"
      "ef\022\r\n\005async\030\002 \001(\010\022\027\n\017keep_alive_secs\030\003 \001"
      "(\003\022+\n\013version_def\030\004 \001(\0132\026.tensorflow.Ver"
      "sionDef\022\025\n\rrendezvous_id\030\005 \001(\003\"d\n\025Create"
      "ContextResponse\022\022\n\ncontext_id\030\001 \001(\006\0227\n\021d"
      "evice_attributes\030\002 \003(\0132\034.tensorflow.Devi"
      "ceAttributes\"P\n\016EnqueueRequest\022\022\n\ncontex"
      "t_id\030\001 \001(\006\022*\n\005queue\030\003 \003(\0132\033.tensorflow.e"
      "ager.QueueItem\"J\n\017EnqueueResponse\0227\n\016que"
      "ue_response\030\001 \003(\0132\037.tensorflow.eager.Que"
      "ueResponse\"9\n\024WaitQueueDoneRequest\022\022\n\nco"
      "ntext_id\030\001 \001(\006\022\r\n\005op_id\030\002 \003(\003\"\027\n\025WaitQue"
      "ueDoneResponse\"&\n\020KeepAliveRequest\022\022\n\nco"
      "ntext_id\030\001 \001(\006\"\023\n\021KeepAliveResponse\")\n\023C"
      "loseContextRequest\022\022\n\ncontext_id\030\001 \001(\006\"\026"
      "\n\024CloseContextResponse\"\\\n\027RegisterFuncti"
      "onRequest\022\022\n\ncontext_id\030\001 \001(\006\022-\n\014functio"
      "n_def\030\002 \001(\0132\027.tensorflow.FunctionDef\"\032\n\030"
      "RegisterFunctionResponse\"u\n\021SendTensorRe"
      "quest\022\022\n\ncontext_id\030\001 \001(\006\022\r\n\005op_id\030\002 \001(\003"
      "\022(\n\007tensors\030\003 \003(\0132\027.tensorflow.TensorPro"
      "to\022\023\n\013device_name\030\004 \001(\t\"\024\n\022SendTensorRes"
      "ponse2\233\005\n\014EagerService\022`\n\rCreateContext\022"
      "&.tensorflow.eager.CreateContextRequest\032"
      "\'.tensorflow.eager.CreateContextResponse"
      "\022N\n\007Enqueue\022 .tensorflow.eager.EnqueueRe"
      "quest\032!.tensorflow.eager.EnqueueResponse"
      "\022`\n\rWaitQueueDone\022&.tensorflow.eager.Wai"
      "tQueueDoneRequest\032\'.tensorflow.eager.Wai"
      "tQueueDoneResponse\022T\n\tKeepAlive\022\".tensor"
      "flow.eager.KeepAliveRequest\032#.tensorflow"
      ".eager.KeepAliveResponse\022]\n\014CloseContext"
      "\022%.tensorflow.eager.CloseContextRequest\032"
      "&.tensorflow.eager.CloseContextResponse\022"
      "i\n\020RegisterFunction\022).tensorflow.eager.R"
      "egisterFunctionRequest\032*.tensorflow.eage"
      "r.RegisterFunctionResponse\022W\n\nSendTensor"
      "\022#.tensorflow.eager.SendTensorRequest\032$."
      "tensorflow.eager.SendTensorResponseb\006pro"
      "to3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 2483);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/protobuf/eager_service.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ffunction_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2fversions_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftensorflow_5fserver_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto
namespace tensorflow {
namespace eager {

// ===================================================================

void RemoteTensorHandle::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RemoteTensorHandle::kOpIdFieldNumber;
const int RemoteTensorHandle::kOutputNumFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RemoteTensorHandle::RemoteTensorHandle()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_RemoteTensorHandle.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.eager.RemoteTensorHandle)
}
RemoteTensorHandle::RemoteTensorHandle(const RemoteTensorHandle& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&op_id_, &from.op_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&output_num_) -
    reinterpret_cast<char*>(&op_id_)) + sizeof(output_num_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.eager.RemoteTensorHandle)
}

void RemoteTensorHandle::SharedCtor() {
  ::memset(&op_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&output_num_) -
      reinterpret_cast<char*>(&op_id_)) + sizeof(output_num_));
}

RemoteTensorHandle::~RemoteTensorHandle() {
  // @@protoc_insertion_point(destructor:tensorflow.eager.RemoteTensorHandle)
  SharedDtor();
}

void RemoteTensorHandle::SharedDtor() {
}

void RemoteTensorHandle::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* RemoteTensorHandle::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const RemoteTensorHandle& RemoteTensorHandle::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_RemoteTensorHandle.base);
  return *internal_default_instance();
}


void RemoteTensorHandle::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.eager.RemoteTensorHandle)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&op_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&output_num_) -
      reinterpret_cast<char*>(&op_id_)) + sizeof(output_num_));
  _internal_metadata_.Clear();
}

bool RemoteTensorHandle::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.eager.RemoteTensorHandle)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 op_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &op_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 output_num = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &output_num_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.eager.RemoteTensorHandle)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.eager.RemoteTensorHandle)
  return false;
#undef DO_
}

void RemoteTensorHandle::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.eager.RemoteTensorHandle)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 op_id = 1;
  if (this->op_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->op_id(), output);
  }

  // int32 output_num = 2;
  if (this->output_num() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->output_num(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.eager.RemoteTensorHandle)
}

::google::protobuf::uint8* RemoteTensorHandle::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.eager.RemoteTensorHandle)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 op_id = 1;
  if (this->op_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->op_id(), target);
  }

  // int32 output_num = 2;
  if (this->output_num() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->output_num(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.eager.RemoteTensorHandle)
  return target;
}

size_t RemoteTensorHandle::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.eager.RemoteTensorHandle)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int64 op_id = 1;
  if (this->op_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->op_id());
  }

  // int32 output_num = 2;
  if (this->output_num() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->output_num());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RemoteTensorHandle::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.eager.RemoteTensorHandle)
  GOOGLE_DCHECK_NE(&from, this);
  const RemoteTensorHandle* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RemoteTensorHandle>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.eager.RemoteTensorHandle)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.eager.RemoteTensorHandle)
    MergeFrom(*source);
  }
}

void RemoteTensorHandle::MergeFrom(const RemoteTensorHandle& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.eager.RemoteTensorHandle)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.op_id() != 0) {
    set_op_id(from.op_id());
  }
  if (from.output_num() != 0) {
    set_output_num(from.output_num());
  }
}

void RemoteTensorHandle::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.eager.RemoteTensorHandle)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RemoteTensorHandle::CopyFrom(const RemoteTensorHandle& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.eager.RemoteTensorHandle)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RemoteTensorHandle::IsInitialized() const {
  return true;
}

void RemoteTensorHandle::Swap(RemoteTensorHandle* other) {
  if (other == this) return;
  InternalSwap(other);
}
void RemoteTensorHandle::InternalSwap(RemoteTensorHandle* other) {
  using std::swap;
  swap(op_id_, other->op_id_);
  swap(output_num_, other->output_num_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata RemoteTensorHandle::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

Operation_AttrsEntry_DoNotUse::Operation_AttrsEntry_DoNotUse() {}
Operation_AttrsEntry_DoNotUse::Operation_AttrsEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void Operation_AttrsEntry_DoNotUse::MergeFrom(const Operation_AttrsEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata Operation_AttrsEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[1];
}
void Operation_AttrsEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void Operation::InitAsDefaultInstance() {
}
void Operation::clear_attrs() {
  attrs_.Clear();
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Operation::kIdFieldNumber;
const int Operation::kNameFieldNumber;
const int Operation::kInputsFieldNumber;
const int Operation::kControlOpIdsFieldNumber;
const int Operation::kAttrsFieldNumber;
const int Operation::kDeviceFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Operation::Operation()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_Operation.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.eager.Operation)
}
Operation::Operation(const Operation& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      inputs_(from.inputs_),
      control_op_ids_(from.control_op_ids_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  attrs_.MergeFrom(from.attrs_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  device_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.device().size() > 0) {
    device_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.device_);
  }
  id_ = from.id_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.eager.Operation)
}

void Operation::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  device_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  id_ = GOOGLE_LONGLONG(0);
}

Operation::~Operation() {
  // @@protoc_insertion_point(destructor:tensorflow.eager.Operation)
  SharedDtor();
}

void Operation::SharedDtor() {
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  device_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void Operation::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* Operation::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Operation& Operation::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_Operation.base);
  return *internal_default_instance();
}


void Operation::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.eager.Operation)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  inputs_.Clear();
  control_op_ids_.Clear();
  attrs_.Clear();
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  device_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  id_ = GOOGLE_LONGLONG(0);
  _internal_metadata_.Clear();
}

bool Operation::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.eager.Operation)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string name = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.eager.Operation.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.eager.RemoteTensorHandle inputs = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_inputs()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated int64 control_op_ids = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_control_op_ids())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 1, 34u, input, this->mutable_control_op_ids())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, .tensorflow.AttrValue> attrs = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          Operation_AttrsEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              Operation_AttrsEntry_DoNotUse,
              ::std::string, ::tensorflow::AttrValue,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue > > parser(&attrs_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.eager.Operation.AttrsEntry.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string device = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_device()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->device().data(), static_cast<int>(this->device().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.eager.Operation.device"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.eager.Operation)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.eager.Operation)
  return false;
#undef DO_
}

void Operation::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.eager.Operation)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 id = 1;
  if (this->id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->id(), output);
  }

  // string name = 2;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.eager.Operation.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->name(), output);
  }

  // repeated .tensorflow.eager.RemoteTensorHandle inputs = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->inputs_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3,
      this->inputs(static_cast<int>(i)),
      output);
  }

  // repeated int64 control_op_ids = 4;
  if (this->control_op_ids_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(4, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _control_op_ids_cached_byte_size_));
  }
  for (int i = 0, n = this->control_op_ids_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->control_op_ids(i), output);
  }

  // map<string, .tensorflow.AttrValue> attrs = 5;
  if (!this->attrs().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.eager.Operation.AttrsEntry.key");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->attrs().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->attrs().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
          it = this->attrs().begin();
          it != this->attrs().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<Operation_AttrsEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(attrs_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            5, *entry, output);
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<Operation_AttrsEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
          it = this->attrs().begin();
          it != this->attrs().end(); ++it) {
        entry.reset(attrs_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            5, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  // string device = 6;
  if (this->device().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->device().data(), static_cast<int>(this->device().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.eager.Operation.device");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      6, this->device(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.eager.Operation)
}

::google::protobuf::uint8* Operation::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.eager.Operation)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 id = 1;
  if (this->id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->id(), target);
  }

  // string name = 2;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.eager.Operation.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->name(), target);
  }

  // repeated .tensorflow.eager.RemoteTensorHandle inputs = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->inputs_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->inputs(static_cast<int>(i)), deterministic, target);
  }

  // repeated int64 control_op_ids = 4;
  if (this->control_op_ids_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      4,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _control_op_ids_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->control_op_ids_, target);
  }

  // map<string, .tensorflow.AttrValue> attrs = 5;
  if (!this->attrs().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.eager.Operation.AttrsEntry.key");
      }
    };

    if (deterministic &&
        this->attrs().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->attrs().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
          it = this->attrs().begin();
          it != this->attrs().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<Operation_AttrsEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(attrs_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       5, *entry, deterministic, target);
;
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<Operation_AttrsEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
          it = this->attrs().begin();
          it != this->attrs().end(); ++it) {
        entry.reset(attrs_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       5, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  // string device = 6;
  if (this->device().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->device().data(), static_cast<int>(this->device().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.eager.Operation.device");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        6, this->device(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.eager.Operation)
  return target;
}

size_t Operation::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.eager.Operation)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.eager.RemoteTensorHandle inputs = 3;
  {
    unsigned int count = static_cast<unsigned int>(this->inputs_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->inputs(static_cast<int>(i)));
    }
  }

  // repeated int64 control_op_ids = 4;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int64Size(this->control_op_ids_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _control_op_ids_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // map<string, .tensorflow.AttrValue> attrs = 5;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->attrs_size());
  {
    ::std::unique_ptr<Operation_AttrsEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
        it = this->attrs().begin();
        it != this->attrs().end(); ++it) {
      entry.reset(attrs_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // string name = 2;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // string device = 6;
  if (this->device().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->device());
  }

  // int64 id = 1;
  if (this->id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Operation::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.eager.Operation)
  GOOGLE_DCHECK_NE(&from, this);
  const Operation* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Operation>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.eager.Operation)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.eager.Operation)
    MergeFrom(*source);
  }
}

void Operation::MergeFrom(const Operation& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.eager.Operation)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  inputs_.MergeFrom(from.inputs_);
  control_op_ids_.MergeFrom(from.control_op_ids_);
  attrs_.MergeFrom(from.attrs_);
  if (from.name().size() > 0) {

    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  if (from.device().size() > 0) {

    device_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.device_);
  }
  if (from.id() != 0) {
    set_id(from.id());
  }
}

void Operation::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.eager.Operation)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Operation::CopyFrom(const Operation& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.eager.Operation)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Operation::IsInitialized() const {
  return true;
}

void Operation::Swap(Operation* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Operation::InternalSwap(Operation* other) {
  using std::swap;
  CastToBase(&inputs_)->InternalSwap(CastToBase(&other->inputs_));
  control_op_ids_.InternalSwap(&other->control_op_ids_);
  attrs_.Swap(&other->attrs_);
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  device_.Swap(&other->device_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(id_, other->id_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata Operation::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void QueueItem::InitAsDefaultInstance() {
  ::tensorflow::eager::_QueueItem_default_instance_.handle_to_decref_ = const_cast< ::tensorflow::eager::RemoteTensorHandle*>(
      ::tensorflow::eager::RemoteTensorHandle::internal_default_instance());
  ::tensorflow::eager::_QueueItem_default_instance_.operation_ = const_cast< ::tensorflow::eager::Operation*>(
      ::tensorflow::eager::Operation::internal_default_instance());
}
void QueueItem::set_allocated_handle_to_decref(::tensorflow::eager::RemoteTensorHandle* handle_to_decref) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_item();
  if (handle_to_decref) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      handle_to_decref = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, handle_to_decref, submessage_arena);
    }
    set_has_handle_to_decref();
    item_.handle_to_decref_ = handle_to_decref;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.QueueItem.handle_to_decref)
}
void QueueItem::set_allocated_operation(::tensorflow::eager::Operation* operation) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_item();
  if (operation) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      operation = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, operation, submessage_arena);
    }
    set_has_operation();
    item_.operation_ = operation;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.QueueItem.operation)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int QueueItem::kHandleToDecrefFieldNumber;
const int QueueItem::kOperationFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

QueueItem::QueueItem()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_QueueItem.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.eager.QueueItem)
}
QueueItem::QueueItem(const QueueItem& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  clear_has_item();
  switch (from.item_case()) {
    case kHandleToDecref: {
      mutable_handle_to_decref()->::tensorflow::eager::RemoteTensorHandle::MergeFrom(from.handle_to_decref());
      break;
    }
    case kOperation: {
      mutable_operation()->::tensorflow::eager::Operation::MergeFrom(from.operation());
      break;
    }
    case ITEM_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.eager.QueueItem)
}

void QueueItem::SharedCtor() {
  clear_has_item();
}

QueueItem::~QueueItem() {
  // @@protoc_insertion_point(destructor:tensorflow.eager.QueueItem)
  SharedDtor();
}

void QueueItem::SharedDtor() {
  if (has_item()) {
    clear_item();
  }
}

void QueueItem::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* QueueItem::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const QueueItem& QueueItem::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_QueueItem.base);
  return *internal_default_instance();
}


void QueueItem::clear_item() {
// @@protoc_insertion_point(one_of_clear_start:tensorflow.eager.QueueItem)
  switch (item_case()) {
    case kHandleToDecref: {
      delete item_.handle_to_decref_;
      break;
    }
    case kOperation: {
      delete item_.operation_;
      break;
    }
    case ITEM_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = ITEM_NOT_SET;
}


void QueueItem::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.eager.QueueItem)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_item();
  _internal_metadata_.Clear();
}

bool QueueItem::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.eager.QueueItem)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.eager.RemoteTensorHandle handle_to_decref = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_handle_to_decref()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.eager.Operation operation = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_operation()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.eager.QueueItem)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.eager.QueueItem)
  return false;
#undef DO_
}

void QueueItem::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.eager.QueueItem)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.eager.RemoteTensorHandle handle_to_decref = 1;
  if (has_handle_to_decref()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->_internal_handle_to_decref(), output);
  }

  // .tensorflow.eager.Operation operation = 2;
  if (has_operation()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_operation(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.eager.QueueItem)
}

::google::protobuf::uint8* QueueItem::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.eager.QueueItem)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.eager.RemoteTensorHandle handle_to_decref = 1;
  if (has_handle_to_decref()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->_internal_handle_to_decref(), deterministic, target);
  }

  // .tensorflow.eager.Operation operation = 2;
  if (has_operation()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_operation(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.eager.QueueItem)
  return target;
}

size_t QueueItem::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.eager.QueueItem)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  switch (item_case()) {
    // .tensorflow.eager.RemoteTensorHandle handle_to_decref = 1;
    case kHandleToDecref: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *item_.handle_to_decref_);
      break;
    }
    // .tensorflow.eager.Operation operation = 2;
    case kOperation: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *item_.operation_);
      break;
    }
    case ITEM_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void QueueItem::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.eager.QueueItem)
  GOOGLE_DCHECK_NE(&from, this);
  const QueueItem* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const QueueItem>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.eager.QueueItem)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.eager.QueueItem)
    MergeFrom(*source);
  }
}

void QueueItem::MergeFrom(const QueueItem& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.eager.QueueItem)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.item_case()) {
    case kHandleToDecref: {
      mutable_handle_to_decref()->::tensorflow::eager::RemoteTensorHandle::MergeFrom(from.handle_to_decref());
      break;
    }
    case kOperation: {
      mutable_operation()->::tensorflow::eager::Operation::MergeFrom(from.operation());
      break;
    }
    case ITEM_NOT_SET: {
      break;
    }
  }
}

void QueueItem::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.eager.QueueItem)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void QueueItem::CopyFrom(const QueueItem& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.eager.QueueItem)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool QueueItem::IsInitialized() const {
  return true;
}

void QueueItem::Swap(QueueItem* other) {
  if (other == this) return;
  InternalSwap(other);
}
void QueueItem::InternalSwap(QueueItem* other) {
  using std::swap;
  swap(item_, other->item_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata QueueItem::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void QueueResponse::InitAsDefaultInstance() {
}
void QueueResponse::clear_shape() {
  shape_.Clear();
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int QueueResponse::kShapeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

QueueResponse::QueueResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_QueueResponse.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.eager.QueueResponse)
}
QueueResponse::QueueResponse(const QueueResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      shape_(from.shape_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.eager.QueueResponse)
}

void QueueResponse::SharedCtor() {
}

QueueResponse::~QueueResponse() {
  // @@protoc_insertion_point(destructor:tensorflow.eager.QueueResponse)
  SharedDtor();
}

void QueueResponse::SharedDtor() {
}

void QueueResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* QueueResponse::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const QueueResponse& QueueResponse::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_QueueResponse.base);
  return *internal_default_instance();
}


void QueueResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.eager.QueueResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  shape_.Clear();
  _internal_metadata_.Clear();
}

bool QueueResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.eager.QueueResponse)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.TensorShapeProto shape = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_shape()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.eager.QueueResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.eager.QueueResponse)
  return false;
#undef DO_
}

void QueueResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.eager.QueueResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.TensorShapeProto shape = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->shape_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->shape(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.eager.QueueResponse)
}

::google::protobuf::uint8* QueueResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.eager.QueueResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.TensorShapeProto shape = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->shape_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->shape(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.eager.QueueResponse)
  return target;
}

size_t QueueResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.eager.QueueResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.TensorShapeProto shape = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->shape_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->shape(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void QueueResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.eager.QueueResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const QueueResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const QueueResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.eager.QueueResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.eager.QueueResponse)
    MergeFrom(*source);
  }
}

void QueueResponse::MergeFrom(const QueueResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.eager.QueueResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  shape_.MergeFrom(from.shape_);
}

void QueueResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.eager.QueueResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void QueueResponse::CopyFrom(const QueueResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.eager.QueueResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool QueueResponse::IsInitialized() const {
  return true;
}

void QueueResponse::Swap(QueueResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void QueueResponse::InternalSwap(QueueResponse* other) {
  using std::swap;
  CastToBase(&shape_)->InternalSwap(CastToBase(&other->shape_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata QueueResponse::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void CreateContextRequest::InitAsDefaultInstance() {
  ::tensorflow::eager::_CreateContextRequest_default_instance_._instance.get_mutable()->server_def_ = const_cast< ::tensorflow::ServerDef*>(
      ::tensorflow::ServerDef::internal_default_instance());
  ::tensorflow::eager::_CreateContextRequest_default_instance_._instance.get_mutable()->version_def_ = const_cast< ::tensorflow::VersionDef*>(
      ::tensorflow::VersionDef::internal_default_instance());
}
void CreateContextRequest::clear_server_def() {
  if (GetArenaNoVirtual() == NULL && server_def_ != NULL) {
    delete server_def_;
  }
  server_def_ = NULL;
}
void CreateContextRequest::clear_version_def() {
  if (GetArenaNoVirtual() == NULL && version_def_ != NULL) {
    delete version_def_;
  }
  version_def_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CreateContextRequest::kServerDefFieldNumber;
const int CreateContextRequest::kAsyncFieldNumber;
const int CreateContextRequest::kKeepAliveSecsFieldNumber;
const int CreateContextRequest::kVersionDefFieldNumber;
const int CreateContextRequest::kRendezvousIdFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CreateContextRequest::CreateContextRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_CreateContextRequest.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.eager.CreateContextRequest)
}
CreateContextRequest::CreateContextRequest(const CreateContextRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_server_def()) {
    server_def_ = new ::tensorflow::ServerDef(*from.server_def_);
  } else {
    server_def_ = NULL;
  }
  if (from.has_version_def()) {
    version_def_ = new ::tensorflow::VersionDef(*from.version_def_);
  } else {
    version_def_ = NULL;
  }
  ::memcpy(&keep_alive_secs_, &from.keep_alive_secs_,
    static_cast<size_t>(reinterpret_cast<char*>(&async_) -
    reinterpret_cast<char*>(&keep_alive_secs_)) + sizeof(async_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.eager.CreateContextRequest)
}

void CreateContextRequest::SharedCtor() {
  ::memset(&server_def_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&async_) -
      reinterpret_cast<char*>(&server_def_)) + sizeof(async_));
}

CreateContextRequest::~CreateContextRequest() {
  // @@protoc_insertion_point(destructor:tensorflow.eager.CreateContextRequest)
  SharedDtor();
}

void CreateContextRequest::SharedDtor() {
  if (this != internal_default_instance()) delete server_def_;
  if (this != internal_default_instance()) delete version_def_;
}

void CreateContextRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* CreateContextRequest::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CreateContextRequest& CreateContextRequest::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_CreateContextRequest.base);
  return *internal_default_instance();
}


void CreateContextRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.eager.CreateContextRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && server_def_ != NULL) {
    delete server_def_;
  }
  server_def_ = NULL;
  if (GetArenaNoVirtual() == NULL && version_def_ != NULL) {
    delete version_def_;
  }
  version_def_ = NULL;
  ::memset(&keep_alive_secs_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&async_) -
      reinterpret_cast<char*>(&keep_alive_secs_)) + sizeof(async_));
  _internal_metadata_.Clear();
}

bool CreateContextRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.eager.CreateContextRequest)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.ServerDef server_def = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_server_def()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool async = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &async_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 keep_alive_secs = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &keep_alive_secs_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.VersionDef version_def = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_version_def()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 rendezvous_id = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &rendezvous_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.eager.CreateContextRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.eager.CreateContextRequest)
  return false;
#undef DO_
}

void CreateContextRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.eager.CreateContextRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.ServerDef server_def = 1;
  if (this->has_server_def()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->_internal_server_def(), output);
  }

  // bool async = 2;
  if (this->async() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(2, this->async(), output);
  }

  // int64 keep_alive_secs = 3;
  if (this->keep_alive_secs() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->keep_alive_secs(), output);
  }

  // .tensorflow.VersionDef version_def = 4;
  if (this->has_version_def()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->_internal_version_def(), output);
  }

  // int64 rendezvous_id = 5;
  if (this->rendezvous_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(5, this->rendezvous_id(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.eager.CreateContextRequest)
}

::google::protobuf::uint8* CreateContextRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.eager.CreateContextRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.ServerDef server_def = 1;
  if (this->has_server_def()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->_internal_server_def(), deterministic, target);
  }

  // bool async = 2;
  if (this->async() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(2, this->async(), target);
  }

  // int64 keep_alive_secs = 3;
  if (this->keep_alive_secs() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->keep_alive_secs(), target);
  }

  // .tensorflow.VersionDef version_def = 4;
  if (this->has_version_def()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->_internal_version_def(), deterministic, target);
  }

  // int64 rendezvous_id = 5;
  if (this->rendezvous_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(5, this->rendezvous_id(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.eager.CreateContextRequest)
  return target;
}

size_t CreateContextRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.eager.CreateContextRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .tensorflow.ServerDef server_def = 1;
  if (this->has_server_def()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *server_def_);
  }

  // .tensorflow.VersionDef version_def = 4;
  if (this->has_version_def()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *version_def_);
  }

  // int64 keep_alive_secs = 3;
  if (this->keep_alive_secs() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->keep_alive_secs());
  }

  // int64 rendezvous_id = 5;
  if (this->rendezvous_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->rendezvous_id());
  }

  // bool async = 2;
  if (this->async() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CreateContextRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.eager.CreateContextRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const CreateContextRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CreateContextRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.eager.CreateContextRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.eager.CreateContextRequest)
    MergeFrom(*source);
  }
}

void CreateContextRequest::MergeFrom(const CreateContextRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.eager.CreateContextRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_server_def()) {
    mutable_server_def()->::tensorflow::ServerDef::MergeFrom(from.server_def());
  }
  if (from.has_version_def()) {
    mutable_version_def()->::tensorflow::VersionDef::MergeFrom(from.version_def());
  }
  if (from.keep_alive_secs() != 0) {
    set_keep_alive_secs(from.keep_alive_secs());
  }
  if (from.rendezvous_id() != 0) {
    set_rendezvous_id(from.rendezvous_id());
  }
  if (from.async() != 0) {
    set_async(from.async());
  }
}

void CreateContextRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.eager.CreateContextRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CreateContextRequest::CopyFrom(const CreateContextRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.eager.CreateContextRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CreateContextRequest::IsInitialized() const {
  return true;
}

void CreateContextRequest::Swap(CreateContextRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void CreateContextRequest::InternalSwap(CreateContextRequest* other) {
  using std::swap;
  swap(server_def_, other->server_def_);
  swap(version_def_, other->version_def_);
  swap(keep_alive_secs_, other->keep_alive_secs_);
  swap(rendezvous_id_, other->rendezvous_id_);
  swap(async_, other->async_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata CreateContextRequest::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void CreateContextResponse::InitAsDefaultInstance() {
}
void CreateContextResponse::clear_device_attributes() {
  device_attributes_.Clear();
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CreateContextResponse::kContextIdFieldNumber;
const int CreateContextResponse::kDeviceAttributesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CreateContextResponse::CreateContextResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_CreateContextResponse.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.eager.CreateContextResponse)
}
CreateContextResponse::CreateContextResponse(const CreateContextResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      device_attributes_(from.device_attributes_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  context_id_ = from.context_id_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.eager.CreateContextResponse)
}

void CreateContextResponse::SharedCtor() {
  context_id_ = GOOGLE_ULONGLONG(0);
}

CreateContextResponse::~CreateContextResponse() {
  // @@protoc_insertion_point(destructor:tensorflow.eager.CreateContextResponse)
  SharedDtor();
}

void CreateContextResponse::SharedDtor() {
}

void CreateContextResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* CreateContextResponse::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CreateContextResponse& CreateContextResponse::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_CreateContextResponse.base);
  return *internal_default_instance();
}


void CreateContextResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.eager.CreateContextResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  device_attributes_.Clear();
  context_id_ = GOOGLE_ULONGLONG(0);
  _internal_metadata_.Clear();
}

bool CreateContextResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.eager.CreateContextResponse)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // fixed64 context_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(9u /* 9 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 input, &context_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.DeviceAttributes device_attributes = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_device_attributes()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.eager.CreateContextResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.eager.CreateContextResponse)
  return false;
#undef DO_
}

void CreateContextResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.eager.CreateContextResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // fixed64 context_id = 1;
  if (this->context_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed64(1, this->context_id(), output);
  }

  // repeated .tensorflow.DeviceAttributes device_attributes = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->device_attributes_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2,
      this->device_attributes(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.eager.CreateContextResponse)
}

::google::protobuf::uint8* CreateContextResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.eager.CreateContextResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // fixed64 context_id = 1;
  if (this->context_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFixed64ToArray(1, this->context_id(), target);
  }

  // repeated .tensorflow.DeviceAttributes device_attributes = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->device_attributes_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->device_attributes(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.eager.CreateContextResponse)
  return target;
}

size_t CreateContextResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.eager.CreateContextResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.DeviceAttributes device_attributes = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->device_attributes_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->device_attributes(static_cast<int>(i)));
    }
  }

  // fixed64 context_id = 1;
  if (this->context_id() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CreateContextResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.eager.CreateContextResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const CreateContextResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CreateContextResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.eager.CreateContextResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.eager.CreateContextResponse)
    MergeFrom(*source);
  }
}

void CreateContextResponse::MergeFrom(const CreateContextResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.eager.CreateContextResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  device_attributes_.MergeFrom(from.device_attributes_);
  if (from.context_id() != 0) {
    set_context_id(from.context_id());
  }
}

void CreateContextResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.eager.CreateContextResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CreateContextResponse::CopyFrom(const CreateContextResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.eager.CreateContextResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CreateContextResponse::IsInitialized() const {
  return true;
}

void CreateContextResponse::Swap(CreateContextResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void CreateContextResponse::InternalSwap(CreateContextResponse* other) {
  using std::swap;
  CastToBase(&device_attributes_)->InternalSwap(CastToBase(&other->device_attributes_));
  swap(context_id_, other->context_id_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata CreateContextResponse::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void EnqueueRequest::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int EnqueueRequest::kContextIdFieldNumber;
const int EnqueueRequest::kQueueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

EnqueueRequest::EnqueueRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_EnqueueRequest.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.eager.EnqueueRequest)
}
EnqueueRequest::EnqueueRequest(const EnqueueRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      queue_(from.queue_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  context_id_ = from.context_id_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.eager.EnqueueRequest)
}

void EnqueueRequest::SharedCtor() {
  context_id_ = GOOGLE_ULONGLONG(0);
}

EnqueueRequest::~EnqueueRequest() {
  // @@protoc_insertion_point(destructor:tensorflow.eager.EnqueueRequest)
  SharedDtor();
}

void EnqueueRequest::SharedDtor() {
}

void EnqueueRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* EnqueueRequest::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const EnqueueRequest& EnqueueRequest::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_EnqueueRequest.base);
  return *internal_default_instance();
}


void EnqueueRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.eager.EnqueueRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  queue_.Clear();
  context_id_ = GOOGLE_ULONGLONG(0);
  _internal_metadata_.Clear();
}

bool EnqueueRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.eager.EnqueueRequest)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // fixed64 context_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(9u /* 9 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 input, &context_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.eager.QueueItem queue = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_queue()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.eager.EnqueueRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.eager.EnqueueRequest)
  return false;
#undef DO_
}

void EnqueueRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.eager.EnqueueRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // fixed64 context_id = 1;
  if (this->context_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed64(1, this->context_id(), output);
  }

  // repeated .tensorflow.eager.QueueItem queue = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->queue_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3,
      this->queue(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.eager.EnqueueRequest)
}

::google::protobuf::uint8* EnqueueRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.eager.EnqueueRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // fixed64 context_id = 1;
  if (this->context_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFixed64ToArray(1, this->context_id(), target);
  }

  // repeated .tensorflow.eager.QueueItem queue = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->queue_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->queue(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.eager.EnqueueRequest)
  return target;
}

size_t EnqueueRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.eager.EnqueueRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.eager.QueueItem queue = 3;
  {
    unsigned int count = static_cast<unsigned int>(this->queue_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->queue(static_cast<int>(i)));
    }
  }

  // fixed64 context_id = 1;
  if (this->context_id() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void EnqueueRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.eager.EnqueueRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const EnqueueRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const EnqueueRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.eager.EnqueueRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.eager.EnqueueRequest)
    MergeFrom(*source);
  }
}

void EnqueueRequest::MergeFrom(const EnqueueRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.eager.EnqueueRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  queue_.MergeFrom(from.queue_);
  if (from.context_id() != 0) {
    set_context_id(from.context_id());
  }
}

void EnqueueRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.eager.EnqueueRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void EnqueueRequest::CopyFrom(const EnqueueRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.eager.EnqueueRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EnqueueRequest::IsInitialized() const {
  return true;
}

void EnqueueRequest::Swap(EnqueueRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void EnqueueRequest::InternalSwap(EnqueueRequest* other) {
  using std::swap;
  CastToBase(&queue_)->InternalSwap(CastToBase(&other->queue_));
  swap(context_id_, other->context_id_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata EnqueueRequest::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void EnqueueResponse::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int EnqueueResponse::kQueueResponseFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

EnqueueResponse::EnqueueResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_EnqueueResponse.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.eager.EnqueueResponse)
}
EnqueueResponse::EnqueueResponse(const EnqueueResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      queue_response_(from.queue_response_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.eager.EnqueueResponse)
}

void EnqueueResponse::SharedCtor() {
}

EnqueueResponse::~EnqueueResponse() {
  // @@protoc_insertion_point(destructor:tensorflow.eager.EnqueueResponse)
  SharedDtor();
}

void EnqueueResponse::SharedDtor() {
}

void EnqueueResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* EnqueueResponse::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const EnqueueResponse& EnqueueResponse::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_EnqueueResponse.base);
  return *internal_default_instance();
}


void EnqueueResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.eager.EnqueueResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  queue_response_.Clear();
  _internal_metadata_.Clear();
}

bool EnqueueResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.eager.EnqueueResponse)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.eager.QueueResponse queue_response = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_queue_response()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.eager.EnqueueResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.eager.EnqueueResponse)
  return false;
#undef DO_
}

void EnqueueResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.eager.EnqueueResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.eager.QueueResponse queue_response = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->queue_response_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->queue_response(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.eager.EnqueueResponse)
}

::google::protobuf::uint8* EnqueueResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.eager.EnqueueResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.eager.QueueResponse queue_response = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->queue_response_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->queue_response(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.eager.EnqueueResponse)
  return target;
}

size_t EnqueueResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.eager.EnqueueResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.eager.QueueResponse queue_response = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->queue_response_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->queue_response(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void EnqueueResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.eager.EnqueueResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const EnqueueResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const EnqueueResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.eager.EnqueueResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.eager.EnqueueResponse)
    MergeFrom(*source);
  }
}

void EnqueueResponse::MergeFrom(const EnqueueResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.eager.EnqueueResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  queue_response_.MergeFrom(from.queue_response_);
}

void EnqueueResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.eager.EnqueueResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void EnqueueResponse::CopyFrom(const EnqueueResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.eager.EnqueueResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EnqueueResponse::IsInitialized() const {
  return true;
}

void EnqueueResponse::Swap(EnqueueResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void EnqueueResponse::InternalSwap(EnqueueResponse* other) {
  using std::swap;
  CastToBase(&queue_response_)->InternalSwap(CastToBase(&other->queue_response_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata EnqueueResponse::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void WaitQueueDoneRequest::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int WaitQueueDoneRequest::kContextIdFieldNumber;
const int WaitQueueDoneRequest::kOpIdFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

WaitQueueDoneRequest::WaitQueueDoneRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_WaitQueueDoneRequest.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.eager.WaitQueueDoneRequest)
}
WaitQueueDoneRequest::WaitQueueDoneRequest(const WaitQueueDoneRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      op_id_(from.op_id_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  context_id_ = from.context_id_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.eager.WaitQueueDoneRequest)
}

void WaitQueueDoneRequest::SharedCtor() {
  context_id_ = GOOGLE_ULONGLONG(0);
}

WaitQueueDoneRequest::~WaitQueueDoneRequest() {
  // @@protoc_insertion_point(destructor:tensorflow.eager.WaitQueueDoneRequest)
  SharedDtor();
}

void WaitQueueDoneRequest::SharedDtor() {
}

void WaitQueueDoneRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* WaitQueueDoneRequest::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const WaitQueueDoneRequest& WaitQueueDoneRequest::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_WaitQueueDoneRequest.base);
  return *internal_default_instance();
}


void WaitQueueDoneRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.eager.WaitQueueDoneRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  op_id_.Clear();
  context_id_ = GOOGLE_ULONGLONG(0);
  _internal_metadata_.Clear();
}

bool WaitQueueDoneRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.eager.WaitQueueDoneRequest)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // fixed64 context_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(9u /* 9 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 input, &context_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated int64 op_id = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_op_id())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 1, 18u, input, this->mutable_op_id())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.eager.WaitQueueDoneRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.eager.WaitQueueDoneRequest)
  return false;
#undef DO_
}

void WaitQueueDoneRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.eager.WaitQueueDoneRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // fixed64 context_id = 1;
  if (this->context_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed64(1, this->context_id(), output);
  }

  // repeated int64 op_id = 2;
  if (this->op_id_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(2, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _op_id_cached_byte_size_));
  }
  for (int i = 0, n = this->op_id_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->op_id(i), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.eager.WaitQueueDoneRequest)
}

::google::protobuf::uint8* WaitQueueDoneRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.eager.WaitQueueDoneRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // fixed64 context_id = 1;
  if (this->context_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFixed64ToArray(1, this->context_id(), target);
  }

  // repeated int64 op_id = 2;
  if (this->op_id_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      2,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _op_id_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->op_id_, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.eager.WaitQueueDoneRequest)
  return target;
}

size_t WaitQueueDoneRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.eager.WaitQueueDoneRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated int64 op_id = 2;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int64Size(this->op_id_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _op_id_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // fixed64 context_id = 1;
  if (this->context_id() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void WaitQueueDoneRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.eager.WaitQueueDoneRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const WaitQueueDoneRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const WaitQueueDoneRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.eager.WaitQueueDoneRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.eager.WaitQueueDoneRequest)
    MergeFrom(*source);
  }
}

void WaitQueueDoneRequest::MergeFrom(const WaitQueueDoneRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.eager.WaitQueueDoneRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  op_id_.MergeFrom(from.op_id_);
  if (from.context_id() != 0) {
    set_context_id(from.context_id());
  }
}

void WaitQueueDoneRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.eager.WaitQueueDoneRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void WaitQueueDoneRequest::CopyFrom(const WaitQueueDoneRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.eager.WaitQueueDoneRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool WaitQueueDoneRequest::IsInitialized() const {
  return true;
}

void WaitQueueDoneRequest::Swap(WaitQueueDoneRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void WaitQueueDoneRequest::InternalSwap(WaitQueueDoneRequest* other) {
  using std::swap;
  op_id_.InternalSwap(&other->op_id_);
  swap(context_id_, other->context_id_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata WaitQueueDoneRequest::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void WaitQueueDoneResponse::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

WaitQueueDoneResponse::WaitQueueDoneResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_WaitQueueDoneResponse.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.eager.WaitQueueDoneResponse)
}
WaitQueueDoneResponse::WaitQueueDoneResponse(const WaitQueueDoneResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.eager.WaitQueueDoneResponse)
}

void WaitQueueDoneResponse::SharedCtor() {
}

WaitQueueDoneResponse::~WaitQueueDoneResponse() {
  // @@protoc_insertion_point(destructor:tensorflow.eager.WaitQueueDoneResponse)
  SharedDtor();
}

void WaitQueueDoneResponse::SharedDtor() {
}

void WaitQueueDoneResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* WaitQueueDoneResponse::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const WaitQueueDoneResponse& WaitQueueDoneResponse::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_WaitQueueDoneResponse.base);
  return *internal_default_instance();
}


void WaitQueueDoneResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.eager.WaitQueueDoneResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_metadata_.Clear();
}

bool WaitQueueDoneResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.eager.WaitQueueDoneResponse)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, _internal_metadata_.mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.eager.WaitQueueDoneResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.eager.WaitQueueDoneResponse)
  return false;
#undef DO_
}

void WaitQueueDoneResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.eager.WaitQueueDoneResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.eager.WaitQueueDoneResponse)
}

::google::protobuf::uint8* WaitQueueDoneResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.eager.WaitQueueDoneResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.eager.WaitQueueDoneResponse)
  return target;
}

size_t WaitQueueDoneResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.eager.WaitQueueDoneResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void WaitQueueDoneResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.eager.WaitQueueDoneResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const WaitQueueDoneResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const WaitQueueDoneResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.eager.WaitQueueDoneResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.eager.WaitQueueDoneResponse)
    MergeFrom(*source);
  }
}

void WaitQueueDoneResponse::MergeFrom(const WaitQueueDoneResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.eager.WaitQueueDoneResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

}

void WaitQueueDoneResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.eager.WaitQueueDoneResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void WaitQueueDoneResponse::CopyFrom(const WaitQueueDoneResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.eager.WaitQueueDoneResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool WaitQueueDoneResponse::IsInitialized() const {
  return true;
}

void WaitQueueDoneResponse::Swap(WaitQueueDoneResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void WaitQueueDoneResponse::InternalSwap(WaitQueueDoneResponse* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata WaitQueueDoneResponse::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void KeepAliveRequest::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int KeepAliveRequest::kContextIdFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

KeepAliveRequest::KeepAliveRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_KeepAliveRequest.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.eager.KeepAliveRequest)
}
KeepAliveRequest::KeepAliveRequest(const KeepAliveRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  context_id_ = from.context_id_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.eager.KeepAliveRequest)
}

void KeepAliveRequest::SharedCtor() {
  context_id_ = GOOGLE_ULONGLONG(0);
}

KeepAliveRequest::~KeepAliveRequest() {
  // @@protoc_insertion_point(destructor:tensorflow.eager.KeepAliveRequest)
  SharedDtor();
}

void KeepAliveRequest::SharedDtor() {
}

void KeepAliveRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* KeepAliveRequest::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const KeepAliveRequest& KeepAliveRequest::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_KeepAliveRequest.base);
  return *internal_default_instance();
}


void KeepAliveRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.eager.KeepAliveRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  context_id_ = GOOGLE_ULONGLONG(0);
  _internal_metadata_.Clear();
}

bool KeepAliveRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.eager.KeepAliveRequest)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // fixed64 context_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(9u /* 9 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 input, &context_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.eager.KeepAliveRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.eager.KeepAliveRequest)
  return false;
#undef DO_
}

void KeepAliveRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.eager.KeepAliveRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // fixed64 context_id = 1;
  if (this->context_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed64(1, this->context_id(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.eager.KeepAliveRequest)
}

::google::protobuf::uint8* KeepAliveRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.eager.KeepAliveRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // fixed64 context_id = 1;
  if (this->context_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFixed64ToArray(1, this->context_id(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.eager.KeepAliveRequest)
  return target;
}

size_t KeepAliveRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.eager.KeepAliveRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // fixed64 context_id = 1;
  if (this->context_id() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void KeepAliveRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.eager.KeepAliveRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const KeepAliveRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const KeepAliveRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.eager.KeepAliveRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.eager.KeepAliveRequest)
    MergeFrom(*source);
  }
}

void KeepAliveRequest::MergeFrom(const KeepAliveRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.eager.KeepAliveRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.context_id() != 0) {
    set_context_id(from.context_id());
  }
}

void KeepAliveRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.eager.KeepAliveRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void KeepAliveRequest::CopyFrom(const KeepAliveRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.eager.KeepAliveRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool KeepAliveRequest::IsInitialized() const {
  return true;
}

void KeepAliveRequest::Swap(KeepAliveRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void KeepAliveRequest::InternalSwap(KeepAliveRequest* other) {
  using std::swap;
  swap(context_id_, other->context_id_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata KeepAliveRequest::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void KeepAliveResponse::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

KeepAliveResponse::KeepAliveResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_KeepAliveResponse.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.eager.KeepAliveResponse)
}
KeepAliveResponse::KeepAliveResponse(const KeepAliveResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.eager.KeepAliveResponse)
}

void KeepAliveResponse::SharedCtor() {
}

KeepAliveResponse::~KeepAliveResponse() {
  // @@protoc_insertion_point(destructor:tensorflow.eager.KeepAliveResponse)
  SharedDtor();
}

void KeepAliveResponse::SharedDtor() {
}

void KeepAliveResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* KeepAliveResponse::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const KeepAliveResponse& KeepAliveResponse::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_KeepAliveResponse.base);
  return *internal_default_instance();
}


void KeepAliveResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.eager.KeepAliveResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_metadata_.Clear();
}

bool KeepAliveResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.eager.KeepAliveResponse)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, _internal_metadata_.mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.eager.KeepAliveResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.eager.KeepAliveResponse)
  return false;
#undef DO_
}

void KeepAliveResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.eager.KeepAliveResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.eager.KeepAliveResponse)
}

::google::protobuf::uint8* KeepAliveResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.eager.KeepAliveResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.eager.KeepAliveResponse)
  return target;
}

size_t KeepAliveResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.eager.KeepAliveResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void KeepAliveResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.eager.KeepAliveResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const KeepAliveResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const KeepAliveResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.eager.KeepAliveResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.eager.KeepAliveResponse)
    MergeFrom(*source);
  }
}

void KeepAliveResponse::MergeFrom(const KeepAliveResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.eager.KeepAliveResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

}

void KeepAliveResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.eager.KeepAliveResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void KeepAliveResponse::CopyFrom(const KeepAliveResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.eager.KeepAliveResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool KeepAliveResponse::IsInitialized() const {
  return true;
}

void KeepAliveResponse::Swap(KeepAliveResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void KeepAliveResponse::InternalSwap(KeepAliveResponse* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata KeepAliveResponse::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void CloseContextRequest::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CloseContextRequest::kContextIdFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CloseContextRequest::CloseContextRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_CloseContextRequest.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.eager.CloseContextRequest)
}
CloseContextRequest::CloseContextRequest(const CloseContextRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  context_id_ = from.context_id_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.eager.CloseContextRequest)
}

void CloseContextRequest::SharedCtor() {
  context_id_ = GOOGLE_ULONGLONG(0);
}

CloseContextRequest::~CloseContextRequest() {
  // @@protoc_insertion_point(destructor:tensorflow.eager.CloseContextRequest)
  SharedDtor();
}

void CloseContextRequest::SharedDtor() {
}

void CloseContextRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* CloseContextRequest::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CloseContextRequest& CloseContextRequest::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_CloseContextRequest.base);
  return *internal_default_instance();
}


void CloseContextRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.eager.CloseContextRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  context_id_ = GOOGLE_ULONGLONG(0);
  _internal_metadata_.Clear();
}

bool CloseContextRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.eager.CloseContextRequest)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // fixed64 context_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(9u /* 9 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 input, &context_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.eager.CloseContextRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.eager.CloseContextRequest)
  return false;
#undef DO_
}

void CloseContextRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.eager.CloseContextRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // fixed64 context_id = 1;
  if (this->context_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed64(1, this->context_id(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.eager.CloseContextRequest)
}

::google::protobuf::uint8* CloseContextRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.eager.CloseContextRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // fixed64 context_id = 1;
  if (this->context_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFixed64ToArray(1, this->context_id(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.eager.CloseContextRequest)
  return target;
}

size_t CloseContextRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.eager.CloseContextRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // fixed64 context_id = 1;
  if (this->context_id() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CloseContextRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.eager.CloseContextRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const CloseContextRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CloseContextRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.eager.CloseContextRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.eager.CloseContextRequest)
    MergeFrom(*source);
  }
}

void CloseContextRequest::MergeFrom(const CloseContextRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.eager.CloseContextRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.context_id() != 0) {
    set_context_id(from.context_id());
  }
}

void CloseContextRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.eager.CloseContextRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CloseContextRequest::CopyFrom(const CloseContextRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.eager.CloseContextRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CloseContextRequest::IsInitialized() const {
  return true;
}

void CloseContextRequest::Swap(CloseContextRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void CloseContextRequest::InternalSwap(CloseContextRequest* other) {
  using std::swap;
  swap(context_id_, other->context_id_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata CloseContextRequest::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void CloseContextResponse::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CloseContextResponse::CloseContextResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_CloseContextResponse.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.eager.CloseContextResponse)
}
CloseContextResponse::CloseContextResponse(const CloseContextResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.eager.CloseContextResponse)
}

void CloseContextResponse::SharedCtor() {
}

CloseContextResponse::~CloseContextResponse() {
  // @@protoc_insertion_point(destructor:tensorflow.eager.CloseContextResponse)
  SharedDtor();
}

void CloseContextResponse::SharedDtor() {
}

void CloseContextResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* CloseContextResponse::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CloseContextResponse& CloseContextResponse::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_CloseContextResponse.base);
  return *internal_default_instance();
}


void CloseContextResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.eager.CloseContextResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_metadata_.Clear();
}

bool CloseContextResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.eager.CloseContextResponse)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, _internal_metadata_.mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.eager.CloseContextResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.eager.CloseContextResponse)
  return false;
#undef DO_
}

void CloseContextResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.eager.CloseContextResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.eager.CloseContextResponse)
}

::google::protobuf::uint8* CloseContextResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.eager.CloseContextResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.eager.CloseContextResponse)
  return target;
}

size_t CloseContextResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.eager.CloseContextResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CloseContextResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.eager.CloseContextResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const CloseContextResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CloseContextResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.eager.CloseContextResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.eager.CloseContextResponse)
    MergeFrom(*source);
  }
}

void CloseContextResponse::MergeFrom(const CloseContextResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.eager.CloseContextResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

}

void CloseContextResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.eager.CloseContextResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CloseContextResponse::CopyFrom(const CloseContextResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.eager.CloseContextResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CloseContextResponse::IsInitialized() const {
  return true;
}

void CloseContextResponse::Swap(CloseContextResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void CloseContextResponse::InternalSwap(CloseContextResponse* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata CloseContextResponse::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void RegisterFunctionRequest::InitAsDefaultInstance() {
  ::tensorflow::eager::_RegisterFunctionRequest_default_instance_._instance.get_mutable()->function_def_ = const_cast< ::tensorflow::FunctionDef*>(
      ::tensorflow::FunctionDef::internal_default_instance());
}
void RegisterFunctionRequest::clear_function_def() {
  if (GetArenaNoVirtual() == NULL && function_def_ != NULL) {
    delete function_def_;
  }
  function_def_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RegisterFunctionRequest::kContextIdFieldNumber;
const int RegisterFunctionRequest::kFunctionDefFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RegisterFunctionRequest::RegisterFunctionRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_RegisterFunctionRequest.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.eager.RegisterFunctionRequest)
}
RegisterFunctionRequest::RegisterFunctionRequest(const RegisterFunctionRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_function_def()) {
    function_def_ = new ::tensorflow::FunctionDef(*from.function_def_);
  } else {
    function_def_ = NULL;
  }
  context_id_ = from.context_id_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.eager.RegisterFunctionRequest)
}

void RegisterFunctionRequest::SharedCtor() {
  ::memset(&function_def_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&context_id_) -
      reinterpret_cast<char*>(&function_def_)) + sizeof(context_id_));
}

RegisterFunctionRequest::~RegisterFunctionRequest() {
  // @@protoc_insertion_point(destructor:tensorflow.eager.RegisterFunctionRequest)
  SharedDtor();
}

void RegisterFunctionRequest::SharedDtor() {
  if (this != internal_default_instance()) delete function_def_;
}

void RegisterFunctionRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* RegisterFunctionRequest::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const RegisterFunctionRequest& RegisterFunctionRequest::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_RegisterFunctionRequest.base);
  return *internal_default_instance();
}


void RegisterFunctionRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.eager.RegisterFunctionRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && function_def_ != NULL) {
    delete function_def_;
  }
  function_def_ = NULL;
  context_id_ = GOOGLE_ULONGLONG(0);
  _internal_metadata_.Clear();
}

bool RegisterFunctionRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.eager.RegisterFunctionRequest)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // fixed64 context_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(9u /* 9 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 input, &context_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.FunctionDef function_def = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_function_def()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.eager.RegisterFunctionRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.eager.RegisterFunctionRequest)
  return false;
#undef DO_
}

void RegisterFunctionRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.eager.RegisterFunctionRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // fixed64 context_id = 1;
  if (this->context_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed64(1, this->context_id(), output);
  }

  // .tensorflow.FunctionDef function_def = 2;
  if (this->has_function_def()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_function_def(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.eager.RegisterFunctionRequest)
}

::google::protobuf::uint8* RegisterFunctionRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.eager.RegisterFunctionRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // fixed64 context_id = 1;
  if (this->context_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFixed64ToArray(1, this->context_id(), target);
  }

  // .tensorflow.FunctionDef function_def = 2;
  if (this->has_function_def()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_function_def(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.eager.RegisterFunctionRequest)
  return target;
}

size_t RegisterFunctionRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.eager.RegisterFunctionRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .tensorflow.FunctionDef function_def = 2;
  if (this->has_function_def()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *function_def_);
  }

  // fixed64 context_id = 1;
  if (this->context_id() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RegisterFunctionRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.eager.RegisterFunctionRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const RegisterFunctionRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RegisterFunctionRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.eager.RegisterFunctionRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.eager.RegisterFunctionRequest)
    MergeFrom(*source);
  }
}

void RegisterFunctionRequest::MergeFrom(const RegisterFunctionRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.eager.RegisterFunctionRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_function_def()) {
    mutable_function_def()->::tensorflow::FunctionDef::MergeFrom(from.function_def());
  }
  if (from.context_id() != 0) {
    set_context_id(from.context_id());
  }
}

void RegisterFunctionRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.eager.RegisterFunctionRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RegisterFunctionRequest::CopyFrom(const RegisterFunctionRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.eager.RegisterFunctionRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RegisterFunctionRequest::IsInitialized() const {
  return true;
}

void RegisterFunctionRequest::Swap(RegisterFunctionRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void RegisterFunctionRequest::InternalSwap(RegisterFunctionRequest* other) {
  using std::swap;
  swap(function_def_, other->function_def_);
  swap(context_id_, other->context_id_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata RegisterFunctionRequest::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void RegisterFunctionResponse::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RegisterFunctionResponse::RegisterFunctionResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_RegisterFunctionResponse.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.eager.RegisterFunctionResponse)
}
RegisterFunctionResponse::RegisterFunctionResponse(const RegisterFunctionResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.eager.RegisterFunctionResponse)
}

void RegisterFunctionResponse::SharedCtor() {
}

RegisterFunctionResponse::~RegisterFunctionResponse() {
  // @@protoc_insertion_point(destructor:tensorflow.eager.RegisterFunctionResponse)
  SharedDtor();
}

void RegisterFunctionResponse::SharedDtor() {
}

void RegisterFunctionResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* RegisterFunctionResponse::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const RegisterFunctionResponse& RegisterFunctionResponse::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_RegisterFunctionResponse.base);
  return *internal_default_instance();
}


void RegisterFunctionResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.eager.RegisterFunctionResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_metadata_.Clear();
}

bool RegisterFunctionResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.eager.RegisterFunctionResponse)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, _internal_metadata_.mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.eager.RegisterFunctionResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.eager.RegisterFunctionResponse)
  return false;
#undef DO_
}

void RegisterFunctionResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.eager.RegisterFunctionResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.eager.RegisterFunctionResponse)
}

::google::protobuf::uint8* RegisterFunctionResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.eager.RegisterFunctionResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.eager.RegisterFunctionResponse)
  return target;
}

size_t RegisterFunctionResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.eager.RegisterFunctionResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RegisterFunctionResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.eager.RegisterFunctionResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const RegisterFunctionResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RegisterFunctionResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.eager.RegisterFunctionResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.eager.RegisterFunctionResponse)
    MergeFrom(*source);
  }
}

void RegisterFunctionResponse::MergeFrom(const RegisterFunctionResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.eager.RegisterFunctionResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

}

void RegisterFunctionResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.eager.RegisterFunctionResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RegisterFunctionResponse::CopyFrom(const RegisterFunctionResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.eager.RegisterFunctionResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RegisterFunctionResponse::IsInitialized() const {
  return true;
}

void RegisterFunctionResponse::Swap(RegisterFunctionResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void RegisterFunctionResponse::InternalSwap(RegisterFunctionResponse* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata RegisterFunctionResponse::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void SendTensorRequest::InitAsDefaultInstance() {
}
void SendTensorRequest::clear_tensors() {
  tensors_.Clear();
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SendTensorRequest::kContextIdFieldNumber;
const int SendTensorRequest::kOpIdFieldNumber;
const int SendTensorRequest::kTensorsFieldNumber;
const int SendTensorRequest::kDeviceNameFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SendTensorRequest::SendTensorRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_SendTensorRequest.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.eager.SendTensorRequest)
}
SendTensorRequest::SendTensorRequest(const SendTensorRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      tensors_(from.tensors_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  device_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.device_name().size() > 0) {
    device_name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.device_name_);
  }
  ::memcpy(&context_id_, &from.context_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&op_id_) -
    reinterpret_cast<char*>(&context_id_)) + sizeof(op_id_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.eager.SendTensorRequest)
}

void SendTensorRequest::SharedCtor() {
  device_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&context_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&op_id_) -
      reinterpret_cast<char*>(&context_id_)) + sizeof(op_id_));
}

SendTensorRequest::~SendTensorRequest() {
  // @@protoc_insertion_point(destructor:tensorflow.eager.SendTensorRequest)
  SharedDtor();
}

void SendTensorRequest::SharedDtor() {
  device_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void SendTensorRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* SendTensorRequest::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const SendTensorRequest& SendTensorRequest::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_SendTensorRequest.base);
  return *internal_default_instance();
}


void SendTensorRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.eager.SendTensorRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  tensors_.Clear();
  device_name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&context_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&op_id_) -
      reinterpret_cast<char*>(&context_id_)) + sizeof(op_id_));
  _internal_metadata_.Clear();
}

bool SendTensorRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.eager.SendTensorRequest)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // fixed64 context_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(9u /* 9 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED64>(
                 input, &context_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 op_id = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &op_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.TensorProto tensors = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_tensors()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string device_name = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_device_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->device_name().data(), static_cast<int>(this->device_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.eager.SendTensorRequest.device_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.eager.SendTensorRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.eager.SendTensorRequest)
  return false;
#undef DO_
}

void SendTensorRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.eager.SendTensorRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // fixed64 context_id = 1;
  if (this->context_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed64(1, this->context_id(), output);
  }

  // int64 op_id = 2;
  if (this->op_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->op_id(), output);
  }

  // repeated .tensorflow.TensorProto tensors = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->tensors_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3,
      this->tensors(static_cast<int>(i)),
      output);
  }

  // string device_name = 4;
  if (this->device_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->device_name().data(), static_cast<int>(this->device_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.eager.SendTensorRequest.device_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->device_name(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.eager.SendTensorRequest)
}

::google::protobuf::uint8* SendTensorRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.eager.SendTensorRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // fixed64 context_id = 1;
  if (this->context_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFixed64ToArray(1, this->context_id(), target);
  }

  // int64 op_id = 2;
  if (this->op_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->op_id(), target);
  }

  // repeated .tensorflow.TensorProto tensors = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->tensors_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->tensors(static_cast<int>(i)), deterministic, target);
  }

  // string device_name = 4;
  if (this->device_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->device_name().data(), static_cast<int>(this->device_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.eager.SendTensorRequest.device_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->device_name(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.eager.SendTensorRequest)
  return target;
}

size_t SendTensorRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.eager.SendTensorRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.TensorProto tensors = 3;
  {
    unsigned int count = static_cast<unsigned int>(this->tensors_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->tensors(static_cast<int>(i)));
    }
  }

  // string device_name = 4;
  if (this->device_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->device_name());
  }

  // fixed64 context_id = 1;
  if (this->context_id() != 0) {
    total_size += 1 + 8;
  }

  // int64 op_id = 2;
  if (this->op_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->op_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SendTensorRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.eager.SendTensorRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const SendTensorRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SendTensorRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.eager.SendTensorRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.eager.SendTensorRequest)
    MergeFrom(*source);
  }
}

void SendTensorRequest::MergeFrom(const SendTensorRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.eager.SendTensorRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  tensors_.MergeFrom(from.tensors_);
  if (from.device_name().size() > 0) {

    device_name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.device_name_);
  }
  if (from.context_id() != 0) {
    set_context_id(from.context_id());
  }
  if (from.op_id() != 0) {
    set_op_id(from.op_id());
  }
}

void SendTensorRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.eager.SendTensorRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SendTensorRequest::CopyFrom(const SendTensorRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.eager.SendTensorRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SendTensorRequest::IsInitialized() const {
  return true;
}

void SendTensorRequest::Swap(SendTensorRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void SendTensorRequest::InternalSwap(SendTensorRequest* other) {
  using std::swap;
  CastToBase(&tensors_)->InternalSwap(CastToBase(&other->tensors_));
  device_name_.Swap(&other->device_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(context_id_, other->context_id_);
  swap(op_id_, other->op_id_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata SendTensorRequest::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void SendTensorResponse::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SendTensorResponse::SendTensorResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_SendTensorResponse.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.eager.SendTensorResponse)
}
SendTensorResponse::SendTensorResponse(const SendTensorResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.eager.SendTensorResponse)
}

void SendTensorResponse::SharedCtor() {
}

SendTensorResponse::~SendTensorResponse() {
  // @@protoc_insertion_point(destructor:tensorflow.eager.SendTensorResponse)
  SharedDtor();
}

void SendTensorResponse::SharedDtor() {
}

void SendTensorResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* SendTensorResponse::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const SendTensorResponse& SendTensorResponse::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::scc_info_SendTensorResponse.base);
  return *internal_default_instance();
}


void SendTensorResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.eager.SendTensorResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_metadata_.Clear();
}

bool SendTensorResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.eager.SendTensorResponse)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, _internal_metadata_.mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.eager.SendTensorResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.eager.SendTensorResponse)
  return false;
#undef DO_
}

void SendTensorResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.eager.SendTensorResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.eager.SendTensorResponse)
}

::google::protobuf::uint8* SendTensorResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.eager.SendTensorResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.eager.SendTensorResponse)
  return target;
}

size_t SendTensorResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.eager.SendTensorResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SendTensorResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.eager.SendTensorResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const SendTensorResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SendTensorResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.eager.SendTensorResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.eager.SendTensorResponse)
    MergeFrom(*source);
  }
}

void SendTensorResponse::MergeFrom(const SendTensorResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.eager.SendTensorResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

}

void SendTensorResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.eager.SendTensorResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SendTensorResponse::CopyFrom(const SendTensorResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.eager.SendTensorResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SendTensorResponse::IsInitialized() const {
  return true;
}

void SendTensorResponse::Swap(SendTensorResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void SendTensorResponse::InternalSwap(SendTensorResponse* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata SendTensorResponse::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace eager
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::eager::RemoteTensorHandle* Arena::CreateMaybeMessage< ::tensorflow::eager::RemoteTensorHandle >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::eager::RemoteTensorHandle >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::eager::Operation_AttrsEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::eager::Operation_AttrsEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::eager::Operation_AttrsEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::eager::Operation* Arena::CreateMaybeMessage< ::tensorflow::eager::Operation >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::eager::Operation >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::eager::QueueItem* Arena::CreateMaybeMessage< ::tensorflow::eager::QueueItem >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::eager::QueueItem >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::eager::QueueResponse* Arena::CreateMaybeMessage< ::tensorflow::eager::QueueResponse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::eager::QueueResponse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::eager::CreateContextRequest* Arena::CreateMaybeMessage< ::tensorflow::eager::CreateContextRequest >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::eager::CreateContextRequest >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::eager::CreateContextResponse* Arena::CreateMaybeMessage< ::tensorflow::eager::CreateContextResponse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::eager::CreateContextResponse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::eager::EnqueueRequest* Arena::CreateMaybeMessage< ::tensorflow::eager::EnqueueRequest >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::eager::EnqueueRequest >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::eager::EnqueueResponse* Arena::CreateMaybeMessage< ::tensorflow::eager::EnqueueResponse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::eager::EnqueueResponse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::eager::WaitQueueDoneRequest* Arena::CreateMaybeMessage< ::tensorflow::eager::WaitQueueDoneRequest >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::eager::WaitQueueDoneRequest >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::eager::WaitQueueDoneResponse* Arena::CreateMaybeMessage< ::tensorflow::eager::WaitQueueDoneResponse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::eager::WaitQueueDoneResponse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::eager::KeepAliveRequest* Arena::CreateMaybeMessage< ::tensorflow::eager::KeepAliveRequest >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::eager::KeepAliveRequest >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::eager::KeepAliveResponse* Arena::CreateMaybeMessage< ::tensorflow::eager::KeepAliveResponse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::eager::KeepAliveResponse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::eager::CloseContextRequest* Arena::CreateMaybeMessage< ::tensorflow::eager::CloseContextRequest >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::eager::CloseContextRequest >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::eager::CloseContextResponse* Arena::CreateMaybeMessage< ::tensorflow::eager::CloseContextResponse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::eager::CloseContextResponse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::eager::RegisterFunctionRequest* Arena::CreateMaybeMessage< ::tensorflow::eager::RegisterFunctionRequest >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::eager::RegisterFunctionRequest >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::eager::RegisterFunctionResponse* Arena::CreateMaybeMessage< ::tensorflow::eager::RegisterFunctionResponse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::eager::RegisterFunctionResponse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::eager::SendTensorRequest* Arena::CreateMaybeMessage< ::tensorflow::eager::SendTensorRequest >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::eager::SendTensorRequest >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::eager::SendTensorResponse* Arena::CreateMaybeMessage< ::tensorflow::eager::SendTensorResponse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::eager::SendTensorResponse >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

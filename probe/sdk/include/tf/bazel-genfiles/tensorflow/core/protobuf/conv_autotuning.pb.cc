// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/conv_autotuning.proto

#include "tensorflow/core/protobuf/conv_autotuning.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_NodeDef;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_TensorProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto
namespace tensorflow {
class ConvNodeDefDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ConvNodeDef>
      _instance;
  const ::tensorflow::TensorProto* side_input_;
} _ConvNodeDef_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fprotobuf_2fconv_5fautotuning_2eproto {
static void InitDefaultsConvNodeDef() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_ConvNodeDef_default_instance_;
    new (ptr) ::tensorflow::ConvNodeDef();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::ConvNodeDef::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_ConvNodeDef =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsConvNodeDef}, {
      &protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto::scc_info_NodeDef.base,
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::scc_info_TensorProto.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_ConvNodeDef.base);
}

::google::protobuf::Metadata file_level_metadata[1];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConvNodeDef, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConvNodeDef, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConvNodeDef, conv_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConvNodeDef, input_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConvNodeDef, filter_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConvNodeDef, output_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConvNodeDef, bias_),
  offsetof(::tensorflow::ConvNodeDefDefaultTypeInternal, side_input_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConvNodeDef, side_input_oneof_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::ConvNodeDef)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_ConvNodeDef_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/protobuf/conv_autotuning.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 1);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n.tensorflow/core/protobuf/conv_autotuni"
      "ng.proto\022\ntensorflow\032(tensorflow/core/fr"
      "amework/node_def.proto\032&tensorflow/core/"
      "framework/tensor.proto\"\224\002\n\013ConvNodeDef\022!"
      "\n\004conv\030\001 \001(\0132\023.tensorflow.NodeDef\022&\n\005inp"
      "ut\030\002 \001(\0132\027.tensorflow.TensorProto\022\'\n\006fil"
      "ter\030\003 \001(\0132\027.tensorflow.TensorProto\022\'\n\006ou"
      "tput\030\004 \001(\0132\027.tensorflow.TensorProto\022%\n\004b"
      "ias\030\005 \001(\0132\027.tensorflow.TensorProto\022-\n\nsi"
      "de_input\030\006 \001(\0132\027.tensorflow.TensorProtoH"
      "\000B\022\n\020side_input_oneofb\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 429);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/protobuf/conv_autotuning.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fconv_5fautotuning_2eproto
namespace tensorflow {

// ===================================================================

void ConvNodeDef::InitAsDefaultInstance() {
  ::tensorflow::_ConvNodeDef_default_instance_._instance.get_mutable()->conv_ = const_cast< ::tensorflow::NodeDef*>(
      ::tensorflow::NodeDef::internal_default_instance());
  ::tensorflow::_ConvNodeDef_default_instance_._instance.get_mutable()->input_ = const_cast< ::tensorflow::TensorProto*>(
      ::tensorflow::TensorProto::internal_default_instance());
  ::tensorflow::_ConvNodeDef_default_instance_._instance.get_mutable()->filter_ = const_cast< ::tensorflow::TensorProto*>(
      ::tensorflow::TensorProto::internal_default_instance());
  ::tensorflow::_ConvNodeDef_default_instance_._instance.get_mutable()->output_ = const_cast< ::tensorflow::TensorProto*>(
      ::tensorflow::TensorProto::internal_default_instance());
  ::tensorflow::_ConvNodeDef_default_instance_._instance.get_mutable()->bias_ = const_cast< ::tensorflow::TensorProto*>(
      ::tensorflow::TensorProto::internal_default_instance());
  ::tensorflow::_ConvNodeDef_default_instance_.side_input_ = const_cast< ::tensorflow::TensorProto*>(
      ::tensorflow::TensorProto::internal_default_instance());
}
void ConvNodeDef::clear_conv() {
  if (GetArenaNoVirtual() == NULL && conv_ != NULL) {
    delete conv_;
  }
  conv_ = NULL;
}
void ConvNodeDef::clear_input() {
  if (GetArenaNoVirtual() == NULL && input_ != NULL) {
    delete input_;
  }
  input_ = NULL;
}
void ConvNodeDef::clear_filter() {
  if (GetArenaNoVirtual() == NULL && filter_ != NULL) {
    delete filter_;
  }
  filter_ = NULL;
}
void ConvNodeDef::clear_output() {
  if (GetArenaNoVirtual() == NULL && output_ != NULL) {
    delete output_;
  }
  output_ = NULL;
}
void ConvNodeDef::clear_bias() {
  if (GetArenaNoVirtual() == NULL && bias_ != NULL) {
    delete bias_;
  }
  bias_ = NULL;
}
void ConvNodeDef::set_allocated_side_input(::tensorflow::TensorProto* side_input) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_side_input_oneof();
  if (side_input) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(side_input)->GetArena();
    if (message_arena != submessage_arena) {
      side_input = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, side_input, submessage_arena);
    }
    set_has_side_input();
    side_input_oneof_.side_input_ = side_input;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ConvNodeDef.side_input)
}
void ConvNodeDef::clear_side_input() {
  if (has_side_input()) {
    delete side_input_oneof_.side_input_;
    clear_has_side_input_oneof();
  }
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ConvNodeDef::kConvFieldNumber;
const int ConvNodeDef::kInputFieldNumber;
const int ConvNodeDef::kFilterFieldNumber;
const int ConvNodeDef::kOutputFieldNumber;
const int ConvNodeDef::kBiasFieldNumber;
const int ConvNodeDef::kSideInputFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ConvNodeDef::ConvNodeDef()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconv_5fautotuning_2eproto::scc_info_ConvNodeDef.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.ConvNodeDef)
}
ConvNodeDef::ConvNodeDef(const ConvNodeDef& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_conv()) {
    conv_ = new ::tensorflow::NodeDef(*from.conv_);
  } else {
    conv_ = NULL;
  }
  if (from.has_input()) {
    input_ = new ::tensorflow::TensorProto(*from.input_);
  } else {
    input_ = NULL;
  }
  if (from.has_filter()) {
    filter_ = new ::tensorflow::TensorProto(*from.filter_);
  } else {
    filter_ = NULL;
  }
  if (from.has_output()) {
    output_ = new ::tensorflow::TensorProto(*from.output_);
  } else {
    output_ = NULL;
  }
  if (from.has_bias()) {
    bias_ = new ::tensorflow::TensorProto(*from.bias_);
  } else {
    bias_ = NULL;
  }
  clear_has_side_input_oneof();
  switch (from.side_input_oneof_case()) {
    case kSideInput: {
      mutable_side_input()->::tensorflow::TensorProto::MergeFrom(from.side_input());
      break;
    }
    case SIDE_INPUT_ONEOF_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.ConvNodeDef)
}

void ConvNodeDef::SharedCtor() {
  ::memset(&conv_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&bias_) -
      reinterpret_cast<char*>(&conv_)) + sizeof(bias_));
  clear_has_side_input_oneof();
}

ConvNodeDef::~ConvNodeDef() {
  // @@protoc_insertion_point(destructor:tensorflow.ConvNodeDef)
  SharedDtor();
}

void ConvNodeDef::SharedDtor() {
  if (this != internal_default_instance()) delete conv_;
  if (this != internal_default_instance()) delete input_;
  if (this != internal_default_instance()) delete filter_;
  if (this != internal_default_instance()) delete output_;
  if (this != internal_default_instance()) delete bias_;
  if (has_side_input_oneof()) {
    clear_side_input_oneof();
  }
}

void ConvNodeDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ConvNodeDef::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fconv_5fautotuning_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconv_5fautotuning_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ConvNodeDef& ConvNodeDef::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconv_5fautotuning_2eproto::scc_info_ConvNodeDef.base);
  return *internal_default_instance();
}


void ConvNodeDef::clear_side_input_oneof() {
// @@protoc_insertion_point(one_of_clear_start:tensorflow.ConvNodeDef)
  switch (side_input_oneof_case()) {
    case kSideInput: {
      delete side_input_oneof_.side_input_;
      break;
    }
    case SIDE_INPUT_ONEOF_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = SIDE_INPUT_ONEOF_NOT_SET;
}


void ConvNodeDef::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.ConvNodeDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && conv_ != NULL) {
    delete conv_;
  }
  conv_ = NULL;
  if (GetArenaNoVirtual() == NULL && input_ != NULL) {
    delete input_;
  }
  input_ = NULL;
  if (GetArenaNoVirtual() == NULL && filter_ != NULL) {
    delete filter_;
  }
  filter_ = NULL;
  if (GetArenaNoVirtual() == NULL && output_ != NULL) {
    delete output_;
  }
  output_ = NULL;
  if (GetArenaNoVirtual() == NULL && bias_ != NULL) {
    delete bias_;
  }
  bias_ = NULL;
  clear_side_input_oneof();
  _internal_metadata_.Clear();
}

bool ConvNodeDef::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.ConvNodeDef)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.NodeDef conv = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_conv()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorProto input = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_input()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorProto filter = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_filter()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorProto output = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_output()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorProto bias = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_bias()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorProto side_input = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_side_input()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.ConvNodeDef)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.ConvNodeDef)
  return false;
#undef DO_
}

void ConvNodeDef::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.ConvNodeDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.NodeDef conv = 1;
  if (this->has_conv()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->_internal_conv(), output);
  }

  // .tensorflow.TensorProto input = 2;
  if (this->has_input()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_input(), output);
  }

  // .tensorflow.TensorProto filter = 3;
  if (this->has_filter()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->_internal_filter(), output);
  }

  // .tensorflow.TensorProto output = 4;
  if (this->has_output()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->_internal_output(), output);
  }

  // .tensorflow.TensorProto bias = 5;
  if (this->has_bias()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, this->_internal_bias(), output);
  }

  // .tensorflow.TensorProto side_input = 6;
  if (has_side_input()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, this->_internal_side_input(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.ConvNodeDef)
}

::google::protobuf::uint8* ConvNodeDef::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.ConvNodeDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.NodeDef conv = 1;
  if (this->has_conv()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->_internal_conv(), deterministic, target);
  }

  // .tensorflow.TensorProto input = 2;
  if (this->has_input()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_input(), deterministic, target);
  }

  // .tensorflow.TensorProto filter = 3;
  if (this->has_filter()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->_internal_filter(), deterministic, target);
  }

  // .tensorflow.TensorProto output = 4;
  if (this->has_output()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->_internal_output(), deterministic, target);
  }

  // .tensorflow.TensorProto bias = 5;
  if (this->has_bias()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        5, this->_internal_bias(), deterministic, target);
  }

  // .tensorflow.TensorProto side_input = 6;
  if (has_side_input()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        6, this->_internal_side_input(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.ConvNodeDef)
  return target;
}

size_t ConvNodeDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.ConvNodeDef)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .tensorflow.NodeDef conv = 1;
  if (this->has_conv()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *conv_);
  }

  // .tensorflow.TensorProto input = 2;
  if (this->has_input()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *input_);
  }

  // .tensorflow.TensorProto filter = 3;
  if (this->has_filter()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *filter_);
  }

  // .tensorflow.TensorProto output = 4;
  if (this->has_output()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *output_);
  }

  // .tensorflow.TensorProto bias = 5;
  if (this->has_bias()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *bias_);
  }

  switch (side_input_oneof_case()) {
    // .tensorflow.TensorProto side_input = 6;
    case kSideInput: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *side_input_oneof_.side_input_);
      break;
    }
    case SIDE_INPUT_ONEOF_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ConvNodeDef::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.ConvNodeDef)
  GOOGLE_DCHECK_NE(&from, this);
  const ConvNodeDef* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ConvNodeDef>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.ConvNodeDef)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.ConvNodeDef)
    MergeFrom(*source);
  }
}

void ConvNodeDef::MergeFrom(const ConvNodeDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.ConvNodeDef)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_conv()) {
    mutable_conv()->::tensorflow::NodeDef::MergeFrom(from.conv());
  }
  if (from.has_input()) {
    mutable_input()->::tensorflow::TensorProto::MergeFrom(from.input());
  }
  if (from.has_filter()) {
    mutable_filter()->::tensorflow::TensorProto::MergeFrom(from.filter());
  }
  if (from.has_output()) {
    mutable_output()->::tensorflow::TensorProto::MergeFrom(from.output());
  }
  if (from.has_bias()) {
    mutable_bias()->::tensorflow::TensorProto::MergeFrom(from.bias());
  }
  switch (from.side_input_oneof_case()) {
    case kSideInput: {
      mutable_side_input()->::tensorflow::TensorProto::MergeFrom(from.side_input());
      break;
    }
    case SIDE_INPUT_ONEOF_NOT_SET: {
      break;
    }
  }
}

void ConvNodeDef::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.ConvNodeDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ConvNodeDef::CopyFrom(const ConvNodeDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.ConvNodeDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ConvNodeDef::IsInitialized() const {
  return true;
}

void ConvNodeDef::Swap(ConvNodeDef* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ConvNodeDef::InternalSwap(ConvNodeDef* other) {
  using std::swap;
  swap(conv_, other->conv_);
  swap(input_, other->input_);
  swap(filter_, other->filter_);
  swap(output_, other->output_);
  swap(bias_, other->bias_);
  swap(side_input_oneof_, other->side_input_oneof_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ConvNodeDef::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fconv_5fautotuning_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconv_5fautotuning_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::ConvNodeDef* Arena::CreateMaybeMessage< ::tensorflow::ConvNodeDef >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::ConvNodeDef >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

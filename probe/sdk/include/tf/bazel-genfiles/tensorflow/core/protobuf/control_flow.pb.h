// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/control_flow.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto 

namespace protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[5];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto
namespace tensorflow {
class CondContextDef;
class CondContextDefDefaultTypeInternal;
extern CondContextDefDefaultTypeInternal _CondContextDef_default_instance_;
class ControlFlowContextDef;
class ControlFlowContextDefDefaultTypeInternal;
extern ControlFlowContextDefDefaultTypeInternal _ControlFlowContextDef_default_instance_;
class ValuesDef;
class ValuesDefDefaultTypeInternal;
extern ValuesDefDefaultTypeInternal _ValuesDef_default_instance_;
class ValuesDef_ExternalValuesEntry_DoNotUse;
class ValuesDef_ExternalValuesEntry_DoNotUseDefaultTypeInternal;
extern ValuesDef_ExternalValuesEntry_DoNotUseDefaultTypeInternal _ValuesDef_ExternalValuesEntry_DoNotUse_default_instance_;
class WhileContextDef;
class WhileContextDefDefaultTypeInternal;
extern WhileContextDefDefaultTypeInternal _WhileContextDef_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::CondContextDef* Arena::CreateMaybeMessage<::tensorflow::CondContextDef>(Arena*);
template<> ::tensorflow::ControlFlowContextDef* Arena::CreateMaybeMessage<::tensorflow::ControlFlowContextDef>(Arena*);
template<> ::tensorflow::ValuesDef* Arena::CreateMaybeMessage<::tensorflow::ValuesDef>(Arena*);
template<> ::tensorflow::ValuesDef_ExternalValuesEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::ValuesDef_ExternalValuesEntry_DoNotUse>(Arena*);
template<> ::tensorflow::WhileContextDef* Arena::CreateMaybeMessage<::tensorflow::WhileContextDef>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

// ===================================================================

class ValuesDef_ExternalValuesEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<ValuesDef_ExternalValuesEntry_DoNotUse, 
    ::std::string, ::std::string,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<ValuesDef_ExternalValuesEntry_DoNotUse, 
    ::std::string, ::std::string,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    0 > SuperType;
  ValuesDef_ExternalValuesEntry_DoNotUse();
  ValuesDef_ExternalValuesEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const ValuesDef_ExternalValuesEntry_DoNotUse& other);
  static const ValuesDef_ExternalValuesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ValuesDef_ExternalValuesEntry_DoNotUse*>(&_ValuesDef_ExternalValuesEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class ValuesDef : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.ValuesDef) */ {
 public:
  ValuesDef();
  virtual ~ValuesDef();

  ValuesDef(const ValuesDef& from);

  inline ValuesDef& operator=(const ValuesDef& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ValuesDef(ValuesDef&& from) noexcept
    : ValuesDef() {
    *this = ::std::move(from);
  }

  inline ValuesDef& operator=(ValuesDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const ValuesDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ValuesDef* internal_default_instance() {
    return reinterpret_cast<const ValuesDef*>(
               &_ValuesDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void UnsafeArenaSwap(ValuesDef* other);
  void Swap(ValuesDef* other);
  friend void swap(ValuesDef& a, ValuesDef& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ValuesDef* New() const final {
    return CreateMaybeMessage<ValuesDef>(NULL);
  }

  ValuesDef* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ValuesDef>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ValuesDef& from);
  void MergeFrom(const ValuesDef& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ValuesDef* other);
  protected:
  explicit ValuesDef(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // repeated string values = 1;
  int values_size() const;
  void clear_values();
  static const int kValuesFieldNumber = 1;
  const ::std::string& values(int index) const;
  ::std::string* mutable_values(int index);
  void set_values(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_values(int index, ::std::string&& value);
  #endif
  void set_values(int index, const char* value);
  void set_values(int index, const char* value, size_t size);
  ::std::string* add_values();
  void add_values(const ::std::string& value);
  #if LANG_CXX11
  void add_values(::std::string&& value);
  #endif
  void add_values(const char* value);
  void add_values(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& values() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_values();

  // map<string, string> external_values = 2;
  int external_values_size() const;
  void clear_external_values();
  static const int kExternalValuesFieldNumber = 2;
  const ::google::protobuf::Map< ::std::string, ::std::string >&
      external_values() const;
  ::google::protobuf::Map< ::std::string, ::std::string >*
      mutable_external_values();

  // @@protoc_insertion_point(class_scope:tensorflow.ValuesDef)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::std::string> values_;
  ::google::protobuf::internal::MapField<
      ValuesDef_ExternalValuesEntry_DoNotUse,
      ::std::string, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 > external_values_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ControlFlowContextDef : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.ControlFlowContextDef) */ {
 public:
  ControlFlowContextDef();
  virtual ~ControlFlowContextDef();

  ControlFlowContextDef(const ControlFlowContextDef& from);

  inline ControlFlowContextDef& operator=(const ControlFlowContextDef& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ControlFlowContextDef(ControlFlowContextDef&& from) noexcept
    : ControlFlowContextDef() {
    *this = ::std::move(from);
  }

  inline ControlFlowContextDef& operator=(ControlFlowContextDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const ControlFlowContextDef& default_instance();

  enum CtxtCase {
    kCondCtxt = 1,
    kWhileCtxt = 2,
    CTXT_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ControlFlowContextDef* internal_default_instance() {
    return reinterpret_cast<const ControlFlowContextDef*>(
               &_ControlFlowContextDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void UnsafeArenaSwap(ControlFlowContextDef* other);
  void Swap(ControlFlowContextDef* other);
  friend void swap(ControlFlowContextDef& a, ControlFlowContextDef& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ControlFlowContextDef* New() const final {
    return CreateMaybeMessage<ControlFlowContextDef>(NULL);
  }

  ControlFlowContextDef* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ControlFlowContextDef>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ControlFlowContextDef& from);
  void MergeFrom(const ControlFlowContextDef& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ControlFlowContextDef* other);
  protected:
  explicit ControlFlowContextDef(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .tensorflow.CondContextDef cond_ctxt = 1;
  bool has_cond_ctxt() const;
  void clear_cond_ctxt();
  static const int kCondCtxtFieldNumber = 1;
  private:
  const ::tensorflow::CondContextDef& _internal_cond_ctxt() const;
  public:
  const ::tensorflow::CondContextDef& cond_ctxt() const;
  ::tensorflow::CondContextDef* release_cond_ctxt();
  ::tensorflow::CondContextDef* mutable_cond_ctxt();
  void set_allocated_cond_ctxt(::tensorflow::CondContextDef* cond_ctxt);
  void unsafe_arena_set_allocated_cond_ctxt(
      ::tensorflow::CondContextDef* cond_ctxt);
  ::tensorflow::CondContextDef* unsafe_arena_release_cond_ctxt();

  // .tensorflow.WhileContextDef while_ctxt = 2;
  bool has_while_ctxt() const;
  void clear_while_ctxt();
  static const int kWhileCtxtFieldNumber = 2;
  private:
  const ::tensorflow::WhileContextDef& _internal_while_ctxt() const;
  public:
  const ::tensorflow::WhileContextDef& while_ctxt() const;
  ::tensorflow::WhileContextDef* release_while_ctxt();
  ::tensorflow::WhileContextDef* mutable_while_ctxt();
  void set_allocated_while_ctxt(::tensorflow::WhileContextDef* while_ctxt);
  void unsafe_arena_set_allocated_while_ctxt(
      ::tensorflow::WhileContextDef* while_ctxt);
  ::tensorflow::WhileContextDef* unsafe_arena_release_while_ctxt();

  void clear_ctxt();
  CtxtCase ctxt_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.ControlFlowContextDef)
 private:
  void set_has_cond_ctxt();
  void set_has_while_ctxt();

  inline bool has_ctxt() const;
  inline void clear_has_ctxt();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  union CtxtUnion {
    CtxtUnion() {}
    ::tensorflow::CondContextDef* cond_ctxt_;
    ::tensorflow::WhileContextDef* while_ctxt_;
  } ctxt_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CondContextDef : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.CondContextDef) */ {
 public:
  CondContextDef();
  virtual ~CondContextDef();

  CondContextDef(const CondContextDef& from);

  inline CondContextDef& operator=(const CondContextDef& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CondContextDef(CondContextDef&& from) noexcept
    : CondContextDef() {
    *this = ::std::move(from);
  }

  inline CondContextDef& operator=(CondContextDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const CondContextDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CondContextDef* internal_default_instance() {
    return reinterpret_cast<const CondContextDef*>(
               &_CondContextDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void UnsafeArenaSwap(CondContextDef* other);
  void Swap(CondContextDef* other);
  friend void swap(CondContextDef& a, CondContextDef& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CondContextDef* New() const final {
    return CreateMaybeMessage<CondContextDef>(NULL);
  }

  CondContextDef* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CondContextDef>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CondContextDef& from);
  void MergeFrom(const CondContextDef& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CondContextDef* other);
  protected:
  explicit CondContextDef(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.ControlFlowContextDef nested_contexts = 6;
  int nested_contexts_size() const;
  void clear_nested_contexts();
  static const int kNestedContextsFieldNumber = 6;
  ::tensorflow::ControlFlowContextDef* mutable_nested_contexts(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::ControlFlowContextDef >*
      mutable_nested_contexts();
  const ::tensorflow::ControlFlowContextDef& nested_contexts(int index) const;
  ::tensorflow::ControlFlowContextDef* add_nested_contexts();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::ControlFlowContextDef >&
      nested_contexts() const;

  // string context_name = 1;
  void clear_context_name();
  static const int kContextNameFieldNumber = 1;
  const ::std::string& context_name() const;
  void set_context_name(const ::std::string& value);
  #if LANG_CXX11
  void set_context_name(::std::string&& value);
  #endif
  void set_context_name(const char* value);
  void set_context_name(const char* value, size_t size);
  ::std::string* mutable_context_name();
  ::std::string* release_context_name();
  void set_allocated_context_name(::std::string* context_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_context_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_context_name(
      ::std::string* context_name);

  // string pred_name = 2;
  void clear_pred_name();
  static const int kPredNameFieldNumber = 2;
  const ::std::string& pred_name() const;
  void set_pred_name(const ::std::string& value);
  #if LANG_CXX11
  void set_pred_name(::std::string&& value);
  #endif
  void set_pred_name(const char* value);
  void set_pred_name(const char* value, size_t size);
  ::std::string* mutable_pred_name();
  ::std::string* release_pred_name();
  void set_allocated_pred_name(::std::string* pred_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_pred_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_pred_name(
      ::std::string* pred_name);

  // string pivot_name = 3;
  void clear_pivot_name();
  static const int kPivotNameFieldNumber = 3;
  const ::std::string& pivot_name() const;
  void set_pivot_name(const ::std::string& value);
  #if LANG_CXX11
  void set_pivot_name(::std::string&& value);
  #endif
  void set_pivot_name(const char* value);
  void set_pivot_name(const char* value, size_t size);
  ::std::string* mutable_pivot_name();
  ::std::string* release_pivot_name();
  void set_allocated_pivot_name(::std::string* pivot_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_pivot_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_pivot_name(
      ::std::string* pivot_name);

  // .tensorflow.ValuesDef values_def = 5;
  bool has_values_def() const;
  void clear_values_def();
  static const int kValuesDefFieldNumber = 5;
  private:
  const ::tensorflow::ValuesDef& _internal_values_def() const;
  public:
  const ::tensorflow::ValuesDef& values_def() const;
  ::tensorflow::ValuesDef* release_values_def();
  ::tensorflow::ValuesDef* mutable_values_def();
  void set_allocated_values_def(::tensorflow::ValuesDef* values_def);
  void unsafe_arena_set_allocated_values_def(
      ::tensorflow::ValuesDef* values_def);
  ::tensorflow::ValuesDef* unsafe_arena_release_values_def();

  // int32 branch = 4;
  void clear_branch();
  static const int kBranchFieldNumber = 4;
  ::google::protobuf::int32 branch() const;
  void set_branch(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.CondContextDef)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::ControlFlowContextDef > nested_contexts_;
  ::google::protobuf::internal::ArenaStringPtr context_name_;
  ::google::protobuf::internal::ArenaStringPtr pred_name_;
  ::google::protobuf::internal::ArenaStringPtr pivot_name_;
  ::tensorflow::ValuesDef* values_def_;
  ::google::protobuf::int32 branch_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class WhileContextDef : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.WhileContextDef) */ {
 public:
  WhileContextDef();
  virtual ~WhileContextDef();

  WhileContextDef(const WhileContextDef& from);

  inline WhileContextDef& operator=(const WhileContextDef& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  WhileContextDef(WhileContextDef&& from) noexcept
    : WhileContextDef() {
    *this = ::std::move(from);
  }

  inline WhileContextDef& operator=(WhileContextDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const WhileContextDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const WhileContextDef* internal_default_instance() {
    return reinterpret_cast<const WhileContextDef*>(
               &_WhileContextDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void UnsafeArenaSwap(WhileContextDef* other);
  void Swap(WhileContextDef* other);
  friend void swap(WhileContextDef& a, WhileContextDef& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline WhileContextDef* New() const final {
    return CreateMaybeMessage<WhileContextDef>(NULL);
  }

  WhileContextDef* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<WhileContextDef>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const WhileContextDef& from);
  void MergeFrom(const WhileContextDef& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WhileContextDef* other);
  protected:
  explicit WhileContextDef(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string loop_exit_names = 8;
  int loop_exit_names_size() const;
  void clear_loop_exit_names();
  static const int kLoopExitNamesFieldNumber = 8;
  const ::std::string& loop_exit_names(int index) const;
  ::std::string* mutable_loop_exit_names(int index);
  void set_loop_exit_names(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_loop_exit_names(int index, ::std::string&& value);
  #endif
  void set_loop_exit_names(int index, const char* value);
  void set_loop_exit_names(int index, const char* value, size_t size);
  ::std::string* add_loop_exit_names();
  void add_loop_exit_names(const ::std::string& value);
  #if LANG_CXX11
  void add_loop_exit_names(::std::string&& value);
  #endif
  void add_loop_exit_names(const char* value);
  void add_loop_exit_names(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& loop_exit_names() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_loop_exit_names();

  // repeated string loop_enter_names = 10;
  int loop_enter_names_size() const;
  void clear_loop_enter_names();
  static const int kLoopEnterNamesFieldNumber = 10;
  const ::std::string& loop_enter_names(int index) const;
  ::std::string* mutable_loop_enter_names(int index);
  void set_loop_enter_names(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_loop_enter_names(int index, ::std::string&& value);
  #endif
  void set_loop_enter_names(int index, const char* value);
  void set_loop_enter_names(int index, const char* value, size_t size);
  ::std::string* add_loop_enter_names();
  void add_loop_enter_names(const ::std::string& value);
  #if LANG_CXX11
  void add_loop_enter_names(::std::string&& value);
  #endif
  void add_loop_enter_names(const char* value);
  void add_loop_enter_names(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& loop_enter_names() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_loop_enter_names();

  // repeated .tensorflow.ControlFlowContextDef nested_contexts = 12;
  int nested_contexts_size() const;
  void clear_nested_contexts();
  static const int kNestedContextsFieldNumber = 12;
  ::tensorflow::ControlFlowContextDef* mutable_nested_contexts(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::ControlFlowContextDef >*
      mutable_nested_contexts();
  const ::tensorflow::ControlFlowContextDef& nested_contexts(int index) const;
  ::tensorflow::ControlFlowContextDef* add_nested_contexts();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::ControlFlowContextDef >&
      nested_contexts() const;

  // string context_name = 1;
  void clear_context_name();
  static const int kContextNameFieldNumber = 1;
  const ::std::string& context_name() const;
  void set_context_name(const ::std::string& value);
  #if LANG_CXX11
  void set_context_name(::std::string&& value);
  #endif
  void set_context_name(const char* value);
  void set_context_name(const char* value, size_t size);
  ::std::string* mutable_context_name();
  ::std::string* release_context_name();
  void set_allocated_context_name(::std::string* context_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_context_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_context_name(
      ::std::string* context_name);

  // string pivot_name = 5;
  void clear_pivot_name();
  static const int kPivotNameFieldNumber = 5;
  const ::std::string& pivot_name() const;
  void set_pivot_name(const ::std::string& value);
  #if LANG_CXX11
  void set_pivot_name(::std::string&& value);
  #endif
  void set_pivot_name(const char* value);
  void set_pivot_name(const char* value, size_t size);
  ::std::string* mutable_pivot_name();
  ::std::string* release_pivot_name();
  void set_allocated_pivot_name(::std::string* pivot_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_pivot_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_pivot_name(
      ::std::string* pivot_name);

  // string pivot_for_pred_name = 6;
  void clear_pivot_for_pred_name();
  static const int kPivotForPredNameFieldNumber = 6;
  const ::std::string& pivot_for_pred_name() const;
  void set_pivot_for_pred_name(const ::std::string& value);
  #if LANG_CXX11
  void set_pivot_for_pred_name(::std::string&& value);
  #endif
  void set_pivot_for_pred_name(const char* value);
  void set_pivot_for_pred_name(const char* value, size_t size);
  ::std::string* mutable_pivot_for_pred_name();
  ::std::string* release_pivot_for_pred_name();
  void set_allocated_pivot_for_pred_name(::std::string* pivot_for_pred_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_pivot_for_pred_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_pivot_for_pred_name(
      ::std::string* pivot_for_pred_name);

  // string pivot_for_body_name = 7;
  void clear_pivot_for_body_name();
  static const int kPivotForBodyNameFieldNumber = 7;
  const ::std::string& pivot_for_body_name() const;
  void set_pivot_for_body_name(const ::std::string& value);
  #if LANG_CXX11
  void set_pivot_for_body_name(::std::string&& value);
  #endif
  void set_pivot_for_body_name(const char* value);
  void set_pivot_for_body_name(const char* value, size_t size);
  ::std::string* mutable_pivot_for_body_name();
  ::std::string* release_pivot_for_body_name();
  void set_allocated_pivot_for_body_name(::std::string* pivot_for_body_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_pivot_for_body_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_pivot_for_body_name(
      ::std::string* pivot_for_body_name);

  // string maximum_iterations_name = 11;
  void clear_maximum_iterations_name();
  static const int kMaximumIterationsNameFieldNumber = 11;
  const ::std::string& maximum_iterations_name() const;
  void set_maximum_iterations_name(const ::std::string& value);
  #if LANG_CXX11
  void set_maximum_iterations_name(::std::string&& value);
  #endif
  void set_maximum_iterations_name(const char* value);
  void set_maximum_iterations_name(const char* value, size_t size);
  ::std::string* mutable_maximum_iterations_name();
  ::std::string* release_maximum_iterations_name();
  void set_allocated_maximum_iterations_name(::std::string* maximum_iterations_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_maximum_iterations_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_maximum_iterations_name(
      ::std::string* maximum_iterations_name);

  // .tensorflow.ValuesDef values_def = 9;
  bool has_values_def() const;
  void clear_values_def();
  static const int kValuesDefFieldNumber = 9;
  private:
  const ::tensorflow::ValuesDef& _internal_values_def() const;
  public:
  const ::tensorflow::ValuesDef& values_def() const;
  ::tensorflow::ValuesDef* release_values_def();
  ::tensorflow::ValuesDef* mutable_values_def();
  void set_allocated_values_def(::tensorflow::ValuesDef* values_def);
  void unsafe_arena_set_allocated_values_def(
      ::tensorflow::ValuesDef* values_def);
  ::tensorflow::ValuesDef* unsafe_arena_release_values_def();

  // int32 parallel_iterations = 2;
  void clear_parallel_iterations();
  static const int kParallelIterationsFieldNumber = 2;
  ::google::protobuf::int32 parallel_iterations() const;
  void set_parallel_iterations(::google::protobuf::int32 value);

  // bool back_prop = 3;
  void clear_back_prop();
  static const int kBackPropFieldNumber = 3;
  bool back_prop() const;
  void set_back_prop(bool value);

  // bool swap_memory = 4;
  void clear_swap_memory();
  static const int kSwapMemoryFieldNumber = 4;
  bool swap_memory() const;
  void set_swap_memory(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.WhileContextDef)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::std::string> loop_exit_names_;
  ::google::protobuf::RepeatedPtrField< ::std::string> loop_enter_names_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::ControlFlowContextDef > nested_contexts_;
  ::google::protobuf::internal::ArenaStringPtr context_name_;
  ::google::protobuf::internal::ArenaStringPtr pivot_name_;
  ::google::protobuf::internal::ArenaStringPtr pivot_for_pred_name_;
  ::google::protobuf::internal::ArenaStringPtr pivot_for_body_name_;
  ::google::protobuf::internal::ArenaStringPtr maximum_iterations_name_;
  ::tensorflow::ValuesDef* values_def_;
  ::google::protobuf::int32 parallel_iterations_;
  bool back_prop_;
  bool swap_memory_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// -------------------------------------------------------------------

// ValuesDef

// repeated string values = 1;
inline int ValuesDef::values_size() const {
  return values_.size();
}
inline void ValuesDef::clear_values() {
  values_.Clear();
}
inline const ::std::string& ValuesDef::values(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ValuesDef.values)
  return values_.Get(index);
}
inline ::std::string* ValuesDef::mutable_values(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ValuesDef.values)
  return values_.Mutable(index);
}
inline void ValuesDef::set_values(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.ValuesDef.values)
  values_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void ValuesDef::set_values(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.ValuesDef.values)
  values_.Mutable(index)->assign(std::move(value));
}
#endif
inline void ValuesDef::set_values(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  values_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.ValuesDef.values)
}
inline void ValuesDef::set_values(int index, const char* value, size_t size) {
  values_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ValuesDef.values)
}
inline ::std::string* ValuesDef::add_values() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.ValuesDef.values)
  return values_.Add();
}
inline void ValuesDef::add_values(const ::std::string& value) {
  values_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.ValuesDef.values)
}
#if LANG_CXX11
inline void ValuesDef::add_values(::std::string&& value) {
  values_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.ValuesDef.values)
}
#endif
inline void ValuesDef::add_values(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  values_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.ValuesDef.values)
}
inline void ValuesDef::add_values(const char* value, size_t size) {
  values_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.ValuesDef.values)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
ValuesDef::values() const {
  // @@protoc_insertion_point(field_list:tensorflow.ValuesDef.values)
  return values_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
ValuesDef::mutable_values() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ValuesDef.values)
  return &values_;
}

// map<string, string> external_values = 2;
inline int ValuesDef::external_values_size() const {
  return external_values_.size();
}
inline void ValuesDef::clear_external_values() {
  external_values_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::std::string >&
ValuesDef::external_values() const {
  // @@protoc_insertion_point(field_map:tensorflow.ValuesDef.external_values)
  return external_values_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::std::string >*
ValuesDef::mutable_external_values() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.ValuesDef.external_values)
  return external_values_.MutableMap();
}

// -------------------------------------------------------------------

// ControlFlowContextDef

// .tensorflow.CondContextDef cond_ctxt = 1;
inline bool ControlFlowContextDef::has_cond_ctxt() const {
  return ctxt_case() == kCondCtxt;
}
inline void ControlFlowContextDef::set_has_cond_ctxt() {
  _oneof_case_[0] = kCondCtxt;
}
inline void ControlFlowContextDef::clear_cond_ctxt() {
  if (has_cond_ctxt()) {
    if (GetArenaNoVirtual() == NULL) {
      delete ctxt_.cond_ctxt_;
    }
    clear_has_ctxt();
  }
}
inline const ::tensorflow::CondContextDef& ControlFlowContextDef::_internal_cond_ctxt() const {
  return *ctxt_.cond_ctxt_;
}
inline ::tensorflow::CondContextDef* ControlFlowContextDef::release_cond_ctxt() {
  // @@protoc_insertion_point(field_release:tensorflow.ControlFlowContextDef.cond_ctxt)
  if (has_cond_ctxt()) {
    clear_has_ctxt();
      ::tensorflow::CondContextDef* temp = ctxt_.cond_ctxt_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    ctxt_.cond_ctxt_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::CondContextDef& ControlFlowContextDef::cond_ctxt() const {
  // @@protoc_insertion_point(field_get:tensorflow.ControlFlowContextDef.cond_ctxt)
  return has_cond_ctxt()
      ? *ctxt_.cond_ctxt_
      : *reinterpret_cast< ::tensorflow::CondContextDef*>(&::tensorflow::_CondContextDef_default_instance_);
}
inline ::tensorflow::CondContextDef* ControlFlowContextDef::unsafe_arena_release_cond_ctxt() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ControlFlowContextDef.cond_ctxt)
  if (has_cond_ctxt()) {
    clear_has_ctxt();
    ::tensorflow::CondContextDef* temp = ctxt_.cond_ctxt_;
    ctxt_.cond_ctxt_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void ControlFlowContextDef::unsafe_arena_set_allocated_cond_ctxt(::tensorflow::CondContextDef* cond_ctxt) {
  clear_ctxt();
  if (cond_ctxt) {
    set_has_cond_ctxt();
    ctxt_.cond_ctxt_ = cond_ctxt;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ControlFlowContextDef.cond_ctxt)
}
inline ::tensorflow::CondContextDef* ControlFlowContextDef::mutable_cond_ctxt() {
  if (!has_cond_ctxt()) {
    clear_ctxt();
    set_has_cond_ctxt();
    ctxt_.cond_ctxt_ = CreateMaybeMessage< ::tensorflow::CondContextDef >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ControlFlowContextDef.cond_ctxt)
  return ctxt_.cond_ctxt_;
}

// .tensorflow.WhileContextDef while_ctxt = 2;
inline bool ControlFlowContextDef::has_while_ctxt() const {
  return ctxt_case() == kWhileCtxt;
}
inline void ControlFlowContextDef::set_has_while_ctxt() {
  _oneof_case_[0] = kWhileCtxt;
}
inline void ControlFlowContextDef::clear_while_ctxt() {
  if (has_while_ctxt()) {
    if (GetArenaNoVirtual() == NULL) {
      delete ctxt_.while_ctxt_;
    }
    clear_has_ctxt();
  }
}
inline const ::tensorflow::WhileContextDef& ControlFlowContextDef::_internal_while_ctxt() const {
  return *ctxt_.while_ctxt_;
}
inline ::tensorflow::WhileContextDef* ControlFlowContextDef::release_while_ctxt() {
  // @@protoc_insertion_point(field_release:tensorflow.ControlFlowContextDef.while_ctxt)
  if (has_while_ctxt()) {
    clear_has_ctxt();
      ::tensorflow::WhileContextDef* temp = ctxt_.while_ctxt_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    ctxt_.while_ctxt_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::WhileContextDef& ControlFlowContextDef::while_ctxt() const {
  // @@protoc_insertion_point(field_get:tensorflow.ControlFlowContextDef.while_ctxt)
  return has_while_ctxt()
      ? *ctxt_.while_ctxt_
      : *reinterpret_cast< ::tensorflow::WhileContextDef*>(&::tensorflow::_WhileContextDef_default_instance_);
}
inline ::tensorflow::WhileContextDef* ControlFlowContextDef::unsafe_arena_release_while_ctxt() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ControlFlowContextDef.while_ctxt)
  if (has_while_ctxt()) {
    clear_has_ctxt();
    ::tensorflow::WhileContextDef* temp = ctxt_.while_ctxt_;
    ctxt_.while_ctxt_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void ControlFlowContextDef::unsafe_arena_set_allocated_while_ctxt(::tensorflow::WhileContextDef* while_ctxt) {
  clear_ctxt();
  if (while_ctxt) {
    set_has_while_ctxt();
    ctxt_.while_ctxt_ = while_ctxt;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ControlFlowContextDef.while_ctxt)
}
inline ::tensorflow::WhileContextDef* ControlFlowContextDef::mutable_while_ctxt() {
  if (!has_while_ctxt()) {
    clear_ctxt();
    set_has_while_ctxt();
    ctxt_.while_ctxt_ = CreateMaybeMessage< ::tensorflow::WhileContextDef >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ControlFlowContextDef.while_ctxt)
  return ctxt_.while_ctxt_;
}

inline bool ControlFlowContextDef::has_ctxt() const {
  return ctxt_case() != CTXT_NOT_SET;
}
inline void ControlFlowContextDef::clear_has_ctxt() {
  _oneof_case_[0] = CTXT_NOT_SET;
}
inline ControlFlowContextDef::CtxtCase ControlFlowContextDef::ctxt_case() const {
  return ControlFlowContextDef::CtxtCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// CondContextDef

// string context_name = 1;
inline void CondContextDef::clear_context_name() {
  context_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& CondContextDef::context_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.CondContextDef.context_name)
  return context_name_.Get();
}
inline void CondContextDef::set_context_name(const ::std::string& value) {
  
  context_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CondContextDef.context_name)
}
#if LANG_CXX11
inline void CondContextDef::set_context_name(::std::string&& value) {
  
  context_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CondContextDef.context_name)
}
#endif
inline void CondContextDef::set_context_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  context_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CondContextDef.context_name)
}
inline void CondContextDef::set_context_name(const char* value,
    size_t size) {
  
  context_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CondContextDef.context_name)
}
inline ::std::string* CondContextDef::mutable_context_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CondContextDef.context_name)
  return context_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* CondContextDef::release_context_name() {
  // @@protoc_insertion_point(field_release:tensorflow.CondContextDef.context_name)
  
  return context_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CondContextDef::set_allocated_context_name(::std::string* context_name) {
  if (context_name != NULL) {
    
  } else {
    
  }
  context_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), context_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CondContextDef.context_name)
}
inline ::std::string* CondContextDef::unsafe_arena_release_context_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CondContextDef.context_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return context_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CondContextDef::unsafe_arena_set_allocated_context_name(
    ::std::string* context_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (context_name != NULL) {
    
  } else {
    
  }
  context_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      context_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CondContextDef.context_name)
}

// string pred_name = 2;
inline void CondContextDef::clear_pred_name() {
  pred_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& CondContextDef::pred_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.CondContextDef.pred_name)
  return pred_name_.Get();
}
inline void CondContextDef::set_pred_name(const ::std::string& value) {
  
  pred_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CondContextDef.pred_name)
}
#if LANG_CXX11
inline void CondContextDef::set_pred_name(::std::string&& value) {
  
  pred_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CondContextDef.pred_name)
}
#endif
inline void CondContextDef::set_pred_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  pred_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CondContextDef.pred_name)
}
inline void CondContextDef::set_pred_name(const char* value,
    size_t size) {
  
  pred_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CondContextDef.pred_name)
}
inline ::std::string* CondContextDef::mutable_pred_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CondContextDef.pred_name)
  return pred_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* CondContextDef::release_pred_name() {
  // @@protoc_insertion_point(field_release:tensorflow.CondContextDef.pred_name)
  
  return pred_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CondContextDef::set_allocated_pred_name(::std::string* pred_name) {
  if (pred_name != NULL) {
    
  } else {
    
  }
  pred_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), pred_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CondContextDef.pred_name)
}
inline ::std::string* CondContextDef::unsafe_arena_release_pred_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CondContextDef.pred_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return pred_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CondContextDef::unsafe_arena_set_allocated_pred_name(
    ::std::string* pred_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (pred_name != NULL) {
    
  } else {
    
  }
  pred_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      pred_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CondContextDef.pred_name)
}

// string pivot_name = 3;
inline void CondContextDef::clear_pivot_name() {
  pivot_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& CondContextDef::pivot_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.CondContextDef.pivot_name)
  return pivot_name_.Get();
}
inline void CondContextDef::set_pivot_name(const ::std::string& value) {
  
  pivot_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CondContextDef.pivot_name)
}
#if LANG_CXX11
inline void CondContextDef::set_pivot_name(::std::string&& value) {
  
  pivot_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CondContextDef.pivot_name)
}
#endif
inline void CondContextDef::set_pivot_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  pivot_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CondContextDef.pivot_name)
}
inline void CondContextDef::set_pivot_name(const char* value,
    size_t size) {
  
  pivot_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CondContextDef.pivot_name)
}
inline ::std::string* CondContextDef::mutable_pivot_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CondContextDef.pivot_name)
  return pivot_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* CondContextDef::release_pivot_name() {
  // @@protoc_insertion_point(field_release:tensorflow.CondContextDef.pivot_name)
  
  return pivot_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CondContextDef::set_allocated_pivot_name(::std::string* pivot_name) {
  if (pivot_name != NULL) {
    
  } else {
    
  }
  pivot_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), pivot_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CondContextDef.pivot_name)
}
inline ::std::string* CondContextDef::unsafe_arena_release_pivot_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CondContextDef.pivot_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return pivot_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CondContextDef::unsafe_arena_set_allocated_pivot_name(
    ::std::string* pivot_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (pivot_name != NULL) {
    
  } else {
    
  }
  pivot_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      pivot_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CondContextDef.pivot_name)
}

// int32 branch = 4;
inline void CondContextDef::clear_branch() {
  branch_ = 0;
}
inline ::google::protobuf::int32 CondContextDef::branch() const {
  // @@protoc_insertion_point(field_get:tensorflow.CondContextDef.branch)
  return branch_;
}
inline void CondContextDef::set_branch(::google::protobuf::int32 value) {
  
  branch_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CondContextDef.branch)
}

// .tensorflow.ValuesDef values_def = 5;
inline bool CondContextDef::has_values_def() const {
  return this != internal_default_instance() && values_def_ != NULL;
}
inline void CondContextDef::clear_values_def() {
  if (GetArenaNoVirtual() == NULL && values_def_ != NULL) {
    delete values_def_;
  }
  values_def_ = NULL;
}
inline const ::tensorflow::ValuesDef& CondContextDef::_internal_values_def() const {
  return *values_def_;
}
inline const ::tensorflow::ValuesDef& CondContextDef::values_def() const {
  const ::tensorflow::ValuesDef* p = values_def_;
  // @@protoc_insertion_point(field_get:tensorflow.CondContextDef.values_def)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::ValuesDef*>(
      &::tensorflow::_ValuesDef_default_instance_);
}
inline ::tensorflow::ValuesDef* CondContextDef::release_values_def() {
  // @@protoc_insertion_point(field_release:tensorflow.CondContextDef.values_def)
  
  ::tensorflow::ValuesDef* temp = values_def_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  values_def_ = NULL;
  return temp;
}
inline ::tensorflow::ValuesDef* CondContextDef::unsafe_arena_release_values_def() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CondContextDef.values_def)
  
  ::tensorflow::ValuesDef* temp = values_def_;
  values_def_ = NULL;
  return temp;
}
inline ::tensorflow::ValuesDef* CondContextDef::mutable_values_def() {
  
  if (values_def_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::ValuesDef>(GetArenaNoVirtual());
    values_def_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.CondContextDef.values_def)
  return values_def_;
}
inline void CondContextDef::set_allocated_values_def(::tensorflow::ValuesDef* values_def) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete values_def_;
  }
  if (values_def) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(values_def);
    if (message_arena != submessage_arena) {
      values_def = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, values_def, submessage_arena);
    }
    
  } else {
    
  }
  values_def_ = values_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CondContextDef.values_def)
}

// repeated .tensorflow.ControlFlowContextDef nested_contexts = 6;
inline int CondContextDef::nested_contexts_size() const {
  return nested_contexts_.size();
}
inline void CondContextDef::clear_nested_contexts() {
  nested_contexts_.Clear();
}
inline ::tensorflow::ControlFlowContextDef* CondContextDef::mutable_nested_contexts(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CondContextDef.nested_contexts)
  return nested_contexts_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::ControlFlowContextDef >*
CondContextDef::mutable_nested_contexts() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CondContextDef.nested_contexts)
  return &nested_contexts_;
}
inline const ::tensorflow::ControlFlowContextDef& CondContextDef::nested_contexts(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CondContextDef.nested_contexts)
  return nested_contexts_.Get(index);
}
inline ::tensorflow::ControlFlowContextDef* CondContextDef::add_nested_contexts() {
  // @@protoc_insertion_point(field_add:tensorflow.CondContextDef.nested_contexts)
  return nested_contexts_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::ControlFlowContextDef >&
CondContextDef::nested_contexts() const {
  // @@protoc_insertion_point(field_list:tensorflow.CondContextDef.nested_contexts)
  return nested_contexts_;
}

// -------------------------------------------------------------------

// WhileContextDef

// string context_name = 1;
inline void WhileContextDef::clear_context_name() {
  context_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& WhileContextDef::context_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.WhileContextDef.context_name)
  return context_name_.Get();
}
inline void WhileContextDef::set_context_name(const ::std::string& value) {
  
  context_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.WhileContextDef.context_name)
}
#if LANG_CXX11
inline void WhileContextDef::set_context_name(::std::string&& value) {
  
  context_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.WhileContextDef.context_name)
}
#endif
inline void WhileContextDef::set_context_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  context_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.WhileContextDef.context_name)
}
inline void WhileContextDef::set_context_name(const char* value,
    size_t size) {
  
  context_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.WhileContextDef.context_name)
}
inline ::std::string* WhileContextDef::mutable_context_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.WhileContextDef.context_name)
  return context_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* WhileContextDef::release_context_name() {
  // @@protoc_insertion_point(field_release:tensorflow.WhileContextDef.context_name)
  
  return context_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void WhileContextDef::set_allocated_context_name(::std::string* context_name) {
  if (context_name != NULL) {
    
  } else {
    
  }
  context_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), context_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.WhileContextDef.context_name)
}
inline ::std::string* WhileContextDef::unsafe_arena_release_context_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.WhileContextDef.context_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return context_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void WhileContextDef::unsafe_arena_set_allocated_context_name(
    ::std::string* context_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (context_name != NULL) {
    
  } else {
    
  }
  context_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      context_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.WhileContextDef.context_name)
}

// int32 parallel_iterations = 2;
inline void WhileContextDef::clear_parallel_iterations() {
  parallel_iterations_ = 0;
}
inline ::google::protobuf::int32 WhileContextDef::parallel_iterations() const {
  // @@protoc_insertion_point(field_get:tensorflow.WhileContextDef.parallel_iterations)
  return parallel_iterations_;
}
inline void WhileContextDef::set_parallel_iterations(::google::protobuf::int32 value) {
  
  parallel_iterations_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.WhileContextDef.parallel_iterations)
}

// bool back_prop = 3;
inline void WhileContextDef::clear_back_prop() {
  back_prop_ = false;
}
inline bool WhileContextDef::back_prop() const {
  // @@protoc_insertion_point(field_get:tensorflow.WhileContextDef.back_prop)
  return back_prop_;
}
inline void WhileContextDef::set_back_prop(bool value) {
  
  back_prop_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.WhileContextDef.back_prop)
}

// bool swap_memory = 4;
inline void WhileContextDef::clear_swap_memory() {
  swap_memory_ = false;
}
inline bool WhileContextDef::swap_memory() const {
  // @@protoc_insertion_point(field_get:tensorflow.WhileContextDef.swap_memory)
  return swap_memory_;
}
inline void WhileContextDef::set_swap_memory(bool value) {
  
  swap_memory_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.WhileContextDef.swap_memory)
}

// string pivot_name = 5;
inline void WhileContextDef::clear_pivot_name() {
  pivot_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& WhileContextDef::pivot_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.WhileContextDef.pivot_name)
  return pivot_name_.Get();
}
inline void WhileContextDef::set_pivot_name(const ::std::string& value) {
  
  pivot_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.WhileContextDef.pivot_name)
}
#if LANG_CXX11
inline void WhileContextDef::set_pivot_name(::std::string&& value) {
  
  pivot_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.WhileContextDef.pivot_name)
}
#endif
inline void WhileContextDef::set_pivot_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  pivot_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.WhileContextDef.pivot_name)
}
inline void WhileContextDef::set_pivot_name(const char* value,
    size_t size) {
  
  pivot_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.WhileContextDef.pivot_name)
}
inline ::std::string* WhileContextDef::mutable_pivot_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.WhileContextDef.pivot_name)
  return pivot_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* WhileContextDef::release_pivot_name() {
  // @@protoc_insertion_point(field_release:tensorflow.WhileContextDef.pivot_name)
  
  return pivot_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void WhileContextDef::set_allocated_pivot_name(::std::string* pivot_name) {
  if (pivot_name != NULL) {
    
  } else {
    
  }
  pivot_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), pivot_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.WhileContextDef.pivot_name)
}
inline ::std::string* WhileContextDef::unsafe_arena_release_pivot_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.WhileContextDef.pivot_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return pivot_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void WhileContextDef::unsafe_arena_set_allocated_pivot_name(
    ::std::string* pivot_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (pivot_name != NULL) {
    
  } else {
    
  }
  pivot_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      pivot_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.WhileContextDef.pivot_name)
}

// string pivot_for_pred_name = 6;
inline void WhileContextDef::clear_pivot_for_pred_name() {
  pivot_for_pred_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& WhileContextDef::pivot_for_pred_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.WhileContextDef.pivot_for_pred_name)
  return pivot_for_pred_name_.Get();
}
inline void WhileContextDef::set_pivot_for_pred_name(const ::std::string& value) {
  
  pivot_for_pred_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.WhileContextDef.pivot_for_pred_name)
}
#if LANG_CXX11
inline void WhileContextDef::set_pivot_for_pred_name(::std::string&& value) {
  
  pivot_for_pred_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.WhileContextDef.pivot_for_pred_name)
}
#endif
inline void WhileContextDef::set_pivot_for_pred_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  pivot_for_pred_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.WhileContextDef.pivot_for_pred_name)
}
inline void WhileContextDef::set_pivot_for_pred_name(const char* value,
    size_t size) {
  
  pivot_for_pred_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.WhileContextDef.pivot_for_pred_name)
}
inline ::std::string* WhileContextDef::mutable_pivot_for_pred_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.WhileContextDef.pivot_for_pred_name)
  return pivot_for_pred_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* WhileContextDef::release_pivot_for_pred_name() {
  // @@protoc_insertion_point(field_release:tensorflow.WhileContextDef.pivot_for_pred_name)
  
  return pivot_for_pred_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void WhileContextDef::set_allocated_pivot_for_pred_name(::std::string* pivot_for_pred_name) {
  if (pivot_for_pred_name != NULL) {
    
  } else {
    
  }
  pivot_for_pred_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), pivot_for_pred_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.WhileContextDef.pivot_for_pred_name)
}
inline ::std::string* WhileContextDef::unsafe_arena_release_pivot_for_pred_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.WhileContextDef.pivot_for_pred_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return pivot_for_pred_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void WhileContextDef::unsafe_arena_set_allocated_pivot_for_pred_name(
    ::std::string* pivot_for_pred_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (pivot_for_pred_name != NULL) {
    
  } else {
    
  }
  pivot_for_pred_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      pivot_for_pred_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.WhileContextDef.pivot_for_pred_name)
}

// string pivot_for_body_name = 7;
inline void WhileContextDef::clear_pivot_for_body_name() {
  pivot_for_body_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& WhileContextDef::pivot_for_body_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.WhileContextDef.pivot_for_body_name)
  return pivot_for_body_name_.Get();
}
inline void WhileContextDef::set_pivot_for_body_name(const ::std::string& value) {
  
  pivot_for_body_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.WhileContextDef.pivot_for_body_name)
}
#if LANG_CXX11
inline void WhileContextDef::set_pivot_for_body_name(::std::string&& value) {
  
  pivot_for_body_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.WhileContextDef.pivot_for_body_name)
}
#endif
inline void WhileContextDef::set_pivot_for_body_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  pivot_for_body_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.WhileContextDef.pivot_for_body_name)
}
inline void WhileContextDef::set_pivot_for_body_name(const char* value,
    size_t size) {
  
  pivot_for_body_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.WhileContextDef.pivot_for_body_name)
}
inline ::std::string* WhileContextDef::mutable_pivot_for_body_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.WhileContextDef.pivot_for_body_name)
  return pivot_for_body_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* WhileContextDef::release_pivot_for_body_name() {
  // @@protoc_insertion_point(field_release:tensorflow.WhileContextDef.pivot_for_body_name)
  
  return pivot_for_body_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void WhileContextDef::set_allocated_pivot_for_body_name(::std::string* pivot_for_body_name) {
  if (pivot_for_body_name != NULL) {
    
  } else {
    
  }
  pivot_for_body_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), pivot_for_body_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.WhileContextDef.pivot_for_body_name)
}
inline ::std::string* WhileContextDef::unsafe_arena_release_pivot_for_body_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.WhileContextDef.pivot_for_body_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return pivot_for_body_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void WhileContextDef::unsafe_arena_set_allocated_pivot_for_body_name(
    ::std::string* pivot_for_body_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (pivot_for_body_name != NULL) {
    
  } else {
    
  }
  pivot_for_body_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      pivot_for_body_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.WhileContextDef.pivot_for_body_name)
}

// repeated string loop_exit_names = 8;
inline int WhileContextDef::loop_exit_names_size() const {
  return loop_exit_names_.size();
}
inline void WhileContextDef::clear_loop_exit_names() {
  loop_exit_names_.Clear();
}
inline const ::std::string& WhileContextDef::loop_exit_names(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.WhileContextDef.loop_exit_names)
  return loop_exit_names_.Get(index);
}
inline ::std::string* WhileContextDef::mutable_loop_exit_names(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.WhileContextDef.loop_exit_names)
  return loop_exit_names_.Mutable(index);
}
inline void WhileContextDef::set_loop_exit_names(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.WhileContextDef.loop_exit_names)
  loop_exit_names_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void WhileContextDef::set_loop_exit_names(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.WhileContextDef.loop_exit_names)
  loop_exit_names_.Mutable(index)->assign(std::move(value));
}
#endif
inline void WhileContextDef::set_loop_exit_names(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  loop_exit_names_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.WhileContextDef.loop_exit_names)
}
inline void WhileContextDef::set_loop_exit_names(int index, const char* value, size_t size) {
  loop_exit_names_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.WhileContextDef.loop_exit_names)
}
inline ::std::string* WhileContextDef::add_loop_exit_names() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.WhileContextDef.loop_exit_names)
  return loop_exit_names_.Add();
}
inline void WhileContextDef::add_loop_exit_names(const ::std::string& value) {
  loop_exit_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.WhileContextDef.loop_exit_names)
}
#if LANG_CXX11
inline void WhileContextDef::add_loop_exit_names(::std::string&& value) {
  loop_exit_names_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.WhileContextDef.loop_exit_names)
}
#endif
inline void WhileContextDef::add_loop_exit_names(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  loop_exit_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.WhileContextDef.loop_exit_names)
}
inline void WhileContextDef::add_loop_exit_names(const char* value, size_t size) {
  loop_exit_names_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.WhileContextDef.loop_exit_names)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
WhileContextDef::loop_exit_names() const {
  // @@protoc_insertion_point(field_list:tensorflow.WhileContextDef.loop_exit_names)
  return loop_exit_names_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
WhileContextDef::mutable_loop_exit_names() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.WhileContextDef.loop_exit_names)
  return &loop_exit_names_;
}

// repeated string loop_enter_names = 10;
inline int WhileContextDef::loop_enter_names_size() const {
  return loop_enter_names_.size();
}
inline void WhileContextDef::clear_loop_enter_names() {
  loop_enter_names_.Clear();
}
inline const ::std::string& WhileContextDef::loop_enter_names(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.WhileContextDef.loop_enter_names)
  return loop_enter_names_.Get(index);
}
inline ::std::string* WhileContextDef::mutable_loop_enter_names(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.WhileContextDef.loop_enter_names)
  return loop_enter_names_.Mutable(index);
}
inline void WhileContextDef::set_loop_enter_names(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.WhileContextDef.loop_enter_names)
  loop_enter_names_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void WhileContextDef::set_loop_enter_names(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.WhileContextDef.loop_enter_names)
  loop_enter_names_.Mutable(index)->assign(std::move(value));
}
#endif
inline void WhileContextDef::set_loop_enter_names(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  loop_enter_names_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.WhileContextDef.loop_enter_names)
}
inline void WhileContextDef::set_loop_enter_names(int index, const char* value, size_t size) {
  loop_enter_names_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.WhileContextDef.loop_enter_names)
}
inline ::std::string* WhileContextDef::add_loop_enter_names() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.WhileContextDef.loop_enter_names)
  return loop_enter_names_.Add();
}
inline void WhileContextDef::add_loop_enter_names(const ::std::string& value) {
  loop_enter_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.WhileContextDef.loop_enter_names)
}
#if LANG_CXX11
inline void WhileContextDef::add_loop_enter_names(::std::string&& value) {
  loop_enter_names_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.WhileContextDef.loop_enter_names)
}
#endif
inline void WhileContextDef::add_loop_enter_names(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  loop_enter_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.WhileContextDef.loop_enter_names)
}
inline void WhileContextDef::add_loop_enter_names(const char* value, size_t size) {
  loop_enter_names_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.WhileContextDef.loop_enter_names)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
WhileContextDef::loop_enter_names() const {
  // @@protoc_insertion_point(field_list:tensorflow.WhileContextDef.loop_enter_names)
  return loop_enter_names_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
WhileContextDef::mutable_loop_enter_names() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.WhileContextDef.loop_enter_names)
  return &loop_enter_names_;
}

// .tensorflow.ValuesDef values_def = 9;
inline bool WhileContextDef::has_values_def() const {
  return this != internal_default_instance() && values_def_ != NULL;
}
inline void WhileContextDef::clear_values_def() {
  if (GetArenaNoVirtual() == NULL && values_def_ != NULL) {
    delete values_def_;
  }
  values_def_ = NULL;
}
inline const ::tensorflow::ValuesDef& WhileContextDef::_internal_values_def() const {
  return *values_def_;
}
inline const ::tensorflow::ValuesDef& WhileContextDef::values_def() const {
  const ::tensorflow::ValuesDef* p = values_def_;
  // @@protoc_insertion_point(field_get:tensorflow.WhileContextDef.values_def)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::ValuesDef*>(
      &::tensorflow::_ValuesDef_default_instance_);
}
inline ::tensorflow::ValuesDef* WhileContextDef::release_values_def() {
  // @@protoc_insertion_point(field_release:tensorflow.WhileContextDef.values_def)
  
  ::tensorflow::ValuesDef* temp = values_def_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  values_def_ = NULL;
  return temp;
}
inline ::tensorflow::ValuesDef* WhileContextDef::unsafe_arena_release_values_def() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.WhileContextDef.values_def)
  
  ::tensorflow::ValuesDef* temp = values_def_;
  values_def_ = NULL;
  return temp;
}
inline ::tensorflow::ValuesDef* WhileContextDef::mutable_values_def() {
  
  if (values_def_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::ValuesDef>(GetArenaNoVirtual());
    values_def_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.WhileContextDef.values_def)
  return values_def_;
}
inline void WhileContextDef::set_allocated_values_def(::tensorflow::ValuesDef* values_def) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete values_def_;
  }
  if (values_def) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(values_def);
    if (message_arena != submessage_arena) {
      values_def = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, values_def, submessage_arena);
    }
    
  } else {
    
  }
  values_def_ = values_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.WhileContextDef.values_def)
}

// string maximum_iterations_name = 11;
inline void WhileContextDef::clear_maximum_iterations_name() {
  maximum_iterations_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& WhileContextDef::maximum_iterations_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.WhileContextDef.maximum_iterations_name)
  return maximum_iterations_name_.Get();
}
inline void WhileContextDef::set_maximum_iterations_name(const ::std::string& value) {
  
  maximum_iterations_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.WhileContextDef.maximum_iterations_name)
}
#if LANG_CXX11
inline void WhileContextDef::set_maximum_iterations_name(::std::string&& value) {
  
  maximum_iterations_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.WhileContextDef.maximum_iterations_name)
}
#endif
inline void WhileContextDef::set_maximum_iterations_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  maximum_iterations_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.WhileContextDef.maximum_iterations_name)
}
inline void WhileContextDef::set_maximum_iterations_name(const char* value,
    size_t size) {
  
  maximum_iterations_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.WhileContextDef.maximum_iterations_name)
}
inline ::std::string* WhileContextDef::mutable_maximum_iterations_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.WhileContextDef.maximum_iterations_name)
  return maximum_iterations_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* WhileContextDef::release_maximum_iterations_name() {
  // @@protoc_insertion_point(field_release:tensorflow.WhileContextDef.maximum_iterations_name)
  
  return maximum_iterations_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void WhileContextDef::set_allocated_maximum_iterations_name(::std::string* maximum_iterations_name) {
  if (maximum_iterations_name != NULL) {
    
  } else {
    
  }
  maximum_iterations_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), maximum_iterations_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.WhileContextDef.maximum_iterations_name)
}
inline ::std::string* WhileContextDef::unsafe_arena_release_maximum_iterations_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.WhileContextDef.maximum_iterations_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return maximum_iterations_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void WhileContextDef::unsafe_arena_set_allocated_maximum_iterations_name(
    ::std::string* maximum_iterations_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (maximum_iterations_name != NULL) {
    
  } else {
    
  }
  maximum_iterations_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      maximum_iterations_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.WhileContextDef.maximum_iterations_name)
}

// repeated .tensorflow.ControlFlowContextDef nested_contexts = 12;
inline int WhileContextDef::nested_contexts_size() const {
  return nested_contexts_.size();
}
inline void WhileContextDef::clear_nested_contexts() {
  nested_contexts_.Clear();
}
inline ::tensorflow::ControlFlowContextDef* WhileContextDef::mutable_nested_contexts(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.WhileContextDef.nested_contexts)
  return nested_contexts_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::ControlFlowContextDef >*
WhileContextDef::mutable_nested_contexts() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.WhileContextDef.nested_contexts)
  return &nested_contexts_;
}
inline const ::tensorflow::ControlFlowContextDef& WhileContextDef::nested_contexts(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.WhileContextDef.nested_contexts)
  return nested_contexts_.Get(index);
}
inline ::tensorflow::ControlFlowContextDef* WhileContextDef::add_nested_contexts() {
  // @@protoc_insertion_point(field_add:tensorflow.WhileContextDef.nested_contexts)
  return nested_contexts_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::ControlFlowContextDef >&
WhileContextDef::nested_contexts() const {
  // @@protoc_insertion_point(field_list:tensorflow.WhileContextDef.nested_contexts)
  return nested_contexts_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto

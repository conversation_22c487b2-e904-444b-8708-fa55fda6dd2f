// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/tpu/tpu_embedding_configuration.proto

#include "tensorflow/core/protobuf/tpu/tpu_embedding_configuration.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto ::google::protobuf::internal::SCCInfo<13> scc_info_OptimizationParameters;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto
namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_TPUEmbeddingConfiguration_TableDescriptor;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto
namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_TPUEmbeddingOutputLayout;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto
namespace tensorflow {
namespace tpu {
class TPUEmbeddingConfiguration_TableDescriptorDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TPUEmbeddingConfiguration_TableDescriptor>
      _instance;
} _TPUEmbeddingConfiguration_TableDescriptor_default_instance_;
class TPUEmbeddingConfigurationDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TPUEmbeddingConfiguration>
      _instance;
} _TPUEmbeddingConfiguration_default_instance_;
}  // namespace tpu
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto {
static void InitDefaultsTPUEmbeddingConfiguration_TableDescriptor() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tpu::_TPUEmbeddingConfiguration_TableDescriptor_default_instance_;
    new (ptr) ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_TPUEmbeddingConfiguration_TableDescriptor =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsTPUEmbeddingConfiguration_TableDescriptor}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_OptimizationParameters.base,}};

static void InitDefaultsTPUEmbeddingConfiguration() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tpu::_TPUEmbeddingConfiguration_default_instance_;
    new (ptr) ::tensorflow::tpu::TPUEmbeddingConfiguration();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tpu::TPUEmbeddingConfiguration::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_TPUEmbeddingConfiguration =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsTPUEmbeddingConfiguration}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto::scc_info_TPUEmbeddingConfiguration_TableDescriptor.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::scc_info_TPUEmbeddingOutputLayout.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_TPUEmbeddingConfiguration_TableDescriptor.base);
  ::google::protobuf::internal::InitSCC(&scc_info_TPUEmbeddingConfiguration.base);
}

::google::protobuf::Metadata file_level_metadata[2];
const ::google::protobuf::EnumDescriptor* file_level_enum_descriptors[2];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor, vocabulary_size_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor, dimension_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor, num_features_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor, optimization_parameters_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingConfiguration, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingConfiguration, table_descriptor_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingConfiguration, mode_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingConfiguration, batch_size_per_tensor_core_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingConfiguration, num_hosts_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingConfiguration, num_tensor_cores_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingConfiguration, sharding_strategy_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingConfiguration, pipeline_execution_with_tensor_core_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingConfiguration, output_layout_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor)},
  { 10, -1, sizeof(::tensorflow::tpu::TPUEmbeddingConfiguration)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tpu::_TPUEmbeddingConfiguration_TableDescriptor_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tpu::_TPUEmbeddingConfiguration_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/protobuf/tpu/tpu_embedding_configuration.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, file_level_enum_descriptors, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 2);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n>tensorflow/core/protobuf/tpu/tpu_embed"
      "ding_configuration.proto\022\016tensorflow.tpu"
      "\032:tensorflow/core/protobuf/tpu/optimizat"
      "ion_parameters.proto\032>tensorflow/core/pr"
      "otobuf/tpu/tpu_embedding_output_layout.p"
      "roto\"\355\005\n\031TPUEmbeddingConfiguration\022S\n\020ta"
      "ble_descriptor\030\001 \003(\01329.tensorflow.tpu.TP"
      "UEmbeddingConfiguration.TableDescriptor\022"
      "<\n\004mode\030\002 \001(\0162..tensorflow.tpu.TPUEmbedd"
      "ingConfiguration.Mode\022\"\n\032batch_size_per_"
      "tensor_core\030\003 \001(\005\022\021\n\tnum_hosts\030\004 \001(\005\022\030\n\020"
      "num_tensor_cores\030\005 \001(\005\022U\n\021sharding_strat"
      "egy\030\006 \001(\0162:.tensorflow.tpu.TPUEmbeddingC"
      "onfiguration.ShardingStrategy\022+\n#pipelin"
      "e_execution_with_tensor_core\030\007 \001(\010\022\?\n\rou"
      "tput_layout\030\010 \001(\0132(.tensorflow.tpu.TPUEm"
      "beddingOutputLayout\032\252\001\n\017TableDescriptor\022"
      "\014\n\004name\030\001 \001(\t\022\027\n\017vocabulary_size\030\002 \001(\005\022\021"
      "\n\tdimension\030\003 \001(\005\022\024\n\014num_features\030\004 \001(\005\022"
      "G\n\027optimization_parameters\030\005 \001(\0132&.tenso"
      "rflow.tpu.OptimizationParameters\"L\n\004Mode"
      "\022\017\n\013UNSPECIFIED\020\000\022\r\n\tINFERENCE\020\001\022\014\n\010TRAI"
      "NING\020\002\022\026\n\022BACKWARD_PASS_ONLY\020\003\",\n\020Shardi"
      "ngStrategy\022\017\n\013DIV_DEFAULT\020\000\022\007\n\003MOD\020\001b\006pr"
      "oto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 964);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/protobuf/tpu/tpu_embedding_configuration.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto
namespace tensorflow {
namespace tpu {
const ::google::protobuf::EnumDescriptor* TPUEmbeddingConfiguration_Mode_descriptor() {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto::file_level_enum_descriptors[0];
}
bool TPUEmbeddingConfiguration_Mode_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const TPUEmbeddingConfiguration_Mode TPUEmbeddingConfiguration::UNSPECIFIED;
const TPUEmbeddingConfiguration_Mode TPUEmbeddingConfiguration::INFERENCE;
const TPUEmbeddingConfiguration_Mode TPUEmbeddingConfiguration::TRAINING;
const TPUEmbeddingConfiguration_Mode TPUEmbeddingConfiguration::BACKWARD_PASS_ONLY;
const TPUEmbeddingConfiguration_Mode TPUEmbeddingConfiguration::Mode_MIN;
const TPUEmbeddingConfiguration_Mode TPUEmbeddingConfiguration::Mode_MAX;
const int TPUEmbeddingConfiguration::Mode_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900
const ::google::protobuf::EnumDescriptor* TPUEmbeddingConfiguration_ShardingStrategy_descriptor() {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto::file_level_enum_descriptors[1];
}
bool TPUEmbeddingConfiguration_ShardingStrategy_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const TPUEmbeddingConfiguration_ShardingStrategy TPUEmbeddingConfiguration::DIV_DEFAULT;
const TPUEmbeddingConfiguration_ShardingStrategy TPUEmbeddingConfiguration::MOD;
const TPUEmbeddingConfiguration_ShardingStrategy TPUEmbeddingConfiguration::ShardingStrategy_MIN;
const TPUEmbeddingConfiguration_ShardingStrategy TPUEmbeddingConfiguration::ShardingStrategy_MAX;
const int TPUEmbeddingConfiguration::ShardingStrategy_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

// ===================================================================

void TPUEmbeddingConfiguration_TableDescriptor::InitAsDefaultInstance() {
  ::tensorflow::tpu::_TPUEmbeddingConfiguration_TableDescriptor_default_instance_._instance.get_mutable()->optimization_parameters_ = const_cast< ::tensorflow::tpu::OptimizationParameters*>(
      ::tensorflow::tpu::OptimizationParameters::internal_default_instance());
}
void TPUEmbeddingConfiguration_TableDescriptor::clear_optimization_parameters() {
  if (GetArenaNoVirtual() == NULL && optimization_parameters_ != NULL) {
    delete optimization_parameters_;
  }
  optimization_parameters_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TPUEmbeddingConfiguration_TableDescriptor::kNameFieldNumber;
const int TPUEmbeddingConfiguration_TableDescriptor::kVocabularySizeFieldNumber;
const int TPUEmbeddingConfiguration_TableDescriptor::kDimensionFieldNumber;
const int TPUEmbeddingConfiguration_TableDescriptor::kNumFeaturesFieldNumber;
const int TPUEmbeddingConfiguration_TableDescriptor::kOptimizationParametersFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TPUEmbeddingConfiguration_TableDescriptor::TPUEmbeddingConfiguration_TableDescriptor()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto::scc_info_TPUEmbeddingConfiguration_TableDescriptor.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor)
}
TPUEmbeddingConfiguration_TableDescriptor::TPUEmbeddingConfiguration_TableDescriptor(const TPUEmbeddingConfiguration_TableDescriptor& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  if (from.has_optimization_parameters()) {
    optimization_parameters_ = new ::tensorflow::tpu::OptimizationParameters(*from.optimization_parameters_);
  } else {
    optimization_parameters_ = NULL;
  }
  ::memcpy(&vocabulary_size_, &from.vocabulary_size_,
    static_cast<size_t>(reinterpret_cast<char*>(&num_features_) -
    reinterpret_cast<char*>(&vocabulary_size_)) + sizeof(num_features_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor)
}

void TPUEmbeddingConfiguration_TableDescriptor::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&optimization_parameters_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&num_features_) -
      reinterpret_cast<char*>(&optimization_parameters_)) + sizeof(num_features_));
}

TPUEmbeddingConfiguration_TableDescriptor::~TPUEmbeddingConfiguration_TableDescriptor() {
  // @@protoc_insertion_point(destructor:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor)
  SharedDtor();
}

void TPUEmbeddingConfiguration_TableDescriptor::SharedDtor() {
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete optimization_parameters_;
}

void TPUEmbeddingConfiguration_TableDescriptor::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* TPUEmbeddingConfiguration_TableDescriptor::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const TPUEmbeddingConfiguration_TableDescriptor& TPUEmbeddingConfiguration_TableDescriptor::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto::scc_info_TPUEmbeddingConfiguration_TableDescriptor.base);
  return *internal_default_instance();
}


void TPUEmbeddingConfiguration_TableDescriptor::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && optimization_parameters_ != NULL) {
    delete optimization_parameters_;
  }
  optimization_parameters_ = NULL;
  ::memset(&vocabulary_size_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&num_features_) -
      reinterpret_cast<char*>(&vocabulary_size_)) + sizeof(num_features_));
  _internal_metadata_.Clear();
}

bool TPUEmbeddingConfiguration_TableDescriptor::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 vocabulary_size = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &vocabulary_size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 dimension = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &dimension_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 num_features = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &num_features_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.tpu.OptimizationParameters optimization_parameters = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_optimization_parameters()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor)
  return false;
#undef DO_
}

void TPUEmbeddingConfiguration_TableDescriptor::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // int32 vocabulary_size = 2;
  if (this->vocabulary_size() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->vocabulary_size(), output);
  }

  // int32 dimension = 3;
  if (this->dimension() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->dimension(), output);
  }

  // int32 num_features = 4;
  if (this->num_features() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->num_features(), output);
  }

  // .tensorflow.tpu.OptimizationParameters optimization_parameters = 5;
  if (this->has_optimization_parameters()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, this->_internal_optimization_parameters(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor)
}

::google::protobuf::uint8* TPUEmbeddingConfiguration_TableDescriptor::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // int32 vocabulary_size = 2;
  if (this->vocabulary_size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->vocabulary_size(), target);
  }

  // int32 dimension = 3;
  if (this->dimension() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->dimension(), target);
  }

  // int32 num_features = 4;
  if (this->num_features() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->num_features(), target);
  }

  // .tensorflow.tpu.OptimizationParameters optimization_parameters = 5;
  if (this->has_optimization_parameters()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        5, this->_internal_optimization_parameters(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor)
  return target;
}

size_t TPUEmbeddingConfiguration_TableDescriptor::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // .tensorflow.tpu.OptimizationParameters optimization_parameters = 5;
  if (this->has_optimization_parameters()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *optimization_parameters_);
  }

  // int32 vocabulary_size = 2;
  if (this->vocabulary_size() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->vocabulary_size());
  }

  // int32 dimension = 3;
  if (this->dimension() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->dimension());
  }

  // int32 num_features = 4;
  if (this->num_features() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->num_features());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TPUEmbeddingConfiguration_TableDescriptor::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor)
  GOOGLE_DCHECK_NE(&from, this);
  const TPUEmbeddingConfiguration_TableDescriptor* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TPUEmbeddingConfiguration_TableDescriptor>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor)
    MergeFrom(*source);
  }
}

void TPUEmbeddingConfiguration_TableDescriptor::MergeFrom(const TPUEmbeddingConfiguration_TableDescriptor& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.name().size() > 0) {

    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  if (from.has_optimization_parameters()) {
    mutable_optimization_parameters()->::tensorflow::tpu::OptimizationParameters::MergeFrom(from.optimization_parameters());
  }
  if (from.vocabulary_size() != 0) {
    set_vocabulary_size(from.vocabulary_size());
  }
  if (from.dimension() != 0) {
    set_dimension(from.dimension());
  }
  if (from.num_features() != 0) {
    set_num_features(from.num_features());
  }
}

void TPUEmbeddingConfiguration_TableDescriptor::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TPUEmbeddingConfiguration_TableDescriptor::CopyFrom(const TPUEmbeddingConfiguration_TableDescriptor& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TPUEmbeddingConfiguration_TableDescriptor::IsInitialized() const {
  return true;
}

void TPUEmbeddingConfiguration_TableDescriptor::Swap(TPUEmbeddingConfiguration_TableDescriptor* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TPUEmbeddingConfiguration_TableDescriptor::InternalSwap(TPUEmbeddingConfiguration_TableDescriptor* other) {
  using std::swap;
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(optimization_parameters_, other->optimization_parameters_);
  swap(vocabulary_size_, other->vocabulary_size_);
  swap(dimension_, other->dimension_);
  swap(num_features_, other->num_features_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata TPUEmbeddingConfiguration_TableDescriptor::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void TPUEmbeddingConfiguration::InitAsDefaultInstance() {
  ::tensorflow::tpu::_TPUEmbeddingConfiguration_default_instance_._instance.get_mutable()->output_layout_ = const_cast< ::tensorflow::tpu::TPUEmbeddingOutputLayout*>(
      ::tensorflow::tpu::TPUEmbeddingOutputLayout::internal_default_instance());
}
void TPUEmbeddingConfiguration::clear_output_layout() {
  if (GetArenaNoVirtual() == NULL && output_layout_ != NULL) {
    delete output_layout_;
  }
  output_layout_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TPUEmbeddingConfiguration::kTableDescriptorFieldNumber;
const int TPUEmbeddingConfiguration::kModeFieldNumber;
const int TPUEmbeddingConfiguration::kBatchSizePerTensorCoreFieldNumber;
const int TPUEmbeddingConfiguration::kNumHostsFieldNumber;
const int TPUEmbeddingConfiguration::kNumTensorCoresFieldNumber;
const int TPUEmbeddingConfiguration::kShardingStrategyFieldNumber;
const int TPUEmbeddingConfiguration::kPipelineExecutionWithTensorCoreFieldNumber;
const int TPUEmbeddingConfiguration::kOutputLayoutFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TPUEmbeddingConfiguration::TPUEmbeddingConfiguration()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto::scc_info_TPUEmbeddingConfiguration.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tpu.TPUEmbeddingConfiguration)
}
TPUEmbeddingConfiguration::TPUEmbeddingConfiguration(const TPUEmbeddingConfiguration& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      table_descriptor_(from.table_descriptor_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_output_layout()) {
    output_layout_ = new ::tensorflow::tpu::TPUEmbeddingOutputLayout(*from.output_layout_);
  } else {
    output_layout_ = NULL;
  }
  ::memcpy(&mode_, &from.mode_,
    static_cast<size_t>(reinterpret_cast<char*>(&pipeline_execution_with_tensor_core_) -
    reinterpret_cast<char*>(&mode_)) + sizeof(pipeline_execution_with_tensor_core_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.tpu.TPUEmbeddingConfiguration)
}

void TPUEmbeddingConfiguration::SharedCtor() {
  ::memset(&output_layout_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&pipeline_execution_with_tensor_core_) -
      reinterpret_cast<char*>(&output_layout_)) + sizeof(pipeline_execution_with_tensor_core_));
}

TPUEmbeddingConfiguration::~TPUEmbeddingConfiguration() {
  // @@protoc_insertion_point(destructor:tensorflow.tpu.TPUEmbeddingConfiguration)
  SharedDtor();
}

void TPUEmbeddingConfiguration::SharedDtor() {
  if (this != internal_default_instance()) delete output_layout_;
}

void TPUEmbeddingConfiguration::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* TPUEmbeddingConfiguration::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const TPUEmbeddingConfiguration& TPUEmbeddingConfiguration::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto::scc_info_TPUEmbeddingConfiguration.base);
  return *internal_default_instance();
}


void TPUEmbeddingConfiguration::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tpu.TPUEmbeddingConfiguration)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  table_descriptor_.Clear();
  if (GetArenaNoVirtual() == NULL && output_layout_ != NULL) {
    delete output_layout_;
  }
  output_layout_ = NULL;
  ::memset(&mode_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&pipeline_execution_with_tensor_core_) -
      reinterpret_cast<char*>(&mode_)) + sizeof(pipeline_execution_with_tensor_core_));
  _internal_metadata_.Clear();
}

bool TPUEmbeddingConfiguration::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tpu.TPUEmbeddingConfiguration)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor table_descriptor = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_table_descriptor()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.tpu.TPUEmbeddingConfiguration.Mode mode = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_mode(static_cast< ::tensorflow::tpu::TPUEmbeddingConfiguration_Mode >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 batch_size_per_tensor_core = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &batch_size_per_tensor_core_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 num_hosts = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &num_hosts_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 num_tensor_cores = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &num_tensor_cores_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.tpu.TPUEmbeddingConfiguration.ShardingStrategy sharding_strategy = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(48u /* 48 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_sharding_strategy(static_cast< ::tensorflow::tpu::TPUEmbeddingConfiguration_ShardingStrategy >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool pipeline_execution_with_tensor_core = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(56u /* 56 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &pipeline_execution_with_tensor_core_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.tpu.TPUEmbeddingOutputLayout output_layout = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(66u /* 66 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_output_layout()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tpu.TPUEmbeddingConfiguration)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tpu.TPUEmbeddingConfiguration)
  return false;
#undef DO_
}

void TPUEmbeddingConfiguration::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tpu.TPUEmbeddingConfiguration)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor table_descriptor = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->table_descriptor_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->table_descriptor(static_cast<int>(i)),
      output);
  }

  // .tensorflow.tpu.TPUEmbeddingConfiguration.Mode mode = 2;
  if (this->mode() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      2, this->mode(), output);
  }

  // int32 batch_size_per_tensor_core = 3;
  if (this->batch_size_per_tensor_core() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->batch_size_per_tensor_core(), output);
  }

  // int32 num_hosts = 4;
  if (this->num_hosts() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->num_hosts(), output);
  }

  // int32 num_tensor_cores = 5;
  if (this->num_tensor_cores() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(5, this->num_tensor_cores(), output);
  }

  // .tensorflow.tpu.TPUEmbeddingConfiguration.ShardingStrategy sharding_strategy = 6;
  if (this->sharding_strategy() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->sharding_strategy(), output);
  }

  // bool pipeline_execution_with_tensor_core = 7;
  if (this->pipeline_execution_with_tensor_core() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(7, this->pipeline_execution_with_tensor_core(), output);
  }

  // .tensorflow.tpu.TPUEmbeddingOutputLayout output_layout = 8;
  if (this->has_output_layout()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8, this->_internal_output_layout(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tpu.TPUEmbeddingConfiguration)
}

::google::protobuf::uint8* TPUEmbeddingConfiguration::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tpu.TPUEmbeddingConfiguration)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor table_descriptor = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->table_descriptor_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->table_descriptor(static_cast<int>(i)), deterministic, target);
  }

  // .tensorflow.tpu.TPUEmbeddingConfiguration.Mode mode = 2;
  if (this->mode() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      2, this->mode(), target);
  }

  // int32 batch_size_per_tensor_core = 3;
  if (this->batch_size_per_tensor_core() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->batch_size_per_tensor_core(), target);
  }

  // int32 num_hosts = 4;
  if (this->num_hosts() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->num_hosts(), target);
  }

  // int32 num_tensor_cores = 5;
  if (this->num_tensor_cores() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(5, this->num_tensor_cores(), target);
  }

  // .tensorflow.tpu.TPUEmbeddingConfiguration.ShardingStrategy sharding_strategy = 6;
  if (this->sharding_strategy() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->sharding_strategy(), target);
  }

  // bool pipeline_execution_with_tensor_core = 7;
  if (this->pipeline_execution_with_tensor_core() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(7, this->pipeline_execution_with_tensor_core(), target);
  }

  // .tensorflow.tpu.TPUEmbeddingOutputLayout output_layout = 8;
  if (this->has_output_layout()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        8, this->_internal_output_layout(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tpu.TPUEmbeddingConfiguration)
  return target;
}

size_t TPUEmbeddingConfiguration::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tpu.TPUEmbeddingConfiguration)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor table_descriptor = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->table_descriptor_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->table_descriptor(static_cast<int>(i)));
    }
  }

  // .tensorflow.tpu.TPUEmbeddingOutputLayout output_layout = 8;
  if (this->has_output_layout()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *output_layout_);
  }

  // .tensorflow.tpu.TPUEmbeddingConfiguration.Mode mode = 2;
  if (this->mode() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->mode());
  }

  // int32 batch_size_per_tensor_core = 3;
  if (this->batch_size_per_tensor_core() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->batch_size_per_tensor_core());
  }

  // int32 num_hosts = 4;
  if (this->num_hosts() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->num_hosts());
  }

  // int32 num_tensor_cores = 5;
  if (this->num_tensor_cores() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->num_tensor_cores());
  }

  // .tensorflow.tpu.TPUEmbeddingConfiguration.ShardingStrategy sharding_strategy = 6;
  if (this->sharding_strategy() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->sharding_strategy());
  }

  // bool pipeline_execution_with_tensor_core = 7;
  if (this->pipeline_execution_with_tensor_core() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TPUEmbeddingConfiguration::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tpu.TPUEmbeddingConfiguration)
  GOOGLE_DCHECK_NE(&from, this);
  const TPUEmbeddingConfiguration* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TPUEmbeddingConfiguration>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tpu.TPUEmbeddingConfiguration)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tpu.TPUEmbeddingConfiguration)
    MergeFrom(*source);
  }
}

void TPUEmbeddingConfiguration::MergeFrom(const TPUEmbeddingConfiguration& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tpu.TPUEmbeddingConfiguration)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  table_descriptor_.MergeFrom(from.table_descriptor_);
  if (from.has_output_layout()) {
    mutable_output_layout()->::tensorflow::tpu::TPUEmbeddingOutputLayout::MergeFrom(from.output_layout());
  }
  if (from.mode() != 0) {
    set_mode(from.mode());
  }
  if (from.batch_size_per_tensor_core() != 0) {
    set_batch_size_per_tensor_core(from.batch_size_per_tensor_core());
  }
  if (from.num_hosts() != 0) {
    set_num_hosts(from.num_hosts());
  }
  if (from.num_tensor_cores() != 0) {
    set_num_tensor_cores(from.num_tensor_cores());
  }
  if (from.sharding_strategy() != 0) {
    set_sharding_strategy(from.sharding_strategy());
  }
  if (from.pipeline_execution_with_tensor_core() != 0) {
    set_pipeline_execution_with_tensor_core(from.pipeline_execution_with_tensor_core());
  }
}

void TPUEmbeddingConfiguration::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tpu.TPUEmbeddingConfiguration)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TPUEmbeddingConfiguration::CopyFrom(const TPUEmbeddingConfiguration& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tpu.TPUEmbeddingConfiguration)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TPUEmbeddingConfiguration::IsInitialized() const {
  return true;
}

void TPUEmbeddingConfiguration::Swap(TPUEmbeddingConfiguration* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TPUEmbeddingConfiguration::InternalSwap(TPUEmbeddingConfiguration* other) {
  using std::swap;
  CastToBase(&table_descriptor_)->InternalSwap(CastToBase(&other->table_descriptor_));
  swap(output_layout_, other->output_layout_);
  swap(mode_, other->mode_);
  swap(batch_size_per_tensor_core_, other->batch_size_per_tensor_core_);
  swap(num_hosts_, other->num_hosts_);
  swap(num_tensor_cores_, other->num_tensor_cores_);
  swap(sharding_strategy_, other->sharding_strategy_);
  swap(pipeline_execution_with_tensor_core_, other->pipeline_execution_with_tensor_core_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata TPUEmbeddingConfiguration::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tpu
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor* Arena::CreateMaybeMessage< ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tpu::TPUEmbeddingConfiguration* Arena::CreateMaybeMessage< ::tensorflow::tpu::TPUEmbeddingConfiguration >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tpu::TPUEmbeddingConfiguration >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

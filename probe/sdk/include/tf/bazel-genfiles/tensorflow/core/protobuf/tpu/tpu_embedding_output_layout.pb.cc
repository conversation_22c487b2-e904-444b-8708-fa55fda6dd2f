// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/tpu/tpu_embedding_output_layout.proto

#include "tensorflow/core/protobuf/tpu/tpu_embedding_output_layout.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_TPUEmbeddingOutputLayout_OutputLocation;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_TPUEmbeddingOutputLayout_TwoDOutputTensor;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_TPUEmbeddingOutputLayout_EmbeddingOutputTensor;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_TPUEmbeddingOutputLayout_FeatureDescriptor;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_TPUEmbeddingOutputLayout_TableDescriptor;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto
namespace tensorflow {
namespace tpu {
class TPUEmbeddingOutputLayout_OutputLocationDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TPUEmbeddingOutputLayout_OutputLocation>
      _instance;
} _TPUEmbeddingOutputLayout_OutputLocation_default_instance_;
class TPUEmbeddingOutputLayout_FeatureDescriptorDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TPUEmbeddingOutputLayout_FeatureDescriptor>
      _instance;
} _TPUEmbeddingOutputLayout_FeatureDescriptor_default_instance_;
class TPUEmbeddingOutputLayout_TableDescriptorDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TPUEmbeddingOutputLayout_TableDescriptor>
      _instance;
} _TPUEmbeddingOutputLayout_TableDescriptor_default_instance_;
class TPUEmbeddingOutputLayout_TwoDOutputTensorDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TPUEmbeddingOutputLayout_TwoDOutputTensor>
      _instance;
} _TPUEmbeddingOutputLayout_TwoDOutputTensor_default_instance_;
class TPUEmbeddingOutputLayout_EmbeddingOutputTensorDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TPUEmbeddingOutputLayout_EmbeddingOutputTensor>
      _instance;
  const ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor* two_d_;
} _TPUEmbeddingOutputLayout_EmbeddingOutputTensor_default_instance_;
class TPUEmbeddingOutputLayoutDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TPUEmbeddingOutputLayout>
      _instance;
} _TPUEmbeddingOutputLayout_default_instance_;
}  // namespace tpu
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto {
static void InitDefaultsTPUEmbeddingOutputLayout_OutputLocation() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tpu::_TPUEmbeddingOutputLayout_OutputLocation_default_instance_;
    new (ptr) ::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_TPUEmbeddingOutputLayout_OutputLocation =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsTPUEmbeddingOutputLayout_OutputLocation}, {}};

static void InitDefaultsTPUEmbeddingOutputLayout_FeatureDescriptor() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tpu::_TPUEmbeddingOutputLayout_FeatureDescriptor_default_instance_;
    new (ptr) ::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_TPUEmbeddingOutputLayout_FeatureDescriptor =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsTPUEmbeddingOutputLayout_FeatureDescriptor}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::scc_info_TPUEmbeddingOutputLayout_OutputLocation.base,}};

static void InitDefaultsTPUEmbeddingOutputLayout_TableDescriptor() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tpu::_TPUEmbeddingOutputLayout_TableDescriptor_default_instance_;
    new (ptr) ::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_TPUEmbeddingOutputLayout_TableDescriptor =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsTPUEmbeddingOutputLayout_TableDescriptor}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::scc_info_TPUEmbeddingOutputLayout_FeatureDescriptor.base,}};

static void InitDefaultsTPUEmbeddingOutputLayout_TwoDOutputTensor() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tpu::_TPUEmbeddingOutputLayout_TwoDOutputTensor_default_instance_;
    new (ptr) ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_TPUEmbeddingOutputLayout_TwoDOutputTensor =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsTPUEmbeddingOutputLayout_TwoDOutputTensor}, {}};

static void InitDefaultsTPUEmbeddingOutputLayout_EmbeddingOutputTensor() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tpu::_TPUEmbeddingOutputLayout_EmbeddingOutputTensor_default_instance_;
    new (ptr) ::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_TPUEmbeddingOutputLayout_EmbeddingOutputTensor =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsTPUEmbeddingOutputLayout_EmbeddingOutputTensor}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::scc_info_TPUEmbeddingOutputLayout_TwoDOutputTensor.base,}};

static void InitDefaultsTPUEmbeddingOutputLayout() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tpu::_TPUEmbeddingOutputLayout_default_instance_;
    new (ptr) ::tensorflow::tpu::TPUEmbeddingOutputLayout();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tpu::TPUEmbeddingOutputLayout::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_TPUEmbeddingOutputLayout =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsTPUEmbeddingOutputLayout}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::scc_info_TPUEmbeddingOutputLayout_TableDescriptor.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::scc_info_TPUEmbeddingOutputLayout_EmbeddingOutputTensor.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_TPUEmbeddingOutputLayout_OutputLocation.base);
  ::google::protobuf::internal::InitSCC(&scc_info_TPUEmbeddingOutputLayout_FeatureDescriptor.base);
  ::google::protobuf::internal::InitSCC(&scc_info_TPUEmbeddingOutputLayout_TableDescriptor.base);
  ::google::protobuf::internal::InitSCC(&scc_info_TPUEmbeddingOutputLayout_TwoDOutputTensor.base);
  ::google::protobuf::internal::InitSCC(&scc_info_TPUEmbeddingOutputLayout_EmbeddingOutputTensor.base);
  ::google::protobuf::internal::InitSCC(&scc_info_TPUEmbeddingOutputLayout.base);
}

::google::protobuf::Metadata file_level_metadata[6];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation, tensor_index_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation, dim0_offset_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation, dim1_offset_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor, output_location_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor, feature_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor, dim0_size_per_sample_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor, dim1_size_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  offsetof(::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensorDefaultTypeInternal, two_d_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor, output_format_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingOutputLayout, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingOutputLayout, table_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::TPUEmbeddingOutputLayout, output_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation)},
  { 8, -1, sizeof(::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor)},
  { 14, -1, sizeof(::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor)},
  { 20, -1, sizeof(::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor)},
  { 27, -1, sizeof(::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor)},
  { 34, -1, sizeof(::tensorflow::tpu::TPUEmbeddingOutputLayout)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tpu::_TPUEmbeddingOutputLayout_OutputLocation_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tpu::_TPUEmbeddingOutputLayout_FeatureDescriptor_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tpu::_TPUEmbeddingOutputLayout_TableDescriptor_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tpu::_TPUEmbeddingOutputLayout_TwoDOutputTensor_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tpu::_TPUEmbeddingOutputLayout_EmbeddingOutputTensor_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tpu::_TPUEmbeddingOutputLayout_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/protobuf/tpu/tpu_embedding_output_layout.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 6);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n>tensorflow/core/protobuf/tpu/tpu_embed"
      "ding_output_layout.proto\022\016tensorflow.tpu"
      "\"\207\005\n\030TPUEmbeddingOutputLayout\022G\n\005table\030\001"
      " \003(\01328.tensorflow.tpu.TPUEmbeddingOutput"
      "Layout.TableDescriptor\022N\n\006output\030\002 \003(\0132>"
      ".tensorflow.tpu.TPUEmbeddingOutputLayout"
      ".EmbeddingOutputTensor\032P\n\016OutputLocation"
      "\022\024\n\014tensor_index\030\001 \001(\005\022\023\n\013dim0_offset\030\002 "
      "\001(\005\022\023\n\013dim1_offset\030\003 \001(\005\032e\n\021FeatureDescr"
      "iptor\022P\n\017output_location\030\001 \003(\01327.tensorf"
      "low.tpu.TPUEmbeddingOutputLayout.OutputL"
      "ocation\032^\n\017TableDescriptor\022K\n\007feature\030\001 "
      "\003(\0132:.tensorflow.tpu.TPUEmbeddingOutputL"
      "ayout.FeatureDescriptor\032C\n\020TwoDOutputTen"
      "sor\022\034\n\024dim0_size_per_sample\030\002 \001(\005\022\021\n\tdim"
      "1_size\030\001 \001(\005\032t\n\025EmbeddingOutputTensor\022J\n"
      "\005two_d\030\004 \001(\01329.tensorflow.tpu.TPUEmbeddi"
      "ngOutputLayout.TwoDOutputTensorH\000B\017\n\rout"
      "put_formatb\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 738);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/protobuf/tpu/tpu_embedding_output_layout.proto", &protobuf_RegisterTypes);
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto
namespace tensorflow {
namespace tpu {

// ===================================================================

void TPUEmbeddingOutputLayout_OutputLocation::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TPUEmbeddingOutputLayout_OutputLocation::kTensorIndexFieldNumber;
const int TPUEmbeddingOutputLayout_OutputLocation::kDim0OffsetFieldNumber;
const int TPUEmbeddingOutputLayout_OutputLocation::kDim1OffsetFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TPUEmbeddingOutputLayout_OutputLocation::TPUEmbeddingOutputLayout_OutputLocation()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::scc_info_TPUEmbeddingOutputLayout_OutputLocation.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation)
}
TPUEmbeddingOutputLayout_OutputLocation::TPUEmbeddingOutputLayout_OutputLocation(const TPUEmbeddingOutputLayout_OutputLocation& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&tensor_index_, &from.tensor_index_,
    static_cast<size_t>(reinterpret_cast<char*>(&dim1_offset_) -
    reinterpret_cast<char*>(&tensor_index_)) + sizeof(dim1_offset_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation)
}

void TPUEmbeddingOutputLayout_OutputLocation::SharedCtor() {
  ::memset(&tensor_index_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&dim1_offset_) -
      reinterpret_cast<char*>(&tensor_index_)) + sizeof(dim1_offset_));
}

TPUEmbeddingOutputLayout_OutputLocation::~TPUEmbeddingOutputLayout_OutputLocation() {
  // @@protoc_insertion_point(destructor:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation)
  SharedDtor();
}

void TPUEmbeddingOutputLayout_OutputLocation::SharedDtor() {
}

void TPUEmbeddingOutputLayout_OutputLocation::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* TPUEmbeddingOutputLayout_OutputLocation::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const TPUEmbeddingOutputLayout_OutputLocation& TPUEmbeddingOutputLayout_OutputLocation::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::scc_info_TPUEmbeddingOutputLayout_OutputLocation.base);
  return *internal_default_instance();
}


void TPUEmbeddingOutputLayout_OutputLocation::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&tensor_index_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&dim1_offset_) -
      reinterpret_cast<char*>(&tensor_index_)) + sizeof(dim1_offset_));
  _internal_metadata_.Clear();
}

bool TPUEmbeddingOutputLayout_OutputLocation::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 tensor_index = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &tensor_index_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 dim0_offset = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &dim0_offset_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 dim1_offset = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &dim1_offset_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation)
  return false;
#undef DO_
}

void TPUEmbeddingOutputLayout_OutputLocation::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 tensor_index = 1;
  if (this->tensor_index() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->tensor_index(), output);
  }

  // int32 dim0_offset = 2;
  if (this->dim0_offset() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->dim0_offset(), output);
  }

  // int32 dim1_offset = 3;
  if (this->dim1_offset() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->dim1_offset(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation)
}

::google::protobuf::uint8* TPUEmbeddingOutputLayout_OutputLocation::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 tensor_index = 1;
  if (this->tensor_index() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->tensor_index(), target);
  }

  // int32 dim0_offset = 2;
  if (this->dim0_offset() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->dim0_offset(), target);
  }

  // int32 dim1_offset = 3;
  if (this->dim1_offset() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->dim1_offset(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation)
  return target;
}

size_t TPUEmbeddingOutputLayout_OutputLocation::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int32 tensor_index = 1;
  if (this->tensor_index() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->tensor_index());
  }

  // int32 dim0_offset = 2;
  if (this->dim0_offset() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->dim0_offset());
  }

  // int32 dim1_offset = 3;
  if (this->dim1_offset() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->dim1_offset());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TPUEmbeddingOutputLayout_OutputLocation::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation)
  GOOGLE_DCHECK_NE(&from, this);
  const TPUEmbeddingOutputLayout_OutputLocation* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TPUEmbeddingOutputLayout_OutputLocation>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation)
    MergeFrom(*source);
  }
}

void TPUEmbeddingOutputLayout_OutputLocation::MergeFrom(const TPUEmbeddingOutputLayout_OutputLocation& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.tensor_index() != 0) {
    set_tensor_index(from.tensor_index());
  }
  if (from.dim0_offset() != 0) {
    set_dim0_offset(from.dim0_offset());
  }
  if (from.dim1_offset() != 0) {
    set_dim1_offset(from.dim1_offset());
  }
}

void TPUEmbeddingOutputLayout_OutputLocation::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TPUEmbeddingOutputLayout_OutputLocation::CopyFrom(const TPUEmbeddingOutputLayout_OutputLocation& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TPUEmbeddingOutputLayout_OutputLocation::IsInitialized() const {
  return true;
}

void TPUEmbeddingOutputLayout_OutputLocation::Swap(TPUEmbeddingOutputLayout_OutputLocation* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TPUEmbeddingOutputLayout_OutputLocation::InternalSwap(TPUEmbeddingOutputLayout_OutputLocation* other) {
  using std::swap;
  swap(tensor_index_, other->tensor_index_);
  swap(dim0_offset_, other->dim0_offset_);
  swap(dim1_offset_, other->dim1_offset_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata TPUEmbeddingOutputLayout_OutputLocation::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void TPUEmbeddingOutputLayout_FeatureDescriptor::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TPUEmbeddingOutputLayout_FeatureDescriptor::kOutputLocationFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TPUEmbeddingOutputLayout_FeatureDescriptor::TPUEmbeddingOutputLayout_FeatureDescriptor()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::scc_info_TPUEmbeddingOutputLayout_FeatureDescriptor.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor)
}
TPUEmbeddingOutputLayout_FeatureDescriptor::TPUEmbeddingOutputLayout_FeatureDescriptor(const TPUEmbeddingOutputLayout_FeatureDescriptor& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      output_location_(from.output_location_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor)
}

void TPUEmbeddingOutputLayout_FeatureDescriptor::SharedCtor() {
}

TPUEmbeddingOutputLayout_FeatureDescriptor::~TPUEmbeddingOutputLayout_FeatureDescriptor() {
  // @@protoc_insertion_point(destructor:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor)
  SharedDtor();
}

void TPUEmbeddingOutputLayout_FeatureDescriptor::SharedDtor() {
}

void TPUEmbeddingOutputLayout_FeatureDescriptor::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* TPUEmbeddingOutputLayout_FeatureDescriptor::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const TPUEmbeddingOutputLayout_FeatureDescriptor& TPUEmbeddingOutputLayout_FeatureDescriptor::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::scc_info_TPUEmbeddingOutputLayout_FeatureDescriptor.base);
  return *internal_default_instance();
}


void TPUEmbeddingOutputLayout_FeatureDescriptor::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  output_location_.Clear();
  _internal_metadata_.Clear();
}

bool TPUEmbeddingOutputLayout_FeatureDescriptor::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation output_location = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_output_location()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor)
  return false;
#undef DO_
}

void TPUEmbeddingOutputLayout_FeatureDescriptor::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation output_location = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->output_location_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->output_location(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor)
}

::google::protobuf::uint8* TPUEmbeddingOutputLayout_FeatureDescriptor::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation output_location = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->output_location_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->output_location(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor)
  return target;
}

size_t TPUEmbeddingOutputLayout_FeatureDescriptor::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.OutputLocation output_location = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->output_location_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->output_location(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TPUEmbeddingOutputLayout_FeatureDescriptor::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor)
  GOOGLE_DCHECK_NE(&from, this);
  const TPUEmbeddingOutputLayout_FeatureDescriptor* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TPUEmbeddingOutputLayout_FeatureDescriptor>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor)
    MergeFrom(*source);
  }
}

void TPUEmbeddingOutputLayout_FeatureDescriptor::MergeFrom(const TPUEmbeddingOutputLayout_FeatureDescriptor& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  output_location_.MergeFrom(from.output_location_);
}

void TPUEmbeddingOutputLayout_FeatureDescriptor::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TPUEmbeddingOutputLayout_FeatureDescriptor::CopyFrom(const TPUEmbeddingOutputLayout_FeatureDescriptor& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TPUEmbeddingOutputLayout_FeatureDescriptor::IsInitialized() const {
  return true;
}

void TPUEmbeddingOutputLayout_FeatureDescriptor::Swap(TPUEmbeddingOutputLayout_FeatureDescriptor* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TPUEmbeddingOutputLayout_FeatureDescriptor::InternalSwap(TPUEmbeddingOutputLayout_FeatureDescriptor* other) {
  using std::swap;
  CastToBase(&output_location_)->InternalSwap(CastToBase(&other->output_location_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata TPUEmbeddingOutputLayout_FeatureDescriptor::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void TPUEmbeddingOutputLayout_TableDescriptor::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TPUEmbeddingOutputLayout_TableDescriptor::kFeatureFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TPUEmbeddingOutputLayout_TableDescriptor::TPUEmbeddingOutputLayout_TableDescriptor()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::scc_info_TPUEmbeddingOutputLayout_TableDescriptor.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor)
}
TPUEmbeddingOutputLayout_TableDescriptor::TPUEmbeddingOutputLayout_TableDescriptor(const TPUEmbeddingOutputLayout_TableDescriptor& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      feature_(from.feature_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor)
}

void TPUEmbeddingOutputLayout_TableDescriptor::SharedCtor() {
}

TPUEmbeddingOutputLayout_TableDescriptor::~TPUEmbeddingOutputLayout_TableDescriptor() {
  // @@protoc_insertion_point(destructor:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor)
  SharedDtor();
}

void TPUEmbeddingOutputLayout_TableDescriptor::SharedDtor() {
}

void TPUEmbeddingOutputLayout_TableDescriptor::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* TPUEmbeddingOutputLayout_TableDescriptor::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const TPUEmbeddingOutputLayout_TableDescriptor& TPUEmbeddingOutputLayout_TableDescriptor::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::scc_info_TPUEmbeddingOutputLayout_TableDescriptor.base);
  return *internal_default_instance();
}


void TPUEmbeddingOutputLayout_TableDescriptor::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  feature_.Clear();
  _internal_metadata_.Clear();
}

bool TPUEmbeddingOutputLayout_TableDescriptor::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor feature = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_feature()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor)
  return false;
#undef DO_
}

void TPUEmbeddingOutputLayout_TableDescriptor::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor feature = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->feature_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->feature(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor)
}

::google::protobuf::uint8* TPUEmbeddingOutputLayout_TableDescriptor::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor feature = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->feature_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->feature(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor)
  return target;
}

size_t TPUEmbeddingOutputLayout_TableDescriptor::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.FeatureDescriptor feature = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->feature_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->feature(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TPUEmbeddingOutputLayout_TableDescriptor::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor)
  GOOGLE_DCHECK_NE(&from, this);
  const TPUEmbeddingOutputLayout_TableDescriptor* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TPUEmbeddingOutputLayout_TableDescriptor>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor)
    MergeFrom(*source);
  }
}

void TPUEmbeddingOutputLayout_TableDescriptor::MergeFrom(const TPUEmbeddingOutputLayout_TableDescriptor& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  feature_.MergeFrom(from.feature_);
}

void TPUEmbeddingOutputLayout_TableDescriptor::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TPUEmbeddingOutputLayout_TableDescriptor::CopyFrom(const TPUEmbeddingOutputLayout_TableDescriptor& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TPUEmbeddingOutputLayout_TableDescriptor::IsInitialized() const {
  return true;
}

void TPUEmbeddingOutputLayout_TableDescriptor::Swap(TPUEmbeddingOutputLayout_TableDescriptor* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TPUEmbeddingOutputLayout_TableDescriptor::InternalSwap(TPUEmbeddingOutputLayout_TableDescriptor* other) {
  using std::swap;
  CastToBase(&feature_)->InternalSwap(CastToBase(&other->feature_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata TPUEmbeddingOutputLayout_TableDescriptor::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void TPUEmbeddingOutputLayout_TwoDOutputTensor::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TPUEmbeddingOutputLayout_TwoDOutputTensor::kDim0SizePerSampleFieldNumber;
const int TPUEmbeddingOutputLayout_TwoDOutputTensor::kDim1SizeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TPUEmbeddingOutputLayout_TwoDOutputTensor::TPUEmbeddingOutputLayout_TwoDOutputTensor()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::scc_info_TPUEmbeddingOutputLayout_TwoDOutputTensor.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor)
}
TPUEmbeddingOutputLayout_TwoDOutputTensor::TPUEmbeddingOutputLayout_TwoDOutputTensor(const TPUEmbeddingOutputLayout_TwoDOutputTensor& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&dim1_size_, &from.dim1_size_,
    static_cast<size_t>(reinterpret_cast<char*>(&dim0_size_per_sample_) -
    reinterpret_cast<char*>(&dim1_size_)) + sizeof(dim0_size_per_sample_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor)
}

void TPUEmbeddingOutputLayout_TwoDOutputTensor::SharedCtor() {
  ::memset(&dim1_size_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&dim0_size_per_sample_) -
      reinterpret_cast<char*>(&dim1_size_)) + sizeof(dim0_size_per_sample_));
}

TPUEmbeddingOutputLayout_TwoDOutputTensor::~TPUEmbeddingOutputLayout_TwoDOutputTensor() {
  // @@protoc_insertion_point(destructor:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor)
  SharedDtor();
}

void TPUEmbeddingOutputLayout_TwoDOutputTensor::SharedDtor() {
}

void TPUEmbeddingOutputLayout_TwoDOutputTensor::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* TPUEmbeddingOutputLayout_TwoDOutputTensor::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const TPUEmbeddingOutputLayout_TwoDOutputTensor& TPUEmbeddingOutputLayout_TwoDOutputTensor::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::scc_info_TPUEmbeddingOutputLayout_TwoDOutputTensor.base);
  return *internal_default_instance();
}


void TPUEmbeddingOutputLayout_TwoDOutputTensor::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&dim1_size_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&dim0_size_per_sample_) -
      reinterpret_cast<char*>(&dim1_size_)) + sizeof(dim0_size_per_sample_));
  _internal_metadata_.Clear();
}

bool TPUEmbeddingOutputLayout_TwoDOutputTensor::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 dim1_size = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &dim1_size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 dim0_size_per_sample = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &dim0_size_per_sample_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor)
  return false;
#undef DO_
}

void TPUEmbeddingOutputLayout_TwoDOutputTensor::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 dim1_size = 1;
  if (this->dim1_size() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->dim1_size(), output);
  }

  // int32 dim0_size_per_sample = 2;
  if (this->dim0_size_per_sample() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->dim0_size_per_sample(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor)
}

::google::protobuf::uint8* TPUEmbeddingOutputLayout_TwoDOutputTensor::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 dim1_size = 1;
  if (this->dim1_size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->dim1_size(), target);
  }

  // int32 dim0_size_per_sample = 2;
  if (this->dim0_size_per_sample() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->dim0_size_per_sample(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor)
  return target;
}

size_t TPUEmbeddingOutputLayout_TwoDOutputTensor::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int32 dim1_size = 1;
  if (this->dim1_size() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->dim1_size());
  }

  // int32 dim0_size_per_sample = 2;
  if (this->dim0_size_per_sample() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->dim0_size_per_sample());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TPUEmbeddingOutputLayout_TwoDOutputTensor::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor)
  GOOGLE_DCHECK_NE(&from, this);
  const TPUEmbeddingOutputLayout_TwoDOutputTensor* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TPUEmbeddingOutputLayout_TwoDOutputTensor>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor)
    MergeFrom(*source);
  }
}

void TPUEmbeddingOutputLayout_TwoDOutputTensor::MergeFrom(const TPUEmbeddingOutputLayout_TwoDOutputTensor& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.dim1_size() != 0) {
    set_dim1_size(from.dim1_size());
  }
  if (from.dim0_size_per_sample() != 0) {
    set_dim0_size_per_sample(from.dim0_size_per_sample());
  }
}

void TPUEmbeddingOutputLayout_TwoDOutputTensor::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TPUEmbeddingOutputLayout_TwoDOutputTensor::CopyFrom(const TPUEmbeddingOutputLayout_TwoDOutputTensor& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TPUEmbeddingOutputLayout_TwoDOutputTensor::IsInitialized() const {
  return true;
}

void TPUEmbeddingOutputLayout_TwoDOutputTensor::Swap(TPUEmbeddingOutputLayout_TwoDOutputTensor* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TPUEmbeddingOutputLayout_TwoDOutputTensor::InternalSwap(TPUEmbeddingOutputLayout_TwoDOutputTensor* other) {
  using std::swap;
  swap(dim1_size_, other->dim1_size_);
  swap(dim0_size_per_sample_, other->dim0_size_per_sample_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata TPUEmbeddingOutputLayout_TwoDOutputTensor::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void TPUEmbeddingOutputLayout_EmbeddingOutputTensor::InitAsDefaultInstance() {
  ::tensorflow::tpu::_TPUEmbeddingOutputLayout_EmbeddingOutputTensor_default_instance_.two_d_ = const_cast< ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor*>(
      ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor::internal_default_instance());
}
void TPUEmbeddingOutputLayout_EmbeddingOutputTensor::set_allocated_two_d(::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor* two_d) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_output_format();
  if (two_d) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      two_d = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, two_d, submessage_arena);
    }
    set_has_two_d();
    output_format_.two_d_ = two_d;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor.two_d)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TPUEmbeddingOutputLayout_EmbeddingOutputTensor::kTwoDFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TPUEmbeddingOutputLayout_EmbeddingOutputTensor::TPUEmbeddingOutputLayout_EmbeddingOutputTensor()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::scc_info_TPUEmbeddingOutputLayout_EmbeddingOutputTensor.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor)
}
TPUEmbeddingOutputLayout_EmbeddingOutputTensor::TPUEmbeddingOutputLayout_EmbeddingOutputTensor(const TPUEmbeddingOutputLayout_EmbeddingOutputTensor& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  clear_has_output_format();
  switch (from.output_format_case()) {
    case kTwoD: {
      mutable_two_d()->::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor::MergeFrom(from.two_d());
      break;
    }
    case OUTPUT_FORMAT_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor)
}

void TPUEmbeddingOutputLayout_EmbeddingOutputTensor::SharedCtor() {
  clear_has_output_format();
}

TPUEmbeddingOutputLayout_EmbeddingOutputTensor::~TPUEmbeddingOutputLayout_EmbeddingOutputTensor() {
  // @@protoc_insertion_point(destructor:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor)
  SharedDtor();
}

void TPUEmbeddingOutputLayout_EmbeddingOutputTensor::SharedDtor() {
  if (has_output_format()) {
    clear_output_format();
  }
}

void TPUEmbeddingOutputLayout_EmbeddingOutputTensor::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* TPUEmbeddingOutputLayout_EmbeddingOutputTensor::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const TPUEmbeddingOutputLayout_EmbeddingOutputTensor& TPUEmbeddingOutputLayout_EmbeddingOutputTensor::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::scc_info_TPUEmbeddingOutputLayout_EmbeddingOutputTensor.base);
  return *internal_default_instance();
}


void TPUEmbeddingOutputLayout_EmbeddingOutputTensor::clear_output_format() {
// @@protoc_insertion_point(one_of_clear_start:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor)
  switch (output_format_case()) {
    case kTwoD: {
      delete output_format_.two_d_;
      break;
    }
    case OUTPUT_FORMAT_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = OUTPUT_FORMAT_NOT_SET;
}


void TPUEmbeddingOutputLayout_EmbeddingOutputTensor::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_output_format();
  _internal_metadata_.Clear();
}

bool TPUEmbeddingOutputLayout_EmbeddingOutputTensor::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor two_d = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_two_d()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor)
  return false;
#undef DO_
}

void TPUEmbeddingOutputLayout_EmbeddingOutputTensor::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor two_d = 4;
  if (has_two_d()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->_internal_two_d(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor)
}

::google::protobuf::uint8* TPUEmbeddingOutputLayout_EmbeddingOutputTensor::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor two_d = 4;
  if (has_two_d()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->_internal_two_d(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor)
  return target;
}

size_t TPUEmbeddingOutputLayout_EmbeddingOutputTensor::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  switch (output_format_case()) {
    // .tensorflow.tpu.TPUEmbeddingOutputLayout.TwoDOutputTensor two_d = 4;
    case kTwoD: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *output_format_.two_d_);
      break;
    }
    case OUTPUT_FORMAT_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TPUEmbeddingOutputLayout_EmbeddingOutputTensor::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor)
  GOOGLE_DCHECK_NE(&from, this);
  const TPUEmbeddingOutputLayout_EmbeddingOutputTensor* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TPUEmbeddingOutputLayout_EmbeddingOutputTensor>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor)
    MergeFrom(*source);
  }
}

void TPUEmbeddingOutputLayout_EmbeddingOutputTensor::MergeFrom(const TPUEmbeddingOutputLayout_EmbeddingOutputTensor& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.output_format_case()) {
    case kTwoD: {
      mutable_two_d()->::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor::MergeFrom(from.two_d());
      break;
    }
    case OUTPUT_FORMAT_NOT_SET: {
      break;
    }
  }
}

void TPUEmbeddingOutputLayout_EmbeddingOutputTensor::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TPUEmbeddingOutputLayout_EmbeddingOutputTensor::CopyFrom(const TPUEmbeddingOutputLayout_EmbeddingOutputTensor& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TPUEmbeddingOutputLayout_EmbeddingOutputTensor::IsInitialized() const {
  return true;
}

void TPUEmbeddingOutputLayout_EmbeddingOutputTensor::Swap(TPUEmbeddingOutputLayout_EmbeddingOutputTensor* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TPUEmbeddingOutputLayout_EmbeddingOutputTensor::InternalSwap(TPUEmbeddingOutputLayout_EmbeddingOutputTensor* other) {
  using std::swap;
  swap(output_format_, other->output_format_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata TPUEmbeddingOutputLayout_EmbeddingOutputTensor::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void TPUEmbeddingOutputLayout::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TPUEmbeddingOutputLayout::kTableFieldNumber;
const int TPUEmbeddingOutputLayout::kOutputFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TPUEmbeddingOutputLayout::TPUEmbeddingOutputLayout()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::scc_info_TPUEmbeddingOutputLayout.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tpu.TPUEmbeddingOutputLayout)
}
TPUEmbeddingOutputLayout::TPUEmbeddingOutputLayout(const TPUEmbeddingOutputLayout& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      table_(from.table_),
      output_(from.output_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.tpu.TPUEmbeddingOutputLayout)
}

void TPUEmbeddingOutputLayout::SharedCtor() {
}

TPUEmbeddingOutputLayout::~TPUEmbeddingOutputLayout() {
  // @@protoc_insertion_point(destructor:tensorflow.tpu.TPUEmbeddingOutputLayout)
  SharedDtor();
}

void TPUEmbeddingOutputLayout::SharedDtor() {
}

void TPUEmbeddingOutputLayout::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* TPUEmbeddingOutputLayout::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const TPUEmbeddingOutputLayout& TPUEmbeddingOutputLayout::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::scc_info_TPUEmbeddingOutputLayout.base);
  return *internal_default_instance();
}


void TPUEmbeddingOutputLayout::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tpu.TPUEmbeddingOutputLayout)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  table_.Clear();
  output_.Clear();
  _internal_metadata_.Clear();
}

bool TPUEmbeddingOutputLayout::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tpu.TPUEmbeddingOutputLayout)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor table = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_table()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor output = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_output()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tpu.TPUEmbeddingOutputLayout)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tpu.TPUEmbeddingOutputLayout)
  return false;
#undef DO_
}

void TPUEmbeddingOutputLayout::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tpu.TPUEmbeddingOutputLayout)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor table = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->table_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->table(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor output = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->output_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2,
      this->output(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tpu.TPUEmbeddingOutputLayout)
}

::google::protobuf::uint8* TPUEmbeddingOutputLayout::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tpu.TPUEmbeddingOutputLayout)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor table = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->table_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->table(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor output = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->output_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->output(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tpu.TPUEmbeddingOutputLayout)
  return target;
}

size_t TPUEmbeddingOutputLayout::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tpu.TPUEmbeddingOutputLayout)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.TableDescriptor table = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->table_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->table(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.tpu.TPUEmbeddingOutputLayout.EmbeddingOutputTensor output = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->output_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->output(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TPUEmbeddingOutputLayout::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tpu.TPUEmbeddingOutputLayout)
  GOOGLE_DCHECK_NE(&from, this);
  const TPUEmbeddingOutputLayout* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TPUEmbeddingOutputLayout>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tpu.TPUEmbeddingOutputLayout)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tpu.TPUEmbeddingOutputLayout)
    MergeFrom(*source);
  }
}

void TPUEmbeddingOutputLayout::MergeFrom(const TPUEmbeddingOutputLayout& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tpu.TPUEmbeddingOutputLayout)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  table_.MergeFrom(from.table_);
  output_.MergeFrom(from.output_);
}

void TPUEmbeddingOutputLayout::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tpu.TPUEmbeddingOutputLayout)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TPUEmbeddingOutputLayout::CopyFrom(const TPUEmbeddingOutputLayout& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tpu.TPUEmbeddingOutputLayout)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TPUEmbeddingOutputLayout::IsInitialized() const {
  return true;
}

void TPUEmbeddingOutputLayout::Swap(TPUEmbeddingOutputLayout* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TPUEmbeddingOutputLayout::InternalSwap(TPUEmbeddingOutputLayout* other) {
  using std::swap;
  CastToBase(&table_)->InternalSwap(CastToBase(&other->table_));
  CastToBase(&output_)->InternalSwap(CastToBase(&other->output_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata TPUEmbeddingOutputLayout::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5foutput_5flayout_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tpu
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation* Arena::CreateMaybeMessage< ::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tpu::TPUEmbeddingOutputLayout_OutputLocation >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor* Arena::CreateMaybeMessage< ::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tpu::TPUEmbeddingOutputLayout_FeatureDescriptor >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor* Arena::CreateMaybeMessage< ::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tpu::TPUEmbeddingOutputLayout_TableDescriptor >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor* Arena::CreateMaybeMessage< ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tpu::TPUEmbeddingOutputLayout_TwoDOutputTensor >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor* Arena::CreateMaybeMessage< ::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tpu::TPUEmbeddingOutputLayout_EmbeddingOutputTensor >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tpu::TPUEmbeddingOutputLayout* Arena::CreateMaybeMessage< ::tensorflow::tpu::TPUEmbeddingOutputLayout >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tpu::TPUEmbeddingOutputLayout >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

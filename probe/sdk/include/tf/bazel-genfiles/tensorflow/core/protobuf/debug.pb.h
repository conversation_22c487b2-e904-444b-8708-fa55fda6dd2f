// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/debug.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto 

namespace protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[4];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto
namespace tensorflow {
class DebugOptions;
class DebugOptionsDefaultTypeInternal;
extern DebugOptionsDefaultTypeInternal _DebugOptions_default_instance_;
class DebugTensorWatch;
class DebugTensorWatchDefaultTypeInternal;
extern DebugTensorWatchDefaultTypeInternal _DebugTensorWatch_default_instance_;
class DebuggedSourceFile;
class DebuggedSourceFileDefaultTypeInternal;
extern DebuggedSourceFileDefaultTypeInternal _DebuggedSourceFile_default_instance_;
class DebuggedSourceFiles;
class DebuggedSourceFilesDefaultTypeInternal;
extern DebuggedSourceFilesDefaultTypeInternal _DebuggedSourceFiles_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::DebugOptions* Arena::CreateMaybeMessage<::tensorflow::DebugOptions>(Arena*);
template<> ::tensorflow::DebugTensorWatch* Arena::CreateMaybeMessage<::tensorflow::DebugTensorWatch>(Arena*);
template<> ::tensorflow::DebuggedSourceFile* Arena::CreateMaybeMessage<::tensorflow::DebuggedSourceFile>(Arena*);
template<> ::tensorflow::DebuggedSourceFiles* Arena::CreateMaybeMessage<::tensorflow::DebuggedSourceFiles>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

// ===================================================================

class DebugTensorWatch : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.DebugTensorWatch) */ {
 public:
  DebugTensorWatch();
  virtual ~DebugTensorWatch();

  DebugTensorWatch(const DebugTensorWatch& from);

  inline DebugTensorWatch& operator=(const DebugTensorWatch& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  DebugTensorWatch(DebugTensorWatch&& from) noexcept
    : DebugTensorWatch() {
    *this = ::std::move(from);
  }

  inline DebugTensorWatch& operator=(DebugTensorWatch&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const DebugTensorWatch& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DebugTensorWatch* internal_default_instance() {
    return reinterpret_cast<const DebugTensorWatch*>(
               &_DebugTensorWatch_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(DebugTensorWatch* other);
  void Swap(DebugTensorWatch* other);
  friend void swap(DebugTensorWatch& a, DebugTensorWatch& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline DebugTensorWatch* New() const final {
    return CreateMaybeMessage<DebugTensorWatch>(NULL);
  }

  DebugTensorWatch* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<DebugTensorWatch>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const DebugTensorWatch& from);
  void MergeFrom(const DebugTensorWatch& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DebugTensorWatch* other);
  protected:
  explicit DebugTensorWatch(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string debug_ops = 3;
  int debug_ops_size() const;
  void clear_debug_ops();
  static const int kDebugOpsFieldNumber = 3;
  const ::std::string& debug_ops(int index) const;
  ::std::string* mutable_debug_ops(int index);
  void set_debug_ops(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_debug_ops(int index, ::std::string&& value);
  #endif
  void set_debug_ops(int index, const char* value);
  void set_debug_ops(int index, const char* value, size_t size);
  ::std::string* add_debug_ops();
  void add_debug_ops(const ::std::string& value);
  #if LANG_CXX11
  void add_debug_ops(::std::string&& value);
  #endif
  void add_debug_ops(const char* value);
  void add_debug_ops(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& debug_ops() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_debug_ops();

  // repeated string debug_urls = 4;
  int debug_urls_size() const;
  void clear_debug_urls();
  static const int kDebugUrlsFieldNumber = 4;
  const ::std::string& debug_urls(int index) const;
  ::std::string* mutable_debug_urls(int index);
  void set_debug_urls(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_debug_urls(int index, ::std::string&& value);
  #endif
  void set_debug_urls(int index, const char* value);
  void set_debug_urls(int index, const char* value, size_t size);
  ::std::string* add_debug_urls();
  void add_debug_urls(const ::std::string& value);
  #if LANG_CXX11
  void add_debug_urls(::std::string&& value);
  #endif
  void add_debug_urls(const char* value);
  void add_debug_urls(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& debug_urls() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_debug_urls();

  // string node_name = 1;
  void clear_node_name();
  static const int kNodeNameFieldNumber = 1;
  const ::std::string& node_name() const;
  void set_node_name(const ::std::string& value);
  #if LANG_CXX11
  void set_node_name(::std::string&& value);
  #endif
  void set_node_name(const char* value);
  void set_node_name(const char* value, size_t size);
  ::std::string* mutable_node_name();
  ::std::string* release_node_name();
  void set_allocated_node_name(::std::string* node_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_node_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_node_name(
      ::std::string* node_name);

  // int32 output_slot = 2;
  void clear_output_slot();
  static const int kOutputSlotFieldNumber = 2;
  ::google::protobuf::int32 output_slot() const;
  void set_output_slot(::google::protobuf::int32 value);

  // bool tolerate_debug_op_creation_failures = 5;
  void clear_tolerate_debug_op_creation_failures();
  static const int kTolerateDebugOpCreationFailuresFieldNumber = 5;
  bool tolerate_debug_op_creation_failures() const;
  void set_tolerate_debug_op_creation_failures(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.DebugTensorWatch)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::std::string> debug_ops_;
  ::google::protobuf::RepeatedPtrField< ::std::string> debug_urls_;
  ::google::protobuf::internal::ArenaStringPtr node_name_;
  ::google::protobuf::int32 output_slot_;
  bool tolerate_debug_op_creation_failures_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class DebugOptions : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.DebugOptions) */ {
 public:
  DebugOptions();
  virtual ~DebugOptions();

  DebugOptions(const DebugOptions& from);

  inline DebugOptions& operator=(const DebugOptions& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  DebugOptions(DebugOptions&& from) noexcept
    : DebugOptions() {
    *this = ::std::move(from);
  }

  inline DebugOptions& operator=(DebugOptions&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const DebugOptions& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DebugOptions* internal_default_instance() {
    return reinterpret_cast<const DebugOptions*>(
               &_DebugOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void UnsafeArenaSwap(DebugOptions* other);
  void Swap(DebugOptions* other);
  friend void swap(DebugOptions& a, DebugOptions& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline DebugOptions* New() const final {
    return CreateMaybeMessage<DebugOptions>(NULL);
  }

  DebugOptions* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<DebugOptions>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const DebugOptions& from);
  void MergeFrom(const DebugOptions& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DebugOptions* other);
  protected:
  explicit DebugOptions(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.DebugTensorWatch debug_tensor_watch_opts = 4;
  int debug_tensor_watch_opts_size() const;
  void clear_debug_tensor_watch_opts();
  static const int kDebugTensorWatchOptsFieldNumber = 4;
  ::tensorflow::DebugTensorWatch* mutable_debug_tensor_watch_opts(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::DebugTensorWatch >*
      mutable_debug_tensor_watch_opts();
  const ::tensorflow::DebugTensorWatch& debug_tensor_watch_opts(int index) const;
  ::tensorflow::DebugTensorWatch* add_debug_tensor_watch_opts();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::DebugTensorWatch >&
      debug_tensor_watch_opts() const;

  // bool reset_disk_byte_usage = 11;
  void clear_reset_disk_byte_usage();
  static const int kResetDiskByteUsageFieldNumber = 11;
  bool reset_disk_byte_usage() const;
  void set_reset_disk_byte_usage(bool value);

  // int64 global_step = 10;
  void clear_global_step();
  static const int kGlobalStepFieldNumber = 10;
  ::google::protobuf::int64 global_step() const;
  void set_global_step(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.DebugOptions)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::DebugTensorWatch > debug_tensor_watch_opts_;
  bool reset_disk_byte_usage_;
  ::google::protobuf::int64 global_step_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class DebuggedSourceFile : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.DebuggedSourceFile) */ {
 public:
  DebuggedSourceFile();
  virtual ~DebuggedSourceFile();

  DebuggedSourceFile(const DebuggedSourceFile& from);

  inline DebuggedSourceFile& operator=(const DebuggedSourceFile& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  DebuggedSourceFile(DebuggedSourceFile&& from) noexcept
    : DebuggedSourceFile() {
    *this = ::std::move(from);
  }

  inline DebuggedSourceFile& operator=(DebuggedSourceFile&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const DebuggedSourceFile& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DebuggedSourceFile* internal_default_instance() {
    return reinterpret_cast<const DebuggedSourceFile*>(
               &_DebuggedSourceFile_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void UnsafeArenaSwap(DebuggedSourceFile* other);
  void Swap(DebuggedSourceFile* other);
  friend void swap(DebuggedSourceFile& a, DebuggedSourceFile& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline DebuggedSourceFile* New() const final {
    return CreateMaybeMessage<DebuggedSourceFile>(NULL);
  }

  DebuggedSourceFile* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<DebuggedSourceFile>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const DebuggedSourceFile& from);
  void MergeFrom(const DebuggedSourceFile& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DebuggedSourceFile* other);
  protected:
  explicit DebuggedSourceFile(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string lines = 5;
  int lines_size() const;
  void clear_lines();
  static const int kLinesFieldNumber = 5;
  const ::std::string& lines(int index) const;
  ::std::string* mutable_lines(int index);
  void set_lines(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_lines(int index, ::std::string&& value);
  #endif
  void set_lines(int index, const char* value);
  void set_lines(int index, const char* value, size_t size);
  ::std::string* add_lines();
  void add_lines(const ::std::string& value);
  #if LANG_CXX11
  void add_lines(::std::string&& value);
  #endif
  void add_lines(const char* value);
  void add_lines(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& lines() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_lines();

  // string host = 1;
  void clear_host();
  static const int kHostFieldNumber = 1;
  const ::std::string& host() const;
  void set_host(const ::std::string& value);
  #if LANG_CXX11
  void set_host(::std::string&& value);
  #endif
  void set_host(const char* value);
  void set_host(const char* value, size_t size);
  ::std::string* mutable_host();
  ::std::string* release_host();
  void set_allocated_host(::std::string* host);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_host();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_host(
      ::std::string* host);

  // string file_path = 2;
  void clear_file_path();
  static const int kFilePathFieldNumber = 2;
  const ::std::string& file_path() const;
  void set_file_path(const ::std::string& value);
  #if LANG_CXX11
  void set_file_path(::std::string&& value);
  #endif
  void set_file_path(const char* value);
  void set_file_path(const char* value, size_t size);
  ::std::string* mutable_file_path();
  ::std::string* release_file_path();
  void set_allocated_file_path(::std::string* file_path);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_file_path();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_file_path(
      ::std::string* file_path);

  // int64 last_modified = 3;
  void clear_last_modified();
  static const int kLastModifiedFieldNumber = 3;
  ::google::protobuf::int64 last_modified() const;
  void set_last_modified(::google::protobuf::int64 value);

  // int64 bytes = 4;
  void clear_bytes();
  static const int kBytesFieldNumber = 4;
  ::google::protobuf::int64 bytes() const;
  void set_bytes(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.DebuggedSourceFile)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::std::string> lines_;
  ::google::protobuf::internal::ArenaStringPtr host_;
  ::google::protobuf::internal::ArenaStringPtr file_path_;
  ::google::protobuf::int64 last_modified_;
  ::google::protobuf::int64 bytes_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class DebuggedSourceFiles : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.DebuggedSourceFiles) */ {
 public:
  DebuggedSourceFiles();
  virtual ~DebuggedSourceFiles();

  DebuggedSourceFiles(const DebuggedSourceFiles& from);

  inline DebuggedSourceFiles& operator=(const DebuggedSourceFiles& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  DebuggedSourceFiles(DebuggedSourceFiles&& from) noexcept
    : DebuggedSourceFiles() {
    *this = ::std::move(from);
  }

  inline DebuggedSourceFiles& operator=(DebuggedSourceFiles&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const DebuggedSourceFiles& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DebuggedSourceFiles* internal_default_instance() {
    return reinterpret_cast<const DebuggedSourceFiles*>(
               &_DebuggedSourceFiles_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void UnsafeArenaSwap(DebuggedSourceFiles* other);
  void Swap(DebuggedSourceFiles* other);
  friend void swap(DebuggedSourceFiles& a, DebuggedSourceFiles& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline DebuggedSourceFiles* New() const final {
    return CreateMaybeMessage<DebuggedSourceFiles>(NULL);
  }

  DebuggedSourceFiles* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<DebuggedSourceFiles>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const DebuggedSourceFiles& from);
  void MergeFrom(const DebuggedSourceFiles& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DebuggedSourceFiles* other);
  protected:
  explicit DebuggedSourceFiles(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.DebuggedSourceFile source_files = 1;
  int source_files_size() const;
  void clear_source_files();
  static const int kSourceFilesFieldNumber = 1;
  ::tensorflow::DebuggedSourceFile* mutable_source_files(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::DebuggedSourceFile >*
      mutable_source_files();
  const ::tensorflow::DebuggedSourceFile& source_files(int index) const;
  ::tensorflow::DebuggedSourceFile* add_source_files();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::DebuggedSourceFile >&
      source_files() const;

  // @@protoc_insertion_point(class_scope:tensorflow.DebuggedSourceFiles)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::DebuggedSourceFile > source_files_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// DebugTensorWatch

// string node_name = 1;
inline void DebugTensorWatch::clear_node_name() {
  node_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& DebugTensorWatch::node_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugTensorWatch.node_name)
  return node_name_.Get();
}
inline void DebugTensorWatch::set_node_name(const ::std::string& value) {
  
  node_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DebugTensorWatch.node_name)
}
#if LANG_CXX11
inline void DebugTensorWatch::set_node_name(::std::string&& value) {
  
  node_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DebugTensorWatch.node_name)
}
#endif
inline void DebugTensorWatch::set_node_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  node_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DebugTensorWatch.node_name)
}
inline void DebugTensorWatch::set_node_name(const char* value,
    size_t size) {
  
  node_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DebugTensorWatch.node_name)
}
inline ::std::string* DebugTensorWatch::mutable_node_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugTensorWatch.node_name)
  return node_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* DebugTensorWatch::release_node_name() {
  // @@protoc_insertion_point(field_release:tensorflow.DebugTensorWatch.node_name)
  
  return node_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DebugTensorWatch::set_allocated_node_name(::std::string* node_name) {
  if (node_name != NULL) {
    
  } else {
    
  }
  node_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), node_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DebugTensorWatch.node_name)
}
inline ::std::string* DebugTensorWatch::unsafe_arena_release_node_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebugTensorWatch.node_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return node_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DebugTensorWatch::unsafe_arena_set_allocated_node_name(
    ::std::string* node_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (node_name != NULL) {
    
  } else {
    
  }
  node_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      node_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebugTensorWatch.node_name)
}

// int32 output_slot = 2;
inline void DebugTensorWatch::clear_output_slot() {
  output_slot_ = 0;
}
inline ::google::protobuf::int32 DebugTensorWatch::output_slot() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugTensorWatch.output_slot)
  return output_slot_;
}
inline void DebugTensorWatch::set_output_slot(::google::protobuf::int32 value) {
  
  output_slot_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DebugTensorWatch.output_slot)
}

// repeated string debug_ops = 3;
inline int DebugTensorWatch::debug_ops_size() const {
  return debug_ops_.size();
}
inline void DebugTensorWatch::clear_debug_ops() {
  debug_ops_.Clear();
}
inline const ::std::string& DebugTensorWatch::debug_ops(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugTensorWatch.debug_ops)
  return debug_ops_.Get(index);
}
inline ::std::string* DebugTensorWatch::mutable_debug_ops(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugTensorWatch.debug_ops)
  return debug_ops_.Mutable(index);
}
inline void DebugTensorWatch::set_debug_ops(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.DebugTensorWatch.debug_ops)
  debug_ops_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void DebugTensorWatch::set_debug_ops(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.DebugTensorWatch.debug_ops)
  debug_ops_.Mutable(index)->assign(std::move(value));
}
#endif
inline void DebugTensorWatch::set_debug_ops(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  debug_ops_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.DebugTensorWatch.debug_ops)
}
inline void DebugTensorWatch::set_debug_ops(int index, const char* value, size_t size) {
  debug_ops_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DebugTensorWatch.debug_ops)
}
inline ::std::string* DebugTensorWatch::add_debug_ops() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.DebugTensorWatch.debug_ops)
  return debug_ops_.Add();
}
inline void DebugTensorWatch::add_debug_ops(const ::std::string& value) {
  debug_ops_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.DebugTensorWatch.debug_ops)
}
#if LANG_CXX11
inline void DebugTensorWatch::add_debug_ops(::std::string&& value) {
  debug_ops_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.DebugTensorWatch.debug_ops)
}
#endif
inline void DebugTensorWatch::add_debug_ops(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  debug_ops_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.DebugTensorWatch.debug_ops)
}
inline void DebugTensorWatch::add_debug_ops(const char* value, size_t size) {
  debug_ops_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.DebugTensorWatch.debug_ops)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
DebugTensorWatch::debug_ops() const {
  // @@protoc_insertion_point(field_list:tensorflow.DebugTensorWatch.debug_ops)
  return debug_ops_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
DebugTensorWatch::mutable_debug_ops() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.DebugTensorWatch.debug_ops)
  return &debug_ops_;
}

// repeated string debug_urls = 4;
inline int DebugTensorWatch::debug_urls_size() const {
  return debug_urls_.size();
}
inline void DebugTensorWatch::clear_debug_urls() {
  debug_urls_.Clear();
}
inline const ::std::string& DebugTensorWatch::debug_urls(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugTensorWatch.debug_urls)
  return debug_urls_.Get(index);
}
inline ::std::string* DebugTensorWatch::mutable_debug_urls(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugTensorWatch.debug_urls)
  return debug_urls_.Mutable(index);
}
inline void DebugTensorWatch::set_debug_urls(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.DebugTensorWatch.debug_urls)
  debug_urls_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void DebugTensorWatch::set_debug_urls(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.DebugTensorWatch.debug_urls)
  debug_urls_.Mutable(index)->assign(std::move(value));
}
#endif
inline void DebugTensorWatch::set_debug_urls(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  debug_urls_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.DebugTensorWatch.debug_urls)
}
inline void DebugTensorWatch::set_debug_urls(int index, const char* value, size_t size) {
  debug_urls_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DebugTensorWatch.debug_urls)
}
inline ::std::string* DebugTensorWatch::add_debug_urls() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.DebugTensorWatch.debug_urls)
  return debug_urls_.Add();
}
inline void DebugTensorWatch::add_debug_urls(const ::std::string& value) {
  debug_urls_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.DebugTensorWatch.debug_urls)
}
#if LANG_CXX11
inline void DebugTensorWatch::add_debug_urls(::std::string&& value) {
  debug_urls_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.DebugTensorWatch.debug_urls)
}
#endif
inline void DebugTensorWatch::add_debug_urls(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  debug_urls_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.DebugTensorWatch.debug_urls)
}
inline void DebugTensorWatch::add_debug_urls(const char* value, size_t size) {
  debug_urls_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.DebugTensorWatch.debug_urls)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
DebugTensorWatch::debug_urls() const {
  // @@protoc_insertion_point(field_list:tensorflow.DebugTensorWatch.debug_urls)
  return debug_urls_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
DebugTensorWatch::mutable_debug_urls() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.DebugTensorWatch.debug_urls)
  return &debug_urls_;
}

// bool tolerate_debug_op_creation_failures = 5;
inline void DebugTensorWatch::clear_tolerate_debug_op_creation_failures() {
  tolerate_debug_op_creation_failures_ = false;
}
inline bool DebugTensorWatch::tolerate_debug_op_creation_failures() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugTensorWatch.tolerate_debug_op_creation_failures)
  return tolerate_debug_op_creation_failures_;
}
inline void DebugTensorWatch::set_tolerate_debug_op_creation_failures(bool value) {
  
  tolerate_debug_op_creation_failures_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DebugTensorWatch.tolerate_debug_op_creation_failures)
}

// -------------------------------------------------------------------

// DebugOptions

// repeated .tensorflow.DebugTensorWatch debug_tensor_watch_opts = 4;
inline int DebugOptions::debug_tensor_watch_opts_size() const {
  return debug_tensor_watch_opts_.size();
}
inline void DebugOptions::clear_debug_tensor_watch_opts() {
  debug_tensor_watch_opts_.Clear();
}
inline ::tensorflow::DebugTensorWatch* DebugOptions::mutable_debug_tensor_watch_opts(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugOptions.debug_tensor_watch_opts)
  return debug_tensor_watch_opts_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::DebugTensorWatch >*
DebugOptions::mutable_debug_tensor_watch_opts() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.DebugOptions.debug_tensor_watch_opts)
  return &debug_tensor_watch_opts_;
}
inline const ::tensorflow::DebugTensorWatch& DebugOptions::debug_tensor_watch_opts(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugOptions.debug_tensor_watch_opts)
  return debug_tensor_watch_opts_.Get(index);
}
inline ::tensorflow::DebugTensorWatch* DebugOptions::add_debug_tensor_watch_opts() {
  // @@protoc_insertion_point(field_add:tensorflow.DebugOptions.debug_tensor_watch_opts)
  return debug_tensor_watch_opts_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::DebugTensorWatch >&
DebugOptions::debug_tensor_watch_opts() const {
  // @@protoc_insertion_point(field_list:tensorflow.DebugOptions.debug_tensor_watch_opts)
  return debug_tensor_watch_opts_;
}

// int64 global_step = 10;
inline void DebugOptions::clear_global_step() {
  global_step_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 DebugOptions::global_step() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugOptions.global_step)
  return global_step_;
}
inline void DebugOptions::set_global_step(::google::protobuf::int64 value) {
  
  global_step_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DebugOptions.global_step)
}

// bool reset_disk_byte_usage = 11;
inline void DebugOptions::clear_reset_disk_byte_usage() {
  reset_disk_byte_usage_ = false;
}
inline bool DebugOptions::reset_disk_byte_usage() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugOptions.reset_disk_byte_usage)
  return reset_disk_byte_usage_;
}
inline void DebugOptions::set_reset_disk_byte_usage(bool value) {
  
  reset_disk_byte_usage_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DebugOptions.reset_disk_byte_usage)
}

// -------------------------------------------------------------------

// DebuggedSourceFile

// string host = 1;
inline void DebuggedSourceFile::clear_host() {
  host_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& DebuggedSourceFile::host() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedSourceFile.host)
  return host_.Get();
}
inline void DebuggedSourceFile::set_host(const ::std::string& value) {
  
  host_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedSourceFile.host)
}
#if LANG_CXX11
inline void DebuggedSourceFile::set_host(::std::string&& value) {
  
  host_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DebuggedSourceFile.host)
}
#endif
inline void DebuggedSourceFile::set_host(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  host_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DebuggedSourceFile.host)
}
inline void DebuggedSourceFile::set_host(const char* value,
    size_t size) {
  
  host_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DebuggedSourceFile.host)
}
inline ::std::string* DebuggedSourceFile::mutable_host() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DebuggedSourceFile.host)
  return host_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* DebuggedSourceFile::release_host() {
  // @@protoc_insertion_point(field_release:tensorflow.DebuggedSourceFile.host)
  
  return host_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DebuggedSourceFile::set_allocated_host(::std::string* host) {
  if (host != NULL) {
    
  } else {
    
  }
  host_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), host,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DebuggedSourceFile.host)
}
inline ::std::string* DebuggedSourceFile::unsafe_arena_release_host() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebuggedSourceFile.host)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return host_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DebuggedSourceFile::unsafe_arena_set_allocated_host(
    ::std::string* host) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (host != NULL) {
    
  } else {
    
  }
  host_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      host, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebuggedSourceFile.host)
}

// string file_path = 2;
inline void DebuggedSourceFile::clear_file_path() {
  file_path_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& DebuggedSourceFile::file_path() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedSourceFile.file_path)
  return file_path_.Get();
}
inline void DebuggedSourceFile::set_file_path(const ::std::string& value) {
  
  file_path_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedSourceFile.file_path)
}
#if LANG_CXX11
inline void DebuggedSourceFile::set_file_path(::std::string&& value) {
  
  file_path_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DebuggedSourceFile.file_path)
}
#endif
inline void DebuggedSourceFile::set_file_path(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  file_path_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DebuggedSourceFile.file_path)
}
inline void DebuggedSourceFile::set_file_path(const char* value,
    size_t size) {
  
  file_path_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DebuggedSourceFile.file_path)
}
inline ::std::string* DebuggedSourceFile::mutable_file_path() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DebuggedSourceFile.file_path)
  return file_path_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* DebuggedSourceFile::release_file_path() {
  // @@protoc_insertion_point(field_release:tensorflow.DebuggedSourceFile.file_path)
  
  return file_path_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DebuggedSourceFile::set_allocated_file_path(::std::string* file_path) {
  if (file_path != NULL) {
    
  } else {
    
  }
  file_path_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), file_path,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DebuggedSourceFile.file_path)
}
inline ::std::string* DebuggedSourceFile::unsafe_arena_release_file_path() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebuggedSourceFile.file_path)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return file_path_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DebuggedSourceFile::unsafe_arena_set_allocated_file_path(
    ::std::string* file_path) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (file_path != NULL) {
    
  } else {
    
  }
  file_path_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      file_path, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebuggedSourceFile.file_path)
}

// int64 last_modified = 3;
inline void DebuggedSourceFile::clear_last_modified() {
  last_modified_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 DebuggedSourceFile::last_modified() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedSourceFile.last_modified)
  return last_modified_;
}
inline void DebuggedSourceFile::set_last_modified(::google::protobuf::int64 value) {
  
  last_modified_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedSourceFile.last_modified)
}

// int64 bytes = 4;
inline void DebuggedSourceFile::clear_bytes() {
  bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 DebuggedSourceFile::bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedSourceFile.bytes)
  return bytes_;
}
inline void DebuggedSourceFile::set_bytes(::google::protobuf::int64 value) {
  
  bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedSourceFile.bytes)
}

// repeated string lines = 5;
inline int DebuggedSourceFile::lines_size() const {
  return lines_.size();
}
inline void DebuggedSourceFile::clear_lines() {
  lines_.Clear();
}
inline const ::std::string& DebuggedSourceFile::lines(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedSourceFile.lines)
  return lines_.Get(index);
}
inline ::std::string* DebuggedSourceFile::mutable_lines(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.DebuggedSourceFile.lines)
  return lines_.Mutable(index);
}
inline void DebuggedSourceFile::set_lines(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedSourceFile.lines)
  lines_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void DebuggedSourceFile::set_lines(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedSourceFile.lines)
  lines_.Mutable(index)->assign(std::move(value));
}
#endif
inline void DebuggedSourceFile::set_lines(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  lines_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.DebuggedSourceFile.lines)
}
inline void DebuggedSourceFile::set_lines(int index, const char* value, size_t size) {
  lines_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DebuggedSourceFile.lines)
}
inline ::std::string* DebuggedSourceFile::add_lines() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.DebuggedSourceFile.lines)
  return lines_.Add();
}
inline void DebuggedSourceFile::add_lines(const ::std::string& value) {
  lines_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.DebuggedSourceFile.lines)
}
#if LANG_CXX11
inline void DebuggedSourceFile::add_lines(::std::string&& value) {
  lines_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.DebuggedSourceFile.lines)
}
#endif
inline void DebuggedSourceFile::add_lines(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  lines_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.DebuggedSourceFile.lines)
}
inline void DebuggedSourceFile::add_lines(const char* value, size_t size) {
  lines_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.DebuggedSourceFile.lines)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
DebuggedSourceFile::lines() const {
  // @@protoc_insertion_point(field_list:tensorflow.DebuggedSourceFile.lines)
  return lines_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
DebuggedSourceFile::mutable_lines() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.DebuggedSourceFile.lines)
  return &lines_;
}

// -------------------------------------------------------------------

// DebuggedSourceFiles

// repeated .tensorflow.DebuggedSourceFile source_files = 1;
inline int DebuggedSourceFiles::source_files_size() const {
  return source_files_.size();
}
inline void DebuggedSourceFiles::clear_source_files() {
  source_files_.Clear();
}
inline ::tensorflow::DebuggedSourceFile* DebuggedSourceFiles::mutable_source_files(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.DebuggedSourceFiles.source_files)
  return source_files_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::DebuggedSourceFile >*
DebuggedSourceFiles::mutable_source_files() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.DebuggedSourceFiles.source_files)
  return &source_files_;
}
inline const ::tensorflow::DebuggedSourceFile& DebuggedSourceFiles::source_files(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedSourceFiles.source_files)
  return source_files_.Get(index);
}
inline ::tensorflow::DebuggedSourceFile* DebuggedSourceFiles::add_source_files() {
  // @@protoc_insertion_point(field_add:tensorflow.DebuggedSourceFiles.source_files)
  return source_files_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::DebuggedSourceFile >&
DebuggedSourceFiles::source_files() const {
  // @@protoc_insertion_point(field_list:tensorflow.DebuggedSourceFiles.source_files)
  return source_files_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto

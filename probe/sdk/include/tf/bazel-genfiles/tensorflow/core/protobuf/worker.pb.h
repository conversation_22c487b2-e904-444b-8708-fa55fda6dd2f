// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/worker.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fworker_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fworker_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/any.pb.h>
#include "tensorflow/core/framework/cost_graph.pb.h"
#include "tensorflow/core/framework/device_attributes.pb.h"
#include "tensorflow/core/framework/graph.pb.h"
#include "tensorflow/core/framework/step_stats.pb.h"
#include "tensorflow/core/framework/tensor.pb.h"
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
#include "tensorflow/core/lib/core/error_codes.pb.h"
#include "tensorflow/core/protobuf/config.pb.h"
#include "tensorflow/core/protobuf/debug.pb.h"
#include "tensorflow/core/protobuf/named_tensor.pb.h"
#include "tensorflow/core/protobuf/tensorflow_server.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto 

namespace protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[36];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto
namespace tensorflow {
class CleanupAllRequest;
class CleanupAllRequestDefaultTypeInternal;
extern CleanupAllRequestDefaultTypeInternal _CleanupAllRequest_default_instance_;
class CleanupAllResponse;
class CleanupAllResponseDefaultTypeInternal;
extern CleanupAllResponseDefaultTypeInternal _CleanupAllResponse_default_instance_;
class CleanupGraphRequest;
class CleanupGraphRequestDefaultTypeInternal;
extern CleanupGraphRequestDefaultTypeInternal _CleanupGraphRequest_default_instance_;
class CleanupGraphResponse;
class CleanupGraphResponseDefaultTypeInternal;
extern CleanupGraphResponseDefaultTypeInternal _CleanupGraphResponse_default_instance_;
class CompleteGroupRequest;
class CompleteGroupRequestDefaultTypeInternal;
extern CompleteGroupRequestDefaultTypeInternal _CompleteGroupRequest_default_instance_;
class CompleteGroupResponse;
class CompleteGroupResponseDefaultTypeInternal;
extern CompleteGroupResponseDefaultTypeInternal _CompleteGroupResponse_default_instance_;
class CompleteInstanceRequest;
class CompleteInstanceRequestDefaultTypeInternal;
extern CompleteInstanceRequestDefaultTypeInternal _CompleteInstanceRequest_default_instance_;
class CompleteInstanceResponse;
class CompleteInstanceResponseDefaultTypeInternal;
extern CompleteInstanceResponseDefaultTypeInternal _CompleteInstanceResponse_default_instance_;
class CreateWorkerSessionRequest;
class CreateWorkerSessionRequestDefaultTypeInternal;
extern CreateWorkerSessionRequestDefaultTypeInternal _CreateWorkerSessionRequest_default_instance_;
class CreateWorkerSessionResponse;
class CreateWorkerSessionResponseDefaultTypeInternal;
extern CreateWorkerSessionResponseDefaultTypeInternal _CreateWorkerSessionResponse_default_instance_;
class DeleteWorkerSessionRequest;
class DeleteWorkerSessionRequestDefaultTypeInternal;
extern DeleteWorkerSessionRequestDefaultTypeInternal _DeleteWorkerSessionRequest_default_instance_;
class DeleteWorkerSessionResponse;
class DeleteWorkerSessionResponseDefaultTypeInternal;
extern DeleteWorkerSessionResponseDefaultTypeInternal _DeleteWorkerSessionResponse_default_instance_;
class DeregisterGraphRequest;
class DeregisterGraphRequestDefaultTypeInternal;
extern DeregisterGraphRequestDefaultTypeInternal _DeregisterGraphRequest_default_instance_;
class DeregisterGraphResponse;
class DeregisterGraphResponseDefaultTypeInternal;
extern DeregisterGraphResponseDefaultTypeInternal _DeregisterGraphResponse_default_instance_;
class ExecutorOpts;
class ExecutorOptsDefaultTypeInternal;
extern ExecutorOptsDefaultTypeInternal _ExecutorOpts_default_instance_;
class GetStatusRequest;
class GetStatusRequestDefaultTypeInternal;
extern GetStatusRequestDefaultTypeInternal _GetStatusRequest_default_instance_;
class GetStatusResponse;
class GetStatusResponseDefaultTypeInternal;
extern GetStatusResponseDefaultTypeInternal _GetStatusResponse_default_instance_;
class GetStepSequenceRequest;
class GetStepSequenceRequestDefaultTypeInternal;
extern GetStepSequenceRequestDefaultTypeInternal _GetStepSequenceRequest_default_instance_;
class GetStepSequenceResponse;
class GetStepSequenceResponseDefaultTypeInternal;
extern GetStepSequenceResponseDefaultTypeInternal _GetStepSequenceResponse_default_instance_;
class LabeledStepStats;
class LabeledStepStatsDefaultTypeInternal;
extern LabeledStepStatsDefaultTypeInternal _LabeledStepStats_default_instance_;
class LoggingRequest;
class LoggingRequestDefaultTypeInternal;
extern LoggingRequestDefaultTypeInternal _LoggingRequest_default_instance_;
class LoggingResponse;
class LoggingResponseDefaultTypeInternal;
extern LoggingResponseDefaultTypeInternal _LoggingResponse_default_instance_;
class MarkRecvFinishedRequest;
class MarkRecvFinishedRequestDefaultTypeInternal;
extern MarkRecvFinishedRequestDefaultTypeInternal _MarkRecvFinishedRequest_default_instance_;
class MarkRecvFinishedResponse;
class MarkRecvFinishedResponseDefaultTypeInternal;
extern MarkRecvFinishedResponseDefaultTypeInternal _MarkRecvFinishedResponse_default_instance_;
class RecvBufRequest;
class RecvBufRequestDefaultTypeInternal;
extern RecvBufRequestDefaultTypeInternal _RecvBufRequest_default_instance_;
class RecvBufResponse;
class RecvBufResponseDefaultTypeInternal;
extern RecvBufResponseDefaultTypeInternal _RecvBufResponse_default_instance_;
class RecvTensorRequest;
class RecvTensorRequestDefaultTypeInternal;
extern RecvTensorRequestDefaultTypeInternal _RecvTensorRequest_default_instance_;
class RecvTensorResponse;
class RecvTensorResponseDefaultTypeInternal;
extern RecvTensorResponseDefaultTypeInternal _RecvTensorResponse_default_instance_;
class RegisterGraphRequest;
class RegisterGraphRequestDefaultTypeInternal;
extern RegisterGraphRequestDefaultTypeInternal _RegisterGraphRequest_default_instance_;
class RegisterGraphResponse;
class RegisterGraphResponseDefaultTypeInternal;
extern RegisterGraphResponseDefaultTypeInternal _RegisterGraphResponse_default_instance_;
class RunGraphRequest;
class RunGraphRequestDefaultTypeInternal;
extern RunGraphRequestDefaultTypeInternal _RunGraphRequest_default_instance_;
class RunGraphResponse;
class RunGraphResponseDefaultTypeInternal;
extern RunGraphResponseDefaultTypeInternal _RunGraphResponse_default_instance_;
class StepSequence;
class StepSequenceDefaultTypeInternal;
extern StepSequenceDefaultTypeInternal _StepSequence_default_instance_;
class TraceOpts;
class TraceOptsDefaultTypeInternal;
extern TraceOptsDefaultTypeInternal _TraceOpts_default_instance_;
class TracingRequest;
class TracingRequestDefaultTypeInternal;
extern TracingRequestDefaultTypeInternal _TracingRequest_default_instance_;
class TracingResponse;
class TracingResponseDefaultTypeInternal;
extern TracingResponseDefaultTypeInternal _TracingResponse_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::CleanupAllRequest* Arena::CreateMaybeMessage<::tensorflow::CleanupAllRequest>(Arena*);
template<> ::tensorflow::CleanupAllResponse* Arena::CreateMaybeMessage<::tensorflow::CleanupAllResponse>(Arena*);
template<> ::tensorflow::CleanupGraphRequest* Arena::CreateMaybeMessage<::tensorflow::CleanupGraphRequest>(Arena*);
template<> ::tensorflow::CleanupGraphResponse* Arena::CreateMaybeMessage<::tensorflow::CleanupGraphResponse>(Arena*);
template<> ::tensorflow::CompleteGroupRequest* Arena::CreateMaybeMessage<::tensorflow::CompleteGroupRequest>(Arena*);
template<> ::tensorflow::CompleteGroupResponse* Arena::CreateMaybeMessage<::tensorflow::CompleteGroupResponse>(Arena*);
template<> ::tensorflow::CompleteInstanceRequest* Arena::CreateMaybeMessage<::tensorflow::CompleteInstanceRequest>(Arena*);
template<> ::tensorflow::CompleteInstanceResponse* Arena::CreateMaybeMessage<::tensorflow::CompleteInstanceResponse>(Arena*);
template<> ::tensorflow::CreateWorkerSessionRequest* Arena::CreateMaybeMessage<::tensorflow::CreateWorkerSessionRequest>(Arena*);
template<> ::tensorflow::CreateWorkerSessionResponse* Arena::CreateMaybeMessage<::tensorflow::CreateWorkerSessionResponse>(Arena*);
template<> ::tensorflow::DeleteWorkerSessionRequest* Arena::CreateMaybeMessage<::tensorflow::DeleteWorkerSessionRequest>(Arena*);
template<> ::tensorflow::DeleteWorkerSessionResponse* Arena::CreateMaybeMessage<::tensorflow::DeleteWorkerSessionResponse>(Arena*);
template<> ::tensorflow::DeregisterGraphRequest* Arena::CreateMaybeMessage<::tensorflow::DeregisterGraphRequest>(Arena*);
template<> ::tensorflow::DeregisterGraphResponse* Arena::CreateMaybeMessage<::tensorflow::DeregisterGraphResponse>(Arena*);
template<> ::tensorflow::ExecutorOpts* Arena::CreateMaybeMessage<::tensorflow::ExecutorOpts>(Arena*);
template<> ::tensorflow::GetStatusRequest* Arena::CreateMaybeMessage<::tensorflow::GetStatusRequest>(Arena*);
template<> ::tensorflow::GetStatusResponse* Arena::CreateMaybeMessage<::tensorflow::GetStatusResponse>(Arena*);
template<> ::tensorflow::GetStepSequenceRequest* Arena::CreateMaybeMessage<::tensorflow::GetStepSequenceRequest>(Arena*);
template<> ::tensorflow::GetStepSequenceResponse* Arena::CreateMaybeMessage<::tensorflow::GetStepSequenceResponse>(Arena*);
template<> ::tensorflow::LabeledStepStats* Arena::CreateMaybeMessage<::tensorflow::LabeledStepStats>(Arena*);
template<> ::tensorflow::LoggingRequest* Arena::CreateMaybeMessage<::tensorflow::LoggingRequest>(Arena*);
template<> ::tensorflow::LoggingResponse* Arena::CreateMaybeMessage<::tensorflow::LoggingResponse>(Arena*);
template<> ::tensorflow::MarkRecvFinishedRequest* Arena::CreateMaybeMessage<::tensorflow::MarkRecvFinishedRequest>(Arena*);
template<> ::tensorflow::MarkRecvFinishedResponse* Arena::CreateMaybeMessage<::tensorflow::MarkRecvFinishedResponse>(Arena*);
template<> ::tensorflow::RecvBufRequest* Arena::CreateMaybeMessage<::tensorflow::RecvBufRequest>(Arena*);
template<> ::tensorflow::RecvBufResponse* Arena::CreateMaybeMessage<::tensorflow::RecvBufResponse>(Arena*);
template<> ::tensorflow::RecvTensorRequest* Arena::CreateMaybeMessage<::tensorflow::RecvTensorRequest>(Arena*);
template<> ::tensorflow::RecvTensorResponse* Arena::CreateMaybeMessage<::tensorflow::RecvTensorResponse>(Arena*);
template<> ::tensorflow::RegisterGraphRequest* Arena::CreateMaybeMessage<::tensorflow::RegisterGraphRequest>(Arena*);
template<> ::tensorflow::RegisterGraphResponse* Arena::CreateMaybeMessage<::tensorflow::RegisterGraphResponse>(Arena*);
template<> ::tensorflow::RunGraphRequest* Arena::CreateMaybeMessage<::tensorflow::RunGraphRequest>(Arena*);
template<> ::tensorflow::RunGraphResponse* Arena::CreateMaybeMessage<::tensorflow::RunGraphResponse>(Arena*);
template<> ::tensorflow::StepSequence* Arena::CreateMaybeMessage<::tensorflow::StepSequence>(Arena*);
template<> ::tensorflow::TraceOpts* Arena::CreateMaybeMessage<::tensorflow::TraceOpts>(Arena*);
template<> ::tensorflow::TracingRequest* Arena::CreateMaybeMessage<::tensorflow::TracingRequest>(Arena*);
template<> ::tensorflow::TracingResponse* Arena::CreateMaybeMessage<::tensorflow::TracingResponse>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

// ===================================================================

class GetStatusRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.GetStatusRequest) */ {
 public:
  GetStatusRequest();
  virtual ~GetStatusRequest();

  GetStatusRequest(const GetStatusRequest& from);

  inline GetStatusRequest& operator=(const GetStatusRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  GetStatusRequest(GetStatusRequest&& from) noexcept
    : GetStatusRequest() {
    *this = ::std::move(from);
  }

  inline GetStatusRequest& operator=(GetStatusRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const GetStatusRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GetStatusRequest* internal_default_instance() {
    return reinterpret_cast<const GetStatusRequest*>(
               &_GetStatusRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(GetStatusRequest* other);
  void Swap(GetStatusRequest* other);
  friend void swap(GetStatusRequest& a, GetStatusRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline GetStatusRequest* New() const final {
    return CreateMaybeMessage<GetStatusRequest>(NULL);
  }

  GetStatusRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<GetStatusRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const GetStatusRequest& from);
  void MergeFrom(const GetStatusRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetStatusRequest* other);
  protected:
  explicit GetStatusRequest(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.GetStatusRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class GetStatusResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.GetStatusResponse) */ {
 public:
  GetStatusResponse();
  virtual ~GetStatusResponse();

  GetStatusResponse(const GetStatusResponse& from);

  inline GetStatusResponse& operator=(const GetStatusResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  GetStatusResponse(GetStatusResponse&& from) noexcept
    : GetStatusResponse() {
    *this = ::std::move(from);
  }

  inline GetStatusResponse& operator=(GetStatusResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const GetStatusResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GetStatusResponse* internal_default_instance() {
    return reinterpret_cast<const GetStatusResponse*>(
               &_GetStatusResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void UnsafeArenaSwap(GetStatusResponse* other);
  void Swap(GetStatusResponse* other);
  friend void swap(GetStatusResponse& a, GetStatusResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline GetStatusResponse* New() const final {
    return CreateMaybeMessage<GetStatusResponse>(NULL);
  }

  GetStatusResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<GetStatusResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const GetStatusResponse& from);
  void MergeFrom(const GetStatusResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetStatusResponse* other);
  protected:
  explicit GetStatusResponse(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.DeviceAttributes device_attributes = 1;
  int device_attributes_size() const;
  void clear_device_attributes();
  static const int kDeviceAttributesFieldNumber = 1;
  ::tensorflow::DeviceAttributes* mutable_device_attributes(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
      mutable_device_attributes();
  const ::tensorflow::DeviceAttributes& device_attributes(int index) const;
  ::tensorflow::DeviceAttributes* add_device_attributes();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
      device_attributes() const;

  // @@protoc_insertion_point(class_scope:tensorflow.GetStatusResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::DeviceAttributes > device_attributes_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CreateWorkerSessionRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.CreateWorkerSessionRequest) */ {
 public:
  CreateWorkerSessionRequest();
  virtual ~CreateWorkerSessionRequest();

  CreateWorkerSessionRequest(const CreateWorkerSessionRequest& from);

  inline CreateWorkerSessionRequest& operator=(const CreateWorkerSessionRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CreateWorkerSessionRequest(CreateWorkerSessionRequest&& from) noexcept
    : CreateWorkerSessionRequest() {
    *this = ::std::move(from);
  }

  inline CreateWorkerSessionRequest& operator=(CreateWorkerSessionRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const CreateWorkerSessionRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CreateWorkerSessionRequest* internal_default_instance() {
    return reinterpret_cast<const CreateWorkerSessionRequest*>(
               &_CreateWorkerSessionRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void UnsafeArenaSwap(CreateWorkerSessionRequest* other);
  void Swap(CreateWorkerSessionRequest* other);
  friend void swap(CreateWorkerSessionRequest& a, CreateWorkerSessionRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CreateWorkerSessionRequest* New() const final {
    return CreateMaybeMessage<CreateWorkerSessionRequest>(NULL);
  }

  CreateWorkerSessionRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CreateWorkerSessionRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CreateWorkerSessionRequest& from);
  void MergeFrom(const CreateWorkerSessionRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CreateWorkerSessionRequest* other);
  protected:
  explicit CreateWorkerSessionRequest(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string session_handle = 1;
  void clear_session_handle();
  static const int kSessionHandleFieldNumber = 1;
  const ::std::string& session_handle() const;
  void set_session_handle(const ::std::string& value);
  #if LANG_CXX11
  void set_session_handle(::std::string&& value);
  #endif
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  ::std::string* mutable_session_handle();
  ::std::string* release_session_handle();
  void set_allocated_session_handle(::std::string* session_handle);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_session_handle();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      ::std::string* session_handle);

  // .tensorflow.ServerDef server_def = 2;
  bool has_server_def() const;
  void clear_server_def();
  static const int kServerDefFieldNumber = 2;
  private:
  const ::tensorflow::ServerDef& _internal_server_def() const;
  public:
  const ::tensorflow::ServerDef& server_def() const;
  ::tensorflow::ServerDef* release_server_def();
  ::tensorflow::ServerDef* mutable_server_def();
  void set_allocated_server_def(::tensorflow::ServerDef* server_def);
  void unsafe_arena_set_allocated_server_def(
      ::tensorflow::ServerDef* server_def);
  ::tensorflow::ServerDef* unsafe_arena_release_server_def();

  // bool isolate_session_state = 3;
  void clear_isolate_session_state();
  static const int kIsolateSessionStateFieldNumber = 3;
  bool isolate_session_state() const;
  void set_isolate_session_state(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.CreateWorkerSessionRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr session_handle_;
  ::tensorflow::ServerDef* server_def_;
  bool isolate_session_state_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CreateWorkerSessionResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.CreateWorkerSessionResponse) */ {
 public:
  CreateWorkerSessionResponse();
  virtual ~CreateWorkerSessionResponse();

  CreateWorkerSessionResponse(const CreateWorkerSessionResponse& from);

  inline CreateWorkerSessionResponse& operator=(const CreateWorkerSessionResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CreateWorkerSessionResponse(CreateWorkerSessionResponse&& from) noexcept
    : CreateWorkerSessionResponse() {
    *this = ::std::move(from);
  }

  inline CreateWorkerSessionResponse& operator=(CreateWorkerSessionResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const CreateWorkerSessionResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CreateWorkerSessionResponse* internal_default_instance() {
    return reinterpret_cast<const CreateWorkerSessionResponse*>(
               &_CreateWorkerSessionResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void UnsafeArenaSwap(CreateWorkerSessionResponse* other);
  void Swap(CreateWorkerSessionResponse* other);
  friend void swap(CreateWorkerSessionResponse& a, CreateWorkerSessionResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CreateWorkerSessionResponse* New() const final {
    return CreateMaybeMessage<CreateWorkerSessionResponse>(NULL);
  }

  CreateWorkerSessionResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CreateWorkerSessionResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CreateWorkerSessionResponse& from);
  void MergeFrom(const CreateWorkerSessionResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CreateWorkerSessionResponse* other);
  protected:
  explicit CreateWorkerSessionResponse(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.CreateWorkerSessionResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class DeleteWorkerSessionRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.DeleteWorkerSessionRequest) */ {
 public:
  DeleteWorkerSessionRequest();
  virtual ~DeleteWorkerSessionRequest();

  DeleteWorkerSessionRequest(const DeleteWorkerSessionRequest& from);

  inline DeleteWorkerSessionRequest& operator=(const DeleteWorkerSessionRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  DeleteWorkerSessionRequest(DeleteWorkerSessionRequest&& from) noexcept
    : DeleteWorkerSessionRequest() {
    *this = ::std::move(from);
  }

  inline DeleteWorkerSessionRequest& operator=(DeleteWorkerSessionRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const DeleteWorkerSessionRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DeleteWorkerSessionRequest* internal_default_instance() {
    return reinterpret_cast<const DeleteWorkerSessionRequest*>(
               &_DeleteWorkerSessionRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void UnsafeArenaSwap(DeleteWorkerSessionRequest* other);
  void Swap(DeleteWorkerSessionRequest* other);
  friend void swap(DeleteWorkerSessionRequest& a, DeleteWorkerSessionRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline DeleteWorkerSessionRequest* New() const final {
    return CreateMaybeMessage<DeleteWorkerSessionRequest>(NULL);
  }

  DeleteWorkerSessionRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<DeleteWorkerSessionRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const DeleteWorkerSessionRequest& from);
  void MergeFrom(const DeleteWorkerSessionRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeleteWorkerSessionRequest* other);
  protected:
  explicit DeleteWorkerSessionRequest(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string session_handle = 1;
  void clear_session_handle();
  static const int kSessionHandleFieldNumber = 1;
  const ::std::string& session_handle() const;
  void set_session_handle(const ::std::string& value);
  #if LANG_CXX11
  void set_session_handle(::std::string&& value);
  #endif
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  ::std::string* mutable_session_handle();
  ::std::string* release_session_handle();
  void set_allocated_session_handle(::std::string* session_handle);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_session_handle();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      ::std::string* session_handle);

  // @@protoc_insertion_point(class_scope:tensorflow.DeleteWorkerSessionRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr session_handle_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class DeleteWorkerSessionResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.DeleteWorkerSessionResponse) */ {
 public:
  DeleteWorkerSessionResponse();
  virtual ~DeleteWorkerSessionResponse();

  DeleteWorkerSessionResponse(const DeleteWorkerSessionResponse& from);

  inline DeleteWorkerSessionResponse& operator=(const DeleteWorkerSessionResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  DeleteWorkerSessionResponse(DeleteWorkerSessionResponse&& from) noexcept
    : DeleteWorkerSessionResponse() {
    *this = ::std::move(from);
  }

  inline DeleteWorkerSessionResponse& operator=(DeleteWorkerSessionResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const DeleteWorkerSessionResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DeleteWorkerSessionResponse* internal_default_instance() {
    return reinterpret_cast<const DeleteWorkerSessionResponse*>(
               &_DeleteWorkerSessionResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  void UnsafeArenaSwap(DeleteWorkerSessionResponse* other);
  void Swap(DeleteWorkerSessionResponse* other);
  friend void swap(DeleteWorkerSessionResponse& a, DeleteWorkerSessionResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline DeleteWorkerSessionResponse* New() const final {
    return CreateMaybeMessage<DeleteWorkerSessionResponse>(NULL);
  }

  DeleteWorkerSessionResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<DeleteWorkerSessionResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const DeleteWorkerSessionResponse& from);
  void MergeFrom(const DeleteWorkerSessionResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeleteWorkerSessionResponse* other);
  protected:
  explicit DeleteWorkerSessionResponse(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.DeleteWorkerSessionResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class RegisterGraphRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.RegisterGraphRequest) */ {
 public:
  RegisterGraphRequest();
  virtual ~RegisterGraphRequest();

  RegisterGraphRequest(const RegisterGraphRequest& from);

  inline RegisterGraphRequest& operator=(const RegisterGraphRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  RegisterGraphRequest(RegisterGraphRequest&& from) noexcept
    : RegisterGraphRequest() {
    *this = ::std::move(from);
  }

  inline RegisterGraphRequest& operator=(RegisterGraphRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const RegisterGraphRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RegisterGraphRequest* internal_default_instance() {
    return reinterpret_cast<const RegisterGraphRequest*>(
               &_RegisterGraphRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  void UnsafeArenaSwap(RegisterGraphRequest* other);
  void Swap(RegisterGraphRequest* other);
  friend void swap(RegisterGraphRequest& a, RegisterGraphRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RegisterGraphRequest* New() const final {
    return CreateMaybeMessage<RegisterGraphRequest>(NULL);
  }

  RegisterGraphRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<RegisterGraphRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const RegisterGraphRequest& from);
  void MergeFrom(const RegisterGraphRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RegisterGraphRequest* other);
  protected:
  explicit RegisterGraphRequest(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string session_handle = 1;
  void clear_session_handle();
  static const int kSessionHandleFieldNumber = 1;
  const ::std::string& session_handle() const;
  void set_session_handle(const ::std::string& value);
  #if LANG_CXX11
  void set_session_handle(::std::string&& value);
  #endif
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  ::std::string* mutable_session_handle();
  ::std::string* release_session_handle();
  void set_allocated_session_handle(::std::string* session_handle);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_session_handle();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      ::std::string* session_handle);

  // .tensorflow.GraphDef graph_def = 2;
  bool has_graph_def() const;
  void clear_graph_def();
  static const int kGraphDefFieldNumber = 2;
  private:
  const ::tensorflow::GraphDef& _internal_graph_def() const;
  public:
  const ::tensorflow::GraphDef& graph_def() const;
  ::tensorflow::GraphDef* release_graph_def();
  ::tensorflow::GraphDef* mutable_graph_def();
  void set_allocated_graph_def(::tensorflow::GraphDef* graph_def);
  void unsafe_arena_set_allocated_graph_def(
      ::tensorflow::GraphDef* graph_def);
  ::tensorflow::GraphDef* unsafe_arena_release_graph_def();

  // .tensorflow.GraphOptions graph_options = 4;
  bool has_graph_options() const;
  void clear_graph_options();
  static const int kGraphOptionsFieldNumber = 4;
  private:
  const ::tensorflow::GraphOptions& _internal_graph_options() const;
  public:
  const ::tensorflow::GraphOptions& graph_options() const;
  ::tensorflow::GraphOptions* release_graph_options();
  ::tensorflow::GraphOptions* mutable_graph_options();
  void set_allocated_graph_options(::tensorflow::GraphOptions* graph_options);
  void unsafe_arena_set_allocated_graph_options(
      ::tensorflow::GraphOptions* graph_options);
  ::tensorflow::GraphOptions* unsafe_arena_release_graph_options();

  // .tensorflow.DebugOptions debug_options = 5;
  bool has_debug_options() const;
  void clear_debug_options();
  static const int kDebugOptionsFieldNumber = 5;
  private:
  const ::tensorflow::DebugOptions& _internal_debug_options() const;
  public:
  const ::tensorflow::DebugOptions& debug_options() const;
  ::tensorflow::DebugOptions* release_debug_options();
  ::tensorflow::DebugOptions* mutable_debug_options();
  void set_allocated_debug_options(::tensorflow::DebugOptions* debug_options);
  void unsafe_arena_set_allocated_debug_options(
      ::tensorflow::DebugOptions* debug_options);
  ::tensorflow::DebugOptions* unsafe_arena_release_debug_options();

  // int64 collective_graph_key = 7;
  void clear_collective_graph_key();
  static const int kCollectiveGraphKeyFieldNumber = 7;
  ::google::protobuf::int64 collective_graph_key() const;
  void set_collective_graph_key(::google::protobuf::int64 value);

  // bool create_worker_session_called = 6;
  void clear_create_worker_session_called();
  static const int kCreateWorkerSessionCalledFieldNumber = 6;
  bool create_worker_session_called() const;
  void set_create_worker_session_called(bool value);

  // bool has_control_flow = 3 [deprecated = true];
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void clear_has_control_flow();
  GOOGLE_PROTOBUF_DEPRECATED_ATTR static const int kHasControlFlowFieldNumber = 3;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR bool has_control_flow() const;
  GOOGLE_PROTOBUF_DEPRECATED_ATTR void set_has_control_flow(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.RegisterGraphRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr session_handle_;
  ::tensorflow::GraphDef* graph_def_;
  ::tensorflow::GraphOptions* graph_options_;
  ::tensorflow::DebugOptions* debug_options_;
  ::google::protobuf::int64 collective_graph_key_;
  bool create_worker_session_called_;
  bool has_control_flow_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class RegisterGraphResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.RegisterGraphResponse) */ {
 public:
  RegisterGraphResponse();
  virtual ~RegisterGraphResponse();

  RegisterGraphResponse(const RegisterGraphResponse& from);

  inline RegisterGraphResponse& operator=(const RegisterGraphResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  RegisterGraphResponse(RegisterGraphResponse&& from) noexcept
    : RegisterGraphResponse() {
    *this = ::std::move(from);
  }

  inline RegisterGraphResponse& operator=(RegisterGraphResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const RegisterGraphResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RegisterGraphResponse* internal_default_instance() {
    return reinterpret_cast<const RegisterGraphResponse*>(
               &_RegisterGraphResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  void UnsafeArenaSwap(RegisterGraphResponse* other);
  void Swap(RegisterGraphResponse* other);
  friend void swap(RegisterGraphResponse& a, RegisterGraphResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RegisterGraphResponse* New() const final {
    return CreateMaybeMessage<RegisterGraphResponse>(NULL);
  }

  RegisterGraphResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<RegisterGraphResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const RegisterGraphResponse& from);
  void MergeFrom(const RegisterGraphResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RegisterGraphResponse* other);
  protected:
  explicit RegisterGraphResponse(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string graph_handle = 1;
  void clear_graph_handle();
  static const int kGraphHandleFieldNumber = 1;
  const ::std::string& graph_handle() const;
  void set_graph_handle(const ::std::string& value);
  #if LANG_CXX11
  void set_graph_handle(::std::string&& value);
  #endif
  void set_graph_handle(const char* value);
  void set_graph_handle(const char* value, size_t size);
  ::std::string* mutable_graph_handle();
  ::std::string* release_graph_handle();
  void set_allocated_graph_handle(::std::string* graph_handle);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_graph_handle();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_graph_handle(
      ::std::string* graph_handle);

  // @@protoc_insertion_point(class_scope:tensorflow.RegisterGraphResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr graph_handle_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class DeregisterGraphRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.DeregisterGraphRequest) */ {
 public:
  DeregisterGraphRequest();
  virtual ~DeregisterGraphRequest();

  DeregisterGraphRequest(const DeregisterGraphRequest& from);

  inline DeregisterGraphRequest& operator=(const DeregisterGraphRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  DeregisterGraphRequest(DeregisterGraphRequest&& from) noexcept
    : DeregisterGraphRequest() {
    *this = ::std::move(from);
  }

  inline DeregisterGraphRequest& operator=(DeregisterGraphRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const DeregisterGraphRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DeregisterGraphRequest* internal_default_instance() {
    return reinterpret_cast<const DeregisterGraphRequest*>(
               &_DeregisterGraphRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  void UnsafeArenaSwap(DeregisterGraphRequest* other);
  void Swap(DeregisterGraphRequest* other);
  friend void swap(DeregisterGraphRequest& a, DeregisterGraphRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline DeregisterGraphRequest* New() const final {
    return CreateMaybeMessage<DeregisterGraphRequest>(NULL);
  }

  DeregisterGraphRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<DeregisterGraphRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const DeregisterGraphRequest& from);
  void MergeFrom(const DeregisterGraphRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeregisterGraphRequest* other);
  protected:
  explicit DeregisterGraphRequest(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string graph_handle = 1;
  void clear_graph_handle();
  static const int kGraphHandleFieldNumber = 1;
  const ::std::string& graph_handle() const;
  void set_graph_handle(const ::std::string& value);
  #if LANG_CXX11
  void set_graph_handle(::std::string&& value);
  #endif
  void set_graph_handle(const char* value);
  void set_graph_handle(const char* value, size_t size);
  ::std::string* mutable_graph_handle();
  ::std::string* release_graph_handle();
  void set_allocated_graph_handle(::std::string* graph_handle);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_graph_handle();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_graph_handle(
      ::std::string* graph_handle);

  // string session_handle = 2;
  void clear_session_handle();
  static const int kSessionHandleFieldNumber = 2;
  const ::std::string& session_handle() const;
  void set_session_handle(const ::std::string& value);
  #if LANG_CXX11
  void set_session_handle(::std::string&& value);
  #endif
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  ::std::string* mutable_session_handle();
  ::std::string* release_session_handle();
  void set_allocated_session_handle(::std::string* session_handle);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_session_handle();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      ::std::string* session_handle);

  // bool create_worker_session_called = 3;
  void clear_create_worker_session_called();
  static const int kCreateWorkerSessionCalledFieldNumber = 3;
  bool create_worker_session_called() const;
  void set_create_worker_session_called(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.DeregisterGraphRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr graph_handle_;
  ::google::protobuf::internal::ArenaStringPtr session_handle_;
  bool create_worker_session_called_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class DeregisterGraphResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.DeregisterGraphResponse) */ {
 public:
  DeregisterGraphResponse();
  virtual ~DeregisterGraphResponse();

  DeregisterGraphResponse(const DeregisterGraphResponse& from);

  inline DeregisterGraphResponse& operator=(const DeregisterGraphResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  DeregisterGraphResponse(DeregisterGraphResponse&& from) noexcept
    : DeregisterGraphResponse() {
    *this = ::std::move(from);
  }

  inline DeregisterGraphResponse& operator=(DeregisterGraphResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const DeregisterGraphResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DeregisterGraphResponse* internal_default_instance() {
    return reinterpret_cast<const DeregisterGraphResponse*>(
               &_DeregisterGraphResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  void UnsafeArenaSwap(DeregisterGraphResponse* other);
  void Swap(DeregisterGraphResponse* other);
  friend void swap(DeregisterGraphResponse& a, DeregisterGraphResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline DeregisterGraphResponse* New() const final {
    return CreateMaybeMessage<DeregisterGraphResponse>(NULL);
  }

  DeregisterGraphResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<DeregisterGraphResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const DeregisterGraphResponse& from);
  void MergeFrom(const DeregisterGraphResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeregisterGraphResponse* other);
  protected:
  explicit DeregisterGraphResponse(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.DeregisterGraphResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CleanupAllRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.CleanupAllRequest) */ {
 public:
  CleanupAllRequest();
  virtual ~CleanupAllRequest();

  CleanupAllRequest(const CleanupAllRequest& from);

  inline CleanupAllRequest& operator=(const CleanupAllRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CleanupAllRequest(CleanupAllRequest&& from) noexcept
    : CleanupAllRequest() {
    *this = ::std::move(from);
  }

  inline CleanupAllRequest& operator=(CleanupAllRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const CleanupAllRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CleanupAllRequest* internal_default_instance() {
    return reinterpret_cast<const CleanupAllRequest*>(
               &_CleanupAllRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  void UnsafeArenaSwap(CleanupAllRequest* other);
  void Swap(CleanupAllRequest* other);
  friend void swap(CleanupAllRequest& a, CleanupAllRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CleanupAllRequest* New() const final {
    return CreateMaybeMessage<CleanupAllRequest>(NULL);
  }

  CleanupAllRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CleanupAllRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CleanupAllRequest& from);
  void MergeFrom(const CleanupAllRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CleanupAllRequest* other);
  protected:
  explicit CleanupAllRequest(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string container = 1;
  int container_size() const;
  void clear_container();
  static const int kContainerFieldNumber = 1;
  const ::std::string& container(int index) const;
  ::std::string* mutable_container(int index);
  void set_container(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_container(int index, ::std::string&& value);
  #endif
  void set_container(int index, const char* value);
  void set_container(int index, const char* value, size_t size);
  ::std::string* add_container();
  void add_container(const ::std::string& value);
  #if LANG_CXX11
  void add_container(::std::string&& value);
  #endif
  void add_container(const char* value);
  void add_container(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& container() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_container();

  // @@protoc_insertion_point(class_scope:tensorflow.CleanupAllRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::std::string> container_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CleanupAllResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.CleanupAllResponse) */ {
 public:
  CleanupAllResponse();
  virtual ~CleanupAllResponse();

  CleanupAllResponse(const CleanupAllResponse& from);

  inline CleanupAllResponse& operator=(const CleanupAllResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CleanupAllResponse(CleanupAllResponse&& from) noexcept
    : CleanupAllResponse() {
    *this = ::std::move(from);
  }

  inline CleanupAllResponse& operator=(CleanupAllResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const CleanupAllResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CleanupAllResponse* internal_default_instance() {
    return reinterpret_cast<const CleanupAllResponse*>(
               &_CleanupAllResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  void UnsafeArenaSwap(CleanupAllResponse* other);
  void Swap(CleanupAllResponse* other);
  friend void swap(CleanupAllResponse& a, CleanupAllResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CleanupAllResponse* New() const final {
    return CreateMaybeMessage<CleanupAllResponse>(NULL);
  }

  CleanupAllResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CleanupAllResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CleanupAllResponse& from);
  void MergeFrom(const CleanupAllResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CleanupAllResponse* other);
  protected:
  explicit CleanupAllResponse(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.CleanupAllResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ExecutorOpts : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.ExecutorOpts) */ {
 public:
  ExecutorOpts();
  virtual ~ExecutorOpts();

  ExecutorOpts(const ExecutorOpts& from);

  inline ExecutorOpts& operator=(const ExecutorOpts& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ExecutorOpts(ExecutorOpts&& from) noexcept
    : ExecutorOpts() {
    *this = ::std::move(from);
  }

  inline ExecutorOpts& operator=(ExecutorOpts&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const ExecutorOpts& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ExecutorOpts* internal_default_instance() {
    return reinterpret_cast<const ExecutorOpts*>(
               &_ExecutorOpts_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  void UnsafeArenaSwap(ExecutorOpts* other);
  void Swap(ExecutorOpts* other);
  friend void swap(ExecutorOpts& a, ExecutorOpts& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ExecutorOpts* New() const final {
    return CreateMaybeMessage<ExecutorOpts>(NULL);
  }

  ExecutorOpts* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ExecutorOpts>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ExecutorOpts& from);
  void MergeFrom(const ExecutorOpts& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExecutorOpts* other);
  protected:
  explicit ExecutorOpts(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // bool record_costs = 1;
  void clear_record_costs();
  static const int kRecordCostsFieldNumber = 1;
  bool record_costs() const;
  void set_record_costs(bool value);

  // bool record_timeline = 3;
  void clear_record_timeline();
  static const int kRecordTimelineFieldNumber = 3;
  bool record_timeline() const;
  void set_record_timeline(bool value);

  // bool record_partition_graphs = 4;
  void clear_record_partition_graphs();
  static const int kRecordPartitionGraphsFieldNumber = 4;
  bool record_partition_graphs() const;
  void set_record_partition_graphs(bool value);

  // bool report_tensor_allocations_upon_oom = 5;
  void clear_report_tensor_allocations_upon_oom();
  static const int kReportTensorAllocationsUponOomFieldNumber = 5;
  bool report_tensor_allocations_upon_oom() const;
  void set_report_tensor_allocations_upon_oom(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.ExecutorOpts)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  bool record_costs_;
  bool record_timeline_;
  bool record_partition_graphs_;
  bool report_tensor_allocations_upon_oom_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class RunGraphRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunGraphRequest) */ {
 public:
  RunGraphRequest();
  virtual ~RunGraphRequest();

  RunGraphRequest(const RunGraphRequest& from);

  inline RunGraphRequest& operator=(const RunGraphRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  RunGraphRequest(RunGraphRequest&& from) noexcept
    : RunGraphRequest() {
    *this = ::std::move(from);
  }

  inline RunGraphRequest& operator=(RunGraphRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const RunGraphRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RunGraphRequest* internal_default_instance() {
    return reinterpret_cast<const RunGraphRequest*>(
               &_RunGraphRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  void UnsafeArenaSwap(RunGraphRequest* other);
  void Swap(RunGraphRequest* other);
  friend void swap(RunGraphRequest& a, RunGraphRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RunGraphRequest* New() const final {
    return CreateMaybeMessage<RunGraphRequest>(NULL);
  }

  RunGraphRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<RunGraphRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const RunGraphRequest& from);
  void MergeFrom(const RunGraphRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunGraphRequest* other);
  protected:
  explicit RunGraphRequest(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.NamedTensorProto send = 3;
  int send_size() const;
  void clear_send();
  static const int kSendFieldNumber = 3;
  ::tensorflow::NamedTensorProto* mutable_send(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::NamedTensorProto >*
      mutable_send();
  const ::tensorflow::NamedTensorProto& send(int index) const;
  ::tensorflow::NamedTensorProto* add_send();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::NamedTensorProto >&
      send() const;

  // repeated string recv_key = 4;
  int recv_key_size() const;
  void clear_recv_key();
  static const int kRecvKeyFieldNumber = 4;
  const ::std::string& recv_key(int index) const;
  ::std::string* mutable_recv_key(int index);
  void set_recv_key(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_recv_key(int index, ::std::string&& value);
  #endif
  void set_recv_key(int index, const char* value);
  void set_recv_key(int index, const char* value, size_t size);
  ::std::string* add_recv_key();
  void add_recv_key(const ::std::string& value);
  #if LANG_CXX11
  void add_recv_key(::std::string&& value);
  #endif
  void add_recv_key(const char* value);
  void add_recv_key(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& recv_key() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_recv_key();

  // string graph_handle = 1;
  void clear_graph_handle();
  static const int kGraphHandleFieldNumber = 1;
  const ::std::string& graph_handle() const;
  void set_graph_handle(const ::std::string& value);
  #if LANG_CXX11
  void set_graph_handle(::std::string&& value);
  #endif
  void set_graph_handle(const char* value);
  void set_graph_handle(const char* value, size_t size);
  ::std::string* mutable_graph_handle();
  ::std::string* release_graph_handle();
  void set_allocated_graph_handle(::std::string* graph_handle);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_graph_handle();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_graph_handle(
      ::std::string* graph_handle);

  // string session_handle = 8;
  void clear_session_handle();
  static const int kSessionHandleFieldNumber = 8;
  const ::std::string& session_handle() const;
  void set_session_handle(const ::std::string& value);
  #if LANG_CXX11
  void set_session_handle(::std::string&& value);
  #endif
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  ::std::string* mutable_session_handle();
  ::std::string* release_session_handle();
  void set_allocated_session_handle(::std::string* session_handle);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_session_handle();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      ::std::string* session_handle);

  // .tensorflow.ExecutorOpts exec_opts = 5;
  bool has_exec_opts() const;
  void clear_exec_opts();
  static const int kExecOptsFieldNumber = 5;
  private:
  const ::tensorflow::ExecutorOpts& _internal_exec_opts() const;
  public:
  const ::tensorflow::ExecutorOpts& exec_opts() const;
  ::tensorflow::ExecutorOpts* release_exec_opts();
  ::tensorflow::ExecutorOpts* mutable_exec_opts();
  void set_allocated_exec_opts(::tensorflow::ExecutorOpts* exec_opts);
  void unsafe_arena_set_allocated_exec_opts(
      ::tensorflow::ExecutorOpts* exec_opts);
  ::tensorflow::ExecutorOpts* unsafe_arena_release_exec_opts();

  // int64 step_id = 2;
  void clear_step_id();
  static const int kStepIdFieldNumber = 2;
  ::google::protobuf::int64 step_id() const;
  void set_step_id(::google::protobuf::int64 value);

  // int64 request_id = 11;
  void clear_request_id();
  static const int kRequestIdFieldNumber = 11;
  ::google::protobuf::int64 request_id() const;
  void set_request_id(::google::protobuf::int64 value);

  // bool create_worker_session_called = 10;
  void clear_create_worker_session_called();
  static const int kCreateWorkerSessionCalledFieldNumber = 10;
  bool create_worker_session_called() const;
  void set_create_worker_session_called(bool value);

  // bool is_partial = 6;
  void clear_is_partial();
  static const int kIsPartialFieldNumber = 6;
  bool is_partial() const;
  void set_is_partial(bool value);

  // bool is_last_partial_run = 7;
  void clear_is_last_partial_run();
  static const int kIsLastPartialRunFieldNumber = 7;
  bool is_last_partial_run() const;
  void set_is_last_partial_run(bool value);

  // bool store_errors_in_response_body = 9;
  void clear_store_errors_in_response_body();
  static const int kStoreErrorsInResponseBodyFieldNumber = 9;
  bool store_errors_in_response_body() const;
  void set_store_errors_in_response_body(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.RunGraphRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::NamedTensorProto > send_;
  ::google::protobuf::RepeatedPtrField< ::std::string> recv_key_;
  ::google::protobuf::internal::ArenaStringPtr graph_handle_;
  ::google::protobuf::internal::ArenaStringPtr session_handle_;
  ::tensorflow::ExecutorOpts* exec_opts_;
  ::google::protobuf::int64 step_id_;
  ::google::protobuf::int64 request_id_;
  bool create_worker_session_called_;
  bool is_partial_;
  bool is_last_partial_run_;
  bool store_errors_in_response_body_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class RunGraphResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunGraphResponse) */ {
 public:
  RunGraphResponse();
  virtual ~RunGraphResponse();

  RunGraphResponse(const RunGraphResponse& from);

  inline RunGraphResponse& operator=(const RunGraphResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  RunGraphResponse(RunGraphResponse&& from) noexcept
    : RunGraphResponse() {
    *this = ::std::move(from);
  }

  inline RunGraphResponse& operator=(RunGraphResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const RunGraphResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RunGraphResponse* internal_default_instance() {
    return reinterpret_cast<const RunGraphResponse*>(
               &_RunGraphResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  void UnsafeArenaSwap(RunGraphResponse* other);
  void Swap(RunGraphResponse* other);
  friend void swap(RunGraphResponse& a, RunGraphResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RunGraphResponse* New() const final {
    return CreateMaybeMessage<RunGraphResponse>(NULL);
  }

  RunGraphResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<RunGraphResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const RunGraphResponse& from);
  void MergeFrom(const RunGraphResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunGraphResponse* other);
  protected:
  explicit RunGraphResponse(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.NamedTensorProto recv = 1;
  int recv_size() const;
  void clear_recv();
  static const int kRecvFieldNumber = 1;
  ::tensorflow::NamedTensorProto* mutable_recv(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::NamedTensorProto >*
      mutable_recv();
  const ::tensorflow::NamedTensorProto& recv(int index) const;
  ::tensorflow::NamedTensorProto* add_recv();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::NamedTensorProto >&
      recv() const;

  // repeated .tensorflow.GraphDef partition_graph = 4;
  int partition_graph_size() const;
  void clear_partition_graph();
  static const int kPartitionGraphFieldNumber = 4;
  ::tensorflow::GraphDef* mutable_partition_graph(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::GraphDef >*
      mutable_partition_graph();
  const ::tensorflow::GraphDef& partition_graph(int index) const;
  ::tensorflow::GraphDef* add_partition_graph();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::GraphDef >&
      partition_graph() const;

  // string status_error_message = 6;
  void clear_status_error_message();
  static const int kStatusErrorMessageFieldNumber = 6;
  const ::std::string& status_error_message() const;
  void set_status_error_message(const ::std::string& value);
  #if LANG_CXX11
  void set_status_error_message(::std::string&& value);
  #endif
  void set_status_error_message(const char* value);
  void set_status_error_message(const char* value, size_t size);
  ::std::string* mutable_status_error_message();
  ::std::string* release_status_error_message();
  void set_allocated_status_error_message(::std::string* status_error_message);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_status_error_message();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_status_error_message(
      ::std::string* status_error_message);

  // .tensorflow.StepStats step_stats = 2;
  bool has_step_stats() const;
  void clear_step_stats();
  static const int kStepStatsFieldNumber = 2;
  private:
  const ::tensorflow::StepStats& _internal_step_stats() const;
  public:
  const ::tensorflow::StepStats& step_stats() const;
  ::tensorflow::StepStats* release_step_stats();
  ::tensorflow::StepStats* mutable_step_stats();
  void set_allocated_step_stats(::tensorflow::StepStats* step_stats);
  void unsafe_arena_set_allocated_step_stats(
      ::tensorflow::StepStats* step_stats);
  ::tensorflow::StepStats* unsafe_arena_release_step_stats();

  // .tensorflow.CostGraphDef cost_graph = 3;
  bool has_cost_graph() const;
  void clear_cost_graph();
  static const int kCostGraphFieldNumber = 3;
  private:
  const ::tensorflow::CostGraphDef& _internal_cost_graph() const;
  public:
  const ::tensorflow::CostGraphDef& cost_graph() const;
  ::tensorflow::CostGraphDef* release_cost_graph();
  ::tensorflow::CostGraphDef* mutable_cost_graph();
  void set_allocated_cost_graph(::tensorflow::CostGraphDef* cost_graph);
  void unsafe_arena_set_allocated_cost_graph(
      ::tensorflow::CostGraphDef* cost_graph);
  ::tensorflow::CostGraphDef* unsafe_arena_release_cost_graph();

  // .tensorflow.error.Code status_code = 5;
  void clear_status_code();
  static const int kStatusCodeFieldNumber = 5;
  ::tensorflow::error::Code status_code() const;
  void set_status_code(::tensorflow::error::Code value);

  // @@protoc_insertion_point(class_scope:tensorflow.RunGraphResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::NamedTensorProto > recv_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::GraphDef > partition_graph_;
  ::google::protobuf::internal::ArenaStringPtr status_error_message_;
  ::tensorflow::StepStats* step_stats_;
  ::tensorflow::CostGraphDef* cost_graph_;
  int status_code_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CleanupGraphRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.CleanupGraphRequest) */ {
 public:
  CleanupGraphRequest();
  virtual ~CleanupGraphRequest();

  CleanupGraphRequest(const CleanupGraphRequest& from);

  inline CleanupGraphRequest& operator=(const CleanupGraphRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CleanupGraphRequest(CleanupGraphRequest&& from) noexcept
    : CleanupGraphRequest() {
    *this = ::std::move(from);
  }

  inline CleanupGraphRequest& operator=(CleanupGraphRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const CleanupGraphRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CleanupGraphRequest* internal_default_instance() {
    return reinterpret_cast<const CleanupGraphRequest*>(
               &_CleanupGraphRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  void UnsafeArenaSwap(CleanupGraphRequest* other);
  void Swap(CleanupGraphRequest* other);
  friend void swap(CleanupGraphRequest& a, CleanupGraphRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CleanupGraphRequest* New() const final {
    return CreateMaybeMessage<CleanupGraphRequest>(NULL);
  }

  CleanupGraphRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CleanupGraphRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CleanupGraphRequest& from);
  void MergeFrom(const CleanupGraphRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CleanupGraphRequest* other);
  protected:
  explicit CleanupGraphRequest(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 step_id = 1;
  void clear_step_id();
  static const int kStepIdFieldNumber = 1;
  ::google::protobuf::int64 step_id() const;
  void set_step_id(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.CleanupGraphRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::int64 step_id_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CleanupGraphResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.CleanupGraphResponse) */ {
 public:
  CleanupGraphResponse();
  virtual ~CleanupGraphResponse();

  CleanupGraphResponse(const CleanupGraphResponse& from);

  inline CleanupGraphResponse& operator=(const CleanupGraphResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CleanupGraphResponse(CleanupGraphResponse&& from) noexcept
    : CleanupGraphResponse() {
    *this = ::std::move(from);
  }

  inline CleanupGraphResponse& operator=(CleanupGraphResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const CleanupGraphResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CleanupGraphResponse* internal_default_instance() {
    return reinterpret_cast<const CleanupGraphResponse*>(
               &_CleanupGraphResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  void UnsafeArenaSwap(CleanupGraphResponse* other);
  void Swap(CleanupGraphResponse* other);
  friend void swap(CleanupGraphResponse& a, CleanupGraphResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CleanupGraphResponse* New() const final {
    return CreateMaybeMessage<CleanupGraphResponse>(NULL);
  }

  CleanupGraphResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CleanupGraphResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CleanupGraphResponse& from);
  void MergeFrom(const CleanupGraphResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CleanupGraphResponse* other);
  protected:
  explicit CleanupGraphResponse(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.CleanupGraphResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class RecvTensorRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.RecvTensorRequest) */ {
 public:
  RecvTensorRequest();
  virtual ~RecvTensorRequest();

  RecvTensorRequest(const RecvTensorRequest& from);

  inline RecvTensorRequest& operator=(const RecvTensorRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  RecvTensorRequest(RecvTensorRequest&& from) noexcept
    : RecvTensorRequest() {
    *this = ::std::move(from);
  }

  inline RecvTensorRequest& operator=(RecvTensorRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const RecvTensorRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RecvTensorRequest* internal_default_instance() {
    return reinterpret_cast<const RecvTensorRequest*>(
               &_RecvTensorRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  void UnsafeArenaSwap(RecvTensorRequest* other);
  void Swap(RecvTensorRequest* other);
  friend void swap(RecvTensorRequest& a, RecvTensorRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RecvTensorRequest* New() const final {
    return CreateMaybeMessage<RecvTensorRequest>(NULL);
  }

  RecvTensorRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<RecvTensorRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const RecvTensorRequest& from);
  void MergeFrom(const RecvTensorRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RecvTensorRequest* other);
  protected:
  explicit RecvTensorRequest(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string rendezvous_key = 2;
  void clear_rendezvous_key();
  static const int kRendezvousKeyFieldNumber = 2;
  const ::std::string& rendezvous_key() const;
  void set_rendezvous_key(const ::std::string& value);
  #if LANG_CXX11
  void set_rendezvous_key(::std::string&& value);
  #endif
  void set_rendezvous_key(const char* value);
  void set_rendezvous_key(const char* value, size_t size);
  ::std::string* mutable_rendezvous_key();
  ::std::string* release_rendezvous_key();
  void set_allocated_rendezvous_key(::std::string* rendezvous_key);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_rendezvous_key();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_rendezvous_key(
      ::std::string* rendezvous_key);

  // .tensorflow.DeviceLocality client_locality = 4;
  bool has_client_locality() const;
  void clear_client_locality();
  static const int kClientLocalityFieldNumber = 4;
  private:
  const ::tensorflow::DeviceLocality& _internal_client_locality() const;
  public:
  const ::tensorflow::DeviceLocality& client_locality() const;
  ::tensorflow::DeviceLocality* release_client_locality();
  ::tensorflow::DeviceLocality* mutable_client_locality();
  void set_allocated_client_locality(::tensorflow::DeviceLocality* client_locality);
  void unsafe_arena_set_allocated_client_locality(
      ::tensorflow::DeviceLocality* client_locality);
  ::tensorflow::DeviceLocality* unsafe_arena_release_client_locality();

  // .tensorflow.DeviceLocality server_locality = 5;
  bool has_server_locality() const;
  void clear_server_locality();
  static const int kServerLocalityFieldNumber = 5;
  private:
  const ::tensorflow::DeviceLocality& _internal_server_locality() const;
  public:
  const ::tensorflow::DeviceLocality& server_locality() const;
  ::tensorflow::DeviceLocality* release_server_locality();
  ::tensorflow::DeviceLocality* mutable_server_locality();
  void set_allocated_server_locality(::tensorflow::DeviceLocality* server_locality);
  void unsafe_arena_set_allocated_server_locality(
      ::tensorflow::DeviceLocality* server_locality);
  ::tensorflow::DeviceLocality* unsafe_arena_release_server_locality();

  // .google.protobuf.Any transport_options = 6;
  bool has_transport_options() const;
  void clear_transport_options();
  static const int kTransportOptionsFieldNumber = 6;
  private:
  const ::google::protobuf::Any& _internal_transport_options() const;
  public:
  const ::google::protobuf::Any& transport_options() const;
  ::google::protobuf::Any* release_transport_options();
  ::google::protobuf::Any* mutable_transport_options();
  void set_allocated_transport_options(::google::protobuf::Any* transport_options);
  void unsafe_arena_set_allocated_transport_options(
      ::google::protobuf::Any* transport_options);
  ::google::protobuf::Any* unsafe_arena_release_transport_options();

  // int64 step_id = 1;
  void clear_step_id();
  static const int kStepIdFieldNumber = 1;
  ::google::protobuf::int64 step_id() const;
  void set_step_id(::google::protobuf::int64 value);

  // int64 request_id = 7;
  void clear_request_id();
  static const int kRequestIdFieldNumber = 7;
  ::google::protobuf::int64 request_id() const;
  void set_request_id(::google::protobuf::int64 value);

  // bool dma_ok = 3;
  void clear_dma_ok();
  static const int kDmaOkFieldNumber = 3;
  bool dma_ok() const;
  void set_dma_ok(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.RecvTensorRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr rendezvous_key_;
  ::tensorflow::DeviceLocality* client_locality_;
  ::tensorflow::DeviceLocality* server_locality_;
  ::google::protobuf::Any* transport_options_;
  ::google::protobuf::int64 step_id_;
  ::google::protobuf::int64 request_id_;
  bool dma_ok_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class RecvTensorResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.RecvTensorResponse) */ {
 public:
  RecvTensorResponse();
  virtual ~RecvTensorResponse();

  RecvTensorResponse(const RecvTensorResponse& from);

  inline RecvTensorResponse& operator=(const RecvTensorResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  RecvTensorResponse(RecvTensorResponse&& from) noexcept
    : RecvTensorResponse() {
    *this = ::std::move(from);
  }

  inline RecvTensorResponse& operator=(RecvTensorResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const RecvTensorResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RecvTensorResponse* internal_default_instance() {
    return reinterpret_cast<const RecvTensorResponse*>(
               &_RecvTensorResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  void UnsafeArenaSwap(RecvTensorResponse* other);
  void Swap(RecvTensorResponse* other);
  friend void swap(RecvTensorResponse& a, RecvTensorResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RecvTensorResponse* New() const final {
    return CreateMaybeMessage<RecvTensorResponse>(NULL);
  }

  RecvTensorResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<RecvTensorResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const RecvTensorResponse& from);
  void MergeFrom(const RecvTensorResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RecvTensorResponse* other);
  protected:
  explicit RecvTensorResponse(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .tensorflow.TensorProto tensor = 1;
  bool has_tensor() const;
  void clear_tensor();
  static const int kTensorFieldNumber = 1;
  private:
  const ::tensorflow::TensorProto& _internal_tensor() const;
  public:
  const ::tensorflow::TensorProto& tensor() const;
  ::tensorflow::TensorProto* release_tensor();
  ::tensorflow::TensorProto* mutable_tensor();
  void set_allocated_tensor(::tensorflow::TensorProto* tensor);
  void unsafe_arena_set_allocated_tensor(
      ::tensorflow::TensorProto* tensor);
  ::tensorflow::TensorProto* unsafe_arena_release_tensor();

  // .google.protobuf.Any transport_options = 4;
  bool has_transport_options() const;
  void clear_transport_options();
  static const int kTransportOptionsFieldNumber = 4;
  private:
  const ::google::protobuf::Any& _internal_transport_options() const;
  public:
  const ::google::protobuf::Any& transport_options() const;
  ::google::protobuf::Any* release_transport_options();
  ::google::protobuf::Any* mutable_transport_options();
  void set_allocated_transport_options(::google::protobuf::Any* transport_options);
  void unsafe_arena_set_allocated_transport_options(
      ::google::protobuf::Any* transport_options);
  ::google::protobuf::Any* unsafe_arena_release_transport_options();

  // int64 send_start_micros = 3;
  void clear_send_start_micros();
  static const int kSendStartMicrosFieldNumber = 3;
  ::google::protobuf::int64 send_start_micros() const;
  void set_send_start_micros(::google::protobuf::int64 value);

  // bool is_dead = 2;
  void clear_is_dead();
  static const int kIsDeadFieldNumber = 2;
  bool is_dead() const;
  void set_is_dead(bool value);

  // bool require_ack = 5;
  void clear_require_ack();
  static const int kRequireAckFieldNumber = 5;
  bool require_ack() const;
  void set_require_ack(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.RecvTensorResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::tensorflow::TensorProto* tensor_;
  ::google::protobuf::Any* transport_options_;
  ::google::protobuf::int64 send_start_micros_;
  bool is_dead_;
  bool require_ack_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class MarkRecvFinishedRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.MarkRecvFinishedRequest) */ {
 public:
  MarkRecvFinishedRequest();
  virtual ~MarkRecvFinishedRequest();

  MarkRecvFinishedRequest(const MarkRecvFinishedRequest& from);

  inline MarkRecvFinishedRequest& operator=(const MarkRecvFinishedRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  MarkRecvFinishedRequest(MarkRecvFinishedRequest&& from) noexcept
    : MarkRecvFinishedRequest() {
    *this = ::std::move(from);
  }

  inline MarkRecvFinishedRequest& operator=(MarkRecvFinishedRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const MarkRecvFinishedRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MarkRecvFinishedRequest* internal_default_instance() {
    return reinterpret_cast<const MarkRecvFinishedRequest*>(
               &_MarkRecvFinishedRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  void UnsafeArenaSwap(MarkRecvFinishedRequest* other);
  void Swap(MarkRecvFinishedRequest* other);
  friend void swap(MarkRecvFinishedRequest& a, MarkRecvFinishedRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline MarkRecvFinishedRequest* New() const final {
    return CreateMaybeMessage<MarkRecvFinishedRequest>(NULL);
  }

  MarkRecvFinishedRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<MarkRecvFinishedRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const MarkRecvFinishedRequest& from);
  void MergeFrom(const MarkRecvFinishedRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MarkRecvFinishedRequest* other);
  protected:
  explicit MarkRecvFinishedRequest(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 request_id = 1;
  void clear_request_id();
  static const int kRequestIdFieldNumber = 1;
  ::google::protobuf::int64 request_id() const;
  void set_request_id(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.MarkRecvFinishedRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::int64 request_id_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class MarkRecvFinishedResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.MarkRecvFinishedResponse) */ {
 public:
  MarkRecvFinishedResponse();
  virtual ~MarkRecvFinishedResponse();

  MarkRecvFinishedResponse(const MarkRecvFinishedResponse& from);

  inline MarkRecvFinishedResponse& operator=(const MarkRecvFinishedResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  MarkRecvFinishedResponse(MarkRecvFinishedResponse&& from) noexcept
    : MarkRecvFinishedResponse() {
    *this = ::std::move(from);
  }

  inline MarkRecvFinishedResponse& operator=(MarkRecvFinishedResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const MarkRecvFinishedResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MarkRecvFinishedResponse* internal_default_instance() {
    return reinterpret_cast<const MarkRecvFinishedResponse*>(
               &_MarkRecvFinishedResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    20;

  void UnsafeArenaSwap(MarkRecvFinishedResponse* other);
  void Swap(MarkRecvFinishedResponse* other);
  friend void swap(MarkRecvFinishedResponse& a, MarkRecvFinishedResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline MarkRecvFinishedResponse* New() const final {
    return CreateMaybeMessage<MarkRecvFinishedResponse>(NULL);
  }

  MarkRecvFinishedResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<MarkRecvFinishedResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const MarkRecvFinishedResponse& from);
  void MergeFrom(const MarkRecvFinishedResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MarkRecvFinishedResponse* other);
  protected:
  explicit MarkRecvFinishedResponse(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.MarkRecvFinishedResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class LoggingRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.LoggingRequest) */ {
 public:
  LoggingRequest();
  virtual ~LoggingRequest();

  LoggingRequest(const LoggingRequest& from);

  inline LoggingRequest& operator=(const LoggingRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  LoggingRequest(LoggingRequest&& from) noexcept
    : LoggingRequest() {
    *this = ::std::move(from);
  }

  inline LoggingRequest& operator=(LoggingRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const LoggingRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const LoggingRequest* internal_default_instance() {
    return reinterpret_cast<const LoggingRequest*>(
               &_LoggingRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  void UnsafeArenaSwap(LoggingRequest* other);
  void Swap(LoggingRequest* other);
  friend void swap(LoggingRequest& a, LoggingRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline LoggingRequest* New() const final {
    return CreateMaybeMessage<LoggingRequest>(NULL);
  }

  LoggingRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<LoggingRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const LoggingRequest& from);
  void MergeFrom(const LoggingRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LoggingRequest* other);
  protected:
  explicit LoggingRequest(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int64 fetch_step_id = 3;
  int fetch_step_id_size() const;
  void clear_fetch_step_id();
  static const int kFetchStepIdFieldNumber = 3;
  ::google::protobuf::int64 fetch_step_id(int index) const;
  void set_fetch_step_id(int index, ::google::protobuf::int64 value);
  void add_fetch_step_id(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      fetch_step_id() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_fetch_step_id();

  // bool enable_rpc_logging = 1;
  void clear_enable_rpc_logging();
  static const int kEnableRpcLoggingFieldNumber = 1;
  bool enable_rpc_logging() const;
  void set_enable_rpc_logging(bool value);

  // bool disable_rpc_logging = 4;
  void clear_disable_rpc_logging();
  static const int kDisableRpcLoggingFieldNumber = 4;
  bool disable_rpc_logging() const;
  void set_disable_rpc_logging(bool value);

  // bool clear = 2;
  void clear_clear();
  static const int kClearFieldNumber = 2;
  bool clear() const;
  void set_clear(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.LoggingRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > fetch_step_id_;
  mutable int _fetch_step_id_cached_byte_size_;
  bool enable_rpc_logging_;
  bool disable_rpc_logging_;
  bool clear_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class LabeledStepStats : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.LabeledStepStats) */ {
 public:
  LabeledStepStats();
  virtual ~LabeledStepStats();

  LabeledStepStats(const LabeledStepStats& from);

  inline LabeledStepStats& operator=(const LabeledStepStats& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  LabeledStepStats(LabeledStepStats&& from) noexcept
    : LabeledStepStats() {
    *this = ::std::move(from);
  }

  inline LabeledStepStats& operator=(LabeledStepStats&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const LabeledStepStats& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const LabeledStepStats* internal_default_instance() {
    return reinterpret_cast<const LabeledStepStats*>(
               &_LabeledStepStats_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    22;

  void UnsafeArenaSwap(LabeledStepStats* other);
  void Swap(LabeledStepStats* other);
  friend void swap(LabeledStepStats& a, LabeledStepStats& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline LabeledStepStats* New() const final {
    return CreateMaybeMessage<LabeledStepStats>(NULL);
  }

  LabeledStepStats* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<LabeledStepStats>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const LabeledStepStats& from);
  void MergeFrom(const LabeledStepStats& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LabeledStepStats* other);
  protected:
  explicit LabeledStepStats(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .tensorflow.StepStats step_stats = 2;
  bool has_step_stats() const;
  void clear_step_stats();
  static const int kStepStatsFieldNumber = 2;
  private:
  const ::tensorflow::StepStats& _internal_step_stats() const;
  public:
  const ::tensorflow::StepStats& step_stats() const;
  ::tensorflow::StepStats* release_step_stats();
  ::tensorflow::StepStats* mutable_step_stats();
  void set_allocated_step_stats(::tensorflow::StepStats* step_stats);
  void unsafe_arena_set_allocated_step_stats(
      ::tensorflow::StepStats* step_stats);
  ::tensorflow::StepStats* unsafe_arena_release_step_stats();

  // int64 step_id = 1;
  void clear_step_id();
  static const int kStepIdFieldNumber = 1;
  ::google::protobuf::int64 step_id() const;
  void set_step_id(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.LabeledStepStats)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::tensorflow::StepStats* step_stats_;
  ::google::protobuf::int64 step_id_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class LoggingResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.LoggingResponse) */ {
 public:
  LoggingResponse();
  virtual ~LoggingResponse();

  LoggingResponse(const LoggingResponse& from);

  inline LoggingResponse& operator=(const LoggingResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  LoggingResponse(LoggingResponse&& from) noexcept
    : LoggingResponse() {
    *this = ::std::move(from);
  }

  inline LoggingResponse& operator=(LoggingResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const LoggingResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const LoggingResponse* internal_default_instance() {
    return reinterpret_cast<const LoggingResponse*>(
               &_LoggingResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    23;

  void UnsafeArenaSwap(LoggingResponse* other);
  void Swap(LoggingResponse* other);
  friend void swap(LoggingResponse& a, LoggingResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline LoggingResponse* New() const final {
    return CreateMaybeMessage<LoggingResponse>(NULL);
  }

  LoggingResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<LoggingResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const LoggingResponse& from);
  void MergeFrom(const LoggingResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LoggingResponse* other);
  protected:
  explicit LoggingResponse(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.LabeledStepStats step = 1;
  int step_size() const;
  void clear_step();
  static const int kStepFieldNumber = 1;
  ::tensorflow::LabeledStepStats* mutable_step(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::LabeledStepStats >*
      mutable_step();
  const ::tensorflow::LabeledStepStats& step(int index) const;
  ::tensorflow::LabeledStepStats* add_step();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::LabeledStepStats >&
      step() const;

  // @@protoc_insertion_point(class_scope:tensorflow.LoggingResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::LabeledStepStats > step_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class TraceOpts : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.TraceOpts) */ {
 public:
  TraceOpts();
  virtual ~TraceOpts();

  TraceOpts(const TraceOpts& from);

  inline TraceOpts& operator=(const TraceOpts& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TraceOpts(TraceOpts&& from) noexcept
    : TraceOpts() {
    *this = ::std::move(from);
  }

  inline TraceOpts& operator=(TraceOpts&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TraceOpts& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TraceOpts* internal_default_instance() {
    return reinterpret_cast<const TraceOpts*>(
               &_TraceOpts_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    24;

  void UnsafeArenaSwap(TraceOpts* other);
  void Swap(TraceOpts* other);
  friend void swap(TraceOpts& a, TraceOpts& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TraceOpts* New() const final {
    return CreateMaybeMessage<TraceOpts>(NULL);
  }

  TraceOpts* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TraceOpts>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TraceOpts& from);
  void MergeFrom(const TraceOpts& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TraceOpts* other);
  protected:
  explicit TraceOpts(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // double duration = 1;
  void clear_duration();
  static const int kDurationFieldNumber = 1;
  double duration() const;
  void set_duration(double value);

  // bool use_step_profiler = 2;
  void clear_use_step_profiler();
  static const int kUseStepProfilerFieldNumber = 2;
  bool use_step_profiler() const;
  void set_use_step_profiler(bool value);

  // bool use_kernel_profiler = 3;
  void clear_use_kernel_profiler();
  static const int kUseKernelProfilerFieldNumber = 3;
  bool use_kernel_profiler() const;
  void set_use_kernel_profiler(bool value);

  // bool use_extended_profiler = 4;
  void clear_use_extended_profiler();
  static const int kUseExtendedProfilerFieldNumber = 4;
  bool use_extended_profiler() const;
  void set_use_extended_profiler(bool value);

  // bool use_gpu_profiler = 5;
  void clear_use_gpu_profiler();
  static const int kUseGpuProfilerFieldNumber = 5;
  bool use_gpu_profiler() const;
  void set_use_gpu_profiler(bool value);

  // bool use_sample_profiler = 6;
  void clear_use_sample_profiler();
  static const int kUseSampleProfilerFieldNumber = 6;
  bool use_sample_profiler() const;
  void set_use_sample_profiler(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.TraceOpts)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double duration_;
  bool use_step_profiler_;
  bool use_kernel_profiler_;
  bool use_extended_profiler_;
  bool use_gpu_profiler_;
  bool use_sample_profiler_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class TracingRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.TracingRequest) */ {
 public:
  TracingRequest();
  virtual ~TracingRequest();

  TracingRequest(const TracingRequest& from);

  inline TracingRequest& operator=(const TracingRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TracingRequest(TracingRequest&& from) noexcept
    : TracingRequest() {
    *this = ::std::move(from);
  }

  inline TracingRequest& operator=(TracingRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TracingRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TracingRequest* internal_default_instance() {
    return reinterpret_cast<const TracingRequest*>(
               &_TracingRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    25;

  void UnsafeArenaSwap(TracingRequest* other);
  void Swap(TracingRequest* other);
  friend void swap(TracingRequest& a, TracingRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TracingRequest* New() const final {
    return CreateMaybeMessage<TracingRequest>(NULL);
  }

  TracingRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TracingRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TracingRequest& from);
  void MergeFrom(const TracingRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TracingRequest* other);
  protected:
  explicit TracingRequest(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .tensorflow.TraceOpts options = 1;
  bool has_options() const;
  void clear_options();
  static const int kOptionsFieldNumber = 1;
  private:
  const ::tensorflow::TraceOpts& _internal_options() const;
  public:
  const ::tensorflow::TraceOpts& options() const;
  ::tensorflow::TraceOpts* release_options();
  ::tensorflow::TraceOpts* mutable_options();
  void set_allocated_options(::tensorflow::TraceOpts* options);
  void unsafe_arena_set_allocated_options(
      ::tensorflow::TraceOpts* options);
  ::tensorflow::TraceOpts* unsafe_arena_release_options();

  // @@protoc_insertion_point(class_scope:tensorflow.TracingRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::tensorflow::TraceOpts* options_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class TracingResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.TracingResponse) */ {
 public:
  TracingResponse();
  virtual ~TracingResponse();

  TracingResponse(const TracingResponse& from);

  inline TracingResponse& operator=(const TracingResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TracingResponse(TracingResponse&& from) noexcept
    : TracingResponse() {
    *this = ::std::move(from);
  }

  inline TracingResponse& operator=(TracingResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TracingResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TracingResponse* internal_default_instance() {
    return reinterpret_cast<const TracingResponse*>(
               &_TracingResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    26;

  void UnsafeArenaSwap(TracingResponse* other);
  void Swap(TracingResponse* other);
  friend void swap(TracingResponse& a, TracingResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TracingResponse* New() const final {
    return CreateMaybeMessage<TracingResponse>(NULL);
  }

  TracingResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TracingResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TracingResponse& from);
  void MergeFrom(const TracingResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TracingResponse* other);
  protected:
  explicit TracingResponse(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.TracingResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class RecvBufRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.RecvBufRequest) */ {
 public:
  RecvBufRequest();
  virtual ~RecvBufRequest();

  RecvBufRequest(const RecvBufRequest& from);

  inline RecvBufRequest& operator=(const RecvBufRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  RecvBufRequest(RecvBufRequest&& from) noexcept
    : RecvBufRequest() {
    *this = ::std::move(from);
  }

  inline RecvBufRequest& operator=(RecvBufRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const RecvBufRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RecvBufRequest* internal_default_instance() {
    return reinterpret_cast<const RecvBufRequest*>(
               &_RecvBufRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    27;

  void UnsafeArenaSwap(RecvBufRequest* other);
  void Swap(RecvBufRequest* other);
  friend void swap(RecvBufRequest& a, RecvBufRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RecvBufRequest* New() const final {
    return CreateMaybeMessage<RecvBufRequest>(NULL);
  }

  RecvBufRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<RecvBufRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const RecvBufRequest& from);
  void MergeFrom(const RecvBufRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RecvBufRequest* other);
  protected:
  explicit RecvBufRequest(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string buf_rendezvous_key = 2;
  void clear_buf_rendezvous_key();
  static const int kBufRendezvousKeyFieldNumber = 2;
  const ::std::string& buf_rendezvous_key() const;
  void set_buf_rendezvous_key(const ::std::string& value);
  #if LANG_CXX11
  void set_buf_rendezvous_key(::std::string&& value);
  #endif
  void set_buf_rendezvous_key(const char* value);
  void set_buf_rendezvous_key(const char* value, size_t size);
  ::std::string* mutable_buf_rendezvous_key();
  ::std::string* release_buf_rendezvous_key();
  void set_allocated_buf_rendezvous_key(::std::string* buf_rendezvous_key);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_buf_rendezvous_key();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_buf_rendezvous_key(
      ::std::string* buf_rendezvous_key);

  // string src_device = 8;
  void clear_src_device();
  static const int kSrcDeviceFieldNumber = 8;
  const ::std::string& src_device() const;
  void set_src_device(const ::std::string& value);
  #if LANG_CXX11
  void set_src_device(::std::string&& value);
  #endif
  void set_src_device(const char* value);
  void set_src_device(const char* value, size_t size);
  ::std::string* mutable_src_device();
  ::std::string* release_src_device();
  void set_allocated_src_device(::std::string* src_device);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_src_device();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_src_device(
      ::std::string* src_device);

  // string dst_device = 9;
  void clear_dst_device();
  static const int kDstDeviceFieldNumber = 9;
  const ::std::string& dst_device() const;
  void set_dst_device(const ::std::string& value);
  #if LANG_CXX11
  void set_dst_device(::std::string&& value);
  #endif
  void set_dst_device(const char* value);
  void set_dst_device(const char* value, size_t size);
  ::std::string* mutable_dst_device();
  ::std::string* release_dst_device();
  void set_allocated_dst_device(::std::string* dst_device);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_dst_device();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_dst_device(
      ::std::string* dst_device);

  // .tensorflow.DeviceLocality client_locality = 5;
  bool has_client_locality() const;
  void clear_client_locality();
  static const int kClientLocalityFieldNumber = 5;
  private:
  const ::tensorflow::DeviceLocality& _internal_client_locality() const;
  public:
  const ::tensorflow::DeviceLocality& client_locality() const;
  ::tensorflow::DeviceLocality* release_client_locality();
  ::tensorflow::DeviceLocality* mutable_client_locality();
  void set_allocated_client_locality(::tensorflow::DeviceLocality* client_locality);
  void unsafe_arena_set_allocated_client_locality(
      ::tensorflow::DeviceLocality* client_locality);
  ::tensorflow::DeviceLocality* unsafe_arena_release_client_locality();

  // .tensorflow.DeviceLocality server_locality = 6;
  bool has_server_locality() const;
  void clear_server_locality();
  static const int kServerLocalityFieldNumber = 6;
  private:
  const ::tensorflow::DeviceLocality& _internal_server_locality() const;
  public:
  const ::tensorflow::DeviceLocality& server_locality() const;
  ::tensorflow::DeviceLocality* release_server_locality();
  ::tensorflow::DeviceLocality* mutable_server_locality();
  void set_allocated_server_locality(::tensorflow::DeviceLocality* server_locality);
  void unsafe_arena_set_allocated_server_locality(
      ::tensorflow::DeviceLocality* server_locality);
  ::tensorflow::DeviceLocality* unsafe_arena_release_server_locality();

  // .google.protobuf.Any transport_options = 7;
  bool has_transport_options() const;
  void clear_transport_options();
  static const int kTransportOptionsFieldNumber = 7;
  private:
  const ::google::protobuf::Any& _internal_transport_options() const;
  public:
  const ::google::protobuf::Any& transport_options() const;
  ::google::protobuf::Any* release_transport_options();
  ::google::protobuf::Any* mutable_transport_options();
  void set_allocated_transport_options(::google::protobuf::Any* transport_options);
  void unsafe_arena_set_allocated_transport_options(
      ::google::protobuf::Any* transport_options);
  ::google::protobuf::Any* unsafe_arena_release_transport_options();

  // int64 step_id = 1;
  void clear_step_id();
  static const int kStepIdFieldNumber = 1;
  ::google::protobuf::int64 step_id() const;
  void set_step_id(::google::protobuf::int64 value);

  // int64 num_bytes = 3;
  void clear_num_bytes();
  static const int kNumBytesFieldNumber = 3;
  ::google::protobuf::int64 num_bytes() const;
  void set_num_bytes(::google::protobuf::int64 value);

  // fixed64 buf_ptr = 4;
  void clear_buf_ptr();
  static const int kBufPtrFieldNumber = 4;
  ::google::protobuf::uint64 buf_ptr() const;
  void set_buf_ptr(::google::protobuf::uint64 value);

  // int64 request_id = 10;
  void clear_request_id();
  static const int kRequestIdFieldNumber = 10;
  ::google::protobuf::int64 request_id() const;
  void set_request_id(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.RecvBufRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr buf_rendezvous_key_;
  ::google::protobuf::internal::ArenaStringPtr src_device_;
  ::google::protobuf::internal::ArenaStringPtr dst_device_;
  ::tensorflow::DeviceLocality* client_locality_;
  ::tensorflow::DeviceLocality* server_locality_;
  ::google::protobuf::Any* transport_options_;
  ::google::protobuf::int64 step_id_;
  ::google::protobuf::int64 num_bytes_;
  ::google::protobuf::uint64 buf_ptr_;
  ::google::protobuf::int64 request_id_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class RecvBufResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.RecvBufResponse) */ {
 public:
  RecvBufResponse();
  virtual ~RecvBufResponse();

  RecvBufResponse(const RecvBufResponse& from);

  inline RecvBufResponse& operator=(const RecvBufResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  RecvBufResponse(RecvBufResponse&& from) noexcept
    : RecvBufResponse() {
    *this = ::std::move(from);
  }

  inline RecvBufResponse& operator=(RecvBufResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const RecvBufResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RecvBufResponse* internal_default_instance() {
    return reinterpret_cast<const RecvBufResponse*>(
               &_RecvBufResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    28;

  void UnsafeArenaSwap(RecvBufResponse* other);
  void Swap(RecvBufResponse* other);
  friend void swap(RecvBufResponse& a, RecvBufResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RecvBufResponse* New() const final {
    return CreateMaybeMessage<RecvBufResponse>(NULL);
  }

  RecvBufResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<RecvBufResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const RecvBufResponse& from);
  void MergeFrom(const RecvBufResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RecvBufResponse* other);
  protected:
  explicit RecvBufResponse(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .google.protobuf.Any transport_options = 4;
  bool has_transport_options() const;
  void clear_transport_options();
  static const int kTransportOptionsFieldNumber = 4;
  private:
  const ::google::protobuf::Any& _internal_transport_options() const;
  public:
  const ::google::protobuf::Any& transport_options() const;
  ::google::protobuf::Any* release_transport_options();
  ::google::protobuf::Any* mutable_transport_options();
  void set_allocated_transport_options(::google::protobuf::Any* transport_options);
  void unsafe_arena_set_allocated_transport_options(
      ::google::protobuf::Any* transport_options);
  ::google::protobuf::Any* unsafe_arena_release_transport_options();

  // fixed64 buf_ptr = 1;
  void clear_buf_ptr();
  static const int kBufPtrFieldNumber = 1;
  ::google::protobuf::uint64 buf_ptr() const;
  void set_buf_ptr(::google::protobuf::uint64 value);

  // int64 num_bytes = 2;
  void clear_num_bytes();
  static const int kNumBytesFieldNumber = 2;
  ::google::protobuf::int64 num_bytes() const;
  void set_num_bytes(::google::protobuf::int64 value);

  // int64 send_start_micros = 5;
  void clear_send_start_micros();
  static const int kSendStartMicrosFieldNumber = 5;
  ::google::protobuf::int64 send_start_micros() const;
  void set_send_start_micros(::google::protobuf::int64 value);

  // bool is_dead = 3;
  void clear_is_dead();
  static const int kIsDeadFieldNumber = 3;
  bool is_dead() const;
  void set_is_dead(bool value);

  // bool require_ack = 6;
  void clear_require_ack();
  static const int kRequireAckFieldNumber = 6;
  bool require_ack() const;
  void set_require_ack(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.RecvBufResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::Any* transport_options_;
  ::google::protobuf::uint64 buf_ptr_;
  ::google::protobuf::int64 num_bytes_;
  ::google::protobuf::int64 send_start_micros_;
  bool is_dead_;
  bool require_ack_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CompleteGroupRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.CompleteGroupRequest) */ {
 public:
  CompleteGroupRequest();
  virtual ~CompleteGroupRequest();

  CompleteGroupRequest(const CompleteGroupRequest& from);

  inline CompleteGroupRequest& operator=(const CompleteGroupRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CompleteGroupRequest(CompleteGroupRequest&& from) noexcept
    : CompleteGroupRequest() {
    *this = ::std::move(from);
  }

  inline CompleteGroupRequest& operator=(CompleteGroupRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const CompleteGroupRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CompleteGroupRequest* internal_default_instance() {
    return reinterpret_cast<const CompleteGroupRequest*>(
               &_CompleteGroupRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    29;

  void UnsafeArenaSwap(CompleteGroupRequest* other);
  void Swap(CompleteGroupRequest* other);
  friend void swap(CompleteGroupRequest& a, CompleteGroupRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CompleteGroupRequest* New() const final {
    return CreateMaybeMessage<CompleteGroupRequest>(NULL);
  }

  CompleteGroupRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CompleteGroupRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CompleteGroupRequest& from);
  void MergeFrom(const CompleteGroupRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CompleteGroupRequest* other);
  protected:
  explicit CompleteGroupRequest(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string device_name = 4;
  int device_name_size() const;
  void clear_device_name();
  static const int kDeviceNameFieldNumber = 4;
  const ::std::string& device_name(int index) const;
  ::std::string* mutable_device_name(int index);
  void set_device_name(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_device_name(int index, ::std::string&& value);
  #endif
  void set_device_name(int index, const char* value);
  void set_device_name(int index, const char* value, size_t size);
  ::std::string* add_device_name();
  void add_device_name(const ::std::string& value);
  #if LANG_CXX11
  void add_device_name(::std::string&& value);
  #endif
  void add_device_name(const char* value);
  void add_device_name(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& device_name() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_device_name();

  // string device_type = 3;
  void clear_device_type();
  static const int kDeviceTypeFieldNumber = 3;
  const ::std::string& device_type() const;
  void set_device_type(const ::std::string& value);
  #if LANG_CXX11
  void set_device_type(::std::string&& value);
  #endif
  void set_device_type(const char* value);
  void set_device_type(const char* value, size_t size);
  ::std::string* mutable_device_type();
  ::std::string* release_device_type();
  void set_allocated_device_type(::std::string* device_type);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_device_type();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_device_type(
      ::std::string* device_type);

  // int32 group_key = 1;
  void clear_group_key();
  static const int kGroupKeyFieldNumber = 1;
  ::google::protobuf::int32 group_key() const;
  void set_group_key(::google::protobuf::int32 value);

  // int32 group_size = 2;
  void clear_group_size();
  static const int kGroupSizeFieldNumber = 2;
  ::google::protobuf::int32 group_size() const;
  void set_group_size(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.CompleteGroupRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::std::string> device_name_;
  ::google::protobuf::internal::ArenaStringPtr device_type_;
  ::google::protobuf::int32 group_key_;
  ::google::protobuf::int32 group_size_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CompleteGroupResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.CompleteGroupResponse) */ {
 public:
  CompleteGroupResponse();
  virtual ~CompleteGroupResponse();

  CompleteGroupResponse(const CompleteGroupResponse& from);

  inline CompleteGroupResponse& operator=(const CompleteGroupResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CompleteGroupResponse(CompleteGroupResponse&& from) noexcept
    : CompleteGroupResponse() {
    *this = ::std::move(from);
  }

  inline CompleteGroupResponse& operator=(CompleteGroupResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const CompleteGroupResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CompleteGroupResponse* internal_default_instance() {
    return reinterpret_cast<const CompleteGroupResponse*>(
               &_CompleteGroupResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    30;

  void UnsafeArenaSwap(CompleteGroupResponse* other);
  void Swap(CompleteGroupResponse* other);
  friend void swap(CompleteGroupResponse& a, CompleteGroupResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CompleteGroupResponse* New() const final {
    return CreateMaybeMessage<CompleteGroupResponse>(NULL);
  }

  CompleteGroupResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CompleteGroupResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CompleteGroupResponse& from);
  void MergeFrom(const CompleteGroupResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CompleteGroupResponse* other);
  protected:
  explicit CompleteGroupResponse(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string device_name = 5;
  int device_name_size() const;
  void clear_device_name();
  static const int kDeviceNameFieldNumber = 5;
  const ::std::string& device_name(int index) const;
  ::std::string* mutable_device_name(int index);
  void set_device_name(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_device_name(int index, ::std::string&& value);
  #endif
  void set_device_name(int index, const char* value);
  void set_device_name(int index, const char* value, size_t size);
  ::std::string* add_device_name();
  void add_device_name(const ::std::string& value);
  #if LANG_CXX11
  void add_device_name(::std::string&& value);
  #endif
  void add_device_name(const char* value);
  void add_device_name(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& device_name() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_device_name();

  // repeated string task_name = 6;
  int task_name_size() const;
  void clear_task_name();
  static const int kTaskNameFieldNumber = 6;
  const ::std::string& task_name(int index) const;
  ::std::string* mutable_task_name(int index);
  void set_task_name(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_task_name(int index, ::std::string&& value);
  #endif
  void set_task_name(int index, const char* value);
  void set_task_name(int index, const char* value, size_t size);
  ::std::string* add_task_name();
  void add_task_name(const ::std::string& value);
  #if LANG_CXX11
  void add_task_name(::std::string&& value);
  #endif
  void add_task_name(const char* value);
  void add_task_name(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& task_name() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_task_name();

  // string device_type = 3;
  void clear_device_type();
  static const int kDeviceTypeFieldNumber = 3;
  const ::std::string& device_type() const;
  void set_device_type(const ::std::string& value);
  #if LANG_CXX11
  void set_device_type(::std::string&& value);
  #endif
  void set_device_type(const char* value);
  void set_device_type(const char* value, size_t size);
  ::std::string* mutable_device_type();
  ::std::string* release_device_type();
  void set_allocated_device_type(::std::string* device_type);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_device_type();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_device_type(
      ::std::string* device_type);

  // int32 group_key = 1;
  void clear_group_key();
  static const int kGroupKeyFieldNumber = 1;
  ::google::protobuf::int32 group_key() const;
  void set_group_key(::google::protobuf::int32 value);

  // int32 group_size = 2;
  void clear_group_size();
  static const int kGroupSizeFieldNumber = 2;
  ::google::protobuf::int32 group_size() const;
  void set_group_size(::google::protobuf::int32 value);

  // int32 num_tasks = 4;
  void clear_num_tasks();
  static const int kNumTasksFieldNumber = 4;
  ::google::protobuf::int32 num_tasks() const;
  void set_num_tasks(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.CompleteGroupResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::std::string> device_name_;
  ::google::protobuf::RepeatedPtrField< ::std::string> task_name_;
  ::google::protobuf::internal::ArenaStringPtr device_type_;
  ::google::protobuf::int32 group_key_;
  ::google::protobuf::int32 group_size_;
  ::google::protobuf::int32 num_tasks_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CompleteInstanceRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.CompleteInstanceRequest) */ {
 public:
  CompleteInstanceRequest();
  virtual ~CompleteInstanceRequest();

  CompleteInstanceRequest(const CompleteInstanceRequest& from);

  inline CompleteInstanceRequest& operator=(const CompleteInstanceRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CompleteInstanceRequest(CompleteInstanceRequest&& from) noexcept
    : CompleteInstanceRequest() {
    *this = ::std::move(from);
  }

  inline CompleteInstanceRequest& operator=(CompleteInstanceRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const CompleteInstanceRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CompleteInstanceRequest* internal_default_instance() {
    return reinterpret_cast<const CompleteInstanceRequest*>(
               &_CompleteInstanceRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    31;

  void UnsafeArenaSwap(CompleteInstanceRequest* other);
  void Swap(CompleteInstanceRequest* other);
  friend void swap(CompleteInstanceRequest& a, CompleteInstanceRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CompleteInstanceRequest* New() const final {
    return CreateMaybeMessage<CompleteInstanceRequest>(NULL);
  }

  CompleteInstanceRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CompleteInstanceRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CompleteInstanceRequest& from);
  void MergeFrom(const CompleteInstanceRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CompleteInstanceRequest* other);
  protected:
  explicit CompleteInstanceRequest(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int32 subdiv_offset = 9;
  int subdiv_offset_size() const;
  void clear_subdiv_offset();
  static const int kSubdivOffsetFieldNumber = 9;
  ::google::protobuf::int32 subdiv_offset(int index) const;
  void set_subdiv_offset(int index, ::google::protobuf::int32 value);
  void add_subdiv_offset(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      subdiv_offset() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_subdiv_offset();

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      ::std::string* name);

  // string device_type = 8;
  void clear_device_type();
  static const int kDeviceTypeFieldNumber = 8;
  const ::std::string& device_type() const;
  void set_device_type(const ::std::string& value);
  #if LANG_CXX11
  void set_device_type(::std::string&& value);
  #endif
  void set_device_type(const char* value);
  void set_device_type(const char* value, size_t size);
  ::std::string* mutable_device_type();
  ::std::string* release_device_type();
  void set_allocated_device_type(::std::string* device_type);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_device_type();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_device_type(
      ::std::string* device_type);

  // string device = 10;
  void clear_device();
  static const int kDeviceFieldNumber = 10;
  const ::std::string& device() const;
  void set_device(const ::std::string& value);
  #if LANG_CXX11
  void set_device(::std::string&& value);
  #endif
  void set_device(const char* value);
  void set_device(const char* value, size_t size);
  ::std::string* mutable_device();
  ::std::string* release_device();
  void set_allocated_device(::std::string* device);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_device();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_device(
      ::std::string* device);

  // .tensorflow.TensorShapeProto shape = 4;
  bool has_shape() const;
  void clear_shape();
  static const int kShapeFieldNumber = 4;
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape() const;
  public:
  const ::tensorflow::TensorShapeProto& shape() const;
  ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // int32 type = 2;
  void clear_type();
  static const int kTypeFieldNumber = 2;
  ::google::protobuf::int32 type() const;
  void set_type(::google::protobuf::int32 value);

  // .tensorflow.DataType data_type = 3;
  void clear_data_type();
  static const int kDataTypeFieldNumber = 3;
  ::tensorflow::DataType data_type() const;
  void set_data_type(::tensorflow::DataType value);

  // int32 group_key = 5;
  void clear_group_key();
  static const int kGroupKeyFieldNumber = 5;
  ::google::protobuf::int32 group_key() const;
  void set_group_key(::google::protobuf::int32 value);

  // int32 group_size = 6;
  void clear_group_size();
  static const int kGroupSizeFieldNumber = 6;
  ::google::protobuf::int32 group_size() const;
  void set_group_size(::google::protobuf::int32 value);

  // int32 instance_key = 7;
  void clear_instance_key();
  static const int kInstanceKeyFieldNumber = 7;
  ::google::protobuf::int32 instance_key() const;
  void set_instance_key(::google::protobuf::int32 value);

  // bool is_source = 11;
  void clear_is_source();
  static const int kIsSourceFieldNumber = 11;
  bool is_source() const;
  void set_is_source(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.CompleteInstanceRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > subdiv_offset_;
  mutable int _subdiv_offset_cached_byte_size_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::internal::ArenaStringPtr device_type_;
  ::google::protobuf::internal::ArenaStringPtr device_;
  ::tensorflow::TensorShapeProto* shape_;
  ::google::protobuf::int32 type_;
  int data_type_;
  ::google::protobuf::int32 group_key_;
  ::google::protobuf::int32 group_size_;
  ::google::protobuf::int32 instance_key_;
  bool is_source_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CompleteInstanceResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.CompleteInstanceResponse) */ {
 public:
  CompleteInstanceResponse();
  virtual ~CompleteInstanceResponse();

  CompleteInstanceResponse(const CompleteInstanceResponse& from);

  inline CompleteInstanceResponse& operator=(const CompleteInstanceResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CompleteInstanceResponse(CompleteInstanceResponse&& from) noexcept
    : CompleteInstanceResponse() {
    *this = ::std::move(from);
  }

  inline CompleteInstanceResponse& operator=(CompleteInstanceResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const CompleteInstanceResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CompleteInstanceResponse* internal_default_instance() {
    return reinterpret_cast<const CompleteInstanceResponse*>(
               &_CompleteInstanceResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    32;

  void UnsafeArenaSwap(CompleteInstanceResponse* other);
  void Swap(CompleteInstanceResponse* other);
  friend void swap(CompleteInstanceResponse& a, CompleteInstanceResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CompleteInstanceResponse* New() const final {
    return CreateMaybeMessage<CompleteInstanceResponse>(NULL);
  }

  CompleteInstanceResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CompleteInstanceResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CompleteInstanceResponse& from);
  void MergeFrom(const CompleteInstanceResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CompleteInstanceResponse* other);
  protected:
  explicit CompleteInstanceResponse(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // bytes communicator_key = 3;
  void clear_communicator_key();
  static const int kCommunicatorKeyFieldNumber = 3;
  const ::std::string& communicator_key() const;
  void set_communicator_key(const ::std::string& value);
  #if LANG_CXX11
  void set_communicator_key(::std::string&& value);
  #endif
  void set_communicator_key(const char* value);
  void set_communicator_key(const void* value, size_t size);
  ::std::string* mutable_communicator_key();
  ::std::string* release_communicator_key();
  void set_allocated_communicator_key(::std::string* communicator_key);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_communicator_key();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_communicator_key(
      ::std::string* communicator_key);

  // int32 instance_key = 1;
  void clear_instance_key();
  static const int kInstanceKeyFieldNumber = 1;
  ::google::protobuf::int32 instance_key() const;
  void set_instance_key(::google::protobuf::int32 value);

  // int32 source_rank = 2;
  void clear_source_rank();
  static const int kSourceRankFieldNumber = 2;
  ::google::protobuf::int32 source_rank() const;
  void set_source_rank(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.CompleteInstanceResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr communicator_key_;
  ::google::protobuf::int32 instance_key_;
  ::google::protobuf::int32 source_rank_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class GetStepSequenceRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.GetStepSequenceRequest) */ {
 public:
  GetStepSequenceRequest();
  virtual ~GetStepSequenceRequest();

  GetStepSequenceRequest(const GetStepSequenceRequest& from);

  inline GetStepSequenceRequest& operator=(const GetStepSequenceRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  GetStepSequenceRequest(GetStepSequenceRequest&& from) noexcept
    : GetStepSequenceRequest() {
    *this = ::std::move(from);
  }

  inline GetStepSequenceRequest& operator=(GetStepSequenceRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const GetStepSequenceRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GetStepSequenceRequest* internal_default_instance() {
    return reinterpret_cast<const GetStepSequenceRequest*>(
               &_GetStepSequenceRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    33;

  void UnsafeArenaSwap(GetStepSequenceRequest* other);
  void Swap(GetStepSequenceRequest* other);
  friend void swap(GetStepSequenceRequest& a, GetStepSequenceRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline GetStepSequenceRequest* New() const final {
    return CreateMaybeMessage<GetStepSequenceRequest>(NULL);
  }

  GetStepSequenceRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<GetStepSequenceRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const GetStepSequenceRequest& from);
  void MergeFrom(const GetStepSequenceRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetStepSequenceRequest* other);
  protected:
  explicit GetStepSequenceRequest(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int64 graph_key = 1;
  int graph_key_size() const;
  void clear_graph_key();
  static const int kGraphKeyFieldNumber = 1;
  ::google::protobuf::int64 graph_key(int index) const;
  void set_graph_key(int index, ::google::protobuf::int64 value);
  void add_graph_key(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      graph_key() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_graph_key();

  // @@protoc_insertion_point(class_scope:tensorflow.GetStepSequenceRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > graph_key_;
  mutable int _graph_key_cached_byte_size_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class StepSequence : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.StepSequence) */ {
 public:
  StepSequence();
  virtual ~StepSequence();

  StepSequence(const StepSequence& from);

  inline StepSequence& operator=(const StepSequence& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  StepSequence(StepSequence&& from) noexcept
    : StepSequence() {
    *this = ::std::move(from);
  }

  inline StepSequence& operator=(StepSequence&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const StepSequence& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const StepSequence* internal_default_instance() {
    return reinterpret_cast<const StepSequence*>(
               &_StepSequence_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    34;

  void UnsafeArenaSwap(StepSequence* other);
  void Swap(StepSequence* other);
  friend void swap(StepSequence& a, StepSequence& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline StepSequence* New() const final {
    return CreateMaybeMessage<StepSequence>(NULL);
  }

  StepSequence* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<StepSequence>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const StepSequence& from);
  void MergeFrom(const StepSequence& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StepSequence* other);
  protected:
  explicit StepSequence(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 graph_key = 1;
  void clear_graph_key();
  static const int kGraphKeyFieldNumber = 1;
  ::google::protobuf::int64 graph_key() const;
  void set_graph_key(::google::protobuf::int64 value);

  // int64 next_step_id = 2;
  void clear_next_step_id();
  static const int kNextStepIdFieldNumber = 2;
  ::google::protobuf::int64 next_step_id() const;
  void set_next_step_id(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.StepSequence)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::int64 graph_key_;
  ::google::protobuf::int64 next_step_id_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class GetStepSequenceResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.GetStepSequenceResponse) */ {
 public:
  GetStepSequenceResponse();
  virtual ~GetStepSequenceResponse();

  GetStepSequenceResponse(const GetStepSequenceResponse& from);

  inline GetStepSequenceResponse& operator=(const GetStepSequenceResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  GetStepSequenceResponse(GetStepSequenceResponse&& from) noexcept
    : GetStepSequenceResponse() {
    *this = ::std::move(from);
  }

  inline GetStepSequenceResponse& operator=(GetStepSequenceResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const GetStepSequenceResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GetStepSequenceResponse* internal_default_instance() {
    return reinterpret_cast<const GetStepSequenceResponse*>(
               &_GetStepSequenceResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    35;

  void UnsafeArenaSwap(GetStepSequenceResponse* other);
  void Swap(GetStepSequenceResponse* other);
  friend void swap(GetStepSequenceResponse& a, GetStepSequenceResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline GetStepSequenceResponse* New() const final {
    return CreateMaybeMessage<GetStepSequenceResponse>(NULL);
  }

  GetStepSequenceResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<GetStepSequenceResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const GetStepSequenceResponse& from);
  void MergeFrom(const GetStepSequenceResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetStepSequenceResponse* other);
  protected:
  explicit GetStepSequenceResponse(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.StepSequence step_sequence = 1;
  int step_sequence_size() const;
  void clear_step_sequence();
  static const int kStepSequenceFieldNumber = 1;
  ::tensorflow::StepSequence* mutable_step_sequence(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::StepSequence >*
      mutable_step_sequence();
  const ::tensorflow::StepSequence& step_sequence(int index) const;
  ::tensorflow::StepSequence* add_step_sequence();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::StepSequence >&
      step_sequence() const;

  // @@protoc_insertion_point(class_scope:tensorflow.GetStepSequenceResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::StepSequence > step_sequence_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fworker_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GetStatusRequest

// -------------------------------------------------------------------

// GetStatusResponse

// repeated .tensorflow.DeviceAttributes device_attributes = 1;
inline int GetStatusResponse::device_attributes_size() const {
  return device_attributes_.size();
}
inline ::tensorflow::DeviceAttributes* GetStatusResponse::mutable_device_attributes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GetStatusResponse.device_attributes)
  return device_attributes_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
GetStatusResponse::mutable_device_attributes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GetStatusResponse.device_attributes)
  return &device_attributes_;
}
inline const ::tensorflow::DeviceAttributes& GetStatusResponse::device_attributes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GetStatusResponse.device_attributes)
  return device_attributes_.Get(index);
}
inline ::tensorflow::DeviceAttributes* GetStatusResponse::add_device_attributes() {
  // @@protoc_insertion_point(field_add:tensorflow.GetStatusResponse.device_attributes)
  return device_attributes_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
GetStatusResponse::device_attributes() const {
  // @@protoc_insertion_point(field_list:tensorflow.GetStatusResponse.device_attributes)
  return device_attributes_;
}

// -------------------------------------------------------------------

// CreateWorkerSessionRequest

// string session_handle = 1;
inline void CreateWorkerSessionRequest::clear_session_handle() {
  session_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& CreateWorkerSessionRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.CreateWorkerSessionRequest.session_handle)
  return session_handle_.Get();
}
inline void CreateWorkerSessionRequest::set_session_handle(const ::std::string& value) {
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CreateWorkerSessionRequest.session_handle)
}
#if LANG_CXX11
inline void CreateWorkerSessionRequest::set_session_handle(::std::string&& value) {
  
  session_handle_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CreateWorkerSessionRequest.session_handle)
}
#endif
inline void CreateWorkerSessionRequest::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CreateWorkerSessionRequest.session_handle)
}
inline void CreateWorkerSessionRequest::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CreateWorkerSessionRequest.session_handle)
}
inline ::std::string* CreateWorkerSessionRequest::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CreateWorkerSessionRequest.session_handle)
  return session_handle_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* CreateWorkerSessionRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.CreateWorkerSessionRequest.session_handle)
  
  return session_handle_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CreateWorkerSessionRequest::set_allocated_session_handle(::std::string* session_handle) {
  if (session_handle != NULL) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CreateWorkerSessionRequest.session_handle)
}
inline ::std::string* CreateWorkerSessionRequest::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CreateWorkerSessionRequest.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return session_handle_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CreateWorkerSessionRequest::unsafe_arena_set_allocated_session_handle(
    ::std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (session_handle != NULL) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CreateWorkerSessionRequest.session_handle)
}

// .tensorflow.ServerDef server_def = 2;
inline bool CreateWorkerSessionRequest::has_server_def() const {
  return this != internal_default_instance() && server_def_ != NULL;
}
inline const ::tensorflow::ServerDef& CreateWorkerSessionRequest::_internal_server_def() const {
  return *server_def_;
}
inline const ::tensorflow::ServerDef& CreateWorkerSessionRequest::server_def() const {
  const ::tensorflow::ServerDef* p = server_def_;
  // @@protoc_insertion_point(field_get:tensorflow.CreateWorkerSessionRequest.server_def)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::ServerDef*>(
      &::tensorflow::_ServerDef_default_instance_);
}
inline ::tensorflow::ServerDef* CreateWorkerSessionRequest::release_server_def() {
  // @@protoc_insertion_point(field_release:tensorflow.CreateWorkerSessionRequest.server_def)
  
  ::tensorflow::ServerDef* temp = server_def_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  server_def_ = NULL;
  return temp;
}
inline ::tensorflow::ServerDef* CreateWorkerSessionRequest::unsafe_arena_release_server_def() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CreateWorkerSessionRequest.server_def)
  
  ::tensorflow::ServerDef* temp = server_def_;
  server_def_ = NULL;
  return temp;
}
inline ::tensorflow::ServerDef* CreateWorkerSessionRequest::mutable_server_def() {
  
  if (server_def_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::ServerDef>(GetArenaNoVirtual());
    server_def_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.CreateWorkerSessionRequest.server_def)
  return server_def_;
}
inline void CreateWorkerSessionRequest::set_allocated_server_def(::tensorflow::ServerDef* server_def) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(server_def_);
  }
  if (server_def) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(server_def)->GetArena();
    if (message_arena != submessage_arena) {
      server_def = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, server_def, submessage_arena);
    }
    
  } else {
    
  }
  server_def_ = server_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CreateWorkerSessionRequest.server_def)
}

// bool isolate_session_state = 3;
inline void CreateWorkerSessionRequest::clear_isolate_session_state() {
  isolate_session_state_ = false;
}
inline bool CreateWorkerSessionRequest::isolate_session_state() const {
  // @@protoc_insertion_point(field_get:tensorflow.CreateWorkerSessionRequest.isolate_session_state)
  return isolate_session_state_;
}
inline void CreateWorkerSessionRequest::set_isolate_session_state(bool value) {
  
  isolate_session_state_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CreateWorkerSessionRequest.isolate_session_state)
}

// -------------------------------------------------------------------

// CreateWorkerSessionResponse

// -------------------------------------------------------------------

// DeleteWorkerSessionRequest

// string session_handle = 1;
inline void DeleteWorkerSessionRequest::clear_session_handle() {
  session_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& DeleteWorkerSessionRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeleteWorkerSessionRequest.session_handle)
  return session_handle_.Get();
}
inline void DeleteWorkerSessionRequest::set_session_handle(const ::std::string& value) {
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DeleteWorkerSessionRequest.session_handle)
}
#if LANG_CXX11
inline void DeleteWorkerSessionRequest::set_session_handle(::std::string&& value) {
  
  session_handle_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DeleteWorkerSessionRequest.session_handle)
}
#endif
inline void DeleteWorkerSessionRequest::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DeleteWorkerSessionRequest.session_handle)
}
inline void DeleteWorkerSessionRequest::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DeleteWorkerSessionRequest.session_handle)
}
inline ::std::string* DeleteWorkerSessionRequest::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DeleteWorkerSessionRequest.session_handle)
  return session_handle_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* DeleteWorkerSessionRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.DeleteWorkerSessionRequest.session_handle)
  
  return session_handle_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DeleteWorkerSessionRequest::set_allocated_session_handle(::std::string* session_handle) {
  if (session_handle != NULL) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeleteWorkerSessionRequest.session_handle)
}
inline ::std::string* DeleteWorkerSessionRequest::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DeleteWorkerSessionRequest.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return session_handle_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DeleteWorkerSessionRequest::unsafe_arena_set_allocated_session_handle(
    ::std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (session_handle != NULL) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DeleteWorkerSessionRequest.session_handle)
}

// -------------------------------------------------------------------

// DeleteWorkerSessionResponse

// -------------------------------------------------------------------

// RegisterGraphRequest

// string session_handle = 1;
inline void RegisterGraphRequest::clear_session_handle() {
  session_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& RegisterGraphRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.RegisterGraphRequest.session_handle)
  return session_handle_.Get();
}
inline void RegisterGraphRequest::set_session_handle(const ::std::string& value) {
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RegisterGraphRequest.session_handle)
}
#if LANG_CXX11
inline void RegisterGraphRequest::set_session_handle(::std::string&& value) {
  
  session_handle_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RegisterGraphRequest.session_handle)
}
#endif
inline void RegisterGraphRequest::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RegisterGraphRequest.session_handle)
}
inline void RegisterGraphRequest::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RegisterGraphRequest.session_handle)
}
inline ::std::string* RegisterGraphRequest::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RegisterGraphRequest.session_handle)
  return session_handle_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* RegisterGraphRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.RegisterGraphRequest.session_handle)
  
  return session_handle_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RegisterGraphRequest::set_allocated_session_handle(::std::string* session_handle) {
  if (session_handle != NULL) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RegisterGraphRequest.session_handle)
}
inline ::std::string* RegisterGraphRequest::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RegisterGraphRequest.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return session_handle_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RegisterGraphRequest::unsafe_arena_set_allocated_session_handle(
    ::std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (session_handle != NULL) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RegisterGraphRequest.session_handle)
}

// bool create_worker_session_called = 6;
inline void RegisterGraphRequest::clear_create_worker_session_called() {
  create_worker_session_called_ = false;
}
inline bool RegisterGraphRequest::create_worker_session_called() const {
  // @@protoc_insertion_point(field_get:tensorflow.RegisterGraphRequest.create_worker_session_called)
  return create_worker_session_called_;
}
inline void RegisterGraphRequest::set_create_worker_session_called(bool value) {
  
  create_worker_session_called_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RegisterGraphRequest.create_worker_session_called)
}

// .tensorflow.GraphDef graph_def = 2;
inline bool RegisterGraphRequest::has_graph_def() const {
  return this != internal_default_instance() && graph_def_ != NULL;
}
inline const ::tensorflow::GraphDef& RegisterGraphRequest::_internal_graph_def() const {
  return *graph_def_;
}
inline const ::tensorflow::GraphDef& RegisterGraphRequest::graph_def() const {
  const ::tensorflow::GraphDef* p = graph_def_;
  // @@protoc_insertion_point(field_get:tensorflow.RegisterGraphRequest.graph_def)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::GraphDef*>(
      &::tensorflow::_GraphDef_default_instance_);
}
inline ::tensorflow::GraphDef* RegisterGraphRequest::release_graph_def() {
  // @@protoc_insertion_point(field_release:tensorflow.RegisterGraphRequest.graph_def)
  
  ::tensorflow::GraphDef* temp = graph_def_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  graph_def_ = NULL;
  return temp;
}
inline ::tensorflow::GraphDef* RegisterGraphRequest::unsafe_arena_release_graph_def() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RegisterGraphRequest.graph_def)
  
  ::tensorflow::GraphDef* temp = graph_def_;
  graph_def_ = NULL;
  return temp;
}
inline ::tensorflow::GraphDef* RegisterGraphRequest::mutable_graph_def() {
  
  if (graph_def_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::GraphDef>(GetArenaNoVirtual());
    graph_def_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RegisterGraphRequest.graph_def)
  return graph_def_;
}
inline void RegisterGraphRequest::set_allocated_graph_def(::tensorflow::GraphDef* graph_def) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(graph_def_);
  }
  if (graph_def) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(graph_def)->GetArena();
    if (message_arena != submessage_arena) {
      graph_def = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, graph_def, submessage_arena);
    }
    
  } else {
    
  }
  graph_def_ = graph_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RegisterGraphRequest.graph_def)
}

// bool has_control_flow = 3 [deprecated = true];
inline void RegisterGraphRequest::clear_has_control_flow() {
  has_control_flow_ = false;
}
inline bool RegisterGraphRequest::has_control_flow() const {
  // @@protoc_insertion_point(field_get:tensorflow.RegisterGraphRequest.has_control_flow)
  return has_control_flow_;
}
inline void RegisterGraphRequest::set_has_control_flow(bool value) {
  
  has_control_flow_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RegisterGraphRequest.has_control_flow)
}

// .tensorflow.GraphOptions graph_options = 4;
inline bool RegisterGraphRequest::has_graph_options() const {
  return this != internal_default_instance() && graph_options_ != NULL;
}
inline const ::tensorflow::GraphOptions& RegisterGraphRequest::_internal_graph_options() const {
  return *graph_options_;
}
inline const ::tensorflow::GraphOptions& RegisterGraphRequest::graph_options() const {
  const ::tensorflow::GraphOptions* p = graph_options_;
  // @@protoc_insertion_point(field_get:tensorflow.RegisterGraphRequest.graph_options)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::GraphOptions*>(
      &::tensorflow::_GraphOptions_default_instance_);
}
inline ::tensorflow::GraphOptions* RegisterGraphRequest::release_graph_options() {
  // @@protoc_insertion_point(field_release:tensorflow.RegisterGraphRequest.graph_options)
  
  ::tensorflow::GraphOptions* temp = graph_options_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  graph_options_ = NULL;
  return temp;
}
inline ::tensorflow::GraphOptions* RegisterGraphRequest::unsafe_arena_release_graph_options() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RegisterGraphRequest.graph_options)
  
  ::tensorflow::GraphOptions* temp = graph_options_;
  graph_options_ = NULL;
  return temp;
}
inline ::tensorflow::GraphOptions* RegisterGraphRequest::mutable_graph_options() {
  
  if (graph_options_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::GraphOptions>(GetArenaNoVirtual());
    graph_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RegisterGraphRequest.graph_options)
  return graph_options_;
}
inline void RegisterGraphRequest::set_allocated_graph_options(::tensorflow::GraphOptions* graph_options) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(graph_options_);
  }
  if (graph_options) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(graph_options)->GetArena();
    if (message_arena != submessage_arena) {
      graph_options = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, graph_options, submessage_arena);
    }
    
  } else {
    
  }
  graph_options_ = graph_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RegisterGraphRequest.graph_options)
}

// .tensorflow.DebugOptions debug_options = 5;
inline bool RegisterGraphRequest::has_debug_options() const {
  return this != internal_default_instance() && debug_options_ != NULL;
}
inline const ::tensorflow::DebugOptions& RegisterGraphRequest::_internal_debug_options() const {
  return *debug_options_;
}
inline const ::tensorflow::DebugOptions& RegisterGraphRequest::debug_options() const {
  const ::tensorflow::DebugOptions* p = debug_options_;
  // @@protoc_insertion_point(field_get:tensorflow.RegisterGraphRequest.debug_options)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::DebugOptions*>(
      &::tensorflow::_DebugOptions_default_instance_);
}
inline ::tensorflow::DebugOptions* RegisterGraphRequest::release_debug_options() {
  // @@protoc_insertion_point(field_release:tensorflow.RegisterGraphRequest.debug_options)
  
  ::tensorflow::DebugOptions* temp = debug_options_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  debug_options_ = NULL;
  return temp;
}
inline ::tensorflow::DebugOptions* RegisterGraphRequest::unsafe_arena_release_debug_options() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RegisterGraphRequest.debug_options)
  
  ::tensorflow::DebugOptions* temp = debug_options_;
  debug_options_ = NULL;
  return temp;
}
inline ::tensorflow::DebugOptions* RegisterGraphRequest::mutable_debug_options() {
  
  if (debug_options_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::DebugOptions>(GetArenaNoVirtual());
    debug_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RegisterGraphRequest.debug_options)
  return debug_options_;
}
inline void RegisterGraphRequest::set_allocated_debug_options(::tensorflow::DebugOptions* debug_options) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(debug_options_);
  }
  if (debug_options) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(debug_options)->GetArena();
    if (message_arena != submessage_arena) {
      debug_options = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, debug_options, submessage_arena);
    }
    
  } else {
    
  }
  debug_options_ = debug_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RegisterGraphRequest.debug_options)
}

// int64 collective_graph_key = 7;
inline void RegisterGraphRequest::clear_collective_graph_key() {
  collective_graph_key_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 RegisterGraphRequest::collective_graph_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.RegisterGraphRequest.collective_graph_key)
  return collective_graph_key_;
}
inline void RegisterGraphRequest::set_collective_graph_key(::google::protobuf::int64 value) {
  
  collective_graph_key_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RegisterGraphRequest.collective_graph_key)
}

// -------------------------------------------------------------------

// RegisterGraphResponse

// string graph_handle = 1;
inline void RegisterGraphResponse::clear_graph_handle() {
  graph_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& RegisterGraphResponse::graph_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.RegisterGraphResponse.graph_handle)
  return graph_handle_.Get();
}
inline void RegisterGraphResponse::set_graph_handle(const ::std::string& value) {
  
  graph_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RegisterGraphResponse.graph_handle)
}
#if LANG_CXX11
inline void RegisterGraphResponse::set_graph_handle(::std::string&& value) {
  
  graph_handle_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RegisterGraphResponse.graph_handle)
}
#endif
inline void RegisterGraphResponse::set_graph_handle(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  graph_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RegisterGraphResponse.graph_handle)
}
inline void RegisterGraphResponse::set_graph_handle(const char* value,
    size_t size) {
  
  graph_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RegisterGraphResponse.graph_handle)
}
inline ::std::string* RegisterGraphResponse::mutable_graph_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RegisterGraphResponse.graph_handle)
  return graph_handle_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* RegisterGraphResponse::release_graph_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.RegisterGraphResponse.graph_handle)
  
  return graph_handle_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RegisterGraphResponse::set_allocated_graph_handle(::std::string* graph_handle) {
  if (graph_handle != NULL) {
    
  } else {
    
  }
  graph_handle_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), graph_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RegisterGraphResponse.graph_handle)
}
inline ::std::string* RegisterGraphResponse::unsafe_arena_release_graph_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RegisterGraphResponse.graph_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return graph_handle_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RegisterGraphResponse::unsafe_arena_set_allocated_graph_handle(
    ::std::string* graph_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (graph_handle != NULL) {
    
  } else {
    
  }
  graph_handle_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      graph_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RegisterGraphResponse.graph_handle)
}

// -------------------------------------------------------------------

// DeregisterGraphRequest

// string session_handle = 2;
inline void DeregisterGraphRequest::clear_session_handle() {
  session_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& DeregisterGraphRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeregisterGraphRequest.session_handle)
  return session_handle_.Get();
}
inline void DeregisterGraphRequest::set_session_handle(const ::std::string& value) {
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DeregisterGraphRequest.session_handle)
}
#if LANG_CXX11
inline void DeregisterGraphRequest::set_session_handle(::std::string&& value) {
  
  session_handle_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DeregisterGraphRequest.session_handle)
}
#endif
inline void DeregisterGraphRequest::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DeregisterGraphRequest.session_handle)
}
inline void DeregisterGraphRequest::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DeregisterGraphRequest.session_handle)
}
inline ::std::string* DeregisterGraphRequest::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DeregisterGraphRequest.session_handle)
  return session_handle_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* DeregisterGraphRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.DeregisterGraphRequest.session_handle)
  
  return session_handle_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DeregisterGraphRequest::set_allocated_session_handle(::std::string* session_handle) {
  if (session_handle != NULL) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeregisterGraphRequest.session_handle)
}
inline ::std::string* DeregisterGraphRequest::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DeregisterGraphRequest.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return session_handle_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DeregisterGraphRequest::unsafe_arena_set_allocated_session_handle(
    ::std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (session_handle != NULL) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DeregisterGraphRequest.session_handle)
}

// bool create_worker_session_called = 3;
inline void DeregisterGraphRequest::clear_create_worker_session_called() {
  create_worker_session_called_ = false;
}
inline bool DeregisterGraphRequest::create_worker_session_called() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeregisterGraphRequest.create_worker_session_called)
  return create_worker_session_called_;
}
inline void DeregisterGraphRequest::set_create_worker_session_called(bool value) {
  
  create_worker_session_called_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DeregisterGraphRequest.create_worker_session_called)
}

// string graph_handle = 1;
inline void DeregisterGraphRequest::clear_graph_handle() {
  graph_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& DeregisterGraphRequest::graph_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeregisterGraphRequest.graph_handle)
  return graph_handle_.Get();
}
inline void DeregisterGraphRequest::set_graph_handle(const ::std::string& value) {
  
  graph_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DeregisterGraphRequest.graph_handle)
}
#if LANG_CXX11
inline void DeregisterGraphRequest::set_graph_handle(::std::string&& value) {
  
  graph_handle_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DeregisterGraphRequest.graph_handle)
}
#endif
inline void DeregisterGraphRequest::set_graph_handle(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  graph_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DeregisterGraphRequest.graph_handle)
}
inline void DeregisterGraphRequest::set_graph_handle(const char* value,
    size_t size) {
  
  graph_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DeregisterGraphRequest.graph_handle)
}
inline ::std::string* DeregisterGraphRequest::mutable_graph_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DeregisterGraphRequest.graph_handle)
  return graph_handle_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* DeregisterGraphRequest::release_graph_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.DeregisterGraphRequest.graph_handle)
  
  return graph_handle_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DeregisterGraphRequest::set_allocated_graph_handle(::std::string* graph_handle) {
  if (graph_handle != NULL) {
    
  } else {
    
  }
  graph_handle_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), graph_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeregisterGraphRequest.graph_handle)
}
inline ::std::string* DeregisterGraphRequest::unsafe_arena_release_graph_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DeregisterGraphRequest.graph_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return graph_handle_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DeregisterGraphRequest::unsafe_arena_set_allocated_graph_handle(
    ::std::string* graph_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (graph_handle != NULL) {
    
  } else {
    
  }
  graph_handle_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      graph_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DeregisterGraphRequest.graph_handle)
}

// -------------------------------------------------------------------

// DeregisterGraphResponse

// -------------------------------------------------------------------

// CleanupAllRequest

// repeated string container = 1;
inline int CleanupAllRequest::container_size() const {
  return container_.size();
}
inline void CleanupAllRequest::clear_container() {
  container_.Clear();
}
inline const ::std::string& CleanupAllRequest::container(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CleanupAllRequest.container)
  return container_.Get(index);
}
inline ::std::string* CleanupAllRequest::mutable_container(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CleanupAllRequest.container)
  return container_.Mutable(index);
}
inline void CleanupAllRequest::set_container(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.CleanupAllRequest.container)
  container_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void CleanupAllRequest::set_container(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.CleanupAllRequest.container)
  container_.Mutable(index)->assign(std::move(value));
}
#endif
inline void CleanupAllRequest::set_container(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  container_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.CleanupAllRequest.container)
}
inline void CleanupAllRequest::set_container(int index, const char* value, size_t size) {
  container_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CleanupAllRequest.container)
}
inline ::std::string* CleanupAllRequest::add_container() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.CleanupAllRequest.container)
  return container_.Add();
}
inline void CleanupAllRequest::add_container(const ::std::string& value) {
  container_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.CleanupAllRequest.container)
}
#if LANG_CXX11
inline void CleanupAllRequest::add_container(::std::string&& value) {
  container_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.CleanupAllRequest.container)
}
#endif
inline void CleanupAllRequest::add_container(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  container_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.CleanupAllRequest.container)
}
inline void CleanupAllRequest::add_container(const char* value, size_t size) {
  container_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.CleanupAllRequest.container)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
CleanupAllRequest::container() const {
  // @@protoc_insertion_point(field_list:tensorflow.CleanupAllRequest.container)
  return container_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
CleanupAllRequest::mutable_container() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CleanupAllRequest.container)
  return &container_;
}

// -------------------------------------------------------------------

// CleanupAllResponse

// -------------------------------------------------------------------

// ExecutorOpts

// bool record_costs = 1;
inline void ExecutorOpts::clear_record_costs() {
  record_costs_ = false;
}
inline bool ExecutorOpts::record_costs() const {
  // @@protoc_insertion_point(field_get:tensorflow.ExecutorOpts.record_costs)
  return record_costs_;
}
inline void ExecutorOpts::set_record_costs(bool value) {
  
  record_costs_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ExecutorOpts.record_costs)
}

// bool record_timeline = 3;
inline void ExecutorOpts::clear_record_timeline() {
  record_timeline_ = false;
}
inline bool ExecutorOpts::record_timeline() const {
  // @@protoc_insertion_point(field_get:tensorflow.ExecutorOpts.record_timeline)
  return record_timeline_;
}
inline void ExecutorOpts::set_record_timeline(bool value) {
  
  record_timeline_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ExecutorOpts.record_timeline)
}

// bool record_partition_graphs = 4;
inline void ExecutorOpts::clear_record_partition_graphs() {
  record_partition_graphs_ = false;
}
inline bool ExecutorOpts::record_partition_graphs() const {
  // @@protoc_insertion_point(field_get:tensorflow.ExecutorOpts.record_partition_graphs)
  return record_partition_graphs_;
}
inline void ExecutorOpts::set_record_partition_graphs(bool value) {
  
  record_partition_graphs_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ExecutorOpts.record_partition_graphs)
}

// bool report_tensor_allocations_upon_oom = 5;
inline void ExecutorOpts::clear_report_tensor_allocations_upon_oom() {
  report_tensor_allocations_upon_oom_ = false;
}
inline bool ExecutorOpts::report_tensor_allocations_upon_oom() const {
  // @@protoc_insertion_point(field_get:tensorflow.ExecutorOpts.report_tensor_allocations_upon_oom)
  return report_tensor_allocations_upon_oom_;
}
inline void ExecutorOpts::set_report_tensor_allocations_upon_oom(bool value) {
  
  report_tensor_allocations_upon_oom_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ExecutorOpts.report_tensor_allocations_upon_oom)
}

// -------------------------------------------------------------------

// RunGraphRequest

// string session_handle = 8;
inline void RunGraphRequest::clear_session_handle() {
  session_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& RunGraphRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.session_handle)
  return session_handle_.Get();
}
inline void RunGraphRequest::set_session_handle(const ::std::string& value) {
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.session_handle)
}
#if LANG_CXX11
inline void RunGraphRequest::set_session_handle(::std::string&& value) {
  
  session_handle_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RunGraphRequest.session_handle)
}
#endif
inline void RunGraphRequest::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RunGraphRequest.session_handle)
}
inline void RunGraphRequest::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RunGraphRequest.session_handle)
}
inline ::std::string* RunGraphRequest::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphRequest.session_handle)
  return session_handle_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* RunGraphRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.RunGraphRequest.session_handle)
  
  return session_handle_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RunGraphRequest::set_allocated_session_handle(::std::string* session_handle) {
  if (session_handle != NULL) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunGraphRequest.session_handle)
}
inline ::std::string* RunGraphRequest::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunGraphRequest.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return session_handle_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RunGraphRequest::unsafe_arena_set_allocated_session_handle(
    ::std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (session_handle != NULL) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunGraphRequest.session_handle)
}

// bool create_worker_session_called = 10;
inline void RunGraphRequest::clear_create_worker_session_called() {
  create_worker_session_called_ = false;
}
inline bool RunGraphRequest::create_worker_session_called() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.create_worker_session_called)
  return create_worker_session_called_;
}
inline void RunGraphRequest::set_create_worker_session_called(bool value) {
  
  create_worker_session_called_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.create_worker_session_called)
}

// string graph_handle = 1;
inline void RunGraphRequest::clear_graph_handle() {
  graph_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& RunGraphRequest::graph_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.graph_handle)
  return graph_handle_.Get();
}
inline void RunGraphRequest::set_graph_handle(const ::std::string& value) {
  
  graph_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.graph_handle)
}
#if LANG_CXX11
inline void RunGraphRequest::set_graph_handle(::std::string&& value) {
  
  graph_handle_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RunGraphRequest.graph_handle)
}
#endif
inline void RunGraphRequest::set_graph_handle(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  graph_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RunGraphRequest.graph_handle)
}
inline void RunGraphRequest::set_graph_handle(const char* value,
    size_t size) {
  
  graph_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RunGraphRequest.graph_handle)
}
inline ::std::string* RunGraphRequest::mutable_graph_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphRequest.graph_handle)
  return graph_handle_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* RunGraphRequest::release_graph_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.RunGraphRequest.graph_handle)
  
  return graph_handle_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RunGraphRequest::set_allocated_graph_handle(::std::string* graph_handle) {
  if (graph_handle != NULL) {
    
  } else {
    
  }
  graph_handle_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), graph_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunGraphRequest.graph_handle)
}
inline ::std::string* RunGraphRequest::unsafe_arena_release_graph_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunGraphRequest.graph_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return graph_handle_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RunGraphRequest::unsafe_arena_set_allocated_graph_handle(
    ::std::string* graph_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (graph_handle != NULL) {
    
  } else {
    
  }
  graph_handle_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      graph_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunGraphRequest.graph_handle)
}

// int64 step_id = 2;
inline void RunGraphRequest::clear_step_id() {
  step_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 RunGraphRequest::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.step_id)
  return step_id_;
}
inline void RunGraphRequest::set_step_id(::google::protobuf::int64 value) {
  
  step_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.step_id)
}

// .tensorflow.ExecutorOpts exec_opts = 5;
inline bool RunGraphRequest::has_exec_opts() const {
  return this != internal_default_instance() && exec_opts_ != NULL;
}
inline void RunGraphRequest::clear_exec_opts() {
  if (GetArenaNoVirtual() == NULL && exec_opts_ != NULL) {
    delete exec_opts_;
  }
  exec_opts_ = NULL;
}
inline const ::tensorflow::ExecutorOpts& RunGraphRequest::_internal_exec_opts() const {
  return *exec_opts_;
}
inline const ::tensorflow::ExecutorOpts& RunGraphRequest::exec_opts() const {
  const ::tensorflow::ExecutorOpts* p = exec_opts_;
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.exec_opts)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::ExecutorOpts*>(
      &::tensorflow::_ExecutorOpts_default_instance_);
}
inline ::tensorflow::ExecutorOpts* RunGraphRequest::release_exec_opts() {
  // @@protoc_insertion_point(field_release:tensorflow.RunGraphRequest.exec_opts)
  
  ::tensorflow::ExecutorOpts* temp = exec_opts_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  exec_opts_ = NULL;
  return temp;
}
inline ::tensorflow::ExecutorOpts* RunGraphRequest::unsafe_arena_release_exec_opts() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunGraphRequest.exec_opts)
  
  ::tensorflow::ExecutorOpts* temp = exec_opts_;
  exec_opts_ = NULL;
  return temp;
}
inline ::tensorflow::ExecutorOpts* RunGraphRequest::mutable_exec_opts() {
  
  if (exec_opts_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::ExecutorOpts>(GetArenaNoVirtual());
    exec_opts_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphRequest.exec_opts)
  return exec_opts_;
}
inline void RunGraphRequest::set_allocated_exec_opts(::tensorflow::ExecutorOpts* exec_opts) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete exec_opts_;
  }
  if (exec_opts) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(exec_opts);
    if (message_arena != submessage_arena) {
      exec_opts = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, exec_opts, submessage_arena);
    }
    
  } else {
    
  }
  exec_opts_ = exec_opts;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunGraphRequest.exec_opts)
}

// repeated .tensorflow.NamedTensorProto send = 3;
inline int RunGraphRequest::send_size() const {
  return send_.size();
}
inline ::tensorflow::NamedTensorProto* RunGraphRequest::mutable_send(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphRequest.send)
  return send_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::NamedTensorProto >*
RunGraphRequest::mutable_send() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunGraphRequest.send)
  return &send_;
}
inline const ::tensorflow::NamedTensorProto& RunGraphRequest::send(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.send)
  return send_.Get(index);
}
inline ::tensorflow::NamedTensorProto* RunGraphRequest::add_send() {
  // @@protoc_insertion_point(field_add:tensorflow.RunGraphRequest.send)
  return send_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::NamedTensorProto >&
RunGraphRequest::send() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunGraphRequest.send)
  return send_;
}

// repeated string recv_key = 4;
inline int RunGraphRequest::recv_key_size() const {
  return recv_key_.size();
}
inline void RunGraphRequest::clear_recv_key() {
  recv_key_.Clear();
}
inline const ::std::string& RunGraphRequest::recv_key(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.recv_key)
  return recv_key_.Get(index);
}
inline ::std::string* RunGraphRequest::mutable_recv_key(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphRequest.recv_key)
  return recv_key_.Mutable(index);
}
inline void RunGraphRequest::set_recv_key(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.recv_key)
  recv_key_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void RunGraphRequest::set_recv_key(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.recv_key)
  recv_key_.Mutable(index)->assign(std::move(value));
}
#endif
inline void RunGraphRequest::set_recv_key(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  recv_key_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.RunGraphRequest.recv_key)
}
inline void RunGraphRequest::set_recv_key(int index, const char* value, size_t size) {
  recv_key_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RunGraphRequest.recv_key)
}
inline ::std::string* RunGraphRequest::add_recv_key() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.RunGraphRequest.recv_key)
  return recv_key_.Add();
}
inline void RunGraphRequest::add_recv_key(const ::std::string& value) {
  recv_key_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.RunGraphRequest.recv_key)
}
#if LANG_CXX11
inline void RunGraphRequest::add_recv_key(::std::string&& value) {
  recv_key_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.RunGraphRequest.recv_key)
}
#endif
inline void RunGraphRequest::add_recv_key(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  recv_key_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.RunGraphRequest.recv_key)
}
inline void RunGraphRequest::add_recv_key(const char* value, size_t size) {
  recv_key_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.RunGraphRequest.recv_key)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
RunGraphRequest::recv_key() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunGraphRequest.recv_key)
  return recv_key_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
RunGraphRequest::mutable_recv_key() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunGraphRequest.recv_key)
  return &recv_key_;
}

// bool is_partial = 6;
inline void RunGraphRequest::clear_is_partial() {
  is_partial_ = false;
}
inline bool RunGraphRequest::is_partial() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.is_partial)
  return is_partial_;
}
inline void RunGraphRequest::set_is_partial(bool value) {
  
  is_partial_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.is_partial)
}

// bool is_last_partial_run = 7;
inline void RunGraphRequest::clear_is_last_partial_run() {
  is_last_partial_run_ = false;
}
inline bool RunGraphRequest::is_last_partial_run() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.is_last_partial_run)
  return is_last_partial_run_;
}
inline void RunGraphRequest::set_is_last_partial_run(bool value) {
  
  is_last_partial_run_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.is_last_partial_run)
}

// bool store_errors_in_response_body = 9;
inline void RunGraphRequest::clear_store_errors_in_response_body() {
  store_errors_in_response_body_ = false;
}
inline bool RunGraphRequest::store_errors_in_response_body() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.store_errors_in_response_body)
  return store_errors_in_response_body_;
}
inline void RunGraphRequest::set_store_errors_in_response_body(bool value) {
  
  store_errors_in_response_body_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.store_errors_in_response_body)
}

// int64 request_id = 11;
inline void RunGraphRequest::clear_request_id() {
  request_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 RunGraphRequest::request_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.request_id)
  return request_id_;
}
inline void RunGraphRequest::set_request_id(::google::protobuf::int64 value) {
  
  request_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.request_id)
}

// -------------------------------------------------------------------

// RunGraphResponse

// repeated .tensorflow.NamedTensorProto recv = 1;
inline int RunGraphResponse::recv_size() const {
  return recv_.size();
}
inline ::tensorflow::NamedTensorProto* RunGraphResponse::mutable_recv(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphResponse.recv)
  return recv_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::NamedTensorProto >*
RunGraphResponse::mutable_recv() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunGraphResponse.recv)
  return &recv_;
}
inline const ::tensorflow::NamedTensorProto& RunGraphResponse::recv(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphResponse.recv)
  return recv_.Get(index);
}
inline ::tensorflow::NamedTensorProto* RunGraphResponse::add_recv() {
  // @@protoc_insertion_point(field_add:tensorflow.RunGraphResponse.recv)
  return recv_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::NamedTensorProto >&
RunGraphResponse::recv() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunGraphResponse.recv)
  return recv_;
}

// .tensorflow.StepStats step_stats = 2;
inline bool RunGraphResponse::has_step_stats() const {
  return this != internal_default_instance() && step_stats_ != NULL;
}
inline const ::tensorflow::StepStats& RunGraphResponse::_internal_step_stats() const {
  return *step_stats_;
}
inline const ::tensorflow::StepStats& RunGraphResponse::step_stats() const {
  const ::tensorflow::StepStats* p = step_stats_;
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphResponse.step_stats)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::StepStats*>(
      &::tensorflow::_StepStats_default_instance_);
}
inline ::tensorflow::StepStats* RunGraphResponse::release_step_stats() {
  // @@protoc_insertion_point(field_release:tensorflow.RunGraphResponse.step_stats)
  
  ::tensorflow::StepStats* temp = step_stats_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  step_stats_ = NULL;
  return temp;
}
inline ::tensorflow::StepStats* RunGraphResponse::unsafe_arena_release_step_stats() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunGraphResponse.step_stats)
  
  ::tensorflow::StepStats* temp = step_stats_;
  step_stats_ = NULL;
  return temp;
}
inline ::tensorflow::StepStats* RunGraphResponse::mutable_step_stats() {
  
  if (step_stats_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::StepStats>(GetArenaNoVirtual());
    step_stats_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphResponse.step_stats)
  return step_stats_;
}
inline void RunGraphResponse::set_allocated_step_stats(::tensorflow::StepStats* step_stats) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(step_stats_);
  }
  if (step_stats) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(step_stats)->GetArena();
    if (message_arena != submessage_arena) {
      step_stats = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, step_stats, submessage_arena);
    }
    
  } else {
    
  }
  step_stats_ = step_stats;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunGraphResponse.step_stats)
}

// .tensorflow.CostGraphDef cost_graph = 3;
inline bool RunGraphResponse::has_cost_graph() const {
  return this != internal_default_instance() && cost_graph_ != NULL;
}
inline const ::tensorflow::CostGraphDef& RunGraphResponse::_internal_cost_graph() const {
  return *cost_graph_;
}
inline const ::tensorflow::CostGraphDef& RunGraphResponse::cost_graph() const {
  const ::tensorflow::CostGraphDef* p = cost_graph_;
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphResponse.cost_graph)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::CostGraphDef*>(
      &::tensorflow::_CostGraphDef_default_instance_);
}
inline ::tensorflow::CostGraphDef* RunGraphResponse::release_cost_graph() {
  // @@protoc_insertion_point(field_release:tensorflow.RunGraphResponse.cost_graph)
  
  ::tensorflow::CostGraphDef* temp = cost_graph_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  cost_graph_ = NULL;
  return temp;
}
inline ::tensorflow::CostGraphDef* RunGraphResponse::unsafe_arena_release_cost_graph() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunGraphResponse.cost_graph)
  
  ::tensorflow::CostGraphDef* temp = cost_graph_;
  cost_graph_ = NULL;
  return temp;
}
inline ::tensorflow::CostGraphDef* RunGraphResponse::mutable_cost_graph() {
  
  if (cost_graph_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::CostGraphDef>(GetArenaNoVirtual());
    cost_graph_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphResponse.cost_graph)
  return cost_graph_;
}
inline void RunGraphResponse::set_allocated_cost_graph(::tensorflow::CostGraphDef* cost_graph) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(cost_graph_);
  }
  if (cost_graph) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(cost_graph)->GetArena();
    if (message_arena != submessage_arena) {
      cost_graph = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, cost_graph, submessage_arena);
    }
    
  } else {
    
  }
  cost_graph_ = cost_graph;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunGraphResponse.cost_graph)
}

// repeated .tensorflow.GraphDef partition_graph = 4;
inline int RunGraphResponse::partition_graph_size() const {
  return partition_graph_.size();
}
inline ::tensorflow::GraphDef* RunGraphResponse::mutable_partition_graph(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphResponse.partition_graph)
  return partition_graph_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::GraphDef >*
RunGraphResponse::mutable_partition_graph() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunGraphResponse.partition_graph)
  return &partition_graph_;
}
inline const ::tensorflow::GraphDef& RunGraphResponse::partition_graph(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphResponse.partition_graph)
  return partition_graph_.Get(index);
}
inline ::tensorflow::GraphDef* RunGraphResponse::add_partition_graph() {
  // @@protoc_insertion_point(field_add:tensorflow.RunGraphResponse.partition_graph)
  return partition_graph_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::GraphDef >&
RunGraphResponse::partition_graph() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunGraphResponse.partition_graph)
  return partition_graph_;
}

// .tensorflow.error.Code status_code = 5;
inline void RunGraphResponse::clear_status_code() {
  status_code_ = 0;
}
inline ::tensorflow::error::Code RunGraphResponse::status_code() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphResponse.status_code)
  return static_cast< ::tensorflow::error::Code >(status_code_);
}
inline void RunGraphResponse::set_status_code(::tensorflow::error::Code value) {
  
  status_code_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphResponse.status_code)
}

// string status_error_message = 6;
inline void RunGraphResponse::clear_status_error_message() {
  status_error_message_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& RunGraphResponse::status_error_message() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphResponse.status_error_message)
  return status_error_message_.Get();
}
inline void RunGraphResponse::set_status_error_message(const ::std::string& value) {
  
  status_error_message_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphResponse.status_error_message)
}
#if LANG_CXX11
inline void RunGraphResponse::set_status_error_message(::std::string&& value) {
  
  status_error_message_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RunGraphResponse.status_error_message)
}
#endif
inline void RunGraphResponse::set_status_error_message(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  status_error_message_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RunGraphResponse.status_error_message)
}
inline void RunGraphResponse::set_status_error_message(const char* value,
    size_t size) {
  
  status_error_message_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RunGraphResponse.status_error_message)
}
inline ::std::string* RunGraphResponse::mutable_status_error_message() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphResponse.status_error_message)
  return status_error_message_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* RunGraphResponse::release_status_error_message() {
  // @@protoc_insertion_point(field_release:tensorflow.RunGraphResponse.status_error_message)
  
  return status_error_message_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RunGraphResponse::set_allocated_status_error_message(::std::string* status_error_message) {
  if (status_error_message != NULL) {
    
  } else {
    
  }
  status_error_message_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), status_error_message,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunGraphResponse.status_error_message)
}
inline ::std::string* RunGraphResponse::unsafe_arena_release_status_error_message() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunGraphResponse.status_error_message)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return status_error_message_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RunGraphResponse::unsafe_arena_set_allocated_status_error_message(
    ::std::string* status_error_message) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (status_error_message != NULL) {
    
  } else {
    
  }
  status_error_message_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      status_error_message, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunGraphResponse.status_error_message)
}

// -------------------------------------------------------------------

// CleanupGraphRequest

// int64 step_id = 1;
inline void CleanupGraphRequest::clear_step_id() {
  step_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CleanupGraphRequest::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.CleanupGraphRequest.step_id)
  return step_id_;
}
inline void CleanupGraphRequest::set_step_id(::google::protobuf::int64 value) {
  
  step_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CleanupGraphRequest.step_id)
}

// -------------------------------------------------------------------

// CleanupGraphResponse

// -------------------------------------------------------------------

// RecvTensorRequest

// int64 step_id = 1;
inline void RecvTensorRequest::clear_step_id() {
  step_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 RecvTensorRequest::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorRequest.step_id)
  return step_id_;
}
inline void RecvTensorRequest::set_step_id(::google::protobuf::int64 value) {
  
  step_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvTensorRequest.step_id)
}

// string rendezvous_key = 2;
inline void RecvTensorRequest::clear_rendezvous_key() {
  rendezvous_key_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& RecvTensorRequest::rendezvous_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorRequest.rendezvous_key)
  return rendezvous_key_.Get();
}
inline void RecvTensorRequest::set_rendezvous_key(const ::std::string& value) {
  
  rendezvous_key_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RecvTensorRequest.rendezvous_key)
}
#if LANG_CXX11
inline void RecvTensorRequest::set_rendezvous_key(::std::string&& value) {
  
  rendezvous_key_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RecvTensorRequest.rendezvous_key)
}
#endif
inline void RecvTensorRequest::set_rendezvous_key(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  rendezvous_key_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RecvTensorRequest.rendezvous_key)
}
inline void RecvTensorRequest::set_rendezvous_key(const char* value,
    size_t size) {
  
  rendezvous_key_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RecvTensorRequest.rendezvous_key)
}
inline ::std::string* RecvTensorRequest::mutable_rendezvous_key() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvTensorRequest.rendezvous_key)
  return rendezvous_key_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* RecvTensorRequest::release_rendezvous_key() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvTensorRequest.rendezvous_key)
  
  return rendezvous_key_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RecvTensorRequest::set_allocated_rendezvous_key(::std::string* rendezvous_key) {
  if (rendezvous_key != NULL) {
    
  } else {
    
  }
  rendezvous_key_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), rendezvous_key,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvTensorRequest.rendezvous_key)
}
inline ::std::string* RecvTensorRequest::unsafe_arena_release_rendezvous_key() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RecvTensorRequest.rendezvous_key)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return rendezvous_key_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RecvTensorRequest::unsafe_arena_set_allocated_rendezvous_key(
    ::std::string* rendezvous_key) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (rendezvous_key != NULL) {
    
  } else {
    
  }
  rendezvous_key_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      rendezvous_key, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RecvTensorRequest.rendezvous_key)
}

// bool dma_ok = 3;
inline void RecvTensorRequest::clear_dma_ok() {
  dma_ok_ = false;
}
inline bool RecvTensorRequest::dma_ok() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorRequest.dma_ok)
  return dma_ok_;
}
inline void RecvTensorRequest::set_dma_ok(bool value) {
  
  dma_ok_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvTensorRequest.dma_ok)
}

// .tensorflow.DeviceLocality client_locality = 4;
inline bool RecvTensorRequest::has_client_locality() const {
  return this != internal_default_instance() && client_locality_ != NULL;
}
inline const ::tensorflow::DeviceLocality& RecvTensorRequest::_internal_client_locality() const {
  return *client_locality_;
}
inline const ::tensorflow::DeviceLocality& RecvTensorRequest::client_locality() const {
  const ::tensorflow::DeviceLocality* p = client_locality_;
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorRequest.client_locality)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::DeviceLocality*>(
      &::tensorflow::_DeviceLocality_default_instance_);
}
inline ::tensorflow::DeviceLocality* RecvTensorRequest::release_client_locality() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvTensorRequest.client_locality)
  
  ::tensorflow::DeviceLocality* temp = client_locality_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  client_locality_ = NULL;
  return temp;
}
inline ::tensorflow::DeviceLocality* RecvTensorRequest::unsafe_arena_release_client_locality() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RecvTensorRequest.client_locality)
  
  ::tensorflow::DeviceLocality* temp = client_locality_;
  client_locality_ = NULL;
  return temp;
}
inline ::tensorflow::DeviceLocality* RecvTensorRequest::mutable_client_locality() {
  
  if (client_locality_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::DeviceLocality>(GetArenaNoVirtual());
    client_locality_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvTensorRequest.client_locality)
  return client_locality_;
}
inline void RecvTensorRequest::set_allocated_client_locality(::tensorflow::DeviceLocality* client_locality) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(client_locality_);
  }
  if (client_locality) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(client_locality)->GetArena();
    if (message_arena != submessage_arena) {
      client_locality = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, client_locality, submessage_arena);
    }
    
  } else {
    
  }
  client_locality_ = client_locality;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvTensorRequest.client_locality)
}

// .tensorflow.DeviceLocality server_locality = 5;
inline bool RecvTensorRequest::has_server_locality() const {
  return this != internal_default_instance() && server_locality_ != NULL;
}
inline const ::tensorflow::DeviceLocality& RecvTensorRequest::_internal_server_locality() const {
  return *server_locality_;
}
inline const ::tensorflow::DeviceLocality& RecvTensorRequest::server_locality() const {
  const ::tensorflow::DeviceLocality* p = server_locality_;
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorRequest.server_locality)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::DeviceLocality*>(
      &::tensorflow::_DeviceLocality_default_instance_);
}
inline ::tensorflow::DeviceLocality* RecvTensorRequest::release_server_locality() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvTensorRequest.server_locality)
  
  ::tensorflow::DeviceLocality* temp = server_locality_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  server_locality_ = NULL;
  return temp;
}
inline ::tensorflow::DeviceLocality* RecvTensorRequest::unsafe_arena_release_server_locality() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RecvTensorRequest.server_locality)
  
  ::tensorflow::DeviceLocality* temp = server_locality_;
  server_locality_ = NULL;
  return temp;
}
inline ::tensorflow::DeviceLocality* RecvTensorRequest::mutable_server_locality() {
  
  if (server_locality_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::DeviceLocality>(GetArenaNoVirtual());
    server_locality_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvTensorRequest.server_locality)
  return server_locality_;
}
inline void RecvTensorRequest::set_allocated_server_locality(::tensorflow::DeviceLocality* server_locality) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(server_locality_);
  }
  if (server_locality) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(server_locality)->GetArena();
    if (message_arena != submessage_arena) {
      server_locality = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, server_locality, submessage_arena);
    }
    
  } else {
    
  }
  server_locality_ = server_locality;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvTensorRequest.server_locality)
}

// .google.protobuf.Any transport_options = 6;
inline bool RecvTensorRequest::has_transport_options() const {
  return this != internal_default_instance() && transport_options_ != NULL;
}
inline const ::google::protobuf::Any& RecvTensorRequest::_internal_transport_options() const {
  return *transport_options_;
}
inline const ::google::protobuf::Any& RecvTensorRequest::transport_options() const {
  const ::google::protobuf::Any* p = transport_options_;
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorRequest.transport_options)
  return p != NULL ? *p : *reinterpret_cast<const ::google::protobuf::Any*>(
      &::google::protobuf::_Any_default_instance_);
}
inline ::google::protobuf::Any* RecvTensorRequest::release_transport_options() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvTensorRequest.transport_options)
  
  ::google::protobuf::Any* temp = transport_options_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  transport_options_ = NULL;
  return temp;
}
inline ::google::protobuf::Any* RecvTensorRequest::unsafe_arena_release_transport_options() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RecvTensorRequest.transport_options)
  
  ::google::protobuf::Any* temp = transport_options_;
  transport_options_ = NULL;
  return temp;
}
inline ::google::protobuf::Any* RecvTensorRequest::mutable_transport_options() {
  
  if (transport_options_ == NULL) {
    auto* p = CreateMaybeMessage<::google::protobuf::Any>(GetArenaNoVirtual());
    transport_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvTensorRequest.transport_options)
  return transport_options_;
}
inline void RecvTensorRequest::set_allocated_transport_options(::google::protobuf::Any* transport_options) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(transport_options_);
  }
  if (transport_options) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      transport_options = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, transport_options, submessage_arena);
    }
    
  } else {
    
  }
  transport_options_ = transport_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvTensorRequest.transport_options)
}

// int64 request_id = 7;
inline void RecvTensorRequest::clear_request_id() {
  request_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 RecvTensorRequest::request_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorRequest.request_id)
  return request_id_;
}
inline void RecvTensorRequest::set_request_id(::google::protobuf::int64 value) {
  
  request_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvTensorRequest.request_id)
}

// -------------------------------------------------------------------

// RecvTensorResponse

// .tensorflow.TensorProto tensor = 1;
inline bool RecvTensorResponse::has_tensor() const {
  return this != internal_default_instance() && tensor_ != NULL;
}
inline const ::tensorflow::TensorProto& RecvTensorResponse::_internal_tensor() const {
  return *tensor_;
}
inline const ::tensorflow::TensorProto& RecvTensorResponse::tensor() const {
  const ::tensorflow::TensorProto* p = tensor_;
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorResponse.tensor)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::TensorProto*>(
      &::tensorflow::_TensorProto_default_instance_);
}
inline ::tensorflow::TensorProto* RecvTensorResponse::release_tensor() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvTensorResponse.tensor)
  
  ::tensorflow::TensorProto* temp = tensor_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  tensor_ = NULL;
  return temp;
}
inline ::tensorflow::TensorProto* RecvTensorResponse::unsafe_arena_release_tensor() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RecvTensorResponse.tensor)
  
  ::tensorflow::TensorProto* temp = tensor_;
  tensor_ = NULL;
  return temp;
}
inline ::tensorflow::TensorProto* RecvTensorResponse::mutable_tensor() {
  
  if (tensor_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorProto>(GetArenaNoVirtual());
    tensor_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvTensorResponse.tensor)
  return tensor_;
}
inline void RecvTensorResponse::set_allocated_tensor(::tensorflow::TensorProto* tensor) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(tensor_);
  }
  if (tensor) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(tensor)->GetArena();
    if (message_arena != submessage_arena) {
      tensor = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, tensor, submessage_arena);
    }
    
  } else {
    
  }
  tensor_ = tensor;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvTensorResponse.tensor)
}

// bool is_dead = 2;
inline void RecvTensorResponse::clear_is_dead() {
  is_dead_ = false;
}
inline bool RecvTensorResponse::is_dead() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorResponse.is_dead)
  return is_dead_;
}
inline void RecvTensorResponse::set_is_dead(bool value) {
  
  is_dead_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvTensorResponse.is_dead)
}

// int64 send_start_micros = 3;
inline void RecvTensorResponse::clear_send_start_micros() {
  send_start_micros_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 RecvTensorResponse::send_start_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorResponse.send_start_micros)
  return send_start_micros_;
}
inline void RecvTensorResponse::set_send_start_micros(::google::protobuf::int64 value) {
  
  send_start_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvTensorResponse.send_start_micros)
}

// .google.protobuf.Any transport_options = 4;
inline bool RecvTensorResponse::has_transport_options() const {
  return this != internal_default_instance() && transport_options_ != NULL;
}
inline const ::google::protobuf::Any& RecvTensorResponse::_internal_transport_options() const {
  return *transport_options_;
}
inline const ::google::protobuf::Any& RecvTensorResponse::transport_options() const {
  const ::google::protobuf::Any* p = transport_options_;
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorResponse.transport_options)
  return p != NULL ? *p : *reinterpret_cast<const ::google::protobuf::Any*>(
      &::google::protobuf::_Any_default_instance_);
}
inline ::google::protobuf::Any* RecvTensorResponse::release_transport_options() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvTensorResponse.transport_options)
  
  ::google::protobuf::Any* temp = transport_options_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  transport_options_ = NULL;
  return temp;
}
inline ::google::protobuf::Any* RecvTensorResponse::unsafe_arena_release_transport_options() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RecvTensorResponse.transport_options)
  
  ::google::protobuf::Any* temp = transport_options_;
  transport_options_ = NULL;
  return temp;
}
inline ::google::protobuf::Any* RecvTensorResponse::mutable_transport_options() {
  
  if (transport_options_ == NULL) {
    auto* p = CreateMaybeMessage<::google::protobuf::Any>(GetArenaNoVirtual());
    transport_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvTensorResponse.transport_options)
  return transport_options_;
}
inline void RecvTensorResponse::set_allocated_transport_options(::google::protobuf::Any* transport_options) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(transport_options_);
  }
  if (transport_options) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      transport_options = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, transport_options, submessage_arena);
    }
    
  } else {
    
  }
  transport_options_ = transport_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvTensorResponse.transport_options)
}

// bool require_ack = 5;
inline void RecvTensorResponse::clear_require_ack() {
  require_ack_ = false;
}
inline bool RecvTensorResponse::require_ack() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorResponse.require_ack)
  return require_ack_;
}
inline void RecvTensorResponse::set_require_ack(bool value) {
  
  require_ack_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvTensorResponse.require_ack)
}

// -------------------------------------------------------------------

// MarkRecvFinishedRequest

// int64 request_id = 1;
inline void MarkRecvFinishedRequest::clear_request_id() {
  request_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MarkRecvFinishedRequest::request_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.MarkRecvFinishedRequest.request_id)
  return request_id_;
}
inline void MarkRecvFinishedRequest::set_request_id(::google::protobuf::int64 value) {
  
  request_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MarkRecvFinishedRequest.request_id)
}

// -------------------------------------------------------------------

// MarkRecvFinishedResponse

// -------------------------------------------------------------------

// LoggingRequest

// bool enable_rpc_logging = 1;
inline void LoggingRequest::clear_enable_rpc_logging() {
  enable_rpc_logging_ = false;
}
inline bool LoggingRequest::enable_rpc_logging() const {
  // @@protoc_insertion_point(field_get:tensorflow.LoggingRequest.enable_rpc_logging)
  return enable_rpc_logging_;
}
inline void LoggingRequest::set_enable_rpc_logging(bool value) {
  
  enable_rpc_logging_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.LoggingRequest.enable_rpc_logging)
}

// bool disable_rpc_logging = 4;
inline void LoggingRequest::clear_disable_rpc_logging() {
  disable_rpc_logging_ = false;
}
inline bool LoggingRequest::disable_rpc_logging() const {
  // @@protoc_insertion_point(field_get:tensorflow.LoggingRequest.disable_rpc_logging)
  return disable_rpc_logging_;
}
inline void LoggingRequest::set_disable_rpc_logging(bool value) {
  
  disable_rpc_logging_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.LoggingRequest.disable_rpc_logging)
}

// bool clear = 2;
inline void LoggingRequest::clear_clear() {
  clear_ = false;
}
inline bool LoggingRequest::clear() const {
  // @@protoc_insertion_point(field_get:tensorflow.LoggingRequest.clear)
  return clear_;
}
inline void LoggingRequest::set_clear(bool value) {
  
  clear_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.LoggingRequest.clear)
}

// repeated int64 fetch_step_id = 3;
inline int LoggingRequest::fetch_step_id_size() const {
  return fetch_step_id_.size();
}
inline void LoggingRequest::clear_fetch_step_id() {
  fetch_step_id_.Clear();
}
inline ::google::protobuf::int64 LoggingRequest::fetch_step_id(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.LoggingRequest.fetch_step_id)
  return fetch_step_id_.Get(index);
}
inline void LoggingRequest::set_fetch_step_id(int index, ::google::protobuf::int64 value) {
  fetch_step_id_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.LoggingRequest.fetch_step_id)
}
inline void LoggingRequest::add_fetch_step_id(::google::protobuf::int64 value) {
  fetch_step_id_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.LoggingRequest.fetch_step_id)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
LoggingRequest::fetch_step_id() const {
  // @@protoc_insertion_point(field_list:tensorflow.LoggingRequest.fetch_step_id)
  return fetch_step_id_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
LoggingRequest::mutable_fetch_step_id() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.LoggingRequest.fetch_step_id)
  return &fetch_step_id_;
}

// -------------------------------------------------------------------

// LabeledStepStats

// int64 step_id = 1;
inline void LabeledStepStats::clear_step_id() {
  step_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 LabeledStepStats::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.LabeledStepStats.step_id)
  return step_id_;
}
inline void LabeledStepStats::set_step_id(::google::protobuf::int64 value) {
  
  step_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.LabeledStepStats.step_id)
}

// .tensorflow.StepStats step_stats = 2;
inline bool LabeledStepStats::has_step_stats() const {
  return this != internal_default_instance() && step_stats_ != NULL;
}
inline const ::tensorflow::StepStats& LabeledStepStats::_internal_step_stats() const {
  return *step_stats_;
}
inline const ::tensorflow::StepStats& LabeledStepStats::step_stats() const {
  const ::tensorflow::StepStats* p = step_stats_;
  // @@protoc_insertion_point(field_get:tensorflow.LabeledStepStats.step_stats)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::StepStats*>(
      &::tensorflow::_StepStats_default_instance_);
}
inline ::tensorflow::StepStats* LabeledStepStats::release_step_stats() {
  // @@protoc_insertion_point(field_release:tensorflow.LabeledStepStats.step_stats)
  
  ::tensorflow::StepStats* temp = step_stats_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  step_stats_ = NULL;
  return temp;
}
inline ::tensorflow::StepStats* LabeledStepStats::unsafe_arena_release_step_stats() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.LabeledStepStats.step_stats)
  
  ::tensorflow::StepStats* temp = step_stats_;
  step_stats_ = NULL;
  return temp;
}
inline ::tensorflow::StepStats* LabeledStepStats::mutable_step_stats() {
  
  if (step_stats_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::StepStats>(GetArenaNoVirtual());
    step_stats_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.LabeledStepStats.step_stats)
  return step_stats_;
}
inline void LabeledStepStats::set_allocated_step_stats(::tensorflow::StepStats* step_stats) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(step_stats_);
  }
  if (step_stats) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(step_stats)->GetArena();
    if (message_arena != submessage_arena) {
      step_stats = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, step_stats, submessage_arena);
    }
    
  } else {
    
  }
  step_stats_ = step_stats;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.LabeledStepStats.step_stats)
}

// -------------------------------------------------------------------

// LoggingResponse

// repeated .tensorflow.LabeledStepStats step = 1;
inline int LoggingResponse::step_size() const {
  return step_.size();
}
inline void LoggingResponse::clear_step() {
  step_.Clear();
}
inline ::tensorflow::LabeledStepStats* LoggingResponse::mutable_step(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.LoggingResponse.step)
  return step_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::LabeledStepStats >*
LoggingResponse::mutable_step() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.LoggingResponse.step)
  return &step_;
}
inline const ::tensorflow::LabeledStepStats& LoggingResponse::step(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.LoggingResponse.step)
  return step_.Get(index);
}
inline ::tensorflow::LabeledStepStats* LoggingResponse::add_step() {
  // @@protoc_insertion_point(field_add:tensorflow.LoggingResponse.step)
  return step_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::LabeledStepStats >&
LoggingResponse::step() const {
  // @@protoc_insertion_point(field_list:tensorflow.LoggingResponse.step)
  return step_;
}

// -------------------------------------------------------------------

// TraceOpts

// double duration = 1;
inline void TraceOpts::clear_duration() {
  duration_ = 0;
}
inline double TraceOpts::duration() const {
  // @@protoc_insertion_point(field_get:tensorflow.TraceOpts.duration)
  return duration_;
}
inline void TraceOpts::set_duration(double value) {
  
  duration_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TraceOpts.duration)
}

// bool use_step_profiler = 2;
inline void TraceOpts::clear_use_step_profiler() {
  use_step_profiler_ = false;
}
inline bool TraceOpts::use_step_profiler() const {
  // @@protoc_insertion_point(field_get:tensorflow.TraceOpts.use_step_profiler)
  return use_step_profiler_;
}
inline void TraceOpts::set_use_step_profiler(bool value) {
  
  use_step_profiler_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TraceOpts.use_step_profiler)
}

// bool use_kernel_profiler = 3;
inline void TraceOpts::clear_use_kernel_profiler() {
  use_kernel_profiler_ = false;
}
inline bool TraceOpts::use_kernel_profiler() const {
  // @@protoc_insertion_point(field_get:tensorflow.TraceOpts.use_kernel_profiler)
  return use_kernel_profiler_;
}
inline void TraceOpts::set_use_kernel_profiler(bool value) {
  
  use_kernel_profiler_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TraceOpts.use_kernel_profiler)
}

// bool use_extended_profiler = 4;
inline void TraceOpts::clear_use_extended_profiler() {
  use_extended_profiler_ = false;
}
inline bool TraceOpts::use_extended_profiler() const {
  // @@protoc_insertion_point(field_get:tensorflow.TraceOpts.use_extended_profiler)
  return use_extended_profiler_;
}
inline void TraceOpts::set_use_extended_profiler(bool value) {
  
  use_extended_profiler_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TraceOpts.use_extended_profiler)
}

// bool use_gpu_profiler = 5;
inline void TraceOpts::clear_use_gpu_profiler() {
  use_gpu_profiler_ = false;
}
inline bool TraceOpts::use_gpu_profiler() const {
  // @@protoc_insertion_point(field_get:tensorflow.TraceOpts.use_gpu_profiler)
  return use_gpu_profiler_;
}
inline void TraceOpts::set_use_gpu_profiler(bool value) {
  
  use_gpu_profiler_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TraceOpts.use_gpu_profiler)
}

// bool use_sample_profiler = 6;
inline void TraceOpts::clear_use_sample_profiler() {
  use_sample_profiler_ = false;
}
inline bool TraceOpts::use_sample_profiler() const {
  // @@protoc_insertion_point(field_get:tensorflow.TraceOpts.use_sample_profiler)
  return use_sample_profiler_;
}
inline void TraceOpts::set_use_sample_profiler(bool value) {
  
  use_sample_profiler_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TraceOpts.use_sample_profiler)
}

// -------------------------------------------------------------------

// TracingRequest

// .tensorflow.TraceOpts options = 1;
inline bool TracingRequest::has_options() const {
  return this != internal_default_instance() && options_ != NULL;
}
inline void TracingRequest::clear_options() {
  if (GetArenaNoVirtual() == NULL && options_ != NULL) {
    delete options_;
  }
  options_ = NULL;
}
inline const ::tensorflow::TraceOpts& TracingRequest::_internal_options() const {
  return *options_;
}
inline const ::tensorflow::TraceOpts& TracingRequest::options() const {
  const ::tensorflow::TraceOpts* p = options_;
  // @@protoc_insertion_point(field_get:tensorflow.TracingRequest.options)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::TraceOpts*>(
      &::tensorflow::_TraceOpts_default_instance_);
}
inline ::tensorflow::TraceOpts* TracingRequest::release_options() {
  // @@protoc_insertion_point(field_release:tensorflow.TracingRequest.options)
  
  ::tensorflow::TraceOpts* temp = options_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  options_ = NULL;
  return temp;
}
inline ::tensorflow::TraceOpts* TracingRequest::unsafe_arena_release_options() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TracingRequest.options)
  
  ::tensorflow::TraceOpts* temp = options_;
  options_ = NULL;
  return temp;
}
inline ::tensorflow::TraceOpts* TracingRequest::mutable_options() {
  
  if (options_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::TraceOpts>(GetArenaNoVirtual());
    options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.TracingRequest.options)
  return options_;
}
inline void TracingRequest::set_allocated_options(::tensorflow::TraceOpts* options) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete options_;
  }
  if (options) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(options);
    if (message_arena != submessage_arena) {
      options = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, options, submessage_arena);
    }
    
  } else {
    
  }
  options_ = options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TracingRequest.options)
}

// -------------------------------------------------------------------

// TracingResponse

// -------------------------------------------------------------------

// RecvBufRequest

// int64 step_id = 1;
inline void RecvBufRequest::clear_step_id() {
  step_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 RecvBufRequest::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.step_id)
  return step_id_;
}
inline void RecvBufRequest::set_step_id(::google::protobuf::int64 value) {
  
  step_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufRequest.step_id)
}

// string buf_rendezvous_key = 2;
inline void RecvBufRequest::clear_buf_rendezvous_key() {
  buf_rendezvous_key_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& RecvBufRequest::buf_rendezvous_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.buf_rendezvous_key)
  return buf_rendezvous_key_.Get();
}
inline void RecvBufRequest::set_buf_rendezvous_key(const ::std::string& value) {
  
  buf_rendezvous_key_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufRequest.buf_rendezvous_key)
}
#if LANG_CXX11
inline void RecvBufRequest::set_buf_rendezvous_key(::std::string&& value) {
  
  buf_rendezvous_key_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RecvBufRequest.buf_rendezvous_key)
}
#endif
inline void RecvBufRequest::set_buf_rendezvous_key(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  buf_rendezvous_key_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RecvBufRequest.buf_rendezvous_key)
}
inline void RecvBufRequest::set_buf_rendezvous_key(const char* value,
    size_t size) {
  
  buf_rendezvous_key_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RecvBufRequest.buf_rendezvous_key)
}
inline ::std::string* RecvBufRequest::mutable_buf_rendezvous_key() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvBufRequest.buf_rendezvous_key)
  return buf_rendezvous_key_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* RecvBufRequest::release_buf_rendezvous_key() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvBufRequest.buf_rendezvous_key)
  
  return buf_rendezvous_key_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RecvBufRequest::set_allocated_buf_rendezvous_key(::std::string* buf_rendezvous_key) {
  if (buf_rendezvous_key != NULL) {
    
  } else {
    
  }
  buf_rendezvous_key_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), buf_rendezvous_key,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvBufRequest.buf_rendezvous_key)
}
inline ::std::string* RecvBufRequest::unsafe_arena_release_buf_rendezvous_key() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RecvBufRequest.buf_rendezvous_key)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return buf_rendezvous_key_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RecvBufRequest::unsafe_arena_set_allocated_buf_rendezvous_key(
    ::std::string* buf_rendezvous_key) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (buf_rendezvous_key != NULL) {
    
  } else {
    
  }
  buf_rendezvous_key_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      buf_rendezvous_key, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RecvBufRequest.buf_rendezvous_key)
}

// int64 num_bytes = 3;
inline void RecvBufRequest::clear_num_bytes() {
  num_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 RecvBufRequest::num_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.num_bytes)
  return num_bytes_;
}
inline void RecvBufRequest::set_num_bytes(::google::protobuf::int64 value) {
  
  num_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufRequest.num_bytes)
}

// fixed64 buf_ptr = 4;
inline void RecvBufRequest::clear_buf_ptr() {
  buf_ptr_ = GOOGLE_ULONGLONG(0);
}
inline ::google::protobuf::uint64 RecvBufRequest::buf_ptr() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.buf_ptr)
  return buf_ptr_;
}
inline void RecvBufRequest::set_buf_ptr(::google::protobuf::uint64 value) {
  
  buf_ptr_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufRequest.buf_ptr)
}

// .tensorflow.DeviceLocality client_locality = 5;
inline bool RecvBufRequest::has_client_locality() const {
  return this != internal_default_instance() && client_locality_ != NULL;
}
inline const ::tensorflow::DeviceLocality& RecvBufRequest::_internal_client_locality() const {
  return *client_locality_;
}
inline const ::tensorflow::DeviceLocality& RecvBufRequest::client_locality() const {
  const ::tensorflow::DeviceLocality* p = client_locality_;
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.client_locality)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::DeviceLocality*>(
      &::tensorflow::_DeviceLocality_default_instance_);
}
inline ::tensorflow::DeviceLocality* RecvBufRequest::release_client_locality() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvBufRequest.client_locality)
  
  ::tensorflow::DeviceLocality* temp = client_locality_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  client_locality_ = NULL;
  return temp;
}
inline ::tensorflow::DeviceLocality* RecvBufRequest::unsafe_arena_release_client_locality() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RecvBufRequest.client_locality)
  
  ::tensorflow::DeviceLocality* temp = client_locality_;
  client_locality_ = NULL;
  return temp;
}
inline ::tensorflow::DeviceLocality* RecvBufRequest::mutable_client_locality() {
  
  if (client_locality_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::DeviceLocality>(GetArenaNoVirtual());
    client_locality_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvBufRequest.client_locality)
  return client_locality_;
}
inline void RecvBufRequest::set_allocated_client_locality(::tensorflow::DeviceLocality* client_locality) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(client_locality_);
  }
  if (client_locality) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(client_locality)->GetArena();
    if (message_arena != submessage_arena) {
      client_locality = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, client_locality, submessage_arena);
    }
    
  } else {
    
  }
  client_locality_ = client_locality;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvBufRequest.client_locality)
}

// .tensorflow.DeviceLocality server_locality = 6;
inline bool RecvBufRequest::has_server_locality() const {
  return this != internal_default_instance() && server_locality_ != NULL;
}
inline const ::tensorflow::DeviceLocality& RecvBufRequest::_internal_server_locality() const {
  return *server_locality_;
}
inline const ::tensorflow::DeviceLocality& RecvBufRequest::server_locality() const {
  const ::tensorflow::DeviceLocality* p = server_locality_;
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.server_locality)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::DeviceLocality*>(
      &::tensorflow::_DeviceLocality_default_instance_);
}
inline ::tensorflow::DeviceLocality* RecvBufRequest::release_server_locality() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvBufRequest.server_locality)
  
  ::tensorflow::DeviceLocality* temp = server_locality_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  server_locality_ = NULL;
  return temp;
}
inline ::tensorflow::DeviceLocality* RecvBufRequest::unsafe_arena_release_server_locality() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RecvBufRequest.server_locality)
  
  ::tensorflow::DeviceLocality* temp = server_locality_;
  server_locality_ = NULL;
  return temp;
}
inline ::tensorflow::DeviceLocality* RecvBufRequest::mutable_server_locality() {
  
  if (server_locality_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::DeviceLocality>(GetArenaNoVirtual());
    server_locality_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvBufRequest.server_locality)
  return server_locality_;
}
inline void RecvBufRequest::set_allocated_server_locality(::tensorflow::DeviceLocality* server_locality) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(server_locality_);
  }
  if (server_locality) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(server_locality)->GetArena();
    if (message_arena != submessage_arena) {
      server_locality = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, server_locality, submessage_arena);
    }
    
  } else {
    
  }
  server_locality_ = server_locality;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvBufRequest.server_locality)
}

// .google.protobuf.Any transport_options = 7;
inline bool RecvBufRequest::has_transport_options() const {
  return this != internal_default_instance() && transport_options_ != NULL;
}
inline const ::google::protobuf::Any& RecvBufRequest::_internal_transport_options() const {
  return *transport_options_;
}
inline const ::google::protobuf::Any& RecvBufRequest::transport_options() const {
  const ::google::protobuf::Any* p = transport_options_;
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.transport_options)
  return p != NULL ? *p : *reinterpret_cast<const ::google::protobuf::Any*>(
      &::google::protobuf::_Any_default_instance_);
}
inline ::google::protobuf::Any* RecvBufRequest::release_transport_options() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvBufRequest.transport_options)
  
  ::google::protobuf::Any* temp = transport_options_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  transport_options_ = NULL;
  return temp;
}
inline ::google::protobuf::Any* RecvBufRequest::unsafe_arena_release_transport_options() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RecvBufRequest.transport_options)
  
  ::google::protobuf::Any* temp = transport_options_;
  transport_options_ = NULL;
  return temp;
}
inline ::google::protobuf::Any* RecvBufRequest::mutable_transport_options() {
  
  if (transport_options_ == NULL) {
    auto* p = CreateMaybeMessage<::google::protobuf::Any>(GetArenaNoVirtual());
    transport_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvBufRequest.transport_options)
  return transport_options_;
}
inline void RecvBufRequest::set_allocated_transport_options(::google::protobuf::Any* transport_options) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(transport_options_);
  }
  if (transport_options) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      transport_options = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, transport_options, submessage_arena);
    }
    
  } else {
    
  }
  transport_options_ = transport_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvBufRequest.transport_options)
}

// string src_device = 8;
inline void RecvBufRequest::clear_src_device() {
  src_device_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& RecvBufRequest::src_device() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.src_device)
  return src_device_.Get();
}
inline void RecvBufRequest::set_src_device(const ::std::string& value) {
  
  src_device_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufRequest.src_device)
}
#if LANG_CXX11
inline void RecvBufRequest::set_src_device(::std::string&& value) {
  
  src_device_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RecvBufRequest.src_device)
}
#endif
inline void RecvBufRequest::set_src_device(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  src_device_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RecvBufRequest.src_device)
}
inline void RecvBufRequest::set_src_device(const char* value,
    size_t size) {
  
  src_device_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RecvBufRequest.src_device)
}
inline ::std::string* RecvBufRequest::mutable_src_device() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvBufRequest.src_device)
  return src_device_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* RecvBufRequest::release_src_device() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvBufRequest.src_device)
  
  return src_device_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RecvBufRequest::set_allocated_src_device(::std::string* src_device) {
  if (src_device != NULL) {
    
  } else {
    
  }
  src_device_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), src_device,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvBufRequest.src_device)
}
inline ::std::string* RecvBufRequest::unsafe_arena_release_src_device() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RecvBufRequest.src_device)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return src_device_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RecvBufRequest::unsafe_arena_set_allocated_src_device(
    ::std::string* src_device) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (src_device != NULL) {
    
  } else {
    
  }
  src_device_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      src_device, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RecvBufRequest.src_device)
}

// string dst_device = 9;
inline void RecvBufRequest::clear_dst_device() {
  dst_device_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& RecvBufRequest::dst_device() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.dst_device)
  return dst_device_.Get();
}
inline void RecvBufRequest::set_dst_device(const ::std::string& value) {
  
  dst_device_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufRequest.dst_device)
}
#if LANG_CXX11
inline void RecvBufRequest::set_dst_device(::std::string&& value) {
  
  dst_device_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RecvBufRequest.dst_device)
}
#endif
inline void RecvBufRequest::set_dst_device(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  dst_device_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RecvBufRequest.dst_device)
}
inline void RecvBufRequest::set_dst_device(const char* value,
    size_t size) {
  
  dst_device_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RecvBufRequest.dst_device)
}
inline ::std::string* RecvBufRequest::mutable_dst_device() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvBufRequest.dst_device)
  return dst_device_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* RecvBufRequest::release_dst_device() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvBufRequest.dst_device)
  
  return dst_device_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RecvBufRequest::set_allocated_dst_device(::std::string* dst_device) {
  if (dst_device != NULL) {
    
  } else {
    
  }
  dst_device_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), dst_device,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvBufRequest.dst_device)
}
inline ::std::string* RecvBufRequest::unsafe_arena_release_dst_device() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RecvBufRequest.dst_device)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return dst_device_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RecvBufRequest::unsafe_arena_set_allocated_dst_device(
    ::std::string* dst_device) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (dst_device != NULL) {
    
  } else {
    
  }
  dst_device_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      dst_device, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RecvBufRequest.dst_device)
}

// int64 request_id = 10;
inline void RecvBufRequest::clear_request_id() {
  request_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 RecvBufRequest::request_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.request_id)
  return request_id_;
}
inline void RecvBufRequest::set_request_id(::google::protobuf::int64 value) {
  
  request_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufRequest.request_id)
}

// -------------------------------------------------------------------

// RecvBufResponse

// fixed64 buf_ptr = 1;
inline void RecvBufResponse::clear_buf_ptr() {
  buf_ptr_ = GOOGLE_ULONGLONG(0);
}
inline ::google::protobuf::uint64 RecvBufResponse::buf_ptr() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufResponse.buf_ptr)
  return buf_ptr_;
}
inline void RecvBufResponse::set_buf_ptr(::google::protobuf::uint64 value) {
  
  buf_ptr_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufResponse.buf_ptr)
}

// int64 num_bytes = 2;
inline void RecvBufResponse::clear_num_bytes() {
  num_bytes_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 RecvBufResponse::num_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufResponse.num_bytes)
  return num_bytes_;
}
inline void RecvBufResponse::set_num_bytes(::google::protobuf::int64 value) {
  
  num_bytes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufResponse.num_bytes)
}

// bool is_dead = 3;
inline void RecvBufResponse::clear_is_dead() {
  is_dead_ = false;
}
inline bool RecvBufResponse::is_dead() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufResponse.is_dead)
  return is_dead_;
}
inline void RecvBufResponse::set_is_dead(bool value) {
  
  is_dead_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufResponse.is_dead)
}

// .google.protobuf.Any transport_options = 4;
inline bool RecvBufResponse::has_transport_options() const {
  return this != internal_default_instance() && transport_options_ != NULL;
}
inline const ::google::protobuf::Any& RecvBufResponse::_internal_transport_options() const {
  return *transport_options_;
}
inline const ::google::protobuf::Any& RecvBufResponse::transport_options() const {
  const ::google::protobuf::Any* p = transport_options_;
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufResponse.transport_options)
  return p != NULL ? *p : *reinterpret_cast<const ::google::protobuf::Any*>(
      &::google::protobuf::_Any_default_instance_);
}
inline ::google::protobuf::Any* RecvBufResponse::release_transport_options() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvBufResponse.transport_options)
  
  ::google::protobuf::Any* temp = transport_options_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  transport_options_ = NULL;
  return temp;
}
inline ::google::protobuf::Any* RecvBufResponse::unsafe_arena_release_transport_options() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RecvBufResponse.transport_options)
  
  ::google::protobuf::Any* temp = transport_options_;
  transport_options_ = NULL;
  return temp;
}
inline ::google::protobuf::Any* RecvBufResponse::mutable_transport_options() {
  
  if (transport_options_ == NULL) {
    auto* p = CreateMaybeMessage<::google::protobuf::Any>(GetArenaNoVirtual());
    transport_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvBufResponse.transport_options)
  return transport_options_;
}
inline void RecvBufResponse::set_allocated_transport_options(::google::protobuf::Any* transport_options) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(transport_options_);
  }
  if (transport_options) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      transport_options = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, transport_options, submessage_arena);
    }
    
  } else {
    
  }
  transport_options_ = transport_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvBufResponse.transport_options)
}

// int64 send_start_micros = 5;
inline void RecvBufResponse::clear_send_start_micros() {
  send_start_micros_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 RecvBufResponse::send_start_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufResponse.send_start_micros)
  return send_start_micros_;
}
inline void RecvBufResponse::set_send_start_micros(::google::protobuf::int64 value) {
  
  send_start_micros_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufResponse.send_start_micros)
}

// bool require_ack = 6;
inline void RecvBufResponse::clear_require_ack() {
  require_ack_ = false;
}
inline bool RecvBufResponse::require_ack() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufResponse.require_ack)
  return require_ack_;
}
inline void RecvBufResponse::set_require_ack(bool value) {
  
  require_ack_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufResponse.require_ack)
}

// -------------------------------------------------------------------

// CompleteGroupRequest

// int32 group_key = 1;
inline void CompleteGroupRequest::clear_group_key() {
  group_key_ = 0;
}
inline ::google::protobuf::int32 CompleteGroupRequest::group_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupRequest.group_key)
  return group_key_;
}
inline void CompleteGroupRequest::set_group_key(::google::protobuf::int32 value) {
  
  group_key_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupRequest.group_key)
}

// int32 group_size = 2;
inline void CompleteGroupRequest::clear_group_size() {
  group_size_ = 0;
}
inline ::google::protobuf::int32 CompleteGroupRequest::group_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupRequest.group_size)
  return group_size_;
}
inline void CompleteGroupRequest::set_group_size(::google::protobuf::int32 value) {
  
  group_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupRequest.group_size)
}

// string device_type = 3;
inline void CompleteGroupRequest::clear_device_type() {
  device_type_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& CompleteGroupRequest::device_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupRequest.device_type)
  return device_type_.Get();
}
inline void CompleteGroupRequest::set_device_type(const ::std::string& value) {
  
  device_type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupRequest.device_type)
}
#if LANG_CXX11
inline void CompleteGroupRequest::set_device_type(::std::string&& value) {
  
  device_type_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CompleteGroupRequest.device_type)
}
#endif
inline void CompleteGroupRequest::set_device_type(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  device_type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CompleteGroupRequest.device_type)
}
inline void CompleteGroupRequest::set_device_type(const char* value,
    size_t size) {
  
  device_type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CompleteGroupRequest.device_type)
}
inline ::std::string* CompleteGroupRequest::mutable_device_type() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CompleteGroupRequest.device_type)
  return device_type_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* CompleteGroupRequest::release_device_type() {
  // @@protoc_insertion_point(field_release:tensorflow.CompleteGroupRequest.device_type)
  
  return device_type_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CompleteGroupRequest::set_allocated_device_type(::std::string* device_type) {
  if (device_type != NULL) {
    
  } else {
    
  }
  device_type_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), device_type,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CompleteGroupRequest.device_type)
}
inline ::std::string* CompleteGroupRequest::unsafe_arena_release_device_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CompleteGroupRequest.device_type)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return device_type_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CompleteGroupRequest::unsafe_arena_set_allocated_device_type(
    ::std::string* device_type) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (device_type != NULL) {
    
  } else {
    
  }
  device_type_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      device_type, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CompleteGroupRequest.device_type)
}

// repeated string device_name = 4;
inline int CompleteGroupRequest::device_name_size() const {
  return device_name_.size();
}
inline void CompleteGroupRequest::clear_device_name() {
  device_name_.Clear();
}
inline const ::std::string& CompleteGroupRequest::device_name(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupRequest.device_name)
  return device_name_.Get(index);
}
inline ::std::string* CompleteGroupRequest::mutable_device_name(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CompleteGroupRequest.device_name)
  return device_name_.Mutable(index);
}
inline void CompleteGroupRequest::set_device_name(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupRequest.device_name)
  device_name_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void CompleteGroupRequest::set_device_name(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupRequest.device_name)
  device_name_.Mutable(index)->assign(std::move(value));
}
#endif
inline void CompleteGroupRequest::set_device_name(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  device_name_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.CompleteGroupRequest.device_name)
}
inline void CompleteGroupRequest::set_device_name(int index, const char* value, size_t size) {
  device_name_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CompleteGroupRequest.device_name)
}
inline ::std::string* CompleteGroupRequest::add_device_name() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.CompleteGroupRequest.device_name)
  return device_name_.Add();
}
inline void CompleteGroupRequest::add_device_name(const ::std::string& value) {
  device_name_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.CompleteGroupRequest.device_name)
}
#if LANG_CXX11
inline void CompleteGroupRequest::add_device_name(::std::string&& value) {
  device_name_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.CompleteGroupRequest.device_name)
}
#endif
inline void CompleteGroupRequest::add_device_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  device_name_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.CompleteGroupRequest.device_name)
}
inline void CompleteGroupRequest::add_device_name(const char* value, size_t size) {
  device_name_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.CompleteGroupRequest.device_name)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
CompleteGroupRequest::device_name() const {
  // @@protoc_insertion_point(field_list:tensorflow.CompleteGroupRequest.device_name)
  return device_name_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
CompleteGroupRequest::mutable_device_name() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CompleteGroupRequest.device_name)
  return &device_name_;
}

// -------------------------------------------------------------------

// CompleteGroupResponse

// int32 group_key = 1;
inline void CompleteGroupResponse::clear_group_key() {
  group_key_ = 0;
}
inline ::google::protobuf::int32 CompleteGroupResponse::group_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupResponse.group_key)
  return group_key_;
}
inline void CompleteGroupResponse::set_group_key(::google::protobuf::int32 value) {
  
  group_key_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupResponse.group_key)
}

// int32 group_size = 2;
inline void CompleteGroupResponse::clear_group_size() {
  group_size_ = 0;
}
inline ::google::protobuf::int32 CompleteGroupResponse::group_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupResponse.group_size)
  return group_size_;
}
inline void CompleteGroupResponse::set_group_size(::google::protobuf::int32 value) {
  
  group_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupResponse.group_size)
}

// string device_type = 3;
inline void CompleteGroupResponse::clear_device_type() {
  device_type_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& CompleteGroupResponse::device_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupResponse.device_type)
  return device_type_.Get();
}
inline void CompleteGroupResponse::set_device_type(const ::std::string& value) {
  
  device_type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupResponse.device_type)
}
#if LANG_CXX11
inline void CompleteGroupResponse::set_device_type(::std::string&& value) {
  
  device_type_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CompleteGroupResponse.device_type)
}
#endif
inline void CompleteGroupResponse::set_device_type(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  device_type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CompleteGroupResponse.device_type)
}
inline void CompleteGroupResponse::set_device_type(const char* value,
    size_t size) {
  
  device_type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CompleteGroupResponse.device_type)
}
inline ::std::string* CompleteGroupResponse::mutable_device_type() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CompleteGroupResponse.device_type)
  return device_type_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* CompleteGroupResponse::release_device_type() {
  // @@protoc_insertion_point(field_release:tensorflow.CompleteGroupResponse.device_type)
  
  return device_type_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CompleteGroupResponse::set_allocated_device_type(::std::string* device_type) {
  if (device_type != NULL) {
    
  } else {
    
  }
  device_type_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), device_type,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CompleteGroupResponse.device_type)
}
inline ::std::string* CompleteGroupResponse::unsafe_arena_release_device_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CompleteGroupResponse.device_type)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return device_type_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CompleteGroupResponse::unsafe_arena_set_allocated_device_type(
    ::std::string* device_type) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (device_type != NULL) {
    
  } else {
    
  }
  device_type_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      device_type, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CompleteGroupResponse.device_type)
}

// int32 num_tasks = 4;
inline void CompleteGroupResponse::clear_num_tasks() {
  num_tasks_ = 0;
}
inline ::google::protobuf::int32 CompleteGroupResponse::num_tasks() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupResponse.num_tasks)
  return num_tasks_;
}
inline void CompleteGroupResponse::set_num_tasks(::google::protobuf::int32 value) {
  
  num_tasks_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupResponse.num_tasks)
}

// repeated string device_name = 5;
inline int CompleteGroupResponse::device_name_size() const {
  return device_name_.size();
}
inline void CompleteGroupResponse::clear_device_name() {
  device_name_.Clear();
}
inline const ::std::string& CompleteGroupResponse::device_name(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupResponse.device_name)
  return device_name_.Get(index);
}
inline ::std::string* CompleteGroupResponse::mutable_device_name(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CompleteGroupResponse.device_name)
  return device_name_.Mutable(index);
}
inline void CompleteGroupResponse::set_device_name(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupResponse.device_name)
  device_name_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void CompleteGroupResponse::set_device_name(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupResponse.device_name)
  device_name_.Mutable(index)->assign(std::move(value));
}
#endif
inline void CompleteGroupResponse::set_device_name(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  device_name_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.CompleteGroupResponse.device_name)
}
inline void CompleteGroupResponse::set_device_name(int index, const char* value, size_t size) {
  device_name_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CompleteGroupResponse.device_name)
}
inline ::std::string* CompleteGroupResponse::add_device_name() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.CompleteGroupResponse.device_name)
  return device_name_.Add();
}
inline void CompleteGroupResponse::add_device_name(const ::std::string& value) {
  device_name_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.CompleteGroupResponse.device_name)
}
#if LANG_CXX11
inline void CompleteGroupResponse::add_device_name(::std::string&& value) {
  device_name_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.CompleteGroupResponse.device_name)
}
#endif
inline void CompleteGroupResponse::add_device_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  device_name_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.CompleteGroupResponse.device_name)
}
inline void CompleteGroupResponse::add_device_name(const char* value, size_t size) {
  device_name_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.CompleteGroupResponse.device_name)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
CompleteGroupResponse::device_name() const {
  // @@protoc_insertion_point(field_list:tensorflow.CompleteGroupResponse.device_name)
  return device_name_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
CompleteGroupResponse::mutable_device_name() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CompleteGroupResponse.device_name)
  return &device_name_;
}

// repeated string task_name = 6;
inline int CompleteGroupResponse::task_name_size() const {
  return task_name_.size();
}
inline void CompleteGroupResponse::clear_task_name() {
  task_name_.Clear();
}
inline const ::std::string& CompleteGroupResponse::task_name(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupResponse.task_name)
  return task_name_.Get(index);
}
inline ::std::string* CompleteGroupResponse::mutable_task_name(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CompleteGroupResponse.task_name)
  return task_name_.Mutable(index);
}
inline void CompleteGroupResponse::set_task_name(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupResponse.task_name)
  task_name_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void CompleteGroupResponse::set_task_name(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupResponse.task_name)
  task_name_.Mutable(index)->assign(std::move(value));
}
#endif
inline void CompleteGroupResponse::set_task_name(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  task_name_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.CompleteGroupResponse.task_name)
}
inline void CompleteGroupResponse::set_task_name(int index, const char* value, size_t size) {
  task_name_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CompleteGroupResponse.task_name)
}
inline ::std::string* CompleteGroupResponse::add_task_name() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.CompleteGroupResponse.task_name)
  return task_name_.Add();
}
inline void CompleteGroupResponse::add_task_name(const ::std::string& value) {
  task_name_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.CompleteGroupResponse.task_name)
}
#if LANG_CXX11
inline void CompleteGroupResponse::add_task_name(::std::string&& value) {
  task_name_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.CompleteGroupResponse.task_name)
}
#endif
inline void CompleteGroupResponse::add_task_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  task_name_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.CompleteGroupResponse.task_name)
}
inline void CompleteGroupResponse::add_task_name(const char* value, size_t size) {
  task_name_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.CompleteGroupResponse.task_name)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
CompleteGroupResponse::task_name() const {
  // @@protoc_insertion_point(field_list:tensorflow.CompleteGroupResponse.task_name)
  return task_name_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
CompleteGroupResponse::mutable_task_name() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CompleteGroupResponse.task_name)
  return &task_name_;
}

// -------------------------------------------------------------------

// CompleteInstanceRequest

// string name = 1;
inline void CompleteInstanceRequest::clear_name() {
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& CompleteInstanceRequest::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.name)
  return name_.Get();
}
inline void CompleteInstanceRequest::set_name(const ::std::string& value) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.name)
}
#if LANG_CXX11
inline void CompleteInstanceRequest::set_name(::std::string&& value) {
  
  name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CompleteInstanceRequest.name)
}
#endif
inline void CompleteInstanceRequest::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CompleteInstanceRequest.name)
}
inline void CompleteInstanceRequest::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CompleteInstanceRequest.name)
}
inline ::std::string* CompleteInstanceRequest::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CompleteInstanceRequest.name)
  return name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* CompleteInstanceRequest::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.CompleteInstanceRequest.name)
  
  return name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CompleteInstanceRequest::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CompleteInstanceRequest.name)
}
inline ::std::string* CompleteInstanceRequest::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CompleteInstanceRequest.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CompleteInstanceRequest::unsafe_arena_set_allocated_name(
    ::std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (name != NULL) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CompleteInstanceRequest.name)
}

// int32 type = 2;
inline void CompleteInstanceRequest::clear_type() {
  type_ = 0;
}
inline ::google::protobuf::int32 CompleteInstanceRequest::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.type)
  return type_;
}
inline void CompleteInstanceRequest::set_type(::google::protobuf::int32 value) {
  
  type_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.type)
}

// .tensorflow.DataType data_type = 3;
inline void CompleteInstanceRequest::clear_data_type() {
  data_type_ = 0;
}
inline ::tensorflow::DataType CompleteInstanceRequest::data_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.data_type)
  return static_cast< ::tensorflow::DataType >(data_type_);
}
inline void CompleteInstanceRequest::set_data_type(::tensorflow::DataType value) {
  
  data_type_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.data_type)
}

// .tensorflow.TensorShapeProto shape = 4;
inline bool CompleteInstanceRequest::has_shape() const {
  return this != internal_default_instance() && shape_ != NULL;
}
inline const ::tensorflow::TensorShapeProto& CompleteInstanceRequest::_internal_shape() const {
  return *shape_;
}
inline const ::tensorflow::TensorShapeProto& CompleteInstanceRequest::shape() const {
  const ::tensorflow::TensorShapeProto* p = shape_;
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.shape)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::TensorShapeProto*>(
      &::tensorflow::_TensorShapeProto_default_instance_);
}
inline ::tensorflow::TensorShapeProto* CompleteInstanceRequest::release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.CompleteInstanceRequest.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  shape_ = NULL;
  return temp;
}
inline ::tensorflow::TensorShapeProto* CompleteInstanceRequest::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CompleteInstanceRequest.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  shape_ = NULL;
  return temp;
}
inline ::tensorflow::TensorShapeProto* CompleteInstanceRequest::mutable_shape() {
  
  if (shape_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaNoVirtual());
    shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.CompleteInstanceRequest.shape)
  return shape_;
}
inline void CompleteInstanceRequest::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(shape_);
  }
  if (shape) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(shape)->GetArena();
    if (message_arena != submessage_arena) {
      shape = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CompleteInstanceRequest.shape)
}

// int32 group_key = 5;
inline void CompleteInstanceRequest::clear_group_key() {
  group_key_ = 0;
}
inline ::google::protobuf::int32 CompleteInstanceRequest::group_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.group_key)
  return group_key_;
}
inline void CompleteInstanceRequest::set_group_key(::google::protobuf::int32 value) {
  
  group_key_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.group_key)
}

// int32 group_size = 6;
inline void CompleteInstanceRequest::clear_group_size() {
  group_size_ = 0;
}
inline ::google::protobuf::int32 CompleteInstanceRequest::group_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.group_size)
  return group_size_;
}
inline void CompleteInstanceRequest::set_group_size(::google::protobuf::int32 value) {
  
  group_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.group_size)
}

// int32 instance_key = 7;
inline void CompleteInstanceRequest::clear_instance_key() {
  instance_key_ = 0;
}
inline ::google::protobuf::int32 CompleteInstanceRequest::instance_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.instance_key)
  return instance_key_;
}
inline void CompleteInstanceRequest::set_instance_key(::google::protobuf::int32 value) {
  
  instance_key_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.instance_key)
}

// string device_type = 8;
inline void CompleteInstanceRequest::clear_device_type() {
  device_type_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& CompleteInstanceRequest::device_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.device_type)
  return device_type_.Get();
}
inline void CompleteInstanceRequest::set_device_type(const ::std::string& value) {
  
  device_type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.device_type)
}
#if LANG_CXX11
inline void CompleteInstanceRequest::set_device_type(::std::string&& value) {
  
  device_type_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CompleteInstanceRequest.device_type)
}
#endif
inline void CompleteInstanceRequest::set_device_type(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  device_type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CompleteInstanceRequest.device_type)
}
inline void CompleteInstanceRequest::set_device_type(const char* value,
    size_t size) {
  
  device_type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CompleteInstanceRequest.device_type)
}
inline ::std::string* CompleteInstanceRequest::mutable_device_type() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CompleteInstanceRequest.device_type)
  return device_type_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* CompleteInstanceRequest::release_device_type() {
  // @@protoc_insertion_point(field_release:tensorflow.CompleteInstanceRequest.device_type)
  
  return device_type_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CompleteInstanceRequest::set_allocated_device_type(::std::string* device_type) {
  if (device_type != NULL) {
    
  } else {
    
  }
  device_type_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), device_type,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CompleteInstanceRequest.device_type)
}
inline ::std::string* CompleteInstanceRequest::unsafe_arena_release_device_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CompleteInstanceRequest.device_type)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return device_type_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CompleteInstanceRequest::unsafe_arena_set_allocated_device_type(
    ::std::string* device_type) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (device_type != NULL) {
    
  } else {
    
  }
  device_type_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      device_type, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CompleteInstanceRequest.device_type)
}

// repeated int32 subdiv_offset = 9;
inline int CompleteInstanceRequest::subdiv_offset_size() const {
  return subdiv_offset_.size();
}
inline void CompleteInstanceRequest::clear_subdiv_offset() {
  subdiv_offset_.Clear();
}
inline ::google::protobuf::int32 CompleteInstanceRequest::subdiv_offset(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.subdiv_offset)
  return subdiv_offset_.Get(index);
}
inline void CompleteInstanceRequest::set_subdiv_offset(int index, ::google::protobuf::int32 value) {
  subdiv_offset_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.subdiv_offset)
}
inline void CompleteInstanceRequest::add_subdiv_offset(::google::protobuf::int32 value) {
  subdiv_offset_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.CompleteInstanceRequest.subdiv_offset)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
CompleteInstanceRequest::subdiv_offset() const {
  // @@protoc_insertion_point(field_list:tensorflow.CompleteInstanceRequest.subdiv_offset)
  return subdiv_offset_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
CompleteInstanceRequest::mutable_subdiv_offset() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CompleteInstanceRequest.subdiv_offset)
  return &subdiv_offset_;
}

// string device = 10;
inline void CompleteInstanceRequest::clear_device() {
  device_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& CompleteInstanceRequest::device() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.device)
  return device_.Get();
}
inline void CompleteInstanceRequest::set_device(const ::std::string& value) {
  
  device_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.device)
}
#if LANG_CXX11
inline void CompleteInstanceRequest::set_device(::std::string&& value) {
  
  device_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CompleteInstanceRequest.device)
}
#endif
inline void CompleteInstanceRequest::set_device(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  device_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CompleteInstanceRequest.device)
}
inline void CompleteInstanceRequest::set_device(const char* value,
    size_t size) {
  
  device_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CompleteInstanceRequest.device)
}
inline ::std::string* CompleteInstanceRequest::mutable_device() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CompleteInstanceRequest.device)
  return device_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* CompleteInstanceRequest::release_device() {
  // @@protoc_insertion_point(field_release:tensorflow.CompleteInstanceRequest.device)
  
  return device_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CompleteInstanceRequest::set_allocated_device(::std::string* device) {
  if (device != NULL) {
    
  } else {
    
  }
  device_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), device,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CompleteInstanceRequest.device)
}
inline ::std::string* CompleteInstanceRequest::unsafe_arena_release_device() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CompleteInstanceRequest.device)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return device_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CompleteInstanceRequest::unsafe_arena_set_allocated_device(
    ::std::string* device) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (device != NULL) {
    
  } else {
    
  }
  device_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      device, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CompleteInstanceRequest.device)
}

// bool is_source = 11;
inline void CompleteInstanceRequest::clear_is_source() {
  is_source_ = false;
}
inline bool CompleteInstanceRequest::is_source() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.is_source)
  return is_source_;
}
inline void CompleteInstanceRequest::set_is_source(bool value) {
  
  is_source_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.is_source)
}

// -------------------------------------------------------------------

// CompleteInstanceResponse

// int32 instance_key = 1;
inline void CompleteInstanceResponse::clear_instance_key() {
  instance_key_ = 0;
}
inline ::google::protobuf::int32 CompleteInstanceResponse::instance_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceResponse.instance_key)
  return instance_key_;
}
inline void CompleteInstanceResponse::set_instance_key(::google::protobuf::int32 value) {
  
  instance_key_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceResponse.instance_key)
}

// int32 source_rank = 2;
inline void CompleteInstanceResponse::clear_source_rank() {
  source_rank_ = 0;
}
inline ::google::protobuf::int32 CompleteInstanceResponse::source_rank() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceResponse.source_rank)
  return source_rank_;
}
inline void CompleteInstanceResponse::set_source_rank(::google::protobuf::int32 value) {
  
  source_rank_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceResponse.source_rank)
}

// bytes communicator_key = 3;
inline void CompleteInstanceResponse::clear_communicator_key() {
  communicator_key_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& CompleteInstanceResponse::communicator_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceResponse.communicator_key)
  return communicator_key_.Get();
}
inline void CompleteInstanceResponse::set_communicator_key(const ::std::string& value) {
  
  communicator_key_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceResponse.communicator_key)
}
#if LANG_CXX11
inline void CompleteInstanceResponse::set_communicator_key(::std::string&& value) {
  
  communicator_key_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CompleteInstanceResponse.communicator_key)
}
#endif
inline void CompleteInstanceResponse::set_communicator_key(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  communicator_key_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CompleteInstanceResponse.communicator_key)
}
inline void CompleteInstanceResponse::set_communicator_key(const void* value,
    size_t size) {
  
  communicator_key_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CompleteInstanceResponse.communicator_key)
}
inline ::std::string* CompleteInstanceResponse::mutable_communicator_key() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CompleteInstanceResponse.communicator_key)
  return communicator_key_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* CompleteInstanceResponse::release_communicator_key() {
  // @@protoc_insertion_point(field_release:tensorflow.CompleteInstanceResponse.communicator_key)
  
  return communicator_key_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CompleteInstanceResponse::set_allocated_communicator_key(::std::string* communicator_key) {
  if (communicator_key != NULL) {
    
  } else {
    
  }
  communicator_key_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), communicator_key,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CompleteInstanceResponse.communicator_key)
}
inline ::std::string* CompleteInstanceResponse::unsafe_arena_release_communicator_key() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CompleteInstanceResponse.communicator_key)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return communicator_key_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CompleteInstanceResponse::unsafe_arena_set_allocated_communicator_key(
    ::std::string* communicator_key) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (communicator_key != NULL) {
    
  } else {
    
  }
  communicator_key_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      communicator_key, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CompleteInstanceResponse.communicator_key)
}

// -------------------------------------------------------------------

// GetStepSequenceRequest

// repeated int64 graph_key = 1;
inline int GetStepSequenceRequest::graph_key_size() const {
  return graph_key_.size();
}
inline void GetStepSequenceRequest::clear_graph_key() {
  graph_key_.Clear();
}
inline ::google::protobuf::int64 GetStepSequenceRequest::graph_key(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GetStepSequenceRequest.graph_key)
  return graph_key_.Get(index);
}
inline void GetStepSequenceRequest::set_graph_key(int index, ::google::protobuf::int64 value) {
  graph_key_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.GetStepSequenceRequest.graph_key)
}
inline void GetStepSequenceRequest::add_graph_key(::google::protobuf::int64 value) {
  graph_key_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.GetStepSequenceRequest.graph_key)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
GetStepSequenceRequest::graph_key() const {
  // @@protoc_insertion_point(field_list:tensorflow.GetStepSequenceRequest.graph_key)
  return graph_key_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
GetStepSequenceRequest::mutable_graph_key() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GetStepSequenceRequest.graph_key)
  return &graph_key_;
}

// -------------------------------------------------------------------

// StepSequence

// int64 graph_key = 1;
inline void StepSequence::clear_graph_key() {
  graph_key_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 StepSequence::graph_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.StepSequence.graph_key)
  return graph_key_;
}
inline void StepSequence::set_graph_key(::google::protobuf::int64 value) {
  
  graph_key_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.StepSequence.graph_key)
}

// int64 next_step_id = 2;
inline void StepSequence::clear_next_step_id() {
  next_step_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 StepSequence::next_step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.StepSequence.next_step_id)
  return next_step_id_;
}
inline void StepSequence::set_next_step_id(::google::protobuf::int64 value) {
  
  next_step_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.StepSequence.next_step_id)
}

// -------------------------------------------------------------------

// GetStepSequenceResponse

// repeated .tensorflow.StepSequence step_sequence = 1;
inline int GetStepSequenceResponse::step_sequence_size() const {
  return step_sequence_.size();
}
inline void GetStepSequenceResponse::clear_step_sequence() {
  step_sequence_.Clear();
}
inline ::tensorflow::StepSequence* GetStepSequenceResponse::mutable_step_sequence(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GetStepSequenceResponse.step_sequence)
  return step_sequence_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::StepSequence >*
GetStepSequenceResponse::mutable_step_sequence() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GetStepSequenceResponse.step_sequence)
  return &step_sequence_;
}
inline const ::tensorflow::StepSequence& GetStepSequenceResponse::step_sequence(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GetStepSequenceResponse.step_sequence)
  return step_sequence_.Get(index);
}
inline ::tensorflow::StepSequence* GetStepSequenceResponse::add_step_sequence() {
  // @@protoc_insertion_point(field_add:tensorflow.GetStepSequenceResponse.step_sequence)
  return step_sequence_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::StepSequence >&
GetStepSequenceResponse::step_sequence() const {
  // @@protoc_insertion_point(field_list:tensorflow.GetStepSequenceResponse.step_sequence)
  return step_sequence_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fworker_2eproto

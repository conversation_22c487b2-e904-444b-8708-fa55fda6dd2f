// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/meta_graph.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/any.pb.h>
#include "tensorflow/core/framework/graph.pb.h"
#include "tensorflow/core/framework/op_def.pb.h"
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
#include "tensorflow/core/protobuf/saved_object_graph.pb.h"
#include "tensorflow/core/protobuf/saver.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto 

namespace protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[16];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto
namespace tensorflow {
class AssetFileDef;
class AssetFileDefDefaultTypeInternal;
extern AssetFileDefDefaultTypeInternal _AssetFileDef_default_instance_;
class CollectionDef;
class CollectionDefDefaultTypeInternal;
extern CollectionDefDefaultTypeInternal _CollectionDef_default_instance_;
class CollectionDef_AnyList;
class CollectionDef_AnyListDefaultTypeInternal;
extern CollectionDef_AnyListDefaultTypeInternal _CollectionDef_AnyList_default_instance_;
class CollectionDef_BytesList;
class CollectionDef_BytesListDefaultTypeInternal;
extern CollectionDef_BytesListDefaultTypeInternal _CollectionDef_BytesList_default_instance_;
class CollectionDef_FloatList;
class CollectionDef_FloatListDefaultTypeInternal;
extern CollectionDef_FloatListDefaultTypeInternal _CollectionDef_FloatList_default_instance_;
class CollectionDef_Int64List;
class CollectionDef_Int64ListDefaultTypeInternal;
extern CollectionDef_Int64ListDefaultTypeInternal _CollectionDef_Int64List_default_instance_;
class CollectionDef_NodeList;
class CollectionDef_NodeListDefaultTypeInternal;
extern CollectionDef_NodeListDefaultTypeInternal _CollectionDef_NodeList_default_instance_;
class MetaGraphDef;
class MetaGraphDefDefaultTypeInternal;
extern MetaGraphDefDefaultTypeInternal _MetaGraphDef_default_instance_;
class MetaGraphDef_CollectionDefEntry_DoNotUse;
class MetaGraphDef_CollectionDefEntry_DoNotUseDefaultTypeInternal;
extern MetaGraphDef_CollectionDefEntry_DoNotUseDefaultTypeInternal _MetaGraphDef_CollectionDefEntry_DoNotUse_default_instance_;
class MetaGraphDef_MetaInfoDef;
class MetaGraphDef_MetaInfoDefDefaultTypeInternal;
extern MetaGraphDef_MetaInfoDefDefaultTypeInternal _MetaGraphDef_MetaInfoDef_default_instance_;
class MetaGraphDef_SignatureDefEntry_DoNotUse;
class MetaGraphDef_SignatureDefEntry_DoNotUseDefaultTypeInternal;
extern MetaGraphDef_SignatureDefEntry_DoNotUseDefaultTypeInternal _MetaGraphDef_SignatureDefEntry_DoNotUse_default_instance_;
class SignatureDef;
class SignatureDefDefaultTypeInternal;
extern SignatureDefDefaultTypeInternal _SignatureDef_default_instance_;
class SignatureDef_InputsEntry_DoNotUse;
class SignatureDef_InputsEntry_DoNotUseDefaultTypeInternal;
extern SignatureDef_InputsEntry_DoNotUseDefaultTypeInternal _SignatureDef_InputsEntry_DoNotUse_default_instance_;
class SignatureDef_OutputsEntry_DoNotUse;
class SignatureDef_OutputsEntry_DoNotUseDefaultTypeInternal;
extern SignatureDef_OutputsEntry_DoNotUseDefaultTypeInternal _SignatureDef_OutputsEntry_DoNotUse_default_instance_;
class TensorInfo;
class TensorInfoDefaultTypeInternal;
extern TensorInfoDefaultTypeInternal _TensorInfo_default_instance_;
class TensorInfo_CooSparse;
class TensorInfo_CooSparseDefaultTypeInternal;
extern TensorInfo_CooSparseDefaultTypeInternal _TensorInfo_CooSparse_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::AssetFileDef* Arena::CreateMaybeMessage<::tensorflow::AssetFileDef>(Arena*);
template<> ::tensorflow::CollectionDef* Arena::CreateMaybeMessage<::tensorflow::CollectionDef>(Arena*);
template<> ::tensorflow::CollectionDef_AnyList* Arena::CreateMaybeMessage<::tensorflow::CollectionDef_AnyList>(Arena*);
template<> ::tensorflow::CollectionDef_BytesList* Arena::CreateMaybeMessage<::tensorflow::CollectionDef_BytesList>(Arena*);
template<> ::tensorflow::CollectionDef_FloatList* Arena::CreateMaybeMessage<::tensorflow::CollectionDef_FloatList>(Arena*);
template<> ::tensorflow::CollectionDef_Int64List* Arena::CreateMaybeMessage<::tensorflow::CollectionDef_Int64List>(Arena*);
template<> ::tensorflow::CollectionDef_NodeList* Arena::CreateMaybeMessage<::tensorflow::CollectionDef_NodeList>(Arena*);
template<> ::tensorflow::MetaGraphDef* Arena::CreateMaybeMessage<::tensorflow::MetaGraphDef>(Arena*);
template<> ::tensorflow::MetaGraphDef_CollectionDefEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::MetaGraphDef_CollectionDefEntry_DoNotUse>(Arena*);
template<> ::tensorflow::MetaGraphDef_MetaInfoDef* Arena::CreateMaybeMessage<::tensorflow::MetaGraphDef_MetaInfoDef>(Arena*);
template<> ::tensorflow::MetaGraphDef_SignatureDefEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::MetaGraphDef_SignatureDefEntry_DoNotUse>(Arena*);
template<> ::tensorflow::SignatureDef* Arena::CreateMaybeMessage<::tensorflow::SignatureDef>(Arena*);
template<> ::tensorflow::SignatureDef_InputsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::SignatureDef_InputsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::SignatureDef_OutputsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::SignatureDef_OutputsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::TensorInfo* Arena::CreateMaybeMessage<::tensorflow::TensorInfo>(Arena*);
template<> ::tensorflow::TensorInfo_CooSparse* Arena::CreateMaybeMessage<::tensorflow::TensorInfo_CooSparse>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

// ===================================================================

class MetaGraphDef_MetaInfoDef : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.MetaGraphDef.MetaInfoDef) */ {
 public:
  MetaGraphDef_MetaInfoDef();
  virtual ~MetaGraphDef_MetaInfoDef();

  MetaGraphDef_MetaInfoDef(const MetaGraphDef_MetaInfoDef& from);

  inline MetaGraphDef_MetaInfoDef& operator=(const MetaGraphDef_MetaInfoDef& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  MetaGraphDef_MetaInfoDef(MetaGraphDef_MetaInfoDef&& from) noexcept
    : MetaGraphDef_MetaInfoDef() {
    *this = ::std::move(from);
  }

  inline MetaGraphDef_MetaInfoDef& operator=(MetaGraphDef_MetaInfoDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const MetaGraphDef_MetaInfoDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MetaGraphDef_MetaInfoDef* internal_default_instance() {
    return reinterpret_cast<const MetaGraphDef_MetaInfoDef*>(
               &_MetaGraphDef_MetaInfoDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(MetaGraphDef_MetaInfoDef* other);
  void Swap(MetaGraphDef_MetaInfoDef* other);
  friend void swap(MetaGraphDef_MetaInfoDef& a, MetaGraphDef_MetaInfoDef& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline MetaGraphDef_MetaInfoDef* New() const final {
    return CreateMaybeMessage<MetaGraphDef_MetaInfoDef>(NULL);
  }

  MetaGraphDef_MetaInfoDef* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<MetaGraphDef_MetaInfoDef>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const MetaGraphDef_MetaInfoDef& from);
  void MergeFrom(const MetaGraphDef_MetaInfoDef& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MetaGraphDef_MetaInfoDef* other);
  protected:
  explicit MetaGraphDef_MetaInfoDef(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string tags = 4;
  int tags_size() const;
  void clear_tags();
  static const int kTagsFieldNumber = 4;
  const ::std::string& tags(int index) const;
  ::std::string* mutable_tags(int index);
  void set_tags(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_tags(int index, ::std::string&& value);
  #endif
  void set_tags(int index, const char* value);
  void set_tags(int index, const char* value, size_t size);
  ::std::string* add_tags();
  void add_tags(const ::std::string& value);
  #if LANG_CXX11
  void add_tags(::std::string&& value);
  #endif
  void add_tags(const char* value);
  void add_tags(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& tags() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_tags();

  // string meta_graph_version = 1;
  void clear_meta_graph_version();
  static const int kMetaGraphVersionFieldNumber = 1;
  const ::std::string& meta_graph_version() const;
  void set_meta_graph_version(const ::std::string& value);
  #if LANG_CXX11
  void set_meta_graph_version(::std::string&& value);
  #endif
  void set_meta_graph_version(const char* value);
  void set_meta_graph_version(const char* value, size_t size);
  ::std::string* mutable_meta_graph_version();
  ::std::string* release_meta_graph_version();
  void set_allocated_meta_graph_version(::std::string* meta_graph_version);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_meta_graph_version();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_meta_graph_version(
      ::std::string* meta_graph_version);

  // string tensorflow_version = 5;
  void clear_tensorflow_version();
  static const int kTensorflowVersionFieldNumber = 5;
  const ::std::string& tensorflow_version() const;
  void set_tensorflow_version(const ::std::string& value);
  #if LANG_CXX11
  void set_tensorflow_version(::std::string&& value);
  #endif
  void set_tensorflow_version(const char* value);
  void set_tensorflow_version(const char* value, size_t size);
  ::std::string* mutable_tensorflow_version();
  ::std::string* release_tensorflow_version();
  void set_allocated_tensorflow_version(::std::string* tensorflow_version);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_tensorflow_version();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_tensorflow_version(
      ::std::string* tensorflow_version);

  // string tensorflow_git_version = 6;
  void clear_tensorflow_git_version();
  static const int kTensorflowGitVersionFieldNumber = 6;
  const ::std::string& tensorflow_git_version() const;
  void set_tensorflow_git_version(const ::std::string& value);
  #if LANG_CXX11
  void set_tensorflow_git_version(::std::string&& value);
  #endif
  void set_tensorflow_git_version(const char* value);
  void set_tensorflow_git_version(const char* value, size_t size);
  ::std::string* mutable_tensorflow_git_version();
  ::std::string* release_tensorflow_git_version();
  void set_allocated_tensorflow_git_version(::std::string* tensorflow_git_version);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_tensorflow_git_version();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_tensorflow_git_version(
      ::std::string* tensorflow_git_version);

  // .tensorflow.OpList stripped_op_list = 2;
  bool has_stripped_op_list() const;
  void clear_stripped_op_list();
  static const int kStrippedOpListFieldNumber = 2;
  private:
  const ::tensorflow::OpList& _internal_stripped_op_list() const;
  public:
  const ::tensorflow::OpList& stripped_op_list() const;
  ::tensorflow::OpList* release_stripped_op_list();
  ::tensorflow::OpList* mutable_stripped_op_list();
  void set_allocated_stripped_op_list(::tensorflow::OpList* stripped_op_list);
  void unsafe_arena_set_allocated_stripped_op_list(
      ::tensorflow::OpList* stripped_op_list);
  ::tensorflow::OpList* unsafe_arena_release_stripped_op_list();

  // .google.protobuf.Any any_info = 3;
  bool has_any_info() const;
  void clear_any_info();
  static const int kAnyInfoFieldNumber = 3;
  private:
  const ::google::protobuf::Any& _internal_any_info() const;
  public:
  const ::google::protobuf::Any& any_info() const;
  ::google::protobuf::Any* release_any_info();
  ::google::protobuf::Any* mutable_any_info();
  void set_allocated_any_info(::google::protobuf::Any* any_info);
  void unsafe_arena_set_allocated_any_info(
      ::google::protobuf::Any* any_info);
  ::google::protobuf::Any* unsafe_arena_release_any_info();

  // bool stripped_default_attrs = 7;
  void clear_stripped_default_attrs();
  static const int kStrippedDefaultAttrsFieldNumber = 7;
  bool stripped_default_attrs() const;
  void set_stripped_default_attrs(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.MetaGraphDef.MetaInfoDef)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::std::string> tags_;
  ::google::protobuf::internal::ArenaStringPtr meta_graph_version_;
  ::google::protobuf::internal::ArenaStringPtr tensorflow_version_;
  ::google::protobuf::internal::ArenaStringPtr tensorflow_git_version_;
  ::tensorflow::OpList* stripped_op_list_;
  ::google::protobuf::Any* any_info_;
  bool stripped_default_attrs_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class MetaGraphDef_CollectionDefEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<MetaGraphDef_CollectionDefEntry_DoNotUse, 
    ::std::string, ::tensorflow::CollectionDef,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<MetaGraphDef_CollectionDefEntry_DoNotUse, 
    ::std::string, ::tensorflow::CollectionDef,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  MetaGraphDef_CollectionDefEntry_DoNotUse();
  MetaGraphDef_CollectionDefEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const MetaGraphDef_CollectionDefEntry_DoNotUse& other);
  static const MetaGraphDef_CollectionDefEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const MetaGraphDef_CollectionDefEntry_DoNotUse*>(&_MetaGraphDef_CollectionDefEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class MetaGraphDef_SignatureDefEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<MetaGraphDef_SignatureDefEntry_DoNotUse, 
    ::std::string, ::tensorflow::SignatureDef,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<MetaGraphDef_SignatureDefEntry_DoNotUse, 
    ::std::string, ::tensorflow::SignatureDef,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  MetaGraphDef_SignatureDefEntry_DoNotUse();
  MetaGraphDef_SignatureDefEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const MetaGraphDef_SignatureDefEntry_DoNotUse& other);
  static const MetaGraphDef_SignatureDefEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const MetaGraphDef_SignatureDefEntry_DoNotUse*>(&_MetaGraphDef_SignatureDefEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class MetaGraphDef : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.MetaGraphDef) */ {
 public:
  MetaGraphDef();
  virtual ~MetaGraphDef();

  MetaGraphDef(const MetaGraphDef& from);

  inline MetaGraphDef& operator=(const MetaGraphDef& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  MetaGraphDef(MetaGraphDef&& from) noexcept
    : MetaGraphDef() {
    *this = ::std::move(from);
  }

  inline MetaGraphDef& operator=(MetaGraphDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const MetaGraphDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MetaGraphDef* internal_default_instance() {
    return reinterpret_cast<const MetaGraphDef*>(
               &_MetaGraphDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void UnsafeArenaSwap(MetaGraphDef* other);
  void Swap(MetaGraphDef* other);
  friend void swap(MetaGraphDef& a, MetaGraphDef& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline MetaGraphDef* New() const final {
    return CreateMaybeMessage<MetaGraphDef>(NULL);
  }

  MetaGraphDef* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<MetaGraphDef>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const MetaGraphDef& from);
  void MergeFrom(const MetaGraphDef& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MetaGraphDef* other);
  protected:
  explicit MetaGraphDef(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef MetaGraphDef_MetaInfoDef MetaInfoDef;

  // accessors -------------------------------------------------------

  // map<string, .tensorflow.CollectionDef> collection_def = 4;
  int collection_def_size() const;
  void clear_collection_def();
  static const int kCollectionDefFieldNumber = 4;
  const ::google::protobuf::Map< ::std::string, ::tensorflow::CollectionDef >&
      collection_def() const;
  ::google::protobuf::Map< ::std::string, ::tensorflow::CollectionDef >*
      mutable_collection_def();

  // map<string, .tensorflow.SignatureDef> signature_def = 5;
  int signature_def_size() const;
  void clear_signature_def();
  static const int kSignatureDefFieldNumber = 5;
  const ::google::protobuf::Map< ::std::string, ::tensorflow::SignatureDef >&
      signature_def() const;
  ::google::protobuf::Map< ::std::string, ::tensorflow::SignatureDef >*
      mutable_signature_def();

  // repeated .tensorflow.AssetFileDef asset_file_def = 6;
  int asset_file_def_size() const;
  void clear_asset_file_def();
  static const int kAssetFileDefFieldNumber = 6;
  ::tensorflow::AssetFileDef* mutable_asset_file_def(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::AssetFileDef >*
      mutable_asset_file_def();
  const ::tensorflow::AssetFileDef& asset_file_def(int index) const;
  ::tensorflow::AssetFileDef* add_asset_file_def();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::AssetFileDef >&
      asset_file_def() const;

  // .tensorflow.MetaGraphDef.MetaInfoDef meta_info_def = 1;
  bool has_meta_info_def() const;
  void clear_meta_info_def();
  static const int kMetaInfoDefFieldNumber = 1;
  private:
  const ::tensorflow::MetaGraphDef_MetaInfoDef& _internal_meta_info_def() const;
  public:
  const ::tensorflow::MetaGraphDef_MetaInfoDef& meta_info_def() const;
  ::tensorflow::MetaGraphDef_MetaInfoDef* release_meta_info_def();
  ::tensorflow::MetaGraphDef_MetaInfoDef* mutable_meta_info_def();
  void set_allocated_meta_info_def(::tensorflow::MetaGraphDef_MetaInfoDef* meta_info_def);
  void unsafe_arena_set_allocated_meta_info_def(
      ::tensorflow::MetaGraphDef_MetaInfoDef* meta_info_def);
  ::tensorflow::MetaGraphDef_MetaInfoDef* unsafe_arena_release_meta_info_def();

  // .tensorflow.GraphDef graph_def = 2;
  bool has_graph_def() const;
  void clear_graph_def();
  static const int kGraphDefFieldNumber = 2;
  private:
  const ::tensorflow::GraphDef& _internal_graph_def() const;
  public:
  const ::tensorflow::GraphDef& graph_def() const;
  ::tensorflow::GraphDef* release_graph_def();
  ::tensorflow::GraphDef* mutable_graph_def();
  void set_allocated_graph_def(::tensorflow::GraphDef* graph_def);
  void unsafe_arena_set_allocated_graph_def(
      ::tensorflow::GraphDef* graph_def);
  ::tensorflow::GraphDef* unsafe_arena_release_graph_def();

  // .tensorflow.SaverDef saver_def = 3;
  bool has_saver_def() const;
  void clear_saver_def();
  static const int kSaverDefFieldNumber = 3;
  private:
  const ::tensorflow::SaverDef& _internal_saver_def() const;
  public:
  const ::tensorflow::SaverDef& saver_def() const;
  ::tensorflow::SaverDef* release_saver_def();
  ::tensorflow::SaverDef* mutable_saver_def();
  void set_allocated_saver_def(::tensorflow::SaverDef* saver_def);
  void unsafe_arena_set_allocated_saver_def(
      ::tensorflow::SaverDef* saver_def);
  ::tensorflow::SaverDef* unsafe_arena_release_saver_def();

  // .tensorflow.SavedObjectGraph object_graph_def = 7;
  bool has_object_graph_def() const;
  void clear_object_graph_def();
  static const int kObjectGraphDefFieldNumber = 7;
  private:
  const ::tensorflow::SavedObjectGraph& _internal_object_graph_def() const;
  public:
  const ::tensorflow::SavedObjectGraph& object_graph_def() const;
  ::tensorflow::SavedObjectGraph* release_object_graph_def();
  ::tensorflow::SavedObjectGraph* mutable_object_graph_def();
  void set_allocated_object_graph_def(::tensorflow::SavedObjectGraph* object_graph_def);
  void unsafe_arena_set_allocated_object_graph_def(
      ::tensorflow::SavedObjectGraph* object_graph_def);
  ::tensorflow::SavedObjectGraph* unsafe_arena_release_object_graph_def();

  // @@protoc_insertion_point(class_scope:tensorflow.MetaGraphDef)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::MapField<
      MetaGraphDef_CollectionDefEntry_DoNotUse,
      ::std::string, ::tensorflow::CollectionDef,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > collection_def_;
  ::google::protobuf::internal::MapField<
      MetaGraphDef_SignatureDefEntry_DoNotUse,
      ::std::string, ::tensorflow::SignatureDef,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > signature_def_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::AssetFileDef > asset_file_def_;
  ::tensorflow::MetaGraphDef_MetaInfoDef* meta_info_def_;
  ::tensorflow::GraphDef* graph_def_;
  ::tensorflow::SaverDef* saver_def_;
  ::tensorflow::SavedObjectGraph* object_graph_def_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CollectionDef_NodeList : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.CollectionDef.NodeList) */ {
 public:
  CollectionDef_NodeList();
  virtual ~CollectionDef_NodeList();

  CollectionDef_NodeList(const CollectionDef_NodeList& from);

  inline CollectionDef_NodeList& operator=(const CollectionDef_NodeList& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CollectionDef_NodeList(CollectionDef_NodeList&& from) noexcept
    : CollectionDef_NodeList() {
    *this = ::std::move(from);
  }

  inline CollectionDef_NodeList& operator=(CollectionDef_NodeList&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const CollectionDef_NodeList& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CollectionDef_NodeList* internal_default_instance() {
    return reinterpret_cast<const CollectionDef_NodeList*>(
               &_CollectionDef_NodeList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void UnsafeArenaSwap(CollectionDef_NodeList* other);
  void Swap(CollectionDef_NodeList* other);
  friend void swap(CollectionDef_NodeList& a, CollectionDef_NodeList& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CollectionDef_NodeList* New() const final {
    return CreateMaybeMessage<CollectionDef_NodeList>(NULL);
  }

  CollectionDef_NodeList* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CollectionDef_NodeList>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CollectionDef_NodeList& from);
  void MergeFrom(const CollectionDef_NodeList& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CollectionDef_NodeList* other);
  protected:
  explicit CollectionDef_NodeList(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string value = 1;
  int value_size() const;
  void clear_value();
  static const int kValueFieldNumber = 1;
  const ::std::string& value(int index) const;
  ::std::string* mutable_value(int index);
  void set_value(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_value(int index, ::std::string&& value);
  #endif
  void set_value(int index, const char* value);
  void set_value(int index, const char* value, size_t size);
  ::std::string* add_value();
  void add_value(const ::std::string& value);
  #if LANG_CXX11
  void add_value(::std::string&& value);
  #endif
  void add_value(const char* value);
  void add_value(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& value() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_value();

  // @@protoc_insertion_point(class_scope:tensorflow.CollectionDef.NodeList)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::std::string> value_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CollectionDef_BytesList : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.CollectionDef.BytesList) */ {
 public:
  CollectionDef_BytesList();
  virtual ~CollectionDef_BytesList();

  CollectionDef_BytesList(const CollectionDef_BytesList& from);

  inline CollectionDef_BytesList& operator=(const CollectionDef_BytesList& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CollectionDef_BytesList(CollectionDef_BytesList&& from) noexcept
    : CollectionDef_BytesList() {
    *this = ::std::move(from);
  }

  inline CollectionDef_BytesList& operator=(CollectionDef_BytesList&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const CollectionDef_BytesList& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CollectionDef_BytesList* internal_default_instance() {
    return reinterpret_cast<const CollectionDef_BytesList*>(
               &_CollectionDef_BytesList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  void UnsafeArenaSwap(CollectionDef_BytesList* other);
  void Swap(CollectionDef_BytesList* other);
  friend void swap(CollectionDef_BytesList& a, CollectionDef_BytesList& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CollectionDef_BytesList* New() const final {
    return CreateMaybeMessage<CollectionDef_BytesList>(NULL);
  }

  CollectionDef_BytesList* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CollectionDef_BytesList>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CollectionDef_BytesList& from);
  void MergeFrom(const CollectionDef_BytesList& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CollectionDef_BytesList* other);
  protected:
  explicit CollectionDef_BytesList(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated bytes value = 1;
  int value_size() const;
  void clear_value();
  static const int kValueFieldNumber = 1;
  const ::std::string& value(int index) const;
  ::std::string* mutable_value(int index);
  void set_value(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_value(int index, ::std::string&& value);
  #endif
  void set_value(int index, const char* value);
  void set_value(int index, const void* value, size_t size);
  ::std::string* add_value();
  void add_value(const ::std::string& value);
  #if LANG_CXX11
  void add_value(::std::string&& value);
  #endif
  void add_value(const char* value);
  void add_value(const void* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& value() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_value();

  // @@protoc_insertion_point(class_scope:tensorflow.CollectionDef.BytesList)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::std::string> value_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CollectionDef_Int64List : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.CollectionDef.Int64List) */ {
 public:
  CollectionDef_Int64List();
  virtual ~CollectionDef_Int64List();

  CollectionDef_Int64List(const CollectionDef_Int64List& from);

  inline CollectionDef_Int64List& operator=(const CollectionDef_Int64List& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CollectionDef_Int64List(CollectionDef_Int64List&& from) noexcept
    : CollectionDef_Int64List() {
    *this = ::std::move(from);
  }

  inline CollectionDef_Int64List& operator=(CollectionDef_Int64List&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const CollectionDef_Int64List& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CollectionDef_Int64List* internal_default_instance() {
    return reinterpret_cast<const CollectionDef_Int64List*>(
               &_CollectionDef_Int64List_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  void UnsafeArenaSwap(CollectionDef_Int64List* other);
  void Swap(CollectionDef_Int64List* other);
  friend void swap(CollectionDef_Int64List& a, CollectionDef_Int64List& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CollectionDef_Int64List* New() const final {
    return CreateMaybeMessage<CollectionDef_Int64List>(NULL);
  }

  CollectionDef_Int64List* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CollectionDef_Int64List>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CollectionDef_Int64List& from);
  void MergeFrom(const CollectionDef_Int64List& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CollectionDef_Int64List* other);
  protected:
  explicit CollectionDef_Int64List(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int64 value = 1 [packed = true];
  int value_size() const;
  void clear_value();
  static const int kValueFieldNumber = 1;
  ::google::protobuf::int64 value(int index) const;
  void set_value(int index, ::google::protobuf::int64 value);
  void add_value(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      value() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_value();

  // @@protoc_insertion_point(class_scope:tensorflow.CollectionDef.Int64List)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > value_;
  mutable int _value_cached_byte_size_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CollectionDef_FloatList : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.CollectionDef.FloatList) */ {
 public:
  CollectionDef_FloatList();
  virtual ~CollectionDef_FloatList();

  CollectionDef_FloatList(const CollectionDef_FloatList& from);

  inline CollectionDef_FloatList& operator=(const CollectionDef_FloatList& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CollectionDef_FloatList(CollectionDef_FloatList&& from) noexcept
    : CollectionDef_FloatList() {
    *this = ::std::move(from);
  }

  inline CollectionDef_FloatList& operator=(CollectionDef_FloatList&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const CollectionDef_FloatList& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CollectionDef_FloatList* internal_default_instance() {
    return reinterpret_cast<const CollectionDef_FloatList*>(
               &_CollectionDef_FloatList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  void UnsafeArenaSwap(CollectionDef_FloatList* other);
  void Swap(CollectionDef_FloatList* other);
  friend void swap(CollectionDef_FloatList& a, CollectionDef_FloatList& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CollectionDef_FloatList* New() const final {
    return CreateMaybeMessage<CollectionDef_FloatList>(NULL);
  }

  CollectionDef_FloatList* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CollectionDef_FloatList>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CollectionDef_FloatList& from);
  void MergeFrom(const CollectionDef_FloatList& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CollectionDef_FloatList* other);
  protected:
  explicit CollectionDef_FloatList(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated float value = 1 [packed = true];
  int value_size() const;
  void clear_value();
  static const int kValueFieldNumber = 1;
  float value(int index) const;
  void set_value(int index, float value);
  void add_value(float value);
  const ::google::protobuf::RepeatedField< float >&
      value() const;
  ::google::protobuf::RepeatedField< float >*
      mutable_value();

  // @@protoc_insertion_point(class_scope:tensorflow.CollectionDef.FloatList)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< float > value_;
  mutable int _value_cached_byte_size_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CollectionDef_AnyList : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.CollectionDef.AnyList) */ {
 public:
  CollectionDef_AnyList();
  virtual ~CollectionDef_AnyList();

  CollectionDef_AnyList(const CollectionDef_AnyList& from);

  inline CollectionDef_AnyList& operator=(const CollectionDef_AnyList& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CollectionDef_AnyList(CollectionDef_AnyList&& from) noexcept
    : CollectionDef_AnyList() {
    *this = ::std::move(from);
  }

  inline CollectionDef_AnyList& operator=(CollectionDef_AnyList&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const CollectionDef_AnyList& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CollectionDef_AnyList* internal_default_instance() {
    return reinterpret_cast<const CollectionDef_AnyList*>(
               &_CollectionDef_AnyList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  void UnsafeArenaSwap(CollectionDef_AnyList* other);
  void Swap(CollectionDef_AnyList* other);
  friend void swap(CollectionDef_AnyList& a, CollectionDef_AnyList& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CollectionDef_AnyList* New() const final {
    return CreateMaybeMessage<CollectionDef_AnyList>(NULL);
  }

  CollectionDef_AnyList* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CollectionDef_AnyList>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CollectionDef_AnyList& from);
  void MergeFrom(const CollectionDef_AnyList& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CollectionDef_AnyList* other);
  protected:
  explicit CollectionDef_AnyList(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .google.protobuf.Any value = 1;
  int value_size() const;
  void clear_value();
  static const int kValueFieldNumber = 1;
  ::google::protobuf::Any* mutable_value(int index);
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Any >*
      mutable_value();
  const ::google::protobuf::Any& value(int index) const;
  ::google::protobuf::Any* add_value();
  const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Any >&
      value() const;

  // @@protoc_insertion_point(class_scope:tensorflow.CollectionDef.AnyList)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::google::protobuf::Any > value_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CollectionDef : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.CollectionDef) */ {
 public:
  CollectionDef();
  virtual ~CollectionDef();

  CollectionDef(const CollectionDef& from);

  inline CollectionDef& operator=(const CollectionDef& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CollectionDef(CollectionDef&& from) noexcept
    : CollectionDef() {
    *this = ::std::move(from);
  }

  inline CollectionDef& operator=(CollectionDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const CollectionDef& default_instance();

  enum KindCase {
    kNodeList = 1,
    kBytesList = 2,
    kInt64List = 3,
    kFloatList = 4,
    kAnyList = 5,
    KIND_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CollectionDef* internal_default_instance() {
    return reinterpret_cast<const CollectionDef*>(
               &_CollectionDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  void UnsafeArenaSwap(CollectionDef* other);
  void Swap(CollectionDef* other);
  friend void swap(CollectionDef& a, CollectionDef& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CollectionDef* New() const final {
    return CreateMaybeMessage<CollectionDef>(NULL);
  }

  CollectionDef* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CollectionDef>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CollectionDef& from);
  void MergeFrom(const CollectionDef& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CollectionDef* other);
  protected:
  explicit CollectionDef(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef CollectionDef_NodeList NodeList;
  typedef CollectionDef_BytesList BytesList;
  typedef CollectionDef_Int64List Int64List;
  typedef CollectionDef_FloatList FloatList;
  typedef CollectionDef_AnyList AnyList;

  // accessors -------------------------------------------------------

  // .tensorflow.CollectionDef.NodeList node_list = 1;
  bool has_node_list() const;
  void clear_node_list();
  static const int kNodeListFieldNumber = 1;
  private:
  const ::tensorflow::CollectionDef_NodeList& _internal_node_list() const;
  public:
  const ::tensorflow::CollectionDef_NodeList& node_list() const;
  ::tensorflow::CollectionDef_NodeList* release_node_list();
  ::tensorflow::CollectionDef_NodeList* mutable_node_list();
  void set_allocated_node_list(::tensorflow::CollectionDef_NodeList* node_list);
  void unsafe_arena_set_allocated_node_list(
      ::tensorflow::CollectionDef_NodeList* node_list);
  ::tensorflow::CollectionDef_NodeList* unsafe_arena_release_node_list();

  // .tensorflow.CollectionDef.BytesList bytes_list = 2;
  bool has_bytes_list() const;
  void clear_bytes_list();
  static const int kBytesListFieldNumber = 2;
  private:
  const ::tensorflow::CollectionDef_BytesList& _internal_bytes_list() const;
  public:
  const ::tensorflow::CollectionDef_BytesList& bytes_list() const;
  ::tensorflow::CollectionDef_BytesList* release_bytes_list();
  ::tensorflow::CollectionDef_BytesList* mutable_bytes_list();
  void set_allocated_bytes_list(::tensorflow::CollectionDef_BytesList* bytes_list);
  void unsafe_arena_set_allocated_bytes_list(
      ::tensorflow::CollectionDef_BytesList* bytes_list);
  ::tensorflow::CollectionDef_BytesList* unsafe_arena_release_bytes_list();

  // .tensorflow.CollectionDef.Int64List int64_list = 3;
  bool has_int64_list() const;
  void clear_int64_list();
  static const int kInt64ListFieldNumber = 3;
  private:
  const ::tensorflow::CollectionDef_Int64List& _internal_int64_list() const;
  public:
  const ::tensorflow::CollectionDef_Int64List& int64_list() const;
  ::tensorflow::CollectionDef_Int64List* release_int64_list();
  ::tensorflow::CollectionDef_Int64List* mutable_int64_list();
  void set_allocated_int64_list(::tensorflow::CollectionDef_Int64List* int64_list);
  void unsafe_arena_set_allocated_int64_list(
      ::tensorflow::CollectionDef_Int64List* int64_list);
  ::tensorflow::CollectionDef_Int64List* unsafe_arena_release_int64_list();

  // .tensorflow.CollectionDef.FloatList float_list = 4;
  bool has_float_list() const;
  void clear_float_list();
  static const int kFloatListFieldNumber = 4;
  private:
  const ::tensorflow::CollectionDef_FloatList& _internal_float_list() const;
  public:
  const ::tensorflow::CollectionDef_FloatList& float_list() const;
  ::tensorflow::CollectionDef_FloatList* release_float_list();
  ::tensorflow::CollectionDef_FloatList* mutable_float_list();
  void set_allocated_float_list(::tensorflow::CollectionDef_FloatList* float_list);
  void unsafe_arena_set_allocated_float_list(
      ::tensorflow::CollectionDef_FloatList* float_list);
  ::tensorflow::CollectionDef_FloatList* unsafe_arena_release_float_list();

  // .tensorflow.CollectionDef.AnyList any_list = 5;
  bool has_any_list() const;
  void clear_any_list();
  static const int kAnyListFieldNumber = 5;
  private:
  const ::tensorflow::CollectionDef_AnyList& _internal_any_list() const;
  public:
  const ::tensorflow::CollectionDef_AnyList& any_list() const;
  ::tensorflow::CollectionDef_AnyList* release_any_list();
  ::tensorflow::CollectionDef_AnyList* mutable_any_list();
  void set_allocated_any_list(::tensorflow::CollectionDef_AnyList* any_list);
  void unsafe_arena_set_allocated_any_list(
      ::tensorflow::CollectionDef_AnyList* any_list);
  ::tensorflow::CollectionDef_AnyList* unsafe_arena_release_any_list();

  void clear_kind();
  KindCase kind_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.CollectionDef)
 private:
  void set_has_node_list();
  void set_has_bytes_list();
  void set_has_int64_list();
  void set_has_float_list();
  void set_has_any_list();

  inline bool has_kind() const;
  inline void clear_has_kind();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  union KindUnion {
    KindUnion() {}
    ::tensorflow::CollectionDef_NodeList* node_list_;
    ::tensorflow::CollectionDef_BytesList* bytes_list_;
    ::tensorflow::CollectionDef_Int64List* int64_list_;
    ::tensorflow::CollectionDef_FloatList* float_list_;
    ::tensorflow::CollectionDef_AnyList* any_list_;
  } kind_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class TensorInfo_CooSparse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.TensorInfo.CooSparse) */ {
 public:
  TensorInfo_CooSparse();
  virtual ~TensorInfo_CooSparse();

  TensorInfo_CooSparse(const TensorInfo_CooSparse& from);

  inline TensorInfo_CooSparse& operator=(const TensorInfo_CooSparse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TensorInfo_CooSparse(TensorInfo_CooSparse&& from) noexcept
    : TensorInfo_CooSparse() {
    *this = ::std::move(from);
  }

  inline TensorInfo_CooSparse& operator=(TensorInfo_CooSparse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TensorInfo_CooSparse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TensorInfo_CooSparse* internal_default_instance() {
    return reinterpret_cast<const TensorInfo_CooSparse*>(
               &_TensorInfo_CooSparse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  void UnsafeArenaSwap(TensorInfo_CooSparse* other);
  void Swap(TensorInfo_CooSparse* other);
  friend void swap(TensorInfo_CooSparse& a, TensorInfo_CooSparse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TensorInfo_CooSparse* New() const final {
    return CreateMaybeMessage<TensorInfo_CooSparse>(NULL);
  }

  TensorInfo_CooSparse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TensorInfo_CooSparse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TensorInfo_CooSparse& from);
  void MergeFrom(const TensorInfo_CooSparse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorInfo_CooSparse* other);
  protected:
  explicit TensorInfo_CooSparse(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string values_tensor_name = 1;
  void clear_values_tensor_name();
  static const int kValuesTensorNameFieldNumber = 1;
  const ::std::string& values_tensor_name() const;
  void set_values_tensor_name(const ::std::string& value);
  #if LANG_CXX11
  void set_values_tensor_name(::std::string&& value);
  #endif
  void set_values_tensor_name(const char* value);
  void set_values_tensor_name(const char* value, size_t size);
  ::std::string* mutable_values_tensor_name();
  ::std::string* release_values_tensor_name();
  void set_allocated_values_tensor_name(::std::string* values_tensor_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_values_tensor_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_values_tensor_name(
      ::std::string* values_tensor_name);

  // string indices_tensor_name = 2;
  void clear_indices_tensor_name();
  static const int kIndicesTensorNameFieldNumber = 2;
  const ::std::string& indices_tensor_name() const;
  void set_indices_tensor_name(const ::std::string& value);
  #if LANG_CXX11
  void set_indices_tensor_name(::std::string&& value);
  #endif
  void set_indices_tensor_name(const char* value);
  void set_indices_tensor_name(const char* value, size_t size);
  ::std::string* mutable_indices_tensor_name();
  ::std::string* release_indices_tensor_name();
  void set_allocated_indices_tensor_name(::std::string* indices_tensor_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_indices_tensor_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_indices_tensor_name(
      ::std::string* indices_tensor_name);

  // string dense_shape_tensor_name = 3;
  void clear_dense_shape_tensor_name();
  static const int kDenseShapeTensorNameFieldNumber = 3;
  const ::std::string& dense_shape_tensor_name() const;
  void set_dense_shape_tensor_name(const ::std::string& value);
  #if LANG_CXX11
  void set_dense_shape_tensor_name(::std::string&& value);
  #endif
  void set_dense_shape_tensor_name(const char* value);
  void set_dense_shape_tensor_name(const char* value, size_t size);
  ::std::string* mutable_dense_shape_tensor_name();
  ::std::string* release_dense_shape_tensor_name();
  void set_allocated_dense_shape_tensor_name(::std::string* dense_shape_tensor_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_dense_shape_tensor_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_dense_shape_tensor_name(
      ::std::string* dense_shape_tensor_name);

  // @@protoc_insertion_point(class_scope:tensorflow.TensorInfo.CooSparse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr values_tensor_name_;
  ::google::protobuf::internal::ArenaStringPtr indices_tensor_name_;
  ::google::protobuf::internal::ArenaStringPtr dense_shape_tensor_name_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class TensorInfo : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.TensorInfo) */ {
 public:
  TensorInfo();
  virtual ~TensorInfo();

  TensorInfo(const TensorInfo& from);

  inline TensorInfo& operator=(const TensorInfo& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TensorInfo(TensorInfo&& from) noexcept
    : TensorInfo() {
    *this = ::std::move(from);
  }

  inline TensorInfo& operator=(TensorInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TensorInfo& default_instance();

  enum EncodingCase {
    kName = 1,
    kCooSparse = 4,
    ENCODING_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TensorInfo* internal_default_instance() {
    return reinterpret_cast<const TensorInfo*>(
               &_TensorInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  void UnsafeArenaSwap(TensorInfo* other);
  void Swap(TensorInfo* other);
  friend void swap(TensorInfo& a, TensorInfo& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TensorInfo* New() const final {
    return CreateMaybeMessage<TensorInfo>(NULL);
  }

  TensorInfo* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TensorInfo>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TensorInfo& from);
  void MergeFrom(const TensorInfo& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorInfo* other);
  protected:
  explicit TensorInfo(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef TensorInfo_CooSparse CooSparse;

  // accessors -------------------------------------------------------

  // .tensorflow.TensorShapeProto tensor_shape = 3;
  bool has_tensor_shape() const;
  void clear_tensor_shape();
  static const int kTensorShapeFieldNumber = 3;
  private:
  const ::tensorflow::TensorShapeProto& _internal_tensor_shape() const;
  public:
  const ::tensorflow::TensorShapeProto& tensor_shape() const;
  ::tensorflow::TensorShapeProto* release_tensor_shape();
  ::tensorflow::TensorShapeProto* mutable_tensor_shape();
  void set_allocated_tensor_shape(::tensorflow::TensorShapeProto* tensor_shape);
  void unsafe_arena_set_allocated_tensor_shape(
      ::tensorflow::TensorShapeProto* tensor_shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_tensor_shape();

  // .tensorflow.DataType dtype = 2;
  void clear_dtype();
  static const int kDtypeFieldNumber = 2;
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);

  // string name = 1;
  private:
  bool has_name() const;
  public:
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      ::std::string* name);

  // .tensorflow.TensorInfo.CooSparse coo_sparse = 4;
  bool has_coo_sparse() const;
  void clear_coo_sparse();
  static const int kCooSparseFieldNumber = 4;
  private:
  const ::tensorflow::TensorInfo_CooSparse& _internal_coo_sparse() const;
  public:
  const ::tensorflow::TensorInfo_CooSparse& coo_sparse() const;
  ::tensorflow::TensorInfo_CooSparse* release_coo_sparse();
  ::tensorflow::TensorInfo_CooSparse* mutable_coo_sparse();
  void set_allocated_coo_sparse(::tensorflow::TensorInfo_CooSparse* coo_sparse);
  void unsafe_arena_set_allocated_coo_sparse(
      ::tensorflow::TensorInfo_CooSparse* coo_sparse);
  ::tensorflow::TensorInfo_CooSparse* unsafe_arena_release_coo_sparse();

  void clear_encoding();
  EncodingCase encoding_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.TensorInfo)
 private:
  void set_has_name();
  void set_has_coo_sparse();

  inline bool has_encoding() const;
  inline void clear_has_encoding();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::tensorflow::TensorShapeProto* tensor_shape_;
  int dtype_;
  union EncodingUnion {
    EncodingUnion() {}
    ::google::protobuf::internal::ArenaStringPtr name_;
    ::tensorflow::TensorInfo_CooSparse* coo_sparse_;
  } encoding_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class SignatureDef_InputsEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<SignatureDef_InputsEntry_DoNotUse, 
    ::std::string, ::tensorflow::TensorInfo,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<SignatureDef_InputsEntry_DoNotUse, 
    ::std::string, ::tensorflow::TensorInfo,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  SignatureDef_InputsEntry_DoNotUse();
  SignatureDef_InputsEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const SignatureDef_InputsEntry_DoNotUse& other);
  static const SignatureDef_InputsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const SignatureDef_InputsEntry_DoNotUse*>(&_SignatureDef_InputsEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class SignatureDef_OutputsEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<SignatureDef_OutputsEntry_DoNotUse, 
    ::std::string, ::tensorflow::TensorInfo,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<SignatureDef_OutputsEntry_DoNotUse, 
    ::std::string, ::tensorflow::TensorInfo,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  SignatureDef_OutputsEntry_DoNotUse();
  SignatureDef_OutputsEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const SignatureDef_OutputsEntry_DoNotUse& other);
  static const SignatureDef_OutputsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const SignatureDef_OutputsEntry_DoNotUse*>(&_SignatureDef_OutputsEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class SignatureDef : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.SignatureDef) */ {
 public:
  SignatureDef();
  virtual ~SignatureDef();

  SignatureDef(const SignatureDef& from);

  inline SignatureDef& operator=(const SignatureDef& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SignatureDef(SignatureDef&& from) noexcept
    : SignatureDef() {
    *this = ::std::move(from);
  }

  inline SignatureDef& operator=(SignatureDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const SignatureDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SignatureDef* internal_default_instance() {
    return reinterpret_cast<const SignatureDef*>(
               &_SignatureDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  void UnsafeArenaSwap(SignatureDef* other);
  void Swap(SignatureDef* other);
  friend void swap(SignatureDef& a, SignatureDef& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SignatureDef* New() const final {
    return CreateMaybeMessage<SignatureDef>(NULL);
  }

  SignatureDef* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<SignatureDef>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const SignatureDef& from);
  void MergeFrom(const SignatureDef& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SignatureDef* other);
  protected:
  explicit SignatureDef(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<string, .tensorflow.TensorInfo> inputs = 1;
  int inputs_size() const;
  void clear_inputs();
  static const int kInputsFieldNumber = 1;
  const ::google::protobuf::Map< ::std::string, ::tensorflow::TensorInfo >&
      inputs() const;
  ::google::protobuf::Map< ::std::string, ::tensorflow::TensorInfo >*
      mutable_inputs();

  // map<string, .tensorflow.TensorInfo> outputs = 2;
  int outputs_size() const;
  void clear_outputs();
  static const int kOutputsFieldNumber = 2;
  const ::google::protobuf::Map< ::std::string, ::tensorflow::TensorInfo >&
      outputs() const;
  ::google::protobuf::Map< ::std::string, ::tensorflow::TensorInfo >*
      mutable_outputs();

  // string method_name = 3;
  void clear_method_name();
  static const int kMethodNameFieldNumber = 3;
  const ::std::string& method_name() const;
  void set_method_name(const ::std::string& value);
  #if LANG_CXX11
  void set_method_name(::std::string&& value);
  #endif
  void set_method_name(const char* value);
  void set_method_name(const char* value, size_t size);
  ::std::string* mutable_method_name();
  ::std::string* release_method_name();
  void set_allocated_method_name(::std::string* method_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_method_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_method_name(
      ::std::string* method_name);

  // @@protoc_insertion_point(class_scope:tensorflow.SignatureDef)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::MapField<
      SignatureDef_InputsEntry_DoNotUse,
      ::std::string, ::tensorflow::TensorInfo,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > inputs_;
  ::google::protobuf::internal::MapField<
      SignatureDef_OutputsEntry_DoNotUse,
      ::std::string, ::tensorflow::TensorInfo,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > outputs_;
  ::google::protobuf::internal::ArenaStringPtr method_name_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class AssetFileDef : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.AssetFileDef) */ {
 public:
  AssetFileDef();
  virtual ~AssetFileDef();

  AssetFileDef(const AssetFileDef& from);

  inline AssetFileDef& operator=(const AssetFileDef& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  AssetFileDef(AssetFileDef&& from) noexcept
    : AssetFileDef() {
    *this = ::std::move(from);
  }

  inline AssetFileDef& operator=(AssetFileDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const AssetFileDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AssetFileDef* internal_default_instance() {
    return reinterpret_cast<const AssetFileDef*>(
               &_AssetFileDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  void UnsafeArenaSwap(AssetFileDef* other);
  void Swap(AssetFileDef* other);
  friend void swap(AssetFileDef& a, AssetFileDef& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline AssetFileDef* New() const final {
    return CreateMaybeMessage<AssetFileDef>(NULL);
  }

  AssetFileDef* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<AssetFileDef>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const AssetFileDef& from);
  void MergeFrom(const AssetFileDef& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AssetFileDef* other);
  protected:
  explicit AssetFileDef(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string filename = 2;
  void clear_filename();
  static const int kFilenameFieldNumber = 2;
  const ::std::string& filename() const;
  void set_filename(const ::std::string& value);
  #if LANG_CXX11
  void set_filename(::std::string&& value);
  #endif
  void set_filename(const char* value);
  void set_filename(const char* value, size_t size);
  ::std::string* mutable_filename();
  ::std::string* release_filename();
  void set_allocated_filename(::std::string* filename);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_filename();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_filename(
      ::std::string* filename);

  // .tensorflow.TensorInfo tensor_info = 1;
  bool has_tensor_info() const;
  void clear_tensor_info();
  static const int kTensorInfoFieldNumber = 1;
  private:
  const ::tensorflow::TensorInfo& _internal_tensor_info() const;
  public:
  const ::tensorflow::TensorInfo& tensor_info() const;
  ::tensorflow::TensorInfo* release_tensor_info();
  ::tensorflow::TensorInfo* mutable_tensor_info();
  void set_allocated_tensor_info(::tensorflow::TensorInfo* tensor_info);
  void unsafe_arena_set_allocated_tensor_info(
      ::tensorflow::TensorInfo* tensor_info);
  ::tensorflow::TensorInfo* unsafe_arena_release_tensor_info();

  // @@protoc_insertion_point(class_scope:tensorflow.AssetFileDef)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr filename_;
  ::tensorflow::TensorInfo* tensor_info_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// MetaGraphDef_MetaInfoDef

// string meta_graph_version = 1;
inline void MetaGraphDef_MetaInfoDef::clear_meta_graph_version() {
  meta_graph_version_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& MetaGraphDef_MetaInfoDef::meta_graph_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.MetaGraphDef.MetaInfoDef.meta_graph_version)
  return meta_graph_version_.Get();
}
inline void MetaGraphDef_MetaInfoDef::set_meta_graph_version(const ::std::string& value) {
  
  meta_graph_version_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.MetaGraphDef.MetaInfoDef.meta_graph_version)
}
#if LANG_CXX11
inline void MetaGraphDef_MetaInfoDef::set_meta_graph_version(::std::string&& value) {
  
  meta_graph_version_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.MetaGraphDef.MetaInfoDef.meta_graph_version)
}
#endif
inline void MetaGraphDef_MetaInfoDef::set_meta_graph_version(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  meta_graph_version_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.MetaGraphDef.MetaInfoDef.meta_graph_version)
}
inline void MetaGraphDef_MetaInfoDef::set_meta_graph_version(const char* value,
    size_t size) {
  
  meta_graph_version_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.MetaGraphDef.MetaInfoDef.meta_graph_version)
}
inline ::std::string* MetaGraphDef_MetaInfoDef::mutable_meta_graph_version() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.MetaGraphDef.MetaInfoDef.meta_graph_version)
  return meta_graph_version_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* MetaGraphDef_MetaInfoDef::release_meta_graph_version() {
  // @@protoc_insertion_point(field_release:tensorflow.MetaGraphDef.MetaInfoDef.meta_graph_version)
  
  return meta_graph_version_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void MetaGraphDef_MetaInfoDef::set_allocated_meta_graph_version(::std::string* meta_graph_version) {
  if (meta_graph_version != NULL) {
    
  } else {
    
  }
  meta_graph_version_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), meta_graph_version,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MetaGraphDef.MetaInfoDef.meta_graph_version)
}
inline ::std::string* MetaGraphDef_MetaInfoDef::unsafe_arena_release_meta_graph_version() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MetaGraphDef.MetaInfoDef.meta_graph_version)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return meta_graph_version_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void MetaGraphDef_MetaInfoDef::unsafe_arena_set_allocated_meta_graph_version(
    ::std::string* meta_graph_version) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (meta_graph_version != NULL) {
    
  } else {
    
  }
  meta_graph_version_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      meta_graph_version, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MetaGraphDef.MetaInfoDef.meta_graph_version)
}

// .tensorflow.OpList stripped_op_list = 2;
inline bool MetaGraphDef_MetaInfoDef::has_stripped_op_list() const {
  return this != internal_default_instance() && stripped_op_list_ != NULL;
}
inline const ::tensorflow::OpList& MetaGraphDef_MetaInfoDef::_internal_stripped_op_list() const {
  return *stripped_op_list_;
}
inline const ::tensorflow::OpList& MetaGraphDef_MetaInfoDef::stripped_op_list() const {
  const ::tensorflow::OpList* p = stripped_op_list_;
  // @@protoc_insertion_point(field_get:tensorflow.MetaGraphDef.MetaInfoDef.stripped_op_list)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::OpList*>(
      &::tensorflow::_OpList_default_instance_);
}
inline ::tensorflow::OpList* MetaGraphDef_MetaInfoDef::release_stripped_op_list() {
  // @@protoc_insertion_point(field_release:tensorflow.MetaGraphDef.MetaInfoDef.stripped_op_list)
  
  ::tensorflow::OpList* temp = stripped_op_list_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  stripped_op_list_ = NULL;
  return temp;
}
inline ::tensorflow::OpList* MetaGraphDef_MetaInfoDef::unsafe_arena_release_stripped_op_list() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MetaGraphDef.MetaInfoDef.stripped_op_list)
  
  ::tensorflow::OpList* temp = stripped_op_list_;
  stripped_op_list_ = NULL;
  return temp;
}
inline ::tensorflow::OpList* MetaGraphDef_MetaInfoDef::mutable_stripped_op_list() {
  
  if (stripped_op_list_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::OpList>(GetArenaNoVirtual());
    stripped_op_list_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.MetaGraphDef.MetaInfoDef.stripped_op_list)
  return stripped_op_list_;
}
inline void MetaGraphDef_MetaInfoDef::set_allocated_stripped_op_list(::tensorflow::OpList* stripped_op_list) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(stripped_op_list_);
  }
  if (stripped_op_list) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(stripped_op_list)->GetArena();
    if (message_arena != submessage_arena) {
      stripped_op_list = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, stripped_op_list, submessage_arena);
    }
    
  } else {
    
  }
  stripped_op_list_ = stripped_op_list;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MetaGraphDef.MetaInfoDef.stripped_op_list)
}

// .google.protobuf.Any any_info = 3;
inline bool MetaGraphDef_MetaInfoDef::has_any_info() const {
  return this != internal_default_instance() && any_info_ != NULL;
}
inline const ::google::protobuf::Any& MetaGraphDef_MetaInfoDef::_internal_any_info() const {
  return *any_info_;
}
inline const ::google::protobuf::Any& MetaGraphDef_MetaInfoDef::any_info() const {
  const ::google::protobuf::Any* p = any_info_;
  // @@protoc_insertion_point(field_get:tensorflow.MetaGraphDef.MetaInfoDef.any_info)
  return p != NULL ? *p : *reinterpret_cast<const ::google::protobuf::Any*>(
      &::google::protobuf::_Any_default_instance_);
}
inline ::google::protobuf::Any* MetaGraphDef_MetaInfoDef::release_any_info() {
  // @@protoc_insertion_point(field_release:tensorflow.MetaGraphDef.MetaInfoDef.any_info)
  
  ::google::protobuf::Any* temp = any_info_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  any_info_ = NULL;
  return temp;
}
inline ::google::protobuf::Any* MetaGraphDef_MetaInfoDef::unsafe_arena_release_any_info() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MetaGraphDef.MetaInfoDef.any_info)
  
  ::google::protobuf::Any* temp = any_info_;
  any_info_ = NULL;
  return temp;
}
inline ::google::protobuf::Any* MetaGraphDef_MetaInfoDef::mutable_any_info() {
  
  if (any_info_ == NULL) {
    auto* p = CreateMaybeMessage<::google::protobuf::Any>(GetArenaNoVirtual());
    any_info_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.MetaGraphDef.MetaInfoDef.any_info)
  return any_info_;
}
inline void MetaGraphDef_MetaInfoDef::set_allocated_any_info(::google::protobuf::Any* any_info) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(any_info_);
  }
  if (any_info) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      any_info = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, any_info, submessage_arena);
    }
    
  } else {
    
  }
  any_info_ = any_info;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MetaGraphDef.MetaInfoDef.any_info)
}

// repeated string tags = 4;
inline int MetaGraphDef_MetaInfoDef::tags_size() const {
  return tags_.size();
}
inline void MetaGraphDef_MetaInfoDef::clear_tags() {
  tags_.Clear();
}
inline const ::std::string& MetaGraphDef_MetaInfoDef::tags(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.MetaGraphDef.MetaInfoDef.tags)
  return tags_.Get(index);
}
inline ::std::string* MetaGraphDef_MetaInfoDef::mutable_tags(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.MetaGraphDef.MetaInfoDef.tags)
  return tags_.Mutable(index);
}
inline void MetaGraphDef_MetaInfoDef::set_tags(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.MetaGraphDef.MetaInfoDef.tags)
  tags_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void MetaGraphDef_MetaInfoDef::set_tags(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.MetaGraphDef.MetaInfoDef.tags)
  tags_.Mutable(index)->assign(std::move(value));
}
#endif
inline void MetaGraphDef_MetaInfoDef::set_tags(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  tags_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.MetaGraphDef.MetaInfoDef.tags)
}
inline void MetaGraphDef_MetaInfoDef::set_tags(int index, const char* value, size_t size) {
  tags_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.MetaGraphDef.MetaInfoDef.tags)
}
inline ::std::string* MetaGraphDef_MetaInfoDef::add_tags() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.MetaGraphDef.MetaInfoDef.tags)
  return tags_.Add();
}
inline void MetaGraphDef_MetaInfoDef::add_tags(const ::std::string& value) {
  tags_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.MetaGraphDef.MetaInfoDef.tags)
}
#if LANG_CXX11
inline void MetaGraphDef_MetaInfoDef::add_tags(::std::string&& value) {
  tags_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.MetaGraphDef.MetaInfoDef.tags)
}
#endif
inline void MetaGraphDef_MetaInfoDef::add_tags(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  tags_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.MetaGraphDef.MetaInfoDef.tags)
}
inline void MetaGraphDef_MetaInfoDef::add_tags(const char* value, size_t size) {
  tags_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.MetaGraphDef.MetaInfoDef.tags)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
MetaGraphDef_MetaInfoDef::tags() const {
  // @@protoc_insertion_point(field_list:tensorflow.MetaGraphDef.MetaInfoDef.tags)
  return tags_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
MetaGraphDef_MetaInfoDef::mutable_tags() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.MetaGraphDef.MetaInfoDef.tags)
  return &tags_;
}

// string tensorflow_version = 5;
inline void MetaGraphDef_MetaInfoDef::clear_tensorflow_version() {
  tensorflow_version_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& MetaGraphDef_MetaInfoDef::tensorflow_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_version)
  return tensorflow_version_.Get();
}
inline void MetaGraphDef_MetaInfoDef::set_tensorflow_version(const ::std::string& value) {
  
  tensorflow_version_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_version)
}
#if LANG_CXX11
inline void MetaGraphDef_MetaInfoDef::set_tensorflow_version(::std::string&& value) {
  
  tensorflow_version_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_version)
}
#endif
inline void MetaGraphDef_MetaInfoDef::set_tensorflow_version(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  tensorflow_version_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_version)
}
inline void MetaGraphDef_MetaInfoDef::set_tensorflow_version(const char* value,
    size_t size) {
  
  tensorflow_version_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_version)
}
inline ::std::string* MetaGraphDef_MetaInfoDef::mutable_tensorflow_version() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_version)
  return tensorflow_version_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* MetaGraphDef_MetaInfoDef::release_tensorflow_version() {
  // @@protoc_insertion_point(field_release:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_version)
  
  return tensorflow_version_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void MetaGraphDef_MetaInfoDef::set_allocated_tensorflow_version(::std::string* tensorflow_version) {
  if (tensorflow_version != NULL) {
    
  } else {
    
  }
  tensorflow_version_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tensorflow_version,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_version)
}
inline ::std::string* MetaGraphDef_MetaInfoDef::unsafe_arena_release_tensorflow_version() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_version)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return tensorflow_version_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void MetaGraphDef_MetaInfoDef::unsafe_arena_set_allocated_tensorflow_version(
    ::std::string* tensorflow_version) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (tensorflow_version != NULL) {
    
  } else {
    
  }
  tensorflow_version_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      tensorflow_version, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_version)
}

// string tensorflow_git_version = 6;
inline void MetaGraphDef_MetaInfoDef::clear_tensorflow_git_version() {
  tensorflow_git_version_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& MetaGraphDef_MetaInfoDef::tensorflow_git_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_git_version)
  return tensorflow_git_version_.Get();
}
inline void MetaGraphDef_MetaInfoDef::set_tensorflow_git_version(const ::std::string& value) {
  
  tensorflow_git_version_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_git_version)
}
#if LANG_CXX11
inline void MetaGraphDef_MetaInfoDef::set_tensorflow_git_version(::std::string&& value) {
  
  tensorflow_git_version_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_git_version)
}
#endif
inline void MetaGraphDef_MetaInfoDef::set_tensorflow_git_version(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  tensorflow_git_version_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_git_version)
}
inline void MetaGraphDef_MetaInfoDef::set_tensorflow_git_version(const char* value,
    size_t size) {
  
  tensorflow_git_version_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_git_version)
}
inline ::std::string* MetaGraphDef_MetaInfoDef::mutable_tensorflow_git_version() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_git_version)
  return tensorflow_git_version_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* MetaGraphDef_MetaInfoDef::release_tensorflow_git_version() {
  // @@protoc_insertion_point(field_release:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_git_version)
  
  return tensorflow_git_version_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void MetaGraphDef_MetaInfoDef::set_allocated_tensorflow_git_version(::std::string* tensorflow_git_version) {
  if (tensorflow_git_version != NULL) {
    
  } else {
    
  }
  tensorflow_git_version_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tensorflow_git_version,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_git_version)
}
inline ::std::string* MetaGraphDef_MetaInfoDef::unsafe_arena_release_tensorflow_git_version() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_git_version)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return tensorflow_git_version_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void MetaGraphDef_MetaInfoDef::unsafe_arena_set_allocated_tensorflow_git_version(
    ::std::string* tensorflow_git_version) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (tensorflow_git_version != NULL) {
    
  } else {
    
  }
  tensorflow_git_version_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      tensorflow_git_version, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_git_version)
}

// bool stripped_default_attrs = 7;
inline void MetaGraphDef_MetaInfoDef::clear_stripped_default_attrs() {
  stripped_default_attrs_ = false;
}
inline bool MetaGraphDef_MetaInfoDef::stripped_default_attrs() const {
  // @@protoc_insertion_point(field_get:tensorflow.MetaGraphDef.MetaInfoDef.stripped_default_attrs)
  return stripped_default_attrs_;
}
inline void MetaGraphDef_MetaInfoDef::set_stripped_default_attrs(bool value) {
  
  stripped_default_attrs_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MetaGraphDef.MetaInfoDef.stripped_default_attrs)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// MetaGraphDef

// .tensorflow.MetaGraphDef.MetaInfoDef meta_info_def = 1;
inline bool MetaGraphDef::has_meta_info_def() const {
  return this != internal_default_instance() && meta_info_def_ != NULL;
}
inline void MetaGraphDef::clear_meta_info_def() {
  if (GetArenaNoVirtual() == NULL && meta_info_def_ != NULL) {
    delete meta_info_def_;
  }
  meta_info_def_ = NULL;
}
inline const ::tensorflow::MetaGraphDef_MetaInfoDef& MetaGraphDef::_internal_meta_info_def() const {
  return *meta_info_def_;
}
inline const ::tensorflow::MetaGraphDef_MetaInfoDef& MetaGraphDef::meta_info_def() const {
  const ::tensorflow::MetaGraphDef_MetaInfoDef* p = meta_info_def_;
  // @@protoc_insertion_point(field_get:tensorflow.MetaGraphDef.meta_info_def)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::MetaGraphDef_MetaInfoDef*>(
      &::tensorflow::_MetaGraphDef_MetaInfoDef_default_instance_);
}
inline ::tensorflow::MetaGraphDef_MetaInfoDef* MetaGraphDef::release_meta_info_def() {
  // @@protoc_insertion_point(field_release:tensorflow.MetaGraphDef.meta_info_def)
  
  ::tensorflow::MetaGraphDef_MetaInfoDef* temp = meta_info_def_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  meta_info_def_ = NULL;
  return temp;
}
inline ::tensorflow::MetaGraphDef_MetaInfoDef* MetaGraphDef::unsafe_arena_release_meta_info_def() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MetaGraphDef.meta_info_def)
  
  ::tensorflow::MetaGraphDef_MetaInfoDef* temp = meta_info_def_;
  meta_info_def_ = NULL;
  return temp;
}
inline ::tensorflow::MetaGraphDef_MetaInfoDef* MetaGraphDef::mutable_meta_info_def() {
  
  if (meta_info_def_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::MetaGraphDef_MetaInfoDef>(GetArenaNoVirtual());
    meta_info_def_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.MetaGraphDef.meta_info_def)
  return meta_info_def_;
}
inline void MetaGraphDef::set_allocated_meta_info_def(::tensorflow::MetaGraphDef_MetaInfoDef* meta_info_def) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete meta_info_def_;
  }
  if (meta_info_def) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(meta_info_def);
    if (message_arena != submessage_arena) {
      meta_info_def = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, meta_info_def, submessage_arena);
    }
    
  } else {
    
  }
  meta_info_def_ = meta_info_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MetaGraphDef.meta_info_def)
}

// .tensorflow.GraphDef graph_def = 2;
inline bool MetaGraphDef::has_graph_def() const {
  return this != internal_default_instance() && graph_def_ != NULL;
}
inline const ::tensorflow::GraphDef& MetaGraphDef::_internal_graph_def() const {
  return *graph_def_;
}
inline const ::tensorflow::GraphDef& MetaGraphDef::graph_def() const {
  const ::tensorflow::GraphDef* p = graph_def_;
  // @@protoc_insertion_point(field_get:tensorflow.MetaGraphDef.graph_def)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::GraphDef*>(
      &::tensorflow::_GraphDef_default_instance_);
}
inline ::tensorflow::GraphDef* MetaGraphDef::release_graph_def() {
  // @@protoc_insertion_point(field_release:tensorflow.MetaGraphDef.graph_def)
  
  ::tensorflow::GraphDef* temp = graph_def_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  graph_def_ = NULL;
  return temp;
}
inline ::tensorflow::GraphDef* MetaGraphDef::unsafe_arena_release_graph_def() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MetaGraphDef.graph_def)
  
  ::tensorflow::GraphDef* temp = graph_def_;
  graph_def_ = NULL;
  return temp;
}
inline ::tensorflow::GraphDef* MetaGraphDef::mutable_graph_def() {
  
  if (graph_def_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::GraphDef>(GetArenaNoVirtual());
    graph_def_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.MetaGraphDef.graph_def)
  return graph_def_;
}
inline void MetaGraphDef::set_allocated_graph_def(::tensorflow::GraphDef* graph_def) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(graph_def_);
  }
  if (graph_def) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(graph_def)->GetArena();
    if (message_arena != submessage_arena) {
      graph_def = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, graph_def, submessage_arena);
    }
    
  } else {
    
  }
  graph_def_ = graph_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MetaGraphDef.graph_def)
}

// .tensorflow.SaverDef saver_def = 3;
inline bool MetaGraphDef::has_saver_def() const {
  return this != internal_default_instance() && saver_def_ != NULL;
}
inline const ::tensorflow::SaverDef& MetaGraphDef::_internal_saver_def() const {
  return *saver_def_;
}
inline const ::tensorflow::SaverDef& MetaGraphDef::saver_def() const {
  const ::tensorflow::SaverDef* p = saver_def_;
  // @@protoc_insertion_point(field_get:tensorflow.MetaGraphDef.saver_def)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::SaverDef*>(
      &::tensorflow::_SaverDef_default_instance_);
}
inline ::tensorflow::SaverDef* MetaGraphDef::release_saver_def() {
  // @@protoc_insertion_point(field_release:tensorflow.MetaGraphDef.saver_def)
  
  ::tensorflow::SaverDef* temp = saver_def_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  saver_def_ = NULL;
  return temp;
}
inline ::tensorflow::SaverDef* MetaGraphDef::unsafe_arena_release_saver_def() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MetaGraphDef.saver_def)
  
  ::tensorflow::SaverDef* temp = saver_def_;
  saver_def_ = NULL;
  return temp;
}
inline ::tensorflow::SaverDef* MetaGraphDef::mutable_saver_def() {
  
  if (saver_def_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::SaverDef>(GetArenaNoVirtual());
    saver_def_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.MetaGraphDef.saver_def)
  return saver_def_;
}
inline void MetaGraphDef::set_allocated_saver_def(::tensorflow::SaverDef* saver_def) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(saver_def_);
  }
  if (saver_def) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(saver_def)->GetArena();
    if (message_arena != submessage_arena) {
      saver_def = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, saver_def, submessage_arena);
    }
    
  } else {
    
  }
  saver_def_ = saver_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MetaGraphDef.saver_def)
}

// map<string, .tensorflow.CollectionDef> collection_def = 4;
inline int MetaGraphDef::collection_def_size() const {
  return collection_def_.size();
}
inline void MetaGraphDef::clear_collection_def() {
  collection_def_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::tensorflow::CollectionDef >&
MetaGraphDef::collection_def() const {
  // @@protoc_insertion_point(field_map:tensorflow.MetaGraphDef.collection_def)
  return collection_def_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::tensorflow::CollectionDef >*
MetaGraphDef::mutable_collection_def() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.MetaGraphDef.collection_def)
  return collection_def_.MutableMap();
}

// map<string, .tensorflow.SignatureDef> signature_def = 5;
inline int MetaGraphDef::signature_def_size() const {
  return signature_def_.size();
}
inline void MetaGraphDef::clear_signature_def() {
  signature_def_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::tensorflow::SignatureDef >&
MetaGraphDef::signature_def() const {
  // @@protoc_insertion_point(field_map:tensorflow.MetaGraphDef.signature_def)
  return signature_def_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::tensorflow::SignatureDef >*
MetaGraphDef::mutable_signature_def() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.MetaGraphDef.signature_def)
  return signature_def_.MutableMap();
}

// repeated .tensorflow.AssetFileDef asset_file_def = 6;
inline int MetaGraphDef::asset_file_def_size() const {
  return asset_file_def_.size();
}
inline void MetaGraphDef::clear_asset_file_def() {
  asset_file_def_.Clear();
}
inline ::tensorflow::AssetFileDef* MetaGraphDef::mutable_asset_file_def(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.MetaGraphDef.asset_file_def)
  return asset_file_def_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::AssetFileDef >*
MetaGraphDef::mutable_asset_file_def() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.MetaGraphDef.asset_file_def)
  return &asset_file_def_;
}
inline const ::tensorflow::AssetFileDef& MetaGraphDef::asset_file_def(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.MetaGraphDef.asset_file_def)
  return asset_file_def_.Get(index);
}
inline ::tensorflow::AssetFileDef* MetaGraphDef::add_asset_file_def() {
  // @@protoc_insertion_point(field_add:tensorflow.MetaGraphDef.asset_file_def)
  return asset_file_def_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::AssetFileDef >&
MetaGraphDef::asset_file_def() const {
  // @@protoc_insertion_point(field_list:tensorflow.MetaGraphDef.asset_file_def)
  return asset_file_def_;
}

// .tensorflow.SavedObjectGraph object_graph_def = 7;
inline bool MetaGraphDef::has_object_graph_def() const {
  return this != internal_default_instance() && object_graph_def_ != NULL;
}
inline const ::tensorflow::SavedObjectGraph& MetaGraphDef::_internal_object_graph_def() const {
  return *object_graph_def_;
}
inline const ::tensorflow::SavedObjectGraph& MetaGraphDef::object_graph_def() const {
  const ::tensorflow::SavedObjectGraph* p = object_graph_def_;
  // @@protoc_insertion_point(field_get:tensorflow.MetaGraphDef.object_graph_def)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::SavedObjectGraph*>(
      &::tensorflow::_SavedObjectGraph_default_instance_);
}
inline ::tensorflow::SavedObjectGraph* MetaGraphDef::release_object_graph_def() {
  // @@protoc_insertion_point(field_release:tensorflow.MetaGraphDef.object_graph_def)
  
  ::tensorflow::SavedObjectGraph* temp = object_graph_def_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  object_graph_def_ = NULL;
  return temp;
}
inline ::tensorflow::SavedObjectGraph* MetaGraphDef::unsafe_arena_release_object_graph_def() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MetaGraphDef.object_graph_def)
  
  ::tensorflow::SavedObjectGraph* temp = object_graph_def_;
  object_graph_def_ = NULL;
  return temp;
}
inline ::tensorflow::SavedObjectGraph* MetaGraphDef::mutable_object_graph_def() {
  
  if (object_graph_def_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::SavedObjectGraph>(GetArenaNoVirtual());
    object_graph_def_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.MetaGraphDef.object_graph_def)
  return object_graph_def_;
}
inline void MetaGraphDef::set_allocated_object_graph_def(::tensorflow::SavedObjectGraph* object_graph_def) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(object_graph_def_);
  }
  if (object_graph_def) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(object_graph_def)->GetArena();
    if (message_arena != submessage_arena) {
      object_graph_def = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, object_graph_def, submessage_arena);
    }
    
  } else {
    
  }
  object_graph_def_ = object_graph_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MetaGraphDef.object_graph_def)
}

// -------------------------------------------------------------------

// CollectionDef_NodeList

// repeated string value = 1;
inline int CollectionDef_NodeList::value_size() const {
  return value_.size();
}
inline void CollectionDef_NodeList::clear_value() {
  value_.Clear();
}
inline const ::std::string& CollectionDef_NodeList::value(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CollectionDef.NodeList.value)
  return value_.Get(index);
}
inline ::std::string* CollectionDef_NodeList::mutable_value(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CollectionDef.NodeList.value)
  return value_.Mutable(index);
}
inline void CollectionDef_NodeList::set_value(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.CollectionDef.NodeList.value)
  value_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void CollectionDef_NodeList::set_value(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.CollectionDef.NodeList.value)
  value_.Mutable(index)->assign(std::move(value));
}
#endif
inline void CollectionDef_NodeList::set_value(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  value_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.CollectionDef.NodeList.value)
}
inline void CollectionDef_NodeList::set_value(int index, const char* value, size_t size) {
  value_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CollectionDef.NodeList.value)
}
inline ::std::string* CollectionDef_NodeList::add_value() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.CollectionDef.NodeList.value)
  return value_.Add();
}
inline void CollectionDef_NodeList::add_value(const ::std::string& value) {
  value_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.CollectionDef.NodeList.value)
}
#if LANG_CXX11
inline void CollectionDef_NodeList::add_value(::std::string&& value) {
  value_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.CollectionDef.NodeList.value)
}
#endif
inline void CollectionDef_NodeList::add_value(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  value_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.CollectionDef.NodeList.value)
}
inline void CollectionDef_NodeList::add_value(const char* value, size_t size) {
  value_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.CollectionDef.NodeList.value)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
CollectionDef_NodeList::value() const {
  // @@protoc_insertion_point(field_list:tensorflow.CollectionDef.NodeList.value)
  return value_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
CollectionDef_NodeList::mutable_value() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CollectionDef.NodeList.value)
  return &value_;
}

// -------------------------------------------------------------------

// CollectionDef_BytesList

// repeated bytes value = 1;
inline int CollectionDef_BytesList::value_size() const {
  return value_.size();
}
inline void CollectionDef_BytesList::clear_value() {
  value_.Clear();
}
inline const ::std::string& CollectionDef_BytesList::value(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CollectionDef.BytesList.value)
  return value_.Get(index);
}
inline ::std::string* CollectionDef_BytesList::mutable_value(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CollectionDef.BytesList.value)
  return value_.Mutable(index);
}
inline void CollectionDef_BytesList::set_value(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.CollectionDef.BytesList.value)
  value_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void CollectionDef_BytesList::set_value(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.CollectionDef.BytesList.value)
  value_.Mutable(index)->assign(std::move(value));
}
#endif
inline void CollectionDef_BytesList::set_value(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  value_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.CollectionDef.BytesList.value)
}
inline void CollectionDef_BytesList::set_value(int index, const void* value, size_t size) {
  value_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CollectionDef.BytesList.value)
}
inline ::std::string* CollectionDef_BytesList::add_value() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.CollectionDef.BytesList.value)
  return value_.Add();
}
inline void CollectionDef_BytesList::add_value(const ::std::string& value) {
  value_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.CollectionDef.BytesList.value)
}
#if LANG_CXX11
inline void CollectionDef_BytesList::add_value(::std::string&& value) {
  value_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.CollectionDef.BytesList.value)
}
#endif
inline void CollectionDef_BytesList::add_value(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  value_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.CollectionDef.BytesList.value)
}
inline void CollectionDef_BytesList::add_value(const void* value, size_t size) {
  value_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.CollectionDef.BytesList.value)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
CollectionDef_BytesList::value() const {
  // @@protoc_insertion_point(field_list:tensorflow.CollectionDef.BytesList.value)
  return value_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
CollectionDef_BytesList::mutable_value() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CollectionDef.BytesList.value)
  return &value_;
}

// -------------------------------------------------------------------

// CollectionDef_Int64List

// repeated int64 value = 1 [packed = true];
inline int CollectionDef_Int64List::value_size() const {
  return value_.size();
}
inline void CollectionDef_Int64List::clear_value() {
  value_.Clear();
}
inline ::google::protobuf::int64 CollectionDef_Int64List::value(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CollectionDef.Int64List.value)
  return value_.Get(index);
}
inline void CollectionDef_Int64List::set_value(int index, ::google::protobuf::int64 value) {
  value_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.CollectionDef.Int64List.value)
}
inline void CollectionDef_Int64List::add_value(::google::protobuf::int64 value) {
  value_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.CollectionDef.Int64List.value)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
CollectionDef_Int64List::value() const {
  // @@protoc_insertion_point(field_list:tensorflow.CollectionDef.Int64List.value)
  return value_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
CollectionDef_Int64List::mutable_value() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CollectionDef.Int64List.value)
  return &value_;
}

// -------------------------------------------------------------------

// CollectionDef_FloatList

// repeated float value = 1 [packed = true];
inline int CollectionDef_FloatList::value_size() const {
  return value_.size();
}
inline void CollectionDef_FloatList::clear_value() {
  value_.Clear();
}
inline float CollectionDef_FloatList::value(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CollectionDef.FloatList.value)
  return value_.Get(index);
}
inline void CollectionDef_FloatList::set_value(int index, float value) {
  value_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.CollectionDef.FloatList.value)
}
inline void CollectionDef_FloatList::add_value(float value) {
  value_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.CollectionDef.FloatList.value)
}
inline const ::google::protobuf::RepeatedField< float >&
CollectionDef_FloatList::value() const {
  // @@protoc_insertion_point(field_list:tensorflow.CollectionDef.FloatList.value)
  return value_;
}
inline ::google::protobuf::RepeatedField< float >*
CollectionDef_FloatList::mutable_value() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CollectionDef.FloatList.value)
  return &value_;
}

// -------------------------------------------------------------------

// CollectionDef_AnyList

// repeated .google.protobuf.Any value = 1;
inline int CollectionDef_AnyList::value_size() const {
  return value_.size();
}
inline ::google::protobuf::Any* CollectionDef_AnyList::mutable_value(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CollectionDef.AnyList.value)
  return value_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::google::protobuf::Any >*
CollectionDef_AnyList::mutable_value() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CollectionDef.AnyList.value)
  return &value_;
}
inline const ::google::protobuf::Any& CollectionDef_AnyList::value(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CollectionDef.AnyList.value)
  return value_.Get(index);
}
inline ::google::protobuf::Any* CollectionDef_AnyList::add_value() {
  // @@protoc_insertion_point(field_add:tensorflow.CollectionDef.AnyList.value)
  return value_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::google::protobuf::Any >&
CollectionDef_AnyList::value() const {
  // @@protoc_insertion_point(field_list:tensorflow.CollectionDef.AnyList.value)
  return value_;
}

// -------------------------------------------------------------------

// CollectionDef

// .tensorflow.CollectionDef.NodeList node_list = 1;
inline bool CollectionDef::has_node_list() const {
  return kind_case() == kNodeList;
}
inline void CollectionDef::set_has_node_list() {
  _oneof_case_[0] = kNodeList;
}
inline void CollectionDef::clear_node_list() {
  if (has_node_list()) {
    if (GetArenaNoVirtual() == NULL) {
      delete kind_.node_list_;
    }
    clear_has_kind();
  }
}
inline const ::tensorflow::CollectionDef_NodeList& CollectionDef::_internal_node_list() const {
  return *kind_.node_list_;
}
inline ::tensorflow::CollectionDef_NodeList* CollectionDef::release_node_list() {
  // @@protoc_insertion_point(field_release:tensorflow.CollectionDef.node_list)
  if (has_node_list()) {
    clear_has_kind();
      ::tensorflow::CollectionDef_NodeList* temp = kind_.node_list_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    kind_.node_list_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::CollectionDef_NodeList& CollectionDef::node_list() const {
  // @@protoc_insertion_point(field_get:tensorflow.CollectionDef.node_list)
  return has_node_list()
      ? *kind_.node_list_
      : *reinterpret_cast< ::tensorflow::CollectionDef_NodeList*>(&::tensorflow::_CollectionDef_NodeList_default_instance_);
}
inline ::tensorflow::CollectionDef_NodeList* CollectionDef::unsafe_arena_release_node_list() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CollectionDef.node_list)
  if (has_node_list()) {
    clear_has_kind();
    ::tensorflow::CollectionDef_NodeList* temp = kind_.node_list_;
    kind_.node_list_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void CollectionDef::unsafe_arena_set_allocated_node_list(::tensorflow::CollectionDef_NodeList* node_list) {
  clear_kind();
  if (node_list) {
    set_has_node_list();
    kind_.node_list_ = node_list;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CollectionDef.node_list)
}
inline ::tensorflow::CollectionDef_NodeList* CollectionDef::mutable_node_list() {
  if (!has_node_list()) {
    clear_kind();
    set_has_node_list();
    kind_.node_list_ = CreateMaybeMessage< ::tensorflow::CollectionDef_NodeList >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.CollectionDef.node_list)
  return kind_.node_list_;
}

// .tensorflow.CollectionDef.BytesList bytes_list = 2;
inline bool CollectionDef::has_bytes_list() const {
  return kind_case() == kBytesList;
}
inline void CollectionDef::set_has_bytes_list() {
  _oneof_case_[0] = kBytesList;
}
inline void CollectionDef::clear_bytes_list() {
  if (has_bytes_list()) {
    if (GetArenaNoVirtual() == NULL) {
      delete kind_.bytes_list_;
    }
    clear_has_kind();
  }
}
inline const ::tensorflow::CollectionDef_BytesList& CollectionDef::_internal_bytes_list() const {
  return *kind_.bytes_list_;
}
inline ::tensorflow::CollectionDef_BytesList* CollectionDef::release_bytes_list() {
  // @@protoc_insertion_point(field_release:tensorflow.CollectionDef.bytes_list)
  if (has_bytes_list()) {
    clear_has_kind();
      ::tensorflow::CollectionDef_BytesList* temp = kind_.bytes_list_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    kind_.bytes_list_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::CollectionDef_BytesList& CollectionDef::bytes_list() const {
  // @@protoc_insertion_point(field_get:tensorflow.CollectionDef.bytes_list)
  return has_bytes_list()
      ? *kind_.bytes_list_
      : *reinterpret_cast< ::tensorflow::CollectionDef_BytesList*>(&::tensorflow::_CollectionDef_BytesList_default_instance_);
}
inline ::tensorflow::CollectionDef_BytesList* CollectionDef::unsafe_arena_release_bytes_list() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CollectionDef.bytes_list)
  if (has_bytes_list()) {
    clear_has_kind();
    ::tensorflow::CollectionDef_BytesList* temp = kind_.bytes_list_;
    kind_.bytes_list_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void CollectionDef::unsafe_arena_set_allocated_bytes_list(::tensorflow::CollectionDef_BytesList* bytes_list) {
  clear_kind();
  if (bytes_list) {
    set_has_bytes_list();
    kind_.bytes_list_ = bytes_list;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CollectionDef.bytes_list)
}
inline ::tensorflow::CollectionDef_BytesList* CollectionDef::mutable_bytes_list() {
  if (!has_bytes_list()) {
    clear_kind();
    set_has_bytes_list();
    kind_.bytes_list_ = CreateMaybeMessage< ::tensorflow::CollectionDef_BytesList >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.CollectionDef.bytes_list)
  return kind_.bytes_list_;
}

// .tensorflow.CollectionDef.Int64List int64_list = 3;
inline bool CollectionDef::has_int64_list() const {
  return kind_case() == kInt64List;
}
inline void CollectionDef::set_has_int64_list() {
  _oneof_case_[0] = kInt64List;
}
inline void CollectionDef::clear_int64_list() {
  if (has_int64_list()) {
    if (GetArenaNoVirtual() == NULL) {
      delete kind_.int64_list_;
    }
    clear_has_kind();
  }
}
inline const ::tensorflow::CollectionDef_Int64List& CollectionDef::_internal_int64_list() const {
  return *kind_.int64_list_;
}
inline ::tensorflow::CollectionDef_Int64List* CollectionDef::release_int64_list() {
  // @@protoc_insertion_point(field_release:tensorflow.CollectionDef.int64_list)
  if (has_int64_list()) {
    clear_has_kind();
      ::tensorflow::CollectionDef_Int64List* temp = kind_.int64_list_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    kind_.int64_list_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::CollectionDef_Int64List& CollectionDef::int64_list() const {
  // @@protoc_insertion_point(field_get:tensorflow.CollectionDef.int64_list)
  return has_int64_list()
      ? *kind_.int64_list_
      : *reinterpret_cast< ::tensorflow::CollectionDef_Int64List*>(&::tensorflow::_CollectionDef_Int64List_default_instance_);
}
inline ::tensorflow::CollectionDef_Int64List* CollectionDef::unsafe_arena_release_int64_list() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CollectionDef.int64_list)
  if (has_int64_list()) {
    clear_has_kind();
    ::tensorflow::CollectionDef_Int64List* temp = kind_.int64_list_;
    kind_.int64_list_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void CollectionDef::unsafe_arena_set_allocated_int64_list(::tensorflow::CollectionDef_Int64List* int64_list) {
  clear_kind();
  if (int64_list) {
    set_has_int64_list();
    kind_.int64_list_ = int64_list;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CollectionDef.int64_list)
}
inline ::tensorflow::CollectionDef_Int64List* CollectionDef::mutable_int64_list() {
  if (!has_int64_list()) {
    clear_kind();
    set_has_int64_list();
    kind_.int64_list_ = CreateMaybeMessage< ::tensorflow::CollectionDef_Int64List >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.CollectionDef.int64_list)
  return kind_.int64_list_;
}

// .tensorflow.CollectionDef.FloatList float_list = 4;
inline bool CollectionDef::has_float_list() const {
  return kind_case() == kFloatList;
}
inline void CollectionDef::set_has_float_list() {
  _oneof_case_[0] = kFloatList;
}
inline void CollectionDef::clear_float_list() {
  if (has_float_list()) {
    if (GetArenaNoVirtual() == NULL) {
      delete kind_.float_list_;
    }
    clear_has_kind();
  }
}
inline const ::tensorflow::CollectionDef_FloatList& CollectionDef::_internal_float_list() const {
  return *kind_.float_list_;
}
inline ::tensorflow::CollectionDef_FloatList* CollectionDef::release_float_list() {
  // @@protoc_insertion_point(field_release:tensorflow.CollectionDef.float_list)
  if (has_float_list()) {
    clear_has_kind();
      ::tensorflow::CollectionDef_FloatList* temp = kind_.float_list_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    kind_.float_list_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::CollectionDef_FloatList& CollectionDef::float_list() const {
  // @@protoc_insertion_point(field_get:tensorflow.CollectionDef.float_list)
  return has_float_list()
      ? *kind_.float_list_
      : *reinterpret_cast< ::tensorflow::CollectionDef_FloatList*>(&::tensorflow::_CollectionDef_FloatList_default_instance_);
}
inline ::tensorflow::CollectionDef_FloatList* CollectionDef::unsafe_arena_release_float_list() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CollectionDef.float_list)
  if (has_float_list()) {
    clear_has_kind();
    ::tensorflow::CollectionDef_FloatList* temp = kind_.float_list_;
    kind_.float_list_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void CollectionDef::unsafe_arena_set_allocated_float_list(::tensorflow::CollectionDef_FloatList* float_list) {
  clear_kind();
  if (float_list) {
    set_has_float_list();
    kind_.float_list_ = float_list;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CollectionDef.float_list)
}
inline ::tensorflow::CollectionDef_FloatList* CollectionDef::mutable_float_list() {
  if (!has_float_list()) {
    clear_kind();
    set_has_float_list();
    kind_.float_list_ = CreateMaybeMessage< ::tensorflow::CollectionDef_FloatList >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.CollectionDef.float_list)
  return kind_.float_list_;
}

// .tensorflow.CollectionDef.AnyList any_list = 5;
inline bool CollectionDef::has_any_list() const {
  return kind_case() == kAnyList;
}
inline void CollectionDef::set_has_any_list() {
  _oneof_case_[0] = kAnyList;
}
inline void CollectionDef::clear_any_list() {
  if (has_any_list()) {
    if (GetArenaNoVirtual() == NULL) {
      delete kind_.any_list_;
    }
    clear_has_kind();
  }
}
inline const ::tensorflow::CollectionDef_AnyList& CollectionDef::_internal_any_list() const {
  return *kind_.any_list_;
}
inline ::tensorflow::CollectionDef_AnyList* CollectionDef::release_any_list() {
  // @@protoc_insertion_point(field_release:tensorflow.CollectionDef.any_list)
  if (has_any_list()) {
    clear_has_kind();
      ::tensorflow::CollectionDef_AnyList* temp = kind_.any_list_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    kind_.any_list_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::CollectionDef_AnyList& CollectionDef::any_list() const {
  // @@protoc_insertion_point(field_get:tensorflow.CollectionDef.any_list)
  return has_any_list()
      ? *kind_.any_list_
      : *reinterpret_cast< ::tensorflow::CollectionDef_AnyList*>(&::tensorflow::_CollectionDef_AnyList_default_instance_);
}
inline ::tensorflow::CollectionDef_AnyList* CollectionDef::unsafe_arena_release_any_list() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CollectionDef.any_list)
  if (has_any_list()) {
    clear_has_kind();
    ::tensorflow::CollectionDef_AnyList* temp = kind_.any_list_;
    kind_.any_list_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void CollectionDef::unsafe_arena_set_allocated_any_list(::tensorflow::CollectionDef_AnyList* any_list) {
  clear_kind();
  if (any_list) {
    set_has_any_list();
    kind_.any_list_ = any_list;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CollectionDef.any_list)
}
inline ::tensorflow::CollectionDef_AnyList* CollectionDef::mutable_any_list() {
  if (!has_any_list()) {
    clear_kind();
    set_has_any_list();
    kind_.any_list_ = CreateMaybeMessage< ::tensorflow::CollectionDef_AnyList >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.CollectionDef.any_list)
  return kind_.any_list_;
}

inline bool CollectionDef::has_kind() const {
  return kind_case() != KIND_NOT_SET;
}
inline void CollectionDef::clear_has_kind() {
  _oneof_case_[0] = KIND_NOT_SET;
}
inline CollectionDef::KindCase CollectionDef::kind_case() const {
  return CollectionDef::KindCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// TensorInfo_CooSparse

// string values_tensor_name = 1;
inline void TensorInfo_CooSparse::clear_values_tensor_name() {
  values_tensor_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& TensorInfo_CooSparse::values_tensor_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorInfo.CooSparse.values_tensor_name)
  return values_tensor_name_.Get();
}
inline void TensorInfo_CooSparse::set_values_tensor_name(const ::std::string& value) {
  
  values_tensor_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.TensorInfo.CooSparse.values_tensor_name)
}
#if LANG_CXX11
inline void TensorInfo_CooSparse::set_values_tensor_name(::std::string&& value) {
  
  values_tensor_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TensorInfo.CooSparse.values_tensor_name)
}
#endif
inline void TensorInfo_CooSparse::set_values_tensor_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  values_tensor_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.TensorInfo.CooSparse.values_tensor_name)
}
inline void TensorInfo_CooSparse::set_values_tensor_name(const char* value,
    size_t size) {
  
  values_tensor_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TensorInfo.CooSparse.values_tensor_name)
}
inline ::std::string* TensorInfo_CooSparse::mutable_values_tensor_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorInfo.CooSparse.values_tensor_name)
  return values_tensor_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* TensorInfo_CooSparse::release_values_tensor_name() {
  // @@protoc_insertion_point(field_release:tensorflow.TensorInfo.CooSparse.values_tensor_name)
  
  return values_tensor_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TensorInfo_CooSparse::set_allocated_values_tensor_name(::std::string* values_tensor_name) {
  if (values_tensor_name != NULL) {
    
  } else {
    
  }
  values_tensor_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), values_tensor_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TensorInfo.CooSparse.values_tensor_name)
}
inline ::std::string* TensorInfo_CooSparse::unsafe_arena_release_values_tensor_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TensorInfo.CooSparse.values_tensor_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return values_tensor_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TensorInfo_CooSparse::unsafe_arena_set_allocated_values_tensor_name(
    ::std::string* values_tensor_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (values_tensor_name != NULL) {
    
  } else {
    
  }
  values_tensor_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      values_tensor_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TensorInfo.CooSparse.values_tensor_name)
}

// string indices_tensor_name = 2;
inline void TensorInfo_CooSparse::clear_indices_tensor_name() {
  indices_tensor_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& TensorInfo_CooSparse::indices_tensor_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorInfo.CooSparse.indices_tensor_name)
  return indices_tensor_name_.Get();
}
inline void TensorInfo_CooSparse::set_indices_tensor_name(const ::std::string& value) {
  
  indices_tensor_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.TensorInfo.CooSparse.indices_tensor_name)
}
#if LANG_CXX11
inline void TensorInfo_CooSparse::set_indices_tensor_name(::std::string&& value) {
  
  indices_tensor_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TensorInfo.CooSparse.indices_tensor_name)
}
#endif
inline void TensorInfo_CooSparse::set_indices_tensor_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  indices_tensor_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.TensorInfo.CooSparse.indices_tensor_name)
}
inline void TensorInfo_CooSparse::set_indices_tensor_name(const char* value,
    size_t size) {
  
  indices_tensor_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TensorInfo.CooSparse.indices_tensor_name)
}
inline ::std::string* TensorInfo_CooSparse::mutable_indices_tensor_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorInfo.CooSparse.indices_tensor_name)
  return indices_tensor_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* TensorInfo_CooSparse::release_indices_tensor_name() {
  // @@protoc_insertion_point(field_release:tensorflow.TensorInfo.CooSparse.indices_tensor_name)
  
  return indices_tensor_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TensorInfo_CooSparse::set_allocated_indices_tensor_name(::std::string* indices_tensor_name) {
  if (indices_tensor_name != NULL) {
    
  } else {
    
  }
  indices_tensor_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), indices_tensor_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TensorInfo.CooSparse.indices_tensor_name)
}
inline ::std::string* TensorInfo_CooSparse::unsafe_arena_release_indices_tensor_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TensorInfo.CooSparse.indices_tensor_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return indices_tensor_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TensorInfo_CooSparse::unsafe_arena_set_allocated_indices_tensor_name(
    ::std::string* indices_tensor_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (indices_tensor_name != NULL) {
    
  } else {
    
  }
  indices_tensor_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      indices_tensor_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TensorInfo.CooSparse.indices_tensor_name)
}

// string dense_shape_tensor_name = 3;
inline void TensorInfo_CooSparse::clear_dense_shape_tensor_name() {
  dense_shape_tensor_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& TensorInfo_CooSparse::dense_shape_tensor_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorInfo.CooSparse.dense_shape_tensor_name)
  return dense_shape_tensor_name_.Get();
}
inline void TensorInfo_CooSparse::set_dense_shape_tensor_name(const ::std::string& value) {
  
  dense_shape_tensor_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.TensorInfo.CooSparse.dense_shape_tensor_name)
}
#if LANG_CXX11
inline void TensorInfo_CooSparse::set_dense_shape_tensor_name(::std::string&& value) {
  
  dense_shape_tensor_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TensorInfo.CooSparse.dense_shape_tensor_name)
}
#endif
inline void TensorInfo_CooSparse::set_dense_shape_tensor_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  dense_shape_tensor_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.TensorInfo.CooSparse.dense_shape_tensor_name)
}
inline void TensorInfo_CooSparse::set_dense_shape_tensor_name(const char* value,
    size_t size) {
  
  dense_shape_tensor_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TensorInfo.CooSparse.dense_shape_tensor_name)
}
inline ::std::string* TensorInfo_CooSparse::mutable_dense_shape_tensor_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorInfo.CooSparse.dense_shape_tensor_name)
  return dense_shape_tensor_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* TensorInfo_CooSparse::release_dense_shape_tensor_name() {
  // @@protoc_insertion_point(field_release:tensorflow.TensorInfo.CooSparse.dense_shape_tensor_name)
  
  return dense_shape_tensor_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TensorInfo_CooSparse::set_allocated_dense_shape_tensor_name(::std::string* dense_shape_tensor_name) {
  if (dense_shape_tensor_name != NULL) {
    
  } else {
    
  }
  dense_shape_tensor_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), dense_shape_tensor_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TensorInfo.CooSparse.dense_shape_tensor_name)
}
inline ::std::string* TensorInfo_CooSparse::unsafe_arena_release_dense_shape_tensor_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TensorInfo.CooSparse.dense_shape_tensor_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return dense_shape_tensor_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TensorInfo_CooSparse::unsafe_arena_set_allocated_dense_shape_tensor_name(
    ::std::string* dense_shape_tensor_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (dense_shape_tensor_name != NULL) {
    
  } else {
    
  }
  dense_shape_tensor_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      dense_shape_tensor_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TensorInfo.CooSparse.dense_shape_tensor_name)
}

// -------------------------------------------------------------------

// TensorInfo

// string name = 1;
inline bool TensorInfo::has_name() const {
  return encoding_case() == kName;
}
inline void TensorInfo::set_has_name() {
  _oneof_case_[0] = kName;
}
inline void TensorInfo::clear_name() {
  if (has_name()) {
    encoding_.name_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
    clear_has_encoding();
  }
}
inline const ::std::string& TensorInfo::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorInfo.name)
  if (has_name()) {
    return encoding_.name_.Get();
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void TensorInfo::set_name(const ::std::string& value) {
  if (!has_name()) {
    clear_encoding();
    set_has_name();
    encoding_.name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  encoding_.name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.TensorInfo.name)
}
#if LANG_CXX11
inline void TensorInfo::set_name(::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.TensorInfo.name)
  if (!has_name()) {
    clear_encoding();
    set_has_name();
    encoding_.name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  encoding_.name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TensorInfo.name)
}
#endif
inline void TensorInfo::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  if (!has_name()) {
    clear_encoding();
    set_has_name();
    encoding_.name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  encoding_.name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.TensorInfo.name)
}
inline void TensorInfo::set_name(const char* value,
                             size_t size) {
  if (!has_name()) {
    clear_encoding();
    set_has_name();
    encoding_.name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  encoding_.name_.Set(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TensorInfo.name)
}
inline ::std::string* TensorInfo::mutable_name() {
  if (!has_name()) {
    clear_encoding();
    set_has_name();
    encoding_.name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  return encoding_.name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorInfo.name)
}
inline ::std::string* TensorInfo::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.TensorInfo.name)
  if (has_name()) {
    clear_has_encoding();
    return encoding_.name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        GetArenaNoVirtual());
  } else {
    return NULL;
  }
}
inline void TensorInfo::set_allocated_name(::std::string* name) {
  if (!has_name()) {
    encoding_.name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_encoding();
  if (name != NULL) {
    set_has_name();
    encoding_.name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name,
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TensorInfo.name)
}
inline ::std::string* TensorInfo::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TensorInfo.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (has_name()) {
    clear_has_encoding();
    return encoding_.name_.UnsafeArenaRelease(
        &::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  } else {
    return NULL;
  }
}
inline void TensorInfo::unsafe_arena_set_allocated_name(::std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (!has_name()) {
    encoding_.name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_encoding();
  if (name) {
    set_has_name();
    encoding_.name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name, GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TensorInfo.name)
}

// .tensorflow.TensorInfo.CooSparse coo_sparse = 4;
inline bool TensorInfo::has_coo_sparse() const {
  return encoding_case() == kCooSparse;
}
inline void TensorInfo::set_has_coo_sparse() {
  _oneof_case_[0] = kCooSparse;
}
inline void TensorInfo::clear_coo_sparse() {
  if (has_coo_sparse()) {
    if (GetArenaNoVirtual() == NULL) {
      delete encoding_.coo_sparse_;
    }
    clear_has_encoding();
  }
}
inline const ::tensorflow::TensorInfo_CooSparse& TensorInfo::_internal_coo_sparse() const {
  return *encoding_.coo_sparse_;
}
inline ::tensorflow::TensorInfo_CooSparse* TensorInfo::release_coo_sparse() {
  // @@protoc_insertion_point(field_release:tensorflow.TensorInfo.coo_sparse)
  if (has_coo_sparse()) {
    clear_has_encoding();
      ::tensorflow::TensorInfo_CooSparse* temp = encoding_.coo_sparse_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    encoding_.coo_sparse_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::TensorInfo_CooSparse& TensorInfo::coo_sparse() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorInfo.coo_sparse)
  return has_coo_sparse()
      ? *encoding_.coo_sparse_
      : *reinterpret_cast< ::tensorflow::TensorInfo_CooSparse*>(&::tensorflow::_TensorInfo_CooSparse_default_instance_);
}
inline ::tensorflow::TensorInfo_CooSparse* TensorInfo::unsafe_arena_release_coo_sparse() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TensorInfo.coo_sparse)
  if (has_coo_sparse()) {
    clear_has_encoding();
    ::tensorflow::TensorInfo_CooSparse* temp = encoding_.coo_sparse_;
    encoding_.coo_sparse_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void TensorInfo::unsafe_arena_set_allocated_coo_sparse(::tensorflow::TensorInfo_CooSparse* coo_sparse) {
  clear_encoding();
  if (coo_sparse) {
    set_has_coo_sparse();
    encoding_.coo_sparse_ = coo_sparse;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TensorInfo.coo_sparse)
}
inline ::tensorflow::TensorInfo_CooSparse* TensorInfo::mutable_coo_sparse() {
  if (!has_coo_sparse()) {
    clear_encoding();
    set_has_coo_sparse();
    encoding_.coo_sparse_ = CreateMaybeMessage< ::tensorflow::TensorInfo_CooSparse >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorInfo.coo_sparse)
  return encoding_.coo_sparse_;
}

// .tensorflow.DataType dtype = 2;
inline void TensorInfo::clear_dtype() {
  dtype_ = 0;
}
inline ::tensorflow::DataType TensorInfo::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorInfo.dtype)
  return static_cast< ::tensorflow::DataType >(dtype_);
}
inline void TensorInfo::set_dtype(::tensorflow::DataType value) {
  
  dtype_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TensorInfo.dtype)
}

// .tensorflow.TensorShapeProto tensor_shape = 3;
inline bool TensorInfo::has_tensor_shape() const {
  return this != internal_default_instance() && tensor_shape_ != NULL;
}
inline const ::tensorflow::TensorShapeProto& TensorInfo::_internal_tensor_shape() const {
  return *tensor_shape_;
}
inline const ::tensorflow::TensorShapeProto& TensorInfo::tensor_shape() const {
  const ::tensorflow::TensorShapeProto* p = tensor_shape_;
  // @@protoc_insertion_point(field_get:tensorflow.TensorInfo.tensor_shape)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::TensorShapeProto*>(
      &::tensorflow::_TensorShapeProto_default_instance_);
}
inline ::tensorflow::TensorShapeProto* TensorInfo::release_tensor_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.TensorInfo.tensor_shape)
  
  ::tensorflow::TensorShapeProto* temp = tensor_shape_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  tensor_shape_ = NULL;
  return temp;
}
inline ::tensorflow::TensorShapeProto* TensorInfo::unsafe_arena_release_tensor_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TensorInfo.tensor_shape)
  
  ::tensorflow::TensorShapeProto* temp = tensor_shape_;
  tensor_shape_ = NULL;
  return temp;
}
inline ::tensorflow::TensorShapeProto* TensorInfo::mutable_tensor_shape() {
  
  if (tensor_shape_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaNoVirtual());
    tensor_shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorInfo.tensor_shape)
  return tensor_shape_;
}
inline void TensorInfo::set_allocated_tensor_shape(::tensorflow::TensorShapeProto* tensor_shape) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(tensor_shape_);
  }
  if (tensor_shape) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(tensor_shape)->GetArena();
    if (message_arena != submessage_arena) {
      tensor_shape = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, tensor_shape, submessage_arena);
    }
    
  } else {
    
  }
  tensor_shape_ = tensor_shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TensorInfo.tensor_shape)
}

inline bool TensorInfo::has_encoding() const {
  return encoding_case() != ENCODING_NOT_SET;
}
inline void TensorInfo::clear_has_encoding() {
  _oneof_case_[0] = ENCODING_NOT_SET;
}
inline TensorInfo::EncodingCase TensorInfo::encoding_case() const {
  return TensorInfo::EncodingCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// SignatureDef

// map<string, .tensorflow.TensorInfo> inputs = 1;
inline int SignatureDef::inputs_size() const {
  return inputs_.size();
}
inline void SignatureDef::clear_inputs() {
  inputs_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::tensorflow::TensorInfo >&
SignatureDef::inputs() const {
  // @@protoc_insertion_point(field_map:tensorflow.SignatureDef.inputs)
  return inputs_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::tensorflow::TensorInfo >*
SignatureDef::mutable_inputs() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.SignatureDef.inputs)
  return inputs_.MutableMap();
}

// map<string, .tensorflow.TensorInfo> outputs = 2;
inline int SignatureDef::outputs_size() const {
  return outputs_.size();
}
inline void SignatureDef::clear_outputs() {
  outputs_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::tensorflow::TensorInfo >&
SignatureDef::outputs() const {
  // @@protoc_insertion_point(field_map:tensorflow.SignatureDef.outputs)
  return outputs_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::tensorflow::TensorInfo >*
SignatureDef::mutable_outputs() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.SignatureDef.outputs)
  return outputs_.MutableMap();
}

// string method_name = 3;
inline void SignatureDef::clear_method_name() {
  method_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& SignatureDef::method_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SignatureDef.method_name)
  return method_name_.Get();
}
inline void SignatureDef::set_method_name(const ::std::string& value) {
  
  method_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SignatureDef.method_name)
}
#if LANG_CXX11
inline void SignatureDef::set_method_name(::std::string&& value) {
  
  method_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SignatureDef.method_name)
}
#endif
inline void SignatureDef::set_method_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  method_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SignatureDef.method_name)
}
inline void SignatureDef::set_method_name(const char* value,
    size_t size) {
  
  method_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SignatureDef.method_name)
}
inline ::std::string* SignatureDef::mutable_method_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SignatureDef.method_name)
  return method_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* SignatureDef::release_method_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SignatureDef.method_name)
  
  return method_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SignatureDef::set_allocated_method_name(::std::string* method_name) {
  if (method_name != NULL) {
    
  } else {
    
  }
  method_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), method_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SignatureDef.method_name)
}
inline ::std::string* SignatureDef::unsafe_arena_release_method_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SignatureDef.method_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return method_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SignatureDef::unsafe_arena_set_allocated_method_name(
    ::std::string* method_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (method_name != NULL) {
    
  } else {
    
  }
  method_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      method_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SignatureDef.method_name)
}

// -------------------------------------------------------------------

// AssetFileDef

// .tensorflow.TensorInfo tensor_info = 1;
inline bool AssetFileDef::has_tensor_info() const {
  return this != internal_default_instance() && tensor_info_ != NULL;
}
inline void AssetFileDef::clear_tensor_info() {
  if (GetArenaNoVirtual() == NULL && tensor_info_ != NULL) {
    delete tensor_info_;
  }
  tensor_info_ = NULL;
}
inline const ::tensorflow::TensorInfo& AssetFileDef::_internal_tensor_info() const {
  return *tensor_info_;
}
inline const ::tensorflow::TensorInfo& AssetFileDef::tensor_info() const {
  const ::tensorflow::TensorInfo* p = tensor_info_;
  // @@protoc_insertion_point(field_get:tensorflow.AssetFileDef.tensor_info)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::TensorInfo*>(
      &::tensorflow::_TensorInfo_default_instance_);
}
inline ::tensorflow::TensorInfo* AssetFileDef::release_tensor_info() {
  // @@protoc_insertion_point(field_release:tensorflow.AssetFileDef.tensor_info)
  
  ::tensorflow::TensorInfo* temp = tensor_info_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  tensor_info_ = NULL;
  return temp;
}
inline ::tensorflow::TensorInfo* AssetFileDef::unsafe_arena_release_tensor_info() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.AssetFileDef.tensor_info)
  
  ::tensorflow::TensorInfo* temp = tensor_info_;
  tensor_info_ = NULL;
  return temp;
}
inline ::tensorflow::TensorInfo* AssetFileDef::mutable_tensor_info() {
  
  if (tensor_info_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorInfo>(GetArenaNoVirtual());
    tensor_info_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.AssetFileDef.tensor_info)
  return tensor_info_;
}
inline void AssetFileDef::set_allocated_tensor_info(::tensorflow::TensorInfo* tensor_info) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete tensor_info_;
  }
  if (tensor_info) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(tensor_info);
    if (message_arena != submessage_arena) {
      tensor_info = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, tensor_info, submessage_arena);
    }
    
  } else {
    
  }
  tensor_info_ = tensor_info;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AssetFileDef.tensor_info)
}

// string filename = 2;
inline void AssetFileDef::clear_filename() {
  filename_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& AssetFileDef::filename() const {
  // @@protoc_insertion_point(field_get:tensorflow.AssetFileDef.filename)
  return filename_.Get();
}
inline void AssetFileDef::set_filename(const ::std::string& value) {
  
  filename_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.AssetFileDef.filename)
}
#if LANG_CXX11
inline void AssetFileDef::set_filename(::std::string&& value) {
  
  filename_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.AssetFileDef.filename)
}
#endif
inline void AssetFileDef::set_filename(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  filename_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.AssetFileDef.filename)
}
inline void AssetFileDef::set_filename(const char* value,
    size_t size) {
  
  filename_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.AssetFileDef.filename)
}
inline ::std::string* AssetFileDef::mutable_filename() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.AssetFileDef.filename)
  return filename_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* AssetFileDef::release_filename() {
  // @@protoc_insertion_point(field_release:tensorflow.AssetFileDef.filename)
  
  return filename_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void AssetFileDef::set_allocated_filename(::std::string* filename) {
  if (filename != NULL) {
    
  } else {
    
  }
  filename_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), filename,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AssetFileDef.filename)
}
inline ::std::string* AssetFileDef::unsafe_arena_release_filename() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.AssetFileDef.filename)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return filename_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void AssetFileDef::unsafe_arena_set_allocated_filename(
    ::std::string* filename) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (filename != NULL) {
    
  } else {
    
  }
  filename_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      filename, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.AssetFileDef.filename)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/device_properties.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto 

namespace protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[3];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto
namespace tensorflow {
class DeviceProperties;
class DevicePropertiesDefaultTypeInternal;
extern DevicePropertiesDefaultTypeInternal _DeviceProperties_default_instance_;
class DeviceProperties_EnvironmentEntry_DoNotUse;
class DeviceProperties_EnvironmentEntry_DoNotUseDefaultTypeInternal;
extern DeviceProperties_EnvironmentEntry_DoNotUseDefaultTypeInternal _DeviceProperties_EnvironmentEntry_DoNotUse_default_instance_;
class NamedDevice;
class NamedDeviceDefaultTypeInternal;
extern NamedDeviceDefaultTypeInternal _NamedDevice_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::DeviceProperties* Arena::CreateMaybeMessage<::tensorflow::DeviceProperties>(Arena*);
template<> ::tensorflow::DeviceProperties_EnvironmentEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::DeviceProperties_EnvironmentEntry_DoNotUse>(Arena*);
template<> ::tensorflow::NamedDevice* Arena::CreateMaybeMessage<::tensorflow::NamedDevice>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

// ===================================================================

class DeviceProperties_EnvironmentEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<DeviceProperties_EnvironmentEntry_DoNotUse, 
    ::std::string, ::std::string,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<DeviceProperties_EnvironmentEntry_DoNotUse, 
    ::std::string, ::std::string,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    0 > SuperType;
  DeviceProperties_EnvironmentEntry_DoNotUse();
  DeviceProperties_EnvironmentEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const DeviceProperties_EnvironmentEntry_DoNotUse& other);
  static const DeviceProperties_EnvironmentEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const DeviceProperties_EnvironmentEntry_DoNotUse*>(&_DeviceProperties_EnvironmentEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class DeviceProperties : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.DeviceProperties) */ {
 public:
  DeviceProperties();
  virtual ~DeviceProperties();

  DeviceProperties(const DeviceProperties& from);

  inline DeviceProperties& operator=(const DeviceProperties& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  DeviceProperties(DeviceProperties&& from) noexcept
    : DeviceProperties() {
    *this = ::std::move(from);
  }

  inline DeviceProperties& operator=(DeviceProperties&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const DeviceProperties& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DeviceProperties* internal_default_instance() {
    return reinterpret_cast<const DeviceProperties*>(
               &_DeviceProperties_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void UnsafeArenaSwap(DeviceProperties* other);
  void Swap(DeviceProperties* other);
  friend void swap(DeviceProperties& a, DeviceProperties& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline DeviceProperties* New() const final {
    return CreateMaybeMessage<DeviceProperties>(NULL);
  }

  DeviceProperties* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<DeviceProperties>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const DeviceProperties& from);
  void MergeFrom(const DeviceProperties& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeviceProperties* other);
  protected:
  explicit DeviceProperties(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<string, string> environment = 6;
  int environment_size() const;
  void clear_environment();
  static const int kEnvironmentFieldNumber = 6;
  const ::google::protobuf::Map< ::std::string, ::std::string >&
      environment() const;
  ::google::protobuf::Map< ::std::string, ::std::string >*
      mutable_environment();

  // string type = 1;
  void clear_type();
  static const int kTypeFieldNumber = 1;
  const ::std::string& type() const;
  void set_type(const ::std::string& value);
  #if LANG_CXX11
  void set_type(::std::string&& value);
  #endif
  void set_type(const char* value);
  void set_type(const char* value, size_t size);
  ::std::string* mutable_type();
  ::std::string* release_type();
  void set_allocated_type(::std::string* type);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_type();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_type(
      ::std::string* type);

  // string vendor = 2;
  void clear_vendor();
  static const int kVendorFieldNumber = 2;
  const ::std::string& vendor() const;
  void set_vendor(const ::std::string& value);
  #if LANG_CXX11
  void set_vendor(::std::string&& value);
  #endif
  void set_vendor(const char* value);
  void set_vendor(const char* value, size_t size);
  ::std::string* mutable_vendor();
  ::std::string* release_vendor();
  void set_allocated_vendor(::std::string* vendor);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_vendor();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_vendor(
      ::std::string* vendor);

  // string model = 3;
  void clear_model();
  static const int kModelFieldNumber = 3;
  const ::std::string& model() const;
  void set_model(const ::std::string& value);
  #if LANG_CXX11
  void set_model(::std::string&& value);
  #endif
  void set_model(const char* value);
  void set_model(const char* value, size_t size);
  ::std::string* mutable_model();
  ::std::string* release_model();
  void set_allocated_model(::std::string* model);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_model();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_model(
      ::std::string* model);

  // int64 frequency = 4;
  void clear_frequency();
  static const int kFrequencyFieldNumber = 4;
  ::google::protobuf::int64 frequency() const;
  void set_frequency(::google::protobuf::int64 value);

  // int64 num_cores = 5;
  void clear_num_cores();
  static const int kNumCoresFieldNumber = 5;
  ::google::protobuf::int64 num_cores() const;
  void set_num_cores(::google::protobuf::int64 value);

  // int64 num_registers = 7;
  void clear_num_registers();
  static const int kNumRegistersFieldNumber = 7;
  ::google::protobuf::int64 num_registers() const;
  void set_num_registers(::google::protobuf::int64 value);

  // int64 l1_cache_size = 8;
  void clear_l1_cache_size();
  static const int kL1CacheSizeFieldNumber = 8;
  ::google::protobuf::int64 l1_cache_size() const;
  void set_l1_cache_size(::google::protobuf::int64 value);

  // int64 l2_cache_size = 9;
  void clear_l2_cache_size();
  static const int kL2CacheSizeFieldNumber = 9;
  ::google::protobuf::int64 l2_cache_size() const;
  void set_l2_cache_size(::google::protobuf::int64 value);

  // int64 l3_cache_size = 10;
  void clear_l3_cache_size();
  static const int kL3CacheSizeFieldNumber = 10;
  ::google::protobuf::int64 l3_cache_size() const;
  void set_l3_cache_size(::google::protobuf::int64 value);

  // int64 shared_memory_size_per_multiprocessor = 11;
  void clear_shared_memory_size_per_multiprocessor();
  static const int kSharedMemorySizePerMultiprocessorFieldNumber = 11;
  ::google::protobuf::int64 shared_memory_size_per_multiprocessor() const;
  void set_shared_memory_size_per_multiprocessor(::google::protobuf::int64 value);

  // int64 memory_size = 12;
  void clear_memory_size();
  static const int kMemorySizeFieldNumber = 12;
  ::google::protobuf::int64 memory_size() const;
  void set_memory_size(::google::protobuf::int64 value);

  // int64 bandwidth = 13;
  void clear_bandwidth();
  static const int kBandwidthFieldNumber = 13;
  ::google::protobuf::int64 bandwidth() const;
  void set_bandwidth(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.DeviceProperties)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::MapField<
      DeviceProperties_EnvironmentEntry_DoNotUse,
      ::std::string, ::std::string,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      0 > environment_;
  ::google::protobuf::internal::ArenaStringPtr type_;
  ::google::protobuf::internal::ArenaStringPtr vendor_;
  ::google::protobuf::internal::ArenaStringPtr model_;
  ::google::protobuf::int64 frequency_;
  ::google::protobuf::int64 num_cores_;
  ::google::protobuf::int64 num_registers_;
  ::google::protobuf::int64 l1_cache_size_;
  ::google::protobuf::int64 l2_cache_size_;
  ::google::protobuf::int64 l3_cache_size_;
  ::google::protobuf::int64 shared_memory_size_per_multiprocessor_;
  ::google::protobuf::int64 memory_size_;
  ::google::protobuf::int64 bandwidth_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class NamedDevice : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.NamedDevice) */ {
 public:
  NamedDevice();
  virtual ~NamedDevice();

  NamedDevice(const NamedDevice& from);

  inline NamedDevice& operator=(const NamedDevice& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  NamedDevice(NamedDevice&& from) noexcept
    : NamedDevice() {
    *this = ::std::move(from);
  }

  inline NamedDevice& operator=(NamedDevice&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const NamedDevice& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const NamedDevice* internal_default_instance() {
    return reinterpret_cast<const NamedDevice*>(
               &_NamedDevice_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void UnsafeArenaSwap(NamedDevice* other);
  void Swap(NamedDevice* other);
  friend void swap(NamedDevice& a, NamedDevice& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline NamedDevice* New() const final {
    return CreateMaybeMessage<NamedDevice>(NULL);
  }

  NamedDevice* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<NamedDevice>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const NamedDevice& from);
  void MergeFrom(const NamedDevice& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NamedDevice* other);
  protected:
  explicit NamedDevice(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      ::std::string* name);

  // .tensorflow.DeviceProperties properties = 2;
  bool has_properties() const;
  void clear_properties();
  static const int kPropertiesFieldNumber = 2;
  private:
  const ::tensorflow::DeviceProperties& _internal_properties() const;
  public:
  const ::tensorflow::DeviceProperties& properties() const;
  ::tensorflow::DeviceProperties* release_properties();
  ::tensorflow::DeviceProperties* mutable_properties();
  void set_allocated_properties(::tensorflow::DeviceProperties* properties);
  void unsafe_arena_set_allocated_properties(
      ::tensorflow::DeviceProperties* properties);
  ::tensorflow::DeviceProperties* unsafe_arena_release_properties();

  // @@protoc_insertion_point(class_scope:tensorflow.NamedDevice)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::tensorflow::DeviceProperties* properties_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// -------------------------------------------------------------------

// DeviceProperties

// string type = 1;
inline void DeviceProperties::clear_type() {
  type_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& DeviceProperties::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.type)
  return type_.Get();
}
inline void DeviceProperties::set_type(const ::std::string& value) {
  
  type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.type)
}
#if LANG_CXX11
inline void DeviceProperties::set_type(::std::string&& value) {
  
  type_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DeviceProperties.type)
}
#endif
inline void DeviceProperties::set_type(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DeviceProperties.type)
}
inline void DeviceProperties::set_type(const char* value,
    size_t size) {
  
  type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DeviceProperties.type)
}
inline ::std::string* DeviceProperties::mutable_type() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceProperties.type)
  return type_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* DeviceProperties::release_type() {
  // @@protoc_insertion_point(field_release:tensorflow.DeviceProperties.type)
  
  return type_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DeviceProperties::set_allocated_type(::std::string* type) {
  if (type != NULL) {
    
  } else {
    
  }
  type_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), type,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeviceProperties.type)
}
inline ::std::string* DeviceProperties::unsafe_arena_release_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DeviceProperties.type)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return type_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DeviceProperties::unsafe_arena_set_allocated_type(
    ::std::string* type) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (type != NULL) {
    
  } else {
    
  }
  type_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      type, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DeviceProperties.type)
}

// string vendor = 2;
inline void DeviceProperties::clear_vendor() {
  vendor_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& DeviceProperties::vendor() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.vendor)
  return vendor_.Get();
}
inline void DeviceProperties::set_vendor(const ::std::string& value) {
  
  vendor_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.vendor)
}
#if LANG_CXX11
inline void DeviceProperties::set_vendor(::std::string&& value) {
  
  vendor_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DeviceProperties.vendor)
}
#endif
inline void DeviceProperties::set_vendor(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  vendor_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DeviceProperties.vendor)
}
inline void DeviceProperties::set_vendor(const char* value,
    size_t size) {
  
  vendor_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DeviceProperties.vendor)
}
inline ::std::string* DeviceProperties::mutable_vendor() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceProperties.vendor)
  return vendor_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* DeviceProperties::release_vendor() {
  // @@protoc_insertion_point(field_release:tensorflow.DeviceProperties.vendor)
  
  return vendor_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DeviceProperties::set_allocated_vendor(::std::string* vendor) {
  if (vendor != NULL) {
    
  } else {
    
  }
  vendor_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), vendor,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeviceProperties.vendor)
}
inline ::std::string* DeviceProperties::unsafe_arena_release_vendor() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DeviceProperties.vendor)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return vendor_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DeviceProperties::unsafe_arena_set_allocated_vendor(
    ::std::string* vendor) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (vendor != NULL) {
    
  } else {
    
  }
  vendor_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      vendor, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DeviceProperties.vendor)
}

// string model = 3;
inline void DeviceProperties::clear_model() {
  model_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& DeviceProperties::model() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.model)
  return model_.Get();
}
inline void DeviceProperties::set_model(const ::std::string& value) {
  
  model_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.model)
}
#if LANG_CXX11
inline void DeviceProperties::set_model(::std::string&& value) {
  
  model_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.DeviceProperties.model)
}
#endif
inline void DeviceProperties::set_model(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  model_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.DeviceProperties.model)
}
inline void DeviceProperties::set_model(const char* value,
    size_t size) {
  
  model_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DeviceProperties.model)
}
inline ::std::string* DeviceProperties::mutable_model() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceProperties.model)
  return model_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* DeviceProperties::release_model() {
  // @@protoc_insertion_point(field_release:tensorflow.DeviceProperties.model)
  
  return model_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void DeviceProperties::set_allocated_model(::std::string* model) {
  if (model != NULL) {
    
  } else {
    
  }
  model_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), model,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeviceProperties.model)
}
inline ::std::string* DeviceProperties::unsafe_arena_release_model() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DeviceProperties.model)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return model_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void DeviceProperties::unsafe_arena_set_allocated_model(
    ::std::string* model) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (model != NULL) {
    
  } else {
    
  }
  model_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      model, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DeviceProperties.model)
}

// int64 frequency = 4;
inline void DeviceProperties::clear_frequency() {
  frequency_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 DeviceProperties::frequency() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.frequency)
  return frequency_;
}
inline void DeviceProperties::set_frequency(::google::protobuf::int64 value) {
  
  frequency_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.frequency)
}

// int64 num_cores = 5;
inline void DeviceProperties::clear_num_cores() {
  num_cores_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 DeviceProperties::num_cores() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.num_cores)
  return num_cores_;
}
inline void DeviceProperties::set_num_cores(::google::protobuf::int64 value) {
  
  num_cores_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.num_cores)
}

// map<string, string> environment = 6;
inline int DeviceProperties::environment_size() const {
  return environment_.size();
}
inline void DeviceProperties::clear_environment() {
  environment_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::std::string >&
DeviceProperties::environment() const {
  // @@protoc_insertion_point(field_map:tensorflow.DeviceProperties.environment)
  return environment_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::std::string >*
DeviceProperties::mutable_environment() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.DeviceProperties.environment)
  return environment_.MutableMap();
}

// int64 num_registers = 7;
inline void DeviceProperties::clear_num_registers() {
  num_registers_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 DeviceProperties::num_registers() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.num_registers)
  return num_registers_;
}
inline void DeviceProperties::set_num_registers(::google::protobuf::int64 value) {
  
  num_registers_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.num_registers)
}

// int64 l1_cache_size = 8;
inline void DeviceProperties::clear_l1_cache_size() {
  l1_cache_size_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 DeviceProperties::l1_cache_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.l1_cache_size)
  return l1_cache_size_;
}
inline void DeviceProperties::set_l1_cache_size(::google::protobuf::int64 value) {
  
  l1_cache_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.l1_cache_size)
}

// int64 l2_cache_size = 9;
inline void DeviceProperties::clear_l2_cache_size() {
  l2_cache_size_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 DeviceProperties::l2_cache_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.l2_cache_size)
  return l2_cache_size_;
}
inline void DeviceProperties::set_l2_cache_size(::google::protobuf::int64 value) {
  
  l2_cache_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.l2_cache_size)
}

// int64 l3_cache_size = 10;
inline void DeviceProperties::clear_l3_cache_size() {
  l3_cache_size_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 DeviceProperties::l3_cache_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.l3_cache_size)
  return l3_cache_size_;
}
inline void DeviceProperties::set_l3_cache_size(::google::protobuf::int64 value) {
  
  l3_cache_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.l3_cache_size)
}

// int64 shared_memory_size_per_multiprocessor = 11;
inline void DeviceProperties::clear_shared_memory_size_per_multiprocessor() {
  shared_memory_size_per_multiprocessor_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 DeviceProperties::shared_memory_size_per_multiprocessor() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.shared_memory_size_per_multiprocessor)
  return shared_memory_size_per_multiprocessor_;
}
inline void DeviceProperties::set_shared_memory_size_per_multiprocessor(::google::protobuf::int64 value) {
  
  shared_memory_size_per_multiprocessor_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.shared_memory_size_per_multiprocessor)
}

// int64 memory_size = 12;
inline void DeviceProperties::clear_memory_size() {
  memory_size_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 DeviceProperties::memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.memory_size)
  return memory_size_;
}
inline void DeviceProperties::set_memory_size(::google::protobuf::int64 value) {
  
  memory_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.memory_size)
}

// int64 bandwidth = 13;
inline void DeviceProperties::clear_bandwidth() {
  bandwidth_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 DeviceProperties::bandwidth() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.bandwidth)
  return bandwidth_;
}
inline void DeviceProperties::set_bandwidth(::google::protobuf::int64 value) {
  
  bandwidth_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.bandwidth)
}

// -------------------------------------------------------------------

// NamedDevice

// string name = 1;
inline void NamedDevice::clear_name() {
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& NamedDevice::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.NamedDevice.name)
  return name_.Get();
}
inline void NamedDevice::set_name(const ::std::string& value) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.NamedDevice.name)
}
#if LANG_CXX11
inline void NamedDevice::set_name(::std::string&& value) {
  
  name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.NamedDevice.name)
}
#endif
inline void NamedDevice::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.NamedDevice.name)
}
inline void NamedDevice::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.NamedDevice.name)
}
inline ::std::string* NamedDevice::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.NamedDevice.name)
  return name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* NamedDevice::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.NamedDevice.name)
  
  return name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void NamedDevice::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NamedDevice.name)
}
inline ::std::string* NamedDevice::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.NamedDevice.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void NamedDevice::unsafe_arena_set_allocated_name(
    ::std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (name != NULL) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.NamedDevice.name)
}

// .tensorflow.DeviceProperties properties = 2;
inline bool NamedDevice::has_properties() const {
  return this != internal_default_instance() && properties_ != NULL;
}
inline void NamedDevice::clear_properties() {
  if (GetArenaNoVirtual() == NULL && properties_ != NULL) {
    delete properties_;
  }
  properties_ = NULL;
}
inline const ::tensorflow::DeviceProperties& NamedDevice::_internal_properties() const {
  return *properties_;
}
inline const ::tensorflow::DeviceProperties& NamedDevice::properties() const {
  const ::tensorflow::DeviceProperties* p = properties_;
  // @@protoc_insertion_point(field_get:tensorflow.NamedDevice.properties)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::DeviceProperties*>(
      &::tensorflow::_DeviceProperties_default_instance_);
}
inline ::tensorflow::DeviceProperties* NamedDevice::release_properties() {
  // @@protoc_insertion_point(field_release:tensorflow.NamedDevice.properties)
  
  ::tensorflow::DeviceProperties* temp = properties_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  properties_ = NULL;
  return temp;
}
inline ::tensorflow::DeviceProperties* NamedDevice::unsafe_arena_release_properties() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.NamedDevice.properties)
  
  ::tensorflow::DeviceProperties* temp = properties_;
  properties_ = NULL;
  return temp;
}
inline ::tensorflow::DeviceProperties* NamedDevice::mutable_properties() {
  
  if (properties_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::DeviceProperties>(GetArenaNoVirtual());
    properties_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.NamedDevice.properties)
  return properties_;
}
inline void NamedDevice::set_allocated_properties(::tensorflow::DeviceProperties* properties) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete properties_;
  }
  if (properties) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(properties);
    if (message_arena != submessage_arena) {
      properties = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, properties, submessage_arena);
    }
    
  } else {
    
  }
  properties_ = properties;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NamedDevice.properties)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto

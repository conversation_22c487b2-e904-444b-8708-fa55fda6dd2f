// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/tpu/tpu_embedding_configuration.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/protobuf/tpu/optimization_parameters.pb.h"
#include "tensorflow/core/protobuf/tpu/tpu_embedding_output_layout.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto 

namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[2];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto
namespace tensorflow {
namespace tpu {
class TPUEmbeddingConfiguration;
class TPUEmbeddingConfigurationDefaultTypeInternal;
extern TPUEmbeddingConfigurationDefaultTypeInternal _TPUEmbeddingConfiguration_default_instance_;
class TPUEmbeddingConfiguration_TableDescriptor;
class TPUEmbeddingConfiguration_TableDescriptorDefaultTypeInternal;
extern TPUEmbeddingConfiguration_TableDescriptorDefaultTypeInternal _TPUEmbeddingConfiguration_TableDescriptor_default_instance_;
}  // namespace tpu
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::tpu::TPUEmbeddingConfiguration* Arena::CreateMaybeMessage<::tensorflow::tpu::TPUEmbeddingConfiguration>(Arena*);
template<> ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor* Arena::CreateMaybeMessage<::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {
namespace tpu {

enum TPUEmbeddingConfiguration_Mode {
  TPUEmbeddingConfiguration_Mode_UNSPECIFIED = 0,
  TPUEmbeddingConfiguration_Mode_INFERENCE = 1,
  TPUEmbeddingConfiguration_Mode_TRAINING = 2,
  TPUEmbeddingConfiguration_Mode_BACKWARD_PASS_ONLY = 3,
  TPUEmbeddingConfiguration_Mode_TPUEmbeddingConfiguration_Mode_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  TPUEmbeddingConfiguration_Mode_TPUEmbeddingConfiguration_Mode_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool TPUEmbeddingConfiguration_Mode_IsValid(int value);
const TPUEmbeddingConfiguration_Mode TPUEmbeddingConfiguration_Mode_Mode_MIN = TPUEmbeddingConfiguration_Mode_UNSPECIFIED;
const TPUEmbeddingConfiguration_Mode TPUEmbeddingConfiguration_Mode_Mode_MAX = TPUEmbeddingConfiguration_Mode_BACKWARD_PASS_ONLY;
const int TPUEmbeddingConfiguration_Mode_Mode_ARRAYSIZE = TPUEmbeddingConfiguration_Mode_Mode_MAX + 1;

const ::google::protobuf::EnumDescriptor* TPUEmbeddingConfiguration_Mode_descriptor();
inline const ::std::string& TPUEmbeddingConfiguration_Mode_Name(TPUEmbeddingConfiguration_Mode value) {
  return ::google::protobuf::internal::NameOfEnum(
    TPUEmbeddingConfiguration_Mode_descriptor(), value);
}
inline bool TPUEmbeddingConfiguration_Mode_Parse(
    const ::std::string& name, TPUEmbeddingConfiguration_Mode* value) {
  return ::google::protobuf::internal::ParseNamedEnum<TPUEmbeddingConfiguration_Mode>(
    TPUEmbeddingConfiguration_Mode_descriptor(), name, value);
}
enum TPUEmbeddingConfiguration_ShardingStrategy {
  TPUEmbeddingConfiguration_ShardingStrategy_DIV_DEFAULT = 0,
  TPUEmbeddingConfiguration_ShardingStrategy_MOD = 1,
  TPUEmbeddingConfiguration_ShardingStrategy_TPUEmbeddingConfiguration_ShardingStrategy_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  TPUEmbeddingConfiguration_ShardingStrategy_TPUEmbeddingConfiguration_ShardingStrategy_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool TPUEmbeddingConfiguration_ShardingStrategy_IsValid(int value);
const TPUEmbeddingConfiguration_ShardingStrategy TPUEmbeddingConfiguration_ShardingStrategy_ShardingStrategy_MIN = TPUEmbeddingConfiguration_ShardingStrategy_DIV_DEFAULT;
const TPUEmbeddingConfiguration_ShardingStrategy TPUEmbeddingConfiguration_ShardingStrategy_ShardingStrategy_MAX = TPUEmbeddingConfiguration_ShardingStrategy_MOD;
const int TPUEmbeddingConfiguration_ShardingStrategy_ShardingStrategy_ARRAYSIZE = TPUEmbeddingConfiguration_ShardingStrategy_ShardingStrategy_MAX + 1;

const ::google::protobuf::EnumDescriptor* TPUEmbeddingConfiguration_ShardingStrategy_descriptor();
inline const ::std::string& TPUEmbeddingConfiguration_ShardingStrategy_Name(TPUEmbeddingConfiguration_ShardingStrategy value) {
  return ::google::protobuf::internal::NameOfEnum(
    TPUEmbeddingConfiguration_ShardingStrategy_descriptor(), value);
}
inline bool TPUEmbeddingConfiguration_ShardingStrategy_Parse(
    const ::std::string& name, TPUEmbeddingConfiguration_ShardingStrategy* value) {
  return ::google::protobuf::internal::ParseNamedEnum<TPUEmbeddingConfiguration_ShardingStrategy>(
    TPUEmbeddingConfiguration_ShardingStrategy_descriptor(), name, value);
}
// ===================================================================

class TPUEmbeddingConfiguration_TableDescriptor : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor) */ {
 public:
  TPUEmbeddingConfiguration_TableDescriptor();
  virtual ~TPUEmbeddingConfiguration_TableDescriptor();

  TPUEmbeddingConfiguration_TableDescriptor(const TPUEmbeddingConfiguration_TableDescriptor& from);

  inline TPUEmbeddingConfiguration_TableDescriptor& operator=(const TPUEmbeddingConfiguration_TableDescriptor& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TPUEmbeddingConfiguration_TableDescriptor(TPUEmbeddingConfiguration_TableDescriptor&& from) noexcept
    : TPUEmbeddingConfiguration_TableDescriptor() {
    *this = ::std::move(from);
  }

  inline TPUEmbeddingConfiguration_TableDescriptor& operator=(TPUEmbeddingConfiguration_TableDescriptor&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const TPUEmbeddingConfiguration_TableDescriptor& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TPUEmbeddingConfiguration_TableDescriptor* internal_default_instance() {
    return reinterpret_cast<const TPUEmbeddingConfiguration_TableDescriptor*>(
               &_TPUEmbeddingConfiguration_TableDescriptor_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void Swap(TPUEmbeddingConfiguration_TableDescriptor* other);
  friend void swap(TPUEmbeddingConfiguration_TableDescriptor& a, TPUEmbeddingConfiguration_TableDescriptor& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TPUEmbeddingConfiguration_TableDescriptor* New() const final {
    return CreateMaybeMessage<TPUEmbeddingConfiguration_TableDescriptor>(NULL);
  }

  TPUEmbeddingConfiguration_TableDescriptor* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TPUEmbeddingConfiguration_TableDescriptor>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TPUEmbeddingConfiguration_TableDescriptor& from);
  void MergeFrom(const TPUEmbeddingConfiguration_TableDescriptor& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TPUEmbeddingConfiguration_TableDescriptor* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // .tensorflow.tpu.OptimizationParameters optimization_parameters = 5;
  bool has_optimization_parameters() const;
  void clear_optimization_parameters();
  static const int kOptimizationParametersFieldNumber = 5;
  private:
  const ::tensorflow::tpu::OptimizationParameters& _internal_optimization_parameters() const;
  public:
  const ::tensorflow::tpu::OptimizationParameters& optimization_parameters() const;
  ::tensorflow::tpu::OptimizationParameters* release_optimization_parameters();
  ::tensorflow::tpu::OptimizationParameters* mutable_optimization_parameters();
  void set_allocated_optimization_parameters(::tensorflow::tpu::OptimizationParameters* optimization_parameters);

  // int32 vocabulary_size = 2;
  void clear_vocabulary_size();
  static const int kVocabularySizeFieldNumber = 2;
  ::google::protobuf::int32 vocabulary_size() const;
  void set_vocabulary_size(::google::protobuf::int32 value);

  // int32 dimension = 3;
  void clear_dimension();
  static const int kDimensionFieldNumber = 3;
  ::google::protobuf::int32 dimension() const;
  void set_dimension(::google::protobuf::int32 value);

  // int32 num_features = 4;
  void clear_num_features();
  static const int kNumFeaturesFieldNumber = 4;
  ::google::protobuf::int32 num_features() const;
  void set_num_features(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::tensorflow::tpu::OptimizationParameters* optimization_parameters_;
  ::google::protobuf::int32 vocabulary_size_;
  ::google::protobuf::int32 dimension_;
  ::google::protobuf::int32 num_features_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class TPUEmbeddingConfiguration : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.TPUEmbeddingConfiguration) */ {
 public:
  TPUEmbeddingConfiguration();
  virtual ~TPUEmbeddingConfiguration();

  TPUEmbeddingConfiguration(const TPUEmbeddingConfiguration& from);

  inline TPUEmbeddingConfiguration& operator=(const TPUEmbeddingConfiguration& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TPUEmbeddingConfiguration(TPUEmbeddingConfiguration&& from) noexcept
    : TPUEmbeddingConfiguration() {
    *this = ::std::move(from);
  }

  inline TPUEmbeddingConfiguration& operator=(TPUEmbeddingConfiguration&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const TPUEmbeddingConfiguration& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TPUEmbeddingConfiguration* internal_default_instance() {
    return reinterpret_cast<const TPUEmbeddingConfiguration*>(
               &_TPUEmbeddingConfiguration_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void Swap(TPUEmbeddingConfiguration* other);
  friend void swap(TPUEmbeddingConfiguration& a, TPUEmbeddingConfiguration& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TPUEmbeddingConfiguration* New() const final {
    return CreateMaybeMessage<TPUEmbeddingConfiguration>(NULL);
  }

  TPUEmbeddingConfiguration* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TPUEmbeddingConfiguration>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TPUEmbeddingConfiguration& from);
  void MergeFrom(const TPUEmbeddingConfiguration& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TPUEmbeddingConfiguration* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef TPUEmbeddingConfiguration_TableDescriptor TableDescriptor;

  typedef TPUEmbeddingConfiguration_Mode Mode;
  static const Mode UNSPECIFIED =
    TPUEmbeddingConfiguration_Mode_UNSPECIFIED;
  static const Mode INFERENCE =
    TPUEmbeddingConfiguration_Mode_INFERENCE;
  static const Mode TRAINING =
    TPUEmbeddingConfiguration_Mode_TRAINING;
  static const Mode BACKWARD_PASS_ONLY =
    TPUEmbeddingConfiguration_Mode_BACKWARD_PASS_ONLY;
  static inline bool Mode_IsValid(int value) {
    return TPUEmbeddingConfiguration_Mode_IsValid(value);
  }
  static const Mode Mode_MIN =
    TPUEmbeddingConfiguration_Mode_Mode_MIN;
  static const Mode Mode_MAX =
    TPUEmbeddingConfiguration_Mode_Mode_MAX;
  static const int Mode_ARRAYSIZE =
    TPUEmbeddingConfiguration_Mode_Mode_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  Mode_descriptor() {
    return TPUEmbeddingConfiguration_Mode_descriptor();
  }
  static inline const ::std::string& Mode_Name(Mode value) {
    return TPUEmbeddingConfiguration_Mode_Name(value);
  }
  static inline bool Mode_Parse(const ::std::string& name,
      Mode* value) {
    return TPUEmbeddingConfiguration_Mode_Parse(name, value);
  }

  typedef TPUEmbeddingConfiguration_ShardingStrategy ShardingStrategy;
  static const ShardingStrategy DIV_DEFAULT =
    TPUEmbeddingConfiguration_ShardingStrategy_DIV_DEFAULT;
  static const ShardingStrategy MOD =
    TPUEmbeddingConfiguration_ShardingStrategy_MOD;
  static inline bool ShardingStrategy_IsValid(int value) {
    return TPUEmbeddingConfiguration_ShardingStrategy_IsValid(value);
  }
  static const ShardingStrategy ShardingStrategy_MIN =
    TPUEmbeddingConfiguration_ShardingStrategy_ShardingStrategy_MIN;
  static const ShardingStrategy ShardingStrategy_MAX =
    TPUEmbeddingConfiguration_ShardingStrategy_ShardingStrategy_MAX;
  static const int ShardingStrategy_ARRAYSIZE =
    TPUEmbeddingConfiguration_ShardingStrategy_ShardingStrategy_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  ShardingStrategy_descriptor() {
    return TPUEmbeddingConfiguration_ShardingStrategy_descriptor();
  }
  static inline const ::std::string& ShardingStrategy_Name(ShardingStrategy value) {
    return TPUEmbeddingConfiguration_ShardingStrategy_Name(value);
  }
  static inline bool ShardingStrategy_Parse(const ::std::string& name,
      ShardingStrategy* value) {
    return TPUEmbeddingConfiguration_ShardingStrategy_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // repeated .tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor table_descriptor = 1;
  int table_descriptor_size() const;
  void clear_table_descriptor();
  static const int kTableDescriptorFieldNumber = 1;
  ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor* mutable_table_descriptor(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor >*
      mutable_table_descriptor();
  const ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor& table_descriptor(int index) const;
  ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor* add_table_descriptor();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor >&
      table_descriptor() const;

  // .tensorflow.tpu.TPUEmbeddingOutputLayout output_layout = 8;
  bool has_output_layout() const;
  void clear_output_layout();
  static const int kOutputLayoutFieldNumber = 8;
  private:
  const ::tensorflow::tpu::TPUEmbeddingOutputLayout& _internal_output_layout() const;
  public:
  const ::tensorflow::tpu::TPUEmbeddingOutputLayout& output_layout() const;
  ::tensorflow::tpu::TPUEmbeddingOutputLayout* release_output_layout();
  ::tensorflow::tpu::TPUEmbeddingOutputLayout* mutable_output_layout();
  void set_allocated_output_layout(::tensorflow::tpu::TPUEmbeddingOutputLayout* output_layout);

  // .tensorflow.tpu.TPUEmbeddingConfiguration.Mode mode = 2;
  void clear_mode();
  static const int kModeFieldNumber = 2;
  ::tensorflow::tpu::TPUEmbeddingConfiguration_Mode mode() const;
  void set_mode(::tensorflow::tpu::TPUEmbeddingConfiguration_Mode value);

  // int32 batch_size_per_tensor_core = 3;
  void clear_batch_size_per_tensor_core();
  static const int kBatchSizePerTensorCoreFieldNumber = 3;
  ::google::protobuf::int32 batch_size_per_tensor_core() const;
  void set_batch_size_per_tensor_core(::google::protobuf::int32 value);

  // int32 num_hosts = 4;
  void clear_num_hosts();
  static const int kNumHostsFieldNumber = 4;
  ::google::protobuf::int32 num_hosts() const;
  void set_num_hosts(::google::protobuf::int32 value);

  // int32 num_tensor_cores = 5;
  void clear_num_tensor_cores();
  static const int kNumTensorCoresFieldNumber = 5;
  ::google::protobuf::int32 num_tensor_cores() const;
  void set_num_tensor_cores(::google::protobuf::int32 value);

  // .tensorflow.tpu.TPUEmbeddingConfiguration.ShardingStrategy sharding_strategy = 6;
  void clear_sharding_strategy();
  static const int kShardingStrategyFieldNumber = 6;
  ::tensorflow::tpu::TPUEmbeddingConfiguration_ShardingStrategy sharding_strategy() const;
  void set_sharding_strategy(::tensorflow::tpu::TPUEmbeddingConfiguration_ShardingStrategy value);

  // bool pipeline_execution_with_tensor_core = 7;
  void clear_pipeline_execution_with_tensor_core();
  static const int kPipelineExecutionWithTensorCoreFieldNumber = 7;
  bool pipeline_execution_with_tensor_core() const;
  void set_pipeline_execution_with_tensor_core(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.TPUEmbeddingConfiguration)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor > table_descriptor_;
  ::tensorflow::tpu::TPUEmbeddingOutputLayout* output_layout_;
  int mode_;
  ::google::protobuf::int32 batch_size_per_tensor_core_;
  ::google::protobuf::int32 num_hosts_;
  ::google::protobuf::int32 num_tensor_cores_;
  int sharding_strategy_;
  bool pipeline_execution_with_tensor_core_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TPUEmbeddingConfiguration_TableDescriptor

// string name = 1;
inline void TPUEmbeddingConfiguration_TableDescriptor::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& TPUEmbeddingConfiguration_TableDescriptor::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.name)
  return name_.GetNoArena();
}
inline void TPUEmbeddingConfiguration_TableDescriptor::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.name)
}
#if LANG_CXX11
inline void TPUEmbeddingConfiguration_TableDescriptor::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.name)
}
#endif
inline void TPUEmbeddingConfiguration_TableDescriptor::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.name)
}
inline void TPUEmbeddingConfiguration_TableDescriptor::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.name)
}
inline ::std::string* TPUEmbeddingConfiguration_TableDescriptor::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TPUEmbeddingConfiguration_TableDescriptor::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TPUEmbeddingConfiguration_TableDescriptor::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.name)
}

// int32 vocabulary_size = 2;
inline void TPUEmbeddingConfiguration_TableDescriptor::clear_vocabulary_size() {
  vocabulary_size_ = 0;
}
inline ::google::protobuf::int32 TPUEmbeddingConfiguration_TableDescriptor::vocabulary_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.vocabulary_size)
  return vocabulary_size_;
}
inline void TPUEmbeddingConfiguration_TableDescriptor::set_vocabulary_size(::google::protobuf::int32 value) {
  
  vocabulary_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.vocabulary_size)
}

// int32 dimension = 3;
inline void TPUEmbeddingConfiguration_TableDescriptor::clear_dimension() {
  dimension_ = 0;
}
inline ::google::protobuf::int32 TPUEmbeddingConfiguration_TableDescriptor::dimension() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.dimension)
  return dimension_;
}
inline void TPUEmbeddingConfiguration_TableDescriptor::set_dimension(::google::protobuf::int32 value) {
  
  dimension_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.dimension)
}

// int32 num_features = 4;
inline void TPUEmbeddingConfiguration_TableDescriptor::clear_num_features() {
  num_features_ = 0;
}
inline ::google::protobuf::int32 TPUEmbeddingConfiguration_TableDescriptor::num_features() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.num_features)
  return num_features_;
}
inline void TPUEmbeddingConfiguration_TableDescriptor::set_num_features(::google::protobuf::int32 value) {
  
  num_features_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.num_features)
}

// .tensorflow.tpu.OptimizationParameters optimization_parameters = 5;
inline bool TPUEmbeddingConfiguration_TableDescriptor::has_optimization_parameters() const {
  return this != internal_default_instance() && optimization_parameters_ != NULL;
}
inline const ::tensorflow::tpu::OptimizationParameters& TPUEmbeddingConfiguration_TableDescriptor::_internal_optimization_parameters() const {
  return *optimization_parameters_;
}
inline const ::tensorflow::tpu::OptimizationParameters& TPUEmbeddingConfiguration_TableDescriptor::optimization_parameters() const {
  const ::tensorflow::tpu::OptimizationParameters* p = optimization_parameters_;
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.optimization_parameters)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::tpu::OptimizationParameters*>(
      &::tensorflow::tpu::_OptimizationParameters_default_instance_);
}
inline ::tensorflow::tpu::OptimizationParameters* TPUEmbeddingConfiguration_TableDescriptor::release_optimization_parameters() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.optimization_parameters)
  
  ::tensorflow::tpu::OptimizationParameters* temp = optimization_parameters_;
  optimization_parameters_ = NULL;
  return temp;
}
inline ::tensorflow::tpu::OptimizationParameters* TPUEmbeddingConfiguration_TableDescriptor::mutable_optimization_parameters() {
  
  if (optimization_parameters_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::tpu::OptimizationParameters>(GetArenaNoVirtual());
    optimization_parameters_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.optimization_parameters)
  return optimization_parameters_;
}
inline void TPUEmbeddingConfiguration_TableDescriptor::set_allocated_optimization_parameters(::tensorflow::tpu::OptimizationParameters* optimization_parameters) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(optimization_parameters_);
  }
  if (optimization_parameters) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      optimization_parameters = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, optimization_parameters, submessage_arena);
    }
    
  } else {
    
  }
  optimization_parameters_ = optimization_parameters;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.optimization_parameters)
}

// -------------------------------------------------------------------

// TPUEmbeddingConfiguration

// repeated .tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor table_descriptor = 1;
inline int TPUEmbeddingConfiguration::table_descriptor_size() const {
  return table_descriptor_.size();
}
inline void TPUEmbeddingConfiguration::clear_table_descriptor() {
  table_descriptor_.Clear();
}
inline ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor* TPUEmbeddingConfiguration::mutable_table_descriptor(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUEmbeddingConfiguration.table_descriptor)
  return table_descriptor_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor >*
TPUEmbeddingConfiguration::mutable_table_descriptor() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tpu.TPUEmbeddingConfiguration.table_descriptor)
  return &table_descriptor_;
}
inline const ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor& TPUEmbeddingConfiguration::table_descriptor(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.table_descriptor)
  return table_descriptor_.Get(index);
}
inline ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor* TPUEmbeddingConfiguration::add_table_descriptor() {
  // @@protoc_insertion_point(field_add:tensorflow.tpu.TPUEmbeddingConfiguration.table_descriptor)
  return table_descriptor_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor >&
TPUEmbeddingConfiguration::table_descriptor() const {
  // @@protoc_insertion_point(field_list:tensorflow.tpu.TPUEmbeddingConfiguration.table_descriptor)
  return table_descriptor_;
}

// .tensorflow.tpu.TPUEmbeddingConfiguration.Mode mode = 2;
inline void TPUEmbeddingConfiguration::clear_mode() {
  mode_ = 0;
}
inline ::tensorflow::tpu::TPUEmbeddingConfiguration_Mode TPUEmbeddingConfiguration::mode() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.mode)
  return static_cast< ::tensorflow::tpu::TPUEmbeddingConfiguration_Mode >(mode_);
}
inline void TPUEmbeddingConfiguration::set_mode(::tensorflow::tpu::TPUEmbeddingConfiguration_Mode value) {
  
  mode_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.mode)
}

// int32 batch_size_per_tensor_core = 3;
inline void TPUEmbeddingConfiguration::clear_batch_size_per_tensor_core() {
  batch_size_per_tensor_core_ = 0;
}
inline ::google::protobuf::int32 TPUEmbeddingConfiguration::batch_size_per_tensor_core() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.batch_size_per_tensor_core)
  return batch_size_per_tensor_core_;
}
inline void TPUEmbeddingConfiguration::set_batch_size_per_tensor_core(::google::protobuf::int32 value) {
  
  batch_size_per_tensor_core_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.batch_size_per_tensor_core)
}

// int32 num_hosts = 4;
inline void TPUEmbeddingConfiguration::clear_num_hosts() {
  num_hosts_ = 0;
}
inline ::google::protobuf::int32 TPUEmbeddingConfiguration::num_hosts() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.num_hosts)
  return num_hosts_;
}
inline void TPUEmbeddingConfiguration::set_num_hosts(::google::protobuf::int32 value) {
  
  num_hosts_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.num_hosts)
}

// int32 num_tensor_cores = 5;
inline void TPUEmbeddingConfiguration::clear_num_tensor_cores() {
  num_tensor_cores_ = 0;
}
inline ::google::protobuf::int32 TPUEmbeddingConfiguration::num_tensor_cores() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.num_tensor_cores)
  return num_tensor_cores_;
}
inline void TPUEmbeddingConfiguration::set_num_tensor_cores(::google::protobuf::int32 value) {
  
  num_tensor_cores_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.num_tensor_cores)
}

// .tensorflow.tpu.TPUEmbeddingConfiguration.ShardingStrategy sharding_strategy = 6;
inline void TPUEmbeddingConfiguration::clear_sharding_strategy() {
  sharding_strategy_ = 0;
}
inline ::tensorflow::tpu::TPUEmbeddingConfiguration_ShardingStrategy TPUEmbeddingConfiguration::sharding_strategy() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.sharding_strategy)
  return static_cast< ::tensorflow::tpu::TPUEmbeddingConfiguration_ShardingStrategy >(sharding_strategy_);
}
inline void TPUEmbeddingConfiguration::set_sharding_strategy(::tensorflow::tpu::TPUEmbeddingConfiguration_ShardingStrategy value) {
  
  sharding_strategy_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.sharding_strategy)
}

// bool pipeline_execution_with_tensor_core = 7;
inline void TPUEmbeddingConfiguration::clear_pipeline_execution_with_tensor_core() {
  pipeline_execution_with_tensor_core_ = false;
}
inline bool TPUEmbeddingConfiguration::pipeline_execution_with_tensor_core() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.pipeline_execution_with_tensor_core)
  return pipeline_execution_with_tensor_core_;
}
inline void TPUEmbeddingConfiguration::set_pipeline_execution_with_tensor_core(bool value) {
  
  pipeline_execution_with_tensor_core_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.pipeline_execution_with_tensor_core)
}

// .tensorflow.tpu.TPUEmbeddingOutputLayout output_layout = 8;
inline bool TPUEmbeddingConfiguration::has_output_layout() const {
  return this != internal_default_instance() && output_layout_ != NULL;
}
inline const ::tensorflow::tpu::TPUEmbeddingOutputLayout& TPUEmbeddingConfiguration::_internal_output_layout() const {
  return *output_layout_;
}
inline const ::tensorflow::tpu::TPUEmbeddingOutputLayout& TPUEmbeddingConfiguration::output_layout() const {
  const ::tensorflow::tpu::TPUEmbeddingOutputLayout* p = output_layout_;
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.output_layout)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::tpu::TPUEmbeddingOutputLayout*>(
      &::tensorflow::tpu::_TPUEmbeddingOutputLayout_default_instance_);
}
inline ::tensorflow::tpu::TPUEmbeddingOutputLayout* TPUEmbeddingConfiguration::release_output_layout() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUEmbeddingConfiguration.output_layout)
  
  ::tensorflow::tpu::TPUEmbeddingOutputLayout* temp = output_layout_;
  output_layout_ = NULL;
  return temp;
}
inline ::tensorflow::tpu::TPUEmbeddingOutputLayout* TPUEmbeddingConfiguration::mutable_output_layout() {
  
  if (output_layout_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::tpu::TPUEmbeddingOutputLayout>(GetArenaNoVirtual());
    output_layout_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUEmbeddingConfiguration.output_layout)
  return output_layout_;
}
inline void TPUEmbeddingConfiguration::set_allocated_output_layout(::tensorflow::tpu::TPUEmbeddingOutputLayout* output_layout) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(output_layout_);
  }
  if (output_layout) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      output_layout = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, output_layout, submessage_arena);
    }
    
  } else {
    
  }
  output_layout_ = output_layout;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.TPUEmbeddingConfiguration.output_layout)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tpu
}  // namespace tensorflow

namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::tensorflow::tpu::TPUEmbeddingConfiguration_Mode> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::tpu::TPUEmbeddingConfiguration_Mode>() {
  return ::tensorflow::tpu::TPUEmbeddingConfiguration_Mode_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::tpu::TPUEmbeddingConfiguration_ShardingStrategy> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::tpu::TPUEmbeddingConfiguration_ShardingStrategy>() {
  return ::tensorflow::tpu::TPUEmbeddingConfiguration_ShardingStrategy_descriptor();
}

}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto

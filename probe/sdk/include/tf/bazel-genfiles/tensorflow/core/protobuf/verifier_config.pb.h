// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/verifier_config.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fverifier_5fconfig_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fverifier_5fconfig_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fverifier_5fconfig_2eproto 

namespace protobuf_tensorflow_2fcore_2fprotobuf_2fverifier_5fconfig_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[1];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fverifier_5fconfig_2eproto
namespace tensorflow {
class VerifierConfig;
class VerifierConfigDefaultTypeInternal;
extern VerifierConfigDefaultTypeInternal _VerifierConfig_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::VerifierConfig* Arena::CreateMaybeMessage<::tensorflow::VerifierConfig>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

enum VerifierConfig_Toggle {
  VerifierConfig_Toggle_DEFAULT = 0,
  VerifierConfig_Toggle_ON = 1,
  VerifierConfig_Toggle_OFF = 2,
  VerifierConfig_Toggle_VerifierConfig_Toggle_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  VerifierConfig_Toggle_VerifierConfig_Toggle_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool VerifierConfig_Toggle_IsValid(int value);
const VerifierConfig_Toggle VerifierConfig_Toggle_Toggle_MIN = VerifierConfig_Toggle_DEFAULT;
const VerifierConfig_Toggle VerifierConfig_Toggle_Toggle_MAX = VerifierConfig_Toggle_OFF;
const int VerifierConfig_Toggle_Toggle_ARRAYSIZE = VerifierConfig_Toggle_Toggle_MAX + 1;

const ::google::protobuf::EnumDescriptor* VerifierConfig_Toggle_descriptor();
inline const ::std::string& VerifierConfig_Toggle_Name(VerifierConfig_Toggle value) {
  return ::google::protobuf::internal::NameOfEnum(
    VerifierConfig_Toggle_descriptor(), value);
}
inline bool VerifierConfig_Toggle_Parse(
    const ::std::string& name, VerifierConfig_Toggle* value) {
  return ::google::protobuf::internal::ParseNamedEnum<VerifierConfig_Toggle>(
    VerifierConfig_Toggle_descriptor(), name, value);
}
// ===================================================================

class VerifierConfig : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.VerifierConfig) */ {
 public:
  VerifierConfig();
  virtual ~VerifierConfig();

  VerifierConfig(const VerifierConfig& from);

  inline VerifierConfig& operator=(const VerifierConfig& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  VerifierConfig(VerifierConfig&& from) noexcept
    : VerifierConfig() {
    *this = ::std::move(from);
  }

  inline VerifierConfig& operator=(VerifierConfig&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const VerifierConfig& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const VerifierConfig* internal_default_instance() {
    return reinterpret_cast<const VerifierConfig*>(
               &_VerifierConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(VerifierConfig* other);
  void Swap(VerifierConfig* other);
  friend void swap(VerifierConfig& a, VerifierConfig& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline VerifierConfig* New() const final {
    return CreateMaybeMessage<VerifierConfig>(NULL);
  }

  VerifierConfig* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<VerifierConfig>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const VerifierConfig& from);
  void MergeFrom(const VerifierConfig& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(VerifierConfig* other);
  protected:
  explicit VerifierConfig(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef VerifierConfig_Toggle Toggle;
  static const Toggle DEFAULT =
    VerifierConfig_Toggle_DEFAULT;
  static const Toggle ON =
    VerifierConfig_Toggle_ON;
  static const Toggle OFF =
    VerifierConfig_Toggle_OFF;
  static inline bool Toggle_IsValid(int value) {
    return VerifierConfig_Toggle_IsValid(value);
  }
  static const Toggle Toggle_MIN =
    VerifierConfig_Toggle_Toggle_MIN;
  static const Toggle Toggle_MAX =
    VerifierConfig_Toggle_Toggle_MAX;
  static const int Toggle_ARRAYSIZE =
    VerifierConfig_Toggle_Toggle_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  Toggle_descriptor() {
    return VerifierConfig_Toggle_descriptor();
  }
  static inline const ::std::string& Toggle_Name(Toggle value) {
    return VerifierConfig_Toggle_Name(value);
  }
  static inline bool Toggle_Parse(const ::std::string& name,
      Toggle* value) {
    return VerifierConfig_Toggle_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // int64 verification_timeout_in_ms = 1;
  void clear_verification_timeout_in_ms();
  static const int kVerificationTimeoutInMsFieldNumber = 1;
  ::google::protobuf::int64 verification_timeout_in_ms() const;
  void set_verification_timeout_in_ms(::google::protobuf::int64 value);

  // .tensorflow.VerifierConfig.Toggle structure_verifier = 2;
  void clear_structure_verifier();
  static const int kStructureVerifierFieldNumber = 2;
  ::tensorflow::VerifierConfig_Toggle structure_verifier() const;
  void set_structure_verifier(::tensorflow::VerifierConfig_Toggle value);

  // @@protoc_insertion_point(class_scope:tensorflow.VerifierConfig)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::int64 verification_timeout_in_ms_;
  int structure_verifier_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fverifier_5fconfig_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// VerifierConfig

// int64 verification_timeout_in_ms = 1;
inline void VerifierConfig::clear_verification_timeout_in_ms() {
  verification_timeout_in_ms_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 VerifierConfig::verification_timeout_in_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.VerifierConfig.verification_timeout_in_ms)
  return verification_timeout_in_ms_;
}
inline void VerifierConfig::set_verification_timeout_in_ms(::google::protobuf::int64 value) {
  
  verification_timeout_in_ms_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.VerifierConfig.verification_timeout_in_ms)
}

// .tensorflow.VerifierConfig.Toggle structure_verifier = 2;
inline void VerifierConfig::clear_structure_verifier() {
  structure_verifier_ = 0;
}
inline ::tensorflow::VerifierConfig_Toggle VerifierConfig::structure_verifier() const {
  // @@protoc_insertion_point(field_get:tensorflow.VerifierConfig.structure_verifier)
  return static_cast< ::tensorflow::VerifierConfig_Toggle >(structure_verifier_);
}
inline void VerifierConfig::set_structure_verifier(::tensorflow::VerifierConfig_Toggle value) {
  
  structure_verifier_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.VerifierConfig.structure_verifier)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::tensorflow::VerifierConfig_Toggle> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::VerifierConfig_Toggle>() {
  return ::tensorflow::VerifierConfig_Toggle_descriptor();
}

}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fverifier_5fconfig_2eproto

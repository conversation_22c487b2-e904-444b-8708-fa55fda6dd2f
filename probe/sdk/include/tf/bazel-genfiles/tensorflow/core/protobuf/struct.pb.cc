// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/struct.proto

#include "tensorflow/core/protobuf/struct.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_TensorShapeProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto
namespace protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_NoneValue;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_TensorSpecProto;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto ::google::protobuf::internal::SCCInfo<3> scc_info_DictValue;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto
namespace tensorflow {
class StructuredValueDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<StructuredValue>
      _instance;
  const ::tensorflow::NoneValue* none_value_;
  double float64_value_;
  ::google::protobuf::int64 int64_value_;
  ::google::protobuf::internal::ArenaStringPtr string_value_;
  bool bool_value_;
  const ::tensorflow::TensorShapeProto* tensor_shape_value_;
  int tensor_dtype_value_;
  const ::tensorflow::TensorSpecProto* tensor_spec_value_;
  const ::tensorflow::ListValue* list_value_;
  const ::tensorflow::TupleValue* tuple_value_;
  const ::tensorflow::DictValue* dict_value_;
  const ::tensorflow::NamedTupleValue* named_tuple_value_;
} _StructuredValue_default_instance_;
class NoneValueDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<NoneValue>
      _instance;
} _NoneValue_default_instance_;
class ListValueDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ListValue>
      _instance;
} _ListValue_default_instance_;
class TupleValueDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TupleValue>
      _instance;
} _TupleValue_default_instance_;
class DictValue_FieldsEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<DictValue_FieldsEntry_DoNotUse>
      _instance;
} _DictValue_FieldsEntry_DoNotUse_default_instance_;
class DictValueDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<DictValue>
      _instance;
} _DictValue_default_instance_;
class PairValueDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<PairValue>
      _instance;
} _PairValue_default_instance_;
class NamedTupleValueDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<NamedTupleValue>
      _instance;
} _NamedTupleValue_default_instance_;
class TensorSpecProtoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TensorSpecProto>
      _instance;
} _TensorSpecProto_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto {
static void InitDefaultsNoneValue() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_NoneValue_default_instance_;
    new (ptr) ::tensorflow::NoneValue();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::NoneValue::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_NoneValue =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsNoneValue}, {}};

static void InitDefaultsDictValue() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_StructuredValue_default_instance_;
    new (ptr) ::tensorflow::StructuredValue();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  {
    void* ptr = &::tensorflow::_ListValue_default_instance_;
    new (ptr) ::tensorflow::ListValue();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  {
    void* ptr = &::tensorflow::_TupleValue_default_instance_;
    new (ptr) ::tensorflow::TupleValue();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  {
    void* ptr = &::tensorflow::_DictValue_FieldsEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::DictValue_FieldsEntry_DoNotUse();
  }
  {
    void* ptr = &::tensorflow::_DictValue_default_instance_;
    new (ptr) ::tensorflow::DictValue();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  {
    void* ptr = &::tensorflow::_PairValue_default_instance_;
    new (ptr) ::tensorflow::PairValue();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  {
    void* ptr = &::tensorflow::_NamedTupleValue_default_instance_;
    new (ptr) ::tensorflow::NamedTupleValue();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::StructuredValue::InitAsDefaultInstance();
  ::tensorflow::ListValue::InitAsDefaultInstance();
  ::tensorflow::TupleValue::InitAsDefaultInstance();
  ::tensorflow::DictValue_FieldsEntry_DoNotUse::InitAsDefaultInstance();
  ::tensorflow::DictValue::InitAsDefaultInstance();
  ::tensorflow::PairValue::InitAsDefaultInstance();
  ::tensorflow::NamedTupleValue::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<3> scc_info_DictValue =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 3, InitDefaultsDictValue}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::scc_info_NoneValue.base,
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::scc_info_TensorShapeProto.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::scc_info_TensorSpecProto.base,}};

static void InitDefaultsTensorSpecProto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_TensorSpecProto_default_instance_;
    new (ptr) ::tensorflow::TensorSpecProto();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::TensorSpecProto::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_TensorSpecProto =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsTensorSpecProto}, {
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::scc_info_TensorShapeProto.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_NoneValue.base);
  ::google::protobuf::internal::InitSCC(&scc_info_DictValue.base);
  ::google::protobuf::internal::InitSCC(&scc_info_TensorSpecProto.base);
}

::google::protobuf::Metadata file_level_metadata[9];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::StructuredValue, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::StructuredValue, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  offsetof(::tensorflow::StructuredValueDefaultTypeInternal, none_value_),
  offsetof(::tensorflow::StructuredValueDefaultTypeInternal, float64_value_),
  offsetof(::tensorflow::StructuredValueDefaultTypeInternal, int64_value_),
  offsetof(::tensorflow::StructuredValueDefaultTypeInternal, string_value_),
  offsetof(::tensorflow::StructuredValueDefaultTypeInternal, bool_value_),
  offsetof(::tensorflow::StructuredValueDefaultTypeInternal, tensor_shape_value_),
  offsetof(::tensorflow::StructuredValueDefaultTypeInternal, tensor_dtype_value_),
  offsetof(::tensorflow::StructuredValueDefaultTypeInternal, tensor_spec_value_),
  offsetof(::tensorflow::StructuredValueDefaultTypeInternal, list_value_),
  offsetof(::tensorflow::StructuredValueDefaultTypeInternal, tuple_value_),
  offsetof(::tensorflow::StructuredValueDefaultTypeInternal, dict_value_),
  offsetof(::tensorflow::StructuredValueDefaultTypeInternal, named_tuple_value_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::StructuredValue, kind_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NoneValue, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ListValue, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ListValue, values_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TupleValue, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TupleValue, values_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DictValue_FieldsEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DictValue_FieldsEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DictValue_FieldsEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DictValue_FieldsEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DictValue, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DictValue, fields_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::PairValue, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::PairValue, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::PairValue, value_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NamedTupleValue, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NamedTupleValue, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NamedTupleValue, values_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorSpecProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorSpecProto, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorSpecProto, shape_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorSpecProto, dtype_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::StructuredValue)},
  { 18, -1, sizeof(::tensorflow::NoneValue)},
  { 23, -1, sizeof(::tensorflow::ListValue)},
  { 29, -1, sizeof(::tensorflow::TupleValue)},
  { 35, 42, sizeof(::tensorflow::DictValue_FieldsEntry_DoNotUse)},
  { 44, -1, sizeof(::tensorflow::DictValue)},
  { 50, -1, sizeof(::tensorflow::PairValue)},
  { 57, -1, sizeof(::tensorflow::NamedTupleValue)},
  { 64, -1, sizeof(::tensorflow::TensorSpecProto)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_StructuredValue_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_NoneValue_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_ListValue_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_TupleValue_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_DictValue_FieldsEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_DictValue_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_PairValue_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_NamedTupleValue_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_TensorSpecProto_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/protobuf/struct.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 9);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n%tensorflow/core/protobuf/struct.proto\022"
      "\ntensorflow\032,tensorflow/core/framework/t"
      "ensor_shape.proto\032%tensorflow/core/frame"
      "work/types.proto\"\221\004\n\017StructuredValue\022+\n\n"
      "none_value\030\001 \001(\0132\025.tensorflow.NoneValueH"
      "\000\022\027\n\rfloat64_value\030\013 \001(\001H\000\022\025\n\013int64_valu"
      "e\030\014 \001(\022H\000\022\026\n\014string_value\030\r \001(\tH\000\022\024\n\nboo"
      "l_value\030\016 \001(\010H\000\022:\n\022tensor_shape_value\030\037 "
      "\001(\0132\034.tensorflow.TensorShapeProtoH\000\0222\n\022t"
      "ensor_dtype_value\030  \001(\0162\024.tensorflow.Dat"
      "aTypeH\000\0228\n\021tensor_spec_value\030! \001(\0132\033.ten"
      "sorflow.TensorSpecProtoH\000\022+\n\nlist_value\030"
      "3 \001(\0132\025.tensorflow.ListValueH\000\022-\n\013tuple_"
      "value\0304 \001(\0132\026.tensorflow.TupleValueH\000\022+\n"
      "\ndict_value\0305 \001(\0132\025.tensorflow.DictValue"
      "H\000\0228\n\021named_tuple_value\0306 \001(\0132\033.tensorfl"
      "ow.NamedTupleValueH\000B\006\n\004kind\"\013\n\tNoneValu"
      "e\"8\n\tListValue\022+\n\006values\030\001 \003(\0132\033.tensorf"
      "low.StructuredValue\"9\n\nTupleValue\022+\n\006val"
      "ues\030\001 \003(\0132\033.tensorflow.StructuredValue\"\212"
      "\001\n\tDictValue\0221\n\006fields\030\001 \003(\0132!.tensorflo"
      "w.DictValue.FieldsEntry\032J\n\013FieldsEntry\022\013"
      "\n\003key\030\001 \001(\t\022*\n\005value\030\002 \001(\0132\033.tensorflow."
      "StructuredValue:\0028\001\"D\n\tPairValue\022\013\n\003key\030"
      "\001 \001(\t\022*\n\005value\030\002 \001(\0132\033.tensorflow.Struct"
      "uredValue\"F\n\017NamedTupleValue\022\014\n\004name\030\001 \001"
      "(\t\022%\n\006values\030\002 \003(\0132\025.tensorflow.PairValu"
      "e\"q\n\017TensorSpecProto\022\014\n\004name\030\001 \001(\t\022+\n\005sh"
      "ape\030\002 \001(\0132\034.tensorflow.TensorShapeProto\022"
      "#\n\005dtype\030\003 \001(\0162\024.tensorflow.DataTypeb\006pr"
      "oto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 1204);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/protobuf/struct.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftypes_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto
namespace tensorflow {

// ===================================================================

void StructuredValue::InitAsDefaultInstance() {
  ::tensorflow::_StructuredValue_default_instance_.none_value_ = const_cast< ::tensorflow::NoneValue*>(
      ::tensorflow::NoneValue::internal_default_instance());
  ::tensorflow::_StructuredValue_default_instance_.float64_value_ = 0;
  ::tensorflow::_StructuredValue_default_instance_.int64_value_ = GOOGLE_LONGLONG(0);
  ::tensorflow::_StructuredValue_default_instance_.string_value_.UnsafeSetDefault(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::tensorflow::_StructuredValue_default_instance_.bool_value_ = false;
  ::tensorflow::_StructuredValue_default_instance_.tensor_shape_value_ = const_cast< ::tensorflow::TensorShapeProto*>(
      ::tensorflow::TensorShapeProto::internal_default_instance());
  ::tensorflow::_StructuredValue_default_instance_.tensor_dtype_value_ = 0;
  ::tensorflow::_StructuredValue_default_instance_.tensor_spec_value_ = const_cast< ::tensorflow::TensorSpecProto*>(
      ::tensorflow::TensorSpecProto::internal_default_instance());
  ::tensorflow::_StructuredValue_default_instance_.list_value_ = const_cast< ::tensorflow::ListValue*>(
      ::tensorflow::ListValue::internal_default_instance());
  ::tensorflow::_StructuredValue_default_instance_.tuple_value_ = const_cast< ::tensorflow::TupleValue*>(
      ::tensorflow::TupleValue::internal_default_instance());
  ::tensorflow::_StructuredValue_default_instance_.dict_value_ = const_cast< ::tensorflow::DictValue*>(
      ::tensorflow::DictValue::internal_default_instance());
  ::tensorflow::_StructuredValue_default_instance_.named_tuple_value_ = const_cast< ::tensorflow::NamedTupleValue*>(
      ::tensorflow::NamedTupleValue::internal_default_instance());
}
void StructuredValue::set_allocated_none_value(::tensorflow::NoneValue* none_value) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_kind();
  if (none_value) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      none_value = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, none_value, submessage_arena);
    }
    set_has_none_value();
    kind_.none_value_ = none_value;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.StructuredValue.none_value)
}
void StructuredValue::set_allocated_tensor_shape_value(::tensorflow::TensorShapeProto* tensor_shape_value) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_kind();
  if (tensor_shape_value) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(tensor_shape_value)->GetArena();
    if (message_arena != submessage_arena) {
      tensor_shape_value = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, tensor_shape_value, submessage_arena);
    }
    set_has_tensor_shape_value();
    kind_.tensor_shape_value_ = tensor_shape_value;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.StructuredValue.tensor_shape_value)
}
void StructuredValue::clear_tensor_shape_value() {
  if (has_tensor_shape_value()) {
    delete kind_.tensor_shape_value_;
    clear_has_kind();
  }
}
void StructuredValue::set_allocated_tensor_spec_value(::tensorflow::TensorSpecProto* tensor_spec_value) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_kind();
  if (tensor_spec_value) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      tensor_spec_value = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, tensor_spec_value, submessage_arena);
    }
    set_has_tensor_spec_value();
    kind_.tensor_spec_value_ = tensor_spec_value;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.StructuredValue.tensor_spec_value)
}
void StructuredValue::set_allocated_list_value(::tensorflow::ListValue* list_value) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_kind();
  if (list_value) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      list_value = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, list_value, submessage_arena);
    }
    set_has_list_value();
    kind_.list_value_ = list_value;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.StructuredValue.list_value)
}
void StructuredValue::set_allocated_tuple_value(::tensorflow::TupleValue* tuple_value) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_kind();
  if (tuple_value) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      tuple_value = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, tuple_value, submessage_arena);
    }
    set_has_tuple_value();
    kind_.tuple_value_ = tuple_value;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.StructuredValue.tuple_value)
}
void StructuredValue::set_allocated_dict_value(::tensorflow::DictValue* dict_value) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_kind();
  if (dict_value) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      dict_value = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, dict_value, submessage_arena);
    }
    set_has_dict_value();
    kind_.dict_value_ = dict_value;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.StructuredValue.dict_value)
}
void StructuredValue::set_allocated_named_tuple_value(::tensorflow::NamedTupleValue* named_tuple_value) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_kind();
  if (named_tuple_value) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      named_tuple_value = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, named_tuple_value, submessage_arena);
    }
    set_has_named_tuple_value();
    kind_.named_tuple_value_ = named_tuple_value;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.StructuredValue.named_tuple_value)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int StructuredValue::kNoneValueFieldNumber;
const int StructuredValue::kFloat64ValueFieldNumber;
const int StructuredValue::kInt64ValueFieldNumber;
const int StructuredValue::kStringValueFieldNumber;
const int StructuredValue::kBoolValueFieldNumber;
const int StructuredValue::kTensorShapeValueFieldNumber;
const int StructuredValue::kTensorDtypeValueFieldNumber;
const int StructuredValue::kTensorSpecValueFieldNumber;
const int StructuredValue::kListValueFieldNumber;
const int StructuredValue::kTupleValueFieldNumber;
const int StructuredValue::kDictValueFieldNumber;
const int StructuredValue::kNamedTupleValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

StructuredValue::StructuredValue()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::scc_info_DictValue.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.StructuredValue)
}
StructuredValue::StructuredValue(const StructuredValue& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  clear_has_kind();
  switch (from.kind_case()) {
    case kNoneValue: {
      mutable_none_value()->::tensorflow::NoneValue::MergeFrom(from.none_value());
      break;
    }
    case kFloat64Value: {
      set_float64_value(from.float64_value());
      break;
    }
    case kInt64Value: {
      set_int64_value(from.int64_value());
      break;
    }
    case kStringValue: {
      set_string_value(from.string_value());
      break;
    }
    case kBoolValue: {
      set_bool_value(from.bool_value());
      break;
    }
    case kTensorShapeValue: {
      mutable_tensor_shape_value()->::tensorflow::TensorShapeProto::MergeFrom(from.tensor_shape_value());
      break;
    }
    case kTensorDtypeValue: {
      set_tensor_dtype_value(from.tensor_dtype_value());
      break;
    }
    case kTensorSpecValue: {
      mutable_tensor_spec_value()->::tensorflow::TensorSpecProto::MergeFrom(from.tensor_spec_value());
      break;
    }
    case kListValue: {
      mutable_list_value()->::tensorflow::ListValue::MergeFrom(from.list_value());
      break;
    }
    case kTupleValue: {
      mutable_tuple_value()->::tensorflow::TupleValue::MergeFrom(from.tuple_value());
      break;
    }
    case kDictValue: {
      mutable_dict_value()->::tensorflow::DictValue::MergeFrom(from.dict_value());
      break;
    }
    case kNamedTupleValue: {
      mutable_named_tuple_value()->::tensorflow::NamedTupleValue::MergeFrom(from.named_tuple_value());
      break;
    }
    case KIND_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.StructuredValue)
}

void StructuredValue::SharedCtor() {
  clear_has_kind();
}

StructuredValue::~StructuredValue() {
  // @@protoc_insertion_point(destructor:tensorflow.StructuredValue)
  SharedDtor();
}

void StructuredValue::SharedDtor() {
  if (has_kind()) {
    clear_kind();
  }
}

void StructuredValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* StructuredValue::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const StructuredValue& StructuredValue::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::scc_info_DictValue.base);
  return *internal_default_instance();
}


void StructuredValue::clear_kind() {
// @@protoc_insertion_point(one_of_clear_start:tensorflow.StructuredValue)
  switch (kind_case()) {
    case kNoneValue: {
      delete kind_.none_value_;
      break;
    }
    case kFloat64Value: {
      // No need to clear
      break;
    }
    case kInt64Value: {
      // No need to clear
      break;
    }
    case kStringValue: {
      kind_.string_value_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
      break;
    }
    case kBoolValue: {
      // No need to clear
      break;
    }
    case kTensorShapeValue: {
      delete kind_.tensor_shape_value_;
      break;
    }
    case kTensorDtypeValue: {
      // No need to clear
      break;
    }
    case kTensorSpecValue: {
      delete kind_.tensor_spec_value_;
      break;
    }
    case kListValue: {
      delete kind_.list_value_;
      break;
    }
    case kTupleValue: {
      delete kind_.tuple_value_;
      break;
    }
    case kDictValue: {
      delete kind_.dict_value_;
      break;
    }
    case kNamedTupleValue: {
      delete kind_.named_tuple_value_;
      break;
    }
    case KIND_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = KIND_NOT_SET;
}


void StructuredValue::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.StructuredValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_kind();
  _internal_metadata_.Clear();
}

bool StructuredValue::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.StructuredValue)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(16383u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.NoneValue none_value = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_none_value()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double float64_value = 11;
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(89u /* 89 & 0xFF */)) {
          clear_kind();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &kind_.float64_value_)));
          set_has_float64_value();
        } else {
          goto handle_unusual;
        }
        break;
      }

      // sint64 int64_value = 12;
      case 12: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(96u /* 96 & 0xFF */)) {
          clear_kind();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_SINT64>(
                 input, &kind_.int64_value_)));
          set_has_int64_value();
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string string_value = 13;
      case 13: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(106u /* 106 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_string_value()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->string_value().data(), static_cast<int>(this->string_value().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.StructuredValue.string_value"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool bool_value = 14;
      case 14: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(112u /* 112 & 0xFF */)) {
          clear_kind();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &kind_.bool_value_)));
          set_has_bool_value();
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorShapeProto tensor_shape_value = 31;
      case 31: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(250u /* 250 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_tensor_shape_value()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.DataType tensor_dtype_value = 32;
      case 32: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(0u /* 256 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_tensor_dtype_value(static_cast< ::tensorflow::DataType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorSpecProto tensor_spec_value = 33;
      case 33: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 266 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_tensor_spec_value()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.ListValue list_value = 51;
      case 51: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(154u /* 410 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_list_value()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TupleValue tuple_value = 52;
      case 52: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(162u /* 418 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_tuple_value()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.DictValue dict_value = 53;
      case 53: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(170u /* 426 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_dict_value()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.NamedTupleValue named_tuple_value = 54;
      case 54: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(178u /* 434 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_named_tuple_value()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.StructuredValue)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.StructuredValue)
  return false;
#undef DO_
}

void StructuredValue::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.StructuredValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.NoneValue none_value = 1;
  if (has_none_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->_internal_none_value(), output);
  }

  // double float64_value = 11;
  if (has_float64_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(11, this->float64_value(), output);
  }

  // sint64 int64_value = 12;
  if (has_int64_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteSInt64(12, this->int64_value(), output);
  }

  // string string_value = 13;
  if (has_string_value()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->string_value().data(), static_cast<int>(this->string_value().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.StructuredValue.string_value");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      13, this->string_value(), output);
  }

  // bool bool_value = 14;
  if (has_bool_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(14, this->bool_value(), output);
  }

  // .tensorflow.TensorShapeProto tensor_shape_value = 31;
  if (has_tensor_shape_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      31, this->_internal_tensor_shape_value(), output);
  }

  // .tensorflow.DataType tensor_dtype_value = 32;
  if (has_tensor_dtype_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      32, this->tensor_dtype_value(), output);
  }

  // .tensorflow.TensorSpecProto tensor_spec_value = 33;
  if (has_tensor_spec_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      33, this->_internal_tensor_spec_value(), output);
  }

  // .tensorflow.ListValue list_value = 51;
  if (has_list_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      51, this->_internal_list_value(), output);
  }

  // .tensorflow.TupleValue tuple_value = 52;
  if (has_tuple_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      52, this->_internal_tuple_value(), output);
  }

  // .tensorflow.DictValue dict_value = 53;
  if (has_dict_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      53, this->_internal_dict_value(), output);
  }

  // .tensorflow.NamedTupleValue named_tuple_value = 54;
  if (has_named_tuple_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      54, this->_internal_named_tuple_value(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.StructuredValue)
}

::google::protobuf::uint8* StructuredValue::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.StructuredValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.NoneValue none_value = 1;
  if (has_none_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->_internal_none_value(), deterministic, target);
  }

  // double float64_value = 11;
  if (has_float64_value()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(11, this->float64_value(), target);
  }

  // sint64 int64_value = 12;
  if (has_int64_value()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteSInt64ToArray(12, this->int64_value(), target);
  }

  // string string_value = 13;
  if (has_string_value()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->string_value().data(), static_cast<int>(this->string_value().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.StructuredValue.string_value");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        13, this->string_value(), target);
  }

  // bool bool_value = 14;
  if (has_bool_value()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(14, this->bool_value(), target);
  }

  // .tensorflow.TensorShapeProto tensor_shape_value = 31;
  if (has_tensor_shape_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        31, this->_internal_tensor_shape_value(), deterministic, target);
  }

  // .tensorflow.DataType tensor_dtype_value = 32;
  if (has_tensor_dtype_value()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      32, this->tensor_dtype_value(), target);
  }

  // .tensorflow.TensorSpecProto tensor_spec_value = 33;
  if (has_tensor_spec_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        33, this->_internal_tensor_spec_value(), deterministic, target);
  }

  // .tensorflow.ListValue list_value = 51;
  if (has_list_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        51, this->_internal_list_value(), deterministic, target);
  }

  // .tensorflow.TupleValue tuple_value = 52;
  if (has_tuple_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        52, this->_internal_tuple_value(), deterministic, target);
  }

  // .tensorflow.DictValue dict_value = 53;
  if (has_dict_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        53, this->_internal_dict_value(), deterministic, target);
  }

  // .tensorflow.NamedTupleValue named_tuple_value = 54;
  if (has_named_tuple_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        54, this->_internal_named_tuple_value(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.StructuredValue)
  return target;
}

size_t StructuredValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.StructuredValue)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  switch (kind_case()) {
    // .tensorflow.NoneValue none_value = 1;
    case kNoneValue: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *kind_.none_value_);
      break;
    }
    // double float64_value = 11;
    case kFloat64Value: {
      total_size += 1 + 8;
      break;
    }
    // sint64 int64_value = 12;
    case kInt64Value: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::SInt64Size(
          this->int64_value());
      break;
    }
    // string string_value = 13;
    case kStringValue: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->string_value());
      break;
    }
    // bool bool_value = 14;
    case kBoolValue: {
      total_size += 1 + 1;
      break;
    }
    // .tensorflow.TensorShapeProto tensor_shape_value = 31;
    case kTensorShapeValue: {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *kind_.tensor_shape_value_);
      break;
    }
    // .tensorflow.DataType tensor_dtype_value = 32;
    case kTensorDtypeValue: {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::EnumSize(this->tensor_dtype_value());
      break;
    }
    // .tensorflow.TensorSpecProto tensor_spec_value = 33;
    case kTensorSpecValue: {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *kind_.tensor_spec_value_);
      break;
    }
    // .tensorflow.ListValue list_value = 51;
    case kListValue: {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *kind_.list_value_);
      break;
    }
    // .tensorflow.TupleValue tuple_value = 52;
    case kTupleValue: {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *kind_.tuple_value_);
      break;
    }
    // .tensorflow.DictValue dict_value = 53;
    case kDictValue: {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *kind_.dict_value_);
      break;
    }
    // .tensorflow.NamedTupleValue named_tuple_value = 54;
    case kNamedTupleValue: {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *kind_.named_tuple_value_);
      break;
    }
    case KIND_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void StructuredValue::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.StructuredValue)
  GOOGLE_DCHECK_NE(&from, this);
  const StructuredValue* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const StructuredValue>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.StructuredValue)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.StructuredValue)
    MergeFrom(*source);
  }
}

void StructuredValue::MergeFrom(const StructuredValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.StructuredValue)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.kind_case()) {
    case kNoneValue: {
      mutable_none_value()->::tensorflow::NoneValue::MergeFrom(from.none_value());
      break;
    }
    case kFloat64Value: {
      set_float64_value(from.float64_value());
      break;
    }
    case kInt64Value: {
      set_int64_value(from.int64_value());
      break;
    }
    case kStringValue: {
      set_string_value(from.string_value());
      break;
    }
    case kBoolValue: {
      set_bool_value(from.bool_value());
      break;
    }
    case kTensorShapeValue: {
      mutable_tensor_shape_value()->::tensorflow::TensorShapeProto::MergeFrom(from.tensor_shape_value());
      break;
    }
    case kTensorDtypeValue: {
      set_tensor_dtype_value(from.tensor_dtype_value());
      break;
    }
    case kTensorSpecValue: {
      mutable_tensor_spec_value()->::tensorflow::TensorSpecProto::MergeFrom(from.tensor_spec_value());
      break;
    }
    case kListValue: {
      mutable_list_value()->::tensorflow::ListValue::MergeFrom(from.list_value());
      break;
    }
    case kTupleValue: {
      mutable_tuple_value()->::tensorflow::TupleValue::MergeFrom(from.tuple_value());
      break;
    }
    case kDictValue: {
      mutable_dict_value()->::tensorflow::DictValue::MergeFrom(from.dict_value());
      break;
    }
    case kNamedTupleValue: {
      mutable_named_tuple_value()->::tensorflow::NamedTupleValue::MergeFrom(from.named_tuple_value());
      break;
    }
    case KIND_NOT_SET: {
      break;
    }
  }
}

void StructuredValue::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.StructuredValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StructuredValue::CopyFrom(const StructuredValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.StructuredValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StructuredValue::IsInitialized() const {
  return true;
}

void StructuredValue::Swap(StructuredValue* other) {
  if (other == this) return;
  InternalSwap(other);
}
void StructuredValue::InternalSwap(StructuredValue* other) {
  using std::swap;
  swap(kind_, other->kind_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata StructuredValue::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void NoneValue::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

NoneValue::NoneValue()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::scc_info_NoneValue.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.NoneValue)
}
NoneValue::NoneValue(const NoneValue& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.NoneValue)
}

void NoneValue::SharedCtor() {
}

NoneValue::~NoneValue() {
  // @@protoc_insertion_point(destructor:tensorflow.NoneValue)
  SharedDtor();
}

void NoneValue::SharedDtor() {
}

void NoneValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* NoneValue::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const NoneValue& NoneValue::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::scc_info_NoneValue.base);
  return *internal_default_instance();
}


void NoneValue::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.NoneValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_metadata_.Clear();
}

bool NoneValue::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.NoneValue)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, _internal_metadata_.mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.NoneValue)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.NoneValue)
  return false;
#undef DO_
}

void NoneValue::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.NoneValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.NoneValue)
}

::google::protobuf::uint8* NoneValue::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.NoneValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.NoneValue)
  return target;
}

size_t NoneValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.NoneValue)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void NoneValue::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.NoneValue)
  GOOGLE_DCHECK_NE(&from, this);
  const NoneValue* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const NoneValue>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.NoneValue)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.NoneValue)
    MergeFrom(*source);
  }
}

void NoneValue::MergeFrom(const NoneValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.NoneValue)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

}

void NoneValue::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.NoneValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void NoneValue::CopyFrom(const NoneValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.NoneValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool NoneValue::IsInitialized() const {
  return true;
}

void NoneValue::Swap(NoneValue* other) {
  if (other == this) return;
  InternalSwap(other);
}
void NoneValue::InternalSwap(NoneValue* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata NoneValue::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ListValue::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ListValue::kValuesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ListValue::ListValue()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::scc_info_DictValue.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.ListValue)
}
ListValue::ListValue(const ListValue& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      values_(from.values_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.ListValue)
}

void ListValue::SharedCtor() {
}

ListValue::~ListValue() {
  // @@protoc_insertion_point(destructor:tensorflow.ListValue)
  SharedDtor();
}

void ListValue::SharedDtor() {
}

void ListValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ListValue::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ListValue& ListValue::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::scc_info_DictValue.base);
  return *internal_default_instance();
}


void ListValue::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.ListValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  values_.Clear();
  _internal_metadata_.Clear();
}

bool ListValue::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.ListValue)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.StructuredValue values = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_values()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.ListValue)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.ListValue)
  return false;
#undef DO_
}

void ListValue::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.ListValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.StructuredValue values = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->values_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->values(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.ListValue)
}

::google::protobuf::uint8* ListValue::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.ListValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.StructuredValue values = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->values_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->values(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.ListValue)
  return target;
}

size_t ListValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.ListValue)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.StructuredValue values = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->values_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->values(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ListValue::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.ListValue)
  GOOGLE_DCHECK_NE(&from, this);
  const ListValue* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ListValue>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.ListValue)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.ListValue)
    MergeFrom(*source);
  }
}

void ListValue::MergeFrom(const ListValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.ListValue)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  values_.MergeFrom(from.values_);
}

void ListValue::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.ListValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ListValue::CopyFrom(const ListValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.ListValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ListValue::IsInitialized() const {
  return true;
}

void ListValue::Swap(ListValue* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ListValue::InternalSwap(ListValue* other) {
  using std::swap;
  CastToBase(&values_)->InternalSwap(CastToBase(&other->values_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ListValue::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void TupleValue::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TupleValue::kValuesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TupleValue::TupleValue()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::scc_info_DictValue.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.TupleValue)
}
TupleValue::TupleValue(const TupleValue& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      values_(from.values_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.TupleValue)
}

void TupleValue::SharedCtor() {
}

TupleValue::~TupleValue() {
  // @@protoc_insertion_point(destructor:tensorflow.TupleValue)
  SharedDtor();
}

void TupleValue::SharedDtor() {
}

void TupleValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* TupleValue::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const TupleValue& TupleValue::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::scc_info_DictValue.base);
  return *internal_default_instance();
}


void TupleValue::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.TupleValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  values_.Clear();
  _internal_metadata_.Clear();
}

bool TupleValue::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.TupleValue)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.StructuredValue values = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_values()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.TupleValue)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.TupleValue)
  return false;
#undef DO_
}

void TupleValue::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.TupleValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.StructuredValue values = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->values_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->values(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.TupleValue)
}

::google::protobuf::uint8* TupleValue::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.TupleValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.StructuredValue values = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->values_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->values(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.TupleValue)
  return target;
}

size_t TupleValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.TupleValue)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.StructuredValue values = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->values_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->values(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TupleValue::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.TupleValue)
  GOOGLE_DCHECK_NE(&from, this);
  const TupleValue* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TupleValue>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.TupleValue)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.TupleValue)
    MergeFrom(*source);
  }
}

void TupleValue::MergeFrom(const TupleValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.TupleValue)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  values_.MergeFrom(from.values_);
}

void TupleValue::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.TupleValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TupleValue::CopyFrom(const TupleValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.TupleValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TupleValue::IsInitialized() const {
  return true;
}

void TupleValue::Swap(TupleValue* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TupleValue::InternalSwap(TupleValue* other) {
  using std::swap;
  CastToBase(&values_)->InternalSwap(CastToBase(&other->values_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata TupleValue::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

DictValue_FieldsEntry_DoNotUse::DictValue_FieldsEntry_DoNotUse() {}
DictValue_FieldsEntry_DoNotUse::DictValue_FieldsEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void DictValue_FieldsEntry_DoNotUse::MergeFrom(const DictValue_FieldsEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata DictValue_FieldsEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::file_level_metadata[4];
}
void DictValue_FieldsEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void DictValue::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int DictValue::kFieldsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DictValue::DictValue()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::scc_info_DictValue.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.DictValue)
}
DictValue::DictValue(const DictValue& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  fields_.MergeFrom(from.fields_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.DictValue)
}

void DictValue::SharedCtor() {
}

DictValue::~DictValue() {
  // @@protoc_insertion_point(destructor:tensorflow.DictValue)
  SharedDtor();
}

void DictValue::SharedDtor() {
}

void DictValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* DictValue::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const DictValue& DictValue::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::scc_info_DictValue.base);
  return *internal_default_instance();
}


void DictValue::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.DictValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  fields_.Clear();
  _internal_metadata_.Clear();
}

bool DictValue::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.DictValue)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<string, .tensorflow.StructuredValue> fields = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DictValue_FieldsEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              DictValue_FieldsEntry_DoNotUse,
              ::std::string, ::tensorflow::StructuredValue,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::tensorflow::StructuredValue > > parser(&fields_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.DictValue.FieldsEntry.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.DictValue)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.DictValue)
  return false;
#undef DO_
}

void DictValue::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.DictValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, .tensorflow.StructuredValue> fields = 1;
  if (!this->fields().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::StructuredValue >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.DictValue.FieldsEntry.key");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->fields().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->fields().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::StructuredValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::StructuredValue >::const_iterator
          it = this->fields().begin();
          it != this->fields().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<DictValue_FieldsEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(fields_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<DictValue_FieldsEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::StructuredValue >::const_iterator
          it = this->fields().begin();
          it != this->fields().end(); ++it) {
        entry.reset(fields_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.DictValue)
}

::google::protobuf::uint8* DictValue::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.DictValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, .tensorflow.StructuredValue> fields = 1;
  if (!this->fields().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::StructuredValue >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.DictValue.FieldsEntry.key");
      }
    };

    if (deterministic &&
        this->fields().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->fields().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::StructuredValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::StructuredValue >::const_iterator
          it = this->fields().begin();
          it != this->fields().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<DictValue_FieldsEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(fields_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<DictValue_FieldsEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::StructuredValue >::const_iterator
          it = this->fields().begin();
          it != this->fields().end(); ++it) {
        entry.reset(fields_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.DictValue)
  return target;
}

size_t DictValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.DictValue)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // map<string, .tensorflow.StructuredValue> fields = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->fields_size());
  {
    ::std::unique_ptr<DictValue_FieldsEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::tensorflow::StructuredValue >::const_iterator
        it = this->fields().begin();
        it != this->fields().end(); ++it) {
      entry.reset(fields_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DictValue::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.DictValue)
  GOOGLE_DCHECK_NE(&from, this);
  const DictValue* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const DictValue>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.DictValue)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.DictValue)
    MergeFrom(*source);
  }
}

void DictValue::MergeFrom(const DictValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.DictValue)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  fields_.MergeFrom(from.fields_);
}

void DictValue::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.DictValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DictValue::CopyFrom(const DictValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.DictValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DictValue::IsInitialized() const {
  return true;
}

void DictValue::Swap(DictValue* other) {
  if (other == this) return;
  InternalSwap(other);
}
void DictValue::InternalSwap(DictValue* other) {
  using std::swap;
  fields_.Swap(&other->fields_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata DictValue::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void PairValue::InitAsDefaultInstance() {
  ::tensorflow::_PairValue_default_instance_._instance.get_mutable()->value_ = const_cast< ::tensorflow::StructuredValue*>(
      ::tensorflow::StructuredValue::internal_default_instance());
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int PairValue::kKeyFieldNumber;
const int PairValue::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

PairValue::PairValue()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::scc_info_DictValue.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.PairValue)
}
PairValue::PairValue(const PairValue& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  key_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.key().size() > 0) {
    key_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.key_);
  }
  if (from.has_value()) {
    value_ = new ::tensorflow::StructuredValue(*from.value_);
  } else {
    value_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.PairValue)
}

void PairValue::SharedCtor() {
  key_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  value_ = NULL;
}

PairValue::~PairValue() {
  // @@protoc_insertion_point(destructor:tensorflow.PairValue)
  SharedDtor();
}

void PairValue::SharedDtor() {
  key_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete value_;
}

void PairValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* PairValue::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const PairValue& PairValue::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::scc_info_DictValue.base);
  return *internal_default_instance();
}


void PairValue::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.PairValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  key_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && value_ != NULL) {
    delete value_;
  }
  value_ = NULL;
  _internal_metadata_.Clear();
}

bool PairValue::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.PairValue)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string key = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_key()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->key().data(), static_cast<int>(this->key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.PairValue.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.StructuredValue value = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_value()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.PairValue)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.PairValue)
  return false;
#undef DO_
}

void PairValue::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.PairValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string key = 1;
  if (this->key().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->key().data(), static_cast<int>(this->key().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.PairValue.key");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->key(), output);
  }

  // .tensorflow.StructuredValue value = 2;
  if (this->has_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_value(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.PairValue)
}

::google::protobuf::uint8* PairValue::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.PairValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string key = 1;
  if (this->key().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->key().data(), static_cast<int>(this->key().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.PairValue.key");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->key(), target);
  }

  // .tensorflow.StructuredValue value = 2;
  if (this->has_value()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_value(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.PairValue)
  return target;
}

size_t PairValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.PairValue)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string key = 1;
  if (this->key().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->key());
  }

  // .tensorflow.StructuredValue value = 2;
  if (this->has_value()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *value_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void PairValue::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.PairValue)
  GOOGLE_DCHECK_NE(&from, this);
  const PairValue* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const PairValue>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.PairValue)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.PairValue)
    MergeFrom(*source);
  }
}

void PairValue::MergeFrom(const PairValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.PairValue)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.key().size() > 0) {

    key_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.key_);
  }
  if (from.has_value()) {
    mutable_value()->::tensorflow::StructuredValue::MergeFrom(from.value());
  }
}

void PairValue::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.PairValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PairValue::CopyFrom(const PairValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.PairValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PairValue::IsInitialized() const {
  return true;
}

void PairValue::Swap(PairValue* other) {
  if (other == this) return;
  InternalSwap(other);
}
void PairValue::InternalSwap(PairValue* other) {
  using std::swap;
  key_.Swap(&other->key_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(value_, other->value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata PairValue::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void NamedTupleValue::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int NamedTupleValue::kNameFieldNumber;
const int NamedTupleValue::kValuesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

NamedTupleValue::NamedTupleValue()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::scc_info_DictValue.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.NamedTupleValue)
}
NamedTupleValue::NamedTupleValue(const NamedTupleValue& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      values_(from.values_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.NamedTupleValue)
}

void NamedTupleValue::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

NamedTupleValue::~NamedTupleValue() {
  // @@protoc_insertion_point(destructor:tensorflow.NamedTupleValue)
  SharedDtor();
}

void NamedTupleValue::SharedDtor() {
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void NamedTupleValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* NamedTupleValue::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const NamedTupleValue& NamedTupleValue::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::scc_info_DictValue.base);
  return *internal_default_instance();
}


void NamedTupleValue::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.NamedTupleValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  values_.Clear();
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _internal_metadata_.Clear();
}

bool NamedTupleValue::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.NamedTupleValue)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.NamedTupleValue.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.PairValue values = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_values()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.NamedTupleValue)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.NamedTupleValue)
  return false;
#undef DO_
}

void NamedTupleValue::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.NamedTupleValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.NamedTupleValue.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // repeated .tensorflow.PairValue values = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->values_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2,
      this->values(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.NamedTupleValue)
}

::google::protobuf::uint8* NamedTupleValue::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.NamedTupleValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.NamedTupleValue.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // repeated .tensorflow.PairValue values = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->values_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->values(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.NamedTupleValue)
  return target;
}

size_t NamedTupleValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.NamedTupleValue)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.PairValue values = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->values_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->values(static_cast<int>(i)));
    }
  }

  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void NamedTupleValue::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.NamedTupleValue)
  GOOGLE_DCHECK_NE(&from, this);
  const NamedTupleValue* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const NamedTupleValue>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.NamedTupleValue)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.NamedTupleValue)
    MergeFrom(*source);
  }
}

void NamedTupleValue::MergeFrom(const NamedTupleValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.NamedTupleValue)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  values_.MergeFrom(from.values_);
  if (from.name().size() > 0) {

    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
}

void NamedTupleValue::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.NamedTupleValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void NamedTupleValue::CopyFrom(const NamedTupleValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.NamedTupleValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool NamedTupleValue::IsInitialized() const {
  return true;
}

void NamedTupleValue::Swap(NamedTupleValue* other) {
  if (other == this) return;
  InternalSwap(other);
}
void NamedTupleValue::InternalSwap(NamedTupleValue* other) {
  using std::swap;
  CastToBase(&values_)->InternalSwap(CastToBase(&other->values_));
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata NamedTupleValue::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void TensorSpecProto::InitAsDefaultInstance() {
  ::tensorflow::_TensorSpecProto_default_instance_._instance.get_mutable()->shape_ = const_cast< ::tensorflow::TensorShapeProto*>(
      ::tensorflow::TensorShapeProto::internal_default_instance());
}
void TensorSpecProto::clear_shape() {
  if (GetArenaNoVirtual() == NULL && shape_ != NULL) {
    delete shape_;
  }
  shape_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TensorSpecProto::kNameFieldNumber;
const int TensorSpecProto::kShapeFieldNumber;
const int TensorSpecProto::kDtypeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TensorSpecProto::TensorSpecProto()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::scc_info_TensorSpecProto.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.TensorSpecProto)
}
TensorSpecProto::TensorSpecProto(const TensorSpecProto& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  if (from.has_shape()) {
    shape_ = new ::tensorflow::TensorShapeProto(*from.shape_);
  } else {
    shape_ = NULL;
  }
  dtype_ = from.dtype_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.TensorSpecProto)
}

void TensorSpecProto::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&shape_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&dtype_) -
      reinterpret_cast<char*>(&shape_)) + sizeof(dtype_));
}

TensorSpecProto::~TensorSpecProto() {
  // @@protoc_insertion_point(destructor:tensorflow.TensorSpecProto)
  SharedDtor();
}

void TensorSpecProto::SharedDtor() {
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete shape_;
}

void TensorSpecProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* TensorSpecProto::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const TensorSpecProto& TensorSpecProto::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::scc_info_TensorSpecProto.base);
  return *internal_default_instance();
}


void TensorSpecProto::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.TensorSpecProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && shape_ != NULL) {
    delete shape_;
  }
  shape_ = NULL;
  dtype_ = 0;
  _internal_metadata_.Clear();
}

bool TensorSpecProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.TensorSpecProto)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.TensorSpecProto.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorShapeProto shape = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_shape()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.DataType dtype = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_dtype(static_cast< ::tensorflow::DataType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.TensorSpecProto)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.TensorSpecProto)
  return false;
#undef DO_
}

void TensorSpecProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.TensorSpecProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TensorSpecProto.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // .tensorflow.TensorShapeProto shape = 2;
  if (this->has_shape()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_shape(), output);
  }

  // .tensorflow.DataType dtype = 3;
  if (this->dtype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      3, this->dtype(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.TensorSpecProto)
}

::google::protobuf::uint8* TensorSpecProto::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.TensorSpecProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TensorSpecProto.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // .tensorflow.TensorShapeProto shape = 2;
  if (this->has_shape()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_shape(), deterministic, target);
  }

  // .tensorflow.DataType dtype = 3;
  if (this->dtype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      3, this->dtype(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.TensorSpecProto)
  return target;
}

size_t TensorSpecProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.TensorSpecProto)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // .tensorflow.TensorShapeProto shape = 2;
  if (this->has_shape()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *shape_);
  }

  // .tensorflow.DataType dtype = 3;
  if (this->dtype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->dtype());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TensorSpecProto::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.TensorSpecProto)
  GOOGLE_DCHECK_NE(&from, this);
  const TensorSpecProto* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TensorSpecProto>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.TensorSpecProto)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.TensorSpecProto)
    MergeFrom(*source);
  }
}

void TensorSpecProto::MergeFrom(const TensorSpecProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.TensorSpecProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.name().size() > 0) {

    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  if (from.has_shape()) {
    mutable_shape()->::tensorflow::TensorShapeProto::MergeFrom(from.shape());
  }
  if (from.dtype() != 0) {
    set_dtype(from.dtype());
  }
}

void TensorSpecProto::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.TensorSpecProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TensorSpecProto::CopyFrom(const TensorSpecProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.TensorSpecProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TensorSpecProto::IsInitialized() const {
  return true;
}

void TensorSpecProto::Swap(TensorSpecProto* other) {
  if (other == this) return;
  InternalSwap(other);
}
void TensorSpecProto::InternalSwap(TensorSpecProto* other) {
  using std::swap;
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(shape_, other->shape_);
  swap(dtype_, other->dtype_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata TensorSpecProto::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::StructuredValue* Arena::CreateMaybeMessage< ::tensorflow::StructuredValue >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::StructuredValue >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::NoneValue* Arena::CreateMaybeMessage< ::tensorflow::NoneValue >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::NoneValue >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::ListValue* Arena::CreateMaybeMessage< ::tensorflow::ListValue >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::ListValue >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::TupleValue* Arena::CreateMaybeMessage< ::tensorflow::TupleValue >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::TupleValue >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::DictValue_FieldsEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::DictValue_FieldsEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::DictValue_FieldsEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::DictValue* Arena::CreateMaybeMessage< ::tensorflow::DictValue >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::DictValue >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::PairValue* Arena::CreateMaybeMessage< ::tensorflow::PairValue >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::PairValue >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::NamedTupleValue* Arena::CreateMaybeMessage< ::tensorflow::NamedTupleValue >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::NamedTupleValue >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::TensorSpecProto* Arena::CreateMaybeMessage< ::tensorflow::TensorSpecProto >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::TensorSpecProto >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

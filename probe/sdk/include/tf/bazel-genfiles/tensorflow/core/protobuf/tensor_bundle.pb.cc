// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/tensor_bundle.proto

#include "tensorflow/core/protobuf/tensor_bundle.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_TensorShapeProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fslice_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_5fslice_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_TensorSliceProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fslice_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2fversions_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fversions_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_VersionDef;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fversions_2eproto
namespace tensorflow {
class BundleHeaderProtoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<BundleHeaderProto>
      _instance;
} _BundleHeaderProto_default_instance_;
class BundleEntryProtoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<BundleEntryProto>
      _instance;
} _BundleEntryProto_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftensor_5fbundle_2eproto {
static void InitDefaultsBundleHeaderProto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_BundleHeaderProto_default_instance_;
    new (ptr) ::tensorflow::BundleHeaderProto();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::BundleHeaderProto::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_BundleHeaderProto =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsBundleHeaderProto}, {
      &protobuf_tensorflow_2fcore_2fframework_2fversions_2eproto::scc_info_VersionDef.base,}};

static void InitDefaultsBundleEntryProto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_BundleEntryProto_default_instance_;
    new (ptr) ::tensorflow::BundleEntryProto();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::BundleEntryProto::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_BundleEntryProto =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsBundleEntryProto}, {
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::scc_info_TensorShapeProto.base,
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_5fslice_2eproto::scc_info_TensorSliceProto.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_BundleHeaderProto.base);
  ::google::protobuf::internal::InitSCC(&scc_info_BundleEntryProto.base);
}

::google::protobuf::Metadata file_level_metadata[2];
const ::google::protobuf::EnumDescriptor* file_level_enum_descriptors[1];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BundleHeaderProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BundleHeaderProto, num_shards_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BundleHeaderProto, endianness_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BundleHeaderProto, version_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BundleEntryProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BundleEntryProto, dtype_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BundleEntryProto, shape_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BundleEntryProto, shard_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BundleEntryProto, offset_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BundleEntryProto, size_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BundleEntryProto, crc32c_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::BundleEntryProto, slices_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::BundleHeaderProto)},
  { 8, -1, sizeof(::tensorflow::BundleEntryProto)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_BundleHeaderProto_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_BundleEntryProto_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/protobuf/tensor_bundle.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, file_level_enum_descriptors, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 2);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n,tensorflow/core/protobuf/tensor_bundle"
      ".proto\022\ntensorflow\032,tensorflow/core/fram"
      "ework/tensor_shape.proto\032,tensorflow/cor"
      "e/framework/tensor_slice.proto\032%tensorfl"
      "ow/core/framework/types.proto\032(tensorflo"
      "w/core/framework/versions.proto\"\261\001\n\021Bund"
      "leHeaderProto\022\022\n\nnum_shards\030\001 \001(\005\022<\n\nend"
      "ianness\030\002 \001(\0162(.tensorflow.BundleHeaderP"
      "roto.Endianness\022\'\n\007version\030\003 \001(\0132\026.tenso"
      "rflow.VersionDef\"!\n\nEndianness\022\n\n\006LITTLE"
      "\020\000\022\007\n\003BIG\020\001\"\322\001\n\020BundleEntryProto\022#\n\005dtyp"
      "e\030\001 \001(\0162\024.tensorflow.DataType\022+\n\005shape\030\002"
      " \001(\0132\034.tensorflow.TensorShapeProto\022\020\n\010sh"
      "ard_id\030\003 \001(\005\022\016\n\006offset\030\004 \001(\003\022\014\n\004size\030\005 \001"
      "(\003\022\016\n\006crc32c\030\006 \001(\007\022,\n\006slices\030\007 \003(\0132\034.ten"
      "sorflow.TensorSliceProtoBl\n\023org.tensorfl"
      "ow.utilB\022TensorBundleProtosP\001Z<github.co"
      "m/tensorflow/tensorflow/tensorflow/go/co"
      "re/protobuf\370\001\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 742);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/protobuf/tensor_bundle.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_5fslice_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftypes_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2fversions_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftensor_5fbundle_2eproto
namespace tensorflow {
const ::google::protobuf::EnumDescriptor* BundleHeaderProto_Endianness_descriptor() {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftensor_5fbundle_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_tensorflow_2fcore_2fprotobuf_2ftensor_5fbundle_2eproto::file_level_enum_descriptors[0];
}
bool BundleHeaderProto_Endianness_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const BundleHeaderProto_Endianness BundleHeaderProto::LITTLE;
const BundleHeaderProto_Endianness BundleHeaderProto::BIG;
const BundleHeaderProto_Endianness BundleHeaderProto::Endianness_MIN;
const BundleHeaderProto_Endianness BundleHeaderProto::Endianness_MAX;
const int BundleHeaderProto::Endianness_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

// ===================================================================

void BundleHeaderProto::InitAsDefaultInstance() {
  ::tensorflow::_BundleHeaderProto_default_instance_._instance.get_mutable()->version_ = const_cast< ::tensorflow::VersionDef*>(
      ::tensorflow::VersionDef::internal_default_instance());
}
void BundleHeaderProto::unsafe_arena_set_allocated_version(
    ::tensorflow::VersionDef* version) {
  if (GetArenaNoVirtual() == NULL) {
    delete version_;
  }
  version_ = version;
  if (version) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.BundleHeaderProto.version)
}
void BundleHeaderProto::clear_version() {
  if (GetArenaNoVirtual() == NULL && version_ != NULL) {
    delete version_;
  }
  version_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int BundleHeaderProto::kNumShardsFieldNumber;
const int BundleHeaderProto::kEndiannessFieldNumber;
const int BundleHeaderProto::kVersionFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

BundleHeaderProto::BundleHeaderProto()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftensor_5fbundle_2eproto::scc_info_BundleHeaderProto.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.BundleHeaderProto)
}
BundleHeaderProto::BundleHeaderProto(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftensor_5fbundle_2eproto::scc_info_BundleHeaderProto.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.BundleHeaderProto)
}
BundleHeaderProto::BundleHeaderProto(const BundleHeaderProto& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_version()) {
    version_ = new ::tensorflow::VersionDef(*from.version_);
  } else {
    version_ = NULL;
  }
  ::memcpy(&num_shards_, &from.num_shards_,
    static_cast<size_t>(reinterpret_cast<char*>(&endianness_) -
    reinterpret_cast<char*>(&num_shards_)) + sizeof(endianness_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.BundleHeaderProto)
}

void BundleHeaderProto::SharedCtor() {
  ::memset(&version_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&endianness_) -
      reinterpret_cast<char*>(&version_)) + sizeof(endianness_));
}

BundleHeaderProto::~BundleHeaderProto() {
  // @@protoc_insertion_point(destructor:tensorflow.BundleHeaderProto)
  SharedDtor();
}

void BundleHeaderProto::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (this != internal_default_instance()) delete version_;
}

void BundleHeaderProto::ArenaDtor(void* object) {
  BundleHeaderProto* _this = reinterpret_cast< BundleHeaderProto* >(object);
  (void)_this;
}
void BundleHeaderProto::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void BundleHeaderProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* BundleHeaderProto::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftensor_5fbundle_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftensor_5fbundle_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const BundleHeaderProto& BundleHeaderProto::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftensor_5fbundle_2eproto::scc_info_BundleHeaderProto.base);
  return *internal_default_instance();
}


void BundleHeaderProto::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.BundleHeaderProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && version_ != NULL) {
    delete version_;
  }
  version_ = NULL;
  ::memset(&num_shards_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&endianness_) -
      reinterpret_cast<char*>(&num_shards_)) + sizeof(endianness_));
  _internal_metadata_.Clear();
}

bool BundleHeaderProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.BundleHeaderProto)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 num_shards = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &num_shards_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.BundleHeaderProto.Endianness endianness = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_endianness(static_cast< ::tensorflow::BundleHeaderProto_Endianness >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.VersionDef version = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_version()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.BundleHeaderProto)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.BundleHeaderProto)
  return false;
#undef DO_
}

void BundleHeaderProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.BundleHeaderProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 num_shards = 1;
  if (this->num_shards() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->num_shards(), output);
  }

  // .tensorflow.BundleHeaderProto.Endianness endianness = 2;
  if (this->endianness() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      2, this->endianness(), output);
  }

  // .tensorflow.VersionDef version = 3;
  if (this->has_version()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->_internal_version(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.BundleHeaderProto)
}

::google::protobuf::uint8* BundleHeaderProto::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.BundleHeaderProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 num_shards = 1;
  if (this->num_shards() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->num_shards(), target);
  }

  // .tensorflow.BundleHeaderProto.Endianness endianness = 2;
  if (this->endianness() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      2, this->endianness(), target);
  }

  // .tensorflow.VersionDef version = 3;
  if (this->has_version()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->_internal_version(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.BundleHeaderProto)
  return target;
}

size_t BundleHeaderProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.BundleHeaderProto)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .tensorflow.VersionDef version = 3;
  if (this->has_version()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *version_);
  }

  // int32 num_shards = 1;
  if (this->num_shards() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->num_shards());
  }

  // .tensorflow.BundleHeaderProto.Endianness endianness = 2;
  if (this->endianness() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->endianness());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void BundleHeaderProto::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.BundleHeaderProto)
  GOOGLE_DCHECK_NE(&from, this);
  const BundleHeaderProto* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const BundleHeaderProto>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.BundleHeaderProto)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.BundleHeaderProto)
    MergeFrom(*source);
  }
}

void BundleHeaderProto::MergeFrom(const BundleHeaderProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.BundleHeaderProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_version()) {
    mutable_version()->::tensorflow::VersionDef::MergeFrom(from.version());
  }
  if (from.num_shards() != 0) {
    set_num_shards(from.num_shards());
  }
  if (from.endianness() != 0) {
    set_endianness(from.endianness());
  }
}

void BundleHeaderProto::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.BundleHeaderProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void BundleHeaderProto::CopyFrom(const BundleHeaderProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.BundleHeaderProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BundleHeaderProto::IsInitialized() const {
  return true;
}

void BundleHeaderProto::Swap(BundleHeaderProto* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    BundleHeaderProto* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void BundleHeaderProto::UnsafeArenaSwap(BundleHeaderProto* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void BundleHeaderProto::InternalSwap(BundleHeaderProto* other) {
  using std::swap;
  swap(version_, other->version_);
  swap(num_shards_, other->num_shards_);
  swap(endianness_, other->endianness_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata BundleHeaderProto::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftensor_5fbundle_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftensor_5fbundle_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void BundleEntryProto::InitAsDefaultInstance() {
  ::tensorflow::_BundleEntryProto_default_instance_._instance.get_mutable()->shape_ = const_cast< ::tensorflow::TensorShapeProto*>(
      ::tensorflow::TensorShapeProto::internal_default_instance());
}
void BundleEntryProto::unsafe_arena_set_allocated_shape(
    ::tensorflow::TensorShapeProto* shape) {
  if (GetArenaNoVirtual() == NULL) {
    delete shape_;
  }
  shape_ = shape;
  if (shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.BundleEntryProto.shape)
}
void BundleEntryProto::clear_shape() {
  if (GetArenaNoVirtual() == NULL && shape_ != NULL) {
    delete shape_;
  }
  shape_ = NULL;
}
void BundleEntryProto::clear_slices() {
  slices_.Clear();
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int BundleEntryProto::kDtypeFieldNumber;
const int BundleEntryProto::kShapeFieldNumber;
const int BundleEntryProto::kShardIdFieldNumber;
const int BundleEntryProto::kOffsetFieldNumber;
const int BundleEntryProto::kSizeFieldNumber;
const int BundleEntryProto::kCrc32CFieldNumber;
const int BundleEntryProto::kSlicesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

BundleEntryProto::BundleEntryProto()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftensor_5fbundle_2eproto::scc_info_BundleEntryProto.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.BundleEntryProto)
}
BundleEntryProto::BundleEntryProto(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  slices_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftensor_5fbundle_2eproto::scc_info_BundleEntryProto.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.BundleEntryProto)
}
BundleEntryProto::BundleEntryProto(const BundleEntryProto& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      slices_(from.slices_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_shape()) {
    shape_ = new ::tensorflow::TensorShapeProto(*from.shape_);
  } else {
    shape_ = NULL;
  }
  ::memcpy(&dtype_, &from.dtype_,
    static_cast<size_t>(reinterpret_cast<char*>(&crc32c_) -
    reinterpret_cast<char*>(&dtype_)) + sizeof(crc32c_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.BundleEntryProto)
}

void BundleEntryProto::SharedCtor() {
  ::memset(&shape_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&crc32c_) -
      reinterpret_cast<char*>(&shape_)) + sizeof(crc32c_));
}

BundleEntryProto::~BundleEntryProto() {
  // @@protoc_insertion_point(destructor:tensorflow.BundleEntryProto)
  SharedDtor();
}

void BundleEntryProto::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (this != internal_default_instance()) delete shape_;
}

void BundleEntryProto::ArenaDtor(void* object) {
  BundleEntryProto* _this = reinterpret_cast< BundleEntryProto* >(object);
  (void)_this;
}
void BundleEntryProto::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void BundleEntryProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* BundleEntryProto::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftensor_5fbundle_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftensor_5fbundle_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const BundleEntryProto& BundleEntryProto::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftensor_5fbundle_2eproto::scc_info_BundleEntryProto.base);
  return *internal_default_instance();
}


void BundleEntryProto::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.BundleEntryProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  slices_.Clear();
  if (GetArenaNoVirtual() == NULL && shape_ != NULL) {
    delete shape_;
  }
  shape_ = NULL;
  ::memset(&dtype_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&crc32c_) -
      reinterpret_cast<char*>(&dtype_)) + sizeof(crc32c_));
  _internal_metadata_.Clear();
}

bool BundleEntryProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.BundleEntryProto)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.DataType dtype = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_dtype(static_cast< ::tensorflow::DataType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorShapeProto shape = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_shape()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 shard_id = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &shard_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 offset = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &offset_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 size = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // fixed32 crc32c = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(53u /* 53 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_FIXED32>(
                 input, &crc32c_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.TensorSliceProto slices = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_slices()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.BundleEntryProto)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.BundleEntryProto)
  return false;
#undef DO_
}

void BundleEntryProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.BundleEntryProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.DataType dtype = 1;
  if (this->dtype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->dtype(), output);
  }

  // .tensorflow.TensorShapeProto shape = 2;
  if (this->has_shape()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_shape(), output);
  }

  // int32 shard_id = 3;
  if (this->shard_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->shard_id(), output);
  }

  // int64 offset = 4;
  if (this->offset() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->offset(), output);
  }

  // int64 size = 5;
  if (this->size() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(5, this->size(), output);
  }

  // fixed32 crc32c = 6;
  if (this->crc32c() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFixed32(6, this->crc32c(), output);
  }

  // repeated .tensorflow.TensorSliceProto slices = 7;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->slices_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7,
      this->slices(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.BundleEntryProto)
}

::google::protobuf::uint8* BundleEntryProto::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.BundleEntryProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.DataType dtype = 1;
  if (this->dtype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->dtype(), target);
  }

  // .tensorflow.TensorShapeProto shape = 2;
  if (this->has_shape()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_shape(), deterministic, target);
  }

  // int32 shard_id = 3;
  if (this->shard_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->shard_id(), target);
  }

  // int64 offset = 4;
  if (this->offset() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->offset(), target);
  }

  // int64 size = 5;
  if (this->size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(5, this->size(), target);
  }

  // fixed32 crc32c = 6;
  if (this->crc32c() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFixed32ToArray(6, this->crc32c(), target);
  }

  // repeated .tensorflow.TensorSliceProto slices = 7;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->slices_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        7, this->slices(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.BundleEntryProto)
  return target;
}

size_t BundleEntryProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.BundleEntryProto)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.TensorSliceProto slices = 7;
  {
    unsigned int count = static_cast<unsigned int>(this->slices_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->slices(static_cast<int>(i)));
    }
  }

  // .tensorflow.TensorShapeProto shape = 2;
  if (this->has_shape()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *shape_);
  }

  // .tensorflow.DataType dtype = 1;
  if (this->dtype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->dtype());
  }

  // int32 shard_id = 3;
  if (this->shard_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->shard_id());
  }

  // int64 offset = 4;
  if (this->offset() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->offset());
  }

  // int64 size = 5;
  if (this->size() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->size());
  }

  // fixed32 crc32c = 6;
  if (this->crc32c() != 0) {
    total_size += 1 + 4;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void BundleEntryProto::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.BundleEntryProto)
  GOOGLE_DCHECK_NE(&from, this);
  const BundleEntryProto* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const BundleEntryProto>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.BundleEntryProto)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.BundleEntryProto)
    MergeFrom(*source);
  }
}

void BundleEntryProto::MergeFrom(const BundleEntryProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.BundleEntryProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  slices_.MergeFrom(from.slices_);
  if (from.has_shape()) {
    mutable_shape()->::tensorflow::TensorShapeProto::MergeFrom(from.shape());
  }
  if (from.dtype() != 0) {
    set_dtype(from.dtype());
  }
  if (from.shard_id() != 0) {
    set_shard_id(from.shard_id());
  }
  if (from.offset() != 0) {
    set_offset(from.offset());
  }
  if (from.size() != 0) {
    set_size(from.size());
  }
  if (from.crc32c() != 0) {
    set_crc32c(from.crc32c());
  }
}

void BundleEntryProto::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.BundleEntryProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void BundleEntryProto::CopyFrom(const BundleEntryProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.BundleEntryProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BundleEntryProto::IsInitialized() const {
  return true;
}

void BundleEntryProto::Swap(BundleEntryProto* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    BundleEntryProto* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void BundleEntryProto::UnsafeArenaSwap(BundleEntryProto* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void BundleEntryProto::InternalSwap(BundleEntryProto* other) {
  using std::swap;
  CastToBase(&slices_)->InternalSwap(CastToBase(&other->slices_));
  swap(shape_, other->shape_);
  swap(dtype_, other->dtype_);
  swap(shard_id_, other->shard_id_);
  swap(offset_, other->offset_);
  swap(size_, other->size_);
  swap(crc32c_, other->crc32c_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata BundleEntryProto::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftensor_5fbundle_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftensor_5fbundle_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::BundleHeaderProto* Arena::CreateMaybeMessage< ::tensorflow::BundleHeaderProto >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::BundleHeaderProto >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::BundleEntryProto* Arena::CreateMaybeMessage< ::tensorflow::BundleEntryProto >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::BundleEntryProto >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

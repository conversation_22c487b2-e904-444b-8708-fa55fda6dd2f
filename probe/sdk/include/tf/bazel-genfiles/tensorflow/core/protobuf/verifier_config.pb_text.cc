// GENERATED FILE - DO NOT MODIFY

#include <algorithm>

#include "tensorflow/core/protobuf/verifier_config.pb_text-impl.h"

using ::tensorflow::strings::Scanner;
using ::tensorflow::strings::StrCat;

namespace tensorflow {

const char* EnumName_VerifierConfig_Toggle(
    ::tensorflow::VerifierConfig_Toggle value) {
  switch (value) {
    case 0: return "DEFAULT";
    case 1: return "ON";
    case 2: return "OFF";
    default: return "";
  }
}

string ProtoDebugString(
    const ::tensorflow::VerifierConfig& msg) {
  string s;
  ::tensorflow::strings::ProtoTextOutput o(&s, false);
  internal::AppendProtoDebugString(&o, msg);
  o.CloseTopMessage();
  return s;
}

string ProtoShortDebugString(
    const ::tensorflow::VerifierConfig& msg) {
  string s;
  ::tensorflow::strings::ProtoTextOutput o(&s, true);
  internal::AppendProtoDebugString(&o, msg);
  o.CloseTopMessage();
  return s;
}

namespace internal {

void AppendProtoDebugString(
    ::tensorflow::strings::ProtoTextOutput* o,
    const ::tensorflow::VerifierConfig& msg) {
  o->AppendNumericIfNotZero("verification_timeout_in_ms", msg.verification_timeout_in_ms());
  if (msg.structure_verifier() != 0) {
    const char* enum_name = ::tensorflow::EnumName_VerifierConfig_Toggle(msg.structure_verifier());
    if (enum_name[0]) {
      o->AppendEnumName("structure_verifier", enum_name);
    } else {
      o->AppendNumeric("structure_verifier", msg.structure_verifier());
    }
  }
}

}  // namespace internal

bool ProtoParseFromString(
    const string& s,
    ::tensorflow::VerifierConfig* msg) {
  msg->Clear();
  Scanner scanner(s);
  if (!internal::ProtoParseFromScanner(&scanner, false, false, msg)) return false;
  scanner.Eos();
  return scanner.GetResult();
}

namespace internal {

bool ProtoParseFromScanner(
    ::tensorflow::strings::Scanner* scanner, bool nested, bool close_curly,
    ::tensorflow::VerifierConfig* msg) {
  std::vector<bool> has_seen(2, false);
  while(true) {
    ProtoSpaceAndComments(scanner);
    if (nested && (scanner->Peek() == (close_curly ? '}' : '>'))) {
      scanner->One(Scanner::ALL);
      ProtoSpaceAndComments(scanner);
      return true;
    }
    if (!nested && scanner->empty()) { return true; }
    scanner->RestartCapture()
        .Many(Scanner::LETTER_DIGIT_UNDERSCORE)
        .StopCapture();
    StringPiece identifier;
    if (!scanner->GetResult(nullptr, &identifier)) return false;
    bool parsed_colon = false;
    (void)parsed_colon;
    ProtoSpaceAndComments(scanner);
    if (scanner->Peek() == ':') {
      parsed_colon = true;
      scanner->One(Scanner::ALL);
      ProtoSpaceAndComments(scanner);
    }
    if (identifier == "verification_timeout_in_ms") {
      if (has_seen[0]) return false;
      has_seen[0] = true;
      int64 value;
      if (!parsed_colon || !::tensorflow::strings::ProtoParseNumericFromScanner(scanner, &value)) return false;
      msg->set_verification_timeout_in_ms(value);
    }
    else if (identifier == "structure_verifier") {
      if (has_seen[1]) return false;
      has_seen[1] = true;
      StringPiece value;
      if (!parsed_colon || !scanner->RestartCapture().Many(Scanner::LETTER_DIGIT_DASH_UNDERSCORE).GetResult(nullptr, &value)) return false;
      if (value == "DEFAULT") {
        msg->set_structure_verifier(::tensorflow::VerifierConfig_Toggle_DEFAULT);
      } else if (value == "ON") {
        msg->set_structure_verifier(::tensorflow::VerifierConfig_Toggle_ON);
      } else if (value == "OFF") {
        msg->set_structure_verifier(::tensorflow::VerifierConfig_Toggle_OFF);
      } else {
        int32 int_value;
        if (strings::SafeStringToNumeric(value, &int_value)) {
          msg->set_structure_verifier(static_cast<::tensorflow::VerifierConfig_Toggle>(int_value));
        } else {
          return false;
        }
      }
    }
  }
}

}  // namespace internal

}  // namespace tensorflow

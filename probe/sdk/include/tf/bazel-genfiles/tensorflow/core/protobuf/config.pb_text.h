// GENERATED FILE - DO NOT MODIFY
#ifndef tensorflow_core_protobuf_config_proto_H_
#define tensorflow_core_protobuf_config_proto_H_

#include "tensorflow/core/protobuf/config.pb.h"
#include "tensorflow/core/platform/macros.h"
#include "tensorflow/core/platform/protobuf.h"
#include "tensorflow/core/platform/types.h"

namespace tensorflow {

// Message-text conversion for tensorflow.GPUOptions.Experimental.VirtualDevices
string ProtoDebugString(
    const ::tensorflow::GPUOptions_Experimental_VirtualDevices& msg);
string ProtoShortDebugString(
    const ::tensorflow::GPUOptions_Experimental_VirtualDevices& msg);
bool ProtoParseFromString(
    const string& s,
    ::tensorflow::GPUOptions_Experimental_VirtualDevices* msg)
        TF_MUST_USE_RESULT;

// Message-text conversion for tensorflow.GPUOptions.Experimental
string ProtoDebugString(
    const ::tensorflow::GPUOptions_Experimental& msg);
string ProtoShortDebugString(
    const ::tensorflow::GPUOptions_Experimental& msg);
bool ProtoParseFromString(
    const string& s,
    ::tensorflow::GPUOptions_Experimental* msg)
        TF_MUST_USE_RESULT;

// Message-text conversion for tensorflow.GPUOptions
string ProtoDebugString(
    const ::tensorflow::GPUOptions& msg);
string ProtoShortDebugString(
    const ::tensorflow::GPUOptions& msg);
bool ProtoParseFromString(
    const string& s,
    ::tensorflow::GPUOptions* msg)
        TF_MUST_USE_RESULT;

// Enum text output for tensorflow.OptimizerOptions.Level
const char* EnumName_OptimizerOptions_Level(
    ::tensorflow::OptimizerOptions_Level value);

// Enum text output for tensorflow.OptimizerOptions.GlobalJitLevel
const char* EnumName_OptimizerOptions_GlobalJitLevel(
    ::tensorflow::OptimizerOptions_GlobalJitLevel value);

// Message-text conversion for tensorflow.OptimizerOptions
string ProtoDebugString(
    const ::tensorflow::OptimizerOptions& msg);
string ProtoShortDebugString(
    const ::tensorflow::OptimizerOptions& msg);
bool ProtoParseFromString(
    const string& s,
    ::tensorflow::OptimizerOptions* msg)
        TF_MUST_USE_RESULT;

// Message-text conversion for tensorflow.GraphOptions
string ProtoDebugString(
    const ::tensorflow::GraphOptions& msg);
string ProtoShortDebugString(
    const ::tensorflow::GraphOptions& msg);
bool ProtoParseFromString(
    const string& s,
    ::tensorflow::GraphOptions* msg)
        TF_MUST_USE_RESULT;

// Message-text conversion for tensorflow.ThreadPoolOptionProto
string ProtoDebugString(
    const ::tensorflow::ThreadPoolOptionProto& msg);
string ProtoShortDebugString(
    const ::tensorflow::ThreadPoolOptionProto& msg);
bool ProtoParseFromString(
    const string& s,
    ::tensorflow::ThreadPoolOptionProto* msg)
        TF_MUST_USE_RESULT;

// Message-text conversion for tensorflow.RPCOptions
string ProtoDebugString(
    const ::tensorflow::RPCOptions& msg);
string ProtoShortDebugString(
    const ::tensorflow::RPCOptions& msg);
bool ProtoParseFromString(
    const string& s,
    ::tensorflow::RPCOptions* msg)
        TF_MUST_USE_RESULT;

// Message-text conversion for tensorflow.ConfigProto.Experimental
string ProtoDebugString(
    const ::tensorflow::ConfigProto_Experimental& msg);
string ProtoShortDebugString(
    const ::tensorflow::ConfigProto_Experimental& msg);
bool ProtoParseFromString(
    const string& s,
    ::tensorflow::ConfigProto_Experimental* msg)
        TF_MUST_USE_RESULT;

// Message-text conversion for tensorflow.ConfigProto
string ProtoDebugString(
    const ::tensorflow::ConfigProto& msg);
string ProtoShortDebugString(
    const ::tensorflow::ConfigProto& msg);
bool ProtoParseFromString(
    const string& s,
    ::tensorflow::ConfigProto* msg)
        TF_MUST_USE_RESULT;

// Enum text output for tensorflow.RunOptions.TraceLevel
const char* EnumName_RunOptions_TraceLevel(
    ::tensorflow::RunOptions_TraceLevel value);

// Message-text conversion for tensorflow.RunOptions.Experimental
string ProtoDebugString(
    const ::tensorflow::RunOptions_Experimental& msg);
string ProtoShortDebugString(
    const ::tensorflow::RunOptions_Experimental& msg);
bool ProtoParseFromString(
    const string& s,
    ::tensorflow::RunOptions_Experimental* msg)
        TF_MUST_USE_RESULT;

// Message-text conversion for tensorflow.RunOptions
string ProtoDebugString(
    const ::tensorflow::RunOptions& msg);
string ProtoShortDebugString(
    const ::tensorflow::RunOptions& msg);
bool ProtoParseFromString(
    const string& s,
    ::tensorflow::RunOptions* msg)
        TF_MUST_USE_RESULT;

// Message-text conversion for tensorflow.RunMetadata.FunctionGraphs
string ProtoDebugString(
    const ::tensorflow::RunMetadata_FunctionGraphs& msg);
string ProtoShortDebugString(
    const ::tensorflow::RunMetadata_FunctionGraphs& msg);
bool ProtoParseFromString(
    const string& s,
    ::tensorflow::RunMetadata_FunctionGraphs* msg)
        TF_MUST_USE_RESULT;

// Message-text conversion for tensorflow.RunMetadata
string ProtoDebugString(
    const ::tensorflow::RunMetadata& msg);
string ProtoShortDebugString(
    const ::tensorflow::RunMetadata& msg);
bool ProtoParseFromString(
    const string& s,
    ::tensorflow::RunMetadata* msg)
        TF_MUST_USE_RESULT;

// Message-text conversion for tensorflow.TensorConnection
string ProtoDebugString(
    const ::tensorflow::TensorConnection& msg);
string ProtoShortDebugString(
    const ::tensorflow::TensorConnection& msg);
bool ProtoParseFromString(
    const string& s,
    ::tensorflow::TensorConnection* msg)
        TF_MUST_USE_RESULT;

// Message-text conversion for tensorflow.CallableOptions
string ProtoDebugString(
    const ::tensorflow::CallableOptions& msg);
string ProtoShortDebugString(
    const ::tensorflow::CallableOptions& msg);
bool ProtoParseFromString(
    const string& s,
    ::tensorflow::CallableOptions* msg)
        TF_MUST_USE_RESULT;

}  // namespace tensorflow

#endif  // tensorflow_core_protobuf_config_proto_H_

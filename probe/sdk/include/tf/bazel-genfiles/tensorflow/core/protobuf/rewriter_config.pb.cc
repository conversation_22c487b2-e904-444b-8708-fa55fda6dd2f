// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/rewriter_config.proto

#include "tensorflow/core/protobuf/rewriter_config.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_AttrValue;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto
namespace protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_AutoParallelOptions;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_ScopedAllocatorOptions;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_RewriterConfig_CustomGraphOptimizer;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto
namespace protobuf_tensorflow_2fcore_2fprotobuf_2fverifier_5fconfig_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fverifier_5fconfig_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_VerifierConfig;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fverifier_5fconfig_2eproto
namespace tensorflow {
class AutoParallelOptionsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<AutoParallelOptions>
      _instance;
} _AutoParallelOptions_default_instance_;
class ScopedAllocatorOptionsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ScopedAllocatorOptions>
      _instance;
} _ScopedAllocatorOptions_default_instance_;
class RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse>
      _instance;
} _RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse_default_instance_;
class RewriterConfig_CustomGraphOptimizerDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<RewriterConfig_CustomGraphOptimizer>
      _instance;
} _RewriterConfig_CustomGraphOptimizer_default_instance_;
class RewriterConfigDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<RewriterConfig>
      _instance;
} _RewriterConfig_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto {
static void InitDefaultsAutoParallelOptions() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_AutoParallelOptions_default_instance_;
    new (ptr) ::tensorflow::AutoParallelOptions();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::AutoParallelOptions::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_AutoParallelOptions =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsAutoParallelOptions}, {}};

static void InitDefaultsScopedAllocatorOptions() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_ScopedAllocatorOptions_default_instance_;
    new (ptr) ::tensorflow::ScopedAllocatorOptions();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::ScopedAllocatorOptions::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_ScopedAllocatorOptions =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsScopedAllocatorOptions}, {}};

static void InitDefaultsRewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse();
  }
  ::tensorflow::RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsRewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse}, {
      &protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::scc_info_AttrValue.base,}};

static void InitDefaultsRewriterConfig_CustomGraphOptimizer() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_RewriterConfig_CustomGraphOptimizer_default_instance_;
    new (ptr) ::tensorflow::RewriterConfig_CustomGraphOptimizer();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::RewriterConfig_CustomGraphOptimizer::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_RewriterConfig_CustomGraphOptimizer =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsRewriterConfig_CustomGraphOptimizer}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::scc_info_RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse.base,}};

static void InitDefaultsRewriterConfig() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_RewriterConfig_default_instance_;
    new (ptr) ::tensorflow::RewriterConfig();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::RewriterConfig::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<4> scc_info_RewriterConfig =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 4, InitDefaultsRewriterConfig}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::scc_info_AutoParallelOptions.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::scc_info_ScopedAllocatorOptions.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::scc_info_RewriterConfig_CustomGraphOptimizer.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fverifier_5fconfig_2eproto::scc_info_VerifierConfig.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_AutoParallelOptions.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ScopedAllocatorOptions.base);
  ::google::protobuf::internal::InitSCC(&scc_info_RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_RewriterConfig_CustomGraphOptimizer.base);
  ::google::protobuf::internal::InitSCC(&scc_info_RewriterConfig.base);
}

::google::protobuf::Metadata file_level_metadata[5];
const ::google::protobuf::EnumDescriptor* file_level_enum_descriptors[3];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AutoParallelOptions, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AutoParallelOptions, enable_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AutoParallelOptions, num_replicas_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ScopedAllocatorOptions, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ScopedAllocatorOptions, enable_op_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig_CustomGraphOptimizer, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig_CustomGraphOptimizer, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig_CustomGraphOptimizer, parameter_map_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig, layout_optimizer_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig, constant_folding_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig, shape_optimization_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig, remapping_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig, arithmetic_optimization_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig, dependency_optimization_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig, loop_optimization_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig, function_optimization_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig, debug_stripper_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig, disable_model_pruning_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig, scoped_allocator_optimization_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig, pin_to_host_optimization_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig, implementation_selector_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig, disable_meta_optimizer_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig, meta_optimizer_iterations_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig, min_graph_nodes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig, memory_optimization_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig, memory_optimizer_target_node_name_scope_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig, meta_optimizer_timeout_ms_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig, auto_parallel_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig, fail_on_optimizer_errors_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig, scoped_allocator_opts_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig, optimizers_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig, custom_optimizers_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig, inter_optimizer_verifier_config_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RewriterConfig, post_optimization_verifier_config_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::AutoParallelOptions)},
  { 7, -1, sizeof(::tensorflow::ScopedAllocatorOptions)},
  { 13, 20, sizeof(::tensorflow::RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse)},
  { 22, -1, sizeof(::tensorflow::RewriterConfig_CustomGraphOptimizer)},
  { 29, -1, sizeof(::tensorflow::RewriterConfig)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_AutoParallelOptions_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_ScopedAllocatorOptions_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_RewriterConfig_CustomGraphOptimizer_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_RewriterConfig_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/protobuf/rewriter_config.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, file_level_enum_descriptors, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 5);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n.tensorflow/core/protobuf/rewriter_conf"
      "ig.proto\022\ntensorflow\032*tensorflow/core/fr"
      "amework/attr_value.proto\032.tensorflow/cor"
      "e/protobuf/verifier_config.proto\";\n\023Auto"
      "ParallelOptions\022\016\n\006enable\030\001 \001(\010\022\024\n\014num_r"
      "eplicas\030\002 \001(\005\"+\n\026ScopedAllocatorOptions\022"
      "\021\n\tenable_op\030\001 \003(\t\"\307\017\n\016RewriterConfig\022;\n"
      "\020layout_optimizer\030\001 \001(\0162!.tensorflow.Rew"
      "riterConfig.Toggle\022;\n\020constant_folding\030\003"
      " \001(\0162!.tensorflow.RewriterConfig.Toggle\022"
      "=\n\022shape_optimization\030\r \001(\0162!.tensorflow"
      ".RewriterConfig.Toggle\0224\n\tremapping\030\016 \001("
      "\0162!.tensorflow.RewriterConfig.Toggle\022B\n\027"
      "arithmetic_optimization\030\007 \001(\0162!.tensorfl"
      "ow.RewriterConfig.Toggle\022B\n\027dependency_o"
      "ptimization\030\010 \001(\0162!.tensorflow.RewriterC"
      "onfig.Toggle\022<\n\021loop_optimization\030\t \001(\0162"
      "!.tensorflow.RewriterConfig.Toggle\022@\n\025fu"
      "nction_optimization\030\n \001(\0162!.tensorflow.R"
      "ewriterConfig.Toggle\0229\n\016debug_stripper\030\013"
      " \001(\0162!.tensorflow.RewriterConfig.Toggle\022"
      "\035\n\025disable_model_pruning\030\002 \001(\010\022H\n\035scoped"
      "_allocator_optimization\030\017 \001(\0162!.tensorfl"
      "ow.RewriterConfig.Toggle\022C\n\030pin_to_host_"
      "optimization\030\022 \001(\0162!.tensorflow.Rewriter"
      "Config.Toggle\022B\n\027implementation_selector"
      "\030\026 \001(\0162!.tensorflow.RewriterConfig.Toggl"
      "e\022\036\n\026disable_meta_optimizer\030\023 \001(\010\022O\n\031met"
      "a_optimizer_iterations\030\014 \001(\0162,.tensorflo"
      "w.RewriterConfig.NumIterationsType\022\027\n\017mi"
      "n_graph_nodes\030\021 \001(\005\022B\n\023memory_optimizati"
      "on\030\004 \001(\0162%.tensorflow.RewriterConfig.Mem"
      "OptType\022/\n\'memory_optimizer_target_node_"
      "name_scope\030\006 \001(\t\022!\n\031meta_optimizer_timeo"
      "ut_ms\030\024 \001(\003\0226\n\rauto_parallel\030\005 \001(\0132\037.ten"
      "sorflow.AutoParallelOptions\022 \n\030fail_on_o"
      "ptimizer_errors\030\025 \001(\010\022A\n\025scoped_allocato"
      "r_opts\030\020 \001(\0132\".tensorflow.ScopedAllocato"
      "rOptions\022\022\n\noptimizers\030d \003(\t\022K\n\021custom_o"
      "ptimizers\030\310\001 \003(\0132/.tensorflow.RewriterCo"
      "nfig.CustomGraphOptimizer\022D\n\037inter_optim"
      "izer_verifier_config\030\254\002 \001(\0132\032.tensorflow"
      ".VerifierConfig\022F\n!post_optimization_ver"
      "ifier_config\030\255\002 \001(\0132\032.tensorflow.Verifie"
      "rConfig\032\312\001\n\024CustomGraphOptimizer\022\014\n\004name"
      "\030\001 \001(\t\022X\n\rparameter_map\030\002 \003(\0132A.tensorfl"
      "ow.RewriterConfig.CustomGraphOptimizer.P"
      "arameterMapEntry\032J\n\021ParameterMapEntry\022\013\n"
      "\003key\030\001 \001(\t\022$\n\005value\030\002 \001(\0132\025.tensorflow.A"
      "ttrValue:\0028\001\"6\n\006Toggle\022\013\n\007DEFAULT\020\000\022\006\n\002O"
      "N\020\001\022\007\n\003OFF\020\002\022\016\n\nAGGRESSIVE\020\003\"<\n\021NumItera"
      "tionsType\022\025\n\021DEFAULT_NUM_ITERS\020\000\022\007\n\003ONE\020"
      "\001\022\007\n\003TWO\020\002\"\237\001\n\nMemOptType\022\023\n\017DEFAULT_MEM"
      "_OPT\020\000\022\016\n\nNO_MEM_OPT\020\001\022\n\n\006MANUAL\020\002\022\027\n\023SW"
      "APPING_HEURISTICS\020\004\022\034\n\030RECOMPUTATION_HEU"
      "RISTICS\020\005\022\031\n\025SCHEDULING_HEURISTICS\020\006\022\016\n\n"
      "HEURISTICS\020\003B5\n\030org.tensorflow.framework"
      "B\024RewriterConfigProtosP\001\370\001\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 2315);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/protobuf/rewriter_config.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2fattr_5fvalue_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fverifier_5fconfig_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto
namespace tensorflow {
const ::google::protobuf::EnumDescriptor* RewriterConfig_Toggle_descriptor() {
  protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::file_level_enum_descriptors[0];
}
bool RewriterConfig_Toggle_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const RewriterConfig_Toggle RewriterConfig::DEFAULT;
const RewriterConfig_Toggle RewriterConfig::ON;
const RewriterConfig_Toggle RewriterConfig::OFF;
const RewriterConfig_Toggle RewriterConfig::AGGRESSIVE;
const RewriterConfig_Toggle RewriterConfig::Toggle_MIN;
const RewriterConfig_Toggle RewriterConfig::Toggle_MAX;
const int RewriterConfig::Toggle_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900
const ::google::protobuf::EnumDescriptor* RewriterConfig_NumIterationsType_descriptor() {
  protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::file_level_enum_descriptors[1];
}
bool RewriterConfig_NumIterationsType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const RewriterConfig_NumIterationsType RewriterConfig::DEFAULT_NUM_ITERS;
const RewriterConfig_NumIterationsType RewriterConfig::ONE;
const RewriterConfig_NumIterationsType RewriterConfig::TWO;
const RewriterConfig_NumIterationsType RewriterConfig::NumIterationsType_MIN;
const RewriterConfig_NumIterationsType RewriterConfig::NumIterationsType_MAX;
const int RewriterConfig::NumIterationsType_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900
const ::google::protobuf::EnumDescriptor* RewriterConfig_MemOptType_descriptor() {
  protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::file_level_enum_descriptors[2];
}
bool RewriterConfig_MemOptType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const RewriterConfig_MemOptType RewriterConfig::DEFAULT_MEM_OPT;
const RewriterConfig_MemOptType RewriterConfig::NO_MEM_OPT;
const RewriterConfig_MemOptType RewriterConfig::MANUAL;
const RewriterConfig_MemOptType RewriterConfig::SWAPPING_HEURISTICS;
const RewriterConfig_MemOptType RewriterConfig::RECOMPUTATION_HEURISTICS;
const RewriterConfig_MemOptType RewriterConfig::SCHEDULING_HEURISTICS;
const RewriterConfig_MemOptType RewriterConfig::HEURISTICS;
const RewriterConfig_MemOptType RewriterConfig::MemOptType_MIN;
const RewriterConfig_MemOptType RewriterConfig::MemOptType_MAX;
const int RewriterConfig::MemOptType_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

// ===================================================================

void AutoParallelOptions::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AutoParallelOptions::kEnableFieldNumber;
const int AutoParallelOptions::kNumReplicasFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AutoParallelOptions::AutoParallelOptions()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::scc_info_AutoParallelOptions.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.AutoParallelOptions)
}
AutoParallelOptions::AutoParallelOptions(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::scc_info_AutoParallelOptions.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.AutoParallelOptions)
}
AutoParallelOptions::AutoParallelOptions(const AutoParallelOptions& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&enable_, &from.enable_,
    static_cast<size_t>(reinterpret_cast<char*>(&num_replicas_) -
    reinterpret_cast<char*>(&enable_)) + sizeof(num_replicas_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.AutoParallelOptions)
}

void AutoParallelOptions::SharedCtor() {
  ::memset(&enable_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&num_replicas_) -
      reinterpret_cast<char*>(&enable_)) + sizeof(num_replicas_));
}

AutoParallelOptions::~AutoParallelOptions() {
  // @@protoc_insertion_point(destructor:tensorflow.AutoParallelOptions)
  SharedDtor();
}

void AutoParallelOptions::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void AutoParallelOptions::ArenaDtor(void* object) {
  AutoParallelOptions* _this = reinterpret_cast< AutoParallelOptions* >(object);
  (void)_this;
}
void AutoParallelOptions::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void AutoParallelOptions::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* AutoParallelOptions::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const AutoParallelOptions& AutoParallelOptions::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::scc_info_AutoParallelOptions.base);
  return *internal_default_instance();
}


void AutoParallelOptions::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.AutoParallelOptions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&enable_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&num_replicas_) -
      reinterpret_cast<char*>(&enable_)) + sizeof(num_replicas_));
  _internal_metadata_.Clear();
}

bool AutoParallelOptions::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.AutoParallelOptions)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // bool enable = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &enable_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 num_replicas = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &num_replicas_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.AutoParallelOptions)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.AutoParallelOptions)
  return false;
#undef DO_
}

void AutoParallelOptions::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.AutoParallelOptions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bool enable = 1;
  if (this->enable() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(1, this->enable(), output);
  }

  // int32 num_replicas = 2;
  if (this->num_replicas() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->num_replicas(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.AutoParallelOptions)
}

::google::protobuf::uint8* AutoParallelOptions::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.AutoParallelOptions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bool enable = 1;
  if (this->enable() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(1, this->enable(), target);
  }

  // int32 num_replicas = 2;
  if (this->num_replicas() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->num_replicas(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.AutoParallelOptions)
  return target;
}

size_t AutoParallelOptions::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.AutoParallelOptions)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // bool enable = 1;
  if (this->enable() != 0) {
    total_size += 1 + 1;
  }

  // int32 num_replicas = 2;
  if (this->num_replicas() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->num_replicas());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AutoParallelOptions::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.AutoParallelOptions)
  GOOGLE_DCHECK_NE(&from, this);
  const AutoParallelOptions* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AutoParallelOptions>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.AutoParallelOptions)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.AutoParallelOptions)
    MergeFrom(*source);
  }
}

void AutoParallelOptions::MergeFrom(const AutoParallelOptions& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.AutoParallelOptions)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.enable() != 0) {
    set_enable(from.enable());
  }
  if (from.num_replicas() != 0) {
    set_num_replicas(from.num_replicas());
  }
}

void AutoParallelOptions::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.AutoParallelOptions)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AutoParallelOptions::CopyFrom(const AutoParallelOptions& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.AutoParallelOptions)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AutoParallelOptions::IsInitialized() const {
  return true;
}

void AutoParallelOptions::Swap(AutoParallelOptions* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    AutoParallelOptions* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void AutoParallelOptions::UnsafeArenaSwap(AutoParallelOptions* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void AutoParallelOptions::InternalSwap(AutoParallelOptions* other) {
  using std::swap;
  swap(enable_, other->enable_);
  swap(num_replicas_, other->num_replicas_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata AutoParallelOptions::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ScopedAllocatorOptions::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ScopedAllocatorOptions::kEnableOpFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ScopedAllocatorOptions::ScopedAllocatorOptions()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::scc_info_ScopedAllocatorOptions.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.ScopedAllocatorOptions)
}
ScopedAllocatorOptions::ScopedAllocatorOptions(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  enable_op_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::scc_info_ScopedAllocatorOptions.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.ScopedAllocatorOptions)
}
ScopedAllocatorOptions::ScopedAllocatorOptions(const ScopedAllocatorOptions& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      enable_op_(from.enable_op_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.ScopedAllocatorOptions)
}

void ScopedAllocatorOptions::SharedCtor() {
}

ScopedAllocatorOptions::~ScopedAllocatorOptions() {
  // @@protoc_insertion_point(destructor:tensorflow.ScopedAllocatorOptions)
  SharedDtor();
}

void ScopedAllocatorOptions::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void ScopedAllocatorOptions::ArenaDtor(void* object) {
  ScopedAllocatorOptions* _this = reinterpret_cast< ScopedAllocatorOptions* >(object);
  (void)_this;
}
void ScopedAllocatorOptions::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void ScopedAllocatorOptions::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ScopedAllocatorOptions::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ScopedAllocatorOptions& ScopedAllocatorOptions::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::scc_info_ScopedAllocatorOptions.base);
  return *internal_default_instance();
}


void ScopedAllocatorOptions::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.ScopedAllocatorOptions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  enable_op_.Clear();
  _internal_metadata_.Clear();
}

bool ScopedAllocatorOptions::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.ScopedAllocatorOptions)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated string enable_op = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_enable_op()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->enable_op(this->enable_op_size() - 1).data(),
            static_cast<int>(this->enable_op(this->enable_op_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ScopedAllocatorOptions.enable_op"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.ScopedAllocatorOptions)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.ScopedAllocatorOptions)
  return false;
#undef DO_
}

void ScopedAllocatorOptions::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.ScopedAllocatorOptions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string enable_op = 1;
  for (int i = 0, n = this->enable_op_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->enable_op(i).data(), static_cast<int>(this->enable_op(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ScopedAllocatorOptions.enable_op");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->enable_op(i), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.ScopedAllocatorOptions)
}

::google::protobuf::uint8* ScopedAllocatorOptions::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.ScopedAllocatorOptions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string enable_op = 1;
  for (int i = 0, n = this->enable_op_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->enable_op(i).data(), static_cast<int>(this->enable_op(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ScopedAllocatorOptions.enable_op");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(1, this->enable_op(i), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.ScopedAllocatorOptions)
  return target;
}

size_t ScopedAllocatorOptions::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.ScopedAllocatorOptions)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated string enable_op = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->enable_op_size());
  for (int i = 0, n = this->enable_op_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->enable_op(i));
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ScopedAllocatorOptions::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.ScopedAllocatorOptions)
  GOOGLE_DCHECK_NE(&from, this);
  const ScopedAllocatorOptions* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ScopedAllocatorOptions>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.ScopedAllocatorOptions)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.ScopedAllocatorOptions)
    MergeFrom(*source);
  }
}

void ScopedAllocatorOptions::MergeFrom(const ScopedAllocatorOptions& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.ScopedAllocatorOptions)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  enable_op_.MergeFrom(from.enable_op_);
}

void ScopedAllocatorOptions::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.ScopedAllocatorOptions)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ScopedAllocatorOptions::CopyFrom(const ScopedAllocatorOptions& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.ScopedAllocatorOptions)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ScopedAllocatorOptions::IsInitialized() const {
  return true;
}

void ScopedAllocatorOptions::Swap(ScopedAllocatorOptions* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    ScopedAllocatorOptions* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void ScopedAllocatorOptions::UnsafeArenaSwap(ScopedAllocatorOptions* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void ScopedAllocatorOptions::InternalSwap(ScopedAllocatorOptions* other) {
  using std::swap;
  enable_op_.InternalSwap(CastToBase(&other->enable_op_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ScopedAllocatorOptions::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse::RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse() {}
RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse::RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse::MergeFrom(const RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::file_level_metadata[2];
}
void RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void RewriterConfig_CustomGraphOptimizer::InitAsDefaultInstance() {
}
void RewriterConfig_CustomGraphOptimizer::clear_parameter_map() {
  parameter_map_.Clear();
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RewriterConfig_CustomGraphOptimizer::kNameFieldNumber;
const int RewriterConfig_CustomGraphOptimizer::kParameterMapFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RewriterConfig_CustomGraphOptimizer::RewriterConfig_CustomGraphOptimizer()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::scc_info_RewriterConfig_CustomGraphOptimizer.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.RewriterConfig.CustomGraphOptimizer)
}
RewriterConfig_CustomGraphOptimizer::RewriterConfig_CustomGraphOptimizer(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  parameter_map_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::scc_info_RewriterConfig_CustomGraphOptimizer.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.RewriterConfig.CustomGraphOptimizer)
}
RewriterConfig_CustomGraphOptimizer::RewriterConfig_CustomGraphOptimizer(const RewriterConfig_CustomGraphOptimizer& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  parameter_map_.MergeFrom(from.parameter_map_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name(),
      GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.RewriterConfig.CustomGraphOptimizer)
}

void RewriterConfig_CustomGraphOptimizer::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

RewriterConfig_CustomGraphOptimizer::~RewriterConfig_CustomGraphOptimizer() {
  // @@protoc_insertion_point(destructor:tensorflow.RewriterConfig.CustomGraphOptimizer)
  SharedDtor();
}

void RewriterConfig_CustomGraphOptimizer::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void RewriterConfig_CustomGraphOptimizer::ArenaDtor(void* object) {
  RewriterConfig_CustomGraphOptimizer* _this = reinterpret_cast< RewriterConfig_CustomGraphOptimizer* >(object);
  (void)_this;
}
void RewriterConfig_CustomGraphOptimizer::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void RewriterConfig_CustomGraphOptimizer::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* RewriterConfig_CustomGraphOptimizer::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const RewriterConfig_CustomGraphOptimizer& RewriterConfig_CustomGraphOptimizer::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::scc_info_RewriterConfig_CustomGraphOptimizer.base);
  return *internal_default_instance();
}


void RewriterConfig_CustomGraphOptimizer::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.RewriterConfig.CustomGraphOptimizer)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  parameter_map_.Clear();
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  _internal_metadata_.Clear();
}

bool RewriterConfig_CustomGraphOptimizer::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.RewriterConfig.CustomGraphOptimizer)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.RewriterConfig.CustomGraphOptimizer.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, .tensorflow.AttrValue> parameter_map = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse,
              ::std::string, ::tensorflow::AttrValue,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue > > parser(&parameter_map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.RewriterConfig.CustomGraphOptimizer.ParameterMapEntry.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.RewriterConfig.CustomGraphOptimizer)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.RewriterConfig.CustomGraphOptimizer)
  return false;
#undef DO_
}

void RewriterConfig_CustomGraphOptimizer::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.RewriterConfig.CustomGraphOptimizer)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.RewriterConfig.CustomGraphOptimizer.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // map<string, .tensorflow.AttrValue> parameter_map = 2;
  if (!this->parameter_map().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.RewriterConfig.CustomGraphOptimizer.ParameterMapEntry.key");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->parameter_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->parameter_map().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
          it = this->parameter_map().begin();
          it != this->parameter_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(parameter_map_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
          it = this->parameter_map().begin();
          it != this->parameter_map().end(); ++it) {
        entry.reset(parameter_map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.RewriterConfig.CustomGraphOptimizer)
}

::google::protobuf::uint8* RewriterConfig_CustomGraphOptimizer::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.RewriterConfig.CustomGraphOptimizer)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.RewriterConfig.CustomGraphOptimizer.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // map<string, .tensorflow.AttrValue> parameter_map = 2;
  if (!this->parameter_map().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.RewriterConfig.CustomGraphOptimizer.ParameterMapEntry.key");
      }
    };

    if (deterministic &&
        this->parameter_map().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->parameter_map().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
          it = this->parameter_map().begin();
          it != this->parameter_map().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(parameter_map_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
          it = this->parameter_map().begin();
          it != this->parameter_map().end(); ++it) {
        entry.reset(parameter_map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.RewriterConfig.CustomGraphOptimizer)
  return target;
}

size_t RewriterConfig_CustomGraphOptimizer::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.RewriterConfig.CustomGraphOptimizer)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // map<string, .tensorflow.AttrValue> parameter_map = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->parameter_map_size());
  {
    ::std::unique_ptr<RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >::const_iterator
        it = this->parameter_map().begin();
        it != this->parameter_map().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(parameter_map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RewriterConfig_CustomGraphOptimizer::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.RewriterConfig.CustomGraphOptimizer)
  GOOGLE_DCHECK_NE(&from, this);
  const RewriterConfig_CustomGraphOptimizer* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RewriterConfig_CustomGraphOptimizer>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.RewriterConfig.CustomGraphOptimizer)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.RewriterConfig.CustomGraphOptimizer)
    MergeFrom(*source);
  }
}

void RewriterConfig_CustomGraphOptimizer::MergeFrom(const RewriterConfig_CustomGraphOptimizer& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.RewriterConfig.CustomGraphOptimizer)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  parameter_map_.MergeFrom(from.parameter_map_);
  if (from.name().size() > 0) {
    set_name(from.name());
  }
}

void RewriterConfig_CustomGraphOptimizer::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.RewriterConfig.CustomGraphOptimizer)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RewriterConfig_CustomGraphOptimizer::CopyFrom(const RewriterConfig_CustomGraphOptimizer& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.RewriterConfig.CustomGraphOptimizer)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RewriterConfig_CustomGraphOptimizer::IsInitialized() const {
  return true;
}

void RewriterConfig_CustomGraphOptimizer::Swap(RewriterConfig_CustomGraphOptimizer* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    RewriterConfig_CustomGraphOptimizer* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void RewriterConfig_CustomGraphOptimizer::UnsafeArenaSwap(RewriterConfig_CustomGraphOptimizer* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void RewriterConfig_CustomGraphOptimizer::InternalSwap(RewriterConfig_CustomGraphOptimizer* other) {
  using std::swap;
  parameter_map_.Swap(&other->parameter_map_);
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata RewriterConfig_CustomGraphOptimizer::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void RewriterConfig::InitAsDefaultInstance() {
  ::tensorflow::_RewriterConfig_default_instance_._instance.get_mutable()->auto_parallel_ = const_cast< ::tensorflow::AutoParallelOptions*>(
      ::tensorflow::AutoParallelOptions::internal_default_instance());
  ::tensorflow::_RewriterConfig_default_instance_._instance.get_mutable()->scoped_allocator_opts_ = const_cast< ::tensorflow::ScopedAllocatorOptions*>(
      ::tensorflow::ScopedAllocatorOptions::internal_default_instance());
  ::tensorflow::_RewriterConfig_default_instance_._instance.get_mutable()->inter_optimizer_verifier_config_ = const_cast< ::tensorflow::VerifierConfig*>(
      ::tensorflow::VerifierConfig::internal_default_instance());
  ::tensorflow::_RewriterConfig_default_instance_._instance.get_mutable()->post_optimization_verifier_config_ = const_cast< ::tensorflow::VerifierConfig*>(
      ::tensorflow::VerifierConfig::internal_default_instance());
}
void RewriterConfig::unsafe_arena_set_allocated_auto_parallel(
    ::tensorflow::AutoParallelOptions* auto_parallel) {
  if (GetArenaNoVirtual() == NULL) {
    delete auto_parallel_;
  }
  auto_parallel_ = auto_parallel;
  if (auto_parallel) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RewriterConfig.auto_parallel)
}
void RewriterConfig::unsafe_arena_set_allocated_scoped_allocator_opts(
    ::tensorflow::ScopedAllocatorOptions* scoped_allocator_opts) {
  if (GetArenaNoVirtual() == NULL) {
    delete scoped_allocator_opts_;
  }
  scoped_allocator_opts_ = scoped_allocator_opts;
  if (scoped_allocator_opts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RewriterConfig.scoped_allocator_opts)
}
void RewriterConfig::unsafe_arena_set_allocated_inter_optimizer_verifier_config(
    ::tensorflow::VerifierConfig* inter_optimizer_verifier_config) {
  if (GetArenaNoVirtual() == NULL) {
    delete inter_optimizer_verifier_config_;
  }
  inter_optimizer_verifier_config_ = inter_optimizer_verifier_config;
  if (inter_optimizer_verifier_config) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RewriterConfig.inter_optimizer_verifier_config)
}
void RewriterConfig::clear_inter_optimizer_verifier_config() {
  if (GetArenaNoVirtual() == NULL && inter_optimizer_verifier_config_ != NULL) {
    delete inter_optimizer_verifier_config_;
  }
  inter_optimizer_verifier_config_ = NULL;
}
void RewriterConfig::unsafe_arena_set_allocated_post_optimization_verifier_config(
    ::tensorflow::VerifierConfig* post_optimization_verifier_config) {
  if (GetArenaNoVirtual() == NULL) {
    delete post_optimization_verifier_config_;
  }
  post_optimization_verifier_config_ = post_optimization_verifier_config;
  if (post_optimization_verifier_config) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RewriterConfig.post_optimization_verifier_config)
}
void RewriterConfig::clear_post_optimization_verifier_config() {
  if (GetArenaNoVirtual() == NULL && post_optimization_verifier_config_ != NULL) {
    delete post_optimization_verifier_config_;
  }
  post_optimization_verifier_config_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RewriterConfig::kLayoutOptimizerFieldNumber;
const int RewriterConfig::kConstantFoldingFieldNumber;
const int RewriterConfig::kShapeOptimizationFieldNumber;
const int RewriterConfig::kRemappingFieldNumber;
const int RewriterConfig::kArithmeticOptimizationFieldNumber;
const int RewriterConfig::kDependencyOptimizationFieldNumber;
const int RewriterConfig::kLoopOptimizationFieldNumber;
const int RewriterConfig::kFunctionOptimizationFieldNumber;
const int RewriterConfig::kDebugStripperFieldNumber;
const int RewriterConfig::kDisableModelPruningFieldNumber;
const int RewriterConfig::kScopedAllocatorOptimizationFieldNumber;
const int RewriterConfig::kPinToHostOptimizationFieldNumber;
const int RewriterConfig::kImplementationSelectorFieldNumber;
const int RewriterConfig::kDisableMetaOptimizerFieldNumber;
const int RewriterConfig::kMetaOptimizerIterationsFieldNumber;
const int RewriterConfig::kMinGraphNodesFieldNumber;
const int RewriterConfig::kMemoryOptimizationFieldNumber;
const int RewriterConfig::kMemoryOptimizerTargetNodeNameScopeFieldNumber;
const int RewriterConfig::kMetaOptimizerTimeoutMsFieldNumber;
const int RewriterConfig::kAutoParallelFieldNumber;
const int RewriterConfig::kFailOnOptimizerErrorsFieldNumber;
const int RewriterConfig::kScopedAllocatorOptsFieldNumber;
const int RewriterConfig::kOptimizersFieldNumber;
const int RewriterConfig::kCustomOptimizersFieldNumber;
const int RewriterConfig::kInterOptimizerVerifierConfigFieldNumber;
const int RewriterConfig::kPostOptimizationVerifierConfigFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RewriterConfig::RewriterConfig()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::scc_info_RewriterConfig.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.RewriterConfig)
}
RewriterConfig::RewriterConfig(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  optimizers_(arena),
  custom_optimizers_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::scc_info_RewriterConfig.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.RewriterConfig)
}
RewriterConfig::RewriterConfig(const RewriterConfig& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      optimizers_(from.optimizers_),
      custom_optimizers_(from.custom_optimizers_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  memory_optimizer_target_node_name_scope_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.memory_optimizer_target_node_name_scope().size() > 0) {
    memory_optimizer_target_node_name_scope_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.memory_optimizer_target_node_name_scope(),
      GetArenaNoVirtual());
  }
  if (from.has_auto_parallel()) {
    auto_parallel_ = new ::tensorflow::AutoParallelOptions(*from.auto_parallel_);
  } else {
    auto_parallel_ = NULL;
  }
  if (from.has_scoped_allocator_opts()) {
    scoped_allocator_opts_ = new ::tensorflow::ScopedAllocatorOptions(*from.scoped_allocator_opts_);
  } else {
    scoped_allocator_opts_ = NULL;
  }
  if (from.has_inter_optimizer_verifier_config()) {
    inter_optimizer_verifier_config_ = new ::tensorflow::VerifierConfig(*from.inter_optimizer_verifier_config_);
  } else {
    inter_optimizer_verifier_config_ = NULL;
  }
  if (from.has_post_optimization_verifier_config()) {
    post_optimization_verifier_config_ = new ::tensorflow::VerifierConfig(*from.post_optimization_verifier_config_);
  } else {
    post_optimization_verifier_config_ = NULL;
  }
  ::memcpy(&layout_optimizer_, &from.layout_optimizer_,
    static_cast<size_t>(reinterpret_cast<char*>(&implementation_selector_) -
    reinterpret_cast<char*>(&layout_optimizer_)) + sizeof(implementation_selector_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.RewriterConfig)
}

void RewriterConfig::SharedCtor() {
  memory_optimizer_target_node_name_scope_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&auto_parallel_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&implementation_selector_) -
      reinterpret_cast<char*>(&auto_parallel_)) + sizeof(implementation_selector_));
}

RewriterConfig::~RewriterConfig() {
  // @@protoc_insertion_point(destructor:tensorflow.RewriterConfig)
  SharedDtor();
}

void RewriterConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  memory_optimizer_target_node_name_scope_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete auto_parallel_;
  if (this != internal_default_instance()) delete scoped_allocator_opts_;
  if (this != internal_default_instance()) delete inter_optimizer_verifier_config_;
  if (this != internal_default_instance()) delete post_optimization_verifier_config_;
}

void RewriterConfig::ArenaDtor(void* object) {
  RewriterConfig* _this = reinterpret_cast< RewriterConfig* >(object);
  (void)_this;
}
void RewriterConfig::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void RewriterConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* RewriterConfig::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const RewriterConfig& RewriterConfig::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::scc_info_RewriterConfig.base);
  return *internal_default_instance();
}


void RewriterConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.RewriterConfig)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  optimizers_.Clear();
  custom_optimizers_.Clear();
  memory_optimizer_target_node_name_scope_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && auto_parallel_ != NULL) {
    delete auto_parallel_;
  }
  auto_parallel_ = NULL;
  if (GetArenaNoVirtual() == NULL && scoped_allocator_opts_ != NULL) {
    delete scoped_allocator_opts_;
  }
  scoped_allocator_opts_ = NULL;
  if (GetArenaNoVirtual() == NULL && inter_optimizer_verifier_config_ != NULL) {
    delete inter_optimizer_verifier_config_;
  }
  inter_optimizer_verifier_config_ = NULL;
  if (GetArenaNoVirtual() == NULL && post_optimization_verifier_config_ != NULL) {
    delete post_optimization_verifier_config_;
  }
  post_optimization_verifier_config_ = NULL;
  ::memset(&layout_optimizer_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&implementation_selector_) -
      reinterpret_cast<char*>(&layout_optimizer_)) + sizeof(implementation_selector_));
  _internal_metadata_.Clear();
}

bool RewriterConfig::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.RewriterConfig)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(16383u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.RewriterConfig.Toggle layout_optimizer = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_layout_optimizer(static_cast< ::tensorflow::RewriterConfig_Toggle >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool disable_model_pruning = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &disable_model_pruning_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.RewriterConfig.Toggle constant_folding = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_constant_folding(static_cast< ::tensorflow::RewriterConfig_Toggle >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.RewriterConfig.MemOptType memory_optimization = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_memory_optimization(static_cast< ::tensorflow::RewriterConfig_MemOptType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.AutoParallelOptions auto_parallel = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_auto_parallel()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string memory_optimizer_target_node_name_scope = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_memory_optimizer_target_node_name_scope()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->memory_optimizer_target_node_name_scope().data(), static_cast<int>(this->memory_optimizer_target_node_name_scope().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.RewriterConfig.memory_optimizer_target_node_name_scope"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.RewriterConfig.Toggle arithmetic_optimization = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(56u /* 56 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_arithmetic_optimization(static_cast< ::tensorflow::RewriterConfig_Toggle >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.RewriterConfig.Toggle dependency_optimization = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(64u /* 64 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_dependency_optimization(static_cast< ::tensorflow::RewriterConfig_Toggle >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.RewriterConfig.Toggle loop_optimization = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(72u /* 72 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_loop_optimization(static_cast< ::tensorflow::RewriterConfig_Toggle >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.RewriterConfig.Toggle function_optimization = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(80u /* 80 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_function_optimization(static_cast< ::tensorflow::RewriterConfig_Toggle >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.RewriterConfig.Toggle debug_stripper = 11;
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(88u /* 88 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_debug_stripper(static_cast< ::tensorflow::RewriterConfig_Toggle >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.RewriterConfig.NumIterationsType meta_optimizer_iterations = 12;
      case 12: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(96u /* 96 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_meta_optimizer_iterations(static_cast< ::tensorflow::RewriterConfig_NumIterationsType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.RewriterConfig.Toggle shape_optimization = 13;
      case 13: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(104u /* 104 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_shape_optimization(static_cast< ::tensorflow::RewriterConfig_Toggle >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.RewriterConfig.Toggle remapping = 14;
      case 14: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(112u /* 112 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_remapping(static_cast< ::tensorflow::RewriterConfig_Toggle >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.RewriterConfig.Toggle scoped_allocator_optimization = 15;
      case 15: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(120u /* 120 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_scoped_allocator_optimization(static_cast< ::tensorflow::RewriterConfig_Toggle >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.ScopedAllocatorOptions scoped_allocator_opts = 16;
      case 16: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(130u /* 130 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_scoped_allocator_opts()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 min_graph_nodes = 17;
      case 17: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(136u /* 136 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &min_graph_nodes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.RewriterConfig.Toggle pin_to_host_optimization = 18;
      case 18: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(144u /* 144 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_pin_to_host_optimization(static_cast< ::tensorflow::RewriterConfig_Toggle >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool disable_meta_optimizer = 19;
      case 19: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(152u /* 152 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &disable_meta_optimizer_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 meta_optimizer_timeout_ms = 20;
      case 20: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(160u /* 160 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &meta_optimizer_timeout_ms_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool fail_on_optimizer_errors = 21;
      case 21: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(168u /* 168 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &fail_on_optimizer_errors_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.RewriterConfig.Toggle implementation_selector = 22;
      case 22: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(176u /* 176 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_implementation_selector(static_cast< ::tensorflow::RewriterConfig_Toggle >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string optimizers = 100;
      case 100: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 802 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_optimizers()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optimizers(this->optimizers_size() - 1).data(),
            static_cast<int>(this->optimizers(this->optimizers_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.RewriterConfig.optimizers"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.RewriterConfig.CustomGraphOptimizer custom_optimizers = 200;
      case 200: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(66u /* 1602 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_custom_optimizers()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.VerifierConfig inter_optimizer_verifier_config = 300;
      case 300: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(98u /* 2402 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_inter_optimizer_verifier_config()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.VerifierConfig post_optimization_verifier_config = 301;
      case 301: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(106u /* 2410 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_post_optimization_verifier_config()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.RewriterConfig)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.RewriterConfig)
  return false;
#undef DO_
}

void RewriterConfig::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.RewriterConfig)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.RewriterConfig.Toggle layout_optimizer = 1;
  if (this->layout_optimizer() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->layout_optimizer(), output);
  }

  // bool disable_model_pruning = 2;
  if (this->disable_model_pruning() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(2, this->disable_model_pruning(), output);
  }

  // .tensorflow.RewriterConfig.Toggle constant_folding = 3;
  if (this->constant_folding() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      3, this->constant_folding(), output);
  }

  // .tensorflow.RewriterConfig.MemOptType memory_optimization = 4;
  if (this->memory_optimization() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      4, this->memory_optimization(), output);
  }

  // .tensorflow.AutoParallelOptions auto_parallel = 5;
  if (this->has_auto_parallel()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, this->_internal_auto_parallel(), output);
  }

  // string memory_optimizer_target_node_name_scope = 6;
  if (this->memory_optimizer_target_node_name_scope().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->memory_optimizer_target_node_name_scope().data(), static_cast<int>(this->memory_optimizer_target_node_name_scope().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.RewriterConfig.memory_optimizer_target_node_name_scope");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      6, this->memory_optimizer_target_node_name_scope(), output);
  }

  // .tensorflow.RewriterConfig.Toggle arithmetic_optimization = 7;
  if (this->arithmetic_optimization() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      7, this->arithmetic_optimization(), output);
  }

  // .tensorflow.RewriterConfig.Toggle dependency_optimization = 8;
  if (this->dependency_optimization() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      8, this->dependency_optimization(), output);
  }

  // .tensorflow.RewriterConfig.Toggle loop_optimization = 9;
  if (this->loop_optimization() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      9, this->loop_optimization(), output);
  }

  // .tensorflow.RewriterConfig.Toggle function_optimization = 10;
  if (this->function_optimization() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      10, this->function_optimization(), output);
  }

  // .tensorflow.RewriterConfig.Toggle debug_stripper = 11;
  if (this->debug_stripper() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      11, this->debug_stripper(), output);
  }

  // .tensorflow.RewriterConfig.NumIterationsType meta_optimizer_iterations = 12;
  if (this->meta_optimizer_iterations() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      12, this->meta_optimizer_iterations(), output);
  }

  // .tensorflow.RewriterConfig.Toggle shape_optimization = 13;
  if (this->shape_optimization() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      13, this->shape_optimization(), output);
  }

  // .tensorflow.RewriterConfig.Toggle remapping = 14;
  if (this->remapping() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      14, this->remapping(), output);
  }

  // .tensorflow.RewriterConfig.Toggle scoped_allocator_optimization = 15;
  if (this->scoped_allocator_optimization() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      15, this->scoped_allocator_optimization(), output);
  }

  // .tensorflow.ScopedAllocatorOptions scoped_allocator_opts = 16;
  if (this->has_scoped_allocator_opts()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      16, this->_internal_scoped_allocator_opts(), output);
  }

  // int32 min_graph_nodes = 17;
  if (this->min_graph_nodes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(17, this->min_graph_nodes(), output);
  }

  // .tensorflow.RewriterConfig.Toggle pin_to_host_optimization = 18;
  if (this->pin_to_host_optimization() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      18, this->pin_to_host_optimization(), output);
  }

  // bool disable_meta_optimizer = 19;
  if (this->disable_meta_optimizer() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(19, this->disable_meta_optimizer(), output);
  }

  // int64 meta_optimizer_timeout_ms = 20;
  if (this->meta_optimizer_timeout_ms() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(20, this->meta_optimizer_timeout_ms(), output);
  }

  // bool fail_on_optimizer_errors = 21;
  if (this->fail_on_optimizer_errors() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(21, this->fail_on_optimizer_errors(), output);
  }

  // .tensorflow.RewriterConfig.Toggle implementation_selector = 22;
  if (this->implementation_selector() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      22, this->implementation_selector(), output);
  }

  // repeated string optimizers = 100;
  for (int i = 0, n = this->optimizers_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optimizers(i).data(), static_cast<int>(this->optimizers(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.RewriterConfig.optimizers");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      100, this->optimizers(i), output);
  }

  // repeated .tensorflow.RewriterConfig.CustomGraphOptimizer custom_optimizers = 200;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->custom_optimizers_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      200,
      this->custom_optimizers(static_cast<int>(i)),
      output);
  }

  // .tensorflow.VerifierConfig inter_optimizer_verifier_config = 300;
  if (this->has_inter_optimizer_verifier_config()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      300, this->_internal_inter_optimizer_verifier_config(), output);
  }

  // .tensorflow.VerifierConfig post_optimization_verifier_config = 301;
  if (this->has_post_optimization_verifier_config()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      301, this->_internal_post_optimization_verifier_config(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.RewriterConfig)
}

::google::protobuf::uint8* RewriterConfig::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.RewriterConfig)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.RewriterConfig.Toggle layout_optimizer = 1;
  if (this->layout_optimizer() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->layout_optimizer(), target);
  }

  // bool disable_model_pruning = 2;
  if (this->disable_model_pruning() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(2, this->disable_model_pruning(), target);
  }

  // .tensorflow.RewriterConfig.Toggle constant_folding = 3;
  if (this->constant_folding() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      3, this->constant_folding(), target);
  }

  // .tensorflow.RewriterConfig.MemOptType memory_optimization = 4;
  if (this->memory_optimization() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      4, this->memory_optimization(), target);
  }

  // .tensorflow.AutoParallelOptions auto_parallel = 5;
  if (this->has_auto_parallel()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        5, this->_internal_auto_parallel(), deterministic, target);
  }

  // string memory_optimizer_target_node_name_scope = 6;
  if (this->memory_optimizer_target_node_name_scope().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->memory_optimizer_target_node_name_scope().data(), static_cast<int>(this->memory_optimizer_target_node_name_scope().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.RewriterConfig.memory_optimizer_target_node_name_scope");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        6, this->memory_optimizer_target_node_name_scope(), target);
  }

  // .tensorflow.RewriterConfig.Toggle arithmetic_optimization = 7;
  if (this->arithmetic_optimization() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      7, this->arithmetic_optimization(), target);
  }

  // .tensorflow.RewriterConfig.Toggle dependency_optimization = 8;
  if (this->dependency_optimization() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      8, this->dependency_optimization(), target);
  }

  // .tensorflow.RewriterConfig.Toggle loop_optimization = 9;
  if (this->loop_optimization() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      9, this->loop_optimization(), target);
  }

  // .tensorflow.RewriterConfig.Toggle function_optimization = 10;
  if (this->function_optimization() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      10, this->function_optimization(), target);
  }

  // .tensorflow.RewriterConfig.Toggle debug_stripper = 11;
  if (this->debug_stripper() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      11, this->debug_stripper(), target);
  }

  // .tensorflow.RewriterConfig.NumIterationsType meta_optimizer_iterations = 12;
  if (this->meta_optimizer_iterations() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      12, this->meta_optimizer_iterations(), target);
  }

  // .tensorflow.RewriterConfig.Toggle shape_optimization = 13;
  if (this->shape_optimization() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      13, this->shape_optimization(), target);
  }

  // .tensorflow.RewriterConfig.Toggle remapping = 14;
  if (this->remapping() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      14, this->remapping(), target);
  }

  // .tensorflow.RewriterConfig.Toggle scoped_allocator_optimization = 15;
  if (this->scoped_allocator_optimization() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      15, this->scoped_allocator_optimization(), target);
  }

  // .tensorflow.ScopedAllocatorOptions scoped_allocator_opts = 16;
  if (this->has_scoped_allocator_opts()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        16, this->_internal_scoped_allocator_opts(), deterministic, target);
  }

  // int32 min_graph_nodes = 17;
  if (this->min_graph_nodes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(17, this->min_graph_nodes(), target);
  }

  // .tensorflow.RewriterConfig.Toggle pin_to_host_optimization = 18;
  if (this->pin_to_host_optimization() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      18, this->pin_to_host_optimization(), target);
  }

  // bool disable_meta_optimizer = 19;
  if (this->disable_meta_optimizer() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(19, this->disable_meta_optimizer(), target);
  }

  // int64 meta_optimizer_timeout_ms = 20;
  if (this->meta_optimizer_timeout_ms() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(20, this->meta_optimizer_timeout_ms(), target);
  }

  // bool fail_on_optimizer_errors = 21;
  if (this->fail_on_optimizer_errors() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(21, this->fail_on_optimizer_errors(), target);
  }

  // .tensorflow.RewriterConfig.Toggle implementation_selector = 22;
  if (this->implementation_selector() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      22, this->implementation_selector(), target);
  }

  // repeated string optimizers = 100;
  for (int i = 0, n = this->optimizers_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optimizers(i).data(), static_cast<int>(this->optimizers(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.RewriterConfig.optimizers");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(100, this->optimizers(i), target);
  }

  // repeated .tensorflow.RewriterConfig.CustomGraphOptimizer custom_optimizers = 200;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->custom_optimizers_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        200, this->custom_optimizers(static_cast<int>(i)), deterministic, target);
  }

  // .tensorflow.VerifierConfig inter_optimizer_verifier_config = 300;
  if (this->has_inter_optimizer_verifier_config()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        300, this->_internal_inter_optimizer_verifier_config(), deterministic, target);
  }

  // .tensorflow.VerifierConfig post_optimization_verifier_config = 301;
  if (this->has_post_optimization_verifier_config()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        301, this->_internal_post_optimization_verifier_config(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.RewriterConfig)
  return target;
}

size_t RewriterConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.RewriterConfig)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated string optimizers = 100;
  total_size += 2 *
      ::google::protobuf::internal::FromIntSize(this->optimizers_size());
  for (int i = 0, n = this->optimizers_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->optimizers(i));
  }

  // repeated .tensorflow.RewriterConfig.CustomGraphOptimizer custom_optimizers = 200;
  {
    unsigned int count = static_cast<unsigned int>(this->custom_optimizers_size());
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->custom_optimizers(static_cast<int>(i)));
    }
  }

  // string memory_optimizer_target_node_name_scope = 6;
  if (this->memory_optimizer_target_node_name_scope().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->memory_optimizer_target_node_name_scope());
  }

  // .tensorflow.AutoParallelOptions auto_parallel = 5;
  if (this->has_auto_parallel()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *auto_parallel_);
  }

  // .tensorflow.ScopedAllocatorOptions scoped_allocator_opts = 16;
  if (this->has_scoped_allocator_opts()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *scoped_allocator_opts_);
  }

  // .tensorflow.VerifierConfig inter_optimizer_verifier_config = 300;
  if (this->has_inter_optimizer_verifier_config()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *inter_optimizer_verifier_config_);
  }

  // .tensorflow.VerifierConfig post_optimization_verifier_config = 301;
  if (this->has_post_optimization_verifier_config()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *post_optimization_verifier_config_);
  }

  // .tensorflow.RewriterConfig.Toggle layout_optimizer = 1;
  if (this->layout_optimizer() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->layout_optimizer());
  }

  // .tensorflow.RewriterConfig.Toggle constant_folding = 3;
  if (this->constant_folding() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->constant_folding());
  }

  // .tensorflow.RewriterConfig.MemOptType memory_optimization = 4;
  if (this->memory_optimization() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->memory_optimization());
  }

  // .tensorflow.RewriterConfig.Toggle arithmetic_optimization = 7;
  if (this->arithmetic_optimization() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->arithmetic_optimization());
  }

  // .tensorflow.RewriterConfig.Toggle dependency_optimization = 8;
  if (this->dependency_optimization() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->dependency_optimization());
  }

  // .tensorflow.RewriterConfig.Toggle loop_optimization = 9;
  if (this->loop_optimization() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->loop_optimization());
  }

  // .tensorflow.RewriterConfig.Toggle function_optimization = 10;
  if (this->function_optimization() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->function_optimization());
  }

  // .tensorflow.RewriterConfig.Toggle debug_stripper = 11;
  if (this->debug_stripper() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->debug_stripper());
  }

  // .tensorflow.RewriterConfig.NumIterationsType meta_optimizer_iterations = 12;
  if (this->meta_optimizer_iterations() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->meta_optimizer_iterations());
  }

  // .tensorflow.RewriterConfig.Toggle shape_optimization = 13;
  if (this->shape_optimization() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->shape_optimization());
  }

  // .tensorflow.RewriterConfig.Toggle remapping = 14;
  if (this->remapping() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->remapping());
  }

  // bool disable_model_pruning = 2;
  if (this->disable_model_pruning() != 0) {
    total_size += 1 + 1;
  }

  // bool disable_meta_optimizer = 19;
  if (this->disable_meta_optimizer() != 0) {
    total_size += 2 + 1;
  }

  // bool fail_on_optimizer_errors = 21;
  if (this->fail_on_optimizer_errors() != 0) {
    total_size += 2 + 1;
  }

  // .tensorflow.RewriterConfig.Toggle scoped_allocator_optimization = 15;
  if (this->scoped_allocator_optimization() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->scoped_allocator_optimization());
  }

  // int32 min_graph_nodes = 17;
  if (this->min_graph_nodes() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->min_graph_nodes());
  }

  // int64 meta_optimizer_timeout_ms = 20;
  if (this->meta_optimizer_timeout_ms() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->meta_optimizer_timeout_ms());
  }

  // .tensorflow.RewriterConfig.Toggle pin_to_host_optimization = 18;
  if (this->pin_to_host_optimization() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->pin_to_host_optimization());
  }

  // .tensorflow.RewriterConfig.Toggle implementation_selector = 22;
  if (this->implementation_selector() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->implementation_selector());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RewriterConfig::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.RewriterConfig)
  GOOGLE_DCHECK_NE(&from, this);
  const RewriterConfig* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RewriterConfig>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.RewriterConfig)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.RewriterConfig)
    MergeFrom(*source);
  }
}

void RewriterConfig::MergeFrom(const RewriterConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.RewriterConfig)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  optimizers_.MergeFrom(from.optimizers_);
  custom_optimizers_.MergeFrom(from.custom_optimizers_);
  if (from.memory_optimizer_target_node_name_scope().size() > 0) {
    set_memory_optimizer_target_node_name_scope(from.memory_optimizer_target_node_name_scope());
  }
  if (from.has_auto_parallel()) {
    mutable_auto_parallel()->::tensorflow::AutoParallelOptions::MergeFrom(from.auto_parallel());
  }
  if (from.has_scoped_allocator_opts()) {
    mutable_scoped_allocator_opts()->::tensorflow::ScopedAllocatorOptions::MergeFrom(from.scoped_allocator_opts());
  }
  if (from.has_inter_optimizer_verifier_config()) {
    mutable_inter_optimizer_verifier_config()->::tensorflow::VerifierConfig::MergeFrom(from.inter_optimizer_verifier_config());
  }
  if (from.has_post_optimization_verifier_config()) {
    mutable_post_optimization_verifier_config()->::tensorflow::VerifierConfig::MergeFrom(from.post_optimization_verifier_config());
  }
  if (from.layout_optimizer() != 0) {
    set_layout_optimizer(from.layout_optimizer());
  }
  if (from.constant_folding() != 0) {
    set_constant_folding(from.constant_folding());
  }
  if (from.memory_optimization() != 0) {
    set_memory_optimization(from.memory_optimization());
  }
  if (from.arithmetic_optimization() != 0) {
    set_arithmetic_optimization(from.arithmetic_optimization());
  }
  if (from.dependency_optimization() != 0) {
    set_dependency_optimization(from.dependency_optimization());
  }
  if (from.loop_optimization() != 0) {
    set_loop_optimization(from.loop_optimization());
  }
  if (from.function_optimization() != 0) {
    set_function_optimization(from.function_optimization());
  }
  if (from.debug_stripper() != 0) {
    set_debug_stripper(from.debug_stripper());
  }
  if (from.meta_optimizer_iterations() != 0) {
    set_meta_optimizer_iterations(from.meta_optimizer_iterations());
  }
  if (from.shape_optimization() != 0) {
    set_shape_optimization(from.shape_optimization());
  }
  if (from.remapping() != 0) {
    set_remapping(from.remapping());
  }
  if (from.disable_model_pruning() != 0) {
    set_disable_model_pruning(from.disable_model_pruning());
  }
  if (from.disable_meta_optimizer() != 0) {
    set_disable_meta_optimizer(from.disable_meta_optimizer());
  }
  if (from.fail_on_optimizer_errors() != 0) {
    set_fail_on_optimizer_errors(from.fail_on_optimizer_errors());
  }
  if (from.scoped_allocator_optimization() != 0) {
    set_scoped_allocator_optimization(from.scoped_allocator_optimization());
  }
  if (from.min_graph_nodes() != 0) {
    set_min_graph_nodes(from.min_graph_nodes());
  }
  if (from.meta_optimizer_timeout_ms() != 0) {
    set_meta_optimizer_timeout_ms(from.meta_optimizer_timeout_ms());
  }
  if (from.pin_to_host_optimization() != 0) {
    set_pin_to_host_optimization(from.pin_to_host_optimization());
  }
  if (from.implementation_selector() != 0) {
    set_implementation_selector(from.implementation_selector());
  }
}

void RewriterConfig::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.RewriterConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RewriterConfig::CopyFrom(const RewriterConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.RewriterConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RewriterConfig::IsInitialized() const {
  return true;
}

void RewriterConfig::Swap(RewriterConfig* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    RewriterConfig* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void RewriterConfig::UnsafeArenaSwap(RewriterConfig* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void RewriterConfig::InternalSwap(RewriterConfig* other) {
  using std::swap;
  optimizers_.InternalSwap(CastToBase(&other->optimizers_));
  CastToBase(&custom_optimizers_)->InternalSwap(CastToBase(&other->custom_optimizers_));
  memory_optimizer_target_node_name_scope_.Swap(&other->memory_optimizer_target_node_name_scope_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(auto_parallel_, other->auto_parallel_);
  swap(scoped_allocator_opts_, other->scoped_allocator_opts_);
  swap(inter_optimizer_verifier_config_, other->inter_optimizer_verifier_config_);
  swap(post_optimization_verifier_config_, other->post_optimization_verifier_config_);
  swap(layout_optimizer_, other->layout_optimizer_);
  swap(constant_folding_, other->constant_folding_);
  swap(memory_optimization_, other->memory_optimization_);
  swap(arithmetic_optimization_, other->arithmetic_optimization_);
  swap(dependency_optimization_, other->dependency_optimization_);
  swap(loop_optimization_, other->loop_optimization_);
  swap(function_optimization_, other->function_optimization_);
  swap(debug_stripper_, other->debug_stripper_);
  swap(meta_optimizer_iterations_, other->meta_optimizer_iterations_);
  swap(shape_optimization_, other->shape_optimization_);
  swap(remapping_, other->remapping_);
  swap(disable_model_pruning_, other->disable_model_pruning_);
  swap(disable_meta_optimizer_, other->disable_meta_optimizer_);
  swap(fail_on_optimizer_errors_, other->fail_on_optimizer_errors_);
  swap(scoped_allocator_optimization_, other->scoped_allocator_optimization_);
  swap(min_graph_nodes_, other->min_graph_nodes_);
  swap(meta_optimizer_timeout_ms_, other->meta_optimizer_timeout_ms_);
  swap(pin_to_host_optimization_, other->pin_to_host_optimization_);
  swap(implementation_selector_, other->implementation_selector_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata RewriterConfig::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::AutoParallelOptions* Arena::CreateMaybeMessage< ::tensorflow::AutoParallelOptions >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::AutoParallelOptions >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::ScopedAllocatorOptions* Arena::CreateMaybeMessage< ::tensorflow::ScopedAllocatorOptions >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::ScopedAllocatorOptions >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::RewriterConfig_CustomGraphOptimizer* Arena::CreateMaybeMessage< ::tensorflow::RewriterConfig_CustomGraphOptimizer >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::RewriterConfig_CustomGraphOptimizer >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::RewriterConfig* Arena::CreateMaybeMessage< ::tensorflow::RewriterConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::RewriterConfig >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

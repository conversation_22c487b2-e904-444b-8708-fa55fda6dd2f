// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/device_properties.proto

#include "tensorflow/core/protobuf/device_properties.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_DeviceProperties_EnvironmentEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_DeviceProperties;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto
namespace tensorflow {
class DeviceProperties_EnvironmentEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<DeviceProperties_EnvironmentEntry_DoNotUse>
      _instance;
} _DeviceProperties_EnvironmentEntry_DoNotUse_default_instance_;
class DevicePropertiesDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<DeviceProperties>
      _instance;
} _DeviceProperties_default_instance_;
class NamedDeviceDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<NamedDevice>
      _instance;
} _NamedDevice_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto {
static void InitDefaultsDeviceProperties_EnvironmentEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_DeviceProperties_EnvironmentEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::DeviceProperties_EnvironmentEntry_DoNotUse();
  }
  ::tensorflow::DeviceProperties_EnvironmentEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_DeviceProperties_EnvironmentEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsDeviceProperties_EnvironmentEntry_DoNotUse}, {}};

static void InitDefaultsDeviceProperties() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_DeviceProperties_default_instance_;
    new (ptr) ::tensorflow::DeviceProperties();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::DeviceProperties::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_DeviceProperties =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsDeviceProperties}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto::scc_info_DeviceProperties_EnvironmentEntry_DoNotUse.base,}};

static void InitDefaultsNamedDevice() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_NamedDevice_default_instance_;
    new (ptr) ::tensorflow::NamedDevice();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::NamedDevice::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_NamedDevice =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsNamedDevice}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto::scc_info_DeviceProperties.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_DeviceProperties_EnvironmentEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_DeviceProperties.base);
  ::google::protobuf::internal::InitSCC(&scc_info_NamedDevice.base);
}

::google::protobuf::Metadata file_level_metadata[3];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceProperties_EnvironmentEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceProperties_EnvironmentEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceProperties_EnvironmentEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceProperties_EnvironmentEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceProperties, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceProperties, type_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceProperties, vendor_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceProperties, model_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceProperties, frequency_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceProperties, num_cores_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceProperties, environment_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceProperties, num_registers_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceProperties, l1_cache_size_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceProperties, l2_cache_size_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceProperties, l3_cache_size_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceProperties, shared_memory_size_per_multiprocessor_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceProperties, memory_size_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DeviceProperties, bandwidth_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NamedDevice, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NamedDevice, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::NamedDevice, properties_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, 7, sizeof(::tensorflow::DeviceProperties_EnvironmentEntry_DoNotUse)},
  { 9, -1, sizeof(::tensorflow::DeviceProperties)},
  { 27, -1, sizeof(::tensorflow::NamedDevice)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_DeviceProperties_EnvironmentEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_DeviceProperties_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_NamedDevice_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/protobuf/device_properties.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 3);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n0tensorflow/core/protobuf/device_proper"
      "ties.proto\022\ntensorflow\"\220\003\n\020DevicePropert"
      "ies\022\014\n\004type\030\001 \001(\t\022\016\n\006vendor\030\002 \001(\t\022\r\n\005mod"
      "el\030\003 \001(\t\022\021\n\tfrequency\030\004 \001(\003\022\021\n\tnum_cores"
      "\030\005 \001(\003\022B\n\013environment\030\006 \003(\0132-.tensorflow"
      ".DeviceProperties.EnvironmentEntry\022\025\n\rnu"
      "m_registers\030\007 \001(\003\022\025\n\rl1_cache_size\030\010 \001(\003"
      "\022\025\n\rl2_cache_size\030\t \001(\003\022\025\n\rl3_cache_size"
      "\030\n \001(\003\022-\n%shared_memory_size_per_multipr"
      "ocessor\030\013 \001(\003\022\023\n\013memory_size\030\014 \001(\003\022\021\n\tba"
      "ndwidth\030\r \001(\003\0322\n\020EnvironmentEntry\022\013\n\003key"
      "\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001\"M\n\013NamedDevice"
      "\022\014\n\004name\030\001 \001(\t\0220\n\nproperties\030\002 \001(\0132\034.ten"
      "sorflow.DevicePropertiesBYB\026DeviceProper"
      "tiesProtosZ<github.com/tensorflow/tensor"
      "flow/tensorflow/go/core/protobuf\370\001\001b\006pro"
      "to3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 643);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/protobuf/device_properties.proto", &protobuf_RegisterTypes);
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto
namespace tensorflow {

// ===================================================================

DeviceProperties_EnvironmentEntry_DoNotUse::DeviceProperties_EnvironmentEntry_DoNotUse() {}
DeviceProperties_EnvironmentEntry_DoNotUse::DeviceProperties_EnvironmentEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void DeviceProperties_EnvironmentEntry_DoNotUse::MergeFrom(const DeviceProperties_EnvironmentEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata DeviceProperties_EnvironmentEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto::file_level_metadata[0];
}
void DeviceProperties_EnvironmentEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void DeviceProperties::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int DeviceProperties::kTypeFieldNumber;
const int DeviceProperties::kVendorFieldNumber;
const int DeviceProperties::kModelFieldNumber;
const int DeviceProperties::kFrequencyFieldNumber;
const int DeviceProperties::kNumCoresFieldNumber;
const int DeviceProperties::kEnvironmentFieldNumber;
const int DeviceProperties::kNumRegistersFieldNumber;
const int DeviceProperties::kL1CacheSizeFieldNumber;
const int DeviceProperties::kL2CacheSizeFieldNumber;
const int DeviceProperties::kL3CacheSizeFieldNumber;
const int DeviceProperties::kSharedMemorySizePerMultiprocessorFieldNumber;
const int DeviceProperties::kMemorySizeFieldNumber;
const int DeviceProperties::kBandwidthFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DeviceProperties::DeviceProperties()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto::scc_info_DeviceProperties.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.DeviceProperties)
}
DeviceProperties::DeviceProperties(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  environment_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto::scc_info_DeviceProperties.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.DeviceProperties)
}
DeviceProperties::DeviceProperties(const DeviceProperties& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  environment_.MergeFrom(from.environment_);
  type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.type().size() > 0) {
    type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.type(),
      GetArenaNoVirtual());
  }
  vendor_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.vendor().size() > 0) {
    vendor_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.vendor(),
      GetArenaNoVirtual());
  }
  model_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.model().size() > 0) {
    model_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.model(),
      GetArenaNoVirtual());
  }
  ::memcpy(&frequency_, &from.frequency_,
    static_cast<size_t>(reinterpret_cast<char*>(&bandwidth_) -
    reinterpret_cast<char*>(&frequency_)) + sizeof(bandwidth_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.DeviceProperties)
}

void DeviceProperties::SharedCtor() {
  type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  vendor_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  model_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&frequency_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&bandwidth_) -
      reinterpret_cast<char*>(&frequency_)) + sizeof(bandwidth_));
}

DeviceProperties::~DeviceProperties() {
  // @@protoc_insertion_point(destructor:tensorflow.DeviceProperties)
  SharedDtor();
}

void DeviceProperties::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  type_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  vendor_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  model_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void DeviceProperties::ArenaDtor(void* object) {
  DeviceProperties* _this = reinterpret_cast< DeviceProperties* >(object);
  (void)_this;
}
void DeviceProperties::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void DeviceProperties::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* DeviceProperties::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const DeviceProperties& DeviceProperties::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto::scc_info_DeviceProperties.base);
  return *internal_default_instance();
}


void DeviceProperties::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.DeviceProperties)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  environment_.Clear();
  type_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  vendor_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  model_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  ::memset(&frequency_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&bandwidth_) -
      reinterpret_cast<char*>(&frequency_)) + sizeof(bandwidth_));
  _internal_metadata_.Clear();
}

bool DeviceProperties::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.DeviceProperties)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string type = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_type()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->type().data(), static_cast<int>(this->type().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.DeviceProperties.type"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string vendor = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_vendor()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->vendor().data(), static_cast<int>(this->vendor().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.DeviceProperties.vendor"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string model = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_model()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->model().data(), static_cast<int>(this->model().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.DeviceProperties.model"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 frequency = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &frequency_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 num_cores = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &num_cores_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, string> environment = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DeviceProperties_EnvironmentEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              DeviceProperties_EnvironmentEntry_DoNotUse,
              ::std::string, ::std::string,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              0 >,
            ::google::protobuf::Map< ::std::string, ::std::string > > parser(&environment_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.DeviceProperties.EnvironmentEntry.key"));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.value().data(), static_cast<int>(parser.value().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.DeviceProperties.EnvironmentEntry.value"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 num_registers = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(56u /* 56 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &num_registers_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 l1_cache_size = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(64u /* 64 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &l1_cache_size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 l2_cache_size = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(72u /* 72 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &l2_cache_size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 l3_cache_size = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(80u /* 80 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &l3_cache_size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 shared_memory_size_per_multiprocessor = 11;
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(88u /* 88 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &shared_memory_size_per_multiprocessor_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 memory_size = 12;
      case 12: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(96u /* 96 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &memory_size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 bandwidth = 13;
      case 13: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(104u /* 104 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &bandwidth_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.DeviceProperties)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.DeviceProperties)
  return false;
#undef DO_
}

void DeviceProperties::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.DeviceProperties)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string type = 1;
  if (this->type().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->type().data(), static_cast<int>(this->type().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.DeviceProperties.type");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->type(), output);
  }

  // string vendor = 2;
  if (this->vendor().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->vendor().data(), static_cast<int>(this->vendor().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.DeviceProperties.vendor");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->vendor(), output);
  }

  // string model = 3;
  if (this->model().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->model().data(), static_cast<int>(this->model().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.DeviceProperties.model");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->model(), output);
  }

  // int64 frequency = 4;
  if (this->frequency() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->frequency(), output);
  }

  // int64 num_cores = 5;
  if (this->num_cores() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(5, this->num_cores(), output);
  }

  // map<string, string> environment = 6;
  if (!this->environment().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.DeviceProperties.EnvironmentEntry.key");
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.DeviceProperties.EnvironmentEntry.value");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->environment().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->environment().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->environment().begin();
          it != this->environment().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<DeviceProperties_EnvironmentEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(environment_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            6, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<DeviceProperties_EnvironmentEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->environment().begin();
          it != this->environment().end(); ++it) {
        entry.reset(environment_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            6, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // int64 num_registers = 7;
  if (this->num_registers() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(7, this->num_registers(), output);
  }

  // int64 l1_cache_size = 8;
  if (this->l1_cache_size() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->l1_cache_size(), output);
  }

  // int64 l2_cache_size = 9;
  if (this->l2_cache_size() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->l2_cache_size(), output);
  }

  // int64 l3_cache_size = 10;
  if (this->l3_cache_size() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->l3_cache_size(), output);
  }

  // int64 shared_memory_size_per_multiprocessor = 11;
  if (this->shared_memory_size_per_multiprocessor() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->shared_memory_size_per_multiprocessor(), output);
  }

  // int64 memory_size = 12;
  if (this->memory_size() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->memory_size(), output);
  }

  // int64 bandwidth = 13;
  if (this->bandwidth() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->bandwidth(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.DeviceProperties)
}

::google::protobuf::uint8* DeviceProperties::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.DeviceProperties)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string type = 1;
  if (this->type().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->type().data(), static_cast<int>(this->type().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.DeviceProperties.type");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->type(), target);
  }

  // string vendor = 2;
  if (this->vendor().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->vendor().data(), static_cast<int>(this->vendor().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.DeviceProperties.vendor");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->vendor(), target);
  }

  // string model = 3;
  if (this->model().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->model().data(), static_cast<int>(this->model().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.DeviceProperties.model");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->model(), target);
  }

  // int64 frequency = 4;
  if (this->frequency() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->frequency(), target);
  }

  // int64 num_cores = 5;
  if (this->num_cores() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(5, this->num_cores(), target);
  }

  // map<string, string> environment = 6;
  if (!this->environment().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.DeviceProperties.EnvironmentEntry.key");
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.DeviceProperties.EnvironmentEntry.value");
      }
    };

    if (deterministic &&
        this->environment().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->environment().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->environment().begin();
          it != this->environment().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<DeviceProperties_EnvironmentEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(environment_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       6, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<DeviceProperties_EnvironmentEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->environment().begin();
          it != this->environment().end(); ++it) {
        entry.reset(environment_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       6, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // int64 num_registers = 7;
  if (this->num_registers() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(7, this->num_registers(), target);
  }

  // int64 l1_cache_size = 8;
  if (this->l1_cache_size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->l1_cache_size(), target);
  }

  // int64 l2_cache_size = 9;
  if (this->l2_cache_size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->l2_cache_size(), target);
  }

  // int64 l3_cache_size = 10;
  if (this->l3_cache_size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->l3_cache_size(), target);
  }

  // int64 shared_memory_size_per_multiprocessor = 11;
  if (this->shared_memory_size_per_multiprocessor() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->shared_memory_size_per_multiprocessor(), target);
  }

  // int64 memory_size = 12;
  if (this->memory_size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->memory_size(), target);
  }

  // int64 bandwidth = 13;
  if (this->bandwidth() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->bandwidth(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.DeviceProperties)
  return target;
}

size_t DeviceProperties::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.DeviceProperties)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // map<string, string> environment = 6;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->environment_size());
  {
    ::std::unique_ptr<DeviceProperties_EnvironmentEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
        it = this->environment().begin();
        it != this->environment().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(environment_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // string type = 1;
  if (this->type().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->type());
  }

  // string vendor = 2;
  if (this->vendor().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->vendor());
  }

  // string model = 3;
  if (this->model().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->model());
  }

  // int64 frequency = 4;
  if (this->frequency() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->frequency());
  }

  // int64 num_cores = 5;
  if (this->num_cores() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->num_cores());
  }

  // int64 num_registers = 7;
  if (this->num_registers() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->num_registers());
  }

  // int64 l1_cache_size = 8;
  if (this->l1_cache_size() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->l1_cache_size());
  }

  // int64 l2_cache_size = 9;
  if (this->l2_cache_size() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->l2_cache_size());
  }

  // int64 l3_cache_size = 10;
  if (this->l3_cache_size() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->l3_cache_size());
  }

  // int64 shared_memory_size_per_multiprocessor = 11;
  if (this->shared_memory_size_per_multiprocessor() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->shared_memory_size_per_multiprocessor());
  }

  // int64 memory_size = 12;
  if (this->memory_size() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->memory_size());
  }

  // int64 bandwidth = 13;
  if (this->bandwidth() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->bandwidth());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DeviceProperties::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.DeviceProperties)
  GOOGLE_DCHECK_NE(&from, this);
  const DeviceProperties* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const DeviceProperties>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.DeviceProperties)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.DeviceProperties)
    MergeFrom(*source);
  }
}

void DeviceProperties::MergeFrom(const DeviceProperties& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.DeviceProperties)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  environment_.MergeFrom(from.environment_);
  if (from.type().size() > 0) {
    set_type(from.type());
  }
  if (from.vendor().size() > 0) {
    set_vendor(from.vendor());
  }
  if (from.model().size() > 0) {
    set_model(from.model());
  }
  if (from.frequency() != 0) {
    set_frequency(from.frequency());
  }
  if (from.num_cores() != 0) {
    set_num_cores(from.num_cores());
  }
  if (from.num_registers() != 0) {
    set_num_registers(from.num_registers());
  }
  if (from.l1_cache_size() != 0) {
    set_l1_cache_size(from.l1_cache_size());
  }
  if (from.l2_cache_size() != 0) {
    set_l2_cache_size(from.l2_cache_size());
  }
  if (from.l3_cache_size() != 0) {
    set_l3_cache_size(from.l3_cache_size());
  }
  if (from.shared_memory_size_per_multiprocessor() != 0) {
    set_shared_memory_size_per_multiprocessor(from.shared_memory_size_per_multiprocessor());
  }
  if (from.memory_size() != 0) {
    set_memory_size(from.memory_size());
  }
  if (from.bandwidth() != 0) {
    set_bandwidth(from.bandwidth());
  }
}

void DeviceProperties::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.DeviceProperties)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DeviceProperties::CopyFrom(const DeviceProperties& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.DeviceProperties)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DeviceProperties::IsInitialized() const {
  return true;
}

void DeviceProperties::Swap(DeviceProperties* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    DeviceProperties* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void DeviceProperties::UnsafeArenaSwap(DeviceProperties* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void DeviceProperties::InternalSwap(DeviceProperties* other) {
  using std::swap;
  environment_.Swap(&other->environment_);
  type_.Swap(&other->type_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  vendor_.Swap(&other->vendor_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  model_.Swap(&other->model_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(frequency_, other->frequency_);
  swap(num_cores_, other->num_cores_);
  swap(num_registers_, other->num_registers_);
  swap(l1_cache_size_, other->l1_cache_size_);
  swap(l2_cache_size_, other->l2_cache_size_);
  swap(l3_cache_size_, other->l3_cache_size_);
  swap(shared_memory_size_per_multiprocessor_, other->shared_memory_size_per_multiprocessor_);
  swap(memory_size_, other->memory_size_);
  swap(bandwidth_, other->bandwidth_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata DeviceProperties::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void NamedDevice::InitAsDefaultInstance() {
  ::tensorflow::_NamedDevice_default_instance_._instance.get_mutable()->properties_ = const_cast< ::tensorflow::DeviceProperties*>(
      ::tensorflow::DeviceProperties::internal_default_instance());
}
void NamedDevice::unsafe_arena_set_allocated_properties(
    ::tensorflow::DeviceProperties* properties) {
  if (GetArenaNoVirtual() == NULL) {
    delete properties_;
  }
  properties_ = properties;
  if (properties) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.NamedDevice.properties)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int NamedDevice::kNameFieldNumber;
const int NamedDevice::kPropertiesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

NamedDevice::NamedDevice()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto::scc_info_NamedDevice.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.NamedDevice)
}
NamedDevice::NamedDevice(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto::scc_info_NamedDevice.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.NamedDevice)
}
NamedDevice::NamedDevice(const NamedDevice& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name(),
      GetArenaNoVirtual());
  }
  if (from.has_properties()) {
    properties_ = new ::tensorflow::DeviceProperties(*from.properties_);
  } else {
    properties_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.NamedDevice)
}

void NamedDevice::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  properties_ = NULL;
}

NamedDevice::~NamedDevice() {
  // @@protoc_insertion_point(destructor:tensorflow.NamedDevice)
  SharedDtor();
}

void NamedDevice::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete properties_;
}

void NamedDevice::ArenaDtor(void* object) {
  NamedDevice* _this = reinterpret_cast< NamedDevice* >(object);
  (void)_this;
}
void NamedDevice::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void NamedDevice::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* NamedDevice::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const NamedDevice& NamedDevice::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto::scc_info_NamedDevice.base);
  return *internal_default_instance();
}


void NamedDevice::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.NamedDevice)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && properties_ != NULL) {
    delete properties_;
  }
  properties_ = NULL;
  _internal_metadata_.Clear();
}

bool NamedDevice::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.NamedDevice)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.NamedDevice.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.DeviceProperties properties = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_properties()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.NamedDevice)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.NamedDevice)
  return false;
#undef DO_
}

void NamedDevice::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.NamedDevice)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.NamedDevice.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // .tensorflow.DeviceProperties properties = 2;
  if (this->has_properties()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_properties(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.NamedDevice)
}

::google::protobuf::uint8* NamedDevice::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.NamedDevice)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.NamedDevice.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // .tensorflow.DeviceProperties properties = 2;
  if (this->has_properties()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_properties(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.NamedDevice)
  return target;
}

size_t NamedDevice::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.NamedDevice)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // .tensorflow.DeviceProperties properties = 2;
  if (this->has_properties()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *properties_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void NamedDevice::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.NamedDevice)
  GOOGLE_DCHECK_NE(&from, this);
  const NamedDevice* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const NamedDevice>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.NamedDevice)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.NamedDevice)
    MergeFrom(*source);
  }
}

void NamedDevice::MergeFrom(const NamedDevice& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.NamedDevice)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.name().size() > 0) {
    set_name(from.name());
  }
  if (from.has_properties()) {
    mutable_properties()->::tensorflow::DeviceProperties::MergeFrom(from.properties());
  }
}

void NamedDevice::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.NamedDevice)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void NamedDevice::CopyFrom(const NamedDevice& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.NamedDevice)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool NamedDevice::IsInitialized() const {
  return true;
}

void NamedDevice::Swap(NamedDevice* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    NamedDevice* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void NamedDevice::UnsafeArenaSwap(NamedDevice* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void NamedDevice::InternalSwap(NamedDevice* other) {
  using std::swap;
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(properties_, other->properties_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata NamedDevice::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::DeviceProperties_EnvironmentEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::DeviceProperties_EnvironmentEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::DeviceProperties_EnvironmentEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::DeviceProperties* Arena::CreateMaybeMessage< ::tensorflow::DeviceProperties >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::DeviceProperties >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::NamedDevice* Arena::CreateMaybeMessage< ::tensorflow::NamedDevice >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::NamedDevice >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

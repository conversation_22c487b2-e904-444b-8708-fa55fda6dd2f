// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/control_flow.proto

#include "tensorflow/core/protobuf/control_flow.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_ValuesDef_ExternalValuesEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_CondContextDef;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_ValuesDef;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto
namespace tensorflow {
class ValuesDef_ExternalValuesEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ValuesDef_ExternalValuesEntry_DoNotUse>
      _instance;
} _ValuesDef_ExternalValuesEntry_DoNotUse_default_instance_;
class ValuesDefDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ValuesDef>
      _instance;
} _ValuesDef_default_instance_;
class ControlFlowContextDefDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ControlFlowContextDef>
      _instance;
  const ::tensorflow::CondContextDef* cond_ctxt_;
  const ::tensorflow::WhileContextDef* while_ctxt_;
} _ControlFlowContextDef_default_instance_;
class CondContextDefDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CondContextDef>
      _instance;
} _CondContextDef_default_instance_;
class WhileContextDefDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<WhileContextDef>
      _instance;
} _WhileContextDef_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto {
static void InitDefaultsValuesDef_ExternalValuesEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_ValuesDef_ExternalValuesEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::ValuesDef_ExternalValuesEntry_DoNotUse();
  }
  ::tensorflow::ValuesDef_ExternalValuesEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_ValuesDef_ExternalValuesEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsValuesDef_ExternalValuesEntry_DoNotUse}, {}};

static void InitDefaultsValuesDef() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_ValuesDef_default_instance_;
    new (ptr) ::tensorflow::ValuesDef();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::ValuesDef::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_ValuesDef =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsValuesDef}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::scc_info_ValuesDef_ExternalValuesEntry_DoNotUse.base,}};

static void InitDefaultsCondContextDef() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_ControlFlowContextDef_default_instance_;
    new (ptr) ::tensorflow::ControlFlowContextDef();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  {
    void* ptr = &::tensorflow::_CondContextDef_default_instance_;
    new (ptr) ::tensorflow::CondContextDef();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  {
    void* ptr = &::tensorflow::_WhileContextDef_default_instance_;
    new (ptr) ::tensorflow::WhileContextDef();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::ControlFlowContextDef::InitAsDefaultInstance();
  ::tensorflow::CondContextDef::InitAsDefaultInstance();
  ::tensorflow::WhileContextDef::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_CondContextDef =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsCondContextDef}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::scc_info_ValuesDef.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_ValuesDef_ExternalValuesEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ValuesDef.base);
  ::google::protobuf::internal::InitSCC(&scc_info_CondContextDef.base);
}

::google::protobuf::Metadata file_level_metadata[5];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ValuesDef_ExternalValuesEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ValuesDef_ExternalValuesEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ValuesDef_ExternalValuesEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ValuesDef_ExternalValuesEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ValuesDef, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ValuesDef, values_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ValuesDef, external_values_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ControlFlowContextDef, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ControlFlowContextDef, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  offsetof(::tensorflow::ControlFlowContextDefDefaultTypeInternal, cond_ctxt_),
  offsetof(::tensorflow::ControlFlowContextDefDefaultTypeInternal, while_ctxt_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ControlFlowContextDef, ctxt_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CondContextDef, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CondContextDef, context_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CondContextDef, pred_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CondContextDef, pivot_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CondContextDef, branch_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CondContextDef, values_def_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CondContextDef, nested_contexts_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::WhileContextDef, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::WhileContextDef, context_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::WhileContextDef, parallel_iterations_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::WhileContextDef, back_prop_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::WhileContextDef, swap_memory_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::WhileContextDef, pivot_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::WhileContextDef, pivot_for_pred_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::WhileContextDef, pivot_for_body_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::WhileContextDef, loop_exit_names_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::WhileContextDef, loop_enter_names_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::WhileContextDef, values_def_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::WhileContextDef, maximum_iterations_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::WhileContextDef, nested_contexts_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, 7, sizeof(::tensorflow::ValuesDef_ExternalValuesEntry_DoNotUse)},
  { 9, -1, sizeof(::tensorflow::ValuesDef)},
  { 16, -1, sizeof(::tensorflow::ControlFlowContextDef)},
  { 24, -1, sizeof(::tensorflow::CondContextDef)},
  { 35, -1, sizeof(::tensorflow::WhileContextDef)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_ValuesDef_ExternalValuesEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_ValuesDef_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_ControlFlowContextDef_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_CondContextDef_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_WhileContextDef_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/protobuf/control_flow.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 5);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n+tensorflow/core/protobuf/control_flow."
      "proto\022\ntensorflow\"\226\001\n\tValuesDef\022\016\n\006value"
      "s\030\001 \003(\t\022B\n\017external_values\030\002 \003(\0132).tenso"
      "rflow.ValuesDef.ExternalValuesEntry\0325\n\023E"
      "xternalValuesEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005value"
      "\030\002 \001(\t:\0028\001\"\203\001\n\025ControlFlowContextDef\022/\n\t"
      "cond_ctxt\030\001 \001(\0132\032.tensorflow.CondContext"
      "DefH\000\0221\n\nwhile_ctxt\030\002 \001(\0132\033.tensorflow.W"
      "hileContextDefH\000B\006\n\004ctxt\"\304\001\n\016CondContext"
      "Def\022\024\n\014context_name\030\001 \001(\t\022\021\n\tpred_name\030\002"
      " \001(\t\022\022\n\npivot_name\030\003 \001(\t\022\016\n\006branch\030\004 \001(\005"
      "\022)\n\nvalues_def\030\005 \001(\0132\025.tensorflow.Values"
      "Def\022:\n\017nested_contexts\030\006 \003(\0132!.tensorflo"
      "w.ControlFlowContextDef\"\365\002\n\017WhileContext"
      "Def\022\024\n\014context_name\030\001 \001(\t\022\033\n\023parallel_it"
      "erations\030\002 \001(\005\022\021\n\tback_prop\030\003 \001(\010\022\023\n\013swa"
      "p_memory\030\004 \001(\010\022\022\n\npivot_name\030\005 \001(\t\022\033\n\023pi"
      "vot_for_pred_name\030\006 \001(\t\022\033\n\023pivot_for_bod"
      "y_name\030\007 \001(\t\022\027\n\017loop_exit_names\030\010 \003(\t\022\030\n"
      "\020loop_enter_names\030\n \003(\t\022)\n\nvalues_def\030\t "
      "\001(\0132\025.tensorflow.ValuesDef\022\037\n\027maximum_it"
      "erations_name\030\013 \001(\t\022:\n\017nested_contexts\030\014"
      " \003(\0132!.tensorflow.ControlFlowContextDefB"
      "p\n\030org.tensorflow.frameworkB\021ControlFlow"
      "ProtosP\001Z<github.com/tensorflow/tensorfl"
      "ow/tensorflow/go/core/protobuf\370\001\001b\006proto"
      "3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 1041);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/protobuf/control_flow.proto", &protobuf_RegisterTypes);
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto
namespace tensorflow {

// ===================================================================

ValuesDef_ExternalValuesEntry_DoNotUse::ValuesDef_ExternalValuesEntry_DoNotUse() {}
ValuesDef_ExternalValuesEntry_DoNotUse::ValuesDef_ExternalValuesEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void ValuesDef_ExternalValuesEntry_DoNotUse::MergeFrom(const ValuesDef_ExternalValuesEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata ValuesDef_ExternalValuesEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::file_level_metadata[0];
}
void ValuesDef_ExternalValuesEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void ValuesDef::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ValuesDef::kValuesFieldNumber;
const int ValuesDef::kExternalValuesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ValuesDef::ValuesDef()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::scc_info_ValuesDef.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.ValuesDef)
}
ValuesDef::ValuesDef(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  values_(arena),
  external_values_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::scc_info_ValuesDef.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.ValuesDef)
}
ValuesDef::ValuesDef(const ValuesDef& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      values_(from.values_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  external_values_.MergeFrom(from.external_values_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.ValuesDef)
}

void ValuesDef::SharedCtor() {
}

ValuesDef::~ValuesDef() {
  // @@protoc_insertion_point(destructor:tensorflow.ValuesDef)
  SharedDtor();
}

void ValuesDef::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void ValuesDef::ArenaDtor(void* object) {
  ValuesDef* _this = reinterpret_cast< ValuesDef* >(object);
  (void)_this;
}
void ValuesDef::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void ValuesDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ValuesDef::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ValuesDef& ValuesDef::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::scc_info_ValuesDef.base);
  return *internal_default_instance();
}


void ValuesDef::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.ValuesDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  values_.Clear();
  external_values_.Clear();
  _internal_metadata_.Clear();
}

bool ValuesDef::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.ValuesDef)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated string values = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_values()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->values(this->values_size() - 1).data(),
            static_cast<int>(this->values(this->values_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ValuesDef.values"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, string> external_values = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          ValuesDef_ExternalValuesEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              ValuesDef_ExternalValuesEntry_DoNotUse,
              ::std::string, ::std::string,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              0 >,
            ::google::protobuf::Map< ::std::string, ::std::string > > parser(&external_values_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ValuesDef.ExternalValuesEntry.key"));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.value().data(), static_cast<int>(parser.value().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ValuesDef.ExternalValuesEntry.value"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.ValuesDef)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.ValuesDef)
  return false;
#undef DO_
}

void ValuesDef::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.ValuesDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string values = 1;
  for (int i = 0, n = this->values_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->values(i).data(), static_cast<int>(this->values(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ValuesDef.values");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->values(i), output);
  }

  // map<string, string> external_values = 2;
  if (!this->external_values().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.ValuesDef.ExternalValuesEntry.key");
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.ValuesDef.ExternalValuesEntry.value");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->external_values().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->external_values().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->external_values().begin();
          it != this->external_values().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ValuesDef_ExternalValuesEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(external_values_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<ValuesDef_ExternalValuesEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->external_values().begin();
          it != this->external_values().end(); ++it) {
        entry.reset(external_values_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.ValuesDef)
}

::google::protobuf::uint8* ValuesDef::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.ValuesDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string values = 1;
  for (int i = 0, n = this->values_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->values(i).data(), static_cast<int>(this->values(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ValuesDef.values");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(1, this->values(i), target);
  }

  // map<string, string> external_values = 2;
  if (!this->external_values().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.ValuesDef.ExternalValuesEntry.key");
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.ValuesDef.ExternalValuesEntry.value");
      }
    };

    if (deterministic &&
        this->external_values().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->external_values().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->external_values().begin();
          it != this->external_values().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ValuesDef_ExternalValuesEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(external_values_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<ValuesDef_ExternalValuesEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->external_values().begin();
          it != this->external_values().end(); ++it) {
        entry.reset(external_values_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.ValuesDef)
  return target;
}

size_t ValuesDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.ValuesDef)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated string values = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->values_size());
  for (int i = 0, n = this->values_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->values(i));
  }

  // map<string, string> external_values = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->external_values_size());
  {
    ::std::unique_ptr<ValuesDef_ExternalValuesEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
        it = this->external_values().begin();
        it != this->external_values().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(external_values_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ValuesDef::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.ValuesDef)
  GOOGLE_DCHECK_NE(&from, this);
  const ValuesDef* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ValuesDef>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.ValuesDef)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.ValuesDef)
    MergeFrom(*source);
  }
}

void ValuesDef::MergeFrom(const ValuesDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.ValuesDef)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  values_.MergeFrom(from.values_);
  external_values_.MergeFrom(from.external_values_);
}

void ValuesDef::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.ValuesDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ValuesDef::CopyFrom(const ValuesDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.ValuesDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ValuesDef::IsInitialized() const {
  return true;
}

void ValuesDef::Swap(ValuesDef* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    ValuesDef* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void ValuesDef::UnsafeArenaSwap(ValuesDef* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void ValuesDef::InternalSwap(ValuesDef* other) {
  using std::swap;
  values_.InternalSwap(CastToBase(&other->values_));
  external_values_.Swap(&other->external_values_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ValuesDef::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ControlFlowContextDef::InitAsDefaultInstance() {
  ::tensorflow::_ControlFlowContextDef_default_instance_.cond_ctxt_ = const_cast< ::tensorflow::CondContextDef*>(
      ::tensorflow::CondContextDef::internal_default_instance());
  ::tensorflow::_ControlFlowContextDef_default_instance_.while_ctxt_ = const_cast< ::tensorflow::WhileContextDef*>(
      ::tensorflow::WhileContextDef::internal_default_instance());
}
void ControlFlowContextDef::set_allocated_cond_ctxt(::tensorflow::CondContextDef* cond_ctxt) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_ctxt();
  if (cond_ctxt) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(cond_ctxt);
    if (message_arena != submessage_arena) {
      cond_ctxt = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, cond_ctxt, submessage_arena);
    }
    set_has_cond_ctxt();
    ctxt_.cond_ctxt_ = cond_ctxt;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ControlFlowContextDef.cond_ctxt)
}
void ControlFlowContextDef::set_allocated_while_ctxt(::tensorflow::WhileContextDef* while_ctxt) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_ctxt();
  if (while_ctxt) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(while_ctxt);
    if (message_arena != submessage_arena) {
      while_ctxt = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, while_ctxt, submessage_arena);
    }
    set_has_while_ctxt();
    ctxt_.while_ctxt_ = while_ctxt;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ControlFlowContextDef.while_ctxt)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ControlFlowContextDef::kCondCtxtFieldNumber;
const int ControlFlowContextDef::kWhileCtxtFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ControlFlowContextDef::ControlFlowContextDef()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::scc_info_CondContextDef.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.ControlFlowContextDef)
}
ControlFlowContextDef::ControlFlowContextDef(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::scc_info_CondContextDef.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.ControlFlowContextDef)
}
ControlFlowContextDef::ControlFlowContextDef(const ControlFlowContextDef& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  clear_has_ctxt();
  switch (from.ctxt_case()) {
    case kCondCtxt: {
      mutable_cond_ctxt()->::tensorflow::CondContextDef::MergeFrom(from.cond_ctxt());
      break;
    }
    case kWhileCtxt: {
      mutable_while_ctxt()->::tensorflow::WhileContextDef::MergeFrom(from.while_ctxt());
      break;
    }
    case CTXT_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.ControlFlowContextDef)
}

void ControlFlowContextDef::SharedCtor() {
  clear_has_ctxt();
}

ControlFlowContextDef::~ControlFlowContextDef() {
  // @@protoc_insertion_point(destructor:tensorflow.ControlFlowContextDef)
  SharedDtor();
}

void ControlFlowContextDef::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (has_ctxt()) {
    clear_ctxt();
  }
}

void ControlFlowContextDef::ArenaDtor(void* object) {
  ControlFlowContextDef* _this = reinterpret_cast< ControlFlowContextDef* >(object);
  (void)_this;
}
void ControlFlowContextDef::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void ControlFlowContextDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ControlFlowContextDef::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ControlFlowContextDef& ControlFlowContextDef::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::scc_info_CondContextDef.base);
  return *internal_default_instance();
}


void ControlFlowContextDef::clear_ctxt() {
// @@protoc_insertion_point(one_of_clear_start:tensorflow.ControlFlowContextDef)
  switch (ctxt_case()) {
    case kCondCtxt: {
      if (GetArenaNoVirtual() == NULL) {
        delete ctxt_.cond_ctxt_;
      }
      break;
    }
    case kWhileCtxt: {
      if (GetArenaNoVirtual() == NULL) {
        delete ctxt_.while_ctxt_;
      }
      break;
    }
    case CTXT_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = CTXT_NOT_SET;
}


void ControlFlowContextDef::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.ControlFlowContextDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_ctxt();
  _internal_metadata_.Clear();
}

bool ControlFlowContextDef::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.ControlFlowContextDef)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.CondContextDef cond_ctxt = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_cond_ctxt()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.WhileContextDef while_ctxt = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_while_ctxt()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.ControlFlowContextDef)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.ControlFlowContextDef)
  return false;
#undef DO_
}

void ControlFlowContextDef::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.ControlFlowContextDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.CondContextDef cond_ctxt = 1;
  if (has_cond_ctxt()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->_internal_cond_ctxt(), output);
  }

  // .tensorflow.WhileContextDef while_ctxt = 2;
  if (has_while_ctxt()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_while_ctxt(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.ControlFlowContextDef)
}

::google::protobuf::uint8* ControlFlowContextDef::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.ControlFlowContextDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.CondContextDef cond_ctxt = 1;
  if (has_cond_ctxt()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->_internal_cond_ctxt(), deterministic, target);
  }

  // .tensorflow.WhileContextDef while_ctxt = 2;
  if (has_while_ctxt()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_while_ctxt(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.ControlFlowContextDef)
  return target;
}

size_t ControlFlowContextDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.ControlFlowContextDef)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  switch (ctxt_case()) {
    // .tensorflow.CondContextDef cond_ctxt = 1;
    case kCondCtxt: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *ctxt_.cond_ctxt_);
      break;
    }
    // .tensorflow.WhileContextDef while_ctxt = 2;
    case kWhileCtxt: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *ctxt_.while_ctxt_);
      break;
    }
    case CTXT_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ControlFlowContextDef::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.ControlFlowContextDef)
  GOOGLE_DCHECK_NE(&from, this);
  const ControlFlowContextDef* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ControlFlowContextDef>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.ControlFlowContextDef)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.ControlFlowContextDef)
    MergeFrom(*source);
  }
}

void ControlFlowContextDef::MergeFrom(const ControlFlowContextDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.ControlFlowContextDef)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.ctxt_case()) {
    case kCondCtxt: {
      mutable_cond_ctxt()->::tensorflow::CondContextDef::MergeFrom(from.cond_ctxt());
      break;
    }
    case kWhileCtxt: {
      mutable_while_ctxt()->::tensorflow::WhileContextDef::MergeFrom(from.while_ctxt());
      break;
    }
    case CTXT_NOT_SET: {
      break;
    }
  }
}

void ControlFlowContextDef::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.ControlFlowContextDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ControlFlowContextDef::CopyFrom(const ControlFlowContextDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.ControlFlowContextDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ControlFlowContextDef::IsInitialized() const {
  return true;
}

void ControlFlowContextDef::Swap(ControlFlowContextDef* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    ControlFlowContextDef* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void ControlFlowContextDef::UnsafeArenaSwap(ControlFlowContextDef* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void ControlFlowContextDef::InternalSwap(ControlFlowContextDef* other) {
  using std::swap;
  swap(ctxt_, other->ctxt_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ControlFlowContextDef::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void CondContextDef::InitAsDefaultInstance() {
  ::tensorflow::_CondContextDef_default_instance_._instance.get_mutable()->values_def_ = const_cast< ::tensorflow::ValuesDef*>(
      ::tensorflow::ValuesDef::internal_default_instance());
}
void CondContextDef::unsafe_arena_set_allocated_values_def(
    ::tensorflow::ValuesDef* values_def) {
  if (GetArenaNoVirtual() == NULL) {
    delete values_def_;
  }
  values_def_ = values_def;
  if (values_def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CondContextDef.values_def)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CondContextDef::kContextNameFieldNumber;
const int CondContextDef::kPredNameFieldNumber;
const int CondContextDef::kPivotNameFieldNumber;
const int CondContextDef::kBranchFieldNumber;
const int CondContextDef::kValuesDefFieldNumber;
const int CondContextDef::kNestedContextsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CondContextDef::CondContextDef()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::scc_info_CondContextDef.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.CondContextDef)
}
CondContextDef::CondContextDef(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  nested_contexts_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::scc_info_CondContextDef.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.CondContextDef)
}
CondContextDef::CondContextDef(const CondContextDef& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      nested_contexts_(from.nested_contexts_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  context_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.context_name().size() > 0) {
    context_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.context_name(),
      GetArenaNoVirtual());
  }
  pred_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.pred_name().size() > 0) {
    pred_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.pred_name(),
      GetArenaNoVirtual());
  }
  pivot_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.pivot_name().size() > 0) {
    pivot_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.pivot_name(),
      GetArenaNoVirtual());
  }
  if (from.has_values_def()) {
    values_def_ = new ::tensorflow::ValuesDef(*from.values_def_);
  } else {
    values_def_ = NULL;
  }
  branch_ = from.branch_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.CondContextDef)
}

void CondContextDef::SharedCtor() {
  context_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  pred_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  pivot_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&values_def_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&branch_) -
      reinterpret_cast<char*>(&values_def_)) + sizeof(branch_));
}

CondContextDef::~CondContextDef() {
  // @@protoc_insertion_point(destructor:tensorflow.CondContextDef)
  SharedDtor();
}

void CondContextDef::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  context_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  pred_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  pivot_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete values_def_;
}

void CondContextDef::ArenaDtor(void* object) {
  CondContextDef* _this = reinterpret_cast< CondContextDef* >(object);
  (void)_this;
}
void CondContextDef::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void CondContextDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* CondContextDef::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CondContextDef& CondContextDef::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::scc_info_CondContextDef.base);
  return *internal_default_instance();
}


void CondContextDef::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.CondContextDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  nested_contexts_.Clear();
  context_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  pred_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  pivot_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && values_def_ != NULL) {
    delete values_def_;
  }
  values_def_ = NULL;
  branch_ = 0;
  _internal_metadata_.Clear();
}

bool CondContextDef::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.CondContextDef)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string context_name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_context_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->context_name().data(), static_cast<int>(this->context_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.CondContextDef.context_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string pred_name = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_pred_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->pred_name().data(), static_cast<int>(this->pred_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.CondContextDef.pred_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string pivot_name = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_pivot_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->pivot_name().data(), static_cast<int>(this->pivot_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.CondContextDef.pivot_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 branch = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &branch_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.ValuesDef values_def = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_values_def()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.ControlFlowContextDef nested_contexts = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_nested_contexts()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.CondContextDef)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.CondContextDef)
  return false;
#undef DO_
}

void CondContextDef::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.CondContextDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string context_name = 1;
  if (this->context_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->context_name().data(), static_cast<int>(this->context_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CondContextDef.context_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->context_name(), output);
  }

  // string pred_name = 2;
  if (this->pred_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->pred_name().data(), static_cast<int>(this->pred_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CondContextDef.pred_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->pred_name(), output);
  }

  // string pivot_name = 3;
  if (this->pivot_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->pivot_name().data(), static_cast<int>(this->pivot_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CondContextDef.pivot_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->pivot_name(), output);
  }

  // int32 branch = 4;
  if (this->branch() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->branch(), output);
  }

  // .tensorflow.ValuesDef values_def = 5;
  if (this->has_values_def()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, this->_internal_values_def(), output);
  }

  // repeated .tensorflow.ControlFlowContextDef nested_contexts = 6;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->nested_contexts_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6,
      this->nested_contexts(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.CondContextDef)
}

::google::protobuf::uint8* CondContextDef::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.CondContextDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string context_name = 1;
  if (this->context_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->context_name().data(), static_cast<int>(this->context_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CondContextDef.context_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->context_name(), target);
  }

  // string pred_name = 2;
  if (this->pred_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->pred_name().data(), static_cast<int>(this->pred_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CondContextDef.pred_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->pred_name(), target);
  }

  // string pivot_name = 3;
  if (this->pivot_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->pivot_name().data(), static_cast<int>(this->pivot_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CondContextDef.pivot_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->pivot_name(), target);
  }

  // int32 branch = 4;
  if (this->branch() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->branch(), target);
  }

  // .tensorflow.ValuesDef values_def = 5;
  if (this->has_values_def()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        5, this->_internal_values_def(), deterministic, target);
  }

  // repeated .tensorflow.ControlFlowContextDef nested_contexts = 6;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->nested_contexts_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        6, this->nested_contexts(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.CondContextDef)
  return target;
}

size_t CondContextDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.CondContextDef)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.ControlFlowContextDef nested_contexts = 6;
  {
    unsigned int count = static_cast<unsigned int>(this->nested_contexts_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->nested_contexts(static_cast<int>(i)));
    }
  }

  // string context_name = 1;
  if (this->context_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->context_name());
  }

  // string pred_name = 2;
  if (this->pred_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->pred_name());
  }

  // string pivot_name = 3;
  if (this->pivot_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->pivot_name());
  }

  // .tensorflow.ValuesDef values_def = 5;
  if (this->has_values_def()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *values_def_);
  }

  // int32 branch = 4;
  if (this->branch() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->branch());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CondContextDef::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.CondContextDef)
  GOOGLE_DCHECK_NE(&from, this);
  const CondContextDef* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CondContextDef>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.CondContextDef)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.CondContextDef)
    MergeFrom(*source);
  }
}

void CondContextDef::MergeFrom(const CondContextDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.CondContextDef)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  nested_contexts_.MergeFrom(from.nested_contexts_);
  if (from.context_name().size() > 0) {
    set_context_name(from.context_name());
  }
  if (from.pred_name().size() > 0) {
    set_pred_name(from.pred_name());
  }
  if (from.pivot_name().size() > 0) {
    set_pivot_name(from.pivot_name());
  }
  if (from.has_values_def()) {
    mutable_values_def()->::tensorflow::ValuesDef::MergeFrom(from.values_def());
  }
  if (from.branch() != 0) {
    set_branch(from.branch());
  }
}

void CondContextDef::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.CondContextDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CondContextDef::CopyFrom(const CondContextDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.CondContextDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CondContextDef::IsInitialized() const {
  return true;
}

void CondContextDef::Swap(CondContextDef* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    CondContextDef* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void CondContextDef::UnsafeArenaSwap(CondContextDef* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void CondContextDef::InternalSwap(CondContextDef* other) {
  using std::swap;
  CastToBase(&nested_contexts_)->InternalSwap(CastToBase(&other->nested_contexts_));
  context_name_.Swap(&other->context_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  pred_name_.Swap(&other->pred_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  pivot_name_.Swap(&other->pivot_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(values_def_, other->values_def_);
  swap(branch_, other->branch_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata CondContextDef::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void WhileContextDef::InitAsDefaultInstance() {
  ::tensorflow::_WhileContextDef_default_instance_._instance.get_mutable()->values_def_ = const_cast< ::tensorflow::ValuesDef*>(
      ::tensorflow::ValuesDef::internal_default_instance());
}
void WhileContextDef::unsafe_arena_set_allocated_values_def(
    ::tensorflow::ValuesDef* values_def) {
  if (GetArenaNoVirtual() == NULL) {
    delete values_def_;
  }
  values_def_ = values_def;
  if (values_def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.WhileContextDef.values_def)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int WhileContextDef::kContextNameFieldNumber;
const int WhileContextDef::kParallelIterationsFieldNumber;
const int WhileContextDef::kBackPropFieldNumber;
const int WhileContextDef::kSwapMemoryFieldNumber;
const int WhileContextDef::kPivotNameFieldNumber;
const int WhileContextDef::kPivotForPredNameFieldNumber;
const int WhileContextDef::kPivotForBodyNameFieldNumber;
const int WhileContextDef::kLoopExitNamesFieldNumber;
const int WhileContextDef::kLoopEnterNamesFieldNumber;
const int WhileContextDef::kValuesDefFieldNumber;
const int WhileContextDef::kMaximumIterationsNameFieldNumber;
const int WhileContextDef::kNestedContextsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

WhileContextDef::WhileContextDef()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::scc_info_CondContextDef.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.WhileContextDef)
}
WhileContextDef::WhileContextDef(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  loop_exit_names_(arena),
  loop_enter_names_(arena),
  nested_contexts_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::scc_info_CondContextDef.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.WhileContextDef)
}
WhileContextDef::WhileContextDef(const WhileContextDef& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      loop_exit_names_(from.loop_exit_names_),
      loop_enter_names_(from.loop_enter_names_),
      nested_contexts_(from.nested_contexts_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  context_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.context_name().size() > 0) {
    context_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.context_name(),
      GetArenaNoVirtual());
  }
  pivot_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.pivot_name().size() > 0) {
    pivot_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.pivot_name(),
      GetArenaNoVirtual());
  }
  pivot_for_pred_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.pivot_for_pred_name().size() > 0) {
    pivot_for_pred_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.pivot_for_pred_name(),
      GetArenaNoVirtual());
  }
  pivot_for_body_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.pivot_for_body_name().size() > 0) {
    pivot_for_body_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.pivot_for_body_name(),
      GetArenaNoVirtual());
  }
  maximum_iterations_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.maximum_iterations_name().size() > 0) {
    maximum_iterations_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.maximum_iterations_name(),
      GetArenaNoVirtual());
  }
  if (from.has_values_def()) {
    values_def_ = new ::tensorflow::ValuesDef(*from.values_def_);
  } else {
    values_def_ = NULL;
  }
  ::memcpy(&parallel_iterations_, &from.parallel_iterations_,
    static_cast<size_t>(reinterpret_cast<char*>(&swap_memory_) -
    reinterpret_cast<char*>(&parallel_iterations_)) + sizeof(swap_memory_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.WhileContextDef)
}

void WhileContextDef::SharedCtor() {
  context_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  pivot_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  pivot_for_pred_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  pivot_for_body_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  maximum_iterations_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&values_def_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&swap_memory_) -
      reinterpret_cast<char*>(&values_def_)) + sizeof(swap_memory_));
}

WhileContextDef::~WhileContextDef() {
  // @@protoc_insertion_point(destructor:tensorflow.WhileContextDef)
  SharedDtor();
}

void WhileContextDef::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  context_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  pivot_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  pivot_for_pred_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  pivot_for_body_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  maximum_iterations_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete values_def_;
}

void WhileContextDef::ArenaDtor(void* object) {
  WhileContextDef* _this = reinterpret_cast< WhileContextDef* >(object);
  (void)_this;
}
void WhileContextDef::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void WhileContextDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* WhileContextDef::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const WhileContextDef& WhileContextDef::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::scc_info_CondContextDef.base);
  return *internal_default_instance();
}


void WhileContextDef::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.WhileContextDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  loop_exit_names_.Clear();
  loop_enter_names_.Clear();
  nested_contexts_.Clear();
  context_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  pivot_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  pivot_for_pred_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  pivot_for_body_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  maximum_iterations_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && values_def_ != NULL) {
    delete values_def_;
  }
  values_def_ = NULL;
  ::memset(&parallel_iterations_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&swap_memory_) -
      reinterpret_cast<char*>(&parallel_iterations_)) + sizeof(swap_memory_));
  _internal_metadata_.Clear();
}

bool WhileContextDef::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.WhileContextDef)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string context_name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_context_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->context_name().data(), static_cast<int>(this->context_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.WhileContextDef.context_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 parallel_iterations = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &parallel_iterations_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool back_prop = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &back_prop_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool swap_memory = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &swap_memory_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string pivot_name = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_pivot_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->pivot_name().data(), static_cast<int>(this->pivot_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.WhileContextDef.pivot_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string pivot_for_pred_name = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_pivot_for_pred_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->pivot_for_pred_name().data(), static_cast<int>(this->pivot_for_pred_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.WhileContextDef.pivot_for_pred_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string pivot_for_body_name = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_pivot_for_body_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->pivot_for_body_name().data(), static_cast<int>(this->pivot_for_body_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.WhileContextDef.pivot_for_body_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string loop_exit_names = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(66u /* 66 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_loop_exit_names()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->loop_exit_names(this->loop_exit_names_size() - 1).data(),
            static_cast<int>(this->loop_exit_names(this->loop_exit_names_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.WhileContextDef.loop_exit_names"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.ValuesDef values_def = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(74u /* 74 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_values_def()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string loop_enter_names = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(82u /* 82 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_loop_enter_names()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->loop_enter_names(this->loop_enter_names_size() - 1).data(),
            static_cast<int>(this->loop_enter_names(this->loop_enter_names_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.WhileContextDef.loop_enter_names"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string maximum_iterations_name = 11;
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(90u /* 90 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_maximum_iterations_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->maximum_iterations_name().data(), static_cast<int>(this->maximum_iterations_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.WhileContextDef.maximum_iterations_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.ControlFlowContextDef nested_contexts = 12;
      case 12: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(98u /* 98 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_nested_contexts()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.WhileContextDef)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.WhileContextDef)
  return false;
#undef DO_
}

void WhileContextDef::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.WhileContextDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string context_name = 1;
  if (this->context_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->context_name().data(), static_cast<int>(this->context_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.WhileContextDef.context_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->context_name(), output);
  }

  // int32 parallel_iterations = 2;
  if (this->parallel_iterations() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->parallel_iterations(), output);
  }

  // bool back_prop = 3;
  if (this->back_prop() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(3, this->back_prop(), output);
  }

  // bool swap_memory = 4;
  if (this->swap_memory() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(4, this->swap_memory(), output);
  }

  // string pivot_name = 5;
  if (this->pivot_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->pivot_name().data(), static_cast<int>(this->pivot_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.WhileContextDef.pivot_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->pivot_name(), output);
  }

  // string pivot_for_pred_name = 6;
  if (this->pivot_for_pred_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->pivot_for_pred_name().data(), static_cast<int>(this->pivot_for_pred_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.WhileContextDef.pivot_for_pred_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      6, this->pivot_for_pred_name(), output);
  }

  // string pivot_for_body_name = 7;
  if (this->pivot_for_body_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->pivot_for_body_name().data(), static_cast<int>(this->pivot_for_body_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.WhileContextDef.pivot_for_body_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->pivot_for_body_name(), output);
  }

  // repeated string loop_exit_names = 8;
  for (int i = 0, n = this->loop_exit_names_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->loop_exit_names(i).data(), static_cast<int>(this->loop_exit_names(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.WhileContextDef.loop_exit_names");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      8, this->loop_exit_names(i), output);
  }

  // .tensorflow.ValuesDef values_def = 9;
  if (this->has_values_def()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, this->_internal_values_def(), output);
  }

  // repeated string loop_enter_names = 10;
  for (int i = 0, n = this->loop_enter_names_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->loop_enter_names(i).data(), static_cast<int>(this->loop_enter_names(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.WhileContextDef.loop_enter_names");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      10, this->loop_enter_names(i), output);
  }

  // string maximum_iterations_name = 11;
  if (this->maximum_iterations_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->maximum_iterations_name().data(), static_cast<int>(this->maximum_iterations_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.WhileContextDef.maximum_iterations_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      11, this->maximum_iterations_name(), output);
  }

  // repeated .tensorflow.ControlFlowContextDef nested_contexts = 12;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->nested_contexts_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      12,
      this->nested_contexts(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.WhileContextDef)
}

::google::protobuf::uint8* WhileContextDef::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.WhileContextDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string context_name = 1;
  if (this->context_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->context_name().data(), static_cast<int>(this->context_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.WhileContextDef.context_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->context_name(), target);
  }

  // int32 parallel_iterations = 2;
  if (this->parallel_iterations() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->parallel_iterations(), target);
  }

  // bool back_prop = 3;
  if (this->back_prop() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(3, this->back_prop(), target);
  }

  // bool swap_memory = 4;
  if (this->swap_memory() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(4, this->swap_memory(), target);
  }

  // string pivot_name = 5;
  if (this->pivot_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->pivot_name().data(), static_cast<int>(this->pivot_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.WhileContextDef.pivot_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->pivot_name(), target);
  }

  // string pivot_for_pred_name = 6;
  if (this->pivot_for_pred_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->pivot_for_pred_name().data(), static_cast<int>(this->pivot_for_pred_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.WhileContextDef.pivot_for_pred_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        6, this->pivot_for_pred_name(), target);
  }

  // string pivot_for_body_name = 7;
  if (this->pivot_for_body_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->pivot_for_body_name().data(), static_cast<int>(this->pivot_for_body_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.WhileContextDef.pivot_for_body_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->pivot_for_body_name(), target);
  }

  // repeated string loop_exit_names = 8;
  for (int i = 0, n = this->loop_exit_names_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->loop_exit_names(i).data(), static_cast<int>(this->loop_exit_names(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.WhileContextDef.loop_exit_names");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(8, this->loop_exit_names(i), target);
  }

  // .tensorflow.ValuesDef values_def = 9;
  if (this->has_values_def()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        9, this->_internal_values_def(), deterministic, target);
  }

  // repeated string loop_enter_names = 10;
  for (int i = 0, n = this->loop_enter_names_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->loop_enter_names(i).data(), static_cast<int>(this->loop_enter_names(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.WhileContextDef.loop_enter_names");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(10, this->loop_enter_names(i), target);
  }

  // string maximum_iterations_name = 11;
  if (this->maximum_iterations_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->maximum_iterations_name().data(), static_cast<int>(this->maximum_iterations_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.WhileContextDef.maximum_iterations_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        11, this->maximum_iterations_name(), target);
  }

  // repeated .tensorflow.ControlFlowContextDef nested_contexts = 12;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->nested_contexts_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        12, this->nested_contexts(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.WhileContextDef)
  return target;
}

size_t WhileContextDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.WhileContextDef)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated string loop_exit_names = 8;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->loop_exit_names_size());
  for (int i = 0, n = this->loop_exit_names_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->loop_exit_names(i));
  }

  // repeated string loop_enter_names = 10;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->loop_enter_names_size());
  for (int i = 0, n = this->loop_enter_names_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->loop_enter_names(i));
  }

  // repeated .tensorflow.ControlFlowContextDef nested_contexts = 12;
  {
    unsigned int count = static_cast<unsigned int>(this->nested_contexts_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->nested_contexts(static_cast<int>(i)));
    }
  }

  // string context_name = 1;
  if (this->context_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->context_name());
  }

  // string pivot_name = 5;
  if (this->pivot_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->pivot_name());
  }

  // string pivot_for_pred_name = 6;
  if (this->pivot_for_pred_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->pivot_for_pred_name());
  }

  // string pivot_for_body_name = 7;
  if (this->pivot_for_body_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->pivot_for_body_name());
  }

  // string maximum_iterations_name = 11;
  if (this->maximum_iterations_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->maximum_iterations_name());
  }

  // .tensorflow.ValuesDef values_def = 9;
  if (this->has_values_def()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *values_def_);
  }

  // int32 parallel_iterations = 2;
  if (this->parallel_iterations() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->parallel_iterations());
  }

  // bool back_prop = 3;
  if (this->back_prop() != 0) {
    total_size += 1 + 1;
  }

  // bool swap_memory = 4;
  if (this->swap_memory() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void WhileContextDef::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.WhileContextDef)
  GOOGLE_DCHECK_NE(&from, this);
  const WhileContextDef* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const WhileContextDef>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.WhileContextDef)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.WhileContextDef)
    MergeFrom(*source);
  }
}

void WhileContextDef::MergeFrom(const WhileContextDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.WhileContextDef)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  loop_exit_names_.MergeFrom(from.loop_exit_names_);
  loop_enter_names_.MergeFrom(from.loop_enter_names_);
  nested_contexts_.MergeFrom(from.nested_contexts_);
  if (from.context_name().size() > 0) {
    set_context_name(from.context_name());
  }
  if (from.pivot_name().size() > 0) {
    set_pivot_name(from.pivot_name());
  }
  if (from.pivot_for_pred_name().size() > 0) {
    set_pivot_for_pred_name(from.pivot_for_pred_name());
  }
  if (from.pivot_for_body_name().size() > 0) {
    set_pivot_for_body_name(from.pivot_for_body_name());
  }
  if (from.maximum_iterations_name().size() > 0) {
    set_maximum_iterations_name(from.maximum_iterations_name());
  }
  if (from.has_values_def()) {
    mutable_values_def()->::tensorflow::ValuesDef::MergeFrom(from.values_def());
  }
  if (from.parallel_iterations() != 0) {
    set_parallel_iterations(from.parallel_iterations());
  }
  if (from.back_prop() != 0) {
    set_back_prop(from.back_prop());
  }
  if (from.swap_memory() != 0) {
    set_swap_memory(from.swap_memory());
  }
}

void WhileContextDef::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.WhileContextDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void WhileContextDef::CopyFrom(const WhileContextDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.WhileContextDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool WhileContextDef::IsInitialized() const {
  return true;
}

void WhileContextDef::Swap(WhileContextDef* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    WhileContextDef* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void WhileContextDef::UnsafeArenaSwap(WhileContextDef* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void WhileContextDef::InternalSwap(WhileContextDef* other) {
  using std::swap;
  loop_exit_names_.InternalSwap(CastToBase(&other->loop_exit_names_));
  loop_enter_names_.InternalSwap(CastToBase(&other->loop_enter_names_));
  CastToBase(&nested_contexts_)->InternalSwap(CastToBase(&other->nested_contexts_));
  context_name_.Swap(&other->context_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  pivot_name_.Swap(&other->pivot_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  pivot_for_pred_name_.Swap(&other->pivot_for_pred_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  pivot_for_body_name_.Swap(&other->pivot_for_body_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  maximum_iterations_name_.Swap(&other->maximum_iterations_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(values_def_, other->values_def_);
  swap(parallel_iterations_, other->parallel_iterations_);
  swap(back_prop_, other->back_prop_);
  swap(swap_memory_, other->swap_memory_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata WhileContextDef::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::ValuesDef_ExternalValuesEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::ValuesDef_ExternalValuesEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::ValuesDef_ExternalValuesEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::ValuesDef* Arena::CreateMaybeMessage< ::tensorflow::ValuesDef >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::ValuesDef >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::ControlFlowContextDef* Arena::CreateMaybeMessage< ::tensorflow::ControlFlowContextDef >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::ControlFlowContextDef >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::CondContextDef* Arena::CreateMaybeMessage< ::tensorflow::CondContextDef >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::CondContextDef >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::WhileContextDef* Arena::CreateMaybeMessage< ::tensorflow::WhileContextDef >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::WhileContextDef >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

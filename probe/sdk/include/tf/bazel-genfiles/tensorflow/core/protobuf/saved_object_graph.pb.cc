// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/saved_object_graph.proto

#include "tensorflow/core/protobuf/saved_object_graph.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_TensorShapeProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2fversions_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fversions_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_VersionDef;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fversions_2eproto
namespace protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_SavedAsset;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_SavedBareConcreteFunction;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_SavedConstant;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_SavedResource;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_FunctionSpec;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_SavedConcreteFunction;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_SavedFunction;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_SavedUserObject;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_SavedVariable;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<9> scc_info_SavedObject;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto
namespace protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto ::google::protobuf::internal::SCCInfo<3> scc_info_DictValue;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto
namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_TrackableObjectGraph_TrackableObject_ObjectReference;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_TrackableObjectGraph_TrackableObject_SlotVariableReference;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto
namespace tensorflow {
class SavedObjectGraph_ConcreteFunctionsEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse>
      _instance;
} _SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse_default_instance_;
class SavedObjectGraphDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SavedObjectGraph>
      _instance;
} _SavedObjectGraph_default_instance_;
class SavedObjectDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SavedObject>
      _instance;
  const ::tensorflow::SavedUserObject* user_object_;
  const ::tensorflow::SavedAsset* asset_;
  const ::tensorflow::SavedFunction* function_;
  const ::tensorflow::SavedVariable* variable_;
  const ::tensorflow::SavedBareConcreteFunction* bare_concrete_function_;
  const ::tensorflow::SavedConstant* constant_;
  const ::tensorflow::SavedResource* resource_;
} _SavedObject_default_instance_;
class SavedUserObjectDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SavedUserObject>
      _instance;
} _SavedUserObject_default_instance_;
class SavedAssetDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SavedAsset>
      _instance;
} _SavedAsset_default_instance_;
class SavedFunctionDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SavedFunction>
      _instance;
} _SavedFunction_default_instance_;
class SavedConcreteFunctionDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SavedConcreteFunction>
      _instance;
} _SavedConcreteFunction_default_instance_;
class SavedBareConcreteFunctionDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SavedBareConcreteFunction>
      _instance;
} _SavedBareConcreteFunction_default_instance_;
class SavedConstantDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SavedConstant>
      _instance;
} _SavedConstant_default_instance_;
class SavedVariableDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SavedVariable>
      _instance;
} _SavedVariable_default_instance_;
class FunctionSpecDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<FunctionSpec>
      _instance;
} _FunctionSpec_default_instance_;
class SavedResourceDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SavedResource>
      _instance;
} _SavedResource_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto {
static void InitDefaultsSavedObjectGraph_ConcreteFunctionsEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse();
  }
  ::tensorflow::SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsSavedObjectGraph_ConcreteFunctionsEntry_DoNotUse}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedConcreteFunction.base,}};

static void InitDefaultsSavedObjectGraph() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_SavedObjectGraph_default_instance_;
    new (ptr) ::tensorflow::SavedObjectGraph();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::SavedObjectGraph::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_SavedObjectGraph =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsSavedObjectGraph}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedObject.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse.base,}};

static void InitDefaultsSavedObject() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_SavedObject_default_instance_;
    new (ptr) ::tensorflow::SavedObject();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::SavedObject::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<9> scc_info_SavedObject =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 9, InitDefaultsSavedObject}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::scc_info_TrackableObjectGraph_TrackableObject_ObjectReference.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::scc_info_TrackableObjectGraph_TrackableObject_SlotVariableReference.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedUserObject.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedAsset.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedFunction.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedVariable.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedBareConcreteFunction.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedConstant.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedResource.base,}};

static void InitDefaultsSavedUserObject() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_SavedUserObject_default_instance_;
    new (ptr) ::tensorflow::SavedUserObject();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::SavedUserObject::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_SavedUserObject =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsSavedUserObject}, {
      &protobuf_tensorflow_2fcore_2fframework_2fversions_2eproto::scc_info_VersionDef.base,}};

static void InitDefaultsSavedAsset() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_SavedAsset_default_instance_;
    new (ptr) ::tensorflow::SavedAsset();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::SavedAsset::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_SavedAsset =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsSavedAsset}, {}};

static void InitDefaultsSavedFunction() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_SavedFunction_default_instance_;
    new (ptr) ::tensorflow::SavedFunction();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::SavedFunction::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_SavedFunction =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsSavedFunction}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_FunctionSpec.base,}};

static void InitDefaultsSavedConcreteFunction() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_SavedConcreteFunction_default_instance_;
    new (ptr) ::tensorflow::SavedConcreteFunction();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::SavedConcreteFunction::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_SavedConcreteFunction =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsSavedConcreteFunction}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::scc_info_DictValue.base,}};

static void InitDefaultsSavedBareConcreteFunction() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_SavedBareConcreteFunction_default_instance_;
    new (ptr) ::tensorflow::SavedBareConcreteFunction();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::SavedBareConcreteFunction::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_SavedBareConcreteFunction =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsSavedBareConcreteFunction}, {}};

static void InitDefaultsSavedConstant() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_SavedConstant_default_instance_;
    new (ptr) ::tensorflow::SavedConstant();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::SavedConstant::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_SavedConstant =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsSavedConstant}, {}};

static void InitDefaultsSavedVariable() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_SavedVariable_default_instance_;
    new (ptr) ::tensorflow::SavedVariable();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::SavedVariable::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_SavedVariable =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsSavedVariable}, {
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::scc_info_TensorShapeProto.base,}};

static void InitDefaultsFunctionSpec() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_FunctionSpec_default_instance_;
    new (ptr) ::tensorflow::FunctionSpec();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::FunctionSpec::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_FunctionSpec =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsFunctionSpec}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::scc_info_DictValue.base,}};

static void InitDefaultsSavedResource() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_SavedResource_default_instance_;
    new (ptr) ::tensorflow::SavedResource();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::SavedResource::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_SavedResource =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsSavedResource}, {}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_SavedObjectGraph.base);
  ::google::protobuf::internal::InitSCC(&scc_info_SavedObject.base);
  ::google::protobuf::internal::InitSCC(&scc_info_SavedUserObject.base);
  ::google::protobuf::internal::InitSCC(&scc_info_SavedAsset.base);
  ::google::protobuf::internal::InitSCC(&scc_info_SavedFunction.base);
  ::google::protobuf::internal::InitSCC(&scc_info_SavedConcreteFunction.base);
  ::google::protobuf::internal::InitSCC(&scc_info_SavedBareConcreteFunction.base);
  ::google::protobuf::internal::InitSCC(&scc_info_SavedConstant.base);
  ::google::protobuf::internal::InitSCC(&scc_info_SavedVariable.base);
  ::google::protobuf::internal::InitSCC(&scc_info_FunctionSpec.base);
  ::google::protobuf::internal::InitSCC(&scc_info_SavedResource.base);
}

::google::protobuf::Metadata file_level_metadata[12];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedObjectGraph, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedObjectGraph, nodes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedObjectGraph, concrete_functions_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedObject, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedObject, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedObject, children_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedObject, slot_variables_),
  offsetof(::tensorflow::SavedObjectDefaultTypeInternal, user_object_),
  offsetof(::tensorflow::SavedObjectDefaultTypeInternal, asset_),
  offsetof(::tensorflow::SavedObjectDefaultTypeInternal, function_),
  offsetof(::tensorflow::SavedObjectDefaultTypeInternal, variable_),
  offsetof(::tensorflow::SavedObjectDefaultTypeInternal, bare_concrete_function_),
  offsetof(::tensorflow::SavedObjectDefaultTypeInternal, constant_),
  offsetof(::tensorflow::SavedObjectDefaultTypeInternal, resource_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedObject, kind_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedUserObject, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedUserObject, identifier_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedUserObject, version_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedAsset, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedAsset, asset_file_def_index_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedFunction, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedFunction, concrete_functions_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedFunction, function_spec_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedConcreteFunction, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedConcreteFunction, bound_inputs_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedConcreteFunction, canonicalized_input_signature_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedConcreteFunction, output_signature_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedBareConcreteFunction, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedBareConcreteFunction, concrete_function_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedBareConcreteFunction, argument_keywords_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedBareConcreteFunction, allowed_positional_arguments_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedConstant, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedConstant, operation_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedVariable, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedVariable, dtype_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedVariable, shape_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedVariable, trainable_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FunctionSpec, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FunctionSpec, fullargspec_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FunctionSpec, is_method_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FunctionSpec, args_to_prepend_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FunctionSpec, kwargs_to_include_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::FunctionSpec, input_signature_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SavedResource, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, 7, sizeof(::tensorflow::SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse)},
  { 9, -1, sizeof(::tensorflow::SavedObjectGraph)},
  { 16, -1, sizeof(::tensorflow::SavedObject)},
  { 31, -1, sizeof(::tensorflow::SavedUserObject)},
  { 38, -1, sizeof(::tensorflow::SavedAsset)},
  { 44, -1, sizeof(::tensorflow::SavedFunction)},
  { 51, -1, sizeof(::tensorflow::SavedConcreteFunction)},
  { 59, -1, sizeof(::tensorflow::SavedBareConcreteFunction)},
  { 67, -1, sizeof(::tensorflow::SavedConstant)},
  { 73, -1, sizeof(::tensorflow::SavedVariable)},
  { 81, -1, sizeof(::tensorflow::FunctionSpec)},
  { 91, -1, sizeof(::tensorflow::SavedResource)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_SavedObjectGraph_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_SavedObject_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_SavedUserObject_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_SavedAsset_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_SavedFunction_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_SavedConcreteFunction_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_SavedBareConcreteFunction_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_SavedConstant_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_SavedVariable_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_FunctionSpec_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_SavedResource_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/protobuf/saved_object_graph.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 12);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n1tensorflow/core/protobuf/saved_object_"
      "graph.proto\022\ntensorflow\0325tensorflow/core"
      "/protobuf/trackable_object_graph.proto\032%"
      "tensorflow/core/protobuf/struct.proto\032,t"
      "ensorflow/core/framework/tensor_shape.pr"
      "oto\032%tensorflow/core/framework/types.pro"
      "to\032(tensorflow/core/framework/versions.p"
      "roto\"\350\001\n\020SavedObjectGraph\022&\n\005nodes\030\001 \003(\013"
      "2\027.tensorflow.SavedObject\022O\n\022concrete_fu"
      "nctions\030\002 \003(\01323.tensorflow.SavedObjectGr"
      "aph.ConcreteFunctionsEntry\032[\n\026ConcreteFu"
      "nctionsEntry\022\013\n\003key\030\001 \001(\t\0220\n\005value\030\002 \001(\013"
      "2!.tensorflow.SavedConcreteFunction:\0028\001\""
      "\275\004\n\013SavedObject\022R\n\010children\030\001 \003(\0132@.tens"
      "orflow.TrackableObjectGraph.TrackableObj"
      "ect.ObjectReference\022^\n\016slot_variables\030\003 "
      "\003(\0132F.tensorflow.TrackableObjectGraph.Tr"
      "ackableObject.SlotVariableReference\0222\n\013u"
      "ser_object\030\004 \001(\0132\033.tensorflow.SavedUserO"
      "bjectH\000\022\'\n\005asset\030\005 \001(\0132\026.tensorflow.Save"
      "dAssetH\000\022-\n\010function\030\006 \001(\0132\031.tensorflow."
      "SavedFunctionH\000\022-\n\010variable\030\007 \001(\0132\031.tens"
      "orflow.SavedVariableH\000\022G\n\026bare_concrete_"
      "function\030\010 \001(\0132%.tensorflow.SavedBareCon"
      "creteFunctionH\000\022-\n\010constant\030\t \001(\0132\031.tens"
      "orflow.SavedConstantH\000\022-\n\010resource\030\n \001(\013"
      "2\031.tensorflow.SavedResourceH\000B\006\n\004kindJ\004\010"
      "\002\020\003R\nattributes\"N\n\017SavedUserObject\022\022\n\nid"
      "entifier\030\001 \001(\t\022\'\n\007version\030\002 \001(\0132\026.tensor"
      "flow.VersionDef\"*\n\nSavedAsset\022\034\n\024asset_f"
      "ile_def_index\030\001 \001(\005\"\\\n\rSavedFunction\022\032\n\022"
      "concrete_functions\030\001 \003(\t\022/\n\rfunction_spe"
      "c\030\002 \001(\0132\030.tensorflow.FunctionSpec\"\250\001\n\025Sa"
      "vedConcreteFunction\022\024\n\014bound_inputs\030\002 \003("
      "\005\022B\n\035canonicalized_input_signature\030\003 \001(\013"
      "2\033.tensorflow.StructuredValue\0225\n\020output_"
      "signature\030\004 \001(\0132\033.tensorflow.StructuredV"
      "alue\"|\n\031SavedBareConcreteFunction\022\036\n\026con"
      "crete_function_name\030\001 \001(\t\022\031\n\021argument_ke"
      "ywords\030\002 \003(\t\022$\n\034allowed_positional_argum"
      "ents\030\003 \001(\003\"\"\n\rSavedConstant\022\021\n\toperation"
      "\030\001 \001(\t\"t\n\rSavedVariable\022#\n\005dtype\030\001 \001(\0162\024"
      ".tensorflow.DataType\022+\n\005shape\030\002 \001(\0132\034.te"
      "nsorflow.TensorShapeProto\022\021\n\ttrainable\030\003"
      " \001(\010\"\367\001\n\014FunctionSpec\0220\n\013fullargspec\030\001 \001"
      "(\0132\033.tensorflow.StructuredValue\022\021\n\tis_me"
      "thod\030\002 \001(\010\0224\n\017args_to_prepend\030\003 \001(\0132\033.te"
      "nsorflow.StructuredValue\0226\n\021kwargs_to_in"
      "clude\030\004 \001(\0132\033.tensorflow.StructuredValue"
      "\0224\n\017input_signature\030\005 \001(\0132\033.tensorflow.S"
      "tructuredValue\"\017\n\rSavedResourceB\003\370\001\001b\006pr"
      "oto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 2044);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/protobuf/saved_object_graph.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftypes_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2fversions_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto
namespace tensorflow {

// ===================================================================

SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse::SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse() {}
SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse::SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse::MergeFrom(const SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::file_level_metadata[0];
}
void SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void SavedObjectGraph::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SavedObjectGraph::kNodesFieldNumber;
const int SavedObjectGraph::kConcreteFunctionsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SavedObjectGraph::SavedObjectGraph()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedObjectGraph.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.SavedObjectGraph)
}
SavedObjectGraph::SavedObjectGraph(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  nodes_(arena),
  concrete_functions_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedObjectGraph.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.SavedObjectGraph)
}
SavedObjectGraph::SavedObjectGraph(const SavedObjectGraph& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      nodes_(from.nodes_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  concrete_functions_.MergeFrom(from.concrete_functions_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.SavedObjectGraph)
}

void SavedObjectGraph::SharedCtor() {
}

SavedObjectGraph::~SavedObjectGraph() {
  // @@protoc_insertion_point(destructor:tensorflow.SavedObjectGraph)
  SharedDtor();
}

void SavedObjectGraph::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void SavedObjectGraph::ArenaDtor(void* object) {
  SavedObjectGraph* _this = reinterpret_cast< SavedObjectGraph* >(object);
  (void)_this;
}
void SavedObjectGraph::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void SavedObjectGraph::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* SavedObjectGraph::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const SavedObjectGraph& SavedObjectGraph::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedObjectGraph.base);
  return *internal_default_instance();
}


void SavedObjectGraph::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.SavedObjectGraph)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  nodes_.Clear();
  concrete_functions_.Clear();
  _internal_metadata_.Clear();
}

bool SavedObjectGraph::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.SavedObjectGraph)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.SavedObject nodes = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_nodes()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, .tensorflow.SavedConcreteFunction> concrete_functions = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse,
              ::std::string, ::tensorflow::SavedConcreteFunction,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::tensorflow::SavedConcreteFunction > > parser(&concrete_functions_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.SavedObjectGraph.ConcreteFunctionsEntry.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.SavedObjectGraph)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.SavedObjectGraph)
  return false;
#undef DO_
}

void SavedObjectGraph::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.SavedObjectGraph)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.SavedObject nodes = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->nodes_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->nodes(static_cast<int>(i)),
      output);
  }

  // map<string, .tensorflow.SavedConcreteFunction> concrete_functions = 2;
  if (!this->concrete_functions().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::SavedConcreteFunction >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.SavedObjectGraph.ConcreteFunctionsEntry.key");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->concrete_functions().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->concrete_functions().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::SavedConcreteFunction >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::SavedConcreteFunction >::const_iterator
          it = this->concrete_functions().begin();
          it != this->concrete_functions().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(concrete_functions_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::SavedConcreteFunction >::const_iterator
          it = this->concrete_functions().begin();
          it != this->concrete_functions().end(); ++it) {
        entry.reset(concrete_functions_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.SavedObjectGraph)
}

::google::protobuf::uint8* SavedObjectGraph::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.SavedObjectGraph)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.SavedObject nodes = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->nodes_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->nodes(static_cast<int>(i)), deterministic, target);
  }

  // map<string, .tensorflow.SavedConcreteFunction> concrete_functions = 2;
  if (!this->concrete_functions().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::SavedConcreteFunction >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.SavedObjectGraph.ConcreteFunctionsEntry.key");
      }
    };

    if (deterministic &&
        this->concrete_functions().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->concrete_functions().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::SavedConcreteFunction >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::SavedConcreteFunction >::const_iterator
          it = this->concrete_functions().begin();
          it != this->concrete_functions().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(concrete_functions_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::SavedConcreteFunction >::const_iterator
          it = this->concrete_functions().begin();
          it != this->concrete_functions().end(); ++it) {
        entry.reset(concrete_functions_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.SavedObjectGraph)
  return target;
}

size_t SavedObjectGraph::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.SavedObjectGraph)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.SavedObject nodes = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->nodes_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->nodes(static_cast<int>(i)));
    }
  }

  // map<string, .tensorflow.SavedConcreteFunction> concrete_functions = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->concrete_functions_size());
  {
    ::std::unique_ptr<SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::tensorflow::SavedConcreteFunction >::const_iterator
        it = this->concrete_functions().begin();
        it != this->concrete_functions().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(concrete_functions_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SavedObjectGraph::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.SavedObjectGraph)
  GOOGLE_DCHECK_NE(&from, this);
  const SavedObjectGraph* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SavedObjectGraph>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.SavedObjectGraph)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.SavedObjectGraph)
    MergeFrom(*source);
  }
}

void SavedObjectGraph::MergeFrom(const SavedObjectGraph& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.SavedObjectGraph)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  nodes_.MergeFrom(from.nodes_);
  concrete_functions_.MergeFrom(from.concrete_functions_);
}

void SavedObjectGraph::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.SavedObjectGraph)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SavedObjectGraph::CopyFrom(const SavedObjectGraph& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.SavedObjectGraph)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SavedObjectGraph::IsInitialized() const {
  return true;
}

void SavedObjectGraph::Swap(SavedObjectGraph* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    SavedObjectGraph* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void SavedObjectGraph::UnsafeArenaSwap(SavedObjectGraph* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void SavedObjectGraph::InternalSwap(SavedObjectGraph* other) {
  using std::swap;
  CastToBase(&nodes_)->InternalSwap(CastToBase(&other->nodes_));
  concrete_functions_.Swap(&other->concrete_functions_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata SavedObjectGraph::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void SavedObject::InitAsDefaultInstance() {
  ::tensorflow::_SavedObject_default_instance_.user_object_ = const_cast< ::tensorflow::SavedUserObject*>(
      ::tensorflow::SavedUserObject::internal_default_instance());
  ::tensorflow::_SavedObject_default_instance_.asset_ = const_cast< ::tensorflow::SavedAsset*>(
      ::tensorflow::SavedAsset::internal_default_instance());
  ::tensorflow::_SavedObject_default_instance_.function_ = const_cast< ::tensorflow::SavedFunction*>(
      ::tensorflow::SavedFunction::internal_default_instance());
  ::tensorflow::_SavedObject_default_instance_.variable_ = const_cast< ::tensorflow::SavedVariable*>(
      ::tensorflow::SavedVariable::internal_default_instance());
  ::tensorflow::_SavedObject_default_instance_.bare_concrete_function_ = const_cast< ::tensorflow::SavedBareConcreteFunction*>(
      ::tensorflow::SavedBareConcreteFunction::internal_default_instance());
  ::tensorflow::_SavedObject_default_instance_.constant_ = const_cast< ::tensorflow::SavedConstant*>(
      ::tensorflow::SavedConstant::internal_default_instance());
  ::tensorflow::_SavedObject_default_instance_.resource_ = const_cast< ::tensorflow::SavedResource*>(
      ::tensorflow::SavedResource::internal_default_instance());
}
void SavedObject::clear_children() {
  children_.Clear();
}
void SavedObject::clear_slot_variables() {
  slot_variables_.Clear();
}
void SavedObject::set_allocated_user_object(::tensorflow::SavedUserObject* user_object) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_kind();
  if (user_object) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(user_object);
    if (message_arena != submessage_arena) {
      user_object = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, user_object, submessage_arena);
    }
    set_has_user_object();
    kind_.user_object_ = user_object;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedObject.user_object)
}
void SavedObject::set_allocated_asset(::tensorflow::SavedAsset* asset) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_kind();
  if (asset) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(asset);
    if (message_arena != submessage_arena) {
      asset = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, asset, submessage_arena);
    }
    set_has_asset();
    kind_.asset_ = asset;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedObject.asset)
}
void SavedObject::set_allocated_function(::tensorflow::SavedFunction* function) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_kind();
  if (function) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(function);
    if (message_arena != submessage_arena) {
      function = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, function, submessage_arena);
    }
    set_has_function();
    kind_.function_ = function;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedObject.function)
}
void SavedObject::set_allocated_variable(::tensorflow::SavedVariable* variable) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_kind();
  if (variable) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(variable);
    if (message_arena != submessage_arena) {
      variable = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, variable, submessage_arena);
    }
    set_has_variable();
    kind_.variable_ = variable;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedObject.variable)
}
void SavedObject::set_allocated_bare_concrete_function(::tensorflow::SavedBareConcreteFunction* bare_concrete_function) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_kind();
  if (bare_concrete_function) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(bare_concrete_function);
    if (message_arena != submessage_arena) {
      bare_concrete_function = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, bare_concrete_function, submessage_arena);
    }
    set_has_bare_concrete_function();
    kind_.bare_concrete_function_ = bare_concrete_function;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedObject.bare_concrete_function)
}
void SavedObject::set_allocated_constant(::tensorflow::SavedConstant* constant) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_kind();
  if (constant) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(constant);
    if (message_arena != submessage_arena) {
      constant = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, constant, submessage_arena);
    }
    set_has_constant();
    kind_.constant_ = constant;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedObject.constant)
}
void SavedObject::set_allocated_resource(::tensorflow::SavedResource* resource) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_kind();
  if (resource) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(resource);
    if (message_arena != submessage_arena) {
      resource = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, resource, submessage_arena);
    }
    set_has_resource();
    kind_.resource_ = resource;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedObject.resource)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SavedObject::kChildrenFieldNumber;
const int SavedObject::kSlotVariablesFieldNumber;
const int SavedObject::kUserObjectFieldNumber;
const int SavedObject::kAssetFieldNumber;
const int SavedObject::kFunctionFieldNumber;
const int SavedObject::kVariableFieldNumber;
const int SavedObject::kBareConcreteFunctionFieldNumber;
const int SavedObject::kConstantFieldNumber;
const int SavedObject::kResourceFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SavedObject::SavedObject()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedObject.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.SavedObject)
}
SavedObject::SavedObject(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  children_(arena),
  slot_variables_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedObject.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.SavedObject)
}
SavedObject::SavedObject(const SavedObject& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      children_(from.children_),
      slot_variables_(from.slot_variables_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  clear_has_kind();
  switch (from.kind_case()) {
    case kUserObject: {
      mutable_user_object()->::tensorflow::SavedUserObject::MergeFrom(from.user_object());
      break;
    }
    case kAsset: {
      mutable_asset()->::tensorflow::SavedAsset::MergeFrom(from.asset());
      break;
    }
    case kFunction: {
      mutable_function()->::tensorflow::SavedFunction::MergeFrom(from.function());
      break;
    }
    case kVariable: {
      mutable_variable()->::tensorflow::SavedVariable::MergeFrom(from.variable());
      break;
    }
    case kBareConcreteFunction: {
      mutable_bare_concrete_function()->::tensorflow::SavedBareConcreteFunction::MergeFrom(from.bare_concrete_function());
      break;
    }
    case kConstant: {
      mutable_constant()->::tensorflow::SavedConstant::MergeFrom(from.constant());
      break;
    }
    case kResource: {
      mutable_resource()->::tensorflow::SavedResource::MergeFrom(from.resource());
      break;
    }
    case KIND_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.SavedObject)
}

void SavedObject::SharedCtor() {
  clear_has_kind();
}

SavedObject::~SavedObject() {
  // @@protoc_insertion_point(destructor:tensorflow.SavedObject)
  SharedDtor();
}

void SavedObject::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (has_kind()) {
    clear_kind();
  }
}

void SavedObject::ArenaDtor(void* object) {
  SavedObject* _this = reinterpret_cast< SavedObject* >(object);
  (void)_this;
}
void SavedObject::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void SavedObject::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* SavedObject::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const SavedObject& SavedObject::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedObject.base);
  return *internal_default_instance();
}


void SavedObject::clear_kind() {
// @@protoc_insertion_point(one_of_clear_start:tensorflow.SavedObject)
  switch (kind_case()) {
    case kUserObject: {
      if (GetArenaNoVirtual() == NULL) {
        delete kind_.user_object_;
      }
      break;
    }
    case kAsset: {
      if (GetArenaNoVirtual() == NULL) {
        delete kind_.asset_;
      }
      break;
    }
    case kFunction: {
      if (GetArenaNoVirtual() == NULL) {
        delete kind_.function_;
      }
      break;
    }
    case kVariable: {
      if (GetArenaNoVirtual() == NULL) {
        delete kind_.variable_;
      }
      break;
    }
    case kBareConcreteFunction: {
      if (GetArenaNoVirtual() == NULL) {
        delete kind_.bare_concrete_function_;
      }
      break;
    }
    case kConstant: {
      if (GetArenaNoVirtual() == NULL) {
        delete kind_.constant_;
      }
      break;
    }
    case kResource: {
      if (GetArenaNoVirtual() == NULL) {
        delete kind_.resource_;
      }
      break;
    }
    case KIND_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = KIND_NOT_SET;
}


void SavedObject::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.SavedObject)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  children_.Clear();
  slot_variables_.Clear();
  clear_kind();
  _internal_metadata_.Clear();
}

bool SavedObject::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.SavedObject)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference children = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_children()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference slot_variables = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_slot_variables()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.SavedUserObject user_object = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_user_object()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.SavedAsset asset = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_asset()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.SavedFunction function = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_function()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.SavedVariable variable = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_variable()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.SavedBareConcreteFunction bare_concrete_function = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(66u /* 66 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_bare_concrete_function()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.SavedConstant constant = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(74u /* 74 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_constant()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.SavedResource resource = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(82u /* 82 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_resource()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.SavedObject)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.SavedObject)
  return false;
#undef DO_
}

void SavedObject::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.SavedObject)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference children = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->children_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->children(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference slot_variables = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->slot_variables_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3,
      this->slot_variables(static_cast<int>(i)),
      output);
  }

  // .tensorflow.SavedUserObject user_object = 4;
  if (has_user_object()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->_internal_user_object(), output);
  }

  // .tensorflow.SavedAsset asset = 5;
  if (has_asset()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, this->_internal_asset(), output);
  }

  // .tensorflow.SavedFunction function = 6;
  if (has_function()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, this->_internal_function(), output);
  }

  // .tensorflow.SavedVariable variable = 7;
  if (has_variable()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7, this->_internal_variable(), output);
  }

  // .tensorflow.SavedBareConcreteFunction bare_concrete_function = 8;
  if (has_bare_concrete_function()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8, this->_internal_bare_concrete_function(), output);
  }

  // .tensorflow.SavedConstant constant = 9;
  if (has_constant()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, this->_internal_constant(), output);
  }

  // .tensorflow.SavedResource resource = 10;
  if (has_resource()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      10, this->_internal_resource(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.SavedObject)
}

::google::protobuf::uint8* SavedObject::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.SavedObject)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference children = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->children_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->children(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference slot_variables = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->slot_variables_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->slot_variables(static_cast<int>(i)), deterministic, target);
  }

  // .tensorflow.SavedUserObject user_object = 4;
  if (has_user_object()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->_internal_user_object(), deterministic, target);
  }

  // .tensorflow.SavedAsset asset = 5;
  if (has_asset()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        5, this->_internal_asset(), deterministic, target);
  }

  // .tensorflow.SavedFunction function = 6;
  if (has_function()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        6, this->_internal_function(), deterministic, target);
  }

  // .tensorflow.SavedVariable variable = 7;
  if (has_variable()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        7, this->_internal_variable(), deterministic, target);
  }

  // .tensorflow.SavedBareConcreteFunction bare_concrete_function = 8;
  if (has_bare_concrete_function()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        8, this->_internal_bare_concrete_function(), deterministic, target);
  }

  // .tensorflow.SavedConstant constant = 9;
  if (has_constant()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        9, this->_internal_constant(), deterministic, target);
  }

  // .tensorflow.SavedResource resource = 10;
  if (has_resource()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        10, this->_internal_resource(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.SavedObject)
  return target;
}

size_t SavedObject::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.SavedObject)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference children = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->children_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->children(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference slot_variables = 3;
  {
    unsigned int count = static_cast<unsigned int>(this->slot_variables_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->slot_variables(static_cast<int>(i)));
    }
  }

  switch (kind_case()) {
    // .tensorflow.SavedUserObject user_object = 4;
    case kUserObject: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *kind_.user_object_);
      break;
    }
    // .tensorflow.SavedAsset asset = 5;
    case kAsset: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *kind_.asset_);
      break;
    }
    // .tensorflow.SavedFunction function = 6;
    case kFunction: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *kind_.function_);
      break;
    }
    // .tensorflow.SavedVariable variable = 7;
    case kVariable: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *kind_.variable_);
      break;
    }
    // .tensorflow.SavedBareConcreteFunction bare_concrete_function = 8;
    case kBareConcreteFunction: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *kind_.bare_concrete_function_);
      break;
    }
    // .tensorflow.SavedConstant constant = 9;
    case kConstant: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *kind_.constant_);
      break;
    }
    // .tensorflow.SavedResource resource = 10;
    case kResource: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *kind_.resource_);
      break;
    }
    case KIND_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SavedObject::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.SavedObject)
  GOOGLE_DCHECK_NE(&from, this);
  const SavedObject* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SavedObject>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.SavedObject)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.SavedObject)
    MergeFrom(*source);
  }
}

void SavedObject::MergeFrom(const SavedObject& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.SavedObject)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  children_.MergeFrom(from.children_);
  slot_variables_.MergeFrom(from.slot_variables_);
  switch (from.kind_case()) {
    case kUserObject: {
      mutable_user_object()->::tensorflow::SavedUserObject::MergeFrom(from.user_object());
      break;
    }
    case kAsset: {
      mutable_asset()->::tensorflow::SavedAsset::MergeFrom(from.asset());
      break;
    }
    case kFunction: {
      mutable_function()->::tensorflow::SavedFunction::MergeFrom(from.function());
      break;
    }
    case kVariable: {
      mutable_variable()->::tensorflow::SavedVariable::MergeFrom(from.variable());
      break;
    }
    case kBareConcreteFunction: {
      mutable_bare_concrete_function()->::tensorflow::SavedBareConcreteFunction::MergeFrom(from.bare_concrete_function());
      break;
    }
    case kConstant: {
      mutable_constant()->::tensorflow::SavedConstant::MergeFrom(from.constant());
      break;
    }
    case kResource: {
      mutable_resource()->::tensorflow::SavedResource::MergeFrom(from.resource());
      break;
    }
    case KIND_NOT_SET: {
      break;
    }
  }
}

void SavedObject::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.SavedObject)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SavedObject::CopyFrom(const SavedObject& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.SavedObject)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SavedObject::IsInitialized() const {
  return true;
}

void SavedObject::Swap(SavedObject* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    SavedObject* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void SavedObject::UnsafeArenaSwap(SavedObject* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void SavedObject::InternalSwap(SavedObject* other) {
  using std::swap;
  CastToBase(&children_)->InternalSwap(CastToBase(&other->children_));
  CastToBase(&slot_variables_)->InternalSwap(CastToBase(&other->slot_variables_));
  swap(kind_, other->kind_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata SavedObject::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void SavedUserObject::InitAsDefaultInstance() {
  ::tensorflow::_SavedUserObject_default_instance_._instance.get_mutable()->version_ = const_cast< ::tensorflow::VersionDef*>(
      ::tensorflow::VersionDef::internal_default_instance());
}
void SavedUserObject::unsafe_arena_set_allocated_version(
    ::tensorflow::VersionDef* version) {
  if (GetArenaNoVirtual() == NULL) {
    delete version_;
  }
  version_ = version;
  if (version) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedUserObject.version)
}
void SavedUserObject::clear_version() {
  if (GetArenaNoVirtual() == NULL && version_ != NULL) {
    delete version_;
  }
  version_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SavedUserObject::kIdentifierFieldNumber;
const int SavedUserObject::kVersionFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SavedUserObject::SavedUserObject()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedUserObject.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.SavedUserObject)
}
SavedUserObject::SavedUserObject(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedUserObject.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.SavedUserObject)
}
SavedUserObject::SavedUserObject(const SavedUserObject& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  identifier_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.identifier().size() > 0) {
    identifier_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.identifier(),
      GetArenaNoVirtual());
  }
  if (from.has_version()) {
    version_ = new ::tensorflow::VersionDef(*from.version_);
  } else {
    version_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.SavedUserObject)
}

void SavedUserObject::SharedCtor() {
  identifier_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  version_ = NULL;
}

SavedUserObject::~SavedUserObject() {
  // @@protoc_insertion_point(destructor:tensorflow.SavedUserObject)
  SharedDtor();
}

void SavedUserObject::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  identifier_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete version_;
}

void SavedUserObject::ArenaDtor(void* object) {
  SavedUserObject* _this = reinterpret_cast< SavedUserObject* >(object);
  (void)_this;
}
void SavedUserObject::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void SavedUserObject::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* SavedUserObject::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const SavedUserObject& SavedUserObject::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedUserObject.base);
  return *internal_default_instance();
}


void SavedUserObject::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.SavedUserObject)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  identifier_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && version_ != NULL) {
    delete version_;
  }
  version_ = NULL;
  _internal_metadata_.Clear();
}

bool SavedUserObject::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.SavedUserObject)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string identifier = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_identifier()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->identifier().data(), static_cast<int>(this->identifier().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.SavedUserObject.identifier"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.VersionDef version = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_version()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.SavedUserObject)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.SavedUserObject)
  return false;
#undef DO_
}

void SavedUserObject::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.SavedUserObject)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string identifier = 1;
  if (this->identifier().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->identifier().data(), static_cast<int>(this->identifier().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SavedUserObject.identifier");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->identifier(), output);
  }

  // .tensorflow.VersionDef version = 2;
  if (this->has_version()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_version(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.SavedUserObject)
}

::google::protobuf::uint8* SavedUserObject::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.SavedUserObject)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string identifier = 1;
  if (this->identifier().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->identifier().data(), static_cast<int>(this->identifier().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SavedUserObject.identifier");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->identifier(), target);
  }

  // .tensorflow.VersionDef version = 2;
  if (this->has_version()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_version(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.SavedUserObject)
  return target;
}

size_t SavedUserObject::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.SavedUserObject)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string identifier = 1;
  if (this->identifier().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->identifier());
  }

  // .tensorflow.VersionDef version = 2;
  if (this->has_version()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *version_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SavedUserObject::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.SavedUserObject)
  GOOGLE_DCHECK_NE(&from, this);
  const SavedUserObject* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SavedUserObject>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.SavedUserObject)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.SavedUserObject)
    MergeFrom(*source);
  }
}

void SavedUserObject::MergeFrom(const SavedUserObject& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.SavedUserObject)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.identifier().size() > 0) {
    set_identifier(from.identifier());
  }
  if (from.has_version()) {
    mutable_version()->::tensorflow::VersionDef::MergeFrom(from.version());
  }
}

void SavedUserObject::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.SavedUserObject)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SavedUserObject::CopyFrom(const SavedUserObject& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.SavedUserObject)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SavedUserObject::IsInitialized() const {
  return true;
}

void SavedUserObject::Swap(SavedUserObject* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    SavedUserObject* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void SavedUserObject::UnsafeArenaSwap(SavedUserObject* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void SavedUserObject::InternalSwap(SavedUserObject* other) {
  using std::swap;
  identifier_.Swap(&other->identifier_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(version_, other->version_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata SavedUserObject::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void SavedAsset::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SavedAsset::kAssetFileDefIndexFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SavedAsset::SavedAsset()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedAsset.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.SavedAsset)
}
SavedAsset::SavedAsset(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedAsset.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.SavedAsset)
}
SavedAsset::SavedAsset(const SavedAsset& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  asset_file_def_index_ = from.asset_file_def_index_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.SavedAsset)
}

void SavedAsset::SharedCtor() {
  asset_file_def_index_ = 0;
}

SavedAsset::~SavedAsset() {
  // @@protoc_insertion_point(destructor:tensorflow.SavedAsset)
  SharedDtor();
}

void SavedAsset::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void SavedAsset::ArenaDtor(void* object) {
  SavedAsset* _this = reinterpret_cast< SavedAsset* >(object);
  (void)_this;
}
void SavedAsset::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void SavedAsset::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* SavedAsset::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const SavedAsset& SavedAsset::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedAsset.base);
  return *internal_default_instance();
}


void SavedAsset::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.SavedAsset)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  asset_file_def_index_ = 0;
  _internal_metadata_.Clear();
}

bool SavedAsset::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.SavedAsset)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 asset_file_def_index = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &asset_file_def_index_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.SavedAsset)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.SavedAsset)
  return false;
#undef DO_
}

void SavedAsset::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.SavedAsset)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 asset_file_def_index = 1;
  if (this->asset_file_def_index() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->asset_file_def_index(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.SavedAsset)
}

::google::protobuf::uint8* SavedAsset::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.SavedAsset)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 asset_file_def_index = 1;
  if (this->asset_file_def_index() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->asset_file_def_index(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.SavedAsset)
  return target;
}

size_t SavedAsset::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.SavedAsset)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int32 asset_file_def_index = 1;
  if (this->asset_file_def_index() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->asset_file_def_index());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SavedAsset::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.SavedAsset)
  GOOGLE_DCHECK_NE(&from, this);
  const SavedAsset* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SavedAsset>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.SavedAsset)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.SavedAsset)
    MergeFrom(*source);
  }
}

void SavedAsset::MergeFrom(const SavedAsset& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.SavedAsset)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.asset_file_def_index() != 0) {
    set_asset_file_def_index(from.asset_file_def_index());
  }
}

void SavedAsset::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.SavedAsset)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SavedAsset::CopyFrom(const SavedAsset& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.SavedAsset)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SavedAsset::IsInitialized() const {
  return true;
}

void SavedAsset::Swap(SavedAsset* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    SavedAsset* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void SavedAsset::UnsafeArenaSwap(SavedAsset* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void SavedAsset::InternalSwap(SavedAsset* other) {
  using std::swap;
  swap(asset_file_def_index_, other->asset_file_def_index_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata SavedAsset::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void SavedFunction::InitAsDefaultInstance() {
  ::tensorflow::_SavedFunction_default_instance_._instance.get_mutable()->function_spec_ = const_cast< ::tensorflow::FunctionSpec*>(
      ::tensorflow::FunctionSpec::internal_default_instance());
}
void SavedFunction::unsafe_arena_set_allocated_function_spec(
    ::tensorflow::FunctionSpec* function_spec) {
  if (GetArenaNoVirtual() == NULL) {
    delete function_spec_;
  }
  function_spec_ = function_spec;
  if (function_spec) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedFunction.function_spec)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SavedFunction::kConcreteFunctionsFieldNumber;
const int SavedFunction::kFunctionSpecFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SavedFunction::SavedFunction()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedFunction.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.SavedFunction)
}
SavedFunction::SavedFunction(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  concrete_functions_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedFunction.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.SavedFunction)
}
SavedFunction::SavedFunction(const SavedFunction& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      concrete_functions_(from.concrete_functions_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_function_spec()) {
    function_spec_ = new ::tensorflow::FunctionSpec(*from.function_spec_);
  } else {
    function_spec_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.SavedFunction)
}

void SavedFunction::SharedCtor() {
  function_spec_ = NULL;
}

SavedFunction::~SavedFunction() {
  // @@protoc_insertion_point(destructor:tensorflow.SavedFunction)
  SharedDtor();
}

void SavedFunction::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (this != internal_default_instance()) delete function_spec_;
}

void SavedFunction::ArenaDtor(void* object) {
  SavedFunction* _this = reinterpret_cast< SavedFunction* >(object);
  (void)_this;
}
void SavedFunction::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void SavedFunction::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* SavedFunction::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const SavedFunction& SavedFunction::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedFunction.base);
  return *internal_default_instance();
}


void SavedFunction::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.SavedFunction)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  concrete_functions_.Clear();
  if (GetArenaNoVirtual() == NULL && function_spec_ != NULL) {
    delete function_spec_;
  }
  function_spec_ = NULL;
  _internal_metadata_.Clear();
}

bool SavedFunction::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.SavedFunction)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated string concrete_functions = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_concrete_functions()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->concrete_functions(this->concrete_functions_size() - 1).data(),
            static_cast<int>(this->concrete_functions(this->concrete_functions_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.SavedFunction.concrete_functions"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.FunctionSpec function_spec = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_function_spec()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.SavedFunction)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.SavedFunction)
  return false;
#undef DO_
}

void SavedFunction::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.SavedFunction)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string concrete_functions = 1;
  for (int i = 0, n = this->concrete_functions_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->concrete_functions(i).data(), static_cast<int>(this->concrete_functions(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SavedFunction.concrete_functions");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->concrete_functions(i), output);
  }

  // .tensorflow.FunctionSpec function_spec = 2;
  if (this->has_function_spec()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_function_spec(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.SavedFunction)
}

::google::protobuf::uint8* SavedFunction::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.SavedFunction)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string concrete_functions = 1;
  for (int i = 0, n = this->concrete_functions_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->concrete_functions(i).data(), static_cast<int>(this->concrete_functions(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SavedFunction.concrete_functions");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(1, this->concrete_functions(i), target);
  }

  // .tensorflow.FunctionSpec function_spec = 2;
  if (this->has_function_spec()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_function_spec(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.SavedFunction)
  return target;
}

size_t SavedFunction::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.SavedFunction)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated string concrete_functions = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->concrete_functions_size());
  for (int i = 0, n = this->concrete_functions_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->concrete_functions(i));
  }

  // .tensorflow.FunctionSpec function_spec = 2;
  if (this->has_function_spec()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *function_spec_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SavedFunction::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.SavedFunction)
  GOOGLE_DCHECK_NE(&from, this);
  const SavedFunction* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SavedFunction>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.SavedFunction)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.SavedFunction)
    MergeFrom(*source);
  }
}

void SavedFunction::MergeFrom(const SavedFunction& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.SavedFunction)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  concrete_functions_.MergeFrom(from.concrete_functions_);
  if (from.has_function_spec()) {
    mutable_function_spec()->::tensorflow::FunctionSpec::MergeFrom(from.function_spec());
  }
}

void SavedFunction::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.SavedFunction)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SavedFunction::CopyFrom(const SavedFunction& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.SavedFunction)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SavedFunction::IsInitialized() const {
  return true;
}

void SavedFunction::Swap(SavedFunction* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    SavedFunction* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void SavedFunction::UnsafeArenaSwap(SavedFunction* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void SavedFunction::InternalSwap(SavedFunction* other) {
  using std::swap;
  concrete_functions_.InternalSwap(CastToBase(&other->concrete_functions_));
  swap(function_spec_, other->function_spec_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata SavedFunction::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void SavedConcreteFunction::InitAsDefaultInstance() {
  ::tensorflow::_SavedConcreteFunction_default_instance_._instance.get_mutable()->canonicalized_input_signature_ = const_cast< ::tensorflow::StructuredValue*>(
      ::tensorflow::StructuredValue::internal_default_instance());
  ::tensorflow::_SavedConcreteFunction_default_instance_._instance.get_mutable()->output_signature_ = const_cast< ::tensorflow::StructuredValue*>(
      ::tensorflow::StructuredValue::internal_default_instance());
}
void SavedConcreteFunction::unsafe_arena_set_allocated_canonicalized_input_signature(
    ::tensorflow::StructuredValue* canonicalized_input_signature) {
  if (GetArenaNoVirtual() == NULL) {
    delete canonicalized_input_signature_;
  }
  canonicalized_input_signature_ = canonicalized_input_signature;
  if (canonicalized_input_signature) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedConcreteFunction.canonicalized_input_signature)
}
void SavedConcreteFunction::clear_canonicalized_input_signature() {
  if (GetArenaNoVirtual() == NULL && canonicalized_input_signature_ != NULL) {
    delete canonicalized_input_signature_;
  }
  canonicalized_input_signature_ = NULL;
}
void SavedConcreteFunction::unsafe_arena_set_allocated_output_signature(
    ::tensorflow::StructuredValue* output_signature) {
  if (GetArenaNoVirtual() == NULL) {
    delete output_signature_;
  }
  output_signature_ = output_signature;
  if (output_signature) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedConcreteFunction.output_signature)
}
void SavedConcreteFunction::clear_output_signature() {
  if (GetArenaNoVirtual() == NULL && output_signature_ != NULL) {
    delete output_signature_;
  }
  output_signature_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SavedConcreteFunction::kBoundInputsFieldNumber;
const int SavedConcreteFunction::kCanonicalizedInputSignatureFieldNumber;
const int SavedConcreteFunction::kOutputSignatureFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SavedConcreteFunction::SavedConcreteFunction()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedConcreteFunction.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.SavedConcreteFunction)
}
SavedConcreteFunction::SavedConcreteFunction(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  bound_inputs_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedConcreteFunction.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.SavedConcreteFunction)
}
SavedConcreteFunction::SavedConcreteFunction(const SavedConcreteFunction& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      bound_inputs_(from.bound_inputs_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_canonicalized_input_signature()) {
    canonicalized_input_signature_ = new ::tensorflow::StructuredValue(*from.canonicalized_input_signature_);
  } else {
    canonicalized_input_signature_ = NULL;
  }
  if (from.has_output_signature()) {
    output_signature_ = new ::tensorflow::StructuredValue(*from.output_signature_);
  } else {
    output_signature_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.SavedConcreteFunction)
}

void SavedConcreteFunction::SharedCtor() {
  ::memset(&canonicalized_input_signature_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&output_signature_) -
      reinterpret_cast<char*>(&canonicalized_input_signature_)) + sizeof(output_signature_));
}

SavedConcreteFunction::~SavedConcreteFunction() {
  // @@protoc_insertion_point(destructor:tensorflow.SavedConcreteFunction)
  SharedDtor();
}

void SavedConcreteFunction::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (this != internal_default_instance()) delete canonicalized_input_signature_;
  if (this != internal_default_instance()) delete output_signature_;
}

void SavedConcreteFunction::ArenaDtor(void* object) {
  SavedConcreteFunction* _this = reinterpret_cast< SavedConcreteFunction* >(object);
  (void)_this;
}
void SavedConcreteFunction::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void SavedConcreteFunction::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* SavedConcreteFunction::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const SavedConcreteFunction& SavedConcreteFunction::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedConcreteFunction.base);
  return *internal_default_instance();
}


void SavedConcreteFunction::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.SavedConcreteFunction)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  bound_inputs_.Clear();
  if (GetArenaNoVirtual() == NULL && canonicalized_input_signature_ != NULL) {
    delete canonicalized_input_signature_;
  }
  canonicalized_input_signature_ = NULL;
  if (GetArenaNoVirtual() == NULL && output_signature_ != NULL) {
    delete output_signature_;
  }
  output_signature_ = NULL;
  _internal_metadata_.Clear();
}

bool SavedConcreteFunction::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.SavedConcreteFunction)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated int32 bound_inputs = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_bound_inputs())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 1, 18u, input, this->mutable_bound_inputs())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.StructuredValue canonicalized_input_signature = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_canonicalized_input_signature()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.StructuredValue output_signature = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_output_signature()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.SavedConcreteFunction)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.SavedConcreteFunction)
  return false;
#undef DO_
}

void SavedConcreteFunction::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.SavedConcreteFunction)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated int32 bound_inputs = 2;
  if (this->bound_inputs_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(2, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _bound_inputs_cached_byte_size_));
  }
  for (int i = 0, n = this->bound_inputs_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32NoTag(
      this->bound_inputs(i), output);
  }

  // .tensorflow.StructuredValue canonicalized_input_signature = 3;
  if (this->has_canonicalized_input_signature()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->_internal_canonicalized_input_signature(), output);
  }

  // .tensorflow.StructuredValue output_signature = 4;
  if (this->has_output_signature()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->_internal_output_signature(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.SavedConcreteFunction)
}

::google::protobuf::uint8* SavedConcreteFunction::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.SavedConcreteFunction)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated int32 bound_inputs = 2;
  if (this->bound_inputs_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      2,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _bound_inputs_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt32NoTagToArray(this->bound_inputs_, target);
  }

  // .tensorflow.StructuredValue canonicalized_input_signature = 3;
  if (this->has_canonicalized_input_signature()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->_internal_canonicalized_input_signature(), deterministic, target);
  }

  // .tensorflow.StructuredValue output_signature = 4;
  if (this->has_output_signature()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->_internal_output_signature(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.SavedConcreteFunction)
  return target;
}

size_t SavedConcreteFunction::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.SavedConcreteFunction)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated int32 bound_inputs = 2;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int32Size(this->bound_inputs_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _bound_inputs_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // .tensorflow.StructuredValue canonicalized_input_signature = 3;
  if (this->has_canonicalized_input_signature()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *canonicalized_input_signature_);
  }

  // .tensorflow.StructuredValue output_signature = 4;
  if (this->has_output_signature()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *output_signature_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SavedConcreteFunction::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.SavedConcreteFunction)
  GOOGLE_DCHECK_NE(&from, this);
  const SavedConcreteFunction* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SavedConcreteFunction>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.SavedConcreteFunction)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.SavedConcreteFunction)
    MergeFrom(*source);
  }
}

void SavedConcreteFunction::MergeFrom(const SavedConcreteFunction& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.SavedConcreteFunction)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  bound_inputs_.MergeFrom(from.bound_inputs_);
  if (from.has_canonicalized_input_signature()) {
    mutable_canonicalized_input_signature()->::tensorflow::StructuredValue::MergeFrom(from.canonicalized_input_signature());
  }
  if (from.has_output_signature()) {
    mutable_output_signature()->::tensorflow::StructuredValue::MergeFrom(from.output_signature());
  }
}

void SavedConcreteFunction::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.SavedConcreteFunction)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SavedConcreteFunction::CopyFrom(const SavedConcreteFunction& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.SavedConcreteFunction)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SavedConcreteFunction::IsInitialized() const {
  return true;
}

void SavedConcreteFunction::Swap(SavedConcreteFunction* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    SavedConcreteFunction* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void SavedConcreteFunction::UnsafeArenaSwap(SavedConcreteFunction* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void SavedConcreteFunction::InternalSwap(SavedConcreteFunction* other) {
  using std::swap;
  bound_inputs_.InternalSwap(&other->bound_inputs_);
  swap(canonicalized_input_signature_, other->canonicalized_input_signature_);
  swap(output_signature_, other->output_signature_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata SavedConcreteFunction::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void SavedBareConcreteFunction::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SavedBareConcreteFunction::kConcreteFunctionNameFieldNumber;
const int SavedBareConcreteFunction::kArgumentKeywordsFieldNumber;
const int SavedBareConcreteFunction::kAllowedPositionalArgumentsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SavedBareConcreteFunction::SavedBareConcreteFunction()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedBareConcreteFunction.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.SavedBareConcreteFunction)
}
SavedBareConcreteFunction::SavedBareConcreteFunction(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  argument_keywords_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedBareConcreteFunction.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.SavedBareConcreteFunction)
}
SavedBareConcreteFunction::SavedBareConcreteFunction(const SavedBareConcreteFunction& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      argument_keywords_(from.argument_keywords_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  concrete_function_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.concrete_function_name().size() > 0) {
    concrete_function_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.concrete_function_name(),
      GetArenaNoVirtual());
  }
  allowed_positional_arguments_ = from.allowed_positional_arguments_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.SavedBareConcreteFunction)
}

void SavedBareConcreteFunction::SharedCtor() {
  concrete_function_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  allowed_positional_arguments_ = GOOGLE_LONGLONG(0);
}

SavedBareConcreteFunction::~SavedBareConcreteFunction() {
  // @@protoc_insertion_point(destructor:tensorflow.SavedBareConcreteFunction)
  SharedDtor();
}

void SavedBareConcreteFunction::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  concrete_function_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void SavedBareConcreteFunction::ArenaDtor(void* object) {
  SavedBareConcreteFunction* _this = reinterpret_cast< SavedBareConcreteFunction* >(object);
  (void)_this;
}
void SavedBareConcreteFunction::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void SavedBareConcreteFunction::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* SavedBareConcreteFunction::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const SavedBareConcreteFunction& SavedBareConcreteFunction::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedBareConcreteFunction.base);
  return *internal_default_instance();
}


void SavedBareConcreteFunction::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.SavedBareConcreteFunction)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  argument_keywords_.Clear();
  concrete_function_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  allowed_positional_arguments_ = GOOGLE_LONGLONG(0);
  _internal_metadata_.Clear();
}

bool SavedBareConcreteFunction::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.SavedBareConcreteFunction)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string concrete_function_name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_concrete_function_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->concrete_function_name().data(), static_cast<int>(this->concrete_function_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.SavedBareConcreteFunction.concrete_function_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string argument_keywords = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_argument_keywords()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->argument_keywords(this->argument_keywords_size() - 1).data(),
            static_cast<int>(this->argument_keywords(this->argument_keywords_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.SavedBareConcreteFunction.argument_keywords"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 allowed_positional_arguments = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &allowed_positional_arguments_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.SavedBareConcreteFunction)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.SavedBareConcreteFunction)
  return false;
#undef DO_
}

void SavedBareConcreteFunction::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.SavedBareConcreteFunction)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string concrete_function_name = 1;
  if (this->concrete_function_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->concrete_function_name().data(), static_cast<int>(this->concrete_function_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SavedBareConcreteFunction.concrete_function_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->concrete_function_name(), output);
  }

  // repeated string argument_keywords = 2;
  for (int i = 0, n = this->argument_keywords_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->argument_keywords(i).data(), static_cast<int>(this->argument_keywords(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SavedBareConcreteFunction.argument_keywords");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->argument_keywords(i), output);
  }

  // int64 allowed_positional_arguments = 3;
  if (this->allowed_positional_arguments() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->allowed_positional_arguments(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.SavedBareConcreteFunction)
}

::google::protobuf::uint8* SavedBareConcreteFunction::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.SavedBareConcreteFunction)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string concrete_function_name = 1;
  if (this->concrete_function_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->concrete_function_name().data(), static_cast<int>(this->concrete_function_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SavedBareConcreteFunction.concrete_function_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->concrete_function_name(), target);
  }

  // repeated string argument_keywords = 2;
  for (int i = 0, n = this->argument_keywords_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->argument_keywords(i).data(), static_cast<int>(this->argument_keywords(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SavedBareConcreteFunction.argument_keywords");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(2, this->argument_keywords(i), target);
  }

  // int64 allowed_positional_arguments = 3;
  if (this->allowed_positional_arguments() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->allowed_positional_arguments(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.SavedBareConcreteFunction)
  return target;
}

size_t SavedBareConcreteFunction::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.SavedBareConcreteFunction)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated string argument_keywords = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->argument_keywords_size());
  for (int i = 0, n = this->argument_keywords_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->argument_keywords(i));
  }

  // string concrete_function_name = 1;
  if (this->concrete_function_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->concrete_function_name());
  }

  // int64 allowed_positional_arguments = 3;
  if (this->allowed_positional_arguments() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->allowed_positional_arguments());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SavedBareConcreteFunction::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.SavedBareConcreteFunction)
  GOOGLE_DCHECK_NE(&from, this);
  const SavedBareConcreteFunction* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SavedBareConcreteFunction>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.SavedBareConcreteFunction)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.SavedBareConcreteFunction)
    MergeFrom(*source);
  }
}

void SavedBareConcreteFunction::MergeFrom(const SavedBareConcreteFunction& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.SavedBareConcreteFunction)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  argument_keywords_.MergeFrom(from.argument_keywords_);
  if (from.concrete_function_name().size() > 0) {
    set_concrete_function_name(from.concrete_function_name());
  }
  if (from.allowed_positional_arguments() != 0) {
    set_allowed_positional_arguments(from.allowed_positional_arguments());
  }
}

void SavedBareConcreteFunction::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.SavedBareConcreteFunction)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SavedBareConcreteFunction::CopyFrom(const SavedBareConcreteFunction& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.SavedBareConcreteFunction)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SavedBareConcreteFunction::IsInitialized() const {
  return true;
}

void SavedBareConcreteFunction::Swap(SavedBareConcreteFunction* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    SavedBareConcreteFunction* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void SavedBareConcreteFunction::UnsafeArenaSwap(SavedBareConcreteFunction* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void SavedBareConcreteFunction::InternalSwap(SavedBareConcreteFunction* other) {
  using std::swap;
  argument_keywords_.InternalSwap(CastToBase(&other->argument_keywords_));
  concrete_function_name_.Swap(&other->concrete_function_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(allowed_positional_arguments_, other->allowed_positional_arguments_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata SavedBareConcreteFunction::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void SavedConstant::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SavedConstant::kOperationFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SavedConstant::SavedConstant()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedConstant.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.SavedConstant)
}
SavedConstant::SavedConstant(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedConstant.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.SavedConstant)
}
SavedConstant::SavedConstant(const SavedConstant& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  operation_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.operation().size() > 0) {
    operation_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.operation(),
      GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.SavedConstant)
}

void SavedConstant::SharedCtor() {
  operation_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

SavedConstant::~SavedConstant() {
  // @@protoc_insertion_point(destructor:tensorflow.SavedConstant)
  SharedDtor();
}

void SavedConstant::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  operation_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void SavedConstant::ArenaDtor(void* object) {
  SavedConstant* _this = reinterpret_cast< SavedConstant* >(object);
  (void)_this;
}
void SavedConstant::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void SavedConstant::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* SavedConstant::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const SavedConstant& SavedConstant::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedConstant.base);
  return *internal_default_instance();
}


void SavedConstant::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.SavedConstant)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  operation_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  _internal_metadata_.Clear();
}

bool SavedConstant::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.SavedConstant)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string operation = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_operation()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->operation().data(), static_cast<int>(this->operation().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.SavedConstant.operation"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.SavedConstant)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.SavedConstant)
  return false;
#undef DO_
}

void SavedConstant::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.SavedConstant)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string operation = 1;
  if (this->operation().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->operation().data(), static_cast<int>(this->operation().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SavedConstant.operation");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->operation(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.SavedConstant)
}

::google::protobuf::uint8* SavedConstant::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.SavedConstant)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string operation = 1;
  if (this->operation().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->operation().data(), static_cast<int>(this->operation().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SavedConstant.operation");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->operation(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.SavedConstant)
  return target;
}

size_t SavedConstant::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.SavedConstant)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string operation = 1;
  if (this->operation().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->operation());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SavedConstant::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.SavedConstant)
  GOOGLE_DCHECK_NE(&from, this);
  const SavedConstant* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SavedConstant>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.SavedConstant)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.SavedConstant)
    MergeFrom(*source);
  }
}

void SavedConstant::MergeFrom(const SavedConstant& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.SavedConstant)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.operation().size() > 0) {
    set_operation(from.operation());
  }
}

void SavedConstant::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.SavedConstant)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SavedConstant::CopyFrom(const SavedConstant& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.SavedConstant)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SavedConstant::IsInitialized() const {
  return true;
}

void SavedConstant::Swap(SavedConstant* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    SavedConstant* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void SavedConstant::UnsafeArenaSwap(SavedConstant* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void SavedConstant::InternalSwap(SavedConstant* other) {
  using std::swap;
  operation_.Swap(&other->operation_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata SavedConstant::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void SavedVariable::InitAsDefaultInstance() {
  ::tensorflow::_SavedVariable_default_instance_._instance.get_mutable()->shape_ = const_cast< ::tensorflow::TensorShapeProto*>(
      ::tensorflow::TensorShapeProto::internal_default_instance());
}
void SavedVariable::unsafe_arena_set_allocated_shape(
    ::tensorflow::TensorShapeProto* shape) {
  if (GetArenaNoVirtual() == NULL) {
    delete shape_;
  }
  shape_ = shape;
  if (shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedVariable.shape)
}
void SavedVariable::clear_shape() {
  if (GetArenaNoVirtual() == NULL && shape_ != NULL) {
    delete shape_;
  }
  shape_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SavedVariable::kDtypeFieldNumber;
const int SavedVariable::kShapeFieldNumber;
const int SavedVariable::kTrainableFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SavedVariable::SavedVariable()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedVariable.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.SavedVariable)
}
SavedVariable::SavedVariable(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedVariable.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.SavedVariable)
}
SavedVariable::SavedVariable(const SavedVariable& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_shape()) {
    shape_ = new ::tensorflow::TensorShapeProto(*from.shape_);
  } else {
    shape_ = NULL;
  }
  ::memcpy(&dtype_, &from.dtype_,
    static_cast<size_t>(reinterpret_cast<char*>(&trainable_) -
    reinterpret_cast<char*>(&dtype_)) + sizeof(trainable_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.SavedVariable)
}

void SavedVariable::SharedCtor() {
  ::memset(&shape_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&trainable_) -
      reinterpret_cast<char*>(&shape_)) + sizeof(trainable_));
}

SavedVariable::~SavedVariable() {
  // @@protoc_insertion_point(destructor:tensorflow.SavedVariable)
  SharedDtor();
}

void SavedVariable::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (this != internal_default_instance()) delete shape_;
}

void SavedVariable::ArenaDtor(void* object) {
  SavedVariable* _this = reinterpret_cast< SavedVariable* >(object);
  (void)_this;
}
void SavedVariable::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void SavedVariable::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* SavedVariable::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const SavedVariable& SavedVariable::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedVariable.base);
  return *internal_default_instance();
}


void SavedVariable::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.SavedVariable)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && shape_ != NULL) {
    delete shape_;
  }
  shape_ = NULL;
  ::memset(&dtype_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&trainable_) -
      reinterpret_cast<char*>(&dtype_)) + sizeof(trainable_));
  _internal_metadata_.Clear();
}

bool SavedVariable::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.SavedVariable)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.DataType dtype = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_dtype(static_cast< ::tensorflow::DataType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorShapeProto shape = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_shape()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool trainable = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &trainable_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.SavedVariable)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.SavedVariable)
  return false;
#undef DO_
}

void SavedVariable::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.SavedVariable)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.DataType dtype = 1;
  if (this->dtype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->dtype(), output);
  }

  // .tensorflow.TensorShapeProto shape = 2;
  if (this->has_shape()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_shape(), output);
  }

  // bool trainable = 3;
  if (this->trainable() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(3, this->trainable(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.SavedVariable)
}

::google::protobuf::uint8* SavedVariable::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.SavedVariable)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.DataType dtype = 1;
  if (this->dtype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->dtype(), target);
  }

  // .tensorflow.TensorShapeProto shape = 2;
  if (this->has_shape()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_shape(), deterministic, target);
  }

  // bool trainable = 3;
  if (this->trainable() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(3, this->trainable(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.SavedVariable)
  return target;
}

size_t SavedVariable::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.SavedVariable)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .tensorflow.TensorShapeProto shape = 2;
  if (this->has_shape()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *shape_);
  }

  // .tensorflow.DataType dtype = 1;
  if (this->dtype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->dtype());
  }

  // bool trainable = 3;
  if (this->trainable() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SavedVariable::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.SavedVariable)
  GOOGLE_DCHECK_NE(&from, this);
  const SavedVariable* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SavedVariable>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.SavedVariable)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.SavedVariable)
    MergeFrom(*source);
  }
}

void SavedVariable::MergeFrom(const SavedVariable& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.SavedVariable)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_shape()) {
    mutable_shape()->::tensorflow::TensorShapeProto::MergeFrom(from.shape());
  }
  if (from.dtype() != 0) {
    set_dtype(from.dtype());
  }
  if (from.trainable() != 0) {
    set_trainable(from.trainable());
  }
}

void SavedVariable::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.SavedVariable)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SavedVariable::CopyFrom(const SavedVariable& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.SavedVariable)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SavedVariable::IsInitialized() const {
  return true;
}

void SavedVariable::Swap(SavedVariable* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    SavedVariable* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void SavedVariable::UnsafeArenaSwap(SavedVariable* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void SavedVariable::InternalSwap(SavedVariable* other) {
  using std::swap;
  swap(shape_, other->shape_);
  swap(dtype_, other->dtype_);
  swap(trainable_, other->trainable_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata SavedVariable::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void FunctionSpec::InitAsDefaultInstance() {
  ::tensorflow::_FunctionSpec_default_instance_._instance.get_mutable()->fullargspec_ = const_cast< ::tensorflow::StructuredValue*>(
      ::tensorflow::StructuredValue::internal_default_instance());
  ::tensorflow::_FunctionSpec_default_instance_._instance.get_mutable()->args_to_prepend_ = const_cast< ::tensorflow::StructuredValue*>(
      ::tensorflow::StructuredValue::internal_default_instance());
  ::tensorflow::_FunctionSpec_default_instance_._instance.get_mutable()->kwargs_to_include_ = const_cast< ::tensorflow::StructuredValue*>(
      ::tensorflow::StructuredValue::internal_default_instance());
  ::tensorflow::_FunctionSpec_default_instance_._instance.get_mutable()->input_signature_ = const_cast< ::tensorflow::StructuredValue*>(
      ::tensorflow::StructuredValue::internal_default_instance());
}
void FunctionSpec::unsafe_arena_set_allocated_fullargspec(
    ::tensorflow::StructuredValue* fullargspec) {
  if (GetArenaNoVirtual() == NULL) {
    delete fullargspec_;
  }
  fullargspec_ = fullargspec;
  if (fullargspec) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.FunctionSpec.fullargspec)
}
void FunctionSpec::clear_fullargspec() {
  if (GetArenaNoVirtual() == NULL && fullargspec_ != NULL) {
    delete fullargspec_;
  }
  fullargspec_ = NULL;
}
void FunctionSpec::unsafe_arena_set_allocated_args_to_prepend(
    ::tensorflow::StructuredValue* args_to_prepend) {
  if (GetArenaNoVirtual() == NULL) {
    delete args_to_prepend_;
  }
  args_to_prepend_ = args_to_prepend;
  if (args_to_prepend) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.FunctionSpec.args_to_prepend)
}
void FunctionSpec::clear_args_to_prepend() {
  if (GetArenaNoVirtual() == NULL && args_to_prepend_ != NULL) {
    delete args_to_prepend_;
  }
  args_to_prepend_ = NULL;
}
void FunctionSpec::unsafe_arena_set_allocated_kwargs_to_include(
    ::tensorflow::StructuredValue* kwargs_to_include) {
  if (GetArenaNoVirtual() == NULL) {
    delete kwargs_to_include_;
  }
  kwargs_to_include_ = kwargs_to_include;
  if (kwargs_to_include) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.FunctionSpec.kwargs_to_include)
}
void FunctionSpec::clear_kwargs_to_include() {
  if (GetArenaNoVirtual() == NULL && kwargs_to_include_ != NULL) {
    delete kwargs_to_include_;
  }
  kwargs_to_include_ = NULL;
}
void FunctionSpec::unsafe_arena_set_allocated_input_signature(
    ::tensorflow::StructuredValue* input_signature) {
  if (GetArenaNoVirtual() == NULL) {
    delete input_signature_;
  }
  input_signature_ = input_signature;
  if (input_signature) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.FunctionSpec.input_signature)
}
void FunctionSpec::clear_input_signature() {
  if (GetArenaNoVirtual() == NULL && input_signature_ != NULL) {
    delete input_signature_;
  }
  input_signature_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int FunctionSpec::kFullargspecFieldNumber;
const int FunctionSpec::kIsMethodFieldNumber;
const int FunctionSpec::kArgsToPrependFieldNumber;
const int FunctionSpec::kKwargsToIncludeFieldNumber;
const int FunctionSpec::kInputSignatureFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

FunctionSpec::FunctionSpec()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_FunctionSpec.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.FunctionSpec)
}
FunctionSpec::FunctionSpec(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_FunctionSpec.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.FunctionSpec)
}
FunctionSpec::FunctionSpec(const FunctionSpec& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_fullargspec()) {
    fullargspec_ = new ::tensorflow::StructuredValue(*from.fullargspec_);
  } else {
    fullargspec_ = NULL;
  }
  if (from.has_args_to_prepend()) {
    args_to_prepend_ = new ::tensorflow::StructuredValue(*from.args_to_prepend_);
  } else {
    args_to_prepend_ = NULL;
  }
  if (from.has_kwargs_to_include()) {
    kwargs_to_include_ = new ::tensorflow::StructuredValue(*from.kwargs_to_include_);
  } else {
    kwargs_to_include_ = NULL;
  }
  if (from.has_input_signature()) {
    input_signature_ = new ::tensorflow::StructuredValue(*from.input_signature_);
  } else {
    input_signature_ = NULL;
  }
  is_method_ = from.is_method_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.FunctionSpec)
}

void FunctionSpec::SharedCtor() {
  ::memset(&fullargspec_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&is_method_) -
      reinterpret_cast<char*>(&fullargspec_)) + sizeof(is_method_));
}

FunctionSpec::~FunctionSpec() {
  // @@protoc_insertion_point(destructor:tensorflow.FunctionSpec)
  SharedDtor();
}

void FunctionSpec::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (this != internal_default_instance()) delete fullargspec_;
  if (this != internal_default_instance()) delete args_to_prepend_;
  if (this != internal_default_instance()) delete kwargs_to_include_;
  if (this != internal_default_instance()) delete input_signature_;
}

void FunctionSpec::ArenaDtor(void* object) {
  FunctionSpec* _this = reinterpret_cast< FunctionSpec* >(object);
  (void)_this;
}
void FunctionSpec::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void FunctionSpec::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* FunctionSpec::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const FunctionSpec& FunctionSpec::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_FunctionSpec.base);
  return *internal_default_instance();
}


void FunctionSpec::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.FunctionSpec)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && fullargspec_ != NULL) {
    delete fullargspec_;
  }
  fullargspec_ = NULL;
  if (GetArenaNoVirtual() == NULL && args_to_prepend_ != NULL) {
    delete args_to_prepend_;
  }
  args_to_prepend_ = NULL;
  if (GetArenaNoVirtual() == NULL && kwargs_to_include_ != NULL) {
    delete kwargs_to_include_;
  }
  kwargs_to_include_ = NULL;
  if (GetArenaNoVirtual() == NULL && input_signature_ != NULL) {
    delete input_signature_;
  }
  input_signature_ = NULL;
  is_method_ = false;
  _internal_metadata_.Clear();
}

bool FunctionSpec::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.FunctionSpec)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.StructuredValue fullargspec = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_fullargspec()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool is_method = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &is_method_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.StructuredValue args_to_prepend = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_args_to_prepend()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.StructuredValue kwargs_to_include = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_kwargs_to_include()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.StructuredValue input_signature = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_input_signature()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.FunctionSpec)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.FunctionSpec)
  return false;
#undef DO_
}

void FunctionSpec::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.FunctionSpec)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.StructuredValue fullargspec = 1;
  if (this->has_fullargspec()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->_internal_fullargspec(), output);
  }

  // bool is_method = 2;
  if (this->is_method() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(2, this->is_method(), output);
  }

  // .tensorflow.StructuredValue args_to_prepend = 3;
  if (this->has_args_to_prepend()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->_internal_args_to_prepend(), output);
  }

  // .tensorflow.StructuredValue kwargs_to_include = 4;
  if (this->has_kwargs_to_include()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->_internal_kwargs_to_include(), output);
  }

  // .tensorflow.StructuredValue input_signature = 5;
  if (this->has_input_signature()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, this->_internal_input_signature(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.FunctionSpec)
}

::google::protobuf::uint8* FunctionSpec::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.FunctionSpec)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.StructuredValue fullargspec = 1;
  if (this->has_fullargspec()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->_internal_fullargspec(), deterministic, target);
  }

  // bool is_method = 2;
  if (this->is_method() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(2, this->is_method(), target);
  }

  // .tensorflow.StructuredValue args_to_prepend = 3;
  if (this->has_args_to_prepend()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->_internal_args_to_prepend(), deterministic, target);
  }

  // .tensorflow.StructuredValue kwargs_to_include = 4;
  if (this->has_kwargs_to_include()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->_internal_kwargs_to_include(), deterministic, target);
  }

  // .tensorflow.StructuredValue input_signature = 5;
  if (this->has_input_signature()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        5, this->_internal_input_signature(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.FunctionSpec)
  return target;
}

size_t FunctionSpec::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.FunctionSpec)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .tensorflow.StructuredValue fullargspec = 1;
  if (this->has_fullargspec()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *fullargspec_);
  }

  // .tensorflow.StructuredValue args_to_prepend = 3;
  if (this->has_args_to_prepend()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *args_to_prepend_);
  }

  // .tensorflow.StructuredValue kwargs_to_include = 4;
  if (this->has_kwargs_to_include()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *kwargs_to_include_);
  }

  // .tensorflow.StructuredValue input_signature = 5;
  if (this->has_input_signature()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *input_signature_);
  }

  // bool is_method = 2;
  if (this->is_method() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void FunctionSpec::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.FunctionSpec)
  GOOGLE_DCHECK_NE(&from, this);
  const FunctionSpec* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const FunctionSpec>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.FunctionSpec)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.FunctionSpec)
    MergeFrom(*source);
  }
}

void FunctionSpec::MergeFrom(const FunctionSpec& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.FunctionSpec)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_fullargspec()) {
    mutable_fullargspec()->::tensorflow::StructuredValue::MergeFrom(from.fullargspec());
  }
  if (from.has_args_to_prepend()) {
    mutable_args_to_prepend()->::tensorflow::StructuredValue::MergeFrom(from.args_to_prepend());
  }
  if (from.has_kwargs_to_include()) {
    mutable_kwargs_to_include()->::tensorflow::StructuredValue::MergeFrom(from.kwargs_to_include());
  }
  if (from.has_input_signature()) {
    mutable_input_signature()->::tensorflow::StructuredValue::MergeFrom(from.input_signature());
  }
  if (from.is_method() != 0) {
    set_is_method(from.is_method());
  }
}

void FunctionSpec::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.FunctionSpec)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FunctionSpec::CopyFrom(const FunctionSpec& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.FunctionSpec)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FunctionSpec::IsInitialized() const {
  return true;
}

void FunctionSpec::Swap(FunctionSpec* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    FunctionSpec* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void FunctionSpec::UnsafeArenaSwap(FunctionSpec* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void FunctionSpec::InternalSwap(FunctionSpec* other) {
  using std::swap;
  swap(fullargspec_, other->fullargspec_);
  swap(args_to_prepend_, other->args_to_prepend_);
  swap(kwargs_to_include_, other->kwargs_to_include_);
  swap(input_signature_, other->input_signature_);
  swap(is_method_, other->is_method_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata FunctionSpec::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void SavedResource::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SavedResource::SavedResource()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedResource.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.SavedResource)
}
SavedResource::SavedResource(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedResource.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.SavedResource)
}
SavedResource::SavedResource(const SavedResource& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.SavedResource)
}

void SavedResource::SharedCtor() {
}

SavedResource::~SavedResource() {
  // @@protoc_insertion_point(destructor:tensorflow.SavedResource)
  SharedDtor();
}

void SavedResource::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void SavedResource::ArenaDtor(void* object) {
  SavedResource* _this = reinterpret_cast< SavedResource* >(object);
  (void)_this;
}
void SavedResource::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void SavedResource::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* SavedResource::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const SavedResource& SavedResource::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedResource.base);
  return *internal_default_instance();
}


void SavedResource::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.SavedResource)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_metadata_.Clear();
}

bool SavedResource::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.SavedResource)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, _internal_metadata_.mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.SavedResource)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.SavedResource)
  return false;
#undef DO_
}

void SavedResource::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.SavedResource)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.SavedResource)
}

::google::protobuf::uint8* SavedResource::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.SavedResource)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.SavedResource)
  return target;
}

size_t SavedResource::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.SavedResource)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SavedResource::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.SavedResource)
  GOOGLE_DCHECK_NE(&from, this);
  const SavedResource* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SavedResource>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.SavedResource)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.SavedResource)
    MergeFrom(*source);
  }
}

void SavedResource::MergeFrom(const SavedResource& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.SavedResource)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

}

void SavedResource::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.SavedResource)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SavedResource::CopyFrom(const SavedResource& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.SavedResource)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SavedResource::IsInitialized() const {
  return true;
}

void SavedResource::Swap(SavedResource* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    SavedResource* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void SavedResource::UnsafeArenaSwap(SavedResource* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void SavedResource::InternalSwap(SavedResource* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata SavedResource::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::SavedObjectGraph* Arena::CreateMaybeMessage< ::tensorflow::SavedObjectGraph >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::SavedObjectGraph >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::SavedObject* Arena::CreateMaybeMessage< ::tensorflow::SavedObject >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::SavedObject >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::SavedUserObject* Arena::CreateMaybeMessage< ::tensorflow::SavedUserObject >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::SavedUserObject >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::SavedAsset* Arena::CreateMaybeMessage< ::tensorflow::SavedAsset >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::SavedAsset >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::SavedFunction* Arena::CreateMaybeMessage< ::tensorflow::SavedFunction >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::SavedFunction >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::SavedConcreteFunction* Arena::CreateMaybeMessage< ::tensorflow::SavedConcreteFunction >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::SavedConcreteFunction >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::SavedBareConcreteFunction* Arena::CreateMaybeMessage< ::tensorflow::SavedBareConcreteFunction >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::SavedBareConcreteFunction >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::SavedConstant* Arena::CreateMaybeMessage< ::tensorflow::SavedConstant >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::SavedConstant >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::SavedVariable* Arena::CreateMaybeMessage< ::tensorflow::SavedVariable >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::SavedVariable >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::FunctionSpec* Arena::CreateMaybeMessage< ::tensorflow::FunctionSpec >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::FunctionSpec >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::SavedResource* Arena::CreateMaybeMessage< ::tensorflow::SavedResource >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::SavedResource >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

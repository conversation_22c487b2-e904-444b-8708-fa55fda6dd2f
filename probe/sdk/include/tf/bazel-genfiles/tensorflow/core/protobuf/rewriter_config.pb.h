// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/rewriter_config.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/attr_value.pb.h"
#include "tensorflow/core/protobuf/verifier_config.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto 

namespace protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[5];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto
namespace tensorflow {
class AutoParallelOptions;
class AutoParallelOptionsDefaultTypeInternal;
extern AutoParallelOptionsDefaultTypeInternal _AutoParallelOptions_default_instance_;
class RewriterConfig;
class RewriterConfigDefaultTypeInternal;
extern RewriterConfigDefaultTypeInternal _RewriterConfig_default_instance_;
class RewriterConfig_CustomGraphOptimizer;
class RewriterConfig_CustomGraphOptimizerDefaultTypeInternal;
extern RewriterConfig_CustomGraphOptimizerDefaultTypeInternal _RewriterConfig_CustomGraphOptimizer_default_instance_;
class RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse;
class RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUseDefaultTypeInternal;
extern RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUseDefaultTypeInternal _RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse_default_instance_;
class ScopedAllocatorOptions;
class ScopedAllocatorOptionsDefaultTypeInternal;
extern ScopedAllocatorOptionsDefaultTypeInternal _ScopedAllocatorOptions_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::AutoParallelOptions* Arena::CreateMaybeMessage<::tensorflow::AutoParallelOptions>(Arena*);
template<> ::tensorflow::RewriterConfig* Arena::CreateMaybeMessage<::tensorflow::RewriterConfig>(Arena*);
template<> ::tensorflow::RewriterConfig_CustomGraphOptimizer* Arena::CreateMaybeMessage<::tensorflow::RewriterConfig_CustomGraphOptimizer>(Arena*);
template<> ::tensorflow::RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse>(Arena*);
template<> ::tensorflow::ScopedAllocatorOptions* Arena::CreateMaybeMessage<::tensorflow::ScopedAllocatorOptions>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

enum RewriterConfig_Toggle {
  RewriterConfig_Toggle_DEFAULT = 0,
  RewriterConfig_Toggle_ON = 1,
  RewriterConfig_Toggle_OFF = 2,
  RewriterConfig_Toggle_AGGRESSIVE = 3,
  RewriterConfig_Toggle_RewriterConfig_Toggle_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  RewriterConfig_Toggle_RewriterConfig_Toggle_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool RewriterConfig_Toggle_IsValid(int value);
const RewriterConfig_Toggle RewriterConfig_Toggle_Toggle_MIN = RewriterConfig_Toggle_DEFAULT;
const RewriterConfig_Toggle RewriterConfig_Toggle_Toggle_MAX = RewriterConfig_Toggle_AGGRESSIVE;
const int RewriterConfig_Toggle_Toggle_ARRAYSIZE = RewriterConfig_Toggle_Toggle_MAX + 1;

const ::google::protobuf::EnumDescriptor* RewriterConfig_Toggle_descriptor();
inline const ::std::string& RewriterConfig_Toggle_Name(RewriterConfig_Toggle value) {
  return ::google::protobuf::internal::NameOfEnum(
    RewriterConfig_Toggle_descriptor(), value);
}
inline bool RewriterConfig_Toggle_Parse(
    const ::std::string& name, RewriterConfig_Toggle* value) {
  return ::google::protobuf::internal::ParseNamedEnum<RewriterConfig_Toggle>(
    RewriterConfig_Toggle_descriptor(), name, value);
}
enum RewriterConfig_NumIterationsType {
  RewriterConfig_NumIterationsType_DEFAULT_NUM_ITERS = 0,
  RewriterConfig_NumIterationsType_ONE = 1,
  RewriterConfig_NumIterationsType_TWO = 2,
  RewriterConfig_NumIterationsType_RewriterConfig_NumIterationsType_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  RewriterConfig_NumIterationsType_RewriterConfig_NumIterationsType_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool RewriterConfig_NumIterationsType_IsValid(int value);
const RewriterConfig_NumIterationsType RewriterConfig_NumIterationsType_NumIterationsType_MIN = RewriterConfig_NumIterationsType_DEFAULT_NUM_ITERS;
const RewriterConfig_NumIterationsType RewriterConfig_NumIterationsType_NumIterationsType_MAX = RewriterConfig_NumIterationsType_TWO;
const int RewriterConfig_NumIterationsType_NumIterationsType_ARRAYSIZE = RewriterConfig_NumIterationsType_NumIterationsType_MAX + 1;

const ::google::protobuf::EnumDescriptor* RewriterConfig_NumIterationsType_descriptor();
inline const ::std::string& RewriterConfig_NumIterationsType_Name(RewriterConfig_NumIterationsType value) {
  return ::google::protobuf::internal::NameOfEnum(
    RewriterConfig_NumIterationsType_descriptor(), value);
}
inline bool RewriterConfig_NumIterationsType_Parse(
    const ::std::string& name, RewriterConfig_NumIterationsType* value) {
  return ::google::protobuf::internal::ParseNamedEnum<RewriterConfig_NumIterationsType>(
    RewriterConfig_NumIterationsType_descriptor(), name, value);
}
enum RewriterConfig_MemOptType {
  RewriterConfig_MemOptType_DEFAULT_MEM_OPT = 0,
  RewriterConfig_MemOptType_NO_MEM_OPT = 1,
  RewriterConfig_MemOptType_MANUAL = 2,
  RewriterConfig_MemOptType_SWAPPING_HEURISTICS = 4,
  RewriterConfig_MemOptType_RECOMPUTATION_HEURISTICS = 5,
  RewriterConfig_MemOptType_SCHEDULING_HEURISTICS = 6,
  RewriterConfig_MemOptType_HEURISTICS = 3,
  RewriterConfig_MemOptType_RewriterConfig_MemOptType_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  RewriterConfig_MemOptType_RewriterConfig_MemOptType_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool RewriterConfig_MemOptType_IsValid(int value);
const RewriterConfig_MemOptType RewriterConfig_MemOptType_MemOptType_MIN = RewriterConfig_MemOptType_DEFAULT_MEM_OPT;
const RewriterConfig_MemOptType RewriterConfig_MemOptType_MemOptType_MAX = RewriterConfig_MemOptType_SCHEDULING_HEURISTICS;
const int RewriterConfig_MemOptType_MemOptType_ARRAYSIZE = RewriterConfig_MemOptType_MemOptType_MAX + 1;

const ::google::protobuf::EnumDescriptor* RewriterConfig_MemOptType_descriptor();
inline const ::std::string& RewriterConfig_MemOptType_Name(RewriterConfig_MemOptType value) {
  return ::google::protobuf::internal::NameOfEnum(
    RewriterConfig_MemOptType_descriptor(), value);
}
inline bool RewriterConfig_MemOptType_Parse(
    const ::std::string& name, RewriterConfig_MemOptType* value) {
  return ::google::protobuf::internal::ParseNamedEnum<RewriterConfig_MemOptType>(
    RewriterConfig_MemOptType_descriptor(), name, value);
}
// ===================================================================

class AutoParallelOptions : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.AutoParallelOptions) */ {
 public:
  AutoParallelOptions();
  virtual ~AutoParallelOptions();

  AutoParallelOptions(const AutoParallelOptions& from);

  inline AutoParallelOptions& operator=(const AutoParallelOptions& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  AutoParallelOptions(AutoParallelOptions&& from) noexcept
    : AutoParallelOptions() {
    *this = ::std::move(from);
  }

  inline AutoParallelOptions& operator=(AutoParallelOptions&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const AutoParallelOptions& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const AutoParallelOptions* internal_default_instance() {
    return reinterpret_cast<const AutoParallelOptions*>(
               &_AutoParallelOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(AutoParallelOptions* other);
  void Swap(AutoParallelOptions* other);
  friend void swap(AutoParallelOptions& a, AutoParallelOptions& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline AutoParallelOptions* New() const final {
    return CreateMaybeMessage<AutoParallelOptions>(NULL);
  }

  AutoParallelOptions* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<AutoParallelOptions>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const AutoParallelOptions& from);
  void MergeFrom(const AutoParallelOptions& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AutoParallelOptions* other);
  protected:
  explicit AutoParallelOptions(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // bool enable = 1;
  void clear_enable();
  static const int kEnableFieldNumber = 1;
  bool enable() const;
  void set_enable(bool value);

  // int32 num_replicas = 2;
  void clear_num_replicas();
  static const int kNumReplicasFieldNumber = 2;
  ::google::protobuf::int32 num_replicas() const;
  void set_num_replicas(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.AutoParallelOptions)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  bool enable_;
  ::google::protobuf::int32 num_replicas_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ScopedAllocatorOptions : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.ScopedAllocatorOptions) */ {
 public:
  ScopedAllocatorOptions();
  virtual ~ScopedAllocatorOptions();

  ScopedAllocatorOptions(const ScopedAllocatorOptions& from);

  inline ScopedAllocatorOptions& operator=(const ScopedAllocatorOptions& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ScopedAllocatorOptions(ScopedAllocatorOptions&& from) noexcept
    : ScopedAllocatorOptions() {
    *this = ::std::move(from);
  }

  inline ScopedAllocatorOptions& operator=(ScopedAllocatorOptions&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const ScopedAllocatorOptions& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ScopedAllocatorOptions* internal_default_instance() {
    return reinterpret_cast<const ScopedAllocatorOptions*>(
               &_ScopedAllocatorOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void UnsafeArenaSwap(ScopedAllocatorOptions* other);
  void Swap(ScopedAllocatorOptions* other);
  friend void swap(ScopedAllocatorOptions& a, ScopedAllocatorOptions& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ScopedAllocatorOptions* New() const final {
    return CreateMaybeMessage<ScopedAllocatorOptions>(NULL);
  }

  ScopedAllocatorOptions* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ScopedAllocatorOptions>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ScopedAllocatorOptions& from);
  void MergeFrom(const ScopedAllocatorOptions& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ScopedAllocatorOptions* other);
  protected:
  explicit ScopedAllocatorOptions(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string enable_op = 1;
  int enable_op_size() const;
  void clear_enable_op();
  static const int kEnableOpFieldNumber = 1;
  const ::std::string& enable_op(int index) const;
  ::std::string* mutable_enable_op(int index);
  void set_enable_op(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_enable_op(int index, ::std::string&& value);
  #endif
  void set_enable_op(int index, const char* value);
  void set_enable_op(int index, const char* value, size_t size);
  ::std::string* add_enable_op();
  void add_enable_op(const ::std::string& value);
  #if LANG_CXX11
  void add_enable_op(::std::string&& value);
  #endif
  void add_enable_op(const char* value);
  void add_enable_op(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& enable_op() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_enable_op();

  // @@protoc_insertion_point(class_scope:tensorflow.ScopedAllocatorOptions)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::std::string> enable_op_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse, 
    ::std::string, ::tensorflow::AttrValue,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse, 
    ::std::string, ::tensorflow::AttrValue,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse();
  RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse& other);
  static const RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse*>(&_RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class RewriterConfig_CustomGraphOptimizer : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.RewriterConfig.CustomGraphOptimizer) */ {
 public:
  RewriterConfig_CustomGraphOptimizer();
  virtual ~RewriterConfig_CustomGraphOptimizer();

  RewriterConfig_CustomGraphOptimizer(const RewriterConfig_CustomGraphOptimizer& from);

  inline RewriterConfig_CustomGraphOptimizer& operator=(const RewriterConfig_CustomGraphOptimizer& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  RewriterConfig_CustomGraphOptimizer(RewriterConfig_CustomGraphOptimizer&& from) noexcept
    : RewriterConfig_CustomGraphOptimizer() {
    *this = ::std::move(from);
  }

  inline RewriterConfig_CustomGraphOptimizer& operator=(RewriterConfig_CustomGraphOptimizer&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const RewriterConfig_CustomGraphOptimizer& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RewriterConfig_CustomGraphOptimizer* internal_default_instance() {
    return reinterpret_cast<const RewriterConfig_CustomGraphOptimizer*>(
               &_RewriterConfig_CustomGraphOptimizer_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void UnsafeArenaSwap(RewriterConfig_CustomGraphOptimizer* other);
  void Swap(RewriterConfig_CustomGraphOptimizer* other);
  friend void swap(RewriterConfig_CustomGraphOptimizer& a, RewriterConfig_CustomGraphOptimizer& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RewriterConfig_CustomGraphOptimizer* New() const final {
    return CreateMaybeMessage<RewriterConfig_CustomGraphOptimizer>(NULL);
  }

  RewriterConfig_CustomGraphOptimizer* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<RewriterConfig_CustomGraphOptimizer>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const RewriterConfig_CustomGraphOptimizer& from);
  void MergeFrom(const RewriterConfig_CustomGraphOptimizer& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RewriterConfig_CustomGraphOptimizer* other);
  protected:
  explicit RewriterConfig_CustomGraphOptimizer(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<string, .tensorflow.AttrValue> parameter_map = 2;
  int parameter_map_size() const;
  void clear_parameter_map();
  static const int kParameterMapFieldNumber = 2;
  const ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >&
      parameter_map() const;
  ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >*
      mutable_parameter_map();

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      ::std::string* name);

  // @@protoc_insertion_point(class_scope:tensorflow.RewriterConfig.CustomGraphOptimizer)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::MapField<
      RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse,
      ::std::string, ::tensorflow::AttrValue,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > parameter_map_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class RewriterConfig : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.RewriterConfig) */ {
 public:
  RewriterConfig();
  virtual ~RewriterConfig();

  RewriterConfig(const RewriterConfig& from);

  inline RewriterConfig& operator=(const RewriterConfig& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  RewriterConfig(RewriterConfig&& from) noexcept
    : RewriterConfig() {
    *this = ::std::move(from);
  }

  inline RewriterConfig& operator=(RewriterConfig&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const RewriterConfig& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RewriterConfig* internal_default_instance() {
    return reinterpret_cast<const RewriterConfig*>(
               &_RewriterConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void UnsafeArenaSwap(RewriterConfig* other);
  void Swap(RewriterConfig* other);
  friend void swap(RewriterConfig& a, RewriterConfig& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RewriterConfig* New() const final {
    return CreateMaybeMessage<RewriterConfig>(NULL);
  }

  RewriterConfig* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<RewriterConfig>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const RewriterConfig& from);
  void MergeFrom(const RewriterConfig& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RewriterConfig* other);
  protected:
  explicit RewriterConfig(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef RewriterConfig_CustomGraphOptimizer CustomGraphOptimizer;

  typedef RewriterConfig_Toggle Toggle;
  static const Toggle DEFAULT =
    RewriterConfig_Toggle_DEFAULT;
  static const Toggle ON =
    RewriterConfig_Toggle_ON;
  static const Toggle OFF =
    RewriterConfig_Toggle_OFF;
  static const Toggle AGGRESSIVE =
    RewriterConfig_Toggle_AGGRESSIVE;
  static inline bool Toggle_IsValid(int value) {
    return RewriterConfig_Toggle_IsValid(value);
  }
  static const Toggle Toggle_MIN =
    RewriterConfig_Toggle_Toggle_MIN;
  static const Toggle Toggle_MAX =
    RewriterConfig_Toggle_Toggle_MAX;
  static const int Toggle_ARRAYSIZE =
    RewriterConfig_Toggle_Toggle_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  Toggle_descriptor() {
    return RewriterConfig_Toggle_descriptor();
  }
  static inline const ::std::string& Toggle_Name(Toggle value) {
    return RewriterConfig_Toggle_Name(value);
  }
  static inline bool Toggle_Parse(const ::std::string& name,
      Toggle* value) {
    return RewriterConfig_Toggle_Parse(name, value);
  }

  typedef RewriterConfig_NumIterationsType NumIterationsType;
  static const NumIterationsType DEFAULT_NUM_ITERS =
    RewriterConfig_NumIterationsType_DEFAULT_NUM_ITERS;
  static const NumIterationsType ONE =
    RewriterConfig_NumIterationsType_ONE;
  static const NumIterationsType TWO =
    RewriterConfig_NumIterationsType_TWO;
  static inline bool NumIterationsType_IsValid(int value) {
    return RewriterConfig_NumIterationsType_IsValid(value);
  }
  static const NumIterationsType NumIterationsType_MIN =
    RewriterConfig_NumIterationsType_NumIterationsType_MIN;
  static const NumIterationsType NumIterationsType_MAX =
    RewriterConfig_NumIterationsType_NumIterationsType_MAX;
  static const int NumIterationsType_ARRAYSIZE =
    RewriterConfig_NumIterationsType_NumIterationsType_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  NumIterationsType_descriptor() {
    return RewriterConfig_NumIterationsType_descriptor();
  }
  static inline const ::std::string& NumIterationsType_Name(NumIterationsType value) {
    return RewriterConfig_NumIterationsType_Name(value);
  }
  static inline bool NumIterationsType_Parse(const ::std::string& name,
      NumIterationsType* value) {
    return RewriterConfig_NumIterationsType_Parse(name, value);
  }

  typedef RewriterConfig_MemOptType MemOptType;
  static const MemOptType DEFAULT_MEM_OPT =
    RewriterConfig_MemOptType_DEFAULT_MEM_OPT;
  static const MemOptType NO_MEM_OPT =
    RewriterConfig_MemOptType_NO_MEM_OPT;
  static const MemOptType MANUAL =
    RewriterConfig_MemOptType_MANUAL;
  static const MemOptType SWAPPING_HEURISTICS =
    RewriterConfig_MemOptType_SWAPPING_HEURISTICS;
  static const MemOptType RECOMPUTATION_HEURISTICS =
    RewriterConfig_MemOptType_RECOMPUTATION_HEURISTICS;
  static const MemOptType SCHEDULING_HEURISTICS =
    RewriterConfig_MemOptType_SCHEDULING_HEURISTICS;
  static const MemOptType HEURISTICS =
    RewriterConfig_MemOptType_HEURISTICS;
  static inline bool MemOptType_IsValid(int value) {
    return RewriterConfig_MemOptType_IsValid(value);
  }
  static const MemOptType MemOptType_MIN =
    RewriterConfig_MemOptType_MemOptType_MIN;
  static const MemOptType MemOptType_MAX =
    RewriterConfig_MemOptType_MemOptType_MAX;
  static const int MemOptType_ARRAYSIZE =
    RewriterConfig_MemOptType_MemOptType_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  MemOptType_descriptor() {
    return RewriterConfig_MemOptType_descriptor();
  }
  static inline const ::std::string& MemOptType_Name(MemOptType value) {
    return RewriterConfig_MemOptType_Name(value);
  }
  static inline bool MemOptType_Parse(const ::std::string& name,
      MemOptType* value) {
    return RewriterConfig_MemOptType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // repeated string optimizers = 100;
  int optimizers_size() const;
  void clear_optimizers();
  static const int kOptimizersFieldNumber = 100;
  const ::std::string& optimizers(int index) const;
  ::std::string* mutable_optimizers(int index);
  void set_optimizers(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_optimizers(int index, ::std::string&& value);
  #endif
  void set_optimizers(int index, const char* value);
  void set_optimizers(int index, const char* value, size_t size);
  ::std::string* add_optimizers();
  void add_optimizers(const ::std::string& value);
  #if LANG_CXX11
  void add_optimizers(::std::string&& value);
  #endif
  void add_optimizers(const char* value);
  void add_optimizers(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& optimizers() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_optimizers();

  // repeated .tensorflow.RewriterConfig.CustomGraphOptimizer custom_optimizers = 200;
  int custom_optimizers_size() const;
  void clear_custom_optimizers();
  static const int kCustomOptimizersFieldNumber = 200;
  ::tensorflow::RewriterConfig_CustomGraphOptimizer* mutable_custom_optimizers(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::RewriterConfig_CustomGraphOptimizer >*
      mutable_custom_optimizers();
  const ::tensorflow::RewriterConfig_CustomGraphOptimizer& custom_optimizers(int index) const;
  ::tensorflow::RewriterConfig_CustomGraphOptimizer* add_custom_optimizers();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::RewriterConfig_CustomGraphOptimizer >&
      custom_optimizers() const;

  // string memory_optimizer_target_node_name_scope = 6;
  void clear_memory_optimizer_target_node_name_scope();
  static const int kMemoryOptimizerTargetNodeNameScopeFieldNumber = 6;
  const ::std::string& memory_optimizer_target_node_name_scope() const;
  void set_memory_optimizer_target_node_name_scope(const ::std::string& value);
  #if LANG_CXX11
  void set_memory_optimizer_target_node_name_scope(::std::string&& value);
  #endif
  void set_memory_optimizer_target_node_name_scope(const char* value);
  void set_memory_optimizer_target_node_name_scope(const char* value, size_t size);
  ::std::string* mutable_memory_optimizer_target_node_name_scope();
  ::std::string* release_memory_optimizer_target_node_name_scope();
  void set_allocated_memory_optimizer_target_node_name_scope(::std::string* memory_optimizer_target_node_name_scope);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_memory_optimizer_target_node_name_scope();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_memory_optimizer_target_node_name_scope(
      ::std::string* memory_optimizer_target_node_name_scope);

  // .tensorflow.AutoParallelOptions auto_parallel = 5;
  bool has_auto_parallel() const;
  void clear_auto_parallel();
  static const int kAutoParallelFieldNumber = 5;
  private:
  const ::tensorflow::AutoParallelOptions& _internal_auto_parallel() const;
  public:
  const ::tensorflow::AutoParallelOptions& auto_parallel() const;
  ::tensorflow::AutoParallelOptions* release_auto_parallel();
  ::tensorflow::AutoParallelOptions* mutable_auto_parallel();
  void set_allocated_auto_parallel(::tensorflow::AutoParallelOptions* auto_parallel);
  void unsafe_arena_set_allocated_auto_parallel(
      ::tensorflow::AutoParallelOptions* auto_parallel);
  ::tensorflow::AutoParallelOptions* unsafe_arena_release_auto_parallel();

  // .tensorflow.ScopedAllocatorOptions scoped_allocator_opts = 16;
  bool has_scoped_allocator_opts() const;
  void clear_scoped_allocator_opts();
  static const int kScopedAllocatorOptsFieldNumber = 16;
  private:
  const ::tensorflow::ScopedAllocatorOptions& _internal_scoped_allocator_opts() const;
  public:
  const ::tensorflow::ScopedAllocatorOptions& scoped_allocator_opts() const;
  ::tensorflow::ScopedAllocatorOptions* release_scoped_allocator_opts();
  ::tensorflow::ScopedAllocatorOptions* mutable_scoped_allocator_opts();
  void set_allocated_scoped_allocator_opts(::tensorflow::ScopedAllocatorOptions* scoped_allocator_opts);
  void unsafe_arena_set_allocated_scoped_allocator_opts(
      ::tensorflow::ScopedAllocatorOptions* scoped_allocator_opts);
  ::tensorflow::ScopedAllocatorOptions* unsafe_arena_release_scoped_allocator_opts();

  // .tensorflow.VerifierConfig inter_optimizer_verifier_config = 300;
  bool has_inter_optimizer_verifier_config() const;
  void clear_inter_optimizer_verifier_config();
  static const int kInterOptimizerVerifierConfigFieldNumber = 300;
  private:
  const ::tensorflow::VerifierConfig& _internal_inter_optimizer_verifier_config() const;
  public:
  const ::tensorflow::VerifierConfig& inter_optimizer_verifier_config() const;
  ::tensorflow::VerifierConfig* release_inter_optimizer_verifier_config();
  ::tensorflow::VerifierConfig* mutable_inter_optimizer_verifier_config();
  void set_allocated_inter_optimizer_verifier_config(::tensorflow::VerifierConfig* inter_optimizer_verifier_config);
  void unsafe_arena_set_allocated_inter_optimizer_verifier_config(
      ::tensorflow::VerifierConfig* inter_optimizer_verifier_config);
  ::tensorflow::VerifierConfig* unsafe_arena_release_inter_optimizer_verifier_config();

  // .tensorflow.VerifierConfig post_optimization_verifier_config = 301;
  bool has_post_optimization_verifier_config() const;
  void clear_post_optimization_verifier_config();
  static const int kPostOptimizationVerifierConfigFieldNumber = 301;
  private:
  const ::tensorflow::VerifierConfig& _internal_post_optimization_verifier_config() const;
  public:
  const ::tensorflow::VerifierConfig& post_optimization_verifier_config() const;
  ::tensorflow::VerifierConfig* release_post_optimization_verifier_config();
  ::tensorflow::VerifierConfig* mutable_post_optimization_verifier_config();
  void set_allocated_post_optimization_verifier_config(::tensorflow::VerifierConfig* post_optimization_verifier_config);
  void unsafe_arena_set_allocated_post_optimization_verifier_config(
      ::tensorflow::VerifierConfig* post_optimization_verifier_config);
  ::tensorflow::VerifierConfig* unsafe_arena_release_post_optimization_verifier_config();

  // .tensorflow.RewriterConfig.Toggle layout_optimizer = 1;
  void clear_layout_optimizer();
  static const int kLayoutOptimizerFieldNumber = 1;
  ::tensorflow::RewriterConfig_Toggle layout_optimizer() const;
  void set_layout_optimizer(::tensorflow::RewriterConfig_Toggle value);

  // .tensorflow.RewriterConfig.Toggle constant_folding = 3;
  void clear_constant_folding();
  static const int kConstantFoldingFieldNumber = 3;
  ::tensorflow::RewriterConfig_Toggle constant_folding() const;
  void set_constant_folding(::tensorflow::RewriterConfig_Toggle value);

  // .tensorflow.RewriterConfig.MemOptType memory_optimization = 4;
  void clear_memory_optimization();
  static const int kMemoryOptimizationFieldNumber = 4;
  ::tensorflow::RewriterConfig_MemOptType memory_optimization() const;
  void set_memory_optimization(::tensorflow::RewriterConfig_MemOptType value);

  // .tensorflow.RewriterConfig.Toggle arithmetic_optimization = 7;
  void clear_arithmetic_optimization();
  static const int kArithmeticOptimizationFieldNumber = 7;
  ::tensorflow::RewriterConfig_Toggle arithmetic_optimization() const;
  void set_arithmetic_optimization(::tensorflow::RewriterConfig_Toggle value);

  // .tensorflow.RewriterConfig.Toggle dependency_optimization = 8;
  void clear_dependency_optimization();
  static const int kDependencyOptimizationFieldNumber = 8;
  ::tensorflow::RewriterConfig_Toggle dependency_optimization() const;
  void set_dependency_optimization(::tensorflow::RewriterConfig_Toggle value);

  // .tensorflow.RewriterConfig.Toggle loop_optimization = 9;
  void clear_loop_optimization();
  static const int kLoopOptimizationFieldNumber = 9;
  ::tensorflow::RewriterConfig_Toggle loop_optimization() const;
  void set_loop_optimization(::tensorflow::RewriterConfig_Toggle value);

  // .tensorflow.RewriterConfig.Toggle function_optimization = 10;
  void clear_function_optimization();
  static const int kFunctionOptimizationFieldNumber = 10;
  ::tensorflow::RewriterConfig_Toggle function_optimization() const;
  void set_function_optimization(::tensorflow::RewriterConfig_Toggle value);

  // .tensorflow.RewriterConfig.Toggle debug_stripper = 11;
  void clear_debug_stripper();
  static const int kDebugStripperFieldNumber = 11;
  ::tensorflow::RewriterConfig_Toggle debug_stripper() const;
  void set_debug_stripper(::tensorflow::RewriterConfig_Toggle value);

  // .tensorflow.RewriterConfig.NumIterationsType meta_optimizer_iterations = 12;
  void clear_meta_optimizer_iterations();
  static const int kMetaOptimizerIterationsFieldNumber = 12;
  ::tensorflow::RewriterConfig_NumIterationsType meta_optimizer_iterations() const;
  void set_meta_optimizer_iterations(::tensorflow::RewriterConfig_NumIterationsType value);

  // .tensorflow.RewriterConfig.Toggle shape_optimization = 13;
  void clear_shape_optimization();
  static const int kShapeOptimizationFieldNumber = 13;
  ::tensorflow::RewriterConfig_Toggle shape_optimization() const;
  void set_shape_optimization(::tensorflow::RewriterConfig_Toggle value);

  // .tensorflow.RewriterConfig.Toggle remapping = 14;
  void clear_remapping();
  static const int kRemappingFieldNumber = 14;
  ::tensorflow::RewriterConfig_Toggle remapping() const;
  void set_remapping(::tensorflow::RewriterConfig_Toggle value);

  // bool disable_model_pruning = 2;
  void clear_disable_model_pruning();
  static const int kDisableModelPruningFieldNumber = 2;
  bool disable_model_pruning() const;
  void set_disable_model_pruning(bool value);

  // bool disable_meta_optimizer = 19;
  void clear_disable_meta_optimizer();
  static const int kDisableMetaOptimizerFieldNumber = 19;
  bool disable_meta_optimizer() const;
  void set_disable_meta_optimizer(bool value);

  // bool fail_on_optimizer_errors = 21;
  void clear_fail_on_optimizer_errors();
  static const int kFailOnOptimizerErrorsFieldNumber = 21;
  bool fail_on_optimizer_errors() const;
  void set_fail_on_optimizer_errors(bool value);

  // .tensorflow.RewriterConfig.Toggle scoped_allocator_optimization = 15;
  void clear_scoped_allocator_optimization();
  static const int kScopedAllocatorOptimizationFieldNumber = 15;
  ::tensorflow::RewriterConfig_Toggle scoped_allocator_optimization() const;
  void set_scoped_allocator_optimization(::tensorflow::RewriterConfig_Toggle value);

  // int32 min_graph_nodes = 17;
  void clear_min_graph_nodes();
  static const int kMinGraphNodesFieldNumber = 17;
  ::google::protobuf::int32 min_graph_nodes() const;
  void set_min_graph_nodes(::google::protobuf::int32 value);

  // int64 meta_optimizer_timeout_ms = 20;
  void clear_meta_optimizer_timeout_ms();
  static const int kMetaOptimizerTimeoutMsFieldNumber = 20;
  ::google::protobuf::int64 meta_optimizer_timeout_ms() const;
  void set_meta_optimizer_timeout_ms(::google::protobuf::int64 value);

  // .tensorflow.RewriterConfig.Toggle pin_to_host_optimization = 18;
  void clear_pin_to_host_optimization();
  static const int kPinToHostOptimizationFieldNumber = 18;
  ::tensorflow::RewriterConfig_Toggle pin_to_host_optimization() const;
  void set_pin_to_host_optimization(::tensorflow::RewriterConfig_Toggle value);

  // .tensorflow.RewriterConfig.Toggle implementation_selector = 22;
  void clear_implementation_selector();
  static const int kImplementationSelectorFieldNumber = 22;
  ::tensorflow::RewriterConfig_Toggle implementation_selector() const;
  void set_implementation_selector(::tensorflow::RewriterConfig_Toggle value);

  // @@protoc_insertion_point(class_scope:tensorflow.RewriterConfig)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::std::string> optimizers_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::RewriterConfig_CustomGraphOptimizer > custom_optimizers_;
  ::google::protobuf::internal::ArenaStringPtr memory_optimizer_target_node_name_scope_;
  ::tensorflow::AutoParallelOptions* auto_parallel_;
  ::tensorflow::ScopedAllocatorOptions* scoped_allocator_opts_;
  ::tensorflow::VerifierConfig* inter_optimizer_verifier_config_;
  ::tensorflow::VerifierConfig* post_optimization_verifier_config_;
  int layout_optimizer_;
  int constant_folding_;
  int memory_optimization_;
  int arithmetic_optimization_;
  int dependency_optimization_;
  int loop_optimization_;
  int function_optimization_;
  int debug_stripper_;
  int meta_optimizer_iterations_;
  int shape_optimization_;
  int remapping_;
  bool disable_model_pruning_;
  bool disable_meta_optimizer_;
  bool fail_on_optimizer_errors_;
  int scoped_allocator_optimization_;
  ::google::protobuf::int32 min_graph_nodes_;
  ::google::protobuf::int64 meta_optimizer_timeout_ms_;
  int pin_to_host_optimization_;
  int implementation_selector_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// AutoParallelOptions

// bool enable = 1;
inline void AutoParallelOptions::clear_enable() {
  enable_ = false;
}
inline bool AutoParallelOptions::enable() const {
  // @@protoc_insertion_point(field_get:tensorflow.AutoParallelOptions.enable)
  return enable_;
}
inline void AutoParallelOptions::set_enable(bool value) {
  
  enable_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AutoParallelOptions.enable)
}

// int32 num_replicas = 2;
inline void AutoParallelOptions::clear_num_replicas() {
  num_replicas_ = 0;
}
inline ::google::protobuf::int32 AutoParallelOptions::num_replicas() const {
  // @@protoc_insertion_point(field_get:tensorflow.AutoParallelOptions.num_replicas)
  return num_replicas_;
}
inline void AutoParallelOptions::set_num_replicas(::google::protobuf::int32 value) {
  
  num_replicas_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.AutoParallelOptions.num_replicas)
}

// -------------------------------------------------------------------

// ScopedAllocatorOptions

// repeated string enable_op = 1;
inline int ScopedAllocatorOptions::enable_op_size() const {
  return enable_op_.size();
}
inline void ScopedAllocatorOptions::clear_enable_op() {
  enable_op_.Clear();
}
inline const ::std::string& ScopedAllocatorOptions::enable_op(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ScopedAllocatorOptions.enable_op)
  return enable_op_.Get(index);
}
inline ::std::string* ScopedAllocatorOptions::mutable_enable_op(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ScopedAllocatorOptions.enable_op)
  return enable_op_.Mutable(index);
}
inline void ScopedAllocatorOptions::set_enable_op(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.ScopedAllocatorOptions.enable_op)
  enable_op_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void ScopedAllocatorOptions::set_enable_op(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.ScopedAllocatorOptions.enable_op)
  enable_op_.Mutable(index)->assign(std::move(value));
}
#endif
inline void ScopedAllocatorOptions::set_enable_op(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  enable_op_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.ScopedAllocatorOptions.enable_op)
}
inline void ScopedAllocatorOptions::set_enable_op(int index, const char* value, size_t size) {
  enable_op_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ScopedAllocatorOptions.enable_op)
}
inline ::std::string* ScopedAllocatorOptions::add_enable_op() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.ScopedAllocatorOptions.enable_op)
  return enable_op_.Add();
}
inline void ScopedAllocatorOptions::add_enable_op(const ::std::string& value) {
  enable_op_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.ScopedAllocatorOptions.enable_op)
}
#if LANG_CXX11
inline void ScopedAllocatorOptions::add_enable_op(::std::string&& value) {
  enable_op_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.ScopedAllocatorOptions.enable_op)
}
#endif
inline void ScopedAllocatorOptions::add_enable_op(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  enable_op_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.ScopedAllocatorOptions.enable_op)
}
inline void ScopedAllocatorOptions::add_enable_op(const char* value, size_t size) {
  enable_op_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.ScopedAllocatorOptions.enable_op)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
ScopedAllocatorOptions::enable_op() const {
  // @@protoc_insertion_point(field_list:tensorflow.ScopedAllocatorOptions.enable_op)
  return enable_op_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
ScopedAllocatorOptions::mutable_enable_op() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ScopedAllocatorOptions.enable_op)
  return &enable_op_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// RewriterConfig_CustomGraphOptimizer

// string name = 1;
inline void RewriterConfig_CustomGraphOptimizer::clear_name() {
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& RewriterConfig_CustomGraphOptimizer::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.CustomGraphOptimizer.name)
  return name_.Get();
}
inline void RewriterConfig_CustomGraphOptimizer::set_name(const ::std::string& value) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.CustomGraphOptimizer.name)
}
#if LANG_CXX11
inline void RewriterConfig_CustomGraphOptimizer::set_name(::std::string&& value) {
  
  name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RewriterConfig.CustomGraphOptimizer.name)
}
#endif
inline void RewriterConfig_CustomGraphOptimizer::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RewriterConfig.CustomGraphOptimizer.name)
}
inline void RewriterConfig_CustomGraphOptimizer::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RewriterConfig.CustomGraphOptimizer.name)
}
inline ::std::string* RewriterConfig_CustomGraphOptimizer::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RewriterConfig.CustomGraphOptimizer.name)
  return name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* RewriterConfig_CustomGraphOptimizer::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.RewriterConfig.CustomGraphOptimizer.name)
  
  return name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RewriterConfig_CustomGraphOptimizer::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RewriterConfig.CustomGraphOptimizer.name)
}
inline ::std::string* RewriterConfig_CustomGraphOptimizer::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RewriterConfig.CustomGraphOptimizer.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RewriterConfig_CustomGraphOptimizer::unsafe_arena_set_allocated_name(
    ::std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (name != NULL) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RewriterConfig.CustomGraphOptimizer.name)
}

// map<string, .tensorflow.AttrValue> parameter_map = 2;
inline int RewriterConfig_CustomGraphOptimizer::parameter_map_size() const {
  return parameter_map_.size();
}
inline const ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >&
RewriterConfig_CustomGraphOptimizer::parameter_map() const {
  // @@protoc_insertion_point(field_map:tensorflow.RewriterConfig.CustomGraphOptimizer.parameter_map)
  return parameter_map_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >*
RewriterConfig_CustomGraphOptimizer::mutable_parameter_map() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.RewriterConfig.CustomGraphOptimizer.parameter_map)
  return parameter_map_.MutableMap();
}

// -------------------------------------------------------------------

// RewriterConfig

// .tensorflow.RewriterConfig.Toggle layout_optimizer = 1;
inline void RewriterConfig::clear_layout_optimizer() {
  layout_optimizer_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::layout_optimizer() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.layout_optimizer)
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(layout_optimizer_);
}
inline void RewriterConfig::set_layout_optimizer(::tensorflow::RewriterConfig_Toggle value) {
  
  layout_optimizer_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.layout_optimizer)
}

// .tensorflow.RewriterConfig.Toggle constant_folding = 3;
inline void RewriterConfig::clear_constant_folding() {
  constant_folding_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::constant_folding() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.constant_folding)
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(constant_folding_);
}
inline void RewriterConfig::set_constant_folding(::tensorflow::RewriterConfig_Toggle value) {
  
  constant_folding_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.constant_folding)
}

// .tensorflow.RewriterConfig.Toggle shape_optimization = 13;
inline void RewriterConfig::clear_shape_optimization() {
  shape_optimization_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::shape_optimization() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.shape_optimization)
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(shape_optimization_);
}
inline void RewriterConfig::set_shape_optimization(::tensorflow::RewriterConfig_Toggle value) {
  
  shape_optimization_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.shape_optimization)
}

// .tensorflow.RewriterConfig.Toggle remapping = 14;
inline void RewriterConfig::clear_remapping() {
  remapping_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::remapping() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.remapping)
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(remapping_);
}
inline void RewriterConfig::set_remapping(::tensorflow::RewriterConfig_Toggle value) {
  
  remapping_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.remapping)
}

// .tensorflow.RewriterConfig.Toggle arithmetic_optimization = 7;
inline void RewriterConfig::clear_arithmetic_optimization() {
  arithmetic_optimization_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::arithmetic_optimization() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.arithmetic_optimization)
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(arithmetic_optimization_);
}
inline void RewriterConfig::set_arithmetic_optimization(::tensorflow::RewriterConfig_Toggle value) {
  
  arithmetic_optimization_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.arithmetic_optimization)
}

// .tensorflow.RewriterConfig.Toggle dependency_optimization = 8;
inline void RewriterConfig::clear_dependency_optimization() {
  dependency_optimization_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::dependency_optimization() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.dependency_optimization)
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(dependency_optimization_);
}
inline void RewriterConfig::set_dependency_optimization(::tensorflow::RewriterConfig_Toggle value) {
  
  dependency_optimization_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.dependency_optimization)
}

// .tensorflow.RewriterConfig.Toggle loop_optimization = 9;
inline void RewriterConfig::clear_loop_optimization() {
  loop_optimization_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::loop_optimization() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.loop_optimization)
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(loop_optimization_);
}
inline void RewriterConfig::set_loop_optimization(::tensorflow::RewriterConfig_Toggle value) {
  
  loop_optimization_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.loop_optimization)
}

// .tensorflow.RewriterConfig.Toggle function_optimization = 10;
inline void RewriterConfig::clear_function_optimization() {
  function_optimization_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::function_optimization() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.function_optimization)
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(function_optimization_);
}
inline void RewriterConfig::set_function_optimization(::tensorflow::RewriterConfig_Toggle value) {
  
  function_optimization_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.function_optimization)
}

// .tensorflow.RewriterConfig.Toggle debug_stripper = 11;
inline void RewriterConfig::clear_debug_stripper() {
  debug_stripper_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::debug_stripper() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.debug_stripper)
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(debug_stripper_);
}
inline void RewriterConfig::set_debug_stripper(::tensorflow::RewriterConfig_Toggle value) {
  
  debug_stripper_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.debug_stripper)
}

// bool disable_model_pruning = 2;
inline void RewriterConfig::clear_disable_model_pruning() {
  disable_model_pruning_ = false;
}
inline bool RewriterConfig::disable_model_pruning() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.disable_model_pruning)
  return disable_model_pruning_;
}
inline void RewriterConfig::set_disable_model_pruning(bool value) {
  
  disable_model_pruning_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.disable_model_pruning)
}

// .tensorflow.RewriterConfig.Toggle scoped_allocator_optimization = 15;
inline void RewriterConfig::clear_scoped_allocator_optimization() {
  scoped_allocator_optimization_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::scoped_allocator_optimization() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.scoped_allocator_optimization)
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(scoped_allocator_optimization_);
}
inline void RewriterConfig::set_scoped_allocator_optimization(::tensorflow::RewriterConfig_Toggle value) {
  
  scoped_allocator_optimization_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.scoped_allocator_optimization)
}

// .tensorflow.RewriterConfig.Toggle pin_to_host_optimization = 18;
inline void RewriterConfig::clear_pin_to_host_optimization() {
  pin_to_host_optimization_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::pin_to_host_optimization() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.pin_to_host_optimization)
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(pin_to_host_optimization_);
}
inline void RewriterConfig::set_pin_to_host_optimization(::tensorflow::RewriterConfig_Toggle value) {
  
  pin_to_host_optimization_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.pin_to_host_optimization)
}

// .tensorflow.RewriterConfig.Toggle implementation_selector = 22;
inline void RewriterConfig::clear_implementation_selector() {
  implementation_selector_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::implementation_selector() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.implementation_selector)
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(implementation_selector_);
}
inline void RewriterConfig::set_implementation_selector(::tensorflow::RewriterConfig_Toggle value) {
  
  implementation_selector_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.implementation_selector)
}

// bool disable_meta_optimizer = 19;
inline void RewriterConfig::clear_disable_meta_optimizer() {
  disable_meta_optimizer_ = false;
}
inline bool RewriterConfig::disable_meta_optimizer() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.disable_meta_optimizer)
  return disable_meta_optimizer_;
}
inline void RewriterConfig::set_disable_meta_optimizer(bool value) {
  
  disable_meta_optimizer_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.disable_meta_optimizer)
}

// .tensorflow.RewriterConfig.NumIterationsType meta_optimizer_iterations = 12;
inline void RewriterConfig::clear_meta_optimizer_iterations() {
  meta_optimizer_iterations_ = 0;
}
inline ::tensorflow::RewriterConfig_NumIterationsType RewriterConfig::meta_optimizer_iterations() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.meta_optimizer_iterations)
  return static_cast< ::tensorflow::RewriterConfig_NumIterationsType >(meta_optimizer_iterations_);
}
inline void RewriterConfig::set_meta_optimizer_iterations(::tensorflow::RewriterConfig_NumIterationsType value) {
  
  meta_optimizer_iterations_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.meta_optimizer_iterations)
}

// int32 min_graph_nodes = 17;
inline void RewriterConfig::clear_min_graph_nodes() {
  min_graph_nodes_ = 0;
}
inline ::google::protobuf::int32 RewriterConfig::min_graph_nodes() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.min_graph_nodes)
  return min_graph_nodes_;
}
inline void RewriterConfig::set_min_graph_nodes(::google::protobuf::int32 value) {
  
  min_graph_nodes_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.min_graph_nodes)
}

// .tensorflow.RewriterConfig.MemOptType memory_optimization = 4;
inline void RewriterConfig::clear_memory_optimization() {
  memory_optimization_ = 0;
}
inline ::tensorflow::RewriterConfig_MemOptType RewriterConfig::memory_optimization() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.memory_optimization)
  return static_cast< ::tensorflow::RewriterConfig_MemOptType >(memory_optimization_);
}
inline void RewriterConfig::set_memory_optimization(::tensorflow::RewriterConfig_MemOptType value) {
  
  memory_optimization_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.memory_optimization)
}

// string memory_optimizer_target_node_name_scope = 6;
inline void RewriterConfig::clear_memory_optimizer_target_node_name_scope() {
  memory_optimizer_target_node_name_scope_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& RewriterConfig::memory_optimizer_target_node_name_scope() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.memory_optimizer_target_node_name_scope)
  return memory_optimizer_target_node_name_scope_.Get();
}
inline void RewriterConfig::set_memory_optimizer_target_node_name_scope(const ::std::string& value) {
  
  memory_optimizer_target_node_name_scope_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.memory_optimizer_target_node_name_scope)
}
#if LANG_CXX11
inline void RewriterConfig::set_memory_optimizer_target_node_name_scope(::std::string&& value) {
  
  memory_optimizer_target_node_name_scope_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RewriterConfig.memory_optimizer_target_node_name_scope)
}
#endif
inline void RewriterConfig::set_memory_optimizer_target_node_name_scope(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  memory_optimizer_target_node_name_scope_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RewriterConfig.memory_optimizer_target_node_name_scope)
}
inline void RewriterConfig::set_memory_optimizer_target_node_name_scope(const char* value,
    size_t size) {
  
  memory_optimizer_target_node_name_scope_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RewriterConfig.memory_optimizer_target_node_name_scope)
}
inline ::std::string* RewriterConfig::mutable_memory_optimizer_target_node_name_scope() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RewriterConfig.memory_optimizer_target_node_name_scope)
  return memory_optimizer_target_node_name_scope_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* RewriterConfig::release_memory_optimizer_target_node_name_scope() {
  // @@protoc_insertion_point(field_release:tensorflow.RewriterConfig.memory_optimizer_target_node_name_scope)
  
  return memory_optimizer_target_node_name_scope_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RewriterConfig::set_allocated_memory_optimizer_target_node_name_scope(::std::string* memory_optimizer_target_node_name_scope) {
  if (memory_optimizer_target_node_name_scope != NULL) {
    
  } else {
    
  }
  memory_optimizer_target_node_name_scope_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), memory_optimizer_target_node_name_scope,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RewriterConfig.memory_optimizer_target_node_name_scope)
}
inline ::std::string* RewriterConfig::unsafe_arena_release_memory_optimizer_target_node_name_scope() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RewriterConfig.memory_optimizer_target_node_name_scope)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return memory_optimizer_target_node_name_scope_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RewriterConfig::unsafe_arena_set_allocated_memory_optimizer_target_node_name_scope(
    ::std::string* memory_optimizer_target_node_name_scope) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (memory_optimizer_target_node_name_scope != NULL) {
    
  } else {
    
  }
  memory_optimizer_target_node_name_scope_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      memory_optimizer_target_node_name_scope, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RewriterConfig.memory_optimizer_target_node_name_scope)
}

// int64 meta_optimizer_timeout_ms = 20;
inline void RewriterConfig::clear_meta_optimizer_timeout_ms() {
  meta_optimizer_timeout_ms_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 RewriterConfig::meta_optimizer_timeout_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.meta_optimizer_timeout_ms)
  return meta_optimizer_timeout_ms_;
}
inline void RewriterConfig::set_meta_optimizer_timeout_ms(::google::protobuf::int64 value) {
  
  meta_optimizer_timeout_ms_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.meta_optimizer_timeout_ms)
}

// .tensorflow.AutoParallelOptions auto_parallel = 5;
inline bool RewriterConfig::has_auto_parallel() const {
  return this != internal_default_instance() && auto_parallel_ != NULL;
}
inline void RewriterConfig::clear_auto_parallel() {
  if (GetArenaNoVirtual() == NULL && auto_parallel_ != NULL) {
    delete auto_parallel_;
  }
  auto_parallel_ = NULL;
}
inline const ::tensorflow::AutoParallelOptions& RewriterConfig::_internal_auto_parallel() const {
  return *auto_parallel_;
}
inline const ::tensorflow::AutoParallelOptions& RewriterConfig::auto_parallel() const {
  const ::tensorflow::AutoParallelOptions* p = auto_parallel_;
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.auto_parallel)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::AutoParallelOptions*>(
      &::tensorflow::_AutoParallelOptions_default_instance_);
}
inline ::tensorflow::AutoParallelOptions* RewriterConfig::release_auto_parallel() {
  // @@protoc_insertion_point(field_release:tensorflow.RewriterConfig.auto_parallel)
  
  ::tensorflow::AutoParallelOptions* temp = auto_parallel_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  auto_parallel_ = NULL;
  return temp;
}
inline ::tensorflow::AutoParallelOptions* RewriterConfig::unsafe_arena_release_auto_parallel() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RewriterConfig.auto_parallel)
  
  ::tensorflow::AutoParallelOptions* temp = auto_parallel_;
  auto_parallel_ = NULL;
  return temp;
}
inline ::tensorflow::AutoParallelOptions* RewriterConfig::mutable_auto_parallel() {
  
  if (auto_parallel_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::AutoParallelOptions>(GetArenaNoVirtual());
    auto_parallel_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RewriterConfig.auto_parallel)
  return auto_parallel_;
}
inline void RewriterConfig::set_allocated_auto_parallel(::tensorflow::AutoParallelOptions* auto_parallel) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete auto_parallel_;
  }
  if (auto_parallel) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(auto_parallel);
    if (message_arena != submessage_arena) {
      auto_parallel = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, auto_parallel, submessage_arena);
    }
    
  } else {
    
  }
  auto_parallel_ = auto_parallel;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RewriterConfig.auto_parallel)
}

// bool fail_on_optimizer_errors = 21;
inline void RewriterConfig::clear_fail_on_optimizer_errors() {
  fail_on_optimizer_errors_ = false;
}
inline bool RewriterConfig::fail_on_optimizer_errors() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.fail_on_optimizer_errors)
  return fail_on_optimizer_errors_;
}
inline void RewriterConfig::set_fail_on_optimizer_errors(bool value) {
  
  fail_on_optimizer_errors_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.fail_on_optimizer_errors)
}

// .tensorflow.ScopedAllocatorOptions scoped_allocator_opts = 16;
inline bool RewriterConfig::has_scoped_allocator_opts() const {
  return this != internal_default_instance() && scoped_allocator_opts_ != NULL;
}
inline void RewriterConfig::clear_scoped_allocator_opts() {
  if (GetArenaNoVirtual() == NULL && scoped_allocator_opts_ != NULL) {
    delete scoped_allocator_opts_;
  }
  scoped_allocator_opts_ = NULL;
}
inline const ::tensorflow::ScopedAllocatorOptions& RewriterConfig::_internal_scoped_allocator_opts() const {
  return *scoped_allocator_opts_;
}
inline const ::tensorflow::ScopedAllocatorOptions& RewriterConfig::scoped_allocator_opts() const {
  const ::tensorflow::ScopedAllocatorOptions* p = scoped_allocator_opts_;
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.scoped_allocator_opts)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::ScopedAllocatorOptions*>(
      &::tensorflow::_ScopedAllocatorOptions_default_instance_);
}
inline ::tensorflow::ScopedAllocatorOptions* RewriterConfig::release_scoped_allocator_opts() {
  // @@protoc_insertion_point(field_release:tensorflow.RewriterConfig.scoped_allocator_opts)
  
  ::tensorflow::ScopedAllocatorOptions* temp = scoped_allocator_opts_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  scoped_allocator_opts_ = NULL;
  return temp;
}
inline ::tensorflow::ScopedAllocatorOptions* RewriterConfig::unsafe_arena_release_scoped_allocator_opts() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RewriterConfig.scoped_allocator_opts)
  
  ::tensorflow::ScopedAllocatorOptions* temp = scoped_allocator_opts_;
  scoped_allocator_opts_ = NULL;
  return temp;
}
inline ::tensorflow::ScopedAllocatorOptions* RewriterConfig::mutable_scoped_allocator_opts() {
  
  if (scoped_allocator_opts_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::ScopedAllocatorOptions>(GetArenaNoVirtual());
    scoped_allocator_opts_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RewriterConfig.scoped_allocator_opts)
  return scoped_allocator_opts_;
}
inline void RewriterConfig::set_allocated_scoped_allocator_opts(::tensorflow::ScopedAllocatorOptions* scoped_allocator_opts) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete scoped_allocator_opts_;
  }
  if (scoped_allocator_opts) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(scoped_allocator_opts);
    if (message_arena != submessage_arena) {
      scoped_allocator_opts = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, scoped_allocator_opts, submessage_arena);
    }
    
  } else {
    
  }
  scoped_allocator_opts_ = scoped_allocator_opts;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RewriterConfig.scoped_allocator_opts)
}

// repeated string optimizers = 100;
inline int RewriterConfig::optimizers_size() const {
  return optimizers_.size();
}
inline void RewriterConfig::clear_optimizers() {
  optimizers_.Clear();
}
inline const ::std::string& RewriterConfig::optimizers(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.optimizers)
  return optimizers_.Get(index);
}
inline ::std::string* RewriterConfig::mutable_optimizers(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RewriterConfig.optimizers)
  return optimizers_.Mutable(index);
}
inline void RewriterConfig::set_optimizers(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.optimizers)
  optimizers_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void RewriterConfig::set_optimizers(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.optimizers)
  optimizers_.Mutable(index)->assign(std::move(value));
}
#endif
inline void RewriterConfig::set_optimizers(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  optimizers_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.RewriterConfig.optimizers)
}
inline void RewriterConfig::set_optimizers(int index, const char* value, size_t size) {
  optimizers_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RewriterConfig.optimizers)
}
inline ::std::string* RewriterConfig::add_optimizers() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.RewriterConfig.optimizers)
  return optimizers_.Add();
}
inline void RewriterConfig::add_optimizers(const ::std::string& value) {
  optimizers_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.RewriterConfig.optimizers)
}
#if LANG_CXX11
inline void RewriterConfig::add_optimizers(::std::string&& value) {
  optimizers_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.RewriterConfig.optimizers)
}
#endif
inline void RewriterConfig::add_optimizers(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  optimizers_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.RewriterConfig.optimizers)
}
inline void RewriterConfig::add_optimizers(const char* value, size_t size) {
  optimizers_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.RewriterConfig.optimizers)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
RewriterConfig::optimizers() const {
  // @@protoc_insertion_point(field_list:tensorflow.RewriterConfig.optimizers)
  return optimizers_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
RewriterConfig::mutable_optimizers() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RewriterConfig.optimizers)
  return &optimizers_;
}

// repeated .tensorflow.RewriterConfig.CustomGraphOptimizer custom_optimizers = 200;
inline int RewriterConfig::custom_optimizers_size() const {
  return custom_optimizers_.size();
}
inline void RewriterConfig::clear_custom_optimizers() {
  custom_optimizers_.Clear();
}
inline ::tensorflow::RewriterConfig_CustomGraphOptimizer* RewriterConfig::mutable_custom_optimizers(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RewriterConfig.custom_optimizers)
  return custom_optimizers_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::RewriterConfig_CustomGraphOptimizer >*
RewriterConfig::mutable_custom_optimizers() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RewriterConfig.custom_optimizers)
  return &custom_optimizers_;
}
inline const ::tensorflow::RewriterConfig_CustomGraphOptimizer& RewriterConfig::custom_optimizers(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.custom_optimizers)
  return custom_optimizers_.Get(index);
}
inline ::tensorflow::RewriterConfig_CustomGraphOptimizer* RewriterConfig::add_custom_optimizers() {
  // @@protoc_insertion_point(field_add:tensorflow.RewriterConfig.custom_optimizers)
  return custom_optimizers_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::RewriterConfig_CustomGraphOptimizer >&
RewriterConfig::custom_optimizers() const {
  // @@protoc_insertion_point(field_list:tensorflow.RewriterConfig.custom_optimizers)
  return custom_optimizers_;
}

// .tensorflow.VerifierConfig inter_optimizer_verifier_config = 300;
inline bool RewriterConfig::has_inter_optimizer_verifier_config() const {
  return this != internal_default_instance() && inter_optimizer_verifier_config_ != NULL;
}
inline const ::tensorflow::VerifierConfig& RewriterConfig::_internal_inter_optimizer_verifier_config() const {
  return *inter_optimizer_verifier_config_;
}
inline const ::tensorflow::VerifierConfig& RewriterConfig::inter_optimizer_verifier_config() const {
  const ::tensorflow::VerifierConfig* p = inter_optimizer_verifier_config_;
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.inter_optimizer_verifier_config)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::VerifierConfig*>(
      &::tensorflow::_VerifierConfig_default_instance_);
}
inline ::tensorflow::VerifierConfig* RewriterConfig::release_inter_optimizer_verifier_config() {
  // @@protoc_insertion_point(field_release:tensorflow.RewriterConfig.inter_optimizer_verifier_config)
  
  ::tensorflow::VerifierConfig* temp = inter_optimizer_verifier_config_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  inter_optimizer_verifier_config_ = NULL;
  return temp;
}
inline ::tensorflow::VerifierConfig* RewriterConfig::unsafe_arena_release_inter_optimizer_verifier_config() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RewriterConfig.inter_optimizer_verifier_config)
  
  ::tensorflow::VerifierConfig* temp = inter_optimizer_verifier_config_;
  inter_optimizer_verifier_config_ = NULL;
  return temp;
}
inline ::tensorflow::VerifierConfig* RewriterConfig::mutable_inter_optimizer_verifier_config() {
  
  if (inter_optimizer_verifier_config_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::VerifierConfig>(GetArenaNoVirtual());
    inter_optimizer_verifier_config_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RewriterConfig.inter_optimizer_verifier_config)
  return inter_optimizer_verifier_config_;
}
inline void RewriterConfig::set_allocated_inter_optimizer_verifier_config(::tensorflow::VerifierConfig* inter_optimizer_verifier_config) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(inter_optimizer_verifier_config_);
  }
  if (inter_optimizer_verifier_config) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(inter_optimizer_verifier_config)->GetArena();
    if (message_arena != submessage_arena) {
      inter_optimizer_verifier_config = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, inter_optimizer_verifier_config, submessage_arena);
    }
    
  } else {
    
  }
  inter_optimizer_verifier_config_ = inter_optimizer_verifier_config;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RewriterConfig.inter_optimizer_verifier_config)
}

// .tensorflow.VerifierConfig post_optimization_verifier_config = 301;
inline bool RewriterConfig::has_post_optimization_verifier_config() const {
  return this != internal_default_instance() && post_optimization_verifier_config_ != NULL;
}
inline const ::tensorflow::VerifierConfig& RewriterConfig::_internal_post_optimization_verifier_config() const {
  return *post_optimization_verifier_config_;
}
inline const ::tensorflow::VerifierConfig& RewriterConfig::post_optimization_verifier_config() const {
  const ::tensorflow::VerifierConfig* p = post_optimization_verifier_config_;
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.post_optimization_verifier_config)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::VerifierConfig*>(
      &::tensorflow::_VerifierConfig_default_instance_);
}
inline ::tensorflow::VerifierConfig* RewriterConfig::release_post_optimization_verifier_config() {
  // @@protoc_insertion_point(field_release:tensorflow.RewriterConfig.post_optimization_verifier_config)
  
  ::tensorflow::VerifierConfig* temp = post_optimization_verifier_config_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  post_optimization_verifier_config_ = NULL;
  return temp;
}
inline ::tensorflow::VerifierConfig* RewriterConfig::unsafe_arena_release_post_optimization_verifier_config() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RewriterConfig.post_optimization_verifier_config)
  
  ::tensorflow::VerifierConfig* temp = post_optimization_verifier_config_;
  post_optimization_verifier_config_ = NULL;
  return temp;
}
inline ::tensorflow::VerifierConfig* RewriterConfig::mutable_post_optimization_verifier_config() {
  
  if (post_optimization_verifier_config_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::VerifierConfig>(GetArenaNoVirtual());
    post_optimization_verifier_config_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RewriterConfig.post_optimization_verifier_config)
  return post_optimization_verifier_config_;
}
inline void RewriterConfig::set_allocated_post_optimization_verifier_config(::tensorflow::VerifierConfig* post_optimization_verifier_config) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(post_optimization_verifier_config_);
  }
  if (post_optimization_verifier_config) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(post_optimization_verifier_config)->GetArena();
    if (message_arena != submessage_arena) {
      post_optimization_verifier_config = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, post_optimization_verifier_config, submessage_arena);
    }
    
  } else {
    
  }
  post_optimization_verifier_config_ = post_optimization_verifier_config;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RewriterConfig.post_optimization_verifier_config)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::tensorflow::RewriterConfig_Toggle> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::RewriterConfig_Toggle>() {
  return ::tensorflow::RewriterConfig_Toggle_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::RewriterConfig_NumIterationsType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::RewriterConfig_NumIterationsType>() {
  return ::tensorflow::RewriterConfig_NumIterationsType_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::RewriterConfig_MemOptType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::RewriterConfig_MemOptType>() {
  return ::tensorflow::RewriterConfig_MemOptType_descriptor();
}

}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/debug.proto

#include "tensorflow/core/protobuf/debug.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_DebugTensorWatch;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_DebuggedSourceFile;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto
namespace tensorflow {
class DebugTensorWatchDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<DebugTensorWatch>
      _instance;
} _DebugTensorWatch_default_instance_;
class DebugOptionsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<DebugOptions>
      _instance;
} _DebugOptions_default_instance_;
class DebuggedSourceFileDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<DebuggedSourceFile>
      _instance;
} _DebuggedSourceFile_default_instance_;
class DebuggedSourceFilesDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<DebuggedSourceFiles>
      _instance;
} _DebuggedSourceFiles_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto {
static void InitDefaultsDebugTensorWatch() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_DebugTensorWatch_default_instance_;
    new (ptr) ::tensorflow::DebugTensorWatch();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::DebugTensorWatch::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_DebugTensorWatch =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsDebugTensorWatch}, {}};

static void InitDefaultsDebugOptions() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_DebugOptions_default_instance_;
    new (ptr) ::tensorflow::DebugOptions();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::DebugOptions::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_DebugOptions =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsDebugOptions}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::scc_info_DebugTensorWatch.base,}};

static void InitDefaultsDebuggedSourceFile() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_DebuggedSourceFile_default_instance_;
    new (ptr) ::tensorflow::DebuggedSourceFile();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::DebuggedSourceFile::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_DebuggedSourceFile =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsDebuggedSourceFile}, {}};

static void InitDefaultsDebuggedSourceFiles() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_DebuggedSourceFiles_default_instance_;
    new (ptr) ::tensorflow::DebuggedSourceFiles();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::DebuggedSourceFiles::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_DebuggedSourceFiles =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsDebuggedSourceFiles}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::scc_info_DebuggedSourceFile.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_DebugTensorWatch.base);
  ::google::protobuf::internal::InitSCC(&scc_info_DebugOptions.base);
  ::google::protobuf::internal::InitSCC(&scc_info_DebuggedSourceFile.base);
  ::google::protobuf::internal::InitSCC(&scc_info_DebuggedSourceFiles.base);
}

::google::protobuf::Metadata file_level_metadata[4];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DebugTensorWatch, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DebugTensorWatch, node_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DebugTensorWatch, output_slot_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DebugTensorWatch, debug_ops_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DebugTensorWatch, debug_urls_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DebugTensorWatch, tolerate_debug_op_creation_failures_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DebugOptions, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DebugOptions, debug_tensor_watch_opts_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DebugOptions, global_step_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DebugOptions, reset_disk_byte_usage_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DebuggedSourceFile, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DebuggedSourceFile, host_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DebuggedSourceFile, file_path_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DebuggedSourceFile, last_modified_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DebuggedSourceFile, bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DebuggedSourceFile, lines_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DebuggedSourceFiles, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::DebuggedSourceFiles, source_files_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::DebugTensorWatch)},
  { 10, -1, sizeof(::tensorflow::DebugOptions)},
  { 18, -1, sizeof(::tensorflow::DebuggedSourceFile)},
  { 28, -1, sizeof(::tensorflow::DebuggedSourceFiles)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_DebugTensorWatch_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_DebugOptions_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_DebuggedSourceFile_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_DebuggedSourceFiles_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/protobuf/debug.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 4);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n$tensorflow/core/protobuf/debug.proto\022\n"
      "tensorflow\"\216\001\n\020DebugTensorWatch\022\021\n\tnode_"
      "name\030\001 \001(\t\022\023\n\013output_slot\030\002 \001(\005\022\021\n\tdebug"
      "_ops\030\003 \003(\t\022\022\n\ndebug_urls\030\004 \003(\t\022+\n#tolera"
      "te_debug_op_creation_failures\030\005 \001(\010\"\201\001\n\014"
      "DebugOptions\022=\n\027debug_tensor_watch_opts\030"
      "\004 \003(\0132\034.tensorflow.DebugTensorWatch\022\023\n\013g"
      "lobal_step\030\n \001(\003\022\035\n\025reset_disk_byte_usag"
      "e\030\013 \001(\010\"j\n\022DebuggedSourceFile\022\014\n\004host\030\001 "
      "\001(\t\022\021\n\tfile_path\030\002 \001(\t\022\025\n\rlast_modified\030"
      "\003 \001(\003\022\r\n\005bytes\030\004 \001(\003\022\r\n\005lines\030\005 \003(\t\"K\n\023D"
      "ebuggedSourceFiles\0224\n\014source_files\030\001 \003(\013"
      "2\036.tensorflow.DebuggedSourceFileBj\n\030org."
      "tensorflow.frameworkB\013DebugProtosP\001Z<git"
      "hub.com/tensorflow/tensorflow/tensorflow"
      "/go/core/protobuf\370\001\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 628);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/protobuf/debug.proto", &protobuf_RegisterTypes);
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto
namespace tensorflow {

// ===================================================================

void DebugTensorWatch::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int DebugTensorWatch::kNodeNameFieldNumber;
const int DebugTensorWatch::kOutputSlotFieldNumber;
const int DebugTensorWatch::kDebugOpsFieldNumber;
const int DebugTensorWatch::kDebugUrlsFieldNumber;
const int DebugTensorWatch::kTolerateDebugOpCreationFailuresFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DebugTensorWatch::DebugTensorWatch()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::scc_info_DebugTensorWatch.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.DebugTensorWatch)
}
DebugTensorWatch::DebugTensorWatch(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  debug_ops_(arena),
  debug_urls_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::scc_info_DebugTensorWatch.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.DebugTensorWatch)
}
DebugTensorWatch::DebugTensorWatch(const DebugTensorWatch& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      debug_ops_(from.debug_ops_),
      debug_urls_(from.debug_urls_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  node_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.node_name().size() > 0) {
    node_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.node_name(),
      GetArenaNoVirtual());
  }
  ::memcpy(&output_slot_, &from.output_slot_,
    static_cast<size_t>(reinterpret_cast<char*>(&tolerate_debug_op_creation_failures_) -
    reinterpret_cast<char*>(&output_slot_)) + sizeof(tolerate_debug_op_creation_failures_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.DebugTensorWatch)
}

void DebugTensorWatch::SharedCtor() {
  node_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&output_slot_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&tolerate_debug_op_creation_failures_) -
      reinterpret_cast<char*>(&output_slot_)) + sizeof(tolerate_debug_op_creation_failures_));
}

DebugTensorWatch::~DebugTensorWatch() {
  // @@protoc_insertion_point(destructor:tensorflow.DebugTensorWatch)
  SharedDtor();
}

void DebugTensorWatch::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  node_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void DebugTensorWatch::ArenaDtor(void* object) {
  DebugTensorWatch* _this = reinterpret_cast< DebugTensorWatch* >(object);
  (void)_this;
}
void DebugTensorWatch::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void DebugTensorWatch::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* DebugTensorWatch::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const DebugTensorWatch& DebugTensorWatch::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::scc_info_DebugTensorWatch.base);
  return *internal_default_instance();
}


void DebugTensorWatch::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.DebugTensorWatch)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  debug_ops_.Clear();
  debug_urls_.Clear();
  node_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  ::memset(&output_slot_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&tolerate_debug_op_creation_failures_) -
      reinterpret_cast<char*>(&output_slot_)) + sizeof(tolerate_debug_op_creation_failures_));
  _internal_metadata_.Clear();
}

bool DebugTensorWatch::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.DebugTensorWatch)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string node_name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_node_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->node_name().data(), static_cast<int>(this->node_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.DebugTensorWatch.node_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 output_slot = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &output_slot_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string debug_ops = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_debug_ops()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->debug_ops(this->debug_ops_size() - 1).data(),
            static_cast<int>(this->debug_ops(this->debug_ops_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.DebugTensorWatch.debug_ops"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string debug_urls = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_debug_urls()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->debug_urls(this->debug_urls_size() - 1).data(),
            static_cast<int>(this->debug_urls(this->debug_urls_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.DebugTensorWatch.debug_urls"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool tolerate_debug_op_creation_failures = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &tolerate_debug_op_creation_failures_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.DebugTensorWatch)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.DebugTensorWatch)
  return false;
#undef DO_
}

void DebugTensorWatch::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.DebugTensorWatch)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string node_name = 1;
  if (this->node_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->node_name().data(), static_cast<int>(this->node_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.DebugTensorWatch.node_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->node_name(), output);
  }

  // int32 output_slot = 2;
  if (this->output_slot() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->output_slot(), output);
  }

  // repeated string debug_ops = 3;
  for (int i = 0, n = this->debug_ops_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->debug_ops(i).data(), static_cast<int>(this->debug_ops(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.DebugTensorWatch.debug_ops");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      3, this->debug_ops(i), output);
  }

  // repeated string debug_urls = 4;
  for (int i = 0, n = this->debug_urls_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->debug_urls(i).data(), static_cast<int>(this->debug_urls(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.DebugTensorWatch.debug_urls");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      4, this->debug_urls(i), output);
  }

  // bool tolerate_debug_op_creation_failures = 5;
  if (this->tolerate_debug_op_creation_failures() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(5, this->tolerate_debug_op_creation_failures(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.DebugTensorWatch)
}

::google::protobuf::uint8* DebugTensorWatch::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.DebugTensorWatch)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string node_name = 1;
  if (this->node_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->node_name().data(), static_cast<int>(this->node_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.DebugTensorWatch.node_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->node_name(), target);
  }

  // int32 output_slot = 2;
  if (this->output_slot() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->output_slot(), target);
  }

  // repeated string debug_ops = 3;
  for (int i = 0, n = this->debug_ops_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->debug_ops(i).data(), static_cast<int>(this->debug_ops(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.DebugTensorWatch.debug_ops");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(3, this->debug_ops(i), target);
  }

  // repeated string debug_urls = 4;
  for (int i = 0, n = this->debug_urls_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->debug_urls(i).data(), static_cast<int>(this->debug_urls(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.DebugTensorWatch.debug_urls");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(4, this->debug_urls(i), target);
  }

  // bool tolerate_debug_op_creation_failures = 5;
  if (this->tolerate_debug_op_creation_failures() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(5, this->tolerate_debug_op_creation_failures(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.DebugTensorWatch)
  return target;
}

size_t DebugTensorWatch::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.DebugTensorWatch)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated string debug_ops = 3;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->debug_ops_size());
  for (int i = 0, n = this->debug_ops_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->debug_ops(i));
  }

  // repeated string debug_urls = 4;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->debug_urls_size());
  for (int i = 0, n = this->debug_urls_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->debug_urls(i));
  }

  // string node_name = 1;
  if (this->node_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->node_name());
  }

  // int32 output_slot = 2;
  if (this->output_slot() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->output_slot());
  }

  // bool tolerate_debug_op_creation_failures = 5;
  if (this->tolerate_debug_op_creation_failures() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DebugTensorWatch::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.DebugTensorWatch)
  GOOGLE_DCHECK_NE(&from, this);
  const DebugTensorWatch* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const DebugTensorWatch>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.DebugTensorWatch)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.DebugTensorWatch)
    MergeFrom(*source);
  }
}

void DebugTensorWatch::MergeFrom(const DebugTensorWatch& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.DebugTensorWatch)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  debug_ops_.MergeFrom(from.debug_ops_);
  debug_urls_.MergeFrom(from.debug_urls_);
  if (from.node_name().size() > 0) {
    set_node_name(from.node_name());
  }
  if (from.output_slot() != 0) {
    set_output_slot(from.output_slot());
  }
  if (from.tolerate_debug_op_creation_failures() != 0) {
    set_tolerate_debug_op_creation_failures(from.tolerate_debug_op_creation_failures());
  }
}

void DebugTensorWatch::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.DebugTensorWatch)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DebugTensorWatch::CopyFrom(const DebugTensorWatch& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.DebugTensorWatch)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DebugTensorWatch::IsInitialized() const {
  return true;
}

void DebugTensorWatch::Swap(DebugTensorWatch* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    DebugTensorWatch* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void DebugTensorWatch::UnsafeArenaSwap(DebugTensorWatch* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void DebugTensorWatch::InternalSwap(DebugTensorWatch* other) {
  using std::swap;
  debug_ops_.InternalSwap(CastToBase(&other->debug_ops_));
  debug_urls_.InternalSwap(CastToBase(&other->debug_urls_));
  node_name_.Swap(&other->node_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(output_slot_, other->output_slot_);
  swap(tolerate_debug_op_creation_failures_, other->tolerate_debug_op_creation_failures_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata DebugTensorWatch::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void DebugOptions::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int DebugOptions::kDebugTensorWatchOptsFieldNumber;
const int DebugOptions::kGlobalStepFieldNumber;
const int DebugOptions::kResetDiskByteUsageFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DebugOptions::DebugOptions()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::scc_info_DebugOptions.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.DebugOptions)
}
DebugOptions::DebugOptions(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  debug_tensor_watch_opts_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::scc_info_DebugOptions.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.DebugOptions)
}
DebugOptions::DebugOptions(const DebugOptions& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      debug_tensor_watch_opts_(from.debug_tensor_watch_opts_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&reset_disk_byte_usage_, &from.reset_disk_byte_usage_,
    static_cast<size_t>(reinterpret_cast<char*>(&global_step_) -
    reinterpret_cast<char*>(&reset_disk_byte_usage_)) + sizeof(global_step_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.DebugOptions)
}

void DebugOptions::SharedCtor() {
  ::memset(&reset_disk_byte_usage_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&global_step_) -
      reinterpret_cast<char*>(&reset_disk_byte_usage_)) + sizeof(global_step_));
}

DebugOptions::~DebugOptions() {
  // @@protoc_insertion_point(destructor:tensorflow.DebugOptions)
  SharedDtor();
}

void DebugOptions::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void DebugOptions::ArenaDtor(void* object) {
  DebugOptions* _this = reinterpret_cast< DebugOptions* >(object);
  (void)_this;
}
void DebugOptions::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void DebugOptions::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* DebugOptions::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const DebugOptions& DebugOptions::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::scc_info_DebugOptions.base);
  return *internal_default_instance();
}


void DebugOptions::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.DebugOptions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  debug_tensor_watch_opts_.Clear();
  ::memset(&reset_disk_byte_usage_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&global_step_) -
      reinterpret_cast<char*>(&reset_disk_byte_usage_)) + sizeof(global_step_));
  _internal_metadata_.Clear();
}

bool DebugOptions::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.DebugOptions)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.DebugTensorWatch debug_tensor_watch_opts = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_debug_tensor_watch_opts()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 global_step = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(80u /* 80 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &global_step_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool reset_disk_byte_usage = 11;
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(88u /* 88 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &reset_disk_byte_usage_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.DebugOptions)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.DebugOptions)
  return false;
#undef DO_
}

void DebugOptions::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.DebugOptions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.DebugTensorWatch debug_tensor_watch_opts = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->debug_tensor_watch_opts_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4,
      this->debug_tensor_watch_opts(static_cast<int>(i)),
      output);
  }

  // int64 global_step = 10;
  if (this->global_step() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->global_step(), output);
  }

  // bool reset_disk_byte_usage = 11;
  if (this->reset_disk_byte_usage() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(11, this->reset_disk_byte_usage(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.DebugOptions)
}

::google::protobuf::uint8* DebugOptions::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.DebugOptions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.DebugTensorWatch debug_tensor_watch_opts = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->debug_tensor_watch_opts_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->debug_tensor_watch_opts(static_cast<int>(i)), deterministic, target);
  }

  // int64 global_step = 10;
  if (this->global_step() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->global_step(), target);
  }

  // bool reset_disk_byte_usage = 11;
  if (this->reset_disk_byte_usage() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(11, this->reset_disk_byte_usage(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.DebugOptions)
  return target;
}

size_t DebugOptions::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.DebugOptions)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.DebugTensorWatch debug_tensor_watch_opts = 4;
  {
    unsigned int count = static_cast<unsigned int>(this->debug_tensor_watch_opts_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->debug_tensor_watch_opts(static_cast<int>(i)));
    }
  }

  // bool reset_disk_byte_usage = 11;
  if (this->reset_disk_byte_usage() != 0) {
    total_size += 1 + 1;
  }

  // int64 global_step = 10;
  if (this->global_step() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->global_step());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DebugOptions::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.DebugOptions)
  GOOGLE_DCHECK_NE(&from, this);
  const DebugOptions* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const DebugOptions>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.DebugOptions)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.DebugOptions)
    MergeFrom(*source);
  }
}

void DebugOptions::MergeFrom(const DebugOptions& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.DebugOptions)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  debug_tensor_watch_opts_.MergeFrom(from.debug_tensor_watch_opts_);
  if (from.reset_disk_byte_usage() != 0) {
    set_reset_disk_byte_usage(from.reset_disk_byte_usage());
  }
  if (from.global_step() != 0) {
    set_global_step(from.global_step());
  }
}

void DebugOptions::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.DebugOptions)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DebugOptions::CopyFrom(const DebugOptions& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.DebugOptions)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DebugOptions::IsInitialized() const {
  return true;
}

void DebugOptions::Swap(DebugOptions* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    DebugOptions* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void DebugOptions::UnsafeArenaSwap(DebugOptions* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void DebugOptions::InternalSwap(DebugOptions* other) {
  using std::swap;
  CastToBase(&debug_tensor_watch_opts_)->InternalSwap(CastToBase(&other->debug_tensor_watch_opts_));
  swap(reset_disk_byte_usage_, other->reset_disk_byte_usage_);
  swap(global_step_, other->global_step_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata DebugOptions::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void DebuggedSourceFile::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int DebuggedSourceFile::kHostFieldNumber;
const int DebuggedSourceFile::kFilePathFieldNumber;
const int DebuggedSourceFile::kLastModifiedFieldNumber;
const int DebuggedSourceFile::kBytesFieldNumber;
const int DebuggedSourceFile::kLinesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DebuggedSourceFile::DebuggedSourceFile()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::scc_info_DebuggedSourceFile.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.DebuggedSourceFile)
}
DebuggedSourceFile::DebuggedSourceFile(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  lines_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::scc_info_DebuggedSourceFile.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.DebuggedSourceFile)
}
DebuggedSourceFile::DebuggedSourceFile(const DebuggedSourceFile& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      lines_(from.lines_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  host_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.host().size() > 0) {
    host_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.host(),
      GetArenaNoVirtual());
  }
  file_path_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.file_path().size() > 0) {
    file_path_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.file_path(),
      GetArenaNoVirtual());
  }
  ::memcpy(&last_modified_, &from.last_modified_,
    static_cast<size_t>(reinterpret_cast<char*>(&bytes_) -
    reinterpret_cast<char*>(&last_modified_)) + sizeof(bytes_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.DebuggedSourceFile)
}

void DebuggedSourceFile::SharedCtor() {
  host_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  file_path_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&last_modified_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&bytes_) -
      reinterpret_cast<char*>(&last_modified_)) + sizeof(bytes_));
}

DebuggedSourceFile::~DebuggedSourceFile() {
  // @@protoc_insertion_point(destructor:tensorflow.DebuggedSourceFile)
  SharedDtor();
}

void DebuggedSourceFile::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  host_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  file_path_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void DebuggedSourceFile::ArenaDtor(void* object) {
  DebuggedSourceFile* _this = reinterpret_cast< DebuggedSourceFile* >(object);
  (void)_this;
}
void DebuggedSourceFile::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void DebuggedSourceFile::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* DebuggedSourceFile::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const DebuggedSourceFile& DebuggedSourceFile::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::scc_info_DebuggedSourceFile.base);
  return *internal_default_instance();
}


void DebuggedSourceFile::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.DebuggedSourceFile)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  lines_.Clear();
  host_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  file_path_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  ::memset(&last_modified_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&bytes_) -
      reinterpret_cast<char*>(&last_modified_)) + sizeof(bytes_));
  _internal_metadata_.Clear();
}

bool DebuggedSourceFile::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.DebuggedSourceFile)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string host = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_host()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->host().data(), static_cast<int>(this->host().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.DebuggedSourceFile.host"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string file_path = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_file_path()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->file_path().data(), static_cast<int>(this->file_path().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.DebuggedSourceFile.file_path"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 last_modified = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &last_modified_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 bytes = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string lines = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_lines()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->lines(this->lines_size() - 1).data(),
            static_cast<int>(this->lines(this->lines_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.DebuggedSourceFile.lines"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.DebuggedSourceFile)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.DebuggedSourceFile)
  return false;
#undef DO_
}

void DebuggedSourceFile::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.DebuggedSourceFile)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string host = 1;
  if (this->host().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->host().data(), static_cast<int>(this->host().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.DebuggedSourceFile.host");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->host(), output);
  }

  // string file_path = 2;
  if (this->file_path().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->file_path().data(), static_cast<int>(this->file_path().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.DebuggedSourceFile.file_path");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->file_path(), output);
  }

  // int64 last_modified = 3;
  if (this->last_modified() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->last_modified(), output);
  }

  // int64 bytes = 4;
  if (this->bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->bytes(), output);
  }

  // repeated string lines = 5;
  for (int i = 0, n = this->lines_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->lines(i).data(), static_cast<int>(this->lines(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.DebuggedSourceFile.lines");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      5, this->lines(i), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.DebuggedSourceFile)
}

::google::protobuf::uint8* DebuggedSourceFile::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.DebuggedSourceFile)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string host = 1;
  if (this->host().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->host().data(), static_cast<int>(this->host().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.DebuggedSourceFile.host");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->host(), target);
  }

  // string file_path = 2;
  if (this->file_path().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->file_path().data(), static_cast<int>(this->file_path().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.DebuggedSourceFile.file_path");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->file_path(), target);
  }

  // int64 last_modified = 3;
  if (this->last_modified() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->last_modified(), target);
  }

  // int64 bytes = 4;
  if (this->bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->bytes(), target);
  }

  // repeated string lines = 5;
  for (int i = 0, n = this->lines_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->lines(i).data(), static_cast<int>(this->lines(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.DebuggedSourceFile.lines");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(5, this->lines(i), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.DebuggedSourceFile)
  return target;
}

size_t DebuggedSourceFile::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.DebuggedSourceFile)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated string lines = 5;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->lines_size());
  for (int i = 0, n = this->lines_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->lines(i));
  }

  // string host = 1;
  if (this->host().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->host());
  }

  // string file_path = 2;
  if (this->file_path().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->file_path());
  }

  // int64 last_modified = 3;
  if (this->last_modified() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->last_modified());
  }

  // int64 bytes = 4;
  if (this->bytes() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->bytes());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DebuggedSourceFile::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.DebuggedSourceFile)
  GOOGLE_DCHECK_NE(&from, this);
  const DebuggedSourceFile* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const DebuggedSourceFile>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.DebuggedSourceFile)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.DebuggedSourceFile)
    MergeFrom(*source);
  }
}

void DebuggedSourceFile::MergeFrom(const DebuggedSourceFile& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.DebuggedSourceFile)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  lines_.MergeFrom(from.lines_);
  if (from.host().size() > 0) {
    set_host(from.host());
  }
  if (from.file_path().size() > 0) {
    set_file_path(from.file_path());
  }
  if (from.last_modified() != 0) {
    set_last_modified(from.last_modified());
  }
  if (from.bytes() != 0) {
    set_bytes(from.bytes());
  }
}

void DebuggedSourceFile::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.DebuggedSourceFile)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DebuggedSourceFile::CopyFrom(const DebuggedSourceFile& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.DebuggedSourceFile)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DebuggedSourceFile::IsInitialized() const {
  return true;
}

void DebuggedSourceFile::Swap(DebuggedSourceFile* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    DebuggedSourceFile* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void DebuggedSourceFile::UnsafeArenaSwap(DebuggedSourceFile* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void DebuggedSourceFile::InternalSwap(DebuggedSourceFile* other) {
  using std::swap;
  lines_.InternalSwap(CastToBase(&other->lines_));
  host_.Swap(&other->host_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  file_path_.Swap(&other->file_path_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(last_modified_, other->last_modified_);
  swap(bytes_, other->bytes_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata DebuggedSourceFile::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void DebuggedSourceFiles::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int DebuggedSourceFiles::kSourceFilesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DebuggedSourceFiles::DebuggedSourceFiles()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::scc_info_DebuggedSourceFiles.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.DebuggedSourceFiles)
}
DebuggedSourceFiles::DebuggedSourceFiles(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  source_files_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::scc_info_DebuggedSourceFiles.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.DebuggedSourceFiles)
}
DebuggedSourceFiles::DebuggedSourceFiles(const DebuggedSourceFiles& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      source_files_(from.source_files_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.DebuggedSourceFiles)
}

void DebuggedSourceFiles::SharedCtor() {
}

DebuggedSourceFiles::~DebuggedSourceFiles() {
  // @@protoc_insertion_point(destructor:tensorflow.DebuggedSourceFiles)
  SharedDtor();
}

void DebuggedSourceFiles::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void DebuggedSourceFiles::ArenaDtor(void* object) {
  DebuggedSourceFiles* _this = reinterpret_cast< DebuggedSourceFiles* >(object);
  (void)_this;
}
void DebuggedSourceFiles::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void DebuggedSourceFiles::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* DebuggedSourceFiles::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const DebuggedSourceFiles& DebuggedSourceFiles::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::scc_info_DebuggedSourceFiles.base);
  return *internal_default_instance();
}


void DebuggedSourceFiles::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.DebuggedSourceFiles)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  source_files_.Clear();
  _internal_metadata_.Clear();
}

bool DebuggedSourceFiles::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.DebuggedSourceFiles)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.DebuggedSourceFile source_files = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_source_files()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.DebuggedSourceFiles)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.DebuggedSourceFiles)
  return false;
#undef DO_
}

void DebuggedSourceFiles::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.DebuggedSourceFiles)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.DebuggedSourceFile source_files = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->source_files_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->source_files(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.DebuggedSourceFiles)
}

::google::protobuf::uint8* DebuggedSourceFiles::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.DebuggedSourceFiles)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.DebuggedSourceFile source_files = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->source_files_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->source_files(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.DebuggedSourceFiles)
  return target;
}

size_t DebuggedSourceFiles::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.DebuggedSourceFiles)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.DebuggedSourceFile source_files = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->source_files_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->source_files(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DebuggedSourceFiles::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.DebuggedSourceFiles)
  GOOGLE_DCHECK_NE(&from, this);
  const DebuggedSourceFiles* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const DebuggedSourceFiles>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.DebuggedSourceFiles)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.DebuggedSourceFiles)
    MergeFrom(*source);
  }
}

void DebuggedSourceFiles::MergeFrom(const DebuggedSourceFiles& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.DebuggedSourceFiles)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  source_files_.MergeFrom(from.source_files_);
}

void DebuggedSourceFiles::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.DebuggedSourceFiles)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DebuggedSourceFiles::CopyFrom(const DebuggedSourceFiles& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.DebuggedSourceFiles)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DebuggedSourceFiles::IsInitialized() const {
  return true;
}

void DebuggedSourceFiles::Swap(DebuggedSourceFiles* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    DebuggedSourceFiles* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void DebuggedSourceFiles::UnsafeArenaSwap(DebuggedSourceFiles* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void DebuggedSourceFiles::InternalSwap(DebuggedSourceFiles* other) {
  using std::swap;
  CastToBase(&source_files_)->InternalSwap(CastToBase(&other->source_files_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata DebuggedSourceFiles::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::DebugTensorWatch* Arena::CreateMaybeMessage< ::tensorflow::DebugTensorWatch >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::DebugTensorWatch >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::DebugOptions* Arena::CreateMaybeMessage< ::tensorflow::DebugOptions >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::DebugOptions >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::DebuggedSourceFile* Arena::CreateMaybeMessage< ::tensorflow::DebuggedSourceFile >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::DebuggedSourceFile >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::DebuggedSourceFiles* Arena::CreateMaybeMessage< ::tensorflow::DebuggedSourceFiles >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::DebuggedSourceFiles >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

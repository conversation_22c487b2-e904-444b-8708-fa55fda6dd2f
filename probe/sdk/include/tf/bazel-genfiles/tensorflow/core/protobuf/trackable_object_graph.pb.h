// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/trackable_object_graph.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto 

namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[5];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto
namespace tensorflow {
class TrackableObjectGraph;
class TrackableObjectGraphDefaultTypeInternal;
extern TrackableObjectGraphDefaultTypeInternal _TrackableObjectGraph_default_instance_;
class TrackableObjectGraph_TrackableObject;
class TrackableObjectGraph_TrackableObjectDefaultTypeInternal;
extern TrackableObjectGraph_TrackableObjectDefaultTypeInternal _TrackableObjectGraph_TrackableObject_default_instance_;
class TrackableObjectGraph_TrackableObject_ObjectReference;
class TrackableObjectGraph_TrackableObject_ObjectReferenceDefaultTypeInternal;
extern TrackableObjectGraph_TrackableObject_ObjectReferenceDefaultTypeInternal _TrackableObjectGraph_TrackableObject_ObjectReference_default_instance_;
class TrackableObjectGraph_TrackableObject_SerializedTensor;
class TrackableObjectGraph_TrackableObject_SerializedTensorDefaultTypeInternal;
extern TrackableObjectGraph_TrackableObject_SerializedTensorDefaultTypeInternal _TrackableObjectGraph_TrackableObject_SerializedTensor_default_instance_;
class TrackableObjectGraph_TrackableObject_SlotVariableReference;
class TrackableObjectGraph_TrackableObject_SlotVariableReferenceDefaultTypeInternal;
extern TrackableObjectGraph_TrackableObject_SlotVariableReferenceDefaultTypeInternal _TrackableObjectGraph_TrackableObject_SlotVariableReference_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::TrackableObjectGraph* Arena::CreateMaybeMessage<::tensorflow::TrackableObjectGraph>(Arena*);
template<> ::tensorflow::TrackableObjectGraph_TrackableObject* Arena::CreateMaybeMessage<::tensorflow::TrackableObjectGraph_TrackableObject>(Arena*);
template<> ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* Arena::CreateMaybeMessage<::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference>(Arena*);
template<> ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor* Arena::CreateMaybeMessage<::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor>(Arena*);
template<> ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* Arena::CreateMaybeMessage<::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

// ===================================================================

class TrackableObjectGraph_TrackableObject_ObjectReference : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference) */ {
 public:
  TrackableObjectGraph_TrackableObject_ObjectReference();
  virtual ~TrackableObjectGraph_TrackableObject_ObjectReference();

  TrackableObjectGraph_TrackableObject_ObjectReference(const TrackableObjectGraph_TrackableObject_ObjectReference& from);

  inline TrackableObjectGraph_TrackableObject_ObjectReference& operator=(const TrackableObjectGraph_TrackableObject_ObjectReference& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TrackableObjectGraph_TrackableObject_ObjectReference(TrackableObjectGraph_TrackableObject_ObjectReference&& from) noexcept
    : TrackableObjectGraph_TrackableObject_ObjectReference() {
    *this = ::std::move(from);
  }

  inline TrackableObjectGraph_TrackableObject_ObjectReference& operator=(TrackableObjectGraph_TrackableObject_ObjectReference&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TrackableObjectGraph_TrackableObject_ObjectReference& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TrackableObjectGraph_TrackableObject_ObjectReference* internal_default_instance() {
    return reinterpret_cast<const TrackableObjectGraph_TrackableObject_ObjectReference*>(
               &_TrackableObjectGraph_TrackableObject_ObjectReference_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(TrackableObjectGraph_TrackableObject_ObjectReference* other);
  void Swap(TrackableObjectGraph_TrackableObject_ObjectReference* other);
  friend void swap(TrackableObjectGraph_TrackableObject_ObjectReference& a, TrackableObjectGraph_TrackableObject_ObjectReference& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TrackableObjectGraph_TrackableObject_ObjectReference* New() const final {
    return CreateMaybeMessage<TrackableObjectGraph_TrackableObject_ObjectReference>(NULL);
  }

  TrackableObjectGraph_TrackableObject_ObjectReference* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TrackableObjectGraph_TrackableObject_ObjectReference>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TrackableObjectGraph_TrackableObject_ObjectReference& from);
  void MergeFrom(const TrackableObjectGraph_TrackableObject_ObjectReference& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TrackableObjectGraph_TrackableObject_ObjectReference* other);
  protected:
  explicit TrackableObjectGraph_TrackableObject_ObjectReference(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string local_name = 2;
  void clear_local_name();
  static const int kLocalNameFieldNumber = 2;
  const ::std::string& local_name() const;
  void set_local_name(const ::std::string& value);
  #if LANG_CXX11
  void set_local_name(::std::string&& value);
  #endif
  void set_local_name(const char* value);
  void set_local_name(const char* value, size_t size);
  ::std::string* mutable_local_name();
  ::std::string* release_local_name();
  void set_allocated_local_name(::std::string* local_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_local_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_local_name(
      ::std::string* local_name);

  // int32 node_id = 1;
  void clear_node_id();
  static const int kNodeIdFieldNumber = 1;
  ::google::protobuf::int32 node_id() const;
  void set_node_id(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr local_name_;
  ::google::protobuf::int32 node_id_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class TrackableObjectGraph_TrackableObject_SerializedTensor : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor) */ {
 public:
  TrackableObjectGraph_TrackableObject_SerializedTensor();
  virtual ~TrackableObjectGraph_TrackableObject_SerializedTensor();

  TrackableObjectGraph_TrackableObject_SerializedTensor(const TrackableObjectGraph_TrackableObject_SerializedTensor& from);

  inline TrackableObjectGraph_TrackableObject_SerializedTensor& operator=(const TrackableObjectGraph_TrackableObject_SerializedTensor& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TrackableObjectGraph_TrackableObject_SerializedTensor(TrackableObjectGraph_TrackableObject_SerializedTensor&& from) noexcept
    : TrackableObjectGraph_TrackableObject_SerializedTensor() {
    *this = ::std::move(from);
  }

  inline TrackableObjectGraph_TrackableObject_SerializedTensor& operator=(TrackableObjectGraph_TrackableObject_SerializedTensor&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TrackableObjectGraph_TrackableObject_SerializedTensor& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TrackableObjectGraph_TrackableObject_SerializedTensor* internal_default_instance() {
    return reinterpret_cast<const TrackableObjectGraph_TrackableObject_SerializedTensor*>(
               &_TrackableObjectGraph_TrackableObject_SerializedTensor_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void UnsafeArenaSwap(TrackableObjectGraph_TrackableObject_SerializedTensor* other);
  void Swap(TrackableObjectGraph_TrackableObject_SerializedTensor* other);
  friend void swap(TrackableObjectGraph_TrackableObject_SerializedTensor& a, TrackableObjectGraph_TrackableObject_SerializedTensor& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TrackableObjectGraph_TrackableObject_SerializedTensor* New() const final {
    return CreateMaybeMessage<TrackableObjectGraph_TrackableObject_SerializedTensor>(NULL);
  }

  TrackableObjectGraph_TrackableObject_SerializedTensor* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TrackableObjectGraph_TrackableObject_SerializedTensor>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TrackableObjectGraph_TrackableObject_SerializedTensor& from);
  void MergeFrom(const TrackableObjectGraph_TrackableObject_SerializedTensor& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TrackableObjectGraph_TrackableObject_SerializedTensor* other);
  protected:
  explicit TrackableObjectGraph_TrackableObject_SerializedTensor(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      ::std::string* name);

  // string full_name = 2;
  void clear_full_name();
  static const int kFullNameFieldNumber = 2;
  const ::std::string& full_name() const;
  void set_full_name(const ::std::string& value);
  #if LANG_CXX11
  void set_full_name(::std::string&& value);
  #endif
  void set_full_name(const char* value);
  void set_full_name(const char* value, size_t size);
  ::std::string* mutable_full_name();
  ::std::string* release_full_name();
  void set_allocated_full_name(::std::string* full_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_full_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_full_name(
      ::std::string* full_name);

  // string checkpoint_key = 3;
  void clear_checkpoint_key();
  static const int kCheckpointKeyFieldNumber = 3;
  const ::std::string& checkpoint_key() const;
  void set_checkpoint_key(const ::std::string& value);
  #if LANG_CXX11
  void set_checkpoint_key(::std::string&& value);
  #endif
  void set_checkpoint_key(const char* value);
  void set_checkpoint_key(const char* value, size_t size);
  ::std::string* mutable_checkpoint_key();
  ::std::string* release_checkpoint_key();
  void set_allocated_checkpoint_key(::std::string* checkpoint_key);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_checkpoint_key();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_checkpoint_key(
      ::std::string* checkpoint_key);

  // bool optional_restore = 4;
  void clear_optional_restore();
  static const int kOptionalRestoreFieldNumber = 4;
  bool optional_restore() const;
  void set_optional_restore(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::internal::ArenaStringPtr full_name_;
  ::google::protobuf::internal::ArenaStringPtr checkpoint_key_;
  bool optional_restore_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class TrackableObjectGraph_TrackableObject_SlotVariableReference : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference) */ {
 public:
  TrackableObjectGraph_TrackableObject_SlotVariableReference();
  virtual ~TrackableObjectGraph_TrackableObject_SlotVariableReference();

  TrackableObjectGraph_TrackableObject_SlotVariableReference(const TrackableObjectGraph_TrackableObject_SlotVariableReference& from);

  inline TrackableObjectGraph_TrackableObject_SlotVariableReference& operator=(const TrackableObjectGraph_TrackableObject_SlotVariableReference& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TrackableObjectGraph_TrackableObject_SlotVariableReference(TrackableObjectGraph_TrackableObject_SlotVariableReference&& from) noexcept
    : TrackableObjectGraph_TrackableObject_SlotVariableReference() {
    *this = ::std::move(from);
  }

  inline TrackableObjectGraph_TrackableObject_SlotVariableReference& operator=(TrackableObjectGraph_TrackableObject_SlotVariableReference&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TrackableObjectGraph_TrackableObject_SlotVariableReference& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TrackableObjectGraph_TrackableObject_SlotVariableReference* internal_default_instance() {
    return reinterpret_cast<const TrackableObjectGraph_TrackableObject_SlotVariableReference*>(
               &_TrackableObjectGraph_TrackableObject_SlotVariableReference_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void UnsafeArenaSwap(TrackableObjectGraph_TrackableObject_SlotVariableReference* other);
  void Swap(TrackableObjectGraph_TrackableObject_SlotVariableReference* other);
  friend void swap(TrackableObjectGraph_TrackableObject_SlotVariableReference& a, TrackableObjectGraph_TrackableObject_SlotVariableReference& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TrackableObjectGraph_TrackableObject_SlotVariableReference* New() const final {
    return CreateMaybeMessage<TrackableObjectGraph_TrackableObject_SlotVariableReference>(NULL);
  }

  TrackableObjectGraph_TrackableObject_SlotVariableReference* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TrackableObjectGraph_TrackableObject_SlotVariableReference>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TrackableObjectGraph_TrackableObject_SlotVariableReference& from);
  void MergeFrom(const TrackableObjectGraph_TrackableObject_SlotVariableReference& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TrackableObjectGraph_TrackableObject_SlotVariableReference* other);
  protected:
  explicit TrackableObjectGraph_TrackableObject_SlotVariableReference(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string slot_name = 2;
  void clear_slot_name();
  static const int kSlotNameFieldNumber = 2;
  const ::std::string& slot_name() const;
  void set_slot_name(const ::std::string& value);
  #if LANG_CXX11
  void set_slot_name(::std::string&& value);
  #endif
  void set_slot_name(const char* value);
  void set_slot_name(const char* value, size_t size);
  ::std::string* mutable_slot_name();
  ::std::string* release_slot_name();
  void set_allocated_slot_name(::std::string* slot_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_slot_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_slot_name(
      ::std::string* slot_name);

  // int32 original_variable_node_id = 1;
  void clear_original_variable_node_id();
  static const int kOriginalVariableNodeIdFieldNumber = 1;
  ::google::protobuf::int32 original_variable_node_id() const;
  void set_original_variable_node_id(::google::protobuf::int32 value);

  // int32 slot_variable_node_id = 3;
  void clear_slot_variable_node_id();
  static const int kSlotVariableNodeIdFieldNumber = 3;
  ::google::protobuf::int32 slot_variable_node_id() const;
  void set_slot_variable_node_id(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr slot_name_;
  ::google::protobuf::int32 original_variable_node_id_;
  ::google::protobuf::int32 slot_variable_node_id_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class TrackableObjectGraph_TrackableObject : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.TrackableObjectGraph.TrackableObject) */ {
 public:
  TrackableObjectGraph_TrackableObject();
  virtual ~TrackableObjectGraph_TrackableObject();

  TrackableObjectGraph_TrackableObject(const TrackableObjectGraph_TrackableObject& from);

  inline TrackableObjectGraph_TrackableObject& operator=(const TrackableObjectGraph_TrackableObject& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TrackableObjectGraph_TrackableObject(TrackableObjectGraph_TrackableObject&& from) noexcept
    : TrackableObjectGraph_TrackableObject() {
    *this = ::std::move(from);
  }

  inline TrackableObjectGraph_TrackableObject& operator=(TrackableObjectGraph_TrackableObject&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TrackableObjectGraph_TrackableObject& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TrackableObjectGraph_TrackableObject* internal_default_instance() {
    return reinterpret_cast<const TrackableObjectGraph_TrackableObject*>(
               &_TrackableObjectGraph_TrackableObject_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void UnsafeArenaSwap(TrackableObjectGraph_TrackableObject* other);
  void Swap(TrackableObjectGraph_TrackableObject* other);
  friend void swap(TrackableObjectGraph_TrackableObject& a, TrackableObjectGraph_TrackableObject& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TrackableObjectGraph_TrackableObject* New() const final {
    return CreateMaybeMessage<TrackableObjectGraph_TrackableObject>(NULL);
  }

  TrackableObjectGraph_TrackableObject* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TrackableObjectGraph_TrackableObject>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TrackableObjectGraph_TrackableObject& from);
  void MergeFrom(const TrackableObjectGraph_TrackableObject& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TrackableObjectGraph_TrackableObject* other);
  protected:
  explicit TrackableObjectGraph_TrackableObject(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef TrackableObjectGraph_TrackableObject_ObjectReference ObjectReference;
  typedef TrackableObjectGraph_TrackableObject_SerializedTensor SerializedTensor;
  typedef TrackableObjectGraph_TrackableObject_SlotVariableReference SlotVariableReference;

  // accessors -------------------------------------------------------

  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference children = 1;
  int children_size() const;
  void clear_children();
  static const int kChildrenFieldNumber = 1;
  ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* mutable_children(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >*
      mutable_children();
  const ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference& children(int index) const;
  ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* add_children();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >&
      children() const;

  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor attributes = 2;
  int attributes_size() const;
  void clear_attributes();
  static const int kAttributesFieldNumber = 2;
  ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor* mutable_attributes(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor >*
      mutable_attributes();
  const ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor& attributes(int index) const;
  ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor* add_attributes();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor >&
      attributes() const;

  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference slot_variables = 3;
  int slot_variables_size() const;
  void clear_slot_variables();
  static const int kSlotVariablesFieldNumber = 3;
  ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* mutable_slot_variables(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference >*
      mutable_slot_variables();
  const ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference& slot_variables(int index) const;
  ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* add_slot_variables();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference >&
      slot_variables() const;

  // @@protoc_insertion_point(class_scope:tensorflow.TrackableObjectGraph.TrackableObject)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference > children_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor > attributes_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference > slot_variables_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class TrackableObjectGraph : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.TrackableObjectGraph) */ {
 public:
  TrackableObjectGraph();
  virtual ~TrackableObjectGraph();

  TrackableObjectGraph(const TrackableObjectGraph& from);

  inline TrackableObjectGraph& operator=(const TrackableObjectGraph& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TrackableObjectGraph(TrackableObjectGraph&& from) noexcept
    : TrackableObjectGraph() {
    *this = ::std::move(from);
  }

  inline TrackableObjectGraph& operator=(TrackableObjectGraph&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const TrackableObjectGraph& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TrackableObjectGraph* internal_default_instance() {
    return reinterpret_cast<const TrackableObjectGraph*>(
               &_TrackableObjectGraph_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void UnsafeArenaSwap(TrackableObjectGraph* other);
  void Swap(TrackableObjectGraph* other);
  friend void swap(TrackableObjectGraph& a, TrackableObjectGraph& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TrackableObjectGraph* New() const final {
    return CreateMaybeMessage<TrackableObjectGraph>(NULL);
  }

  TrackableObjectGraph* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TrackableObjectGraph>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TrackableObjectGraph& from);
  void MergeFrom(const TrackableObjectGraph& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TrackableObjectGraph* other);
  protected:
  explicit TrackableObjectGraph(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef TrackableObjectGraph_TrackableObject TrackableObject;

  // accessors -------------------------------------------------------

  // repeated .tensorflow.TrackableObjectGraph.TrackableObject nodes = 1;
  int nodes_size() const;
  void clear_nodes();
  static const int kNodesFieldNumber = 1;
  ::tensorflow::TrackableObjectGraph_TrackableObject* mutable_nodes(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject >*
      mutable_nodes();
  const ::tensorflow::TrackableObjectGraph_TrackableObject& nodes(int index) const;
  ::tensorflow::TrackableObjectGraph_TrackableObject* add_nodes();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject >&
      nodes() const;

  // @@protoc_insertion_point(class_scope:tensorflow.TrackableObjectGraph)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject > nodes_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TrackableObjectGraph_TrackableObject_ObjectReference

// int32 node_id = 1;
inline void TrackableObjectGraph_TrackableObject_ObjectReference::clear_node_id() {
  node_id_ = 0;
}
inline ::google::protobuf::int32 TrackableObjectGraph_TrackableObject_ObjectReference::node_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.node_id)
  return node_id_;
}
inline void TrackableObjectGraph_TrackableObject_ObjectReference::set_node_id(::google::protobuf::int32 value) {
  
  node_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.node_id)
}

// string local_name = 2;
inline void TrackableObjectGraph_TrackableObject_ObjectReference::clear_local_name() {
  local_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& TrackableObjectGraph_TrackableObject_ObjectReference::local_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.local_name)
  return local_name_.Get();
}
inline void TrackableObjectGraph_TrackableObject_ObjectReference::set_local_name(const ::std::string& value) {
  
  local_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.local_name)
}
#if LANG_CXX11
inline void TrackableObjectGraph_TrackableObject_ObjectReference::set_local_name(::std::string&& value) {
  
  local_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.local_name)
}
#endif
inline void TrackableObjectGraph_TrackableObject_ObjectReference::set_local_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  local_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.local_name)
}
inline void TrackableObjectGraph_TrackableObject_ObjectReference::set_local_name(const char* value,
    size_t size) {
  
  local_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.local_name)
}
inline ::std::string* TrackableObjectGraph_TrackableObject_ObjectReference::mutable_local_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.local_name)
  return local_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* TrackableObjectGraph_TrackableObject_ObjectReference::release_local_name() {
  // @@protoc_insertion_point(field_release:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.local_name)
  
  return local_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TrackableObjectGraph_TrackableObject_ObjectReference::set_allocated_local_name(::std::string* local_name) {
  if (local_name != NULL) {
    
  } else {
    
  }
  local_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), local_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.local_name)
}
inline ::std::string* TrackableObjectGraph_TrackableObject_ObjectReference::unsafe_arena_release_local_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.local_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return local_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TrackableObjectGraph_TrackableObject_ObjectReference::unsafe_arena_set_allocated_local_name(
    ::std::string* local_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (local_name != NULL) {
    
  } else {
    
  }
  local_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      local_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.local_name)
}

// -------------------------------------------------------------------

// TrackableObjectGraph_TrackableObject_SerializedTensor

// string name = 1;
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::clear_name() {
  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& TrackableObjectGraph_TrackableObject_SerializedTensor::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.name)
  return name_.Get();
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_name(const ::std::string& value) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.name)
}
#if LANG_CXX11
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_name(::std::string&& value) {
  
  name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.name)
}
#endif
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.name)
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.name)
}
inline ::std::string* TrackableObjectGraph_TrackableObject_SerializedTensor::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.name)
  return name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* TrackableObjectGraph_TrackableObject_SerializedTensor::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.name)
  
  return name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.name)
}
inline ::std::string* TrackableObjectGraph_TrackableObject_SerializedTensor::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::unsafe_arena_set_allocated_name(
    ::std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (name != NULL) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.name)
}

// string full_name = 2;
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::clear_full_name() {
  full_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& TrackableObjectGraph_TrackableObject_SerializedTensor::full_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.full_name)
  return full_name_.Get();
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_full_name(const ::std::string& value) {
  
  full_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.full_name)
}
#if LANG_CXX11
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_full_name(::std::string&& value) {
  
  full_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.full_name)
}
#endif
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_full_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  full_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.full_name)
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_full_name(const char* value,
    size_t size) {
  
  full_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.full_name)
}
inline ::std::string* TrackableObjectGraph_TrackableObject_SerializedTensor::mutable_full_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.full_name)
  return full_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* TrackableObjectGraph_TrackableObject_SerializedTensor::release_full_name() {
  // @@protoc_insertion_point(field_release:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.full_name)
  
  return full_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_allocated_full_name(::std::string* full_name) {
  if (full_name != NULL) {
    
  } else {
    
  }
  full_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), full_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.full_name)
}
inline ::std::string* TrackableObjectGraph_TrackableObject_SerializedTensor::unsafe_arena_release_full_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.full_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return full_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::unsafe_arena_set_allocated_full_name(
    ::std::string* full_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (full_name != NULL) {
    
  } else {
    
  }
  full_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      full_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.full_name)
}

// string checkpoint_key = 3;
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::clear_checkpoint_key() {
  checkpoint_key_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& TrackableObjectGraph_TrackableObject_SerializedTensor::checkpoint_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.checkpoint_key)
  return checkpoint_key_.Get();
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_checkpoint_key(const ::std::string& value) {
  
  checkpoint_key_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.checkpoint_key)
}
#if LANG_CXX11
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_checkpoint_key(::std::string&& value) {
  
  checkpoint_key_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.checkpoint_key)
}
#endif
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_checkpoint_key(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  checkpoint_key_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.checkpoint_key)
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_checkpoint_key(const char* value,
    size_t size) {
  
  checkpoint_key_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.checkpoint_key)
}
inline ::std::string* TrackableObjectGraph_TrackableObject_SerializedTensor::mutable_checkpoint_key() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.checkpoint_key)
  return checkpoint_key_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* TrackableObjectGraph_TrackableObject_SerializedTensor::release_checkpoint_key() {
  // @@protoc_insertion_point(field_release:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.checkpoint_key)
  
  return checkpoint_key_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_allocated_checkpoint_key(::std::string* checkpoint_key) {
  if (checkpoint_key != NULL) {
    
  } else {
    
  }
  checkpoint_key_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), checkpoint_key,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.checkpoint_key)
}
inline ::std::string* TrackableObjectGraph_TrackableObject_SerializedTensor::unsafe_arena_release_checkpoint_key() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.checkpoint_key)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return checkpoint_key_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::unsafe_arena_set_allocated_checkpoint_key(
    ::std::string* checkpoint_key) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (checkpoint_key != NULL) {
    
  } else {
    
  }
  checkpoint_key_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      checkpoint_key, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.checkpoint_key)
}

// bool optional_restore = 4;
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::clear_optional_restore() {
  optional_restore_ = false;
}
inline bool TrackableObjectGraph_TrackableObject_SerializedTensor::optional_restore() const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.optional_restore)
  return optional_restore_;
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_optional_restore(bool value) {
  
  optional_restore_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.optional_restore)
}

// -------------------------------------------------------------------

// TrackableObjectGraph_TrackableObject_SlotVariableReference

// int32 original_variable_node_id = 1;
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::clear_original_variable_node_id() {
  original_variable_node_id_ = 0;
}
inline ::google::protobuf::int32 TrackableObjectGraph_TrackableObject_SlotVariableReference::original_variable_node_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.original_variable_node_id)
  return original_variable_node_id_;
}
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::set_original_variable_node_id(::google::protobuf::int32 value) {
  
  original_variable_node_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.original_variable_node_id)
}

// string slot_name = 2;
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::clear_slot_name() {
  slot_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& TrackableObjectGraph_TrackableObject_SlotVariableReference::slot_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_name)
  return slot_name_.Get();
}
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::set_slot_name(const ::std::string& value) {
  
  slot_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_name)
}
#if LANG_CXX11
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::set_slot_name(::std::string&& value) {
  
  slot_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_name)
}
#endif
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::set_slot_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  slot_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_name)
}
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::set_slot_name(const char* value,
    size_t size) {
  
  slot_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_name)
}
inline ::std::string* TrackableObjectGraph_TrackableObject_SlotVariableReference::mutable_slot_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_name)
  return slot_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* TrackableObjectGraph_TrackableObject_SlotVariableReference::release_slot_name() {
  // @@protoc_insertion_point(field_release:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_name)
  
  return slot_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::set_allocated_slot_name(::std::string* slot_name) {
  if (slot_name != NULL) {
    
  } else {
    
  }
  slot_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), slot_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_name)
}
inline ::std::string* TrackableObjectGraph_TrackableObject_SlotVariableReference::unsafe_arena_release_slot_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return slot_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::unsafe_arena_set_allocated_slot_name(
    ::std::string* slot_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (slot_name != NULL) {
    
  } else {
    
  }
  slot_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      slot_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_name)
}

// int32 slot_variable_node_id = 3;
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::clear_slot_variable_node_id() {
  slot_variable_node_id_ = 0;
}
inline ::google::protobuf::int32 TrackableObjectGraph_TrackableObject_SlotVariableReference::slot_variable_node_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_variable_node_id)
  return slot_variable_node_id_;
}
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::set_slot_variable_node_id(::google::protobuf::int32 value) {
  
  slot_variable_node_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_variable_node_id)
}

// -------------------------------------------------------------------

// TrackableObjectGraph_TrackableObject

// repeated .tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference children = 1;
inline int TrackableObjectGraph_TrackableObject::children_size() const {
  return children_.size();
}
inline void TrackableObjectGraph_TrackableObject::clear_children() {
  children_.Clear();
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* TrackableObjectGraph_TrackableObject::mutable_children(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.TrackableObjectGraph.TrackableObject.children)
  return children_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >*
TrackableObjectGraph_TrackableObject::mutable_children() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TrackableObjectGraph.TrackableObject.children)
  return &children_;
}
inline const ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference& TrackableObjectGraph_TrackableObject::children(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.children)
  return children_.Get(index);
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* TrackableObjectGraph_TrackableObject::add_children() {
  // @@protoc_insertion_point(field_add:tensorflow.TrackableObjectGraph.TrackableObject.children)
  return children_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >&
TrackableObjectGraph_TrackableObject::children() const {
  // @@protoc_insertion_point(field_list:tensorflow.TrackableObjectGraph.TrackableObject.children)
  return children_;
}

// repeated .tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor attributes = 2;
inline int TrackableObjectGraph_TrackableObject::attributes_size() const {
  return attributes_.size();
}
inline void TrackableObjectGraph_TrackableObject::clear_attributes() {
  attributes_.Clear();
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor* TrackableObjectGraph_TrackableObject::mutable_attributes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.TrackableObjectGraph.TrackableObject.attributes)
  return attributes_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor >*
TrackableObjectGraph_TrackableObject::mutable_attributes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TrackableObjectGraph.TrackableObject.attributes)
  return &attributes_;
}
inline const ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor& TrackableObjectGraph_TrackableObject::attributes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.attributes)
  return attributes_.Get(index);
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor* TrackableObjectGraph_TrackableObject::add_attributes() {
  // @@protoc_insertion_point(field_add:tensorflow.TrackableObjectGraph.TrackableObject.attributes)
  return attributes_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor >&
TrackableObjectGraph_TrackableObject::attributes() const {
  // @@protoc_insertion_point(field_list:tensorflow.TrackableObjectGraph.TrackableObject.attributes)
  return attributes_;
}

// repeated .tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference slot_variables = 3;
inline int TrackableObjectGraph_TrackableObject::slot_variables_size() const {
  return slot_variables_.size();
}
inline void TrackableObjectGraph_TrackableObject::clear_slot_variables() {
  slot_variables_.Clear();
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* TrackableObjectGraph_TrackableObject::mutable_slot_variables(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.TrackableObjectGraph.TrackableObject.slot_variables)
  return slot_variables_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference >*
TrackableObjectGraph_TrackableObject::mutable_slot_variables() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TrackableObjectGraph.TrackableObject.slot_variables)
  return &slot_variables_;
}
inline const ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference& TrackableObjectGraph_TrackableObject::slot_variables(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.slot_variables)
  return slot_variables_.Get(index);
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* TrackableObjectGraph_TrackableObject::add_slot_variables() {
  // @@protoc_insertion_point(field_add:tensorflow.TrackableObjectGraph.TrackableObject.slot_variables)
  return slot_variables_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference >&
TrackableObjectGraph_TrackableObject::slot_variables() const {
  // @@protoc_insertion_point(field_list:tensorflow.TrackableObjectGraph.TrackableObject.slot_variables)
  return slot_variables_;
}

// -------------------------------------------------------------------

// TrackableObjectGraph

// repeated .tensorflow.TrackableObjectGraph.TrackableObject nodes = 1;
inline int TrackableObjectGraph::nodes_size() const {
  return nodes_.size();
}
inline void TrackableObjectGraph::clear_nodes() {
  nodes_.Clear();
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject* TrackableObjectGraph::mutable_nodes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.TrackableObjectGraph.nodes)
  return nodes_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject >*
TrackableObjectGraph::mutable_nodes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TrackableObjectGraph.nodes)
  return &nodes_;
}
inline const ::tensorflow::TrackableObjectGraph_TrackableObject& TrackableObjectGraph::nodes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.nodes)
  return nodes_.Get(index);
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject* TrackableObjectGraph::add_nodes() {
  // @@protoc_insertion_point(field_add:tensorflow.TrackableObjectGraph.nodes)
  return nodes_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject >&
TrackableObjectGraph::nodes() const {
  // @@protoc_insertion_point(field_list:tensorflow.TrackableObjectGraph.nodes)
  return nodes_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/trackable_object_graph.proto

#include "tensorflow/core/protobuf/trackable_object_graph.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_TrackableObjectGraph_TrackableObject_ObjectReference;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_TrackableObjectGraph_TrackableObject_SerializedTensor;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_TrackableObjectGraph_TrackableObject_SlotVariableReference;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<3> scc_info_TrackableObjectGraph_TrackableObject;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto
namespace tensorflow {
class TrackableObjectGraph_TrackableObject_ObjectReferenceDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TrackableObjectGraph_TrackableObject_ObjectReference>
      _instance;
} _TrackableObjectGraph_TrackableObject_ObjectReference_default_instance_;
class TrackableObjectGraph_TrackableObject_SerializedTensorDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TrackableObjectGraph_TrackableObject_SerializedTensor>
      _instance;
} _TrackableObjectGraph_TrackableObject_SerializedTensor_default_instance_;
class TrackableObjectGraph_TrackableObject_SlotVariableReferenceDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TrackableObjectGraph_TrackableObject_SlotVariableReference>
      _instance;
} _TrackableObjectGraph_TrackableObject_SlotVariableReference_default_instance_;
class TrackableObjectGraph_TrackableObjectDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TrackableObjectGraph_TrackableObject>
      _instance;
} _TrackableObjectGraph_TrackableObject_default_instance_;
class TrackableObjectGraphDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TrackableObjectGraph>
      _instance;
} _TrackableObjectGraph_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto {
static void InitDefaultsTrackableObjectGraph_TrackableObject_ObjectReference() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_TrackableObjectGraph_TrackableObject_ObjectReference_default_instance_;
    new (ptr) ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_TrackableObjectGraph_TrackableObject_ObjectReference =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsTrackableObjectGraph_TrackableObject_ObjectReference}, {}};

static void InitDefaultsTrackableObjectGraph_TrackableObject_SerializedTensor() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_TrackableObjectGraph_TrackableObject_SerializedTensor_default_instance_;
    new (ptr) ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_TrackableObjectGraph_TrackableObject_SerializedTensor =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsTrackableObjectGraph_TrackableObject_SerializedTensor}, {}};

static void InitDefaultsTrackableObjectGraph_TrackableObject_SlotVariableReference() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_TrackableObjectGraph_TrackableObject_SlotVariableReference_default_instance_;
    new (ptr) ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_TrackableObjectGraph_TrackableObject_SlotVariableReference =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsTrackableObjectGraph_TrackableObject_SlotVariableReference}, {}};

static void InitDefaultsTrackableObjectGraph_TrackableObject() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_TrackableObjectGraph_TrackableObject_default_instance_;
    new (ptr) ::tensorflow::TrackableObjectGraph_TrackableObject();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::TrackableObjectGraph_TrackableObject::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<3> scc_info_TrackableObjectGraph_TrackableObject =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 3, InitDefaultsTrackableObjectGraph_TrackableObject}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::scc_info_TrackableObjectGraph_TrackableObject_ObjectReference.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::scc_info_TrackableObjectGraph_TrackableObject_SerializedTensor.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::scc_info_TrackableObjectGraph_TrackableObject_SlotVariableReference.base,}};

static void InitDefaultsTrackableObjectGraph() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_TrackableObjectGraph_default_instance_;
    new (ptr) ::tensorflow::TrackableObjectGraph();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::TrackableObjectGraph::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_TrackableObjectGraph =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsTrackableObjectGraph}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::scc_info_TrackableObjectGraph_TrackableObject.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_TrackableObjectGraph_TrackableObject_ObjectReference.base);
  ::google::protobuf::internal::InitSCC(&scc_info_TrackableObjectGraph_TrackableObject_SerializedTensor.base);
  ::google::protobuf::internal::InitSCC(&scc_info_TrackableObjectGraph_TrackableObject_SlotVariableReference.base);
  ::google::protobuf::internal::InitSCC(&scc_info_TrackableObjectGraph_TrackableObject.base);
  ::google::protobuf::internal::InitSCC(&scc_info_TrackableObjectGraph.base);
}

::google::protobuf::Metadata file_level_metadata[5];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference, node_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference, local_name_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor, full_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor, checkpoint_key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor, optional_restore_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference, original_variable_node_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference, slot_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference, slot_variable_node_id_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TrackableObjectGraph_TrackableObject, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TrackableObjectGraph_TrackableObject, children_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TrackableObjectGraph_TrackableObject, attributes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TrackableObjectGraph_TrackableObject, slot_variables_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TrackableObjectGraph, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TrackableObjectGraph, nodes_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference)},
  { 7, -1, sizeof(::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor)},
  { 16, -1, sizeof(::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference)},
  { 24, -1, sizeof(::tensorflow::TrackableObjectGraph_TrackableObject)},
  { 32, -1, sizeof(::tensorflow::TrackableObjectGraph)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_TrackableObjectGraph_TrackableObject_ObjectReference_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_TrackableObjectGraph_TrackableObject_SerializedTensor_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_TrackableObjectGraph_TrackableObject_SlotVariableReference_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_TrackableObjectGraph_TrackableObject_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_TrackableObjectGraph_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/protobuf/trackable_object_graph.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 5);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n5tensorflow/core/protobuf/trackable_obj"
      "ect_graph.proto\022\ntensorflow\"\203\005\n\024Trackabl"
      "eObjectGraph\022\?\n\005nodes\030\001 \003(\01320.tensorflow"
      ".TrackableObjectGraph.TrackableObject\032\251\004"
      "\n\017TrackableObject\022R\n\010children\030\001 \003(\0132@.te"
      "nsorflow.TrackableObjectGraph.TrackableO"
      "bject.ObjectReference\022U\n\nattributes\030\002 \003("
      "\0132A.tensorflow.TrackableObjectGraph.Trac"
      "kableObject.SerializedTensor\022^\n\016slot_var"
      "iables\030\003 \003(\0132F.tensorflow.TrackableObjec"
      "tGraph.TrackableObject.SlotVariableRefer"
      "ence\0326\n\017ObjectReference\022\017\n\007node_id\030\001 \001(\005"
      "\022\022\n\nlocal_name\030\002 \001(\t\032e\n\020SerializedTensor"
      "\022\014\n\004name\030\001 \001(\t\022\021\n\tfull_name\030\002 \001(\t\022\026\n\016che"
      "ckpoint_key\030\003 \001(\t\022\030\n\020optional_restore\030\004 "
      "\001(\010\032l\n\025SlotVariableReference\022!\n\031original"
      "_variable_node_id\030\001 \001(\005\022\021\n\tslot_name\030\002 \001"
      "(\t\022\035\n\025slot_variable_node_id\030\003 \001(\005B\003\370\001\001b\006"
      "proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 726);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/protobuf/trackable_object_graph.proto", &protobuf_RegisterTypes);
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto
namespace tensorflow {

// ===================================================================

void TrackableObjectGraph_TrackableObject_ObjectReference::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TrackableObjectGraph_TrackableObject_ObjectReference::kNodeIdFieldNumber;
const int TrackableObjectGraph_TrackableObject_ObjectReference::kLocalNameFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TrackableObjectGraph_TrackableObject_ObjectReference::TrackableObjectGraph_TrackableObject_ObjectReference()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::scc_info_TrackableObjectGraph_TrackableObject_ObjectReference.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference)
}
TrackableObjectGraph_TrackableObject_ObjectReference::TrackableObjectGraph_TrackableObject_ObjectReference(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::scc_info_TrackableObjectGraph_TrackableObject_ObjectReference.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference)
}
TrackableObjectGraph_TrackableObject_ObjectReference::TrackableObjectGraph_TrackableObject_ObjectReference(const TrackableObjectGraph_TrackableObject_ObjectReference& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  local_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.local_name().size() > 0) {
    local_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.local_name(),
      GetArenaNoVirtual());
  }
  node_id_ = from.node_id_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference)
}

void TrackableObjectGraph_TrackableObject_ObjectReference::SharedCtor() {
  local_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  node_id_ = 0;
}

TrackableObjectGraph_TrackableObject_ObjectReference::~TrackableObjectGraph_TrackableObject_ObjectReference() {
  // @@protoc_insertion_point(destructor:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference)
  SharedDtor();
}

void TrackableObjectGraph_TrackableObject_ObjectReference::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  local_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void TrackableObjectGraph_TrackableObject_ObjectReference::ArenaDtor(void* object) {
  TrackableObjectGraph_TrackableObject_ObjectReference* _this = reinterpret_cast< TrackableObjectGraph_TrackableObject_ObjectReference* >(object);
  (void)_this;
}
void TrackableObjectGraph_TrackableObject_ObjectReference::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TrackableObjectGraph_TrackableObject_ObjectReference::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* TrackableObjectGraph_TrackableObject_ObjectReference::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const TrackableObjectGraph_TrackableObject_ObjectReference& TrackableObjectGraph_TrackableObject_ObjectReference::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::scc_info_TrackableObjectGraph_TrackableObject_ObjectReference.base);
  return *internal_default_instance();
}


void TrackableObjectGraph_TrackableObject_ObjectReference::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  local_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  node_id_ = 0;
  _internal_metadata_.Clear();
}

bool TrackableObjectGraph_TrackableObject_ObjectReference::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 node_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &node_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string local_name = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_local_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->local_name().data(), static_cast<int>(this->local_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.local_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference)
  return false;
#undef DO_
}

void TrackableObjectGraph_TrackableObject_ObjectReference::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 node_id = 1;
  if (this->node_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->node_id(), output);
  }

  // string local_name = 2;
  if (this->local_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->local_name().data(), static_cast<int>(this->local_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.local_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->local_name(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference)
}

::google::protobuf::uint8* TrackableObjectGraph_TrackableObject_ObjectReference::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 node_id = 1;
  if (this->node_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->node_id(), target);
  }

  // string local_name = 2;
  if (this->local_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->local_name().data(), static_cast<int>(this->local_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.local_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->local_name(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference)
  return target;
}

size_t TrackableObjectGraph_TrackableObject_ObjectReference::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string local_name = 2;
  if (this->local_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->local_name());
  }

  // int32 node_id = 1;
  if (this->node_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->node_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TrackableObjectGraph_TrackableObject_ObjectReference::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference)
  GOOGLE_DCHECK_NE(&from, this);
  const TrackableObjectGraph_TrackableObject_ObjectReference* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TrackableObjectGraph_TrackableObject_ObjectReference>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference)
    MergeFrom(*source);
  }
}

void TrackableObjectGraph_TrackableObject_ObjectReference::MergeFrom(const TrackableObjectGraph_TrackableObject_ObjectReference& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.local_name().size() > 0) {
    set_local_name(from.local_name());
  }
  if (from.node_id() != 0) {
    set_node_id(from.node_id());
  }
}

void TrackableObjectGraph_TrackableObject_ObjectReference::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TrackableObjectGraph_TrackableObject_ObjectReference::CopyFrom(const TrackableObjectGraph_TrackableObject_ObjectReference& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TrackableObjectGraph_TrackableObject_ObjectReference::IsInitialized() const {
  return true;
}

void TrackableObjectGraph_TrackableObject_ObjectReference::Swap(TrackableObjectGraph_TrackableObject_ObjectReference* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TrackableObjectGraph_TrackableObject_ObjectReference* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void TrackableObjectGraph_TrackableObject_ObjectReference::UnsafeArenaSwap(TrackableObjectGraph_TrackableObject_ObjectReference* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TrackableObjectGraph_TrackableObject_ObjectReference::InternalSwap(TrackableObjectGraph_TrackableObject_ObjectReference* other) {
  using std::swap;
  local_name_.Swap(&other->local_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(node_id_, other->node_id_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata TrackableObjectGraph_TrackableObject_ObjectReference::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void TrackableObjectGraph_TrackableObject_SerializedTensor::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TrackableObjectGraph_TrackableObject_SerializedTensor::kNameFieldNumber;
const int TrackableObjectGraph_TrackableObject_SerializedTensor::kFullNameFieldNumber;
const int TrackableObjectGraph_TrackableObject_SerializedTensor::kCheckpointKeyFieldNumber;
const int TrackableObjectGraph_TrackableObject_SerializedTensor::kOptionalRestoreFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TrackableObjectGraph_TrackableObject_SerializedTensor::TrackableObjectGraph_TrackableObject_SerializedTensor()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::scc_info_TrackableObjectGraph_TrackableObject_SerializedTensor.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor)
}
TrackableObjectGraph_TrackableObject_SerializedTensor::TrackableObjectGraph_TrackableObject_SerializedTensor(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::scc_info_TrackableObjectGraph_TrackableObject_SerializedTensor.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor)
}
TrackableObjectGraph_TrackableObject_SerializedTensor::TrackableObjectGraph_TrackableObject_SerializedTensor(const TrackableObjectGraph_TrackableObject_SerializedTensor& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name(),
      GetArenaNoVirtual());
  }
  full_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.full_name().size() > 0) {
    full_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.full_name(),
      GetArenaNoVirtual());
  }
  checkpoint_key_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.checkpoint_key().size() > 0) {
    checkpoint_key_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.checkpoint_key(),
      GetArenaNoVirtual());
  }
  optional_restore_ = from.optional_restore_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor)
}

void TrackableObjectGraph_TrackableObject_SerializedTensor::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  full_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  checkpoint_key_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optional_restore_ = false;
}

TrackableObjectGraph_TrackableObject_SerializedTensor::~TrackableObjectGraph_TrackableObject_SerializedTensor() {
  // @@protoc_insertion_point(destructor:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor)
  SharedDtor();
}

void TrackableObjectGraph_TrackableObject_SerializedTensor::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  full_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  checkpoint_key_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void TrackableObjectGraph_TrackableObject_SerializedTensor::ArenaDtor(void* object) {
  TrackableObjectGraph_TrackableObject_SerializedTensor* _this = reinterpret_cast< TrackableObjectGraph_TrackableObject_SerializedTensor* >(object);
  (void)_this;
}
void TrackableObjectGraph_TrackableObject_SerializedTensor::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TrackableObjectGraph_TrackableObject_SerializedTensor::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* TrackableObjectGraph_TrackableObject_SerializedTensor::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const TrackableObjectGraph_TrackableObject_SerializedTensor& TrackableObjectGraph_TrackableObject_SerializedTensor::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::scc_info_TrackableObjectGraph_TrackableObject_SerializedTensor.base);
  return *internal_default_instance();
}


void TrackableObjectGraph_TrackableObject_SerializedTensor::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  full_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  checkpoint_key_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  optional_restore_ = false;
  _internal_metadata_.Clear();
}

bool TrackableObjectGraph_TrackableObject_SerializedTensor::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string full_name = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_full_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->full_name().data(), static_cast<int>(this->full_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.full_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string checkpoint_key = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_checkpoint_key()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->checkpoint_key().data(), static_cast<int>(this->checkpoint_key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.checkpoint_key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool optional_restore = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &optional_restore_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor)
  return false;
#undef DO_
}

void TrackableObjectGraph_TrackableObject_SerializedTensor::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // string full_name = 2;
  if (this->full_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->full_name().data(), static_cast<int>(this->full_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.full_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->full_name(), output);
  }

  // string checkpoint_key = 3;
  if (this->checkpoint_key().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->checkpoint_key().data(), static_cast<int>(this->checkpoint_key().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.checkpoint_key");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->checkpoint_key(), output);
  }

  // bool optional_restore = 4;
  if (this->optional_restore() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(4, this->optional_restore(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor)
}

::google::protobuf::uint8* TrackableObjectGraph_TrackableObject_SerializedTensor::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // string full_name = 2;
  if (this->full_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->full_name().data(), static_cast<int>(this->full_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.full_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->full_name(), target);
  }

  // string checkpoint_key = 3;
  if (this->checkpoint_key().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->checkpoint_key().data(), static_cast<int>(this->checkpoint_key().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.checkpoint_key");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->checkpoint_key(), target);
  }

  // bool optional_restore = 4;
  if (this->optional_restore() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(4, this->optional_restore(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor)
  return target;
}

size_t TrackableObjectGraph_TrackableObject_SerializedTensor::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // string full_name = 2;
  if (this->full_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->full_name());
  }

  // string checkpoint_key = 3;
  if (this->checkpoint_key().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->checkpoint_key());
  }

  // bool optional_restore = 4;
  if (this->optional_restore() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TrackableObjectGraph_TrackableObject_SerializedTensor::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor)
  GOOGLE_DCHECK_NE(&from, this);
  const TrackableObjectGraph_TrackableObject_SerializedTensor* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TrackableObjectGraph_TrackableObject_SerializedTensor>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor)
    MergeFrom(*source);
  }
}

void TrackableObjectGraph_TrackableObject_SerializedTensor::MergeFrom(const TrackableObjectGraph_TrackableObject_SerializedTensor& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.name().size() > 0) {
    set_name(from.name());
  }
  if (from.full_name().size() > 0) {
    set_full_name(from.full_name());
  }
  if (from.checkpoint_key().size() > 0) {
    set_checkpoint_key(from.checkpoint_key());
  }
  if (from.optional_restore() != 0) {
    set_optional_restore(from.optional_restore());
  }
}

void TrackableObjectGraph_TrackableObject_SerializedTensor::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TrackableObjectGraph_TrackableObject_SerializedTensor::CopyFrom(const TrackableObjectGraph_TrackableObject_SerializedTensor& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TrackableObjectGraph_TrackableObject_SerializedTensor::IsInitialized() const {
  return true;
}

void TrackableObjectGraph_TrackableObject_SerializedTensor::Swap(TrackableObjectGraph_TrackableObject_SerializedTensor* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TrackableObjectGraph_TrackableObject_SerializedTensor* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void TrackableObjectGraph_TrackableObject_SerializedTensor::UnsafeArenaSwap(TrackableObjectGraph_TrackableObject_SerializedTensor* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TrackableObjectGraph_TrackableObject_SerializedTensor::InternalSwap(TrackableObjectGraph_TrackableObject_SerializedTensor* other) {
  using std::swap;
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  full_name_.Swap(&other->full_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  checkpoint_key_.Swap(&other->checkpoint_key_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(optional_restore_, other->optional_restore_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata TrackableObjectGraph_TrackableObject_SerializedTensor::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void TrackableObjectGraph_TrackableObject_SlotVariableReference::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TrackableObjectGraph_TrackableObject_SlotVariableReference::kOriginalVariableNodeIdFieldNumber;
const int TrackableObjectGraph_TrackableObject_SlotVariableReference::kSlotNameFieldNumber;
const int TrackableObjectGraph_TrackableObject_SlotVariableReference::kSlotVariableNodeIdFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TrackableObjectGraph_TrackableObject_SlotVariableReference::TrackableObjectGraph_TrackableObject_SlotVariableReference()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::scc_info_TrackableObjectGraph_TrackableObject_SlotVariableReference.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference)
}
TrackableObjectGraph_TrackableObject_SlotVariableReference::TrackableObjectGraph_TrackableObject_SlotVariableReference(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::scc_info_TrackableObjectGraph_TrackableObject_SlotVariableReference.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference)
}
TrackableObjectGraph_TrackableObject_SlotVariableReference::TrackableObjectGraph_TrackableObject_SlotVariableReference(const TrackableObjectGraph_TrackableObject_SlotVariableReference& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  slot_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.slot_name().size() > 0) {
    slot_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.slot_name(),
      GetArenaNoVirtual());
  }
  ::memcpy(&original_variable_node_id_, &from.original_variable_node_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&slot_variable_node_id_) -
    reinterpret_cast<char*>(&original_variable_node_id_)) + sizeof(slot_variable_node_id_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference)
}

void TrackableObjectGraph_TrackableObject_SlotVariableReference::SharedCtor() {
  slot_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&original_variable_node_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&slot_variable_node_id_) -
      reinterpret_cast<char*>(&original_variable_node_id_)) + sizeof(slot_variable_node_id_));
}

TrackableObjectGraph_TrackableObject_SlotVariableReference::~TrackableObjectGraph_TrackableObject_SlotVariableReference() {
  // @@protoc_insertion_point(destructor:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference)
  SharedDtor();
}

void TrackableObjectGraph_TrackableObject_SlotVariableReference::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  slot_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void TrackableObjectGraph_TrackableObject_SlotVariableReference::ArenaDtor(void* object) {
  TrackableObjectGraph_TrackableObject_SlotVariableReference* _this = reinterpret_cast< TrackableObjectGraph_TrackableObject_SlotVariableReference* >(object);
  (void)_this;
}
void TrackableObjectGraph_TrackableObject_SlotVariableReference::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TrackableObjectGraph_TrackableObject_SlotVariableReference::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* TrackableObjectGraph_TrackableObject_SlotVariableReference::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const TrackableObjectGraph_TrackableObject_SlotVariableReference& TrackableObjectGraph_TrackableObject_SlotVariableReference::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::scc_info_TrackableObjectGraph_TrackableObject_SlotVariableReference.base);
  return *internal_default_instance();
}


void TrackableObjectGraph_TrackableObject_SlotVariableReference::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  slot_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  ::memset(&original_variable_node_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&slot_variable_node_id_) -
      reinterpret_cast<char*>(&original_variable_node_id_)) + sizeof(slot_variable_node_id_));
  _internal_metadata_.Clear();
}

bool TrackableObjectGraph_TrackableObject_SlotVariableReference::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 original_variable_node_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &original_variable_node_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string slot_name = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_slot_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->slot_name().data(), static_cast<int>(this->slot_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 slot_variable_node_id = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &slot_variable_node_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference)
  return false;
#undef DO_
}

void TrackableObjectGraph_TrackableObject_SlotVariableReference::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 original_variable_node_id = 1;
  if (this->original_variable_node_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->original_variable_node_id(), output);
  }

  // string slot_name = 2;
  if (this->slot_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->slot_name().data(), static_cast<int>(this->slot_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->slot_name(), output);
  }

  // int32 slot_variable_node_id = 3;
  if (this->slot_variable_node_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->slot_variable_node_id(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference)
}

::google::protobuf::uint8* TrackableObjectGraph_TrackableObject_SlotVariableReference::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 original_variable_node_id = 1;
  if (this->original_variable_node_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->original_variable_node_id(), target);
  }

  // string slot_name = 2;
  if (this->slot_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->slot_name().data(), static_cast<int>(this->slot_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->slot_name(), target);
  }

  // int32 slot_variable_node_id = 3;
  if (this->slot_variable_node_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->slot_variable_node_id(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference)
  return target;
}

size_t TrackableObjectGraph_TrackableObject_SlotVariableReference::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string slot_name = 2;
  if (this->slot_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->slot_name());
  }

  // int32 original_variable_node_id = 1;
  if (this->original_variable_node_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->original_variable_node_id());
  }

  // int32 slot_variable_node_id = 3;
  if (this->slot_variable_node_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->slot_variable_node_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TrackableObjectGraph_TrackableObject_SlotVariableReference::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference)
  GOOGLE_DCHECK_NE(&from, this);
  const TrackableObjectGraph_TrackableObject_SlotVariableReference* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TrackableObjectGraph_TrackableObject_SlotVariableReference>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference)
    MergeFrom(*source);
  }
}

void TrackableObjectGraph_TrackableObject_SlotVariableReference::MergeFrom(const TrackableObjectGraph_TrackableObject_SlotVariableReference& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.slot_name().size() > 0) {
    set_slot_name(from.slot_name());
  }
  if (from.original_variable_node_id() != 0) {
    set_original_variable_node_id(from.original_variable_node_id());
  }
  if (from.slot_variable_node_id() != 0) {
    set_slot_variable_node_id(from.slot_variable_node_id());
  }
}

void TrackableObjectGraph_TrackableObject_SlotVariableReference::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TrackableObjectGraph_TrackableObject_SlotVariableReference::CopyFrom(const TrackableObjectGraph_TrackableObject_SlotVariableReference& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TrackableObjectGraph_TrackableObject_SlotVariableReference::IsInitialized() const {
  return true;
}

void TrackableObjectGraph_TrackableObject_SlotVariableReference::Swap(TrackableObjectGraph_TrackableObject_SlotVariableReference* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TrackableObjectGraph_TrackableObject_SlotVariableReference* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void TrackableObjectGraph_TrackableObject_SlotVariableReference::UnsafeArenaSwap(TrackableObjectGraph_TrackableObject_SlotVariableReference* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TrackableObjectGraph_TrackableObject_SlotVariableReference::InternalSwap(TrackableObjectGraph_TrackableObject_SlotVariableReference* other) {
  using std::swap;
  slot_name_.Swap(&other->slot_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(original_variable_node_id_, other->original_variable_node_id_);
  swap(slot_variable_node_id_, other->slot_variable_node_id_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata TrackableObjectGraph_TrackableObject_SlotVariableReference::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void TrackableObjectGraph_TrackableObject::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TrackableObjectGraph_TrackableObject::kChildrenFieldNumber;
const int TrackableObjectGraph_TrackableObject::kAttributesFieldNumber;
const int TrackableObjectGraph_TrackableObject::kSlotVariablesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TrackableObjectGraph_TrackableObject::TrackableObjectGraph_TrackableObject()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::scc_info_TrackableObjectGraph_TrackableObject.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.TrackableObjectGraph.TrackableObject)
}
TrackableObjectGraph_TrackableObject::TrackableObjectGraph_TrackableObject(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  children_(arena),
  attributes_(arena),
  slot_variables_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::scc_info_TrackableObjectGraph_TrackableObject.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.TrackableObjectGraph.TrackableObject)
}
TrackableObjectGraph_TrackableObject::TrackableObjectGraph_TrackableObject(const TrackableObjectGraph_TrackableObject& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      children_(from.children_),
      attributes_(from.attributes_),
      slot_variables_(from.slot_variables_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.TrackableObjectGraph.TrackableObject)
}

void TrackableObjectGraph_TrackableObject::SharedCtor() {
}

TrackableObjectGraph_TrackableObject::~TrackableObjectGraph_TrackableObject() {
  // @@protoc_insertion_point(destructor:tensorflow.TrackableObjectGraph.TrackableObject)
  SharedDtor();
}

void TrackableObjectGraph_TrackableObject::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void TrackableObjectGraph_TrackableObject::ArenaDtor(void* object) {
  TrackableObjectGraph_TrackableObject* _this = reinterpret_cast< TrackableObjectGraph_TrackableObject* >(object);
  (void)_this;
}
void TrackableObjectGraph_TrackableObject::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TrackableObjectGraph_TrackableObject::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* TrackableObjectGraph_TrackableObject::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const TrackableObjectGraph_TrackableObject& TrackableObjectGraph_TrackableObject::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::scc_info_TrackableObjectGraph_TrackableObject.base);
  return *internal_default_instance();
}


void TrackableObjectGraph_TrackableObject::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.TrackableObjectGraph.TrackableObject)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  children_.Clear();
  attributes_.Clear();
  slot_variables_.Clear();
  _internal_metadata_.Clear();
}

bool TrackableObjectGraph_TrackableObject::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.TrackableObjectGraph.TrackableObject)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference children = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_children()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor attributes = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_attributes()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference slot_variables = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_slot_variables()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.TrackableObjectGraph.TrackableObject)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.TrackableObjectGraph.TrackableObject)
  return false;
#undef DO_
}

void TrackableObjectGraph_TrackableObject::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.TrackableObjectGraph.TrackableObject)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference children = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->children_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->children(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor attributes = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->attributes_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2,
      this->attributes(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference slot_variables = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->slot_variables_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3,
      this->slot_variables(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.TrackableObjectGraph.TrackableObject)
}

::google::protobuf::uint8* TrackableObjectGraph_TrackableObject::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.TrackableObjectGraph.TrackableObject)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference children = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->children_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->children(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor attributes = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->attributes_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->attributes(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference slot_variables = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->slot_variables_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->slot_variables(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.TrackableObjectGraph.TrackableObject)
  return target;
}

size_t TrackableObjectGraph_TrackableObject::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.TrackableObjectGraph.TrackableObject)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference children = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->children_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->children(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor attributes = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->attributes_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->attributes(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference slot_variables = 3;
  {
    unsigned int count = static_cast<unsigned int>(this->slot_variables_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->slot_variables(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TrackableObjectGraph_TrackableObject::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.TrackableObjectGraph.TrackableObject)
  GOOGLE_DCHECK_NE(&from, this);
  const TrackableObjectGraph_TrackableObject* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TrackableObjectGraph_TrackableObject>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.TrackableObjectGraph.TrackableObject)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.TrackableObjectGraph.TrackableObject)
    MergeFrom(*source);
  }
}

void TrackableObjectGraph_TrackableObject::MergeFrom(const TrackableObjectGraph_TrackableObject& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.TrackableObjectGraph.TrackableObject)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  children_.MergeFrom(from.children_);
  attributes_.MergeFrom(from.attributes_);
  slot_variables_.MergeFrom(from.slot_variables_);
}

void TrackableObjectGraph_TrackableObject::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.TrackableObjectGraph.TrackableObject)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TrackableObjectGraph_TrackableObject::CopyFrom(const TrackableObjectGraph_TrackableObject& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.TrackableObjectGraph.TrackableObject)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TrackableObjectGraph_TrackableObject::IsInitialized() const {
  return true;
}

void TrackableObjectGraph_TrackableObject::Swap(TrackableObjectGraph_TrackableObject* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TrackableObjectGraph_TrackableObject* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void TrackableObjectGraph_TrackableObject::UnsafeArenaSwap(TrackableObjectGraph_TrackableObject* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TrackableObjectGraph_TrackableObject::InternalSwap(TrackableObjectGraph_TrackableObject* other) {
  using std::swap;
  CastToBase(&children_)->InternalSwap(CastToBase(&other->children_));
  CastToBase(&attributes_)->InternalSwap(CastToBase(&other->attributes_));
  CastToBase(&slot_variables_)->InternalSwap(CastToBase(&other->slot_variables_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata TrackableObjectGraph_TrackableObject::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void TrackableObjectGraph::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TrackableObjectGraph::kNodesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TrackableObjectGraph::TrackableObjectGraph()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::scc_info_TrackableObjectGraph.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.TrackableObjectGraph)
}
TrackableObjectGraph::TrackableObjectGraph(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  nodes_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::scc_info_TrackableObjectGraph.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.TrackableObjectGraph)
}
TrackableObjectGraph::TrackableObjectGraph(const TrackableObjectGraph& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      nodes_(from.nodes_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.TrackableObjectGraph)
}

void TrackableObjectGraph::SharedCtor() {
}

TrackableObjectGraph::~TrackableObjectGraph() {
  // @@protoc_insertion_point(destructor:tensorflow.TrackableObjectGraph)
  SharedDtor();
}

void TrackableObjectGraph::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void TrackableObjectGraph::ArenaDtor(void* object) {
  TrackableObjectGraph* _this = reinterpret_cast< TrackableObjectGraph* >(object);
  (void)_this;
}
void TrackableObjectGraph::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TrackableObjectGraph::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* TrackableObjectGraph::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const TrackableObjectGraph& TrackableObjectGraph::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::scc_info_TrackableObjectGraph.base);
  return *internal_default_instance();
}


void TrackableObjectGraph::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.TrackableObjectGraph)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  nodes_.Clear();
  _internal_metadata_.Clear();
}

bool TrackableObjectGraph::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.TrackableObjectGraph)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.TrackableObjectGraph.TrackableObject nodes = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_nodes()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.TrackableObjectGraph)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.TrackableObjectGraph)
  return false;
#undef DO_
}

void TrackableObjectGraph::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.TrackableObjectGraph)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.TrackableObjectGraph.TrackableObject nodes = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->nodes_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->nodes(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.TrackableObjectGraph)
}

::google::protobuf::uint8* TrackableObjectGraph::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.TrackableObjectGraph)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.TrackableObjectGraph.TrackableObject nodes = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->nodes_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->nodes(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.TrackableObjectGraph)
  return target;
}

size_t TrackableObjectGraph::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.TrackableObjectGraph)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.TrackableObjectGraph.TrackableObject nodes = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->nodes_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->nodes(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TrackableObjectGraph::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.TrackableObjectGraph)
  GOOGLE_DCHECK_NE(&from, this);
  const TrackableObjectGraph* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TrackableObjectGraph>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.TrackableObjectGraph)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.TrackableObjectGraph)
    MergeFrom(*source);
  }
}

void TrackableObjectGraph::MergeFrom(const TrackableObjectGraph& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.TrackableObjectGraph)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  nodes_.MergeFrom(from.nodes_);
}

void TrackableObjectGraph::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.TrackableObjectGraph)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TrackableObjectGraph::CopyFrom(const TrackableObjectGraph& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.TrackableObjectGraph)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TrackableObjectGraph::IsInitialized() const {
  return true;
}

void TrackableObjectGraph::Swap(TrackableObjectGraph* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TrackableObjectGraph* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void TrackableObjectGraph::UnsafeArenaSwap(TrackableObjectGraph* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TrackableObjectGraph::InternalSwap(TrackableObjectGraph* other) {
  using std::swap;
  CastToBase(&nodes_)->InternalSwap(CastToBase(&other->nodes_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata TrackableObjectGraph::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* Arena::CreateMaybeMessage< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor* Arena::CreateMaybeMessage< ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* Arena::CreateMaybeMessage< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::TrackableObjectGraph_TrackableObject* Arena::CreateMaybeMessage< ::tensorflow::TrackableObjectGraph_TrackableObject >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::TrackableObjectGraph_TrackableObject >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::TrackableObjectGraph* Arena::CreateMaybeMessage< ::tensorflow::TrackableObjectGraph >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::TrackableObjectGraph >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/saved_object_graph.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/protobuf/trackable_object_graph.pb.h"
#include "tensorflow/core/protobuf/struct.pb.h"
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
#include "tensorflow/core/framework/versions.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto 

namespace protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[12];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto
namespace tensorflow {
class FunctionSpec;
class FunctionSpecDefaultTypeInternal;
extern FunctionSpecDefaultTypeInternal _FunctionSpec_default_instance_;
class SavedAsset;
class SavedAssetDefaultTypeInternal;
extern SavedAssetDefaultTypeInternal _SavedAsset_default_instance_;
class SavedBareConcreteFunction;
class SavedBareConcreteFunctionDefaultTypeInternal;
extern SavedBareConcreteFunctionDefaultTypeInternal _SavedBareConcreteFunction_default_instance_;
class SavedConcreteFunction;
class SavedConcreteFunctionDefaultTypeInternal;
extern SavedConcreteFunctionDefaultTypeInternal _SavedConcreteFunction_default_instance_;
class SavedConstant;
class SavedConstantDefaultTypeInternal;
extern SavedConstantDefaultTypeInternal _SavedConstant_default_instance_;
class SavedFunction;
class SavedFunctionDefaultTypeInternal;
extern SavedFunctionDefaultTypeInternal _SavedFunction_default_instance_;
class SavedObject;
class SavedObjectDefaultTypeInternal;
extern SavedObjectDefaultTypeInternal _SavedObject_default_instance_;
class SavedObjectGraph;
class SavedObjectGraphDefaultTypeInternal;
extern SavedObjectGraphDefaultTypeInternal _SavedObjectGraph_default_instance_;
class SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse;
class SavedObjectGraph_ConcreteFunctionsEntry_DoNotUseDefaultTypeInternal;
extern SavedObjectGraph_ConcreteFunctionsEntry_DoNotUseDefaultTypeInternal _SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse_default_instance_;
class SavedResource;
class SavedResourceDefaultTypeInternal;
extern SavedResourceDefaultTypeInternal _SavedResource_default_instance_;
class SavedUserObject;
class SavedUserObjectDefaultTypeInternal;
extern SavedUserObjectDefaultTypeInternal _SavedUserObject_default_instance_;
class SavedVariable;
class SavedVariableDefaultTypeInternal;
extern SavedVariableDefaultTypeInternal _SavedVariable_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::FunctionSpec* Arena::CreateMaybeMessage<::tensorflow::FunctionSpec>(Arena*);
template<> ::tensorflow::SavedAsset* Arena::CreateMaybeMessage<::tensorflow::SavedAsset>(Arena*);
template<> ::tensorflow::SavedBareConcreteFunction* Arena::CreateMaybeMessage<::tensorflow::SavedBareConcreteFunction>(Arena*);
template<> ::tensorflow::SavedConcreteFunction* Arena::CreateMaybeMessage<::tensorflow::SavedConcreteFunction>(Arena*);
template<> ::tensorflow::SavedConstant* Arena::CreateMaybeMessage<::tensorflow::SavedConstant>(Arena*);
template<> ::tensorflow::SavedFunction* Arena::CreateMaybeMessage<::tensorflow::SavedFunction>(Arena*);
template<> ::tensorflow::SavedObject* Arena::CreateMaybeMessage<::tensorflow::SavedObject>(Arena*);
template<> ::tensorflow::SavedObjectGraph* Arena::CreateMaybeMessage<::tensorflow::SavedObjectGraph>(Arena*);
template<> ::tensorflow::SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::SavedResource* Arena::CreateMaybeMessage<::tensorflow::SavedResource>(Arena*);
template<> ::tensorflow::SavedUserObject* Arena::CreateMaybeMessage<::tensorflow::SavedUserObject>(Arena*);
template<> ::tensorflow::SavedVariable* Arena::CreateMaybeMessage<::tensorflow::SavedVariable>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

// ===================================================================

class SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse, 
    ::std::string, ::tensorflow::SavedConcreteFunction,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse, 
    ::std::string, ::tensorflow::SavedConcreteFunction,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse();
  SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse& other);
  static const SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse*>(&_SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class SavedObjectGraph : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedObjectGraph) */ {
 public:
  SavedObjectGraph();
  virtual ~SavedObjectGraph();

  SavedObjectGraph(const SavedObjectGraph& from);

  inline SavedObjectGraph& operator=(const SavedObjectGraph& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SavedObjectGraph(SavedObjectGraph&& from) noexcept
    : SavedObjectGraph() {
    *this = ::std::move(from);
  }

  inline SavedObjectGraph& operator=(SavedObjectGraph&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const SavedObjectGraph& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SavedObjectGraph* internal_default_instance() {
    return reinterpret_cast<const SavedObjectGraph*>(
               &_SavedObjectGraph_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void UnsafeArenaSwap(SavedObjectGraph* other);
  void Swap(SavedObjectGraph* other);
  friend void swap(SavedObjectGraph& a, SavedObjectGraph& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SavedObjectGraph* New() const final {
    return CreateMaybeMessage<SavedObjectGraph>(NULL);
  }

  SavedObjectGraph* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<SavedObjectGraph>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const SavedObjectGraph& from);
  void MergeFrom(const SavedObjectGraph& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedObjectGraph* other);
  protected:
  explicit SavedObjectGraph(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // repeated .tensorflow.SavedObject nodes = 1;
  int nodes_size() const;
  void clear_nodes();
  static const int kNodesFieldNumber = 1;
  ::tensorflow::SavedObject* mutable_nodes(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::SavedObject >*
      mutable_nodes();
  const ::tensorflow::SavedObject& nodes(int index) const;
  ::tensorflow::SavedObject* add_nodes();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::SavedObject >&
      nodes() const;

  // map<string, .tensorflow.SavedConcreteFunction> concrete_functions = 2;
  int concrete_functions_size() const;
  void clear_concrete_functions();
  static const int kConcreteFunctionsFieldNumber = 2;
  const ::google::protobuf::Map< ::std::string, ::tensorflow::SavedConcreteFunction >&
      concrete_functions() const;
  ::google::protobuf::Map< ::std::string, ::tensorflow::SavedConcreteFunction >*
      mutable_concrete_functions();

  // @@protoc_insertion_point(class_scope:tensorflow.SavedObjectGraph)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::SavedObject > nodes_;
  ::google::protobuf::internal::MapField<
      SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse,
      ::std::string, ::tensorflow::SavedConcreteFunction,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > concrete_functions_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class SavedObject : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedObject) */ {
 public:
  SavedObject();
  virtual ~SavedObject();

  SavedObject(const SavedObject& from);

  inline SavedObject& operator=(const SavedObject& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SavedObject(SavedObject&& from) noexcept
    : SavedObject() {
    *this = ::std::move(from);
  }

  inline SavedObject& operator=(SavedObject&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const SavedObject& default_instance();

  enum KindCase {
    kUserObject = 4,
    kAsset = 5,
    kFunction = 6,
    kVariable = 7,
    kBareConcreteFunction = 8,
    kConstant = 9,
    kResource = 10,
    KIND_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SavedObject* internal_default_instance() {
    return reinterpret_cast<const SavedObject*>(
               &_SavedObject_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void UnsafeArenaSwap(SavedObject* other);
  void Swap(SavedObject* other);
  friend void swap(SavedObject& a, SavedObject& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SavedObject* New() const final {
    return CreateMaybeMessage<SavedObject>(NULL);
  }

  SavedObject* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<SavedObject>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const SavedObject& from);
  void MergeFrom(const SavedObject& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedObject* other);
  protected:
  explicit SavedObject(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference children = 1;
  int children_size() const;
  void clear_children();
  static const int kChildrenFieldNumber = 1;
  ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* mutable_children(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >*
      mutable_children();
  const ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference& children(int index) const;
  ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* add_children();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >&
      children() const;

  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference slot_variables = 3;
  int slot_variables_size() const;
  void clear_slot_variables();
  static const int kSlotVariablesFieldNumber = 3;
  ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* mutable_slot_variables(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference >*
      mutable_slot_variables();
  const ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference& slot_variables(int index) const;
  ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* add_slot_variables();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference >&
      slot_variables() const;

  // .tensorflow.SavedUserObject user_object = 4;
  bool has_user_object() const;
  void clear_user_object();
  static const int kUserObjectFieldNumber = 4;
  private:
  const ::tensorflow::SavedUserObject& _internal_user_object() const;
  public:
  const ::tensorflow::SavedUserObject& user_object() const;
  ::tensorflow::SavedUserObject* release_user_object();
  ::tensorflow::SavedUserObject* mutable_user_object();
  void set_allocated_user_object(::tensorflow::SavedUserObject* user_object);
  void unsafe_arena_set_allocated_user_object(
      ::tensorflow::SavedUserObject* user_object);
  ::tensorflow::SavedUserObject* unsafe_arena_release_user_object();

  // .tensorflow.SavedAsset asset = 5;
  bool has_asset() const;
  void clear_asset();
  static const int kAssetFieldNumber = 5;
  private:
  const ::tensorflow::SavedAsset& _internal_asset() const;
  public:
  const ::tensorflow::SavedAsset& asset() const;
  ::tensorflow::SavedAsset* release_asset();
  ::tensorflow::SavedAsset* mutable_asset();
  void set_allocated_asset(::tensorflow::SavedAsset* asset);
  void unsafe_arena_set_allocated_asset(
      ::tensorflow::SavedAsset* asset);
  ::tensorflow::SavedAsset* unsafe_arena_release_asset();

  // .tensorflow.SavedFunction function = 6;
  bool has_function() const;
  void clear_function();
  static const int kFunctionFieldNumber = 6;
  private:
  const ::tensorflow::SavedFunction& _internal_function() const;
  public:
  const ::tensorflow::SavedFunction& function() const;
  ::tensorflow::SavedFunction* release_function();
  ::tensorflow::SavedFunction* mutable_function();
  void set_allocated_function(::tensorflow::SavedFunction* function);
  void unsafe_arena_set_allocated_function(
      ::tensorflow::SavedFunction* function);
  ::tensorflow::SavedFunction* unsafe_arena_release_function();

  // .tensorflow.SavedVariable variable = 7;
  bool has_variable() const;
  void clear_variable();
  static const int kVariableFieldNumber = 7;
  private:
  const ::tensorflow::SavedVariable& _internal_variable() const;
  public:
  const ::tensorflow::SavedVariable& variable() const;
  ::tensorflow::SavedVariable* release_variable();
  ::tensorflow::SavedVariable* mutable_variable();
  void set_allocated_variable(::tensorflow::SavedVariable* variable);
  void unsafe_arena_set_allocated_variable(
      ::tensorflow::SavedVariable* variable);
  ::tensorflow::SavedVariable* unsafe_arena_release_variable();

  // .tensorflow.SavedBareConcreteFunction bare_concrete_function = 8;
  bool has_bare_concrete_function() const;
  void clear_bare_concrete_function();
  static const int kBareConcreteFunctionFieldNumber = 8;
  private:
  const ::tensorflow::SavedBareConcreteFunction& _internal_bare_concrete_function() const;
  public:
  const ::tensorflow::SavedBareConcreteFunction& bare_concrete_function() const;
  ::tensorflow::SavedBareConcreteFunction* release_bare_concrete_function();
  ::tensorflow::SavedBareConcreteFunction* mutable_bare_concrete_function();
  void set_allocated_bare_concrete_function(::tensorflow::SavedBareConcreteFunction* bare_concrete_function);
  void unsafe_arena_set_allocated_bare_concrete_function(
      ::tensorflow::SavedBareConcreteFunction* bare_concrete_function);
  ::tensorflow::SavedBareConcreteFunction* unsafe_arena_release_bare_concrete_function();

  // .tensorflow.SavedConstant constant = 9;
  bool has_constant() const;
  void clear_constant();
  static const int kConstantFieldNumber = 9;
  private:
  const ::tensorflow::SavedConstant& _internal_constant() const;
  public:
  const ::tensorflow::SavedConstant& constant() const;
  ::tensorflow::SavedConstant* release_constant();
  ::tensorflow::SavedConstant* mutable_constant();
  void set_allocated_constant(::tensorflow::SavedConstant* constant);
  void unsafe_arena_set_allocated_constant(
      ::tensorflow::SavedConstant* constant);
  ::tensorflow::SavedConstant* unsafe_arena_release_constant();

  // .tensorflow.SavedResource resource = 10;
  bool has_resource() const;
  void clear_resource();
  static const int kResourceFieldNumber = 10;
  private:
  const ::tensorflow::SavedResource& _internal_resource() const;
  public:
  const ::tensorflow::SavedResource& resource() const;
  ::tensorflow::SavedResource* release_resource();
  ::tensorflow::SavedResource* mutable_resource();
  void set_allocated_resource(::tensorflow::SavedResource* resource);
  void unsafe_arena_set_allocated_resource(
      ::tensorflow::SavedResource* resource);
  ::tensorflow::SavedResource* unsafe_arena_release_resource();

  void clear_kind();
  KindCase kind_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.SavedObject)
 private:
  void set_has_user_object();
  void set_has_asset();
  void set_has_function();
  void set_has_variable();
  void set_has_bare_concrete_function();
  void set_has_constant();
  void set_has_resource();

  inline bool has_kind() const;
  inline void clear_has_kind();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference > children_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference > slot_variables_;
  union KindUnion {
    KindUnion() {}
    ::tensorflow::SavedUserObject* user_object_;
    ::tensorflow::SavedAsset* asset_;
    ::tensorflow::SavedFunction* function_;
    ::tensorflow::SavedVariable* variable_;
    ::tensorflow::SavedBareConcreteFunction* bare_concrete_function_;
    ::tensorflow::SavedConstant* constant_;
    ::tensorflow::SavedResource* resource_;
  } kind_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class SavedUserObject : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedUserObject) */ {
 public:
  SavedUserObject();
  virtual ~SavedUserObject();

  SavedUserObject(const SavedUserObject& from);

  inline SavedUserObject& operator=(const SavedUserObject& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SavedUserObject(SavedUserObject&& from) noexcept
    : SavedUserObject() {
    *this = ::std::move(from);
  }

  inline SavedUserObject& operator=(SavedUserObject&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const SavedUserObject& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SavedUserObject* internal_default_instance() {
    return reinterpret_cast<const SavedUserObject*>(
               &_SavedUserObject_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void UnsafeArenaSwap(SavedUserObject* other);
  void Swap(SavedUserObject* other);
  friend void swap(SavedUserObject& a, SavedUserObject& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SavedUserObject* New() const final {
    return CreateMaybeMessage<SavedUserObject>(NULL);
  }

  SavedUserObject* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<SavedUserObject>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const SavedUserObject& from);
  void MergeFrom(const SavedUserObject& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedUserObject* other);
  protected:
  explicit SavedUserObject(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string identifier = 1;
  void clear_identifier();
  static const int kIdentifierFieldNumber = 1;
  const ::std::string& identifier() const;
  void set_identifier(const ::std::string& value);
  #if LANG_CXX11
  void set_identifier(::std::string&& value);
  #endif
  void set_identifier(const char* value);
  void set_identifier(const char* value, size_t size);
  ::std::string* mutable_identifier();
  ::std::string* release_identifier();
  void set_allocated_identifier(::std::string* identifier);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_identifier();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_identifier(
      ::std::string* identifier);

  // .tensorflow.VersionDef version = 2;
  bool has_version() const;
  void clear_version();
  static const int kVersionFieldNumber = 2;
  private:
  const ::tensorflow::VersionDef& _internal_version() const;
  public:
  const ::tensorflow::VersionDef& version() const;
  ::tensorflow::VersionDef* release_version();
  ::tensorflow::VersionDef* mutable_version();
  void set_allocated_version(::tensorflow::VersionDef* version);
  void unsafe_arena_set_allocated_version(
      ::tensorflow::VersionDef* version);
  ::tensorflow::VersionDef* unsafe_arena_release_version();

  // @@protoc_insertion_point(class_scope:tensorflow.SavedUserObject)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr identifier_;
  ::tensorflow::VersionDef* version_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class SavedAsset : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedAsset) */ {
 public:
  SavedAsset();
  virtual ~SavedAsset();

  SavedAsset(const SavedAsset& from);

  inline SavedAsset& operator=(const SavedAsset& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SavedAsset(SavedAsset&& from) noexcept
    : SavedAsset() {
    *this = ::std::move(from);
  }

  inline SavedAsset& operator=(SavedAsset&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const SavedAsset& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SavedAsset* internal_default_instance() {
    return reinterpret_cast<const SavedAsset*>(
               &_SavedAsset_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void UnsafeArenaSwap(SavedAsset* other);
  void Swap(SavedAsset* other);
  friend void swap(SavedAsset& a, SavedAsset& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SavedAsset* New() const final {
    return CreateMaybeMessage<SavedAsset>(NULL);
  }

  SavedAsset* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<SavedAsset>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const SavedAsset& from);
  void MergeFrom(const SavedAsset& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedAsset* other);
  protected:
  explicit SavedAsset(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int32 asset_file_def_index = 1;
  void clear_asset_file_def_index();
  static const int kAssetFileDefIndexFieldNumber = 1;
  ::google::protobuf::int32 asset_file_def_index() const;
  void set_asset_file_def_index(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.SavedAsset)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::int32 asset_file_def_index_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class SavedFunction : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedFunction) */ {
 public:
  SavedFunction();
  virtual ~SavedFunction();

  SavedFunction(const SavedFunction& from);

  inline SavedFunction& operator=(const SavedFunction& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SavedFunction(SavedFunction&& from) noexcept
    : SavedFunction() {
    *this = ::std::move(from);
  }

  inline SavedFunction& operator=(SavedFunction&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const SavedFunction& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SavedFunction* internal_default_instance() {
    return reinterpret_cast<const SavedFunction*>(
               &_SavedFunction_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  void UnsafeArenaSwap(SavedFunction* other);
  void Swap(SavedFunction* other);
  friend void swap(SavedFunction& a, SavedFunction& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SavedFunction* New() const final {
    return CreateMaybeMessage<SavedFunction>(NULL);
  }

  SavedFunction* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<SavedFunction>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const SavedFunction& from);
  void MergeFrom(const SavedFunction& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedFunction* other);
  protected:
  explicit SavedFunction(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string concrete_functions = 1;
  int concrete_functions_size() const;
  void clear_concrete_functions();
  static const int kConcreteFunctionsFieldNumber = 1;
  const ::std::string& concrete_functions(int index) const;
  ::std::string* mutable_concrete_functions(int index);
  void set_concrete_functions(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_concrete_functions(int index, ::std::string&& value);
  #endif
  void set_concrete_functions(int index, const char* value);
  void set_concrete_functions(int index, const char* value, size_t size);
  ::std::string* add_concrete_functions();
  void add_concrete_functions(const ::std::string& value);
  #if LANG_CXX11
  void add_concrete_functions(::std::string&& value);
  #endif
  void add_concrete_functions(const char* value);
  void add_concrete_functions(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& concrete_functions() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_concrete_functions();

  // .tensorflow.FunctionSpec function_spec = 2;
  bool has_function_spec() const;
  void clear_function_spec();
  static const int kFunctionSpecFieldNumber = 2;
  private:
  const ::tensorflow::FunctionSpec& _internal_function_spec() const;
  public:
  const ::tensorflow::FunctionSpec& function_spec() const;
  ::tensorflow::FunctionSpec* release_function_spec();
  ::tensorflow::FunctionSpec* mutable_function_spec();
  void set_allocated_function_spec(::tensorflow::FunctionSpec* function_spec);
  void unsafe_arena_set_allocated_function_spec(
      ::tensorflow::FunctionSpec* function_spec);
  ::tensorflow::FunctionSpec* unsafe_arena_release_function_spec();

  // @@protoc_insertion_point(class_scope:tensorflow.SavedFunction)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::std::string> concrete_functions_;
  ::tensorflow::FunctionSpec* function_spec_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class SavedConcreteFunction : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedConcreteFunction) */ {
 public:
  SavedConcreteFunction();
  virtual ~SavedConcreteFunction();

  SavedConcreteFunction(const SavedConcreteFunction& from);

  inline SavedConcreteFunction& operator=(const SavedConcreteFunction& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SavedConcreteFunction(SavedConcreteFunction&& from) noexcept
    : SavedConcreteFunction() {
    *this = ::std::move(from);
  }

  inline SavedConcreteFunction& operator=(SavedConcreteFunction&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const SavedConcreteFunction& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SavedConcreteFunction* internal_default_instance() {
    return reinterpret_cast<const SavedConcreteFunction*>(
               &_SavedConcreteFunction_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  void UnsafeArenaSwap(SavedConcreteFunction* other);
  void Swap(SavedConcreteFunction* other);
  friend void swap(SavedConcreteFunction& a, SavedConcreteFunction& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SavedConcreteFunction* New() const final {
    return CreateMaybeMessage<SavedConcreteFunction>(NULL);
  }

  SavedConcreteFunction* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<SavedConcreteFunction>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const SavedConcreteFunction& from);
  void MergeFrom(const SavedConcreteFunction& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedConcreteFunction* other);
  protected:
  explicit SavedConcreteFunction(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int32 bound_inputs = 2;
  int bound_inputs_size() const;
  void clear_bound_inputs();
  static const int kBoundInputsFieldNumber = 2;
  ::google::protobuf::int32 bound_inputs(int index) const;
  void set_bound_inputs(int index, ::google::protobuf::int32 value);
  void add_bound_inputs(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      bound_inputs() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_bound_inputs();

  // .tensorflow.StructuredValue canonicalized_input_signature = 3;
  bool has_canonicalized_input_signature() const;
  void clear_canonicalized_input_signature();
  static const int kCanonicalizedInputSignatureFieldNumber = 3;
  private:
  const ::tensorflow::StructuredValue& _internal_canonicalized_input_signature() const;
  public:
  const ::tensorflow::StructuredValue& canonicalized_input_signature() const;
  ::tensorflow::StructuredValue* release_canonicalized_input_signature();
  ::tensorflow::StructuredValue* mutable_canonicalized_input_signature();
  void set_allocated_canonicalized_input_signature(::tensorflow::StructuredValue* canonicalized_input_signature);
  void unsafe_arena_set_allocated_canonicalized_input_signature(
      ::tensorflow::StructuredValue* canonicalized_input_signature);
  ::tensorflow::StructuredValue* unsafe_arena_release_canonicalized_input_signature();

  // .tensorflow.StructuredValue output_signature = 4;
  bool has_output_signature() const;
  void clear_output_signature();
  static const int kOutputSignatureFieldNumber = 4;
  private:
  const ::tensorflow::StructuredValue& _internal_output_signature() const;
  public:
  const ::tensorflow::StructuredValue& output_signature() const;
  ::tensorflow::StructuredValue* release_output_signature();
  ::tensorflow::StructuredValue* mutable_output_signature();
  void set_allocated_output_signature(::tensorflow::StructuredValue* output_signature);
  void unsafe_arena_set_allocated_output_signature(
      ::tensorflow::StructuredValue* output_signature);
  ::tensorflow::StructuredValue* unsafe_arena_release_output_signature();

  // @@protoc_insertion_point(class_scope:tensorflow.SavedConcreteFunction)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > bound_inputs_;
  mutable int _bound_inputs_cached_byte_size_;
  ::tensorflow::StructuredValue* canonicalized_input_signature_;
  ::tensorflow::StructuredValue* output_signature_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class SavedBareConcreteFunction : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedBareConcreteFunction) */ {
 public:
  SavedBareConcreteFunction();
  virtual ~SavedBareConcreteFunction();

  SavedBareConcreteFunction(const SavedBareConcreteFunction& from);

  inline SavedBareConcreteFunction& operator=(const SavedBareConcreteFunction& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SavedBareConcreteFunction(SavedBareConcreteFunction&& from) noexcept
    : SavedBareConcreteFunction() {
    *this = ::std::move(from);
  }

  inline SavedBareConcreteFunction& operator=(SavedBareConcreteFunction&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const SavedBareConcreteFunction& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SavedBareConcreteFunction* internal_default_instance() {
    return reinterpret_cast<const SavedBareConcreteFunction*>(
               &_SavedBareConcreteFunction_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  void UnsafeArenaSwap(SavedBareConcreteFunction* other);
  void Swap(SavedBareConcreteFunction* other);
  friend void swap(SavedBareConcreteFunction& a, SavedBareConcreteFunction& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SavedBareConcreteFunction* New() const final {
    return CreateMaybeMessage<SavedBareConcreteFunction>(NULL);
  }

  SavedBareConcreteFunction* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<SavedBareConcreteFunction>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const SavedBareConcreteFunction& from);
  void MergeFrom(const SavedBareConcreteFunction& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedBareConcreteFunction* other);
  protected:
  explicit SavedBareConcreteFunction(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string argument_keywords = 2;
  int argument_keywords_size() const;
  void clear_argument_keywords();
  static const int kArgumentKeywordsFieldNumber = 2;
  const ::std::string& argument_keywords(int index) const;
  ::std::string* mutable_argument_keywords(int index);
  void set_argument_keywords(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_argument_keywords(int index, ::std::string&& value);
  #endif
  void set_argument_keywords(int index, const char* value);
  void set_argument_keywords(int index, const char* value, size_t size);
  ::std::string* add_argument_keywords();
  void add_argument_keywords(const ::std::string& value);
  #if LANG_CXX11
  void add_argument_keywords(::std::string&& value);
  #endif
  void add_argument_keywords(const char* value);
  void add_argument_keywords(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& argument_keywords() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_argument_keywords();

  // string concrete_function_name = 1;
  void clear_concrete_function_name();
  static const int kConcreteFunctionNameFieldNumber = 1;
  const ::std::string& concrete_function_name() const;
  void set_concrete_function_name(const ::std::string& value);
  #if LANG_CXX11
  void set_concrete_function_name(::std::string&& value);
  #endif
  void set_concrete_function_name(const char* value);
  void set_concrete_function_name(const char* value, size_t size);
  ::std::string* mutable_concrete_function_name();
  ::std::string* release_concrete_function_name();
  void set_allocated_concrete_function_name(::std::string* concrete_function_name);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_concrete_function_name();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_concrete_function_name(
      ::std::string* concrete_function_name);

  // int64 allowed_positional_arguments = 3;
  void clear_allowed_positional_arguments();
  static const int kAllowedPositionalArgumentsFieldNumber = 3;
  ::google::protobuf::int64 allowed_positional_arguments() const;
  void set_allowed_positional_arguments(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.SavedBareConcreteFunction)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::std::string> argument_keywords_;
  ::google::protobuf::internal::ArenaStringPtr concrete_function_name_;
  ::google::protobuf::int64 allowed_positional_arguments_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class SavedConstant : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedConstant) */ {
 public:
  SavedConstant();
  virtual ~SavedConstant();

  SavedConstant(const SavedConstant& from);

  inline SavedConstant& operator=(const SavedConstant& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SavedConstant(SavedConstant&& from) noexcept
    : SavedConstant() {
    *this = ::std::move(from);
  }

  inline SavedConstant& operator=(SavedConstant&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const SavedConstant& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SavedConstant* internal_default_instance() {
    return reinterpret_cast<const SavedConstant*>(
               &_SavedConstant_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  void UnsafeArenaSwap(SavedConstant* other);
  void Swap(SavedConstant* other);
  friend void swap(SavedConstant& a, SavedConstant& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SavedConstant* New() const final {
    return CreateMaybeMessage<SavedConstant>(NULL);
  }

  SavedConstant* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<SavedConstant>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const SavedConstant& from);
  void MergeFrom(const SavedConstant& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedConstant* other);
  protected:
  explicit SavedConstant(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string operation = 1;
  void clear_operation();
  static const int kOperationFieldNumber = 1;
  const ::std::string& operation() const;
  void set_operation(const ::std::string& value);
  #if LANG_CXX11
  void set_operation(::std::string&& value);
  #endif
  void set_operation(const char* value);
  void set_operation(const char* value, size_t size);
  ::std::string* mutable_operation();
  ::std::string* release_operation();
  void set_allocated_operation(::std::string* operation);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_operation();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_operation(
      ::std::string* operation);

  // @@protoc_insertion_point(class_scope:tensorflow.SavedConstant)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr operation_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class SavedVariable : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedVariable) */ {
 public:
  SavedVariable();
  virtual ~SavedVariable();

  SavedVariable(const SavedVariable& from);

  inline SavedVariable& operator=(const SavedVariable& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SavedVariable(SavedVariable&& from) noexcept
    : SavedVariable() {
    *this = ::std::move(from);
  }

  inline SavedVariable& operator=(SavedVariable&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const SavedVariable& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SavedVariable* internal_default_instance() {
    return reinterpret_cast<const SavedVariable*>(
               &_SavedVariable_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  void UnsafeArenaSwap(SavedVariable* other);
  void Swap(SavedVariable* other);
  friend void swap(SavedVariable& a, SavedVariable& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SavedVariable* New() const final {
    return CreateMaybeMessage<SavedVariable>(NULL);
  }

  SavedVariable* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<SavedVariable>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const SavedVariable& from);
  void MergeFrom(const SavedVariable& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedVariable* other);
  protected:
  explicit SavedVariable(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .tensorflow.TensorShapeProto shape = 2;
  bool has_shape() const;
  void clear_shape();
  static const int kShapeFieldNumber = 2;
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape() const;
  public:
  const ::tensorflow::TensorShapeProto& shape() const;
  ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .tensorflow.DataType dtype = 1;
  void clear_dtype();
  static const int kDtypeFieldNumber = 1;
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);

  // bool trainable = 3;
  void clear_trainable();
  static const int kTrainableFieldNumber = 3;
  bool trainable() const;
  void set_trainable(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.SavedVariable)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::tensorflow::TensorShapeProto* shape_;
  int dtype_;
  bool trainable_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class FunctionSpec : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.FunctionSpec) */ {
 public:
  FunctionSpec();
  virtual ~FunctionSpec();

  FunctionSpec(const FunctionSpec& from);

  inline FunctionSpec& operator=(const FunctionSpec& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  FunctionSpec(FunctionSpec&& from) noexcept
    : FunctionSpec() {
    *this = ::std::move(from);
  }

  inline FunctionSpec& operator=(FunctionSpec&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const FunctionSpec& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const FunctionSpec* internal_default_instance() {
    return reinterpret_cast<const FunctionSpec*>(
               &_FunctionSpec_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  void UnsafeArenaSwap(FunctionSpec* other);
  void Swap(FunctionSpec* other);
  friend void swap(FunctionSpec& a, FunctionSpec& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline FunctionSpec* New() const final {
    return CreateMaybeMessage<FunctionSpec>(NULL);
  }

  FunctionSpec* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<FunctionSpec>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const FunctionSpec& from);
  void MergeFrom(const FunctionSpec& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FunctionSpec* other);
  protected:
  explicit FunctionSpec(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .tensorflow.StructuredValue fullargspec = 1;
  bool has_fullargspec() const;
  void clear_fullargspec();
  static const int kFullargspecFieldNumber = 1;
  private:
  const ::tensorflow::StructuredValue& _internal_fullargspec() const;
  public:
  const ::tensorflow::StructuredValue& fullargspec() const;
  ::tensorflow::StructuredValue* release_fullargspec();
  ::tensorflow::StructuredValue* mutable_fullargspec();
  void set_allocated_fullargspec(::tensorflow::StructuredValue* fullargspec);
  void unsafe_arena_set_allocated_fullargspec(
      ::tensorflow::StructuredValue* fullargspec);
  ::tensorflow::StructuredValue* unsafe_arena_release_fullargspec();

  // .tensorflow.StructuredValue args_to_prepend = 3;
  bool has_args_to_prepend() const;
  void clear_args_to_prepend();
  static const int kArgsToPrependFieldNumber = 3;
  private:
  const ::tensorflow::StructuredValue& _internal_args_to_prepend() const;
  public:
  const ::tensorflow::StructuredValue& args_to_prepend() const;
  ::tensorflow::StructuredValue* release_args_to_prepend();
  ::tensorflow::StructuredValue* mutable_args_to_prepend();
  void set_allocated_args_to_prepend(::tensorflow::StructuredValue* args_to_prepend);
  void unsafe_arena_set_allocated_args_to_prepend(
      ::tensorflow::StructuredValue* args_to_prepend);
  ::tensorflow::StructuredValue* unsafe_arena_release_args_to_prepend();

  // .tensorflow.StructuredValue kwargs_to_include = 4;
  bool has_kwargs_to_include() const;
  void clear_kwargs_to_include();
  static const int kKwargsToIncludeFieldNumber = 4;
  private:
  const ::tensorflow::StructuredValue& _internal_kwargs_to_include() const;
  public:
  const ::tensorflow::StructuredValue& kwargs_to_include() const;
  ::tensorflow::StructuredValue* release_kwargs_to_include();
  ::tensorflow::StructuredValue* mutable_kwargs_to_include();
  void set_allocated_kwargs_to_include(::tensorflow::StructuredValue* kwargs_to_include);
  void unsafe_arena_set_allocated_kwargs_to_include(
      ::tensorflow::StructuredValue* kwargs_to_include);
  ::tensorflow::StructuredValue* unsafe_arena_release_kwargs_to_include();

  // .tensorflow.StructuredValue input_signature = 5;
  bool has_input_signature() const;
  void clear_input_signature();
  static const int kInputSignatureFieldNumber = 5;
  private:
  const ::tensorflow::StructuredValue& _internal_input_signature() const;
  public:
  const ::tensorflow::StructuredValue& input_signature() const;
  ::tensorflow::StructuredValue* release_input_signature();
  ::tensorflow::StructuredValue* mutable_input_signature();
  void set_allocated_input_signature(::tensorflow::StructuredValue* input_signature);
  void unsafe_arena_set_allocated_input_signature(
      ::tensorflow::StructuredValue* input_signature);
  ::tensorflow::StructuredValue* unsafe_arena_release_input_signature();

  // bool is_method = 2;
  void clear_is_method();
  static const int kIsMethodFieldNumber = 2;
  bool is_method() const;
  void set_is_method(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.FunctionSpec)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::tensorflow::StructuredValue* fullargspec_;
  ::tensorflow::StructuredValue* args_to_prepend_;
  ::tensorflow::StructuredValue* kwargs_to_include_;
  ::tensorflow::StructuredValue* input_signature_;
  bool is_method_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class SavedResource : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedResource) */ {
 public:
  SavedResource();
  virtual ~SavedResource();

  SavedResource(const SavedResource& from);

  inline SavedResource& operator=(const SavedResource& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SavedResource(SavedResource&& from) noexcept
    : SavedResource() {
    *this = ::std::move(from);
  }

  inline SavedResource& operator=(SavedResource&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const SavedResource& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SavedResource* internal_default_instance() {
    return reinterpret_cast<const SavedResource*>(
               &_SavedResource_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  void UnsafeArenaSwap(SavedResource* other);
  void Swap(SavedResource* other);
  friend void swap(SavedResource& a, SavedResource& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SavedResource* New() const final {
    return CreateMaybeMessage<SavedResource>(NULL);
  }

  SavedResource* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<SavedResource>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const SavedResource& from);
  void MergeFrom(const SavedResource& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedResource* other);
  protected:
  explicit SavedResource(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.SavedResource)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// -------------------------------------------------------------------

// SavedObjectGraph

// repeated .tensorflow.SavedObject nodes = 1;
inline int SavedObjectGraph::nodes_size() const {
  return nodes_.size();
}
inline void SavedObjectGraph::clear_nodes() {
  nodes_.Clear();
}
inline ::tensorflow::SavedObject* SavedObjectGraph::mutable_nodes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObjectGraph.nodes)
  return nodes_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::SavedObject >*
SavedObjectGraph::mutable_nodes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SavedObjectGraph.nodes)
  return &nodes_;
}
inline const ::tensorflow::SavedObject& SavedObjectGraph::nodes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObjectGraph.nodes)
  return nodes_.Get(index);
}
inline ::tensorflow::SavedObject* SavedObjectGraph::add_nodes() {
  // @@protoc_insertion_point(field_add:tensorflow.SavedObjectGraph.nodes)
  return nodes_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::SavedObject >&
SavedObjectGraph::nodes() const {
  // @@protoc_insertion_point(field_list:tensorflow.SavedObjectGraph.nodes)
  return nodes_;
}

// map<string, .tensorflow.SavedConcreteFunction> concrete_functions = 2;
inline int SavedObjectGraph::concrete_functions_size() const {
  return concrete_functions_.size();
}
inline void SavedObjectGraph::clear_concrete_functions() {
  concrete_functions_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::tensorflow::SavedConcreteFunction >&
SavedObjectGraph::concrete_functions() const {
  // @@protoc_insertion_point(field_map:tensorflow.SavedObjectGraph.concrete_functions)
  return concrete_functions_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::tensorflow::SavedConcreteFunction >*
SavedObjectGraph::mutable_concrete_functions() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.SavedObjectGraph.concrete_functions)
  return concrete_functions_.MutableMap();
}

// -------------------------------------------------------------------

// SavedObject

// repeated .tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference children = 1;
inline int SavedObject::children_size() const {
  return children_.size();
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* SavedObject::mutable_children(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.children)
  return children_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >*
SavedObject::mutable_children() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SavedObject.children)
  return &children_;
}
inline const ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference& SavedObject::children(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.children)
  return children_.Get(index);
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* SavedObject::add_children() {
  // @@protoc_insertion_point(field_add:tensorflow.SavedObject.children)
  return children_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >&
SavedObject::children() const {
  // @@protoc_insertion_point(field_list:tensorflow.SavedObject.children)
  return children_;
}

// repeated .tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference slot_variables = 3;
inline int SavedObject::slot_variables_size() const {
  return slot_variables_.size();
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* SavedObject::mutable_slot_variables(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.slot_variables)
  return slot_variables_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference >*
SavedObject::mutable_slot_variables() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SavedObject.slot_variables)
  return &slot_variables_;
}
inline const ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference& SavedObject::slot_variables(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.slot_variables)
  return slot_variables_.Get(index);
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* SavedObject::add_slot_variables() {
  // @@protoc_insertion_point(field_add:tensorflow.SavedObject.slot_variables)
  return slot_variables_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference >&
SavedObject::slot_variables() const {
  // @@protoc_insertion_point(field_list:tensorflow.SavedObject.slot_variables)
  return slot_variables_;
}

// .tensorflow.SavedUserObject user_object = 4;
inline bool SavedObject::has_user_object() const {
  return kind_case() == kUserObject;
}
inline void SavedObject::set_has_user_object() {
  _oneof_case_[0] = kUserObject;
}
inline void SavedObject::clear_user_object() {
  if (has_user_object()) {
    if (GetArenaNoVirtual() == NULL) {
      delete kind_.user_object_;
    }
    clear_has_kind();
  }
}
inline const ::tensorflow::SavedUserObject& SavedObject::_internal_user_object() const {
  return *kind_.user_object_;
}
inline ::tensorflow::SavedUserObject* SavedObject::release_user_object() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedObject.user_object)
  if (has_user_object()) {
    clear_has_kind();
      ::tensorflow::SavedUserObject* temp = kind_.user_object_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    kind_.user_object_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::SavedUserObject& SavedObject::user_object() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.user_object)
  return has_user_object()
      ? *kind_.user_object_
      : *reinterpret_cast< ::tensorflow::SavedUserObject*>(&::tensorflow::_SavedUserObject_default_instance_);
}
inline ::tensorflow::SavedUserObject* SavedObject::unsafe_arena_release_user_object() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedObject.user_object)
  if (has_user_object()) {
    clear_has_kind();
    ::tensorflow::SavedUserObject* temp = kind_.user_object_;
    kind_.user_object_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void SavedObject::unsafe_arena_set_allocated_user_object(::tensorflow::SavedUserObject* user_object) {
  clear_kind();
  if (user_object) {
    set_has_user_object();
    kind_.user_object_ = user_object;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedObject.user_object)
}
inline ::tensorflow::SavedUserObject* SavedObject::mutable_user_object() {
  if (!has_user_object()) {
    clear_kind();
    set_has_user_object();
    kind_.user_object_ = CreateMaybeMessage< ::tensorflow::SavedUserObject >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.user_object)
  return kind_.user_object_;
}

// .tensorflow.SavedAsset asset = 5;
inline bool SavedObject::has_asset() const {
  return kind_case() == kAsset;
}
inline void SavedObject::set_has_asset() {
  _oneof_case_[0] = kAsset;
}
inline void SavedObject::clear_asset() {
  if (has_asset()) {
    if (GetArenaNoVirtual() == NULL) {
      delete kind_.asset_;
    }
    clear_has_kind();
  }
}
inline const ::tensorflow::SavedAsset& SavedObject::_internal_asset() const {
  return *kind_.asset_;
}
inline ::tensorflow::SavedAsset* SavedObject::release_asset() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedObject.asset)
  if (has_asset()) {
    clear_has_kind();
      ::tensorflow::SavedAsset* temp = kind_.asset_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    kind_.asset_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::SavedAsset& SavedObject::asset() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.asset)
  return has_asset()
      ? *kind_.asset_
      : *reinterpret_cast< ::tensorflow::SavedAsset*>(&::tensorflow::_SavedAsset_default_instance_);
}
inline ::tensorflow::SavedAsset* SavedObject::unsafe_arena_release_asset() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedObject.asset)
  if (has_asset()) {
    clear_has_kind();
    ::tensorflow::SavedAsset* temp = kind_.asset_;
    kind_.asset_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void SavedObject::unsafe_arena_set_allocated_asset(::tensorflow::SavedAsset* asset) {
  clear_kind();
  if (asset) {
    set_has_asset();
    kind_.asset_ = asset;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedObject.asset)
}
inline ::tensorflow::SavedAsset* SavedObject::mutable_asset() {
  if (!has_asset()) {
    clear_kind();
    set_has_asset();
    kind_.asset_ = CreateMaybeMessage< ::tensorflow::SavedAsset >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.asset)
  return kind_.asset_;
}

// .tensorflow.SavedFunction function = 6;
inline bool SavedObject::has_function() const {
  return kind_case() == kFunction;
}
inline void SavedObject::set_has_function() {
  _oneof_case_[0] = kFunction;
}
inline void SavedObject::clear_function() {
  if (has_function()) {
    if (GetArenaNoVirtual() == NULL) {
      delete kind_.function_;
    }
    clear_has_kind();
  }
}
inline const ::tensorflow::SavedFunction& SavedObject::_internal_function() const {
  return *kind_.function_;
}
inline ::tensorflow::SavedFunction* SavedObject::release_function() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedObject.function)
  if (has_function()) {
    clear_has_kind();
      ::tensorflow::SavedFunction* temp = kind_.function_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    kind_.function_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::SavedFunction& SavedObject::function() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.function)
  return has_function()
      ? *kind_.function_
      : *reinterpret_cast< ::tensorflow::SavedFunction*>(&::tensorflow::_SavedFunction_default_instance_);
}
inline ::tensorflow::SavedFunction* SavedObject::unsafe_arena_release_function() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedObject.function)
  if (has_function()) {
    clear_has_kind();
    ::tensorflow::SavedFunction* temp = kind_.function_;
    kind_.function_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void SavedObject::unsafe_arena_set_allocated_function(::tensorflow::SavedFunction* function) {
  clear_kind();
  if (function) {
    set_has_function();
    kind_.function_ = function;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedObject.function)
}
inline ::tensorflow::SavedFunction* SavedObject::mutable_function() {
  if (!has_function()) {
    clear_kind();
    set_has_function();
    kind_.function_ = CreateMaybeMessage< ::tensorflow::SavedFunction >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.function)
  return kind_.function_;
}

// .tensorflow.SavedVariable variable = 7;
inline bool SavedObject::has_variable() const {
  return kind_case() == kVariable;
}
inline void SavedObject::set_has_variable() {
  _oneof_case_[0] = kVariable;
}
inline void SavedObject::clear_variable() {
  if (has_variable()) {
    if (GetArenaNoVirtual() == NULL) {
      delete kind_.variable_;
    }
    clear_has_kind();
  }
}
inline const ::tensorflow::SavedVariable& SavedObject::_internal_variable() const {
  return *kind_.variable_;
}
inline ::tensorflow::SavedVariable* SavedObject::release_variable() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedObject.variable)
  if (has_variable()) {
    clear_has_kind();
      ::tensorflow::SavedVariable* temp = kind_.variable_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    kind_.variable_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::SavedVariable& SavedObject::variable() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.variable)
  return has_variable()
      ? *kind_.variable_
      : *reinterpret_cast< ::tensorflow::SavedVariable*>(&::tensorflow::_SavedVariable_default_instance_);
}
inline ::tensorflow::SavedVariable* SavedObject::unsafe_arena_release_variable() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedObject.variable)
  if (has_variable()) {
    clear_has_kind();
    ::tensorflow::SavedVariable* temp = kind_.variable_;
    kind_.variable_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void SavedObject::unsafe_arena_set_allocated_variable(::tensorflow::SavedVariable* variable) {
  clear_kind();
  if (variable) {
    set_has_variable();
    kind_.variable_ = variable;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedObject.variable)
}
inline ::tensorflow::SavedVariable* SavedObject::mutable_variable() {
  if (!has_variable()) {
    clear_kind();
    set_has_variable();
    kind_.variable_ = CreateMaybeMessage< ::tensorflow::SavedVariable >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.variable)
  return kind_.variable_;
}

// .tensorflow.SavedBareConcreteFunction bare_concrete_function = 8;
inline bool SavedObject::has_bare_concrete_function() const {
  return kind_case() == kBareConcreteFunction;
}
inline void SavedObject::set_has_bare_concrete_function() {
  _oneof_case_[0] = kBareConcreteFunction;
}
inline void SavedObject::clear_bare_concrete_function() {
  if (has_bare_concrete_function()) {
    if (GetArenaNoVirtual() == NULL) {
      delete kind_.bare_concrete_function_;
    }
    clear_has_kind();
  }
}
inline const ::tensorflow::SavedBareConcreteFunction& SavedObject::_internal_bare_concrete_function() const {
  return *kind_.bare_concrete_function_;
}
inline ::tensorflow::SavedBareConcreteFunction* SavedObject::release_bare_concrete_function() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedObject.bare_concrete_function)
  if (has_bare_concrete_function()) {
    clear_has_kind();
      ::tensorflow::SavedBareConcreteFunction* temp = kind_.bare_concrete_function_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    kind_.bare_concrete_function_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::SavedBareConcreteFunction& SavedObject::bare_concrete_function() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.bare_concrete_function)
  return has_bare_concrete_function()
      ? *kind_.bare_concrete_function_
      : *reinterpret_cast< ::tensorflow::SavedBareConcreteFunction*>(&::tensorflow::_SavedBareConcreteFunction_default_instance_);
}
inline ::tensorflow::SavedBareConcreteFunction* SavedObject::unsafe_arena_release_bare_concrete_function() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedObject.bare_concrete_function)
  if (has_bare_concrete_function()) {
    clear_has_kind();
    ::tensorflow::SavedBareConcreteFunction* temp = kind_.bare_concrete_function_;
    kind_.bare_concrete_function_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void SavedObject::unsafe_arena_set_allocated_bare_concrete_function(::tensorflow::SavedBareConcreteFunction* bare_concrete_function) {
  clear_kind();
  if (bare_concrete_function) {
    set_has_bare_concrete_function();
    kind_.bare_concrete_function_ = bare_concrete_function;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedObject.bare_concrete_function)
}
inline ::tensorflow::SavedBareConcreteFunction* SavedObject::mutable_bare_concrete_function() {
  if (!has_bare_concrete_function()) {
    clear_kind();
    set_has_bare_concrete_function();
    kind_.bare_concrete_function_ = CreateMaybeMessage< ::tensorflow::SavedBareConcreteFunction >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.bare_concrete_function)
  return kind_.bare_concrete_function_;
}

// .tensorflow.SavedConstant constant = 9;
inline bool SavedObject::has_constant() const {
  return kind_case() == kConstant;
}
inline void SavedObject::set_has_constant() {
  _oneof_case_[0] = kConstant;
}
inline void SavedObject::clear_constant() {
  if (has_constant()) {
    if (GetArenaNoVirtual() == NULL) {
      delete kind_.constant_;
    }
    clear_has_kind();
  }
}
inline const ::tensorflow::SavedConstant& SavedObject::_internal_constant() const {
  return *kind_.constant_;
}
inline ::tensorflow::SavedConstant* SavedObject::release_constant() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedObject.constant)
  if (has_constant()) {
    clear_has_kind();
      ::tensorflow::SavedConstant* temp = kind_.constant_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    kind_.constant_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::SavedConstant& SavedObject::constant() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.constant)
  return has_constant()
      ? *kind_.constant_
      : *reinterpret_cast< ::tensorflow::SavedConstant*>(&::tensorflow::_SavedConstant_default_instance_);
}
inline ::tensorflow::SavedConstant* SavedObject::unsafe_arena_release_constant() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedObject.constant)
  if (has_constant()) {
    clear_has_kind();
    ::tensorflow::SavedConstant* temp = kind_.constant_;
    kind_.constant_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void SavedObject::unsafe_arena_set_allocated_constant(::tensorflow::SavedConstant* constant) {
  clear_kind();
  if (constant) {
    set_has_constant();
    kind_.constant_ = constant;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedObject.constant)
}
inline ::tensorflow::SavedConstant* SavedObject::mutable_constant() {
  if (!has_constant()) {
    clear_kind();
    set_has_constant();
    kind_.constant_ = CreateMaybeMessage< ::tensorflow::SavedConstant >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.constant)
  return kind_.constant_;
}

// .tensorflow.SavedResource resource = 10;
inline bool SavedObject::has_resource() const {
  return kind_case() == kResource;
}
inline void SavedObject::set_has_resource() {
  _oneof_case_[0] = kResource;
}
inline void SavedObject::clear_resource() {
  if (has_resource()) {
    if (GetArenaNoVirtual() == NULL) {
      delete kind_.resource_;
    }
    clear_has_kind();
  }
}
inline const ::tensorflow::SavedResource& SavedObject::_internal_resource() const {
  return *kind_.resource_;
}
inline ::tensorflow::SavedResource* SavedObject::release_resource() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedObject.resource)
  if (has_resource()) {
    clear_has_kind();
      ::tensorflow::SavedResource* temp = kind_.resource_;
    if (GetArenaNoVirtual() != NULL) {
      temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
    }
    kind_.resource_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::SavedResource& SavedObject::resource() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.resource)
  return has_resource()
      ? *kind_.resource_
      : *reinterpret_cast< ::tensorflow::SavedResource*>(&::tensorflow::_SavedResource_default_instance_);
}
inline ::tensorflow::SavedResource* SavedObject::unsafe_arena_release_resource() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedObject.resource)
  if (has_resource()) {
    clear_has_kind();
    ::tensorflow::SavedResource* temp = kind_.resource_;
    kind_.resource_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline void SavedObject::unsafe_arena_set_allocated_resource(::tensorflow::SavedResource* resource) {
  clear_kind();
  if (resource) {
    set_has_resource();
    kind_.resource_ = resource;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedObject.resource)
}
inline ::tensorflow::SavedResource* SavedObject::mutable_resource() {
  if (!has_resource()) {
    clear_kind();
    set_has_resource();
    kind_.resource_ = CreateMaybeMessage< ::tensorflow::SavedResource >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.resource)
  return kind_.resource_;
}

inline bool SavedObject::has_kind() const {
  return kind_case() != KIND_NOT_SET;
}
inline void SavedObject::clear_has_kind() {
  _oneof_case_[0] = KIND_NOT_SET;
}
inline SavedObject::KindCase SavedObject::kind_case() const {
  return SavedObject::KindCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// SavedUserObject

// string identifier = 1;
inline void SavedUserObject::clear_identifier() {
  identifier_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& SavedUserObject::identifier() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedUserObject.identifier)
  return identifier_.Get();
}
inline void SavedUserObject::set_identifier(const ::std::string& value) {
  
  identifier_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SavedUserObject.identifier)
}
#if LANG_CXX11
inline void SavedUserObject::set_identifier(::std::string&& value) {
  
  identifier_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SavedUserObject.identifier)
}
#endif
inline void SavedUserObject::set_identifier(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  identifier_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SavedUserObject.identifier)
}
inline void SavedUserObject::set_identifier(const char* value,
    size_t size) {
  
  identifier_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SavedUserObject.identifier)
}
inline ::std::string* SavedUserObject::mutable_identifier() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedUserObject.identifier)
  return identifier_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* SavedUserObject::release_identifier() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedUserObject.identifier)
  
  return identifier_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SavedUserObject::set_allocated_identifier(::std::string* identifier) {
  if (identifier != NULL) {
    
  } else {
    
  }
  identifier_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), identifier,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedUserObject.identifier)
}
inline ::std::string* SavedUserObject::unsafe_arena_release_identifier() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedUserObject.identifier)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return identifier_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SavedUserObject::unsafe_arena_set_allocated_identifier(
    ::std::string* identifier) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (identifier != NULL) {
    
  } else {
    
  }
  identifier_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      identifier, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedUserObject.identifier)
}

// .tensorflow.VersionDef version = 2;
inline bool SavedUserObject::has_version() const {
  return this != internal_default_instance() && version_ != NULL;
}
inline const ::tensorflow::VersionDef& SavedUserObject::_internal_version() const {
  return *version_;
}
inline const ::tensorflow::VersionDef& SavedUserObject::version() const {
  const ::tensorflow::VersionDef* p = version_;
  // @@protoc_insertion_point(field_get:tensorflow.SavedUserObject.version)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::VersionDef*>(
      &::tensorflow::_VersionDef_default_instance_);
}
inline ::tensorflow::VersionDef* SavedUserObject::release_version() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedUserObject.version)
  
  ::tensorflow::VersionDef* temp = version_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  version_ = NULL;
  return temp;
}
inline ::tensorflow::VersionDef* SavedUserObject::unsafe_arena_release_version() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedUserObject.version)
  
  ::tensorflow::VersionDef* temp = version_;
  version_ = NULL;
  return temp;
}
inline ::tensorflow::VersionDef* SavedUserObject::mutable_version() {
  
  if (version_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::VersionDef>(GetArenaNoVirtual());
    version_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedUserObject.version)
  return version_;
}
inline void SavedUserObject::set_allocated_version(::tensorflow::VersionDef* version) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(version_);
  }
  if (version) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(version)->GetArena();
    if (message_arena != submessage_arena) {
      version = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, version, submessage_arena);
    }
    
  } else {
    
  }
  version_ = version;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedUserObject.version)
}

// -------------------------------------------------------------------

// SavedAsset

// int32 asset_file_def_index = 1;
inline void SavedAsset::clear_asset_file_def_index() {
  asset_file_def_index_ = 0;
}
inline ::google::protobuf::int32 SavedAsset::asset_file_def_index() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedAsset.asset_file_def_index)
  return asset_file_def_index_;
}
inline void SavedAsset::set_asset_file_def_index(::google::protobuf::int32 value) {
  
  asset_file_def_index_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.SavedAsset.asset_file_def_index)
}

// -------------------------------------------------------------------

// SavedFunction

// repeated string concrete_functions = 1;
inline int SavedFunction::concrete_functions_size() const {
  return concrete_functions_.size();
}
inline void SavedFunction::clear_concrete_functions() {
  concrete_functions_.Clear();
}
inline const ::std::string& SavedFunction::concrete_functions(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedFunction.concrete_functions)
  return concrete_functions_.Get(index);
}
inline ::std::string* SavedFunction::mutable_concrete_functions(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedFunction.concrete_functions)
  return concrete_functions_.Mutable(index);
}
inline void SavedFunction::set_concrete_functions(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.SavedFunction.concrete_functions)
  concrete_functions_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void SavedFunction::set_concrete_functions(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.SavedFunction.concrete_functions)
  concrete_functions_.Mutable(index)->assign(std::move(value));
}
#endif
inline void SavedFunction::set_concrete_functions(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  concrete_functions_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.SavedFunction.concrete_functions)
}
inline void SavedFunction::set_concrete_functions(int index, const char* value, size_t size) {
  concrete_functions_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SavedFunction.concrete_functions)
}
inline ::std::string* SavedFunction::add_concrete_functions() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.SavedFunction.concrete_functions)
  return concrete_functions_.Add();
}
inline void SavedFunction::add_concrete_functions(const ::std::string& value) {
  concrete_functions_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.SavedFunction.concrete_functions)
}
#if LANG_CXX11
inline void SavedFunction::add_concrete_functions(::std::string&& value) {
  concrete_functions_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.SavedFunction.concrete_functions)
}
#endif
inline void SavedFunction::add_concrete_functions(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  concrete_functions_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.SavedFunction.concrete_functions)
}
inline void SavedFunction::add_concrete_functions(const char* value, size_t size) {
  concrete_functions_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.SavedFunction.concrete_functions)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
SavedFunction::concrete_functions() const {
  // @@protoc_insertion_point(field_list:tensorflow.SavedFunction.concrete_functions)
  return concrete_functions_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
SavedFunction::mutable_concrete_functions() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SavedFunction.concrete_functions)
  return &concrete_functions_;
}

// .tensorflow.FunctionSpec function_spec = 2;
inline bool SavedFunction::has_function_spec() const {
  return this != internal_default_instance() && function_spec_ != NULL;
}
inline void SavedFunction::clear_function_spec() {
  if (GetArenaNoVirtual() == NULL && function_spec_ != NULL) {
    delete function_spec_;
  }
  function_spec_ = NULL;
}
inline const ::tensorflow::FunctionSpec& SavedFunction::_internal_function_spec() const {
  return *function_spec_;
}
inline const ::tensorflow::FunctionSpec& SavedFunction::function_spec() const {
  const ::tensorflow::FunctionSpec* p = function_spec_;
  // @@protoc_insertion_point(field_get:tensorflow.SavedFunction.function_spec)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::FunctionSpec*>(
      &::tensorflow::_FunctionSpec_default_instance_);
}
inline ::tensorflow::FunctionSpec* SavedFunction::release_function_spec() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedFunction.function_spec)
  
  ::tensorflow::FunctionSpec* temp = function_spec_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  function_spec_ = NULL;
  return temp;
}
inline ::tensorflow::FunctionSpec* SavedFunction::unsafe_arena_release_function_spec() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedFunction.function_spec)
  
  ::tensorflow::FunctionSpec* temp = function_spec_;
  function_spec_ = NULL;
  return temp;
}
inline ::tensorflow::FunctionSpec* SavedFunction::mutable_function_spec() {
  
  if (function_spec_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::FunctionSpec>(GetArenaNoVirtual());
    function_spec_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedFunction.function_spec)
  return function_spec_;
}
inline void SavedFunction::set_allocated_function_spec(::tensorflow::FunctionSpec* function_spec) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete function_spec_;
  }
  if (function_spec) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(function_spec);
    if (message_arena != submessage_arena) {
      function_spec = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, function_spec, submessage_arena);
    }
    
  } else {
    
  }
  function_spec_ = function_spec;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedFunction.function_spec)
}

// -------------------------------------------------------------------

// SavedConcreteFunction

// repeated int32 bound_inputs = 2;
inline int SavedConcreteFunction::bound_inputs_size() const {
  return bound_inputs_.size();
}
inline void SavedConcreteFunction::clear_bound_inputs() {
  bound_inputs_.Clear();
}
inline ::google::protobuf::int32 SavedConcreteFunction::bound_inputs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedConcreteFunction.bound_inputs)
  return bound_inputs_.Get(index);
}
inline void SavedConcreteFunction::set_bound_inputs(int index, ::google::protobuf::int32 value) {
  bound_inputs_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.SavedConcreteFunction.bound_inputs)
}
inline void SavedConcreteFunction::add_bound_inputs(::google::protobuf::int32 value) {
  bound_inputs_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.SavedConcreteFunction.bound_inputs)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
SavedConcreteFunction::bound_inputs() const {
  // @@protoc_insertion_point(field_list:tensorflow.SavedConcreteFunction.bound_inputs)
  return bound_inputs_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
SavedConcreteFunction::mutable_bound_inputs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SavedConcreteFunction.bound_inputs)
  return &bound_inputs_;
}

// .tensorflow.StructuredValue canonicalized_input_signature = 3;
inline bool SavedConcreteFunction::has_canonicalized_input_signature() const {
  return this != internal_default_instance() && canonicalized_input_signature_ != NULL;
}
inline const ::tensorflow::StructuredValue& SavedConcreteFunction::_internal_canonicalized_input_signature() const {
  return *canonicalized_input_signature_;
}
inline const ::tensorflow::StructuredValue& SavedConcreteFunction::canonicalized_input_signature() const {
  const ::tensorflow::StructuredValue* p = canonicalized_input_signature_;
  // @@protoc_insertion_point(field_get:tensorflow.SavedConcreteFunction.canonicalized_input_signature)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::StructuredValue*>(
      &::tensorflow::_StructuredValue_default_instance_);
}
inline ::tensorflow::StructuredValue* SavedConcreteFunction::release_canonicalized_input_signature() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedConcreteFunction.canonicalized_input_signature)
  
  ::tensorflow::StructuredValue* temp = canonicalized_input_signature_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  canonicalized_input_signature_ = NULL;
  return temp;
}
inline ::tensorflow::StructuredValue* SavedConcreteFunction::unsafe_arena_release_canonicalized_input_signature() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedConcreteFunction.canonicalized_input_signature)
  
  ::tensorflow::StructuredValue* temp = canonicalized_input_signature_;
  canonicalized_input_signature_ = NULL;
  return temp;
}
inline ::tensorflow::StructuredValue* SavedConcreteFunction::mutable_canonicalized_input_signature() {
  
  if (canonicalized_input_signature_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::StructuredValue>(GetArenaNoVirtual());
    canonicalized_input_signature_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedConcreteFunction.canonicalized_input_signature)
  return canonicalized_input_signature_;
}
inline void SavedConcreteFunction::set_allocated_canonicalized_input_signature(::tensorflow::StructuredValue* canonicalized_input_signature) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(canonicalized_input_signature_);
  }
  if (canonicalized_input_signature) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      canonicalized_input_signature = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, canonicalized_input_signature, submessage_arena);
    }
    
  } else {
    
  }
  canonicalized_input_signature_ = canonicalized_input_signature;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedConcreteFunction.canonicalized_input_signature)
}

// .tensorflow.StructuredValue output_signature = 4;
inline bool SavedConcreteFunction::has_output_signature() const {
  return this != internal_default_instance() && output_signature_ != NULL;
}
inline const ::tensorflow::StructuredValue& SavedConcreteFunction::_internal_output_signature() const {
  return *output_signature_;
}
inline const ::tensorflow::StructuredValue& SavedConcreteFunction::output_signature() const {
  const ::tensorflow::StructuredValue* p = output_signature_;
  // @@protoc_insertion_point(field_get:tensorflow.SavedConcreteFunction.output_signature)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::StructuredValue*>(
      &::tensorflow::_StructuredValue_default_instance_);
}
inline ::tensorflow::StructuredValue* SavedConcreteFunction::release_output_signature() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedConcreteFunction.output_signature)
  
  ::tensorflow::StructuredValue* temp = output_signature_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  output_signature_ = NULL;
  return temp;
}
inline ::tensorflow::StructuredValue* SavedConcreteFunction::unsafe_arena_release_output_signature() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedConcreteFunction.output_signature)
  
  ::tensorflow::StructuredValue* temp = output_signature_;
  output_signature_ = NULL;
  return temp;
}
inline ::tensorflow::StructuredValue* SavedConcreteFunction::mutable_output_signature() {
  
  if (output_signature_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::StructuredValue>(GetArenaNoVirtual());
    output_signature_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedConcreteFunction.output_signature)
  return output_signature_;
}
inline void SavedConcreteFunction::set_allocated_output_signature(::tensorflow::StructuredValue* output_signature) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(output_signature_);
  }
  if (output_signature) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      output_signature = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, output_signature, submessage_arena);
    }
    
  } else {
    
  }
  output_signature_ = output_signature;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedConcreteFunction.output_signature)
}

// -------------------------------------------------------------------

// SavedBareConcreteFunction

// string concrete_function_name = 1;
inline void SavedBareConcreteFunction::clear_concrete_function_name() {
  concrete_function_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& SavedBareConcreteFunction::concrete_function_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedBareConcreteFunction.concrete_function_name)
  return concrete_function_name_.Get();
}
inline void SavedBareConcreteFunction::set_concrete_function_name(const ::std::string& value) {
  
  concrete_function_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SavedBareConcreteFunction.concrete_function_name)
}
#if LANG_CXX11
inline void SavedBareConcreteFunction::set_concrete_function_name(::std::string&& value) {
  
  concrete_function_name_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SavedBareConcreteFunction.concrete_function_name)
}
#endif
inline void SavedBareConcreteFunction::set_concrete_function_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  concrete_function_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SavedBareConcreteFunction.concrete_function_name)
}
inline void SavedBareConcreteFunction::set_concrete_function_name(const char* value,
    size_t size) {
  
  concrete_function_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SavedBareConcreteFunction.concrete_function_name)
}
inline ::std::string* SavedBareConcreteFunction::mutable_concrete_function_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedBareConcreteFunction.concrete_function_name)
  return concrete_function_name_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* SavedBareConcreteFunction::release_concrete_function_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedBareConcreteFunction.concrete_function_name)
  
  return concrete_function_name_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SavedBareConcreteFunction::set_allocated_concrete_function_name(::std::string* concrete_function_name) {
  if (concrete_function_name != NULL) {
    
  } else {
    
  }
  concrete_function_name_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), concrete_function_name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedBareConcreteFunction.concrete_function_name)
}
inline ::std::string* SavedBareConcreteFunction::unsafe_arena_release_concrete_function_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedBareConcreteFunction.concrete_function_name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return concrete_function_name_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SavedBareConcreteFunction::unsafe_arena_set_allocated_concrete_function_name(
    ::std::string* concrete_function_name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (concrete_function_name != NULL) {
    
  } else {
    
  }
  concrete_function_name_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      concrete_function_name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedBareConcreteFunction.concrete_function_name)
}

// repeated string argument_keywords = 2;
inline int SavedBareConcreteFunction::argument_keywords_size() const {
  return argument_keywords_.size();
}
inline void SavedBareConcreteFunction::clear_argument_keywords() {
  argument_keywords_.Clear();
}
inline const ::std::string& SavedBareConcreteFunction::argument_keywords(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedBareConcreteFunction.argument_keywords)
  return argument_keywords_.Get(index);
}
inline ::std::string* SavedBareConcreteFunction::mutable_argument_keywords(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedBareConcreteFunction.argument_keywords)
  return argument_keywords_.Mutable(index);
}
inline void SavedBareConcreteFunction::set_argument_keywords(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.SavedBareConcreteFunction.argument_keywords)
  argument_keywords_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void SavedBareConcreteFunction::set_argument_keywords(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.SavedBareConcreteFunction.argument_keywords)
  argument_keywords_.Mutable(index)->assign(std::move(value));
}
#endif
inline void SavedBareConcreteFunction::set_argument_keywords(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  argument_keywords_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.SavedBareConcreteFunction.argument_keywords)
}
inline void SavedBareConcreteFunction::set_argument_keywords(int index, const char* value, size_t size) {
  argument_keywords_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SavedBareConcreteFunction.argument_keywords)
}
inline ::std::string* SavedBareConcreteFunction::add_argument_keywords() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.SavedBareConcreteFunction.argument_keywords)
  return argument_keywords_.Add();
}
inline void SavedBareConcreteFunction::add_argument_keywords(const ::std::string& value) {
  argument_keywords_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.SavedBareConcreteFunction.argument_keywords)
}
#if LANG_CXX11
inline void SavedBareConcreteFunction::add_argument_keywords(::std::string&& value) {
  argument_keywords_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.SavedBareConcreteFunction.argument_keywords)
}
#endif
inline void SavedBareConcreteFunction::add_argument_keywords(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  argument_keywords_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.SavedBareConcreteFunction.argument_keywords)
}
inline void SavedBareConcreteFunction::add_argument_keywords(const char* value, size_t size) {
  argument_keywords_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.SavedBareConcreteFunction.argument_keywords)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
SavedBareConcreteFunction::argument_keywords() const {
  // @@protoc_insertion_point(field_list:tensorflow.SavedBareConcreteFunction.argument_keywords)
  return argument_keywords_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
SavedBareConcreteFunction::mutable_argument_keywords() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SavedBareConcreteFunction.argument_keywords)
  return &argument_keywords_;
}

// int64 allowed_positional_arguments = 3;
inline void SavedBareConcreteFunction::clear_allowed_positional_arguments() {
  allowed_positional_arguments_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SavedBareConcreteFunction::allowed_positional_arguments() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedBareConcreteFunction.allowed_positional_arguments)
  return allowed_positional_arguments_;
}
inline void SavedBareConcreteFunction::set_allowed_positional_arguments(::google::protobuf::int64 value) {
  
  allowed_positional_arguments_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.SavedBareConcreteFunction.allowed_positional_arguments)
}

// -------------------------------------------------------------------

// SavedConstant

// string operation = 1;
inline void SavedConstant::clear_operation() {
  operation_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& SavedConstant::operation() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedConstant.operation)
  return operation_.Get();
}
inline void SavedConstant::set_operation(const ::std::string& value) {
  
  operation_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.SavedConstant.operation)
}
#if LANG_CXX11
inline void SavedConstant::set_operation(::std::string&& value) {
  
  operation_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.SavedConstant.operation)
}
#endif
inline void SavedConstant::set_operation(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  operation_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.SavedConstant.operation)
}
inline void SavedConstant::set_operation(const char* value,
    size_t size) {
  
  operation_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SavedConstant.operation)
}
inline ::std::string* SavedConstant::mutable_operation() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedConstant.operation)
  return operation_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* SavedConstant::release_operation() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedConstant.operation)
  
  return operation_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void SavedConstant::set_allocated_operation(::std::string* operation) {
  if (operation != NULL) {
    
  } else {
    
  }
  operation_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), operation,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedConstant.operation)
}
inline ::std::string* SavedConstant::unsafe_arena_release_operation() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedConstant.operation)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return operation_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void SavedConstant::unsafe_arena_set_allocated_operation(
    ::std::string* operation) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (operation != NULL) {
    
  } else {
    
  }
  operation_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      operation, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedConstant.operation)
}

// -------------------------------------------------------------------

// SavedVariable

// .tensorflow.DataType dtype = 1;
inline void SavedVariable::clear_dtype() {
  dtype_ = 0;
}
inline ::tensorflow::DataType SavedVariable::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedVariable.dtype)
  return static_cast< ::tensorflow::DataType >(dtype_);
}
inline void SavedVariable::set_dtype(::tensorflow::DataType value) {
  
  dtype_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.SavedVariable.dtype)
}

// .tensorflow.TensorShapeProto shape = 2;
inline bool SavedVariable::has_shape() const {
  return this != internal_default_instance() && shape_ != NULL;
}
inline const ::tensorflow::TensorShapeProto& SavedVariable::_internal_shape() const {
  return *shape_;
}
inline const ::tensorflow::TensorShapeProto& SavedVariable::shape() const {
  const ::tensorflow::TensorShapeProto* p = shape_;
  // @@protoc_insertion_point(field_get:tensorflow.SavedVariable.shape)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::TensorShapeProto*>(
      &::tensorflow::_TensorShapeProto_default_instance_);
}
inline ::tensorflow::TensorShapeProto* SavedVariable::release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedVariable.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  shape_ = NULL;
  return temp;
}
inline ::tensorflow::TensorShapeProto* SavedVariable::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedVariable.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  shape_ = NULL;
  return temp;
}
inline ::tensorflow::TensorShapeProto* SavedVariable::mutable_shape() {
  
  if (shape_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaNoVirtual());
    shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedVariable.shape)
  return shape_;
}
inline void SavedVariable::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(shape_);
  }
  if (shape) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(shape)->GetArena();
    if (message_arena != submessage_arena) {
      shape = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedVariable.shape)
}

// bool trainable = 3;
inline void SavedVariable::clear_trainable() {
  trainable_ = false;
}
inline bool SavedVariable::trainable() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedVariable.trainable)
  return trainable_;
}
inline void SavedVariable::set_trainable(bool value) {
  
  trainable_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.SavedVariable.trainable)
}

// -------------------------------------------------------------------

// FunctionSpec

// .tensorflow.StructuredValue fullargspec = 1;
inline bool FunctionSpec::has_fullargspec() const {
  return this != internal_default_instance() && fullargspec_ != NULL;
}
inline const ::tensorflow::StructuredValue& FunctionSpec::_internal_fullargspec() const {
  return *fullargspec_;
}
inline const ::tensorflow::StructuredValue& FunctionSpec::fullargspec() const {
  const ::tensorflow::StructuredValue* p = fullargspec_;
  // @@protoc_insertion_point(field_get:tensorflow.FunctionSpec.fullargspec)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::StructuredValue*>(
      &::tensorflow::_StructuredValue_default_instance_);
}
inline ::tensorflow::StructuredValue* FunctionSpec::release_fullargspec() {
  // @@protoc_insertion_point(field_release:tensorflow.FunctionSpec.fullargspec)
  
  ::tensorflow::StructuredValue* temp = fullargspec_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  fullargspec_ = NULL;
  return temp;
}
inline ::tensorflow::StructuredValue* FunctionSpec::unsafe_arena_release_fullargspec() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.FunctionSpec.fullargspec)
  
  ::tensorflow::StructuredValue* temp = fullargspec_;
  fullargspec_ = NULL;
  return temp;
}
inline ::tensorflow::StructuredValue* FunctionSpec::mutable_fullargspec() {
  
  if (fullargspec_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::StructuredValue>(GetArenaNoVirtual());
    fullargspec_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.FunctionSpec.fullargspec)
  return fullargspec_;
}
inline void FunctionSpec::set_allocated_fullargspec(::tensorflow::StructuredValue* fullargspec) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(fullargspec_);
  }
  if (fullargspec) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      fullargspec = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, fullargspec, submessage_arena);
    }
    
  } else {
    
  }
  fullargspec_ = fullargspec;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.FunctionSpec.fullargspec)
}

// bool is_method = 2;
inline void FunctionSpec::clear_is_method() {
  is_method_ = false;
}
inline bool FunctionSpec::is_method() const {
  // @@protoc_insertion_point(field_get:tensorflow.FunctionSpec.is_method)
  return is_method_;
}
inline void FunctionSpec::set_is_method(bool value) {
  
  is_method_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.FunctionSpec.is_method)
}

// .tensorflow.StructuredValue args_to_prepend = 3;
inline bool FunctionSpec::has_args_to_prepend() const {
  return this != internal_default_instance() && args_to_prepend_ != NULL;
}
inline const ::tensorflow::StructuredValue& FunctionSpec::_internal_args_to_prepend() const {
  return *args_to_prepend_;
}
inline const ::tensorflow::StructuredValue& FunctionSpec::args_to_prepend() const {
  const ::tensorflow::StructuredValue* p = args_to_prepend_;
  // @@protoc_insertion_point(field_get:tensorflow.FunctionSpec.args_to_prepend)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::StructuredValue*>(
      &::tensorflow::_StructuredValue_default_instance_);
}
inline ::tensorflow::StructuredValue* FunctionSpec::release_args_to_prepend() {
  // @@protoc_insertion_point(field_release:tensorflow.FunctionSpec.args_to_prepend)
  
  ::tensorflow::StructuredValue* temp = args_to_prepend_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  args_to_prepend_ = NULL;
  return temp;
}
inline ::tensorflow::StructuredValue* FunctionSpec::unsafe_arena_release_args_to_prepend() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.FunctionSpec.args_to_prepend)
  
  ::tensorflow::StructuredValue* temp = args_to_prepend_;
  args_to_prepend_ = NULL;
  return temp;
}
inline ::tensorflow::StructuredValue* FunctionSpec::mutable_args_to_prepend() {
  
  if (args_to_prepend_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::StructuredValue>(GetArenaNoVirtual());
    args_to_prepend_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.FunctionSpec.args_to_prepend)
  return args_to_prepend_;
}
inline void FunctionSpec::set_allocated_args_to_prepend(::tensorflow::StructuredValue* args_to_prepend) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(args_to_prepend_);
  }
  if (args_to_prepend) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      args_to_prepend = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, args_to_prepend, submessage_arena);
    }
    
  } else {
    
  }
  args_to_prepend_ = args_to_prepend;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.FunctionSpec.args_to_prepend)
}

// .tensorflow.StructuredValue kwargs_to_include = 4;
inline bool FunctionSpec::has_kwargs_to_include() const {
  return this != internal_default_instance() && kwargs_to_include_ != NULL;
}
inline const ::tensorflow::StructuredValue& FunctionSpec::_internal_kwargs_to_include() const {
  return *kwargs_to_include_;
}
inline const ::tensorflow::StructuredValue& FunctionSpec::kwargs_to_include() const {
  const ::tensorflow::StructuredValue* p = kwargs_to_include_;
  // @@protoc_insertion_point(field_get:tensorflow.FunctionSpec.kwargs_to_include)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::StructuredValue*>(
      &::tensorflow::_StructuredValue_default_instance_);
}
inline ::tensorflow::StructuredValue* FunctionSpec::release_kwargs_to_include() {
  // @@protoc_insertion_point(field_release:tensorflow.FunctionSpec.kwargs_to_include)
  
  ::tensorflow::StructuredValue* temp = kwargs_to_include_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  kwargs_to_include_ = NULL;
  return temp;
}
inline ::tensorflow::StructuredValue* FunctionSpec::unsafe_arena_release_kwargs_to_include() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.FunctionSpec.kwargs_to_include)
  
  ::tensorflow::StructuredValue* temp = kwargs_to_include_;
  kwargs_to_include_ = NULL;
  return temp;
}
inline ::tensorflow::StructuredValue* FunctionSpec::mutable_kwargs_to_include() {
  
  if (kwargs_to_include_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::StructuredValue>(GetArenaNoVirtual());
    kwargs_to_include_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.FunctionSpec.kwargs_to_include)
  return kwargs_to_include_;
}
inline void FunctionSpec::set_allocated_kwargs_to_include(::tensorflow::StructuredValue* kwargs_to_include) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(kwargs_to_include_);
  }
  if (kwargs_to_include) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      kwargs_to_include = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, kwargs_to_include, submessage_arena);
    }
    
  } else {
    
  }
  kwargs_to_include_ = kwargs_to_include;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.FunctionSpec.kwargs_to_include)
}

// .tensorflow.StructuredValue input_signature = 5;
inline bool FunctionSpec::has_input_signature() const {
  return this != internal_default_instance() && input_signature_ != NULL;
}
inline const ::tensorflow::StructuredValue& FunctionSpec::_internal_input_signature() const {
  return *input_signature_;
}
inline const ::tensorflow::StructuredValue& FunctionSpec::input_signature() const {
  const ::tensorflow::StructuredValue* p = input_signature_;
  // @@protoc_insertion_point(field_get:tensorflow.FunctionSpec.input_signature)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::StructuredValue*>(
      &::tensorflow::_StructuredValue_default_instance_);
}
inline ::tensorflow::StructuredValue* FunctionSpec::release_input_signature() {
  // @@protoc_insertion_point(field_release:tensorflow.FunctionSpec.input_signature)
  
  ::tensorflow::StructuredValue* temp = input_signature_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  input_signature_ = NULL;
  return temp;
}
inline ::tensorflow::StructuredValue* FunctionSpec::unsafe_arena_release_input_signature() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.FunctionSpec.input_signature)
  
  ::tensorflow::StructuredValue* temp = input_signature_;
  input_signature_ = NULL;
  return temp;
}
inline ::tensorflow::StructuredValue* FunctionSpec::mutable_input_signature() {
  
  if (input_signature_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::StructuredValue>(GetArenaNoVirtual());
    input_signature_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.FunctionSpec.input_signature)
  return input_signature_;
}
inline void FunctionSpec::set_allocated_input_signature(::tensorflow::StructuredValue* input_signature) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(input_signature_);
  }
  if (input_signature) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      input_signature = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, input_signature, submessage_arena);
    }
    
  } else {
    
  }
  input_signature_ = input_signature;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.FunctionSpec.input_signature)
}

// -------------------------------------------------------------------

// SavedResource

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto

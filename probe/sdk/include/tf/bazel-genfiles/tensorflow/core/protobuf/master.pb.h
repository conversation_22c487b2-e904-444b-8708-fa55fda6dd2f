// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/master.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/device_attributes.pb.h"
#include "tensorflow/core/framework/graph.pb.h"
#include "tensorflow/core/framework/tensor.pb.h"
#include "tensorflow/core/lib/core/error_codes.pb.h"
#include "tensorflow/core/protobuf/config.pb.h"
#include "tensorflow/core/protobuf/named_tensor.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto 

namespace protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[20];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto
namespace tensorflow {
class CloseSessionRequest;
class CloseSessionRequestDefaultTypeInternal;
extern CloseSessionRequestDefaultTypeInternal _CloseSessionRequest_default_instance_;
class CloseSessionResponse;
class CloseSessionResponseDefaultTypeInternal;
extern CloseSessionResponseDefaultTypeInternal _CloseSessionResponse_default_instance_;
class CreateSessionRequest;
class CreateSessionRequestDefaultTypeInternal;
extern CreateSessionRequestDefaultTypeInternal _CreateSessionRequest_default_instance_;
class CreateSessionResponse;
class CreateSessionResponseDefaultTypeInternal;
extern CreateSessionResponseDefaultTypeInternal _CreateSessionResponse_default_instance_;
class ExtendSessionRequest;
class ExtendSessionRequestDefaultTypeInternal;
extern ExtendSessionRequestDefaultTypeInternal _ExtendSessionRequest_default_instance_;
class ExtendSessionResponse;
class ExtendSessionResponseDefaultTypeInternal;
extern ExtendSessionResponseDefaultTypeInternal _ExtendSessionResponse_default_instance_;
class ListDevicesRequest;
class ListDevicesRequestDefaultTypeInternal;
extern ListDevicesRequestDefaultTypeInternal _ListDevicesRequest_default_instance_;
class ListDevicesResponse;
class ListDevicesResponseDefaultTypeInternal;
extern ListDevicesResponseDefaultTypeInternal _ListDevicesResponse_default_instance_;
class MakeCallableRequest;
class MakeCallableRequestDefaultTypeInternal;
extern MakeCallableRequestDefaultTypeInternal _MakeCallableRequest_default_instance_;
class MakeCallableResponse;
class MakeCallableResponseDefaultTypeInternal;
extern MakeCallableResponseDefaultTypeInternal _MakeCallableResponse_default_instance_;
class PartialRunSetupRequest;
class PartialRunSetupRequestDefaultTypeInternal;
extern PartialRunSetupRequestDefaultTypeInternal _PartialRunSetupRequest_default_instance_;
class PartialRunSetupResponse;
class PartialRunSetupResponseDefaultTypeInternal;
extern PartialRunSetupResponseDefaultTypeInternal _PartialRunSetupResponse_default_instance_;
class ReleaseCallableRequest;
class ReleaseCallableRequestDefaultTypeInternal;
extern ReleaseCallableRequestDefaultTypeInternal _ReleaseCallableRequest_default_instance_;
class ReleaseCallableResponse;
class ReleaseCallableResponseDefaultTypeInternal;
extern ReleaseCallableResponseDefaultTypeInternal _ReleaseCallableResponse_default_instance_;
class ResetRequest;
class ResetRequestDefaultTypeInternal;
extern ResetRequestDefaultTypeInternal _ResetRequest_default_instance_;
class ResetResponse;
class ResetResponseDefaultTypeInternal;
extern ResetResponseDefaultTypeInternal _ResetResponse_default_instance_;
class RunCallableRequest;
class RunCallableRequestDefaultTypeInternal;
extern RunCallableRequestDefaultTypeInternal _RunCallableRequest_default_instance_;
class RunCallableResponse;
class RunCallableResponseDefaultTypeInternal;
extern RunCallableResponseDefaultTypeInternal _RunCallableResponse_default_instance_;
class RunStepRequest;
class RunStepRequestDefaultTypeInternal;
extern RunStepRequestDefaultTypeInternal _RunStepRequest_default_instance_;
class RunStepResponse;
class RunStepResponseDefaultTypeInternal;
extern RunStepResponseDefaultTypeInternal _RunStepResponse_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::CloseSessionRequest* Arena::CreateMaybeMessage<::tensorflow::CloseSessionRequest>(Arena*);
template<> ::tensorflow::CloseSessionResponse* Arena::CreateMaybeMessage<::tensorflow::CloseSessionResponse>(Arena*);
template<> ::tensorflow::CreateSessionRequest* Arena::CreateMaybeMessage<::tensorflow::CreateSessionRequest>(Arena*);
template<> ::tensorflow::CreateSessionResponse* Arena::CreateMaybeMessage<::tensorflow::CreateSessionResponse>(Arena*);
template<> ::tensorflow::ExtendSessionRequest* Arena::CreateMaybeMessage<::tensorflow::ExtendSessionRequest>(Arena*);
template<> ::tensorflow::ExtendSessionResponse* Arena::CreateMaybeMessage<::tensorflow::ExtendSessionResponse>(Arena*);
template<> ::tensorflow::ListDevicesRequest* Arena::CreateMaybeMessage<::tensorflow::ListDevicesRequest>(Arena*);
template<> ::tensorflow::ListDevicesResponse* Arena::CreateMaybeMessage<::tensorflow::ListDevicesResponse>(Arena*);
template<> ::tensorflow::MakeCallableRequest* Arena::CreateMaybeMessage<::tensorflow::MakeCallableRequest>(Arena*);
template<> ::tensorflow::MakeCallableResponse* Arena::CreateMaybeMessage<::tensorflow::MakeCallableResponse>(Arena*);
template<> ::tensorflow::PartialRunSetupRequest* Arena::CreateMaybeMessage<::tensorflow::PartialRunSetupRequest>(Arena*);
template<> ::tensorflow::PartialRunSetupResponse* Arena::CreateMaybeMessage<::tensorflow::PartialRunSetupResponse>(Arena*);
template<> ::tensorflow::ReleaseCallableRequest* Arena::CreateMaybeMessage<::tensorflow::ReleaseCallableRequest>(Arena*);
template<> ::tensorflow::ReleaseCallableResponse* Arena::CreateMaybeMessage<::tensorflow::ReleaseCallableResponse>(Arena*);
template<> ::tensorflow::ResetRequest* Arena::CreateMaybeMessage<::tensorflow::ResetRequest>(Arena*);
template<> ::tensorflow::ResetResponse* Arena::CreateMaybeMessage<::tensorflow::ResetResponse>(Arena*);
template<> ::tensorflow::RunCallableRequest* Arena::CreateMaybeMessage<::tensorflow::RunCallableRequest>(Arena*);
template<> ::tensorflow::RunCallableResponse* Arena::CreateMaybeMessage<::tensorflow::RunCallableResponse>(Arena*);
template<> ::tensorflow::RunStepRequest* Arena::CreateMaybeMessage<::tensorflow::RunStepRequest>(Arena*);
template<> ::tensorflow::RunStepResponse* Arena::CreateMaybeMessage<::tensorflow::RunStepResponse>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

// ===================================================================

class CreateSessionRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.CreateSessionRequest) */ {
 public:
  CreateSessionRequest();
  virtual ~CreateSessionRequest();

  CreateSessionRequest(const CreateSessionRequest& from);

  inline CreateSessionRequest& operator=(const CreateSessionRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CreateSessionRequest(CreateSessionRequest&& from) noexcept
    : CreateSessionRequest() {
    *this = ::std::move(from);
  }

  inline CreateSessionRequest& operator=(CreateSessionRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const CreateSessionRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CreateSessionRequest* internal_default_instance() {
    return reinterpret_cast<const CreateSessionRequest*>(
               &_CreateSessionRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(CreateSessionRequest* other);
  void Swap(CreateSessionRequest* other);
  friend void swap(CreateSessionRequest& a, CreateSessionRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CreateSessionRequest* New() const final {
    return CreateMaybeMessage<CreateSessionRequest>(NULL);
  }

  CreateSessionRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CreateSessionRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CreateSessionRequest& from);
  void MergeFrom(const CreateSessionRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CreateSessionRequest* other);
  protected:
  explicit CreateSessionRequest(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string target = 3;
  void clear_target();
  static const int kTargetFieldNumber = 3;
  const ::std::string& target() const;
  void set_target(const ::std::string& value);
  #if LANG_CXX11
  void set_target(::std::string&& value);
  #endif
  void set_target(const char* value);
  void set_target(const char* value, size_t size);
  ::std::string* mutable_target();
  ::std::string* release_target();
  void set_allocated_target(::std::string* target);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_target();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_target(
      ::std::string* target);

  // .tensorflow.GraphDef graph_def = 1;
  bool has_graph_def() const;
  void clear_graph_def();
  static const int kGraphDefFieldNumber = 1;
  private:
  const ::tensorflow::GraphDef& _internal_graph_def() const;
  public:
  const ::tensorflow::GraphDef& graph_def() const;
  ::tensorflow::GraphDef* release_graph_def();
  ::tensorflow::GraphDef* mutable_graph_def();
  void set_allocated_graph_def(::tensorflow::GraphDef* graph_def);
  void unsafe_arena_set_allocated_graph_def(
      ::tensorflow::GraphDef* graph_def);
  ::tensorflow::GraphDef* unsafe_arena_release_graph_def();

  // .tensorflow.ConfigProto config = 2;
  bool has_config() const;
  void clear_config();
  static const int kConfigFieldNumber = 2;
  private:
  const ::tensorflow::ConfigProto& _internal_config() const;
  public:
  const ::tensorflow::ConfigProto& config() const;
  ::tensorflow::ConfigProto* release_config();
  ::tensorflow::ConfigProto* mutable_config();
  void set_allocated_config(::tensorflow::ConfigProto* config);
  void unsafe_arena_set_allocated_config(
      ::tensorflow::ConfigProto* config);
  ::tensorflow::ConfigProto* unsafe_arena_release_config();

  // @@protoc_insertion_point(class_scope:tensorflow.CreateSessionRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr target_;
  ::tensorflow::GraphDef* graph_def_;
  ::tensorflow::ConfigProto* config_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CreateSessionResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.CreateSessionResponse) */ {
 public:
  CreateSessionResponse();
  virtual ~CreateSessionResponse();

  CreateSessionResponse(const CreateSessionResponse& from);

  inline CreateSessionResponse& operator=(const CreateSessionResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CreateSessionResponse(CreateSessionResponse&& from) noexcept
    : CreateSessionResponse() {
    *this = ::std::move(from);
  }

  inline CreateSessionResponse& operator=(CreateSessionResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const CreateSessionResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CreateSessionResponse* internal_default_instance() {
    return reinterpret_cast<const CreateSessionResponse*>(
               &_CreateSessionResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void UnsafeArenaSwap(CreateSessionResponse* other);
  void Swap(CreateSessionResponse* other);
  friend void swap(CreateSessionResponse& a, CreateSessionResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CreateSessionResponse* New() const final {
    return CreateMaybeMessage<CreateSessionResponse>(NULL);
  }

  CreateSessionResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CreateSessionResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CreateSessionResponse& from);
  void MergeFrom(const CreateSessionResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CreateSessionResponse* other);
  protected:
  explicit CreateSessionResponse(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string session_handle = 1;
  void clear_session_handle();
  static const int kSessionHandleFieldNumber = 1;
  const ::std::string& session_handle() const;
  void set_session_handle(const ::std::string& value);
  #if LANG_CXX11
  void set_session_handle(::std::string&& value);
  #endif
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  ::std::string* mutable_session_handle();
  ::std::string* release_session_handle();
  void set_allocated_session_handle(::std::string* session_handle);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_session_handle();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      ::std::string* session_handle);

  // int64 graph_version = 2;
  void clear_graph_version();
  static const int kGraphVersionFieldNumber = 2;
  ::google::protobuf::int64 graph_version() const;
  void set_graph_version(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.CreateSessionResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr session_handle_;
  ::google::protobuf::int64 graph_version_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ExtendSessionRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.ExtendSessionRequest) */ {
 public:
  ExtendSessionRequest();
  virtual ~ExtendSessionRequest();

  ExtendSessionRequest(const ExtendSessionRequest& from);

  inline ExtendSessionRequest& operator=(const ExtendSessionRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ExtendSessionRequest(ExtendSessionRequest&& from) noexcept
    : ExtendSessionRequest() {
    *this = ::std::move(from);
  }

  inline ExtendSessionRequest& operator=(ExtendSessionRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const ExtendSessionRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ExtendSessionRequest* internal_default_instance() {
    return reinterpret_cast<const ExtendSessionRequest*>(
               &_ExtendSessionRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void UnsafeArenaSwap(ExtendSessionRequest* other);
  void Swap(ExtendSessionRequest* other);
  friend void swap(ExtendSessionRequest& a, ExtendSessionRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ExtendSessionRequest* New() const final {
    return CreateMaybeMessage<ExtendSessionRequest>(NULL);
  }

  ExtendSessionRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ExtendSessionRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ExtendSessionRequest& from);
  void MergeFrom(const ExtendSessionRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExtendSessionRequest* other);
  protected:
  explicit ExtendSessionRequest(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string session_handle = 1;
  void clear_session_handle();
  static const int kSessionHandleFieldNumber = 1;
  const ::std::string& session_handle() const;
  void set_session_handle(const ::std::string& value);
  #if LANG_CXX11
  void set_session_handle(::std::string&& value);
  #endif
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  ::std::string* mutable_session_handle();
  ::std::string* release_session_handle();
  void set_allocated_session_handle(::std::string* session_handle);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_session_handle();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      ::std::string* session_handle);

  // .tensorflow.GraphDef graph_def = 2;
  bool has_graph_def() const;
  void clear_graph_def();
  static const int kGraphDefFieldNumber = 2;
  private:
  const ::tensorflow::GraphDef& _internal_graph_def() const;
  public:
  const ::tensorflow::GraphDef& graph_def() const;
  ::tensorflow::GraphDef* release_graph_def();
  ::tensorflow::GraphDef* mutable_graph_def();
  void set_allocated_graph_def(::tensorflow::GraphDef* graph_def);
  void unsafe_arena_set_allocated_graph_def(
      ::tensorflow::GraphDef* graph_def);
  ::tensorflow::GraphDef* unsafe_arena_release_graph_def();

  // int64 current_graph_version = 3;
  void clear_current_graph_version();
  static const int kCurrentGraphVersionFieldNumber = 3;
  ::google::protobuf::int64 current_graph_version() const;
  void set_current_graph_version(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.ExtendSessionRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr session_handle_;
  ::tensorflow::GraphDef* graph_def_;
  ::google::protobuf::int64 current_graph_version_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ExtendSessionResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.ExtendSessionResponse) */ {
 public:
  ExtendSessionResponse();
  virtual ~ExtendSessionResponse();

  ExtendSessionResponse(const ExtendSessionResponse& from);

  inline ExtendSessionResponse& operator=(const ExtendSessionResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ExtendSessionResponse(ExtendSessionResponse&& from) noexcept
    : ExtendSessionResponse() {
    *this = ::std::move(from);
  }

  inline ExtendSessionResponse& operator=(ExtendSessionResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const ExtendSessionResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ExtendSessionResponse* internal_default_instance() {
    return reinterpret_cast<const ExtendSessionResponse*>(
               &_ExtendSessionResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void UnsafeArenaSwap(ExtendSessionResponse* other);
  void Swap(ExtendSessionResponse* other);
  friend void swap(ExtendSessionResponse& a, ExtendSessionResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ExtendSessionResponse* New() const final {
    return CreateMaybeMessage<ExtendSessionResponse>(NULL);
  }

  ExtendSessionResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ExtendSessionResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ExtendSessionResponse& from);
  void MergeFrom(const ExtendSessionResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExtendSessionResponse* other);
  protected:
  explicit ExtendSessionResponse(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 new_graph_version = 4;
  void clear_new_graph_version();
  static const int kNewGraphVersionFieldNumber = 4;
  ::google::protobuf::int64 new_graph_version() const;
  void set_new_graph_version(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.ExtendSessionResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::int64 new_graph_version_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class RunStepRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunStepRequest) */ {
 public:
  RunStepRequest();
  virtual ~RunStepRequest();

  RunStepRequest(const RunStepRequest& from);

  inline RunStepRequest& operator=(const RunStepRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  RunStepRequest(RunStepRequest&& from) noexcept
    : RunStepRequest() {
    *this = ::std::move(from);
  }

  inline RunStepRequest& operator=(RunStepRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const RunStepRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RunStepRequest* internal_default_instance() {
    return reinterpret_cast<const RunStepRequest*>(
               &_RunStepRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void UnsafeArenaSwap(RunStepRequest* other);
  void Swap(RunStepRequest* other);
  friend void swap(RunStepRequest& a, RunStepRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RunStepRequest* New() const final {
    return CreateMaybeMessage<RunStepRequest>(NULL);
  }

  RunStepRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<RunStepRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const RunStepRequest& from);
  void MergeFrom(const RunStepRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunStepRequest* other);
  protected:
  explicit RunStepRequest(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.NamedTensorProto feed = 2;
  int feed_size() const;
  void clear_feed();
  static const int kFeedFieldNumber = 2;
  ::tensorflow::NamedTensorProto* mutable_feed(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::NamedTensorProto >*
      mutable_feed();
  const ::tensorflow::NamedTensorProto& feed(int index) const;
  ::tensorflow::NamedTensorProto* add_feed();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::NamedTensorProto >&
      feed() const;

  // repeated string fetch = 3;
  int fetch_size() const;
  void clear_fetch();
  static const int kFetchFieldNumber = 3;
  const ::std::string& fetch(int index) const;
  ::std::string* mutable_fetch(int index);
  void set_fetch(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_fetch(int index, ::std::string&& value);
  #endif
  void set_fetch(int index, const char* value);
  void set_fetch(int index, const char* value, size_t size);
  ::std::string* add_fetch();
  void add_fetch(const ::std::string& value);
  #if LANG_CXX11
  void add_fetch(::std::string&& value);
  #endif
  void add_fetch(const char* value);
  void add_fetch(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& fetch() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_fetch();

  // repeated string target = 4;
  int target_size() const;
  void clear_target();
  static const int kTargetFieldNumber = 4;
  const ::std::string& target(int index) const;
  ::std::string* mutable_target(int index);
  void set_target(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_target(int index, ::std::string&& value);
  #endif
  void set_target(int index, const char* value);
  void set_target(int index, const char* value, size_t size);
  ::std::string* add_target();
  void add_target(const ::std::string& value);
  #if LANG_CXX11
  void add_target(::std::string&& value);
  #endif
  void add_target(const char* value);
  void add_target(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& target() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_target();

  // string session_handle = 1;
  void clear_session_handle();
  static const int kSessionHandleFieldNumber = 1;
  const ::std::string& session_handle() const;
  void set_session_handle(const ::std::string& value);
  #if LANG_CXX11
  void set_session_handle(::std::string&& value);
  #endif
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  ::std::string* mutable_session_handle();
  ::std::string* release_session_handle();
  void set_allocated_session_handle(::std::string* session_handle);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_session_handle();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      ::std::string* session_handle);

  // string partial_run_handle = 6;
  void clear_partial_run_handle();
  static const int kPartialRunHandleFieldNumber = 6;
  const ::std::string& partial_run_handle() const;
  void set_partial_run_handle(const ::std::string& value);
  #if LANG_CXX11
  void set_partial_run_handle(::std::string&& value);
  #endif
  void set_partial_run_handle(const char* value);
  void set_partial_run_handle(const char* value, size_t size);
  ::std::string* mutable_partial_run_handle();
  ::std::string* release_partial_run_handle();
  void set_allocated_partial_run_handle(::std::string* partial_run_handle);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_partial_run_handle();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_partial_run_handle(
      ::std::string* partial_run_handle);

  // .tensorflow.RunOptions options = 5;
  bool has_options() const;
  void clear_options();
  static const int kOptionsFieldNumber = 5;
  private:
  const ::tensorflow::RunOptions& _internal_options() const;
  public:
  const ::tensorflow::RunOptions& options() const;
  ::tensorflow::RunOptions* release_options();
  ::tensorflow::RunOptions* mutable_options();
  void set_allocated_options(::tensorflow::RunOptions* options);
  void unsafe_arena_set_allocated_options(
      ::tensorflow::RunOptions* options);
  ::tensorflow::RunOptions* unsafe_arena_release_options();

  // int64 request_id = 8;
  void clear_request_id();
  static const int kRequestIdFieldNumber = 8;
  ::google::protobuf::int64 request_id() const;
  void set_request_id(::google::protobuf::int64 value);

  // bool store_errors_in_response_body = 7;
  void clear_store_errors_in_response_body();
  static const int kStoreErrorsInResponseBodyFieldNumber = 7;
  bool store_errors_in_response_body() const;
  void set_store_errors_in_response_body(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.RunStepRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::NamedTensorProto > feed_;
  ::google::protobuf::RepeatedPtrField< ::std::string> fetch_;
  ::google::protobuf::RepeatedPtrField< ::std::string> target_;
  ::google::protobuf::internal::ArenaStringPtr session_handle_;
  ::google::protobuf::internal::ArenaStringPtr partial_run_handle_;
  ::tensorflow::RunOptions* options_;
  ::google::protobuf::int64 request_id_;
  bool store_errors_in_response_body_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class RunStepResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunStepResponse) */ {
 public:
  RunStepResponse();
  virtual ~RunStepResponse();

  RunStepResponse(const RunStepResponse& from);

  inline RunStepResponse& operator=(const RunStepResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  RunStepResponse(RunStepResponse&& from) noexcept
    : RunStepResponse() {
    *this = ::std::move(from);
  }

  inline RunStepResponse& operator=(RunStepResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const RunStepResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RunStepResponse* internal_default_instance() {
    return reinterpret_cast<const RunStepResponse*>(
               &_RunStepResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  void UnsafeArenaSwap(RunStepResponse* other);
  void Swap(RunStepResponse* other);
  friend void swap(RunStepResponse& a, RunStepResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RunStepResponse* New() const final {
    return CreateMaybeMessage<RunStepResponse>(NULL);
  }

  RunStepResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<RunStepResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const RunStepResponse& from);
  void MergeFrom(const RunStepResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunStepResponse* other);
  protected:
  explicit RunStepResponse(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.NamedTensorProto tensor = 1;
  int tensor_size() const;
  void clear_tensor();
  static const int kTensorFieldNumber = 1;
  ::tensorflow::NamedTensorProto* mutable_tensor(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::NamedTensorProto >*
      mutable_tensor();
  const ::tensorflow::NamedTensorProto& tensor(int index) const;
  ::tensorflow::NamedTensorProto* add_tensor();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::NamedTensorProto >&
      tensor() const;

  // string status_error_message = 4;
  void clear_status_error_message();
  static const int kStatusErrorMessageFieldNumber = 4;
  const ::std::string& status_error_message() const;
  void set_status_error_message(const ::std::string& value);
  #if LANG_CXX11
  void set_status_error_message(::std::string&& value);
  #endif
  void set_status_error_message(const char* value);
  void set_status_error_message(const char* value, size_t size);
  ::std::string* mutable_status_error_message();
  ::std::string* release_status_error_message();
  void set_allocated_status_error_message(::std::string* status_error_message);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_status_error_message();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_status_error_message(
      ::std::string* status_error_message);

  // .tensorflow.RunMetadata metadata = 2;
  bool has_metadata() const;
  void clear_metadata();
  static const int kMetadataFieldNumber = 2;
  private:
  const ::tensorflow::RunMetadata& _internal_metadata() const;
  public:
  const ::tensorflow::RunMetadata& metadata() const;
  ::tensorflow::RunMetadata* release_metadata();
  ::tensorflow::RunMetadata* mutable_metadata();
  void set_allocated_metadata(::tensorflow::RunMetadata* metadata);
  void unsafe_arena_set_allocated_metadata(
      ::tensorflow::RunMetadata* metadata);
  ::tensorflow::RunMetadata* unsafe_arena_release_metadata();

  // .tensorflow.error.Code status_code = 3;
  void clear_status_code();
  static const int kStatusCodeFieldNumber = 3;
  ::tensorflow::error::Code status_code() const;
  void set_status_code(::tensorflow::error::Code value);

  // @@protoc_insertion_point(class_scope:tensorflow.RunStepResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::NamedTensorProto > tensor_;
  ::google::protobuf::internal::ArenaStringPtr status_error_message_;
  ::tensorflow::RunMetadata* metadata_;
  int status_code_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class PartialRunSetupRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.PartialRunSetupRequest) */ {
 public:
  PartialRunSetupRequest();
  virtual ~PartialRunSetupRequest();

  PartialRunSetupRequest(const PartialRunSetupRequest& from);

  inline PartialRunSetupRequest& operator=(const PartialRunSetupRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  PartialRunSetupRequest(PartialRunSetupRequest&& from) noexcept
    : PartialRunSetupRequest() {
    *this = ::std::move(from);
  }

  inline PartialRunSetupRequest& operator=(PartialRunSetupRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const PartialRunSetupRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const PartialRunSetupRequest* internal_default_instance() {
    return reinterpret_cast<const PartialRunSetupRequest*>(
               &_PartialRunSetupRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  void UnsafeArenaSwap(PartialRunSetupRequest* other);
  void Swap(PartialRunSetupRequest* other);
  friend void swap(PartialRunSetupRequest& a, PartialRunSetupRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline PartialRunSetupRequest* New() const final {
    return CreateMaybeMessage<PartialRunSetupRequest>(NULL);
  }

  PartialRunSetupRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<PartialRunSetupRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const PartialRunSetupRequest& from);
  void MergeFrom(const PartialRunSetupRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PartialRunSetupRequest* other);
  protected:
  explicit PartialRunSetupRequest(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string feed = 2;
  int feed_size() const;
  void clear_feed();
  static const int kFeedFieldNumber = 2;
  const ::std::string& feed(int index) const;
  ::std::string* mutable_feed(int index);
  void set_feed(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_feed(int index, ::std::string&& value);
  #endif
  void set_feed(int index, const char* value);
  void set_feed(int index, const char* value, size_t size);
  ::std::string* add_feed();
  void add_feed(const ::std::string& value);
  #if LANG_CXX11
  void add_feed(::std::string&& value);
  #endif
  void add_feed(const char* value);
  void add_feed(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& feed() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_feed();

  // repeated string fetch = 3;
  int fetch_size() const;
  void clear_fetch();
  static const int kFetchFieldNumber = 3;
  const ::std::string& fetch(int index) const;
  ::std::string* mutable_fetch(int index);
  void set_fetch(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_fetch(int index, ::std::string&& value);
  #endif
  void set_fetch(int index, const char* value);
  void set_fetch(int index, const char* value, size_t size);
  ::std::string* add_fetch();
  void add_fetch(const ::std::string& value);
  #if LANG_CXX11
  void add_fetch(::std::string&& value);
  #endif
  void add_fetch(const char* value);
  void add_fetch(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& fetch() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_fetch();

  // repeated string target = 4;
  int target_size() const;
  void clear_target();
  static const int kTargetFieldNumber = 4;
  const ::std::string& target(int index) const;
  ::std::string* mutable_target(int index);
  void set_target(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_target(int index, ::std::string&& value);
  #endif
  void set_target(int index, const char* value);
  void set_target(int index, const char* value, size_t size);
  ::std::string* add_target();
  void add_target(const ::std::string& value);
  #if LANG_CXX11
  void add_target(::std::string&& value);
  #endif
  void add_target(const char* value);
  void add_target(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& target() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_target();

  // string session_handle = 1;
  void clear_session_handle();
  static const int kSessionHandleFieldNumber = 1;
  const ::std::string& session_handle() const;
  void set_session_handle(const ::std::string& value);
  #if LANG_CXX11
  void set_session_handle(::std::string&& value);
  #endif
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  ::std::string* mutable_session_handle();
  ::std::string* release_session_handle();
  void set_allocated_session_handle(::std::string* session_handle);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_session_handle();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      ::std::string* session_handle);

  // int64 request_id = 5;
  void clear_request_id();
  static const int kRequestIdFieldNumber = 5;
  ::google::protobuf::int64 request_id() const;
  void set_request_id(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.PartialRunSetupRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::std::string> feed_;
  ::google::protobuf::RepeatedPtrField< ::std::string> fetch_;
  ::google::protobuf::RepeatedPtrField< ::std::string> target_;
  ::google::protobuf::internal::ArenaStringPtr session_handle_;
  ::google::protobuf::int64 request_id_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class PartialRunSetupResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.PartialRunSetupResponse) */ {
 public:
  PartialRunSetupResponse();
  virtual ~PartialRunSetupResponse();

  PartialRunSetupResponse(const PartialRunSetupResponse& from);

  inline PartialRunSetupResponse& operator=(const PartialRunSetupResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  PartialRunSetupResponse(PartialRunSetupResponse&& from) noexcept
    : PartialRunSetupResponse() {
    *this = ::std::move(from);
  }

  inline PartialRunSetupResponse& operator=(PartialRunSetupResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const PartialRunSetupResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const PartialRunSetupResponse* internal_default_instance() {
    return reinterpret_cast<const PartialRunSetupResponse*>(
               &_PartialRunSetupResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  void UnsafeArenaSwap(PartialRunSetupResponse* other);
  void Swap(PartialRunSetupResponse* other);
  friend void swap(PartialRunSetupResponse& a, PartialRunSetupResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline PartialRunSetupResponse* New() const final {
    return CreateMaybeMessage<PartialRunSetupResponse>(NULL);
  }

  PartialRunSetupResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<PartialRunSetupResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const PartialRunSetupResponse& from);
  void MergeFrom(const PartialRunSetupResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PartialRunSetupResponse* other);
  protected:
  explicit PartialRunSetupResponse(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string partial_run_handle = 1;
  void clear_partial_run_handle();
  static const int kPartialRunHandleFieldNumber = 1;
  const ::std::string& partial_run_handle() const;
  void set_partial_run_handle(const ::std::string& value);
  #if LANG_CXX11
  void set_partial_run_handle(::std::string&& value);
  #endif
  void set_partial_run_handle(const char* value);
  void set_partial_run_handle(const char* value, size_t size);
  ::std::string* mutable_partial_run_handle();
  ::std::string* release_partial_run_handle();
  void set_allocated_partial_run_handle(::std::string* partial_run_handle);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_partial_run_handle();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_partial_run_handle(
      ::std::string* partial_run_handle);

  // @@protoc_insertion_point(class_scope:tensorflow.PartialRunSetupResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr partial_run_handle_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CloseSessionRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.CloseSessionRequest) */ {
 public:
  CloseSessionRequest();
  virtual ~CloseSessionRequest();

  CloseSessionRequest(const CloseSessionRequest& from);

  inline CloseSessionRequest& operator=(const CloseSessionRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CloseSessionRequest(CloseSessionRequest&& from) noexcept
    : CloseSessionRequest() {
    *this = ::std::move(from);
  }

  inline CloseSessionRequest& operator=(CloseSessionRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const CloseSessionRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CloseSessionRequest* internal_default_instance() {
    return reinterpret_cast<const CloseSessionRequest*>(
               &_CloseSessionRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  void UnsafeArenaSwap(CloseSessionRequest* other);
  void Swap(CloseSessionRequest* other);
  friend void swap(CloseSessionRequest& a, CloseSessionRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CloseSessionRequest* New() const final {
    return CreateMaybeMessage<CloseSessionRequest>(NULL);
  }

  CloseSessionRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CloseSessionRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CloseSessionRequest& from);
  void MergeFrom(const CloseSessionRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CloseSessionRequest* other);
  protected:
  explicit CloseSessionRequest(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string session_handle = 1;
  void clear_session_handle();
  static const int kSessionHandleFieldNumber = 1;
  const ::std::string& session_handle() const;
  void set_session_handle(const ::std::string& value);
  #if LANG_CXX11
  void set_session_handle(::std::string&& value);
  #endif
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  ::std::string* mutable_session_handle();
  ::std::string* release_session_handle();
  void set_allocated_session_handle(::std::string* session_handle);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_session_handle();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      ::std::string* session_handle);

  // @@protoc_insertion_point(class_scope:tensorflow.CloseSessionRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr session_handle_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CloseSessionResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.CloseSessionResponse) */ {
 public:
  CloseSessionResponse();
  virtual ~CloseSessionResponse();

  CloseSessionResponse(const CloseSessionResponse& from);

  inline CloseSessionResponse& operator=(const CloseSessionResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CloseSessionResponse(CloseSessionResponse&& from) noexcept
    : CloseSessionResponse() {
    *this = ::std::move(from);
  }

  inline CloseSessionResponse& operator=(CloseSessionResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const CloseSessionResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CloseSessionResponse* internal_default_instance() {
    return reinterpret_cast<const CloseSessionResponse*>(
               &_CloseSessionResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  void UnsafeArenaSwap(CloseSessionResponse* other);
  void Swap(CloseSessionResponse* other);
  friend void swap(CloseSessionResponse& a, CloseSessionResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CloseSessionResponse* New() const final {
    return CreateMaybeMessage<CloseSessionResponse>(NULL);
  }

  CloseSessionResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CloseSessionResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CloseSessionResponse& from);
  void MergeFrom(const CloseSessionResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CloseSessionResponse* other);
  protected:
  explicit CloseSessionResponse(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.CloseSessionResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ResetRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.ResetRequest) */ {
 public:
  ResetRequest();
  virtual ~ResetRequest();

  ResetRequest(const ResetRequest& from);

  inline ResetRequest& operator=(const ResetRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ResetRequest(ResetRequest&& from) noexcept
    : ResetRequest() {
    *this = ::std::move(from);
  }

  inline ResetRequest& operator=(ResetRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const ResetRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ResetRequest* internal_default_instance() {
    return reinterpret_cast<const ResetRequest*>(
               &_ResetRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  void UnsafeArenaSwap(ResetRequest* other);
  void Swap(ResetRequest* other);
  friend void swap(ResetRequest& a, ResetRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ResetRequest* New() const final {
    return CreateMaybeMessage<ResetRequest>(NULL);
  }

  ResetRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ResetRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ResetRequest& from);
  void MergeFrom(const ResetRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ResetRequest* other);
  protected:
  explicit ResetRequest(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string container = 1;
  int container_size() const;
  void clear_container();
  static const int kContainerFieldNumber = 1;
  const ::std::string& container(int index) const;
  ::std::string* mutable_container(int index);
  void set_container(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_container(int index, ::std::string&& value);
  #endif
  void set_container(int index, const char* value);
  void set_container(int index, const char* value, size_t size);
  ::std::string* add_container();
  void add_container(const ::std::string& value);
  #if LANG_CXX11
  void add_container(::std::string&& value);
  #endif
  void add_container(const char* value);
  void add_container(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& container() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_container();

  // repeated string device_filters = 2;
  int device_filters_size() const;
  void clear_device_filters();
  static const int kDeviceFiltersFieldNumber = 2;
  const ::std::string& device_filters(int index) const;
  ::std::string* mutable_device_filters(int index);
  void set_device_filters(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_device_filters(int index, ::std::string&& value);
  #endif
  void set_device_filters(int index, const char* value);
  void set_device_filters(int index, const char* value, size_t size);
  ::std::string* add_device_filters();
  void add_device_filters(const ::std::string& value);
  #if LANG_CXX11
  void add_device_filters(::std::string&& value);
  #endif
  void add_device_filters(const char* value);
  void add_device_filters(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& device_filters() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_device_filters();

  // @@protoc_insertion_point(class_scope:tensorflow.ResetRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::std::string> container_;
  ::google::protobuf::RepeatedPtrField< ::std::string> device_filters_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ResetResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.ResetResponse) */ {
 public:
  ResetResponse();
  virtual ~ResetResponse();

  ResetResponse(const ResetResponse& from);

  inline ResetResponse& operator=(const ResetResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ResetResponse(ResetResponse&& from) noexcept
    : ResetResponse() {
    *this = ::std::move(from);
  }

  inline ResetResponse& operator=(ResetResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const ResetResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ResetResponse* internal_default_instance() {
    return reinterpret_cast<const ResetResponse*>(
               &_ResetResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  void UnsafeArenaSwap(ResetResponse* other);
  void Swap(ResetResponse* other);
  friend void swap(ResetResponse& a, ResetResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ResetResponse* New() const final {
    return CreateMaybeMessage<ResetResponse>(NULL);
  }

  ResetResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ResetResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ResetResponse& from);
  void MergeFrom(const ResetResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ResetResponse* other);
  protected:
  explicit ResetResponse(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.ResetResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ListDevicesRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.ListDevicesRequest) */ {
 public:
  ListDevicesRequest();
  virtual ~ListDevicesRequest();

  ListDevicesRequest(const ListDevicesRequest& from);

  inline ListDevicesRequest& operator=(const ListDevicesRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ListDevicesRequest(ListDevicesRequest&& from) noexcept
    : ListDevicesRequest() {
    *this = ::std::move(from);
  }

  inline ListDevicesRequest& operator=(ListDevicesRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const ListDevicesRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ListDevicesRequest* internal_default_instance() {
    return reinterpret_cast<const ListDevicesRequest*>(
               &_ListDevicesRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  void UnsafeArenaSwap(ListDevicesRequest* other);
  void Swap(ListDevicesRequest* other);
  friend void swap(ListDevicesRequest& a, ListDevicesRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ListDevicesRequest* New() const final {
    return CreateMaybeMessage<ListDevicesRequest>(NULL);
  }

  ListDevicesRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ListDevicesRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ListDevicesRequest& from);
  void MergeFrom(const ListDevicesRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ListDevicesRequest* other);
  protected:
  explicit ListDevicesRequest(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string session_handle = 1;
  void clear_session_handle();
  static const int kSessionHandleFieldNumber = 1;
  const ::std::string& session_handle() const;
  void set_session_handle(const ::std::string& value);
  #if LANG_CXX11
  void set_session_handle(::std::string&& value);
  #endif
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  ::std::string* mutable_session_handle();
  ::std::string* release_session_handle();
  void set_allocated_session_handle(::std::string* session_handle);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_session_handle();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      ::std::string* session_handle);

  // @@protoc_insertion_point(class_scope:tensorflow.ListDevicesRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr session_handle_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ListDevicesResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.ListDevicesResponse) */ {
 public:
  ListDevicesResponse();
  virtual ~ListDevicesResponse();

  ListDevicesResponse(const ListDevicesResponse& from);

  inline ListDevicesResponse& operator=(const ListDevicesResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ListDevicesResponse(ListDevicesResponse&& from) noexcept
    : ListDevicesResponse() {
    *this = ::std::move(from);
  }

  inline ListDevicesResponse& operator=(ListDevicesResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const ListDevicesResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ListDevicesResponse* internal_default_instance() {
    return reinterpret_cast<const ListDevicesResponse*>(
               &_ListDevicesResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  void UnsafeArenaSwap(ListDevicesResponse* other);
  void Swap(ListDevicesResponse* other);
  friend void swap(ListDevicesResponse& a, ListDevicesResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ListDevicesResponse* New() const final {
    return CreateMaybeMessage<ListDevicesResponse>(NULL);
  }

  ListDevicesResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ListDevicesResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ListDevicesResponse& from);
  void MergeFrom(const ListDevicesResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ListDevicesResponse* other);
  protected:
  explicit ListDevicesResponse(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.DeviceAttributes local_device = 1;
  int local_device_size() const;
  void clear_local_device();
  static const int kLocalDeviceFieldNumber = 1;
  ::tensorflow::DeviceAttributes* mutable_local_device(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
      mutable_local_device();
  const ::tensorflow::DeviceAttributes& local_device(int index) const;
  ::tensorflow::DeviceAttributes* add_local_device();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
      local_device() const;

  // repeated .tensorflow.DeviceAttributes remote_device = 2;
  int remote_device_size() const;
  void clear_remote_device();
  static const int kRemoteDeviceFieldNumber = 2;
  ::tensorflow::DeviceAttributes* mutable_remote_device(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
      mutable_remote_device();
  const ::tensorflow::DeviceAttributes& remote_device(int index) const;
  ::tensorflow::DeviceAttributes* add_remote_device();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
      remote_device() const;

  // @@protoc_insertion_point(class_scope:tensorflow.ListDevicesResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::DeviceAttributes > local_device_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::DeviceAttributes > remote_device_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class MakeCallableRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.MakeCallableRequest) */ {
 public:
  MakeCallableRequest();
  virtual ~MakeCallableRequest();

  MakeCallableRequest(const MakeCallableRequest& from);

  inline MakeCallableRequest& operator=(const MakeCallableRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  MakeCallableRequest(MakeCallableRequest&& from) noexcept
    : MakeCallableRequest() {
    *this = ::std::move(from);
  }

  inline MakeCallableRequest& operator=(MakeCallableRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const MakeCallableRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MakeCallableRequest* internal_default_instance() {
    return reinterpret_cast<const MakeCallableRequest*>(
               &_MakeCallableRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  void UnsafeArenaSwap(MakeCallableRequest* other);
  void Swap(MakeCallableRequest* other);
  friend void swap(MakeCallableRequest& a, MakeCallableRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline MakeCallableRequest* New() const final {
    return CreateMaybeMessage<MakeCallableRequest>(NULL);
  }

  MakeCallableRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<MakeCallableRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const MakeCallableRequest& from);
  void MergeFrom(const MakeCallableRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MakeCallableRequest* other);
  protected:
  explicit MakeCallableRequest(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string session_handle = 1;
  void clear_session_handle();
  static const int kSessionHandleFieldNumber = 1;
  const ::std::string& session_handle() const;
  void set_session_handle(const ::std::string& value);
  #if LANG_CXX11
  void set_session_handle(::std::string&& value);
  #endif
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  ::std::string* mutable_session_handle();
  ::std::string* release_session_handle();
  void set_allocated_session_handle(::std::string* session_handle);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_session_handle();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      ::std::string* session_handle);

  // .tensorflow.CallableOptions options = 2;
  bool has_options() const;
  void clear_options();
  static const int kOptionsFieldNumber = 2;
  private:
  const ::tensorflow::CallableOptions& _internal_options() const;
  public:
  const ::tensorflow::CallableOptions& options() const;
  ::tensorflow::CallableOptions* release_options();
  ::tensorflow::CallableOptions* mutable_options();
  void set_allocated_options(::tensorflow::CallableOptions* options);
  void unsafe_arena_set_allocated_options(
      ::tensorflow::CallableOptions* options);
  ::tensorflow::CallableOptions* unsafe_arena_release_options();

  // int64 request_id = 3;
  void clear_request_id();
  static const int kRequestIdFieldNumber = 3;
  ::google::protobuf::int64 request_id() const;
  void set_request_id(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.MakeCallableRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr session_handle_;
  ::tensorflow::CallableOptions* options_;
  ::google::protobuf::int64 request_id_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class MakeCallableResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.MakeCallableResponse) */ {
 public:
  MakeCallableResponse();
  virtual ~MakeCallableResponse();

  MakeCallableResponse(const MakeCallableResponse& from);

  inline MakeCallableResponse& operator=(const MakeCallableResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  MakeCallableResponse(MakeCallableResponse&& from) noexcept
    : MakeCallableResponse() {
    *this = ::std::move(from);
  }

  inline MakeCallableResponse& operator=(MakeCallableResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const MakeCallableResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MakeCallableResponse* internal_default_instance() {
    return reinterpret_cast<const MakeCallableResponse*>(
               &_MakeCallableResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  void UnsafeArenaSwap(MakeCallableResponse* other);
  void Swap(MakeCallableResponse* other);
  friend void swap(MakeCallableResponse& a, MakeCallableResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline MakeCallableResponse* New() const final {
    return CreateMaybeMessage<MakeCallableResponse>(NULL);
  }

  MakeCallableResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<MakeCallableResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const MakeCallableResponse& from);
  void MergeFrom(const MakeCallableResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MakeCallableResponse* other);
  protected:
  explicit MakeCallableResponse(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 handle = 1;
  void clear_handle();
  static const int kHandleFieldNumber = 1;
  ::google::protobuf::int64 handle() const;
  void set_handle(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.MakeCallableResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::int64 handle_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class RunCallableRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunCallableRequest) */ {
 public:
  RunCallableRequest();
  virtual ~RunCallableRequest();

  RunCallableRequest(const RunCallableRequest& from);

  inline RunCallableRequest& operator=(const RunCallableRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  RunCallableRequest(RunCallableRequest&& from) noexcept
    : RunCallableRequest() {
    *this = ::std::move(from);
  }

  inline RunCallableRequest& operator=(RunCallableRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const RunCallableRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RunCallableRequest* internal_default_instance() {
    return reinterpret_cast<const RunCallableRequest*>(
               &_RunCallableRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  void UnsafeArenaSwap(RunCallableRequest* other);
  void Swap(RunCallableRequest* other);
  friend void swap(RunCallableRequest& a, RunCallableRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RunCallableRequest* New() const final {
    return CreateMaybeMessage<RunCallableRequest>(NULL);
  }

  RunCallableRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<RunCallableRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const RunCallableRequest& from);
  void MergeFrom(const RunCallableRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunCallableRequest* other);
  protected:
  explicit RunCallableRequest(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.TensorProto feed = 3;
  int feed_size() const;
  void clear_feed();
  static const int kFeedFieldNumber = 3;
  ::tensorflow::TensorProto* mutable_feed(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorProto >*
      mutable_feed();
  const ::tensorflow::TensorProto& feed(int index) const;
  ::tensorflow::TensorProto* add_feed();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorProto >&
      feed() const;

  // string session_handle = 1;
  void clear_session_handle();
  static const int kSessionHandleFieldNumber = 1;
  const ::std::string& session_handle() const;
  void set_session_handle(const ::std::string& value);
  #if LANG_CXX11
  void set_session_handle(::std::string&& value);
  #endif
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  ::std::string* mutable_session_handle();
  ::std::string* release_session_handle();
  void set_allocated_session_handle(::std::string* session_handle);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_session_handle();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      ::std::string* session_handle);

  // int64 handle = 2;
  void clear_handle();
  static const int kHandleFieldNumber = 2;
  ::google::protobuf::int64 handle() const;
  void set_handle(::google::protobuf::int64 value);

  // int64 request_id = 4;
  void clear_request_id();
  static const int kRequestIdFieldNumber = 4;
  ::google::protobuf::int64 request_id() const;
  void set_request_id(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.RunCallableRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorProto > feed_;
  ::google::protobuf::internal::ArenaStringPtr session_handle_;
  ::google::protobuf::int64 handle_;
  ::google::protobuf::int64 request_id_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class RunCallableResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunCallableResponse) */ {
 public:
  RunCallableResponse();
  virtual ~RunCallableResponse();

  RunCallableResponse(const RunCallableResponse& from);

  inline RunCallableResponse& operator=(const RunCallableResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  RunCallableResponse(RunCallableResponse&& from) noexcept
    : RunCallableResponse() {
    *this = ::std::move(from);
  }

  inline RunCallableResponse& operator=(RunCallableResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const RunCallableResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RunCallableResponse* internal_default_instance() {
    return reinterpret_cast<const RunCallableResponse*>(
               &_RunCallableResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  void UnsafeArenaSwap(RunCallableResponse* other);
  void Swap(RunCallableResponse* other);
  friend void swap(RunCallableResponse& a, RunCallableResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RunCallableResponse* New() const final {
    return CreateMaybeMessage<RunCallableResponse>(NULL);
  }

  RunCallableResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<RunCallableResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const RunCallableResponse& from);
  void MergeFrom(const RunCallableResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunCallableResponse* other);
  protected:
  explicit RunCallableResponse(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.TensorProto fetch = 1;
  int fetch_size() const;
  void clear_fetch();
  static const int kFetchFieldNumber = 1;
  ::tensorflow::TensorProto* mutable_fetch(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorProto >*
      mutable_fetch();
  const ::tensorflow::TensorProto& fetch(int index) const;
  ::tensorflow::TensorProto* add_fetch();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorProto >&
      fetch() const;

  // .tensorflow.RunMetadata metadata = 2;
  bool has_metadata() const;
  void clear_metadata();
  static const int kMetadataFieldNumber = 2;
  private:
  const ::tensorflow::RunMetadata& _internal_metadata() const;
  public:
  const ::tensorflow::RunMetadata& metadata() const;
  ::tensorflow::RunMetadata* release_metadata();
  ::tensorflow::RunMetadata* mutable_metadata();
  void set_allocated_metadata(::tensorflow::RunMetadata* metadata);
  void unsafe_arena_set_allocated_metadata(
      ::tensorflow::RunMetadata* metadata);
  ::tensorflow::RunMetadata* unsafe_arena_release_metadata();

  // @@protoc_insertion_point(class_scope:tensorflow.RunCallableResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorProto > fetch_;
  ::tensorflow::RunMetadata* metadata_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ReleaseCallableRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.ReleaseCallableRequest) */ {
 public:
  ReleaseCallableRequest();
  virtual ~ReleaseCallableRequest();

  ReleaseCallableRequest(const ReleaseCallableRequest& from);

  inline ReleaseCallableRequest& operator=(const ReleaseCallableRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ReleaseCallableRequest(ReleaseCallableRequest&& from) noexcept
    : ReleaseCallableRequest() {
    *this = ::std::move(from);
  }

  inline ReleaseCallableRequest& operator=(ReleaseCallableRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const ReleaseCallableRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ReleaseCallableRequest* internal_default_instance() {
    return reinterpret_cast<const ReleaseCallableRequest*>(
               &_ReleaseCallableRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  void UnsafeArenaSwap(ReleaseCallableRequest* other);
  void Swap(ReleaseCallableRequest* other);
  friend void swap(ReleaseCallableRequest& a, ReleaseCallableRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ReleaseCallableRequest* New() const final {
    return CreateMaybeMessage<ReleaseCallableRequest>(NULL);
  }

  ReleaseCallableRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ReleaseCallableRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ReleaseCallableRequest& from);
  void MergeFrom(const ReleaseCallableRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ReleaseCallableRequest* other);
  protected:
  explicit ReleaseCallableRequest(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string session_handle = 1;
  void clear_session_handle();
  static const int kSessionHandleFieldNumber = 1;
  const ::std::string& session_handle() const;
  void set_session_handle(const ::std::string& value);
  #if LANG_CXX11
  void set_session_handle(::std::string&& value);
  #endif
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  ::std::string* mutable_session_handle();
  ::std::string* release_session_handle();
  void set_allocated_session_handle(::std::string* session_handle);
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  ::std::string* unsafe_arena_release_session_handle();
  PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      ::std::string* session_handle);

  // int64 handle = 2;
  void clear_handle();
  static const int kHandleFieldNumber = 2;
  ::google::protobuf::int64 handle() const;
  void set_handle(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.ReleaseCallableRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::internal::ArenaStringPtr session_handle_;
  ::google::protobuf::int64 handle_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ReleaseCallableResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.ReleaseCallableResponse) */ {
 public:
  ReleaseCallableResponse();
  virtual ~ReleaseCallableResponse();

  ReleaseCallableResponse(const ReleaseCallableResponse& from);

  inline ReleaseCallableResponse& operator=(const ReleaseCallableResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ReleaseCallableResponse(ReleaseCallableResponse&& from) noexcept
    : ReleaseCallableResponse() {
    *this = ::std::move(from);
  }

  inline ReleaseCallableResponse& operator=(ReleaseCallableResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const ReleaseCallableResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ReleaseCallableResponse* internal_default_instance() {
    return reinterpret_cast<const ReleaseCallableResponse*>(
               &_ReleaseCallableResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  void UnsafeArenaSwap(ReleaseCallableResponse* other);
  void Swap(ReleaseCallableResponse* other);
  friend void swap(ReleaseCallableResponse& a, ReleaseCallableResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ReleaseCallableResponse* New() const final {
    return CreateMaybeMessage<ReleaseCallableResponse>(NULL);
  }

  ReleaseCallableResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ReleaseCallableResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ReleaseCallableResponse& from);
  void MergeFrom(const ReleaseCallableResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ReleaseCallableResponse* other);
  protected:
  explicit ReleaseCallableResponse(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.ReleaseCallableResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CreateSessionRequest

// .tensorflow.GraphDef graph_def = 1;
inline bool CreateSessionRequest::has_graph_def() const {
  return this != internal_default_instance() && graph_def_ != NULL;
}
inline const ::tensorflow::GraphDef& CreateSessionRequest::_internal_graph_def() const {
  return *graph_def_;
}
inline const ::tensorflow::GraphDef& CreateSessionRequest::graph_def() const {
  const ::tensorflow::GraphDef* p = graph_def_;
  // @@protoc_insertion_point(field_get:tensorflow.CreateSessionRequest.graph_def)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::GraphDef*>(
      &::tensorflow::_GraphDef_default_instance_);
}
inline ::tensorflow::GraphDef* CreateSessionRequest::release_graph_def() {
  // @@protoc_insertion_point(field_release:tensorflow.CreateSessionRequest.graph_def)
  
  ::tensorflow::GraphDef* temp = graph_def_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  graph_def_ = NULL;
  return temp;
}
inline ::tensorflow::GraphDef* CreateSessionRequest::unsafe_arena_release_graph_def() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CreateSessionRequest.graph_def)
  
  ::tensorflow::GraphDef* temp = graph_def_;
  graph_def_ = NULL;
  return temp;
}
inline ::tensorflow::GraphDef* CreateSessionRequest::mutable_graph_def() {
  
  if (graph_def_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::GraphDef>(GetArenaNoVirtual());
    graph_def_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.CreateSessionRequest.graph_def)
  return graph_def_;
}
inline void CreateSessionRequest::set_allocated_graph_def(::tensorflow::GraphDef* graph_def) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(graph_def_);
  }
  if (graph_def) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(graph_def)->GetArena();
    if (message_arena != submessage_arena) {
      graph_def = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, graph_def, submessage_arena);
    }
    
  } else {
    
  }
  graph_def_ = graph_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CreateSessionRequest.graph_def)
}

// .tensorflow.ConfigProto config = 2;
inline bool CreateSessionRequest::has_config() const {
  return this != internal_default_instance() && config_ != NULL;
}
inline const ::tensorflow::ConfigProto& CreateSessionRequest::_internal_config() const {
  return *config_;
}
inline const ::tensorflow::ConfigProto& CreateSessionRequest::config() const {
  const ::tensorflow::ConfigProto* p = config_;
  // @@protoc_insertion_point(field_get:tensorflow.CreateSessionRequest.config)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::ConfigProto*>(
      &::tensorflow::_ConfigProto_default_instance_);
}
inline ::tensorflow::ConfigProto* CreateSessionRequest::release_config() {
  // @@protoc_insertion_point(field_release:tensorflow.CreateSessionRequest.config)
  
  ::tensorflow::ConfigProto* temp = config_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  config_ = NULL;
  return temp;
}
inline ::tensorflow::ConfigProto* CreateSessionRequest::unsafe_arena_release_config() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CreateSessionRequest.config)
  
  ::tensorflow::ConfigProto* temp = config_;
  config_ = NULL;
  return temp;
}
inline ::tensorflow::ConfigProto* CreateSessionRequest::mutable_config() {
  
  if (config_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::ConfigProto>(GetArenaNoVirtual());
    config_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.CreateSessionRequest.config)
  return config_;
}
inline void CreateSessionRequest::set_allocated_config(::tensorflow::ConfigProto* config) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(config_);
  }
  if (config) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(config)->GetArena();
    if (message_arena != submessage_arena) {
      config = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, config, submessage_arena);
    }
    
  } else {
    
  }
  config_ = config;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CreateSessionRequest.config)
}

// string target = 3;
inline void CreateSessionRequest::clear_target() {
  target_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& CreateSessionRequest::target() const {
  // @@protoc_insertion_point(field_get:tensorflow.CreateSessionRequest.target)
  return target_.Get();
}
inline void CreateSessionRequest::set_target(const ::std::string& value) {
  
  target_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CreateSessionRequest.target)
}
#if LANG_CXX11
inline void CreateSessionRequest::set_target(::std::string&& value) {
  
  target_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CreateSessionRequest.target)
}
#endif
inline void CreateSessionRequest::set_target(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  target_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CreateSessionRequest.target)
}
inline void CreateSessionRequest::set_target(const char* value,
    size_t size) {
  
  target_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CreateSessionRequest.target)
}
inline ::std::string* CreateSessionRequest::mutable_target() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CreateSessionRequest.target)
  return target_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* CreateSessionRequest::release_target() {
  // @@protoc_insertion_point(field_release:tensorflow.CreateSessionRequest.target)
  
  return target_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CreateSessionRequest::set_allocated_target(::std::string* target) {
  if (target != NULL) {
    
  } else {
    
  }
  target_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), target,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CreateSessionRequest.target)
}
inline ::std::string* CreateSessionRequest::unsafe_arena_release_target() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CreateSessionRequest.target)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return target_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CreateSessionRequest::unsafe_arena_set_allocated_target(
    ::std::string* target) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (target != NULL) {
    
  } else {
    
  }
  target_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      target, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CreateSessionRequest.target)
}

// -------------------------------------------------------------------

// CreateSessionResponse

// string session_handle = 1;
inline void CreateSessionResponse::clear_session_handle() {
  session_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& CreateSessionResponse::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.CreateSessionResponse.session_handle)
  return session_handle_.Get();
}
inline void CreateSessionResponse::set_session_handle(const ::std::string& value) {
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CreateSessionResponse.session_handle)
}
#if LANG_CXX11
inline void CreateSessionResponse::set_session_handle(::std::string&& value) {
  
  session_handle_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CreateSessionResponse.session_handle)
}
#endif
inline void CreateSessionResponse::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CreateSessionResponse.session_handle)
}
inline void CreateSessionResponse::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CreateSessionResponse.session_handle)
}
inline ::std::string* CreateSessionResponse::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CreateSessionResponse.session_handle)
  return session_handle_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* CreateSessionResponse::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.CreateSessionResponse.session_handle)
  
  return session_handle_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CreateSessionResponse::set_allocated_session_handle(::std::string* session_handle) {
  if (session_handle != NULL) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CreateSessionResponse.session_handle)
}
inline ::std::string* CreateSessionResponse::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CreateSessionResponse.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return session_handle_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CreateSessionResponse::unsafe_arena_set_allocated_session_handle(
    ::std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (session_handle != NULL) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CreateSessionResponse.session_handle)
}

// int64 graph_version = 2;
inline void CreateSessionResponse::clear_graph_version() {
  graph_version_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CreateSessionResponse::graph_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.CreateSessionResponse.graph_version)
  return graph_version_;
}
inline void CreateSessionResponse::set_graph_version(::google::protobuf::int64 value) {
  
  graph_version_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CreateSessionResponse.graph_version)
}

// -------------------------------------------------------------------

// ExtendSessionRequest

// string session_handle = 1;
inline void ExtendSessionRequest::clear_session_handle() {
  session_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& ExtendSessionRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.ExtendSessionRequest.session_handle)
  return session_handle_.Get();
}
inline void ExtendSessionRequest::set_session_handle(const ::std::string& value) {
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.ExtendSessionRequest.session_handle)
}
#if LANG_CXX11
inline void ExtendSessionRequest::set_session_handle(::std::string&& value) {
  
  session_handle_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ExtendSessionRequest.session_handle)
}
#endif
inline void ExtendSessionRequest::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.ExtendSessionRequest.session_handle)
}
inline void ExtendSessionRequest::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ExtendSessionRequest.session_handle)
}
inline ::std::string* ExtendSessionRequest::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ExtendSessionRequest.session_handle)
  return session_handle_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* ExtendSessionRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.ExtendSessionRequest.session_handle)
  
  return session_handle_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ExtendSessionRequest::set_allocated_session_handle(::std::string* session_handle) {
  if (session_handle != NULL) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ExtendSessionRequest.session_handle)
}
inline ::std::string* ExtendSessionRequest::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ExtendSessionRequest.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return session_handle_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ExtendSessionRequest::unsafe_arena_set_allocated_session_handle(
    ::std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (session_handle != NULL) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ExtendSessionRequest.session_handle)
}

// .tensorflow.GraphDef graph_def = 2;
inline bool ExtendSessionRequest::has_graph_def() const {
  return this != internal_default_instance() && graph_def_ != NULL;
}
inline const ::tensorflow::GraphDef& ExtendSessionRequest::_internal_graph_def() const {
  return *graph_def_;
}
inline const ::tensorflow::GraphDef& ExtendSessionRequest::graph_def() const {
  const ::tensorflow::GraphDef* p = graph_def_;
  // @@protoc_insertion_point(field_get:tensorflow.ExtendSessionRequest.graph_def)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::GraphDef*>(
      &::tensorflow::_GraphDef_default_instance_);
}
inline ::tensorflow::GraphDef* ExtendSessionRequest::release_graph_def() {
  // @@protoc_insertion_point(field_release:tensorflow.ExtendSessionRequest.graph_def)
  
  ::tensorflow::GraphDef* temp = graph_def_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  graph_def_ = NULL;
  return temp;
}
inline ::tensorflow::GraphDef* ExtendSessionRequest::unsafe_arena_release_graph_def() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ExtendSessionRequest.graph_def)
  
  ::tensorflow::GraphDef* temp = graph_def_;
  graph_def_ = NULL;
  return temp;
}
inline ::tensorflow::GraphDef* ExtendSessionRequest::mutable_graph_def() {
  
  if (graph_def_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::GraphDef>(GetArenaNoVirtual());
    graph_def_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ExtendSessionRequest.graph_def)
  return graph_def_;
}
inline void ExtendSessionRequest::set_allocated_graph_def(::tensorflow::GraphDef* graph_def) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(graph_def_);
  }
  if (graph_def) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(graph_def)->GetArena();
    if (message_arena != submessage_arena) {
      graph_def = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, graph_def, submessage_arena);
    }
    
  } else {
    
  }
  graph_def_ = graph_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ExtendSessionRequest.graph_def)
}

// int64 current_graph_version = 3;
inline void ExtendSessionRequest::clear_current_graph_version() {
  current_graph_version_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ExtendSessionRequest::current_graph_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.ExtendSessionRequest.current_graph_version)
  return current_graph_version_;
}
inline void ExtendSessionRequest::set_current_graph_version(::google::protobuf::int64 value) {
  
  current_graph_version_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ExtendSessionRequest.current_graph_version)
}

// -------------------------------------------------------------------

// ExtendSessionResponse

// int64 new_graph_version = 4;
inline void ExtendSessionResponse::clear_new_graph_version() {
  new_graph_version_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ExtendSessionResponse::new_graph_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.ExtendSessionResponse.new_graph_version)
  return new_graph_version_;
}
inline void ExtendSessionResponse::set_new_graph_version(::google::protobuf::int64 value) {
  
  new_graph_version_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ExtendSessionResponse.new_graph_version)
}

// -------------------------------------------------------------------

// RunStepRequest

// string session_handle = 1;
inline void RunStepRequest::clear_session_handle() {
  session_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& RunStepRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepRequest.session_handle)
  return session_handle_.Get();
}
inline void RunStepRequest::set_session_handle(const ::std::string& value) {
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RunStepRequest.session_handle)
}
#if LANG_CXX11
inline void RunStepRequest::set_session_handle(::std::string&& value) {
  
  session_handle_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RunStepRequest.session_handle)
}
#endif
inline void RunStepRequest::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RunStepRequest.session_handle)
}
inline void RunStepRequest::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RunStepRequest.session_handle)
}
inline ::std::string* RunStepRequest::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RunStepRequest.session_handle)
  return session_handle_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* RunStepRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.RunStepRequest.session_handle)
  
  return session_handle_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RunStepRequest::set_allocated_session_handle(::std::string* session_handle) {
  if (session_handle != NULL) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunStepRequest.session_handle)
}
inline ::std::string* RunStepRequest::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunStepRequest.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return session_handle_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RunStepRequest::unsafe_arena_set_allocated_session_handle(
    ::std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (session_handle != NULL) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunStepRequest.session_handle)
}

// repeated .tensorflow.NamedTensorProto feed = 2;
inline int RunStepRequest::feed_size() const {
  return feed_.size();
}
inline ::tensorflow::NamedTensorProto* RunStepRequest::mutable_feed(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunStepRequest.feed)
  return feed_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::NamedTensorProto >*
RunStepRequest::mutable_feed() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunStepRequest.feed)
  return &feed_;
}
inline const ::tensorflow::NamedTensorProto& RunStepRequest::feed(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepRequest.feed)
  return feed_.Get(index);
}
inline ::tensorflow::NamedTensorProto* RunStepRequest::add_feed() {
  // @@protoc_insertion_point(field_add:tensorflow.RunStepRequest.feed)
  return feed_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::NamedTensorProto >&
RunStepRequest::feed() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunStepRequest.feed)
  return feed_;
}

// repeated string fetch = 3;
inline int RunStepRequest::fetch_size() const {
  return fetch_.size();
}
inline void RunStepRequest::clear_fetch() {
  fetch_.Clear();
}
inline const ::std::string& RunStepRequest::fetch(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepRequest.fetch)
  return fetch_.Get(index);
}
inline ::std::string* RunStepRequest::mutable_fetch(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunStepRequest.fetch)
  return fetch_.Mutable(index);
}
inline void RunStepRequest::set_fetch(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.RunStepRequest.fetch)
  fetch_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void RunStepRequest::set_fetch(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.RunStepRequest.fetch)
  fetch_.Mutable(index)->assign(std::move(value));
}
#endif
inline void RunStepRequest::set_fetch(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  fetch_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.RunStepRequest.fetch)
}
inline void RunStepRequest::set_fetch(int index, const char* value, size_t size) {
  fetch_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RunStepRequest.fetch)
}
inline ::std::string* RunStepRequest::add_fetch() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.RunStepRequest.fetch)
  return fetch_.Add();
}
inline void RunStepRequest::add_fetch(const ::std::string& value) {
  fetch_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.RunStepRequest.fetch)
}
#if LANG_CXX11
inline void RunStepRequest::add_fetch(::std::string&& value) {
  fetch_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.RunStepRequest.fetch)
}
#endif
inline void RunStepRequest::add_fetch(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  fetch_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.RunStepRequest.fetch)
}
inline void RunStepRequest::add_fetch(const char* value, size_t size) {
  fetch_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.RunStepRequest.fetch)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
RunStepRequest::fetch() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunStepRequest.fetch)
  return fetch_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
RunStepRequest::mutable_fetch() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunStepRequest.fetch)
  return &fetch_;
}

// repeated string target = 4;
inline int RunStepRequest::target_size() const {
  return target_.size();
}
inline void RunStepRequest::clear_target() {
  target_.Clear();
}
inline const ::std::string& RunStepRequest::target(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepRequest.target)
  return target_.Get(index);
}
inline ::std::string* RunStepRequest::mutable_target(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunStepRequest.target)
  return target_.Mutable(index);
}
inline void RunStepRequest::set_target(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.RunStepRequest.target)
  target_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void RunStepRequest::set_target(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.RunStepRequest.target)
  target_.Mutable(index)->assign(std::move(value));
}
#endif
inline void RunStepRequest::set_target(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  target_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.RunStepRequest.target)
}
inline void RunStepRequest::set_target(int index, const char* value, size_t size) {
  target_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RunStepRequest.target)
}
inline ::std::string* RunStepRequest::add_target() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.RunStepRequest.target)
  return target_.Add();
}
inline void RunStepRequest::add_target(const ::std::string& value) {
  target_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.RunStepRequest.target)
}
#if LANG_CXX11
inline void RunStepRequest::add_target(::std::string&& value) {
  target_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.RunStepRequest.target)
}
#endif
inline void RunStepRequest::add_target(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  target_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.RunStepRequest.target)
}
inline void RunStepRequest::add_target(const char* value, size_t size) {
  target_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.RunStepRequest.target)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
RunStepRequest::target() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunStepRequest.target)
  return target_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
RunStepRequest::mutable_target() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunStepRequest.target)
  return &target_;
}

// .tensorflow.RunOptions options = 5;
inline bool RunStepRequest::has_options() const {
  return this != internal_default_instance() && options_ != NULL;
}
inline const ::tensorflow::RunOptions& RunStepRequest::_internal_options() const {
  return *options_;
}
inline const ::tensorflow::RunOptions& RunStepRequest::options() const {
  const ::tensorflow::RunOptions* p = options_;
  // @@protoc_insertion_point(field_get:tensorflow.RunStepRequest.options)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::RunOptions*>(
      &::tensorflow::_RunOptions_default_instance_);
}
inline ::tensorflow::RunOptions* RunStepRequest::release_options() {
  // @@protoc_insertion_point(field_release:tensorflow.RunStepRequest.options)
  
  ::tensorflow::RunOptions* temp = options_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  options_ = NULL;
  return temp;
}
inline ::tensorflow::RunOptions* RunStepRequest::unsafe_arena_release_options() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunStepRequest.options)
  
  ::tensorflow::RunOptions* temp = options_;
  options_ = NULL;
  return temp;
}
inline ::tensorflow::RunOptions* RunStepRequest::mutable_options() {
  
  if (options_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::RunOptions>(GetArenaNoVirtual());
    options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RunStepRequest.options)
  return options_;
}
inline void RunStepRequest::set_allocated_options(::tensorflow::RunOptions* options) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(options_);
  }
  if (options) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(options)->GetArena();
    if (message_arena != submessage_arena) {
      options = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, options, submessage_arena);
    }
    
  } else {
    
  }
  options_ = options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunStepRequest.options)
}

// string partial_run_handle = 6;
inline void RunStepRequest::clear_partial_run_handle() {
  partial_run_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& RunStepRequest::partial_run_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepRequest.partial_run_handle)
  return partial_run_handle_.Get();
}
inline void RunStepRequest::set_partial_run_handle(const ::std::string& value) {
  
  partial_run_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RunStepRequest.partial_run_handle)
}
#if LANG_CXX11
inline void RunStepRequest::set_partial_run_handle(::std::string&& value) {
  
  partial_run_handle_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RunStepRequest.partial_run_handle)
}
#endif
inline void RunStepRequest::set_partial_run_handle(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  partial_run_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RunStepRequest.partial_run_handle)
}
inline void RunStepRequest::set_partial_run_handle(const char* value,
    size_t size) {
  
  partial_run_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RunStepRequest.partial_run_handle)
}
inline ::std::string* RunStepRequest::mutable_partial_run_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RunStepRequest.partial_run_handle)
  return partial_run_handle_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* RunStepRequest::release_partial_run_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.RunStepRequest.partial_run_handle)
  
  return partial_run_handle_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RunStepRequest::set_allocated_partial_run_handle(::std::string* partial_run_handle) {
  if (partial_run_handle != NULL) {
    
  } else {
    
  }
  partial_run_handle_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), partial_run_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunStepRequest.partial_run_handle)
}
inline ::std::string* RunStepRequest::unsafe_arena_release_partial_run_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunStepRequest.partial_run_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return partial_run_handle_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RunStepRequest::unsafe_arena_set_allocated_partial_run_handle(
    ::std::string* partial_run_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (partial_run_handle != NULL) {
    
  } else {
    
  }
  partial_run_handle_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      partial_run_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunStepRequest.partial_run_handle)
}

// bool store_errors_in_response_body = 7;
inline void RunStepRequest::clear_store_errors_in_response_body() {
  store_errors_in_response_body_ = false;
}
inline bool RunStepRequest::store_errors_in_response_body() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepRequest.store_errors_in_response_body)
  return store_errors_in_response_body_;
}
inline void RunStepRequest::set_store_errors_in_response_body(bool value) {
  
  store_errors_in_response_body_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunStepRequest.store_errors_in_response_body)
}

// int64 request_id = 8;
inline void RunStepRequest::clear_request_id() {
  request_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 RunStepRequest::request_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepRequest.request_id)
  return request_id_;
}
inline void RunStepRequest::set_request_id(::google::protobuf::int64 value) {
  
  request_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunStepRequest.request_id)
}

// -------------------------------------------------------------------

// RunStepResponse

// repeated .tensorflow.NamedTensorProto tensor = 1;
inline int RunStepResponse::tensor_size() const {
  return tensor_.size();
}
inline ::tensorflow::NamedTensorProto* RunStepResponse::mutable_tensor(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunStepResponse.tensor)
  return tensor_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::NamedTensorProto >*
RunStepResponse::mutable_tensor() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunStepResponse.tensor)
  return &tensor_;
}
inline const ::tensorflow::NamedTensorProto& RunStepResponse::tensor(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepResponse.tensor)
  return tensor_.Get(index);
}
inline ::tensorflow::NamedTensorProto* RunStepResponse::add_tensor() {
  // @@protoc_insertion_point(field_add:tensorflow.RunStepResponse.tensor)
  return tensor_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::NamedTensorProto >&
RunStepResponse::tensor() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunStepResponse.tensor)
  return tensor_;
}

// .tensorflow.RunMetadata metadata = 2;
inline bool RunStepResponse::has_metadata() const {
  return this != internal_default_instance() && metadata_ != NULL;
}
inline const ::tensorflow::RunMetadata& RunStepResponse::_internal_metadata() const {
  return *metadata_;
}
inline const ::tensorflow::RunMetadata& RunStepResponse::metadata() const {
  const ::tensorflow::RunMetadata* p = metadata_;
  // @@protoc_insertion_point(field_get:tensorflow.RunStepResponse.metadata)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::RunMetadata*>(
      &::tensorflow::_RunMetadata_default_instance_);
}
inline ::tensorflow::RunMetadata* RunStepResponse::release_metadata() {
  // @@protoc_insertion_point(field_release:tensorflow.RunStepResponse.metadata)
  
  ::tensorflow::RunMetadata* temp = metadata_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  metadata_ = NULL;
  return temp;
}
inline ::tensorflow::RunMetadata* RunStepResponse::unsafe_arena_release_metadata() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunStepResponse.metadata)
  
  ::tensorflow::RunMetadata* temp = metadata_;
  metadata_ = NULL;
  return temp;
}
inline ::tensorflow::RunMetadata* RunStepResponse::mutable_metadata() {
  
  if (metadata_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::RunMetadata>(GetArenaNoVirtual());
    metadata_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RunStepResponse.metadata)
  return metadata_;
}
inline void RunStepResponse::set_allocated_metadata(::tensorflow::RunMetadata* metadata) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(metadata_);
  }
  if (metadata) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(metadata)->GetArena();
    if (message_arena != submessage_arena) {
      metadata = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, metadata, submessage_arena);
    }
    
  } else {
    
  }
  metadata_ = metadata;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunStepResponse.metadata)
}

// .tensorflow.error.Code status_code = 3;
inline void RunStepResponse::clear_status_code() {
  status_code_ = 0;
}
inline ::tensorflow::error::Code RunStepResponse::status_code() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepResponse.status_code)
  return static_cast< ::tensorflow::error::Code >(status_code_);
}
inline void RunStepResponse::set_status_code(::tensorflow::error::Code value) {
  
  status_code_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunStepResponse.status_code)
}

// string status_error_message = 4;
inline void RunStepResponse::clear_status_error_message() {
  status_error_message_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& RunStepResponse::status_error_message() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepResponse.status_error_message)
  return status_error_message_.Get();
}
inline void RunStepResponse::set_status_error_message(const ::std::string& value) {
  
  status_error_message_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RunStepResponse.status_error_message)
}
#if LANG_CXX11
inline void RunStepResponse::set_status_error_message(::std::string&& value) {
  
  status_error_message_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RunStepResponse.status_error_message)
}
#endif
inline void RunStepResponse::set_status_error_message(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  status_error_message_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RunStepResponse.status_error_message)
}
inline void RunStepResponse::set_status_error_message(const char* value,
    size_t size) {
  
  status_error_message_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RunStepResponse.status_error_message)
}
inline ::std::string* RunStepResponse::mutable_status_error_message() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RunStepResponse.status_error_message)
  return status_error_message_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* RunStepResponse::release_status_error_message() {
  // @@protoc_insertion_point(field_release:tensorflow.RunStepResponse.status_error_message)
  
  return status_error_message_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RunStepResponse::set_allocated_status_error_message(::std::string* status_error_message) {
  if (status_error_message != NULL) {
    
  } else {
    
  }
  status_error_message_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), status_error_message,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunStepResponse.status_error_message)
}
inline ::std::string* RunStepResponse::unsafe_arena_release_status_error_message() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunStepResponse.status_error_message)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return status_error_message_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RunStepResponse::unsafe_arena_set_allocated_status_error_message(
    ::std::string* status_error_message) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (status_error_message != NULL) {
    
  } else {
    
  }
  status_error_message_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      status_error_message, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunStepResponse.status_error_message)
}

// -------------------------------------------------------------------

// PartialRunSetupRequest

// string session_handle = 1;
inline void PartialRunSetupRequest::clear_session_handle() {
  session_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& PartialRunSetupRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.PartialRunSetupRequest.session_handle)
  return session_handle_.Get();
}
inline void PartialRunSetupRequest::set_session_handle(const ::std::string& value) {
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.PartialRunSetupRequest.session_handle)
}
#if LANG_CXX11
inline void PartialRunSetupRequest::set_session_handle(::std::string&& value) {
  
  session_handle_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.PartialRunSetupRequest.session_handle)
}
#endif
inline void PartialRunSetupRequest::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.PartialRunSetupRequest.session_handle)
}
inline void PartialRunSetupRequest::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.PartialRunSetupRequest.session_handle)
}
inline ::std::string* PartialRunSetupRequest::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.PartialRunSetupRequest.session_handle)
  return session_handle_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* PartialRunSetupRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.PartialRunSetupRequest.session_handle)
  
  return session_handle_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void PartialRunSetupRequest::set_allocated_session_handle(::std::string* session_handle) {
  if (session_handle != NULL) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PartialRunSetupRequest.session_handle)
}
inline ::std::string* PartialRunSetupRequest::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.PartialRunSetupRequest.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return session_handle_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void PartialRunSetupRequest::unsafe_arena_set_allocated_session_handle(
    ::std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (session_handle != NULL) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.PartialRunSetupRequest.session_handle)
}

// repeated string feed = 2;
inline int PartialRunSetupRequest::feed_size() const {
  return feed_.size();
}
inline void PartialRunSetupRequest::clear_feed() {
  feed_.Clear();
}
inline const ::std::string& PartialRunSetupRequest::feed(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.PartialRunSetupRequest.feed)
  return feed_.Get(index);
}
inline ::std::string* PartialRunSetupRequest::mutable_feed(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.PartialRunSetupRequest.feed)
  return feed_.Mutable(index);
}
inline void PartialRunSetupRequest::set_feed(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.PartialRunSetupRequest.feed)
  feed_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void PartialRunSetupRequest::set_feed(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.PartialRunSetupRequest.feed)
  feed_.Mutable(index)->assign(std::move(value));
}
#endif
inline void PartialRunSetupRequest::set_feed(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  feed_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.PartialRunSetupRequest.feed)
}
inline void PartialRunSetupRequest::set_feed(int index, const char* value, size_t size) {
  feed_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.PartialRunSetupRequest.feed)
}
inline ::std::string* PartialRunSetupRequest::add_feed() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.PartialRunSetupRequest.feed)
  return feed_.Add();
}
inline void PartialRunSetupRequest::add_feed(const ::std::string& value) {
  feed_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.PartialRunSetupRequest.feed)
}
#if LANG_CXX11
inline void PartialRunSetupRequest::add_feed(::std::string&& value) {
  feed_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.PartialRunSetupRequest.feed)
}
#endif
inline void PartialRunSetupRequest::add_feed(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  feed_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.PartialRunSetupRequest.feed)
}
inline void PartialRunSetupRequest::add_feed(const char* value, size_t size) {
  feed_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.PartialRunSetupRequest.feed)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
PartialRunSetupRequest::feed() const {
  // @@protoc_insertion_point(field_list:tensorflow.PartialRunSetupRequest.feed)
  return feed_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
PartialRunSetupRequest::mutable_feed() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.PartialRunSetupRequest.feed)
  return &feed_;
}

// repeated string fetch = 3;
inline int PartialRunSetupRequest::fetch_size() const {
  return fetch_.size();
}
inline void PartialRunSetupRequest::clear_fetch() {
  fetch_.Clear();
}
inline const ::std::string& PartialRunSetupRequest::fetch(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.PartialRunSetupRequest.fetch)
  return fetch_.Get(index);
}
inline ::std::string* PartialRunSetupRequest::mutable_fetch(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.PartialRunSetupRequest.fetch)
  return fetch_.Mutable(index);
}
inline void PartialRunSetupRequest::set_fetch(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.PartialRunSetupRequest.fetch)
  fetch_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void PartialRunSetupRequest::set_fetch(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.PartialRunSetupRequest.fetch)
  fetch_.Mutable(index)->assign(std::move(value));
}
#endif
inline void PartialRunSetupRequest::set_fetch(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  fetch_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.PartialRunSetupRequest.fetch)
}
inline void PartialRunSetupRequest::set_fetch(int index, const char* value, size_t size) {
  fetch_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.PartialRunSetupRequest.fetch)
}
inline ::std::string* PartialRunSetupRequest::add_fetch() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.PartialRunSetupRequest.fetch)
  return fetch_.Add();
}
inline void PartialRunSetupRequest::add_fetch(const ::std::string& value) {
  fetch_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.PartialRunSetupRequest.fetch)
}
#if LANG_CXX11
inline void PartialRunSetupRequest::add_fetch(::std::string&& value) {
  fetch_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.PartialRunSetupRequest.fetch)
}
#endif
inline void PartialRunSetupRequest::add_fetch(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  fetch_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.PartialRunSetupRequest.fetch)
}
inline void PartialRunSetupRequest::add_fetch(const char* value, size_t size) {
  fetch_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.PartialRunSetupRequest.fetch)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
PartialRunSetupRequest::fetch() const {
  // @@protoc_insertion_point(field_list:tensorflow.PartialRunSetupRequest.fetch)
  return fetch_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
PartialRunSetupRequest::mutable_fetch() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.PartialRunSetupRequest.fetch)
  return &fetch_;
}

// repeated string target = 4;
inline int PartialRunSetupRequest::target_size() const {
  return target_.size();
}
inline void PartialRunSetupRequest::clear_target() {
  target_.Clear();
}
inline const ::std::string& PartialRunSetupRequest::target(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.PartialRunSetupRequest.target)
  return target_.Get(index);
}
inline ::std::string* PartialRunSetupRequest::mutable_target(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.PartialRunSetupRequest.target)
  return target_.Mutable(index);
}
inline void PartialRunSetupRequest::set_target(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.PartialRunSetupRequest.target)
  target_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void PartialRunSetupRequest::set_target(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.PartialRunSetupRequest.target)
  target_.Mutable(index)->assign(std::move(value));
}
#endif
inline void PartialRunSetupRequest::set_target(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  target_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.PartialRunSetupRequest.target)
}
inline void PartialRunSetupRequest::set_target(int index, const char* value, size_t size) {
  target_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.PartialRunSetupRequest.target)
}
inline ::std::string* PartialRunSetupRequest::add_target() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.PartialRunSetupRequest.target)
  return target_.Add();
}
inline void PartialRunSetupRequest::add_target(const ::std::string& value) {
  target_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.PartialRunSetupRequest.target)
}
#if LANG_CXX11
inline void PartialRunSetupRequest::add_target(::std::string&& value) {
  target_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.PartialRunSetupRequest.target)
}
#endif
inline void PartialRunSetupRequest::add_target(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  target_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.PartialRunSetupRequest.target)
}
inline void PartialRunSetupRequest::add_target(const char* value, size_t size) {
  target_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.PartialRunSetupRequest.target)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
PartialRunSetupRequest::target() const {
  // @@protoc_insertion_point(field_list:tensorflow.PartialRunSetupRequest.target)
  return target_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
PartialRunSetupRequest::mutable_target() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.PartialRunSetupRequest.target)
  return &target_;
}

// int64 request_id = 5;
inline void PartialRunSetupRequest::clear_request_id() {
  request_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 PartialRunSetupRequest::request_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.PartialRunSetupRequest.request_id)
  return request_id_;
}
inline void PartialRunSetupRequest::set_request_id(::google::protobuf::int64 value) {
  
  request_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.PartialRunSetupRequest.request_id)
}

// -------------------------------------------------------------------

// PartialRunSetupResponse

// string partial_run_handle = 1;
inline void PartialRunSetupResponse::clear_partial_run_handle() {
  partial_run_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& PartialRunSetupResponse::partial_run_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.PartialRunSetupResponse.partial_run_handle)
  return partial_run_handle_.Get();
}
inline void PartialRunSetupResponse::set_partial_run_handle(const ::std::string& value) {
  
  partial_run_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.PartialRunSetupResponse.partial_run_handle)
}
#if LANG_CXX11
inline void PartialRunSetupResponse::set_partial_run_handle(::std::string&& value) {
  
  partial_run_handle_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.PartialRunSetupResponse.partial_run_handle)
}
#endif
inline void PartialRunSetupResponse::set_partial_run_handle(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  partial_run_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.PartialRunSetupResponse.partial_run_handle)
}
inline void PartialRunSetupResponse::set_partial_run_handle(const char* value,
    size_t size) {
  
  partial_run_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.PartialRunSetupResponse.partial_run_handle)
}
inline ::std::string* PartialRunSetupResponse::mutable_partial_run_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.PartialRunSetupResponse.partial_run_handle)
  return partial_run_handle_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* PartialRunSetupResponse::release_partial_run_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.PartialRunSetupResponse.partial_run_handle)
  
  return partial_run_handle_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void PartialRunSetupResponse::set_allocated_partial_run_handle(::std::string* partial_run_handle) {
  if (partial_run_handle != NULL) {
    
  } else {
    
  }
  partial_run_handle_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), partial_run_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PartialRunSetupResponse.partial_run_handle)
}
inline ::std::string* PartialRunSetupResponse::unsafe_arena_release_partial_run_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.PartialRunSetupResponse.partial_run_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return partial_run_handle_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void PartialRunSetupResponse::unsafe_arena_set_allocated_partial_run_handle(
    ::std::string* partial_run_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (partial_run_handle != NULL) {
    
  } else {
    
  }
  partial_run_handle_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      partial_run_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.PartialRunSetupResponse.partial_run_handle)
}

// -------------------------------------------------------------------

// CloseSessionRequest

// string session_handle = 1;
inline void CloseSessionRequest::clear_session_handle() {
  session_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& CloseSessionRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.CloseSessionRequest.session_handle)
  return session_handle_.Get();
}
inline void CloseSessionRequest::set_session_handle(const ::std::string& value) {
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CloseSessionRequest.session_handle)
}
#if LANG_CXX11
inline void CloseSessionRequest::set_session_handle(::std::string&& value) {
  
  session_handle_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CloseSessionRequest.session_handle)
}
#endif
inline void CloseSessionRequest::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CloseSessionRequest.session_handle)
}
inline void CloseSessionRequest::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CloseSessionRequest.session_handle)
}
inline ::std::string* CloseSessionRequest::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CloseSessionRequest.session_handle)
  return session_handle_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* CloseSessionRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.CloseSessionRequest.session_handle)
  
  return session_handle_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CloseSessionRequest::set_allocated_session_handle(::std::string* session_handle) {
  if (session_handle != NULL) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CloseSessionRequest.session_handle)
}
inline ::std::string* CloseSessionRequest::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CloseSessionRequest.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return session_handle_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CloseSessionRequest::unsafe_arena_set_allocated_session_handle(
    ::std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (session_handle != NULL) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CloseSessionRequest.session_handle)
}

// -------------------------------------------------------------------

// CloseSessionResponse

// -------------------------------------------------------------------

// ResetRequest

// repeated string container = 1;
inline int ResetRequest::container_size() const {
  return container_.size();
}
inline void ResetRequest::clear_container() {
  container_.Clear();
}
inline const ::std::string& ResetRequest::container(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ResetRequest.container)
  return container_.Get(index);
}
inline ::std::string* ResetRequest::mutable_container(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ResetRequest.container)
  return container_.Mutable(index);
}
inline void ResetRequest::set_container(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.ResetRequest.container)
  container_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void ResetRequest::set_container(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.ResetRequest.container)
  container_.Mutable(index)->assign(std::move(value));
}
#endif
inline void ResetRequest::set_container(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  container_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.ResetRequest.container)
}
inline void ResetRequest::set_container(int index, const char* value, size_t size) {
  container_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ResetRequest.container)
}
inline ::std::string* ResetRequest::add_container() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.ResetRequest.container)
  return container_.Add();
}
inline void ResetRequest::add_container(const ::std::string& value) {
  container_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.ResetRequest.container)
}
#if LANG_CXX11
inline void ResetRequest::add_container(::std::string&& value) {
  container_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.ResetRequest.container)
}
#endif
inline void ResetRequest::add_container(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  container_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.ResetRequest.container)
}
inline void ResetRequest::add_container(const char* value, size_t size) {
  container_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.ResetRequest.container)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
ResetRequest::container() const {
  // @@protoc_insertion_point(field_list:tensorflow.ResetRequest.container)
  return container_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
ResetRequest::mutable_container() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ResetRequest.container)
  return &container_;
}

// repeated string device_filters = 2;
inline int ResetRequest::device_filters_size() const {
  return device_filters_.size();
}
inline void ResetRequest::clear_device_filters() {
  device_filters_.Clear();
}
inline const ::std::string& ResetRequest::device_filters(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ResetRequest.device_filters)
  return device_filters_.Get(index);
}
inline ::std::string* ResetRequest::mutable_device_filters(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ResetRequest.device_filters)
  return device_filters_.Mutable(index);
}
inline void ResetRequest::set_device_filters(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.ResetRequest.device_filters)
  device_filters_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void ResetRequest::set_device_filters(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.ResetRequest.device_filters)
  device_filters_.Mutable(index)->assign(std::move(value));
}
#endif
inline void ResetRequest::set_device_filters(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  device_filters_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.ResetRequest.device_filters)
}
inline void ResetRequest::set_device_filters(int index, const char* value, size_t size) {
  device_filters_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ResetRequest.device_filters)
}
inline ::std::string* ResetRequest::add_device_filters() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.ResetRequest.device_filters)
  return device_filters_.Add();
}
inline void ResetRequest::add_device_filters(const ::std::string& value) {
  device_filters_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.ResetRequest.device_filters)
}
#if LANG_CXX11
inline void ResetRequest::add_device_filters(::std::string&& value) {
  device_filters_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.ResetRequest.device_filters)
}
#endif
inline void ResetRequest::add_device_filters(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  device_filters_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.ResetRequest.device_filters)
}
inline void ResetRequest::add_device_filters(const char* value, size_t size) {
  device_filters_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.ResetRequest.device_filters)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
ResetRequest::device_filters() const {
  // @@protoc_insertion_point(field_list:tensorflow.ResetRequest.device_filters)
  return device_filters_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
ResetRequest::mutable_device_filters() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ResetRequest.device_filters)
  return &device_filters_;
}

// -------------------------------------------------------------------

// ResetResponse

// -------------------------------------------------------------------

// ListDevicesRequest

// string session_handle = 1;
inline void ListDevicesRequest::clear_session_handle() {
  session_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& ListDevicesRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.ListDevicesRequest.session_handle)
  return session_handle_.Get();
}
inline void ListDevicesRequest::set_session_handle(const ::std::string& value) {
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.ListDevicesRequest.session_handle)
}
#if LANG_CXX11
inline void ListDevicesRequest::set_session_handle(::std::string&& value) {
  
  session_handle_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ListDevicesRequest.session_handle)
}
#endif
inline void ListDevicesRequest::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.ListDevicesRequest.session_handle)
}
inline void ListDevicesRequest::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ListDevicesRequest.session_handle)
}
inline ::std::string* ListDevicesRequest::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ListDevicesRequest.session_handle)
  return session_handle_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* ListDevicesRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.ListDevicesRequest.session_handle)
  
  return session_handle_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ListDevicesRequest::set_allocated_session_handle(::std::string* session_handle) {
  if (session_handle != NULL) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ListDevicesRequest.session_handle)
}
inline ::std::string* ListDevicesRequest::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ListDevicesRequest.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return session_handle_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ListDevicesRequest::unsafe_arena_set_allocated_session_handle(
    ::std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (session_handle != NULL) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ListDevicesRequest.session_handle)
}

// -------------------------------------------------------------------

// ListDevicesResponse

// repeated .tensorflow.DeviceAttributes local_device = 1;
inline int ListDevicesResponse::local_device_size() const {
  return local_device_.size();
}
inline ::tensorflow::DeviceAttributes* ListDevicesResponse::mutable_local_device(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ListDevicesResponse.local_device)
  return local_device_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
ListDevicesResponse::mutable_local_device() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ListDevicesResponse.local_device)
  return &local_device_;
}
inline const ::tensorflow::DeviceAttributes& ListDevicesResponse::local_device(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ListDevicesResponse.local_device)
  return local_device_.Get(index);
}
inline ::tensorflow::DeviceAttributes* ListDevicesResponse::add_local_device() {
  // @@protoc_insertion_point(field_add:tensorflow.ListDevicesResponse.local_device)
  return local_device_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
ListDevicesResponse::local_device() const {
  // @@protoc_insertion_point(field_list:tensorflow.ListDevicesResponse.local_device)
  return local_device_;
}

// repeated .tensorflow.DeviceAttributes remote_device = 2;
inline int ListDevicesResponse::remote_device_size() const {
  return remote_device_.size();
}
inline ::tensorflow::DeviceAttributes* ListDevicesResponse::mutable_remote_device(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ListDevicesResponse.remote_device)
  return remote_device_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
ListDevicesResponse::mutable_remote_device() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ListDevicesResponse.remote_device)
  return &remote_device_;
}
inline const ::tensorflow::DeviceAttributes& ListDevicesResponse::remote_device(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ListDevicesResponse.remote_device)
  return remote_device_.Get(index);
}
inline ::tensorflow::DeviceAttributes* ListDevicesResponse::add_remote_device() {
  // @@protoc_insertion_point(field_add:tensorflow.ListDevicesResponse.remote_device)
  return remote_device_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
ListDevicesResponse::remote_device() const {
  // @@protoc_insertion_point(field_list:tensorflow.ListDevicesResponse.remote_device)
  return remote_device_;
}

// -------------------------------------------------------------------

// MakeCallableRequest

// string session_handle = 1;
inline void MakeCallableRequest::clear_session_handle() {
  session_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& MakeCallableRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.MakeCallableRequest.session_handle)
  return session_handle_.Get();
}
inline void MakeCallableRequest::set_session_handle(const ::std::string& value) {
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.MakeCallableRequest.session_handle)
}
#if LANG_CXX11
inline void MakeCallableRequest::set_session_handle(::std::string&& value) {
  
  session_handle_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.MakeCallableRequest.session_handle)
}
#endif
inline void MakeCallableRequest::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.MakeCallableRequest.session_handle)
}
inline void MakeCallableRequest::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.MakeCallableRequest.session_handle)
}
inline ::std::string* MakeCallableRequest::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.MakeCallableRequest.session_handle)
  return session_handle_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* MakeCallableRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.MakeCallableRequest.session_handle)
  
  return session_handle_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void MakeCallableRequest::set_allocated_session_handle(::std::string* session_handle) {
  if (session_handle != NULL) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MakeCallableRequest.session_handle)
}
inline ::std::string* MakeCallableRequest::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MakeCallableRequest.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return session_handle_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void MakeCallableRequest::unsafe_arena_set_allocated_session_handle(
    ::std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (session_handle != NULL) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MakeCallableRequest.session_handle)
}

// .tensorflow.CallableOptions options = 2;
inline bool MakeCallableRequest::has_options() const {
  return this != internal_default_instance() && options_ != NULL;
}
inline const ::tensorflow::CallableOptions& MakeCallableRequest::_internal_options() const {
  return *options_;
}
inline const ::tensorflow::CallableOptions& MakeCallableRequest::options() const {
  const ::tensorflow::CallableOptions* p = options_;
  // @@protoc_insertion_point(field_get:tensorflow.MakeCallableRequest.options)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::CallableOptions*>(
      &::tensorflow::_CallableOptions_default_instance_);
}
inline ::tensorflow::CallableOptions* MakeCallableRequest::release_options() {
  // @@protoc_insertion_point(field_release:tensorflow.MakeCallableRequest.options)
  
  ::tensorflow::CallableOptions* temp = options_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  options_ = NULL;
  return temp;
}
inline ::tensorflow::CallableOptions* MakeCallableRequest::unsafe_arena_release_options() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.MakeCallableRequest.options)
  
  ::tensorflow::CallableOptions* temp = options_;
  options_ = NULL;
  return temp;
}
inline ::tensorflow::CallableOptions* MakeCallableRequest::mutable_options() {
  
  if (options_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::CallableOptions>(GetArenaNoVirtual());
    options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.MakeCallableRequest.options)
  return options_;
}
inline void MakeCallableRequest::set_allocated_options(::tensorflow::CallableOptions* options) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(options_);
  }
  if (options) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(options)->GetArena();
    if (message_arena != submessage_arena) {
      options = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, options, submessage_arena);
    }
    
  } else {
    
  }
  options_ = options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MakeCallableRequest.options)
}

// int64 request_id = 3;
inline void MakeCallableRequest::clear_request_id() {
  request_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MakeCallableRequest::request_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.MakeCallableRequest.request_id)
  return request_id_;
}
inline void MakeCallableRequest::set_request_id(::google::protobuf::int64 value) {
  
  request_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MakeCallableRequest.request_id)
}

// -------------------------------------------------------------------

// MakeCallableResponse

// int64 handle = 1;
inline void MakeCallableResponse::clear_handle() {
  handle_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MakeCallableResponse::handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.MakeCallableResponse.handle)
  return handle_;
}
inline void MakeCallableResponse::set_handle(::google::protobuf::int64 value) {
  
  handle_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MakeCallableResponse.handle)
}

// -------------------------------------------------------------------

// RunCallableRequest

// string session_handle = 1;
inline void RunCallableRequest::clear_session_handle() {
  session_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& RunCallableRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunCallableRequest.session_handle)
  return session_handle_.Get();
}
inline void RunCallableRequest::set_session_handle(const ::std::string& value) {
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.RunCallableRequest.session_handle)
}
#if LANG_CXX11
inline void RunCallableRequest::set_session_handle(::std::string&& value) {
  
  session_handle_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.RunCallableRequest.session_handle)
}
#endif
inline void RunCallableRequest::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.RunCallableRequest.session_handle)
}
inline void RunCallableRequest::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RunCallableRequest.session_handle)
}
inline ::std::string* RunCallableRequest::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.RunCallableRequest.session_handle)
  return session_handle_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* RunCallableRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.RunCallableRequest.session_handle)
  
  return session_handle_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void RunCallableRequest::set_allocated_session_handle(::std::string* session_handle) {
  if (session_handle != NULL) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunCallableRequest.session_handle)
}
inline ::std::string* RunCallableRequest::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunCallableRequest.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return session_handle_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void RunCallableRequest::unsafe_arena_set_allocated_session_handle(
    ::std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (session_handle != NULL) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunCallableRequest.session_handle)
}

// int64 handle = 2;
inline void RunCallableRequest::clear_handle() {
  handle_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 RunCallableRequest::handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunCallableRequest.handle)
  return handle_;
}
inline void RunCallableRequest::set_handle(::google::protobuf::int64 value) {
  
  handle_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunCallableRequest.handle)
}

// repeated .tensorflow.TensorProto feed = 3;
inline int RunCallableRequest::feed_size() const {
  return feed_.size();
}
inline ::tensorflow::TensorProto* RunCallableRequest::mutable_feed(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunCallableRequest.feed)
  return feed_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorProto >*
RunCallableRequest::mutable_feed() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunCallableRequest.feed)
  return &feed_;
}
inline const ::tensorflow::TensorProto& RunCallableRequest::feed(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunCallableRequest.feed)
  return feed_.Get(index);
}
inline ::tensorflow::TensorProto* RunCallableRequest::add_feed() {
  // @@protoc_insertion_point(field_add:tensorflow.RunCallableRequest.feed)
  return feed_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorProto >&
RunCallableRequest::feed() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunCallableRequest.feed)
  return feed_;
}

// int64 request_id = 4;
inline void RunCallableRequest::clear_request_id() {
  request_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 RunCallableRequest::request_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunCallableRequest.request_id)
  return request_id_;
}
inline void RunCallableRequest::set_request_id(::google::protobuf::int64 value) {
  
  request_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.RunCallableRequest.request_id)
}

// -------------------------------------------------------------------

// RunCallableResponse

// repeated .tensorflow.TensorProto fetch = 1;
inline int RunCallableResponse::fetch_size() const {
  return fetch_.size();
}
inline ::tensorflow::TensorProto* RunCallableResponse::mutable_fetch(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunCallableResponse.fetch)
  return fetch_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorProto >*
RunCallableResponse::mutable_fetch() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunCallableResponse.fetch)
  return &fetch_;
}
inline const ::tensorflow::TensorProto& RunCallableResponse::fetch(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunCallableResponse.fetch)
  return fetch_.Get(index);
}
inline ::tensorflow::TensorProto* RunCallableResponse::add_fetch() {
  // @@protoc_insertion_point(field_add:tensorflow.RunCallableResponse.fetch)
  return fetch_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorProto >&
RunCallableResponse::fetch() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunCallableResponse.fetch)
  return fetch_;
}

// .tensorflow.RunMetadata metadata = 2;
inline bool RunCallableResponse::has_metadata() const {
  return this != internal_default_instance() && metadata_ != NULL;
}
inline const ::tensorflow::RunMetadata& RunCallableResponse::_internal_metadata() const {
  return *metadata_;
}
inline const ::tensorflow::RunMetadata& RunCallableResponse::metadata() const {
  const ::tensorflow::RunMetadata* p = metadata_;
  // @@protoc_insertion_point(field_get:tensorflow.RunCallableResponse.metadata)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::RunMetadata*>(
      &::tensorflow::_RunMetadata_default_instance_);
}
inline ::tensorflow::RunMetadata* RunCallableResponse::release_metadata() {
  // @@protoc_insertion_point(field_release:tensorflow.RunCallableResponse.metadata)
  
  ::tensorflow::RunMetadata* temp = metadata_;
  if (GetArenaNoVirtual() != NULL) {
    temp = ::google::protobuf::internal::DuplicateIfNonNull(temp);
  }
  metadata_ = NULL;
  return temp;
}
inline ::tensorflow::RunMetadata* RunCallableResponse::unsafe_arena_release_metadata() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.RunCallableResponse.metadata)
  
  ::tensorflow::RunMetadata* temp = metadata_;
  metadata_ = NULL;
  return temp;
}
inline ::tensorflow::RunMetadata* RunCallableResponse::mutable_metadata() {
  
  if (metadata_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::RunMetadata>(GetArenaNoVirtual());
    metadata_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.RunCallableResponse.metadata)
  return metadata_;
}
inline void RunCallableResponse::set_allocated_metadata(::tensorflow::RunMetadata* metadata) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(metadata_);
  }
  if (metadata) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(metadata)->GetArena();
    if (message_arena != submessage_arena) {
      metadata = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, metadata, submessage_arena);
    }
    
  } else {
    
  }
  metadata_ = metadata;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunCallableResponse.metadata)
}

// -------------------------------------------------------------------

// ReleaseCallableRequest

// string session_handle = 1;
inline void ReleaseCallableRequest::clear_session_handle() {
  session_handle_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const ::std::string& ReleaseCallableRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReleaseCallableRequest.session_handle)
  return session_handle_.Get();
}
inline void ReleaseCallableRequest::set_session_handle(const ::std::string& value) {
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.ReleaseCallableRequest.session_handle)
}
#if LANG_CXX11
inline void ReleaseCallableRequest::set_session_handle(::std::string&& value) {
  
  session_handle_.Set(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ReleaseCallableRequest.session_handle)
}
#endif
inline void ReleaseCallableRequest::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.ReleaseCallableRequest.session_handle)
}
inline void ReleaseCallableRequest::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ReleaseCallableRequest.session_handle)
}
inline ::std::string* ReleaseCallableRequest::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ReleaseCallableRequest.session_handle)
  return session_handle_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline ::std::string* ReleaseCallableRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.ReleaseCallableRequest.session_handle)
  
  return session_handle_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void ReleaseCallableRequest::set_allocated_session_handle(::std::string* session_handle) {
  if (session_handle != NULL) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ReleaseCallableRequest.session_handle)
}
inline ::std::string* ReleaseCallableRequest::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReleaseCallableRequest.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return session_handle_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void ReleaseCallableRequest::unsafe_arena_set_allocated_session_handle(
    ::std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (session_handle != NULL) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReleaseCallableRequest.session_handle)
}

// int64 handle = 2;
inline void ReleaseCallableRequest::clear_handle() {
  handle_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ReleaseCallableRequest::handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReleaseCallableRequest.handle)
  return handle_;
}
inline void ReleaseCallableRequest::set_handle(::google::protobuf::int64 value) {
  
  handle_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ReleaseCallableRequest.handle)
}

// -------------------------------------------------------------------

// ReleaseCallableResponse

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/saved_model.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fsaved_5fmodel_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fsaved_5fmodel_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/protobuf/meta_graph.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fmodel_2eproto 

namespace protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fmodel_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[1];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fmodel_2eproto
namespace tensorflow {
class SavedModel;
class SavedModelDefaultTypeInternal;
extern SavedModelDefaultTypeInternal _SavedModel_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::SavedModel* Arena::CreateMaybeMessage<::tensorflow::SavedModel>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

// ===================================================================

class SavedModel : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedModel) */ {
 public:
  SavedModel();
  virtual ~SavedModel();

  SavedModel(const SavedModel& from);

  inline SavedModel& operator=(const SavedModel& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SavedModel(SavedModel&& from) noexcept
    : SavedModel() {
    *this = ::std::move(from);
  }

  inline SavedModel& operator=(SavedModel&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  inline ::google::protobuf::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::google::protobuf::Descriptor* descriptor();
  static const SavedModel& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SavedModel* internal_default_instance() {
    return reinterpret_cast<const SavedModel*>(
               &_SavedModel_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void UnsafeArenaSwap(SavedModel* other);
  void Swap(SavedModel* other);
  friend void swap(SavedModel& a, SavedModel& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SavedModel* New() const final {
    return CreateMaybeMessage<SavedModel>(NULL);
  }

  SavedModel* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<SavedModel>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const SavedModel& from);
  void MergeFrom(const SavedModel& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedModel* other);
  protected:
  explicit SavedModel(::google::protobuf::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::google::protobuf::Arena* arena);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.MetaGraphDef meta_graphs = 2;
  int meta_graphs_size() const;
  void clear_meta_graphs();
  static const int kMetaGraphsFieldNumber = 2;
  ::tensorflow::MetaGraphDef* mutable_meta_graphs(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::MetaGraphDef >*
      mutable_meta_graphs();
  const ::tensorflow::MetaGraphDef& meta_graphs(int index) const;
  ::tensorflow::MetaGraphDef* add_meta_graphs();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::MetaGraphDef >&
      meta_graphs() const;

  // int64 saved_model_schema_version = 1;
  void clear_saved_model_schema_version();
  static const int kSavedModelSchemaVersionFieldNumber = 1;
  ::google::protobuf::int64 saved_model_schema_version() const;
  void set_saved_model_schema_version(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.SavedModel)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::google::protobuf::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::MetaGraphDef > meta_graphs_;
  ::google::protobuf::int64 saved_model_schema_version_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fmodel_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SavedModel

// int64 saved_model_schema_version = 1;
inline void SavedModel::clear_saved_model_schema_version() {
  saved_model_schema_version_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SavedModel::saved_model_schema_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedModel.saved_model_schema_version)
  return saved_model_schema_version_;
}
inline void SavedModel::set_saved_model_schema_version(::google::protobuf::int64 value) {
  
  saved_model_schema_version_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.SavedModel.saved_model_schema_version)
}

// repeated .tensorflow.MetaGraphDef meta_graphs = 2;
inline int SavedModel::meta_graphs_size() const {
  return meta_graphs_.size();
}
inline ::tensorflow::MetaGraphDef* SavedModel::mutable_meta_graphs(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedModel.meta_graphs)
  return meta_graphs_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::MetaGraphDef >*
SavedModel::mutable_meta_graphs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SavedModel.meta_graphs)
  return &meta_graphs_;
}
inline const ::tensorflow::MetaGraphDef& SavedModel::meta_graphs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedModel.meta_graphs)
  return meta_graphs_.Get(index);
}
inline ::tensorflow::MetaGraphDef* SavedModel::add_meta_graphs() {
  // @@protoc_insertion_point(field_add:tensorflow.SavedModel.meta_graphs)
  return meta_graphs_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::MetaGraphDef >&
SavedModel::meta_graphs() const {
  // @@protoc_insertion_point(field_list:tensorflow.SavedModel.meta_graphs)
  return meta_graphs_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fsaved_5fmodel_2eproto

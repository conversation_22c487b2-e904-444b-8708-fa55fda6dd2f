// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/tensorflow_server.proto

#include "tensorflow/core/protobuf/tensorflow_server.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fprotobuf_2fcluster_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fcluster_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_ClusterDef;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fcluster_2eproto
namespace protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto ::google::protobuf::internal::SCCInfo<7> scc_info_ConfigProto;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto
namespace tensorflow {
class ServerDefDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ServerDef>
      _instance;
} _ServerDef_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftensorflow_5fserver_2eproto {
static void InitDefaultsServerDef() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_ServerDef_default_instance_;
    new (ptr) ::tensorflow::ServerDef();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::ServerDef::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_ServerDef =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsServerDef}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fcluster_2eproto::scc_info_ClusterDef.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_ConfigProto.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_ServerDef.base);
}

::google::protobuf::Metadata file_level_metadata[1];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ServerDef, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ServerDef, cluster_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ServerDef, job_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ServerDef, task_index_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ServerDef, default_session_config_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ServerDef, protocol_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::ServerDef)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_ServerDef_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/protobuf/tensorflow_server.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 1);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n0tensorflow/core/protobuf/tensorflow_se"
      "rver.proto\022\ntensorflow\032%tensorflow/core/"
      "protobuf/config.proto\032&tensorflow/core/p"
      "rotobuf/cluster.proto\"\245\001\n\tServerDef\022\'\n\007c"
      "luster\030\001 \001(\0132\026.tensorflow.ClusterDef\022\020\n\010"
      "job_name\030\002 \001(\t\022\022\n\ntask_index\030\003 \001(\005\0227\n\026de"
      "fault_session_config\030\004 \001(\0132\027.tensorflow."
      "ConfigProto\022\020\n\010protocol\030\005 \001(\tBm\n\032org.ten"
      "sorflow.distruntimeB\014ServerProtosP\001Z<git"
      "hub.com/tensorflow/tensorflow/tensorflow"
      "/go/core/protobuf\370\001\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 428);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/protobuf/tensorflow_server.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fcluster_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftensorflow_5fserver_2eproto
namespace tensorflow {

// ===================================================================

void ServerDef::InitAsDefaultInstance() {
  ::tensorflow::_ServerDef_default_instance_._instance.get_mutable()->cluster_ = const_cast< ::tensorflow::ClusterDef*>(
      ::tensorflow::ClusterDef::internal_default_instance());
  ::tensorflow::_ServerDef_default_instance_._instance.get_mutable()->default_session_config_ = const_cast< ::tensorflow::ConfigProto*>(
      ::tensorflow::ConfigProto::internal_default_instance());
}
void ServerDef::unsafe_arena_set_allocated_cluster(
    ::tensorflow::ClusterDef* cluster) {
  if (GetArenaNoVirtual() == NULL) {
    delete cluster_;
  }
  cluster_ = cluster;
  if (cluster) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ServerDef.cluster)
}
void ServerDef::clear_cluster() {
  if (GetArenaNoVirtual() == NULL && cluster_ != NULL) {
    delete cluster_;
  }
  cluster_ = NULL;
}
void ServerDef::unsafe_arena_set_allocated_default_session_config(
    ::tensorflow::ConfigProto* default_session_config) {
  if (GetArenaNoVirtual() == NULL) {
    delete default_session_config_;
  }
  default_session_config_ = default_session_config;
  if (default_session_config) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ServerDef.default_session_config)
}
void ServerDef::clear_default_session_config() {
  if (GetArenaNoVirtual() == NULL && default_session_config_ != NULL) {
    delete default_session_config_;
  }
  default_session_config_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ServerDef::kClusterFieldNumber;
const int ServerDef::kJobNameFieldNumber;
const int ServerDef::kTaskIndexFieldNumber;
const int ServerDef::kDefaultSessionConfigFieldNumber;
const int ServerDef::kProtocolFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ServerDef::ServerDef()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftensorflow_5fserver_2eproto::scc_info_ServerDef.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.ServerDef)
}
ServerDef::ServerDef(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftensorflow_5fserver_2eproto::scc_info_ServerDef.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.ServerDef)
}
ServerDef::ServerDef(const ServerDef& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  job_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.job_name().size() > 0) {
    job_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.job_name(),
      GetArenaNoVirtual());
  }
  protocol_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.protocol().size() > 0) {
    protocol_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.protocol(),
      GetArenaNoVirtual());
  }
  if (from.has_cluster()) {
    cluster_ = new ::tensorflow::ClusterDef(*from.cluster_);
  } else {
    cluster_ = NULL;
  }
  if (from.has_default_session_config()) {
    default_session_config_ = new ::tensorflow::ConfigProto(*from.default_session_config_);
  } else {
    default_session_config_ = NULL;
  }
  task_index_ = from.task_index_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.ServerDef)
}

void ServerDef::SharedCtor() {
  job_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  protocol_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&cluster_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&task_index_) -
      reinterpret_cast<char*>(&cluster_)) + sizeof(task_index_));
}

ServerDef::~ServerDef() {
  // @@protoc_insertion_point(destructor:tensorflow.ServerDef)
  SharedDtor();
}

void ServerDef::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  job_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  protocol_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete cluster_;
  if (this != internal_default_instance()) delete default_session_config_;
}

void ServerDef::ArenaDtor(void* object) {
  ServerDef* _this = reinterpret_cast< ServerDef* >(object);
  (void)_this;
}
void ServerDef::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void ServerDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ServerDef::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftensorflow_5fserver_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftensorflow_5fserver_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ServerDef& ServerDef::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftensorflow_5fserver_2eproto::scc_info_ServerDef.base);
  return *internal_default_instance();
}


void ServerDef::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.ServerDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  job_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  protocol_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && cluster_ != NULL) {
    delete cluster_;
  }
  cluster_ = NULL;
  if (GetArenaNoVirtual() == NULL && default_session_config_ != NULL) {
    delete default_session_config_;
  }
  default_session_config_ = NULL;
  task_index_ = 0;
  _internal_metadata_.Clear();
}

bool ServerDef::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.ServerDef)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.ClusterDef cluster = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_cluster()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string job_name = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_job_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->job_name().data(), static_cast<int>(this->job_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ServerDef.job_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 task_index = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &task_index_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.ConfigProto default_session_config = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_default_session_config()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string protocol = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_protocol()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->protocol().data(), static_cast<int>(this->protocol().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ServerDef.protocol"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.ServerDef)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.ServerDef)
  return false;
#undef DO_
}

void ServerDef::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.ServerDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.ClusterDef cluster = 1;
  if (this->has_cluster()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->_internal_cluster(), output);
  }

  // string job_name = 2;
  if (this->job_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->job_name().data(), static_cast<int>(this->job_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ServerDef.job_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->job_name(), output);
  }

  // int32 task_index = 3;
  if (this->task_index() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->task_index(), output);
  }

  // .tensorflow.ConfigProto default_session_config = 4;
  if (this->has_default_session_config()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->_internal_default_session_config(), output);
  }

  // string protocol = 5;
  if (this->protocol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->protocol().data(), static_cast<int>(this->protocol().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ServerDef.protocol");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->protocol(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.ServerDef)
}

::google::protobuf::uint8* ServerDef::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.ServerDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.ClusterDef cluster = 1;
  if (this->has_cluster()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->_internal_cluster(), deterministic, target);
  }

  // string job_name = 2;
  if (this->job_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->job_name().data(), static_cast<int>(this->job_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ServerDef.job_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->job_name(), target);
  }

  // int32 task_index = 3;
  if (this->task_index() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->task_index(), target);
  }

  // .tensorflow.ConfigProto default_session_config = 4;
  if (this->has_default_session_config()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->_internal_default_session_config(), deterministic, target);
  }

  // string protocol = 5;
  if (this->protocol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->protocol().data(), static_cast<int>(this->protocol().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ServerDef.protocol");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->protocol(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.ServerDef)
  return target;
}

size_t ServerDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.ServerDef)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string job_name = 2;
  if (this->job_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->job_name());
  }

  // string protocol = 5;
  if (this->protocol().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->protocol());
  }

  // .tensorflow.ClusterDef cluster = 1;
  if (this->has_cluster()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *cluster_);
  }

  // .tensorflow.ConfigProto default_session_config = 4;
  if (this->has_default_session_config()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *default_session_config_);
  }

  // int32 task_index = 3;
  if (this->task_index() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->task_index());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ServerDef::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.ServerDef)
  GOOGLE_DCHECK_NE(&from, this);
  const ServerDef* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ServerDef>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.ServerDef)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.ServerDef)
    MergeFrom(*source);
  }
}

void ServerDef::MergeFrom(const ServerDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.ServerDef)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.job_name().size() > 0) {
    set_job_name(from.job_name());
  }
  if (from.protocol().size() > 0) {
    set_protocol(from.protocol());
  }
  if (from.has_cluster()) {
    mutable_cluster()->::tensorflow::ClusterDef::MergeFrom(from.cluster());
  }
  if (from.has_default_session_config()) {
    mutable_default_session_config()->::tensorflow::ConfigProto::MergeFrom(from.default_session_config());
  }
  if (from.task_index() != 0) {
    set_task_index(from.task_index());
  }
}

void ServerDef::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.ServerDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ServerDef::CopyFrom(const ServerDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.ServerDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ServerDef::IsInitialized() const {
  return true;
}

void ServerDef::Swap(ServerDef* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    ServerDef* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void ServerDef::UnsafeArenaSwap(ServerDef* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void ServerDef::InternalSwap(ServerDef* other) {
  using std::swap;
  job_name_.Swap(&other->job_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  protocol_.Swap(&other->protocol_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(cluster_, other->cluster_);
  swap(default_session_config_, other->default_session_config_);
  swap(task_index_, other->task_index_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ServerDef::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftensorflow_5fserver_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftensorflow_5fserver_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::ServerDef* Arena::CreateMaybeMessage< ::tensorflow::ServerDef >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::ServerDef >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/meta_graph.proto

#include "tensorflow/core/protobuf/meta_graph.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_google_2fprotobuf_2fany_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_google_2fprotobuf_2fany_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_Any;
}  // namespace protobuf_google_2fprotobuf_2fany_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2fgraph_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fgraph_2eproto ::google::protobuf::internal::SCCInfo<3> scc_info_GraphDef;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fgraph_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_OpList;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_TensorShapeProto;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto
namespace protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_CollectionDef_BytesList;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_CollectionDef_FloatList;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_CollectionDef_Int64List;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_CollectionDef_NodeList;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_TensorInfo_CooSparse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_AssetFileDef;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_CollectionDef_AnyList;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_MetaGraphDef_CollectionDefEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_MetaGraphDef_SignatureDefEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_SignatureDef_InputsEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_SignatureDef_OutputsEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_MetaGraphDef_MetaInfoDef;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_SignatureDef;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_TensorInfo;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<5> scc_info_CollectionDef;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto
namespace protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_SavedObjectGraph;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto
namespace protobuf_tensorflow_2fcore_2fprotobuf_2fsaver_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fsaver_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_SaverDef;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fsaver_2eproto
namespace tensorflow {
class MetaGraphDef_MetaInfoDefDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<MetaGraphDef_MetaInfoDef>
      _instance;
} _MetaGraphDef_MetaInfoDef_default_instance_;
class MetaGraphDef_CollectionDefEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<MetaGraphDef_CollectionDefEntry_DoNotUse>
      _instance;
} _MetaGraphDef_CollectionDefEntry_DoNotUse_default_instance_;
class MetaGraphDef_SignatureDefEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<MetaGraphDef_SignatureDefEntry_DoNotUse>
      _instance;
} _MetaGraphDef_SignatureDefEntry_DoNotUse_default_instance_;
class MetaGraphDefDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<MetaGraphDef>
      _instance;
} _MetaGraphDef_default_instance_;
class CollectionDef_NodeListDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CollectionDef_NodeList>
      _instance;
} _CollectionDef_NodeList_default_instance_;
class CollectionDef_BytesListDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CollectionDef_BytesList>
      _instance;
} _CollectionDef_BytesList_default_instance_;
class CollectionDef_Int64ListDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CollectionDef_Int64List>
      _instance;
} _CollectionDef_Int64List_default_instance_;
class CollectionDef_FloatListDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CollectionDef_FloatList>
      _instance;
} _CollectionDef_FloatList_default_instance_;
class CollectionDef_AnyListDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CollectionDef_AnyList>
      _instance;
} _CollectionDef_AnyList_default_instance_;
class CollectionDefDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CollectionDef>
      _instance;
  const ::tensorflow::CollectionDef_NodeList* node_list_;
  const ::tensorflow::CollectionDef_BytesList* bytes_list_;
  const ::tensorflow::CollectionDef_Int64List* int64_list_;
  const ::tensorflow::CollectionDef_FloatList* float_list_;
  const ::tensorflow::CollectionDef_AnyList* any_list_;
} _CollectionDef_default_instance_;
class TensorInfo_CooSparseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TensorInfo_CooSparse>
      _instance;
} _TensorInfo_CooSparse_default_instance_;
class TensorInfoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TensorInfo>
      _instance;
  ::google::protobuf::internal::ArenaStringPtr name_;
  const ::tensorflow::TensorInfo_CooSparse* coo_sparse_;
} _TensorInfo_default_instance_;
class SignatureDef_InputsEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SignatureDef_InputsEntry_DoNotUse>
      _instance;
} _SignatureDef_InputsEntry_DoNotUse_default_instance_;
class SignatureDef_OutputsEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SignatureDef_OutputsEntry_DoNotUse>
      _instance;
} _SignatureDef_OutputsEntry_DoNotUse_default_instance_;
class SignatureDefDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SignatureDef>
      _instance;
} _SignatureDef_default_instance_;
class AssetFileDefDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<AssetFileDef>
      _instance;
} _AssetFileDef_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto {
static void InitDefaultsMetaGraphDef_MetaInfoDef() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_MetaGraphDef_MetaInfoDef_default_instance_;
    new (ptr) ::tensorflow::MetaGraphDef_MetaInfoDef();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::MetaGraphDef_MetaInfoDef::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_MetaGraphDef_MetaInfoDef =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsMetaGraphDef_MetaInfoDef}, {
      &protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::scc_info_OpList.base,
      &protobuf_google_2fprotobuf_2fany_2eproto::scc_info_Any.base,}};

static void InitDefaultsMetaGraphDef_CollectionDefEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_MetaGraphDef_CollectionDefEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::MetaGraphDef_CollectionDefEntry_DoNotUse();
  }
  ::tensorflow::MetaGraphDef_CollectionDefEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_MetaGraphDef_CollectionDefEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsMetaGraphDef_CollectionDefEntry_DoNotUse}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_CollectionDef.base,}};

static void InitDefaultsMetaGraphDef_SignatureDefEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_MetaGraphDef_SignatureDefEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::MetaGraphDef_SignatureDefEntry_DoNotUse();
  }
  ::tensorflow::MetaGraphDef_SignatureDefEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_MetaGraphDef_SignatureDefEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsMetaGraphDef_SignatureDefEntry_DoNotUse}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_SignatureDef.base,}};

static void InitDefaultsMetaGraphDef() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_MetaGraphDef_default_instance_;
    new (ptr) ::tensorflow::MetaGraphDef();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::MetaGraphDef::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<7> scc_info_MetaGraphDef =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 7, InitDefaultsMetaGraphDef}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_MetaGraphDef_MetaInfoDef.base,
      &protobuf_tensorflow_2fcore_2fframework_2fgraph_2eproto::scc_info_GraphDef.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fsaver_2eproto::scc_info_SaverDef.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_MetaGraphDef_CollectionDefEntry_DoNotUse.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_MetaGraphDef_SignatureDefEntry_DoNotUse.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_AssetFileDef.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::scc_info_SavedObjectGraph.base,}};

static void InitDefaultsCollectionDef_NodeList() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_CollectionDef_NodeList_default_instance_;
    new (ptr) ::tensorflow::CollectionDef_NodeList();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::CollectionDef_NodeList::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_CollectionDef_NodeList =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsCollectionDef_NodeList}, {}};

static void InitDefaultsCollectionDef_BytesList() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_CollectionDef_BytesList_default_instance_;
    new (ptr) ::tensorflow::CollectionDef_BytesList();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::CollectionDef_BytesList::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_CollectionDef_BytesList =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsCollectionDef_BytesList}, {}};

static void InitDefaultsCollectionDef_Int64List() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_CollectionDef_Int64List_default_instance_;
    new (ptr) ::tensorflow::CollectionDef_Int64List();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::CollectionDef_Int64List::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_CollectionDef_Int64List =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsCollectionDef_Int64List}, {}};

static void InitDefaultsCollectionDef_FloatList() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_CollectionDef_FloatList_default_instance_;
    new (ptr) ::tensorflow::CollectionDef_FloatList();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::CollectionDef_FloatList::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_CollectionDef_FloatList =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsCollectionDef_FloatList}, {}};

static void InitDefaultsCollectionDef_AnyList() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_CollectionDef_AnyList_default_instance_;
    new (ptr) ::tensorflow::CollectionDef_AnyList();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::CollectionDef_AnyList::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_CollectionDef_AnyList =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsCollectionDef_AnyList}, {
      &protobuf_google_2fprotobuf_2fany_2eproto::scc_info_Any.base,}};

static void InitDefaultsCollectionDef() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_CollectionDef_default_instance_;
    new (ptr) ::tensorflow::CollectionDef();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::CollectionDef::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<5> scc_info_CollectionDef =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 5, InitDefaultsCollectionDef}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_CollectionDef_NodeList.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_CollectionDef_BytesList.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_CollectionDef_Int64List.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_CollectionDef_FloatList.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_CollectionDef_AnyList.base,}};

static void InitDefaultsTensorInfo_CooSparse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_TensorInfo_CooSparse_default_instance_;
    new (ptr) ::tensorflow::TensorInfo_CooSparse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::TensorInfo_CooSparse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_TensorInfo_CooSparse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsTensorInfo_CooSparse}, {}};

static void InitDefaultsTensorInfo() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_TensorInfo_default_instance_;
    new (ptr) ::tensorflow::TensorInfo();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::TensorInfo::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_TensorInfo =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsTensorInfo}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_TensorInfo_CooSparse.base,
      &protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::scc_info_TensorShapeProto.base,}};

static void InitDefaultsSignatureDef_InputsEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_SignatureDef_InputsEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::SignatureDef_InputsEntry_DoNotUse();
  }
  ::tensorflow::SignatureDef_InputsEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_SignatureDef_InputsEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsSignatureDef_InputsEntry_DoNotUse}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_TensorInfo.base,}};

static void InitDefaultsSignatureDef_OutputsEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_SignatureDef_OutputsEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::SignatureDef_OutputsEntry_DoNotUse();
  }
  ::tensorflow::SignatureDef_OutputsEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_SignatureDef_OutputsEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsSignatureDef_OutputsEntry_DoNotUse}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_TensorInfo.base,}};

static void InitDefaultsSignatureDef() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_SignatureDef_default_instance_;
    new (ptr) ::tensorflow::SignatureDef();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::SignatureDef::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_SignatureDef =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsSignatureDef}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_SignatureDef_InputsEntry_DoNotUse.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_SignatureDef_OutputsEntry_DoNotUse.base,}};

static void InitDefaultsAssetFileDef() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_AssetFileDef_default_instance_;
    new (ptr) ::tensorflow::AssetFileDef();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::AssetFileDef::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_AssetFileDef =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsAssetFileDef}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_TensorInfo.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_MetaGraphDef_MetaInfoDef.base);
  ::google::protobuf::internal::InitSCC(&scc_info_MetaGraphDef_CollectionDefEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_MetaGraphDef_SignatureDefEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_MetaGraphDef.base);
  ::google::protobuf::internal::InitSCC(&scc_info_CollectionDef_NodeList.base);
  ::google::protobuf::internal::InitSCC(&scc_info_CollectionDef_BytesList.base);
  ::google::protobuf::internal::InitSCC(&scc_info_CollectionDef_Int64List.base);
  ::google::protobuf::internal::InitSCC(&scc_info_CollectionDef_FloatList.base);
  ::google::protobuf::internal::InitSCC(&scc_info_CollectionDef_AnyList.base);
  ::google::protobuf::internal::InitSCC(&scc_info_CollectionDef.base);
  ::google::protobuf::internal::InitSCC(&scc_info_TensorInfo_CooSparse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_TensorInfo.base);
  ::google::protobuf::internal::InitSCC(&scc_info_SignatureDef_InputsEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_SignatureDef_OutputsEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_SignatureDef.base);
  ::google::protobuf::internal::InitSCC(&scc_info_AssetFileDef.base);
}

::google::protobuf::Metadata file_level_metadata[16];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MetaGraphDef_MetaInfoDef, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MetaGraphDef_MetaInfoDef, meta_graph_version_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MetaGraphDef_MetaInfoDef, stripped_op_list_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MetaGraphDef_MetaInfoDef, any_info_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MetaGraphDef_MetaInfoDef, tags_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MetaGraphDef_MetaInfoDef, tensorflow_version_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MetaGraphDef_MetaInfoDef, tensorflow_git_version_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MetaGraphDef_MetaInfoDef, stripped_default_attrs_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MetaGraphDef_CollectionDefEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MetaGraphDef_CollectionDefEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MetaGraphDef_CollectionDefEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MetaGraphDef_CollectionDefEntry_DoNotUse, value_),
  0,
  1,
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MetaGraphDef_SignatureDefEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MetaGraphDef_SignatureDefEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MetaGraphDef_SignatureDefEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MetaGraphDef_SignatureDefEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MetaGraphDef, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MetaGraphDef, meta_info_def_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MetaGraphDef, graph_def_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MetaGraphDef, saver_def_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MetaGraphDef, collection_def_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MetaGraphDef, signature_def_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MetaGraphDef, asset_file_def_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::MetaGraphDef, object_graph_def_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CollectionDef_NodeList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CollectionDef_NodeList, value_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CollectionDef_BytesList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CollectionDef_BytesList, value_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CollectionDef_Int64List, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CollectionDef_Int64List, value_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CollectionDef_FloatList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CollectionDef_FloatList, value_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CollectionDef_AnyList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CollectionDef_AnyList, value_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CollectionDef, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CollectionDef, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  offsetof(::tensorflow::CollectionDefDefaultTypeInternal, node_list_),
  offsetof(::tensorflow::CollectionDefDefaultTypeInternal, bytes_list_),
  offsetof(::tensorflow::CollectionDefDefaultTypeInternal, int64_list_),
  offsetof(::tensorflow::CollectionDefDefaultTypeInternal, float_list_),
  offsetof(::tensorflow::CollectionDefDefaultTypeInternal, any_list_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CollectionDef, kind_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorInfo_CooSparse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorInfo_CooSparse, values_tensor_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorInfo_CooSparse, indices_tensor_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorInfo_CooSparse, dense_shape_tensor_name_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorInfo, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  offsetof(::tensorflow::TensorInfoDefaultTypeInternal, name_),
  offsetof(::tensorflow::TensorInfoDefaultTypeInternal, coo_sparse_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorInfo, dtype_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorInfo, tensor_shape_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorInfo, encoding_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SignatureDef_InputsEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SignatureDef_InputsEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SignatureDef_InputsEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SignatureDef_InputsEntry_DoNotUse, value_),
  0,
  1,
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SignatureDef_OutputsEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SignatureDef_OutputsEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SignatureDef_OutputsEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SignatureDef_OutputsEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SignatureDef, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SignatureDef, inputs_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SignatureDef, outputs_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::SignatureDef, method_name_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AssetFileDef, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AssetFileDef, tensor_info_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::AssetFileDef, filename_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::MetaGraphDef_MetaInfoDef)},
  { 12, 19, sizeof(::tensorflow::MetaGraphDef_CollectionDefEntry_DoNotUse)},
  { 21, 28, sizeof(::tensorflow::MetaGraphDef_SignatureDefEntry_DoNotUse)},
  { 30, -1, sizeof(::tensorflow::MetaGraphDef)},
  { 42, -1, sizeof(::tensorflow::CollectionDef_NodeList)},
  { 48, -1, sizeof(::tensorflow::CollectionDef_BytesList)},
  { 54, -1, sizeof(::tensorflow::CollectionDef_Int64List)},
  { 60, -1, sizeof(::tensorflow::CollectionDef_FloatList)},
  { 66, -1, sizeof(::tensorflow::CollectionDef_AnyList)},
  { 72, -1, sizeof(::tensorflow::CollectionDef)},
  { 83, -1, sizeof(::tensorflow::TensorInfo_CooSparse)},
  { 91, -1, sizeof(::tensorflow::TensorInfo)},
  { 101, 108, sizeof(::tensorflow::SignatureDef_InputsEntry_DoNotUse)},
  { 110, 117, sizeof(::tensorflow::SignatureDef_OutputsEntry_DoNotUse)},
  { 119, -1, sizeof(::tensorflow::SignatureDef)},
  { 127, -1, sizeof(::tensorflow::AssetFileDef)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_MetaGraphDef_MetaInfoDef_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_MetaGraphDef_CollectionDefEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_MetaGraphDef_SignatureDefEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_MetaGraphDef_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_CollectionDef_NodeList_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_CollectionDef_BytesList_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_CollectionDef_Int64List_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_CollectionDef_FloatList_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_CollectionDef_AnyList_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_CollectionDef_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_TensorInfo_CooSparse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_TensorInfo_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_SignatureDef_InputsEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_SignatureDef_OutputsEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_SignatureDef_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_AssetFileDef_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/protobuf/meta_graph.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 16);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n)tensorflow/core/protobuf/meta_graph.pr"
      "oto\022\ntensorflow\032\031google/protobuf/any.pro"
      "to\032%tensorflow/core/framework/graph.prot"
      "o\032&tensorflow/core/framework/op_def.prot"
      "o\032,tensorflow/core/framework/tensor_shap"
      "e.proto\032%tensorflow/core/framework/types"
      ".proto\0321tensorflow/core/protobuf/saved_o"
      "bject_graph.proto\032$tensorflow/core/proto"
      "buf/saver.proto\"\233\006\n\014MetaGraphDef\022;\n\rmeta"
      "_info_def\030\001 \001(\0132$.tensorflow.MetaGraphDe"
      "f.MetaInfoDef\022\'\n\tgraph_def\030\002 \001(\0132\024.tenso"
      "rflow.GraphDef\022\'\n\tsaver_def\030\003 \001(\0132\024.tens"
      "orflow.SaverDef\022C\n\016collection_def\030\004 \003(\0132"
      "+.tensorflow.MetaGraphDef.CollectionDefE"
      "ntry\022A\n\rsignature_def\030\005 \003(\0132*.tensorflow"
      ".MetaGraphDef.SignatureDefEntry\0220\n\016asset"
      "_file_def\030\006 \003(\0132\030.tensorflow.AssetFileDe"
      "f\0226\n\020object_graph_def\030\007 \001(\0132\034.tensorflow"
      ".SavedObjectGraph\032\351\001\n\013MetaInfoDef\022\032\n\022met"
      "a_graph_version\030\001 \001(\t\022,\n\020stripped_op_lis"
      "t\030\002 \001(\0132\022.tensorflow.OpList\022&\n\010any_info\030"
      "\003 \001(\0132\024.google.protobuf.Any\022\014\n\004tags\030\004 \003("
      "\t\022\032\n\022tensorflow_version\030\005 \001(\t\022\036\n\026tensorf"
      "low_git_version\030\006 \001(\t\022\036\n\026stripped_defaul"
      "t_attrs\030\007 \001(\010\032O\n\022CollectionDefEntry\022\013\n\003k"
      "ey\030\001 \001(\t\022(\n\005value\030\002 \001(\0132\031.tensorflow.Col"
      "lectionDef:\0028\001\032M\n\021SignatureDefEntry\022\013\n\003k"
      "ey\030\001 \001(\t\022\'\n\005value\030\002 \001(\0132\030.tensorflow.Sig"
      "natureDef:\0028\001\"\337\003\n\rCollectionDef\0227\n\tnode_"
      "list\030\001 \001(\0132\".tensorflow.CollectionDef.No"
      "deListH\000\0229\n\nbytes_list\030\002 \001(\0132#.tensorflo"
      "w.CollectionDef.BytesListH\000\0229\n\nint64_lis"
      "t\030\003 \001(\0132#.tensorflow.CollectionDef.Int64"
      "ListH\000\0229\n\nfloat_list\030\004 \001(\0132#.tensorflow."
      "CollectionDef.FloatListH\000\0225\n\010any_list\030\005 "
      "\001(\0132!.tensorflow.CollectionDef.AnyListH\000"
      "\032\031\n\010NodeList\022\r\n\005value\030\001 \003(\t\032\032\n\tBytesList"
      "\022\r\n\005value\030\001 \003(\014\032\036\n\tInt64List\022\021\n\005value\030\001 "
      "\003(\003B\002\020\001\032\036\n\tFloatList\022\021\n\005value\030\001 \003(\002B\002\020\001\032"
      ".\n\007AnyList\022#\n\005value\030\001 \003(\0132\024.google.proto"
      "buf.AnyB\006\n\004kind\"\240\002\n\nTensorInfo\022\016\n\004name\030\001"
      " \001(\tH\000\0226\n\ncoo_sparse\030\004 \001(\0132 .tensorflow."
      "TensorInfo.CooSparseH\000\022#\n\005dtype\030\002 \001(\0162\024."
      "tensorflow.DataType\0222\n\014tensor_shape\030\003 \001("
      "\0132\034.tensorflow.TensorShapeProto\032e\n\tCooSp"
      "arse\022\032\n\022values_tensor_name\030\001 \001(\t\022\033\n\023indi"
      "ces_tensor_name\030\002 \001(\t\022\037\n\027dense_shape_ten"
      "sor_name\030\003 \001(\tB\n\n\010encoding\"\240\002\n\014Signature"
      "Def\0224\n\006inputs\030\001 \003(\0132$.tensorflow.Signatu"
      "reDef.InputsEntry\0226\n\007outputs\030\002 \003(\0132%.ten"
      "sorflow.SignatureDef.OutputsEntry\022\023\n\013met"
      "hod_name\030\003 \001(\t\032E\n\013InputsEntry\022\013\n\003key\030\001 \001"
      "(\t\022%\n\005value\030\002 \001(\0132\026.tensorflow.TensorInf"
      "o:\0028\001\032F\n\014OutputsEntry\022\013\n\003key\030\001 \001(\t\022%\n\005va"
      "lue\030\002 \001(\0132\026.tensorflow.TensorInfo:\0028\001\"M\n"
      "\014AssetFileDef\022+\n\013tensor_info\030\001 \001(\0132\026.ten"
      "sorflow.TensorInfo\022\020\n\010filename\030\002 \001(\tBn\n\030"
      "org.tensorflow.frameworkB\017MetaGraphProto"
      "sP\001Z<github.com/tensorflow/tensorflow/te"
      "nsorflow/go/core/protobuf\370\001\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 2396);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/protobuf/meta_graph.proto", &protobuf_RegisterTypes);
  ::protobuf_google_2fprotobuf_2fany_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2fgraph_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftensor_5fshape_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2ftypes_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fsaver_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto
namespace tensorflow {

// ===================================================================

void MetaGraphDef_MetaInfoDef::InitAsDefaultInstance() {
  ::tensorflow::_MetaGraphDef_MetaInfoDef_default_instance_._instance.get_mutable()->stripped_op_list_ = const_cast< ::tensorflow::OpList*>(
      ::tensorflow::OpList::internal_default_instance());
  ::tensorflow::_MetaGraphDef_MetaInfoDef_default_instance_._instance.get_mutable()->any_info_ = const_cast< ::google::protobuf::Any*>(
      ::google::protobuf::Any::internal_default_instance());
}
void MetaGraphDef_MetaInfoDef::unsafe_arena_set_allocated_stripped_op_list(
    ::tensorflow::OpList* stripped_op_list) {
  if (GetArenaNoVirtual() == NULL) {
    delete stripped_op_list_;
  }
  stripped_op_list_ = stripped_op_list;
  if (stripped_op_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MetaGraphDef.MetaInfoDef.stripped_op_list)
}
void MetaGraphDef_MetaInfoDef::clear_stripped_op_list() {
  if (GetArenaNoVirtual() == NULL && stripped_op_list_ != NULL) {
    delete stripped_op_list_;
  }
  stripped_op_list_ = NULL;
}
void MetaGraphDef_MetaInfoDef::unsafe_arena_set_allocated_any_info(
    ::google::protobuf::Any* any_info) {
  if (GetArenaNoVirtual() == NULL) {
    delete any_info_;
  }
  any_info_ = any_info;
  if (any_info) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MetaGraphDef.MetaInfoDef.any_info)
}
void MetaGraphDef_MetaInfoDef::clear_any_info() {
  if (GetArenaNoVirtual() == NULL && any_info_ != NULL) {
    delete any_info_;
  }
  any_info_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MetaGraphDef_MetaInfoDef::kMetaGraphVersionFieldNumber;
const int MetaGraphDef_MetaInfoDef::kStrippedOpListFieldNumber;
const int MetaGraphDef_MetaInfoDef::kAnyInfoFieldNumber;
const int MetaGraphDef_MetaInfoDef::kTagsFieldNumber;
const int MetaGraphDef_MetaInfoDef::kTensorflowVersionFieldNumber;
const int MetaGraphDef_MetaInfoDef::kTensorflowGitVersionFieldNumber;
const int MetaGraphDef_MetaInfoDef::kStrippedDefaultAttrsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MetaGraphDef_MetaInfoDef::MetaGraphDef_MetaInfoDef()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_MetaGraphDef_MetaInfoDef.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.MetaGraphDef.MetaInfoDef)
}
MetaGraphDef_MetaInfoDef::MetaGraphDef_MetaInfoDef(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  tags_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_MetaGraphDef_MetaInfoDef.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.MetaGraphDef.MetaInfoDef)
}
MetaGraphDef_MetaInfoDef::MetaGraphDef_MetaInfoDef(const MetaGraphDef_MetaInfoDef& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      tags_(from.tags_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  meta_graph_version_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.meta_graph_version().size() > 0) {
    meta_graph_version_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.meta_graph_version(),
      GetArenaNoVirtual());
  }
  tensorflow_version_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.tensorflow_version().size() > 0) {
    tensorflow_version_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tensorflow_version(),
      GetArenaNoVirtual());
  }
  tensorflow_git_version_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.tensorflow_git_version().size() > 0) {
    tensorflow_git_version_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tensorflow_git_version(),
      GetArenaNoVirtual());
  }
  if (from.has_stripped_op_list()) {
    stripped_op_list_ = new ::tensorflow::OpList(*from.stripped_op_list_);
  } else {
    stripped_op_list_ = NULL;
  }
  if (from.has_any_info()) {
    any_info_ = new ::google::protobuf::Any(*from.any_info_);
  } else {
    any_info_ = NULL;
  }
  stripped_default_attrs_ = from.stripped_default_attrs_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.MetaGraphDef.MetaInfoDef)
}

void MetaGraphDef_MetaInfoDef::SharedCtor() {
  meta_graph_version_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tensorflow_version_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tensorflow_git_version_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&stripped_op_list_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&stripped_default_attrs_) -
      reinterpret_cast<char*>(&stripped_op_list_)) + sizeof(stripped_default_attrs_));
}

MetaGraphDef_MetaInfoDef::~MetaGraphDef_MetaInfoDef() {
  // @@protoc_insertion_point(destructor:tensorflow.MetaGraphDef.MetaInfoDef)
  SharedDtor();
}

void MetaGraphDef_MetaInfoDef::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  meta_graph_version_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tensorflow_version_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tensorflow_git_version_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete stripped_op_list_;
  if (this != internal_default_instance()) delete any_info_;
}

void MetaGraphDef_MetaInfoDef::ArenaDtor(void* object) {
  MetaGraphDef_MetaInfoDef* _this = reinterpret_cast< MetaGraphDef_MetaInfoDef* >(object);
  (void)_this;
}
void MetaGraphDef_MetaInfoDef::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void MetaGraphDef_MetaInfoDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* MetaGraphDef_MetaInfoDef::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const MetaGraphDef_MetaInfoDef& MetaGraphDef_MetaInfoDef::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_MetaGraphDef_MetaInfoDef.base);
  return *internal_default_instance();
}


void MetaGraphDef_MetaInfoDef::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.MetaGraphDef.MetaInfoDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  tags_.Clear();
  meta_graph_version_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  tensorflow_version_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  tensorflow_git_version_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && stripped_op_list_ != NULL) {
    delete stripped_op_list_;
  }
  stripped_op_list_ = NULL;
  if (GetArenaNoVirtual() == NULL && any_info_ != NULL) {
    delete any_info_;
  }
  any_info_ = NULL;
  stripped_default_attrs_ = false;
  _internal_metadata_.Clear();
}

bool MetaGraphDef_MetaInfoDef::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.MetaGraphDef.MetaInfoDef)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string meta_graph_version = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_meta_graph_version()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->meta_graph_version().data(), static_cast<int>(this->meta_graph_version().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.MetaGraphDef.MetaInfoDef.meta_graph_version"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.OpList stripped_op_list = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_stripped_op_list()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .google.protobuf.Any any_info = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_any_info()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string tags = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_tags()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tags(this->tags_size() - 1).data(),
            static_cast<int>(this->tags(this->tags_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.MetaGraphDef.MetaInfoDef.tags"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string tensorflow_version = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tensorflow_version()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tensorflow_version().data(), static_cast<int>(this->tensorflow_version().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_version"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string tensorflow_git_version = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tensorflow_git_version()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tensorflow_git_version().data(), static_cast<int>(this->tensorflow_git_version().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_git_version"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool stripped_default_attrs = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(56u /* 56 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &stripped_default_attrs_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.MetaGraphDef.MetaInfoDef)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.MetaGraphDef.MetaInfoDef)
  return false;
#undef DO_
}

void MetaGraphDef_MetaInfoDef::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.MetaGraphDef.MetaInfoDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string meta_graph_version = 1;
  if (this->meta_graph_version().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->meta_graph_version().data(), static_cast<int>(this->meta_graph_version().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MetaGraphDef.MetaInfoDef.meta_graph_version");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->meta_graph_version(), output);
  }

  // .tensorflow.OpList stripped_op_list = 2;
  if (this->has_stripped_op_list()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_stripped_op_list(), output);
  }

  // .google.protobuf.Any any_info = 3;
  if (this->has_any_info()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->_internal_any_info(), output);
  }

  // repeated string tags = 4;
  for (int i = 0, n = this->tags_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tags(i).data(), static_cast<int>(this->tags(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MetaGraphDef.MetaInfoDef.tags");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      4, this->tags(i), output);
  }

  // string tensorflow_version = 5;
  if (this->tensorflow_version().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tensorflow_version().data(), static_cast<int>(this->tensorflow_version().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_version");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->tensorflow_version(), output);
  }

  // string tensorflow_git_version = 6;
  if (this->tensorflow_git_version().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tensorflow_git_version().data(), static_cast<int>(this->tensorflow_git_version().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_git_version");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      6, this->tensorflow_git_version(), output);
  }

  // bool stripped_default_attrs = 7;
  if (this->stripped_default_attrs() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(7, this->stripped_default_attrs(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.MetaGraphDef.MetaInfoDef)
}

::google::protobuf::uint8* MetaGraphDef_MetaInfoDef::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.MetaGraphDef.MetaInfoDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string meta_graph_version = 1;
  if (this->meta_graph_version().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->meta_graph_version().data(), static_cast<int>(this->meta_graph_version().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MetaGraphDef.MetaInfoDef.meta_graph_version");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->meta_graph_version(), target);
  }

  // .tensorflow.OpList stripped_op_list = 2;
  if (this->has_stripped_op_list()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_stripped_op_list(), deterministic, target);
  }

  // .google.protobuf.Any any_info = 3;
  if (this->has_any_info()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->_internal_any_info(), deterministic, target);
  }

  // repeated string tags = 4;
  for (int i = 0, n = this->tags_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tags(i).data(), static_cast<int>(this->tags(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MetaGraphDef.MetaInfoDef.tags");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(4, this->tags(i), target);
  }

  // string tensorflow_version = 5;
  if (this->tensorflow_version().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tensorflow_version().data(), static_cast<int>(this->tensorflow_version().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_version");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->tensorflow_version(), target);
  }

  // string tensorflow_git_version = 6;
  if (this->tensorflow_git_version().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tensorflow_git_version().data(), static_cast<int>(this->tensorflow_git_version().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_git_version");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        6, this->tensorflow_git_version(), target);
  }

  // bool stripped_default_attrs = 7;
  if (this->stripped_default_attrs() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(7, this->stripped_default_attrs(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.MetaGraphDef.MetaInfoDef)
  return target;
}

size_t MetaGraphDef_MetaInfoDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.MetaGraphDef.MetaInfoDef)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated string tags = 4;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->tags_size());
  for (int i = 0, n = this->tags_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->tags(i));
  }

  // string meta_graph_version = 1;
  if (this->meta_graph_version().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->meta_graph_version());
  }

  // string tensorflow_version = 5;
  if (this->tensorflow_version().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tensorflow_version());
  }

  // string tensorflow_git_version = 6;
  if (this->tensorflow_git_version().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tensorflow_git_version());
  }

  // .tensorflow.OpList stripped_op_list = 2;
  if (this->has_stripped_op_list()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *stripped_op_list_);
  }

  // .google.protobuf.Any any_info = 3;
  if (this->has_any_info()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *any_info_);
  }

  // bool stripped_default_attrs = 7;
  if (this->stripped_default_attrs() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void MetaGraphDef_MetaInfoDef::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.MetaGraphDef.MetaInfoDef)
  GOOGLE_DCHECK_NE(&from, this);
  const MetaGraphDef_MetaInfoDef* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MetaGraphDef_MetaInfoDef>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.MetaGraphDef.MetaInfoDef)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.MetaGraphDef.MetaInfoDef)
    MergeFrom(*source);
  }
}

void MetaGraphDef_MetaInfoDef::MergeFrom(const MetaGraphDef_MetaInfoDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.MetaGraphDef.MetaInfoDef)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  tags_.MergeFrom(from.tags_);
  if (from.meta_graph_version().size() > 0) {
    set_meta_graph_version(from.meta_graph_version());
  }
  if (from.tensorflow_version().size() > 0) {
    set_tensorflow_version(from.tensorflow_version());
  }
  if (from.tensorflow_git_version().size() > 0) {
    set_tensorflow_git_version(from.tensorflow_git_version());
  }
  if (from.has_stripped_op_list()) {
    mutable_stripped_op_list()->::tensorflow::OpList::MergeFrom(from.stripped_op_list());
  }
  if (from.has_any_info()) {
    mutable_any_info()->::google::protobuf::Any::MergeFrom(from.any_info());
  }
  if (from.stripped_default_attrs() != 0) {
    set_stripped_default_attrs(from.stripped_default_attrs());
  }
}

void MetaGraphDef_MetaInfoDef::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.MetaGraphDef.MetaInfoDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MetaGraphDef_MetaInfoDef::CopyFrom(const MetaGraphDef_MetaInfoDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.MetaGraphDef.MetaInfoDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MetaGraphDef_MetaInfoDef::IsInitialized() const {
  return true;
}

void MetaGraphDef_MetaInfoDef::Swap(MetaGraphDef_MetaInfoDef* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    MetaGraphDef_MetaInfoDef* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void MetaGraphDef_MetaInfoDef::UnsafeArenaSwap(MetaGraphDef_MetaInfoDef* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void MetaGraphDef_MetaInfoDef::InternalSwap(MetaGraphDef_MetaInfoDef* other) {
  using std::swap;
  tags_.InternalSwap(CastToBase(&other->tags_));
  meta_graph_version_.Swap(&other->meta_graph_version_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  tensorflow_version_.Swap(&other->tensorflow_version_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  tensorflow_git_version_.Swap(&other->tensorflow_git_version_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(stripped_op_list_, other->stripped_op_list_);
  swap(any_info_, other->any_info_);
  swap(stripped_default_attrs_, other->stripped_default_attrs_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata MetaGraphDef_MetaInfoDef::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

MetaGraphDef_CollectionDefEntry_DoNotUse::MetaGraphDef_CollectionDefEntry_DoNotUse() {}
MetaGraphDef_CollectionDefEntry_DoNotUse::MetaGraphDef_CollectionDefEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void MetaGraphDef_CollectionDefEntry_DoNotUse::MergeFrom(const MetaGraphDef_CollectionDefEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata MetaGraphDef_CollectionDefEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::file_level_metadata[1];
}
void MetaGraphDef_CollectionDefEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

MetaGraphDef_SignatureDefEntry_DoNotUse::MetaGraphDef_SignatureDefEntry_DoNotUse() {}
MetaGraphDef_SignatureDefEntry_DoNotUse::MetaGraphDef_SignatureDefEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void MetaGraphDef_SignatureDefEntry_DoNotUse::MergeFrom(const MetaGraphDef_SignatureDefEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata MetaGraphDef_SignatureDefEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::file_level_metadata[2];
}
void MetaGraphDef_SignatureDefEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void MetaGraphDef::InitAsDefaultInstance() {
  ::tensorflow::_MetaGraphDef_default_instance_._instance.get_mutable()->meta_info_def_ = const_cast< ::tensorflow::MetaGraphDef_MetaInfoDef*>(
      ::tensorflow::MetaGraphDef_MetaInfoDef::internal_default_instance());
  ::tensorflow::_MetaGraphDef_default_instance_._instance.get_mutable()->graph_def_ = const_cast< ::tensorflow::GraphDef*>(
      ::tensorflow::GraphDef::internal_default_instance());
  ::tensorflow::_MetaGraphDef_default_instance_._instance.get_mutable()->saver_def_ = const_cast< ::tensorflow::SaverDef*>(
      ::tensorflow::SaverDef::internal_default_instance());
  ::tensorflow::_MetaGraphDef_default_instance_._instance.get_mutable()->object_graph_def_ = const_cast< ::tensorflow::SavedObjectGraph*>(
      ::tensorflow::SavedObjectGraph::internal_default_instance());
}
void MetaGraphDef::unsafe_arena_set_allocated_meta_info_def(
    ::tensorflow::MetaGraphDef_MetaInfoDef* meta_info_def) {
  if (GetArenaNoVirtual() == NULL) {
    delete meta_info_def_;
  }
  meta_info_def_ = meta_info_def;
  if (meta_info_def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MetaGraphDef.meta_info_def)
}
void MetaGraphDef::unsafe_arena_set_allocated_graph_def(
    ::tensorflow::GraphDef* graph_def) {
  if (GetArenaNoVirtual() == NULL) {
    delete graph_def_;
  }
  graph_def_ = graph_def;
  if (graph_def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MetaGraphDef.graph_def)
}
void MetaGraphDef::clear_graph_def() {
  if (GetArenaNoVirtual() == NULL && graph_def_ != NULL) {
    delete graph_def_;
  }
  graph_def_ = NULL;
}
void MetaGraphDef::unsafe_arena_set_allocated_saver_def(
    ::tensorflow::SaverDef* saver_def) {
  if (GetArenaNoVirtual() == NULL) {
    delete saver_def_;
  }
  saver_def_ = saver_def;
  if (saver_def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MetaGraphDef.saver_def)
}
void MetaGraphDef::clear_saver_def() {
  if (GetArenaNoVirtual() == NULL && saver_def_ != NULL) {
    delete saver_def_;
  }
  saver_def_ = NULL;
}
void MetaGraphDef::unsafe_arena_set_allocated_object_graph_def(
    ::tensorflow::SavedObjectGraph* object_graph_def) {
  if (GetArenaNoVirtual() == NULL) {
    delete object_graph_def_;
  }
  object_graph_def_ = object_graph_def;
  if (object_graph_def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MetaGraphDef.object_graph_def)
}
void MetaGraphDef::clear_object_graph_def() {
  if (GetArenaNoVirtual() == NULL && object_graph_def_ != NULL) {
    delete object_graph_def_;
  }
  object_graph_def_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MetaGraphDef::kMetaInfoDefFieldNumber;
const int MetaGraphDef::kGraphDefFieldNumber;
const int MetaGraphDef::kSaverDefFieldNumber;
const int MetaGraphDef::kCollectionDefFieldNumber;
const int MetaGraphDef::kSignatureDefFieldNumber;
const int MetaGraphDef::kAssetFileDefFieldNumber;
const int MetaGraphDef::kObjectGraphDefFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MetaGraphDef::MetaGraphDef()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_MetaGraphDef.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.MetaGraphDef)
}
MetaGraphDef::MetaGraphDef(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  collection_def_(arena),
  signature_def_(arena),
  asset_file_def_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_MetaGraphDef.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.MetaGraphDef)
}
MetaGraphDef::MetaGraphDef(const MetaGraphDef& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      asset_file_def_(from.asset_file_def_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  collection_def_.MergeFrom(from.collection_def_);
  signature_def_.MergeFrom(from.signature_def_);
  if (from.has_meta_info_def()) {
    meta_info_def_ = new ::tensorflow::MetaGraphDef_MetaInfoDef(*from.meta_info_def_);
  } else {
    meta_info_def_ = NULL;
  }
  if (from.has_graph_def()) {
    graph_def_ = new ::tensorflow::GraphDef(*from.graph_def_);
  } else {
    graph_def_ = NULL;
  }
  if (from.has_saver_def()) {
    saver_def_ = new ::tensorflow::SaverDef(*from.saver_def_);
  } else {
    saver_def_ = NULL;
  }
  if (from.has_object_graph_def()) {
    object_graph_def_ = new ::tensorflow::SavedObjectGraph(*from.object_graph_def_);
  } else {
    object_graph_def_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.MetaGraphDef)
}

void MetaGraphDef::SharedCtor() {
  ::memset(&meta_info_def_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&object_graph_def_) -
      reinterpret_cast<char*>(&meta_info_def_)) + sizeof(object_graph_def_));
}

MetaGraphDef::~MetaGraphDef() {
  // @@protoc_insertion_point(destructor:tensorflow.MetaGraphDef)
  SharedDtor();
}

void MetaGraphDef::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (this != internal_default_instance()) delete meta_info_def_;
  if (this != internal_default_instance()) delete graph_def_;
  if (this != internal_default_instance()) delete saver_def_;
  if (this != internal_default_instance()) delete object_graph_def_;
}

void MetaGraphDef::ArenaDtor(void* object) {
  MetaGraphDef* _this = reinterpret_cast< MetaGraphDef* >(object);
  (void)_this;
}
void MetaGraphDef::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void MetaGraphDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* MetaGraphDef::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const MetaGraphDef& MetaGraphDef::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_MetaGraphDef.base);
  return *internal_default_instance();
}


void MetaGraphDef::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.MetaGraphDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  collection_def_.Clear();
  signature_def_.Clear();
  asset_file_def_.Clear();
  if (GetArenaNoVirtual() == NULL && meta_info_def_ != NULL) {
    delete meta_info_def_;
  }
  meta_info_def_ = NULL;
  if (GetArenaNoVirtual() == NULL && graph_def_ != NULL) {
    delete graph_def_;
  }
  graph_def_ = NULL;
  if (GetArenaNoVirtual() == NULL && saver_def_ != NULL) {
    delete saver_def_;
  }
  saver_def_ = NULL;
  if (GetArenaNoVirtual() == NULL && object_graph_def_ != NULL) {
    delete object_graph_def_;
  }
  object_graph_def_ = NULL;
  _internal_metadata_.Clear();
}

bool MetaGraphDef::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.MetaGraphDef)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.MetaGraphDef.MetaInfoDef meta_info_def = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_meta_info_def()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.GraphDef graph_def = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_graph_def()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.SaverDef saver_def = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_saver_def()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, .tensorflow.CollectionDef> collection_def = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          MetaGraphDef_CollectionDefEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              MetaGraphDef_CollectionDefEntry_DoNotUse,
              ::std::string, ::tensorflow::CollectionDef,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::tensorflow::CollectionDef > > parser(&collection_def_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.MetaGraphDef.CollectionDefEntry.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, .tensorflow.SignatureDef> signature_def = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          MetaGraphDef_SignatureDefEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              MetaGraphDef_SignatureDefEntry_DoNotUse,
              ::std::string, ::tensorflow::SignatureDef,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::tensorflow::SignatureDef > > parser(&signature_def_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.MetaGraphDef.SignatureDefEntry.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.AssetFileDef asset_file_def = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_asset_file_def()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.SavedObjectGraph object_graph_def = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_object_graph_def()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.MetaGraphDef)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.MetaGraphDef)
  return false;
#undef DO_
}

void MetaGraphDef::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.MetaGraphDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.MetaGraphDef.MetaInfoDef meta_info_def = 1;
  if (this->has_meta_info_def()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->_internal_meta_info_def(), output);
  }

  // .tensorflow.GraphDef graph_def = 2;
  if (this->has_graph_def()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_graph_def(), output);
  }

  // .tensorflow.SaverDef saver_def = 3;
  if (this->has_saver_def()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->_internal_saver_def(), output);
  }

  // map<string, .tensorflow.CollectionDef> collection_def = 4;
  if (!this->collection_def().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::CollectionDef >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.MetaGraphDef.CollectionDefEntry.key");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->collection_def().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->collection_def().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::CollectionDef >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::CollectionDef >::const_iterator
          it = this->collection_def().begin();
          it != this->collection_def().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<MetaGraphDef_CollectionDefEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(collection_def_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            4, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<MetaGraphDef_CollectionDefEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::CollectionDef >::const_iterator
          it = this->collection_def().begin();
          it != this->collection_def().end(); ++it) {
        entry.reset(collection_def_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            4, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<string, .tensorflow.SignatureDef> signature_def = 5;
  if (!this->signature_def().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::SignatureDef >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.MetaGraphDef.SignatureDefEntry.key");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->signature_def().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->signature_def().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::SignatureDef >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::SignatureDef >::const_iterator
          it = this->signature_def().begin();
          it != this->signature_def().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<MetaGraphDef_SignatureDefEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(signature_def_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            5, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<MetaGraphDef_SignatureDefEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::SignatureDef >::const_iterator
          it = this->signature_def().begin();
          it != this->signature_def().end(); ++it) {
        entry.reset(signature_def_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            5, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // repeated .tensorflow.AssetFileDef asset_file_def = 6;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->asset_file_def_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6,
      this->asset_file_def(static_cast<int>(i)),
      output);
  }

  // .tensorflow.SavedObjectGraph object_graph_def = 7;
  if (this->has_object_graph_def()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7, this->_internal_object_graph_def(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.MetaGraphDef)
}

::google::protobuf::uint8* MetaGraphDef::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.MetaGraphDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.MetaGraphDef.MetaInfoDef meta_info_def = 1;
  if (this->has_meta_info_def()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->_internal_meta_info_def(), deterministic, target);
  }

  // .tensorflow.GraphDef graph_def = 2;
  if (this->has_graph_def()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_graph_def(), deterministic, target);
  }

  // .tensorflow.SaverDef saver_def = 3;
  if (this->has_saver_def()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->_internal_saver_def(), deterministic, target);
  }

  // map<string, .tensorflow.CollectionDef> collection_def = 4;
  if (!this->collection_def().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::CollectionDef >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.MetaGraphDef.CollectionDefEntry.key");
      }
    };

    if (deterministic &&
        this->collection_def().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->collection_def().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::CollectionDef >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::CollectionDef >::const_iterator
          it = this->collection_def().begin();
          it != this->collection_def().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<MetaGraphDef_CollectionDefEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(collection_def_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       4, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<MetaGraphDef_CollectionDefEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::CollectionDef >::const_iterator
          it = this->collection_def().begin();
          it != this->collection_def().end(); ++it) {
        entry.reset(collection_def_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       4, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<string, .tensorflow.SignatureDef> signature_def = 5;
  if (!this->signature_def().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::SignatureDef >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.MetaGraphDef.SignatureDefEntry.key");
      }
    };

    if (deterministic &&
        this->signature_def().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->signature_def().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::SignatureDef >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::SignatureDef >::const_iterator
          it = this->signature_def().begin();
          it != this->signature_def().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<MetaGraphDef_SignatureDefEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(signature_def_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       5, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<MetaGraphDef_SignatureDefEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::SignatureDef >::const_iterator
          it = this->signature_def().begin();
          it != this->signature_def().end(); ++it) {
        entry.reset(signature_def_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       5, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // repeated .tensorflow.AssetFileDef asset_file_def = 6;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->asset_file_def_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        6, this->asset_file_def(static_cast<int>(i)), deterministic, target);
  }

  // .tensorflow.SavedObjectGraph object_graph_def = 7;
  if (this->has_object_graph_def()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        7, this->_internal_object_graph_def(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.MetaGraphDef)
  return target;
}

size_t MetaGraphDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.MetaGraphDef)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // map<string, .tensorflow.CollectionDef> collection_def = 4;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->collection_def_size());
  {
    ::std::unique_ptr<MetaGraphDef_CollectionDefEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::tensorflow::CollectionDef >::const_iterator
        it = this->collection_def().begin();
        it != this->collection_def().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(collection_def_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<string, .tensorflow.SignatureDef> signature_def = 5;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->signature_def_size());
  {
    ::std::unique_ptr<MetaGraphDef_SignatureDefEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::tensorflow::SignatureDef >::const_iterator
        it = this->signature_def().begin();
        it != this->signature_def().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(signature_def_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // repeated .tensorflow.AssetFileDef asset_file_def = 6;
  {
    unsigned int count = static_cast<unsigned int>(this->asset_file_def_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->asset_file_def(static_cast<int>(i)));
    }
  }

  // .tensorflow.MetaGraphDef.MetaInfoDef meta_info_def = 1;
  if (this->has_meta_info_def()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *meta_info_def_);
  }

  // .tensorflow.GraphDef graph_def = 2;
  if (this->has_graph_def()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *graph_def_);
  }

  // .tensorflow.SaverDef saver_def = 3;
  if (this->has_saver_def()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *saver_def_);
  }

  // .tensorflow.SavedObjectGraph object_graph_def = 7;
  if (this->has_object_graph_def()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *object_graph_def_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void MetaGraphDef::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.MetaGraphDef)
  GOOGLE_DCHECK_NE(&from, this);
  const MetaGraphDef* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MetaGraphDef>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.MetaGraphDef)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.MetaGraphDef)
    MergeFrom(*source);
  }
}

void MetaGraphDef::MergeFrom(const MetaGraphDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.MetaGraphDef)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  collection_def_.MergeFrom(from.collection_def_);
  signature_def_.MergeFrom(from.signature_def_);
  asset_file_def_.MergeFrom(from.asset_file_def_);
  if (from.has_meta_info_def()) {
    mutable_meta_info_def()->::tensorflow::MetaGraphDef_MetaInfoDef::MergeFrom(from.meta_info_def());
  }
  if (from.has_graph_def()) {
    mutable_graph_def()->::tensorflow::GraphDef::MergeFrom(from.graph_def());
  }
  if (from.has_saver_def()) {
    mutable_saver_def()->::tensorflow::SaverDef::MergeFrom(from.saver_def());
  }
  if (from.has_object_graph_def()) {
    mutable_object_graph_def()->::tensorflow::SavedObjectGraph::MergeFrom(from.object_graph_def());
  }
}

void MetaGraphDef::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.MetaGraphDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MetaGraphDef::CopyFrom(const MetaGraphDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.MetaGraphDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MetaGraphDef::IsInitialized() const {
  return true;
}

void MetaGraphDef::Swap(MetaGraphDef* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    MetaGraphDef* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void MetaGraphDef::UnsafeArenaSwap(MetaGraphDef* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void MetaGraphDef::InternalSwap(MetaGraphDef* other) {
  using std::swap;
  collection_def_.Swap(&other->collection_def_);
  signature_def_.Swap(&other->signature_def_);
  CastToBase(&asset_file_def_)->InternalSwap(CastToBase(&other->asset_file_def_));
  swap(meta_info_def_, other->meta_info_def_);
  swap(graph_def_, other->graph_def_);
  swap(saver_def_, other->saver_def_);
  swap(object_graph_def_, other->object_graph_def_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata MetaGraphDef::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void CollectionDef_NodeList::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CollectionDef_NodeList::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CollectionDef_NodeList::CollectionDef_NodeList()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_CollectionDef_NodeList.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.CollectionDef.NodeList)
}
CollectionDef_NodeList::CollectionDef_NodeList(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  value_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_CollectionDef_NodeList.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.CollectionDef.NodeList)
}
CollectionDef_NodeList::CollectionDef_NodeList(const CollectionDef_NodeList& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      value_(from.value_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.CollectionDef.NodeList)
}

void CollectionDef_NodeList::SharedCtor() {
}

CollectionDef_NodeList::~CollectionDef_NodeList() {
  // @@protoc_insertion_point(destructor:tensorflow.CollectionDef.NodeList)
  SharedDtor();
}

void CollectionDef_NodeList::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void CollectionDef_NodeList::ArenaDtor(void* object) {
  CollectionDef_NodeList* _this = reinterpret_cast< CollectionDef_NodeList* >(object);
  (void)_this;
}
void CollectionDef_NodeList::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void CollectionDef_NodeList::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* CollectionDef_NodeList::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CollectionDef_NodeList& CollectionDef_NodeList::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_CollectionDef_NodeList.base);
  return *internal_default_instance();
}


void CollectionDef_NodeList::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.CollectionDef.NodeList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  value_.Clear();
  _internal_metadata_.Clear();
}

bool CollectionDef_NodeList::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.CollectionDef.NodeList)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated string value = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_value()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->value(this->value_size() - 1).data(),
            static_cast<int>(this->value(this->value_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.CollectionDef.NodeList.value"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.CollectionDef.NodeList)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.CollectionDef.NodeList)
  return false;
#undef DO_
}

void CollectionDef_NodeList::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.CollectionDef.NodeList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string value = 1;
  for (int i = 0, n = this->value_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->value(i).data(), static_cast<int>(this->value(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CollectionDef.NodeList.value");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->value(i), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.CollectionDef.NodeList)
}

::google::protobuf::uint8* CollectionDef_NodeList::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.CollectionDef.NodeList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string value = 1;
  for (int i = 0, n = this->value_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->value(i).data(), static_cast<int>(this->value(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CollectionDef.NodeList.value");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(1, this->value(i), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.CollectionDef.NodeList)
  return target;
}

size_t CollectionDef_NodeList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.CollectionDef.NodeList)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated string value = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->value_size());
  for (int i = 0, n = this->value_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->value(i));
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CollectionDef_NodeList::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.CollectionDef.NodeList)
  GOOGLE_DCHECK_NE(&from, this);
  const CollectionDef_NodeList* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CollectionDef_NodeList>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.CollectionDef.NodeList)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.CollectionDef.NodeList)
    MergeFrom(*source);
  }
}

void CollectionDef_NodeList::MergeFrom(const CollectionDef_NodeList& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.CollectionDef.NodeList)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  value_.MergeFrom(from.value_);
}

void CollectionDef_NodeList::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.CollectionDef.NodeList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CollectionDef_NodeList::CopyFrom(const CollectionDef_NodeList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.CollectionDef.NodeList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CollectionDef_NodeList::IsInitialized() const {
  return true;
}

void CollectionDef_NodeList::Swap(CollectionDef_NodeList* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    CollectionDef_NodeList* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void CollectionDef_NodeList::UnsafeArenaSwap(CollectionDef_NodeList* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void CollectionDef_NodeList::InternalSwap(CollectionDef_NodeList* other) {
  using std::swap;
  value_.InternalSwap(CastToBase(&other->value_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata CollectionDef_NodeList::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void CollectionDef_BytesList::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CollectionDef_BytesList::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CollectionDef_BytesList::CollectionDef_BytesList()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_CollectionDef_BytesList.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.CollectionDef.BytesList)
}
CollectionDef_BytesList::CollectionDef_BytesList(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  value_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_CollectionDef_BytesList.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.CollectionDef.BytesList)
}
CollectionDef_BytesList::CollectionDef_BytesList(const CollectionDef_BytesList& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      value_(from.value_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.CollectionDef.BytesList)
}

void CollectionDef_BytesList::SharedCtor() {
}

CollectionDef_BytesList::~CollectionDef_BytesList() {
  // @@protoc_insertion_point(destructor:tensorflow.CollectionDef.BytesList)
  SharedDtor();
}

void CollectionDef_BytesList::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void CollectionDef_BytesList::ArenaDtor(void* object) {
  CollectionDef_BytesList* _this = reinterpret_cast< CollectionDef_BytesList* >(object);
  (void)_this;
}
void CollectionDef_BytesList::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void CollectionDef_BytesList::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* CollectionDef_BytesList::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CollectionDef_BytesList& CollectionDef_BytesList::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_CollectionDef_BytesList.base);
  return *internal_default_instance();
}


void CollectionDef_BytesList::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.CollectionDef.BytesList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  value_.Clear();
  _internal_metadata_.Clear();
}

bool CollectionDef_BytesList::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.CollectionDef.BytesList)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated bytes value = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->add_value()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.CollectionDef.BytesList)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.CollectionDef.BytesList)
  return false;
#undef DO_
}

void CollectionDef_BytesList::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.CollectionDef.BytesList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated bytes value = 1;
  for (int i = 0, n = this->value_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      1, this->value(i), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.CollectionDef.BytesList)
}

::google::protobuf::uint8* CollectionDef_BytesList::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.CollectionDef.BytesList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated bytes value = 1;
  for (int i = 0, n = this->value_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteBytesToArray(1, this->value(i), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.CollectionDef.BytesList)
  return target;
}

size_t CollectionDef_BytesList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.CollectionDef.BytesList)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated bytes value = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->value_size());
  for (int i = 0, n = this->value_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::BytesSize(
      this->value(i));
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CollectionDef_BytesList::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.CollectionDef.BytesList)
  GOOGLE_DCHECK_NE(&from, this);
  const CollectionDef_BytesList* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CollectionDef_BytesList>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.CollectionDef.BytesList)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.CollectionDef.BytesList)
    MergeFrom(*source);
  }
}

void CollectionDef_BytesList::MergeFrom(const CollectionDef_BytesList& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.CollectionDef.BytesList)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  value_.MergeFrom(from.value_);
}

void CollectionDef_BytesList::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.CollectionDef.BytesList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CollectionDef_BytesList::CopyFrom(const CollectionDef_BytesList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.CollectionDef.BytesList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CollectionDef_BytesList::IsInitialized() const {
  return true;
}

void CollectionDef_BytesList::Swap(CollectionDef_BytesList* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    CollectionDef_BytesList* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void CollectionDef_BytesList::UnsafeArenaSwap(CollectionDef_BytesList* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void CollectionDef_BytesList::InternalSwap(CollectionDef_BytesList* other) {
  using std::swap;
  value_.InternalSwap(CastToBase(&other->value_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata CollectionDef_BytesList::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void CollectionDef_Int64List::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CollectionDef_Int64List::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CollectionDef_Int64List::CollectionDef_Int64List()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_CollectionDef_Int64List.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.CollectionDef.Int64List)
}
CollectionDef_Int64List::CollectionDef_Int64List(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  value_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_CollectionDef_Int64List.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.CollectionDef.Int64List)
}
CollectionDef_Int64List::CollectionDef_Int64List(const CollectionDef_Int64List& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      value_(from.value_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.CollectionDef.Int64List)
}

void CollectionDef_Int64List::SharedCtor() {
}

CollectionDef_Int64List::~CollectionDef_Int64List() {
  // @@protoc_insertion_point(destructor:tensorflow.CollectionDef.Int64List)
  SharedDtor();
}

void CollectionDef_Int64List::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void CollectionDef_Int64List::ArenaDtor(void* object) {
  CollectionDef_Int64List* _this = reinterpret_cast< CollectionDef_Int64List* >(object);
  (void)_this;
}
void CollectionDef_Int64List::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void CollectionDef_Int64List::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* CollectionDef_Int64List::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CollectionDef_Int64List& CollectionDef_Int64List::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_CollectionDef_Int64List.base);
  return *internal_default_instance();
}


void CollectionDef_Int64List::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.CollectionDef.Int64List)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  value_.Clear();
  _internal_metadata_.Clear();
}

bool CollectionDef_Int64List::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.CollectionDef.Int64List)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated int64 value = 1 [packed = true];
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_value())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 1, 10u, input, this->mutable_value())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.CollectionDef.Int64List)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.CollectionDef.Int64List)
  return false;
#undef DO_
}

void CollectionDef_Int64List::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.CollectionDef.Int64List)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated int64 value = 1 [packed = true];
  if (this->value_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(1, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _value_cached_byte_size_));
  }
  for (int i = 0, n = this->value_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->value(i), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.CollectionDef.Int64List)
}

::google::protobuf::uint8* CollectionDef_Int64List::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.CollectionDef.Int64List)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated int64 value = 1 [packed = true];
  if (this->value_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      1,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _value_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->value_, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.CollectionDef.Int64List)
  return target;
}

size_t CollectionDef_Int64List::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.CollectionDef.Int64List)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated int64 value = 1 [packed = true];
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int64Size(this->value_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _value_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CollectionDef_Int64List::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.CollectionDef.Int64List)
  GOOGLE_DCHECK_NE(&from, this);
  const CollectionDef_Int64List* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CollectionDef_Int64List>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.CollectionDef.Int64List)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.CollectionDef.Int64List)
    MergeFrom(*source);
  }
}

void CollectionDef_Int64List::MergeFrom(const CollectionDef_Int64List& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.CollectionDef.Int64List)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  value_.MergeFrom(from.value_);
}

void CollectionDef_Int64List::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.CollectionDef.Int64List)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CollectionDef_Int64List::CopyFrom(const CollectionDef_Int64List& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.CollectionDef.Int64List)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CollectionDef_Int64List::IsInitialized() const {
  return true;
}

void CollectionDef_Int64List::Swap(CollectionDef_Int64List* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    CollectionDef_Int64List* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void CollectionDef_Int64List::UnsafeArenaSwap(CollectionDef_Int64List* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void CollectionDef_Int64List::InternalSwap(CollectionDef_Int64List* other) {
  using std::swap;
  value_.InternalSwap(&other->value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata CollectionDef_Int64List::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void CollectionDef_FloatList::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CollectionDef_FloatList::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CollectionDef_FloatList::CollectionDef_FloatList()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_CollectionDef_FloatList.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.CollectionDef.FloatList)
}
CollectionDef_FloatList::CollectionDef_FloatList(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  value_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_CollectionDef_FloatList.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.CollectionDef.FloatList)
}
CollectionDef_FloatList::CollectionDef_FloatList(const CollectionDef_FloatList& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      value_(from.value_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.CollectionDef.FloatList)
}

void CollectionDef_FloatList::SharedCtor() {
}

CollectionDef_FloatList::~CollectionDef_FloatList() {
  // @@protoc_insertion_point(destructor:tensorflow.CollectionDef.FloatList)
  SharedDtor();
}

void CollectionDef_FloatList::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void CollectionDef_FloatList::ArenaDtor(void* object) {
  CollectionDef_FloatList* _this = reinterpret_cast< CollectionDef_FloatList* >(object);
  (void)_this;
}
void CollectionDef_FloatList::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void CollectionDef_FloatList::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* CollectionDef_FloatList::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CollectionDef_FloatList& CollectionDef_FloatList::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_CollectionDef_FloatList.base);
  return *internal_default_instance();
}


void CollectionDef_FloatList::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.CollectionDef.FloatList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  value_.Clear();
  _internal_metadata_.Clear();
}

bool CollectionDef_FloatList::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.CollectionDef.FloatList)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated float value = 1 [packed = true];
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, this->mutable_value())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(13u /* 13 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 1, 10u, input, this->mutable_value())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.CollectionDef.FloatList)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.CollectionDef.FloatList)
  return false;
#undef DO_
}

void CollectionDef_FloatList::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.CollectionDef.FloatList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated float value = 1 [packed = true];
  if (this->value_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(1, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _value_cached_byte_size_));
    ::google::protobuf::internal::WireFormatLite::WriteFloatArray(
      this->value().data(), this->value_size(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.CollectionDef.FloatList)
}

::google::protobuf::uint8* CollectionDef_FloatList::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.CollectionDef.FloatList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated float value = 1 [packed = true];
  if (this->value_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      1,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _value_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteFloatNoTagToArray(this->value_, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.CollectionDef.FloatList)
  return target;
}

size_t CollectionDef_FloatList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.CollectionDef.FloatList)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated float value = 1 [packed = true];
  {
    unsigned int count = static_cast<unsigned int>(this->value_size());
    size_t data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _value_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CollectionDef_FloatList::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.CollectionDef.FloatList)
  GOOGLE_DCHECK_NE(&from, this);
  const CollectionDef_FloatList* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CollectionDef_FloatList>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.CollectionDef.FloatList)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.CollectionDef.FloatList)
    MergeFrom(*source);
  }
}

void CollectionDef_FloatList::MergeFrom(const CollectionDef_FloatList& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.CollectionDef.FloatList)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  value_.MergeFrom(from.value_);
}

void CollectionDef_FloatList::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.CollectionDef.FloatList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CollectionDef_FloatList::CopyFrom(const CollectionDef_FloatList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.CollectionDef.FloatList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CollectionDef_FloatList::IsInitialized() const {
  return true;
}

void CollectionDef_FloatList::Swap(CollectionDef_FloatList* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    CollectionDef_FloatList* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void CollectionDef_FloatList::UnsafeArenaSwap(CollectionDef_FloatList* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void CollectionDef_FloatList::InternalSwap(CollectionDef_FloatList* other) {
  using std::swap;
  value_.InternalSwap(&other->value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata CollectionDef_FloatList::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void CollectionDef_AnyList::InitAsDefaultInstance() {
}
void CollectionDef_AnyList::clear_value() {
  value_.Clear();
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CollectionDef_AnyList::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CollectionDef_AnyList::CollectionDef_AnyList()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_CollectionDef_AnyList.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.CollectionDef.AnyList)
}
CollectionDef_AnyList::CollectionDef_AnyList(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  value_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_CollectionDef_AnyList.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.CollectionDef.AnyList)
}
CollectionDef_AnyList::CollectionDef_AnyList(const CollectionDef_AnyList& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      value_(from.value_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.CollectionDef.AnyList)
}

void CollectionDef_AnyList::SharedCtor() {
}

CollectionDef_AnyList::~CollectionDef_AnyList() {
  // @@protoc_insertion_point(destructor:tensorflow.CollectionDef.AnyList)
  SharedDtor();
}

void CollectionDef_AnyList::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void CollectionDef_AnyList::ArenaDtor(void* object) {
  CollectionDef_AnyList* _this = reinterpret_cast< CollectionDef_AnyList* >(object);
  (void)_this;
}
void CollectionDef_AnyList::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void CollectionDef_AnyList::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* CollectionDef_AnyList::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CollectionDef_AnyList& CollectionDef_AnyList::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_CollectionDef_AnyList.base);
  return *internal_default_instance();
}


void CollectionDef_AnyList::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.CollectionDef.AnyList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  value_.Clear();
  _internal_metadata_.Clear();
}

bool CollectionDef_AnyList::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.CollectionDef.AnyList)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .google.protobuf.Any value = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_value()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.CollectionDef.AnyList)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.CollectionDef.AnyList)
  return false;
#undef DO_
}

void CollectionDef_AnyList::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.CollectionDef.AnyList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .google.protobuf.Any value = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->value_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->value(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.CollectionDef.AnyList)
}

::google::protobuf::uint8* CollectionDef_AnyList::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.CollectionDef.AnyList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .google.protobuf.Any value = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->value_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->value(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.CollectionDef.AnyList)
  return target;
}

size_t CollectionDef_AnyList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.CollectionDef.AnyList)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .google.protobuf.Any value = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->value_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->value(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CollectionDef_AnyList::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.CollectionDef.AnyList)
  GOOGLE_DCHECK_NE(&from, this);
  const CollectionDef_AnyList* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CollectionDef_AnyList>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.CollectionDef.AnyList)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.CollectionDef.AnyList)
    MergeFrom(*source);
  }
}

void CollectionDef_AnyList::MergeFrom(const CollectionDef_AnyList& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.CollectionDef.AnyList)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  value_.MergeFrom(from.value_);
}

void CollectionDef_AnyList::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.CollectionDef.AnyList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CollectionDef_AnyList::CopyFrom(const CollectionDef_AnyList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.CollectionDef.AnyList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CollectionDef_AnyList::IsInitialized() const {
  return true;
}

void CollectionDef_AnyList::Swap(CollectionDef_AnyList* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    CollectionDef_AnyList* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void CollectionDef_AnyList::UnsafeArenaSwap(CollectionDef_AnyList* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void CollectionDef_AnyList::InternalSwap(CollectionDef_AnyList* other) {
  using std::swap;
  CastToBase(&value_)->InternalSwap(CastToBase(&other->value_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata CollectionDef_AnyList::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void CollectionDef::InitAsDefaultInstance() {
  ::tensorflow::_CollectionDef_default_instance_.node_list_ = const_cast< ::tensorflow::CollectionDef_NodeList*>(
      ::tensorflow::CollectionDef_NodeList::internal_default_instance());
  ::tensorflow::_CollectionDef_default_instance_.bytes_list_ = const_cast< ::tensorflow::CollectionDef_BytesList*>(
      ::tensorflow::CollectionDef_BytesList::internal_default_instance());
  ::tensorflow::_CollectionDef_default_instance_.int64_list_ = const_cast< ::tensorflow::CollectionDef_Int64List*>(
      ::tensorflow::CollectionDef_Int64List::internal_default_instance());
  ::tensorflow::_CollectionDef_default_instance_.float_list_ = const_cast< ::tensorflow::CollectionDef_FloatList*>(
      ::tensorflow::CollectionDef_FloatList::internal_default_instance());
  ::tensorflow::_CollectionDef_default_instance_.any_list_ = const_cast< ::tensorflow::CollectionDef_AnyList*>(
      ::tensorflow::CollectionDef_AnyList::internal_default_instance());
}
void CollectionDef::set_allocated_node_list(::tensorflow::CollectionDef_NodeList* node_list) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_kind();
  if (node_list) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(node_list);
    if (message_arena != submessage_arena) {
      node_list = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, node_list, submessage_arena);
    }
    set_has_node_list();
    kind_.node_list_ = node_list;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CollectionDef.node_list)
}
void CollectionDef::set_allocated_bytes_list(::tensorflow::CollectionDef_BytesList* bytes_list) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_kind();
  if (bytes_list) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(bytes_list);
    if (message_arena != submessage_arena) {
      bytes_list = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, bytes_list, submessage_arena);
    }
    set_has_bytes_list();
    kind_.bytes_list_ = bytes_list;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CollectionDef.bytes_list)
}
void CollectionDef::set_allocated_int64_list(::tensorflow::CollectionDef_Int64List* int64_list) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_kind();
  if (int64_list) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(int64_list);
    if (message_arena != submessage_arena) {
      int64_list = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, int64_list, submessage_arena);
    }
    set_has_int64_list();
    kind_.int64_list_ = int64_list;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CollectionDef.int64_list)
}
void CollectionDef::set_allocated_float_list(::tensorflow::CollectionDef_FloatList* float_list) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_kind();
  if (float_list) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(float_list);
    if (message_arena != submessage_arena) {
      float_list = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, float_list, submessage_arena);
    }
    set_has_float_list();
    kind_.float_list_ = float_list;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CollectionDef.float_list)
}
void CollectionDef::set_allocated_any_list(::tensorflow::CollectionDef_AnyList* any_list) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_kind();
  if (any_list) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(any_list);
    if (message_arena != submessage_arena) {
      any_list = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, any_list, submessage_arena);
    }
    set_has_any_list();
    kind_.any_list_ = any_list;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CollectionDef.any_list)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CollectionDef::kNodeListFieldNumber;
const int CollectionDef::kBytesListFieldNumber;
const int CollectionDef::kInt64ListFieldNumber;
const int CollectionDef::kFloatListFieldNumber;
const int CollectionDef::kAnyListFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CollectionDef::CollectionDef()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_CollectionDef.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.CollectionDef)
}
CollectionDef::CollectionDef(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_CollectionDef.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.CollectionDef)
}
CollectionDef::CollectionDef(const CollectionDef& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  clear_has_kind();
  switch (from.kind_case()) {
    case kNodeList: {
      mutable_node_list()->::tensorflow::CollectionDef_NodeList::MergeFrom(from.node_list());
      break;
    }
    case kBytesList: {
      mutable_bytes_list()->::tensorflow::CollectionDef_BytesList::MergeFrom(from.bytes_list());
      break;
    }
    case kInt64List: {
      mutable_int64_list()->::tensorflow::CollectionDef_Int64List::MergeFrom(from.int64_list());
      break;
    }
    case kFloatList: {
      mutable_float_list()->::tensorflow::CollectionDef_FloatList::MergeFrom(from.float_list());
      break;
    }
    case kAnyList: {
      mutable_any_list()->::tensorflow::CollectionDef_AnyList::MergeFrom(from.any_list());
      break;
    }
    case KIND_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.CollectionDef)
}

void CollectionDef::SharedCtor() {
  clear_has_kind();
}

CollectionDef::~CollectionDef() {
  // @@protoc_insertion_point(destructor:tensorflow.CollectionDef)
  SharedDtor();
}

void CollectionDef::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (has_kind()) {
    clear_kind();
  }
}

void CollectionDef::ArenaDtor(void* object) {
  CollectionDef* _this = reinterpret_cast< CollectionDef* >(object);
  (void)_this;
}
void CollectionDef::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void CollectionDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* CollectionDef::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CollectionDef& CollectionDef::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_CollectionDef.base);
  return *internal_default_instance();
}


void CollectionDef::clear_kind() {
// @@protoc_insertion_point(one_of_clear_start:tensorflow.CollectionDef)
  switch (kind_case()) {
    case kNodeList: {
      if (GetArenaNoVirtual() == NULL) {
        delete kind_.node_list_;
      }
      break;
    }
    case kBytesList: {
      if (GetArenaNoVirtual() == NULL) {
        delete kind_.bytes_list_;
      }
      break;
    }
    case kInt64List: {
      if (GetArenaNoVirtual() == NULL) {
        delete kind_.int64_list_;
      }
      break;
    }
    case kFloatList: {
      if (GetArenaNoVirtual() == NULL) {
        delete kind_.float_list_;
      }
      break;
    }
    case kAnyList: {
      if (GetArenaNoVirtual() == NULL) {
        delete kind_.any_list_;
      }
      break;
    }
    case KIND_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = KIND_NOT_SET;
}


void CollectionDef::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.CollectionDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_kind();
  _internal_metadata_.Clear();
}

bool CollectionDef::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.CollectionDef)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.CollectionDef.NodeList node_list = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_node_list()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.CollectionDef.BytesList bytes_list = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_bytes_list()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.CollectionDef.Int64List int64_list = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_int64_list()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.CollectionDef.FloatList float_list = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_float_list()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.CollectionDef.AnyList any_list = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_any_list()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.CollectionDef)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.CollectionDef)
  return false;
#undef DO_
}

void CollectionDef::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.CollectionDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.CollectionDef.NodeList node_list = 1;
  if (has_node_list()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->_internal_node_list(), output);
  }

  // .tensorflow.CollectionDef.BytesList bytes_list = 2;
  if (has_bytes_list()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_bytes_list(), output);
  }

  // .tensorflow.CollectionDef.Int64List int64_list = 3;
  if (has_int64_list()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->_internal_int64_list(), output);
  }

  // .tensorflow.CollectionDef.FloatList float_list = 4;
  if (has_float_list()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->_internal_float_list(), output);
  }

  // .tensorflow.CollectionDef.AnyList any_list = 5;
  if (has_any_list()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, this->_internal_any_list(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.CollectionDef)
}

::google::protobuf::uint8* CollectionDef::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.CollectionDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.CollectionDef.NodeList node_list = 1;
  if (has_node_list()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->_internal_node_list(), deterministic, target);
  }

  // .tensorflow.CollectionDef.BytesList bytes_list = 2;
  if (has_bytes_list()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_bytes_list(), deterministic, target);
  }

  // .tensorflow.CollectionDef.Int64List int64_list = 3;
  if (has_int64_list()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->_internal_int64_list(), deterministic, target);
  }

  // .tensorflow.CollectionDef.FloatList float_list = 4;
  if (has_float_list()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->_internal_float_list(), deterministic, target);
  }

  // .tensorflow.CollectionDef.AnyList any_list = 5;
  if (has_any_list()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        5, this->_internal_any_list(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.CollectionDef)
  return target;
}

size_t CollectionDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.CollectionDef)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  switch (kind_case()) {
    // .tensorflow.CollectionDef.NodeList node_list = 1;
    case kNodeList: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *kind_.node_list_);
      break;
    }
    // .tensorflow.CollectionDef.BytesList bytes_list = 2;
    case kBytesList: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *kind_.bytes_list_);
      break;
    }
    // .tensorflow.CollectionDef.Int64List int64_list = 3;
    case kInt64List: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *kind_.int64_list_);
      break;
    }
    // .tensorflow.CollectionDef.FloatList float_list = 4;
    case kFloatList: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *kind_.float_list_);
      break;
    }
    // .tensorflow.CollectionDef.AnyList any_list = 5;
    case kAnyList: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *kind_.any_list_);
      break;
    }
    case KIND_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CollectionDef::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.CollectionDef)
  GOOGLE_DCHECK_NE(&from, this);
  const CollectionDef* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CollectionDef>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.CollectionDef)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.CollectionDef)
    MergeFrom(*source);
  }
}

void CollectionDef::MergeFrom(const CollectionDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.CollectionDef)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.kind_case()) {
    case kNodeList: {
      mutable_node_list()->::tensorflow::CollectionDef_NodeList::MergeFrom(from.node_list());
      break;
    }
    case kBytesList: {
      mutable_bytes_list()->::tensorflow::CollectionDef_BytesList::MergeFrom(from.bytes_list());
      break;
    }
    case kInt64List: {
      mutable_int64_list()->::tensorflow::CollectionDef_Int64List::MergeFrom(from.int64_list());
      break;
    }
    case kFloatList: {
      mutable_float_list()->::tensorflow::CollectionDef_FloatList::MergeFrom(from.float_list());
      break;
    }
    case kAnyList: {
      mutable_any_list()->::tensorflow::CollectionDef_AnyList::MergeFrom(from.any_list());
      break;
    }
    case KIND_NOT_SET: {
      break;
    }
  }
}

void CollectionDef::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.CollectionDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CollectionDef::CopyFrom(const CollectionDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.CollectionDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CollectionDef::IsInitialized() const {
  return true;
}

void CollectionDef::Swap(CollectionDef* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    CollectionDef* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void CollectionDef::UnsafeArenaSwap(CollectionDef* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void CollectionDef::InternalSwap(CollectionDef* other) {
  using std::swap;
  swap(kind_, other->kind_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata CollectionDef::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void TensorInfo_CooSparse::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TensorInfo_CooSparse::kValuesTensorNameFieldNumber;
const int TensorInfo_CooSparse::kIndicesTensorNameFieldNumber;
const int TensorInfo_CooSparse::kDenseShapeTensorNameFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TensorInfo_CooSparse::TensorInfo_CooSparse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_TensorInfo_CooSparse.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.TensorInfo.CooSparse)
}
TensorInfo_CooSparse::TensorInfo_CooSparse(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_TensorInfo_CooSparse.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.TensorInfo.CooSparse)
}
TensorInfo_CooSparse::TensorInfo_CooSparse(const TensorInfo_CooSparse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  values_tensor_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.values_tensor_name().size() > 0) {
    values_tensor_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.values_tensor_name(),
      GetArenaNoVirtual());
  }
  indices_tensor_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.indices_tensor_name().size() > 0) {
    indices_tensor_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.indices_tensor_name(),
      GetArenaNoVirtual());
  }
  dense_shape_tensor_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.dense_shape_tensor_name().size() > 0) {
    dense_shape_tensor_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.dense_shape_tensor_name(),
      GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.TensorInfo.CooSparse)
}

void TensorInfo_CooSparse::SharedCtor() {
  values_tensor_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  indices_tensor_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dense_shape_tensor_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

TensorInfo_CooSparse::~TensorInfo_CooSparse() {
  // @@protoc_insertion_point(destructor:tensorflow.TensorInfo.CooSparse)
  SharedDtor();
}

void TensorInfo_CooSparse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  values_tensor_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  indices_tensor_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dense_shape_tensor_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void TensorInfo_CooSparse::ArenaDtor(void* object) {
  TensorInfo_CooSparse* _this = reinterpret_cast< TensorInfo_CooSparse* >(object);
  (void)_this;
}
void TensorInfo_CooSparse::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TensorInfo_CooSparse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* TensorInfo_CooSparse::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const TensorInfo_CooSparse& TensorInfo_CooSparse::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_TensorInfo_CooSparse.base);
  return *internal_default_instance();
}


void TensorInfo_CooSparse::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.TensorInfo.CooSparse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  values_tensor_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  indices_tensor_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  dense_shape_tensor_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  _internal_metadata_.Clear();
}

bool TensorInfo_CooSparse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.TensorInfo.CooSparse)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string values_tensor_name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_values_tensor_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->values_tensor_name().data(), static_cast<int>(this->values_tensor_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.TensorInfo.CooSparse.values_tensor_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string indices_tensor_name = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_indices_tensor_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->indices_tensor_name().data(), static_cast<int>(this->indices_tensor_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.TensorInfo.CooSparse.indices_tensor_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string dense_shape_tensor_name = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_dense_shape_tensor_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->dense_shape_tensor_name().data(), static_cast<int>(this->dense_shape_tensor_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.TensorInfo.CooSparse.dense_shape_tensor_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.TensorInfo.CooSparse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.TensorInfo.CooSparse)
  return false;
#undef DO_
}

void TensorInfo_CooSparse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.TensorInfo.CooSparse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string values_tensor_name = 1;
  if (this->values_tensor_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->values_tensor_name().data(), static_cast<int>(this->values_tensor_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TensorInfo.CooSparse.values_tensor_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->values_tensor_name(), output);
  }

  // string indices_tensor_name = 2;
  if (this->indices_tensor_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->indices_tensor_name().data(), static_cast<int>(this->indices_tensor_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TensorInfo.CooSparse.indices_tensor_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->indices_tensor_name(), output);
  }

  // string dense_shape_tensor_name = 3;
  if (this->dense_shape_tensor_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->dense_shape_tensor_name().data(), static_cast<int>(this->dense_shape_tensor_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TensorInfo.CooSparse.dense_shape_tensor_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->dense_shape_tensor_name(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.TensorInfo.CooSparse)
}

::google::protobuf::uint8* TensorInfo_CooSparse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.TensorInfo.CooSparse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string values_tensor_name = 1;
  if (this->values_tensor_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->values_tensor_name().data(), static_cast<int>(this->values_tensor_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TensorInfo.CooSparse.values_tensor_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->values_tensor_name(), target);
  }

  // string indices_tensor_name = 2;
  if (this->indices_tensor_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->indices_tensor_name().data(), static_cast<int>(this->indices_tensor_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TensorInfo.CooSparse.indices_tensor_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->indices_tensor_name(), target);
  }

  // string dense_shape_tensor_name = 3;
  if (this->dense_shape_tensor_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->dense_shape_tensor_name().data(), static_cast<int>(this->dense_shape_tensor_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TensorInfo.CooSparse.dense_shape_tensor_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->dense_shape_tensor_name(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.TensorInfo.CooSparse)
  return target;
}

size_t TensorInfo_CooSparse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.TensorInfo.CooSparse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string values_tensor_name = 1;
  if (this->values_tensor_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->values_tensor_name());
  }

  // string indices_tensor_name = 2;
  if (this->indices_tensor_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->indices_tensor_name());
  }

  // string dense_shape_tensor_name = 3;
  if (this->dense_shape_tensor_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->dense_shape_tensor_name());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TensorInfo_CooSparse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.TensorInfo.CooSparse)
  GOOGLE_DCHECK_NE(&from, this);
  const TensorInfo_CooSparse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TensorInfo_CooSparse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.TensorInfo.CooSparse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.TensorInfo.CooSparse)
    MergeFrom(*source);
  }
}

void TensorInfo_CooSparse::MergeFrom(const TensorInfo_CooSparse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.TensorInfo.CooSparse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.values_tensor_name().size() > 0) {
    set_values_tensor_name(from.values_tensor_name());
  }
  if (from.indices_tensor_name().size() > 0) {
    set_indices_tensor_name(from.indices_tensor_name());
  }
  if (from.dense_shape_tensor_name().size() > 0) {
    set_dense_shape_tensor_name(from.dense_shape_tensor_name());
  }
}

void TensorInfo_CooSparse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.TensorInfo.CooSparse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TensorInfo_CooSparse::CopyFrom(const TensorInfo_CooSparse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.TensorInfo.CooSparse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TensorInfo_CooSparse::IsInitialized() const {
  return true;
}

void TensorInfo_CooSparse::Swap(TensorInfo_CooSparse* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TensorInfo_CooSparse* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void TensorInfo_CooSparse::UnsafeArenaSwap(TensorInfo_CooSparse* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TensorInfo_CooSparse::InternalSwap(TensorInfo_CooSparse* other) {
  using std::swap;
  values_tensor_name_.Swap(&other->values_tensor_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  indices_tensor_name_.Swap(&other->indices_tensor_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  dense_shape_tensor_name_.Swap(&other->dense_shape_tensor_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata TensorInfo_CooSparse::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void TensorInfo::InitAsDefaultInstance() {
  ::tensorflow::_TensorInfo_default_instance_.name_.UnsafeSetDefault(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::tensorflow::_TensorInfo_default_instance_.coo_sparse_ = const_cast< ::tensorflow::TensorInfo_CooSparse*>(
      ::tensorflow::TensorInfo_CooSparse::internal_default_instance());
  ::tensorflow::_TensorInfo_default_instance_._instance.get_mutable()->tensor_shape_ = const_cast< ::tensorflow::TensorShapeProto*>(
      ::tensorflow::TensorShapeProto::internal_default_instance());
}
void TensorInfo::set_allocated_coo_sparse(::tensorflow::TensorInfo_CooSparse* coo_sparse) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_encoding();
  if (coo_sparse) {
    ::google::protobuf::Arena* submessage_arena =
      ::google::protobuf::Arena::GetArena(coo_sparse);
    if (message_arena != submessage_arena) {
      coo_sparse = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, coo_sparse, submessage_arena);
    }
    set_has_coo_sparse();
    encoding_.coo_sparse_ = coo_sparse;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TensorInfo.coo_sparse)
}
void TensorInfo::unsafe_arena_set_allocated_tensor_shape(
    ::tensorflow::TensorShapeProto* tensor_shape) {
  if (GetArenaNoVirtual() == NULL) {
    delete tensor_shape_;
  }
  tensor_shape_ = tensor_shape;
  if (tensor_shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TensorInfo.tensor_shape)
}
void TensorInfo::clear_tensor_shape() {
  if (GetArenaNoVirtual() == NULL && tensor_shape_ != NULL) {
    delete tensor_shape_;
  }
  tensor_shape_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TensorInfo::kNameFieldNumber;
const int TensorInfo::kCooSparseFieldNumber;
const int TensorInfo::kDtypeFieldNumber;
const int TensorInfo::kTensorShapeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TensorInfo::TensorInfo()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_TensorInfo.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.TensorInfo)
}
TensorInfo::TensorInfo(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_TensorInfo.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.TensorInfo)
}
TensorInfo::TensorInfo(const TensorInfo& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_tensor_shape()) {
    tensor_shape_ = new ::tensorflow::TensorShapeProto(*from.tensor_shape_);
  } else {
    tensor_shape_ = NULL;
  }
  dtype_ = from.dtype_;
  clear_has_encoding();
  switch (from.encoding_case()) {
    case kName: {
      set_name(from.name());
      break;
    }
    case kCooSparse: {
      mutable_coo_sparse()->::tensorflow::TensorInfo_CooSparse::MergeFrom(from.coo_sparse());
      break;
    }
    case ENCODING_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.TensorInfo)
}

void TensorInfo::SharedCtor() {
  ::memset(&tensor_shape_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&dtype_) -
      reinterpret_cast<char*>(&tensor_shape_)) + sizeof(dtype_));
  clear_has_encoding();
}

TensorInfo::~TensorInfo() {
  // @@protoc_insertion_point(destructor:tensorflow.TensorInfo)
  SharedDtor();
}

void TensorInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (this != internal_default_instance()) delete tensor_shape_;
  if (has_encoding()) {
    clear_encoding();
  }
}

void TensorInfo::ArenaDtor(void* object) {
  TensorInfo* _this = reinterpret_cast< TensorInfo* >(object);
  (void)_this;
}
void TensorInfo::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TensorInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* TensorInfo::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const TensorInfo& TensorInfo::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_TensorInfo.base);
  return *internal_default_instance();
}


void TensorInfo::clear_encoding() {
// @@protoc_insertion_point(one_of_clear_start:tensorflow.TensorInfo)
  switch (encoding_case()) {
    case kName: {
      encoding_.name_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
          GetArenaNoVirtual());
      break;
    }
    case kCooSparse: {
      if (GetArenaNoVirtual() == NULL) {
        delete encoding_.coo_sparse_;
      }
      break;
    }
    case ENCODING_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = ENCODING_NOT_SET;
}


void TensorInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.TensorInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && tensor_shape_ != NULL) {
    delete tensor_shape_;
  }
  tensor_shape_ = NULL;
  dtype_ = 0;
  clear_encoding();
  _internal_metadata_.Clear();
}

bool TensorInfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.TensorInfo)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.TensorInfo.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.DataType dtype = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_dtype(static_cast< ::tensorflow::DataType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorShapeProto tensor_shape = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_tensor_shape()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.TensorInfo.CooSparse coo_sparse = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_coo_sparse()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.TensorInfo)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.TensorInfo)
  return false;
#undef DO_
}

void TensorInfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.TensorInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (has_name()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TensorInfo.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // .tensorflow.DataType dtype = 2;
  if (this->dtype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      2, this->dtype(), output);
  }

  // .tensorflow.TensorShapeProto tensor_shape = 3;
  if (this->has_tensor_shape()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->_internal_tensor_shape(), output);
  }

  // .tensorflow.TensorInfo.CooSparse coo_sparse = 4;
  if (has_coo_sparse()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->_internal_coo_sparse(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.TensorInfo)
}

::google::protobuf::uint8* TensorInfo::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.TensorInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (has_name()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TensorInfo.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // .tensorflow.DataType dtype = 2;
  if (this->dtype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      2, this->dtype(), target);
  }

  // .tensorflow.TensorShapeProto tensor_shape = 3;
  if (this->has_tensor_shape()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->_internal_tensor_shape(), deterministic, target);
  }

  // .tensorflow.TensorInfo.CooSparse coo_sparse = 4;
  if (has_coo_sparse()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->_internal_coo_sparse(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.TensorInfo)
  return target;
}

size_t TensorInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.TensorInfo)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .tensorflow.TensorShapeProto tensor_shape = 3;
  if (this->has_tensor_shape()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *tensor_shape_);
  }

  // .tensorflow.DataType dtype = 2;
  if (this->dtype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->dtype());
  }

  switch (encoding_case()) {
    // string name = 1;
    case kName: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->name());
      break;
    }
    // .tensorflow.TensorInfo.CooSparse coo_sparse = 4;
    case kCooSparse: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *encoding_.coo_sparse_);
      break;
    }
    case ENCODING_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TensorInfo::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.TensorInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const TensorInfo* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TensorInfo>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.TensorInfo)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.TensorInfo)
    MergeFrom(*source);
  }
}

void TensorInfo::MergeFrom(const TensorInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.TensorInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_tensor_shape()) {
    mutable_tensor_shape()->::tensorflow::TensorShapeProto::MergeFrom(from.tensor_shape());
  }
  if (from.dtype() != 0) {
    set_dtype(from.dtype());
  }
  switch (from.encoding_case()) {
    case kName: {
      set_name(from.name());
      break;
    }
    case kCooSparse: {
      mutable_coo_sparse()->::tensorflow::TensorInfo_CooSparse::MergeFrom(from.coo_sparse());
      break;
    }
    case ENCODING_NOT_SET: {
      break;
    }
  }
}

void TensorInfo::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.TensorInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TensorInfo::CopyFrom(const TensorInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.TensorInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TensorInfo::IsInitialized() const {
  return true;
}

void TensorInfo::Swap(TensorInfo* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TensorInfo* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void TensorInfo::UnsafeArenaSwap(TensorInfo* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TensorInfo::InternalSwap(TensorInfo* other) {
  using std::swap;
  swap(tensor_shape_, other->tensor_shape_);
  swap(dtype_, other->dtype_);
  swap(encoding_, other->encoding_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata TensorInfo::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

SignatureDef_InputsEntry_DoNotUse::SignatureDef_InputsEntry_DoNotUse() {}
SignatureDef_InputsEntry_DoNotUse::SignatureDef_InputsEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void SignatureDef_InputsEntry_DoNotUse::MergeFrom(const SignatureDef_InputsEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata SignatureDef_InputsEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::file_level_metadata[12];
}
void SignatureDef_InputsEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

SignatureDef_OutputsEntry_DoNotUse::SignatureDef_OutputsEntry_DoNotUse() {}
SignatureDef_OutputsEntry_DoNotUse::SignatureDef_OutputsEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void SignatureDef_OutputsEntry_DoNotUse::MergeFrom(const SignatureDef_OutputsEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata SignatureDef_OutputsEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::file_level_metadata[13];
}
void SignatureDef_OutputsEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void SignatureDef::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SignatureDef::kInputsFieldNumber;
const int SignatureDef::kOutputsFieldNumber;
const int SignatureDef::kMethodNameFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SignatureDef::SignatureDef()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_SignatureDef.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.SignatureDef)
}
SignatureDef::SignatureDef(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  inputs_(arena),
  outputs_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_SignatureDef.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.SignatureDef)
}
SignatureDef::SignatureDef(const SignatureDef& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  inputs_.MergeFrom(from.inputs_);
  outputs_.MergeFrom(from.outputs_);
  method_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.method_name().size() > 0) {
    method_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.method_name(),
      GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.SignatureDef)
}

void SignatureDef::SharedCtor() {
  method_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

SignatureDef::~SignatureDef() {
  // @@protoc_insertion_point(destructor:tensorflow.SignatureDef)
  SharedDtor();
}

void SignatureDef::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  method_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void SignatureDef::ArenaDtor(void* object) {
  SignatureDef* _this = reinterpret_cast< SignatureDef* >(object);
  (void)_this;
}
void SignatureDef::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void SignatureDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* SignatureDef::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const SignatureDef& SignatureDef::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_SignatureDef.base);
  return *internal_default_instance();
}


void SignatureDef::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.SignatureDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  inputs_.Clear();
  outputs_.Clear();
  method_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  _internal_metadata_.Clear();
}

bool SignatureDef::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.SignatureDef)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<string, .tensorflow.TensorInfo> inputs = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          SignatureDef_InputsEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              SignatureDef_InputsEntry_DoNotUse,
              ::std::string, ::tensorflow::TensorInfo,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::tensorflow::TensorInfo > > parser(&inputs_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.SignatureDef.InputsEntry.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, .tensorflow.TensorInfo> outputs = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          SignatureDef_OutputsEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              SignatureDef_OutputsEntry_DoNotUse,
              ::std::string, ::tensorflow::TensorInfo,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::tensorflow::TensorInfo > > parser(&outputs_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.SignatureDef.OutputsEntry.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string method_name = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_method_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->method_name().data(), static_cast<int>(this->method_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.SignatureDef.method_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.SignatureDef)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.SignatureDef)
  return false;
#undef DO_
}

void SignatureDef::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.SignatureDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, .tensorflow.TensorInfo> inputs = 1;
  if (!this->inputs().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::TensorInfo >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.SignatureDef.InputsEntry.key");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->inputs().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->inputs().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::TensorInfo >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::TensorInfo >::const_iterator
          it = this->inputs().begin();
          it != this->inputs().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<SignatureDef_InputsEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(inputs_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<SignatureDef_InputsEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::TensorInfo >::const_iterator
          it = this->inputs().begin();
          it != this->inputs().end(); ++it) {
        entry.reset(inputs_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<string, .tensorflow.TensorInfo> outputs = 2;
  if (!this->outputs().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::TensorInfo >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.SignatureDef.OutputsEntry.key");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->outputs().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->outputs().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::TensorInfo >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::TensorInfo >::const_iterator
          it = this->outputs().begin();
          it != this->outputs().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<SignatureDef_OutputsEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(outputs_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<SignatureDef_OutputsEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::TensorInfo >::const_iterator
          it = this->outputs().begin();
          it != this->outputs().end(); ++it) {
        entry.reset(outputs_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // string method_name = 3;
  if (this->method_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->method_name().data(), static_cast<int>(this->method_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SignatureDef.method_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->method_name(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.SignatureDef)
}

::google::protobuf::uint8* SignatureDef::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.SignatureDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, .tensorflow.TensorInfo> inputs = 1;
  if (!this->inputs().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::TensorInfo >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.SignatureDef.InputsEntry.key");
      }
    };

    if (deterministic &&
        this->inputs().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->inputs().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::TensorInfo >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::TensorInfo >::const_iterator
          it = this->inputs().begin();
          it != this->inputs().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<SignatureDef_InputsEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(inputs_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<SignatureDef_InputsEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::TensorInfo >::const_iterator
          it = this->inputs().begin();
          it != this->inputs().end(); ++it) {
        entry.reset(inputs_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<string, .tensorflow.TensorInfo> outputs = 2;
  if (!this->outputs().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::TensorInfo >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.SignatureDef.OutputsEntry.key");
      }
    };

    if (deterministic &&
        this->outputs().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->outputs().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::TensorInfo >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::TensorInfo >::const_iterator
          it = this->outputs().begin();
          it != this->outputs().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<SignatureDef_OutputsEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(outputs_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<SignatureDef_OutputsEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::TensorInfo >::const_iterator
          it = this->outputs().begin();
          it != this->outputs().end(); ++it) {
        entry.reset(outputs_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // string method_name = 3;
  if (this->method_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->method_name().data(), static_cast<int>(this->method_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.SignatureDef.method_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->method_name(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.SignatureDef)
  return target;
}

size_t SignatureDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.SignatureDef)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // map<string, .tensorflow.TensorInfo> inputs = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->inputs_size());
  {
    ::std::unique_ptr<SignatureDef_InputsEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::tensorflow::TensorInfo >::const_iterator
        it = this->inputs().begin();
        it != this->inputs().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(inputs_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<string, .tensorflow.TensorInfo> outputs = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->outputs_size());
  {
    ::std::unique_ptr<SignatureDef_OutputsEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::tensorflow::TensorInfo >::const_iterator
        it = this->outputs().begin();
        it != this->outputs().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(outputs_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // string method_name = 3;
  if (this->method_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->method_name());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SignatureDef::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.SignatureDef)
  GOOGLE_DCHECK_NE(&from, this);
  const SignatureDef* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SignatureDef>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.SignatureDef)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.SignatureDef)
    MergeFrom(*source);
  }
}

void SignatureDef::MergeFrom(const SignatureDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.SignatureDef)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  inputs_.MergeFrom(from.inputs_);
  outputs_.MergeFrom(from.outputs_);
  if (from.method_name().size() > 0) {
    set_method_name(from.method_name());
  }
}

void SignatureDef::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.SignatureDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SignatureDef::CopyFrom(const SignatureDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.SignatureDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SignatureDef::IsInitialized() const {
  return true;
}

void SignatureDef::Swap(SignatureDef* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    SignatureDef* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void SignatureDef::UnsafeArenaSwap(SignatureDef* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void SignatureDef::InternalSwap(SignatureDef* other) {
  using std::swap;
  inputs_.Swap(&other->inputs_);
  outputs_.Swap(&other->outputs_);
  method_name_.Swap(&other->method_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata SignatureDef::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void AssetFileDef::InitAsDefaultInstance() {
  ::tensorflow::_AssetFileDef_default_instance_._instance.get_mutable()->tensor_info_ = const_cast< ::tensorflow::TensorInfo*>(
      ::tensorflow::TensorInfo::internal_default_instance());
}
void AssetFileDef::unsafe_arena_set_allocated_tensor_info(
    ::tensorflow::TensorInfo* tensor_info) {
  if (GetArenaNoVirtual() == NULL) {
    delete tensor_info_;
  }
  tensor_info_ = tensor_info;
  if (tensor_info) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.AssetFileDef.tensor_info)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AssetFileDef::kTensorInfoFieldNumber;
const int AssetFileDef::kFilenameFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AssetFileDef::AssetFileDef()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_AssetFileDef.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.AssetFileDef)
}
AssetFileDef::AssetFileDef(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_AssetFileDef.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.AssetFileDef)
}
AssetFileDef::AssetFileDef(const AssetFileDef& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  filename_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.filename().size() > 0) {
    filename_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.filename(),
      GetArenaNoVirtual());
  }
  if (from.has_tensor_info()) {
    tensor_info_ = new ::tensorflow::TensorInfo(*from.tensor_info_);
  } else {
    tensor_info_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.AssetFileDef)
}

void AssetFileDef::SharedCtor() {
  filename_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tensor_info_ = NULL;
}

AssetFileDef::~AssetFileDef() {
  // @@protoc_insertion_point(destructor:tensorflow.AssetFileDef)
  SharedDtor();
}

void AssetFileDef::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  filename_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete tensor_info_;
}

void AssetFileDef::ArenaDtor(void* object) {
  AssetFileDef* _this = reinterpret_cast< AssetFileDef* >(object);
  (void)_this;
}
void AssetFileDef::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void AssetFileDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* AssetFileDef::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const AssetFileDef& AssetFileDef::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::scc_info_AssetFileDef.base);
  return *internal_default_instance();
}


void AssetFileDef::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.AssetFileDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  filename_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && tensor_info_ != NULL) {
    delete tensor_info_;
  }
  tensor_info_ = NULL;
  _internal_metadata_.Clear();
}

bool AssetFileDef::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.AssetFileDef)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.TensorInfo tensor_info = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_tensor_info()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string filename = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_filename()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->filename().data(), static_cast<int>(this->filename().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.AssetFileDef.filename"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.AssetFileDef)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.AssetFileDef)
  return false;
#undef DO_
}

void AssetFileDef::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.AssetFileDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.TensorInfo tensor_info = 1;
  if (this->has_tensor_info()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->_internal_tensor_info(), output);
  }

  // string filename = 2;
  if (this->filename().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->filename().data(), static_cast<int>(this->filename().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.AssetFileDef.filename");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->filename(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.AssetFileDef)
}

::google::protobuf::uint8* AssetFileDef::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.AssetFileDef)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.TensorInfo tensor_info = 1;
  if (this->has_tensor_info()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->_internal_tensor_info(), deterministic, target);
  }

  // string filename = 2;
  if (this->filename().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->filename().data(), static_cast<int>(this->filename().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.AssetFileDef.filename");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->filename(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.AssetFileDef)
  return target;
}

size_t AssetFileDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.AssetFileDef)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string filename = 2;
  if (this->filename().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->filename());
  }

  // .tensorflow.TensorInfo tensor_info = 1;
  if (this->has_tensor_info()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *tensor_info_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AssetFileDef::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.AssetFileDef)
  GOOGLE_DCHECK_NE(&from, this);
  const AssetFileDef* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AssetFileDef>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.AssetFileDef)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.AssetFileDef)
    MergeFrom(*source);
  }
}

void AssetFileDef::MergeFrom(const AssetFileDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.AssetFileDef)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.filename().size() > 0) {
    set_filename(from.filename());
  }
  if (from.has_tensor_info()) {
    mutable_tensor_info()->::tensorflow::TensorInfo::MergeFrom(from.tensor_info());
  }
}

void AssetFileDef::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.AssetFileDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AssetFileDef::CopyFrom(const AssetFileDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.AssetFileDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AssetFileDef::IsInitialized() const {
  return true;
}

void AssetFileDef::Swap(AssetFileDef* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    AssetFileDef* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void AssetFileDef::UnsafeArenaSwap(AssetFileDef* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void AssetFileDef::InternalSwap(AssetFileDef* other) {
  using std::swap;
  filename_.Swap(&other->filename_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(tensor_info_, other->tensor_info_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata AssetFileDef::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::MetaGraphDef_MetaInfoDef* Arena::CreateMaybeMessage< ::tensorflow::MetaGraphDef_MetaInfoDef >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::MetaGraphDef_MetaInfoDef >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::MetaGraphDef_CollectionDefEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::MetaGraphDef_CollectionDefEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::MetaGraphDef_CollectionDefEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::MetaGraphDef_SignatureDefEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::MetaGraphDef_SignatureDefEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::MetaGraphDef_SignatureDefEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::MetaGraphDef* Arena::CreateMaybeMessage< ::tensorflow::MetaGraphDef >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::MetaGraphDef >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::CollectionDef_NodeList* Arena::CreateMaybeMessage< ::tensorflow::CollectionDef_NodeList >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::CollectionDef_NodeList >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::CollectionDef_BytesList* Arena::CreateMaybeMessage< ::tensorflow::CollectionDef_BytesList >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::CollectionDef_BytesList >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::CollectionDef_Int64List* Arena::CreateMaybeMessage< ::tensorflow::CollectionDef_Int64List >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::CollectionDef_Int64List >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::CollectionDef_FloatList* Arena::CreateMaybeMessage< ::tensorflow::CollectionDef_FloatList >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::CollectionDef_FloatList >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::CollectionDef_AnyList* Arena::CreateMaybeMessage< ::tensorflow::CollectionDef_AnyList >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::CollectionDef_AnyList >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::CollectionDef* Arena::CreateMaybeMessage< ::tensorflow::CollectionDef >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::CollectionDef >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::TensorInfo_CooSparse* Arena::CreateMaybeMessage< ::tensorflow::TensorInfo_CooSparse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::TensorInfo_CooSparse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::TensorInfo* Arena::CreateMaybeMessage< ::tensorflow::TensorInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::TensorInfo >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::SignatureDef_InputsEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::SignatureDef_InputsEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::SignatureDef_InputsEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::SignatureDef_OutputsEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::SignatureDef_OutputsEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::SignatureDef_OutputsEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::SignatureDef* Arena::CreateMaybeMessage< ::tensorflow::SignatureDef >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::SignatureDef >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::AssetFileDef* Arena::CreateMaybeMessage< ::tensorflow::AssetFileDef >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::AssetFileDef >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

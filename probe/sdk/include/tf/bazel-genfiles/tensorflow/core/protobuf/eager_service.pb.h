// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/eager_service.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/attr_value.pb.h"
#include "tensorflow/core/framework/device_attributes.pb.h"
#include "tensorflow/core/framework/function.pb.h"
#include "tensorflow/core/framework/versions.pb.h"
#include "tensorflow/core/protobuf/tensorflow_server.pb.h"
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/tensor.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto 

namespace protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[19];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto
namespace tensorflow {
namespace eager {
class CloseContextRequest;
class CloseContextRequestDefaultTypeInternal;
extern CloseContextRequestDefaultTypeInternal _CloseContextRequest_default_instance_;
class CloseContextResponse;
class CloseContextResponseDefaultTypeInternal;
extern CloseContextResponseDefaultTypeInternal _CloseContextResponse_default_instance_;
class CreateContextRequest;
class CreateContextRequestDefaultTypeInternal;
extern CreateContextRequestDefaultTypeInternal _CreateContextRequest_default_instance_;
class CreateContextResponse;
class CreateContextResponseDefaultTypeInternal;
extern CreateContextResponseDefaultTypeInternal _CreateContextResponse_default_instance_;
class EnqueueRequest;
class EnqueueRequestDefaultTypeInternal;
extern EnqueueRequestDefaultTypeInternal _EnqueueRequest_default_instance_;
class EnqueueResponse;
class EnqueueResponseDefaultTypeInternal;
extern EnqueueResponseDefaultTypeInternal _EnqueueResponse_default_instance_;
class KeepAliveRequest;
class KeepAliveRequestDefaultTypeInternal;
extern KeepAliveRequestDefaultTypeInternal _KeepAliveRequest_default_instance_;
class KeepAliveResponse;
class KeepAliveResponseDefaultTypeInternal;
extern KeepAliveResponseDefaultTypeInternal _KeepAliveResponse_default_instance_;
class Operation;
class OperationDefaultTypeInternal;
extern OperationDefaultTypeInternal _Operation_default_instance_;
class Operation_AttrsEntry_DoNotUse;
class Operation_AttrsEntry_DoNotUseDefaultTypeInternal;
extern Operation_AttrsEntry_DoNotUseDefaultTypeInternal _Operation_AttrsEntry_DoNotUse_default_instance_;
class QueueItem;
class QueueItemDefaultTypeInternal;
extern QueueItemDefaultTypeInternal _QueueItem_default_instance_;
class QueueResponse;
class QueueResponseDefaultTypeInternal;
extern QueueResponseDefaultTypeInternal _QueueResponse_default_instance_;
class RegisterFunctionRequest;
class RegisterFunctionRequestDefaultTypeInternal;
extern RegisterFunctionRequestDefaultTypeInternal _RegisterFunctionRequest_default_instance_;
class RegisterFunctionResponse;
class RegisterFunctionResponseDefaultTypeInternal;
extern RegisterFunctionResponseDefaultTypeInternal _RegisterFunctionResponse_default_instance_;
class RemoteTensorHandle;
class RemoteTensorHandleDefaultTypeInternal;
extern RemoteTensorHandleDefaultTypeInternal _RemoteTensorHandle_default_instance_;
class SendTensorRequest;
class SendTensorRequestDefaultTypeInternal;
extern SendTensorRequestDefaultTypeInternal _SendTensorRequest_default_instance_;
class SendTensorResponse;
class SendTensorResponseDefaultTypeInternal;
extern SendTensorResponseDefaultTypeInternal _SendTensorResponse_default_instance_;
class WaitQueueDoneRequest;
class WaitQueueDoneRequestDefaultTypeInternal;
extern WaitQueueDoneRequestDefaultTypeInternal _WaitQueueDoneRequest_default_instance_;
class WaitQueueDoneResponse;
class WaitQueueDoneResponseDefaultTypeInternal;
extern WaitQueueDoneResponseDefaultTypeInternal _WaitQueueDoneResponse_default_instance_;
}  // namespace eager
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::eager::CloseContextRequest* Arena::CreateMaybeMessage<::tensorflow::eager::CloseContextRequest>(Arena*);
template<> ::tensorflow::eager::CloseContextResponse* Arena::CreateMaybeMessage<::tensorflow::eager::CloseContextResponse>(Arena*);
template<> ::tensorflow::eager::CreateContextRequest* Arena::CreateMaybeMessage<::tensorflow::eager::CreateContextRequest>(Arena*);
template<> ::tensorflow::eager::CreateContextResponse* Arena::CreateMaybeMessage<::tensorflow::eager::CreateContextResponse>(Arena*);
template<> ::tensorflow::eager::EnqueueRequest* Arena::CreateMaybeMessage<::tensorflow::eager::EnqueueRequest>(Arena*);
template<> ::tensorflow::eager::EnqueueResponse* Arena::CreateMaybeMessage<::tensorflow::eager::EnqueueResponse>(Arena*);
template<> ::tensorflow::eager::KeepAliveRequest* Arena::CreateMaybeMessage<::tensorflow::eager::KeepAliveRequest>(Arena*);
template<> ::tensorflow::eager::KeepAliveResponse* Arena::CreateMaybeMessage<::tensorflow::eager::KeepAliveResponse>(Arena*);
template<> ::tensorflow::eager::Operation* Arena::CreateMaybeMessage<::tensorflow::eager::Operation>(Arena*);
template<> ::tensorflow::eager::Operation_AttrsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::eager::Operation_AttrsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::eager::QueueItem* Arena::CreateMaybeMessage<::tensorflow::eager::QueueItem>(Arena*);
template<> ::tensorflow::eager::QueueResponse* Arena::CreateMaybeMessage<::tensorflow::eager::QueueResponse>(Arena*);
template<> ::tensorflow::eager::RegisterFunctionRequest* Arena::CreateMaybeMessage<::tensorflow::eager::RegisterFunctionRequest>(Arena*);
template<> ::tensorflow::eager::RegisterFunctionResponse* Arena::CreateMaybeMessage<::tensorflow::eager::RegisterFunctionResponse>(Arena*);
template<> ::tensorflow::eager::RemoteTensorHandle* Arena::CreateMaybeMessage<::tensorflow::eager::RemoteTensorHandle>(Arena*);
template<> ::tensorflow::eager::SendTensorRequest* Arena::CreateMaybeMessage<::tensorflow::eager::SendTensorRequest>(Arena*);
template<> ::tensorflow::eager::SendTensorResponse* Arena::CreateMaybeMessage<::tensorflow::eager::SendTensorResponse>(Arena*);
template<> ::tensorflow::eager::WaitQueueDoneRequest* Arena::CreateMaybeMessage<::tensorflow::eager::WaitQueueDoneRequest>(Arena*);
template<> ::tensorflow::eager::WaitQueueDoneResponse* Arena::CreateMaybeMessage<::tensorflow::eager::WaitQueueDoneResponse>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {
namespace eager {

// ===================================================================

class RemoteTensorHandle : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.RemoteTensorHandle) */ {
 public:
  RemoteTensorHandle();
  virtual ~RemoteTensorHandle();

  RemoteTensorHandle(const RemoteTensorHandle& from);

  inline RemoteTensorHandle& operator=(const RemoteTensorHandle& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  RemoteTensorHandle(RemoteTensorHandle&& from) noexcept
    : RemoteTensorHandle() {
    *this = ::std::move(from);
  }

  inline RemoteTensorHandle& operator=(RemoteTensorHandle&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const RemoteTensorHandle& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RemoteTensorHandle* internal_default_instance() {
    return reinterpret_cast<const RemoteTensorHandle*>(
               &_RemoteTensorHandle_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void Swap(RemoteTensorHandle* other);
  friend void swap(RemoteTensorHandle& a, RemoteTensorHandle& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RemoteTensorHandle* New() const final {
    return CreateMaybeMessage<RemoteTensorHandle>(NULL);
  }

  RemoteTensorHandle* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<RemoteTensorHandle>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const RemoteTensorHandle& from);
  void MergeFrom(const RemoteTensorHandle& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RemoteTensorHandle* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 op_id = 1;
  void clear_op_id();
  static const int kOpIdFieldNumber = 1;
  ::google::protobuf::int64 op_id() const;
  void set_op_id(::google::protobuf::int64 value);

  // int32 output_num = 2;
  void clear_output_num();
  static const int kOutputNumFieldNumber = 2;
  ::google::protobuf::int32 output_num() const;
  void set_output_num(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.eager.RemoteTensorHandle)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int64 op_id_;
  ::google::protobuf::int32 output_num_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class Operation_AttrsEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<Operation_AttrsEntry_DoNotUse, 
    ::std::string, ::tensorflow::AttrValue,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<Operation_AttrsEntry_DoNotUse, 
    ::std::string, ::tensorflow::AttrValue,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  Operation_AttrsEntry_DoNotUse();
  Operation_AttrsEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const Operation_AttrsEntry_DoNotUse& other);
  static const Operation_AttrsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const Operation_AttrsEntry_DoNotUse*>(&_Operation_AttrsEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class Operation : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.Operation) */ {
 public:
  Operation();
  virtual ~Operation();

  Operation(const Operation& from);

  inline Operation& operator=(const Operation& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Operation(Operation&& from) noexcept
    : Operation() {
    *this = ::std::move(from);
  }

  inline Operation& operator=(Operation&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Operation& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Operation* internal_default_instance() {
    return reinterpret_cast<const Operation*>(
               &_Operation_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void Swap(Operation* other);
  friend void swap(Operation& a, Operation& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Operation* New() const final {
    return CreateMaybeMessage<Operation>(NULL);
  }

  Operation* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<Operation>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const Operation& from);
  void MergeFrom(const Operation& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Operation* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // repeated .tensorflow.eager.RemoteTensorHandle inputs = 3;
  int inputs_size() const;
  void clear_inputs();
  static const int kInputsFieldNumber = 3;
  ::tensorflow::eager::RemoteTensorHandle* mutable_inputs(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::eager::RemoteTensorHandle >*
      mutable_inputs();
  const ::tensorflow::eager::RemoteTensorHandle& inputs(int index) const;
  ::tensorflow::eager::RemoteTensorHandle* add_inputs();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::eager::RemoteTensorHandle >&
      inputs() const;

  // repeated int64 control_op_ids = 4;
  int control_op_ids_size() const;
  void clear_control_op_ids();
  static const int kControlOpIdsFieldNumber = 4;
  ::google::protobuf::int64 control_op_ids(int index) const;
  void set_control_op_ids(int index, ::google::protobuf::int64 value);
  void add_control_op_ids(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      control_op_ids() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_control_op_ids();

  // map<string, .tensorflow.AttrValue> attrs = 5;
  int attrs_size() const;
  void clear_attrs();
  static const int kAttrsFieldNumber = 5;
  const ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >&
      attrs() const;
  ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >*
      mutable_attrs();

  // string name = 2;
  void clear_name();
  static const int kNameFieldNumber = 2;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // string device = 6;
  void clear_device();
  static const int kDeviceFieldNumber = 6;
  const ::std::string& device() const;
  void set_device(const ::std::string& value);
  #if LANG_CXX11
  void set_device(::std::string&& value);
  #endif
  void set_device(const char* value);
  void set_device(const char* value, size_t size);
  ::std::string* mutable_device();
  ::std::string* release_device();
  void set_allocated_device(::std::string* device);

  // int64 id = 1;
  void clear_id();
  static const int kIdFieldNumber = 1;
  ::google::protobuf::int64 id() const;
  void set_id(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.eager.Operation)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::eager::RemoteTensorHandle > inputs_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > control_op_ids_;
  mutable int _control_op_ids_cached_byte_size_;
  ::google::protobuf::internal::MapField<
      Operation_AttrsEntry_DoNotUse,
      ::std::string, ::tensorflow::AttrValue,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > attrs_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::internal::ArenaStringPtr device_;
  ::google::protobuf::int64 id_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class QueueItem : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.QueueItem) */ {
 public:
  QueueItem();
  virtual ~QueueItem();

  QueueItem(const QueueItem& from);

  inline QueueItem& operator=(const QueueItem& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  QueueItem(QueueItem&& from) noexcept
    : QueueItem() {
    *this = ::std::move(from);
  }

  inline QueueItem& operator=(QueueItem&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const QueueItem& default_instance();

  enum ItemCase {
    kHandleToDecref = 1,
    kOperation = 2,
    ITEM_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const QueueItem* internal_default_instance() {
    return reinterpret_cast<const QueueItem*>(
               &_QueueItem_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void Swap(QueueItem* other);
  friend void swap(QueueItem& a, QueueItem& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline QueueItem* New() const final {
    return CreateMaybeMessage<QueueItem>(NULL);
  }

  QueueItem* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<QueueItem>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const QueueItem& from);
  void MergeFrom(const QueueItem& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(QueueItem* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .tensorflow.eager.RemoteTensorHandle handle_to_decref = 1;
  bool has_handle_to_decref() const;
  void clear_handle_to_decref();
  static const int kHandleToDecrefFieldNumber = 1;
  private:
  const ::tensorflow::eager::RemoteTensorHandle& _internal_handle_to_decref() const;
  public:
  const ::tensorflow::eager::RemoteTensorHandle& handle_to_decref() const;
  ::tensorflow::eager::RemoteTensorHandle* release_handle_to_decref();
  ::tensorflow::eager::RemoteTensorHandle* mutable_handle_to_decref();
  void set_allocated_handle_to_decref(::tensorflow::eager::RemoteTensorHandle* handle_to_decref);

  // .tensorflow.eager.Operation operation = 2;
  bool has_operation() const;
  void clear_operation();
  static const int kOperationFieldNumber = 2;
  private:
  const ::tensorflow::eager::Operation& _internal_operation() const;
  public:
  const ::tensorflow::eager::Operation& operation() const;
  ::tensorflow::eager::Operation* release_operation();
  ::tensorflow::eager::Operation* mutable_operation();
  void set_allocated_operation(::tensorflow::eager::Operation* operation);

  void clear_item();
  ItemCase item_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.eager.QueueItem)
 private:
  void set_has_handle_to_decref();
  void set_has_operation();

  inline bool has_item() const;
  inline void clear_has_item();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  union ItemUnion {
    ItemUnion() {}
    ::tensorflow::eager::RemoteTensorHandle* handle_to_decref_;
    ::tensorflow::eager::Operation* operation_;
  } item_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class QueueResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.QueueResponse) */ {
 public:
  QueueResponse();
  virtual ~QueueResponse();

  QueueResponse(const QueueResponse& from);

  inline QueueResponse& operator=(const QueueResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  QueueResponse(QueueResponse&& from) noexcept
    : QueueResponse() {
    *this = ::std::move(from);
  }

  inline QueueResponse& operator=(QueueResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const QueueResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const QueueResponse* internal_default_instance() {
    return reinterpret_cast<const QueueResponse*>(
               &_QueueResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  void Swap(QueueResponse* other);
  friend void swap(QueueResponse& a, QueueResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline QueueResponse* New() const final {
    return CreateMaybeMessage<QueueResponse>(NULL);
  }

  QueueResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<QueueResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const QueueResponse& from);
  void MergeFrom(const QueueResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(QueueResponse* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.TensorShapeProto shape = 1;
  int shape_size() const;
  void clear_shape();
  static const int kShapeFieldNumber = 1;
  ::tensorflow::TensorShapeProto* mutable_shape(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorShapeProto >*
      mutable_shape();
  const ::tensorflow::TensorShapeProto& shape(int index) const;
  ::tensorflow::TensorShapeProto* add_shape();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorShapeProto >&
      shape() const;

  // @@protoc_insertion_point(class_scope:tensorflow.eager.QueueResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorShapeProto > shape_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CreateContextRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.CreateContextRequest) */ {
 public:
  CreateContextRequest();
  virtual ~CreateContextRequest();

  CreateContextRequest(const CreateContextRequest& from);

  inline CreateContextRequest& operator=(const CreateContextRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CreateContextRequest(CreateContextRequest&& from) noexcept
    : CreateContextRequest() {
    *this = ::std::move(from);
  }

  inline CreateContextRequest& operator=(CreateContextRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const CreateContextRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CreateContextRequest* internal_default_instance() {
    return reinterpret_cast<const CreateContextRequest*>(
               &_CreateContextRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  void Swap(CreateContextRequest* other);
  friend void swap(CreateContextRequest& a, CreateContextRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CreateContextRequest* New() const final {
    return CreateMaybeMessage<CreateContextRequest>(NULL);
  }

  CreateContextRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CreateContextRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CreateContextRequest& from);
  void MergeFrom(const CreateContextRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CreateContextRequest* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .tensorflow.ServerDef server_def = 1;
  bool has_server_def() const;
  void clear_server_def();
  static const int kServerDefFieldNumber = 1;
  private:
  const ::tensorflow::ServerDef& _internal_server_def() const;
  public:
  const ::tensorflow::ServerDef& server_def() const;
  ::tensorflow::ServerDef* release_server_def();
  ::tensorflow::ServerDef* mutable_server_def();
  void set_allocated_server_def(::tensorflow::ServerDef* server_def);

  // .tensorflow.VersionDef version_def = 4;
  bool has_version_def() const;
  void clear_version_def();
  static const int kVersionDefFieldNumber = 4;
  private:
  const ::tensorflow::VersionDef& _internal_version_def() const;
  public:
  const ::tensorflow::VersionDef& version_def() const;
  ::tensorflow::VersionDef* release_version_def();
  ::tensorflow::VersionDef* mutable_version_def();
  void set_allocated_version_def(::tensorflow::VersionDef* version_def);

  // int64 keep_alive_secs = 3;
  void clear_keep_alive_secs();
  static const int kKeepAliveSecsFieldNumber = 3;
  ::google::protobuf::int64 keep_alive_secs() const;
  void set_keep_alive_secs(::google::protobuf::int64 value);

  // int64 rendezvous_id = 5;
  void clear_rendezvous_id();
  static const int kRendezvousIdFieldNumber = 5;
  ::google::protobuf::int64 rendezvous_id() const;
  void set_rendezvous_id(::google::protobuf::int64 value);

  // bool async = 2;
  void clear_async();
  static const int kAsyncFieldNumber = 2;
  bool async() const;
  void set_async(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.eager.CreateContextRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::tensorflow::ServerDef* server_def_;
  ::tensorflow::VersionDef* version_def_;
  ::google::protobuf::int64 keep_alive_secs_;
  ::google::protobuf::int64 rendezvous_id_;
  bool async_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CreateContextResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.CreateContextResponse) */ {
 public:
  CreateContextResponse();
  virtual ~CreateContextResponse();

  CreateContextResponse(const CreateContextResponse& from);

  inline CreateContextResponse& operator=(const CreateContextResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CreateContextResponse(CreateContextResponse&& from) noexcept
    : CreateContextResponse() {
    *this = ::std::move(from);
  }

  inline CreateContextResponse& operator=(CreateContextResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const CreateContextResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CreateContextResponse* internal_default_instance() {
    return reinterpret_cast<const CreateContextResponse*>(
               &_CreateContextResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  void Swap(CreateContextResponse* other);
  friend void swap(CreateContextResponse& a, CreateContextResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CreateContextResponse* New() const final {
    return CreateMaybeMessage<CreateContextResponse>(NULL);
  }

  CreateContextResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CreateContextResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CreateContextResponse& from);
  void MergeFrom(const CreateContextResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CreateContextResponse* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.DeviceAttributes device_attributes = 2;
  int device_attributes_size() const;
  void clear_device_attributes();
  static const int kDeviceAttributesFieldNumber = 2;
  ::tensorflow::DeviceAttributes* mutable_device_attributes(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
      mutable_device_attributes();
  const ::tensorflow::DeviceAttributes& device_attributes(int index) const;
  ::tensorflow::DeviceAttributes* add_device_attributes();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
      device_attributes() const;

  // fixed64 context_id = 1;
  void clear_context_id();
  static const int kContextIdFieldNumber = 1;
  ::google::protobuf::uint64 context_id() const;
  void set_context_id(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.eager.CreateContextResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::DeviceAttributes > device_attributes_;
  ::google::protobuf::uint64 context_id_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class EnqueueRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.EnqueueRequest) */ {
 public:
  EnqueueRequest();
  virtual ~EnqueueRequest();

  EnqueueRequest(const EnqueueRequest& from);

  inline EnqueueRequest& operator=(const EnqueueRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  EnqueueRequest(EnqueueRequest&& from) noexcept
    : EnqueueRequest() {
    *this = ::std::move(from);
  }

  inline EnqueueRequest& operator=(EnqueueRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const EnqueueRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const EnqueueRequest* internal_default_instance() {
    return reinterpret_cast<const EnqueueRequest*>(
               &_EnqueueRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  void Swap(EnqueueRequest* other);
  friend void swap(EnqueueRequest& a, EnqueueRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline EnqueueRequest* New() const final {
    return CreateMaybeMessage<EnqueueRequest>(NULL);
  }

  EnqueueRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<EnqueueRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const EnqueueRequest& from);
  void MergeFrom(const EnqueueRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EnqueueRequest* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.eager.QueueItem queue = 3;
  int queue_size() const;
  void clear_queue();
  static const int kQueueFieldNumber = 3;
  ::tensorflow::eager::QueueItem* mutable_queue(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::eager::QueueItem >*
      mutable_queue();
  const ::tensorflow::eager::QueueItem& queue(int index) const;
  ::tensorflow::eager::QueueItem* add_queue();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::eager::QueueItem >&
      queue() const;

  // fixed64 context_id = 1;
  void clear_context_id();
  static const int kContextIdFieldNumber = 1;
  ::google::protobuf::uint64 context_id() const;
  void set_context_id(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.eager.EnqueueRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::eager::QueueItem > queue_;
  ::google::protobuf::uint64 context_id_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class EnqueueResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.EnqueueResponse) */ {
 public:
  EnqueueResponse();
  virtual ~EnqueueResponse();

  EnqueueResponse(const EnqueueResponse& from);

  inline EnqueueResponse& operator=(const EnqueueResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  EnqueueResponse(EnqueueResponse&& from) noexcept
    : EnqueueResponse() {
    *this = ::std::move(from);
  }

  inline EnqueueResponse& operator=(EnqueueResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const EnqueueResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const EnqueueResponse* internal_default_instance() {
    return reinterpret_cast<const EnqueueResponse*>(
               &_EnqueueResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  void Swap(EnqueueResponse* other);
  friend void swap(EnqueueResponse& a, EnqueueResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline EnqueueResponse* New() const final {
    return CreateMaybeMessage<EnqueueResponse>(NULL);
  }

  EnqueueResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<EnqueueResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const EnqueueResponse& from);
  void MergeFrom(const EnqueueResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EnqueueResponse* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.eager.QueueResponse queue_response = 1;
  int queue_response_size() const;
  void clear_queue_response();
  static const int kQueueResponseFieldNumber = 1;
  ::tensorflow::eager::QueueResponse* mutable_queue_response(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::eager::QueueResponse >*
      mutable_queue_response();
  const ::tensorflow::eager::QueueResponse& queue_response(int index) const;
  ::tensorflow::eager::QueueResponse* add_queue_response();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::eager::QueueResponse >&
      queue_response() const;

  // @@protoc_insertion_point(class_scope:tensorflow.eager.EnqueueResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::eager::QueueResponse > queue_response_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class WaitQueueDoneRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.WaitQueueDoneRequest) */ {
 public:
  WaitQueueDoneRequest();
  virtual ~WaitQueueDoneRequest();

  WaitQueueDoneRequest(const WaitQueueDoneRequest& from);

  inline WaitQueueDoneRequest& operator=(const WaitQueueDoneRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  WaitQueueDoneRequest(WaitQueueDoneRequest&& from) noexcept
    : WaitQueueDoneRequest() {
    *this = ::std::move(from);
  }

  inline WaitQueueDoneRequest& operator=(WaitQueueDoneRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const WaitQueueDoneRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const WaitQueueDoneRequest* internal_default_instance() {
    return reinterpret_cast<const WaitQueueDoneRequest*>(
               &_WaitQueueDoneRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  void Swap(WaitQueueDoneRequest* other);
  friend void swap(WaitQueueDoneRequest& a, WaitQueueDoneRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline WaitQueueDoneRequest* New() const final {
    return CreateMaybeMessage<WaitQueueDoneRequest>(NULL);
  }

  WaitQueueDoneRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<WaitQueueDoneRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const WaitQueueDoneRequest& from);
  void MergeFrom(const WaitQueueDoneRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WaitQueueDoneRequest* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int64 op_id = 2;
  int op_id_size() const;
  void clear_op_id();
  static const int kOpIdFieldNumber = 2;
  ::google::protobuf::int64 op_id(int index) const;
  void set_op_id(int index, ::google::protobuf::int64 value);
  void add_op_id(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      op_id() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_op_id();

  // fixed64 context_id = 1;
  void clear_context_id();
  static const int kContextIdFieldNumber = 1;
  ::google::protobuf::uint64 context_id() const;
  void set_context_id(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.eager.WaitQueueDoneRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > op_id_;
  mutable int _op_id_cached_byte_size_;
  ::google::protobuf::uint64 context_id_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class WaitQueueDoneResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.WaitQueueDoneResponse) */ {
 public:
  WaitQueueDoneResponse();
  virtual ~WaitQueueDoneResponse();

  WaitQueueDoneResponse(const WaitQueueDoneResponse& from);

  inline WaitQueueDoneResponse& operator=(const WaitQueueDoneResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  WaitQueueDoneResponse(WaitQueueDoneResponse&& from) noexcept
    : WaitQueueDoneResponse() {
    *this = ::std::move(from);
  }

  inline WaitQueueDoneResponse& operator=(WaitQueueDoneResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const WaitQueueDoneResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const WaitQueueDoneResponse* internal_default_instance() {
    return reinterpret_cast<const WaitQueueDoneResponse*>(
               &_WaitQueueDoneResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  void Swap(WaitQueueDoneResponse* other);
  friend void swap(WaitQueueDoneResponse& a, WaitQueueDoneResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline WaitQueueDoneResponse* New() const final {
    return CreateMaybeMessage<WaitQueueDoneResponse>(NULL);
  }

  WaitQueueDoneResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<WaitQueueDoneResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const WaitQueueDoneResponse& from);
  void MergeFrom(const WaitQueueDoneResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WaitQueueDoneResponse* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.eager.WaitQueueDoneResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class KeepAliveRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.KeepAliveRequest) */ {
 public:
  KeepAliveRequest();
  virtual ~KeepAliveRequest();

  KeepAliveRequest(const KeepAliveRequest& from);

  inline KeepAliveRequest& operator=(const KeepAliveRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  KeepAliveRequest(KeepAliveRequest&& from) noexcept
    : KeepAliveRequest() {
    *this = ::std::move(from);
  }

  inline KeepAliveRequest& operator=(KeepAliveRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const KeepAliveRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const KeepAliveRequest* internal_default_instance() {
    return reinterpret_cast<const KeepAliveRequest*>(
               &_KeepAliveRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  void Swap(KeepAliveRequest* other);
  friend void swap(KeepAliveRequest& a, KeepAliveRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline KeepAliveRequest* New() const final {
    return CreateMaybeMessage<KeepAliveRequest>(NULL);
  }

  KeepAliveRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<KeepAliveRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const KeepAliveRequest& from);
  void MergeFrom(const KeepAliveRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(KeepAliveRequest* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // fixed64 context_id = 1;
  void clear_context_id();
  static const int kContextIdFieldNumber = 1;
  ::google::protobuf::uint64 context_id() const;
  void set_context_id(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.eager.KeepAliveRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::uint64 context_id_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class KeepAliveResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.KeepAliveResponse) */ {
 public:
  KeepAliveResponse();
  virtual ~KeepAliveResponse();

  KeepAliveResponse(const KeepAliveResponse& from);

  inline KeepAliveResponse& operator=(const KeepAliveResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  KeepAliveResponse(KeepAliveResponse&& from) noexcept
    : KeepAliveResponse() {
    *this = ::std::move(from);
  }

  inline KeepAliveResponse& operator=(KeepAliveResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const KeepAliveResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const KeepAliveResponse* internal_default_instance() {
    return reinterpret_cast<const KeepAliveResponse*>(
               &_KeepAliveResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  void Swap(KeepAliveResponse* other);
  friend void swap(KeepAliveResponse& a, KeepAliveResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline KeepAliveResponse* New() const final {
    return CreateMaybeMessage<KeepAliveResponse>(NULL);
  }

  KeepAliveResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<KeepAliveResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const KeepAliveResponse& from);
  void MergeFrom(const KeepAliveResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(KeepAliveResponse* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.eager.KeepAliveResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CloseContextRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.CloseContextRequest) */ {
 public:
  CloseContextRequest();
  virtual ~CloseContextRequest();

  CloseContextRequest(const CloseContextRequest& from);

  inline CloseContextRequest& operator=(const CloseContextRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CloseContextRequest(CloseContextRequest&& from) noexcept
    : CloseContextRequest() {
    *this = ::std::move(from);
  }

  inline CloseContextRequest& operator=(CloseContextRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const CloseContextRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CloseContextRequest* internal_default_instance() {
    return reinterpret_cast<const CloseContextRequest*>(
               &_CloseContextRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  void Swap(CloseContextRequest* other);
  friend void swap(CloseContextRequest& a, CloseContextRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CloseContextRequest* New() const final {
    return CreateMaybeMessage<CloseContextRequest>(NULL);
  }

  CloseContextRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CloseContextRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CloseContextRequest& from);
  void MergeFrom(const CloseContextRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CloseContextRequest* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // fixed64 context_id = 1;
  void clear_context_id();
  static const int kContextIdFieldNumber = 1;
  ::google::protobuf::uint64 context_id() const;
  void set_context_id(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.eager.CloseContextRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::uint64 context_id_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CloseContextResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.CloseContextResponse) */ {
 public:
  CloseContextResponse();
  virtual ~CloseContextResponse();

  CloseContextResponse(const CloseContextResponse& from);

  inline CloseContextResponse& operator=(const CloseContextResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CloseContextResponse(CloseContextResponse&& from) noexcept
    : CloseContextResponse() {
    *this = ::std::move(from);
  }

  inline CloseContextResponse& operator=(CloseContextResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const CloseContextResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CloseContextResponse* internal_default_instance() {
    return reinterpret_cast<const CloseContextResponse*>(
               &_CloseContextResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  void Swap(CloseContextResponse* other);
  friend void swap(CloseContextResponse& a, CloseContextResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CloseContextResponse* New() const final {
    return CreateMaybeMessage<CloseContextResponse>(NULL);
  }

  CloseContextResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<CloseContextResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const CloseContextResponse& from);
  void MergeFrom(const CloseContextResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CloseContextResponse* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.eager.CloseContextResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class RegisterFunctionRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.RegisterFunctionRequest) */ {
 public:
  RegisterFunctionRequest();
  virtual ~RegisterFunctionRequest();

  RegisterFunctionRequest(const RegisterFunctionRequest& from);

  inline RegisterFunctionRequest& operator=(const RegisterFunctionRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  RegisterFunctionRequest(RegisterFunctionRequest&& from) noexcept
    : RegisterFunctionRequest() {
    *this = ::std::move(from);
  }

  inline RegisterFunctionRequest& operator=(RegisterFunctionRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const RegisterFunctionRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RegisterFunctionRequest* internal_default_instance() {
    return reinterpret_cast<const RegisterFunctionRequest*>(
               &_RegisterFunctionRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  void Swap(RegisterFunctionRequest* other);
  friend void swap(RegisterFunctionRequest& a, RegisterFunctionRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RegisterFunctionRequest* New() const final {
    return CreateMaybeMessage<RegisterFunctionRequest>(NULL);
  }

  RegisterFunctionRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<RegisterFunctionRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const RegisterFunctionRequest& from);
  void MergeFrom(const RegisterFunctionRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RegisterFunctionRequest* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .tensorflow.FunctionDef function_def = 2;
  bool has_function_def() const;
  void clear_function_def();
  static const int kFunctionDefFieldNumber = 2;
  private:
  const ::tensorflow::FunctionDef& _internal_function_def() const;
  public:
  const ::tensorflow::FunctionDef& function_def() const;
  ::tensorflow::FunctionDef* release_function_def();
  ::tensorflow::FunctionDef* mutable_function_def();
  void set_allocated_function_def(::tensorflow::FunctionDef* function_def);

  // fixed64 context_id = 1;
  void clear_context_id();
  static const int kContextIdFieldNumber = 1;
  ::google::protobuf::uint64 context_id() const;
  void set_context_id(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.eager.RegisterFunctionRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::tensorflow::FunctionDef* function_def_;
  ::google::protobuf::uint64 context_id_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class RegisterFunctionResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.RegisterFunctionResponse) */ {
 public:
  RegisterFunctionResponse();
  virtual ~RegisterFunctionResponse();

  RegisterFunctionResponse(const RegisterFunctionResponse& from);

  inline RegisterFunctionResponse& operator=(const RegisterFunctionResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  RegisterFunctionResponse(RegisterFunctionResponse&& from) noexcept
    : RegisterFunctionResponse() {
    *this = ::std::move(from);
  }

  inline RegisterFunctionResponse& operator=(RegisterFunctionResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const RegisterFunctionResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RegisterFunctionResponse* internal_default_instance() {
    return reinterpret_cast<const RegisterFunctionResponse*>(
               &_RegisterFunctionResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  void Swap(RegisterFunctionResponse* other);
  friend void swap(RegisterFunctionResponse& a, RegisterFunctionResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RegisterFunctionResponse* New() const final {
    return CreateMaybeMessage<RegisterFunctionResponse>(NULL);
  }

  RegisterFunctionResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<RegisterFunctionResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const RegisterFunctionResponse& from);
  void MergeFrom(const RegisterFunctionResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RegisterFunctionResponse* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.eager.RegisterFunctionResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class SendTensorRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.SendTensorRequest) */ {
 public:
  SendTensorRequest();
  virtual ~SendTensorRequest();

  SendTensorRequest(const SendTensorRequest& from);

  inline SendTensorRequest& operator=(const SendTensorRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SendTensorRequest(SendTensorRequest&& from) noexcept
    : SendTensorRequest() {
    *this = ::std::move(from);
  }

  inline SendTensorRequest& operator=(SendTensorRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const SendTensorRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SendTensorRequest* internal_default_instance() {
    return reinterpret_cast<const SendTensorRequest*>(
               &_SendTensorRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  void Swap(SendTensorRequest* other);
  friend void swap(SendTensorRequest& a, SendTensorRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SendTensorRequest* New() const final {
    return CreateMaybeMessage<SendTensorRequest>(NULL);
  }

  SendTensorRequest* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<SendTensorRequest>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const SendTensorRequest& from);
  void MergeFrom(const SendTensorRequest& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SendTensorRequest* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.TensorProto tensors = 3;
  int tensors_size() const;
  void clear_tensors();
  static const int kTensorsFieldNumber = 3;
  ::tensorflow::TensorProto* mutable_tensors(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorProto >*
      mutable_tensors();
  const ::tensorflow::TensorProto& tensors(int index) const;
  ::tensorflow::TensorProto* add_tensors();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorProto >&
      tensors() const;

  // string device_name = 4;
  void clear_device_name();
  static const int kDeviceNameFieldNumber = 4;
  const ::std::string& device_name() const;
  void set_device_name(const ::std::string& value);
  #if LANG_CXX11
  void set_device_name(::std::string&& value);
  #endif
  void set_device_name(const char* value);
  void set_device_name(const char* value, size_t size);
  ::std::string* mutable_device_name();
  ::std::string* release_device_name();
  void set_allocated_device_name(::std::string* device_name);

  // fixed64 context_id = 1;
  void clear_context_id();
  static const int kContextIdFieldNumber = 1;
  ::google::protobuf::uint64 context_id() const;
  void set_context_id(::google::protobuf::uint64 value);

  // int64 op_id = 2;
  void clear_op_id();
  static const int kOpIdFieldNumber = 2;
  ::google::protobuf::int64 op_id() const;
  void set_op_id(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.eager.SendTensorRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorProto > tensors_;
  ::google::protobuf::internal::ArenaStringPtr device_name_;
  ::google::protobuf::uint64 context_id_;
  ::google::protobuf::int64 op_id_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class SendTensorResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.SendTensorResponse) */ {
 public:
  SendTensorResponse();
  virtual ~SendTensorResponse();

  SendTensorResponse(const SendTensorResponse& from);

  inline SendTensorResponse& operator=(const SendTensorResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SendTensorResponse(SendTensorResponse&& from) noexcept
    : SendTensorResponse() {
    *this = ::std::move(from);
  }

  inline SendTensorResponse& operator=(SendTensorResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const SendTensorResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SendTensorResponse* internal_default_instance() {
    return reinterpret_cast<const SendTensorResponse*>(
               &_SendTensorResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  void Swap(SendTensorResponse* other);
  friend void swap(SendTensorResponse& a, SendTensorResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SendTensorResponse* New() const final {
    return CreateMaybeMessage<SendTensorResponse>(NULL);
  }

  SendTensorResponse* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<SendTensorResponse>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const SendTensorResponse& from);
  void MergeFrom(const SendTensorResponse& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SendTensorResponse* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.eager.SendTensorResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// RemoteTensorHandle

// int64 op_id = 1;
inline void RemoteTensorHandle::clear_op_id() {
  op_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 RemoteTensorHandle::op_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.RemoteTensorHandle.op_id)
  return op_id_;
}
inline void RemoteTensorHandle::set_op_id(::google::protobuf::int64 value) {
  
  op_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.RemoteTensorHandle.op_id)
}

// int32 output_num = 2;
inline void RemoteTensorHandle::clear_output_num() {
  output_num_ = 0;
}
inline ::google::protobuf::int32 RemoteTensorHandle::output_num() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.RemoteTensorHandle.output_num)
  return output_num_;
}
inline void RemoteTensorHandle::set_output_num(::google::protobuf::int32 value) {
  
  output_num_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.RemoteTensorHandle.output_num)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// Operation

// int64 id = 1;
inline void Operation::clear_id() {
  id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 Operation::id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.Operation.id)
  return id_;
}
inline void Operation::set_id(::google::protobuf::int64 value) {
  
  id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.Operation.id)
}

// string name = 2;
inline void Operation::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Operation::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.Operation.name)
  return name_.GetNoArena();
}
inline void Operation::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.Operation.name)
}
#if LANG_CXX11
inline void Operation::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.eager.Operation.name)
}
#endif
inline void Operation::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.eager.Operation.name)
}
inline void Operation::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.eager.Operation.name)
}
inline ::std::string* Operation::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.Operation.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Operation::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.Operation.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Operation::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.Operation.name)
}

// repeated .tensorflow.eager.RemoteTensorHandle inputs = 3;
inline int Operation::inputs_size() const {
  return inputs_.size();
}
inline void Operation::clear_inputs() {
  inputs_.Clear();
}
inline ::tensorflow::eager::RemoteTensorHandle* Operation::mutable_inputs(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.Operation.inputs)
  return inputs_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::eager::RemoteTensorHandle >*
Operation::mutable_inputs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.Operation.inputs)
  return &inputs_;
}
inline const ::tensorflow::eager::RemoteTensorHandle& Operation::inputs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.Operation.inputs)
  return inputs_.Get(index);
}
inline ::tensorflow::eager::RemoteTensorHandle* Operation::add_inputs() {
  // @@protoc_insertion_point(field_add:tensorflow.eager.Operation.inputs)
  return inputs_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::eager::RemoteTensorHandle >&
Operation::inputs() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.Operation.inputs)
  return inputs_;
}

// repeated int64 control_op_ids = 4;
inline int Operation::control_op_ids_size() const {
  return control_op_ids_.size();
}
inline void Operation::clear_control_op_ids() {
  control_op_ids_.Clear();
}
inline ::google::protobuf::int64 Operation::control_op_ids(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.Operation.control_op_ids)
  return control_op_ids_.Get(index);
}
inline void Operation::set_control_op_ids(int index, ::google::protobuf::int64 value) {
  control_op_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.Operation.control_op_ids)
}
inline void Operation::add_control_op_ids(::google::protobuf::int64 value) {
  control_op_ids_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.eager.Operation.control_op_ids)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
Operation::control_op_ids() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.Operation.control_op_ids)
  return control_op_ids_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
Operation::mutable_control_op_ids() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.Operation.control_op_ids)
  return &control_op_ids_;
}

// map<string, .tensorflow.AttrValue> attrs = 5;
inline int Operation::attrs_size() const {
  return attrs_.size();
}
inline const ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >&
Operation::attrs() const {
  // @@protoc_insertion_point(field_map:tensorflow.eager.Operation.attrs)
  return attrs_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::tensorflow::AttrValue >*
Operation::mutable_attrs() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.eager.Operation.attrs)
  return attrs_.MutableMap();
}

// string device = 6;
inline void Operation::clear_device() {
  device_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Operation::device() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.Operation.device)
  return device_.GetNoArena();
}
inline void Operation::set_device(const ::std::string& value) {
  
  device_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.Operation.device)
}
#if LANG_CXX11
inline void Operation::set_device(::std::string&& value) {
  
  device_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.eager.Operation.device)
}
#endif
inline void Operation::set_device(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  device_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.eager.Operation.device)
}
inline void Operation::set_device(const char* value, size_t size) {
  
  device_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.eager.Operation.device)
}
inline ::std::string* Operation::mutable_device() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.Operation.device)
  return device_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Operation::release_device() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.Operation.device)
  
  return device_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Operation::set_allocated_device(::std::string* device) {
  if (device != NULL) {
    
  } else {
    
  }
  device_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), device);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.Operation.device)
}

// -------------------------------------------------------------------

// QueueItem

// .tensorflow.eager.RemoteTensorHandle handle_to_decref = 1;
inline bool QueueItem::has_handle_to_decref() const {
  return item_case() == kHandleToDecref;
}
inline void QueueItem::set_has_handle_to_decref() {
  _oneof_case_[0] = kHandleToDecref;
}
inline void QueueItem::clear_handle_to_decref() {
  if (has_handle_to_decref()) {
    delete item_.handle_to_decref_;
    clear_has_item();
  }
}
inline const ::tensorflow::eager::RemoteTensorHandle& QueueItem::_internal_handle_to_decref() const {
  return *item_.handle_to_decref_;
}
inline ::tensorflow::eager::RemoteTensorHandle* QueueItem::release_handle_to_decref() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.QueueItem.handle_to_decref)
  if (has_handle_to_decref()) {
    clear_has_item();
      ::tensorflow::eager::RemoteTensorHandle* temp = item_.handle_to_decref_;
    item_.handle_to_decref_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::eager::RemoteTensorHandle& QueueItem::handle_to_decref() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.QueueItem.handle_to_decref)
  return has_handle_to_decref()
      ? *item_.handle_to_decref_
      : *reinterpret_cast< ::tensorflow::eager::RemoteTensorHandle*>(&::tensorflow::eager::_RemoteTensorHandle_default_instance_);
}
inline ::tensorflow::eager::RemoteTensorHandle* QueueItem::mutable_handle_to_decref() {
  if (!has_handle_to_decref()) {
    clear_item();
    set_has_handle_to_decref();
    item_.handle_to_decref_ = CreateMaybeMessage< ::tensorflow::eager::RemoteTensorHandle >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.QueueItem.handle_to_decref)
  return item_.handle_to_decref_;
}

// .tensorflow.eager.Operation operation = 2;
inline bool QueueItem::has_operation() const {
  return item_case() == kOperation;
}
inline void QueueItem::set_has_operation() {
  _oneof_case_[0] = kOperation;
}
inline void QueueItem::clear_operation() {
  if (has_operation()) {
    delete item_.operation_;
    clear_has_item();
  }
}
inline const ::tensorflow::eager::Operation& QueueItem::_internal_operation() const {
  return *item_.operation_;
}
inline ::tensorflow::eager::Operation* QueueItem::release_operation() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.QueueItem.operation)
  if (has_operation()) {
    clear_has_item();
      ::tensorflow::eager::Operation* temp = item_.operation_;
    item_.operation_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::eager::Operation& QueueItem::operation() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.QueueItem.operation)
  return has_operation()
      ? *item_.operation_
      : *reinterpret_cast< ::tensorflow::eager::Operation*>(&::tensorflow::eager::_Operation_default_instance_);
}
inline ::tensorflow::eager::Operation* QueueItem::mutable_operation() {
  if (!has_operation()) {
    clear_item();
    set_has_operation();
    item_.operation_ = CreateMaybeMessage< ::tensorflow::eager::Operation >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.QueueItem.operation)
  return item_.operation_;
}

inline bool QueueItem::has_item() const {
  return item_case() != ITEM_NOT_SET;
}
inline void QueueItem::clear_has_item() {
  _oneof_case_[0] = ITEM_NOT_SET;
}
inline QueueItem::ItemCase QueueItem::item_case() const {
  return QueueItem::ItemCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// QueueResponse

// repeated .tensorflow.TensorShapeProto shape = 1;
inline int QueueResponse::shape_size() const {
  return shape_.size();
}
inline ::tensorflow::TensorShapeProto* QueueResponse::mutable_shape(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.QueueResponse.shape)
  return shape_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorShapeProto >*
QueueResponse::mutable_shape() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.QueueResponse.shape)
  return &shape_;
}
inline const ::tensorflow::TensorShapeProto& QueueResponse::shape(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.QueueResponse.shape)
  return shape_.Get(index);
}
inline ::tensorflow::TensorShapeProto* QueueResponse::add_shape() {
  // @@protoc_insertion_point(field_add:tensorflow.eager.QueueResponse.shape)
  return shape_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorShapeProto >&
QueueResponse::shape() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.QueueResponse.shape)
  return shape_;
}

// -------------------------------------------------------------------

// CreateContextRequest

// .tensorflow.ServerDef server_def = 1;
inline bool CreateContextRequest::has_server_def() const {
  return this != internal_default_instance() && server_def_ != NULL;
}
inline const ::tensorflow::ServerDef& CreateContextRequest::_internal_server_def() const {
  return *server_def_;
}
inline const ::tensorflow::ServerDef& CreateContextRequest::server_def() const {
  const ::tensorflow::ServerDef* p = server_def_;
  // @@protoc_insertion_point(field_get:tensorflow.eager.CreateContextRequest.server_def)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::ServerDef*>(
      &::tensorflow::_ServerDef_default_instance_);
}
inline ::tensorflow::ServerDef* CreateContextRequest::release_server_def() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.CreateContextRequest.server_def)
  
  ::tensorflow::ServerDef* temp = server_def_;
  server_def_ = NULL;
  return temp;
}
inline ::tensorflow::ServerDef* CreateContextRequest::mutable_server_def() {
  
  if (server_def_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::ServerDef>(GetArenaNoVirtual());
    server_def_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.CreateContextRequest.server_def)
  return server_def_;
}
inline void CreateContextRequest::set_allocated_server_def(::tensorflow::ServerDef* server_def) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(server_def_);
  }
  if (server_def) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(server_def)->GetArena();
    if (message_arena != submessage_arena) {
      server_def = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, server_def, submessage_arena);
    }
    
  } else {
    
  }
  server_def_ = server_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.CreateContextRequest.server_def)
}

// bool async = 2;
inline void CreateContextRequest::clear_async() {
  async_ = false;
}
inline bool CreateContextRequest::async() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.CreateContextRequest.async)
  return async_;
}
inline void CreateContextRequest::set_async(bool value) {
  
  async_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.CreateContextRequest.async)
}

// int64 keep_alive_secs = 3;
inline void CreateContextRequest::clear_keep_alive_secs() {
  keep_alive_secs_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CreateContextRequest::keep_alive_secs() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.CreateContextRequest.keep_alive_secs)
  return keep_alive_secs_;
}
inline void CreateContextRequest::set_keep_alive_secs(::google::protobuf::int64 value) {
  
  keep_alive_secs_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.CreateContextRequest.keep_alive_secs)
}

// .tensorflow.VersionDef version_def = 4;
inline bool CreateContextRequest::has_version_def() const {
  return this != internal_default_instance() && version_def_ != NULL;
}
inline const ::tensorflow::VersionDef& CreateContextRequest::_internal_version_def() const {
  return *version_def_;
}
inline const ::tensorflow::VersionDef& CreateContextRequest::version_def() const {
  const ::tensorflow::VersionDef* p = version_def_;
  // @@protoc_insertion_point(field_get:tensorflow.eager.CreateContextRequest.version_def)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::VersionDef*>(
      &::tensorflow::_VersionDef_default_instance_);
}
inline ::tensorflow::VersionDef* CreateContextRequest::release_version_def() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.CreateContextRequest.version_def)
  
  ::tensorflow::VersionDef* temp = version_def_;
  version_def_ = NULL;
  return temp;
}
inline ::tensorflow::VersionDef* CreateContextRequest::mutable_version_def() {
  
  if (version_def_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::VersionDef>(GetArenaNoVirtual());
    version_def_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.CreateContextRequest.version_def)
  return version_def_;
}
inline void CreateContextRequest::set_allocated_version_def(::tensorflow::VersionDef* version_def) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(version_def_);
  }
  if (version_def) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(version_def)->GetArena();
    if (message_arena != submessage_arena) {
      version_def = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, version_def, submessage_arena);
    }
    
  } else {
    
  }
  version_def_ = version_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.CreateContextRequest.version_def)
}

// int64 rendezvous_id = 5;
inline void CreateContextRequest::clear_rendezvous_id() {
  rendezvous_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CreateContextRequest::rendezvous_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.CreateContextRequest.rendezvous_id)
  return rendezvous_id_;
}
inline void CreateContextRequest::set_rendezvous_id(::google::protobuf::int64 value) {
  
  rendezvous_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.CreateContextRequest.rendezvous_id)
}

// -------------------------------------------------------------------

// CreateContextResponse

// fixed64 context_id = 1;
inline void CreateContextResponse::clear_context_id() {
  context_id_ = GOOGLE_ULONGLONG(0);
}
inline ::google::protobuf::uint64 CreateContextResponse::context_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.CreateContextResponse.context_id)
  return context_id_;
}
inline void CreateContextResponse::set_context_id(::google::protobuf::uint64 value) {
  
  context_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.CreateContextResponse.context_id)
}

// repeated .tensorflow.DeviceAttributes device_attributes = 2;
inline int CreateContextResponse::device_attributes_size() const {
  return device_attributes_.size();
}
inline ::tensorflow::DeviceAttributes* CreateContextResponse::mutable_device_attributes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.CreateContextResponse.device_attributes)
  return device_attributes_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
CreateContextResponse::mutable_device_attributes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.CreateContextResponse.device_attributes)
  return &device_attributes_;
}
inline const ::tensorflow::DeviceAttributes& CreateContextResponse::device_attributes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.CreateContextResponse.device_attributes)
  return device_attributes_.Get(index);
}
inline ::tensorflow::DeviceAttributes* CreateContextResponse::add_device_attributes() {
  // @@protoc_insertion_point(field_add:tensorflow.eager.CreateContextResponse.device_attributes)
  return device_attributes_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
CreateContextResponse::device_attributes() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.CreateContextResponse.device_attributes)
  return device_attributes_;
}

// -------------------------------------------------------------------

// EnqueueRequest

// fixed64 context_id = 1;
inline void EnqueueRequest::clear_context_id() {
  context_id_ = GOOGLE_ULONGLONG(0);
}
inline ::google::protobuf::uint64 EnqueueRequest::context_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.EnqueueRequest.context_id)
  return context_id_;
}
inline void EnqueueRequest::set_context_id(::google::protobuf::uint64 value) {
  
  context_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.EnqueueRequest.context_id)
}

// repeated .tensorflow.eager.QueueItem queue = 3;
inline int EnqueueRequest::queue_size() const {
  return queue_.size();
}
inline void EnqueueRequest::clear_queue() {
  queue_.Clear();
}
inline ::tensorflow::eager::QueueItem* EnqueueRequest::mutable_queue(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.EnqueueRequest.queue)
  return queue_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::eager::QueueItem >*
EnqueueRequest::mutable_queue() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.EnqueueRequest.queue)
  return &queue_;
}
inline const ::tensorflow::eager::QueueItem& EnqueueRequest::queue(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.EnqueueRequest.queue)
  return queue_.Get(index);
}
inline ::tensorflow::eager::QueueItem* EnqueueRequest::add_queue() {
  // @@protoc_insertion_point(field_add:tensorflow.eager.EnqueueRequest.queue)
  return queue_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::eager::QueueItem >&
EnqueueRequest::queue() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.EnqueueRequest.queue)
  return queue_;
}

// -------------------------------------------------------------------

// EnqueueResponse

// repeated .tensorflow.eager.QueueResponse queue_response = 1;
inline int EnqueueResponse::queue_response_size() const {
  return queue_response_.size();
}
inline void EnqueueResponse::clear_queue_response() {
  queue_response_.Clear();
}
inline ::tensorflow::eager::QueueResponse* EnqueueResponse::mutable_queue_response(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.EnqueueResponse.queue_response)
  return queue_response_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::eager::QueueResponse >*
EnqueueResponse::mutable_queue_response() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.EnqueueResponse.queue_response)
  return &queue_response_;
}
inline const ::tensorflow::eager::QueueResponse& EnqueueResponse::queue_response(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.EnqueueResponse.queue_response)
  return queue_response_.Get(index);
}
inline ::tensorflow::eager::QueueResponse* EnqueueResponse::add_queue_response() {
  // @@protoc_insertion_point(field_add:tensorflow.eager.EnqueueResponse.queue_response)
  return queue_response_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::eager::QueueResponse >&
EnqueueResponse::queue_response() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.EnqueueResponse.queue_response)
  return queue_response_;
}

// -------------------------------------------------------------------

// WaitQueueDoneRequest

// fixed64 context_id = 1;
inline void WaitQueueDoneRequest::clear_context_id() {
  context_id_ = GOOGLE_ULONGLONG(0);
}
inline ::google::protobuf::uint64 WaitQueueDoneRequest::context_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.WaitQueueDoneRequest.context_id)
  return context_id_;
}
inline void WaitQueueDoneRequest::set_context_id(::google::protobuf::uint64 value) {
  
  context_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.WaitQueueDoneRequest.context_id)
}

// repeated int64 op_id = 2;
inline int WaitQueueDoneRequest::op_id_size() const {
  return op_id_.size();
}
inline void WaitQueueDoneRequest::clear_op_id() {
  op_id_.Clear();
}
inline ::google::protobuf::int64 WaitQueueDoneRequest::op_id(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.WaitQueueDoneRequest.op_id)
  return op_id_.Get(index);
}
inline void WaitQueueDoneRequest::set_op_id(int index, ::google::protobuf::int64 value) {
  op_id_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.WaitQueueDoneRequest.op_id)
}
inline void WaitQueueDoneRequest::add_op_id(::google::protobuf::int64 value) {
  op_id_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.eager.WaitQueueDoneRequest.op_id)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
WaitQueueDoneRequest::op_id() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.WaitQueueDoneRequest.op_id)
  return op_id_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
WaitQueueDoneRequest::mutable_op_id() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.WaitQueueDoneRequest.op_id)
  return &op_id_;
}

// -------------------------------------------------------------------

// WaitQueueDoneResponse

// -------------------------------------------------------------------

// KeepAliveRequest

// fixed64 context_id = 1;
inline void KeepAliveRequest::clear_context_id() {
  context_id_ = GOOGLE_ULONGLONG(0);
}
inline ::google::protobuf::uint64 KeepAliveRequest::context_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.KeepAliveRequest.context_id)
  return context_id_;
}
inline void KeepAliveRequest::set_context_id(::google::protobuf::uint64 value) {
  
  context_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.KeepAliveRequest.context_id)
}

// -------------------------------------------------------------------

// KeepAliveResponse

// -------------------------------------------------------------------

// CloseContextRequest

// fixed64 context_id = 1;
inline void CloseContextRequest::clear_context_id() {
  context_id_ = GOOGLE_ULONGLONG(0);
}
inline ::google::protobuf::uint64 CloseContextRequest::context_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.CloseContextRequest.context_id)
  return context_id_;
}
inline void CloseContextRequest::set_context_id(::google::protobuf::uint64 value) {
  
  context_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.CloseContextRequest.context_id)
}

// -------------------------------------------------------------------

// CloseContextResponse

// -------------------------------------------------------------------

// RegisterFunctionRequest

// fixed64 context_id = 1;
inline void RegisterFunctionRequest::clear_context_id() {
  context_id_ = GOOGLE_ULONGLONG(0);
}
inline ::google::protobuf::uint64 RegisterFunctionRequest::context_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.RegisterFunctionRequest.context_id)
  return context_id_;
}
inline void RegisterFunctionRequest::set_context_id(::google::protobuf::uint64 value) {
  
  context_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.RegisterFunctionRequest.context_id)
}

// .tensorflow.FunctionDef function_def = 2;
inline bool RegisterFunctionRequest::has_function_def() const {
  return this != internal_default_instance() && function_def_ != NULL;
}
inline const ::tensorflow::FunctionDef& RegisterFunctionRequest::_internal_function_def() const {
  return *function_def_;
}
inline const ::tensorflow::FunctionDef& RegisterFunctionRequest::function_def() const {
  const ::tensorflow::FunctionDef* p = function_def_;
  // @@protoc_insertion_point(field_get:tensorflow.eager.RegisterFunctionRequest.function_def)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::FunctionDef*>(
      &::tensorflow::_FunctionDef_default_instance_);
}
inline ::tensorflow::FunctionDef* RegisterFunctionRequest::release_function_def() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.RegisterFunctionRequest.function_def)
  
  ::tensorflow::FunctionDef* temp = function_def_;
  function_def_ = NULL;
  return temp;
}
inline ::tensorflow::FunctionDef* RegisterFunctionRequest::mutable_function_def() {
  
  if (function_def_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::FunctionDef>(GetArenaNoVirtual());
    function_def_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.RegisterFunctionRequest.function_def)
  return function_def_;
}
inline void RegisterFunctionRequest::set_allocated_function_def(::tensorflow::FunctionDef* function_def) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(function_def_);
  }
  if (function_def) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(function_def)->GetArena();
    if (message_arena != submessage_arena) {
      function_def = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, function_def, submessage_arena);
    }
    
  } else {
    
  }
  function_def_ = function_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.RegisterFunctionRequest.function_def)
}

// -------------------------------------------------------------------

// RegisterFunctionResponse

// -------------------------------------------------------------------

// SendTensorRequest

// fixed64 context_id = 1;
inline void SendTensorRequest::clear_context_id() {
  context_id_ = GOOGLE_ULONGLONG(0);
}
inline ::google::protobuf::uint64 SendTensorRequest::context_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.SendTensorRequest.context_id)
  return context_id_;
}
inline void SendTensorRequest::set_context_id(::google::protobuf::uint64 value) {
  
  context_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.SendTensorRequest.context_id)
}

// int64 op_id = 2;
inline void SendTensorRequest::clear_op_id() {
  op_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SendTensorRequest::op_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.SendTensorRequest.op_id)
  return op_id_;
}
inline void SendTensorRequest::set_op_id(::google::protobuf::int64 value) {
  
  op_id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.eager.SendTensorRequest.op_id)
}

// repeated .tensorflow.TensorProto tensors = 3;
inline int SendTensorRequest::tensors_size() const {
  return tensors_.size();
}
inline ::tensorflow::TensorProto* SendTensorRequest::mutable_tensors(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.SendTensorRequest.tensors)
  return tensors_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorProto >*
SendTensorRequest::mutable_tensors() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.SendTensorRequest.tensors)
  return &tensors_;
}
inline const ::tensorflow::TensorProto& SendTensorRequest::tensors(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.SendTensorRequest.tensors)
  return tensors_.Get(index);
}
inline ::tensorflow::TensorProto* SendTensorRequest::add_tensors() {
  // @@protoc_insertion_point(field_add:tensorflow.eager.SendTensorRequest.tensors)
  return tensors_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::TensorProto >&
SendTensorRequest::tensors() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.SendTensorRequest.tensors)
  return tensors_;
}

// string device_name = 4;
inline void SendTensorRequest::clear_device_name() {
  device_name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SendTensorRequest::device_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.SendTensorRequest.device_name)
  return device_name_.GetNoArena();
}
inline void SendTensorRequest::set_device_name(const ::std::string& value) {
  
  device_name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.SendTensorRequest.device_name)
}
#if LANG_CXX11
inline void SendTensorRequest::set_device_name(::std::string&& value) {
  
  device_name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.eager.SendTensorRequest.device_name)
}
#endif
inline void SendTensorRequest::set_device_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  device_name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.eager.SendTensorRequest.device_name)
}
inline void SendTensorRequest::set_device_name(const char* value, size_t size) {
  
  device_name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.eager.SendTensorRequest.device_name)
}
inline ::std::string* SendTensorRequest::mutable_device_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.SendTensorRequest.device_name)
  return device_name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SendTensorRequest::release_device_name() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.SendTensorRequest.device_name)
  
  return device_name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SendTensorRequest::set_allocated_device_name(::std::string* device_name) {
  if (device_name != NULL) {
    
  } else {
    
  }
  device_name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), device_name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.SendTensorRequest.device_name)
}

// -------------------------------------------------------------------

// SendTensorResponse

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace eager
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/tpu/optimization_parameters.proto

#include "tensorflow/core/protobuf/tpu/optimization_parameters.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_google_2fprotobuf_2fwrappers_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_google_2fprotobuf_2fwrappers_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_FloatValue;
}  // namespace protobuf_google_2fprotobuf_2fwrappers_2eproto
namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_AdadeltaParameters;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_AdagradParameters;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_AdamParameters;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_CenteredRmsPropParameters;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_DynamicLearningRate;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_FtrlParameters;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_HotIdOptimizerConfiguration;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_MdlAdagradLightParameters;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_MomentumParameters;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_ProximalAdagradParameters;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_RmsPropParameters;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_StateVariableSpecification_FillWithConstant;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_StateVariableSpecification_UserDefined;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_StochasticGradientDescentParameters;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_ClippingLimits;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_LearningRate;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto
namespace tensorflow {
namespace tpu {
class ClippingLimitsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ClippingLimits>
      _instance;
} _ClippingLimits_default_instance_;
class DynamicLearningRateDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<DynamicLearningRate>
      _instance;
} _DynamicLearningRate_default_instance_;
class LearningRateDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<LearningRate>
      _instance;
  float constant_;
  const ::tensorflow::tpu::DynamicLearningRate* dynamic_;
} _LearningRate_default_instance_;
class AdagradParametersDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<AdagradParameters>
      _instance;
} _AdagradParameters_default_instance_;
class StochasticGradientDescentParametersDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<StochasticGradientDescentParameters>
      _instance;
} _StochasticGradientDescentParameters_default_instance_;
class FtrlParametersDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<FtrlParameters>
      _instance;
} _FtrlParameters_default_instance_;
class AdamParametersDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<AdamParameters>
      _instance;
} _AdamParameters_default_instance_;
class MomentumParametersDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<MomentumParameters>
      _instance;
} _MomentumParameters_default_instance_;
class RmsPropParametersDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<RmsPropParameters>
      _instance;
} _RmsPropParameters_default_instance_;
class CenteredRmsPropParametersDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CenteredRmsPropParameters>
      _instance;
} _CenteredRmsPropParameters_default_instance_;
class MdlAdagradLightParametersDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<MdlAdagradLightParameters>
      _instance;
} _MdlAdagradLightParameters_default_instance_;
class AdadeltaParametersDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<AdadeltaParameters>
      _instance;
} _AdadeltaParameters_default_instance_;
class ProximalAdagradParametersDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ProximalAdagradParameters>
      _instance;
} _ProximalAdagradParameters_default_instance_;
class GradientAccumulationStatusDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<GradientAccumulationStatus>
      _instance;
} _GradientAccumulationStatus_default_instance_;
class HotIdOptimizerConfigurationDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<HotIdOptimizerConfiguration>
      _instance;
} _HotIdOptimizerConfiguration_default_instance_;
class OptimizationParametersDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<OptimizationParameters>
      _instance;
  const ::tensorflow::tpu::AdagradParameters* adagrad_;
  const ::tensorflow::tpu::StochasticGradientDescentParameters* stochastic_gradient_descent_;
  const ::tensorflow::tpu::FtrlParameters* ftrl_;
  const ::tensorflow::tpu::AdamParameters* adam_;
  const ::tensorflow::tpu::MomentumParameters* momentum_;
  const ::tensorflow::tpu::RmsPropParameters* rms_prop_;
  const ::tensorflow::tpu::CenteredRmsPropParameters* centered_rms_prop_;
  const ::tensorflow::tpu::MdlAdagradLightParameters* mdl_adagrad_light_;
  const ::tensorflow::tpu::AdadeltaParameters* adadelta_;
  const ::tensorflow::tpu::ProximalAdagradParameters* proximal_adagrad_;
} _OptimizationParameters_default_instance_;
class StateVariableSpecification_UserDefinedDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<StateVariableSpecification_UserDefined>
      _instance;
} _StateVariableSpecification_UserDefined_default_instance_;
class StateVariableSpecification_FillWithConstantDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<StateVariableSpecification_FillWithConstant>
      _instance;
} _StateVariableSpecification_FillWithConstant_default_instance_;
class StateVariableSpecificationDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<StateVariableSpecification>
      _instance;
  const ::tensorflow::tpu::StateVariableSpecification_UserDefined* user_defined_;
  const ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* fill_with_constant_;
} _StateVariableSpecification_default_instance_;
}  // namespace tpu
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto {
static void InitDefaultsClippingLimits() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tpu::_ClippingLimits_default_instance_;
    new (ptr) ::tensorflow::tpu::ClippingLimits();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tpu::ClippingLimits::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_ClippingLimits =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsClippingLimits}, {
      &protobuf_google_2fprotobuf_2fwrappers_2eproto::scc_info_FloatValue.base,}};

static void InitDefaultsDynamicLearningRate() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tpu::_DynamicLearningRate_default_instance_;
    new (ptr) ::tensorflow::tpu::DynamicLearningRate();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tpu::DynamicLearningRate::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_DynamicLearningRate =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsDynamicLearningRate}, {}};

static void InitDefaultsLearningRate() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tpu::_LearningRate_default_instance_;
    new (ptr) ::tensorflow::tpu::LearningRate();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tpu::LearningRate::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_LearningRate =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsLearningRate}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_DynamicLearningRate.base,}};

static void InitDefaultsAdagradParameters() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tpu::_AdagradParameters_default_instance_;
    new (ptr) ::tensorflow::tpu::AdagradParameters();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tpu::AdagradParameters::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_AdagradParameters =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsAdagradParameters}, {}};

static void InitDefaultsStochasticGradientDescentParameters() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tpu::_StochasticGradientDescentParameters_default_instance_;
    new (ptr) ::tensorflow::tpu::StochasticGradientDescentParameters();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tpu::StochasticGradientDescentParameters::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_StochasticGradientDescentParameters =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsStochasticGradientDescentParameters}, {}};

static void InitDefaultsFtrlParameters() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tpu::_FtrlParameters_default_instance_;
    new (ptr) ::tensorflow::tpu::FtrlParameters();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tpu::FtrlParameters::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_FtrlParameters =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsFtrlParameters}, {}};

static void InitDefaultsAdamParameters() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tpu::_AdamParameters_default_instance_;
    new (ptr) ::tensorflow::tpu::AdamParameters();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tpu::AdamParameters::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_AdamParameters =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsAdamParameters}, {}};

static void InitDefaultsMomentumParameters() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tpu::_MomentumParameters_default_instance_;
    new (ptr) ::tensorflow::tpu::MomentumParameters();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tpu::MomentumParameters::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_MomentumParameters =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsMomentumParameters}, {}};

static void InitDefaultsRmsPropParameters() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tpu::_RmsPropParameters_default_instance_;
    new (ptr) ::tensorflow::tpu::RmsPropParameters();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tpu::RmsPropParameters::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_RmsPropParameters =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsRmsPropParameters}, {}};

static void InitDefaultsCenteredRmsPropParameters() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tpu::_CenteredRmsPropParameters_default_instance_;
    new (ptr) ::tensorflow::tpu::CenteredRmsPropParameters();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tpu::CenteredRmsPropParameters::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_CenteredRmsPropParameters =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsCenteredRmsPropParameters}, {}};

static void InitDefaultsMdlAdagradLightParameters() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tpu::_MdlAdagradLightParameters_default_instance_;
    new (ptr) ::tensorflow::tpu::MdlAdagradLightParameters();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tpu::MdlAdagradLightParameters::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_MdlAdagradLightParameters =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsMdlAdagradLightParameters}, {}};

static void InitDefaultsAdadeltaParameters() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tpu::_AdadeltaParameters_default_instance_;
    new (ptr) ::tensorflow::tpu::AdadeltaParameters();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tpu::AdadeltaParameters::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_AdadeltaParameters =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsAdadeltaParameters}, {}};

static void InitDefaultsProximalAdagradParameters() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tpu::_ProximalAdagradParameters_default_instance_;
    new (ptr) ::tensorflow::tpu::ProximalAdagradParameters();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tpu::ProximalAdagradParameters::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_ProximalAdagradParameters =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsProximalAdagradParameters}, {}};

static void InitDefaultsGradientAccumulationStatus() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tpu::_GradientAccumulationStatus_default_instance_;
    new (ptr) ::tensorflow::tpu::GradientAccumulationStatus();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tpu::GradientAccumulationStatus::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_GradientAccumulationStatus =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsGradientAccumulationStatus}, {}};

static void InitDefaultsHotIdOptimizerConfiguration() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tpu::_HotIdOptimizerConfiguration_default_instance_;
    new (ptr) ::tensorflow::tpu::HotIdOptimizerConfiguration();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tpu::HotIdOptimizerConfiguration::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_HotIdOptimizerConfiguration =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsHotIdOptimizerConfiguration}, {}};

static void InitDefaultsOptimizationParameters() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tpu::_OptimizationParameters_default_instance_;
    new (ptr) ::tensorflow::tpu::OptimizationParameters();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tpu::OptimizationParameters::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<13> scc_info_OptimizationParameters =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 13, InitDefaultsOptimizationParameters}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_LearningRate.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_ClippingLimits.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_HotIdOptimizerConfiguration.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_AdagradParameters.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_StochasticGradientDescentParameters.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_FtrlParameters.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_AdamParameters.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_MomentumParameters.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_RmsPropParameters.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_CenteredRmsPropParameters.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_MdlAdagradLightParameters.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_AdadeltaParameters.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_ProximalAdagradParameters.base,}};

static void InitDefaultsStateVariableSpecification_UserDefined() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tpu::_StateVariableSpecification_UserDefined_default_instance_;
    new (ptr) ::tensorflow::tpu::StateVariableSpecification_UserDefined();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tpu::StateVariableSpecification_UserDefined::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_StateVariableSpecification_UserDefined =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsStateVariableSpecification_UserDefined}, {}};

static void InitDefaultsStateVariableSpecification_FillWithConstant() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tpu::_StateVariableSpecification_FillWithConstant_default_instance_;
    new (ptr) ::tensorflow::tpu::StateVariableSpecification_FillWithConstant();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tpu::StateVariableSpecification_FillWithConstant::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_StateVariableSpecification_FillWithConstant =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsStateVariableSpecification_FillWithConstant}, {}};

static void InitDefaultsStateVariableSpecification() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::tpu::_StateVariableSpecification_default_instance_;
    new (ptr) ::tensorflow::tpu::StateVariableSpecification();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::tpu::StateVariableSpecification::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_StateVariableSpecification =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsStateVariableSpecification}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_StateVariableSpecification_UserDefined.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_StateVariableSpecification_FillWithConstant.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_ClippingLimits.base);
  ::google::protobuf::internal::InitSCC(&scc_info_DynamicLearningRate.base);
  ::google::protobuf::internal::InitSCC(&scc_info_LearningRate.base);
  ::google::protobuf::internal::InitSCC(&scc_info_AdagradParameters.base);
  ::google::protobuf::internal::InitSCC(&scc_info_StochasticGradientDescentParameters.base);
  ::google::protobuf::internal::InitSCC(&scc_info_FtrlParameters.base);
  ::google::protobuf::internal::InitSCC(&scc_info_AdamParameters.base);
  ::google::protobuf::internal::InitSCC(&scc_info_MomentumParameters.base);
  ::google::protobuf::internal::InitSCC(&scc_info_RmsPropParameters.base);
  ::google::protobuf::internal::InitSCC(&scc_info_CenteredRmsPropParameters.base);
  ::google::protobuf::internal::InitSCC(&scc_info_MdlAdagradLightParameters.base);
  ::google::protobuf::internal::InitSCC(&scc_info_AdadeltaParameters.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ProximalAdagradParameters.base);
  ::google::protobuf::internal::InitSCC(&scc_info_GradientAccumulationStatus.base);
  ::google::protobuf::internal::InitSCC(&scc_info_HotIdOptimizerConfiguration.base);
  ::google::protobuf::internal::InitSCC(&scc_info_OptimizationParameters.base);
  ::google::protobuf::internal::InitSCC(&scc_info_StateVariableSpecification_UserDefined.base);
  ::google::protobuf::internal::InitSCC(&scc_info_StateVariableSpecification_FillWithConstant.base);
  ::google::protobuf::internal::InitSCC(&scc_info_StateVariableSpecification.base);
}

::google::protobuf::Metadata file_level_metadata[19];
const ::google::protobuf::EnumDescriptor* file_level_enum_descriptors[2];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::ClippingLimits, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::ClippingLimits, lower_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::ClippingLimits, upper_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::DynamicLearningRate, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::DynamicLearningRate, tag_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::LearningRate, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::LearningRate, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  offsetof(::tensorflow::tpu::LearningRateDefaultTypeInternal, constant_),
  offsetof(::tensorflow::tpu::LearningRateDefaultTypeInternal, dynamic_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::LearningRate, learning_rate_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::AdagradParameters, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::AdagradParameters, initial_accumulator_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::StochasticGradientDescentParameters, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::FtrlParameters, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::FtrlParameters, l1_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::FtrlParameters, l2_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::FtrlParameters, lr_power_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::FtrlParameters, initial_accum_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::FtrlParameters, initial_linear_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::AdamParameters, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::AdamParameters, beta1_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::AdamParameters, beta2_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::AdamParameters, epsilon_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::AdamParameters, initial_m_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::AdamParameters, initial_v_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::AdamParameters, use_non_lazy_adam_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::AdamParameters, use_sum_inside_sqrt_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::MomentumParameters, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::MomentumParameters, momentum_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::MomentumParameters, use_nesterov_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::MomentumParameters, initial_accum_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::RmsPropParameters, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::RmsPropParameters, rho_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::RmsPropParameters, momentum_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::RmsPropParameters, epsilon_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::RmsPropParameters, initial_ms_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::RmsPropParameters, initial_mom_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::CenteredRmsPropParameters, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::CenteredRmsPropParameters, rho_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::CenteredRmsPropParameters, momentum_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::CenteredRmsPropParameters, epsilon_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::CenteredRmsPropParameters, initial_ms_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::CenteredRmsPropParameters, initial_mom_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::CenteredRmsPropParameters, initial_mg_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::MdlAdagradLightParameters, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::MdlAdagradLightParameters, l2_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::MdlAdagradLightParameters, lr_power_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::MdlAdagradLightParameters, min_servable_mdl_benefit_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::MdlAdagradLightParameters, mdl_mix_in_margin_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::MdlAdagradLightParameters, mdl_benefit_rampup_coeff_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::MdlAdagradLightParameters, mdl_min_weight_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::MdlAdagradLightParameters, benefit_revisit_scale_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::MdlAdagradLightParameters, max_event_benefit_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::MdlAdagradLightParameters, max_total_benefit_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::MdlAdagradLightParameters, mdl_hard_limit_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::MdlAdagradLightParameters, hard_limit_min_benefit_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::MdlAdagradLightParameters, mdl_regularize_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::MdlAdagradLightParameters, initial_accumulator_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::MdlAdagradLightParameters, initial_weight_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::MdlAdagradLightParameters, initial_benefit_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::AdadeltaParameters, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::AdadeltaParameters, rho_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::AdadeltaParameters, epsilon_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::AdadeltaParameters, initial_accumulator_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::AdadeltaParameters, initial_update_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::ProximalAdagradParameters, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::ProximalAdagradParameters, l1_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::ProximalAdagradParameters, l2_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::ProximalAdagradParameters, initial_accumulator_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::GradientAccumulationStatus, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::HotIdOptimizerConfiguration, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::HotIdOptimizerConfiguration, status_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::HotIdOptimizerConfiguration, frequency_threshold_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::HotIdOptimizerConfiguration, max_id_count_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::HotIdOptimizerConfiguration, max_slot_count_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::OptimizationParameters, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::OptimizationParameters, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::OptimizationParameters, learning_rate_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::OptimizationParameters, clipping_limits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::OptimizationParameters, gradient_clipping_limits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::OptimizationParameters, weight_decay_factor_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::OptimizationParameters, gradient_accumulation_status_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::OptimizationParameters, hot_id_optimizer_configuration_),
  offsetof(::tensorflow::tpu::OptimizationParametersDefaultTypeInternal, adagrad_),
  offsetof(::tensorflow::tpu::OptimizationParametersDefaultTypeInternal, stochastic_gradient_descent_),
  offsetof(::tensorflow::tpu::OptimizationParametersDefaultTypeInternal, ftrl_),
  offsetof(::tensorflow::tpu::OptimizationParametersDefaultTypeInternal, adam_),
  offsetof(::tensorflow::tpu::OptimizationParametersDefaultTypeInternal, momentum_),
  offsetof(::tensorflow::tpu::OptimizationParametersDefaultTypeInternal, rms_prop_),
  offsetof(::tensorflow::tpu::OptimizationParametersDefaultTypeInternal, centered_rms_prop_),
  offsetof(::tensorflow::tpu::OptimizationParametersDefaultTypeInternal, mdl_adagrad_light_),
  offsetof(::tensorflow::tpu::OptimizationParametersDefaultTypeInternal, adadelta_),
  offsetof(::tensorflow::tpu::OptimizationParametersDefaultTypeInternal, proximal_adagrad_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::OptimizationParameters, parameters_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::StateVariableSpecification_UserDefined, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::StateVariableSpecification_UserDefined, padding_initial_value_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::StateVariableSpecification_FillWithConstant, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::StateVariableSpecification_FillWithConstant, initial_value_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::StateVariableSpecification, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::StateVariableSpecification, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::StateVariableSpecification, name_),
  offsetof(::tensorflow::tpu::StateVariableSpecificationDefaultTypeInternal, user_defined_),
  offsetof(::tensorflow::tpu::StateVariableSpecificationDefaultTypeInternal, fill_with_constant_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::tpu::StateVariableSpecification, usage_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::tpu::ClippingLimits)},
  { 7, -1, sizeof(::tensorflow::tpu::DynamicLearningRate)},
  { 13, -1, sizeof(::tensorflow::tpu::LearningRate)},
  { 21, -1, sizeof(::tensorflow::tpu::AdagradParameters)},
  { 27, -1, sizeof(::tensorflow::tpu::StochasticGradientDescentParameters)},
  { 32, -1, sizeof(::tensorflow::tpu::FtrlParameters)},
  { 42, -1, sizeof(::tensorflow::tpu::AdamParameters)},
  { 54, -1, sizeof(::tensorflow::tpu::MomentumParameters)},
  { 62, -1, sizeof(::tensorflow::tpu::RmsPropParameters)},
  { 72, -1, sizeof(::tensorflow::tpu::CenteredRmsPropParameters)},
  { 83, -1, sizeof(::tensorflow::tpu::MdlAdagradLightParameters)},
  { 103, -1, sizeof(::tensorflow::tpu::AdadeltaParameters)},
  { 112, -1, sizeof(::tensorflow::tpu::ProximalAdagradParameters)},
  { 120, -1, sizeof(::tensorflow::tpu::GradientAccumulationStatus)},
  { 125, -1, sizeof(::tensorflow::tpu::HotIdOptimizerConfiguration)},
  { 134, -1, sizeof(::tensorflow::tpu::OptimizationParameters)},
  { 156, -1, sizeof(::tensorflow::tpu::StateVariableSpecification_UserDefined)},
  { 162, -1, sizeof(::tensorflow::tpu::StateVariableSpecification_FillWithConstant)},
  { 168, -1, sizeof(::tensorflow::tpu::StateVariableSpecification)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tpu::_ClippingLimits_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tpu::_DynamicLearningRate_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tpu::_LearningRate_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tpu::_AdagradParameters_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tpu::_StochasticGradientDescentParameters_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tpu::_FtrlParameters_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tpu::_AdamParameters_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tpu::_MomentumParameters_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tpu::_RmsPropParameters_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tpu::_CenteredRmsPropParameters_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tpu::_MdlAdagradLightParameters_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tpu::_AdadeltaParameters_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tpu::_ProximalAdagradParameters_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tpu::_GradientAccumulationStatus_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tpu::_HotIdOptimizerConfiguration_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tpu::_OptimizationParameters_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tpu::_StateVariableSpecification_UserDefined_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tpu::_StateVariableSpecification_FillWithConstant_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::tpu::_StateVariableSpecification_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/protobuf/tpu/optimization_parameters.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, file_level_enum_descriptors, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 19);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n:tensorflow/core/protobuf/tpu/optimizat"
      "ion_parameters.proto\022\016tensorflow.tpu\032\036go"
      "ogle/protobuf/wrappers.proto\"h\n\016Clipping"
      "Limits\022*\n\005lower\030\001 \001(\0132\033.google.protobuf."
      "FloatValue\022*\n\005upper\030\002 \001(\0132\033.google.proto"
      "buf.FloatValue\"\"\n\023DynamicLearningRate\022\013\n"
      "\003tag\030\001 \001(\005\"k\n\014LearningRate\022\022\n\010constant\030\001"
      " \001(\002H\000\0226\n\007dynamic\030\002 \001(\0132#.tensorflow.tpu"
      ".DynamicLearningRateH\000B\017\n\rlearning_rate\""
      "0\n\021AdagradParameters\022\033\n\023initial_accumula"
      "tor\030\001 \001(\002\"%\n#StochasticGradientDescentPa"
      "rameters\"i\n\016FtrlParameters\022\n\n\002l1\030\001 \001(\002\022\n"
      "\n\002l2\030\002 \001(\002\022\020\n\010lr_power\030\003 \001(\002\022\025\n\rinitial_"
      "accum\030\004 \001(\002\022\026\n\016initial_linear\030\005 \001(\002\"\235\001\n\016"
      "AdamParameters\022\r\n\005beta1\030\003 \001(\002\022\r\n\005beta2\030\004"
      " \001(\002\022\017\n\007epsilon\030\005 \001(\002\022\021\n\tinitial_m\030\006 \001(\002"
      "\022\021\n\tinitial_v\030\007 \001(\002\022\031\n\021use_non_lazy_adam"
      "\030\010 \001(\010\022\033\n\023use_sum_inside_sqrt\030\n \001(\010\"S\n\022M"
      "omentumParameters\022\020\n\010momentum\030\001 \001(\002\022\024\n\014u"
      "se_nesterov\030\002 \001(\010\022\025\n\rinitial_accum\030\003 \001(\002"
      "\"l\n\021RmsPropParameters\022\013\n\003rho\030\001 \001(\002\022\020\n\010mo"
      "mentum\030\002 \001(\002\022\017\n\007epsilon\030\003 \001(\002\022\022\n\ninitial"
      "_ms\030\004 \001(\002\022\023\n\013initial_mom\030\005 \001(\002\"\210\001\n\031Cente"
      "redRmsPropParameters\022\013\n\003rho\030\001 \001(\002\022\020\n\010mom"
      "entum\030\002 \001(\002\022\017\n\007epsilon\030\003 \001(\002\022\022\n\ninitial_"
      "ms\030\004 \001(\002\022\023\n\013initial_mom\030\005 \001(\002\022\022\n\ninitial"
      "_mg\030\006 \001(\002\"\243\003\n\031MdlAdagradLightParameters\022"
      "\n\n\002l2\030\001 \001(\002\022\020\n\010lr_power\030\002 \001(\002\022 \n\030min_ser"
      "vable_mdl_benefit\030\003 \001(\002\022\031\n\021mdl_mix_in_ma"
      "rgin\030\004 \001(\002\022 \n\030mdl_benefit_rampup_coeff\030\005"
      " \001(\002\022\026\n\016mdl_min_weight\030\006 \001(\002\022\035\n\025benefit_"
      "revisit_scale\030\007 \001(\002\022\031\n\021max_event_benefit"
      "\030\010 \001(\002\022\031\n\021max_total_benefit\030\t \001(\002\022\026\n\016mdl"
      "_hard_limit\030\n \001(\002\022\036\n\026hard_limit_min_bene"
      "fit\030\013 \001(\010\022\026\n\016mdl_regularize\030\014 \001(\010\022\033\n\023ini"
      "tial_accumulator\030\r \001(\002\022\026\n\016initial_weight"
      "\030\016 \001(\002\022\027\n\017initial_benefit\030\017 \001(\002\"g\n\022Adade"
      "ltaParameters\022\013\n\003rho\030\001 \001(\002\022\017\n\007epsilon\030\002 "
      "\001(\002\022\033\n\023initial_accumulator\030\003 \001(\002\022\026\n\016init"
      "ial_update\030\004 \001(\002\"P\n\031ProximalAdagradParam"
      "eters\022\n\n\002l1\030\001 \001(\002\022\n\n\002l2\030\002 \001(\002\022\033\n\023initial"
      "_accumulator\030\003 \001(\002\"R\n\032GradientAccumulati"
      "onStatus\"4\n\006Status\022\017\n\013UNSPECIFIED\020\000\022\013\n\007E"
      "NABLED\020\001\022\014\n\010DISABLED\020\002\"\342\001\n\033HotIdOptimize"
      "rConfiguration\022B\n\006status\030\001 \001(\01622.tensorf"
      "low.tpu.HotIdOptimizerConfiguration.Stat"
      "us\022\033\n\023frequency_threshold\030\002 \001(\002\022\024\n\014max_i"
      "d_count\030\003 \001(\005\022\026\n\016max_slot_count\030\004 \001(\005\"4\n"
      "\006Status\022\017\n\013UNSPECIFIED\020\000\022\013\n\007ENABLED\020\001\022\014\n"
      "\010DISABLED\020\002\"\235\010\n\026OptimizationParameters\0223"
      "\n\rlearning_rate\030\r \001(\0132\034.tensorflow.tpu.L"
      "earningRate\0227\n\017clipping_limits\030\002 \001(\0132\036.t"
      "ensorflow.tpu.ClippingLimits\022@\n\030gradient"
      "_clipping_limits\030\007 \001(\0132\036.tensorflow.tpu."
      "ClippingLimits\022\033\n\023weight_decay_factor\030\020 "
      "\001(\002\022W\n\034gradient_accumulation_status\030\021 \001("
      "\01621.tensorflow.tpu.GradientAccumulationS"
      "tatus.Status\022S\n\036hot_id_optimizer_configu"
      "ration\030\022 \001(\0132+.tensorflow.tpu.HotIdOptim"
      "izerConfiguration\0224\n\007adagrad\030\003 \001(\0132!.ten"
      "sorflow.tpu.AdagradParametersH\000\022Z\n\033stoch"
      "astic_gradient_descent\030\004 \001(\01323.tensorflo"
      "w.tpu.StochasticGradientDescentParameter"
      "sH\000\022.\n\004ftrl\030\005 \001(\0132\036.tensorflow.tpu.FtrlP"
      "arametersH\000\022.\n\004adam\030\006 \001(\0132\036.tensorflow.t"
      "pu.AdamParametersH\000\0226\n\010momentum\030\010 \001(\0132\"."
      "tensorflow.tpu.MomentumParametersH\000\0225\n\010r"
      "ms_prop\030\t \001(\0132!.tensorflow.tpu.RmsPropPa"
      "rametersH\000\022F\n\021centered_rms_prop\030\n \001(\0132)."
      "tensorflow.tpu.CenteredRmsPropParameters"
      "H\000\022F\n\021mdl_adagrad_light\030\013 \001(\0132).tensorfl"
      "ow.tpu.MdlAdagradLightParametersH\000\0226\n\010ad"
      "adelta\030\014 \001(\0132\".tensorflow.tpu.AdadeltaPa"
      "rametersH\000\022E\n\020proximal_adagrad\030\016 \001(\0132).t"
      "ensorflow.tpu.ProximalAdagradParametersH"
      "\000B\014\n\nparametersJ\004\010\001\020\002J\004\010\017\020\020\"\267\002\n\032StateVar"
      "iableSpecification\022\014\n\004name\030\001 \001(\t\022N\n\014user"
      "_defined\030\002 \001(\01326.tensorflow.tpu.StateVar"
      "iableSpecification.UserDefinedH\000\022Y\n\022fill"
      "_with_constant\030\003 \001(\0132;.tensorflow.tpu.St"
      "ateVariableSpecification.FillWithConstan"
      "tH\000\032,\n\013UserDefined\022\035\n\025padding_initial_va"
      "lue\030\001 \001(\001\032)\n\020FillWithConstant\022\025\n\rinitial"
      "_value\030\001 \001(\001B\007\n\005usageb\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 3349);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/protobuf/tpu/optimization_parameters.proto", &protobuf_RegisterTypes);
  ::protobuf_google_2fprotobuf_2fwrappers_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto
namespace tensorflow {
namespace tpu {
const ::google::protobuf::EnumDescriptor* GradientAccumulationStatus_Status_descriptor() {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_enum_descriptors[0];
}
bool GradientAccumulationStatus_Status_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const GradientAccumulationStatus_Status GradientAccumulationStatus::UNSPECIFIED;
const GradientAccumulationStatus_Status GradientAccumulationStatus::ENABLED;
const GradientAccumulationStatus_Status GradientAccumulationStatus::DISABLED;
const GradientAccumulationStatus_Status GradientAccumulationStatus::Status_MIN;
const GradientAccumulationStatus_Status GradientAccumulationStatus::Status_MAX;
const int GradientAccumulationStatus::Status_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900
const ::google::protobuf::EnumDescriptor* HotIdOptimizerConfiguration_Status_descriptor() {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_enum_descriptors[1];
}
bool HotIdOptimizerConfiguration_Status_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const HotIdOptimizerConfiguration_Status HotIdOptimizerConfiguration::UNSPECIFIED;
const HotIdOptimizerConfiguration_Status HotIdOptimizerConfiguration::ENABLED;
const HotIdOptimizerConfiguration_Status HotIdOptimizerConfiguration::DISABLED;
const HotIdOptimizerConfiguration_Status HotIdOptimizerConfiguration::Status_MIN;
const HotIdOptimizerConfiguration_Status HotIdOptimizerConfiguration::Status_MAX;
const int HotIdOptimizerConfiguration::Status_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

// ===================================================================

void ClippingLimits::InitAsDefaultInstance() {
  ::tensorflow::tpu::_ClippingLimits_default_instance_._instance.get_mutable()->lower_ = const_cast< ::google::protobuf::FloatValue*>(
      ::google::protobuf::FloatValue::internal_default_instance());
  ::tensorflow::tpu::_ClippingLimits_default_instance_._instance.get_mutable()->upper_ = const_cast< ::google::protobuf::FloatValue*>(
      ::google::protobuf::FloatValue::internal_default_instance());
}
void ClippingLimits::clear_lower() {
  if (GetArenaNoVirtual() == NULL && lower_ != NULL) {
    delete lower_;
  }
  lower_ = NULL;
}
void ClippingLimits::clear_upper() {
  if (GetArenaNoVirtual() == NULL && upper_ != NULL) {
    delete upper_;
  }
  upper_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ClippingLimits::kLowerFieldNumber;
const int ClippingLimits::kUpperFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ClippingLimits::ClippingLimits()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_ClippingLimits.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tpu.ClippingLimits)
}
ClippingLimits::ClippingLimits(const ClippingLimits& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_lower()) {
    lower_ = new ::google::protobuf::FloatValue(*from.lower_);
  } else {
    lower_ = NULL;
  }
  if (from.has_upper()) {
    upper_ = new ::google::protobuf::FloatValue(*from.upper_);
  } else {
    upper_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.tpu.ClippingLimits)
}

void ClippingLimits::SharedCtor() {
  ::memset(&lower_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&upper_) -
      reinterpret_cast<char*>(&lower_)) + sizeof(upper_));
}

ClippingLimits::~ClippingLimits() {
  // @@protoc_insertion_point(destructor:tensorflow.tpu.ClippingLimits)
  SharedDtor();
}

void ClippingLimits::SharedDtor() {
  if (this != internal_default_instance()) delete lower_;
  if (this != internal_default_instance()) delete upper_;
}

void ClippingLimits::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ClippingLimits::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ClippingLimits& ClippingLimits::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_ClippingLimits.base);
  return *internal_default_instance();
}


void ClippingLimits::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tpu.ClippingLimits)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && lower_ != NULL) {
    delete lower_;
  }
  lower_ = NULL;
  if (GetArenaNoVirtual() == NULL && upper_ != NULL) {
    delete upper_;
  }
  upper_ = NULL;
  _internal_metadata_.Clear();
}

bool ClippingLimits::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tpu.ClippingLimits)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .google.protobuf.FloatValue lower = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_lower()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .google.protobuf.FloatValue upper = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_upper()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tpu.ClippingLimits)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tpu.ClippingLimits)
  return false;
#undef DO_
}

void ClippingLimits::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tpu.ClippingLimits)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .google.protobuf.FloatValue lower = 1;
  if (this->has_lower()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->_internal_lower(), output);
  }

  // .google.protobuf.FloatValue upper = 2;
  if (this->has_upper()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_upper(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tpu.ClippingLimits)
}

::google::protobuf::uint8* ClippingLimits::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tpu.ClippingLimits)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .google.protobuf.FloatValue lower = 1;
  if (this->has_lower()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->_internal_lower(), deterministic, target);
  }

  // .google.protobuf.FloatValue upper = 2;
  if (this->has_upper()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_upper(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tpu.ClippingLimits)
  return target;
}

size_t ClippingLimits::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tpu.ClippingLimits)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .google.protobuf.FloatValue lower = 1;
  if (this->has_lower()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *lower_);
  }

  // .google.protobuf.FloatValue upper = 2;
  if (this->has_upper()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *upper_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ClippingLimits::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tpu.ClippingLimits)
  GOOGLE_DCHECK_NE(&from, this);
  const ClippingLimits* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ClippingLimits>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tpu.ClippingLimits)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tpu.ClippingLimits)
    MergeFrom(*source);
  }
}

void ClippingLimits::MergeFrom(const ClippingLimits& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tpu.ClippingLimits)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_lower()) {
    mutable_lower()->::google::protobuf::FloatValue::MergeFrom(from.lower());
  }
  if (from.has_upper()) {
    mutable_upper()->::google::protobuf::FloatValue::MergeFrom(from.upper());
  }
}

void ClippingLimits::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tpu.ClippingLimits)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ClippingLimits::CopyFrom(const ClippingLimits& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tpu.ClippingLimits)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ClippingLimits::IsInitialized() const {
  return true;
}

void ClippingLimits::Swap(ClippingLimits* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ClippingLimits::InternalSwap(ClippingLimits* other) {
  using std::swap;
  swap(lower_, other->lower_);
  swap(upper_, other->upper_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ClippingLimits::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void DynamicLearningRate::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int DynamicLearningRate::kTagFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DynamicLearningRate::DynamicLearningRate()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_DynamicLearningRate.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tpu.DynamicLearningRate)
}
DynamicLearningRate::DynamicLearningRate(const DynamicLearningRate& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  tag_ = from.tag_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.tpu.DynamicLearningRate)
}

void DynamicLearningRate::SharedCtor() {
  tag_ = 0;
}

DynamicLearningRate::~DynamicLearningRate() {
  // @@protoc_insertion_point(destructor:tensorflow.tpu.DynamicLearningRate)
  SharedDtor();
}

void DynamicLearningRate::SharedDtor() {
}

void DynamicLearningRate::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* DynamicLearningRate::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const DynamicLearningRate& DynamicLearningRate::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_DynamicLearningRate.base);
  return *internal_default_instance();
}


void DynamicLearningRate::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tpu.DynamicLearningRate)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  tag_ = 0;
  _internal_metadata_.Clear();
}

bool DynamicLearningRate::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tpu.DynamicLearningRate)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 tag = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &tag_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tpu.DynamicLearningRate)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tpu.DynamicLearningRate)
  return false;
#undef DO_
}

void DynamicLearningRate::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tpu.DynamicLearningRate)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 tag = 1;
  if (this->tag() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->tag(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tpu.DynamicLearningRate)
}

::google::protobuf::uint8* DynamicLearningRate::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tpu.DynamicLearningRate)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 tag = 1;
  if (this->tag() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->tag(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tpu.DynamicLearningRate)
  return target;
}

size_t DynamicLearningRate::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tpu.DynamicLearningRate)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int32 tag = 1;
  if (this->tag() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->tag());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DynamicLearningRate::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tpu.DynamicLearningRate)
  GOOGLE_DCHECK_NE(&from, this);
  const DynamicLearningRate* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const DynamicLearningRate>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tpu.DynamicLearningRate)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tpu.DynamicLearningRate)
    MergeFrom(*source);
  }
}

void DynamicLearningRate::MergeFrom(const DynamicLearningRate& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tpu.DynamicLearningRate)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.tag() != 0) {
    set_tag(from.tag());
  }
}

void DynamicLearningRate::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tpu.DynamicLearningRate)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DynamicLearningRate::CopyFrom(const DynamicLearningRate& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tpu.DynamicLearningRate)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DynamicLearningRate::IsInitialized() const {
  return true;
}

void DynamicLearningRate::Swap(DynamicLearningRate* other) {
  if (other == this) return;
  InternalSwap(other);
}
void DynamicLearningRate::InternalSwap(DynamicLearningRate* other) {
  using std::swap;
  swap(tag_, other->tag_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata DynamicLearningRate::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void LearningRate::InitAsDefaultInstance() {
  ::tensorflow::tpu::_LearningRate_default_instance_.constant_ = 0;
  ::tensorflow::tpu::_LearningRate_default_instance_.dynamic_ = const_cast< ::tensorflow::tpu::DynamicLearningRate*>(
      ::tensorflow::tpu::DynamicLearningRate::internal_default_instance());
}
void LearningRate::set_allocated_dynamic(::tensorflow::tpu::DynamicLearningRate* dynamic) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_learning_rate();
  if (dynamic) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      dynamic = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, dynamic, submessage_arena);
    }
    set_has_dynamic();
    learning_rate_.dynamic_ = dynamic;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.LearningRate.dynamic)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int LearningRate::kConstantFieldNumber;
const int LearningRate::kDynamicFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

LearningRate::LearningRate()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_LearningRate.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tpu.LearningRate)
}
LearningRate::LearningRate(const LearningRate& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  clear_has_learning_rate();
  switch (from.learning_rate_case()) {
    case kConstant: {
      set_constant(from.constant());
      break;
    }
    case kDynamic: {
      mutable_dynamic()->::tensorflow::tpu::DynamicLearningRate::MergeFrom(from.dynamic());
      break;
    }
    case LEARNING_RATE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.tpu.LearningRate)
}

void LearningRate::SharedCtor() {
  clear_has_learning_rate();
}

LearningRate::~LearningRate() {
  // @@protoc_insertion_point(destructor:tensorflow.tpu.LearningRate)
  SharedDtor();
}

void LearningRate::SharedDtor() {
  if (has_learning_rate()) {
    clear_learning_rate();
  }
}

void LearningRate::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* LearningRate::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const LearningRate& LearningRate::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_LearningRate.base);
  return *internal_default_instance();
}


void LearningRate::clear_learning_rate() {
// @@protoc_insertion_point(one_of_clear_start:tensorflow.tpu.LearningRate)
  switch (learning_rate_case()) {
    case kConstant: {
      // No need to clear
      break;
    }
    case kDynamic: {
      delete learning_rate_.dynamic_;
      break;
    }
    case LEARNING_RATE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = LEARNING_RATE_NOT_SET;
}


void LearningRate::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tpu.LearningRate)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_learning_rate();
  _internal_metadata_.Clear();
}

bool LearningRate::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tpu.LearningRate)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // float constant = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(13u /* 13 & 0xFF */)) {
          clear_learning_rate();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &learning_rate_.constant_)));
          set_has_constant();
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.tpu.DynamicLearningRate dynamic = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_dynamic()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tpu.LearningRate)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tpu.LearningRate)
  return false;
#undef DO_
}

void LearningRate::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tpu.LearningRate)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float constant = 1;
  if (has_constant()) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(1, this->constant(), output);
  }

  // .tensorflow.tpu.DynamicLearningRate dynamic = 2;
  if (has_dynamic()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_dynamic(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tpu.LearningRate)
}

::google::protobuf::uint8* LearningRate::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tpu.LearningRate)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float constant = 1;
  if (has_constant()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(1, this->constant(), target);
  }

  // .tensorflow.tpu.DynamicLearningRate dynamic = 2;
  if (has_dynamic()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_dynamic(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tpu.LearningRate)
  return target;
}

size_t LearningRate::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tpu.LearningRate)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  switch (learning_rate_case()) {
    // float constant = 1;
    case kConstant: {
      total_size += 1 + 4;
      break;
    }
    // .tensorflow.tpu.DynamicLearningRate dynamic = 2;
    case kDynamic: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *learning_rate_.dynamic_);
      break;
    }
    case LEARNING_RATE_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void LearningRate::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tpu.LearningRate)
  GOOGLE_DCHECK_NE(&from, this);
  const LearningRate* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const LearningRate>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tpu.LearningRate)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tpu.LearningRate)
    MergeFrom(*source);
  }
}

void LearningRate::MergeFrom(const LearningRate& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tpu.LearningRate)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.learning_rate_case()) {
    case kConstant: {
      set_constant(from.constant());
      break;
    }
    case kDynamic: {
      mutable_dynamic()->::tensorflow::tpu::DynamicLearningRate::MergeFrom(from.dynamic());
      break;
    }
    case LEARNING_RATE_NOT_SET: {
      break;
    }
  }
}

void LearningRate::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tpu.LearningRate)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void LearningRate::CopyFrom(const LearningRate& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tpu.LearningRate)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LearningRate::IsInitialized() const {
  return true;
}

void LearningRate::Swap(LearningRate* other) {
  if (other == this) return;
  InternalSwap(other);
}
void LearningRate::InternalSwap(LearningRate* other) {
  using std::swap;
  swap(learning_rate_, other->learning_rate_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata LearningRate::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void AdagradParameters::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AdagradParameters::kInitialAccumulatorFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AdagradParameters::AdagradParameters()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_AdagradParameters.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tpu.AdagradParameters)
}
AdagradParameters::AdagradParameters(const AdagradParameters& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  initial_accumulator_ = from.initial_accumulator_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.tpu.AdagradParameters)
}

void AdagradParameters::SharedCtor() {
  initial_accumulator_ = 0;
}

AdagradParameters::~AdagradParameters() {
  // @@protoc_insertion_point(destructor:tensorflow.tpu.AdagradParameters)
  SharedDtor();
}

void AdagradParameters::SharedDtor() {
}

void AdagradParameters::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* AdagradParameters::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const AdagradParameters& AdagradParameters::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_AdagradParameters.base);
  return *internal_default_instance();
}


void AdagradParameters::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tpu.AdagradParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  initial_accumulator_ = 0;
  _internal_metadata_.Clear();
}

bool AdagradParameters::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tpu.AdagradParameters)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // float initial_accumulator = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(13u /* 13 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &initial_accumulator_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tpu.AdagradParameters)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tpu.AdagradParameters)
  return false;
#undef DO_
}

void AdagradParameters::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tpu.AdagradParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float initial_accumulator = 1;
  if (this->initial_accumulator() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(1, this->initial_accumulator(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tpu.AdagradParameters)
}

::google::protobuf::uint8* AdagradParameters::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tpu.AdagradParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float initial_accumulator = 1;
  if (this->initial_accumulator() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(1, this->initial_accumulator(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tpu.AdagradParameters)
  return target;
}

size_t AdagradParameters::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tpu.AdagradParameters)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // float initial_accumulator = 1;
  if (this->initial_accumulator() != 0) {
    total_size += 1 + 4;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AdagradParameters::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tpu.AdagradParameters)
  GOOGLE_DCHECK_NE(&from, this);
  const AdagradParameters* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AdagradParameters>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tpu.AdagradParameters)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tpu.AdagradParameters)
    MergeFrom(*source);
  }
}

void AdagradParameters::MergeFrom(const AdagradParameters& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tpu.AdagradParameters)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.initial_accumulator() != 0) {
    set_initial_accumulator(from.initial_accumulator());
  }
}

void AdagradParameters::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tpu.AdagradParameters)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AdagradParameters::CopyFrom(const AdagradParameters& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tpu.AdagradParameters)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AdagradParameters::IsInitialized() const {
  return true;
}

void AdagradParameters::Swap(AdagradParameters* other) {
  if (other == this) return;
  InternalSwap(other);
}
void AdagradParameters::InternalSwap(AdagradParameters* other) {
  using std::swap;
  swap(initial_accumulator_, other->initial_accumulator_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata AdagradParameters::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void StochasticGradientDescentParameters::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

StochasticGradientDescentParameters::StochasticGradientDescentParameters()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_StochasticGradientDescentParameters.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tpu.StochasticGradientDescentParameters)
}
StochasticGradientDescentParameters::StochasticGradientDescentParameters(const StochasticGradientDescentParameters& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.tpu.StochasticGradientDescentParameters)
}

void StochasticGradientDescentParameters::SharedCtor() {
}

StochasticGradientDescentParameters::~StochasticGradientDescentParameters() {
  // @@protoc_insertion_point(destructor:tensorflow.tpu.StochasticGradientDescentParameters)
  SharedDtor();
}

void StochasticGradientDescentParameters::SharedDtor() {
}

void StochasticGradientDescentParameters::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* StochasticGradientDescentParameters::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const StochasticGradientDescentParameters& StochasticGradientDescentParameters::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_StochasticGradientDescentParameters.base);
  return *internal_default_instance();
}


void StochasticGradientDescentParameters::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tpu.StochasticGradientDescentParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_metadata_.Clear();
}

bool StochasticGradientDescentParameters::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tpu.StochasticGradientDescentParameters)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, _internal_metadata_.mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tpu.StochasticGradientDescentParameters)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tpu.StochasticGradientDescentParameters)
  return false;
#undef DO_
}

void StochasticGradientDescentParameters::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tpu.StochasticGradientDescentParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tpu.StochasticGradientDescentParameters)
}

::google::protobuf::uint8* StochasticGradientDescentParameters::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tpu.StochasticGradientDescentParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tpu.StochasticGradientDescentParameters)
  return target;
}

size_t StochasticGradientDescentParameters::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tpu.StochasticGradientDescentParameters)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void StochasticGradientDescentParameters::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tpu.StochasticGradientDescentParameters)
  GOOGLE_DCHECK_NE(&from, this);
  const StochasticGradientDescentParameters* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const StochasticGradientDescentParameters>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tpu.StochasticGradientDescentParameters)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tpu.StochasticGradientDescentParameters)
    MergeFrom(*source);
  }
}

void StochasticGradientDescentParameters::MergeFrom(const StochasticGradientDescentParameters& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tpu.StochasticGradientDescentParameters)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

}

void StochasticGradientDescentParameters::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tpu.StochasticGradientDescentParameters)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StochasticGradientDescentParameters::CopyFrom(const StochasticGradientDescentParameters& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tpu.StochasticGradientDescentParameters)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StochasticGradientDescentParameters::IsInitialized() const {
  return true;
}

void StochasticGradientDescentParameters::Swap(StochasticGradientDescentParameters* other) {
  if (other == this) return;
  InternalSwap(other);
}
void StochasticGradientDescentParameters::InternalSwap(StochasticGradientDescentParameters* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata StochasticGradientDescentParameters::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void FtrlParameters::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int FtrlParameters::kL1FieldNumber;
const int FtrlParameters::kL2FieldNumber;
const int FtrlParameters::kLrPowerFieldNumber;
const int FtrlParameters::kInitialAccumFieldNumber;
const int FtrlParameters::kInitialLinearFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

FtrlParameters::FtrlParameters()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_FtrlParameters.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tpu.FtrlParameters)
}
FtrlParameters::FtrlParameters(const FtrlParameters& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&l1_, &from.l1_,
    static_cast<size_t>(reinterpret_cast<char*>(&initial_linear_) -
    reinterpret_cast<char*>(&l1_)) + sizeof(initial_linear_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.tpu.FtrlParameters)
}

void FtrlParameters::SharedCtor() {
  ::memset(&l1_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&initial_linear_) -
      reinterpret_cast<char*>(&l1_)) + sizeof(initial_linear_));
}

FtrlParameters::~FtrlParameters() {
  // @@protoc_insertion_point(destructor:tensorflow.tpu.FtrlParameters)
  SharedDtor();
}

void FtrlParameters::SharedDtor() {
}

void FtrlParameters::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* FtrlParameters::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const FtrlParameters& FtrlParameters::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_FtrlParameters.base);
  return *internal_default_instance();
}


void FtrlParameters::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tpu.FtrlParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&l1_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&initial_linear_) -
      reinterpret_cast<char*>(&l1_)) + sizeof(initial_linear_));
  _internal_metadata_.Clear();
}

bool FtrlParameters::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tpu.FtrlParameters)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // float l1 = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(13u /* 13 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &l1_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float l2 = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(21u /* 21 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &l2_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float lr_power = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(29u /* 29 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &lr_power_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float initial_accum = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(37u /* 37 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &initial_accum_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float initial_linear = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(45u /* 45 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &initial_linear_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tpu.FtrlParameters)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tpu.FtrlParameters)
  return false;
#undef DO_
}

void FtrlParameters::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tpu.FtrlParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float l1 = 1;
  if (this->l1() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(1, this->l1(), output);
  }

  // float l2 = 2;
  if (this->l2() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(2, this->l2(), output);
  }

  // float lr_power = 3;
  if (this->lr_power() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(3, this->lr_power(), output);
  }

  // float initial_accum = 4;
  if (this->initial_accum() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(4, this->initial_accum(), output);
  }

  // float initial_linear = 5;
  if (this->initial_linear() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(5, this->initial_linear(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tpu.FtrlParameters)
}

::google::protobuf::uint8* FtrlParameters::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tpu.FtrlParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float l1 = 1;
  if (this->l1() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(1, this->l1(), target);
  }

  // float l2 = 2;
  if (this->l2() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(2, this->l2(), target);
  }

  // float lr_power = 3;
  if (this->lr_power() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(3, this->lr_power(), target);
  }

  // float initial_accum = 4;
  if (this->initial_accum() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(4, this->initial_accum(), target);
  }

  // float initial_linear = 5;
  if (this->initial_linear() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(5, this->initial_linear(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tpu.FtrlParameters)
  return target;
}

size_t FtrlParameters::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tpu.FtrlParameters)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // float l1 = 1;
  if (this->l1() != 0) {
    total_size += 1 + 4;
  }

  // float l2 = 2;
  if (this->l2() != 0) {
    total_size += 1 + 4;
  }

  // float lr_power = 3;
  if (this->lr_power() != 0) {
    total_size += 1 + 4;
  }

  // float initial_accum = 4;
  if (this->initial_accum() != 0) {
    total_size += 1 + 4;
  }

  // float initial_linear = 5;
  if (this->initial_linear() != 0) {
    total_size += 1 + 4;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void FtrlParameters::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tpu.FtrlParameters)
  GOOGLE_DCHECK_NE(&from, this);
  const FtrlParameters* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const FtrlParameters>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tpu.FtrlParameters)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tpu.FtrlParameters)
    MergeFrom(*source);
  }
}

void FtrlParameters::MergeFrom(const FtrlParameters& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tpu.FtrlParameters)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.l1() != 0) {
    set_l1(from.l1());
  }
  if (from.l2() != 0) {
    set_l2(from.l2());
  }
  if (from.lr_power() != 0) {
    set_lr_power(from.lr_power());
  }
  if (from.initial_accum() != 0) {
    set_initial_accum(from.initial_accum());
  }
  if (from.initial_linear() != 0) {
    set_initial_linear(from.initial_linear());
  }
}

void FtrlParameters::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tpu.FtrlParameters)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FtrlParameters::CopyFrom(const FtrlParameters& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tpu.FtrlParameters)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FtrlParameters::IsInitialized() const {
  return true;
}

void FtrlParameters::Swap(FtrlParameters* other) {
  if (other == this) return;
  InternalSwap(other);
}
void FtrlParameters::InternalSwap(FtrlParameters* other) {
  using std::swap;
  swap(l1_, other->l1_);
  swap(l2_, other->l2_);
  swap(lr_power_, other->lr_power_);
  swap(initial_accum_, other->initial_accum_);
  swap(initial_linear_, other->initial_linear_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata FtrlParameters::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void AdamParameters::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AdamParameters::kBeta1FieldNumber;
const int AdamParameters::kBeta2FieldNumber;
const int AdamParameters::kEpsilonFieldNumber;
const int AdamParameters::kInitialMFieldNumber;
const int AdamParameters::kInitialVFieldNumber;
const int AdamParameters::kUseNonLazyAdamFieldNumber;
const int AdamParameters::kUseSumInsideSqrtFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AdamParameters::AdamParameters()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_AdamParameters.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tpu.AdamParameters)
}
AdamParameters::AdamParameters(const AdamParameters& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&beta1_, &from.beta1_,
    static_cast<size_t>(reinterpret_cast<char*>(&use_sum_inside_sqrt_) -
    reinterpret_cast<char*>(&beta1_)) + sizeof(use_sum_inside_sqrt_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.tpu.AdamParameters)
}

void AdamParameters::SharedCtor() {
  ::memset(&beta1_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&use_sum_inside_sqrt_) -
      reinterpret_cast<char*>(&beta1_)) + sizeof(use_sum_inside_sqrt_));
}

AdamParameters::~AdamParameters() {
  // @@protoc_insertion_point(destructor:tensorflow.tpu.AdamParameters)
  SharedDtor();
}

void AdamParameters::SharedDtor() {
}

void AdamParameters::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* AdamParameters::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const AdamParameters& AdamParameters::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_AdamParameters.base);
  return *internal_default_instance();
}


void AdamParameters::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tpu.AdamParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&beta1_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&use_sum_inside_sqrt_) -
      reinterpret_cast<char*>(&beta1_)) + sizeof(use_sum_inside_sqrt_));
  _internal_metadata_.Clear();
}

bool AdamParameters::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tpu.AdamParameters)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // float beta1 = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(29u /* 29 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &beta1_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float beta2 = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(37u /* 37 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &beta2_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float epsilon = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(45u /* 45 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &epsilon_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float initial_m = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(53u /* 53 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &initial_m_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float initial_v = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(61u /* 61 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &initial_v_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool use_non_lazy_adam = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(64u /* 64 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &use_non_lazy_adam_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool use_sum_inside_sqrt = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(80u /* 80 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &use_sum_inside_sqrt_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tpu.AdamParameters)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tpu.AdamParameters)
  return false;
#undef DO_
}

void AdamParameters::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tpu.AdamParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float beta1 = 3;
  if (this->beta1() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(3, this->beta1(), output);
  }

  // float beta2 = 4;
  if (this->beta2() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(4, this->beta2(), output);
  }

  // float epsilon = 5;
  if (this->epsilon() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(5, this->epsilon(), output);
  }

  // float initial_m = 6;
  if (this->initial_m() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(6, this->initial_m(), output);
  }

  // float initial_v = 7;
  if (this->initial_v() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(7, this->initial_v(), output);
  }

  // bool use_non_lazy_adam = 8;
  if (this->use_non_lazy_adam() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(8, this->use_non_lazy_adam(), output);
  }

  // bool use_sum_inside_sqrt = 10;
  if (this->use_sum_inside_sqrt() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(10, this->use_sum_inside_sqrt(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tpu.AdamParameters)
}

::google::protobuf::uint8* AdamParameters::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tpu.AdamParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float beta1 = 3;
  if (this->beta1() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(3, this->beta1(), target);
  }

  // float beta2 = 4;
  if (this->beta2() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(4, this->beta2(), target);
  }

  // float epsilon = 5;
  if (this->epsilon() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(5, this->epsilon(), target);
  }

  // float initial_m = 6;
  if (this->initial_m() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(6, this->initial_m(), target);
  }

  // float initial_v = 7;
  if (this->initial_v() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(7, this->initial_v(), target);
  }

  // bool use_non_lazy_adam = 8;
  if (this->use_non_lazy_adam() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(8, this->use_non_lazy_adam(), target);
  }

  // bool use_sum_inside_sqrt = 10;
  if (this->use_sum_inside_sqrt() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(10, this->use_sum_inside_sqrt(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tpu.AdamParameters)
  return target;
}

size_t AdamParameters::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tpu.AdamParameters)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // float beta1 = 3;
  if (this->beta1() != 0) {
    total_size += 1 + 4;
  }

  // float beta2 = 4;
  if (this->beta2() != 0) {
    total_size += 1 + 4;
  }

  // float epsilon = 5;
  if (this->epsilon() != 0) {
    total_size += 1 + 4;
  }

  // float initial_m = 6;
  if (this->initial_m() != 0) {
    total_size += 1 + 4;
  }

  // float initial_v = 7;
  if (this->initial_v() != 0) {
    total_size += 1 + 4;
  }

  // bool use_non_lazy_adam = 8;
  if (this->use_non_lazy_adam() != 0) {
    total_size += 1 + 1;
  }

  // bool use_sum_inside_sqrt = 10;
  if (this->use_sum_inside_sqrt() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AdamParameters::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tpu.AdamParameters)
  GOOGLE_DCHECK_NE(&from, this);
  const AdamParameters* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AdamParameters>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tpu.AdamParameters)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tpu.AdamParameters)
    MergeFrom(*source);
  }
}

void AdamParameters::MergeFrom(const AdamParameters& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tpu.AdamParameters)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.beta1() != 0) {
    set_beta1(from.beta1());
  }
  if (from.beta2() != 0) {
    set_beta2(from.beta2());
  }
  if (from.epsilon() != 0) {
    set_epsilon(from.epsilon());
  }
  if (from.initial_m() != 0) {
    set_initial_m(from.initial_m());
  }
  if (from.initial_v() != 0) {
    set_initial_v(from.initial_v());
  }
  if (from.use_non_lazy_adam() != 0) {
    set_use_non_lazy_adam(from.use_non_lazy_adam());
  }
  if (from.use_sum_inside_sqrt() != 0) {
    set_use_sum_inside_sqrt(from.use_sum_inside_sqrt());
  }
}

void AdamParameters::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tpu.AdamParameters)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AdamParameters::CopyFrom(const AdamParameters& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tpu.AdamParameters)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AdamParameters::IsInitialized() const {
  return true;
}

void AdamParameters::Swap(AdamParameters* other) {
  if (other == this) return;
  InternalSwap(other);
}
void AdamParameters::InternalSwap(AdamParameters* other) {
  using std::swap;
  swap(beta1_, other->beta1_);
  swap(beta2_, other->beta2_);
  swap(epsilon_, other->epsilon_);
  swap(initial_m_, other->initial_m_);
  swap(initial_v_, other->initial_v_);
  swap(use_non_lazy_adam_, other->use_non_lazy_adam_);
  swap(use_sum_inside_sqrt_, other->use_sum_inside_sqrt_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata AdamParameters::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void MomentumParameters::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MomentumParameters::kMomentumFieldNumber;
const int MomentumParameters::kUseNesterovFieldNumber;
const int MomentumParameters::kInitialAccumFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MomentumParameters::MomentumParameters()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_MomentumParameters.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tpu.MomentumParameters)
}
MomentumParameters::MomentumParameters(const MomentumParameters& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&momentum_, &from.momentum_,
    static_cast<size_t>(reinterpret_cast<char*>(&initial_accum_) -
    reinterpret_cast<char*>(&momentum_)) + sizeof(initial_accum_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.tpu.MomentumParameters)
}

void MomentumParameters::SharedCtor() {
  ::memset(&momentum_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&initial_accum_) -
      reinterpret_cast<char*>(&momentum_)) + sizeof(initial_accum_));
}

MomentumParameters::~MomentumParameters() {
  // @@protoc_insertion_point(destructor:tensorflow.tpu.MomentumParameters)
  SharedDtor();
}

void MomentumParameters::SharedDtor() {
}

void MomentumParameters::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* MomentumParameters::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const MomentumParameters& MomentumParameters::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_MomentumParameters.base);
  return *internal_default_instance();
}


void MomentumParameters::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tpu.MomentumParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&momentum_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&initial_accum_) -
      reinterpret_cast<char*>(&momentum_)) + sizeof(initial_accum_));
  _internal_metadata_.Clear();
}

bool MomentumParameters::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tpu.MomentumParameters)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // float momentum = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(13u /* 13 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &momentum_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool use_nesterov = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &use_nesterov_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float initial_accum = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(29u /* 29 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &initial_accum_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tpu.MomentumParameters)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tpu.MomentumParameters)
  return false;
#undef DO_
}

void MomentumParameters::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tpu.MomentumParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float momentum = 1;
  if (this->momentum() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(1, this->momentum(), output);
  }

  // bool use_nesterov = 2;
  if (this->use_nesterov() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(2, this->use_nesterov(), output);
  }

  // float initial_accum = 3;
  if (this->initial_accum() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(3, this->initial_accum(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tpu.MomentumParameters)
}

::google::protobuf::uint8* MomentumParameters::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tpu.MomentumParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float momentum = 1;
  if (this->momentum() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(1, this->momentum(), target);
  }

  // bool use_nesterov = 2;
  if (this->use_nesterov() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(2, this->use_nesterov(), target);
  }

  // float initial_accum = 3;
  if (this->initial_accum() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(3, this->initial_accum(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tpu.MomentumParameters)
  return target;
}

size_t MomentumParameters::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tpu.MomentumParameters)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // float momentum = 1;
  if (this->momentum() != 0) {
    total_size += 1 + 4;
  }

  // bool use_nesterov = 2;
  if (this->use_nesterov() != 0) {
    total_size += 1 + 1;
  }

  // float initial_accum = 3;
  if (this->initial_accum() != 0) {
    total_size += 1 + 4;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void MomentumParameters::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tpu.MomentumParameters)
  GOOGLE_DCHECK_NE(&from, this);
  const MomentumParameters* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MomentumParameters>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tpu.MomentumParameters)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tpu.MomentumParameters)
    MergeFrom(*source);
  }
}

void MomentumParameters::MergeFrom(const MomentumParameters& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tpu.MomentumParameters)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.momentum() != 0) {
    set_momentum(from.momentum());
  }
  if (from.use_nesterov() != 0) {
    set_use_nesterov(from.use_nesterov());
  }
  if (from.initial_accum() != 0) {
    set_initial_accum(from.initial_accum());
  }
}

void MomentumParameters::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tpu.MomentumParameters)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MomentumParameters::CopyFrom(const MomentumParameters& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tpu.MomentumParameters)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MomentumParameters::IsInitialized() const {
  return true;
}

void MomentumParameters::Swap(MomentumParameters* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MomentumParameters::InternalSwap(MomentumParameters* other) {
  using std::swap;
  swap(momentum_, other->momentum_);
  swap(use_nesterov_, other->use_nesterov_);
  swap(initial_accum_, other->initial_accum_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata MomentumParameters::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void RmsPropParameters::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RmsPropParameters::kRhoFieldNumber;
const int RmsPropParameters::kMomentumFieldNumber;
const int RmsPropParameters::kEpsilonFieldNumber;
const int RmsPropParameters::kInitialMsFieldNumber;
const int RmsPropParameters::kInitialMomFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RmsPropParameters::RmsPropParameters()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_RmsPropParameters.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tpu.RmsPropParameters)
}
RmsPropParameters::RmsPropParameters(const RmsPropParameters& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&rho_, &from.rho_,
    static_cast<size_t>(reinterpret_cast<char*>(&initial_mom_) -
    reinterpret_cast<char*>(&rho_)) + sizeof(initial_mom_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.tpu.RmsPropParameters)
}

void RmsPropParameters::SharedCtor() {
  ::memset(&rho_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&initial_mom_) -
      reinterpret_cast<char*>(&rho_)) + sizeof(initial_mom_));
}

RmsPropParameters::~RmsPropParameters() {
  // @@protoc_insertion_point(destructor:tensorflow.tpu.RmsPropParameters)
  SharedDtor();
}

void RmsPropParameters::SharedDtor() {
}

void RmsPropParameters::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* RmsPropParameters::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const RmsPropParameters& RmsPropParameters::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_RmsPropParameters.base);
  return *internal_default_instance();
}


void RmsPropParameters::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tpu.RmsPropParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&rho_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&initial_mom_) -
      reinterpret_cast<char*>(&rho_)) + sizeof(initial_mom_));
  _internal_metadata_.Clear();
}

bool RmsPropParameters::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tpu.RmsPropParameters)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // float rho = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(13u /* 13 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &rho_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float momentum = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(21u /* 21 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &momentum_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float epsilon = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(29u /* 29 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &epsilon_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float initial_ms = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(37u /* 37 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &initial_ms_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float initial_mom = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(45u /* 45 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &initial_mom_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tpu.RmsPropParameters)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tpu.RmsPropParameters)
  return false;
#undef DO_
}

void RmsPropParameters::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tpu.RmsPropParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float rho = 1;
  if (this->rho() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(1, this->rho(), output);
  }

  // float momentum = 2;
  if (this->momentum() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(2, this->momentum(), output);
  }

  // float epsilon = 3;
  if (this->epsilon() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(3, this->epsilon(), output);
  }

  // float initial_ms = 4;
  if (this->initial_ms() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(4, this->initial_ms(), output);
  }

  // float initial_mom = 5;
  if (this->initial_mom() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(5, this->initial_mom(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tpu.RmsPropParameters)
}

::google::protobuf::uint8* RmsPropParameters::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tpu.RmsPropParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float rho = 1;
  if (this->rho() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(1, this->rho(), target);
  }

  // float momentum = 2;
  if (this->momentum() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(2, this->momentum(), target);
  }

  // float epsilon = 3;
  if (this->epsilon() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(3, this->epsilon(), target);
  }

  // float initial_ms = 4;
  if (this->initial_ms() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(4, this->initial_ms(), target);
  }

  // float initial_mom = 5;
  if (this->initial_mom() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(5, this->initial_mom(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tpu.RmsPropParameters)
  return target;
}

size_t RmsPropParameters::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tpu.RmsPropParameters)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // float rho = 1;
  if (this->rho() != 0) {
    total_size += 1 + 4;
  }

  // float momentum = 2;
  if (this->momentum() != 0) {
    total_size += 1 + 4;
  }

  // float epsilon = 3;
  if (this->epsilon() != 0) {
    total_size += 1 + 4;
  }

  // float initial_ms = 4;
  if (this->initial_ms() != 0) {
    total_size += 1 + 4;
  }

  // float initial_mom = 5;
  if (this->initial_mom() != 0) {
    total_size += 1 + 4;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RmsPropParameters::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tpu.RmsPropParameters)
  GOOGLE_DCHECK_NE(&from, this);
  const RmsPropParameters* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RmsPropParameters>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tpu.RmsPropParameters)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tpu.RmsPropParameters)
    MergeFrom(*source);
  }
}

void RmsPropParameters::MergeFrom(const RmsPropParameters& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tpu.RmsPropParameters)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.rho() != 0) {
    set_rho(from.rho());
  }
  if (from.momentum() != 0) {
    set_momentum(from.momentum());
  }
  if (from.epsilon() != 0) {
    set_epsilon(from.epsilon());
  }
  if (from.initial_ms() != 0) {
    set_initial_ms(from.initial_ms());
  }
  if (from.initial_mom() != 0) {
    set_initial_mom(from.initial_mom());
  }
}

void RmsPropParameters::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tpu.RmsPropParameters)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RmsPropParameters::CopyFrom(const RmsPropParameters& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tpu.RmsPropParameters)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RmsPropParameters::IsInitialized() const {
  return true;
}

void RmsPropParameters::Swap(RmsPropParameters* other) {
  if (other == this) return;
  InternalSwap(other);
}
void RmsPropParameters::InternalSwap(RmsPropParameters* other) {
  using std::swap;
  swap(rho_, other->rho_);
  swap(momentum_, other->momentum_);
  swap(epsilon_, other->epsilon_);
  swap(initial_ms_, other->initial_ms_);
  swap(initial_mom_, other->initial_mom_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata RmsPropParameters::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void CenteredRmsPropParameters::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CenteredRmsPropParameters::kRhoFieldNumber;
const int CenteredRmsPropParameters::kMomentumFieldNumber;
const int CenteredRmsPropParameters::kEpsilonFieldNumber;
const int CenteredRmsPropParameters::kInitialMsFieldNumber;
const int CenteredRmsPropParameters::kInitialMomFieldNumber;
const int CenteredRmsPropParameters::kInitialMgFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CenteredRmsPropParameters::CenteredRmsPropParameters()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_CenteredRmsPropParameters.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tpu.CenteredRmsPropParameters)
}
CenteredRmsPropParameters::CenteredRmsPropParameters(const CenteredRmsPropParameters& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&rho_, &from.rho_,
    static_cast<size_t>(reinterpret_cast<char*>(&initial_mg_) -
    reinterpret_cast<char*>(&rho_)) + sizeof(initial_mg_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.tpu.CenteredRmsPropParameters)
}

void CenteredRmsPropParameters::SharedCtor() {
  ::memset(&rho_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&initial_mg_) -
      reinterpret_cast<char*>(&rho_)) + sizeof(initial_mg_));
}

CenteredRmsPropParameters::~CenteredRmsPropParameters() {
  // @@protoc_insertion_point(destructor:tensorflow.tpu.CenteredRmsPropParameters)
  SharedDtor();
}

void CenteredRmsPropParameters::SharedDtor() {
}

void CenteredRmsPropParameters::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* CenteredRmsPropParameters::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CenteredRmsPropParameters& CenteredRmsPropParameters::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_CenteredRmsPropParameters.base);
  return *internal_default_instance();
}


void CenteredRmsPropParameters::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tpu.CenteredRmsPropParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&rho_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&initial_mg_) -
      reinterpret_cast<char*>(&rho_)) + sizeof(initial_mg_));
  _internal_metadata_.Clear();
}

bool CenteredRmsPropParameters::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tpu.CenteredRmsPropParameters)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // float rho = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(13u /* 13 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &rho_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float momentum = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(21u /* 21 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &momentum_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float epsilon = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(29u /* 29 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &epsilon_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float initial_ms = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(37u /* 37 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &initial_ms_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float initial_mom = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(45u /* 45 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &initial_mom_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float initial_mg = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(53u /* 53 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &initial_mg_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tpu.CenteredRmsPropParameters)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tpu.CenteredRmsPropParameters)
  return false;
#undef DO_
}

void CenteredRmsPropParameters::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tpu.CenteredRmsPropParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float rho = 1;
  if (this->rho() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(1, this->rho(), output);
  }

  // float momentum = 2;
  if (this->momentum() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(2, this->momentum(), output);
  }

  // float epsilon = 3;
  if (this->epsilon() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(3, this->epsilon(), output);
  }

  // float initial_ms = 4;
  if (this->initial_ms() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(4, this->initial_ms(), output);
  }

  // float initial_mom = 5;
  if (this->initial_mom() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(5, this->initial_mom(), output);
  }

  // float initial_mg = 6;
  if (this->initial_mg() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(6, this->initial_mg(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tpu.CenteredRmsPropParameters)
}

::google::protobuf::uint8* CenteredRmsPropParameters::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tpu.CenteredRmsPropParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float rho = 1;
  if (this->rho() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(1, this->rho(), target);
  }

  // float momentum = 2;
  if (this->momentum() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(2, this->momentum(), target);
  }

  // float epsilon = 3;
  if (this->epsilon() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(3, this->epsilon(), target);
  }

  // float initial_ms = 4;
  if (this->initial_ms() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(4, this->initial_ms(), target);
  }

  // float initial_mom = 5;
  if (this->initial_mom() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(5, this->initial_mom(), target);
  }

  // float initial_mg = 6;
  if (this->initial_mg() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(6, this->initial_mg(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tpu.CenteredRmsPropParameters)
  return target;
}

size_t CenteredRmsPropParameters::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tpu.CenteredRmsPropParameters)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // float rho = 1;
  if (this->rho() != 0) {
    total_size += 1 + 4;
  }

  // float momentum = 2;
  if (this->momentum() != 0) {
    total_size += 1 + 4;
  }

  // float epsilon = 3;
  if (this->epsilon() != 0) {
    total_size += 1 + 4;
  }

  // float initial_ms = 4;
  if (this->initial_ms() != 0) {
    total_size += 1 + 4;
  }

  // float initial_mom = 5;
  if (this->initial_mom() != 0) {
    total_size += 1 + 4;
  }

  // float initial_mg = 6;
  if (this->initial_mg() != 0) {
    total_size += 1 + 4;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CenteredRmsPropParameters::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tpu.CenteredRmsPropParameters)
  GOOGLE_DCHECK_NE(&from, this);
  const CenteredRmsPropParameters* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CenteredRmsPropParameters>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tpu.CenteredRmsPropParameters)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tpu.CenteredRmsPropParameters)
    MergeFrom(*source);
  }
}

void CenteredRmsPropParameters::MergeFrom(const CenteredRmsPropParameters& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tpu.CenteredRmsPropParameters)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.rho() != 0) {
    set_rho(from.rho());
  }
  if (from.momentum() != 0) {
    set_momentum(from.momentum());
  }
  if (from.epsilon() != 0) {
    set_epsilon(from.epsilon());
  }
  if (from.initial_ms() != 0) {
    set_initial_ms(from.initial_ms());
  }
  if (from.initial_mom() != 0) {
    set_initial_mom(from.initial_mom());
  }
  if (from.initial_mg() != 0) {
    set_initial_mg(from.initial_mg());
  }
}

void CenteredRmsPropParameters::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tpu.CenteredRmsPropParameters)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CenteredRmsPropParameters::CopyFrom(const CenteredRmsPropParameters& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tpu.CenteredRmsPropParameters)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CenteredRmsPropParameters::IsInitialized() const {
  return true;
}

void CenteredRmsPropParameters::Swap(CenteredRmsPropParameters* other) {
  if (other == this) return;
  InternalSwap(other);
}
void CenteredRmsPropParameters::InternalSwap(CenteredRmsPropParameters* other) {
  using std::swap;
  swap(rho_, other->rho_);
  swap(momentum_, other->momentum_);
  swap(epsilon_, other->epsilon_);
  swap(initial_ms_, other->initial_ms_);
  swap(initial_mom_, other->initial_mom_);
  swap(initial_mg_, other->initial_mg_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata CenteredRmsPropParameters::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void MdlAdagradLightParameters::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MdlAdagradLightParameters::kL2FieldNumber;
const int MdlAdagradLightParameters::kLrPowerFieldNumber;
const int MdlAdagradLightParameters::kMinServableMdlBenefitFieldNumber;
const int MdlAdagradLightParameters::kMdlMixInMarginFieldNumber;
const int MdlAdagradLightParameters::kMdlBenefitRampupCoeffFieldNumber;
const int MdlAdagradLightParameters::kMdlMinWeightFieldNumber;
const int MdlAdagradLightParameters::kBenefitRevisitScaleFieldNumber;
const int MdlAdagradLightParameters::kMaxEventBenefitFieldNumber;
const int MdlAdagradLightParameters::kMaxTotalBenefitFieldNumber;
const int MdlAdagradLightParameters::kMdlHardLimitFieldNumber;
const int MdlAdagradLightParameters::kHardLimitMinBenefitFieldNumber;
const int MdlAdagradLightParameters::kMdlRegularizeFieldNumber;
const int MdlAdagradLightParameters::kInitialAccumulatorFieldNumber;
const int MdlAdagradLightParameters::kInitialWeightFieldNumber;
const int MdlAdagradLightParameters::kInitialBenefitFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MdlAdagradLightParameters::MdlAdagradLightParameters()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_MdlAdagradLightParameters.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tpu.MdlAdagradLightParameters)
}
MdlAdagradLightParameters::MdlAdagradLightParameters(const MdlAdagradLightParameters& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&l2_, &from.l2_,
    static_cast<size_t>(reinterpret_cast<char*>(&initial_benefit_) -
    reinterpret_cast<char*>(&l2_)) + sizeof(initial_benefit_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.tpu.MdlAdagradLightParameters)
}

void MdlAdagradLightParameters::SharedCtor() {
  ::memset(&l2_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&initial_benefit_) -
      reinterpret_cast<char*>(&l2_)) + sizeof(initial_benefit_));
}

MdlAdagradLightParameters::~MdlAdagradLightParameters() {
  // @@protoc_insertion_point(destructor:tensorflow.tpu.MdlAdagradLightParameters)
  SharedDtor();
}

void MdlAdagradLightParameters::SharedDtor() {
}

void MdlAdagradLightParameters::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* MdlAdagradLightParameters::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const MdlAdagradLightParameters& MdlAdagradLightParameters::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_MdlAdagradLightParameters.base);
  return *internal_default_instance();
}


void MdlAdagradLightParameters::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tpu.MdlAdagradLightParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&l2_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&initial_benefit_) -
      reinterpret_cast<char*>(&l2_)) + sizeof(initial_benefit_));
  _internal_metadata_.Clear();
}

bool MdlAdagradLightParameters::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tpu.MdlAdagradLightParameters)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // float l2 = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(13u /* 13 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &l2_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float lr_power = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(21u /* 21 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &lr_power_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float min_servable_mdl_benefit = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(29u /* 29 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &min_servable_mdl_benefit_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float mdl_mix_in_margin = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(37u /* 37 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &mdl_mix_in_margin_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float mdl_benefit_rampup_coeff = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(45u /* 45 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &mdl_benefit_rampup_coeff_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float mdl_min_weight = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(53u /* 53 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &mdl_min_weight_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float benefit_revisit_scale = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(61u /* 61 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &benefit_revisit_scale_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float max_event_benefit = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(69u /* 69 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &max_event_benefit_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float max_total_benefit = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(77u /* 77 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &max_total_benefit_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float mdl_hard_limit = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(85u /* 85 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &mdl_hard_limit_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool hard_limit_min_benefit = 11;
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(88u /* 88 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &hard_limit_min_benefit_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool mdl_regularize = 12;
      case 12: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(96u /* 96 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &mdl_regularize_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float initial_accumulator = 13;
      case 13: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(109u /* 109 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &initial_accumulator_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float initial_weight = 14;
      case 14: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(117u /* 117 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &initial_weight_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float initial_benefit = 15;
      case 15: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(125u /* 125 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &initial_benefit_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tpu.MdlAdagradLightParameters)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tpu.MdlAdagradLightParameters)
  return false;
#undef DO_
}

void MdlAdagradLightParameters::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tpu.MdlAdagradLightParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float l2 = 1;
  if (this->l2() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(1, this->l2(), output);
  }

  // float lr_power = 2;
  if (this->lr_power() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(2, this->lr_power(), output);
  }

  // float min_servable_mdl_benefit = 3;
  if (this->min_servable_mdl_benefit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(3, this->min_servable_mdl_benefit(), output);
  }

  // float mdl_mix_in_margin = 4;
  if (this->mdl_mix_in_margin() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(4, this->mdl_mix_in_margin(), output);
  }

  // float mdl_benefit_rampup_coeff = 5;
  if (this->mdl_benefit_rampup_coeff() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(5, this->mdl_benefit_rampup_coeff(), output);
  }

  // float mdl_min_weight = 6;
  if (this->mdl_min_weight() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(6, this->mdl_min_weight(), output);
  }

  // float benefit_revisit_scale = 7;
  if (this->benefit_revisit_scale() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(7, this->benefit_revisit_scale(), output);
  }

  // float max_event_benefit = 8;
  if (this->max_event_benefit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(8, this->max_event_benefit(), output);
  }

  // float max_total_benefit = 9;
  if (this->max_total_benefit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(9, this->max_total_benefit(), output);
  }

  // float mdl_hard_limit = 10;
  if (this->mdl_hard_limit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(10, this->mdl_hard_limit(), output);
  }

  // bool hard_limit_min_benefit = 11;
  if (this->hard_limit_min_benefit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(11, this->hard_limit_min_benefit(), output);
  }

  // bool mdl_regularize = 12;
  if (this->mdl_regularize() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(12, this->mdl_regularize(), output);
  }

  // float initial_accumulator = 13;
  if (this->initial_accumulator() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(13, this->initial_accumulator(), output);
  }

  // float initial_weight = 14;
  if (this->initial_weight() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(14, this->initial_weight(), output);
  }

  // float initial_benefit = 15;
  if (this->initial_benefit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(15, this->initial_benefit(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tpu.MdlAdagradLightParameters)
}

::google::protobuf::uint8* MdlAdagradLightParameters::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tpu.MdlAdagradLightParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float l2 = 1;
  if (this->l2() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(1, this->l2(), target);
  }

  // float lr_power = 2;
  if (this->lr_power() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(2, this->lr_power(), target);
  }

  // float min_servable_mdl_benefit = 3;
  if (this->min_servable_mdl_benefit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(3, this->min_servable_mdl_benefit(), target);
  }

  // float mdl_mix_in_margin = 4;
  if (this->mdl_mix_in_margin() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(4, this->mdl_mix_in_margin(), target);
  }

  // float mdl_benefit_rampup_coeff = 5;
  if (this->mdl_benefit_rampup_coeff() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(5, this->mdl_benefit_rampup_coeff(), target);
  }

  // float mdl_min_weight = 6;
  if (this->mdl_min_weight() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(6, this->mdl_min_weight(), target);
  }

  // float benefit_revisit_scale = 7;
  if (this->benefit_revisit_scale() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(7, this->benefit_revisit_scale(), target);
  }

  // float max_event_benefit = 8;
  if (this->max_event_benefit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(8, this->max_event_benefit(), target);
  }

  // float max_total_benefit = 9;
  if (this->max_total_benefit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(9, this->max_total_benefit(), target);
  }

  // float mdl_hard_limit = 10;
  if (this->mdl_hard_limit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(10, this->mdl_hard_limit(), target);
  }

  // bool hard_limit_min_benefit = 11;
  if (this->hard_limit_min_benefit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(11, this->hard_limit_min_benefit(), target);
  }

  // bool mdl_regularize = 12;
  if (this->mdl_regularize() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(12, this->mdl_regularize(), target);
  }

  // float initial_accumulator = 13;
  if (this->initial_accumulator() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(13, this->initial_accumulator(), target);
  }

  // float initial_weight = 14;
  if (this->initial_weight() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(14, this->initial_weight(), target);
  }

  // float initial_benefit = 15;
  if (this->initial_benefit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(15, this->initial_benefit(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tpu.MdlAdagradLightParameters)
  return target;
}

size_t MdlAdagradLightParameters::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tpu.MdlAdagradLightParameters)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // float l2 = 1;
  if (this->l2() != 0) {
    total_size += 1 + 4;
  }

  // float lr_power = 2;
  if (this->lr_power() != 0) {
    total_size += 1 + 4;
  }

  // float min_servable_mdl_benefit = 3;
  if (this->min_servable_mdl_benefit() != 0) {
    total_size += 1 + 4;
  }

  // float mdl_mix_in_margin = 4;
  if (this->mdl_mix_in_margin() != 0) {
    total_size += 1 + 4;
  }

  // float mdl_benefit_rampup_coeff = 5;
  if (this->mdl_benefit_rampup_coeff() != 0) {
    total_size += 1 + 4;
  }

  // float mdl_min_weight = 6;
  if (this->mdl_min_weight() != 0) {
    total_size += 1 + 4;
  }

  // float benefit_revisit_scale = 7;
  if (this->benefit_revisit_scale() != 0) {
    total_size += 1 + 4;
  }

  // float max_event_benefit = 8;
  if (this->max_event_benefit() != 0) {
    total_size += 1 + 4;
  }

  // float max_total_benefit = 9;
  if (this->max_total_benefit() != 0) {
    total_size += 1 + 4;
  }

  // float mdl_hard_limit = 10;
  if (this->mdl_hard_limit() != 0) {
    total_size += 1 + 4;
  }

  // bool hard_limit_min_benefit = 11;
  if (this->hard_limit_min_benefit() != 0) {
    total_size += 1 + 1;
  }

  // bool mdl_regularize = 12;
  if (this->mdl_regularize() != 0) {
    total_size += 1 + 1;
  }

  // float initial_accumulator = 13;
  if (this->initial_accumulator() != 0) {
    total_size += 1 + 4;
  }

  // float initial_weight = 14;
  if (this->initial_weight() != 0) {
    total_size += 1 + 4;
  }

  // float initial_benefit = 15;
  if (this->initial_benefit() != 0) {
    total_size += 1 + 4;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void MdlAdagradLightParameters::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tpu.MdlAdagradLightParameters)
  GOOGLE_DCHECK_NE(&from, this);
  const MdlAdagradLightParameters* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MdlAdagradLightParameters>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tpu.MdlAdagradLightParameters)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tpu.MdlAdagradLightParameters)
    MergeFrom(*source);
  }
}

void MdlAdagradLightParameters::MergeFrom(const MdlAdagradLightParameters& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tpu.MdlAdagradLightParameters)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.l2() != 0) {
    set_l2(from.l2());
  }
  if (from.lr_power() != 0) {
    set_lr_power(from.lr_power());
  }
  if (from.min_servable_mdl_benefit() != 0) {
    set_min_servable_mdl_benefit(from.min_servable_mdl_benefit());
  }
  if (from.mdl_mix_in_margin() != 0) {
    set_mdl_mix_in_margin(from.mdl_mix_in_margin());
  }
  if (from.mdl_benefit_rampup_coeff() != 0) {
    set_mdl_benefit_rampup_coeff(from.mdl_benefit_rampup_coeff());
  }
  if (from.mdl_min_weight() != 0) {
    set_mdl_min_weight(from.mdl_min_weight());
  }
  if (from.benefit_revisit_scale() != 0) {
    set_benefit_revisit_scale(from.benefit_revisit_scale());
  }
  if (from.max_event_benefit() != 0) {
    set_max_event_benefit(from.max_event_benefit());
  }
  if (from.max_total_benefit() != 0) {
    set_max_total_benefit(from.max_total_benefit());
  }
  if (from.mdl_hard_limit() != 0) {
    set_mdl_hard_limit(from.mdl_hard_limit());
  }
  if (from.hard_limit_min_benefit() != 0) {
    set_hard_limit_min_benefit(from.hard_limit_min_benefit());
  }
  if (from.mdl_regularize() != 0) {
    set_mdl_regularize(from.mdl_regularize());
  }
  if (from.initial_accumulator() != 0) {
    set_initial_accumulator(from.initial_accumulator());
  }
  if (from.initial_weight() != 0) {
    set_initial_weight(from.initial_weight());
  }
  if (from.initial_benefit() != 0) {
    set_initial_benefit(from.initial_benefit());
  }
}

void MdlAdagradLightParameters::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tpu.MdlAdagradLightParameters)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MdlAdagradLightParameters::CopyFrom(const MdlAdagradLightParameters& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tpu.MdlAdagradLightParameters)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MdlAdagradLightParameters::IsInitialized() const {
  return true;
}

void MdlAdagradLightParameters::Swap(MdlAdagradLightParameters* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MdlAdagradLightParameters::InternalSwap(MdlAdagradLightParameters* other) {
  using std::swap;
  swap(l2_, other->l2_);
  swap(lr_power_, other->lr_power_);
  swap(min_servable_mdl_benefit_, other->min_servable_mdl_benefit_);
  swap(mdl_mix_in_margin_, other->mdl_mix_in_margin_);
  swap(mdl_benefit_rampup_coeff_, other->mdl_benefit_rampup_coeff_);
  swap(mdl_min_weight_, other->mdl_min_weight_);
  swap(benefit_revisit_scale_, other->benefit_revisit_scale_);
  swap(max_event_benefit_, other->max_event_benefit_);
  swap(max_total_benefit_, other->max_total_benefit_);
  swap(mdl_hard_limit_, other->mdl_hard_limit_);
  swap(hard_limit_min_benefit_, other->hard_limit_min_benefit_);
  swap(mdl_regularize_, other->mdl_regularize_);
  swap(initial_accumulator_, other->initial_accumulator_);
  swap(initial_weight_, other->initial_weight_);
  swap(initial_benefit_, other->initial_benefit_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata MdlAdagradLightParameters::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void AdadeltaParameters::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AdadeltaParameters::kRhoFieldNumber;
const int AdadeltaParameters::kEpsilonFieldNumber;
const int AdadeltaParameters::kInitialAccumulatorFieldNumber;
const int AdadeltaParameters::kInitialUpdateFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AdadeltaParameters::AdadeltaParameters()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_AdadeltaParameters.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tpu.AdadeltaParameters)
}
AdadeltaParameters::AdadeltaParameters(const AdadeltaParameters& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&rho_, &from.rho_,
    static_cast<size_t>(reinterpret_cast<char*>(&initial_update_) -
    reinterpret_cast<char*>(&rho_)) + sizeof(initial_update_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.tpu.AdadeltaParameters)
}

void AdadeltaParameters::SharedCtor() {
  ::memset(&rho_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&initial_update_) -
      reinterpret_cast<char*>(&rho_)) + sizeof(initial_update_));
}

AdadeltaParameters::~AdadeltaParameters() {
  // @@protoc_insertion_point(destructor:tensorflow.tpu.AdadeltaParameters)
  SharedDtor();
}

void AdadeltaParameters::SharedDtor() {
}

void AdadeltaParameters::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* AdadeltaParameters::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const AdadeltaParameters& AdadeltaParameters::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_AdadeltaParameters.base);
  return *internal_default_instance();
}


void AdadeltaParameters::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tpu.AdadeltaParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&rho_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&initial_update_) -
      reinterpret_cast<char*>(&rho_)) + sizeof(initial_update_));
  _internal_metadata_.Clear();
}

bool AdadeltaParameters::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tpu.AdadeltaParameters)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // float rho = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(13u /* 13 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &rho_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float epsilon = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(21u /* 21 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &epsilon_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float initial_accumulator = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(29u /* 29 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &initial_accumulator_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float initial_update = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(37u /* 37 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &initial_update_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tpu.AdadeltaParameters)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tpu.AdadeltaParameters)
  return false;
#undef DO_
}

void AdadeltaParameters::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tpu.AdadeltaParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float rho = 1;
  if (this->rho() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(1, this->rho(), output);
  }

  // float epsilon = 2;
  if (this->epsilon() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(2, this->epsilon(), output);
  }

  // float initial_accumulator = 3;
  if (this->initial_accumulator() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(3, this->initial_accumulator(), output);
  }

  // float initial_update = 4;
  if (this->initial_update() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(4, this->initial_update(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tpu.AdadeltaParameters)
}

::google::protobuf::uint8* AdadeltaParameters::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tpu.AdadeltaParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float rho = 1;
  if (this->rho() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(1, this->rho(), target);
  }

  // float epsilon = 2;
  if (this->epsilon() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(2, this->epsilon(), target);
  }

  // float initial_accumulator = 3;
  if (this->initial_accumulator() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(3, this->initial_accumulator(), target);
  }

  // float initial_update = 4;
  if (this->initial_update() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(4, this->initial_update(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tpu.AdadeltaParameters)
  return target;
}

size_t AdadeltaParameters::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tpu.AdadeltaParameters)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // float rho = 1;
  if (this->rho() != 0) {
    total_size += 1 + 4;
  }

  // float epsilon = 2;
  if (this->epsilon() != 0) {
    total_size += 1 + 4;
  }

  // float initial_accumulator = 3;
  if (this->initial_accumulator() != 0) {
    total_size += 1 + 4;
  }

  // float initial_update = 4;
  if (this->initial_update() != 0) {
    total_size += 1 + 4;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AdadeltaParameters::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tpu.AdadeltaParameters)
  GOOGLE_DCHECK_NE(&from, this);
  const AdadeltaParameters* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AdadeltaParameters>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tpu.AdadeltaParameters)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tpu.AdadeltaParameters)
    MergeFrom(*source);
  }
}

void AdadeltaParameters::MergeFrom(const AdadeltaParameters& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tpu.AdadeltaParameters)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.rho() != 0) {
    set_rho(from.rho());
  }
  if (from.epsilon() != 0) {
    set_epsilon(from.epsilon());
  }
  if (from.initial_accumulator() != 0) {
    set_initial_accumulator(from.initial_accumulator());
  }
  if (from.initial_update() != 0) {
    set_initial_update(from.initial_update());
  }
}

void AdadeltaParameters::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tpu.AdadeltaParameters)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AdadeltaParameters::CopyFrom(const AdadeltaParameters& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tpu.AdadeltaParameters)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AdadeltaParameters::IsInitialized() const {
  return true;
}

void AdadeltaParameters::Swap(AdadeltaParameters* other) {
  if (other == this) return;
  InternalSwap(other);
}
void AdadeltaParameters::InternalSwap(AdadeltaParameters* other) {
  using std::swap;
  swap(rho_, other->rho_);
  swap(epsilon_, other->epsilon_);
  swap(initial_accumulator_, other->initial_accumulator_);
  swap(initial_update_, other->initial_update_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata AdadeltaParameters::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ProximalAdagradParameters::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ProximalAdagradParameters::kL1FieldNumber;
const int ProximalAdagradParameters::kL2FieldNumber;
const int ProximalAdagradParameters::kInitialAccumulatorFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ProximalAdagradParameters::ProximalAdagradParameters()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_ProximalAdagradParameters.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tpu.ProximalAdagradParameters)
}
ProximalAdagradParameters::ProximalAdagradParameters(const ProximalAdagradParameters& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&l1_, &from.l1_,
    static_cast<size_t>(reinterpret_cast<char*>(&initial_accumulator_) -
    reinterpret_cast<char*>(&l1_)) + sizeof(initial_accumulator_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.tpu.ProximalAdagradParameters)
}

void ProximalAdagradParameters::SharedCtor() {
  ::memset(&l1_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&initial_accumulator_) -
      reinterpret_cast<char*>(&l1_)) + sizeof(initial_accumulator_));
}

ProximalAdagradParameters::~ProximalAdagradParameters() {
  // @@protoc_insertion_point(destructor:tensorflow.tpu.ProximalAdagradParameters)
  SharedDtor();
}

void ProximalAdagradParameters::SharedDtor() {
}

void ProximalAdagradParameters::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ProximalAdagradParameters::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ProximalAdagradParameters& ProximalAdagradParameters::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_ProximalAdagradParameters.base);
  return *internal_default_instance();
}


void ProximalAdagradParameters::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tpu.ProximalAdagradParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&l1_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&initial_accumulator_) -
      reinterpret_cast<char*>(&l1_)) + sizeof(initial_accumulator_));
  _internal_metadata_.Clear();
}

bool ProximalAdagradParameters::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tpu.ProximalAdagradParameters)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // float l1 = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(13u /* 13 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &l1_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float l2 = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(21u /* 21 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &l2_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float initial_accumulator = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(29u /* 29 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &initial_accumulator_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tpu.ProximalAdagradParameters)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tpu.ProximalAdagradParameters)
  return false;
#undef DO_
}

void ProximalAdagradParameters::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tpu.ProximalAdagradParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float l1 = 1;
  if (this->l1() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(1, this->l1(), output);
  }

  // float l2 = 2;
  if (this->l2() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(2, this->l2(), output);
  }

  // float initial_accumulator = 3;
  if (this->initial_accumulator() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(3, this->initial_accumulator(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tpu.ProximalAdagradParameters)
}

::google::protobuf::uint8* ProximalAdagradParameters::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tpu.ProximalAdagradParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float l1 = 1;
  if (this->l1() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(1, this->l1(), target);
  }

  // float l2 = 2;
  if (this->l2() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(2, this->l2(), target);
  }

  // float initial_accumulator = 3;
  if (this->initial_accumulator() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(3, this->initial_accumulator(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tpu.ProximalAdagradParameters)
  return target;
}

size_t ProximalAdagradParameters::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tpu.ProximalAdagradParameters)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // float l1 = 1;
  if (this->l1() != 0) {
    total_size += 1 + 4;
  }

  // float l2 = 2;
  if (this->l2() != 0) {
    total_size += 1 + 4;
  }

  // float initial_accumulator = 3;
  if (this->initial_accumulator() != 0) {
    total_size += 1 + 4;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ProximalAdagradParameters::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tpu.ProximalAdagradParameters)
  GOOGLE_DCHECK_NE(&from, this);
  const ProximalAdagradParameters* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ProximalAdagradParameters>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tpu.ProximalAdagradParameters)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tpu.ProximalAdagradParameters)
    MergeFrom(*source);
  }
}

void ProximalAdagradParameters::MergeFrom(const ProximalAdagradParameters& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tpu.ProximalAdagradParameters)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.l1() != 0) {
    set_l1(from.l1());
  }
  if (from.l2() != 0) {
    set_l2(from.l2());
  }
  if (from.initial_accumulator() != 0) {
    set_initial_accumulator(from.initial_accumulator());
  }
}

void ProximalAdagradParameters::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tpu.ProximalAdagradParameters)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ProximalAdagradParameters::CopyFrom(const ProximalAdagradParameters& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tpu.ProximalAdagradParameters)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ProximalAdagradParameters::IsInitialized() const {
  return true;
}

void ProximalAdagradParameters::Swap(ProximalAdagradParameters* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ProximalAdagradParameters::InternalSwap(ProximalAdagradParameters* other) {
  using std::swap;
  swap(l1_, other->l1_);
  swap(l2_, other->l2_);
  swap(initial_accumulator_, other->initial_accumulator_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ProximalAdagradParameters::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void GradientAccumulationStatus::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GradientAccumulationStatus::GradientAccumulationStatus()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_GradientAccumulationStatus.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tpu.GradientAccumulationStatus)
}
GradientAccumulationStatus::GradientAccumulationStatus(const GradientAccumulationStatus& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.tpu.GradientAccumulationStatus)
}

void GradientAccumulationStatus::SharedCtor() {
}

GradientAccumulationStatus::~GradientAccumulationStatus() {
  // @@protoc_insertion_point(destructor:tensorflow.tpu.GradientAccumulationStatus)
  SharedDtor();
}

void GradientAccumulationStatus::SharedDtor() {
}

void GradientAccumulationStatus::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* GradientAccumulationStatus::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const GradientAccumulationStatus& GradientAccumulationStatus::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_GradientAccumulationStatus.base);
  return *internal_default_instance();
}


void GradientAccumulationStatus::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tpu.GradientAccumulationStatus)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_metadata_.Clear();
}

bool GradientAccumulationStatus::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tpu.GradientAccumulationStatus)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, _internal_metadata_.mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tpu.GradientAccumulationStatus)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tpu.GradientAccumulationStatus)
  return false;
#undef DO_
}

void GradientAccumulationStatus::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tpu.GradientAccumulationStatus)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tpu.GradientAccumulationStatus)
}

::google::protobuf::uint8* GradientAccumulationStatus::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tpu.GradientAccumulationStatus)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tpu.GradientAccumulationStatus)
  return target;
}

size_t GradientAccumulationStatus::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tpu.GradientAccumulationStatus)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void GradientAccumulationStatus::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tpu.GradientAccumulationStatus)
  GOOGLE_DCHECK_NE(&from, this);
  const GradientAccumulationStatus* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GradientAccumulationStatus>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tpu.GradientAccumulationStatus)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tpu.GradientAccumulationStatus)
    MergeFrom(*source);
  }
}

void GradientAccumulationStatus::MergeFrom(const GradientAccumulationStatus& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tpu.GradientAccumulationStatus)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

}

void GradientAccumulationStatus::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tpu.GradientAccumulationStatus)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GradientAccumulationStatus::CopyFrom(const GradientAccumulationStatus& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tpu.GradientAccumulationStatus)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GradientAccumulationStatus::IsInitialized() const {
  return true;
}

void GradientAccumulationStatus::Swap(GradientAccumulationStatus* other) {
  if (other == this) return;
  InternalSwap(other);
}
void GradientAccumulationStatus::InternalSwap(GradientAccumulationStatus* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata GradientAccumulationStatus::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void HotIdOptimizerConfiguration::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int HotIdOptimizerConfiguration::kStatusFieldNumber;
const int HotIdOptimizerConfiguration::kFrequencyThresholdFieldNumber;
const int HotIdOptimizerConfiguration::kMaxIdCountFieldNumber;
const int HotIdOptimizerConfiguration::kMaxSlotCountFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

HotIdOptimizerConfiguration::HotIdOptimizerConfiguration()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_HotIdOptimizerConfiguration.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tpu.HotIdOptimizerConfiguration)
}
HotIdOptimizerConfiguration::HotIdOptimizerConfiguration(const HotIdOptimizerConfiguration& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&status_, &from.status_,
    static_cast<size_t>(reinterpret_cast<char*>(&max_slot_count_) -
    reinterpret_cast<char*>(&status_)) + sizeof(max_slot_count_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.tpu.HotIdOptimizerConfiguration)
}

void HotIdOptimizerConfiguration::SharedCtor() {
  ::memset(&status_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&max_slot_count_) -
      reinterpret_cast<char*>(&status_)) + sizeof(max_slot_count_));
}

HotIdOptimizerConfiguration::~HotIdOptimizerConfiguration() {
  // @@protoc_insertion_point(destructor:tensorflow.tpu.HotIdOptimizerConfiguration)
  SharedDtor();
}

void HotIdOptimizerConfiguration::SharedDtor() {
}

void HotIdOptimizerConfiguration::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* HotIdOptimizerConfiguration::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const HotIdOptimizerConfiguration& HotIdOptimizerConfiguration::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_HotIdOptimizerConfiguration.base);
  return *internal_default_instance();
}


void HotIdOptimizerConfiguration::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tpu.HotIdOptimizerConfiguration)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&status_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&max_slot_count_) -
      reinterpret_cast<char*>(&status_)) + sizeof(max_slot_count_));
  _internal_metadata_.Clear();
}

bool HotIdOptimizerConfiguration::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tpu.HotIdOptimizerConfiguration)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.tpu.HotIdOptimizerConfiguration.Status status = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_status(static_cast< ::tensorflow::tpu::HotIdOptimizerConfiguration_Status >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float frequency_threshold = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(21u /* 21 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &frequency_threshold_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 max_id_count = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &max_id_count_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 max_slot_count = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &max_slot_count_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tpu.HotIdOptimizerConfiguration)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tpu.HotIdOptimizerConfiguration)
  return false;
#undef DO_
}

void HotIdOptimizerConfiguration::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tpu.HotIdOptimizerConfiguration)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.tpu.HotIdOptimizerConfiguration.Status status = 1;
  if (this->status() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->status(), output);
  }

  // float frequency_threshold = 2;
  if (this->frequency_threshold() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(2, this->frequency_threshold(), output);
  }

  // int32 max_id_count = 3;
  if (this->max_id_count() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->max_id_count(), output);
  }

  // int32 max_slot_count = 4;
  if (this->max_slot_count() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->max_slot_count(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tpu.HotIdOptimizerConfiguration)
}

::google::protobuf::uint8* HotIdOptimizerConfiguration::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tpu.HotIdOptimizerConfiguration)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.tpu.HotIdOptimizerConfiguration.Status status = 1;
  if (this->status() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->status(), target);
  }

  // float frequency_threshold = 2;
  if (this->frequency_threshold() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(2, this->frequency_threshold(), target);
  }

  // int32 max_id_count = 3;
  if (this->max_id_count() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->max_id_count(), target);
  }

  // int32 max_slot_count = 4;
  if (this->max_slot_count() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->max_slot_count(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tpu.HotIdOptimizerConfiguration)
  return target;
}

size_t HotIdOptimizerConfiguration::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tpu.HotIdOptimizerConfiguration)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .tensorflow.tpu.HotIdOptimizerConfiguration.Status status = 1;
  if (this->status() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->status());
  }

  // float frequency_threshold = 2;
  if (this->frequency_threshold() != 0) {
    total_size += 1 + 4;
  }

  // int32 max_id_count = 3;
  if (this->max_id_count() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->max_id_count());
  }

  // int32 max_slot_count = 4;
  if (this->max_slot_count() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->max_slot_count());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void HotIdOptimizerConfiguration::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tpu.HotIdOptimizerConfiguration)
  GOOGLE_DCHECK_NE(&from, this);
  const HotIdOptimizerConfiguration* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const HotIdOptimizerConfiguration>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tpu.HotIdOptimizerConfiguration)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tpu.HotIdOptimizerConfiguration)
    MergeFrom(*source);
  }
}

void HotIdOptimizerConfiguration::MergeFrom(const HotIdOptimizerConfiguration& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tpu.HotIdOptimizerConfiguration)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.status() != 0) {
    set_status(from.status());
  }
  if (from.frequency_threshold() != 0) {
    set_frequency_threshold(from.frequency_threshold());
  }
  if (from.max_id_count() != 0) {
    set_max_id_count(from.max_id_count());
  }
  if (from.max_slot_count() != 0) {
    set_max_slot_count(from.max_slot_count());
  }
}

void HotIdOptimizerConfiguration::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tpu.HotIdOptimizerConfiguration)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void HotIdOptimizerConfiguration::CopyFrom(const HotIdOptimizerConfiguration& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tpu.HotIdOptimizerConfiguration)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HotIdOptimizerConfiguration::IsInitialized() const {
  return true;
}

void HotIdOptimizerConfiguration::Swap(HotIdOptimizerConfiguration* other) {
  if (other == this) return;
  InternalSwap(other);
}
void HotIdOptimizerConfiguration::InternalSwap(HotIdOptimizerConfiguration* other) {
  using std::swap;
  swap(status_, other->status_);
  swap(frequency_threshold_, other->frequency_threshold_);
  swap(max_id_count_, other->max_id_count_);
  swap(max_slot_count_, other->max_slot_count_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata HotIdOptimizerConfiguration::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void OptimizationParameters::InitAsDefaultInstance() {
  ::tensorflow::tpu::_OptimizationParameters_default_instance_._instance.get_mutable()->learning_rate_ = const_cast< ::tensorflow::tpu::LearningRate*>(
      ::tensorflow::tpu::LearningRate::internal_default_instance());
  ::tensorflow::tpu::_OptimizationParameters_default_instance_._instance.get_mutable()->clipping_limits_ = const_cast< ::tensorflow::tpu::ClippingLimits*>(
      ::tensorflow::tpu::ClippingLimits::internal_default_instance());
  ::tensorflow::tpu::_OptimizationParameters_default_instance_._instance.get_mutable()->gradient_clipping_limits_ = const_cast< ::tensorflow::tpu::ClippingLimits*>(
      ::tensorflow::tpu::ClippingLimits::internal_default_instance());
  ::tensorflow::tpu::_OptimizationParameters_default_instance_._instance.get_mutable()->hot_id_optimizer_configuration_ = const_cast< ::tensorflow::tpu::HotIdOptimizerConfiguration*>(
      ::tensorflow::tpu::HotIdOptimizerConfiguration::internal_default_instance());
  ::tensorflow::tpu::_OptimizationParameters_default_instance_.adagrad_ = const_cast< ::tensorflow::tpu::AdagradParameters*>(
      ::tensorflow::tpu::AdagradParameters::internal_default_instance());
  ::tensorflow::tpu::_OptimizationParameters_default_instance_.stochastic_gradient_descent_ = const_cast< ::tensorflow::tpu::StochasticGradientDescentParameters*>(
      ::tensorflow::tpu::StochasticGradientDescentParameters::internal_default_instance());
  ::tensorflow::tpu::_OptimizationParameters_default_instance_.ftrl_ = const_cast< ::tensorflow::tpu::FtrlParameters*>(
      ::tensorflow::tpu::FtrlParameters::internal_default_instance());
  ::tensorflow::tpu::_OptimizationParameters_default_instance_.adam_ = const_cast< ::tensorflow::tpu::AdamParameters*>(
      ::tensorflow::tpu::AdamParameters::internal_default_instance());
  ::tensorflow::tpu::_OptimizationParameters_default_instance_.momentum_ = const_cast< ::tensorflow::tpu::MomentumParameters*>(
      ::tensorflow::tpu::MomentumParameters::internal_default_instance());
  ::tensorflow::tpu::_OptimizationParameters_default_instance_.rms_prop_ = const_cast< ::tensorflow::tpu::RmsPropParameters*>(
      ::tensorflow::tpu::RmsPropParameters::internal_default_instance());
  ::tensorflow::tpu::_OptimizationParameters_default_instance_.centered_rms_prop_ = const_cast< ::tensorflow::tpu::CenteredRmsPropParameters*>(
      ::tensorflow::tpu::CenteredRmsPropParameters::internal_default_instance());
  ::tensorflow::tpu::_OptimizationParameters_default_instance_.mdl_adagrad_light_ = const_cast< ::tensorflow::tpu::MdlAdagradLightParameters*>(
      ::tensorflow::tpu::MdlAdagradLightParameters::internal_default_instance());
  ::tensorflow::tpu::_OptimizationParameters_default_instance_.adadelta_ = const_cast< ::tensorflow::tpu::AdadeltaParameters*>(
      ::tensorflow::tpu::AdadeltaParameters::internal_default_instance());
  ::tensorflow::tpu::_OptimizationParameters_default_instance_.proximal_adagrad_ = const_cast< ::tensorflow::tpu::ProximalAdagradParameters*>(
      ::tensorflow::tpu::ProximalAdagradParameters::internal_default_instance());
}
void OptimizationParameters::set_allocated_adagrad(::tensorflow::tpu::AdagradParameters* adagrad) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_parameters();
  if (adagrad) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      adagrad = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, adagrad, submessage_arena);
    }
    set_has_adagrad();
    parameters_.adagrad_ = adagrad;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.OptimizationParameters.adagrad)
}
void OptimizationParameters::set_allocated_stochastic_gradient_descent(::tensorflow::tpu::StochasticGradientDescentParameters* stochastic_gradient_descent) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_parameters();
  if (stochastic_gradient_descent) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      stochastic_gradient_descent = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, stochastic_gradient_descent, submessage_arena);
    }
    set_has_stochastic_gradient_descent();
    parameters_.stochastic_gradient_descent_ = stochastic_gradient_descent;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.OptimizationParameters.stochastic_gradient_descent)
}
void OptimizationParameters::set_allocated_ftrl(::tensorflow::tpu::FtrlParameters* ftrl) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_parameters();
  if (ftrl) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      ftrl = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, ftrl, submessage_arena);
    }
    set_has_ftrl();
    parameters_.ftrl_ = ftrl;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.OptimizationParameters.ftrl)
}
void OptimizationParameters::set_allocated_adam(::tensorflow::tpu::AdamParameters* adam) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_parameters();
  if (adam) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      adam = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, adam, submessage_arena);
    }
    set_has_adam();
    parameters_.adam_ = adam;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.OptimizationParameters.adam)
}
void OptimizationParameters::set_allocated_momentum(::tensorflow::tpu::MomentumParameters* momentum) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_parameters();
  if (momentum) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      momentum = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, momentum, submessage_arena);
    }
    set_has_momentum();
    parameters_.momentum_ = momentum;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.OptimizationParameters.momentum)
}
void OptimizationParameters::set_allocated_rms_prop(::tensorflow::tpu::RmsPropParameters* rms_prop) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_parameters();
  if (rms_prop) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      rms_prop = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, rms_prop, submessage_arena);
    }
    set_has_rms_prop();
    parameters_.rms_prop_ = rms_prop;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.OptimizationParameters.rms_prop)
}
void OptimizationParameters::set_allocated_centered_rms_prop(::tensorflow::tpu::CenteredRmsPropParameters* centered_rms_prop) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_parameters();
  if (centered_rms_prop) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      centered_rms_prop = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, centered_rms_prop, submessage_arena);
    }
    set_has_centered_rms_prop();
    parameters_.centered_rms_prop_ = centered_rms_prop;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.OptimizationParameters.centered_rms_prop)
}
void OptimizationParameters::set_allocated_mdl_adagrad_light(::tensorflow::tpu::MdlAdagradLightParameters* mdl_adagrad_light) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_parameters();
  if (mdl_adagrad_light) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      mdl_adagrad_light = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, mdl_adagrad_light, submessage_arena);
    }
    set_has_mdl_adagrad_light();
    parameters_.mdl_adagrad_light_ = mdl_adagrad_light;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.OptimizationParameters.mdl_adagrad_light)
}
void OptimizationParameters::set_allocated_adadelta(::tensorflow::tpu::AdadeltaParameters* adadelta) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_parameters();
  if (adadelta) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      adadelta = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, adadelta, submessage_arena);
    }
    set_has_adadelta();
    parameters_.adadelta_ = adadelta;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.OptimizationParameters.adadelta)
}
void OptimizationParameters::set_allocated_proximal_adagrad(::tensorflow::tpu::ProximalAdagradParameters* proximal_adagrad) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_parameters();
  if (proximal_adagrad) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      proximal_adagrad = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, proximal_adagrad, submessage_arena);
    }
    set_has_proximal_adagrad();
    parameters_.proximal_adagrad_ = proximal_adagrad;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.OptimizationParameters.proximal_adagrad)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int OptimizationParameters::kLearningRateFieldNumber;
const int OptimizationParameters::kClippingLimitsFieldNumber;
const int OptimizationParameters::kGradientClippingLimitsFieldNumber;
const int OptimizationParameters::kWeightDecayFactorFieldNumber;
const int OptimizationParameters::kGradientAccumulationStatusFieldNumber;
const int OptimizationParameters::kHotIdOptimizerConfigurationFieldNumber;
const int OptimizationParameters::kAdagradFieldNumber;
const int OptimizationParameters::kStochasticGradientDescentFieldNumber;
const int OptimizationParameters::kFtrlFieldNumber;
const int OptimizationParameters::kAdamFieldNumber;
const int OptimizationParameters::kMomentumFieldNumber;
const int OptimizationParameters::kRmsPropFieldNumber;
const int OptimizationParameters::kCenteredRmsPropFieldNumber;
const int OptimizationParameters::kMdlAdagradLightFieldNumber;
const int OptimizationParameters::kAdadeltaFieldNumber;
const int OptimizationParameters::kProximalAdagradFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

OptimizationParameters::OptimizationParameters()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_OptimizationParameters.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tpu.OptimizationParameters)
}
OptimizationParameters::OptimizationParameters(const OptimizationParameters& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_clipping_limits()) {
    clipping_limits_ = new ::tensorflow::tpu::ClippingLimits(*from.clipping_limits_);
  } else {
    clipping_limits_ = NULL;
  }
  if (from.has_gradient_clipping_limits()) {
    gradient_clipping_limits_ = new ::tensorflow::tpu::ClippingLimits(*from.gradient_clipping_limits_);
  } else {
    gradient_clipping_limits_ = NULL;
  }
  if (from.has_learning_rate()) {
    learning_rate_ = new ::tensorflow::tpu::LearningRate(*from.learning_rate_);
  } else {
    learning_rate_ = NULL;
  }
  if (from.has_hot_id_optimizer_configuration()) {
    hot_id_optimizer_configuration_ = new ::tensorflow::tpu::HotIdOptimizerConfiguration(*from.hot_id_optimizer_configuration_);
  } else {
    hot_id_optimizer_configuration_ = NULL;
  }
  ::memcpy(&weight_decay_factor_, &from.weight_decay_factor_,
    static_cast<size_t>(reinterpret_cast<char*>(&gradient_accumulation_status_) -
    reinterpret_cast<char*>(&weight_decay_factor_)) + sizeof(gradient_accumulation_status_));
  clear_has_parameters();
  switch (from.parameters_case()) {
    case kAdagrad: {
      mutable_adagrad()->::tensorflow::tpu::AdagradParameters::MergeFrom(from.adagrad());
      break;
    }
    case kStochasticGradientDescent: {
      mutable_stochastic_gradient_descent()->::tensorflow::tpu::StochasticGradientDescentParameters::MergeFrom(from.stochastic_gradient_descent());
      break;
    }
    case kFtrl: {
      mutable_ftrl()->::tensorflow::tpu::FtrlParameters::MergeFrom(from.ftrl());
      break;
    }
    case kAdam: {
      mutable_adam()->::tensorflow::tpu::AdamParameters::MergeFrom(from.adam());
      break;
    }
    case kMomentum: {
      mutable_momentum()->::tensorflow::tpu::MomentumParameters::MergeFrom(from.momentum());
      break;
    }
    case kRmsProp: {
      mutable_rms_prop()->::tensorflow::tpu::RmsPropParameters::MergeFrom(from.rms_prop());
      break;
    }
    case kCenteredRmsProp: {
      mutable_centered_rms_prop()->::tensorflow::tpu::CenteredRmsPropParameters::MergeFrom(from.centered_rms_prop());
      break;
    }
    case kMdlAdagradLight: {
      mutable_mdl_adagrad_light()->::tensorflow::tpu::MdlAdagradLightParameters::MergeFrom(from.mdl_adagrad_light());
      break;
    }
    case kAdadelta: {
      mutable_adadelta()->::tensorflow::tpu::AdadeltaParameters::MergeFrom(from.adadelta());
      break;
    }
    case kProximalAdagrad: {
      mutable_proximal_adagrad()->::tensorflow::tpu::ProximalAdagradParameters::MergeFrom(from.proximal_adagrad());
      break;
    }
    case PARAMETERS_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.tpu.OptimizationParameters)
}

void OptimizationParameters::SharedCtor() {
  ::memset(&clipping_limits_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&gradient_accumulation_status_) -
      reinterpret_cast<char*>(&clipping_limits_)) + sizeof(gradient_accumulation_status_));
  clear_has_parameters();
}

OptimizationParameters::~OptimizationParameters() {
  // @@protoc_insertion_point(destructor:tensorflow.tpu.OptimizationParameters)
  SharedDtor();
}

void OptimizationParameters::SharedDtor() {
  if (this != internal_default_instance()) delete clipping_limits_;
  if (this != internal_default_instance()) delete gradient_clipping_limits_;
  if (this != internal_default_instance()) delete learning_rate_;
  if (this != internal_default_instance()) delete hot_id_optimizer_configuration_;
  if (has_parameters()) {
    clear_parameters();
  }
}

void OptimizationParameters::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* OptimizationParameters::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const OptimizationParameters& OptimizationParameters::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_OptimizationParameters.base);
  return *internal_default_instance();
}


void OptimizationParameters::clear_parameters() {
// @@protoc_insertion_point(one_of_clear_start:tensorflow.tpu.OptimizationParameters)
  switch (parameters_case()) {
    case kAdagrad: {
      delete parameters_.adagrad_;
      break;
    }
    case kStochasticGradientDescent: {
      delete parameters_.stochastic_gradient_descent_;
      break;
    }
    case kFtrl: {
      delete parameters_.ftrl_;
      break;
    }
    case kAdam: {
      delete parameters_.adam_;
      break;
    }
    case kMomentum: {
      delete parameters_.momentum_;
      break;
    }
    case kRmsProp: {
      delete parameters_.rms_prop_;
      break;
    }
    case kCenteredRmsProp: {
      delete parameters_.centered_rms_prop_;
      break;
    }
    case kMdlAdagradLight: {
      delete parameters_.mdl_adagrad_light_;
      break;
    }
    case kAdadelta: {
      delete parameters_.adadelta_;
      break;
    }
    case kProximalAdagrad: {
      delete parameters_.proximal_adagrad_;
      break;
    }
    case PARAMETERS_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = PARAMETERS_NOT_SET;
}


void OptimizationParameters::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tpu.OptimizationParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && clipping_limits_ != NULL) {
    delete clipping_limits_;
  }
  clipping_limits_ = NULL;
  if (GetArenaNoVirtual() == NULL && gradient_clipping_limits_ != NULL) {
    delete gradient_clipping_limits_;
  }
  gradient_clipping_limits_ = NULL;
  if (GetArenaNoVirtual() == NULL && learning_rate_ != NULL) {
    delete learning_rate_;
  }
  learning_rate_ = NULL;
  if (GetArenaNoVirtual() == NULL && hot_id_optimizer_configuration_ != NULL) {
    delete hot_id_optimizer_configuration_;
  }
  hot_id_optimizer_configuration_ = NULL;
  ::memset(&weight_decay_factor_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&gradient_accumulation_status_) -
      reinterpret_cast<char*>(&weight_decay_factor_)) + sizeof(gradient_accumulation_status_));
  clear_parameters();
  _internal_metadata_.Clear();
}

bool OptimizationParameters::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tpu.OptimizationParameters)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(16383u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.tpu.ClippingLimits clipping_limits = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_clipping_limits()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.tpu.AdagradParameters adagrad = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_adagrad()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.tpu.StochasticGradientDescentParameters stochastic_gradient_descent = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_stochastic_gradient_descent()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.tpu.FtrlParameters ftrl = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_ftrl()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.tpu.AdamParameters adam = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_adam()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.tpu.ClippingLimits gradient_clipping_limits = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_gradient_clipping_limits()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.tpu.MomentumParameters momentum = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(66u /* 66 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_momentum()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.tpu.RmsPropParameters rms_prop = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(74u /* 74 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_rms_prop()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.tpu.CenteredRmsPropParameters centered_rms_prop = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(82u /* 82 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_centered_rms_prop()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.tpu.MdlAdagradLightParameters mdl_adagrad_light = 11;
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(90u /* 90 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_mdl_adagrad_light()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.tpu.AdadeltaParameters adadelta = 12;
      case 12: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(98u /* 98 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_adadelta()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.tpu.LearningRate learning_rate = 13;
      case 13: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(106u /* 106 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_learning_rate()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.tpu.ProximalAdagradParameters proximal_adagrad = 14;
      case 14: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(114u /* 114 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_proximal_adagrad()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // float weight_decay_factor = 16;
      case 16: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(133u /* 133 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &weight_decay_factor_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.tpu.GradientAccumulationStatus.Status gradient_accumulation_status = 17;
      case 17: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(136u /* 136 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_gradient_accumulation_status(static_cast< ::tensorflow::tpu::GradientAccumulationStatus_Status >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.tpu.HotIdOptimizerConfiguration hot_id_optimizer_configuration = 18;
      case 18: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(146u /* 146 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_hot_id_optimizer_configuration()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tpu.OptimizationParameters)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tpu.OptimizationParameters)
  return false;
#undef DO_
}

void OptimizationParameters::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tpu.OptimizationParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.tpu.ClippingLimits clipping_limits = 2;
  if (this->has_clipping_limits()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_clipping_limits(), output);
  }

  // .tensorflow.tpu.AdagradParameters adagrad = 3;
  if (has_adagrad()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->_internal_adagrad(), output);
  }

  // .tensorflow.tpu.StochasticGradientDescentParameters stochastic_gradient_descent = 4;
  if (has_stochastic_gradient_descent()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->_internal_stochastic_gradient_descent(), output);
  }

  // .tensorflow.tpu.FtrlParameters ftrl = 5;
  if (has_ftrl()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, this->_internal_ftrl(), output);
  }

  // .tensorflow.tpu.AdamParameters adam = 6;
  if (has_adam()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, this->_internal_adam(), output);
  }

  // .tensorflow.tpu.ClippingLimits gradient_clipping_limits = 7;
  if (this->has_gradient_clipping_limits()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7, this->_internal_gradient_clipping_limits(), output);
  }

  // .tensorflow.tpu.MomentumParameters momentum = 8;
  if (has_momentum()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8, this->_internal_momentum(), output);
  }

  // .tensorflow.tpu.RmsPropParameters rms_prop = 9;
  if (has_rms_prop()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, this->_internal_rms_prop(), output);
  }

  // .tensorflow.tpu.CenteredRmsPropParameters centered_rms_prop = 10;
  if (has_centered_rms_prop()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      10, this->_internal_centered_rms_prop(), output);
  }

  // .tensorflow.tpu.MdlAdagradLightParameters mdl_adagrad_light = 11;
  if (has_mdl_adagrad_light()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      11, this->_internal_mdl_adagrad_light(), output);
  }

  // .tensorflow.tpu.AdadeltaParameters adadelta = 12;
  if (has_adadelta()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      12, this->_internal_adadelta(), output);
  }

  // .tensorflow.tpu.LearningRate learning_rate = 13;
  if (this->has_learning_rate()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      13, this->_internal_learning_rate(), output);
  }

  // .tensorflow.tpu.ProximalAdagradParameters proximal_adagrad = 14;
  if (has_proximal_adagrad()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      14, this->_internal_proximal_adagrad(), output);
  }

  // float weight_decay_factor = 16;
  if (this->weight_decay_factor() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(16, this->weight_decay_factor(), output);
  }

  // .tensorflow.tpu.GradientAccumulationStatus.Status gradient_accumulation_status = 17;
  if (this->gradient_accumulation_status() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      17, this->gradient_accumulation_status(), output);
  }

  // .tensorflow.tpu.HotIdOptimizerConfiguration hot_id_optimizer_configuration = 18;
  if (this->has_hot_id_optimizer_configuration()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      18, this->_internal_hot_id_optimizer_configuration(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tpu.OptimizationParameters)
}

::google::protobuf::uint8* OptimizationParameters::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tpu.OptimizationParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.tpu.ClippingLimits clipping_limits = 2;
  if (this->has_clipping_limits()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_clipping_limits(), deterministic, target);
  }

  // .tensorflow.tpu.AdagradParameters adagrad = 3;
  if (has_adagrad()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->_internal_adagrad(), deterministic, target);
  }

  // .tensorflow.tpu.StochasticGradientDescentParameters stochastic_gradient_descent = 4;
  if (has_stochastic_gradient_descent()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->_internal_stochastic_gradient_descent(), deterministic, target);
  }

  // .tensorflow.tpu.FtrlParameters ftrl = 5;
  if (has_ftrl()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        5, this->_internal_ftrl(), deterministic, target);
  }

  // .tensorflow.tpu.AdamParameters adam = 6;
  if (has_adam()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        6, this->_internal_adam(), deterministic, target);
  }

  // .tensorflow.tpu.ClippingLimits gradient_clipping_limits = 7;
  if (this->has_gradient_clipping_limits()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        7, this->_internal_gradient_clipping_limits(), deterministic, target);
  }

  // .tensorflow.tpu.MomentumParameters momentum = 8;
  if (has_momentum()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        8, this->_internal_momentum(), deterministic, target);
  }

  // .tensorflow.tpu.RmsPropParameters rms_prop = 9;
  if (has_rms_prop()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        9, this->_internal_rms_prop(), deterministic, target);
  }

  // .tensorflow.tpu.CenteredRmsPropParameters centered_rms_prop = 10;
  if (has_centered_rms_prop()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        10, this->_internal_centered_rms_prop(), deterministic, target);
  }

  // .tensorflow.tpu.MdlAdagradLightParameters mdl_adagrad_light = 11;
  if (has_mdl_adagrad_light()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        11, this->_internal_mdl_adagrad_light(), deterministic, target);
  }

  // .tensorflow.tpu.AdadeltaParameters adadelta = 12;
  if (has_adadelta()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        12, this->_internal_adadelta(), deterministic, target);
  }

  // .tensorflow.tpu.LearningRate learning_rate = 13;
  if (this->has_learning_rate()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        13, this->_internal_learning_rate(), deterministic, target);
  }

  // .tensorflow.tpu.ProximalAdagradParameters proximal_adagrad = 14;
  if (has_proximal_adagrad()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        14, this->_internal_proximal_adagrad(), deterministic, target);
  }

  // float weight_decay_factor = 16;
  if (this->weight_decay_factor() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(16, this->weight_decay_factor(), target);
  }

  // .tensorflow.tpu.GradientAccumulationStatus.Status gradient_accumulation_status = 17;
  if (this->gradient_accumulation_status() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      17, this->gradient_accumulation_status(), target);
  }

  // .tensorflow.tpu.HotIdOptimizerConfiguration hot_id_optimizer_configuration = 18;
  if (this->has_hot_id_optimizer_configuration()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        18, this->_internal_hot_id_optimizer_configuration(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tpu.OptimizationParameters)
  return target;
}

size_t OptimizationParameters::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tpu.OptimizationParameters)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .tensorflow.tpu.ClippingLimits clipping_limits = 2;
  if (this->has_clipping_limits()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *clipping_limits_);
  }

  // .tensorflow.tpu.ClippingLimits gradient_clipping_limits = 7;
  if (this->has_gradient_clipping_limits()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *gradient_clipping_limits_);
  }

  // .tensorflow.tpu.LearningRate learning_rate = 13;
  if (this->has_learning_rate()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *learning_rate_);
  }

  // .tensorflow.tpu.HotIdOptimizerConfiguration hot_id_optimizer_configuration = 18;
  if (this->has_hot_id_optimizer_configuration()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *hot_id_optimizer_configuration_);
  }

  // float weight_decay_factor = 16;
  if (this->weight_decay_factor() != 0) {
    total_size += 2 + 4;
  }

  // .tensorflow.tpu.GradientAccumulationStatus.Status gradient_accumulation_status = 17;
  if (this->gradient_accumulation_status() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->gradient_accumulation_status());
  }

  switch (parameters_case()) {
    // .tensorflow.tpu.AdagradParameters adagrad = 3;
    case kAdagrad: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *parameters_.adagrad_);
      break;
    }
    // .tensorflow.tpu.StochasticGradientDescentParameters stochastic_gradient_descent = 4;
    case kStochasticGradientDescent: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *parameters_.stochastic_gradient_descent_);
      break;
    }
    // .tensorflow.tpu.FtrlParameters ftrl = 5;
    case kFtrl: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *parameters_.ftrl_);
      break;
    }
    // .tensorflow.tpu.AdamParameters adam = 6;
    case kAdam: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *parameters_.adam_);
      break;
    }
    // .tensorflow.tpu.MomentumParameters momentum = 8;
    case kMomentum: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *parameters_.momentum_);
      break;
    }
    // .tensorflow.tpu.RmsPropParameters rms_prop = 9;
    case kRmsProp: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *parameters_.rms_prop_);
      break;
    }
    // .tensorflow.tpu.CenteredRmsPropParameters centered_rms_prop = 10;
    case kCenteredRmsProp: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *parameters_.centered_rms_prop_);
      break;
    }
    // .tensorflow.tpu.MdlAdagradLightParameters mdl_adagrad_light = 11;
    case kMdlAdagradLight: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *parameters_.mdl_adagrad_light_);
      break;
    }
    // .tensorflow.tpu.AdadeltaParameters adadelta = 12;
    case kAdadelta: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *parameters_.adadelta_);
      break;
    }
    // .tensorflow.tpu.ProximalAdagradParameters proximal_adagrad = 14;
    case kProximalAdagrad: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *parameters_.proximal_adagrad_);
      break;
    }
    case PARAMETERS_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void OptimizationParameters::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tpu.OptimizationParameters)
  GOOGLE_DCHECK_NE(&from, this);
  const OptimizationParameters* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const OptimizationParameters>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tpu.OptimizationParameters)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tpu.OptimizationParameters)
    MergeFrom(*source);
  }
}

void OptimizationParameters::MergeFrom(const OptimizationParameters& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tpu.OptimizationParameters)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_clipping_limits()) {
    mutable_clipping_limits()->::tensorflow::tpu::ClippingLimits::MergeFrom(from.clipping_limits());
  }
  if (from.has_gradient_clipping_limits()) {
    mutable_gradient_clipping_limits()->::tensorflow::tpu::ClippingLimits::MergeFrom(from.gradient_clipping_limits());
  }
  if (from.has_learning_rate()) {
    mutable_learning_rate()->::tensorflow::tpu::LearningRate::MergeFrom(from.learning_rate());
  }
  if (from.has_hot_id_optimizer_configuration()) {
    mutable_hot_id_optimizer_configuration()->::tensorflow::tpu::HotIdOptimizerConfiguration::MergeFrom(from.hot_id_optimizer_configuration());
  }
  if (from.weight_decay_factor() != 0) {
    set_weight_decay_factor(from.weight_decay_factor());
  }
  if (from.gradient_accumulation_status() != 0) {
    set_gradient_accumulation_status(from.gradient_accumulation_status());
  }
  switch (from.parameters_case()) {
    case kAdagrad: {
      mutable_adagrad()->::tensorflow::tpu::AdagradParameters::MergeFrom(from.adagrad());
      break;
    }
    case kStochasticGradientDescent: {
      mutable_stochastic_gradient_descent()->::tensorflow::tpu::StochasticGradientDescentParameters::MergeFrom(from.stochastic_gradient_descent());
      break;
    }
    case kFtrl: {
      mutable_ftrl()->::tensorflow::tpu::FtrlParameters::MergeFrom(from.ftrl());
      break;
    }
    case kAdam: {
      mutable_adam()->::tensorflow::tpu::AdamParameters::MergeFrom(from.adam());
      break;
    }
    case kMomentum: {
      mutable_momentum()->::tensorflow::tpu::MomentumParameters::MergeFrom(from.momentum());
      break;
    }
    case kRmsProp: {
      mutable_rms_prop()->::tensorflow::tpu::RmsPropParameters::MergeFrom(from.rms_prop());
      break;
    }
    case kCenteredRmsProp: {
      mutable_centered_rms_prop()->::tensorflow::tpu::CenteredRmsPropParameters::MergeFrom(from.centered_rms_prop());
      break;
    }
    case kMdlAdagradLight: {
      mutable_mdl_adagrad_light()->::tensorflow::tpu::MdlAdagradLightParameters::MergeFrom(from.mdl_adagrad_light());
      break;
    }
    case kAdadelta: {
      mutable_adadelta()->::tensorflow::tpu::AdadeltaParameters::MergeFrom(from.adadelta());
      break;
    }
    case kProximalAdagrad: {
      mutable_proximal_adagrad()->::tensorflow::tpu::ProximalAdagradParameters::MergeFrom(from.proximal_adagrad());
      break;
    }
    case PARAMETERS_NOT_SET: {
      break;
    }
  }
}

void OptimizationParameters::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tpu.OptimizationParameters)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OptimizationParameters::CopyFrom(const OptimizationParameters& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tpu.OptimizationParameters)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OptimizationParameters::IsInitialized() const {
  return true;
}

void OptimizationParameters::Swap(OptimizationParameters* other) {
  if (other == this) return;
  InternalSwap(other);
}
void OptimizationParameters::InternalSwap(OptimizationParameters* other) {
  using std::swap;
  swap(clipping_limits_, other->clipping_limits_);
  swap(gradient_clipping_limits_, other->gradient_clipping_limits_);
  swap(learning_rate_, other->learning_rate_);
  swap(hot_id_optimizer_configuration_, other->hot_id_optimizer_configuration_);
  swap(weight_decay_factor_, other->weight_decay_factor_);
  swap(gradient_accumulation_status_, other->gradient_accumulation_status_);
  swap(parameters_, other->parameters_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata OptimizationParameters::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void StateVariableSpecification_UserDefined::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int StateVariableSpecification_UserDefined::kPaddingInitialValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

StateVariableSpecification_UserDefined::StateVariableSpecification_UserDefined()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_StateVariableSpecification_UserDefined.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tpu.StateVariableSpecification.UserDefined)
}
StateVariableSpecification_UserDefined::StateVariableSpecification_UserDefined(const StateVariableSpecification_UserDefined& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  padding_initial_value_ = from.padding_initial_value_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.tpu.StateVariableSpecification.UserDefined)
}

void StateVariableSpecification_UserDefined::SharedCtor() {
  padding_initial_value_ = 0;
}

StateVariableSpecification_UserDefined::~StateVariableSpecification_UserDefined() {
  // @@protoc_insertion_point(destructor:tensorflow.tpu.StateVariableSpecification.UserDefined)
  SharedDtor();
}

void StateVariableSpecification_UserDefined::SharedDtor() {
}

void StateVariableSpecification_UserDefined::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* StateVariableSpecification_UserDefined::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const StateVariableSpecification_UserDefined& StateVariableSpecification_UserDefined::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_StateVariableSpecification_UserDefined.base);
  return *internal_default_instance();
}


void StateVariableSpecification_UserDefined::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tpu.StateVariableSpecification.UserDefined)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  padding_initial_value_ = 0;
  _internal_metadata_.Clear();
}

bool StateVariableSpecification_UserDefined::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tpu.StateVariableSpecification.UserDefined)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // double padding_initial_value = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(9u /* 9 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &padding_initial_value_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tpu.StateVariableSpecification.UserDefined)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tpu.StateVariableSpecification.UserDefined)
  return false;
#undef DO_
}

void StateVariableSpecification_UserDefined::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tpu.StateVariableSpecification.UserDefined)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double padding_initial_value = 1;
  if (this->padding_initial_value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(1, this->padding_initial_value(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tpu.StateVariableSpecification.UserDefined)
}

::google::protobuf::uint8* StateVariableSpecification_UserDefined::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tpu.StateVariableSpecification.UserDefined)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double padding_initial_value = 1;
  if (this->padding_initial_value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(1, this->padding_initial_value(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tpu.StateVariableSpecification.UserDefined)
  return target;
}

size_t StateVariableSpecification_UserDefined::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tpu.StateVariableSpecification.UserDefined)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // double padding_initial_value = 1;
  if (this->padding_initial_value() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void StateVariableSpecification_UserDefined::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tpu.StateVariableSpecification.UserDefined)
  GOOGLE_DCHECK_NE(&from, this);
  const StateVariableSpecification_UserDefined* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const StateVariableSpecification_UserDefined>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tpu.StateVariableSpecification.UserDefined)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tpu.StateVariableSpecification.UserDefined)
    MergeFrom(*source);
  }
}

void StateVariableSpecification_UserDefined::MergeFrom(const StateVariableSpecification_UserDefined& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tpu.StateVariableSpecification.UserDefined)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.padding_initial_value() != 0) {
    set_padding_initial_value(from.padding_initial_value());
  }
}

void StateVariableSpecification_UserDefined::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tpu.StateVariableSpecification.UserDefined)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StateVariableSpecification_UserDefined::CopyFrom(const StateVariableSpecification_UserDefined& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tpu.StateVariableSpecification.UserDefined)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StateVariableSpecification_UserDefined::IsInitialized() const {
  return true;
}

void StateVariableSpecification_UserDefined::Swap(StateVariableSpecification_UserDefined* other) {
  if (other == this) return;
  InternalSwap(other);
}
void StateVariableSpecification_UserDefined::InternalSwap(StateVariableSpecification_UserDefined* other) {
  using std::swap;
  swap(padding_initial_value_, other->padding_initial_value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata StateVariableSpecification_UserDefined::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void StateVariableSpecification_FillWithConstant::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int StateVariableSpecification_FillWithConstant::kInitialValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

StateVariableSpecification_FillWithConstant::StateVariableSpecification_FillWithConstant()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_StateVariableSpecification_FillWithConstant.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tpu.StateVariableSpecification.FillWithConstant)
}
StateVariableSpecification_FillWithConstant::StateVariableSpecification_FillWithConstant(const StateVariableSpecification_FillWithConstant& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  initial_value_ = from.initial_value_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.tpu.StateVariableSpecification.FillWithConstant)
}

void StateVariableSpecification_FillWithConstant::SharedCtor() {
  initial_value_ = 0;
}

StateVariableSpecification_FillWithConstant::~StateVariableSpecification_FillWithConstant() {
  // @@protoc_insertion_point(destructor:tensorflow.tpu.StateVariableSpecification.FillWithConstant)
  SharedDtor();
}

void StateVariableSpecification_FillWithConstant::SharedDtor() {
}

void StateVariableSpecification_FillWithConstant::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* StateVariableSpecification_FillWithConstant::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const StateVariableSpecification_FillWithConstant& StateVariableSpecification_FillWithConstant::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_StateVariableSpecification_FillWithConstant.base);
  return *internal_default_instance();
}


void StateVariableSpecification_FillWithConstant::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tpu.StateVariableSpecification.FillWithConstant)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  initial_value_ = 0;
  _internal_metadata_.Clear();
}

bool StateVariableSpecification_FillWithConstant::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tpu.StateVariableSpecification.FillWithConstant)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // double initial_value = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(9u /* 9 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &initial_value_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tpu.StateVariableSpecification.FillWithConstant)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tpu.StateVariableSpecification.FillWithConstant)
  return false;
#undef DO_
}

void StateVariableSpecification_FillWithConstant::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tpu.StateVariableSpecification.FillWithConstant)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double initial_value = 1;
  if (this->initial_value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(1, this->initial_value(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tpu.StateVariableSpecification.FillWithConstant)
}

::google::protobuf::uint8* StateVariableSpecification_FillWithConstant::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tpu.StateVariableSpecification.FillWithConstant)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double initial_value = 1;
  if (this->initial_value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(1, this->initial_value(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tpu.StateVariableSpecification.FillWithConstant)
  return target;
}

size_t StateVariableSpecification_FillWithConstant::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tpu.StateVariableSpecification.FillWithConstant)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // double initial_value = 1;
  if (this->initial_value() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void StateVariableSpecification_FillWithConstant::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tpu.StateVariableSpecification.FillWithConstant)
  GOOGLE_DCHECK_NE(&from, this);
  const StateVariableSpecification_FillWithConstant* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const StateVariableSpecification_FillWithConstant>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tpu.StateVariableSpecification.FillWithConstant)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tpu.StateVariableSpecification.FillWithConstant)
    MergeFrom(*source);
  }
}

void StateVariableSpecification_FillWithConstant::MergeFrom(const StateVariableSpecification_FillWithConstant& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tpu.StateVariableSpecification.FillWithConstant)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.initial_value() != 0) {
    set_initial_value(from.initial_value());
  }
}

void StateVariableSpecification_FillWithConstant::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tpu.StateVariableSpecification.FillWithConstant)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StateVariableSpecification_FillWithConstant::CopyFrom(const StateVariableSpecification_FillWithConstant& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tpu.StateVariableSpecification.FillWithConstant)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StateVariableSpecification_FillWithConstant::IsInitialized() const {
  return true;
}

void StateVariableSpecification_FillWithConstant::Swap(StateVariableSpecification_FillWithConstant* other) {
  if (other == this) return;
  InternalSwap(other);
}
void StateVariableSpecification_FillWithConstant::InternalSwap(StateVariableSpecification_FillWithConstant* other) {
  using std::swap;
  swap(initial_value_, other->initial_value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata StateVariableSpecification_FillWithConstant::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void StateVariableSpecification::InitAsDefaultInstance() {
  ::tensorflow::tpu::_StateVariableSpecification_default_instance_.user_defined_ = const_cast< ::tensorflow::tpu::StateVariableSpecification_UserDefined*>(
      ::tensorflow::tpu::StateVariableSpecification_UserDefined::internal_default_instance());
  ::tensorflow::tpu::_StateVariableSpecification_default_instance_.fill_with_constant_ = const_cast< ::tensorflow::tpu::StateVariableSpecification_FillWithConstant*>(
      ::tensorflow::tpu::StateVariableSpecification_FillWithConstant::internal_default_instance());
}
void StateVariableSpecification::set_allocated_user_defined(::tensorflow::tpu::StateVariableSpecification_UserDefined* user_defined) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_usage();
  if (user_defined) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      user_defined = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, user_defined, submessage_arena);
    }
    set_has_user_defined();
    usage_.user_defined_ = user_defined;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.StateVariableSpecification.user_defined)
}
void StateVariableSpecification::set_allocated_fill_with_constant(::tensorflow::tpu::StateVariableSpecification_FillWithConstant* fill_with_constant) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_usage();
  if (fill_with_constant) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      fill_with_constant = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, fill_with_constant, submessage_arena);
    }
    set_has_fill_with_constant();
    usage_.fill_with_constant_ = fill_with_constant;
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.StateVariableSpecification.fill_with_constant)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int StateVariableSpecification::kNameFieldNumber;
const int StateVariableSpecification::kUserDefinedFieldNumber;
const int StateVariableSpecification::kFillWithConstantFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

StateVariableSpecification::StateVariableSpecification()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_StateVariableSpecification.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.tpu.StateVariableSpecification)
}
StateVariableSpecification::StateVariableSpecification(const StateVariableSpecification& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  clear_has_usage();
  switch (from.usage_case()) {
    case kUserDefined: {
      mutable_user_defined()->::tensorflow::tpu::StateVariableSpecification_UserDefined::MergeFrom(from.user_defined());
      break;
    }
    case kFillWithConstant: {
      mutable_fill_with_constant()->::tensorflow::tpu::StateVariableSpecification_FillWithConstant::MergeFrom(from.fill_with_constant());
      break;
    }
    case USAGE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.tpu.StateVariableSpecification)
}

void StateVariableSpecification::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_usage();
}

StateVariableSpecification::~StateVariableSpecification() {
  // @@protoc_insertion_point(destructor:tensorflow.tpu.StateVariableSpecification)
  SharedDtor();
}

void StateVariableSpecification::SharedDtor() {
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (has_usage()) {
    clear_usage();
  }
}

void StateVariableSpecification::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* StateVariableSpecification::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const StateVariableSpecification& StateVariableSpecification::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::scc_info_StateVariableSpecification.base);
  return *internal_default_instance();
}


void StateVariableSpecification::clear_usage() {
// @@protoc_insertion_point(one_of_clear_start:tensorflow.tpu.StateVariableSpecification)
  switch (usage_case()) {
    case kUserDefined: {
      delete usage_.user_defined_;
      break;
    }
    case kFillWithConstant: {
      delete usage_.fill_with_constant_;
      break;
    }
    case USAGE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = USAGE_NOT_SET;
}


void StateVariableSpecification::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.tpu.StateVariableSpecification)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_usage();
  _internal_metadata_.Clear();
}

bool StateVariableSpecification::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.tpu.StateVariableSpecification)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.tpu.StateVariableSpecification.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.tpu.StateVariableSpecification.UserDefined user_defined = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_user_defined()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.tpu.StateVariableSpecification.FillWithConstant fill_with_constant = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_fill_with_constant()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.tpu.StateVariableSpecification)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.tpu.StateVariableSpecification)
  return false;
#undef DO_
}

void StateVariableSpecification::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.tpu.StateVariableSpecification)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tpu.StateVariableSpecification.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // .tensorflow.tpu.StateVariableSpecification.UserDefined user_defined = 2;
  if (has_user_defined()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_user_defined(), output);
  }

  // .tensorflow.tpu.StateVariableSpecification.FillWithConstant fill_with_constant = 3;
  if (has_fill_with_constant()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->_internal_fill_with_constant(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.tpu.StateVariableSpecification)
}

::google::protobuf::uint8* StateVariableSpecification::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.tpu.StateVariableSpecification)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.tpu.StateVariableSpecification.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // .tensorflow.tpu.StateVariableSpecification.UserDefined user_defined = 2;
  if (has_user_defined()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_user_defined(), deterministic, target);
  }

  // .tensorflow.tpu.StateVariableSpecification.FillWithConstant fill_with_constant = 3;
  if (has_fill_with_constant()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->_internal_fill_with_constant(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.tpu.StateVariableSpecification)
  return target;
}

size_t StateVariableSpecification::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.tpu.StateVariableSpecification)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  switch (usage_case()) {
    // .tensorflow.tpu.StateVariableSpecification.UserDefined user_defined = 2;
    case kUserDefined: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *usage_.user_defined_);
      break;
    }
    // .tensorflow.tpu.StateVariableSpecification.FillWithConstant fill_with_constant = 3;
    case kFillWithConstant: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *usage_.fill_with_constant_);
      break;
    }
    case USAGE_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void StateVariableSpecification::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.tpu.StateVariableSpecification)
  GOOGLE_DCHECK_NE(&from, this);
  const StateVariableSpecification* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const StateVariableSpecification>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.tpu.StateVariableSpecification)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.tpu.StateVariableSpecification)
    MergeFrom(*source);
  }
}

void StateVariableSpecification::MergeFrom(const StateVariableSpecification& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.tpu.StateVariableSpecification)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.name().size() > 0) {

    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  switch (from.usage_case()) {
    case kUserDefined: {
      mutable_user_defined()->::tensorflow::tpu::StateVariableSpecification_UserDefined::MergeFrom(from.user_defined());
      break;
    }
    case kFillWithConstant: {
      mutable_fill_with_constant()->::tensorflow::tpu::StateVariableSpecification_FillWithConstant::MergeFrom(from.fill_with_constant());
      break;
    }
    case USAGE_NOT_SET: {
      break;
    }
  }
}

void StateVariableSpecification::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.tpu.StateVariableSpecification)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StateVariableSpecification::CopyFrom(const StateVariableSpecification& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.tpu.StateVariableSpecification)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StateVariableSpecification::IsInitialized() const {
  return true;
}

void StateVariableSpecification::Swap(StateVariableSpecification* other) {
  if (other == this) return;
  InternalSwap(other);
}
void StateVariableSpecification::InternalSwap(StateVariableSpecification* other) {
  using std::swap;
  name_.Swap(&other->name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(usage_, other->usage_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata StateVariableSpecification::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tpu
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tpu::ClippingLimits* Arena::CreateMaybeMessage< ::tensorflow::tpu::ClippingLimits >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tpu::ClippingLimits >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tpu::DynamicLearningRate* Arena::CreateMaybeMessage< ::tensorflow::tpu::DynamicLearningRate >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tpu::DynamicLearningRate >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tpu::LearningRate* Arena::CreateMaybeMessage< ::tensorflow::tpu::LearningRate >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tpu::LearningRate >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tpu::AdagradParameters* Arena::CreateMaybeMessage< ::tensorflow::tpu::AdagradParameters >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tpu::AdagradParameters >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tpu::StochasticGradientDescentParameters* Arena::CreateMaybeMessage< ::tensorflow::tpu::StochasticGradientDescentParameters >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tpu::StochasticGradientDescentParameters >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tpu::FtrlParameters* Arena::CreateMaybeMessage< ::tensorflow::tpu::FtrlParameters >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tpu::FtrlParameters >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tpu::AdamParameters* Arena::CreateMaybeMessage< ::tensorflow::tpu::AdamParameters >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tpu::AdamParameters >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tpu::MomentumParameters* Arena::CreateMaybeMessage< ::tensorflow::tpu::MomentumParameters >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tpu::MomentumParameters >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tpu::RmsPropParameters* Arena::CreateMaybeMessage< ::tensorflow::tpu::RmsPropParameters >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tpu::RmsPropParameters >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tpu::CenteredRmsPropParameters* Arena::CreateMaybeMessage< ::tensorflow::tpu::CenteredRmsPropParameters >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tpu::CenteredRmsPropParameters >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tpu::MdlAdagradLightParameters* Arena::CreateMaybeMessage< ::tensorflow::tpu::MdlAdagradLightParameters >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tpu::MdlAdagradLightParameters >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tpu::AdadeltaParameters* Arena::CreateMaybeMessage< ::tensorflow::tpu::AdadeltaParameters >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tpu::AdadeltaParameters >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tpu::ProximalAdagradParameters* Arena::CreateMaybeMessage< ::tensorflow::tpu::ProximalAdagradParameters >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tpu::ProximalAdagradParameters >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tpu::GradientAccumulationStatus* Arena::CreateMaybeMessage< ::tensorflow::tpu::GradientAccumulationStatus >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tpu::GradientAccumulationStatus >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tpu::HotIdOptimizerConfiguration* Arena::CreateMaybeMessage< ::tensorflow::tpu::HotIdOptimizerConfiguration >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tpu::HotIdOptimizerConfiguration >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tpu::OptimizationParameters* Arena::CreateMaybeMessage< ::tensorflow::tpu::OptimizationParameters >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tpu::OptimizationParameters >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tpu::StateVariableSpecification_UserDefined* Arena::CreateMaybeMessage< ::tensorflow::tpu::StateVariableSpecification_UserDefined >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tpu::StateVariableSpecification_UserDefined >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* Arena::CreateMaybeMessage< ::tensorflow::tpu::StateVariableSpecification_FillWithConstant >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tpu::StateVariableSpecification_FillWithConstant >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::tpu::StateVariableSpecification* Arena::CreateMaybeMessage< ::tensorflow::tpu::StateVariableSpecification >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::tpu::StateVariableSpecification >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

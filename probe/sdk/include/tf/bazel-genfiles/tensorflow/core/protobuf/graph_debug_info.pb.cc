// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/graph_debug_info.proto

#include "tensorflow/core/protobuf/graph_debug_info.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_GraphDebugInfo_FileLineCol;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_GraphDebugInfo_StackTrace;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_GraphDebugInfo_TracesEntry_DoNotUse;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto
namespace tensorflow {
class GraphDebugInfo_FileLineColDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<GraphDebugInfo_FileLineCol>
      _instance;
} _GraphDebugInfo_FileLineCol_default_instance_;
class GraphDebugInfo_StackTraceDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<GraphDebugInfo_StackTrace>
      _instance;
} _GraphDebugInfo_StackTrace_default_instance_;
class GraphDebugInfo_TracesEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<GraphDebugInfo_TracesEntry_DoNotUse>
      _instance;
} _GraphDebugInfo_TracesEntry_DoNotUse_default_instance_;
class GraphDebugInfoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<GraphDebugInfo>
      _instance;
} _GraphDebugInfo_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto {
static void InitDefaultsGraphDebugInfo_FileLineCol() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_GraphDebugInfo_FileLineCol_default_instance_;
    new (ptr) ::tensorflow::GraphDebugInfo_FileLineCol();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::GraphDebugInfo_FileLineCol::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_GraphDebugInfo_FileLineCol =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsGraphDebugInfo_FileLineCol}, {}};

static void InitDefaultsGraphDebugInfo_StackTrace() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_GraphDebugInfo_StackTrace_default_instance_;
    new (ptr) ::tensorflow::GraphDebugInfo_StackTrace();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::GraphDebugInfo_StackTrace::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_GraphDebugInfo_StackTrace =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsGraphDebugInfo_StackTrace}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto::scc_info_GraphDebugInfo_FileLineCol.base,}};

static void InitDefaultsGraphDebugInfo_TracesEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_GraphDebugInfo_TracesEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::GraphDebugInfo_TracesEntry_DoNotUse();
  }
  ::tensorflow::GraphDebugInfo_TracesEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_GraphDebugInfo_TracesEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsGraphDebugInfo_TracesEntry_DoNotUse}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto::scc_info_GraphDebugInfo_StackTrace.base,}};

static void InitDefaultsGraphDebugInfo() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_GraphDebugInfo_default_instance_;
    new (ptr) ::tensorflow::GraphDebugInfo();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::GraphDebugInfo::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_GraphDebugInfo =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsGraphDebugInfo}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto::scc_info_GraphDebugInfo_TracesEntry_DoNotUse.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_GraphDebugInfo_FileLineCol.base);
  ::google::protobuf::internal::InitSCC(&scc_info_GraphDebugInfo_StackTrace.base);
  ::google::protobuf::internal::InitSCC(&scc_info_GraphDebugInfo_TracesEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_GraphDebugInfo.base);
}

::google::protobuf::Metadata file_level_metadata[4];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphDebugInfo_FileLineCol, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphDebugInfo_FileLineCol, file_index_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphDebugInfo_FileLineCol, line_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphDebugInfo_FileLineCol, col_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphDebugInfo_FileLineCol, func_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphDebugInfo_FileLineCol, code_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphDebugInfo_StackTrace, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphDebugInfo_StackTrace, file_line_cols_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphDebugInfo_TracesEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphDebugInfo_TracesEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphDebugInfo_TracesEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphDebugInfo_TracesEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphDebugInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphDebugInfo, files_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphDebugInfo, traces_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::GraphDebugInfo_FileLineCol)},
  { 10, -1, sizeof(::tensorflow::GraphDebugInfo_StackTrace)},
  { 16, 23, sizeof(::tensorflow::GraphDebugInfo_TracesEntry_DoNotUse)},
  { 25, -1, sizeof(::tensorflow::GraphDebugInfo)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_GraphDebugInfo_FileLineCol_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_GraphDebugInfo_StackTrace_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_GraphDebugInfo_TracesEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_GraphDebugInfo_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/protobuf/graph_debug_info.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 4);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n/tensorflow/core/protobuf/graph_debug_i"
      "nfo.proto\022\ntensorflow\"\325\002\n\016GraphDebugInfo"
      "\022\r\n\005files\030\001 \003(\t\0226\n\006traces\030\002 \003(\0132&.tensor"
      "flow.GraphDebugInfo.TracesEntry\032X\n\013FileL"
      "ineCol\022\022\n\nfile_index\030\001 \001(\005\022\014\n\004line\030\002 \001(\005"
      "\022\013\n\003col\030\003 \001(\005\022\014\n\004func\030\004 \001(\t\022\014\n\004code\030\005 \001("
      "\t\032L\n\nStackTrace\022>\n\016file_line_cols\030\001 \003(\0132"
      "&.tensorflow.GraphDebugInfo.FileLineCol\032"
      "T\n\013TracesEntry\022\013\n\003key\030\001 \001(\t\0224\n\005value\030\002 \001"
      "(\0132%.tensorflow.GraphDebugInfo.StackTrac"
      "e:\0028\001B5\n\030org.tensorflow.frameworkB\024Graph"
      "DebugInfoProtosP\001\370\001\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 468);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/protobuf/graph_debug_info.proto", &protobuf_RegisterTypes);
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto
namespace tensorflow {

// ===================================================================

void GraphDebugInfo_FileLineCol::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GraphDebugInfo_FileLineCol::kFileIndexFieldNumber;
const int GraphDebugInfo_FileLineCol::kLineFieldNumber;
const int GraphDebugInfo_FileLineCol::kColFieldNumber;
const int GraphDebugInfo_FileLineCol::kFuncFieldNumber;
const int GraphDebugInfo_FileLineCol::kCodeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GraphDebugInfo_FileLineCol::GraphDebugInfo_FileLineCol()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto::scc_info_GraphDebugInfo_FileLineCol.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.GraphDebugInfo.FileLineCol)
}
GraphDebugInfo_FileLineCol::GraphDebugInfo_FileLineCol(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto::scc_info_GraphDebugInfo_FileLineCol.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.GraphDebugInfo.FileLineCol)
}
GraphDebugInfo_FileLineCol::GraphDebugInfo_FileLineCol(const GraphDebugInfo_FileLineCol& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  func_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.func().size() > 0) {
    func_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.func(),
      GetArenaNoVirtual());
  }
  code_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.code().size() > 0) {
    code_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.code(),
      GetArenaNoVirtual());
  }
  ::memcpy(&file_index_, &from.file_index_,
    static_cast<size_t>(reinterpret_cast<char*>(&col_) -
    reinterpret_cast<char*>(&file_index_)) + sizeof(col_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.GraphDebugInfo.FileLineCol)
}

void GraphDebugInfo_FileLineCol::SharedCtor() {
  func_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  code_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&file_index_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&col_) -
      reinterpret_cast<char*>(&file_index_)) + sizeof(col_));
}

GraphDebugInfo_FileLineCol::~GraphDebugInfo_FileLineCol() {
  // @@protoc_insertion_point(destructor:tensorflow.GraphDebugInfo.FileLineCol)
  SharedDtor();
}

void GraphDebugInfo_FileLineCol::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  func_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  code_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void GraphDebugInfo_FileLineCol::ArenaDtor(void* object) {
  GraphDebugInfo_FileLineCol* _this = reinterpret_cast< GraphDebugInfo_FileLineCol* >(object);
  (void)_this;
}
void GraphDebugInfo_FileLineCol::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void GraphDebugInfo_FileLineCol::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* GraphDebugInfo_FileLineCol::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const GraphDebugInfo_FileLineCol& GraphDebugInfo_FileLineCol::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto::scc_info_GraphDebugInfo_FileLineCol.base);
  return *internal_default_instance();
}


void GraphDebugInfo_FileLineCol::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.GraphDebugInfo.FileLineCol)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  func_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  code_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  ::memset(&file_index_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&col_) -
      reinterpret_cast<char*>(&file_index_)) + sizeof(col_));
  _internal_metadata_.Clear();
}

bool GraphDebugInfo_FileLineCol::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.GraphDebugInfo.FileLineCol)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 file_index = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &file_index_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 line = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &line_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 col = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &col_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string func = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_func()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->func().data(), static_cast<int>(this->func().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.GraphDebugInfo.FileLineCol.func"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string code = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_code()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->code().data(), static_cast<int>(this->code().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.GraphDebugInfo.FileLineCol.code"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.GraphDebugInfo.FileLineCol)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.GraphDebugInfo.FileLineCol)
  return false;
#undef DO_
}

void GraphDebugInfo_FileLineCol::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.GraphDebugInfo.FileLineCol)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 file_index = 1;
  if (this->file_index() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->file_index(), output);
  }

  // int32 line = 2;
  if (this->line() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->line(), output);
  }

  // int32 col = 3;
  if (this->col() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->col(), output);
  }

  // string func = 4;
  if (this->func().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->func().data(), static_cast<int>(this->func().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GraphDebugInfo.FileLineCol.func");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->func(), output);
  }

  // string code = 5;
  if (this->code().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->code().data(), static_cast<int>(this->code().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GraphDebugInfo.FileLineCol.code");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->code(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.GraphDebugInfo.FileLineCol)
}

::google::protobuf::uint8* GraphDebugInfo_FileLineCol::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.GraphDebugInfo.FileLineCol)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 file_index = 1;
  if (this->file_index() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->file_index(), target);
  }

  // int32 line = 2;
  if (this->line() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->line(), target);
  }

  // int32 col = 3;
  if (this->col() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->col(), target);
  }

  // string func = 4;
  if (this->func().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->func().data(), static_cast<int>(this->func().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GraphDebugInfo.FileLineCol.func");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->func(), target);
  }

  // string code = 5;
  if (this->code().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->code().data(), static_cast<int>(this->code().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GraphDebugInfo.FileLineCol.code");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->code(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.GraphDebugInfo.FileLineCol)
  return target;
}

size_t GraphDebugInfo_FileLineCol::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.GraphDebugInfo.FileLineCol)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string func = 4;
  if (this->func().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->func());
  }

  // string code = 5;
  if (this->code().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->code());
  }

  // int32 file_index = 1;
  if (this->file_index() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->file_index());
  }

  // int32 line = 2;
  if (this->line() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->line());
  }

  // int32 col = 3;
  if (this->col() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->col());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void GraphDebugInfo_FileLineCol::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.GraphDebugInfo.FileLineCol)
  GOOGLE_DCHECK_NE(&from, this);
  const GraphDebugInfo_FileLineCol* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GraphDebugInfo_FileLineCol>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.GraphDebugInfo.FileLineCol)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.GraphDebugInfo.FileLineCol)
    MergeFrom(*source);
  }
}

void GraphDebugInfo_FileLineCol::MergeFrom(const GraphDebugInfo_FileLineCol& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.GraphDebugInfo.FileLineCol)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.func().size() > 0) {
    set_func(from.func());
  }
  if (from.code().size() > 0) {
    set_code(from.code());
  }
  if (from.file_index() != 0) {
    set_file_index(from.file_index());
  }
  if (from.line() != 0) {
    set_line(from.line());
  }
  if (from.col() != 0) {
    set_col(from.col());
  }
}

void GraphDebugInfo_FileLineCol::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.GraphDebugInfo.FileLineCol)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GraphDebugInfo_FileLineCol::CopyFrom(const GraphDebugInfo_FileLineCol& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.GraphDebugInfo.FileLineCol)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GraphDebugInfo_FileLineCol::IsInitialized() const {
  return true;
}

void GraphDebugInfo_FileLineCol::Swap(GraphDebugInfo_FileLineCol* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    GraphDebugInfo_FileLineCol* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void GraphDebugInfo_FileLineCol::UnsafeArenaSwap(GraphDebugInfo_FileLineCol* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void GraphDebugInfo_FileLineCol::InternalSwap(GraphDebugInfo_FileLineCol* other) {
  using std::swap;
  func_.Swap(&other->func_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  code_.Swap(&other->code_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(file_index_, other->file_index_);
  swap(line_, other->line_);
  swap(col_, other->col_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata GraphDebugInfo_FileLineCol::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void GraphDebugInfo_StackTrace::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GraphDebugInfo_StackTrace::kFileLineColsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GraphDebugInfo_StackTrace::GraphDebugInfo_StackTrace()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto::scc_info_GraphDebugInfo_StackTrace.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.GraphDebugInfo.StackTrace)
}
GraphDebugInfo_StackTrace::GraphDebugInfo_StackTrace(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  file_line_cols_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto::scc_info_GraphDebugInfo_StackTrace.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.GraphDebugInfo.StackTrace)
}
GraphDebugInfo_StackTrace::GraphDebugInfo_StackTrace(const GraphDebugInfo_StackTrace& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      file_line_cols_(from.file_line_cols_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.GraphDebugInfo.StackTrace)
}

void GraphDebugInfo_StackTrace::SharedCtor() {
}

GraphDebugInfo_StackTrace::~GraphDebugInfo_StackTrace() {
  // @@protoc_insertion_point(destructor:tensorflow.GraphDebugInfo.StackTrace)
  SharedDtor();
}

void GraphDebugInfo_StackTrace::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void GraphDebugInfo_StackTrace::ArenaDtor(void* object) {
  GraphDebugInfo_StackTrace* _this = reinterpret_cast< GraphDebugInfo_StackTrace* >(object);
  (void)_this;
}
void GraphDebugInfo_StackTrace::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void GraphDebugInfo_StackTrace::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* GraphDebugInfo_StackTrace::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const GraphDebugInfo_StackTrace& GraphDebugInfo_StackTrace::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto::scc_info_GraphDebugInfo_StackTrace.base);
  return *internal_default_instance();
}


void GraphDebugInfo_StackTrace::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.GraphDebugInfo.StackTrace)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  file_line_cols_.Clear();
  _internal_metadata_.Clear();
}

bool GraphDebugInfo_StackTrace::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.GraphDebugInfo.StackTrace)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.GraphDebugInfo.FileLineCol file_line_cols = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_file_line_cols()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.GraphDebugInfo.StackTrace)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.GraphDebugInfo.StackTrace)
  return false;
#undef DO_
}

void GraphDebugInfo_StackTrace::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.GraphDebugInfo.StackTrace)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.GraphDebugInfo.FileLineCol file_line_cols = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->file_line_cols_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->file_line_cols(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.GraphDebugInfo.StackTrace)
}

::google::protobuf::uint8* GraphDebugInfo_StackTrace::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.GraphDebugInfo.StackTrace)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.GraphDebugInfo.FileLineCol file_line_cols = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->file_line_cols_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->file_line_cols(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.GraphDebugInfo.StackTrace)
  return target;
}

size_t GraphDebugInfo_StackTrace::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.GraphDebugInfo.StackTrace)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.GraphDebugInfo.FileLineCol file_line_cols = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->file_line_cols_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->file_line_cols(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void GraphDebugInfo_StackTrace::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.GraphDebugInfo.StackTrace)
  GOOGLE_DCHECK_NE(&from, this);
  const GraphDebugInfo_StackTrace* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GraphDebugInfo_StackTrace>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.GraphDebugInfo.StackTrace)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.GraphDebugInfo.StackTrace)
    MergeFrom(*source);
  }
}

void GraphDebugInfo_StackTrace::MergeFrom(const GraphDebugInfo_StackTrace& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.GraphDebugInfo.StackTrace)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  file_line_cols_.MergeFrom(from.file_line_cols_);
}

void GraphDebugInfo_StackTrace::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.GraphDebugInfo.StackTrace)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GraphDebugInfo_StackTrace::CopyFrom(const GraphDebugInfo_StackTrace& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.GraphDebugInfo.StackTrace)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GraphDebugInfo_StackTrace::IsInitialized() const {
  return true;
}

void GraphDebugInfo_StackTrace::Swap(GraphDebugInfo_StackTrace* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    GraphDebugInfo_StackTrace* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void GraphDebugInfo_StackTrace::UnsafeArenaSwap(GraphDebugInfo_StackTrace* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void GraphDebugInfo_StackTrace::InternalSwap(GraphDebugInfo_StackTrace* other) {
  using std::swap;
  CastToBase(&file_line_cols_)->InternalSwap(CastToBase(&other->file_line_cols_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata GraphDebugInfo_StackTrace::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

GraphDebugInfo_TracesEntry_DoNotUse::GraphDebugInfo_TracesEntry_DoNotUse() {}
GraphDebugInfo_TracesEntry_DoNotUse::GraphDebugInfo_TracesEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void GraphDebugInfo_TracesEntry_DoNotUse::MergeFrom(const GraphDebugInfo_TracesEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata GraphDebugInfo_TracesEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto::file_level_metadata[2];
}
void GraphDebugInfo_TracesEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void GraphDebugInfo::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GraphDebugInfo::kFilesFieldNumber;
const int GraphDebugInfo::kTracesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GraphDebugInfo::GraphDebugInfo()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto::scc_info_GraphDebugInfo.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.GraphDebugInfo)
}
GraphDebugInfo::GraphDebugInfo(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  files_(arena),
  traces_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto::scc_info_GraphDebugInfo.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.GraphDebugInfo)
}
GraphDebugInfo::GraphDebugInfo(const GraphDebugInfo& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      files_(from.files_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  traces_.MergeFrom(from.traces_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.GraphDebugInfo)
}

void GraphDebugInfo::SharedCtor() {
}

GraphDebugInfo::~GraphDebugInfo() {
  // @@protoc_insertion_point(destructor:tensorflow.GraphDebugInfo)
  SharedDtor();
}

void GraphDebugInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void GraphDebugInfo::ArenaDtor(void* object) {
  GraphDebugInfo* _this = reinterpret_cast< GraphDebugInfo* >(object);
  (void)_this;
}
void GraphDebugInfo::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void GraphDebugInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* GraphDebugInfo::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const GraphDebugInfo& GraphDebugInfo::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto::scc_info_GraphDebugInfo.base);
  return *internal_default_instance();
}


void GraphDebugInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.GraphDebugInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  files_.Clear();
  traces_.Clear();
  _internal_metadata_.Clear();
}

bool GraphDebugInfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.GraphDebugInfo)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated string files = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_files()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->files(this->files_size() - 1).data(),
            static_cast<int>(this->files(this->files_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.GraphDebugInfo.files"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, .tensorflow.GraphDebugInfo.StackTrace> traces = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          GraphDebugInfo_TracesEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              GraphDebugInfo_TracesEntry_DoNotUse,
              ::std::string, ::tensorflow::GraphDebugInfo_StackTrace,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::std::string, ::tensorflow::GraphDebugInfo_StackTrace > > parser(&traces_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.GraphDebugInfo.TracesEntry.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.GraphDebugInfo)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.GraphDebugInfo)
  return false;
#undef DO_
}

void GraphDebugInfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.GraphDebugInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string files = 1;
  for (int i = 0, n = this->files_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->files(i).data(), static_cast<int>(this->files(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GraphDebugInfo.files");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->files(i), output);
  }

  // map<string, .tensorflow.GraphDebugInfo.StackTrace> traces = 2;
  if (!this->traces().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::GraphDebugInfo_StackTrace >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.GraphDebugInfo.TracesEntry.key");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->traces().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->traces().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::GraphDebugInfo_StackTrace >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::GraphDebugInfo_StackTrace >::const_iterator
          it = this->traces().begin();
          it != this->traces().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<GraphDebugInfo_TracesEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(traces_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<GraphDebugInfo_TracesEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::GraphDebugInfo_StackTrace >::const_iterator
          it = this->traces().begin();
          it != this->traces().end(); ++it) {
        entry.reset(traces_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.GraphDebugInfo)
}

::google::protobuf::uint8* GraphDebugInfo::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.GraphDebugInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string files = 1;
  for (int i = 0, n = this->files_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->files(i).data(), static_cast<int>(this->files(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GraphDebugInfo.files");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(1, this->files(i), target);
  }

  // map<string, .tensorflow.GraphDebugInfo.StackTrace> traces = 2;
  if (!this->traces().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::tensorflow::GraphDebugInfo_StackTrace >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.GraphDebugInfo.TracesEntry.key");
      }
    };

    if (deterministic &&
        this->traces().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->traces().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::tensorflow::GraphDebugInfo_StackTrace >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::GraphDebugInfo_StackTrace >::const_iterator
          it = this->traces().begin();
          it != this->traces().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<GraphDebugInfo_TracesEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(traces_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<GraphDebugInfo_TracesEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::tensorflow::GraphDebugInfo_StackTrace >::const_iterator
          it = this->traces().begin();
          it != this->traces().end(); ++it) {
        entry.reset(traces_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.GraphDebugInfo)
  return target;
}

size_t GraphDebugInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.GraphDebugInfo)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated string files = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->files_size());
  for (int i = 0, n = this->files_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->files(i));
  }

  // map<string, .tensorflow.GraphDebugInfo.StackTrace> traces = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->traces_size());
  {
    ::std::unique_ptr<GraphDebugInfo_TracesEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::tensorflow::GraphDebugInfo_StackTrace >::const_iterator
        it = this->traces().begin();
        it != this->traces().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(traces_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void GraphDebugInfo::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.GraphDebugInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const GraphDebugInfo* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GraphDebugInfo>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.GraphDebugInfo)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.GraphDebugInfo)
    MergeFrom(*source);
  }
}

void GraphDebugInfo::MergeFrom(const GraphDebugInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.GraphDebugInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  files_.MergeFrom(from.files_);
  traces_.MergeFrom(from.traces_);
}

void GraphDebugInfo::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.GraphDebugInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GraphDebugInfo::CopyFrom(const GraphDebugInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.GraphDebugInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GraphDebugInfo::IsInitialized() const {
  return true;
}

void GraphDebugInfo::Swap(GraphDebugInfo* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    GraphDebugInfo* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void GraphDebugInfo::UnsafeArenaSwap(GraphDebugInfo* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void GraphDebugInfo::InternalSwap(GraphDebugInfo* other) {
  using std::swap;
  files_.InternalSwap(CastToBase(&other->files_));
  traces_.Swap(&other->traces_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata GraphDebugInfo::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fgraph_5fdebug_5finfo_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::GraphDebugInfo_FileLineCol* Arena::CreateMaybeMessage< ::tensorflow::GraphDebugInfo_FileLineCol >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::GraphDebugInfo_FileLineCol >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::GraphDebugInfo_StackTrace* Arena::CreateMaybeMessage< ::tensorflow::GraphDebugInfo_StackTrace >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::GraphDebugInfo_StackTrace >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::GraphDebugInfo_TracesEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::GraphDebugInfo_TracesEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::GraphDebugInfo_TracesEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::GraphDebugInfo* Arena::CreateMaybeMessage< ::tensorflow::GraphDebugInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::GraphDebugInfo >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/struct.proto

#ifndef PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto
#define PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3006001
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#define PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto 

namespace protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[9];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto
namespace tensorflow {
class DictValue;
class DictValueDefaultTypeInternal;
extern DictValueDefaultTypeInternal _DictValue_default_instance_;
class DictValue_FieldsEntry_DoNotUse;
class DictValue_FieldsEntry_DoNotUseDefaultTypeInternal;
extern DictValue_FieldsEntry_DoNotUseDefaultTypeInternal _DictValue_FieldsEntry_DoNotUse_default_instance_;
class ListValue;
class ListValueDefaultTypeInternal;
extern ListValueDefaultTypeInternal _ListValue_default_instance_;
class NamedTupleValue;
class NamedTupleValueDefaultTypeInternal;
extern NamedTupleValueDefaultTypeInternal _NamedTupleValue_default_instance_;
class NoneValue;
class NoneValueDefaultTypeInternal;
extern NoneValueDefaultTypeInternal _NoneValue_default_instance_;
class PairValue;
class PairValueDefaultTypeInternal;
extern PairValueDefaultTypeInternal _PairValue_default_instance_;
class StructuredValue;
class StructuredValueDefaultTypeInternal;
extern StructuredValueDefaultTypeInternal _StructuredValue_default_instance_;
class TensorSpecProto;
class TensorSpecProtoDefaultTypeInternal;
extern TensorSpecProtoDefaultTypeInternal _TensorSpecProto_default_instance_;
class TupleValue;
class TupleValueDefaultTypeInternal;
extern TupleValueDefaultTypeInternal _TupleValue_default_instance_;
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> ::tensorflow::DictValue* Arena::CreateMaybeMessage<::tensorflow::DictValue>(Arena*);
template<> ::tensorflow::DictValue_FieldsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::DictValue_FieldsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::ListValue* Arena::CreateMaybeMessage<::tensorflow::ListValue>(Arena*);
template<> ::tensorflow::NamedTupleValue* Arena::CreateMaybeMessage<::tensorflow::NamedTupleValue>(Arena*);
template<> ::tensorflow::NoneValue* Arena::CreateMaybeMessage<::tensorflow::NoneValue>(Arena*);
template<> ::tensorflow::PairValue* Arena::CreateMaybeMessage<::tensorflow::PairValue>(Arena*);
template<> ::tensorflow::StructuredValue* Arena::CreateMaybeMessage<::tensorflow::StructuredValue>(Arena*);
template<> ::tensorflow::TensorSpecProto* Arena::CreateMaybeMessage<::tensorflow::TensorSpecProto>(Arena*);
template<> ::tensorflow::TupleValue* Arena::CreateMaybeMessage<::tensorflow::TupleValue>(Arena*);
}  // namespace protobuf
}  // namespace google
namespace tensorflow {

// ===================================================================

class StructuredValue : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.StructuredValue) */ {
 public:
  StructuredValue();
  virtual ~StructuredValue();

  StructuredValue(const StructuredValue& from);

  inline StructuredValue& operator=(const StructuredValue& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  StructuredValue(StructuredValue&& from) noexcept
    : StructuredValue() {
    *this = ::std::move(from);
  }

  inline StructuredValue& operator=(StructuredValue&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const StructuredValue& default_instance();

  enum KindCase {
    kNoneValue = 1,
    kFloat64Value = 11,
    kInt64Value = 12,
    kStringValue = 13,
    kBoolValue = 14,
    kTensorShapeValue = 31,
    kTensorDtypeValue = 32,
    kTensorSpecValue = 33,
    kListValue = 51,
    kTupleValue = 52,
    kDictValue = 53,
    kNamedTupleValue = 54,
    KIND_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const StructuredValue* internal_default_instance() {
    return reinterpret_cast<const StructuredValue*>(
               &_StructuredValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  void Swap(StructuredValue* other);
  friend void swap(StructuredValue& a, StructuredValue& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline StructuredValue* New() const final {
    return CreateMaybeMessage<StructuredValue>(NULL);
  }

  StructuredValue* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<StructuredValue>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const StructuredValue& from);
  void MergeFrom(const StructuredValue& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StructuredValue* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .tensorflow.NoneValue none_value = 1;
  bool has_none_value() const;
  void clear_none_value();
  static const int kNoneValueFieldNumber = 1;
  private:
  const ::tensorflow::NoneValue& _internal_none_value() const;
  public:
  const ::tensorflow::NoneValue& none_value() const;
  ::tensorflow::NoneValue* release_none_value();
  ::tensorflow::NoneValue* mutable_none_value();
  void set_allocated_none_value(::tensorflow::NoneValue* none_value);

  // double float64_value = 11;
  private:
  bool has_float64_value() const;
  public:
  void clear_float64_value();
  static const int kFloat64ValueFieldNumber = 11;
  double float64_value() const;
  void set_float64_value(double value);

  // sint64 int64_value = 12;
  private:
  bool has_int64_value() const;
  public:
  void clear_int64_value();
  static const int kInt64ValueFieldNumber = 12;
  ::google::protobuf::int64 int64_value() const;
  void set_int64_value(::google::protobuf::int64 value);

  // string string_value = 13;
  private:
  bool has_string_value() const;
  public:
  void clear_string_value();
  static const int kStringValueFieldNumber = 13;
  const ::std::string& string_value() const;
  void set_string_value(const ::std::string& value);
  #if LANG_CXX11
  void set_string_value(::std::string&& value);
  #endif
  void set_string_value(const char* value);
  void set_string_value(const char* value, size_t size);
  ::std::string* mutable_string_value();
  ::std::string* release_string_value();
  void set_allocated_string_value(::std::string* string_value);

  // bool bool_value = 14;
  private:
  bool has_bool_value() const;
  public:
  void clear_bool_value();
  static const int kBoolValueFieldNumber = 14;
  bool bool_value() const;
  void set_bool_value(bool value);

  // .tensorflow.TensorShapeProto tensor_shape_value = 31;
  bool has_tensor_shape_value() const;
  void clear_tensor_shape_value();
  static const int kTensorShapeValueFieldNumber = 31;
  private:
  const ::tensorflow::TensorShapeProto& _internal_tensor_shape_value() const;
  public:
  const ::tensorflow::TensorShapeProto& tensor_shape_value() const;
  ::tensorflow::TensorShapeProto* release_tensor_shape_value();
  ::tensorflow::TensorShapeProto* mutable_tensor_shape_value();
  void set_allocated_tensor_shape_value(::tensorflow::TensorShapeProto* tensor_shape_value);

  // .tensorflow.DataType tensor_dtype_value = 32;
  private:
  bool has_tensor_dtype_value() const;
  public:
  void clear_tensor_dtype_value();
  static const int kTensorDtypeValueFieldNumber = 32;
  ::tensorflow::DataType tensor_dtype_value() const;
  void set_tensor_dtype_value(::tensorflow::DataType value);

  // .tensorflow.TensorSpecProto tensor_spec_value = 33;
  bool has_tensor_spec_value() const;
  void clear_tensor_spec_value();
  static const int kTensorSpecValueFieldNumber = 33;
  private:
  const ::tensorflow::TensorSpecProto& _internal_tensor_spec_value() const;
  public:
  const ::tensorflow::TensorSpecProto& tensor_spec_value() const;
  ::tensorflow::TensorSpecProto* release_tensor_spec_value();
  ::tensorflow::TensorSpecProto* mutable_tensor_spec_value();
  void set_allocated_tensor_spec_value(::tensorflow::TensorSpecProto* tensor_spec_value);

  // .tensorflow.ListValue list_value = 51;
  bool has_list_value() const;
  void clear_list_value();
  static const int kListValueFieldNumber = 51;
  private:
  const ::tensorflow::ListValue& _internal_list_value() const;
  public:
  const ::tensorflow::ListValue& list_value() const;
  ::tensorflow::ListValue* release_list_value();
  ::tensorflow::ListValue* mutable_list_value();
  void set_allocated_list_value(::tensorflow::ListValue* list_value);

  // .tensorflow.TupleValue tuple_value = 52;
  bool has_tuple_value() const;
  void clear_tuple_value();
  static const int kTupleValueFieldNumber = 52;
  private:
  const ::tensorflow::TupleValue& _internal_tuple_value() const;
  public:
  const ::tensorflow::TupleValue& tuple_value() const;
  ::tensorflow::TupleValue* release_tuple_value();
  ::tensorflow::TupleValue* mutable_tuple_value();
  void set_allocated_tuple_value(::tensorflow::TupleValue* tuple_value);

  // .tensorflow.DictValue dict_value = 53;
  bool has_dict_value() const;
  void clear_dict_value();
  static const int kDictValueFieldNumber = 53;
  private:
  const ::tensorflow::DictValue& _internal_dict_value() const;
  public:
  const ::tensorflow::DictValue& dict_value() const;
  ::tensorflow::DictValue* release_dict_value();
  ::tensorflow::DictValue* mutable_dict_value();
  void set_allocated_dict_value(::tensorflow::DictValue* dict_value);

  // .tensorflow.NamedTupleValue named_tuple_value = 54;
  bool has_named_tuple_value() const;
  void clear_named_tuple_value();
  static const int kNamedTupleValueFieldNumber = 54;
  private:
  const ::tensorflow::NamedTupleValue& _internal_named_tuple_value() const;
  public:
  const ::tensorflow::NamedTupleValue& named_tuple_value() const;
  ::tensorflow::NamedTupleValue* release_named_tuple_value();
  ::tensorflow::NamedTupleValue* mutable_named_tuple_value();
  void set_allocated_named_tuple_value(::tensorflow::NamedTupleValue* named_tuple_value);

  void clear_kind();
  KindCase kind_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.StructuredValue)
 private:
  void set_has_none_value();
  void set_has_float64_value();
  void set_has_int64_value();
  void set_has_string_value();
  void set_has_bool_value();
  void set_has_tensor_shape_value();
  void set_has_tensor_dtype_value();
  void set_has_tensor_spec_value();
  void set_has_list_value();
  void set_has_tuple_value();
  void set_has_dict_value();
  void set_has_named_tuple_value();

  inline bool has_kind() const;
  inline void clear_has_kind();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  union KindUnion {
    KindUnion() {}
    ::tensorflow::NoneValue* none_value_;
    double float64_value_;
    ::google::protobuf::int64 int64_value_;
    ::google::protobuf::internal::ArenaStringPtr string_value_;
    bool bool_value_;
    ::tensorflow::TensorShapeProto* tensor_shape_value_;
    int tensor_dtype_value_;
    ::tensorflow::TensorSpecProto* tensor_spec_value_;
    ::tensorflow::ListValue* list_value_;
    ::tensorflow::TupleValue* tuple_value_;
    ::tensorflow::DictValue* dict_value_;
    ::tensorflow::NamedTupleValue* named_tuple_value_;
  } kind_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class NoneValue : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.NoneValue) */ {
 public:
  NoneValue();
  virtual ~NoneValue();

  NoneValue(const NoneValue& from);

  inline NoneValue& operator=(const NoneValue& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  NoneValue(NoneValue&& from) noexcept
    : NoneValue() {
    *this = ::std::move(from);
  }

  inline NoneValue& operator=(NoneValue&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const NoneValue& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const NoneValue* internal_default_instance() {
    return reinterpret_cast<const NoneValue*>(
               &_NoneValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  void Swap(NoneValue* other);
  friend void swap(NoneValue& a, NoneValue& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline NoneValue* New() const final {
    return CreateMaybeMessage<NoneValue>(NULL);
  }

  NoneValue* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<NoneValue>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const NoneValue& from);
  void MergeFrom(const NoneValue& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NoneValue* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.NoneValue)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ListValue : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.ListValue) */ {
 public:
  ListValue();
  virtual ~ListValue();

  ListValue(const ListValue& from);

  inline ListValue& operator=(const ListValue& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ListValue(ListValue&& from) noexcept
    : ListValue() {
    *this = ::std::move(from);
  }

  inline ListValue& operator=(ListValue&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ListValue& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ListValue* internal_default_instance() {
    return reinterpret_cast<const ListValue*>(
               &_ListValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  void Swap(ListValue* other);
  friend void swap(ListValue& a, ListValue& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ListValue* New() const final {
    return CreateMaybeMessage<ListValue>(NULL);
  }

  ListValue* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<ListValue>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const ListValue& from);
  void MergeFrom(const ListValue& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ListValue* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.StructuredValue values = 1;
  int values_size() const;
  void clear_values();
  static const int kValuesFieldNumber = 1;
  ::tensorflow::StructuredValue* mutable_values(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::StructuredValue >*
      mutable_values();
  const ::tensorflow::StructuredValue& values(int index) const;
  ::tensorflow::StructuredValue* add_values();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::StructuredValue >&
      values() const;

  // @@protoc_insertion_point(class_scope:tensorflow.ListValue)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::StructuredValue > values_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class TupleValue : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.TupleValue) */ {
 public:
  TupleValue();
  virtual ~TupleValue();

  TupleValue(const TupleValue& from);

  inline TupleValue& operator=(const TupleValue& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TupleValue(TupleValue&& from) noexcept
    : TupleValue() {
    *this = ::std::move(from);
  }

  inline TupleValue& operator=(TupleValue&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const TupleValue& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TupleValue* internal_default_instance() {
    return reinterpret_cast<const TupleValue*>(
               &_TupleValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  void Swap(TupleValue* other);
  friend void swap(TupleValue& a, TupleValue& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TupleValue* New() const final {
    return CreateMaybeMessage<TupleValue>(NULL);
  }

  TupleValue* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TupleValue>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TupleValue& from);
  void MergeFrom(const TupleValue& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TupleValue* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.StructuredValue values = 1;
  int values_size() const;
  void clear_values();
  static const int kValuesFieldNumber = 1;
  ::tensorflow::StructuredValue* mutable_values(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::StructuredValue >*
      mutable_values();
  const ::tensorflow::StructuredValue& values(int index) const;
  ::tensorflow::StructuredValue* add_values();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::StructuredValue >&
      values() const;

  // @@protoc_insertion_point(class_scope:tensorflow.TupleValue)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::StructuredValue > values_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class DictValue_FieldsEntry_DoNotUse : public ::google::protobuf::internal::MapEntry<DictValue_FieldsEntry_DoNotUse, 
    ::std::string, ::tensorflow::StructuredValue,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::google::protobuf::internal::MapEntry<DictValue_FieldsEntry_DoNotUse, 
    ::std::string, ::tensorflow::StructuredValue,
    ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
    ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  DictValue_FieldsEntry_DoNotUse();
  DictValue_FieldsEntry_DoNotUse(::google::protobuf::Arena* arena);
  void MergeFrom(const DictValue_FieldsEntry_DoNotUse& other);
  static const DictValue_FieldsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const DictValue_FieldsEntry_DoNotUse*>(&_DictValue_FieldsEntry_DoNotUse_default_instance_); }
  void MergeFrom(const ::google::protobuf::Message& other) final;
  ::google::protobuf::Metadata GetMetadata() const;
};

// -------------------------------------------------------------------

class DictValue : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.DictValue) */ {
 public:
  DictValue();
  virtual ~DictValue();

  DictValue(const DictValue& from);

  inline DictValue& operator=(const DictValue& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  DictValue(DictValue&& from) noexcept
    : DictValue() {
    *this = ::std::move(from);
  }

  inline DictValue& operator=(DictValue&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const DictValue& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DictValue* internal_default_instance() {
    return reinterpret_cast<const DictValue*>(
               &_DictValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  void Swap(DictValue* other);
  friend void swap(DictValue& a, DictValue& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline DictValue* New() const final {
    return CreateMaybeMessage<DictValue>(NULL);
  }

  DictValue* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<DictValue>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const DictValue& from);
  void MergeFrom(const DictValue& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DictValue* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<string, .tensorflow.StructuredValue> fields = 1;
  int fields_size() const;
  void clear_fields();
  static const int kFieldsFieldNumber = 1;
  const ::google::protobuf::Map< ::std::string, ::tensorflow::StructuredValue >&
      fields() const;
  ::google::protobuf::Map< ::std::string, ::tensorflow::StructuredValue >*
      mutable_fields();

  // @@protoc_insertion_point(class_scope:tensorflow.DictValue)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::MapField<
      DictValue_FieldsEntry_DoNotUse,
      ::std::string, ::tensorflow::StructuredValue,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > fields_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class PairValue : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.PairValue) */ {
 public:
  PairValue();
  virtual ~PairValue();

  PairValue(const PairValue& from);

  inline PairValue& operator=(const PairValue& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  PairValue(PairValue&& from) noexcept
    : PairValue() {
    *this = ::std::move(from);
  }

  inline PairValue& operator=(PairValue&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const PairValue& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const PairValue* internal_default_instance() {
    return reinterpret_cast<const PairValue*>(
               &_PairValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  void Swap(PairValue* other);
  friend void swap(PairValue& a, PairValue& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline PairValue* New() const final {
    return CreateMaybeMessage<PairValue>(NULL);
  }

  PairValue* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<PairValue>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const PairValue& from);
  void MergeFrom(const PairValue& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PairValue* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string key = 1;
  void clear_key();
  static const int kKeyFieldNumber = 1;
  const ::std::string& key() const;
  void set_key(const ::std::string& value);
  #if LANG_CXX11
  void set_key(::std::string&& value);
  #endif
  void set_key(const char* value);
  void set_key(const char* value, size_t size);
  ::std::string* mutable_key();
  ::std::string* release_key();
  void set_allocated_key(::std::string* key);

  // .tensorflow.StructuredValue value = 2;
  bool has_value() const;
  void clear_value();
  static const int kValueFieldNumber = 2;
  private:
  const ::tensorflow::StructuredValue& _internal_value() const;
  public:
  const ::tensorflow::StructuredValue& value() const;
  ::tensorflow::StructuredValue* release_value();
  ::tensorflow::StructuredValue* mutable_value();
  void set_allocated_value(::tensorflow::StructuredValue* value);

  // @@protoc_insertion_point(class_scope:tensorflow.PairValue)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr key_;
  ::tensorflow::StructuredValue* value_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class NamedTupleValue : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.NamedTupleValue) */ {
 public:
  NamedTupleValue();
  virtual ~NamedTupleValue();

  NamedTupleValue(const NamedTupleValue& from);

  inline NamedTupleValue& operator=(const NamedTupleValue& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  NamedTupleValue(NamedTupleValue&& from) noexcept
    : NamedTupleValue() {
    *this = ::std::move(from);
  }

  inline NamedTupleValue& operator=(NamedTupleValue&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const NamedTupleValue& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const NamedTupleValue* internal_default_instance() {
    return reinterpret_cast<const NamedTupleValue*>(
               &_NamedTupleValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  void Swap(NamedTupleValue* other);
  friend void swap(NamedTupleValue& a, NamedTupleValue& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline NamedTupleValue* New() const final {
    return CreateMaybeMessage<NamedTupleValue>(NULL);
  }

  NamedTupleValue* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<NamedTupleValue>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const NamedTupleValue& from);
  void MergeFrom(const NamedTupleValue& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NamedTupleValue* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .tensorflow.PairValue values = 2;
  int values_size() const;
  void clear_values();
  static const int kValuesFieldNumber = 2;
  ::tensorflow::PairValue* mutable_values(int index);
  ::google::protobuf::RepeatedPtrField< ::tensorflow::PairValue >*
      mutable_values();
  const ::tensorflow::PairValue& values(int index) const;
  ::tensorflow::PairValue* add_values();
  const ::google::protobuf::RepeatedPtrField< ::tensorflow::PairValue >&
      values() const;

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // @@protoc_insertion_point(class_scope:tensorflow.NamedTupleValue)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::tensorflow::PairValue > values_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class TensorSpecProto : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:tensorflow.TensorSpecProto) */ {
 public:
  TensorSpecProto();
  virtual ~TensorSpecProto();

  TensorSpecProto(const TensorSpecProto& from);

  inline TensorSpecProto& operator=(const TensorSpecProto& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  TensorSpecProto(TensorSpecProto&& from) noexcept
    : TensorSpecProto() {
    *this = ::std::move(from);
  }

  inline TensorSpecProto& operator=(TensorSpecProto&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const TensorSpecProto& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TensorSpecProto* internal_default_instance() {
    return reinterpret_cast<const TensorSpecProto*>(
               &_TensorSpecProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  void Swap(TensorSpecProto* other);
  friend void swap(TensorSpecProto& a, TensorSpecProto& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline TensorSpecProto* New() const final {
    return CreateMaybeMessage<TensorSpecProto>(NULL);
  }

  TensorSpecProto* New(::google::protobuf::Arena* arena) const final {
    return CreateMaybeMessage<TensorSpecProto>(arena);
  }
  void CopyFrom(const ::google::protobuf::Message& from) final;
  void MergeFrom(const ::google::protobuf::Message& from) final;
  void CopyFrom(const TensorSpecProto& from);
  void MergeFrom(const TensorSpecProto& from);
  void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) final;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const final;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorSpecProto* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // .tensorflow.TensorShapeProto shape = 2;
  bool has_shape() const;
  void clear_shape();
  static const int kShapeFieldNumber = 2;
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape() const;
  public:
  const ::tensorflow::TensorShapeProto& shape() const;
  ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);

  // .tensorflow.DataType dtype = 3;
  void clear_dtype();
  static const int kDtypeFieldNumber = 3;
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);

  // @@protoc_insertion_point(class_scope:tensorflow.TensorSpecProto)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::tensorflow::TensorShapeProto* shape_;
  int dtype_;
  mutable ::google::protobuf::internal::CachedSize _cached_size_;
  friend struct ::protobuf_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// StructuredValue

// .tensorflow.NoneValue none_value = 1;
inline bool StructuredValue::has_none_value() const {
  return kind_case() == kNoneValue;
}
inline void StructuredValue::set_has_none_value() {
  _oneof_case_[0] = kNoneValue;
}
inline void StructuredValue::clear_none_value() {
  if (has_none_value()) {
    delete kind_.none_value_;
    clear_has_kind();
  }
}
inline const ::tensorflow::NoneValue& StructuredValue::_internal_none_value() const {
  return *kind_.none_value_;
}
inline ::tensorflow::NoneValue* StructuredValue::release_none_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.none_value)
  if (has_none_value()) {
    clear_has_kind();
      ::tensorflow::NoneValue* temp = kind_.none_value_;
    kind_.none_value_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::NoneValue& StructuredValue::none_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.none_value)
  return has_none_value()
      ? *kind_.none_value_
      : *reinterpret_cast< ::tensorflow::NoneValue*>(&::tensorflow::_NoneValue_default_instance_);
}
inline ::tensorflow::NoneValue* StructuredValue::mutable_none_value() {
  if (!has_none_value()) {
    clear_kind();
    set_has_none_value();
    kind_.none_value_ = CreateMaybeMessage< ::tensorflow::NoneValue >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.none_value)
  return kind_.none_value_;
}

// double float64_value = 11;
inline bool StructuredValue::has_float64_value() const {
  return kind_case() == kFloat64Value;
}
inline void StructuredValue::set_has_float64_value() {
  _oneof_case_[0] = kFloat64Value;
}
inline void StructuredValue::clear_float64_value() {
  if (has_float64_value()) {
    kind_.float64_value_ = 0;
    clear_has_kind();
  }
}
inline double StructuredValue::float64_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.float64_value)
  if (has_float64_value()) {
    return kind_.float64_value_;
  }
  return 0;
}
inline void StructuredValue::set_float64_value(double value) {
  if (!has_float64_value()) {
    clear_kind();
    set_has_float64_value();
  }
  kind_.float64_value_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.StructuredValue.float64_value)
}

// sint64 int64_value = 12;
inline bool StructuredValue::has_int64_value() const {
  return kind_case() == kInt64Value;
}
inline void StructuredValue::set_has_int64_value() {
  _oneof_case_[0] = kInt64Value;
}
inline void StructuredValue::clear_int64_value() {
  if (has_int64_value()) {
    kind_.int64_value_ = GOOGLE_LONGLONG(0);
    clear_has_kind();
  }
}
inline ::google::protobuf::int64 StructuredValue::int64_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.int64_value)
  if (has_int64_value()) {
    return kind_.int64_value_;
  }
  return GOOGLE_LONGLONG(0);
}
inline void StructuredValue::set_int64_value(::google::protobuf::int64 value) {
  if (!has_int64_value()) {
    clear_kind();
    set_has_int64_value();
  }
  kind_.int64_value_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.StructuredValue.int64_value)
}

// string string_value = 13;
inline bool StructuredValue::has_string_value() const {
  return kind_case() == kStringValue;
}
inline void StructuredValue::set_has_string_value() {
  _oneof_case_[0] = kStringValue;
}
inline void StructuredValue::clear_string_value() {
  if (has_string_value()) {
    kind_.string_value_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    clear_has_kind();
  }
}
inline const ::std::string& StructuredValue::string_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.string_value)
  if (has_string_value()) {
    return kind_.string_value_.GetNoArena();
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void StructuredValue::set_string_value(const ::std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.StructuredValue.string_value)
  if (!has_string_value()) {
    clear_kind();
    set_has_string_value();
    kind_.string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  kind_.string_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.StructuredValue.string_value)
}
#if LANG_CXX11
inline void StructuredValue::set_string_value(::std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.StructuredValue.string_value)
  if (!has_string_value()) {
    clear_kind();
    set_has_string_value();
    kind_.string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  kind_.string_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.StructuredValue.string_value)
}
#endif
inline void StructuredValue::set_string_value(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  if (!has_string_value()) {
    clear_kind();
    set_has_string_value();
    kind_.string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  kind_.string_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.StructuredValue.string_value)
}
inline void StructuredValue::set_string_value(const char* value, size_t size) {
  if (!has_string_value()) {
    clear_kind();
    set_has_string_value();
    kind_.string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  kind_.string_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.StructuredValue.string_value)
}
inline ::std::string* StructuredValue::mutable_string_value() {
  if (!has_string_value()) {
    clear_kind();
    set_has_string_value();
    kind_.string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.string_value)
  return kind_.string_value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* StructuredValue::release_string_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.string_value)
  if (has_string_value()) {
    clear_has_kind();
    return kind_.string_value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  } else {
    return NULL;
  }
}
inline void StructuredValue::set_allocated_string_value(::std::string* string_value) {
  if (!has_string_value()) {
    kind_.string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_kind();
  if (string_value != NULL) {
    set_has_string_value();
    kind_.string_value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), string_value);
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.StructuredValue.string_value)
}

// bool bool_value = 14;
inline bool StructuredValue::has_bool_value() const {
  return kind_case() == kBoolValue;
}
inline void StructuredValue::set_has_bool_value() {
  _oneof_case_[0] = kBoolValue;
}
inline void StructuredValue::clear_bool_value() {
  if (has_bool_value()) {
    kind_.bool_value_ = false;
    clear_has_kind();
  }
}
inline bool StructuredValue::bool_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.bool_value)
  if (has_bool_value()) {
    return kind_.bool_value_;
  }
  return false;
}
inline void StructuredValue::set_bool_value(bool value) {
  if (!has_bool_value()) {
    clear_kind();
    set_has_bool_value();
  }
  kind_.bool_value_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.StructuredValue.bool_value)
}

// .tensorflow.TensorShapeProto tensor_shape_value = 31;
inline bool StructuredValue::has_tensor_shape_value() const {
  return kind_case() == kTensorShapeValue;
}
inline void StructuredValue::set_has_tensor_shape_value() {
  _oneof_case_[0] = kTensorShapeValue;
}
inline const ::tensorflow::TensorShapeProto& StructuredValue::_internal_tensor_shape_value() const {
  return *kind_.tensor_shape_value_;
}
inline ::tensorflow::TensorShapeProto* StructuredValue::release_tensor_shape_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.tensor_shape_value)
  if (has_tensor_shape_value()) {
    clear_has_kind();
      ::tensorflow::TensorShapeProto* temp = kind_.tensor_shape_value_;
    kind_.tensor_shape_value_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::TensorShapeProto& StructuredValue::tensor_shape_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.tensor_shape_value)
  return has_tensor_shape_value()
      ? *kind_.tensor_shape_value_
      : *reinterpret_cast< ::tensorflow::TensorShapeProto*>(&::tensorflow::_TensorShapeProto_default_instance_);
}
inline ::tensorflow::TensorShapeProto* StructuredValue::mutable_tensor_shape_value() {
  if (!has_tensor_shape_value()) {
    clear_kind();
    set_has_tensor_shape_value();
    kind_.tensor_shape_value_ = CreateMaybeMessage< ::tensorflow::TensorShapeProto >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.tensor_shape_value)
  return kind_.tensor_shape_value_;
}

// .tensorflow.DataType tensor_dtype_value = 32;
inline bool StructuredValue::has_tensor_dtype_value() const {
  return kind_case() == kTensorDtypeValue;
}
inline void StructuredValue::set_has_tensor_dtype_value() {
  _oneof_case_[0] = kTensorDtypeValue;
}
inline void StructuredValue::clear_tensor_dtype_value() {
  if (has_tensor_dtype_value()) {
    kind_.tensor_dtype_value_ = 0;
    clear_has_kind();
  }
}
inline ::tensorflow::DataType StructuredValue::tensor_dtype_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.tensor_dtype_value)
  if (has_tensor_dtype_value()) {
    return static_cast< ::tensorflow::DataType >(kind_.tensor_dtype_value_);
  }
  return static_cast< ::tensorflow::DataType >(0);
}
inline void StructuredValue::set_tensor_dtype_value(::tensorflow::DataType value) {
  if (!has_tensor_dtype_value()) {
    clear_kind();
    set_has_tensor_dtype_value();
  }
  kind_.tensor_dtype_value_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.StructuredValue.tensor_dtype_value)
}

// .tensorflow.TensorSpecProto tensor_spec_value = 33;
inline bool StructuredValue::has_tensor_spec_value() const {
  return kind_case() == kTensorSpecValue;
}
inline void StructuredValue::set_has_tensor_spec_value() {
  _oneof_case_[0] = kTensorSpecValue;
}
inline void StructuredValue::clear_tensor_spec_value() {
  if (has_tensor_spec_value()) {
    delete kind_.tensor_spec_value_;
    clear_has_kind();
  }
}
inline const ::tensorflow::TensorSpecProto& StructuredValue::_internal_tensor_spec_value() const {
  return *kind_.tensor_spec_value_;
}
inline ::tensorflow::TensorSpecProto* StructuredValue::release_tensor_spec_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.tensor_spec_value)
  if (has_tensor_spec_value()) {
    clear_has_kind();
      ::tensorflow::TensorSpecProto* temp = kind_.tensor_spec_value_;
    kind_.tensor_spec_value_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::TensorSpecProto& StructuredValue::tensor_spec_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.tensor_spec_value)
  return has_tensor_spec_value()
      ? *kind_.tensor_spec_value_
      : *reinterpret_cast< ::tensorflow::TensorSpecProto*>(&::tensorflow::_TensorSpecProto_default_instance_);
}
inline ::tensorflow::TensorSpecProto* StructuredValue::mutable_tensor_spec_value() {
  if (!has_tensor_spec_value()) {
    clear_kind();
    set_has_tensor_spec_value();
    kind_.tensor_spec_value_ = CreateMaybeMessage< ::tensorflow::TensorSpecProto >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.tensor_spec_value)
  return kind_.tensor_spec_value_;
}

// .tensorflow.ListValue list_value = 51;
inline bool StructuredValue::has_list_value() const {
  return kind_case() == kListValue;
}
inline void StructuredValue::set_has_list_value() {
  _oneof_case_[0] = kListValue;
}
inline void StructuredValue::clear_list_value() {
  if (has_list_value()) {
    delete kind_.list_value_;
    clear_has_kind();
  }
}
inline const ::tensorflow::ListValue& StructuredValue::_internal_list_value() const {
  return *kind_.list_value_;
}
inline ::tensorflow::ListValue* StructuredValue::release_list_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.list_value)
  if (has_list_value()) {
    clear_has_kind();
      ::tensorflow::ListValue* temp = kind_.list_value_;
    kind_.list_value_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::ListValue& StructuredValue::list_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.list_value)
  return has_list_value()
      ? *kind_.list_value_
      : *reinterpret_cast< ::tensorflow::ListValue*>(&::tensorflow::_ListValue_default_instance_);
}
inline ::tensorflow::ListValue* StructuredValue::mutable_list_value() {
  if (!has_list_value()) {
    clear_kind();
    set_has_list_value();
    kind_.list_value_ = CreateMaybeMessage< ::tensorflow::ListValue >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.list_value)
  return kind_.list_value_;
}

// .tensorflow.TupleValue tuple_value = 52;
inline bool StructuredValue::has_tuple_value() const {
  return kind_case() == kTupleValue;
}
inline void StructuredValue::set_has_tuple_value() {
  _oneof_case_[0] = kTupleValue;
}
inline void StructuredValue::clear_tuple_value() {
  if (has_tuple_value()) {
    delete kind_.tuple_value_;
    clear_has_kind();
  }
}
inline const ::tensorflow::TupleValue& StructuredValue::_internal_tuple_value() const {
  return *kind_.tuple_value_;
}
inline ::tensorflow::TupleValue* StructuredValue::release_tuple_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.tuple_value)
  if (has_tuple_value()) {
    clear_has_kind();
      ::tensorflow::TupleValue* temp = kind_.tuple_value_;
    kind_.tuple_value_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::TupleValue& StructuredValue::tuple_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.tuple_value)
  return has_tuple_value()
      ? *kind_.tuple_value_
      : *reinterpret_cast< ::tensorflow::TupleValue*>(&::tensorflow::_TupleValue_default_instance_);
}
inline ::tensorflow::TupleValue* StructuredValue::mutable_tuple_value() {
  if (!has_tuple_value()) {
    clear_kind();
    set_has_tuple_value();
    kind_.tuple_value_ = CreateMaybeMessage< ::tensorflow::TupleValue >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.tuple_value)
  return kind_.tuple_value_;
}

// .tensorflow.DictValue dict_value = 53;
inline bool StructuredValue::has_dict_value() const {
  return kind_case() == kDictValue;
}
inline void StructuredValue::set_has_dict_value() {
  _oneof_case_[0] = kDictValue;
}
inline void StructuredValue::clear_dict_value() {
  if (has_dict_value()) {
    delete kind_.dict_value_;
    clear_has_kind();
  }
}
inline const ::tensorflow::DictValue& StructuredValue::_internal_dict_value() const {
  return *kind_.dict_value_;
}
inline ::tensorflow::DictValue* StructuredValue::release_dict_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.dict_value)
  if (has_dict_value()) {
    clear_has_kind();
      ::tensorflow::DictValue* temp = kind_.dict_value_;
    kind_.dict_value_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::DictValue& StructuredValue::dict_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.dict_value)
  return has_dict_value()
      ? *kind_.dict_value_
      : *reinterpret_cast< ::tensorflow::DictValue*>(&::tensorflow::_DictValue_default_instance_);
}
inline ::tensorflow::DictValue* StructuredValue::mutable_dict_value() {
  if (!has_dict_value()) {
    clear_kind();
    set_has_dict_value();
    kind_.dict_value_ = CreateMaybeMessage< ::tensorflow::DictValue >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.dict_value)
  return kind_.dict_value_;
}

// .tensorflow.NamedTupleValue named_tuple_value = 54;
inline bool StructuredValue::has_named_tuple_value() const {
  return kind_case() == kNamedTupleValue;
}
inline void StructuredValue::set_has_named_tuple_value() {
  _oneof_case_[0] = kNamedTupleValue;
}
inline void StructuredValue::clear_named_tuple_value() {
  if (has_named_tuple_value()) {
    delete kind_.named_tuple_value_;
    clear_has_kind();
  }
}
inline const ::tensorflow::NamedTupleValue& StructuredValue::_internal_named_tuple_value() const {
  return *kind_.named_tuple_value_;
}
inline ::tensorflow::NamedTupleValue* StructuredValue::release_named_tuple_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.named_tuple_value)
  if (has_named_tuple_value()) {
    clear_has_kind();
      ::tensorflow::NamedTupleValue* temp = kind_.named_tuple_value_;
    kind_.named_tuple_value_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::tensorflow::NamedTupleValue& StructuredValue::named_tuple_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.named_tuple_value)
  return has_named_tuple_value()
      ? *kind_.named_tuple_value_
      : *reinterpret_cast< ::tensorflow::NamedTupleValue*>(&::tensorflow::_NamedTupleValue_default_instance_);
}
inline ::tensorflow::NamedTupleValue* StructuredValue::mutable_named_tuple_value() {
  if (!has_named_tuple_value()) {
    clear_kind();
    set_has_named_tuple_value();
    kind_.named_tuple_value_ = CreateMaybeMessage< ::tensorflow::NamedTupleValue >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.named_tuple_value)
  return kind_.named_tuple_value_;
}

inline bool StructuredValue::has_kind() const {
  return kind_case() != KIND_NOT_SET;
}
inline void StructuredValue::clear_has_kind() {
  _oneof_case_[0] = KIND_NOT_SET;
}
inline StructuredValue::KindCase StructuredValue::kind_case() const {
  return StructuredValue::KindCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// NoneValue

// -------------------------------------------------------------------

// ListValue

// repeated .tensorflow.StructuredValue values = 1;
inline int ListValue::values_size() const {
  return values_.size();
}
inline void ListValue::clear_values() {
  values_.Clear();
}
inline ::tensorflow::StructuredValue* ListValue::mutable_values(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ListValue.values)
  return values_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::StructuredValue >*
ListValue::mutable_values() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ListValue.values)
  return &values_;
}
inline const ::tensorflow::StructuredValue& ListValue::values(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ListValue.values)
  return values_.Get(index);
}
inline ::tensorflow::StructuredValue* ListValue::add_values() {
  // @@protoc_insertion_point(field_add:tensorflow.ListValue.values)
  return values_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::StructuredValue >&
ListValue::values() const {
  // @@protoc_insertion_point(field_list:tensorflow.ListValue.values)
  return values_;
}

// -------------------------------------------------------------------

// TupleValue

// repeated .tensorflow.StructuredValue values = 1;
inline int TupleValue::values_size() const {
  return values_.size();
}
inline void TupleValue::clear_values() {
  values_.Clear();
}
inline ::tensorflow::StructuredValue* TupleValue::mutable_values(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.TupleValue.values)
  return values_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::StructuredValue >*
TupleValue::mutable_values() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TupleValue.values)
  return &values_;
}
inline const ::tensorflow::StructuredValue& TupleValue::values(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TupleValue.values)
  return values_.Get(index);
}
inline ::tensorflow::StructuredValue* TupleValue::add_values() {
  // @@protoc_insertion_point(field_add:tensorflow.TupleValue.values)
  return values_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::StructuredValue >&
TupleValue::values() const {
  // @@protoc_insertion_point(field_list:tensorflow.TupleValue.values)
  return values_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// DictValue

// map<string, .tensorflow.StructuredValue> fields = 1;
inline int DictValue::fields_size() const {
  return fields_.size();
}
inline void DictValue::clear_fields() {
  fields_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::tensorflow::StructuredValue >&
DictValue::fields() const {
  // @@protoc_insertion_point(field_map:tensorflow.DictValue.fields)
  return fields_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::tensorflow::StructuredValue >*
DictValue::mutable_fields() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.DictValue.fields)
  return fields_.MutableMap();
}

// -------------------------------------------------------------------

// PairValue

// string key = 1;
inline void PairValue::clear_key() {
  key_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& PairValue::key() const {
  // @@protoc_insertion_point(field_get:tensorflow.PairValue.key)
  return key_.GetNoArena();
}
inline void PairValue::set_key(const ::std::string& value) {
  
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.PairValue.key)
}
#if LANG_CXX11
inline void PairValue::set_key(::std::string&& value) {
  
  key_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.PairValue.key)
}
#endif
inline void PairValue::set_key(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.PairValue.key)
}
inline void PairValue::set_key(const char* value, size_t size) {
  
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.PairValue.key)
}
inline ::std::string* PairValue::mutable_key() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.PairValue.key)
  return key_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* PairValue::release_key() {
  // @@protoc_insertion_point(field_release:tensorflow.PairValue.key)
  
  return key_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void PairValue::set_allocated_key(::std::string* key) {
  if (key != NULL) {
    
  } else {
    
  }
  key_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), key);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PairValue.key)
}

// .tensorflow.StructuredValue value = 2;
inline bool PairValue::has_value() const {
  return this != internal_default_instance() && value_ != NULL;
}
inline void PairValue::clear_value() {
  if (GetArenaNoVirtual() == NULL && value_ != NULL) {
    delete value_;
  }
  value_ = NULL;
}
inline const ::tensorflow::StructuredValue& PairValue::_internal_value() const {
  return *value_;
}
inline const ::tensorflow::StructuredValue& PairValue::value() const {
  const ::tensorflow::StructuredValue* p = value_;
  // @@protoc_insertion_point(field_get:tensorflow.PairValue.value)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::StructuredValue*>(
      &::tensorflow::_StructuredValue_default_instance_);
}
inline ::tensorflow::StructuredValue* PairValue::release_value() {
  // @@protoc_insertion_point(field_release:tensorflow.PairValue.value)
  
  ::tensorflow::StructuredValue* temp = value_;
  value_ = NULL;
  return temp;
}
inline ::tensorflow::StructuredValue* PairValue::mutable_value() {
  
  if (value_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::StructuredValue>(GetArenaNoVirtual());
    value_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.PairValue.value)
  return value_;
}
inline void PairValue::set_allocated_value(::tensorflow::StructuredValue* value) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete value_;
  }
  if (value) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      value = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, value, submessage_arena);
    }
    
  } else {
    
  }
  value_ = value;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PairValue.value)
}

// -------------------------------------------------------------------

// NamedTupleValue

// string name = 1;
inline void NamedTupleValue::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& NamedTupleValue::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.NamedTupleValue.name)
  return name_.GetNoArena();
}
inline void NamedTupleValue::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.NamedTupleValue.name)
}
#if LANG_CXX11
inline void NamedTupleValue::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.NamedTupleValue.name)
}
#endif
inline void NamedTupleValue::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.NamedTupleValue.name)
}
inline void NamedTupleValue::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.NamedTupleValue.name)
}
inline ::std::string* NamedTupleValue::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.NamedTupleValue.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* NamedTupleValue::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.NamedTupleValue.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void NamedTupleValue::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NamedTupleValue.name)
}

// repeated .tensorflow.PairValue values = 2;
inline int NamedTupleValue::values_size() const {
  return values_.size();
}
inline void NamedTupleValue::clear_values() {
  values_.Clear();
}
inline ::tensorflow::PairValue* NamedTupleValue::mutable_values(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.NamedTupleValue.values)
  return values_.Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField< ::tensorflow::PairValue >*
NamedTupleValue::mutable_values() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.NamedTupleValue.values)
  return &values_;
}
inline const ::tensorflow::PairValue& NamedTupleValue::values(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.NamedTupleValue.values)
  return values_.Get(index);
}
inline ::tensorflow::PairValue* NamedTupleValue::add_values() {
  // @@protoc_insertion_point(field_add:tensorflow.NamedTupleValue.values)
  return values_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::tensorflow::PairValue >&
NamedTupleValue::values() const {
  // @@protoc_insertion_point(field_list:tensorflow.NamedTupleValue.values)
  return values_;
}

// -------------------------------------------------------------------

// TensorSpecProto

// string name = 1;
inline void TensorSpecProto::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& TensorSpecProto::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorSpecProto.name)
  return name_.GetNoArena();
}
inline void TensorSpecProto::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.TensorSpecProto.name)
}
#if LANG_CXX11
inline void TensorSpecProto::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TensorSpecProto.name)
}
#endif
inline void TensorSpecProto::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.TensorSpecProto.name)
}
inline void TensorSpecProto::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TensorSpecProto.name)
}
inline ::std::string* TensorSpecProto::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorSpecProto.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* TensorSpecProto::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.TensorSpecProto.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void TensorSpecProto::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TensorSpecProto.name)
}

// .tensorflow.TensorShapeProto shape = 2;
inline bool TensorSpecProto::has_shape() const {
  return this != internal_default_instance() && shape_ != NULL;
}
inline const ::tensorflow::TensorShapeProto& TensorSpecProto::_internal_shape() const {
  return *shape_;
}
inline const ::tensorflow::TensorShapeProto& TensorSpecProto::shape() const {
  const ::tensorflow::TensorShapeProto* p = shape_;
  // @@protoc_insertion_point(field_get:tensorflow.TensorSpecProto.shape)
  return p != NULL ? *p : *reinterpret_cast<const ::tensorflow::TensorShapeProto*>(
      &::tensorflow::_TensorShapeProto_default_instance_);
}
inline ::tensorflow::TensorShapeProto* TensorSpecProto::release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.TensorSpecProto.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  shape_ = NULL;
  return temp;
}
inline ::tensorflow::TensorShapeProto* TensorSpecProto::mutable_shape() {
  
  if (shape_ == NULL) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaNoVirtual());
    shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorSpecProto.shape)
  return shape_;
}
inline void TensorSpecProto::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(shape_);
  }
  if (shape) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast<::google::protobuf::MessageLite*>(shape)->GetArena();
    if (message_arena != submessage_arena) {
      shape = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TensorSpecProto.shape)
}

// .tensorflow.DataType dtype = 3;
inline void TensorSpecProto::clear_dtype() {
  dtype_ = 0;
}
inline ::tensorflow::DataType TensorSpecProto::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorSpecProto.dtype)
  return static_cast< ::tensorflow::DataType >(dtype_);
}
inline void TensorSpecProto::set_dtype(::tensorflow::DataType value) {
  
  dtype_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.TensorSpecProto.dtype)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto

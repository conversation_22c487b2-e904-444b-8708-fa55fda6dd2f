// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/verifier_config.proto

#include "tensorflow/core/protobuf/verifier_config.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace tensorflow {
class VerifierConfigDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<VerifierConfig>
      _instance;
} _VerifierConfig_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fprotobuf_2fverifier_5fconfig_2eproto {
static void InitDefaultsVerifierConfig() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_VerifierConfig_default_instance_;
    new (ptr) ::tensorflow::VerifierConfig();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::VerifierConfig::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_VerifierConfig =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsVerifierConfig}, {}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_VerifierConfig.base);
}

::google::protobuf::Metadata file_level_metadata[1];
const ::google::protobuf::EnumDescriptor* file_level_enum_descriptors[1];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::VerifierConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::VerifierConfig, verification_timeout_in_ms_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::VerifierConfig, structure_verifier_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::VerifierConfig)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_VerifierConfig_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/protobuf/verifier_config.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, file_level_enum_descriptors, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 1);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n.tensorflow/core/protobuf/verifier_conf"
      "ig.proto\022\ntensorflow\"\233\001\n\016VerifierConfig\022"
      "\"\n\032verification_timeout_in_ms\030\001 \001(\003\022=\n\022s"
      "tructure_verifier\030\002 \001(\0162!.tensorflow.Ver"
      "ifierConfig.Toggle\"&\n\006Toggle\022\013\n\007DEFAULT\020"
      "\000\022\006\n\002ON\020\001\022\007\n\003OFF\020\002B5\n\030org.tensorflow.fra"
      "meworkB\024VerifierConfigProtosP\001\370\001\001b\006proto"
      "3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 281);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/protobuf/verifier_config.proto", &protobuf_RegisterTypes);
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fverifier_5fconfig_2eproto
namespace tensorflow {
const ::google::protobuf::EnumDescriptor* VerifierConfig_Toggle_descriptor() {
  protobuf_tensorflow_2fcore_2fprotobuf_2fverifier_5fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_tensorflow_2fcore_2fprotobuf_2fverifier_5fconfig_2eproto::file_level_enum_descriptors[0];
}
bool VerifierConfig_Toggle_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const VerifierConfig_Toggle VerifierConfig::DEFAULT;
const VerifierConfig_Toggle VerifierConfig::ON;
const VerifierConfig_Toggle VerifierConfig::OFF;
const VerifierConfig_Toggle VerifierConfig::Toggle_MIN;
const VerifierConfig_Toggle VerifierConfig::Toggle_MAX;
const int VerifierConfig::Toggle_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

// ===================================================================

void VerifierConfig::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int VerifierConfig::kVerificationTimeoutInMsFieldNumber;
const int VerifierConfig::kStructureVerifierFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

VerifierConfig::VerifierConfig()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fverifier_5fconfig_2eproto::scc_info_VerifierConfig.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.VerifierConfig)
}
VerifierConfig::VerifierConfig(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fverifier_5fconfig_2eproto::scc_info_VerifierConfig.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.VerifierConfig)
}
VerifierConfig::VerifierConfig(const VerifierConfig& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&verification_timeout_in_ms_, &from.verification_timeout_in_ms_,
    static_cast<size_t>(reinterpret_cast<char*>(&structure_verifier_) -
    reinterpret_cast<char*>(&verification_timeout_in_ms_)) + sizeof(structure_verifier_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.VerifierConfig)
}

void VerifierConfig::SharedCtor() {
  ::memset(&verification_timeout_in_ms_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&structure_verifier_) -
      reinterpret_cast<char*>(&verification_timeout_in_ms_)) + sizeof(structure_verifier_));
}

VerifierConfig::~VerifierConfig() {
  // @@protoc_insertion_point(destructor:tensorflow.VerifierConfig)
  SharedDtor();
}

void VerifierConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void VerifierConfig::ArenaDtor(void* object) {
  VerifierConfig* _this = reinterpret_cast< VerifierConfig* >(object);
  (void)_this;
}
void VerifierConfig::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void VerifierConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* VerifierConfig::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fverifier_5fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fverifier_5fconfig_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const VerifierConfig& VerifierConfig::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fverifier_5fconfig_2eproto::scc_info_VerifierConfig.base);
  return *internal_default_instance();
}


void VerifierConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.VerifierConfig)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&verification_timeout_in_ms_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&structure_verifier_) -
      reinterpret_cast<char*>(&verification_timeout_in_ms_)) + sizeof(structure_verifier_));
  _internal_metadata_.Clear();
}

bool VerifierConfig::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.VerifierConfig)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 verification_timeout_in_ms = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &verification_timeout_in_ms_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.VerifierConfig.Toggle structure_verifier = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_structure_verifier(static_cast< ::tensorflow::VerifierConfig_Toggle >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.VerifierConfig)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.VerifierConfig)
  return false;
#undef DO_
}

void VerifierConfig::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.VerifierConfig)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 verification_timeout_in_ms = 1;
  if (this->verification_timeout_in_ms() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->verification_timeout_in_ms(), output);
  }

  // .tensorflow.VerifierConfig.Toggle structure_verifier = 2;
  if (this->structure_verifier() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      2, this->structure_verifier(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.VerifierConfig)
}

::google::protobuf::uint8* VerifierConfig::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.VerifierConfig)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 verification_timeout_in_ms = 1;
  if (this->verification_timeout_in_ms() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->verification_timeout_in_ms(), target);
  }

  // .tensorflow.VerifierConfig.Toggle structure_verifier = 2;
  if (this->structure_verifier() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      2, this->structure_verifier(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.VerifierConfig)
  return target;
}

size_t VerifierConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.VerifierConfig)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int64 verification_timeout_in_ms = 1;
  if (this->verification_timeout_in_ms() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->verification_timeout_in_ms());
  }

  // .tensorflow.VerifierConfig.Toggle structure_verifier = 2;
  if (this->structure_verifier() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->structure_verifier());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void VerifierConfig::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.VerifierConfig)
  GOOGLE_DCHECK_NE(&from, this);
  const VerifierConfig* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const VerifierConfig>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.VerifierConfig)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.VerifierConfig)
    MergeFrom(*source);
  }
}

void VerifierConfig::MergeFrom(const VerifierConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.VerifierConfig)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.verification_timeout_in_ms() != 0) {
    set_verification_timeout_in_ms(from.verification_timeout_in_ms());
  }
  if (from.structure_verifier() != 0) {
    set_structure_verifier(from.structure_verifier());
  }
}

void VerifierConfig::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.VerifierConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void VerifierConfig::CopyFrom(const VerifierConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.VerifierConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VerifierConfig::IsInitialized() const {
  return true;
}

void VerifierConfig::Swap(VerifierConfig* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    VerifierConfig* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void VerifierConfig::UnsafeArenaSwap(VerifierConfig* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void VerifierConfig::InternalSwap(VerifierConfig* other) {
  using std::swap;
  swap(verification_timeout_in_ms_, other->verification_timeout_in_ms_);
  swap(structure_verifier_, other->structure_verifier_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata VerifierConfig::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fverifier_5fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fverifier_5fconfig_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::VerifierConfig* Arena::CreateMaybeMessage< ::tensorflow::VerifierConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::VerifierConfig >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

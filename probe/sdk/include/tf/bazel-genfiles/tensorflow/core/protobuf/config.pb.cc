// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/config.proto

#include "tensorflow/core/protobuf/config.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_CostGraphDef;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2fgraph_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fgraph_2eproto ::google::protobuf::internal::SCCInfo<3> scc_info_GraphDef;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fgraph_2eproto
namespace protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_StepStats;
}  // namespace protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto
namespace protobuf_tensorflow_2fcore_2fprotobuf_2fcluster_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fcluster_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_ClusterDef;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fcluster_2eproto
namespace protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_CallableOptions_FeedDevicesEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_CallableOptions_FetchDevicesEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_ConfigProto_DeviceCountEntry_DoNotUse;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_ConfigProto_Experimental;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_GPUOptions_Experimental_VirtualDevices;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_OptimizerOptions;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_RPCOptions;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_RunOptions_Experimental;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_TensorConnection;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto ::google::protobuf::internal::SCCInfo<0> scc_info_ThreadPoolOptionProto;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_GPUOptions;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_GPUOptions_Experimental;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_RunMetadata_FunctionGraphs;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_GraphOptions;
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto ::google::protobuf::internal::SCCInfo<2> scc_info_RunOptions;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto
namespace protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto ::google::protobuf::internal::SCCInfo<1> scc_info_DebugOptions;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto
namespace protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto {
extern PROTOBUF_INTERNAL_EXPORT_protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto ::google::protobuf::internal::SCCInfo<4> scc_info_RewriterConfig;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto
namespace tensorflow {
class GPUOptions_Experimental_VirtualDevicesDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<GPUOptions_Experimental_VirtualDevices>
      _instance;
} _GPUOptions_Experimental_VirtualDevices_default_instance_;
class GPUOptions_ExperimentalDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<GPUOptions_Experimental>
      _instance;
} _GPUOptions_Experimental_default_instance_;
class GPUOptionsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<GPUOptions>
      _instance;
} _GPUOptions_default_instance_;
class OptimizerOptionsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<OptimizerOptions>
      _instance;
} _OptimizerOptions_default_instance_;
class GraphOptionsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<GraphOptions>
      _instance;
} _GraphOptions_default_instance_;
class ThreadPoolOptionProtoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ThreadPoolOptionProto>
      _instance;
} _ThreadPoolOptionProto_default_instance_;
class RPCOptionsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<RPCOptions>
      _instance;
} _RPCOptions_default_instance_;
class ConfigProto_DeviceCountEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ConfigProto_DeviceCountEntry_DoNotUse>
      _instance;
} _ConfigProto_DeviceCountEntry_DoNotUse_default_instance_;
class ConfigProto_ExperimentalDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ConfigProto_Experimental>
      _instance;
} _ConfigProto_Experimental_default_instance_;
class ConfigProtoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ConfigProto>
      _instance;
} _ConfigProto_default_instance_;
class RunOptions_ExperimentalDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<RunOptions_Experimental>
      _instance;
} _RunOptions_Experimental_default_instance_;
class RunOptionsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<RunOptions>
      _instance;
} _RunOptions_default_instance_;
class RunMetadata_FunctionGraphsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<RunMetadata_FunctionGraphs>
      _instance;
} _RunMetadata_FunctionGraphs_default_instance_;
class RunMetadataDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<RunMetadata>
      _instance;
} _RunMetadata_default_instance_;
class TensorConnectionDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<TensorConnection>
      _instance;
} _TensorConnection_default_instance_;
class CallableOptions_FeedDevicesEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CallableOptions_FeedDevicesEntry_DoNotUse>
      _instance;
} _CallableOptions_FeedDevicesEntry_DoNotUse_default_instance_;
class CallableOptions_FetchDevicesEntry_DoNotUseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CallableOptions_FetchDevicesEntry_DoNotUse>
      _instance;
} _CallableOptions_FetchDevicesEntry_DoNotUse_default_instance_;
class CallableOptionsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CallableOptions>
      _instance;
} _CallableOptions_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto {
static void InitDefaultsGPUOptions_Experimental_VirtualDevices() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_GPUOptions_Experimental_VirtualDevices_default_instance_;
    new (ptr) ::tensorflow::GPUOptions_Experimental_VirtualDevices();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::GPUOptions_Experimental_VirtualDevices::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_GPUOptions_Experimental_VirtualDevices =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsGPUOptions_Experimental_VirtualDevices}, {}};

static void InitDefaultsGPUOptions_Experimental() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_GPUOptions_Experimental_default_instance_;
    new (ptr) ::tensorflow::GPUOptions_Experimental();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::GPUOptions_Experimental::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_GPUOptions_Experimental =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsGPUOptions_Experimental}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_GPUOptions_Experimental_VirtualDevices.base,}};

static void InitDefaultsGPUOptions() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_GPUOptions_default_instance_;
    new (ptr) ::tensorflow::GPUOptions();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::GPUOptions::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_GPUOptions =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsGPUOptions}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_GPUOptions_Experimental.base,}};

static void InitDefaultsOptimizerOptions() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_OptimizerOptions_default_instance_;
    new (ptr) ::tensorflow::OptimizerOptions();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::OptimizerOptions::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_OptimizerOptions =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsOptimizerOptions}, {}};

static void InitDefaultsGraphOptions() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_GraphOptions_default_instance_;
    new (ptr) ::tensorflow::GraphOptions();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::GraphOptions::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_GraphOptions =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsGraphOptions}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_OptimizerOptions.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::scc_info_RewriterConfig.base,}};

static void InitDefaultsThreadPoolOptionProto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_ThreadPoolOptionProto_default_instance_;
    new (ptr) ::tensorflow::ThreadPoolOptionProto();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::ThreadPoolOptionProto::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_ThreadPoolOptionProto =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsThreadPoolOptionProto}, {}};

static void InitDefaultsRPCOptions() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_RPCOptions_default_instance_;
    new (ptr) ::tensorflow::RPCOptions();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::RPCOptions::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_RPCOptions =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsRPCOptions}, {}};

static void InitDefaultsConfigProto_DeviceCountEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_ConfigProto_DeviceCountEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::ConfigProto_DeviceCountEntry_DoNotUse();
  }
  ::tensorflow::ConfigProto_DeviceCountEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_ConfigProto_DeviceCountEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsConfigProto_DeviceCountEntry_DoNotUse}, {}};

static void InitDefaultsConfigProto_Experimental() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_ConfigProto_Experimental_default_instance_;
    new (ptr) ::tensorflow::ConfigProto_Experimental();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::ConfigProto_Experimental::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_ConfigProto_Experimental =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsConfigProto_Experimental}, {}};

static void InitDefaultsConfigProto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_ConfigProto_default_instance_;
    new (ptr) ::tensorflow::ConfigProto();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::ConfigProto::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<7> scc_info_ConfigProto =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 7, InitDefaultsConfigProto}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_ConfigProto_DeviceCountEntry_DoNotUse.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_ThreadPoolOptionProto.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_GPUOptions.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_GraphOptions.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_RPCOptions.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fcluster_2eproto::scc_info_ClusterDef.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_ConfigProto_Experimental.base,}};

static void InitDefaultsRunOptions_Experimental() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_RunOptions_Experimental_default_instance_;
    new (ptr) ::tensorflow::RunOptions_Experimental();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::RunOptions_Experimental::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_RunOptions_Experimental =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsRunOptions_Experimental}, {}};

static void InitDefaultsRunOptions() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_RunOptions_default_instance_;
    new (ptr) ::tensorflow::RunOptions();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::RunOptions::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<2> scc_info_RunOptions =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 2, InitDefaultsRunOptions}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::scc_info_DebugOptions.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_RunOptions_Experimental.base,}};

static void InitDefaultsRunMetadata_FunctionGraphs() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_RunMetadata_FunctionGraphs_default_instance_;
    new (ptr) ::tensorflow::RunMetadata_FunctionGraphs();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::RunMetadata_FunctionGraphs::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<1> scc_info_RunMetadata_FunctionGraphs =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 1, InitDefaultsRunMetadata_FunctionGraphs}, {
      &protobuf_tensorflow_2fcore_2fframework_2fgraph_2eproto::scc_info_GraphDef.base,}};

static void InitDefaultsRunMetadata() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_RunMetadata_default_instance_;
    new (ptr) ::tensorflow::RunMetadata();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::RunMetadata::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<4> scc_info_RunMetadata =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 4, InitDefaultsRunMetadata}, {
      &protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::scc_info_StepStats.base,
      &protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::scc_info_CostGraphDef.base,
      &protobuf_tensorflow_2fcore_2fframework_2fgraph_2eproto::scc_info_GraphDef.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_RunMetadata_FunctionGraphs.base,}};

static void InitDefaultsTensorConnection() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_TensorConnection_default_instance_;
    new (ptr) ::tensorflow::TensorConnection();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::TensorConnection::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_TensorConnection =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsTensorConnection}, {}};

static void InitDefaultsCallableOptions_FeedDevicesEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_CallableOptions_FeedDevicesEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::CallableOptions_FeedDevicesEntry_DoNotUse();
  }
  ::tensorflow::CallableOptions_FeedDevicesEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_CallableOptions_FeedDevicesEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsCallableOptions_FeedDevicesEntry_DoNotUse}, {}};

static void InitDefaultsCallableOptions_FetchDevicesEntry_DoNotUse() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_CallableOptions_FetchDevicesEntry_DoNotUse_default_instance_;
    new (ptr) ::tensorflow::CallableOptions_FetchDevicesEntry_DoNotUse();
  }
  ::tensorflow::CallableOptions_FetchDevicesEntry_DoNotUse::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_CallableOptions_FetchDevicesEntry_DoNotUse =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsCallableOptions_FetchDevicesEntry_DoNotUse}, {}};

static void InitDefaultsCallableOptions() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_CallableOptions_default_instance_;
    new (ptr) ::tensorflow::CallableOptions();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::CallableOptions::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<4> scc_info_CallableOptions =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 4, InitDefaultsCallableOptions}, {
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_RunOptions.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_TensorConnection.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_CallableOptions_FeedDevicesEntry_DoNotUse.base,
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_CallableOptions_FetchDevicesEntry_DoNotUse.base,}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_GPUOptions_Experimental_VirtualDevices.base);
  ::google::protobuf::internal::InitSCC(&scc_info_GPUOptions_Experimental.base);
  ::google::protobuf::internal::InitSCC(&scc_info_GPUOptions.base);
  ::google::protobuf::internal::InitSCC(&scc_info_OptimizerOptions.base);
  ::google::protobuf::internal::InitSCC(&scc_info_GraphOptions.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ThreadPoolOptionProto.base);
  ::google::protobuf::internal::InitSCC(&scc_info_RPCOptions.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ConfigProto_DeviceCountEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ConfigProto_Experimental.base);
  ::google::protobuf::internal::InitSCC(&scc_info_ConfigProto.base);
  ::google::protobuf::internal::InitSCC(&scc_info_RunOptions_Experimental.base);
  ::google::protobuf::internal::InitSCC(&scc_info_RunOptions.base);
  ::google::protobuf::internal::InitSCC(&scc_info_RunMetadata_FunctionGraphs.base);
  ::google::protobuf::internal::InitSCC(&scc_info_RunMetadata.base);
  ::google::protobuf::internal::InitSCC(&scc_info_TensorConnection.base);
  ::google::protobuf::internal::InitSCC(&scc_info_CallableOptions_FeedDevicesEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_CallableOptions_FetchDevicesEntry_DoNotUse.base);
  ::google::protobuf::internal::InitSCC(&scc_info_CallableOptions.base);
}

::google::protobuf::Metadata file_level_metadata[18];
const ::google::protobuf::EnumDescriptor* file_level_enum_descriptors[3];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GPUOptions_Experimental_VirtualDevices, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GPUOptions_Experimental_VirtualDevices, memory_limit_mb_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GPUOptions_Experimental, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GPUOptions_Experimental, virtual_devices_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GPUOptions_Experimental, use_unified_memory_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GPUOptions_Experimental, num_dev_to_dev_copy_streams_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GPUOptions_Experimental, collective_ring_order_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GPUOptions_Experimental, timestamped_allocator_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GPUOptions_Experimental, pending_cap_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GPUOptions, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GPUOptions, per_process_gpu_memory_fraction_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GPUOptions, allow_growth_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GPUOptions, allocator_type_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GPUOptions, deferred_deletion_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GPUOptions, visible_device_list_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GPUOptions, polling_active_delay_usecs_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GPUOptions, polling_inactive_delay_msecs_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GPUOptions, force_gpu_compatible_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GPUOptions, experimental_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OptimizerOptions, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OptimizerOptions, do_common_subexpression_elimination_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OptimizerOptions, do_constant_folding_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OptimizerOptions, max_folded_constant_in_bytes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OptimizerOptions, do_function_inlining_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OptimizerOptions, opt_level_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::OptimizerOptions, global_jit_level_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphOptions, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphOptions, enable_recv_scheduling_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphOptions, optimizer_options_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphOptions, build_cost_model_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphOptions, build_cost_model_after_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphOptions, infer_shapes_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphOptions, place_pruned_graph_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphOptions, enable_bfloat16_sendrecv_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphOptions, timeline_step_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::GraphOptions, rewrite_options_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ThreadPoolOptionProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ThreadPoolOptionProto, num_threads_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ThreadPoolOptionProto, global_name_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RPCOptions, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RPCOptions, use_rpc_for_inprocess_master_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RPCOptions, compression_algorithm_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RPCOptions, compression_level_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConfigProto_DeviceCountEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConfigProto_DeviceCountEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConfigProto_DeviceCountEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConfigProto_DeviceCountEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConfigProto_Experimental, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConfigProto_Experimental, collective_group_leader_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConfigProto_Experimental, executor_type_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConfigProto_Experimental, recv_buf_max_chunk_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConfigProto_Experimental, use_numa_affinity_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConfigProto_Experimental, collective_deterministic_sequential_execution_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConfigProto_Experimental, collective_nccl_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConfigProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConfigProto, device_count_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConfigProto, intra_op_parallelism_threads_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConfigProto, inter_op_parallelism_threads_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConfigProto, use_per_session_threads_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConfigProto, session_inter_op_thread_pool_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConfigProto, placement_period_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConfigProto, device_filters_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConfigProto, gpu_options_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConfigProto, allow_soft_placement_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConfigProto, log_device_placement_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConfigProto, graph_options_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConfigProto, operation_timeout_in_ms_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConfigProto, rpc_options_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConfigProto, cluster_def_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConfigProto, isolate_session_state_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::ConfigProto, experimental_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunOptions_Experimental, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunOptions_Experimental, collective_graph_key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunOptions_Experimental, use_run_handler_pool_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunOptions, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunOptions, trace_level_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunOptions, timeout_in_ms_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunOptions, inter_op_thread_pool_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunOptions, output_partition_graphs_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunOptions, debug_options_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunOptions, report_tensor_allocations_upon_oom_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunOptions, experimental_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunMetadata_FunctionGraphs, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunMetadata_FunctionGraphs, partition_graphs_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunMetadata_FunctionGraphs, pre_optimization_graph_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunMetadata_FunctionGraphs, post_optimization_graph_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunMetadata, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunMetadata, step_stats_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunMetadata, cost_graph_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunMetadata, partition_graphs_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RunMetadata, function_graphs_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorConnection, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorConnection, from_tensor_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::TensorConnection, to_tensor_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CallableOptions_FeedDevicesEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CallableOptions_FeedDevicesEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CallableOptions_FeedDevicesEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CallableOptions_FeedDevicesEntry_DoNotUse, value_),
  0,
  1,
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CallableOptions_FetchDevicesEntry_DoNotUse, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CallableOptions_FetchDevicesEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CallableOptions_FetchDevicesEntry_DoNotUse, key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CallableOptions_FetchDevicesEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CallableOptions, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CallableOptions, feed_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CallableOptions, fetch_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CallableOptions, target_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CallableOptions, run_options_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CallableOptions, tensor_connection_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CallableOptions, feed_devices_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CallableOptions, fetch_devices_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::CallableOptions, fetch_skip_sync_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::GPUOptions_Experimental_VirtualDevices)},
  { 6, -1, sizeof(::tensorflow::GPUOptions_Experimental)},
  { 17, -1, sizeof(::tensorflow::GPUOptions)},
  { 31, -1, sizeof(::tensorflow::OptimizerOptions)},
  { 42, -1, sizeof(::tensorflow::GraphOptions)},
  { 56, -1, sizeof(::tensorflow::ThreadPoolOptionProto)},
  { 63, -1, sizeof(::tensorflow::RPCOptions)},
  { 71, 78, sizeof(::tensorflow::ConfigProto_DeviceCountEntry_DoNotUse)},
  { 80, -1, sizeof(::tensorflow::ConfigProto_Experimental)},
  { 91, -1, sizeof(::tensorflow::ConfigProto)},
  { 112, -1, sizeof(::tensorflow::RunOptions_Experimental)},
  { 119, -1, sizeof(::tensorflow::RunOptions)},
  { 131, -1, sizeof(::tensorflow::RunMetadata_FunctionGraphs)},
  { 139, -1, sizeof(::tensorflow::RunMetadata)},
  { 148, -1, sizeof(::tensorflow::TensorConnection)},
  { 155, 162, sizeof(::tensorflow::CallableOptions_FeedDevicesEntry_DoNotUse)},
  { 164, 171, sizeof(::tensorflow::CallableOptions_FetchDevicesEntry_DoNotUse)},
  { 173, -1, sizeof(::tensorflow::CallableOptions)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_GPUOptions_Experimental_VirtualDevices_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_GPUOptions_Experimental_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_GPUOptions_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_OptimizerOptions_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_GraphOptions_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_ThreadPoolOptionProto_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_RPCOptions_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_ConfigProto_DeviceCountEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_ConfigProto_Experimental_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_ConfigProto_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_RunOptions_Experimental_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_RunOptions_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_RunMetadata_FunctionGraphs_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_RunMetadata_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_TensorConnection_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_CallableOptions_FeedDevicesEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_CallableOptions_FetchDevicesEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_CallableOptions_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/protobuf/config.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, file_level_enum_descriptors, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 18);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n%tensorflow/core/protobuf/config.proto\022"
      "\ntensorflow\032*tensorflow/core/framework/c"
      "ost_graph.proto\032%tensorflow/core/framewo"
      "rk/graph.proto\032*tensorflow/core/framewor"
      "k/step_stats.proto\032$tensorflow/core/prot"
      "obuf/debug.proto\032&tensorflow/core/protob"
      "uf/cluster.proto\032.tensorflow/core/protob"
      "uf/rewriter_config.proto\"\341\004\n\nGPUOptions\022"
      "\'\n\037per_process_gpu_memory_fraction\030\001 \001(\001"
      "\022\024\n\014allow_growth\030\004 \001(\010\022\026\n\016allocator_type"
      "\030\002 \001(\t\022\037\n\027deferred_deletion_bytes\030\003 \001(\003\022"
      "\033\n\023visible_device_list\030\005 \001(\t\022\"\n\032polling_"
      "active_delay_usecs\030\006 \001(\005\022$\n\034polling_inac"
      "tive_delay_msecs\030\007 \001(\005\022\034\n\024force_gpu_comp"
      "atible\030\010 \001(\010\0229\n\014experimental\030\t \001(\0132#.ten"
      "sorflow.GPUOptions.Experimental\032\232\002\n\014Expe"
      "rimental\022K\n\017virtual_devices\030\001 \003(\01322.tens"
      "orflow.GPUOptions.Experimental.VirtualDe"
      "vices\022\032\n\022use_unified_memory\030\002 \001(\010\022#\n\033num"
      "_dev_to_dev_copy_streams\030\003 \001(\005\022\035\n\025collec"
      "tive_ring_order\030\004 \001(\t\022\035\n\025timestamped_all"
      "ocator\030\005 \001(\010\022\023\n\013pending_cap\030\006 \001(\005\032)\n\016Vir"
      "tualDevices\022\027\n\017memory_limit_mb\030\001 \003(\002\"\205\003\n"
      "\020OptimizerOptions\022+\n#do_common_subexpres"
      "sion_elimination\030\001 \001(\010\022\033\n\023do_constant_fo"
      "lding\030\002 \001(\010\022$\n\034max_folded_constant_in_by"
      "tes\030\006 \001(\003\022\034\n\024do_function_inlining\030\004 \001(\010\022"
      "5\n\topt_level\030\003 \001(\0162\".tensorflow.Optimize"
      "rOptions.Level\022E\n\020global_jit_level\030\005 \001(\016"
      "2+.tensorflow.OptimizerOptions.GlobalJit"
      "Level\" \n\005Level\022\006\n\002L1\020\000\022\017\n\002L0\020\377\377\377\377\377\377\377\377\377\001\""
      "C\n\016GlobalJitLevel\022\013\n\007DEFAULT\020\000\022\020\n\003OFF\020\377\377"
      "\377\377\377\377\377\377\377\001\022\010\n\004ON_1\020\001\022\010\n\004ON_2\020\002\"\356\002\n\014GraphOp"
      "tions\022\036\n\026enable_recv_scheduling\030\002 \001(\010\0227\n"
      "\021optimizer_options\030\003 \001(\0132\034.tensorflow.Op"
      "timizerOptions\022\030\n\020build_cost_model\030\004 \001(\003"
      "\022\036\n\026build_cost_model_after\030\t \001(\003\022\024\n\014infe"
      "r_shapes\030\005 \001(\010\022\032\n\022place_pruned_graph\030\006 \001"
      "(\010\022 \n\030enable_bfloat16_sendrecv\030\007 \001(\010\022\025\n\r"
      "timeline_step\030\010 \001(\005\0223\n\017rewrite_options\030\n"
      " \001(\0132\032.tensorflow.RewriterConfigJ\004\010\001\020\002R%"
      "skip_common_subexpression_elimination\"A\n"
      "\025ThreadPoolOptionProto\022\023\n\013num_threads\030\001 "
      "\001(\005\022\023\n\013global_name\030\002 \001(\t\"l\n\nRPCOptions\022$"
      "\n\034use_rpc_for_inprocess_master\030\001 \001(\010\022\035\n\025"
      "compression_algorithm\030\002 \001(\t\022\031\n\021compressi"
      "on_level\030\003 \001(\005\"\257\007\n\013ConfigProto\022>\n\014device"
      "_count\030\001 \003(\0132(.tensorflow.ConfigProto.De"
      "viceCountEntry\022$\n\034intra_op_parallelism_t"
      "hreads\030\002 \001(\005\022$\n\034inter_op_parallelism_thr"
      "eads\030\005 \001(\005\022\037\n\027use_per_session_threads\030\t "
      "\001(\010\022G\n\034session_inter_op_thread_pool\030\014 \003("
      "\0132!.tensorflow.ThreadPoolOptionProto\022\030\n\020"
      "placement_period\030\003 \001(\005\022\026\n\016device_filters"
      "\030\004 \003(\t\022+\n\013gpu_options\030\006 \001(\0132\026.tensorflow"
      ".GPUOptions\022\034\n\024allow_soft_placement\030\007 \001("
      "\010\022\034\n\024log_device_placement\030\010 \001(\010\022/\n\rgraph"
      "_options\030\n \001(\0132\030.tensorflow.GraphOptions"
      "\022\037\n\027operation_timeout_in_ms\030\013 \001(\003\022+\n\013rpc"
      "_options\030\r \001(\0132\026.tensorflow.RPCOptions\022+"
      "\n\013cluster_def\030\016 \001(\0132\026.tensorflow.Cluster"
      "Def\022\035\n\025isolate_session_state\030\017 \001(\010\022:\n\014ex"
      "perimental\030\020 \001(\0132$.tensorflow.ConfigProt"
      "o.Experimental\0322\n\020DeviceCountEntry\022\013\n\003ke"
      "y\030\001 \001(\t\022\r\n\005value\030\002 \001(\005:\0028\001\032\323\001\n\014Experimen"
      "tal\022\037\n\027collective_group_leader\030\001 \001(\t\022\025\n\r"
      "executor_type\030\003 \001(\t\022\032\n\022recv_buf_max_chun"
      "k\030\004 \001(\005\022\031\n\021use_numa_affinity\030\005 \001(\010\0225\n-co"
      "llective_deterministic_sequential_execut"
      "ion\030\006 \001(\010\022\027\n\017collective_nccl\030\007 \001(\010J\004\010\002\020\003"
      "\"\330\003\n\nRunOptions\0226\n\013trace_level\030\001 \001(\0162!.t"
      "ensorflow.RunOptions.TraceLevel\022\025\n\rtimeo"
      "ut_in_ms\030\002 \001(\003\022\034\n\024inter_op_thread_pool\030\003"
      " \001(\005\022\037\n\027output_partition_graphs\030\005 \001(\010\022/\n"
      "\rdebug_options\030\006 \001(\0132\030.tensorflow.DebugO"
      "ptions\022*\n\"report_tensor_allocations_upon"
      "_oom\030\007 \001(\010\0229\n\014experimental\030\010 \001(\0132#.tenso"
      "rflow.RunOptions.Experimental\032J\n\014Experim"
      "ental\022\034\n\024collective_graph_key\030\001 \001(\003\022\034\n\024u"
      "se_run_handler_pool\030\002 \001(\010\"R\n\nTraceLevel\022"
      "\014\n\010NO_TRACE\020\000\022\022\n\016SOFTWARE_TRACE\020\001\022\022\n\016HAR"
      "DWARE_TRACE\020\002\022\016\n\nFULL_TRACE\020\003J\004\010\004\020\005\"\207\003\n\013"
      "RunMetadata\022)\n\nstep_stats\030\001 \001(\0132\025.tensor"
      "flow.StepStats\022,\n\ncost_graph\030\002 \001(\0132\030.ten"
      "sorflow.CostGraphDef\022.\n\020partition_graphs"
      "\030\003 \003(\0132\024.tensorflow.GraphDef\022\?\n\017function"
      "_graphs\030\004 \003(\0132&.tensorflow.RunMetadata.F"
      "unctionGraphs\032\255\001\n\016FunctionGraphs\022.\n\020part"
      "ition_graphs\030\001 \003(\0132\024.tensorflow.GraphDef"
      "\0224\n\026pre_optimization_graph\030\002 \001(\0132\024.tenso"
      "rflow.GraphDef\0225\n\027post_optimization_grap"
      "h\030\003 \001(\0132\024.tensorflow.GraphDef\":\n\020TensorC"
      "onnection\022\023\n\013from_tensor\030\001 \001(\t\022\021\n\tto_ten"
      "sor\030\002 \001(\t\"\260\003\n\017CallableOptions\022\014\n\004feed\030\001 "
      "\003(\t\022\r\n\005fetch\030\002 \003(\t\022\016\n\006target\030\003 \003(\t\022+\n\013ru"
      "n_options\030\004 \001(\0132\026.tensorflow.RunOptions\022"
      "7\n\021tensor_connection\030\005 \003(\0132\034.tensorflow."
      "TensorConnection\022B\n\014feed_devices\030\006 \003(\0132,"
      ".tensorflow.CallableOptions.FeedDevicesE"
      "ntry\022D\n\rfetch_devices\030\007 \003(\0132-.tensorflow"
      ".CallableOptions.FetchDevicesEntry\022\027\n\017fe"
      "tch_skip_sync\030\010 \001(\010\0322\n\020FeedDevicesEntry\022"
      "\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001\0323\n\021Fetch"
      "DevicesEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t"
      ":\0028\001B-\n\030org.tensorflow.frameworkB\014Config"
      "ProtosP\001\370\001\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 4219);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/protobuf/config.proto", &protobuf_RegisterTypes);
  ::protobuf_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2fgraph_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fcluster_2eproto::AddDescriptors();
  ::protobuf_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto
namespace tensorflow {
const ::google::protobuf::EnumDescriptor* OptimizerOptions_Level_descriptor() {
  protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_enum_descriptors[0];
}
bool OptimizerOptions_Level_IsValid(int value) {
  switch (value) {
    case -1:
    case 0:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const OptimizerOptions_Level OptimizerOptions::L1;
const OptimizerOptions_Level OptimizerOptions::L0;
const OptimizerOptions_Level OptimizerOptions::Level_MIN;
const OptimizerOptions_Level OptimizerOptions::Level_MAX;
const int OptimizerOptions::Level_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900
const ::google::protobuf::EnumDescriptor* OptimizerOptions_GlobalJitLevel_descriptor() {
  protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_enum_descriptors[1];
}
bool OptimizerOptions_GlobalJitLevel_IsValid(int value) {
  switch (value) {
    case -1:
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const OptimizerOptions_GlobalJitLevel OptimizerOptions::DEFAULT;
const OptimizerOptions_GlobalJitLevel OptimizerOptions::OFF;
const OptimizerOptions_GlobalJitLevel OptimizerOptions::ON_1;
const OptimizerOptions_GlobalJitLevel OptimizerOptions::ON_2;
const OptimizerOptions_GlobalJitLevel OptimizerOptions::GlobalJitLevel_MIN;
const OptimizerOptions_GlobalJitLevel OptimizerOptions::GlobalJitLevel_MAX;
const int OptimizerOptions::GlobalJitLevel_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900
const ::google::protobuf::EnumDescriptor* RunOptions_TraceLevel_descriptor() {
  protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_enum_descriptors[2];
}
bool RunOptions_TraceLevel_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const RunOptions_TraceLevel RunOptions::NO_TRACE;
const RunOptions_TraceLevel RunOptions::SOFTWARE_TRACE;
const RunOptions_TraceLevel RunOptions::HARDWARE_TRACE;
const RunOptions_TraceLevel RunOptions::FULL_TRACE;
const RunOptions_TraceLevel RunOptions::TraceLevel_MIN;
const RunOptions_TraceLevel RunOptions::TraceLevel_MAX;
const int RunOptions::TraceLevel_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

// ===================================================================

void GPUOptions_Experimental_VirtualDevices::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GPUOptions_Experimental_VirtualDevices::kMemoryLimitMbFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GPUOptions_Experimental_VirtualDevices::GPUOptions_Experimental_VirtualDevices()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_GPUOptions_Experimental_VirtualDevices.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.GPUOptions.Experimental.VirtualDevices)
}
GPUOptions_Experimental_VirtualDevices::GPUOptions_Experimental_VirtualDevices(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  memory_limit_mb_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_GPUOptions_Experimental_VirtualDevices.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.GPUOptions.Experimental.VirtualDevices)
}
GPUOptions_Experimental_VirtualDevices::GPUOptions_Experimental_VirtualDevices(const GPUOptions_Experimental_VirtualDevices& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      memory_limit_mb_(from.memory_limit_mb_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.GPUOptions.Experimental.VirtualDevices)
}

void GPUOptions_Experimental_VirtualDevices::SharedCtor() {
}

GPUOptions_Experimental_VirtualDevices::~GPUOptions_Experimental_VirtualDevices() {
  // @@protoc_insertion_point(destructor:tensorflow.GPUOptions.Experimental.VirtualDevices)
  SharedDtor();
}

void GPUOptions_Experimental_VirtualDevices::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void GPUOptions_Experimental_VirtualDevices::ArenaDtor(void* object) {
  GPUOptions_Experimental_VirtualDevices* _this = reinterpret_cast< GPUOptions_Experimental_VirtualDevices* >(object);
  (void)_this;
}
void GPUOptions_Experimental_VirtualDevices::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void GPUOptions_Experimental_VirtualDevices::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* GPUOptions_Experimental_VirtualDevices::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const GPUOptions_Experimental_VirtualDevices& GPUOptions_Experimental_VirtualDevices::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_GPUOptions_Experimental_VirtualDevices.base);
  return *internal_default_instance();
}


void GPUOptions_Experimental_VirtualDevices::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.GPUOptions.Experimental.VirtualDevices)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  memory_limit_mb_.Clear();
  _internal_metadata_.Clear();
}

bool GPUOptions_Experimental_VirtualDevices::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.GPUOptions.Experimental.VirtualDevices)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated float memory_limit_mb = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, this->mutable_memory_limit_mb())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(13u /* 13 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 1, 10u, input, this->mutable_memory_limit_mb())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.GPUOptions.Experimental.VirtualDevices)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.GPUOptions.Experimental.VirtualDevices)
  return false;
#undef DO_
}

void GPUOptions_Experimental_VirtualDevices::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.GPUOptions.Experimental.VirtualDevices)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated float memory_limit_mb = 1;
  if (this->memory_limit_mb_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(1, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _memory_limit_mb_cached_byte_size_));
    ::google::protobuf::internal::WireFormatLite::WriteFloatArray(
      this->memory_limit_mb().data(), this->memory_limit_mb_size(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.GPUOptions.Experimental.VirtualDevices)
}

::google::protobuf::uint8* GPUOptions_Experimental_VirtualDevices::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.GPUOptions.Experimental.VirtualDevices)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated float memory_limit_mb = 1;
  if (this->memory_limit_mb_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      1,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _memory_limit_mb_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteFloatNoTagToArray(this->memory_limit_mb_, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.GPUOptions.Experimental.VirtualDevices)
  return target;
}

size_t GPUOptions_Experimental_VirtualDevices::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.GPUOptions.Experimental.VirtualDevices)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated float memory_limit_mb = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->memory_limit_mb_size());
    size_t data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _memory_limit_mb_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void GPUOptions_Experimental_VirtualDevices::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.GPUOptions.Experimental.VirtualDevices)
  GOOGLE_DCHECK_NE(&from, this);
  const GPUOptions_Experimental_VirtualDevices* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GPUOptions_Experimental_VirtualDevices>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.GPUOptions.Experimental.VirtualDevices)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.GPUOptions.Experimental.VirtualDevices)
    MergeFrom(*source);
  }
}

void GPUOptions_Experimental_VirtualDevices::MergeFrom(const GPUOptions_Experimental_VirtualDevices& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.GPUOptions.Experimental.VirtualDevices)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  memory_limit_mb_.MergeFrom(from.memory_limit_mb_);
}

void GPUOptions_Experimental_VirtualDevices::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.GPUOptions.Experimental.VirtualDevices)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GPUOptions_Experimental_VirtualDevices::CopyFrom(const GPUOptions_Experimental_VirtualDevices& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.GPUOptions.Experimental.VirtualDevices)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GPUOptions_Experimental_VirtualDevices::IsInitialized() const {
  return true;
}

void GPUOptions_Experimental_VirtualDevices::Swap(GPUOptions_Experimental_VirtualDevices* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    GPUOptions_Experimental_VirtualDevices* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void GPUOptions_Experimental_VirtualDevices::UnsafeArenaSwap(GPUOptions_Experimental_VirtualDevices* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void GPUOptions_Experimental_VirtualDevices::InternalSwap(GPUOptions_Experimental_VirtualDevices* other) {
  using std::swap;
  memory_limit_mb_.InternalSwap(&other->memory_limit_mb_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata GPUOptions_Experimental_VirtualDevices::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void GPUOptions_Experimental::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GPUOptions_Experimental::kVirtualDevicesFieldNumber;
const int GPUOptions_Experimental::kUseUnifiedMemoryFieldNumber;
const int GPUOptions_Experimental::kNumDevToDevCopyStreamsFieldNumber;
const int GPUOptions_Experimental::kCollectiveRingOrderFieldNumber;
const int GPUOptions_Experimental::kTimestampedAllocatorFieldNumber;
const int GPUOptions_Experimental::kPendingCapFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GPUOptions_Experimental::GPUOptions_Experimental()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_GPUOptions_Experimental.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.GPUOptions.Experimental)
}
GPUOptions_Experimental::GPUOptions_Experimental(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  virtual_devices_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_GPUOptions_Experimental.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.GPUOptions.Experimental)
}
GPUOptions_Experimental::GPUOptions_Experimental(const GPUOptions_Experimental& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      virtual_devices_(from.virtual_devices_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  collective_ring_order_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.collective_ring_order().size() > 0) {
    collective_ring_order_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.collective_ring_order(),
      GetArenaNoVirtual());
  }
  ::memcpy(&num_dev_to_dev_copy_streams_, &from.num_dev_to_dev_copy_streams_,
    static_cast<size_t>(reinterpret_cast<char*>(&pending_cap_) -
    reinterpret_cast<char*>(&num_dev_to_dev_copy_streams_)) + sizeof(pending_cap_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.GPUOptions.Experimental)
}

void GPUOptions_Experimental::SharedCtor() {
  collective_ring_order_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&num_dev_to_dev_copy_streams_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&pending_cap_) -
      reinterpret_cast<char*>(&num_dev_to_dev_copy_streams_)) + sizeof(pending_cap_));
}

GPUOptions_Experimental::~GPUOptions_Experimental() {
  // @@protoc_insertion_point(destructor:tensorflow.GPUOptions.Experimental)
  SharedDtor();
}

void GPUOptions_Experimental::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  collective_ring_order_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void GPUOptions_Experimental::ArenaDtor(void* object) {
  GPUOptions_Experimental* _this = reinterpret_cast< GPUOptions_Experimental* >(object);
  (void)_this;
}
void GPUOptions_Experimental::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void GPUOptions_Experimental::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* GPUOptions_Experimental::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const GPUOptions_Experimental& GPUOptions_Experimental::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_GPUOptions_Experimental.base);
  return *internal_default_instance();
}


void GPUOptions_Experimental::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.GPUOptions.Experimental)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  virtual_devices_.Clear();
  collective_ring_order_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  ::memset(&num_dev_to_dev_copy_streams_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&pending_cap_) -
      reinterpret_cast<char*>(&num_dev_to_dev_copy_streams_)) + sizeof(pending_cap_));
  _internal_metadata_.Clear();
}

bool GPUOptions_Experimental::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.GPUOptions.Experimental)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.GPUOptions.Experimental.VirtualDevices virtual_devices = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_virtual_devices()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool use_unified_memory = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &use_unified_memory_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 num_dev_to_dev_copy_streams = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &num_dev_to_dev_copy_streams_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string collective_ring_order = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_collective_ring_order()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->collective_ring_order().data(), static_cast<int>(this->collective_ring_order().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.GPUOptions.Experimental.collective_ring_order"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool timestamped_allocator = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &timestamped_allocator_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 pending_cap = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(48u /* 48 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &pending_cap_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.GPUOptions.Experimental)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.GPUOptions.Experimental)
  return false;
#undef DO_
}

void GPUOptions_Experimental::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.GPUOptions.Experimental)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.GPUOptions.Experimental.VirtualDevices virtual_devices = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->virtual_devices_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->virtual_devices(static_cast<int>(i)),
      output);
  }

  // bool use_unified_memory = 2;
  if (this->use_unified_memory() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(2, this->use_unified_memory(), output);
  }

  // int32 num_dev_to_dev_copy_streams = 3;
  if (this->num_dev_to_dev_copy_streams() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->num_dev_to_dev_copy_streams(), output);
  }

  // string collective_ring_order = 4;
  if (this->collective_ring_order().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->collective_ring_order().data(), static_cast<int>(this->collective_ring_order().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GPUOptions.Experimental.collective_ring_order");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->collective_ring_order(), output);
  }

  // bool timestamped_allocator = 5;
  if (this->timestamped_allocator() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(5, this->timestamped_allocator(), output);
  }

  // int32 pending_cap = 6;
  if (this->pending_cap() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(6, this->pending_cap(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.GPUOptions.Experimental)
}

::google::protobuf::uint8* GPUOptions_Experimental::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.GPUOptions.Experimental)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.GPUOptions.Experimental.VirtualDevices virtual_devices = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->virtual_devices_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->virtual_devices(static_cast<int>(i)), deterministic, target);
  }

  // bool use_unified_memory = 2;
  if (this->use_unified_memory() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(2, this->use_unified_memory(), target);
  }

  // int32 num_dev_to_dev_copy_streams = 3;
  if (this->num_dev_to_dev_copy_streams() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->num_dev_to_dev_copy_streams(), target);
  }

  // string collective_ring_order = 4;
  if (this->collective_ring_order().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->collective_ring_order().data(), static_cast<int>(this->collective_ring_order().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GPUOptions.Experimental.collective_ring_order");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->collective_ring_order(), target);
  }

  // bool timestamped_allocator = 5;
  if (this->timestamped_allocator() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(5, this->timestamped_allocator(), target);
  }

  // int32 pending_cap = 6;
  if (this->pending_cap() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(6, this->pending_cap(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.GPUOptions.Experimental)
  return target;
}

size_t GPUOptions_Experimental::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.GPUOptions.Experimental)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.GPUOptions.Experimental.VirtualDevices virtual_devices = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->virtual_devices_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->virtual_devices(static_cast<int>(i)));
    }
  }

  // string collective_ring_order = 4;
  if (this->collective_ring_order().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->collective_ring_order());
  }

  // int32 num_dev_to_dev_copy_streams = 3;
  if (this->num_dev_to_dev_copy_streams() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->num_dev_to_dev_copy_streams());
  }

  // bool use_unified_memory = 2;
  if (this->use_unified_memory() != 0) {
    total_size += 1 + 1;
  }

  // bool timestamped_allocator = 5;
  if (this->timestamped_allocator() != 0) {
    total_size += 1 + 1;
  }

  // int32 pending_cap = 6;
  if (this->pending_cap() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->pending_cap());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void GPUOptions_Experimental::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.GPUOptions.Experimental)
  GOOGLE_DCHECK_NE(&from, this);
  const GPUOptions_Experimental* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GPUOptions_Experimental>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.GPUOptions.Experimental)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.GPUOptions.Experimental)
    MergeFrom(*source);
  }
}

void GPUOptions_Experimental::MergeFrom(const GPUOptions_Experimental& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.GPUOptions.Experimental)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  virtual_devices_.MergeFrom(from.virtual_devices_);
  if (from.collective_ring_order().size() > 0) {
    set_collective_ring_order(from.collective_ring_order());
  }
  if (from.num_dev_to_dev_copy_streams() != 0) {
    set_num_dev_to_dev_copy_streams(from.num_dev_to_dev_copy_streams());
  }
  if (from.use_unified_memory() != 0) {
    set_use_unified_memory(from.use_unified_memory());
  }
  if (from.timestamped_allocator() != 0) {
    set_timestamped_allocator(from.timestamped_allocator());
  }
  if (from.pending_cap() != 0) {
    set_pending_cap(from.pending_cap());
  }
}

void GPUOptions_Experimental::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.GPUOptions.Experimental)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GPUOptions_Experimental::CopyFrom(const GPUOptions_Experimental& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.GPUOptions.Experimental)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GPUOptions_Experimental::IsInitialized() const {
  return true;
}

void GPUOptions_Experimental::Swap(GPUOptions_Experimental* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    GPUOptions_Experimental* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void GPUOptions_Experimental::UnsafeArenaSwap(GPUOptions_Experimental* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void GPUOptions_Experimental::InternalSwap(GPUOptions_Experimental* other) {
  using std::swap;
  CastToBase(&virtual_devices_)->InternalSwap(CastToBase(&other->virtual_devices_));
  collective_ring_order_.Swap(&other->collective_ring_order_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(num_dev_to_dev_copy_streams_, other->num_dev_to_dev_copy_streams_);
  swap(use_unified_memory_, other->use_unified_memory_);
  swap(timestamped_allocator_, other->timestamped_allocator_);
  swap(pending_cap_, other->pending_cap_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata GPUOptions_Experimental::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void GPUOptions::InitAsDefaultInstance() {
  ::tensorflow::_GPUOptions_default_instance_._instance.get_mutable()->experimental_ = const_cast< ::tensorflow::GPUOptions_Experimental*>(
      ::tensorflow::GPUOptions_Experimental::internal_default_instance());
}
void GPUOptions::unsafe_arena_set_allocated_experimental(
    ::tensorflow::GPUOptions_Experimental* experimental) {
  if (GetArenaNoVirtual() == NULL) {
    delete experimental_;
  }
  experimental_ = experimental;
  if (experimental) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GPUOptions.experimental)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GPUOptions::kPerProcessGpuMemoryFractionFieldNumber;
const int GPUOptions::kAllowGrowthFieldNumber;
const int GPUOptions::kAllocatorTypeFieldNumber;
const int GPUOptions::kDeferredDeletionBytesFieldNumber;
const int GPUOptions::kVisibleDeviceListFieldNumber;
const int GPUOptions::kPollingActiveDelayUsecsFieldNumber;
const int GPUOptions::kPollingInactiveDelayMsecsFieldNumber;
const int GPUOptions::kForceGpuCompatibleFieldNumber;
const int GPUOptions::kExperimentalFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GPUOptions::GPUOptions()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_GPUOptions.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.GPUOptions)
}
GPUOptions::GPUOptions(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_GPUOptions.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.GPUOptions)
}
GPUOptions::GPUOptions(const GPUOptions& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  allocator_type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.allocator_type().size() > 0) {
    allocator_type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.allocator_type(),
      GetArenaNoVirtual());
  }
  visible_device_list_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.visible_device_list().size() > 0) {
    visible_device_list_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.visible_device_list(),
      GetArenaNoVirtual());
  }
  if (from.has_experimental()) {
    experimental_ = new ::tensorflow::GPUOptions_Experimental(*from.experimental_);
  } else {
    experimental_ = NULL;
  }
  ::memcpy(&per_process_gpu_memory_fraction_, &from.per_process_gpu_memory_fraction_,
    static_cast<size_t>(reinterpret_cast<char*>(&polling_inactive_delay_msecs_) -
    reinterpret_cast<char*>(&per_process_gpu_memory_fraction_)) + sizeof(polling_inactive_delay_msecs_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.GPUOptions)
}

void GPUOptions::SharedCtor() {
  allocator_type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  visible_device_list_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&experimental_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&polling_inactive_delay_msecs_) -
      reinterpret_cast<char*>(&experimental_)) + sizeof(polling_inactive_delay_msecs_));
}

GPUOptions::~GPUOptions() {
  // @@protoc_insertion_point(destructor:tensorflow.GPUOptions)
  SharedDtor();
}

void GPUOptions::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  allocator_type_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  visible_device_list_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete experimental_;
}

void GPUOptions::ArenaDtor(void* object) {
  GPUOptions* _this = reinterpret_cast< GPUOptions* >(object);
  (void)_this;
}
void GPUOptions::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void GPUOptions::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* GPUOptions::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const GPUOptions& GPUOptions::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_GPUOptions.base);
  return *internal_default_instance();
}


void GPUOptions::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.GPUOptions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  allocator_type_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  visible_device_list_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  if (GetArenaNoVirtual() == NULL && experimental_ != NULL) {
    delete experimental_;
  }
  experimental_ = NULL;
  ::memset(&per_process_gpu_memory_fraction_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&polling_inactive_delay_msecs_) -
      reinterpret_cast<char*>(&per_process_gpu_memory_fraction_)) + sizeof(polling_inactive_delay_msecs_));
  _internal_metadata_.Clear();
}

bool GPUOptions::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.GPUOptions)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // double per_process_gpu_memory_fraction = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(9u /* 9 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &per_process_gpu_memory_fraction_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string allocator_type = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_allocator_type()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->allocator_type().data(), static_cast<int>(this->allocator_type().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.GPUOptions.allocator_type"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 deferred_deletion_bytes = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &deferred_deletion_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool allow_growth = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &allow_growth_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string visible_device_list = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_visible_device_list()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->visible_device_list().data(), static_cast<int>(this->visible_device_list().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.GPUOptions.visible_device_list"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 polling_active_delay_usecs = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(48u /* 48 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &polling_active_delay_usecs_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 polling_inactive_delay_msecs = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(56u /* 56 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &polling_inactive_delay_msecs_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool force_gpu_compatible = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(64u /* 64 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &force_gpu_compatible_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.GPUOptions.Experimental experimental = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(74u /* 74 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_experimental()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.GPUOptions)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.GPUOptions)
  return false;
#undef DO_
}

void GPUOptions::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.GPUOptions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double per_process_gpu_memory_fraction = 1;
  if (this->per_process_gpu_memory_fraction() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(1, this->per_process_gpu_memory_fraction(), output);
  }

  // string allocator_type = 2;
  if (this->allocator_type().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->allocator_type().data(), static_cast<int>(this->allocator_type().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GPUOptions.allocator_type");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->allocator_type(), output);
  }

  // int64 deferred_deletion_bytes = 3;
  if (this->deferred_deletion_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->deferred_deletion_bytes(), output);
  }

  // bool allow_growth = 4;
  if (this->allow_growth() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(4, this->allow_growth(), output);
  }

  // string visible_device_list = 5;
  if (this->visible_device_list().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->visible_device_list().data(), static_cast<int>(this->visible_device_list().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GPUOptions.visible_device_list");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->visible_device_list(), output);
  }

  // int32 polling_active_delay_usecs = 6;
  if (this->polling_active_delay_usecs() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(6, this->polling_active_delay_usecs(), output);
  }

  // int32 polling_inactive_delay_msecs = 7;
  if (this->polling_inactive_delay_msecs() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(7, this->polling_inactive_delay_msecs(), output);
  }

  // bool force_gpu_compatible = 8;
  if (this->force_gpu_compatible() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(8, this->force_gpu_compatible(), output);
  }

  // .tensorflow.GPUOptions.Experimental experimental = 9;
  if (this->has_experimental()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, this->_internal_experimental(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.GPUOptions)
}

::google::protobuf::uint8* GPUOptions::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.GPUOptions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double per_process_gpu_memory_fraction = 1;
  if (this->per_process_gpu_memory_fraction() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(1, this->per_process_gpu_memory_fraction(), target);
  }

  // string allocator_type = 2;
  if (this->allocator_type().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->allocator_type().data(), static_cast<int>(this->allocator_type().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GPUOptions.allocator_type");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->allocator_type(), target);
  }

  // int64 deferred_deletion_bytes = 3;
  if (this->deferred_deletion_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->deferred_deletion_bytes(), target);
  }

  // bool allow_growth = 4;
  if (this->allow_growth() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(4, this->allow_growth(), target);
  }

  // string visible_device_list = 5;
  if (this->visible_device_list().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->visible_device_list().data(), static_cast<int>(this->visible_device_list().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.GPUOptions.visible_device_list");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->visible_device_list(), target);
  }

  // int32 polling_active_delay_usecs = 6;
  if (this->polling_active_delay_usecs() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(6, this->polling_active_delay_usecs(), target);
  }

  // int32 polling_inactive_delay_msecs = 7;
  if (this->polling_inactive_delay_msecs() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(7, this->polling_inactive_delay_msecs(), target);
  }

  // bool force_gpu_compatible = 8;
  if (this->force_gpu_compatible() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(8, this->force_gpu_compatible(), target);
  }

  // .tensorflow.GPUOptions.Experimental experimental = 9;
  if (this->has_experimental()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        9, this->_internal_experimental(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.GPUOptions)
  return target;
}

size_t GPUOptions::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.GPUOptions)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string allocator_type = 2;
  if (this->allocator_type().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->allocator_type());
  }

  // string visible_device_list = 5;
  if (this->visible_device_list().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->visible_device_list());
  }

  // .tensorflow.GPUOptions.Experimental experimental = 9;
  if (this->has_experimental()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *experimental_);
  }

  // double per_process_gpu_memory_fraction = 1;
  if (this->per_process_gpu_memory_fraction() != 0) {
    total_size += 1 + 8;
  }

  // int64 deferred_deletion_bytes = 3;
  if (this->deferred_deletion_bytes() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->deferred_deletion_bytes());
  }

  // int32 polling_active_delay_usecs = 6;
  if (this->polling_active_delay_usecs() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->polling_active_delay_usecs());
  }

  // bool allow_growth = 4;
  if (this->allow_growth() != 0) {
    total_size += 1 + 1;
  }

  // bool force_gpu_compatible = 8;
  if (this->force_gpu_compatible() != 0) {
    total_size += 1 + 1;
  }

  // int32 polling_inactive_delay_msecs = 7;
  if (this->polling_inactive_delay_msecs() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->polling_inactive_delay_msecs());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void GPUOptions::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.GPUOptions)
  GOOGLE_DCHECK_NE(&from, this);
  const GPUOptions* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GPUOptions>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.GPUOptions)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.GPUOptions)
    MergeFrom(*source);
  }
}

void GPUOptions::MergeFrom(const GPUOptions& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.GPUOptions)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.allocator_type().size() > 0) {
    set_allocator_type(from.allocator_type());
  }
  if (from.visible_device_list().size() > 0) {
    set_visible_device_list(from.visible_device_list());
  }
  if (from.has_experimental()) {
    mutable_experimental()->::tensorflow::GPUOptions_Experimental::MergeFrom(from.experimental());
  }
  if (from.per_process_gpu_memory_fraction() != 0) {
    set_per_process_gpu_memory_fraction(from.per_process_gpu_memory_fraction());
  }
  if (from.deferred_deletion_bytes() != 0) {
    set_deferred_deletion_bytes(from.deferred_deletion_bytes());
  }
  if (from.polling_active_delay_usecs() != 0) {
    set_polling_active_delay_usecs(from.polling_active_delay_usecs());
  }
  if (from.allow_growth() != 0) {
    set_allow_growth(from.allow_growth());
  }
  if (from.force_gpu_compatible() != 0) {
    set_force_gpu_compatible(from.force_gpu_compatible());
  }
  if (from.polling_inactive_delay_msecs() != 0) {
    set_polling_inactive_delay_msecs(from.polling_inactive_delay_msecs());
  }
}

void GPUOptions::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.GPUOptions)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GPUOptions::CopyFrom(const GPUOptions& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.GPUOptions)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GPUOptions::IsInitialized() const {
  return true;
}

void GPUOptions::Swap(GPUOptions* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    GPUOptions* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void GPUOptions::UnsafeArenaSwap(GPUOptions* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void GPUOptions::InternalSwap(GPUOptions* other) {
  using std::swap;
  allocator_type_.Swap(&other->allocator_type_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  visible_device_list_.Swap(&other->visible_device_list_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(experimental_, other->experimental_);
  swap(per_process_gpu_memory_fraction_, other->per_process_gpu_memory_fraction_);
  swap(deferred_deletion_bytes_, other->deferred_deletion_bytes_);
  swap(polling_active_delay_usecs_, other->polling_active_delay_usecs_);
  swap(allow_growth_, other->allow_growth_);
  swap(force_gpu_compatible_, other->force_gpu_compatible_);
  swap(polling_inactive_delay_msecs_, other->polling_inactive_delay_msecs_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata GPUOptions::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void OptimizerOptions::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int OptimizerOptions::kDoCommonSubexpressionEliminationFieldNumber;
const int OptimizerOptions::kDoConstantFoldingFieldNumber;
const int OptimizerOptions::kMaxFoldedConstantInBytesFieldNumber;
const int OptimizerOptions::kDoFunctionInliningFieldNumber;
const int OptimizerOptions::kOptLevelFieldNumber;
const int OptimizerOptions::kGlobalJitLevelFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

OptimizerOptions::OptimizerOptions()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_OptimizerOptions.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.OptimizerOptions)
}
OptimizerOptions::OptimizerOptions(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_OptimizerOptions.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.OptimizerOptions)
}
OptimizerOptions::OptimizerOptions(const OptimizerOptions& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&do_common_subexpression_elimination_, &from.do_common_subexpression_elimination_,
    static_cast<size_t>(reinterpret_cast<char*>(&global_jit_level_) -
    reinterpret_cast<char*>(&do_common_subexpression_elimination_)) + sizeof(global_jit_level_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.OptimizerOptions)
}

void OptimizerOptions::SharedCtor() {
  ::memset(&do_common_subexpression_elimination_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&global_jit_level_) -
      reinterpret_cast<char*>(&do_common_subexpression_elimination_)) + sizeof(global_jit_level_));
}

OptimizerOptions::~OptimizerOptions() {
  // @@protoc_insertion_point(destructor:tensorflow.OptimizerOptions)
  SharedDtor();
}

void OptimizerOptions::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void OptimizerOptions::ArenaDtor(void* object) {
  OptimizerOptions* _this = reinterpret_cast< OptimizerOptions* >(object);
  (void)_this;
}
void OptimizerOptions::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void OptimizerOptions::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* OptimizerOptions::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const OptimizerOptions& OptimizerOptions::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_OptimizerOptions.base);
  return *internal_default_instance();
}


void OptimizerOptions::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.OptimizerOptions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&do_common_subexpression_elimination_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&global_jit_level_) -
      reinterpret_cast<char*>(&do_common_subexpression_elimination_)) + sizeof(global_jit_level_));
  _internal_metadata_.Clear();
}

bool OptimizerOptions::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.OptimizerOptions)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // bool do_common_subexpression_elimination = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &do_common_subexpression_elimination_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool do_constant_folding = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &do_constant_folding_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.OptimizerOptions.Level opt_level = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_opt_level(static_cast< ::tensorflow::OptimizerOptions_Level >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool do_function_inlining = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &do_function_inlining_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.OptimizerOptions.GlobalJitLevel global_jit_level = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_global_jit_level(static_cast< ::tensorflow::OptimizerOptions_GlobalJitLevel >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 max_folded_constant_in_bytes = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(48u /* 48 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &max_folded_constant_in_bytes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.OptimizerOptions)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.OptimizerOptions)
  return false;
#undef DO_
}

void OptimizerOptions::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.OptimizerOptions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bool do_common_subexpression_elimination = 1;
  if (this->do_common_subexpression_elimination() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(1, this->do_common_subexpression_elimination(), output);
  }

  // bool do_constant_folding = 2;
  if (this->do_constant_folding() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(2, this->do_constant_folding(), output);
  }

  // .tensorflow.OptimizerOptions.Level opt_level = 3;
  if (this->opt_level() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      3, this->opt_level(), output);
  }

  // bool do_function_inlining = 4;
  if (this->do_function_inlining() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(4, this->do_function_inlining(), output);
  }

  // .tensorflow.OptimizerOptions.GlobalJitLevel global_jit_level = 5;
  if (this->global_jit_level() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      5, this->global_jit_level(), output);
  }

  // int64 max_folded_constant_in_bytes = 6;
  if (this->max_folded_constant_in_bytes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(6, this->max_folded_constant_in_bytes(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.OptimizerOptions)
}

::google::protobuf::uint8* OptimizerOptions::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.OptimizerOptions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bool do_common_subexpression_elimination = 1;
  if (this->do_common_subexpression_elimination() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(1, this->do_common_subexpression_elimination(), target);
  }

  // bool do_constant_folding = 2;
  if (this->do_constant_folding() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(2, this->do_constant_folding(), target);
  }

  // .tensorflow.OptimizerOptions.Level opt_level = 3;
  if (this->opt_level() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      3, this->opt_level(), target);
  }

  // bool do_function_inlining = 4;
  if (this->do_function_inlining() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(4, this->do_function_inlining(), target);
  }

  // .tensorflow.OptimizerOptions.GlobalJitLevel global_jit_level = 5;
  if (this->global_jit_level() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      5, this->global_jit_level(), target);
  }

  // int64 max_folded_constant_in_bytes = 6;
  if (this->max_folded_constant_in_bytes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(6, this->max_folded_constant_in_bytes(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.OptimizerOptions)
  return target;
}

size_t OptimizerOptions::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.OptimizerOptions)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // bool do_common_subexpression_elimination = 1;
  if (this->do_common_subexpression_elimination() != 0) {
    total_size += 1 + 1;
  }

  // bool do_constant_folding = 2;
  if (this->do_constant_folding() != 0) {
    total_size += 1 + 1;
  }

  // bool do_function_inlining = 4;
  if (this->do_function_inlining() != 0) {
    total_size += 1 + 1;
  }

  // .tensorflow.OptimizerOptions.Level opt_level = 3;
  if (this->opt_level() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->opt_level());
  }

  // int64 max_folded_constant_in_bytes = 6;
  if (this->max_folded_constant_in_bytes() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->max_folded_constant_in_bytes());
  }

  // .tensorflow.OptimizerOptions.GlobalJitLevel global_jit_level = 5;
  if (this->global_jit_level() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->global_jit_level());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void OptimizerOptions::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.OptimizerOptions)
  GOOGLE_DCHECK_NE(&from, this);
  const OptimizerOptions* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const OptimizerOptions>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.OptimizerOptions)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.OptimizerOptions)
    MergeFrom(*source);
  }
}

void OptimizerOptions::MergeFrom(const OptimizerOptions& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.OptimizerOptions)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.do_common_subexpression_elimination() != 0) {
    set_do_common_subexpression_elimination(from.do_common_subexpression_elimination());
  }
  if (from.do_constant_folding() != 0) {
    set_do_constant_folding(from.do_constant_folding());
  }
  if (from.do_function_inlining() != 0) {
    set_do_function_inlining(from.do_function_inlining());
  }
  if (from.opt_level() != 0) {
    set_opt_level(from.opt_level());
  }
  if (from.max_folded_constant_in_bytes() != 0) {
    set_max_folded_constant_in_bytes(from.max_folded_constant_in_bytes());
  }
  if (from.global_jit_level() != 0) {
    set_global_jit_level(from.global_jit_level());
  }
}

void OptimizerOptions::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.OptimizerOptions)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OptimizerOptions::CopyFrom(const OptimizerOptions& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.OptimizerOptions)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OptimizerOptions::IsInitialized() const {
  return true;
}

void OptimizerOptions::Swap(OptimizerOptions* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    OptimizerOptions* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void OptimizerOptions::UnsafeArenaSwap(OptimizerOptions* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void OptimizerOptions::InternalSwap(OptimizerOptions* other) {
  using std::swap;
  swap(do_common_subexpression_elimination_, other->do_common_subexpression_elimination_);
  swap(do_constant_folding_, other->do_constant_folding_);
  swap(do_function_inlining_, other->do_function_inlining_);
  swap(opt_level_, other->opt_level_);
  swap(max_folded_constant_in_bytes_, other->max_folded_constant_in_bytes_);
  swap(global_jit_level_, other->global_jit_level_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata OptimizerOptions::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void GraphOptions::InitAsDefaultInstance() {
  ::tensorflow::_GraphOptions_default_instance_._instance.get_mutable()->optimizer_options_ = const_cast< ::tensorflow::OptimizerOptions*>(
      ::tensorflow::OptimizerOptions::internal_default_instance());
  ::tensorflow::_GraphOptions_default_instance_._instance.get_mutable()->rewrite_options_ = const_cast< ::tensorflow::RewriterConfig*>(
      ::tensorflow::RewriterConfig::internal_default_instance());
}
void GraphOptions::unsafe_arena_set_allocated_optimizer_options(
    ::tensorflow::OptimizerOptions* optimizer_options) {
  if (GetArenaNoVirtual() == NULL) {
    delete optimizer_options_;
  }
  optimizer_options_ = optimizer_options;
  if (optimizer_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GraphOptions.optimizer_options)
}
void GraphOptions::unsafe_arena_set_allocated_rewrite_options(
    ::tensorflow::RewriterConfig* rewrite_options) {
  if (GetArenaNoVirtual() == NULL) {
    delete rewrite_options_;
  }
  rewrite_options_ = rewrite_options;
  if (rewrite_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GraphOptions.rewrite_options)
}
void GraphOptions::clear_rewrite_options() {
  if (GetArenaNoVirtual() == NULL && rewrite_options_ != NULL) {
    delete rewrite_options_;
  }
  rewrite_options_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GraphOptions::kEnableRecvSchedulingFieldNumber;
const int GraphOptions::kOptimizerOptionsFieldNumber;
const int GraphOptions::kBuildCostModelFieldNumber;
const int GraphOptions::kBuildCostModelAfterFieldNumber;
const int GraphOptions::kInferShapesFieldNumber;
const int GraphOptions::kPlacePrunedGraphFieldNumber;
const int GraphOptions::kEnableBfloat16SendrecvFieldNumber;
const int GraphOptions::kTimelineStepFieldNumber;
const int GraphOptions::kRewriteOptionsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GraphOptions::GraphOptions()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_GraphOptions.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.GraphOptions)
}
GraphOptions::GraphOptions(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_GraphOptions.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.GraphOptions)
}
GraphOptions::GraphOptions(const GraphOptions& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_optimizer_options()) {
    optimizer_options_ = new ::tensorflow::OptimizerOptions(*from.optimizer_options_);
  } else {
    optimizer_options_ = NULL;
  }
  if (from.has_rewrite_options()) {
    rewrite_options_ = new ::tensorflow::RewriterConfig(*from.rewrite_options_);
  } else {
    rewrite_options_ = NULL;
  }
  ::memcpy(&build_cost_model_, &from.build_cost_model_,
    static_cast<size_t>(reinterpret_cast<char*>(&build_cost_model_after_) -
    reinterpret_cast<char*>(&build_cost_model_)) + sizeof(build_cost_model_after_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.GraphOptions)
}

void GraphOptions::SharedCtor() {
  ::memset(&optimizer_options_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&build_cost_model_after_) -
      reinterpret_cast<char*>(&optimizer_options_)) + sizeof(build_cost_model_after_));
}

GraphOptions::~GraphOptions() {
  // @@protoc_insertion_point(destructor:tensorflow.GraphOptions)
  SharedDtor();
}

void GraphOptions::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (this != internal_default_instance()) delete optimizer_options_;
  if (this != internal_default_instance()) delete rewrite_options_;
}

void GraphOptions::ArenaDtor(void* object) {
  GraphOptions* _this = reinterpret_cast< GraphOptions* >(object);
  (void)_this;
}
void GraphOptions::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void GraphOptions::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* GraphOptions::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const GraphOptions& GraphOptions::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_GraphOptions.base);
  return *internal_default_instance();
}


void GraphOptions::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.GraphOptions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && optimizer_options_ != NULL) {
    delete optimizer_options_;
  }
  optimizer_options_ = NULL;
  if (GetArenaNoVirtual() == NULL && rewrite_options_ != NULL) {
    delete rewrite_options_;
  }
  rewrite_options_ = NULL;
  ::memset(&build_cost_model_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&build_cost_model_after_) -
      reinterpret_cast<char*>(&build_cost_model_)) + sizeof(build_cost_model_after_));
  _internal_metadata_.Clear();
}

bool GraphOptions::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.GraphOptions)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // bool enable_recv_scheduling = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &enable_recv_scheduling_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.OptimizerOptions optimizer_options = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_optimizer_options()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 build_cost_model = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &build_cost_model_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool infer_shapes = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &infer_shapes_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool place_pruned_graph = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(48u /* 48 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &place_pruned_graph_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool enable_bfloat16_sendrecv = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(56u /* 56 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &enable_bfloat16_sendrecv_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 timeline_step = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(64u /* 64 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &timeline_step_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 build_cost_model_after = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(72u /* 72 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &build_cost_model_after_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.RewriterConfig rewrite_options = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(82u /* 82 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_rewrite_options()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.GraphOptions)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.GraphOptions)
  return false;
#undef DO_
}

void GraphOptions::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.GraphOptions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bool enable_recv_scheduling = 2;
  if (this->enable_recv_scheduling() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(2, this->enable_recv_scheduling(), output);
  }

  // .tensorflow.OptimizerOptions optimizer_options = 3;
  if (this->has_optimizer_options()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->_internal_optimizer_options(), output);
  }

  // int64 build_cost_model = 4;
  if (this->build_cost_model() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->build_cost_model(), output);
  }

  // bool infer_shapes = 5;
  if (this->infer_shapes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(5, this->infer_shapes(), output);
  }

  // bool place_pruned_graph = 6;
  if (this->place_pruned_graph() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(6, this->place_pruned_graph(), output);
  }

  // bool enable_bfloat16_sendrecv = 7;
  if (this->enable_bfloat16_sendrecv() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(7, this->enable_bfloat16_sendrecv(), output);
  }

  // int32 timeline_step = 8;
  if (this->timeline_step() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(8, this->timeline_step(), output);
  }

  // int64 build_cost_model_after = 9;
  if (this->build_cost_model_after() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->build_cost_model_after(), output);
  }

  // .tensorflow.RewriterConfig rewrite_options = 10;
  if (this->has_rewrite_options()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      10, this->_internal_rewrite_options(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.GraphOptions)
}

::google::protobuf::uint8* GraphOptions::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.GraphOptions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bool enable_recv_scheduling = 2;
  if (this->enable_recv_scheduling() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(2, this->enable_recv_scheduling(), target);
  }

  // .tensorflow.OptimizerOptions optimizer_options = 3;
  if (this->has_optimizer_options()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->_internal_optimizer_options(), deterministic, target);
  }

  // int64 build_cost_model = 4;
  if (this->build_cost_model() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->build_cost_model(), target);
  }

  // bool infer_shapes = 5;
  if (this->infer_shapes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(5, this->infer_shapes(), target);
  }

  // bool place_pruned_graph = 6;
  if (this->place_pruned_graph() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(6, this->place_pruned_graph(), target);
  }

  // bool enable_bfloat16_sendrecv = 7;
  if (this->enable_bfloat16_sendrecv() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(7, this->enable_bfloat16_sendrecv(), target);
  }

  // int32 timeline_step = 8;
  if (this->timeline_step() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(8, this->timeline_step(), target);
  }

  // int64 build_cost_model_after = 9;
  if (this->build_cost_model_after() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->build_cost_model_after(), target);
  }

  // .tensorflow.RewriterConfig rewrite_options = 10;
  if (this->has_rewrite_options()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        10, this->_internal_rewrite_options(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.GraphOptions)
  return target;
}

size_t GraphOptions::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.GraphOptions)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .tensorflow.OptimizerOptions optimizer_options = 3;
  if (this->has_optimizer_options()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *optimizer_options_);
  }

  // .tensorflow.RewriterConfig rewrite_options = 10;
  if (this->has_rewrite_options()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *rewrite_options_);
  }

  // int64 build_cost_model = 4;
  if (this->build_cost_model() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->build_cost_model());
  }

  // bool enable_recv_scheduling = 2;
  if (this->enable_recv_scheduling() != 0) {
    total_size += 1 + 1;
  }

  // bool infer_shapes = 5;
  if (this->infer_shapes() != 0) {
    total_size += 1 + 1;
  }

  // bool place_pruned_graph = 6;
  if (this->place_pruned_graph() != 0) {
    total_size += 1 + 1;
  }

  // bool enable_bfloat16_sendrecv = 7;
  if (this->enable_bfloat16_sendrecv() != 0) {
    total_size += 1 + 1;
  }

  // int32 timeline_step = 8;
  if (this->timeline_step() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->timeline_step());
  }

  // int64 build_cost_model_after = 9;
  if (this->build_cost_model_after() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->build_cost_model_after());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void GraphOptions::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.GraphOptions)
  GOOGLE_DCHECK_NE(&from, this);
  const GraphOptions* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GraphOptions>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.GraphOptions)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.GraphOptions)
    MergeFrom(*source);
  }
}

void GraphOptions::MergeFrom(const GraphOptions& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.GraphOptions)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_optimizer_options()) {
    mutable_optimizer_options()->::tensorflow::OptimizerOptions::MergeFrom(from.optimizer_options());
  }
  if (from.has_rewrite_options()) {
    mutable_rewrite_options()->::tensorflow::RewriterConfig::MergeFrom(from.rewrite_options());
  }
  if (from.build_cost_model() != 0) {
    set_build_cost_model(from.build_cost_model());
  }
  if (from.enable_recv_scheduling() != 0) {
    set_enable_recv_scheduling(from.enable_recv_scheduling());
  }
  if (from.infer_shapes() != 0) {
    set_infer_shapes(from.infer_shapes());
  }
  if (from.place_pruned_graph() != 0) {
    set_place_pruned_graph(from.place_pruned_graph());
  }
  if (from.enable_bfloat16_sendrecv() != 0) {
    set_enable_bfloat16_sendrecv(from.enable_bfloat16_sendrecv());
  }
  if (from.timeline_step() != 0) {
    set_timeline_step(from.timeline_step());
  }
  if (from.build_cost_model_after() != 0) {
    set_build_cost_model_after(from.build_cost_model_after());
  }
}

void GraphOptions::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.GraphOptions)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GraphOptions::CopyFrom(const GraphOptions& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.GraphOptions)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GraphOptions::IsInitialized() const {
  return true;
}

void GraphOptions::Swap(GraphOptions* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    GraphOptions* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void GraphOptions::UnsafeArenaSwap(GraphOptions* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void GraphOptions::InternalSwap(GraphOptions* other) {
  using std::swap;
  swap(optimizer_options_, other->optimizer_options_);
  swap(rewrite_options_, other->rewrite_options_);
  swap(build_cost_model_, other->build_cost_model_);
  swap(enable_recv_scheduling_, other->enable_recv_scheduling_);
  swap(infer_shapes_, other->infer_shapes_);
  swap(place_pruned_graph_, other->place_pruned_graph_);
  swap(enable_bfloat16_sendrecv_, other->enable_bfloat16_sendrecv_);
  swap(timeline_step_, other->timeline_step_);
  swap(build_cost_model_after_, other->build_cost_model_after_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata GraphOptions::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ThreadPoolOptionProto::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ThreadPoolOptionProto::kNumThreadsFieldNumber;
const int ThreadPoolOptionProto::kGlobalNameFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ThreadPoolOptionProto::ThreadPoolOptionProto()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_ThreadPoolOptionProto.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.ThreadPoolOptionProto)
}
ThreadPoolOptionProto::ThreadPoolOptionProto(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_ThreadPoolOptionProto.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.ThreadPoolOptionProto)
}
ThreadPoolOptionProto::ThreadPoolOptionProto(const ThreadPoolOptionProto& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  global_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.global_name().size() > 0) {
    global_name_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.global_name(),
      GetArenaNoVirtual());
  }
  num_threads_ = from.num_threads_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.ThreadPoolOptionProto)
}

void ThreadPoolOptionProto::SharedCtor() {
  global_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  num_threads_ = 0;
}

ThreadPoolOptionProto::~ThreadPoolOptionProto() {
  // @@protoc_insertion_point(destructor:tensorflow.ThreadPoolOptionProto)
  SharedDtor();
}

void ThreadPoolOptionProto::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  global_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void ThreadPoolOptionProto::ArenaDtor(void* object) {
  ThreadPoolOptionProto* _this = reinterpret_cast< ThreadPoolOptionProto* >(object);
  (void)_this;
}
void ThreadPoolOptionProto::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void ThreadPoolOptionProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ThreadPoolOptionProto::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ThreadPoolOptionProto& ThreadPoolOptionProto::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_ThreadPoolOptionProto.base);
  return *internal_default_instance();
}


void ThreadPoolOptionProto::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.ThreadPoolOptionProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  global_name_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  num_threads_ = 0;
  _internal_metadata_.Clear();
}

bool ThreadPoolOptionProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.ThreadPoolOptionProto)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 num_threads = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &num_threads_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string global_name = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_global_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->global_name().data(), static_cast<int>(this->global_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ThreadPoolOptionProto.global_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.ThreadPoolOptionProto)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.ThreadPoolOptionProto)
  return false;
#undef DO_
}

void ThreadPoolOptionProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.ThreadPoolOptionProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 num_threads = 1;
  if (this->num_threads() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->num_threads(), output);
  }

  // string global_name = 2;
  if (this->global_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->global_name().data(), static_cast<int>(this->global_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ThreadPoolOptionProto.global_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->global_name(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.ThreadPoolOptionProto)
}

::google::protobuf::uint8* ThreadPoolOptionProto::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.ThreadPoolOptionProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 num_threads = 1;
  if (this->num_threads() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->num_threads(), target);
  }

  // string global_name = 2;
  if (this->global_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->global_name().data(), static_cast<int>(this->global_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ThreadPoolOptionProto.global_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->global_name(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.ThreadPoolOptionProto)
  return target;
}

size_t ThreadPoolOptionProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.ThreadPoolOptionProto)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string global_name = 2;
  if (this->global_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->global_name());
  }

  // int32 num_threads = 1;
  if (this->num_threads() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->num_threads());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ThreadPoolOptionProto::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.ThreadPoolOptionProto)
  GOOGLE_DCHECK_NE(&from, this);
  const ThreadPoolOptionProto* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ThreadPoolOptionProto>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.ThreadPoolOptionProto)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.ThreadPoolOptionProto)
    MergeFrom(*source);
  }
}

void ThreadPoolOptionProto::MergeFrom(const ThreadPoolOptionProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.ThreadPoolOptionProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.global_name().size() > 0) {
    set_global_name(from.global_name());
  }
  if (from.num_threads() != 0) {
    set_num_threads(from.num_threads());
  }
}

void ThreadPoolOptionProto::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.ThreadPoolOptionProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ThreadPoolOptionProto::CopyFrom(const ThreadPoolOptionProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.ThreadPoolOptionProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ThreadPoolOptionProto::IsInitialized() const {
  return true;
}

void ThreadPoolOptionProto::Swap(ThreadPoolOptionProto* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    ThreadPoolOptionProto* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void ThreadPoolOptionProto::UnsafeArenaSwap(ThreadPoolOptionProto* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void ThreadPoolOptionProto::InternalSwap(ThreadPoolOptionProto* other) {
  using std::swap;
  global_name_.Swap(&other->global_name_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(num_threads_, other->num_threads_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ThreadPoolOptionProto::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void RPCOptions::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RPCOptions::kUseRpcForInprocessMasterFieldNumber;
const int RPCOptions::kCompressionAlgorithmFieldNumber;
const int RPCOptions::kCompressionLevelFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RPCOptions::RPCOptions()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_RPCOptions.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.RPCOptions)
}
RPCOptions::RPCOptions(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_RPCOptions.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.RPCOptions)
}
RPCOptions::RPCOptions(const RPCOptions& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  compression_algorithm_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.compression_algorithm().size() > 0) {
    compression_algorithm_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.compression_algorithm(),
      GetArenaNoVirtual());
  }
  ::memcpy(&use_rpc_for_inprocess_master_, &from.use_rpc_for_inprocess_master_,
    static_cast<size_t>(reinterpret_cast<char*>(&compression_level_) -
    reinterpret_cast<char*>(&use_rpc_for_inprocess_master_)) + sizeof(compression_level_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.RPCOptions)
}

void RPCOptions::SharedCtor() {
  compression_algorithm_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&use_rpc_for_inprocess_master_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&compression_level_) -
      reinterpret_cast<char*>(&use_rpc_for_inprocess_master_)) + sizeof(compression_level_));
}

RPCOptions::~RPCOptions() {
  // @@protoc_insertion_point(destructor:tensorflow.RPCOptions)
  SharedDtor();
}

void RPCOptions::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  compression_algorithm_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void RPCOptions::ArenaDtor(void* object) {
  RPCOptions* _this = reinterpret_cast< RPCOptions* >(object);
  (void)_this;
}
void RPCOptions::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void RPCOptions::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* RPCOptions::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const RPCOptions& RPCOptions::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_RPCOptions.base);
  return *internal_default_instance();
}


void RPCOptions::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.RPCOptions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  compression_algorithm_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  ::memset(&use_rpc_for_inprocess_master_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&compression_level_) -
      reinterpret_cast<char*>(&use_rpc_for_inprocess_master_)) + sizeof(compression_level_));
  _internal_metadata_.Clear();
}

bool RPCOptions::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.RPCOptions)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // bool use_rpc_for_inprocess_master = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &use_rpc_for_inprocess_master_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string compression_algorithm = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_compression_algorithm()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->compression_algorithm().data(), static_cast<int>(this->compression_algorithm().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.RPCOptions.compression_algorithm"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 compression_level = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &compression_level_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.RPCOptions)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.RPCOptions)
  return false;
#undef DO_
}

void RPCOptions::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.RPCOptions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bool use_rpc_for_inprocess_master = 1;
  if (this->use_rpc_for_inprocess_master() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(1, this->use_rpc_for_inprocess_master(), output);
  }

  // string compression_algorithm = 2;
  if (this->compression_algorithm().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->compression_algorithm().data(), static_cast<int>(this->compression_algorithm().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.RPCOptions.compression_algorithm");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->compression_algorithm(), output);
  }

  // int32 compression_level = 3;
  if (this->compression_level() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->compression_level(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.RPCOptions)
}

::google::protobuf::uint8* RPCOptions::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.RPCOptions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bool use_rpc_for_inprocess_master = 1;
  if (this->use_rpc_for_inprocess_master() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(1, this->use_rpc_for_inprocess_master(), target);
  }

  // string compression_algorithm = 2;
  if (this->compression_algorithm().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->compression_algorithm().data(), static_cast<int>(this->compression_algorithm().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.RPCOptions.compression_algorithm");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->compression_algorithm(), target);
  }

  // int32 compression_level = 3;
  if (this->compression_level() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->compression_level(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.RPCOptions)
  return target;
}

size_t RPCOptions::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.RPCOptions)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string compression_algorithm = 2;
  if (this->compression_algorithm().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->compression_algorithm());
  }

  // bool use_rpc_for_inprocess_master = 1;
  if (this->use_rpc_for_inprocess_master() != 0) {
    total_size += 1 + 1;
  }

  // int32 compression_level = 3;
  if (this->compression_level() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->compression_level());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RPCOptions::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.RPCOptions)
  GOOGLE_DCHECK_NE(&from, this);
  const RPCOptions* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RPCOptions>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.RPCOptions)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.RPCOptions)
    MergeFrom(*source);
  }
}

void RPCOptions::MergeFrom(const RPCOptions& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.RPCOptions)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.compression_algorithm().size() > 0) {
    set_compression_algorithm(from.compression_algorithm());
  }
  if (from.use_rpc_for_inprocess_master() != 0) {
    set_use_rpc_for_inprocess_master(from.use_rpc_for_inprocess_master());
  }
  if (from.compression_level() != 0) {
    set_compression_level(from.compression_level());
  }
}

void RPCOptions::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.RPCOptions)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RPCOptions::CopyFrom(const RPCOptions& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.RPCOptions)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RPCOptions::IsInitialized() const {
  return true;
}

void RPCOptions::Swap(RPCOptions* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    RPCOptions* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void RPCOptions::UnsafeArenaSwap(RPCOptions* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void RPCOptions::InternalSwap(RPCOptions* other) {
  using std::swap;
  compression_algorithm_.Swap(&other->compression_algorithm_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(use_rpc_for_inprocess_master_, other->use_rpc_for_inprocess_master_);
  swap(compression_level_, other->compression_level_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata RPCOptions::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

ConfigProto_DeviceCountEntry_DoNotUse::ConfigProto_DeviceCountEntry_DoNotUse() {}
ConfigProto_DeviceCountEntry_DoNotUse::ConfigProto_DeviceCountEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void ConfigProto_DeviceCountEntry_DoNotUse::MergeFrom(const ConfigProto_DeviceCountEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata ConfigProto_DeviceCountEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[7];
}
void ConfigProto_DeviceCountEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void ConfigProto_Experimental::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ConfigProto_Experimental::kCollectiveGroupLeaderFieldNumber;
const int ConfigProto_Experimental::kExecutorTypeFieldNumber;
const int ConfigProto_Experimental::kRecvBufMaxChunkFieldNumber;
const int ConfigProto_Experimental::kUseNumaAffinityFieldNumber;
const int ConfigProto_Experimental::kCollectiveDeterministicSequentialExecutionFieldNumber;
const int ConfigProto_Experimental::kCollectiveNcclFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ConfigProto_Experimental::ConfigProto_Experimental()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_ConfigProto_Experimental.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.ConfigProto.Experimental)
}
ConfigProto_Experimental::ConfigProto_Experimental(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_ConfigProto_Experimental.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.ConfigProto.Experimental)
}
ConfigProto_Experimental::ConfigProto_Experimental(const ConfigProto_Experimental& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  collective_group_leader_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.collective_group_leader().size() > 0) {
    collective_group_leader_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.collective_group_leader(),
      GetArenaNoVirtual());
  }
  executor_type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.executor_type().size() > 0) {
    executor_type_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.executor_type(),
      GetArenaNoVirtual());
  }
  ::memcpy(&recv_buf_max_chunk_, &from.recv_buf_max_chunk_,
    static_cast<size_t>(reinterpret_cast<char*>(&collective_nccl_) -
    reinterpret_cast<char*>(&recv_buf_max_chunk_)) + sizeof(collective_nccl_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.ConfigProto.Experimental)
}

void ConfigProto_Experimental::SharedCtor() {
  collective_group_leader_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  executor_type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&recv_buf_max_chunk_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&collective_nccl_) -
      reinterpret_cast<char*>(&recv_buf_max_chunk_)) + sizeof(collective_nccl_));
}

ConfigProto_Experimental::~ConfigProto_Experimental() {
  // @@protoc_insertion_point(destructor:tensorflow.ConfigProto.Experimental)
  SharedDtor();
}

void ConfigProto_Experimental::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  collective_group_leader_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  executor_type_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void ConfigProto_Experimental::ArenaDtor(void* object) {
  ConfigProto_Experimental* _this = reinterpret_cast< ConfigProto_Experimental* >(object);
  (void)_this;
}
void ConfigProto_Experimental::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void ConfigProto_Experimental::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ConfigProto_Experimental::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ConfigProto_Experimental& ConfigProto_Experimental::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_ConfigProto_Experimental.base);
  return *internal_default_instance();
}


void ConfigProto_Experimental::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.ConfigProto.Experimental)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  collective_group_leader_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  executor_type_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  ::memset(&recv_buf_max_chunk_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&collective_nccl_) -
      reinterpret_cast<char*>(&recv_buf_max_chunk_)) + sizeof(collective_nccl_));
  _internal_metadata_.Clear();
}

bool ConfigProto_Experimental::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.ConfigProto.Experimental)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string collective_group_leader = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_collective_group_leader()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->collective_group_leader().data(), static_cast<int>(this->collective_group_leader().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ConfigProto.Experimental.collective_group_leader"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string executor_type = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_executor_type()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->executor_type().data(), static_cast<int>(this->executor_type().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ConfigProto.Experimental.executor_type"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 recv_buf_max_chunk = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &recv_buf_max_chunk_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool use_numa_affinity = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &use_numa_affinity_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool collective_deterministic_sequential_execution = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(48u /* 48 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &collective_deterministic_sequential_execution_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool collective_nccl = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(56u /* 56 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &collective_nccl_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.ConfigProto.Experimental)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.ConfigProto.Experimental)
  return false;
#undef DO_
}

void ConfigProto_Experimental::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.ConfigProto.Experimental)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string collective_group_leader = 1;
  if (this->collective_group_leader().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->collective_group_leader().data(), static_cast<int>(this->collective_group_leader().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ConfigProto.Experimental.collective_group_leader");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->collective_group_leader(), output);
  }

  // string executor_type = 3;
  if (this->executor_type().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->executor_type().data(), static_cast<int>(this->executor_type().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ConfigProto.Experimental.executor_type");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->executor_type(), output);
  }

  // int32 recv_buf_max_chunk = 4;
  if (this->recv_buf_max_chunk() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->recv_buf_max_chunk(), output);
  }

  // bool use_numa_affinity = 5;
  if (this->use_numa_affinity() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(5, this->use_numa_affinity(), output);
  }

  // bool collective_deterministic_sequential_execution = 6;
  if (this->collective_deterministic_sequential_execution() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(6, this->collective_deterministic_sequential_execution(), output);
  }

  // bool collective_nccl = 7;
  if (this->collective_nccl() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(7, this->collective_nccl(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.ConfigProto.Experimental)
}

::google::protobuf::uint8* ConfigProto_Experimental::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.ConfigProto.Experimental)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string collective_group_leader = 1;
  if (this->collective_group_leader().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->collective_group_leader().data(), static_cast<int>(this->collective_group_leader().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ConfigProto.Experimental.collective_group_leader");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->collective_group_leader(), target);
  }

  // string executor_type = 3;
  if (this->executor_type().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->executor_type().data(), static_cast<int>(this->executor_type().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ConfigProto.Experimental.executor_type");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->executor_type(), target);
  }

  // int32 recv_buf_max_chunk = 4;
  if (this->recv_buf_max_chunk() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->recv_buf_max_chunk(), target);
  }

  // bool use_numa_affinity = 5;
  if (this->use_numa_affinity() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(5, this->use_numa_affinity(), target);
  }

  // bool collective_deterministic_sequential_execution = 6;
  if (this->collective_deterministic_sequential_execution() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(6, this->collective_deterministic_sequential_execution(), target);
  }

  // bool collective_nccl = 7;
  if (this->collective_nccl() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(7, this->collective_nccl(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.ConfigProto.Experimental)
  return target;
}

size_t ConfigProto_Experimental::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.ConfigProto.Experimental)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string collective_group_leader = 1;
  if (this->collective_group_leader().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->collective_group_leader());
  }

  // string executor_type = 3;
  if (this->executor_type().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->executor_type());
  }

  // int32 recv_buf_max_chunk = 4;
  if (this->recv_buf_max_chunk() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->recv_buf_max_chunk());
  }

  // bool use_numa_affinity = 5;
  if (this->use_numa_affinity() != 0) {
    total_size += 1 + 1;
  }

  // bool collective_deterministic_sequential_execution = 6;
  if (this->collective_deterministic_sequential_execution() != 0) {
    total_size += 1 + 1;
  }

  // bool collective_nccl = 7;
  if (this->collective_nccl() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ConfigProto_Experimental::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.ConfigProto.Experimental)
  GOOGLE_DCHECK_NE(&from, this);
  const ConfigProto_Experimental* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ConfigProto_Experimental>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.ConfigProto.Experimental)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.ConfigProto.Experimental)
    MergeFrom(*source);
  }
}

void ConfigProto_Experimental::MergeFrom(const ConfigProto_Experimental& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.ConfigProto.Experimental)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.collective_group_leader().size() > 0) {
    set_collective_group_leader(from.collective_group_leader());
  }
  if (from.executor_type().size() > 0) {
    set_executor_type(from.executor_type());
  }
  if (from.recv_buf_max_chunk() != 0) {
    set_recv_buf_max_chunk(from.recv_buf_max_chunk());
  }
  if (from.use_numa_affinity() != 0) {
    set_use_numa_affinity(from.use_numa_affinity());
  }
  if (from.collective_deterministic_sequential_execution() != 0) {
    set_collective_deterministic_sequential_execution(from.collective_deterministic_sequential_execution());
  }
  if (from.collective_nccl() != 0) {
    set_collective_nccl(from.collective_nccl());
  }
}

void ConfigProto_Experimental::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.ConfigProto.Experimental)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ConfigProto_Experimental::CopyFrom(const ConfigProto_Experimental& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.ConfigProto.Experimental)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ConfigProto_Experimental::IsInitialized() const {
  return true;
}

void ConfigProto_Experimental::Swap(ConfigProto_Experimental* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    ConfigProto_Experimental* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void ConfigProto_Experimental::UnsafeArenaSwap(ConfigProto_Experimental* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void ConfigProto_Experimental::InternalSwap(ConfigProto_Experimental* other) {
  using std::swap;
  collective_group_leader_.Swap(&other->collective_group_leader_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  executor_type_.Swap(&other->executor_type_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  swap(recv_buf_max_chunk_, other->recv_buf_max_chunk_);
  swap(use_numa_affinity_, other->use_numa_affinity_);
  swap(collective_deterministic_sequential_execution_, other->collective_deterministic_sequential_execution_);
  swap(collective_nccl_, other->collective_nccl_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ConfigProto_Experimental::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ConfigProto::InitAsDefaultInstance() {
  ::tensorflow::_ConfigProto_default_instance_._instance.get_mutable()->gpu_options_ = const_cast< ::tensorflow::GPUOptions*>(
      ::tensorflow::GPUOptions::internal_default_instance());
  ::tensorflow::_ConfigProto_default_instance_._instance.get_mutable()->graph_options_ = const_cast< ::tensorflow::GraphOptions*>(
      ::tensorflow::GraphOptions::internal_default_instance());
  ::tensorflow::_ConfigProto_default_instance_._instance.get_mutable()->rpc_options_ = const_cast< ::tensorflow::RPCOptions*>(
      ::tensorflow::RPCOptions::internal_default_instance());
  ::tensorflow::_ConfigProto_default_instance_._instance.get_mutable()->cluster_def_ = const_cast< ::tensorflow::ClusterDef*>(
      ::tensorflow::ClusterDef::internal_default_instance());
  ::tensorflow::_ConfigProto_default_instance_._instance.get_mutable()->experimental_ = const_cast< ::tensorflow::ConfigProto_Experimental*>(
      ::tensorflow::ConfigProto_Experimental::internal_default_instance());
}
void ConfigProto::unsafe_arena_set_allocated_gpu_options(
    ::tensorflow::GPUOptions* gpu_options) {
  if (GetArenaNoVirtual() == NULL) {
    delete gpu_options_;
  }
  gpu_options_ = gpu_options;
  if (gpu_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ConfigProto.gpu_options)
}
void ConfigProto::unsafe_arena_set_allocated_graph_options(
    ::tensorflow::GraphOptions* graph_options) {
  if (GetArenaNoVirtual() == NULL) {
    delete graph_options_;
  }
  graph_options_ = graph_options;
  if (graph_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ConfigProto.graph_options)
}
void ConfigProto::unsafe_arena_set_allocated_rpc_options(
    ::tensorflow::RPCOptions* rpc_options) {
  if (GetArenaNoVirtual() == NULL) {
    delete rpc_options_;
  }
  rpc_options_ = rpc_options;
  if (rpc_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ConfigProto.rpc_options)
}
void ConfigProto::unsafe_arena_set_allocated_cluster_def(
    ::tensorflow::ClusterDef* cluster_def) {
  if (GetArenaNoVirtual() == NULL) {
    delete cluster_def_;
  }
  cluster_def_ = cluster_def;
  if (cluster_def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ConfigProto.cluster_def)
}
void ConfigProto::clear_cluster_def() {
  if (GetArenaNoVirtual() == NULL && cluster_def_ != NULL) {
    delete cluster_def_;
  }
  cluster_def_ = NULL;
}
void ConfigProto::unsafe_arena_set_allocated_experimental(
    ::tensorflow::ConfigProto_Experimental* experimental) {
  if (GetArenaNoVirtual() == NULL) {
    delete experimental_;
  }
  experimental_ = experimental;
  if (experimental) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ConfigProto.experimental)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ConfigProto::kDeviceCountFieldNumber;
const int ConfigProto::kIntraOpParallelismThreadsFieldNumber;
const int ConfigProto::kInterOpParallelismThreadsFieldNumber;
const int ConfigProto::kUsePerSessionThreadsFieldNumber;
const int ConfigProto::kSessionInterOpThreadPoolFieldNumber;
const int ConfigProto::kPlacementPeriodFieldNumber;
const int ConfigProto::kDeviceFiltersFieldNumber;
const int ConfigProto::kGpuOptionsFieldNumber;
const int ConfigProto::kAllowSoftPlacementFieldNumber;
const int ConfigProto::kLogDevicePlacementFieldNumber;
const int ConfigProto::kGraphOptionsFieldNumber;
const int ConfigProto::kOperationTimeoutInMsFieldNumber;
const int ConfigProto::kRpcOptionsFieldNumber;
const int ConfigProto::kClusterDefFieldNumber;
const int ConfigProto::kIsolateSessionStateFieldNumber;
const int ConfigProto::kExperimentalFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ConfigProto::ConfigProto()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_ConfigProto.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.ConfigProto)
}
ConfigProto::ConfigProto(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  device_count_(arena),
  device_filters_(arena),
  session_inter_op_thread_pool_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_ConfigProto.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.ConfigProto)
}
ConfigProto::ConfigProto(const ConfigProto& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      device_filters_(from.device_filters_),
      session_inter_op_thread_pool_(from.session_inter_op_thread_pool_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  device_count_.MergeFrom(from.device_count_);
  if (from.has_gpu_options()) {
    gpu_options_ = new ::tensorflow::GPUOptions(*from.gpu_options_);
  } else {
    gpu_options_ = NULL;
  }
  if (from.has_graph_options()) {
    graph_options_ = new ::tensorflow::GraphOptions(*from.graph_options_);
  } else {
    graph_options_ = NULL;
  }
  if (from.has_rpc_options()) {
    rpc_options_ = new ::tensorflow::RPCOptions(*from.rpc_options_);
  } else {
    rpc_options_ = NULL;
  }
  if (from.has_cluster_def()) {
    cluster_def_ = new ::tensorflow::ClusterDef(*from.cluster_def_);
  } else {
    cluster_def_ = NULL;
  }
  if (from.has_experimental()) {
    experimental_ = new ::tensorflow::ConfigProto_Experimental(*from.experimental_);
  } else {
    experimental_ = NULL;
  }
  ::memcpy(&intra_op_parallelism_threads_, &from.intra_op_parallelism_threads_,
    static_cast<size_t>(reinterpret_cast<char*>(&operation_timeout_in_ms_) -
    reinterpret_cast<char*>(&intra_op_parallelism_threads_)) + sizeof(operation_timeout_in_ms_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.ConfigProto)
}

void ConfigProto::SharedCtor() {
  ::memset(&gpu_options_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&operation_timeout_in_ms_) -
      reinterpret_cast<char*>(&gpu_options_)) + sizeof(operation_timeout_in_ms_));
}

ConfigProto::~ConfigProto() {
  // @@protoc_insertion_point(destructor:tensorflow.ConfigProto)
  SharedDtor();
}

void ConfigProto::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (this != internal_default_instance()) delete gpu_options_;
  if (this != internal_default_instance()) delete graph_options_;
  if (this != internal_default_instance()) delete rpc_options_;
  if (this != internal_default_instance()) delete cluster_def_;
  if (this != internal_default_instance()) delete experimental_;
}

void ConfigProto::ArenaDtor(void* object) {
  ConfigProto* _this = reinterpret_cast< ConfigProto* >(object);
  (void)_this;
}
void ConfigProto::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void ConfigProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* ConfigProto::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ConfigProto& ConfigProto::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_ConfigProto.base);
  return *internal_default_instance();
}


void ConfigProto::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.ConfigProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  device_count_.Clear();
  device_filters_.Clear();
  session_inter_op_thread_pool_.Clear();
  if (GetArenaNoVirtual() == NULL && gpu_options_ != NULL) {
    delete gpu_options_;
  }
  gpu_options_ = NULL;
  if (GetArenaNoVirtual() == NULL && graph_options_ != NULL) {
    delete graph_options_;
  }
  graph_options_ = NULL;
  if (GetArenaNoVirtual() == NULL && rpc_options_ != NULL) {
    delete rpc_options_;
  }
  rpc_options_ = NULL;
  if (GetArenaNoVirtual() == NULL && cluster_def_ != NULL) {
    delete cluster_def_;
  }
  cluster_def_ = NULL;
  if (GetArenaNoVirtual() == NULL && experimental_ != NULL) {
    delete experimental_;
  }
  experimental_ = NULL;
  ::memset(&intra_op_parallelism_threads_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&operation_timeout_in_ms_) -
      reinterpret_cast<char*>(&intra_op_parallelism_threads_)) + sizeof(operation_timeout_in_ms_));
  _internal_metadata_.Clear();
}

bool ConfigProto::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.ConfigProto)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(16383u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<string, int32> device_count = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          ConfigProto_DeviceCountEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              ConfigProto_DeviceCountEntry_DoNotUse,
              ::std::string, ::google::protobuf::int32,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_INT32,
              0 >,
            ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 > > parser(&device_count_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ConfigProto.DeviceCountEntry.key"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 intra_op_parallelism_threads = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &intra_op_parallelism_threads_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 placement_period = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &placement_period_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string device_filters = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_device_filters()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->device_filters(this->device_filters_size() - 1).data(),
            static_cast<int>(this->device_filters(this->device_filters_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.ConfigProto.device_filters"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 inter_op_parallelism_threads = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &inter_op_parallelism_threads_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.GPUOptions gpu_options = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_gpu_options()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool allow_soft_placement = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(56u /* 56 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &allow_soft_placement_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool log_device_placement = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(64u /* 64 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &log_device_placement_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool use_per_session_threads = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(72u /* 72 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &use_per_session_threads_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.GraphOptions graph_options = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(82u /* 82 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_graph_options()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 operation_timeout_in_ms = 11;
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(88u /* 88 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &operation_timeout_in_ms_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.ThreadPoolOptionProto session_inter_op_thread_pool = 12;
      case 12: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(98u /* 98 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_session_inter_op_thread_pool()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.RPCOptions rpc_options = 13;
      case 13: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(106u /* 106 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_rpc_options()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.ClusterDef cluster_def = 14;
      case 14: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(114u /* 114 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_cluster_def()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool isolate_session_state = 15;
      case 15: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(120u /* 120 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &isolate_session_state_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.ConfigProto.Experimental experimental = 16;
      case 16: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(130u /* 130 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_experimental()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.ConfigProto)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.ConfigProto)
  return false;
#undef DO_
}

void ConfigProto::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.ConfigProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, int32> device_count = 1;
  if (!this->device_count().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.ConfigProto.DeviceCountEntry.key");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->device_count().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->device_count().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_iterator
          it = this->device_count().begin();
          it != this->device_count().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ConfigProto_DeviceCountEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(device_count_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<ConfigProto_DeviceCountEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_iterator
          it = this->device_count().begin();
          it != this->device_count().end(); ++it) {
        entry.reset(device_count_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // int32 intra_op_parallelism_threads = 2;
  if (this->intra_op_parallelism_threads() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->intra_op_parallelism_threads(), output);
  }

  // int32 placement_period = 3;
  if (this->placement_period() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->placement_period(), output);
  }

  // repeated string device_filters = 4;
  for (int i = 0, n = this->device_filters_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->device_filters(i).data(), static_cast<int>(this->device_filters(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ConfigProto.device_filters");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      4, this->device_filters(i), output);
  }

  // int32 inter_op_parallelism_threads = 5;
  if (this->inter_op_parallelism_threads() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(5, this->inter_op_parallelism_threads(), output);
  }

  // .tensorflow.GPUOptions gpu_options = 6;
  if (this->has_gpu_options()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, this->_internal_gpu_options(), output);
  }

  // bool allow_soft_placement = 7;
  if (this->allow_soft_placement() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(7, this->allow_soft_placement(), output);
  }

  // bool log_device_placement = 8;
  if (this->log_device_placement() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(8, this->log_device_placement(), output);
  }

  // bool use_per_session_threads = 9;
  if (this->use_per_session_threads() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(9, this->use_per_session_threads(), output);
  }

  // .tensorflow.GraphOptions graph_options = 10;
  if (this->has_graph_options()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      10, this->_internal_graph_options(), output);
  }

  // int64 operation_timeout_in_ms = 11;
  if (this->operation_timeout_in_ms() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->operation_timeout_in_ms(), output);
  }

  // repeated .tensorflow.ThreadPoolOptionProto session_inter_op_thread_pool = 12;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->session_inter_op_thread_pool_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      12,
      this->session_inter_op_thread_pool(static_cast<int>(i)),
      output);
  }

  // .tensorflow.RPCOptions rpc_options = 13;
  if (this->has_rpc_options()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      13, this->_internal_rpc_options(), output);
  }

  // .tensorflow.ClusterDef cluster_def = 14;
  if (this->has_cluster_def()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      14, this->_internal_cluster_def(), output);
  }

  // bool isolate_session_state = 15;
  if (this->isolate_session_state() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(15, this->isolate_session_state(), output);
  }

  // .tensorflow.ConfigProto.Experimental experimental = 16;
  if (this->has_experimental()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      16, this->_internal_experimental(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.ConfigProto)
}

::google::protobuf::uint8* ConfigProto::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.ConfigProto)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, int32> device_count = 1;
  if (!this->device_count().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.ConfigProto.DeviceCountEntry.key");
      }
    };

    if (deterministic &&
        this->device_count().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->device_count().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_iterator
          it = this->device_count().begin();
          it != this->device_count().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<ConfigProto_DeviceCountEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(device_count_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<ConfigProto_DeviceCountEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_iterator
          it = this->device_count().begin();
          it != this->device_count().end(); ++it) {
        entry.reset(device_count_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // int32 intra_op_parallelism_threads = 2;
  if (this->intra_op_parallelism_threads() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->intra_op_parallelism_threads(), target);
  }

  // int32 placement_period = 3;
  if (this->placement_period() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->placement_period(), target);
  }

  // repeated string device_filters = 4;
  for (int i = 0, n = this->device_filters_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->device_filters(i).data(), static_cast<int>(this->device_filters(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.ConfigProto.device_filters");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(4, this->device_filters(i), target);
  }

  // int32 inter_op_parallelism_threads = 5;
  if (this->inter_op_parallelism_threads() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(5, this->inter_op_parallelism_threads(), target);
  }

  // .tensorflow.GPUOptions gpu_options = 6;
  if (this->has_gpu_options()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        6, this->_internal_gpu_options(), deterministic, target);
  }

  // bool allow_soft_placement = 7;
  if (this->allow_soft_placement() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(7, this->allow_soft_placement(), target);
  }

  // bool log_device_placement = 8;
  if (this->log_device_placement() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(8, this->log_device_placement(), target);
  }

  // bool use_per_session_threads = 9;
  if (this->use_per_session_threads() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(9, this->use_per_session_threads(), target);
  }

  // .tensorflow.GraphOptions graph_options = 10;
  if (this->has_graph_options()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        10, this->_internal_graph_options(), deterministic, target);
  }

  // int64 operation_timeout_in_ms = 11;
  if (this->operation_timeout_in_ms() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->operation_timeout_in_ms(), target);
  }

  // repeated .tensorflow.ThreadPoolOptionProto session_inter_op_thread_pool = 12;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->session_inter_op_thread_pool_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        12, this->session_inter_op_thread_pool(static_cast<int>(i)), deterministic, target);
  }

  // .tensorflow.RPCOptions rpc_options = 13;
  if (this->has_rpc_options()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        13, this->_internal_rpc_options(), deterministic, target);
  }

  // .tensorflow.ClusterDef cluster_def = 14;
  if (this->has_cluster_def()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        14, this->_internal_cluster_def(), deterministic, target);
  }

  // bool isolate_session_state = 15;
  if (this->isolate_session_state() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(15, this->isolate_session_state(), target);
  }

  // .tensorflow.ConfigProto.Experimental experimental = 16;
  if (this->has_experimental()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        16, this->_internal_experimental(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.ConfigProto)
  return target;
}

size_t ConfigProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.ConfigProto)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // map<string, int32> device_count = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->device_count_size());
  {
    ::std::unique_ptr<ConfigProto_DeviceCountEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::google::protobuf::int32 >::const_iterator
        it = this->device_count().begin();
        it != this->device_count().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(device_count_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // repeated string device_filters = 4;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->device_filters_size());
  for (int i = 0, n = this->device_filters_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->device_filters(i));
  }

  // repeated .tensorflow.ThreadPoolOptionProto session_inter_op_thread_pool = 12;
  {
    unsigned int count = static_cast<unsigned int>(this->session_inter_op_thread_pool_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->session_inter_op_thread_pool(static_cast<int>(i)));
    }
  }

  // .tensorflow.GPUOptions gpu_options = 6;
  if (this->has_gpu_options()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *gpu_options_);
  }

  // .tensorflow.GraphOptions graph_options = 10;
  if (this->has_graph_options()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *graph_options_);
  }

  // .tensorflow.RPCOptions rpc_options = 13;
  if (this->has_rpc_options()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *rpc_options_);
  }

  // .tensorflow.ClusterDef cluster_def = 14;
  if (this->has_cluster_def()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *cluster_def_);
  }

  // .tensorflow.ConfigProto.Experimental experimental = 16;
  if (this->has_experimental()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *experimental_);
  }

  // int32 intra_op_parallelism_threads = 2;
  if (this->intra_op_parallelism_threads() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->intra_op_parallelism_threads());
  }

  // int32 placement_period = 3;
  if (this->placement_period() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->placement_period());
  }

  // int32 inter_op_parallelism_threads = 5;
  if (this->inter_op_parallelism_threads() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->inter_op_parallelism_threads());
  }

  // bool use_per_session_threads = 9;
  if (this->use_per_session_threads() != 0) {
    total_size += 1 + 1;
  }

  // bool allow_soft_placement = 7;
  if (this->allow_soft_placement() != 0) {
    total_size += 1 + 1;
  }

  // bool log_device_placement = 8;
  if (this->log_device_placement() != 0) {
    total_size += 1 + 1;
  }

  // bool isolate_session_state = 15;
  if (this->isolate_session_state() != 0) {
    total_size += 1 + 1;
  }

  // int64 operation_timeout_in_ms = 11;
  if (this->operation_timeout_in_ms() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->operation_timeout_in_ms());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ConfigProto::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.ConfigProto)
  GOOGLE_DCHECK_NE(&from, this);
  const ConfigProto* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ConfigProto>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.ConfigProto)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.ConfigProto)
    MergeFrom(*source);
  }
}

void ConfigProto::MergeFrom(const ConfigProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.ConfigProto)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  device_count_.MergeFrom(from.device_count_);
  device_filters_.MergeFrom(from.device_filters_);
  session_inter_op_thread_pool_.MergeFrom(from.session_inter_op_thread_pool_);
  if (from.has_gpu_options()) {
    mutable_gpu_options()->::tensorflow::GPUOptions::MergeFrom(from.gpu_options());
  }
  if (from.has_graph_options()) {
    mutable_graph_options()->::tensorflow::GraphOptions::MergeFrom(from.graph_options());
  }
  if (from.has_rpc_options()) {
    mutable_rpc_options()->::tensorflow::RPCOptions::MergeFrom(from.rpc_options());
  }
  if (from.has_cluster_def()) {
    mutable_cluster_def()->::tensorflow::ClusterDef::MergeFrom(from.cluster_def());
  }
  if (from.has_experimental()) {
    mutable_experimental()->::tensorflow::ConfigProto_Experimental::MergeFrom(from.experimental());
  }
  if (from.intra_op_parallelism_threads() != 0) {
    set_intra_op_parallelism_threads(from.intra_op_parallelism_threads());
  }
  if (from.placement_period() != 0) {
    set_placement_period(from.placement_period());
  }
  if (from.inter_op_parallelism_threads() != 0) {
    set_inter_op_parallelism_threads(from.inter_op_parallelism_threads());
  }
  if (from.use_per_session_threads() != 0) {
    set_use_per_session_threads(from.use_per_session_threads());
  }
  if (from.allow_soft_placement() != 0) {
    set_allow_soft_placement(from.allow_soft_placement());
  }
  if (from.log_device_placement() != 0) {
    set_log_device_placement(from.log_device_placement());
  }
  if (from.isolate_session_state() != 0) {
    set_isolate_session_state(from.isolate_session_state());
  }
  if (from.operation_timeout_in_ms() != 0) {
    set_operation_timeout_in_ms(from.operation_timeout_in_ms());
  }
}

void ConfigProto::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.ConfigProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ConfigProto::CopyFrom(const ConfigProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.ConfigProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ConfigProto::IsInitialized() const {
  return true;
}

void ConfigProto::Swap(ConfigProto* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    ConfigProto* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void ConfigProto::UnsafeArenaSwap(ConfigProto* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void ConfigProto::InternalSwap(ConfigProto* other) {
  using std::swap;
  device_count_.Swap(&other->device_count_);
  device_filters_.InternalSwap(CastToBase(&other->device_filters_));
  CastToBase(&session_inter_op_thread_pool_)->InternalSwap(CastToBase(&other->session_inter_op_thread_pool_));
  swap(gpu_options_, other->gpu_options_);
  swap(graph_options_, other->graph_options_);
  swap(rpc_options_, other->rpc_options_);
  swap(cluster_def_, other->cluster_def_);
  swap(experimental_, other->experimental_);
  swap(intra_op_parallelism_threads_, other->intra_op_parallelism_threads_);
  swap(placement_period_, other->placement_period_);
  swap(inter_op_parallelism_threads_, other->inter_op_parallelism_threads_);
  swap(use_per_session_threads_, other->use_per_session_threads_);
  swap(allow_soft_placement_, other->allow_soft_placement_);
  swap(log_device_placement_, other->log_device_placement_);
  swap(isolate_session_state_, other->isolate_session_state_);
  swap(operation_timeout_in_ms_, other->operation_timeout_in_ms_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata ConfigProto::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void RunOptions_Experimental::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RunOptions_Experimental::kCollectiveGraphKeyFieldNumber;
const int RunOptions_Experimental::kUseRunHandlerPoolFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RunOptions_Experimental::RunOptions_Experimental()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_RunOptions_Experimental.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.RunOptions.Experimental)
}
RunOptions_Experimental::RunOptions_Experimental(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_RunOptions_Experimental.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.RunOptions.Experimental)
}
RunOptions_Experimental::RunOptions_Experimental(const RunOptions_Experimental& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&collective_graph_key_, &from.collective_graph_key_,
    static_cast<size_t>(reinterpret_cast<char*>(&use_run_handler_pool_) -
    reinterpret_cast<char*>(&collective_graph_key_)) + sizeof(use_run_handler_pool_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.RunOptions.Experimental)
}

void RunOptions_Experimental::SharedCtor() {
  ::memset(&collective_graph_key_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&use_run_handler_pool_) -
      reinterpret_cast<char*>(&collective_graph_key_)) + sizeof(use_run_handler_pool_));
}

RunOptions_Experimental::~RunOptions_Experimental() {
  // @@protoc_insertion_point(destructor:tensorflow.RunOptions.Experimental)
  SharedDtor();
}

void RunOptions_Experimental::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
}

void RunOptions_Experimental::ArenaDtor(void* object) {
  RunOptions_Experimental* _this = reinterpret_cast< RunOptions_Experimental* >(object);
  (void)_this;
}
void RunOptions_Experimental::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void RunOptions_Experimental::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* RunOptions_Experimental::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const RunOptions_Experimental& RunOptions_Experimental::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_RunOptions_Experimental.base);
  return *internal_default_instance();
}


void RunOptions_Experimental::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.RunOptions.Experimental)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&collective_graph_key_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&use_run_handler_pool_) -
      reinterpret_cast<char*>(&collective_graph_key_)) + sizeof(use_run_handler_pool_));
  _internal_metadata_.Clear();
}

bool RunOptions_Experimental::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.RunOptions.Experimental)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 collective_graph_key = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &collective_graph_key_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool use_run_handler_pool = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &use_run_handler_pool_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.RunOptions.Experimental)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.RunOptions.Experimental)
  return false;
#undef DO_
}

void RunOptions_Experimental::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.RunOptions.Experimental)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 collective_graph_key = 1;
  if (this->collective_graph_key() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->collective_graph_key(), output);
  }

  // bool use_run_handler_pool = 2;
  if (this->use_run_handler_pool() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(2, this->use_run_handler_pool(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.RunOptions.Experimental)
}

::google::protobuf::uint8* RunOptions_Experimental::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.RunOptions.Experimental)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 collective_graph_key = 1;
  if (this->collective_graph_key() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->collective_graph_key(), target);
  }

  // bool use_run_handler_pool = 2;
  if (this->use_run_handler_pool() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(2, this->use_run_handler_pool(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.RunOptions.Experimental)
  return target;
}

size_t RunOptions_Experimental::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.RunOptions.Experimental)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int64 collective_graph_key = 1;
  if (this->collective_graph_key() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->collective_graph_key());
  }

  // bool use_run_handler_pool = 2;
  if (this->use_run_handler_pool() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RunOptions_Experimental::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.RunOptions.Experimental)
  GOOGLE_DCHECK_NE(&from, this);
  const RunOptions_Experimental* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RunOptions_Experimental>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.RunOptions.Experimental)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.RunOptions.Experimental)
    MergeFrom(*source);
  }
}

void RunOptions_Experimental::MergeFrom(const RunOptions_Experimental& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.RunOptions.Experimental)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.collective_graph_key() != 0) {
    set_collective_graph_key(from.collective_graph_key());
  }
  if (from.use_run_handler_pool() != 0) {
    set_use_run_handler_pool(from.use_run_handler_pool());
  }
}

void RunOptions_Experimental::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.RunOptions.Experimental)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RunOptions_Experimental::CopyFrom(const RunOptions_Experimental& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.RunOptions.Experimental)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RunOptions_Experimental::IsInitialized() const {
  return true;
}

void RunOptions_Experimental::Swap(RunOptions_Experimental* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    RunOptions_Experimental* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void RunOptions_Experimental::UnsafeArenaSwap(RunOptions_Experimental* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void RunOptions_Experimental::InternalSwap(RunOptions_Experimental* other) {
  using std::swap;
  swap(collective_graph_key_, other->collective_graph_key_);
  swap(use_run_handler_pool_, other->use_run_handler_pool_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata RunOptions_Experimental::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void RunOptions::InitAsDefaultInstance() {
  ::tensorflow::_RunOptions_default_instance_._instance.get_mutable()->debug_options_ = const_cast< ::tensorflow::DebugOptions*>(
      ::tensorflow::DebugOptions::internal_default_instance());
  ::tensorflow::_RunOptions_default_instance_._instance.get_mutable()->experimental_ = const_cast< ::tensorflow::RunOptions_Experimental*>(
      ::tensorflow::RunOptions_Experimental::internal_default_instance());
}
void RunOptions::unsafe_arena_set_allocated_debug_options(
    ::tensorflow::DebugOptions* debug_options) {
  if (GetArenaNoVirtual() == NULL) {
    delete debug_options_;
  }
  debug_options_ = debug_options;
  if (debug_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunOptions.debug_options)
}
void RunOptions::clear_debug_options() {
  if (GetArenaNoVirtual() == NULL && debug_options_ != NULL) {
    delete debug_options_;
  }
  debug_options_ = NULL;
}
void RunOptions::unsafe_arena_set_allocated_experimental(
    ::tensorflow::RunOptions_Experimental* experimental) {
  if (GetArenaNoVirtual() == NULL) {
    delete experimental_;
  }
  experimental_ = experimental;
  if (experimental) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunOptions.experimental)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RunOptions::kTraceLevelFieldNumber;
const int RunOptions::kTimeoutInMsFieldNumber;
const int RunOptions::kInterOpThreadPoolFieldNumber;
const int RunOptions::kOutputPartitionGraphsFieldNumber;
const int RunOptions::kDebugOptionsFieldNumber;
const int RunOptions::kReportTensorAllocationsUponOomFieldNumber;
const int RunOptions::kExperimentalFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RunOptions::RunOptions()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_RunOptions.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.RunOptions)
}
RunOptions::RunOptions(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_RunOptions.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.RunOptions)
}
RunOptions::RunOptions(const RunOptions& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_debug_options()) {
    debug_options_ = new ::tensorflow::DebugOptions(*from.debug_options_);
  } else {
    debug_options_ = NULL;
  }
  if (from.has_experimental()) {
    experimental_ = new ::tensorflow::RunOptions_Experimental(*from.experimental_);
  } else {
    experimental_ = NULL;
  }
  ::memcpy(&timeout_in_ms_, &from.timeout_in_ms_,
    static_cast<size_t>(reinterpret_cast<char*>(&report_tensor_allocations_upon_oom_) -
    reinterpret_cast<char*>(&timeout_in_ms_)) + sizeof(report_tensor_allocations_upon_oom_));
  // @@protoc_insertion_point(copy_constructor:tensorflow.RunOptions)
}

void RunOptions::SharedCtor() {
  ::memset(&debug_options_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&report_tensor_allocations_upon_oom_) -
      reinterpret_cast<char*>(&debug_options_)) + sizeof(report_tensor_allocations_upon_oom_));
}

RunOptions::~RunOptions() {
  // @@protoc_insertion_point(destructor:tensorflow.RunOptions)
  SharedDtor();
}

void RunOptions::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (this != internal_default_instance()) delete debug_options_;
  if (this != internal_default_instance()) delete experimental_;
}

void RunOptions::ArenaDtor(void* object) {
  RunOptions* _this = reinterpret_cast< RunOptions* >(object);
  (void)_this;
}
void RunOptions::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void RunOptions::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* RunOptions::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const RunOptions& RunOptions::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_RunOptions.base);
  return *internal_default_instance();
}


void RunOptions::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.RunOptions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && debug_options_ != NULL) {
    delete debug_options_;
  }
  debug_options_ = NULL;
  if (GetArenaNoVirtual() == NULL && experimental_ != NULL) {
    delete experimental_;
  }
  experimental_ = NULL;
  ::memset(&timeout_in_ms_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&report_tensor_allocations_upon_oom_) -
      reinterpret_cast<char*>(&timeout_in_ms_)) + sizeof(report_tensor_allocations_upon_oom_));
  _internal_metadata_.Clear();
}

bool RunOptions::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.RunOptions)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.RunOptions.TraceLevel trace_level = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_trace_level(static_cast< ::tensorflow::RunOptions_TraceLevel >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 timeout_in_ms = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &timeout_in_ms_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 inter_op_thread_pool = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &inter_op_thread_pool_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool output_partition_graphs = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &output_partition_graphs_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.DebugOptions debug_options = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_debug_options()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool report_tensor_allocations_upon_oom = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(56u /* 56 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &report_tensor_allocations_upon_oom_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.RunOptions.Experimental experimental = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(66u /* 66 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_experimental()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.RunOptions)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.RunOptions)
  return false;
#undef DO_
}

void RunOptions::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.RunOptions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.RunOptions.TraceLevel trace_level = 1;
  if (this->trace_level() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->trace_level(), output);
  }

  // int64 timeout_in_ms = 2;
  if (this->timeout_in_ms() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->timeout_in_ms(), output);
  }

  // int32 inter_op_thread_pool = 3;
  if (this->inter_op_thread_pool() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->inter_op_thread_pool(), output);
  }

  // bool output_partition_graphs = 5;
  if (this->output_partition_graphs() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(5, this->output_partition_graphs(), output);
  }

  // .tensorflow.DebugOptions debug_options = 6;
  if (this->has_debug_options()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, this->_internal_debug_options(), output);
  }

  // bool report_tensor_allocations_upon_oom = 7;
  if (this->report_tensor_allocations_upon_oom() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(7, this->report_tensor_allocations_upon_oom(), output);
  }

  // .tensorflow.RunOptions.Experimental experimental = 8;
  if (this->has_experimental()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8, this->_internal_experimental(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.RunOptions)
}

::google::protobuf::uint8* RunOptions::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.RunOptions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.RunOptions.TraceLevel trace_level = 1;
  if (this->trace_level() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->trace_level(), target);
  }

  // int64 timeout_in_ms = 2;
  if (this->timeout_in_ms() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->timeout_in_ms(), target);
  }

  // int32 inter_op_thread_pool = 3;
  if (this->inter_op_thread_pool() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->inter_op_thread_pool(), target);
  }

  // bool output_partition_graphs = 5;
  if (this->output_partition_graphs() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(5, this->output_partition_graphs(), target);
  }

  // .tensorflow.DebugOptions debug_options = 6;
  if (this->has_debug_options()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        6, this->_internal_debug_options(), deterministic, target);
  }

  // bool report_tensor_allocations_upon_oom = 7;
  if (this->report_tensor_allocations_upon_oom() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(7, this->report_tensor_allocations_upon_oom(), target);
  }

  // .tensorflow.RunOptions.Experimental experimental = 8;
  if (this->has_experimental()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        8, this->_internal_experimental(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.RunOptions)
  return target;
}

size_t RunOptions::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.RunOptions)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .tensorflow.DebugOptions debug_options = 6;
  if (this->has_debug_options()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *debug_options_);
  }

  // .tensorflow.RunOptions.Experimental experimental = 8;
  if (this->has_experimental()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *experimental_);
  }

  // int64 timeout_in_ms = 2;
  if (this->timeout_in_ms() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->timeout_in_ms());
  }

  // .tensorflow.RunOptions.TraceLevel trace_level = 1;
  if (this->trace_level() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->trace_level());
  }

  // int32 inter_op_thread_pool = 3;
  if (this->inter_op_thread_pool() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->inter_op_thread_pool());
  }

  // bool output_partition_graphs = 5;
  if (this->output_partition_graphs() != 0) {
    total_size += 1 + 1;
  }

  // bool report_tensor_allocations_upon_oom = 7;
  if (this->report_tensor_allocations_upon_oom() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RunOptions::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.RunOptions)
  GOOGLE_DCHECK_NE(&from, this);
  const RunOptions* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RunOptions>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.RunOptions)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.RunOptions)
    MergeFrom(*source);
  }
}

void RunOptions::MergeFrom(const RunOptions& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.RunOptions)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_debug_options()) {
    mutable_debug_options()->::tensorflow::DebugOptions::MergeFrom(from.debug_options());
  }
  if (from.has_experimental()) {
    mutable_experimental()->::tensorflow::RunOptions_Experimental::MergeFrom(from.experimental());
  }
  if (from.timeout_in_ms() != 0) {
    set_timeout_in_ms(from.timeout_in_ms());
  }
  if (from.trace_level() != 0) {
    set_trace_level(from.trace_level());
  }
  if (from.inter_op_thread_pool() != 0) {
    set_inter_op_thread_pool(from.inter_op_thread_pool());
  }
  if (from.output_partition_graphs() != 0) {
    set_output_partition_graphs(from.output_partition_graphs());
  }
  if (from.report_tensor_allocations_upon_oom() != 0) {
    set_report_tensor_allocations_upon_oom(from.report_tensor_allocations_upon_oom());
  }
}

void RunOptions::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.RunOptions)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RunOptions::CopyFrom(const RunOptions& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.RunOptions)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RunOptions::IsInitialized() const {
  return true;
}

void RunOptions::Swap(RunOptions* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    RunOptions* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void RunOptions::UnsafeArenaSwap(RunOptions* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void RunOptions::InternalSwap(RunOptions* other) {
  using std::swap;
  swap(debug_options_, other->debug_options_);
  swap(experimental_, other->experimental_);
  swap(timeout_in_ms_, other->timeout_in_ms_);
  swap(trace_level_, other->trace_level_);
  swap(inter_op_thread_pool_, other->inter_op_thread_pool_);
  swap(output_partition_graphs_, other->output_partition_graphs_);
  swap(report_tensor_allocations_upon_oom_, other->report_tensor_allocations_upon_oom_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata RunOptions::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void RunMetadata_FunctionGraphs::InitAsDefaultInstance() {
  ::tensorflow::_RunMetadata_FunctionGraphs_default_instance_._instance.get_mutable()->pre_optimization_graph_ = const_cast< ::tensorflow::GraphDef*>(
      ::tensorflow::GraphDef::internal_default_instance());
  ::tensorflow::_RunMetadata_FunctionGraphs_default_instance_._instance.get_mutable()->post_optimization_graph_ = const_cast< ::tensorflow::GraphDef*>(
      ::tensorflow::GraphDef::internal_default_instance());
}
void RunMetadata_FunctionGraphs::clear_partition_graphs() {
  partition_graphs_.Clear();
}
void RunMetadata_FunctionGraphs::unsafe_arena_set_allocated_pre_optimization_graph(
    ::tensorflow::GraphDef* pre_optimization_graph) {
  if (GetArenaNoVirtual() == NULL) {
    delete pre_optimization_graph_;
  }
  pre_optimization_graph_ = pre_optimization_graph;
  if (pre_optimization_graph) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunMetadata.FunctionGraphs.pre_optimization_graph)
}
void RunMetadata_FunctionGraphs::clear_pre_optimization_graph() {
  if (GetArenaNoVirtual() == NULL && pre_optimization_graph_ != NULL) {
    delete pre_optimization_graph_;
  }
  pre_optimization_graph_ = NULL;
}
void RunMetadata_FunctionGraphs::unsafe_arena_set_allocated_post_optimization_graph(
    ::tensorflow::GraphDef* post_optimization_graph) {
  if (GetArenaNoVirtual() == NULL) {
    delete post_optimization_graph_;
  }
  post_optimization_graph_ = post_optimization_graph;
  if (post_optimization_graph) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunMetadata.FunctionGraphs.post_optimization_graph)
}
void RunMetadata_FunctionGraphs::clear_post_optimization_graph() {
  if (GetArenaNoVirtual() == NULL && post_optimization_graph_ != NULL) {
    delete post_optimization_graph_;
  }
  post_optimization_graph_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RunMetadata_FunctionGraphs::kPartitionGraphsFieldNumber;
const int RunMetadata_FunctionGraphs::kPreOptimizationGraphFieldNumber;
const int RunMetadata_FunctionGraphs::kPostOptimizationGraphFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RunMetadata_FunctionGraphs::RunMetadata_FunctionGraphs()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_RunMetadata_FunctionGraphs.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.RunMetadata.FunctionGraphs)
}
RunMetadata_FunctionGraphs::RunMetadata_FunctionGraphs(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  partition_graphs_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_RunMetadata_FunctionGraphs.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.RunMetadata.FunctionGraphs)
}
RunMetadata_FunctionGraphs::RunMetadata_FunctionGraphs(const RunMetadata_FunctionGraphs& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      partition_graphs_(from.partition_graphs_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_pre_optimization_graph()) {
    pre_optimization_graph_ = new ::tensorflow::GraphDef(*from.pre_optimization_graph_);
  } else {
    pre_optimization_graph_ = NULL;
  }
  if (from.has_post_optimization_graph()) {
    post_optimization_graph_ = new ::tensorflow::GraphDef(*from.post_optimization_graph_);
  } else {
    post_optimization_graph_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.RunMetadata.FunctionGraphs)
}

void RunMetadata_FunctionGraphs::SharedCtor() {
  ::memset(&pre_optimization_graph_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&post_optimization_graph_) -
      reinterpret_cast<char*>(&pre_optimization_graph_)) + sizeof(post_optimization_graph_));
}

RunMetadata_FunctionGraphs::~RunMetadata_FunctionGraphs() {
  // @@protoc_insertion_point(destructor:tensorflow.RunMetadata.FunctionGraphs)
  SharedDtor();
}

void RunMetadata_FunctionGraphs::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (this != internal_default_instance()) delete pre_optimization_graph_;
  if (this != internal_default_instance()) delete post_optimization_graph_;
}

void RunMetadata_FunctionGraphs::ArenaDtor(void* object) {
  RunMetadata_FunctionGraphs* _this = reinterpret_cast< RunMetadata_FunctionGraphs* >(object);
  (void)_this;
}
void RunMetadata_FunctionGraphs::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void RunMetadata_FunctionGraphs::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* RunMetadata_FunctionGraphs::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const RunMetadata_FunctionGraphs& RunMetadata_FunctionGraphs::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_RunMetadata_FunctionGraphs.base);
  return *internal_default_instance();
}


void RunMetadata_FunctionGraphs::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.RunMetadata.FunctionGraphs)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  partition_graphs_.Clear();
  if (GetArenaNoVirtual() == NULL && pre_optimization_graph_ != NULL) {
    delete pre_optimization_graph_;
  }
  pre_optimization_graph_ = NULL;
  if (GetArenaNoVirtual() == NULL && post_optimization_graph_ != NULL) {
    delete post_optimization_graph_;
  }
  post_optimization_graph_ = NULL;
  _internal_metadata_.Clear();
}

bool RunMetadata_FunctionGraphs::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.RunMetadata.FunctionGraphs)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .tensorflow.GraphDef partition_graphs = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_partition_graphs()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.GraphDef pre_optimization_graph = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_pre_optimization_graph()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.GraphDef post_optimization_graph = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_post_optimization_graph()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.RunMetadata.FunctionGraphs)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.RunMetadata.FunctionGraphs)
  return false;
#undef DO_
}

void RunMetadata_FunctionGraphs::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.RunMetadata.FunctionGraphs)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.GraphDef partition_graphs = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->partition_graphs_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1,
      this->partition_graphs(static_cast<int>(i)),
      output);
  }

  // .tensorflow.GraphDef pre_optimization_graph = 2;
  if (this->has_pre_optimization_graph()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_pre_optimization_graph(), output);
  }

  // .tensorflow.GraphDef post_optimization_graph = 3;
  if (this->has_post_optimization_graph()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->_internal_post_optimization_graph(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.RunMetadata.FunctionGraphs)
}

::google::protobuf::uint8* RunMetadata_FunctionGraphs::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.RunMetadata.FunctionGraphs)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .tensorflow.GraphDef partition_graphs = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->partition_graphs_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->partition_graphs(static_cast<int>(i)), deterministic, target);
  }

  // .tensorflow.GraphDef pre_optimization_graph = 2;
  if (this->has_pre_optimization_graph()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_pre_optimization_graph(), deterministic, target);
  }

  // .tensorflow.GraphDef post_optimization_graph = 3;
  if (this->has_post_optimization_graph()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->_internal_post_optimization_graph(), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.RunMetadata.FunctionGraphs)
  return target;
}

size_t RunMetadata_FunctionGraphs::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.RunMetadata.FunctionGraphs)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.GraphDef partition_graphs = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->partition_graphs_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->partition_graphs(static_cast<int>(i)));
    }
  }

  // .tensorflow.GraphDef pre_optimization_graph = 2;
  if (this->has_pre_optimization_graph()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *pre_optimization_graph_);
  }

  // .tensorflow.GraphDef post_optimization_graph = 3;
  if (this->has_post_optimization_graph()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *post_optimization_graph_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RunMetadata_FunctionGraphs::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.RunMetadata.FunctionGraphs)
  GOOGLE_DCHECK_NE(&from, this);
  const RunMetadata_FunctionGraphs* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RunMetadata_FunctionGraphs>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.RunMetadata.FunctionGraphs)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.RunMetadata.FunctionGraphs)
    MergeFrom(*source);
  }
}

void RunMetadata_FunctionGraphs::MergeFrom(const RunMetadata_FunctionGraphs& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.RunMetadata.FunctionGraphs)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  partition_graphs_.MergeFrom(from.partition_graphs_);
  if (from.has_pre_optimization_graph()) {
    mutable_pre_optimization_graph()->::tensorflow::GraphDef::MergeFrom(from.pre_optimization_graph());
  }
  if (from.has_post_optimization_graph()) {
    mutable_post_optimization_graph()->::tensorflow::GraphDef::MergeFrom(from.post_optimization_graph());
  }
}

void RunMetadata_FunctionGraphs::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.RunMetadata.FunctionGraphs)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RunMetadata_FunctionGraphs::CopyFrom(const RunMetadata_FunctionGraphs& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.RunMetadata.FunctionGraphs)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RunMetadata_FunctionGraphs::IsInitialized() const {
  return true;
}

void RunMetadata_FunctionGraphs::Swap(RunMetadata_FunctionGraphs* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    RunMetadata_FunctionGraphs* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void RunMetadata_FunctionGraphs::UnsafeArenaSwap(RunMetadata_FunctionGraphs* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void RunMetadata_FunctionGraphs::InternalSwap(RunMetadata_FunctionGraphs* other) {
  using std::swap;
  CastToBase(&partition_graphs_)->InternalSwap(CastToBase(&other->partition_graphs_));
  swap(pre_optimization_graph_, other->pre_optimization_graph_);
  swap(post_optimization_graph_, other->post_optimization_graph_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata RunMetadata_FunctionGraphs::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void RunMetadata::InitAsDefaultInstance() {
  ::tensorflow::_RunMetadata_default_instance_._instance.get_mutable()->step_stats_ = const_cast< ::tensorflow::StepStats*>(
      ::tensorflow::StepStats::internal_default_instance());
  ::tensorflow::_RunMetadata_default_instance_._instance.get_mutable()->cost_graph_ = const_cast< ::tensorflow::CostGraphDef*>(
      ::tensorflow::CostGraphDef::internal_default_instance());
}
void RunMetadata::unsafe_arena_set_allocated_step_stats(
    ::tensorflow::StepStats* step_stats) {
  if (GetArenaNoVirtual() == NULL) {
    delete step_stats_;
  }
  step_stats_ = step_stats;
  if (step_stats) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunMetadata.step_stats)
}
void RunMetadata::clear_step_stats() {
  if (GetArenaNoVirtual() == NULL && step_stats_ != NULL) {
    delete step_stats_;
  }
  step_stats_ = NULL;
}
void RunMetadata::unsafe_arena_set_allocated_cost_graph(
    ::tensorflow::CostGraphDef* cost_graph) {
  if (GetArenaNoVirtual() == NULL) {
    delete cost_graph_;
  }
  cost_graph_ = cost_graph;
  if (cost_graph) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunMetadata.cost_graph)
}
void RunMetadata::clear_cost_graph() {
  if (GetArenaNoVirtual() == NULL && cost_graph_ != NULL) {
    delete cost_graph_;
  }
  cost_graph_ = NULL;
}
void RunMetadata::clear_partition_graphs() {
  partition_graphs_.Clear();
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RunMetadata::kStepStatsFieldNumber;
const int RunMetadata::kCostGraphFieldNumber;
const int RunMetadata::kPartitionGraphsFieldNumber;
const int RunMetadata::kFunctionGraphsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RunMetadata::RunMetadata()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_RunMetadata.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.RunMetadata)
}
RunMetadata::RunMetadata(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  partition_graphs_(arena),
  function_graphs_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_RunMetadata.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.RunMetadata)
}
RunMetadata::RunMetadata(const RunMetadata& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      partition_graphs_(from.partition_graphs_),
      function_graphs_(from.function_graphs_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_step_stats()) {
    step_stats_ = new ::tensorflow::StepStats(*from.step_stats_);
  } else {
    step_stats_ = NULL;
  }
  if (from.has_cost_graph()) {
    cost_graph_ = new ::tensorflow::CostGraphDef(*from.cost_graph_);
  } else {
    cost_graph_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.RunMetadata)
}

void RunMetadata::SharedCtor() {
  ::memset(&step_stats_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&cost_graph_) -
      reinterpret_cast<char*>(&step_stats_)) + sizeof(cost_graph_));
}

RunMetadata::~RunMetadata() {
  // @@protoc_insertion_point(destructor:tensorflow.RunMetadata)
  SharedDtor();
}

void RunMetadata::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (this != internal_default_instance()) delete step_stats_;
  if (this != internal_default_instance()) delete cost_graph_;
}

void RunMetadata::ArenaDtor(void* object) {
  RunMetadata* _this = reinterpret_cast< RunMetadata* >(object);
  (void)_this;
}
void RunMetadata::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void RunMetadata::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* RunMetadata::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const RunMetadata& RunMetadata::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_RunMetadata.base);
  return *internal_default_instance();
}


void RunMetadata::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.RunMetadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  partition_graphs_.Clear();
  function_graphs_.Clear();
  if (GetArenaNoVirtual() == NULL && step_stats_ != NULL) {
    delete step_stats_;
  }
  step_stats_ = NULL;
  if (GetArenaNoVirtual() == NULL && cost_graph_ != NULL) {
    delete cost_graph_;
  }
  cost_graph_ = NULL;
  _internal_metadata_.Clear();
}

bool RunMetadata::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.RunMetadata)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .tensorflow.StepStats step_stats = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_step_stats()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.CostGraphDef cost_graph = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_cost_graph()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.GraphDef partition_graphs = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_partition_graphs()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.RunMetadata.FunctionGraphs function_graphs = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_function_graphs()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.RunMetadata)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.RunMetadata)
  return false;
#undef DO_
}

void RunMetadata::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.RunMetadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.StepStats step_stats = 1;
  if (this->has_step_stats()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->_internal_step_stats(), output);
  }

  // .tensorflow.CostGraphDef cost_graph = 2;
  if (this->has_cost_graph()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->_internal_cost_graph(), output);
  }

  // repeated .tensorflow.GraphDef partition_graphs = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->partition_graphs_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3,
      this->partition_graphs(static_cast<int>(i)),
      output);
  }

  // repeated .tensorflow.RunMetadata.FunctionGraphs function_graphs = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->function_graphs_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4,
      this->function_graphs(static_cast<int>(i)),
      output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.RunMetadata)
}

::google::protobuf::uint8* RunMetadata::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.RunMetadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .tensorflow.StepStats step_stats = 1;
  if (this->has_step_stats()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->_internal_step_stats(), deterministic, target);
  }

  // .tensorflow.CostGraphDef cost_graph = 2;
  if (this->has_cost_graph()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->_internal_cost_graph(), deterministic, target);
  }

  // repeated .tensorflow.GraphDef partition_graphs = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->partition_graphs_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->partition_graphs(static_cast<int>(i)), deterministic, target);
  }

  // repeated .tensorflow.RunMetadata.FunctionGraphs function_graphs = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->function_graphs_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->function_graphs(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.RunMetadata)
  return target;
}

size_t RunMetadata::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.RunMetadata)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .tensorflow.GraphDef partition_graphs = 3;
  {
    unsigned int count = static_cast<unsigned int>(this->partition_graphs_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->partition_graphs(static_cast<int>(i)));
    }
  }

  // repeated .tensorflow.RunMetadata.FunctionGraphs function_graphs = 4;
  {
    unsigned int count = static_cast<unsigned int>(this->function_graphs_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->function_graphs(static_cast<int>(i)));
    }
  }

  // .tensorflow.StepStats step_stats = 1;
  if (this->has_step_stats()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *step_stats_);
  }

  // .tensorflow.CostGraphDef cost_graph = 2;
  if (this->has_cost_graph()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *cost_graph_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RunMetadata::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.RunMetadata)
  GOOGLE_DCHECK_NE(&from, this);
  const RunMetadata* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RunMetadata>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.RunMetadata)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.RunMetadata)
    MergeFrom(*source);
  }
}

void RunMetadata::MergeFrom(const RunMetadata& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.RunMetadata)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  partition_graphs_.MergeFrom(from.partition_graphs_);
  function_graphs_.MergeFrom(from.function_graphs_);
  if (from.has_step_stats()) {
    mutable_step_stats()->::tensorflow::StepStats::MergeFrom(from.step_stats());
  }
  if (from.has_cost_graph()) {
    mutable_cost_graph()->::tensorflow::CostGraphDef::MergeFrom(from.cost_graph());
  }
}

void RunMetadata::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.RunMetadata)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RunMetadata::CopyFrom(const RunMetadata& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.RunMetadata)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RunMetadata::IsInitialized() const {
  return true;
}

void RunMetadata::Swap(RunMetadata* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    RunMetadata* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void RunMetadata::UnsafeArenaSwap(RunMetadata* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void RunMetadata::InternalSwap(RunMetadata* other) {
  using std::swap;
  CastToBase(&partition_graphs_)->InternalSwap(CastToBase(&other->partition_graphs_));
  CastToBase(&function_graphs_)->InternalSwap(CastToBase(&other->function_graphs_));
  swap(step_stats_, other->step_stats_);
  swap(cost_graph_, other->cost_graph_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata RunMetadata::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void TensorConnection::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int TensorConnection::kFromTensorFieldNumber;
const int TensorConnection::kToTensorFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

TensorConnection::TensorConnection()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_TensorConnection.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.TensorConnection)
}
TensorConnection::TensorConnection(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_TensorConnection.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.TensorConnection)
}
TensorConnection::TensorConnection(const TensorConnection& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  from_tensor_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.from_tensor().size() > 0) {
    from_tensor_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.from_tensor(),
      GetArenaNoVirtual());
  }
  to_tensor_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.to_tensor().size() > 0) {
    to_tensor_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.to_tensor(),
      GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(copy_constructor:tensorflow.TensorConnection)
}

void TensorConnection::SharedCtor() {
  from_tensor_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  to_tensor_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

TensorConnection::~TensorConnection() {
  // @@protoc_insertion_point(destructor:tensorflow.TensorConnection)
  SharedDtor();
}

void TensorConnection::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  from_tensor_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  to_tensor_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void TensorConnection::ArenaDtor(void* object) {
  TensorConnection* _this = reinterpret_cast< TensorConnection* >(object);
  (void)_this;
}
void TensorConnection::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void TensorConnection::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* TensorConnection::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const TensorConnection& TensorConnection::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_TensorConnection.base);
  return *internal_default_instance();
}


void TensorConnection::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.TensorConnection)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  from_tensor_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  to_tensor_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
  _internal_metadata_.Clear();
}

bool TensorConnection::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.TensorConnection)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string from_tensor = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_from_tensor()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->from_tensor().data(), static_cast<int>(this->from_tensor().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.TensorConnection.from_tensor"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string to_tensor = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_to_tensor()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->to_tensor().data(), static_cast<int>(this->to_tensor().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.TensorConnection.to_tensor"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.TensorConnection)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.TensorConnection)
  return false;
#undef DO_
}

void TensorConnection::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.TensorConnection)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string from_tensor = 1;
  if (this->from_tensor().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->from_tensor().data(), static_cast<int>(this->from_tensor().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TensorConnection.from_tensor");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->from_tensor(), output);
  }

  // string to_tensor = 2;
  if (this->to_tensor().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->to_tensor().data(), static_cast<int>(this->to_tensor().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TensorConnection.to_tensor");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->to_tensor(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.TensorConnection)
}

::google::protobuf::uint8* TensorConnection::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.TensorConnection)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string from_tensor = 1;
  if (this->from_tensor().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->from_tensor().data(), static_cast<int>(this->from_tensor().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TensorConnection.from_tensor");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->from_tensor(), target);
  }

  // string to_tensor = 2;
  if (this->to_tensor().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->to_tensor().data(), static_cast<int>(this->to_tensor().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.TensorConnection.to_tensor");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->to_tensor(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.TensorConnection)
  return target;
}

size_t TensorConnection::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.TensorConnection)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string from_tensor = 1;
  if (this->from_tensor().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->from_tensor());
  }

  // string to_tensor = 2;
  if (this->to_tensor().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->to_tensor());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TensorConnection::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.TensorConnection)
  GOOGLE_DCHECK_NE(&from, this);
  const TensorConnection* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const TensorConnection>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.TensorConnection)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.TensorConnection)
    MergeFrom(*source);
  }
}

void TensorConnection::MergeFrom(const TensorConnection& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.TensorConnection)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.from_tensor().size() > 0) {
    set_from_tensor(from.from_tensor());
  }
  if (from.to_tensor().size() > 0) {
    set_to_tensor(from.to_tensor());
  }
}

void TensorConnection::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.TensorConnection)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TensorConnection::CopyFrom(const TensorConnection& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.TensorConnection)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TensorConnection::IsInitialized() const {
  return true;
}

void TensorConnection::Swap(TensorConnection* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    TensorConnection* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void TensorConnection::UnsafeArenaSwap(TensorConnection* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void TensorConnection::InternalSwap(TensorConnection* other) {
  using std::swap;
  from_tensor_.Swap(&other->from_tensor_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  to_tensor_.Swap(&other->to_tensor_, &::google::protobuf::internal::GetEmptyStringAlreadyInited(),
    GetArenaNoVirtual());
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata TensorConnection::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

CallableOptions_FeedDevicesEntry_DoNotUse::CallableOptions_FeedDevicesEntry_DoNotUse() {}
CallableOptions_FeedDevicesEntry_DoNotUse::CallableOptions_FeedDevicesEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void CallableOptions_FeedDevicesEntry_DoNotUse::MergeFrom(const CallableOptions_FeedDevicesEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata CallableOptions_FeedDevicesEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[15];
}
void CallableOptions_FeedDevicesEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

CallableOptions_FetchDevicesEntry_DoNotUse::CallableOptions_FetchDevicesEntry_DoNotUse() {}
CallableOptions_FetchDevicesEntry_DoNotUse::CallableOptions_FetchDevicesEntry_DoNotUse(::google::protobuf::Arena* arena) : SuperType(arena) {}
void CallableOptions_FetchDevicesEntry_DoNotUse::MergeFrom(const CallableOptions_FetchDevicesEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::google::protobuf::Metadata CallableOptions_FetchDevicesEntry_DoNotUse::GetMetadata() const {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[16];
}
void CallableOptions_FetchDevicesEntry_DoNotUse::MergeFrom(
    const ::google::protobuf::Message& other) {
  ::google::protobuf::Message::MergeFrom(other);
}


// ===================================================================

void CallableOptions::InitAsDefaultInstance() {
  ::tensorflow::_CallableOptions_default_instance_._instance.get_mutable()->run_options_ = const_cast< ::tensorflow::RunOptions*>(
      ::tensorflow::RunOptions::internal_default_instance());
}
void CallableOptions::unsafe_arena_set_allocated_run_options(
    ::tensorflow::RunOptions* run_options) {
  if (GetArenaNoVirtual() == NULL) {
    delete run_options_;
  }
  run_options_ = run_options;
  if (run_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CallableOptions.run_options)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CallableOptions::kFeedFieldNumber;
const int CallableOptions::kFetchFieldNumber;
const int CallableOptions::kTargetFieldNumber;
const int CallableOptions::kRunOptionsFieldNumber;
const int CallableOptions::kTensorConnectionFieldNumber;
const int CallableOptions::kFeedDevicesFieldNumber;
const int CallableOptions::kFetchDevicesFieldNumber;
const int CallableOptions::kFetchSkipSyncFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CallableOptions::CallableOptions()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_CallableOptions.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.CallableOptions)
}
CallableOptions::CallableOptions(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena),
  feed_(arena),
  fetch_(arena),
  target_(arena),
  tensor_connection_(arena),
  feed_devices_(arena),
  fetch_devices_(arena) {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_CallableOptions.base);
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:tensorflow.CallableOptions)
}
CallableOptions::CallableOptions(const CallableOptions& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      feed_(from.feed_),
      fetch_(from.fetch_),
      target_(from.target_),
      tensor_connection_(from.tensor_connection_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  feed_devices_.MergeFrom(from.feed_devices_);
  fetch_devices_.MergeFrom(from.fetch_devices_);
  if (from.has_run_options()) {
    run_options_ = new ::tensorflow::RunOptions(*from.run_options_);
  } else {
    run_options_ = NULL;
  }
  fetch_skip_sync_ = from.fetch_skip_sync_;
  // @@protoc_insertion_point(copy_constructor:tensorflow.CallableOptions)
}

void CallableOptions::SharedCtor() {
  ::memset(&run_options_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&fetch_skip_sync_) -
      reinterpret_cast<char*>(&run_options_)) + sizeof(fetch_skip_sync_));
}

CallableOptions::~CallableOptions() {
  // @@protoc_insertion_point(destructor:tensorflow.CallableOptions)
  SharedDtor();
}

void CallableOptions::SharedDtor() {
  GOOGLE_DCHECK(GetArenaNoVirtual() == NULL);
  if (this != internal_default_instance()) delete run_options_;
}

void CallableOptions::ArenaDtor(void* object) {
  CallableOptions* _this = reinterpret_cast< CallableOptions* >(object);
  (void)_this;
}
void CallableOptions::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void CallableOptions::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* CallableOptions::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CallableOptions& CallableOptions::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::scc_info_CallableOptions.base);
  return *internal_default_instance();
}


void CallableOptions::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.CallableOptions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  feed_.Clear();
  fetch_.Clear();
  target_.Clear();
  tensor_connection_.Clear();
  feed_devices_.Clear();
  fetch_devices_.Clear();
  if (GetArenaNoVirtual() == NULL && run_options_ != NULL) {
    delete run_options_;
  }
  run_options_ = NULL;
  fetch_skip_sync_ = false;
  _internal_metadata_.Clear();
}

bool CallableOptions::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.CallableOptions)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated string feed = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_feed()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->feed(this->feed_size() - 1).data(),
            static_cast<int>(this->feed(this->feed_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.CallableOptions.feed"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string fetch = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_fetch()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->fetch(this->fetch_size() - 1).data(),
            static_cast<int>(this->fetch(this->fetch_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.CallableOptions.fetch"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string target = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_target()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->target(this->target_size() - 1).data(),
            static_cast<int>(this->target(this->target_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.CallableOptions.target"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .tensorflow.RunOptions run_options = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_run_options()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .tensorflow.TensorConnection tensor_connection = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
                input, add_tensor_connection()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, string> feed_devices = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          CallableOptions_FeedDevicesEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              CallableOptions_FeedDevicesEntry_DoNotUse,
              ::std::string, ::std::string,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              0 >,
            ::google::protobuf::Map< ::std::string, ::std::string > > parser(&feed_devices_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.CallableOptions.FeedDevicesEntry.key"));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.value().data(), static_cast<int>(parser.value().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.CallableOptions.FeedDevicesEntry.value"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // map<string, string> fetch_devices = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          CallableOptions_FetchDevicesEntry_DoNotUse::Parser< ::google::protobuf::internal::MapField<
              CallableOptions_FetchDevicesEntry_DoNotUse,
              ::std::string, ::std::string,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
              0 >,
            ::google::protobuf::Map< ::std::string, ::std::string > > parser(&fetch_devices_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.key().data(), static_cast<int>(parser.key().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.CallableOptions.FetchDevicesEntry.key"));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            parser.value().data(), static_cast<int>(parser.value().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "tensorflow.CallableOptions.FetchDevicesEntry.value"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool fetch_skip_sync = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(64u /* 64 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &fetch_skip_sync_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.CallableOptions)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.CallableOptions)
  return false;
#undef DO_
}

void CallableOptions::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.CallableOptions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string feed = 1;
  for (int i = 0, n = this->feed_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->feed(i).data(), static_cast<int>(this->feed(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CallableOptions.feed");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->feed(i), output);
  }

  // repeated string fetch = 2;
  for (int i = 0, n = this->fetch_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fetch(i).data(), static_cast<int>(this->fetch(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CallableOptions.fetch");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->fetch(i), output);
  }

  // repeated string target = 3;
  for (int i = 0, n = this->target_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->target(i).data(), static_cast<int>(this->target(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CallableOptions.target");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      3, this->target(i), output);
  }

  // .tensorflow.RunOptions run_options = 4;
  if (this->has_run_options()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->_internal_run_options(), output);
  }

  // repeated .tensorflow.TensorConnection tensor_connection = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->tensor_connection_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5,
      this->tensor_connection(static_cast<int>(i)),
      output);
  }

  // map<string, string> feed_devices = 6;
  if (!this->feed_devices().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.CallableOptions.FeedDevicesEntry.key");
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.CallableOptions.FeedDevicesEntry.value");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->feed_devices().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->feed_devices().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->feed_devices().begin();
          it != this->feed_devices().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<CallableOptions_FeedDevicesEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(feed_devices_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            6, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<CallableOptions_FeedDevicesEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->feed_devices().begin();
          it != this->feed_devices().end(); ++it) {
        entry.reset(feed_devices_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            6, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<string, string> fetch_devices = 7;
  if (!this->fetch_devices().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.CallableOptions.FetchDevicesEntry.key");
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.CallableOptions.FetchDevicesEntry.value");
      }
    };

    if (output->IsSerializationDeterministic() &&
        this->fetch_devices().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->fetch_devices().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->fetch_devices().begin();
          it != this->fetch_devices().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<CallableOptions_FetchDevicesEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(fetch_devices_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            7, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<CallableOptions_FetchDevicesEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->fetch_devices().begin();
          it != this->fetch_devices().end(); ++it) {
        entry.reset(fetch_devices_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            7, *entry, output);
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // bool fetch_skip_sync = 8;
  if (this->fetch_skip_sync() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(8, this->fetch_skip_sync(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.CallableOptions)
}

::google::protobuf::uint8* CallableOptions::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.CallableOptions)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string feed = 1;
  for (int i = 0, n = this->feed_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->feed(i).data(), static_cast<int>(this->feed(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CallableOptions.feed");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(1, this->feed(i), target);
  }

  // repeated string fetch = 2;
  for (int i = 0, n = this->fetch_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fetch(i).data(), static_cast<int>(this->fetch(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CallableOptions.fetch");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(2, this->fetch(i), target);
  }

  // repeated string target = 3;
  for (int i = 0, n = this->target_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->target(i).data(), static_cast<int>(this->target(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "tensorflow.CallableOptions.target");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(3, this->target(i), target);
  }

  // .tensorflow.RunOptions run_options = 4;
  if (this->has_run_options()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->_internal_run_options(), deterministic, target);
  }

  // repeated .tensorflow.TensorConnection tensor_connection = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->tensor_connection_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        5, this->tensor_connection(static_cast<int>(i)), deterministic, target);
  }

  // map<string, string> feed_devices = 6;
  if (!this->feed_devices().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.CallableOptions.FeedDevicesEntry.key");
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.CallableOptions.FeedDevicesEntry.value");
      }
    };

    if (deterministic &&
        this->feed_devices().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->feed_devices().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->feed_devices().begin();
          it != this->feed_devices().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<CallableOptions_FeedDevicesEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(feed_devices_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       6, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<CallableOptions_FeedDevicesEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->feed_devices().begin();
          it != this->feed_devices().end(); ++it) {
        entry.reset(feed_devices_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       6, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // map<string, string> fetch_devices = 7;
  if (!this->fetch_devices().empty()) {
    typedef ::google::protobuf::Map< ::std::string, ::std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::google::protobuf::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.CallableOptions.FetchDevicesEntry.key");
        ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::google::protobuf::internal::WireFormatLite::SERIALIZE,
          "tensorflow.CallableOptions.FetchDevicesEntry.value");
      }
    };

    if (deterministic &&
        this->fetch_devices().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->fetch_devices().size()]);
      typedef ::google::protobuf::Map< ::std::string, ::std::string >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->fetch_devices().begin();
          it != this->fetch_devices().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      ::std::unique_ptr<CallableOptions_FetchDevicesEntry_DoNotUse> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(fetch_devices_.NewEntryWrapper(
            items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       7, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(items[static_cast<ptrdiff_t>(i)]);
      }
    } else {
      ::std::unique_ptr<CallableOptions_FetchDevicesEntry_DoNotUse> entry;
      for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
          it = this->fetch_devices().begin();
          it != this->fetch_devices().end(); ++it) {
        entry.reset(fetch_devices_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       7, *entry, deterministic, target);
;
        if (entry->GetArena() != NULL) {
          entry.release();
        }
        Utf8Check::Check(&*it);
      }
    }
  }

  // bool fetch_skip_sync = 8;
  if (this->fetch_skip_sync() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(8, this->fetch_skip_sync(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.CallableOptions)
  return target;
}

size_t CallableOptions::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.CallableOptions)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated string feed = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->feed_size());
  for (int i = 0, n = this->feed_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->feed(i));
  }

  // repeated string fetch = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->fetch_size());
  for (int i = 0, n = this->fetch_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->fetch(i));
  }

  // repeated string target = 3;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->target_size());
  for (int i = 0, n = this->target_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->target(i));
  }

  // repeated .tensorflow.TensorConnection tensor_connection = 5;
  {
    unsigned int count = static_cast<unsigned int>(this->tensor_connection_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->tensor_connection(static_cast<int>(i)));
    }
  }

  // map<string, string> feed_devices = 6;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->feed_devices_size());
  {
    ::std::unique_ptr<CallableOptions_FeedDevicesEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
        it = this->feed_devices().begin();
        it != this->feed_devices().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(feed_devices_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // map<string, string> fetch_devices = 7;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->fetch_devices_size());
  {
    ::std::unique_ptr<CallableOptions_FetchDevicesEntry_DoNotUse> entry;
    for (::google::protobuf::Map< ::std::string, ::std::string >::const_iterator
        it = this->fetch_devices().begin();
        it != this->fetch_devices().end(); ++it) {
      if (entry.get() != NULL && entry->GetArena() != NULL) {
        entry.release();
      }
      entry.reset(fetch_devices_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
    if (entry.get() != NULL && entry->GetArena() != NULL) {
      entry.release();
    }
  }

  // .tensorflow.RunOptions run_options = 4;
  if (this->has_run_options()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *run_options_);
  }

  // bool fetch_skip_sync = 8;
  if (this->fetch_skip_sync() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CallableOptions::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.CallableOptions)
  GOOGLE_DCHECK_NE(&from, this);
  const CallableOptions* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CallableOptions>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.CallableOptions)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.CallableOptions)
    MergeFrom(*source);
  }
}

void CallableOptions::MergeFrom(const CallableOptions& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.CallableOptions)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  feed_.MergeFrom(from.feed_);
  fetch_.MergeFrom(from.fetch_);
  target_.MergeFrom(from.target_);
  tensor_connection_.MergeFrom(from.tensor_connection_);
  feed_devices_.MergeFrom(from.feed_devices_);
  fetch_devices_.MergeFrom(from.fetch_devices_);
  if (from.has_run_options()) {
    mutable_run_options()->::tensorflow::RunOptions::MergeFrom(from.run_options());
  }
  if (from.fetch_skip_sync() != 0) {
    set_fetch_skip_sync(from.fetch_skip_sync());
  }
}

void CallableOptions::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.CallableOptions)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CallableOptions::CopyFrom(const CallableOptions& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.CallableOptions)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CallableOptions::IsInitialized() const {
  return true;
}

void CallableOptions::Swap(CallableOptions* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    CallableOptions* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void CallableOptions::UnsafeArenaSwap(CallableOptions* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void CallableOptions::InternalSwap(CallableOptions* other) {
  using std::swap;
  feed_.InternalSwap(CastToBase(&other->feed_));
  fetch_.InternalSwap(CastToBase(&other->fetch_));
  target_.InternalSwap(CastToBase(&other->target_));
  CastToBase(&tensor_connection_)->InternalSwap(CastToBase(&other->tensor_connection_));
  feed_devices_.Swap(&other->feed_devices_);
  fetch_devices_.Swap(&other->fetch_devices_);
  swap(run_options_, other->run_options_);
  swap(fetch_skip_sync_, other->fetch_skip_sync_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata CallableOptions::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::GPUOptions_Experimental_VirtualDevices* Arena::CreateMaybeMessage< ::tensorflow::GPUOptions_Experimental_VirtualDevices >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::GPUOptions_Experimental_VirtualDevices >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::GPUOptions_Experimental* Arena::CreateMaybeMessage< ::tensorflow::GPUOptions_Experimental >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::GPUOptions_Experimental >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::GPUOptions* Arena::CreateMaybeMessage< ::tensorflow::GPUOptions >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::GPUOptions >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::OptimizerOptions* Arena::CreateMaybeMessage< ::tensorflow::OptimizerOptions >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::OptimizerOptions >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::GraphOptions* Arena::CreateMaybeMessage< ::tensorflow::GraphOptions >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::GraphOptions >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::ThreadPoolOptionProto* Arena::CreateMaybeMessage< ::tensorflow::ThreadPoolOptionProto >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::ThreadPoolOptionProto >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::RPCOptions* Arena::CreateMaybeMessage< ::tensorflow::RPCOptions >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::RPCOptions >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::ConfigProto_DeviceCountEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::ConfigProto_DeviceCountEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::ConfigProto_DeviceCountEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::ConfigProto_Experimental* Arena::CreateMaybeMessage< ::tensorflow::ConfigProto_Experimental >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::ConfigProto_Experimental >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::ConfigProto* Arena::CreateMaybeMessage< ::tensorflow::ConfigProto >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::ConfigProto >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::RunOptions_Experimental* Arena::CreateMaybeMessage< ::tensorflow::RunOptions_Experimental >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::RunOptions_Experimental >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::RunOptions* Arena::CreateMaybeMessage< ::tensorflow::RunOptions >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::RunOptions >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::RunMetadata_FunctionGraphs* Arena::CreateMaybeMessage< ::tensorflow::RunMetadata_FunctionGraphs >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::RunMetadata_FunctionGraphs >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::RunMetadata* Arena::CreateMaybeMessage< ::tensorflow::RunMetadata >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::RunMetadata >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::TensorConnection* Arena::CreateMaybeMessage< ::tensorflow::TensorConnection >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::TensorConnection >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::CallableOptions_FeedDevicesEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::CallableOptions_FeedDevicesEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::CallableOptions_FeedDevicesEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::CallableOptions_FetchDevicesEntry_DoNotUse* Arena::CreateMaybeMessage< ::tensorflow::CallableOptions_FetchDevicesEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::CallableOptions_FetchDevicesEntry_DoNotUse >(arena);
}
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::CallableOptions* Arena::CreateMaybeMessage< ::tensorflow::CallableOptions >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tensorflow::CallableOptions >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

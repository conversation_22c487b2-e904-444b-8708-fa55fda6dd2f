// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/transport_options.proto

#include "tensorflow/core/protobuf/transport_options.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)

namespace tensorflow {
class RecvBufRespExtraDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<RecvBufRespExtra>
      _instance;
} _RecvBufRespExtra_default_instance_;
}  // namespace tensorflow
namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftransport_5foptions_2eproto {
static void InitDefaultsRecvBufRespExtra() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::tensorflow::_RecvBufRespExtra_default_instance_;
    new (ptr) ::tensorflow::RecvBufRespExtra();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::tensorflow::RecvBufRespExtra::InitAsDefaultInstance();
}

::google::protobuf::internal::SCCInfo<0> scc_info_RecvBufRespExtra =
    {{ATOMIC_VAR_INIT(::google::protobuf::internal::SCCInfoBase::kUninitialized), 0, InitDefaultsRecvBufRespExtra}, {}};

void InitDefaults() {
  ::google::protobuf::internal::InitSCC(&scc_info_RecvBufRespExtra.base);
}

::google::protobuf::Metadata file_level_metadata[1];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RecvBufRespExtra, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::tensorflow::RecvBufRespExtra, tensor_content_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::tensorflow::RecvBufRespExtra)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::tensorflow::_RecvBufRespExtra_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  AssignDescriptors(
      "tensorflow/core/protobuf/transport_options.proto", schemas, file_default_instances, TableStruct::offsets,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 1);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n0tensorflow/core/protobuf/transport_opt"
      "ions.proto\022\ntensorflow\"*\n\020RecvBufRespExt"
      "ra\022\026\n\016tensor_content\030\001 \003(\014b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 114);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "tensorflow/core/protobuf/transport_options.proto", &protobuf_RegisterTypes);
}

void AddDescriptors() {
  static ::google::protobuf::internal::once_flag once;
  ::google::protobuf::internal::call_once(once, AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_tensorflow_2fcore_2fprotobuf_2ftransport_5foptions_2eproto
namespace tensorflow {

// ===================================================================

void RecvBufRespExtra::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RecvBufRespExtra::kTensorContentFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RecvBufRespExtra::RecvBufRespExtra()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  ::google::protobuf::internal::InitSCC(
      &protobuf_tensorflow_2fcore_2fprotobuf_2ftransport_5foptions_2eproto::scc_info_RecvBufRespExtra.base);
  SharedCtor();
  // @@protoc_insertion_point(constructor:tensorflow.RecvBufRespExtra)
}
RecvBufRespExtra::RecvBufRespExtra(const RecvBufRespExtra& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      tensor_content_(from.tensor_content_) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tensorflow.RecvBufRespExtra)
}

void RecvBufRespExtra::SharedCtor() {
}

RecvBufRespExtra::~RecvBufRespExtra() {
  // @@protoc_insertion_point(destructor:tensorflow.RecvBufRespExtra)
  SharedDtor();
}

void RecvBufRespExtra::SharedDtor() {
}

void RecvBufRespExtra::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ::google::protobuf::Descriptor* RecvBufRespExtra::descriptor() {
  ::protobuf_tensorflow_2fcore_2fprotobuf_2ftransport_5foptions_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftransport_5foptions_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const RecvBufRespExtra& RecvBufRespExtra::default_instance() {
  ::google::protobuf::internal::InitSCC(&protobuf_tensorflow_2fcore_2fprotobuf_2ftransport_5foptions_2eproto::scc_info_RecvBufRespExtra.base);
  return *internal_default_instance();
}


void RecvBufRespExtra::Clear() {
// @@protoc_insertion_point(message_clear_start:tensorflow.RecvBufRespExtra)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  tensor_content_.Clear();
  _internal_metadata_.Clear();
}

bool RecvBufRespExtra::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:tensorflow.RecvBufRespExtra)
  for (;;) {
    ::std::pair<::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated bytes tensor_content = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->add_tensor_content()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:tensorflow.RecvBufRespExtra)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:tensorflow.RecvBufRespExtra)
  return false;
#undef DO_
}

void RecvBufRespExtra::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:tensorflow.RecvBufRespExtra)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated bytes tensor_content = 1;
  for (int i = 0, n = this->tensor_content_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      1, this->tensor_content(i), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:tensorflow.RecvBufRespExtra)
}

::google::protobuf::uint8* RecvBufRespExtra::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:tensorflow.RecvBufRespExtra)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated bytes tensor_content = 1;
  for (int i = 0, n = this->tensor_content_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteBytesToArray(1, this->tensor_content(i), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tensorflow.RecvBufRespExtra)
  return target;
}

size_t RecvBufRespExtra::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tensorflow.RecvBufRespExtra)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated bytes tensor_content = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->tensor_content_size());
  for (int i = 0, n = this->tensor_content_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::BytesSize(
      this->tensor_content(i));
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RecvBufRespExtra::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:tensorflow.RecvBufRespExtra)
  GOOGLE_DCHECK_NE(&from, this);
  const RecvBufRespExtra* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RecvBufRespExtra>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:tensorflow.RecvBufRespExtra)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:tensorflow.RecvBufRespExtra)
    MergeFrom(*source);
  }
}

void RecvBufRespExtra::MergeFrom(const RecvBufRespExtra& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tensorflow.RecvBufRespExtra)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  tensor_content_.MergeFrom(from.tensor_content_);
}

void RecvBufRespExtra::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:tensorflow.RecvBufRespExtra)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RecvBufRespExtra::CopyFrom(const RecvBufRespExtra& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tensorflow.RecvBufRespExtra)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RecvBufRespExtra::IsInitialized() const {
  return true;
}

void RecvBufRespExtra::Swap(RecvBufRespExtra* other) {
  if (other == this) return;
  InternalSwap(other);
}
void RecvBufRespExtra::InternalSwap(RecvBufRespExtra* other) {
  using std::swap;
  tensor_content_.InternalSwap(CastToBase(&other->tensor_content_));
  _internal_metadata_.Swap(&other->_internal_metadata_);
}

::google::protobuf::Metadata RecvBufRespExtra::GetMetadata() const {
  protobuf_tensorflow_2fcore_2fprotobuf_2ftransport_5foptions_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_tensorflow_2fcore_2fprotobuf_2ftransport_5foptions_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace tensorflow
namespace google {
namespace protobuf {
template<> GOOGLE_PROTOBUF_ATTRIBUTE_NOINLINE ::tensorflow::RecvBufRespExtra* Arena::CreateMaybeMessage< ::tensorflow::RecvBufRespExtra >(Arena* arena) {
  return Arena::CreateInternal< ::tensorflow::RecvBufRespExtra >(arena);
}
}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)
